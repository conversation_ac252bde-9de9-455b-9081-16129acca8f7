spring:
  datasource:
    dynamic:
      # 设置默认的数据源或者数据源组,默认值即为 master
      primary: master
      datasource:
        # 主库数据源
        master:
          type: ${spring.datasource.type}
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: ${datasource.system-master.url}
          username: ${datasource.system-master.username}
          password: ${datasource.system-master.password}
  servlet:
    multipart:
      max-file-size: 200MB
      max-request-size: 200MB

  rabbitmq:
    host: **************
    port: 25672
    username: hzw
    password: TR0Y9s27Oda6oqKs
  cloud:
    stream:
      rabbit:
        bindings:
          sms-in-0:
            consumer:
              delayedExchange: false
          sms-out-0:
            producer:
              delayedExchange: false
      bindings:
        sms-in-0:
          destination: sms.exchange.cloud
          content-type: application/json
          group: sms-group
          binder: rabbit
        sms-out-0:
          destination: sms.exchange.cloud
          content-type: application/json
          group: sms-group
          binder: rabbit
sms:
  request-timeout: 5000
  response-timeout: 60000
  host: ************
  port: 8023
  userid: J53199
  pwd: 200215
  token: 3f89deea35258fa9ab3c629751e963f9b5a9113f926f576d07c44b283729275d
  base-path: /sms/v2/std/
  switch: false

balance:
  list-name: JJ3183,J53199,JI2356
  list-pwd: '083320,200215,260015'
  list-source: jitc正式,jitc测试,CWS


#模板文件配置
files:
  template:
    path: /data/template
  temporary:
    path: /data/temporary
