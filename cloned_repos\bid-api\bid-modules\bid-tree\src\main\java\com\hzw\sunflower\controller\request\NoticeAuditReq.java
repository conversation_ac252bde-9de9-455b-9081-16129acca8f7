package com.hzw.sunflower.controller.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date ：Created in 2021/9/7 10:29
 * @description：审核公告撤回
 * @version: 1.0
 */
@Data
public class NoticeAuditReq {

    @ApiModelProperty(value = "招标公告ID", position = 1)
    private Long noticeId;

    @ApiModelProperty(value = "项目ID", position = 2)
    private Long projectId;

    @ApiModelProperty(value = "标段ID", position = 3)
    private Long sectionId;

    @ApiModelProperty(value = "1:同意退回 2:驳回退回", position = 4)
    private Integer auditType;

    @ApiModelProperty(value = "备注建议", position = 5)
    private String approvalOpinion;

    @ApiModelProperty(value = "任务ID", position = 4)
    private String taskId;

    @ApiModelProperty(value = "审核实例ID", position = 6)
    private String processInstanceId;
}
