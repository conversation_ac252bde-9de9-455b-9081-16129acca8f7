package com.hzw.sunflower.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hzw.sunflower.dto.ApprovalOpinionDTO;
import com.hzw.sunflower.entity.ApprovalOpinion;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * TApprovalOpinionMapper接口
 */
@Repository
public interface ApprovalOpinionMapper extends BaseMapper<ApprovalOpinion> {
    /**
     * 查询招标文件处理进度
     *
     * @param docId
     * @return
     */
    List<ApprovalOpinionDTO> getInfoByDocId(@Param("docId") Long docId);

    /**
     * 查询招标公告审批记录
     *
     * @param noticeId
     * @return
     */
    List<ApprovalOpinionDTO> getInfoByNoticeId(@Param("noticeId") Long noticeId);
}
