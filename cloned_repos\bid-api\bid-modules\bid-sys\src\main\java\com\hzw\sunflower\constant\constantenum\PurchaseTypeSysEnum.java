package com.hzw.sunflower.constant.constantenum;

/**
 * 一级采购方式
 */
public enum PurchaseTypeSysEnum {
    ACCORD_LAW_RECRUITED(1,"依法必招"),
    NO_ACCORD_LAW_RECRUITED(2,"非依法必招"),
    GOVERNMENT_PROCUREMENT(3,"政府采购"),
    INTERNATIONAL_TENDERS(4,"国际招标");

    private Integer type;
    private String desc;

    PurchaseTypeSysEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }


    public static PurchaseTypeSysEnum matchType(Integer type) {
        PurchaseTypeSysEnum result = null;
        for (PurchaseTypeSysEnum s : values()) {
            if (s.getType().equals(type)) {
                result = s;
                break;
            }
        }
        return result;
    }

    public static PurchaseTypeSysEnum catchDesc(String desc) {
        PurchaseTypeSysEnum result = null;
        for (PurchaseTypeSysEnum s : values()) {
            if (s.getDesc().equals(desc)) {
                result = s;
                break;
            }
        }
        return result;
    }

}
