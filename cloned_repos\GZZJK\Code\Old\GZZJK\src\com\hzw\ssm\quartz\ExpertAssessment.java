package com.hzw.ssm.quartz;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;

import com.hzw.ssm.expert.entity.ExpertInfoEntity;
import com.hzw.ssm.expert.entity.ExpertScoreEntity;
import com.hzw.ssm.expert.service.ExpertInfoService;
import com.hzw.ssm.expert.service.ExpertScoreService;
import com.hzw.ssm.fw.util.SysConstants;
import com.hzw.ssm.util.string.StringUtils;

/**
 * 专家考核考核操作
 */
public class ExpertAssessment {

	@Autowired
	private ExpertInfoService expertInfoService;

	@Autowired
	private ExpertScoreService expertScoreService;

	/**
	 * 专家状态 0:注册 1:待审核 2:二次待审核 3:审核通过 4:禁用(不可评标) 5：修改待审核 6：修改待审核二次审核
	 * 评标专家在一个计分周期内累计扣满15分的，暂停3个月评标资格； 累计扣满20分的，暂停6个月评标资格；
	 * 累计暂停9个月及以上的，自暂停之日起三年内不得续聘评标专家资格； 一次被扣20分的，暂停12个月评标资格或者视情节依法取消其评标专家资格
	 */
	public void work() {
		try {
			// 获取服务器当前时间
			Calendar cd = Calendar.getInstance();
			
			// 查询专家考核分数
			List<ExpertInfoEntity> expertList = expertInfoService.queryExpertInfoList(SysConstants.EXPERT_STATUS.NORMAL);// 所有正常的专家
			List<ExpertInfoEntity> expertAssessmentList = expertInfoService.queryExpertInfoList(SysConstants.EXPERT_STATUS.PAUSE);// 所有异常的专家

			List<ExpertInfoEntity> recoveryList = new ArrayList<ExpertInfoEntity>();// 需要恢复的专家List
			List<ExpertInfoEntity> assessmentList = new ArrayList<ExpertInfoEntity>();// 需要暂停的专家List

			// 循环(所有异常的专家)满足条件的专家 根据分数进行恢复操作
			for (ExpertInfoEntity entity : expertAssessmentList) {
				// 兼容异常数据:如果数据异常没有禁用结束时间,则跳过
				if (entity.getPause_endTime() == null || cd.getTime() == null) {
					continue;
				}
				// 如果Pause_endTime的时间在当前时间之前（不包括等于）返回true
				if (entity.getPause_endTime().before(cd.getTime())) {
					entity.setPause_startTime(null);// 暂停时间设置为空
					entity.setPause_endTime(null);// 暂停时间设置为空
					entity.setStatus(SysConstants.EXPERT_STATUS.NORMAL_L);// 专家状态设置为正常
					recoveryList.add(entity);
				}
			}
			
			// 循环(所有正常的专家)满足条件的专家 根据分数进行暂停操作
			for (ExpertInfoEntity entity : expertList) {
				//判断专家是否被暂停，若被暂停将数据备份一份到备份表中
				boolean flag = false;
				// 判断当前专家是否有停用情况，如果没有设置为0
				if (entity.getPause_number() == null) {
					entity.setPause_number(0);
				}
				// 评标专家在一个计分周期内累计扣满15分的，暂停3个月评标资格；
				cd.setTime(new Date());
				if (entity.getEval_score() > 80 && entity.getEval_score() <= 85) {
					entity.setPause_startTime(new Date());// 暂停开始时间
					// 累计暂停9个月及以上的，自暂停之日起三年内不得续聘评标专家资格
					if ((entity.getPause_number() + 3) >= 9) {
						cd.add(Calendar.MONTH, 36);// 增加36个月
						entity.setPause_number(36);// 暂停时间累加
					} else {
						cd.add(Calendar.MONTH, 3);// 增加3个月
						entity.setPause_number(entity.getPause_number() + 3);// 暂停时间累加
					}
					flag = true;
					entity.setPause_endTime(cd.getTime());// 暂停结束时间
					entity.setStatus(4L);// 暂停标识
					assessmentList.add(entity);
				}

				// TODO 判断是否为一次性扣20分，还是累计扣20分(在AppraiseAction中已做控制)
				// 累计扣满20分的，暂停6个月评标资格；
				if (entity.getEval_score() <= 80) {
					entity.setPause_startTime(new Date());// 暂停开始时间

					// 累计暂停9个月及以上的，自暂停之日起三年内不得续聘评标专家资格
					if ((entity.getPause_number() + 6) >= 9) {
						cd.add(Calendar.MONTH, 36);// 增加36个月
						entity.setPause_number(36);// 暂停时间累加
					} else {
						cd.add(Calendar.MONTH, 6);// 增加6个月
						entity.setPause_number(entity.getPause_number() + 6);// 暂停时间累加
					}
					flag = true;
					entity.setPause_endTime(cd.getTime());// 暂停结束时间
					entity.setStatus(SysConstants.EXPERT_STATUS.PAUSE_L);// 暂停标识
					assessmentList.add(entity);
				}
				if (flag) {
					// 新增最近的一次扣分详情
					ExpertScoreEntity expertScore = new ExpertScoreEntity();
					expertScore.setId(StringUtils.getUUID());
					expertScore.setUser_id(entity.getUser_id());
					expertScore.setUser_name(entity.getUser_name());
					expertScore.setPause_startTime(entity.getPause_startTime());
					expertScore.setPause_endTime(entity.getPause_endTime());
					expertScore.setPause_number(entity.getPause_number());
					expertScore.setBuckle_score(entity.getEval_score());
					expertScore.setModify_time(new Date());
					expertScoreService.addExpertScoreRecords(expertScore);
				}
			}
			// 专家暂停操作
			if (assessmentList.size() > 0) {
				for (ExpertInfoEntity entity : assessmentList) {
					expertInfoService.modifyExpertAssessment(entity);
				}
			}

			// 专家恢复操作
			if (recoveryList.size() > 0) {
				for (ExpertInfoEntity entity : recoveryList) {
					expertInfoService.modifyExpertAssessment(entity);
				}
			}
			
			resetScore(cd);
			
		} catch (Exception e) {
			e.printStackTrace();
		}

	}
	/**
	 * 专家满一个周期将专家评分重置
	 * @param cd
	 * @param expertAssessmentList
	 * @param recoveryList
	 */
	private void resetScore(Calendar cd) {
		// 跨年的分值恢复(每天5点定时任务启，判断日期为一年的最后一天即可)
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		Calendar calendar = Calendar.getInstance();
		calendar.clear();
		int currentYear = cd.get(Calendar.YEAR);
		calendar.set(Calendar.YEAR, currentYear);
		calendar.roll(Calendar.DAY_OF_YEAR, -1);
		// 获取最后一天
		Date currYearLast = calendar.getTime();
		// 一年的最后一天时间
		String yearLast = sdf.format(currYearLast);
		// 现在的系统时间
		String nowDate = sdf.format(cd.getTime());
		// 如果当前时间等于一年的最后一天时间将分值重置
		System.out.println(yearLast);
		if (nowDate.equals(yearLast)) {
			// 查询专家考核分数
			List<ExpertInfoEntity> expertList = expertInfoService.queryExpertInfoList(SysConstants.EXPERT_STATUS.NORMAL);//所有正常的专家
			//将所有正常的专家在年底分值恢复之前，备份到备份表中
			for(ExpertInfoEntity info :expertList) {
				// 新增最近的一次扣分详情
				ExpertScoreEntity expertScore = new ExpertScoreEntity();
				expertScore.setId(StringUtils.getUUID());
				expertScore.setUser_id(info.getUser_id());
				expertScore.setUser_name(info.getUser_name());
				expertScore.setPause_startTime(info.getPause_startTime());
				expertScore.setPause_endTime(info.getPause_endTime());
				expertScore.setPause_number(info.getPause_number());
				expertScore.setBuckle_score(info.getEval_score());
				expertScore.setModify_time(new Date());
				expertScoreService.addExpertScoreRecords(expertScore);
			}
			//将所有的分值恢复
			ExpertInfoEntity entity = new ExpertInfoEntity();
			entity.setModify_time(cd.getTime());
			entity.setEval_score(100);//跨年分数重置
			entity.setPause_number(0);//跨年暂停月份重置
			// 跨年更新所有的专家的分值(设为100)
			expertInfoService.updateAllExpertScore(entity);
			
		
		}
	}
	
}
