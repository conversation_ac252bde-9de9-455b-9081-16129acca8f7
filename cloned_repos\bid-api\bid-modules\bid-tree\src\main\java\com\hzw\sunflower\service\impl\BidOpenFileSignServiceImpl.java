package com.hzw.sunflower.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzw.sunflower.constant.CaConstants;
import com.hzw.sunflower.controller.request.CompleteSignReq;
import com.hzw.sunflower.dao.BidOpenFileSignMapper;
import com.hzw.sunflower.entity.BidOpenFileSign;
import com.hzw.sunflower.entity.JwtUser;
import com.hzw.sunflower.entity.OssFile;
import com.hzw.sunflower.service.BidOpenFileService;
import com.hzw.sunflower.service.BidOpenFileSignService;
import com.hzw.sunflower.service.OssFileService;
import com.hzw.sunflower.util.OssUtil;
import com.hzw.sunflower.util.oss.OssFileStorage;
import com.hzw.sunflower.utils.FileUtil;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;


/**
 * <AUTHOR>
 * @date ：Created in 2021/8/27 13:51
 * @description：
 * @version: 1.0
 */
@Service
public class BidOpenFileSignServiceImpl extends ServiceImpl<BidOpenFileSignMapper, BidOpenFileSign> implements BidOpenFileSignService {

    @Autowired
    private OssFileService ossFileService;

    @Value("${oss.active}")
    private String ossType;

    @Value("${files.sign.path}")
    private String signPath;

    /**
     * 返回oss文件临时存储路径
     * @param fileOssId
     * @return
     */
    @Override
    public String browseFile(Long fileOssId) {
        // 创建签章临时文件
        String fileName = fileOssId + ".pdf";
        String filePath = signPath + fileName;
        File file = new File(filePath);
        if (!file.exists()) {
            OssFile ossFile = ossFileService.getOssFileById(fileOssId);
            if(BeanUtil.isEmpty(ossFile)){
                return null;
            }
            OssFileStorage storage = OssUtil.getOssFileStorage(ossType);
            byte[] bytes = storage.downloadFile(ossFile.getOssFileKey());
            if (null != bytes && bytes.length > 0) {
                FileUtil.byteArrayToFile(bytes, filePath);
            }
        }
        return filePath;
    }

    /**
     * 打开oss文件
     * @param fileOssId
     */
    @Override
    public void openFile(Long fileOssId, HttpServletResponse response) {
        String filePath = this.browseFile(fileOssId);
        File file = new File(filePath);
        FileInputStream in = null;
        try {
            in = new FileInputStream(file);
            response.setContentType("application/octet-stream");
            response.addIntHeader("Content-Length", (int)file.length());
            int bytesRead = 0;
            byte[] buffer = new byte[8192];
            while ((bytesRead = in.read(buffer, 0, 8192)) != -1) {
                response.getOutputStream().write(buffer, 0, bytesRead);
            }
            in.close();
            //不掉这个方法文件无法操作了一定要掉这个方法
            response.getOutputStream().flush();
            response.getOutputStream().close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 完成签章
     * @param req
     * @param user
     * @return
     */
    @Override
    public Boolean completeSign(CompleteSignReq req, JwtUser user) {
        String fileName = req.getFileId() + ".pdf";
        String filePath = signPath + fileName;
        File file = new File(filePath);
        if (!file.exists()) {
            return false;
        }
        // 更新oss文件
        OssFileStorage storage = OssUtil.getOssFileStorage(ossType);
        String key = storage.uploadFileByPath(filePath);
        if(StringUtils.isNotBlank(key)) {
            OssFile ossFile = ossFileService.getOssFileById(req.getFileId());
            ossFile.setOssFileKey(key);
            ossFile.setOssFileName(fileName);
            ossFileService.updateById(ossFile);
        }
        // 新增签章记录
        BidOpenFileSign bidOpenFileSign = new BidOpenFileSign();
        bidOpenFileSign.setFileOssId(req.getFileId());
        bidOpenFileSign.setCompanyId(req.getCompanyId());
        bidOpenFileSign.setOriginFileOssId(req.getFileId());
        bidOpenFileSign.setSectionId(req.getSectionId());
        bidOpenFileSign.setUserId(user.getUserId());
        return this.save(bidOpenFileSign);
    }

}
