///***
/// * 江苏省及市级地区 代码 - 名称 对应表
/// */

import 'package:flutter_main_app/src/models/common/RegionModel.dart';

const List<RegionModel> jiangSuRegionList = [
  RegionModel(
    name: '江苏省',
    code: '320000',
  ),
  RegionModel(
    name: '南京市',
    code: '320100',
  ),
  RegionModel(
    name: '无锡市',
    code: '320200',
  ),
  RegionModel(
    name: '徐州市',
    code: '320300',
  ),
  RegionModel(
    name: '常州市',
    code: '320400',
  ),
  RegionModel(
    name: '苏州市',
    code: '320500',
  ),
  RegionModel(
    name: '南通市',
    code: '320600',
  ),
  RegionModel(
    name: '连云港市',
    code: '320700',
  ),
  RegionModel(
    name: '淮安市',
    code: '320800',
  ),
  RegionModel(
    name: '盐城市',
    code: '320900',
  ),
  RegionModel(
    name: '扬州市',
    code: '321000',
  ),
  RegionModel(
    name: '镇江市',
    code: '321100',
  ),
  RegionModel(
    name: '泰州市',
    code: '321200',
  ),
  RegionModel(
    name: '宿迁市',
    code: '321300',
  ),
];
