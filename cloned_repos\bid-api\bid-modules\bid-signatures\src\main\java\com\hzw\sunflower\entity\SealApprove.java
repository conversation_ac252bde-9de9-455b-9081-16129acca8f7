package com.hzw.sunflower.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.hzw.sunflower.common.BaseBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
* Created by pj on 2023/11/03
*/
@ApiModel("t_seal_approve表")
@TableName("t_seal_approve")
@Data
public class SealApprove  extends BaseBean implements Serializable {
    private Long id;

    /**
     * 申请id
     *
     * @mbg.generated
     */
    @ApiModelProperty("申请id")
    private Long applyId;

    /**
     * 选择审批类型（1.归口审批部门 2.领导审批）
     *
     * @mbg.generated
     */
    @ApiModelProperty("选择审批类型（1.归口审批部门 2.领导审批）")
    private Integer approveType;

    /**
     * 审批部门
     *
     * @mbg.generated
     */
    @ApiModelProperty("审批部门 1.办公室 2.运管处 3.财审处")
    private Integer approveDept;

    /**
     * 标识符
     *
     * @mbg.generated
     */
    @ApiModelProperty("标识符")
    private String formkey;

    /**
     * 是否选中（1.是 2.否）
     *
     * @mbg.generated
     */
    @ApiModelProperty("是否选中（1.是 2.否）")
    private Integer isChoose;

    /**
     * 是否审批（1.是 2.否）
     *
     * @mbg.generated
     */
    @ApiModelProperty("是否审批（1.是 2.否）")
    private Integer isApprove;


    /**
     * 流程实例id
     *
     * @mbg.generated
     */
    @ApiModelProperty("流程实例id")
    private String processinstanceId;

    private static final long serialVersionUID = 1L;
}