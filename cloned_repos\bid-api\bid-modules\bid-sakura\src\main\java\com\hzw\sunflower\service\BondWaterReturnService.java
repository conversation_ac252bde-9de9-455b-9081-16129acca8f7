package com.hzw.sunflower.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.entity.BondWaterReturn;
import com.hzw.sunflower.entity.BondWaterlabel;

/**
 * @ClassName:BondWaterReturnService
 * @Auther: lijinxin
 * @Description: 保证金流水退还记录表
 * @Date: 2023/5/11 17:23
 * @Version: v1.0
 */
public interface BondWaterReturnService extends IService<BondWaterReturn> {

    public Result<Object> add(BondWaterReturn bondWaterReturn);

    public Boolean checkReturn(Long waterId);
}
