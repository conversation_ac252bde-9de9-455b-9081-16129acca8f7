///***
/// * 自定义路由间跳转动画
/// */

import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';

///***
/// * 根据 theme 决定如何定义 transitions
/// *
/// * size: SizeTransition
/// * fade: FadeTransition
/// * rotate: RotationTransition
/// * scale: ScaleTransition
/// * slideUpDown: SlideTransition
/// * slide: SlideTransition
/// */
Widget _buidTransition({
  BuildContext context,
  Animation<double> animation,
  Animation<double> secondaryAnimation,
  Widget child,
  String theme,
}) {
  Widget transition;

  switch (theme) {
    case 'size':
      transition = SizeTransition(
        sizeFactor: Tween<double>(
          begin: 0.0,
          end: 1.0,
        ).animate(
          CurvedAnimation(
            parent: animation,
            curve: Curves.easeIn,
          ),
        ),
        child: child,
      );
      break;
    case 'fade':
      transition = FadeTransition(
        opacity: Tween<double>(
          begin: 0.0,
          end: 1.0,
        ).animate(
          CurvedAnimation(
            parent: animation,
            curve: Curves.decelerate,
          ),
        ),
        child: child,
      );
      break;
    case 'rotate':
      transition = ScaleTransition(
        scale: Tween<double>(
          begin: 0.0,
          end: 1.0,
        ).animate(
          CurvedAnimation(
            parent: animation,
            curve: Curves.fastOutSlowIn,
          ),
        ),
        child: RotationTransition(
          turns: Tween<double>(
            begin: 0.0,
            end: 1.0,
          ).animate(
            CurvedAnimation(
              parent: animation,
              curve: Curves.linear,
            ),
          ),
          child: child,
        ),
      );
      break;
    case 'scale':
      transition = ScaleTransition(
        scale: Tween<double>(
          begin: 0.0,
          end: 1.0,
        ).animate(
          CurvedAnimation(
            parent: animation,
            curve: Curves.easeInOutBack,
          ),
        ),
        child: FadeTransition(
          opacity: Tween<double>(
            begin: 0.0,
            end: 1.0,
          ).animate(
            CurvedAnimation(
              parent: animation,
              curve: Curves.easeInOutBack,
            ),
          ),
          child: child,
        ),
      );
      break;
    case 'slideUpDown':
      transition = SlideTransition(
        position: Tween<Offset>(
          begin: const Offset(0.0, 1.0),
          end: Offset.zero,
        ).animate(animation),
        child: SlideTransition(
          position: Tween<Offset>(
            begin: Offset.zero,
            end: const Offset(0.0, -0.7),
          ).animate(secondaryAnimation),
          child: child,
        ),
      );
      break;
    case 'slide':
    default:
      transition = SlideTransition(
        position: Tween<Offset>(
          begin: const Offset(1.0, 0.0),
          end: Offset.zero,
        ).animate(animation),
        child: SlideTransition(
          position: Tween<Offset>(
            begin: Offset.zero,
            end: const Offset(-0.7, 0.0),
          ).animate(secondaryAnimation),
          child: child,
        ),
      );
      break;
  }

  return transition;
}

///***
/// * 摒弃 Material 或 Cupertino 的风格
/// * 完全自定义路由动画
/// */
class CustomPageRouteBuilder<T> extends PageRouteBuilder<T> {
  CustomPageRouteBuilder({
    this.widget,
    this.theme = 'slide',
  }) : assert(widget != null),
    super(
      pageBuilder: (BuildContext context, Animation<double> animation, Animation<double> secondaryAnimation) {
        return widget;
      },
      transitionsBuilder: (BuildContext context, Animation<double> animation, Animation<double> secondaryAnimation, Widget child) {
        Widget transition = _buidTransition(
          context: context,
          animation: animation,
          secondaryAnimation: secondaryAnimation,
          child: child,
          theme: theme,
        );

        if (theme == 'size') {
          transition = Align(
            child: transition,
          );
        }

        return transition;
      },
    );

  ///***
  /// * 要添加动画的路由
  /// */
  final Widget widget;

  ///***
  /// * 动画主题
  /// *
  /// * slide - 默认，IOS 默认的页面切换动画
  /// * fade - 隐现
  /// * slideUpDown - 上下滑动隐现
  /// * rotate - 旋转
  /// * scale - 缩放
  /// */
  final String theme;

}

///***
/// * 在 Material 风格的基础上定义动画
/// *
/// * 初始页面无动画
/// *
/// * 新 push 进来的页面执行以下定义的动画
/// * 当有更新的页面被 push 进来时，当前页面执行 secondaryAnimation 动画（如果有的话）
/// */
class CustomMaterialPageRoute<T> extends MaterialPageRoute<T> {
  CustomMaterialPageRoute({
    @required WidgetBuilder builder,
    RouteSettings settings,
    this.theme = 'slide',
  }) : assert(builder != null),
    super(builder: builder, settings: settings);

  ///***
  /// * 动画主题
  /// *
  /// * slide - 默认，IOS 默认的页面切换动画
  /// * fade - 隐现
  /// * slideUpDown - 上下滑动隐现
  /// * rotate - 旋转
  /// * scale - 缩放
  /// */
  final String theme;

  @override
  Widget buildTransitions(
    BuildContext context,
    Animation<double> animation,
    Animation<double> secondaryAnimation,
    Widget child,
  ) {
    Widget transition = _buidTransition(
      context: context,
      animation: animation,
      secondaryAnimation: secondaryAnimation,
      child: child,
      theme: theme,
    );

    if (this.theme == 'size') {
      transition = Align(
        child: transition,
      );
    }

    return transition;
  }
}

///***
/// * 在 Cupertino 风格的基础上定义动画
/// *
/// * 初始页面被盖住时，默认向左做一个带视差的动画
/// *
/// * 新 push 进来的页面执行以下定义的动画
/// * 当有更新的页面被 push 进来时，当前页面执行 secondaryAnimation 动画（如果有的话）
/// */
class CustomCupertinoPageRoute<T> extends CupertinoPageRoute<T> {
  CustomCupertinoPageRoute({
    @required this.builder,
    this.theme = 'slide',
  }) : super(builder: builder);

  final WidgetBuilder builder;

  final String theme;

  @override
  Widget buildPage(BuildContext context, Animation<double> animation, Animation<double> secondaryAnimation) {
    final Widget child = builder(context);

    Widget transition = _buidTransition(
      context: context,
      animation: animation,
      secondaryAnimation: secondaryAnimation,
      child: child,
      theme: theme,
    );

    if (this.theme == 'size') {
      transition = Align(
        child: transition,
      );
    }

    return transition;
  }
}

///***
/// * 为进页面和出页面同时添加动画
/// *
/// * 使用 Stack 来容纳不同的动画
/// *
/// * @animation SlideAnimation
/// */
class CustomEnterExitPageRoute<T> extends PageRouteBuilder<T> {
  CustomEnterExitPageRoute({
    this.enterPage,
    this.exitPage,
  })
    : super(
        pageBuilder: (
          BuildContext context,
          Animation<double> animation,
          Animation<double> secondaryAnimation,
        ) => enterPage,
        transitionsBuilder: (
          BuildContext context,
          Animation<double> animation,
          Animation<double> secondaryAnimation,
          Widget child,
        ) => Stack(
          children: <Widget>[
            SlideTransition(
              position: Tween<Offset>(
                begin: Offset.zero,
                end: Offset(-1.0, 0.0),
              ).animate(animation),
              child: exitPage,
            ),
            SlideTransition(
              position: Tween<Offset>(
                begin: Offset(1.0, 0.0),
                end: Offset.zero,
              ).animate(animation),
              child: enterPage,
            ),
          ],
        ),
      );

  final Widget enterPage;

  final Widget exitPage;
}
