package com.hzw.sunflower.entity.condition;

import com.hzw.sunflower.common.BaseCondition;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(description = "查询条件")
public class TendererReportCondition extends BaseCondition {

    @ApiModelProperty("选择日期")
    private String chooseDate;

    @ApiModelProperty("关键字")
    private String keyWords;

    @ApiModelProperty("开始时间")
    private String startDate;

    @ApiModelProperty("结束时间")
    private String endDate;

    @ApiModelProperty("代理机构")
    private Long companyId;

    @ApiModelProperty("名称")
    private String name;

    private String dataScope;


}
