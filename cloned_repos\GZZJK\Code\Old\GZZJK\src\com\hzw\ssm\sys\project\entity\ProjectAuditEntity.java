package com.hzw.ssm.sys.project.entity;

import java.util.Date;

import com.hzw.ssm.fw.base.BaseEntity;

/**
 * 项目审核记录表
 * <AUTHOR>
 *
 */
public class ProjectAuditEntity extends BaseEntity {
	private static final long serialVersionUID = -8151736570527763583L;
	private String id;// 主键
	private String project_no;//项目编号
	private String project_id;// 项目id
	private String reason;// 理由
	private Long status;// 状态
	private String auditType;
	private Date audit_time;// 审核时间
	private String audit_user;// 审核人
	/**
	 * 抽取批次
	 */
	private String decimationBatch;
	
	
	
	public String getProject_no() {
		return project_no;
	}
	public void setProject_no(String project_no) {
		this.project_no = project_no;
	}
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public String getProject_id() {
		return project_id;
	}
	public void setProject_id(String projectId) {
		project_id = projectId;
	}
	public String getReason() {
		return reason;
	}
	public void setReason(String reason) {
		this.reason = reason;
	}
	public Long getStatus() {
		return status;
	}
	public void setStatus(Long status) {
		this.status = status;
	}
	public Date getAudit_time() {
		return audit_time;
	}
	public void setAudit_time(Date auditTime) {
		audit_time = auditTime;
	}
	public String getAudit_user() {
		return audit_user;
	}
	public void setAudit_user(String auditUser) {
		audit_user = auditUser;
	}
	public String getDecimationBatch() {
		return decimationBatch;
	}
	public void setDecimationBatch(String decimationBatch) {
		this.decimationBatch = decimationBatch;
	}
	public String getAuditType() {
		return auditType;
	}
	public void setAuditType(String auditType) {
		this.auditType = auditType;
	}
	
}
