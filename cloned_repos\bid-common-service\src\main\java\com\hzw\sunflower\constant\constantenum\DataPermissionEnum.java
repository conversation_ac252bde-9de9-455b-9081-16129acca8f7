package com.hzw.sunflower.constant.constantenum;

/**
 * 招标阶段
 *
 * <AUTHOR>
 */
public enum DataPermissionEnum {
    PERSON(0, "个人"),
    DEPARTMENT(1, "本级"),
    CUSTOM(2, "自定义"),
    ALL(4, "全部");
    private Integer type;
    private String desc;

    DataPermissionEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
