package com.hzw.sunflower.constant.constantenum;

/**
 * 保证金申请退还状态
 */
public enum BondApplyRefundStatus {

    WAIT_HANDLE(1, "待处理"),
    HANDLE_WAIT_CONFIRM(2, "已处理待确认"),
    NO_DIVISION_CONFIRM(3,"无处室认领"),
    DIVISION_NO_CONFIRM(4, "处室不同意"),
    DIVISION_CONFIRM(5,"处室同意"),
    RETURNED(6,"已退回"),

    //财务处理状态
    FINANCE_WAIT(11, "待处理"),
    FINANCE_RETURNED(12, "已退回"),
    FINANCE_SUCCESS(13, "退还成功"),
    FINANCE_FAIL(14, "退还失败"),
    FINANCE_REFUNDING(15, "退还中");

    private Integer type;
    private String desc;

    BondApplyRefundStatus(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

}
