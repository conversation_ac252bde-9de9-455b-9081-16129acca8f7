package com.hzw.sunflower.controller.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(description = "编号示例生成参数")
@Data
public class NumberExampleReq {

    @ApiModelProperty("生成规则多选以,隔开 1.系统简称 2.年份 3.部门编码 4.省外（若数据字典勾选该字段可显示）5.自增数字")
    private String genRules;

    @ApiModelProperty("年份位数 值固定为2或4")
    private Integer yearLen;

    @ApiModelProperty("自增数字位数")
    private Integer increNumberLen;

}
