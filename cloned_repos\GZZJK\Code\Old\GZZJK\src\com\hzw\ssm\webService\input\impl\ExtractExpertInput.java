package com.hzw.ssm.webService.input.impl;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;

import com.hzw.ssm.webService.input.BodyInput;

/**
 * 专家抽取申请 接口 输入参数
 * 
 * <AUTHOR>
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "ExtractExpert")
@XmlType(name = "ExtractExpert", propOrder = { "projectName", "projectNum", "entrustingUnit", "chargePerson",
		"contactWay", "department", "handlder", "agency", "openBidTime", "openBidPlace", "remarks",
		"total", "zone","zoneName", "seniorNum", "localNum","expertType","expertTypeName","method" })
public class ExtractExpertInput extends BodyInput{
	// 项目名称 S
	@XmlElement
	private String projectName;
	// 项目编号 S
	@XmlElement
	private String projectNum;
	// 委托单位 S
	@XmlElement
	private String entrustingUnit;
	// 项目负责人 S
	@XmlElement
	private String chargePerson;
	// 联系方式 S
	@XmlElement
	private String contactWay;
	// 部门 S
	@XmlElement
	private String department;
	// 经办人 S
	@XmlElement
	private String handlder;
	// 代理机构 S
	@XmlElement
	private String agency;
	// 开标时间 D
	@XmlElement
	private String openBidTime;
	// 开标地点 S
	@XmlElement
	private String openBidPlace;
	// 备注 S 文本（不需要操作） //抽取条件
	@XmlElement
	private String remarks;
	// 专家人数 I
	@XmlElement
	private Integer total;
	// 区域编码 S //抽取条件
	@XmlElement
	private String zone;
	// 区域名称 S
	@XmlElement
	private String zoneName;
	// 国家级专家 //抽取条件
	@XmlElement
	private Integer seniorNum;
	// 地方级专家 //抽取条件
	@XmlElement
	private Integer localNum;
	// 专业类别 S //抽取条件
	@XmlElement
	private String expertType;
	// 专家类别名称
	@XmlElement
	private String expertTypeName;
	// 抽取方式 I
	@XmlElement
	private Integer method;

	public String getProjectName() {
		return projectName;
	}

	public void setProjectName(String projectName) {
		this.projectName = projectName;
	}

	public String getProjectNum() {
		return projectNum;
	}

	public void setProjectNum(String projectNum) {
		this.projectNum = projectNum;
	}

	public String getEntrustingUnit() {
		return entrustingUnit;
	}

	public void setEntrustingUnit(String entrustingUnit) {
		this.entrustingUnit = entrustingUnit;
	}

	public String getChargePerson() {
		return chargePerson;
	}

	public void setChargePerson(String chargePerson) {
		this.chargePerson = chargePerson;
	}

	public String getContactWay() {
		return contactWay;
	}

	public void setContactWay(String contactWay) {
		this.contactWay = contactWay;
	}

	public String getDepartment() {
		return department;
	}

	public void setDepartment(String department) {
		this.department = department;
	}

	public String getHandlder() {
		return handlder;
	}

	public void setHandlder(String handlder) {
		this.handlder = handlder;
	}

	public String getAgency() {
		return agency;
	}

	public void setAgency(String agency) {
		this.agency = agency;
	}

	public String getOpenBidTime() {
		return openBidTime;
	}

	public void setOpenBidTime(String openBidTime) {
		this.openBidTime = openBidTime;
	}

	public String getOpenBidPlace() {
		return openBidPlace;
	}

	public void setOpenBidPlace(String openBidPlace) {
		this.openBidPlace = openBidPlace;
	}

	public String getRemarks() {
		return remarks;
	}

	public void setRemarks(String remarks) {
		this.remarks = remarks;
	}

	public String getExpertType() {
		return expertType;
	}

	public void setExpertType(String expertType) {
		this.expertType = expertType;
	}

	public Integer getMethod() {
		return method;
	}

	public void setMethod(Integer method) {
		this.method = method;
	}

	public Integer getTotal() {
		return total;
	}

	public void setTotal(Integer total) {
		this.total = total;
	}

	public Integer getSeniorNum() {
		return seniorNum;
	}

	public void setSeniorNum(Integer seniorNum) {
		this.seniorNum = seniorNum;
	}

	public Integer getLocalNum() {
		return localNum;
	}

	public void setLocalNum(Integer localNum) {
		this.localNum = localNum;
	}

	public String getExpertTypeName() {
		return expertTypeName;
	}

	public void setExpertTypeName(String expertTypeName) {
		this.expertTypeName = expertTypeName;
	}

	public String getZone() {
		return zone;
	}

	public void setZone(String zone) {
		this.zone = zone;
	}

	public String getZoneName() {
		return zoneName;
	}

	public void setZoneName(String zoneName) {
		this.zoneName = zoneName;
	}
	
}
