package com.hzw.sunflower.entity;




import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class ProjectInfo implements Serializable {

    //处室编码
    private Long departmentId;

    //处室名称
    private String departmentName;

    //委托金额
    private BigDecimal entrustedAmount;

    //币种
    private Integer entrustedCurrency;

    //汇率
    private String exchangeRate;
    //项目时间
    private Date projectDate;

}
