package com.hzw.sunflower.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hzw.sunflower.common.BaseBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 银行回单明细
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-12
 */
@Data
@TableName("t_bond_bank_receipt")
@ApiModel(value = "BondBankReceipt对象", description = "银行回单明细")
public class BondBankReceipt extends BaseBean implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("关联标识号 如收款流水号或支付请求号")
    @TableField("relation_num")
    private String relationNum;

    @ApiModelProperty("交易日期")
    @TableField("date")
    private String date;

    @ApiModelProperty("交易时间")
    @TableField("time")
    private String time;

    @ApiModelProperty("回单银行 1.中国银行 2.南京银行 3.民生银行")
    @TableField("bank_type")
    private Integer bankType;

    @ApiModelProperty("回单文件ossid")
    @TableField("receipt_oss_file_id")
    private Long receiptOssFileId;

    @ApiModelProperty("回单文件名称")
    @TableField("file_name")
    private String fileName;

    @ApiModelProperty("回单类型 1.收款流水回单 2.支付回单")
    @TableField("receipt_type")
    private Integer receiptType;



}
