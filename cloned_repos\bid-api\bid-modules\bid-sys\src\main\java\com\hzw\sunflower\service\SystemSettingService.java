package com.hzw.sunflower.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hzw.sunflower.entity.SystemSetting;

public interface SystemSettingService extends IService<SystemSetting> {
    /**
     * 保存
     * @param systemSetting
     * @return
     */
    Boolean insertSystemSetting(SystemSetting systemSetting);

    /**
     * 回显
     * @param req
     * @return
     */
    Object querySystemSetting(SystemSetting req);


}
