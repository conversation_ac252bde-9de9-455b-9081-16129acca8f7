package com.hzw.sunflower.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hzw.sunflower.controller.condition.TemCondition;

import com.hzw.sunflower.controller.response.TemplateVo;
import com.hzw.sunflower.entity.Template;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @since 2022/6/15
 */
public interface SenMessMapper extends BaseMapper<Template> {

    IPage<TemplateVo> findListByCondition(IPage<Template> page, @Param("condition") TemCondition condition, @Param("userId") Long userId);

}
