package com.hzw.sunflower.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.hzw.sunflower.common.BaseBean;
import io.swagger.annotations.ApiModel;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
* Created by pj on 2023/11/17
*/
@ApiModel("t_seal_management_operate_log表")
@TableName("t_seal_management_operate_log")
@Data
public class SealManagementOperateLog extends BaseBean implements Serializable {
    private Long id;

    /**
     * 印章id
     *
     * @mbg.generated
     */
    private Long sealId;

    /**
     * 操作人
     *
     * @mbg.generated
     */
    private String operateUser;

    /**
     * 操作
     *
     * @mbg.generated
     */
    private String operate;

    /**
     * 创建人
     *
     * @mbg.generated
     */
    private Long createdUserId;


    private static final long serialVersionUID = 1L;
}