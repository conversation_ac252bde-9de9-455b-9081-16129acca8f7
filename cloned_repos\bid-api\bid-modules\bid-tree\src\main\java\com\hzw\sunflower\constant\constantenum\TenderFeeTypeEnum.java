package com.hzw.sunflower.constant.constantenum;

/**
 * <AUTHOR>
 * @date ：Created in 2021/6/30 10:27
 * @description：标书费类型
 * @version: 1.0
 */
public enum TenderFeeTypeEnum {
    FREE(0, "不收取"),
    GROUP(1, "按套"),
    SECTION(2, "按标段/包"),
    CHARGE(3, "收取");

    private Integer value;

    private String name;

    TenderFeeTypeEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    public Integer getValue() {
        return value;
    }

    public String getName() {
        return name;
    }
}
