package com.hzw.sunflower.controller.project.request.bidder;

import com.hzw.sunflower.common.BaseBean;
import com.hzw.sunflower.controller.project.request.EntrustProjectReq;
import com.hzw.sunflower.entity.ProjectTendererPower;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2021/6/10 11:01
 * @description：招标人项目
 * @modified By：`
 * @version: 1.0
 */
@Data
@ApiModel(description = "招标新建项目类")
public class BidderProjectReq extends BaseBean {
    /**
     * 版本号
     */
    private static final long serialVersionUID = -4256128078404775662L;

    @ApiModelProperty(value = "项目id",  required = true)
    private Long id;

    @ApiModelProperty(value = "委托项目名称",  required = true)
    private String projectName;

    @ApiModelProperty(value = "委托项目编号")
    private List<EntrustProjectReq> entrustProjectNumList;

    @ApiModelProperty(value = "招标人权限表")
    private List<ProjectTendererPower> powerList;

    @ApiModelProperty(value = "代理机构名称", required = true)
    private String agentCompany;

    @ApiModelProperty(value = "代理机构id")
    private Long agentId;

    @ApiModelProperty(value = "代理机构部门id")
    private Long agentDepartmentId;

    @ApiModelProperty(value = "代理机构联系人id")
    private Long agentChargeId;

    @ApiModelProperty(value = "代理机构用户名", required = true)
    private String agentChargeName;

    @ApiModelProperty(value = "代理机构用户联系方式", required = true)
    private String agentChargePhone;

    @ApiModelProperty(value = "项目所在地（省）")
    private Integer addressProvince;

    @ApiModelProperty(value = "项目所在地（市/区）")
    private Integer addressCity;

    @ApiModelProperty(value = "委托金额")
    private BigDecimal entrustedAmount;

    @ApiModelProperty(value = "委托金额币种")
    private Integer entrustedCurrency;

    @ApiModelProperty(value = "委托合同文件")
    private String uploadFileId;

    @ApiModelProperty(value = "项目id")
    private Long projectId;

    @ApiModelProperty(value = "退回原因")
    private String reason;
}
