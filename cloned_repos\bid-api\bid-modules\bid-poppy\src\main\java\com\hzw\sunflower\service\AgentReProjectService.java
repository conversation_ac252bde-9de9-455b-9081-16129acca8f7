package com.hzw.sunflower.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hzw.sunflower.controller.project.request.AgentReProjectReq;
import com.hzw.sunflower.controller.project.request.PurchaseNumberReq;
import com.hzw.sunflower.dto.AgentProjectDTO;
import com.hzw.sunflower.dto.ProjectRecordDTO;
import com.hzw.sunflower.entity.JwtUser;
import com.hzw.sunflower.entity.Project;

import java.util.List;

public interface AgentReProjectService extends IService<Project> {
    /**
     * 代理机构新增委托项目入口
     *
     */
    Boolean addAgentReProject(AgentReProjectReq agentReProjectReq, JwtUser user);

    /**
     * 根据采购项目编号 查询项目信息
     *
     * @param purchaseNumber
     * @return
     */
    AgentProjectDTO getProjectByPurchaseNumber(String purchaseNumber);

    /**
     * 重新招标历史记录
     *
     * @param projectId
     * @return
     */
    List<ProjectRecordDTO> getRecordByProjectId(Long projectId);

    /**
     * 重新采购根据项目编号查询项目信息
     * @param req
     * @return
     */
    AgentProjectDTO getByPurchaseNumberRe(PurchaseNumberReq req);
}
