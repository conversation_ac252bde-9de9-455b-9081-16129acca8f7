package com.hzw.sunflower.controller.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @ClassName:SealFileSectionREQ
 * @Auther: lijinxin
 * @Description: 标段查询文件请求
 * @Date: 2023/11/15 15:55
 * @Version: v1.0
 */
@ApiModel("标段查询文件请求")
@Data
public class SealFileSectionREQ {


    @ApiModelProperty("标段id")
    private Long sectionId;

    @ApiModelProperty("标段id")
    private List<Long> sectionIds;


    @ApiModelProperty("1.澄清/修改 2.异议回复 3.通知 4.函件 5.请示 6.报告 7.投标文件 8.评标报告 9.采购文件 10.其他 11.中标通知书")
    private Integer applyItem;


}
