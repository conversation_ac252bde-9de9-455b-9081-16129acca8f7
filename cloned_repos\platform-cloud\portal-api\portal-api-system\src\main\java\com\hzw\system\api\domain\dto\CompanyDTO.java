package com.hzw.system.api.domain.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.hzw.system.api.domain.SysUser;
import lombok.Data;

import java.util.List;

@Data
public class CompanyDTO {
    /**
     * 主键
     */
    private String id;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 单位类型 1招标人2供应商
     */
    private Integer companyType;

    /**
     * 单位状态  0 正常 1 注销
     */
    private Integer companyStatus;

    /**
     * 组织机构代码
     */
    private String organizationNum;

    /**
     * 是否被企查查验证过 0:否  1:是
     */
    private Integer isValidate;

    /**
     * 法定代表人
     */
    private String legalRepresentative;

    /**
     * 法定代表人身份证
     */
    private String legalRepresentativeIdentity;

    /**
     * 供应商是否被禁用  0：正常  1：禁用
     */
    private Integer supplierDisable;

    /**
     * 营业执照文件id
     */
    private String licenseFileId;

    /**
     * 招标人是否被禁用 0：正常  1：禁用
     */
    private Integer bidderDisable;

    /**
     * 代理机构是否被禁用 0：正常  1：禁用
     */
    private Integer agencyDisable;

    /**
     * 企业简称
     */
    private String abbreviation;


    /**
     * 企业电话
     */
    private String telephone;

    /**
     * 地址
     */
    private String address;

    /**
     * 户名
     */
    private String bankAccountName;

    /**
     * 银行卡号
     */
    private String bankAccount;

    /**
     * 开户行
     */
    private String bankDeposit;

    /**
     * 传真
     */
    private String fax;

    /**
     * 数据来源 0:注册 1:初始化
     */
    private Integer dataSource;

    /**
     * 公司人员
     */
    private List<SysUser> userList;

    /**
     * 开启供应商关注确认 1.是 0.否
     */
    private Integer isOpenFollowConfirm;

    /**
     * 开启已报价供应商名单查看 1.是 2.否
     */
    private Integer isOpenQuoteList;

    /**
     * 市
     */
    private String city;
}
