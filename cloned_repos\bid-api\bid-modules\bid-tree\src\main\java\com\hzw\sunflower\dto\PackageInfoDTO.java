package com.hzw.sunflower.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * Created by zgq on 2021/07/05
 * 包段的基本信息，包括其所关联的公告ID
 */

@ApiModel(description = "包段关联信息")
@Data
@Accessors(chain = true)
public class PackageInfoDTO {

    @ApiModelProperty(value = "包段ID", position = 1)
    private Long packageId;

    @ApiModelProperty(value = "包段编号", position = 2)
    private String packageNumber;

    @ApiModelProperty(value = "包段名", position = 3)
    private String packageName;

    @ApiModelProperty(value = "所属公告ID", position = 4)
    private Long noticeId;

    @ApiModelProperty(value = "采购方式", position = 5)
    private String purchaseMode;

    @ApiModelProperty(value = "采购方式中文名称", position = 6)
    private String purchaseModeName;

    @ApiModelProperty(value = "1 依法必招  2 非依法必招  3 政府采购  4 国际招标", position = 7)
    private String purchaseType;

    @ApiModelProperty(value = "1-预审公开 2-后审公开 3-邀请 4-二阶段 5-政府公开 6-政府邀请", position = 8)
    private String purchaseStatus;

    @ApiModelProperty(value = "委托金额", position = 9)
    private BigDecimal entrustMoney;

    @ApiModelProperty(value = "资格预审方式 0 按包，1 按项目", position = 10)
    private String preQualificationMtd;

    @ApiModelProperty(value = "币种", position = 11)
    private Long entrustCurrency;

    @ApiModelProperty(value = "是否可以关注：0 可以  1：不可", position = 11)
    private Integer canSearch;
}
