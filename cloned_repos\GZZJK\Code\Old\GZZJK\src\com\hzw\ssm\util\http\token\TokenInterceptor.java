/*
 * 公司名称：江苏华招网信息技术有限公司
 * 版权信息：江苏华招网信息技术有限公司版权所有
 * 文件名称：TokenInterceptor.java
 * 修改时间：2020年8月18日
 * 修改人：陈山杉
 * 修改内容：
 * 跟踪单号：
 * 修改单号 ：
 */

package com.hzw.ssm.util.http.token;

import org.apache.struts2.ServletActionContext;

import com.hzw.ssm.sys.user.entity.UserEntity;
import com.opensymphony.xwork2.ActionContext;
import com.opensymphony.xwork2.ActionInvocation;
import com.opensymphony.xwork2.interceptor.Interceptor;
import com.opensymphony.xwork2.interceptor.MethodFilterInterceptor;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <一句话功能简述> token验证拦截 <功能详细描述>
 * 
 * <AUTHOR>
 * @version V0.0.1-SNAPSHOT
 */
public class TokenInterceptor extends MethodFilterInterceptor implements Interceptor {

	private static final long serialVersionUID = 1L;
	
	/** tookencookie时间 */
	private final static Integer TOKEN_COOKIE_TIME_OUT = 240 * 60;
	
	@Override
	protected String doIntercept(ActionInvocation invocation) throws Exception {
		HttpServletRequest request = ServletActionContext.getRequest();
		HttpServletResponse response = ServletActionContext.getResponse();

		response.setCharacterEncoding("utf-8");

		Cookie[] cookies = request.getCookies();
		
		UserEntity user = (UserEntity) ActionContext.getContext().getSession().get("userInfo");

		if (user != null) {
			
			for (Cookie cookie : cookies) {
				if (cookie.getName().equals(user.getLogin_code()+"-accessToken")){
					
					String token = cookie.getValue();
					
					if (null != token) {
						// 验证token是否正确
						boolean resultToken = JwtUtil.verify(token);
						if (resultToken) {
							
							// 每次都返回新的token
			/*				String tokenNew = JwtUtil.sign(user.getLogin_code());
							if (tokenNew != null) {
								Cookie tokenCookie = new Cookie(user.getLogin_code()+"-accessToken", tokenNew);
								// tokencookie设置超时时间15分钟
								tokenCookie.setMaxAge(TOKEN_COOKIE_TIME_OUT);
								tokenCookie.setPath("/");
								response.addCookie(tokenCookie);
							}*/
							return invocation.invoke();
							
						} else {
							return "login";
						}
					}
					
				}
		
				
			}
			
			// 每次都返回新的token
			String tokenNew = JwtUtil.sign(user.getLogin_code());
			if (tokenNew != null) {
				Cookie tokenCookie = new Cookie(user.getLogin_code()+"-accessToken", tokenNew);
				// tokencookie设置超时时间15分钟
				tokenCookie.setMaxAge(TOKEN_COOKIE_TIME_OUT);
				tokenCookie.setPath("/");
				response.addCookie(tokenCookie);
			}
			return invocation.invoke();
		} else {
			return "login";
		}
		
	}

}
