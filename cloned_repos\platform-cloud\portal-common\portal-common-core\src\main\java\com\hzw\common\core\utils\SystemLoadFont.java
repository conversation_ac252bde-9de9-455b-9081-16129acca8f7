package com.hzw.common.core.utils;

import java.awt.Font;
import java.io.File;
import java.io.FileInputStream;



/**
 * 加载中文字体库
 *
 * <AUTHOR>
 *
 */
public class SystemLoadFont {

    /**
     * 本地读取方法
     * @param path 文件路径
     * @param style 字体样式
     * @param fontSize 字体大小
     * @return
     */
    public static java.awt.Font styleFont(String path,int style,float fontSize) {
        Font font = SystemLoadFont.loadStyleFont(path,style,fontSize);// 调用
        return font;
    }
    /**
     *
     * @param fontFileName 外部字体名
     * @param style 字体样式
     * @param fontSize 字体大小
     * @return
     */
    public static Font loadStyleFont(String fontFileName,int style, float fontSize) {
        try{
            File file = new File(fontFileName);
            FileInputStream in = new FileInputStream(file);
            Font dynamicFont = Font.createFont(Font.TRUETYPE_FONT, in);
            Font dynamicFontPt =  dynamicFont.deriveFont(style,fontSize);
            in.close();
            return dynamicFontPt;
        }catch(Exception e) {//异常处理
            e.printStackTrace();
            return new java.awt.Font("宋体", Font.PLAIN, 20);
        }
    }
}
