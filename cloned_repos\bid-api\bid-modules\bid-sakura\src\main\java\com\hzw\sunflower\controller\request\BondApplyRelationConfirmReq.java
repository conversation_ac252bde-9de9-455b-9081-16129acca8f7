package com.hzw.sunflower.controller.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(description = "保证金关联申请同意/退回入参")
@Data
public class BondApplyRelationConfirmReq {

    @ApiModelProperty(value = "关联申请id")
    private Long applyId;

    @ApiModelProperty(value = "付款户名")
    private String payCompanyName;

    @ApiModelProperty(value = "退回原因")
    private String returnMsg;

    @ApiModelProperty(value = "同意备注")
    private String agreeMsg;

    @ApiModelProperty(value = "处理结果 1.同意 2.退回",required = true)
    private Integer type;

}
