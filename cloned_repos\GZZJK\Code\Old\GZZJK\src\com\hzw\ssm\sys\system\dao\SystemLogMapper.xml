<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper 
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hzw.ssm.sys.system.dao.SystemLogMapper">

	<!-- 如果需要查询缓存，将cache的注释去掉即可
	<cache />
	-->

	<!-- 查询系统日志-->
	<select id="queryPageLog" parameterType="SystemLogEntity"
		resultType="SystemLogEntity">
		SELECT
		log_id,opa_user,opa_time,opa_ip,opa_fun,opa_method 
		from ts_systemlog
		where 1 = 1
		<if test="OPA_USER != null and OPA_USER != ''">
			and OPA_USER like '%${OPA_USER}%'
		</if>
		<if test="start_date != null and start_date!=''">
			and OPA_TIME &gt;= to_date('${start_date} 00:00:00','yyyy-MM-dd hh24:mi:ss')
		</if>
		<if test="end_date != null and end_date!=''">
			and OPA_TIME &lt;= to_date('${end_date} 23:59:59','yyyy-MM-dd hh24:mi:ss')
		</if>
		order by create_time desc
	</select>

	<!-- 插入系统日志 -->
	<insert id="saveSystemLog" parameterType="SystemLogEntity">
		insert into ts_systemlog 
		  (log_id,
		   opa_user,
		   opa_time,
		   opa_ip,
		   opa_fun,
		   opa_method,
		   log_type,
		   create_id,
		   create_time,
		   modify_id,
		   modify_time,
		   delete_flag)
		values
		  ('${LOG_ID}',#{OPA_USER},sysdate,#{OPA_IP,jdbcType=VARCHAR},#{OPA_FUN,jdbcType=VARCHAR},#{OPA_METHOD,jdbcType=VARCHAR},null,#{create_id},#{create_time},#{modify_id},#{modify_time},#{delete_flag})
	</insert>
</mapper>