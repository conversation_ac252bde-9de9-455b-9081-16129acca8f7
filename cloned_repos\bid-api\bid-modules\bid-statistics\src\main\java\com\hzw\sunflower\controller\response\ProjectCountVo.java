package com.hzw.sunflower.controller.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class ProjectCountVo {

    @ApiModelProperty("数量")
    private Integer count;

    @ApiModelProperty("百分比")
    private String percent;

    @ApiModelProperty("涨幅 1下降  2上升 ")
    private Integer increase;

    @ApiModelProperty("类型 1总数  2进行中  3已完结")
    private Integer type;

}
