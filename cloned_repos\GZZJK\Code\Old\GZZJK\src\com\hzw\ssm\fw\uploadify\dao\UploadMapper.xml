<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper 
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd"> 

<mapper namespace="com.hzw.ssm.fw.uploadify.dao.UploadMapper">

	<!-- 如果需要查询缓存，将cache的注释去掉即可
	<cache />
	-->

	<!-- 新增出差申请记录 -->
	<insert id="insertFile" parameterType="FileEntity">
		insert into T_FILE(
			FILE_ID,FILE_NAME,FILE_PATH,CREATE_TIME,DELETE_FLAG
		) values(#{fileId},#{fileName},#{filePath},sysdate,0)
	</insert>
	
</mapper>