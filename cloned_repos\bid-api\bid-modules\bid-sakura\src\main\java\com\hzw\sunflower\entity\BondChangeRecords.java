package com.hzw.sunflower.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.hzw.sunflower.common.BaseBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 保证金调整记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-28
 */
@Data
@TableName("t_bond_change_records")
@ApiModel(value = "BondChangeRecords对象", description = "保证金调整记录表")
public class BondChangeRecords extends BaseBean implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("业务id")
    @TableField("project_id")
    private Long projectId;

    @ApiModelProperty("标段id")
    @TableField("section_id")
    private Long sectionId;

    @ApiModelProperty("供应商id")
    @TableField("company_id")
    private Long companyId;

    @ApiModelProperty("操作 1.供应商确认关联（次月触发）2.供应商手动关联（次月或者再次关联）3.项目经理手动关联（次月或者再次关联）4.运管处手动关联（次月触发）")
    @TableField("operation")
    private Integer operation;

    @ApiModelProperty("调整后金额")
    @TableField("amount")
    private BigDecimal amount;

    @ApiModelProperty("调整前项目id")
    @TableField("former_project_id")
    private Long formerProjectId;

    @ApiModelProperty("调整前标段id")
    @TableField("former_section_id")
    private Long formerSectionId;

    @ApiModelProperty("调整人id")
    @TableField("change_user_id")
    private Long changeUserId;

    @ApiModelProperty("调整时间")
    @TableField("change_time")
    private Date changeTime;

}
