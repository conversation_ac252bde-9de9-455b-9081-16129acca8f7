package com.hzw.sunflower.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hzw.sunflower.common.BaseBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <p>
 * 招标文件编制表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-26
 */
@Getter
@Setter
@TableName("t_bid_doc_compile")
@ApiModel(value = "BidDocCompile对象", description = "招标文件编制表")
public class BidDocCompile extends BaseBean implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("项目id")
    @TableField("project_id")
    private Long projectId;

    @ApiModelProperty("模板id")
    @TableField("template_id")
    private Long templateId;

    @ApiModelProperty("招标文件id")
    @TableField("file_id")
    private Long fileId;

    @ApiModelProperty("状态：1编辑中，2已完成")
    @TableField("status")
    private Integer status;

    @ApiModelProperty("签章后文件")
    @TableField("sign_file")
    private Long signFile;

    @ApiModelProperty("签章前文件")
    @TableField("before_sign_file")
    private Long  beforeSignFile;

    @ApiModelProperty("签章状态 1 已签章 2 未签章 3 无需签章")
    @TableField("sign_status")
    private Integer signStatus;

    @ApiModelProperty("生成的压缩文件id")
    @TableField("zip_file_id")
    private Long zipFileId;
}
