/*
 * 公司名称：江苏华招网信息技术有限公司
 * 版权信息：江苏华招网信息技术有限公司版权所有
 * 文件名称：ExperOutEntity.java
 * 修改时间：2021年2月23日
 * 修改人：宋伟
 * 修改内容：
 * 跟踪单号：
 * 修改单号 ：
 */

package com.hzw.ssm.expert.entity;

import java.util.Date;
import java.util.List;

import com.hzw.ssm.fw.base.BaseEntity;
import com.hzw.ssm.fw.util.Page;

/**
 * <一句话功能简述> 专家推荐相关实体类
 * 
 * <AUTHOR>
 * @version V1.0[产品/模块版本]
 */
public class ExpertRecommendEntity extends BaseEntity {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	private String id;
	/**
	 * 分页
	 */
	private Page page;
	/**
	 * 计划id
	 */
	private String planId;
	/**
	 * 推荐记录id
	 */
	private String recommendId;
	/**
	 * 用户id
	 */
	private String userId;
	/**
	 * 推荐业务员id
	 */
	private String recommenderId;
	/**
	 * 推荐业务员名称
	 */
	private String recommenderName;
	/**
	 * 专家名称
	 */
	private String expertName;
	/**
	 * 专家编号
	 */
	private String expertNum;

	/**
	 * 推荐业务员部门
	 */
	private String department;
	/**
	 * 计划推荐人数
	 */
	private Integer recommendNumber;
	/**
	 * 已推荐人数
	 */
	private Integer isNumber;
	/**
	 * 推荐业务员二维码id
	 */
	private String qrCode;
	/**
	 * 计划名称
	 */
	private String planName;
	/**
	 * 备注
	 */
	private String remark;
	/**
	 * 手机号
	 */
	private String mobilePhone;
	/**
	 * 专家状态
	 */
	private String status;
	/**
	 * 专家状态
	 */
	private List<String> statusList;
	/**
	 * 计划年份
	 */
	private String year;
	/**
	 * 创建人名称
	 */
	private String createName;
	/**
	 * 修改人名称
	 */
	private String modifierName;
	/**
	 * 剩余推荐人数
	 */
	private Integer surplusNumber;

	/**
	 * 开始时间
	 */
	private Date startTime;
	/**
	 * 结束时间
	 */
	private Date endTime;

	/**
	 * @return the page
	 */
	public Page getPage() {
		return page;
	}

	/**
	 * @param page
	 *            the page to set
	 */
	public void setPage(Page page) {
		this.page = page;
	}

	/**
	 * @return the planId
	 */
	public String getPlanId() {
		return planId;
	}

	/**
	 * @param planId
	 *            the planId to set
	 */
	public void setPlanId(String planId) {
		this.planId = planId;
	}

	/**
	 * @return the userId
	 */
	public String getUserId() {
		return userId;
	}

	/**
	 * @param userId
	 *            the userId to set
	 */
	public void setUserId(String userId) {
		this.userId = userId;
	}

	/**
	 * @return the recommendId
	 */
	public String getRecommendId() {
		return recommendId;
	}

	/**
	 * @param recommendId
	 *            the recommendId to set
	 */
	public void setRecommendId(String recommendId) {
		this.recommendId = recommendId;
	}

	/**
	 * @return the recommendNumber
	 */
	public Integer getRecommendNumber() {
		return recommendNumber;
	}

	/**
	 * @param recommendNumber
	 *            the recommendNumber to set
	 */
	public void setRecommendNumber(Integer recommendNumber) {
		this.recommendNumber = recommendNumber;
	}

	/**
	 * @return the qrCode
	 */
	public String getQrCode() {
		return qrCode;
	}

	/**
	 * @param qrCode
	 *            the qrCode to set
	 */
	public void setQrCode(String qrCode) {
		this.qrCode = qrCode;
	}

	/**
	 * @return the planName
	 */
	public String getPlanName() {
		return planName;
	}

	/**
	 * @param planName
	 *            the planName to set
	 */
	public void setPlanName(String planName) {
		this.planName = planName;
	}

	/**
	 * @return the remark
	 */
	public String getRemark() {
		return remark;
	}

	/**
	 * @param remark
	 *            the remark to set
	 */
	public void setRemark(String remark) {
		this.remark = remark;
	}

	/**
	 * @return the recommenderId
	 */
	public String getRecommenderId() {
		return recommenderId;
	}

	/**
	 * @param recommenderId
	 *            the recommenderId to set
	 */
	public void setRecommenderId(String recommenderId) {
		this.recommenderId = recommenderId;
	}

	/**
	 * @return the recommenderName
	 */
	public String getRecommenderName() {
		return recommenderName;
	}

	/**
	 * @param recommenderName
	 *            the recommenderName to set
	 */
	public void setRecommenderName(String recommenderName) {
		this.recommenderName = recommenderName;
	}

	/**
	 * @return the id
	 */
	public String getId() {
		return id;
	}

	/**
	 * @param id
	 *            the id to set
	 */
	public void setId(String id) {
		this.id = id;
	}

	/**
	 * @return the year
	 */
	public String getYear() {
		return year;
	}

	/**
	 * @param year
	 *            the year to set
	 */
	public void setYear(String year) {
		this.year = year;
	}

	/**
	 * @return the createName
	 */
	public String getCreateName() {
		return createName;
	}

	/**
	 * @param createName
	 *            the createName to set
	 */
	public void setCreateName(String createName) {
		this.createName = createName;
	}

	/**
	 * @return the modifierName
	 */
	public String getModifierName() {
		return modifierName;
	}

	/**
	 * @param modifierName
	 *            the modifierName to set
	 */
	public void setModifierName(String modifierName) {
		this.modifierName = modifierName;
	}

	/**
	 * @return the department
	 */
	public String getDepartment() {
		return department;
	}

	/**
	 * @param department
	 *            the department to set
	 */
	public void setDepartment(String department) {
		this.department = department;
	}

	/**
	 * @return the surplusNumber
	 */
	public Integer getSurplusNumber() {
		return surplusNumber;
	}

	/**
	 * @param surplusNumber
	 *            the surplusNumber to set
	 */
	public void setSurplusNumber(Integer surplusNumber) {
		this.surplusNumber = surplusNumber;
	}

	/**
	 * @return the expertName
	 */
	public String getExpertName() {
		return expertName;
	}

	/**
	 * @param expertName
	 *            the expertName to set
	 */
	public void setExpertName(String expertName) {
		this.expertName = expertName;
	}

	/**
	 * @return the mobilePhone
	 */
	public String getMobilePhone() {
		return mobilePhone;
	}

	/**
	 * @param mobilePhone
	 *            the mobilePhone to set
	 */
	public void setMobilePhone(String mobilePhone) {
		this.mobilePhone = mobilePhone;
	}

	/**
	 * @return the expertNum
	 */
	public String getExpertNum() {
		return expertNum;
	}

	/**
	 * @param expertNum
	 *            the expertNum to set
	 */
	public void setExpertNum(String expertNum) {
		this.expertNum = expertNum;
	}

	/**
	 * @return the status
	 */
	public String getStatus() {
		return status;
	}

	/**
	 * @param status
	 *            the status to set
	 */
	public void setStatus(String status) {
		this.status = status;
	}

	

	/**
	 * @return the startTime
	 */
	public Date getStartTime() {
		return startTime;
	}

	/**
	 * @param startTime
	 *            the startTime to set
	 */
	public void setStartTime(Date startTime) {
		this.startTime = startTime;
	}

	/**
	 * @return the endTime
	 */
	public Date getEndTime() {
		return endTime;
	}

	/**
	 * @param endTime
	 *            the endTime to set
	 */
	public void setEndTime(Date endTime) {
		this.endTime = endTime;
	}

	/**
	 * @return the statusList
	 */
	public List<String> getStatusList() {
		return statusList;
	}

	/**
	 * @param statusList the statusList to set
	 */
	public void setStatusList(List<String> statusList) {
		this.statusList = statusList;
	}

	/**
	 * @return the isNumber
	 */
	public Integer getIsNumber() {
		return isNumber;
	}

	/**
	 * @param isNumber the isNumber to set
	 */
	public void setIsNumber(Integer isNumber) {
		this.isNumber = isNumber;
	}

	
}
