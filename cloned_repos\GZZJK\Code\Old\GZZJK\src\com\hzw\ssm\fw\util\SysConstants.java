package com.hzw.ssm.fw.util;

import java.util.HashMap;
import java.util.Map;


/*******************************************************************************
 * Description：数据宏定义
 * 
 * @file: Constants.java
 * 
 * @version:1.0.0 Copyright
 ******************************************************************************/
public interface SysConstants {
	/** 参数名称 */
	public final static String PARAM_NAME = "menuId";
	
	/** 顶级菜单 parent_menu_id 为 -1 */
	public final static String ROOT_MENU = "-1";
	/** 项目评审时间相隔多天*/
	public final static int BETWEEN_DAY = 20;
	
	/** 按钮类型1:按钮;2:超链接 */
	public interface BUTTON_TYPE {

		public final static Integer BUTTON_TYPE_ONE = 1;

		public final static Integer BUTTON_TYPE_TWO = 2;
	}

	/**
	 * 推荐码相对路径
	 */
	public final static String QR_CODE_PATH="";
	/** 留言板咨询查看状态Map */
	public final static String MESSAGE_BOARD_STATUS_MAP = "{1:'未查看',2:'已查看'}";

	/** 留言板咨询查看状态 */
	public interface MESSAGE_BOARD_STATUS {

		public final static String WATCH = "2";

		public final static String NOT_WATCH = "1";
	}

	/**
	 * 用户职务级别
	 * 
	 * <AUTHOR>
	 * 
	 */
	public interface USER_POST {
		/** 部门员工 */
		public final static Long EMPLOYEES = 1L;
		/** 部门经理 */
		public final static Long DEPARTMENT_MANAGER = 2L;
		/** 总经理 */
		public final static Long GENERAL_MANAGER = 3L;
	}

	/** 职位信息状态Map */
	public final static String JOB_STATUS_MAP = "{1:'未发布',2:'已发布',3:'已撤销'}";

	/** 职位信息状态 */
	public interface JOB_STATUS {
		/** 已撤销 */
		public final static String PUBLISH = "2";
		/** 未发布 */
		public final static String NOT_PUBLISH = "1";
		/** 已发布 */
		public final static String CANCEL = "3";
	}

	/** 信息状态Map */
	public final static String MESSAGE_STATUS_MAP = "{1:'未发布',2:'已发布',3:'已撤销'}";

	/** 信息状态 */
	public interface MESSAGE_STATUS {
		/** 未发布 */
		public final static String PUBLISH = "2";
		/** 已发布 */
		public final static String NOT_PUBLISH = "1";
		/** 已撤销 */
		public final static String CANCEL = "3";
	}

	/** 信息类型Map */
	public final static String MESSAGE_TYPE_MAP = "{1:'公司新闻',2:'行业新闻',3:'法律法规',4:'人才战略',5:'招贤纳士',6:'售后服务',7:'公司荣誉',8:'公司简介',9:'应聘登记表下载',10:'公司地址',11:'医疗设备租赁',12:'环保设备租赁',13:'租赁模式',14:'融资租赁优势'}";

	/** 信息类型 */
	public interface MESSAGE_TYPE {
		/** 公司新闻 */
		public final static String COMPLANY_NEWS = "1";
		/** 行业新闻 */
		public final static String TRADE_NEWS = "2";
		/** 法律法规 */
		public final static String LAWS = "3";
		/** 人才战略 */
		public final static String TALENT = "4";
		/** 招贤纳士 */
		public final static String EMPLOYS = "5";
		/** 售后服务 */
		public final static String CUSTOMER_SERVICES = "6";
		/** 公司荣誉 */
		public final static String COMPLANY_HONORS = "7";
		/** 公司简介 */
		public final static String INTRODUCE = "8";
		/** 应聘登记表下载 */
		public final static String DOWNLOAD_FILE = "9";
		/** 公司地址 */
		public final static String COMPLANY_ADDRESS = "10";
		/** 医疗设备租赁 */
		public final static String MEDICAL_DEVICE = "11";
		/** 环保设备租赁 */
		public final static String ENVIROMENTAL_COMPLANY_DEVICE = "12";
		/**租赁模式*/
		public final static String LEASE_MODEL = "13";
		/**融资租赁优势*/
		public final static String LEASE_ADVANTAGE  = "14";
	}

	/** 附件类型 */
	public interface ATTACHMENT_YPE {
		/** 前台新闻附件 */
		public final static int NEWS_FILE = 1;
		/** 邮件附件 */
		public final static int MAIL_FILE = 2;
		/** 公司通知 */
		public final static int NOTICE_FILE = 3;
		/** 职位*/
		public final static int JOB_FILE = 4;
	}

	/**
	 * 部门名称
	 * 
	 * <AUTHOR>
	 * 
	 */
	public interface DEPARTMENT_NAME {
		public final static String SALES_DEPARTMENT = "业务部";
		public final static String WIND_CONTROL_DEPARTMENT = "风控部";

	}

	/** 通知信息状态Map*/
	public final static String NOTICE_STATUS_MAP="{1:'未发布',2:'已发布',3:'已撤销'}";
	
	/** 通知信息状态*/
	public interface NOTICE_STATUS {
		/** 已发布*/
		public final static String PUBLISH ="2";
		/** 未发布*/
		public final static String NOT_PUBLISH ="1";
		/** 已撤销*/
		public final static String CANCEL ="3";
	}
	/**时间格式样式*/
	public final static String DATE_FORMAT ="yyyy-MM-dd HH:mm:ss";
	
	public final static String FILE_PATH_ROOT ="upload/";
	
	public final static String EXPERT_FILE_PATH_ROOT="expertFile/";
	public final static String KEEP_FILE_PATH_ROOT="keepFile/";
	
	/** 项目状态 */
	public interface PROJECT_STATUS {
		/** 1：待抽取 */
		public final static Long WAIT = 1L;

		/** 2：抽取中 */
		public final static Long DOING = 2L;

		/** 3：已抽取 */
		public final static Long ALREADY = 3L;

		/** 4：指定抽取待审核 */
		public final static Long APPOINT_WAIT_AUDIT = 4L;

		/** 5：指定抽取审核通过 */
		public final static Long APPOINT_AUDIT_PASS = 5L;

		/** 6：指定抽取审核不通过 */
		public final static Long APPOINT_AUDIT_NOPASS = 6L;
		
		/** 7：信息保存 */
		public final static Long SAVE = 7L;

		/** 8：领导审核通过 */
		public final static Long LEADER_PASS = 8L;

		/** 9：领导审核不通过  */
		public final static Long LEADER_NOPASS = 9L;
		
		/** 10：指定专家已抽取  */
		public final static Long APPOINT_ALREADY = 10L;
		
		/** 20：再次抽取（人数不足）  */
		public final static Long AGAIN = 20L;
		
		/** 待抽取列表 */
		public final static String WAITLIST = "wait";
		
		/** 已抽取 */
		public final static String ALREADYLIST = "already";
	}
	
	/** 角色名称 */
	public interface ROLE_NAME {
		public final static String OPERATOR = "经办人";
		public final static String EXPERT = "专家";
		/** 机电中心抽取人 */
		 String CHOOSER = "机电中心抽取人";
		/** 系统管理员 */
		 String ADMIN = "系统管理员";
	}
	/**角色id*/
	public interface ROLE_ID{
		/**机电中心处长-审核人*/
		public final static String AUDITOR="20141017094028814021";
		/**专家*/
		public final static String EXPERT="20141010095817859054";
		/**主任*/
		public final static String DIRECTOR="20141017105452284107";
		/** 系统管理员 */
		public final static String ADMIN="99";
		/** 处长 */
		public final static String COMMISSIONER = "20150122103034522026";
		/** 机电中心抽取人*/
		public final static String EXTRACT = "20141103100939150087";
	}
	
	/**角色*/
	public interface ROLE{
		/**处长*/
		public final static String AUDITOR="处长";
		/**业务员*/
		public final static String SALESMAN="业务员";
		/**专家*/
		public final static String EXPERT="专家";
		/**机电中心抽取人*/
		public final static String EXTRACT="机电中心抽取人";
		/**机电中心处长*/
		public final static String JYZX_AUDITOR="机电中心处长";
		/**主任*/
		public final static String DIRECTOR="主任";
		/** 系统管理员 */
		public final static String ADMIN="系统管理员";
	}
	/** 录入入库标识 */
	public interface ENTER_FLAG{
		/** 管理员入库 */
		public final static Integer ADMIN_ENTER=1;
		/** 专家入库 */
		public final static Integer EXPERT_ENTER=2;
	}
	
	/** 专家被抽取限制时间 */
	public final static Integer EXPERT_EXTRACTION_TIME = -1;
	
	/** 专家抽取人数倍数 */
	public final static Integer EXPERT_EXTRACTION_MULTIPLE = 3;
	
	/** 超级密码 */
	public final static String SUPER_PASSWORD = "jsgz110";
	
	/**
	 * 短信类型
	 * 
	 */
	public interface SMS_TYPE {
		/** 邀请专家 */
		public final static Integer SMS_ONE = 1;
		/** 通知抽取人抽取专家 */
		public final static Integer SMS_TWO = 2;
		/** 通知项目负责人修改条件 */
		public final static Integer SMS_THREE = 3;
		/** 通知专家参标 */
		public final static Integer SMS_FOUR = 4;
		/** 取消、变更*/
		public final static Integer SMS_FIVE=5;
	}
	
	public interface DATA_SOURCE{
		/** 苏招易采 */
		public final static String SZYC = "szyc";
		
		/** 专家库 */
		public final static String ZJK = "zjk";
	}
	
	/**
     * <一句话功能简述>： 专家违规情况处理状态
     * 审核状态：处理状态：1：项目负责人已处理2：专家管理处已处理3：公司领导已处理
     * <AUTHOR>
     * @version V1.0[产品/模块版本]
     */
    public interface APPRAISEIllegal_STATUS {
        /** 1:项目负责人已处理 */
        public final static Integer dealtWithManager = 1;
        /** 2：专家管理处已处理 */
        public final static Integer dealtWithExpert   = 2;
        /** 3：公司领导已处理 */
        public final static Integer dealtWithExperLeader = 3;
    }
    
    /**
     * 审核状态
     * <AUTHOR>
     *
     */
    public interface AUDIT_STATUS{
    	 /** 1.审核不通过 */
    	public final static Long STATUS_0  = 0L;
    	/** 2.审核通过 */
    	public final static Long STATUS_1 = 1L;
    }
    /**
     * 审核类型（语音审核的流程：1.非工作日审核 2.开标半小时之后审核）
     * <AUTHOR>
     *
     */
    public interface AUDIT_TYPE{
    	 /** 1.非工作日审核 */
    	public final static String TYPE_1  = "1";
    	/** 2.开标2小时候后应急 */
    	public final static String TYPE_2 = "2";
    }
	/**
     * <一句话功能简述>：用户申请指定专家时专家的状态
     * 审核状态：处理状态：1：项目负责人已处理2：专家管理处已处理3：公司领导已处理
     * <AUTHOR>
     * @version V1.0[产品/模块版本]
     */
    public interface APPOINTS_EXPERTS_STAUS{
    	 /** 0.保存状态 */
        public final static Integer SAVE = 0;
        /** 1：库里已存在 */
        public final static Integer EXISTENT  = 1;
        /** 2：库里不存在 */
        public final static Integer NONEXISTENT = 2;
        
        /** 3：被占用 */
        public final static Integer EXTRACTED = 3;
    }
    /**
     * <一句话功能简述>：专家抽取结果，专家状态
     * 抽取之后专家状态：0.参加 1.不参加 2.未通知 3.短信已通知 4.短信未通知
     * <AUTHOR>
     *
     */
    public interface JOIN_STATUS{
    	 /** 0.参加 */
    	public final static long ATTEND = 0L;
    	/** 1.不参加 */
    	public final static long NOATTEND = 1L;
    	/** 2.未通知 */
    	public final static long NOINFORM = 2L;
    	/** 3.短信\语音已通知未回复 */
    	public final static long SMS_NOTIFICATION = 3L;
    }

    /**
     * 专家表中的专家状态
     * <AUTHOR>
     *
     */
    public interface EXPERT_STATUS{
    	/**
    	 * 1.注册待审核（等待审批员审批）
    	 */
    	public final static Integer NEW = 1;
    	
    	/**
    	 * 2.注册待复核（等待机电交易中心主任审批）
    	 */
    	public final static Integer NEW_AUDIT = 2;
    	
    	/** 3.正常专家*/
    	public final static Integer NORMAL = 3;
    	/** 3.正常专家long类型*/
    	public final static long NORMAL_L = 3;
    	/** 4.暂停专家*/
    	public final static Integer PAUSE=4;
    	/** 4.暂停专家long类型*/
    	public final static long PAUSE_L=4;
    	
    	/**
    	 * 5.修改待审核（审批员审批）
    	 */
    	public final static Integer MODIFY = 5;
    	/**
    	 * 6.修改待复核（机电交易中心主任审批）
    	 */
    	public final static Integer MODIFY_AUDIT = 6;
    }
    /**
     * 专家抽取方式
     * <AUTHOR>
     *
     */
    public interface CONDITION_METHOD{
    	/** 1.人工抽取*/
    	public final static long METHOD_ONE = 1;
    	/** 2.系统自动短信抽取 */
		public final static long METHOD_TWO = 2;
		/** 3.语音抽取 */
		public final static long METHOD_THREE = 3;
		
    }
    
    /**
     * 模板类型
     * <AUTHOR>
     *
     */
    public interface TEMPLATE_TYPE{
    	/** 1.*/
    	public final static long TYPE_ONE = 1;
    	/** 2.语音短信模板 */
		public final static long TYPE_TWO = 2;
		/** 3.语音模板 */
		public final static long TYPE_THREE = 3;
		/** 4.语音短信变更项目模板 */
		public final static long TYPE_FOUR = 4;
		/** 5.语音短信取消项目模板 */
		public final static long TYPE_FIVE = 5;
		/** 6.人数不足模板 */
		public final static long TYPE_SIX = 6;
    }
    
    /**
     * 是否有效
     * <AUTHOR>
     *
     */
    public interface IS_VALID{
    	/** 0.生效*/
    	public final static Integer VALID_ZERO = 0;
    	/** 1.失效 */
		public final static Integer VALID_ONE = 1;
    }
    /**
     * 专家语音原始的项目状态(专家参标的状态)
     * <AUTHOR>
     *
     */
    public interface CONDITION_CURRENT_STATUS{
    	/** 1.人工*/
    	public final static long METHOD_ONE = 1;
    	/** 2.语音 */
		public final static long METHOD_TWO = 2;
		/** 3.语音短信 */
		public final static long METHOD_THREE=3;
    }
    /**
     * 记录当前专家是语音抽取还是应急抽取(只使用与语音抽取功能)
     * <AUTHOR>
     *
     */
    public interface EXPERT_WAY{
    	/** 1.语音*/
    	public final static long WAY_ONE = 1;
    	/** 2.应急 */
		public final static long WAY_TWO = 2;
    }
    
    public interface IS_ACCEPT{
    	//成功
    	public final static Integer METHOD_ZONE=0;
    	//失败
    	public final static Integer METHOD_TWO=1;
    }
    /**
     * 当前项目状态(语音抽取)
     * <AUTHOR>
     *
     */
    public interface CURRENT_STATUS{
    	 /** 1.系统正常自动抽取 */
    	public final static Integer NORMAL = 1;
    	/** 2.应急抽取 */
    	public final static Integer EMERGENCY = 2;
    }
    
    public interface DEBARB_TYPE{
    	/**
    	 * 1.公司回避
    	 */
    	public final static String TYPE_1 = "1";
    	/**
    	 *  2.专家回避
    	 */
    	public final static String TYPE_2 = "2";
    	
    } 
    
    /**
     * （语音抽取）短信记录中，记录当前短信的状态
     * <AUTHOR>
     *
     */
    public interface CHANGE_RESULT{
    	/**
    	 * 1.通知专家参加评标
    	 */
    	public final static String RESULT_1 = "1";
    	/**
    	 * 2.项目变更
    	 */
    	public final static String RESULT_2 = "2";
    	/**
    	 * 3.项目取消
    	 */
    	public final static String RESULT_3 = "3";
    }
    /**
     * 语音通话遇到的状态
     */
    public final static Map CASE_MAP = new HashMap<String, String>(){{
    	put("1", "正常接通");
    	put("2", "呼叫遇忙");
    	put("3", "用户不在服务区");
	   	put("4", "用户无应答");
	   	put("5", "用户关机");
	   	put("6", "空号");
	   	put("7", "停机");
	   	put("8", "号码过期");
	   	put("9", "主叫应答，被叫应答前挂机");
	   	put("10", "正在通话中");
	   	put("11", "拒接");
	   	put("99", "其他");
	   	put("20", "主动取消呼叫");
    }};
    /**
     * 是否设置规则
     * <AUTHOR>
     *
     */
    public interface IS_RULE{
    	/** 1.是*/
    	public final static long METHOD_ONE = 1;
    	/** 2.否 */
		public final static long METHOD_TWO = 2;
    }
    
    /**
     * <一句话功能简述> 用户账户状态
     * <功能详细描述>
     * <AUTHOR>
     * @version V1.0[产品/模块版本]
     */
    public interface EnableFlag {
    	/**
    	 * ENABLE：1.启用
    	 */
    	Long ENABLE = 1L;
    	
    	/**
    	 * DISABLE：2.禁用
    	 */
    	Long DISABLE = 2L;
    }
    
    /**
     * <一句话功能简述> 常用菜单
     * <功能详细描述>
     * <AUTHOR>
     * @version V1.0[产品/模块版本]
     */
    public interface CommMenus {
    	/**
    	 * WORK：20141017093154666011.日常工作管理
    	 */
    	String WORK = "20141017093154666011";
    	
    	/**
    	 * ACCOUNT：20141010153245331025.账户信息管理
    	 */
    	String ACCOUNT = "20141010153245331025";
    	
    	/**
    	 * APPLY： 20141010153540294035.填写申请资料
    	 */
    	String APPLY = "20141010153540294035";
    	
    	/**
    	 * INFO：20141020082748012025.基本信息维护
    	 */
    	String INFO = "20141020082748012025";
    }
}
