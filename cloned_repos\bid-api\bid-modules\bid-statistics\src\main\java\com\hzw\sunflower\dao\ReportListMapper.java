package com.hzw.sunflower.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hzw.sunflower.controller.response.ReportListVO;
import com.hzw.sunflower.entity.ArchiveRecallList;
import com.hzw.sunflower.entity.ReportList;
import com.hzw.sunflower.entity.condition.ArchiveRecallCondition;
import com.hzw.sunflower.entity.condition.ReportListCondition;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ReportListMapper extends BaseMapper<ReportList> {

    IPage<ReportList> findInfoByCondition(IPage<ReportList> page, @Param("condition")ReportListCondition condition);

    List<String> findConferenceAddress(@Param("condition")ReportListCondition condition, @Param("purchaseNumber")String purchaseNumber,@Param("packageNum")String packageNum);

    List<Long> findDeptId(Long userId);

    IPage<ReportList> findAllInfo(@Param("page") IPage<ReportList> page,@Param("condition") ReportListCondition condition);

    IPage<ReportList> findInfoByDept(@Param("page") IPage<ReportList> page,@Param("condition") ReportListCondition condition,@Param("deptId") String deptId);

    List<ReportListVO> findAllInfoList(@Param("condition") ReportListCondition condition);

    List<ReportListVO> findInfoListByDept(@Param("condition") ReportListCondition condition,@Param("deptId") String deptId);

    IPage<ArchiveRecallList> archiveRecallList(@Param("page") IPage<ArchiveRecallList> page,@Param("condition") ArchiveRecallCondition condition,@Param("str") String str);

    List<ArchiveRecallList> exportArchiveRecallList(@Param("condition") ArchiveRecallCondition condition,@Param("str") String str);
}
