class TenderBulletinModel {
  // 公告名称
  String bulletinName;
  // 公告编号
  String bulletinNo;

  // 地区名称
  String regionName;

  // 公告类型
  String bulletinType;

  // 招标条件
  String bulletinCondition;

  // 项目批准但尚未
  String approveUnit;

  // 项目批准文件编号
  String approveFileNo;

  // 项目名称
  String projectName;

  // 标段名称
  String bidSectionName;

  // 项目地点
  String projectAddress;

  // 招标数量
  String bidSectionCount;

  // 招标内容和规格
  String bidContent;

  // 交货时间
  String deliverTime;

  // 资金来源
  String fundSource;

  // 合同估算价
  String contractPrice;

  // 资质要求
  String qualifyRequirement;

  // 是否接受联合体投标
  bool isUnion;

  // 招标文件获取
  String bidFileRequriement;

  // 投标文件递交
  String bidDocRefer;

  // 开标时间
  String bidOpenTime;

  // 开标地点
  String bidOpenAddress;

  // 其他事项
  String other;

  // 监督部门
  String supervisor;

  // 发布媒介
  String media;

  // 招标人名称
  String bidderName;

  // 招标人地址
  String bidderAddress;

  // 招标人邮编
  String bidderPostNo;

  // 招标人联系人
  String bidderContract;
  
  // 招标人电话
  String bidderTelNo;

  // 招标人传真
  String bidderFaxNo;

  // 招标人邮件
  String bidderEmail;

  // 招标人网址
  String bidderUrl;

  // 招标人开户银行
  String bidderBank;

  // 招标人开户银行账号
  String bidderBankNo;


  // 招标代理名称
  String agencyName;

  // 招标人地址
  String agencyAddress;

  // 招标邮编
  String agencyPostNo;

  // 招标联系人
  String agencyContract;
  
  // 招标电话
  String agencyTelNo;

  // 招标传真
  String agencyFaxNo;

  // 招标邮件
  String agencyEmail;

  // 招标网址
  String agencyUrl;

  // 招标开户银行
  String agencyBank;

  // 招标人开户银行账号
  String agencyBankNo;
}