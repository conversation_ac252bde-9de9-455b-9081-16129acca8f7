package com.hzw.sunflower.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hzw.sunflower.controller.request.UserStatisticsReq;
import com.hzw.sunflower.controller.response.DepartmentStatisticsVO;
import com.hzw.sunflower.controller.response.UserStatisticsVO;
import com.hzw.sunflower.entity.UserStatistics;

import java.util.List;

public interface UserStatisticsService extends IService<UserStatistics> {
    /**
     *已开标项目数
     * @return
     */
    List<UserStatistics> queryBidOpen();

    /**
     *未开标项目数
     * @return
     */
    List<UserStatistics> queryBidNotOpen();

    /**
     * 已开标项目委托金额
     * @return
     */
    List<UserStatistics> queryBidOpenMoney();

    /**
     * 未开标项目委托金额
     * @return
     */
    List<UserStatistics> queryBidNotOpenMoney();

    /**
     * 已开标未收费中标金额预估
     * @return
     */
    List<UserStatistics> queryBidNotOpenWinMoney();

    /**
     * 中标金额
     * @return
     */
    List<UserStatistics> queryBidOpenWinMoney();

    /**
     * 已收费
     * @return
     */
    List<UserStatistics> queryBidOpenAgencyMoney();

    /**
     * 已开标未收费金额
     * @return
     */
    List<UserStatistics> queryBidNotOpenAgencyMoney();

    /**
     * 新增已开标项目数
     * @return
     */
    void insertQueryBidOpen();

    /**
     * 新增未开标项目数
     * @return
     */
    void insertQueryBidNotOpen();

    /**
     * 新增已开标项目委托金额
     * @return
     */
    void insertQueryBidOpenMoney();

    /**
     * 新增未开标项目委托金额
     * @return
     */
    void insertQueryBidNotOpenMoney();

    /**
     * 新增已开标未收费中标金额预估
     * @return
     */
    void insertQueryBidNotOpenWinMoney();

    /**
     * 新增中标金额
     * @return
     */
    void insertQueryBidOpenWinMoney();

    /**
     * 新增已收费
     * @return
     */
    void insertQueryBidOpenAgencyMoney();

    /**
     * 新增已开标未收费
     * @return
     */
    void insertQueryBidNotOpenAgencyMoney();

    /**
     * 修改已开标项目数
     * @return
     */
    void updateQueryBidOpen();

    /**
     * 修改未开标项目数
     * @return
     */
    void updateQueryBidNotOpen();

    /**
     * 修改已开标项目委托金额
     * @return
     */
    void updateQueryBidOpenMoney();

    /**
     * 修改未开标项目委托金额
     * @return
     */
    void updateQueryBidNotOpenMoney();

    /**
     * 修改已开标未收费中标金额预估
     * @return
     */
    void updateQueryBidNotOpenWinMoney();

    /**
     * 修改中标金额
     * @return
     */
    void updateQueryBidOpenWinMoney();

    /**
     * 修改已开标项目委托金额
     * @return
     */
    void updateQueryBidOpenAgencyMoney();

    /**
     * 修改已收费
     * @return
     */
    void updateQueryBidNotOpenAgencyMoney();

    List<UserStatistics> selectUserStatisticsByMonthAndCondition(UserStatisticsReq userStatisticsReq);

    List<UserStatistics> selectDepartmentStatisticsByMonthAndCondition(UserStatisticsReq userStatisticsReq);

    List<UserStatistics> selectCompanyStatisticsByMonthAndCondition(UserStatisticsReq userStatisticsReq);

    List<UserStatistics> selectUserStatisticsByMonthAndConditionAgo(UserStatisticsReq userStatisticsReq);

    List<UserStatistics> selectDepartmentStatisticsByMonthAndConditionAgo(UserStatisticsReq userStatisticsReq);

    List<UserStatistics> selectCompanyStatisticsByMonthAndConditionAgo(UserStatisticsReq userStatisticsReq);

    /**
     * 按项目经理查询
     * @return
     */
    List<UserStatisticsVO>  selectUserStatistics(UserStatisticsReq userStatisticsReq);

    /**
     * 按处室查询
     * @return
     */
    List<UserStatisticsVO> selectDepartmentStatistics(UserStatisticsReq userStatisticsReq);

    /**
     * 按公司查询
     * @return
     */
    List<UserStatisticsVO> selectCompanyStatistics(UserStatisticsReq userStatisticsReq);

    /**
     * 按处室包含同比查询
     * @return
     */
    List<DepartmentStatisticsVO> selectDepartmentStatisticsBasis(UserStatisticsReq userStatisticsReq);


    void deleteByYearMonth(String format);
}
