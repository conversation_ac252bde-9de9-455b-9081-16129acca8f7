<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="com.hzw.sunflower.dao.ProjectRelevantMapper">
    <update id="updateSectionTime" parameterType="java.util.List">
        <foreach collection="list" item="bean" index="index" open="" close="" separator=";">
            UPDATE t_project_bid_section
            <set>
                <if test="bean.saleEndState != 10">
                    sale_end_time_type=#{bean.saleEndState},
                    sale_end_time=#{bean.saleEndTime},
                </if>
                submit_end_time_type=#{bean.submitEndState},
                submit_end_time=#{bean.submitEndTime}
            </set>
            <where>
                id = #{bean.sectionId}
            </where>
        </foreach>
    </update>

    <update id="updateSectionRecordTime" parameterType="java.util.List">
        <foreach collection="list" item="bean" index="index" open="" close="" separator=";">
            UPDATE t_project_bid_section_record
            <set>
                <if test="bean.saleEndState != 10">
                    sale_end_time_type=#{bean.saleEndState},
                    sale_end_time=#{bean.saleEndTime},
                </if>
                submit_end_time_type=#{bean.submitEndState},
                submit_end_time=#{bean.submitEndTime}
            </set>
            <where>
                section_id = #{bean.sectionId}
                AND bid_round = #{bidRound}
            </where>
        </foreach>
    </update>

    <!--根据项目id查询项目-->
    <select id="getProjectById" resultType="com.hzw.sunflower.dto.ProjectInfoDTO">
        SELECT
            id,
            master_id,
            project_name,
            project_number,
            purchase_number,
            purchase_name,
            STATUS,
            refuse_reason,
            suspension_time,
            suspender,
            commission_status,
            denial_reason,
            public_platform_status,
            parent_id,
            entrusted_amount,
            entrusted_currency,
            upload_file_id,
            address_province,
            address_city,
            principal_user_id,
            principal_company,
            principal_charge_name,
            principal_charge_phone,
            agent_company,
            agent_charge_name,
            agent_charge_phone,
            package_segment_status,
            project_status,
            created_user_id,
            created_time,
            updated_user_id,
            is_delete,
            updated_time,
            remark,
            version,
            project_user_id,
            operation_flow,
            is_process,
            process_address,
            process_code,
            project_approval_file_name,
            project_approval_number,
            project_approval_unit,
            pre_qualification_mtd
        FROM
            t_project
        where id= #{projectId} and is_delete=0
    </select>
    <!--根据标段id查询标段信息-->
    <select id="getSectionById" resultType="com.hzw.sunflower.dto.SectionInfoDTO">
        SELECT
            id,
            project_id,
            package_number,
            package_name,
            entrust_money,
            file_request_end_time,
            file_response_end_time,
            status,
            entrust_currency,
            entrust_type,
            bid_address_province,
            bid_address_city,
            bid_first_level,
            bid_second_level,
            bid_third_level,
            bid_type_name,
            purchase_mode,
            purchase_mode_name,
            purchase_status,
            bond,
            bond_type,
            bond_percent,
            agency_fee,
            agency_fee_obj,
            agency_fee_type,
            tender_fee,
            tender_fee_type,
            deposit_type,
            deposit_amount,
            release_file_type,
            review_file_type,
            material_list,
            created_user_id,
            created_time,
            updated_user_id,
            updated_time,
            is_delete,
            remark,
            version,
            engineering_class,
            purchase_type,
            bid_round,
            sale_end_time_type,
            sale_end_time,
            submit_end_time_type,
            submit_end_time,
            agency_fee_value,
            agency_fee_option,
            pre_qualification_mtd,
            agency_cost_free,
            agency_fee_other_describe,
            can_search
        FROM
            t_project_bid_section
        where id= #{sectionId} and is_delete=0
    </select>
    <select id="getProjectEntrustById" resultType="com.hzw.sunflower.dto.ProjectEntrustInfoDTO">
        SELECT
            distinct
            tp.type,
            tp.user_id,
            tp.company_id,
            tp.project_id,
            tu.user_name,
            tc.company_name
        FROM
            t_project_entrust_user tp
            LEFT JOIN r_user_department  rud ON tp.user_id = rud.user_id and tp.department_id = rud.department_id
            left join t_user tu on tp.user_id =tu.id
            LEFT JOIN t_company tc on tp.company_id =tc.id
            where tp.project_id= #{projectId}  and  tp.type =0 and tp.is_delete=0
    </select>
    <select id="getProjectBidDocRelationById" resultType="com.hzw.sunflower.dto.ProjectBidDocRelationInfoDto">
        SELECT
            id,
            doc_id,
            project_id,
            section_id,
            submit_end_time,
            open_bid_address
        FROM
            t_project_bid_doc_relation
            where project_id= #{projectId} and section_id = #{sectionId} and is_delete=0
    </select>
    <select id="getProjectIdBySection" resultType="com.hzw.sunflower.dto.SectionInfoDTO">
        SELECT
        s.id,
        s.project_id,
        s.package_number,
        s.package_name,
        s.entrust_money,
        s.file_request_end_time,
        s.file_response_end_time,
        s.STATUS,
        s.entrust_currency,
        s.entrust_type,
        s.bid_address_province,
        s.bid_address_city,
        s.bid_first_level,
        s.bid_second_level,
        s.bid_third_level,
        s.bid_type_name,
        s.purchase_mode,
        s.purchase_mode_name,
        s.purchase_status,
        s.bond,
        s.bond_type,
        s.bond_percent,
        s.agency_fee,
        s.agency_fee_obj,
        s.agency_fee_type,
        s.tender_fee,
        s.tender_fee_type,
        s.deposit_type,
        s.deposit_amount,
        s.release_file_type,
        s.review_file_type,
        s.material_list,
        s.created_user_id,
        s.created_time,
        s.updated_user_id,
        s.updated_time,
        s.is_delete,
        s.remark,
        s.version,
        s.engineering_class,
        s.purchase_type,
        s.bid_round,
        s.sale_end_time_type,
        s.sale_end_time,
        s.submit_end_time_type,
        s.submit_end_time,
        s.pre_qualification_mtd,
        IFNULL(q.clarify_on_line,2) clarifyOnLine,
        q.clarify_end_time_type,
        q.clarify_end_time,
        IFNULL(q.question_on_line,2) questionOnLine,
        q.question_end_time_type,
        q.question_end_time,
        s.can_search
        FROM
        t_project_bid_section s
        LEFT JOIN t_project_section_clarify_question q ON s.id = q.section_id
        AND q.is_delete = 0
        WHERE
        s.project_id= #{projectId} and s.is_delete=0 and s.abnormal_status =0
        <if test="userIdentity == 3">
        <if test="clarifyType != null and clarifyType == 1">
        and q.clarify_on_line = 1
        and q.clarify_end_time > now()
        </if>
        <if test="clarifyType != null and clarifyType == 2">
        and q.question_on_line = 1
        and q.question_end_time > now()
        </if>
        </if>
        <if test="bidRound != null and bidRound != ''">
            and s.bid_round =#{bidRound}
        </if>
        and s.status >= 20 and s.status <![CDATA[ < ]]>  50
    </select>
    <select id="getSectionsInfo" resultType="com.hzw.sunflower.dto.SectionInfoDTO">
        SELECT
        id,
        project_id,
        package_number,
        package_name,
        entrust_money,
        file_request_end_time,
        file_response_end_time,
        status,
        entrust_currency,
        entrust_type,
        bid_address_province,
        bid_address_city,
        bid_first_level,
        bid_second_level,
        bid_third_level,
        bid_type_name,
        purchase_mode,
        purchase_mode_name,
        purchase_status,
        bond,
        bond_type,
        bond_percent,
        agency_fee,
        agency_fee_obj,
        agency_fee_type,
        tender_fee,
        tender_fee_type,
        deposit_type,
        deposit_amount,
        release_file_type,
        review_file_type,
        material_list,
        created_user_id,
        created_time,
        updated_user_id,
        updated_time,
        is_delete,
        remark,
        version,
        engineering_class,
        purchase_type,
        bid_round,
        sale_end_time_type,
        sale_end_time,
        submit_end_time_type,
        submit_end_time
        FROM
        t_project_bid_section pbs
        where id in
        <foreach item="item" collection="list" separator="," open="(" close=")" index="">
            #{item}
        </foreach>
        and is_delete=0
    </select>
    <select id="selectprojectNumber" resultType="com.hzw.sunflower.entity.Project">
        SELECT
            p.*
        FROM
            t_project p
            LEFT JOIN t_project_bid_section s ON s.project_id = p.id
            LEFT JOIN t_project_section_clarify_question q ON q.section_id = s.id
            AND q.is_delete = 0
        WHERE
            purchase_number = #{projectNumber}
            AND p.is_delete = 0
            AND s.is_delete = 0
            AND q.question_on_line != 2
        ORDER BY
            created_time DESC

    </select>
    <select id="getNoticeByPurchaseNumber" resultType="com.hzw.sunflower.dto.SectionInfoDTO">
        SELECT
        pbs.section_id AS id,
        pbs.project_id,
        pbs.package_number,
        pbs.package_name,
        pbs.entrust_money,
        pbs.file_request_end_time,
        pbs.file_response_end_time,
        pbs.STATUS,
        pbs.entrust_currency,
        pbs.entrust_type,
        pbs.bid_address_province,
        pbs.bid_address_city,
        pbs.bid_first_level,
        pbs.bid_second_level,
        pbs.bid_third_level,
        pbs.bid_type_name,
        pbs.purchase_mode,
        pbs.purchase_mode_name,
        pbs.purchase_status,
        pbs.bond,
        pbs.bond_type,
        pbs.bond_percent,
        pbs.agency_fee,
        pbs.agency_fee_obj,
        pbs.agency_fee_type,
        pbs.tender_fee,
        pbs.tender_fee_type,
        pbs.deposit_type,
        pbs.deposit_amount,
        pbs.release_file_type,
        pbs.review_file_type,
        pbs.material_list,
        pbs.created_user_id,
        pbs.created_time,
        pbs.updated_user_id,
        pbs.updated_time,
        pbs.is_delete,
        pbs.remark,
        pbs.version,
        pbs.engineering_class,
        pbs.purchase_type,
        pbs.bid_round,
        pbs.sale_end_time_type,
        pbs.sale_end_time,
        pbs.submit_end_time_type,
        pbs.submit_end_time
        FROM
        t_project a
        LEFT JOIN t_project_bid_section_record pbs ON a.id = pbs.project_id
        LEFT JOIN t_project_bid_section pbs1 ON pbs.section_id = pbs1.former_section_id
        LEFT JOIN t_apply_info apply ON apply.SUB_ID = pbs.section_id
        AND pbs.bid_round = apply.bid_round
        LEFT JOIN t_project_section_clarify_question q ON pbs.section_id = q.section_id
        AND q.is_delete = 0
        WHERE
        a.purchase_number = #{purchaseNumber}
        AND a.is_delete = 0
        AND pbs.is_delete = 0
        AND pbs1.base_section_id IS NULL
        AND apply.company_id = #{userId}
        AND q.question_on_line = 1
        AND q.question_end_time > now()
        AND pbs.STATUS >= 20
        AND pbs.STATUS <![CDATA[ < ]]>  50
    </select>


    <select id="getProjectSectionInfoById" resultType="com.hzw.sunflower.dto.ProjectSectionDTO">
        SELECT
            p.id,
            s.pre_qualification_mtd,
            s.id AS sectionId,
            s.package_name,
            s.package_number,
            s.purchase_status,
            s.status,
            p.operation_flow
        FROM
            t_project p
             LEFT JOIN t_project_bid_section s
             ON p.id = s.project_id
             and s.bid_round = #{bidRound}
             and s.is_delete = 0
             and s.purchase_mode = #{purchaseMode}
             and s.purchase_status = #{purchaseStatus}
             and s.status not in (70,71,72)
        WHERE
            p.id = #{projectId} and p.is_delete = 0
    </select>


    <select id="getProjectUserInfo" resultType="com.hzw.sunflower.dto.ProjectInfoDTO">
        select  p.*,u.user_name,ui.user_phone,ud.department_id,d.department_name,peu.user_id
        from t_project p
        left join t_project_entrust_user peu
        on p.id = peu.project_id
        and peu.type = 1
        left join t_user_identity ui
        on ui.user_id = peu.user_id and ui.identity = 2
        left join t_user u
        on ui.user_id = u.id
        LEFT JOIN r_user_department  ud  on u.id = ud.user_id and ud.user_identity_id = 2 and peu.department_id = ud.department_id
        left join t_department d
        on ud.department_id = d.id
        where p.id = #{projectId}
        and p.IS_DELETE = 0 and peu.IS_DELETE = 0
        and ui.IS_DELETE = 0  and u.IS_DELETE = 0
        and ud.IS_DELETE = 0 and d.IS_DELETE = 0
        limit 1
    </select>

    <select id="getSectionsSecondRound" resultType="com.hzw.sunflower.dto.SectionInfoDTO" >
        SELECT
        id,
        project_id,
        package_number,
        package_name,
        entrust_money,
        file_request_end_time,
        file_response_end_time,
        status,
        entrust_currency,
        entrust_type,
        bid_address_province,
        bid_address_city,
        bid_first_level,
        bid_second_level,
        bid_third_level,
        bid_type_name,
        purchase_mode,
        purchase_mode_name,
        purchase_status,
        bond,
        bond_type,
        bond_percent,
        agency_fee,
        agency_fee_obj,
        agency_fee_type,
        tender_fee,
        tender_fee_type,
        deposit_type,
        deposit_amount,
        release_file_type,
        review_file_type,
        material_list,
        created_user_id,
        created_time,
        updated_user_id,
        updated_time,
        is_delete,
        remark,
        version,
        engineering_class,
        purchase_type,
        bid_round,
        sale_end_time_type,
        sale_end_time,
        submit_end_time_type,
        submit_end_time
        FROM
        t_project_bid_section pbs
        where id in
        <foreach item="item" collection="list" separator="," open="(" close=")" index="">
            #{item}
        </foreach>
        and is_delete=0
        and bid_round = 2
        and purchase_status = 1
    </select>
    <select id="getProjectUserInfoByType" resultType="com.hzw.sunflower.dto.ProjectInfoDTO">
        SELECT
            distinct
            p.*,
            u.user_name,
            ui.user_phone,
            ud.department_id,
            d.department_name,
            peu.user_id,
            c.company_name
        FROM
            t_project p
            LEFT JOIN t_project_entrust_user peu ON p.id = peu.project_id AND peu.type = #{entrustType} AND peu.IS_DELETE = 0
            LEFT JOIN t_user_identity ui ON ui.user_id = peu.user_id  AND ui.IS_DELETE = 0
            LEFT JOIN t_user u ON ui.user_id = u.id AND u.IS_DELETE = 0
            LEFT JOIN r_user_department  ud ON u.id = ud.user_id AND ud.user_identity_id = ui.identity  and peu.department_id = ud.department_id  AND ud.IS_DELETE = 0
            LEFT JOIN t_department d ON ud.department_id = d.id AND d.IS_DELETE = 0
            left join t_company c on c.id = ui.company_id and c.is_delete = 0
        WHERE
            p.id = #{projectId}
            AND p.IS_DELETE = 0
        LIMIT 1
    </select>
    <select id="selectDepartCodeByProjectId" resultType="java.lang.String">
        SELECT
            distinct
            d.`code`
        FROM
            t_project_entrust_user tpe
            LEFT JOIN t_department d ON tpe.department_id = d.id
        WHERE
            tpe.is_delete = 0
            AND d.is_delete = 0
            AND tpe.type = 1
            AND tpe.project_id = #{projectId}
    </select>

</mapper>
