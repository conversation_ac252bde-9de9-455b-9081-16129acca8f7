<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!-- 政策法规目录(T_POLICIES) -->
<mapper namespace="com.hzw.ssm.expert.dao.PoliciesMapper">
    <!-- This code was generated by TableGo tools, mark 1 begin. -->
    <!-- 字段映射 -->
    <resultMap id="policiesMap" type="com.hzw.ssm.expert.entity.Policies">
        <id column="ID" property="id" jdbcType="VARCHAR" />
        <result column="NAME" property="name" jdbcType="VARCHAR" />
        <result column="STATUS" property="status" jdbcType="INTEGER" />
        <result column="SORT" property="sort" jdbcType="INTEGER" />
        <result column="CREATE_USER" property="createUser" jdbcType="VARCHAR" />
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
        <result column="UPDATE_USER" property="updateUser" jdbcType="VARCHAR" />
        <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP" />
    </resultMap>
    <!-- This code was generated by TableGo tools, mark 1 end. -->

    <!-- This code was generated by TableGo tools, mark 2 begin. -->
    <!-- 表查询字段 -->
    <sql id="allColumns">
        p.ID, p.NAME, p.STATUS, p.SORT, p.CREATE_USER, p.CREATE_TIME, p.UPDATE_USER, p.UPDATE_TIME
    </sql>
    <!-- This code was generated by TableGo tools, mark 2 end. -->

    <!-- 查询所有政策法规目录 -->
    <select id="findPoliciesAll" resultMap="policiesMap">
        SELECT
            <include refid="allColumns" />
        FROM T_POLICIES p
        where
        status = 0
    </select>

    <select id="queryAllEffective" resultType="java.util.Map">
        SELECT
            id,
            name
        FROM
            T_POLICIES p
        where
            status = 0
        ORDER BY
            p.SORT DESC,
            UPDATE_TIME desc
    </select>

    <!-- 根据条件参数查询政策法规目录列表 -->
    <select id="queryPagePoliciesByCondition" resultMap="policiesMap" parameterType="com.hzw.ssm.expert.entity.Policies">
        SELECT
            <include refid="allColumns" />
        FROM T_POLICIES p WHERE 1 = 1
        <if test="status != null and status != ''">
            AND p.STATUS LIKE '%' || #{status} || '%'
        </if>
        <if test="sort != null">
            AND p.SORT = #{sort}
        </if>
        <if test="createUser != null and createUser != ''">
            AND p.CREATE_USER LIKE '%' || #{createUser} || '%'
        </if>
        <if test="createTime != null and createTime != ''">
            AND p.CREATE_TIME LIKE '%' || #{createTime} || '%'
        </if>
        <if test="updateUser != null and updateUser != ''">
            AND p.UPDATE_USER LIKE '%' || #{updateUser} || '%'
        </if>
        <if test="updateTime != null and updateTime != ''">
            AND p.UPDATE_TIME LIKE '%' || #{updateTime} || '%'
        </if>
     ${order}



    </select>


    <!-- 根据条件参数查询政策法规目录列表 -->
    <select id="queryPoliciesByCondition" resultMap="policiesMap" parameterType="com.hzw.ssm.expert.entity.Policies">
        SELECT
        <include refid="allColumns" />
        FROM T_POLICIES p WHERE 1 = 1
        <if test="status != null and status != ''">
            AND p.STATUS LIKE '%' || #{status} || '%'
        </if>

        <if test="name != null and name != ''">
            AND p.Name= #{name}
        </if>

        <if test="sort != null">
            AND p.SORT = #{sort}
        </if>
        <if test="createUser != null and createUser != ''">
            AND p.CREATE_USER LIKE '%' || #{createUser} || '%'
        </if>
        <if test="createTime != null and createTime != ''">
            AND p.CREATE_TIME LIKE '%' || #{createTime} || '%'
        </if>
        <if test="updateUser != null and updateUser != ''">
            AND p.UPDATE_USER LIKE '%' || #{updateUser} || '%'
        </if>
        <if test="updateTime != null and updateTime != ''">
            AND p.UPDATE_TIME LIKE '%' || #{updateTime} || '%'
        </if>

    </select>



    <!-- 根据条件参数查询政策法规目录列表 -->
    <select id="queryPoliciesByConditionOne" resultMap="policiesMap" parameterType="com.hzw.ssm.expert.entity.Policies">
        SELECT
        <include refid="allColumns" />
        FROM T_POLICIES p WHERE 1 = 1
        <if test="status != null and status != ''">
            AND p.STATUS LIKE '%' || #{status} || '%'
        </if>

        <if test="name != null and name != ''">
            AND p.Name LIKE '%' || #{name} || '%'
        </if>

        <if test="sort != null">
            AND p.SORT = #{sort}
        </if>
        <if test="createUser != null and createUser != ''">
            AND p.CREATE_USER LIKE '%' || #{createUser} || '%'
        </if>
        <if test="createTime != null and createTime != ''">
            AND p.CREATE_TIME LIKE '%' || #{createTime} || '%'
        </if>
        <if test="updateUser != null and updateUser != ''">
            AND p.UPDATE_USER LIKE '%' || #{updateUser} || '%'
        </if>
        <if test="updateTime != null and updateTime != ''">
            AND p.UPDATE_TIME LIKE '%' || #{updateTime} || '%'
        </if>

    </select>








    <!-- 根据主键查询政策法规目录信息 -->
    <select id="findPoliciesByIds" resultMap="policiesMap" parameterType="list">
        SELECT
            <include refid="allColumns" />
        FROM T_POLICIES p WHERE p.ID IN
        <foreach collection="list" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>


    <select id="findPoliciesById" resultMap="policiesMap" parameterType="string">
        SELECT
        <include refid="allColumns" />
        FROM T_POLICIES p WHERE p.ID=
          #{id}
    </select>



    <!-- 根据主键查询政策法规目录信息 -->
    <select id="findSortMax"  resultType="integer">
        SELECT
          max (p.SORT)
        FROM T_POLICIES p
    </select>

    <!-- 根据主键查询政策法规目录信息 -->
    <select id="findSortMin"  resultType="integer">
        SELECT
          min (p.SORT)
        FROM T_POLICIES p
    </select>


    <!-- 新增政策法规目录信息 -->
    <insert id="addPolicies" parameterType="com.hzw.ssm.expert.entity.Policies"  >
        INSERT INTO T_POLICIES (
            ID, STATUS, NAME,SORT, CREATE_USER, CREATE_TIME, UPDATE_USER, UPDATE_TIME
        ) VALUES (
            #{id,jdbcType=VARCHAR},
            #{status,jdbcType=VARCHAR},
            #{name,jdbcType=VARCHAR},
            #{sort,jdbcType=INTEGER},
            #{createUser,jdbcType=VARCHAR},
            #{createTime,jdbcType=VARCHAR},
            #{updateUser,jdbcType=VARCHAR},
            #{updateTime,jdbcType=VARCHAR}
        )
    </insert>

    <!-- 批量新增政策法规目录信息 -->
    <insert id="addPoliciess" parameterType="list" >
        INSERT INTO T_POLICIES (
            ID, STATUS, SORT, CREATE_USER, CREATE_TIME, UPDATE_USER, UPDATE_TIME
        )
        <foreach collection="list" index="index" item="item" separator="UNION ALL">
            SELECT
                #{item.id,jdbcType=VARCHAR},
                #{item.status,jdbcType=VARCHAR},
                #{item.sort,jdbcType=INTEGER},
                #{item.createUser,jdbcType=VARCHAR},
                #{item.createTime,jdbcType=VARCHAR},
                #{item.updateUser,jdbcType=VARCHAR},
                #{item.updateTime,jdbcType=VARCHAR}
            FROM DUAL
        </foreach>
    </insert>

    <!-- 修改政策法规目录信息 -->
    <update id="updatePolicies" >
        UPDATE T_POLICIES
        <set>
            <if test="status != null">
                STATUS = #{status,jdbcType=VARCHAR},
            </if>

            <if test="name != null">
                NAME = #{name,jdbcType=VARCHAR},
            </if>

            <if test="sort != null">
                SORT = #{sort,jdbcType=INTEGER},
            </if>
            <if test="createUser != null">
                CREATE_USER = #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                CREATE_TIME = #{createTime,jdbcType=VARCHAR},
            </if>
            <if test="updateUser != null">
                UPDATE_USER = #{updateUser,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                UPDATE_TIME = #{updateTime,jdbcType=VARCHAR}
            </if>
        </set>
        WHERE ID = #{id}
    </update>

    <!-- 批量修改政策法规目录信息 -->
    <update id="updatePoliciess" parameterType="list" >
        <foreach collection="list" index="index" item="item" open="BEGIN" close="END;">
            UPDATE T_POLICIES
            <set>
                <if test="item.status != null">
                    STATUS = #{item.status, jdbcType=VARCHAR},
                </if>
                <if test="item.sort != null">
                    SORT = #{item.sort, jdbcType=INTEGER},
                </if>
                <if test="item.createUser != null">
                    CREATE_USER = #{item.createUser, jdbcType=VARCHAR},
                </if>
                <if test="item.createTime != null">
                    CREATE_TIME = #{item.createTime, jdbcType=VARCHAR},
                </if>
                <if test="item.updateUser != null">
                    UPDATE_USER = #{item.updateUser, jdbcType=VARCHAR},
                </if>
                <if test="item.updateTime != null">
                    UPDATE_TIME = #{item.updateTime, jdbcType=VARCHAR}
                </if>
            </set>
            WHERE ID = #{item.id};
        </foreach>
    </update>

    <!-- 根据主键删除政策法规目录 -->
    <delete id="deletePoliciesById" parameterType="string" >
        DELETE FROM T_POLICIES WHERE ID = #{id}
    </delete>

    <!-- 根据主键批量删除政策法规目录 -->
    <delete id="deletePoliciesByIds" parameterType="list" >
        DELETE FROM T_POLICIES WHERE ID IN
        <foreach collection="list" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>