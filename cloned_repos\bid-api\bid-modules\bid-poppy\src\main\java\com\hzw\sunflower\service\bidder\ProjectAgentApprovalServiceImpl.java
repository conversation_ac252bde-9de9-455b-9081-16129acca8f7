package com.hzw.sunflower.service.bidder;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzw.sunflower.constant.CommonConstants;
import com.hzw.sunflower.constant.UserConstants;
import com.hzw.sunflower.constant.constantenum.EntrustUserType;
import com.hzw.sunflower.constant.constantenum.ProjectStatusEnum;
import com.hzw.sunflower.constant.constantenum.ProjectTendereeStatusEnum;
import com.hzw.sunflower.constant.constantenum.YesOrNoEnum;
import com.hzw.sunflower.controller.project.request.EntrustProjectReq;
import com.hzw.sunflower.controller.project.request.ProjectAcceptanceRep;
import com.hzw.sunflower.controller.project.request.bidder.BidderProjectReq;
import com.hzw.sunflower.controller.project.request.bidder.ProjectConditionRep;
import com.hzw.sunflower.controller.project.response.ProjectAcceptanceListVo;
import com.hzw.sunflower.dao.ProjectAgentApprovalMapper;
import com.hzw.sunflower.dao.ProjectMapper;
import com.hzw.sunflower.dto.*;
import com.hzw.sunflower.dto.bidder.BidderProjectCondition;
import com.hzw.sunflower.entity.*;
import com.hzw.sunflower.service.*;
import com.hzw.sunflower.util.BeanListUtil;
import lombok.val;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date ：Created in 2021/6/10 9:53
 * @description：招标人项目impl
 * @modified By：`
 * @version: 1.0
 */
@Service
public class ProjectAgentApprovalServiceImpl extends ServiceImpl<ProjectAgentApprovalMapper, ProjectAgentApproval> implements ProjectAgentApprovalService {



}
