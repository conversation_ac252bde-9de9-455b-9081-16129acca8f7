package com.hzw.sunflower.constant.constantenum;

public enum SealRelationEnum {

    //业务类型 1 异议/质疑  2 投诉  3 澄清/修改
    SEAL_RELATION_OBJECTION(1, "异议"),
    SEAL_RELATION_COMPLAINT(2, "投诉"),
    SEAL_RELATION_CLARIFY(3, "澄清/修改"),

    //用印申请状态 1.待审批 2.已通过 3.已退回 4.已盖章
    SEAL_STATUS_1(1, "待审批"),
    SEAL_STATUS_2(2, "已通过"),
    SEAL_STATUS_3(3, "已退回"),
    SEAL_STATUS_6(6, "已盖章"),
    SEAL_STATUS_7(7, "完善修改"),
    SEAL_STATUS_8(8, "已撤回"),
    SEAL_STATUS_9(9, "撤回待审批"),

    // 业务类型  1 回复审批  2 延长回复 3 上传不予受理通知书  4 材料补证 5 澄清/修改
    BUSINESS_TYPE_1(1, "回复审批"),
    BUSINESS_TYPE_2(2, "延长回复"),
    BUSINESS_TYPE_3(3, "上传不予受理通知书"),
    BUSINESS_TYPE_4(4, "材料补证"),
    BUSINESS_TYPE_5(5, "澄清/修改"),


    ;

    private Integer type;
    private String desc;

    SealRelationEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
