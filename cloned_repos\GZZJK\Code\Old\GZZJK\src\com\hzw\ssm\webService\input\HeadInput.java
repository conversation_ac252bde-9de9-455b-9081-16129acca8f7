package com.hzw.ssm.webService.input;

/**
 * 输入参数的基类
 * 
 * <AUTHOR>
 * 
 */
public class HeadInput {
	// 平台代码
	private String platformcode;
	// 平台密钥
	private String platformkey;
	// 时间戳
	private String timeStamp;

	public String getPlatformcode() {
		return platformcode;
	}

	public void setPlatformcode(String platformcode) {
		this.platformcode = platformcode;
	}

	public String getPlatformkey() {
		return platformkey;
	}

	public void setPlatformkey(String platformkey) {
		this.platformkey = platformkey;
	}

	public String getTimeStamp() {
		return timeStamp;
	}

	public void setTimeStamp(String timeStamp) {
		this.timeStamp = timeStamp;
	}

}
