package com.hzw.sunflower.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hzw.sunflower.entity.BondWaterlabel;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @ClassName:BondWaterlabelMapper
 * @Auther: lijinxin
 * @Description: 保证金流水标签
 * @Date: 2023/5/10 16:46
 * @Version: v1.0
 */
@Mapper
public interface BondWaterlabelMapper extends BaseMapper<BondWaterlabel> {


    /**
     * 保证金流水标签校验
     * 判断最新的标签是不是收款标签
     * 最新的状态不是收款返回0
     * @param waterId
     * @return
     */
    Integer getWaterlabelCheck(@Param("waterId") Long waterId);

    /**
     * 校验批量流水标签
     * 返回不是收款流水的数量
     * @param waterIds
     * @return
     */
    Integer chekecWaterLabel(@Param("waterIds") List<Long> waterIds);
}
