package com.hzw.sunflower.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelCollection;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@ApiModel("行业项目分布（附表）")
@Data
public class CollectInfo implements Serializable {

    @Excel(name = "项目编号",orderNum = "1",width=25)
    @ApiModelProperty("项目编号")
    private String purchaseNumber;

    @ApiModelProperty("包号")
    private String packageNumber;

    @Excel(name = "项目名称",orderNum = "2",width=25)
    @ApiModelProperty("项目名称")
    private String projectName;

    @Excel(name = "所属处室",orderNum = "3",width=25)
    @ApiModelProperty("所属处室")
    private String departmentName;

    @Excel(name = "项目负责人",orderNum = "4",width=25)
    @ApiModelProperty("项目负责人")
    private String manager;

    @Excel(name = "一级",groupName = "行业",orderNum = "5",width=25)
    @ApiModelProperty("行业")
    private String firstLevelName;

    @Excel(name = "二级",groupName = "行业",orderNum = "6",width=25)
    @ApiModelProperty("行业（小）")
    private String secondLevelName;

}
