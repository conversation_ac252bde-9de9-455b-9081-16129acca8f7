package com.hzw.system.api.domain.dto;


import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

@Data
public class SysUserDTO implements Serializable {
    /**
     * 验证码
     */
    @NotNull(message = "验证码不能为空")
    private String code;


    /**
     * 身份证正面
     */
    @NotNull(message = "身份证正面不能为空")
    private String idCardFront;

    /**
     * 身份证反面
     */
    @NotNull(message = "身份证反面不能为空")
    private String idCardReverse;

    /**
     * 推荐码
     */
    private String referralCode;

    private String recommendCode;


    /**
     * 专家状态
     */
    private Integer expertStatus;

    /**
     * 专家状态
     */
    private String headimage;

    /**
     * 专家姓名
     */
    @NotNull(message = "专家姓名不能为空")
    private String userName;

    /**
     * 身份证号
     */
    @NotNull(message = "身份证号不能为空")
    private String idNum;

    private Integer idType;


    /**
     * 手机号
     */
    @NotNull(message = "手机号不能为空")
    private String phonenumber;


    /**
     * 性别
     */
    @NotNull(message = "性别不能为空")
    private String sex;

    /**
     * openId
     */
    private String openId;
    /**
     * 专家id
     */
    private String expertId;
    /**
     * 用户id
     */
    private String userId;
    /**
     * 创建时间
     */
    private Date createdTime;
    /**
     * 用户状态
     */
    private Integer status;
}
