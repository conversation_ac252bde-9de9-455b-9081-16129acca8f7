package com.hzw.ssm.expert.dao;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.hzw.ssm.expert.entity.Appraise;
import com.hzw.ssm.expert.entity.AppraiseIllegal;
import com.hzw.ssm.expert.entity.AppraiseInfo;
import com.hzw.ssm.expert.entity.AppraiseInfo2;
import com.hzw.ssm.expert.entity.AppraiseRemark;
import com.hzw.ssm.expert.entity.ExpertInfoEntity;
import com.hzw.ssm.sys.project.entity.ProjectEntity;

/**
 * 专家评价
 * 
 * <AUTHOR>
 * 
 */
public interface AppraiseMapper {
	/**
	 * 查询专家反馈项目列表
	 * 
	 * @param entity
	 * @return
	 */
	public List<ProjectEntity> queryPageProjectInfo(ProjectEntity entity);

	/**
	 * 查询专家反馈专家列表
	 * 
	 * @param entity
	 * @return
	 */
	public List<ExpertInfoEntity> queryExpertAppriaiseList(ExpertInfoEntity entity);
	
	
	/**
	 * 查询违规专家列表
	 * 
	 * @param entity
	 * @return
	 */
	public List<ExpertInfoEntity> queryIllegalExpertsList(ExpertInfoEntity entity);
	
	/**
	 * 查询专家列表
	 * 
	 * @param entity
	 * @return
	 */
	public List<ExpertInfoEntity> queryIllegalExpertsList2(ExpertInfoEntity entity);
	
	/**
	 * 根据id查询评价信息表(基础信息)
	 * 
	 * @return
	 */
	public ExpertInfoEntity queryBaseAppraise(String extractResultId);

	/**
	 * 根据id查询专家评价信息表(评分项)
	 * 
	 * @param parentId
	 * @return
	 */
	public List<AppraiseInfo> queryAppraiseInfoById(String parentId);
	
	/**
	 * 根据id查询专家评价信息表(评分项)
	 * 
	 * @param parentId
	 * @return
	 */
	public List<AppraiseInfo2> queryAppraiseInfoById2(String parentId);

	/**
	 * 专家评价
	 * 
	 * @param list
	 */
	public void saveAppraise(List<Appraise> list);

	/**
	 * 专家评价备注
	 * 
	 * @param appraiseRemark
	 */
	public void saveAppraiseRemark(AppraiseRemark appraiseRemark);

	/**
	 * 修改专家抽取结果表是否评价状态
	 * 
	 * @param exRId
	 */
	public void modifyIsAppraise(ExpertInfoEntity expertInfoEntity);

	
	/**
	 * 修改专家抽取结果表是否奖励状态
	 * 
	 * @param exRId
	 */
	public void modifyIsAppraiseReward(String exRId);
	
	/**
	 * 查看专家已评价信息
	 * 
	 * @param extractResultId
	 * @return
	 */
	public List<Appraise> queryAppraiseDetail(String extractResultId);
	
	/**
	 * 查看专家已奖励信息
	 * 
	 * @param extractResultId
	 * @return
	 */
	public List<Appraise> queryAppraiseRewardDetail(String extractResultId);
	
	

	/**
	 * 查询评价备注信息
	 * 
	 * @param extractResultId
	 * @return
	 */
	public AppraiseRemark queryAppraiseRemark(String extractResultId);
	
	/**
	 * 查看专家评价平均分
	 * @param appraise
	 * @return
	 */
	public Appraise getExpertAvgScore(Appraise appraise);
	/**
	 * 查看专家评价分
	 * @param appraise
	 * @return
	 */
	public Appraise getExpertScore(String  expertId);
	/**
	 * 添加专家评价分
	 * @param appraise
	 * @return
	 */
	public void addExpertScore(Appraise appraise);
	/**
	 * 修改专家评价分
	 * @param appraise
	 * @return
	 */
	public void modifyExpertScore(Appraise appraise);
	/**
	 * 查看专家评价分
	 * @param appraise
	 * @return
	 */
	public Appraise getExpertScoreBAK(String  expertId);
	/**
	 * 添加专家评价分
	 * @param appraise
	 * @return
	 */
	public void addExpertScoreBAK(Appraise appraise);
	/**
	 * 修改专家评价分
	 * @param appraise
	 * @return
	 */
	public void modifyExpertScoreBAK(Appraise appraise);
	/**
	 * 修改专家停职月数
	 * @param appraise
	 * @return
	 */
	public void modifyExpertSuspension(@Param("id") String id,@Param("suspension") Integer suspension);
	/**
	 * 修改专家停职总月数
	 * @param appraise
	 * @return
	 */
	public void modifyExpertTotalSuspension(@Param("id") String id,@Param("totalSuspension") Integer totalSuspension);
	/**
	 * 修改专家停职月数
	 * @param appraise
	 * @return
	 */
	public void modifyExpertSuspensionBAK(@Param("id") String id,@Param("suspension") Integer suspension);
	/**
	 * 修改专家停职总月数
	 * @param appraise
	 * @return
	 */
	public void modifyExpertTotalSuspensionBAK(@Param("id") String id,@Param("totalSuspension") Integer totalSuspension);
	
	/**
	 * 查询专家违规信息
	 * 
	 * @param extractResultId
	 * @return
	 */
	public AppraiseIllegal queryAppraiseIllegal(String extractResultId);
	
	
	/**
	 * 专家违规情况录入
	 * 
	 * @param appraiseRemark
	 */
	public void saveAppraiseIllegal(AppraiseIllegal appraiseIllegal);
	
	
	/**
	 * 查询需要机电管理处违规处理的项目
	 * 
	 * @param entity
	 * @return
	 */
	public List<ProjectEntity> queryPageAppraiseIllegalInfo(ProjectEntity entity);

	
	/**
	 * 查询专家反馈专家列表by机电中心抽取人
	 * @param entity
	 * @return
	 */
	public List<ExpertInfoEntity> queryExpertAppriaiseListByJD(ExpertInfoEntity entity);
	
	/**
	 * 修改专家违规
	 * @param appraiseIllegal
	 * @return
	 */
	public void updateAppraiseIllegal(AppraiseIllegal appraiseIllegal);
	
	/**
	 * 查询专家反馈项目列表for机电中心抽取人
	 * 
	 * @param entity
	 * @return
	 */
	public List<ProjectEntity> queryPageProjectInfoByJD(ProjectEntity entity);
//	public String selectIsDuplicate(String extractResultId);
	public String selectDeducteScore(String extractResultId);

}
