package com.hzw.sunflower.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.hzw.sunflower.common.BaseBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @ClassName:IntegrationLog
 * @Auther: lijinxin
 * @Description: 系统集成日志
 * @Date: 2024/9/3 11:01
 * @Version: v1.0
 */

@Data
@TableName("t_integration_log")
@ApiModel(value = "t_integration_log", description = "系统集成日志表")
public class IntegrationLog extends BaseBean {

    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("业务类型 1实名认证  2企业认证")
    private Integer businessType;

    @ApiModelProperty("服务供应商")
    private String serviceCompany;

    @ApiModelProperty("请求时间")
    private Date requestTime;

    @ApiModelProperty("请求参数")
    private String requestData;

    @ApiModelProperty("请求时间")
    private Date responseTime;

    @ApiModelProperty("请求参数")
    private String responseData;

    @ApiModelProperty("请求状态码")
    private Integer responseStatus;

    @ApiModelProperty("是否成功 1 成功 2失败 3超时")
    private Integer isSuccess;

    @ApiModelProperty("请求地址")
    private String requestUrl;

}
