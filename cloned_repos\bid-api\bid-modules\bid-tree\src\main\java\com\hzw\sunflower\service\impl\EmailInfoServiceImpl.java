package com.hzw.sunflower.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.controller.request.EmailInfoAllReq;
import com.hzw.sunflower.controller.request.EmailInfoReq;
import com.hzw.sunflower.controller.response.EmailFilesVo;
import com.hzw.sunflower.controller.response.EmailInfoVo;
import com.hzw.sunflower.dao.EmailInfoMapper;
import com.hzw.sunflower.entity.*;
import com.hzw.sunflower.exception.ExceptionEnum;
import com.hzw.sunflower.security.utils.SecurityUtils;
import com.hzw.sunflower.service.EmailFileService;
import com.hzw.sunflower.service.EmailInfoService;
import com.hzw.sunflower.service.UserEmailConfigService;
import com.hzw.sunflower.util.FileUtils;
import com.hzw.sunflower.util.ReceQQmail;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * @ClassName:EmailInfoServiceImpl
 * @Auther: lijinxin
 * @Description: 邮件信息
 * @Date: 2023/3/2 17:14
 * @Version: v1.0
 */
@Service
public class EmailInfoServiceImpl extends ServiceImpl<EmailInfoMapper, EmailInfo> implements EmailInfoService {

    @Value("${files.template.path}")
    private String templatePath;

    @Autowired
    private UserEmailConfigService userEmailConfigService;

    @Autowired
    private EmailFileService emailFileService;

    @Autowired
    private ReceQQmail receQQmail;

    @Override
    public Result<Boolean> saveEmailList(EmailInfoReq emailInfo) throws Exception {
        Integer bidRound = emailInfo.getBidRound();
        Long bussinessId = emailInfo.getBussinessId();
        Integer bussinessType = emailInfo.getBussinessType();
        Long projectId = emailInfo.getProjectId();
        List<EmailInfo> emails = emailInfo.getInfoList();
        // 获取之前保存的邮件信息
        List<EmailInfoVo> emailInfoVos = duplicateRemoval(emails,bussinessId,bussinessType);
        // 获取用户邮件配置信息
        UserEmailAuthorization authorization = userEmailConfigService.queryUserEmailConfig(SecurityUtils.getJwtUser().getUserId());
        if(BeanUtil.isEmpty(authorization)){
            return Result.failed(ExceptionEnum.USER_EMAIL_AUTHORIZATION_NULL.getMessage());
        }
        boolean flag = false;
        // 先去删除业务已经关联的附件信息
        LambdaQueryWrapper<EmailInfo> lwqe = new LambdaQueryWrapper<>();
        lwqe.eq(EmailInfo::getBussinessId,bussinessId);
        lwqe.eq(EmailInfo::getBussinessType,bussinessType);
//        lwqe.eq(EmailInfo::getBidRound,bidRound);
//        lwqe.eq(EmailInfo::getProjectId,projectId);
        this.remove(lwqe);
        if(null != authorization && null != emails && emails.size() > 0){
            // 删除业务关联邮件信息
            MailRetrieval retrieval = new MailRetrieval();
            retrieval.setReceiveEmailAddress(authorization.getEmail());
            retrieval.setReceiveEmailPassword(authorization.getAuthorizationCode());
            // 循环添加邮件信息
            for (EmailInfo email : emails) {
                email.setBidRound(bidRound);
                email.setBussinessId(bussinessId);
                email.setBussinessType(bussinessType);
                email.setProjectId(projectId);
                retrieval.setMessgaeId(email.getMessgaeId());
                retrieval.setReceiveDate(email.getReceiveDate());
                EmailEntity mailInfo = receQQmail.getMailInfo(retrieval);
                if(null != mailInfo){
                    List<Long> files = mailInfo.getFiles();
                    // 邮件正文处理
                    email.setContent(mailInfo.getContent());
                    email.setContentOssId(mailInfo.getContentFile());
                    flag = this.save(email);
                    // 追加邮件附件信息
                    if(flag && null != files && files.size() > 0){
                        for (Long file : files) {
                            EmailFile ef = new EmailFile();
                            ef.setEmailId(email.getId());
                            ef.setOssId(file);
                            emailFileService.save(ef);
                        }
                    }
                }
            }
            //保存历史邮件
            saveOldEmail(emailInfoVos);

        }
        return Result.ok();
    }

    // 邮件去重
    private List<EmailInfoVo> duplicateRemoval( List<EmailInfo> emails,Long bussinessId,Integer bussinessType){
        List<EmailInfoVo> eivs = new ArrayList<>();
        for (EmailInfo email : emails) {
            String messgaeId = email.getMessgaeId();
            LambdaQueryWrapper<EmailInfo> lwqe = new LambdaQueryWrapper<>();
            lwqe.eq(EmailInfo::getBussinessId,bussinessId);
            lwqe.eq(EmailInfo::getBussinessType,bussinessType);
            lwqe.eq(EmailInfo::getMessgaeId,messgaeId);
            EmailInfo emailInfo = this.getBaseMapper().selectOne(lwqe);
            if(null != emailInfo){
                EmailInfoVo eiv = new EmailInfoVo();
                BeanUtils.copyProperties(emailInfo,eiv);
                LambdaQueryWrapper<EmailFile> lqwf = new LambdaQueryWrapper<>();
                lqwf.eq(EmailFile::getEmailId,email.getId());
                eiv.setFiles(emailFileService.getBaseMapper().selectList(lqwf));
                eivs.add(eiv);
                emails.remove(email);
            }
        }
       return eivs;
    }

    // 保存历史邮件信息
    private void saveOldEmail(List<EmailInfoVo> emailInfoVos){
        if(null != emailInfoVos && emailInfoVos.size() > 0 ){
            for (EmailInfoVo emailInfoVo : emailInfoVos) {
                EmailInfo ei = new EmailInfoVo();
                List<EmailFile> files = emailInfoVo.getFiles();
                BeanUtils.copyProperties(emailInfoVo,ei);
                ei.setId(null);
                this.save(ei);
                if(null != files && files.size() > 0 ){
                    for (EmailFile file : files) {
                        file.setId(null);
                        file.setEmailId(ei.getId());
                        emailFileService.save(file);
                    }
                }
            }
        }
    }


    @Override
    public List<EmailInfoVo> findEmailList(EmailInfoReq emailInfo) {
        List<EmailInfoVo> infos = new ArrayList<>();
        LambdaQueryWrapper<EmailInfo> lwqe = new LambdaQueryWrapper<>();
        lwqe.eq(EmailInfo::getBussinessId,emailInfo.getBussinessId());
        lwqe.eq(EmailInfo::getBussinessType,emailInfo.getBussinessType());
        lwqe.eq(EmailInfo::getBidRound,emailInfo.getBidRound());
        lwqe.eq(EmailInfo::getProjectId,emailInfo.getProjectId());
        List<EmailInfo> emailInfos = this.getBaseMapper().selectList(lwqe);
        if(null != emailInfos && emailInfos.size() > 0){
            for (EmailInfo info : emailInfos) {
                EmailInfoVo vo = new EmailInfoVo();
                BeanUtils.copyProperties(info,vo);
                LambdaQueryWrapper<EmailFile> lqwf = new LambdaQueryWrapper<>();
                lqwf.eq(EmailFile::getEmailId,info.getId());
                vo.setFiles(emailFileService.getBaseMapper().selectList(lqwf));
                infos.add(vo);
            }
        }
        return infos;
    }




    /**
     * 获取邮箱附件
     * @param type
     * @param id
     * @return
     */
    @Override
    public List<EmailFilesVo> getEmailFiles(Integer type, Long id) {
        return this.getBaseMapper().getEmailFiles(type,id);
    }


    /**
     * 获取邮箱正文附件
     * @param type
     * @param id
     * @return
     */
    @Override
    public List<EmailFilesVo> getEmailContentFiles(Integer type, Long id) {
        return this.getBaseMapper().getEmailContentFiles(type,id);
    }

    @Override
    public Result<List<EmailEntity>> getMailAllList(EmailInfoAllReq req) throws Exception {
        List<EmailEntity> mailAllList = new ArrayList<>();

        UserEmailAuthorization authorization = userEmailConfigService.queryUserEmailConfig(SecurityUtils.getJwtUser().getUserId());
        if(null != authorization){
            mailAllList = receQQmail.getMailAllList(authorization.getEmail(), authorization.getAuthorizationCode(), req.getKeyWords());
        }else{
            return Result.failed(ExceptionEnum.USER_EMAIL_AUTHORIZATION_NULL.getMessage());
        }
        if(null != req.getBussinessId() && null != req.getBussinessType()){
            LambdaQueryWrapper<EmailInfo> lwqe = new LambdaQueryWrapper<>();
            lwqe.eq(EmailInfo::getBussinessId,req.getBussinessId());
            lwqe.eq(EmailInfo::getBussinessType,req.getBussinessType());
            List<EmailInfo> emailInfos = this.getBaseMapper().selectList(lwqe);
            if(null != emailInfos && emailInfos.size() > 0){
                if(null != mailAllList){// 如果之前的邮箱不为空 那么根据messageid去判断是否需要将邮件加入其中
                    mailAllList = compareListHitData(mailAllList,emailInfos);
                }else{ // 如果当前的邮箱没雨邮件 那么就全部展示之前选中的邮箱
                    for (EmailInfo emailInfo : emailInfos) {
                        mailAllList.add(changeEmailinfo(emailInfo));
                    }
                }
            }
        }
        return Result.ok(mailAllList);
    }


    /**
     *  集合去重
     */
    public static List<EmailEntity> compareListHitData(List<EmailEntity> oneList, List<EmailInfo> twoList) {
        List<EmailEntity> nes = new ArrayList<>();
        for (EmailInfo emailInfo : twoList) {
            nes.add(changeEmailinfo(emailInfo));
        }
        oneList.addAll(nes);
        List<EmailEntity> ne = oneList.stream().filter(distinctByKey1(s -> s.getMessgaeId())).collect(Collectors.toList());
        return ne;
    }



    /**
     * 邮件对象实体转换
     * @param info
     * @return
     */
    private static EmailEntity changeEmailinfo(EmailInfo info){
        List<String> toName = new ArrayList<>();
        toName.add(info.getToNames());
        List<String> toAddress = new ArrayList<>();
        toAddress.add(info.getToAddress());
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        EmailEntity e = new EmailEntity();
        e.setContent(info.getContent());
        e.setMessgaeId(info.getMessgaeId());
        e.setSentDate(null !=info.getSentDate()?sdf.format(info.getSentDate()):"");
        e.setReceiveDate(null !=info.getReceiveDate()?sdf.format(info.getReceiveDate()):"");
        e.setSubject(info.getSubject());
        e.setToName(toName);
        e.setToAddress(toAddress);
        e.setFromName(info.getFromName());
        e.setFromAddress(info.getFromAddress());
        return  e;
    }


    static <T> Predicate<T> distinctByKey1(Function<? super T, ?> keyExtractor) {
        Map<Object, Boolean> seen = new ConcurrentHashMap<>();
        return t -> seen.putIfAbsent(keyExtractor.apply(t), Boolean.TRUE) == null;
    }



    /**
     * 加密工具下载
     * @param response
     */
    @Override
    public void downloadEmailTool(HttpServletResponse response) {
        String fileName = "邮箱绑定教程.pdf";
        String readPath = templatePath + "emailhandbook.pdf";
        try {
            FileUtils.outFile(readPath,"application/octet-stream",fileName,response);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

}
