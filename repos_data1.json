[{"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2022-08-19T10:49:53+08:00", "creator_id": 881075, "default_branch": "master", "description": "PublicServiceDashBoard", "http_url_to_repo": "https://codeup.aliyun.com/hzw/JSFGW/PublicServiceDashBoard.git", "id": 2557270, "issues_enabled": true, "last_activity_at": "2025-06-25T08:48:41+08:00", "merge_requests_enabled": true, "name": "PublicServiceDashBoard", "name_with_namespace": "hzw / JSFGW / PublicServiceDashBoard", "namespace": {"created_at": "2022-08-15T14:07:51+08:00", "description": "江苏省招标投标公共服务平台", "id": 861313, "name": "JSFGW", "owner_id": 881075, "path": "JSFGW", "public": false, "updated_at": "2022-08-15T14:07:51+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/112k6ff167e76a698c621bb236aec0bddbce/w/200/h/200", "id": 881075, "name": "qzw", "state": "active", "username": "aliyun:dingtalk_vzxhqg_Xbiwy", "web_url": ""}, "path": "PublicServiceDashBoard", "path_with_namespace": "hzw/JSFGW/PublicServiceDashBoard", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/JSFGW/PublicServiceDashBoard.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/JSFGW/PublicServiceDashBoard", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2022-08-19T10:46:56+08:00", "creator_id": 881075, "default_branch": "master", "description": "江苏省公共服务平台平台API", "http_url_to_repo": "https://codeup.aliyun.com/hzw/JSFGW/PlatformApi.git", "id": 2557257, "issues_enabled": true, "last_activity_at": "2025-07-03T13:51:48+08:00", "merge_requests_enabled": true, "name": "PlatformApi", "name_with_namespace": "hzw / JSFGW / PlatformApi", "namespace": {"created_at": "2022-08-15T14:07:51+08:00", "description": "江苏省招标投标公共服务平台", "id": 861313, "name": "JSFGW", "owner_id": 881075, "path": "JSFGW", "public": false, "updated_at": "2022-08-15T14:07:51+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/112k6ff167e76a698c621bb236aec0bddbce/w/200/h/200", "id": 881075, "name": "qzw", "state": "active", "username": "aliyun:dingtalk_vzxhqg_Xbiwy", "web_url": ""}, "path": "PlatformApi", "path_with_namespace": "hzw/JSFGW/PlatformApi", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/JSFGW/PlatformApi.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/JSFGW/PlatformApi", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2022-08-19T10:44:15+08:00", "creator_id": 881075, "default_branch": "master", "description": "MonitorUserApi", "http_url_to_repo": "https://codeup.aliyun.com/hzw/JSFGW/MonitorUserApi.git", "id": 2557235, "issues_enabled": true, "last_activity_at": "2022-08-19T10:46:17+08:00", "merge_requests_enabled": true, "name": "MonitorUserApi", "name_with_namespace": "hzw / JSFGW / MonitorUserApi", "namespace": {"created_at": "2022-08-15T14:07:51+08:00", "description": "江苏省招标投标公共服务平台", "id": 861313, "name": "JSFGW", "owner_id": 881075, "path": "JSFGW", "public": false, "updated_at": "2022-08-15T14:07:51+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/112k6ff167e76a698c621bb236aec0bddbce/w/200/h/200", "id": 881075, "name": "qzw", "state": "active", "username": "aliyun:dingtalk_vzxhqg_Xbiwy", "web_url": ""}, "path": "MonitorUserApi", "path_with_namespace": "hzw/JSFGW/MonitorUserApi", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/JSFGW/MonitorUserApi.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/JSFGW/MonitorUserApi", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2022-08-19T10:37:38+08:00", "creator_id": 881075, "default_branch": "master", "description": "监控平台前端界面", "http_url_to_repo": "https://codeup.aliyun.com/hzw/JSFGW/MonitorUI.git", "id": 2557200, "issues_enabled": true, "last_activity_at": "2022-08-19T10:38:14+08:00", "merge_requests_enabled": true, "name": "MonitorUI", "name_with_namespace": "hzw / JSFGW / MonitorUI", "namespace": {"created_at": "2022-08-15T14:07:51+08:00", "description": "江苏省招标投标公共服务平台", "id": 861313, "name": "JSFGW", "owner_id": 881075, "path": "JSFGW", "public": false, "updated_at": "2022-08-15T14:07:51+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/112k6ff167e76a698c621bb236aec0bddbce/w/200/h/200", "id": 881075, "name": "qzw", "state": "active", "username": "aliyun:dingtalk_vzxhqg_Xbiwy", "web_url": ""}, "path": "MonitorUI", "path_with_namespace": "hzw/JSFGW/MonitorUI", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/JSFGW/MonitorUI.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/JSFGW/MonitorUI", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2022-08-19T10:13:00+08:00", "creator_id": 881075, "default_branch": "master", "http_url_to_repo": "https://codeup.aliyun.com/hzw/JSFGW/MonitorBusinessApi.git", "id": 2557023, "issues_enabled": true, "last_activity_at": "2022-08-19T10:24:09+08:00", "merge_requests_enabled": true, "name": "MonitorBusinessApi", "name_with_namespace": "hzw / JSFGW / MonitorBusinessApi", "namespace": {"created_at": "2022-08-15T14:07:51+08:00", "description": "江苏省招标投标公共服务平台", "id": 861313, "name": "JSFGW", "owner_id": 881075, "path": "JSFGW", "public": false, "updated_at": "2022-08-15T14:07:51+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/112k6ff167e76a698c621bb236aec0bddbce/w/200/h/200", "id": 881075, "name": "qzw", "state": "active", "username": "aliyun:dingtalk_vzxhqg_Xbiwy", "web_url": ""}, "path": "MonitorBusinessApi", "path_with_namespace": "hzw/JSFGW/MonitorBusinessApi", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/JSFGW/MonitorBusinessApi.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/JSFGW/MonitorBusinessApi", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2022-08-19T10:07:51+08:00", "creator_id": 881075, "default_branch": "master", "description": "江苏招标投标公告服务平台移动端App", "http_url_to_repo": "https://codeup.aliyun.com/hzw/JSFGW/MobileApp.git", "id": 2556978, "issues_enabled": true, "last_activity_at": "2022-08-19T10:12:00+08:00", "merge_requests_enabled": true, "name": "MobileApp", "name_with_namespace": "hzw / JSFGW / MobileApp", "namespace": {"created_at": "2022-08-15T14:07:51+08:00", "description": "江苏省招标投标公共服务平台", "id": 861313, "name": "JSFGW", "owner_id": 881075, "path": "JSFGW", "public": false, "updated_at": "2022-08-15T14:07:51+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/112k6ff167e76a698c621bb236aec0bddbce/w/200/h/200", "id": 881075, "name": "qzw", "state": "active", "username": "aliyun:dingtalk_vzxhqg_Xbiwy", "web_url": ""}, "path": "MobileApp", "path_with_namespace": "hzw/JSFGW/MobileApp", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/JSFGW/MobileApp.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/JSFGW/MobileApp", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2022-08-19T09:59:28+08:00", "creator_id": 881075, "default_branch": "master", "description": "小程序和公众号内网页", "http_url_to_repo": "https://codeup.aliyun.com/hzw/JSFGW/MiniWebApp.git", "id": 2556929, "issues_enabled": true, "last_activity_at": "2022-08-19T10:00:00+08:00", "merge_requests_enabled": true, "name": "MiniWebApp", "name_with_namespace": "hzw / JSFGW / MiniWebApp", "namespace": {"created_at": "2022-08-15T14:07:51+08:00", "description": "江苏省招标投标公共服务平台", "id": 861313, "name": "JSFGW", "owner_id": 881075, "path": "JSFGW", "public": false, "updated_at": "2022-08-15T14:07:51+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/112k6ff167e76a698c621bb236aec0bddbce/w/200/h/200", "id": 881075, "name": "qzw", "state": "active", "username": "aliyun:dingtalk_vzxhqg_Xbiwy", "web_url": ""}, "path": "MiniWebApp", "path_with_namespace": "hzw/JSFGW/MiniWebApp", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/JSFGW/MiniWebApp.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/JSFGW/MiniWebApp", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2022-08-19T09:55:40+08:00", "creator_id": 881075, "default_branch": "master", "description": "MarketPlayersAPI", "http_url_to_repo": "https://codeup.aliyun.com/hzw/JSFGW/MarketPlayersAPI.git", "id": 2556891, "issues_enabled": true, "last_activity_at": "2025-07-04T13:53:15+08:00", "merge_requests_enabled": true, "name": "MarketPlayersAPI", "name_with_namespace": "hzw / JSFGW / MarketPlayersAPI", "namespace": {"created_at": "2022-08-15T14:07:51+08:00", "description": "江苏省招标投标公共服务平台", "id": 861313, "name": "JSFGW", "owner_id": 881075, "path": "JSFGW", "public": false, "updated_at": "2022-08-15T14:07:51+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/112k6ff167e76a698c621bb236aec0bddbce/w/200/h/200", "id": 881075, "name": "qzw", "state": "active", "username": "aliyun:dingtalk_vzxhqg_Xbiwy", "web_url": ""}, "path": "MarketPlayersAPI", "path_with_namespace": "hzw/JSFGW/MarketPlayersAPI", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/JSFGW/MarketPlayersAPI.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/JSFGW/MarketPlayersAPI", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2022-08-19T09:45:59+08:00", "creator_id": 881075, "default_branch": "master", "description": "Data Sync Api", "http_url_to_repo": "https://codeup.aliyun.com/hzw/JSFGW/DataSyncApi.git", "id": 2556783, "issues_enabled": true, "last_activity_at": "2025-08-21T18:09:15+08:00", "merge_requests_enabled": true, "name": "DataSyncApi", "name_with_namespace": "hzw / JSFGW / DataSyncApi", "namespace": {"created_at": "2022-08-15T14:07:51+08:00", "description": "江苏省招标投标公共服务平台", "id": 861313, "name": "JSFGW", "owner_id": 881075, "path": "JSFGW", "public": false, "updated_at": "2022-08-15T14:07:51+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/112k6ff167e76a698c621bb236aec0bddbce/w/200/h/200", "id": 881075, "name": "qzw", "state": "active", "username": "aliyun:dingtalk_vzxhqg_Xbiwy", "web_url": ""}, "path": "DataSyncApi", "path_with_namespace": "hzw/JSFGW/DataSyncApi", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/JSFGW/DataSyncApi.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/JSFGW/DataSyncApi", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2022-08-19T09:42:58+08:00", "creator_id": 881075, "default_branch": "master", "http_url_to_repo": "https://codeup.aliyun.com/hzw/JSFGW/ContentManageAPI.git", "id": 2556771, "issues_enabled": true, "last_activity_at": "2025-05-27T17:38:24+08:00", "merge_requests_enabled": true, "name": "ContentManageAPI", "name_with_namespace": "hzw / JSFGW / ContentManageAPI", "namespace": {"created_at": "2022-08-15T14:07:51+08:00", "description": "江苏省招标投标公共服务平台", "id": 861313, "name": "JSFGW", "owner_id": 881075, "path": "JSFGW", "public": false, "updated_at": "2022-08-15T14:07:51+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/112k6ff167e76a698c621bb236aec0bddbce/w/200/h/200", "id": 881075, "name": "qzw", "state": "active", "username": "aliyun:dingtalk_vzxhqg_Xbiwy", "web_url": ""}, "path": "ContentManageAPI", "path_with_namespace": "hzw/JSFGW/ContentManageAPI", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/JSFGW/ContentManageAPI.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/JSFGW/ContentManageAPI", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2022-08-19T09:23:44+08:00", "creator_id": 881075, "default_branch": "master", "description": "BulletinSearchApi", "http_url_to_repo": "https://codeup.aliyun.com/hzw/JSFGW/BulletinSearchApi.git", "id": 2556651, "issues_enabled": true, "last_activity_at": "2023-05-23T11:04:58+08:00", "merge_requests_enabled": true, "name": "BulletinSearchApi", "name_with_namespace": "hzw / JSFGW / BulletinSearchApi", "namespace": {"created_at": "2022-08-15T14:07:51+08:00", "description": "江苏省招标投标公共服务平台", "id": 861313, "name": "JSFGW", "owner_id": 881075, "path": "JSFGW", "public": false, "updated_at": "2022-08-15T14:07:51+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/112k6ff167e76a698c621bb236aec0bddbce/w/200/h/200", "id": 881075, "name": "qzw", "state": "active", "username": "aliyun:dingtalk_vzxhqg_Xbiwy", "web_url": ""}, "path": "BulletinSearchApi", "path_with_namespace": "hzw/JSFGW/BulletinSearchApi", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/JSFGW/BulletinSearchApi.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/JSFGW/BulletinSearchApi", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2022-08-19T08:48:41+08:00", "creator_id": 881075, "default_branch": "master", "description": "公告公示消息队列处理程序，将数据实时放入ElasticSearch", "http_url_to_repo": "https://codeup.aliyun.com/hzw/JSFGW/BulletinMessageBroker.git", "id": 2556521, "issues_enabled": true, "last_activity_at": "2022-08-19T09:21:49+08:00", "merge_requests_enabled": true, "name": "BulletinMessageBroker", "name_with_namespace": "hzw / JSFGW / BulletinMessageBroker", "namespace": {"created_at": "2022-08-15T14:07:51+08:00", "description": "江苏省招标投标公共服务平台", "id": 861313, "name": "JSFGW", "owner_id": 881075, "path": "JSFGW", "public": false, "updated_at": "2022-08-15T14:07:51+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/112k6ff167e76a698c621bb236aec0bddbce/w/200/h/200", "id": 881075, "name": "qzw", "state": "active", "username": "aliyun:dingtalk_vzxhqg_Xbiwy", "web_url": ""}, "path": "BulletinMessageBroker", "path_with_namespace": "hzw/JSFGW/BulletinMessageBroker", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/JSFGW/BulletinMessageBroker.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/JSFGW/BulletinMessageBroker", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2022-08-12T13:55:59+08:00", "creator_id": 880849, "default_branch": "master", "http_url_to_repo": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-ocr.git", "id": 2523673, "issues_enabled": true, "last_activity_at": "2024-04-02T09:04:55+08:00", "merge_requests_enabled": true, "name": "bid-ocr", "name_with_namespace": "hzw / JSTCCUP / bid-ocr", "namespace": {"created_at": "2022-08-02T16:29:33+08:00", "description": "JSTCC", "id": 833002, "name": "JSTCCUP", "owner_id": 880849, "path": "JSTCCUP", "public": false, "updated_at": "2022-08-02T16:29:33+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/11261002b8a7613983e6e3be3e41501742ba/w/100/h/100", "id": 880849, "name": "dingtalk_fefkma", "state": "active", "username": "aliyun:dingt<PERSON>_fefkma_9qp", "web_url": ""}, "path": "bid-ocr", "path_with_namespace": "hzw/JSTCCUP/bid-ocr", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/JSTCCUP/bid-ocr.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-ocr", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2022-08-12T13:53:11+08:00", "creator_id": 880849, "default_branch": "master", "description": "专家抽取", "http_url_to_repo": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-expert-api.git", "id": 2523661, "issues_enabled": true, "last_activity_at": "2023-07-19T14:45:07+08:00", "merge_requests_enabled": true, "name": "bid-expert-api", "name_with_namespace": "hzw / JSTCCUP / bid-expert-api", "namespace": {"created_at": "2022-08-02T16:29:33+08:00", "description": "JSTCC", "id": 833002, "name": "JSTCCUP", "owner_id": 880849, "path": "JSTCCUP", "public": false, "updated_at": "2022-08-02T16:29:33+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/11261002b8a7613983e6e3be3e41501742ba/w/100/h/100", "id": 880849, "name": "dingtalk_fefkma", "state": "active", "username": "aliyun:dingt<PERSON>_fefkma_9qp", "web_url": ""}, "path": "bid-expert-api", "path_with_namespace": "hzw/JSTCCUP/bid-expert-api", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/JSTCCUP/bid-expert-api.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-expert-api", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2022-08-12T13:52:17+08:00", "creator_id": 880849, "default_branch": "master", "http_url_to_repo": "https://codeup.aliyun.com/hzw/JSTCCUP/file-preview.git", "id": 2523656, "issues_enabled": true, "last_activity_at": "2025-05-26T16:22:50+08:00", "merge_requests_enabled": true, "name": "file-preview", "name_with_namespace": "hzw / JSTCCUP / file-preview", "namespace": {"created_at": "2022-08-02T16:29:33+08:00", "description": "JSTCC", "id": 833002, "name": "JSTCCUP", "owner_id": 880849, "path": "JSTCCUP", "public": false, "updated_at": "2022-08-02T16:29:33+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/11261002b8a7613983e6e3be3e41501742ba/w/100/h/100", "id": 880849, "name": "dingtalk_fefkma", "state": "active", "username": "aliyun:dingt<PERSON>_fefkma_9qp", "web_url": ""}, "path": "file-preview", "path_with_namespace": "hzw/JSTCCUP/file-preview", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/JSTCCUP/file-preview.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/JSTCCUP/file-preview", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2022-08-12T13:51:39+08:00", "creator_id": 880849, "default_branch": "master", "http_url_to_repo": "https://codeup.aliyun.com/hzw/JSTCCUP/sms.git", "id": 2523651, "issues_enabled": true, "last_activity_at": "2024-08-08T08:50:41+08:00", "merge_requests_enabled": true, "name": "sms", "name_with_namespace": "hzw / JSTCCUP / sms", "namespace": {"created_at": "2022-08-02T16:29:33+08:00", "description": "JSTCC", "id": 833002, "name": "JSTCCUP", "owner_id": 880849, "path": "JSTCCUP", "public": false, "updated_at": "2022-08-02T16:29:33+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/11261002b8a7613983e6e3be3e41501742ba/w/100/h/100", "id": 880849, "name": "dingtalk_fefkma", "state": "active", "username": "aliyun:dingt<PERSON>_fefkma_9qp", "web_url": ""}, "path": "sms", "path_with_namespace": "hzw/JSTCCUP/sms", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/JSTCCUP/sms.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/JSTCCUP/sms", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2022-08-12T13:50:51+08:00", "creator_id": 880849, "default_branch": "master", "http_url_to_repo": "https://codeup.aliyun.com/hzw/JSTCCUP/workflow.git", "id": 2523647, "issues_enabled": true, "last_activity_at": "2025-07-08T09:05:18+08:00", "merge_requests_enabled": true, "name": "workflow", "name_with_namespace": "hzw / JSTCCUP / workflow", "namespace": {"created_at": "2022-08-02T16:29:33+08:00", "description": "JSTCC", "id": 833002, "name": "JSTCCUP", "owner_id": 880849, "path": "JSTCCUP", "public": false, "updated_at": "2022-08-02T16:29:33+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/11261002b8a7613983e6e3be3e41501742ba/w/100/h/100", "id": 880849, "name": "dingtalk_fefkma", "state": "active", "username": "aliyun:dingt<PERSON>_fefkma_9qp", "web_url": ""}, "path": "workflow", "path_with_namespace": "hzw/JSTCCUP/workflow", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/JSTCCUP/workflow.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/JSTCCUP/workflow", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2022-08-12T13:49:48+08:00", "creator_id": 880849, "default_branch": "master", "http_url_to_repo": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-ui-tenderee.git", "id": 2523641, "issues_enabled": true, "last_activity_at": "2023-10-21T03:27:53+08:00", "merge_requests_enabled": true, "name": "bid-ui-tenderee", "name_with_namespace": "hzw / JSTCCUP / bid-ui-tenderee", "namespace": {"created_at": "2022-08-02T16:29:33+08:00", "description": "JSTCC", "id": 833002, "name": "JSTCCUP", "owner_id": 880849, "path": "JSTCCUP", "public": false, "updated_at": "2022-08-02T16:29:33+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/11261002b8a7613983e6e3be3e41501742ba/w/100/h/100", "id": 880849, "name": "dingtalk_fefkma", "state": "active", "username": "aliyun:dingt<PERSON>_fefkma_9qp", "web_url": ""}, "path": "bid-ui-tenderee", "path_with_namespace": "hzw/JSTCCUP/bid-ui-tenderee", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/JSTCCUP/bid-ui-tenderee.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-ui-tenderee", "wiki_enabled": true}, {"archived": false, "avatar_url": "", "builds_enabled": true, "checkemail": false, "created_at": "2022-08-12T13:48:55+08:00", "creator_id": 880849, "default_branch": "master", "description": "jstcc系统管理系统", "http_url_to_repo": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-ui-maintain.git", "id": 2523638, "issues_enabled": true, "last_activity_at": "2025-01-03T10:41:51+08:00", "merge_requests_enabled": true, "name": "bid-ui-maintain", "name_with_namespace": "hzw / JSTCCUP / bid-ui-maintain", "namespace": {"created_at": "2022-08-02T16:29:33+08:00", "description": "JSTCC", "id": 833002, "name": "JSTCCUP", "owner_id": 880849, "path": "JSTCCUP", "public": false, "updated_at": "2022-08-02T16:29:33+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/11261002b8a7613983e6e3be3e41501742ba/w/100/h/100", "id": 880849, "name": "dingtalk_fefkma", "state": "active", "username": "aliyun:dingt<PERSON>_fefkma_9qp", "web_url": ""}, "path": "bid-ui-maintain", "path_with_namespace": "hzw/JSTCCUP/bid-ui-maintain", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/JSTCCUP/bid-ui-maintain.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-ui-maintain", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2022-08-12T13:47:51+08:00", "creator_id": 880849, "default_branch": "master", "http_url_to_repo": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-ui-supplier.git", "id": 2523631, "issues_enabled": true, "last_activity_at": "2025-08-26T16:44:06+08:00", "merge_requests_enabled": true, "name": "bid-ui-supplier", "name_with_namespace": "hzw / JSTCCUP / bid-ui-supplier", "namespace": {"created_at": "2022-08-02T16:29:33+08:00", "description": "JSTCC", "id": 833002, "name": "JSTCCUP", "owner_id": 880849, "path": "JSTCCUP", "public": false, "updated_at": "2022-08-02T16:29:33+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/11261002b8a7613983e6e3be3e41501742ba/w/100/h/100", "id": 880849, "name": "dingtalk_fefkma", "state": "active", "username": "aliyun:dingt<PERSON>_fefkma_9qp", "web_url": ""}, "path": "bid-ui-supplier", "path_with_namespace": "hzw/JSTCCUP/bid-ui-supplier", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/JSTCCUP/bid-ui-supplier.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-ui-supplier", "wiki_enabled": true}, {"archived": false, "avatar_url": "", "builds_enabled": true, "checkemail": false, "created_at": "2022-08-12T13:37:49+08:00", "creator_id": 880849, "default_branch": "master", "description": "jstcc代理机构", "http_url_to_repo": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-ui-agency.git", "id": 2523590, "issues_enabled": true, "last_activity_at": "2025-08-28T13:39:09+08:00", "merge_requests_enabled": true, "name": "bid-ui-agency", "name_with_namespace": "hzw / JSTCCUP / bid-ui-agency", "namespace": {"created_at": "2022-08-02T16:29:33+08:00", "description": "JSTCC", "id": 833002, "name": "JSTCCUP", "owner_id": 880849, "path": "JSTCCUP", "public": false, "updated_at": "2022-08-02T16:29:33+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/11261002b8a7613983e6e3be3e41501742ba/w/100/h/100", "id": 880849, "name": "dingtalk_fefkma", "state": "active", "username": "aliyun:dingt<PERSON>_fefkma_9qp", "web_url": ""}, "path": "bid-ui-agency", "path_with_namespace": "hzw/JSTCCUP/bid-ui-agency", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/JSTCCUP/bid-ui-agency.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-ui-agency", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2022-08-11T09:17:18+08:00", "creator_id": 880849, "default_branch": "master", "http_url_to_repo": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-common-security.git", "id": 2516329, "issues_enabled": true, "last_activity_at": "2022-08-26T19:41:46+08:00", "merge_requests_enabled": true, "name": "bid-common-security", "name_with_namespace": "hzw / JSTCCUP / bid-common-security", "namespace": {"created_at": "2022-08-02T16:29:33+08:00", "description": "JSTCC", "id": 833002, "name": "JSTCCUP", "owner_id": 880849, "path": "JSTCCUP", "public": false, "updated_at": "2022-08-02T16:29:33+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/11261002b8a7613983e6e3be3e41501742ba/w/100/h/100", "id": 880849, "name": "dingtalk_fefkma", "state": "active", "username": "aliyun:dingt<PERSON>_fefkma_9qp", "web_url": ""}, "path": "bid-common-security", "path_with_namespace": "hzw/JSTCCUP/bid-common-security", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/JSTCCUP/bid-common-security.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-common-security", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2022-08-09T08:44:57+08:00", "creator_id": 880849, "default_branch": "master", "http_url_to_repo": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-openapi-client.git", "id": 2502532, "issues_enabled": true, "last_activity_at": "2025-08-22T09:52:04+08:00", "merge_requests_enabled": true, "name": "bid-openapi-client", "name_with_namespace": "hzw / JSTCCUP / bid-openapi-client", "namespace": {"created_at": "2022-08-02T16:29:33+08:00", "description": "JSTCC", "id": 833002, "name": "JSTCCUP", "owner_id": 880849, "path": "JSTCCUP", "public": false, "updated_at": "2022-08-02T16:29:33+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/11261002b8a7613983e6e3be3e41501742ba/w/100/h/100", "id": 880849, "name": "dingtalk_fefkma", "state": "active", "username": "aliyun:dingt<PERSON>_fefkma_9qp", "web_url": ""}, "path": "bid-openapi-client", "path_with_namespace": "hzw/JSTCCUP/bid-openapi-client", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/JSTCCUP/bid-openapi-client.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-openapi-client", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2022-08-03T10:02:20+08:00", "creator_id": 880849, "default_branch": "master", "description": "通用数据权限", "http_url_to_repo": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-common-datascope.git", "id": 2472102, "issues_enabled": true, "last_activity_at": "2025-07-08T11:30:57+08:00", "merge_requests_enabled": true, "name": "bid-common-datascope", "name_with_namespace": "hzw / JSTCCUP / bid-common-datascope", "namespace": {"created_at": "2022-08-02T16:29:33+08:00", "description": "JSTCC", "id": 833002, "name": "JSTCCUP", "owner_id": 880849, "path": "JSTCCUP", "public": false, "updated_at": "2022-08-02T16:29:33+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/11261002b8a7613983e6e3be3e41501742ba/w/100/h/100", "id": 880849, "name": "dingtalk_fefkma", "state": "active", "username": "aliyun:dingt<PERSON>_fefkma_9qp", "web_url": ""}, "path": "bid-common-datascope", "path_with_namespace": "hzw/JSTCCUP/bid-common-datascope", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/JSTCCUP/bid-common-datascope.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-common-datascope", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2022-08-03T10:01:13+08:00", "creator_id": 880849, "default_branch": "master", "description": "通用模块", "http_url_to_repo": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-common-core.git", "id": 2472091, "issues_enabled": true, "last_activity_at": "2025-07-02T17:04:13+08:00", "merge_requests_enabled": true, "name": "bid-common-core", "name_with_namespace": "hzw / JSTCCUP / bid-common-core", "namespace": {"created_at": "2022-08-02T16:29:33+08:00", "description": "JSTCC", "id": 833002, "name": "JSTCCUP", "owner_id": 880849, "path": "JSTCCUP", "public": false, "updated_at": "2022-08-02T16:29:33+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/11261002b8a7613983e6e3be3e41501742ba/w/100/h/100", "id": 880849, "name": "dingtalk_fefkma", "state": "active", "username": "aliyun:dingt<PERSON>_fefkma_9qp", "web_url": ""}, "path": "bid-common-core", "path_with_namespace": "hzw/JSTCCUP/bid-common-core", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/JSTCCUP/bid-common-core.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-common-core", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2022-08-03T09:49:32+08:00", "creator_id": 880849, "default_branch": "master", "description": "标书费客户端", "http_url_to_repo": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-cws-client.git", "id": 2471975, "issues_enabled": true, "last_activity_at": "2023-05-23T11:04:58+08:00", "merge_requests_enabled": true, "name": "bid-cws-client", "name_with_namespace": "hzw / JSTCCUP / bid-cws-client", "namespace": {"created_at": "2022-08-02T16:29:33+08:00", "description": "JSTCC", "id": 833002, "name": "JSTCCUP", "owner_id": 880849, "path": "JSTCCUP", "public": false, "updated_at": "2022-08-02T16:29:33+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/11261002b8a7613983e6e3be3e41501742ba/w/100/h/100", "id": 880849, "name": "dingtalk_fefkma", "state": "active", "username": "aliyun:dingt<PERSON>_fefkma_9qp", "web_url": ""}, "path": "bid-cws-client", "path_with_namespace": "hzw/JSTCCUP/bid-cws-client", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/JSTCCUP/bid-cws-client.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-cws-client", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2022-08-03T09:48:28+08:00", "creator_id": 880849, "default_branch": "master", "description": "专家费小程序启动模块", "http_url_to_repo": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-anemone-wechat.git", "id": 2471968, "issues_enabled": true, "last_activity_at": "2022-08-03T09:48:29+08:00", "merge_requests_enabled": true, "name": "bid-anemone-wechat", "name_with_namespace": "hzw / JSTCCUP / bid-anemone-wechat", "namespace": {"created_at": "2022-08-02T16:29:33+08:00", "description": "JSTCC", "id": 833002, "name": "JSTCCUP", "owner_id": 880849, "path": "JSTCCUP", "public": false, "updated_at": "2022-08-02T16:29:33+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/11261002b8a7613983e6e3be3e41501742ba/w/100/h/100", "id": 880849, "name": "dingtalk_fefkma", "state": "active", "username": "aliyun:dingt<PERSON>_fefkma_9qp", "web_url": ""}, "path": "bid-anemone-wechat", "path_with_namespace": "hzw/JSTCCUP/bid-anemone-wechat", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/JSTCCUP/bid-anemone-wechat.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-anemone-wechat", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2022-08-03T09:46:14+08:00", "creator_id": 880849, "default_branch": "master", "description": "JSTCC公函跳转接口", "http_url_to_repo": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-iris.git", "id": 2471956, "issues_enabled": true, "last_activity_at": "2022-08-03T09:46:14+08:00", "merge_requests_enabled": true, "name": "bid-iris", "name_with_namespace": "hzw / JSTCCUP / bid-iris", "namespace": {"created_at": "2022-08-02T16:29:33+08:00", "description": "JSTCC", "id": 833002, "name": "JSTCCUP", "owner_id": 880849, "path": "JSTCCUP", "public": false, "updated_at": "2022-08-02T16:29:33+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/11261002b8a7613983e6e3be3e41501742ba/w/100/h/100", "id": 880849, "name": "dingtalk_fefkma", "state": "active", "username": "aliyun:dingt<PERSON>_fefkma_9qp", "web_url": ""}, "path": "bid-iris", "path_with_namespace": "hzw/JSTCCUP/bid-iris", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/JSTCCUP/bid-iris.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-iris", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2022-08-03T09:45:01+08:00", "creator_id": 880849, "default_branch": "master", "description": "JSTCC定时任务", "http_url_to_repo": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-crocus.git", "id": 2471946, "issues_enabled": true, "last_activity_at": "2025-08-27T13:35:10+08:00", "merge_requests_enabled": true, "name": "bid-crocus", "name_with_namespace": "hzw / JSTCCUP / bid-crocus", "namespace": {"created_at": "2022-08-02T16:29:33+08:00", "description": "JSTCC", "id": 833002, "name": "JSTCCUP", "owner_id": 880849, "path": "JSTCCUP", "public": false, "updated_at": "2022-08-02T16:29:33+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/11261002b8a7613983e6e3be3e41501742ba/w/100/h/100", "id": 880849, "name": "dingtalk_fefkma", "state": "active", "username": "aliyun:dingt<PERSON>_fefkma_9qp", "web_url": ""}, "path": "bid-crocus", "path_with_namespace": "hzw/JSTCCUP/bid-crocus", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/JSTCCUP/bid-crocus.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-crocus", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2022-08-03T09:43:52+08:00", "creator_id": 880849, "default_branch": "master", "description": "专家抽取接口客户端", "http_url_to_repo": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-expert-client.git", "id": 2471940, "issues_enabled": true, "last_activity_at": "2023-07-19T14:44:48+08:00", "merge_requests_enabled": true, "name": "bid-expert-client", "name_with_namespace": "hzw / JSTCCUP / bid-expert-client", "namespace": {"created_at": "2022-08-02T16:29:33+08:00", "description": "JSTCC", "id": 833002, "name": "JSTCCUP", "owner_id": 880849, "path": "JSTCCUP", "public": false, "updated_at": "2022-08-02T16:29:33+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/11261002b8a7613983e6e3be3e41501742ba/w/100/h/100", "id": 880849, "name": "dingtalk_fefkma", "state": "active", "username": "aliyun:dingt<PERSON>_fefkma_9qp", "web_url": ""}, "path": "bid-expert-client", "path_with_namespace": "hzw/JSTCCUP/bid-expert-client", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/JSTCCUP/bid-expert-client.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-expert-client", "wiki_enabled": true}, {"archived": false, "avatar_url": "", "builds_enabled": true, "checkemail": false, "created_at": "2022-08-03T09:33:49+08:00", "creator_id": 880849, "default_branch": "develop", "description": "JSTCC接口服务", "http_url_to_repo": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-api.git", "id": 2471867, "issues_enabled": true, "last_activity_at": "2025-08-29T14:51:48+08:00", "merge_requests_enabled": true, "name": "bid-api", "name_with_namespace": "hzw / JSTCCUP / bid-api", "namespace": {"created_at": "2022-08-02T16:29:33+08:00", "description": "JSTCC", "id": 833002, "name": "JSTCCUP", "owner_id": 880849, "path": "JSTCCUP", "public": false, "updated_at": "2022-08-02T16:29:33+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/11261002b8a7613983e6e3be3e41501742ba/w/100/h/100", "id": 880849, "name": "dingtalk_fefkma", "state": "active", "username": "aliyun:dingt<PERSON>_fefkma_9qp", "web_url": ""}, "path": "bid-api", "path_with_namespace": "hzw/JSTCCUP/bid-api", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/JSTCCUP/bid-api.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-api", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2022-08-02T16:45:57+08:00", "creator_id": 880849, "default_branch": "master", "description": "流程引擎客户端", "http_url_to_repo": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-freesia.git", "id": 2467830, "issues_enabled": true, "last_activity_at": "2025-08-07T12:18:40+08:00", "merge_requests_enabled": true, "name": "bid-freesia", "name_with_namespace": "hzw / JSTCCUP / bid-freesia", "namespace": {"created_at": "2022-08-02T16:29:33+08:00", "description": "JSTCC", "id": 833002, "name": "JSTCCUP", "owner_id": 880849, "path": "JSTCCUP", "public": false, "updated_at": "2022-08-02T16:29:33+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/11261002b8a7613983e6e3be3e41501742ba/w/100/h/100", "id": 880849, "name": "dingtalk_fefkma", "state": "active", "username": "aliyun:dingt<PERSON>_fefkma_9qp", "web_url": ""}, "path": "bid-freesia", "path_with_namespace": "hzw/JSTCCUP/bid-freesia", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/JSTCCUP/bid-freesia.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-freesia", "wiki_enabled": true}, {"archived": false, "avatar_url": "", "builds_enabled": true, "checkemail": false, "created_at": "2022-08-02T15:32:34+08:00", "creator_id": 880832, "default_branch": "master", "http_url_to_repo": "https://codeup.aliyun.com/hzw/LicenseKey/clientDemo.git", "id": 2467108, "issues_enabled": true, "last_activity_at": "2022-08-02T15:34:50+08:00", "merge_requests_enabled": true, "name": "licenseDemo", "name_with_namespace": "hzw / LicenseKey / licenseDemo", "namespace": {"created_at": "2022-08-02T15:05:06+08:00", "description": "激活码项目", "id": 832725, "name": "LicenseKey", "owner_id": 880832, "path": "LicenseKey", "public": false, "updated_at": "2022-08-02T15:05:06+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/112k7732a99b3712805af594fd2c28ea877f/w/100/h/100", "id": 880832, "name": "秋竹康", "state": "active", "username": "aliyun:dingtalk_nussjm_llJnx", "web_url": ""}, "path": "clientDemo", "path_with_namespace": "hzw/LicenseKey/clientDemo", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/LicenseKey/clientDemo.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/LicenseKey/clientDemo", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2022-08-02T15:06:20+08:00", "creator_id": 880832, "default_branch": "master", "http_url_to_repo": "https://codeup.aliyun.com/hzw/LicenseKey/generatorGUI.git", "id": 2466799, "issues_enabled": true, "last_activity_at": "2022-08-02T15:20:11+08:00", "merge_requests_enabled": true, "name": "generatorGUI", "name_with_namespace": "hzw / LicenseKey / generatorGUI", "namespace": {"created_at": "2022-08-02T15:05:06+08:00", "description": "激活码项目", "id": 832725, "name": "LicenseKey", "owner_id": 880832, "path": "LicenseKey", "public": false, "updated_at": "2022-08-02T15:05:06+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/112k7732a99b3712805af594fd2c28ea877f/w/100/h/100", "id": 880832, "name": "秋竹康", "state": "active", "username": "aliyun:dingtalk_nussjm_llJnx", "web_url": ""}, "path": "generatorGUI", "path_with_namespace": "hzw/LicenseKey/generatorGUI", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/LicenseKey/generatorGUI.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/LicenseKey/generatorGUI", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2022-08-02T15:06:01+08:00", "creator_id": 880832, "default_branch": "master", "http_url_to_repo": "https://codeup.aliyun.com/hzw/LicenseKey/generatorAPI.git", "id": 2466796, "issues_enabled": true, "last_activity_at": "2022-08-02T15:17:37+08:00", "merge_requests_enabled": true, "name": "generatorAPI", "name_with_namespace": "hzw / LicenseKey / generatorAPI", "namespace": {"created_at": "2022-08-02T15:05:06+08:00", "description": "激活码项目", "id": 832725, "name": "LicenseKey", "owner_id": 880832, "path": "LicenseKey", "public": false, "updated_at": "2022-08-02T15:05:06+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/112k7732a99b3712805af594fd2c28ea877f/w/100/h/100", "id": 880832, "name": "秋竹康", "state": "active", "username": "aliyun:dingtalk_nussjm_llJnx", "web_url": ""}, "path": "generatorAPI", "path_with_namespace": "hzw/LicenseKey/generatorAPI", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/LicenseKey/generatorAPI.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/LicenseKey/generatorAPI", "wiki_enabled": true}]