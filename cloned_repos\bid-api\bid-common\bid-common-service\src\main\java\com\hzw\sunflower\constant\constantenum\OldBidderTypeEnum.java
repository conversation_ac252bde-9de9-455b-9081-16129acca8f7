package com.hzw.sunflower.constant.constantenum;

/**
 * <AUTHOR>
 * @date ：Created in 2021/9/10 11:27
 * @description：上次项目投标人类型
 * @version: 1.0
 */
public enum OldBidderTypeEnum {

    BUY(1, "已购买招标文件的投标人"),
    ATTENTION(2, "已通过关注审核的投标人"),
    ALL(3, " 指定投标人"),
    EMPTY(4, "不带入");

    private Integer type;
    private String desc;

    OldBidderTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
