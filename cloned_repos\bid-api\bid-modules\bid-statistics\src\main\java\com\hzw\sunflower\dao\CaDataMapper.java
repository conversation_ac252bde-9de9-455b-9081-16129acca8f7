package com.hzw.sunflower.dao;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hzw.sunflower.controller.request.CaDataReq;
import com.hzw.sunflower.controller.response.CaBidOpeningVo;
import com.hzw.sunflower.controller.response.CaVo;
import com.hzw.sunflower.controller.response.CaDataVo;
import com.hzw.sunflower.entity.DepartProject;

import com.hzw.sunflower.controller.response.ProcessAddress;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/3/9 16:51 星期四
 */
@Repository
public interface CaDataMapper {


    IPage<CaDataVo> findInfoByReq(IPage<CaDataVo> page, @Param("req") CaDataReq req);

    List<DepartProject> getDeptIds();

    List<ProcessAddress> getRelatedProjectAddress();

    List<CaDataVo> exportCaList( @Param("req") CaDataReq req);

    List<CaVo> getCaList();

    List<CaBidOpeningVo> getCaBidOpeningList(@Param("req") Integer req);


}
