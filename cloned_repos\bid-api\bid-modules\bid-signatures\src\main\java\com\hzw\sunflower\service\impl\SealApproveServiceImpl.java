package com.hzw.sunflower.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzw.sunflower.dao.SealApproveMapper;
import com.hzw.sunflower.dto.SealApproveDTO;
import com.hzw.sunflower.entity.SealApprove;
import com.hzw.sunflower.entity.condition.SealApproveCondition;
import com.hzw.sunflower.exception.ExceptionEnum;
import com.hzw.sunflower.exception.SunFlowerException;
import com.hzw.sunflower.service.SealApproveService;
import org.springframework.stereotype.Service;

import java.util.List;


/**
* SealApproveService接口实现
*
* <AUTHOR>
* @version 1.0.0 2021-04-07
*/
@Service
public class SealApproveServiceImpl extends ServiceImpl<SealApproveMapper, SealApprove> implements SealApproveService {

    /**
    * 根据条件分页查询列表
    *
    * @param condition 查询条件
    * @return 分页数据
    */
    @Override
    public IPage<SealApprove> findInfoByCondition(SealApproveCondition condition) {
        IPage<SealApprove> page = condition.buildPage();
        QueryWrapper<SealApprove> queryWrapper = condition.buildQueryWrapper(SealApprove.class);
        return this.page(page, queryWrapper);
    }

    /**
    * 根据主键ID查询
    *
    * @param id 主键ID
    * @return
    */
    @Override
    public SealApprove getInfoById(Long id) {
        return this.getById(id);
    }

    /**
    * 新增
    *
    * @param sealApprove
    * @return 是否成功
    */
    @Override
    public Boolean addInfo(SealApproveDTO sealApprove) {
        return this.save(sealApprove);
    }

    /**
    * 修改
    *
    * @param sealApprove
    * @return 是否成功
    */
    @Override
    public Boolean updateInfo(SealApproveDTO sealApprove) {
        return this.updateById(sealApprove);
    }

    /**
    * 根据主键ID删除
    *
    * @param id 主键ID
    * @return 是否成功
    */
    @Override
    public Boolean deleteById(Long id) {
        return this.removeById(id);
    }

    /**
    * 根据主键ID列表批量删除
    *
    * @param idList 主键ID列表
    * @return 是否成功
    */
    @Override
    public Boolean deleteByIds(List<Long> idList) {
        return this.removeByIds(idList);
    }

}