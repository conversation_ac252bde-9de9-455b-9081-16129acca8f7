package com.hzw.ssm.sys.login.action;

import java.io.IOException;
import java.util.List;
import java.util.Map;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpSession;

import org.apache.struts2.convention.annotation.Action;
import org.apache.struts2.convention.annotation.Namespace;
import org.apache.struts2.convention.annotation.ParentPackage;
import org.apache.struts2.convention.annotation.Result;
import org.apache.struts2.convention.annotation.Results;
import org.springframework.beans.factory.annotation.Autowired;

import com.hzw.ssm.expert.entity.ExpertInfoEntity;
import com.hzw.ssm.expert.service.ExpertInfoService;
import com.hzw.ssm.fw.base.BaseAction;
import com.hzw.ssm.fw.util.CommUtil;
import com.hzw.ssm.fw.util.HZWException;
import com.hzw.ssm.fw.util.SysConstants;
import com.hzw.ssm.sys.login.service.LoginService;
import com.hzw.ssm.sys.project.entity.ProjectEntity;
import com.hzw.ssm.sys.system.entity.MenuEntity;
import com.hzw.ssm.sys.system.entity.RoleEntity;
import com.hzw.ssm.sys.system.entity.SystemLogEntity;
import com.hzw.ssm.sys.system.service.RoleService;
import com.hzw.ssm.sys.system.service.SystemLogService;
import com.hzw.ssm.sys.user.entity.UserEntity;
import com.hzw.ssm.util.empty.EmptyUtils;
import com.hzw.ssm.util.encrypt.AesEncryptUtil;
import com.hzw.ssm.util.encrypt.Base64Util;
import com.opensymphony.xwork2.ActionContext;


@Namespace("/serviceLogin")
@ParentPackage(value = "default")
@Results( { @Result(name = "error", location = "/jsp/login.jsp"),
		@Result(name = "success", location="/jsp/index.jsp"),
		@Result(name = "newSuccess", type="redirect", location="/serviceLogin/enter"),
		@Result(name = "loginout", type="redirect", location="/login/cancellation")
})
public class ServiceLoginAction extends BaseAction{
	
	private static final long serialVersionUID = -8823858495799309882L;
		
	/** 用户实体类 */
	private UserEntity userBean;
	/** 提示信息 */
	private String message;
	/** 随机码 */
	private String loginCode;
	/** cookie失效时长（秒）*/
	private int cookie_time = 3600;
	/** cookie 路径*/
	private String cookie_path = "/JSJX";
	/** cookie 帐号标识*/
	private String cookie_code = "jsjx_code";
	/** cookie 密码标识*/
	private String cookie_pw = "jsjx_pw";
	
	private List<MenuEntity>  menus;
	
	private SystemLogEntity logEntity = new SystemLogEntity();
	
	@Autowired
	private LoginService ser;
	
	@Autowired
	private SystemLogService sLog;
	
	@Autowired
	private ExpertInfoService expertInfoService;
	
	@Autowired
	private RoleService roleService;
	
	private Long registerNum;//专家注册人数
	private Long modifyNum;//专家修改审核
	private Long designatedExpertsNum;    // 指定专家审核人数
	
	/**
	 * 常规登录
	 */
	@Action("enter")
	public String enter() throws IOException{
		this.context();
		//有无用户信息
		if(null==userBean){
			HttpSession session = getRequest().getSession();
			if (null == session.getAttribute("userInfo"))
			{
				return ERROR;
			}
			userBean = (UserEntity)session.getAttribute("userInfo");
		}

		//获取登录名及密码
		String loginCodeValue = userBean.getLogin_code().trim();
		String passwordValue = userBean.getPassword().trim();

		/*//对登录名及密码进行Base64解码
		String loginCodeStr = Base64Util.base64Decode(loginCodeValue, "UTF-8");
		String passwordStr = Base64Util.base64Decode(passwordValue, "UTF-8").toUpperCase();*/
		
		//用户输入验证
		if (EmptyUtils.isEmpty(loginCodeValue) || EmptyUtils.isEmpty(loginCodeValue.trim())
				|| EmptyUtils.isEmpty(passwordValue) || EmptyUtils.isEmpty(passwordValue.trim())) {
			this.setMessage("请输入用户名及密码！");
			return ERROR;
		}
		
		//用户信息验证（常规登录）
		UserEntity userEntity = ser.login(loginCodeValue, passwordValue,2);
		if(EmptyUtils.isEmpty(userEntity)){
			this.setMessage("用户名或密码错误！");
			return ERROR;
		}else{
			return LoginLimit(userEntity);
		}
	}
	
	/**
	 * 登录成功之后重定向
	 */
	@Action("access")
	public String access() throws IOException{
		this.context();
		//有无用户信息
		if(null==userBean){
			HttpSession session = getRequest().getSession();
			if (null == session.getAttribute("userInfo"))
			{
				return ERROR;
			}
			userBean = (UserEntity)session.getAttribute("userInfo");
		}
		//获取登录名及密码
		String code = userBean.getLogin_code().trim();
		String pwd = userBean.getPassword().trim();

		//对登录名及密码进行Base64解码
//		String loginCodeStr = Base64Util.base64Decode(loginCodeValue, "UTF-8");
//		String passwordStr = Base64Util.base64Decode(passwordValue, "UTF-8").toUpperCase();

		try {
			String loginCodeValue = AesEncryptUtil.desEncrpt(code);
			String passwordValue = AesEncryptUtil.desEncrpt(pwd);
			//用户输入验证
			if (EmptyUtils.isEmpty(loginCodeValue) || EmptyUtils.isEmpty(loginCodeValue.trim())
					|| EmptyUtils.isEmpty(passwordValue) || EmptyUtils.isEmpty(passwordValue.trim())) {
				this.setMessage("请输入用户名及密码！");
				return ERROR;
			}
			//用户信息验证（常规登录）
			UserEntity userEntity = ser.login(loginCodeValue, passwordValue,2);
			if(EmptyUtils.isEmpty(userEntity)){
				this.setMessage("用户名或密码错误！");
				return ERROR;
			}
			
			if (userEntity != null) {
				this.getRequest().setAttribute("longCode", loginCodeValue);
				this.getRequest().setAttribute("longPwd", passwordValue);
				if(userEntity.getEnabled_flag()==2L){
					this.setMessage("用户被禁用，请联系管理员！");
					return ERROR;
				}
				RoleEntity re = roleService.getRoleInfoById(userEntity.getRole_id());
				if(re != null && !"".equals(re)){
					userEntity.setRole_name(re.getRoleName());
				}
				LoginSucess(userEntity);					
			}
		
			this.initMessage();
		} catch (Exception e) {
			if(e instanceof HZWException){
				this.setMessage(e.getMessage());
			}
			log.error("登录失败", e);
			e.printStackTrace();
			
			return "loginout";
		}
		return "newSuccess";
	}
	
	/**
	 * 登录限制验证
	 * @param info
	 * @return
	 */
	private String LoginLimit(UserEntity info){
		if (info != null) {
			////方便返回登录名回填到表单中
			this.getRequest().setAttribute("longCode", info.getLogin_code().trim());
			////登录名和密码验证成功后保存密码
			this.getRequest().setAttribute("longPwd", info.getPassword().trim());
			if(info.getEnabled_flag()==2L){
				this.setMessage("用户被禁用，请联系管理员！");
				return ERROR;
			}
			RoleEntity re = roleService.getRoleInfoById(info.getRole_id());
			if(re != null && !"".equals(re)){
				info.setRole_name(re.getRoleName());
			}
			LoginSucess(info);					
		}
		try {
			this.initMessage();
		} catch (Exception e) {
			if(e instanceof HZWException){
				this.setMessage(e.getMessage());
			}
			log.error("登录失败", e);
			return "loginout";
		}
		return SUCCESS;
	}
	
	/**
	 * 登录成功设置
	 * @param user
	 */
	private void LoginSucess(UserEntity user){
		//用户信息存入session
		HttpSession session = getRequest().getSession();
		session.setAttribute("userInfo", user);
		userBean = user;
		//全局变量设置
		ActionContext ac = ActionContext.getContext();
		Map applicationMap = ac.getApplication();	
		applicationMap.remove(userBean.getLogin_code().trim());
		applicationMap.put(userBean.getLogin_code().trim(), session);
		
		//cookie设置
		getResponse().setContentType("text/html;charset=utf-8");  
		Cookie namecookie = new Cookie(cookie_code,user.getLogin_code());
		namecookie.setMaxAge(cookie_time);
		namecookie.setPath(cookie_path);
		getResponse().addCookie(namecookie);
		Cookie passwordcookie = new Cookie(cookie_pw,user.getPassword());
		passwordcookie.setMaxAge(cookie_time);
		namecookie.setPath(cookie_path);
		getResponse().addCookie(passwordcookie);
		
		//登录日志
		logEntity.setLOG_ID(CommUtil.getKey());
		logEntity.setOPA_USER(user.getUser_name());
		logEntity.setOPA_IP(getRequest().getRemoteAddr());
		logEntity.setOPA_FUN("/login/enter");
		logEntity.setOPA_METHOD("系统登录");
		this.setOpaUserAndDate(logEntity);
		sLog.saveSystemLog(logEntity);
	}
	
	/**
	 * 初始化待办消息提示
	 * @return
	 */
	@Action("initMessage")
	public String initMessage(){
		this.context();
		//当前session
		HttpSession session = getRequest().getSession();
		//当前用户信息
		UserEntity info = (UserEntity)session.getAttribute("userInfo");
		menus=ser.getMenuMapOfAuth(info.getRole_id());//获得该角色拥有的权限
		filterMenus(info,menus);
		registerNum=0L;
		modifyNum=0L;
		designatedExpertsNum = 0L;
		List<ExpertInfoEntity> list=null;
		String status="-100";//
		for(MenuEntity m:menus){
			if(("/expertInfoAudit/expertInfoAuditList").equals(m.getMenu_url())){//准入审批
				if(SysConstants.ROLE_ID.AUDITOR.equals(info.getRole_id())){//审核员角色
					status="1";
				}
				if(SysConstants.ROLE_ID.DIRECTOR.equals(info.getRole_id())){//主任
					status="2";
				}
				list=ser.queryRegisterExpert(status);//查询注册专家
				if(list!=null){
					registerNum=Long.parseLong(list.size()+"");
				}
			}
			if(("/expertModifyAudit/expertModifyAuditList").equals(m.getMenu_url())){//修改审批
				if(SysConstants.ROLE_ID.AUDITOR.equals(info.getRole_id())){//审核员角色
					status="5";
				}
				if(SysConstants.ROLE_ID.DIRECTOR.equals(info.getRole_id())){//主任
					status="6";
				}
				list=ser.queryRegisterExpert(status);//查询修改专家
				if(list!=null){
					modifyNum=Long.parseLong(list.size()+"");
				}
			}
			// 指定专家审核
			if ("/appointAudit/queryProjectList".equals(m.getMenu_url()))
			{
				ProjectEntity project = new ProjectEntity();
				if (SysConstants.ROLE_ID.AUDITOR.equals(info.getRole_id()) || SysConstants.ROLE_ID.COMMISSIONER.equals(info.getRole_id()))    
				{
					project.setDepartment(info.getDepartment());
					project.setCreate_id(info.getUser_id());
					project.setStatus(SysConstants.PROJECT_STATUS.APPOINT_WAIT_AUDIT);
					designatedExpertsNum = ser.queryProjectCount(project);
					if (SysConstants.ROLE_ID.COMMISSIONER.equals(info.getRole_id()))
					{
						registerNum=-1L;
					}
				}
				else if (SysConstants.ROLE_ID.DIRECTOR.equals(info.getRole_id()))
				{
					project.setStatus(SysConstants.PROJECT_STATUS.APPOINT_AUDIT_PASS);
					designatedExpertsNum = ser.queryProjectCount(project);
				}
			}
		}
		try {
			this.getResponse().getWriter().write(designatedExpertsNum + ":" + registerNum+":"+modifyNum);
		} catch (IOException e) {
			e.printStackTrace();
		}
		return null;
	}
	
	/**
	 * 刚注册的专家，和已经入库的专家看到的菜单不相同
	 * 过滤菜单
	 * @param info
	 * @param menus
	 */
	private void filterMenus(UserEntity info,List<MenuEntity> menus){
		if(info.getRole_id().equals(SysConstants.ROLE_ID.EXPERT) || (info.getRole_name()!=null && info.getRole_name().equals(SysConstants.ROLE_NAME.EXPERT))){
			ExpertInfoEntity colExpert=new ExpertInfoEntity();
			colExpert.setUser_id(info.getUser_id());
			ExpertInfoEntity expert=expertInfoService.getExpertInfoByUserIdOrId(colExpert);
			for(int i=0;i<menus.size();i++){
				MenuEntity pm=menus.get(i);
				if(expert==null || expert.getStatus()<3){
					if(pm.getMenu_id().equals("20141017093154666011") && pm.getParent_menu_id().equals("-1")){//日常工作菜单
						menus.remove(pm);
						i=i-1;
					}else if(pm.getMenu_id().equals("20141010153245331025") && pm.getParent_menu_id().equals("-1")){//账户信息管理
						List<MenuEntity> childmenus=pm.getSubMenuList();
						for(int j=0;j<childmenus.size();j++){
							MenuEntity cm=childmenus.get(j);
							if(cm.getMenu_id().equals("20141020082748012025")){//基本信息维护
								childmenus.remove(cm);
								j--;
								break;
							}
						}
					}
				}else{
					if(pm.getMenu_id().equals("20141010153245331025") && pm.getParent_menu_id().equals("-1")){//账户信息管理
						List<MenuEntity> childmenus=pm.getSubMenuList();
						for(int j=0;j<childmenus.size();j++){
							MenuEntity cm=childmenus.get(j);
							if(cm.getMenu_id().equals("20141010153540294035")){//填写申请资料
								childmenus.remove(cm);
								j--;
								break;
							}
						}
					}
				}
			}
		}
	}
	
	public UserEntity getUserBean() {
		return userBean;
	}
	public void setUserBean(UserEntity userBean) {
		this.userBean = userBean;
	}
	public String getMessage() {
		return message;
	}
	public void setMessage(String message) {
		this.message = message;
	}
	public String getLoginCode() {
		return loginCode;
	}
	public void setLoginCode(String loginCode) {
		this.loginCode = loginCode;
	}

	public int getCookie_time() {
		return cookie_time;
	}

	public void setCookie_time(int cookie_time) {
		this.cookie_time = cookie_time;
	}

	public String getCookie_path() {
		return cookie_path;
	}

	public void setCookie_path(String cookie_path) {
		this.cookie_path = cookie_path;
	}

	public String getCookie_code() {
		return cookie_code;
	}

	public void setCookie_code(String cookie_code) {
		this.cookie_code = cookie_code;
	}

	public String getCookie_pw() {
		return cookie_pw;
	}

	public void setCookie_pw(String cookie_pw) {
		this.cookie_pw = cookie_pw;
	}

	public List<MenuEntity> getMenus() {
		return menus;
	}

	public void setMenus(List<MenuEntity> menus) {
		this.menus = menus;
	}

	public SystemLogEntity getLogEntity() {
		return logEntity;
	}

	public void setLogEntity(SystemLogEntity logEntity) {
		this.logEntity = logEntity;
	}

	public Long getRegisterNum() {
		return registerNum;
	}

	public void setRegisterNum(Long registerNum) {
		this.registerNum = registerNum;
	}

	public Long getModifyNum() {
		return modifyNum;
	}

	public void setModifyNum(Long modifyNum) {
		this.modifyNum = modifyNum;
	}

	public Long getDesignatedExpertsNum() {
		return designatedExpertsNum;
	}

	public void setDesignatedExpertsNum(Long designatedExpertsNum) {
		this.designatedExpertsNum = designatedExpertsNum;
	}
	
}
