package com.hzw.sunflower.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzw.sunflower.controller.request.PromiseFileSignReq;
import com.hzw.sunflower.dao.PromiseFileSignMapper;
import com.hzw.sunflower.entity.JwtUser;
import com.hzw.sunflower.entity.OssFile;
import com.hzw.sunflower.entity.PromiseFileSign;
import com.hzw.sunflower.service.OssFileService;
import com.hzw.sunflower.service.PromiseFileSignService;
import com.hzw.sunflower.util.OssUtil;
import com.hzw.sunflower.util.oss.OssFileStorage;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.util.UUID;


/**
 * <AUTHOR>
 * @date ：Created in 2021/8/27 13:51
 * @description：
 * @version: 1.0
 */
@Service
public class PromiseFileSignServiceImpl extends ServiceImpl<PromiseFileSignMapper, PromiseFileSign> implements PromiseFileSignService {

    @Value("${oss.active}")
    private String ossType;

    @Autowired
    private OssFileService ossFileService;

    /**
     * 专家签署承诺文件
     * @param req
     * @param jwtUser
     * @return
     */
    @Override
    public Boolean sign(MultipartFile file,PromiseFileSignReq req, JwtUser jwtUser) {
        // 文件入库
        Long fileOssId = this.uploadSignFile(file);
        PromiseFileSign sign = new PromiseFileSign();
        sign.setUserId(jwtUser.getUserId());
        sign.setPromiseFileId(req.getPromiseFileId());
        sign.setFileOssId(fileOssId);
        return this.save(sign);
    }

    public Long uploadSignFile(MultipartFile file) {
        OssFileStorage storage = OssUtil.getOssFileStorage(ossType);
        String key = UUID.randomUUID() +".pdf";
        try {
            storage.uploadFile(key, file.getInputStream());
            if(StringUtils.isNotBlank(key)) {
                OssFile ossFile = new OssFile();
                ossFile.setOssFileKey(key);
                ossFile.setOssFileName(file.getOriginalFilename());
                ossFileService.save(ossFile);
                return ossFile.getId();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

}
