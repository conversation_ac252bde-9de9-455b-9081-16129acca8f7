package com.hzw.sunflower.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hzw.sunflower.entity.BidDocColumnConfig;
import com.hzw.sunflower.dao.BidDocColumnConfigMapper;
import com.hzw.sunflower.service.BidDocColumnConfigService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 招标文件编制字段配置展示表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-26
 */
@Service
public class BidDocColumnConfigServiceImpl extends ServiceImpl<BidDocColumnConfigMapper, BidDocColumnConfig> implements BidDocColumnConfigService {

    /**
     * 根据模板id获取配置信息
     * @param templateId
     * @return
     */
    @Override
    public Map<Long, List<BidDocColumnConfig>> getConfigByTemplateId(Long templateId) {
        Map<Long, List<BidDocColumnConfig>> map = null;
        List<BidDocColumnConfig> list = this.list(new LambdaQueryWrapper<BidDocColumnConfig>().eq(BidDocColumnConfig::getTemplateId, templateId)
                .orderByAsc(BidDocColumnConfig::getCatalogId));
        if (list.size() > 0) {
            map = list.stream().collect(Collectors.groupingBy(BidDocColumnConfig::getCatalogId));
        }
        return map;
    }
}
