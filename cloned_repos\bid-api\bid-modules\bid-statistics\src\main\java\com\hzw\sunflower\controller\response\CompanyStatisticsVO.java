package com.hzw.sunflower.controller.response;

import cn.afterturn.easypoi.excel.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class CompanyStatisticsVO {

    @ApiModelProperty(value = "主键")
    private long id;

    @Excel(name = "月份",orderNum = "1")
    @ApiModelProperty(value = "月份")
    private String mon;


    @ApiModelProperty(value = "月份")
    private String yearmonth;

    @ApiModelProperty(value = "项目经理id")
    private long userId;


    @ApiModelProperty(value = "项目经理名称")
    private String userName;

    @ApiModelProperty(value = "部门id")
    private long departmentId;


    @ApiModelProperty(value = "部门名称")
    private String departmentName;

    @Excel(isWrap = false,name = "今年",groupName = "已开标项目数",orderNum = "2")
    @ApiModelProperty(value = "已开标项目数")
    private long projectCount;

    @Excel(isWrap = false,name = "今年",groupName = "未开标项目数",orderNum = "20")
    @ApiModelProperty(value = "未开标项目数")
    private long noProjectCount;

    @Excel(isWrap = false,name = "今年",groupName = "已开标项目委托金额(万元)",orderNum = "5")
    @ApiModelProperty(value = "已开标项目委托金额")
    private BigDecimal entrustedAmount;

    @Excel(isWrap = false,name = "今年",groupName = "未开标项目委托金额(万元)",orderNum = "23")
    @ApiModelProperty(value = "未开标项目委托金额")
    private BigDecimal noEntrustedAmount;

    @Excel(isWrap = false,name = "今年",groupName = "中标金额(万元)",orderNum = "8")
    @ApiModelProperty(value = "中标金额")
    private BigDecimal winPrice;

    @Excel(isWrap = false,name = "今年",groupName = "已收费(万元)",orderNum = "11")
    @ApiModelProperty(value = "已收费")
    private BigDecimal account;

    @Excel(isWrap = false,name = "今年",groupName = "已开标未收费中标金额预估(万元)",orderNum = "14")
    @ApiModelProperty(value = "已开标未收费中标金额预估")
    private BigDecimal estimateAccount;

    @Excel(isWrap = false,name = "今年",groupName = "已开标未收费金额(万元)",orderNum = "17")
    @ApiModelProperty(value = "已开标未收费金额")
    private BigDecimal noAccount;

    @Excel(isWrap = false,name = "去年",groupName = "已开标项目数",orderNum = "3")
    @ApiModelProperty(value = "去年已开标项目数")
    private long projectCountAgo;

    @Excel(isWrap = false,name = "去年",groupName = "未开标项目数",orderNum = "21")
    @ApiModelProperty(value = "去年未开标项目数")
    private long noProjectCountAgo;

    @Excel(isWrap = false,name = "去年",groupName = "已开标项目委托金额(万元)",orderNum = "6")
    @ApiModelProperty(value = "去年已开标项目委托金额")
    private BigDecimal entrustedAmountAgo;

    @Excel(isWrap = false,name = "去年",groupName = "未开标项目委托金额(万元)",orderNum = "24")
    @ApiModelProperty(value = "去年未开标项目委托金额")
    private BigDecimal noEntrustedAmountAgo;

    @Excel(isWrap = false,name = "去年",groupName = "中标金额(万元)",orderNum = "9")
    @ApiModelProperty(value = "去年中标金额")
    private BigDecimal winPriceAgo;

    @Excel(isWrap = false,name = "去年",groupName = "已收费(万元)",orderNum = "12")
    @ApiModelProperty(value = "去年已收费")
    private BigDecimal accountAgo;

    @Excel(isWrap = false,name = "去年",groupName = "已开标未收费中标金额预估(万元)",orderNum = "15")
    @ApiModelProperty(value = "去年已开标未收费中标金额预估")
    private BigDecimal estimateAccountAgo;

    @Excel(isWrap = false,name = "去年",groupName = "已开标未收费金额(万元)",orderNum = "18")
    @ApiModelProperty(value = "去年已开标未收费金额")
    private BigDecimal noAccountAgo;

    @Excel(isWrap = false,name = "增长数",groupName = "已开标项目数",orderNum = "4")
    @ApiModelProperty(value = "已开标项目数增长")
    private long projectCountMinus;

    @Excel(isWrap = false,name = "增长数",groupName = "未开标项目数",orderNum = "22")
    @ApiModelProperty(value = "未开标项目数增长")
    private long noProjectCountMinus;

    @Excel(isWrap = false,name = "增长数",groupName = "已开标项目委托金额(万元)",orderNum = "7")
    @ApiModelProperty(value = "已开标项目委托金额增长")
    private BigDecimal entrustedAmountMinus;

    @Excel(isWrap = false,name = "增长数",groupName = "未开标项目委托金额(万元)",orderNum = "25")
    @ApiModelProperty(value = "未开标项目委托金额增长")
    private BigDecimal noEntrustedAmountMinus;

    @Excel(isWrap = false,name = "增长数",groupName = "中标金额(万元)",orderNum = "10")
    @ApiModelProperty(value = "中标金额增长")
    private BigDecimal winPriceMinus;

    @Excel(isWrap = false,name = "增长数",groupName = "已收费(万元)",orderNum = "13")
    @ApiModelProperty(value = "已收费增长")
    private BigDecimal accountMinus;

    @Excel(isWrap = false,name = "增长数",groupName = "已开标未收费中标金额预估(万元)",orderNum = "16")
    @ApiModelProperty(value = "已开标未收费中标金额预估增长")
    private BigDecimal estimateAccountMinus;

    @Excel(isWrap = false,name = "增长数",groupName = "已开标未收费金额(万元)",orderNum = "19")
    @ApiModelProperty(value = "已开标未收费金额增长")
    private BigDecimal noAccountMinus;

}
