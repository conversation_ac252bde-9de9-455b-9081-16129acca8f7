package com.hzw.sunflower.service.impl;

import com.hzw.sunflower.entity.BondAgentAccount;
import com.hzw.sunflower.dao.BondAgentAccountMapper;
import com.hzw.sunflower.service.BondAgentAccountService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 保证金代理服务费账号 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-12
 */
@Service
public class BondAgentAccountServiceImpl extends ServiceImpl<BondAgentAccountMapper, BondAgentAccount> implements BondAgentAccountService {

}
