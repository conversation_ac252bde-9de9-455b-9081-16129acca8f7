package com.hzw.ssm.sys.login.action;

import java.text.DecimalFormat;
import java.util.List;

import javax.servlet.http.HttpSession;

import org.apache.struts2.convention.annotation.Action;
import org.apache.struts2.convention.annotation.Namespace;
import org.apache.struts2.convention.annotation.ParentPackage;
import org.apache.struts2.convention.annotation.Result;
import org.apache.struts2.convention.annotation.Results;
import org.springframework.beans.factory.annotation.Autowired;

import com.hzw.ssm.fw.base.BaseAction;
import com.hzw.ssm.sys.user.entity.HomeQrEntity;
import com.hzw.ssm.sys.user.entity.UserEntity;
import com.hzw.ssm.sys.user.service.UserService;


@Namespace("/HomeTab")
@ParentPackage(value = "default")
@Results( { @Result(name = "success", location = "/jsp/tab.jsp"),
	@Result(name = "error", type="redirect", location="/login/access"),
	@Result(name = "recommend", location = "/jsp/recommend.jsp")
})
public class HomeTabAction extends BaseAction{

	@Autowired
	private UserService userService;
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	
	/** 用户职务级别 */
	private Long userPost;

	/** 用户对象列表 */
	private List<UserEntity> lstUser;
	@Action("homeTab")
	public String homeTab(){
		this.context();
		//当前session
		HttpSession session = getRequest().getSession();
		
		//当前用户信息
		UserEntity info = (UserEntity)session.getAttribute("userInfo");
		
		if(null==info){
			return ERROR;
		}
		return SUCCESS;
	}
	
	/**
	 * 首页推荐
	 * @return
	 */
	@Action("homeRecommendation")
	public String homeRecommendation(){
		this.context();
		//当前session
		HttpSession session = getRequest().getSession();
		
		//当前用户信息
		UserEntity info = (UserEntity)session.getAttribute("userInfo");
		
		 entity = userService.selectUserRecomend(info.getUser_id());
		 if(entity.getReferralsSucCount()!=0&& entity.getReferralsCount()!=0) {
			 
		 DecimalFormat decimalFormat= new DecimalFormat( "0.000" ); //构造方法的字符格式这里如果小数不足2位,会以0补足.
		 float  avg = Float.valueOf(entity.getReferralsSucCount())/Float.valueOf(entity.getReferralsCount());
		 //float  avg = (float) (entity.getReferralsSucCount()/entity.getReferralsCount());
		 String p=decimalFormat.format(avg);
		 entity.setAvgrefer(p);
		 }else {
			 entity.setAvgrefer("0.0000");
		 }
		return "recommend";
	}
	
	private HomeQrEntity entity;
	public List<UserEntity> getLstUser() {
		return lstUser;
	}

	public void setLstUser(List<UserEntity> lstUser) {
		this.lstUser = lstUser;
	}

	public Long getUserPost() {
		return userPost;
	}

	public void setUserPost(Long userPost) {
		this.userPost = userPost;
	}

	public UserService getUserService() {
		return userService;
	}

	public void setUserService(UserService userService) {
		this.userService = userService;
	}

	public HomeQrEntity getEntity() {
		return entity;
	}

	public void setEntity(HomeQrEntity entity) {
		this.entity = entity;
	}

	
}
