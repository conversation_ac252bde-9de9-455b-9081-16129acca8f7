package com.hzw.sunflower.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzw.sunflower.controller.request.BidDocDetailExcelReq;
import com.hzw.sunflower.dao.BidDocEvaluationDetailsMapper;
import com.hzw.sunflower.entity.BidDocEvaluationDetails;
import com.hzw.sunflower.entity.OssFile;
import com.hzw.sunflower.service.BidDocEvaluationDetailsService;
import com.hzw.sunflower.service.OssFileService;
import com.hzw.sunflower.util.ExcelParserUtil;
import com.hzw.sunflower.util.FileUtils;
import com.hzw.sunflower.util.oss.OssFileStorage;
import com.hzw.sunflower.util.oss.OssUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;

@Service
public class BidDocEvaluationDetailsServiceImpl extends ServiceImpl<BidDocEvaluationDetailsMapper, BidDocEvaluationDetails> implements BidDocEvaluationDetailsService {

    @Value("${files.template.path}")
    private String templatePath;

    @Value("${oss.active}")
    private String ossType;

    @Autowired
    private OssFileService ossFileService;

    /**
     * 下载模板 详细评审评标办法导入模板
     * @param request
     * @param response
     */
    @Override
    public void downTemplateDetails(HttpServletRequest request, HttpServletResponse response) {
        String templateName = "evaluationDetails.xlsx";
        String fileName = "详细评审评标办法导入模板.xlsx";
        //读取的模板路径
        String readPath = templatePath + templateName;
        //以流的形式输出
        try {
            response.setContentType("multipart/form-data");
            FileUtils.outWordFile(readPath,fileName,response);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 批量导入 详细评审评标办法文件解析
     * @param req
     * @return
     */
    @Override
    public List<List<String>> detailsExcelParse(BidDocDetailExcelReq req) {
        //下载文件
        OssFile ossFile = ossFileService.getOssFileById(req.getOssFileId());
        OssFileStorage storage = OssUtil.getOssFileStorage(ossType);
        InputStream inputStream = null;
        if(null != ossFile && null != ossFile.getOssFileKey() ){
            byte[] bytes = storage.downloadFile(ossFile.getOssFileKey());
            inputStream = FileUtils.byte2InputStream(bytes);
        }
        // 取3列，最多100行数据
        List<List<String>> listList = ExcelParserUtil.parseExcelSheet0(inputStream, 101, 3, 1);
        return listList;
    }
}
