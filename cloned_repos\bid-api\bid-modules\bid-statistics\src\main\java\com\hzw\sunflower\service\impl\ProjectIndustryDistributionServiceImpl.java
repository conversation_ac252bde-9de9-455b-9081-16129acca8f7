package com.hzw.sunflower.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzw.sunflower.dao.ProjectIndustryDistributionMapper;
import com.hzw.sunflower.entity.ProjectIndustryDistribution;
import com.hzw.sunflower.service.ProjectIndustryDistributionService;
import org.springframework.stereotype.Service;


@Service
public class ProjectIndustryDistributionServiceImpl extends ServiceImpl<ProjectIndustryDistributionMapper, ProjectIndustryDistribution> implements ProjectIndustryDistributionService {

    @Override
    public int deleteCurrentMonth(String submitEndTime) {
        return this.baseMapper.deleteCurrentMonth(submitEndTime);
    }
}
