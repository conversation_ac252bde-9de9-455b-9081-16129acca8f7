package com.hzw.sunflower.constant.constantenum;

import java.util.ArrayList;
import java.util.List;

public enum SealApplicationApproveEnum {
    // 归口审批部门
    // 办公室
    BGS(1,"bgs"),
    // 运管处
    YGC(2,"ygc"),
    // 财审处
    CSC(3,"csc"),

    // 审批领导
    WJZ(1,"王建专"),
    YFC(2,"叶逢春"),
    LW(3,"凌文"),
    YRZ(4,"姚荣政"),
    SH(5,"宋航"),


    ;

    private Integer type;
    private String desc;

    SealApplicationApproveEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public static List<SealApplicationApproveEnum> getDeptList() {
        List<SealApplicationApproveEnum> list = new ArrayList<>();
        list.add(SealApplicationApproveEnum.BGS);
        list.add(SealApplicationApproveEnum.YGC);
        list.add(SealApplicationApproveEnum.CSC);
        return list;
    }

}
