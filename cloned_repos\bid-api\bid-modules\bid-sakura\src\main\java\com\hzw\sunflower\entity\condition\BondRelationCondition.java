package com.hzw.sunflower.entity.condition;

import com.hzw.sunflower.common.BaseCondition;
import com.hzw.sunflower.controller.request.BondSectionReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2022/7/27 11:18
 * @description：
 * @modified By：`
 * @version: 1.0
 */
@ApiModel(description = "标段流水关联详情查询条件")
@Data
public class BondRelationCondition extends BaseCondition {

    @ApiModelProperty(value = "项目Id")
    private Long projectId;

    @ApiModelProperty(value = "供应商id")
    private Long companyId;

    @ApiModelProperty(value = "标段id 如果有多个以逗号隔开")
    private String sectionId;

    @ApiModelProperty(value = "标段id列表 确认关联需传递")
    List<BondSectionReq> sectionIdList;

    @ApiModelProperty(value = "用户id 无需传递")
    private Long userId;

    @ApiModelProperty(value = "关联状态")
    private Integer relationStatus;

    @ApiModelProperty(value = "流水id 确认关联传输此字段，已关联无需传输")
    private Long waterId;

}
