package com.hzw.externalApi.api.domain;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class VoiceInfoVo implements Serializable {
    /**
     * 语音通知id
     */
    private String id;

    private String extractionId;
    /**
     * 专家抽取id
     */
    private String recordId;
    /**
     * 专家电话
     */
    private String contactNumber;
    /**
     * 通话结束原因
     */
    private String calledRelReason;
    /**
     * 最后一次按键
     */
    private String dtmfKey;
    /**
     * 评审时间
     */
    private String reviewTime;
    /**
     * 评审地点
     */
    private String reviewLocation;
    /**
     * 抽取人
     */
    private String submitUserName;
    /**
     * 抽取人手机号
     */
    private String submitUserPhone;

}
