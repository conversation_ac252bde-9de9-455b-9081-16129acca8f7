package com.hzw.sunflower.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.hzw.sunflower.common.BaseBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * Created by zgq on 2021/06/30
 */
@ApiModel("app已办记录表")
@TableName("app_to_do_record")
@Data
public class AppToDoRecord extends BaseBean implements Serializable {

    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "待办标题")
    private String title;

    @ApiModelProperty(value = "待办类型")
    private String type;

    @ApiModelProperty(value = "项目ID")
    private Long projectId;

    @ApiModelProperty(value = "待办完整json数据")
    private String taskData;

    @ApiModelProperty(value = "审批结果 1.通过 2.退回")
    private Integer result;

    @ApiModelProperty(value = "创建人部门ID")
    private Long createdUserDepartId;

    @ApiModelProperty(value = "设备 1.手机OA 2.PC")
    private Integer equipment;

}
