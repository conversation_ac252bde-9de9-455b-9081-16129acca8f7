package com.hzw.sunflower.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzw.sunflower.util.BeanListUtil;
import com.hzw.sunflower.constant.constantenum.NoticeProgressEnum;
import com.hzw.sunflower.dao.SectionGroupMapper;
import com.hzw.sunflower.dto.GroupSectionInfoDTO;
import com.hzw.sunflower.dto.SectionGroupDTO;
import com.hzw.sunflower.entity.ProjectBidDoc;
import com.hzw.sunflower.entity.ProjectBidDocRelation;
import com.hzw.sunflower.entity.ProjectBidSection;
import com.hzw.sunflower.entity.SectionGroup;
import com.hzw.sunflower.entity.condition.SectionGroupCondition;
import com.hzw.sunflower.service.ProjectBidDocRelationService;
import com.hzw.sunflower.service.ProjectBidDocService;
import com.hzw.sunflower.service.ProjectBidSectionService;
import com.hzw.sunflower.service.SectionGroupService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date ：Created in 2021/8/27 13:51
 * @description：
 * @version: 1.0
 */
@Service
public class SectionGroupServiceImpl extends ServiceImpl<SectionGroupMapper, SectionGroup> implements SectionGroupService {

    @Autowired
    private ProjectBidDocRelationService projectBidDocRelationService;

    @Autowired
    private ProjectBidDocService projectBidDocService;

    @Autowired
    private ProjectBidSectionService projectBidSectionService;

    /**
     * 根据条件分页查询列表
     *
     * @param condition 查询条件
     * @return 分页数据
     */
    @Override
    public IPage<SectionGroup> findInfoByCondition(SectionGroupCondition condition) {
        IPage<SectionGroup> page = condition.buildPage();
        QueryWrapper<SectionGroup> queryWrapper = condition.buildQueryWrapper(SectionGroup.class);
        return this.page(page, queryWrapper);
    }

    /**
     * 根据主键ID查询
     *
     * @param id 主键ID
     * @return
     */
    @Override
    public SectionGroup getInfoById(Long id) {
        return this.getById(id);
    }

    /**
     * 新增
     *
     * @param sectionGroup
     * @return 是否成功
     */
    @Override
    public Boolean addInfo(SectionGroup sectionGroup) {
        return this.save(sectionGroup);
    }

    /**
     * 修改
     *
     * @param sectionGroup
     * @return 是否成功
     */
    @Override
    public Boolean updateInfo(SectionGroup sectionGroup) {
        return this.saveOrUpdate(sectionGroup);
    }

    /**
     * 根据主键ID删除
     *
     * @param id 主键ID
     * @return 是否成功
     */
    @Override
    public Boolean deleteById(Long id) {
        return this.removeById(id);
    }

    /**
     * 根据主键ID列表批量删除
     *
     * @param idList 主键ID列表
     * @return 是否成功
     */
    @Override
    public Boolean deleteByIds(List<Long> idList) {
        return this.removeByIds(idList);
    }

    @Override
    public List<SectionGroupDTO> getByProjectId(Long projectId) {
        LambdaQueryWrapper<SectionGroup> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        //项目ID
        lambdaQueryWrapper.eq(SectionGroup::getProjectId, projectId);
        List<SectionGroup> sectionGroupList = this.getBaseMapper().selectList(lambdaQueryWrapper);
        List<SectionGroupDTO> list = BeanListUtil.convertList(sectionGroupList, SectionGroupDTO.class);
        return list;
    }

    @Override
    public GroupSectionInfoDTO querySection(Long groupId) {
        //查询该套关联的标段ID
        LambdaQueryWrapper<SectionGroup> sectionGroupWrapper = new LambdaQueryWrapper<>();
        sectionGroupWrapper.eq(SectionGroup::getId, groupId);
        SectionGroup sectionGroup = this.getBaseMapper().selectOne(sectionGroupWrapper);
        String[] sections = sectionGroup.getSectionIds().split("、");
        //查询标段ID关联的招标文件
        LambdaQueryWrapper<ProjectBidDocRelation> docRelationWrapper = new LambdaQueryWrapper<>();
        docRelationWrapper.in(ProjectBidDocRelation::getSectionId, sections);
        List<ProjectBidDocRelation> relationList = projectBidDocRelationService.list(docRelationWrapper);
        List<Long> docIds = new ArrayList<>();
        for (int i = 0; i < relationList.size(); i++) {
            docIds.add(relationList.get(i).getDocId());
        }

        List<ProjectBidDoc> docList = new ArrayList<>();
        //查询招标文件进度
        if(docIds != null && docIds.size()>0) {
            LambdaQueryWrapper<ProjectBidDoc> docWrapper = new LambdaQueryWrapper<>();
            docWrapper.in(ProjectBidDoc::getId, docIds.stream().distinct().collect(Collectors.toList()));
            docList = projectBidDocService.list(docWrapper);
        }
        Boolean flag = true;
        for (ProjectBidDoc doc : docList) {
            //所有关联的文件全部被退回或撤回的情况下可以修改本套的金额
            if (doc.getNoticeProgress().equals(NoticeProgressEnum.UNDERREVIEW.getValue())
                    || doc.getNoticeProgress().equals(NoticeProgressEnum.REVIEW.getValue())
                    || doc.getNoticeProgress().equals(NoticeProgressEnum.RELEASE.getValue())
                    || doc.getNoticeProgress().equals(NoticeProgressEnum.WITHDRAWUNDERREVIEW.getValue())
                    || doc.getNoticeProgress().equals(NoticeProgressEnum.CONFIRMING.getValue())) {
                flag = false;
                break;
            }
        }
        GroupSectionInfoDTO dto = new GroupSectionInfoDTO();
        dto.setFlag(flag);
        //查询套金额 projectBidSectionService
        LambdaQueryWrapper<ProjectBidSection> bidSectionWrapper = new LambdaQueryWrapper<>();
        bidSectionWrapper.in(ProjectBidSection::getId, sections);
        List<ProjectBidSection> bidSectionList = projectBidSectionService.list(bidSectionWrapper);
        if(bidSectionList != null && bidSectionList.size()>0) {
            dto.setAmount(bidSectionList.get(0).getTenderFee());
            if (relationList != null && relationList.size() > 0) {
                ProjectBidDoc projectBidDoc = projectBidDocService.getById(relationList.get(0).getDocId());
                dto.setPaymentType(projectBidDoc.getPaymentType());
            }
            dto.setReleaseFileType(bidSectionList.get(0).getReleaseFileType());
            dto.setReviewFileType(bidSectionList.get(0).getReviewFileType());
        }
        return dto;
    }

    /**
     * 根据标包id查询不等于当前套id的套
     * @param projectId
     * @param subId
     * @param id
     * @return
     */
    @Override
    public SectionGroup queryGroupInfoPassId(Long projectId, Long subId, Long id) {
        return this.baseMapper.queryGroupInfoPassId(projectId,subId,id);
    }
}
