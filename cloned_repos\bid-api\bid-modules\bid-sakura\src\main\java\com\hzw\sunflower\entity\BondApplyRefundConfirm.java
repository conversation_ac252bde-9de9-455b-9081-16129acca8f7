package com.hzw.sunflower.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hzw.sunflower.common.BaseBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@TableName("t_bond_apply_refund_confirm")
@ApiModel(value = "BondWater对象", description = "保证金申请退还处室处长确认表")
public class BondApplyRefundConfirm extends BaseBean implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "申请退还id")
    private Long applyRefundId;

    @ApiModelProperty(value = "处室id")
    private Long departmentId;

    @ApiModelProperty(value = "处长id")
    private Long userId;

    @ApiModelProperty(value = "处长处理状态，1.待确认，2.非本处项目，3.本处项目同意退款，4.本处项目不同意退款 5.同意退款，6.不同意退款")
    private Integer departmentDisposeStatus;

    @ApiModelProperty(value = "备注")
    private String remark;

}
