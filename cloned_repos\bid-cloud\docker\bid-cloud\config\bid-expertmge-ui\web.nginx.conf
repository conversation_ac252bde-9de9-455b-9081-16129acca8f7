server {
    listen       80;
    charset utf-8;

    location / {
        root   /usr/share/nginx/html;
        index  index.html index.htm;
        try_files $uri $uri/ /index.html;
    }

    error_page   500 502 503 504  /50x.html;
    location = /50x.html {
        root   /usr/share/nginx/html;
    }

    location /prod-api/auth/ {
        proxy_pass http://**************:28080/auth/;
        proxy_redirect off;
        proxy_set_header HOST $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        client_max_body_size 100m;
        client_body_buffer_size 1m;
        proxy_connect_timeout 270;
        proxy_send_timeout 270;
        proxy_read_timeout 270;
        proxy_buffer_size 1m;
        proxy_buffers 4 1m;
        proxy_busy_buffers_size 1m;
        proxy_temp_file_write_size 1m;
    }
    location ^~ /prod-api/system/ {
        proxy_pass http://**************:28080/system/;
        proxy_redirect off;
        proxy_set_header HOST $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        client_max_body_size 100m;
        client_body_buffer_size 1m;
        proxy_connect_timeout 270;
        proxy_send_timeout 270;
        proxy_read_timeout 270;
        proxy_buffer_size 1m;
        proxy_buffers 4 1m;
        proxy_busy_buffers_size 1m;
        proxy_temp_file_write_size 1m;
    }
    location ^~ /prod-api/resource/ {
        proxy_pass http://**************:28080/resource/;
        proxy_redirect off;
        proxy_set_header HOST $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        client_max_body_size 200m;
        client_body_buffer_size 1m;
        proxy_connect_timeout 270;
        proxy_send_timeout 270;
        proxy_read_timeout 270;
        proxy_buffer_size 1m;
        proxy_buffers 4 1m;
        proxy_busy_buffers_size 1m;
        proxy_temp_file_write_size 1m;
    }
    location /prod-api/expert/ {
        proxy_pass http://**************:28080/expert/;
        proxy_redirect off;
        proxy_set_header HOST $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        client_max_body_size 100m;
        client_body_buffer_size 1m;
        proxy_connect_timeout 270;
        proxy_send_timeout 270;
        proxy_read_timeout 270;
        proxy_buffer_size 1m;
        proxy_buffers 4 1m;
        proxy_busy_buffers_size 1m;
        proxy_temp_file_write_size 1m;
    }
    location ^~ /prod-api/externalApi {
        proxy_pass http://**************:28080/externalApi;
        proxy_redirect off;
        proxy_set_header HOST $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        client_max_body_size 100m;
        client_body_buffer_size 1m;
        proxy_connect_timeout 270;
        proxy_send_timeout 270;
        proxy_read_timeout 270;
        proxy_buffer_size 1m;
        proxy_buffers 4 1m;
        proxy_busy_buffers_size 1m;
        proxy_temp_file_write_size 1m;
    }

}



