package com.hzw.sunflower.constant.constantenum;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 编号生成规则枚举
 */
public enum PurchaseNumRuleEnum {

    SYSTEM_ABBREVIATION(1,"系统简称"),
    YEAR(2,"年份"),
    DEPARTMENT_CODE(3,"部门编码"),
//    PROVINCE_IN_OUT(4,"省外"),
    AUTO_NUMBER(5,"自增数字");

    private Integer type;
    private String desc;

    PurchaseNumRuleEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    //转换成map
    public static Map<Integer, String> toMap() {
        Map<Integer, String> map = new HashMap<Integer, String>();
        for (PurchaseNumRuleEnum purchaseNumRuleEnum : PurchaseNumRuleEnum.values()) {
            map.put(purchaseNumRuleEnum.getType(), purchaseNumRuleEnum.getDesc());
        }
        return map;
    }

    public static List toList() {
        List list = new ArrayList();
        for (PurchaseNumRuleEnum purchaseNumRuleEnum : PurchaseNumRuleEnum.values()) {
                Map<String, Object> map = new HashMap<String, Object>();
                map.put("type", purchaseNumRuleEnum.getType());
                map.put("desc", purchaseNumRuleEnum.getDesc());
                list.add(map);
        }
        return list;
    }

    /**
     * 转换成list
     * @param flag true:包含省外，false:不包含省外
     * @return
     */
    public static List toListInOut(boolean flag) {
        List list = new ArrayList();
        for (PurchaseNumRuleEnum purchaseNumRuleEnum : PurchaseNumRuleEnum.values()) {
            if (flag) {
                Map<String, Object> map = new HashMap<String, Object>();
                map.put("type", purchaseNumRuleEnum.getType());
                map.put("desc", purchaseNumRuleEnum.getDesc());
                list.add(map);
            } else {
                if (purchaseNumRuleEnum.getType() != 4) {
                    Map<String, Object> map = new HashMap<String, Object>();
                    map.put("type", purchaseNumRuleEnum.getType());
                    map.put("desc", purchaseNumRuleEnum.getDesc());
                    list.add(map);
                }
            }
        }
        return list;
    }
}
