package com.hzw.sunflower.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.controller.project.request.ApprovalApplyReq;
import com.hzw.sunflower.controller.project.request.ProjectTurnEntrustREQ;
import com.hzw.sunflower.controller.project.response.ProjectTurnEntrustVo;
import com.hzw.sunflower.entity.ProjectTurnEntrust;
import com.hzw.sunflower.entity.condition.ProjectTurnEntrustCondition;
import org.springframework.transaction.annotation.Transactional;


/**
 * 项目转委托人表Service接口
 *
 * <AUTHOR>
 * @version 1.0.0 2021-04-14
 */
public interface ProjectTurnEntrustService extends IService<ProjectTurnEntrust> {

    /**
     * 新增转委托信息
     *
     * @param projectTurnEntrustREQ
     * @return
     * @throws Exception
     */
    @Transactional(rollbackFor = Exception.class)
    Boolean saveTurnEntrust(ProjectTurnEntrustREQ projectTurnEntrustREQ);

    /**
     * 转委托审批
     * @param req
     * @param entrust
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    Boolean insertApprovalRecordAgent(ApprovalApplyReq req, ProjectTurnEntrust entrust);

    /**
     * 撤回转委托
     * @param id
     * @return
     */
    Boolean withdrawTurnEntrust(Long id);

    /**
     * 获取转委托处理列表
     * @param condition
     * @return
     */
    Result<Object> listWorkBench(ProjectTurnEntrustCondition condition);

    /**
     * 转委托列表
     * @param condition
     * @return
     */
    Result<IPage<ProjectTurnEntrustVo>> listTurnEntrust(ProjectTurnEntrustCondition condition);

    /**
     * 获取转委托处理列表数量
     * @return
     * @param condition
     */
    Result<Integer> listWorkBenchCount(ProjectTurnEntrustCondition condition);
}