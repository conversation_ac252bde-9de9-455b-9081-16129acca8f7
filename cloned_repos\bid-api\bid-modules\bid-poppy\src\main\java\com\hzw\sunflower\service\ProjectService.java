package com.hzw.sunflower.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hzw.sunflower.controller.project.request.ProjectAcceptanceRep;
import com.hzw.sunflower.controller.project.response.ProjectAcceptanceListVo;
import com.hzw.sunflower.controller.project.response.ProjectBidSectionVO;
import com.hzw.sunflower.dto.ProjectSectionGroupDTO;
import com.hzw.sunflower.entity.Project;
import com.hzw.sunflower.entity.ProjectBidSection;

import java.util.List;

/**
 * 项目表 Service接口
 *
 * <AUTHOR>
 * @version 1.0.0 2021-04-07
 */
public interface ProjectService extends IService<Project> {

    /**
     * 根据主键ID查询项目表 信息
     *
     * @param id 主键ID
     * @return 项目表 信息
     */
    Project getProjectById(Long id);


    /**
     * 根据主键ID删除项目表 信息
     *
     * @param id 主键ID
     * @return 项目表 信息
     */
     Boolean deleteProjectById(Long id);


    /**
     * 修改项目表 信息
     *
     * @param project 项目表 信息
     * @return 是否成功
     */
    Boolean updateProject(Project project);

    /**
     * 项目转委托人(代理机构)-项目冗余字段处理
     *
     * @param id 主键ID
     * @return 是否成功
     */
    Boolean updateAgentProjectRedundantFieldDealWith(Long id);

    /**
     * 项目转委托人(招标人)-项目冗余字段处理
     *
     * @param id 主键ID
     * @return 是否成功
     */
    Boolean updateTendererProjectRedundantFieldDealWith(Long id);

    /**
     * 项目新建修改-项目冗余字段处理
     *
     * @param id 主键ID
     * @return 是否成功
     */
    Boolean saveOrUpdateProjectRedundantFieldDealWith(Long id,Boolean isNewAddOrChangeEntrust);

    /**
     * 根据标段集合删除关注供应商关注标段信息
     * @param subList
     * @return
     */
    Integer deleteApplyInfoBySubId(List<Long> subList);

    /**
     * 删除招标公告、招标文件提交的信息
     * @param section
     */
    void deleteProjectBidDoc(ProjectBidSection section);

    /**
     * 删除招标公告、招标文件提交的信息
     * @param section
     */
    void deleteProjectBidNotice(ProjectBidSection section);
    /**
     * 根据项目分摊情况查询项目id
     * @param isShare
     * @param ids
     * @return
     */
    List<Long> findShareProject(Integer isShare,List<Long> ids);


    /**
     * 删除套信息
     * @param projectId
     * @param sectionId
     */
    void deleteSectionGroup(Long projectId, Long sectionId);

    /**
     * //根据项目id、标段id查询套信息
     * @param projectId
     * @param sectionId
     * @return
     */
    List<ProjectSectionGroupDTO> getSectionGroup(Long projectId, Long sectionId);

    /**
     * 获取最大的临时国际表编号
     * @return
     */
    Integer getMaxTempInternational();

    /**
     * 获取标段信息以及招标文件
     * @param projectId
     * @return
     */
    List<ProjectBidSectionVO> listDoc(Long projectId);


    /**
     * 推送项目到ncc
     * @param projectId
     */
    void pushProjectNcc(Long projectId,Integer packageNumber);


    /**
     * 根据项目查询租户用户id
     * @param projectId
     * @return
     */
    Long queryTenantInfoByProject(Long projectId);

    Project getProjectBySectionId(Long sectionId);
}