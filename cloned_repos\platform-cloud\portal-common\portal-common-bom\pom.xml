<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.hzw</groupId>
    <artifactId>portal-common-bom</artifactId>
    <version>1.0.0</version>
    <packaging>pom</packaging>

    <description>
        portal-common-bom common依赖项
    </description>

    <dependencyManagement>
        <dependencies>
            <!-- 核心模块 -->
            <dependency>
                <groupId>com.hzw</groupId>
                <artifactId>portal-common-core</artifactId>
                <version>${project.version}</version>
            </dependency>

            <!-- 接口模块 -->
            <dependency>
                <groupId>com.hzw</groupId>
                <artifactId>portal-common-doc</artifactId>
                <version>${project.version}</version>
            </dependency>

            <!-- 安全模块 -->
            <dependency>
                <groupId>com.hzw</groupId>
                <artifactId>portal-common-security</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.hzw</groupId>
                <artifactId>portal-common-satoken</artifactId>
                <version>${project.version}</version>
            </dependency>

            <!-- 日志记录 -->
            <dependency>
                <groupId>com.hzw</groupId>
                <artifactId>portal-common-log</artifactId>
                <version>${project.version}</version>
            </dependency>

            <!-- 字典 -->
            <dependency>
                <groupId>com.hzw</groupId>
                <artifactId>portal-common-dict</artifactId>
                <version>${project.version}</version>
            </dependency>

            <!-- excel -->
            <dependency>
                <groupId>com.hzw</groupId>
                <artifactId>portal-common-excel</artifactId>
                <version>${project.version}</version>
            </dependency>

            <!-- basebean -->
            <dependency>
                <groupId>com.hzw</groupId>
                <artifactId>portal-common-basebean</artifactId>
                <version>${project.version}</version>
            </dependency>

            <!-- 缓存服务 -->
            <dependency>
                <groupId>com.hzw</groupId>
                <artifactId>portal-common-redis</artifactId>
                <version>${project.version}</version>
            </dependency>

            <!-- web服务 -->
            <dependency>
                <groupId>com.hzw</groupId>
                <artifactId>portal-common-web</artifactId>
                <version>${project.version}</version>
            </dependency>

            <!-- 数据库服务 -->
            <dependency>
                <groupId>com.hzw</groupId>
                <artifactId>portal-common-mybatis</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.hzw</groupId>
                <artifactId>portal-common-job</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.hzw</groupId>
                <artifactId>portal-common-dubbo</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.hzw</groupId>
                <artifactId>portal-common-seata</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.hzw</groupId>
                <artifactId>portal-common-loadbalancer</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.hzw</groupId>
                <artifactId>portal-common-oss</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.hzw</groupId>
                <artifactId>portal-common-idempotent</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.hzw</groupId>
                <artifactId>portal-common-mail</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.hzw</groupId>
                <artifactId>portal-common-sms</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.hzw</groupId>
                <artifactId>portal-common-logstash</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.hzw</groupId>
                <artifactId>portal-common-elasticsearch</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.hzw</groupId>
                <artifactId>portal-common-sentinel</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.hzw</groupId>
                <artifactId>portal-common-skylog</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.hzw</groupId>
                <artifactId>portal-common-prometheus</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.hzw</groupId>
                <artifactId>portal-common-translation</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.hzw</groupId>
                <artifactId>portal-common-encrypt</artifactId>
                <version>${project.version}</version>
            </dependency>

        </dependencies>
    </dependencyManagement>
    <distributionManagement>
        <!-- 定义releases库的坐标 -->
        <repository>
            <id>rdc-releases</id>
            <url>https://packages.aliyun.com/maven/repository/2260816-release-35QHzL/</url>
        </repository>
        <!-- 定义snapshots库 -->
        <snapshotRepository>
            <id>rdc-snapshots</id>
            <url>https://packages.aliyun.com/maven/repository/2260816-snapshot-GzzKEy/</url>
        </snapshotRepository>
    </distributionManagement>
</project>
