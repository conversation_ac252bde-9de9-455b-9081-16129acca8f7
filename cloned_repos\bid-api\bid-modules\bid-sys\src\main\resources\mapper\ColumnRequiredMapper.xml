<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hzw.sunflower.dao.ColumnRequiredMapper">

    <select id="queryModuleName" resultType="com.hzw.sunflower.entity.ColumnRequired">
        select distinct  module_name,module_code  from s_column_required where is_delete = 0
    </select>
</mapper>