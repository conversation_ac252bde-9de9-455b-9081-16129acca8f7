package com.hzw.sunflower.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.constant.CommonConstants;
import com.hzw.sunflower.constant.FormatConstants;
import com.hzw.sunflower.constant.MessageConstants;
import com.hzw.sunflower.constant.constantenum.*;
import com.hzw.sunflower.controller.project.request.*;
import com.hzw.sunflower.controller.project.response.ProjectBidSectionVO;
import com.hzw.sunflower.controller.project.response.ProjectSectionPurchaseVo;
import com.hzw.sunflower.dao.ProjectBidSectionMapper;
import com.hzw.sunflower.dao.ProjectBidSectionRecordMapper;
import com.hzw.sunflower.dao.ProjectMapper;
import com.hzw.sunflower.dao.SupplierInfoMapper;
import com.hzw.sunflower.dto.ProjectBidSectionDTO;
import com.hzw.sunflower.dto.ProjectShareInfoDto;
import com.hzw.sunflower.dto.SectionInfoDTO;
import com.hzw.sunflower.dto.SupplierInfoDTO;
import com.hzw.sunflower.entity.*;
import com.hzw.sunflower.exception.ExceptionEnum;
import com.hzw.sunflower.exception.SunFlowerException;
import com.hzw.sunflower.service.*;
import com.hzw.sunflower.util.BeanListUtil;
import com.hzw.sunflower.util.CurrencyUtils;
import com.hzw.sunflower.util.spring.SpringUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 项目标段表 Service接口实现
 *
 * <AUTHOR>
 * @version 1.0.0 2021-04-07
 */
@Service
public class ProjectBidSectionServiceImpl extends ServiceImpl<ProjectBidSectionMapper, ProjectBidSection>
        implements ProjectBidSectionService {

    @Autowired
    private ProjectMapper projectMapper;

    @Autowired
    private ProjectBidSectionMapper projectBidSectionMapper;

    @Autowired
    private ProjectBidSectionRecordMapper projectBidSectionRecordMapper;

    @Autowired
    private ProjectService projectService;

    @Autowired
    private SupplierInfoMapper supplierInfoMapper;

    @Autowired
    private DictionaryService dictionaryService;

    @Autowired
    private CommonProjectRelevantService projectRelevantService;

//    @Autowired
//    private ApplyInfoService applyInfoService;

//    @Autowired
//    private RabbitMqService rabbitMqService;
    @Autowired
    private CommonApplyInfoService commonApplyInfoService;

    @Autowired
    private CommonSectionService commonSectionService;

    @Resource
    private ISectionExpertSpecialtyService iSectionExpertSpecialtyService;




    /**
     * 根据项目ID查询项目标段表信息
     *
     * @param projectId 项目ID
     * @return
     */
    @Override
    public List<ProjectBidSectionDTO> getProjectBidSectionByProjectId(Long projectId) {
        LambdaQueryWrapper<ProjectBidSection> lambdaQueryWrapper = new LambdaQueryWrapper<ProjectBidSection>();
        //项目ID
        lambdaQueryWrapper.eq(ProjectBidSection::getProjectId, projectId);
        lambdaQueryWrapper.orderByAsc(ProjectBidSection::getPackageNumber);
        List<ProjectBidSection> projectBidSectionList = projectBidSectionMapper.selectList(lambdaQueryWrapper);
        List<ProjectBidSectionDTO> list=new ArrayList<>();
        for (ProjectBidSection projectBidSection : projectBidSectionList) {
            List<SectionExpertSpecialty> listSp = iSectionExpertSpecialtyService.list(new LambdaQueryWrapper<SectionExpertSpecialty>()
                    .eq(SectionExpertSpecialty::getSectionId, projectBidSection.getId()));
            List<Long> spids = listSp.stream()
                    .map(m -> {
                        return m.getExpertSpecialtyId();
                    }).collect(Collectors.toList());
            projectBidSection.setSectionExpertSpecialtyList(spids);
            ProjectBidSectionDTO projectBidSectionDTO=new ProjectBidSectionDTO();
            BeanUtils.copyProperties(projectBidSection,projectBidSectionDTO);
            projectBidSectionDTO.setSectionExpertSpecialtyList(spids);
            list.add(projectBidSectionDTO);
        }
        return list;
    }

    /**
     * 修改项目标段表 信息
     *
     * @param projectBidSection 项目标段表 信息
     * @return 是否成功
     */
    @Override
    public Boolean updateProjectBidSection(ProjectBidSection projectBidSection) {
        Project project = projectService.getProjectById(projectBidSection.getProjectId());
        BigDecimal projectAmount = project.getEntrustedAmount();
        //除去当前标段外的委托总金额
        String strAmount = projectBidSectionMapper.queryAmountByProjectIdExceptBidId(projectBidSection, "1");
        if (StrUtil.isBlank(strAmount)) {
            strAmount = FormatConstants.ENTRUST_MONEY;
        }
        BigDecimal bidTotalAmount = new BigDecimal(strAmount);
        //加上当前标段最新委托金额
        bidTotalAmount = projectBidSection.getEntrustMoney().add(bidTotalAmount);
        // 项目委托金额为空 或者 标段金额总和大于项目委托金额时，修改项目委托金额
        if (null == projectAmount || -1 == projectAmount.compareTo(bidTotalAmount)) {
            project.setEntrustedAmount(bidTotalAmount);
            project.setId(project.getId());
            projectMapper.updateById(project);
        }
        return this.updateById(projectBidSection);
    }


    /**
     * 根据主键ID列表批量删除项目标段表
     *
     * @param idList 主键ID列表
     * @return 是否成功
     */
    @Override
    public Boolean deleteProjectBidSectionByIds(List<Long> idList) {
        //项目ID
        Long projectId = this.getById(idList.get(0)).getProjectId();
        //删除套里的标段信息
        idList.forEach(s->{
            projectService.deleteSectionGroup(projectId,s);
        });
        boolean flag = this.removeByIds(idList);
        // 调用冗余字段处理接口
        if (flag) {
            // 更新标段子表删除状态
            QueryWrapper<ProjectBidSectionRecord> qpsr = new  QueryWrapper<>();
            qpsr.lambda().in(ProjectBidSectionRecord :: getSectionId,idList);
            projectBidSectionRecordMapper.delete(qpsr);
            // 调用冗余字段处理接口
            flag = commonSectionService.saveOrUpdateProjectPackageFieldDealWith(projectId);
            //删除标段关联的供应商关注信息
            projectService.deleteApplyInfoBySubId(idList);
       }
        return flag;
    }

    /**
     * 新增多个项目标段信息
     *
     * @param projectBidReq 项目标段表 信息
     * @return
     */
    @Override
    public Boolean addProjectBidSections(ProjectBidReq projectBidReq) {
        // 保存或修改成功标识
        Boolean saveRows = false;
        List<ProjectBidSection> updateForRecords = new ArrayList<>();
        // 无包件项目
        if (SegmentEnum.NO_BAG.getType().equals(projectBidReq.getPackageSegmentStatus())) {
            Project project = projectMapper.selectById(projectBidReq.getProjectId());
            if (SegmentEnum.HAVE_BAG.getType().equals(project.getPackageSegmentStatus())) {
                //有包件修改为无包件时, 保存第一包数据信息
                LambdaUpdateWrapper<ProjectBidSection> queryWrapper = new LambdaUpdateWrapper<>();
                queryWrapper.eq(ProjectBidSection::getProjectId, projectBidReq.getProjectId());
                queryWrapper.orderByAsc(ProjectBidSection::getId);
                List<ProjectBidSection> sectionList = projectBidSectionMapper.selectList(queryWrapper);
                List<Long> deleteIds = new ArrayList<>();
                for (int i = 1; i < sectionList.size(); i++) {
                    deleteIds.add(sectionList.get(i).getId());
                }
                //删除第一包以外的包
                if (CollectionUtil.isNotEmpty(deleteIds)) {
                    LambdaUpdateWrapper<ProjectBidSection> deleteWrapper = new LambdaUpdateWrapper<>();
                    deleteWrapper.in(ProjectBidSection::getId, deleteIds);
                    projectBidSectionMapper.delete(deleteWrapper);
                }
                ProjectBidSection projectBidSection = sectionList.get(0);
                List<ProjectBidSection> sections = new ArrayList<>();
                sections.add(projectBidSection);
                saveRows = commonSectionService.updateSectionStatus(sections,"addSection");
            } else {
                // 保存标段信息
                ProjectBidSection projectBidSection = new ProjectBidSection();
                projectBidSection.setProjectId(project.getId());
                if (null != project.getEntrustedAmount()) {
                    projectBidSection.setEntrustMoney(project.getEntrustedAmount());
                }
                if (null != project.getEntrustedCurrency()) {
                    projectBidSection.setEntrustCurrency(project.getEntrustedCurrency().longValue());
                }
                saveRows = commonSectionService.updateSectionStatus(projectBidSection);

                updateForRecords.add(projectBidSection);
               // saveRows = save(projectBidSection);
            }
        } else { // 有包件项目
            // 保存标段信息
            List<ProjectBidSection> projectBidSectionList = projectBidReq.getProjectBidSectionList();
            //同时新增标段/包，防止重复包段编号
            ProjectBidSection projectBidSection = this.getOne(new LambdaQueryWrapper<ProjectBidSection>().eq(ProjectBidSection::getProjectId, projectBidReq.getProjectId()).orderByDesc(ProjectBidSection::getPackageNumber).last("limit 1"));
            if (projectBidSection != null && projectBidSection.getPackageNumber() != null) {
                if (projectBidSectionList.get(0).getPackageNumber() <= projectBidSection.getPackageNumber()) {
                    for (int i = 0;i < projectBidSectionList.size();i++) {
                        if (i < 1) {
                            projectBidSectionList.get(i).setPackageNumber(projectBidSection.getPackageNumber()+1);
                        } else {
                            projectBidSectionList.get(i).setPackageNumber(projectBidSectionList.get(i-1).getPackageNumber()+1);
                        }
                    }
                }
            }
            saveRows = commonSectionService.updateSectionStatus(projectBidSectionList);
            updateForRecords = BeanListUtil.convertList(projectBidSectionList,ProjectBidSection.class);
            //saveRows = saveOrUpdateBatch(projectBidSectionList);

            //推送项目标段包到ncc
            projectService.pushProjectNcc(projectBidReq.getProjectId(),projectBidSectionList.get(0).getPackageNumber());
        }

        //归档切换标段包,标段记录表第一轮包名包号为空的问题
        updateForRecords.forEach(p->{
            ProjectBidSectionRecord record = new ProjectBidSectionRecord();
            record.setPackageName(p.getPackageName());
            record.setPackageNumber(p.getPackageNumber());
            LambdaQueryWrapper<ProjectBidSectionRecord> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(ProjectBidSectionRecord::getProjectId,p.getProjectId());
            queryWrapper.eq(ProjectBidSectionRecord::getSectionId,p.getId());
            projectBidSectionRecordMapper.update(record,queryWrapper);
        });

//        if (saveRows) {
            // 更新项目包件状态
            Project project = new Project();
            project.setId(projectBidReq.getProjectId());
            project.setPackageSegmentStatus(projectBidReq.getPackageSegmentStatus());
            project.setBidTypeName(projectBidReq.getBidTypeName());

           projectMapper.updateById(project);
//            if (0 == updateRow) {
//                saveRows = false;
//            }
//        }
        return saveRows;
    }

    @Override
    public String judgeEntrustMoney(List<ProjectBidSection> projectBidSectionList) {
        if (!CollectionUtils.isEmpty(projectBidSectionList) && null != projectBidSectionList.get(0)) {
            // 获取项目委托金额 转换成人名币进行对比
            Project project = projectService.getProjectById(projectBidSectionList.get(0).getProjectId());
            List<Dictionary> dictionaryList = dictionaryService.selectDictionaryForCurrency();
            BigDecimal projectAmount = project.getEntrustedAmount();
            // 标段委托金额
            BigDecimal bidTotalAmount = new BigDecimal(FormatConstants.ENTRUST_MONEY);
            // 项目委托金额为空时，不做判断
            if (null != projectAmount) {
                projectAmount = projectAmount.multiply(CurrencyUtils.getCurrency(dictionaryList, Long.parseLong(project.getEntrustedCurrency()+"")));
                // 无包件项目
                if (SegmentEnum.NO_BAG.getType().equals(project.getPackageSegmentStatus())) {
                    bidTotalAmount = projectBidSectionList.get(0).getEntrustMoney().multiply(CurrencyUtils.getCurrency(dictionaryList, projectBidSectionList.get(0).getEntrustCurrency()));
                    // 超出项目委托金额
                    if (-1 == projectAmount.compareTo(bidTotalAmount)) {
                        return MessageConstants.EXCEED_ENTRUST_MONEY;
                    }
                } else {
                    // 所有标段委托金额总和
                    bidTotalAmount = getBidTotalAmount(dictionaryList,projectBidSectionList);
                    // 超出项目委托金额
                    if (-1 == projectAmount.compareTo(bidTotalAmount)) {
                        return MessageConstants.EXCEED_ENTRUST_MONEY;
                    }
                }
            }
            return MessageConstants.NOT_EXCEED_ENTRUST_MONEY;
        } else {
            return MessageConstants.PROJECT_BID_SECTION_NULL;
        }
    }

    /**
     * 项目包件新建修改-项目冗余字段处理
     *
     * @param projectId 主键ID
     * @return 是否成功
     */
    @Override
    public String queryProjectStatusByProjectId(Long projectId) {
        return this.baseMapper.queryProjectStatusById(projectId);
    }

    @Override
    public Result<Object> updateProjectAndSectionInfo(ProjectBidReq projectBidReq) {
        Result result=Result.ok();
        if (null == projectBidReq.getProjectId() || CollectionUtil.isEmpty(projectBidReq.getProjectBidSectionList())) {
            return Result.failed();
        }
        // 项目判断
        Project oldProjectInfo = projectService.getProjectById(projectBidReq.getProjectId());
        if (null == oldProjectInfo) {
            return Result.failed("项目不存在");
        }


        // 有包修改为无包,删除包件
        if (SegmentEnum.HAVE_BAG.getType().equals(oldProjectInfo.getPackageSegmentStatus()) &&
                SegmentEnum.NO_BAG.getType().equals(projectBidReq.getPackageSegmentStatus())) {
            LambdaUpdateWrapper<ProjectBidSection> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(ProjectBidSection::getProjectId, projectBidReq.getProjectId());
            projectBidSectionMapper.delete(updateWrapper);
        }

        // 标段信息
        ProjectBidSection reqBidSection = projectBidReq.getProjectBidSectionList().get(0);
        // 原标段信息
        SectionInfoDTO sectionById = projectRelevantService.getSectionById(reqBidSection.getId());
        if (projectBidReq.getReTender()) {
            //重新招标保存报名情况
//            saveSupplierApplyInfo(projectBidReq, projectBidSection);
            Integer bidRound=PurchaseStatusEnum.PRE_TRIAL.getType().equals(reqBidSection.getPurchaseStatus()) ? 1 : 2;
            commonApplyInfoService.saveReProjectSupplierApplyInfo(reqBidSection.getOldBidderType(),reqBidSection.getProjectId(),reqBidSection.getId(),projectBidReq.getApplyIds(),bidRound);
        }

        Project project = projectService.getProjectById(projectBidReq.getProjectId());
        List<Dictionary> dictionaryList = dictionaryService.selectDictionaryForCurrency();
        // 项目币种汇率
        BigDecimal projectCurrency = CurrencyUtils.getCurrency(dictionaryList, Long.parseLong(project.getEntrustedCurrency() + ""));
        //委托金额不为空
        if (reqBidSection.getEntrustMoney() != null) {
            //除去当前标段外的委托总金额， 换算成项目汇率
            String strAmount = projectBidSectionMapper.queryAmountByProjectIdExceptBidId(reqBidSection, projectCurrency.toString());
            if (StrUtil.isBlank(strAmount)) {
                strAmount = FormatConstants.ENTRUST_MONEY;
            }
            BigDecimal bidTotalAmount = new BigDecimal(strAmount);

            // 当前标段换算成人名币，再换算成项目对应汇率金额
            BigDecimal sectionCny = reqBidSection.getEntrustMoney().multiply(CurrencyUtils.getCurrency(dictionaryList, reqBidSection.getEntrustCurrency()));
            BigDecimal sectionAmount = sectionCny.divide(projectCurrency, 2, RoundingMode.HALF_UP);

            //加上当前标段最新委托金额
            bidTotalAmount = bidTotalAmount.add(sectionAmount);

            // 同步修改委托金额 add by fanqh on 2022-05-31
            if (SegmentEnum.NO_BAG.getType().equals(project.getPackageSegmentStatus())) {
                // 无包直接同步币种+金额
                project.setEntrustedAmount(reqBidSection.getEntrustMoney());
                project.setEntrustedCurrency(reqBidSection.getEntrustCurrency().intValue());
            } else {
                project.setEntrustedAmount(bidTotalAmount);
            }
        }

        // 修改是否按项目 采购方式
        if (StringUtils.isNotEmpty(projectBidReq.getPreQualificationMtd())){
            project.setPreQualificationMtd(projectBidReq.getPreQualificationMtd());
        }
        project.setPackageSegmentStatus(projectBidReq.getPackageSegmentStatus());
        project.setBidTypeName(projectBidReq.getBidTypeName());
        project.setId(project.getId());
        int i = projectMapper.updateById(project);
        boolean flag = false;
        if (i == 1) {
            flag = true;
        }
        if (flag) {
            //提交
            // 划分标段包时，数据更新
            if (OperationTypeEnum.SUBMIT.getType().equals(projectBidReq.getOperationType())) {
                if (null != reqBidSection.getId()) {
                    ProjectBidSection oldSection = projectBidSectionMapper.selectById(reqBidSection.getId());
                    //设置BidSectionCode
//                    projectBidSection.setBidSectionCode(oldSection.getBidSectionCode() != null ? oldSection.getBidSectionCode() : BidSectionCodeEnum.bidding_announcement_no.getCode());
                    //采购方式由资格预审修改为其他，或者其他采购方式修改为资格预审 删除相关公告或文件
                    if ((PurchaseStatusEnum.PRE_TRIAL.getType().equals(oldSection.getPurchaseStatus()) && !reqBidSection.getPurchaseStatus().equals(oldSection.getPurchaseStatus()))
                            || (PurchaseStatusEnum.PRE_TRIAL.getType().equals(reqBidSection.getPurchaseStatus()) && !reqBidSection.getPurchaseStatus().equals(oldSection.getPurchaseStatus()))) {
                        deleteNoticeAndDoc(reqBidSection.getProjectId(), reqBidSection.getId());
                        //清空材料清单数据
                        reqBidSection.setMaterialList(null);
                    }

                    if (reqBidSection.getPurchaseType()!=null){
                        // 采购方式选择为政府采购-XXX或国际招标
//                        if( BidPurchaseModeEnum.getProjectModeCode(reqBidSection.getPurchaseType()+"").equals("GOVERNMENT_PROCUREMENT") ||
//                            BidPurchaseModeEnum.getProjectModeCode(reqBidSection.getPurchaseType()+"").equals("INTERNATIONAL_BIDDING") ){
                        if( reqBidSection.getPurchaseType().toString().equals(BidPurchaseModeEnum.GOVERNMENT_PROCUREMENT.getType() ) ||
                            reqBidSection.getPurchaseType().toString().equals(BidPurchaseModeEnum.INTERNATIONAL_BIDDING.getType()) ){
                            // 若有一个包发布过公告，则不允许修改所有包的采购方式

                            // 若有一个包的采购方式选择为政府采购-XXX或国际招标 所有标段包的采购方式均选择相同选项，
                            QueryWrapper<ProjectBidSection> updateWrapper =new QueryWrapper<>();
                            updateWrapper.lambda().eq(ProjectBidSection::getProjectId,reqBidSection.getProjectId());
                            updateWrapper.lambda().eq(ProjectBidSection::getPurchaseType,reqBidSection.getPurchaseType());
                            updateWrapper.lambda().eq(ProjectBidSection::getPurchaseMode,reqBidSection.getPurchaseMode());
                            List<ProjectBidSection> projectBidSections = projectBidSectionMapper.selectList(updateWrapper);
                            projectBidSections.forEach(p->{
                                p.setPurchaseStatus(reqBidSection.getPurchaseStatus());
                                p.setPurchaseModeName(reqBidSection.getPurchaseModeName());
                            });
                            commonSectionService.updateSectionStatus(projectBidSections);
                            /*updateWrapper.lambda().set(ProjectBidSection::getPurchaseStatus,reqBidSection.getPurchaseStatus());
                            updateWrapper.lambda().set(ProjectBidSection::getPurchaseModeName,reqBidSection.getPurchaseModeName());
                            projectBidSectionMapper.update(null,updateWrapper);*/
                        }else{

                        }
                        // 资格预审采购方式： 统一修改相同采购方式的标段是否按项目
                        if (StringUtils.isNotEmpty(projectBidReq.getPreQualificationMtd())){
                            reqBidSection.setPreQualificationMtd(projectBidReq.getPreQualificationMtd());
                            reqBidSection.setCanSearch(projectBidReq.getProjectBidSectionList().get(0).getCanSearch());

                            QueryWrapper<ProjectBidSection> updateWrapper =new QueryWrapper<>();
                            updateWrapper.lambda().eq(ProjectBidSection::getProjectId,reqBidSection.getProjectId());
                            updateWrapper.lambda().eq(ProjectBidSection::getPurchaseMode,reqBidSection.getPurchaseMode());
                            updateWrapper.lambda().eq(ProjectBidSection::getPurchaseStatus,PurchaseStatusEnum.PRE_TRIAL.getType());
                            List<ProjectBidSection> projectBidSections = projectBidSectionMapper.selectList(updateWrapper);
                            projectBidSections.forEach(p->{
                                p.setPreQualificationMtd(projectBidReq.getPreQualificationMtd());
                                if(PreQualificationEnum.PROJECT.getType().toString().equals(projectBidReq.getPreQualificationMtd())) {
                                    p.setCanSearch(projectBidReq.getProjectBidSectionList().get(0).getCanSearch());
                                }
                            });
                            commonSectionService.updateSectionStatus(projectBidSections);
                            //pdateWrapper.lambda().notIn(ProjectBidSection::getPreQualificationMtd,projectBidReq.getPreQualificationMtd());
                           /* updateWrapper.lambda().set(ProjectBidSection::getPreQualificationMtd,projectBidReq.getPreQualificationMtd());
                            if(PreQualificationEnum.PROJECT.getType().toString().equals(projectBidReq.getPreQualificationMtd())){
                                updateWrapper.lambda().set(ProjectBidSection::getCanSearch,projectBidReq.getProjectBidSectionList().get(0).getCanSearch());
                                reqBidSection.setCanSearch(projectBidReq.getProjectBidSectionList().get(0).getCanSearch());
                            }
                            projectBidSectionMapper.update(null,updateWrapper);*/
                        }
                    }

                    // 全流程线下+归档
                        if (project.getOperationFlow().intValue()==ProjectOperationFlowEnum.Flow3.getValue().intValue()){
                           // reqBidSection.setStatus(PackageStatusEnum.SUBMITTING.getValue()+"");
                            // 状态改为30
                            if(Integer.valueOf(reqBidSection.getStatus())<PackageStatusEnum.OPENING.getValue()){
                                reqBidSection.setStatus(PackageStatusEnum.OPENING.getValue()+"");
                            }
                            reqBidSection.setBidRound(BidRoundEnum.HS.getType());
                            QueryWrapper<ProjectBidSection> updateWrapper =new QueryWrapper<>();
                            updateWrapper.lambda().eq(ProjectBidSection::getProjectId,reqBidSection.getProjectId());
                            updateWrapper.lambda().eq(ProjectBidSection::getPurchaseMode,reqBidSection.getPurchaseMode());
                            updateWrapper.lambda().eq(ProjectBidSection::getPurchaseStatus,reqBidSection.getPurchaseStatus());
                            updateWrapper.lambda().eq(ProjectBidSection::getId,reqBidSection.getId());
                            List<ProjectBidSection> projectBidSections = projectBidSectionMapper.selectList(updateWrapper);
                            projectBidSections.forEach(p->{
                                p.setStatus(reqBidSection.getStatus());
                                p.setBidRound(reqBidSection.getBidRound());
                                p.setSubmitEndTimeType(EndTimeTypeEnum.CLEAR_TIME.getType());
                                p.setSubmitEndTime(projectBidReq.getSubmitEndTime());
                            });
                            commonSectionService.updateSectionStatus(projectBidSections);
                           /* updateWrapper.lambda().set(ProjectBidSection::getStatus,reqBidSection.getStatus());
                            updateWrapper.lambda().set(ProjectBidSection::getBidRound,reqBidSection.getBidRound());
                            projectBidSectionMapper.update(null,updateWrapper);*/
                        }

                } else {
                    //初始化BidSectionCode 220
//                    projectBidSection.setBidSectionCode(BidSectionCodeEnum.bidding_announcement_no.getCode());
                }
            }

            reqBidSection.setVersion(null);
            //修改标段信息
            flag = commonSectionService.updateSectionStatus(reqBidSection);
            // 标的物信息保存
            iSectionExpertSpecialtyService.remove(new LambdaQueryWrapper<SectionExpertSpecialty>()
                    .eq(SectionExpertSpecialty::getSectionId,reqBidSection.getId()));

            List<SectionExpertSpecialty> addexpertspeclist=new ArrayList<>();
            for (Long aLong : reqBidSection.getSectionExpertSpecialtyList()) {
                SectionExpertSpecialty record=new SectionExpertSpecialty();
                record.setSectionId(reqBidSection.getId());
                record.setExpertSpecialtyId(aLong);
                addexpertspeclist.add(record);
            }
            iSectionExpertSpecialtyService.saveBatch(addexpertspeclist);
            //
           // flag = this.saveOrUpdate(projectBidSection);
        }
        if (!flag) {
            throw new SunFlowerException(ExceptionEnum.BID_UPDATE_ERROR, ExceptionEnum.BID_UPDATE_ERROR.getMessage());
        }
        if (flag) {
            //提交，有包项目，资格预审，按项目
            if (OperationTypeEnum.SUBMIT.getType().equals(projectBidReq.getOperationType())
                    && SegmentEnum.HAVE_BAG.getType().equals(projectBidReq.getPackageSegmentStatus())
                    && PurchaseStatusEnum.PRE_TRIAL.getType().equals(reqBidSection.getPurchaseStatus())
                    && PreQualificationEnum.PROJECT.getType().toString().equals(projectBidReq.getPreQualificationMtd())
                    && BidRoundEnum.ZGYS.getType().equals(reqBidSection.getBidRound())) {
                perfectPreQualificationInfo(projectBidReq.getProjectId(), reqBidSection);
                //统一同步标段状态
                changeBidStatus(projectBidReq.getProjectId(), reqBidSection);
            }
        }

        //推送项目到ncc
        projectService.pushProjectNcc(project.getId(),reqBidSection.getPackageNumber());

        //如果采购方式没变，标段由按包切到按项目，删除招标文件
        if(OperationTypeEnum.SUBMIT.getType().equals(projectBidReq.getOperationType()) && reqBidSection.getPurchaseMode().equals(sectionById.getPurchaseMode())
            && reqBidSection.getPurchaseStatus().equals(sectionById.getPurchaseStatus())
            && reqBidSection.getPurchaseType().equals(sectionById.getPurchaseType())
            && sectionById.getPreQualificationMtd()!=null
            && PreQualificationEnum.SECTION.getType().toString().equals(sectionById.getPreQualificationMtd())
            &&reqBidSection.getPreQualificationMtd().equals(PreQualificationEnum.PROJECT.getType().toString())){
            reqBidSection.setProjectId(project.getId());
            projectService.deleteProjectBidDoc(BeanListUtil.convert(sectionById,ProjectBidSection.class));
            projectService.deleteProjectBidNotice(BeanListUtil.convert(sectionById,ProjectBidSection.class));
        }
        return result;
    }

    /**
     * 统一同步标段状态
     *
     * @param projectId
     * @param bidSection
     */
    private void changeBidStatus(Long projectId, ProjectBidSection bidSection) {
        Long sectionId = bidSection.getId();
        //查询资格预审、非异常状态 标段
        LambdaQueryWrapper<ProjectBidSection> bidQueryWrapper = new LambdaQueryWrapper<>();
        bidQueryWrapper.eq(ProjectBidSection::getProjectId, projectId);
        bidQueryWrapper.eq(ProjectBidSection::getPurchaseStatus, PurchaseStatusEnum.PRE_TRIAL.getType());
        bidQueryWrapper.eq(ProjectBidSection::getPurchaseMode, bidSection.getPurchaseMode());
        //bidQueryWrapper.eq(ProjectBidSection::getBidRound, BidRoundEnum.ZGYS.getType());
        bidQueryWrapper.ge(ProjectBidSection::getStatus, PackageStatusEnum.TENDER_INVITATION.getValue());
        bidQueryWrapper.eq(ProjectBidSection::getAbnormalStatus, AbnormalStatusEnum.NORMAL.getType());
        bidQueryWrapper.orderByAsc(ProjectBidSection::getPackageNumber);
        List<ProjectBidSection> bidSections = projectBidSectionMapper.selectList(bidQueryWrapper);
        if (CollectionUtil.isNotEmpty(bidSections)) {
            ProjectBidSection section = bidSections.get(0);
            //状态同步
            ProjectBidSection projectBidSection = projectBidSectionMapper.selectById(sectionId);
            // 如果按项目资格预审已经到达第二轮状态 那么需要新增一条第一轮已归档数据 并且生成第二轮初始标段数据
            if(section.getBidRound().equals(BidRoundEnum.HS.getType())){
                projectBidSection.setStatus(PackageStatusEnum.ARCHIVING_HAS_RECEIVED.getValue().toString());
                projectBidSection.setBidRound(BidRoundEnum.HS.getType());
                //统一同步标段状态
                List<ProjectBidSection> sections = new ArrayList<>();
                sections.add(projectBidSection);
                commonSectionService.updateSectionStatus(sections,"GD");
            }else{
                projectBidSection.setStatus(bidSections.get(0).getStatus());
                //统一同步标段状态
                commonSectionService.updateSectionStatus(sectionId.toString(),bidSections.get(0).getStatus());
            }
        }
    }

    /**
     * 采购方式由资格预审修改为其他，或者其他采购方式修改为资格预审 删除相关公告或文件
     * @param projectId
     * @param sectionId
     * @return
     */
    @Override
    public Boolean deleteNoticeAndDoc(Long projectId, Long sectionId) {
        Boolean flag = true;
        //招标公告
        PreQualificationService noticPreQualificationService = SpringUtils.getBean("NoticePreQualificationServiceImpl");
        flag = noticPreQualificationService.deletePreQualificationInfo(projectId, sectionId);
        if (flag) {
            //招标文件
            PreQualificationService docPreQualificationService = SpringUtils.getBean("DocPreQualificationServiceImpl");
            flag = docPreQualificationService.deletePreQualificationInfo(projectId, sectionId);
        }
        //删除套里的标段信息
        projectService.deleteSectionGroup(projectId,sectionId);
        return flag;
    }

    @Override
    public Boolean addReBidInfo(ReBidInfoREQ reBidInfoREQ) {
        Boolean flag = true;
        List<Long> formerSectionIds = reBidInfoREQ.getFormerSectionIds();
        List<ProjectBidSection> formerSections = new ArrayList<>();
        List<ProjectBidSection> reSections = new ArrayList<>();
        for (int i = 0; i < formerSectionIds.size(); i++) {
            ProjectBidSection formerSection = projectBidSectionMapper.selectById(formerSectionIds.get(i));
            //原标段终止
            formerSection.setAbnormalStatus(AbnormalStatusEnum.END.getType());
            formerSection.setStatus(PackageStatusEnum.ARCHIVING.getValue().toString());
            //本次项目是否被人重新招标 0：否  1：是
            formerSection.setIsRebidding(CommonConstants.YES);
            formerSections.add(formerSection);


            //重新招标 只带包号 包名称
            ProjectBidSection projectBidSection = BeanListUtil.convert(formerSection, ProjectBidSection.class);//new ProjectBidSection();
            projectBidSection.setStatus(PackageStatusEnum.TENDER_INVITATION.getValue().toString());
            projectBidSection.setBidSectionCode(BidSectionCodeEnum.bidding_announcement_no.getCode());
            projectBidSection.setPackageName(formerSection.getPackageName());
            projectBidSection.setAbnormalStatus(AbnormalStatusEnum.NORMAL.getType());
            projectBidSection.setFormerPurchaseStatus(formerSection.getPurchaseStatus());
            projectBidSection.setSaleEndTimeType(null);
            projectBidSection.setSaleEndTime(null);
            projectBidSection.setSubmitEndTimeType(null);
            projectBidSection.setSubmitEndTime(null);
            projectBidSection.setId(null);
            //本次项目是否被人重新招标 0：否  1：是
            projectBidSection.setIsRebidding(CommonConstants.YES);
            projectBidSection.setSectionGroupId((long) 0);
            projectBidSection.setProjectId(reBidInfoREQ.getProjectId());

            if (formerSection.getBaseSectionId() == null) {
                projectBidSection.setBaseSectionId(formerSectionIds.get(i));
                projectBidSection.setBasePackageNumber(formerSection.getPackageNumber());
            }
            projectBidSection.setFormerSectionId(formerSectionIds.get(i));
            projectBidSection.setFormerPackageNumber(formerSection.getPackageNumber());
            projectBidSection.setFormerPurchaseStatus(formerSection.getPurchaseStatus());
            if (reBidInfoREQ.getIsFormerPurchaseNumber() == CommonConstants.NO) {
                projectBidSection.setPackageNumber(formerSection.getPackageNumber());
            } else {
                String packageNumber = this.baseMapper.queryMaxPackageNumber(reBidInfoREQ.getProjectId());
                projectBidSection.setPackageNumber(Integer.parseInt(packageNumber) + 1);
            }
            reSections.add(projectBidSection);
            //新增重新招标标段
            commonSectionService.updateSectionStatus(projectBidSection);
        }

        //原来标段终止
        flag = commonSectionService.updateSectionStatus(formerSections);
       // flag = commonSectionService.updateSectionStatus(reSections);
        if (!flag) {
            throw new SunFlowerException(ExceptionEnum.BID_DOC_ADD_ERROR, ExceptionEnum.BID_DOC_ADD_ERROR.getMessage());
        }

        //推送ncc
        reSections.forEach(s->{
            if(s.getPackageNumber() != null){
                projectService.pushProjectNcc(s.getProjectId(),s.getPackageNumber());
            }
        });

        return flag;
    }

    /**
     * 检索各标段状态
     *
     * @param id 项目ID
     * @return 各标段状态拼接
     */
    @Override
    public String queryProjectStatusById(Long id) {
        return this.baseMapper.queryProjectStatusById(id);
    }

    @Override
    public String getSectionPackageNumberList(String sections) {
        String packageNumbers = "";
        if(null != sections){
            String[] split = sections.split(",");
            for (String s : split) {
                ProjectBidSection byId = getById(Long.parseLong(s));
                if(null != byId && null !=byId.getPackageNumber()){
                    packageNumbers = packageNumbers + byId.getPackageNumber()+",";
                }
            }
        }
        if(null != packageNumbers && !"".equals(packageNumbers)){
            packageNumbers =  packageNumbers.substring(0, packageNumbers.length()-1);
        }
        return packageNumbers;
    }

    @Override
    public List<ProjectBidSection> getSectionListByExpert(Long projectId) {
        return projectMapper.getSectionListByExpert(projectId);
    }

    @Override
    public ProjectBidSectionVO getSectionInfoById(Long id) {
        ProjectBidSection projectBidSection = this.getById(id);
        //查询项目里的所在地信息 默认带入
        Project project = projectService.getProjectById(projectBidSection.getProjectId());
        List<SectionExpertSpecialty> list = iSectionExpertSpecialtyService.list(new LambdaQueryWrapper<SectionExpertSpecialty>()
                .eq(SectionExpertSpecialty::getSectionId, id));
        List<Long> spids = list.stream()
                .map(m -> {
                    return m.getExpertSpecialtyId();
                }).collect(Collectors.toList());
        // 判断标段是否为重新招标，若标的物备份失败则读取原标段数据
        if (null != projectBidSection.getBaseSectionId() && list.size() == 0) {
            List<SectionExpertSpecialty> baseList = iSectionExpertSpecialtyService.list(new LambdaQueryWrapper<SectionExpertSpecialty>()
                    .eq(SectionExpertSpecialty::getSectionId, projectBidSection.getBaseSectionId()));
            spids = baseList.stream()
                    .map(m -> {
                        return m.getExpertSpecialtyId();
                    }).collect(Collectors.toList());
        }
        projectBidSection.setSectionExpertSpecialtyList(spids);
        ProjectBidSectionVO projectBidSectionVO = BeanListUtil.convert(projectBidSection, ProjectBidSectionVO.class);
        projectBidSectionVO.setSectionExpertSpecialtyList(spids);
        projectBidSectionVO.setAddressProvince(project.getAddressProvince());
        projectBidSectionVO.setAddressCity(project.getAddressCity());
        return projectBidSectionVO;
    }

    /**
     * 查询套内的标段信息
     * @param sectionGroupId
     * @return
     */
    @Override
    public List<ProjectBidSection> getGroupSection(Long sectionGroupId) {
        List<ProjectBidSection> sections = new ArrayList<>();
        String sectionIds = this.baseMapper.getGroupSection(sectionGroupId);
        if (StringUtils.isNotBlank(sectionIds)) {
            String[] ids = sectionIds.split(",");
            for (String id : ids) {
                sections.add(getById(id));
            }
        }
        return sections;
    }

    /**
     * 通过projectId获取最大标段的状态
     * @param projectId
     * @return
     */
    @Override
    public ProjectBidSection getMaxStatusSectionByProjectId(Long projectId,Long sectionId) {
        return this.baseMapper.getMaxStatusSectionByProjectId(projectId,sectionId);
    }

    @Override
    public Boolean changePackageNumber(List<PackageNumberREQ> list) {
        List<ProjectBidSection> projectBidSections = new ArrayList<>();
        list.stream().forEach(e -> {
            ProjectBidSection projectBidSection = new ProjectBidSection();
            projectBidSection.setId(e.getId());
            projectBidSection.setPackageNumber(e.getPackageNumber());
            projectBidSections.add(projectBidSection);
        });
        return this.updateBatchById(projectBidSections);
    }

    @Override
    public List<SupplierInfoDTO> getSupplierInfo(SupplierInfoREQ supplierInfo) {
        List<SupplierInfoDTO> supplierInfoDTOS = new ArrayList<>();
        if (supplierInfo.getOldBidderType().equals(OldBidderTypeEnum.BUY.getType())) {
            //已购买招标文件的投标人
            supplierInfoDTOS = supplierInfoMapper.getBuySupplier(supplierInfo);
        } else if (supplierInfo.getOldBidderType().equals(OldBidderTypeEnum.ATTENTION.getType())) {
            //已通过关注审核的投标人
            supplierInfoDTOS = supplierInfoMapper.getAttentionSupplier(supplierInfo);
        } else if (supplierInfo.getOldBidderType().equals(OldBidderTypeEnum.ALL.getType())) {
            //指定投标人
            supplierInfoDTOS = supplierInfoMapper.getAllSupplier(supplierInfo);
        }
        return supplierInfoDTOS;
    }

    @Override
    public List<SupplierInfoDTO> getAssignSupplierInfo(Long sectionId) {
        return supplierInfoMapper.getAssignSupplierInfo(sectionId);
    }

    @Override
    public Boolean updateProject(ProjectBidReq projectBidReq) {
        Project project = projectService.getProjectById(projectBidReq.getProjectId());
        project.setBidTypeName(projectBidReq.getBidTypeName());
        project.setId(project.getId());
        int i = projectMapper.updateById(project);
        return i == 1;
    }

    /**
     * 资格预审按项目完善信息,追加到各阶段数据
     *
     * @param projectId
     * @param projectBidSection
     * @return
     */
    @Override
    public Boolean perfectPreQualificationInfo(Long projectId, ProjectBidSection projectBidSection) {
        Boolean flag = true;
        // 招标公告信息完善
        PreQualificationService noticPreQualificationService = SpringUtils.getBean("NoticePreQualificationServiceImpl");
        flag = noticPreQualificationService.savePreQualificationInfo(projectId, projectBidSection);
        // 招标文件信息完善
        if (flag) {
            PreQualificationService docPreQualificationService = SpringUtils.getBean("DocPreQualificationServiceImpl");
            flag = docPreQualificationService.savePreQualificationInfo(projectId, projectBidSection);
        }
        // 供应商报名
        PreQualificationService applyPreQualificationService = SpringUtils.getBean("ApplyPreQualificationServiceImpl");
        applyPreQualificationService.savePreQualificationInfo(projectId,projectBidSection);
        // 资格预审备案/结果
        PreQualificationService bidWinPreQualificationService = SpringUtils.getBean("BidWinPreQualificationServiceImpl");
        bidWinPreQualificationService.savePreQualificationInfo(projectId, projectBidSection);
        // 结果公示、通知书
        PreQualificationService bidWinBulletin = SpringUtils.getBean("BidWinBulletinQualificationServiceImpl");
        bidWinBulletin.savePreQualificationInfo(projectId, projectBidSection);
        // 归档
        PreQualificationService archiveBid = SpringUtils.getBean("ArchiveBidPreQualificationServiceImpl");
        archiveBid.savePreQualificationInfo(projectId, projectBidSection);
        // 补充公告、澄清修改
        PreQualificationService tulipPreQualificationService = SpringUtils.getBean("TulipPreQualificationServiceImpl");
        tulipPreQualificationService.savePreQualificationInfo(projectId, projectBidSection);
        // 判断是否全部终止 拷贝终止之前的标段状态
        // 查询该采购方式下所有标段
        Long sectionId = projectBidSection.getId();
        LambdaQueryWrapper bidQueryWrapper=new LambdaQueryWrapper<ProjectBidSection>()
                .notIn(ProjectBidSection::getId,sectionId)
                .eq(ProjectBidSection::getProjectId,projectId)
                .eq(ProjectBidSection::getPurchaseStatus, PurchaseStatusEnum.PRE_TRIAL.getType())
                .eq(ProjectBidSection::getPurchaseMode,projectBidSection.getPurchaseMode())
                .eq(ProjectBidSection::getIsDelete,CommonConstants.NO);
        List<ProjectBidSection> list =projectBidSectionMapper.selectList(bidQueryWrapper);
        if (list!=null&&list.size()>0){
            Integer countZhongZhi=0;
            ProjectBidSection zhongZhiBid=null;
            ProjectBidSection nomBid=null;
            for (ProjectBidSection bidSection : list) {
                if (bidSection.getStatus().equals(PackageStatusEnum.EXCEPT_STOP.getValue()+"")){
                    countZhongZhi++;
                    if (zhongZhiBid==null){
                        zhongZhiBid=bidSection;
                    }
                }else {
                    if (nomBid==null){
                        nomBid=bidSection;
                    }
                }
            }
            // 全部终止
            if (list.size()==countZhongZhi){
                if (zhongZhiBid!=null){
                    UpdateWrapper<ProjectBidSection> updateWrapper =new UpdateWrapper<>();
                    updateWrapper.lambda().eq(ProjectBidSection::getId,projectBidSection.getId());
                    updateWrapper.lambda().set(ProjectBidSection::getStatus,zhongZhiBid.getFormerAbnormalStatus());
                    projectBidSectionMapper.update(null,updateWrapper);
                }
            }else{
                if (nomBid!=null){
                    UpdateWrapper<ProjectBidSection> updateWrapper =new UpdateWrapper<>();
                    updateWrapper.lambda().eq(ProjectBidSection::getId,projectBidSection.getId());
                    updateWrapper.lambda().set(ProjectBidSection::getStatus,nomBid.getStatus());
                    projectBidSectionMapper.update(null,updateWrapper);
                }

            }
        }


        return flag;
    }

    /**
     * 获取所有标段委托金额总和
     *
     * @param dictionaryList 汇率集合
     * @param projectBidSectionList 标段集合
     * @return
     */
    private BigDecimal getBidTotalAmount(List<Dictionary> dictionaryList, List<ProjectBidSection> projectBidSectionList) {
        // 委托金额总和
        BigDecimal totalAmount = new BigDecimal(FormatConstants.ENTRUST_MONEY);
        if (!CollectionUtils.isEmpty(projectBidSectionList)) {
            for (ProjectBidSection projectBidSection : projectBidSectionList) {
                if (null != projectBidSection.getEntrustMoney()) {
                    // 委托金额币种类型不同，转换成人民币计算
                    BigDecimal currency = CurrencyUtils.getCurrency(dictionaryList, projectBidSection.getEntrustCurrency());
                    // 委托金额币种是 人民币
                    totalAmount = totalAmount.add(projectBidSection.getEntrustMoney().multiply(currency));
                }
            }
        }
        return totalAmount;
    }

    @Override
    public Boolean updateDocSaleEndTime(Long sectionId, String saleEndTime) {
        return this.baseMapper.updateDocSaleEndTime(sectionId, saleEndTime) > 0;
    }

    @Override
    public Boolean updateDocSubmitEndTime(Long sectionId, String submitEndTime) {
        return this.baseMapper.updateDocSubmitEndTime(sectionId, submitEndTime) > 0;
    }

    @Override
    public List<ProjectBidSectionVO> listUpdate(Long projectId, Integer value, Integer value1) {
        return this.baseMapper.listUpdate(projectId, value,value1) ;
    }

    /**
     * 通过projectId获取最大标段的状态，除异常状态
     * @param projectId
     * @return
     */
    @Override
    public ProjectBidSection getMaxStatusSectionByProjectId2(Long projectId,Long sectionId) {
        return this.baseMapper.getMaxStatusSectionByProjectId2(projectId,sectionId);
    }

    /**
     * 根据标段id查询标段的采购方式
     * @param req
     * @return
     */
    @Override
    public ProjectSectionPurchaseVo selectSectionById(ProjectBidSectionReq req) {
        ProjectSectionPurchaseVo sectionPurchaseVo = new ProjectSectionPurchaseVo();
//        ProjectBidSection projectBidSection = this.getById(req.getSectionId());
//        if (projectBidSection != null) {
//            if (projectBidSection.getPurchaseType() != null && StringUtils.isNotEmpty(projectBidSection.getPurchaseMode()) && projectBidSection.getPurchaseStatus() != null) {
//                Integer purchaseType = 0;
//                Integer purchaseMode = 0;
//                Integer purchaseStatus = 0;
//
//                //判断一级采购方式
//                if (projectBidSection.getPurchaseType().toString().equals(BidPurchaseModeEnum.REQUIRED_ACCORDING.getType())) {
//                    //依法必招
//                    purchaseType = PurchaseTypeSysEnum.ACCORD_LAW_RECRUITED.getType();
//                } else if (projectBidSection.getPurchaseType().toString().equals(BidPurchaseModeEnum.NOT_REQUIRED_ACCORDING.getType())) {
//                    //非依法必招
//                    purchaseType = PurchaseTypeSysEnum.NO_ACCORD_LAW_RECRUITED.getType();
//                } else if (projectBidSection.getPurchaseType().toString().equals(BidPurchaseModeEnum.GOVERNMENT_PROCUREMENT.getType())) {
//                    //政府采购
//                    purchaseType = PurchaseTypeSysEnum.GOVERNMENT_PROCUREMENT.getType();
//                } else if (projectBidSection.getPurchaseType().toString().equals(BidPurchaseModeEnum.INTERNATIONAL_BIDDING.getType())) {
//                    //国际招标
//                    purchaseType = PurchaseTypeSysEnum.INTERNATIONAL_TENDERS.getType();
//                }
//
//
//                //判断二级采购方式
//                if (projectBidSection.getPurchaseMode().equals(BidPurchaseModeEnum.REQUIRED_ACCORDING_924091.getType())) {
//                    //依法必招-招标
//                    purchaseMode = PurchaseModeSysEnum.TENDER.getType();
//                } else if (projectBidSection.getPurchaseMode().equals(BidPurchaseModeEnum.NOT_REQUIRED_ACCORDING_924093.getType())) {
//                    //非依法必招-招标
//                    purchaseMode = PurchaseModeSysEnum.TENDER.getType();
//                } else if (projectBidSection.getPurchaseMode().equals(BidPurchaseModeEnum.NOT_REQUIRED_ACCORDING_924094.getType())) {
//                    //非依法必招-竞争性磋商
//                    purchaseMode = PurchaseModeSysEnum.COMPETITIVE_CONSULTATIONS.getType();
//                } else if (projectBidSection.getPurchaseMode().equals(BidPurchaseModeEnum.NOT_REQUIRED_ACCORDING_924095.getType())) {
//                    //非依法必招-竞争性谈判
//                    purchaseMode = PurchaseModeSysEnum.GOVERNMENT_PROCUREMENT.getType();
//                } else if (projectBidSection.getPurchaseMode().equals(BidPurchaseModeEnum.NOT_REQUIRED_ACCORDING_924096.getType())) {
//                    //非依法必招-询价
//                    purchaseMode = PurchaseModeSysEnum.GOVERNMENT_NEGOTIATION.getType();
//                } else if (projectBidSection.getPurchaseMode().equals(BidPurchaseModeEnum.NOT_REQUIRED_ACCORDING_924097.getType())) {
//                    //非依法必招-征询
//                    purchaseMode = PurchaseModeSysEnum.CONSULT.getType();
//                } else if (projectBidSection.getPurchaseMode().equals(BidPurchaseModeEnum.NOT_REQUIRED_ACCORDING_924098.getType())) {
//                    //非依法必招-单一来源
//                    purchaseMode = PurchaseModeSysEnum.SINGLE_SOURCE.getType();
//                } else if (projectBidSection.getPurchaseMode().equals(BidPurchaseModeEnum.NOT_REQUIRED_ACCORDING_924099.getType())) {
//                    //非依法必招-其他
//                    purchaseMode = PurchaseModeSysEnum.OTHER.getType();
//                } else if (projectBidSection.getPurchaseMode().equals(BidPurchaseModeEnum.GOVERNMENT_PROCUREMENT_924101.getType())) {
//                    //政府采购-招标
//                    purchaseMode = PurchaseModeSysEnum.TENDER.getType();
//                } else if (projectBidSection.getPurchaseMode().equals(BidPurchaseModeEnum.GOVERNMENT_PROCUREMENT_924102.getType())) {
//                    //政府采购-竞争性磋商
//                    purchaseMode = PurchaseModeSysEnum.COMPETITIVE_CONSULTATIONS.getType();
//                } else if (projectBidSection.getPurchaseMode().equals(BidPurchaseModeEnum.GOVERNMENT_PROCUREMENT_924103.getType())) {
//                    //政府采购-竞争性谈判
//                    purchaseMode = PurchaseModeSysEnum.GOVERNMENT_PROCUREMENT.getType();
//                } else if (projectBidSection.getPurchaseMode().equals(BidPurchaseModeEnum.GOVERNMENT_PROCUREMENT_924104.getType())) {
//                    //政府采购-询价
//                    purchaseMode = PurchaseModeSysEnum.GOVERNMENT_NEGOTIATION.getType();
//                } else if (projectBidSection.getPurchaseMode().equals(BidPurchaseModeEnum.GOVERNMENT_PROCUREMENT_924105.getType())) {
//                    //政府采购-单一来源
//                    purchaseMode = PurchaseModeSysEnum.SINGLE_SOURCE.getType();
//                } else if (projectBidSection.getPurchaseMode().equals(BidPurchaseModeEnum.GOVERNMENT_PROCUREMENT_924106.getType())) {
//                    //政府采购-其他
//                    purchaseMode = PurchaseModeSysEnum.OTHER.getType();
//                } else if (projectBidSection.getPurchaseMode().equals(BidPurchaseModeEnum.INTERNATIONAL_BIDDING_924108.getType())) {
//                    //国际招标-招标
//                    purchaseMode = PurchaseModeSysEnum.TENDER.getType();
//                }
//
//                //三级采购方式
//                purchaseStatus = projectBidSection.getPurchaseStatus();
//
//                sectionPurchaseVo.setPurchaseType(purchaseType);
//                sectionPurchaseVo.setPurchaseMode(purchaseMode);
//                sectionPurchaseVo.setPurchaseStatus(purchaseStatus);
//            }
//
//            if (StringUtils.isNotEmpty(projectBidSection.getPurchaseModeName())) {
//                sectionPurchaseVo.setPurchaseModeName(projectBidSection.getPurchaseModeName());
//            }
//        }
        return sectionPurchaseVo;
    }


    /**
     * 查询ncc中的项目编号和项目的所在部门
     * @param sectionId
     * @return
     */
    @Override
    public ProjectShareInfoDto getProjectShareToNccInfoBySectionId(Long sectionId) {
        return this.baseMapper.getProjectShareToNccInfoBySectionId(sectionId);
    }

    @Override
    public List<ProjectBidSectionVO> listInfo(Long projectId) {
        return this.baseMapper.listInfo(projectId);
    }
}
