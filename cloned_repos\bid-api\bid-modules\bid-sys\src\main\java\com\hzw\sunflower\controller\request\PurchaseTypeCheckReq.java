package com.hzw.sunflower.controller.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(description = "项目详细信息设置")
@Data
public class PurchaseTypeCheckReq {

    @ApiModelProperty("一级采购方式 依法必招，非依法必招，政府采购，国际招标")
    private String purchaseType;

    @ApiModelProperty("二级采购方式 招标，竞争性磋商，竞争性谈判，询价，征询，单一来源，其他")
    private String purchaseMode;

    @ApiModelProperty("三级采购方式 1.预审公开 2.后审公开 3.邀请 4.二阶段 5.政府公开 4.政府邀请")
    private Integer purchaseStatus;

}
