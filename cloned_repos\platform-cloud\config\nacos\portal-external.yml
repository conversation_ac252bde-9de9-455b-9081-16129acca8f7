spring:
  datasource:
    dynamic:
      # 设置默认的数据源或者数据源组,默认值即为 master
      primary: master
      datasource:
        # 主库数据源
        master:
          type: ${spring.datasource.type}
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: ${datasource.system-master.url}
          username: ${datasource.system-master.username}
          password: ${datasource.system-master.password}

#实名认证 聚合
juhe:
  appcode: 聚合手机三要素SHA256
  apiUri: http://apis.juhe.cn/telecom_sha256/query
  validate: false
  key: 2c08521189e72833f7fc863e020a91e2
  telecom2:
    appcode: 姓名手机号二要素验证SHA256版
    apiUri: http://apis.juhe.cn/telecom2_sha256/query
    key: 46a5002697fcf424a57973e33840ff36
  nameIdCard:
    appkey: 11e58d6651a8c16aff602b207fe7a93e
    queryUrl: http://op.juhe.cn/idcard/query?key=
    queryEncryUrl: http://op.juhe.cn/idcard/queryEncry?key=
  certificateOCR:
    appkey: 2b488ab09e4463f59e1896ba7dd07bde
    queryUrl: http://v.juhe.cn/certificates/query
    validate: true

#模板文件配置
files:
  template:
    path: /data/template
  temporary:
    path: /data/temporary


#语音系统
ctd:
  vccid: 3120
  voicedisplayNum: 02566044260
  key: C1D9F86EDC077E44DEE04F135FEC7A1B
  voice_url: http://58.220.61.32:15407/ICCPYYTZ?mac=
  serviceName: YYTZRequest
  voice_supNumber: 1,2
  voice_serviceKey: 900010


  #小程序ocr
weChat:
  ocr:
    appId: wxc6ec43de115c19b7
    secret: b8e48a4302599e6181f8dd05902ea91f
    tokenUrl: https://api.weixin.qq.com/cgi-bin/token
    grantType: client_credential
    requestUrl: https://api.weixin.qq.com/cv/ocr/

tianyan:
  token: e5ea09dd-5c42-4f14-8fc1-297de3ef77ce
  keywordsInfoUrl: http://open.api.tianyancha.com/services/open/search/2.0
  baseInfoUrl: http://open.api.tianyancha.com/services/open/ic/baseinfoV2/2.0
  baseInfoUrlSpecial: http://open.api.tianyancha.com/services/open/ic/baseinfo/special
  baseInfoUrlNormal: http://open.api.tianyancha.com/services/open/ic/baseinfo/normal
  validate: false


