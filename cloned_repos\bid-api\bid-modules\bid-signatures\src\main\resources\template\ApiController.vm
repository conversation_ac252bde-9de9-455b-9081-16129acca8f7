package ${package_name}.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hzw.sunflower.common.BaseController;
import com.hzw.sunflower.util.BeanListUtil;
import com.hzw.sunflower.common.Paging;
import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.constant.MessageConstants;
import ${package_name}.dto.${modelname}DTO;
import ${package_name}.controller.request.${modelname}REQ;
import ${package_name}.entity.${modelname};
import ${package_name}.entity.condition.${modelname}Condition;
import ${package_name}.service.${modelname}Service;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
* ${modelname}Controller
*/
@Api(tags = "${modelname}服务")
@RestController
@RequestMapping("/${lowmodel}")
public class ${modelname}Controller extends BaseController {
    @Autowired
    private ${modelname}Service ${lowmodel}Service;

    @ApiOperation(value = "根据条件分页查询${modelname}列表")
    @ApiImplicitParam(name = "${lowmodel}REQ", value = "用户表 查询条件", required = true, dataType = "${modelname}REQ", paramType = "body")
    @PostMapping("/list")
    public Paging<${modelname}> list(@RequestBody ${modelname}REQ ${lowmodel}REQ) {
        ${modelname}Condition condition = BeanListUtil.convert(${lowmodel}REQ,${modelname}Condition.class);
        IPage<${modelname}> page = ${lowmodel}Service.findInfoByCondition(condition);
            return Paging.buildPaging(page);
    }


    @ApiOperation(value = "根据主键ID查询${modelname}信息")
    @ApiImplicitParam(name = "id", value = "主键ID", required = true)
    @GetMapping(value = "/get/{id}")
    public Result<${modelname}> get(@PathVariable Long id) {
        if (id == null) {
            return Result.failed(MessageConstants.GET_NULL);
        }
        ${modelname} ${lowmodel} = ${lowmodel}Service.getInfoById(id);
        return Result.ok(${lowmodel});
    }

    @ApiOperation(value = "新增${modelname}信息")
    @ApiImplicitParam(name = "${lowmodel}DTO", value = "${modelname}表 ", required = true, dataType = "${modelname}DTO", paramType = "body")
    @PostMapping("/add")
    public Result<Boolean> add(@RequestBody ${modelname}DTO ${lowmodel}DTO) {
        Boolean bool = ${lowmodel}Service.addInfo(${lowmodel}DTO);
        return Result.okOrFailed(bool);
    }

    @ApiOperation(value = "修改${modelname}信息")
    @ApiImplicitParam(name = "${lowmodel}DTO", value = "${modelname}表 ", required = true, dataType = "${modelname}DTO", paramType = "body")
    @PutMapping(value = "/update")
    public Result<Boolean> update(@RequestBody ${modelname}DTO ${lowmodel}DTO) {
        Long id = ${lowmodel}DTO.getId();
        if (id == null) {
            return Result.failed(MessageConstants.UPDATE_NULL);
        }
        Boolean bool = ${lowmodel}Service.updateInfo(${lowmodel}DTO);
        return Result.okOrFailed(bool);
    }

    @ApiOperation(value = "根据主键ID删除${modelname}表 ")
    @ApiImplicitParam(name = "id", value = "主键ID", required = true)
    @DeleteMapping(value = "/delete/{id}")
    public Result<Boolean> delete(@PathVariable Long id) {
        if (id == null) {
            return Result.failed(MessageConstants.DELETE_NULL);
        }
        Boolean bool = ${lowmodel}Service.deleteById(id);
        return Result.okOrFailed(bool);
    }

    @ApiOperation(value = "根据主键ID列表批量删除${modelname}表 ")
    @ApiImplicitParam(name = "idList", value = "主键ID列表", required = true, allowMultiple = true, paramType = "body")
    @DeleteMapping("/deleteList")
    public Result<Boolean> deleteList(@RequestBody List<Long> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return Result.failed(MessageConstants.DELETE_NULL);
        }
        Boolean bool = ${lowmodel}Service.deleteByIds(idList);
        return Result.okOrFailed(bool);
    }
}