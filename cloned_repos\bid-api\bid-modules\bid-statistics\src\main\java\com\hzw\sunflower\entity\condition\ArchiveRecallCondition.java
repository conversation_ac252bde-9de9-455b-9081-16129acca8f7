package com.hzw.sunflower.entity.condition;

import com.hzw.sunflower.common.BaseCondition;
import com.hzw.sunflower.dto.ProjectUserDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(description = "查询条件")
public class ArchiveRecallCondition extends BaseCondition {
    @ApiModelProperty("关键字")
    private String keyWords;

    @ApiModelProperty("项目经理的userId和部门code")
    private List<ProjectUserDTO> projectUser;

    @ApiModelProperty("归档撤回情况（1 撤回待确认、2 已撤回、3 已退回）")
    private Integer status;

    /**
     *  开始时间
     *
     */
    @ApiModelProperty("开始时间")
    private String startDate;


    /**
     *  结束时间
     *
     */
    @ApiModelProperty("结束时间")
    private String endDate;
}
