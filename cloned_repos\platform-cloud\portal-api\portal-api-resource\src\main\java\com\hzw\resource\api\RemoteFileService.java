package com.hzw.resource.api;

import com.hzw.common.core.domain.R;
import com.hzw.common.core.exception.ServiceException;
import com.hzw.resource.api.domain.SysFile;
import com.hzw.resource.api.domain.vo.SysOssVo;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.util.Map;

/**
 * 文件服务
 *
 * <AUTHOR>
 */
public interface RemoteFileService {

    /**
     * 上传文件
     *
     * @param file 文件信息
     * @return 结果
     */
    SysFile upload(String name, String originalFilename, String contentType, byte[] file) throws ServiceException;

    /**
     * 通过ossId查询对应的url
     *
     * @param ossIds ossId串逗号分隔
     * @return url串逗号分隔
     */
    String selectUrlByIds(String ossIds);

    /**
     * 上传文件
     * @param file
     * @return
     */
    String uploadFile(File file);

    /**
     * 上传文件
     * @param originalfileName
     * @param fileContent
     * @param contentType
     * @return
     */
    R<Map<String, String>> uploadFile(String originalfileName, byte[] fileContent, String contentType);

    /**
     * 根据id查询SysFile
     * @param ossId
     * @return
     */
    SysFile selectByOssId(String ossId);

    /**
     * 根据id查询SysFile
     * @param ossId
     * @return
     */
    SysOssVo downloadFile(String ossId) ;
}
