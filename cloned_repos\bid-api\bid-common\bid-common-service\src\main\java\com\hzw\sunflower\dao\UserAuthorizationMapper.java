package com.hzw.sunflower.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hzw.sunflower.dto.UserAuthorizationDto;
import com.hzw.sunflower.entity.UserAuthorization;
import org.apache.ibatis.annotations.Param;

/**
 * 用户表 Mapper接口
 *
 * <AUTHOR>
 * @version 1.0.0 2021-04-07
 */
public interface UserAuthorizationMapper extends BaseMapper<UserAuthorization> {


    /**
     * 查询用户授权方式和授权项目
     * @param userId
     * @param userIdentity
     * @return
     */
    UserAuthorizationDto getUserAuthorizationInfo(@Param("userId") Long userId,@Param("identity") Integer userIdentity);
}
