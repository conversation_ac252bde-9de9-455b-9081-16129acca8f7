package com.hzw.sunflower.controller.condition;

import com.baomidou.mybatisplus.annotation.TableId;
import com.hzw.sunflower.common.BaseCondition;
import io.swagger.annotations.ApiModel;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022/6/15
 */
@ApiModel(description = "短信查询条件")
@Data
public class TemCondition extends BaseCondition {

    @TableId(value = "id")
    private Long id;

    /**
     * 模板code
     */
    private String templateCode;

    /**
     * 模板名称
     */
    private String templateName;

    /**
     * 模板内容
     */
    private String templateContent;

    /**
     * 模板二级code
     */
    private String templateSecondCode;
}
