package com.hzw.sunflower.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.hzw.sunflower.common.BaseBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @ClassName:BidDocSection
 * @Auther: lijinxin
 * @Description: 编制标段表
 * @Date: 2024/6/28 09:45
 * @Version: v1.0
 */
@ApiModel(description = "编制标段表")
@TableName("t_bid_doc_section")
@Data
public class BidDocSection extends BaseBean {


    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "招标文件编制表id")
    private Long docId;

    @ApiModelProperty(value = "标段id")
    private Long sectionId;

    @ApiModelProperty(value = "包号")
    private Integer packageNumber;



}
