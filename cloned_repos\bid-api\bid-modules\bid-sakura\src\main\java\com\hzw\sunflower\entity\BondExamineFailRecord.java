package com.hzw.sunflower.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hzw.sunflower.common.BaseBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 保证金审批失败记录表
 * </p>
 *
 */
@Data
@TableName("t_bond_examine_fail_record")
@ApiModel(value = "BondPayInfo对象", description = "保证金审批失败记录表")
public class BondExamineFailRecord extends BaseBean implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("退还id")
    @TableField("refund_id")
    private Long refundId;

    @ApiModelProperty("失败原因")
    @TableField("fail_reason")
    private String failReason;

    @ApiModelProperty("批次")
    @TableField("batch")
    private String batch;

}
