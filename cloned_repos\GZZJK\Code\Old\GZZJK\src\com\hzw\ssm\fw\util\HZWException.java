package com.hzw.ssm.fw.util;

/*******************************************************************************
 * Description：自定义异常类
 * 
 * @file: JSZCException.java
 * @page P01030100
 * <AUTHOR>
 * @version:1.0.0
 * 
 * Copyright
 ******************************************************************************/
public class HZWException extends RuntimeException {

	private static final long serialVersionUID = 1L;
	
	/** 异常 */
	private Throwable e;
	
	/** 异常code */
	private String code;
	
	/** 参数 */
	private String[] args;

	public HZWException() {
		super();
	}

	public HZWException(Throwable e) {
		super(e);
		this.e = e;
	}

	public HZWException(String code) {
		super(code);
		this.code = code;
	}

	public HZWException(String code, Throwable e) {
		super(code, e);
		this.e = e;
		this.code = code;
	}
	
	
	public HZWException(String code, String[] args, Throwable e) {
		super(e);
		this.args = args;
		this.e = e;
		this.code = code;
	}

	public Throwable getE() {
		return e;
	}

	public void setE(Throwable e) {
		this.e = e;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String[] getArgs() {
		return args;
	}

	public void setArgs(String[] args) {
		this.args = args;
	}
}
