<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzw.sunflower.dao.BondRefundCountMapper">

    <select id="queryBondRefundManagePage" resultType="com.hzw.sunflower.dto.BondRefundManageDto">
        select distinct
            p.bid_type_name as bidTypeName,
            p.purchase_number as  purchaseNumber,
            p.project_name as projectName,
            pbs.id as sectionId,
            pbs.package_number as packageNumber,
            pbs.package_name as packageName,
            pbs.submit_end_time as openBidTime,
            pbs.status as projectStatusCode,
            pbs.agency_cost_free as agencyCostFree
        from t_project p
        left join t_project_bid_section pbs on p.id = pbs.project_id
        left join t_project_entrust_user tpe on p.id = tpe.project_id
        where p.is_delete = '0' and pbs.is_delete = '0' and tpe.is_delete = '0' and pbs.status <![CDATA[ >= ]]> 30 and pbs.bond_type <![CDATA[ <> ]]> 0
        <if test="condition.keyWords != null and condition.keyWords!='' ">
            and (p.project_name like concat( '%', #{condition.keyWords}, '%' ) or p.purchase_number like concat( '%', #{condition.keyWords}, '%' ))
        </if>
        <if test="condition.dataScope!= null and condition.dataScope!='' ">
            ${condition.dataScope}
        </if>
        order by pbs.submit_end_time desc
    </select>

    <select id="relationCompanyCount" resultType="com.hzw.sunflower.entity.BondRelation">
        select
            section_id,
            company_id
        from t_bond_relation where section_id = ${sectionId} and is_delete = 0 and status = '2'  group by section_id,company_id
    </select>

    <select id="queryBondRelationNoRefundPage" resultType="com.hzw.sunflower.dto.BondRelationNoRefundDto">
        select distinct
            tc.company_name as companyName,
            p.purchase_number as purchaseNumber,
            t1.section_id as sectionId,
            t1.company_id as companyId,
            p.id as projectId,
            tpbs.package_number as packageNumber,
            tpbs.`status` as sectionStatus,
            t1.receiveTime,
            (t1.totalAmount - ifnull(t2.alreadyAmount,0)) as balance,
            tbwp.is_win as isWin,
            tbwp.is_candidate as isCandidate,
            tbwn.seal_time as sendTime,
            tpi.bond_remark as remark
        from
            (select group_concat(tbw.date) as receiveTime,
            tbs.amount as totalAmount,
            tbr.section_id,tbr.company_id
            from t_bond_relation tbr
            left join t_bond_split tbs on tbr.company_id =tbs.company_id and tbr.section_id = tbs.section_id
            left join t_bond_water tbw on tbr.water_id = tbw.id
            where tbr.is_delete = 0 and tbw.is_delete = 0 and tbr.status = '2' and tbs.is_delete = 0
            <if test="condition.sendStartTime != null and condition.sendStartTime!='' ">
                and tbw.date <![CDATA[ >= ]]> #{condition.sendStartTime}
            </if>
            <if test="condition.sendEndTime != null and condition.sendEndTime!='' ">
                and tbw.date <![CDATA[ <= ]]> #{condition.sendEndTime}
            </if>
            group by tbr.section_id,tbr.company_id,tbs.amount) t1
            left join t_project_bid_section tpbs on t1.section_id = tpbs.id
            left join t_project p on tpbs.project_id = p.id
            left join t_project_entrust_user tpe on p.id = tpe.project_id
            left join t_apply_info tpi on t1.section_id = tpi.sub_id  and t1.company_id = tpi.company_id
            LEFT JOIN ( SELECT max(id) as id ,section_id,seal_time  FROM t_bid_win_notice where `status` = 5  and bid_round = 2 AND is_delete = 0 group by section_id,seal_time ) tbwn ON t1.section_id = tbwn.section_id
            LEFT JOIN t_bid_win_people tbwp ON tbwn.id = tbwp.winbid_id
            AND t1.company_id = tbwp.tenderer_id
            AND tbwp.win_people_type = 1
            left join t_company tc on t1.company_id = tc.id
            left join t_bond_refund tbr on t1.section_id = tbr.section_id  and t1.company_id = tbr.company_id
            left join(select refund_id,sum(IF(status=3, IF(refund_money >0, bond_money+agency_fee , agency_fee), 0 )) as alreadyAmount from t_bond_refund_details where is_delete ='0' group by refund_id ) t2 on tbr.id = t2.refund_id
        where t1.totalAmount - ifnull(t2.alreadyAmount,0) >0
        <if test="condition.keyWords != null and condition.keyWords!='' ">
            and (p.project_name like concat( '%', #{condition.keyWords}, '%' ) or p.purchase_number like concat( '%', #{condition.keyWords}, '%' ))
        </if>
        <if test="condition.companyName != null and condition.companyName!='' ">
            and tc.company_name like concat( '%', #{condition.companyName}, '%' )
        </if>
        <if test="condition.deptId != null and condition.deptId.size > 0">
            AND tpe.department_id in
            <foreach collection="condition.deptId" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="condition.dataScope!= null and condition.dataScope!='' ">
            ${condition.dataScope}
        </if>
    </select>

    <select id="queryBondRelationNoRefundExcel" resultType="com.hzw.sunflower.dto.BondRelationNoRefundDto">
        select distinct
            tc.company_name as companyName,
            p.purchase_number as purchaseNumber,
            t1.section_id as sectionId,
            t1.company_id as companyId,
            p.id as projectId,
            tpbs.package_number as packageNumber,
            tpbs.`status` as sectionStatus,
            t1.receiveTime,
            (t1.totalAmount - ifnull(t2.alreadyAmount,0)) as balance,
            tbwp.is_win as isWin,
            tbwp.is_candidate as isCandidate,
            tbwn.seal_time as sendTime,
            tpi.bond_remark as remark
            from
            (select group_concat(tbw.date) as receiveTime,
            sum(tbw.amount) as totalAmount,
            section_id,company_id
            from t_bond_relation tbr left join t_bond_water tbw on tbr.water_id = tbw.id
            where tbr.is_delete = 0 and tbw.is_delete = 0 and tbr.status = '2'
        <if test="condition.sendStartTime != null and condition.sendStartTime!='' ">
            and tbw.date <![CDATA[ >= ]]> #{condition.sendStartTime}
        </if>
        <if test="condition.sendEndTime != null and condition.sendEndTime!='' ">
            and tbw.date <![CDATA[ <= ]]> #{condition.sendEndTime}
        </if>
        group by tbr.section_id,tbr.company_id) t1
        left join t_project_bid_section tpbs on t1.section_id = tpbs.id
        left join t_project p on tpbs.project_id = p.id
        left join t_project_entrust_user tpe on p.id = tpe.project_id
        left join t_apply_info tpi on t1.section_id = tpi.sub_id  and t1.company_id = tpi.company_id
        LEFT JOIN ( SELECT max(id) as id ,section_id,seal_time  FROM t_bid_win_notice where `status` = 5  and bid_round = 2 AND is_delete = 0 group by section_id,seal_time ) tbwn ON t1.section_id = tbwn.section_id
        LEFT JOIN t_bid_win_people tbwp ON tbwn.id = tbwp.winbid_id
        AND t1.company_id = tbwp.tenderer_id
        AND tbwp.win_people_type = 1
        left join t_company tc on t1.company_id = tc.id
        left join t_bond_refund tbr on t1.section_id = tbr.section_id  and t1.company_id = tbr.company_id
        left join(select refund_id,sum(IF(status=3, IF(refund_money >0, bond_money+agency_fee , agency_fee), 0 )) as alreadyAmount from t_bond_refund_details where is_delete ='0' group by refund_id ) t2 on tbr.id = t2.refund_id
        where t1.totalAmount - ifnull(t2.alreadyAmount,0) >0
        <if test="condition.keyWords != null and condition.keyWords!='' ">
            and (p.project_name like concat( '%', #{condition.keyWords}, '%' ) or p.purchase_number like concat( '%', #{condition.keyWords}, '%' ))
        </if>
        <if test="condition.companyName != null and condition.companyName!='' ">
            and tc.company_name like concat( '%', #{condition.companyName}, '%' )
        </if>
        <if test="condition.deptId != null and condition.deptId.size > 0">
            AND tpe.department_id in
            <foreach collection="condition.deptId" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="condition.dataScope!= null and condition.dataScope!='' ">
            ${condition.dataScope}
        </if>
    </select>
</mapper>
