package com.hzw.sunflower.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hzw.sunflower.common.Paging;
import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.controller.request.*;
import com.hzw.sunflower.controller.response.*;
import com.hzw.sunflower.dto.SealApplicationDTO;
import com.hzw.sunflower.entity.JwtUser;
import com.hzw.sunflower.entity.SealApplication;
import com.hzw.sunflower.entity.condition.SealApplicationCondition;

import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;


/**
* SealApplicationService接口
*
*/
public interface SealApplicationService extends IService<SealApplication> {
    /**
     * 根据条件分页查询列表
     *
     * @param condition 查询条件
     * @param jwtUser
     * @return 分页信息
     */
    IPage<SealApplicationDTO> findInfoByCondition(SealApplicationCondition condition, JwtUser jwtUser);



    /**
     * 项目用印列表
     * @param condition
     * @return
     */
    IPage<SealApplicationDTO> listProjectInfo( SealApplicationCondition condition);

    /**
    * 根据主键ID查询信息
    *
    * @param id 主键ID
    * @return SealApplication信息
    */
    SealApplicationVO getInfoById(Long id);

    /**
    * 新增
    *
    * @param sealApplication
    * @return 是否成功
    */
    Result<Boolean> addInfo(SealApplicationREQ sealApplication);

    /**
    * 修改单位公司 信息
    *
    * @param sealApplication
    * @return 是否成功
    */
    Result<Boolean> updateInfo(SealApplicationREQ sealApplication);

    /**
     *
     * @param id
     * @return
     */
    Result<Boolean> withdrawApplication(Long id);

    /**
     * 取消申请
     * @param id
     * @return
     */
    Result<Boolean> deleteById(Long id);
    /**
     * 获取处理列表
     * @param req
     * @return
     */
    IPage<SealApplicationHandleVO> selectSealHandleVO(SealApplicationHandleREQ req);


    void  exportSealHandleVO(HttpServletResponse response, SealApplicationHandleREQ req);

    /**
     * 查询审批列表
     * @param appingTaskREQ
     * @return
     */
    Result<Paging<SealAppingTaskVO>> queryApproveList(AppingTaskREQ appingTaskREQ);

    /**
     * 审批数据
     * @param req
     * @return
     */
    Result<Boolean> reviewSeal(SealApprovalREQ req);

    Boolean saveInfo(SealApplicationREQ sealReq);


    /**
     * 根据标段获签章文件数据
     * @param req
     * @return
     */
    List<SealFilesVO> getSealFileBySection(SealFileSectionREQ req);

    /**
     * 是否为最后一级审批
     * @param appingTaskREQ
     * @return
     */
    Result<Boolean> isEndReview(SealApprovalREQ appingTaskREQ);

    /**
     * 获取签章文件
     * @param applyId
     * @return
     */
    List<SealFileVO> getSealFileList(Long applyId);

    /**
     * 根据条件分页查询用印申请列表-- 处长，分管领导 只可以查看
     * @param condition
     * @param jwtUser
     * @return
     */
    IPage<SealApplicationDTO> listOnly(SealApplicationCondition condition, JwtUser jwtUser);

    Boolean checkSymbolOnly(String symbol, Long id);

    /**
     * 根据中标通知书id查询用印申请
     * @param id
     * @return
     */
    SealApplication getByNoticeId(Long id);

    /**
     * 修改中标通知书用印申请数据
     *
     * @param byId1
     * @param sealTime
     * @return
     */
    Boolean updateSealInfo(SealApplication byId1, Date sealTime);
}