///***
/// * 路由映射
/// */

import 'package:flutter/material.dart';
import 'package:flutter_main_app/src/components/CustomAppBar.dart';

import 'package:flutter_main_app/src/widgets/user/LoginPage.dart';
import 'package:flutter_main_app/src/widgets/user/RegisterPage.dart';
import 'package:flutter_main_app/src/widgets/user/ResetPasswordPage.dart';

import 'package:flutter_main_app/src/widgets/root.dart';
import 'package:flutter_main_app/src/widgets/home/<USER>';
import 'package:flutter_main_app/src/widgets/home/<USER>';
import 'package:flutter_main_app/src/widgets/bulletindetail/TenderBulletinDetail.dart';
import 'package:flutter_main_app/src/widgets/bulletindetail/WinCandidateBulletin.dart';
import 'package:flutter_main_app/src/widgets/bulletindetail/WinBidBulletinDetail.dart';
import 'package:flutter_main_app/src/widgets/bulletindetail/ReminderSettings.dart';

import 'package:flutter_main_app/src/widgets/info/InfoIndexWidget.dart';
import 'package:flutter_main_app/src/widgets/info/InfoKeywordSettingsWidget.dart';
import 'package:flutter_main_app/src/widgets/info/InfoDataViewWidget.dart';

import 'package:flutter_main_app/src/widgets/market/MarketIndexWidget.dart';
import 'package:flutter_main_app/src/widgets/market/SubjectHomeWidget.dart';
import 'package:flutter_main_app/src/widgets/market/SubjectBasicWidget.dart';
import 'package:flutter_main_app/src/widgets/market/SubjectPerformanceWidget.dart';
import 'package:flutter_main_app/src/widgets/market/SubjectQualificationWidget.dart';
import 'package:flutter_main_app/src/widgets/market/SubjectStaffWidget.dart';

import 'package:flutter_main_app/src/widgets/datareport/DataReportIndexWidget.dart';

import 'package:flutter_main_app/src/widgets/profile/ProfileIndexWidget.dart';
import 'package:flutter_main_app/src/widgets/profile/ProfilePersonalInformationIndex.dart';
import 'package:flutter_main_app/src/widgets/profile/ProfileCollectionIndex.dart';
import 'package:flutter_main_app/src/widgets/profile/ProfileReminderIndex.dart';
import 'package:flutter_main_app/src/widgets/profile/ProfileAboutUsIndex.dart';
import 'package:flutter_main_app/src/widgets/profile/ProfileSettingsIndex.dart';
import 'package:flutter_main_app/src/widgets/profile/settings/ChangePassword.dart';
import 'package:flutter_main_app/src/widgets/profile/settings/ChangeMobileNumber.dart';
import 'package:flutter_main_app/src/widgets/profile/settings/FeedBack.dart';

typedef ArgumentWidgetBuilder = Widget Function(BuildContext context, { Object arguments });

class RouterModel {

  const RouterModel();

  static final Map<String, ArgumentWidgetBuilder> router = {

    /// * 登录、注册、重置密码
    LoginPageWidget.routeName: (context, { arguments }) => LoginPageWidget(),
    RegisterPageWidget.routeName: (context, { arguments }) => RegisterPageWidget(),
    ResetPasswordPageWidget.routeName: (context, { arguments }) => ResetPasswordPageWidget(),
  
    /// * 首页
    HomePageWidget.routeName: (context, { arguments }) => HomePageWidget(),
    SearchEntranceWidget.routeName: (context, { arguments }) => SearchEntranceWidget(),
    SearchResultWidget.routeName: (context, { arguments }) => SearchResultWidget(),
    TenderBulletinDetail.routeName: (context, { arguments }) => TenderBulletinDetail(),
    WinCandidateBulletin.routeName: (context, { arguments }) => WinCandidateBulletin(),
    WinBidBulletinDetail.routeName: (context, { arguments }) => WinBidBulletinDetail(),
    ReminderSettings.routeName: (context, { arguments }) => ReminderSettings(),

    /// * 信息订阅
    InfoIndexWidget.routeName: (context, { arguments }) => InfoIndexWidget(),
    InfoKeywordSettingsWidget.routeName: (context, { arguments }) => InfoKeywordSettingsWidget(),
    InfoDataViewWidget.routeName: (context, { arguments }) => InfoDataViewWidget(),

    /// * 业主信息
    MarketIndexWidget.routeName: (context, { arguments }) => MarketIndexWidget(),
    SubjectHomeWidget.routeName: (context, { arguments }) => SubjectHomeWidget(),
    SubjectBasicWidget.routeName: (context, { arguments }) => SubjectBasicWidget(),
    SubjectPerformanceWidget.routeName: (context, { arguments }) => SubjectPerformanceWidget(),
    SubjectQualificationWidget.routeName: (context, { arguments }) => SubjectQualificationWidget(),
    SubjectStaffWidget.routeName: (context, { arguments }) => SubjectStaffWidget(),

    /// * 数据报告
    DataReportIndexWidget.routeName: (context, { arguments }) => DataReportIndexWidget(),

    /// * 我的
    ProfileIndexWidget.routeName: (context, { arguments }) => ProfileIndexWidget(),
    ProfilePersonalInformationIndex.routeName: (context, { arguments }) => ProfilePersonalInformationIndex(),
    ProfileCollectionIndex.routeName: (context, { arguments }) => ProfileCollectionIndex(),
    ProfileReminderIndex.routeName: (context, { arguments }) => ProfileReminderIndex(),
    ProfileAboutUsIndex.routeName: (context, { arguments }) => ProfileAboutUsIndex(),
    ProfileSettingsIndex.routeName: (context, { arguments }) => ProfileSettingsIndex(),
    ChangePassword.routeName: (context, { arguments }) => ChangePassword(),
    ChangeMobileNumber.routeName: (context, { arguments }) => ChangeMobileNumber(),
    FeedBack.routeName: (context, { arguments }) => FeedBack(),

  };

  static Route<dynamic> generateRoute(RouteSettings settings) {
    final String name = settings.name;
    final ArgumentWidgetBuilder argumentWidgetBuilder = RouterModel.router[name];

    Route route;

    if (argumentWidgetBuilder != null) {
      if (settings.arguments != null) {
        route = MaterialPageRoute(
          builder: (context) => argumentWidgetBuilder(context, arguments: settings.arguments),
        );
      } else {
        route = MaterialPageRoute(
          builder: (context) => argumentWidgetBuilder(context),
        );
      }
    } else {
      route = MaterialPageRoute(
        builder: (_) => Route404NotFound(name: settings.name),
      );
    }

    return route;
  }

}

class Route404NotFound extends StatelessWidget {

  const Route404NotFound({
    Key key,
    @required this.name,
  }) : super(key: key);

  final String name;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: '404 Not Found',
      ),
      body: Center(
        child: Text('No route defined for "$name"'),
      ),
    );
  }

}
