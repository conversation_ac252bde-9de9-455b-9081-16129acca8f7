package com.hzw.sunflower.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hzw.sunflower.dto.SealApplicationInfoDTO;
import com.hzw.sunflower.entity.SealApplicationInfo;
import com.hzw.sunflower.entity.condition.SealApplicationInfoCondition;

import java.util.List;

/**
* SealApplicationInfoMapper接口
*
*/
public interface SealApplicationInfoMapper extends BaseMapper<SealApplicationInfo> {

}