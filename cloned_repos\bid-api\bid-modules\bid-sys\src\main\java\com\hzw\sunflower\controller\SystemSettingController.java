package com.hzw.sunflower.controller;

import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.common.annotation.RepeatSubmit;
import com.hzw.sunflower.entity.SystemSetting;
import com.hzw.sunflower.service.SystemSettingService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Api(tags = "系统基本信息配置")
@RestController
@RequestMapping("/systemSetting")
public class SystemSettingController {

    @Autowired
    SystemSettingService systemSettingService;

    @ApiOperation(value = "保存")
    @ApiImplicitParam(name = "systemSetting", value = "参数", required = true, dataType = "SystemSetting", paramType = "body")
    @PostMapping("/insertSystemSetting")
    @RepeatSubmit
    public Object insertSystemSetting(@RequestBody SystemSetting systemSetting) {
        Boolean bool  = systemSettingService.insertSystemSetting(systemSetting);
        return Result.okOrFailed(bool);
    }

    @ApiOperation(value = "回显")
    @ApiImplicitParam(name = "req", value = "参数", required = true, dataType = "SystemSetting", paramType = "body")
    @PostMapping("/querySystemSetting")
    public Object querySystemSetting(@RequestBody SystemSetting req) {
        Object systemSetting  = systemSettingService.querySystemSetting(req);
        return Result.ok(systemSetting);
    }
}
