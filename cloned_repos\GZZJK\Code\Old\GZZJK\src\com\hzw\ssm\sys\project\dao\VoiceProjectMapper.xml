<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper 
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hzw.ssm.sys.project.dao.VoiceProjectMapper">
	<!-- 根据项目创建人id获得项目信息 -->
	<select id="queryPageVoiceProjectInitList" resultType="ProjectEntity"
		parameterType="ProjectEntity">
		select
		projectId,projectNo,projectName,phone,bidTime,bidDuration,bidAddress,purpose,tender,agent,
		remark,status,method,conditionId,manager,DECIMATIONBATCH,AUDITTYPE as
		auditType ,modifyType from(
		select p.project_id as
		projectId,p.project_no as
		projectNo,p.project_name as
		projectName,p.phone,p.bid_time as bidTime,p.bid_duration as
		bidDuration,p.bid_address as bidAddress,p.purpose as purpose,
		p.tender,p.agent,p.remark,p.status,c.method as method,c.id as
		conditionId,
		decode((select u.user_name from ts_user u where u.user_id
		= p.manager and
		u.delete_flag = 0),'',p.manager,
		(select u.user_name
		from ts_user u where u.user_id = p.manager and
		u.delete_flag = 0)) as
		manager,P.DECIMATIONBATCH,
		p.AUDITTYPE,
		Row_Number() OVER (partition by
		P.DECIMATIONBATCH ORDER BY p.project_no
		asc,p.project_id)
		RN,P.Modify_Type as modifyType
		from t_project p
		left join t_condition c
		on p.project_id = c.project_id
		LEFT JOIN (SELECT COUNT(1) AS
		NUM,ER.DECIMATIONBATCH FROM T_EXTRACT_RESULT
		ER where ER.DELETE_FLAG=0
		GROUP BY ER.DECIMATIONBATCH)F ON
		F.DECIMATIONBATCH = P.DECIMATIONBATCH
		where p.delete_flag = 0 and p.create_user=#{create_id}
		AND c.METHOD =
		#{method}

		<if test="null != projectName and '' != projectName">
			and p.project_name like '%${projectName}%'
		</if>
		<if test="null != projectNo and '' != projectNo">
			and p.project_no like '%${projectNo}%'
		</if>
		<if test="null != bidStartTime and '' != bidStartTime">
			and p.bid_time >= #{bidStartTime}
		</if>
		<if test="null != bidEndTime and '' != bidEndTime">
			and p.bid_time &lt;= #{bidEndTime}
		</if>
		<if test="null != status and '' != status">
			and p.status = #{status}
		</if>
		<if test="null != status_ and '' != status_">
			and p.status in (${status_})
		</if>
		<if test="tab !=null and tab != ''">
			<if test="tab ==1 or tab ==2">
				AND P.BID_TIME >=sysdate+30/24/60
			</if>
			<if test="tab==4">
				AND P.BID_TIME&lt;=sysdate+30/24/60
			</if>
		</if>
		order by p.create_time desc,p.project_no
		) r1 where r1.rn=1
			And<!-- 没有被取消的 -->
			r1.modifyType is null  or r1.modifyType = '1' or r1.modifyType = ''

	</select>

	<select id="queryPageEmergencyTreatmentList" resultType="ProjectEntity"
		parameterType="ProjectEntity">
		select
		projectId,projectNo,projectName,phone,bidTime,bidDuration,bidAddress,purpose,tender,agent,
		remark,status,method,conditionId,DECIMATIONBATCH from(
		select
		p.project_id as projectId,p.project_no as projectNo,p.project_name as
		projectName,p.phone,p.bid_time as bidTime,p.bid_duration as
		bidDuration,p.bid_address as bidAddress,p.purpose as purpose,
		p.tender,p.agent,p.remark,p.status,c.method as method,c.id as
		conditionId,p.DECIMATIONBATCH,
		Row_Number() OVER (partition by
		p.DECIMATIONBATCH ORDER BY p.project_no
		asc,p.project_id) RN
		from
		t_project p
		left join t_condition c on p.project_id = c.project_id
		LEFT
		JOIN (SELECT T.DECIMATIONBATCH, MAX(CALL_TIME) AS CALL_TIME FROM
		T_EXTRACT_RESULT T WHERE T.JOIN_STATUS=3 GROUP BY T.DECIMATIONBATCH)
		RE
		ON p.Decimationbatch = RE.DECIMATIONBATCH
		where p.delete_flag = 0 and
		p.create_user=#{create_id}
		AND c.METHOD = #{method}
		<if test="null != status and '' != status">
			and p.status = #{status}
		</if>
		<if test="null != projectName and '' != projectName">
			and p.project_name like '%${projectName}%'
		</if>
		<if test="null != decimationBatch and '' != decimationBatch">
			and p.DECIMATIONBATCH like '%${decimationBatch}%'
		</if>
		AND RE.CALL_TIME+30/24/60 &lt; sysdate
		order by p.create_time
		desc,p.project_no
		) r1 where r1.rn=1

	</select>

	<!-- 添加项目变更记录表 -->
	<insert id="addProjectChangeRecords" parameterType="ProjectChangesEntity">
		INSERT INTO
		T_PROJECT_CHANGES(
		CHANGE_ID,
		DECIMATIONBATCH,
		OLD_BID_TIME,
		NEW_BID_TIME,
		OLD_BID_ADDRESS,
		NEW_BID_ADDRESS,
		CHANGE_REASON,
		CHANGE_TIME,
		CHANGE_STATUS,
		CHANGE_USER,
		EXPERT_ID,
		SORT
		) VALUES (
		#{changeId,jdbcType=VARCHAR}
		,#{decimationBatch,jdbcType=VARCHAR}
		,#{oldBidTime,jdbcType=TIMESTAMP}
		,#{newBidTime,jdbcType=TIMESTAMP}
		,#{oldBidAddress,jdbcType=VARCHAR}
		,#{newBidAddress,jdbcType=VARCHAR}
		,#{changeReason,jdbcType=VARCHAR}
		,#{changeTime,jdbcType=TIMESTAMP}
		,#{changeStatus,jdbcType=NUMERIC}
		,#{changeUser,jdbcType=VARCHAR}
		,#{expertId,jdbcType=VARCHAR}
		,#{sort,jdbcType=NUMERIC}
		)
	</insert>

	<!-- 更新项目信息 -->
	<update id="updateChangeProject" parameterType="ProjectEntity">
		update t_project set
		<if test="bidTime!=null and bidTime!=''">
			bid_time=#{bidTime},
		</if>
		<if test="bidAddress!=null and bidAddress!=''">
			bid_address=#{bidAddress},
		</if>
		<if test="modify_time!=null and modify_time!=''">
			modifytime=#{modify_time},
		</if>
		MODIFY_TYPE=#{modifyType}
		where
		<choose>
			<when test="decimationBatch!=null and decimationBatch!=''">
				decimationBatch=#{decimationBatch}
			</when>
			<otherwise>
				project_id=#{projectId}
			</otherwise>
		</choose>
	</update>

	<!-- 获得当前项目变更记录表最大批次号 -->
	<select id="getMaxSort" resultType="int" parameterType="ProjectChangesEntity">
		SELECT
		MAX(A.SORT) FROM T_PROJECT_CHANGES A WHERE
		A.DECIMATIONBATCH=#{decimationBatch}
	</select>
	
	<!-- 变更项目需要发送信息的专家集合 -->
	<select id="queryExpertList" resultType="ExpertInfoEntity" parameterType="ResultEntity">
		SELECT 
			B.ID AS id,B.USER_ID AS user_id,B.USER_NAME AS user_name,B.SEX AS sex,
			B.BIRTHDAY AS birthday,B.POLITICS AS politics,B.ID_TYPE AS id_type,
       		B.ID_NO AS id_no,B.ID_FILEID AS id_fileid,B.SCHOOL AS school,B.MAJOR AS major,
       		B.CERTIFICATE_FILEID AS certificate_fileid,B.EDUCATIONS AS educations,
       		B.DEGREE AS degree,B.GRADE AS grade,B.PROVINCE AS province,B.CITY AS city,
       		B.ZONE AS zone,B.COMPANY AS company,B.COMPANY_ADDR AS company_addr,
       		B.COMPANY_PHONE AS company_phone,B.COMPANY_ZIPCODE AS company_zipcode,
       		B.MOBILEPHONE AS mobilephone,B.EMAIL AS email,B.SPECIAL_SKILL AS special_skill,
       		B.BID_EXPERIENCE AS bid_experience,B.TRAINING_EXPERIENCE AS training_experience,
       		B.REMARK AS remark,B.STATUS AS status,B.CREATE_TIME AS create_time,
       		B.MODIFY_TIME AS modify_time,B.CERTIFICATE AS certificate,B.AUDIT_TIME AS audit_time,
       		B.CERTIFICATE_TIME AS certificate_time,B.EXPIRE_TIME expire_time,
       		B.EVAL_SCORE AS eval_score,B.DISTRICT AS district,B.EXPERT_NUM AS expert_num,
       		B.PHOTO_FILEID AS photo_fileid,B.TECHNICAL_TITAL AS technical_tital,
       		B.POSITION AS position,B.QQ_NUM AS qq_num,B.TECHNICAL_FILED AS technical_filed,
       		B.DOCUMENT_NO AS document_no,B.OLD_CERTIFICATE AS old_certificate,B.OLD_PHOTO AS old_photo,
       		B.OLD_TECHNICAL AS old_technical,B.OLD_ID AS old_id,B.ENTER_FLAG AS enterFlag,
       		B.OUT_TIME AS outTime,B.OUT_REASON AS outReason,B.PAUSE_STARTTIME AS pause_startTime,
       		B.PAUSE_ENDTIME AS pause_endTime,B.PAUSE_NUMBER AS pause_number
		FROM T_EXTRACT_RESULT A
			LEFT JOIN T_EXPERT_INFO B 
			ON A.EXPERT_ID = B.USER_ID 
		WHERE 
			A.DECIMATIONBATCH=#{decimationBatch} 
			AND A.JOIN_STATUS=#{joinStatus}
	</select>
	

	<!-- 查询项目变更记录 -->
	<select id="queryPageProjectChangesList" resultType="ProjectEntity"
		parameterType="ProjectEntity">
		select
		projectId,projectNo,projectName,phone,bidTime,bidDuration,bidAddress,purpose,tender,agent,
		remark,status,method,conditionId,manager,DECIMATIONBATCH,AUDITTYPE as
		auditType ,modifyType from(
		select p.project_id as
		projectId,p.project_no as
		projectNo,p.project_name as
		projectName,p.phone,p.bid_time as bidTime,p.bid_duration as
		bidDuration,p.bid_address as bidAddress,p.purpose as purpose,
		p.tender,p.agent,p.remark,p.status,c.method as method,c.id as
		conditionId,
		decode((select u.user_name from ts_user u where u.user_id
		= p.manager and
		u.delete_flag = 0),'',p.manager,
		(select u.user_name
		from ts_user u where u.user_id = p.manager and
		u.delete_flag = 0)) as
		manager,P.DECIMATIONBATCH,
		p.AUDITTYPE,
		Row_Number() OVER (partition by
		P.DECIMATIONBATCH ORDER BY p.project_no
		asc,p.project_id)
		RN,P.Modify_Type as modifyType
		from t_project p
		left join t_condition c
		on p.project_id = c.project_id
		LEFT JOIN (SELECT COUNT(1) AS
		NUM,ER.DECIMATIONBATCH FROM T_EXTRACT_RESULT
		ER where ER.DELETE_FLAG=0
		GROUP BY ER.DECIMATIONBATCH)F ON
		F.DECIMATIONBATCH = P.DECIMATIONBATCH
		where p.delete_flag = 0 and p.create_user=#{create_id}
		AND c.METHOD =
		#{method}
		<if test="null != projectName and '' != projectName">
			and p.project_name like '%${projectName}%'
		</if>
		<if test="null != projectNo and '' != projectNo">
			and p.project_no like '%${projectNo}%'
		</if>
		<if test="null != bidStartTime and '' != bidStartTime">
			and p.bid_time >= #{bidStartTime}
		</if>
		<if test="null != bidEndTime and '' != bidEndTime">
			and p.bid_time &lt;= #{bidEndTime}
		</if>
		<if test="null != status and '' != status">
			and p.status = #{status}
		</if>
		<if test="null != status_ and '' != status_">
			and p.status in (${status_})
		</if>
		<if test="tab !=null and tab != ''">
			<if test="tab ==1 or tab ==2">
				AND P.BID_TIME >=sysdate+30/24/60
			</if>
			<if test="tab==4">
				AND P.BID_TIME&lt;=sysdate+30/24/60
			</if>
		</if>
		order by p.create_time desc,p.project_no
		) r1 where r1.rn=1
		<if test="tab == 5">
			and (select Count(1) from T_PROJECT_CHANGES C where C.DECIMATIONBATCH =
			r1.DECIMATIONBATCH)>0
		</if>
	</select>
	
	<!-- 查询变更记录详情集合 -->
	<select id="queryProChangeList" resultType="ProjectChangesEntity" parameterType="ProjectChangesEntity">
		SELECT
			B.CHANGE_ID AS changeId,
			B.DECIMATIONBATCH AS decimationBatch, 
			B.OLD_BID_TIME AS oldBidTime,
			B.NEW_BID_TIME AS newBidTime,
			B.OLD_BID_ADDRESS AS oldBidAddress,
			B.NEW_BID_ADDRESS AS newBidAddress,
			B.CHANGE_REASON AS changeReason,
			B.CHANGE_TIME AS changeTime,
			B.CHANGE_STATUS AS changeStatus,
			B.CHANGE_USER AS changeUser,
			B.SORT AS sort
		FROM 
			T_PROJECT_CHANGES B 
		WHERE 
			B.DECIMATIONBATCH = #{decimationBatch} 
		ORDER BY 
			B.SORT
	</select>
	
	<!-- 查询语音短信模板内容 -->
	<select id="querySysMessageTemplateList" resultType="ConditionEntity" parameterType="ConditionEntity">
		SELECT 
       		A.TEMPLATE_ID AS templateId,A.TEMPLATE_TYPE AS templateType,
       		A.TEMPLATE_CONTENT AS templateContent,
       		A.DELETE_FLAG AS deleteFlag,A.SMSCONTENT AS smsContent,
      		A.VOICESMSCONTENT AS voiceSmsContent 
		FROM 
			SYS_MESSAGE_TEMPLATE A
		WHERE 
			A.TEMPLATE_TYPE=#{templateType} 
			AND A.DELETE_FLAG=#{deleteFlag} 
			AND A.IS_VALID=#{isValid}
	</select>
</mapper>