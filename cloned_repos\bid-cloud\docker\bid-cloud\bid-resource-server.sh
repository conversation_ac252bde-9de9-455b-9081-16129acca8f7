#!/bin/sh
#cp TEMPLATE FILE
docker stop bid-resource-server || true
docker rm bid-resource-server || true
docker rmi registry.cn-hangzhou.aliyuncs.com/hzw-jtcc/jtcc:bid-resource-server-1.0.0 || true
docker pull registry.cn-hangzhou.aliyuncs.com/hzw-jtcc/jtcc:bid-resource-server-1.0.0
docker run -itd --net=host  --log-opt max-size=100m --log-opt max-file=3  --name bid-resource-server -v /home/<USER>/plan1/bid-cloud/config/bid-resource-server:/hzw/resource/config -v /data/log:/hzw/resource/logs -v /data/temporary:/data/temporary  registry.cn-hangzhou.aliyuncs.com/hzw-jtcc/jtcc:bid-resource-server-1.0.0
