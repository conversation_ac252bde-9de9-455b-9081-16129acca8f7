package com.hzw.sunflower.controller.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class BidWinSupplierVo implements Serializable {

    @ApiModelProperty(value = "保证金金额")
    private String bondMoney;

    @ApiModelProperty(value = "评标情况")
    private String bidType;

    @ApiModelProperty(value = "中标情况")
    private String bidStatus;

    @ApiModelProperty(value = "合同签订情况")
    private String isSign;

    @ApiModelProperty(value = "中标通知书发放时间")
    private Date sendTime;

    private Long sectionId;

    private Long companyId;



}
