package com.hzw.sunflower.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hzw.sunflower.common.BaseBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 保证金关联关系表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-27
 */
@Data
@TableName("t_bond_relation")
@ApiModel(value = "BondRelation对象", description = "保证金关联关系表")
public class BondRelation extends BaseBean implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("关联项目id")
    @TableField("project_id")
    private Long projectId;

    @ApiModelProperty("关联包id")
    @TableField("section_id")
    private Long sectionId;

    @ApiModelProperty("关联流水id")
    @TableField("water_id")
    private Long waterId;

    @ApiModelProperty("关联供应商id")
    @TableField("company_id")
    private Long companyId;

    @ApiModelProperty("关联状态（0.未关联 1.确认关联 2.已关联）")
    @TableField("status")
    private Integer status;

    @ApiModelProperty("是否自动关联 0.否 1.是")
    @TableField("is_auto")
    private Integer isAuto;

}
