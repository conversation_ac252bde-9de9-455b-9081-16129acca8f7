package com.hzw.sunflower.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzw.sunflower.common.redis.RedisCache;
import com.hzw.sunflower.constant.GeneralConstants;
import com.hzw.sunflower.dao.IpLibraryMapper;
import com.hzw.sunflower.entity.IpLibrary;
import com.hzw.sunflower.service.IpLibraryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * @ClassName:IpLibraryServiceImpl
 * @Auther: lijinxin
 * @Description: IP库
 * @Date: 2024/8/27 10:10
 * @Version: v1.0
 */
@Service
@Slf4j
public class IpLibraryServiceImpl extends ServiceImpl<IpLibraryMapper, IpLibrary> implements IpLibraryService {


    @Autowired
    private RedisCache redisCache;

    /**
     * ip地址
     * @param ipAddress
     * @param ipLocation
     * @return
     */
    @Override
    public String updateIpAddress(String ipAddress, String ipLocation) {
        String key = "ip_library_"+ipAddress;
        String s = "XX XX";
        Object cacheObject = redisCache.getCacheObject(key);
        if(null != cacheObject){
            s = cacheObject.toString();
        }else{
            LambdaQueryWrapper<IpLibrary> lqw = new LambdaQueryWrapper<>();
            lqw.eq(IpLibrary::getIpAddress,ipAddress);
            List<IpLibrary> list = this.list(lqw);
            if(s.equals(ipLocation)){
                if(!list.isEmpty()){
                    s = list.get(0).getIpLocation();
                    redisCache.setCacheObject(key,s, GeneralConstants.REDIS_CACHE_TIME_OUT, TimeUnit.SECONDS);
                }
            }else{
                if(!list.isEmpty()){
                    IpLibrary ipLibrary = list.get(0);
                    if(!ipLocation.equals(ipLibrary.getIpLocation())){
                        ipLibrary.setIpLocation(ipLocation);
                        this.updateById(ipLibrary);
                    }
                }else{
                    IpLibrary ipLibrary = new IpLibrary();
                    ipLibrary.setIpAddress(ipAddress);
                    ipLibrary.setIpLocation(ipLocation);
                    this.save(ipLibrary);
                }
                s = ipLocation;
                redisCache.setCacheObject(key,s, GeneralConstants.REDIS_CACHE_TIME_OUT, TimeUnit.SECONDS);
            }
        }
        return s;
    }
}
