///***
/// * 数据报告 视图模型
/// */

import 'package:flutter/material.dart';

import 'package:flutter_main_app/src/utils/ApiUtils.dart';

import 'package:flutter_main_app/src/models/dataReport/DataReportModel.dart';

class DataReportViewModel extends ChangeNotifier {

  DataReportViewModel();

  /// * 是否正在加载数据
  bool _isLoading = false;

  bool get isLoading => _isLoading;

  set isLoading(bool isLoading) {
    _isLoading = isLoading;

    notifyListeners();
  }

  /// * 当前年份
  int _year;

  int get year => _year;

  set year(int year) {
    _year = year;

    fetchReportData();
  }

  /// * 报告数据
  DataReportModel _reportData;

  DataReportModel get reportData => _reportData;

  set reportData(DataReportModel reportData) {
    _reportData = reportData;

    notifyListeners();
  }

  ///***
  /// * 获取报告数据
  /// */
  Future<void> fetchReportData() async {
    isLoading = true;

    final DataReportModel newReportData = await ApiUtils.getReportData(year);

    reportData = newReportData;

    isLoading = false;
  }

}
