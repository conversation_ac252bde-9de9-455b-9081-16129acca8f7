<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hzw.ssm.applets.dao.BidEvaluationMapper">

    <resultMap id="BaseResultMap" type="BidEvaluation">
        <id column="id" property="id"/>
        <result column="user_id" property="user_id"/>
        <result column="major" property="major"/>
        <result column="year" property="year"/>
        <result column="delete_flag" property="delete_flag"/>
        <result column="cou_major" property="cou_major"/>
        <result column="get_time" property="getTime"/>
    </resultMap>

    <sql id="Base_Column_List">
		 t.id, t.user_id, t.major, t.year, t.delete_flag, t.cou_major, t.get_time
	</sql>

    <insert id="insertBidEvaluation" parameterType="BidEvaluation">
		insert into t_expert_major (
			id,
			user_id,
			major,
			year,
			delete_flag,
			cou_major,
			get_time
		)
		values(
			#{id,jdbcType=VARCHAR},
			#{user_id,jdbcType=VARCHAR},
			#{major,jdbcType=VARCHAR},
			#{year,jdbcType=VARCHAR},
			#{delete_flag,jdbcType=NUMERIC},
			#{cou_major,jdbcType=VARCHAR},
			#{getTime,jdbcType=VARCHAR}
		)
	</insert>

    <update id="updateBidEvaluation" parameterType="BidEvaluation">
        update t_expert_major
        <set>
            <if test="major != null and !&quot;&quot;.equals(major)">
                major = #{major},
            </if>
            <if test="getTime != null and !&quot;&quot;.equals(getTime)">
                get_time = #{getTime},
            </if>
        </set>
        where
            id = #{id}
    </update>

    <select id="getBidEvaluation" parameterType="BidEvaluation" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List"/>
        from
            t_expert_major t
        where
            id = #{id}
    </select>

    <select id="queryCountByExpertId" parameterType="BidEvaluation" resultType="int">
        select
            count(id)
        from
            t_expert_major t
        where
            delete_flag = 0
            and user_id = #{user_id}
    </select>

    <select id="queryBidEvaluation" parameterType="BidEvaluation" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List"/>
        from
            t_expert_major t
        where
            delete_flag = 0
            <if test="user_id != null and !&quot;&quot;.equals(user_id)">
                and user_id = #{user_id}
            </if>
            <if test="major != null and !&quot;&quot;.equals(major)">
                and major = #{major}
            </if>
    </select>

    <select id="queryEvaluationNumById" resultType="int">
        select
            count(t.id)
        from
            t_expert_major t
        where
            delete_flag = 0
            and user_id = #{expertId}
            and major is not null
    </select>

    <select id="getBidEvaluations" parameterType="BidEvaluation" resultMap="BaseResultMap">
        select
            (
                floor(
                ( months_between ( TO_DATE ( to_char ( sysdate, 'yyyy-MM-dd HH24:mi:ss' ), 'yyyy-mm-dd hh24:mi:ss' ), TO_DATE ( t.get_time, 'yyyy-mm-dd hh24:mi:ss' ) ) ) / 12
                )
            ) AS evaluationYears,
            <include refid="Base_Column_List"/>
        from
            t_expert_major t
        where
            t.user_id = #{user_id}
            and t.delete_flag = 0
        ORDER BY
            t.id asc
    </select>

    <update id="deleteBidEvaluation" parameterType="BidEvaluation">
		UPDATE
			t_expert_major
		SET
			delete_flag = 1
		where
			id = #{id}
	</update>

</mapper>
