package com.hzw.sunflower.controller.request;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 保证金申请关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-27
 */
@Data
@ApiModel(value = "BondWater对象", description = "保证金申请关联表")
public class BondApplyRelationReq  implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "项目id")
    private Long projectId;

    @ApiModelProperty(value = "标段id")
    private Long sectionId;

    @ApiModelProperty(value = "供应商id")
    private Long companyId;

    @ApiModelProperty(value = "申请内容")
    private String content;

    @ApiModelProperty(value = "1.待处理 2.已退回 3.已处理")
    private Integer status;

    @ApiModelProperty(value = "类型：1：申请关联  2：异常关联")
    private Integer type;

    private String remark;

    @ApiModelProperty(value = "付款户名")
    private String payCompanyName;

    @ApiModelProperty(value = "支付凭证文件id")
    private List<Long> fileIds;
}
