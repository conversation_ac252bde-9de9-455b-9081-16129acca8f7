package com.hzw.sunflower.controller.response;

import com.hzw.sunflower.dto.SealApplicationDTO;
import com.hzw.sunflower.dto.SealApplicationProjectDTO;
import com.hzw.sunflower.dto.SealFileDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
* SealApplication 返回实体
*
* <AUTHOR>
* @version 1.0.0
*/
@ApiModel(description = "SealApplication ")
@Data
public class SealApplicationVO extends SealApplicationDTO {

    @ApiModelProperty(value = "项目信息")
    private List<SealApplicationProjectDTO> projectDTOS;

    @ApiModelProperty(value = "文件信息")
    private List<SealFileDTO> fileDTOS;

}