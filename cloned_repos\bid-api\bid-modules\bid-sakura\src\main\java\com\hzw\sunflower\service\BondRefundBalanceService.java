package com.hzw.sunflower.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hzw.sunflower.entity.BondRefundBalance;
import com.hzw.sunflower.entity.BondRefundDetails;
import com.hzw.sunflower.entity.BondWater;
import com.hzw.sunflower.entity.BondWaterlabel;

import java.util.List;


/**
 * <p>
 * 保证金退还余额表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-28
 */
public interface BondRefundBalanceService extends IService<BondRefundBalance> {

    /**
     * 查询保证金退款余额
     * @return
     */
    boolean checkBondBalance(List<BondRefundDetails> list);

    /**
     * 查询保证金退款余额
     * @return
     */
    void changeTotalAmount(Long waterId, Integer label, BondWaterlabel lastLabel);

    /**
     * 更新/新增流水总额
     * @return
     */
    void batchSaveOrUpdateChange(List<BondWater> reqList);

    /**
     * 更新已退还保证金
     * @return
     */
    void batchUpdateRefund(List<BondRefundDetails> reqList);

}
