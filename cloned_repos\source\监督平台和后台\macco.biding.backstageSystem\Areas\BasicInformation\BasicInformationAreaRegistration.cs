﻿using System.Web.Mvc;

namespace macco.biding.backstageSystem.Areas.BasicInformation
{
    public class BasicInformationAreaRegistration : AreaRegistration
    {
        public override string AreaName
        {
            get
            {
                return "BasicInformation";
            }
        }

        public override void RegisterArea(AreaRegistrationContext context)
        {
            context.MapRoute(
                "BasicInformation_default",
                "BasicInformation/{controller}/{action}/{id}",
                new { action = "Index", id = UrlParameter.Optional }
            );
        }
    }
}
