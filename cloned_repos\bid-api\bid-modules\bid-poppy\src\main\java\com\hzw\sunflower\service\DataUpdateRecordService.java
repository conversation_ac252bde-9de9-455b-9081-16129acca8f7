package com.hzw.sunflower.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hzw.sunflower.controller.project.request.DataUpdateExamineReq;
import com.hzw.sunflower.controller.project.request.DataUpdateReq;
import com.hzw.sunflower.controller.project.response.DataUpdateRecordVo;
import com.hzw.sunflower.entity.DataUpdateRecord;
import com.hzw.sunflower.entity.JwtUser;
import com.hzw.sunflower.entity.condition.DataUpdateRecordCondition;
import com.hzw.sunflower.controller.project.response.DataUpdateRecordDetailsVo;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2022/9/5 10:52
 * @description：
 * @modified By：`
 * @version: 1.0
 */
public interface DataUpdateRecordService extends IService<DataUpdateRecord> {

    /**
     * 分页查询数据修改记录
     * @param condition
     * @return
     */
    IPage<DataUpdateRecordVo> listRecordPage(DataUpdateRecordCondition condition);

    /**
     * 新增数据修改
     * @param req
     * @return
     */
    Boolean addData(DataUpdateReq req);

    /**
     * 审核记录列表查询
     * @param condition
     * @return
     */
    IPage<DataUpdateRecordVo> queryList(DataUpdateRecordCondition condition);

    DataUpdateRecordDetailsVo getDetailsById(Long id);

    /**
     * 审核
     * @param req
     * @param jwtUser
     * @return
     */
    Boolean updateExamine(DataUpdateExamineReq req, JwtUser jwtUser);

    /**
     * 撤回
     * @param id
     * @param jwtUser
     * @return
     */
    Boolean updateWithdraw(Long id, JwtUser jwtUser);

    /**
     *报分管领导审批
     * @param req
     * @param jwtUser
     * @return
     */
    Boolean updateToLeaderApprove(DataUpdateExamineReq req, JwtUser jwtUser);

    /**
     * app数据修改待办
     * @param dataUpdateRecordCondition
     * @return
     */
    List<DataUpdateRecordVo> queryListForApp(DataUpdateRecordCondition dataUpdateRecordCondition);

    /**
     * 审核记录列表查询（app）
     * @param condition
     * @return
     */
    IPage<DataUpdateRecordVo> queryListApp(DataUpdateRecordCondition condition);
}
