package com.hzw.sunflower.service;

import com.hzw.sunflower.dto.BondPayInfoDto;
import com.hzw.sunflower.entity.BondPayInfo;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 保证金银行支付信息 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-26
 */
public interface BondPayInfoService extends IService<BondPayInfo> {

    /**
     * 获取待刷新的数据
     * @return
     */
    List<BondPayInfoDto> getRefreshData(Long refundId);

    /**
     * 获取待刷新的批量数据
     * @return
     */
    List<BondPayInfoDto> getBatchRefreshData(List<Long> list);
}
