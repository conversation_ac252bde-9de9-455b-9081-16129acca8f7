///***
/// * 主体资质 模型
/// */

import 'package:json_annotation/json_annotation.dart';

part 'SubjectQualificationModel.g.dart';

@JsonSerializable(explicitToJson: true)
class SubjectQualificationModel {

  SubjectQualificationModel({
    this.active,
    this.approvalInstitution,
    this.caNoDataKey,
    this.certificateNo,
    this.createdDate,
    this.dataKey,
    this.declarationPerson,
    this.expiryDate,
    this.modifiedDate,
    this.qualificationClass,
    this.qualificationGrade,
    this.qualificationID,
    this.qualificationName,
    this.qualificationNo,
    this.qualificationType,
    this.statusValue,
    this.subjectCode,
    this.subjectDataKey,
    this.subjectID,
    this.subjectType,
    this.validDate,
  });

  final bool active;

  final String approvalInstitution;

  final String caNoDataKey;

  final String certificateNo;

  final String createdDate;

  final String dataKey;

  final String declarationPerson;

  String expiryDate;

  final String modifiedDate;

  final String qualificationClass;

  final String qualificationGrade;

  final int qualificationID;

  final String qualificationName;

  final String qualificationNo;

  final String qualificationType;

  final int statusValue;

  final String subjectCode;

  final String subjectDataKey;

  final int subjectID;

  final String subjectType;

  final String validDate;

  factory SubjectQualificationModel.fromJson(Map<String, dynamic> json) => _$SubjectQualificationModelFromJson(json);

  Map<String, dynamic> toJson() => _$SubjectQualificationModelToJson(this);

}
