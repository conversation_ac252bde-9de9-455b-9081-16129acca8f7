package com.hzw.sunflower.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hzw.sunflower.controller.request.DocChangeRecordREQ;
import com.hzw.sunflower.dto.DocChangeRecordDTO;
import com.hzw.sunflower.entity.DocChangeRecord;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * TDocChangeRecordMapper接口
 */
public interface DocChangeRecordMapper extends BaseMapper<DocChangeRecord> {

    /**
     * 获取招标文件的变更次数
     *
     * @param docId
     * @return
     */
    List<Long> getTimesByDocId(@Param("docId") Long docId);

    /**
     * 获取招标文件变更记录
     *
     * @param req
     * @return
     */
    List<DocChangeRecordDTO> getDocRecord(@Param("req") DocChangeRecordREQ req);

    /**
     * 查询文件变更记录集合
     *
     * @param docId
     * @return
     */
    List<DocChangeRecordDTO> getListByDocId(@Param("docId") Long docId);


    /**
     * 查询标段变更记录集合
     *
     * @param bidId
     * @return
     */
    List<DocChangeRecordDTO> getListByBidId(@Param("bidId") Long bidId);
}
