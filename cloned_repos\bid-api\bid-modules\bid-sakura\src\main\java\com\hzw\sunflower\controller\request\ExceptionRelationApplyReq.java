package com.hzw.sunflower.controller.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2022/8/1 8:35
 * @description：异常关联申请/退回入参
 * @modified By：`
 * @version: 1.0
 */
@ApiModel(description = "异常关联申请/退回入参")
@Data
public class ExceptionRelationApplyReq implements Serializable {

    @ApiModelProperty(value = "异常关联申请id")
    private Long applyId;

    @ApiModelProperty(value = "退回原因")
    private String returnMsg;

    @ApiModelProperty(value = "同意备注")
    private String remark;

}
