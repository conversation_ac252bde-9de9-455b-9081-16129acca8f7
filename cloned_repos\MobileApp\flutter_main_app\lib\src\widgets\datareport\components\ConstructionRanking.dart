///***
/// * 年度施工类总排名
/// */

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import 'package:flutter_main_app/serviceLocator.dart';
import 'package:flutter_main_app/src/utils/FunctionUtils.dart';
import 'package:flutter_main_app/src/constants/theme.dart';
import 'package:flutter_main_app/src/constants/common.dart';

import 'package:flutter_main_app/src/viewModels/dataReport/DataReportViewModel.dart';

class ConstructionRanking extends StatefulWidget {

  ConstructionRanking();

  @override
  _ConstructionRanking createState() => _ConstructionRanking();

}

class _ConstructionRanking extends State<ConstructionRanking> {

  static const List<String> _constructionRankingTypes = <String>[
    '中标金额', '中标次数', '报名次数', '入围次数', '投标次数',
  ];

  static const Color _activeConstructionRankingTypeTabBackgroundColor = Color(0xffeb8080);
  static const Color _normalConstructionRankingTypeTabBackgroundColor = Color(0xfff0f0f0);

  int _constructionRankingTypeIndex = 0;

  static const TextStyle _tableCellTextStyle = TextStyle(
    fontSize: 12.0,
    color: themeTextColor,
  );

  static const BoxConstraints _tableCellBoxConstraints = BoxConstraints(
    minHeight: 38.0,
  );

  final DataReportViewModel _model = serviceLocator<DataReportViewModel>();

  List<String> _companies = [];

  List<String> _contents = [];

  @override
  void initState() {
    super.initState();

    final List<Map<String, dynamic>> amountRanking = _model.reportData?.constructionAmountRanking ?? [];

    Map<String, List<String>> amountData = _getAmountData(amountRanking);

    setState(() {
      _companies = List<String>.from(amountData['companies']);
      _contents = List<String>.from(amountData['contents']);
    });
  }

  ///***
  /// * 获取 中标金额 相关数据
  /// *
  /// * @param {List<Map<String, dynamic>>} amountRanking
  /// * @return {Map<String, List<String>>}
  /// */
  Map<String, List<String>> _getAmountData(List<Map<String, dynamic>> amountRanking) {
    final List<String> amountCompanies = <String>[];

    final List<String> amountContents = <String>[];

    for (int i = 0; i < amountRanking.length; i++) {
      final double amount = FunctionUtils.convertYuanToWanYuan(amountRanking[i]['amount'] ?? 0.00);

      amountCompanies.add(amountRanking[i]['name'] ?? defaultStringPlaceholder);

      amountContents.add('$amount 万元');
    }

    return <String, List<String>>{
      'companies': amountCompanies,
      'contents': amountContents,
    };
  }

  ///***
  /// * 获取 中标次数 相关数据
  /// *
  /// * @param {List<Map<String, dynamic>>} countRanking
  /// * @return {Map<String, List<String>>}
  /// */
  Map<String, List<String>> _getCountData(List<Map<String, dynamic>> countRanking) {
    final List<String> countCompanies = <String>[];

    final List<String> countContents = <String>[];

    for (int i = 0; i < countRanking.length; i++) {
      countCompanies.add(countRanking[i]['name'] ?? defaultStringPlaceholder);

      countContents.add('${countRanking[i]["count"] ?? defaultStringPlaceholder}');
    }

    return <String, List<String>>{
      'companies': countCompanies,
      'contents': countContents,
    };
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<DataReportViewModel>(
      builder: (context, model, child) {

        ///***
        /// * 表格数据
        /// */
        final List<Map<String, dynamic>> amountRanking = model.reportData?.constructionAmountRanking ?? [];

        final List<Map<String, dynamic>> countRanking = model.reportData?.constructionCountRanking ?? [];

        final Map<String, List<String>> amountData = _getAmountData(amountRanking);

        final Map<String, List<String>> countData = _getCountData(countRanking);

        final List<String> amountCompanies = amountData['companies'];

        final List<String> amountContents = amountData['contents'];

        final List<String> countCompanies = countData['companies'];

        final List<String> countContents = countData['contents'];

        return Container(
          padding: const EdgeInsets.only(left: 15.0, top: 30.0, right: 15.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              /// * 标题
              Text(
                '${model.year}年度施工类总排名列表',
                style: themeTitleTextStyle,
              ),
              /// * tab 切换按钮
              Padding(
                padding: const EdgeInsets.only(top: 14.0, bottom: 8.0),
                child: Row(
                  mainAxisSize: MainAxisSize.max,
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    ...List<Widget>.generate(
                      _constructionRankingTypes.length,
                      (int index) => Expanded(
                        child: InkWell(
                          onTap: () {
                            if (index == _constructionRankingTypeIndex) {
                              return;
                            }

                            setState(() {
                              switch (index) {
                                case 0:
                                  _constructionRankingTypeIndex = index;
                                  _companies = List<String>.from(amountCompanies);
                                  _contents = List<String>.from(amountContents);
                                  break;
                                case 1:
                                  _constructionRankingTypeIndex = index;
                                  _companies = List<String>.from(countCompanies);
                                  _contents = List<String>.from(countContents);
                                  break;
                                default:
                                  break;
                              }
                            });
                          },
                          splashColor: _activeConstructionRankingTypeTabBackgroundColor,
                          child: Container(
                            height: 30.0,
                            color: index == _constructionRankingTypeIndex
                              ? _activeConstructionRankingTypeTabBackgroundColor
                              : _normalConstructionRankingTypeTabBackgroundColor,
                            child: Center(
                              child: Text(
                                _constructionRankingTypes[index],
                                style: TextStyle(
                                  fontSize: 12.0,
                                  color: index == _constructionRankingTypeIndex
                                    ? Colors.white
                                    : themeTextColor,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              /// * 表格
              _companies.isEmpty
                ? Container(
                    padding: const EdgeInsets.symmetric(vertical: 2.0),
                    decoration: BoxDecoration(
                      border: Border.all(
                        color: themeBorderColor,
                      ),
                    ),
                    child: Center(
                      child: Text(
                        '暂无数据',
                        style: themeHintTextStyle,
                      ),
                    ),
                  )
                : Table(
                    columnWidths: {
                      0: FixedColumnWidth(26.0),
                      1: FlexColumnWidth(2.0),
                      2: FlexColumnWidth(1.0),
                    },
                    border: TableBorder.all(color: themeBorderColor),
                    children: <TableRow>[
                      ...List<TableRow>.generate(
                        _companies.length,
                        (int index) => TableRow(
                          decoration: BoxDecoration(),
                          children: <Widget>[
                            /// * 排序
                            TableCell(
                              verticalAlignment: TableCellVerticalAlignment.middle,
                              child: Container(
                                constraints: _tableCellBoxConstraints,
                                child: Center(
                                  child: Text(
                                    '${index + 1}',
                                    style: _tableCellTextStyle,
                                  ),
                                ),
                              ),
                            ),
                            /// * 单位名称
                            TableCell(
                              verticalAlignment: TableCellVerticalAlignment.middle,
                              child: Container(
                                padding: const EdgeInsets.all(5.0),
                                constraints: _tableCellBoxConstraints,
                                child: Align(
                                  alignment: Alignment.centerLeft,
                                  child: Text(
                                    _companies[index] ?? defaultStringPlaceholder,
                                    style: _tableCellTextStyle,
                                  ),
                                ),
                              ),
                            ),
                            /// * 数值
                            TableCell(
                              verticalAlignment: TableCellVerticalAlignment.middle,
                              child: Container(
                                padding: const EdgeInsets.all(5.0),
                                constraints: _tableCellBoxConstraints,
                                child: Align(
                                  alignment: Alignment.centerLeft,
                                  child: Text(
                                    _contents[index] ?? defaultStringPlaceholder,
                                    style: _tableCellTextStyle,
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
            ],
          ),
        );
      },
    );
  }

}
