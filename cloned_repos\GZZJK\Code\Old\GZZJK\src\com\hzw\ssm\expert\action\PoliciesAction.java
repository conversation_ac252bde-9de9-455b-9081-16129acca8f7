package com.hzw.ssm.expert.action;


import com.hzw.ssm.applets.utils.GetJsonReqData;
import com.hzw.ssm.expert.entity.Policies;

import com.hzw.ssm.expert.service.PoliciesService;
import com.hzw.ssm.fw.base.BaseAction;
import com.hzw.ssm.sys.user.entity.UserEntity;
import net.sf.json.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.apache.struts2.convention.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;

import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.ArrayList;
import java.util.List;


@Namespace("/policies1")
@ParentPackage(value = "default")
@Results({

        @Result(name = "initList",
                location = "/jsp/policies/policiesInitList.jsp"),

        @Result(name = "addPolicies",
                location = "/jsp/policies/addPolicies.jsp"),

        @Result(name = "updatePolicies",
                location = "/jsp/policies/updatePolicies.jsp")


})
public class PoliciesAction extends BaseAction {


    @Autowired
    private PoliciesService service;

    private List<Policies> policiesList = new ArrayList<>();


    private String name;
    private String id;
    /**
     * 状态  0启用 1禁用
     */
    private String status;

    private Policies entity;
    private String updSort;
    private String creSort;

    private Integer disableCount;//启用
    private Integer enableCount;//禁用

    private String pageStatus;


    @Action("/getPagePolicies")
    public String getPagePolicies() {
        if (entity == null) {
            entity = new Policies();
        }

        disableCount = service.getPoliciesCount("0");
        enableCount = service.getPoliciesCount("1");

        this.context();
        entity.setPage(this.getPage());


        if (this.pageStatus != null) {
            entity.setStatus(this.pageStatus);
        }


        if (StringUtils.isNotEmpty(updSort)) {
            entity.setOrder("ORDER BY p.CREATE_TIME "+updSort);
        }
        if (StringUtils.isNotEmpty(creSort)) {
            entity.setOrder("ORDER BY p.UPDATE_TIME "+creSort);
        }


        if (StringUtils.isEmpty(creSort)&&StringUtils.isEmpty(updSort)) {
            entity.setOrder("order by p.SORT DESC");
        }





        policiesList = service.getPagePolicies(entity);

        this.getRequest().setAttribute("policiesList", policiesList);
        return "initList";
    }


    @Action("/addPolicies")
    public String addPolicies() {
        return "addPolicies";
    }


    @Action("/updatePolicies")
    public String updatePolicies() {
        return "updatePolicies";
    }

    @Action("/updateStatus")
    public Object updateStatus() {

        this.context();
        // 当前session
        HttpSession session = getRequest().getSession();
        // 当前用户信息
        UserEntity user = (UserEntity) session.getAttribute("userInfo");

        //数据
        JSONObject jsonReqData = GetJsonReqData.getJsonReqData();
        String id = jsonReqData.getString("id");
        String status = jsonReqData.getString("status");


        return service.updateStatus(id, status, user.getUser_id());
    }


    @Action("/updateReviseSort")
    public Object updateReviseSort() throws IOException {


        this.context();
        // 当前session
        HttpSession session = getRequest().getSession();
        // 当前用户信息
        UserEntity user = (UserEntity) session.getAttribute("userInfo");

        //数据
        JSONObject jsonReqData = GetJsonReqData.getJsonReqData();
        Integer sort = jsonReqData.getInt("sort");//当前排序值
        boolean reviseSortFlag = jsonReqData.getBoolean("reviseSortFlag");
        String status = jsonReqData.getString("status");
        /*if(user==null||user.getUser_id()==null){
            PrintWriter out =  getResponse().getWriter();
            out.print("当前用户数据为空!");
            out.flush();
            out.close();
        }*/
        return service.updateReviseSort(sort, reviseSortFlag,status);

    }


    @Action("/updatePoliciesByIdNameDb")
    public String updatePoliciesDb() {

        this.context();
        // 当前session
        HttpSession session = getRequest().getSession();
        // 当前用户信息
        UserEntity user = (UserEntity) session.getAttribute("userInfo");

        //数据
        JSONObject jsonReqData = GetJsonReqData.getJsonReqData();
        String id = jsonReqData.getString("id");//当前排序值
        String name = jsonReqData.getString("name");


        return service.updatePoliciesDb(id, name, user.getUser_id());
    }


    @Action("/addPoliciesDb")
    public String addPoliciesDb() {
        this.context();
        // 当前session
        HttpSession session = getRequest().getSession();
        // 当前用户信息
        UserEntity user = (UserEntity) session.getAttribute("userInfo");

        //数据
        JSONObject jsonReqData = GetJsonReqData.getJsonReqData();
        String name = null;
        if (jsonReqData != null) {
            name = jsonReqData.getString("name");
        }


        return service.addPolicies(name, user.getUser_id());

    }

    public String getUpdSort() {
        return updSort;
    }

    public void setUpdSort(String updSort) {
        this.updSort = updSort;
    }

    public String getCreSort() {
        return creSort;
    }

    public void setCreSort(String creSort) {
        this.creSort = creSort;
    }

    public String getPageStatus() {
        return pageStatus;
    }

    public void setPageStatus(String pageStatus) {
        this.pageStatus = pageStatus;
    }

    public Integer getDisableCount() {
        return disableCount;
    }

    public void setDisableCount(Integer disableCount) {
        this.disableCount = disableCount;
    }

    public Integer getEnableCount() {
        return enableCount;
    }

    public void setEnableCount(Integer enableCount) {
        this.enableCount = enableCount;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Policies getEntity() {
        return entity;
    }

    public void setEntity(Policies entity) {
        this.entity = entity;
    }

    public List<Policies> getPoliciesList() {
        return policiesList;
    }

    public void setPoliciesList(List<Policies> policiesList) {
        this.policiesList = policiesList;
    }
}
