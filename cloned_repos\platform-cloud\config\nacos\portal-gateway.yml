# 安全配置
security:
  # 验证码
  captcha:
    # 是否开启验证码
    enabled: false
    # 验证码类型 math 数组计算 char 字符验证
    type: MATH
    # line 线段干扰 circle 圆圈干扰 shear 扭曲干扰
    category: CIRCLE
    # 数字验证码位数
    numberLength: 1
    # 字符验证码长度
    charLength: 4
  # 防止XSS攻击
  xss:
    enabled: true
    excludeUrls:
      - /system/notice
      - /bidApi/project/publish
  # 不校验白名单
  ignore:
    whites:
      - /code
      - /auth/logout
      - /auth/login
      - /auth/smsLogin
      - /auth/xcxLogin
      - /auth/register
      - /auth/registerByPhoneNumber
      - /auth/registerWechat
      - /auth/wxSmallLogin
      - /auth/supplier/supplierRegister
      - /resource/sms/sendRegisterSmsCaptcha
      - /resource/sms/sendRegisterImageCode
      - /resource/sms/sendLoginSmsCaptcha
      - /resource/sms/sendLoginImageCode
      - /resource/sms/code
      - /resource/sms/v2/std/sendSmsCode
      - /*/v3/api-docs
      - /csrf
      - /resource/oss/upload
      - /resource/oss/getPrivateUrl/{id}
      - /externalApi/ocr/uploadCardImgUrl
      - /externalApi/voice/callback
      - /bidApi/index/**
      - /resource/oss/downloadInIndex/**
      - /system/companyTemp/supplierBindCompany
      - /system/company/searchCompanyByOrgNum
      - /system/company/downloadAuthFile
      - /system/company/queryCompanyIndex
      - /system/company/queryCompanyDetail
      - /system/setting/querySystemSetting
      - /system/user/queryUserCompany


spring:
  cloud:
    # 网关配置
    gateway:
      # 打印请求日志(自定义)
      requestLog: true
      discovery:
        locator:
          lowerCaseServiceId: true
          enabled: true
      routes:
        # 认证中心
        - id: portal-auth
          uri: lb://portal-auth
          predicates:
            - Path=/auth/**
          filters:
            # 验证码处理
            - ValidateCodeFilter
            - StripPrefix=1
        # 代码生成
        - id: portal-gen
          uri: lb://portal-gen
          predicates:
            - Path=/code/**
          filters:
            - StripPrefix=1
        # 系统模块
        - id: portal-system
          uri: lb://portal-system
          predicates:
            - Path=/system/**
          filters:
            - StripPrefix=1
        # 资源服务
        - id: portal-resource
          uri: lb://portal-resource
          predicates:
            - Path=/resource/**
          filters:
            - StripPrefix=1
        # 演示服务
        - id: portal-demo
          uri: lb://portal-demo
          predicates:
            - Path=/demo/**
          filters:
            - StripPrefix=1
        # MQ演示服务
        - id: portal-stream-mq
          uri: lb://portal-stream-mq
          predicates:
            - Path=/stream-mq/**
          filters:
            - StripPrefix=1

        # 第三方对接
        - id: portal-external
          uri: lb://portal-external
          predicates:
            - Path=/externalApi/**
          filters:
            - StripPrefix=1
        # 电厂业务
        - id: portal-bid
          uri: lb://portal-bid
          predicates:
            - Path=/bidApi/**
          filters:
            - StripPrefix=1


    # sentinel 配置
    sentinel:
      filter:
        enabled: true
      # nacos配置持久化
      datasource:
        ds1:
          nacos:
            server-addr: ${spring.cloud.nacos.server-addr}
            dataId: sentinel-${spring.application.name}.json
            groupId: ${spring.cloud.nacos.config.group}
            namespace: ${spring.profiles.active}
            data-type: json
            rule-type: gw-flow
