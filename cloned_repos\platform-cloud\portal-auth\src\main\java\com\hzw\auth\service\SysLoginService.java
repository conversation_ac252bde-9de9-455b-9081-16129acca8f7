package com.hzw.auth.service;

import cn.dev33.satoken.exception.NotLoginException;
import cn.dev33.satoken.secure.BCrypt;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.useragent.UserAgent;
import cn.hutool.http.useragent.UserAgentUtil;
import com.hzw.auth.constantnum.ExceptionEnum;
import com.hzw.auth.form.LoginUserWechatReq;
import com.hzw.auth.form.RegisterBody;
import com.hzw.auth.properties.SmsValidateProperties;
import com.hzw.auth.properties.UserPasswordProperties;
import com.hzw.common.core.constant.CacheConstants;
import com.hzw.common.core.constant.Constants;
import com.hzw.common.core.domain.R;
import com.hzw.common.core.enums.DeviceType;
import com.hzw.common.core.enums.LoginType;
import com.hzw.common.core.enums.UserType;
import com.hzw.common.core.exception.user.CaptchaExpireException;
import com.hzw.common.core.exception.user.UserException;
import com.hzw.common.core.utils.MessageUtils;
import com.hzw.common.core.utils.ServletUtils;
import com.hzw.common.core.utils.SpringUtils;
import com.hzw.common.core.utils.StringUtils;
import com.hzw.common.core.utils.ip.AddressUtils;
import com.hzw.common.encrypt.utils.EncryptUtils;
import com.hzw.common.log.event.LogininforEvent;
import com.hzw.common.redis.utils.RedisUtils;
import com.hzw.common.satoken.utils.LoginHelper;
//import com.hzw.expert.api.domain.vo.ExpertInfoVO;
//import com.hzw.expert.api.RemoteExpertInfoService;
//import com.hzw.expert.api.RemoteQrCodeService;
import com.hzw.externalApi.api.RemoteJuheService;
import com.hzw.system.api.RemoteLogService;
import com.hzw.system.api.RemoteUserRoleService;
import com.hzw.system.api.RemoteUserService;
//import com.hzw.expert.api.domain.dto.ExpertRegisterDTO;
import com.hzw.system.api.domain.SysUser;
import com.hzw.system.api.model.LoginUser;
import com.hzw.system.api.model.TokenLoginUser;
import com.hzw.system.api.model.XcxLoginUser;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.DigestUtils;

import javax.servlet.http.HttpServletRequest;
import java.io.UnsupportedEncodingException;
import java.time.Duration;
import java.util.*;
import java.util.function.Supplier;

/**
 * 登录校验方法
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class SysLoginService {
    /**
     * 过期时间2小时
     */
    private static final long EXPIRE_TIME = 2 * 60 * 60 * 1000;


    @DubboReference
    private RemoteLogService remoteLogService;
    @DubboReference
    private RemoteUserService remoteUserService;
    @DubboReference
    private RemoteUserRoleService remoteUserRoleService;
    @DubboReference
    private RemoteJuheService juheService;

    @Autowired
    private UserPasswordProperties userPasswordProperties;

    @Autowired
    private SmsValidateProperties smsValidateProperties;


//    @DubboReference
//    private RemoteExpertInfoService remoteExpertInfoService;
//
//    @DubboReference
//    private RemoteQrCodeService remoteQrCodeService;

    /**
     * 登录
     */
    public String login(String username, String password) {
        LoginUser userInfo = remoteUserService.getUserInfo(username);

        checkLogin(LoginType.PASSWORD, username, () -> !BCrypt.checkpw(password, userInfo.getPassword()));
        // 获取登录token
        LoginHelper.loginByDevice(userInfo, DeviceType.PC);

        recordLogininfor(username, Constants.LOGIN_SUCCESS, MessageUtils.message("user.login.success"));
        return StpUtil.getTokenValue();
    }



    public R smsLogin(String phonenumber, String smsCode) {
        // 通过手机号查找用户
        LoginUser userInfo = remoteUserService.getUserInfoByPhonenumber(phonenumber);
        if (userInfo.getStatus()==Constants.STATUS_STOPPED){
            return R.fail("账号已停用");
        }
        if (2 == userInfo.getIdentity() && (1 != userInfo.getApproveStatus())){
            String tempToken = buildTempToken(phonenumber);
            return R.fail(1000000, "", tempToken);
        }
        //专家身份校验审核状态
//        if (Constants.IDENTITY_EXPERT.equals(userInfo.getIdentity())){
//            ExpertRegisterDTO expertRegisterDTO = new ExpertRegisterDTO();
//            expertRegisterDTO.setUserId(userInfo.getUserId());
//            ExpertInfoVO expertInfo = remoteExpertInfoService.getExpertInfo(expertRegisterDTO);
//            if (null != expertInfo && Constants.TO_APPROVE.equals(expertInfo.getStatus())){
//                return R.fail("专家待审核");
//            }
//        }
        checkLogin(LoginType.SMS, userInfo.getUsername(), () -> !validateSmsCode(phonenumber, smsCode));
        //checkLogin(LoginType.SMS, userInfo.getUsername(), () -> false);
        // 生成token
        LoginHelper.loginByDevice(userInfo, DeviceType.APP);

        recordLogininfor(userInfo.getUsername(), Constants.LOGIN_SUCCESS, MessageUtils.message("user.login.success"));
        return R.ok("登录成功",StpUtil.getTokenValue());
    }

    public R pwdLogin(String phonenumber, String password) {
        // 通过手机号查找用户
        LoginUser userInfo = remoteUserService.getUserInfoByPhonenumber(phonenumber);
        if (userInfo.getStatus()==Constants.STATUS_STOPPED){
            return R.fail("账号已停用");
        }
        if (userInfo.getPassword() == null){
            return R.fail("该账号尚未设置密码，请使用手机验证码登陆后设置密码");
        }
        String pwd = "pwd";
        checkLogin(LoginType.PASSWORD, userInfo.getUsername() + pwd, () -> !BCrypt.checkpw(password, userInfo.getPassword()));

        if (2 == userInfo.getIdentity() && (1 != userInfo.getApproveStatus())){
            String tempToken = buildTempToken(phonenumber);
            return R.fail(1000000, "", tempToken);
        }
//        String loginPwd = encryptionPwd(password);
        // 生成token
        LoginHelper.loginByDevice(userInfo, DeviceType.PC);

        recordLogininfor(userInfo.getUsername(), Constants.LOGIN_SUCCESS, MessageUtils.message("user.login.success"));
        return R.ok("登录成功",StpUtil.getTokenValue());
    }

    public R autoLogin(String phonenumber) {
        // 通过手机号查找用户
        LoginUser userInfo = remoteUserService.getUserInfoByPhonenumber(phonenumber);
        if (userInfo.getStatus()==Constants.STATUS_STOPPED){
            return R.fail("账号已停用");
        }
        //专家身份校验审核状态
//        if (Constants.IDENTITY_EXPERT.equals(userInfo.getIdentity())){
//            ExpertRegisterDTO expertRegisterDTO = new ExpertRegisterDTO();
//            expertRegisterDTO.setUserId(userInfo.getUserId());
//            ExpertInfoVO expertInfo = remoteExpertInfoService.getExpertInfo(expertRegisterDTO);
//            if (null != expertInfo && Constants.TO_APPROVE.equals(expertInfo.getStatus())){
//                return R.fail("专家待审核");
//            }
//        }
        //checkLogin(LoginType.SMS, userInfo.getUsername(), () -> false);
        // 生成token
        LoginHelper.loginByDevice(userInfo, DeviceType.APP);

        recordLogininfor(userInfo.getUsername(), Constants.LOGIN_SUCCESS, MessageUtils.message("user.login.success"));
        String token = Constants.FRONT_TOKEN + StpUtil.getTokenValue();
        return R.ok("登录成功", token);
    }

    public String emailLogin(String email, String emailCode) {
        // 通过邮箱查找用户
        LoginUser userInfo = remoteUserService.getUserInfoByEmail(email);

        checkLogin(LoginType.EMAIL, userInfo.getUsername(), () -> !validateEmailCode(email, emailCode));
        // 生成token
        LoginHelper.loginByDevice(userInfo, DeviceType.APP);

        recordLogininfor(userInfo.getUsername(), Constants.LOGIN_SUCCESS, MessageUtils.message("user.login.success"));
        return StpUtil.getTokenValue();
    }

    public String xcxLogin(String xcxCode) {
        // xcxCode 为 小程序调用 wx.login 授权后获取
        // todo 自行实现 校验 appid + appsrcret + xcxCode 调用登录凭证校验接口 获取 session_key 与 openid
        String openid = "";
        XcxLoginUser userInfo = remoteUserService.getUserInfoByOpenid(openid);
        // 生成token
        LoginHelper.loginByDevice(userInfo, DeviceType.XCX);

        recordLogininfor(userInfo.getUsername(), Constants.LOGIN_SUCCESS, MessageUtils.message("user.login.success"));
        return StpUtil.getTokenValue();
    }

    /**
     * Api接口登录
     * @param clientId
     * @param clientSecret
     * @return
     */
    public String tokenLogin(String clientId,String clientSecret) {
        TokenLoginUser userInfo = remoteUserService.getUserInfoByClientid(clientId);
        checkLogin(LoginType.PASSWORD, clientId, () -> !clientSecret.equals(userInfo.getClientSecret()));
        // 生成token
        LoginHelper.loginByDevice(userInfo, DeviceType.API);

        recordLogininfor(userInfo.getUsername(), Constants.LOGIN_SUCCESS, MessageUtils.message("user.login.success"));
        return StpUtil.getTokenValue();
    }
    /**
     * 退出登录
     */
    public void logout() {
        try {
            LoginUser loginUser = LoginHelper.getLoginUser();
            recordLogininfor(loginUser.getUsername(), Constants.LOGOUT, MessageUtils.message("user.logout.success"));
        } catch (NotLoginException ignored) {
        } finally {
            try {
                StpUtil.logout();
            } catch (NotLoginException ignored) {
            }
        }
    }

    /**
     * 注册
     */
    public R register(RegisterBody registerBody) {
        String username = registerBody.getUsername();
        String password = registerBody.getPassword();
        // 校验用户类型是否存在
       // String userType = UserType.getUserType(registerBody.getUserType()).getUserType();
//        Boolean aBoolean = remoteQrCodeService.checkQrCodeExist(registerBody.getReferralCode());
//        if(!aBoolean){
//            R.fail("推荐码无效，请重新输入!");
//        }
        // 注册用户信息
        SysUser sysUser = new SysUser();
        sysUser.setUserName(username);
        sysUser.setNickName(username);
        sysUser.setPassword(BCrypt.hashpw(password));
        sysUser.setUserType(UserType.SYS_USER.getUserType());
        R r = remoteUserService.registerUserInfo(sysUser);
        recordLogininfor(username, Constants.REGISTER, MessageUtils.message("user.register.success"));
        return r;
    }

    /**
     * @title 通过*姓名、*手机号、*短信验证码、推荐码注册
     * @description
     * @param registerBody
     * <AUTHOR>
     */
//    @Transactional(rollbackFor = Exception.class)
//    public R registerByPhoneNumber(RegisterBody registerBody) {
//        String username = registerBody.getUsername();
////        String password = registerBody.getPassword();
//        // 校验用户类型是否存在
////        String userType = UserType.getUserType(registerBody.getUserType()).getUserType();
//        //手机号
//        String phoneNumber = registerBody.getPhoneNumber();
//        //短信验证码
//        String smsCode = registerBody.getSmsCode();
//        //推荐码(非必须)
//        String referralCode = registerBody.getReferralCode();
//        if(null != referralCode && !"".equals(referralCode)){
//           if(!remoteQrCodeService.checkQrCodeExist(referralCode)){
//               throw new UserException("推荐码无效，请重新输入!");
//           }
//        }
//
//        //进行姓名手机号二要素校验
///*        if (!juheService.validateByNameAndPhone(username,phoneNumber)){
//            throw new UserException("姓名和手机号不匹配");
//        }*/
//        //进行验证码校验
//        boolean smsFlag = validateSmsCode(phoneNumber, smsCode);
//       // boolean smsFlag = true;
//        if (!smsFlag){
//            throw new UserException("验证码不正确");
//        }
//        // 注册用户信息
//        SysUser sysUser = new SysUser();
//        sysUser.setUserName(username);
//        sysUser.setNickName(phoneNumber);
//        sysUser.setReferralCode(referralCode);
//        sysUser.setIdentity(Constants.IDENTITY_EXPERT);
//        sysUser.setUserType(UserType.SYS_USER.getUserType());
////        sysUser.setPassword(BCrypt.hashpw(password));
//        sysUser.setPhonenumber(phoneNumber);
////        LoginUser userInfo = remoteUserService.getUserInfo(username);
//        //注册信息
//        R<SysUser> r = remoteUserService.registerUserInfo(sysUser);
//        if (!Constants.SUCCESS.equals(r.getCode())){
//            return r;
//        }
//        SysUser su = r.getData();
//        String userId = su.getUserId();
//        SysUserRoleRegister sysUserRoleRegister = new SysUserRoleRegister();
//        sysUserRoleRegister.setCreatedUserId(username);
//        sysUserRoleRegister.setUpdatedUserId(username);
//        sysUserRoleRegister.setUserId(userId);
//        //专家角色
//        sysUserRoleRegister.setRoleId(Constants.ROLE_EXPERT);
//        remoteUserRoleService.registerUserRoleInfo(sysUserRoleRegister);
//        //专家信息
//        ExpertRegisterDTO expertRegisterDTO = new ExpertRegisterDTO();
//        expertRegisterDTO.setUserId(userId);
//        expertRegisterDTO.setUserName(username);
//        expertRegisterDTO.setPhone(phoneNumber);
//        remoteExpertInfoService.registerExpertInfo(expertRegisterDTO);
//
//        recordLogininfor(username, Constants.REGISTER, MessageUtils.message("user.register.success"));
//        return r;
//    }


    /**
     * 校验短信验证码
     */
    private boolean validateSmsCode(String phonenumber, String smsCode) {
        Boolean validate = smsValidateProperties.getValidate();
        // 开启短信验证
        if(validate){
           // String code = RedisUtils.getCacheObject(CacheConstants.CAPTCHA_CODE_KEY + phonenumber);
            String code = RedisUtils.getCacheObject(CacheConstants.SUPPLIER_LOGIN_CAPTCHA_CODE_KEY + phonenumber);
            if (StringUtils.isBlank(code)) {
                recordLogininfor(phonenumber, Constants.LOGIN_FAIL, MessageUtils.message("user.jcaptcha.error"));
                throw new CaptchaExpireException();
            }
            validate = code.equals(smsCode);

        }else{ // 关闭短信验证
            validate = true;
        }
        return validate;
    }

    private boolean checkpwd(String password, String userPwd) {
        if (!userPwd.equals(password)) {
            return false;
        }
        return true;
    }

    /**
     * 校验密码登录
     */
    private String encryptionPwd(String password){
        // 检查密码
        String pwdMd5 = "";
        try {
            pwdMd5 = DigestUtils.md5DigestAsHex(password.getBytes("utf-8"));
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        return pwdMd5;
    }

    /**
     * 校验邮箱验证码
     */
    private boolean validateEmailCode(String email, String emailCode) {
        String code = RedisUtils.getCacheObject(CacheConstants.CAPTCHA_CODE_KEY + email);
        if (StringUtils.isBlank(code)) {
            recordLogininfor(email, Constants.LOGIN_FAIL, MessageUtils.message("user.jcaptcha.expire"));
            throw new CaptchaExpireException();
        }
        return code.equals(emailCode);
    }

    /**
     * 登录校验
     */
    private void checkLogin(LoginType loginType, String username, Supplier<Boolean> supplier) {
        String errorKey = CacheConstants.PWD_ERR_CNT_KEY + username;
        String loginFail = Constants.LOGIN_FAIL;
        Integer maxRetryCount = userPasswordProperties.getMaxRetryCount();
        Integer lockTime = userPasswordProperties.getLockTime();

        // 获取用户登录错误次数(可自定义限制策略 例如: key + username + ip)
        Integer errorNumber = RedisUtils.getCacheObject(errorKey);
        // 锁定时间内登录 则踢出
        if (ObjectUtil.isNotNull(errorNumber) && errorNumber.equals(maxRetryCount)) {
            recordLogininfor(username, loginFail, MessageUtils.message(loginType.getRetryLimitExceed(), maxRetryCount, lockTime));
            throw new UserException(loginType.getRetryLimitExceed(), maxRetryCount, lockTime);
        }

        if (supplier.get()) {
            // 是否第一次
            errorNumber = ObjectUtil.isNull(errorNumber) ? 1 : errorNumber + 1;
            // 达到规定错误次数 则锁定登录
            if (errorNumber.equals(maxRetryCount)) {
                RedisUtils.setCacheObject(errorKey, errorNumber, Duration.ofMinutes(lockTime));
                recordLogininfor(username, loginFail, MessageUtils.message(loginType.getRetryLimitExceed(), maxRetryCount, lockTime));
                throw new UserException(loginType.getRetryLimitExceed(), maxRetryCount, lockTime);
            } else {
                // 未达到规定错误次数 则递增
                RedisUtils.setCacheObject(errorKey, errorNumber);
                recordLogininfor(username, loginFail, MessageUtils.message(loginType.getRetryLimitCount(), errorNumber));
                throw new UserException(loginType.getRetryLimitCount(), errorNumber);
            }
        }
        // 登录成功 清空错误次数
        RedisUtils.deleteObject(errorKey);
        RedisUtils.deleteObject(errorKey + "pwd");
    }





//   public Map<String, Object> wxSmallLogin(LoginUserWechatReq userREQ) throws Exception{
//        try {
//            //       获取opendid
//            Map<String, Object> oauthMap = SmallWxUtil.oauth2GetOpenid(userREQ.getCode());
//            if (null == oauthMap || oauthMap.size() == 0) {
//                throw new Exception("code无效,请检查");
//            }
//
//            List<Map<String, Object>> list = new ArrayList<>();
//            Map<String, Object> map = new HashMap<>(16);
//
//            String openid = String.valueOf(oauthMap.get("openid"));
//
//            map.put("openid", openid);
//
//            map.put("headimage", userREQ.getHeadimage());
//
//            map.put("id", "");
//
//            // 获取用户信息
////            Map<String, Object> wxUserInfo = SmallWxUtil.getWXUserInfo(openid);
//            map.put("UserName", userREQ.getTruename());
//            XcxLoginUser sysUser = new XcxLoginUser();
//            if (openid != null && !"".equals(openid)) {
//                sysUser = remoteUserService.getUserInfoByOpenid(openid);
//            }
//            int isBand = 0;
//            if (null != sysUser) {
//                ExpertInfoVO expertInfo = new ExpertInfoVO();
//                expertInfo.setUserId(sysUser.getUserId());
//                ExpertInfoVO expertInfoByUserId = remoteExpertInfoService.getExpertInfoByUserId(expertInfo);
//                // 专家状态 0:注册 1:待审核 2:二次待审核 3:审核通过 4:禁用(不可评标) 5：修改待审核 6：修改待审核二次审核
//                String status = String.valueOf(sysUser.getStatus());
//                if ("2".equals(status) && expertInfoByUserId != null && !"60".equals(expertInfoByUserId.getStatus().toString())) {
//                    throw new Exception("十分抱歉,您的账号状态异常,暂不允许登录,如有疑问请联系人工客服4000580203");
//                }
//
//                String userId = sysUser.getUserId();
//                map.put("id", userId);
//                isBand = 1;
//
//                Date date = new Date(System.currentTimeMillis() + EXPIRE_TIME);
//                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//                String expireTime = sdf.format(date);
//                XcxLoginUser userInfo = remoteUserService.getUserInfoByOpenid(openid);
//                // 生成token
//                LoginHelper.loginByDevice(userInfo, DeviceType.XCX);
//                recordLogininfor(userInfo.getUsername(), Constants.LOGIN_SUCCESS, MessageUtils.message("user.login.success"));
//                map.put("token", FRONT_TOKEN + StpUtil.getTokenValue());
//                map.put("expireTime", expireTime);
//            }
//
//            map.put("isBand", isBand);
//            return map;
//        }
//            catch (Exception e) {
//                e.printStackTrace();
//                throw new Exception("服务器正忙...");
//            }
//
//    }

    /**
     * 绑定
     * @param userREQ
     * @return
     * @throws Exception
     */
    @Transactional(rollbackFor = Exception.class)
    @GlobalTransactional(rollbackFor = Exception.class)
    public R<Map<String, Object>> binding(LoginUserWechatReq userREQ) throws Exception {

            Map<String, Object> map = new HashMap<>();
            String code = userREQ.getSmsCode();
            String mobilephone = userREQ.getMobile();
            // todo 校验验证码
            String checkPhoneCode = checkPhoneCode(mobilephone, code);
            if ("1".equals(checkPhoneCode)) {
                return R.fail(ExceptionEnum.VERIFIC_CODE_WAIT.message);
            } else if ("2".equals(checkPhoneCode)) {
                return R.fail(ExceptionEnum.VERIFIC_CODE_ATYPISM.message);
            }

            String idNo = userREQ.getIdNo();
            String openid = userREQ.getOpenId();

            SysUser sysUser = new SysUser();
            sysUser.setIdNum(idNo);
            SysUser infoOne = remoteUserService.queryByParam(sysUser);
            sysUser.setIdNum(null);

            sysUser.setPhonenumber(mobilephone);
            SysUser infoTwo = remoteUserService.queryByParam(sysUser);

            // 都不存在
            if (null == infoOne && null == infoTwo) {
                return R.fail( ExceptionEnum.NO_REGISTER.message);
                // 手机号存在，身份证不存在
            } else if (null == infoOne && null != infoTwo) {
                return R.fail( ExceptionEnum.NO_REGISTER.message);
                // 身份证存在，手机不存在
            } else if (null != infoOne && null == infoTwo) {
                String phone = infoOne.getPhonenumber();
                return R.fail("您填写的身份证号已绑定手机尾号" + phone.substring(7) + ",请修改手机号完成绑定,如有问题请联系人工客服4000580203");

          //      throw new Exception("您填写的身份证号已绑定手机尾号" + phone.substring(7) + ",请修改手机号完成绑定,如有问题请联系人工客服4000580203");
                // 都存在，即绑定
            } else if (null != infoOne && null != infoTwo) {
                String id = infoOne.getUserId();
                if (id.equals(infoTwo.getUserId())) {
                    sysUser.setUserId(id);
                    sysUser.setOpenId(openid);
                    remoteUserService.updateUserById(sysUser);
//                    ExpertInfoVO sysExpertInfo = new ExpertInfoVO();
//                    sysExpertInfo.setUserId(sysUser.getUserId());
//                    sysExpertInfo.setIsUpdate(AuthEnum.ISNOTUPDATE.getType());
//                    remoteExpertInfoService.updateById(sysExpertInfo);
                    map.put("id", infoOne.getUserId());
                    map.put("isBand", 1);
                    return R.ok(map);
                } else {
                    String phone = infoOne.getPhonenumber();
                    return R.fail("您填写的身份证号已绑定手机尾号" + phone.substring(7) + ",请修改手机号完成绑定,如有问题请联系人工客服4000580203");
               //     throw  new Exception( "您填写的身份证号已绑定手机尾号" + phone.substring(7) + ",请修改手机号完成绑定,如有问题请联系人工客服4000580203");
                }
            }
        return R.ok();
    }

    /**
     * 注册
     * @param user
     * @return
     */
//    @Transactional(rollbackFor = Exception.class)
//    @GlobalTransactional(rollbackFor = Exception.class)
//    public R<Map<String, Object>> register(SysUserDTO user) throws Exception {
//        Map<String, Object> map = new HashMap<>();
//            // 聚合二要素验证
//            Boolean bool = juheService.validateByNameAndIdCard(user.getUserName(), user.getIdNum());
//            if(!bool){
//               return R.fail("姓名与身份证号不匹配");
//            }
//            String mobilephone = user.getPhonenumber();
//            String code = user.getCode();
//            // 校验验证码
//            String checkPhoneCode = checkPhoneCode(mobilephone, code);
//            if ("1".equals(checkPhoneCode)) {
//                return R.fail(ExceptionEnum.VERIFIC_CODE_WAIT.message);
//            } else if ("2".equals(checkPhoneCode)) {
//                return R.fail(ExceptionEnum.VERIFIC_CODE_WAIT.message);
//            }
//            // 校验推荐码
//            String referralCode = user.getRecommendCode();
//            if(null != referralCode && !"".equals(referralCode)){
//                if(!remoteQrCodeService.checkQrCodeExist(referralCode)){
//                    throw new UserException("推荐码不存在");
//                }
//            }
//            String headImage = user.getHeadimage();
//            String userName = user.getUserName();
//            String idNo = user.getIdNum();
//            String openid = user.getOpenId();
//            String recommendCode = user.getRecommendCode();
//            if (StringUtils.isEmpty(recommendCode)) {
//                recommendCode = "JSZB99";
//            }
//            String birthday = getBirthDate(idNo);
//
//            SysUser sysUser1 = new SysUser();
//            sysUser1.setIdNum(idNo);
//            sysUser1.setNickName(userName);
//            sysUser1.setUserName(userName);
//            SysUser infoOne = remoteUserService.queryByParam(sysUser1);
//            sysUser1.setIdNum(null);
//
//            sysUser1.setPhonenumber(mobilephone);
//            sysUser1.setNickName(userName);
//            sysUser1.setUserName(userName);
//            SysUser infoTwo = remoteUserService.queryByParam(sysUser1);
//            ExpertInfoVO infoEntity = new ExpertInfoVO();
//            // 都不存在
//            if (null == infoOne && null == infoTwo) {
//
//                // 用户表
//                SysUser sysUser = new SysUser();
//                // sysUser.setUserId(userId);
//                sysUser.setAvatar(headImage);
//                sysUser.setPhonenumber(mobilephone);
//                sysUser.setUserName(userName);
//                sysUser.setOpenId(openid);
//                sysUser.setUserType("app_user");
//                //sysUser.setPassword(MD5Util.reverse("123456"));
//                sysUser.setNickName(mobilephone);
//                sysUser.setCreatedTime(new Date());
//                sysUser.setIdentity(1);//专家
//                sysUser.setIdNum(idNo);
//                sysUser.setOpenId(openid);
//                sysUser.setReferralCode(recommendCode);
//                remoteUserService.registerUserInfo(sysUser);
//                SysUser user1 = remoteUserService.querySysUser(openid);
//                // 添加角色
//                SysUserRoleRegister sysUserRoleRegister = new SysUserRoleRegister();
//                sysUserRoleRegister.setRoleId(Constants.ROLE_EXPERT);
//                sysUserRoleRegister.setUserId(user1.getUserId());
//                sysUserRoleRegister.setCreatedUserId(user1.getUserId());
//                sysUserRoleRegister.setUpdatedUserId(user1.getUserId());
//                remoteUserRoleService.registerUserRoleInfo(sysUserRoleRegister);
//                // 根据注册身份证号去专家表查询是否存在
//                ExpertInfoVO sysExpertInfo = remoteExpertInfoService.queryOne(idNo);
////                // 专家表
//                if(sysExpertInfo != null && sysExpertInfo.getId() != null){
//                    sysExpertInfo.setUserId(user1.getUserId());
//                    sysExpertInfo.setIsUpdate(AuthEnum.ISNOTUPDATE.getType());
//                    remoteExpertInfoService.updateById(sysExpertInfo);
//                }else {
//                    // infoEntity.setId(CommUtil.getKey());
//                    infoEntity.setUserId(user1.getUserId());
//                    infoEntity.setUserName(userName);
//                    infoEntity.setSex(Integer.valueOf(user.getSex()));
//                    infoEntity.setIdNum(idNo);
//                    infoEntity.setIdCardFront(user.getIdCardFront());
//                    infoEntity.setIsRetire(2);
//                    infoEntity.setIsUpdate(AuthEnum.ISNOTUPDATE.getType());
//                 //   infoEntity.setSex(Integer.valueOf(sex));
//                    infoEntity.setBirthday(new SimpleDateFormat("yyyy-MM-dd").parse(birthday));
//                    infoEntity.setIdCardReverse(user.getIdCardReverse());
//                    infoEntity.setPhone(mobilephone);
//                    infoEntity.setIdCardFront(user.getIdCardFront());
//                    infoEntity.setIdCardReverse(user.getIdCardReverse());
//                    infoEntity.setReferralCode(user.getReferralCode());
//                    infoEntity.setIdType(1);
//                    infoEntity.setStatus(10);
//                    infoEntity.setCreatedTime(new Date());
//                    remoteExpertInfoService.insert(infoEntity);
//                }
//                map.put("id", user1.getUserId());
//                return R.ok(map);
//                // 手机号存在，身份证不存在
//            } else if (null == infoOne && null != infoTwo) {
//                return R.fail(ExceptionEnum.PHONE_BOUND.message);
////                throw new Exception( ExceptionEnum.PHONE_BOUND.message);
//
//                // 身份证存在，手机不存在
//            } else if (null != infoOne && null == infoTwo) {
//                String phone = infoOne.getPhonenumber();
//                String msg = "身份证号已绑定手机尾号" + phone.substring(7) + ",不能使用此身份证号重复注册,请直接完成绑定,如有疑问请联系人工客服4000580203";
//                return R.fail(msg);
//               // throw new Exception( "您填写的身份证号已绑定手机尾号" + phone.substring(7) + ",不能使用此身份证号重复注册,请直接完成绑定,如有疑问请联系人工客服4000580203");
//                // 都存在
//            } else if (null != infoOne && null != infoTwo) {
//                String phone = infoOne.getPhonenumber();
//                String msg ="身份证号已绑定手机尾号" + phone.substring(7) + ",不能使用此身份证号重复注册,请直接完成绑定,如有疑问请联系人工客服4000580203";
//                return R.fail(msg);
//              //  throw new Exception( "您填写的身份证号已绑定手机尾号" + phone.substring(7) + ",不能使用此身份证号重复注册,请直接完成绑定,如有疑问请联系人工客服4000580203");
//            }
//         return R.ok();
//    }

    /**
     * 记录登录信息
     *
     * @param username 用户名
     * @param status   状态
     * @param message  消息内容
     * @return
     */
    public void recordLogininfor(String username, String status, String message) {
        HttpServletRequest request = ServletUtils.getRequest();
        final UserAgent userAgent = UserAgentUtil.parse(request.getHeader("User-Agent"));
        final String ip = ServletUtils.getClientIP(request);

        String address = AddressUtils.getRealAddressByIP(ip);
        // 获取客户端操作系统
        String os = userAgent.getOs().getName();
        // 获取客户端浏览器
        String browser = userAgent.getBrowser().getName();
        // 封装对象
        LogininforEvent logininfor = new LogininforEvent();
        logininfor.setUserPhone(username);
        logininfor.setIpaddr(ip);
        logininfor.setLoginLocation(address);
        logininfor.setBrowser(browser);
        logininfor.setOs(os);
        logininfor.setMsg(message);
        // 日志状态
        if (StringUtils.equalsAny(status, Constants.LOGIN_SUCCESS, Constants.LOGOUT, Constants.REGISTER)) {
            logininfor.setStatus(Constants.LOGIN_SUCCESS_STATUS);
        } else if (Constants.LOGIN_FAIL.equals(status)) {
            logininfor.setStatus(Constants.LOGIN_FAIL_STATUS);
        }
        SpringUtils.context().publishEvent(logininfor);
    }

    /**
     * 注册 绑定 验证码校验
     *
     * @param mobilephone
     * @param code
     * @return
     */
    public String checkPhoneCode(String mobilephone, String code) {

        String checkPhoneCodeStatus = "0";
        // 进行验证码校验
        String phoneCode = "";
        if (null == phoneCode) {
            checkPhoneCodeStatus = "1";
        } else {
            boolean smsFlag = validateSmsCode(mobilephone, code);
            if (!smsFlag){
                checkPhoneCodeStatus = "2";
            }
        }
        return checkPhoneCodeStatus;
    }

    public  String getBirthDate(String idCardNo) {
        if (idCardNo == null || idCardNo.length() != 18) {
            return null;
        }
        String birthYear = idCardNo.substring(6, 10);
        String birthMonth = idCardNo.substring(10, 12);
        String birthDay = idCardNo.substring(12, 14);
        Calendar cal = Calendar.getInstance();
        cal.set(Calendar.YEAR, Integer.parseInt(birthYear));
        cal.set(Calendar.MONTH, Integer.parseInt(birthMonth) - 1);
        cal.set(Calendar.DAY_OF_MONTH, Integer.parseInt(birthDay));
        return cal.get(Calendar.YEAR) + "-" + (cal.get(Calendar.MONTH) + 1) + "-" + cal.get(Calendar.DAY_OF_MONTH);
    }

    public String buildTempToken(String phonenumber){
        String key = EncryptUtils.encryptBySha256(CacheConstants.SUPPLIER_REGISTER_TEMP_TOKEN_KEY + phonenumber);
        String value = phonenumber;
        RedisUtils.setCacheObject(key, value, Duration.ofMinutes(Constants.TEMP_TOKEN_EXPIRATION));
        return key;
    }


    /**
     * 短信验证
     * @param phoneNumber
     * @param smsCode
     * @return
     */
    public Boolean checkSmsCode(String phoneNumber, String smsCode) {
        Boolean validate = smsValidateProperties.getValidate();
        // 开启短信验证
        if(validate) {
            String prefixKey = CacheConstants.SUPPLIER_REGISTER_CAPTCHA_CODE_KEY;
            String key = prefixKey + phoneNumber;
            String code = RedisUtils.getCacheObject(key);
            if (org.apache.commons.lang3.StringUtils.isBlank(code) || !smsCode.equals(code)) {
                log.error("请输入正确的验证码 => {}", phoneNumber);
                return false;
            } else {
                //校验成功清除缓存验证码
                RedisUtils.deleteObject(key);
                return true;
            }
        }else{
            return true;
        }
    }

    /**
     * 短信验证
     * @param phoneNumber
     * @param smsCode
     * @return
     */
    public Boolean checkChangePwdSmsCode(String phoneNumber, String smsCode) {
        Boolean validate = smsValidateProperties.getValidate();
        // 开启短信验证
        if(validate) {
            String prefixKey = CacheConstants.SUPPLIER_CHANGE_PWD_CAPTCHA_CODE_KEY;
            String key = prefixKey + phoneNumber;
            String code = RedisUtils.getCacheObject(key);
            if (org.apache.commons.lang3.StringUtils.isBlank(code) || !smsCode.equals(code)) {
                log.error("请输入正确的验证码 => {}", phoneNumber);
                return false;
            } else {
                //校验成功清除缓存验证码
                RedisUtils.deleteObject(key);
                return true;
            }
        }else{
            return true;
        }
    }

}
