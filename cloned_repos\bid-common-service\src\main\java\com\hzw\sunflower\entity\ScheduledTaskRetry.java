package com.hzw.sunflower.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 定时任务重试表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-05
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("t_scheduled_task_retry")
public class ScheduledTaskRetry {

    /**
     * 主键
     */
    @TableId(value = "ID")
    private Long id;

    /**
     * 重试类型 1.标书费开票
     */
    @ApiModelProperty("重试类型")
    private Integer retryType;

    /**
     * 重试次数
     */
    @ApiModelProperty("重试次数")
    private Integer retryCount;

    /**
     * 失败原因
     */
    @ApiModelProperty("失败原因")
    private String reason;

    /**
     * 订单编号
     */
    @ApiModelProperty("订单编号")
    private String orderNum;

    /**
     * 逻辑删除
     */
    @ApiModelProperty("逻辑删除")
    private Integer isDelete;

    /**
     * 版本号控制并发
     */
    @ApiModelProperty("版本号控制并发")
    private Integer version;

}
