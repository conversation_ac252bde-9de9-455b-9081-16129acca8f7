package com.hzw.sunflower.service;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hzw.sunflower.entity.ForeignCurrencyRate;

import java.util.List;

/**
 * <p>
 * 外币转换汇率凭证 服务类
 * </p>
 *
 * <AUTHOR> Gen
 * @since 2022-12-15
 */
public interface ForeignCurrencyRateService extends IService<ForeignCurrencyRate> {

    /**
     * 提交外币转换汇率凭证
     * @param fileIdList
     * @param covertId
     */
    void submitForeignCurrencyRate(List<Long> fileIdList, Long covertId);

    /**
     * 查询汇率凭证信息
     * @param covertId
     * @param covertId
     */
    List<Long> queryForeignCurrencyRate(Long covertId);

}
