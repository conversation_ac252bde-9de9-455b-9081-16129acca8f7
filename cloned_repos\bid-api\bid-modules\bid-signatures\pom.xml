<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>bid-modules</artifactId>
        <groupId>com.hzw</groupId>
        <version>0.0.4</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>bid-signatures</artifactId>

    <name>bid-signatures</name>
    <properties>
        <okhttp3.version>3.11.0</okhttp3.version>
    </properties>
    <dependencies>
        <dependency>
            <groupId>com.hzw</groupId>
            <artifactId>bid-common-core</artifactId>
<!--            <version>0.0.4</version>-->
<!--            <scope>compile</scope>-->
        </dependency>
        <dependency>
            <groupId>com.hzw</groupId>
            <artifactId>bid-common-service</artifactId>
        </dependency>
        <dependency>
            <groupId>com.hzw</groupId>
            <artifactId>bid-common-datascope</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>2.2.8</version>
        </dependency>
        <dependency>
            <groupId>com.hzw</groupId>
            <artifactId>bid-datura</artifactId>
        </dependency>
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
            <version>${okhttp3.version}</version>
        </dependency>
        <dependency>
            <groupId>com.hzw</groupId>
            <artifactId>bid-poppy</artifactId>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>com.hzw</groupId>-->
<!--            <artifactId>bid-anemone</artifactId>-->
<!--        </dependency>-->
    </dependencies>

</project>
