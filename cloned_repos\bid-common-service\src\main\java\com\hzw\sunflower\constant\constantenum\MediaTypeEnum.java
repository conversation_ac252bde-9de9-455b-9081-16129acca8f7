package com.hzw.sunflower.constant.constantenum;

/**
 * <AUTHOR>
 * @date ：Created in 2021/11/9 13:49
 * @description：媒体类型
 * @modified By：`
 * @version: 1.0
 */
public enum MediaTypeEnum {
    WIN_MEDIA("WIN-MEDIA", "中标候选人/中标结果媒体类型"),
    TENDER_BULLETIN("TENDER-MEDIA","招标公告媒体类型"),
    SUPPLEMENT_MEDIA("SUPPLEMENT-MEDIA", "补充公告媒体类型");

    /**
     * 类型
     */
    private String code;

    /**
     * 说明
     */
    private String msg;

    MediaTypeEnum(String code, String desc) {
        this.code = code;
        this.msg = desc;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }


    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }
}
