package com.hzw.sunflower.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;

import com.hzw.sunflower.common.BaseBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 保证金流水表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-27
 */
@Data
@TableName("t_bond_water")
@ApiModel(value = "BondWater对象", description = "保证金流水表")
public class BondWater extends BaseBean implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("交易流水号")
    @TableField("water_number")
    private String waterNumber;

    @ApiModelProperty("交易日期")
    @TableField("date")
    private String date;

    @ApiModelProperty("交易时间")
    @TableField("time")
    private String time;

    @ApiModelProperty("付款户名")
    @TableField("company_name")
    private String companyName;

    @ApiModelProperty("付款账户")
    @TableField("company_account")
    private String companyAccount;

    @ApiModelProperty("付款开户行")
    @TableField("company_bank_deposit")
    private String companyBankDeposit;

    @ApiModelProperty("供应商付款银行行号")
    @TableField("company_bank_code")
    private String companyBankCode;

    @ApiModelProperty("交易金额（保留两位小数）")
    @TableField("amount")
    private String amount;

    @ApiModelProperty("收款账户")
    @TableField("receive_acount")
    private String receiveAcount;

    @ApiModelProperty("收款银行")
    @TableField("receive_bank")
    private String receiveBank;

    @ApiModelProperty("附言")
    @TableField("post_script")
    private String postScript;

    @ApiModelProperty("数据来源 1.银行对接 2.excel导入")
    @TableField("source")
    private Integer source;

    @ApiModelProperty("数据来源 1.正常 2.45天未关联 3.60天未关联")
    @TableField("abnormal_status")
    private Integer abnormalStatus;

}
