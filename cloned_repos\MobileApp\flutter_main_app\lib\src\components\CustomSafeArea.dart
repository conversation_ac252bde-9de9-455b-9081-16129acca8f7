///***
/// * 安全区域外包裹一层容器
/// */

import 'package:flutter/material.dart';

import 'package:flutter_main_app/src/constants/theme.dart';
import 'package:flutter_main_app/src/utils/ScreenUtils.dart';

class CustomSafeArea extends StatelessWidget {
  const CustomSafeArea({
    Key key,
    @required this.child,
    this.backgroundColor = themeBackgroundColor,
  }) : assert(child != null),
    assert(backgroundColor != null),
    super(key: key);

  final Widget child;
  final Color backgroundColor;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: ScreenUtils.screenWidth,
      height: ScreenUtils.screenHeight,
      color: this.backgroundColor,
      child: SafeArea(
        top: false,
        child: this.child,
      ),
    );
  }
}
