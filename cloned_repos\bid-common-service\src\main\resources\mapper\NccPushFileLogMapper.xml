<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzw.sunflower.dao.NccPushFileLogMapper">

    <select id="queryNccSyncFile" resultType="com.hzw.sunflower.entity.NccPushFileLog">
        select
            t2.*
        from
            t_pay_status_file_log t1
        left join t_ncc_push_file_log t2 on t1.id = t2.business_id
        where t1.is_delete = 0
        and t2.is_delete = 0
        and t2.business_type = 1
        and t1.push_status = 2
        and t2.push_result = 1
        and t2.push_count &lt; 3
        and t2.file_id is not null
        and DATE_FORMAT(t1.created_time,'%Y-%m-%d') &gt;= DATE_SUB(CURDATE(), INTERVAL 3 DAY)
        and DATE_FORMAT(t1.created_time,'%Y-%m-%d') &lt;= DATE_SUB(CURDATE(), INTERVAL 2 DAY)

        union
        select
            t2.*
        from
        t_ncc_bond_push_file_log t1
        left join t_ncc_push_file_log t2 on t1.id = t2.business_id
        where t1.is_delete = 0
        and t2.is_delete = 0
        and t2.business_type = 2
        and t1.push_status = 2
        and t2.push_result = 1
        and t2.push_count &lt; 3
        and t2.file_id is not null
        and DATE_FORMAT(t1.created_time,'%Y-%m-%d') &gt;= DATE_SUB(CURDATE(), INTERVAL 3 DAY)
        and DATE_FORMAT(t1.created_time,'%Y-%m-%d') &lt;= DATE_SUB(CURDATE(), INTERVAL 2 DAY)
    </select>
    <select id="queryFileInfo" resultType="com.hzw.sunflower.entity.NccPushFileLog">
        select
        t1.*
        from
        t_ncc_push_file_log t1
        where t1.is_delete = 0
        and t1.push_result = 1
        and t1.file_id is null
        and t1.created_time &gt;= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
    </select>
</mapper>