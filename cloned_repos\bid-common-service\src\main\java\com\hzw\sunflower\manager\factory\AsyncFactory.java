package com.hzw.sunflower.manager.factory;

import com.hzw.sunflower.entity.OperationLog;
import com.hzw.sunflower.service.IpLibraryService;
import com.hzw.sunflower.service.OperationLogService;
import com.hzw.sunflower.service.impl.IpLibraryServiceImpl;
import com.hzw.sunflower.util.ip.AddressUtils;
import com.hzw.sunflower.util.spring.SpringUtils;
import lombok.extern.slf4j.Slf4j;

import java.util.TimerTask;

/**
 * 异步工厂（产生任务用）
 *
 * <AUTHOR>
 */
@Slf4j
public class AsyncFactory {

//    /**
//     * 记录登录信息
//     *
//     * @param username 用户名
//     * @param status   状态
//     * @param message  消息
//     * @param args     列表
//     * @return 任务task
//     */
//    public static TimerTask recordLogininfor(final String username, final String status, final String message,
//                                             final Object... args) {
//        final UserAgent userAgent = UserAgent.parseUserAgentString(ServletUtils.getRequest().getHeader("User-Agent"));
//        final String ip = IpUtils.getIpAddr(ServletUtils.getRequest());
//        return new TimerTask() {
//            @Override
//            public void run() {
//                String address = AddressUtils.getRealAddressByIP(ip);
//                StringBuilder s = new StringBuilder();
//                s.append(LogUtils.getBlock(ip));
//                s.append(address);
//                s.append(LogUtils.getBlock(username));
//                s.append(LogUtils.getBlock(status));
//                s.append(LogUtils.getBlock(message));
//                // 打印信息到日志
//                sys_user_logger.info(s.toString(), args);
//                // 获取客户端操作系统
//                String os = userAgent.getOperatingSystem().getName();
//                // 获取客户端浏览器
//                String browser = userAgent.getBrowser().getName();
//                // 封装对象
//                SysLogininfor logininfor = new SysLogininfor();
//                logininfor.setUserName(username);
//                logininfor.setIpaddr(ip);
//                logininfor.setLoginLocation(address);
//                logininfor.setBrowser(browser);
//                logininfor.setOs(os);
//                logininfor.setMsg(message);
//                // 日志状态
//                if (Constants.LOGIN_SUCCESS.equals(status) || Constants.LOGOUT.equals(status)) {
//                    logininfor.setStatus(Constants.SUCCESS);
//                } else if (Constants.LOGIN_FAIL.equals(status)) {
//                    logininfor.setStatus(Constants.FAIL);
//                }
//                // 插入数据
//                SpringUtils.getBean(ISysLogininforService.class).insertLogininfor(logininfor);
//            }
//        };
//    }

    /**
     * 操作日志记录
     *
     * @param operLog 操作日志信息
     * @return 任务task
     */
    public static TimerTask recordOper(final OperationLog operLog) {
        return new TimerTask() {
            @Override
            public void run() {
                // 远程查询操作地点
                String ip = operLog.getOperIp();
                String ipAddress = "";
                IpLibraryService ipLibraryService = SpringUtils.getBean(IpLibraryServiceImpl.class);
                String ipaddr = "XX XX";
                if(null != ip && !"".equals(ip.trim())){
                    String[] split = ip.split(",");
                    if(null != split[0]){
                        ipaddr = AddressUtils.getRealAddressByIP(split[0].trim());
                        operLog.setOperIp(split[0].trim());
                        ipAddress = split[0].trim();
                    }else{
                        log.error("获取地理位置异常 {}", ip);
                    }
                }else{
                    log.error("获取地理位置异常 {}", ip);
                }
                ipaddr = ipLibraryService.updateIpAddress(ipAddress, ipaddr);
                operLog.setOperLocation(ipaddr);
                SpringUtils.getBean(OperationLogService.class).insertOperlog(operLog);
            }
        };
    }
}
