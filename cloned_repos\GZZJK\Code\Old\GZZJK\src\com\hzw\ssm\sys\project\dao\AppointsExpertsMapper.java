package com.hzw.ssm.sys.project.dao;

import java.util.List;

import com.hzw.ssm.sys.project.entity.AppointsExpertsEntity;

public interface AppointsExpertsMapper {
	/**
	 * 保存信息
	 * @param project
	 */
	public void saveInfoList(List<AppointsExpertsEntity> entityList);
	/**
	 * 根据批次号查询数据
	 * @param entity
	 * @return
	 */
	public  List<AppointsExpertsEntity> queryDateByDecimationBatch(AppointsExpertsEntity entity);
	/**
	 * 根据批次号查询最大的抽取次数
	 * @param entity
	 * @return
	 */
	public Integer queryMaxCON(AppointsExpertsEntity entity);
	/**
	 * 查询信息
	 * @param project
	 */
	public List<AppointsExpertsEntity> queryList(AppointsExpertsEntity entity);
	/**
	 * 根据ID修改状态
	 * @param entity
	 * @return
	 */
	public Integer updateStatus(AppointsExpertsEntity entity);
	
	/**
	 * 根据ID删除数据
	 * @param entity
	 * @return
	 */
	public Integer deleteExpert(AppointsExpertsEntity entity);
}


