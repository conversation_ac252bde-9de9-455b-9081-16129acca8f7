package com.hzw.sunflower.service.bidder;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hzw.sunflower.controller.project.request.ProjectAcceptanceRep;
import com.hzw.sunflower.controller.project.request.bidder.BidderProjectReq;
import com.hzw.sunflower.controller.project.request.bidder.ProjectConditionRep;
import com.hzw.sunflower.controller.project.response.ProjectAcceptanceListVo;
import com.hzw.sunflower.dto.AgentProjectDTO;
import com.hzw.sunflower.dto.ProjectListDTO;
import com.hzw.sunflower.dto.UserAgentRecordDTO;
import com.hzw.sunflower.entity.JwtUser;
import com.hzw.sunflower.entity.Project;
import com.hzw.sunflower.entity.ProjectAgentApproval;

/**
 * <AUTHOR>
 * @date ：Created in 2021/6/10 9:53
 * @description：招标人项目service
 * @modified By：`
 * @version: 1.0
 */
public interface ProjectAgentApprovalService extends IService<ProjectAgentApproval> {


}
