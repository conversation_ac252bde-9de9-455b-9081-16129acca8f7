package com.hzw.sunflower.config;

import com.hzw.sunflower.config.InterfaceAuth;
import com.hzw.sunflower.constant.CommonConstants;
import com.hzw.sunflower.constant.DictionaryConstants;
import com.hzw.sunflower.entity.Dictionary;
import com.hzw.sunflower.entity.JwtUser;
import com.hzw.sunflower.exception.ExceptionEnum;
import com.hzw.sunflower.exception.SunFlowerException;
import com.hzw.sunflower.service.CommonMenuService;
import com.hzw.sunflower.service.DictionaryService;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.*;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.List;


/**
 * <AUTHOR>
 * @datetime 2024/07/12 14:42
 * @description: fanqh
 * @version: 1.0
 */
@Aspect
@Component
@Slf4j
public class InterfaceAuthAspect {

    @Autowired
    private CommonMenuService commonMenuService;

    @Autowired
    private DictionaryService dictionaryService;

    @Pointcut("@annotation(com.hzw.sunflower.config.InterfaceAuth)")
    public void InterfaceAuthPointcut() {

    }


    @Around("InterfaceAuthPointcut()")
    public Object aroundAdvice(ProceedingJoinPoint joinPoint) throws Throwable {
        // 方法执行前的逻辑
        log.info("***** 开始校验接口权限 *****");
        handleAuth(joinPoint);
        // 执行方法
        Object result = joinPoint.proceed();
        // 方法执行后的逻辑
        log.info("***** 权限校验结束 *****");
        return result;
    }

    private void handleAuth(ProceedingJoinPoint joinPoint) {
        // 校验开关 未设置或不为1则跳过校验
        Dictionary dictionary = dictionaryService.getDictionaryByCode(DictionaryConstants.INTERFACE_AUTH_SWITCH);
        if (null == dictionary || !dictionary.getValue().equals(CommonConstants.YES.toString())) {
            return;
        }
        // 获取注解路径
        InterfaceAuth auth = getAnnotationAuth(joinPoint);
        if (auth == null) {
            return;
        }
        log.info("当前接口permission：{}", auth.permission());

        // 获取当前的用户
        Object principal = SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        JwtUser jwtUser = null;
        if (principal instanceof JwtUser) {
            jwtUser = (JwtUser) principal;
        }
        if (jwtUser == null) {
            log.info("用户token异常，无权访问");
            throw new SunFlowerException(ExceptionEnum.INSUFFICIENT_PERMISSIONS_TO_ACCESS, "无权访问");
        }

        // 校验权限
        Long count = commonMenuService.judgeAuth(jwtUser, auth.permission());
        if (count == 0) {
            throw new SunFlowerException(ExceptionEnum.INSUFFICIENT_PERMISSIONS_TO_ACCESS, "无权访问");
        }
    }

    /**
     * 是否存在注解，如果存在就获取
     */
    private InterfaceAuth getAnnotationAuth(JoinPoint joinPoint) {
        Signature signature = joinPoint.getSignature();
        MethodSignature methodSignature = (MethodSignature) signature;
        Method method = methodSignature.getMethod();
        if (method != null) {
            return method.getAnnotation(InterfaceAuth.class);
        }
        return null;
    }

}
