///***
/// * 日期选择器
/// *
/// * @return {String} 会返回 'YYYY-MM-DD HH:mm:ss' 格式的日期字符串
/// */

import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';

import 'package:flutter_main_app/src/constants/theme.dart';
import 'package:flutter_main_app/src/utils/ScreenUtils.dart';

class CustomDatePicker extends StatefulWidget {
  const CustomDatePicker({
    Key key,
    this.title = '',
    this.mode = CupertinoDatePickerMode.date,
    this.initialDateTime,
    this.minimumDate,
    this.maximumDate,
    this.minimumYear = 1,
    this.maximumYear,
    this.minuteInterval = 1,
    this.use24hFormat = false,
  }) : assert(title != null),
    assert(mode != null),
    assert(minimumYear != null),
    super(key: key);

  /// * 标题，可为空字符串
  final String title;

  /// * CupertinoDatePicker 的属性
  final CupertinoDatePickerMode mode;
  final DateTime initialDateTime;
  final DateTime minimumDate;
  final DateTime maximumDate;
  final int minimumYear;
  final int maximumYear;
  final int minuteInterval;
  final bool use24hFormat;

  @override
  _CustomDatePickerState createState() => _CustomDatePickerState();
}

class _CustomDatePickerState extends State<CustomDatePicker>{
  String dateTime;

  @override
  void initState() {
    super.initState();

    this.dateTime = widget.initialDateTime != null
      ? widget.initialDateTime.toString().substring(0, 19)
      : DateTime.now().toString().substring(0, 19);
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      child: Container(
        width: ScreenUtils.screenWidth,
        color: themeContentBackgroundColor,
        child: SafeArea(
          top: false,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              /// * 功能按钮 - 取消|确定
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 30.0),
                width: ScreenUtils.screenWidth,
                height: 42.0,
                color: const Color(0xffe5e5e5),
                child: Row(
                  mainAxisSize: MainAxisSize.max,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: <Widget>[
                    GestureDetector(
                      child: Text(
                        '取消',
                        style: TextStyle(
                          fontSize: 14.0,
                          color: const Color(0xff999999),
                        ),
                      ),
                      onTap: () {
                        this._checkboxCanceled(context);
                      },
                    ),
                    Text(
                      widget.title,
                      style: TextStyle(
                        fontSize: 14.0,
                        color: themeTextColor,
                      ),
                    ),
                    GestureDetector(
                      child: Text(
                        '确定',
                        style: TextStyle(
                          fontSize: 14.0,
                          color: themeActiveColor,
                        ),
                      ),
                      onTap: () {
                        this._checkboxConfirmed(context);
                      },
                    ),
                  ],
                ),
              ),
              /// * 日期/时间 选择器
              Container(
                constraints: BoxConstraints(
                  minHeight: 0.0,
                  maxHeight: 195.0,
                ),
                child: CupertinoDatePicker(
                  mode: widget.mode,
                  onDateTimeChanged: (DateTime dateTime) {
                    setState(() {
                      this.dateTime = dateTime.toString().substring(0, 19);
                    });
                  },
                  initialDateTime: widget.initialDateTime,
                  minimumDate: widget.minimumDate,
                  maximumDate: widget.maximumDate,
                  minimumYear: widget.minimumYear,
                  maximumYear: widget.maximumYear,
                  minuteInterval: widget.minuteInterval,
                  use24hFormat: widget.use24hFormat,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  ///**
  /// * 点击取消
  /// * 路由弹出，什么都不返回
  /// *
  /// * @param {BuildContext} context
  /// */
  void _checkboxCanceled(BuildContext context) {
    Navigator.of(context).pop();
  }

  ///**
  /// * 点击确定
  /// * 路由弹出，并返回当前设置
  /// *
  /// * @param {BuildContext} context
  /// */
  void _checkboxConfirmed(BuildContext context) {
    Navigator.of(context).pop(this.dateTime);
  }

}
