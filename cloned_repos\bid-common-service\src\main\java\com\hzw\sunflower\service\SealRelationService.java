package com.hzw.sunflower.service;

import java.util.List;
import java.util.Map;

public interface SealRelationService {
    /**
     * 修改业务状态
     * @param sealId
     * @param approveStatus
     * @return
     */
    Boolean updateBusinessStatus(Long sealId, Integer approveStatus);

    /**
     * 修改用印申请关联表状态
     *
     * @param sealId
     * @param approveStatus
     */
    void updateStatus(Long sealId, Integer approveStatus);

    /**
     * 判断该标段是否暂停
     * @param sectionId
     */
    Boolean checkIsPaused(Long sectionId,List<Long> sectionIds);

    Map<Long,Boolean> checkPaused(List<Long> sectionIds);
}
