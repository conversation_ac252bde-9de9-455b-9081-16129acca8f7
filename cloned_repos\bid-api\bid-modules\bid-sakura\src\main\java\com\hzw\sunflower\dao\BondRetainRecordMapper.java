package com.hzw.sunflower.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hzw.sunflower.entity.BondRetainRecord;
import com.hzw.sunflower.entity.BondSplit;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;

/**
 * <p>
 * 保证金保留记录 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-15
 */
@Mapper
public interface BondRetainRecordMapper extends BaseMapper<BondRetainRecord> {

    /**
     * 查询所有分批次退还记录的利息总和
     * @param refundId
     * @return
     */
    BigDecimal getCountRateByRefundId(@Param("refundId") Long refundId);
}
