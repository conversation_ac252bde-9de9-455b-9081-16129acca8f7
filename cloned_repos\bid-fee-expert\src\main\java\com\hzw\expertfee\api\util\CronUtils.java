package com.hzw.expertfee.api.util;

import org.quartz.impl.triggers.CronTriggerImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021/7/16
 */
public class CronUtils {
    private static final Logger logger = LoggerFactory.getLogger(CronUtils.class);

    public static boolean isValidExpression(final String cronExpression) {
        CronTriggerImpl trigger = new CronTriggerImpl();
        try {
            trigger.setCronExpression(cronExpression);
            Date date = trigger.computeFirstFireTime(null);
            return date != null && date.after(new Date());
        } catch (Exception e) {
            logger.error("invalid expression:{},error msg:{}", cronExpression, e.getMessage());
        }
        return false;
    }

}
