package com.hzw.sunflower.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzw.sunflower.constant.constantenum.BookKeepConfirmTypeEnum;
import com.hzw.sunflower.constant.constantenum.ProjectShareEnum;
import com.hzw.sunflower.controller.project.request.AgentProjectReq;
import com.hzw.sunflower.dao.ProjectShareMapper;
import com.hzw.sunflower.dto.ProjectShareDTO;
import com.hzw.sunflower.entity.Project;
import com.hzw.sunflower.entity.ProjectShare;
import com.hzw.sunflower.entity.condition.ProjectShareCondition;
import com.hzw.sunflower.service.ProjectEntrustUserService;
import com.hzw.sunflower.service.ProjectService;
import com.hzw.sunflower.service.ProjectShareService;
import com.hzw.sunflower.util.BeanListUtil;
import com.hzw.sunflower.util.LoginUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;


/**
* ProjectShareService接口实现
*
* <AUTHOR>
* @version 1.0.0 2021-04-07
*/
@Service
public class ProjectShareServiceImpl extends ServiceImpl<ProjectShareMapper, ProjectShare> implements ProjectShareService {

    @Autowired
    private ProjectEntrustUserService projectEntrustUserService;

    @Autowired
    private ProjectService projectService;


    /**
    * 根据条件分页查询列表
    *
    * @param condition 查询条件
    * @return 分页数据
    */
    @Override
    public IPage<ProjectShare> findInfoByCondition(ProjectShareCondition condition) {
        IPage<ProjectShare> page = condition.buildPage();
        QueryWrapper<ProjectShare> queryWrapper = condition.buildQueryWrapper(ProjectShare.class);
        return this.page(page, queryWrapper);
    }

    /**
    * 根据主键ID查询
    *
    * @param id 主键ID
    * @return
    */
    @Override
    public ProjectShare getInfoById(Long id) {
        return this.getById(id);
    }

    /**
    * 新增
    *
    * @param projectShare
    * @return 是否成功
    */
    @Override
    public Boolean addInfo(ProjectShareDTO projectShare) {
        return this.save(projectShare);
    }

    /**
    * 修改
    *
    * @param projectShare
    * @return 是否成功
    */
    @Override
    public Boolean updateInfo(ProjectShareDTO projectShare) {
        return this.updateById(projectShare);
    }

    /**
    * 根据主键ID删除
    *
    * @param id 主键ID
    * @return 是否成功
    */
    @Override
    public Boolean deleteById(Long id) {
        return this.removeById(id);
    }

    /**
    * 根据主键ID列表批量删除
    *
    * @param idList 主键ID列表
    * @return 是否成功
    */
    @Override
    public Boolean deleteByIds(List<Long> idList) {
        return this.removeByIds(idList);
    }

    /**
     * 添加项目分摊信息
     */
    @Override
    public void updateShareList(AgentProjectReq agentProject,List<ProjectShareDTO> allShareList) {
        // 先去删除之前的数据
        QueryWrapper<ProjectShare> qw = new QueryWrapper<>();
        qw.lambda().eq(ProjectShare::getProjectId,agentProject.getProjectId());
        this.getBaseMapper().delete(qw);
        // 分摊的项目按照页面传入的新增
        //if(ProjectShareEnum.YES.getType().equals(isShare)){
         if(null != agentProject.getShareList() && agentProject.getShareList().size() > 0 && null != agentProject.getShareList().get(0).getShareDepartment()){
             for (ProjectShare projectShare : agentProject.getShareList()) {
                 projectShare.setProjectId(agentProject.getProjectId());
                 this.save(projectShare);

                 //过滤分摊人是自己的情况
                 if(null != projectShare.getShareUser() && !projectShare.getShareUser().equals(LoginUtil.getJwtUser().getUserId())) {
                     ProjectShareDTO shareDTO = BeanListUtil.convert(projectShare, ProjectShareDTO.class);
                     shareDTO.setShareType(BookKeepConfirmTypeEnum.COST.getType());
                     allShareList.add(shareDTO);
                 }
             }
         }
        //}else{
        // 不分摊的项目按照当前操作人添加
        //    ProjectEntrustUserVo projectEntrustUserVos = projectEntrustUserService.selectEntrustUserAndDepartment(projectId);
        //    if(null != projectEntrustUserVos ){
        //        ProjectShare ps = new ProjectShare();
        //        ps.setShareDepartment(projectEntrustUserVos.getDepartmentId());
        //        ps.setProjectId(projectId);
        //        ps.setShareDepartmentName(projectEntrustUserVos.getDepartmentName());
        //        ps.setShareUserName(projectEntrustUserVos.getUserName());
        //        ps.setShareUser(projectEntrustUserVos.getUserId());
        //        ps.setProportion(new BigDecimal("100"));
        //        this.save(ps);
        //    }
        //}

        }


    /**
     * 判断项目集合 和来源项目的分摊人是否完全一致
     * @param projectId
     * @param ids
     * @return
     */
    @Override
    public List<Long> findProjectShareIdentical(Long projectId, List<Long> ids){
         List<Long> projectIds = null;
         if(null != ids && ids.size() > 0){
             Project sourceProject = projectService.getProjectById(projectId);
             // 不分摊的直接查询项目id
//             if(sourceProject.getIsShare().equals(ProjectShareEnum.NO.getType())){
//                 projectIds = projectService.findShareProject(ProjectShareEnum.NO.getType(),ids);
//             }else{//分摊项目需要对比项目分摊情况
                 List<Long> zids = new ArrayList<>();
                 QueryWrapper<ProjectShare> qw = new QueryWrapper<>();
                 qw.lambda().eq(ProjectShare::getProjectId,projectId);
                 List<ProjectShare> list = this.list(qw);
                 projectIds = projectService.findShareProject(ProjectShareEnum.YES.getType(),ids);
                 if(null != projectIds && projectIds.size() > 0){
                     for (Long id : projectIds) {
                         QueryWrapper<ProjectShare> sqw = new QueryWrapper<>();
                         sqw.lambda().eq(ProjectShare::getProjectId,id);
                         List<ProjectShare> shares = this.list(sqw);
                         if(list.size() != shares.size()){// 不相等的项目 直接过滤
                              continue;
                         }else{
                             Boolean flag = true;
                             for (ProjectShare projectShare : list) {
                                 long count = shares.stream()
                                         .filter((share) -> share.getShareDepartment().equals(projectShare.getShareDepartment())
                                                 && share.getShareUser().equals(projectShare.getShareUser())
                                                 && share.getProportion().equals(projectShare.getProportion())).count();
                                  if(count == 0){
                                      flag = false;
                                      continue;
                                  }
                             }
                             if(flag){
                                 zids.add(id);
                             }
                         }

                     }
                     projectIds = zids;
                 }

//             }

         }

        return projectIds;
    }

    @Override
    public List<ProjectShare> findInfo(Long id) {
        return this.baseMapper.findInfo(id);
    }

}