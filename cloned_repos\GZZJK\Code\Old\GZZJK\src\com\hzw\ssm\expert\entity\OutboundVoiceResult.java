package com.hzw.ssm.expert.entity;

/**
 * 外呼结果查询
 * 
 * <AUTHOR>
 * @date 2018-03-27
 */
public class OutboundVoiceResult{

	/**
	 * 项目id
	 */
	private String project_id;
	
	/**
	 * 主叫
	 */
	private String org_num;
	
	/**
	 * 被叫
	 */
	private String call_num;
	
	/**
	 * 外呼结果
	 *  结果码释义:
	 *  NORMAL_CIRCUIT_CONGESTION	网络忙
	 *  NO_ANSWER	无人接听
	 *  NORMAL_CLEARING	正常挂断
	 *  SUBSCRIBER_ABSENT	关机
	 *  NORMAL_UNSPECIFIED	暂停服务
	 *  UNALLOCATED_NUMBER	空号
	 *  USER_BUSY	用户忙
	 *  CALL_REJECTED	拒绝接听
	 */
	private String call_result;
	
	/**
	 * 启呼时间
	 */
	private String call_time;
	
	/**
	 * 接听时间
	 */
	private String answer_time;
	
	/**
	 * 挂机时间
	 */
	private String end_time;
	
	/**
	 * 创建时间
	 */
	private String create_time;
	
	/**
	 * 客户数据
	 */
	private String client_data;
	
	/**
	 * 用户按键
	 */
	private String oper;

	public String getProject_id() {
		return project_id;
	}

	public void setProject_id(String project_id) {
		this.project_id = project_id;
	}

	public String getOrg_num() {
		return org_num;
	}

	public void setOrg_num(String org_num) {
		this.org_num = org_num;
	}

	public String getCall_num() {
		return call_num;
	}

	public void setCall_num(String call_num) {
		this.call_num = call_num;
	}

	public String getCall_result() {
		return call_result;
	}

	public void setCall_result(String call_result) {
		this.call_result = call_result;
	}

	public String getCall_time() {
		return call_time;
	}

	public void setCall_time(String call_time) {
		this.call_time = call_time;
	}

	public String getAnswer_time() {
		return answer_time;
	}

	public void setAnswer_time(String answer_time) {
		this.answer_time = answer_time;
	}

	public String getEnd_time() {
		return end_time;
	}

	public void setEnd_time(String end_time) {
		this.end_time = end_time;
	}

	public String getCreate_time() {
		return create_time;
	}

	public void setCreate_time(String create_time) {
		this.create_time = create_time;
	}

	public String getClient_data() {
		return client_data;
	}

	public void setClient_data(String client_data) {
		this.client_data = client_data;
	}

	public String getOper() {
		return oper;
	}

	public void setOper(String oper) {
		this.oper = oper;
	}
	
}
