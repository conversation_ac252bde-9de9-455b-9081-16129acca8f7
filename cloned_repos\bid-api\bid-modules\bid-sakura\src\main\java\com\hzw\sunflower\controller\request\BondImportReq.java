package com.hzw.sunflower.controller.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date ：Created in 2022/8/2 8:54
 * @description：自定义导入参数
 * @modified By：`
 * @version: 1.0
 */
@ApiModel(description = "自定义导入参数")
@Data
public class BondImportReq implements Serializable {

//    @ApiModelProperty(value = "excel文件")
//    private MultipartFile file;

    @ApiModelProperty(value = "银行模板id")
    private Long templateId;

    @ApiModelProperty(value = "excel文件id")
    private Long ossFileId;

}
