package com.hzw.sunflower.constant.constantenum;

/**
 * <AUTHOR>
 * @date ：Created in 2022/07/27 9:23
 * @description：申请类型
 * @version: 1.0
 */
public enum RefundModeEnum {
    //退还方式 1：收取并退还剩余保证金  2：收取并保留剩余保证金  3：收取并支持再次收取   4：收入并结束收取
    REFUND_SURPLUS(1, "收取并退还剩余保证金"),
    RETAIN_SURPLUS(2, "收取并保留剩余保证金"),
    AGAIN_GATHER(3, "收取并支持再次收取"),
    END_GATHER(4, "收入并结束收取");

    private Integer type;
    private String desc;

    RefundModeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
