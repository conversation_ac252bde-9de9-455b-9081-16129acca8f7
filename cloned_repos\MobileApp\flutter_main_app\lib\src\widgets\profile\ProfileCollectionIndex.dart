///***
/// * 我的 - 我的收藏 - 入口页
/// */

import 'package:flutter/material.dart';

import 'package:flutter_main_app/src/components/CustomAppBar.dart';
import 'package:flutter_main_app/src/components/CustomSafeArea.dart';

class ProfileCollectionIndex extends StatefulWidget {
  const ProfileCollectionIndex({Key key}) : super(key: key);

  /// * 路由名称
  static const String _routeName = 'ProfileCollectionIndex';
  static String get routeName => _routeName;

  @override
  _ProfileCollectionIndexState createState() => _ProfileCollectionIndexState();
}

class _ProfileCollectionIndexState extends State<ProfileCollectionIndex> {

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: '我的收藏',
        actions: <Widget>[
          GestureDetector(
            onTap: () {
              print('edit pressed...');
            },
            child: Container(
              width: 58.0,
              height: 48.0,
              child: Center(
                child: Text(
                  '编辑',
                ),
              ),
            ),
          ),
        ],
      ),
      body: CustomSafeArea(
        child: <PERSON>View(
          physics: BouncingScrollPhysics(),
          children: <Widget>[],
        ),
      ),
    );
  }
}
