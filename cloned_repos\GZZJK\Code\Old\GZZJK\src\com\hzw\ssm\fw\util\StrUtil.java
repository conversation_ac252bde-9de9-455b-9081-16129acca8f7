package com.hzw.ssm.fw.util;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.servlet.FilterConfig;
import javax.servlet.ServletException;

public class StrUtil {
	
	private static Map<String,String> filterMap = new LinkedHashMap<String,String>();
	
	static{
        //含有脚本script
        filterMap.put("[s|S][c|C][r|R][i|C][p|P][t|T]", "");
        //含有脚本javascript
        filterMap.put("[\\\"\\\'][\\s]*[j|J][a|A][v|V][a|A][s|S][c|C][r|R][i|I][p|P][t|T]:(.*)[\\\"\\\']", "\"\"");
        //含有eval函数
        filterMap.put("[e|E][v|V][a|A][l|L]\\((.*)\\)", "");
        //含有特殊符号
  /*      filterMap.put("<", "<");
        filterMap.put(">", ">");
        filterMap.put("\\(", "(");
        filterMap.put("\\)", ")");
        filterMap.put("(", "(");
        filterMap.put(")", ")");
        filterMap.put("'", "'");
        filterMap.put("\"", "'");
        filterMap.put(";", ";");*/
        /*filterMap.put("+", "+");
        filterMap.put("|", "¦");
        filterMap.put("$", ";");
        filterMap.put("@", ";");*/
        //filterMap.put("%", ";");
	}
	
	/**
	 * 直接从信息返回对象中取返回值为字符串的值
	 * 
	 * @param strKey
	 *            返回参数的关键字
	 * @return 返回参数的字符串值
	 */
	public String getString(Map<String, Object> map, String strKey) {
		String strReturn = "";

		if (map != null) {
			Object object = map.get(strKey);
			if (object != null && object instanceof String) {
				strReturn = (String) object;
			}
		}

		return strReturn;
	}

	/**
	 * 直接从信息返回对象中取返回值为字符串数组的值
	 * 
	 * @param strKey
	 *            返回参数的关键字
	 * @return 返回参数的字符串数组
	 */
	public String[] getStringArray(Map<String, Object> map, String strKey) {
		String[] strReturn = null;

		if (map != null) {
			Object object = map.get(strKey);
			if (object != null && object instanceof String[]) {
				strReturn = (String[]) object;
			}
		}

		return strReturn;
	}

	/**
	 * 
	 * @param hm :
	 *            需要取值的HashMap
	 * @param key :
	 *            HashMap中的key
	 * @return
	 */
	public String getStringByKey(Map<String, Object> map, String key) {
		Object object = map.get(key);
		String str = null;

		if (object != null) {
			if (object instanceof String[]) {
				str = ((String[]) object)[0];
			} else if (object instanceof String) {
				str = (String) object;
			}

			if (str == null) {
				str = "";
			}
		} else {
			str = "";
		}

		return str;
	}

	/**
	 * 
	 * @param hm :
	 *            需要取值的HashMap
	 * @param key :
	 *            HashMap中的key
	 * @return
	 */
	public String[] getStringsByKey(Map<String, Object> map, String key) {
		String[] strReturn = null;
		Object object = map.get(key);
		if (object != null) {
			if (object instanceof String[]) {
				strReturn = (String[]) object;
			} else if (object instanceof String) {
				strReturn = new String[] { (String) object };
			}
		}
		return strReturn;
	}

	/**
	 * 
	 * @param hm :
	 *            需要取值的HashMap
	 * @param key :
	 *            HashMap中的key
	 * @return
	 */
	public int getIntByKey(Map<String, Object> map, String key) {
		int ret = -1;
		String strTemp = getStringByKey(map, key);
		try {
			ret = Integer.valueOf(strTemp).intValue();
		} catch (Exception e) {
			ret = -1;
		}
		return ret;
	}

	/**
	 * 将null转换成空
	 * 
	 * @param str
	 *            可能为null的对象
	 * @return 返回非null的字符串
	 */
	public String nulTOstr(Object obj) {
		String ret = "";
		if (obj instanceof String) {
			ret = (String) obj;
		}
		return ret;
	}

	/**
	 * 将null转换成空
	 * 
	 * @param str
	 *            可能为null的对象
	 * @return 返回非null的字符串
	 */
	public String nul2blank(Object obj) {
		String ret = "&nbsp;";
		if (obj instanceof String) {
			ret = (String) obj;
			ret = ret.trim();
		}
		return ret;
	}

	/**
	 * 判断字符串中是否包含script,update set,insert into等敏感单词
	 * 
	 * @param str
	 *            待检字符串
	 * @return true：有敏感词 false：无敏感词
	 */
/*	public static boolean isAttack(String str) {
		boolean res = false;
		if (null == str || "".equals(str.trim())) {
			return res;
		}
		Pattern p = Pattern
				.compile("\\b\\w*[Ss]\\s*[Cc]\\s*[Rr]\\s*[Ii]\\s*[Pp]\\s*[Tt]\\b|\\b[Ii]\\s*[Nn]\\s*[Ss]\\s*[Ee]\\s*[Rr]\\s*[Tt]\\s*[Ii]\\s*[Nn]\\s*[Tt]\\s*[Oo]\\b|\\b[Uu]\\s*[Pp]\\s*[Dd]\\s*[Aa]\\s*[Tt]\\s*[Ee][\\s\\w]*[sS]\\s*[Ee]\\s*[Tt]\\b");
		Matcher m = p.matcher(str);
		res = m.matches();
		return res;
	}*/

	/**
	 * 两个时间之间相差距离多少天
	 */
	public static long getDistanceDays(Date str1, Date str2) {
		DateFormat df = new SimpleDateFormat("yyyy-MM-dd");
		Date one;
		Date two;
		long days = 0;
		try {
			one = df.parse(df.format(str1));
			two = df.parse(df.format(str2));
			long time1 = one.getTime();
			long time2 = two.getTime();
			long diff;
			if (time1 < time2) {
				diff = time2 - time1;
			} else {
				diff = time1 - time2;
			}
			days = diff / (1000 * 60 * 60 * 24);
		} catch (ParseException e) {
			e.printStackTrace();
		}
		return days;

	}
	
	
    /**
     * 清除恶意的脚本
     */
    public static String cleanXSS(String value) {
        Set<String> keySet = filterMap.keySet();
        for(String key : keySet){
            if(value.contains(key)){
                return value;
            }       }
        return "";
    }
	
         
}
