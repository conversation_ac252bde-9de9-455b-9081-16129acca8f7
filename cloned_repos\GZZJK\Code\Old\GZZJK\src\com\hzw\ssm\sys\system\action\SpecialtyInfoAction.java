package com.hzw.ssm.sys.system.action;

import java.io.IOException;
import java.io.PrintWriter;
import java.util.List;

import javax.servlet.http.HttpServletResponse;

import org.apache.struts2.ServletActionContext;
import org.apache.struts2.convention.annotation.Action;
import org.apache.struts2.convention.annotation.Namespace;
import org.apache.struts2.convention.annotation.ParentPackage;
import org.apache.struts2.convention.annotation.Result;
import org.apache.struts2.convention.annotation.Results;
import org.springframework.beans.factory.annotation.Autowired;

import com.hzw.ssm.expert.entity.SpecialtyInfoEntity;
import com.hzw.ssm.fw.base.BaseAction;
import com.hzw.ssm.sys.system.service.SpecialtyInfoService;

/**
 * 专家专业类别管理
 * 
 * <AUTHOR>
 * 
 */
@Namespace("/specialtyInfo")
@ParentPackage(value = "default")
@Results( { @Result(name = "init", location = "/jsp/sys/specialtyList.jsp"),
		@Result(name = "toSpecialtyMain", location = "/jsp/sys/specialtyMain.jsp"),
		@Result(name = "toSpecialtyRight", location = "/jsp/sys/specialtyRight.jsp"),
		@Result(name = "toSpecialtyLeft", location = "/jsp/sys/specialtyLeft.jsp"),
		@Result(name = "toSpecialtyAdd", location = "/jsp/sys/specialtyAdd.jsp") })
public class SpecialtyInfoAction extends BaseAction {

	/**
	 * 
	 */
	private static final long serialVersionUID = -8823858495799309882L;

	/** 专业类别对象列表 */
	private List<SpecialtyInfoEntity> specialtyList;

	/** 专业类别对象列表的查询条件 */
	private SpecialtyInfoEntity specialtyInfoEntity;

	/**
	 * 类别ID
	 */
	private String specialtyId;

	/**
	 * 类别字符串
	 */
	private String specialtyStr;

	/** 需要编辑的用户编号 */
	private String key;

	/** 提示信息 */
	private String msg;

	private String statusCode;
	private String message;

	@Autowired
	private SpecialtyInfoService specialtyInfoService;

	@Action("toSpecialtyMain")
	public String toSpecialtyMain() {
		return "toSpecialtyMain";
	}

	/**
	 * 加载树形专业类别信息
	 * 
	 * @return
	 */
	@Action("querySpecialtyList")
	public String querySpecialtyList() {
		specialtyStr = specialtyInfoService.querySpecialtyList();
		return "toSpecialtyLeft";
	}

	/**
	 * 获取专业类别信息
	 */
	@Action("findSpecialty")
	public String findSpecialty() {
		if (null == specialtyInfoEntity) {
			specialtyInfoEntity = new SpecialtyInfoEntity();
		}
		specialtyInfoEntity = specialtyInfoService.findSpecialty(specialtyInfoEntity);
		return "toSpecialtyRight";
	}

	/**
	 * 更新专业类别信息
	 * 
	 * @return
	 */
	@Action("editSpecialty")
	public String editSpecialty() {
		return specialtyInfoService.editSpecialty(specialtyInfoEntity);
	}

	/**
	 * 检查专业类别名称是否存在
	 * 
	 * @return
	 */
	@Action("checkSpecialty")
	public String checkSpecialty() {
		List<SpecialtyInfoEntity> specialtyOne = specialtyInfoService.checkSpecialty(specialtyInfoEntity);
		if (null != specialtyOne && specialtyOne.size() > 0) {
			HttpServletResponse response = ServletActionContext.getResponse();
			response.setContentType("text/html;charset=utf-8");
			PrintWriter out = null;
			try {
				out = response.getWriter();
				out.print("have");
			} catch (IOException e) {
				e.printStackTrace();
			} finally {
				if (null != out)
					out.close();
			}
		}
		return null;
	}

	/**
	 * 增加专业类别信息
	 * 
	 * @return
	 */
	@Action("saveSpecialty")
	public String saveSpecialty() {
		this.context();
		try {
			specialtyInfoService.saveSpecialty(specialtyInfoEntity);
			this.getResponse().setContentType("text/html;charset=UTF-8");
			this.getResponse().getWriter().write("<script>window.close();</script>");
		} catch (IOException e) {
			e.printStackTrace();
		}
		return null;
	}

	/**
	 * 修改专业类别信息
	 * 
	 * @return
	 */
	@Action("modifySpecialty")
	public String modifySpecialty() {
		specialtyInfoService.modifySpecialty(specialtyInfoEntity);
		return "toSpecialtyMain";
	}

	public List<SpecialtyInfoEntity> getSpecialtyList() {
		return specialtyList;
	}

	public void setSpecialtyList(List<SpecialtyInfoEntity> specialtyList) {
		this.specialtyList = specialtyList;
	}

	public SpecialtyInfoEntity getSpecialtyInfoEntity() {
		return specialtyInfoEntity;
	}

	public void setSpecialtyInfoEntity(SpecialtyInfoEntity specialtyInfoEntity) {
		this.specialtyInfoEntity = specialtyInfoEntity;
	}

	public String getSpecialtyId() {
		return specialtyId;
	}

	public void setSpecialtyId(String specialtyId) {
		this.specialtyId = specialtyId;
	}

	public String getSpecialtyStr() {
		return specialtyStr;
	}

	public void setSpecialtyStr(String specialtyStr) {
		this.specialtyStr = specialtyStr;
	}

	public String getKey() {
		return key;
	}

	public void setKey(String key) {
		this.key = key;
	}

	public String getMsg() {
		return msg;
	}

	public void setMsg(String msg) {
		this.msg = msg;
	}

	public String getStatusCode() {
		return statusCode;
	}

	public void setStatusCode(String statusCode) {
		this.statusCode = statusCode;
	}

	public String getMessage() {
		return message;
	}

	public void setMessage(String message) {
		this.message = message;
	}

}
