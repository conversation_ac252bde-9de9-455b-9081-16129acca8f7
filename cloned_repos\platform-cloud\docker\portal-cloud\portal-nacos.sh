#!/bin/sh
#cp TEMPLATE FILE
docker stop portal-nacos || true
docker rm portal-nacos || true
docker rmi registry.cn-hangzhou.aliyuncs.com/hzw-jtcc/jtcc:portal-nacos-1.0.0 || true
docker pull registry.cn-hangzhou.aliyuncs.com/hzw-jtcc/jtcc:portal-nacos-1.0.0
docker run -itd -p 8848:8848 --net=host  --log-opt max-size=100m --log-opt max-file=3  --name portal-nacos -v /home/<USER>/plan1/portal-cloud/config/nacos:/config -v /data/log/nacos:/root/nacos/logs  registry.cn-hangzhou.aliyuncs.com/hzw-jtcc/jtcc:portal-nacos-1.0.0
