/**
 * 
 */
package com.hzw.ssm.sys.project.entity;

import com.hzw.ssm.fw.util.SysConstants;

/**
 * 专家抽取人数
 * <AUTHOR>
 *
 */
public class ExpertNum {

	/**
	 * 类型编号
	 */
	private String expert;
	
	/**
	 * 类型编号
	 */
	private String expertName;
	/**
	 * 需要总人数
	 */
	private Integer needNum;
	
	/**
	 * 还需要抽取的人数
	 */
	private Integer stillNeedNum;
	/**
	 * 库中拥有该类型合格专家人数
	 */
	private Integer haveNum;

	/**
	 * 缺少人数 （人数不足时有值）
	 */
	private Integer lackNum;
	
	public String getExpert() {
		return expert;
	}

	public void setExpert(String expert) {
		this.expert = expert;
	}

	public String getExpertName() {
		return expertName;
	}

	public void setExpertName(String expertName) {
		this.expertName = expertName;
	}

	public Integer getNeedNum() {
		return needNum;
	}

	public void setNeedNum(Integer needNum) {
		this.needNum = needNum;
	}

	public Integer getHaveNum() {
		return haveNum;
	}

	public void setHaveNum(Integer haveNum) {
		this.haveNum = haveNum;
	}
	
	public Integer getLackNum() {
		return lackNum;
	}

	public void setLackNum(Integer lackNum) {
		this.lackNum = lackNum;
	}
	
	
	public Integer getStillNeedNum() {
		return stillNeedNum;
	}

	public void setStillNeedNum(Integer stillNeedNum) {
		this.stillNeedNum = stillNeedNum;
	}

	public void addNeedNum() {
		if(this.haveNum==null) {
			this.haveNum=1;
		}else {
		this.haveNum=haveNum+1;
		}
	}
	
	public boolean checkedNum() {
		boolean flag=(this.stillNeedNum*SysConstants.EXPERT_EXTRACTION_MULTIPLE)<=this.haveNum;
		if(!flag) {
			this.lackNum=(this.stillNeedNum*SysConstants.EXPERT_EXTRACTION_MULTIPLE)-this.haveNum;
		}
		return flag;
	}
}
