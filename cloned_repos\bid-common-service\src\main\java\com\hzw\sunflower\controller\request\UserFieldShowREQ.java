package com.hzw.sunflower.controller.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 用户字段展示表 请求实体
 *
 * <AUTHOR>
 * @version 1.0.0 2021-04-12
 */
@ApiModel(description = "用户字段展示表 ")
@Data
public class UserFieldShowREQ {

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "用户ID")
    private Long userId;

    @ApiModelProperty(value = "单位ID")
    private Long companyId;

    @ApiModelProperty(value = "页面ID")
    private String pageCode;
}