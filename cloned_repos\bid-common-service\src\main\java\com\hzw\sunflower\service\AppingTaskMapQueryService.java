package com.hzw.sunflower.service;

import com.hzw.sunflower.controller.response.ProjectTaskVO;

import java.util.List;
import java.util.Map;


/**
 *
 * 工作台列表列扩展数据
 */
public interface AppingTaskMapQueryService {


    /**
     * 根据ID集合查询代办列表
     *
     * @param ids
     * @return
     */
    Map<Long, ProjectTaskVO> queryAppingTaskMapByIds(List<Long> ids);

    /**
     * 根据 businessKey （表名:id ,例：t_notice:233）集合查询代办列表
     * @param businessKeys
     * @return
     */
    Map<String, ProjectTaskVO> queryAppingTaskMapByBusinessKeys(List<String> businessKeys);


}
