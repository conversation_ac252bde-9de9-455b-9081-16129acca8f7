package com.hzw.sunflower.controller.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 模板信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-23
 */
@Data
public class TemplateVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "模板code")
    private String templateCode;

    @ApiModelProperty(value = "模板二级code")
    private String templateSecondCode;

    @ApiModelProperty(value = "模板名称")
    private String templateName;

    @ApiModelProperty(value = "模板内容")
    private String templateContent;

    @ApiModelProperty(value = "模板类型1：全局 2：个人")
    private Integer type;

    @ApiModelProperty(value = "主键")
    private Long id;
}
