package com.hzw.ssm.expert.service;

import com.alibaba.fastjson.JSON;
import com.hzw.ssm.applets.dao.DictionaryMapper;
import com.hzw.ssm.applets.dao.EducationInfoMapper;
import com.hzw.ssm.applets.dao.TitleInfoMapper;
import com.hzw.ssm.applets.entity.Dictionary;
import com.hzw.ssm.applets.entity.EducationInfo;
import com.hzw.ssm.applets.entity.TitleInfo;
import com.hzw.ssm.expert.dao.ExpertInfoMapper;
import com.hzw.ssm.expert.entity.ExpertInfoEntity;
import com.hzw.ssm.expert.entity.ExpertUpdateRecordEntity;
import com.hzw.ssm.fw.base.BaseService;
import com.hzw.ssm.fw.util.CommUtil;
import com.hzw.ssm.fw.util.SysConstants;
import com.hzw.ssm.sys.user.dao.UserMapper;
import com.hzw.ssm.util.FileTypeCheck;
import org.apache.commons.lang.StringUtils;
import org.apache.struts2.ServletActionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.*;
import java.util.*;

/**
 * <AUTHOR>
 * @Date 2021-02-24 13:53
 * @Version 1.0
 */
@Service
public class ExpertEducationService extends BaseService {

    @Autowired
    private EducationInfoMapper educationInfoMapper;
    @Autowired
    private TitleInfoMapper titleInfoMapper;
    @Autowired
    private ExpertInfoMapper expertInfoMapper;
    @Autowired
    private UserMapper userMapper;
    @Autowired
    private DictionaryMapper dictionaryMapper;

    public void uploadExpertEducation(ExpertInfoEntity expertInfoEntity, List<EducationInfo> educationList, File educationFile1, String educationFile1FileName, File educationFile2, String educationFile2FileName, File educationFile3, String educationFile3FileName, File educationFile4, String educationFile4FileName, File educationFile5, String educationFile5FileName, File educationFile0, String educationFile0FileName) {
        String rootPath = ServletActionContext.getServletContext().getRealPath("");
        String savePath = rootPath + "/" + SysConstants.EXPERT_FILE_PATH_ROOT + expertInfoEntity.getUser_id();

        if (educationFile0 != null) {
            String newName = FileTypeCheck.fileTypeCheck(educationFile0FileName);
            String filePath = savePath + "/" + newName;
            File file = new File(filePath);
            boolean result = this.copyFile(educationFile0, file);
            if (result) {
                educationList.get(0).setEducationCertificate(SysConstants.EXPERT_FILE_PATH_ROOT + expertInfoEntity.getUser_id() + "/" + newName);
                educationList.get(0).setEducationCertificateName(educationFile0FileName);
            }
        }
        if (educationFile1 != null) {
            String newName = FileTypeCheck.fileTypeCheck(educationFile1FileName);
            String filePath = savePath + "/" + newName;
            File file = new File(filePath);
            boolean result = this.copyFile(educationFile1, file);
            if (result) {
                educationList.get(1).setEducationCertificate(SysConstants.EXPERT_FILE_PATH_ROOT + expertInfoEntity.getUser_id() + "/" + newName);
                educationList.get(1).setEducationCertificateName(educationFile1FileName);
            }
        }
        if (educationFile2 != null) {
            String newName = FileTypeCheck.fileTypeCheck(educationFile2FileName);
            String filePath = savePath + "/" + newName;
            File file = new File(filePath);
            boolean result = this.copyFile(educationFile2, file);
            if (result) {
                educationList.get(2).setEducationCertificate(SysConstants.EXPERT_FILE_PATH_ROOT + expertInfoEntity.getUser_id() + "/" + newName);
                educationList.get(2).setEducationCertificateName(educationFile2FileName);
            }
        }
        if (educationFile3 != null) {
            String newName = FileTypeCheck.fileTypeCheck(educationFile3FileName);
            String filePath = savePath + "/" + newName;
            File file = new File(filePath);
            boolean result = this.copyFile(educationFile3, file);
            if (result) {
                educationList.get(3).setEducationCertificate(SysConstants.EXPERT_FILE_PATH_ROOT + expertInfoEntity.getUser_id() + "/" + newName);
                educationList.get(3).setEducationCertificateName(educationFile3FileName);
            }
        }
        if (educationFile4 != null) {
            String newName = FileTypeCheck.fileTypeCheck(educationFile4FileName);
            String filePath = savePath + "/" + newName;
            File file = new File(filePath);
            boolean result = this.copyFile(educationFile4, file);
            if (result) {
                educationList.get(4).setEducationCertificate(SysConstants.EXPERT_FILE_PATH_ROOT + expertInfoEntity.getUser_id() + "/" + newName);
                educationList.get(4).setEducationCertificateName(educationFile4FileName);
            }
        }
        if (educationFile5 != null) {
            String newName = FileTypeCheck.fileTypeCheck(educationFile5FileName);
            String filePath = savePath + "/" + newName;
            File file = new File(filePath);
            boolean result = this.copyFile(educationFile5, file);
            if (result) {
                educationList.get(5).setEducationCertificate(SysConstants.EXPERT_FILE_PATH_ROOT + expertInfoEntity.getUser_id() + "/" + newName);
                educationList.get(5).setEducationCertificateName(educationFile5FileName);
            }
        }
    }

    /**
     * 学位
     *
     * @param expertInfoEntity
     * @param educationList
     * @param academicFile1
     * @param academicFile1FileName
     * @param academicFile2
     * @param academicFile2FileName
     * @param academicFile3
     * @param academicFile3FileName
     * @param academicFile4
     * @param academicFile4FileName
     * @param academicFile5
     * @param academicFile5FileName
     */
    public void uploadExpertAcademic(ExpertInfoEntity expertInfoEntity, List<EducationInfo> educationList, File academicFile0, String academicFile0FileName, File academicFile1, String academicFile1FileName, File academicFile2, String academicFile2FileName, File academicFile3, String academicFile3FileName, File academicFile4, String academicFile4FileName, File academicFile5, String academicFile5FileName) {
        String rootPath = ServletActionContext.getServletContext().getRealPath("");
        String savePath = rootPath + "/" + SysConstants.EXPERT_FILE_PATH_ROOT + expertInfoEntity.getUser_id();

        if (academicFile0 != null) {
            String newName = FileTypeCheck.fileTypeCheck(academicFile0FileName);
            String filePath = savePath + "/" + newName;
            File file = new File(filePath);
            boolean result = this.copyFile(academicFile0, file);
            if (result) {
                educationList.get(0).setAcademicCertificate(SysConstants.EXPERT_FILE_PATH_ROOT + expertInfoEntity.getUser_id() + "/" + newName);
                educationList.get(0).setAcademicCertificateName(academicFile0FileName);
            }
        }

        if (academicFile1 != null) {
            String newName = FileTypeCheck.fileTypeCheck(academicFile1FileName);
            String filePath = savePath + "/" + newName;
            File file = new File(filePath);
            boolean result = this.copyFile(academicFile1, file);
            if (result) {
                educationList.get(1).setAcademicCertificate(SysConstants.EXPERT_FILE_PATH_ROOT + expertInfoEntity.getUser_id() + "/" + newName);
                educationList.get(1).setAcademicCertificateName(academicFile1FileName);
            }
        }

        if (academicFile2 != null) {
//            String newName = CommUtil.getKey() + academicFile2FileName.substring(academicFile2FileName.lastIndexOf("."));
            String newName = FileTypeCheck.fileTypeCheck(academicFile2FileName);
            String filePath = savePath + "/" + newName;
            File file = new File(filePath);
            boolean result = this.copyFile(academicFile2, file);
            if (result) {
                educationList.get(2).setAcademicCertificate(SysConstants.EXPERT_FILE_PATH_ROOT + expertInfoEntity.getUser_id() + "/" + newName);
                educationList.get(2).setAcademicCertificateName(academicFile2FileName);
            }
        }

        if (academicFile3 != null) {
//            String newName = CommUtil.getKey() + academicFile3FileName.substring(academicFile3FileName.lastIndexOf("."));
            String newName = FileTypeCheck.fileTypeCheck(academicFile3FileName);
            String filePath = savePath + "/" + newName;
            File file = new File(filePath);
            boolean result = this.copyFile(academicFile3, file);
            if (result) {
                educationList.get(3).setAcademicCertificate(SysConstants.EXPERT_FILE_PATH_ROOT + expertInfoEntity.getUser_id() + "/" + newName);
                educationList.get(3).setAcademicCertificateName(academicFile3FileName);
            }
        }

        if (academicFile4 != null) {
//            String newName = CommUtil.getKey() + academicFile4FileName.substring(academicFile4FileName.lastIndexOf("."));
            String newName = FileTypeCheck.fileTypeCheck(academicFile4FileName);
            String filePath = savePath + "/" + newName;
            File file = new File(filePath);
            boolean result = this.copyFile(academicFile4, file);
            if (result) {
                educationList.get(4).setAcademicCertificate(SysConstants.EXPERT_FILE_PATH_ROOT + expertInfoEntity.getUser_id() + "/" + newName);
                educationList.get(4).setAcademicCertificateName(academicFile4FileName);
            }
        }

        if (academicFile5 != null) {
//            String newName = CommUtil.getKey() + academicFile5FileName.substring(academicFile5FileName.lastIndexOf("."));
            String newName = FileTypeCheck.fileTypeCheck(academicFile5FileName);
            String filePath = savePath + "/" + newName;
            File file = new File(filePath);
            boolean result = this.copyFile(academicFile5, file);
            if (result) {
                educationList.get(5).setAcademicCertificate(SysConstants.EXPERT_FILE_PATH_ROOT + expertInfoEntity.getUser_id() + "/" + newName);
                educationList.get(5).setAcademicCertificateName(academicFile5FileName);
            }
        }

    }

    /**
     * 职称信息
     *
     * @param expertInfoEntity
     * @param titleInfos
     * @param picture0
     * @param picture0FileName
     * @param picture1
     * @param picture1FileName
     * @param picture2
     * @param picture2FileName
     * @param picture3
     * @param picture3FileName
     * @param picture4
     * @param picture4FileName
     * @param picture5
     * @param picture5FileName
     */
    public void uploadTitleInfo(ExpertInfoEntity expertInfoEntity, List<TitleInfo> titleInfos, File picture0, String picture0FileName, File picture1, String picture1FileName, File picture2, String picture2FileName, File picture3, String picture3FileName, File picture4, String picture4FileName, File picture5, String picture5FileName) {
        String rootPath = ServletActionContext.getServletContext().getRealPath("");
        String savePath = rootPath + "/" + SysConstants.EXPERT_FILE_PATH_ROOT + expertInfoEntity.getUser_id();
        if (picture0 != null) {
//            String newName = CommUtil.getKey() + picture0FileName.substring(picture0FileName.lastIndexOf("."));
            String newName = FileTypeCheck.fileTypeCheck(picture0FileName);
            String filePath = savePath + "/" + newName;
            File file = new File(filePath);
            boolean result = this.copyFile(picture0, file);
            if (result) {
                titleInfos.get(0).setPicture(SysConstants.EXPERT_FILE_PATH_ROOT + expertInfoEntity.getUser_id() + "/" + newName);
                titleInfos.get(0).setPictureName(picture0FileName);
            }
        }
        if (picture1 != null) {
//            String newName = CommUtil.getKey() + picture1FileName.substring(picture1FileName.lastIndexOf("."));
            String newName = FileTypeCheck.fileTypeCheck(picture1FileName);
            String filePath = savePath + "/" + newName;
            File file = new File(filePath);
            boolean result = this.copyFile(picture1, file);
            if (result) {
                titleInfos.get(1).setPicture(SysConstants.EXPERT_FILE_PATH_ROOT + expertInfoEntity.getUser_id() + "/" + newName);
                titleInfos.get(1).setPictureName(picture1FileName);
            }
        }
        if (picture2 != null) {
//            String newName = CommUtil.getKey() + picture2FileName.substring(picture2FileName.lastIndexOf("."));
            String newName = FileTypeCheck.fileTypeCheck(picture2FileName);
            String filePath = savePath + "/" + newName;
            File file = new File(filePath);
            boolean result = this.copyFile(picture2, file);
            if (result) {
                titleInfos.get(2).setPicture(SysConstants.EXPERT_FILE_PATH_ROOT + expertInfoEntity.getUser_id() + "/" + newName);
                titleInfos.get(2).setPictureName(picture2FileName);
            }
        }
        if (picture3 != null) {
//            String newName = CommUtil.getKey() + picture3FileName.substring(picture3FileName.lastIndexOf("."));
            String newName = FileTypeCheck.fileTypeCheck(picture3FileName);
            String filePath = savePath + "/" + newName;
            File file = new File(filePath);
            boolean result = this.copyFile(picture3, file);
            if (result) {
                titleInfos.get(3).setPicture(SysConstants.EXPERT_FILE_PATH_ROOT + expertInfoEntity.getUser_id() + "/" + newName);
                titleInfos.get(3).setPictureName(picture3FileName);
            }
        }
        if (picture4 != null) {
//            String newName = CommUtil.getKey() + picture4FileName.substring(picture4FileName.lastIndexOf("."));
            String newName = FileTypeCheck.fileTypeCheck(picture4FileName);
            String filePath = savePath + "/" + newName;
            File file = new File(filePath);
            boolean result = this.copyFile(picture4, file);
            if (result) {
                titleInfos.get(4).setPicture(SysConstants.EXPERT_FILE_PATH_ROOT + expertInfoEntity.getUser_id() + "/" + newName);
                titleInfos.get(4).setPictureName(picture4FileName);
            }
        }
        if (picture5 != null) {
//            String newName = CommUtil.getKey() + picture5FileName.substring(picture5FileName.lastIndexOf("."));
            String newName = FileTypeCheck.fileTypeCheck(picture5FileName);
            String filePath = savePath + "/" + newName;
            File file = new File(filePath);
            boolean result = this.copyFile(picture5, file);
            if (result) {
                titleInfos.get(5).setPicture(SysConstants.EXPERT_FILE_PATH_ROOT + expertInfoEntity.getUser_id() + "/" + newName);
                titleInfos.get(5).setPictureName(picture5FileName);
            }
        }

    }

    /**
     * 专家学历
     *
     * @param expertInfoEntity
     * @param educationList
     */
    @Transactional
    public void addExpertEducation(ExpertInfoEntity expertInfoEntity, List<EducationInfo> educationList, String roleId) {
        for (int i = 0; i < educationList.size(); i++) {
            EducationInfo educationInfo = educationList.get(i);
            if (educationInfo.getId() == null || educationInfo.getId().equals("")) {
                String graduateSchool = educationInfo.getGraduateSchool();
                if (!StringUtils.isEmpty(graduateSchool)) {
                    educationInfo.setId(CommUtil.getKey());
                    educationInfo.setExpertId(expertInfoEntity.getUser_id());
                    educationInfo.setDeleteFlag(0);
                    if (!StringUtils.isEmpty(roleId)) {
                        try {
                            infoSaveEducationInfoRecord(educationInfo, roleId);
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                    educationInfoMapper.insertEducationInfo(educationInfo);
                }

            } else {
                String graduateSchool = educationInfo.getGraduateSchool();
                if (StringUtils.isEmpty(graduateSchool)) {
                    if (!StringUtils.isEmpty(roleId)) {
                        EducationInfo info = educationInfoMapper.getEducationInfo(educationInfo);
                        deleteEducationInfoRecord(info, roleId);
                    }
                    educationInfoMapper.deleteEducationInfo(educationInfo);
                } else {
                    EducationInfo key = educationInfoMapper.getEducationInfo(educationInfo);
                    if (!StringUtils.isEmpty(roleId)) {
                        infoRecord(key, educationInfo, roleId);
                    }
                    educationInfoMapper.updateEducationInfo(educationInfo);
                }
            }
        }
    }

    private void infoSaveEducationInfoRecord(EducationInfo after, String role_id) {
        addExpertUpdateRecord(after.getExpertId(), "毕业学校", null, com.hzw.ssm.util.string.StringUtils.toStringNull(after.getGraduateSchool()), role_id, after.getExpertId(), "学历信息", 0);
        addExpertUpdateRecord(after.getExpertId(), "毕业所学专业", null, com.hzw.ssm.util.string.StringUtils.toStringNull(after.getMajor()), role_id, after.getExpertId(), "学历信息", 0);
        // 学历
        Map<String, String> eduMap = new HashMap<String, String>();
        eduMap.put("-1", "请选择");
        eduMap.put("1", "初中");
        eduMap.put("2", "高中");
        eduMap.put("3", "大专");
        eduMap.put("4", "本科");
        eduMap.put("5", "硕士");
        eduMap.put("6", "博士");
        addExpertUpdateRecord(after.getExpertId(), "毕业学历", null, com.hzw.ssm.util.string.StringUtils.toStringNull(eduMap.get(after.getEducation())), role_id, after.getExpertId(), "学历信息", 0);
        if (com.hzw.ssm.util.string.StringUtils.isNotEmpty(after.getAcademicDegree())) {
            Map<String, String> degreeMap = new HashMap<String, String>();
            degreeMap.put("-1", "请选择");
            degreeMap.put("1", "学士");
            degreeMap.put("2", "硕士");
            degreeMap.put("3", "博士");
            addExpertUpdateRecord(after.getExpertId(), "毕业学位", null, com.hzw.ssm.util.string.StringUtils.toStringNull(degreeMap.get(after.getAcademicDegree())), role_id, after.getExpertId(), "学历信息", 0);
        }

        String afterStyle = "<a style=\"color:blue\" onclick=\"javascript:picPreview(530,450,'/GZZJK/" + com.hzw.ssm.util.string.StringUtils.toStringNull(after.getEducationCertificate()) + "')\">" + com.hzw.ssm.util.string.StringUtils.toStringNull(after.getEducationCertificateName()) + "</a>";
        addExpertUpdateRecord(after.getExpertId(), "毕业学历证书", null, com.hzw.ssm.util.string.StringUtils.toStringNull(afterStyle), role_id, after.getExpertId(), "学历信息", 0);
        if (!com.hzw.ssm.util.string.StringUtils.isEmpty(after.getAcademicCertificateName()) && !com.hzw.ssm.util.string.StringUtils.isEmpty(after.getAcademicCertificate())) {
            String afterAcademicCertificate = "<a style=\"color:blue\" onclick=\"javascript:picPreview(530,450,'/GZZJK/" + com.hzw.ssm.util.string.StringUtils.toStringNull(after.getAcademicCertificate()) + "')\">" + com.hzw.ssm.util.string.StringUtils.toStringNull(after.getAcademicCertificateName()) + "</a>";
            addExpertUpdateRecord(after.getExpertId(), "毕业学位证书", null, afterAcademicCertificate, role_id, after.getExpertId(), "学历信息", 0);
        }
    }

    private void deleteEducationInfoRecord(EducationInfo info, String roleId) {
        String graduateSchool = info.getGraduateSchool();
        String content_after = "删除了【" + graduateSchool + "】的学历信息";
        addExpertUpdateRecord(info.getExpertId(), "学历信息", null, content_after, roleId, info.getExpertId(), "学历信息", 0);
    }

    /**
     * 专家职称
     *
     * @param expertInfoEntity
     * @param titleInfos
     */
    @Transactional
    public void addExpertTitle(ExpertInfoEntity expertInfoEntity, List<TitleInfo> titleInfos, String roleId) {
        for (int i = 0; i < titleInfos.size(); i++) {
            TitleInfo titleInfo = titleInfos.get(i);
            if (titleInfo.getId() == null || titleInfo.getId().equals("")) {
                String titleId = titleInfo.getTitleId();
                if (!StringUtils.isEmpty(titleId) && !"-1".equals(titleId)) {
                    titleInfo.setId(CommUtil.getKey());
                    titleInfo.setExpertId(expertInfoEntity.getUser_id());
                    titleInfo.setDeleteFlag("0");
                    titleInfo.setCreateTime(new Date());

                    List<String> pictures = new ArrayList<>();
                    pictures.add(titleInfo.getPicture());
                    String json = JSON.toJSONString(pictures);
                    titleInfo.setPicture(json);
                    if (!StringUtils.isEmpty(roleId)) {
                        try {
                            // 新增记录
                            infoSaveTitleInfoRecord(titleInfo, roleId);
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                    titleInfoMapper.insertTitleInfo(titleInfo);
                }
            } else {
                String titleId = titleInfo.getTitleId();
                // 删除
                if ("-1".equals(titleId)) {
                    if (!StringUtils.isEmpty(roleId)) {
                        TitleInfo info = titleInfoMapper.getTitleInfo(titleInfo);
                        deleteTitleInfoRecord(info, roleId);
                    }
                    titleInfoMapper.deleteTitleInfo(titleInfo);
                } else {
                    if (!StringUtils.isEmpty(titleInfo.getPicture())) {
                        List<String> pictures = new ArrayList<>();
                        pictures.add(titleInfo.getPicture());
                        String json = JSON.toJSONString(pictures);
                        titleInfo.setPicture(json);
                    }
                    TitleInfo info = titleInfoMapper.getTitleInfo(titleInfo);
                    if (!StringUtils.isEmpty(roleId)) {
                        titleInfoRecord(info, titleInfo, roleId);
                    }
                    titleInfoMapper.updateTitleInfo(titleInfo);
                }
            }
        }

    }

    private void infoSaveTitleInfoRecord(TitleInfo after, String role_id) {
        HashMap<String, String> techMap = new HashMap<>();
        List<Dictionary> dictionaries = dictionaryMapper.queryTitle();
        for (Dictionary dictionary : dictionaries) {
            techMap.put(dictionary.getDataCode(), dictionary.getDataValue());
        }

        if (com.hzw.ssm.util.string.StringUtils.isNotEmpty(after.getTitleId())) {// 职称名称id
            addExpertUpdateRecord(after.getExpertId(), "职称名称", null, com.hzw.ssm.util.string.StringUtils.toStringNull(techMap.get(after.getTitleId())), role_id, after.getExpertId(), "职称信息", 0);
        }
        if (com.hzw.ssm.util.string.StringUtils.isNotEmpty(after.getGetTime())) {// 获得时间
            addExpertUpdateRecord(after.getExpertId(), "职称获得时间", null, com.hzw.ssm.util.string.StringUtils.toStringNull(after.getGetTime()), role_id, after.getExpertId(), "职称信息", 0);
        }
        if (com.hzw.ssm.util.string.StringUtils.isNotEmpty(after.getPicture())) {// 职称图片
            List<String> beforePic = JSON.parseArray(after.getPicture(), String.class);
            String afterStyle = "<a style=\"color:blue\" onclick=\"javascript:picPreview(530,450,'/GZZJK/" + com.hzw.ssm.util.string.StringUtils.toStringNull(beforePic.get(0)) + "')\">" + com.hzw.ssm.util.string.StringUtils.toStringNull(after.getPictureName()) + "</a>";
            addExpertUpdateRecord(after.getExpertId(), "职称图片", null, afterStyle, role_id, after.getExpertId(), "职称信息", 0);
        }
    }

    private void deleteTitleInfoRecord(TitleInfo info, String roleId) {

        HashMap<String, String> techMap = new HashMap<>();
        List<Dictionary> dictionaries = dictionaryMapper.queryTitle();
        for (Dictionary dictionary : dictionaries) {
            techMap.put(dictionary.getDataCode(), dictionary.getDataValue());
        }

        String titleName = techMap.get(info.getTitleId());
        String content_after = "删除了【" + titleName + "】的职称信息";
        addExpertUpdateRecord(info.getExpertId(), "职称信息", null, content_after, roleId, info.getExpertId(), "职称信息", 0);
    }


    /**
     * 文件复制处理
     *
     * @param src
     * @param dst
     * @return
     */
    public synchronized static boolean copyFile(File src, File dst) {
        if (src == null || dst == null) {
            return false;
        }
        int BUFFER_SIZE = 16 * 1024;
        InputStream ins = null;
        OutputStream os = null;
        try {
            ins = new BufferedInputStream(new FileInputStream(src), BUFFER_SIZE);
            os = new BufferedOutputStream(new FileOutputStream(dst), BUFFER_SIZE);
            byte[] buffer = new byte[BUFFER_SIZE];
            int len = 0;
            while ((len = ins.read(buffer)) > 0) {
                os.write(buffer, 0, len);
            }
            os.flush();
        } catch (FileNotFoundException e) {
            e.printStackTrace();
            return false;
        } catch (IOException e) {
            e.printStackTrace();
            return false;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        } finally {
            try {
                if (ins != null) {
                    ins.close();
                }
                if (os != null) {
                    os.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return true;
    }

    private void titleInfoRecord(TitleInfo before, TitleInfo after, String role_id) {
        String expertId = before.getExpertId();
        if (before != null && after != null) {

            HashMap<String, String> techMap = new HashMap<>();
            List<com.hzw.ssm.applets.entity.Dictionary> dictionaries = dictionaryMapper.queryTitle();
            for (Dictionary dictionary : dictionaries) {
                techMap.put(dictionary.getDataCode(), dictionary.getDataValue());
            }

            if (com.hzw.ssm.util.string.StringUtils.isNotEmpty(after.getTitleId()) && !com.hzw.ssm.util.string.StringUtils.toStringNull(after.getTitleId()).equalsIgnoreCase(com.hzw.ssm.util.string.StringUtils.toStringNull(before.getTitleId()))) {// 职称名称id
                addExpertUpdateRecord(expertId, "职称名称", com.hzw.ssm.util.string.StringUtils.toStringNull(techMap.get(before.getTitleId())), com.hzw.ssm.util.string.StringUtils.toStringNull(techMap.get(after.getTitleId())), role_id, expertId, "职称信息", 0);
            }
            if (com.hzw.ssm.util.string.StringUtils.isNotEmpty(after.getGetTime()) && !com.hzw.ssm.util.string.StringUtils.toStringNull(after.getGetTime()).equalsIgnoreCase(com.hzw.ssm.util.string.StringUtils.toStringNull(before.getGetTime()))) {// 获得时间
                addExpertUpdateRecord(expertId, "职称获得时间", com.hzw.ssm.util.string.StringUtils.toStringNull(before.getGetTime()), com.hzw.ssm.util.string.StringUtils.toStringNull(after.getGetTime()), role_id, expertId, "职称信息", 0);
            }
            if (com.hzw.ssm.util.string.StringUtils.isNotEmpty(after.getPicture()) && !com.hzw.ssm.util.string.StringUtils.toStringNull(after.getPicture()).equalsIgnoreCase(com.hzw.ssm.util.string.StringUtils.toStringNull(before.getPicture()))) {// 职称图片

                String befoStyle = "无";
                if (!StringUtils.isEmpty(before.getPictureName())) {
                    List<String> beforePic = JSON.parseArray(before.getPicture(), String.class);
                    befoStyle = "<a style=\"color:blue\" onclick=\"javascript:picPreview(530,450,'/GZZJK/" + com.hzw.ssm.util.string.StringUtils.toStringNull(beforePic.get(0)) + "')\">" + com.hzw.ssm.util.string.StringUtils.toStringNull(before.getPictureName()) + "</a>";
                }
                List<String> beforePic = JSON.parseArray(after.getPicture(), String.class);
                String afterStyle = "<a style=\"color:blue\" onclick=\"javascript:picPreview(530,450,'/GZZJK/" + com.hzw.ssm.util.string.StringUtils.toStringNull(beforePic.get(0)) + "')\">" + com.hzw.ssm.util.string.StringUtils.toStringNull(after.getPictureName()) + "</a>";
                addExpertUpdateRecord(expertId, "职称图片", befoStyle, afterStyle, role_id, expertId, "职称信息", 0);
            }
        }
    }

    private void infoRecord(EducationInfo before, EducationInfo after, String role_id) {


        if (before != null && after != null) {
            String expertId = before.getExpertId();

            if (com.hzw.ssm.util.string.StringUtils.isNotEmpty(after.getGraduateSchool()) && !com.hzw.ssm.util.string.StringUtils.toStringNull(before.getGraduateSchool()).equalsIgnoreCase(com.hzw.ssm.util.string.StringUtils.toStringNull(after.getGraduateSchool()))) {
                //毕业学校

                addExpertUpdateRecord(expertId, "毕业学校", com.hzw.ssm.util.string.StringUtils.toStringNull(before.getGraduateSchool()), com.hzw.ssm.util.string.StringUtils.toStringNull(after.getGraduateSchool()), role_id, expertId, "学历信息", 0);

            }
            if (com.hzw.ssm.util.string.StringUtils.isNotEmpty(after.getMajor()) && !com.hzw.ssm.util.string.StringUtils.toStringNull(before.getMajor()).equalsIgnoreCase(com.hzw.ssm.util.string.StringUtils.toStringNull(after.getMajor()))) {
                // 所学专业
                addExpertUpdateRecord(expertId, "毕业所学专业", com.hzw.ssm.util.string.StringUtils.toStringNull(before.getMajor()), com.hzw.ssm.util.string.StringUtils.toStringNull(after.getMajor()), role_id, expertId, "学历信息", 0);

            }
            if (com.hzw.ssm.util.string.StringUtils.isNotEmpty(after.getEducation()) && !com.hzw.ssm.util.string.StringUtils.toStringNull(before.getEducation()).equalsIgnoreCase(com.hzw.ssm.util.string.StringUtils.toStringNull(after.getEducation()))) {
                // 学历
                Map<String, String> eduMap = new HashMap<String, String>();
                eduMap.put("-1", "请选择");
                eduMap.put("1", "初中");
                eduMap.put("2", "高中");
                eduMap.put("3", "大专");
                eduMap.put("4", "本科");
                eduMap.put("5", "硕士");
                eduMap.put("6", "博士");
                addExpertUpdateRecord(expertId, "毕业学历", com.hzw.ssm.util.string.StringUtils.toStringNull(eduMap.get(before.getEducation())), com.hzw.ssm.util.string.StringUtils.toStringNull(eduMap.get(after.getEducation())), role_id, expertId, "学历信息", 0);
            }
            if (com.hzw.ssm.util.string.StringUtils.isNotEmpty(after.getAcademicDegree()) && !com.hzw.ssm.util.string.StringUtils.toStringNull(before.getAcademicDegree()).equalsIgnoreCase(com.hzw.ssm.util.string.StringUtils.toStringNull(after.getAcademicDegree()))) {
                // 学位
                Map<String, String> degreeMap = new HashMap<String, String>();
                degreeMap.put("-1", "请选择");
                degreeMap.put("1", "学士");
                degreeMap.put("2", "硕士");
                degreeMap.put("3", "博士");
                addExpertUpdateRecord(expertId, "毕业学位", com.hzw.ssm.util.string.StringUtils.toStringNull(degreeMap.get(before.getAcademicDegree())), com.hzw.ssm.util.string.StringUtils.toStringNull(degreeMap.get(after.getAcademicDegree())), role_id, expertId, "学历信息", 0);
            }
            if (com.hzw.ssm.util.string.StringUtils.isNotEmpty(after.getEducationCertificate()) && !com.hzw.ssm.util.string.StringUtils.toStringNull(before.getEducationCertificate()).equalsIgnoreCase(com.hzw.ssm.util.string.StringUtils.toStringNull(after.getEducationCertificate()))) {
                // 学历证书

                String befoStyle = "无";
                if (!StringUtils.isEmpty(before.getEducationCertificateName())) {
                    befoStyle = "<a style=\"color:blue\" onclick=\"javascript:picPreview(530,450,'/GZZJK/" + com.hzw.ssm.util.string.StringUtils.toStringNull(before.getEducationCertificate()) + "')\">" + com.hzw.ssm.util.string.StringUtils.toStringNull(before.getEducationCertificateName()) + "</a>";
                }
                String afterStyle = "<a style=\"color:blue\" onclick=\"javascript:picPreview(530,450,'/GZZJK/" + com.hzw.ssm.util.string.StringUtils.toStringNull(after.getEducationCertificate()) + "')\">" + com.hzw.ssm.util.string.StringUtils.toStringNull(after.getEducationCertificateName()) + "</a>";
                addExpertUpdateRecord(expertId, "毕业学历证书", com.hzw.ssm.util.string.StringUtils.toStringNull(befoStyle), com.hzw.ssm.util.string.StringUtils.toStringNull(afterStyle), role_id, expertId, "学历信息", 0);
            }

            if (com.hzw.ssm.util.string.StringUtils.isNotEmpty(after.getAcademicCertificate()) && !com.hzw.ssm.util.string.StringUtils.toStringNull(before.getAcademicCertificate()).equalsIgnoreCase(com.hzw.ssm.util.string.StringUtils.toStringNull(after.getAcademicCertificate()))) {
                // 学位证书
                String befoStyle = "无";
                if (!StringUtils.isEmpty(before.getAcademicCertificateName())) {
                    befoStyle = "<a style=\"color:blue\" onclick=\"javascript:picPreview(530,450,'/GZZJK/" + com.hzw.ssm.util.string.StringUtils.toStringNull(before.getAcademicCertificate()) + "')\">" + com.hzw.ssm.util.string.StringUtils.toStringNull(before.getAcademicCertificateName()) + "</a>";
                }
                if (!StringUtils.isEmpty(after.getAcademicCertificateName())) {
                    String afterStyle = "<a style=\"color:blue\" onclick=\"javascript:picPreview(530,450,'/GZZJK/" + com.hzw.ssm.util.string.StringUtils.toStringNull(after.getAcademicCertificate()) + "')\">" + com.hzw.ssm.util.string.StringUtils.toStringNull(after.getAcademicCertificateName()) + "</a>";
                    addExpertUpdateRecord(expertId, "毕业学位证书", com.hzw.ssm.util.string.StringUtils.toStringNull(befoStyle), com.hzw.ssm.util.string.StringUtils.toStringNull(afterStyle), role_id, expertId, "学历信息", 0);
                }
            }

        }
    }

    public void addExpertUpdateRecord(String user_id, String item, String content_before, String content_after, String modify_role, String modify_user, String modify_reason, int batch_number) {
        ExpertUpdateRecordEntity record = new ExpertUpdateRecordEntity();
        record.setId(CommUtil.getKey());
        record.setUser_id(user_id);
        record.setItem(item);
        record.setContent_before(content_before);
        record.setContent_after(content_after);
        record.setModify_role(modify_role);
        record.setModify_user(modify_user);
        record.setModify_reason(modify_reason);
        record.setBatch_number(batch_number);
        record.setModify_time(new Date());
        record.setDelete_flag(0);
        expertInfoMapper.addExpertUpdateRecord(record);
    }


}
