package com.hzw.sunflower.entity.condition;

import com.hzw.sunflower.common.BaseCondition;
import io.swagger.annotations.ApiModel;

/**
 * 项目委托关系人表 查询条件
 *
 * <AUTHOR>
 * @version 1.0.0 2021-04-07
 */
@ApiModel(description = "项目委托关系人表 查询条件")
public class ProjectEntrustUserCondition extends BaseCondition {
    /**
     * 版本号
     */
    private static final long serialVersionUID = -2497394814090594848L;
}