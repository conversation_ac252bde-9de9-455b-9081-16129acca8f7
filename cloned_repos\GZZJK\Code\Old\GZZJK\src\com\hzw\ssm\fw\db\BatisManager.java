package com.hzw.ssm.fw.db;

import java.io.Reader;
import java.sql.Connection;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.apache.ibatis.io.Resources;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.apache.ibatis.session.SqlSessionFactoryBuilder;

/**
 * 
 * <AUTHOR>
 *
 */
public class BatisManager {

	private static final Log log = LogFactory.getLog(BatisManager.class);
	
	private static SqlSessionFactory sqlMapper = null;
	
	/** 未关闭的数据库连接的个数 */
    private static int iNotCloseConnnectionNum = 0;
    
    /**
     * 初始化
     * @throws Exception
     */
    public static void init() throws Exception{
    	Reader reader = Resources.getResourceAsReader("mybatis-config.xml");
    	sqlMapper = new SqlSessionFactoryBuilder().build(reader);
    	reader.close();
    }
    
    /**
     * 获取SqlSession
     * @param autoCommit 是否自动获取数据库链接
     * @return
     * @throws Exception
     */
    public static SqlSession getSqlSession(boolean autoCommit) throws Exception{
    	SqlSession sqlSession = sqlMapper.openSession(autoCommit);
    	return sqlSession;
    }
    
    /**
     * 返回数据库连接对象
     * 
     * @param configId 数据源编号
     * @return 返回
     */
    public static Connection openConnection(boolean autoCommit){
        Connection conn=null;
        try {
            SqlSession sqlSession = sqlMapper.openSession(autoCommit);
            conn = sqlSession.getConnection();
            
            // 连接个数递增
            iNotCloseConnnectionNum++;
            log.info("获取数据库链接成功！");
        }catch(Exception e) {
            e.printStackTrace();
            log.info("获取数据库链接失败！");
            log.info(e);
        }
        return conn;
    }
    
    /**
     * 关闭数据库连接
     * @param conn
     */
    public static void closeConnection(Connection conn){
        try {
            if (conn != null) {
                conn.close();
                conn = null;
                // 连接个数递减
                iNotCloseConnnectionNum--;
                log.info("关闭数据库链接成功！");
            }
        } catch (Exception e) {
        	 e.printStackTrace();
             log.info("关闭数据库链接失败！");
             log.info(e);
        }
    }

	public static int getiNotCloseConnnectionNum() {
		return iNotCloseConnnectionNum;
	}
    
}
