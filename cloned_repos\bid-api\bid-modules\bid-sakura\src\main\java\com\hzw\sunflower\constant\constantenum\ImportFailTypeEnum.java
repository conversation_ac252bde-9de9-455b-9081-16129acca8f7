package com.hzw.sunflower.constant.constantenum;

/**
 * <AUTHOR>
 * @date ：Created in 2022/07/27 9:23
 * @description：导入失败类型
 * @version: 1.0
 */
public enum ImportFailTypeEnum {

    FAIL_FORMAT_ERROR(1, "格式错误"),
    FAIL_REPEAT(2, "数据重复");

    private Integer type;
    private String desc;

    ImportFailTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
