package com.hzw.sunflower.constant;

import com.hzw.sunflower.constant.constantenum.NoticeProgressEnum;

import javax.swing.plaf.PanelUI;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2021/8/06
 * @description： 招标公告操作与进度的对应关系
 * @version: 1.0
 */
public class NoticeProgressConstants {

    /**
     * 招标公告可删除
     */
    public static final List<Integer> CAN_DELETE = List.of(NoticeProgressEnum.DRAFTING.getValue());

    /**
     * 招标公告可任意编辑
    */
    public static final List<Integer> CAN_EDIT = List.of(NoticeProgressEnum.DRAFTING.getValue(),NoticeProgressEnum.WITHDRAWAL.getValue(),NoticeProgressEnum.RETURN.getValue());

    /**
     * 招标公告仅可查看
     */
    public static final List<Integer> ONLY_READ = List.of(NoticeProgressEnum.UNDERREVIEW.getValue());

    /**
     * 招标公告可撤回
     */
    public static final List<Integer> CAN_WITHDRAW = List.of(NoticeProgressEnum.UNDERREVIEW.getValue(),NoticeProgressEnum.REVIEW.getValue(),NoticeProgressEnum.WITHDRAWUNDERREVIEW.getValue());

    /**
     * 招标公告可发布
     */
    public static final List<Integer> CAN_PUBLISH = List.of(NoticeProgressEnum.REVIEW.getValue());

    /**
     * 招标公告已发布，仅可上传发布截图
     */
    public static final List<Integer> CAN_UPDATE_PICS = List.of(NoticeProgressEnum.RELEASE.getValue());
}
