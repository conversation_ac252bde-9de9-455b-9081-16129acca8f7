package com.hzw.ssm.fw.base;

import java.io.Serializable;
import java.util.Date;

import com.hzw.ssm.fw.util.PageEntity;

public class BaseEntity extends PageEntity implements Serializable {

	/** 对象序列化时，该对象的唯一标识 */
	private static final long serialVersionUID = 1646077306147353671L;

	/** 逻辑删除标志 */
	private Integer delete_flag; // 逻辑删除标志

	/** 新增人员 */
	private String create_id; // 创建人编号

	/** 新增日期 */
	private Date create_time; // 创建时间

	/** 更新人员 */
	private String modify_id; // 修改人编号

	/** 更新日期 */
	private Date modify_time; // 修改时间

	/** 信息主键值 */
	private String key = "";

	public Integer getDelete_flag() {
		return delete_flag;
	}

	public void setDelete_flag(Integer deleteFlag) {
		delete_flag = deleteFlag;
	}

	public String getCreate_id() {
		return create_id;
	}

	public void setCreate_id(String createId) {
		create_id = createId;
	}

	public Date getCreate_time() {
		return create_time;
	}

	public void setCreate_time(Date createTime) {
		create_time = createTime;
	}

	public String getModify_id() {
		return modify_id;
	}

	public void setModify_id(String modifyId) {
		modify_id = modifyId;
	}

	public Date getModify_time() {
		return modify_time;
	}

	public void setModify_time(Date modifyTime) {
		modify_time = modifyTime;
	}

	public String getKey() {
		return key;
	}

	public void setKey(String key) {
		this.key = key;
	}

}
