package com.hzw.sunflower.controller.response;

import com.hzw.sunflower.entity.BondApplyRefund;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@ApiModel(description = "异常退款申请待处理列表")
@Data
public class BondRefundMoneyVO{

    @ApiModelProperty("代理服务费")
    private BigDecimal agencyFee;

    @ApiModelProperty("退还给供应商的保证金")
    private BigDecimal refundSuppliserMoney;

}
