package com.hzw.sunflower.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hzw.sunflower.common.BaseController;
import com.hzw.sunflower.util.BeanListUtil;
import com.hzw.sunflower.common.Paging;
import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.constant.MessageConstants;
import com.hzw.sunflower.dto.SealFileLogDTO;
import com.hzw.sunflower.controller.request.SealFileLogREQ;
import com.hzw.sunflower.entity.SealFileLog;
import com.hzw.sunflower.entity.condition.SealFileLogCondition;
import com.hzw.sunflower.service.SealFileLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
* SealFileLogController
*/
@Api(tags = "SealFileLog服务")
@RestController
@RequestMapping("/sealFileLog")
public class SealFileLogController extends BaseController {
    @Autowired
    private SealFileLogService sealFileLogService;

    @ApiOperation(value = "根据条件分页查询SealFileLog列表")
    @ApiImplicitParam(name = "sealFileLogREQ", value = "用户表 查询条件", required = true, dataType = "SealFileLogREQ", paramType = "body")
    @PostMapping("/list")
    public Paging<SealFileLog> list(@RequestBody SealFileLogREQ sealFileLogREQ) {
        SealFileLogCondition condition = BeanListUtil.convert(sealFileLogREQ,SealFileLogCondition.class);
        IPage<SealFileLog> page = sealFileLogService.findInfoByCondition(condition);
            return Paging.buildPaging(page);
    }


    @ApiOperation(value = "根据主键ID查询SealFileLog信息")
    @ApiImplicitParam(name = "id", value = "主键ID", required = true)
    @GetMapping(value = "/get/{id}")
    public Result<SealFileLog> get(@PathVariable Long id) {
        if (id == null) {
            return Result.failed(MessageConstants.GET_NULL);
        }
        SealFileLog sealFileLog = sealFileLogService.getInfoById(id);
        return Result.ok(sealFileLog);
    }

    @ApiOperation(value = "新增SealFileLog信息")
    @ApiImplicitParam(name = "sealFileLogDTO", value = "SealFileLog表 ", required = true, dataType = "SealFileLogDTO", paramType = "body")
    @PostMapping("/add")
    public Result<Boolean> add(@RequestBody SealFileLogDTO sealFileLogDTO) {
        Boolean bool = sealFileLogService.addInfo(sealFileLogDTO);
        return Result.okOrFailed(bool);
    }

    @ApiOperation(value = "修改SealFileLog信息")
    @ApiImplicitParam(name = "sealFileLogDTO", value = "SealFileLog表 ", required = true, dataType = "SealFileLogDTO", paramType = "body")
    @PutMapping(value = "/update")
    public Result<Boolean> update(@RequestBody SealFileLogDTO sealFileLogDTO) {
        Long id = sealFileLogDTO.getId();
        if (id == null) {
            return Result.failed(MessageConstants.UPDATE_NULL);
        }
        Boolean bool = sealFileLogService.updateInfo(sealFileLogDTO);
        return Result.okOrFailed(bool);
    }

    @ApiOperation(value = "根据主键ID删除SealFileLog表 ")
    @ApiImplicitParam(name = "id", value = "主键ID", required = true)
    @DeleteMapping(value = "/delete/{id}")
    public Result<Boolean> delete(@PathVariable Long id) {
        if (id == null) {
            return Result.failed(MessageConstants.DELETE_NULL);
        }
        Boolean bool = sealFileLogService.deleteById(id);
        return Result.okOrFailed(bool);
    }

    @ApiOperation(value = "根据主键ID列表批量删除SealFileLog表 ")
    @ApiImplicitParam(name = "idList", value = "主键ID列表", required = true, allowMultiple = true, paramType = "body")
    @DeleteMapping("/deleteList")
    public Result<Boolean> deleteList(@RequestBody List<Long> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return Result.failed(MessageConstants.DELETE_NULL);
        }
        Boolean bool = sealFileLogService.deleteByIds(idList);
        return Result.okOrFailed(bool);
    }
}