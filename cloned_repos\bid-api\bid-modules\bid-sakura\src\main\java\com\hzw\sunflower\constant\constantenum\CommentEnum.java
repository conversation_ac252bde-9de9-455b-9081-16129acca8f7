package com.hzw.sunflower.constant.constantenum;

/**
 * <AUTHOR>
 * @date ：Created in 2022/07/27 9:23
 * @description：申请类型
 * @version: 1.0
 */
public enum CommentEnum {
    //保证金转代理费/保证金转代理费-补差额/保证金转代理费-退余额
    ALL_REFUND(1, "全额转"),
    ALL_REFUND_BKE(2, "补差额"),
    BF_REFUND(3, "退余额");

    private Integer type;
    private String desc;

    CommentEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
