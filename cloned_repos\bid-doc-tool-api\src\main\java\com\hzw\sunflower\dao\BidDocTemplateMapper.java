package com.hzw.sunflower.dao;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hzw.sunflower.controller.request.BidDocTemplateListREQ;
import com.hzw.sunflower.controller.responese.BidDocTemplateVO;
import com.hzw.sunflower.entity.BidDocTemplate;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 招标文件编制模板表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-26
 */
@Mapper
public interface BidDocTemplateMapper extends BaseMapper<BidDocTemplate> {


    /**
     * 查询招标文件编制模版列表
     * @param page
     * @param condition
     * @return
     */
    IPage<BidDocTemplateVO>  selectTemplateList(IPage<BidDocTemplateVO> page, @Param("condition") BidDocTemplateListREQ condition);

}
