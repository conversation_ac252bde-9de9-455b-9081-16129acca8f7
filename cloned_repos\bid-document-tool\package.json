{"name": "bid-document-tool", "productName": "投标文件制作工具", "version": "1.0.0", "description": "My Electron application description", "main": ".vite/build/main.js", "scripts": {"build": "vite build", "start": "electron-forge start", "package": "electron-forge package", "make": "electron-forge make", "publish": "electron-forge publish", "lint": "echo \"No linting configured\""}, "devDependencies": {"@electron-forge/cli": "^7.4.0", "@electron-forge/maker-deb": "^7.4.0", "@electron-forge/maker-rpm": "^7.4.0", "@electron-forge/maker-squirrel": "^7.4.0", "@electron-forge/maker-zip": "^7.4.0", "@electron-forge/plugin-auto-unpack-natives": "^7.4.0", "@electron-forge/plugin-fuses": "^7.4.0", "@electron-forge/plugin-vite": "^7.4.0", "@electron/fuses": "^1.8.0", "@vitejs/plugin-vue": "^5.0.5", "electron": "31.0.2", "vite": "^5.0.12"}, "keywords": [], "author": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@tinymce/tinymce-vue": "^6.0.1", "@vue-office/docx": "^1.6.2", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.12", "adm-zip": "^0.5.14", "docx": "^8.5.0", "electron-html-to": "^2.6.1", "electron-squirrel-startup": "^1.0.1", "element-plus": "^2.7.6", "html-to-docx": "^1.8.0", "jszip": "^3.10.1", "mammoth": "^1.8.0", "puppeteer": "^22.12.1", "puppeteer-core": "^22.12.1", "tinymce": "^7.2.0", "vue": "^3.4.30", "vue-demi": "^0.14.6"}, "config": {"forge": {"plugins": [{"name": "@electron-forge/plugin-vite", "config": {"build": [{"entry": "src/main.js", "config": "vite.main.config.mjs"}, {"entry": "src/preload.js", "config": "vite.preload.config.mjs"}], "renderer": [{"name": "main_window", "config": "vite.renderer.config.mjs"}]}}], "packagerConfig": {"asar": true, "icon": "src/assets/logo.ico"}, "makers": [{"name": "@electron-forge/maker-squirrel", "config": {"setupIcon": "src/assets/logo.ico"}}, {"name": "@electron-forge/maker-zip", "platforms": ["darwin"]}]}}}