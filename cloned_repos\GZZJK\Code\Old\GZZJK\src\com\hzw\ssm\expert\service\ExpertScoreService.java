package com.hzw.ssm.expert.service;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.hzw.ssm.expert.dao.ExpertScoreMapper;
import com.hzw.ssm.expert.entity.ExpertScoreEntity;
import com.hzw.ssm.fw.base.BaseService;

@Service
public class ExpertScoreService extends BaseService {
	@Autowired
	private ExpertScoreMapper expertScoreMapper;
	
	/**
	 * 新增专家扣分信息详情
	 * 函数功能描述：TODO
	 * @param expertScore
	 */
	@Transactional
	public void addExpertScoreRecords(ExpertScoreEntity expertScore) {
		expertScoreMapper.addExpertScoreRecords(expertScore);
	}
	
	/**
	 * 定时器更新专家信息
	 * 函数功能描述：TODO
	 * @param expertScore
	 */
	@Transactional
	public void updateExpertScoreRecords(ExpertScoreEntity expertScore) {
		expertScoreMapper.updateExpertScoreRecords(expertScore);
	}
	
	
	/**
	 * 查询专家出现的次数
	 * @return
	 */
	public List<ExpertScoreEntity> queryExpertAppear(String userId){
		return expertScoreMapper.queryExpertAppear(userId);
	}
	
}
