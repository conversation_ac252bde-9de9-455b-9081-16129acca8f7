package com.hzw.sunflower.entity.condition;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date ：Created in 2022/8/3 11:30
 * @description：异常保证金关联搜索条件
 * @modified By：`
 * @version: 1.0
 */
@ApiModel(description = "异常保证金关联流水精准搜索条件")
@Data
public class ExceptionBondRelationCondition {

    @ApiModelProperty(value = "流水对应企业名称")
    private String companyName;

    @ApiModelProperty(value = "金额")
    private String amount;

}
