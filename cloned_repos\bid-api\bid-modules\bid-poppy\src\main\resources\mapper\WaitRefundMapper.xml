<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!-- 项目表 (t_project) -->
<mapper namespace="com.hzw.sunflower.dao.WaitRefundMapper">

	<select id="getTenderFeesApply" resultType="com.hzw.sunflower.controller.project.response.WaitListVo">
	select
		t.updated_time as time,
		t2.created_time AS refundTime,
		'tenderFeesApply' AS type,
		p.purchase_name AS projectName,
		p.purchase_number AS projectNum,
		t.project_id,
		min( pbs.id ) section_id,
		group_concat( DISTINCT pbs.package_number ) AS package_number,
		pbs.bid_round,
		p.base_project_id,
		p.re_tender,
		p.package_segment_status AS packageSegmentStatus,
		p.bid_type_name AS bidType,
		1 as countNum
	FROM
		t_pay_all t
		LEFT JOIN (
		SELECT
			*
		FROM
			t_calibration_process_record
		WHERE
			id IN ( SELECT max( c.id ) AS id FROM t_calibration_process_record c WHERE c.business_code = 'BSF_REFUND' GROUP BY c.business_id )
		) t2 ON t.id = t2.business_id
		LEFT JOIN t_project p ON t.project_id = p.id
        LEFT JOIN t_project_bid_section pbs ON p.id = pbs.project_id
        AND find_in_set( pbs.id, t.section_ids )
        LEFT JOIN t_project_entrust_user tpe ON pbs.project_id = tpe.project_id
		LEFT JOIN r_user_department rud on tpe.user_id = rud.user_id  and tpe.department_id = rud.department_id
	WHERE
		t.is_delete = 0
		AND t2.is_delete = 0
		AND t2.operation = '退回'
		and p.is_delete = 0
		AND pbs.is_delete = 0
		AND tpe.is_delete = 0
		AND tpe.type = 1
		AND rud.is_delete = 0
		<if test="condition.keyWords != null and condition.keyWords != ''">
			and (
			p.purchase_number like concat('%',#{condition.keyWords},'%')
			or
			p.purchase_name like concat('%',#{condition.keyWords},'%')
			)
		</if>
		${dataScope}
		GROUP BY
		t.updated_time,
		t2.created_time,
		p.purchase_name,
		p.purchase_number,
		t.project_id,
		pbs.bid_round,
		p.base_project_id,
		p.re_tender,
		p.package_segment_status,
		p.bid_type_name
	</select>


	<select id="getTenderBid" resultType="com.hzw.sunflower.controller.project.response.WaitListVo">
		select
		t.submit_time as time,
		t2.created_time AS refundTime,
		'tenderBid' AS type,
		p.purchase_name AS projectName,
		p.purchase_number AS projectNum,
		t.project_id,
		min( pbs.id ) section_id,
		group_concat( DISTINCT pbs.package_number ) AS package_number,
		pbs.bid_round,
		p.base_project_id,
		p.re_tender,
		p.package_segment_status AS packageSegmentStatus,
		p.bid_type_name AS bidType,
		1 as countNum
		FROM
		t_project_bid_notice t
		LEFT JOIN t_section_notice sn ON t.id = sn.notice_id
		LEFT JOIN (SELECT * FROM t_approval_opinion o
		WHERE id IN ( SELECT max( o.id ) AS id FROM t_approval_opinion o GROUP BY o.notice_id )
		) t2 ON t.id = t2.notice_id
		LEFT JOIN t_project p ON t.project_id = p.id
		LEFT JOIN t_project_bid_section pbs ON p.id = pbs.project_id
		AND pbs.id = sn.section_id
		LEFT JOIN t_project_entrust_user tpe ON pbs.project_id = tpe.project_id
		LEFT JOIN r_user_department rud on tpe.user_id = rud.user_id  and tpe.department_id = rud.department_id
		WHERE
		t.is_delete = 0
		AND sn.is_delete = 0
		and t2.is_delete = 0
		AND (t.notice_progress = 6 OR t2.approval_type = 5)
		and p.is_delete = 0
		AND pbs.is_delete = 0
		AND tpe.is_delete = 0
		AND tpe.type = 1
		AND rud.is_delete = 0
		<if test="condition.keyWords != null and condition.keyWords != ''">
			and (
			p.purchase_number like concat('%',#{condition.keyWords},'%')
			or
			p.purchase_name like concat('%',#{condition.keyWords},'%')
			)
		</if>
		${dataScope}
		GROUP BY
		t.submit_time,
		t2.created_time,
		p.purchase_name,
		p.purchase_number,
		t.project_id,
		pbs.bid_round,
		p.base_project_id,
		p.re_tender,
		p.package_segment_status,
		p.bid_type_name
    </select>


	<select id="getTenderDoc" resultType="com.hzw.sunflower.controller.project.response.WaitListVo">
	select
		t.submit_time as time,
		t2.created_time AS refundTime,
		'tenderDoc' AS type,
		p.purchase_name AS projectName,
		p.purchase_number AS projectNum,
		t.project_id,
		min( pbs.id ) section_id,
		group_concat( DISTINCT pbs.package_number ) AS package_number,
		pbs.bid_round,
		p.base_project_id,
		p.re_tender,
		p.package_segment_status AS packageSegmentStatus,
		p.bid_type_name AS bidType,
		1 as countNum
	FROM
		t_project_bid_doc t
		LEFT JOIN t_project_bid_doc_relation sn ON t.id = sn.doc_id
		LEFT JOIN (SELECT * FROM t_approval_opinion o
		    WHERE id IN ( SELECT max( o.id ) AS id FROM t_approval_opinion o GROUP BY o.doc_id )
	    ) t2 ON t.id = t2.doc_id
		LEFT JOIN t_project p ON t.project_id = p.id
        LEFT JOIN t_project_bid_section pbs ON p.id = pbs.project_id
        AND pbs.id = sn.section_id
        LEFT JOIN t_project_entrust_user tpe ON pbs.project_id = tpe.project_id
		LEFT JOIN r_user_department rud on tpe.user_id = rud.user_id  and tpe.department_id = rud.department_id
	WHERE
		t.is_delete = 0
		AND sn.is_delete = 0
		and t2.is_delete = 0
	    AND (t.notice_progress = 6 OR t2.approval_type = 5)
		and p.is_delete = 0
		AND pbs.is_delete = 0
		AND tpe.is_delete = 0
		AND tpe.type = 1
		AND rud.is_delete = 0
		<if test="condition.keyWords != null and condition.keyWords != ''">
			and (
			p.purchase_number like concat('%',#{condition.keyWords},'%')
			or
			p.purchase_name like concat('%',#{condition.keyWords},'%')
			)
		</if>
		${dataScope}
		GROUP BY
		t.submit_time,
		t2.created_time,
		p.purchase_name,
		p.purchase_number,
		t.project_id,
		pbs.bid_round,
		p.base_project_id,
		p.re_tender,
		p.package_segment_status,
		p.bid_type_name
	</select>


	<select id="getSupplementBid" resultType="com.hzw.sunflower.controller.project.response.WaitListVo">
	select
		t.submit_time as time,
		t2.created_time AS refundTime,
		'supplementBid' AS type,
		p.purchase_name AS projectName,
		p.purchase_number AS projectNum,
		t.project_id,
		min( pbs.id ) section_id,
		group_concat( DISTINCT pbs.package_number ) AS package_number,
		pbs.bid_round,
		p.base_project_id,
		p.re_tender,
		p.package_segment_status AS packageSegmentStatus,
		p.bid_type_name AS bidType,
		1 as countNum
	FROM
		t_supplement_bulletin t
		LEFT JOIN (
			SELECT * FROM t_calibration_process_record
			WHERE id IN ( SELECT max( c.id ) AS id FROM t_calibration_process_record c WHERE c.business_code = 'supplement' GROUP BY c.business_id )
		) t2 ON t.id = t2.business_id
		LEFT JOIN t_project p ON t.project_id = p.id
        LEFT JOIN t_project_bid_section pbs ON p.id = pbs.project_id
        AND find_in_set(pbs.id,t.related_bid_section)
        LEFT JOIN t_project_entrust_user tpe ON pbs.project_id = tpe.project_id
		LEFT JOIN r_user_department rud on tpe.user_id = rud.user_id  and tpe.department_id = rud.department_id
	WHERE
		t.is_delete = 0
        AND t2.is_delete = 0
        AND (t.`status` = 6 OR t2.operation = '退回')
		and p.is_delete = 0
		AND pbs.is_delete = 0
		AND tpe.is_delete = 0
		AND tpe.type = 1
		AND rud.is_delete = 0
		<if test="condition.keyWords != null and condition.keyWords != ''">
			and (
			p.purchase_number like concat('%',#{condition.keyWords},'%')
			or
			p.purchase_name like concat('%',#{condition.keyWords},'%')
			)
		</if>
		${dataScope}
		GROUP BY
		t.submit_time,
		t2.created_time,
		p.purchase_name,
		p.purchase_number,
		t.project_id,
		pbs.bid_round,
		p.base_project_id,
		p.re_tender,
		p.package_segment_status,
		p.bid_type_name
	</select>


	<select id="getClarifyBid" resultType="com.hzw.sunflower.controller.project.response.WaitListVo">
	select
		t.submit_time as time,
		t2.created_time AS refundTime,
		'clarifyBid' AS type,
		p.purchase_name AS projectName,
		p.purchase_number AS projectNum,
		t.project_id,
		min( pbs.id ) section_id,
		group_concat( DISTINCT pbs.package_number ) AS package_number,
		pbs.bid_round,
		p.base_project_id,
		p.re_tender,
		p.package_segment_status AS packageSegmentStatus,
		p.bid_type_name AS bidType,
		1 as countNum
	FROM
		 t_e_question_reply t
        LEFT JOIN (SELECT * FROM t_calibration_process_record
            WHERE id IN ( SELECT max( c.id ) AS id FROM t_calibration_process_record c WHERE c.business_code = 'clarifyReply' GROUP BY c.business_id )
        ) t2 ON t.id = t2.business_id
		LEFT JOIN t_project p ON t.project_id = p.id
        LEFT JOIN t_project_bid_section pbs ON p.id = pbs.project_id
        AND find_in_set(pbs.id,t.section_ids)
        LEFT JOIN t_project_entrust_user tpe ON pbs.project_id = tpe.project_id
		LEFT JOIN r_user_department rud on tpe.user_id = rud.user_id  and tpe.department_id = rud.department_id
	WHERE
		t.is_delete = 0
        AND t2.is_delete = 0
        AND (t.`status` = 3 OR t2.operation = '退回')
		and p.is_delete = 0
		AND pbs.is_delete = 0
		AND tpe.is_delete = 0
		AND tpe.type = 1
		AND rud.is_delete = 0
		<if test="condition.keyWords != null and condition.keyWords != ''">
			and (
			p.purchase_number like concat('%',#{condition.keyWords},'%')
			or
			p.purchase_name like concat('%',#{condition.keyWords},'%')
			)
		</if>
		${dataScope}
		GROUP BY
		t.submit_time,
		t2.created_time,
		p.purchase_name,
		p.purchase_number,
		t.project_id,
		pbs.bid_round,
		p.base_project_id,
		p.re_tender,
		p.package_segment_status,
		p.bid_type_name
	</select>



	<select id="getObjection" resultType="com.hzw.sunflower.controller.project.response.WaitListVo">
	select
		te.submit_time as time,
		t2.created_time AS refundTime,
		'objection' AS type,
		p.purchase_name AS projectName,
		p.purchase_number AS projectNum,
		t.project_id,
		min( pbs.id ) section_id,
		group_concat( DISTINCT pbs.package_number ) AS package_number,
		pbs.bid_round,
		p.base_project_id,
		p.re_tender,
		p.package_segment_status AS packageSegmentStatus,
		p.bid_type_name AS bidType,
		1 as countNum
	FROM
		t_e_propose_objection t
		LEFT JOIN ( SELECT * FROM
			( SELECT RANK() OVEr ( PARTITION BY clarify_objection_id ORDER BY order_num DESC ) num, a.* FROM t_e_clarify_reply a ) a
			WHERE a.num = 1
		) te ON te.clarify_objection_id = t.id
		LEFT JOIN (SELECT * FROM t_calibration_process_record
			WHERE id IN ( SELECT max( c.id ) AS id FROM t_calibration_process_record c WHERE c.business_code = 'objection' GROUP BY c.business_id )
		) t2 ON t.id = t2.business_id
		LEFT JOIN t_project p ON t.project_id = p.id
        LEFT JOIN t_project_bid_section pbs ON p.id = pbs.project_id
        AND find_in_set(pbs.id,t.section_ids)
        LEFT JOIN t_project_entrust_user tpe ON pbs.project_id = tpe.project_id
		LEFT JOIN r_user_department rud on tpe.user_id = rud.user_id  and tpe.department_id = rud.department_id
	WHERE
		t.is_delete = 0
		AND t2.is_delete = 0
		AND (te.`status` = 9 OR t2.operation = '退回')
		and p.is_delete = 0
		AND pbs.is_delete = 0
		AND tpe.is_delete = 0
		AND tpe.type = 1
		AND rud.is_delete = 0
		<if test="condition.keyWords != null and condition.keyWords != ''">
			and (
			p.purchase_number like concat('%',#{condition.keyWords},'%')
			or
			p.purchase_name like concat('%',#{condition.keyWords},'%')
			)
		</if>
		${dataScope}
		GROUP BY
		te.submit_time,
		t2.created_time,
		p.purchase_name,
		p.purchase_number,
		t.project_id,
		pbs.bid_round,
		p.base_project_id,
		p.re_tender,
		p.package_segment_status,
		p.bid_type_name
	</select>


	<select id="getWinBid" resultType="com.hzw.sunflower.controller.project.response.WaitListVo">
	select
		t.submit_time as time,
		t.updated_time AS refundTime,
		'winBid' AS type,
		p.purchase_name AS projectName,
		p.purchase_number AS projectNum,
		t.project_id,
		min( pbs.id ) section_id,
		group_concat( DISTINCT pbs.package_number ) AS package_number,
		pbs.bid_round,
		p.base_project_id,
		p.re_tender,
		p.package_segment_status AS packageSegmentStatus,
		p.bid_type_name AS bidType,
		1 as countNum
	FROM
		t_bid_win t
		LEFT JOIN t_project p ON t.project_id = p.id
        LEFT JOIN t_project_bid_section pbs ON p.id = pbs.project_id
        AND find_in_set(pbs.id,t.section_id)
        LEFT JOIN t_project_entrust_user tpe ON pbs.project_id = tpe.project_id
		LEFT JOIN r_user_department rud on tpe.user_id = rud.user_id  and tpe.department_id = rud.department_id
	WHERE
		t.is_delete = 0
		AND t.`status` = 3
		AND t.bid_round = 2
		and p.is_delete = 0
		AND pbs.is_delete = 0
		AND tpe.is_delete = 0
		AND tpe.type = 1
		AND rud.is_delete = 0
		<if test="condition.keyWords != null and condition.keyWords != ''">
			and (
			p.purchase_number like concat('%',#{condition.keyWords},'%')
			or
			p.purchase_name like concat('%',#{condition.keyWords},'%')
			)
		</if>
		${dataScope}
		GROUP BY
		t.submit_time,
		t.updated_time,
		p.purchase_name,
		p.purchase_number,
		t.project_id,
		pbs.bid_round,
		p.base_project_id,
		p.re_tender,
		p.package_segment_status,
		p.bid_type_name
	</select>

	<select id="getwinBidZgys" resultType="com.hzw.sunflower.controller.project.response.WaitListVo">
	select
		t.submit_time as time,
		t.updated_time AS refundTime,
		'winBid_zgys' AS type,
		p.purchase_name AS projectName,
		p.purchase_number AS projectNum,
		t.project_id,
		min( pbs.id ) section_id,
		group_concat( DISTINCT pbs.package_number ) AS package_number,
		pbs.bid_round,
		p.base_project_id,
		p.re_tender,
		p.package_segment_status AS packageSegmentStatus,
		p.bid_type_name AS bidType,
		1 as countNum
	FROM
		t_bid_win t
		LEFT JOIN t_project p ON t.project_id = p.id
        LEFT JOIN t_project_bid_section pbs ON p.id = pbs.project_id
        AND find_in_set(pbs.id,t.section_id)
        LEFT JOIN t_project_entrust_user tpe ON pbs.project_id = tpe.project_id
		LEFT JOIN r_user_department rud on tpe.user_id = rud.user_id  and tpe.department_id = rud.department_id
	WHERE
		t.is_delete = 0
		AND t.`status` = 3
		AND t.bid_round = 1
		and p.is_delete = 0
		AND pbs.is_delete = 0
		AND tpe.is_delete = 0
		AND tpe.type = 1
		AND rud.is_delete = 0
		<if test="condition.keyWords != null and condition.keyWords != ''">
			and (
			p.purchase_number like concat('%',#{condition.keyWords},'%')
			or
			p.purchase_name like concat('%',#{condition.keyWords},'%')
			)
		</if>
		${dataScope}
		GROUP BY
		t.submit_time,
		t.updated_time,
		p.purchase_name,
		p.purchase_number,
		t.project_id,
		pbs.bid_round,
		p.base_project_id,
		p.re_tender,
		p.package_segment_status,
		p.bid_type_name
	</select>


	<select id="getWinningBidCandidate" resultType="com.hzw.sunflower.controller.project.response.WaitListVo">
	select
		t.submit_time as time,
		t2.created_time AS refundTime,
		'winningBidCandidate' AS type,
		p.purchase_name AS projectName,
		p.purchase_number AS projectNum,
		t.project_id,
		min( pbs.id ) section_id,
		group_concat( DISTINCT pbs.package_number ) AS package_number,
		pbs.bid_round,
		p.base_project_id,
		p.re_tender,
		p.package_segment_status AS packageSegmentStatus,
		p.bid_type_name AS bidType,
		1 as countNum
	FROM
		t_bid_win_bulletin t
		LEFT JOIN (SELECT * FROM t_calibration_process_record
			WHERE id IN ( SELECT max( c.id ) AS id FROM t_calibration_process_record c WHERE c.business_code = 'winBULLETIN' GROUP BY c.business_id )
		) t2 ON t.id = t2.business_id
		LEFT JOIN t_project p ON t.project_id = p.id
        LEFT JOIN t_project_bid_section pbs ON p.id = pbs.project_id
        AND find_in_set(pbs.id,t.related_bid_section)
        LEFT JOIN t_project_entrust_user tpe ON pbs.project_id = tpe.project_id
		LEFT JOIN r_user_department rud on tpe.user_id = rud.user_id  and tpe.department_id = rud.department_id
	WHERE
		t.is_delete = 0
		AND t.bid_win_type = 0
		AND t2.is_delete = 0
		AND (t.`status` = 6 OR t2.operation = '退回')
		and p.is_delete = 0
		AND pbs.is_delete = 0
		AND tpe.is_delete = 0
		AND tpe.type = 1
		AND rud.is_delete = 0
		<if test="condition.keyWords != null and condition.keyWords != ''">
			and (
			p.purchase_number like concat('%',#{condition.keyWords},'%')
			or
			p.purchase_name like concat('%',#{condition.keyWords},'%')
			)
		</if>
		${dataScope}
		GROUP BY
		t.submit_time,
		t2.created_time,
		p.purchase_name,
		p.purchase_number,
		t.project_id,
		pbs.bid_round,
		p.base_project_id,
		p.re_tender,
		p.package_segment_status,
		p.bid_type_name
	</select>


	<select id="getWinCandidateBid" resultType="com.hzw.sunflower.controller.project.response.WaitListVo">
	select
		t.submit_time as time,
		t2.created_time AS refundTime,
		'winCandidateBid' AS type,
		p.purchase_name AS projectName,
		p.purchase_number AS projectNum,
		t.project_id,
		min( pbs.id ) section_id,
		group_concat( DISTINCT pbs.package_number ) AS package_number,
		pbs.bid_round,
		p.base_project_id,
		p.re_tender,
		p.package_segment_status AS packageSegmentStatus,
		p.bid_type_name AS bidType,
		1 as countNum
	FROM
		t_bid_win_bulletin t
		LEFT JOIN (SELECT * FROM t_calibration_process_record
			WHERE id IN ( SELECT max( c.id ) AS id FROM t_calibration_process_record c WHERE c.business_code = 'winBULLETIN' GROUP BY c.business_id )
		) t2 ON t.id = t2.business_id
		LEFT JOIN t_project p ON t.project_id = p.id
        LEFT JOIN t_project_bid_section pbs ON p.id = pbs.project_id
        AND find_in_set(pbs.id,t.related_bid_section)
        LEFT JOIN t_project_entrust_user tpe ON pbs.project_id = tpe.project_id
		LEFT JOIN r_user_department rud on tpe.user_id = rud.user_id  and tpe.department_id = rud.department_id
	WHERE
		t.is_delete = 0
		AND t.bid_win_type = 1
		AND t2.is_delete = 0
		AND (t.`status` = 6 OR t2.operation = '退回')
		and p.is_delete = 0
		AND pbs.is_delete = 0
		AND tpe.is_delete = 0
		AND tpe.type = 1
		AND rud.is_delete = 0
		<if test="condition.keyWords != null and condition.keyWords != ''">
			and (
			p.purchase_number like concat('%',#{condition.keyWords},'%')
			or
			p.purchase_name like concat('%',#{condition.keyWords},'%')
			)
		</if>
		${dataScope}
		GROUP BY
		t.submit_time,
		t2.created_time,
		p.purchase_name,
		p.purchase_number,
		t.project_id,
		pbs.bid_round,
		p.base_project_id,
		p.re_tender,
		p.package_segment_status,
		p.bid_type_name
	</select>


	<select id="getWinNotice" resultType="com.hzw.sunflower.controller.project.response.WaitListVo">
	select
		t.submit_time as time,
		t2.created_time AS refundTime,
		'winNotice' AS type,
		p.purchase_name AS projectName,
		p.purchase_number AS projectNum,
		t.project_id,
		min( pbs.id ) section_id,
		group_concat( DISTINCT pbs.package_number ) AS package_number,
		pbs.bid_round,
		p.base_project_id,
		p.re_tender,
		p.package_segment_status AS packageSegmentStatus,
		p.bid_type_name AS bidType,
		1 as countNum
	FROM
		t_bid_win_notice t
		LEFT JOIN (
		SELECT
			distinct
			*
		FROM
			t_calibration_process_record
		WHERE
			id IN ( SELECT max( c.id ) AS id FROM t_calibration_process_record c WHERE c.business_code = 'winNotice' GROUP BY c.business_id )
		) t2 ON t.id = t2.business_id
		LEFT JOIN t_project p ON t.project_id = p.id
        LEFT JOIN t_project_bid_section pbs ON p.id = pbs.project_id
        AND find_in_set(pbs.id,t.section_id)
        LEFT JOIN t_project_entrust_user tpe ON pbs.project_id = tpe.project_id
		LEFT JOIN r_user_department rud on tpe.user_id = rud.user_id  and tpe.department_id = rud.department_id
	WHERE
		t.is_delete = 0
		AND t.bid_round = 2
		AND t.`status` != 5
		AND (t.`status` = 6 or t2.operation = '退回')
		and p.is_delete = 0
		AND pbs.is_delete = 0
		AND tpe.is_delete = 0
		AND tpe.type = 1
		AND rud.is_delete = 0
		<if test="condition.keyWords != null and condition.keyWords != ''">
			and (
			p.purchase_number like concat('%',#{condition.keyWords},'%')
			or
			p.purchase_name like concat('%',#{condition.keyWords},'%')
			)
		</if>
		${dataScope}
		GROUP BY
		t.submit_time,
		t2.created_time,
		p.purchase_name,
		p.purchase_number,
		t.project_id,
		pbs.bid_round,
		p.base_project_id,
		p.re_tender,
		p.package_segment_status,
		p.bid_type_name
	</select>


	<select id="getReWinNotice" resultType="com.hzw.sunflower.controller.project.response.WaitListVo">
	select
		t.submit_time as time,
		t2.created_time AS refundTime,
		'reWinNotice' AS type,
		p.purchase_name AS projectName,
		p.purchase_number AS projectNum,
		t.project_id,
		min( pbs.id ) section_id,
		group_concat( DISTINCT pbs.package_number ) AS package_number,
		pbs.bid_round,
		p.base_project_id,
		p.re_tender,
		p.package_segment_status AS packageSegmentStatus,
		p.bid_type_name AS bidType,
		1 as countNum
	FROM
		t_bid_win_notice t
		LEFT JOIN (
		SELECT
			*
		FROM
			t_calibration_process_record
		WHERE
			id IN ( SELECT max( c.id ) AS id FROM t_calibration_process_record c WHERE c.business_code = 'winNotice' GROUP BY c.business_id )
		) t2 ON t.id = t2.business_id
		LEFT JOIN t_project p ON t.project_id = p.id
        LEFT JOIN t_project_bid_section pbs ON p.id = pbs.project_id
        AND find_in_set(pbs.id,t.section_id)
        LEFT JOIN t_project_entrust_user tpe ON pbs.project_id = tpe.project_id
		LEFT JOIN r_user_department rud on tpe.user_id = rud.user_id  and tpe.department_id = rud.department_id
	WHERE
		t.is_delete = 0
		AND t2.is_delete = 0
		AND t2.operation = '退回'
		AND t.is_reapply = 1
		AND t.bid_round = 2
		and p.is_delete = 0
		AND pbs.is_delete = 0
		AND tpe.is_delete = 0
		AND tpe.type = 1
		AND rud.is_delete = 0
		<if test="condition.keyWords != null and condition.keyWords != ''">
			and (
			p.purchase_number like concat('%',#{condition.keyWords},'%')
			or
			p.purchase_name like concat('%',#{condition.keyWords},'%')
			)
		</if>
		${dataScope}
		GROUP BY
		t.submit_time,
		t2.created_time,
		p.purchase_name,
		p.purchase_number,
		t.project_id,
		pbs.bid_round,
		p.base_project_id,
		p.re_tender,
		p.package_segment_status,
		p.bid_type_name
	</select>


	<select id="getBidWinNoticePre" resultType="com.hzw.sunflower.controller.project.response.WaitListVo">
	select
		t.submit_time as time,
		t2.created_time AS refundTime,
		'bidWinNoticePre' AS type,
		p.purchase_name AS projectName,
		p.purchase_number AS projectNum,
		t.project_id,
		min( pbs.id ) section_id,
		group_concat( DISTINCT pbs.package_number ) AS package_number,
		pbs.bid_round,
		p.base_project_id,
		p.re_tender,
		p.package_segment_status AS packageSegmentStatus,
		p.bid_type_name AS bidType,
		1 as countNum
	FROM
		t_bid_win_notice t
		LEFT JOIN (
		SELECT
			*
		FROM
			t_calibration_process_record
		WHERE
			id IN ( SELECT max( c.id ) AS id FROM t_calibration_process_record c WHERE c.business_code = 'winNotice' GROUP BY c.business_id )
		) t2 ON t.id = t2.business_id
		LEFT JOIN t_project p ON t.project_id = p.id
        LEFT JOIN t_project_bid_section pbs ON p.id = pbs.project_id
        AND find_in_set(pbs.id,t.section_id)
        LEFT JOIN t_project_entrust_user tpe ON pbs.project_id = tpe.project_id
		LEFT JOIN r_user_department rud on tpe.user_id = rud.user_id  and tpe.department_id = rud.department_id
	WHERE
		t.is_delete = 0
		AND t.bid_round = 1
		AND t.`status` != 5
		AND t.bid_round = 1
		AND (t.`status` = 6 or t2.operation = '退回')
		and p.is_delete = 0
		AND pbs.is_delete = 0
		AND tpe.is_delete = 0
		AND tpe.type = 1
		AND rud.is_delete = 0
		<if test="condition.keyWords != null and condition.keyWords != ''">
			and (
			p.purchase_number like concat('%',#{condition.keyWords},'%')
			or
			p.purchase_name like concat('%',#{condition.keyWords},'%')
			)
		</if>
		${dataScope}
		GROUP BY
		t.submit_time,
		t2.created_time,
		p.purchase_name,
		p.purchase_number,
		t.project_id,
		pbs.bid_round,
		p.base_project_id,
		p.re_tender,
		p.package_segment_status,
		p.bid_type_name
	</select>


	<select id="getReBidWinNoticePre" resultType="com.hzw.sunflower.controller.project.response.WaitListVo">
	select
		t.submit_time as time,
		t2.created_time AS refundTime,
		'reBidWinNoticePre' AS type,
		p.purchase_name AS projectName,
		p.purchase_number AS projectNum,
		t.project_id,
		min( pbs.id ) section_id,
		group_concat( DISTINCT pbs.package_number ) AS package_number,
		pbs.bid_round,
		p.base_project_id,
		p.re_tender,
		p.package_segment_status AS packageSegmentStatus,
		p.bid_type_name AS bidType,
		1 as countNum
	FROM
		t_bid_win_notice t
		LEFT JOIN (
		SELECT
			*
		FROM
			t_calibration_process_record
		WHERE
			id IN ( SELECT max( c.id ) AS id FROM t_calibration_process_record c WHERE c.business_code = 'winNotice' GROUP BY c.business_id )
		) t2 ON t.id = t2.business_id
		LEFT JOIN t_project p ON t.project_id = p.id
        LEFT JOIN t_project_bid_section pbs ON p.id = pbs.project_id
        AND find_in_set(pbs.id,t.section_id)
        LEFT JOIN t_project_entrust_user tpe ON pbs.project_id = tpe.project_id
		LEFT JOIN r_user_department rud on tpe.user_id = rud.user_id  and tpe.department_id = rud.department_id
	WHERE
		t.is_delete = 0
		AND t2.operation = '退回'
		AND t.is_reapply = 1
		AND t.bid_round = 1
		and p.is_delete = 0
		AND pbs.is_delete = 0
		AND tpe.is_delete = 0
		AND tpe.type = 1
		AND rud.is_delete = 0
		<if test="condition.keyWords != null and condition.keyWords != ''">
			and (
			p.purchase_number like concat('%',#{condition.keyWords},'%')
			or
			p.purchase_name like concat('%',#{condition.keyWords},'%')
			)
		</if>
		${dataScope}
		GROUP BY
		t.submit_time,
		t2.created_time,
		p.purchase_name,
		p.purchase_number,
		t.project_id,
		pbs.bid_round,
		p.base_project_id,
		p.re_tender,
		p.package_segment_status,
		p.bid_type_name
	</select>

	<select id="getAbnormalNotice" resultType="com.hzw.sunflower.controller.project.response.WaitListVo">
	select
		t.submit_time as time,
		t.updated_time AS refundTime,
		'abnormalNotice' AS type,
		p.purchase_name AS projectName,
		p.purchase_number AS projectNum,
		t.project_id,
		min( pbs.id ) section_id,
		group_concat( DISTINCT pbs.package_number ) AS package_number,
		pbs.bid_round,
		p.base_project_id,
		p.re_tender,
		p.package_segment_status AS packageSegmentStatus,
		p.bid_type_name AS bidType,
		1 as countNum
	FROM
		t_except_notice t
		LEFT JOIN t_except_change_record sn ON t.id = sn.except_notice_id
		LEFT JOIN t_except_notice n2 ON t.id = n2.original_notice_id
		LEFT JOIN (SELECT * FROM t_calibration_process_record
		WHERE id IN ( SELECT max( c.id ) AS id FROM t_calibration_process_record c WHERE c.business_code = 'exceptNoticeLc' GROUP BY c.business_id )
		) t2 ON t.id = t2.business_id
		LEFT JOIN t_project p ON t.project_id = p.id
        LEFT JOIN t_project_bid_section pbs ON p.id = pbs.project_id
        AND pbs.id = sn.section_id
        LEFT JOIN t_project_entrust_user tpe ON pbs.project_id = tpe.project_id
		LEFT JOIN r_user_department rud on tpe.user_id = rud.user_id  and tpe.department_id = rud.department_id
	WHERE
		t.is_delete = 0
		AND t.except_type != 4
		AND ( t.`status` = 7 OR t.`status` = 8 or t2.operation = '退回撤回' )
		AND (n2.id IS NULL OR ( n2.id IS NOT NULL AND n2.`status` != 4 ))
		and p.is_delete = 0
		AND pbs.is_delete = 0
		AND tpe.is_delete = 0
		AND tpe.type = 1
		AND rud.is_delete = 0
		<if test="condition.keyWords != null and condition.keyWords != ''">
			and (
			p.purchase_number like concat('%',#{condition.keyWords},'%')
			or
			p.purchase_name like concat('%',#{condition.keyWords},'%')
			)
		</if>
		${dataScope}
		GROUP BY
		t.submit_time,
		t.updated_time,
		p.purchase_name,
		p.purchase_number,
		t.project_id,
		pbs.bid_round,
		p.base_project_id,
		p.re_tender,
		p.package_segment_status,
		p.bid_type_name
	</select>


	<select id="getArchive" resultType="com.hzw.sunflower.controller.project.response.WaitListVo">
	select
		t.submit_time as time,
		t.updated_time AS refundTime,
		'archive' AS type,
		p.purchase_name AS projectName,
		p.purchase_number AS projectNum,
		t.project_id,
		min( pbs.id ) section_id,
		group_concat( DISTINCT pbs.package_number ) AS package_number,
		pbs.bid_round,
		p.base_project_id,
		p.re_tender,
		p.package_segment_status AS packageSegmentStatus,
		p.bid_type_name AS bidType,
		1 as countNum
	FROM
		t_archive_project t
		LEFT JOIN t_archive_bid_section ts ON ts.bid_id = t.section_id
		LEFT JOIN t_project p ON t.project_id = p.id
        LEFT JOIN t_project_bid_section pbs ON p.id = pbs.project_id
         AND pbs.id = t.section_id
        LEFT JOIN t_project_entrust_user tpe ON pbs.project_id = tpe.project_id
		LEFT JOIN r_user_department rud on tpe.user_id = rud.user_id  and tpe.department_id = rud.department_id
	WHERE
		t.is_delete = 0
		AND ts.`status` = 55
		and p.is_delete = 0
		AND pbs.is_delete = 0
		AND tpe.is_delete = 0
		AND tpe.type = 1
		AND rud.is_delete = 0
		<if test="condition.keyWords != null and condition.keyWords != ''">
			and (
			p.purchase_number like concat('%',#{condition.keyWords},'%')
			or
			p.purchase_name like concat('%',#{condition.keyWords},'%')
			)
		</if>
		${dataScope}
		GROUP BY
		t.submit_time,
		t.updated_time,
		p.purchase_name,
		p.purchase_number,
		t.project_id,
		pbs.bid_round,
		p.base_project_id,
		p.re_tender,
		p.package_segment_status,
		p.bid_type_name
	</select>


	<select id="getMaterialApplication" resultType="com.hzw.sunflower.controller.project.response.WaitListVo">
	select
		t.apply_time as time,
		t.updated_time AS refundTime,
		t.id AS `business_id`,
		'materialApplication' AS type,
		1 as countNum
		FROM
		t_material_apply t
		LEFT JOIN r_user_department rud on t.created_user_id = rud.user_id
		WHERE
		t.is_delete = 0
		AND t.apply_status = 5
		AND rud.is_delete = 0
		AND (rud.department_id IN ( SELECT department_id FROM r_user_department WHERE user_id = #{condition.userId} ) OR
		t.created_user_id = #{condition.userId})
	</select>

	<select id="getWithLock" resultType="com.hzw.sunflower.controller.project.response.WaitListVo">
	select
		t.application_time as time,
		t.updated_time AS refundTime,
		t.id AS business_id,
		'withLock' AS type,
		p.purchase_name AS projectName,
		p.purchase_number AS projectNum,
		t.project_id,
		min( pbs.id ) section_id,
		group_concat( DISTINCT pbs.package_number ) AS package_number,
		pbs.bid_round,
		p.base_project_id,
		p.re_tender,
		p.package_segment_status AS packageSegmentStatus,
		p.bid_type_name AS bidType,
		1 as countNum
	FROM
		t_lock_application t
		LEFT JOIN t_project p ON t.project_id = p.id
        LEFT JOIN t_project_bid_section pbs ON p.id = pbs.project_id
         AND pbs.id = t.package_id
        LEFT JOIN t_project_entrust_user tpe ON pbs.project_id = tpe.project_id
		LEFT JOIN r_user_department rud on tpe.user_id = rud.user_id  and tpe.department_id = rud.department_id
	WHERE
		t.is_delete = 0
		AND t.lock_status = 5
		and p.is_delete = 0
		AND pbs.is_delete = 0
		AND tpe.is_delete = 0
		AND tpe.type = 1
		AND rud.is_delete = 0
		<if test="condition.keyWords != null and condition.keyWords != ''">
			and (
			p.purchase_number like concat('%',#{condition.keyWords},'%')
			or
			p.purchase_name like concat('%',#{condition.keyWords},'%')
			)
		</if>
		${dataScope}
		GROUP BY
		t.application_time,
		t.id,
		t.updated_time,
		p.purchase_name,
		p.purchase_number,
		t.project_id,
		pbs.bid_round,
		p.base_project_id,
		p.re_tender,
		p.package_segment_status,
		p.bid_type_name
	</select>

	<select id="getSpecialTicketChange" resultType="com.hzw.sunflower.controller.project.response.WaitListVo">
	select
		t.updated_time as time,
		t2.created_time AS refundTime,
		'specialTicketChange' AS type,
		p.purchase_name AS projectName,
		p.purchase_number AS projectNum,
		t.project_id,
		min( pbs.id ) section_id,
		group_concat( DISTINCT pbs.package_number ) AS package_number,
		pbs.bid_round,
		p.base_project_id,
		p.re_tender,
		p.package_segment_status AS packageSegmentStatus,
		p.bid_type_name AS bidType,
		1 as countNum
	FROM
		t_pay_all t
        LEFT JOIN (
        SELECT
            *
        FROM
            t_calibration_process_record
        WHERE
            id IN ( SELECT max( c.id ) AS id FROM t_calibration_process_record c WHERE c.business_code = 'CHANGE_TICKET' GROUP BY c.business_id )
        ) t2 ON t.id = t2.business_id
		LEFT JOIN t_project p ON t.project_id = p.id
        LEFT JOIN t_project_bid_section pbs ON p.id = pbs.project_id
          AND find_in_set(pbs.id,t.section_ids)
        LEFT JOIN t_project_entrust_user tpe ON pbs.project_id = tpe.project_id
		LEFT JOIN r_user_department rud on tpe.user_id = rud.user_id  and tpe.department_id = rud.department_id
	WHERE
		t.is_delete = 0
        AND t2.is_delete = 0
        AND t2.operation = '退回'
		and p.is_delete = 0
		AND pbs.is_delete = 0
		AND tpe.is_delete = 0
		AND tpe.type = 1
		AND rud.is_delete = 0
		<if test="condition.keyWords != null and condition.keyWords != ''">
			and (
			p.purchase_number like concat('%',#{condition.keyWords},'%')
			or
			p.purchase_name like concat('%',#{condition.keyWords},'%')
			)
		</if>
		${dataScope}
		GROUP BY
		t.updated_time,
		t2.created_time,
		p.purchase_name,
		p.purchase_number,
		t.project_id,
		pbs.bid_round,
		p.base_project_id,
		p.re_tender,
		p.package_segment_status,
		p.bid_type_name
	</select>

	<select id="getRegisteredSuppliers" resultType="com.hzw.sunflower.controller.project.response.WaitListVo">
	select
		t.created_time as time,
		t.updated_time AS refundTime,
		'registeredSuppliers' AS type,
		p.purchase_name AS projectName,
		p.purchase_number AS projectNum,
		t.project_id,
		min( pbs.id ) section_id,
		group_concat( DISTINCT pbs.package_number ) AS package_number,
		pbs.bid_round,
		p.base_project_id,
		p.re_tender,
		p.package_segment_status AS packageSegmentStatus,
		p.bid_type_name AS bidType,
		1 as countNum
	FROM
		t_supplier_register_apply t
		LEFT JOIN t_project p ON t.project_id = p.id
        LEFT JOIN t_project_bid_section pbs ON p.id = pbs.project_id
          AND find_in_set(pbs.id,t.sub_ids)
        LEFT JOIN t_project_entrust_user tpe ON pbs.project_id = tpe.project_id
		LEFT JOIN r_user_department rud on tpe.user_id = rud.user_id  and tpe.department_id = rud.department_id
	WHERE
		t.is_delete = 0
		AND t.`status` = 5
		and p.is_delete = 0
		AND pbs.is_delete = 0
		AND tpe.is_delete = 0
		AND tpe.type = 1
		AND rud.is_delete = 0
		<if test="condition.keyWords != null and condition.keyWords != ''">
			and (
			p.purchase_number like concat('%',#{condition.keyWords},'%')
			or
			p.purchase_name like concat('%',#{condition.keyWords},'%')
			)
		</if>
		${dataScope}
		GROUP BY
		t.created_time,
		t.updated_time,
		p.purchase_name,
		p.purchase_number,
		t.project_id,
		pbs.bid_round,
		p.base_project_id,
		p.re_tender,
		p.package_segment_status,
		p.bid_type_name
	</select>

	<select id="getRerecordPayment" resultType="com.hzw.sunflower.controller.project.response.WaitListVo">
	select
		t.created_time as time,
		t.updated_time AS refundTime,
		'rerecordPayment' AS type,
		p.purchase_name AS projectName,
		p.purchase_number AS projectNum,
		t1.project_id,
		min( pbs.id ) section_id,
		group_concat( DISTINCT pbs.package_number ) AS package_number,
		pbs.bid_round,
		p.base_project_id,
		p.re_tender,
		p.package_segment_status AS packageSegmentStatus,
		p.bid_type_name AS bidType,
		1 as countNum
	FROM
		t_apply_pay_file t
		LEFT JOIN t_apply_info t1 ON t.apply_id = t1.apply_id
		LEFT JOIN t_project p ON t1.project_id = p.id
        LEFT JOIN t_project_bid_section pbs ON p.id = pbs.project_id
          AND pbs.id = t1.sub_id
        LEFT JOIN t_project_entrust_user tpe ON pbs.project_id = tpe.project_id
		LEFT JOIN r_user_department rud on tpe.user_id = rud.user_id  and tpe.department_id = rud.department_id
	WHERE
		t.is_delete = 0
		AND t1.is_delete = 0
		AND t.`status` = 3
		and p.is_delete = 0
		AND pbs.is_delete = 0
		AND tpe.is_delete = 0
		AND tpe.type = 1
		AND rud.is_delete = 0
		<if test="condition.keyWords != null and condition.keyWords != ''">
			and (
			p.purchase_number like concat('%',#{condition.keyWords},'%')
			or
			p.purchase_name like concat('%',#{condition.keyWords},'%')
			)
		</if>
		${dataScope}
		GROUP BY
		t.created_time,
		t.updated_time,
		p.purchase_name,
		p.purchase_number,
		t1.project_id,
		pbs.bid_round,
		p.base_project_id,
		p.re_tender,
		p.package_segment_status,
		p.bid_type_name
	</select>


	<select id="getAgencyService" resultType="com.hzw.sunflower.controller.project.response.WaitListVo">
	select
		t.created_time as time,
		t.updated_time AS refundTime,
		'agencyService' AS type,
		p.purchase_name AS projectName,
		p.purchase_number AS projectNum,
		t.project_id,
		min( pbs.id ) section_id,
		group_concat( DISTINCT pbs.package_number ) AS package_number,
		pbs.bid_round,
		p.base_project_id,
		p.re_tender,
		p.package_segment_status AS packageSegmentStatus,
		p.bid_type_name AS bidType,
		1 as countNum
	FROM
		t_agency_fee_apply t
		LEFT JOIN t_project p ON t.project_id = p.id
        LEFT JOIN t_project_bid_section pbs ON p.id = pbs.project_id
          AND pbs.id = t.section_id
        LEFT JOIN t_project_entrust_user tpe ON pbs.project_id = tpe.project_id
		LEFT JOIN r_user_department rud on tpe.user_id = rud.user_id  and tpe.department_id = rud.department_id
	WHERE
		t.is_delete = 0
		AND t.`status` = 5
		and p.is_delete = 0
		AND pbs.is_delete = 0
		AND tpe.is_delete = 0
		AND tpe.type = 1
		AND rud.is_delete = 0
		<if test="condition.keyWords != null and condition.keyWords != ''">
			and (
			p.purchase_number like concat('%',#{condition.keyWords},'%')
			or
			p.purchase_name like concat('%',#{condition.keyWords},'%')
			)
		</if>
		${dataScope}
		GROUP BY
		t.created_time,
		t.updated_time,
		p.purchase_name,
		p.purchase_number,
		t.project_id,
		pbs.bid_round,
		p.base_project_id,
		p.re_tender,
		p.package_segment_status,
		p.bid_type_name
	</select>

	<select id="getDataModification" resultType="com.hzw.sunflower.controller.project.response.WaitListVo">
		SELECT
		t.created_time AS time,
		t.updated_time AS refundTime,
		'dataModification' AS type,
		CONCAT(t.type,',',t.content) content,
		t.id business_id,
		1 as countNum
		FROM
		t_data_update_record t
		LEFT JOIN r_user_department rud on t.created_user_id = rud.user_id
		WHERE
		t.is_delete = 0
		AND t.`status` = 3
		AND rud.is_delete = 0
		AND (rud.department_id IN ( SELECT department_id FROM r_user_department WHERE user_id = #{condition.userId} ) OR
		t.created_user_id = #{condition.userId})
	</select>


	<select id="getDepositRefund" resultType="com.hzw.sunflower.controller.project.response.WaitListVo">
	select
		t.created_time as time,
		t.updated_time AS refundTime,
		'depositRefund' AS type,
		p.purchase_name AS projectName,
		p.purchase_number AS projectNum,
		t.project_id,
		min( pbs.id ) section_id,
		group_concat( DISTINCT pbs.package_number ) AS package_number,
		pbs.bid_round,
		p.base_project_id,
		p.re_tender,
		p.package_segment_status AS packageSegmentStatus,
		p.bid_type_name AS bidType,
		1 as countNum
	FROM
		t_bond t
		LEFT JOIN t_project p ON t.project_id = p.id
        LEFT JOIN t_project_bid_section pbs ON p.id = pbs.project_id
          AND pbs.id = t.section_id
        LEFT JOIN t_project_entrust_user tpe ON pbs.project_id = tpe.project_id
		LEFT JOIN r_user_department rud on tpe.user_id = rud.user_id  and tpe.department_id = rud.department_id
	WHERE
		t.is_delete = 0
		AND t.`status` = 2
		and p.is_delete = 0
		AND pbs.is_delete = 0
		AND tpe.is_delete = 0
		AND tpe.type = 1
		AND rud.is_delete = 0
		<if test="condition.keyWords != null and condition.keyWords != ''">
			and (
			p.purchase_number like concat('%',#{condition.keyWords},'%')
			or
			p.purchase_name like concat('%',#{condition.keyWords},'%')
			)
		</if>
		${dataScope}
		GROUP BY
		t.created_time,
		t.updated_time,
		p.purchase_name,
		p.purchase_number,
		t.project_id,
		pbs.bid_round,
		p.base_project_id,
		p.re_tender,
		p.package_segment_status,
		p.bid_type_name
	</select>


	<select id="getExpertExtraction" resultType="com.hzw.sunflower.controller.project.response.WaitListVo">
	select
		t.id businessId,
		t.created_time as time,
		t.updated_time AS refundTime,
		'expertExtraction' AS type,
		p.purchase_name AS projectName,
		p.purchase_number AS projectNum,
		t1.project_id,
		min( pbs.id ) section_id,
		group_concat( DISTINCT pbs.package_number ) AS package_number,
		pbs.bid_round,
		p.base_project_id,
		p.re_tender,
		p.package_segment_status AS packageSegmentStatus,
		p.bid_type_name AS bidType,
		1 as countNum
	FROM
		t_expert_extraction t
		LEFT JOIN t_expert_extraction_project t1 ON t1.extration_id = t.id
		LEFT JOIN t_project p ON t1.project_id = p.id
        LEFT JOIN t_project_bid_section pbs ON p.id = pbs.project_id
          AND pbs.id = t1.section_id
        LEFT JOIN t_project_entrust_user tpe ON pbs.project_id = tpe.project_id
		LEFT JOIN r_user_department rud on tpe.user_id = rud.user_id  and tpe.department_id = rud.department_id
	WHERE
		t.is_delete = 0
		AND t.extract_status = 4
		AND t1.is_delete = 0
		and p.is_delete = 0
		AND pbs.is_delete = 0
		AND tpe.is_delete = 0
		AND tpe.type = 1
		AND rud.is_delete = 0
		<if test="condition.keyWords != null and condition.keyWords != ''">
			and (
			p.purchase_number like concat('%',#{condition.keyWords},'%')
			or
			p.purchase_name like concat('%',#{condition.keyWords},'%')
			)
		</if>
		${dataScope}
		GROUP BY
		t.id,
		t.created_time,
		t.updated_time,
		p.purchase_name,
		p.purchase_number,
		t1.project_id,
		pbs.bid_round,
		p.base_project_id,
		p.re_tender,
		p.package_segment_status,
		p.bid_type_name
	</select>

	<select id="getExpertsSelectedRecord" resultType="com.hzw.sunflower.controller.project.response.WaitListVo">
	select
		t.created_time as time,
		t.updated_time AS refundTime,
		'expertsSelectedRecord' AS type,
		p.purchase_name AS projectName,
		p.purchase_number AS projectNum,
		t2.project_id,
		min( pbs.id ) section_id,
		group_concat( DISTINCT pbs.package_number ) AS package_number,
		pbs.bid_round,
		p.base_project_id,
		p.re_tender,
		p.package_segment_status AS packageSegmentStatus,
		p.bid_type_name AS bidType,
		1 as countNum
	FROM
		t_expert_extraction_beian t
		LEFT JOIN t_expert_extraction t1 ON t1.id = t.expert_extraction_id
		LEFT JOIN t_expert_extraction_project t2 ON t2.extration_id = t1.id
		LEFT JOIN t_project p ON t2.project_id = p.id
		LEFT JOIN (SELECT max(id) id,expert_extraction_id FROM t_expert_extraction_beian t GROUP BY expert_extraction_id) t3 ON FIND_IN_SET(t.id,t3.id)
        LEFT JOIN t_project_bid_section pbs ON p.id = pbs.project_id
          AND pbs.id = t2.section_id
        LEFT JOIN t_project_entrust_user tpe ON pbs.project_id = tpe.project_id
		LEFT JOIN r_user_department rud on tpe.user_id = rud.user_id  and tpe.department_id = rud.department_id
	WHERE
		t.is_delete = 0
		AND t.`status` = 2
		AND FIND_IN_SET(t.id,t3.id)
		AND t1.is_delete = 0
		AND t2.is_delete = 0
		and p.is_delete = 0
		AND pbs.is_delete = 0
		AND tpe.is_delete = 0
		AND tpe.type = 1
		AND rud.is_delete = 0
		<if test="condition.keyWords != null and condition.keyWords != ''">
			and (
			p.purchase_number like concat('%',#{condition.keyWords},'%')
			or
			p.purchase_name like concat('%',#{condition.keyWords},'%')
			)
		</if>
		${dataScope}
		GROUP BY
		t.created_time,
		t.updated_time,
		p.purchase_name,
		p.purchase_number,
		t2.project_id,
		pbs.bid_round,
		p.base_project_id,
		p.re_tender,
		p.package_segment_status,
		p.bid_type_name
	</select>

	<select id="getTenderBidCount" resultType="java.lang.Integer">
		select count(1) from (
		select
		t.submit_time as time,
		t2.created_time AS refundTime,
		'tenderBid' AS type,
		p.purchase_name AS projectName,
		p.purchase_number AS projectNum,
		t.project_id,
		min( pbs.id ) section_id,
		group_concat( DISTINCT pbs.package_number ) AS package_number,
		pbs.bid_round,
		p.base_project_id,
		p.re_tender,
		p.package_segment_status AS packageSegmentStatus,
		p.bid_type_name AS bidType,
		1 as countNum
		FROM
		t_project_bid_notice t
		LEFT JOIN t_section_notice sn ON t.id = sn.notice_id
		LEFT JOIN (SELECT * FROM t_approval_opinion o
		WHERE id IN ( SELECT max( o.id ) AS id FROM t_approval_opinion o GROUP BY o.notice_id )
		) t2 ON t.id = t2.notice_id
		LEFT JOIN t_project p ON t.project_id = p.id
		LEFT JOIN t_project_bid_section pbs ON p.id = pbs.project_id
		AND pbs.id = sn.section_id
		LEFT JOIN t_project_entrust_user tpe ON pbs.project_id = tpe.project_id
		LEFT JOIN r_user_department rud on tpe.user_id = rud.user_id  and tpe.department_id = rud.department_id
		WHERE
		t.is_delete = 0
		AND sn.is_delete = 0
		and t2.is_delete = 0
		AND (t.notice_progress = 6 OR t2.approval_type = 5)
		and p.is_delete = 0
		AND pbs.is_delete = 0
		AND tpe.is_delete = 0
		AND tpe.type = 1
		AND rud.is_delete = 0
		<if test="condition.keyWords != null and condition.keyWords != ''">
			and (
			p.purchase_number like concat('%',#{condition.keyWords},'%')
			or
			p.purchase_name like concat('%',#{condition.keyWords},'%')
			)
		</if>
		${dataScope}
		GROUP BY
		t.submit_time,
		t2.created_time,
		p.purchase_name,
		p.purchase_number,
		t.project_id,
		pbs.bid_round,
		p.base_project_id,
		p.re_tender,
		p.package_segment_status,
		p.bid_type_name
		) t
		where 1 = 1
		<if test="condition.type != null and condition.type != ''">
			and t.type = #{condition.type}
		</if>
	</select>
	<select id="getTenderDocCount" resultType="java.lang.Integer">
		select count(1) from (select
		t.submit_time as time,
		t2.created_time AS refundTime,
		'tenderDoc' AS type,
		p.purchase_name AS projectName,
		p.purchase_number AS projectNum,
		t.project_id,
		min( pbs.id ) section_id,
		group_concat( DISTINCT pbs.package_number ) AS package_number,
		pbs.bid_round,
		p.base_project_id,
		p.re_tender,
		p.package_segment_status AS packageSegmentStatus,
		p.bid_type_name AS bidType,
		1 as countNum
		FROM
		t_project_bid_doc t
		LEFT JOIN t_project_bid_doc_relation sn ON t.id = sn.doc_id
		LEFT JOIN (SELECT * FROM t_approval_opinion o
		WHERE id IN ( SELECT max( o.id ) AS id FROM t_approval_opinion o GROUP BY o.doc_id )
		) t2 ON t.id = t2.doc_id
		LEFT JOIN t_project p ON t.project_id = p.id
		LEFT JOIN t_project_bid_section pbs ON p.id = pbs.project_id
		AND pbs.id = sn.section_id
		LEFT JOIN t_project_entrust_user tpe ON pbs.project_id = tpe.project_id
		LEFT JOIN r_user_department rud on tpe.user_id = rud.user_id  and tpe.department_id = rud.department_id
		WHERE
		t.is_delete = 0
		AND sn.is_delete = 0
		and t2.is_delete = 0
		AND (t.notice_progress = 6 OR t2.approval_type = 5)
		and p.is_delete = 0
		AND pbs.is_delete = 0
		AND tpe.is_delete = 0
		AND tpe.type = 1
		AND rud.is_delete = 0
		<if test="condition.keyWords != null and condition.keyWords != ''">
			and (
			p.purchase_number like concat('%',#{condition.keyWords},'%')
			or
			p.purchase_name like concat('%',#{condition.keyWords},'%')
			)
		</if>
		${dataScope}
		GROUP BY
		t.submit_time,
		t2.created_time,
		p.purchase_name,
		p.purchase_number,
		t.project_id,
		pbs.bid_round,
		p.base_project_id,
		p.re_tender,
		p.package_segment_status,
		p.bid_type_name) t
		where 1 = 1
		<if test="condition.type != null and condition.type != ''">
			and t.type = #{condition.type}
		</if>
	</select>
	<select id="getSupplementBidCount" resultType="java.lang.Integer">
		select count(1) from (select
		t.submit_time as time,
		t2.created_time AS refundTime,
		'supplementBid' AS type,
		p.purchase_name AS projectName,
		p.purchase_number AS projectNum,
		t.project_id,
		min( pbs.id ) section_id,
		group_concat( DISTINCT pbs.package_number ) AS package_number,
		pbs.bid_round,
		p.base_project_id,
		p.re_tender,
		p.package_segment_status AS packageSegmentStatus,
		p.bid_type_name AS bidType,
		1 as countNum
		FROM
		t_supplement_bulletin t
		LEFT JOIN (
		SELECT * FROM t_calibration_process_record
		WHERE id IN ( SELECT max( c.id ) AS id FROM t_calibration_process_record c WHERE c.business_code = 'supplement' GROUP BY c.business_id )
		) t2 ON t.id = t2.business_id
		LEFT JOIN t_project p ON t.project_id = p.id
		LEFT JOIN t_project_bid_section pbs ON p.id = pbs.project_id
		AND find_in_set(pbs.id,t.related_bid_section)
		LEFT JOIN t_project_entrust_user tpe ON pbs.project_id = tpe.project_id
		LEFT JOIN r_user_department rud on tpe.user_id = rud.user_id  and tpe.department_id = rud.department_id
		WHERE
		t.is_delete = 0
		AND t2.is_delete = 0
		AND (t.`status` = 6 OR t2.operation = '退回')
		and p.is_delete = 0
		AND pbs.is_delete = 0
		AND tpe.is_delete = 0
		AND tpe.type = 1
		AND rud.is_delete = 0
		<if test="condition.keyWords != null and condition.keyWords != ''">
			and (
			p.purchase_number like concat('%',#{condition.keyWords},'%')
			or
			p.purchase_name like concat('%',#{condition.keyWords},'%')
			)
		</if>
		${dataScope}
		GROUP BY
		t.submit_time,
		t2.created_time,
		p.purchase_name,
		p.purchase_number,
		t.project_id,
		pbs.bid_round,
		p.base_project_id,
		p.re_tender,
		p.package_segment_status,
		p.bid_type_name) t
		where 1 = 1
		<if test="condition.type != null and condition.type != ''">
			and t.type = #{condition.type}
		</if>
	</select>
	<select id="getClarifyBidCount" resultType="java.lang.Integer">
		select count(1) from (select
		t.submit_time as time,
		t2.created_time AS refundTime,
		'clarifyBid' AS type,
		p.purchase_name AS projectName,
		p.purchase_number AS projectNum,
		t.project_id,
		min( pbs.id ) section_id,
		group_concat( DISTINCT pbs.package_number ) AS package_number,
		pbs.bid_round,
		p.base_project_id,
		p.re_tender,
		p.package_segment_status AS packageSegmentStatus,
		p.bid_type_name AS bidType,
		1 as countNum
		FROM
		t_e_question_reply t
		LEFT JOIN (SELECT * FROM t_calibration_process_record
		WHERE id IN ( SELECT max( c.id ) AS id FROM t_calibration_process_record c WHERE c.business_code = 'clarifyReply' GROUP BY c.business_id )
		) t2 ON t.id = t2.business_id
		LEFT JOIN t_project p ON t.project_id = p.id
		LEFT JOIN t_project_bid_section pbs ON p.id = pbs.project_id
		AND find_in_set(pbs.id,t.section_ids)
		LEFT JOIN t_project_entrust_user tpe ON pbs.project_id = tpe.project_id
		LEFT JOIN r_user_department rud on tpe.user_id = rud.user_id  and tpe.department_id = rud.department_id
		WHERE
		t.is_delete = 0
		AND t2.is_delete = 0
		AND (t.`status` = 3 OR t2.operation = '退回')
		and p.is_delete = 0
		AND pbs.is_delete = 0
		AND tpe.is_delete = 0
		AND tpe.type = 1
		AND rud.is_delete = 0
		<if test="condition.keyWords != null and condition.keyWords != ''">
			and (
			p.purchase_number like concat('%',#{condition.keyWords},'%')
			or
			p.purchase_name like concat('%',#{condition.keyWords},'%')
			)
		</if>
		${dataScope}
		GROUP BY
		t.submit_time,
		t2.created_time,
		p.purchase_name,
		p.purchase_number,
		t.project_id,
		pbs.bid_round,
		p.base_project_id,
		p.re_tender,
		p.package_segment_status,
		p.bid_type_name) t
		where 1 = 1
		<if test="condition.type != null and condition.type != ''">
			and t.type = #{condition.type}
		</if>
	</select>
	<select id="getObjectionCount" resultType="java.lang.Integer">
		select count(1) from (select
		te.submit_time as time,
		t2.created_time AS refundTime,
		'objection' AS type,
		p.purchase_name AS projectName,
		p.purchase_number AS projectNum,
		t.project_id,
		min( pbs.id ) section_id,
		group_concat( DISTINCT pbs.package_number ) AS package_number,
		pbs.bid_round,
		p.base_project_id,
		p.re_tender,
		p.package_segment_status AS packageSegmentStatus,
		p.bid_type_name AS bidType,
		1 as countNum
		FROM
		t_e_propose_objection t
		LEFT JOIN ( SELECT * FROM
		( SELECT RANK() OVEr ( PARTITION BY clarify_objection_id ORDER BY order_num DESC ) num, a.* FROM t_e_clarify_reply a ) a
		WHERE a.num = 1
		) te ON te.clarify_objection_id = t.id
		LEFT JOIN (SELECT * FROM t_calibration_process_record
		WHERE id IN ( SELECT max( c.id ) AS id FROM t_calibration_process_record c WHERE c.business_code = 'objection' GROUP BY c.business_id )
		) t2 ON t.id = t2.business_id
		LEFT JOIN t_project p ON t.project_id = p.id
		LEFT JOIN t_project_bid_section pbs ON p.id = pbs.project_id
		AND find_in_set(pbs.id,t.section_ids)
		LEFT JOIN t_project_entrust_user tpe ON pbs.project_id = tpe.project_id
		LEFT JOIN r_user_department rud on tpe.user_id = rud.user_id  and tpe.department_id = rud.department_id
		WHERE
		t.is_delete = 0
		AND t2.is_delete = 0
		AND (te.`status` = 9 OR t2.operation = '退回')
		and p.is_delete = 0
		AND pbs.is_delete = 0
		AND tpe.is_delete = 0
		AND tpe.type = 1
		AND rud.is_delete = 0
		<if test="condition.keyWords != null and condition.keyWords != ''">
			and (
			p.purchase_number like concat('%',#{condition.keyWords},'%')
			or
			p.purchase_name like concat('%',#{condition.keyWords},'%')
			)
		</if>
		${dataScope}
		GROUP BY
		te.submit_time,
		t2.created_time,
		p.purchase_name,
		p.purchase_number,
		t.project_id,
		pbs.bid_round,
		p.base_project_id,
		p.re_tender,
		p.package_segment_status,
		p.bid_type_name) t
		where 1 = 1
		<if test="condition.type != null and condition.type != ''">
			and t.type = #{condition.type}
		</if>
	</select>
	<select id="getWinBidCount" resultType="java.lang.Integer">
		select count(1) from (select
		t.submit_time as time,
		t.updated_time AS refundTime,
		'winBid' AS type,
		p.purchase_name AS projectName,
		p.purchase_number AS projectNum,
		t.project_id,
		min( pbs.id ) section_id,
		group_concat( DISTINCT pbs.package_number ) AS package_number,
		pbs.bid_round,
		p.base_project_id,
		p.re_tender,
		p.package_segment_status AS packageSegmentStatus,
		p.bid_type_name AS bidType,
		1 as countNum
		FROM
		t_bid_win t
		LEFT JOIN t_project p ON t.project_id = p.id
		LEFT JOIN t_project_bid_section pbs ON p.id = pbs.project_id
		AND find_in_set(pbs.id,t.section_id)
		LEFT JOIN t_project_entrust_user tpe ON pbs.project_id = tpe.project_id
		LEFT JOIN r_user_department rud on tpe.user_id = rud.user_id  and tpe.department_id = rud.department_id
		WHERE
		t.is_delete = 0
		AND t.`status` = 3
		AND t.bid_round = 2
		and p.is_delete = 0
		AND pbs.is_delete = 0
		AND tpe.is_delete = 0
		AND tpe.type = 1
		AND rud.is_delete = 0
		<if test="condition.keyWords != null and condition.keyWords != ''">
			and (
			p.purchase_number like concat('%',#{condition.keyWords},'%')
			or
			p.purchase_name like concat('%',#{condition.keyWords},'%')
			)
		</if>
		${dataScope}
		GROUP BY
		t.submit_time,
		t.updated_time,
		p.purchase_name,
		p.purchase_number,
		t.project_id,
		pbs.bid_round,
		p.base_project_id,
		p.re_tender,
		p.package_segment_status,
		p.bid_type_name)t
		where 1 = 1
		<if test="condition.type != null and condition.type != ''">
			and t.type = #{condition.type}
		</if>
	</select>
	<select id="getwinBidZgysCount" resultType="java.lang.Integer">
		select count(1) from (select
		t.submit_time as time,
		t.updated_time AS refundTime,
		'winBid_zgys' AS type,
		p.purchase_name AS projectName,
		p.purchase_number AS projectNum,
		t.project_id,
		min( pbs.id ) section_id,
		group_concat( DISTINCT pbs.package_number ) AS package_number,
		pbs.bid_round,
		p.base_project_id,
		p.re_tender,
		p.package_segment_status AS packageSegmentStatus,
		p.bid_type_name AS bidType,
		1 as countNum
		FROM
		t_bid_win t
		LEFT JOIN t_project p ON t.project_id = p.id
		LEFT JOIN t_project_bid_section pbs ON p.id = pbs.project_id
		AND find_in_set(pbs.id,t.section_id)
		LEFT JOIN t_project_entrust_user tpe ON pbs.project_id = tpe.project_id
		LEFT JOIN r_user_department rud on tpe.user_id = rud.user_id  and tpe.department_id = rud.department_id
		WHERE
		t.is_delete = 0
		AND t.`status` = 3
		AND t.bid_round = 1
		and p.is_delete = 0
		AND pbs.is_delete = 0
		AND tpe.is_delete = 0
		AND tpe.type = 1
		AND rud.is_delete = 0
		<if test="condition.keyWords != null and condition.keyWords != ''">
			and (
			p.purchase_number like concat('%',#{condition.keyWords},'%')
			or
			p.purchase_name like concat('%',#{condition.keyWords},'%')
			)
		</if>
		${dataScope}
		GROUP BY
		t.submit_time,
		t.updated_time,
		p.purchase_name,
		p.purchase_number,
		t.project_id,
		pbs.bid_round,
		p.base_project_id,
		p.re_tender,
		p.package_segment_status,
		p.bid_type_name) t
		where 1 = 1
		<if test="condition.type != null and condition.type != ''">
			and t.type = #{condition.type}
		</if>
	</select>
	<select id="getWinningBidCandidateCount" resultType="java.lang.Integer">
		select count(1) from(select
		t.submit_time as time,
		t2.created_time AS refundTime,
		'winningBidCandidate' AS type,
		p.purchase_name AS projectName,
		p.purchase_number AS projectNum,
		t.project_id,
		min( pbs.id ) section_id,
		group_concat( DISTINCT pbs.package_number ) AS package_number,
		pbs.bid_round,
		p.base_project_id,
		p.re_tender,
		p.package_segment_status AS packageSegmentStatus,
		p.bid_type_name AS bidType,
		1 as countNum
		FROM
		t_bid_win_bulletin t
		LEFT JOIN (SELECT * FROM t_calibration_process_record
		WHERE id IN ( SELECT max( c.id ) AS id FROM t_calibration_process_record c WHERE c.business_code = 'winBULLETIN' GROUP BY c.business_id )
		) t2 ON t.id = t2.business_id
		LEFT JOIN t_project p ON t.project_id = p.id
		LEFT JOIN t_project_bid_section pbs ON p.id = pbs.project_id
		AND find_in_set(pbs.id,t.related_bid_section)
		LEFT JOIN t_project_entrust_user tpe ON pbs.project_id = tpe.project_id
		LEFT JOIN r_user_department rud on tpe.user_id = rud.user_id  and tpe.department_id = rud.department_id
		WHERE
		t.is_delete = 0
		AND t.bid_win_type = 0
		AND t2.is_delete = 0
		AND (t.`status` = 6 OR t2.operation = '退回')
		and p.is_delete = 0
		AND pbs.is_delete = 0
		AND tpe.is_delete = 0
		AND tpe.type = 1
		AND rud.is_delete = 0
		<if test="condition.keyWords != null and condition.keyWords != ''">
			and (
			p.purchase_number like concat('%',#{condition.keyWords},'%')
			or
			p.purchase_name like concat('%',#{condition.keyWords},'%')
			)
		</if>
		${dataScope}
		GROUP BY
		t.submit_time,
		t2.created_time,
		p.purchase_name,
		p.purchase_number,
		t.project_id,
		pbs.bid_round,
		p.base_project_id,
		p.re_tender,
		p.package_segment_status,
		p.bid_type_name)t
		where 1 = 1
		<if test="condition.type != null and condition.type != ''">
			and t.type = #{condition.type}
		</if>
	</select>
	<select id="getWinCandidateBidCount" resultType="java.lang.Integer">
		select count(1) from (select
		t.submit_time as time,
		t2.created_time AS refundTime,
		'winCandidateBid' AS type,
		p.purchase_name AS projectName,
		p.purchase_number AS projectNum,
		t.project_id,
		min( pbs.id ) section_id,
		group_concat( DISTINCT pbs.package_number ) AS package_number,
		pbs.bid_round,
		p.base_project_id,
		p.re_tender,
		p.package_segment_status AS packageSegmentStatus,
		p.bid_type_name AS bidType,
		1 as countNum
		FROM
		t_bid_win_bulletin t
		LEFT JOIN (SELECT * FROM t_calibration_process_record
		WHERE id IN ( SELECT max( c.id ) AS id FROM t_calibration_process_record c WHERE c.business_code = 'winBULLETIN' GROUP BY c.business_id )
		) t2 ON t.id = t2.business_id
		LEFT JOIN t_project p ON t.project_id = p.id
		LEFT JOIN t_project_bid_section pbs ON p.id = pbs.project_id
		AND find_in_set(pbs.id,t.related_bid_section)
		LEFT JOIN t_project_entrust_user tpe ON pbs.project_id = tpe.project_id
		LEFT JOIN r_user_department rud on tpe.user_id = rud.user_id  and tpe.department_id = rud.department_id
		WHERE
		t.is_delete = 0
		AND t.bid_win_type = 1
		AND t2.is_delete = 0
		AND (t.`status` = 6 OR t2.operation = '退回')
		and p.is_delete = 0
		AND pbs.is_delete = 0
		AND tpe.is_delete = 0
		AND tpe.type = 1
		AND rud.is_delete = 0
		<if test="condition.keyWords != null and condition.keyWords != ''">
			and (
			p.purchase_number like concat('%',#{condition.keyWords},'%')
			or
			p.purchase_name like concat('%',#{condition.keyWords},'%')
			)
		</if>
		${dataScope}
		GROUP BY
		t.submit_time,
		t2.created_time,
		p.purchase_name,
		p.purchase_number,
		t.project_id,
		pbs.bid_round,
		p.base_project_id,
		p.re_tender,
		p.package_segment_status,
		p.bid_type_name)t
		where 1 = 1
		<if test="condition.type != null and condition.type != ''">
			and t.type = #{condition.type}
		</if>
	</select>
	<select id="getWinNoticeCount" resultType="java.lang.Integer">
		select count(1) from(select
		t.submit_time as time,
		t2.created_time AS refundTime,
		'winNotice' AS type,
		p.purchase_name AS projectName,
		p.purchase_number AS projectNum,
		t.project_id,
		min( pbs.id ) section_id,
		group_concat( DISTINCT pbs.package_number ) AS package_number,
		pbs.bid_round,
		p.base_project_id,
		p.re_tender,
		p.package_segment_status AS packageSegmentStatus,
		p.bid_type_name AS bidType,
		1 as countNum
		FROM
		t_bid_win_notice t
		LEFT JOIN (
		SELECT
		*
		FROM
		t_calibration_process_record
		WHERE
		id IN ( SELECT max( c.id ) AS id FROM t_calibration_process_record c WHERE c.business_code = 'winNotice' GROUP BY c.business_id )
		) t2 ON t.id = t2.business_id
		LEFT JOIN t_project p ON t.project_id = p.id
		LEFT JOIN t_project_bid_section pbs ON p.id = pbs.project_id
		AND find_in_set(pbs.id,t.section_id)
		LEFT JOIN t_project_entrust_user tpe ON pbs.project_id = tpe.project_id
		LEFT JOIN r_user_department rud on tpe.user_id = rud.user_id  and tpe.department_id = rud.department_id
		WHERE
		t.is_delete = 0
		AND t.bid_round = 2
		AND t.`status` != 5
		AND (t.`status` = 6 or t2.operation = '退回')
		and p.is_delete = 0
		AND pbs.is_delete = 0
		AND tpe.is_delete = 0
		AND tpe.type = 1
		AND rud.is_delete = 0
		<if test="condition.keyWords != null and condition.keyWords != ''">
			and (
			p.purchase_number like concat('%',#{condition.keyWords},'%')
			or
			p.purchase_name like concat('%',#{condition.keyWords},'%')
			)
		</if>
		${dataScope}
		GROUP BY
		t.submit_time,
		t2.created_time,
		p.purchase_name,
		p.purchase_number,
		t.project_id,
		pbs.bid_round,
		p.base_project_id,
		p.re_tender,
		p.package_segment_status,
		p.bid_type_name) t
		where 1 = 1
		<if test="condition.type != null and condition.type != ''">
			and t.type = #{condition.type}
		</if>
	</select>
	<select id="getReWinNoticeCount" resultType="java.lang.Integer">
		select count(1) from (select
		t.submit_time as time,
		t2.created_time AS refundTime,
		'reWinNotice' AS type,
		p.purchase_name AS projectName,
		p.purchase_number AS projectNum,
		t.project_id,
		min( pbs.id ) section_id,
		group_concat( DISTINCT pbs.package_number ) AS package_number,
		pbs.bid_round,
		p.base_project_id,
		p.re_tender,
		p.package_segment_status AS packageSegmentStatus,
		p.bid_type_name AS bidType,
		1 as countNum
		FROM
		t_bid_win_notice t
		LEFT JOIN (
		SELECT
		*
		FROM
		t_calibration_process_record
		WHERE
		id IN ( SELECT max( c.id ) AS id FROM t_calibration_process_record c WHERE c.business_code = 'winNotice' GROUP BY c.business_id )
		) t2 ON t.id = t2.business_id
		LEFT JOIN t_project p ON t.project_id = p.id
		LEFT JOIN t_project_bid_section pbs ON p.id = pbs.project_id
		AND find_in_set(pbs.id,t.section_id)
		LEFT JOIN t_project_entrust_user tpe ON pbs.project_id = tpe.project_id
		LEFT JOIN r_user_department rud on tpe.user_id = rud.user_id  and tpe.department_id = rud.department_id
		WHERE
		t.is_delete = 0
		AND t2.is_delete = 0
		AND t2.operation = '退回'
		AND t.is_reapply = 1
		AND t.bid_round = 2
		and p.is_delete = 0
		AND pbs.is_delete = 0
		AND tpe.is_delete = 0
		AND tpe.type = 1
		AND rud.is_delete = 0
		<if test="condition.keyWords != null and condition.keyWords != ''">
			and (
			p.purchase_number like concat('%',#{condition.keyWords},'%')
			or
			p.purchase_name like concat('%',#{condition.keyWords},'%')
			)
		</if>
		${dataScope}
		GROUP BY
		t.submit_time,
		t2.created_time,
		p.purchase_name,
		p.purchase_number,
		t.project_id,
		pbs.bid_round,
		p.base_project_id,
		p.re_tender,
		p.package_segment_status,
		p.bid_type_name)t
		where 1 = 1
		<if test="condition.type != null and condition.type != ''">
			and t.type = #{condition.type}
		</if>
	</select>
	<select id="getBidWinNoticePreCount" resultType="java.lang.Integer">
		select count(1) from (select
		t.submit_time as time,
		t2.created_time AS refundTime,
		'bidWinNoticePre' AS type,
		p.purchase_name AS projectName,
		p.purchase_number AS projectNum,
		t.project_id,
		min( pbs.id ) section_id,
		group_concat( DISTINCT pbs.package_number ) AS package_number,
		pbs.bid_round,
		p.base_project_id,
		p.re_tender,
		p.package_segment_status AS packageSegmentStatus,
		p.bid_type_name AS bidType,
		1 as countNum
		FROM
		t_bid_win_notice t
		LEFT JOIN (
		SELECT
		*
		FROM
		t_calibration_process_record
		WHERE
		id IN ( SELECT max( c.id ) AS id FROM t_calibration_process_record c WHERE c.business_code = 'winNotice' GROUP BY c.business_id )
		) t2 ON t.id = t2.business_id
		LEFT JOIN t_project p ON t.project_id = p.id
		LEFT JOIN t_project_bid_section pbs ON p.id = pbs.project_id
		AND find_in_set(pbs.id,t.section_id)
		LEFT JOIN t_project_entrust_user tpe ON pbs.project_id = tpe.project_id
		LEFT JOIN r_user_department rud on tpe.user_id = rud.user_id  and tpe.department_id = rud.department_id
		WHERE
		t.is_delete = 0
		AND t.bid_round = 1
		AND t.`status` != 5
		AND t.bid_round = 1
		AND (t.`status` = 6 or t2.operation = '退回')
		and p.is_delete = 0
		AND pbs.is_delete = 0
		AND tpe.is_delete = 0
		AND tpe.type = 1
		AND rud.is_delete = 0
		<if test="condition.keyWords != null and condition.keyWords != ''">
			and (
			p.purchase_number like concat('%',#{condition.keyWords},'%')
			or
			p.purchase_name like concat('%',#{condition.keyWords},'%')
			)
		</if>
		${dataScope}
		GROUP BY
		t.submit_time,
		t2.created_time,
		p.purchase_name,
		p.purchase_number,
		t.project_id,
		pbs.bid_round,
		p.base_project_id,
		p.re_tender,
		p.package_segment_status,
		p.bid_type_name)t
		where 1 = 1
		<if test="condition.type != null and condition.type != ''">
			and t.type = #{condition.type}
		</if>
	</select>
	<select id="getReBidWinNoticePreCount" resultType="java.lang.Integer">
		select count(1) from (select
		t.submit_time as time,
		t2.created_time AS refundTime,
		'reBidWinNoticePre' AS type,
		p.purchase_name AS projectName,
		p.purchase_number AS projectNum,
		t.project_id,
		min( pbs.id ) section_id,
		group_concat( DISTINCT pbs.package_number ) AS package_number,
		pbs.bid_round,
		p.base_project_id,
		p.re_tender,
		p.package_segment_status AS packageSegmentStatus,
		p.bid_type_name AS bidType,
		1 as countNum
		FROM
		t_bid_win_notice t
		LEFT JOIN (
		SELECT
		*
		FROM
		t_calibration_process_record
		WHERE
		id IN ( SELECT max( c.id ) AS id FROM t_calibration_process_record c WHERE c.business_code = 'winNotice' GROUP BY c.business_id )
		) t2 ON t.id = t2.business_id
		LEFT JOIN t_project p ON t.project_id = p.id
		LEFT JOIN t_project_bid_section pbs ON p.id = pbs.project_id
		AND find_in_set(pbs.id,t.section_id)
		LEFT JOIN t_project_entrust_user tpe ON pbs.project_id = tpe.project_id
		LEFT JOIN r_user_department rud on tpe.user_id = rud.user_id  and tpe.department_id = rud.department_id
		WHERE
		t.is_delete = 0
		AND t2.operation = '退回'
		AND t.is_reapply = 1
		AND t.bid_round = 1
		and p.is_delete = 0
		AND pbs.is_delete = 0
		AND tpe.is_delete = 0
		AND tpe.type = 1
		AND rud.is_delete = 0
		<if test="condition.keyWords != null and condition.keyWords != ''">
			and (
			p.purchase_number like concat('%',#{condition.keyWords},'%')
			or
			p.purchase_name like concat('%',#{condition.keyWords},'%')
			)
		</if>
		${dataScope}
		GROUP BY
		t.submit_time,
		t2.created_time,
		p.purchase_name,
		p.purchase_number,
		t.project_id,
		pbs.bid_round,
		p.base_project_id,
		p.re_tender,
		p.package_segment_status,
		p.bid_type_name) t
		where 1 = 1
		<if test="condition.type != null and condition.type != ''">
			and t.type = #{condition.type}
		</if>
	</select>
	<select id="getAbnormalNoticeCount" resultType="java.lang.Integer">
		select count(1) from (select
		t.submit_time as time,
		t.updated_time AS refundTime,
		'abnormalNotice' AS type,
		p.purchase_name AS projectName,
		p.purchase_number AS projectNum,
		t.project_id,
		min( pbs.id ) section_id,
		group_concat( DISTINCT pbs.package_number ) AS package_number,
		pbs.bid_round,
		p.base_project_id,
		p.re_tender,
		p.package_segment_status AS packageSegmentStatus,
		p.bid_type_name AS bidType,
		1 as countNum
		FROM
		t_except_notice t
		LEFT JOIN t_except_change_record sn ON t.id = sn.except_notice_id
		LEFT JOIN t_except_notice n2 ON t.id = n2.original_notice_id
		LEFT JOIN (SELECT * FROM t_calibration_process_record
		WHERE id IN ( SELECT max( c.id ) AS id FROM t_calibration_process_record c WHERE c.business_code = 'exceptNoticeLc' GROUP BY c.business_id )
		) t2 ON t.id = t2.business_id
		LEFT JOIN t_project p ON t.project_id = p.id
		LEFT JOIN t_project_bid_section pbs ON p.id = pbs.project_id
		AND pbs.id = sn.section_id
		LEFT JOIN t_project_entrust_user tpe ON pbs.project_id = tpe.project_id
		LEFT JOIN r_user_department rud on tpe.user_id = rud.user_id  and tpe.department_id = rud.department_id
		WHERE
		t.is_delete = 0
		AND t.except_type != 4
		AND ( t.`status` = 7 OR t.`status` = 8 or t2.operation = '退回撤回' )
		AND (n2.id IS NULL OR ( n2.id IS NOT NULL AND n2.`status` != 4 ))
		and p.is_delete = 0
		AND pbs.is_delete = 0
		AND tpe.is_delete = 0
		AND tpe.type = 1
		AND rud.is_delete = 0
		<if test="condition.keyWords != null and condition.keyWords != ''">
			and (
			p.purchase_number like concat('%',#{condition.keyWords},'%')
			or
			p.purchase_name like concat('%',#{condition.keyWords},'%')
			)
		</if>
		${dataScope}
		GROUP BY
		t.submit_time,
		t.updated_time,
		p.purchase_name,
		p.purchase_number,
		t.project_id,
		pbs.bid_round,
		p.base_project_id,
		p.re_tender,
		p.package_segment_status,
		p.bid_type_name)t
		where 1 = 1
		<if test="condition.type != null and condition.type != ''">
			and t.type = #{condition.type}
		</if>
	</select>
	<select id="getArchiveCount" resultType="java.lang.Integer">
		select count(1) from (select
		t.submit_time as time,
		t.updated_time AS refundTime,
		'archive' AS type,
		p.purchase_name AS projectName,
		p.purchase_number AS projectNum,
		t.project_id,
		min( pbs.id ) section_id,
		group_concat( DISTINCT pbs.package_number ) AS package_number,
		pbs.bid_round,
		p.base_project_id,
		p.re_tender,
		p.package_segment_status AS packageSegmentStatus,
		p.bid_type_name AS bidType,
		1 as countNum
		FROM
		t_archive_project t
		LEFT JOIN t_archive_bid_section ts ON ts.bid_id = t.section_id
		LEFT JOIN t_project p ON t.project_id = p.id
		LEFT JOIN t_project_bid_section pbs ON p.id = pbs.project_id
		AND pbs.id = t.section_id
		LEFT JOIN t_project_entrust_user tpe ON pbs.project_id = tpe.project_id
		LEFT JOIN r_user_department rud on tpe.user_id = rud.user_id  and tpe.department_id = rud.department_id
		WHERE
		t.is_delete = 0
		AND ts.`status` = 55
		and p.is_delete = 0
		AND pbs.is_delete = 0
		AND tpe.is_delete = 0
		AND tpe.type = 1
		AND rud.is_delete = 0
		<if test="condition.keyWords != null and condition.keyWords != ''">
			and (
			p.purchase_number like concat('%',#{condition.keyWords},'%')
			or
			p.purchase_name like concat('%',#{condition.keyWords},'%')
			)
		</if>
		${dataScope}
		GROUP BY
		t.submit_time,
		t.updated_time,
		p.purchase_name,
		p.purchase_number,
		t.project_id,
		pbs.bid_round,
		p.base_project_id,
		p.re_tender,
		p.package_segment_status,
		p.bid_type_name)t
		where 1 = 1
		<if test="condition.type != null and condition.type != ''">
			and t.type = #{condition.type}
		</if>
	</select>
	<select id="getMaterialApplicationCount" resultType="java.lang.Integer">
		select count(1) from (select
		t.apply_time as time,
		t.updated_time AS refundTime,
		t.id AS `business_id`,
		'materialApplication' AS type,
		1 as countNum
		FROM
		t_material_apply t
		LEFT JOIN r_user_department rud  on t.created_user_id = rud.user_id
		WHERE
		t.is_delete = 0
		AND t.apply_status = 5
		AND rud.is_delete = 0
		AND (rud.department_id IN ( SELECT department_id FROM r_user_department WHERE user_id = #{condition.userId} ) OR
		t.created_user_id = #{condition.userId}) )t
		where 1 = 1
		<if test="condition.type != null and condition.type != ''">
			and t.type = #{condition.type}
		</if>
	</select>
	<select id="getWithLockCount" resultType="java.lang.Integer">
		select count(1) from (select
		t.application_time as time,
		t.updated_time AS refundTime,
		t.id AS business_id,
		'withLock' AS type,
		p.purchase_name AS projectName,
		p.purchase_number AS projectNum,
		t.project_id,
		min( pbs.id ) section_id,
		group_concat( DISTINCT pbs.package_number ) AS package_number,
		pbs.bid_round,
		p.base_project_id,
		p.re_tender,
		p.package_segment_status AS packageSegmentStatus,
		p.bid_type_name AS bidType,
		1 as countNum
		FROM
		t_lock_application t
		LEFT JOIN t_project p ON t.project_id = p.id
		LEFT JOIN t_project_bid_section pbs ON p.id = pbs.project_id
		AND pbs.id = t.package_id
		LEFT JOIN t_project_entrust_user tpe ON pbs.project_id = tpe.project_id
		LEFT JOIN r_user_department rud on tpe.user_id = rud.user_id  and tpe.department_id = rud.department_id
		WHERE
		t.is_delete = 0
		AND t.lock_status = 5
		and p.is_delete = 0
		AND pbs.is_delete = 0
		AND tpe.is_delete = 0
		AND tpe.type = 1
		AND rud.is_delete = 0
		<if test="condition.keyWords != null and condition.keyWords != ''">
			and (
			p.purchase_number like concat('%',#{condition.keyWords},'%')
			or
			p.purchase_name like concat('%',#{condition.keyWords},'%')
			)
		</if>
		${dataScope}
		GROUP BY
		t.application_time,
		t.id,
		t.updated_time,
		p.purchase_name,
		p.purchase_number,
		t.project_id,
		pbs.bid_round,
		p.base_project_id,
		p.re_tender,
		p.package_segment_status,
		p.bid_type_name)t
		where 1 = 1
		<if test="condition.type != null and condition.type != ''">
			and t.type = #{condition.type}
		</if>
	</select>
	<select id="getSpecialTicketChangeCount" resultType="java.lang.Integer">
		select count(1) from (select
		t.updated_time as time,
		t2.created_time AS refundTime,
		'specialTicketChange' AS type,
		p.purchase_name AS projectName,
		p.purchase_number AS projectNum,
		t.project_id,
		min( pbs.id ) section_id,
		group_concat( DISTINCT pbs.package_number ) AS package_number,
		pbs.bid_round,
		p.base_project_id,
		p.re_tender,
		p.package_segment_status AS packageSegmentStatus,
		p.bid_type_name AS bidType,
		1 as countNum
		FROM
		t_pay_all t
		LEFT JOIN (
		SELECT
		*
		FROM
		t_calibration_process_record
		WHERE
		id IN ( SELECT max( c.id ) AS id FROM t_calibration_process_record c WHERE c.business_code = 'CHANGE_TICKET' GROUP BY c.business_id )
		) t2 ON t.id = t2.business_id
		LEFT JOIN t_project p ON t.project_id = p.id
		LEFT JOIN t_project_bid_section pbs ON p.id = pbs.project_id
		AND find_in_set(pbs.id,t.section_ids)
		LEFT JOIN t_project_entrust_user tpe ON pbs.project_id = tpe.project_id
		LEFT JOIN r_user_department rud on tpe.user_id = rud.user_id  and tpe.department_id = rud.department_id
		WHERE
		t.is_delete = 0
		AND t2.is_delete = 0
		AND t2.operation = '退回'
		and p.is_delete = 0
		AND pbs.is_delete = 0
		AND tpe.is_delete = 0
		AND tpe.type = 1
		AND rud.is_delete = 0
		<if test="condition.keyWords != null and condition.keyWords != ''">
			and (
			p.purchase_number like concat('%',#{condition.keyWords},'%')
			or
			p.purchase_name like concat('%',#{condition.keyWords},'%')
			)
		</if>
		${dataScope}
		GROUP BY
		t.updated_time,
		t2.created_time,
		p.purchase_name,
		p.purchase_number,
		t.project_id,
		pbs.bid_round,
		p.base_project_id,
		p.re_tender,
		p.package_segment_status,
		p.bid_type_name)t
		where 1 = 1
		<if test="condition.type != null and condition.type != ''">
			and t.type = #{condition.type}
		</if>
	</select>
	<select id="getTenderFeesApplyCount" resultType="java.lang.Integer">
		select count(1) from (select
		t.updated_time as time,
		t2.created_time AS refundTime,
		'tenderFeesApply' AS type,
		p.purchase_name AS projectName,
		p.purchase_number AS projectNum,
		t.project_id,
		min( pbs.id ) section_id,
		group_concat( DISTINCT pbs.package_number ) AS package_number,
		pbs.bid_round,
		p.base_project_id,
		p.re_tender,
		p.package_segment_status AS packageSegmentStatus,
		p.bid_type_name AS bidType,
		1 as countNum
		FROM
		t_pay_all t
		LEFT JOIN (
		SELECT
		*
		FROM
		t_calibration_process_record
		WHERE
		id IN ( SELECT max( c.id ) AS id FROM t_calibration_process_record c WHERE c.business_code = 'BSF_REFUND' GROUP BY c.business_id )
		) t2 ON t.id = t2.business_id
		LEFT JOIN t_project p ON t.project_id = p.id
		LEFT JOIN t_project_bid_section pbs ON p.id = pbs.project_id
		AND find_in_set( pbs.id, t.section_ids )
		LEFT JOIN t_project_entrust_user tpe ON pbs.project_id = tpe.project_id
		LEFT JOIN r_user_department rud on tpe.user_id = rud.user_id  and tpe.department_id = rud.department_id
		WHERE
		t.is_delete = 0
		AND t2.is_delete = 0
		AND t2.operation = '退回'
		and p.is_delete = 0
		AND pbs.is_delete = 0
		AND tpe.is_delete = 0
		AND tpe.type = 1
		AND rud.is_delete = 0
		<if test="condition.keyWords != null and condition.keyWords != ''">
			and (
			p.purchase_number like concat('%',#{condition.keyWords},'%')
			or
			p.purchase_name like concat('%',#{condition.keyWords},'%')
			)
		</if>
		${dataScope}
		GROUP BY
		t.updated_time,
		t2.created_time,
		p.purchase_name,
		p.purchase_number,
		t.project_id,
		pbs.bid_round,
		p.base_project_id,
		p.re_tender,
		p.package_segment_status,
		p.bid_type_name) t
		where 1 = 1
		<if test="condition.type != null and condition.type != ''">
			and t.type = #{condition.type}
		</if>
	</select>
	<select id="getRegisteredSuppliersCount" resultType="java.lang.Integer">
		select count(1) from (select
		t.created_time as time,
		t.updated_time AS refundTime,
		'registeredSuppliers' AS type,
		p.purchase_name AS projectName,
		p.purchase_number AS projectNum,
		t.project_id,
		min( pbs.id ) section_id,
		group_concat( DISTINCT pbs.package_number ) AS package_number,
		pbs.bid_round,
		p.base_project_id,
		p.re_tender,
		p.package_segment_status AS packageSegmentStatus,
		p.bid_type_name AS bidType,
		1 as countNum
		FROM
		t_supplier_register_apply t
		LEFT JOIN t_project p ON t.project_id = p.id
		LEFT JOIN t_project_bid_section pbs ON p.id = pbs.project_id
		AND find_in_set(pbs.id,t.sub_ids)
		LEFT JOIN t_project_entrust_user tpe ON pbs.project_id = tpe.project_id
		LEFT JOIN r_user_department rud on tpe.user_id = rud.user_id  and tpe.department_id = rud.department_id
		WHERE
		t.is_delete = 0
		AND t.`status` = 5
		and p.is_delete = 0
		AND pbs.is_delete = 0
		AND tpe.is_delete = 0
		AND tpe.type = 1
		AND rud.is_delete = 0
		<if test="condition.keyWords != null and condition.keyWords != ''">
			and (
			p.purchase_number like concat('%',#{condition.keyWords},'%')
			or
			p.purchase_name like concat('%',#{condition.keyWords},'%')
			)
		</if>
		${dataScope}
		GROUP BY
		t.created_time,
		t.updated_time,
		p.purchase_name,
		p.purchase_number,
		t.project_id,
		pbs.bid_round,
		p.base_project_id,
		p.re_tender,
		p.package_segment_status,
		p.bid_type_name)t
		where 1 = 1
		<if test="condition.type != null and condition.type != ''">
			and t.type = #{condition.type}
		</if>
	</select>
	<select id="getRerecordPaymentCount" resultType="java.lang.Integer">
		select count(1) from (select
		t.created_time as time,
		t.updated_time AS refundTime,
		'rerecordPayment' AS type,
		p.purchase_name AS projectName,
		p.purchase_number AS projectNum,
		t1.project_id,
		min( pbs.id ) section_id,
		group_concat( DISTINCT pbs.package_number ) AS package_number,
		pbs.bid_round,
		p.base_project_id,
		p.re_tender,
		p.package_segment_status AS packageSegmentStatus,
		p.bid_type_name AS bidType,
		1 as countNum
		FROM
		t_apply_pay_file t
		LEFT JOIN t_apply_info t1 ON t.apply_id = t1.apply_id
		LEFT JOIN t_project p ON t1.project_id = p.id
		LEFT JOIN t_project_bid_section pbs ON p.id = pbs.project_id
		AND pbs.id = t1.sub_id
		LEFT JOIN t_project_entrust_user tpe ON pbs.project_id = tpe.project_id
		LEFT JOIN r_user_department rud on tpe.user_id = rud.user_id  and tpe.department_id = rud.department_id
		WHERE
		t.is_delete = 0
		AND t1.is_delete = 0
		AND t.`status` = 3
		and p.is_delete = 0
		AND pbs.is_delete = 0
		AND tpe.is_delete = 0
		AND tpe.type = 1
		AND rud.is_delete = 0
		<if test="condition.keyWords != null and condition.keyWords != ''">
			and (
			p.purchase_number like concat('%',#{condition.keyWords},'%')
			or
			p.purchase_name like concat('%',#{condition.keyWords},'%')
			)
		</if>
		${dataScope}
		GROUP BY
		t.created_time,
		t.updated_time,
		p.purchase_name,
		p.purchase_number,
		t1.project_id,
		pbs.bid_round,
		p.base_project_id,
		p.re_tender,
		p.package_segment_status,
		p.bid_type_name)t
		where 1 = 1
		<if test="condition.type != null and condition.type != ''">
			and t.type = #{condition.type}
		</if>
	</select>
	<select id="getAgencyServiceCount" resultType="java.lang.Integer">
		select count(1) from (select
		t.created_time as time,
		t.updated_time AS refundTime,
		'agencyService' AS type,
		p.purchase_name AS projectName,
		p.purchase_number AS projectNum,
		t.project_id,
		min( pbs.id ) section_id,
		group_concat( DISTINCT pbs.package_number ) AS package_number,
		pbs.bid_round,
		p.base_project_id,
		p.re_tender,
		p.package_segment_status AS packageSegmentStatus,
		p.bid_type_name AS bidType,
		1 as countNum
		FROM
		t_agency_fee_apply t
		LEFT JOIN t_project p ON t.project_id = p.id
		LEFT JOIN t_project_bid_section pbs ON p.id = pbs.project_id
		AND pbs.id = t.section_id
		LEFT JOIN t_project_entrust_user tpe ON pbs.project_id = tpe.project_id
		LEFT JOIN r_user_department rud on tpe.user_id = rud.user_id  and tpe.department_id = rud.department_id
		WHERE
		t.is_delete = 0
		AND t.`status` = 5
		and p.is_delete = 0
		AND pbs.is_delete = 0
		AND tpe.is_delete = 0
		AND tpe.type = 1
		AND rud.is_delete = 0
		<if test="condition.keyWords != null and condition.keyWords != ''">
			and (
			p.purchase_number like concat('%',#{condition.keyWords},'%')
			or
			p.purchase_name like concat('%',#{condition.keyWords},'%')
			)
		</if>
		${dataScope}
		GROUP BY
		t.created_time,
		t.updated_time,
		p.purchase_name,
		p.purchase_number,
		t.project_id,
		pbs.bid_round,
		p.base_project_id,
		p.re_tender,
		p.package_segment_status,
		p.bid_type_name) t
		where 1 = 1
		<if test="condition.type != null and condition.type != ''">
			and t.type = #{condition.type}
		</if>
	</select>
	<select id="getDataModificationCount" resultType="java.lang.Integer">
		select count(1) from (SELECT
		t.created_time AS time,
		t.updated_time AS refundTime,
		'dataModification' AS type,
		CONCAT(t.type,',',t.content) content,
		t.id business_id,
		1 as countNum
		FROM
		t_data_update_record t
		LEFT JOIN r_user_department rud on  t.created_user_id = rud.user_id
		WHERE
		t.is_delete = 0
		AND t.`status` = 3
		AND rud.is_delete = 0
		AND (rud.department_id IN ( SELECT department_id FROM r_user_department WHERE user_id = #{condition.userId} ) OR
		t.created_user_id = #{condition.userId}) ) t
		where 1 = 1
		<if test="condition.type != null and condition.type != ''">
			and t.type = #{condition.type}
		</if>
	</select>
	<select id="getDepositRefundCount" resultType="java.lang.Integer">
		select count(1) from (select
		t.created_time as time,
		t.updated_time AS refundTime,
		'depositRefund' AS type,
		p.purchase_name AS projectName,
		p.purchase_number AS projectNum,
		t.project_id,
		min( pbs.id ) section_id,
		group_concat( DISTINCT pbs.package_number ) AS package_number,
		pbs.bid_round,
		p.base_project_id,
		p.re_tender,
		p.package_segment_status AS packageSegmentStatus,
		p.bid_type_name AS bidType,
		1 as countNum
		FROM
		t_bond t
		LEFT JOIN t_project p ON t.project_id = p.id
		LEFT JOIN t_project_bid_section pbs ON p.id = pbs.project_id
		AND pbs.id = t.section_id
		LEFT JOIN t_project_entrust_user tpe ON pbs.project_id = tpe.project_id
		LEFT JOIN r_user_department rud on tpe.user_id = rud.user_id  and tpe.department_id = rud.department_id
		WHERE
		t.is_delete = 0
		AND t.`status` = 2
		and p.is_delete = 0
		AND pbs.is_delete = 0
		AND tpe.is_delete = 0
		AND tpe.type = 1
		AND rud.is_delete = 0
		<if test="condition.keyWords != null and condition.keyWords != ''">
			and (
			p.purchase_number like concat('%',#{condition.keyWords},'%')
			or
			p.purchase_name like concat('%',#{condition.keyWords},'%')
			)
		</if>
		${dataScope}
		GROUP BY
		t.created_time,
		t.updated_time,
		p.purchase_name,
		p.purchase_number,
		t.project_id,
		pbs.bid_round,
		p.base_project_id,
		p.re_tender,
		p.package_segment_status,
		p.bid_type_name) t
		where 1 = 1
		<if test="condition.type != null and condition.type != ''">
			and t.type = #{condition.type}
		</if>
	</select>
	<select id="getExpertExtractionCount" resultType="java.lang.Integer">
		select count(1) from (select
		t.id businessId,
		t.created_time as time,
		t.updated_time AS refundTime,
		'expertExtraction' AS type,
		p.purchase_name AS projectName,
		p.purchase_number AS projectNum,
		t1.project_id,
		min( pbs.id ) section_id,
		group_concat( DISTINCT pbs.package_number ) AS package_number,
		pbs.bid_round,
		p.base_project_id,
		p.re_tender,
		p.package_segment_status AS packageSegmentStatus,
		p.bid_type_name AS bidType,
		1 as countNum
		FROM
		t_expert_extraction t
		LEFT JOIN t_expert_extraction_project t1 ON t1.extration_id = t.id
		LEFT JOIN t_project p ON t1.project_id = p.id
		LEFT JOIN t_project_bid_section pbs ON p.id = pbs.project_id
		AND pbs.id = t1.section_id
		LEFT JOIN t_project_entrust_user tpe ON pbs.project_id = tpe.project_id
		LEFT JOIN r_user_department rud on tpe.user_id = rud.user_id  and tpe.department_id = rud.department_id
		WHERE
		t.is_delete = 0
		AND t.extract_status = 4
		AND t1.is_delete = 0
		and p.is_delete = 0
		AND pbs.is_delete = 0
		AND tpe.is_delete = 0
		AND tpe.type = 1
		AND rud.is_delete = 0
		<if test="condition.keyWords != null and condition.keyWords != ''">
			and (
			p.purchase_number like concat('%',#{condition.keyWords},'%')
			or
			p.purchase_name like concat('%',#{condition.keyWords},'%')
			)
		</if>
		${dataScope}
		GROUP BY
		t.id,
		t.created_time,
		t.updated_time,
		p.purchase_name,
		p.purchase_number,
		t1.project_id,
		pbs.bid_round,
		p.base_project_id,
		p.re_tender,
		p.package_segment_status,
		p.bid_type_name) t
		where 1 = 1
		<if test="condition.type != null and condition.type != ''">
			and t.type = #{condition.type}
		</if>
	</select>
	<select id="getExpertsSelectedRecordCount" resultType="java.lang.Integer">
		select count(1) from (select
		t.created_time as time,
		t.updated_time AS refundTime,
		'expertsSelectedRecord' AS type,
		p.purchase_name AS projectName,
		p.purchase_number AS projectNum,
		t2.project_id,
		min( pbs.id ) section_id,
		group_concat( DISTINCT pbs.package_number ) AS package_number,
		pbs.bid_round,
		p.base_project_id,
		p.re_tender,
		p.package_segment_status AS packageSegmentStatus,
		p.bid_type_name AS bidType,
		1 as countNum
		FROM
		t_expert_extraction_beian t
		LEFT JOIN t_expert_extraction t1 ON t1.id = t.expert_extraction_id
		LEFT JOIN t_expert_extraction_project t2 ON t2.extration_id = t1.id
		LEFT JOIN t_project p ON t2.project_id = p.id
		LEFT JOIN (SELECT max(id) id,expert_extraction_id FROM t_expert_extraction_beian t GROUP BY expert_extraction_id) t3 ON FIND_IN_SET(t.id,t3.id)
		LEFT JOIN t_project_bid_section pbs ON p.id = pbs.project_id
		AND pbs.id = t2.section_id
		LEFT JOIN t_project_entrust_user tpe ON pbs.project_id = tpe.project_id
		LEFT JOIN r_user_department rud on tpe.user_id = rud.user_id  and tpe.department_id = rud.department_id
		WHERE
		t.is_delete = 0
		AND t.`status` = 2
		AND FIND_IN_SET(t.id,t3.id)
		AND t1.is_delete = 0
		AND t2.is_delete = 0
		and p.is_delete = 0
		AND pbs.is_delete = 0
		AND tpe.is_delete = 0
		AND tpe.type = 1
		AND rud.is_delete = 0
		<if test="condition.keyWords != null and condition.keyWords != ''">
			and (
			p.purchase_number like concat('%',#{condition.keyWords},'%')
			or
			p.purchase_name like concat('%',#{condition.keyWords},'%')
			)
		</if>
		${dataScope}
		GROUP BY
		t.created_time,
		t.updated_time,
		p.purchase_name,
		p.purchase_number,
		t2.project_id,
		pbs.bid_round,
		p.base_project_id,
		p.re_tender,
		p.package_segment_status,
		p.bid_type_name) t
		where 1 = 1
		<if test="condition.type != null and condition.type != ''">
			and t.type = #{condition.type}
		</if>
	</select>


	<select id="selectReturnList" resultType="com.hzw.sunflower.controller.project.response.WaitListVo">
		SELECT DISTINCT
			pi.id AS pendingId,
			pi.apply_time AS time,
			pi.operation_time AS refundTime,
			pi.business_code AS type,
			pi.business_id AS businessId,
			p.purchase_name AS projectName,
			p.purchase_number AS projectNum,
			pi.project_id,
			min( pbs.id ) AS section_id,
			group_concat( DISTINCT pbs.package_number ) AS package_number,
			pi.bid_round,
			p.base_project_id,
			p.re_tender,
			p.package_segment_status AS packageSegmentStatus,
			p.bid_type_name AS bidType,
			1 AS countNum
		FROM
			t_pending_item pi
			LEFT JOIN
			(
			select * from t_project  where id in (
				select
				distinct
				v.project_id
				from v_user_edit_project v
				where 1 = 1   ${dataScope}
			)
			) p
			ON pi.project_id = p.id
			LEFT JOIN t_project_bid_section pbs ON p.id = pbs.project_id
		WHERE
			pi.is_delete = 0
		  	AND p.is_delete = 0
		  	AND pbs.is_delete = 0
			AND pi.business_type = 2
			AND find_in_set( pbs.id, pi.section_id )
			AND pi.business_code != 'dataModification' AND pi.business_code != 'materialApplication'
		<if test="condition.type != null and condition.type != ''">
			AND pi.business_code = #{condition.type}
		</if>
		<if test="condition.keyWords != null and condition.keyWords != ''">
			AND (
			p.purchase_number LIKE concat('%',#{condition.keyWords},'%')
			OR
			p.purchase_name LIKE concat('%',#{condition.keyWords},'%')
			)
		</if>
		GROUP BY
			pi.id,
			pi.apply_time,
			pi.operation_time,
			pi.business_code,
			pi.business_id,
			p.purchase_name,
			p.purchase_number,
			pi.project_id,
			pi.bid_round,
			p.base_project_id,
			p.re_tender,
			p.package_segment_status,
			p.bid_type_name
		ORDER BY pi.operation_time DESC
	</select>
	<select id="getReturnMaterial" resultType="com.hzw.sunflower.controller.project.response.WaitListVo">
		SELECT
			DISTINCT
			pi.apply_time AS time,
			pi.operation_time AS refundTime,
			pi.business_id AS `business_id`,
			pi.business_code AS type,
			1 as countNum
		FROM
			t_material_apply t
			left join v_user_edit_refund pi
			ON t.id = pi.business_id
		WHERE
			t.is_delete = 0
			AND t.apply_status = 5
			AND pi.business_code = 'materialApplication'
			${dataScope}
	</select>
	<select id="getReturnData" resultType="com.hzw.sunflower.controller.project.response.WaitListVo">
		SELECT DISTINCT
			pi.id AS pendingId,
			pi.apply_time AS time,
			pi.operation_time AS refundTime,
			pi.business_code AS type,
			CONCAT( t.type, ',', t.content ) content,
			t.id business_id,
			1 as countNum
		FROM
			t_data_update_record t
			left join v_user_edit_refund pi
			ON t.id = pi.business_id
		WHERE
			t.is_delete = 0
		  	AND pi.business_code = 'dataModification'
			${dataScope}
	</select>

	<select id="selectReturnListCount" resultType="java.lang.Integer">
		SELECT
			COUNT(1)
		FROM (
				SELECT DISTINCT
					pi.id AS pendingId
				FROM
					t_pending_item pi
					LEFT JOIN (select * from t_project  where id in (select distinct v.project_id from v_user_edit_project v where 1 = 1   ${dataScope})) p ON pi.project_id = p.id
				WHERE
					pi.is_delete = 0
					AND p.is_delete = 0
					AND pi.business_type = 2
					AND pi.business_code != 'dataModification' AND pi.business_code != 'materialApplication'
					<if test="condition.type != null and condition.type != ''">
						AND pi.business_code = #{condition.type}
					</if>
					<if test="condition.keyWords != null and condition.keyWords != ''">
						AND (
						p.purchase_number LIKE concat('%',#{condition.keyWords},'%')
						OR
						p.purchase_name LIKE concat('%',#{condition.keyWords},'%')
						)
					</if>
					GROUP BY pi.id
				) T
	</select>
	<select id="getReturnMaterialCount" resultType="java.lang.Integer">
		SELECT
			COUNT(1)
		FROM (
				 SELECT DISTINCT
					 pi.business_id AS `business_id`
				 FROM
					 t_material_apply t
					 left join v_user_edit_refund pi ON t.id = pi.business_id
				 WHERE
					 t.is_delete = 0
				   	 AND t.apply_status = 5
				     AND pi.business_code = 'materialApplication'
					 ${dataScope}
				 GROUP BY pi.business_id
				) T
	</select>
	<select id="getReturnDataCount" resultType="java.lang.Integer">
		SELECT
			COUNT(1)
		FROM (
				 SELECT DISTINCT
					 pi.id AS pendingId
				 FROM
					 t_data_update_record t
					 left join v_user_edit_refund pi ON t.id = pi.business_id
				 WHERE
					 t.is_delete = 0
				   	 AND pi.business_code = 'dataModification'
					 ${dataScope}
				 GROUP BY pi.id
			 ) T
	</select>
	<select id="getSealReturnData" resultType="com.hzw.sunflower.controller.project.response.WaitListVo">
		SELECT DISTINCT
		pi.id AS pendingId,
		pi.apply_time AS time,
		pi.operation_time AS refundTime,
		pi.business_code AS type,
		t.item_describe content,
		t.id business_id,
		1 as countNum,
		if(count(distinct t1.num)=1,group_concat(distinct t1.project_number),'-') projectNum,
		if(count(distinct t1.num)=1,group_concat(distinct t1.project_name),group_concat(distinct concat(t1.project_number,'-',t1.project_name))) projectName
		FROM
		t_seal_application t
		left join (select
					t2.apply_id,
					t2.project_number,
					t2.project_name,
					count(distinct 1) num
				from
					t_seal_application_project t2
					where t2.is_delete = 0
			group by t2.apply_id,t2.project_name,t2.project_number) t1 on t1.apply_id = t.id
			left join v_user_edit_refund pi ON t.id = pi.business_id
		WHERE
			t.is_delete = 0
			AND pi.business_code = 'sealApplicationReturn'
			${dataScope}
		group by
			pi.id ,
			pi.apply_time,
			pi.operation_time,
			pi.business_code ,
			t.item_describe,
			t.id,
			t1.num
	</select>
	<select id="getSealReturnCount" resultType="java.lang.Integer">
		SELECT
			COUNT(1)
		FROM (
			SELECT DISTINCT
			pi.id AS pendingId
			FROM
			t_seal_application t
			left join v_user_edit_refund pi ON t.id = pi.business_id
			WHERE
			t.is_delete = 0
			AND pi.business_code = 'sealApplicationReturn'
			${dataScope}
			GROUP BY pi.id
		) T
	</select>


</mapper>
