package com.hzw.sunflower.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@ApiModel("行业分布历史表")
@TableName("t_project_industry_distribution")
@Data
public class ProjectIndustryDistribution implements Serializable {

    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("项目id")
    private Long projectId;

    @ApiModelProperty("项目编号")
    private String projectNumber;

    @ApiModelProperty("项目名称")
    private String projectName;

    @ApiModelProperty("委托单位")
    private String principalCompany;

    @ApiModelProperty("项目管理员")
    private String userName;

    @ApiModelProperty("包号")
    private Integer packageNumber;

    @ApiModelProperty("一级分类编码")
    private String firstLevelCode;

    @ApiModelProperty("一级分类名称")
    private String firstLevelName;

    @ApiModelProperty("二级分类编码")
    private String secondLevelCode;

    @ApiModelProperty("二级分类编码")
    private String secondLevelName;

    @ApiModelProperty("关联字段")
    private Long expertSpecialtyId;

    @ApiModelProperty("委托金额")
    private BigDecimal entrustMoney;

    @ApiModelProperty("币种")
    private Integer entrustCurrency;

    @ApiModelProperty("汇率")
    private BigDecimal exchangeRate;

    @ApiModelProperty("委托金额(人民币)")
    private BigDecimal entrustMoneyOfYuan;

    @ApiModelProperty("开标时间")
    private Date submitEndTime;

    @ApiModelProperty("城市编码")
    private Integer cityCode;

    @ApiModelProperty("城市名称")
    private String cityName;

    @ApiModelProperty("部门编码")
    private Integer departmentId;

    @ApiModelProperty("部门名称")
    private String departmentName;
}
