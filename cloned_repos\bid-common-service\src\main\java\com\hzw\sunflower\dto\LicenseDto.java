package com.hzw.sunflower.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @datetime 2023/02/20 10:58
 * @description: license传输实体类
 * @version: 1.0
 */
@Data
public class LicenseDto implements java.io.Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 企业名称
     */
    @ApiModelProperty(value = "企业名称")
    private String companyName;

    /**
     * 版本 正式版 试用版
     */
    @ApiModelProperty(value = "版本")
    private String version;

    /**
     * 功能 基础版 专业版 旗舰版
     */
    @ApiModelProperty(value = "功能")
    private String function;

    /**
     * 组织机构代码
     */
    @ApiModelProperty(value = "组织机构代码")
    private String organizationNum;
    /**
     * 系统有效期开始
     */
    @ApiModelProperty(value = "系统有效期开始时间")
    private String issuedDate;
    /**
     * 系统有效期
     */
    @ApiModelProperty(value = "系统有效期结束时间")
    private String expireDate;

    /**
     * 身份 1.采购人 2.代理机构
     */
    @ApiModelProperty(value = "身份")
    private Integer identity;

    /**
     * 剩余天数
     */
    @ApiModelProperty(value = "剩余天数")
    private String leftTime;

    /**
     * 提示信息
     */
    @ApiModelProperty(value = "提示信息")
    private String toast;

    /**
     * macAddr信息
     */
    @ApiModelProperty(value = "macAddr信息")
    private String macAddr;



}
