///***
/// * 自定义时间折线图
/// */

import 'package:flutter/material.dart';
import 'package:charts_flutter/flutter.dart' as charts;

import 'package:flutter_main_app/src/components/charts/CustomLegendSymbolRenderer.dart' show DiscSymbolRenderer;

class CustomTimeSeries<PERSON>hart extends StatelessWidget {

  CustomTimeSeriesChart({
    this.seriesList,
    this.animate = true,
  });

  /// * 数据列表
  final List<charts.Series<dynamic, DateTime>> seriesList;

  /// * 是否启用动画
  final bool animate;

  /// * 轴线文本样式
  static const charts.TextStyleSpec labelStyle = charts.TextStyleSpec(
    fontSize: 10,
    // color: charts.MaterialPalette.gray.shadeDefault,
    color: charts.Color(a: 255, r: 0x41, g: 0x41, b: 0x41),
  );

  /// * 分隔线样式
  static const charts.LineStyleSpec lineStyle = charts.LineStyleSpec(
    color: charts.Color(a: 255, r: 0xde, g: 0xde, b: 0xde),
    thickness: 1,
  );

  /// * 纵轴分隔线数
  static const int tickCount = 6;

  @override
  Widget build(BuildContext context) {
    return charts.TimeSeriesChart(
      seriesList,
      animate: animate,
      /// * 渲染节点
      defaultRenderer: charts.LineRendererConfig(
        /// TODO: 把点改成空心，暂时没思路
        symbolRenderer: DiscSymbolRenderer(),
        includePoints: true,
        radiusPx: 2.5,
      ),
      /// * 主纵轴样式
      primaryMeasureAxis: charts.NumericAxisSpec(
        renderSpec: charts.GridlineRendererSpec(
          labelStyle: labelStyle,
          lineStyle: lineStyle,
        ),
        tickProviderSpec: charts.BasicNumericTickProviderSpec(
          desiredTickCount: tickCount,
        ),
      ),
      /// * 横轴样式
      domainAxis: charts.DateTimeAxisSpec(
        showAxisLine: true,
        renderSpec: charts.SmallTickRendererSpec(
          labelStyle: labelStyle,
          lineStyle: lineStyle,
        ),
        tickFormatterSpec: charts.AutoDateTimeTickFormatterSpec(
          month: charts.TimeFormatterSpec(
            format: 'M月',
            transitionFormat: 'M月',
          ),
          day: charts.TimeFormatterSpec(
            format: 'EEE',
            transitionFormat: 'EEE',
            noonFormat: 'EEE',
          ),
        ),
        /// * 月份间隔
        tickProviderSpec: charts.DayTickProviderSpec(
          increments: [60, 61, 61, 62, 61, 61],
        ),
      ),
      behaviors: [
        /// * 图例
        charts.SeriesLegend(
          position: charts.BehaviorPosition.top,
          outsideJustification: charts.OutsideJustification.endDrawArea,
          showMeasures: true,
          entryTextStyle: charts.TextStyleSpec(
            fontSize: 12,
            color: charts.Color(a: 255, r: 0x41, g: 0x41, b: 0x41),
          ),
        ),
      ],
    );
  }

}
