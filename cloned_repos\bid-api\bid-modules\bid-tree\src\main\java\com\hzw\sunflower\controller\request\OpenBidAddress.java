package com.hzw.sunflower.controller.request;

import com.sun.istack.NotNull;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date ：Created in 2021/7/15 16:30
 * @description：开标地点
 * @version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class OpenBidAddress {
    @NotNull
    @ApiModelProperty(value = "开标地点省", position = 1)
    private Long province;

    @NotNull
    @ApiModelProperty(value = "开标地点市", position = 2)
    private Long city;
}
