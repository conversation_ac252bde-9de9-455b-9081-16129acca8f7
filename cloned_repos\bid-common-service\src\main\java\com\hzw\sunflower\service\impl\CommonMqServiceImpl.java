package com.hzw.sunflower.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hzw.sunflower.constant.constantenum.AppMsgTypeEnum;
import com.hzw.sunflower.constant.constantenum.AppTaskTypeEnum;
import com.hzw.sunflower.dto.AppOaMsgDto;
import com.hzw.sunflower.dto.AppOaNoticeDto;
import com.hzw.sunflower.entity.AppToRead;
import com.hzw.sunflower.entity.PendingItem;
import com.hzw.sunflower.entity.User;
import com.hzw.sunflower.service.AppToReadService;
import com.hzw.sunflower.service.CommonMqService;
import com.hzw.sunflower.service.UserOpenService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.AmqpTemplate;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * @description:
 * @author: fanqh
 * @create: 2023-09-18 10:20
 * @Version 1.0.0
 **/
@Slf4j
@Service
public class CommonMqServiceImpl implements CommonMqService {

    @Autowired
    private AmqpTemplate rabbitTemplate;

    @Autowired
    private AppToReadService appToReadService;

    @Autowired
    private UserOpenService userOpenService;

    /**
     * 发送待办消息
     * 标题：JSTCC
     * 待办内容：您有一条xxxx待审批
     * 待阅内容：您有一条xxxx待查看
     * @param dto
     */
    @Override
    public void sendOaMsg(AppOaMsgDto dto) {
        try {
            List<String> recipientList = new ArrayList<>();
            for (String recipient : dto.getRecipientList()) {
                LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper();
                queryWrapper.and(wq -> wq.eq(User::getId, recipient).or().eq(User::getOtherUserId, recipient));
                queryWrapper.last("limit 1");
                User user = userOpenService.getOne(queryWrapper);
                if (user != null) {
                    recipientList.add(user.getId().toString());
                }
            }
            dto.setRecipientList(recipientList);
            dto.setMsgType(AppMsgTypeEnum.TO_DO.getType());
            dto.setTitle("JSTCC");
            dto.setContent("您有一条" + getMsgByType(dto.getTaskType()) + "待审批");
            rabbitTemplate.convertAndSend("msgPushExchange", "msgPushRouting", JSON.toJSONString(dto));
            System.out.println("发送oa待办消息成功:" + JSON.toJSONString(dto));
        } catch (Exception e) {
            e.printStackTrace();
            log.error("发送oa待办消息异常", e);
        }
    }

    private String getMsgByType(String taskType) {
        String type = "";
        if (taskType.contains("EXPERT_FEE_")) {
            type = "劳务费";
        } else {
            type = AppTaskTypeEnum.getTaskType(taskType).getMsg();
        }
        return type;
    }

    /**
     * 发送待阅
     * @param msg
     * @param pendingItem
     */
    @Override
    public void sendOaReadMsg(String msg, PendingItem pendingItem) {
        try {
            // 添加待阅信息
            AppToRead appToRead = new AppToRead();
            appToRead.setTitle("JSTCC");
            appToRead.setContent("您有一条" + msg + "待查看");
            BeanUtils.copyProperties(pendingItem, appToRead);
            appToReadService.save(appToRead);
            // 发送oa待阅
            List<String> recipientList = new ArrayList<>();
            recipientList.add(pendingItem.getApplyUserId().toString());
            AppOaMsgDto dto = new AppOaMsgDto();
            dto.setMsgType(AppMsgTypeEnum.TO_READ.getType());
            dto.setRecipientList(recipientList);
            dto.setTaskType(pendingItem.getBusinessCode());
            dto.setTitle(appToRead.getTitle());
            dto.setContent(appToRead.getContent());
            rabbitTemplate.convertAndSend("msgPushExchange", "msgPushRouting", JSON.toJSONString(dto));
            System.out.println("发送oa待阅消息成功:" + JSON.toJSONString(dto));
        } catch (Exception e) {
            e.printStackTrace();
            log.error("发送oa待阅消息异常", e);
        }
    }

    @Override
    public void sendOaNoticeMsg(List<String> recipientList,AppOaNoticeDto appNoticeDto) {
        try {
            // 发送oa通知
            AppOaMsgDto dto = new AppOaMsgDto();
            dto.setMsgType(AppMsgTypeEnum.TO_NOTICE.getType());
            dto.setRecipientList(recipientList);
            dto.setTaskType("INFORMATION_NOTICE");
            dto.setTitle(appNoticeDto.getTitle());
            dto.setContent(appNoticeDto.getContent());
            rabbitTemplate.convertAndSend("msgPushExchange", "msgPushRouting", JSON.toJSONString(dto));
            System.out.println("发送oa通知消息成功:" + JSON.toJSONString(dto));
        } catch (Exception e) {
            e.printStackTrace();
            log.error("发送oa通知消息异常", e);
        }
    }

}
