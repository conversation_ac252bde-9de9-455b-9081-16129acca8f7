package com.hzw.sunflower.constant;

public class ConfigConstants {

    /**
     * 系统全局功能配置code
     */
    public static final String SYS_SETTING = "SYS_SETTING";

    /**
     * 系统全局功能配置持续时间
     */
    public static final Integer SYS_SETTING_TIME = 5*60;

    /**
     * 系统全局功能配置code
     */
    public static final String FUNCTION_CONFIG = "FUNCTION_CONFIG";

    /**
     * 系统全局功能配置持续时间
     */
    public static final Integer FUNCTION_CONFIG_TIME = 5*60;

    /**
     * 校验方式  1.不限制 2.仅作提示 3.强校验
     */
    public static final Integer END_TIME_TYPE_NO_CHECK = 1;
    public static final Integer END_TIME_TYPE_TIP = 2;
    public static final Integer END_TIME_TYPE_CHECK = 3;

    /**
     * 时间校验单位  1.天 2.工作日
     */
    public static final Integer END_TIME_UNIT_DAY = 1;
    public static final Integer END_TIME_UNIT_WORK_DAY = 2;
    public static final String DAY = "日";
    public static final String WORK_DAY = "工作日";

}
