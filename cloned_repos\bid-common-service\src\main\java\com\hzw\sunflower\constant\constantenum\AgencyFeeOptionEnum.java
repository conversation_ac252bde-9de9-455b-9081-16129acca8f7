package com.hzw.sunflower.constant.constantenum;

/**
 * 业务操作类型
 *
 * <AUTHOR>
 */
public enum AgencyFeeOptionEnum {
    //代理服务费比例类型 1.国家纪委1980号文 2.苏招协2022002号文 3.固定比例
    PROPORTION_1980(1, "国家纪委1980号文"),
    PROPORTION_SZX2022002(2, "苏招协2022002号文"),
    PROPORTION_FIX(3, "固定比例");

    private Integer type;
    private String desc;

    AgencyFeeOptionEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
