package com.hzw.sunflower.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzw.sunflower.constant.constantenum.ProjectSourceEnum;
import com.hzw.sunflower.controller.project.response.ProjectEntrustUserVo;
import com.hzw.sunflower.util.BeanListUtil;
import com.hzw.sunflower.constant.constantenum.EntrustUserType;
import com.hzw.sunflower.controller.project.request.AgentUserReq;
import com.hzw.sunflower.controller.project.request.ProjectEntrustUserREQ;
import com.hzw.sunflower.controller.response.FileVo;
import com.hzw.sunflower.dao.ProjectEntrustUserMapper;
import com.hzw.sunflower.dto.AgentInItDto;
import com.hzw.sunflower.dto.AgentUserDTO;
import com.hzw.sunflower.dto.ContactDto;
import com.hzw.sunflower.entity.JwtUser;
import com.hzw.sunflower.entity.Project;
import com.hzw.sunflower.entity.ProjectEntrustUser;
import com.hzw.sunflower.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 项目委托关系人表 Service接口实现
 *
 * <AUTHOR>
 * @version 1.0.0 2021-04-07
 */
@Service
public class ProjectEntrustUserServiceImpl extends ServiceImpl<ProjectEntrustUserMapper, ProjectEntrustUser> implements ProjectEntrustUserService {

    @Autowired
    private ProjectService projectService;

    @Autowired
    private ProjectEntrustUserMapper projectEntrustUserMapper;

    @Autowired
    private OssFileService ossFileService;


    /**
     * 新增项目委托关系人表 信息
     *
     * @param projectEntrustUser 项目委托关系人表 信息
     * @return 是否成功
     */
    @Override
    public Boolean addProjectEntrustUser(ProjectEntrustUserREQ projectEntrustUser, JwtUser user) {
        //操作标识
        Boolean flag = false;
        List<ProjectEntrustUser> list = addUnitsAndUsers(projectEntrustUser, user);
        //执行委托项目入库
        if(null != list && list.size() > 0){
            flag = this.saveBatch(list);
        }else{
            flag = true;
        }
        if (flag) {
            //执行冗余字段更新
            projectService.updateTendererProjectRedundantFieldDealWith(projectEntrustUser.getProjectId());
        }
        return flag;
    }

    /**
     * 修改项目委托合同及封装委托关系
     *
     * @param projectEntrustUser
     * @return
     */
    private List<ProjectEntrustUser> addUnitsAndUsers(ProjectEntrustUserREQ projectEntrustUser, JwtUser user) {
        Boolean flag;
        flag = modifyProject(projectEntrustUser);
        //找出立项人
//        List<AgentUserDTO> agentUserDTOs = projectEntrustUserMapper.getEntrustUserByProjectIdAndType(projectEntrustUser.getProjectId(), 1);

        //委托关系集合
        List<ProjectEntrustUser> list = new ArrayList<ProjectEntrustUser>();
        if (flag) {
            //封装委托人用户关系
            flag = addProjectUser(projectEntrustUser, list, projectEntrustUser.getAgentUser());
//            ProjectEntrustUser agerUser = enclAgentUser(projectEntrustUser, user);
//            list.add(agerUser);
        }
        //去除上一次记录
        LambdaUpdateWrapper<ProjectEntrustUser> up = new LambdaUpdateWrapper<ProjectEntrustUser>();
        up.eq(ProjectEntrustUser::getProjectId, projectEntrustUser.getProjectId()).notIn(ProjectEntrustUser::getType,EntrustUserType.ENTRUSTED_PERSON.getType());
        // 代理机构只能删除代理机构新建的招标人
        up.eq(ProjectEntrustUser::getSource, ProjectSourceEnum.AGENCY.getType());
        this.remove(up);
        return list;
    }

    private ProjectEntrustUser enclAgentUser(ProjectEntrustUserREQ projectEntrustUser, JwtUser user) {
        ProjectEntrustUser oldEntrustUserId = projectEntrustUserMapper.getOldEntrustUserId(projectEntrustUser.getProjectId());
        ProjectEntrustUser puser = new ProjectEntrustUser();
        puser.setProjectId(projectEntrustUser.getProjectId());
//        puser.setUserId(user.getUserId());
        puser.setUserId(oldEntrustUserId.getUserId());
        puser.setType(EntrustUserType.ENTRUSTED_PERSON.getType());
        puser.setCompanyId(user.getCompanyId());
        return puser;
    }

    /**
     * 修改委托合同
     *
     * @param projectEntrustUser
     * @return
     */
    private Boolean modifyProject(ProjectEntrustUserREQ projectEntrustUser) {
        //修改项目委托合同
        Project p = new Project();
        p.setId(projectEntrustUser.getProjectId());
        p.setUploadFileId(projectEntrustUser.getUploadFileId());
        return projectService.updateProject(p);
    }

    /**
     * 新增项目时判断是否新增用户
     *
     * @param projectEntrustUser
     * @param list
     * @param agentUserReq
     */
    private Boolean addProjectUser(ProjectEntrustUserREQ projectEntrustUser, List<ProjectEntrustUser> list, List<AgentUserReq> agentUserReq) {
        for (AgentUserReq lt : agentUserReq) {
            if(ProjectSourceEnum.AGENCY.getType().equals(lt.getSource())){
                //赋值
                ProjectEntrustUser puser = new ProjectEntrustUser();
                puser.setProjectId(projectEntrustUser.getProjectId());
                puser.setUserId(lt.getUserId());
                puser.setType(EntrustUserType.PRINCIPAL.getType());
                puser.setCompanyId(projectEntrustUser.getCompanyId());
                list.add(puser);
            }
        }
        return true;
    }


    /**
     * 查询委托人
     *
     * @param id
     * @param type
     * @return
     */
    @Override
    public List<Map<String, String>> queryPrincipalByIds(Long id, Integer type) {
        return this.baseMapper.queryPrincipalByIds(id, type);
    }

    @Override
    public ProjectEntrustUser getEntrustUser(Long userId, Long projectId) {
        return projectEntrustUserMapper.getEntrustUser(userId, projectId,null);
    }

    /**
     * 查询项目是否是本级项目
     * @param deptId
     * @param projectId
     * @return
     */
    @Override
    public ProjectEntrustUser getDeptLevelProject(Long deptId, Long projectId) {
        return projectEntrustUserMapper.getEntrustUser(null, projectId,deptId);
    }

    /**
     * 根据项目id查询委托人信息
     *
     * @param id
     * @return
     */
    @Override
    public AgentInItDto getEntrustUserByProjectIdAndType(Long id, int type) {
        AgentInItDto init = new AgentInItDto();
        List<AgentUserDTO> dto = projectEntrustUserMapper.getEntrustUserByProjectIdAndType(id, type);
        if (!CollectionUtils.isEmpty(dto)) {
            AgentUserDTO userDto = dto.get(0);
            init.setCompanyId(userDto.getCompanyId());
            init.setCompanyName(userDto.getCompanyName());
            init.setUploadFileId(userDto.getUploadFileId());
            init.setUserId(userDto.getUserId());
            init.setDepartmentId(userDto.getDepartmentId());
            init.setSource(userDto.getSource());
        }
        //查询文件信息
        List<FileVo> ossFile = null;
        if (init.getUploadFileId() != null) {
            ossFile = ossFileService.getOssFileByFileIds(init.getUploadFileId());
        }
        init.setOssFile(ossFile);
        List<ContactDto> cont = BeanListUtil.convertList(dto, ContactDto.class);
        init.setContactDto(cont);
        return init;
    }
    @Override
    public void updateProjectEntrustUserById(ProjectEntrustUser projectEntrustUser){
        projectEntrustUserMapper.updateProjectEntrustUserById(projectEntrustUser.getId(),projectEntrustUser.getUserId());
    }

    /**
     * 获取项目被委托人信息
     * @param projectId
     * @return
     */
    @Override
    public ProjectEntrustUserVo selectEntrustUserAndDepartment(Long projectId) {
        return this.baseMapper.selectEntrustUserAndDepartment(projectId);
    }


}