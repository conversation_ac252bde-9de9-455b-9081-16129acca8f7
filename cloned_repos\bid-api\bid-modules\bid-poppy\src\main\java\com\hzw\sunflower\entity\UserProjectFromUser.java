package com.hzw.sunflower.entity;


import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(description = "用户授权项目表 ")
@TableName("t_user_project_from_user")
@Data
public class UserProjectFromUser {
    private Long id;

    @ApiModelProperty(value = "用户id")
    private Long userId;

    @ApiModelProperty(value = "部门id")
    private Long departId;

    @ApiModelProperty(value = "项目id")
    private Long projectId;

    @ApiModelProperty(value = "来源用户ID")
    private Long fromUserId;

    @ApiModelProperty(value = "权限码0：查看，1：编辑查看")
    private Integer rightCode;

    @ApiModelProperty(value = "授权全部项目给用户时，取消此项目授权 1 取消")
    private Integer batchSingleCancel;
}
