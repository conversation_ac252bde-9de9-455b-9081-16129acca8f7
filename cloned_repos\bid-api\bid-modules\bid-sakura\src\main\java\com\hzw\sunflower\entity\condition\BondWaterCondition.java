package com.hzw.sunflower.entity.condition;

import com.hzw.sunflower.common.BaseCondition;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2022/8/3 17:28
 * @description：收款流水列表检索条件
 * @modified By：`
 * @version: 1.0
 */
@ApiModel(description = "收款流水列表检索条件")
@Data
public class BondWaterCondition extends BaseCondition {

    @ApiModelProperty(value = "关键字")
    private String keyWords;

    @ApiModelProperty(value = "起始日期")
    private String startDate;

    @ApiModelProperty(value = "结束日期")
    private String endDate;

    @ApiModelProperty(value = "关联起始日期")
    private String relateStartDate;

    @ApiModelProperty(value = "关联结束日期")
    private String relateEndDate;

    @ApiModelProperty(value = "收款银行 全部则不传参")
    private String bankName;

    @ApiModelProperty("关联状态 1.确认关联 2.已关联 3.异常关联 4.未关联")
    List<Integer> relationStatus;

    @ApiModelProperty("是否已出回单 1.是 2.否")
    private Integer hasReceipt;

    @ApiModelProperty("导出下拉框状态 1.全部已收款流水 2.已收款未退还")
    private Integer exportType;

    @ApiModelProperty("流水标签 1收款 2其他 3退汇 4错汇 5 标书费")
    private Integer label;

    @ApiModelProperty(value = "项目编号")
    private String purchaseNumber;

    @ApiModelProperty(value = "付款户名")
    private String payCompanyName;

    @ApiModelProperty(value = "起始金额")
    private String startAmount;

    @ApiModelProperty(value = "结束金额")
    private String endAmount;

}
