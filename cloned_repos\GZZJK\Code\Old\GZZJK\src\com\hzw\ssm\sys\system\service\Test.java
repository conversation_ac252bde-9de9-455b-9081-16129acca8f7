package com.hzw.ssm.sys.system.service;

public class Test {

	public static void main(String[] args) {
		String[] roleRights = { "1", "3", "5", "8" };
		String menuId = "4";
		System.out.println(hasRights(menuId, roleRights));
	}

	public static boolean hasRights(String menuId, String[] roleRights) {
		for (int i = 0; i < roleRights.length; i++) {
			if (roleRights[i].equals(menuId)) {
				return true;// 查找到了就返回真，不在继续查询
			}
		}
		return false;
	}
}
