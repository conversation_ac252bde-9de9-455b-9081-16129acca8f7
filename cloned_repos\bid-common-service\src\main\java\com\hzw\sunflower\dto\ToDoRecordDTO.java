package com.hzw.sunflower.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class ToDoRecordDTO {
    @ApiModelProperty(value = "业务id")
    private Long businessId;
    private Long id;
    private Long applyRelationId;

    @ApiModelProperty("项目ID")
    private Long projectId;

    @ApiModelProperty(value = "招标阶段（0：默认，1：第一轮）")
    private Integer bidRound;

    @ApiModelProperty(value = "标段id")
    private String sectionId;

    @ApiModelProperty(value = "标段id")
    private String sectionIds;

    @ApiModelProperty(value = "业务管理key ,表名:id 例如 notice:123456")
    private String businessKey;

    @ApiModelProperty("待办类型")
    private String taskType;

    @ApiModelProperty(value = "发布次数")
    private Integer orderNum;

    @ApiModelProperty(value = "类别")
    private String processDefinitionKey;

    @ApiModelProperty(value = "关联标段")
    private String projectBidSections;

    @ApiModelProperty("申请类型1专家单独报销2一起报销3协评单独报销")
    private Integer expenseType;

    @ApiModelProperty(value = "启用状态")
    private Integer status;

    @ApiModelProperty("业务类型")
    private Integer type;

    private Boolean isAudit;

    @ApiModelProperty("企业名称")
    private String companyName;

    @ApiModelProperty("用户名称")
    private String userName;

    @ApiModelProperty("项目集合信息")
    private List<Object> projectVOList;

    @ApiModelProperty("项目名称")
    private String projectName;

    @ApiModelProperty("项目编号")
    private String projectNum;

    @ApiModelProperty("标段id集合")
    private String packageNumber;

}
