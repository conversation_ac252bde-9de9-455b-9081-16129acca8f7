const mammoth = require('mammoth');

async function convertDocxToHtml(path) {
  try {
    const options = {
      styleMap: [
        "p[style-name='Heading 1'] => h1:fresh",
        "p[style-name='Heading 2'] => h2:fresh"
      ]
    };
    const result = await mammoth.convertToHtml({ path: path },options);
    const html = result.value;
    const processedHtml = addPageBreaks(html); // 添加自定义的分页处理
    return processedHtml
  } catch (err) {
    console.error('Error converting file:', err);
    throw err;
  }
}

function addPageBreaks(html) {
  // 在这里添加自定义的分页逻辑,全局替换
  // <w:sectPr><w:pgBr/></w:sectPr>
  return (html.replace(/<\/a>/g, '</div><w:sectPr><w:pgBr/></w:sectPr>')
    .replace(/<table>/g, '<table border width="100%" cellspacing="0" cellpadding="10">')
    .replace(/<td>/g, '<td style="padding:10px 20px">')
    .replace(/<td /g, '<td style="padding:10px 20px" ')
    .replace(/<h1>/g, '<h1 style="text-align:center">')
    .replace(/<h2>/g, '<h2 style="text-align:center">')
    .replace(/<a /g, '<div ')
  );
}

module.exports = {
    convertDocxToHtml,
};
