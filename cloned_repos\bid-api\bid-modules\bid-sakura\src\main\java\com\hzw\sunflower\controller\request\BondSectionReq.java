package com.hzw.sunflower.controller.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @datetime 2022/09/15 11:11
 * @description:
 * @version: 1.0
 */
@ApiModel(description = "新增关联标段入参")
@Data
public class BondSectionReq implements Serializable {

    @ApiModelProperty(value = "项目id")
    private Long projectId;

    @ApiModelProperty(value = "标段id")
    private Long sectionId;

    @ApiModelProperty(value = "金额")
    private BigDecimal amount;
}
