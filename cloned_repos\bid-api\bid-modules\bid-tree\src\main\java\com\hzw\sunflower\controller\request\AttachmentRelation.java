package com.hzw.sunflower.controller.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date ：Created in 2021/7/7 14:21
 * @description：附件信息
 * @version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AttachmentRelation implements Serializable {

    @ApiModelProperty(value = "附件ID", position = 1)
    private long attachmentId;

    @ApiModelProperty(value = "项目ID", position = 2)
    private Long projectId;

    @ApiModelProperty(value = "标段ID", position = 3)
    private Long sectionId;

    @ApiModelProperty(value = "文件ID", position = 4)
    private Long fileId;
}
