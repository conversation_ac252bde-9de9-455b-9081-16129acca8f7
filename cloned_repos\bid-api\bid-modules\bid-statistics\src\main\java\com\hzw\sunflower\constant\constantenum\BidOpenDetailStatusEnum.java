package com.hzw.sunflower.constant.constantenum;

public enum BidOpenDetailStatusEnum {
    //标段状态 1.全部 2.已开标(包含异常前已开标) 3.未开标 4.异常
    ALL("1", "全部"),
    IS_OPENED("2", "已开标(包含异常前已开标)"),
    ONT_OPEN("3", "未开标"),
    ABNORMA("4", "异常");


    private String type;
    private String desc;

    BidOpenDetailStatusEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
