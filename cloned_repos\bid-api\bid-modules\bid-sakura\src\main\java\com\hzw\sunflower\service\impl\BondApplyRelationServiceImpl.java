package com.hzw.sunflower.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzw.sunflower.controller.response.AbnormalApplyRelationVo;
import com.hzw.sunflower.controller.response.BondApplyRelationVo;
import com.hzw.sunflower.dao.BondApplyRelationMapper;
import com.hzw.sunflower.entity.BondApplyRelation;
import com.hzw.sunflower.entity.JwtUser;
import com.hzw.sunflower.entity.condition.BondApplyRelationCondition;
import com.hzw.sunflower.entity.condition.BondProjectCondition;
import com.hzw.sunflower.service.BondApplyRelationService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 保证金申请关联表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-27
 */
@Service
public class BondApplyRelationServiceImpl extends ServiceImpl<BondApplyRelationMapper, BondApplyRelation> implements BondApplyRelationService {

    /**
     * 异常关联申请列表
     * @param condition
     * @return
     */
    @Override
    public IPage<AbnormalApplyRelationVo> applyRelationList(BondProjectCondition condition) {
        Page<AbnormalApplyRelationVo> page = condition.buildPage();
        return this.baseMapper.applyRelationList(page,condition);
    }

    /**
     * 分页查询保证金关联申请列表(包含异常关联)
     * @param condition
     * @return
     */
    @Override
    public IPage<BondApplyRelationVo> getBondApplyRelationPage(BondApplyRelationCondition condition, JwtUser jwtUser) {
        //存入项目经理id
        condition.setUserId(jwtUser.getUserId());
        condition.setDepartId(jwtUser.getUser().getDepartId());
        Page<BondApplyRelation> page = condition.buildPage();
        return this.baseMapper.getBondApplyRelationPage(page,condition);
    }
}
