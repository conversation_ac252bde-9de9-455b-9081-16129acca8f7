package com.hzw.ssm.applets.dao;

import com.hzw.ssm.applets.entity.TitleInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021-02-02 16:19
 * @Version 1.0
 */
public interface TitleInfoMapper {

    int insertTitleInfo(TitleInfo titleInfo);

    int updateTitleInfo(TitleInfo titleInfo);

    TitleInfo getTitleInfo(TitleInfo titleInfo);

    int deleteTitleInfo(TitleInfo titleInfo);

    List<TitleInfo> getTitleInfos(TitleInfo titleInfo);

    TitleInfo queryTitleInfo(TitleInfo info);

    int queryTitleInfoNumById(@Param("expertId") String expertId);

    List<TitleInfo> queryTitleInfoList(@Param("expertId") String expertId);

    void deletePictureById(@Param("id") String fileId);
}
