package com.hzw.sunflower.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.constant.constantenum.CompanyEnum;
import com.hzw.sunflower.constant.constantenum.DataPermissionEnum;
import com.hzw.sunflower.controller.project.request.AgentUserProPermREQ;
import com.hzw.sunflower.controller.project.request.UserProjectFromUserREQ;
import com.hzw.sunflower.dao.MenuMapper;
import com.hzw.sunflower.dao.UserDatapermUserMapper;
import com.hzw.sunflower.dao.UserProjectFromUserMapper;
import com.hzw.sunflower.datascope.utils.DataScopeUtil;
import com.hzw.sunflower.dto.MyProjectListDTO;
import com.hzw.sunflower.dto.SingleProjectPermUserDTO;
import com.hzw.sunflower.dto.UserDepartDTO;
import com.hzw.sunflower.dto.UserProjectFromUserDTO;
import com.hzw.sunflower.entity.*;
import com.hzw.sunflower.security.utils.SecurityUtils;
import com.hzw.sunflower.service.ProjectEntrustUserService;
import com.hzw.sunflower.service.RoleService;
import com.hzw.sunflower.service.UserProjectFromUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


@Service
public class UserProjectFromUserServiceImpl extends ServiceImpl<UserProjectFromUserMapper, UserProjectFromUser> implements UserProjectFromUserService {

    @Resource
    private UserProjectFromUserMapper userProjectFromUserMapper;
    @Resource
    private UserDatapermUserMapper userDatapermUserMapper;

    @Value("${organ.organization_type:}")
    private Integer organizationType;

    @Autowired
    private MenuMapper menuMapper;

    @Autowired
    private ProjectEntrustUserService projectEntrustUserService;

    @Autowired
    private RoleService roleService;

    @Override
    public Result<Object> addUsers(SingleProjectPermUserDTO singleProjectPermUserDTO) {
        // 用户项目权限
        Long fromUserId=SecurityUtils.getJwtUser().getUserId();
        Long fromUserDepartId = SecurityUtils.getJwtUser().getUser().getDepartId();
        // 准备数据
        List<UserProjectFromUser> addProList=new ArrayList<>();
        List<Long> userIdList = new ArrayList<>();
        List<Long> deptIdList = new ArrayList<>();
        // 循环用户
        for (UserDepartDTO userDepart : singleProjectPermUserDTO.getUserDeparts()) {
            userIdList.add(userDepart.getUserId());
            deptIdList.add(userDepart.getDepartId());
            UserProjectFromUser userProjectFromUser=new UserProjectFromUser();
            userProjectFromUser.setProjectId(singleProjectPermUserDTO.getProjectId());
            userProjectFromUser.setFromUserId(fromUserId);
            userProjectFromUser.setDepartId(userDepart.getDepartId());
            userProjectFromUser.setUserId(userDepart.getUserId());
            // 不能授权给自己
            if (fromUserId.equals(userDepart.getUserId()) && fromUserDepartId.equals(userDepart.getDepartId())){
                continue;
            }
            userProjectFromUser.setRightCode(singleProjectPermUserDTO.getRightCode());

            UserDatapermUser userDatapermUser = userDatapermUserMapper.selectOne(new LambdaQueryWrapper<UserDatapermUser>()
                    .eq(UserDatapermUser::getFromUserId, fromUserId)
                    .eq(UserDatapermUser::getUserId, userDepart.getUserId())
                    .eq(UserDatapermUser::getDepartId,userDepart.getDepartId())
            );
            // 判断是否已授权了全部项目 0 我的全部项目 1 选择项目
            // 之前已授权 1选择项目
            if (userDatapermUser!=null&&userDatapermUser.getDataScope().intValue()==1){
                addProList.add(userProjectFromUser);
            }
            // 之前已授权 0 我的全部项目
            if (userDatapermUser!=null&&userDatapermUser.getDataScope().intValue()==0){
                addProList.add(userProjectFromUser);
            }

            // 之前未进行授权
            if (userDatapermUser==null){
                addProList.add(userProjectFromUser);
            }else{

            }

        }
        // 删除之前的用户授权
        if (singleProjectPermUserDTO.getUserDeparts()!=null&&singleProjectPermUserDTO.getUserDeparts().size()>0){

            userProjectFromUserMapper.delete(
                    new LambdaQueryWrapper<UserProjectFromUser>()
                            .eq(UserProjectFromUser::getProjectId, singleProjectPermUserDTO.getProjectId())
                            .eq(UserProjectFromUser::getFromUserId, fromUserId)
                            .in(UserProjectFromUser::getUserId,userIdList)
                            .in(UserProjectFromUser::getDepartId,deptIdList)
            );
        }
        // 批量加入用户授权
        if (addProList!=null&&addProList.size()>0){
            saveBatch(addProList);
        }

        return Result.ok();
    }

    @Override
    public IPage<UserProjectFromUserDTO> selectUserListByProjectId(UserProjectFromUserREQ req) {

        Page<UserProjectFromUserDTO> page=new Page<>();
        page.setCurrent(req.getPage());
        page.setSize(req.getPageSize());
        IPage<UserProjectFromUserDTO> fromUserDTOList = userProjectFromUserMapper.selectListByProjectIdAndFromUserId(page,req.getProjectId() , SecurityUtils.getJwtUser().getUserId());
        return fromUserDTOList;
    }

    @Override
    public Result<Object> delUsers(SingleProjectPermUserDTO singleProjectPermUserDTO) {
        Long fromUserId = SecurityUtils.getJwtUser().getUserId();
        List<Long> deptIdList = singleProjectPermUserDTO.getUserDeparts().stream().map(UserDepartDTO::getDepartId).collect(Collectors.toList());
        List<Long> userIdList = singleProjectPermUserDTO.getUserDeparts().stream().map(UserDepartDTO::getUserId).collect(Collectors.toList());
        if(!singleProjectPermUserDTO.getUserDeparts().isEmpty()) {
            userProjectFromUserMapper.delete(
                    new LambdaQueryWrapper<UserProjectFromUser>()
                            .eq(UserProjectFromUser::getProjectId, singleProjectPermUserDTO.getProjectId())
                            .eq(UserProjectFromUser::getFromUserId, fromUserId)
                            .in(UserProjectFromUser::getUserId,userIdList)
                            .in(UserProjectFromUser::getDepartId,deptIdList)
            );
        }
        //
        List<UserProjectFromUser> addlist=new ArrayList<>();
        Map<Long,Long> mapUIds=new HashMap<>();
        for (UserDepartDTO userDepart : singleProjectPermUserDTO.getUserDeparts()) {
            UserDatapermUser userDatapermUser = userDatapermUserMapper.selectOne(new LambdaQueryWrapper<UserDatapermUser>()
                    .eq(UserDatapermUser::getFromUserId, fromUserId)
                    .eq(UserDatapermUser::getUserId, userDepart.getUserId())
                    .eq(UserDatapermUser::getDepartId,userDepart.getDepartId())

            );
            if (userDatapermUser!=null){
                // 选择项目
                if (userDatapermUser.getDataScope().intValue()==0){
                    // 全部项目 删除某个项目
                    UserProjectFromUser userProjectFromUser =new UserProjectFromUser();
                    userProjectFromUser.setFromUserId(fromUserId);
                    userProjectFromUser.setDepartId(userDepart.getDepartId());
                    userProjectFromUser.setUserId(userDepart.getUserId());
                    userProjectFromUser.setBatchSingleCancel(1);
                    userProjectFromUser.setRightCode(singleProjectPermUserDTO.getRightCode());
                    userProjectFromUser.setProjectId(singleProjectPermUserDTO.getProjectId());
                   if (mapUIds.get(userDepart.getUserId())!=null){
                        // doing something

                   }else {
                       addlist.add(userProjectFromUser);
                       mapUIds.put(userDepart.getUserId(),userDepart.getUserId());
                   }


                }else{
                }
            }
        }
        saveBatch(addlist);

        return Result.ok();
    }

    @Override
    public List<MyProjectListDTO> selectListWithPermByProjectIdAndUserId(AgentUserProPermREQ req) {
        // 处理数据权限
        String dataScopeFilterSql= DataScopeUtil.getDataScopeFilterDepartSql("tpe","tpe");
        Integer userIdentity = SecurityUtils.getJwtUser().getUserIdentity();
        //默认一开始为代理结构
        Integer type = 1;
        //判断用户身份，为招标人时候userIdentity为1，为代理结构时候userIdentity为2
        //如果租户是代理机构，招标人查询项目type为0（委托人）
        if (userIdentity==1 && organizationType.equals(CompanyEnum.AGENCY.getType())){
            type = 0;
        }else{
            type = 1;
        }

        return userProjectFromUserMapper.selectListWithPermByProjectIdAndUserId(dataScopeFilterSql,req.getProjectIds(),req.getUserId(),type,SecurityUtils.getJwtUser().getUser().getDepartId());
    }

    @Override
    public MyProjectListDTO selectOneWithPermByProjectIdAndUserId(AgentUserProPermREQ req) {
        List<MyProjectListDTO> myProjectListDTOS = selectListWithPermByProjectIdAndUserId(req);
        MyProjectListDTO _one=null;
        for (MyProjectListDTO myProjectListDTO : myProjectListDTOS) {
            _one=myProjectListDTO;
            if (_one.getRightCode().intValue()==1){
                break;
            }
        }
        return _one;
    }

    @Override
    public Map<Long, MyProjectListDTO> selectListMapWithPermByProjectIdsAndUserId(List<Long> projectIds,Long userId) {
        AgentUserProPermREQ agentUserProPermREQ=new AgentUserProPermREQ();
        agentUserProPermREQ.setProjectIds(projectIds);
        agentUserProPermREQ.setUserId(userId);
        Map<Long, MyProjectListDTO> reMap=new HashMap<>();
        List<MyProjectListDTO> myProjectListDTOS = selectListWithPermByProjectIdAndUserId(agentUserProPermREQ);
        for (MyProjectListDTO myProjectListDTO : myProjectListDTOS) {
            reMap.put(myProjectListDTO.getId(),myProjectListDTO);
        }

        return reMap;
    }

    /**
     * 查询项目是否有编辑权限
     * @param projectId
     * @return
     */
    @Override
    public Boolean getProjectPermissions(Long projectId) {
        Boolean isEdit = false;
        Integer dataPerission = null;
        JwtUser jwtUser = SecurityUtils.getJwtUser();
        List<Role> roles = jwtUser.getUser().getRoles();
        List<Long> roleIds = roles.stream().map(Role::getId).collect(Collectors.toList());
        //查看该项目是属于个人，处长，自定义、全公司，还是授权
        ProjectEntrustUser entrustUser = null;

        //判断是否是全部权限
        long isAll = roles.stream().filter(r -> DataPermissionEnum.ALL.getType().equals(r.getDataPermission())).count();
        if(isAll>0){
            //全部数据权限
            dataPerission = DataPermissionEnum.ALL.getType();
            isEdit = hasEditPerission(roleIds,dataPerission,jwtUser);
        }

        //先判断自定义
        long isCustom = roles.stream().filter(r -> DataPermissionEnum.CUSTOM.getType().equals(r.getDataPermission())).count();
        if(isCustom>0){
            //自定义数据权限
            dataPerission = DataPermissionEnum.CUSTOM.getType();
            isEdit = hasEditPerission(roleIds,dataPerission,jwtUser);
        }

        if(!isEdit){
            //判断项目是否属于本级创建
            entrustUser = projectEntrustUserService.getDeptLevelProject(jwtUser.getUser().getDepartId(), projectId);
            if(entrustUser != null){
                //查询本级的角色是否存在级别数据权限
                dataPerission = DataPermissionEnum.DEPARTMENT.getType();
                isEdit = hasEditPerission(roleIds,dataPerission,jwtUser);
            }
        }

        if(!isEdit){
            //判断是否属于个人创建
            entrustUser = projectEntrustUserService.getEntrustUser(jwtUser.getUserId(), projectId);
            if(entrustUser != null){
                //查询个人的角色是否存在个人数据权限
                dataPerission = DataPermissionEnum.PERSON.getType();
                isEdit = hasEditPerission(roleIds,dataPerission,jwtUser);
            }
        }

        if(!isEdit) {
            //查询授权项目权限是否可以编辑
            Integer hasEmpowerEditCount = this.baseMapper.getEmpowerProjectFoeEdit(jwtUser.getUserId(), projectId, jwtUser.getUser().getDepartId());
            if (hasEmpowerEditCount > 0) {
                isEdit = true;
            }
        }
        return  isEdit;
    }


    /**
     * 是否有编辑权限
     * @param roleIds
     * @param dataPerission
     * @param jwtUser
     * @return
     */
    private Boolean hasEditPerission(List<Long> roleIds,Integer dataPerission,JwtUser jwtUser){
        Boolean isEdit = false;
        LambdaQueryWrapper<Role> roleQuery = new LambdaQueryWrapper<>();
        roleQuery.in(Role::getId,roleIds)
                .eq(Role::getDataPermission,dataPerission);
        List<Role> dataPermissionRole = roleService.list(roleQuery);
        if(!dataPermissionRole.isEmpty()){
            //查询是否有编辑按钮
            List<Long> dataPermissionIds = dataPermissionRole.stream().map(Role::getId).collect(Collectors.toList());
            // 查询菜单权限
            List<String> permissions = menuMapper.getPermissionByUser(jwtUser.getUserId(),jwtUser.getUserIdentity(),dataPermissionIds);
            long count = permissions.stream().filter(p ->("/permission/isEdit").equals(p)).count();
            if(count>0){
                return true;
            }
        }
        return  false;
    }
}

