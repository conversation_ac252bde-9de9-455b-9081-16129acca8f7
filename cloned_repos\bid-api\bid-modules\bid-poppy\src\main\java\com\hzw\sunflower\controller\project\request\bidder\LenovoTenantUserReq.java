package com.hzw.sunflower.controller.project.request.bidder;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(description = "当前租户用户信息联想")
@Data
public class LenovoTenantUserReq {

    @ApiModelProperty(value = "租户公司统一社会信用代码")
    private String organizationNum;

    @ApiModelProperty(value = "租户类型 1招标人 2代理机构")
    private Integer organizationType;

    @ApiModelProperty(value = "公司id")
    private Long companyId;

    @ApiModelProperty(value = "公司名称")
    private String companyName;

    @ApiModelProperty(value = "部门id")
    private Long departmentId;

    @ApiModelProperty(value = "部门名称")
    private String departmentName;

    @ApiModelProperty(value = "用户名称")
    private String userName;

    @ApiModelProperty(value = "搜索级别， 1公司 2部门 3人员")
    private Integer type;

}
