package com.hzw.sunflower.controller.response;

import com.baomidou.mybatisplus.annotation.TableName;
import com.hzw.sunflower.common.BaseBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date ：Created in 2022/8/31
 * @description：短信记录表
 * @modified By：`
 * @version: 1.0
 */
@ApiModel(description = "短信回复表")
@TableName("t_sms_reply")
@Data
public class SmsReplyVo{

    private static final long serialVersionUID = 2304155224328814668L;

    @ApiModelProperty(value = "主键", position = 1)
    private Long id;

    @ApiModelProperty(value = "短信平台流水号", position = 2)
    private String msgId;

    @ApiModelProperty(value = "回复手机号", position = 3)
    private String replyMobile;

    @ApiModelProperty(value = "回复时间", position = 4)
    private String replyTime;

    @ApiModelProperty(value = "回复内容", position = 5)
    private String replyContent;

}
