///***
/// * common Refresh gif animation view
/// *
/// * <AUTHOR>
/// * @date 20101024
/// */

import "package:flutter/material.dart";

import 'package:flutter_main_app/src/utils/GifAnimation.dart';

class DefaultRefreshLoadingWidget extends StatefulWidget {
  const DefaultRefreshLoadingWidget({Key key}) : super(key: key);

  @override
  _DefaultRefreshLoadingWidgetState createState() => _DefaultRefreshLoadingWidgetState();
}

class _DefaultRefreshLoadingWidgetState extends State<DefaultRefreshLoadingWidget> with SingleTickerProviderStateMixin {

  static const String _loadingTip = '正在更新';

  GifController _animationCtrl;

  Widget _animateWidget;

  bool _isShowPlaceHoldImage = true;

  @override
  void initState() {
    super.initState();

    _animationCtrl = GifController(
      vsync: this,
      duration: Duration(milliseconds: 1200),
      frameCount: 35,
      startAnimationCallback: () {
        setState(() {
          _isShowPlaceHoldImage = false;
        });
      },
    );
  }

  @override
  void dispose() {
    _animationCtrl.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    this._animateWidget = _buildGif();

    return Container(
      constraints: BoxConstraints(minHeight: 4, maxHeight: 20),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: <Widget>[
          Visibility(
            child: Image.asset('assets/images/waitting.png'),
            visible: _isShowPlaceHoldImage,
          ),
          this._animateWidget,
          Text(_loadingTip),
        ],
      ),
    );
  }

  ///***
  /// * build loading widget rotating infinitely
  /// *
  /// * @return {Widget}
  /// */
  Widget _buildGif() {
    Widget ret = GifAnimation(
      image: AssetImage('assets/images/waitting.gif'),
      controller: _animationCtrl,
    );

    _animationCtrl.runAni();
    _animationCtrl.repeat();

    return ret;
  }

}
