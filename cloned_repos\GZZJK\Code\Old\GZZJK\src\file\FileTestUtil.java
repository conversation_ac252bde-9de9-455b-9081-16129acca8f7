/*
 * 公司名称：江苏华招网信息技术有限公司
 * 版权信息：江苏华招网信息技术有限公司版权所有
 * 文件名称：FileUtil.java
 * 修改时间：2018年8月31日
 * 修改人：朱加健
 * 修改内容：
 * 跟踪单号：
 * 修改单号 ：
 */

package file;

import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;

/**
 * <一句话功能简述> TODO
 * <功能详细描述>
 * <AUTHOR>
 * @version V1.0[产品/模块版本]
 */
public class FileTestUtil {
    /**
     *文件夹拷贝
     * @throws CopyWrongException 
     */
    public static void copyFolder(String fromPath, String toPath) throws Exception {
        // 具体实现如下
        if (fromPath != null) {
            File sf = new File(fromPath);
            if (sf.exists()) {
                //获取目录中子文件
                File[] files = sf.listFiles();
                String from = null;
                String to = null;
                //把目录中的子文件复制到目标文件
                for (File f : files) {
                    if (f.isDirectory()) { 
                        from = fromPath + "/" + f.getName();
                        to = toPath + "/" + f.getName();
                        //创建一个新的文件夹
                        creatDirectory(to);
                        //递归
                        copyFolder(from, to);
                    } else if (f.isFile()) {
                        //创建一个新文件
                        //creatFile(toPath + "/" + f.getName());
                        File file = new File(toPath + "/" + f.getName());
                        if(!file.exists()){
                            //调用copyFile()方法把指定目录文件拷贝到目标文件
                            copyFile(fromPath + "/" + f.getName(), toPath + "/" + f.getName());
                          //  System.out.println("====>" + toPath + "/" + f.getName() + "<====");
                        }
                    }
                }
            } else {
                System.err.println("文件不存在！");
                throw new Exception();
            }
        }
        System.out.println("文件合并成功！");
    }

    //创建一个目录文件
    public static void creatDirectory(String dir) {
        File file = new File(dir);
        if(!file.exists()){
            file.mkdir();
        }
    }

    //创建一个文件
    public static void creatFile(String filepath) throws Exception {
        try {
            File file = new File(filepath);
            if(!file.exists()){
                file.createNewFile();
            }else{
                file = new File(filepath+".bak");
                file.createNewFile();
            }
            
        } catch (IOException e) {
            throw new Exception();
        }
    }

    //拷贝文件内容到新文件中
    public static void copyFile(String fromPath, String toPath) throws Exception {
        //创建输入输出流
        BufferedInputStream bis = null;
        BufferedOutputStream bos = null;
        try {
            //初始化
            bis = new BufferedInputStream(new FileInputStream(fromPath));
            bos = new BufferedOutputStream(new FileOutputStream(toPath));
            //创建变量保存读取到的值
            int len = -1;
            //设置缓存大小,1K
            byte[] bytes = new byte[1024];
            //2.循环读取
            while (true) {
                //读取一个缓存大小
                len = bis.read(bytes);
                //判断文件是否读取完毕
                if (len == -1) {
                    break;
                }
                //写入目标文件
                bos.write(bytes, 0, len);
            }
            
        } catch (IOException e) {
            throw new Exception();
        }finally{
            //关闭输入输出流
            if(bis != null){
                try {
                    bis.close();
                } catch (IOException e) {
                    throw new Exception();
                }
            }
            if(bos != null){
                try {
                    bos.close();
                } catch (IOException e) {
                    throw new Exception();
                }
            }
        }
    }

    public static void main(String[] args){
        try {
            copyFolder("E:\\Files\\GZZJK\\Upload", "E:\\apache-tomcat-6.0.48-GZZJK\\webapps\\GZZJK");
        }
        catch (Exception e) {
            e.printStackTrace();
        }
    }
}

