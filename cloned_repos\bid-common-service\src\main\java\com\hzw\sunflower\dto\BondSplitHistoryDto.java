package com.hzw.sunflower.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.List;

/**
 * <p>
 * 保证金拆分历史记录
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-27
 */
@Data
@ApiModel(value = "BondWater对象", description = "保证金流水表")
public class BondSplitHistoryDto {

    @ApiModelProperty("交易流水号")
    private Long waterId;

    @ApiModelProperty("流水拆分信息")
    private List<BondSplitDto> bondSplitDtoList;

}
