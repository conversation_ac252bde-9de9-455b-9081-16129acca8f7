package com.hzw.sunflower.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@EqualsAndHashCode
public class DocChangeRecordDTO implements Serializable {

    @ApiModelProperty(value = "标段ID", position = 1)
    private Long sectionId;

    @ApiModelProperty(value = "变更类型", position = 2)
    private String changeType;

    @ApiModelProperty(value = "提交时间", position = 3)
    private Date submitTime;

    @ApiModelProperty(value = "包号", position = 4)
    private Integer packageNumber;

    @ApiModelProperty(value = "标段名称", position = 5)
    private String packageName;

    @ApiModelProperty(value = "标书费类型 0不收取 1按套 2按标段/包", position = 6)
    private Integer tenderFeeType;

    @ApiModelProperty(value = "标书费金额", position = 7)
    private BigDecimal tenderFee;

    @ApiModelProperty(value = "保证金类型 0不收取 1定额 2比例", position = 8)
    private Integer bondType;

    @ApiModelProperty(value = "保证金金额", position = 9)
    private BigDecimal bond;

    @ApiModelProperty(value = "保证金金额比例", position = 10)
    private BigDecimal bondPercent;

    @ApiModelProperty(value = "文件发售方式(0:电子文件线上下载, 1:纸质文件不支持邮寄)", position = 11)
    private Integer releaseFileType;

    @ApiModelProperty(value = "文件发售审核方式(0:无需审核, 1:审核无需材料, 2:审核上传材料)", position = 12)
    private Integer reviewFileType;

    @ApiModelProperty(value = "材料清单", position = 13)
    private String materialList;

    @ApiModelProperty(value = "招标文件发售开始时间", position = 14)
    private Date saleStartTime;

    @ApiModelProperty(value = "招标文件发售结束时间类型（1：确定时间; 2.另行通知; 3：投标文件递交截止时间前一天）", position = 15)
    private Integer saleEndTimeType;

    @ApiModelProperty(value = "招标文件发售结束时间", position = 16)
    private Date saleEndTime;

    @ApiModelProperty(value = "投标文件递交截止日期", position = 17)
    private Date submitEndTime;

    @ApiModelProperty(value = "标书费支付方式 0：线上 1：线下", position = 18)
    private Integer paymentType;

    @ApiModelProperty(value = "联系人", position = 19)
    private String contact;

    @ApiModelProperty(value = "联系电话", position = 20)
    private String phone;

    @ApiModelProperty(value = "关联招标附件oss表ID集合", position = 21)
    private String annexOssFileIds;

    @ApiModelProperty(value = "招标文件oss文件ID", position = 22)
    private Long ossFileId;

    @ApiModelProperty(value = "招标文件key", position = 23)
    private String ossFileKey;

    @ApiModelProperty(value = "招标文件名称", position = 24)
    private String ossFileName;

    @ApiModelProperty(value = "开标地点", position = 25)
    private String openBidAddress;

    @ApiModelProperty(value = "招标文件售价说明（1：交通；2：水利；3：其他）", position = 26)
    private Integer filePriceState;

    @ApiModelProperty(value = "按套信息", position = 27)
    private String groupInfo;

    @ApiModelProperty(value = "标段委托金额", position = 28)
    private BigDecimal entrustMoney;

    @ApiModelProperty(value = "投标文件递交截止日期类型（1：确定时间; 2.另行通知）", position = 29)
    private Integer submitEndTimeType;

    @ApiModelProperty(value = "招标文件表ID", position = 30)
    private Long docId;

    @ApiModelProperty(value = "关联标段(冗余字段)", position = 31)
    private String relevancePackageNumber;

    @ApiModelProperty(value = "文件提交次数", position = 32)
    private Integer submitNumber;

    @ApiModelProperty(value = "标段/包提交次数", position = 33)
    private Integer bidSubmitNumber;

    @ApiModelProperty(value = "招标文件进度", position = 34)
    private Integer noticeProgress;

    @ApiModelProperty(value = "包件状态（0-无包，1-有包）", position = 35)
    private Integer packageSegmentStatus;

    @ApiModelProperty(value = "标书费是否收费1 收费 ，2免费", position = 36)
    private Integer tenderFeeFlag;

    @ApiModelProperty(value = "1-预审公开 2-后审公开 3-邀请 4-二阶段 5-政府公开 6-政府邀请", position = 37)
    private Integer formerPurchaseStatus;

    @ApiModelProperty(value = "已购买招标文件的投标人本次是否收费", position = 38)
    private Integer free;

    @ApiModelProperty(value = "线上澄清（1 支持 2 不支持）")
    private Integer clarifyOnLine;

    @ApiModelProperty(value ="提出澄清截止时间")
    private Date clarifyEndTime;

    @ApiModelProperty(value ="提出澄清截止时间（1：确定时间; 2：投标文件递交截止时间前一天）")
    private Integer clarifyEndTimeType;

    @ApiModelProperty(value = "线上异议（1 支持 2 不支持）")
    private Integer questionOnLine;

    @ApiModelProperty(value ="提出异议截止时间（1：确定时间; 2：投标文件递交截止时间前一天）")
    private Integer questionEndTimeType;

    @ApiModelProperty(value ="提出异议截止时间")
    private Date questionEndTime;

    @ApiModelProperty(value = "线上开标（1 支持 2 不支持）")
    private Integer bidOpenOnline;

    @ApiModelProperty(value ="解密时间")
    private Integer decryptTime;

    @ApiModelProperty(value ="开标条件")
    private Integer supNumEndBid;

    @ApiModelProperty(value ="关联招标开标一览表oss表ID集合")
    private String scheduleOssFileIds;

    @ApiModelProperty(value ="文件售价说明其他")
    private String otherPriceDescribe;


}
