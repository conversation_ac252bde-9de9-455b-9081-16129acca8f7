package com.hzw.ssm.log.dao;

import com.hzw.ssm.log.entity.WebServiceLogEntity;


/**
 * web service日志服务接口
 * <一句话功能简述> TODO
 * <功能详细描述>
 * <AUTHOR>
 * @version V1.0[产品/模块版本]
 */
public interface WebServiceLogMapper {

	/**
	 * 日志保存
	 * 函数功能描述：TODO
	 * @param entity
	 */
	public void insertLog(WebServiceLogEntity entity);
	
	/**
	 * 更新响应日志
	 * 函数功能描述：TODO
	 * @param entity
	 */
	public void updateLog(WebServiceLogEntity entity);

}
