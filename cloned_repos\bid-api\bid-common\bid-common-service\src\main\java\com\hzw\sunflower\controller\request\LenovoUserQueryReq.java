package com.hzw.sunflower.controller.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date ：Created in 2021/4/16 17:03
 * @description：联系功能req
 * @modified By：`
 * @version: 1.0
 */
@ApiModel(description = "委托联系人")
@Data
public class LenovoUserQueryReq {

    @ApiModelProperty(value = "委托单位id", position = 1)
    private Long companyId;

    @ApiModelProperty(value = "委托单位名称", position = 2)
    private String companyName;

    @ApiModelProperty(value = "委托用户名称", position = 3)
    private String userName;

    @ApiModelProperty(value = "委托用户id", position = 4)
    private Long userId;

    @ApiModelProperty(value = "委托用户联系方式", position = 5)
    private String userPhone;

    @ApiModelProperty(value = "是否根据自身企业性质进行过滤(默认值：true过滤)", position = 6)
    private Boolean filterBySelfCompanyType = true;
}
