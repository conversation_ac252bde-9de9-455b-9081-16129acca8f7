<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hzw.sunflower.dao.ProjectAnalysisMapper" >

    <resultMap id="BaseResultMap" type="com.hzw.sunflower.entity.ProjectAnalysis">
        <result column="engineering_class" property="engineeringClass" />
        <result column="projectIndustry" property="projectIndustry" />
        <result column="projectCount" property="projectCount" />
        <result column="entrustMoney" property="entrustMoney" />
        <result column="proportion" property="proportion" />
    </resultMap>

    <resultMap id="BaseResultMapArea" type="com.hzw.sunflower.entity.ProjectAnalysisArea">
        <result column="city_code" property="cityCode" />
        <result column="city_name" property="city" />
        <result column="projectCount" property="projectCount" />
        <result column="entrustMoney" property="entrustMoney" />
        <result column="proportion" property="proportion" />
    </resultMap>

    <resultMap id="projectInfo" type="com.hzw.sunflower.entity.ProjectInfo">
        <result column="entrust_money" property="entrustedAmount" />
        <result column="entrust_currency" property="entrustedCurrency" />
        <result column="exchange_rate" property="exchangeRate" />
        <result column="department_id" property="departmentId" />
        <result column="department_name" property="departmentName" />
        <result column="submit_end_time" property="projectDate" />
    </resultMap>



    <!-- 二级项目分组统计 -->
    <select id="queryProjectAnalysisList" resultMap="BaseResultMap">
        SELECT engineering_class, count(*) AS projectCount, sum( entrust_money ) AS entrustMoney FROM t_project_bid_section WHERE DATE_FORMAT( submit_end_time, '%Y-%m' ) &gt;= #{startDate}
        AND DATE_FORMAT( submit_end_time, '%Y-%m' ) &lt;= #{endDate}
        GROUP BY
        engineering_class
        ORDER BY
        engineering_class
    </select>

    <select id="queryProjectBidSectionList" resultType="com.hzw.sunflower.entity.ProjectBidSection">
        SELECT * FROM t_project_bid_section WHERE DATE_FORMAT( submit_end_time, '%Y-%m' ) &gt;= #{startDate}
        AND DATE_FORMAT( submit_end_time, '%Y-%m' ) &lt;= #{endDate} AND is_delete = 0
    </select>

    <select id="queryExpertSpecialtyBySectionId" resultType="com.hzw.sunflower.entity.SectionExpertSpecialty">
        select * from t_section_expert_specialty where section_id = #{id}
    </select>



    <select id="querySecondClassByThree" resultType="com.hzw.sunflower.entity.Dictionary">
        select * from t_dictionary where id = (select parent_id from t_dictionary where id = #{id})
    </select>
    <select id="querySecondClassByFour" resultType="com.hzw.sunflower.entity.Dictionary">
        select * from t_dictionary where id = (select parent_id from t_dictionary where id = (select parent_id from t_dictionary where id = #{id}))
    </select>
    <select id="queryFirstClass" resultType="com.hzw.sunflower.entity.Dictionary">
        select * from t_dictionary where id = #{id}
    </select>

    <select id="queryByAreaGroup" resultMap="BaseResultMapArea">
        SELECT
        bid_address_city,
        bid_address_name,
        count(*) AS projectCount,
        sum( entrust_money ) AS entrustMoney
        FROM
        t_project_bid_section
        WHERE
        DATE_FORMAT( submit_end_time, '%Y-%m' ) &gt;= #{startDate}
        AND DATE_FORMAT( submit_end_time, '%Y-%m' ) &lt;= #{endDate} AND is_delete = 0
        GROUP BY
        bid_address_city,
        bid_address_name
        ORDER BY
        bid_address_city
    </select>





    <select id="selectProjectIndustryListForUpdate"  resultType="com.hzw.sunflower.controller.response.ProjectIndustryDistributionVo">
        SELECT
        bs.project_id,
        tp.purchase_number AS project_number,
        tp.project_name,
        bs.package_number,
        tp.principal_company,
        tu.user_name,
        bs.entrust_money,
        bs.entrust_currency,
        bs.submit_end_time,
        rud.department_id,
        td.department_name,
        bs.bid_address_city AS city_code,
        bs.bid_address_name AS city_name,
        es.expert_specialty_id,
        dic.`value` AS exchangeRate,
        dic2.CODE AS classCode
        FROM
        t_project_bid_section bs
        LEFT JOIN t_project tp ON bs.project_id = tp.id
        LEFT JOIN t_project_entrust_user eu ON bs.project_id = eu.project_id
        and eu.type = 1 AND eu.is_delete = 0
        LEFT JOIN r_user_department  rud ON eu.user_id = rud.user_id and eu.department_id = rud.department_id
        AND rud.is_delete = 0
        LEFT JOIN t_user tu ON rud.user_id = tu.id
        AND tu.user_name IS NOT NULL AND tu.is_delete = 0
        LEFT JOIN t_department td ON td.id = rud.department_id and td.is_delete = 0
        LEFT JOIN t_section_expert_specialty es ON bs.id = es.section_id
        LEFT JOIN t_dictionary dic ON bs.entrust_currency = dic.id and dic.is_delete = 0
        LEFT JOIN t_dictionary dic2 ON es.expert_specialty_id = dic2.id AND dic2.CODE IS NOT NULL and dic2.is_delete = 0
        WHERE
        bs.is_delete = 0
        AND bs.is_delete = 0
        and bs.bid_round = 2
        AND bs.submit_end_time IS NOT NULL
        AND ((bs.`status` &gt;= 30 AND bs.`status` &lt;= 60) or (bs.status &gt;= 70 and bs.former_abnormal_status &gt;= 30 and bs.former_abnormal_status &lt; 70 ))
        <if test="nowParameter !=null and nowParameter !=''">
            AND DATE_FORMAT( bs.submit_end_time, '%Y-%m' ) = #{nowParameter}
        </if>
    </select>

    <!-- 统计历史表所有项目 -->
    <select id="queryAll" resultMap="BaseResultMap">
        SELECT
        count(*) AS projectCount,
        sum( entrust_money_of_yuan ) AS entrustMoney
        FROM
        (
        SELECT
        first_level_name,
        second_level_code,
        second_level_name,
        entrust_money_of_yuan
        FROM
        t_project_industry_distribution
        WHERE
        DATE_FORMAT( submit_end_time, '%Y-%m' ) &gt;= #{startDate}
        AND DATE_FORMAT( submit_end_time, '%Y-%m' ) &lt;= #{endDate}
        ) AS a
    </select>

    <select id="queryAllForArea" resultMap="BaseResultMap">
        SELECT
        count(project_id) AS projectCount,
        sum( entrust_money_of_yuan ) AS entrustMoney
        FROM
        (
        SELECT
        project_id,
        project_number,
        package_number,
        first_level_code,
        first_level_name,
        second_level_code,
        second_level_name,
        submit_end_time,
        entrust_money_of_yuan
        FROM
        t_project_industry_distribution
        WHERE
        DATE_FORMAT( submit_end_time, '%Y-%m' ) &gt;= #{startDate}
        AND DATE_FORMAT( submit_end_time, '%Y-%m' ) &lt;= #{endDate}
        ) AS a
    </select>

    <!-- 行业分组统计 -->
    <select id="queryProjectIndustryDistributionList" resultMap="BaseResultMap">
        SELECT
        first_level_name AS engineering_class,
        second_level_code,
        second_level_name AS projectIndustry,
        count(*) AS projectCount,
        sum( entrust_money_of_yuan ) AS entrustMoney
        FROM
        (
        SELECT DISTINCT
        first_level_name,
        second_level_code,
        second_level_name,
        entrust_money_of_yuan
        FROM
        t_project_industry_distribution
        WHERE
        DATE_FORMAT( submit_end_time, '%Y-%m' ) &gt;= #{startDate}
        AND DATE_FORMAT( submit_end_time, '%Y-%m' ) &lt;= #{endDate}
        ) AS a
        GROUP BY
        second_level_code,
        second_level_name,
        first_level_name;
    </select>

    <select id="statisticalIndustryDistribution" resultMap="BaseResultMap">
        SELECT
        s.first_level_name AS engineering_class,
        s.second_level_name AS projectIndustry,
        sum( s.n2 ) AS entrustMoney,
        sum( s.projectCount ) AS projectCount
        FROM
        (
        SELECT
        first_level_name,
        second_level_name,
        SUM( entrust_money_of_yuan ) n2,
        count(project_id) projectCount
        FROM
        (
        SELECT
        project_id,
        project_number,
        package_number,
        first_level_code,
        first_level_name,
        second_level_code,
        second_level_name,
        entrust_money_of_yuan
        FROM
        t_project_industry_distribution
        WHERE
        DATE_FORMAT( submit_end_time, '%Y-%m' ) &gt;= #{startDate}
        AND DATE_FORMAT( submit_end_time, '%Y-%m' ) &lt;= #{endDate}
        ) a
        GROUP BY
        first_level_name,
        second_level_name
        ) s
        GROUP BY
        s.first_level_name,
        s.second_level_name
        <!--SELECT
        s.first_level_name AS engineering_class,
        s.second_level_name AS projectIndustry,
        sum( s.n2 ) AS entrustMoney,
        count( s.second_level_name ) AS projectCount
        FROM
        (
        SELECT
        first_level_name,
        second_level_name,
        SUM( entrust_money_of_yuan ) n2
        FROM
        (
        SELECT DISTINCT
        project_id,
        project_number,
        package_number,
        first_level_code,
        first_level_name,
        second_level_code,
        second_level_name,
        entrust_money_of_yuan
        FROM
        t_project_industry_distribution
        WHERE
        DATE_FORMAT( submit_end_time, '%Y-%m' ) &gt;= #{startDate}
        AND DATE_FORMAT( submit_end_time, '%Y-%m' ) &lt;= #{endDate}
        ) a
        GROUP BY
        first_level_name,
        second_level_name,
        project_number
        ) s
        GROUP BY
        s.first_level_name,
        s.second_level_name -->
    </select>

    <!-- 地区分组统计 -->
    <select id="queryProjectListWithArea" resultMap="BaseResultMapArea">
        SELECT
        city_code,
        city_name,
        count(project_id) AS projectCount,
        sum( entrust_money_of_yuan ) AS entrustMoney
        FROM
        (
        SELECT
        project_id,
        project_number,
        package_number,
        first_level_code,
        first_level_name,
        second_level_code,
        second_level_name,
        submit_end_time,
        entrust_money_of_yuan,
        city_code,
        city_name
        FROM
        t_project_industry_distribution
        WHERE
        DATE_FORMAT( submit_end_time, '%Y-%m' ) &gt;= #{startDate}
        AND DATE_FORMAT( submit_end_time, '%Y-%m' ) &lt;= #{endDate}
        ) AS a
        GROUP BY
        city_code,
        city_name
        ORDER BY
        city_code
    </select>

    <select id="queryProjectInfoByDate"  resultMap="projectInfo">
        SELECT
        *
        FROM
        (
        SELECT
        project_id,
        package_number,
        submit_end_time,
        entrust_money_of_yuan AS entrust_money,
        department_id,
        department_name,
        exchange_rate,
        entrust_currency
        FROM
        t_project_industry_distribution
        WHERE
        DATE_FORMAT( submit_end_time, '%Y-%m' ) &gt;= #{startDate}
        AND DATE_FORMAT( submit_end_time, '%Y-%m' ) &lt;= #{endDate}
        ) AS a
    </select>

    <!-- 大项目明细 -->
    <select id="queryLargeProjectList"  resultType="com.hzw.sunflower.entity.LargeProject">
        SELECT
        *
        FROM
        (
        SELECT
        project_id,
        package_number,
        user_name,
        project_number,
        principal_company,
        project_name,
        submit_end_time,
        entrust_money,
        department_id,
        department_name,
        exchange_rate,
        entrust_currency
        FROM
        t_project_industry_distribution
        WHERE
        DATE_FORMAT( submit_end_time, '%Y-%m' ) &gt;= #{startDate}
        AND DATE_FORMAT( submit_end_time, '%Y-%m' ) &lt;= #{endDate}
        ) AS a
        ORDER BY
        submit_end_time
    </select>
    <select id="collectExpertList" resultType="com.hzw.sunflower.entity.CollectInfo">
        SELECT
        *
        FROM
        (
        SELECT
        id.project_number purchaseNumber,
        id.package_number,
        id.project_name,
        d.department_name,
        id.user_name manager,
        id.first_level_name,
        id.second_level_name,
        id.project_id,
        id.submit_end_time
        FROM
        t_project_industry_distribution id
        LEFT JOIN t_department d ON id.department_id = d.id
        WHERE
        DATE_FORMAT( submit_end_time, '%Y-%m' ) &gt;= #{startDate}
        AND DATE_FORMAT( submit_end_time, '%Y-%m' ) &lt;= #{endDate}
        ) AS a
        ORDER BY
        project_id DESC,
        package_number + 0 ASC
    </select>

    <select id="statisticalIndustryDistributionNew" resultType="com.hzw.sunflower.entity.SectionSpecialty">
        SELECT
        bs.id AS section_id,
        ses.expert_specialty_id,
        bs.entrust_money
        FROM
        t_project_bid_section bs
        LEFT JOIN t_project p ON bs.project_id = p.id
        LEFT JOIN t_section_expert_specialty ses ON bs.id = ses.section_id
        WHERE
        DATE_FORMAT( bs.submit_end_time, '%Y-%m' ) &gt;= #{startDate}
        AND DATE_FORMAT( bs.submit_end_time, '%Y-%m' ) &lt;= #{endDate}
          AND ((bs.`status` &gt;= 30 AND bs.`status` &lt;= 60) or (bs.status &gt;= 70 and bs.former_abnormal_status &gt;= 30 and bs.former_abnormal_status &lt; 70))
        AND bs.submit_end_time_type = 1
        AND bs.bid_round = 2
        AND bs.is_delete = 0
        AND p.is_delete = 0
        AND ses.expert_specialty_id IS NOT NULL
    </select>
    <select id="queryParentIndustryById" resultType="com.hzw.sunflower.entity.Industry">
        SELECT t2.id,t2.`name`,t2.`code` FROM t_dictionary t1 LEFT JOIN t_dictionary t2 ON t1.parent_id = t2.id WHERE t1.id = #{industryId}
    </select>

    <select id="queryProjectListWithAreaNew" resultType="com.hzw.sunflower.entity.ProjectAnalysisArea">
        SELECT
            d.`name` AS city,
            COUNT( bs.id ) AS projectCount,
            SUM( bs.entrust_money ) AS entrustMoney
        FROM
            t_project_bid_section bs
                LEFT JOIN t_project p ON bs.project_id = p.id
                LEFT JOIN t_dictionary d ON address_city = d.id
        WHERE
            DATE_FORMAT( bs.submit_end_time, '%Y-%m' ) &gt;= #{startDate}

          AND DATE_FORMAT( bs.submit_end_time, '%Y-%m' ) &lt;= #{endDate}
          AND ((bs.`status` &gt;= 30 AND bs.`status` &lt;= 60) or (bs.status &gt;= 70 and bs.former_abnormal_status &gt;= 30 and bs.former_abnormal_status &lt; 70))
          AND bs.submit_end_time_type = 1
          AND bs.bid_round = 2
          AND bs.is_delete = 0
          AND p.is_delete = 0
          AND d.parent_id = 320000
        GROUP BY
            d.`name` UNION ALL
        SELECT
            mm.area AS city,
            SUM( mm.section_count ) AS projectCount,
            SUM( mm.entrust_money ) AS entrustMoney
        FROM
            (
                SELECT
                    d.`name`,
                    COUNT( bs.id ) AS section_count,
                    SUM( bs.entrust_money ) AS entrust_money,
                    p.address_province,
                    p.address_city,
                    CASE

                        WHEN p.address_city = 320100 THEN
                            '苏南'
                        WHEN p.address_city = 320200 THEN
                            '苏南'
                        WHEN p.address_city = 320400 THEN
                            '苏南'
                        WHEN p.address_city = 320500 THEN
                            '苏南'
                        WHEN p.address_city = 321100 THEN
                            '苏南'
                        WHEN p.address_city = 321000 THEN
                            '苏中'
                        WHEN p.address_city = 321200 THEN
                            '苏中'
                        WHEN p.address_city = 320600 THEN
                            '苏中'
                        WHEN p.address_city = 321300 THEN
                            '苏北'
                        WHEN p.address_city = 320800 THEN
                            '苏北'
                        WHEN p.address_city = 320900 THEN
                            '苏北'
                        WHEN p.address_city = 320300 THEN
                            '苏北'
                        WHEN p.address_city = 320700 THEN
                            '苏北' ELSE '外省'
                        END AS area
                FROM
                    t_project_bid_section bs
                        LEFT JOIN t_project p ON bs.project_id = p.id
                        LEFT JOIN t_dictionary d ON address_city = d.id
                WHERE
                    DATE_FORMAT( bs.submit_end_time, '%Y-%m' ) &gt;= #{startDate}

                  AND DATE_FORMAT( bs.submit_end_time, '%Y-%m' ) &lt;= #{endDate}
                  AND ((bs.`status` &gt;= 30 AND bs.`status` &lt;= 60) or (bs.status &gt;= 70 and bs.former_abnormal_status &gt;= 30 AND bs.former_abnormal_status &lt; 70))
                  AND bs.submit_end_time_type = 1
                  AND bs.is_delete = 0
                  AND p.is_delete = 0
                  AND bs.bid_round = 2
                GROUP BY
                    d.`name`,
                    p.address_province,
                    p.address_city
            ) mm
        GROUP BY
            mm.area
    </select>

    <select id="queryAllAreaNew" resultType="com.hzw.sunflower.entity.ProjectAnalysisArea">
        SELECT
            COUNT( bs.id ) AS projectCount,
            SUM( bs.entrust_money ) AS entrustMoney
        FROM
            t_project_bid_section bs
                LEFT JOIN t_project p ON bs.project_id = p.id
        WHERE
            DATE_FORMAT( bs.submit_end_time, '%Y-%m' ) &gt;= #{startDate}
          AND DATE_FORMAT( bs.submit_end_time, '%Y-%m' ) &lt;= #{endDate}
          AND ((bs.`status` &gt;= 30 AND bs.`status` &lt;= 60) or (bs.status &gt;= 70 and bs.former_abnormal_status &gt;= 30 AND bs.former_abnormal_status &lt; 70))
          AND bs.submit_end_time_type = 1
          AND bs.is_delete = 0
          AND p.is_delete = 0
    </select>
    <select id="queryProjectByDepartmentNew" resultType="com.hzw.sunflower.entity.DepartProject">
        SELECT
            mm.department_name AS departmentName,
            IF(mm.current_period_count is null, 0, mm.current_period_count) AS monthCount,
            IF(mm.current_year_count is null, 0, mm.current_year_count) AS yearCount,
            IF(nn.current_period_count is null, 0, nn.current_period_count) AS lastYearCount,
            IF(mm.current_period_money is null, 0, mm.current_period_money) AS monthAmount,
            IF(mm.current_year_money is null, 0, mm.current_year_money) AS yearAmount,
            IF(nn.current_period_money is null, 0, nn.current_period_money) AS lastYearAmount

        FROM
            (
                SELECT
                    m.department_name,
                    n.current_period_count,
                    n.current_period_money,
                    m.current_year_count,
                    m.current_year_money
                FROM
                    (
                        SELECT
                            a.department_name,
                            COUNT( a.project_id ) AS current_year_count,
                            SUM( a.entrust_money ) AS current_year_money
                        FROM
                            (
                                SELECT
                                    bs.project_id,
                                    p.purchase_number,
                                    bs.package_number,
                                    bs.entrust_money,
                                    bs.submit_end_time,
                                    eu.user_id,
                                    d.department_name
                                FROM
                                    t_project_bid_section bs
                                        LEFT JOIN t_project p ON p.id = bs.project_id
                                        LEFT JOIN t_project_entrust_user eu ON p.id = eu.project_id
                                        LEFT JOIN r_user_department rud ON rud.user_id = eu.user_id and eu.department_id = rud.department_id
                                        LEFT JOIN t_department d ON d.id = rud.department_id
                                WHERE
                                    DATE_FORMAT( bs.submit_end_time, '%Y-%m' ) &gt;= #{startDate}
                                  AND DATE_FORMAT( bs.submit_end_time, '%Y-%m' ) &lt;= #{endDate}
                                  AND ((bs.`status` &gt;= 30 AND bs.`status` &lt;= 60) or (bs.status &gt;= 70 and bs.former_abnormal_status &gt;= 30 and bs.former_abnormal_status &lt; 70 ))
                                  AND bs.submit_end_time_type = 1 AND bs.is_delete = 0 AND p.is_delete = 0 AND entrust_money &gt;= 30000000
                                  AND eu.type = 1
                                  AND bs.bid_round = 2
                            ) a
                        GROUP BY
                            a.department_name
                    ) m
                        LEFT JOIN (
                        SELECT
                            a.department_name,
                            COUNT( a.project_id ) AS current_period_count,
                            SUM( a.entrust_money ) AS current_period_money
                        FROM
                            (
                                SELECT
                                    bs.project_id,
                                    p.purchase_number,
                                    bs.package_number,
                                    bs.entrust_money,
                                    bs.submit_end_time,
                                    eu.user_id,
                                    d.department_name
                                FROM
                                    t_project_bid_section bs
                                        LEFT JOIN t_project p ON p.id = bs.project_id
                                        LEFT JOIN t_project_entrust_user eu ON p.id = eu.project_id
                                        LEFT JOIN r_user_department  rud ON rud.user_id = eu.user_id  and eu.department_id = rud.department_id
                                        LEFT JOIN t_department d ON d.id = rud.department_id
                                WHERE
                                    DATE_FORMAT( bs.submit_end_time, '%Y-%m' ) = #{endDate}
                                  AND ((bs.`status` &gt;= 30 AND bs.`status` &lt;= 60) or (bs.status &gt;= 70 and bs.former_abnormal_status &gt;= 30 and bs.former_abnormal_status &lt; 70 ))
                                  AND bs.submit_end_time_type = 1 AND bs.is_delete = 0 AND p.is_delete = 0 AND entrust_money &gt;= 30000000
                                  AND eu.type = 1
                                  AND bs.bid_round = 2
                            ) a
                        GROUP BY
                            a.department_name
                    ) n ON m.department_name = n.department_name
            ) mm
                LEFT JOIN (
                SELECT
                    m.department_name,
                    n.current_period_count,
                    n.current_period_money,
                    m.current_year_count,
                    m.current_year_money
                FROM
                    (
                        SELECT
                            a.department_name,
                            COUNT( a.project_id ) AS current_year_count,
                            SUM( a.entrust_money ) AS current_year_money
                        FROM
                            (
                                SELECT
                                    bs.project_id,
                                    p.purchase_number,
                                    bs.package_number,
                                    bs.entrust_money,
                                    bs.submit_end_time,
                                    eu.user_id,
                                    d.department_name
                                FROM
                                    t_project_bid_section bs
                                        LEFT JOIN t_project p ON p.id = bs.project_id
                                        LEFT JOIN t_project_entrust_user eu ON p.id = eu.project_id
                                        LEFT JOIN r_user_department  rud ON rud.user_id = eu.user_id  and eu.department_id = rud.department_id
                                        LEFT JOIN t_department d ON d.id = rud.department_id
                                WHERE
                                    DATE_FORMAT( bs.submit_end_time, '%Y-%m' ) &gt;= #{lastYearStartDate}
                                  AND DATE_FORMAT( bs.submit_end_time, '%Y-%m' ) &lt;= #{lastYearEndDate}
                                  AND ((bs.`status` &gt;= 30 AND bs.`status` &lt;= 60) or (bs.status &gt;= 70 and bs.former_abnormal_status &gt;= 30 and bs.former_abnormal_status &lt; 70 ))
                                  AND bs.submit_end_time_type = 1 AND bs.is_delete = 0 AND p.is_delete = 0 AND entrust_money &gt;= 30000000
                                  AND eu.type = 1
                                  AND bs.bid_round = 2
                            ) a
                        GROUP BY
                            a.department_name
                    ) m
                        LEFT JOIN (
                        SELECT
                            a.department_name,
                            COUNT( a.project_id ) AS current_period_count,
                            SUM( a.entrust_money ) AS current_period_money
                        FROM
                            (
                                SELECT
                                    bs.project_id,
                                    p.purchase_number,
                                    bs.package_number,
                                    bs.entrust_money,
                                    bs.submit_end_time,
                                    eu.user_id,
                                    d.department_name
                                FROM
                                    t_project_bid_section bs
                                        LEFT JOIN t_project p ON p.id = bs.project_id
                                        LEFT JOIN t_project_entrust_user eu ON p.id = eu.project_id
                                        LEFT JOIN r_user_department  rud ON rud.user_id = eu.user_id  and eu.department_id = rud.department_id
                                        LEFT JOIN t_department d ON d.id = rud.department_id
                                WHERE
                                    DATE_FORMAT( bs.submit_end_time, '%Y-%m' ) = #{lastYearEndDate}
                                  AND ((bs.`status` &gt;= 30 AND bs.`status` &lt;= 60) or (bs.status &gt;= 70 and bs.former_abnormal_status &gt;= 30 and bs.former_abnormal_status &lt; 70 ))
                                  AND bs.submit_end_time_type = 1 AND bs.is_delete = 0 AND p.is_delete = 0 AND entrust_money &gt;= 30000000
                                  AND eu.type = 1
                                  AND bs.bid_round = 2
                            ) a
                        GROUP BY
                            a.department_name
                    ) n ON m.department_name = n.department_name
            ) nn ON mm.department_name = nn.department_name

    </select>
    <select id="queryLargeProjectListNew" resultType="com.hzw.sunflower.entity.LargeProject">
        SELECT DISTINCT
            bs.project_id,
            tp.purchase_number AS project_number,
            tp.purchase_name AS project_name,
            tp.created_time,
            bs.package_number,
            tu.user_name,
            rud.department_id,
            td.department_name,
            tp.principal_company,
            bs.entrust_money,
            bs.submit_end_time,
            bs.entrust_currency,
            dic.`value` AS exchange_rate
        FROM
            t_project_bid_section bs
                LEFT JOIN t_project tp ON bs.project_id = tp.id
                LEFT JOIN t_project_entrust_user eu ON bs.project_id = eu.project_id
                LEFT JOIN r_user_department  rud ON eu.user_id = rud.user_id and eu.department_id = rud.department_id
                LEFT JOIN t_department td ON td.id = rud.department_id
                LEFT JOIN t_user tu ON tu.id = rud.user_id
                LEFT JOIN t_dictionary dic ON bs.entrust_currency = dic.id
        WHERE
            1 = 1
          AND bs.is_delete = 0
          AND tp.is_delete = 0
          AND eu.type = 1
          AND bs.bid_round = 2
          AND eu.is_delete = 0
          AND bs.submit_end_time_type = 1
          AND ((bs.`status` &gt;= 30 AND bs.`status` &lt;= 60) or (bs.status &gt;= 70 and bs.former_abnormal_status &gt;= 30 and bs.former_abnormal_status &lt; 70 ))

          AND bs.entrust_money &gt;= 30000000
          AND DATE_FORMAT( bs.submit_end_time, '%Y-%m' ) &gt;= #{startDate}
          AND DATE_FORMAT( bs.submit_end_time, '%Y-%m' ) &lt;= #{endDate}
    </select>
</mapper>
