///***
/// * 主体人|机构信息 - 首页
/// */

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import 'package:flutter_main_app/serviceLocator.dart';
import 'package:flutter_main_app/src/constants/theme.dart';
import 'package:flutter_main_app/src/utils/ScreenUtils.dart';
import 'package:flutter_main_app/src/fonts/fonts.dart';

import 'package:flutter_main_app/src/viewModels/marketPlayer/SubjectBasicViewModel.dart';

import 'package:flutter_main_app/src/models/marketPlayer/SubjectBasicModel.dart';

import 'package:flutter_main_app/src/components/CustomAppBar.dart';
import 'package:flutter_main_app/src/components/CustomCircularProgressIndicator.dart';
import 'package:flutter_main_app/src/components/CustomSafeArea.dart';
import 'package:flutter_main_app/src/components/CustomMonospacedText.dart';
import 'package:flutter_main_app/src/components/CustomFieldDisplay.dart';

import 'package:flutter_main_app/src/widgets/market/SubjectBasicWidget.dart';
import 'package:flutter_main_app/src/widgets/market/SubjectPerformanceWidget.dart';
import 'package:flutter_main_app/src/widgets/market/SubjectQualificationWidget.dart';
import 'package:flutter_main_app/src/widgets/market/SubjectStaffWidget.dart';

class SubjectHomeWidget extends StatefulWidget {
  const SubjectHomeWidget({Key key}) : super(key: key);

  /// * 路由名称
  static const String _routeName = 'SubjectHome';
  static String get routeName => _routeName;

  @override
  _SubjectHomeWidgetState createState() => _SubjectHomeWidgetState();
}

class _SubjectHomeWidgetState extends State<SubjectHomeWidget> {

  /// * 仿证件图片的一些设置
  static const TextStyle _certificatePairTextStyle = TextStyle(
    height: 2,
    fontSize: 12.0,
    fontWeight: FontWeight.w400,
    color: themeTextColor,
  );

  /// * 其他信息的一些设置
  static const int _crossAxisCount = 4;

  final double _itemWidth = ScreenUtils.screenWidth / _crossAxisCount;
  final double _itemHeight = 70.0;

  static const List<String> _infoTypes = [
    '基本信息',
    '资质',
    '人员',
    '业绩',
    '分支机构',
    '股东信息',
    '企业年报',
    '行政处罚',
    '变更记录',
  ];
  static const List<IconData> _infoIcons = [
    FontsHelper.basicInfoIconData,
    FontsHelper.qualificationIconData,
    FontsHelper.membersIconData,
    FontsHelper.achievementIconData,
    FontsHelper.branchIconData,
    FontsHelper.shareHolderIconData,
    FontsHelper.annualReportIconData,
    FontsHelper.administrativePenaltyIconData,
    FontsHelper.changeLogIconData,
  ];
  static const List<Color> _infoColors = [
    Color(0xff4f8fe5),
    Color(0xffe36363),
    Color(0xffedac32),
    Color(0xffe36363),
  ];

  final List<String> _infoPageNames = [
    SubjectBasicWidget.routeName,
    SubjectQualificationWidget.routeName,
    SubjectStaffWidget.routeName,
    SubjectPerformanceWidget.routeName,
  ];

  /// * 主体基本信息 视图模型 单例
  final SubjectBasicViewModel _model = serviceLocator<SubjectBasicViewModel>();

  @override
  void initState() {
    super.initState();

    _model.fetchSubjectBasicInfo();
  }

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider<SubjectBasicViewModel>.value(
          value: serviceLocator<SubjectBasicViewModel>(),
        ),
      ],
      child: Consumer<SubjectBasicViewModel>(
        builder: (context, model, child) {
          return Scaffold(
            appBar: CustomAppBar(
              title: '招标人信息',
            ),
            body: model.isLoading
              ? CustomCircularProgressIndicator()
              : CustomSafeArea(
                  backgroundColor: themeContentBackgroundColor,
                  child: ListView(
                    physics: BouncingScrollPhysics(),
                    children: <Widget>[
                      /// * 基本信息
                      Container(
                        width: ScreenUtils.screenWidth,
                        padding: const EdgeInsets.fromLTRB(15.0, 10.0, 15.0, 10.0),
                        decoration: BoxDecoration(
                          border: Border(
                            bottom: BorderSide(
                              width: 6.0,
                              color: themeBackgroundColor,
                            ),
                          ),
                        ),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.start,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: <Widget>[
                            /// * 仿证件图片
                            Container(
                              margin: const EdgeInsets.only(
                                left: 15.0,
                                top: 10.0,
                                right: 15.0,
                              ),
                              width: ScreenUtils.screenWidth - 15.0 * 2,
                              height: 220.0,
                              decoration: BoxDecoration(
                                color: themeBackgroundColor,
                                image: DecorationImage(
                                  image: ExactAssetImage('assets/images/bg_supplier_subject_home.png'),
                                  fit: BoxFit.fill,
                                ),
                                borderRadius: BorderRadius.circular(themeBorderRadius),
                              ),
                              child: Column(
                                mainAxisSize: MainAxisSize.max,
                                mainAxisAlignment: MainAxisAlignment.spaceAround,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: <Widget>[
                                  Container(
                                    width: ScreenUtils.screenWidth - 15.0 * 2,
                                    child: Text(
                                      model.subjectBasic.subjectName,
                                      textAlign: TextAlign.center,
                                      maxLines: 1,
                                      style: TextStyle(
                                        fontSize: 17.0,
                                        fontWeight: FontWeight.bold,
                                        color: themeTextColor,
                                      ),
                                    ),
                                  ),
                                  Padding(
                                    padding: const EdgeInsets.only(left: 30.0),
                                    child: Column(
                                      mainAxisSize: MainAxisSize.max,
                                      mainAxisAlignment: MainAxisAlignment.start,
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: _buildCertificatePairs(model.subjectBasic),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            Padding(
                              padding: const EdgeInsets.only(
                                top: 20.0,
                                bottom: 10.0,
                              ),
                              child: Text(
                                '联系方式',
                                style: TextStyle(),
                              ),
                            ),
                            ...this._buildBasicInfo(model.subjectBasic),
                          ],
                        ),
                      ),
                      /// * 其他信息
                      Column(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: <Widget>[
                          Container(
                            width: ScreenUtils.screenWidth,
                            padding: const EdgeInsets.only(
                              top: 20.0,
                              bottom: 20.0,
                              left: 15.0,
                            ),
                            decoration: BoxDecoration(
                              border: Border(
                                bottom: BorderSide(
                                  width: themeBorderWidth,
                                  color: themeBorderColor,
                                ),
                              ),
                            ),
                            child: Text(
                              '其他信息',
                              style: TextStyle(
                                fontSize: 14.0,
                                color: themeActiveColor,
                              ),
                            ),
                          ),
                          SizedBox(
                            width: ScreenUtils.screenWidth,
                            height: _itemHeight * (_infoTypes.length / _crossAxisCount).ceil(),
                            child: GridView.count(
                              physics: BouncingScrollPhysics(),
                              crossAxisCount: _crossAxisCount,
                              childAspectRatio: _itemWidth / _itemHeight,
                              children: this._buildOtherInfoGridChildrenWidgets(context, model.subjectBasic),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
          );
        },
      ),
    );
  }

  ///***
  /// * 生成仿证件部分的键值对组件 UI
  /// *
  /// * @param {SubjectBasicModel} subjectBasic
  /// * @return {List<Widget>}
  /// */
  List<Widget> _buildCertificatePairs(SubjectBasicModel subjectBasic) {
    const List<String> fieldNames = ['法定代表人', '注册资本', '分支机构', '成立日期'];

    /// * 处理字段值
    final String legalName = subjectBasic.legalName;

    final String capital = subjectBasic.capital;

    final String branchCount = '${subjectBasic.branchCount} 家';

    final String registerTime = subjectBasic.registerTime;

    final List<String> fieldValues = [
      legalName,
      capital,
      branchCount,
      registerTime,
    ];

    final List<Widget> pairWidgets = [];

    for (int i = 0, len = fieldNames.length; i < len; i++) {
      final Widget pairWidget = Row(
        mainAxisSize: MainAxisSize.max,
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: <Widget>[
          CustomMonospacedText(
            width: 65.0,
            text: fieldNames[i],
            textStyle: _certificatePairTextStyle,
            needColon: false,
            maxTextLengthLimit: 5,
          ),
          Padding(
            padding: const EdgeInsets.only(left: 17.0),
            child: Text(
              fieldValues[i],
              style: _certificatePairTextStyle,
            ),
          ),
        ],
      );

      pairWidgets.add(pairWidget);
    }

    return pairWidgets;
  }

  ///***
  /// * 生成基本信息组件
  /// *
  /// * @param {SubjectBasicModel} subjectBasic
  /// * @return {List<Widget>}
  /// */
  List<Widget> _buildBasicInfo(SubjectBasicModel subjectBasic) {
    const List<String> fieldNames = ['电话', '邮箱', '网址', '地址'];

    /// * 处理字段值
    final String contactNumber = subjectBasic.contactNumber;

    final String email = '';

    final String url = subjectBasic.url;

    final String address = subjectBasic.address;

    final List<String> fieldValues = [
      contactNumber,
      email,
      url,
      address,
    ];

    final List<Widget> basicInfoWidgets = [];

    for (int i = 0, len = fieldNames.length; i < len; i++) {
      final Widget basicInfoWidget = CustomFieldDisplay(
        fieldName: fieldNames[i] ?? '',
        fieldValue: fieldValues[i] ?? '',
        isFieldValueMultiLine: fieldNames[i] == '地址',
      );

      basicInfoWidgets.add(basicInfoWidget);
    }

    return basicInfoWidgets;
  }

  ///***
  /// * 生成其他信息网格单元组件
  /// *
  /// * @param {BuildContext} context
  /// * @param {SubjectBasicModel} subjectBasic
  /// * @return {List<Widget>}
  /// */
  List<Widget> _buildOtherInfoGridChildrenWidgets(BuildContext context, SubjectBasicModel subjectBasic) {
    const double topPadding = 20.0;
    const double dividerHeight = 26.0;

    final List<Widget> gridChildren = [];

    for (int i = 0, len = _infoTypes.length; i < len; i++) {
      /// * 项目是否可点击
      bool isActive = false;

      switch (i) {
        case 0:
          isActive = true;
          break;
        case 1:
          isActive = subjectBasic.isexistSupplierSubjectQualification ?? false;
          break;
        case 2:
          isActive = subjectBasic.isexistSupplierStaff ?? false;
          break;
        case 3:
          isActive = subjectBasic.isexistSupplierPerformance ?? false;
          break;
        default:
          isActive = false;
          break;
      }

      final Widget gridChild = GestureDetector(
        onTap: () {
          if (isActive) {
            Navigator.pushNamed(
              context,
              _infoPageNames[i],
            );
          }
        },
        child: Stack(
          alignment: AlignmentDirectional.topStart,
          children: <Widget>[
            // * 不规则边框
            i % _crossAxisCount != 0
              ? VerticalDivider(
                  width: themeBorderWidth,
                  thickness: themeBorderWidth,
                  color: themeBorderColor,
                  indent: (_itemHeight - dividerHeight + topPadding) / 2,
                  endIndent: (_itemHeight - dividerHeight - topPadding) / 2,
                )
              : Container(),
            Container(
              width: _itemWidth,
              height: _itemHeight,
              padding: const EdgeInsets.only(top: topPadding),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: <Widget>[
                  SizedBox(
                    width: 24.0,
                    height: 24.0,
                    child: Center(
                      child: Icon(
                        _infoIcons[i],
                        size: 23.0,
                        color: isActive
                          ? _infoColors[i] ?? themeHintTextColor
                          : themeHintTextColor,
                      ),
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.only(top: 6.0),
                    child: Text(
                      _infoTypes[i],
                      style: TextStyle(
                        fontSize: 12.0,
                        color: isActive
                          ? themeTextColor
                          : themeHintTextColor,
                      ),
                    ),
                  )
                ],
              ),
            ),
          ],
        ),
      );

      gridChildren.add(gridChild);
    }

    return gridChildren;
  }

}
