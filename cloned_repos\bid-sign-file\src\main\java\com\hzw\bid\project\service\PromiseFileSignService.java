package com.hzw.bid.project.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hzw.bid.project.controller.request.PromiseFileSignReq;
import com.hzw.bid.project.model.PromiseFileSign;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 * @date ：Created in 2021/8/27 11:31
 * @description：
 * @version: 1.0
 */
public interface PromiseFileSignService extends IService<PromiseFileSign> {

    /**
     * 专家签署承诺文件
     * @param req
     * @param jwtUser
     * @return
     */
    Boolean sign(MultipartFile file, PromiseFileSignReq req, Long userId);

    Long uploadSignFile(MultipartFile file,String fileName);

    /**
     * 上传签章文件（文件名不包含时间）
     * @param file
     * @param fileName
     * @return
     */
    Long uploadSignFileNoTime(MultipartFile file,String fileName);
}
