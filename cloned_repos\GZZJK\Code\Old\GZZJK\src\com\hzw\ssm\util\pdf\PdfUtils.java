/*
 * 公司名称：江苏华招网信息技术有限公司
 * 版权信息：江苏华招网信息技术有限公司版权所有
 * 文件名称：PdfUtils.java
 * 修改时间：2018年5月22日
 * 修改人：朱加健
 * 修改内容：
 * 跟踪单号：
 * 修改单号 ：
 */

package com.hzw.ssm.util.pdf;

import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.MalformedURLException;
import java.nio.charset.Charset;

import org.apache.struts2.ServletActionContext;

import com.itextpdf.text.BaseColor;
import com.itextpdf.text.Document;
import com.itextpdf.text.DocumentException;
import com.itextpdf.text.Element;
import com.itextpdf.text.ExceptionConverter;
import com.itextpdf.text.Font;
import com.itextpdf.text.Image;
import com.itextpdf.text.PageSize;
import com.itextpdf.text.Paragraph;
import com.itextpdf.text.Phrase;
import com.itextpdf.text.Rectangle;
import com.itextpdf.text.pdf.BaseFont;
import com.itextpdf.text.pdf.ColumnText;
import com.itextpdf.text.pdf.GrayColor;
import com.itextpdf.text.pdf.PdfContentByte;
import com.itextpdf.text.pdf.PdfGState;
import com.itextpdf.text.pdf.PdfPCell;
import com.itextpdf.text.pdf.PdfPTable;
import com.itextpdf.text.pdf.PdfPageEventHelper;
import com.itextpdf.text.pdf.PdfReader;
import com.itextpdf.text.pdf.PdfStamper;
import com.itextpdf.text.pdf.PdfTemplate;
import com.itextpdf.text.pdf.PdfWriter;
import com.itextpdf.tool.xml.XMLWorkerHelper;

/**
 * <一句话功能简述> PDF文件工具类
 * <功能详细描述>
 * <AUTHOR>
 * @version V1.0[产品/模块版本]
 */
public class PdfUtils {
    /**
     * 主机的样式文件所在的路径
     */
	public static String prefixFont ;
    
    static {
    	//根据系统自动匹配默认路径
        String os = System.getProperties().getProperty("os.name");
        if(os.startsWith("win") || os.startsWith("Win")){
        	//TODO 说明：如果主机的系统不是安装在C盘，则需要修改此处的路径
            prefixFont = "C:\\Windows\\Fonts" + File.separator;
        }else {
            prefixFont = "/usr/share/fonts/chinese" + File.separator;
        }
    }
	
    /**
     * 函数功能描述：将指定位置的HTML文件转换成PDF文件
     * @param htmlPath String html文件路径
     * @param pdfPath String pdf文件路径
     * @param encode String 编码格式（默认UTF-8）
     * @throws Exception 
     */
    public static void parseHtmlToPdf(String htmlPath, String pdfPath, String encode,String footerContent)
            throws Exception {
        if (null == encode || "".equals(encode)) {
            //默认UTF-8格式
            encode = "UTF-8";
        }
        
        //读取HTML文件内容
        String content = readContent(htmlPath, encode);
        
        //将HTML文件内容转换成PDF文件
        parsePdfByContent(content, pdfPath, encode,footerContent);
    }
    
    /**
     * 函数功能描述：将读取到的HTML文件内容转换成PDF文件
     * @param content String html文件内容
     * @param pdfPath String pdf文件路径
     * @param encode String 编码格式（默认UTF-8）
     * @throws Exception
     */
    public static void parsePdfByContent(String content, String pdfPath, String encode,String footerContent)
            throws Exception {
        if (null == encode || "".equals(encode)) {
            //默认UTF-8格式
            encode = "UTF-8";
        }
        
        //创建“文档”，默认A4纸大小（纵向版式），左、右、上边距30，下边距70（PageSize.A4.rotate()表示横向版式）
        Document document = new Document(PageSize.A4,30,30,30,70);
        
        //根据系统环境自动匹配文件路径分隔符
        String savePath = ServletActionContext.getServletContext().getRealPath("") + File.separator+"pdf"+ File.separator+System.currentTimeMillis()+".pdf";
        savePath = replaceSeparator(savePath);
        //加载pdf文件
        PdfWriter writer = PdfWriter.getInstance(document, new FileOutputStream(savePath));
        writer.setViewerPreferences(PdfWriter.PageLayoutOneColumn);
        // 为这篇文档设置页面事件(X/Y)
        writer.setPageEvent(new PageEvent());
        
        BaseFont baseFont1 = BaseFont.createFont(prefixFont + "simsun.ttc,0", 
        		BaseFont.IDENTITY_H, BaseFont.NOT_EMBEDDED);
        Font songti09 = new Font(baseFont1, 9f); //宋体 小五
        //“打开”文档
        document.open();
        document.newPage();
        PdfPTable pdfPTable = new PdfPTable(1);
        //为文档添加页脚，事件的发生是在生成文档之后，写入到硬盘之前
        Footer footerTable = new Footer(pdfPTable);
        footerTable.setTableFooter(writer, songti09, footerContent);
        document.add(pdfPTable);
        XMLWorkerHelper.getInstance().parseXHtml(writer, document,
                new ByteArrayInputStream(content.getBytes(encode)), Charset.forName(encode));
        //关闭“文档”
      		
        document.close();
        parsePdfByContent(savePath, replaceSeparator(pdfPath));
    }
    public static void parsePdfByContent( String pdfPath, String spdfPath)
            throws Exception {
    	
        PdfReader reader = new PdfReader(pdfPath);
        BaseFont bfChinese = BaseFont.createFont(prefixFont +"simsun.ttc,0", BaseFont.IDENTITY_H, BaseFont.NOT_EMBEDDED);
        Font font1 = new Font(bfChinese, 34, Font.NORMAL, new GrayColor(0.5f));
        Phrase p = new Phrase("江苏省招标中心有限公司专家库系统",font1);
        PdfStamper stamper = new PdfStamper(reader, new FileOutputStream(spdfPath));
        int n = reader.getNumberOfPages();
        PdfContentByte over;
  
        for (int i = 1; i <= n; i++) {
            over = stamper.getUnderContent(i);
            over.saveState();
             // set transparency
            PdfGState state = new PdfGState();
            state.setFillOpacity(0.2f);
            over.setGState(state);
//                ColumnText.showTextAligned(over, Element.ALIGN_CENTER, p, x, y, 0);
            //控制水印的显示效果，可以自己调整看看
            float beginPositionX = 50, beginPositionY = 0, distance = 180;
            for (int i2 = 0; i2 < 1; i2++) {
                for (int j = 0; j < 4; j++) {
                    ColumnText.showTextAligned(over, Element.ALIGN_LEFT,
                            p, beginPositionX + distance*i2, beginPositionY + distance*j, 30);
 
                }
            }
            // add watermark text and image
//                if (i % 2 == 1) {
//                    ColumnText.showTextAligned(over, Element.ALIGN_CENTER, p, x, y, 0);
//                } else {
//                    over.addImage(img, w, 0, 0, h, x - (w / 2), y - (h / 2));
//                }
             over.restoreState();
        }
        stamper.close();
        reader.close();
    }
    
    
    /**
     * 设置水印
     * @param writer
     * @throws DocumentException
     */
	private static void setWatermark(PdfWriter writer) throws DocumentException {
		// 加入水印
		PdfContentByte waterMar = writer.getDirectContentUnder();
		// 开始设置水印
		waterMar.beginText();
		// 设置水印透明度
		PdfGState gs = new PdfGState();
		// 设置填充字体不透明度为0.4f
		gs.setFillOpacity(0.4f);
		try {
			// 设置水印字体参数及大小                                  (字体参数，字体编码格式，是否将字体信息嵌入到pdf中（一般不需要嵌入），字体大小)
			waterMar.setFontAndSize(BaseFont.createFont(prefixFont +"simsun.ttc,0", BaseFont.IDENTITY_H, BaseFont.NOT_EMBEDDED), 60);
			// 设置透明度
			waterMar.setGState(gs);
			// 设置水印对齐方式 水印内容 X坐标 Y坐标 旋转角度
			//Rectangle pageRect = pdfStamper.getReader(). getPageSizeWithRotation(i);
			waterMar.showTextAligned(Element.ALIGN_RIGHT, "江苏省招标中心有限公司专家库系统" , 500, 430, 45);
			// 设置水印颜色
			waterMar.setColorFill(BaseColor.GRAY);
			//结束设置
			waterMar.endText();
			waterMar.stroke();
		} catch (IOException e) {
			e.printStackTrace();
		}finally {
			waterMar = null;
			gs = null;
		}
	}
    
    /**
     * 函数功能描述：读取指定位置文件的内容
     * @param path String 文件路径
     * @param path String 编码格式
     * @return content String 文件内容
     * @throws IOException 
     */
    public static String readContent(String path, String encode) throws IOException {
        //根据系统环境自动匹配文件路径分隔符
        path = replaceSeparator(path);
        
        @SuppressWarnings("resource")
        BufferedReader reader = new BufferedReader(
                new InputStreamReader(new FileInputStream(path), encode));

        String content = "";
        String line = "";
        while ( (line = reader.readLine()) != null) {
            content += line;
        }
        
        return content;
    }
    
    /**
     * 函数功能描述：根据系统环境自动匹配文件路径分隔符
     * @param path String 原文件路径
     * @return path String 处理后的文件路径
     */
    public static String replaceSeparator(String path) {
        if (File.separator.equals("/")) {
            return path.replaceAll("[\\\\/]+", "/");
        }
        
        return path.replaceAll("[\\\\/]+", "\\\\");
    }
    
    /**
     * <一句话功能简述> 内部类，实现页码事件
     * <功能详细描述>
     * <AUTHOR>
     * @version V1.0[产品/模块版本]
     */
    private static class PageEvent extends PdfPageEventHelper{
        /** The PdfTemplate that contains the total number of pages. */
        public PdfTemplate total;

        public BaseFont bfChinese;
        
        /**
         * 重写PdfPageEventHelper中的onOpenDocument方法
         */
        @Override
        public void onOpenDocument(PdfWriter writer, Document document) {
            // 得到文档的内容并为该内容新建一个模板
            total = writer.getDirectContent().createTemplate(500, 500);
            try {
                // 设置字体对象为Windows系统默认的字体
                bfChinese = BaseFont.createFont(prefixFont + "simsun.ttc,0", 
                		BaseFont.IDENTITY_H, BaseFont.NOT_EMBEDDED);
            } catch (Exception e) {
                throw new ExceptionConverter(e);
            }
        }

        /**
         * 重写PdfPageEventHelper中的onEndPage方法
         */
        @Override
        public void onEndPage(PdfWriter writer, Document document) {
            // 新建获得用户页面文本和图片内容位置的对象
            PdfContentByte pdfContentByte = writer.getDirectContent();
            // 保存图形状态
            pdfContentByte.saveState();
            String text = writer.getPageNumber() + "/";
            // 获取点字符串的宽度
            float textSize = bfChinese.getWidthPoint(text, 9);
            pdfContentByte.beginText();
            // 设置随后的文本内容写作的字体和字号
            pdfContentByte.setFontAndSize(bfChinese, 9);

            // 定位'X/'
            float x = (document.right() + document.left()) / 2;
            float y = 56f;
            pdfContentByte.setTextMatrix(x, y);
            pdfContentByte.showText(text);
            pdfContentByte.endText();

            // 将模板加入到内容（content）中- // 定位'Y'
            pdfContentByte.addTemplate(total, x + textSize, y);

            pdfContentByte.restoreState();
        }

        /**
         * 重写PdfPageEventHelper中的onCloseDocument方法
         */
        @Override
        public void onCloseDocument(PdfWriter writer, Document document) {
            total.beginText();
            try {
                bfChinese = BaseFont.createFont(prefixFont + "simsun.ttc,0",
                		BaseFont.IDENTITY_H, BaseFont.NOT_EMBEDDED);
                total.setFontAndSize(bfChinese, 9);
            } catch (DocumentException e) {
                e.printStackTrace();
            } catch (IOException e) {
                e.printStackTrace();
            }

            total.setTextMatrix(0, 0);
            // 设置总页数的值到模板上，并应用到每个界面
            total.showText(String.valueOf(writer.getPageNumber() - 1));
            total.endText();
        }
    }

    //页眉事件
    @SuppressWarnings("unused")
	private static class Header extends PdfPageEventHelper {
        public static PdfPTable header;

        public Header(PdfPTable header) {
            Header.header = header;
        }

        @Override
        public void onEndPage(PdfWriter writer, Document document) {
            //把页眉表格定位
            header.writeSelectedRows(0, -1, 36, 806, writer.getDirectContent());
        }

        /**
         * 设置页眉
         * @param writer
         * @param req
         * @throws MalformedURLException
         * @throws IOException
         * @throws DocumentException
         */
		public void setTableHeader(PdfWriter writer,Font songti09) 
        		throws MalformedURLException, IOException, DocumentException {
        	//页眉水印
        	/*PdfPTable table = new PdfPTable(1);
            table.setTotalWidth(520);
            PdfPCell cell = new PdfPCell();
            //不显示横线
            cell.setBorder(0);
            Image image01;
            //图片自己传
            image01 = Image.getInstance("e:\\excel.png"); 
            image01.setWidthPercentage(80);
            cell.setPaddingLeft(400f);
            cell.setPaddingTop(-20f);
            cell.addElement(image01);
            table.addCell(cell);
            Header event = new Header(table);
            writer.setPageEvent(event);*/
        	
        	//页眉文字
        	/*PdfPTable table = new PdfPTable(1);
            table.setTotalWidth(520);
            PdfPCell cell = new PdfPCell();
            //显示横线
            cell.setBorder(1);
            String string = "抽取批次：123456789";
            Paragraph p = new Paragraph(string, songti09);
            cell.setPaddingLeft(400f);
            cell.setPaddingTop(-20f);
            cell.addElement(p);
            //cell.addElement(image01);
            table.addCell(cell);
            Header event = new Header(table);
            writer.setPageEvent(event);*/
        }   
    }

    //页脚事件
    private static class Footer extends PdfPageEventHelper {
        public static PdfPTable footer;

        @SuppressWarnings("static-access")
        public Footer(PdfPTable footer) {
            this.footer = footer;
        }

        @Override
        public void onEndPage(PdfWriter writer, Document document) {
            //把页脚表格定位
            footer.writeSelectedRows(0, -1, 38, 50, writer.getDirectContent());
        }

        /**
         * 页脚是图片
         * @param writer
         * @throws MalformedURLException
         * @throws IOException
         * @throws DocumentException
         */
        @SuppressWarnings("unused")
		public void setTableFooter(PdfWriter writer) 
				throws MalformedURLException, IOException, DocumentException {
            //页脚水印
        	/*
            PdfPTable table = new PdfPTable(1);
            table.setTotalWidth(523);
            PdfPCell cell = new PdfPCell();
            cell.setBorder(1);
            Image image01;
            image01 = Image.getInstance(imageAddress + "E:\\MyWorkspace\\Test\\excel.png"); //图片自己传
            image01.scaleAbsoluteWidth(523);
            image01.scaleAbsoluteHeight(30f);
            image01.setWidthPercentage(100);
            cell.addElement(image01);
            table.addCell(cell);
            Footer event = new Footer(table);
            writer.setPageEvent(event);*/
        }

        /**
         * 页脚是文字
         * @param writer
         * @param songti09
         */
        public void setTableFooter(PdfWriter writer, Font songti09,String footerContent) {
        	//添加页脚文字
        	Paragraph p = new Paragraph(footerContent, songti09);
        	
        	//实例化一个PDF表格
            PdfPTable table = new PdfPTable(1);
            //设置页脚表格宽度
            table.setTotalWidth(520);
            //实例化一个PDF单元格
            PdfPCell cell = new PdfPCell();
            //显示横线
            cell.setBorder(1);
            cell.setPaddingLeft(380f);
            cell.setPaddingTop(-2f);
            cell.addElement(p);
            table.addCell(cell);
            Footer event = new Footer(table);
            writer.setPageEvent(event);
        }
    }
   
    
    /**
     * 函数功能描述：测试代码
     * @param args
     */
    public static void main(String[] args) {
        String htmlPath = "E:\\Test\\expertInfo.html";
        
        String pdfPath = "E:\\Test\\templete34.pdf";
        
        try {
            parseHtmlToPdf(htmlPath, pdfPath, "UTF-8","抽取批次");
        }
        catch (Exception e) {
            e.printStackTrace();
        }
    }
}
