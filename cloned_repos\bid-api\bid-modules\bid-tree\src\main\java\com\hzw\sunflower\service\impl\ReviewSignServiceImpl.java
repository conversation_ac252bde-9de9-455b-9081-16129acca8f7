package com.hzw.sunflower.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.constant.constantenum.FileDocumentCodeEnum;
import com.hzw.sunflower.constant.constantenum.ReviewFileTypeEnum;
import com.hzw.sunflower.controller.request.ReviewSignReq;
import com.hzw.sunflower.dao.ReviewSignMapper;
import com.hzw.sunflower.entity.*;
import com.hzw.sunflower.security.utils.SecurityUtils;
import com.hzw.sunflower.service.*;
import com.hzw.sunflower.util.OssUtil;
import com.hzw.sunflower.util.oss.OssFileStorage;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;
import java.util.regex.Pattern;


/**
 * <AUTHOR>
 * @date ：Created in 2023/9/11 13:51
 * @description：
 * @version: 1.0
 */
@Service
public class ReviewSignServiceImpl extends ServiceImpl<ReviewSignMapper, ReviewSign> implements ReviewSignService {

    @Autowired
    private RBidDocumentService rBidDocumentService;

    @Autowired
    private OnlineConferenceReviewService onlineConferenceReviewService;

    @Autowired
    private OssFileService ossFileService;

    @Value("${oss.active}")
    private String ossType;

    @Autowired
    private ReviewFileService reviewFileService;

    /**
     * 评审文件签章
     * @param req
     * @param file
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean signForReview(ReviewSignReq req, MultipartFile file) {
        // 文件入库
        Long fileOssId = this.uploadSignFile(file);
        ReviewSign reviewSign = new ReviewSign();
        reviewSign.setReviewId(req.getReviewId());
        reviewSign.setFileOssId(fileOssId);
        reviewSign.setOriginFileOssId(req.getOriginFileOssId());
        reviewSign.setFileType(req.getFileType());
        reviewSign.setUserId(req.getUserId());
        reviewSign.setSectionId(req.getSectionId());
        reviewSign.setRemark(req.getRemark());
        this.save(reviewSign);
        // 保存文件至开评标备案
        OnlineConferenceReview review = onlineConferenceReviewService.getById(req.getReviewId());
        RBidDocument document = rBidDocumentService.getOne(new LambdaQueryWrapper<RBidDocument>().eq(RBidDocument::getBidRound, review.getBidRound())
                .eq(RBidDocument::getSectionId, req.getSectionId()).eq(RBidDocument::getCatalogueCode, FileDocumentCodeEnum.DFJLWJ.getCode()));
        if (document != null) {
            String fileIds = document.getFileIds();
            document.setFileIds(fileIds + "," + fileOssId);
        } else {
            document = new RBidDocument();
            document.setBidRound(review.getBidRound());
            document.setCatalogueCode(FileDocumentCodeEnum.DFJLWJ.getCode());
            document.setSectionId(req.getSectionId().toString());
            document.setFileIds(fileOssId.toString());
            document.setProjectId(review.getProjectId());
        }
        return rBidDocumentService.saveOrUpdate(document);
    }

    /**
     * 评审报告签章
     * @param req
     * @param file
     * @return
     */
    @Override
    public Boolean signForReport(ReviewSignReq req, MultipartFile file) {
        // 文件入库
        Long fileOssId = this.uploadSignFile(file);
        // 所有专家共同在源文件上签章
        ReviewSign reviewSign = this.getOne(new LambdaQueryWrapper<ReviewSign>().eq(ReviewSign::getReviewId, req.getReviewId())
                                .eq(ReviewSign::getSectionId, req.getSectionId()).eq(ReviewSign::getUserId, req.getUserId())
                                .eq(ReviewSign::getFileType, ReviewFileTypeEnum.REPORT.getType()));
        if (reviewSign == null) {
            reviewSign = new ReviewSign();
        }
        reviewSign.setReviewId(req.getReviewId());
        reviewSign.setFileOssId(fileOssId);
        reviewSign.setOriginFileOssId(req.getOriginFileOssId());
        reviewSign.setFileType(ReviewFileTypeEnum.REPORT.getType());
        reviewSign.setUserId(req.getUserId());
        reviewSign.setRemark(req.getRemark());
        reviewSign.setSectionId(req.getSectionId());
        this.saveOrUpdate(reviewSign);
        // 保存文件至开评标备案
        OnlineConferenceReview review = onlineConferenceReviewService.getById(req.getReviewId());
        RBidDocument document = rBidDocumentService.getOne(new LambdaQueryWrapper<RBidDocument>().eq(RBidDocument::getBidRound, review.getBidRound())
                .eq(RBidDocument::getSectionId, req.getSectionId()).eq(RBidDocument::getCatalogueCode, FileDocumentCodeEnum.PBBGWJ.getCode()));
        if (document != null) {
            document.setFileIds(fileOssId.toString());
        } else {
            document = new RBidDocument();
            document.setBidRound(review.getBidRound());
            document.setCatalogueCode(FileDocumentCodeEnum.PBBGWJ.getCode());
            document.setSectionId(req.getSectionId().toString());
            document.setFileIds(fileOssId.toString());
            document.setProjectId(review.getProjectId());
        }
        return rBidDocumentService.saveOrUpdate(document);
    }

    /**
     * 签章文件上传
     * @param file
     * @return
     */
    @Override
    public Long uploadSignFile(MultipartFile file) {
        OssFileStorage storage = OssUtil.getOssFileStorage(ossType);
        String key = UUID.randomUUID() +".pdf";
        try {
            storage.uploadFile(key, file.getInputStream());
            if(StringUtils.isNotBlank(key)) {
                OssFile ossFile = new OssFile();
                ossFile.setOssFileKey(key);
                ossFile.setOssFileName(file.getOriginalFilename());
                ossFileService.save(ossFile);
                return ossFile.getId();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 根据文件id获取oss文件
     * @param fileOssId
     * @param response
     */
    @Override
    public void getOssFile(Long fileOssId, HttpServletResponse response) {
        OssFile ossFile = ossFileService.getOssFileById(fileOssId);
        OssFileStorage storage = OssUtil.getOssFileStorage(ossType);
        byte[] bytes = storage.downloadFile(ossFile.getOssFileKey());
        if (null != bytes && bytes.length > 0) {
            try {
                response.reset();
                response.setCharacterEncoding("UTF-8");
                response.addHeader("Content-Disposition", "attachment;filename=" + new String(ossFile.getOssFileName().getBytes("GB2312"), "iso8859-1"));
                // 告知浏览器文件的大小
                response.addHeader("Content-Length", "" + bytes.length);
                OutputStream outputStream = new BufferedOutputStream(response.getOutputStream());
                response.setContentType("application/octet-stream");
                outputStream.write(bytes);
                outputStream.flush();
            } catch (Exception e) {
                e.printStackTrace();
            }
            /*OutputStream outputStream;
            InputStream inputStream = null;
            try {
                inputStream = FileUtils.byte2InputStream(bytes);
                response.setContentType("application/octet-stream");
                response.setCharacterEncoding("utf-8");
                response.setHeader("Content-disposition", "attachment;filename=" + new String(ossFile.getOssFileName().getBytes("GB2312"), "iso8859-1"));
                outputStream = new BufferedOutputStream(response.getOutputStream());
                byte[] b = new byte[1024];
                int len;
                while ((len = inputStream.read(b)) > 0) {
                    outputStream.write(b, 0, len);
                }
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                if (inputStream != null) {
                    try {
                        inputStream.close();
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }*/
        }
    }

    @Override
    public Result<OssFile> getSignProcess(Long id,Integer fileType) {
        LambdaQueryWrapper<ReviewSign> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ReviewSign::getReviewId,id).eq(ReviewSign::getFileType,fileType).orderByDesc(ReviewSign::getId).last("limit 1");
        ReviewSign one = getOne(queryWrapper);
        if(BeanUtil.isNotEmpty(one)){
            if(null != one.getFileOssId()){
                OssFile byId = ossFileService.getById(one.getFileOssId());
                if(BeanUtil.isNotEmpty(byId)) {
                    return Result.ok(byId);
                }else {
                    return Result.failed("签章文件未生成");
                }
            }else {
                return Result.failed("签章文件未生成");
            }
        } else {
            LambdaQueryWrapper<ReviewFile> fileWrapper =  new LambdaQueryWrapper<>();
            fileWrapper.eq(ReviewFile::getReviewId,id).eq(ReviewFile::getFileType,fileType).orderByDesc(ReviewFile::getUpdatedTime).last("limit 1");
            ReviewFile one1 = reviewFileService.getOne(fileWrapper);
            if(BeanUtil.isNotEmpty(one1)){
                OssFile byId = ossFileService.getById(one1.getFileOssId());
                if(BeanUtil.isNotEmpty(byId)) {
                    return Result.ok(byId);
                }else {
                    return Result.failed("签章文件未生成");
                }
            } else {
                return Result.failed("暂无签章文件，请联系项目经理完成文件样式确认");
            }
        }
    }

    @Override
    public Result<Long> querySignFile(Long reviewId, Integer fileType) {
        LambdaQueryWrapper<ReviewSign> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ReviewSign::getReviewId,reviewId).eq(ReviewSign::getFileType,fileType).orderByDesc(ReviewSign::getId).last("limit 1");
        ReviewSign one = getOne(queryWrapper);
        // 查询是否存在签章文件
        if(BeanUtil.isNotEmpty(one)){
            /*queryWrapper.eq(ReviewSign::getUserId, SecurityUtils.getJwtUser().getUserId());
            ReviewSign one1 = getOne(queryWrapper);
            // 查询当前登录人是否签过章
            if(BeanUtil.isNotEmpty(one1)){
                return Result.ok();
            }else {
                return Result.ok(one.getFileOssId());
            }*/
            return Result.ok(one.getFileOssId());
        } else {
            LambdaQueryWrapper<ReviewFile> fileLambdaQueryWrapper = new LambdaQueryWrapper<>();
            fileLambdaQueryWrapper.eq(ReviewFile::getReviewId,reviewId).eq(ReviewFile::getFileType,fileType);
            ReviewFile file = reviewFileService.getOne(fileLambdaQueryWrapper);
            if(BeanUtil.isNotEmpty(file)){
                return Result.ok(file.getFileOssId());
            }else {
                return Result.failed("暂无签章文件，请联系项目经理完成文件样式确认");
            }
        }
    }
}
