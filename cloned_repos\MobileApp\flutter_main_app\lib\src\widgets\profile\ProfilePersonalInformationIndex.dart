///***
/// * 我的 - 个人信息 - 入口页
/// */

import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_main_app/src/components/pickers/CustomChinaRegionsPicker.dart';

import 'package:flutter_main_app/src/constants/theme.dart';

import 'package:flutter_main_app/src/models/common/CheckBoxItemModel.dart';
import 'package:flutter_main_app/src/models/common/RegionModel.dart';

import 'package:flutter_main_app/src/components/CustomAppBar.dart';
import 'package:flutter_main_app/src/components/CustomSafeArea.dart';
import 'package:flutter_main_app/src/components/CustomListTile.dart';
import 'package:flutter_main_app/src/components/pickers/CustomSingleSelectPicker.dart';
import 'package:flutter_main_app/src/components/pickers/CustomDatePicker.dart';

class ProfilePersonalInformationIndex extends StatefulWidget {
  const ProfilePersonalInformationIndex({Key key}) : super(key: key);

  /// * 路由名称
  static const String _routeName = 'ProfilePersonalInformationIndex';
  static String get routeName => _routeName;

  @override
  _ProfilePersonalInformationIndexState createState() => _ProfilePersonalInformationIndexState();
}

class _ProfilePersonalInformationIndexState extends State<ProfilePersonalInformationIndex> {

  /// * 尾部图标
  static const Icon _trailingIcon = Icon(
    Icons.arrow_forward_ios,
    size: 14.0,
    color: Color(0xff999999),
  );

  /// * 头像边长
  static const double _avatarSideLength = 50.0;

  /// * 双字节字符的正则
  final RegExp _singleByteRe = RegExp(
    r'[\x00-\xff]',
    multiLine: false,
    caseSensitive: true,
  );

  /// * 昵称
  String _nickName;

  /// * 性别
  String _gender;

  /// * 性别设置
  List<CheckBoxItemModel<String>> _genderSettings;

  /// * 生日
  String _birthday;

  /// * 城市
  String _city;

  /// * 当前城市数据
  List<RegionModel> _currentRegions;

  /// * 职业
  String _profession;

  @override
  void initState() {
    super.initState();

    this._genderSettings = [
      CheckBoxItemModel(
        label: '男',
        value: '男',
        isChecked: false,
      ),
      CheckBoxItemModel(
        label: '女',
        value: '女',
        isChecked: false,
      ),
    ];
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: '个人信息',
        actions: <Widget>[
          GestureDetector(
            onTap: () {
              print('save pressed...');
            },
            child: Container(
              width: 58.0,
              height: 48.0,
              child: Center(
                child: Text(
                  '保存',
                ),
              ),
            ),
          ),
        ],
      ),
      body: CustomSafeArea(
        child: ListView(
          physics: BouncingScrollPhysics(),
          children: <Widget>[
            /// * 头像
            CustomListTile(
              title: '头像',
              minHeight: _avatarSideLength + 2 * 9.0,
              infos: <Widget>[
                Container(
                  width: _avatarSideLength,
                  height: _avatarSideLength,
                  decoration: BoxDecoration(
                    color: themeBackgroundColor,
                    borderRadius: BorderRadius.circular(_avatarSideLength),
                  ),
                  child: Image.asset(
                    'assets/images/qq.png',
                    fit: BoxFit.fill,
                  ),
                ),
              ],
              trailing: _trailingIcon,
              isFirstChild: true,
            ),
            /// * ID
            CustomListTile(
              title: 'ID',
              infos: <Widget>[
                Text(
                  '385549',
                  style: themeTipsTextStyle,
                ),
              ],
            ),
            /// * 昵称
            CustomListTile(
              title: '昵称',
              infos: <Widget>[
                Container(
                  width: this._nickName == null || this._nickName.length == 0
                    ? ('填写昵称'.length + 0.5) * themeFontSize
                    /// * (字符串长度 + 1.0 的富余 - 单字节字符长度的一半) * 字号
                    : (this._nickName.length + 1.0 - _singleByteRe.allMatches(this._nickName).length ~/ 2) * themeFontSize,
                  child: TextField(
                    onChanged: (String text) {
                      setState(() {
                        this._nickName = text;
                      });
                    },
                    textAlign: TextAlign.end,
                    decoration: InputDecoration(
                      contentPadding: const EdgeInsets.all(1.0),
                      hintText: '填写昵称',
                      hintStyle: themeHintTextStyle,
                      border: InputBorder.none,
                    ),
                    style: themeTipsTextStyle,
                  ),
                ),
              ],
            ),
            /// * 性别
            CustomListTile(
              title: '性别',
              infos: <Widget>[
                this._gender != null && this._gender.length > 0
                  ? Text(
                      this._gender,
                      style: themeTextStyle,
                    )
                  : Text(
                      '选择性别',
                      style: themeHintTextStyle,
                    ),
              ],
              trailing: _trailingIcon,
              onTap: () {
                this._pickGender();
              },
            ),
            /// * 生日
            CustomListTile(
              title: '生日',
              infos: <Widget>[
                this._birthday != null && this._birthday.length > 0
                  ? Text(
                    this._birthday,
                    style: themeTextStyle,
                  )
                  : Text(
                      '选择生日',
                      style: themeHintTextStyle,
                    ),
              ],
              trailing: _trailingIcon,
              onTap: () {
                this._pickBirthday();
              },
            ),
            /// * 城市
            CustomListTile(
              title: '城市',
              infos: <Widget>[
                this._city != null && this._city.length > 0
                  ? Text(
                    this._city,
                    style: themeTextStyle,
                  )
                  : Text(
                      '选择城市',
                      style: themeHintTextStyle,
                    ),
              ],
              trailing: _trailingIcon,
              onTap: () {
                this._pickCity();
              },
            ),
            /// * 职业
            CustomListTile(
              title: '职业',
              infos: <Widget>[
                Container(
                  width: this._profession == null || this._profession.length == 0
                    ? ('填写职业'.length + 0.5) * themeFontSize
                    /// * (字符串长度 + 1.0 的富余 - 单字节字符长度的一半) * 字号
                    : (this._profession.length + 1.0 - _singleByteRe.allMatches(this._profession).length ~/ 2) * themeFontSize,
                  child: TextField(
                    onChanged: (String text) {
                      setState(() {
                        this._profession = text;
                      });
                    },
                    textAlign: TextAlign.end,
                    decoration: InputDecoration(
                      contentPadding: const EdgeInsets.all(1.0),
                      hintText: '填写职业',
                      hintStyle: themeHintTextStyle,
                      border: InputBorder.none,
                    ),
                    style: themeTipsTextStyle,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  ///***
  /// * 选择性别
  /// */
  void _pickGender() {
    final CustomSingleSelectPicker _genderPicker = CustomSingleSelectPicker(
      settings: this._genderSettings,
      checkboxWidth: 120.0,
    );

    final Future<CheckBoxItemModel<String>> _modalBottomSheet = showModalBottomSheet<CheckBoxItemModel<String>>(
      context: context,
      builder: (BuildContext context) => _genderPicker,
    );

    _modalBottomSheet.then((CheckBoxItemModel<String> gender) {
      if (gender != null) {
        setState(() {
          this._gender = gender.label;

          for (int i = 0, len = this._genderSettings.length; i < len; i++) {
            if (this._genderSettings[i].value == gender.value) {
              this._genderSettings[i].isChecked = true;
              break;
            }
          }
        });
      } else {
        setState(() {
          this._gender = '';
        });
      }
    });
  }

  ///***
  /// * 选择生日
  /// */
  void _pickBirthday() {
    final CustomDatePicker _birthdayPicker = CustomDatePicker(
      title: '选择生日',
    );

    final Future<String> _modalPopup = showCupertinoModalPopup<String>(
      context: context,
      builder: (BuildContext context) {
        return _birthdayPicker;
      },
    );

    _modalPopup.then((String birthday) {
      if (birthday != null) {
        setState(() {
          this._birthday = birthday.substring(0, 10);
        });
      }
    });
  }

  ///***
  /// * 选择城市
  /// */
  void _pickCity() {
    final CustomChinaRegionsPicker _cityPicker = CustomChinaRegionsPicker(
      initialRegions: this._currentRegions ?? [],
    );

    final Future<List<RegionModel>> _modalBottomSheet = showModalBottomSheet<List<RegionModel>>(
      context: context,
      builder: (BuildContext context) {
        return _cityPicker;
      },
    );

    _modalBottomSheet.then((List<RegionModel> regions) {
      if (regions != null && regions.length == 2) {
        setState(() {
          this._currentRegions = List<RegionModel>.from(regions);
          this._city = '${regions[0].name}-${regions[1].name}';
        });
      }
    });
  }

}
