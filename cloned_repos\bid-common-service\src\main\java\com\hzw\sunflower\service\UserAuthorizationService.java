package com.hzw.sunflower.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hzw.sunflower.dto.UserAuthorizationDto;
import com.hzw.sunflower.entity.UserAuthorization;


/**
 * OpenService接口
 */
public interface UserAuthorizationService extends IService<UserAuthorization> {

    /**
     * 保存用户授权方式
     * @param dto
     */
    public Boolean saveUserAuthorization(UserAuthorizationDto dto);

    /**
     * 查询用户授权项目信息
     * @param userId
     * @param userIdentity
     * @return
     */
    UserAuthorizationDto getUserAuthorizationInfo(Long userId, Integer userIdentity);
}