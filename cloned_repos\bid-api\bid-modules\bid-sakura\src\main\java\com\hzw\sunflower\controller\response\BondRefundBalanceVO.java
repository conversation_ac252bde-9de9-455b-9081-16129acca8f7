package com.hzw.sunflower.controller.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <p>
 * 保证金退还余额
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-28
 */
@Data
public class BondRefundBalanceVO {

    @ApiModelProperty(value = "保证金余额")
    private BigDecimal balance;

    @ApiModelProperty(value = "保证金退还金额")
    private BigDecimal bondMoney;

    @ApiModelProperty(value = "退还账户")
    private String refundNumber;


}
