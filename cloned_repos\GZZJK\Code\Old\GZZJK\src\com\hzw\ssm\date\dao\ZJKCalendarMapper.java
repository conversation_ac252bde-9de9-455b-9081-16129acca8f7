package com.hzw.ssm.date.dao;

import java.util.List;

import com.hzw.ssm.date.entity.AutoCheckEntity;
import com.hzw.ssm.date.entity.CalendarEntity;
import com.hzw.ssm.sys.project.entity.ProjectEntity;

public interface ZJKCalendarMapper {
	
	public List<ProjectEntity> queryAutoProjectList(AutoCheckEntity autoCheckEntity);
	
	public AutoCheckEntity quertAutoCheck();
	
	public Integer queryCalendarExist(Integer year);
	
	public Integer insertCalendar(CalendarEntity entity);
	
	public Integer getCalendarStatus(String date_time);
	
	public Integer deleteCalendarForYear(Integer year);
	
	public String getCalendarStatus2(String date_time);
	
	public List<CalendarEntity> getCalendarByYear(Integer year);
	
	public List<CalendarEntity> getCalendarByYearMonth(CalendarEntity calendarEntity);
	
	public List<Integer> getAllYears();
	
	public int updateStatus(CalendarEntity calendarEntity);
	
	public int updateAutoCheck(ProjectEntity projectEntity);
}
