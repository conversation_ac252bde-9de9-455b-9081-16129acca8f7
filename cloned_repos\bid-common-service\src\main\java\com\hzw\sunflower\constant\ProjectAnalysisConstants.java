package com.hzw.sunflower.constant;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

public class ProjectAnalysisConstants {

    /**
     * 苏南(南京、无锡、常州、苏州、镇江)
     */
    public static final List<Integer> SOUTH_OF_JIANGSU = Arrays.asList(320100,320200,320400,320500,321100);
    /**
     * 苏中(扬州、泰州、南通)
     */
    public static final List<Integer> MID_OF_JIANGSU = Arrays.asList(321000,321200,320600);

    /**
     * 苏北(宿迁、淮安、盐城、徐州、连云港)
     */
    public static final List<Integer> NORTH_OF_JIANGSU = Arrays.asList(321300,320800,320900,320300,320700);

    public static  final Integer NANJING = 320100;
    public static  final Integer WUXI = 320200;
    public static  final Integer XUZHOU = 320300;
    public static  final Integer CHANGZHOU = 320400;
    public static  final Integer SUZHOU = 320500;
    public static  final Integer NANTONG = 320600;
    public static  final Integer LIANYUNGANG = 320700;
    public static  final Integer HUAIAN = 320800;
    public static  final Integer YANCHENG = 320900;
    public static  final Integer YANGZHOU = 321000;
    public static  final Integer ZHENJIANG = 321100;
    public static  final Integer TAIZHOU = 321200;
    public static  final Integer SUQIAN = 321300;

    public  static  final String SOUTHSU = "苏南";
    public  static  final String MIDSU = "苏中";
    public  static  final String NORTHSU = "苏北";
    public  static  final String OUTSU = "外省";
    public  static  final String ALLCOMPANY = "全公司";

    public static  final String NANJING_STR = "南京市";

    public static  final String WUXI_STR = "无锡市";

    public static  final String XUZHOU_STR = "徐州市";

    public static  final String CHANGZHOU_STR = "常州市";

    public static  final String SUZHOU_STR = "苏州市";

    public static  final String NANTONG_STR = "南通市";

    public static  final String LIANYUNGANG_STR = "连云港市";

    public static  final String HUAIAN_STR = "淮安市";

    public static  final String YANCHENG_STR = "盐城市";

    public static  final String YANGZHOU_STR = "扬州市";

    public static  final String ZHENJIANG_STR = "镇江市";

    public static  final String TAIZHOU_STR = "泰州市";

    public static  final String SUQIAN_STR = "宿迁市";
    public static final BigDecimal ZERO4 = new BigDecimal("0.0000");
    public static final BigDecimal ZERO2 = new BigDecimal("0.00");

    public static final BigDecimal TEN_THOUSAND = new BigDecimal("10000");

    public  static  final String ENGINEERING = "工程类";
    public  static  final String GOODS = "货物类";
    public  static  final String SERVICE = "服务类";


    public  static  final String ENGINEERING_CODE = "A";
    public  static  final String GOODS_CODE = "B";
    public  static  final String SERVICE_CODE = "C";

    //大项目边界金额
    public static final BigDecimal LARGE_PROJECT_DEMARCATION = new BigDecimal("30000000");

    //人民币编码
    public static  final  Integer RMB_CODE = 101;


}
