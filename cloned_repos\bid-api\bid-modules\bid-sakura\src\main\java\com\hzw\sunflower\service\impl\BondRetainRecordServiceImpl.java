package com.hzw.sunflower.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzw.sunflower.constant.constantenum.ChangeRecordTypeEnum;
import com.hzw.sunflower.controller.request.BondSectionReq;
import com.hzw.sunflower.dao.BondRetainRecordMapper;
import com.hzw.sunflower.dao.BondSplitMapper;
import com.hzw.sunflower.entity.BondChangeRecords;
import com.hzw.sunflower.entity.BondRetainRecord;
import com.hzw.sunflower.entity.BondSplit;
import com.hzw.sunflower.entity.BondWater;
import com.hzw.sunflower.service.BondChangeRecordsService;
import com.hzw.sunflower.service.BondRetainRecordService;
import com.hzw.sunflower.service.BondSplitService;
import com.hzw.sunflower.service.BondWaterService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 保证金保留记录服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-15
 */
@Service
public class BondRetainRecordServiceImpl extends ServiceImpl<BondRetainRecordMapper, BondRetainRecord> implements BondRetainRecordService {

    /**
     * 查询所有分批次退还记录的利息总和
     * @param refundId
     * @return
     */
    @Override
    public BigDecimal getCountRateByRefundId(Long refundId) {
        return this.baseMapper.getCountRateByRefundId(refundId);
    }
}
