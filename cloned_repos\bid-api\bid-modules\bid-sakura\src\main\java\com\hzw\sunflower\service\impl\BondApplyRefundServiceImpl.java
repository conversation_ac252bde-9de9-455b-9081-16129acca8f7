package com.hzw.sunflower.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.constant.CommonConstants;
import com.hzw.sunflower.constant.GeneralConstants;
import com.hzw.sunflower.constant.constantenum.*;
import com.hzw.sunflower.controller.request.*;
import com.hzw.sunflower.controller.response.*;
import com.hzw.sunflower.dao.*;
import com.hzw.sunflower.datascope.utils.DataScopeUtil;
import com.hzw.sunflower.dto.BondWaterSupplierDto;
import com.hzw.sunflower.entity.*;
import com.hzw.sunflower.entity.condition.BondApplyRefundCondition;
import com.hzw.sunflower.entity.condition.BondApplyRelationCondition;
import com.hzw.sunflower.entity.condition.BondNotRelationCondition;
import com.hzw.sunflower.exception.ExceptionEnum;
import com.hzw.sunflower.exception.SunFlowerException;
import com.hzw.sunflower.security.utils.SecurityUtils;
import com.hzw.sunflower.service.*;
import com.hzw.sunflower.util.BeanListUtil;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 保证金退还申请表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-27
 */
@Service
public class BondApplyRefundServiceImpl extends ServiceImpl<BondApplyRefundMapper, BondApplyRefund> implements BondApplyRefundService {

    @Autowired
    private BondWaterMapper bondWaterMapper;

    @Autowired
    private BondRelationMapper bondRelationMapper;

    @Autowired
    private BondApplyRefundMapper bondApplyRefundMapper;

    @Autowired
    private BondRefundMapper bondRefundMapper;

    @Autowired
    private BondApplyRefundProjectService bondApplyRefundProjectService;

    @Autowired
    private BondApplyRefundConfirmService bondApplyRefundConfirmService;

    @Autowired
    private BondApplyRefundConfirmMapper bondApplyRefundConfirmMapper;

    @Autowired
    private BondRefundService bondRefundService;

    @Autowired
    private BondApplyRelationMapper bondApplyRelationMapper;

    @Autowired
    private OssFileMapper ossFileMapper;

    @Autowired
    private BondWaterlabelService bondWaterlabelService;

    @Autowired
    private CnapsBankCodeService cnapsBankCodeService;

    @Autowired
    private BondCompanyBankService bondCompanyBankService;

    /**
     * 供应商未关联流水查询----待优化
     * @param condition
     * @param jwtUser
     * @return
     */
    @Override
    public IPage<BondNotRelationVo> getBondWaterNotRelationPage(BondNotRelationCondition condition, JwtUser jwtUser) {
        Page<BondNotRelationVo> bondNotRelationVoPage = new Page<>();
        BondWaterSupplierDto bsd = new BondWaterSupplierDto();
        //LambdaQueryWrapper<BondWater> queryWrapper = new LambdaQueryWrapper<>();
        //查询当前登录供应商公司名称
        String companyName = bondApplyRefundMapper.selectCompanyName(jwtUser.getUserId());
        //queryWrapper.eq(BondWater::getCompanyName,companyName);
        bsd.setCompanyName(companyName);
        //查询所有已关联的流水id
        LambdaQueryWrapper<BondRelation> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        List<BondRelation> bondRelations = bondRelationMapper.selectList(lambdaQueryWrapper);
        List<Long> relationWaterIds = new ArrayList<>();
        for (BondRelation bondRelation : bondRelations) {
            relationWaterIds.add(bondRelation.getWaterId());
        }
        if (relationWaterIds.size() > 0) {
            //queryWrapper.notIn(BondWater::getId,relationWaterIds);
            bsd.setRelationWaterIds(relationWaterIds);
        }

        //查询所有已申请退款的流水
        LambdaQueryWrapper<BondApplyRefund> bondApplyRefundLambdaQueryWrapper = new LambdaQueryWrapper<>();
        List<BondApplyRefund> bondApplyRefunds = bondApplyRefundMapper.selectList(bondApplyRefundLambdaQueryWrapper);
        //已申请退款的流水id
        List<Long> applyWaterIds = new ArrayList<>();
        for (BondApplyRefund bondApplyRefund : bondApplyRefunds) {
            applyWaterIds.add(bondApplyRefund.getWaterId());
        }
        //交易金额搜索
        if (StringUtils.isNotBlank(condition.getAmount())) {
            //queryWrapper.eq(BondWater::getAmount,condition.getAmount());
            bsd.setAmount(condition.getAmount());
        }
       // queryWrapper.orderByAsc(BondWater::getDate).orderByAsc(BondWater::getTime);
        if (condition.getDate() != null) {
            //queryWrapper.eq(BondWater::getDate,condition.getDate());
            bsd.setDate(DateUtil.formatDate(condition.getDate()));
        }

        //全部列表查询
        if (condition.getStatus() == null) {
            //根据条件查询流水
            Page<BondWater> page = condition.buildPage();
            //Page<BondWater> bondWaters = bondWaterMapper.selectSupplierPage(page,queryWrapper);
            Page<BondWater> bondWaters = bondWaterMapper.selectSupplierPage(page,bsd);
            //拼装返回值分页数据
            BeanUtils.copyProperties(bondWaters,bondNotRelationVoPage);
            List<BondWater> records = bondWaters.getRecords();
            List<BondNotRelationVo> vos = BeanListUtil.convertList(records,BondNotRelationVo.class);
            bondNotRelationVoPage.setRecords(vos);

            //每笔流水添加状态
            for (BondNotRelationVo record : bondNotRelationVoPage.getRecords()) {
                //待申请退款
                if (!checkId(record.getId(),applyWaterIds)){
                    record.setStatus(BondWaterNotRelationEnum.WAIT_APPLY_REFUND.getType());
                }
                //已申请退款
                if (checkId(record.getId(),applyWaterIds)) {
                    //查询该流水申请退款的申请状态(运管处)
                    QueryWrapper<BondApplyRefund> bondApplyRefundQueryWrapper = new QueryWrapper<>();
                    bondApplyRefundQueryWrapper.lambda().eq(BondApplyRefund::getWaterId,record.getId());
                    BondApplyRefund bondApplyRefundsList = bondApplyRefundMapper.selectOne(bondApplyRefundQueryWrapper);
                    //获取最新一条退款申请的申请状态
                    //1：待处理  2：已处理待确认 3：无处室认领  --未关联流水申请待处理(运管处)
                    if (bondApplyRefundsList != null && (bondApplyRefundsList.getStatus().equals(BondApplyRefundStatus.WAIT_HANDLE.getType())
                            || bondApplyRefundsList.getStatus().equals(BondApplyRefundStatus.HANDLE_WAIT_CONFIRM.getType())
                            || bondApplyRefundsList.getStatus().equals(BondApplyRefundStatus.NO_DIVISION_CONFIRM.getType()))) {
                        record.setStatus(BondWaterNotRelationEnum.WAIT_DISPOSE.getType());
                    }
                    //4：处室不同意  6.已退回（运管处）  --未关联流水申请已退回(运管处)
                    if (bondApplyRefundsList != null &&(bondApplyRefundsList.getStatus().equals(BondApplyRefundStatus.DIVISION_NO_CONFIRM.getType())
                            || bondApplyRefundsList.getStatus().equals(BondApplyRefundStatus.RETURNED.getType()))) {
                        record.setStatus(BondWaterNotRelationEnum.ALREADY_RETURN.getType());
                    }
                    //5：处室同意（退款中）,财务处理
                    if (bondApplyRefundsList != null && (bondApplyRefundsList.getStatus().equals(BondApplyRefundStatus.DIVISION_CONFIRM.getType()))) {
                        //查询该流水申请退款的退款状态(财务)
                        QueryWrapper<BondRefund> bondRefundQueryWrapper = new QueryWrapper<>();
                        bondRefundQueryWrapper.lambda().eq(BondRefund::getApplyRefundId,bondApplyRefundsList.getId());
                        BondRefund bondRefund = bondRefundMapper.selectOne(bondRefundQueryWrapper);
                        //1：待处理  5.退还中  --未关联流水申请退款中(财务)
                        if (bondRefund != null && (bondRefund.getStatus().equals(BondRefundStatusEnum.WAIT.getType())
                                || bondRefund.getStatus().equals(BondRefundStatusEnum.REFUNDING.getType()))) {
                            record.setStatus(BondWaterNotRelationEnum.IN_REFUND.getType());
                        }
                        //3：退还成功  --未关联流水申请已退款(财务)
                        if (bondRefund != null && (bondRefund.getStatus().equals(BondRefundStatusEnum.SUCCESS.getType()))) {
                            record.setStatus(BondWaterNotRelationEnum.ALREADY_REFUND.getType());
                        }

                    }
                }

            }
        }
        //选择状态
        if (condition.getStatus() != null) {
            Page<BondWater> page = condition.buildPage();
            //待申请退款
            if (condition.getStatus().equals(BondWaterNotRelationEnum.WAIT_APPLY_REFUND.getType())) {
                if (applyWaterIds.size() > 0) {
                    //queryWrapper.notIn(BondWater::getId,applyWaterIds);
                    bsd.setRelationWaterIds(applyWaterIds);
                }
                //交易日期
                if (condition.getDate() != null) {
                    //queryWrapper.eq(BondWater::getDate,condition.getDate());
                    bsd.setDate(DateUtil.formatDate(condition.getDate()));
                }
                //根据条件查询流水
                //Page<BondWater> bondWaters = bondWaterMapper.selectPage(page,queryWrapper);
                Page<BondWater> bondWaters = bondWaterMapper.selectSupplierPage(page,bsd);
                bondNotRelationVoPage = copyBondWater(bondWaters, bondNotRelationVoPage,condition.getStatus());
            }
            //待处理,已退回(运管处)
            if (condition.getStatus().equals(BondWaterNotRelationEnum.WAIT_DISPOSE.getType()) || condition.getStatus().equals(BondWaterNotRelationEnum.ALREADY_RETURN.getType())) {
                IPage<BondWater> bondWaters = bondApplyRefundMapper.bondApplyWaitDisposeList(page,condition,companyName,relationWaterIds);
                bondNotRelationVoPage = copyBondWater(bondWaters, bondNotRelationVoPage,condition.getStatus());
            }
            //退款中,已退款(财务)
            if (condition.getStatus().equals(BondWaterNotRelationEnum.IN_REFUND.getType()) || condition.getStatus().equals(BondWaterNotRelationEnum.ALREADY_REFUND.getType())) {
                IPage<BondWater> bondWaters = bondApplyRefundMapper.bondApplyRefundList(page,condition,companyName,relationWaterIds);
                bondNotRelationVoPage = copyBondWater(bondWaters, bondNotRelationVoPage,condition.getStatus());
            }
//            //已退款
//            if (condition.getStatus().equals(BondWaterNotRelationEnum.ALREADY_REFUND.getType())) {
//
//            }
//            //已退回
//            if (condition.getStatus().equals(BondWaterNotRelationEnum.ALREADY_RETURN.getType())) {
//
//            }
        }
        return bondNotRelationVoPage;
    }

    @Override
    public IPage<BondNotRelationVo> getBondWaterNotRelationPageNew(BondNotRelationCondition condition, JwtUser jwtUser) {
        //查询当前登录供应商公司名称
        String companyName = bondApplyRefundMapper.selectCompanyName(jwtUser.getUserId());
        condition.setCompanyName(companyName);
        Page<BondNotRelationVo> page = condition.buildPage();
        return this.baseMapper.getBondWaterNotRelationPageNew(page,condition);
    }

    /**
     * 查询当前登录供应商所在公司参与的所有项目信息
     * @param req
     * @param jwtUser
     * @return
     */
    @Override
    public List<MyCompanyProjectVo> getMyCompanyProject(MyCompanyProjectReq req, JwtUser jwtUser) {
        List<MyCompanyProjectVo> companyProjectVoList = bondApplyRefundMapper.getMyCompanyProject(req,jwtUser.getUserId());
        return companyProjectVoList;
    }

    /**
     * 根据项目id获取当前供应商参与是标包信息
     * @param req
     * @param jwtUser
     * @return
     */
    @Override
    public List<MyCompanyProjectVo> getMyCompanySection(MyCompanyProjectReq req, JwtUser jwtUser) {
        if (req == null || req.getProjectId() == null) {
            return null;
        }
        List<MyCompanyProjectVo> companyProjectVoList = bondApplyRefundMapper.getMyCompanySection(req,jwtUser.getUserId());
        return companyProjectVoList;
    }

    /**
     * 异常退款申请/编辑(供应商)
     * @param req
     * @return
     */
    @Override
    public Boolean saveBondAbnormalApplyRefund(BondAbnormalApplyRefundReq req) {
        //根据南京银行给到的库判断正误，（如无南京银行库或无法查询到则不做校验）
        LambdaQueryWrapper<CnapsBankCode> cnapsBankCodeLambdaQueryWrapper = new LambdaQueryWrapper<>();
        cnapsBankCodeLambdaQueryWrapper.eq(CnapsBankCode::getOrgFullname,req.getCompanyBankDeposit()).or().eq(CnapsBankCode::getBankCode,req.getCompanyBankCode());
        List<CnapsBankCode> cnapsBankCodeList = cnapsBankCodeService.list(cnapsBankCodeLambdaQueryWrapper);
        if (cnapsBankCodeList.size() == 1) {
            CnapsBankCode cnapsBankCode = cnapsBankCodeList.get(0);
            if (!cnapsBankCode.getOrgFullname().equals(req.getCompanyBankDeposit()) || !cnapsBankCode.getBankCode().equals(req.getCompanyBankCode())) {
                throw new SunFlowerException(ExceptionEnum.ACCOUNT_BANK_NUMBER_ERROR, ExceptionEnum.ACCOUNT_BANK_NUMBER_ERROR.getMessage());
            }
        }
        if (cnapsBankCodeList.size() > 1) {
            throw new SunFlowerException(ExceptionEnum.ACCOUNT_BANK_NUMBER_ERROR, ExceptionEnum.ACCOUNT_BANK_NUMBER_ERROR.getMessage());
        }
        //1.编辑银行行号
        LambdaUpdateWrapper<BondCompanyBank> bondCompanyBankLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        bondCompanyBankLambdaUpdateWrapper.set(BondCompanyBank::getCompanyBankDeposit,req.getCompanyBankDeposit());
        bondCompanyBankLambdaUpdateWrapper.set(BondCompanyBank::getCompanyBankCode,req.getCompanyBankCode());
        bondCompanyBankLambdaUpdateWrapper.eq(BondCompanyBank::getCompanyId,SecurityUtils.getJwtUser().getCompanyId());
        bondCompanyBankService.update(bondCompanyBankLambdaUpdateWrapper);
        if (req.getApplyId()==null) {
            //申请
            // 判断此流水是否已退还或被关联
            List<Long> waterIdList = new ArrayList<>();
            waterIdList.add(req.getWaterId());
            if (bondRefundService.judgeWaterInReturn(waterIdList)) {
                throw new SunFlowerException(ExceptionEnum.WATER_IN_RETURN, ExceptionEnum.WATER_IN_RETURN.getMessage());
            }
            List<BondRelation> relationList = bondRelationMapper.selectList(new LambdaQueryWrapper<BondRelation>().eq(BondRelation::getWaterId, req.getWaterId()));
            if (relationList.size() > 0) {
                throw new SunFlowerException(ExceptionEnum.WATER_IN_PROCESS, ExceptionEnum.WATER_IN_PROCESS.getMessage());
            }
            // 判断异常流水
            if (bondWaterlabelService.chekecWaterLabel(waterIdList) > 0) {
                throw new SunFlowerException(ExceptionEnum.WATER_ERROR, ExceptionEnum.WATER_ERROR.getMessage());
            }

            //查询流水
            BondWater bondWater = bondWaterMapper.selectById(req.getWaterId());
            BondApplyRefund bondApplyRefund = new BondApplyRefund();
            //流水id
            bondApplyRefund.setWaterId(req.getWaterId());
            //保证金退还金额
            bondApplyRefund.setRefundMoney(NumberUtils.createBigDecimal(bondWater.getAmount()));
            //状态(待处理)
            bondApplyRefund.setStatus(BondApplyRefundStatus.WAIT_HANDLE.getType());
//        //处理方式
//        bondApplyRefund.setHandleType();
            //申请时间
            bondApplyRefund.setApplyTime(new Date());
            //付款凭证
            bondApplyRefund.setFileIds(req.getFileIds().toString());
            //退款说明函
            bondApplyRefund.setRefundFiles(req.getRefundFiles().toString());
            boolean save = this.save(bondApplyRefund);
            if (save && req.getProjectSections() != null && req.getProjectSections().size() > 0) {
                List<Long> projectIdList = new ArrayList<>();
                //同步插入保证金申请退还项目信息表
                List<BondApplyRefundProject> bondApplyRefundProjects = new ArrayList<>();
                for (BondApplyRefundProjectReq projectSection : req.getProjectSections()) {
                    BondApplyRefundProject bondApplyRefundProject = new BondApplyRefundProject();
                    //申请退还id
                    bondApplyRefundProject.setApplyRefundId(bondApplyRefund.getId());
                    //项目id
                    bondApplyRefundProject.setProjectId(projectSection.getProjectId());
                    //标段id
                    bondApplyRefundProject.setSectionId(projectSection.getSectionId());
                    bondApplyRefundProjects.add(bondApplyRefundProject);
                    projectIdList.add(projectSection.getProjectId());
                }
                //批量插入
                bondApplyRefundProjectService.saveBatch(bondApplyRefundProjects);
                //直接到处长审批
                bondApplyRefund.setStatus(BondApplyRefundStatus.HANDLE_WAIT_CONFIRM.getType());
                bondApplyRefund.setHandleType(CommonConstants.YES);
                this.updateById(bondApplyRefund);
                //查询处长并设置待确认
                //查询部门处长
                List<String> roleCodes = new ArrayList<>();
                roleCodes.add(RoleCodeEnum.CHUZHANG.getType());
                projectIdList = projectIdList.stream().distinct().collect(Collectors.toList());
                List<DepartmentUserVo> departmentUserVoList = bondApplyRefundMapper.queryDepartCZByProject(projectIdList,roleCodes);
                List<BondApplyRefundConfirm> bondApplyRefundConfirmList = new ArrayList<>();
                for (DepartmentUserVo departmentUserVo : departmentUserVoList) {
                    BondApplyRefundConfirm bondApplyRefundConfirm = new BondApplyRefundConfirm();
                    bondApplyRefundConfirm.setApplyRefundId(bondApplyRefund.getId());
                    bondApplyRefundConfirm.setDepartmentId(departmentUserVo.getDepartmentId());
                    bondApplyRefundConfirm.setUserId(departmentUserVo.getUserId());
                    bondApplyRefundConfirm.setDepartmentDisposeStatus(RegistrarConfirmStatusEnum.WAIT_DISPOSE.getType());
                    bondApplyRefundConfirmList.add(bondApplyRefundConfirm);
                }
                bondApplyRefundConfirmService.saveBatch(bondApplyRefundConfirmList);
            }
            return save;
        } else {
            BondApplyRefund refund = getById(req.getApplyId());
            if(!BondApplyRefundStatus.DIVISION_NO_CONFIRM.getType().equals(refund.getStatus()) && !BondApplyRefundStatus.RETURNED.getType().equals(refund.getStatus())){
                throw new SunFlowerException(ExceptionEnum.DATA_IS_PASS, ExceptionEnum.DATA_IS_PASS.getMessage());
            }
            //编辑
            BondApplyRefund bondApplyRefund = new BondApplyRefund();
            //申请id
            bondApplyRefund.setId(req.getApplyId());
            //状态(待处理)
            bondApplyRefund.setStatus(BondApplyRefundStatus.WAIT_HANDLE.getType());
            //申请时间
            bondApplyRefund.setApplyTime(new Date());
            //付款凭证
            bondApplyRefund.setFileIds(req.getFileIds().toString());
            //退款说明函
            bondApplyRefund.setRefundFiles(req.getRefundFiles().toString());
            //处理方式初始化
            bondApplyRefund.setHandleType(CommonConstants.NO);
            boolean update = this.updateById(bondApplyRefund);
            if (update && req.getProjectSections() != null && req.getProjectSections().size() > 0) {
                //删除上次申请关联的项目
                LambdaQueryWrapper<BondApplyRefundProject> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                lambdaQueryWrapper.eq(BondApplyRefundProject::getApplyRefundId,req.getApplyId());
                bondApplyRefundProjectService.remove(lambdaQueryWrapper);
                List<Long> projectIdList = new ArrayList<>();
                //同步更新保证金申请退还项目信息表
                List<BondApplyRefundProject> bondApplyRefundProjects = new ArrayList<>();
                for (BondApplyRefundProjectReq projectSection : req.getProjectSections()) {
                    BondApplyRefundProject bondApplyRefundProject = new BondApplyRefundProject();
                    //申请退还id
                    bondApplyRefundProject.setApplyRefundId(req.getApplyId());
                    //项目id
                    bondApplyRefundProject.setProjectId(projectSection.getProjectId());
                    //标段id
                    bondApplyRefundProject.setSectionId(projectSection.getSectionId());
                    bondApplyRefundProjects.add(bondApplyRefundProject);
                    projectIdList.add(projectSection.getProjectId());
                }
                //批量插入
                bondApplyRefundProjectService.saveBatch(bondApplyRefundProjects);
                //直接到处长审批
                bondApplyRefund.setStatus(BondApplyRefundStatus.HANDLE_WAIT_CONFIRM.getType());
                bondApplyRefund.setHandleType(CommonConstants.YES);
                this.updateById(bondApplyRefund);
                //查询处长并设置待确认
                //查询部门处长
                List<String> roleCodes = new ArrayList<>();
                roleCodes.add(RoleCodeEnum.CHUZHANG.getType());
                projectIdList = projectIdList.stream().distinct().collect(Collectors.toList());
                List<DepartmentUserVo> departmentUserVoList = bondApplyRefundMapper.queryDepartCZByProject(projectIdList,roleCodes);
                List<BondApplyRefundConfirm> bondApplyRefundConfirmList = new ArrayList<>();
                for (DepartmentUserVo departmentUserVo : departmentUserVoList) {
                    BondApplyRefundConfirm bondApplyRefundConfirm = new BondApplyRefundConfirm();
                    bondApplyRefundConfirm.setApplyRefundId(bondApplyRefund.getId());
                    bondApplyRefundConfirm.setDepartmentId(departmentUserVo.getDepartmentId());
                    bondApplyRefundConfirm.setUserId(departmentUserVo.getUserId());
                    bondApplyRefundConfirm.setDepartmentDisposeStatus(RegistrarConfirmStatusEnum.WAIT_DISPOSE.getType());
                    bondApplyRefundConfirmList.add(bondApplyRefundConfirm);
                }
                //删除上一次申请的数据
                LambdaQueryWrapper<BondApplyRefundConfirm> bondApplyRefundConfirmLambdaQueryWrapper = new LambdaQueryWrapper<>();
                bondApplyRefundConfirmLambdaQueryWrapper.eq(BondApplyRefundConfirm::getApplyRefundId,req.getApplyId());
                bondApplyRefundConfirmService.remove(bondApplyRefundConfirmLambdaQueryWrapper);
                //保存
                bondApplyRefundConfirmService.saveBatch(bondApplyRefundConfirmList);
            }
            return update;
        }

    }

    /**
     * 根据退款流水id查询异常退款申请信息
     * @param req
     * @return
     */
    @Override
    public BondAbnormalApplyRefundVo getBondAbnormalApplyRefund(BondAbnormalApplyRefundReq req) {
        BondAbnormalApplyRefundVo bondAbnormalApplyRefundVo = new BondAbnormalApplyRefundVo();
        LambdaQueryWrapper<BondApplyRefund> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(BondApplyRefund::getWaterId,req.getWaterId());
        BondApplyRefund bondApplyRefund = this.getOne(lambdaQueryWrapper);
        //申请退还表信息
        bondAbnormalApplyRefundVo.setBondApplyRefund(bondApplyRefund);
        //申请退还项目信息表
        LambdaQueryWrapper<BondApplyRefundProject> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BondApplyRefundProject::getApplyRefundId,bondApplyRefund.getId());
        List<BondApplyRefundProject> list = bondApplyRefundProjectService.list(queryWrapper);
        bondAbnormalApplyRefundVo.setBondApplyRefundProjectList(list);
        return bondAbnormalApplyRefundVo;
    }

    /**
     * 异常退款申请待处理列表(由供应商发起)
     * @param condition
     * @return
     */
    @Override
    public IPage<BondApplyRefundVo> getBondApplyRefundList(BondApplyRefundCondition condition) {
        Page<BondApplyRefundVo> page = condition.buildPage();
        IPage<BondApplyRefundVo> bondApplyRefundPage = bondApplyRefundMapper.selectBondApplyRefundPage(page,condition);
        //增加退回原因
        for(BondApplyRefundVo vo:bondApplyRefundPage.getRecords()){
            //若处室不同意，增加不同意原因，取时间最新的一个
            if(BondApplyRefundStatus.DIVISION_NO_CONFIRM.getType().equals(vo.getStatus())){
                BondApplyRefundConfirm confirm = bondApplyRefundMapper.getReturnList(vo.getId());
                if(confirm!=null){
                    vo.setRemark(confirm.getRemark());
                }
            }
        }
        //拼接财务付款状态
        // 通过sql处理
     /* for (BondApplyRefundVo bondApplyRefundVo : bondApplyRefundPage.getRecords()) {
            //如果是运管处已处理，则将状态改为财务付款状态
            if (BondApplyRefundStatus.DIVISION_CONFIRM.getType().equals(bondApplyRefundVo.getStatus()) || bondApplyRefundVo.getPayStatus() != null) {
                if (BondRefundStatusEnum.WAIT.getType().equals(bondApplyRefundVo.getPayStatus())) {
                    bondApplyRefundVo.setStatus(BondApplyRefundStatus.FINANCE_WAIT.getType());
                } else if (BondRefundStatusEnum.RETURNED.getType().equals(bondApplyRefundVo.getPayStatus())) {
                    bondApplyRefundVo.setStatus(BondApplyRefundStatus.FINANCE_RETURNED.getType());
                } else if (BondRefundStatusEnum.SUCCESS.getType().equals(bondApplyRefundVo.getPayStatus())) {
                    bondApplyRefundVo.setStatus(BondApplyRefundStatus.FINANCE_SUCCESS.getType());
                } else if (BondRefundStatusEnum.FAIL.getType().equals(bondApplyRefundVo.getPayStatus())) {
                    bondApplyRefundVo.setStatus(BondApplyRefundStatus.FINANCE_FAIL.getType());
                } else if (BondRefundStatusEnum.REFUNDING.getType().equals(bondApplyRefundVo.getPayStatus())) {
                    bondApplyRefundVo.setStatus(BondApplyRefundStatus.FINANCE_REFUNDING.getType());
                }
            }
        } */
        return bondApplyRefundPage;
    }

    /**
     * 异常退款申请(供应商发起)详情
     * @param req
     * @return
     */
    @Override
    public BondAbnormalApplyRefundDetailVo bondAbnormalApplyRefundDetail(BondApplyRefundDetailReq req,JwtUser jwtUser) {
        //如果是从处长待处理列表跳转
        if (req.getApplyId() == null && req.getApplyRefundIdConfirmId() != null) {
            BondApplyRefundConfirm bondApplyRefundConfirm = bondApplyRefundConfirmMapper.selectById(req.getApplyRefundIdConfirmId());
            if (bondApplyRefundConfirm != null) {
                req.setApplyId(bondApplyRefundConfirm.getApplyRefundId());
            }else{
                throw new SunFlowerException(ExceptionEnum.DATA_IS_PASS, ExceptionEnum.DATA_IS_PASS.getMessage());
            }
        }
        BondAbnormalApplyRefundDetailVo bondAbnormalApplyRefundDetailVo = new BondAbnormalApplyRefundDetailVo();
        if (req.getApplyId() == null) {
            return bondAbnormalApplyRefundDetailVo;
        }
        //申请退还信息
        BondApplyRefund bondApplyRefund = bondApplyRefundMapper.selectById(req.getApplyId());
        //申请状态
        bondAbnormalApplyRefundDetailVo.setStatus(bondApplyRefund.getStatus());
        //申请id
        bondAbnormalApplyRefundDetailVo.setApplyId(bondApplyRefund.getId());
        //处理方式
        bondAbnormalApplyRefundDetailVo.setHandleType(bondApplyRefund.getHandleType());

        //供应商信息
        VendorVo vendorVo = bondApplyRefundMapper.getVendor(req.getApplyId());
        bondAbnormalApplyRefundDetailVo.setVendorVo(vendorVo);

        //选择的项目信息
        ChooseProjectVo chooseProjectVo = new ChooseProjectVo();
        //项目信息
        List<MyCompanyProjectVo> chooseProjects = bondApplyRefundMapper.getChooseProjects(req.getApplyId());
        //申请退还的关联项目
        chooseProjectVo.setMyCompanyProjectVos(chooseProjects);
        //付款凭证
        chooseProjectVo.setFileIds(bondApplyRefund.getFileIds());
        if (bondApplyRefund.getFileIds() != null) {
            OssFile ossFile = ossFileMapper.selectById(Long.valueOf(bondApplyRefund.getFileIds()));
            if (ossFile != null) {
                chooseProjectVo.setFileKey(ossFile.getOssFileKey());
            }
        }
        //退款说明函
        chooseProjectVo.setRefundFiles(bondApplyRefund.getRefundFiles());
        if (bondApplyRefund.getRefundFiles() != null) {
            OssFile ossFile = ossFileMapper.selectById(Long.valueOf(bondApplyRefund.getRefundFiles()));
            if (ossFile != null) {
                chooseProjectVo.setRefundFileKey(ossFile.getOssFileKey());
            }
        }
        bondAbnormalApplyRefundDetailVo.setChooseProjectVo(chooseProjectVo);

        // 处理数据权限
        String datascopesql= DataScopeUtil.getProjectDataScopeFilterDepartSql("ud","eu","p");
        //参与的项目信息
        List<MyCompanyProjectVo> myCompanyProjectVos = bondApplyRefundMapper.getVendorProjects(bondApplyRefund.getCreatedUserId(),datascopesql);
        myCompanyProjectVos.forEach(l->l.setPackagesStatusCode(Arrays.asList(l.getPackagesStatus().split(GeneralConstants.SPLICING_PARAMETER_METHOD))));
        bondAbnormalApplyRefundDetailVo.setMyCompanyProjectVos(myCompanyProjectVos);

        //处长处理详情
        List<DepartmentUserVo> departmentUserVos = bondApplyRefundMapper.getDepartmentResult(req.getApplyId());
        //统计数据
        int waitDisposeTotals = 0;
        int noDivisionTotals = 0;
        for (DepartmentUserVo departmentUserVo : departmentUserVos) {
            //待处理
            if (departmentUserVo.getDepartmentDisposeStatus().equals(RegistrarConfirmStatusEnum.WAIT_DISPOSE.getType())) {
                waitDisposeTotals++;
            }
            //非本处项目
            if (departmentUserVo.getDepartmentDisposeStatus().equals(RegistrarConfirmStatusEnum.NO_DIVISION.getType())) {
                noDivisionTotals++;
            }
        }
        bondAbnormalApplyRefundDetailVo.setAllTotals(departmentUserVos.size());
        bondAbnormalApplyRefundDetailVo.setWaitDisposeTotals(waitDisposeTotals);
        bondAbnormalApplyRefundDetailVo.setNoDivisionTotals(noDivisionTotals);
        bondAbnormalApplyRefundDetailVo.setDepartmentUserVos(departmentUserVos);

        //处长处理该条申请
        if (req.getApplyRefundIdConfirmId()!=null) {
            bondAbnormalApplyRefundDetailVo.setApplyRefundIdConfirmId(req.getApplyRefundIdConfirmId());
        }

        return bondAbnormalApplyRefundDetailVo;
    }

    /**
     * 异常退款申请运管处处理查询供应商参与的项目
     * @param req
     * @return
     */
    @Override
    public IPage<MyCompanyProjectVo> bondAbnormalApplyRefundProject(BondApplyRefundProjectDetailReq req) {
        //查询申请人所在公司
        Long companyId = bondApplyRefundMapper.queryApplyUserCompanyId(req.getApplyId());
        req.setCompanyId(companyId);
        Page<BondNotRelationVo> page = req.buildPage();
        return bondApplyRefundMapper.queryVendorProjects(page, req);
    }

    /**
     * 异常退款申请(供应商发起)运管处处理
     * @param req
     * @return
     */
    @Override
    public Boolean saveBondAbnormalApplyRefundConfirm(BondAbnormalApplyRefundConfirmReq req) {
        boolean result = false;
        BondApplyRefund bondApplyRefund = new BondApplyRefund();
        List<BondApplyRefundConfirm> bondApplyRefundConfirmList = new ArrayList<>();
        //指定处室处长确认
        if (req.getConfirmType().equals(BondApplyRefundHandleType.DESIGNATE_REGISTRAR_CONFIRM.getType())) {
            //处室id去重
            List<Long> departmentIds = req.getDepartments().stream().distinct().collect(Collectors.toList());
            //查询部门处长
            List<String> roleCodes = new ArrayList<>();
            roleCodes.add(RoleCodeEnum.CHUZHANG.getType());
            List<DepartmentUserVo> departmentUserVoList = bondApplyRefundMapper.getDepartments(departmentIds,roleCodes);
            //过滤掉三处的车芸、十二处的王圣喜（移除过滤）
            //departmentUserVoList = departmentUserVoList.stream().filter(x -> !x.getUserName().equals(BondConstants.THREE_REGISTRAR) && !x.getUserName().equals(BondConstants.TWELVE_REGISTRAR)).collect(Collectors.toList());
            for (DepartmentUserVo departmentUserVo : departmentUserVoList) {
                BondApplyRefundConfirm bondApplyRefundConfirm = new BondApplyRefundConfirm();
                bondApplyRefundConfirm.setApplyRefundId(req.getApplyId());
                bondApplyRefundConfirm.setDepartmentId(departmentUserVo.getDepartmentId());
                bondApplyRefundConfirm.setUserId(departmentUserVo.getUserId());
                bondApplyRefundConfirm.setDepartmentDisposeStatus(RegistrarConfirmStatusEnum.WAIT_DISPOSE.getType());
                bondApplyRefundConfirmList.add(bondApplyRefundConfirm);
            }
            //更改申请表状态为2：已处理待确认
            bondApplyRefund.setId(req.getApplyId());
            bondApplyRefund.setHandleType(req.getConfirmType());
            bondApplyRefund.setStatus(BondApplyRefundStatus.HANDLE_WAIT_CONFIRM.getType());
            this.updateById(bondApplyRefund);
            //删除上一次申请的数据
            LambdaQueryWrapper<BondApplyRefundConfirm> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(BondApplyRefundConfirm::getApplyRefundId,req.getApplyId());
            bondApplyRefundConfirmService.remove(lambdaQueryWrapper);
            //保证金申请退还处室处长确认表添加数据
            result = bondApplyRefundConfirmService.saveBatch(bondApplyRefundConfirmList);
        }
        //所有处室处长确认
        if (req.getConfirmType().equals(BondApplyRefundHandleType.ALL_REGISTRAR_CONFIRM.getType())) {
            //目前共17个处室
            List<Long> departmentId = new ArrayList<>();
            //查询部门处长
            List<String> roleCodes = new ArrayList<>();
            roleCodes.add(RoleCodeEnum.CHUZHANG.getType());
            List<DepartmentUserVo> departments = bondApplyRefundMapper.getDepartments(departmentId,roleCodes);
            //过滤掉三处的车芸、十二处的王圣喜（移除过滤）
            //departments = departments.stream().filter(x -> !x.getUserName().equals(BondConstants.THREE_REGISTRAR) && !x.getUserName().equals(BondConstants.TWELVE_REGISTRAR)).collect(Collectors.toList());
            for (DepartmentUserVo department : departments) {
                BondApplyRefundConfirm bondApplyRefundConfirm = new BondApplyRefundConfirm();
                bondApplyRefundConfirm.setApplyRefundId(req.getApplyId());
                bondApplyRefundConfirm.setDepartmentId(department.getDepartmentId());
                bondApplyRefundConfirm.setUserId(department.getUserId());
                bondApplyRefundConfirm.setDepartmentDisposeStatus(RegistrarConfirmStatusEnum.WAIT_DISPOSE.getType());
                bondApplyRefundConfirmList.add(bondApplyRefundConfirm);
            }
            //更改申请表状态为2：已处理待确认
            bondApplyRefund.setId(req.getApplyId());
            bondApplyRefund.setHandleType(req.getConfirmType());
            bondApplyRefund.setStatus(BondApplyRefundStatus.HANDLE_WAIT_CONFIRM.getType());
            this.updateById(bondApplyRefund);
            //删除上一次申请的数据
            LambdaQueryWrapper<BondApplyRefundConfirm> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(BondApplyRefundConfirm::getApplyRefundId,req.getApplyId());
            bondApplyRefundConfirmService.remove(lambdaQueryWrapper);
            //保证金申请退还处室处长确认表添加数据
            result = bondApplyRefundConfirmService.saveBatch(bondApplyRefundConfirmList);
        }
        //退回
        if (req.getConfirmType().equals(BondApplyRefundHandleType.RETURNED.getType())) {
            bondApplyRefund.setId(req.getApplyId());
            //运管处退回
            bondApplyRefund.setStatus(BondApplyRefundStatus.RETURNED.getType());
            bondApplyRefund.setHandleType(req.getConfirmType());
            bondApplyRefund.setRemark(req.getRemark());
            result = this.updateById(bondApplyRefund);
        }
        return result;
    }

    /**
     * 异常退款申请处长处理
     * @param req
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean saveBondAbnormalApplyRefundRegistrar(BondAbnormalApplyRefundRegistrarReq req) {
        BondApplyRefundConfirm bondApplyRefundConfirm = bondApplyRefundConfirmMapper.selectById(req.getApplyRefundIdConfirmId());
        BondApplyRefund bondApplyRefund = new BondApplyRefund();
        if(bondApplyRefundConfirm == null){
            throw new SunFlowerException(ExceptionEnum.DATA_IS_PASS, ExceptionEnum.DATA_IS_PASS.getMessage());
        }
        if(!RegistrarConfirmStatusEnum.WAIT_DISPOSE.getType().equals(bondApplyRefundConfirm.getDepartmentDisposeStatus())){
            throw new SunFlowerException(ExceptionEnum.DATA_IS_PASS, ExceptionEnum.DATA_IS_PASS.getMessage());
        }
        boolean result = false;
        //非本处项目
        if (req.getConfirmType().equals(RegistrarConfirmStatusEnum.NO_DIVISION.getType())) {
            //修改当前处长的处理状态
            bondApplyRefundConfirm.setId(req.getApplyRefundIdConfirmId());
            bondApplyRefundConfirm.setDepartmentDisposeStatus(req.getConfirmType());
            bondApplyRefundConfirmMapper.updateById(bondApplyRefundConfirm);
            // 移除同处室的处长待处理处理状态
            LambdaQueryWrapper<BondApplyRefundConfirm> queryWrapper =  new LambdaQueryWrapper<>();
            queryWrapper.eq(BondApplyRefundConfirm::getApplyRefundId,bondApplyRefundConfirm.getApplyRefundId())
                    .eq(BondApplyRefundConfirm::getDepartmentId,bondApplyRefundConfirm.getDepartmentId())
                    .ne(BondApplyRefundConfirm::getId,req.getApplyRefundIdConfirmId());
            bondApplyRefundConfirmService.remove(queryWrapper);
            //判断是否全部处长对这条申请选择非本处项目
            BondApplyRefundConfirm bondApplyRefundConfirmOne = bondApplyRefundConfirmMapper.selectById(req.getApplyRefundIdConfirmId());
            LambdaQueryWrapper<BondApplyRefundConfirm> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(BondApplyRefundConfirm::getApplyRefundId,bondApplyRefundConfirmOne.getApplyRefundId());
            List<BondApplyRefundConfirm> bondApplyRefundConfirmList = bondApplyRefundConfirmMapper.selectList(lambdaQueryWrapper);
            boolean bool = true;
            for (BondApplyRefundConfirm applyRefundConfirm : bondApplyRefundConfirmList) {
                //存在处长处理状态不是非本处项目
                if (!applyRefundConfirm.getDepartmentDisposeStatus().equals(RegistrarConfirmStatusEnum.NO_DIVISION.getType())) {
                    bool = false;
                }
            }
            //如果全部处长选择非本处项目，退回运管处，修改申请状态为无处室认领，同时删除所有处长待办
            if (bool) {
                bondApplyRefund.setId(bondApplyRefundConfirmOne.getApplyRefundId());
                bondApplyRefund.setStatus(BondApplyRefundStatus.NO_DIVISION_CONFIRM.getType());
                bondApplyRefundMapper.updateById(bondApplyRefund);
                //移除所有处长对本条申请的待办
                LambdaQueryWrapper<BondApplyRefundConfirm> confirmLambdaQueryWrapper = new LambdaQueryWrapper<>();
                confirmLambdaQueryWrapper.eq(BondApplyRefundConfirm::getApplyRefundId,bondApplyRefundConfirmOne.getApplyRefundId());
                bondApplyRefundConfirmMapper.delete(confirmLambdaQueryWrapper);
            }
            //存在处室处长待处理状态，不修改数据状态
            result = true;
        }
        //本处项目同意退款、同意退款
        if (req.getConfirmType().equals(RegistrarConfirmStatusEnum.DIVISION_AGREE.getType()) || req.getConfirmType().equals(RegistrarConfirmStatusEnum.AGREE_REFUND.getType())) {
            //修改当前处长的处理状态
            bondApplyRefundConfirm.setId(req.getApplyRefundIdConfirmId());
            bondApplyRefundConfirm.setDepartmentDisposeStatus(req.getConfirmType());
            bondApplyRefundConfirmMapper.updateById(bondApplyRefundConfirm);
            //数据状态调整为‘处室同意(退款中)‘
            BondApplyRefundConfirm bondApplyRefundConfirmOne = bondApplyRefundConfirmMapper.selectById(req.getApplyRefundIdConfirmId());
            bondApplyRefund.setId(bondApplyRefundConfirmOne.getApplyRefundId());
            bondApplyRefund.setStatus(BondApplyRefundStatus.DIVISION_CONFIRM.getType());
            bondApplyRefundMapper.updateById(bondApplyRefund);
            //移除其他处长对本条申请的待办
            LambdaQueryWrapper<BondApplyRefundConfirm> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(BondApplyRefundConfirm::getApplyRefundId,bondApplyRefundConfirmOne.getApplyRefundId());
            bondApplyRefundConfirmMapper.delete(lambdaQueryWrapper);
            //向保证金退还表新增一条数据
            BondRefundFeq bondRefundReq = bondApplyRefundMapper.getBondRefundReq(bondApplyRefundConfirmOne.getApplyRefundId());
            //服务费收取方式：1：保证金转代理服务费  2：保证金全额转代理服务费并补差价  3：保证金全额退还，代理服务费另汇
            //bondRefundReq.setAgencyFeeType(BondAgencyFeeTypeEnum.BOND_TO_AGENCY.getType());
            bondRefundReq.setAgencyFee(new BigDecimal(0));
            bondRefundReq.setFormerAgencyFee(new BigDecimal(0));
            bondRefundReq.setRefundType(RefundTypeEnum.SUPPLIER_REFUND.getType());
            bondRefundReq.setBondType(BondWaterTypeEnum.WATER.getType());
            //退还方式 1：收取并退还剩余保证金  2：收取并保留剩余保证金
            bondRefundReq.setRefundMode(RefundModeEnum.REFUND_SURPLUS.getType());
            //获取流水信息
            BondWater water = bondWaterMapper.selectById(bondRefundReq.getWaterId());

            bondRefundReq.setWaterIds(bondRefundReq.getWaterId().toString());
            //查询该流水申请退款的退款状态(财务)
            QueryWrapper<BondRefund> bondRefundQueryWrapper = new QueryWrapper<>();
            bondRefundQueryWrapper.lambda().eq(BondRefund::getApplyRefundId,bondApplyRefundConfirmOne.getApplyRefundId());
            BondRefund bondRefund = bondRefundMapper.selectOne(bondRefundQueryWrapper);
            if(bondRefund != null){
                bondRefundReq.setId(bondRefund.getId());
            }

            BondRefundDetailsVo details = BeanListUtil.convert(bondRefundReq,BondRefundDetailsVo.class);
            details.setAmountDate(water.getDate());
            details.setFromBankNumber(water.getReceiveAcount());
            details.setFromOpenBank(water.getReceiveBank());
            details.setRefundBankCode(water.getCompanyBankCode());
            details.setRefundNumber(water.getCompanyAccount());
            details.setRefundCompanyName(water.getCompanyName());
            details.setRefundOpenBank(water.getCompanyBankDeposit());
            details.setStatus(BondRefundStatusEnum.WAIT.getType());
            List<BondRefundDetailsVo> detailsList = new ArrayList<>();
            detailsList.add(details);
            bondRefundReq.setBondRefundDetails(detailsList);
            result = bondRefundService.saveApplyRefund(bondRefundReq);
        }
        //本处项目不同意退款、不同意退款
        if (req.getConfirmType().equals(RegistrarConfirmStatusEnum.DIVISION_NOT_AGREE.getType()) || req.getConfirmType().equals(RegistrarConfirmStatusEnum.NOT_AGREE_REFUND.getType())) {
            //修改当前处长的处理状态
            bondApplyRefundConfirm.setId(req.getApplyRefundIdConfirmId());
            bondApplyRefundConfirm.setDepartmentDisposeStatus(req.getConfirmType());
            bondApplyRefundConfirm.setRemark(req.getRemark());
            bondApplyRefundConfirmMapper.updateById(bondApplyRefundConfirm);
            //数据状态调整为‘处室不同意‘
            BondApplyRefundConfirm bondApplyRefundConfirmOne = bondApplyRefundConfirmMapper.selectById(req.getApplyRefundIdConfirmId());
            bondApplyRefund.setId(bondApplyRefundConfirmOne.getApplyRefundId());
            bondApplyRefund.setStatus(BondApplyRefundStatus.DIVISION_NO_CONFIRM.getType());
            bondApplyRefundMapper.updateById(bondApplyRefund);
            //移除其他处长对本条申请的待办
            LambdaQueryWrapper<BondApplyRefundConfirm> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(BondApplyRefundConfirm::getApplyRefundId,bondApplyRefundConfirmOne.getApplyRefundId());
            bondApplyRefundConfirmMapper.delete(lambdaQueryWrapper);
            result = true;
        }
        return result;
    }

    /**
     * 异常关联确认(项目经理发起)列表
     * @param condition
     * @return
     */
    @Override
    public IPage<AbnormalApplyRelationVo> getBondAbnormalRelationList(BondApplyRelationCondition condition) {
        Page<AbnormalApplyRelationVo> page = condition.buildPage();
        IPage<AbnormalApplyRelationVo> abnormalApplyRelationVoIPage = bondApplyRefundMapper.getBondAbnormalRelationList(page,condition);
        return abnormalApplyRelationVoIPage;
    }

    /**
     * 保证金关联申请(项目经理发起)列表
     * @param condition
     * @return
     */
    @Override
    public IPage<AbnormalApplyRelationVo> bondApplyRelationList(BondApplyRelationCondition condition) {
        Page<AbnormalApplyRelationVo> page = condition.buildPage();
        IPage<AbnormalApplyRelationVo> abnormalApplyRelationVoIPage = bondApplyRefundMapper.bondApplyRelationList(page,condition);
        //支付凭证文件
        for (AbnormalApplyRelationVo record : abnormalApplyRelationVoIPage.getRecords()) {
            List<OssFile> ossFileList = new ArrayList<>();
            if (StringUtils.isNotEmpty(record.getPaymentVoucherIds())) {
                List<Long> ossFileIds =new ArrayList<>();
                for (String s : record.getPaymentVoucherIds().split(",")) {
                    ossFileIds.add(Long.valueOf(s));
                }
                LambdaQueryWrapper<OssFile> ossFileLambdaQueryWrapper = new LambdaQueryWrapper<>();
                ossFileLambdaQueryWrapper.in(OssFile::getId,ossFileIds);
                ossFileList = ossFileMapper.selectList(ossFileLambdaQueryWrapper);
            }
            record.setOssFileList(ossFileList);
        }
        return abnormalApplyRelationVoIPage;
    }

    /**
     * 根据id查询保证金申请关联(项目经理发起)详情
     * @param req
     * @return
     */
    @Override
    public BondApplyRelation getBondApplyRelationById(BondApplyRelationConfirmReq req) {
        if (req == null || req.getApplyId() == null) {
            return null;
        }
        return bondApplyRelationMapper.selectById(req.getApplyId());
    }

    /**
     * 保证金关联申请同意/退回(项目经理发起)
     * @param req
     * @return
     */
    @Override
    public Boolean saveBondApplyRelationConfirm(BondApplyRelationConfirmReq req) {
        Boolean result = false;
        BondApplyRelation bondApplyRelation = new BondApplyRelation();
        bondApplyRelation = bondApplyRelationMapper.selectById(req.getApplyId());
        if (!ApplyRelationStatusEnum.WAIT_HANDLE.getType().equals(bondApplyRelation.getStatus())) {
            throw new SunFlowerException(ExceptionEnum.DATA_IS_PASS, ExceptionEnum.DATA_IS_PASS.getMessage());
        }
        bondApplyRelation.setId(req.getApplyId());
        //同意
        if (req.getType() == 1) {
            bondApplyRelation.setStatus(ApplyRelationStatusEnum.HANDLED.getType());
            bondApplyRelation.setRemark(req.getAgreeMsg());
//            bondApplyRelation.setPayCompanyName(req.getPayCompanyName());
        }
        //退回
        if (req.getType() == 2) {
            bondApplyRelation.setStatus(ApplyRelationStatusEnum.RETRUNED.getType());
            bondApplyRelation.setReturnMsg(req.getReturnMsg());
        }
        int i = bondApplyRelationMapper.updateById(bondApplyRelation);
        if (i > 0) {
            result = true;
        }
        return result;
    }

    @Override
    public Result<Boolean> deleteBondRefundWater(Long waterId) {
        Boolean flag = true;
        LambdaQueryWrapper<BondApplyRefund> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BondApplyRefund::getWaterId,waterId);
        BondApplyRefund byId = this.getOne(queryWrapper);
        if(BeanUtil.isNotEmpty(byId) && (BondApplyRefundStatus.RETURNED.getType().equals(byId.getStatus())
                || BondApplyRefundStatus.DIVISION_NO_CONFIRM.getType().equals(byId.getStatus()))){
            flag = this.removeById(byId);
        } else {
            flag = false;
        }
        return Result.okOrFailed(flag);
    }

    /**
     * id是否在List中
     * @param id
     * @param ids
     * @return
     */
    public Boolean checkId(Long id, List<Long> ids) {
        boolean result = false;
        for (Long aLong : ids) {
            if (aLong.equals(id)) {
                result = true;
            }
        }
        return result;
    }

    /**
     * 分页数据替换
     * @param bondWaters
     * @param bondNotRelationVoPage
     * @return
     */
    public Page<BondNotRelationVo> copyBondWater(IPage<BondWater> bondWaters,Page<BondNotRelationVo> bondNotRelationVoPage,Integer status){
        //拼装返回值分页数据
        BeanUtils.copyProperties(bondWaters,bondNotRelationVoPage);
        List<BondWater> records = bondWaters.getRecords();
        List<BondNotRelationVo> vos = BeanListUtil.convertList(records,BondNotRelationVo.class);
        for (BondNotRelationVo vo : vos) {
            vo.setStatus(status);
        }
        return bondNotRelationVoPage.setRecords(vos);
    }

}
