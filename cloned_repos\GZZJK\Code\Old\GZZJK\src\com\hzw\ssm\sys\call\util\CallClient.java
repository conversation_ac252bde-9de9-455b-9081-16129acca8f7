package com.hzw.ssm.sys.call.util;


import java.io.BufferedInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;
import java.util.UUID;

import org.apache.struts2.convention.annotation.Namespace;

import com.hzw.ssm.fw.util.StringUtil;
import com.hzw.ssm.sys.call.entity.VoiceEntity;
import com.sun.xml.internal.ws.client.RequestContext;

@Namespace("/callClient")
public class CallClient {
	
	public String getnoticemac(String json,String key) {
		String mac = MD5.getMD5Str(MD5.getMD5Str(StringUtil.deleteN(json)) + key);
		return mac;
	}
	
	
	public String sendVoice(VoiceEntity entity){
		//企业ID
		 String vccid =null;
		
		//呼叫显示号码
		 String voice_displayNum=null;
		
		//对接的key
		 String key=null;
		
		//对接的接口URL
		 String voice_url=null;
		
		//播放次数
		 String voice_playTime=null;
		 
		 //serviceName
		 String serviceName = null;
		
		 String voice_serviceKey = null;
		//指定的手机按键
		 String voice_supNumber=null;
		 
		
		String fileUrl =new File("/java/tomcat/tomcat6-9001-GZZJK/webapps/GZZJK")+System.getProperty("file.separator")+"WEB-INF"+System.getProperty("file.separator")+"config.properties";
		//String fileUrl =new File("E:\\Tomcat_6_GZZJK_TEMP_5058\\webapps\\GZZJK")+System.getProperty("file.separator")+"WEB-INF"+System.getProperty("file.separator")+"config.properties";
		vccid =readData(fileUrl, "voice_vccId");
		
		System.out.println("12121312313123"+vccid);
		voice_displayNum =readData(fileUrl, "voice_displayNum");
		key =readData(fileUrl, "voice_key");
		voice_supNumber =readData(fileUrl, "voice_supNumber");
		voice_url =readData(fileUrl, "voice_url");
		serviceName = readData(fileUrl, "voice_serviceName");
		voice_serviceKey = readData(fileUrl, "voice_serviceKey");
		//呼出
		Map<String, String> headerMap = new HashMap<String, String>();
		Map<String, Object> responseMap = new HashMap<String, Object>();
		
		headerMap.put("serviceName", serviceName);   //IVRQuestRequest
		headerMap.put("streamNumber", UUID.randomUUID().toString().replaceAll("-", ""));
		headerMap.put("messageId", entity.getMessageId());
		headerMap.put("vccId", vccid);
		responseMap.put("header", headerMap);
		
		Map<String, String> bodyMap = new HashMap<String, String>();
	/*	bodyMap.put("displayNum", "02566806888");
		bodyMap.put("calledNum", "15295378180");  
		bodyMap.put("serviceKey", "900013"); 
		bodyMap.put("playMode", "2");
		bodyMap.put("playMediaName", "");
		bodyMap.put("isRecord", "0");
		bodyMap.put("welcomeIvrName", "");
		bodyMap.put("numCode", "");
		bodyMap.put("mediaContent","测试数据");
		bodyMap.put("playTime", "3");
		bodyMap.put("bussinessId", "0000001");
		bodyMap.put("endMediaName", "");
		bodyMap.put("errorMediaName", "");
		bodyMap.put("endMediaContent", "");
		bodyMap.put("supNumber", "1,2");
		bodyMap.put("oneMediaName", "");
		bodyMap.put("oneMediaContent", "欢迎参加此次招标");
		bodyMap.put("twoMediaName", "");
		bodyMap.put("twoMediaContent", "欢迎参加此次招标");*/
		bodyMap.put("displayNum", voice_displayNum);
		bodyMap.put("calledNum", entity.getCalledNum());  //13505140602
		bodyMap.put("serviceKey", voice_serviceKey); 
		bodyMap.put("playMode", "2");
		bodyMap.put("playMediaName", "");
		bodyMap.put("errorMediaName", "");
		bodyMap.put("supNumber", voice_supNumber);
		bodyMap.put("mediaContent",entity.getMediaContent());
		bodyMap.put("playTime", "3");
		responseMap.put("body", bodyMap);
		net.sf.json.JSONObject responseToJson = net.sf.json.JSONObject.fromObject(responseMap);
		String sendJson =  responseToJson.toString();
		System.out.println(sendJson);
		String mac = getnoticemac(sendJson, key);
		String result = HttpClientUtil.doPostJson(voice_url+mac, sendJson);
		System.out.println(result);
		return result;
	}
	 /** 
     * 根据KEY，读取文件对应的值 
     * @param filePath 文件路径，即文件所在包的路径，例如：java/util/config.properties 
     * @param key 键 
     * @return key对应的值 
     */  
    public String readData(String filePath, String key) {  
        
        Properties props = new Properties();  
        try {  
            InputStream in = new BufferedInputStream(new FileInputStream(filePath));  
            props.load(in);  
            in.close();  
            String value = props.getProperty(key);  
            return value;  
        } catch (Exception e) {  
            e.printStackTrace();  
            return null;  
        }  
    }  
	//查询呼叫在线数量 
	public String getCallOnlineNums(){
		String vccid = "3343";
		String key = "371776F02B5C9DA4F74FA0E4ECE630A2";
		Map<String, String> headerMap = new HashMap<String, String>();
		Map<String, Object> responseMap = new HashMap<String, Object>();
		headerMap.put("vccId", vccid);
		headerMap.put("messageId", UUID.randomUUID().toString().replaceAll("-", ""));
		headerMap.put("serviceName", "countRequest");
		responseMap.put("header", headerMap);
		Map<String, String> bodyMap = new HashMap<String, String>();
		bodyMap.put("bussinessId", "0000001");
		bodyMap.put("serviceKey", "900008");
		bodyMap.put("playMode", "0");
		responseMap.put("body", bodyMap);
		net.sf.json.JSONObject responseToJson = net.sf.json.JSONObject.fromObject(responseMap);
		String sendJson = responseToJson.toString();
//		logger.info("linkcircle请求json：" + sendJson);
		String mac = getnoticemac(sendJson, key);
		String result =   HttpClientUtil.doPostJson("http://58.220.51.44:15407/CTDNOTICE/CTD?mac="+mac, sendJson);
		System.out.print(result);
		return result;
	}
	
		//查询话单
		public  String getCallMoney(){
		String vccid = "3343";
		String key = "371776F02B5C9DA4F74FA0E4ECE630A2";
		Map<String, String> headerMap = new HashMap<String, String>();
		Map<String, Object> responseMap = new HashMap<String, Object>();
		
		headerMap.put("serviceName", "ACRQuestRequest");
		headerMap.put("messageId", UUID.randomUUID().toString().replaceAll("-", ""));
		responseMap.put("header", headerMap);
		
		Map<String, String> bodyMap = new HashMap<String, String>();
		bodyMap.put("serviceKey", "900013");
		bodyMap.put("callId",UUID.randomUUID().toString().replaceAll("-", ""));   //测试用，要求唯一，可以动态生成
		bodyMap.put("calledNum", "18551970970");
		bodyMap.put("calledDisplayNumber", "02566834713");
		//bodyMap.put("stopCalledTime", "");  //文档中没有
		bodyMap.put("calledDuration", "8");
		bodyMap.put("calledCost", "0");
		bodyMap.put("calledRelCause", "9");
		bodyMap.put("calledOriRescode", "9");
		bodyMap.put("chargeNumber", "02566834713");
		bodyMap.put("calledRelReason", "");
		//bodyMap.put("msServer", "");
		bodyMap.put("duration", "8");
		bodyMap.put("costCount", "0");
		bodyMap.put("vccId", vccid);
		bodyMap.put("dtmfKey", "1");
		
		responseMap.put("body", bodyMap);
		net.sf.json.JSONObject responseToJson = net.sf.json.JSONObject.fromObject(responseMap);
		String sendJson = responseToJson.toString();
//		logger.info("linkcircle请求json：" + sendJson);
		String mac = getnoticemac(sendJson, key);
		String result =   HttpClientUtil.doPostJson("http://58.220.51.44:15407/CTDQUEST/ICCP?mac="+mac, sendJson);
		System.out.print(result);
		return result;
	}
	public static void main(String[] args) {
		/*try {
			String fileUrl =new File(RequestContext.class.getResource("/").getFile()).getParentFile().getParentFile().getCanonicalPath()+System.getProperty("file.separator")+"WEB-INF"+System.getProperty("file.separator")+"config.properties";
		System.out.println(fileUrl);
		} catch (IOException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}*/
		CallClient client = new CallClient();
		//呼出
		String vccid = "3343";
		String key = "371776F02B5C9DA4F74FA0E4ECE630A2";
		Map<String, String> headerMap = new HashMap<String, String>();
		Map<String, Object> responseMap = new HashMap<String, Object>();
		
		headerMap.put("serviceName", "IVRQuestRequest");   //IVRQuestRequest
		headerMap.put("streamNumber", UUID.randomUUID().toString().replaceAll("-", ""));
		headerMap.put("messageId", UUID.randomUUID().toString().replaceAll("-", ""));
		headerMap.put("vccId", vccid);
		responseMap.put("header", headerMap);
		
		Map<String, String> bodyMap = new HashMap<String, String>();
		bodyMap.put("displayNum", "02566806888");
		bodyMap.put("calledNum", "15295378180");  //13505140602
		bodyMap.put("playMode", "2");
		bodyMap.put("playMediaName", "");
//		bodyMap.put("endivrName", "");
		bodyMap.put("errorMediaName", "");
		bodyMap.put("supNumber", "1,2");
		bodyMap.put("mediaContent", "通知你明天参加16楼评标会议，参加请回复1，不参加请回复2.");
		bodyMap.put("playTime", "3");
		responseMap.put("body", bodyMap);
		net.sf.json.JSONObject responseToJson = net.sf.json.JSONObject.fromObject(responseMap);
		String sendJson = responseToJson.toString();
//		logger.info("linkcircle请求json：" + sendJson);
		String mac = client.getnoticemac(sendJson, key);
		System.out.println(mac+"-"+sendJson);
	//	String result = HttpClientUtil.doPostJson("http://callapi.linkcircle.net/ICCPQUEST?mac="+mac, sendJson);
//		System.out.println(result);
//		logger.info("请求返回："+result);;
		//查询呼叫在线数量
//	getCallOnlineNums();
		
		//
//		getCallMoney();
	}

}
