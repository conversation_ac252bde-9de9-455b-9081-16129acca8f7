package com.hzw.sunflower.entity.condition;

import com.hzw.sunflower.common.BaseCondition;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


@ApiModel(description = "招标文件查询条件")
@Data
public class ProjectBidDocCondition extends BaseCondition {

    @ApiModelProperty(value = "当前用户Id")
    private Long userId;

    @ApiModelProperty(value = "项目Id")
    private String projectId;

    @ApiModelProperty(value = "招标文件进度")
    private String noticeProgress;

    @ApiModelProperty(value = "招标文件进度集合")
    private String[] noticeProgressList;

    @ApiModelProperty(value = "文件名称")
    private String fileName;

    @ApiModelProperty(value = "招标文件阶段")
    private Integer bidRound;
}
