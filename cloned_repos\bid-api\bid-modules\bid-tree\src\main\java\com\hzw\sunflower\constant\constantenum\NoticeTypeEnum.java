package com.hzw.sunflower.constant.constantenum;

/**
 * <AUTHOR>
 * @date ：Created in 2021/7/14 10:51
 * @description：招标公告类型
 * @version: 1.0
 */
public enum NoticeTypeEnum {

    PURCHASE_NOTICE(1, "采购公告"),

    SUPPLEMENT_NOTICE(2, "补充公告"),

    PURCHASE_RESULT_NOTICE(3, "采购结果公示"),

    TENDER_CANDIDATE_NOTICE(4, "中标候选人公示");

    NoticeTypeEnum(Integer value, String name){
        this.value = value;
        this.name = name;
    }

    private Integer value;

    private String name;

    public Integer getValue() {
        return value;
    }

    public String getName() {
        return name;
    }
}
