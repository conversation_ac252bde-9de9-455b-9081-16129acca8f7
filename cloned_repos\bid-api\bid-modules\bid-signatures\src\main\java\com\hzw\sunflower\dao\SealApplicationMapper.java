package com.hzw.sunflower.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hzw.sunflower.controller.request.SealApplicationHandleREQ;
import com.hzw.sunflower.controller.request.SealFileSectionREQ;
import com.hzw.sunflower.controller.response.*;
import com.hzw.sunflower.dto.SealApplicationDTO;
import com.hzw.sunflower.dto.SealApplicationProjectDTO;
import com.hzw.sunflower.dto.SealFileDTO;
import com.hzw.sunflower.entity.SealApplication;
import com.hzw.sunflower.entity.condition.SealApplicationCondition;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* SealApplicationMapper接口
*
*/
public interface SealApplicationMapper extends BaseMapper<SealApplication> {
    /**
     * 分页查询用印申请
     *
     * @param page
     * @param condition
     * @param userId
     * @param departId
     * @return
     */
    IPage<SealApplicationDTO> listInfo(@Param("page") IPage<SealApplicationDTO> page, @Param("condition") SealApplicationCondition condition, @Param("userId")Long userId, @Param("departId")Long departId,@Param("datascope")String datascopesql);


    /**
     * 项目用印列表
     * @param page
     * @param condition
     * @return
     */
    IPage<SealApplicationDTO> listProjectInfo(@Param("page") IPage<SealApplicationDTO> page, @Param("condition") SealApplicationCondition condition);
    /**
     * 查询申请项目信息
     * @param id
     * @return
     */
    List<SealApplicationProjectDTO> queryProjectInfo(@Param("id")Long id);


    /**
     * 获取处理列表
     * @param req
     * @return
     */
    IPage<SealApplicationHandleVO> selectSealHandleVO(@Param("page") IPage<SealApplicationHandleVO> page , @Param("req") SealApplicationHandleREQ req);

    /**
     * 查询申请信息
     * @param id
     * @return
     */
    SealApplicationVO queryApplyInfo(@Param("id")Long id);

    List<SealFileDTO> queryFileInfo(@Param("id")Long id);

    /**
     * 导出数据
     * @param req
     * @return
     */
    List<SealApplicationHandleVO> selectSealHandleVO( @Param("req") SealApplicationHandleREQ req);

    /**
     * 查询审批数据
     *
     * @param id
     * @param deptId
     * @return
     */
    SealAppingTaskVO queryInfo(@Param("id")Long id);

    String queryCompanyInfo(@Param("ids") List<Long> companyIds);

    /**
     * 根据标段获签章文件数据
     * @param req
     * @return
     */
    List<SealFilesVO> getSealFileBySection(@Param("req") SealFileSectionREQ req);


    List<SealFileVO> getSealFileList(@Param("applyId")Long applyId);

    String findName();

    /**
     * 根据通知书id查询用印申请
     * @param id
     * @return
     */
    SealApplication getByNoticeId(@Param("id")Long id);
}