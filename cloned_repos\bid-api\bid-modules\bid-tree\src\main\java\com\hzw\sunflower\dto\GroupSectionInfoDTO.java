package com.hzw.sunflower.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date ：Created in 2021/10/13 17:21
 * @description：套相关信息
 * @version: 1.0
 */
@Data
@EqualsAndHashCode
public class GroupSectionInfoDTO {

    @ApiModelProperty(value = "本套金额", position = 1)
    private BigDecimal amount;

    @ApiModelProperty(value = "支付方式", position = 2)
    private Integer paymentType;

    @ApiModelProperty(value = "招标文件发售方式", position = 3)
    private String releaseFileType;

    @ApiModelProperty(value = "招标文件发售前审核方式", position = 4)
    private String reviewFileType;

    @ApiModelProperty(value = "是否可以修改", position = 5)
    private Boolean flag;
}
