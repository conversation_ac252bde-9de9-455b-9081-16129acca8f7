package com.hzw.sunflower.entity;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class LargeProject implements Serializable {

    @ApiModelProperty(value = "序号")
    @ExcelProperty(value = "序号")
    private Integer seq;

    @ApiModelProperty(value = "部门")
    @ExcelProperty(value = "部门")
    private String departmentName;

    @ApiModelProperty(value = "负责人")
    @ExcelProperty(value = "负责人")
    private String userName;

    @ApiModelProperty(value = "项目编号")
    @ExcelProperty(value = "项目编号")
    private String projectNumber;

    @ApiModelProperty(value = "委托单位")
    @ExcelProperty(value = "委托单位")
    private String principalCompany;

    @ApiModelProperty(value = "项目名称")
    @ExcelProperty(value = "项目名称")
    private String projectName;

    @ApiModelProperty(value = "委托金额(万元)")
    @ExcelProperty(value = "委托金额(万元)")
    private BigDecimal entrustMoney;

    @ExcelIgnore
    private Integer packageNumber;//包号

    @ExcelIgnore
    private Integer entrustCurrency;//币种
    @ExcelIgnore
    private BigDecimal exchangeRate;//汇率
}
