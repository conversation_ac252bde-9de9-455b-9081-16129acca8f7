package com.hzw.sunflower.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;


/**
 * 简单公告信息
 *
 * <AUTHOR> 2021/07/05
 * @version 1.0.0
 */

@ApiModel(description = "简单公告信息")
@Data
@Accessors(chain = true)
public class SimpleBidNoticeDTO {

    @ApiModelProperty(value = "项目ID", position = 1)
    private Long projectId;

    @ApiModelProperty(value = "公告ID", position = 2)
    private Long noticeId;

    @ApiModelProperty(value = "公告名称", position = 3)
    private String noticeName;

    @ApiModelProperty(value = "公告所关联的包段", position = 4)
    private String packageNumber;

    @ApiModelProperty(value = "公告进度", position = 5)
    private Integer noticeProgress;

    @ApiModelProperty(value = "最后更新记录的用户", position = 6)
    private String updateUserName;

    @ApiModelProperty(value = "创建时间", position = 7)
    private Date createTime;

    @ApiModelProperty(value = "最后更新时间", position = 8)
    private Date updateTime;

    @ApiModelProperty(value = "公告所关联的标段包", position = 9)
    private List<PackageInfoDTO> packageInfo;

    @ApiModelProperty(value = "1:正式 2:暂存", position = 10)
    private Integer state;

    @ApiModelProperty(value = "用户名称", position = 11)
    private String userName;
    /**
     * 招标公告阶段： 0：默认无阶段； 1：第一轮； 2：第二轮
     */
    @ApiModelProperty(value = "招标公告阶段", position = 12)
    private Integer bidRound;

    @ApiModelProperty(value = "发布时间", position = 13)
    private Date publishTime;
}
