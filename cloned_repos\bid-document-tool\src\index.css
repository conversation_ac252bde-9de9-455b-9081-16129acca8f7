body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica,
    Arial, sans-serif;
  margin: 0;
  padding: 0;
}
.fl{
  float: left !important;
}
.fr{
  float: right !important;
}
.ml5{
  margin-left: 5px !important;
}
.ml6{
  margin-left: 6px !important;
}
.ml10{
  margin-left: 10px !important;
}
.ml15{
  margin-left: 15px !important;
}
.ml20{
  margin-left: 20px !important;
}
.ml30{
  margin-left: 30px !important;
}
.mr5{
  margin-right: 5px !important;
}
.mr6{
  margin-right: 6px !important;
}
.mr10{
  margin-right: 10px !important;
}
.mr15{
  margin-right: 15px !important;
}
.mr20{
  margin-right: 20px !important;
}
.mr30{
  margin-right: 30px !important;
}
.mt5{
  margin-top: 5px !important;
}
.mt10{
  margin-top: 10px !important;
}
.mt15{
  margin-top: 15px !important;
}
.mt20{
  margin-top: 20px !important;
}
.mt30{
  margin-top: 30px !important;
}

.mb5{
  margin-bottom: 5px !important;
}
.mb10{
  margin-bottom: 10px !important;
}
.mb15{
  margin-bottom: 15px !important;
}
.mb20{
  margin-bottom: 20px !important;
}
.mb30{
  margin-bottom: 30px !important;
}


.pl5{
  padding-left: 5px !important;
}
.pl10{
  padding-left: 10px !important;
}
.pl15{
  padding-left: 15px !important;
}
.pl20{
  padding-left: 20px !important;
}
.pl30{
  padding-left: 30px !important;
}

.pr5{
  padding-right: 5px !important;
}
.pr10{
  padding-right: 10px !important;
}
.pr15{
  padding-right: 15px !important;
}
.pr20{
  padding-right: 20px !important;
}
.pr30{
  padding-right: 30px !important;
}

.pt5{
  padding-top: 5px !important;
}
.pt10{
  padding-top: 10px !important;
}
.pt15{
  padding-top: 15px !important;
}
.pt20{
  padding-top: 20px !important;
}
.pt30{
  padding-top: 30px !important;
}
.pb5{
  padding-bottom: 5px !important;
}
.pb10{
  padding-bottom: 10px !important;
}
.pb15{
  padding-bottom: 15px !important;
}
.pb20{
  padding-bottom: 20px !important;
}
.pb30{
  padding-bottom: 30px !important;
}
.bold{
  font-weight: bold !important;
}
.creatTitle{
  display: inline-block !important;
}
.dinb{
  display: inline-block;
}
.curp{
  cursor: pointer !important;
}