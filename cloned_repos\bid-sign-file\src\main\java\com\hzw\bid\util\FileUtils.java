/*
 * 公司名称：江苏华招网
 * 版权信息：江苏华招网版权
 * 描述：文件相关操作
 * 文件名称：FileUtils.java
 * 修改时间：2017年10月16日
 * 修改人：ypp
 * 修改内容：
 * 跟踪单号：
 * 修改单号 ：
 */

package com.hzw.bid.util;


import cn.hutool.core.io.FileUtil;
import com.aspose.cells.License;
import com.aspose.cells.PdfSaveOptions;
import com.aspose.cells.Workbook;
import com.deepoove.poi.XWPFTemplate;
import com.lowagie.text.Document;
import com.lowagie.text.pdf.PdfCopy;
import com.lowagie.text.pdf.PdfImportedPage;
import com.lowagie.text.pdf.PdfReader;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.rendering.PDFRenderer;
import org.apache.poi.xwpf.converter.pdf.PdfConverter;
import org.apache.poi.xwpf.converter.pdf.PdfOptions;
import org.apache.poi.xwpf.usermodel.XWPFDocument;

import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.*;

/**
 * <一句话功能简述> 文件相关操作
 * <功能详细描述>
 * <AUTHOR>
 * @version V1.0[产品/模块版本]
 */
public class FileUtils {

    private final static String[] SUPPORT_FILE_SUFFIX_ARR = new String[] { "docx", "doc", "wps",
            "pdf", "zip", "rar", "7z", "bmp", "jpg", "jpeg", "png", "gif", "xls", "xlsx", "tiff",
            "pcx", "tga", "exif", "fpx", "svg", "psd", "cdr", "pcd", "dxf", "ufo", "eps", "ai",
            "raw" };

    /**
     *
     * 函数功能描述：把路径中的分隔符替换成允许系统中的符号
     * @param path
     * @return
     */
    public static String replaceSeparator(String path) {
        if (File.separator.equals("/")) {
            return path.replaceAll("[\\\\/]+", "/");
        }
        return path.replaceAll("[\\\\/]+", "\\\\");
    }

    /**
     *
     * 函数功能描述：根据字符串路径校验路径以及文件是否存在，如果不存在那么就创建
     * @param fileStr
     * @return
     * @throws IOException
     */
    public static File checkAndNewCreateFile(String fileStr) throws IOException {
        if (fileStr == null || fileStr.equals("")) {
            return null;
        }
        File file = new File(fileStr);
        if (!file.getParentFile().exists()) {
            file.getParentFile().mkdirs();
        }
        if (file.exists()) {
            file.delete();
        }
        if (!file.exists()) {
            file.createNewFile();
        }

        return file;
    }

    /**
     *
     * 函数功能描述：根据字符串路径校验路径以及文件是否存在，如果不存在那么就创建
     * @param fileName 文件名称
     * @return boolean，如果支持该文件上传，那么返回true，否则false
     * @throws IOException
     */
    public static boolean isSupportUploadFileType(String fileName) {
        return isSupportUploadFileType(fileName, SUPPORT_FILE_SUFFIX_ARR);
    }

    /**
     *
     * 函数功能描述：根据字符串路径校验路径以及文件是否存在，如果不存在那么就创建
     * @param fileName 文件名称
     * @param suffix 支持文件上传的后缀名数组，无须包含“.”
     * @return boolean，如果支持该文件上传，那么返回true，否则false
     * @throws IOException
     */
    public static boolean isSupportUploadFileType(String fileName, String... suffix) {
        if (EmptyUtils.isEmptyAfterTrim(fileName)) {
            return false;
        }
        int lastDotIndex = fileName.lastIndexOf(".");
        if (lastDotIndex == -1) {
            return false;
        }
        String fileSuffix = fileName.substring(lastDotIndex + 1);
        if (EmptyUtils.isEmptyAfterTrim(fileSuffix)) {
            return false;
        }
        fileSuffix = fileSuffix.trim();
        boolean isSupport = false;

        for ( String suffixItem : suffix ) {
            if (fileSuffix.equalsIgnoreCase(suffixItem)) {
                isSupport = true;
                break;
            }
        }

        return isSupport;
    }


    /**
     *	 根据路径判断文件是否存在
     * @param path
     * @return
     */
    public static boolean checkedFilePath(String path) {
    	File file = new File(path);

    	if(file.exists()) {
    		return true;
    	}else {
    		return false;
    	}
    }
    /**
     * 函数功能描述：以指定的编码格式读取指定路径文件的内容
     * @param path String
     * @param encoding String
     * @return content String
     * @throws IOException
     */
    public static String readFileContent(String path, String encoding) throws IOException {
        String content = "";

        StringBuffer sb = new StringBuffer();
        BufferedReader in = null;
        try {
            InputStream _in = new FileInputStream(path);
            in = new BufferedReader(new InputStreamReader(_in, encoding));

            char[] buf = new char[512];
            int readCount = in.read(buf, 0, 512);
            while (readCount >= 0) {
                sb.append(buf, 0, readCount);
                readCount = in.read(buf, 0, 512);
            }
            content = sb.toString();
        } catch (Exception e) {
            e.printStackTrace();
        }
        finally {
            if (in != null) {
                in.close();
            }
        }

        return content;
    }

    /**
     * 函数功能描述：替换部分字符串
     * @param content String
     * @param target String
     * @param replacement String
     * @return
     */
    public static String replaceSubString(String content, String target, String replacement) {
        if (content == null) {
            content = "";
        }
        if (EmptyUtils.isEmpty(replacement)) {
            return content;
        }

        return content.replace(target, replacement);
    }

    /**
     * 函数功能描述：TODO
     * @param path
     * @param content
     */
    public static void writeFile(String path, String content) {
        FileWriter writer;
        try {
            if (path != null && !path.equals("")) {
                File file = new File(path);
                if (!file.getParentFile().exists()) {
                    file.getParentFile().mkdirs();
                }
                if (file.exists()) {
                    file.delete();
                }
                if (!file.exists()) {
                    file.createNewFile();
                }

                writer = new FileWriter(path);
                writer.write(content);
                writer.flush();
                writer.close();
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 函数功能描述：按照指定的编码格式写入文件
     * @param path
     * @param content
     */
    public static void writeFileWithEncoding(String path, String content, String encoding) {
        try {
            if (path != null && !path.equals("")) {
                File file = new File(path);
                if (!file.getParentFile().exists()) {
                    file.getParentFile().mkdirs();
                }
                if (file.exists()) {
                    file.delete();
                }
                if (!file.exists()) {
                    file.createNewFile();
                }

                BufferedWriter writer = new BufferedWriter(new OutputStreamWriter(
                        new FileOutputStream(file), encoding));
                writer.write(content);
                writer.flush();
                writer.close();
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 功能描述: byte数组转 InputStream
     *
     * @param bytes byte数组
     * @return java.io.InputStream
     * <AUTHOR>
     * @date 2019/3/28 16:01
     * @version 1.0
     */
    public static InputStream byte2InputStream(byte[] bytes) {
        return new ByteArrayInputStream(bytes);
    }


    /**
     * 功能描述:
     *
     * @param inputStream 输入流
     * @return byte[] 数组
     * <AUTHOR>
     * @date 2019/3/28 16:03
     * @version 1.0
     */
    public static byte[] inputStream2byte(InputStream inputStream) throws IOException {
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        byte[] buff = new byte[100];
        int rc = 0;
        while ((rc = inputStream.read(buff, 0, 100)) > 0) {
            byteArrayOutputStream.write(buff, 0, rc);
        }
        return byteArrayOutputStream.toByteArray();
    }



    /**
     *读取文件以流的形式输出
     * @param filePath  路径
     * @param contentType  输出格式
     * @param fileName  文件名称
     * @throws IOException
     */
    public static void outFile(String filePath, String contentType,String fileName, HttpServletResponse response) throws IOException {
        File file = new File(FileUtils.replaceSeparator(filePath));
        InputStream in = null;
        OutputStream os = null;
        try {
            response.setContentType(contentType); // 设置返回内容格式
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-disposition", "attachment;filename=" + new String(fileName.getBytes("GB2312"),"iso8859-1"));
            in = new FileInputStream(file); //用该文件创建一个输入流
            os = response.getOutputStream(); //创建输出流
            byte[] b = new byte[1024];
            while (in.read(b) != -1) {
                os.write(b);
            }
            in.close();
            os.flush();
            os.close();
        } catch (Exception e) {
            if (null != in) {
                in.close();
            }
            if (null != os) {
                os.close();
            }
        }
    }



    /**
     *读取文件以流的形式输出
     * @param filePath  路径
     * @param contentType  输出格式
     * @param fileName  文件名称
     * @throws IOException
     */
    public static void outWordFile(String filePath,String fileName, HttpServletResponse response) throws IOException {
        // path是指欲下载的文件的路径。
        File file = new File(filePath);
        // 取得文件名。
        //String filename = file.getName();
        // 取得文件的后缀名。
        //String ext = filename.substring(filename.lastIndexOf(".") + 1).toUpperCase();
        // 以流的形式下载文件。
        InputStream fis = new BufferedInputStream(new FileInputStream(filePath));
        byte[] buffer = new byte[fis.available()];
        fis.read(buffer);
        fis.close();
        // 清空response
        response.reset();
        // 设置response的Header
        response.setHeader("Content-disposition", "attachment;filename=" + new String(fileName.getBytes("GB2312"),"iso8859-1"));
        response.addHeader("Content-Length", "" + file.length());
        OutputStream toClient = new BufferedOutputStream(response.getOutputStream());
        response.setContentType("application/octet-stream");
        toClient.write(buffer);
        toClient.flush();
        toClient.close();
    }



    /**
     * 填充word模板
     * @param readPath
     * @param outPath
     * @param list
     */
    public static void XWPFTemplateWord(String readPath, String outPath, List<Map<String, Object>> list){
        //将word模板转换成word
        try {
            XWPFTemplate.compile(readPath).render(new HashMap<String, Object>() {
                {
                    put("list", list);
                }
            }).writeToFile(outPath);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }


    /**
     * 流转换成文件
     * @param inputStream
     */
    public static void inputStreamToFile(InputStream inputStream,String filePath) {
        try {
            //新建文件
            File file = new File(filePath);
            if (file.exists()) {
                file.createNewFile();
            }
            OutputStream os = new FileOutputStream(file);
            int read = 0;
            byte[] bytes = new byte[1024 * 1024];
            //先读后写
            while ((read = inputStream.read(bytes)) > 0) {
                byte[] wBytes = new byte[read];
                System.arraycopy(bytes, 0, wBytes, 0, read);
                os.write(wBytes);
            }
            os.flush();
            os.close();
            inputStream.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    /**
     * 根据url下载文件流
     * @param urlStr
     * @return
     */
    public static InputStream getInputStreamFromUrl(String urlStr) {
        InputStream inputStream=null;
        try {
            //url解码
            URL url = new URL(java.net.URLDecoder.decode(urlStr, "UTF-8"));
            HttpURLConnection conn = (HttpURLConnection) url.openConnection();
            //设置超时间为3秒
            conn.setConnectTimeout(3 * 1000);
            //防止屏蔽程序抓取而返回403错误
            conn.setRequestProperty("User-Agent", "Mozilla/4.0 (compatible; MSIE 5.0; Windows NT; DigExt)");
            //得到输入流
            inputStream = conn.getInputStream();
        } catch (IOException e) {

        }
        return inputStream;
    }



    /**
     * 转换全部的pdf
     pdfPath
    type 是图片类型
     */
    public static List<String> pdf2Base64(String pdfPath) {
        // 将pdf装图片 并且自定义图片得格式大小
        File file = new File(pdfPath);
        List<String> list = new ArrayList<>();
        try {
            PDDocument doc = PDDocument.load(file);
            list = toBase64(doc);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return list;
    }


    /**
     * 转换全部的pdf
     byte[] decode
     type 是图片类型
     */
    public static List<String> pdf2Base64(byte[] decode) {
        List<String> list = new ArrayList<>();
        try {
            PDDocument doc = PDDocument.load(decode);
            list = toBase64(doc);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return list;
    }

    private static List<String> toBase64(PDDocument doc){
        List<String> list = new ArrayList<>();
        try {
            //PDDocument doc = PDDocument.load(file);
            PDFRenderer renderer = new PDFRenderer(doc);
            int pageCount = doc.getNumberOfPages();
            for (int i = 0; i < pageCount; i++) {
                // 控制清晰度 后面的100 值越大越清晰
                BufferedImage image = renderer.renderImageWithDPI(i, 100); // Windows native DPI
                // BufferedImage srcImage = resize(image, 240, 240);//产生缩略图
//                ImageIO.write(image, type, new File(fileAddress+"\\"+filename+"_"+(i+1)+"."+type));
                ByteArrayOutputStream bos = new ByteArrayOutputStream();
                ImageIO.write(image, "jpg", bos);
                byte[] bytes = bos.toByteArray();
                String base = "data:image/jpeg;base64,"+Base64.getEncoder().encodeToString(bytes);
                list.add(base);
//                System.out.println("base = " + base);
//                File file = new File("D:\\testAnySignEncPackage\\"+ i +"."+"jpg");
//                FileUtils.writeByteArrayToFile(file, bytes);
            }
            return list;
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }


    /**
     * 根据word转换成pdf
     *
     * @param inputFile
     * @return
     * @isdel isdel 是否在生成后删除传入文件
     */
    public static String word2PDF(String inputFile, boolean isdel) {
        String path = inputFile.substring(0, inputFile.lastIndexOf("."));
        String pdfPath = path + ".pdf";

//        //组合成pdf文件路径
//        if (word2PDF(inputFile, pdfPath)) {
//            //删除传过来的文件
//            if (isdel && FileUtil.exist(inputFile)) {
//                FileUtil.del(inputFile);
//            }
//            return pdfPath;
//        }
//        return null;

        try {
            XWPFDocument document;
            InputStream doc = new FileInputStream(inputFile);
            document = new XWPFDocument(doc);
            PdfOptions options = PdfOptions.create();
            OutputStream out = new FileOutputStream(pdfPath);
            PdfConverter.getInstance().convert(document, out, options);
            doc.close();
            out.close();

            //删除传过来的文件
            if (isdel && FileUtil.exist(inputFile)) {
                FileUtil.del(inputFile);
            }
            return pdfPath;
        } catch (Exception e) {
            return null;
        }
    }



    /**
     * 多个pdf合并成一个
     * @param files  多个pdf文件集合
     * @param newfile  生成的新文件路径
     * @return
     */
    public static boolean mergePdfFiles(List<String> files, String newfile) {
        boolean retValue = false;
        Document document = null;
        try {
            document = new Document(new PdfReader(files.get(0)).getPageSize(1));
            PdfCopy copy = new PdfCopy(document, new FileOutputStream(newfile));
            document.open();
            for (int i = 0; i < files.size(); i++) {
                PdfReader reader = new PdfReader(files.get(i));
                int n = reader.getNumberOfPages();
                for (int j = 1; j <= n; j++) {
                    document.newPage();
                    PdfImportedPage page = copy.getImportedPage(reader, j);
                    copy.addPage(page);
                }
            }
            retValue = true;
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            document.close();

        }
        return retValue;
    }


    /**
     * excel文件转换为PDF文件
     * @param readPath 需要转化的Excel文件地址，
     * @param outputPath 转换后的文件地址
     */
    public static void excel2pdf(String readPath,String outputPath) {
        if (!getLicense()) {   // 验证License 若不验证则转化出的pdf文档会有水印产生
            return ;
        }
        try {
            FileWriter writer = new FileWriter(outputPath);
            writer.close();
            File pdfFile = new File(outputPath); // 输出路径
            FileInputStream excelstream = new FileInputStream(readPath);
            Workbook wb = new Workbook(excelstream);// excel路径，这里是先把数据放进缓存表里，然后把缓存表转化成PDF
            FileOutputStream fileOS = new FileOutputStream(pdfFile);
            //** 自动缩放了
            int[] autoDrawSheets={3};
            //当excel中对应的sheet页宽度太大时，在PDF中会拆断并分页。此处等比缩放。
            autoDraw(wb,autoDrawSheets);
            //以下代码打开则只显示第一个sheet
            int[] showSheets={0};
            //隐藏workbook中不需要的sheet页。
            printSheetPage(wb,showSheets);
            PdfSaveOptions  pdfSaveOptions = new PdfSaveOptions();
            pdfSaveOptions.setOnePagePerSheet(true);
            wb.save(fileOS, pdfSaveOptions);
            fileOS.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * @param wb 设置打印的sheet自动拉伸比例
     * @param page 自动拉伸的页的sheet数组
     */
    public static void autoDraw(Workbook wb, int[] page){
        if (null!=page&&page.length>0){
            for (int i = 0; i < page.length; i++) {
                wb.getWorksheets().get(i).getHorizontalPageBreaks().clear();
                wb.getWorksheets().get(i).getVerticalPageBreaks().clear();
            }
        }
    }

    /**
     * 隐藏workbook中不需要的sheet页。
     * @param wb
     * @param page 显示页的sheet数组
     */
    public static void printSheetPage(Workbook wb, int[] page){
        for (int i= 1; i < wb.getWorksheets().getCount(); i++)  {
            wb.getWorksheets().get(i).setVisible(false);
        }
        if (null==page||page.length==0){
            wb.getWorksheets().get(0).setVisible(true);
        }else{
            for (int i = 0; i < page.length; i++) {
                wb.getWorksheets().get(i).setVisible(true);
            }
        }
    }


    //获取认证，去除水印
    public static boolean getLicense() {
        boolean result = false;
        try {
            InputStream is = FileUtils.class.getClassLoader().getResourceAsStream("\\license.xml");//这个文件应该是类似于密码验证(证书？)，用于获得去除水印的权限
            License aposeLic = new License();
            aposeLic.setLicense(is);
            result = true;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }

}
