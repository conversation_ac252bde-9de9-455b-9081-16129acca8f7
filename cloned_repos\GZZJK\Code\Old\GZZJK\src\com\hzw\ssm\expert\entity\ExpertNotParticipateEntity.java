package com.hzw.ssm.expert.entity;

import com.hzw.ssm.fw.base.BaseEntity;
import com.hzw.ssm.fw.util.Page;

/**
 * 专家不参加理由统计
 *
 */
public class ExpertNotParticipateEntity extends BaseEntity {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	/**
	 * 项目Id
	 */
	private String projectId;
	/**
	 * 专家名称
	 */
	private String expertName;
	/**
	 * 项目编号
	 */
	private String projectNo;
	/**
	 * 专家Id
	 */
	private String expertId;
	/** 分页 */
	private Page page;
	/**
	 * 项目名称
	 */
	private String projectName;
	/**
	 * 不参加理由
	 */
	private String reason;
	
	public Page getPage() {
		return page;
	}

	public void setPage(Page page) {
		this.page = page;
	}

	public String getProjectId() {
		return projectId;
	}

	public void setProjectId(String projectId) {
		this.projectId = projectId;
	}

	public String getExpertName() {
		return expertName;
	}

	public void setExpertName(String expertName) {
		this.expertName = expertName;
	}

	public String getExpertId() {
		return expertId;
	}

	public void setExpertId(String expertId) {
		this.expertId = expertId;
	}

	public String getProjectNo() {
		return projectNo;
	}

	public void setProjectNo(String projectNo) {
		this.projectNo = projectNo;
	}

	public String getProjectName() {
		return projectName;
	}

	public void setProjectName(String projectName) {
		this.projectName = projectName;
	}

	public String getReason() {
		return reason;
	}

	public void setReason(String reason) {
		this.reason = reason;
	}

	
}
