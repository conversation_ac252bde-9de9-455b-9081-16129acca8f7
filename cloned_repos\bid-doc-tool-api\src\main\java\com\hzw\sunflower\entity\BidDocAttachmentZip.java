package com.hzw.sunflower.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.hzw.sunflower.common.BaseBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@TableName("t_bid_doc_attachment_zip")
@ApiModel(value = "打包文件表", description = "")
public class BidDocAttachmentZip extends BaseBean implements Serializable {

    @ApiModelProperty(value = "主键id")
    private Long id;

    @ApiModelProperty(value = "招标文件附件目录id")
    private Long docId;

    @ApiModelProperty(value = "主键id")
    private Long zipFileId;

    @ApiModelProperty(value = "招标文件附件目录id")
    private String zipFileName;

    @ApiModelProperty(value = "状态（1未压缩2进行中3已经压缩成功4失败）")
    private Integer status;
}
