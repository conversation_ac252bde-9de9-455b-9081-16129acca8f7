<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzw.sunflower.dao.OperationLogMapper">

    <resultMap type="com.hzw.sunflower.entity.OperationLog" id="SysOperLogResult">
        <id property="id" column="id"/>
        <result property="title" column="title"/>
        <result property="description" column="description"/>
        <result property="businessType" column="business_type"/>
        <result property="method" column="method"/>
        <result property="requestMethod" column="request_method"/>
        <result property="operatorType" column="operator_type"/>
        <result property="operId" column="oper_id"/>
        <result property="operName" column="oper_name"/>
        <result property="deptName" column="dept_name"/>
        <result property="companyId" column="company_id"/>
        <result property="companyName" column="company_name"/>
        <result property="operUrl" column="oper_url"/>
        <result property="operIp" column="oper_ip"/>
        <result property="operLocation" column="oper_location"/>
        <result property="operParam" column="oper_param"/>
        <result property="jsonResult" column="json_result"/>
        <result property="status" column="status"/>
        <result property="errorMsg" column="error_msg"/>
        <result property="operTime" column="oper_time"/>
    </resultMap>

    <sql id="selectOperLogVo">
        select id, title, description, business_type, method, request_method, operator_type, oper_id, oper_name, dept_name, company_id, company_name, oper_url, oper_ip, oper_location, oper_param, json_result, status, error_msg, oper_time
        from t_oper_log
    </sql>

    <insert id="insertOperlog" parameterType="com.hzw.sunflower.entity.OperationLog">
		insert into t_oper_log(title, description, business_type, method, request_method, operator_type, oper_id, oper_name, dept_name, company_id, company_name, oper_url, oper_ip, oper_location, oper_param, json_result, status, error_msg, oper_time,user_agent,head_message)
        values (#{operlog.title},#{operlog.description}, #{operlog.businessType}, #{operlog.method}, #{operlog.requestMethod}, #{operlog.operatorType}, #{operlog.operId}, #{operlog.operName}, #{operlog.deptName}, #{operlog.companyId}, #{operlog.companyName}, #{operlog.operUrl}, #{operlog.operIp}, #{operlog.operLocation}, #{operlog.operParam}, #{operlog.jsonResult}, #{operlog.status}, #{operlog.errorMsg}, sysdate(), #{operlog.userAgent}, #{operlog.headMessage})
	</insert>

    <select id="selectOperLogList" parameterType="com.hzw.sunflower.entity.OperationLog" resultMap="SysOperLogResult">
        <include refid="selectOperLogVo"/>
        <where>
            <if test="operlog.title != null and operlog.title != ''">
                AND title like concat('%', #{operlog.title}, '%')
            </if>
            <if test="operlog.businessType != null and operlog.businessType != ''">
                AND business_type = #{operlog.businessType}
            </if>
            <if test="operlog.businessTypes != null and operlog.businessTypes.length > 0">
                AND business_type in
                <foreach collection="operlog.businessTypes" item="businessType" open="(" separator="," close=")">
                    #{businessType}
                </foreach>
            </if>
            <if test="operlog.status != null">
                AND status = #{operlog.status}
            </if>
            <if test="operlog.operName != null and operlog.operName != ''">
                AND oper_name like concat('%', #{operlog.operName}, '%')
            </if>
            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                and date_format(oper_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
            </if>
            <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                and date_format(oper_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
            </if>
        </where>
        order by oper_id desc
    </select>

    <delete id="deleteOperLogByIds" parameterType="Long">
        delete from t_oper_log where oper_id in
        <foreach collection="operIds" item="operId" open="(" separator="," close=")">
            #{operId}
        </foreach>
    </delete>

    <select id="selectOperLogById" parameterType="Long" resultMap="SysOperLogResult">
        <include refid="selectOperLogVo"/>
        where oper_id = #{operId}
    </select>

    <update id="cleanOperLog">
        truncate table t_oper_log
    </update>

</mapper> 