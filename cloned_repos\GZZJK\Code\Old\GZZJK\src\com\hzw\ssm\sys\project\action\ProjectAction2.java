package com.hzw.ssm.sys.project.action;
import java.util.List;

import javax.servlet.http.HttpSession;

import org.apache.struts2.convention.annotation.Action;
import org.apache.struts2.convention.annotation.Namespace;
import org.apache.struts2.convention.annotation.ParentPackage;
import org.apache.struts2.convention.annotation.Result;
import org.apache.struts2.convention.annotation.Results;
import org.springframework.beans.factory.annotation.Autowired;

import com.hzw.ssm.date.service.ZJKCalendarService;
import com.hzw.ssm.expert.entity.Appraise;
import com.hzw.ssm.expert.entity.AppraiseInfo;
import com.hzw.ssm.expert.entity.AppraiseRemark;
import com.hzw.ssm.expert.entity.ExpertInfoEntity;
import com.hzw.ssm.fw.base.BaseAction;
import com.hzw.ssm.sys.project.entity.ApplyRecordEntity;
import com.hzw.ssm.sys.project.entity.ConditionEntity;
import com.hzw.ssm.sys.project.entity.ExtractRecordEntity;
import com.hzw.ssm.sys.project.entity.ProjectAuditEntity;
import com.hzw.ssm.sys.project.entity.ProjectEntity;
import com.hzw.ssm.sys.project.entity.ResultEntity;
import com.hzw.ssm.sys.project.service.ProjectService;
import com.hzw.ssm.sys.user.entity.UserEntity;
import com.hzw.ssm.sys.user.service.UserService;

@Namespace("/project2")
@ParentPackage(value = "default")
@Results( {
		@Result(name = "toProjectList2", location = "/jsp/project/projectAuditByDirector.jsp")})
public class ProjectAction2 extends BaseAction {
	private static final long serialVersionUID = -8823858495799309882L;
	private String isSave;//是否为保存
	private List<ExpertInfoEntity> expertList;//专家信息列表
	private ProjectEntity project;
	private ProjectEntity projectEntity;
	private ProjectAuditEntity projectAuditEntity;
	private ResultEntity result;
	private ApplyRecordEntity apply;
	private List<ResultEntity> resultList;//专家抽取结果列表
	private List<List<ResultEntity>> extractList;//不同轮次抽取结果集
	private ConditionEntity conEntity;
	private ExpertInfoEntity expert;
	private List<ExpertInfoEntity> extractExpertList;//已抽取用于指定专家页面展示
	private String majorStr;//专业选择
	private String expertType;         // 以选中的评标专业
	private String message;//
	List<UserEntity> userList;
	private List<UserEntity> operatorList;//经办人列表
	private List<ProjectEntity> projectList;//专家抽取结果列表
	private List<ConditionEntity> conditionList;//专家抽取结果列表
	
	private String extractResultId;
	
	private ExpertInfoEntity expertInfoEntity;
	
	/** 评分项 */
	private List<AppraiseInfo> appInfoList;
	
	private List<Appraise> appraiseList;
	
	/** 专家评价备注 */
	private AppraiseRemark appraiseRemark;
	
	/** 指定专家列表 */
	private List<ExpertInfoEntity> expertInfoList;

	/** 抽取记录 */
	private List<ExtractRecordEntity> exRecordList;
	
	/** 本地专家人数by条件 */
	private Integer localExpertCount;
	/** 国家级专家人数by条件 */
	private Integer seniorExpertCount;
	/** 删除记录原因*/
	private String reason;
	
	private boolean applyFlag=false;
	
	private String applyDate;
	
	private boolean isHoliday=false;
	
	@Autowired
	private ProjectService projectService;
	
	@Autowired
	private UserService userService;
	
	@Autowired
	private ZJKCalendarService zjkCalendarService;
	
	/**
	 * 查询专家抽取审核列表
	 * 
	 * @return
	 */
	@Action("queryAuditProjectListDR")
	public String queryProjectList2() {
		this.context();
		// 当前session
		HttpSession session = getRequest().getSession();
		// 当前用户信息
		UserEntity user = (UserEntity) session.getAttribute("userInfo");
		
		try {
			
			if (null == projectEntity){
				projectEntity = new ProjectEntity();
			}
				projectEntity.setPage(this.getPage());
			projectEntity.setStatus_("90,91,92");
				
			// 查询部门下所有用户ID
			UserEntity ue = new UserEntity();
			ue.setUser_id(user.getUser_id());
			List<UserEntity> lstUser = userService.getUserInfoByUserId(ue);
			if(lstUser != null && lstUser.size()>0){
				String createUsers = "'";
				for (int i = 0; i < lstUser.size(); i++) {
					createUsers+=lstUser.get(i).getUser_id()+"','";
					//createUsers.concat(lstUser.get(i).getUser_id()).concat("','");
				}
				createUsers = createUsers.substring(0, createUsers.length()-2);
				projectEntity.setCreateUsers(createUsers);
			}
			this.getRequest().setAttribute("flag", "2");
			projectList = projectService.queryPageProjectList(projectEntity, user);
		
			
		} catch (Exception e) {
			e.printStackTrace();
		}
		
		return "toProjectList2";
	}
	


	public ProjectEntity getProject() {
		return project;
	}

	public void setProject(ProjectEntity project) {
		this.project = project;
	}

	public ProjectEntity getProjectEntity() {
		return projectEntity;
	}
	public void setProjectEntity(ProjectEntity projectEntity) {
		this.projectEntity = projectEntity;
	}
	public String getIsSave() {
		return isSave;
	}

	public void setIsSave(String isSave) {
		this.isSave = isSave;
	}

	public ConditionEntity getConEntity() {
		return conEntity;
	}

	public void setConEntity(ConditionEntity conEntity) {
		this.conEntity = conEntity;
	}

	public List<ExpertInfoEntity> getExpertList() {
		return expertList;
	}

	public void setExpertList(List<ExpertInfoEntity> expertList) {
		this.expertList = expertList;
	}

	public ResultEntity getResult() {
		return result;
	}

	public void setResult(ResultEntity result) {
		this.result = result;
	}

	public List<ResultEntity> getResultList() {
		return resultList;
	}

	public void setResultList(List<ResultEntity> resultList) {
		this.resultList = resultList;
	}

	public List<List<ResultEntity>> getExtractList() {
		return extractList;
	}

	public void setExtractList(List<List<ResultEntity>> extractList) {
		this.extractList = extractList;
	}

	public ApplyRecordEntity getApply() {
		return apply;
	}

	public void setApply(ApplyRecordEntity apply) {
		this.apply = apply;
	}

	public ProjectService getProjectService() {
		return projectService;
	}

	public void setProjectService(ProjectService projectService) {
		this.projectService = projectService;
	}

	public ExpertInfoEntity getExpert() {
		return expert;
	}

	public void setExpert(ExpertInfoEntity expert) {
		this.expert = expert;
	}

	public List<ExpertInfoEntity> getExtractExpertList() {
		return extractExpertList;
	}

	public void setExtractExpertList(List<ExpertInfoEntity> extractExpertList) {
		this.extractExpertList = extractExpertList;
	}

	public String getMajorStr() {
		return majorStr;
	}

	public void setMajorStr(String majorStr) {
		this.majorStr = majorStr;
	}

	public String getExpertType() {
		return expertType;
	}
	
	public void setExpertType(String expertType) {
		this.expertType = expertType;
	}
	
	public String getMessage() {
		return message;
	}

	public void setMessage(String message) {
		this.message = message;
	}

	public List<UserEntity> getUserList() {
		return userList;
	}

	public void setUserList(List<UserEntity> userList) {
		this.userList = userList;
	}

	public List<UserEntity> getOperatorList() {
		return operatorList;
	}

	public void setOperatorList(List<UserEntity> operatorList) {
		this.operatorList = operatorList;
	}
	public List<ProjectEntity> getProjectList() {
		return projectList;
	}
	public void setProjectList(List<ProjectEntity> projectList) {
		this.projectList = projectList;
	}
	public List<ConditionEntity> getConditionList() {
		return conditionList;
	}
	public void setConditionList(List<ConditionEntity> conditionList) {
		this.conditionList = conditionList;
	}
	public List<ExpertInfoEntity> getExpertInfoList() {
		return expertInfoList;
	}
	public void setExpertInfoList(List<ExpertInfoEntity> expertInfoList) {
		this.expertInfoList = expertInfoList;
	}
	public List<ExtractRecordEntity> getExRecordList() {
		return exRecordList;
	}
	public void setExRecordList(List<ExtractRecordEntity> exRecordList) {
		this.exRecordList = exRecordList;
	}
	public ProjectAuditEntity getProjectAuditEntity() {
		return projectAuditEntity;
	}
	public void setProjectAuditEntity(ProjectAuditEntity projectAuditEntity) {
		this.projectAuditEntity = projectAuditEntity;
	}
	public String getExtractResultId() {
		return extractResultId;
	}
	public void setExtractResultId(String extractResultId) {
		this.extractResultId = extractResultId;
	}
	public ExpertInfoEntity getExpertInfoEntity() {
		return expertInfoEntity;
	}
	public void setExpertInfoEntity(ExpertInfoEntity expertInfoEntity) {
		this.expertInfoEntity = expertInfoEntity;
	}
	public List<AppraiseInfo> getAppInfoList() {
		return appInfoList;
	}
	public void setAppInfoList(List<AppraiseInfo> appInfoList) {
		this.appInfoList = appInfoList;
	}
	public List<Appraise> getAppraiseList() {
		return appraiseList;
	}
	public void setAppraiseList(List<Appraise> appraiseList) {
		this.appraiseList = appraiseList;
	}
	public AppraiseRemark getAppraiseRemark() {
		return appraiseRemark;
	}
	public void setAppraiseRemark(AppraiseRemark appraiseRemark) {
		this.appraiseRemark = appraiseRemark;
	}

	public Integer getLocalExpertCount() {
		return localExpertCount;
	}

	public void setLocalExpertCount(Integer localExpertCount) {
		this.localExpertCount = localExpertCount;
	}

	public Integer getSeniorExpertCount() {
		return seniorExpertCount;
	}

	public void setSeniorExpertCount(Integer seniorExpertCount) {
		this.seniorExpertCount = seniorExpertCount;
	}

	public String getReason() {
		return reason;
	}

	public void setReason(String reason) {
		this.reason = reason;
	}


	public boolean isApplyFlag() {
		return applyFlag;
	}


	public void setApplyFlag(boolean applyFlag) {
		this.applyFlag = applyFlag;
	}


	public String getApplyDate() {
		return applyDate;
	}


	public void setApplyDate(String applyDate) {
		this.applyDate = applyDate;
	}


	public boolean isHoliday() {
		return isHoliday;
	}


	public void setHoliday(boolean isHoliday) {
		this.isHoliday = isHoliday;
	}


	public ZJKCalendarService getZjkCalendarService() {
		return zjkCalendarService;
	}


	public void setZjkCalendarService(ZJKCalendarService zjkCalendarService) {
		this.zjkCalendarService = zjkCalendarService;
	}
	
	
	
}
