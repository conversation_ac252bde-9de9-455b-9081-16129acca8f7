package com.hzw.sunflower.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hzw.sunflower.controller.project.request.AgentProjectReq;
import com.hzw.sunflower.dto.ProjectShareDTO;
import com.hzw.sunflower.entity.ProjectShareNcc;

import java.util.List;

/**
 * ProjectShareNccService接口
 *
 */
public interface ProjectShareNccService extends IService<ProjectShareNcc> {


    /**
     * 更新项目分摊成本信息
     */
    void updateProjectShareNccList(AgentProjectReq agentProject,List<ProjectShareDTO> allShareList);

    List<ProjectShareNcc> findInfo(Long id);

    List<ProjectShareNcc> listNCC(Long projectId);
}
