<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper 
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd"> 
    
<mapper namespace="com.hzw.ssm.fw.listener.dao.InterceptorMapper">
	<!-- 验证用户名和密码是否匹配 -->
	<select id="hasQA" parameterType="Map" resultType="int">
		select count(*)
		  from ts_user a
		  left join ts_auth b
		    on a.role_id = b.role_id
		  left join ts_menu c
		    on b.menu_id = c.menu_id
		 where a.delete_flag = 0
		   and a.login_code = '${login_id}'
		   and INSTR(c.menu_url, '${actionName}') > 0
	</select>
	
	<!-- 权限名称获取 -->
	<select id="getMothod" parameterType="Map" resultType="String">
		select b.menu_name from ts_menu b where b.menu_url like '%${actionName}%' and b.delete_flag=0
	</select>
</mapper>
