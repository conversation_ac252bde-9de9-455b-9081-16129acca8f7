///***
/// * 全国省市地区选择器
/// */

import 'dart:convert';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import 'package:flutter_main_app/src/constants/theme.dart';
import 'package:flutter_main_app/src/utils/ScreenUtils.dart';

import 'package:flutter_main_app/src/models/common/RegionModel.dart';

class CustomChinaRegionsPicker extends StatefulWidget {
  const CustomChinaRegionsPicker({
    Key key,
    this.title = '选择城市',
    this.initialRegions = const <RegionModel>[],
  }) : assert(initialRegions != null),
    super(key: key);

  final String title;

  final List<RegionModel> initialRegions;

  @override
  _CustomChinaRegionsPickerState createState() => _CustomChinaRegionsPickerState();
}

class _CustomChinaRegionsPickerState extends State<CustomChinaRegionsPicker> {

  /// * JSON 数据本地路径
  static const String _provinceJsonPath = 'assets/regions/province.json';
  static const String _cityJsonPath = 'assets/regions/city.json';

  /// * 存储地区数据
  List<RegionModel> _provinces;
  List<List<RegionModel>> _cities;

  /// * 下标
  int _provinceIndex;
  int _cityIndex;

  /// * 滚动控制器
  FixedExtentScrollController _provinceController;
  FixedExtentScrollController _cityController;

  @override
  void initState() {
    super.initState();

    this._provinceIndex = 0;
    this._cityIndex = 0;
  }

  @override
  void dispose() {
    this._provinceController.dispose();
    this._cityController.dispose();

    super.dispose();
  }

  /// * 初始化地区数据
  Future<bool> _initRegionData() async {
    /// * 只初始化一次
    if (this._provinces == null || this._cities == null) {
      await this._initProvinceData();
      await this._initCityData();

      int index;

      /// * 获取初始值的下标
      if (widget.initialRegions.length == 2) {
        index = 0;

        for (int i = 0, len = this._provinces.length; i < len; i++) {
          if (this._provinces[i].code == widget.initialRegions[0].code) {
            index = i;
            break;
          }
        }

        this._provinceIndex = index;

        index = 0;

        for (int i = 0, len = this._cities[this._provinceIndex].length; i < len; i++) {
          if (this._cities[this._provinceIndex][i].code == widget.initialRegions[1].code) {
            index = i;
            break;
          }
        }

        this._cityIndex = index;
      }

      /// * 滚动至相应初始值
      this._provinceController = FixedExtentScrollController(initialItem: this._provinceIndex);
      this._cityController = FixedExtentScrollController(initialItem: this._cityIndex);
    }

    return this._provinces != null && this._cities != null;
  }

  /// * 初始化省级数据
  Future<List<RegionModel>> _initProvinceData() async {
    String _provinceStr = await rootBundle.loadString(_provinceJsonPath);
    List _provinceList = json.decode(_provinceStr) as List;

    this._provinces = _provinceList.map<RegionModel>(
      (json) => RegionModel.fromJson(json)
    ).toList();

    return this._provinces;
  }

  /// * 初始化市级数据
  Future<List<List<RegionModel>>> _initCityData() async {
    String _cityStr = await rootBundle.loadString(_cityJsonPath);
    List _cityList = json.decode(_cityStr) as List;

    this._cities = _cityList.map<List<RegionModel>>(
      (list) => list.map<RegionModel>(
        (json) => RegionModel.fromJson(json)
      ).toList()
    ).toList();

    return this._cities;
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      child: Container(
        width: ScreenUtils.screenWidth,
        color: themeContentBackgroundColor,
        child: SafeArea(
          top: false,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              /// * 功能按钮 - 取消|确定
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 30.0),
                width: ScreenUtils.screenWidth,
                height: 42.0,
                color: const Color(0xffe5e5e5),
                child: Row(
                  mainAxisSize: MainAxisSize.max,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: <Widget>[
                    GestureDetector(
                      child: Text(
                        '取消',
                        style: TextStyle(
                          fontSize: 14.0,
                          color: const Color(0xff999999),
                        ),
                      ),
                      onTap: () {
                        this._regionSelectionCanceled(context);
                      },
                    ),
                    Text(
                      widget.title,
                      style: TextStyle(
                        fontSize: 14.0,
                        color: themeTextColor,
                      ),
                    ),
                    GestureDetector(
                      child: Text(
                        '确定',
                        style: TextStyle(
                          fontSize: 14.0,
                          color: themeActiveColor,
                        ),
                      ),
                      onTap: () {
                        this._regionSelectionConfirmed(context);
                      },
                    ),
                  ],
                ),
              ),
              /// * 选项列表
              Container(
                width: ScreenUtils.screenWidth,
                height: 256.0,
                color: Colors.white,
                child: FutureBuilder(
                  future: this._initRegionData(),
                  builder: (BuildContext context, AsyncSnapshot snapshot) {
                    if (snapshot.hasData) {
                      return Row(
                        mainAxisSize: MainAxisSize.max,
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: <Widget>[
                          Expanded(
                            child: CupertinoPicker.builder(
                              backgroundColor: Colors.white,
                              scrollController: this._provinceController,
                              itemExtent: 39.0,
                              onSelectedItemChanged: (int index) {
                                setState(() {
                                  this._provinceIndex = index;

                                  this._cityIndex = 0;

                                  this._cityController.animateToItem(
                                    this._cityIndex,
                                    duration: Duration(milliseconds: 300),
                                    curve: Curves.easeInOut,
                                  );
                                });
                              },
                              itemBuilder: (BuildContext context, int index) {
                                return Center(
                                  child: Text('${this._provinces[index].name ?? ""}'),
                                );
                              },
                              childCount: this._provinces.length ?? 0,
                            ),
                          ),

                          Expanded(
                            child: CupertinoPicker.builder(
                              backgroundColor: Colors.white,
                              scrollController: this._cityController,
                              itemExtent: 39.0,
                              onSelectedItemChanged: (int index) {
                                setState(() {
                                  this._cityIndex = index;
                                });
                              },
                              itemBuilder: (BuildContext context, int index) {
                                return Center(
                                  child: Text('${this._cities[this._provinceIndex][index].name ?? ""}'),
                                );
                              },
                              childCount: this._cities[this._provinceIndex].length ?? 0,
                            ),
                          ),
                        ],
                      );
                    } else {
                      return Container();
                    }

                    /// * 不知道为什么，下面的写法会出问题 - picker 划不动
                    // switch (snapshot.connectionState) {
                    //   case ConnectionState.done:
                    //     return Row();
                    //   case ConnectionState.none:
                    //   case ConnectionState.active:
                    //   case ConnectionState.waiting:
                    //   default:
                    //     return Container();
                    // }
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  ///**
  /// * 点击取消
  /// * 路由弹出，什么都不返回
  /// *
  /// * @param {BuildContext} context
  /// */
  void _regionSelectionCanceled(BuildContext context) {
    Navigator.of(context).pop();
  }

  ///**
  /// * 点击确定
  /// * 路由弹出，并返回当前设置
  /// *
  /// * @param {BuildContext} context
  /// *
  /// * @return {List<Region>}
  /// */
  void _regionSelectionConfirmed(BuildContext context) {
    Navigator.of(context).pop(<RegionModel>[
      this._provinces[this._provinceIndex],
      this._cities[this._provinceIndex][this._cityIndex],
    ]);
  }

}
