<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper 
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd"> 

<mapper namespace="com.hzw.ssm.sys.user.dao.UserMapper">
<!-- 根据页面条件查询用户信息列表 -->
	<select id="queryPageUser" parameterType="UserEntity" resultType="UserEntity">
		SELECT
			tsu.USER_ID,
			tsu.ROLE_ID,
			tsu.USER_NAME,
			tsu.LOGIN_CODE,
			tsu.PASSWORD,
			tsu.SEX,
			tsu.MOBILE,
			tsu.EMAIL,
			tsu.TEL,
			tsu.ENABLED_FLAG,
			tsu.delete_flag,
			tsr.role_name
		FROM ts_user tsu
		left join ts_role tsr on tsu.role_id=tsr.role_id 
		where 1=1 and tsu.delete_flag!=1 and (login_code IS NULL OR login_code!='admin')
		<if test="login_code!=null and login_code!=''">
			and tsu.LOGIN_CODE like '%${login_code}%'
		</if>
		<if test="user_name!=null and user_name!=''">
			and tsu.user_name like '%${user_name}%'
		</if>
		<if test="role_name!=null and role_name!=''">
			and tsr.role_name like '%${role_name}%'
		</if>
		<if test="user_post!=null and user_post!=''">
			and tsu.user_post='${user_post}'
		</if>
		ORDER BY tsu.role_id desc, tsu.user_id desc
	</select>
		<!-- 新增用户对象 -->
	<insert id="insertUser" parameterType="UserEntity">
	    INSERT INTO ts_user(
	          USER_ID,
	          USER_NAME,
	          LOGIN_CODE,
	          PASSWORD,
	          SEX,
	          ROLE_ID,
	          MOBILE,
	          TEL,
	          EMAIL,
	          ENABLED_FLAG,
	          CREATE_ID,
	          CREATE_TIME,
	          MODIFY_ID,
	          MODIFY_TIME,
	          DELETE_FLAG,
	          DEPARTMENT
	     ) VALUES (#{user_id},#{user_name},#{login_code},#{password},1,#{role_id},#{mobile},#{tel},#{email},#{enabled_flag},#{create_id},#{create_time},#{modify_id},#{modify_time},0,#{department,jdbcType=VARCHAR})
	</insert>
	<!-- 用户修改密码 -->
	<update id="changePassword" parameterType="UserEntity">
		UPDATE ts_user SET MODIFY_ID=#{modify_id},MODIFY_TIME=#{modify_time},password=#{password} WHERE user_id=#{user_id}
	</update>
	<!-- 重置用户密码 -->
	<update id="resetPassword" parameterType="UserEntity">
		UPDATE ts_user SET
		MODIFY_ID=#{modify_id},MODIFY_TIME=#{modify_time},password=#{password} WHERE user_id=#{user_id}
	</update>
	<!-- 根据用户编号查询用户信息 -->
	<select id="selectUserById" parameterType="String" resultType="UserEntity">
		SELECT
			U.USER_ID,
			U.ROLE_ID,
			U.USER_NAME,
			U.LOGIN_CODE,
			U.PASSWORD,
			U.SEX,
			U.MOBILE,
			U.EMAIL,
			U.TEL,
			U.ENABLED_FLAG,
			U.delete_flag,
			U.DEPARTMENT,
			R.ROLE_NAME ,
			U.RECOMMEND_CODE recommendCode,
			U.QR_CODE
		FROM ts_user U
		LEFT JOIN TS_ROLE R ON U.ROLE_ID = R.ROLE_ID 
		WHERE user_id = #{id}
	</select>
	<!-- 根据用户编号查询用户信息 -->
	<select id="getUserInfoByUserId" parameterType="UserEntity" resultType="UserEntity">
		select 
			user_id
	  	from ts_user a
	 	where a.department =
	       (select b.department
	          from ts_user b
	         where b.user_id = #{user_id})
	   and a.user_id != #{user_id}
	</select>
	<!-- 禁用用户 -->
	<update id="invalidUser" parameterType="UserEntity">
		UPDATE ts_user
		SET MODIFY_ID=#{modify_id},MODIFY_TIME=#{modify_time}, ENABLED_FLAG=2 WHERE user_id=#{user_id}
	</update>
	<!-- 重新启用用户 -->
	<update id="validUser" parameterType="UserEntity">
		UPDATE ts_user SET
		MODIFY_ID=#{modify_id},MODIFY_TIME=#{modify_time},ENABLED_FLAG=1  WHERE user_id=#{user_id}
	</update>
	<!-- 删除用户 -->
	<delete id="deleteUser" parameterType="UserEntity">
		update ts_user set MODIFY_ID=#{modify_id},MODIFY_TIME=#{modify_time},delete_flag=1 where user_id=#{user_id}
	</delete>
	<!-- 更新用户对象 -->
	<update id="updateUser" parameterType="UserEntity">
	    UPDATE ts_user SET
	          ROLE_ID=#{role_id},
	          USER_NAME=#{user_name},
	          LOGIN_CODE=#{login_code}, 
	          MOBILE=#{mobile},
	          EMAIL=#{email},
	          TEL=#{tel},
	          ENABLED_FLAG=#{enabled_flag}, 
	          MODIFY_ID=#{modify_id},
	          MODIFY_TIME=#{modify_time} 
	          <if test="password!=null and password!=''">
	          ,password=#{password}
	          </if>
	          <if test="department!=null and department!=''">
	          ,DEPARTMENT=#{department} 
	          </if>
	    WHERE user_id = #{user_id}
	</update>
	<!-- 用户修改自己信息 -->
	<update id="updatePersonnalUser" parameterType="UserEntity">
	    UPDATE ts_user SET
	          USER_NAME=#{user_name}
	          <if test="mobile!=null and mobile!=''">
	          	,MOBILE=#{mobile}
	          </if>
	          <if test="email!=null and email!=''">
	          	,EMAIL=#{email}
	          </if>
	          <if test="tel!=null and tel!=''">
	          	,TEL=#{tel}
	          </if>
	          <if test="modify_id!=null and modify_id!='' and modify_id!=0">
	          	,MODIFY_ID=#{modify_id}
	          </if>
	          <if test="modify_time!=null and modify_time!=''">
	          	,MODIFY_TIME=#{modify_time} 
	          </if>
	          <if test="department!=null and department!=''">
	          	,DEPARTMENT=#{department} 
	          </if>
	           <if test="sms_code!=null and sms_code!=''">
	          	,SMS_CODE=#{sms_code} 
	          </if>
	           <if test="sms_time!=null">
	          	, SMS_TIME=#{sms_time}
	          </if>
	          <if test="login_fail_count!=null">
	          	, LOGIN_FAIL_COUNT=#{login_fail_count}
	          </if>
	          <if test="login_fail_time!=null">
	          	, LOGIN_FAIL_TIME=#{login_fail_time}
	          </if>
	    WHERE user_id = #{user_id}
	</update>
	
	<!--验证登录名在数据库中是否已经存在  -->
	<select id="checkLoginCode" parameterType="UserEntity" resultType="int">
		select count(1) from ts_user tu where tu.login_code=#{login_code}
		<if test="user_id!=null and user_id!=''">
			and tu.user_id != #{user_id}
		</if>
		and tu.DELETE_FLAG = 0
	</select>
	
	<!--验证手机号码在数据库中是否已经存在  -->
	<select id="checkMobile" parameterType="UserEntity" resultType="int">
		select count(1) from ts_user tu where tu.mobile=#{mobile} AND  TU.DELETE_FLAG = 1
		<if test="user_id!=null and user_id!=''">
			and tu.user_id != #{user_id}
		</if>
		and tu.DELETE_FLAG = 0
	</select>
	
	<!-- 查询机电交易中心抽取人的手机号码 -->
	<select id="queryExtractMobiles" parameterType="String" resultType="UserEntity">
		select user_id, user_name, mobile from ts_user us 
		left join ts_role r on r.role_id = us.role_id
		where r.role_name = #{role_name}
	</select>
	<!-- 查询登陆用户可访问路径 -->
	<select id="queryUrlByIdAndUrl" parameterType="Map" resultType="int" >
		select count(1) from Ts_User u
		left join TS_AUTH a on u.role_id=a.role_id
		left join TS_MENU m on a.menu_id=m.menu_id
		where u.user_id = #{userId}
		and m.menu_url = #{url}
	</select>

	<!-- 查询用户手机号是否存在 -->
	<select id="queryUser" parameterType="String" resultType="UserEntity">
		SELECT
			U.USER_ID,
			U.ROLE_ID,
			U.USER_NAME,
			U.LOGIN_CODE,
			U.PASSWORD,
			U.SEX,
			U.MOBILE,
			U.EMAIL,
			U.TEL,
			U.ENABLED_FLAG,
			U.delete_flag,
			U.DEPARTMENT,
			U.SMS_CODE,
			U.SMS_TIME,
			R.ROLE_NAME,
			U.LOGIN_FAIL_TIME,
			U.LOGIN_FAIL_COUNT
		FROM ts_user U
		LEFT JOIN TS_ROLE R ON U.ROLE_ID = R.ROLE_ID 
		WHERE login_code = #{login_code}
	</select>
	<select id="selectUserRcode" parameterType="String" resultType="Integer">
		select count(1) FROM ts_user U
		where U.QR_CODE=#{code}
	</select>
	<update id="updateQCode" parameterType="UserEntity">
	update ts_user set QR_CODE= #{qr_code} where user_id=#{user_id}
	</update>
	
	<select id="selectRecommendExamine" parameterType="String" resultType="HomeQrEntity">
		select qestatus as qeStatus,count(1) as qeCount from (
		SELECT ROW_NUMBER()over(PARTITION by user_ID ORDER BY qestatus DESC) as ro,qestatus from ( select (case
		                 when t.status = '3' then
		                  4
		                 when t.status = '8' then
		                  4
		                 when t.status = '1' then
		                  2
		                 when t.status = '2' then
		                  2
		                 when t.status = '5' then
		                  2
		                 when t.status = '6' then
		                  2
		                 when t.status = '0' then
		                  (case
		                    when e.status = '0' then
		                     4
		                    when e.status = '1' then
		                     1
		                    when e.status is null then
		                     3
		                  end)
		               end) qestatus,t.user_id
		          from t_expert_info t
		          left join t_expert_audit e
		            on t.User_id = e.expert_id
		         where t.REFERRER_QRCODE =#{qrCode}
		         order by qestatus desc )    
		) dual  where ro=1
		group by qestatus
	</select>
	
	<select id="selectRecommend" parameterType="String" resultType="Integer">
		select  t.plan_number from t_recommend t 
		left join  t_recommend_plan r on r.id =t.plan_id 
		where r.year=#{newdate} and t.recommender_id=#{user_id}
	</select>
	<select id="checkExtract" parameterType="String" resultType="int">
		select count(1) from t_expert_info where user_id=#{userId}
	</select>
	<select id="selectUserJurisdiction" parameterType="String" resultType="String" >
		select m.menu_url from Ts_User u
		left join TS_AUTH a on u.role_id=a.role_id
		left join TS_MENU m on a.menu_id=m.menu_id
		where u.user_id = #{userId}
		and m.menu_url is not null
	</select>
</mapper>