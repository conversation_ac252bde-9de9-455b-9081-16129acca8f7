package com.hzw.sunflower.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.hzw.sunflower.common.BaseBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@TableName("t_foreign_currency_rate")
@ApiModel(value = "t_foreign_currency_rate", description = "外币转换汇率凭证表")
public class ForeignCurrencyRate extends BaseBean {

    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("转换表ID")
    private Long convertId;

    @ApiModelProperty("汇率凭证ID")
    private Long rateFileId;

}
