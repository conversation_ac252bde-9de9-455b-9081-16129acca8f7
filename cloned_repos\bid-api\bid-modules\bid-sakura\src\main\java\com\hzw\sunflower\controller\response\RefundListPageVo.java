package com.hzw.sunflower.controller.response;

import com.hzw.sunflower.common.Paging;
import com.hzw.sunflower.controller.response.RefundListVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@ApiModel(description = "保证金收款流水分页返回实体类")
public class RefundListPageVo implements Serializable {

    @ApiModelProperty("分页数据")
    Paging<RefundListVo> pageData;

    @ApiModelProperty("总金额")
    BigDecimal totalAmount;

}
