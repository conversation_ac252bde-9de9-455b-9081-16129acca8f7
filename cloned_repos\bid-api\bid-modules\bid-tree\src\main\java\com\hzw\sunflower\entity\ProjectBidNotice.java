package com.hzw.sunflower.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hzw.sunflower.common.BaseBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.ibatis.type.JdbcType;

import java.io.Serializable;
import java.util.Date;

/**
* Created by pj on 2021/06/25
*/
@ApiModel("公告表")
@TableName("t_project_bid_notice")
@Data
@Accessors(chain = true)
public class ProjectBidNotice extends BaseBean implements Serializable{
    /**
     * 主键
     *
     * @mbg.generated
     */
    private Long id;

    /**
     * 项目ID
     *
     * @mbg.generated
     */
    private Long projectId;

    /**
     * 公告名称
     *
     * @mbg.generated
     */
    private String noticeName;

    /**
     * 公告内容
     *
     * @mbg.generated
     */
    private String noticeContent;

    /**
     * 公告拟发布时间
     *
     * @mbg.generated
     */
    @TableField(value = "Intended_Release_Time",updateStrategy = FieldStrategy.IGNORED,jdbcType = JdbcType.DATETIMEOFFSET)
    private Date intendedReleaseTime;

    /**
     * 公告结束时间
     *
     * @mbg.generated
     */
    @TableField(value = "end_time",updateStrategy = FieldStrategy.IGNORED,jdbcType = JdbcType.DATETIMEOFFSET)
    private Date endTime;

    /**
     * 公告状态 1：正式 2：暂存
     *
     * @mbg.generated
     */
    private Integer state;

    /**
     * 招标文件发售截止时间（用于补充公告，若为空，则表示另行通知）（也用于公示结束时间）
     *
     * @mbg.generated
     */
    private Date saleEndTime;

    /**
     * 递交响应文件截止时间（用于补充公告，若为空，则表示另行通知）
     *
     * @mbg.generated
     */
    private Date submitEndTime;

    /**
     * 公告进度 1：起草中 2：审核中 3：已撤回 4：已审核 5：已发布 6：已退回
     *
     * @mbg.generated
     */
    private Integer noticeProgress;

    /**
     * 公告关联包段编号，以逗号隔开
     *
     * @mbg.generated
     */
    @TableField(value = "package_number",updateStrategy = FieldStrategy.IGNORED,jdbcType = JdbcType.VARCHAR)
    private String packageNumber;

    /**
     * osskey
     *
     * @mbg.generated
     */
    private long noticeOssKey;

    private static final long serialVersionUID = 1L;


    /**
     * 招标公告阶段： 0：默认无阶段； 1：第一轮； 2：第二轮
     */
    private Integer bidRound;

    /**
     * 最近一次提交审核时间
     *
     * @mbg.generated
     */
    private Date submitTime;

    /**
     * 公告发布时间
     *
     * @mbg.generated
     */
    private Date publishTime;
}