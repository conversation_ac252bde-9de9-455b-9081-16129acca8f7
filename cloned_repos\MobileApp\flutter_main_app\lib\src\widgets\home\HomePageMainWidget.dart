///***
/// * 首页主要布局
/// */

import 'dart:async';
import "package:flutter/material.dart";

import 'package:flutter_main_app/serviceLocator.dart';
import 'package:flutter_main_app/src/constants/region.dart';
import 'package:flutter_main_app/src/constants/theme.dart';
import 'package:flutter_main_app/src/utils/ScreenUtils.dart';

import 'package:flutter_main_app/src/viewModels/home/<USER>';

import 'package:flutter_main_app/src/models/bulletin/BulletinListQueryModel.dart';
import 'package:flutter_main_app/src/models/bulletin/BulletinListItemModel.dart';

import 'package:flutter_main_app/src/components/DataListView.dart';
import 'package:flutter_main_app/src/components/CustomInfoCard.dart';
import 'package:flutter_main_app/src/components/CustomTab.dart';

import "package:flutter_main_app/src/widgets/home/<USER>/HomePageHeader.dart";
import "package:flutter_main_app/src/widgets/home/<USER>/HomePageTabWidget.dart";
import 'package:flutter_main_app/src/widgets/home/<USER>';

class HomePageMainWidget extends StatefulWidget {
  const HomePageMainWidget({Key key}) : super(key: key);

  @override
  _HomePageMainWidgetState createState() => _HomePageMainWidgetState();
}

class _HomePageMainWidgetState extends State<HomePageMainWidget> {

  String _selectedRegionText = '全省';

  bool _isShowRegionSelector = false;

  int _selectRegionIndex = 0;

  final ScrollController _nestedScrollController = ScrollController();

  final HomeIndexViewModel _model = serviceLocator<HomeIndexViewModel>();

  Future<List<BulletinListItemModel>> _initDataList;

  @override
  void initState() {
    super.initState();

    _model.params = BulletinListQueryModel(
      bidType: [],
      bulletinTypeName: [],
      classifyName: [],
      currentPage: '1',
      fundRange: [],
      fundSource: [],
      industryCode: [],
      keyWord: [],
      pageSize: 20,
      platformCode: [],
      regionCode: [],
      uid: '',
    );

    _initDataList = _model.fetchDataList();
  }

  @override
  void dispose() {
    _initDataList = null;

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: themeContentBackgroundColor,
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        children: <Widget>[
          // 自定义的AppBar
          Container(
            color: themeActiveColor,
            width: ScreenUtils.screenWidth,
            height: 107,
            child: Container(
              padding: EdgeInsets.fromLTRB(20, 44, 20, 10),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: <Widget>[
                  Container(
                    height: 40,
                    width: ScreenUtils.screenWidth - 40,
                    color: themeContentBackgroundColor,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: <Widget>[
                        /// * 地区选择器
                        ConstrainedBox(
                          constraints: BoxConstraints(
                            minWidth: 71, //宽度尽可能大
                            minHeight: 40.0 //最小高度为50像素
                          ),
                          child: InkWell(
                            child: Container(
                              padding: EdgeInsets.only(left: 10),
                              alignment: Alignment.center,
                              child: Row(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: <Widget>[
                                  Text(
                                    _selectedRegionText,
                                    style: TextStyle(
                                      fontSize: 14,
                                      color: themeTitleColor,
                                    ),
                                  ),
                                  Padding(
                                    padding: const EdgeInsets.only(left: 6),
                                    child: Icon(
                                      Icons.arrow_drop_down,
                                      size: 14,
                                    ),
                                  ),
                                  Padding(
                                    padding: const EdgeInsets.only(left: 8),
                                    child: Container(
                                      height: 19,
                                      width: themeBorderWidth,
                                      color: Color(0xFF999999),
                                    ),
                                  )
                                ],
                              ),
                            ),
                            onTap: () {
                              setState(() {
                                this._isShowRegionSelector = !this._isShowRegionSelector;
                              });
                            },
                          ),
                        ),
                        /// * 搜索框
                        InkWell(
                          onTap: () {
                            Navigator.pushNamed(context, SearchEntranceWidget.routeName);
                          },
                          child: Container(
                            height: 40,
                            width: ScreenUtils.screenWidth - 40 - 111,
                            child: Row(
                              children: <Widget>[
                                Padding(
                                  padding: EdgeInsets.fromLTRB(13, 0, 7, 0),
                                  child: Icon(
                                    Icons.search,
                                    size: 14,
                                    color: themeHintTextColor,
                                  ),
                                ),
                                Text(
                                  '请输入你想要查询的招标信息',
                                  style: TextStyle(
                                    color: themeHintTextColor,
                                    fontSize: 12,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
          // 遮罩和正文
          Expanded(
            child: Stack(
              children: <Widget>[
                /// * 黏性头部实现
                NestedScrollView(
                  controller: _nestedScrollController,
                  headerSliverBuilder: (BuildContext context, bool innerBoxIsScrolled) {
                    return <Widget>[
                      /// * 图标按钮列表 & 今日新增
                      SliverOverlapAbsorber(
                        handle: NestedScrollView.sliverOverlapAbsorberHandleFor(context),
                        child: SliverAppBar(
                          backgroundColor: themeContentBackgroundColor,
                          expandedHeight: 208.0,
                          flexibleSpace: FlexibleSpaceBar(
                            background: Container(
                              child: HomePageHeaderWidget(
                                callback: _queryParamsChangeCallback,
                              ),
                            ),
                          ),
                          primary: false,
                          forceElevated: innerBoxIsScrolled,
                        ),
                      ),
                      /// * 公告公示切换按钮列表
                      SliverPersistentHeader(
                        pinned: true,
                        delegate: _SliverAppBarDelegate(
                          HomePageTabWidget(
                            callback: _queryParamsChangeCallback,
                          ),
                        ),
                      ),
                    ];
                  },
                  body: NotificationListener<ScrollNotification>(
                    child: FutureBuilder(
                      future: _initDataList,
                      builder: (BuildContext context, AsyncSnapshot snapshot) {
                        switch (snapshot.connectionState) {
                          case ConnectionState.none:
                          case ConnectionState.active:
                          case ConnectionState.waiting:
                            return Center(
                              child: CircularProgressIndicator(),
                            );
                          case ConnectionState.done:
                            if (snapshot.hasError) {
                              return Text('Error: ${snapshot.error}');
                            }

                            return DataListView<BulletinListItemModel>(
                              initialDataItems: snapshot.data,
                              itemBuilder: (BuildContext context, int index, List<BulletinListItemModel> dataItems) {
                                final bulletin = dataItems[index];

                                return Container(
                                  color: themeContentBackgroundColor,
                                  child: CustomInfoCard(
                                    title: bulletin.bulletinName != null && bulletin.bulletinName.isNotEmpty
                                      ? bulletin.bulletinName
                                      : '---',
                                    tabs: [
                                      CustomTab(
                                        text: '${bulletin.regionName}',
                                        color: const Color(0xff3e78e6),
                                        borderColor: const Color(0xff3e78e6),
                                      ),
                                      CustomTab(
                                        text: '${bulletin.classifyName ?? "其他"}',
                                        color: const Color(0xfff9a545),
                                        borderColor: const Color(0xfff9a545),
                                      ),
                                    ],
                                    date: bulletin.noticeSendTime != null && bulletin.noticeSendTime.length >= 10
                                      ? bulletin.noticeSendTime.substring(0, 10)
                                      : null,
                                    isFirstChild: index == 0,
                                  ),
                                );
                              },
                              itemClickFunction: (_, __, ___) => _handleDataTileTaped,
                              refreshDataFunction: () => _handleDataListRefresh(),
                              loadingMoreDataFunction: () => _handleDataListLoadMore(),
                            );
                          default:
                            return Container();
                        }
                      },
                    ),
                    onNotification: (scrollNotification) {
                      return true;
                    },
                  ), 
                ),
                /// * 地区下拉框的遮罩
                Offstage(
                  offstage: !_isShowRegionSelector,
                  child: Opacity(
                    opacity: 0.5,
                    child: Container(color: Colors.black),
                  ),
                ),
                /// * 地区下拉框
                Offstage(
                  offstage: !_isShowRegionSelector,
                  child: Container(
                    width: ScreenUtils.screenWidth,
                    height: 440,
                    child: _buildRegionListView(),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  ///***
  /// * 生成 地区下拉框
  /// *
  /// * @return {Widget}
  /// */
  Widget _buildRegionListView() {
    final Widget regionListView = ListView.builder(
      padding: EdgeInsets.zero,
      shrinkWrap: false,
      itemExtent: null,
      physics: NeverScrollableScrollPhysics(),
      itemCount: jiangSuRegionList.length,
      itemBuilder: (BuildContext context, int index) {
        return GestureDetector(
          onTap: () {
            setState(() {
              _isShowRegionSelector = false;
              _selectedRegionText = jiangSuRegionList[index].name;
              _selectRegionIndex = index;
            });

            _model.setSpecifiedParam(
              'regionCode',
              jiangSuRegionList[index].code == '320000' ? [] : [jiangSuRegionList[index].code],
            );

            _queryParamsChangeCallback();
          },
          child: Container(
            alignment: Alignment.center,
            width: ScreenUtils.screenWidth,
            height: 39,
            color: index != this._selectRegionIndex
              ? themeContentBackgroundColor
              : themeBackgroundColor,
            child: Text(
              jiangSuRegionList[index].name,
              style: TextStyle(
                color: themeTextColor,
                fontSize: 14,
              ),
            ),
          ),
        );
      },
    );

    return regionListView;
  }

  ///***
  /// * 回调函数 - 当页面中的地区|classifyName|公告类型 等改变时，重建页面
  /// */
  void _queryParamsChangeCallback() {
    setState(() {
      _initDataList = _model.fetchDataList();
    });
  }

  ///***
  /// * 下拉刷新数据列表
  /// *
  /// * @return {Future<List<BulletinListItemModel>>}
  /// */
  Future<List<BulletinListItemModel>> _handleDataListRefresh() async {
    _model.clearDataList();
    _model.setSpecifiedParam('currentPage', '1');

    return await _model.fetchDataList();
  }

  ///***
  /// * 滚动到数据列表底部加载更多
  /// *
  /// * @return {Future<List<BulletinListItemModel>>}
  /// */
  Future<List<BulletinListItemModel>> _handleDataListLoadMore() async {
    _model.setSpecifiedParam(
      'currentPage',
      _model.dataList[_model.dataList.length - 1].currentPageCursor,
    );

    return await _model.fetchDataList();
  }

  ///***
  /// * 点击查看市场主体的信息
  /// *
  /// * @param {BuildContext} context
  /// * @param {int} index
  /// * @param {List<BulletinListItemModel>} dataList
  /// */
  void _handleDataTileTaped(
    BuildContext context,
    int index,
    List<BulletinListItemModel> dataList,
  ) {
    /// TODO: 公告公示 Tile 点击事件
    if (index % 3 == 0) {
      Navigator.pushNamed(context, 'TenderBulletinDetail');
    } else if (index % 3 == 1) {
      Navigator.pushNamed(context, 'WinCandidateBulletinDetail');
    } else if (index % 3 == 2) {
      Navigator.pushNamed(context, 'WinBidBulletinDetail');
    }
  }

}

///**
/// * 黏性头部固定部分
/// */
class _SliverAppBarDelegate extends SliverPersistentHeaderDelegate {
  _SliverAppBarDelegate(this.child);

  final PreferredSizeWidget child;

  @override
  double get minExtent => child.preferredSize.height;

  @override
  double get maxExtent => child.preferredSize.height;

  @override
  Widget build(
    BuildContext context,
    double shrinkOffset,
    bool overlapsContent,
  ) {
    return Container(
      child: child,
    );
  }

  @override
  bool shouldRebuild(_SliverAppBarDelegate oldDelegate) {
    return false;
  }
}
