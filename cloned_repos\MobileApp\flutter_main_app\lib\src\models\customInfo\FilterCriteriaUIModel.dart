///***
/// * 单个筛选条件 - UI - 包含的字段类型
/// */

import 'package:flutter/foundation.dart';

class FilterCriteriaUIModel {

  const FilterCriteriaUIModel({
    @required this.type,
    this.checkboxWidth = 240.0,
    @required this.fieldName,
    @required this.criteria,
  }) : assert(type != null),
    assert(checkboxWidth != null && checkboxWidth > 0.0),
    assert(fieldName != null),
    assert(criteria != null);

  final String type;

  final double checkboxWidth;

  final String fieldName;

  final List<String> criteria;

}
