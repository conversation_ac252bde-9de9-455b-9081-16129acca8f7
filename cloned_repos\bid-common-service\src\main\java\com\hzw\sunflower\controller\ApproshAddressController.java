package com.hzw.sunflower.controller;

import com.hzw.sunflower.common.BaseController;
import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.entity.ApproshAddress;
import com.hzw.sunflower.service.ApproshAddressService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "进场场地 服务")
@RestController
@RequestMapping("/approshaddress")
public class ApproshAddressController extends BaseController {
    @Resource
    private ApproshAddressService approshAddressService;

    @ApiOperation(value = "列表")
    @GetMapping("/list")
    public Result<List<ApproshAddress>> list() {
        List<ApproshAddress> list = approshAddressService.list();
        return Result.ok(list);
    }
}
