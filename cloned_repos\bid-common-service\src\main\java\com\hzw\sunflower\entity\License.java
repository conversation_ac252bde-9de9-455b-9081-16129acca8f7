package com.hzw.sunflower.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.hzw.sunflower.common.BaseBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @datetime 2023/02/20 10:53
 * @description: license实体
 * @version: 1.0
 */
@ApiModel(description = "系统license")
@TableName("s_license")
@Data
public class License extends BaseBean {

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "license信息")
    private String license;

    @ApiModelProperty(value = "macAddr信息")
    private String macAddr;


}
