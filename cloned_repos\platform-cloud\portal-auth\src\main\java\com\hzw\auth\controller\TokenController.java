package com.hzw.auth.controller;

import com.hzw.auth.form.*;
import com.hzw.auth.service.SysLoginService;
import com.hzw.common.core.constant.CacheConstants;
import com.hzw.common.core.constant.Constants;
import com.hzw.common.core.domain.R;
import com.hzw.common.core.web.controller.BaseController;
import com.hzw.common.idempotent.annotation.RepeatSubmit;
import com.hzw.common.log.annotation.Log;
import com.hzw.common.redis.utils.RedisUtils;
import com.hzw.system.api.domain.dto.SysUserDTO;
import com.hzw.system.api.model.RegisterUserReq;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotBlank;
import java.util.HashMap;
import java.util.Map;

/**
 * token 控制
 *
 * <AUTHOR>
 */
@Validated
@RequiredArgsConstructor
@RestController
@Slf4j
public class TokenController extends BaseController {

    private final SysLoginService sysLoginService;

    /**
     * 登录方法
     */
    @PostMapping("login")
    public R<Map<String, Object>> login(@Validated @RequestBody LoginBody form) {
        // 用户登录
        String accessToken = sysLoginService.login(form.getUsername(), form.getPassword());

        // 接口返回信息
        Map<String, Object> rspMap = new HashMap<>();
        rspMap.put(Constants.ACCESS_TOKEN, accessToken);
        return R.ok(rspMap);
    }

    /**
     * api接口
     * 登录方法
     */
    @PostMapping("/tokenLogin")
    public R<Map<String, Object>> tokenLogin(@Validated @RequestBody TokenLoginBody form) {
        Map<String, Object> ajax = new HashMap<>(8);
        // 生成令牌
        String token = sysLoginService.tokenLogin(form.getClientId(),form.getClientSecret());
        ajax.put(Constants.ACCESS_TOKEN, token);
        return R.ok(ajax);
    }
    /**
     * 短信登录
     *
     * @param smsLoginBody 登录信息
     * @return 结果
     */
    @PostMapping("/smsLogin")
    public R<Map<String, Object>> smsLogin(@Validated @RequestBody SmsLoginBody smsLoginBody) {
        Map<String, Object> ajax = new HashMap<>();
        // 生成令牌
        R r = sysLoginService.smsLogin(smsLoginBody.getPhonenumber(), smsLoginBody.getSmsCode());
        if (!Constants.SUCCESS.equals(r.getCode())){
            return r;
        }
        String token = Constants.FRONT_TOKEN + r.getData();
        ajax.put(Constants.ACCESS_TOKEN, token);
        return R.ok(ajax);
    }

    /**
     * 账号密码登录
     *
     * @param pwdLoginBody 登录信息
     * @return 结果
     */
    @PostMapping("/pwdLogin")
    public R<Map<String, Object>> pwdLogin(@Validated @RequestBody PwdLoginBody pwdLoginBody) {
        Map<String, Object> ajax = new HashMap<>();
        String prefixKey = CacheConstants.SUPPLIER_PWD_LOGIN_IMAGE_CODE_KEY;
        Boolean imageValidateResult = checkImageCode(prefixKey, pwdLoginBody.getTempToken(), pwdLoginBody.getCaptcha());
        if (!imageValidateResult){
            return R.fail("图片验证码不正确");
        }
        // 生成令牌
        R r = sysLoginService.pwdLogin(pwdLoginBody.getPhonenumber(), pwdLoginBody.getPassword());
        if (!Constants.SUCCESS.equals(r.getCode())){
            return r;
        }
        String token = Constants.FRONT_TOKEN + r.getData();
        ajax.put(Constants.ACCESS_TOKEN, token);
        return R.ok(ajax);
    }

    /**
     * 邮件登录
     *
     * @param body 登录信息
     * @return 结果
     */
    @PostMapping("/emailLogin")
    public R<Map<String, Object>> emailLogin(@Validated @RequestBody EmailLoginBody body) {
        Map<String, Object> ajax = new HashMap<>();
        // 生成令牌
        String token = sysLoginService.emailLogin(body.getEmail(), body.getEmailCode());
        ajax.put(Constants.ACCESS_TOKEN, token);
        return R.ok(ajax);
    }

    /**
     * 小程序登录(示例)
     *
     * @param xcxCode 小程序code
     * @return 结果
     */
    @PostMapping("/xcxLogin")
    public R<Map<String, Object>> xcxLogin(@NotBlank(message = "{xcx.code.not.blank}") String xcxCode) {
        Map<String, Object> ajax = new HashMap<>();
        // 生成令牌
        String token = sysLoginService.xcxLogin(xcxCode);
        ajax.put(Constants.ACCESS_TOKEN, token);
        return R.ok(ajax);
    }

    /**
     * 登出方法
     */
    @DeleteMapping("logout")
    public R<Void> logout() {
        sysLoginService.logout();
        return R.ok();
    }

    /**
     * 用户注册
     */
    @RepeatSubmit
    @PostMapping("register")
    public R<Void> register(@RequestBody RegisterBody registerBody) {
        // 用户注册
        return sysLoginService.register(registerBody);
    }

    /**
     * 通过*姓名、*手机号、*短信验证码、推荐码注册
     *
     */
//    @RepeatSubmit
//    @PostMapping("registerByPhoneNumber")
//    public R<Void> registerByPhoneNumber(@RequestBody RegisterBody registerBody) {
//        // 用户注册
//        return sysLoginService.registerByPhoneNumber(registerBody);
//    }


    /**
     * 登录
     *
     * @return
     */
    @PostMapping("wxSmallLogin")
    public R<Map<String, Object>> wxSmallLogin(@RequestBody LoginUserWechatReq userREQ) throws Exception{
        //参数check
        if (userREQ.getMobile() == null) {
            return R.fail("手机号/验证码不正确");
        }
        // 用户登录
        Map<String, Object> rspMap = null;
        try {
//            rspMap = sysLoginService.wxSmallLogin(userREQ);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return R.ok(rspMap);
    }

    /**
     * 绑定
     *
     * @return
     */
    @RepeatSubmit
    @PostMapping("/binding")
    public R<Map<String, Object>> binding(@RequestBody LoginUserWechatReq userREQ) throws Exception{
        //参数check
        if (userREQ.getMobile() == null) {
            return R.fail("手机号/验证码不正确");
        }
        if (userREQ.getSmsCode() == null) {
            return R.fail("手机号/验证码不正确");
        }
        // 用户绑定
        R<Map<String, Object>> rspMap = null;
        rspMap = sysLoginService.binding(userREQ);
        return rspMap;
    }

    /**
     * 注册
     *
     * @return
     */
    @RepeatSubmit
    @PostMapping("/registerWechat")
    public R<Map<String, Object>> register(@RequestBody SysUserDTO user) throws Exception{
        // 注册
        R<Map<String, Object>> rspMap = null;
//        rspMap = sysLoginService.register(user);
        return rspMap;

    }

    public Boolean checkImageCode(String prefixKey, String tempToken, String imageCode) {
        String key = prefixKey + tempToken;
        String vcode = RedisUtils.getCacheObject(key);
        if (null == imageCode || StringUtils.isBlank(vcode) || !imageCode.toLowerCase().equals(vcode)){
            log.error("请输入正确的验证码 => {}", tempToken);
            return false;
        } else {
            //校验成功清除缓存验证码
            RedisUtils.deleteObject(key);
            return true;
        }
    }

}
