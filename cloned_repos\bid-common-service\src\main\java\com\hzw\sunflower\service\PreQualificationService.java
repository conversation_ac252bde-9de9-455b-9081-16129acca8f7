package com.hzw.sunflower.service;

import com.hzw.sunflower.entity.ProjectBidSection;

public interface PreQualificationService {

    /**
     * 资格预审按项目 添加标段完善信息
     *
     * @param projectId
     * @param projectBidSection
     * @return
     */
    Boolean savePreQualificationInfo(Long projectId, ProjectBidSection projectBidSection);
    /**
     *
     *
     * @param projectId
     * @param sectionId
     * @return
     */
    Boolean deletePreQualificationInfo(Long projectId, Long sectionId);
}
