package com.hzw.sunflower.controller.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @datetime 2023/02/20 10:52
 * @description: license入参
 * @version: 1.0
 */
@Data
@ApiModel(description = "license入参")
public class LicenseReq {

    @ApiModelProperty(value = "license信息")
    private String license;


    @ApiModelProperty(value = "系统macAddr")
    private String macAddr;

}
