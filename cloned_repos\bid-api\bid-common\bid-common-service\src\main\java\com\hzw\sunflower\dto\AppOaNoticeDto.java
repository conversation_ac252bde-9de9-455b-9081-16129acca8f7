package com.hzw.sunflower.dto;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class AppOaNoticeDto implements Serializable {

    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "通知标题")
    private String title;

    @ApiModelProperty(value = "内容")
    private String content;


    @ApiModelProperty(value = "用户id集合")
    private List<Long> userIdList;
}
