package com.hzw.sunflower.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hzw.sunflower.common.BaseController;
import com.hzw.sunflower.util.BeanListUtil;
import com.hzw.sunflower.common.Paging;
import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.constant.MessageConstants;
import com.hzw.sunflower.dto.SealApplicationInfoDTO;
import com.hzw.sunflower.controller.request.SealApplicationInfoREQ;
import com.hzw.sunflower.entity.SealApplicationInfo;
import com.hzw.sunflower.entity.condition.SealApplicationInfoCondition;
import com.hzw.sunflower.service.SealApplicationInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
* SealApplicationInfoController
*/
@Api(tags = "SealApplicationInfo服务")
@RestController
@RequestMapping("/sealApplicationInfo")
public class SealApplicationInfoController extends BaseController {
    @Autowired
    private SealApplicationInfoService sealApplicationInfoService;

    @ApiOperation(value = "修改SealApplicationInfo信息")
    @ApiImplicitParam(name = "sealApplicationInfoDTO", value = "SealApplicationInfo表 ", required = true, dataType = "SealApplicationInfoDTO", paramType = "body")
    @PutMapping(value = "/update")
    public Result<Boolean> update(@RequestBody SealApplicationInfoDTO sealApplicationInfoDTO) {
        Long id = sealApplicationInfoDTO.getId();
        if (id == null) {
            return Result.failed(MessageConstants.UPDATE_NULL);
        }
        Boolean bool = sealApplicationInfoService.updateInfo(sealApplicationInfoDTO);
        return Result.okOrFailed(bool);
    }


}