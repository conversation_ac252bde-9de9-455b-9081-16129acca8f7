package com.hzw.sunflower.controller;

import com.hzw.sunflower.common.BaseController;
import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.common.annotation.RepeatSubmit;
import com.hzw.sunflower.entity.BondTemplate;
import com.hzw.sunflower.entity.BondWaterlabel;
import com.hzw.sunflower.service.BondWaterlabelService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @ClassName:BondWaterlabelController
 * @Auther: lijinxin
 * @Description: 保证金流水标签
 * @Date: 2023/5/10 16:57
 * @Version: v1.0
 */
@Api(tags = "保证金流水标签")
@RestController
@RequestMapping("/bondWaterLabel")
public class BondWaterlabelController extends BaseController {


    @Autowired
    private BondWaterlabelService bondWaterlabelService;

    /**
     * 收款保证金认领
     * @param bondWaterlabel
     * @return
     */
    @ApiOperation(value = "新增保证金流水标签")
    @ApiImplicitParam(name = "bondWaterlabel", value = "标签", required = true, dataType = "BondWaterlabel", paramType = "body")
    @PostMapping(value = "/add")
    @RepeatSubmit
    public Result<Object> add(@RequestBody BondWaterlabel bondWaterlabel) {
        return bondWaterlabelService.add(bondWaterlabel);
    }

}
