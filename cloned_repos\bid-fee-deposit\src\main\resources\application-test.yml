server:
  port: 28929
  servlet:
    context-path: /depositfee
  max-http-header-size: 100000
#datasource
spring:
  main:
    allow-circular-references: true
    allow-bean-definition-overriding: true
  application:
    name: bid-tts-api
  datasource:
    url: *********************************************************************************************************************************************************************************************************************
    username: deposit-fee
    password: deposit-fee
    driver-class-name: com.mysql.cj.jdbc.Driver

  jackson:
    # 全局设置@JsonFormat的格式pattern
    date-format: yyyy-MM-dd HH:mm:ss
    # 当地时区
    locale: zh
    # 设置全局时区
    time-zone: GMT+8
    # 常用，全局设置pojo或被@JsonInclude注解的属性的序列化方式
    #    default-property-inclusion: non_null #不为空的属性才会序列化,具体属性可看JsonInclude.Include
    # 常规默认,枚举类SerializationFeature中的枚举属性为key，值为boolean设置jackson序列化特性,具体key请看SerializationFeature源码
    serialization:
      write-dates-as-timestamps: false # 返回的java.util.date转换成timestamp
      FAIL_ON_EMPTY_BEANS: true # 对象为空时是否报错，默认true
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
    throw-exception-if-no-handler-found: true
  servlet:
    multipart:
      # 开启 multipart 上传功能
      enabled: true
      # 文件写入磁盘的阈值
      file-size-threshold: 2KB
      # 最大文件大小
      max-file-size: 200MB
      # 最大请求大小
      max-request-size: 215MB
  resources:
    add-mappings: true
  redis:
    # 地址
    host: **************
    # 端口，默认为6379
    port: 26379
    # 数据库索引
    database: 0
    # 密码
    password: TR0Y9s27Oda6oqKs
    # 连接超时时间
    timeout: 10s
    lettuce:
      pool:
        # 连接池中的最小空闲连接
        min-idle: 0
        # 连接池中的最大空闲连接
        max-idle: 8
        # 连接池的最大数据库连接数
        max-active: 8
        # #连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1ms

  rabbitmq:
    # 地址
    host: **************
    # 端口
    port: 25672
    # 账号
    username: hzw
    # 密码
    password: TR0Y9s27Oda6oqKs
    publisher-confirm-type: correlated
    publisher-returns: true
    listener:
      simple:
        retry:
          enabled: true
          max-attempts: 3
          initial-interval: 2000
        default-requeue-rejected: false

mybatis-plus:
  global-config:
    #数据库相关配置
    db-config:
      #主键类型  AUTO:"数据库ID自增", INPUT:"用户输入ID", ID_WORKER:"全局唯一ID (数字类型唯一ID)", UUID:"全局唯一ID UUID";
      id-type: AUTO
  configuration:
    map-underscore-to-camel-case: true
    typeAliasesPackage: com.hzw.depositfee.api.**.entity
    mapper-locations: mapper/**/*Mapper.xml
    config-location: mybatis/mybatis-config.xml
    #logic-delete-field: isDelete  # 全局逻辑删除的实体字段名(since 3.3.0,配置后可以忽略不配置步骤2)
    logic-delete-value: 1 # 逻辑已删除值(默认为 1)
    logic-not-delete-value: 0 # 逻辑未删除值(默认为 0)
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
sa-token:
  token-name: token

#log
logging:
  # 过滤开关
  enabled: true
  level:
    root: INFO
    com:
      hzw: trace
  file:
    path: /log

# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /system/notice/*
  # 匹配链接（多个用逗号分隔）
  urlPatterns: /company/*,/monitor/*,/tool/*







