<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <modelVersion>4.0.0</modelVersion>

    <groupId>com.hzw</groupId>
    <artifactId>portal-api-bom</artifactId>
    <packaging>pom</packaging>
    <version>1.0.0</version>

    <description>
        portal-api-bom api依赖项
    </description>

    <dependencyManagement>
        <dependencies>
            <!-- 系统接口 -->
            <dependency>
                <groupId>com.hzw</groupId>
                <artifactId>portal-api-system</artifactId>
                <version>1.0.0</version>
            </dependency>
            <dependency>
                <groupId>com.hzw</groupId>
                <artifactId>portal-api-bid</artifactId>
                <version>1.0.0</version>
            </dependency>
            <!-- 资源服务接口 -->
            <dependency>
                <groupId>com.hzw</groupId>
                <artifactId>portal-api-resource</artifactId>
                <version>1.0.0</version>
            </dependency>


            <dependency>
                <groupId>com.hzw</groupId>
                <artifactId>portal-api-externalApi</artifactId>
                <version>1.0.0</version>
            </dependency>

        </dependencies>
    </dependencyManagement>
    <distributionManagement>
        <!-- 定义releases库的坐标 -->
        <repository>
            <id>rdc-releases</id>
            <url>https://packages.aliyun.com/maven/repository/2260816-release-35QHzL/</url>
        </repository>
        <!-- 定义snapshots库 -->
        <snapshotRepository>
            <id>rdc-snapshots</id>
            <url>https://packages.aliyun.com/maven/repository/2260816-snapshot-GzzKEy/</url>
        </snapshotRepository>
    </distributionManagement>
</project>
