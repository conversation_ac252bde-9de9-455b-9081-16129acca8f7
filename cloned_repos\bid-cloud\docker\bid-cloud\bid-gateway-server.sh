#!/bin/sh
#cp TEMPLATE FILE
docker stop bid-gateway-server || true
docker rm bid-gateway-server || true
docker rmi registry.cn-hangzhou.aliyuncs.com/hzw-jtcc/jtcc:bid-gateway-server-1.0.0 || true
docker pull registry.cn-hangzhou.aliyuncs.com/hzw-jtcc/jtcc:bid-gateway-server-1.0.0
docker run -itd --net=host  --log-opt max-size=100m --log-opt max-file=3  --name bid-gateway-server -v /home/<USER>/plan1/bid-cloud/config/bid-gateway-server:/hzw/gateway/config -v /data/log:/hzw/gateway/logs registry.cn-hangzhou.aliyuncs.com/hzw-jtcc/jtcc:bid-gateway-server-1.0.0
