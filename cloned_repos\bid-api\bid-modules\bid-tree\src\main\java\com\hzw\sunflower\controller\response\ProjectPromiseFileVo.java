package com.hzw.sunflower.controller.response;

import com.hzw.sunflower.entity.ProjectPromiseFile;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @datetime 2022/11/04 9:24
 * @description: 承诺文件响应实体
 * @version: 1.0
 */
@ApiModel(description = "承诺文件响应实体")
@Data
public class ProjectPromiseFileVo extends ProjectPromiseFile {

    @ApiModelProperty(value = "签章文件id")
    private Long signFileId;

    @ApiModelProperty(value = "文件key")
    private String ossFileKey;

    @ApiModelProperty(value = "文件名称")
    private String ossFileName;

    @ApiModelProperty(value = "专家是否签署 1.是 2.否")
    private Integer hasSign;

    @ApiModelProperty(value = "采购项目编号")
    private String purchaseNumber;

}
