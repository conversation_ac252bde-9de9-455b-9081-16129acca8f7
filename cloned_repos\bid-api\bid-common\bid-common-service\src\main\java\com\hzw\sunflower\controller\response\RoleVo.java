package com.hzw.sunflower.controller.response;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 用户表 返回实体
 *
 * <AUTHOR>
 * @version 1.0.0 2021-04-12
 */
@ApiModel(description = "角色表 ")
@Data
public class RoleVo {
    @ApiModelProperty(value = "主键", position = 1)

    private Long id;

    @ApiModelProperty(value = "'角色名'", position = 2)
    private String roleName;

    @ApiModelProperty(value = "'角色代码'", position = 3)
    private String roleCode;


}