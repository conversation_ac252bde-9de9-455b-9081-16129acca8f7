package com.hzw.ssm.applets.dao;

import com.hzw.ssm.applets.entity.BidEvaluation;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021-02-03 10:49
 * @Version 1.0
 */
public interface BidEvaluationMapper {

    int insertBidEvaluation(BidEvaluation bidEvaluation);

    int updateBidEvaluation(BidEvaluation bidEvaluation);

    BidEvaluation getBidEvaluation(BidEvaluation bidEvaluation);

    int deleteBidEvaluation(BidEvaluation bidEvaluation);

    List<BidEvaluation> getBidEvaluations(BidEvaluation bidEvaluation);

    BidEvaluation queryBidEvaluation(BidEvaluation evaluation);

    int queryCountByExpertId(BidEvaluation bidEvaluation);

    int queryEvaluationNumById(@Param("expertId") String expertId);
}
