const { app, BrowserWindow, dialog, ipcMain } = require('electron');
const path = require('path');
const fs = require('fs');
const admZip = require('adm-zip');
const htmlToDocx = require('html-to-docx');
const mammoth = require('mammoth');
const puppeteer = require('puppeteer-core');

function createWindow() {
  const win = new BrowserWindow({
    // 设置窗口的宽高都是100%
    width: 1300,
    height: 800,
    webPreferences: {
      preload: path.join(__dirname, 'preload.js'),
      contextIsolation: true,
      enableRemoteModule: false,
      nodeIntegration: false,
    },
  });
   // 加载应用程序的 index.html。
   if (process.env.MAIN_WINDOW_VITE_DEV_SERVER_URL) {
    win.loadURL(process.env.MAIN_WINDOW_VITE_DEV_SERVER_URL)
    // 打开开发者工具
    win.webContents.openDevTools();
  } else {
    // win.loadFile('dist/index.html')
    win.loadFile(path.join(__dirname, `../renderer/main_window/index.html`));
  }
}

// 上传文件
ipcMain.handle('upload-file', async (event, filePath) => {
  try {
    // const fileData = await fs.promises.readFile(filePath);
    const html = await convertDocxToHtml(filePath);
    // console.log('Extracted Text:', html); // 你可以将提取的文本发送回渲染进程或进行其他处理
    return html;
  } catch (err) {
    console.error('Error reading file:', err);
    throw err;
  }
});

// 读取文件信息
ipcMain.handle('read-file', async (event, filePath) => {
  try {
    const fileData = await fs.promises.readFile(filePath);
    return fileData;
  } catch (err) {
    console.error('Error reading file:', err);
    throw err;
  }
});

// 文件选择对话框
ipcMain.handle('open-file-dialog', async (event) => {
  const result = await dialog.showOpenDialog({
    properties: ['openFile'],
    filters: [{ name: 'Word Files', extensions: ['zip','rar'] }],
  });
  return result.filePaths;
});

// 解压缩并读取文件
ipcMain.handle('unzip-file', async (event, filePath) => {
  try {
    const extractPath = path.join(app.getPath('temp'), 'extracted');
  // 清空extractPath文件夹里的文件
  fs.rmdirSync(extractPath, { recursive: true });
  const zip = new admZip(filePath)
  zip.extractAllTo(extractPath, true /* 是否覆盖 */);

  const files = fs.readdirSync(extractPath);
  
  return files.map(file => path.join(extractPath, file)); 
  } catch (error) {
    const extractPath = path.join(app.getPath('temp'), 'extracted');
    const zip = new admZip(filePath)
    zip.extractAllTo(extractPath, true /* 是否覆盖 */);
  
    const files = fs.readdirSync(extractPath);
    
    return files.map(file => path.join(extractPath, file)); 
  }
  
});

// 处理从渲染器发送的生成DOCX请求
ipcMain.handle('generate-docx', async (event, htmlContent) => {
  const fileBuffer = await htmlToDocx(htmlContent);
  dialog.showSaveDialog({
    title: 'Save DOCX',
    defaultPath: '投标文件.docx',
    filters: [{ name: 'Word Document', extensions: ['docx'] }]
  }).then(result => {
    if (!result.canceled) {
      fs.writeFile(result.filePath, fileBuffer, (err) => {
        if (err) {
          console.error('Failed to save the file:', err);
        } else {
          console.log('File saved successfully!');
        }
      });
    }
  });
});


// 将html转为PDF导出
ipcMain.handle('generate-pdf',async (event, htmlContent) => {
  dialog.showSaveDialog({
    title: 'Save PDF',
    defaultPath: path.join(__dirname, '投标文件.pdf'),
    filters: [{ name: 'Word Document', extensions: ['pdf'] }]
  }).then(async file => {
    if (!file.canceled) {
      try {
        // 判断当前系统是window还是mac
        let platformPath = ''
        if (process.platform === 'darwin') {
          platformPath = '/Applications/Google Chrome.app/Contents/MacOS/Google Chrome'
        } else if (process.platform === 'win32') {
          platformPath = 'C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe'
        } else {
          platformPath = 'C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe'
        }
        const browser = await puppeteer.launch({
          executablePath: platformPath  // 替换为你本地 Chrome 的路径
        });
        const page = await browser.newPage();
        await page.setContent(htmlContent);
        const pdfBuffer = await page.pdf({
          format: 'A4',
          margin: {
            top: '1cm',
            bottom: '1cm',
            left: '1cm',
            right: '1cm'
          },
        });
    
        fs.writeFile(file.filePath, pdfBuffer, (err) => {
          if (err) {
            console.log(err,'导出失败')
          } 
        });
      } catch (err) {
        console.error('Failed to generate PDF:', err);
        throw err;
      }
    }
  }).catch(err => {
    console.log(err);
  });
});

async function convertDocxToHtml(path) {
  try {
    const options = {
      styleMap: [
        "p[style-name='Heading 1'] => h1:fresh",
        "p[style-name='Heading 2'] => h2:fresh"
      ]
    };
    const result = await mammoth.convertToHtml({ path: path },options);
    const html = result.value;
    const processedHtml = addPageBreaks(html); // 添加自定义的分页处理
    return processedHtml
  } catch (err) {
    console.error('Error converting file:', err);
    throw err;
  }
}

function addPageBreaks(html) {
  // 在这里添加自定义的分页逻辑,全局替换
  // <w:sectPr><w:pgBr/></w:sectPr>
  return (html.replace(/<\/a>/g, '</div><w:sectPr><w:pgBr/></w:sectPr>')
    .replace(/<table>/g, '<table border width="100%" cellspacing="0" cellpadding="10">')
    .replace(/<td>/g, '<td style="padding:10px 20px">')
    .replace(/<td /g, '<td style="padding:10px 20px" ')
    .replace(/<h1>/g, '<h1 style="text-align:center">')
    .replace(/<h2>/g, '<h2 style="text-align:center">')
    .replace(/<a /g, '<div ')
  );
}


app.whenReady().then(createWindow);

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});
