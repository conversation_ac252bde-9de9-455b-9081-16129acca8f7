package com.hzw.sunflower.service.impl;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzw.sunflower.constant.constantenum.ProjectSerialTypeEnum;
import com.hzw.sunflower.dao.ProjectSerialNumMapper;
import com.hzw.sunflower.entity.ProjectSerialNum;
import com.hzw.sunflower.exception.ExceptionEnum;
import com.hzw.sunflower.exception.SunFlowerException;
import com.hzw.sunflower.service.ProjectSerialNumService;
import com.hzw.sunflower.util.LoginUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.Date;


/**
 * 项目流水表Service接口实现
 *
 * <AUTHOR>
 * @version 1.0.0 2021-04-13
 */
@Service
public class ProjectSerialNumServiceImpl extends ServiceImpl<ProjectSerialNumMapper, ProjectSerialNum> implements ProjectSerialNumService {


    /**
     * 生成采购项目编号
     * 生成规则：JSTCC2100510001
     * 租户简称+年份后两位如2021年则展示为21+三位处室编号005+一位省内省外标识（:省内，2:省外）+四位流水号0001
     *
     * @return
     */
    @Override
    public  String generateProjectNum(Integer provinceInOut, Long agentDepartId) {
            StringBuffer sb = new StringBuffer();
            //1.根据当前用户的单位查询租户4位数的简称
            String tenantName = getCompanyToTenant();
            sb.append(tenantName);
            //2.获取当前年份后2位
            String yearLastTwo = getCurrentYearLastTwo();
            sb.append(yearLastTwo);
            //3.获取处室编号
            String departmentNum = null;
            if (null == agentDepartId) {
                departmentNum = getCurrentDepartmentNum();
            } else {
                departmentNum = getCurrentDepartmentNum(agentDepartId);
            }
            sb.append(departmentNum);
            //4.加入省内省外标识，1:省内，2:省外
            sb.append(provinceInOut);
            //5.获取4位数的流水号
            String serialNumber = getSerialNumberNew(ProjectSerialTypeEnum.PROJECT.getValue());
            sb.append(serialNumber);
            return sb.toString();
    }

    /**
     * 获取存根编号
     * @return
     */
    @Override
    public String generateStubNum() {
        StringBuffer sb = new StringBuffer();
        Date date = DateUtil.date();
        //年份
        String year = Convert.toStr(DateUtil.year(date));
        sb.append(year);
        //3.获取处室编号
        String departmentNum = getCurrentDepartmentNum();
        sb.append(departmentNum);
        //4.获取5位数的流水号
        String serialNumber = getSerialNumber(ProjectSerialTypeEnum.STUB.getValue());
        sb.append(serialNumber);
        return  sb.toString();
    }



    /**
     * 获取存根编号
     * @return
     */
    @Override
    public String generateStubNum(Long userId,String purchaseNumber) {
        StringBuffer sb = new StringBuffer();
        Date date = DateUtil.date();
        //年份
        String year = Convert.toStr(DateUtil.year(date));
        sb.append(year);
        //3.获取处室编号
        String departmentNum = purchaseNumber.substring(7, 10); // 092-根据项目编号中的部门编码生成存根编号
        sb.append(departmentNum);
        //4.获取5位数的流水号
        String serialNumber = getSerialNumber(ProjectSerialTypeEnum.STUB.getValue());
        sb.append(serialNumber);
        return  sb.toString();
    }

    /**
     *国际标编号
     *国际标项目编号生成规则：年份后两位（22）+部门编号（比如021）+四位自增数字。示例：220210001
     * @param userId
     * @return
     */
    @Override
    public String getInternationalNumber(Long userId,Integer internationalCode) {
        StringBuffer sb = new StringBuffer();
        SimpleDateFormat sdf = new SimpleDateFormat("yy");
        String yy = sdf.format(new Date());
        sb.append(yy);
        //3.获取处室编号
        //String departmentNum = getCurrentDepartmentNum(userId);
        String departmentNum = getCurrentDepartmentNum();
        sb.append(departmentNum);
        //4.获取4位数的流水号
        /**
         * 流水号格式
         */
        String FORMAT_CODE = "0000";
        DecimalFormat dft = new DecimalFormat(FORMAT_CODE);
        String codes = dft.format(internationalCode);
        sb.append(codes);
        return  sb.toString();
    }

    /**
     * 国际标编号(根据部门id生产)
     * @param agentDepartId
     * @param internationalCode
     * @return
     */
    @Override
    public String getInternationalNumberByDeptId(Long agentDepartId, Integer internationalCode) {
        StringBuffer sb = new StringBuffer();
        SimpleDateFormat sdf = new SimpleDateFormat("yy");
        String yy = sdf.format(new Date());
        sb.append(yy);
        //3.获取处室编号
        //String departmentNum = getCurrentDepartmentNum(userId);
        String departmentNum = getCurrentDepartmentNum(agentDepartId);
        sb.append(departmentNum);
        //4.获取4位数的流水号
        /**
         * 流水号格式
         */
        String FORMAT_CODE = "0000";
        DecimalFormat dft = new DecimalFormat(FORMAT_CODE);
        String codes = dft.format(internationalCode);
        sb.append(codes);
        return  sb.toString();
    }

    /**
     * 自采获取存根编号
     * @return
     */
    @Override
    public String generateStubNumMyPurchase(Long deptId) {
        StringBuffer sb = new StringBuffer();
        Date date = DateUtil.date();
        //年份
        String year = Convert.toStr(DateUtil.year(date));
        sb.append(year);
        //3.获取处室编号
        String departmentNum = getCurrentDepartmentNum(deptId);
        sb.append(departmentNum);
        //4.获取5位数的流水号
        String serialNumber = getSerialNumber(ProjectSerialTypeEnum.STUB.getValue());
        sb.append(serialNumber);
        return  sb.toString();
    }

    /**
     * 自采项目生成采购项目编号
     * @param systemAbbreviation
     * @return
     */
    @Override
    public String generateProjectNumMyPurchase(String systemAbbreviation) {
        StringBuffer sb = new StringBuffer();
        //1.租户简称
        String tenantName = null;
        if (StringUtils.isNotEmpty(systemAbbreviation)) {
            tenantName = systemAbbreviation;
        } else {
            tenantName = getCompanyToTenant();
        }
        sb.append(tenantName);
        //2.获取当前年份后2位
        String yearLastTwo = getCurrentYearLastTwo();
        sb.append(yearLastTwo);
        //3.获取处室编号
        String departmentNum = getCurrentDepartmentNum();
        sb.append(departmentNum);
        //4.获取5位数的流水号
        String serialNumber = getSerialNumber(ProjectSerialTypeEnum.PROJECT.getValue());
        sb.append(serialNumber);
        return sb.toString();
    }

    /**
     * 获取4位数流水号，每一个新年重新开始
     * 新规则
     * @return
     */
    public   String getSerialNumberNew(Integer type) {
        //synchronized(getSerialNumberNewLock){
            Date date = DateUtil.date();
            String year = Convert.toStr(DateUtil.year(date));
   /*             //执行查询取最大的值
                Integer code = baseMapper.getYearMaxSerialNum(year, type);

                if (BeanUtil.isEmpty(code)) {
                    //空默认为1
                    code = 1;
                } else {
                    //有值时为当前code根据库里最大值加一
                    code++;
                }
                //执行新增操作
                ProjectSerialNum num = new ProjectSerialNum();
                num.setSerialNumber(Convert.convert(Long.class, code));
                num.setYear(year);
                num.setSerialType(type);
                this.save(num);*/
                ProjectSerialNum num = new ProjectSerialNum();
                num.setYear(year);
                num.setSerialType(type);
                Integer flag = baseMapper.insetMaxNumByYearType(num);
                if(flag < 1 && num.getId() == null ){
                    throw new SunFlowerException(ExceptionEnum.PROJECT_NUM_WRONG,ExceptionEnum.PROJECT_NUM_WRONG.getMessage());
                }
                ProjectSerialNum projectSerialNum = baseMapper.selectById(num.getId());
                Integer code = projectSerialNum.getSerialNumber().intValue();
        /**
                 * 流水号格式
                 */
                String FORMAT_CODE = "0000";
                DecimalFormat dft = new DecimalFormat(FORMAT_CODE);
                String codes = dft.format(code);

                return codes;
        //}
    }

    /**
     * 获取5位数流水号，每一个新年重新开始
     *
     * @return
     */
    public  String getSerialNumber(Integer type) {// 加锁，确保每次只有一个线程执行
            Date date = DateUtil.date();
            String year = Convert.toStr(DateUtil.year(date));
/*            //执行查询取最大的值
            Integer code = baseMapper.getYearMaxSerialNum(year, type);

            if (BeanUtil.isEmpty(code)) {
                //空默认为1
                code = 1;
            } else {
                //有值时为当前code根据库里最大值加一
                code++;
            }
        //执行新增操作
        ProjectSerialNum num = new ProjectSerialNum();
        num.setSerialNumber(Convert.convert(Long.class, code));
        num.setYear(year);
        num.setSerialType(type);
        this.save(num);*/
        ProjectSerialNum num = new ProjectSerialNum();
        num.setYear(year);
        num.setSerialType(type);
        Integer flag = baseMapper.insetMaxNumByYearType(num);
        if(flag < 1 && num.getId() == null ){
            throw new SunFlowerException(ExceptionEnum.NUM_WRONG,ExceptionEnum.NUM_WRONG.getMessage());
        }
        ProjectSerialNum projectSerialNum = baseMapper.selectById(num.getId());
        Integer code = projectSerialNum.getSerialNumber().intValue();
        /**
         * 流水号格式
         */
        String FORMAT_CODE = "00000";
        DecimalFormat dft = new DecimalFormat(FORMAT_CODE);
        String codes = dft.format(code);
        return codes;
    }

    /**
     * 获取3位数流水号，每一个新年重新开始
     *
     * @return
     */
    public  String getSerialNumberThree(Integer type) {
            Date date = DateUtil.date();
            String year = Convert.toStr(DateUtil.year(date));
            //执行查询取最大的值
/*            Integer code = baseMapper.getYearMaxSerialNum(year, type);

            if (BeanUtil.isEmpty(code)) {
                //空默认为1
                code = 1;
            } else {
                //有值时为当前code根据库里最大值加一
                code++;
            }
        //执行新增操作
        ProjectSerialNum num = new ProjectSerialNum();
        num.setSerialNumber(Convert.convert(Long.class, code));
        num.setYear(year);
        num.setSerialType(type);
        this.save(num);*/
        ProjectSerialNum num = new ProjectSerialNum();
        num.setYear(year);
        num.setSerialType(type);
        Integer flag = baseMapper.insetMaxNumByYearType(num);
        if(flag < 1 && num.getId() == null ){
            throw new SunFlowerException(ExceptionEnum.NUM_WRONG,ExceptionEnum.NUM_WRONG.getMessage());
        }
        ProjectSerialNum projectSerialNum = baseMapper.selectById(num.getId());
        Integer code = projectSerialNum.getSerialNumber().intValue();
        /**
         * 流水号格式
         */
        String FORMAT_CODE = "000";
        DecimalFormat dft = new DecimalFormat(FORMAT_CODE);
        String codes = dft.format(code);
        return codes;
    }

    /**
     * 获取当前用户处室编号
     *
     * @return
     */
    private String getCurrentDepartmentNum() {
        Long id=  LoginUtil.getJwtUser().getUser().getDepartId();
        //String departCode = baseMapper.selectDepartCodeById(id);
        String departCode = baseMapper.selectDepartProjectCodeById(id);
        return departCode;
    }

    /**
     * 获取当前用户处室编号
     *
     * @return
     */
    private String getCurrentDepartmentNum(Long deptId) {
//        String departCode = baseMapper.selectDepartCodeByUserId(userId);
//        String departCode = baseMapper.selectDepartCodeById(deptId);
        String departCode = baseMapper.selectDepartProjectCodeById(deptId);
        return departCode;
    }

    /**
     * 截取当前年份后2位
     *
     * @return
     */
    public String getCurrentYearLastTwo() {
        Date date = DateUtil.date();
        String year = Convert.toStr(DateUtil.year(date));
        /**
         * 年份截取的起始位置
         */
        int YEARTWO = 2;
        //开始截取
        return StrUtil.sub(year, YEARTWO, year.length());
    }


    /**
     * 查询租户简称
     *
     * @return
     */
    private String getCompanyToTenant() {

        return "JSTCC";
    }

    @Override
    public String generateNoAgentProjectNum(Long projectManagerDepartId) {
        StringBuffer sb = new StringBuffer();
        //1.租户简称
        String tenantName = getCompanyToTenant();
        sb.append(tenantName);
        //2.获取当前年份后2位
        String yearLastTwo = getCurrentYearLastTwo();
        sb.append(yearLastTwo);
        //3.获取处室编号
        String departmentNum = getCurrentDepartmentNum();
        sb.append(departmentNum);
        //4.添加非招标代理项目标识符3
        sb.append(3);
        //5.获取3位数的流水号
        String serialNumber = getSerialNumberThree(ProjectSerialTypeEnum.NO_AGENT_PROJECT_NUM.getValue());
        sb.append(serialNumber);
        return sb.toString();
    }

    @Override
    public Long generateNoAgentProjectId() {
        int key = 3;
        String binary = Integer.toBinaryString(key);
        StringBuffer top2 = new StringBuffer();
        StringBuffer bot2 = new StringBuffer();
        StringBuffer idString = new StringBuffer();
        top2.append(binary.substring(0, 2));
        bot2.append(binary.substring(2));
        idString.append(bot2).append(top2).append(idString).append(idString);
        String seq = getSerialNumberThree(ProjectSerialTypeEnum.NO_AGENT_PROJECT_ID.getValue());
        idString.append(seq);
        return Long.valueOf(idString.toString());
    }
}