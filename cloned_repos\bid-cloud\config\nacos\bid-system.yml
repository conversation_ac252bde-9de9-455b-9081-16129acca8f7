spring:
  datasource:
    dynamic:
      # 设置默认的数据源或者数据源组,默认值即为 master
      primary: master
      datasource:
        # 主库数据源
        master:
          type: ${spring.datasource.type}
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: ${datasource.system-master.url}
          username: ${datasource.system-master.username}
          password: ${datasource.system-master.password}
#        oracle:
#          type: ${spring.datasource.type}
#          driverClassName: oracle.jdbc.OracleDriver
#          url: ${datasource.system-oracle.url}
#          username: ${datasource.system-oracle.username}
#          password: ${datasource.system-oracle.password}
#          hikari:
#            connectionTestQuery: SELECT 1 FROM DUAL
#        postgres:
#          type: ${spring.datasource.type}
#          driverClassName: org.postgresql.Driver
#          url: ${datasource.system-postgres.url}
#          username: ${datasource.system-postgres.username}
#          password: ${datasource.system-postgres.password}

#实名认证 聚合
juhe:
  appcode: 聚合手机三要素SHA256
  apiUri: http://apis.juhe.cn/telecom_sha256/query
  validate: true
  key: 2c08521189e72833f7fc863e020a91e2
  telecom2:
    appcode: 姓名手机号二要素验证SHA256版
    apiUri: http://apis.juhe.cn/telecom2_sha256/query
    key: 46a5002697fcf424a57973e33840ff36
  nameIdCard:
    appkey: 11e58d6651a8c16aff602b207fe7a93e
    queryUrl: http://op.juhe.cn/idcard/query?key=
    queryEncryUrl: http://op.juhe.cn/idcard/queryEncry?key=