package com.hzw.sunflower.constant.constantenum;

/**
 * <AUTHOR>
 * @date ：Created in 2022/10/18 9:23
 * @description：银行借贷类型标志
 * @version: 1.0
 */
public enum BankLoanFlagEnum {

    BORROW("0", "借"),
    LOAN("1", "贷");

    private String code;
    private String desc;

    BankLoanFlagEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
