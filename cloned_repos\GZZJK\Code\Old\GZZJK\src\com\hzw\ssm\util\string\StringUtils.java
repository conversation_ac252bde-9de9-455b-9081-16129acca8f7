/*
 * 公司名称：江苏华招网
 * 版权信息：江苏华招网版权
 * 描述：字符时间等工具类
 * 文件名称：StringUtils.java
 * 修改时间：2016年4月15日
 * 修改人：ypp
 * 修改内容：
 * 跟踪单号：
 * 修改单号 ：
 */
 	
package com.hzw.ssm.util.string;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.Random;
import java.util.UUID;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.apache.log4j.Logger;

/**
 * 时间、字符串、数字处理 util
 * <AUTHOR>
 * @version V1.0[产品/模块版本]
 */
public class StringUtils{
    
    protected Logger log = Logger.getLogger(StringUtils.class);
    
    /** 最大三位的流水号 */
    private static int serial = 0;
    /** 日期生成字符串格式 */
    private static SimpleDateFormat dfDate = new SimpleDateFormat("yyyyMMddHHmmssSSS"); // yyyy-MM-dd HH:mm:ss.SSS
    /** 三位流水号生成字符串格式 */
    private static DecimalFormat dfSerialNumThree = new DecimalFormat("000");
    
    /**
     * 函数功能描述：检查字符串是否为空或者null
     * @param str
     * @return
     */
    public static boolean isEmpty(String str){
        return str == null || "".equals(str);
    }


    /**
     * 函数功能描述：检查字符串是否不为空
     * @param str
     * @return
     */
    public static boolean isNotEmpty(String str){
        return str != null || !"".equals(str);
    }


    /**
     * 函数功能描述：检查字符串是否不为空
     * @param str
     * @return
     */
    public static String toStringNull(String str){
        if(str == null || "".equals(str)){
            return "无";
        }else {
            return str;
        }
    }
    
    /**
     * 函数功能描述：检查字符串是否为空或者null，并检查trim后是否为空或者null
     * @param str
     * @return
     */
    public static boolean isEmptyAfterTrim(String str){
        boolean flag = isEmpty(str);
        if(flag){
            return flag;
        }
        return "".equals(str.trim());
    }
    
    /**
     * 验证是不是数字
     * @param str
     * @return
     */
    public static boolean isNum(String val) {
        if (null == val || val.isEmpty() || "-".equals(val) || "+".equals(val))
        {
            return false;
        }
        return val.matches("^[-+]?(([0-9]+)([.]([0-9]+))?|([.]([0-9]+))?)$");
    }
    
    /**
     * 去除字符串中的空格、回车、换行符、制表符
     * \n 回车(\u000a) 
     * \t 水平制表符(\u0009) 
     * \s 空格(\u0008) 
     * \r 换行(\u000d)
     * @param str
     * @return
     */
    public static String replaceBlank(String str) {
        String dest = "";
        if (str!=null) {
            Pattern p = Pattern.compile("\\s*|\t|\r|\n");
            Matcher m = p.matcher(str);
            dest = m.replaceAll("");
        }
        return dest;
    }
    
    /**
     * 
     * 函数功能描述：bigDecimal数据类型进行相加
     * @param bd1
     * @param bd2
     * @return
     */
    public static BigDecimal addBigDecimal(BigDecimal bd1, BigDecimal bd2){
        if(bd1 == null && bd2 == null){
            return null;
        }
        if(bd1 != null && bd2 == null){
            return bd1;
        }
        if(bd1 == null && bd2 != null){
            return bd2;
        }
        return bd1.add(bd2);
    }
    
    
    /**
     * 函数功能描述：使用17位日期时间加3位流水号创建一个20位的主键值
     * @return 20位主键值
     */
    public synchronized static String getKey() {

        StringBuffer ret = new StringBuffer(20);
        dfDate.setLenient(false);
        ret.append(dfDate.format(new java.util.Date()));

        ret.append(dfSerialNumThree.format(serial++));
        if (serial >= 1000)
            serial = 0;

        return ret.toString();
    }
    
    /**
     * 函数功能描述：GUID是一个128位长的数字，一般用16进制表示。
     * 算法的核心思想是结合机器的网卡、当地时间、一个随机数来生成GUID。
     * 从理论上讲，如果一台机器每秒产生10000000个GUID，则可以保证（概率意义上）3240年不重复。 
     * @return
     */
    public static String getUUID()
    {
        UUID uuId = UUID.randomUUID();
        return uuId.toString();
    }
    
    /**
     * 
     * 函数功能描述：处理BigDecimal对象
     * @param bd
     * @return
     */
    public static String processBigDecimal(BigDecimal bd)
    {
        if(bd == null){
            return "";
        }
        return bd.toString();
    }
    
    /**
     * 
     * 函数功能描述：转换为Long类型
     * @param object
     * @return
     */
    public static Long parseLong(Object object)
    {
        // 判断是否为空
        if (null == object)
        {
            return 0L;
        }
        try
        {
            return Long.valueOf(String.valueOf(object));
        }
        catch (Exception e)
        {
            // 转换失败后返回OL
            return 0L;
        }
    }
    
    /**
     * 
     * 函数功能描述：将对象转换成数值
     * @param val 对象
     * @param def 默认值
     * @return 数值
     */
    public static int changeStringToInt(Object val, int def) 
    {
        int ret = def;
        try 
        {
            if (val != null && !val.equals("")) 
            {
                ret = Integer.valueOf(String.valueOf(val).trim()).intValue();
            }
        } 
        catch (Exception e) 
        {
            e.printStackTrace();
            ret = def;
        }
        return ret;
    }
    
    /**
	 * 按字节截取字符串
	 * 
	 * @param str
	 * @param len
	 * @return
	 */
	public static String subStringByByte(String str, int len) {
		String result = null;
		if (str != null) {
			byte[] a = str.getBytes();
			if (a.length <= len) {
				result = str;
			} else if (len > 0) {
				result = new String(a, 0, len);
				int length = result.length();
				if (str.charAt(length - 1) != result.charAt(length - 1)) {
					if (length < 2) {
						result = null;
					} else {
						result = result.substring(0, length - 1);
					}
				}
				result+="...";
			}
		}
		return result;
	}
	
	/**
	 * html过滤标签
	 * 
	 * @param inputString
	 * @return
	 */
	public static String Html2Text(String inputString) {
		String htmlStr = inputString; // 含html标签的字符串
		String textStr = "";
		java.util.regex.Pattern p_script;
		java.util.regex.Matcher m_script;
		java.util.regex.Pattern p_style;
		java.util.regex.Matcher m_style;
		java.util.regex.Pattern p_html;
		java.util.regex.Matcher m_html;

		java.util.regex.Pattern p_content;
		java.util.regex.Matcher m_content;
		java.util.regex.Pattern p_flag;
		java.util.regex.Matcher m_flag;

		try {
			String regEx_script = "<[\\s]*?script[^>]*?>[\\s\\S]*<[\\s]*?\\/[\\s]*?script[\\s]*?>|<[\\s]*?script[^>]*?>[\\s\\S]*"; // 定义script的正则表达式{或<script[^>]*?>[//s//S]*?<///script>
			String regEx_style = "<[\\s]*?style[^>]*?>[\\s\\S]*?<[\\s]*?\\/[\\s]*?style[\\s]*?>"; // 定义style的正则表达式{或<style[^>]*?>[//s//S]*?<///style>
			String regEx_html = "<[^>]+>"; // 定义HTML标签的正则表达式
			String regEx_content = "</adress>|</caption>|</dd>|</dt>|</dl>|</div>|</form>|</h1>|</h2>|</h3>|</h4>|</h5>|</h6>|</h7>|</ol>|</li>|</ul>|</p>|</table>|</td>|</tr>|</th>|</br>|<br>|<BR>+";
			String regEx_flag = "&nbsp;|&rdquo;|&ldquo;|&mdash;|&ensp;|&emsp;|&emsp;|&gt;|&amp;|&quot;|&copy;|&reg;|&times;|&divide;+";
			p_script = Pattern.compile(regEx_script, Pattern.CASE_INSENSITIVE);
			m_script = p_script.matcher(htmlStr);
			htmlStr = m_script.replaceAll(""); // 过滤script标签

			p_style = Pattern.compile(regEx_style, Pattern.CASE_INSENSITIVE);
			m_style = p_style.matcher(htmlStr);
			htmlStr = m_style.replaceAll(""); // 过滤style标签

			p_flag = Pattern.compile(regEx_flag, Pattern.CASE_INSENSITIVE);
			m_flag = p_flag.matcher(htmlStr);
			htmlStr = m_flag.replaceAll(" "); // 过滤特殊标记

			p_content = Pattern
					.compile(regEx_content, Pattern.CASE_INSENSITIVE);
			m_content = p_content.matcher(htmlStr);
			htmlStr = m_content.replaceAll("\n"); // 过滤特殊标记

			p_html = Pattern.compile(regEx_html, Pattern.CASE_INSENSITIVE);
			m_html = p_html.matcher(htmlStr);
			htmlStr = m_html.replaceAll(""); // 过滤html标签

			textStr = htmlStr;

		} catch (Exception e) {
			System.err.println("Html2Text: " + e.getMessage());
		}
		return textStr;// 返回文本字符串
	}
	
    /**
     * java生成随机数字和字母组合
     * @param length[生成随机数的长度]
     * @return
     */
    public static String getCharAndNumr(int length)
    {
        String val = "";
        Random random = new Random();
        for (int i = 0; i < length; i++)
        {
            // 输出字母还是数字
            String charOrNum = random.nextInt(2) % 2 == 0 ? "char" : "num";
            // 字符串
            if ("char".equalsIgnoreCase(charOrNum))
            {
                // 取得大写字母还是小写字母
                int choice = random.nextInt(2) % 2 == 0 ? 65 : 97;
                val += (char) (choice + random.nextInt(26));
            }
            else if ("num".equalsIgnoreCase(charOrNum))
            { 
                // 数字
                val += String.valueOf(random.nextInt(10));
            }
        }
        return val;
    }

    /**
     * 函数功能描述：替换内容
     * @param content
     * @param target
     * @param replacement
     * @return
     */
    public static String replace(String content, String target, String replacement) {
        if (content == null) {
            content = "";
        }
        if (null == replacement) {
        	replacement = "";
        }
        
        return content.replace(target, replacement);
    }
}
