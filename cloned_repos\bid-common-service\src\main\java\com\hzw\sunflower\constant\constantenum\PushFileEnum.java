package com.hzw.sunflower.constant.constantenum;

public enum PushFileEnum {

    // 业务类型 1.标书费 2.保证金
    BID_FEE(1, "标书费"),
    BOND_FEE(2, "保证金"),

    // 标书费文件：10.标书费蓝票 11.标书费红票 12.标书费换票申请单 13.标书费退款申请单 14.发票信息统计表
    FILE_TYPE_10(10, "标书费蓝票"),
    FILE_TYPE_11(11, "标书费红票"),
    FILE_TYPE_12(12, "标书费换票申请单"),
    FILE_TYPE_13(13, "标书费退款申请单"),
    FILE_TYPE_14(14, "发票信息统计表"),
    // 保证金文件：20.保证金银行回单 21.保证金退款申请单 22.保证金中标通知书存根 23.保证金代理费发票
    FILE_TYPE_20(20, "保证金银行回单"),
    FILE_TYPE_21(21, "保证金退款申请单"),
    FILE_TYPE_22(22, "保证金中标通知书存根"),
    FILE_TYPE_23(23, "保证金代理费发票"),

    // 推送状态
    PUSH_STATUS_1(1, "待推送"),
    PUSH_STATUS_2(2, "成功"),
    PUSH_STATUS_3(3, "失败"),

    // 费用类型  1：标书费  2：平台服务费
    BID_FEE_1(1, "标书费"),
    BID_FEE_2(2, "平台服务费"),
    ;

    private Integer type;
    private String desc;

    PushFileEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
