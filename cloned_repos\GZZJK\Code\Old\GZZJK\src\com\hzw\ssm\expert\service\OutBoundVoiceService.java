package com.hzw.ssm.expert.service;

import java.util.List;

import net.sf.json.JSONObject;

import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.HttpClients;
import org.springframework.stereotype.Service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.hzw.ssm.expert.entity.OutboundVoice;
import com.hzw.ssm.expert.entity.OutboundVoiceOut;
import com.hzw.ssm.expert.entity.OutboundVoiceResult;
import com.hzw.ssm.fw.base.BaseService;

/**
 * 专家外呼短信服务
 * <AUTHOR>
 *
 */
@Service
public class OutBoundVoiceService extends BaseService {

	/**
	 * 启呼接口
	 * @return
	 */
    public OutboundVoiceOut outbBoundVoice(List<OutboundVoice> outboundVoice) {
		OutboundVoiceOut out = null;
		try {
			
			// 创建HttpClient实例
			HttpClient client = HttpClients.createDefault();
			
			// 请求地址
			//HttpPost httpPost = new HttpPost("http://202.102.40.184:21191/telecom-api/call/cServ");
			HttpPost httpPost = new HttpPost("http://202.102.40.184:21191/telecom-api/call/cServTts");
			

			// 封装请求参数 JSON格式
			JSONObject jsonParam = JSONObject.fromObject(outboundVoice);
			StringEntity entity = new StringEntity(jsonParam.toString(), "utf-8");// 解决中文乱码问题
			entity.setContentEncoding("UTF-8");
			entity.setContentType("application/json");
			httpPost.setEntity(entity);

			//发送请求
			HttpResponse response = client.execute(httpPost);
			HttpEntity httpEntity = response.getEntity();
			ObjectMapper mapper = new ObjectMapper();
			out = mapper.readValue(httpEntity.getContent(), OutboundVoiceOut.class);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return out;
	}
	
	/**
	 * 外呼结果查询接口
	 * @return
	 */
    public OutboundVoiceResult queryObvResult(OutboundVoice outboundVoice) {
		OutboundVoiceResult out = null;
		try {
			
			// 创建HttpClient实例
			HttpClient client = HttpClients.createDefault();
			
			// 请求地址
			HttpPost httpPost = new HttpPost("http://202.102.40.184:21191/telecom-api/call/cQueryResult");

			// 封装请求参数 JSON格式
			JSONObject jsonParam = JSONObject.fromObject(outboundVoice);
			StringEntity entity = new StringEntity(jsonParam.toString(), "utf-8");// 解决中文乱码问题
			entity.setContentEncoding("UTF-8");
			entity.setContentType("application/json");
			httpPost.setEntity(entity);

			//发送请求
			HttpResponse response = client.execute(httpPost);
			HttpEntity httpEntity = response.getEntity();
			ObjectMapper mapper = new ObjectMapper();
			out = mapper.readValue(httpEntity.getContent(), OutboundVoiceResult.class);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return out;
	}
	
}
