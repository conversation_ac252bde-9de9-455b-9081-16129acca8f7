package com.hzw.sunflower.controller.response;

import com.hzw.sunflower.dto.PackageInfoDTO;
import com.hzw.sunflower.dto.SimpleBidNoticeDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 招标公告列表页返回实体
 *
 * <AUTHOR> 2021/07/05
 * @version 1.0.0
 */

@ApiModel(description = "公告列表页响应")
@Data
@Accessors(chain = true)
public class ProjectBidNoticeListVO {

    @ApiModelProperty(value = "是否可以创建新公告", position = 1)
    private Boolean canCreate;

    @ApiModelProperty(value = "项目总包段数量", position = 2)
    private Integer totalPackageNumber;

    @ApiModelProperty(value = "符合条件的公告总数", position = 3)
    private Long totalNoticeNumber;

    @ApiModelProperty(value = "当前页码", position = 4)
    private Long currentPage;

    @ApiModelProperty(value = "总页数", position = 5)
    private Long totalPage;

    @ApiModelProperty(value = "每页记录数", position = 6)
    private Long pageSize;

    @ApiModelProperty(value = "简单公告信息列表", position = 7)
    private List<SimpleBidNoticeDTO> bidNotices;

    @ApiModelProperty(value = "简单包段信息", position = 8)
    private List<PackageInfoDTO> packageInfo;

    @ApiModelProperty(value = "招标公告阶段", position = 9)
    private Integer bidRound;
}
