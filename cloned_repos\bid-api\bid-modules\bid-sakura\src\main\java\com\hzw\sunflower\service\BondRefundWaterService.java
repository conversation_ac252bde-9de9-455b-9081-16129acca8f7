package com.hzw.sunflower.service;

import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.controller.response.BondWaterPageVo;
import com.hzw.sunflower.controller.response.BondWaterRefundVO;
import com.hzw.sunflower.controller.response.ImportExcelVo;
import com.hzw.sunflower.dto.NjBankWaterDetailDto;
import com.hzw.sunflower.entity.BondRefundWater;
import com.hzw.sunflower.entity.BondWater;
import com.hzw.sunflower.entity.condition.BondProjectCondition;
import com.hzw.sunflower.entity.condition.BondWaterCondition;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.List;

/**
 * <p>
 * 保证金流水表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-27
 */
public interface BondRefundWaterService extends IService<BondRefundWater> {

}
