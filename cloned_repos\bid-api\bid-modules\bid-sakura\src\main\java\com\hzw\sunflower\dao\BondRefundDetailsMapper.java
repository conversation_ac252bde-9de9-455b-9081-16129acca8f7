package com.hzw.sunflower.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hzw.sunflower.dto.SyncNccRefundInfoDto;
import com.hzw.sunflower.entity.BondRefundDetails;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 保证金退还表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-27
 */
@Mapper
public interface BondRefundDetailsMapper extends BaseMapper<BondRefundDetails> {

    /**
     * 查询退还详情（退还成功/处理中的不查）
     * @return
     */
    List<BondRefundDetails> getDetatisForRefund(@Param("refundId") Long refundId);

    /**
     * 查询同步到ncc的退还信息
     * @param refundId
     * @return
     */
    List<SyncNccRefundInfoDto> getSyncNccRefundInfo(@Param("refundId") Long refundId);
}
