#logger level default is INFO
log4j.rootLogger=ERROR,stdout,file

#append to stdout
log4j.appender.stdout=org.apache.log4j.ConsoleAppender
log4j.appender.stdout.layout=org.apache.log4j.PatternLayout
log4j.appender.stdout.layout.ConversionPattern=%d %p [%c] - %m%n

#append to file
log4j.appender.file=org.apache.log4j.RollingFileAppender
log4j.appender.file.File=D:/logs/GZZJK.log
log4j.appender.file.MaxFileSize=1MB
log4j.appender.file.MaxBackupIndex=10000
log4j.appender.file.layout=org.apache.log4j.PatternLayout
log4j.appender.file.layout.ConversionPattern=%d %p - %m%n

#ibatis logger config
log4j.logger.com.ibatis=ERROR
log4j.logger.com.ibatis.common.jdbc.SimpleDataSource=ERROR
log4j.logger.com.ibatis.common.jdbc.ScriptRunner=ERROR
log4j.logger.com.ibatis.sqlmap.engine.impl.SqlMapClientDelegate=ERROR
log4j.logger.java.sql.Connection=ERROR
log4j.logger.java.sql.Statement=ERROR
log4j.logger.java.sql.PreparedStatement=ERROR
log4j.logger.java.sql.ResultSet = ERROR
log4j.logger.org.apache=ERROR
#log4j.logger.com.opensymphony.xwork2=debug