package com.hzw.sunflower.controller.response;

import com.hzw.sunflower.common.OverrideBeanMethods;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
/**
 * 单位公司 返回实体
 *
 * <AUTHOR>
 * @version 1.0.0 2021-04-12
 */
@ApiModel(description = "人员列表 ")
@Data
public class UserDeptCompanyVo extends OverrideBeanMethods {

    @ApiModelProperty(value = "userId")
   private Long userId;

   @ApiModelProperty(value = "用户名")
   private String userName;

   @ApiModelProperty(value = "用户身份证")
   private String identityNumber;

   @ApiModelProperty(value = "护照号码")
   private String passportNumber;

   @ApiModelProperty(value = "国家编号")
   private String nationCode;

   @ApiModelProperty(value = "是否为境外用户 0.否 1.是")
   private String isUserAbroad;

   @ApiModelProperty(value = "身份标识")
   private Integer identity;

   @ApiModelProperty(value = "用户身份id")
   private Long userIdentityId;

   @ApiModelProperty(value = "用户手机号")
   private String userPhone;

   @ApiModelProperty(value = "法人授权函")
   private Long fileId;

   @ApiModelProperty(value = "是否是管理员  1 否 2 是")
   private Integer isAdmin;

   @ApiModelProperty(value = "审核状态")
   private Integer status;

   @ApiModelProperty(value = "企业id")
   private Long companyId;

   @ApiModelProperty(value = "部门id")
   private Long departmentId;

   @ApiModelProperty(value = "部门名称")
   private String departmentName;

   @ApiModelProperty(value = "部门code")
   private String departmentCode;

   @ApiModelProperty(value = "企业名称")
   private String companyName;

   @ApiModelProperty(value = "组织机构代码证")
   private String organizationNum;

   @ApiModelProperty(value = "企业营业执照")
   private String businessLicense;

   @ApiModelProperty(value = "是否为境外企业 0.否 1.是")
   private Integer isCompanyAbroad;

   @ApiModelProperty(value = "法人")
   private String legalRepresentative;

   @ApiModelProperty(value = "法人身份证")
   private String legalRepresentativeIdentity;

   @ApiModelProperty(value = "电子邮箱")
   private String email;

}
