package com.hzw.sunflower.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @description:
 * @author: fanqh
 * @create: 2023-09-18 10:23
 * @Version 1.0.0
 **/
@Data
public class AppOaMsgDto implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("消息标题")
    private String title;

    @ApiModelProperty("消息内容")
    private String content;

    @ApiModelProperty("消息跳转链接")
    private String linkUrl;

    @ApiModelProperty("推送用户列表")
    private List<String> recipientList;

    @ApiModelProperty("待办类型")
    private String taskType;

    @ApiModelProperty("消息通知类型 1.待办 2.待阅")
    private String msgType;
}
