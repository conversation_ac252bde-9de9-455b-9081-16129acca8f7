package com.hzw.Service.Impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.hzw.Service.LightReadingCallbackService;
import com.hzw.config.TokenProperties;
import com.hzw.constant.ApiEnum;
import com.hzw.entity.FileInfo;
import com.hzw.entity.InfoResponse;
import com.hzw.entity.OssFileInfo;
import com.hzw.redis.RedisCache;
import com.hzw.utils.ResultPreview;
import com.hzw.utils.SpringUtils;
import com.hzw.utils.TokenUtils;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.security.SecureRandom;
import java.util.Base64;
import java.util.concurrent.TimeUnit;

import static java.util.UUID.randomUUID;

@Log4j2
@Service
public class LightReadingCallbackServiceImpl implements LightReadingCallbackService {

    @Autowired
    private RedisCache redisCache;

    @Override
    public ResultPreview<String> getFileToken(OssFileInfo ossFileInfo , String token) {
        TokenProperties properties = SpringUtils.getBean(TokenProperties.class);
        Boolean check = false;
        if (ApiEnum.JSTCC.getType().equals(ossFileInfo.getType())) {
            check = token.equals(properties.getJstccToken());
        }
        if (ApiEnum.EXPERT.getType().equals(ossFileInfo.getType())) {
            check = token.equals(properties.getZjkToken());
        }
        if (!check) {
            return ResultPreview.failed("权限错误");
        }
        String UUID = randomUUID().toString().replace("-", "");
        redisCache.setCacheObject(UUID, JSONObject.toJSONString(ossFileInfo), 30, TimeUnit.MINUTES);
        //String jws = TokenUtils.generateToken(ossFileInfo.getOssKey());
        //redisCache.setCacheObject(ossFileInfo.getOssKey() + "_token",jws);
        return ResultPreview.ok(UUID);
    }

    @Override
    public InfoResponse getFileInfoV2(String token, String ossKey) {
        String key = TokenUtils.validateToken(token);
        String json = redisCache.getCacheObject(key + "_token");
        if (null == json) {
            log.info("token失效");
            return null;
        }
        //redisCache.deleteObject(key + "_token");
        //if (!key.equals(ossKey)) {
        //    log.info("token异常:{}",ossKey);
        //    return null;
        //}
        String ossJson = redisCache.getCacheObject(key);
        if (null == ossJson) {
            log.info("文件不存在:{}",key);
            return null;
        }
        OssFileInfo oss = JSON.parseObject(ossJson, OssFileInfo.class);

        InfoResponse response = new InfoResponse();
        FileInfo fileInfo = new FileInfo();
        fileInfo.setId(oss.getId());
        fileInfo.setName(oss.getOssName());
        fileInfo.setSize(oss.getSize());
        fileInfo.setDownloadUrl(oss.getDownloadUrl());
        //fileInfo.setUploadUrl("http://example.com/upload");
        response.setFile(fileInfo);
        // 可以根据需要设置其他属性的值
        return response;
    }

    @Override
    public InfoResponse getFileInfo(String token) {
        String ossJson = redisCache.getCacheObject(token);
        if (null == ossJson) {
            log.info("文件不存在:{}",token);
            return null;
        }
        redisCache.deleteObject(token);
        OssFileInfo oss = JSON.parseObject(ossJson, OssFileInfo.class);
        InfoResponse response = new InfoResponse();
        FileInfo fileInfo = new FileInfo();
        fileInfo.setId(oss.getId());
        fileInfo.setName(oss.getOssName());
        fileInfo.setSize(oss.getSize());
        fileInfo.setDownloadUrl(oss.getDownloadUrl());
        response.setFile(fileInfo);
        // 可以根据需要设置其他属性的值
        return response;
    }


    public static void main(String[] args) {
        SecureRandom random = new SecureRandom();
        byte[] tokenBytes = new byte[32]; // 32字节 = 256位安全强度
        random.nextBytes(tokenBytes);
        String s = Base64.getUrlEncoder().withoutPadding().encodeToString(tokenBytes);
        System.out.println(s);
    }

}
