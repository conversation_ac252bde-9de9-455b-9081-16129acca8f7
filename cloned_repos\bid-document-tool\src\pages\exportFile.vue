<template>
<div>
 <div class="exportFile">
    <div class="exportOffice" v-html="props.fileHtml">
    </div>
 </div>
 <div class="footBtn">
    <el-button @click="nextStep" type="info">上一步</el-button>
    <el-button @click="exportFn" type="primary">导出</el-button>
  </div>
</div>
 
</template>

<script setup>
import { ref } from 'vue'
//引入VueOfficeDocx组件
import VueOfficeDocx from '@vue-office/docx'
//引入相关样式
import '@vue-office/docx/lib/index.css'
const props = defineProps(['fileHtml'])

const src = ref('')
const generatePdfFn = async () => {
 await window.electronAPI.generatePdf(props.fileHtml);   
}


const generateDocxFn = async () => {
    await window.electronAPI.generateDocx(props.fileHtml);
}

const emits = defineEmits(['nextStep'])
const nextStep = ()=>{
    emits('nextStep', 2)
}
const exportFn = ()=>{
    generatePdfFn()
    // generateDocxFn()
}
</script>

<style>
.exportOffice{
    width: 820px;
    margin: 0 auto;
    padding: 20px 20px 50px 20px;
    margin: 30px auto 150px auto;
    box-shadow: 0 2px 10px rgb(0 0 0 / 12%);
}
</style>