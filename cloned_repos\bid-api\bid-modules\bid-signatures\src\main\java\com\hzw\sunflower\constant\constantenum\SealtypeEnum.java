package com.hzw.sunflower.constant.constantenum;

public enum SealtypeEnum {

    // 用印方式
    ELECTRONIC_SEAL(1,"电子章"),
    PHYSICAL_SEAL(2,"实体章"),
    ;

    private Integer type;
    private String desc;

    SealtypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static String getValueByKey(Integer key) {
        for (SealtypeEnum enumValue : SealtypeEnum.values()) {
            if (enumValue.type.equals(key)) {
                return enumValue.desc;
            }
        }
        return null;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
