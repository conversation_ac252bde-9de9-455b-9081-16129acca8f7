package com.hzw.sunflower.constant.constantenum;

/**
 * <AUTHOR>
 * @date ：Created in 2022/07/27 9:23
 * @description：退还申请状态
 * @version: 1.0
 */
public enum BondRefundStatusEnum {
    //状态  1：待处理  2：已退回    3：退还成功 4：退还失败  5.退还中    8:已撤回
    WAIT(1, "待处理"),
    RETURNED(2, "已退回"),
    SUCCESS(3, "退还成功"),
    FAIL(4, "退还失败"),
    REFUNDING(5, "退还中"),
    WITHDRAWED(8, "已撤回");


    private Integer type;
    private String desc;

    BondRefundStatusEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
