package com.hzw.sunflower.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hzw.sunflower.controller.request.CompanyFlowConfigReq;
import com.hzw.sunflower.entity.CompanyFlowConfig;
import org.apache.ibatis.annotations.Param;

/**
* CompanyFlowConfigMapper接口
*
*/
public interface CompanyFlowConfigMapper extends BaseMapper<CompanyFlowConfig> {

    /**
     * 根据部门id获取流程配置
     * @param req
     * @return
     */
    CompanyFlowConfig getByDeptId(@Param("req") CompanyFlowConfigReq req);
}