package com.hzw.sunflower.entity.condition;

import com.hzw.sunflower.common.BaseCondition;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2022/9/5 10:59
 * @description：数据修改记录分页查询条件
 * @modified By：`
 * @version: 1.0
 */
@Data
@ApiModel(description = "数据修改记录分页查询参数条件")
public class DataUpdateRecordCondition extends BaseCondition {

    @ApiModelProperty("关键字")
    private String keyWords;

    @ApiModelProperty(value = "修改类型 1.委托项目名称 2.采购项目名称 3.采购文件发售截止时间 4.响应文件递交截止时间 5.企业信息")
    private Integer type;

    @ApiModelProperty(value = "起始日期")
    private String startDate;

    @ApiModelProperty(value = "结束日期")
    private String endDate;

    private  Long userId;

    @ApiModelProperty(value = "部门id")
    private Long departId;

    private List<String> taskCodes;

}
