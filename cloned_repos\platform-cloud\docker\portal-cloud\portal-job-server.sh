#!/bin/sh
#cp TEMPLATE FILE
docker stop portal-job-server || true
docker rm portal-job-server || true
docker rmi registry.cn-hangzhou.aliyuncs.com/hzw-jtcc/jtcc:portal-job-server-1.0.0 || true
docker pull registry.cn-hangzhou.aliyuncs.com/hzw-jtcc/jtcc:portal-job-server-1.0.0
docker run -itd --net=host  --log-opt max-size=100m --log-opt max-file=3  --name portal-job-server -v /home/<USER>/plan1/portal-cloud/config/portal-job-server:/hzw/job/config -v /data/log:/hzw/job/logs  registry.cn-hangzhou.aliyuncs.com/hzw-jtcc/jtcc:portal-job-server-1.0.0
