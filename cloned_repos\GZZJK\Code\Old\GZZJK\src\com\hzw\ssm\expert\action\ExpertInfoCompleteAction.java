package com.hzw.ssm.expert.action;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFFont;
import org.apache.poi.hssf.usermodel.HSSFPrintSetup;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.struts2.ServletActionContext;
import org.apache.struts2.convention.annotation.Action;
import org.apache.struts2.convention.annotation.Namespace;
import org.apache.struts2.convention.annotation.ParentPackage;
import org.apache.struts2.convention.annotation.Result;
import org.apache.struts2.convention.annotation.Results;
import org.springframework.beans.factory.annotation.Autowired;

import com.hzw.ssm.expert.entity.ExpertAuditEntity;
import com.hzw.ssm.expert.entity.ExpertExperienceEntity;
import com.hzw.ssm.expert.entity.ExpertInfoEntity;
import com.hzw.ssm.expert.entity.ExpertMajorEntity;
import com.hzw.ssm.expert.entity.ExpertPracticeEntity;
import com.hzw.ssm.expert.entity.ExpertSupplementInfoEntity;
import com.hzw.ssm.expert.entity.ExpertUpdateRecordEntity;
import com.hzw.ssm.expert.service.ExpertInfoCompleteService;
import com.hzw.ssm.expert.service.ExpertInfoService;
import com.hzw.ssm.fw.base.BaseAction;
import com.hzw.ssm.fw.util.ExcelUtil;
import com.hzw.ssm.fw.util.MessageConstants;
import com.hzw.ssm.fw.util.SysConstants;
import com.hzw.ssm.sys.user.entity.UserEntity;

@Namespace("/expertComplete")
@ParentPackage(value = "default")
@Results({ @Result(	name = "completeList",
					location = "/jsp/complete/completelist.jsp"),
	@Result(name = "expertComplete", location = "/jsp/expert/expert_complete.jsp") })
/**
 * 专家信息完善Action
 */
public class ExpertInfoCompleteAction extends BaseAction {

	private static final long serialVersionUID = -8823858495799309882L;

	@Autowired
	private ExpertInfoCompleteService expertinfoService;

	@Autowired
	private ExpertInfoService expertInfoService;
	/** 专家基本信息 */
	private ExpertInfoEntity expertInfoEntity;
	/** 执业资格 */
	private List<ExpertPracticeEntity> practiceList;

	/** 评标专业列表集合 */
	private List<ExpertMajorEntity> majorList;

	/** 工作经历集合列表 */
	private List<ExpertExperienceEntity> experienceList;
	 /**
     * 专家基本信息封装查询条件
     */
    private ExpertInfoEntity colExpertInfo;
	/** 通用key */
	private String key;

	/** 身份证复印件对应的文件域 */
	private File idFile;
	/** 身份证复印件背面对应的文件域 */
	private File ICBackFile;

	/** 身份证复印件背面文件名 */
	private String ICBackFileFileName;

	/** 身份证复印件对应文件名 */
	private String idFileFileName;

	/** 毕业证书复印对应的文件域 */
	private File certificateFile;

	/** 毕业证书复印对应的文件名 */
	private String certificateFileFileName;

	/** 证件照片对应的文件域 */
	private File photoFileid;

	/** 证件照片对应的文件名 */
	private String photoFileidFileName;

	/** 职称证书 对应的文件域 */
	private File technicalFiled;

	/** 职称证书 对应文件名 */
	private String technicalFiledFileName;

	/** 执业资格文件列表对应页面多个文件域 */
	private File practiceFile1;
	private File practiceFile2;
	private File practiceFile3;

	/** 执业资格文件名列表对应页面多个文件域文件名 */
	private String practiceFile1FileName;
	private String practiceFile2FileName;
	private String practiceFile3FileName;

	private String filePath;

	private String tableField;

	private String fileId;

	private String tableName;

	 /**
     * 专家信息修改记录列表
     */
    private List<ExpertUpdateRecordEntity> updateRecordList;

    /**
     * 审核意见实体类
     */
    private ExpertAuditEntity auditEntity;
	/**
	 * 专家信息列表
	 * 
	 * @return
	 * @throws Exception
	 */
	@Action("expertCompleteList")
	public String expertInfoCompleteList() {
		if (exertEntity == null) {
			exertEntity = new ExpertSupplementInfoEntity();
			exertEntity.setExpertGrade("0000");
			exertEntity.setIsBasicInfo("00");
			exertEntity.setIsCertificateInfo("00");
		}
		if ("2000".equals(exertEntity.getExpertGrade())) {
			exertEntity.setIsBasicInfo("1");
			exertEntity.setIsCertificateInfo("1");
		}
		exertEntity.setPage(this.getPage());
		List<ExpertSupplementInfoEntity> list = expertinfoService.queryListexpert(exertEntity);

		if ("0000".equals(exertEntity.getExpertGrade())) {
			expertlist = list;
		}
		if ("1000".equals(exertEntity.getExpertGrade())) {
			expertlist1 = list;
		}
		if ("2000".equals(exertEntity.getExpertGrade())) {
			expertlist2 = list;
			exertEntity.setIsBasicInfo(null);
			exertEntity.setIsCertificateInfo(null);
		}
		return "completeList";
	}

	/**
	 * 专家信息已完善
	 * 
	 * @return
	 * @throws Exception
	 */
	@Action("compeleteExpert")
	public String compeleteExpert() {
		try {
			if (exertEntity == null) {
				exertEntity = new ExpertSupplementInfoEntity();
			}
			exertEntity.setId(key);
			expertinfoService.expertCompelete(exertEntity);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return this.expertInfoCompleteList();
	}

	/**
	 * 专家信息备注
	 * 
	 * @return
	 * @throws Exception
	 */
	@Action("expertRemark")
	public String expertRemark() {
		try {
			if (exertEntity == null) {
				exertEntity = new ExpertSupplementInfoEntity();
			}
			exertEntity.setId(key);
			expertinfoService.expertRemark(exertEntity);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return this.expertInfoCompleteList();
	}

	/**
	 * 专家信息管理保存
	 * 
	 * @return
	 * @throws Exception
	 */
	@Action("save")
	public String save() throws Exception {
		try {
			this.context();
			// 当前session
			HttpSession session = getRequest().getSession();
			// 当前用户信息
			UserEntity info = (UserEntity) session.getAttribute("userInfo");

			expertinfoService.uploadFile(expertInfoEntity, practiceList, idFile, idFileFileName, certificateFile,
					certificateFileFileName, photoFileid, photoFileidFileName, technicalFiled, technicalFiledFileName,
					practiceFile1, practiceFile1FileName, practiceFile2, practiceFile2FileName, practiceFile3,
					practiceFile3FileName, ICBackFile, ICBackFileFileName);

			expertInfoEntity.setModify_time(new Date());
			expertInfoEntity.setModify_user(info.getUser_id());
			expertInfoEntity.setModify_role(info.getRole_id());
			expertInfoEntity.setModify_reason("");
			if (null != expertInfoEntity.getEnterFlag()) {
				expertInfoEntity.setEnterFlag(SysConstants.ENTER_FLAG.ADMIN_ENTER);// 专家录入标识
			}
			// 判断数据全不全
			if (StringUtils.isNotEmpty(expertInfoEntity.getUser_name())
					&& StringUtils.isNotEmpty(expertInfoEntity.getMobilephone())
					&& StringUtils.isNotEmpty(expertInfoEntity.getProvince())
					&& StringUtils.isNotEmpty(expertInfoEntity.getId_no())
					&& StringUtils.isNotEmpty(expertInfoEntity.getEducations())
					&& StringUtils.isNotEmpty(expertInfoEntity.getTechnical_tital())
					&& expertInfoEntity.getTechnicalTime() != null) {
				expertInfoEntity.setIsBasicInfo("1");
			} else {
				expertInfoEntity.setIsBasicInfo("0");
			}
			expertInfoService.compareExpertInfoManager(expertInfoEntity, practiceList, majorList, experienceList);
			expertInfoService.expertInfoSyncIntoBakTable(expertInfoEntity);
			this.setAlertMessage(MessageConstants.SAVE_SUCCESS);
		} catch (Exception e) {
			this.setAlertMessage(MessageConstants.SAVE_FAILED);
			e.printStackTrace();
			expertInfoService.uploadFileDelete(expertInfoEntity, practiceList, idFile, idFileFileName, certificateFile,
					certificateFileFileName, photoFileid, photoFileidFileName, technicalFiled, technicalFiledFileName,
					practiceFile1, practiceFile1FileName, practiceFile2, practiceFile2FileName, practiceFile3,
					practiceFile3FileName);
		}
		this.key = expertInfoEntity.getUser_id();
		return expertComplete();
	}

	/**
	 * 导出
	 * 
	 * @return
	 */
	@Action("ExpertDetailInfo")
	public String ExpertDetailInfo() {
		if (exertEntity == null) {
			exertEntity = new ExpertSupplementInfoEntity();
			exertEntity.setExpertGrade("0000");
			exertEntity.setIsBasicInfo("00");
			exertEntity.setIsCertificateInfo("00");
		}
		if ("2000".equals(exertEntity.getExpertGrade())) {
			exertEntity.setIsBasicInfo("1");
			exertEntity.setIsCertificateInfo("1");
		}
		List<ExpertSupplementInfoEntity> list = expertinfoService.queryListexpertInfo(exertEntity);
		this.context();
		HttpServletResponse respose = this.getResponse();
		respose.setHeader("Content-type", "text/html;charset=utf-8");
		respose.setCharacterEncoding("utf-8");
		PrintWriter out = null;
		String tableTopStyle = "0";
		try {
			out = respose.getWriter();
			int totalCols = 7;
			int autoSizeColsCount = 6;
			int currentRowNo = 0;
			HSSFWorkbook wb = ExcelUtil.createHSSFWorkbook();
			// 标题
			String titleName = "专家待完善信息统计(汇总)";
			if ("0000".equals(exertEntity.getExpertGrade())) {
				titleName = "中级专家待完善信息统计";
				fileName = "IntermediateExpert";
			}
			if ("1000".equals(exertEntity.getExpertGrade())) {
				titleName = "高级专家待完善信息统计";
				fileName = "SeniorExperts";
			}
			if ("2000".equals(exertEntity.getExpertGrade())) {
				titleName = "专家完善信息统计";
				fileName = "ExpertPerfection";
			}

			HSSFCellStyle titleCell = ExcelUtil.createHSSFCellStyle(wb, 30, HSSFFont.BOLDWEIGHT_BOLD,
					HSSFCellStyle.ALIGN_CENTER);
			HSSFSheet sheet = ExcelUtil.createHSSFSheet(wb, titleName, HSSFPrintSetup.A4_PAPERSIZE, tableTopStyle);
			currentRowNo = ExcelUtil.mergeRowsOne2(sheet, titleCell, currentRowNo, totalCols,
					new String[] { titleName }, new int[] { totalCols });

			currentRowNo++;
			HSSFCellStyle tableTotalCell = ExcelUtil.createHSSFCellStyle(wb, 10, HSSFFont.BOLDWEIGHT_NORMAL,
					HSSFCellStyle.ALIGN_CENTER);
			String[] tableTotal = { "序号", "专家名字", "专家手机", "专家省份范围", "基本信息", "专家证书信息", "备注" };
			int[] tableCols = { 1, 1, 1, 1, 1, 1, 1 };
			currentRowNo = ExcelUtil.mergeRowsOne2(sheet, tableTotalCell, currentRowNo, totalCols, tableTotal,
					tableCols);

			int[] contentCols = { 1, 1, 1, 1, 1, 1, 1 };

			for (int i = 0; i < list.size(); i++) {
				currentRowNo++;
				List<String> sls = entityToList(i, list.get(i)); //
				// * 动态合并第一步将相同的数据变成空值，保留第一次出现的数据()
				currentRowNo = mergeRowsOne1(sheet, tableTotalCell, currentRowNo, currentRowNo, totalCols, sls,
						contentCols);

			}
			ExcelUtil.autoSheetAutoSizeColumn(sheet, autoSizeColsCount);
			// 备注设置宽度
			sheet.setColumnWidth(totalCols - 1, 10000);
			getFile(titleName, wb, out);
			String isGen = exertEntity.getExpertGrade();
			exertEntity = new ExpertSupplementInfoEntity();
			exertEntity.setExpertGrade(isGen);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}

	/**
	 * 获取文件保存路径
	 * 
	 * @param titleName
	 * @param wb
	 * @param out
	 */
	private void getFile(String titleName, HSSFWorkbook wb, PrintWriter out) {
		String savePath = ServletActionContext.getServletContext().getRealPath("") + File.separator + fileName;
		File f1 = new File(savePath);
		if (!f1.exists()) {//
			f1.mkdirs();
		}
		String newName = titleName + "_" + System.currentTimeMillis() + ".xls";
		FileOutputStream fileOut = null;
		try {
			fileOut = new FileOutputStream(savePath + File.separator + newName);
			wb.write(fileOut);
		} catch (Exception e) {
			out.print("error");
			log.error(e);
		} finally {
			if (fileOut != null) {
				try {
					fileOut.close();
					// out.print("zjcqjl/"+projectEntity.getProjectId());
					// newName=new String(newName.getBytes("GB-2312"),"UTF-8");
					out.print("{\"filePath\":\"" + "/" + fileName + "\"," + "\"fileName\":\"" + newName + "\"}");
					out.close();
				} catch (IOException e) {
					out.print("error");
				}
			}
		}
	}

	/**
	 * 查看专家详细信息
	 * 
	 * @return
	 */
	@Action("toExpertDetail")
	public String toExpertDetail() {

		return "";
	}

	public List<String> entityToList(int i, ExpertSupplementInfoEntity entity) {
		List<String> lt = new ArrayList<String>();
		lt.add((i + 1) + "");
		lt.add(entity.getUserName());
		lt.add(entity.getMobilePhone());
		if ("0".equals(entity.getIsProv())) {
			lt.add("外省");
		} else {
			lt.add("本省");
		}
		if ("0".equals(entity.getIsBasicInfo())) {
			lt.add("不全");
		} else {
			lt.add("全");
		}
		if ("0".equals(entity.getIsCertificateInfo())) {
			lt.add("不全");
		} else {
			lt.add("全");
		}
		lt.add(entity.getRemark());
		return lt;
	}

	/**
	 * 
	 * 函数功能描述：生成结构化EXCEL (主要用在遍历没有跨行EXCEL的内容)
	 * 
	 * @param sheet
	 *            表单对象
	 * @param style
	 *            单元格风格
	 * @param startRowNo
	 *            开始行
	 * @param currentRowNo
	 *            当前行号
	 * @param totalCols
	 *            每行共有多少单元格
	 * @param contextList
	 *            需要合并的内容 单元格List
	 * @param mergeCols
	 *            内容对应占有多少单元格 new[] margeCols 只需要告知跨多少列
	 * @return
	 */
	public static int mergeRowsOne1(HSSFSheet sheet, HSSFCellStyle style, int startRowNo, int currentRowNo,
			int totalCols, List<String> contextList, int[] mergeCols) {
		if (mergeCols == null || mergeCols.length <= 0) {
			return currentRowNo;
		}
		if (contextList == null) {
			contextList = new ArrayList<String>();
		}
		// 1.创建改行
		HSSFRow row = ExcelUtil.createHSSFRow(style, sheet, currentRowNo, totalCols);
		int firstCols = 0;
		int lastCols = 0;
		for (int i = 0; i < mergeCols.length; i++) {
			lastCols = firstCols + mergeCols[i] - 1;
			HSSFCell cell = row.getCell(firstCols);
			cell.setCellStyle(style);
			String value = null;
			if (contextList.size() > i) {
				value = contextList.get(i);
			}

			if (value == null || value == "") {
				value = "";
			}
			cell.setCellType(HSSFCell.CELL_TYPE_STRING);
			cell.setCellValue(value);
			sheet.setColumnWidth(firstCols, value.getBytes().length * 2);
			/*
			 * sheet.addMergedRegion( new CellRangeAddress(startRowNo,
			 * currentRowNo, firstCols, lastCols));
			 */

			firstCols = lastCols + 1;
		}
		return currentRowNo;
	}

	/**
	 * 删除服务器上的文件
	 * 
	 * @return
	 */
	@Action("deleteFiles")
	public String deleteFiles() {
		String savePath = ServletActionContext.getServletContext().getRealPath("");
		this.filePath = savePath + filePath;
		File file = new File(this.filePath);
		if (file.exists()) {
			// file.delete(); 文件不删除【如果删除，修改前文件无法查】
		}
		HttpServletResponse response = ServletActionContext.getResponse();
		response.setCharacterEncoding("utf-8");
		PrintWriter writer = null;
		try {
			writer = response.getWriter();
			writer.print("success");
			// 更新专家表文件
			ExpertInfoEntity expertInfoEntity = new ExpertInfoEntity();
			expertInfoEntity.setId(fileId);
			expertInfoEntity.setTableField(tableField);
			expertInfoEntity.setTableName(tableName);
			expertinfoService.deleteFiles(expertInfoEntity);
			// 根据专家表id修改专家信息状态
		} catch (IOException e) {
			writer.print("fail");
		} finally {
			writer.flush();
			writer.close();
		}
		return null;
	}

	  /**
     * 专家信息完善页面
     *
     * @return
     */
    @Action("expertComplete")
    public String expertComplete() {
        if (colExpertInfo == null) {
            colExpertInfo = new ExpertInfoEntity();
            colExpertInfo.setUser_id(key);
        }
        expertInfoEntity = expertInfoService.getExpertInfoByUserIdOrId(colExpertInfo);
        practiceList = expertInfoService.queryPracticeByUserId(key);
        majorList = expertInfoService.queryMajorByUserId(key);
        experienceList = expertInfoService.queryExpertExperienceList(key);
        updateRecordList = expertInfoService.queryAllExpertUpdateRecordList(key);
        auditEntity = expertInfoService.getExpertAuditEntityByUserId(key);
        return "expertComplete";
    }
    
	private List<ExpertSupplementInfoEntity> expertlist;

	private List<ExpertSupplementInfoEntity> expertlist1;

	private List<ExpertSupplementInfoEntity> expertlist2;
	private ExpertSupplementInfoEntity exertEntity;

	private String fileName;

	public ExpertSupplementInfoEntity getExertEntity() {
		return exertEntity;
	}

	public void setExertEntity(ExpertSupplementInfoEntity exertEntity) {
		this.exertEntity = exertEntity;
	}

	public List<ExpertSupplementInfoEntity> getExpertlist() {
		return expertlist;
	}

	public void setExpertlist(List<ExpertSupplementInfoEntity> expertlist) {
		this.expertlist = expertlist;
	}

	public List<ExpertSupplementInfoEntity> getExpertlist1() {
		return expertlist1;
	}

	public void setExpertlist1(List<ExpertSupplementInfoEntity> expertlist1) {
		this.expertlist1 = expertlist1;
	}

	public List<ExpertSupplementInfoEntity> getExpertlist2() {
		return expertlist2;
	}

	public void setExpertlist2(List<ExpertSupplementInfoEntity> expertlist2) {
		this.expertlist2 = expertlist2;
	}

	public String getFileName() {
		return fileName;
	}

	public void setFileName(String fileName) {
		this.fileName = fileName;
	}

	/**
	 * @return the expertinfoService
	 */
	public ExpertInfoCompleteService getExpertinfoService() {
		return expertinfoService;
	}

	/**
	 * @param expertinfoService
	 *            the expertinfoService to set
	 */
	public void setExpertinfoService(ExpertInfoCompleteService expertinfoService) {
		this.expertinfoService = expertinfoService;
	}

	/**
	 * @return the expertInfoService
	 */
	public ExpertInfoService getExpertInfoService() {
		return expertInfoService;
	}

	/**
	 * @param expertInfoService
	 *            the expertInfoService to set
	 */
	public void setExpertInfoService(ExpertInfoService expertInfoService) {
		this.expertInfoService = expertInfoService;
	}

	/**
	 * @return the expertInfoEntity
	 */
	public ExpertInfoEntity getExpertInfoEntity() {
		return expertInfoEntity;
	}

	/**
	 * @param expertInfoEntity
	 *            the expertInfoEntity to set
	 */
	public void setExpertInfoEntity(ExpertInfoEntity expertInfoEntity) {
		this.expertInfoEntity = expertInfoEntity;
	}

	/**
	 * @return the practiceList
	 */
	public List<ExpertPracticeEntity> getPracticeList() {
		return practiceList;
	}

	/**
	 * @param practiceList
	 *            the practiceList to set
	 */
	public void setPracticeList(List<ExpertPracticeEntity> practiceList) {
		this.practiceList = practiceList;
	}

	/**
	 * @return the majorList
	 */
	public List<ExpertMajorEntity> getMajorList() {
		return majorList;
	}

	/**
	 * @param majorList
	 *            the majorList to set
	 */
	public void setMajorList(List<ExpertMajorEntity> majorList) {
		this.majorList = majorList;
	}

	/**
	 * @return the experienceList
	 */
	public List<ExpertExperienceEntity> getExperienceList() {
		return experienceList;
	}

	/**
	 * @param experienceList
	 *            the experienceList to set
	 */
	public void setExperienceList(List<ExpertExperienceEntity> experienceList) {
		this.experienceList = experienceList;
	}

	/**
	 * @return the key
	 */
	public String getKey() {
		return key;
	}

	/**
	 * @param key
	 *            the key to set
	 */
	public void setKey(String key) {
		this.key = key;
	}

	/**
	 * @return the idFile
	 */
	public File getIdFile() {
		return idFile;
	}

	/**
	 * @param idFile
	 *            the idFile to set
	 */
	public void setIdFile(File idFile) {
		this.idFile = idFile;
	}

	/**
	 * @return the idFileFileName
	 */
	public String getIdFileFileName() {
		return idFileFileName;
	}

	/**
	 * @param idFileFileName
	 *            the idFileFileName to set
	 */
	public void setIdFileFileName(String idFileFileName) {
		this.idFileFileName = idFileFileName;
	}

	/**
	 * @return the certificateFile
	 */
	public File getCertificateFile() {
		return certificateFile;
	}

	/**
	 * @param certificateFile
	 *            the certificateFile to set
	 */
	public void setCertificateFile(File certificateFile) {
		this.certificateFile = certificateFile;
	}

	/**
	 * @return the certificateFileFileName
	 */
	public String getCertificateFileFileName() {
		return certificateFileFileName;
	}

	/**
	 * @param certificateFileFileName
	 *            the certificateFileFileName to set
	 */
	public void setCertificateFileFileName(String certificateFileFileName) {
		this.certificateFileFileName = certificateFileFileName;
	}

	/**
	 * @return the photoFileid
	 */
	public File getPhotoFileid() {
		return photoFileid;
	}

	/**
	 * @param photoFileid
	 *            the photoFileid to set
	 */
	public void setPhotoFileid(File photoFileid) {
		this.photoFileid = photoFileid;
	}

	/**
	 * @return the photoFileidFileName
	 */
	public String getPhotoFileidFileName() {
		return photoFileidFileName;
	}

	/**
	 * @param photoFileidFileName
	 *            the photoFileidFileName to set
	 */
	public void setPhotoFileidFileName(String photoFileidFileName) {
		this.photoFileidFileName = photoFileidFileName;
	}

	/**
	 * @return the technicalFiled
	 */
	public File getTechnicalFiled() {
		return technicalFiled;
	}

	/**
	 * @param technicalFiled
	 *            the technicalFiled to set
	 */
	public void setTechnicalFiled(File technicalFiled) {
		this.technicalFiled = technicalFiled;
	}

	/**
	 * @return the technicalFiledFileName
	 */
	public String getTechnicalFiledFileName() {
		return technicalFiledFileName;
	}

	/**
	 * @param technicalFiledFileName
	 *            the technicalFiledFileName to set
	 */
	public void setTechnicalFiledFileName(String technicalFiledFileName) {
		this.technicalFiledFileName = technicalFiledFileName;
	}

	/**
	 * @return the practiceFile1
	 */
	public File getPracticeFile1() {
		return practiceFile1;
	}

	/**
	 * @param practiceFile1
	 *            the practiceFile1 to set
	 */
	public void setPracticeFile1(File practiceFile1) {
		this.practiceFile1 = practiceFile1;
	}

	/**
	 * @return the practiceFile2
	 */
	public File getPracticeFile2() {
		return practiceFile2;
	}

	/**
	 * @param practiceFile2
	 *            the practiceFile2 to set
	 */
	public void setPracticeFile2(File practiceFile2) {
		this.practiceFile2 = practiceFile2;
	}

	/**
	 * @return the practiceFile3
	 */
	public File getPracticeFile3() {
		return practiceFile3;
	}

	/**
	 * @param practiceFile3
	 *            the practiceFile3 to set
	 */
	public void setPracticeFile3(File practiceFile3) {
		this.practiceFile3 = practiceFile3;
	}

	/**
	 * @return the practiceFile1FileName
	 */
	public String getPracticeFile1FileName() {
		return practiceFile1FileName;
	}

	/**
	 * @param practiceFile1FileName
	 *            the practiceFile1FileName to set
	 */
	public void setPracticeFile1FileName(String practiceFile1FileName) {
		this.practiceFile1FileName = practiceFile1FileName;
	}

	/**
	 * @return the practiceFile2FileName
	 */
	public String getPracticeFile2FileName() {
		return practiceFile2FileName;
	}

	/**
	 * @param practiceFile2FileName
	 *            the practiceFile2FileName to set
	 */
	public void setPracticeFile2FileName(String practiceFile2FileName) {
		this.practiceFile2FileName = practiceFile2FileName;
	}

	/**
	 * @return the practiceFile3FileName
	 */
	public String getPracticeFile3FileName() {
		return practiceFile3FileName;
	}

	/**
	 * @param practiceFile3FileName
	 *            the practiceFile3FileName to set
	 */
	public void setPracticeFile3FileName(String practiceFile3FileName) {
		this.practiceFile3FileName = practiceFile3FileName;
	}

	/**
	 * @return the iCBackFile
	 */
	public File getICBackFile() {
		return ICBackFile;
	}

	/**
	 * @param iCBackFile
	 *            the iCBackFile to set
	 */
	public void setICBackFile(File iCBackFile) {
		ICBackFile = iCBackFile;
	}

	/**
	 * @return the iCBackFileFileName
	 */
	public String getICBackFileFileName() {
		return ICBackFileFileName;
	}

	/**
	 * @param iCBackFileFileName
	 *            the iCBackFileFileName to set
	 */
	public void setICBackFileFileName(String iCBackFileFileName) {
		ICBackFileFileName = iCBackFileFileName;
	}

	/**
	 * @return the filePath
	 */
	public String getFilePath() {
		return filePath;
	}

	/**
	 * @param filePath
	 *            the filePath to set
	 */
	public void setFilePath(String filePath) {
		this.filePath = filePath;
	}

	/**
	 * @return the tableField
	 */
	public String getTableField() {
		return tableField;
	}

	/**
	 * @param tableField
	 *            the tableField to set
	 */
	public void setTableField(String tableField) {
		this.tableField = tableField;
	}

	/**
	 * @return the fileId
	 */
	public String getFileId() {
		return fileId;
	}

	/**
	 * @param fileId
	 *            the fileId to set
	 */
	public void setFileId(String fileId) {
		this.fileId = fileId;
	}

	/**
	 * @return the tableName
	 */
	public String getTableName() {
		return tableName;
	}

	/**
	 * @param tableName
	 *            the tableName to set
	 */
	public void setTableName(String tableName) {
		this.tableName = tableName;
	}

}
