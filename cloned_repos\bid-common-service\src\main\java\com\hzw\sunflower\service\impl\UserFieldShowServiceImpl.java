package com.hzw.sunflower.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzw.sunflower.controller.request.UserFieldShowREQ;
import com.hzw.sunflower.dao.UserFieldShowMapper;
import com.hzw.sunflower.dto.UserFieldShowDTO;
import com.hzw.sunflower.entity.JwtUser;
import com.hzw.sunflower.entity.UserFieldShow;
import com.hzw.sunflower.service.UserFieldShowService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

/**
 * 用户字段展示表 Service接口实现
 *
 * <AUTHOR>
 * @version 1.0.0 2021-04-07
 */
@Service
public class UserFieldShowServiceImpl extends ServiceImpl<UserFieldShowMapper, UserFieldShow> implements UserFieldShowService {


    /**
     * 新增用户字段展示表 信息
     *
     * @param userFieldShow 用户字段展示表 信息
     * @return 是否成功
     */
    @Override
    public Boolean addUserFieldShow(UserFieldShow userFieldShow, JwtUser user) {
        userFieldShow.setUserId(user.getUserId());
        userFieldShow.setCompanyId(user.getCompanyId());
        return this.save(userFieldShow);
    }

    /**
     * 修改用户字段展示表 信息
     *
     * @param userFieldShow 用户字段展示表 信息
     * @return 是否成功
     */
    @Override
    public Boolean updateUserFieldShow(UserFieldShow userFieldShow, JwtUser user) {
        userFieldShow.setUserId(user.getUserId());
        userFieldShow.setCompanyId(user.getCompanyId());
        return this.updateById(userFieldShow);
    }


    /**
     * 根据用户字段展示表查询单条数据
     *
     * @param condition
     * @return
     */
    @Override
    public UserFieldShowDTO getOne(UserFieldShowREQ condition) {
        UserFieldShowDTO dto = null;
        QueryWrapper<UserFieldShow> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(UserFieldShow::getUserId, condition.getUserId());
        queryWrapper.lambda().eq(UserFieldShow::getPageCode, condition.getPageCode());
        queryWrapper.lambda().eq(UserFieldShow::getCompanyId, condition.getCompanyId());
        UserFieldShow userFieldShow = this.getOne(queryWrapper);
        if (null != userFieldShow) {
            dto = new UserFieldShowDTO();
            BeanUtils.copyProperties(userFieldShow, dto);
        }
        return dto;
    }
}