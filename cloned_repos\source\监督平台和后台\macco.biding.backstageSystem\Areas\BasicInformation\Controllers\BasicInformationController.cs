﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using AttributeRouting.Web.Mvc;
using System.Threading.Tasks;
using Newtonsoft.Json;
using macco.infrastructure.extensions;
using macco.biding.backstageSystem.Models;
using macco.biding.backstageSystem.Areas.BasicInformation.Models;
using macco.biding.entities.mup007.operation.bidsupervision;
using macco.infrastructure.io;
using macco.biding.entities.mup007.operation.common;
using macco.biding.entities.mup007.operation.backstage;
using macco.biding.mvchelper.api;
using macco.infrastructure.security;
using macco.biding.backstageSystem.Tools;
using System.Collections.Specialized;
using System.IO;
using System.Data;
using System.Text;

namespace macco.biding.backstageSystem.Areas.BasicInformation.Controllers
{
    public class BasicInformationController : Controller
    {
        //
        // GET: /BasicInformation/BasicInformation/
        RestApiHelper apiHelper = new RestApiHelper();

        #region 用户信息管理
        #region 页面
        /// <summary>
        /// 用户信息管理页面
        /// </summary>
        /// <param name="collection"></param>
        /// <returns></returns>
        [GET("BasicInformation/InformationManagement")]
        public ActionResult InformationManagement()
        {
            return View();
        }
        /// <summary>
        /// 用户增加页面
        /// </summary>
        /// <param name="collection"></param>
        /// <returns></returns>
        [GET("BasicInformation/AddUser")]
        public ActionResult AddUser()
        {
            UserProfile model = new UserProfile();
            return View(model);
        }
        /// <summary>
        /// 锁号关联
        /// </summary>
        /// <param name="collection"></param>
        /// <returns></returns>
        [GET("BasicInformation/LockCorrelation")]
        public ActionResult LockCorrelation()
        {
            return View();
        }
        /// <summary>
        /// 密码修改页面
        /// </summary>
        /// <returns></returns>
        [GET("BasicInformation/ModifiyPassword")]
        public ActionResult ModifiyPassword()
        {
            return View();
        }
        #endregion

        #region 查询
        /// <summary>
        /// 查询用户基本信息
        /// </summary>
        /// <returns></returns>
        [GET("BasicInformation/FindUserInfo")]
        public async Task<ActionResult> FindUserInfo()
        {
            UserProfile model = new UserProfile();
            Dictionary<string, string> param = new Dictionary<string, string>();
            param.Add(CommonParam.ConnKey, DBConnHelper.GetConnectionKey(DBConnHelper.BidConfigData));
            param.Add(CommonParam.AppNo, SystemPlatformEnum.BackStage_Platform);
            param.Add(CommonParam.FunctionNo, ((int)SystemModuleEnum.BackStage_User).ToString());
            param.Add("UserID", Request.QueryString["UserID"]);
            param.Add(CommonParam.Command, "FindUserRoleInfo");
            var result = await apiHelper.SearchData(null, param);
            if (result != null && result.Tables.Count > 0)
            {
                model = ReflectionHelper<UserProfile>.DeserializeObject(result.Tables[0]);
                model.Password = EncryptDecryptHelper.DESDecrypt(result.Tables[0].Rows[0]["Password"].ToString());
            }
            return View("AddUser", model);
        }

        /// <summary>
        /// 查询用户列表
        /// </summary>
        /// <returns></returns>
        [GET("BasicInformation/InformationManagementList/{currentPage}")]
        public async Task<ActionResult> InformationManagementList(int currentPage)
        {
            PagingViewModel<UserProfile> model = new PagingViewModel<UserProfile>();
            List<UserProfile> prjList = new List<UserProfile>();
            Dictionary<string, string> param = new Dictionary<string, string>();
            param.Add(CommonParam.ConnKey, DBConnHelper.GetConnectionKey(DBConnHelper.BidConfigData));
            param.Add(CommonParam.AppNo, SystemPlatformEnum.BackStage_Platform);
            param.Add(CommonParam.FunctionNo, ((int)SystemModuleEnum.BackStage_User).ToString());
            param.Add(CommonParam.Command, "FindUserList");
            param.Add("PageIndex", currentPage.ToString());
            param.Add("KeyWords", HttpUtility.UrlDecode(Request.QueryString["KeyWords"]));
            var result = await apiHelper.SearchData(null, param);
            if (result != null && result.Tables.Count > 0)
            {
                prjList = ReflectionHelper<UserProfile>.DataTableToList(result.Tables[0]);
            }
            
            model.CurrentPage = currentPage;
            model.TotalPageNum = prjList.Count() > 0 ? CommonTools.GetTotoalPage(prjList[0].TotalRows, CommonConst.PAGING_SIZE) : 1;
            model.Data = prjList;
            return View(model);
        }

        /// <summary>
        /// 用户访问菜单页面
        /// </summary>
        /// <param name="collection"></param>
        /// <returns></returns>
        [GET("BasicInformation/ComeInUserLog")]
        public ActionResult ComeInUserLog()
        {
            return View();
        }

        /// <summary>
        /// 查询用户访问菜单记录列表
        /// </summary>
        /// <returns></returns>
        [POST]
        public async Task<ActionResult> ComeInUserLogList(int currentPage,string startDate,string endDate,string whereData)
        {
            PagingViewModel<ComeInUserLog> model = new PagingViewModel<ComeInUserLog>();
            List<ComeInUserLog> prjList = new List<ComeInUserLog>();
            Dictionary<string, string> param = new Dictionary<string, string>(); 
            param.Add(CommonParam.ConnKey, DBConnHelper.GetConnectionKey(DBConnHelper.SupervisionData));
            param.Add(CommonParam.AppNo, SystemPlatformEnum.Bidsupervision);
            param.Add(CommonParam.FunctionNo, "403010");
            param.Add(CommonParam.Command, "ComeInUserLog");          
            param.Add("PageIndex", currentPage.ToString());
            param.Add("Type", "SELECT");
            param.Add("StartDate", String.IsNullOrEmpty(startDate) ? DateTime.MinValue.ToShortDateString() : startDate);
            param.Add("EndDate", String.IsNullOrEmpty(endDate) ? DateTime.MaxValue.ToShortDateString() : endDate);
            param.Add("WhereData", String.IsNullOrEmpty(whereData) ? DateTime.MaxValue.ToShortDateString() : endDate);
            var result = await apiHelper.SearchData(null, param);
            if (result != null && result.Tables.Count > 0)
            {
                prjList = ReflectionHelper<macco.biding.entities.mup007.operation.backstage.ComeInUserLog>.DataTableToList(result.Tables[0]);
            }
            model.CurrentPage = currentPage;
            model.TotalPageNum = prjList.Count() > 0 ? CommonTools.GetTotoalPage(prjList[0].TotalRows, CommonConst.PAGING_SIZE) : 1;
            model.Data = prjList;
            return View(model);
        }

        /// <summary>
        /// 载入下拉角色列表
        /// </summary>
        /// <param name="classFlag"></param>
        /// <param name="parentCode"></param>
        /// <returns></returns>
        [GET("BasicInformation/GetGeneralCode")]
        public async Task<String> GetGeneralCode()
        {
            Dictionary<string, string> param = new Dictionary<string, string>();
            param.Add(CommonParam.ConnKey, DBConnHelper.GetConnectionKey(DBConnHelper.BidConfigData));
            param.Add(CommonParam.AppNo, SystemPlatformEnum.BackStage_Platform);
            param.Add(CommonParam.FunctionNo, ((int)SystemModuleEnum.BackStage_User).ToString());
            param.Add(CommonParam.Command, "FindUserRoleInfo");

            List<GeneralCode> list = new List<GeneralCode>();
            var ds = await apiHelper.SearchData(null, param);
            if (ds != null && ds.Tables.Count > 0)
            {
                list = ReflectionHelper<GeneralCode>.DataTableToList(ds.Tables[1]);
            }

            return JsonConvert.SerializeObject(list);

        }

        /// <summary>
        /// 查询旧密码是否正确
        /// </summary>
        /// <returns></returns>
        [GET("BasicInformation/FindPassword")]
        public async Task<String> FindPassword()
        {
            AjaxResult ajax = new AjaxResult();
            ajax.Result = "1";
            var UserID = EncryptDecryptHelper.DESDecrypt(Request.Cookies["UserID"].Value);
            UserProfile model = new UserProfile();
            Dictionary<string, string> param = new Dictionary<string, string>();
            param.Add(CommonParam.ConnKey, DBConnHelper.GetConnectionKey(DBConnHelper.BidConfigData));
            param.Add(CommonParam.AppNo, SystemPlatformEnum.BackStage_Platform);
            param.Add(CommonParam.FunctionNo, ((int)SystemModuleEnum.BackStage_User).ToString());
            param.Add(CommonParam.Command, "FindUserRoleInfo");
            param.Add("UserID", UserID);
            var result = await apiHelper.SearchData(null, param);
            if (result != null && result.Tables.Count > 0)
            {
                model = ReflectionHelper<UserProfile>.DeserializeObject(result.Tables[0]);
            }
            if (!EncryptDecryptHelper.DESEncrypt(Request.QueryString["Password"]).Equals(model.Password)) 
            {
                ajax.Result = "0";
                ajax.Message = "原密码错误请重新输入！";
            }
            return ajax.ToString();
        }
        #endregion

        #region 保存
        [POST("BasicInformation/SaveUser")]
        public async Task<string> SaveUser(FormCollection collection)
        {
            var UserID = EncryptDecryptHelper.DESDecrypt(Request.Cookies["UserID"].Value);
            Dictionary<string, string> param = new Dictionary<string, string>();
            param.Add(CommonParam.ConnKey, DBConnHelper.GetConnectionKey(DBConnHelper.BidConfigData));
            param.Add(CommonParam.AppNo, SystemPlatformEnum.BackStage_Platform);
            param.Add(CommonParam.FunctionNo, ((int)SystemModuleEnum.BackStage_User).ToString());
            param.Add(CommonParam.Command, "DoSaveUserInfo");
            param.Add("OperateType", Request.QueryString["OperateType"]);
            param.Add("EncryptPassword", EncryptDecryptHelper.DESEncrypt(collection["Password"]));
            var result = await apiHelper.HandleData(collection,param);

            return JsonConvert.DeserializeObject(result).ToString();
        }
        /// <summary>
        /// 修改密码
        /// </summary>
        /// <param name="collection"></param>
        /// <returns></returns>
        [POST("BasicInformation/Modify")]
        public async Task<string> Modify(FormCollection collection)
        {
            var UserID = EncryptDecryptHelper.DESDecrypt(Request.Cookies["UserID"].Value);
            Dictionary<string, string> param = new Dictionary<string, string>();
            param.Add(CommonParam.ConnKey, DBConnHelper.GetConnectionKey(DBConnHelper.BidConfigData));
            param.Add(CommonParam.AppNo, SystemPlatformEnum.BackStage_Platform);
            param.Add(CommonParam.FunctionNo, ((int)SystemModuleEnum.BackStage_User).ToString());
            param.Add(CommonParam.Command, "DoSaveUserInfo");
            param.Add("OperateType", Request.QueryString["OperateType"]);
            param.Add("UserID", UserID);
            param.Add("EncryptPassword", EncryptDecryptHelper.DESEncrypt(collection["Password"]));
            var result = await apiHelper.HandleData(collection, param);

            return JsonConvert.DeserializeObject(result).ToString();
        }
        #endregion

        #endregion

        #region 用户权限管理

        #region 页面

        /// <summary>
        /// 权限管理页面
        /// </summary>
        /// <returns></returns>
        [GET("BasicInformation/AuthorityManagement")]
        public ActionResult AuthorityManagement()
        {
            return View();
        }
        /// <summary>
        /// 角色增加页面
        /// </summary>
        /// <returns></returns>
        [GET("BasicInformation/AddRole")]
        public async Task<ActionResult> AddRole()
        {
            UserProfile model = new UserProfile();
            List<AuthoritySource> authority = new List<AuthoritySource>();
            Dictionary<string, string> param = new Dictionary<string, string>();
            param.Add(CommonParam.ConnKey, DBConnHelper.GetConnectionKey(DBConnHelper.BidConfigData));
            param.Add(CommonParam.AppNo, SystemPlatformEnum.BackStage_Platform);
            param.Add(CommonParam.FunctionNo, ((int)SystemModuleEnum.BackStage_Authority).ToString());
            param.Add(CommonParam.Command, "FindRoleAuthority");
            param.Add("OperateType", "Role");
            param.Add("RoleID", Request.QueryString["RoleID"]);
            var result = await apiHelper.SearchData(null, param);
            if (result != null && result.Tables.Count > 0)
            {
                model = ReflectionHelper<UserProfile>.DeserializeObject(result.Tables[0]);
                authority = ReflectionHelper<AuthoritySource>.DataTableToList(result.Tables[1]);
            }
            GetListSource(ref authority);
            ViewData.Add("authority", authority);
            return View(model);
        }
        #endregion

        #region 查询
        /// <summary>
        /// 查询用户列表
        /// </summary>
        /// <returns></returns>
        [GET("BasicInformation/AuthorityManagementtList/{currentPage}")]
        public async Task<ActionResult> AuthorityManagementtList(int currentPage)
        {
            PagingViewModel<UserProfile> model = new PagingViewModel<UserProfile>();
            List<UserProfile> prjList = new List<UserProfile>();
            Dictionary<string, string> param = new Dictionary<string, string>();
            param.Add(CommonParam.ConnKey, DBConnHelper.GetConnectionKey(DBConnHelper.BidConfigData));
            param.Add(CommonParam.AppNo, SystemPlatformEnum.BackStage_Platform);
            param.Add(CommonParam.FunctionNo, ((int)SystemModuleEnum.BackStage_Authority).ToString());
            param.Add(CommonParam.Command, "FindRoleList");
            param.Add("PageIndex", currentPage.ToString());
            param.Add("KeyWords", HttpUtility.UrlDecode(Request.QueryString["KeyWords"]));
            var result = await apiHelper.SearchData(null, param);
            if (result != null && result.Tables.Count > 0)
            {
                prjList = ReflectionHelper<UserProfile>.DataTableToList(result.Tables[0]);
            }
            model.CurrentPage = currentPage;
            model.TotalPageNum = prjList.Count() > 0 ? CommonTools.GetTotoalPage(prjList[0].TotalRows, CommonConst.PAGING_SIZE) : 1;
            model.Data = prjList;
            return View(model);
        }

        
        /// <summary>
        /// 查询模块
        /// </summary>
        /// <param name="Source"></param>
        private void GetListSource(ref List<AuthoritySource> Source)
        {
            List<AuthoritySource> authority = new List<AuthoritySource>();
            Dictionary<string, string> param = new Dictionary<string, string>();
            param.Add(CommonParam.ConnKey, DBConnHelper.GetConnectionKey(DBConnHelper.BidConfigData));
            param.Add(CommonParam.AppNo, SystemPlatformEnum.BackStage_Platform);
            param.Add(CommonParam.FunctionNo, ((int)SystemModuleEnum.BackStage_Authority).ToString());
            param.Add(CommonParam.Command, "FindRoleAuthority");
            param.Add("OperateType", "Authority");
            param.Add("RoleID", Request.QueryString["RoleID"]);
            param.Add("IsNeedValidateCA", "false");
            var result = apiHelper.SearchDataSync(null, param);
            if (result != null && result.Tables.Count > 0)
            {
                authority = ReflectionHelper<AuthoritySource>.DataTableToList(result.Tables[0]);
            }
            if (Source != null)
            {
                foreach (var p in authority)
                {
                    AuthoritySource item = Source.FirstOrDefault<AuthoritySource>(f => f.Menucode == p.Menucode);
                    if (item!=null)
                    {
                        item.IsChecked = true;
                    }
                }
                
            }
            
        }
        #endregion

        #region 保存

        [POST("BasicInformation/DosaveRole")]
        public async Task<string> DosaveRole(FormCollection collection)
        {
            Dictionary<string, string> param = new Dictionary<string, string>();
            param.Add(CommonParam.ConnKey, DBConnHelper.GetConnectionKey(DBConnHelper.BidConfigData));
            param.Add(CommonParam.AppNo, SystemPlatformEnum.BackStage_Platform);
            param.Add(CommonParam.FunctionNo, ((int)SystemModuleEnum.BackStage_Authority).ToString());
            param.Add(CommonParam.Command, "DosaveRole");
            param.Add("OperateType", Request.QueryString["OperateType"]);
            var result = await apiHelper.HandleData(collection, param);
            return JsonConvert.DeserializeObject(result).ToString();
        }
        #endregion
        #endregion

        #region 辅助查询

        #region SQL语句查询
        /// <summary>
        /// SQL语句页面
        /// </summary>
        /// <returns></returns>
        [GET("AssistQuery/SqlSentence")]
        public ActionResult SqlSentence()
        {
            return View("AssistQuery/SqlSentence");
        }
        /// <summary>
        /// 新建查询页面
        /// </summary>
        /// <returns></returns>
        [GET("AssistQuery/AddSqlPage")]
        public ActionResult AddSqlPage()
        {
            SqlQuery model = new SqlQuery();
            return View("AssistQuery/AddSqlPage",model);
        }
        /// <summary>
        /// 查询结果页面
        /// </summary>
        /// <returns></returns>
        [GET("AssistQuery/ExecuteResult")]
        public ActionResult ExecuteResult()
        {
            SqlQuery model = new SqlQuery();
            return View("AssistQuery/ExecuteResult");
        }
        /// <summary>
        /// SQL语句页面
        /// </summary>
        /// <returns></returns>
        [GET("AssistQuery/SqlSentenceList/{currentPage}")]
        public async Task<ActionResult> SqlSentenceList(int currentPage)
        {
            PagingViewModel<SqlQuery> model = new PagingViewModel<SqlQuery>();
            List<SqlQuery> prjList = new List<SqlQuery>();
            Dictionary<string, string> param = new Dictionary<string, string>();
            param.Add(CommonParam.ConnKey, DBConnHelper.GetConnectionKey(DBConnHelper.BidConfigData));
            param.Add(CommonParam.AppNo, SystemPlatformEnum.BackStage_Platform);
            param.Add(CommonParam.FunctionNo,"991000");
            param.Add(CommonParam.Command, "FindSqlQueryList");
            param.Add("PageIndex", currentPage.ToString());
            param.Add("PageSize", CommonConst.PAGING_SIZE.ToString());
            param.Add("OperateType", "List");
            var result = await apiHelper.SearchData(null, param);
            if (result != null && result.Tables.Count > 0)
            {
                prjList = ReflectionHelper<SqlQuery>.DataTableToList(result.Tables[0]);
            }
            model.CurrentPage = currentPage;
            model.TotalPageNum = prjList.Count() > 0 ? CommonTools.GetTotoalPage(prjList[0].TotalRows,CommonConst.PAGING_SIZE) : 1;
            model.Data = prjList;
            return View("AssistQuery/SqlSentenceList",model);
        }
        /// <summary>
        /// SQL执行页面
        /// </summary>
        /// <returns></returns>
        [GET("AssistQuery/ExecuteSqlSentence")]
        public async Task<ActionResult> ExecuteSqlSentence()
        {
            SqlQuery model = new SqlQuery();
            Dictionary<string, string> param = new Dictionary<string, string>();
            param.Add(CommonParam.ConnKey, DBConnHelper.GetConnectionKey(DBConnHelper.BidConfigData));
            param.Add(CommonParam.AppNo, SystemPlatformEnum.BackStage_Platform);
            param.Add(CommonParam.FunctionNo, "991000");
            param.Add(CommonParam.Command, "FindSqlQueryList");
            param.Add("OperateType", "details");
            param.Add("QueryId", Request.QueryString["QueryId"]);
            var result = await apiHelper.SearchData(null, param);
            if (result != null && result.Tables.Count > 0)
            {
                model = ReflectionHelper<SqlQuery>.DeserializeObject(result.Tables[0]);
                model.sqlParameters = ReflectionHelper<SqlParameters>.DataTableToList(result.Tables[1]);
            }

            return View("AssistQuery/ExecuteSqlSentence", model);
        }
        /// <summary>
        /// SQL修改页面
        /// </summary>
        /// <returns></returns>
        [GET("AssistQuery/UpdateSqlSentence")]
        public async Task<ActionResult> UpdateSqlSentence()
        {
            SqlQuery model = new SqlQuery();
            Dictionary<string, string> param = new Dictionary<string, string>();
            param.Add(CommonParam.ConnKey, DBConnHelper.GetConnectionKey(DBConnHelper.BidConfigData));
            param.Add(CommonParam.AppNo, SystemPlatformEnum.BackStage_Platform);
            param.Add(CommonParam.FunctionNo, "991000");
            param.Add(CommonParam.Command, "FindSqlQueryList");
            param.Add("OperateType", "details");
            param.Add("QueryId", Request.QueryString["QueryId"]);
            var result = await apiHelper.SearchData(null, param);
            if (result != null && result.Tables.Count > 0)
            {
                model = ReflectionHelper<SqlQuery>.DeserializeObject(result.Tables[0]);
                model.sqlParameters = ReflectionHelper<SqlParameters>.DataTableToList(result.Tables[1]);
            }

            return View("AssistQuery/AddSqlPage", model);
        }
        /// <summary>
        /// 保存更新操作
        /// </summary>
        /// <param name="collection"></param>
        /// <returns></returns>
        [POST("AssistQuery/DoSaveSqlSentence")]
        public async Task<string> DoSaveSqlSentence(FormCollection collection)
        {
            Dictionary<string, string> param = new Dictionary<string, string>();
            param.Add(CommonParam.ConnKey, DBConnHelper.GetConnectionKey(DBConnHelper.BidConfigData));
            param.Add(CommonParam.AppNo, SystemPlatformEnum.BackStage_Platform);
            param.Add(CommonParam.FunctionNo,"991000");
            param.Add(CommonParam.Command, "DoSaveSql");
            param.Add("OperateType", Request.QueryString["OperateType"]);
            //param.Add("QueryId", Request.QueryString["QueryId"]);
            param.Add("Params", HttpUtility.UrlDecode(collection["Params"]));
            var result = await apiHelper.HandleData(collection, param);
            return JsonConvert.DeserializeObject(result).ToString();
        }
        /// <summary>
        /// sql执行操作
        /// </summary>
        /// <param name="collection"></param>
        /// <returns></returns>
         [GET("AssistQuery/ExecuteSql")]
        public async Task<string> ExecuteSql()
        {
            SqlDataModel model = new SqlDataModel();
            model.Result = new List<SqlData>();

            Dictionary<string, string> param = new Dictionary<string, string>();
            param.Add(CommonParam.ConnKey, DBConnHelper.GetConnectionKey(DBConnHelper.BidConfigData));
            param.Add(CommonParam.AppNo, SystemPlatformEnum.BackStage_Platform);
            param.Add(CommonParam.FunctionNo, "991000");
            param.Add(CommonParam.Command, "FindDataFromSqlQuery");
            var QuerySqlString = Request.QueryString["QuerySqlString"];

            if (!string.IsNullOrEmpty(Request.QueryString["ParamValues"]))
            {
                string[] ParamValues = HttpUtility.UrlDecode(Request.QueryString["ParamValues"]).Split(',');
                int count = 0;
                while (count < ParamValues.Length)
                {
                    QuerySqlString = QuerySqlString.Replace(ParamValues[count], ParamValues[++count]);
                    count++;
                }
            }

            param.Add("SqlString", QuerySqlString.Replace(@"\n", " "));
            var result = await apiHelper.SearchData(null, param);
            if (result != null && result.Tables.Count > 0)
            {
                SqlData data = new SqlData();
                data.TableName = result.Tables[0].TableName;
                data.ColumnNames = new List<string>();
                foreach (DataColumn col in result.Tables[0].Columns)
                {
                    data.ColumnNames.Add(col.ColumnName);
                }
                data.Data = JsonConvert.SerializeObject(result.Tables[0]).Replace(@"\n", " ");
                model.Result.Add(data);

            }

            var ssss = JsonConvert.SerializeObject(model);
            return ssss;
        }
        /// <summary>
        /// 常规查询-数据导出
        /// </summary>
        /// <param name="collection"></param>
        /// <returns></returns>
        [POST("AssistQuery/SearchDataExportData")]
        public async Task<ActionResult> SearchDataExportData(FormCollection collection) 
        {
            //collection["Keywords"] = Server.UrlDecode(collection["Keywords"]);
            EpPlusHelper ep = new EpPlusHelper();
            DataTable dt = new DataTable();
            //  查数据
            Dictionary<string, string> param = new Dictionary<string, string>();
            param.Add(CommonParam.ConnKey, DBConnHelper.GetConnectionKey(DBConnHelper.BidConfigData));
            param.Add(CommonParam.AppNo, SystemPlatformEnum.BackStage_Platform);
            param.Add(CommonParam.FunctionNo, "991000");
            param.Add(CommonParam.Command, "FindDataFromSqlQuery");
            var QuerySqlString = HttpUtility.UrlDecode(Request.QueryString["QuerySqlString"]);

            if (!string.IsNullOrEmpty(Request.QueryString["ParamValues"]))
            {
                string[] ParamValues = HttpUtility.UrlDecode(Request.QueryString["ParamValues"]).Split(',');
                int count = 0;
                while (count < ParamValues.Length)
                {
                    QuerySqlString = QuerySqlString.Replace(ParamValues[count], ParamValues[++count]);
                    count++;
                }
            }

            param.Add("SqlString", QuerySqlString.Replace(@"\n", " "));
            var result = await apiHelper.SearchData(null, param);
            if (result != null && result.Tables.Count > 0)
            {
                SqlData data = new SqlData();
                List<string> ColumnName = new List<string>();
                data.TableName = result.Tables[0].TableName;
                data.ColumnNames = new List<string>();
                foreach (DataColumn col in result.Tables[0].Columns)
                {
                    data.ColumnNames.Add(col.ColumnName);
                    ColumnName.Add(col.ColumnName);
                }
                ep.ColumnName = ColumnName;
                ep.FiledName = ColumnName;
                dt = result.Tables[0];
            }
            var fileName = "统计数据导出" + DateTime.Now.ToShortDateString();
            ep.CreateSheet(fileName)
                .ChangeSheet(1)
                .AddColumnName()
                .AddRowdata(dt);
            ep.worksheet.Cells.AutoFitColumns();//宽度自适应
            string XlsxContentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
            return File(ep.package.GetAsByteArray(), XlsxContentType, HttpUtility.UrlEncode(fileName + ".xlsx", Encoding.UTF8));
        }


        #endregion
        #endregion
    }
}
