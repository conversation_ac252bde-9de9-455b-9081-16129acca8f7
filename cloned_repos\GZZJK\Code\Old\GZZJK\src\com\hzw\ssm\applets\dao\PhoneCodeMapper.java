package com.hzw.ssm.applets.dao;

import com.hzw.ssm.applets.entity.PhoneCode;
import org.apache.ibatis.annotations.Param;

/**
 * tbl_phone_code验证码 Dao接口
 */
public interface PhoneCodeMapper {

    PhoneCode checkPhone(@Param(value = "phone") String phone);

    PhoneCode checkSms(@Param(value = "phone") String phone, @Param(value = "verificationCode") String verificationCode);

    void deleteByPhone(@Param(value = "phone") String phone);

    PhoneCode queryByPhone(@Param(value = "phone") String phone);

    int querySendCount(@Param(value = "phone") String phone);

    int insertSelective(PhoneCode phoneCode);
}

