package com.hzw.sunflower.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hzw.sunflower.controller.request.*;
import com.hzw.sunflower.controller.response.*;
import com.hzw.sunflower.entity.BondApplyRefund;
import com.hzw.sunflower.entity.BondApplyRefundConfirm;
import com.hzw.sunflower.entity.BondWater;
import com.hzw.sunflower.entity.condition.BondApplyRefundCondition;
import com.hzw.sunflower.entity.condition.BondApplyRelationCondition;
import com.hzw.sunflower.entity.condition.BondNotRelationCondition;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;


/**
 * <p>
 * 保证金退还申请表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-27
 */
@Mapper
public interface BondApplyRefundMapper extends BaseMapper<BondApplyRefund> {

    /**
     * 根据用户id查询用户所在公司名称
     * @param userId
     * @return
     */
    String selectCompanyName(@Param("userId")Long userId);

    /**
     * 未关联流水，待处理(运管处)、已退回(运管处)状态列表查询
     * @param page
     * @param condition
     * @param companyName
     * @param relationWaterIds
     * @return
     */
    IPage<BondWater> bondApplyWaitDisposeList(Page<BondWater> page, @Param("condition") BondNotRelationCondition condition, @Param("companyName") String companyName, @Param("relationWaterIds") List<Long> relationWaterIds);


    /**
     * 未关联流水，退款中(财务),已退款(财务)状态列表查询
     * @param page
     * @param condition
     * @param companyName
     * @param relationWaterIds
     * @return
     */
    IPage<BondWater> bondApplyRefundList(Page<BondWater> page,@Param("condition") BondNotRelationCondition condition,@Param("companyName") String companyName,@Param("relationWaterIds") List<Long> relationWaterIds);

    /**
     * 查询当前登录供应商所在公司参与的所有项目信息
     * @param req
     * @param userId
     * @return
     */
    List<MyCompanyProjectVo> getMyCompanyProject(@Param("req") MyCompanyProjectReq req, @Param("userId") Long userId);

    /**
     * 根据项目id获取当前供应商参与是标包信息
     * @param req
     * @param userId
     * @return
     */
    List<MyCompanyProjectVo> getMyCompanySection(@Param("req") MyCompanyProjectReq req, @Param("userId") Long userId);

    /**
     * 异常退款申请待处理列表(由供应商发起)
     * @param page
     * @param condition
     * @return
     */
    IPage<BondApplyRefundVo> selectBondApplyRefundPage(Page<BondApplyRefundVo> page,@Param("condition") BondApplyRefundCondition condition);

    /**
     * 查询退回原因
     * @param applyRefundId
     * @return
     */
    BondApplyRefundConfirm getReturnList(@Param("applyRefundId") Long applyRefundId);

    /**
     * 查询供应商及其公司信息
     * @param applyId
     * @return
     */
    VendorVo getVendor(@Param("applyId") Long applyId);

    /**
     * 申请退还的关联项目信息
     * @param applyId
     * @return
     */
    List<MyCompanyProjectVo> getChooseProjects(@Param("applyId") Long applyId);

    /**
     * 供应商所在公司参与的项目信息
     *
     * @param userId
     * @param datascopesql
     * @return
     */
    List<MyCompanyProjectVo> getVendorProjects(@Param("userId")Long userId, @Param("dataScope") String datascopesql);

    /**
     * 根据部门id查询部门及处长信息
     * @param departmentIds
     * @return
     */
    List<DepartmentUserVo> getDepartments(@Param("departmentIds") List<Long> departmentIds,@Param("roleCodes") List<String> roleCodes);

    /**
     * 根据申请退款id查询部门处长处理结果
     * @param applyId
     * @return
     */
    List<DepartmentUserVo> getDepartmentResult(@Param("applyId") Long applyId);

    /**
     * 查询一条申请的保证金退还数据
     * @param applyRefundId
     * @return
     */
    BondRefundFeq getBondRefundReq(@Param("applyRefundId") Long applyRefundId);

    /**
     * 异常关联确认(项目经理发起)列表
     * @param page
     * @param condition
     * @return
     */
    IPage<AbnormalApplyRelationVo> getBondAbnormalRelationList(Page<AbnormalApplyRelationVo> page, @Param("condition") BondApplyRelationCondition condition);

    /**
     * 保证金关联申请(项目经理发起)列表
     * @param page
     * @param condition
     * @return
     */
    IPage<AbnormalApplyRelationVo> bondApplyRelationList(Page<AbnormalApplyRelationVo> page, @Param("condition") BondApplyRelationCondition condition);

    /**
     * 查询公司未关联流水
     * @param page
     * @param condition
     * @return
     */
    IPage<BondNotRelationVo> getBondWaterNotRelationPageNew(Page<BondNotRelationVo> page, @Param("condition") BondNotRelationCondition condition);

    /**
     * 根据项目id查询处长信息
     * @param projectIdList
     * @param roleCodes
     * @return
     */
    List<DepartmentUserVo> queryDepartCZByProject(@Param("projectIdList") List<Long> projectIdList, @Param("roleCodes") List<String> roleCodes);

    /**
     * 查询申请人所在公司
     * @param applyId
     * @return
     */
    Long queryApplyUserCompanyId(@Param("applyId") Long applyId);

    /**
     *  查询供应商参与的项目
     * @param page
     * @param req
     * @return
     */
    IPage<MyCompanyProjectVo> queryVendorProjects(Page<BondNotRelationVo> page, @Param("req") BondApplyRefundProjectDetailReq req);
}
