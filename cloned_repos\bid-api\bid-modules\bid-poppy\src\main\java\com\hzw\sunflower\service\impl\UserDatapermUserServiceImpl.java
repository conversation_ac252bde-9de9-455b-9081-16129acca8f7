package com.hzw.sunflower.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.dao.UserDatapermUserMapper;

import com.hzw.sunflower.dto.UserDataPermUserDTO;
import com.hzw.sunflower.dto.UserDepartDTO;
import com.hzw.sunflower.entity.UserDatapermUser;
import com.hzw.sunflower.entity.UserProjectFromUser;
import com.hzw.sunflower.security.utils.SecurityUtils;
import com.hzw.sunflower.service.UserDatapermUserService;
import com.hzw.sunflower.service.UserProjectFromUserService;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;


@Service
public class UserDatapermUserServiceImpl extends ServiceImpl<UserDatapermUserMapper, UserDatapermUser> implements UserDatapermUserService {

    @Resource
    private UserDatapermUserMapper userDatapermUserMapper;

    @Resource
    private UserProjectFromUserService userProjectFromUserService;

    /**
     * 批量添加项目授权
     * @param userDataPermUserDTO
     * @return
     */
    @Override
    public Result<Object> addBatchPerm(UserDataPermUserDTO userDataPermUserDTO) {
        Long fromUserId=SecurityUtils.getJwtUser().getUserId();
        List<Long> userIdList = new ArrayList<>();
        List<Long> departList = new ArrayList<>();
        for (UserDepartDTO userDepart : userDataPermUserDTO.getUserDeparts()) {
            userIdList.add(userDepart.getUserId());
            if(!departList.contains(userDepart.getDepartId())){
                departList.add(userDepart.getDepartId());
            }
        }
        // 用户权限
        List<UserDatapermUser> addList=new ArrayList<>();
        if (userDataPermUserDTO.getUserDeparts()!=null &&userDataPermUserDTO.getUserDeparts().size()>0){
            // 删除之前的授权
            userDatapermUserMapper.delete(new LambdaQueryWrapper<UserDatapermUser>()
                    .eq(UserDatapermUser::getFromUserId,fromUserId)
                    .in(UserDatapermUser::getUserId,userIdList)
                    .in(UserDatapermUser::getDepartId,departList)
            );
        }

        // 用户项目权限
        List<UserProjectFromUser> addProList=new ArrayList<>();
        for (UserDepartDTO userDepart : userDataPermUserDTO.getUserDeparts()) {
            //
            UserDatapermUser userDatapermUser=new UserDatapermUser();
            userDatapermUser.setFromUserId(fromUserId);
            userDatapermUser.setUserId(userDepart.getUserId());
            userDatapermUser.setDepartId(userDepart.getDepartId());
            if (fromUserId.equals(userDepart.getUserId())){
                continue;
            }
            userDatapermUser.setDataScope(userDataPermUserDTO.getDataScope());
            userDatapermUser.setId(null);
            userDatapermUser.setType("project");
            userDatapermUser.setRightCode(userDataPermUserDTO.getRightCode());
            addList.add(userDatapermUser);
            // 单个项目授权时 写入
            if (userDataPermUserDTO.getDataScope().intValue()==1){
                for (Long projectId : userDataPermUserDTO.getProjectList()) {
                    UserProjectFromUser userProjectFromUser=new UserProjectFromUser();
                    userProjectFromUser.setFromUserId(SecurityUtils.getJwtUser().getUserId());
                    userProjectFromUser.setUserId(userDepart.getUserId());
                    userProjectFromUser.setDepartId(userDepart.getDepartId());
                    userProjectFromUser.setRightCode(userDataPermUserDTO.getRightCode());
                    userProjectFromUser.setProjectId(projectId);
                    addProList.add(userProjectFromUser);
                    // 删除之前的单个授权
                    userProjectFromUserService.remove(new LambdaQueryWrapper<UserProjectFromUser>()
                            .eq(UserProjectFromUser::getProjectId,projectId)
                            .eq(UserProjectFromUser::getFromUserId,fromUserId)
                            .eq(UserProjectFromUser::getUserId,userDepart.getUserId())
                            .eq(UserProjectFromUser::getDepartId,userDepart.getDepartId())
                    );
                }

            }else{
                // 全部授权
                // 删除之前的单个授权
                    userProjectFromUserService.remove(new LambdaQueryWrapper<UserProjectFromUser>()
//                            .eq(UserProjectFromUser::getProjectId,projectId)
                            .eq(UserProjectFromUser::getFromUserId,fromUserId)
                            .eq(UserProjectFromUser::getUserId,userDepart.getUserId())
                            .eq(UserProjectFromUser::getDepartId,userDepart.getDepartId())
                    );

            }
        }

        // 添加新的授权
        if (addList!=null&&addList.size()>0){
            saveBatch(addList);
        }
        if (addProList!=null&&addProList.size()>0){
            userProjectFromUserService.saveBatch(addProList);
        }

        return Result.ok();
    }
}
