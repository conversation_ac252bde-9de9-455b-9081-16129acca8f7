<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper 
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hzw.ssm.sys.project.dao.ProjectMapper">

	<!-- 根据项目id查询项目信息 -->
	<select id="queryProjectById" parameterType="ProjectEntity"
		resultType="ProjectEntity">
		select p.project_id as projectId,p.project_no as
		projectNo,p.project_name as projectName,p.phone,
		p.AUDITTYPE as
		auditType,
		p.bid_time as
		bidTime,p.bid_address as
		bidAddress,p.APPLY_REASON as
		applyReason,p.tender,p.agent,p.remark,p.bid_duration as
		bidDuration,p.status,p.department,
		decode((select u.user_name from
		ts_user u where u.user_id = p.manager and u.delete_flag =
		0),'',p.manager,
		(select u.user_name from ts_user u where u.user_id =
		p.manager and u.delete_flag = 0)) as manager,
		decode((select
		u.user_name from ts_user u where u.user_id = p.operator and
		u.delete_flag = 0),'',p.operator,
		(select u.user_name from ts_user u
		where u.user_id = p.operator and u.delete_flag = 0)) as operator,
		p.create_user create_id,decimationBatch,p.purpose as purpose
		from
		t_project p
		where p.delete_flag=0 and p.project_id=#{projectId}
	</select>

	<select id="queryProjectByDecimationBatch" parameterType="ProjectEntity"
		resultType="ProjectEntity">
		select p.project_id as projectId,p.project_no as
		projectNo,p.project_name as projectName,p.phone,
		p.bid_time as
		bidTime,p.bid_address as
		bidAddress,p.tender,p.agent,p.remark,p.bid_duration as
		bidDuration,p.status,p.department,
		decode((select u.user_name from
		ts_user u where u.user_id = p.manager and u.delete_flag =
		0),'',p.manager,
		(select u.user_name from ts_user u where u.user_id =
		p.manager and u.delete_flag = 0)) as manager,
		decode((select
		u.user_name from ts_user u where u.user_id = p.operator and
		u.delete_flag = 0),'',p.operator,
		(select u.user_name from ts_user u
		where u.user_id = p.operator and u.delete_flag = 0)) as operator,
		p.create_user create_id,decimationBatch,p.purpose as purpose
		from
		t_project p
		where p.delete_flag=0 and
		p.decimationBatch=#{decimationBatch}
	</select>

	<!-- 根据项目id查询项目信息 -->
	<select id="queryProjectListById" parameterType="ProjectEntity"
		resultType="ProjectEntity">
		select p.project_id as projectId,p.project_no as
		projectNo,p.project_name as projectName,p.phone,
		p.bid_time as
		bidTime,p.APPLY_REASON as applyReason,p.bid_address as
		bidAddress,p.tender,p.agent,p.remark,p.bid_duration as
		bidDuration,p.status,p.department,
		decode((select u.user_name from
		ts_user u where u.user_id = p.manager and u.delete_flag =
		0),'',p.manager,
		(select u.user_name from ts_user u where u.user_id =
		p.manager and u.delete_flag = 0)) as manager,
		decode((select
		u.user_name from ts_user u where u.user_id = p.operator and
		u.delete_flag = 0),'',p.operator,
		(select u.user_name from ts_user u
		where u.user_id = p.operator and u.delete_flag = 0)) as operator,
		p.create_user create_id,
		(select TU.USER_NAME from TS_USER TU WHERE TU.USER_ID = p.create_user)as userName,
		decimationBatch,c.id as conditionId,p.purpose
		as purpose,c.method as method
		from t_project p left join t_condition c
		on p.project_id = c.project_id
		where
		<choose>
			<when test="decimationBatch!=null and decimationBatch!=''">
				p.decimationBatch=#{decimationBatch}
			</when>
			<otherwise>
				p.project_id=#{projectId}
			</otherwise>
		</choose>
		and p.delete_flag=0 ORDER BY p.PROJECT_NO,p.project_id
	</select>

	<!-- 保存项目信息 -->
	<insert id="saveProject" parameterType="ProjectEntity">
		insert into t_project(
		project_id,project_no,project_name,manager,phone,bid_time,
		bid_address,tender,agent,status,delete_flag,remark,create_time,create_user,department,operator,decimationBatch,purpose)
		values(
		#{projectId},#{projectNo},#{projectName},#{manager},#{phone},#{bidTime},
		#{bidAddress},#{tender},#{agent},#{status},0,'${remark}',#{create_time},#{create_id},#{department},#{operator},#{decimationBatch},#{purpose})
	</insert>

	<!-- 保存项目信息(多条) -->
	<insert id="saveProjectList" parameterType="java.util.List">
		insert into
		t_project(project_id,project_no,project_name,manager,phone,bid_time,bid_address,tender,agent,status,delete_flag,remark,create_time,create_user,department,operator,purpose)
		values
		<foreach collection="list" item="item" index="index"
			separator=",">
			(#{projectId},#{projectNo},#{projectName},#{manager},#{phone},#{bidTime},#{bidAddress},
			#{tender},#{agent},#{status},0,'${remark}',#{create_time},#{create_id},#{department},#{operator},#{purpose})
		</foreach>
	</insert>

	<!-- 更新项目信息 -->
	<update id="updateProject" parameterType="ProjectEntity">
		update t_project set
		project_name=#{projectName},manager=#{manager},phone=#{phone},
		bid_time=#{bidTime},
		bid_address=#{bidAddress},tender=#{tender},department=#{department},
		operator=#{operator},
		agent=#{agent},remark=#{remark}
		where
		project_id=#{projectId}
	</update>

	<!-- 更新项目状态 -->
	<update id="updateProjectStatus" parameterType="ProjectEntity">
		update t_project set
		status=#{status},modifytime=SYSDATE,APPLY_REASON=#{applyReason,jdbcType=VARCHAR}
		<if test="auditType!=null and auditType !=''">
			,AUDITTYPE=#{auditType,jdbcType=VARCHAR}
		</if>

		where
		<choose>
			<when test="decimationBatch!=null and decimationBatch!=''">
				decimationBatch=#{decimationBatch}
			</when>
			<otherwise>
				project_id=#{projectId}
			</otherwise>
		</choose>
	</update>

	<!-- 根据条件id查询抽取条件信息 -->
	<select id="queryConditionById" parameterType="ConditionEntity"
		resultType="ConditionEntity">
		select c.id,c.project_id as
		projectId,c.total,c.zone,c.senior_num as
		seniorNum,c.score,c.expert_type as expertType,c.province,c.city,
		c.method,c.expert_type_name as expertTypeName,c.zone_name as
		zoneName,c.local_num as localNum,c.SHOWEXPERT_TYPE as
		showExpertType,c.TEMPLATE_ID as templateId,c.CURRENT_STATUS as
		currentStatus,
		c.SMSCONTENT as smsContent,
		c.VOICESMSCONTENT as
		voiceSmsContent,
		c.IS_RULE as isRule,
		c.START_TIME as startTime,
		c.END_TIME as endTime,
		c.FREQUENCY as frequency,
		c.START_AGE as startAge,
		c.END_AGE as endAge,
		c.CON_COMPANY as con_company,
		c.TITLE_CODE titleCode,
		c.EXPERT_TYPE_NUM expertTypeNum,
		c.e_method eMethod
		from t_condition c
		where
		<choose>
			<when test="projectId!=null and projectId!=''">
				c.project_id=#{projectId}
			</when>
			<otherwise>
				c.id=#{id}
			</otherwise>
		</choose>
		and c.delete_flag=0
	</select>

	<!-- 保存抽取条件 -->
	<insert id="saveCondition" parameterType="ConditionEntity">
		declare
		v_clob clob
		:=#{expertType};
		v_clobs clob := #{expertTypeName};
		
		v_clobss clob
		:=#{expertTypeNum};
		begin
		insert into
		t_condition(id,project_id,total,zone,senior_num,local_num,score,expert_type,method,delete_flag,zone_name,expert_type_name,sourceCode,showExpert_type,TEMPLATE_ID,VOICESMSCONTENT,SMSCONTENT,is_rule,start_time,end_time,frequency,start_age,end_age,con_company,TITLE_CODE,EXPERT_TYPE_NUM,e_method)
		values(#{id},#{projectId},'${total}','${zone}','${seniorNum}','${localNum}','${score}',v_clob,#{method},0,#{zoneName},v_clobs,#{sourceCode,jdbcType=VARCHAR},#{showExpertType},#{templateId,jdbcType=VARCHAR},#{voiceSmsContent,jdbcType=VARCHAR},#{smsContent,jdbcType=VARCHAR},#{isRule,jdbcType=VARCHAR},#{startTime,jdbcType=DATE},#{endTime,jdbcType=DATE},#{frequency,jdbcType=VARCHAR},#{startAge,jdbcType=VARCHAR},#{endAge,jdbcType=VARCHAR},#{con_company,jdbcType=VARCHAR},#{titleCode,jdbcType=VARCHAR},v_clobss,#{eMethod,jdbcType=VARCHAR});
		end;
	</insert>
	<!-- 更新抽取条件 -->
	<update id="updateCondition" parameterType="ConditionEntity">
		update t_condition
		set total=#{total, jdbcType=NUMERIC},zone=#{zone,
		jdbcType=VARCHAR},senior_num=#{seniorNum,
		jdbcType=NUMERIC},local_num=#{localNum, jdbcType=NUMERIC},
		score=#{score, jdbcType=NUMERIC},expert_type=#{expertType,
		jdbcType=VARCHAR},method=#{method, jdbcType=NUMERIC},
		zone_name=#{zoneName,jdbcType=VARCHAR},
		expert_type_name=#{expertTypeName,jdbcType=VARCHAR},
		showExpert_type=#{showExpertType, jdbcType=VARCHAR},
		TEMPLATE_ID=#{templateId, jdbcType=VARCHAR},
		SMSCONTENT=#{smsContent,
		jdbcType=VARCHAR},
		VOICESMSCONTENT=#{voiceSmsContent, jdbcType=VARCHAR}
		,is_rule=#{isRule,jdbcType=VARCHAR},
		start_time=#{startTime,jdbcType=DATE},
		end_time=#{endTime,jdbcType=DATE},
		frequency=#{frequency,jdbcType=VARCHAR},
		start_age=#{startAge,jdbcType=VARCHAR},
		end_age=#{endAge,jdbcType=VARCHAR},
		con_company=#{con_company,jdbcType=VARCHAR},
		TITLE_CODE = #{titleCode,jdbcType=VARCHAR},
		EXPERT_TYPE_NUM = #{expertTypeNum,jdbcType=VARCHAR},
		E_METHOD = #{eMethod,jdbcType=VARCHAR}
		where id=#{id}
	</update>

	<update id="updateConditionMethod" parameterType="ConditionEntity">
		update
		t_condition set METHOD=#{method, jdbcType=NUMERIC} where
		project_id=#{projectId}
	</update>
	<!-- 更新抽取条件 是否二次抽取[指定专家] -->
	<update id="updateTwoCon" parameterType="ConditionEntity">
		update t_condition set
		is_two_con=#{isTwoCon, jdbcType=NUMERIC} where id=#{id}
	</update>

	<!-- 根据抽取条件抽取专家信息 -->
	<select id="queryExpertsByRule" parameterType="ConditionEntity"
		resultMap="expertInfoMap">
		select
		id,user_id,user_name,
		mobilephone,company,
		company_addr,grade
		from
		t_expert_info
		where
		delete_flag=0 and STATUS=3
		<!-- 去除回避专家 -->
		<if test="companyDebarbList !=null and companyDebarbList.size()>0">
			AND COMPANY NOT IN 
		 <foreach item="item" collection="companyDebarbList" separator="," open="(" close=")" index="">
	     	 <if test="item!=null">
	     	 	#{item.company, jdbcType=VARCHAR}
	     	 </if>
	    </foreach>
		</if>
		<if test="extractsAvoidList!=null and extractsAvoidList.size()>0">
			AND 
			<foreach collection="extractsAvoidList" index="index" item="item"
				separator="or">
				MOBILEPHONE  NOT IN ${item}
			</foreach>
		</if>
		<!-- 同一个公司的 -->
		<if test="tender!=null and tender!=''">
			and company not in(${tender})
		</if>
		<!-- 新入库专家一个工作日之后方可抽取 by libb 20150703 -->
		<![CDATA[
			and to_char(audit_time + 1, 'yyyy-MM-dd hh24:mi:ss') < to_char(sysdate, 'yyyy-MM-dd hh24:mi:ss')
		]]>
		<!-- 70岁以上专家不可被抽取 -->
		and to_char(sysdate, 'yyyy' )-to_char(birthday, 'yyyy' ) &lt;= 70
		<!-- 去除上一轮抽取过的专家 -->
		<!-- and user_id not in(select expert_id from t_extract_result where delete_flag=0 
			and condition_id='${id}') -->
		<if test="null != id and '' != id">
			and not exists (select 1 from t_extract_result tt where
			user_id=tt.expert_id and delete_flag = 0 and condition_id = '${id}')
		</if>
		and (1=2
		<if test="province!=null and province!=''">
			or province in('${province}')
		</if>
		<if test="city!=null and city!=''">
			or city in('${city}')
		</if>
		<if test="zone!=null and zone!=''">
			or zone in('${zone}')
		</if>
		)
		<!-- 抽取条件专业类别可选择三级目录 by libb 2015-08-06 -->
		<!-- and user_id in( select distinct em.user_id from t_expert_major em 
			left join t_specialty_info spi on spi.spe_id = em.major left join t_specialty_info 
			si on ( (si.spe_id = spi.spe_parent and si.spe_level = 4) or (si.spe_id = 
			em.major and si.spe_level = 3) ) where (si.spe_level = 3 and ( <foreach collection="expertTypeList" 
			index="index" item="item" separator="or" > si.spe_id in ${item} </foreach> 
			or <foreach collection="expertTypeList" index="index" item="item" separator="or" 
			> si.spe_code in ${item} </foreach> ) ) or (spi.spe_level = 4 and ( <foreach 
			collection="expertTypeList" index="index" item="item" separator="or" > si.spe_id 
			in ${item} </foreach> or <foreach collection="expertTypeList" index="index" 
			item="item" separator="or" > si.spe_code in ${item} </foreach> ) ) ) -->
		AND USER_ID IN (
		SELECT distinct R1.USER_ID FROM (
		SELECT E3.USER_ID AS
		USER_ID
		FROM T_EXPERT_MAJOR E3
		LEFT JOIN T_SPECIALTY_INFO S3 ON
		E3.MAJOR=S3.SPE_ID
		WHERE (
		(
		(
		<foreach collection="expertTypeList" index="index" item="item"
			separator="or">
			E3.MAJOR IN ${item}
		</foreach>
		)
		OR (
		<foreach collection="expertTypeList" index="index" item="item"
			separator="or">
			S3.SPE_CODE IN ${item}
		</foreach>
		)
		) AND S3.SPE_LEVEL=3
		)
		
		UNION
		SELECT E4.USER_ID AS USER_ID
		FROM
		T_EXPERT_MAJOR E4
		LEFT JOIN T_SPECIALTY_INFO S4 ON E4.MAJOR=S4.SPE_ID
		WHERE (
		(
		(
		<foreach collection="expertTypeList" index="index" item="item"
			separator="or">
			E4.MAJOR IN ${item}
		</foreach>
		)
		OR (
		<foreach collection="expertTypeList" index="index" item="item"
			separator="or">
			S4.SPE_CODE IN ${item}
		</foreach>
		)
		) AND S4.SPE_LEVEL=4
		)
		UNION
		SELECT E2.USER_ID AS USER_ID
		FROM
		T_EXPERT_MAJOR E2
		LEFT JOIN T_SPECIALTY_INFO S2 ON E2.MAJOR=S2.SPE_ID
		WHERE (
		(
		(
		<foreach collection="expertTypeList" index="index" item="item"
			separator="or">
			E2.MAJOR IN ${item}
		</foreach>
		)
		OR (
		<foreach collection="expertTypeList" index="index" item="item"
			separator="or">
			S2.SPE_CODE IN ${item}
		</foreach>
		)
		) AND S2.SPE_LEVEL=2
		)
		
		) R1
		)




		<if test="grade!=null">
			and grade=#{grade}
		</if>

		order by id asc
	</select>

	<!-- 根据抽取条件抽取专家信息 -->
	<select id="copyQueryExpertsByRule" parameterType="ConditionEntity"
		resultMap="expertInfoMap">
		select id,user_id,user_name,mobilephone,company,company_addr,grade
		from t_expert_info
		where delete_flag=0 and STATUS=3
		<if test="tender!=null and tender!=''">
			and company not in(${tender})
		</if>
		<!-- 新入库专家一个工作日之后方可抽取 by libb 20150703 -->
		<![CDATA[
			and to_char(audit_time + 1, 'yyyy-MM-dd hh24:mi:ss') < to_char(sysdate, 'yyyy-MM-dd hh24:mi:ss')
		]]>
		<!-- 70岁以上专家不可被抽取 -->
		and to_char(sysdate, 'yyyy' )-to_char(birthday, 'yyyy' ) &lt;= 70
		<!-- 去除上一轮抽取过的专家 -->
		<!-- and user_id not in(select expert_id from t_extract_result where delete_flag=0 
			and condition_id='${id}') -->
		and not exists (select 1 from t_extract_result tt where
		user_id=tt.expert_id and delete_flag = 0 and condition_id = '${id}')
		and (1=2
		<if test="province!=null and province!=''">
			or province in('${province}')
		</if>
		<if test="city!=null and city!=''">
			or city in('${city}')
		</if>
		<if test="zone!=null and zone!=''">
			or zone in('${zone}')
		</if>
		)
		<!-- 抽取条件专业类别可选择三级目录 by libb 2015-08-06 -->
		<!-- and user_id in( select distinct em.user_id from t_expert_major em 
			left join t_specialty_info spi on spi.spe_id = em.major left join t_specialty_info 
			si on ( (si.spe_id = spi.spe_parent and si.spe_level = 4) or (si.spe_id = 
			em.major and si.spe_level = 3) ) where (si.spe_level = 3 and ( <foreach collection="expertTypeList" 
			index="index" item="item" separator="or" > si.spe_id in ${item} </foreach> 
			or <foreach collection="expertTypeList" index="index" item="item" separator="or" 
			> si.spe_code in ${item} </foreach> ) ) or (spi.spe_level = 4 and ( <foreach 
			collection="expertTypeList" index="index" item="item" separator="or" > si.spe_id 
			in ${item} </foreach> or <foreach collection="expertTypeList" index="index" 
			item="item" separator="or" > si.spe_code in ${item} </foreach> ) ) ) -->
		AND USER_ID IN (
		SELECT distinct R1.USER_ID FROM (
		SELECT E3.USER_ID AS
		USER_ID
		FROM T_EXPERT_MAJOR E3
		LEFT JOIN T_SPECIALTY_INFO S3 ON
		E3.MAJOR=S3.SPE_ID
		WHERE (
		(
		(
		<foreach collection="expertTypeList" index="index" item="item"
			separator="or">
			E3.MAJOR IN ${item}
		</foreach>
		)
		OR (
		<foreach collection="expertTypeList" index="index" item="item"
			separator="or">
			S3.SPE_CODE IN ${item}
		</foreach>
		)
		) AND S3.SPE_LEVEL=3
		)
		UNION
		SELECT E4.USER_ID AS USER_ID
		FROM
		T_EXPERT_MAJOR E4
		LEFT JOIN T_SPECIALTY_INFO S4 ON E4.MAJOR=S4.SPE_ID
		WHERE (
		(
		(
		<foreach collection="expertTypeList" index="index" item="item"
			separator="or">
			E4.MAJOR IN ${item}
		</foreach>
		)
		OR (
		<foreach collection="expertTypeList" index="index" item="item"
			separator="or">
			S4.SPE_CODE IN ${item}
		</foreach>
		)
		) AND S4.SPE_LEVEL=4
		)
		
		UNION
		SELECT E2.USER_ID AS USER_ID
		FROM
		T_EXPERT_MAJOR E2
		LEFT JOIN T_SPECIALTY_INFO S2 ON E2.MAJOR=S2.SPE_ID
		WHERE (
		(
		(
		<foreach collection="expertTypeList" index="index" item="item"
			separator="or">
			E2.MAJOR IN ${item}
		</foreach>
		)
		OR (
		<foreach collection="expertTypeList" index="index" item="item"
			separator="or">
			S2.SPE_CODE IN ${item}
		</foreach>
		)
		) AND S2.SPE_LEVEL=2
		)
		
		) R1
		)
		<if test="grade!=null">
			and grade=#{grade}
		</if>

		order by id asc
	</select>

	
	<select id="judgeExpertIsAtendProject" parameterType="ResultEntity" resultType="java.lang.Integer">
	SELECT count(1) FROM T_EXTRACT_RESULT T 
	LEFT JOIN T_PROJECT TP ON T.DECIMATIONBATCH = TP.DECIMATIONBATCH
	WHERE T.JOIN_STATUS=0 
	<![CDATA[
	AND T.DECIMATIONBATCH <> #{decimationBatch}
	]]>
	AND T.EXPERT_ID=#{userId}
	AND TO_CHAR(TP.BID_TIME ,'yyyy-MM-dd')=TO_CHAR(#{bidTime},'yyyy-MM-dd')
	</select>
	<!-- 根据抽取条件查询符合条件的专家人数 -->
	<select id="queryExpertCountByRule" parameterType="ConditionEntity"
		resultType="java.lang.Integer">
		select count(id) from t_expert_info where delete_flag=0 and STATUS=3
		<if test="tender!=null and tender!=''">
			and company not in(${tender})
		</if>
		<!-- 新入库专家一个工作日之后方可抽取 by libb 20150703 -->
			<![CDATA[
				and to_char(audit_time + 1, 'yyyy-MM-dd hh24:mi:ss') < to_char(sysdate, 'yyyy-MM-dd hh24:mi:ss')
			]]>
		<!-- 70岁以上专家不可被抽取 -->
		and to_char(sysdate, 'yyyy' )-to_char(birthday, 'yyyy' ) &lt;= 70
		<if test="null != id and '' != id">
			and not exists (select 1 from t_extract_result tt where
			user_id=tt.expert_id and delete_flag = 0 and condition_id = '${id}')
		</if>
		and (1=2
		<if test="province!=null and province!=''">
			or province in('${province}')
		</if>
		<if test="city!=null and city!=''">
			or city in('${city}')
		</if>
		<if test="zone!=null and zone!=''">
			or zone in('${zone}')
		</if>
		)
		<!-- 抽取条件专业类别可选择三、四级目录 by libb 2015-08-06 -->
		<!-- and user_id in( select user_id from ( select em.user_id, Row_Number() 
			OVER (partition by em.user_id ORDER BY em.user_id desc) RN from t_expert_major 
			em left join t_specialty_info spi on spi.spe_id = em.major left join t_specialty_info 
			si on ((si.spe_id = spi.spe_parent and si.spe_level = 4) or (si.spe_id = 
			em.major and si.spe_level = 3)) where (si.spe_level = 3 and (si.spe_id in 
			(select CONTENT from tmp_MAJOR_id WHERE ID=#{decimationBatch}) or si.spe_code 
			in (select CONTENT from tmp_MAJOR_id WHERE ID=#{decimationBatch}) ) ) or 
			(spi.spe_level = 4 and (si.spe_id in (select CONTENT from tmp_MAJOR_id WHERE 
			ID=#{decimationBatch}) or si.spe_code in (select CONTENT from tmp_MAJOR_id 
			WHERE ID=#{decimationBatch}) ) ) )where rn=1) -->
		AND USER_ID IN (
		SELECT distinct R1.USER_ID FROM (
		SELECT E3.USER_ID AS
		USER_ID
		FROM T_EXPERT_MAJOR E3
		LEFT JOIN T_SPECIALTY_INFO S3 ON
		E3.MAJOR=S3.SPE_ID
		WHERE (
		(
		(
		<foreach collection="expertTypeList" index="index" item="item"
			separator="or">
			E3.MAJOR IN ${item}
		</foreach>
		)
		OR (
		<foreach collection="expertTypeList" index="index" item="item"
			separator="or">
			S3.SPE_CODE IN ${item}
		</foreach>
		)
		) AND S3.SPE_LEVEL=3
		)
		UNION
		SELECT E4.USER_ID AS USER_ID
		FROM
		T_EXPERT_MAJOR E4
		LEFT JOIN T_SPECIALTY_INFO S4 ON E4.MAJOR=S4.SPE_ID
		WHERE (
		(
		(
		<foreach collection="expertTypeList" index="index" item="item"
			separator="or">
			E4.MAJOR IN ${item}
		</foreach>
		)
		OR (
		<foreach collection="expertTypeList" index="index" item="item"
			separator="or">
			S4.SPE_CODE IN ${item}
		</foreach>
		)
		) AND S4.SPE_LEVEL=4
		)
		
		UNION
		SELECT E2.USER_ID AS USER_ID
		FROM
		T_EXPERT_MAJOR E2
		LEFT JOIN T_SPECIALTY_INFO S2 ON E2.MAJOR=S2.SPE_ID
		WHERE (
		(
		(
		<foreach collection="expertTypeList" index="index" item="item"
			separator="or">
			E2.MAJOR IN ${item}
		</foreach>
		)
		OR (
		<foreach collection="expertTypeList" index="index" item="item"
			separator="or">
			S2.SPE_CODE IN ${item}
		</foreach>
		)
		) AND S2.SPE_LEVEL=2
		)
		
		) R1
		)
		<!-- select distinct em.user_id from t_expert_major em left join t_specialty_info 
			spi on spi.spe_id = em.major left join t_specialty_info si on ( (si.spe_id 
			= spi.spe_parent and si.spe_level = 4) or (si.spe_id = em.major and si.spe_level 
			= 3) ) where (si.spe_level = 3 and ( <foreach collection="expertTypeList" 
			index="index" item="item" separator="or" > si.spe_id in ${item} </foreach> 
			or <foreach collection="expertTypeList" index="index" item="item" separator="or" 
			> si.spe_code in ${item} </foreach> ) ) or (spi.spe_level = 4 and ( <foreach 
			collection="expertTypeList" index="index" item="item" separator="or" > si.spe_id 
			in ${item} </foreach> or <foreach collection="expertTypeList" index="index" 
			item="item" separator="or" > si.spe_code in ${item} </foreach> ) ) ) -->
		<if test="grade!=null">
			and grade=#{grade}
		</if>
	</select>


	<!-- 根据抽取条件查询符合条件的专家人数 -->
	<select id="copyQueryExpertCountByRule" parameterType="ConditionEntity"
		resultType="java.lang.Integer">
		select count(id) from t_expert_info where delete_flag=0 and STATUS=3
		<if test="tender!=null and tender!=''">
			and company not in(${tender})
		</if>
		<!-- 新入库专家一个工作日之后方可抽取 by libb 20150703 -->
			<![CDATA[
				and to_char(audit_time + 1, 'yyyy-MM-dd hh24:mi:ss') < to_char(sysdate, 'yyyy-MM-dd hh24:mi:ss')
			]]>
		<!-- 70岁以上专家不可被抽取 -->
		and to_char(sysdate, 'yyyy' )-to_char(birthday, 'yyyy' ) &lt;= 70
		<if test="null != id and '' != id">
			and not exists (select 1 from t_extract_result tt where
			user_id=tt.expert_id and delete_flag = 0 and condition_id = '${id}')
		</if>
		and (1=2
		<if test="province!=null and province!=''">
			or province in('${province}')
		</if>
		<if test="city!=null and city!=''">
			or city in('${city}')
		</if>
		<if test="zone!=null and zone!=''">
			or zone in('${zone}')
		</if>
		)
		<!-- 抽取条件专业类别可选择三、四级目录 by libb 2015-08-06 -->
		and user_id in(
		select user_id from (
		select em.user_id, Row_Number()
		OVER (partition by em.user_id ORDER BY
		em.user_id desc) RN from
		t_expert_major em left join t_specialty_info
		spi on spi.spe_id =
		em.major left join t_specialty_info si on
		((si.spe_id = spi.spe_parent
		and si.spe_level = 4) or (si.spe_id =
		em.major and si.spe_level = 3))
		where (si.spe_level = 3 and (si.spe_id in ('${expertType}') or
		si.spe_code
		in ('${expertType}'))) or (spi.spe_level = 4 and (si.spe_id
		in
		('${expertType}') or si.spe_code in ('${expertType}'))))where rn=1)
		<if test="grade!=null">
			and grade=#{grade}
		</if>
	</select>

	<!-- 查询专家评标专业 -->
	<select id="queryExpertMajors" resultType="ExpertMajorEntity"
		parameterType="String">
		select m.year,i.spe_name as major
		from t_expert_major m
		left join t_specialty_info i
		on m.major=i.spe_id
		where
		user_id=#{user_id} and delete_flag=0 and major is not null
		order by id
		asc
	</select>

	<!-- 保存抽取专家信息 -->
	<insert id="saveExtractedExpert" parameterType="List">
		insert into
		t_extract_result(id,condition_id,expert_id,sort,extract_time,join_status,delete_flag,DECIMATIONBATCH,EXPERT_WAY,USERNAME,MAJORSELECTION)
		<foreach collection="list" item="re" separator="union all">
			select
			#{re.id},#{re.conditionId},#{re.userId},${re.sort},#{re.extractTime},#{re.joinStatus},0,#{re.decimationBatch},#{re.expertWay,jdbcType=NUMERIC},#{re.userName,jdbcType=VARCHAR},#{re.majorselection,jdbcType=VARCHAR}
			from dual
		</foreach>
	</insert>

	<!-- 更新抽取专家信息 -->
	<update id="updateExtractedExpert" parameterType="ResultEntity">
		update
		t_extract_result set
		join_status=#{joinStatus},reason='${reason}',qt_reason=#{qt_reason,
		jdbcType=VARCHAR},call_time=#{callTime}
		where expert_id=#{userId} and
		condition_id=#{conditionId}
	</update>

	<!-- 更新抽取专家违规记录 -->
	<update id="updateExtractExIllegal" parameterType="ResultEntity">
		UPDATE
		t_extract_result
		<set>
			<if test="isillegal != null">
				ISILLEGAL=#{isillegal},
			</if>
			<if test="illegal_file_path != null">
				ILLEGAL_FILE_PATH=#{illegal_file_path},
			</if>
			<if test="illegal_file_name != null">
				ILLEGAL_FILE_NAME=#{illegal_file_name},
			</if>
			<if test="illegal_detail != null">
				ILLEGAL_DETAIL=#{illegal_detail},
			</if>
			<if test="is_appraise != null">
				IS_APPRAISE=#{is_appraise},
			</if>
			<if test="appraise_time != null">
				APPRAISE_TIME=#{appraise_time},
			</if>
		</set>
		WHERE
		ID=#{id}
	</update>

	<!-- 批量更新抽取专家通知结果 -->
	<update id="batchUpdateExtractedExpert" parameterType="List">
		<foreach collection="list" item="re" open="begin" close=";end;"
			separator=";">
			update t_extract_result set message_no=#{re.messageNo,
			jdbcType=VARCHAR},call_time=#{re.callTime},
			join_status=#{re.joinStatus}
			<if test="0 == re.joinStatus">
				, reason='', qt_reason=''
			</if>
			<if test="re.joinStatus != null and re.joinStatus != 0">
				, reason=#{re.reason, jdbcType=VARCHAR},
				qt_reason=#{re.qt_reason, jdbcType=VARCHAR}
			</if>
			<if test="re.recieveTime!=null">
				,recieve_time=#{re.recieveTime}
			</if>
			where expert_id=#{re.userId}
			<choose>
				<when test="re.decimationBatch !=null and re.decimationBatch !=''">
					AND DECIMATIONBATCH = #{re.decimationBatch}
				</when>
				<otherwise>
					and condition_id=#{re.conditionId}
				</otherwise>
			</choose>

			<!-- 是否参标，修改了，才更新，或者首次 -->
			<!--  and (JOIN_STATUS != #{re.joinStatus} OR reason!=#{re.reason,
			jdbcType=VARCHAR} OR qt_reason!=#{re.qt_reason, jdbcType=VARCHAR} or
			JOIN_STATUS is null
			<if test="null != re.reason and '' != re.reason">
				OR REASON IS NULL
			</if>
			<if test="null != re.qt_reason and '' != re.qt_reason">
				OR qt_reason IS NULL
			</if>
			)-->
		</foreach>
	</update>

	<!-- 查询抽取结果的最大批次 -->
	<select id="queryResultSort" resultType="Long" parameterType="ConditionEntity">
		select * from(select sort from t_extract_result where
		condition_id=#{id} order by extract_time desc)
		where rownum=1
	</select>

	<!-- 根据id查询专家信息 -->
	<select id="queryExpertById" resultMap="expertInfoMap"
		parameterType="String">
		select * from t_expert_info where user_id=#{userId}
	</select>

	<!-- 查询已抽取抽取结果 -->
	<select id="queryExtractedExperts" resultMap="ResultMap"
		parameterType="ConditionEntity">
		select r.condition_id as conditionId,r.expert_id as
		userId,r.sort,r.reason,r.call_time as callTime,r.join_status as
		joinStatus,r.qt_reason
		from t_extract_result r inner join t_expert_info
		e on r.expert_id=e.user_id
		where r.delete_flag=0
		and
		r.condition_id=#{id}
		<if test="join_status!=null">
			and r.join_status = #{join_status}
		</if>
		order by r.id asc
	</select>
	<!-- 删除已存在的相同条件id的抽取结果 -->
	<delete id="deleteResultInfo" parameterType="ConditionEntity">
		delete from
		t_extract_result where condition_id=#{id}
	</delete>
	<!-- 根据项目id查询项目信息 -->
	<select id="queryProjectInfoById" resultType="ProjectEntity"
		parameterType="String">
		select project_id as projectId,project_no as projectNo
		,project_name as projectName
		,bid_time as bidTime
		from t_project where
		project_id=#{projectId}
	</select>
	<!-- 根据条件id查询同意参加的抽取结果 -->
	<select id="queryAgreeFromResult" parameterType="ConditionEntity"
		resultType="ResultEntity">
		select expert_id as userId,join_status as joinStatus from
		t_extract_result where condition_id=#{id} and join_status=0 and
		delete_flag=0
	</select>

	<!-- 查询已同意参标的国家级（或地方级）专家个数 -->
	<select id="queryAgreeExpertCount" parameterType="ConditionEntity"
		resultType="Integer">
		select count(r.expert_id) count from t_extract_result r
		left join
		t_expert_info i on r.expert_id = i.user_id
		where r.condition_id = #{id}
		and r.join_status=0 and r.delete_flag=0
		<if test="null != grade and '' != grade">
			and i.grade = #{grade}
		</if>
	</select>

	<!-- 保存申请说明 -->
	<insert id="saveApplyInfo" parameterType="ApplyRecordEntity">
		insert into
		t_apply_record(id,project_id,type,content,apply_time,delete_flag)
		values(#{id},#{projectId},#{type},#{content},#{create_time},0)
	</insert>
	<!-- 删除已存在指定抽取结果 -->
	<delete id="deletePointResult" parameterType="ResultEntity">
		delete from
		t_extract_result where condition_id=#{conditionId} and
		expert_id=#{userId}
	</delete>
	<!-- 查询指定专家 根据前台查询条件 -->
	<select id="queryPagePointExperts" parameterType="ConditionEntity"
		resultMap="expertInfoMap">
		select * from t_expert_info
		where delete_flag=0 and status=3
		<if test="expert.user_name!=null and expert.user_name!=''">
			and user_name like '%${expert.user_name}%'
		</if>
		<if test="expert.mobilephone!=null and expert.mobilephone!=''">
			and mobilephone like '%${expert.mobilephone}%'
		</if>
		<if test="expert.spe_id!=null and expert.spe_id!=''">
			and user_id in (select tm.user_id from t_expert_major tm
			where tm.major=#{expert.spe_id})
		</if>
		<if test="expert.province!=null and expert.province!=''">
			and province like '%${expert.province}%'
		</if>
		<if test="expert.city!=null and expert.city!=''">
			and city like '%${expert.city}%'
		</if>
		<if test="null != expert.company and '' != expert.company">
			and company like '%${expert.company}%'
		</if>
		<if test="null != expert.id_no and '' != expert.id_no">
			and ID_NO = #{expert.id_no}
		</if>
		<!-- 剔除当前抽取项目的开标时间为时间点的前后n天的项目的已抽取专家 and user_id not in(select distinct 
			r.expert_id from t_extract_result r inner join t_condition c on r.condition_id 
			= c.id inner join t_project p on c.project_id = p.project_id where r.delete_flag 
			= 0 and c.delete_flag = 0 and p.delete_flag = 0 and p.bid_time between #{startBidTime} 
			and #{endBidTime}) -->
	</select>




	<!-- 根据手机号查询所有抽取结果 -->
	<select id="queryExtracedResultByPhone" parameterType="ResultEntity"
		resultType="ResultEntity">
		select condition_id as conditionId,e.expert_id as userId,
		call_time as callTime,message_no as messageNo,i.mobilephone as phone
		from t_extract_result e inner join t_expert_info i on
		e.expert_id=i.user_id
		where e.message_no is not null and i.mobilephone
		in (${phone}) and
		e.delete_flag=0 and join_status=3 and call_time
		between
			to_date(to_char(sysdate,'yyyy-mm-dd') || ' 00:00:01','yyyy-mm-dd hh24:mi:ss') 
		and
			to_date(to_char(sysdate,'yyyy-mm-dd') || ' 23:59:59','yyyy-mm-dd hh24:mi:ss')
	</select>
	<!-- 查询所有项目负责人 -->
	<select id="queryProjectManager" resultType="UserEntity"
		parameterType="UserEntity">
		select * from ts_user t where t.role_id in ('20141028140549978379',
		'20150122103034522026') and delete_flag=0
		<if test="null != department and '' != department">
			AND t.department =#{department}
		</if>
	</select>

	<!-- 根据角色信息查询用户信息 -->
	<select id="queryUserByRole" resultType="UserEntity"
		parameterType="UserEntity">
		select u.user_id, u.user_name from ts_user u left join
		ts_role r on u.role_id = r.role_id where
		r.role_name = #{role_name} and
		u.delete_flag = 0 and r.delete_flag = 0
	</select>

	<!-- 根据项目创建人id获得项目信息 -->
	<select id="queryPageProjectInitList" resultType="ProjectEntity"
		parameterType="ProjectEntity">
		select
		projectId,projectNo,projectName,phone,bidTime,bidDuration,bidAddress,purpose,tender,agent,
		remark,status,method,conditionId,manager,DECIMATIONBATCH,ishandle,ishandles,HANDLEFILE handleFile,create_time,
		examineName,total,extract_times as queryCount from(
		select
		p.project_id as projectId,p.project_no as projectNo,p.project_name as
		projectName,p.phone,p.bid_time as bidTime,p.bid_duration as
		bidDuration,p.bid_address as bidAddress,p.purpose as purpose,
		p.tender,p.agent,p.remark,p.status,c.method as method,c.id as
		conditionId,
		(case when  ti.ishandle='00' then '0'
		when ti.ishandle='30' then '0 '
		when ti.ishandle='50' then '0 '
		ELSE '99' end)ishandles,
		ti.ishandle,
		p.create_time,
		c.total,
		ti.HANDLEFILE ,
		ti.extract_times,
       	examineName,
		decode((select u.user_name from ts_user u where u.user_id
		= p.manager and
		u.delete_flag = 0),'',p.manager,
		(select u.user_name
		from ts_user u where u.user_id = p.manager and
		u.delete_flag = 0)) as
		manager,p.DECIMATIONBATCH,
		Row_Number() OVER (partition by
		p.DECIMATIONBATCH ORDER BY p.project_no
		asc,p.project_id) RN
		from
		t_project p
		left join t_condition c on p.project_id = c.project_id
		 left join   t_illegal_extractioninfo ti
          on  p.decimationbatch =ti.decimationbatch
        left join 
        (select t.condition_id,t.sort examineName ,t.decimationbatch from t_extract_result t   order by  t.sort desc ,condition_id) tr 
         on c.id=tr.condition_id and tr.decimationbatch= p.decimationbatch
		where p.delete_flag = 0 and p.create_user=#{create_id}
		AND c.METHOD !=3
		<if test="null != projectName and '' != projectName">
			and p.project_name like '%${projectName}%'
		</if>
		<if test="null != projectNo and '' != projectNo">
			and p.project_no like '%${projectNo}%'
		</if>
		<if test="null != bidStartTime and '' != bidStartTime">
			and p.bid_time >= #{bidStartTime}
		</if>
		<if test="null != bidEndTime and '' != bidEndTime">
			and p.bid_time &lt;= #{bidEndTime}
		</if>
		<if test="null != status and '' != status">
			and p.status = #{status}
		</if>
		<if test="null != status_ and '' != status_">
			and p.status in (${status_})
		</if>
		<if test="null != ishandle and '' != ishandle">
			and ti.ishandle = #{ishandle}
		</if>
		order by create_time desc
		) r1 where r1.rn=1
		order by ishandles,create_time desc,projectNo
	</select>

	<!-- 专家信息map -->
	<resultMap type="ExpertInfoEntity" id="expertInfoMap">
		<result column="user_id" property="user_id" />
		<collection property="majorList" column="user_id" select="queryExpertMajors" />
	</resultMap>

	<!-- 抽取结果map -->
	<resultMap type="ResultEntity" id="ResultMap">
		<result column="userId" property="userId" />
		<association property="expert" column="userId" select="queryExpertById" />
	</resultMap>

	<!-- 项目列表信息 -->
	<select id="queryPageProjectList" parameterType="ProjectEntity"
		resultType="ProjectEntity">
		select
		projectId,projectNo,projectName,phone,bidTime,bidDuration,bidAddress,tender,agent,purpose
		remark,status,method,conditionId,manager,r1.DECIMATIONBATCH,appNum,createUser,create_id,isTwoCon,auditType,noNotifyNum
		from(
		select /*+ index_desc(p) */
		p.project_id as projectId,p.project_no
		as projectNo,p.project_name as
		projectName,p.phone,p.bid_time as
		bidTime,p.bid_duration as
		bidDuration,p.bid_address as bidAddress,
		p.tender,p.agent,p.purpose as purpose,p.remark,p.status,c.method as
		method,c.id as conditionId,
		c.total appNum,
		decode((select u.user_name
		from ts_user u where u.user_id = p.manager and
		u.delete_flag =
		0),'',p.manager,
		(select u.user_name from ts_user u where u.user_id =
		p.manager and
		u.delete_flag = 0)) as manager,
		decode((select u.user_name
		from ts_user u where u.user_id = p.create_user and
		u.delete_flag =
		0),'',p.manager,
		(select u.user_name from ts_user u where u.user_id =
		p.create_user and
		u.delete_flag = 0)) as createUser,
		p.create_user
		create_id,c.is_two_con as isTwoCon,p.decimationbatch ,
		Row_Number()
		OVER (partition by DECIMATIONBATCH ORDER BY p.project_no
		asc,p.project_id) RN,p.AUDITTYPE as auditType
		--,(SELECT COUNT(1) FROM T_EXTRACT_RESULT TER WHERE p.decimationbatch = TER.decimationbatch AND TER.JOIN_STATUS =2 GROUP BY TER.decimationbatch) AS noNotifyNum
		from t_project p
		left join
		t_condition c on p.project_id = c.project_id
		where p.delete_flag = 0
		<!-- and (SYSDATE-3<![CDATA[ < ]]> p.bid_time or SYSDATE<![CDATA[ < ]]> 
			p.bid_time) -->
		<!-- <if test="null != createUser and '' != createUser"> and p.create_user 
			= #{createUser} </if> -->
		<if test="null != projectNo and '' != projectNo">
			and p.project_no like '%${projectNo}%'
		</if>
		<if test="null != projectName and '' != projectName">
			and p.project_name like '%${projectName}%'
		</if>
		<if test="null != bidStartTime and '' != bidStartTime">
			and p.bid_time >= #{bidStartTime}
		</if>
		<if test="null != bidEndTime and '' != bidEndTime">
			and p.bid_time &lt;= #{bidEndTime}
		</if>
		<if test="null != status and '' != status">
			and p.status = #{status}
		</if>
		<if test="null != status_ and '' != status_">
			and p.status in (${status_})
		</if>
		<if test="null != createUsers and '' != createUsers">
			and p.create_user in (${createUsers})
		</if>
		<if test="null != decimationBatch and '' != decimationBatch">
			and p.decimationBatch = #{decimationBatch}
		</if>
		<choose>
			<!-- 待抽取列表 -->
			<when test="stas == 'wait'">
				<!-- and (SYSDATE-3<![CDATA[ < ]]> p.bid_time or SYSDATE<![CDATA[ < ]]> 
					p.bid_time) -->
				and p.status in(1,2,4,5,6,8,9,91)
			</when>
			<!-- 已抽取列表 -->
			<when test="stas == 'already'">

				and p.status in(3, 10)
			</when>
		</choose>
		<if test="null == status_ or '' == status_">
			order by create_time desc
		</if>
		<if test="null != status_ and '' != status_">
			order by status
		</if>
		) r1
		 left join 
             (SELECT TER.decimationbatch,COUNT(1) AS noNotifyNum
               FROM T_EXTRACT_RESULT TER
              WHERE  TER.JOIN_STATUS = 2
                 AND decimationbatch IS NOT NULL
              GROUP BY TER.decimationbatch) temp on temp.decimationbatch = r1.decimationbatch	
		 where r1.rn=1
	</select>


	<select id="queryPageByErrorProjectList" parameterType="ProjectEntity"
		resultType="ProjectEntity">
		select decimationbatch,project_no projectNo,project_name
		projectName,bid_time bidTime,status,department,manager,project_id
		projectId,id conditionId,method,purpose from (
		select
		distinct(a.decimationbatch),a.project_no,a.project_name,a.bid_time,a.status,u.department,u.user_name
		manager,a.project_id,c.id,c.method,a.purpose as purpose from t_project
		a
		left join t_condition c on a.project_id = c.project_id
		left join
		t_extract_result b on b.condition_id = c.id
		left join ts_user u on
		u.user_id = a.create_user
		where a.status=2
		and b.join_status=2
		and
		ceil(sysdate - a.bid_time)>= 5
		<if test="null != bidStartTime and '' != bidStartTime">
			and a.bid_time >= #{bidStartTime}
		</if>
		<if test="null != bidEndTime and '' != bidEndTime">
			and a.bid_time &lt;= #{bidEndTime}
		</if>
		<if test="null != department and '' != department">
			and u.department like '%${department}%'
		</if>
		<if test="null != projectName and '' != projectName">
			and a.project_name like '%${projectName}%'
		</if>
		)
		order by bid_time desc
	</select>

	<select id="queryErrorProjectList2" parameterType="ProjectEntity"
		resultType="ProjectEntity">
		select decimationbatch,project_no projectNo,project_name
		projectName,bid_time bidTime,status,department,manager,project_id
		projectId,id conditionId,method,purpose from (
		select
		distinct(a.decimationbatch),a.project_no,a.project_name,a.bid_time,a.status,u.department,u.user_name
		manager,a.project_id,c.id,c.method,a.purpose as purpose from t_project
		a
		left join t_condition c on a.project_id = c.project_id
		left join
		t_extract_result b on b.condition_id = c.id
		left join ts_user u on
		u.user_id = a.create_user
		where a.status=2
		and b.join_status=2
		and
		ceil(sysdate - a.bid_time)>= 5
		<if test="null != bidStartTime and '' != bidStartTime">
			and a.bid_time >= #{bidStartTime}
		</if>
		<if test="null != bidEndTime and '' != bidEndTime">
			and a.bid_time &lt;= #{bidEndTime}
		</if>
		<if test="null != department and '' != department">
			and u.department like '%${department}%'
		</if>
		<if test="null != projectName and '' != projectName">
			and a.project_name like '%${projectName}%'
		</if>
		)
		order by bid_time desc
	</select>

	<!-- 根据条件项目id查询抽取条件信息 -->
	<select id="queryConditionList" parameterType="ConditionEntity"
		resultType="ConditionEntity">
		select id,project_id as projectId,total,zone,zone_name as
		zoneName,senior_num as seniorNum,local_num as localNum,score,
		expert_type as expertType,province,city,expert_type_name as
		expertTypeName,method,IS_TWO_CON,SOURCECODE,SHOWEXPERT_TYPE as
		showExpertType,TEMPLATE_ID as templateId,
		SMSCONTENT as smsContent,
		VOICESMSCONTENT as voiceSmsContent,
		IS_RULE as isRule,
		START_TIME as startTime,
		END_TIME as endTime,
		FREQUENCY as frequency,
		START_AGE as startAge,
		END_AGE as endAge,
		CON_COMPANY as con_company,
		TITLE_CODE as titleCode,
		EXPERT_TYPE_NUM as expertTypeNum,
		e_method eMethod
		from t_condition
		where
		project_id=#{projectId} and delete_flag=0
	</select>

	<!-- 查询参评专家信息 -->
	<select id="queryExtractedResult" resultType="ExpertInfoEntity"
		parameterType="ConditionEntity">
		select ei.user_id, CASE er.EXPERT_ID when '0' THEN er.username else ei.user_name end as  user_name, ei.mobilephone, ei.district as aArea,
		ei.company, ei.grade,ei.EXPERT_TYPE as expertType,
		<!--  (select wm_concat(si.spe_name)
		from t_expert_major
		m left join T_SPECIALTY_INFO si on si.spe_id = m.major
		where
		m.delete_flag = 0
		and m.user_id = ei.user_id)-->
		f.bidMajor,er.join_status,er.reason,er.appraise_time,
		er.call_time,er.is_appraise,er.id as
		extractResultId,ei.position,ei.technical_tital,ei.mobilephone,ei.company_phone,
		ei.OLD_ID,
		ei.ID_FILEID,
		ei.ID_TYPE,ei.ID_NO,
		er.qt_reason,
		ttt.TECHNICAL_TITAL_NAME titalName,
		CASE er.sort
		WHEN 0 THEN (select max(err.sort)+1 from t_extract_result err
		where
		err.condition_id = c.id)
		ELSE er.sort END sort_no,er.expert_way as
		expertWay
		from T_EXTRACT_RESULT er
		left join t_expert_info ei on
		er.expert_id =
		ei.user_id and ei.delete_flag = 0
		left join t_title_info tti on tti.expert_id = ei.user_id
    	left join t_technical_tital ttt on ttt.code = tti.title_id
		left join t_condition c on c.id =
		er.condition_id
		 left join (
		   select user_id,xmlagg(xmlparse(content i.spe_name||',' wellformed) order by user_id).getclobval() bidMajor	
		  		from  t_expert_major m
		                  left join t_specialty_info i
		                    on m.major = i.spe_id
		                    where m.delete_flag = 0
		                     group by user_id
		   )f  on ei.user_id=f.user_id
		where
		er.delete_flag = 0  and
		c.delete_flag = 0
		and
		c.project_id = #{projectId}
		<if test="null != joinStatus and '' != joinStatus">
			and er.join_status = #{joinStatus}
		</if>
		<if test="null != id and '' != id">
			and c.id = #{id}
		</if>
		<if test="null != isBid">
			and (ei.IS_BID=#{isBid} or ei.IS_BID is null)
		</if>
		<if test="null != num">
			and er.sort = #{num }
		</if>
		<if test="null == orderFlag or '' == orderFlag">
			order by er.id
		</if>
		<if test="null != orderFlag and '' != orderFlag">
			<!-- order by er.sort asc,er.id -->
			order by sort_no asc,er.id
		</if>
	</select>

	<!-- 统计参加的专家人数 -->
	<select id="countNotExtractedResult" resultType="Integer"
		parameterType="ConditionEntity">
		SELECT COUNT(*)
		FROM T_EXTRACT_RESULT T LEFT JOIN t_condition c ON c.id
		= T.condition_id
		WHERE T.delete_flag = 0
		AND C.ID = #{id}
		<if test="join_status==0">
			AND T.join_status = #{join_status}
		</if>
		<!-- 由于交接项目时导致状态丢失 -->
		<if test="join_status==2">
			AND T.join_status in (${join_status},4)
		</if>

	</select>





	<!-- 查询抽取记录信息 -->
	<select id="queryExtractRecordList" parameterType="String"
		resultType="ExtractRecordEntity">
		select * from (
		select
		row_number() over(PARTITION BY c.id
		order by c.id desc) as rid,
		c.id as
		conId,c.project_id,er.sort,c.total,(select count(err.id) from
		t_extract_result err where err.join_status = 0
		and err.sort = er.sort
		and err.condition_id=er.condition_id and err.delete_flag = 0) as join,
		(select count(err.id) from t_extract_result err where err.join_status
		in(1,2,3,4) and err.sort = er.sort
		and err.condition_id=er.condition_id
		and err.delete_flag = 0) as nojoin,er.extract_time as extractTime,
		c.zone_name as zoneName,c.expert_type_name as
		expertTypeName,c.is_two_con as isTwoCon from
		t_condition c left join
		t_extract_result er on er.condition_id = c.id
		where c.delete_flag = 0
		and er.delete_flag = 0 and c.project_id = #{projectId}
		order by case
		when sort = 0 then
		(select max(err.sort) + 1
		from t_extract_result err
		where err.condition_id = c.id)
		else
		sort
		end,
		sort
		) where rid=1
	</select>

	<!-- 查询抽取记录信息 -->
	<select id="copyQueryExtractRecordList" parameterType="String"
		resultType="ExtractRecordEntity">
		select
		row_number() over(PARTITION BY c.id order by c.id
		desc) as rid,
		c.id as
		conId,c.project_id,er.sort,c.total,(select
		count(err.id) from
		t_extract_result err where err.join_status = 0
		and
		err.sort = er.sort
		and err.condition_id=er.condition_id and
		err.delete_flag = 0) as join,
		(select count(err.id) from
		t_extract_result err where err.join_status
		in(1,2,3,4) and err.sort =
		er.sort
		and err.condition_id=er.condition_id
		and err.delete_flag = 0) as
		nojoin,er.extract_time as extractTime,
		c.zone_name as
		zoneName,c.expert_type_name as
		expertTypeName,c.is_two_con as isTwoCon
		from
		t_condition c left join
		t_extract_result er on er.condition_id =
		c.id
		where c.delete_flag = 0
		and er.delete_flag = 0 and c.project_id =
		#{projectId}
		order by case
		when sort = 0 then
		(select max(err.sort) + 1
		from t_extract_result err
		where err.condition_id = c.id)
		else
		sort
		end,
		sort
	</select>


	<!-- 查询抽取记录中的抽取条件 -->
	<select id="queryExtractConditionById" parameterType="ConditionEntity"
		resultType="ConditionEntity">
		select * from (
		select row_number() over(PARTITION BY c.id
		order by c.id desc) as rid, c.id,c.project_id as
		projectId,c.total,c.zone,c.senior_num as
		seniorNum,c.score,c.expert_type_name as expertTypeName,c.zone_name as
		zoneName,
		c.method,c.local_num as localNum,c.is_two_con as isTwoCon ,
		c.SHOWEXPERT_TYPE AS showExpertType
		from t_condition c
		where c.id=#{id}
		and c.delete_flag=0
		) where rid =1
	</select>

	<!--专家参与项目列表查询 -->
	<select id="queryPageExpertJoinProjectList" parameterType="ProjectEntity"
		resultType="ProjectEntity">
		select
		tp.project_id as projectId,
		tp.project_no as projectNo,
		tp.project_name as projectName,
		tp.bid_time as bidTime,
		tp.bid_address
		as bidAddress
		from t_project tp
		left join t_condition tc on
		tp.project_id=tc.project_id
		left join t_extract_result te on
		tc.id=te.condition_id
		where te.delete_flag=0 and te.join_status=0 and
		te.expert_id=#{expertId}
		<if test="projectName!=null and projectName!=''">
			and tp.project_name like '%${projectName}%'
		</if>
		<if test="projectNo!=null and projectNo!=''">
			and tp.project_no like '%${projectNo}%'
		</if>
		<if test="null != bidStartTime and '' != bidStartTime">
			and tp.bid_time >= #{bidStartTime}
		</if>
		<if test="null != bidEndTime and '' != bidEndTime">
			and tp.bid_time &lt;= #{bidEndTime}
		</if>
		order by tp.bid_time desc
	</select>

	<!-- 查询项目申请记录表 -->
	<select id="queryApplyRecord" parameterType="ApplyRecordEntity"
		resultType="ApplyRecordEntity">
		select ar.id,ar.project_id as
		projectId,ar.type,ar.content,max(ar.apply_time) as applyTime from
		t_apply_record ar
		where ar.delete_flag = 0 and ar.project_id =
		#{projectId} and ar.type = #{type}
		group by
		ar.id,ar.project_id,ar.type,ar.content
	</select>

	<!-- 查询项目最新一条申请记录表（由原来的根据项目ID查询改为根据抽取批次号查询） -->
	<select id="queryNewApplyRecord" parameterType="ApplyRecordEntity"
		resultType="ApplyRecordEntity">
		select a.content,a.apply_time
		from t_apply_record a
		where
		a.id = (
		select max(id)
		from t_apply_record
		where delete_flag = 0
		and type
		= #{type}
		and project_id in (
		select project_id from t_project p where
		p.decimationBatch=#{decimationBatch}
		)
		) and a.delete_flag=0
	</select>

	<!-- 向项目审核记录表插入数据 -->
	<insert id="saveProjectAudit" parameterType="ProjectAuditEntity">
		insert into
		t_project_audit
		(id, project_id, reason, status, audit_time ,
		audit_user,AUDIT_TYPE)
		values
		(#{id}, #{project_id},
		#{reason,jdbcType=VARCHAR},
		#{status}, sysdate,
		#{audit_user},#{auditType,jdbcType=VARCHAR})
	</insert>

	<!-- 指定专家申请审核通过向抽取条件表插入数据 -->
	<insert id="saveAuditCondition" parameterType="ConditionEntity">
		insert into
		t_condition(id,project_id,method) values(#{id},#{projectId},#{method})
	</insert>

	<!-- 查询项目最后抽取时间 -->
	<select id="queryCallTime" parameterType="ProjectEntity"
		resultType="ResultEntity">
		select max(call_time) callTime, count(id)
		callCount from
		t_extract_result where condition_id = #{conditionId }
	</select>

	<!-- 查询项目最小抽取时间 -->
	<select id="queryMinExtractTime" parameterType="String"
		resultType="String">
		select to_char(min(extract_time),'yyyy-MM-dd
		hh24:mi:ss')
		extract_time from t_extract_result where condition_id =
		#{conditionId }
	</select>

	<!-- 根据项目ID，获取项目审批信息 -->
	<select id="getProAuditInfoByProId" parameterType="ProjectAuditEntity"
		resultType="ProjectAuditEntity">
		SELECT a.reason, b.user_name as audit_user
		FROM
		t_project_audit a
		left join ts_user b
		on a.audit_user = b.user_id
		where
		a.id in (
		select max(id) as id
		from t_project_audit
		where project_id in (
		select project_id from t_project p where
		p.decimationBatch=#{decimationBatch}
		)) and a.delete_flag=0
	</select>

	<!-- 根据项目ID，获取指定的审批类型审批通过的信息 (语音) -->
	<select id="getProAuditInfoByProIdToVoice" parameterType="ProjectAuditEntity"
		resultType="ProjectAuditEntity">
		SELECT a.reason, b.user_name as audit_user
		FROM
		t_project_audit a
		left join ts_user b
		on a.audit_user = b.user_id
		where
		a.id in (
		select max(id) as id
		from t_project_audit
		where project_id in (
		select project_id from t_project p where
		p.decimationBatch=#{decimationBatch}
		)) and a.delete_flag=0
		AND
		A.STATUS=#{status}
		AND A.AUDIT_TYPE=#{auditType}
	</select>

	<!-- 根据项目ID，获取项目审批列表信息 -->
	<select id="getAuditInfoById" parameterType="ProjectAuditEntity"
		resultType="ProjectAuditEntity">
		SELECT
		a.reason,
		b.user_name as audit_user,
		a.audit_time ,
		a.status,
		a.reason
		FROM t_project_audit a
		left join ts_user b
		on
		a.audit_user = b.user_id
		where a.project_id =#{project_id} and
		a.delete_flag = 0
		order by a.audit_time desc
	</select>



	<!-- 查询专家除参加当前项目外，在开标当天还参加过是否其他项目 -->
	<select id="queryExtractExpertList" parameterType="ProjectEntity"
		resultType="ProjectEntity">
		SELECT a.expert_id expertId, i.user_name expertName,
		p.project_id projectId, p.project_no projectNo, p.project_name
		projectName,p.bid_time as bidTime, p.bid_address bidAddress FROM
		t_extract_result a
		left join t_expert_info i on i.user_id = a.expert_id
		left join t_condition c on c.id = a.condition_id
		left join t_project p
		on p.project_id = c.project_id
		where a.expert_id in (${expertId})
		and
		a.join_status = 0 and a.condition_id != #{conditionId}
		and
		to_char(p.bid_time, 'yyyyMMdd') = (select to_char(bid_time,
		'yyyyMMdd') from t_project where project_id = #{projectId})
		order by
		a.expert_id, p.project_id
	</select>

	<!-- 查询专家参与项目 -->
	<select id="queryPageExpertPro" parameterType="ProjectEntity"
		resultType="ProjectEntity">
		select
		a.project_id as projectId,
		a.project_no as projectNo,
		a.project_name as projectName,
		a.bid_time as bidTime,
		a.bid_address as
		bidAddress,
		a.MANAGER,
		c.id as id,
		a.TENDER,
		b.id as conditionId,
		c.is_appraise as is_appraise
		from t_project a
		left join t_condition b
		on
		a.project_id = b.project_id
		left join t_extract_result c
		on b.id =
		c.condition_id
		where c.id in(${id})
		and a.bid_time &lt; sysdate
		<if test="projectName!=null and projectName!=''">
			and a.project_name like '%${projectName}%'
		</if>
		<if test="projectNo!=null and projectNo!=''">
			and a.project_no like '%${projectNo}%'
		</if>
		<if test="null != bidStartTime and '' != bidStartTime">
			and a.bid_time >= #{bidStartTime}
		</if>
		<if test="null != bidEndTime and '' != bidEndTime">
			and a.bid_time &lt;= #{bidEndTime}
		</if>
		<if test="null != tender and '' != tender">
			and a.TENDER like '%${tender}%'
		</if>
	</select>

	<!-- 查询国招业务平台项目 -->
	<select id="queryPageJSGZProjectList" parameterType="ProjectEntity"
		resultType="ProjectEntity">
		<!-- SELECT P.PROJECT_ID projectId, P.PROJECT_NUM projectNo, P.PROJECT_NAME 
			projectName, TO_CHAR(P.SUBMIT_END_TIME, 'yyyy-MM-dd hh24:mi:ss') bidTime_, 
			U.F_USER_NAME manager, U.F_MOBILE phone, U.F_DEPARTMENT_NAME department FROM 
			JSGZ.T_PROJECT P LEFT JOIN JSGZ.T_INS_USER U ON P.PROJECT_MANAGER = U.F_USER_ID 
			WHERE P.PAUSE_FLAG = 0 AND P.DELETE_FLAG = 0 AND PROJECT_NUM LIKE '%${projectNo 
			}%' -->
		SELECT
		PROJECTID,
		PROJECTNO,
		PROJECTNAME,
		BIDADDRESS,
		bidTime_,
		MANAGER,
		PHONE,
		DEPARTMENT,
		NAME TENDER
		FROM (SELECT P.PROJECT_ID projectId,
		P.PROJECT_NAME projectName,
		FL.OPEN_BID_ADDRESS bidAddress,
		TO_CHAR(FL.SUBMIT_END_TIME, 'yyyy-MM-dd hh24:mi:ss') bidTime_,
		U.F_USER_NAME manager,
		U.F_MOBILE phone,
		U.F_DEPARTMENT_NAME department,
		EU.NAME name,
		(case
		when S.SORT_NO is not null then P.PROJECT_NUM || '/'
		|| S.SORT_NO
		else P.PROJECT_NUM
		end) as PROJECTNO
		FROM
		JSGZTEST.T_PROJECT P
		LEFT JOIN JSGZTEST.T_INS_USER U
		ON
		P.PROJECT_MANAGER = U.F_USER_ID
		LEFT JOIN JSGZTEST.ENTRUST_UNIT_REL
		EUR
		ON P.PROJECT_ID = EUR.PROJECT_ID
		LEFT JOIN JSGZTEST.ENTRUST_UNIT
		EU
		ON EUR.ENTRUST_UNIT_ID = EU.ENTRUST_UNIT_ID
		LEFT JOIN
		JSGZTEST.T_SUB S
		ON S.PROJECT_ID=P.PROJECT_ID
		LEFT JOIN
		JSGZTEST.T_PURCHASE_FILE FL
		ON FL.PROJECT_ID=P.PROJECT_ID and
		((FL.SUB_ID = S.SUB_ID and FL.SUB_ID
		IS NOT NULL) OR (FL.SUB_ID IS
		NULL))
		WHERE P.PAUSE_FLAG = 0
		AND P.DELETE_FLAG = 0
		ORDER BY
		P.CREATE_TIME DESC
		) TT
		where projectNo LIKE '%${projectNo}%'
		<if test="department !=null and department!=''">
			AND DEPARTMENT = #{department}
		</if>
	</select>


	<!-- 根据项目项目编号、开标日期查询，查询项目专家参标记录 -->
	<select id="queryByProject" resultType="ResultByProjectEntity"
		parameterType="ProjectEntity">
		SELECT * FROM (
		SELECT tender, TO_CHAR(bid_time, 'YYYY-MM-DD') AS
		bidTime,
		p.project_no AS projectNo, e.user_name AS userName, e.id_no AS
		idNo
		, E.ID_TYPE AS IDTYPE,ROW_NUMBER() OVER (PARTITION BY e.mobilephone
		ORDER BY
		mobilephone) AS rn,e.mobilephone phone
		FROM t_extract_result r
		INNER
		JOIN
		t_expert_info e ON r.expert_id = e.user_id
		INNER JOIN t_condition c
		ON c.id = r.condition_id
		INNER JOIN t_project p ON p.project_id =
		c.project_id
		WHERE join_status = '0'
		<choose>
			<when test="projectNo !=null and projectNo !=''">
				and p.project_no in ( '${projectNo}' )
			</when>
			<otherwise>
				and p.project_no in
				<foreach collection="projectNoList" index="index" item="item"
					open="(" separator="," close=")">
					#{item}
				</foreach>
			</otherwise>
		</choose>


		<if test=" bidTime_ !=null ">
			and to_char(p.bid_time,'yyyy-mm-dd')=#{bidTime_}
		</if>
		)
		WHERE rn = 1
	</select>
	<update id="restoreDeleteProject" parameterType="ProjectEntity">
		update t_project set delete_flag = 0 where
		<choose>
			<when test="decimationBatch!=null and decimationBatch!=''">
				decimationBatch=#{decimationBatch}
			</when>
			<otherwise>
				project_id = #{projectId}
			</otherwise>
		</choose>
	</update>
	<delete id="clearDeleteProject">
		delete from t_deleterecord where project_id =
		#{projectId}
	</delete>
	<update id="deleteRecordByProjectId" parameterType="ProjectEntity">
		update t_project set delete_flag = 1 where
		<choose>
			<when test="decimationBatch!=null and decimationBatch!=''">
				decimationBatch=#{decimationBatch}
			</when>
			<otherwise>
				project_id = #{projectId}
			</otherwise>
		</choose>


	</update>

	<insert id="addDeleteRecord" parameterType="DeleteRecord">
		insert into
		t_deleterecord (id,project_id,reason)
		values(#{id},#{projectId},#{reason})
	</insert>

	<!-- 根据项目创建人id获得删除项目信息 -->
	<select id="queryPageDeleteProjectInitList" resultType="ProjectEntity"
		parameterType="ProjectEntity">
		select
		projectId,projectNo,projectName,phone,bidTime,bidDuration,bidAddress,tender,agent,remark,status,method,
		conditionId,purpose,manager,DECIMATIONBATCH from(
		select p.project_id
		as projectId,p.project_no as projectNo,p.project_name as
		projectName,p.phone,p.bid_time as bidTime,p.bid_duration as
		bidDuration,p.bid_address as bidAddress,
		p.tender,p.agent,p.remark,p.status,c.method as method,c.id as
		conditionId,p.purpose as purpose,
		decode((select u.user_name from
		ts_user u where u.user_id = p.manager and
		u.delete_flag =
		0),'',p.manager,
		(select u.user_name from ts_user u where u.user_id =
		p.manager and
		u.delete_flag = 0)) as manager,DECIMATIONBATCH,
		Row_Number() OVER (partition by DECIMATIONBATCH ORDER BY p.project_no
		asc,p.project_id) RN
		from t_project p
		left join t_condition c on
		p.project_id = c.project_id
		where p.delete_flag = 1
		<if test="null != create_id and '' != create_id">
			and p.create_user=#{create_id}
		</if>
		<if test="null != projectName and '' != projectName">
			and p.project_name like '%${projectName}%'
		</if>
		<if test="null != projectNo and '' != projectNo">
			and p.project_no like '%${projectNo}%'
		</if>
		<if test="null != bidStartTime and '' != bidStartTime">
			and p.bid_time >= #{bidStartTime}
		</if>
		<if test="null != bidEndTime and '' != bidEndTime">
			and p.bid_time &lt;= #{bidEndTime}
		</if>
		<if test="null != status and '' != status">
			and p.status = #{status}
		</if>
		<if test="null != status_ and '' != status_">
			and p.status in (${status_})
		</if>
		order by p.create_time desc) where rn=1
	</select>
	<!-- 根据项目id查询项目信息 -->
	<select id="queryDeleteProjectById" parameterType="ProjectEntity"
		resultType="ProjectEntity">
		select p.project_id as projectId,p.project_no as
		projectNo,p.project_name as projectName,p.phone,p.bid_time as bidTime,
		p.bid_address as bidAddress,p.tender,p.agent,p.remark,p.bid_duration
		as bidDuration,p.status,p.department,
		decode((select u.user_name from
		ts_user u where u.user_id = p.manager and u.delete_flag =
		0),'',p.manager,
		(select u.user_name from ts_user u where u.user_id =
		p.manager and u.delete_flag = 0)) as manager,
		decode((select
		u.user_name from ts_user u where u.user_id = p.operator and
		u.delete_flag = 0),'',p.operator,
		(select u.user_name from ts_user u
		where u.user_id = p.operator and u.delete_flag = 0)) as operator,
		p.create_user create_id,p.purpose as purpose
		from t_project p
		where
		project_id=#{projectId} and p.delete_flag=1
	</select>

	<!-- 根据项目id查询项目信息 -->
	<select id="queryDeleteProjectByDecimationbatch" parameterType="ProjectEntity"
		resultType="ProjectEntity">
		select p.project_id as projectId,p.project_no as
		projectNo,p.project_name as projectName,p.phone,p.bid_time as bidTime,
		p.bid_address as bidAddress,p.tender,p.agent,p.remark,p.bid_duration
		as bidDuration,p.status,p.department,
		decode((select u.user_name from
		ts_user u where u.user_id = p.manager and u.delete_flag =
		0),'',p.manager,
		(select u.user_name from ts_user u where u.user_id =
		p.manager and u.delete_flag = 0)) as manager,
		decode((select
		u.user_name from ts_user u where u.user_id = p.operator and
		u.delete_flag = 0),'',p.operator,
		(select u.user_name from ts_user u
		where u.user_id = p.operator and u.delete_flag = 0)) as operator,
		p.create_user create_id,p.purpose as purpose
		from t_project p
		where
		decimationBatch=#{decimationBatch} and p.delete_flag=1
	</select>
	<!-- 查询删除原因 -->
	<select id="getDeleteReason" parameterType="DeleteRecord"
		resultType="string">
		select d.reason from t_deleterecord d where d.project_id =
		#{projectId} and d.delete_flag = 0
	</select>
	<!-- 查询删除原因 -->
	<select id="getRestoreDeleteReason" parameterType="DeleteRecord"
		resultType="string">
		select d.restore_reason as restoreReason from
		t_deleterecord d where d.project_id = #{projectId} and d.delete_flag =
		0
	</select>
	<update id="modifyDeleteRecord" parameterType="DeleteRecord">
		update
		t_deleterecord set status = 1 ,restore_reason = #{restoreReason} where
		project_id=#{projectId} and delete_flag = 0
	</update>
	<select id="getDeleteRecordList" resultType="DeleteRecord">
		select
		d.id,d.project_id as projectId,d.reason,d.status, d.restore_reason as
		restoreReason from t_deleterecord d where d.delete_flag = 0
	</select>
	<!-- 项目列表信息 -->
	<select id="queryRestoreDeletePageProjectList" parameterType="ProjectEntity"
		resultType="ProjectEntity">
		select * from (
		select /*+ index_desc(p) */
		p.project_id as
		projectId,p.project_no as projectNo,p.project_name as
		projectName,p.phone,p.bid_time as bidTime,p.bid_duration as
		bidDuration,p.bid_address as bidAddress,p.purpose as purpose,
		p.tender,p.agent,p.remark,p.status,c.method as method,c.id as
		conditionId,
		c.total appNum,
		decode((select u.user_name from ts_user u
		where u.user_id = p.manager and u.delete_flag = 0),'',p.manager,
		(select u.user_name from ts_user u where u.user_id = p.manager and
		u.delete_flag = 0)) as manager,
		decode((select u.user_name from ts_user
		u where u.user_id = p.create_user and
		u.delete_flag = 0),'',p.manager,
		(select u.user_name from ts_user u where u.user_id = p.create_user and
		u.delete_flag = 0)) as createUser,
		p.create_user create_id,c.is_two_con
		as isTwoCon,DECIMATIONBATCH,
		Row_Number() OVER (partition by
		DECIMATIONBATCH ORDER BY p.project_no
		asc,p.project_id) RN
		from
		t_project p
		left join t_condition c on p.project_id = c.project_id
		where p.delete_flag = 1
		<if test="null != projectName and '' != projectName">
			and p.project_name like '%${projectName}%'
		</if>
		<if test="null != bidStartTime and '' != bidStartTime">
			and p.bid_time >= #{bidStartTime}
		</if>
		<if test="null != bidEndTime and '' != bidEndTime">
			and p.bid_time &lt;= #{bidEndTime}
		</if>
		order by p.create_time asc ) where rn = 1
	</select>
	<!-- 根据抽取条件查询符合条件的专家列表 -->
	<select id="extractExpertApply" resultType="com.hzw.ssm.expert.entity.ExpertInfoEntity">
		select
		te.id,te.user_name,te.id_no,te.mobilephone,te.company,te.position from
		t_expert_info te where delete_flag=0 and STATUS=3
		<!-- 新入库专家一个工作日之后方可抽取 by libb 20150703 -->
		<![CDATA[
			and to_char(audit_time + 1, 'yyyy-MM-dd hh24:mi:ss') < to_char(sysdate, 'yyyy-MM-dd hh24:mi:ss')
		]]>
		<!-- 70岁以上专家不可被抽取 -->
		and to_char(sysdate, 'yyyy' )-to_char(birthday, 'yyyy' ) &lt;= 70
		<!-- and exists (select 1 from t_expert_major tt where USER_ID=tt.user_id 
			and major in ('${expertType}')) -->
		<!-- and user_id in(select distinct user_id from t_expert_major where major 
			in('${expertType}')) -->
		<!-- 抽取条件专业类别可选择三、四级目录 by libb 2015-08-06 -->
		and user_id in(select distinct em.user_id from t_expert_major em left
		join t_specialty_info spi on spi.spe_id = em.major left join
		t_specialty_info si on ((si.spe_id = spi.spe_parent and si.spe_level =
		4) or (si.spe_id = em.major and si.spe_level = 3))
		where (si.spe_level
		= 3 and si.spe_name like #{expertType}) or
		(spi.spe_level = 4 and
		spi.spe_name like #{expertType})))
		<if test="grade!=null">
			and grade=#{grade}
		</if>
		<if test="district!=null">
			and district =#{area}
		</if>
	</select>


	<!-- 查询国招业务平台项目 -->
	<select id="queryJSGZProListByCondition" parameterType="ProjectEntity"
		resultType="ProjectEntity">
		SELECT
		PROJECTID,
		PROJECTNO,
		PROJECTNAME,
		BIDADDRESS,
		bidTime_,
		MANAGER,
		PHONE,
		DEPARTMENT,
		NAME TENDER
		FROM (SELECT P.PROJECT_ID
		projectId,
		P.PROJECT_NAME projectName,
		FL.OPEN_BID_ADDRESS bidAddress,
		TO_CHAR(FL.SUBMIT_END_TIME, 'yyyy-MM-dd hh24:mi:ss') bidTime_,
		U.F_USER_NAME manager,
		U.F_MOBILE phone,
		U.F_DEPARTMENT_NAME department,
		EU.NAME name,
		(case
		when S.SORT_NO is not null then P.PROJECT_NUM || '/'
		|| S.SORT_NO
		else P.PROJECT_NUM
		end) as PROJECTNO
		FROM
		JSGZTEST.T_PROJECT P
		LEFT JOIN JSGZTEST.T_INS_USER U
		ON
		P.PROJECT_MANAGER = U.F_USER_ID
		LEFT JOIN JSGZTEST.ENTRUST_UNIT_REL
		EUR
		ON P.PROJECT_ID = EUR.PROJECT_ID
		LEFT JOIN JSGZTEST.ENTRUST_UNIT
		EU
		ON EUR.ENTRUST_UNIT_ID = EU.ENTRUST_UNIT_ID
		LEFT JOIN
		JSGZTEST.T_SUB S
		ON S.PROJECT_ID=P.PROJECT_ID
		LEFT JOIN
		JSGZTEST.T_PURCHASE_FILE FL
		ON FL.PROJECT_ID=P.PROJECT_ID and
		((FL.SUB_ID = S.SUB_ID and FL.SUB_ID
		IS NOT NULL) OR (FL.SUB_ID IS
		NULL))
		WHERE P.PAUSE_FLAG = 0
		AND P.DELETE_FLAG = 0) TT
		where
		DEPARTMENT='#{departMent}'
		and bidTime_ = '#{bidTime}'
		and projectNo
		LIKE '%${projectNo }%'
	</select>

	<!-- 根据抽取批次查询抽取条件ID -->
	<select id="queryConditionIdByBatch" parameterType="ProjectEntity"
		resultType="ProjectEntity">
		select a.id conditionId,b.project_id projectId,b.BID_TIME
		as bidTime from
		t_condition a
		left join t_project b on a.project_id =
		b.project_id
		where
		b.decimationbatch=#{decimationBatch}
	</select>
	<!-- 根据项目ID查询抽取条件ID -->
	<select id="queryConditionIdByProjectId" parameterType="ProjectEntity"
		resultType="ProjectEntity">
		select a.id conditionId,b.project_id projectId from
		t_condition a
		left join t_project b on a.project_id = b.project_id
		where
		b.project_id=#{projectId}
	</select>


	<!-- 更新抽取项目状态（特定情境下用该方法） -->
	<update id="updateChouProjectStatus" parameterType="ProjectEntity">
		update
		t_project set status=#{status},modifytime=SYSDATE
		where
		project_id=#{projectId}
	</update>

	<!-- 根据流水号查询专家信息 -->
	<select id="queryExpertInfoByBatch" parameterType="ConditionEntity"
		resultType="ExpertInfoEntity">
		select e.user_id,e.user_name,e.mobilephone
		from
		t_extract_result r inner join t_expert_info e on
		r.expert_id=e.user_id
		where r.condition_id in (#{id}) and
		r.delete_flag=0
	</select>

	
		<!-- 根据流水号查询专家信息 -->
		<select id="queryExpertByBatch" parameterType="ConditionEntity"
			resultType="ExpertInfoEntity">
			select distinct e.user_id,e.user_name,e.mobilephone,r.DECIMATIONBATCH
			as decimationBatch
			from t_extract_result r inner join t_expert_info e
			on
			r.expert_id=e.user_id
			where r.delete_flag=0
	
			<if test="id !=null and id !=''">
				and r.condition_id =#{id}
			</if>
			<if test="join_status !=null ">
				and r.join_status = #{join_status}
			</if>
			<if test="decimationBatch !=null and decimationBatch !=''">
				AND R.DECIMATIONBATCH = #{decimationBatch}
			</if>
		</select>
	
	<!-- 根据项目创建人id获得项目信息 -->
	<select id="queryProListByBatch" resultType="ProjectEntity"
		parameterType="ProjectEntity">
		select p.project_id as projectId,p.project_no as
		projectNo,p.project_name as
		projectName,p.phone,p.bid_time as
		bidTime,p.bid_duration as
		bidDuration,p.bid_address as bidAddress,
		p.tender,p.agent,p.remark,p.status,c.method as method,c.id as
		conditionId,
		decode((select u.user_name from ts_user u where u.user_id
		= p.manager and
		u.delete_flag = 0),'',p.manager,
		(select u.user_name
		from ts_user u where u.user_id = p.manager and
		u.delete_flag = 0)) as
		manager,p.delete_flag as deleteFlag,p.purpose
		as purpose
		from t_project
		p
		left join t_condition c on p.project_id = c.project_id
		where
		decimationBatch=#{decimationBatch} order by p.project_no,p.project_id
	</select>


	<!-- 查询已同意参标的专家个数 -->
	<select id="queryConfirmExpertCount" parameterType="ConditionEntity"
		resultType="Integer">
		select count(1) from t_extract_result r
		left join
		t_condition c on c.id = r.condition_id
		left join t_project p on
		p.project_id = c.project_id
		where p.decimationbatch=#{decimationBatch}
		and (r.join_status=0) and r.delete_flag=0
	</select>



	<!-- 根据抽取条件编号查询专家信息 -->
	<select id="queryExpertInfoById" parameterType="ConditionEntity"
		resultType="ExpertInfoEntity">
		select
		T.USER_ID,T.USER_NAME,P.PROJECT_ID,T.mobilephone,r.join_status from
		t_extract_result r
		left join t_condition c on c.id = r.condition_id
		left join t_project p on p.project_id = c.project_id
		LEFT JOIN
		T_EXPERT_INFO T ON T.USER_ID = R.EXPERT_ID
		where c.id=#{id}
		and
		(r.join_status=0) and r.delete_flag=0
	</select>


	<insert id="insertIntoTmpMajor" parameterType="TmpMajor">
		declare
		content
		clob := #{content};
		begin
		insert into tmp_MAJOR_id
		values(#{id},content);
		end;
	</insert>

	<insert id="deleteTmpMajor" parameterType="TmpMajor">
		delete tmp_MAJOR_id
	</insert>

	<!-- 项目列表信息 -->
	<select id="queryProjectList" parameterType="ProjectEntity"
		resultType="ProjectEntity">
		select
		p.project_id as projectId,p.project_no as
		projectNo,p.project_name as
		projectName,p.phone,p.bid_time as
		bidTime,p.bid_duration as
		bidDuration,p.bid_address as bidAddress,
		p.tender,p.agent,p.remark,p.status,c.method as method,c.id as
		conditionId,
		c.total appNum,
		decode((select u.user_name from ts_user u
		where u.user_id = p.manager and
		u.delete_flag = 0),'',p.manager,
		(select u.user_name from ts_user u where u.user_id = p.manager and
		u.delete_flag = 0)) as manager,
		decode((select u.user_name from ts_user
		u where u.user_id = p.create_user and
		u.delete_flag = 0),'',p.manager,
		(select u.user_name from ts_user u where u.user_id = p.create_user and
		u.delete_flag = 0)) as createUser,
		p.create_user create_id,c.is_two_con
		as isTwoCon,p.decimationbatch,p.purpose as
		purpose
		from t_project p
		left
		join t_condition c on p.project_id = c.project_id
		where p.delete_flag =
		0 <!-- and (SYSDATE-3<![CDATA[ < ]]> p.bid_time or SYSDATE<![CDATA[ < ]]> 
			p.bid_time) -->
		<if test="null != projectName and '' != projectName">
			and p.project_name like '%${projectName}%'
		</if>
		<if test="null != projectNo and '' != projectNo">
			and p.project_no like '%${projectNo}%'
		</if>
		<if test="null != bidStartTime and '' != bidStartTime">
			and p.bid_time >= #{bidStartTime}
		</if>
		<if test="null != bidEndTime and '' != bidEndTime">
			and p.bid_time &lt;= #{bidEndTime}
		</if>
		<if test="null != status and '' != status">
			and p.status = #{status}
		</if>
		<if test="null != status_ and '' != status_">
			and p.status in (${status_})
		</if>
		<if test="null != createUsers and '' != createUsers">
			and p.create_user in (${createUsers})
		</if>
		<if test="null != decimationBatch and '' != decimationBatch">
			and p.decimationBatch = #{decimationBatch}
		</if>
		<choose>
			<!-- 待抽取列表 -->
			<when test="stas == 'wait'">
				and p.status in(1,2,4,5,6,8,9,91)
			</when>
			<!-- 已抽取列表 -->
			<when test="stas == 'already'">
				and p.status in(3, 10)
			</when>
		</choose>
		<if test="null == status_ or '' == status_">
			order by create_time desc,p.project_no
		</if>
		<if test="null != status_ and '' != status_">
			order by status,p.project_no
		</if>
	</select>
	<select id="queryProjectList2" parameterType="ProjectEntity"
		resultType="ProjectEntity">
		select
		p.project_id as projectId,p.project_no as
		projectNo,p.project_name as
		projectName,p.phone,p.bid_time as
		bidTime,p.bid_duration as
		bidDuration,p.bid_address as bidAddress,
		p.tender,p.agent,p.remark,p.status,c.method as method,c.id as
		conditionId,
		c.total appNum,
		decode((select u.user_name from ts_user u
		where u.user_id = p.manager and
		u.delete_flag = 0),'',p.manager,
		(select u.user_name from ts_user u where u.user_id = p.manager and
		u.delete_flag = 0)) as manager,
		decode((select u.user_name from ts_user
		u where u.user_id = p.create_user and
		u.delete_flag = 0),'',p.manager,
		(select u.user_name from ts_user u where u.user_id = p.create_user and
		u.delete_flag = 0)) as createUser,
		p.create_user create_id,c.is_two_con
		as isTwoCon,p.decimationbatch,p.purpose as
		purpose
		from t_project p
		left
		join t_condition c on p.project_id = c.project_id
		where (SYSDATE-3<![CDATA[ < ]]>
		p.bid_time or SYSDATE<![CDATA[ < ]]>
		p.bid_time)
		<if test="null != projectName and '' != projectName">
			and p.project_name like '%${projectName}%'
		</if>
		<if test="null != projectNo and '' != projectNo">
			and p.project_no like '%${projectNo}%'
		</if>
		<if test="null != bidStartTime and '' != bidStartTime">
			and p.bid_time >= #{bidStartTime}
		</if>
		<if test="null != bidEndTime and '' != bidEndTime">
			and p.bid_time &lt;= #{bidEndTime}
		</if>
		<if test="null != status and '' != status">
			and p.status = #{status}
		</if>
		<if test="null != status_ and '' != status_">
			and p.status in (${status_})
		</if>
		<if test="null != createUsers and '' != createUsers">
			and p.create_user in (${createUsers})
		</if>
		<if test="null != decimationBatch and '' != decimationBatch">
			and p.decimationBatch = #{decimationBatch}
		</if>
		<choose>
			<!-- 待抽取列表 -->
			<when test="stas == 'wait'">
				and p.status in(1,2,4,5,6,8,9,91)
			</when>
			<!-- 已抽取列表 -->
			<when test="stas == 'already'">
				and p.status in(3, 10)
			</when>
		</choose>
		<if test="null == status_ or '' == status_">
			order by create_time desc,p.project_no
		</if>
		<if test="null != status_ and '' != status_">
			order by status,p.project_no
		</if>
	</select>

	<!-- 根据开标时间查询已经被抽取过并且同意参加\未通知 评标的专家信息 -->
	<select id="queryExpertsByBidTime" parameterType="ConditionEntity"
		resultType="ExpertInfoEntity">
		SELECT
		distinct user_id,e.user_name,
		e.mobilephone,e.company,
		e.company_addr,e.grade
		FROM t_project p
		LEFT JOIN T_CONDITION C ON
		P.PROJECT_ID=C.PROJECT_ID
		LEFT JOIN
		T_EXTRACT_RESULT R ON R.CONDITION_ID
		= C.ID
		LEFT JOIN t_expert_info e
		on e.user_id = r.expert_id
		WHERE
		P.DELETE_FLAG = 0 AND C.DELETE_FLAG =
		0 AND R.DELETE_FLAG = 0 AND
		E.DELETE_FLAG = 0
		AND R.join_status IN (0,4,2,3)

		<if test="grade!=null">
			AND e.grade=#{grade}
		</if>
		<if test="bidTimeDay !=null and bidTimeDay !=''">
			AND to_char(p.bid_time,'yyyy-MM-dd') = #{bidTimeDay}
		</if>
		AND R.EXPERT_ID IN (
		SELECT distinct R1.USER_ID FROM (
		SELECT E3.USER_ID
		AS USER_ID
		FROM T_EXPERT_MAJOR E3
		LEFT JOIN T_SPECIALTY_INFO S3 ON
		E3.MAJOR=S3.SPE_ID
		WHERE (
		(
		(
		<foreach collection="expertTypeList" index="index" item="item"
			separator="or">
			E3.MAJOR IN ${item}
		</foreach>
		)
		OR (
		<foreach collection="expertTypeList" index="index" item="item"
			separator="or">
			S3.SPE_CODE IN ${item}
		</foreach>
		)
		) AND S3.SPE_LEVEL=3
		)
		UNION
		SELECT E4.USER_ID AS USER_ID
		FROM
		T_EXPERT_MAJOR E4
		LEFT JOIN T_SPECIALTY_INFO S4 ON E4.MAJOR=S4.SPE_ID
		WHERE (
		(
		(
		<foreach collection="expertTypeList" index="index" item="item"
			separator="or">
			E4.MAJOR IN ${item}
		</foreach>
		)
		OR (
		<foreach collection="expertTypeList" index="index" item="item"
			separator="or">
			S4.SPE_CODE IN ${item}
		</foreach>
		)
		) AND S4.SPE_LEVEL=4
		)
		
		UNION
		SELECT E2.USER_ID AS USER_ID
		FROM
		T_EXPERT_MAJOR E2
		LEFT JOIN T_SPECIALTY_INFO S2 ON E2.MAJOR=S2.SPE_ID
		WHERE (
		(
		(
		<foreach collection="expertTypeList" index="index" item="item"
			separator="or">
			E2.MAJOR IN ${item}
		</foreach>
		)
		OR (
		<foreach collection="expertTypeList" index="index" item="item"
			separator="or">
			S2.SPE_CODE IN ${item}
		</foreach>
		)
		) AND S2.SPE_LEVEL=2
		)
		
		) R1

		)



	</select>


	<!-- 查询抽取完但未确认专家所对应的项目信息(只有刘红那有用) -->
	<select id="queryDecimationbatchByBidTime" parameterType="ConditionEntity"
		resultType="ConditionEntity">
		SELECT
		distinct p.decimationbatch
		FROM t_project p
		LEFT JOIN T_CONDITION
		C ON P.PROJECT_ID=C.PROJECT_ID
		LEFT JOIN
		T_EXTRACT_RESULT R ON
		R.CONDITION_ID = C.ID
		LEFT JOIN t_expert_info e
		on e.user_id =
		r.expert_id
		WHERE P.DELETE_FLAG = 0 AND C.DELETE_FLAG =
		0 AND
		R.DELETE_FLAG = 0 AND E.DELETE_FLAG = 0
		AND R.join_status IN (2,3,4)

		<if test="grade!=null">
			AND e.grade=#{grade}
		</if>
		<if test="bidTimeDay !=null and bidTimeDay !=''">
			AND to_char(p.bid_time,'yyyy-MM-dd') = #{bidTimeDay}
		</if>
		AND R.EXPERT_ID IN (
		SELECT distinct R1.USER_ID FROM (
		SELECT E3.USER_ID
		AS USER_ID
		FROM T_EXPERT_MAJOR E3
		LEFT JOIN T_SPECIALTY_INFO S3 ON
		E3.MAJOR=S3.SPE_ID
		WHERE (
		(
		(
		<foreach collection="expertTypeList" index="index" item="item"
			separator="or">
			E3.MAJOR IN ${item}
		</foreach>
		)
		OR (
		<foreach collection="expertTypeList" index="index" item="item"
			separator="or">
			S3.SPE_CODE IN ${item}
		</foreach>
		)
		) AND S3.SPE_LEVEL=3
		)
		UNION
		SELECT E4.USER_ID AS USER_ID
		FROM
		T_EXPERT_MAJOR E4
		LEFT JOIN T_SPECIALTY_INFO S4 ON E4.MAJOR=S4.SPE_ID
		WHERE (
		(
		(
		<foreach collection="expertTypeList" index="index" item="item"
			separator="or">
			E4.MAJOR IN ${item}
		</foreach>
		)
		OR (
		<foreach collection="expertTypeList" index="index" item="item"
			separator="or">
			S4.SPE_CODE IN ${item}
		</foreach>
		)
		) AND S4.SPE_LEVEL=4
		)
		) R1

		)



	</select>
	<!-- 语音接口调用方法 start -->
	<!-- 修改抽取专家抽取结果 -->
	<update id="updateExtractedExpertTOVoice" parameterType="ResultEntity">
		UPDATE t_extract_result
		SET join_status=#{joinStatus},
		reason=#{reason,
		jdbcType=VARCHAR},
		qt_reason=#{qt_reason, jdbcType=VARCHAR},
		recieve_time=#{recieveTime, jdbcType=VARCHAR},
		RESULT_BODY =
		#{resultBody, jdbcType=VARCHAR},
		SMS_ID = #{smsId, jdbcType=VARCHAR}
		WHERE
		<choose>
			<when test="phone !=null and phone !=''">
				EXPERT_ID = (SELECT USER_ID FROM T_EXPERT_INFO WHERE
				MOBILEPHONE = #{phone})
				AND DECIMATIONBATCH=#{decimationBatch}
			</when>
			<otherwise>
				join_status='2'
				AND DECIMATIONBATCH=#{decimationBatch}
			</otherwise>
		</choose>
		<if test="userId !=null and userId !=''">
			AND expert_id=#{userId}
		</if>
	</update>

	<!-- 修改抽取专家抽取结果 -->
	<update id="updateProjectEndExpertTOVoice" parameterType="ResultEntity">
		UPDATE t_extract_result
		SET join_status=#{joinStatus},
		reason=#{reason,jdbcType=VARCHAR},
		qt_reason=#{qt_reason, jdbcType=VARCHAR},
		recieve_time=#{recieveTime, jdbcType=VARCHAR},
		RESULT_BODY =#{resultBody, jdbcType=VARCHAR},
		SMS_ID = #{smsId, jdbcType=VARCHAR}
		WHERE
		DECIMATIONBATCH=#{decimationBatch}
		AND (recieve_time IS NULL OR reason ='人数已满')
	</update>

	<!-- 将未通知的专家修改为应急通知EXPERT_WAY -->
	<update id="updateExtractedExpertTOExpertWay" parameterType="ResultEntity">
		UPDATE t_extract_result
		SET
		EXPERT_WAY = #{expertWay}
		WHERE
		join_status='2'
		AND DECIMATIONBATCH=#{decimationBatch}
	</update>

	<!-- 根据专家抽取方式查询所有正在抽取中的项目（同一批次的项目合并成一条语句）[语音、短信] -->
	<select id="queryProjectTOVoice" parameterType="ConditionEntity"
		resultType="ProjectEntity">
		SELECT p.decimationbatch,
		wm_concat(p.project_no) as projectNo,
		wm_concat(p.project_name) as projectName,
		p.bid_time as
		bidTime,p.bid_address as bidAddress,
		p.manager,p.phone
		FROM t_project p
		LEFT JOIN t_condition c ON C.PROJECT_ID =P.PROJECT_ID
		WHERE P.STATUS
		IN('1','2','91')
		<if test="method != null and method != ''">
			AND C.METHOD = #{method}
		</if>
		<if test="decimationBatch !=null and decimationBatch !=''">
			AND p.decimationbatch = #{decimationBatch}
		</if>
		AND P.DELETE_FLAG =0 AND C.DELETE_FLAG =0
		group by
		p.decimationbatch,p.bid_time,p.bid_address,p.manager,p.phone order
		by
		p.decimationbatch desc
	</select>

	<!-- 根据批次号查询抽取条件 -->
	<select id="queryConditionListByBatch" parameterType="ProjectEntity"
		resultType="ConditionEntity">
		SELECT c.id,c.project_id as
		projectId,c.total,c.zone,c.senior_num as
		seniorNum,p.DECIMATIONBATCH as
		decimationBatch,
		c.score,c.expert_type as expertType,c.province,c.city,
		c.method,c.expert_type_name as expertTypeName,c.zone_name as
		zoneName,c.local_num as localNum,c.SHOWEXPERT_TYPE as
		showExpertType,c.CURRENT_STATUS as currentStatus,c.TEMPLATE_ID as
		templateId,
		c.voicesmscontent as voiceSmsContent,
		c.smscontent as smsContent
		FROM T_PROJECT P
		LEFT JOIN T_CONDITION C ON C.PROJECT_ID
		= P.PROJECT_ID
		WHERE P.DECIMATIONBATCH = #{decimationBatch}
		AND
		P.DELETE_FLAG =0 AND C.DELETE_FLAG=0
	</select>

	<!-- 查询项目下所有的专家信息[语音、短信] -->
	<select id="queryProjectExpertInfo" parameterType="ConditionEntity"
		resultType="ExpertInfoEntity">
		select distinct e.user_id,e.user_name,e.mobilephone,r.DECIMATIONBATCH
		as decimationBatch
		from t_extract_result r inner join t_expert_info e
		on
		r.expert_id=e.user_id
		where r.delete_flag=0

		<if test="id !=null and id !=''">
			and r.condition_id =#{id}
		</if>
		<if test="join_status !=null ">
			and r.join_status = #{join_status}
		</if>
		<if test="decimationBatch !=null and decimationBatch !=''">
			AND R.DECIMATIONBATCH = #{decimationBatch}
		</if>
	</select>

	<!-- 自动发送语音功能查询(当天)[语音] -->
	<select id="queryExtracedResultToVoice" parameterType="ResultEntity"
		resultType="ResultEntity">
		select condition_id as conditionId,e.DECIMATIONBATCH as
		decimationBatch,e.expert_id as userId, call_time as
		callTime,message_no as messageNo,
		i.mobilephone as phone ,e.join_status
		as joinStatus
		from t_extract_result e
		inner join t_expert_info i on
		e.expert_id=i.user_id
		where e.message_no is not null
		and e.extract_time
		between
			to_date(to_char(sysdate,'yyyy-mm-dd') || ' 00:00:01','yyyy-mm-dd hh24:mi:ss') 
		and
			to_date(to_char(sysdate,'yyyy-mm-dd') || ' 23:59:59','yyyy-mm-dd hh24:mi:ss')
		and i.mobilephone in (#{phone})
		and
		e.delete_flag=0
		<if test="joinStatus !=null">
			and join_status=#{joinStatus}
		</if>
		<choose>
			<when test="messageNo !=null and messageNo !=''">
				AND e.message_no = #{messageNo}
			</when>
			<otherwise>
				AND E.DECIMATIONBATCH =#{decimationBatch}
			</otherwise>
		</choose>
	</select>

	<!-- 判断短信批次号在抽取当天是否重复[短信] -->
	<select id="queryExtracedResultToMessage" parameterType="ResultEntity"
		resultType="ResultEntity">
		select condition_id as conditionId,e.DECIMATIONBATCH as
		decimationBatch,e.expert_id as userId, call_time as
		callTime,message_no as messageNo,i.mobilephone as phone
		from
		t_extract_result e
		inner join t_expert_info i on e.expert_id=i.user_id
		where e.message_no is not null
		<if test="phone !=null and phone !=''">
			and i.mobilephone in (${phone})
		</if>
		and e.delete_flag=0
		and join_status=3 and extract_time 
		between
			to_date(to_char(sysdate,'yyyy-mm-dd') || ' 00:00:01','yyyy-mm-dd hh24:mi:ss') 
		and
			to_date(to_char(sysdate,'yyyy-mm-dd') || ' 23:59:59','yyyy-mm-dd hh24:mi:ss')
		<if test="recieveTime !=null">
			AND e.recieve_time between e.call_time and
			e.call_time+5/(24*60)
		</if>
		<if test="messageNo !=null and messageNo !=''">
			AND e.message_no = #{messageNo}
		</if>
	</select>
	<!-- 查询已抽取抽取结果 -->
	<select id="queryAllExtractedExperts" resultType="ResultEntity"
		parameterType="ResultEntity">
		select condition_id as conditionId,e.DECIMATIONBATCH as
		decimationBatch,e.expert_id as userId, call_time as
		callTime,message_no as messageNo,i.mobilephone as phone
		from
		t_extract_result e
		inner join t_expert_info i on e.expert_id=i.user_id
		where e.delete_flag=0
		<if test="id !=null and id !=''">
			and e.condition_id=#{id}
		</if>
		<if test="decimationBatch !=null and decimationBatch !=''">
			and e.decimationbatch = #{decimationBatch}
		</if>
		<if test="joinStatus!=null">
			and e.join_status = #{joinStatus}
		</if>
		<if test="userId !=null and  userId !=''">
			and e.expert_id =#{userId}
		</if>
		order by e.id asc
	</select>
	
	
	<select id="queryExtractedFrequency" resultType="ResultEntity"
		parameterType="ConditionEntity">
		SELECT s.decimationbatch as decimationBatch,tei.MOBILEPHONE as phone  FROM (
			SELECT T.decimationbatch,t.expert_id,
			Row_Number() OVER (partition by t.expert_id ORDER BY t.expert_id desc) RN
			 FROM T_EXTRACT_RESULT T
			LEFT JOIN T_CONDITION TC ON T.CONDITION_ID =  TC.ID
			left join t_project p on p.decimationbatch=t.decimationbatch
			where p.bid_time between #{startTime}  and  #{endTime}
			and p.TENDER = #{con_company} AND T.JOIN_STATUS=0
			group by t.decimationbatch,t.expert_id
			) s
			left join t_expert_info tei on s.expert_id = tei.user_id
			where 
			<![CDATA[
			rn >= #{frequency}
			]]>
			<if test="startAge !=null">
				<![CDATA[
				and (to_char(sysdate, 'yyyy' )-to_char(tei.birthday, 'yyyy' ))>=${startAge}
				]]>
			</if>
			<if test="endAge !=null">
				<![CDATA[
				and (to_char(sysdate, 'yyyy' )-to_char(tei.birthday, 'yyyy' ))<=${endAge}
				]]>
			</if>
	</select>
	<!-- 更新抽取方式以及留痕 -->
	<update id="updateConditionCurrentStatus" parameterType="ConditionEntity">
		update
		t_condition set CURRENT_STATUS = #{currentStatus}
		<if test="method!=null">
			,METHOD = #{method}
		</if>
		where id=#{id}
	</update>

	<update id="updateConditionStatusByDecimationBatch"
		parameterType="ProjectEntity">
		update
		t_condition C
		set METHOD = #{method}
		where
		C.PROJECT_ID IN
		(SELECT T.PROJECT_ID FROM t_project T WHERE T.DECIMATIONBATCH
		=#{decimationBatch})
	</update>


	<update id="deleteExtractResultByDecimationBatch" parameterType="ResultEntity">
		update T_EXTRACT_RESULT T
		SET T.DELETE_FLAG = #{deleteFlag}
		WHERE
		T.DECIMATIONBATCH = #{decimationBatch}

	</update>

	<!-- 修改抽取专家抽取结果 -->
	<update id="updateExtractedSmsStartus" parameterType="ResultEntity">
		UPDATE
		t_extract_result T
		SET INFORM_ID=#{informId}
		WHERE
		T.SMS_ID = #{smsId}
	</update>

	<!-- 查询所有的项目 -->
	<select id="queryPageAllProjectExtractedExperts" resultType="ResultProjectExpertInfo"
		parameterType="ResultProjectExpertInfo">
		SELECT F2.DECIMATIONBATCH AS decimationBatch,F2.project_name AS
		projectName,F2.USER_NAME AS managerName,
		F2.BID_TIME AS bidTime,f2.method,f2.department,
		ER.EXPERT_ID AS expertId,ER.JOIN_STATUS AS joinStatus,ER.REASON AS
		reason,ER.QT_REASON AS qt_reason,
		EI.USER_NAME AS expertName,
		Row_Number() OVER (partition by f2.department,F2.DECIMATIONBATCH order by
		F2.BID_TIME) RN
		from
		(SELECT
		F1.DECIMATIONBATCH,F1.ID,F1.project_name,F1.USER_NAME,F1.BID_TIME,f1.method,f1.department
		FROM
		(
		SELECT
		S.DECIMATIONBATCH,S.ID,S.project_name,S.USER_NAME,S.BID_TIME,s.method,s.department
		FROM
		(SELECT
		P.DECIMATIONBATCH,C.ID,p.project_name,U.USER_NAME,P.BID_TIME,c.method,u.department,
		Row_Number() OVER (partition by P.DECIMATIONBATCH ORDER BY p.bid_time
		desc) RN
		FROM T_PROJECT P
		LEFT JOIN T_CONDITION C ON C.PROJECT_ID=P.PROJECT_ID
		LEFT JOIN TS_USER U ON P.MANAGER = U.USER_ID
		WHERE P.DELETE_FLAG = 0
		<if test="decimationBatch!=null and decimationBatch!=''">
			AND P.DECIMATIONBATCH LIKE '%${decimationBatch}%'
		</if>
		<if test="department!=null and department!=''">
			AND u.department = #{department}
		</if>
		<if test="null != bidStartTime and '' != bidStartTime">
			and p.bid_time >= #{bidStartTime}
		</if>
		<if test="null != bidEndTime and '' != bidEndTime">
			and p.bid_time &lt;= #{bidEndTime}
		</if>
		) S
		WHERE S.RN = 1
		)F1 )F2
		left join T_EXTRACT_RESULT er on er.condition_id = f2.ID
		LEFT JOIN T_EXPERT_INFO EI ON EI.USER_ID = ER.EXPERT_ID
		WHERE ER.DELETE_FLAG = 0
	</select>
	<!-- 语音end -->
	<!-- 取消项目，修改数据库中对应的字段值 -->
	<update id="modifyCancelProject" parameterType="ProjectEntity">
		update
		T_PROJECT T
		set
		T.MODIFY_TYPE = '2'
		where
		T.DECIMATIONBATCH=#{decimationBatch}
		and
		T.Delete_Flag=0
	</update>

	<!-- 取消项目时修改专家参加改为不参加 modifyJoinStaus -->
	<update id="modifyJoinStaus" parameterType="ProjectEntity">
		update
		T_EXTRACT_RESULT T
		set
		T.JOIN_STATUS = '1'
		where
		T.DECIMATIONBATCH=#{decimationBatch}
		and
		T.Delete_Flag=0
	</update>
	
		<!-- 根据专家ID和专家身份证判断当前专家是否符合条件 -->
	<select id="checkExpertByBidTime" resultType="ResultEntity"
		parameterType="String">
		SELECT TEI.USER_ID as userId,TER.JOIN_STATUS as joinStatus FROM T_EXTRACT_RESULT TER
		LEFT JOIN T_PROJECT P  ON TER.DECIMATIONBATCH = P.DECIMATIONBATCH
		LEFT JOIN T_EXPERT_INFO TEI  ON TEI.USER_ID = TER.EXPERT_ID
		WHERE TER.DELETE_FLAG=0 AND P.DELETE_FLAG=0 AND TEI.DELETE_FLAG=0 
		AND TEI.ID_NO = #{idNo} AND  to_char(P.BID_TIME,'yyyy-mm-dd')=#{bidTime}
		AND TER.JOIN_STATUS IN ('0','2')
	</select>
	
	<insert id="updateConditionPersonNum" parameterType="ConditionRecordEntity">
		insert into T_CONDITION_RECORD(ID,PROJECT_ID,OLD_TOTAL,NEW_TOTAL,PERSON,DELETE_FLAG,CREATE_ID,CREATE_TIME)
		values(#{id},#{projectId},#{oldTotal},#{newTotal},#{person},#{delete_flag},#{create_id},#{create_time})
	</insert>
	
	<update id="updateConditionPerson" parameterType="ConditionRecordEntity">
		update t_Condition c set c.total=#{newTotal} where c.project_id=#{projectId} and c.delete_flag='0'
	</update>

	<select id="queryCountList" resultType="java.lang.String"
			parameterType="ProjectEntity">
	    select DECIMATIONBATCH
	    from t_project
	    where 1=1
		<if test="null != bidStartTime and '' != bidStartTime">
			and bid_time >= #{bidStartTime}
		</if>
		<if test="null != bidEndTime and '' != bidEndTime">
			and bid_time &lt;= #{bidEndTime}
		</if>
	    group by DECIMATIONBATCH
	    having count(DECIMATIONBATCH) >= #{queryCount}
	    order by DECIMATIONBATCH desc
	</select>
	<select id="queryPDFProjectInfo" parameterType="ProjectEntity" resultType="ProjectEntity">
		select m.DECIMATIONBATCH,m.projectNo,m.projectName,m.manager,m.bidTime,m.bidAddress,
       nvl(m.total,0) zjCount,
       count(r.decimationbatch) sjCount,
       nvl(max(r.sort),0) cqCount
from (select DECIMATIONBATCH,
       projectNo,
       projectName,
       manager,bidTime,bidAddress,
       total,
       id
  from (select  p.DECIMATIONBATCH as DECIMATIONBATCH,
               p.project_no as projectNo,
               p.project_name as projectName,
		decode((select u.user_name
		from ts_user u
		where u.user_id = p.manager
		and u.delete_flag = 0),
		'',
		p.manager,
		(select u.user_name
		from ts_user u
		where u.user_id = p.manager
		and u.delete_flag = 0)) as manager,
              p.bid_time as bidTime,
              p.bid_address as bidAddress,
               c.id,
               c.total,
               Row_Number() OVER(partition by DECIMATIONBATCH ORDER BY p.project_no asc, p.project_id) RN
          from t_project p
          left join t_condition c
            on p.project_id = c.project_id
         where p.delete_flag = 0
		<if test="null != bidStartTime and '' != bidStartTime">
			and p.bid_time >= #{bidStartTime}
		</if>
		<if test="null != bidEndTime and '' != bidEndTime">
			and p.bid_time &lt;= #{bidEndTime}
		</if>
         order by p.create_time desc, p.project_no) r1
	 where r1.rn = 1
	 ) m
	 left join  T_EXTRACT_RESULT r
	on r.condition_id=m.id
	 group by
       m.DECIMATIONBATCH,m.projectNo,m.projectName,m.manager,m.bidTime,m.bidAddress,
       m.total,r.decimationbatch
		having nvl(max(r.sort),0)>= #{queryCount}
		order by
		<if test="null == paixu or '' == paixu">
			m.DECIMATIONBATCH desc
		</if>
		<if test="null != paixu and '' != paixu">
			${paixu}
		</if>

	</select>
	<select id="queryPageProjectInfo" parameterType="ProjectEntity" resultType="ProjectEntity">
		select m.DECIMATIONBATCH,m.projectNo,m.projectName,m.manager,m.bidTime,m.bidAddress, method,
		conditionId,
		nvl(m.total,0) zjCount,
		count(r.decimationbatch) sjCount,
		nvl(max(r.sort),0) cqCount
		from (select DECIMATIONBATCH,
		projectNo,
		projectName,
		manager,bidTime,bidAddress,
		total,method,conditionId,
		id
		from (select  p.DECIMATIONBATCH as DECIMATIONBATCH,
		p.project_no as projectNo,
		p.project_name as projectName,
		decode((select u.user_name
		from ts_user u
		where u.user_id = p.manager
		and u.delete_flag = 0),
		'',
		p.manager,
		(select u.user_name
		from ts_user u
		where u.user_id = p.manager
		and u.delete_flag = 0)) as manager,
		p.bid_time as bidTime,
		p.bid_address as bidAddress,
		c.id,
		c.total,
		c.method as method,
		c.id as conditionId,
		Row_Number() OVER(partition by DECIMATIONBATCH ORDER BY p.project_no asc, p.project_id) RN
		from t_project p
		left join t_condition c
		on p.project_id = c.project_id
		where p.delete_flag = 0
		<if test="null != bidStartTime and '' != bidStartTime">
			and p.bid_time >= #{bidStartTime}
		</if>
		<if test="null != bidEndTime and '' != bidEndTime">
			and p.bid_time &lt;= #{bidEndTime}
		</if>
		order by p.create_time desc, p.project_no) r1
		where r1.rn = 1
		) m
		left join  T_EXTRACT_RESULT r
		on r.condition_id=m.id
		group by
		m.DECIMATIONBATCH,m.projectNo,m.projectName,m.manager,m.bidTime,m.bidAddress, method,
		conditionId,m.total,r.decimationbatch
		having nvl(max(r.sort),0)>= #{queryCount}
		order by
		<if test="null == paixu or '' == paixu">
			m.DECIMATIONBATCH desc
		</if>
		<if test="null != paixu and '' != paixu">
			${paixu}
		</if>

	</select>
	<select id="queryPageLoginRecordList" parameterType="LoginRecord" resultType="LoginRecord">
		select user_id,login_name,login_time,login_status from ts_login
		WHERE 1=1 
		<if test="null != login_name and '' != login_name">
			and login_name = #{login_name}
		</if>
		<if test="null != loginStartTime and '' != loginStartTime">
			and login_time >= to_date(#{loginStartTime},'yyyy-mm-dd hh24:mi:ss')
		</if>
		<if test="null != loginEndTime and '' != loginEndTime">
			and login_time &lt; to_date(#{loginEndTime},'yyyy-mm-dd hh24:mi:ss')
		</if>
		<if test="null != login_status">
			and login_status = #{login_status}
		</if>
		order by login_time desc
	</select>
	<insert id="saveLoginInfo" parameterType="LoginRecord">
		insert into TS_LOGIN(ID,USER_ID,LOGIN_NAME,LOGIN_TIME,LOGIN_STATUS,CREATE_NAME,CREATE_TIME)
		values(#{id},#{user_id},#{login_name},#{login_time},#{login_status},#{create_name},#{create_time})
	</insert>
	
	<!-- 根据抽取条件抽取专家信息 -->
	<select id="queryExpertsByRules" parameterType="ConditionEntity"
		resultType="ExpertInfoEntity">
		select * from (
		select t.id, t.user_id, user_name, mobilephone, company, company_addr, grade,f.major
		from
		t_expert_info t
		left join (select user_id,xmlagg(xmlparse(content i.spe_name||',' wellformed) order by user_id).getclobval() as major
  		from  t_expert_major m
                  left join t_specialty_info i
                    on m.major = i.spe_id
		            where m.delete_flag = 0
                     group by user_id) f
                     on t.user_id=f.user_id
		where
		delete_flag=0 and STATUS in ('3','8') 
		<!-- 去除回避专家 -->
		<if test="companyDebarbList !=null and companyDebarbList.size()>0">
			AND COMPANY NOT IN 
		 <foreach item="item" collection="companyDebarbList" separator="," open="(" close=")" index="">
	     	 <if test="item!=null">
	     	 	#{item.company, jdbcType=VARCHAR}
	     	 </if>
	    </foreach>
		</if>
		<if test="extractsAvoidList!=null and extractsAvoidList.size()>0">
			AND 
			<foreach collection="extractsAvoidList" index="index" item="item"
				separator="or">
				MOBILEPHONE  NOT IN ${item}
			</foreach>
		</if>
		<!-- 同一个公司的 -->
		<if test="tender!=null and tender!=''">
			and company not in(${tender})
		</if>
		<!-- 新入库专家一个工作日之后方可抽取 by libb 20150703 -->
		<![CDATA[
			and to_char(audit_time + 1, 'yyyy-MM-dd hh24:mi:ss') < to_char(sysdate, 'yyyy-MM-dd hh24:mi:ss')
		]]>
		<!-- 70岁以上专家不可被抽取 -->
		and to_char(sysdate, 'yyyy' )-to_char(birthday, 'yyyy' ) &lt;= 70
		<!-- 去除上一轮抽取过的专家 -->
		<!-- and user_id not in(select expert_id from t_extract_result where delete_flag=0 
			and condition_id='${id}') -->
		<if test="null != id and '' != id">
			and not exists (select 1 from t_extract_result tt where
			t.user_id=tt.expert_id and delete_flag = 0 and condition_id = '${id}')
		</if>
		and (1=2
		<if test="province!=null and province!=''">
			or province in('${province}')
		</if>
		<if test="city!=null and city!=''">
			or city in('${city}')
		</if>
		<if test="zone!=null and zone!=''">
			or zone in('${zone}')
		</if>
		)
		<if test="titleCode !=null and titleCode != ''">
			and t.EXPERT_TYPE = #{titleCode}
		</if>
		<if test="titleCode == null or titleCode == '' ">
			and t.EXPERT_TYPE !=3 and t.EXPERT_TYPE !=4
		</if>
		and (OUT_STATUS='40' or OUT_STATUS='30' or OUT_STATUS is null)
		and t.EXPERT_TYPE  is not null 			
		<if test="grade!=null">
			and grade=#{grade}
		</if>
		order by id asc)
		where major is not null
	</select>
	<select id="queryOpenTimeExper" resultType="ExpertInfoEntity" parameterType="String">
	select distinct e.expert_id user_id from 
	t_project t 
	left join 
	t_extract_result e on t.DECIMATIONBATCH = e.decimationbatch 
	where  e.join_status IN (0,4,2,3)   
	AND to_char(t.bid_time,'yyyy-MM-dd') = #{openTime}	
	</select>
	
	<!-- 查询已同意参标的国家级（或地方级）改专业的专家个数 -->
	<select id="queryExpertCount" parameterType="ConditionEntity"
		resultType="ExpertInfoEntity">
		select majorselection, count(r.expert_id) count from t_extract_result r
		left join
		t_expert_info i on r.expert_id = i.user_id
		where r.condition_id = #{id}
		and r.join_status=0 and r.delete_flag=0  
		<if test="null != grade and '' != grade">
			and i.grade = #{grade}
		</if>
		group by MAJORSELECTION
	</select>
	
	<select id="queryConditionId" parameterType="String" resultType="ParamTypeEntity">
		select t.expert_id expertId,
				JOIN_STATUS joinStatus,
				(case when reason is null 
  				then '1' else reason end) reason,
				(case when qt_reason is null 
				then '1' else qt_reason end) qtReason 
				from t_extract_result t where t.condition_id =#{conid}
	</select>
</mapper>