package com.hzw.sunflower.controller;

import com.hzw.sunflower.common.BaseController;
import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.controller.request.ProcessRecordReq;
import com.hzw.sunflower.dto.ProcessRecordDTO;
import com.hzw.sunflower.service.ProcessRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2021/7/22 9:00
 * @description：流程请求
 * @modified By：`
 * @version: 1.0
 */
@Api(tags = "jasmine-流程信息服务")
@RestController
@RequestMapping("/processRecord")
public class ProcessRecordController extends BaseController {

    @Autowired
    private ProcessRecordService processRecordService;

    @ApiOperation(value = "查询流程信息")
    @ApiImplicitParam(name = "req", value = "流程", required = true, dataType = "ProcessRecordReq", paramType = "body")
    @PostMapping(value = "/queryProcessRecord")
    public Result<List<ProcessRecordDTO>> queryProcessRecord(@RequestBody ProcessRecordReq req) {
        List<ProcessRecordDTO> list = processRecordService.queryProcessRecord(req);
        return Result.ok(list);
    }

    @ApiOperation(value = "查询流程信息")
    @ApiImplicitParam(name = "req", value = "流程", required = true, dataType = "ProcessRecordReq", paramType = "body")
    @PostMapping(value = "/queryProcessRecords")
    public Result<List<ProcessRecordDTO>> queryProcessRecords(@RequestBody ProcessRecordReq req) {
        List<ProcessRecordDTO> list = processRecordService.queryProcessRecords(req);
        return Result.ok(list);
    }

    @ApiOperation(value = "劳务费查询流程信息")
    @ApiImplicitParam(name = "req", value = "流程", required = true, dataType = "ProcessRecordReq", paramType = "body")
    @PostMapping(value = "/queryProcessRecordExpertFee")
    public Result<List<ProcessRecordDTO>> queryProcessRecordExpertFee(@RequestBody ProcessRecordReq req) {
        List<ProcessRecordDTO> list = processRecordService.queryProcessRecordExpertFee(req);
        return Result.ok(list);
    }
}
