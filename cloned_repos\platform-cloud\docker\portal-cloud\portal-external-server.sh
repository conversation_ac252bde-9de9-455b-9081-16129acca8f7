#!/bin/sh
#cp TEMPLATE FILE
docker stop portal-external-server || true
docker rm portal-external-server || true
docker rmi registry.cn-hangzhou.aliyuncs.com/hzw-jtcc/jtcc:portal-external-server-1.0.0 || true
docker pull registry.cn-hangzhou.aliyuncs.com/hzw-jtcc/jtcc:portal-external-server-1.0.0
docker run -itd --net=host  --log-opt max-size=100m --log-opt max-file=3  --name portal-external-server -v /home/<USER>/plan1/portal-cloud/config/portal-external-server:/hzw/external/config -v /data/log:/hzw/external/logs  registry.cn-hangzhou.aliyuncs.com/hzw-jtcc/jtcc:portal-external-server-1.0.0
