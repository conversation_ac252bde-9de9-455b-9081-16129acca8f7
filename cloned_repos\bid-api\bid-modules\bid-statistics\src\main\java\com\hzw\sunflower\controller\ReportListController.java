package com.hzw.sunflower.controller;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hzw.sunflower.common.BaseController;
import com.hzw.sunflower.common.Paging;
import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.controller.response.CaDataVo;
import com.hzw.sunflower.controller.response.ReportListVO;
import com.hzw.sunflower.entity.ArchiveRecallList;
import com.hzw.sunflower.entity.ReportList;
import com.hzw.sunflower.entity.condition.ArchiveRecallCondition;
import com.hzw.sunflower.entity.condition.ReportListCondition;
import com.hzw.sunflower.entity.condition.TendererReportCondition;
import com.hzw.sunflower.service.ReportListService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.List;


@Api(tags = "报表中心列表")
@RestController
@RequestMapping("/reportList")
public class ReportListController extends BaseController {

    @Autowired
    ReportListService reportListService;

    @ApiOperation(value = "报表中心列表（17）")
    @ApiImplicitParam(name = "condition", value = "查询条件", required = true, dataType = "ReportListCondition", paramType = "body")
    @PostMapping({"/list"})
    public Result<Paging<ReportList>> list(@RequestBody ReportListCondition condition) {
        IPage<ReportList> page = reportListService.findInfoByCondition(condition);
        return Result.ok(Paging.buildPaging(page));
    }

    @ApiOperation(value = "报表中心列表（20） 归档一览表")
    @ApiImplicitParam(name = "condition", value = "查询条件", required = true, dataType = "ReportListCondition", paramType = "body")
    @PostMapping({"/projectList"})
    public Result<Paging<ReportList>> projectList(@RequestBody ReportListCondition condition) {
        IPage<ReportList> page = reportListService.findInfo(condition,getJwtUser());
        return Result.ok(Paging.buildPaging(page));
    }


    @ApiOperation(value = "报表中心列表 归档撤回统计")
    @ApiImplicitParam(name = "condition", value = "查询条件", required = true, dataType = "ArchiveRecallCondition",  paramType = "body")
    @PostMapping({"/archiveRecallList"})
    public Result<Paging<ArchiveRecallList>> archiveRecallList(@RequestBody ArchiveRecallCondition condition) {
        IPage<ArchiveRecallList> page = reportListService.archiveRecallList(condition,getJwtUser());
        return Result.ok(Paging.buildPaging(page));
    }

    @ApiOperation(value = "归档撤回统计导出excel")
    @ApiImplicitParam(name = "condition", value = "查询条件", required = true, dataType = "ArchiveRecallCondition", paramType = "body")
    @PostMapping("/exportArchiveRecallList")
    public  void  exportArchiveRecallList(HttpServletResponse response,@RequestBody @Valid ArchiveRecallCondition condition) {
        List<ArchiveRecallList> archiveRecallLists = reportListService.exportArchiveRecallList(condition);
        //设置标题信息
        Workbook workbook;
        String title;

        title="归档撤回统计表";
        workbook= ExcelExportUtil.exportExcel(new ExportParams(title,"归档撤回统计表"), ArchiveRecallList.class,archiveRecallLists);
        try {
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("统计.xls", "UTF-8"));
            response.setContentType("application/vnd.ms-excel;charset=UTF-8");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        try {
            workbook.write(response.getOutputStream());
            response.getOutputStream().close();
            workbook.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    @ApiOperation(value = "导出excel")
    @ApiImplicitParam(name = "req", value = "查询条件", required = true, dataType = "ReportListCondition", paramType = "body")
    @PostMapping("/exportProjectList")
    public  void  exportProjectList(HttpServletResponse response, @RequestBody ReportListCondition condition) {
        List<ReportListVO> reportList = reportListService.findInfoList(condition,getJwtUser());

        //设置标题信息
        Workbook workbook;
        String title;
//        DateFormat sdf= new SimpleDateFormat("yyyy-MM");
//        String startTime= null;
//        String endTime= null;
//        try {
//            startTime = sdf.format(sdf.parse(req.getStartDate()));
//            endTime = sdf.format(sdf.parse(req.getEndDate()));
//        } catch (ParseException e) {
//            e.printStackTrace();
//        }

        title="项目归档一览表";
        workbook= ExcelExportUtil.exportExcel(new ExportParams(title,"项目归档一览表"), ReportListVO.class,reportList);
        try {
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("统计.xls", "UTF-8"));
            response.setContentType("application/vnd.ms-excel;charset=UTF-8");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        try {
            workbook.write(response.getOutputStream());
            response.getOutputStream().close();
            workbook.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

}
