package com.hzw.ssm.sys.project.entity;

import java.util.Date;
import java.util.List;

import com.hzw.ssm.expert.entity.ExpertInfoEntity;
import com.hzw.ssm.fw.base.BaseEntity;
import com.hzw.ssm.fw.util.Page;

public class ConditionEntity extends BaseEntity {
	private static final long serialVersionUID = -8151736570527763583L;

	private String projectId;// 项目信息主键
	private String id;// 条件id
	private Long total;// 专家总人数
	private Long score;// 专家评价得分
	private Long seniorNum;// 国家级专家人数
	private Long localNum;//地方级专家人数
	private String zone;// 区县
	private Long method;// 抽取方式
	private String expertType;// 专家类别id
	private List<String> expertTypeList;
	private String expertTypeName;// 专家类别名称
	private Long deleteFlag;// 删除标识
	private Long num;// 第几轮抽取
	private Long extractNum;// 每次抽取的专家人数
	private Long grade;// 专家级别
	private String titleCode;//职称
	private String expertTypeNum;//专家类型数据
	private Date startBidTime;// 用于剔除已抽取的专家（当前项目抽取的时间加上规定一个时间）
	private Date endBidTime;// 用于剔除已抽取的专家

	private Date extractTime;// 抽取时间

	private String province;// 省份
	private String city;// 城市

	private String zoneName;//地区名称
	private ExpertInfoEntity expert = new ExpertInfoEntity();
	private Long join_status;//是否参加 0:参加 1:不参加 2:未通知
	private Long joinNum;          // 参标专家人数
	private String decimationBatch;//抽取批次 
	private String showExpertType;//专家类型展示
	//判断为页面是修改还是新增
	private String optType;
	
	private String voiceType; 
	private String bidTimeDay;//开标时间取年月日
	
	private String voiceSmsContent;//语音模板内容
	
	private String smsContent;//短信模板内容
	
	private String templateId;//模板id
	
	private Long currentStatus;//项目之前的状态是否是语音抽取(用于语音转应急时候的留痕)
	
	private List<ExtractDebarbEntity> companyDebarbList;//回避机构
	private List<ExtractDebarbEntity> extractDebarbList;//回避专家（用于页面）
	
	private List<String> extractsAvoidList;//回避专家集合（用于查询条件）
	
	/****************************设置规则start********************************************/
	/**
	 *  是否设置规则
	 */
	private Long isRule; //1.是 0.否
	/**
	 * 规则开始时间 
	 */
	private Date startTime;
	/**
	 * 规则结束时间
	 */
	private Date endTime;
	/**
	 * 频率
	 */
	private Long frequency;
	/**
	 * 开始年龄段
	 */
	private Long startAge;
	
	/**
	 * 结束年龄段
	 */
	private Long endAge;
	
	private String con_company;
	//是否暂停评标 0:否；1：是
	private Integer isBid;

	private String joinStatus;//是否参加 0:参加 1:不参加 2:未通知

	private String eMethod;
	
	public Integer getIsBid() {
		return isBid;
	}

	public void setIsBid(Integer isBid) {
		this.isBid = isBid;
	}

	public String getJoinStatus() {
		return joinStatus;
	}

	public void setJoinStatus(String joinStatus) {
		this.joinStatus = joinStatus;
	}

	/****************************设置规则end********************************************/
	public String getShowExpertType() {
		return showExpertType;
	}

	public void setShowExpertType(String showExpertType) {
		this.showExpertType = showExpertType;
	}

	public String getDecimationBatch() {
		return decimationBatch;
	}

	public void setDecimationBatch(String decimationBatch) {
		this.decimationBatch = decimationBatch;
	}

	private String orderFlag;//排序
	private String smsStr;
	private String tender;	// 招标人
	private Long isTwoCon;	// 是否二次抽取[指定专家] 0 不是 1是
	private Integer order;                // 排序号
	private Page page;
	private String sourceCode;//数据来源
	
	private String templateType;//模板类型
	private Integer isValid;//是否生效
	
	public String getTemplateType() {
		return templateType;
	}

	public void setTemplateType(String templateType) {
		this.templateType = templateType;
	}

	public Integer getIsValid() {
		return isValid;
	}

	public void setIsValid(Integer isValid) {
		this.isValid = isValid;
	}

	public String getSourceCode() {
		return sourceCode;
	}

	public void setSourceCode(String sourceCode) {
		this.sourceCode = sourceCode;
	}

	public String getProjectId() {
		return projectId;
	}

	public void setProjectId(String projectId) {
		this.projectId = projectId;
	}

	public Long getDeleteFlag() {
		return deleteFlag;
	}

	public void setDeleteFlag(Long deleteFlag) {
		this.deleteFlag = deleteFlag;
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public Long getTotal() {
		return total;
	}

	public void setTotal(Long total) {
		this.total = total;
	}

	public Long getScore() {
		return score;
	}

	public void setScore(Long score) {
		this.score = score;
	}

	public Long getSeniorNum() {
		return seniorNum;
	}

	public void setSeniorNum(Long seniorNum) {
		this.seniorNum = seniorNum;
	}

	public String getZone() {
		return zone;
	}

	public void setZone(String zone) {
		this.zone = zone;
	}

	public Long getMethod() {
		return method;
	}

	public void setMethod(Long method) {
		this.method = method;
	}

	public String getExpertType() {
		return expertType;
	}

	public void setExpertType(String expertType) {
		this.expertType = expertType;
	}

	public String getExpertTypeName() {
		return expertTypeName;
	}

	public void setExpertTypeName(String expertTypeName) {
		this.expertTypeName = expertTypeName;
	}

	public Long getExtractNum() {
		return extractNum;
	}

	public void setExtractNum(Long extractNum) {
		this.extractNum = extractNum;
	}

	public Long getNum() {
		return num;
	}

	public void setNum(Long num) {
		this.num = num;
	}

	public String getProvince() {
		return province;
	}

	public void setProvince(String province) {
		this.province = province;
	}

	public String getCity() {
		return city;
	}

	public void setCity(String city) {
		this.city = city;
	}

	public Long getGrade() {
		return grade;
	}

	public void setGrade(Long grade) {
		this.grade = grade;
	}

	public Date getStartBidTime() {
		return startBidTime;
	}

	public void setStartBidTime(Date startBidTime) {
		this.startBidTime = startBidTime;
	}

	public Date getEndBidTime() {
		return endBidTime;
	}

	public void setEndBidTime(Date endBidTime) {
		this.endBidTime = endBidTime;
	}

	public Date getExtractTime() {
		return extractTime;
	}

	public void setExtractTime(Date extractTime) {
		this.extractTime = extractTime;
	}

	public ExpertInfoEntity getExpert() {
		return expert;
	}

	public void setExpert(ExpertInfoEntity expert) {
		this.expert = expert;
	}

	public Page getPage() {
		return page;
	}

	public void setPage(Page page) {
		this.page = page;
	}

	public String getZoneName() {
		return zoneName;
	}

	public void setZoneName(String zoneName) {
		this.zoneName = zoneName;
	}

	public Long getLocalNum() {
		return localNum;
	}

	public void setLocalNum(Long localNum) {
		this.localNum = localNum;
	}

	public Long getJoin_status() {
		return join_status;
	}

	public void setJoin_status(Long join_status) {
		this.join_status = join_status;
	}

	public Long getJoinNum() {
		return joinNum;
	}

	public void setJoinNum(Long joinNum) {
		this.joinNum = joinNum;
	}

	public String getOrderFlag() {
		return orderFlag;
	}

	public void setOrderFlag(String orderFlag) {
		this.orderFlag = orderFlag;
	}

	public String getSmsStr() {
		return smsStr;
	}

	public void setSmsStr(String smsStr) {
		this.smsStr = smsStr;
	}

	public String getTender() {
		return tender;
	}

	public void setTender(String tender) {
		this.tender = tender;
	}

	public Long getIsTwoCon() {
		return isTwoCon;
	}

	public void setIsTwoCon(Long isTwoCon) {
		this.isTwoCon = isTwoCon;
	}

	public Integer getOrder() {
		return order;
	}

	public void setOrder(Integer order) {
		this.order = order;
	}

	public List<String> getExpertTypeList() {
		return expertTypeList;
	}

	public void setExpertTypeList(List<String> expertTypeList) {
		this.expertTypeList = expertTypeList;
	}

	public String getOptType() {
		return optType;
	}

	public void setOptType(String optType) {
		this.optType = optType;
	}

	public String getBidTimeDay() {
		return bidTimeDay;
	}

	public void setBidTimeDay(String bidTimeDay) {
		this.bidTimeDay = bidTimeDay;
	}

	public String getTemplateId() {
		return templateId;
	}

	public void setTemplateId(String templateId) {
		this.templateId = templateId;
	}

	public Long getCurrentStatus() {
		return currentStatus;
	}

	public void setCurrentStatus(Long currentStatus) {
		this.currentStatus = currentStatus;
	}

	public String getVoiceType() {
		return voiceType;
	}

	public void setVoiceType(String voiceType) {
		this.voiceType = voiceType;
	}

	public String getVoiceSmsContent() {
		return voiceSmsContent;
	}

	public void setVoiceSmsContent(String voiceSmsContent) {
		this.voiceSmsContent = voiceSmsContent;
	}

	public String getSmsContent() {
		return smsContent;
	}

	public void setSmsContent(String smsContent) {
		this.smsContent = smsContent;
	}

	public List<ExtractDebarbEntity> getCompanyDebarbList() {
		return companyDebarbList;
	}

	public void setCompanyDebarbList(List<ExtractDebarbEntity> companyDebarbList) {
		this.companyDebarbList = companyDebarbList;
	}

	public List<ExtractDebarbEntity> getExtractDebarbList() {
		return extractDebarbList;
	}

	public void setExtractDebarbList(List<ExtractDebarbEntity> extractDebarbList) {
		this.extractDebarbList = extractDebarbList;
	}


	public Long getIsRule() {
		return isRule;
	}

	public void setIsRule(Long isRule) {
		this.isRule = isRule;
	}

	public Date getStartTime() {
		return startTime;
	}

	public void setStartTime(Date startTime) {
		this.startTime = startTime;
	}

	public Date getEndTime() {
		return endTime;
	}

	public void setEndTime(Date endTime) {
		this.endTime = endTime;
	}

	

	public Long getFrequency() {
		return frequency;
	}

	public void setFrequency(Long frequency) {
		this.frequency = frequency;
	}

	public Long getStartAge() {
		return startAge;
	}

	public void setStartAge(Long startAge) {
		this.startAge = startAge;
	}

	public Long getEndAge() {
		return endAge;
	}

	public void setEndAge(Long endAge) {
		this.endAge = endAge;
	}

	public String getCon_company() {
		return con_company;
	}

	public void setCon_company(String con_company) {
		this.con_company = con_company;
	}

	public List<String> getExtractsAvoidList() {
		return extractsAvoidList;
	}

	public void setExtractsAvoidList(List<String> extractsAvoidList) {
		this.extractsAvoidList = extractsAvoidList;
	}

	public String getTitleCode() {
		return titleCode;
	}

	public void setTitleCode(String titleCode) {
		this.titleCode = titleCode;
	}

	public String getExpertTypeNum() {
		return expertTypeNum;
	}

	public void setExpertTypeNum(String expertTypeNum) {
		this.expertTypeNum = expertTypeNum;
	}

	public String geteMethod() {
		return eMethod;
	}

	public void seteMethod(String eMethod) {
		this.eMethod = eMethod;
	}
	
}
