package com.hzw.sunflower.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzw.sunflower.dao.BondApplyRefundConfirmMapper;
import com.hzw.sunflower.entity.BondApplyRefundConfirm;
import com.hzw.sunflower.service.BondApplyRefundConfirmService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 保证金申请退还处室处长确认表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-20
 */
@Service
public class BondApplyRefundConfirmServiceImpl extends ServiceImpl<BondApplyRefundConfirmMapper, BondApplyRefundConfirm> implements BondApplyRefundConfirmService {
}
