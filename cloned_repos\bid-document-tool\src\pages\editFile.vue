<template>
<div>
 <div class="editFile">
      <!-- <Toolbar
        id="editor-toolbar-area"
        :editor="editorRef"
        :defaultConfig="toolbarConfig"
        :mode="'simple'"
      />
     
      <Editor
        v-model="valueHtml"
        id="editor-text-area"
        :defaultConfig="editorConfig"
        @onCreated="handleCreated"
      /> -->
      <Editor
        v-model="valueHtml"
        api-key="9e3u9g5cr921cuk0v1n1cw1lr19yrosn4ildx4mnw7ay0zk0"
        id="editor-text-area"
        :init="initConfig"
      /> 
  </div>
 <div class="footBtn">
    <el-button @click="nextStep(1)" type="info">上一步</el-button>
    <el-button @click="nextStep(3)" type="primary">下一步</el-button>
  </div>
</div>
</template>

<script setup>
import {ref, shallowRef, watch, onBeforeUnmount} from 'vue'
import '@wangeditor/editor/dist/css/style.css' 
// import { Editor, Toolbar } from '@wangeditor/editor-for-vue'
import Editor from "@tinymce/tinymce-vue";


const props = defineProps(['fileHtml'])

const editorRef = shallowRef()
const handleCreated = (editor) => {
      editorRef.value = editor // 记录 editor 实例，重要！
      //获取editor-text-area的高度
      // const editorTextArea = document.getElementById('editor-text-area')
      // const editorContainer = document.getElementById('editor-container')
      // editorTextArea.style.height = editorContainer.clientHeight - 100 + 'px'
      // editorTextArea.style.overflowY = 'auto'
}
const toolbarConfig = {
   includeKeys: ['bold', 'italic', 
   'underline', 'strikeThrough', 'headings',
    'list', 'justify', 'textBackgroundColor', 
    'link', 'quote', 'image', 'table', 'undo', 'redo',
    "header1","header2","header3","header4","header5",
    ],
   excludeKeys: ['fullScreen','insertVideo',"codeSelectLang","codeBlock"],
}
const editorConfig = ref({ 
  placeholder: '请输入内容...',
  MENU_CONF: {
    uploadImage: {
      // 自定义上传
      customUpload(file, insertFn) {
        // file转为base64
        const reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onload = function(e) {
          const base64 = e.target.result;
          insertFn(base64); // 插入图片
        };
      }
    }
  }
})

const initConfig = {
        language_url: "./zh_CN.js",
        language: "zh_CN",
        height: 630,
        plugins:"lists pagebreak table wordcount ",
        toolbar:"bold italic underline strikethrough | fontsizeselect | forecolor backcolor | alignleft aligncenter alignright alignjustify|bullist numlist |outdent indent blockquote | undo redo |  image | removeformat",
        branding: false,
        images_upload_handler:(blobInfo, success,failure)=> {
          success('data:image/jpeg;base64,' + blobInfo.base64())
        }
     }


const valueHtml = ref(props.fileHtml)
const emits = defineEmits(['nextStep', 'updateHtml'])
const nextStep = (val)=>{
  if(val === 3){
     emits('updateHtml', valueHtml.value)
  }
  emits('nextStep', val)
}

const pageCount = ref(0)
// 监听valueHtml变化，重新计算编辑器的高度
watch(valueHtml, (val) => {
    // const editorArea = document.getElementById('editor-text-area')
    // const height = editorArea.clientHeight
    // pageCount.value = Math.ceil(height / 1160)
})

// 组件销毁时，也及时销毁编辑器
onBeforeUnmount(() => {
    const editor = editorRef.value
    if (editor == null) return
    editor.destroy()
})
</script>

<style>
    .tox .tox-notifications-container .tox-notification {
      display: none;
    }
    #editor-toolbar-area{
        border-bottom: 1px solid rgb(204, 204, 204);
        border-top: 1px solid #e8e8e8;
        position: sticky;
        top: 100px;
        z-index: 1;
    }

    #editor-toolbar {
      width: 1350px;
      background-color: #FCFCFC;
      margin: 0 auto;
    }

    #content {
      height: calc(100% - 40px);
      background-color: rgb(245, 245, 245);
      overflow-y: auto;
      position: relative;
    }
    .pageUl{
      float: left;
    }
    .pageLi{
      overflow: hidden;
      padding: 15px 20px 0px 20px;
      margin-bottom:10px ;
      list-style: none;
      background: #f5f7fa;
    }
    .pageContent{
      width: 82px;
      height: 116px;
      background: #fff;
    }
    .pageCount{
      margin: 8px;
      text-align: center;
    }
    .page-break{
      display: block;
      page-break-after: always;
    }
    #editor-container {
      width: 820px;
      margin: 30px auto 150px auto;
      background-color: #fff;
      padding: 20px 50px 50px 50px;
      border: 1px solid #e8e8e8;
      box-shadow: 0 2px 10px rgb(0 0 0 / 12%);
    }
    #tinymce {
      width: 820px;
      margin: 30px auto 150px auto;
      background-color: #fff;
      padding: 20px 50px 50px 50px;
      border: 1px solid #e8e8e8;
      box-shadow: 0 2px 10px rgb(0 0 0 / 12%);
    }
    .tox .tox-sidebar-wrap{
       width: 820px;
       margin:20px auto;
       box-shadow: 0 2px 10px rgb(0 0 0 / 12%);
    }
    .tox-tinymce{
      height: 85vh !important;
    }
    .tox-statusbar, .tox-notifications-container{
      display: none !important;
    }
    #title-container {
      padding: 20px 0;
      border-bottom: 1px solid #e8e8e8;
    }

    #title-container input {
      font-size: 30px;
      border: 0;
      outline: none;
      width: 100%;
      line-height: 1;
    }

    #editor-text-area {
      min-height: 750px;
      margin-top: 20px;
      height: 75vh; 
      overflow-y: hidden;
      width: 820px;
      margin: 30px auto 150px auto;
      background-color: #fff;
      padding: 20px 20px 50px 20px;
      border: 1px solid #e8e8e8;
      box-shadow: 0 2px 10px rgb(0 0 0 / 12%);
    }
</style>