package com.hzw.sunflower.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hzw.sunflower.common.BaseController;
import com.hzw.sunflower.common.Paging;
import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.common.annotation.Log;
import com.hzw.sunflower.constant.MessageConstants;
import com.hzw.sunflower.constant.constantenum.BusinessType;
import com.hzw.sunflower.entity.UploadFile;
import com.hzw.sunflower.entity.condition.UploadFileCondition;
import com.hzw.sunflower.service.UploadFileService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * 上传文件存储表 Controller
 *
 * <AUTHOR>
 * @version 1.0.0 2021-04-07
 */
@Api(tags = "上传文件存储表 服务")
@RestController
@RequestMapping("/uploadFile")
public class UploadFileController extends BaseController {

    @Autowired
    private UploadFileService uploadFileService;

    @ApiOperation(value = "根据条件分页查询上传文件存储表 列表")
    @ApiImplicitParam(name = "condition", value = "上传文件存储表 查询条件", required = true, dataType = "UploadFileCondition", paramType = "body")
    @PostMapping("/list")
    public Paging<UploadFile> list(@RequestBody UploadFileCondition condition) {
        IPage<UploadFile> page = uploadFileService.findUploadFileByCondition(condition);
        return Paging.buildPaging(page);
    }

    @ApiOperation(value = "根据主键ID查询上传文件存储表 信息")
    @ApiImplicitParam(name = "id", value = "主键ID", required = true)
    @GetMapping(value = "/get/{id}")
    public Result<UploadFile> get(@PathVariable Long id) {
        if (id == null) {
            return Result.failed(MessageConstants.GET_NULL);
        }
        UploadFile uploadFile = uploadFileService.getUploadFileById(id);
        return Result.ok(uploadFile);
    }

    @ApiOperation(value = "新增上传文件存储表 信息")
    @Log(title = "上传文件存储", description = "新增上传文件", businessType = BusinessType.INSERT)
    @ApiImplicitParam(name = "uploadFile", value = "上传文件存储表 ", required = true, dataType = "UploadFile", paramType = "body")
    @PostMapping("/add")
    public Result<Boolean> add(@RequestBody UploadFile uploadFile) {
        Boolean bool = uploadFileService.addUploadFile(uploadFile);
        return Result.okOrFailed(bool);
    }

    @ApiOperation(value = "修改上传文件存储表 信息")
    @Log(title = "上传文件存储", description = "修改上传文件", businessType = BusinessType.UPDATE)
    @ApiImplicitParam(name = "uploadFile", value = "上传文件存储表 ", required = true, dataType = "UploadFile", paramType = "body")
    @PutMapping(value = "/update")
    public Result<Boolean> update(@RequestBody UploadFile uploadFile) {
        Long id = uploadFile.getId();
        if (id == null) {
            return Result.failed(MessageConstants.UPDATE_NULL);
        }
        Boolean bool = uploadFileService.updateUploadFile(uploadFile);
        return Result.okOrFailed(bool);
    }

    @ApiOperation(value = "根据主键ID删除上传文件存储表 ")
    @Log(title = "上传文件存储", description = "删除上传文件", businessType = BusinessType.DELETE)
    @ApiImplicitParam(name = "id", value = "主键ID", required = true)
    @DeleteMapping(value = "/delete/{id}")
    public Result<Boolean> delete(@PathVariable Long id) {
        if (id == null) {
            return Result.failed(MessageConstants.DELETE_NULL);
        }
        Boolean bool = uploadFileService.deleteUploadFileById(id);
        return Result.okOrFailed(bool);
    }

    @ApiOperation(value = "根据主键ID列表批量删除上传文件存储表 ")
    @Log(title = "上传文件存储", description = "批量删除上传文件", businessType = BusinessType.DELETE)
    @ApiImplicitParam(name = "idList", value = "主键ID列表", required = true, allowMultiple = true, paramType = "body")
    @DeleteMapping("/deleteList")
    public Result<Boolean> deleteList(@RequestBody List<Long> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return Result.failed(MessageConstants.DELETE_NULL);
        }
        Boolean bool = uploadFileService.deleteUploadFileByIds(idList);
        return Result.okOrFailed(bool);
    }
}