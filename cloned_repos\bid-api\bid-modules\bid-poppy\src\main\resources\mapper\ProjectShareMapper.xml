<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="com.hzw.sunflower.dao.ProjectShareMapper">

    <select id="findInfo" resultType="com.hzw.sunflower.entity.ProjectShare">
        SELECT
            s.*,
            IF( s.share_department_name LIKE CONCAT( '%', s.share_user_name, '%' ), d.department_name, s.share_department_name ) shareDepartmentName
        FROM
            t_project_share s
            LEFT JOIN t_department d ON s.share_department = d.id
            WHERE s.project_id = #{id}
    </select>
</mapper>
