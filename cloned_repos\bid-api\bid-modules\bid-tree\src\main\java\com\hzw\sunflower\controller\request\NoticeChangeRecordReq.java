package com.hzw.sunflower.controller.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 招标公告变更记录请求实体
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@ApiModel(description = "招标公告变更记录请求实体")
@Data
public class NoticeChangeRecordReq {

    @ApiModelProperty(value = "招标公告主键", position = 1)
    private Long noticeId;

    @ApiModelProperty(value = "变更次数", position = 2)
    private Integer submitNum;

    @ApiModelProperty(value = "标段id", position = 3)
    private Long sectionId;

    @ApiModelProperty(value = "变更次数", position = 4)
    private Integer latest;

    @ApiModelProperty(value = "项目id", position = 5)
    private Long projectId;
    /**
     * 招标公告阶段： 0：默认无阶段； 1：第一轮； 2：第二轮
     */
    @ApiModelProperty(value = "招标公告阶段", position = 6)
    private Integer bidRound;
}
