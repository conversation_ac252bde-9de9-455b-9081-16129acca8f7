package com.hzw.sunflower.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hzw.sunflower.dto.GroupSectionInfoDTO;
import com.hzw.sunflower.dto.SectionGroupDTO;
import com.hzw.sunflower.entity.SectionGroup;
import com.hzw.sunflower.entity.condition.SectionGroupCondition;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2021/8/27 11:31
 * @description：
 * @version: 1.0
 */
public interface SectionGroupService extends IService<SectionGroup> {
    /**
     * 根据条件分页查询列表
     *
     * @param condition 查询条件
     * @return 分页信息
     */
    IPage<SectionGroup> findInfoByCondition(SectionGroupCondition condition);

    /**
     * 根据主键ID查询信息
     *
     * @param id 主键ID
     * @return SectionGroup信息
     */
    SectionGroup getInfoById(Long id);

    /**
     * 新增
     *
     * @param sectionGroup
     * @return 是否成功
     */
    Boolean addInfo(SectionGroup sectionGroup);

    /**
     * 修改单位公司 信息
     *
     * @param sectionGroup
     * @return 是否成功
     */
    Boolean updateInfo(SectionGroup sectionGroup);

    /**
     * 根据主键ID删除
     *
     * @param id 主键ID
     * @return 是否成功
     */
    Boolean deleteById(Long id);

    /**
     * 根据主键ID列表批量删除
     *
     * @param idList 主键ID列表
     * @return 是否成功
     */
    Boolean deleteByIds(List<Long> idList);

    /**
     * 根据项目ID查询按套销售信息
     *
     * @param projectId
     * @return
     */
    List<SectionGroupDTO> getByProjectId(Long projectId);

    /**
     * 查询套金额信息
     *
     * @param groupId
     * @return
     */
    GroupSectionInfoDTO querySection(Long groupId);

    /**
     * 根据标包id查询不等于当前套id的套
     * @param projectId
     * @param subId
     * @param id
     * @return
     */
    SectionGroup queryGroupInfoPassId(Long projectId, Long subId, Long id);
}
