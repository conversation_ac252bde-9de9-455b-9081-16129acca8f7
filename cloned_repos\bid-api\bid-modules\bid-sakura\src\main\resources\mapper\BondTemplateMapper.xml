<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzw.sunflower.dao.BondTemplateMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hzw.sunflower.entity.BondTemplate">
        <id column="id" property="id" />
        <result column="bank_name" property="bankName" />
        <result column="template_name" property="templateName" />
        <result column="head_row" property="headRow" />
        <result column="water_number" property="waterNumber" />
        <result column="date" property="date" />
        <result column="time" property="time" />
        <result column="company_name" property="companyName" />
        <result column="company_account" property="companyAccount" />
        <result column="company_bank_deposit" property="companyBankDeposit" />
        <result column="company_bank_code" property="companyBankCode" />
        <result column="amount" property="amount" />
        <result column="receive_acount" property="receiveAcount" />
        <result column="receive_bank" property="receiveBank" />
        <result column="post_script" property="postScript" />
        <result column="created_user_id" property="createdUserId" />
        <result column="created_time" property="createdTime" />
        <result column="updated_user_id" property="updatedUserId" />
        <result column="updated_time" property="updatedTime" />
        <result column="is_delete" property="isDelete" />
        <result column="remark" property="remark" />
        <result column="version" property="version" />
    </resultMap>

    <!-- 分页查询模板 -->
    <select id="listPage" resultType="com.hzw.sunflower.dto.BondTemplateDto">
        SELECT
            t.*,
            u.user_name update_user_name
        FROM
            t_bond_template t
            LEFT JOIN t_user u ON u.id = t.updated_user_id
        where 1 = 1
        <if test="condition.keyWords != null and condition.keyWords != ''">
            and t.template_name like concat('%',#{condition.keyWords},'%')
        </if>
        order by t.updated_time desc
    </select>

</mapper>
