package com.hzw.sunflower.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hzw.sunflower.controller.response.BondCompanyBankVo;
import com.hzw.sunflower.entity.BondCompanyBank;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 供应商行号维护表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-28
 */
@Mapper
public interface BondCompanyBankMapper extends BaseMapper<BondCompanyBank> {

    /**
     * 根据companyId去供应商行号维护表中查询行号信息
     * @param companyId
     * @return
     */
    List<BondCompanyBankVo> getPayFileCompanyInfo(@Param("companyId") Long companyId,@Param("refundId") Long refundId);
}
