package com.hzw.sunflower.util.excel;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.hzw.sunflower.dto.BondWaterExcelDto;
import com.hzw.sunflower.util.BondUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 * @date ：Created in 2022/7/27 9:36
 * @description：excel导入监听
 * @modified By：`
 * @version: 1.0
 */
@Slf4j
public class ExcelListener<T> extends AnalysisEventListener<T> {

    /**
     * 仅支持的日期格式
     */
    public static final String FORMAT_LONG_DATE = "yyyy-MM-dd HH:mm:ss";
    public static final String FORMAT_SHORT_DATE = "yyyy-MM-dd";
    public static final String FORMAT_YYYYMMDD = "yyyyMMdd";
    public static final String FORMAT_YYYYMMDDHHMMSS = "yyyyMMddHHmmss";
    public static final String FORMAT_SHORT_DATE_OTHER = "yyyy/M/d";
    public static final String FORMAT_LONG_DATE_OTHER = "yyyy/M/d HH:mm:ss";

    private List<T> rows = new ArrayList<>();

    private List<T> failRows = new ArrayList<>();

    @Override
    public void invoke(T t, AnalysisContext context) {
        // 初步过滤格式
        BondWaterExcelDto water = (BondWaterExcelDto) t;
        // 所有参数去除空格换行
        BondUtils.beanAttributeValueTrim(water);
        //公司名称去各种空格
        if(StringUtils.isNotBlank(water.getCompanyName())){
            water.setCompanyName(water.getCompanyName().replaceAll("[\\s\\p{Z}]", ""));
        }
        // 校验金额格式
        if (checkAmount(context.getCurrentRowNum(), water)) {
            // 当金额小于0表示出账，不计入数据
            if (new BigDecimal(water.getAmount()).compareTo(new BigDecimal(0)) > 0) {
                if (checkStatement(context.getCurrentRowNum(), water)) {
                    rows.add(t);
                } else {
                    failRows.add(t);
                }
            }
        } else {
            failRows.add(t);
        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        log.info("解析完成！成功读取{}行，失败{}行。", rows.size(), failRows.size());
    }

    public Map<String, List<T>> getRowMap() {
        Map<String, List<T>> map = new HashMap<>();
        map.put("rows", rows);
        map.put("failRows", failRows);
        return map;
    }

    /**
     * 校验流水格式
     * @param rowNum
     * @param water
     * @return
     */
    public static boolean checkStatement (Integer rowNum, BondWaterExcelDto water) {
        if (StringUtils.isBlank(water.getWaterNumber())) {
            log.info("第{}行导入失败，流水号不能为空", rowNum);
            water.setRemark("流水号不能为空");
            return false;
        }
        if (StringUtils.isBlank(water.getDate())) {
            log.info("第{}行导入失败，交易日期不能为空", rowNum);
            water.setRemark("交易日期不能为空");
            return false;
        } else if (!checkDate(water)) {
            log.info("第{}行导入失败，交易日期格式有误", rowNum);
            water.setRemark("交易日期格式有误");
            return false;
        }
        if (StringUtils.isBlank(water.getCompanyName())) {
            log.info("第{}行导入失败，付款户名不能为空", rowNum);
            water.setRemark("付款户名不能为空");
            return false;
        }
        if (StringUtils.isBlank(water.getCompanyAccount())) {
            log.info("第{}行导入失败，付款账户不能为空", rowNum);
            water.setRemark("付款账户不能为空");
            return false;
        }
        if (StringUtils.isBlank(water.getCompanyBankDeposit())) {
            log.info("第{}行导入失败，付款开户行不能为空", rowNum);
            water.setRemark("付款开户行不能为空");
            return false;
        }
        return true;
    }

    /**
     * 校验金额，当金额为空或者小于等于0 过滤数据
     * @param rowNum
     * @param water
     * @return
     */
    public static Boolean checkAmount (Integer rowNum, BondWaterExcelDto water) {
        // log.info("导入的金额：" + water.getExcelAmount());
        // 当金额小于等于0时，过滤此数据,为null为格式错误
        if (StringUtils.isBlank(water.getAmount())) {
            water.setAmount("0");
        } else {
            // 转换,
            water.setAmount(water.getAmount().replace(",", "").replace("，", ""));
            // 保留2位小数并四舍五入
            try {
                BigDecimal bigDecimal = new BigDecimal(water.getAmount());
                bigDecimal = bigDecimal.setScale(2, RoundingMode.HALF_UP);
                water.setAmount(bigDecimal.toString());
            } catch (Exception e) {
                log.info("第{}行导入失败，交易金额格式错误", rowNum);
                water.setRemark("交易金额格式错误");
                return false;
            }
        }
        return true;
    }

    /**
     * 校验日期格式 只能为yyyyMMdd、yyyy-mm-dd、yyyy/m/d或yyyy-MM-dd hh:mm:ss
     * @param water
     * @return
     */
    public static boolean checkDate (BondWaterExcelDto water) {
        // 先转换格式
        String date = water.getDate();
        String time = water.getTime() == null ? "00:00:00" : water.getTime();
        // 当类型为yyyy/m/d时，转换为yyyy-mm-dd
        if (isLegalDate(date, FORMAT_SHORT_DATE_OTHER)) {
            date = transferDate(date, FORMAT_SHORT_DATE_OTHER, FORMAT_SHORT_DATE);
        }
        // 当类型为yyyyMMdd时，转换为yyyy-mm-dd
        if (isLegalDate(date, FORMAT_YYYYMMDD)) {
            // 当类型为yyyyMMdd，时间格式为hhmmss 转换为yyyy-MM-dd hh:mm:ss
            if (isLegalDate(date, FORMAT_YYYYMMDD) && isLegalDate(date + "" + time, FORMAT_YYYYMMDDHHMMSS)) {
                date = transferDate(date + "" + time, FORMAT_YYYYMMDDHHMMSS, FORMAT_LONG_DATE);
            }else{
                date = transferDate(date, FORMAT_YYYYMMDD, FORMAT_SHORT_DATE);
            }
        }
        // 当类型为yyyy-mm-dd，还需校验交易时间的格式
        if (isLegalDate(date, FORMAT_SHORT_DATE) && isLegalDate(date + " " + time, FORMAT_LONG_DATE)) {
            water.setDate(date);
            return true;
        }

        // 当类型为yyyy/m/d hh:mm:ss时，转换为yyyy-MM-dd hh:mm:ss
        if (isLegalDate(date, FORMAT_LONG_DATE_OTHER)) {
            date = transferDate(date, FORMAT_LONG_DATE_OTHER, FORMAT_LONG_DATE);
        }
        // 当类型为yyyy-MM-dd hh:mm:ss时，拆分日期和时间
        if (isLegalDate(date, FORMAT_LONG_DATE)) {
            water.setDate(date.substring(0, 10));
            water.setTime(date.substring(11, date.length()));
            return true;
        }
        return false;
    }

    /**
     * 校验日期格式
     * @param dateStr
     * @param pattern
     * @return
     */
    private static boolean isLegalDate(String dateStr, String pattern) {
        if (dateStr == null) {
            return false;
        }
        DateFormat formatter = new SimpleDateFormat(pattern);
        try {
            Date date = formatter.parse(dateStr);
            return dateStr.equals(formatter.format(date));
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 转换日期格式
     * @param dateStr
     * @param pattern
     * @return
     */
    private static String transferDate(String dateStr, String pattern, String transferPattern) {
        DateFormat formatterOther = new SimpleDateFormat(pattern);
        DateFormat formatter = new SimpleDateFormat(transferPattern);
        try {
            Date date = formatterOther.parse(dateStr);
            return formatter.format(date);
        } catch (Exception e) {
            log.error("时间转换异常");
            return null;
        }
    }

}
