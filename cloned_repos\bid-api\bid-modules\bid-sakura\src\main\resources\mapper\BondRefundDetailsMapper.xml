<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzw.sunflower.dao.BondRefundDetailsMapper">

    <select id="getDetatisForRefund" resultType="com.hzw.sunflower.entity.BondRefundDetails">
        SELECT
            *
        FROM
            t_bond_refund_details
        WHERE
                is_delete = 0
                AND refund_id = #{refundId}
                AND (
                    STATUS NOT IN ( 3, 5 )
                    OR agency_refund_status NOT IN (3,5)
                )
    </select>


    <select id="getSyncNccRefundInfo" resultType="com.hzw.sunflower.dto.SyncNccRefundInfoDto">
       SELECT
            (case when t.province_in_out = 2 then 'W' else 'N' end) provinceFlag,
            t.purchase_number,
            t.customer,
            t.pkPsndoc,
            t.pkDeptId,
            t.receiveAcount,
            ( CASE WHEN t.refund_money = 0 THEN 0 ELSE t.rates END ) AS localTaxDe,
            ( CASE WHEN t.refund_money = 0 THEN 0 ELSE ( t.refund_money - t.rates ) END ) noTaxDe,
            t.refund_money AS moneyDe,
            t.agency_fee,
            t.bond_money,
            t.from_open_bank,
            t.from_bank_number,
            t.amount_date
        FROM
            (
            SELECT
                p.province_in_out,
                (case when pbs.package_number is not null then CONCAT(p.purchase_number,'/',pbs.package_number) else p.purchase_number end) as purchase_number,
                c.`code` AS customer,
                psn.share_user_code  AS pkPsndoc,
                ( CASE WHEN  psn.share_department_code IS NULL THEN '9999' ELSE  psn.share_department_code END ) AS pkDeptId,
                (
                CASE

                        WHEN INSTR( rd.from_open_bank, '中国银行' ) THEN
                        ( SELECT account FROM t_bankdata_bond_to_ncc WHERE bank_type = 1 AND account_type = 2 )
                        WHEN INSTR( rd.from_open_bank, '南京银行' ) THEN
                        ( SELECT account FROM t_bankdata_bond_to_ncc WHERE bank_type = 2 AND account_type = 2 )
                        WHEN INSTR( rd.from_open_bank, '民生银行' ) THEN
                        ( SELECT account FROM t_bankdata_bond_to_ncc WHERE bank_type = 3 AND account_type = 2 )
                        WHEN INSTR( rd.from_open_bank, '工商银行' ) THEN
                        ( SELECT account FROM t_bankdata_bond_to_ncc WHERE bank_type = 4 AND account_type = 2 )
                        WHEN INSTR( rd.from_open_bank, '建设银行' ) THEN
                        ( SELECT account FROM t_bankdata_bond_to_ncc WHERE bank_type = 5 AND account_type = 2 )
                        WHEN INSTR( rd.from_open_bank, '中信银行' ) THEN
                        ( SELECT account FROM t_bankdata_bond_to_ncc WHERE bank_type = 6 AND account_type = 2 )
                        WHEN INSTR( rd.from_open_bank, '华夏银行' ) THEN
                        ( SELECT account FROM t_bankdata_bond_to_ncc WHERE bank_type = 7 AND account_type = 2 )
                         WHEN INSTR( rd.from_open_bank, '农业银行' ) THEN
                        ( SELECT account FROM t_bankdata_bond_to_ncc WHERE bank_type = 8 AND account_type = 2 )
                         WHEN INSTR( rd.from_open_bank, '浦发银行' ) THEN
                        ( SELECT account FROM t_bankdata_bond_to_ncc WHERE bank_type = 9 AND account_type = 2 )
                         WHEN INSTR( rd.from_open_bank, '渤海银行' ) THEN
                        ( SELECT account FROM t_bankdata_bond_to_ncc WHERE bank_type = 10 AND account_type = 2 )
                        WHEN INSTR( rd.from_open_bank, '江苏银行' ) THEN
                        ( SELECT account FROM t_bankdata_bond_to_ncc WHERE bank_type = 11 AND account_type = 2 )
                        WHEN INSTR( rd.from_open_bank, '邮储银行' ) THEN
                            ( SELECT account FROM t_bankdata_bond_to_ncc WHERE bank_type = 12 AND account_type = 2 )
                    END
                    ) AS receiveAcount,
                    ( CASE WHEN rd.rates IS NULL THEN 0 ELSE rd.rates END ) AS rates,
                    rd.refund_money,
                    rd.agency_fee,
                    rd.bond_money,
                    rd.from_open_bank,
                    rd.from_bank_number,
                    rd.amount_date
                FROM
                    t_bond_refund r
                    LEFT JOIN t_bond_refund_details rd ON r.id = rd.refund_id
                    LEFT JOIN t_project_bid_section pbs ON pbs.id = r.section_id
                    AND pbs.is_delete = 0
                    LEFT JOIN t_project p ON pbs.project_id = p.id
                    AND p.is_delete = 0
                    LEFT JOIN t_customer_to_ncc c ON rd.refund_company_name = c.`name`
                    AND c.is_delete = 0
                    LEFT JOIN (select * from t_project_share_ncc where is_delete= 0  ) psn ON psn.project_id = p.id
                WHERE
                    r.is_delete = 0
                    AND rd.is_delete = 0
                AND r.id = #{refundId}
            ) t
    </select>
</mapper>
