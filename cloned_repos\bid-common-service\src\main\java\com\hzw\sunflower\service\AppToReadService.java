package com.hzw.sunflower.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hzw.sunflower.controller.request.AppTaskCondition;
import com.hzw.sunflower.controller.response.AppToReadVo;
import com.hzw.sunflower.entity.AppToRead;

import java.util.Map;

/**
 * <p>
 * 待阅表 服务类
 * </p>
 *
 * <AUTHOR> Gen
 * @since 2022-12-15
 */
public interface AppToReadService extends IService<AppToRead> {

    /**
     * 分页查询待阅
     * @param condition
     * @return
     */
    IPage<AppToReadVo> listPage(AppTaskCondition condition);

    /**
     * 消息中心全部列表
     * @param condition
     * @return
     */
    IPage<AppToReadVo> listAllPage(AppTaskCondition condition);

    /**
     * 消息中心数量统计
     * @param condition
     * @return
     */
    Map<String, Long> countAll(AppTaskCondition condition);
}
