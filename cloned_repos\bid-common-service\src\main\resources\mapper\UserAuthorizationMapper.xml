<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hzw.sunflower.dao.UserAuthorizationMapper">

    <select id="getUserAuthorizationInfo" resultType="com.hzw.sunflower.dto.UserAuthorizationDto">
        SELECT
            t.*
        FROM
            (
            SELECT
                ui.user_id,
                ui.identity,
                uap.purchase_number,
                ( CASE WHEN ua.authorization_type IS NULL THEN 1 ELSE ua.authorization_type END ) AS authorization_type,
                uap.legal_person_authorization,
                uap.created_time
            FROM
                t_user_identity ui
                LEFT JOIN t_user_authorization ua ON ui.user_id = ua.user_id
                AND ui.identity = ua.identity
                AND ua.is_delete = 0
                LEFT JOIN t_user_authorization_project uap ON ua.user_id = uap.user_id
                AND uap.is_delete = 0
            WHERE
                ui.is_delete = 0
                AND ui.user_id = #{userId}
                AND ui.identity = #{identity}
            ) t
        ORDER BY
            t.created_time DESC LIMIT 1
    </select>
</mapper>
