package com.hzw.common.basebean.web.domain;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.hzw.common.basebean.annotation.IgnoreCompare;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * Entity基类
 *
 * <AUTHOR>
 */

@Data
public class BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 搜索值
     */
/*    @JsonIgnore
    @TableField(exist = false)
    private String searchValue;*/

    /**
     * 删除状态
     */
    @TableLogic
    private Integer isDelete;

    /**
     * 创建者
     */
    @IgnoreCompare
    @TableField(fill = FieldFill.INSERT)
    private String createdUserId;



    /**
     * 创建时间
     */
    @IgnoreCompare
    @TableField(fill = FieldFill.INSERT)
    private Date createdTime;

    /**
     * 更新者
     */
    @IgnoreCompare
    @TableField(fill = FieldFill.INSERT)
    private String updatedUserId;

    /**
     * 更新时间
     */
    @IgnoreCompare
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updatedTime;

    /**
     * 备注
     */
    //@TableField(fill = FieldFill.INSERT_UPDATE)
    private String remark;

    /**
     * 版本号控制并发
     */
//    @TableField(fill = FieldFill.INSERT_UPDATE)
//    private String version;

    /**
     * 请求参数
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @TableField(exist = false)
    private Map<String, Object> params = new HashMap<>();

}
