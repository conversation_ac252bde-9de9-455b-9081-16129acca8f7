package com.hzw.expertfee.api.common;


import com.hzw.sunflower.constant.GeneralConstants;
import com.hzw.sunflower.entity.JwtUser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 用于其他Controller继承的基础Controller
 *
 * <AUTHOR>
 * @version 1.0.0 2021-03-31
 */
@Slf4j
public abstract class BaseController {
    @Autowired
    protected HttpServletRequest request;

    @Autowired
    protected HttpServletResponse response;


//    /**
//     * <AUTHOR>
//     * @Description 获取当前用户的权限
//     * @Return java.util.List<java.lang.String>
//     * @Date 2021/4/28 10:16
//     */
//    public List<String> getAuthentication() {
//        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
//        Collection<? extends GrantedAuthority> authorities = authentication.getAuthorities();
//        List<String> list = new ArrayList<>();
//        for (GrantedAuthority grantedAuthority : authorities) {
//            log.info("权限列表：{}", grantedAuthority.getAuthority());
//            list.add(grantedAuthority.getAuthority());
//        }
//        return list;
//    }
//
//    /**
//     * 判断是否是管理员
//     *
//     * @return
//     */
//    public boolean isAdmin() {
//        List<String> roles = getAuthentication();
//        for (String role : roles) {
//            if (GeneralConstants.ROLE_CODE_ADMIN.equals(role)) {
//                return true;
//            }
//        }
//        return false;
//    }
//
    /**
     * 获取当前登录用户
     *
     * @return
     */
    public JwtUser getJwtUser() {
        // 获取 token
        String token = request.getHeader(GeneralConstants.TOKEN);

        String jwtToken = token.replace(GeneralConstants.TOKEN_PREFIX, "");

        JwtUser jwtUser = new JwtUser(jwtToken);
        return jwtUser;
    }
//
//    /**
//     * 获取当前用户身份
//     *
//     * @return
//     */
//    public EnterSourceEnum getUserIdentity() {
//        return EnterSourceEnum.getEnterSource(getJwtUser().getUserIdentity());
//    }
}
