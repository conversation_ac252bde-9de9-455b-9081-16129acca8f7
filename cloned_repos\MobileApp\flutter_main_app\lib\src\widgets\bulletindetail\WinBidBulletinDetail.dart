import "package:flutter/material.dart";
import 'package:flutter_main_app/src/components/CustomAppBar.dart';
import 'package:flutter_main_app/src/models/bulletin/WinBidModel.dart';
import 'package:flutter_main_app/src/widgets/bulletindetail/BulletinContact.dart';
import 'package:flutter_main_app/src/widgets/bulletindetail/BulletinDetailHeader.dart';
import 'package:flutter_main_app/src/widgets/bulletindetail/BulletinBottomBtn.dart';

class WinBidBulletinDetail extends StatefulWidget {
  const WinBidBulletinDetail({Key key}) : super(key: key);

  /// * 路由名称
  static const String _routeName = 'WinBidBulletinDetail';
  static String get routeName => _routeName;

  @override
  _WinBidBulletinDetailState createState() => _WinBidBulletinDetailState();
}

class _WinBidBulletinDetailState extends State<WinBidBulletinDetail> {
  final TextStyle titleTextStyle = TextStyle(
    color: Color(0xFF414141),
    fontWeight: FontWeight.bold,
    fontSize: 13,
  );

  final TextStyle normalTextStyle = TextStyle(
    color: Color(0xFF414141),
    fontSize: 13,
    height: 2,
  );

  final TextStyle tableTextStyle = TextStyle(
    color: Color(0xFF414141),
    fontSize: 13,
    height: 1,
  );
  /// 标题的Padding
  final EdgeInsetsGeometry titlePadding = EdgeInsets.fromLTRB(15, 0, 15, 12);

  List<WinBidModel> winBids = <WinBidModel>[];

  void initWinBids() {
    winBids.add(new WinBidModel(
      winBidName: '无锡同育科技有限公司',
      socialCertNo: '91320213MA1PC6J98A',
      address: '无锡市学前东路789-6116',
      bidPrice: '125000元',
      period: '签订合同之日起30日内所有设备安装调试完成',
      quailfyRequirement: '合格',
    ));
     winBids.add(new WinBidModel(
      winBidName: '无锡市华海电子电器有限公司',
      socialCertNo: '91320213MA1PC6J98A',
      address: '无锡市滨湖区南湖中路28-45号',
      bidPrice: '117900元',
      period: '签订合同之日起3日',
      quailfyRequirement: '合格',
    ));
  }

  Widget _createWinBidList(List<WinBidModel> winBids) {
    List<Table> tables = <Table>[];
    for (var winBid in winBids) {
      Table table = Table(
        columnWidths: {
          0: FractionColumnWidth(0.4),
          1: FractionColumnWidth(0.6)
        },
        border: TableBorder.all(
          color: Color(0xFFECECEC),
          width: 1,
        ),
        children: <TableRow>[
          TableRow(
            children: <Widget>[
              Container(
                constraints: BoxConstraints(minHeight: 33),
                child: Center(child:Text('中标供应商名称',style: tableTextStyle))
              ),
              Container(
                constraints: BoxConstraints(minHeight: 33),
                child: Center(child:Text(winBid.winBidName,style: tableTextStyle))
              )
              
            ]
          ),

          TableRow(
            children: <Widget>[
              Container(
                constraints: BoxConstraints(minHeight: 33),
                child: Center(child:Text('统一社会信用代码',style: tableTextStyle))
              ),
              Container(
                constraints: BoxConstraints(minHeight: 33),
                child: Center(child:Text(winBid.socialCertNo,style: tableTextStyle))
              )
              
            ]
          ),

          TableRow(
            children: <Widget>[
              Container(
                constraints: BoxConstraints(minHeight: 33),
                child: Center(child:Text('中标供应商地址',style: tableTextStyle))
              ),
              Container(
                constraints: BoxConstraints(minHeight: 33),
                child: Center(child:Text(winBid.address,style: tableTextStyle))
              )
              
            ]
          ),

          TableRow(
            children: <Widget>[
              Container(
                constraints: BoxConstraints(minHeight: 33),
                child: Center(child:Text('中标金额',style: tableTextStyle))
              ),
              Container(
                constraints: BoxConstraints(minHeight: 33),
                child: Center(child:Text(winBid.bidPrice,style: tableTextStyle))
              )
              
            ]
          ),

          TableRow(
            children: <Widget>[
              Container(
                constraints: BoxConstraints(minHeight: 33),
                child: Center(child:Text('供货期',style: tableTextStyle))
              ),
              Container(
                constraints: BoxConstraints(minHeight: 33),
                child: Center(child:Text(winBid.period,style: tableTextStyle))
              )
              
            ]
          ),

          TableRow(
            children: <Widget>[
              Container(
                constraints: BoxConstraints(minHeight: 33),
                child: Center(child:Text('质量要求',style: tableTextStyle))
              ),
              Container(
                constraints: BoxConstraints(minHeight: 33),
                child: Center(child:Text(winBid.quailfyRequirement,style: tableTextStyle))
              )
              
            ]
          ),
        ]);
        tables.add(table);
    }

    Column bidListCoumnt = Column(
      children: <Widget>[

      ],
      crossAxisAlignment: CrossAxisAlignment.start,
    );
    for(int i=0;i<tables.length;i++) {
      bidListCoumnt.children.add(
        Padding(
          padding: EdgeInsets.only(bottom: 8),
          child: Text('标段${i+1}',style: tableTextStyle,)
        )
      );

       bidListCoumnt.children.add(
        Padding(
          padding: EdgeInsets.only(bottom: 8),
          child: tables[i],
        )
      );
    }
    return bidListCoumnt;
  }

  @override
  void initState() {
    super.initState();
    this.initWinBids();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: '中标候选人公示',
        actions: <Widget>[
          IconButton(
            icon: Icon(Icons.share, color: Colors.white),
            onPressed: null,
            iconSize: 20,
          )
        ],
      ),
      body: Container(
        color: Colors.white,
        child: Stack(
          children: <Widget>[
            ListView(
              children: <Widget>[
                BulletinDetailHeader(),

                Padding(
                  padding: EdgeInsets.fromLTRB(15, 18, 15, 12),
                  child: Text(
                   '1. 中标人信息',
                   style: titleTextStyle, 
                  ),
                ),

                Padding(
                  padding: EdgeInsets.fromLTRB(15, 0, 15, 12),
                  child: Text(
                   '标段包[001]江苏省惠山中等专业学校实训工量具采购：',
                   style: normalTextStyle, 
                  ),
                ),

                Padding(
                  padding: EdgeInsets.fromLTRB(15, 0, 15, 12),
                  child: Column(
                    children: <Widget>[
                      Row(
                        children: <Widget>[
                          Text(
                           '中标人：',
                           style: normalTextStyle, 
                          ),
                          Text(
                           '无锡同育科技有限公司（一标段）',
                           style: normalTextStyle, 
                          ),
                        ],
                      ),
                      Row(
                        children: <Widget>[
                          Text(
                           '中标价格：',
                           style: normalTextStyle, 
                          ),
                          Text(
                           '12.5万元',
                           style: normalTextStyle, 
                          ),
                        ],
                      ),
                      Row(
                        children: <Widget>[
                          Text(
                           '中标人：',
                           style: normalTextStyle, 
                          ),
                          Text(
                           '无锡市华海电子电器有限公司（二标段）',
                           style: normalTextStyle, 
                          ),
                        ],
                      ),
                      Row(
                        children: <Widget>[
                          Text(
                           '中标价格：',
                           style: normalTextStyle, 
                          ),
                          Text(
                           '11.79万元',
                           style: normalTextStyle, 
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              
                Padding(
                  padding: EdgeInsets.fromLTRB(15, 18, 15, 12),
                  child: Text(
                   '2. 其他',
                   style: titleTextStyle, 
                  ),
                ),

                Padding(
                  padding: EdgeInsets.fromLTRB(15, 0, 15, 12),
                  child: Text(
                   '（1）采购项目名称及编号：',
                   style: normalTextStyle, 
                  ),
                ),

                Padding(
                  padding: EdgeInsets.fromLTRB(15, 0, 15, 12),
                  child: Text(
                   '江苏省惠山中等专业学校实训工量具采购项目 JSJGHSZZ2019-001',
                   style: normalTextStyle, 
                  ),
                ),

                Padding(
                  padding: EdgeInsets.fromLTRB(15, 0, 15, 12),
                  child: Text(
                   '（2）采购项目简要说明：',
                   style: normalTextStyle, 
                  ),
                ),

                Padding(
                  padding: EdgeInsets.fromLTRB(15, 0, 15, 12),
                  child: Text.rich(
                    TextSpan(
                      style: normalTextStyle,
                      children: <TextSpan>[
                        TextSpan(
                          text: '①采购内容：惠山中等专业学校实训机械类工量具、电工工具\r\n'
                        ),
                        TextSpan(
                          text: '本项目分为两个标段：\r\n'
                        ),
                        TextSpan(
                          text: ' 一标段：机械类工具\r\n'
                        ),
                        TextSpan(
                          text: '二标段：电工工具\r\n'
                        ),
                        TextSpan(
                          text: '②供货期：签订合同之日起30日内所有设备安装调试完成 \r\n',
                        ),
                        TextSpan(
                          text: '③质量要求：合格 \r\n',
                        ),
                        TextSpan(
                          text: '④本项目预算最高限价：\r\n',
                        ),
                        TextSpan(
                          text: '一标段：13万元 \r\n',
                        ),
                        TextSpan(
                          text:'二标段：12万元\r\n',
                        )
                      ],
                    )
                  )
                ),

                Padding(
                  padding: EdgeInsets.fromLTRB(15, 0, 15, 12),
                  child: Text(
                   '（3）采购公告媒体及日期：',
                   style: normalTextStyle, 
                  ),
                ),

                Padding(
                  padding: EdgeInsets.fromLTRB(15, 0, 15, 12),
                  child: Text(
                   '江苏招标投标公共服务平台 2019年10月10日',
                   style: normalTextStyle, 
                  ),
                ),

                Padding(
                  padding: EdgeInsets.fromLTRB(15, 0, 15, 12),
                  child: Text(
                   '（4）开标、评审信息：',
                   style: normalTextStyle, 
                  ),
                ),

                Padding(
                  padding: EdgeInsets.fromLTRB(15, 0, 15, 12),
                  child: Text.rich(
                    TextSpan(
                      style: normalTextStyle,
                      children: <TextSpan>[
                        TextSpan(
                          text: '谈判日期：2019年10月21日\r\n'
                        ),
                        TextSpan(
                          text: '谈判地点：江苏金港项目管理有限公司（无锡市新吴区湘江路金源国际B栋921室\r\n'
                        ),
                        TextSpan(
                          text: '谈判小组成员名单：郭永威、何海强、何宪君\r\n'
                        )
                      ],
                    )
                  )
                ),

                Padding(
                  padding: EdgeInsets.fromLTRB(15, 0, 15, 12),
                  child: Text(
                   '（5）中标信息：',
                   style: normalTextStyle, 
                  ),
                ),

                _createWinBidList(this.winBids),

                Padding(
                  padding: EdgeInsets.fromLTRB(15, 0, 15, 12),
                  child: Text(
                   '（6）本公告期限：',
                   style: normalTextStyle, 
                  ),
                ),

                Padding(
                  padding: EdgeInsets.fromLTRB(15, 0, 15, 12),
                  child: Text(
                   '一个工作日',
                   style: normalTextStyle, 
                  ),
                ),

                Padding(
                  padding: EdgeInsets.fromLTRB(15, 0, 15, 12),
                  child: Text(
                   '（7）采购代理服务费：',
                   style: normalTextStyle, 
                  ),
                ),

                Padding(
                  padding: EdgeInsets.fromLTRB(15, 0, 15, 12),
                  child: Text.rich(
                    TextSpan(
                       style: normalTextStyle, 
                       children: <TextSpan>[
                         TextSpan(
                           text: '①代理费金额：一标段：1875元；二标段：1769元 \r\n',
                         ),
                         TextSpan(
                           text: '②本项目代理服务费由中标方支付，按国家纪委计价格[2002]1980号《招标代理服务收费管理暂行办法》所规定的收费标准（服务招标）计取。\r\n'
                         )
                       ]
                    )
                  ),
                ),

                Padding(
                  padding: EdgeInsets.fromLTRB(15, 18, 15, 12),
                  child: Text(
                   '3. 监督部门',
                   style: titleTextStyle, 
                  ),
                ),
                Padding(
                  padding: EdgeInsets.fromLTRB(15, 0, 15, 12),
                  child: Text(
                   '本招标项目的监督部门为江苏中烟工业有限责任公司。',
                   style: normalTextStyle, 
                  ),
                ),

                Padding(
                  padding: EdgeInsets.fromLTRB(15, 18, 15, 12),
                  child: Text(
                   '4. 联系方式',
                   style: titleTextStyle, 
                  ),
                ),
                BullectinContact()
              ],
            ),
            Align(
              alignment: Alignment.bottomCenter,
              child: BulletinBottomBtn(),
            )
          ],
        )
      )  
    );
  }
}