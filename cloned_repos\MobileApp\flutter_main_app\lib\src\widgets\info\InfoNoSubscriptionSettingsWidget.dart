///***
/// * 信息订阅 - 无订阅服务设置的页面
/// *
/// * <AUTHOR>
/// * @date 20191015
/// */

import 'package:flutter/material.dart';

import 'package:flutter_main_app/src/constants/theme.dart';

import 'package:flutter_main_app/src/components/CustomSafeArea.dart';

class InfoNoSubscriptionSettingsWidget extends StatelessWidget {

  const InfoNoSubscriptionSettingsWidget({
    Key key,
    this.pushNavigatorToKeywordSettings,
  }) : super(key: key);

  final void Function(BuildContext) pushNavigatorToKeywordSettings;

  @override
  Widget build(BuildContext context) {
    return CustomSafeArea(
      backgroundColor: themeContentBackgroundColor,
      child: Center(
        child: SingleChildScrollView(
          physics: BouncingScrollPhysics(),
          child: Column(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: <Widget>[
              Image.asset(
                'assets/images/info_blank_page.png',
                width: 104.0,
                height: 143.0,
              ),
              Padding(
                padding: const EdgeInsets.only(top: 7.0),
                child: Text(
                  '您还没有订阅服务设置',
                  style: TextStyle(
                    color: themeTextColor,
                    fontSize: 16.0,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              Padding(
                padding: const EdgeInsets.fromLTRB(68.0, 16.0, 68.0, 0.0),
                child: Text(
                  '订阅设置后，第一时间发送符合条件的招标信息给您，再也不会错过商机啦！',
                  style: TextStyle(
                    color: themeTipsTextColor,
                    fontSize: 14.0,
                  ),
                ),
              ),
              Container(
                margin: const EdgeInsets.only(top: 30.0),
                width: 140.0,
                height: 40.0,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.all(Radius.circular(themeBorderRadius)),
                ),
                child: FlatButton(
                  color: themeActiveColor,
                  textColor: const Color(0xffffffff),
                  onPressed: () {
                    this.pushNavigatorToKeywordSettings(context);
                  },
                  child: Text(
                    '立即设置',
                    style: TextStyle(
                      fontSize: 14.0,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

}
