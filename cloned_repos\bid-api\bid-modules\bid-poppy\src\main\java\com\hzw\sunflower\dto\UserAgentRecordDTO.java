package com.hzw.sunflower.dto;

import com.hzw.sunflower.entity.UserAgentRecord;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 用户创建代理的记录数据库返回实体
 *
 * <AUTHOR>
 * @version 1.0.0 2021-04-14
 */
@ApiModel(description = "用户创建代理的记录")
@Data
public class UserAgentRecordDTO extends UserAgentRecord {

    @ApiModelProperty(value = "部门id")
    private Long departmentId;

    @ApiModelProperty(value = "部门名称")
    private String departmentName;

}