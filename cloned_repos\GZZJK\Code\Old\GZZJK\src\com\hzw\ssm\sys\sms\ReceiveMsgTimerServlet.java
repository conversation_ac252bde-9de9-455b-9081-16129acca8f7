package com.hzw.ssm.sys.sms;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

import javax.servlet.ServletConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServlet;

import com.hzw.ssm.sys.sms.mwutil.MO;

public class ReceiveMsgTimerServlet extends HttpServlet {
	String contextPath = "";

	public void init(ServletConfig config) throws ServletException {
		System.out.println("init ReceiveMsgTimerServlet");
		//contextPath = config.getServletContext().getRealPath("/upload/notice/");
		// System.out.println(contextPath);
	}

	public void service(ServletRequest request, ServletResponse response) throws ServletException, IOException {
		System.out.println("start ReceiveMsgTimerServlet at  " + new Date());
		
		//实例化
		//DBQuery db = new DBQuery("oracle");
		
		SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		System.err.println("接收时间为：" + format.format(new Date()));

		List<MO> moList = SMSNewUtil.getSMSNewUtil().getMo(20);
		if (null != moList && !moList.isEmpty())
		{
			System.out.println("接受数据为： " + moList.size() + " 条。");
			for (MO mo : moList) {
				String extNum = mo.getExno();     // 扩展号
				String mobile = mo.getMobile();                                      // 手机号码
				String content = mo.getContent();                                     // 短信回复内容
				String replyTime = mo.getRtime();                                  // 短信回复时间
				System.out.println("接受短信：" + mobile + "--" + content + "--" + replyTime + "--" + extNum);
				//String sql = "update t_apply_info ai set ai.sms_msg = '" + content + "', ai.sms_time = to_date('" + replyTime + "', 'yyyy-MM-dd hh24:mi:ss') where ai.f_mobile = '" + mobile + "' and ai.sms_ext = " + extNum
				//	+ " and ai.apply_id in (select distinct ai.apply_id from t_apply_info ai left join t_project p on ai.project_id = p.project_id left join t_purchase_file pf on pf.project_id = ai.project_id"
				//	+ " where (to_char(pf.submit_end_time, 'yyyy-mm-dd') between (select to_char(sysdate + 1, 'yyyy-mm-dd') from dual) and (select to_char(sysdate + 2, 'yyyy-mm-dd') from dual)) and p.delete_flag = 0"
				//	+ " and ai.f_mobile = '" + mobile + "' and ai.sms_ext = " + extNum + " and p.pause_flag = 0 and p.project_status = 1 and ai.apply_status = 3 and ai.delete_flag = 0 and ai.sms_flag = 1)";
				
				String sql = "update t_sms_reply set sms_reply_msg = '" + content + "', sms_reply_time = to_date('" + replyTime + "', 'yyyy-MM-dd hh24:mi:ss') where submit_end_time >= sysdate"
					+ " and sms_mobile = '" + mobile + "' and sms_ext = " + extNum;
				
				//boolean flag = db.update(sql);
				//if (!flag)
					System.err.println("未在数据库找到对应的数据：" + mobile + "--" + content + "--" + replyTime + "--" + extNum);
			}                      
		}

		System.out.println("end ReceiveMsgTimerServlet at  " + new Date());
	}
	public void destroy() {
		System.out.println("destroy ReceiveMsgTimerServlet");
	}
}
