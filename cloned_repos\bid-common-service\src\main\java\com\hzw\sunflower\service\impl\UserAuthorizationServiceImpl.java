package com.hzw.sunflower.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzw.sunflower.constant.constantenum.AuthorizationTypeEnum;
import com.hzw.sunflower.dao.UserAuthorizationMapper;
import com.hzw.sunflower.dto.UserAuthorizationDto;
import com.hzw.sunflower.entity.UserAuthorization;
import com.hzw.sunflower.entity.UserAuthorizationProject;
import com.hzw.sunflower.service.UserAuthorizationProjectService;
import com.hzw.sunflower.service.UserAuthorizationService;
import com.hzw.sunflower.util.BeanListUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


/**
 *UserService接口实现
 *
 * <AUTHOR>
 * @version 1.0.0 2021-04-21
 */
@Service
public class UserAuthorizationServiceImpl extends ServiceImpl<UserAuthorizationMapper, UserAuthorization> implements UserAuthorizationService {

    @Autowired
    private UserAuthorizationProjectService projectService;
    /**
     * 保存用户授权方式
     * @param dto
     */
    @Override
    public Boolean saveUserAuthorization(UserAuthorizationDto dto){
        Boolean result = false;
        if(dto.getAuthorizationType() == null){
            dto.setAuthorizationType(AuthorizationTypeEnum.ALL_PROJECT.getType());
        }
        UserAuthorization userAuthorization = BeanListUtil.convert(dto,UserAuthorization.class);
        //查看库里是否存在
        LambdaQueryWrapper<UserAuthorization> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserAuthorization::getUserId,dto.getUserId())
                .eq(UserAuthorization::getIdentity,dto.getIdentity());
        UserAuthorization one = getOne(queryWrapper);
        if(one != null){
            userAuthorization.setId(one.getId());
        }
        result = saveOrUpdate(userAuthorization);

        //如果是单项目授权，则插入用户授权项目表
        if(AuthorizationTypeEnum.ONE_PROJECT.getType().equals(dto.getAuthorizationType())
            && StringUtils.isNotBlank(dto.getPurchaseNumber()) && dto.getLegalPersonAuthorization() != null){
            UserAuthorizationProject project = BeanListUtil.convert(dto,UserAuthorizationProject.class);
            LambdaQueryWrapper<UserAuthorizationProject> qw = new LambdaQueryWrapper<>();
            qw.eq(UserAuthorizationProject::getUserId,dto.getUserId())
                    .eq(UserAuthorizationProject::getPurchaseNumber,dto.getPurchaseNumber());
            UserAuthorizationProject authorizationProject = projectService.getOne(qw);
            if(authorizationProject != null){
                project.setId(authorizationProject.getId());
            }
            result = projectService.saveOrUpdate(project);
        }

        //修改用户授权函
        projectService.updateUserPersonAuthorization(dto.getUserId(),dto.getIdentity(),dto.getLegalPersonAuthorization());
        
        return result;
    }

    /**
     * 查询用户授权方式和授权项目信息
     * @param userId
     * @param userIdentity
     * @return
     */
    @Override
    public UserAuthorizationDto getUserAuthorizationInfo(Long userId, Integer userIdentity) {
        return this.baseMapper.getUserAuthorizationInfo(userId,userIdentity);
    }
}