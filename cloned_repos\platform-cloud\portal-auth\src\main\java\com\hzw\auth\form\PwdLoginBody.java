package com.hzw.auth.form;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 短信登录对象
 *
 * <AUTHOR>
 */

@Data
public class PwdLoginBody {

    /**
     * 手机号
     */
    @NotBlank(message = "{user.phonenumber.not.blank}")
    private String phonenumber;

    /**
     * 密码
     */
    @NotBlank(message = "{user.password.not.blank}")
    private String password;

    /**
     * 密码
     */
    @NotBlank(message = "{user.imagecode.not.blank}")
    private String captcha;

    /**
     * 临时token
     */
    private String tempToken;
}
