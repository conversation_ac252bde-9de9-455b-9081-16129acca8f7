package com.jszbtb.mobile.plugins.network.flutter_network_plugin;


import java.io.UnsupportedEncodingException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Comparator;
import java.util.HashMap;
import java.util.Set;
import java.util.TreeMap;

/**
 * URL 帮助类
 * <AUTHOR>
 */
public class UrlUtils {

    /**
     * 计算SHA1值
     * @param text
     * @return
     * @throws NoSuchAlgorithmException
     * @throws UnsupportedEncodingException
     */
    public static String computeSHA1(String text) throws NoSuchAlgorithmException, UnsupportedEncodingException {
        MessageDigest md = MessageDigest.getInstance("SHA-1");
        byte[] textBytes = text.getBytes("iso-8859-1");
        md.update(textBytes, 0, textBytes.length);
        byte[] sha1hash = md.digest();
        return convertToHex(sha1hash);
    }

    /**
     * byte数组转换为十六进制字符
     * @param data byte数组
     * @return 十六进制字符
     */
    private static String convertToHex(byte[] data) {
        StringBuilder buf = new StringBuilder();
        for (byte b : data) {
            int halfbyte = (b >>> 4) & 0x0F;
            int two_halfs = 0;
            do {
                buf.append((0 <= halfbyte) && (halfbyte <= 9) ? (char) ('0' + halfbyte) : (char) ('a' + (halfbyte - 10)));
                halfbyte = b & 0x0F;
            } while (two_halfs++ < 1);
        }
        return buf.toString();
    }

    /**
     * 获取请求URL的 唯一hash值，用于get请求或者www-urlencode编码的请求
     * @param apiUrl api接口地址
     * @param params url参数
     * @return hash值
     */
    public static String getUrlHashCode(String apiUrl, HashMap<String,String> params) {
        String hashCode  = "";

        TreeMap<String,String> orderParams = new TreeMap<String, String>(new Comparator<String>() {
            @Override
            public int compare(String o1, String o2) {
                return o2.compareTo(o1);
            }
        });

        if(params != null) {
            orderParams.putAll(params);
        }

        String paramStr = "";

        Set<String> keys = params.keySet();

        for (String key: keys) {
            paramStr = paramStr + key + "=" + params.get(key) + "&";
        }

        String hashCodeUrl = apiUrl + paramStr;
        try {
            hashCode = computeSHA1(hashCodeUrl);
        } catch (Exception e) {
            hashCode = "";
        }

        return hashCode;
    }

    /**
     *  获取请求URL的 唯一hash值,适用于POST 字符串
     * @param apiUrl 请求地址
     * @param param 参数
     * @return hash值
     */
    public static String getUrlHashCode(String apiUrl,String param) {
        String hashCode  = "";
        String hashCodeUrl = apiUrl + param;
        try {
            hashCode = computeSHA1(hashCodeUrl);
        } catch (Exception e) {
            hashCode = "";
        }

        return hashCode;
    }
}
