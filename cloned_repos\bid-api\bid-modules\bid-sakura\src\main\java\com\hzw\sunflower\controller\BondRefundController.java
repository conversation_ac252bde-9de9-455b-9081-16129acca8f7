package com.hzw.sunflower.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hzw.ncc.client.api.NccApiClient;
import com.hzw.sunflower.common.BaseController;
import com.hzw.sunflower.common.Paging;
import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.common.annotation.RepeatSubmit;
import com.hzw.sunflower.constant.MessageConstants;
import com.hzw.sunflower.constant.constantenum.ShowProcessEnum;
import com.hzw.sunflower.controller.request.*;
import com.hzw.sunflower.controller.response.*;
import com.hzw.sunflower.dto.BondProjectDTO;
import com.hzw.sunflower.entity.BondRate;
import com.hzw.sunflower.entity.BondRefund;
import com.hzw.sunflower.entity.BondRefundDetails;
import com.hzw.sunflower.entity.Project;
import com.hzw.sunflower.entity.condition.BankRateCondition;
import com.hzw.sunflower.entity.condition.BondProjectCondition;
import com.hzw.sunflower.entity.condition.BondRefundCondition;
import com.hzw.sunflower.service.*;
import com.hzw.sunflower.util.BeanListUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 保证金退回待处理
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-27
 */
@Api(tags = "保证金退还")
@RestController
@RequestMapping("/bondRefund")
public class BondRefundController extends BaseController {

    @Autowired
    private BondRefundService bondRefundService;

    @Autowired
    private BondRateService bondRateService;

    @Autowired
    private BondRefundDetailsService bondRefundDetailsService;

    @Autowired
    private ProjectService projectService;

    /**
     * 保证金退还项目列表
     * projectId
     * @return
     */
    @ApiOperation(value = "保证金退款项目列表")
    @ApiImplicitParam(name = "condition", value = "项目表 查询条件", required = true, dataType = "BondProjectCondition", paramType = "body")
    @PostMapping("/bondProjectList")
    public Result<Paging<BondProjectVO>> bondProjectList(@RequestBody BondProjectCondition condition) {
        IPage<BondProjectVO> bondProject = bondRefundService.bondProjectList(condition);
        return Result.ok(Paging.buildPaging(bondProject));
    }

    /**
     * 保证金退还项目列表(全流程线下+线上归档项目)
     * projectId
     * @return
     */
    @ApiOperation(value = "保证金退还项目列表(全流程线下+线上归档项目)")
    @ApiImplicitParam(name = "condition", value = "项目表 查询条件", required = true, dataType = "BondProjectCondition", paramType = "body")
    @PostMapping("/bondProjectListOffline")
    public Result<Paging<BondProjectVO>> bondProjectListOffline(@RequestBody BondProjectCondition condition) {
        IPage<BondProjectVO> bondProject = bondRefundService.bondProjectListOffline(condition);
        return Result.ok(Paging.buildPaging(bondProject));
    }


    /**
     * 保证金退还标供应商列表
     * sectionId
     * @return
     */
    @ApiOperation(value = "保证金退还标供应商列表")
    @ApiImplicitParam(name = "req", value = "保证金退还标段列表", required = true, dataType = "ProjectSectionReq", paramType = "body")
    @PostMapping("/bondSupplierList")
    public Result<Paging<BondSupplierInfoVo>> bondSupplierList(@RequestBody ProjectSectionReq req) {
        IPage<BondSupplierInfoVo> page = req.buildPage();
        List<BondSupplierInfoVo> bondSupplierVos = bondRefundService.bondSupplierList(req,page);
        page.setRecords(bondSupplierVos);
        return Result.ok(Paging.buildPaging(page));
    }



    /**
     * 退还申请
     * sectionId
     * waterId   流水id,项目经理退还的时候，不传
     * waterIds
     * companyId
     * bondMoney  保证金金额
     * agencyFee  代理服务费
     * agencyFeeType  服务费收取方式：1：保证金转代理服务费  2：保证金全额转代理服务费并补差价  3：保证金全额退还，代理服务费另汇
     * fileIds  付款凭证
     * refundType   退还类型：1 ：项目经理申请退还  2：异常保证金退还  3：供应商申请退还
     * refundMode   退还方式：1：收取并退还剩余保证金  2：收取并保留剩余保证金
     * bondType    1:按流水收取  2：无流水收取
     * id  编辑的时候传
     * bondRefundDetails [{
     *                     "refundCompanyName":"xxx公司",  //退款户名
     *                     "refundNumber":"11111",        //退款账号
     *                     "refundOpenBank":"中国银行",     //退款开户行
     *                     "bondMoney":1000,
     *                     "agencyFee":500,
     *                     "amountDate":xxx, //到账日期
     *                     "fromOpenBank":xxxx,
     *                     "fromBankNumber":xxxx,
     *                     "refundBankCode":xxx,退还银联号
     *                     }]
     * @return
     */
    @ApiOperation(value = "退还申请")
    @ApiImplicitParam(name = "req", value = "退还申请", required = true, dataType = "BondRefundFeq", paramType = "body")
    @PostMapping("/applyRefund")
    @RepeatSubmit
    public Result applyRefund(@RequestBody BondRefundFeq req) {
     /*   if(null == req.getSectionId() || null == req.getCompanyId()){
            return Result.failed(MessageConstants.PARAMS_NOT_NULL);
        }*/
        Boolean result = bondRefundService.saveApplyRefund(req);
        return Result.okOrFailed(result);
    }

    @ApiOperation(value = "保证金退还撤回")
    @ApiImplicitParam(name = "req", value = "保证金退还撤回", required = true, dataType = "BondRefundFeq", paramType = "body")
    @PostMapping("/withdrawRefund")
    public Result<String> withdrawRefund(@RequestBody BondWithdrawFeq req) {
        return bondRefundService.withdrawRefund(req);
    }

    @ApiOperation(value = "退还申请批量")
    @ApiImplicitParam(name = "req", value = "退还申请", required = true, dataType = "BondRefundFeq", paramType = "body")
    @PostMapping("/applyRefundBatch")
    @RepeatSubmit
    public Result<BatchRefundRecordVo> applyRefundBatch(@RequestBody List<BondRefundFeq> reqList) {
//        if(null == req.getSectionId() || null == req.getCompanyId()){
//            return Result.failed(MessageConstants.PARAMS_NOT_NULL);
//        }
        return bondRefundService.applyRefundBatch(reqList);
    }


    /**
     * id
     * @return
     */
    @ApiOperation(value = "保证金退还详情信息")
    @ApiImplicitParam(name = "req", value = "退还申请", required = true, dataType = "BondRefundFeq", paramType = "body")
    @PostMapping("/bondRefundDetails")
    public Result<BondRefundVo> bondRefundDetails(@RequestBody BondRefundFeq req) {
        BondRefund refund = bondRefundService.getById(req.getId());
        BondRefundVo vo = BeanListUtil.convert(refund, BondRefundVo.class);
        LambdaQueryWrapper<BondRefundDetails> detailsQuery = new LambdaQueryWrapper<>();
        detailsQuery.eq(BondRefundDetails::getRefundId,refund.getId());
        vo.setBondRefundDetails(bondRefundDetailsService.list(detailsQuery));
        return Result.ok(vo);
    }



    /**
     * 财务退还列表
     *    keywords "关键字：项目编号/项目名称/供应商名称"
     *    applyTime "申请时间"
     *    bankName  收款银行
     *    type "0：全部 1：待处理 2：已处理"
     *    status   状态：1：待处理  2：已退回    3：退还成功 4：退还失败
     * @return
     */
    @ApiOperation(value = "财务退还列表")
    @ApiImplicitParam(name = "condition", value = "查询条件", required = true, dataType = "BondRefundCondition", paramType = "body")
    @PostMapping("/list")
    public Result<Paging<RefundListVo>> list(@RequestBody BondRefundCondition condition) {
        IPage<RefundListVo> list = bondRefundService.bondRefundList(condition);
        return Result.ok(Paging.buildPaging(list));
    }



    /**
     * 今日利率编辑接口
     * rate
     * beforeRate  调整之前的利率
     * beforeDate  调整之前的时间
     * type :bond 保证金
     * @return
     */
    @ApiOperation(value = "今日利率编辑接口")
    @ApiImplicitParam(name = "req", value = "查询条件", required = true, dataType = "BondRateReq", paramType = "body")
    @PostMapping("/saveRete")
    @RepeatSubmit
    public Result saveRete(@RequestBody BondRateReq req) {
        BondRate rate = BeanListUtil.convert(req, BondRate.class);
        boolean b = bondRateService.save(rate);
        return Result.okOrFailed(b);
    }

    /**
     * 银行利率列表
     * @param condition
     * @return
     */
    @ApiOperation(value = "银行利率列表")
    @ApiImplicitParam(name = "condition", value = "查询条件", required = true, dataType = "BankRateCondition", paramType = "body")
    @PostMapping("/bankRateList")
    public Result<Paging<BankRateListVo>> bankRateList(@RequestBody BankRateCondition condition) {
        IPage<BankRateListVo> list = bondRefundService.bankRateList(condition);
        return Result.ok(Paging.buildPaging(list));
    }

    /**
     * 银行利率历史记录
     * @param bankType
     * @return
     */
    @ApiOperation(value = "银行利率历史记录")
    @ApiImplicitParam(name = "condition", value = "查询条件", required = true, dataType = "BankRateCondition", paramType = "body")
    @GetMapping("/bankRateList/{bankType}")
    public Result<List<BankRateHistoryListVo>> bankRateHistoryList(@PathVariable Integer bankType) {
        return bondRefundService.bankRateHistoryList(bankType);
    }

    /**
     * 银行利率保存
     * @param req
     * @return
     */
    @ApiOperation(value = "银行利率保存")
    @ApiImplicitParam(name = "req", value = "查询条件", required = true, dataType = "BankRateCondition", paramType = "body")
    @PostMapping("/saveBankRate")
    public Result<String> saveBankRate(@RequestBody BondRateReq req) {
        return bondRateService.saveBankRate(req);
    }


    /**
     * 根据type查询今日利率
     * type :bond
     * @return
     */
    @ApiOperation(value = "根据code查询今日利率")
    @ApiImplicitParam(name = "req", value = "查询条件", required = true, dataType = "BondRateReq", paramType = "body")
    @PostMapping("/getRateByType")
    public Result<BondRate> getRateByType(@RequestBody BondRateReq req) {
        BondRate rate = bondRateService.getRateByType(req);
        return Result.ok(rate);
    }


    /**
     * id 业务id
     * processInstanceId  审核实例ID
     * taskId     任务id
     * processCode  流程code
     * operation  操作  1：确定  2：退回
     * message    退回原因
     * @param req
     * @return
     */
    @ApiOperation(value = "审核接口")
    @ApiImplicitParam(name = "req", value = "审核参数", required = true, dataType = "ExamineReq", paramType = "body")
    @PostMapping(value = "/toExamine")
    @RepeatSubmit
    public Object examineTicketAndRefund(@RequestBody ExamineReq req) {
       if (StringUtils.isBlank(req.getProcessCode())) {
            return Result.failed("流程不能为空！");
        }
        Boolean result = bondRefundService.updateExamineForRefund(req, getJwtUser());
        if (result) {
            return Result.ok(MessageConstants.SUCCESS);
        } else {
            return Result.failed(MessageConstants.FAIL);
        }
    }




//    /**
//     * [
//     * {
//     * id 业务id
//     * processInstanceId  审核实例ID
//     * taskId     任务id
//     * processCode  流程code
//     * operation  操作  1：确定  2：退回
//     * message    退回原因
//     * }
//     * ]
//     * @param req
//     * @return
//     */
//    @ApiOperation(value = "批量审核")
//    @ApiImplicitParam(name = "req", value = "审核参数", required = true, dataType = "ExamineReq", paramType = "body")
//    @PostMapping(value = "/batchExamine")
//    @RepeatSubmit
//    public Result<Map<String, Long>> batchExamine(@RequestBody List<ExamineReq> req) {
//        // Map<String,Long>  result = bondRefundService.updateBatchExamine(req, getJwtUser());
//        Map<String,Long> map = new HashMap<>();
//        String batch = UUID.randomUUID().toString();
//        req.forEach(e->{
//            try {
//                bondRefundService.updateExamineForRefund(e,getJwtUser());
//            }catch (Exception o){
//                o.printStackTrace();
//                System.out.println("-----------------------"+o.getMessage());
//                //插入保证金审核失败表
//                BondExamineFailRecord failRecord = new BondExamineFailRecord();
//                failRecord.setRefundId(e.getId());
//                failRecord.setFailReason(o.getMessage());
//                failRecord.setBatch(batch);
//                bondExamineFailRecordService.save(failRecord);
//            }
//        });
//
//        //查询成功多少条，失败多少条，返回给前端
//        Long totoal = Long.parseLong(String.valueOf(req.size()));
//        LambdaQueryWrapper<BondExamineFailRecord> failQuery = new LambdaQueryWrapper<>();
//        failQuery.eq(BondExamineFailRecord::getBatch,batch);
//        Long failCount = bondExamineFailRecordService.count(failQuery);
//        map.put("successCount",(totoal-failCount));
//        map.put("failCount",failCount);
//        return Result.ok(map);
//    }

    /**
     * [
     * {
     * id 业务id
     * processInstanceId  审核实例ID
     * taskId     任务id
     * processCode  流程code
     * operation  操作  1：确定  2：退回
     * message    退回原因
     * }
     * ]
     * @param req
     * @return
     */
    @ApiOperation(value = "批量审核")
    @ApiImplicitParam(name = "req", value = "审核参数", required = true, dataType = "ExamineReq", paramType = "body")
    @PostMapping(value = "/batchExamine")
    @RepeatSubmit
    public Result<BatchRefundRecordVo> batchExamine(@RequestBody List<ExamineReq> req) {
        return bondRefundService.batchExamineForRefund(req,getJwtUser());
    }



    /**
     * 再次付款接口
     * id
     * companyId
     * @return
     */
    @ApiOperation(value = "再次付款接口")
    @ApiImplicitParam(name = "req", value = "查询条件", required = true, dataType = "RefundIdReq", paramType = "body")
    @PostMapping("/againRefund")
    @RepeatSubmit
    public Result againRefund(@RequestBody RefundIdReq req) {
        boolean b = bondRefundService.saveAgainRefund(req,getJwtUser());
        return Result.okOrFailed(b);
    }



    /**
     * 转异常接口
     * id
     * companyId
     * abnormalReson
     * @return
     */
    @ApiOperation(value = "转异常接口")
    @ApiImplicitParam(name = "req", value = "查询条件", required = true, dataType = "RefundIdReq", paramType = "body")
    @PostMapping("/toAbnormal")
    @RepeatSubmit
    public Result toAbnormal(@RequestBody RefundIdReq req) {
        boolean b = bondRefundService.saveToAbnormal(req);
        return Result.okOrFailed(b);
    }


    /**
     * 线下退还接口
     * id
     * companyId
     * refundTime
     * refundFiles
     * @return
     */
    @ApiOperation(value = "线下退还接口")
    @ApiImplicitParam(name = "req", value = "查询条件", required = true, dataType = "BondRefundFeq", paramType = "body")
    @PostMapping("/offlineRefund")
    @RepeatSubmit
    public Result offlineRefund(@RequestBody BondRefundFeq req) {
        Boolean b = bondRefundService.saveOfflineRefund(req);
        return Result.okOrFailed(b);
    }



    /**
     * 收款流水退还列表
     *    keywords "关键字：项目编号/项目名称/供应商名称"
     *    refundTime "退还时间"
     *    bankName  收款银行
     * @return
     */
    @ApiOperation(value = "收款流水退还列表")
    @ApiImplicitParam(name = "condition", value = "查询条件", required = true, dataType = "BondRefundCondition", paramType = "body")
    @PostMapping("/bondWaterRefundList")
    public Result<Paging<RefundListVo>> bondWaterRefundList(@RequestBody BondRefundCondition condition) {
        IPage<RefundListVo> list = bondRefundService.bondWaterRefundList(condition);
        return Result.ok(Paging.buildPaging(list));
    }




    /**
     * 打印保证金退还申请表
     *    keywords "关键字：项目编号/项目名称/供应商名称"
     *    applyTime "申请时间"
     *    bankName  收款银行
     *    status   状态：1：待处理  2：已退回    3：退还成功 4：退还失败
     * @return
     */
    @ApiOperation(value = "打印保证金退还申请表")
    @ApiImplicitParam(name = "condition", value = "查询条件", required = true, dataType = "BondRefundCondition", paramType = "body")
    @PostMapping("/printBondRefundApply")
    public Result<List<BondProjectDTO>> printBondRefundApply(@RequestBody BondRefundCondition condition) {
        List<BondProjectDTO> files = bondRefundService.printBondRefundApply(condition);
        return Result.ok(files);
    }



    /**
     * 打印保证金退还申请表  改
     * {
     *   "companySection":  [{"sectionId":1,"companyId":1}]
     * }
     * @return
     */
    @ApiOperation(value = "打印保证金退还申请表")
    @ApiImplicitParam(name = "req", value = "查询条件", required = true, dataType = "RefundSectionReq", paramType = "body")
    @PostMapping("/printBondRefundApply2")
    public Result<List<BondProjectDTO>> printBondRefundApply2(@RequestBody RefundSectionReq req) {
        List<BondProjectDTO> files = bondRefundService.printBondRefundApply2(req);
        return Result.ok(files);
    }



    /**
     * 打印中标通知书存根
     *    keywords "关键字：项目编号/项目名称/供应商名称"
     *    applyTime "申请时间"
     *    bankName  收款银行
     *    status   状态：1：待处理  2：已退回    3：退还成功 4：退还失败
     * @return
     */
    @ApiOperation(value = "打印中标通知书存根")
    @ApiImplicitParam(name = "condition", value = "查询条件", required = true, dataType = "BondRefundCondition", paramType = "body")
    @PostMapping("/printBidWinNoticeStub")
    public Result<List<String>> printBidWinNoticeStub(@RequestBody BondRefundCondition condition) {
        List<String> files = bondRefundService.printBidWinNoticeStub(condition);
        return Result.ok(files);
    }


    /**
     * 打印中标通知书存根
     * {
     *   "companySection":  [{"sectionId":1,"companyId":1}]
     * }
     * @return
     */
    @ApiOperation(value = "打印中标通知书存根")
    @ApiImplicitParam(name = "req", value = "查询条件", required = true, dataType = "RefundSectionReq", paramType = "body")
    @PostMapping("/printBidWinNoticeStub2")
    public Result<List<String>> printBidWinNoticeStub2(@RequestBody RefundSectionReq req) {
        List<String> files = bondRefundService.printBidWinNoticeStub2(req);
        return Result.ok(files);
    }



    /**
     * 失败记录查看接口
     *  id
     * @return
     */
    @ApiOperation(value = "失败记录查看接口")
    @ApiImplicitParam(name = "req", value = "查询条件", required = true, dataType = "RefundIdReq", paramType = "body")
    @PostMapping("/payFailDetails")
    public Result<List<BondRefundDetails>> payFailDetails(@RequestBody RefundIdReq req) {
        LambdaQueryWrapper<BondRefundDetails> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BondRefundDetails::getRefundId,req.getId());
               // .eq(BondRefundDetails::getStatus, BondRefundStatusEnum.FAIL.getType());
        List<BondRefundDetails> list = bondRefundDetailsService.list(queryWrapper);
        return Result.ok(list);
    }


    /**
     * 获取评标情况
     *      * {
     *      *   "companySection":  [{"sectionId":1,"companyId":1}]
     *      * }
     * @return
     */
    @ApiOperation(value = "财务退还列表")
    @ApiImplicitParam(name = "condition", value = "查询条件", required = true, dataType = "RefundSectionReq", paramType = "body")
    @PostMapping("/getBidWinInfo")
    public Result<List<BidWinSupplierVo>> getBidWinInfo(@RequestBody RefundSectionReq req) {
        List<BidWinSupplierVo> bidWinInfos = new ArrayList<>();
        if(req.getCompanySection() != null && req.getCompanySection().size()>0){
            for(CompanySectionReq r:req.getCompanySection()){
                Project project = projectService.getProjectBySectionId(r.getSectionId());
                BidWinSupplierVo bidWinInfo = new BidWinSupplierVo();
                if(ShowProcessEnum.PROVINCE_IN_OUT.getType().equals(project.getOperationFlow())){
                    bidWinInfo = bondRefundService.getBidWinInfoOffline(r.getSectionId(), r.getCompanyId());
                } else {
                    bidWinInfo = bondRefundService.getBidWinInfo(r.getSectionId(), r.getCompanyId());
                }
                if(bidWinInfo != null) {
                    bidWinInfo.setSectionId(r.getSectionId());
                    bidWinInfo.setCompanyId(r.getCompanyId());
                    bidWinInfos.add(bidWinInfo);
                }
            }
        }
        return Result.ok(bidWinInfos);
    }

    @ApiOperation(value = "补缴代理服务费")
    @ApiImplicitParam(name = "req", value = "补缴代理服务费参数", required = true, dataType = "BondAgencyFeeFileReq", paramType = "body")
    @PostMapping("/saveAgencyFeeFile")
    @RepeatSubmit
    public Result saveAgencyFeeFile(@RequestBody BondAgencyFeeFileReq req) {
        if( null == req.getRefundId() || null == req.getFileId()){
            return Result.failed(MessageConstants.PARAMS_NOT_NULL);
        }
        Boolean result = bondRefundService.saveAgencyFeeFile(req);
        return Result.okOrFailed(result);
    }

}
