package com.hzw.ssm.sys.call.service;


import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.apache.http.conn.HttpClientConnectionManager;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.IdleConnectionEvictor;
import org.apache.http.util.Args;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.hzw.ssm.expert.entity.ExpertInfoEntity;
import com.hzw.ssm.fw.util.CommUtil;
import com.hzw.ssm.fw.util.DateUtil;
import com.hzw.ssm.fw.util.HZWException;
import com.hzw.ssm.fw.util.Page;
import com.hzw.ssm.fw.util.SysConstants;
import com.hzw.ssm.sys.call.entity.VoiceBodyEntity;
import com.hzw.ssm.sys.call.entity.VoiceDateEntity;
import com.hzw.ssm.sys.call.util.JsonTOBean;
import com.hzw.ssm.sys.project.entity.ConditionEntity;
import com.hzw.ssm.sys.project.entity.ExtractDebarbEntity;
import com.hzw.ssm.sys.project.entity.ProjectEntity;
import com.hzw.ssm.sys.project.entity.ResultEntity;
import com.hzw.ssm.sys.project.service.DebarbService;
import com.hzw.ssm.sys.project.service.ProjectService;
import com.hzw.ssm.sys.sms.mwutil.MWSmsUtil;
import com.hzw.ssm.sys.sms.mwutil.RPT;
import com.hzw.ssm.sys.system.entity.SmsRecordEntity;
import com.hzw.ssm.sys.system.service.SmsRecordService;
import com.hzw.ssm.sys.template.entity.TemplateEntity;
import com.hzw.ssm.sys.template.service.TemplateService;
import com.opensymphony.xwork2.security.ExcludedPatternsChecker.IsExcluded;

/**
 * 系统短信自动抽取定时器
 * <AUTHOR>
 *
 */
@Service
public class VoiceService {
	/** 日志对象 */
	protected static final Log log = LogFactory.getLog(VoiceService.class);
	@Autowired
	private ProjectService projectService;
	
	@Autowired
	private SmsRecordService smsRecordService;
	
	@Autowired
	private TemplateService templateService;
	
	@Autowired
	private DebarbService debarbService;
	//梦网短信账号
	@Value("${sms_userId}")
	private String userId;
	//梦网短信密码
	@Value("${sms_userPwd}")
	private String userPwd;
	//梦网主IP
	@Value("${sms_masterIpAddress}")
	private String masterIpAddress;
	
	//梦网备用IP1
	@Value("${sms_ipAddress1}")
	private String ipAddress1;
	//梦网备用IP2
	@Value("${sms_ipAddress2}")
	private String ipAddress2;
	
	//综合处联系人
	@Value("${sms_phone}")
	private String phone;
	
	
	//综合处联系人
	@Value("${sms_name}")
	private String smsName;
	private HttpClientConnectionManager connectionManager;
	
	/**
	 * 判断人数是否符合查询条件
	 * @param projectEntity
	 * @return
	 */
/*	public String checkProple(ProjectEntity projectEntity) {
		ConditionEntity entity = new ConditionEntity();
		entity.setDecimationBatch(projectEntity.getDecimationBatch());
		//判断当前的项目专家人数是否充足
		String msg  = projectService.getPeople(entity);
		if(!msg.equals("success")) {
			projectEntity.setStatus(20L);//再次抽取
			msg = "符合条件的专家人数不足！请添加专家类别";
			//人数不足时需要通知业务员重新修改专业刘红再次确认抽取
			//TODO 通知刘红和业务员
		}else {
			projectEntity.setStatus(2L);//再次抽取
		}
		//修改项目状态
		projectService.updateProjectStatus(projectEntity);
		return msg;
	}*/
	
	
	/**
	 * 接收方法
	 * @throws Exception 
	 * 
	 */
	public  void getMessage(String bodys) throws Exception {
	//	log.error("传递参数："+bodys);
		//获得处理之后的专家结果
		ResultEntity entity = disposeGetMessage(bodys);
		try{
			//TODO 判断当前抽取条件是否被修改过
			//System.out.println("修改号码！"+entity.getConditionId());
			if(entity!=null){
				//通过流水号号获取当前项目的查询条件
				ConditionEntity conEntity = new ConditionEntity();
				conEntity.setId(entity.getConditionId());
				conEntity = projectService.queryConditionById(conEntity);
				//log.error("状态："+entity.getJoinStatus());
				//判断处理之后的专家是否是参加
				if(entity.getJoinStatus() !=null && (entity.getJoinStatus().longValue()==SysConstants.JOIN_STATUS.ATTEND)){
					//发短信
					SmsRecordEntity smsRecordEntity =projectService.smsExperts(entity,conEntity);
					entity.setSmsId(smsRecordEntity.getSms_id());
					//修改项目状态
					projectService.updateExtractedExpertTOVoice(entity);
					ProjectEntity projectEntity = new ProjectEntity();
					projectEntity.setDecimationBatch(entity.getDecimationBatch());
					judgeExpertJoinStatus(projectEntity, conEntity);
				}else{
					//修改专家状态
					projectService.updateExtractedExpertTOVoice(entity);
					//给下一个未通知的专家拨打语音通知
					ConditionEntity conditionEntity = new ConditionEntity();
					conditionEntity.setDecimationBatch(entity.getDecimationBatch());
					conditionEntity.setMethod(SysConstants.CONDITION_METHOD.METHOD_THREE);
					disposeSendExpert(conditionEntity);
				}
			}
		}catch (Exception e) {
			log.error(e.toString());
		}
	}


	/**
	 * 定时器功能
	 * 接收短信的报告
	 */
	public void work(){
		
		Thread thread = new Thread();
		thread.setName("SyncDate");
		 //获得短信接收的信息
	     MWSmsUtil util = new MWSmsUtil();
	     List<RPT> rptList = util.getRpt(userId,userPwd,masterIpAddress,ipAddress1,ipAddress2,100);
	     //循环获取未收到短信的专家
	     if(rptList != null && rptList.size() > 0){
	    	 //修改数据库状态
	    	 for(RPT rpt:rptList){
	    		 try{
	    			 System.out.println("-------测试定时器-------");
		    		 SmsRecordEntity smsRecordEntity =  new SmsRecordEntity();
		    		 smsRecordEntity.setSms_id(rpt.getCustid());
		    		 smsRecordEntity.setIs_accept(rpt.getStatus());
		    		 smsRecordEntity.setSms_variety(SysConstants.CONDITION_CURRENT_STATUS.METHOD_THREE);
		    		 //修改短信通知是否成功
		    		 smsRecordService.updateSmsRecord(smsRecordEntity);
	    		 }catch (Exception e) {
					e.printStackTrace();
				}
		     }
	     }
	     
	   /*  synchronized (this) {
	         notifyAll();
	     }*/
	     queryUnreceivedSmsRecord();
	}
	
	public List<SmsRecordEntity> queryUnreceivedSmsRecord(){
		
		List<SmsRecordEntity> smsList = new ArrayList<SmsRecordEntity>();
		SmsRecordEntity smsRecord =  new SmsRecordEntity();
		Page page = new Page();
		page.setCurrentPage(1);
		smsRecord.setPage(page);
		 //获得5分钟前发送短信但是失败，且没有通知综合处的短信列表
		smsList=smsRecordService.queryPageUnreceivedSmsRecord(smsRecord);
		
		if(smsList !=null && smsList.size()>0){
			// 标记未收到短信专家通知综合处
			SmsRecordEntity updateEntity = new SmsRecordEntity();
			//组合整个短信内容
			String smsContent = "您好，以下专家未收到短信";
			//短信 通知专家评标 部分
			String smsContent1="专家参标未收到短信手机号码如下：";
			//短信 项目变更 部分
			String smsContent2="项目变更未收到短信手机号码如下：";
			//短信 项目取消 部分
			String smsContent3="项目取消未收到短信手机号码如下：";
			List<String> smsIds = new ArrayList<String>();
			//拼接字符串给综合处发短信
			for(SmsRecordEntity entity:smsList){
				if(SysConstants.CHANGE_RESULT.RESULT_1.equals(entity.getChange_result())){
					smsContent1 +=entity.getSms_mobile()+",";
				}else if(SysConstants.CHANGE_RESULT.RESULT_2.equals(entity.getChange_result())){
					smsContent2 +=entity.getSms_mobile()+",";
				}else if(SysConstants.CHANGE_RESULT.RESULT_3.equals(entity.getChange_result())){
					smsContent3 +=entity.getSms_mobile()+",";
				}
				smsIds.add(entity.getSms_id());
				//smsContent+="梦网msgid:"+entity.getMw_msgid()+"手机号："+entity.getSms_mobile();
			}
			smsContent = smsContent1+smsContent2+smsContent3;
			// 记录短信
			SmsRecordEntity smsRecordEntity = new SmsRecordEntity();
			smsRecordEntity.setSms_id(CommUtil.getKey());
			smsRecordEntity.setSms_user_id(phone);
			smsRecordEntity.setSms_user_name(smsName);
			smsRecordEntity.setSms_content(smsContent);
			smsRecordEntity.setSms_mobile(phone);
			smsRecordEntity.setSms_type(SysConstants.SMS_TYPE.SMS_FOUR);
			smsRecordEntity.setSms_variety(4L);
			smsRecordEntity.setSms_time(new Date());
			//发送短信通知
			MWSmsUtil util =  new MWSmsUtil();
			util.singleSend(userId, userPwd, masterIpAddress,ipAddress1,ipAddress2,smsRecordEntity);
			smsRecordService.insertSmsRecord(smsRecordEntity);
			
			updateEntity.setInformId(smsRecordEntity.getSms_id());
			updateEntity.setSms_ids(smsIds);
			smsRecordService.updateInform(updateEntity);
			
			/*//发送短信
			ResultEntity resultEntity = new ResultEntity();
			for(SmsRecordEntity entity:smsList){
				
				resultEntity.setSmsId(entity.getSms_id());
				resultEntity.setInformId(smsRecordEntity.getSms_id());
				//修改专家状态
				smsRecordService.updateSmsRecord(smsRecordEntity);
				//projectService.updateExtractedSmsStartus(resultEntity);
			}*/
		}
		//HttpClientConnectionManager connectionManager = new HttpClientConnectionManager();
		//this.connectionManager=Args.notNull(connectionManager, "Connection manager");
		//connectionManager.s .closeExpiredConnections();
		//connectionManager.closeIdleConnections(60000, TimeUnit.MILLISECONDS);
		return smsList;
		
	}

	
	public void smsTongzhi(String phone,String content){
			// 记录短信
			SmsRecordEntity smsRecordEntity = new SmsRecordEntity();
			smsRecordEntity.setSms_id(CommUtil.getKey());
			smsRecordEntity.setSms_user_id(phone);
			smsRecordEntity.setSms_content(content);
			smsRecordEntity.setSms_mobile(phone);
			MWSmsUtil util =  new MWSmsUtil();
			util.singleSend(userId, userPwd, masterIpAddress,ipAddress1,ipAddress2,smsRecordEntity);
	
	}
	
	/**
	 * 给专家发语音
	 * 
	 * @param entity
	 */
	public synchronized String disposeSendExpert(ConditionEntity entity) {
		String msg = "success";
		try{
			//查询根据批次号查询整合的数据
			List<ProjectEntity> projectList = projectService.queryProjectTOVoice(entity);
			//判断是否存在
			if(projectList !=null && projectList.size()>0) {
				//获取第一条数据
				ProjectEntity projectEntity = projectList.get(0);
				//根据流水号查询抽取记录
				log.error("根据流水号查询抽取记录："+projectEntity.getDecimationBatch());
				List<ConditionEntity> conditionList = projectService.queryConditionListByBatch(projectEntity);
				//根据查询条件ID查询专家未参加的
				ConditionEntity conditionEntity = conditionList.get(0);
				conditionEntity.setJoin_status(SysConstants.JOIN_STATUS.NOINFORM);
				//发送语音
				List<ExpertInfoEntity> expertList =projectService.queryProjectExpertInfo(conditionEntity);
				log.error("需要通知的专家数量："+expertList.size());
				//添加判断当前时间是否是开标前半个小时TODO
				//判断当前项目是否是半个小时以内
				Calendar calendar = Calendar.getInstance();
				long currentTime = calendar.getTimeInMillis()+30*60*1000;
				Date date = new Date(currentTime);
				
				//获取当前时间
				Calendar cal = Calendar.getInstance();
				//在当前时间上减去2小时
				cal.add(Calendar.HOUR, -2);
				
				//判断当前时间是否在开标前半个小时
				if(projectEntity.getBidTime().after(cal.getTime()) &&projectEntity.getBidTime().before(date)) {
					for(ConditionEntity cEntity: conditionList){
						//将项目状态修改为应急抽取，并且不在语音   通知将状态改成应急
						ConditionEntity updateConditionEntity =  new ConditionEntity();
						updateConditionEntity.setId(cEntity.getId());
						updateConditionEntity.setCurrentStatus(SysConstants.CONDITION_CURRENT_STATUS.METHOD_TWO);
						projectService.updateConditionCurrentStatus(updateConditionEntity);
					}
					ResultEntity rEntity =  new ResultEntity();
					rEntity.setDecimationBatch(projectEntity.getDecimationBatch());
					rEntity.setJoinStatus(SysConstants.JOIN_STATUS.NOINFORM);
					rEntity.setExpertWay(SysConstants.EXPERT_WAY.WAY_TWO);
					projectService.updateExtractedExpertTOExpertWay(rEntity);
					//判断是否有未通知的专家
					if(expertList==null||expertList.size()==0){
						//如何没有需要发短信的专家判断当前项目人数是否足够，抽取下一批专家
						getExpert(projectEntity, conditionEntity);
					}
					
				}else {
					//判断是否存在
					if(conditionList !=null && conditionList.size()>0) {
						//判断是否有未通知的专家
						if(expertList !=null && expertList.size()>0) {
							//获取第一个未发送的专家
							ExpertInfoEntity expertInfoEntity =	expertList.get(0);
							List<ExpertInfoEntity> expertInfoList = new ArrayList<ExpertInfoEntity>();
							expertInfoList.add(expertInfoEntity);
							//拼接语音内容并且发送
							log.error("获得通知专家的手机号码："+expertInfoEntity.getMobilephone());
							projectService.sendVoiceForExperts(projectEntity, conditionEntity, expertInfoList);
						}else {
							//如何没有需要发短信的专家判断当前项目人数是否足够，抽取下一批专家
							getExpert(projectEntity, conditionEntity);
						}
					}
				}
			}
		}catch (Exception e) {
			e.printStackTrace();
			throw new HZWException("发送短信内容模块失败");
		}
		return msg;
	}
	//TODO 判断当前项目是否审批过
	/**
	 * 
	 *判断当前批次的参加评标的专家是否满足要求
	 *满足：修改项目状态并且将未通知的专家设置为不参加、不满足：不作操作
	 * @param projectEntity
	 * @param conditionEntity
	 */
	private void judgeExpertJoinStatus(ProjectEntity projectEntity,ConditionEntity conditionEntity) {
		try{
			// 查询多少专家已经确定参标
			conditionEntity.setJoin_status(SysConstants.JOIN_STATUS.ATTEND);
			Integer qCount = projectService.countNotExtractResult(conditionEntity);
			//判断确认参标的专家和想要的专家人数是否一致
			if(qCount.longValue()==conditionEntity.getTotal()) {
				//人数一致将项目状态修改成已抽取
				projectEntity.setStatus(SysConstants.PROJECT_STATUS.ALREADY);
				projectService.updateProjectStatus(projectEntity);
				//并且将未通知的专家的状态改成“不参加” 理由 “ 其他-人数以满足”
				ResultEntity resultEntity = new ResultEntity();
				resultEntity.setDecimationBatch(projectEntity.getDecimationBatch());
				resultEntity.setReason("其他");
				resultEntity.setQt_reason("人数已满");
				resultEntity.setJoinStatus(SysConstants.JOIN_STATUS.NOATTEND);
				resultEntity.setRecieveTime(new Date());
				projectService.updateExtractedExpertTOVoice(resultEntity);
			}
		}catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	/**
	 * 抽取下一批专家
	 * @param projectEntity
	 * @param conditionEntity
	 * @return
	 */
	public List<ExpertInfoEntity> getExpert(ProjectEntity projectEntity,ConditionEntity conditionEntity){
		//抽取专家
		List<ProjectEntity> projectList = projectService.queryProjectListById(projectEntity);
		String tender = "";
		for(ProjectEntity entity:projectList) {
			if(null != entity.getTender() && !"".equals(entity.getTender()) && entity.getTender().indexOf("、") > -1){
				String[] tenderSplit = entity.getTender().split("、");
				for (int i = 0; i < tenderSplit.length; i++) {
					tender +="'"+tenderSplit[i]+"',";
				}
			}else{
				tender += "'" + entity.getTender() + "',";
			}
			conditionEntity.setBidTimeDay(DateUtil.dateToString(entity.getBidTime()));
		}
		tender = tender.substring(0, tender.length() - 1);
		conditionEntity.setTender(tender);
		
		List<ExpertInfoEntity> expertInfoList = null;
		
		//给回避专家赋值 获取回避单位、回避专家
		ExtractDebarbEntity debarbEntity = new ExtractDebarbEntity();
		debarbEntity.setDecimationbatch(projectEntity.getDecimationBatch());
		//查询回避单位
		debarbEntity.setDebarbType(SysConstants.DEBARB_TYPE.TYPE_1);
		List<ExtractDebarbEntity> debarbCompyList = debarbService.queryExtractDebarbList(debarbEntity);
		if(debarbCompyList !=null && debarbCompyList.size()>0){
			conditionEntity.setCompanyDebarbList(debarbCompyList);
		}
		debarbEntity.setDebarbType(SysConstants.DEBARB_TYPE.TYPE_2);
		List<ExtractDebarbEntity> debarbExtractCodeList = debarbService.queryExtractDebarbList(debarbEntity);
		if(debarbExtractCodeList !=null && debarbExtractCodeList.size()>0){
			conditionEntity.setExtractDebarbList(debarbExtractCodeList);
		}
		
		try {
			expertInfoList= projectService.queryExpertsToExtraction(conditionEntity, projectList);
		} catch (Exception e) {
			if(e instanceof HZWException) {
				projectEntity.setStatus(SysConstants.PROJECT_STATUS.AGAIN);
				projectService.updateProjectStatus(projectEntity);
				//用短信通知项目负责人修改条件
				//发短信先查询库里的模板是否存在
				TemplateEntity entity  = new TemplateEntity();
				entity.setIsValid(SysConstants.IS_VALID.VALID_ZERO);
				entity.setTemplateType(String.valueOf(SysConstants.TEMPLATE_TYPE.TYPE_SIX));
				List<TemplateEntity> templateList =templateService.queryTemplateList(entity);
				if(templateList !=null && templateList.size()>0){
					projectService.smsProjectCreateUserTemplate(projectList.get(0), templateList.get(0).getTemplateContent());
				}else{
				
					projectService.smsProjectCreateUser(projectList.get(0), e.getMessage());
				}
			}else {
				e.printStackTrace();
			}
		}
		return expertInfoList;
		
	}
	/**
	 * 处理得到的请求
	 */
	private ResultEntity disposeGetMessage(String JsonObject){
		JsonTOBean jsonToBean = new JsonTOBean();
		ResultEntity entity = new ResultEntity();
		try{
			//获取实例beanmessageId
			VoiceDateEntity dateEntity= jsonToBean.JsonToJavaBean(JsonObject, VoiceDateEntity.class);
			//for(VoiceDateEntity dateEntity:dateEntityList){
				VoiceBodyEntity infoEntity  =  dateEntity.getBody();
				//用手机号码+短信批次号来确定当前专家的唯一性
				entity.setMessageNo(dateEntity.getHeader().getMessageId());
				entity.setPhone(dateEntity.getBody().getCalledNum());
				List<ResultEntity> resultList = projectService.queryExtracedResultToVoice(entity);
				//判断当前专家是否已经回复过
				if(resultList!=null && resultList.size()>0){
					//查询
					if(resultList.get(0).getJoinStatus().longValue()==SysConstants.JOIN_STATUS.SMS_NOTIFICATION){
						//判断专家是否正常通话
						if(infoEntity.getCalledRelCause().equals("1")){
							//1.同意  2.不同意
							if(infoEntity.getDtmfKey().equals("1")){
								entity.setJoinStatus(SysConstants.JOIN_STATUS.ATTEND);
							}else if(infoEntity.getDtmfKey().equals("2")){
								entity.setReason("拒绝参加");
								entity.setJoinStatus(SysConstants.JOIN_STATUS.NOATTEND);
							}else{
								entity.setReason("按键回复有误！默认不参加");
								entity.setJoinStatus(SysConstants.JOIN_STATUS.NOATTEND);
							}
						}else{
							//非正常通话状态都标记为“不参加”
							entity.setJoinStatus(SysConstants.JOIN_STATUS.NOATTEND);
							entity.setReason("其他");
							entity.setQt_reason(String.valueOf(SysConstants.CASE_MAP.get(infoEntity.getCalledRelCause())));
						}
						entity.setDecimationBatch(resultList.get(0).getDecimationBatch());
						entity.setConditionId(resultList.get(0).getConditionId());
						//现将时间变成时间格式输出
						Date date = new SimpleDateFormat("yyyyMMddHHmmss").parse(infoEntity.getStopCalledTime());
						//将时间格式转换成字符串
						String recieveTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(date);
						//将日期变成数据库统一格式
						entity.setRecieveTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(recieveTime));
						//记录请求
						entity.setResultBody(JsonObject);
					}else{
						//如果被推送多次，将从第二次以及以后不再更新，此专家的参加状态
						entity= null;
					}
				}else{
					entity= null;
				}
		//	}
		}catch (Exception e) {
			entity = null;
			e.printStackTrace();
		} 
		return entity;
	}
}
