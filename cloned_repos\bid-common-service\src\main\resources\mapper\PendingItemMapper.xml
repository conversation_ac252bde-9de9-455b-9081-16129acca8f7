<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!-- 待处理事项表 (t_pending_item) -->
<mapper namespace="com.hzw.sunflower.dao.PendingItemMapper">

    <select id="getSupplementBulletin" resultType="com.hzw.sunflower.entity.PendingItem">
        SELECT
            cpr.created_time AS applyTime,
            'supplementBid' AS businessCode,
            cpr.business_id AS businessId,
            cpr.operator_id AS applyUserId,
            sb.project_id AS projectId,
            sb.related_bid_section AS sectionId,
            pbs.bid_round AS bidRound
        FROM
            t_calibration_process_record cpr
            LEFT JOIN t_supplement_bulletin sb ON cpr.business_id = sb.id AND cpr.operator_id = sb.created_user_id
            LEFT JOIN t_project_bid_section pbs ON pbs.project_id = sb.project_id
        WHERE
            cpr.is_delete = 0
            AND sb.is_delete = 0
            AND pbs.is_delete = 0
            AND FIND_IN_SET(pbs.id,sb.related_bid_section)
            AND cpr.business_code = 'supplement'
            AND sb.id = #{businessId}
        ORDER BY
            cpr.created_time DESC
            LIMIT 1
    </select>

    <select id="getQuestionReply" resultType="com.hzw.sunflower.entity.PendingItem">
        SELECT
            cpr.created_time AS applyTime,
            'clarifyBid' AS businessCode,
            cpr.business_id AS businessId,
            cpr.operator_id AS applyUserId,
            qr.project_id AS projectId,
            qr.section_ids AS sectionId,
            pbs.bid_round AS bidRound
        FROM
            t_calibration_process_record cpr
            LEFT JOIN t_e_question_reply qr ON cpr.business_id = qr.id AND cpr.operator_id = qr.created_user_id
            LEFT JOIN t_project_bid_section pbs ON qr.project_id = pbs.project_id
        WHERE
            cpr.is_delete = 0
            AND qr.is_delete = 0
            AND pbs.is_delete = 0
            AND cpr.business_code = 'clarifyReply'
            AND FIND_IN_SET(pbs.id,qr.section_ids)
            AND qr.id = #{businessId}
        ORDER BY
            cpr.created_time DESC
            LIMIT 1
    </select>
    <select id="getProposeObjection" resultType="com.hzw.sunflower.entity.PendingItem">
        SELECT
            cpr.created_time AS applyTime,
            'objection' AS businessCode,
            cpr.business_id AS businessId,
            cpr.operator_id AS applyUserId,
            po.project_id AS projectId,
            po.section_ids AS sectionId,
            pbs.bid_round AS bidRound
        FROM
            t_calibration_process_record cpr
            LEFT JOIN t_e_propose_objection po ON cpr.business_id = po.id AND cpr.operator_id = po.created_user_id
            LEFT JOIN t_project_bid_section pbs ON po.project_id = pbs.project_id
        WHERE
            cpr.is_delete = 0
            AND po.is_delete = 0
            AND pbs.is_delete = 0
            AND cpr.business_code = 'objection'
            AND FIND_IN_SET(pbs.id,po.section_ids)
            AND po.id = #{businessId}
        ORDER BY
            cpr.created_time DESC
            LIMIT 1
    </select>
    <select id="getWinBulletin" resultType="com.hzw.sunflower.entity.PendingItem">
        SELECT
            cpr.created_time AS applyTime,
            'winningBidCandidate' AS businessCode,
            cpr.business_id AS businessId,
            cpr.operator_id AS applyUserId,
            wb.project_id AS projectId,
            wb.related_bid_section AS sectionId,
            pbs.bid_round AS bidRound
        FROM
            t_calibration_process_record cpr
            LEFT JOIN t_bid_win_bulletin wb ON cpr.business_id = wb.id AND cpr.operator_id = wb.created_user_id
            LEFT JOIN t_project_bid_section pbs ON wb.project_id = pbs.project_id
        WHERE
            cpr.is_delete = 0
            AND wb.is_delete = 0
            AND pbs.is_delete = 0
            AND cpr.business_code = 'winBULLETIN'
            AND wb.bid_win_type = 0
            AND FIND_IN_SET(pbs.id,wb.related_bid_section)
            AND wb.id = #{businessId}
        ORDER BY
            cpr.created_time DESC
            LIMIT 1
    </select>
    <select id="getWinBulletinResult" resultType="com.hzw.sunflower.entity.PendingItem">
        SELECT
            cpr.created_time AS applyTime,
            'winCandidateBid' AS businessCode,
            cpr.business_id AS businessId,
            cpr.operator_id AS applyUserId,
            wb.project_id AS projectId,
            wb.related_bid_section AS sectionId,
            pbs.bid_round AS bidRound
        FROM
            t_calibration_process_record cpr
            LEFT JOIN t_bid_win_bulletin wb ON cpr.business_id = wb.id AND cpr.operator_id = wb.created_user_id
            LEFT JOIN t_project_bid_section pbs ON wb.project_id = pbs.project_id
        WHERE
            cpr.is_delete = 0
            AND wb.is_delete = 0
            AND pbs.is_delete = 0
            AND cpr.business_code = 'winBULLETIN'
            AND wb.bid_win_type = 1
            AND FIND_IN_SET(pbs.id,wb.related_bid_section)
            AND wb.id = #{businessId}
        ORDER BY
            cpr.created_time DESC
            LIMIT 1
    </select>
    <select id="getWinNotice" resultType="com.hzw.sunflower.entity.PendingItem">
        SELECT
            cpr.created_time AS applyTime,
            'winNotice' AS businessCode,
            cpr.business_id AS businessId,
            cpr.operator_id AS applyUserId,
            wn.project_id AS projectId,
            wn.section_id AS sectionId,
            pbs.bid_round AS bidRound
        FROM
            t_calibration_process_record cpr
            LEFT JOIN t_bid_win_notice wn ON cpr.business_id = wn.id AND cpr.operator_id = wn.created_user_id
            LEFT JOIN t_project_bid_section pbs ON wn.project_id = pbs.project_id
        WHERE
            cpr.is_delete = 0
            AND wn.is_delete = 0
            AND pbs.is_delete = 0
            AND cpr.business_code = 'winNotice'
            AND (wn.is_reapply = 0 OR (wn.is_reapply=1 AND wn.`status` = 11))
            AND wn.bid_round = 2
            AND FIND_IN_SET(pbs.id,wn.section_id)
            AND wn.id = #{businessId}
        ORDER BY
            cpr.created_time DESC
            LIMIT 1
    </select>
    <select id="getWinNoticeNew" resultType="com.hzw.sunflower.entity.PendingItem">
        SELECT
            cpr.created_time AS applyTime,
            'reWinNotice' AS businessCode,
            cpr.business_id AS businessId,
            cpr.operator_id AS applyUserId,
            wn.project_id AS projectId,
            wn.section_id AS sectionId,
            pbs.bid_round AS bidRound
        FROM
            t_calibration_process_record cpr
            LEFT JOIN t_bid_win_notice wn ON cpr.business_id = wn.id AND cpr.operator_id = wn.created_user_id
            LEFT JOIN t_project_bid_section pbs ON wn.project_id = pbs.project_id
        WHERE
            cpr.is_delete = 0
            AND wn.is_delete = 0
            AND pbs.is_delete = 0
            AND cpr.business_code = 'winNotice'
            AND wn.bid_round = 2
            AND wn.is_reapply = 1
            AND FIND_IN_SET(pbs.id,wn.section_id)
            AND wn.id = #{businessId}
        ORDER BY
            cpr.created_time DESC
            LIMIT 1
    </select>
    <select id="getBidWinNoticePre" resultType="com.hzw.sunflower.entity.PendingItem">
        SELECT
            cpr.created_time AS applyTime,
            'bidWinNoticePre' AS businessCode,
            cpr.business_id AS businessId,
            cpr.operator_id AS applyUserId,
            wn.project_id AS projectId,
            wn.section_id AS sectionId,
            pbs.bid_round AS bidRound
        FROM
            t_calibration_process_record cpr
            LEFT JOIN t_bid_win_notice wn ON cpr.business_id = wn.id AND cpr.operator_id = wn.created_user_id
            LEFT JOIN t_project_bid_section pbs ON wn.project_id = pbs.project_id
        WHERE
            cpr.is_delete = 0
            AND wn.is_delete = 0
            AND pbs.is_delete = 0
            AND cpr.business_code = 'winNotice'
            AND wn.bid_round = 1
            AND wn.`status` != 5
            AND (wn.is_reapply = 0 OR (wn.is_reapply=1 AND wn.`status` = 11))
            AND FIND_IN_SET(pbs.id,wn.section_id)
            AND wn.id = #{businessId}
        ORDER BY
            cpr.created_time DESC
            LIMIT 1
    </select>
    <select id="getBidWinNoticePreNew" resultType="com.hzw.sunflower.entity.PendingItem">
        SELECT
            cpr.created_time AS applyTime,
            'reBidWinNoticePre' AS businessCode,
            cpr.business_id AS businessId,
            cpr.operator_id AS applyUserId,
            wn.project_id AS projectId,
            wn.section_id AS sectionId,
            pbs.bid_round AS bidRound
        FROM
            t_calibration_process_record cpr
            LEFT JOIN t_bid_win_notice wn ON cpr.business_id = wn.id AND cpr.operator_id = wn.created_user_id
            LEFT JOIN t_project_bid_section pbs ON wn.project_id = pbs.project_id
        WHERE
            cpr.is_delete = 0
            AND wn.is_delete = 0
            AND pbs.is_delete = 0
            AND cpr.business_code = 'winNotice'
            AND wn.bid_round = 1
            AND wn.is_reapply = 1
            AND FIND_IN_SET(pbs.id,wn.section_id)
            AND wn.id = #{businessId}
        ORDER BY
            cpr.created_time DESC
            LIMIT 1
    </select>
    <select id="getBidWinPeople" resultType="com.hzw.sunflower.entity.PendingItem">
        SELECT
            cpr.created_time AS applyTime,
            'winBid_zgys' AS businessCode,
            cpr.business_id AS businessId,
            cpr.operator_id AS applyUserId,
            bw.project_id AS projectId,
            bw.section_id AS sectionId,
            pbs.bid_round AS bidRound
        FROM
            t_calibration_process_record cpr
            LEFT JOIN t_bid_win bw ON cpr.business_id = bw.id AND cpr.operator_id = bw.created_user_id
            LEFT JOIN t_project_bid_section pbs ON bw.project_id = pbs.project_id
        WHERE
            cpr.is_delete = 0
            AND bw.is_delete = 0
            AND pbs.is_delete = 0
            AND bw.bid_round = 1
            AND cpr.business_code = 'bidwinpeople'
            AND FIND_IN_SET(pbs.id,bw.section_id)
            AND bw.id = #{businessId}
        ORDER BY
            cpr.created_time DESC
            LIMIT 1
    </select>
    <select id="getBidWinPeopleHou" resultType="com.hzw.sunflower.entity.PendingItem">
        SELECT
            cpr.created_time AS applyTime,
            'winBid' AS businessCode,
            cpr.business_id AS businessId,
            cpr.operator_id AS applyUserId,
            bw.project_id AS projectId,
            bw.section_id AS sectionId,
            pbs.bid_round AS bidRound
        FROM
            t_calibration_process_record cpr
                LEFT JOIN t_bid_win bw ON cpr.business_id = bw.id AND cpr.operator_id = bw.created_user_id
                LEFT JOIN t_project_bid_section pbs ON bw.project_id = pbs.project_id
        WHERE
            cpr.is_delete = 0
            AND bw.is_delete = 0
            AND pbs.is_delete = 0
            AND bw.bid_round = 2
            AND cpr.business_code = 'bidwinpeople'
            AND FIND_IN_SET(pbs.id,bw.section_id)
            AND bw.id = #{businessId}
        ORDER BY
            cpr.created_time DESC
            LIMIT 1
    </select>
    <select id="getExceptNotice" resultType="com.hzw.sunflower.entity.PendingItem">
        SELECT
            cpr.created_time AS applyTime,
            'abnormalNotice' AS businessCode,
            cpr.business_id AS businessId,
            cpr.operator_id AS applyUserId,
            en.project_id AS projectId,
            GROUP_CONCAT( pbs.id SEPARATOR ',' ) AS sectionId,
            pbs.bid_round AS bidRound
        FROM
            t_calibration_process_record cpr
            LEFT JOIN t_except_notice en ON cpr.business_id = en.id AND cpr.operator_id = en.created_user_id
            LEFT JOIN t_except_change_record sn ON en.id = sn.except_notice_id
            LEFT JOIN t_project p ON en.project_id = p.id
            LEFT JOIN t_project_bid_section pbs ON p.id = pbs.project_id AND pbs.id = sn.section_id
        WHERE
            cpr.is_delete = 0
            AND en.is_delete = 0
            AND sn.is_delete = 0
            AND p.is_delete = 0
            AND pbs.is_delete = 0
            AND cpr.business_code = 'exceptNoticeLc'
            AND en.except_type != 4
	        AND en.id = #{businessId}
        GROUP BY
            applyTime,
            businessCode,
            businessId,
            applyUserId,
            projectId,
            bidRound
        ORDER BY
            cpr.created_time DESC
            LIMIT 1
    </select>
    <select id="getPayAll" resultType="com.hzw.sunflower.entity.PendingItem">
        SELECT
            cpr.created_time AS applyTime,
            'specialTicketChange' AS businessCode,
            cpr.business_id AS businessId,
            cpr.operator_id AS applyUserId,
            pa.project_id AS projectId,
            pa.section_ids AS sectionId,
            pbs.bid_round AS bidRound
        FROM
            t_calibration_process_record cpr
            LEFT JOIN t_pay_all pa ON cpr.business_id = pa.id
            LEFT JOIN t_project p ON pa.project_id = p.id
            LEFT JOIN t_project_bid_section pbs ON p.id = pbs.project_id
        WHERE
            cpr.is_delete = 0
            AND pa.is_delete = 0
            AND p.is_delete = 0
            AND pbs.is_delete = 0
            AND cpr.business_code = 'CHANGE_TICKET'
            AND find_in_set(pbs.id,pa.section_ids)
            AND pa.id = #{businessId}
        ORDER BY
            cpr.created_time DESC
            LIMIT 1
    </select>
    <select id="getPayAllTenderFees" resultType="com.hzw.sunflower.entity.PendingItem">
        SELECT
            cpr.created_time AS applyTime,
            'tenderFeesApply' AS businessCode,
            cpr.business_id AS businessId,
            cpr.operator_id AS applyUserId,
            pa.project_id AS projectId,
            pa.section_ids AS sectionId,
            pbs.bid_round AS bidRound
        FROM
            t_calibration_process_record cpr
                LEFT JOIN t_pay_all pa ON cpr.business_id = pa.id
                LEFT JOIN t_project p ON pa.project_id = p.id
                LEFT JOIN t_project_bid_section pbs ON p.id = pbs.project_id
        WHERE
            cpr.is_delete = 0
          AND pa.is_delete = 0
          AND p.is_delete = 0
          AND pbs.is_delete = 0
          AND cpr.business_code = 'BSF_REFUND'
          AND find_in_set(pbs.id,pa.section_ids)
          AND pa.id = #{businessId}
        ORDER BY
            cpr.created_time DESC
            LIMIT 1
    </select>
    <select id="getSupplierRegister" resultType="com.hzw.sunflower.entity.PendingItem">
        SELECT
            cpr.created_time AS applyTime,
            'registeredSuppliers' AS businessCode,
            cpr.business_id AS businessId,
            cpr.operator_id AS applyUserId,
            sra.project_id AS projectId,
            sra.sub_ids AS sectionId,
            pbs.bid_round AS bidRound
        FROM
            t_calibration_process_record cpr
            LEFT JOIN t_supplier_register_apply sra ON cpr.business_id = sra.id AND cpr.operator_id = sra.created_user_id
            LEFT JOIN t_project_bid_section pbs ON sra.project_id = pbs.project_id
        WHERE
            cpr.is_delete = 0
            AND sra.is_delete = 0
            AND pbs.is_delete = 0
            AND cpr.business_code = 'SUPPLIER_REGISTER_APPLY'
            AND FIND_IN_SET(pbs.id,sra.sub_ids)
            AND sra.id = #{businessId}
        ORDER BY
            cpr.created_time DESC
            LIMIT 1
    </select>
    <select id="getApplyPayFile" resultType="com.hzw.sunflower.entity.PendingItem">
        SELECT
            cpr.created_time AS applyTime,
            'rerecordPayment' AS businessCode,
            cpr.business_id AS businessId,
            cpr.operator_id AS applyUserId,
            ai.project_id AS projectId,
            GROUP_CONCAT( pbs.id SEPARATOR ',' ) AS sectionId,
            pbs.bid_round AS bidRound
        FROM
            t_calibration_process_record cpr
            LEFT JOIN t_apply_pay_file apf ON cpr.business_id = apf.id
            LEFT JOIN t_apply_info ai ON ai.apply_id = apf.apply_id
            LEFT JOIN t_project p ON ai.project_id = p.id
            LEFT JOIN t_project_bid_section pbs ON p.id = pbs.project_id
        WHERE
            cpr.is_delete = 0
            AND apf.is_delete = 0
            AND ai.is_delete = 0
            AND p.is_delete = 0
            AND pbs.is_delete = 0
            AND cpr.business_code = 'APPLY_PAY_FILE'
            AND find_in_set( pbs.id, ai.sub_id )
            AND apf.id = #{businessId}
        GROUP BY
            applyTime,
            businessCode,
            businessId,
            applyUserId,
            projectId,
            bidRound
        ORDER BY
            cpr.created_time DESC
            LIMIT 1
    </select>
    <select id="getAgencyFeeApply" resultType="com.hzw.sunflower.entity.PendingItem">
        SELECT
            cpr.created_time AS applyTime,
            'agencyService' AS businessCode,
            cpr.business_id AS businessId,
            cpr.operator_id AS applyUserId,
            afa.project_id AS projectId,
            GROUP_CONCAT( pbs.id SEPARATOR ',' ) AS sectionId,
            pbs.bid_round AS bidRound
        FROM
            t_calibration_process_record cpr
            LEFT JOIN t_agency_fee_apply afa ON cpr.business_id = afa.id
            LEFT JOIN t_project p ON afa.project_id = p.id
            LEFT JOIN t_project_bid_section pbs ON p.id = pbs.project_id
        WHERE
            cpr.is_delete = 0
            AND afa.is_delete = 0
            AND p.is_delete = 0
            AND pbs.is_delete = 0
            AND cpr.business_code = 'AGENCY_FEE_APPLY'
            AND find_in_set( pbs.id, afa.section_id )
            AND afa.id = #{businessId}
        GROUP BY
            applyTime,
            businessCode,
            businessId,
            applyUserId,
            projectId,
            bidRound
        ORDER BY
            cpr.created_time DESC
            LIMIT 1
    </select>
    <select id="getDataUpdate" resultType="com.hzw.sunflower.entity.PendingItem">
        SELECT
            cpr.created_time AS applyTime,
            'dataModification' AS businessCode,
            cpr.business_id AS businessId,
            cpr.operator_id AS applyUserId,
            null AS projectId,
            null AS sectionId,
            null AS bidRound
        FROM
            t_calibration_process_record cpr
                LEFT JOIN t_data_update_record duc ON cpr.business_id = duc.id AND cpr.operator_id = duc.created_user_id
        WHERE
            cpr.is_delete = 0
          AND duc.is_delete = 0
          AND cpr.business_code = 'DATA_UPDATE_ONE'
          AND duc.id = #{businessId}
        ORDER BY
            cpr.created_time DESC
            LIMIT 1
    </select>
    <select id="getBondApplyReturn" resultType="com.hzw.sunflower.entity.PendingItem">
        SELECT
            cpr.created_time AS applyTime,
            'depositRefund' AS businessCode,
            cpr.business_id AS businessId,
            cpr.operator_id AS applyUserId,
            t.project_id AS projectId,
            GROUP_CONCAT( pbs.id SEPARATOR ',' ) AS sectionId,
            pbs.bid_round AS bidRound
        FROM
            t_calibration_process_record cpr
            LEFT JOIN t_bond t ON cpr.business_id = t.id
            LEFT JOIN t_project p ON t.project_id = p.id
            LEFT JOIN t_project_bid_section pbs ON p.id = pbs.project_id AND pbs.id = t.section_id
        WHERE
            cpr.is_delete = 0
            AND cpr.business_code = 'returnBond'
            AND t.id = #{businessId}
            AND t.is_delete = 0
            AND t.`status` = 2
            AND p.is_delete = 0
            AND pbs.is_delete = 0
        GROUP BY
            applyTime,
            businessCode,
            businessId,
            applyUserId,
            projectId,
            bidRound
        ORDER BY
            cpr.created_time DESC
            LIMIT 1
    </select>
    <select id="getExpertExtraction" resultType="com.hzw.sunflower.entity.PendingItem">
        SELECT
            cpr.created_time AS applyTime,
            'expertExtraction' AS businessCode,
            cpr.business_id AS businessId,
            cpr.operator_id AS applyUserId,
            t1.project_id AS projectId,
            GROUP_CONCAT( pbs.id SEPARATOR ',' ) AS sectionId,
            pbs.bid_round AS bidRound
        FROM
            t_calibration_process_record cpr
            LEFT JOIN t_expert_extraction t ON cpr.business_id = t.id
            LEFT JOIN t_expert_extraction_project t1 ON t1.extration_id = t.id
            LEFT JOIN t_project p ON t1.project_id = p.id
            LEFT JOIN t_project_bid_section pbs ON p.id = pbs.project_id AND pbs.id = t1.section_id
        WHERE
            cpr.is_delete = 0
            AND cpr.business_code = 'EXPERT_EXTRACTION'
            AND t.id = #{businessId}
            AND t.is_delete = 0
            AND t.extract_status = 4
            AND t1.is_delete = 0
            AND p.is_delete = 0
            AND pbs.is_delete = 0
        GROUP BY
            applyTime,
            businessCode,
            businessId,
            applyUserId,
            projectId,
            bidRound
        ORDER BY
            cpr.created_time DESC
            LIMIT 1
    </select>
    <select id="getExpertExtractionBeian" resultType="com.hzw.sunflower.entity.PendingItem">
        SELECT
            cpr.created_time AS applyTime,
            'expertsSelectedRecord' AS businessCode,
            cpr.business_id AS businessId,
            cpr.operator_id AS applyUserId,
            t2.project_id AS projectId,
            GROUP_CONCAT( pbs.id SEPARATOR ',' ) AS sectionId,
            pbs.bid_round AS bidRound
        FROM
            t_calibration_process_record cpr
            LEFT JOIN t_expert_extraction_beian t ON cpr.business_id = t.id
            LEFT JOIN t_expert_extraction t1 ON t1.id = t.expert_extraction_id
            LEFT JOIN t_expert_extraction_project t2 ON t2.extration_id = t1.id
            LEFT JOIN t_project p ON t2.project_id = p.id
            LEFT JOIN ( SELECT max( id ) id, expert_extraction_id FROM t_expert_extraction_beian t GROUP BY expert_extraction_id ) t3 ON FIND_IN_SET( t.id, t3.id )
            LEFT JOIN t_project_bid_section pbs ON p.id = pbs.project_id
            AND pbs.id = t2.section_id
        WHERE
            cpr.is_delete = 0
            AND cpr.business_code = 'EXPERT_EXTRACTION_BEIAN'
            AND t.id = #{businessId}
            AND t.is_delete = 0
            AND t.`status` = 2
            AND FIND_IN_SET( t.id, t3.id )
            AND t1.is_delete = 0
            AND t2.is_delete = 0
            AND p.is_delete = 0
            AND pbs.is_delete = 0
        GROUP BY
            applyTime,
            businessCode,
            businessId,
            applyUserId,
            projectId,
            bidRound
        ORDER BY
            cpr.created_time DESC
            LIMIT 1
    </select>
</mapper>
