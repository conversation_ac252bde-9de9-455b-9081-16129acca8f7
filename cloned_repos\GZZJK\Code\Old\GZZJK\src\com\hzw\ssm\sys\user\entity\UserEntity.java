package com.hzw.ssm.sys.user.entity;

import com.hzw.ssm.fw.base.BaseEntity;
import com.hzw.ssm.fw.util.Page;

import java.util.Date;

public class UserEntity extends BaseEntity {

	/**
	 * 
	 */
	private static final long serialVersionUID = 8042703708357357837L;

	private static final String SESSION = "SYSTEM_LOGIN_PASSWORD";

	private String user_id;// 用户编号
	private String role_id; // 角色
	private String user_name;// 用户名
	private String login_code; // 用户登陆名
	private String password; // 用户密码
	private Long sex; // 性别(0:女,1:男)
	private String department_id; // 部门
	private Long user_post;//职务级别 1：部门员工 2：部门经理 3：总经理
	private String mobile; // 联系电话
	private String email;// 用户邮箱
	private String tel; // 移动电话
	private Date createTime; // 创建日期
	private Long enabled_flag;//启用标识
	private Integer login_fail_count;
	private Date login_fail_time;
	private String department_name;//部门名称
	private String role_name;//角色名称
	private String email_password;//邮箱密码
	private String department;	//部门名称
	private String sms_code; //短信验证码
	private String recommendCode; //专家推荐码
	private Date sms_time; //验证码失效时间
	/**
	 * 登录状态：true.已登录、false.未登录
	 */
	private Boolean loginStatus;

	/**
	 * 用户推荐码
	 */
	private String qr_code;
	/** 分页 */
	private Page page;

	public String getUser_id() {
		return user_id;
	}

	public void setUser_id(String userId) {
		user_id = userId;
	}

	public String getRole_id() {
		return role_id;
	}

	public void setRole_id(String roleId) {
		role_id = roleId;
	}

	public String getUser_name() {
		return user_name;
	}

	public void setUser_name(String userName) {
		user_name = userName;
	}

	public String getLogin_code() {
		return login_code;
	}

	public void setLogin_code(String loginCode) {
		login_code = loginCode;
	}

	public String getPassword() {
		return password;
	}

	public void setPassword(String password) {
		this.password = password;
	}

	public Long getSex() {
		return sex;
	}

	public void setSex(Long sex) {
		this.sex = sex;
	}

	public String getDepartment_id() {
		return department_id;
	}

	public void setDepartment_id(String departmentId) {
		department_id = departmentId;
	}

	public String getMobile() {
		return mobile;
	}

	public void setMobile(String mobile) {
		this.mobile = mobile;
	}

	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public String getTel() {
		return tel;
	}

	public void setTel(String tel) {
		this.tel = tel;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
	
	public Date getLogin_fail_time() {
		return login_fail_time;
	}

	public void setLogin_fail_time(Date loginFailTime) {
		login_fail_time = loginFailTime;
	}

	public Page getPage() {
		return page;
	}

	public void setPage(Page page) {
		this.page = page;
	}

	public String getDepartment_name() {
		return department_name;
	}

	public void setDepartment_name(String departmentName) {
		department_name = departmentName;
	}

	public String getRole_name() {
		return role_name;
	}

	public void setRole_name(String roleName) {
		role_name = roleName;
	}

	public String getEmail_password() {
		return email_password;
	}

	public void setEmail_password(String emailPassword) {
		email_password = emailPassword;
	}

	public Long getEnabled_flag() {
		return enabled_flag;
	}

	public void setEnabled_flag(Long enabledFlag) {
		enabled_flag = enabledFlag;
	}

	public Long getUser_post() {
		return user_post;
	}

	public void setUser_post(Long userPost) {
		user_post = userPost;
	}

	public String getDepartment() {
		return department;
	}

	public void setDepartment(String department) {
		this.department = department;
	}

	public Integer getLogin_fail_count() {
		return login_fail_count;
	}

	public void setLogin_fail_count(Integer login_fail_count) {
		this.login_fail_count = login_fail_count;
	}

	public Boolean getLoginStatus() {
		return loginStatus;
	}

	public void setLoginStatus(Boolean loginStatus) {
		this.loginStatus = loginStatus;
	}

	public String getSms_code() {
		return sms_code;
	}

	public void setSms_code(String sms_code) {
		this.sms_code = sms_code;
	}

	public Date getSms_time() {
		return sms_time;
	}

	public void setSms_time(Date sms_time) {
		this.sms_time = sms_time;
	}

	public String getQr_code() {
		return qr_code;
	}

	public void setQr_code(String qr_code) {
		this.qr_code = qr_code;
	}

	public String getRecommendCode() {
		return recommendCode;
	}

	public void setRecommendCode(String recommendCode) {
		this.recommendCode = recommendCode;
	}
}
