package com.hzw.sunflower.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

@Data
@EqualsAndHashCode
public class ApprovalOpinionDTO implements Serializable {

    @ApiModelProperty(value = "处理进度ID", position = 1)
    private Long id;

    @ApiModelProperty(value = "审批内容", position = 2)
    private String approvalContent;

    @ApiModelProperty(value = "审批状态 0 提交审核;1 撤回;2 退回;3同意", position = 3)
    private Integer approvalType;

    @ApiModelProperty(value = "审批人", position = 4)
    private String userName;

    @ApiModelProperty(value = "项目ID", position = 5)
    private Long projectId;

    @ApiModelProperty(value = "审批时间", position = 6)
    private Date approvalTime;

    @ApiModelProperty(value = "公告ID", position = 7)
    private Long noticeId;

    @ApiModelProperty(value = "招标文件表ID", position = 8)
    private Long docId;

    /**
     * 审批姓名
     *
     * @mbg.generated
     */
    @ApiModelProperty(value = "审批姓名", position = 9)
    private String approvalUserName;

    private String nextUserName;
}
