///***
/// * 图表式数据报告
/// */

import 'package:flutter/material.dart';

import 'package:flutter_main_app/src/widgets/datareport/components/AnnualTransaction.dart';
import 'package:flutter_main_app/src/widgets/datareport/components/ConstructionRanking.dart';
import 'package:flutter_main_app/src/widgets/datareport/components/ProjectTransaction.dart';
import 'package:flutter_main_app/src/widgets/datareport/components/RegionalTender.dart';
import 'package:flutter_main_app/src/widgets/datareport/components/AnnualTransactionComparison.dart';

class DataReportChart extends StatelessWidget {

  const DataReportChart({Key key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ListView(
      physics: BouncingScrollPhysics(),
      padding: const EdgeInsets.only(left: 0.0, top: 20.0, right: 0.0, bottom: 50.0),
      children: <Widget>[
        /// * 年度累计交易宗数|总额
        AnnualTransaction(),
        /// * 年度施工类总排名
        ConstructionRanking(),
        /// * 年度项目交易总量
        ProjectTransaction(),
        /// * 年度区域招标总体情况
        RegionalTender(),
        /// * 近三年交易总数对比
        AnnualTransactionComparison(),
      ],
    );
  }

}
