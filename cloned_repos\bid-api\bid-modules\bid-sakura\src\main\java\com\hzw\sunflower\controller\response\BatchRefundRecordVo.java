package com.hzw.sunflower.controller.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date ：Created in 2023/7/21 9:51
 * @description：批量退还返回
 * @modified By：`
 * @version: 1.0
 */
@Data
public class BatchRefundRecordVo {

    @ApiModelProperty(value = "批量退还保证金成功数量")
    private Integer successCount;

    @ApiModelProperty(value = "批量退还保证金失败数量")
    private Integer failCount;

}
