<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hzw.sunflower.dao.SenMessMapper">


    <select id="findListByCondition" resultType="com.hzw.sunflower.controller.response.TemplateVo">

        SELECT
        *
        FROM
        (
        SELECT
        id,
        template_name,
        template_content,
        template_code,
        template_second_code,
        created_time,
        1 as type
        FROM
        t_template T
        WHERE
        T.is_delete = 0
        <if test="condition.templateName !=null and condition.templateName !=''">
            AND T.template_name like concat('%',#{condition.templateName},'%')
        </if>

        UNION


        SELECT
        id,
        type_name AS template_name,
        content AS template_content,
        type_code AS template_code,
        NULL AS template_second_code,
        created_time,
        2 as type
        FROM
        t_user_sms_template f
        WHERE
        f.is_delete = 0
        and f.user_id = #{userId}
        <if test="condition.templateName !=null and condition.templateName !=''">
            AND f.type_name like concat('%',#{condition.templateName},'%')
        </if>
        ) s
        ORDER BY
        s.created_time DESC
    </select>
</mapper>