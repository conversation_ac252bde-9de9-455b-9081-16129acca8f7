package com.hzw.sunflower.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hzw.sunflower.common.BaseBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName:BondWaterlabel
 * @Auther: lijinxin
 * @Description: 保证金流水标签
 * @Date: 2023/5/10 16:38
 * @Version: v1.0
 */

@Data
@TableName("t_bond_water_label")
@ApiModel(value = "BondWater对象", description = "保证金流水标签表")
public class BondWaterlabel extends BaseBean implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("流水id")
    @TableField("water_id")
    private Long waterId;

    @ApiModelProperty("流水标签 1收款 2其他 3退汇 4错汇 5 标书费")
    @TableField("label")
    private Integer label;

    @ApiModelProperty("是否存在项目编号 1是 2否")
    @TableField("is_project")
    private Integer isProject;

    @ApiModelProperty("委托项目编号")
    @TableField("purchase_number")
    private String purchaseNumber;

    @ApiModelProperty("流水描述")
    @TableField("notes")
    private String notes;


}
