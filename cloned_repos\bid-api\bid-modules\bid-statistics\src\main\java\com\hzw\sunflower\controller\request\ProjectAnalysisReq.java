package com.hzw.sunflower.controller.request;

import com.hzw.sunflower.common.BaseCondition;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProjectAnalysisReq extends BaseCondition {
    @ApiModelProperty(value = "起始日期")
    private String startDate;
    @ApiModelProperty(value = "结束日期")
    private String endDate;
}
