package com.hzw.sunflower.service;

import com.hzw.sunflower.common.Paging;
import com.hzw.sunflower.controller.request.AppingTaskREQ;
import com.hzw.sunflower.controller.response.AppingTaskVO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date ：Created in 2021/11/8 16:58
 * @description：缓存接口
 * @modified By：
 * @version: 1.0
 */
public interface WorkflowCacheService {

    /**
     * 获取待办事项
     *
     * @param appingTaskREQ 条件
     * @param userOtherId   用戶id
     */
    Paging<AppingTaskVO> getPaddingList(AppingTaskREQ appingTaskREQ, String userOtherId);
    /**
     * 获取待办事项
     *
     * @param appingTaskREQ 条件
     * @param userOtherId   用戶id
     */
    Paging<AppingTaskVO> getPaddingRemoteList(AppingTaskREQ appingTaskREQ, String userOtherId);

    /**
     * 获取待办事项
     *
     * @param appingTaskREQ 条件
     * @param userOtherId   用戶id
     */
    Map<String,List<AppingTaskVO>> getList(AppingTaskREQ appingTaskREQ, String userOtherId);

    /**
     * 获取待办事项
     * @param userOtherId
     * @param b
     * @return
     */
    List<AppingTaskVO> getCacheDataByUserOtherId(String userOtherId, Boolean b);
}
