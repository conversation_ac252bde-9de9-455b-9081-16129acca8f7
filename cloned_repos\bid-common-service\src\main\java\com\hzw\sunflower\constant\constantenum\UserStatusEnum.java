package com.hzw.sunflower.constant.constantenum;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 用户注册状态枚举
 *
 * <AUTHOR>
 * @date ：Created in 2021/5/12 10:02
 * @description：用户类型
 * @modified By：`
 * @version: 1.0
 */
public enum UserStatusEnum {

    JITC(-1, "jitc待补全数据"),

    ABNORMAL(0, "用户异常"),

    NORMAL(1, "启用"),

    REGISTERING(2, "注册中"),
    /**
     * 待公司审核
     */
    REGISTER_TO_EXAMINE(3, "待审核"),
    /**
     * 公司审核拒绝
     */
    REGISTER_NOT_PASS(4, "待完善信息"),
    /**
     * 待平台审核
     */
    PLATFORM_TO_EXAMINE(5, "待审核"),
    /**
     * 平台审核拒绝
     */
    PLATFORM_NOT_PASS(6, "待完善信息"),
    /**
     * 审核拒绝境外用户
     */
    PLATFORM_NOT_PASS_ABROAD(7, "境外用户待完善个人信息"),
    /**
     * 删除
     */
    DELETED(10, "注销"),
    /**
     * 待完善信息
     */
    INFO_PERFECT(11, "待完善信息"),
    /**
     * 用户身份被禁用
     */
    FOBIDDEN(12, "禁用"),

    /**
     * 用户身份转移中
     */
    TRANSFERING(13, "转移中"),

    /**
     * jitc管理员数据待完善
     */
    JITC_PERFECT(14, "jitc用户待完善信息"),
    /**
     * jitc管理员申请提交
     */
    JITC_APPLY(15, "jitc用户待平台审核"),

    /**
     * jitc管理员申请退回
     */
    JITC_RETURN(16, "jitc用户被退回"),

    /**
     * 用户手动注销，此状态非删除状态，表示当前状态可重新绑定新公司
     */
    USER_DELETE(20, "用户注销"),

    USER_INVITE(30, "邀请未注册供应商待补全用户信息"),
    USER_COMPANY_INVITE(31, "邀请未注册供应商待补全企业信息"),

    TENDEREE_USER_INVITE(40, "招标人用户待完善个人信息"),
    TENDEREE_USER_COMPANY_INVITE(41, "招标人用户待补全企业信息"),

    /**
     * 用户被整体禁用
     */
    USER_FOBIDDEN(-2, "用户被禁用"),
    NOT_EXISTED(-99, "用户不存在"),
    USER_PHONE_NUMBER_NOT_EXISTED(-98, "用户已存在，手机号码不存在"),
    USER_ADMIN_TRANSFER(-97, "管理员转移中");
    /**
     * 类型
     */
    private Integer code;

    /**
     * 说明
     */
    private String msg;

    UserStatusEnum(Integer code, String desc) {
        this.code = code;
        this.msg = desc;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }


    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    /**
     * 获取对应的枚举
     *
     * @param code 种类
     * @return 枚举值
     */
    public static UserStatusEnum getUserStatus(Integer code) {
        try {
            for (UserStatusEnum userStatusEnum : UserStatusEnum.values()) {
                if (userStatusEnum.code.equals(code)) {
                    return userStatusEnum;
                }
            }
            return null;
        } catch (Exception e) {
            return null;
        }
    }


    //讲枚举转换成list格式，这样前台遍历的时候比较容易，列如 下拉框 后台调用toList方法，你就可以得到code 和name了
    public static List toList() {
        List list = new ArrayList();
        for (UserStatusEnum airlineTypeEnum : UserStatusEnum.values()) {
            if (airlineTypeEnum.getCode() > 0) {
                Map<String, Object> map = new HashMap<String, Object>();
                map.put("code", airlineTypeEnum.getCode());
                map.put("name", airlineTypeEnum.getMsg());
                list.add(map);
            }
        }
        return list;
    }
}
