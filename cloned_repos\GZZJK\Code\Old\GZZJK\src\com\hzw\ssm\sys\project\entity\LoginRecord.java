package com.hzw.ssm.sys.project.entity;

import java.util.Date;

import com.hzw.ssm.fw.base.BaseEntity;
import com.hzw.ssm.fw.util.Page;

public class LoginRecord extends BaseEntity{
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	private String id;
	private String user_id;// 用户编号	
	private String login_name; // 登陆名
	private Date login_time; //登录时间
	/**
	 * 登录状态：1.已登录、0.未登录
	 */
	private Integer login_status;
	private Date create_time;//创建时间
	private String create_name;//创建人
	private String loginEndTime;//截至时间
	private String loginStartTime;// 开始时间
	/** 分页 */
	private Page page;

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public Page getPage() {
		return page;
	}
	public void setPage(Page page) {
		this.page = page;
	}

	public String getLoginStartTime() {
		return loginStartTime;
	}

	public void setLoginStartTime(String loginStartTime) {
		this.loginStartTime = loginStartTime;
	}

	public String getLoginEndTime() {
		return loginEndTime;
	}

	public void setLoginEndTime(String loginEndTime) {
		this.loginEndTime = loginEndTime;
	}

	public String getUser_id() {
		return user_id;
	}
	public void setUser_id(String user_id) {
		this.user_id = user_id;
	}
	
	public String getLogin_name() {
		return login_name;
	}
	public void setLogin_name(String login_name) {
		this.login_name = login_name;
	}
	public Date getLogin_time() {
		return login_time;
	}
	public void setLogin_time(Date login_time) {
		this.login_time = login_time;
	}
	
	public Date getCreate_time() {
		return create_time;
	}
	public void setCreate_time(Date create_time) {
		this.create_time = create_time;
	}
	
	public Integer getLogin_status() {
		return login_status;
	}
	public void setLogin_status(Integer login_status) {
		this.login_status = login_status;
	}

	public String getCreate_name() {
		return create_name;
	}

	public void setCreate_name(String create_name) {
		this.create_name = create_name;
	}
}
