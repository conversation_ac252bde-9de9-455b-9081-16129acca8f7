package com.hzw.sunflower.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hzw.sunflower.controller.response.BondRelationSectionVo;
import com.hzw.sunflower.dto.AutoRelationDto;
import com.hzw.sunflower.dto.BondSmsDto;
import com.hzw.sunflower.entity.BondRelation;
import com.hzw.sunflower.entity.Project;
import com.hzw.sunflower.entity.condition.BondRelationCondition;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 保证金关联关系表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-27
 */
@Mapper
public interface BondRelationMapper extends BaseMapper<BondRelation> {

    /**
     * 分页查询供应商关注标段保证金关联列表
     * @param page
     * @param condition
     * @return
     */
    IPage<BondRelationSectionVo> getSectionRelationList(IPage<BondRelationSectionVo> page, BondRelationCondition condition);

    /**
     * 校验当前包关联的所有流水均只关联了当前标包
     * @param sectionId
     * @param companyId
     * @return
     */
    Integer checkSectionRelationOnly(@Param("sectionId") Long sectionId, @Param("companyId") Long companyId);

    /**
     * 根据流水ids查询关联标段
     * @param waterIds
     * @param relationStatus
     * @return
     */
    List<BondRelationSectionVo> getSectionListByWaterIds(@Param("waterIds") List<Long> waterIds, @Param("relationStatus") Integer relationStatus);

    /**
     * 根据流水ids查询关联标段（已办）
     * @param waterIds
     * @param relationStatus
     * @return
     */
    List<BondRelationSectionVo> getSectionListByWaterIdsCompleted(@Param("waterIds") List<Long> waterIds,@Param("relationStatus") Integer relationStatus,@Param("createdTime") Date createdTime);
    /**
     * 根据项目id,开标状态查询所有待关联标段 已购标
     * @param projectId
     * @param companyId
     * @return
     */
    List<BondRelationSectionVo> getNotRelateSection(@Param("projectId") Long projectId, @Param("companyId") Long companyId, @Param("operateRole") Integer operateRole);

    /**
     * 根据标段和供应商查询标段详情
     * @param sectionId
     * @param companyId
     * @return
     */
    BondRelationSectionVo getSectionById(@Param("sectionId") Long sectionId, @Param("companyId") Long companyId);

    /**
     * 根据供应商名称和附言匹配 委托编号、项目编号、进场编号、国际标编号
     * @param purchaseNumber
     * @param companyName
     * @return
     */
    List<Project> selectProjectByPurchaseNumber(@Param("purchaseNumber") String purchaseNumber, @Param("companyName") String companyName);

    /**
     * 查询自动关联判断结果
     * @param sectionId
     * @param companyId
     * @param dateTime
     * @return
     */
    AutoRelationDto checkAutoRelation(@Param("sectionId") Long sectionId, @Param("companyId") Long companyId, @Param("dateTime") String dateTime);

    /**
     * 运管处搜索待关联标段
     * @param purchaseNumber
     * @param companyId
     * @return
     */
    List<BondRelationSectionVo> getSectionListByPurchaseNum(@Param("purchaseNumber") String purchaseNumber, @Param("companyId") Long companyId);

    /**
     * 获取供应商内所有存在已购标但未开标标段的联系人
     * @param companyId
     * @return
     */
    List<BondSmsDto> getNoPostScript(Long companyId);

    /**
     * 获取查询项目下供应商关注/购标联系人
     * @param projectId
     * @param companyId
     * @return
     */
    List<BondSmsDto> getOnlyProject(@Param("projectId") Long projectId, @Param("companyId") Long companyId);

    /**
     * 查询该供应商标段下所有购标联系人
     * @param sectionIdList
     * @param projectId
     * @param companyId
     * @return
     */
    List<BondSmsDto> getRelationResult(@Param("sectionIdList") List<String> sectionIdList, @Param("projectId") Long projectId, @Param("companyId") Long companyId);

    /**
     * 查询关联表中是否有删除记录
     * @param waterId
     * @return
     */
    Integer getDeleteCountByWaterId(@Param("waterId") Long waterId);

    /**
     * 校验是够关联
     * @param waterId
     * @return
     */
    Integer checkBondRelationByWaterId(@Param("waterId") Long waterId);

    /**
     * 根据项目id,供应商名称查询所有待关联标段
     * @param projectId
     * @param companyName
     * @return
     */
    List<BondRelationSectionVo> getNotRelateSectionOffline(@Param("projectId") Long projectId, @Param("companyName") String companyName);

    /**
     * 根据菜单查询人员id
     * @param path
     * @return
     */
    List<String> selectUserIdByMenu(String path);
    /**
     * 根据标段和供应商查询标段详情
     * @param sectionId
     * @param offlineCompanyId
     * @return
     */
    BondRelationSectionVo getSectionByIdOffline(@Param("sectionId") Long sectionId, @Param("offlineCompanyId") Long offlineCompanyId);
}
