package com.hzw.sunflower.controller.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2021/7/7 15:57
 * @description：承诺文件签署请求体
 * @version: 1.0
 */
@Data
public class PromiseFileSignReq {

    @ApiModelProperty(value = "oss文件Id")
    private Long fileOssId;

    @ApiModelProperty(value = "承诺文件表id")
    private Long promiseFileId;



    @ApiModelProperty(value = "项目id集合")
    private List<Long> projectIds;

    @ApiModelProperty(value = "会议室id")
    private Long conferenceId;
}
