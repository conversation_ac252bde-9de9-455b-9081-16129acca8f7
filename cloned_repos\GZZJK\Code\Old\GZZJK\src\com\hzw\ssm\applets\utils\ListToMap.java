package com.hzw.ssm.applets.utils;

import com.hzw.ssm.applets.entity.Dictionary;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2021-02-02 15:24
 * @Version 1.0
 */
public class ListToMap {

    public static Map<String, Object> listToMap(List<Dictionary> list) {
        Map<String, Object> map = new HashMap<>();
        for (Dictionary t : list) {
            map.put(t.getDataCode(), t.getDataValue());
        }
        return map;
    }
}
