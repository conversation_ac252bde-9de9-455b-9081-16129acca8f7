// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'HttpResponseModel.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

HttpResponseModel<T> _$HttpResponseModelFromJson<T>(Map<String, dynamic> json) {
  return HttpResponseModel<T>(
    success: json['success'] as bool,
    data: GenericConverter<T>().fromJson(json['data']),
    errorMessage: json['errorMessage'] as String,
    code: json['code'] as int,
  );
}

Map<String, dynamic> _$HttpResponseModelToJson<T>(
        HttpResponseModel<T> instance) =>
    <String, dynamic>{
      'success': instance.success,
      'data': GenericConverter<T>().toJson(instance.data),
      'errorMessage': instance.errorMessage,
      'code': instance.code,
    };
