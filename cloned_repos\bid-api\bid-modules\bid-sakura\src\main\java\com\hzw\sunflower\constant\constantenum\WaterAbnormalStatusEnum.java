package com.hzw.sunflower.constant.constantenum;

/**
 * <AUTHOR>
 * @date ：Created in 2022/07/27 9:23
 * @description：保证金流水异常状态
 * @version: 1.0
 */
public enum WaterAbnormalStatusEnum {

    NORMAL(1, "正常"),
    DAY45(2, "45天未关联"),
    DAY60(3, "60天未关联");

    private Integer type;
    private String desc;

    WaterAbnormalStatusEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
