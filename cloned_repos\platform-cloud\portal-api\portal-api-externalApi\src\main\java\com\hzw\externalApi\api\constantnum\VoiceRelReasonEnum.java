package com.hzw.externalApi.api.constantnum;

import java.util.ArrayList;
import java.util.List;

/**
 * 语音通知
 */
public enum VoiceRelReasonEnum {
    /**
     * 被叫结束原因（合并通话状态原因）
     * 1、正常接通
     * 2、呼叫遇忙；[通过信令]
     * 3、用户不在服务区；[通过信令]
     * 4、用户无应答；[通过信令]
     * 5、用户关机；[通过信令]
     * 6、空号；
     * 7、停机；
     * 8、号码过期
     * 9、主叫应答，被叫应答前挂机
     * 10、正在通话中
     * 11、拒接
     * 99、其他
     * 20：主动取消呼叫
     */
    NORMAL("1","正常接通"),
    BUSY("2","呼叫遇忙；[通过信令]"),
    NOT_IN_SERVICE("3","用户不在服务区；[通过信令]"),
    NO_REPLAY("4","用户无应答；[通过信令]"),
    TURN_OFF("5","用户关机；[通过信令]"),
    EMPTY_NUM("6","空号"),
    STOP_USE("7","停机"),
    OUT_TIME("8","号码过期"),
    EARLY_END("9","主叫应答，被叫应答前挂机"),
    ON_CALLING("10","正在通话中"),
    REFUSE("11","拒接"),
    OTHER("99","其他"),
    CANCEL("20","主动取消呼叫"),

    /**
     * 是否参见
     */
    JOIN("1","参加"),
    NOT_JOIN("2","不参加"),

    /**
     * 短信通知
     */
    IS_SEND("1","已发送"),
    NOT_SEND("2","未发送"),
    NOT_JOIN_SEND("3","不参加"),
    RETRY_SEND("4","重新通知"),
    EMPTY("5","已抽满"),

    ;


    private String type;
    private String desc;

    VoiceRelReasonEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    /**
     * 需要重新通知的状态
     * @return
     */
    public static List<String> retryStatus() {
        List<String> retryStatus = new ArrayList<>();
        retryStatus.add(VoiceRelReasonEnum.BUSY.getType());
        retryStatus.add(VoiceRelReasonEnum.NOT_IN_SERVICE.getType());
        retryStatus.add(VoiceRelReasonEnum.NO_REPLAY.getType());
        retryStatus.add(VoiceRelReasonEnum.TURN_OFF.getType());
        retryStatus.add(VoiceRelReasonEnum.ON_CALLING.getType());
        retryStatus.add(VoiceRelReasonEnum.REFUSE.getType());
        retryStatus.add(VoiceRelReasonEnum.OTHER.getType());
        return retryStatus;
    }
}
