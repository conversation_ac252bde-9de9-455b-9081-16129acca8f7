package com.hzw.sunflower.dto;

import com.hzw.sunflower.controller.response.BondSectionExportVo;
import com.hzw.sunflower.entity.BondWater;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2023/8/15 17:26
 * @description：
 * @modified By：`
 * @version: 1.0
 */
@ApiModel(description = "保证金退还管理")
@Data
public class BondRefundManageDto extends BondWater implements Serializable {

    @ApiModelProperty("项目编号")
    private String purchaseNumber;

    @ApiModelProperty("项目名称")
    private String projectName;

    @ApiModelProperty("标段id")
    private Long sectionId;

    @ApiModelProperty("标段/包编号")
    private Integer packageNumber;

    @ApiModelProperty("标段/包名称")
    private String packageName;

    @ApiModelProperty(value = "开标时间")
    private String openBidTime;

    @ApiModelProperty(value = "标包状态")
    private Integer projectStatusCode;

    @ApiModelProperty("退还总数")
    private Long refundTotal;

    @ApiModelProperty("已退数量")
    private Long refundAlready;

    @ApiModelProperty("已收代理服务费0：是，1：否，2：-")
    private Integer agencyFeeAlready;

    @ApiModelProperty("代理服务费是否收费 0 免费 1 收费")
    private Integer agencyCostFree;

    @ApiModelProperty("0：标段，1：包")
    private Integer bidTypeName;

}
