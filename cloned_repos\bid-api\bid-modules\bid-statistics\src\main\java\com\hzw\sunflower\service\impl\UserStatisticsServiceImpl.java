package com.hzw.sunflower.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzw.sunflower.controller.request.UserStatisticsReq;
import com.hzw.sunflower.controller.response.DepartmentStatisticsVO;
import com.hzw.sunflower.controller.response.UserStatisticsVO;
import com.hzw.sunflower.dao.UserStatisticsMapper;
import com.hzw.sunflower.entity.UserStatistics;
import com.hzw.sunflower.service.UserStatisticsService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import static java.util.stream.Collectors.toList;
@Service
public class UserStatisticsServiceImpl extends ServiceImpl<UserStatisticsMapper, UserStatistics> implements UserStatisticsService {

    /**
     *已开标项目数
     * @return
     */
    @Override
    public List<UserStatistics> queryBidOpen() {
        return this.baseMapper.queryBidOpen();
    }

    /**
     *未开标项目数
     * @return
     */
    @Override
    public List<UserStatistics> queryBidNotOpen() {
        return this.baseMapper.queryBidNotOpen();
    }

    /**
     * 已开标项目委托金额
     * @return
     */
    @Override
    public List<UserStatistics> queryBidOpenMoney() {
        return this.baseMapper.queryBidOpenMoney();
    }

    /**
     * 未开标项目委托金额
     * @return
     */
    @Override
    public List<UserStatistics> queryBidNotOpenMoney() {
        return this.baseMapper.queryBidNotOpenMoney();
    }

    /**
     * 已开标未收费中标金额预估
     * @return
     */
    @Override
    public List<UserStatistics> queryBidNotOpenWinMoney() {
        return this.baseMapper.queryBidNotOpenWinMoney();
    }

    /**
     * 中标金额
     * @return
     */
    @Override
    public List<UserStatistics> queryBidOpenWinMoney() {
        return this.baseMapper.queryBidOpenWinMoney();
    }

    //查询已收费（代理服务费）
    @Override
    public List<UserStatistics> queryBidOpenAgencyMoney() {
        return this.baseMapper.queryBidOpenAgencyMoney();
    }

    /**
     * 已开标未收费金额
     * @return
     */
    @Override
    public List<UserStatistics> queryBidNotOpenAgencyMoney() {
        return this.baseMapper.queryBidNotOpenAgencyMoney();
    }

    /**
     * 新增已开标项目数
     * @return
     */
    @Override
    public void insertQueryBidOpen() {
        List<UserStatistics> userStatisticsList = this.queryBidOpen();
        DateFormat sdf= new SimpleDateFormat("yyyy-MM-01");
        String format = sdf.format(new Date());
        this.baseMapper.deleteByYearMonth(format);
        for (UserStatistics statistics : userStatisticsList) {
            if(Objects.equals(this.baseMapper.selectId(statistics.getUserId(),statistics.getYearmonth()), null)){
                statistics.setCreatedTime(new Date());
                this.save(statistics);
            }else {
                statistics.setId(this.baseMapper.selectId(statistics.getUserId(),statistics.getYearmonth()).getId());
                statistics.setUpdatedTime(new Date());
                this.updateById(statistics);
            }
        }
    }

    /**
     * 新增未开标项目数
     * @return
     */
    @Override
    public void insertQueryBidNotOpen() {
        List<UserStatistics> userStatisticsList = this.queryBidNotOpen();
            for (UserStatistics statistics : userStatisticsList) {
                UserStatistics userStatistics=this.baseMapper.selectId(statistics.getUserId(),statistics.getYearmonth());
                if(Objects.equals(userStatistics, null)){
                    statistics.setCreatedTime(new Date());
                    this.save(statistics);
                }else {
                    statistics.setId(this.baseMapper.selectId(statistics.getUserId(),statistics.getYearmonth()).getId());
                    statistics.setUpdatedTime(new Date());
                    this.updateById(statistics);
                }
            }

    }

    /**
     * 新增已开标项目委托金额
     * @return
     */
    @Override
    public void insertQueryBidOpenMoney() {
        List<UserStatistics> userStatisticsList = this.queryBidOpenMoney();
        for (UserStatistics statistics : userStatisticsList) {
            if(Objects.equals(this.baseMapper.selectId(statistics.getUserId(),statistics.getYearmonth()), null)){
                statistics.setCreatedTime(new Date());
                this.save(statistics);
            }else {
                statistics.setId(this.baseMapper.selectId(statistics.getUserId(),statistics.getYearmonth()).getId());
                statistics.setUpdatedTime(new Date());
                this.updateById(statistics);
            }
        }
    }

    /**
     * 新增未开标项目委托金额
     * @return
     */
    @Override
    public void insertQueryBidNotOpenMoney() {
        List<UserStatistics> userStatisticsList = this.queryBidNotOpenMoney();
        for (UserStatistics statistics : userStatisticsList) {
            if(Objects.equals(this.baseMapper.selectId(statistics.getUserId(),statistics.getYearmonth()), null)){
                statistics.setCreatedTime(new Date());
                this.save(statistics);
            }else {
                statistics.setId(this.baseMapper.selectId(statistics.getUserId(),statistics.getYearmonth()).getId());
                statistics.setUpdatedTime(new Date());
                this.updateById(statistics);
            }
        }
    }

    /**
     * 新增已开标未收费中标金额预估
     * @return
     */
    @Override
    public void insertQueryBidNotOpenWinMoney() {
        List<UserStatistics> userStatisticsList = this.queryBidNotOpenWinMoney();
        for (UserStatistics statistics : userStatisticsList) {
            if(Objects.equals(this.baseMapper.selectId(statistics.getUserId(),statistics.getYearmonth()), null)){
                statistics.setCreatedTime(new Date());
                this.save(statistics);
            }else {
                statistics.setId(this.baseMapper.selectId(statistics.getUserId(),statistics.getYearmonth()).getId());
                statistics.setUpdatedTime(new Date());
                this.updateById(statistics);
            }
        }
    }

    /**
     * 新增中标金额
     * @return
     */
    @Override
    public void insertQueryBidOpenWinMoney() {
        List<UserStatistics> userStatisticsList = this.queryBidOpenWinMoney();
        for (UserStatistics statistics : userStatisticsList) {
            if(Objects.equals(this.baseMapper.selectId(statistics.getUserId(),statistics.getYearmonth()), null)){
                statistics.setCreatedTime(new Date());
                this.save(statistics);
            }else {
                statistics.setId(this.baseMapper.selectId(statistics.getUserId(),statistics.getYearmonth()).getId());
                statistics.setUpdatedTime(new Date());
                this.updateById(statistics);
            }
        }
    }

    /**
     * 新增已收费
     * @return
     */
    @Override
    public void insertQueryBidOpenAgencyMoney() {
        List<UserStatistics> userStatisticsList = this.queryBidOpenAgencyMoney();
        for (UserStatistics statistics : userStatisticsList) {
            if(Objects.equals(this.baseMapper.selectId(statistics.getUserId(),statistics.getYearmonth()), null)){
                statistics.setCreatedTime(new Date());
                this.save(statistics);
            }else {
                statistics.setId(this.baseMapper.selectId(statistics.getUserId(),statistics.getYearmonth()).getId());
                statistics.setUpdatedTime(new Date());
                this.updateById(statistics);
            }
        }
    }

    /**
     * 新增已开标未收费
     * @return
     */
    @Override
    public void insertQueryBidNotOpenAgencyMoney() {
        List<UserStatistics> userStatisticsList = this.queryBidNotOpenAgencyMoney();
        for (UserStatistics statistics : userStatisticsList) {
            if(Objects.equals(this.baseMapper.selectId(statistics.getUserId(),statistics.getYearmonth()), null)){
                statistics.setCreatedTime(new Date());
                this.save(statistics);
            }else {
                statistics.setId(this.baseMapper.selectId(statistics.getUserId(),statistics.getYearmonth()).getId());
                statistics.setUpdatedTime(new Date());
                this.updateById(statistics);
            }
        }
    }

    @Override
    public void updateQueryBidOpen() {
        List<UserStatistics> userStatisticsList = this.queryBidOpen();
        for (UserStatistics statistics : userStatisticsList) {
            statistics.setUpdatedTime(new Date());
            this.updateById(statistics);
        }
    }

    @Override
    public void updateQueryBidNotOpen() {
        List<UserStatistics> userStatisticsList = this.queryBidNotOpen();
        for (UserStatistics statistics : userStatisticsList) {
            statistics.setUpdatedTime(new Date());
            this.updateById(statistics);
        }
    }

    @Override
    public void updateQueryBidOpenMoney() {
        List<UserStatistics> userStatisticsList = this.queryBidOpenMoney();
        for (UserStatistics statistics : userStatisticsList) {
            statistics.setUpdatedTime(new Date());
            this.updateById(statistics);
        }
    }

    @Override
    public void updateQueryBidNotOpenMoney() {
        List<UserStatistics> userStatisticsList = this.queryBidNotOpenMoney();
        for (UserStatistics statistics : userStatisticsList) {
            statistics.setUpdatedTime(new Date());
            this.updateById(statistics);
        }
    }

    @Override
    public void updateQueryBidNotOpenWinMoney() {
        List<UserStatistics> userStatisticsList = this.queryBidNotOpenWinMoney();
        for (UserStatistics statistics : userStatisticsList) {
            statistics.setUpdatedTime(new Date());
            this.updateById(statistics);
        }
    }

    @Override
    public void updateQueryBidOpenWinMoney() {
        List<UserStatistics> userStatisticsList = this.queryBidOpenWinMoney();
        for (UserStatistics statistics : userStatisticsList) {
            statistics.setUpdatedTime(new Date());
            this.updateById(statistics);
        }
    }

    @Override
    public void updateQueryBidOpenAgencyMoney() {
        List<UserStatistics> userStatisticsList = this.queryBidOpenAgencyMoney();
        for (UserStatistics statistics : userStatisticsList) {
            statistics.setUpdatedTime(new Date());
            this.updateById(statistics);
        }
    }

    @Override
    public void updateQueryBidNotOpenAgencyMoney() {
        List<UserStatistics> userStatisticsList = this.queryBidNotOpenAgencyMoney();
        for (UserStatistics statistics : userStatisticsList) {
            statistics.setUpdatedTime(new Date());
            this.updateById(statistics);
        }
    }

    //查询人员报表
    @Override
    public List<UserStatistics> selectUserStatisticsByMonthAndCondition(UserStatisticsReq userStatisticsReq) {
        return this.baseMapper.selectUserStatisticsByMonthAndCondition(userStatisticsReq);
    }

    //查询处室报表
    @Override
    public List<UserStatistics> selectDepartmentStatisticsByMonthAndCondition(UserStatisticsReq userStatisticsReq) {
        return this.baseMapper.selectDepartmentStatisticsByMonthAndCondition(userStatisticsReq);
    }

    //查询公司报表
    @Override
    public List<UserStatistics> selectCompanyStatisticsByMonthAndCondition(UserStatisticsReq userStatisticsReq) {
        return this.baseMapper.selectCompanyStatisticsByMonthAndCondition(userStatisticsReq);
    }

    //查询去年人员报表
    @Override
    public List<UserStatistics> selectUserStatisticsByMonthAndConditionAgo(UserStatisticsReq userStatisticsReq) {
        return this.baseMapper.selectUserStatisticsByMonthAndConditionAgo(userStatisticsReq);
    }

    //查询去年处室报表
    @Override
    public List<UserStatistics> selectDepartmentStatisticsByMonthAndConditionAgo(UserStatisticsReq userStatisticsReq) {
        return this.baseMapper.selectDepartmentStatisticsByMonthAndConditionAgo(userStatisticsReq);
    }

    //查询去年公司报表
    @Override
    public List<UserStatistics> selectCompanyStatisticsByMonthAndConditionAgo(UserStatisticsReq userStatisticsReq) {
        return this.baseMapper.selectCompanyStatisticsByMonthAndConditionAgo(userStatisticsReq);
    }

    /**
     * 按项目经理查询
     * @return
     */
    @Override
    public List<UserStatisticsVO>  selectUserStatistics(UserStatisticsReq userStatisticsReq) {
        List<UserStatistics> userStatistics = this.baseMapper.selectUserStatisticsByMonthAndCondition(userStatisticsReq);
        List<UserStatistics> userStatisticsAgo = this.baseMapper.selectUserStatisticsByMonthAndConditionAgo(userStatisticsReq);
        List<Long>  userIds=new ArrayList<>();
        for (UserStatistics userStatistic : userStatistics) {
            Long userId = userStatistic.getUserId();
            userIds.add(userId);
        }
        List<Long>  userIdAgos=new ArrayList<>();
        for (UserStatistics userStatisticAgo : userStatisticsAgo) {
            Long userId = userStatisticAgo.getUserId();
            userIdAgos.add(userId);
        }
        List<Long> intersectionAgo = userIds.stream().filter(item -> !userIdAgos.contains(item)).collect(toList());
        List<Long> intersection = userIdAgos.stream().filter(item -> !userIds.contains(item)).collect(toList());
        List<UserStatisticsVO> userStatisticsVOS = this.baseMapper.selectUserStatistics(userStatisticsReq);
        for (UserStatisticsVO userStatisticsVO : userStatisticsVOS) {
            for (Long ago : intersectionAgo) {
                if(userStatisticsVO.getUserId()==ago){
                    BigDecimal accountAgo=new BigDecimal(0);
                    userStatisticsVO.setAccountAgo(accountAgo);
                    userStatisticsVO.setProjectCountAgo(0);
                    userStatisticsVO.setNoProjectCountAgo(0);
                    BigDecimal entrustedAmountAgo=new BigDecimal(0);
                    userStatisticsVO.setEntrustedAmountAgo(entrustedAmountAgo);
                    BigDecimal winPriceAgo=new BigDecimal(0);
                    userStatisticsVO.setWinPriceAgo(winPriceAgo);
                    BigDecimal estimateAccountAgo=new BigDecimal(0);
                    userStatisticsVO.setEstimateAccountAgo(estimateAccountAgo);
                    BigDecimal noAccountAgo=new BigDecimal(0);
                    userStatisticsVO.setNoAccountAgo(noAccountAgo);
                    BigDecimal noEntrustedAmountAgo=new BigDecimal(0);
                    userStatisticsVO.setNoEntrustedAmountAgo(noEntrustedAmountAgo);

                    userStatisticsVO.setAccountMinus(userStatisticsVO.getAccount());
                    userStatisticsVO.setProjectCountMinus(userStatisticsVO.getProjectCount());
                    userStatisticsVO.setNoProjectCountMinus(userStatisticsVO.getNoProjectCount());
                    userStatisticsVO.setEntrustedAmountMinus(userStatisticsVO.getEntrustedAmount());
                    userStatisticsVO.setWinPriceMinus(userStatisticsVO.getWinPrice());
                    userStatisticsVO.setEstimateAccountMinus(userStatisticsVO.getEstimateAccount());
                    userStatisticsVO.setNoAccountMinus(userStatisticsVO.getNoAccount());
                    userStatisticsVO.setNoEntrustedAmountMinus(userStatisticsVO.getNoEntrustedAmount());

                }
            }
            for (Long aLong : intersection) {
                if(userStatisticsVO.getUserId()==aLong){
                    BigDecimal account=new BigDecimal(0);
                    userStatisticsVO.setAccount(account);
                    userStatisticsVO.setProjectCount(0);
                    userStatisticsVO.setNoProjectCount(0);
                    BigDecimal entrustedAmount=new BigDecimal(0);
                    userStatisticsVO.setEntrustedAmount(entrustedAmount);
                    BigDecimal winPrice=new BigDecimal(0);
                    userStatisticsVO.setWinPrice(winPrice);
                    BigDecimal estimateAccount=new BigDecimal(0);
                    userStatisticsVO.setEstimateAccount(estimateAccount);
                    BigDecimal noAccount=new BigDecimal(0);
                    userStatisticsVO.setNoAccount(noAccount);
                    BigDecimal noEntrustedAmount=new BigDecimal(0);
                    userStatisticsVO.setNoEntrustedAmount(noEntrustedAmount);

                    userStatisticsVO.setAccountMinus(account.subtract(userStatisticsVO.getAccountAgo()));
                    userStatisticsVO.setProjectCountMinus(-userStatisticsVO.getProjectCountAgo());
                    userStatisticsVO.setNoProjectCountMinus(-userStatisticsVO.getNoProjectCountAgo());
                    userStatisticsVO.setEntrustedAmountMinus(entrustedAmount.subtract(userStatisticsVO.getEntrustedAmountAgo()));
                    userStatisticsVO.setWinPriceMinus(winPrice.subtract(userStatisticsVO.getWinPriceAgo()));
                    userStatisticsVO.setEstimateAccountMinus(estimateAccount.subtract(userStatisticsVO.getEstimateAccountAgo()));
                    userStatisticsVO.setNoAccountMinus(noAccount.subtract(userStatisticsVO.getNoAccountAgo()));
                    userStatisticsVO.setNoEntrustedAmountMinus(noEntrustedAmount.subtract(userStatisticsVO.getNoEntrustedAmountAgo()));
                }
            }
        }
        return userStatisticsVOS;

    }

    /**
     * 按处室查询
     * @return
     */
    @Override
    public List<UserStatisticsVO> selectDepartmentStatistics(UserStatisticsReq userStatisticsReq) {
        List<UserStatistics> userStatistics = this.baseMapper.selectDepartmentStatisticsByMonthAndCondition(userStatisticsReq);
        List<UserStatistics> userStatisticsAgo = this.baseMapper.selectDepartmentStatisticsByMonthAndConditionAgo(userStatisticsReq);
        List<Long>  departmentIds=new ArrayList<>();
        for (UserStatistics userStatistic : userStatistics) {
            Long departmentId = userStatistic.getDepartmentId();
            if(null != departmentId){
                departmentIds.add(departmentId);
            }

        }
        List<Long>  departmentIdAgos=new ArrayList<>();
        for (UserStatistics userStatisticAgo : userStatisticsAgo) {
            Long departmentId = userStatisticAgo.getDepartmentId();
            departmentIdAgos.add(departmentId);
        }
        List<Long> intersectionAgo = departmentIds.stream().filter(item -> !departmentIdAgos.contains(item)).collect(toList());
        List<Long> intersection = departmentIdAgos.stream().filter(item -> !departmentIds.contains(item)).collect(toList());
        List<UserStatisticsVO> userStatisticsVOS = this.baseMapper.selectDepartmentStatistics(userStatisticsReq);
        for (UserStatisticsVO userStatisticsVO : userStatisticsVOS) {
            for (Long ago : intersectionAgo) {
                if(userStatisticsVO.getDepartmentId()==ago){
                    BigDecimal accountAgo=new BigDecimal(0);
                    userStatisticsVO.setAccountAgo(accountAgo);
                    userStatisticsVO.setProjectCountAgo(0);
                    userStatisticsVO.setNoProjectCountAgo(0);
                    BigDecimal entrustedAmountAgo=new BigDecimal(0);
                    userStatisticsVO.setEntrustedAmountAgo(entrustedAmountAgo);
                    BigDecimal winPriceAgo=new BigDecimal(0);
                    userStatisticsVO.setWinPriceAgo(winPriceAgo);
                    BigDecimal estimateAccountAgo=new BigDecimal(0);
                    userStatisticsVO.setEstimateAccountAgo(estimateAccountAgo);
                    BigDecimal noAccountAgo=new BigDecimal(0);
                    userStatisticsVO.setNoAccountAgo(noAccountAgo);
                    BigDecimal noEntrustedAmountAgo=new BigDecimal(0);
                    userStatisticsVO.setNoEntrustedAmountAgo(noEntrustedAmountAgo);
                    userStatisticsVO.setAccountMinus(userStatisticsVO.getAccount());
                    userStatisticsVO.setProjectCountMinus(userStatisticsVO.getProjectCount());
                    userStatisticsVO.setNoProjectCountMinus(userStatisticsVO.getNoProjectCount());
                    userStatisticsVO.setEntrustedAmountMinus(userStatisticsVO.getEntrustedAmount());
                    userStatisticsVO.setWinPriceMinus(userStatisticsVO.getWinPrice());
                    userStatisticsVO.setEstimateAccountMinus(userStatisticsVO.getEstimateAccount());
                    userStatisticsVO.setNoAccountMinus(userStatisticsVO.getNoAccount());
                    userStatisticsVO.setNoEntrustedAmountMinus(userStatisticsVO.getNoEntrustedAmount());

                }
            }
            for (Long aLong : intersection) {
                if(userStatisticsVO.getDepartmentId()==aLong){
                    BigDecimal account=new BigDecimal(0);
                    userStatisticsVO.setAccount(account);
                    userStatisticsVO.setProjectCount(0);
                    userStatisticsVO.setNoProjectCount(0);
                    BigDecimal entrustedAmount=new BigDecimal(0);
                    userStatisticsVO.setEntrustedAmount(entrustedAmount);
                    BigDecimal winPrice=new BigDecimal(0);
                    userStatisticsVO.setWinPrice(winPrice);
                    BigDecimal estimateAccount=new BigDecimal(0);
                    userStatisticsVO.setEstimateAccount(estimateAccount);
                    BigDecimal noAccount=new BigDecimal(0);
                    userStatisticsVO.setNoAccount(noAccount);
                    BigDecimal noEntrustedAmount=new BigDecimal(0);
                    userStatisticsVO.setNoEntrustedAmount(noEntrustedAmount);

                    userStatisticsVO.setAccountMinus(account.subtract(userStatisticsVO.getAccountAgo()));
                    userStatisticsVO.setProjectCountMinus(-userStatisticsVO.getProjectCountAgo());
                    userStatisticsVO.setNoProjectCountMinus(-userStatisticsVO.getNoProjectCountAgo());
                    userStatisticsVO.setEntrustedAmountMinus(entrustedAmount.subtract(userStatisticsVO.getEntrustedAmountAgo()));
                    userStatisticsVO.setWinPriceMinus(winPrice.subtract(userStatisticsVO.getWinPriceAgo()));
                    userStatisticsVO.setEstimateAccountMinus(estimateAccount.subtract(userStatisticsVO.getEstimateAccountAgo()));
                    userStatisticsVO.setNoAccountMinus(noAccount.subtract(userStatisticsVO.getNoAccountAgo()));
                    userStatisticsVO.setNoEntrustedAmountMinus(noEntrustedAmount.subtract(userStatisticsVO.getNoEntrustedAmountAgo()));
                }
            }
        }
        return userStatisticsVOS;
    }

    /**
     * 按公司查询
     * @return
     */
    @Override
    public List<UserStatisticsVO> selectCompanyStatistics(UserStatisticsReq userStatisticsReq) {
        List<UserStatistics> userStatistics = this.baseMapper.selectCompanyStatisticsByMonthAndCondition(userStatisticsReq);
        List<UserStatistics> userStatisticsAgo = this.baseMapper.selectCompanyStatisticsByMonthAndConditionAgo(userStatisticsReq);
        List<String>  yearmonths=new ArrayList<>();
        for (UserStatistics userStatistic : userStatistics) {
            String yearmonth = userStatistic.getYearmonth().substring(5,7);
                yearmonths.add(yearmonth);
        }
        List<String>  yearmonthAgos=new ArrayList<>();
        for (UserStatistics userStatisticAgo : userStatisticsAgo) {
            String yearmonthAgo =userStatisticAgo.getYearmonth().substring(5,7);
                yearmonthAgos.add(yearmonthAgo);
        }
        List<String> intersectionAgo = yearmonths.stream().filter(item -> !yearmonthAgos.contains(item)).collect(toList());
        List<String> intersection = yearmonthAgos.stream().filter(item -> !yearmonths.contains(item)).collect(toList());
        List<UserStatisticsVO> userStatisticsVOS = this.baseMapper.selectCompanyStatistics(userStatisticsReq);
        for (UserStatisticsVO userStatisticsVO : userStatisticsVOS) {
            for (String ago : intersectionAgo) {
                if(userStatisticsVO.getYearmonth().substring(5,7).equals(ago)){
                    BigDecimal accountAgo=new BigDecimal(0);
                    userStatisticsVO.setAccountAgo(accountAgo);
                    userStatisticsVO.setProjectCountAgo(0L);
                    userStatisticsVO.setNoProjectCountAgo(0L);
                    BigDecimal entrustedAmountAgo=new BigDecimal(0);
                    userStatisticsVO.setEntrustedAmountAgo(entrustedAmountAgo);
                    BigDecimal winPriceAgo=new BigDecimal(0);
                    userStatisticsVO.setWinPriceAgo(winPriceAgo);
                    BigDecimal estimateAccountAgo=new BigDecimal(0);
                    userStatisticsVO.setEstimateAccountAgo(estimateAccountAgo);
                    BigDecimal noAccountAgo=new BigDecimal(0);
                    userStatisticsVO.setNoAccountAgo(noAccountAgo);
                    BigDecimal noEntrustedAmountAgo=new BigDecimal(0);
                    userStatisticsVO.setNoEntrustedAmountAgo(noEntrustedAmountAgo);

                    userStatisticsVO.setAccountMinus(userStatisticsVO.getAccount());
                    userStatisticsVO.setProjectCountMinus(userStatisticsVO.getProjectCount());
                    userStatisticsVO.setNoProjectCountMinus(userStatisticsVO.getNoProjectCount());
                    userStatisticsVO.setEntrustedAmountMinus(userStatisticsVO.getEntrustedAmount());
                    userStatisticsVO.setWinPriceMinus(userStatisticsVO.getWinPrice());
                    userStatisticsVO.setEstimateAccountMinus(userStatisticsVO.getEstimateAccount());
                    userStatisticsVO.setNoAccountMinus(userStatisticsVO.getNoAccount());
                    userStatisticsVO.setNoEntrustedAmountMinus(userStatisticsVO.getNoEntrustedAmount());

                }
            }
            for (String aLong : intersection) {
                if(userStatisticsVO.getYearmonth().substring(5,7).equals(aLong)){
                    BigDecimal account=new BigDecimal(0);
                    userStatisticsVO.setAccount(account);
                    userStatisticsVO.setProjectCount(0L);
                    userStatisticsVO.setNoProjectCount(0L);
                    BigDecimal entrustedAmount=new BigDecimal(0);
                    userStatisticsVO.setEntrustedAmount(entrustedAmount);
                    BigDecimal winPrice=new BigDecimal(0);
                    userStatisticsVO.setWinPrice(winPrice);
                    BigDecimal estimateAccount=new BigDecimal(0);
                    userStatisticsVO.setEstimateAccount(estimateAccount);
                    BigDecimal noAccount=new BigDecimal(0);
                    userStatisticsVO.setNoAccount(noAccount);
                    BigDecimal noEntrustedAmount=new BigDecimal(0);
                    userStatisticsVO.setNoEntrustedAmount(noEntrustedAmount);

                    userStatisticsVO.setAccountMinus(account.subtract(userStatisticsVO.getAccountAgo()));
                    userStatisticsVO.setProjectCountMinus(userStatisticsVO.getProjectCount()-userStatisticsVO.getProjectCountAgo());
                    userStatisticsVO.setNoProjectCountMinus(0-userStatisticsVO.getNoProjectCountAgo());
                    userStatisticsVO.setEntrustedAmountMinus(entrustedAmount.subtract(userStatisticsVO.getEntrustedAmountAgo()));
                    userStatisticsVO.setWinPriceMinus(winPrice.subtract(userStatisticsVO.getWinPriceAgo()));
                    userStatisticsVO.setEstimateAccountMinus(estimateAccount.subtract(userStatisticsVO.getEstimateAccountAgo()));
                    userStatisticsVO.setNoAccountMinus(noAccount.subtract(userStatisticsVO.getNoAccountAgo()));
                    userStatisticsVO.setNoEntrustedAmountMinus(noEntrustedAmount.subtract(userStatisticsVO.getNoEntrustedAmountAgo()));
                }
            }
        }
        return userStatisticsVOS;
    }

    /**
     * 按处室包含同比查询
     * @return
     */
    @Override
    public List<DepartmentStatisticsVO> selectDepartmentStatisticsBasis(UserStatisticsReq userStatisticsReq) {
        return this.baseMapper.selectDepartmentStatisticsBasis(userStatisticsReq);
    }
//
    @Override
    public void deleteByYearMonth(String format) {
        this.baseMapper.deleteByYearMonth(format);
    }
}
