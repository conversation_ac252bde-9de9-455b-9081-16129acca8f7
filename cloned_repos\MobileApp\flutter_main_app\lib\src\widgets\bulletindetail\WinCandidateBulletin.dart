import "package:flutter/material.dart";
import 'package:flutter_main_app/src/components/CustomAppBar.dart';
import 'package:flutter_main_app/src/widgets/bulletindetail/BulletinContact.dart';
import 'package:flutter_main_app/src/widgets/bulletindetail/BulletinDetailHeader.dart';
import 'package:flutter_main_app/src/widgets/bulletindetail/BulletinBottomBtn.dart';
import 'package:flutter_main_app/src/models/bulletin/WinCandidateBasicModel.dart';
import 'package:flutter_main_app/src/models/bulletin/ProjectManager.dart';
import 'package:flutter_main_app/src/models/bulletin/WinCandidateRequirementModel.dart';
import 'package:flutter_main_app/src/models/bulletin/EvaleBidResultModel.dart';

/// 项目负责人
class WinCandidateBulletin extends StatefulWidget {
  const WinCandidateBulletin({Key key}) : super(key: key);

  /// * 路由名称
  static const String _routeName = 'WinCandidateBulletin';
  static String get routeName => _routeName;

  @override
  _WinCandidateBulletinState createState() => _WinCandidateBulletinState();
}

class _WinCandidateBulletinState extends State<WinCandidateBulletin> {
   
  final TextStyle titleTextStyle = TextStyle(
    color: Color(0xFF414141),
    fontWeight: FontWeight.bold,
    fontSize: 13,
  );

  final TextStyle normalTextStyle = TextStyle(
    color: Color(0xFF414141),
    fontSize: 13,
    height: 2,
  );

  final TextStyle tableTextStyle = TextStyle(
    color: Color(0xFF414141),
    fontSize: 13,
    height: 1,
  );
  /// 标题的Padding
  final EdgeInsetsGeometry titlePadding = EdgeInsets.fromLTRB(15, 0, 15, 12);

  /// 基本情况
  List<WinCandidateBasicModel> winCandidateBasics = <WinCandidateBasicModel>[];

  /// 项目负责人情况
  List<ProjectManagerModel> projectMangers = <ProjectManagerModel>[];

  /// 响应情况
  List<WinCandidateRequirementModel> winCandidateRequirements = <WinCandidateRequirementModel>[];

  /// 评标结果和情况
  List<EvaleBidResultModel> evaleBidResults = <EvaleBidResultModel>[];


  void initEvalBidResult() {
    evaleBidResults.add(new EvaleBidResultModel(
      candidateName: "南通华荣建设集团有限公司",
      preQualificationReviewResult:'符合',
      preQualificationReviewComment: "/",
      qualificiationReviewFailedReason: "/",
      abandonReason: "/",
      correctionReason:'/',
      correctionBasis:'/',
      originalPrice:null,
      correctionPrice:null,
      technicalEvalePoint: <double>[32,32,25,32.5],
      bidPrice: 4899900,
      bidPricePoint: 100
    ));

    evaleBidResults.add(new EvaleBidResultModel(
      candidateName: "上海嘉事明伦医疗器材有限公司",
      preQualificationReviewResult:'符合',
      preQualificationReviewComment: "/",
      qualificiationReviewFailedReason: "/",
      abandonReason: '/',
      correctionReason:null,
      correctionBasis:null,
      originalPrice:null,
      correctionPrice:null,
      technicalEvalePoint: <double>[18,32,15,32.5],
      bidPrice: 570400,
      bidPricePoint: 98
    ));

    evaleBidResults.add(new EvaleBidResultModel(
      candidateName: "江苏城东建设工程有限公司南京 明辉建设有限公司",
      preQualificationReviewResult:'符合',
      preQualificationReviewComment: "/",
      qualificiationReviewFailedReason: "/",
      abandonReason: '/',
      correctionReason:null,
      correctionBasis:null,
      originalPrice:null,
      correctionPrice:null,
      technicalEvalePoint: <double>[18,32,15,32.5],
      bidPrice: 570600,
      bidPricePoint: 96
    ));
  }


  void initBasicInfo() {
    winCandidateBasics.add(new WinCandidateBasicModel(
      candidateName: "南通华荣建设集团有限公司",
      bidPrice: 1959.36,
      qualifity: '满足招标文件要求',
      period: '120日历天内',
      marginPayment: "/",
      performance: '/',
      prize: '/'
    ));

    winCandidateBasics.add(new WinCandidateBasicModel(
      candidateName: "上海嘉事明伦医疗器材有限公司",
      bidPrice: 1969.36,
      qualifity: '满足招标文件要求',
      period: '120日历天内',
      marginPayment: "/",
      performance: '/',
      prize: '/'
    ));

    winCandidateBasics.add(new WinCandidateBasicModel(
      candidateName: "江苏城东建设工程有限公司南京 明辉建设有限公司",
      bidPrice: 1969.26,
      qualifity: '满足招标文件要求',
      period: '120日历天内',
      marginPayment: "/",
      performance: '/',
      prize: '/'
    ));
  }

  void initProjectManager() {
    projectMangers.add(new ProjectManagerModel(
      candidateName:'南通华荣建设集团有限公司',
      projectManagerName: '刘新民',
      projectMangerLicenseNo: '01275706',
      projectMangerPerformance: '1、泗阳县淮泗河治理工程建设监理；2、沭阳县柴南河治理增补完善工程建设监理；'
    ));
    projectMangers.add(new ProjectManagerModel(
      candidateName:'上海嘉事明伦医疗器材有限公司',
      projectManagerName: '刘新民',
      projectMangerLicenseNo: '01275706',
      projectMangerPerformance: '1、泗阳县淮泗河治理工程建设监理；2、沭阳县柴南河治理增补完善工程建设监理；'
    ));
    projectMangers.add(new ProjectManagerModel(
      candidateName:'江苏城东建设工程有限公司南京 明辉建设有限公司',
      projectManagerName: '刘新民',
      projectMangerLicenseNo: '01275706',
      projectMangerPerformance: '1、泗阳县淮泗河治理工程建设监理；2、沭阳县柴南河治理增补完善工程建设监理；'
    ));
  }

  void initWinCandidateRequirements() {
    winCandidateRequirements.add(new WinCandidateRequirementModel(
      candidateName:'南通华荣建设集团有限公司',
      requirement: '满足招标文件要求',
      responseSatution: '满足招标文件要求',
    ));

    winCandidateRequirements.add(new WinCandidateRequirementModel(
      candidateName:'上海嘉事明伦医疗器材有限公司',
      requirement: '满足招标文件要求',
      responseSatution: '满足招标文件要求',
    ));

    winCandidateRequirements.add(new WinCandidateRequirementModel(
      candidateName:'江苏城东建设工程有限公司南京 明辉建设有限公司',
      requirement: '满足招标文件要求',
      responseSatution: '满足招标文件要求',
    ));
  }

  @override
  void initState() { 
    super.initState();
    this.initBasicInfo();
    this.initProjectManager();
    this.initWinCandidateRequirements();
    this.initEvalBidResult();
  }

  ///中标候选人基本情况布局
  Widget _createCandidateList(List<WinCandidateBasicModel> winCandidateBasics) {
    List<Table> tables = <Table>[];
    List<Padding> titles = <Padding>[];
    int index = 1;
    for (var winCandidateBasic in winCandidateBasics) {
      Table table = Table(
        columnWidths: {
          0: FractionColumnWidth(0.4),
          1: FractionColumnWidth(0.6)
        },
        border: TableBorder.all(
          color: Color(0xFFECECEC),
          width: 1,
        ),
        children: <TableRow>[
          TableRow(
            children: <Widget>[
              Container(
                constraints: BoxConstraints(minHeight: 33),
                child: Center(child:Text('中标候选人名称',style: tableTextStyle))
              ),
              Container(
                constraints: BoxConstraints(minHeight: 33),
                child: Center(child:Text(winCandidateBasic.candidateName,style: tableTextStyle))
              )
              
            ]
          ),
          TableRow(
            children: <Widget>[
              Container(
                constraints: BoxConstraints(minHeight: 33),
                child: Center(child:Text('投标报价(万元)',style: tableTextStyle))
              ),
              Container(
                constraints: BoxConstraints(minHeight: 33),
                child: Center(child:Text(winCandidateBasic.bidPrice.toString(),style: tableTextStyle))
              )
              
            ]
          ),
          TableRow(
            children: <Widget>[
              Container(
                constraints: BoxConstraints(minHeight: 33),
                child: Center(child:Text('质量',style: tableTextStyle))
              ),
              Container(
                constraints: BoxConstraints(minHeight: 33),
                child: Center(child:Text(winCandidateBasic.qualifity,style: tableTextStyle))
              )
            ]
          ),
          TableRow(
            children: <Widget>[
              Container(
                constraints: BoxConstraints(minHeight: 33),
                child: Center(child:Text('工期/交货期/服务期',style: tableTextStyle))
              ),
              Container(
                constraints: BoxConstraints(minHeight: 33),
                child: Center(child:Text(winCandidateBasic.period,style: tableTextStyle))
              )
            ]
          ),
          TableRow(
            children: <Widget>[
              Container(
                constraints: BoxConstraints(minHeight: 33),
                child: Center(child:Text('保证金缴纳情况',style: tableTextStyle))
              ),
              Container(
                constraints: BoxConstraints(minHeight: 33),
                child: Center(child:Text(winCandidateBasic.marginPayment,style: tableTextStyle))
              )
            ]
          ),
          TableRow(
            children: <Widget>[
              Container(
                constraints: BoxConstraints(minHeight: 33),
                child: Center(child:Text('企业业绩',style: tableTextStyle))
              ),
              Container(
                constraints: BoxConstraints(minHeight: 33),
                child: Center(child:Text(winCandidateBasic.performance,style: tableTextStyle))
              )
            ]
          ),
          TableRow(
            children: <Widget>[
              Container(
                constraints: BoxConstraints(minHeight: 33),
                child: Center(child:Text('企业获奖情况',style: tableTextStyle))
              ),
              Container(
                constraints: BoxConstraints(minHeight: 33),
                child: Center(child:Text(winCandidateBasic.prize,style: tableTextStyle))
              )
            ]
          ),
        ],
      );
      tables.add(table);
      Padding titleText = 
        Padding(padding: EdgeInsets.only(bottom: 8,top:12),child:Text('中标候选人第$index名',style: normalTextStyle));
      titles.add(titleText);
      index = index +1;
    }
    
    List<Widget> widgets = <Widget>[];
    
    for(int i=0;i<tables.length;i++) {
      widgets.add(titles[i]);
      widgets.add(tables[i]);
    }
    
    return Padding(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: widgets
      ),
      padding: EdgeInsets.fromLTRB(15, 0, 15, 18),
    );
  }

  /// 项目负责人基本情况
  Widget _createProjectMangerList(List<ProjectManagerModel> projectMangers) {
    List<Table> tables = <Table>[];
    List<Padding> titles = <Padding>[];
    int index = 1;
    for (var projectManger in projectMangers) {
      Table table = Table(
        columnWidths: {
          0: FractionColumnWidth(0.4),
          1: FractionColumnWidth(0.6)
        },
        border: TableBorder.all(
          color: Color(0xFFECECEC),
          width: 1,
        ),
        children: <TableRow>[
          TableRow(
            children: <Widget>[
              Container(
                constraints: BoxConstraints(minHeight: 33),
                child: Center(child:Text('中标候选人名称',style: tableTextStyle))
              ),
              Container(
                constraints: BoxConstraints(minHeight: 33),
                child: Center(child:Text(projectManger.candidateName,style: tableTextStyle))
              )
              
            ]
          ),
          TableRow(
            children: <Widget>[
              Container(
                constraints: BoxConstraints(minHeight: 33),
                child: Center(child:Text('项目负责人',style: tableTextStyle))
              ),
              Container(
                constraints: BoxConstraints(minHeight: 33),
                child: Center(child:Text(projectManger.projectManagerName,style: tableTextStyle))
              )
              
            ]
          ),
          TableRow(
            children: <Widget>[
              Container(
                constraints: BoxConstraints(minHeight: 33),
                child: Center(child:Text('项目负责人证号',style: tableTextStyle))
              ),
              Container(
                constraints: BoxConstraints(minHeight: 33),
                child: Center(child:Text(projectManger.projectMangerLicenseNo,style: tableTextStyle))
              )
            ]
          ),
          TableRow(
            children: <Widget>[
              Container(
                constraints: BoxConstraints(minHeight: 33),
                child: Center(child:Text('项目负责人业绩',style: tableTextStyle))
              ),
              Container(
                constraints: BoxConstraints(minHeight: 33),
                child: Center(child:Text(projectManger.projectMangerPerformance,style: tableTextStyle))
              )
            ]
          ),
        ],
      );
      tables.add(table);
      Padding titleText = 
        Padding(padding: EdgeInsets.only(bottom: 8,top:12),child:Text('中标候选人第$index名',style: normalTextStyle));
      titles.add(titleText);
      index = index +1;
    }
    
    List<Widget> widgets = <Widget>[];
    
    for(int i=0;i<tables.length;i++) {
      widgets.add(titles[i]);
      widgets.add(tables[i]);
    }
    
    return Padding(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: widgets
      ),
      padding: EdgeInsets.fromLTRB(15, 0, 15, 18),
    );
  }

  /// 响应招标文件要求的资格能力
  Widget _createRequriementList(List<WinCandidateRequirementModel> winCandidateRequirements) {
    List<Table> tables = <Table>[];
    List<Padding> titles = <Padding>[];
    int index = 1;
    for (var winCandidateRequirement in winCandidateRequirements) {
      Table table = Table(
        columnWidths: {
          0: FractionColumnWidth(0.4),
          1: FractionColumnWidth(0.6)
        },
        border: TableBorder.all(
          color: Color(0xFFECECEC),
          width: 1,
        ),
        children: <TableRow>[
          TableRow(
            children: <Widget>[
              Container(
                constraints: BoxConstraints(minHeight: 33),
                child: Center(child:Text('中标候选人名称',style: tableTextStyle))
              ),
              Container(
                constraints: BoxConstraints(minHeight: 33),
                child: Center(child:Text(winCandidateRequirement.candidateName,style: tableTextStyle))
              )
              
            ]
          ),
          TableRow(
            children: <Widget>[
              Container(
                constraints: BoxConstraints(minHeight: 33),
                child: Center(child:Text('资格能力条件',style: tableTextStyle))
              ),
              Container(
                constraints: BoxConstraints(minHeight: 33),
                child: Center(child:Text(winCandidateRequirement.requirement,style: tableTextStyle))
              )
              
            ]
          ),
          TableRow(
            children: <Widget>[
              Container(
                constraints: BoxConstraints(minHeight: 33),
                child: Center(child:Text('响应情况',style: tableTextStyle))
              ),
              Container(
                constraints: BoxConstraints(minHeight: 33),
                child: Center(child:Text(winCandidateRequirement.responseSatution,style: tableTextStyle))
              )
            ]
          ) 
        ],
      );
      tables.add(table);
      Padding titleText = 
        Padding(padding: EdgeInsets.only(bottom: 8,top:12),child:Text('中标候选人第$index名',style: normalTextStyle));
      titles.add(titleText);
      index = index +1;
    }
    
    List<Widget> widgets = <Widget>[];
    
    for(int i=0;i<tables.length;i++) {
      widgets.add(titles[i]);
      widgets.add(tables[i]);
    }
    
    return Padding(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: widgets
      ),
      padding: EdgeInsets.fromLTRB(15, 0, 15, 18),
    );
  }

  /// (1)资格预审结果名单及原因：
  Widget _createPreQualifyResult(List<EvaleBidResultModel> evaleBidResults) {
    Table table = Table(
        columnWidths: {
          0: FractionColumnWidth(0.45),
          1: FractionColumnWidth(0.15),
          2: FractionColumnWidth(0.40)
        },
        border: TableBorder.all(
          color: Color(0xFFECECEC),
          width: 1,
        ),
        children: <TableRow>[
           TableRow(
            children: <Widget>[
              Container(
                constraints: BoxConstraints(minHeight: 33),
                child: Center(child:Text('单位名称',style: tableTextStyle))
              ),
              Container(
                constraints: BoxConstraints(minHeight: 33),
                child: Center(child:Text('结果',style: tableTextStyle))
              ),
              Container(
                constraints: BoxConstraints(minHeight: 33),
                child: Center(child:Text('备注',style: tableTextStyle))
              )
            ]
          ),
        ]
    );

    for (var evaleBidResult in evaleBidResults) {
      TableRow row = TableRow(
          children: <Widget>[
            Container(
              constraints: BoxConstraints(minHeight: 33),
              child: Center(child:Text(evaleBidResult.candidateName,style: tableTextStyle))
            ),
            Container(
              constraints: BoxConstraints(minHeight: 33),
              child: Center(child:Text(evaleBidResult.preQualificationReviewResult,style: tableTextStyle))
            ),
            Container(
              constraints: BoxConstraints(minHeight: 33),
              child: Center(child:Text(evaleBidResult.preQualificationReviewComment,style: tableTextStyle))
            )
          ]
      );
      table.children.add(row);
    }
    return table;
  }
  
  /// (2)资格后审不合格名单及原因：
  Widget _createQuailfyFaildResult(List<EvaleBidResultModel> evaleBidResults) {
    Table table = Table(
        columnWidths: {
          0: FractionColumnWidth(0.55),
          1: FractionColumnWidth(0.45),
        },
        border: TableBorder.all(
          color: Color(0xFFECECEC),
          width: 1,
        ),
        children: <TableRow>[
          TableRow(
            children: <Widget>[
              Container(
                constraints: BoxConstraints(minHeight: 33),
                child: Center(child:Text('单位名称',style: tableTextStyle))
              ),
              Container(
                constraints: BoxConstraints(minHeight: 33),
                child: Center(child:Text('不合格原因',style: tableTextStyle))
              ),
            ]
          )
        ]
    );
    for (var evaleBidResult in evaleBidResults) {
      TableRow row = TableRow(
          children: <Widget>[
            Container(
              constraints: BoxConstraints(minHeight: 33),
              child: Center(child:Text(evaleBidResult.candidateName,style: tableTextStyle))
            ),
            Container(
              constraints: BoxConstraints(minHeight: 33),
              child: Center(child:Text(evaleBidResult.qualificiationReviewFailedReason,style: tableTextStyle))
            ),
          ]
      );
      table.children.add(row);
    }
    return table;
  }

  /// (3)废标及原因：
  Widget _createAbandonResult(List<EvaleBidResultModel> evaleBidResults) {
    Table table = Table(
        columnWidths: {
          0: FractionColumnWidth(0.55),
          1: FractionColumnWidth(0.45),
        },
        border: TableBorder.all(
          color: Color(0xFFECECEC),
          width: 1,
        ),
        children: <TableRow>[
          TableRow(
            children: <Widget>[
              Container(
                constraints: BoxConstraints(minHeight: 33),
                child: Center(child:Text('单位名称',style: tableTextStyle))
              ),
              Container(
                constraints: BoxConstraints(minHeight: 33),
                child: Center(child:Text('废标原因',style: tableTextStyle))
              ),
            ]
          )
        ]
    );
    for (var evaleBidResult in evaleBidResults) {
      TableRow row = TableRow(
          children: <Widget>[
            Container(
              constraints: BoxConstraints(minHeight: 33),
              child: Center(child:Text(evaleBidResult.candidateName,style: tableTextStyle))
            ),
            Container(
              constraints: BoxConstraints(minHeight: 33),
              child: Center(child:Text(evaleBidResult.abandonReason,style: tableTextStyle))
            ),
          ]
      );
      table.children.add(row);
    }
    return table;
  }

  /// (4)报价修正：
  Widget _createCorrectionPriceResult(List<EvaleBidResultModel> evaleBidResults) {
    List<Table> correctionTables = <Table>[];
    for (var evaleBidResult in evaleBidResults) {
      if(evaleBidResult.correctionReason != null && evaleBidResult.correctionBasis != null) {
        Table table = Table(
          columnWidths: {
            0: FractionColumnWidth(0.55),
            1: FractionColumnWidth(0.45),
          },
          border: TableBorder.all(
            color: Color(0xFFECECEC),
            width: 1,
          ),
          children: <TableRow>[
            TableRow(
              children: <Widget>[
                Container(
                  constraints: BoxConstraints(minHeight: 33),
                  child: Center(child:Text('单位名称',style: tableTextStyle))
                ),
                Container(
                  constraints: BoxConstraints(minHeight: 33),
                  child: Center(child:Text(evaleBidResult.candidateName,style: tableTextStyle))
                ),
              ],
            ),

            TableRow(
              children: <Widget>[
                Container(
                  constraints: BoxConstraints(minHeight: 33),
                  child: Center(child:Text('修正原因',style: tableTextStyle))
                ),
                Container(
                  constraints: BoxConstraints(minHeight: 33),
                  child: Center(child:Text(evaleBidResult.correctionReason,style: tableTextStyle))
                ),
              ],
            ),

            TableRow(
              children: <Widget>[
                Container(
                  constraints: BoxConstraints(minHeight: 33),
                  child: Center(child:Text('修正依据',style: tableTextStyle))
                ),
                Container(
                  constraints: BoxConstraints(minHeight: 33),
                  child: Center(child:Text(evaleBidResult.correctionBasis,style: tableTextStyle))
                ),
              ],
            ),
          ]
        );
        if(evaleBidResult.originalPrice == null) {
          table.children.add(
            TableRow(
              children: <Widget>[
                Container(
                  constraints: BoxConstraints(minHeight: 33),
                  child: Center(child:Text('修正前报价',style: tableTextStyle))
                ),
                Container(
                  constraints: BoxConstraints(minHeight: 33),
                  child: Center(child:Text("/",style: tableTextStyle))
                ),
              ],
            ),
          );
        } else {
          table.children.add(
            TableRow(
              children: <Widget>[
                Container(
                  constraints: BoxConstraints(minHeight: 33),
                  child: Center(child:Text('修正前报价',style: tableTextStyle))
                ),
                Container(
                  constraints: BoxConstraints(minHeight: 33),
                  child: Center(child:Text(evaleBidResult.originalPrice.toString(),style: tableTextStyle))
                ),
              ],
            ),
          );
        }

        if(evaleBidResult.correctionPrice == null) {
          table.children.add(
            TableRow(
              children: <Widget>[
                Container(
                  constraints: BoxConstraints(minHeight: 33),
                  child: Center(child:Text('修正后报价',style: tableTextStyle))
                ),
                Container(
                  constraints: BoxConstraints(minHeight: 33),
                  child: Center(child:Text("/",style: tableTextStyle))
                ),
              ],
            ),
          );
        } else {
          table.children.add(
            TableRow(
              children: <Widget>[
                Container(
                  constraints: BoxConstraints(minHeight: 33),
                  child: Center(child:Text('修正后报价',style: tableTextStyle))
                ),
                Container(
                  constraints: BoxConstraints(minHeight: 33),
                  child: Center(child:Text(evaleBidResult.correctionPrice.toString(),style: tableTextStyle))
                ),
              ],
            ),
          );
        }
        correctionTables.add(table);
      }
    }

    Column correctionTableColumn = Column(
      children: <Widget>[

      ],
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.start,
    );
    for(var correctionTable in correctionTables) {
      correctionTableColumn.children.add(
        new Padding(
          padding: EdgeInsets.only(bottom: 12),
          child: correctionTable,
        )
      );
    }
    return correctionTableColumn;
  }

  /// (5)所有投标人技术标评分情况：
  Widget _createTechnicalEvalePointResult(List<EvaleBidResultModel> evaleBidResults) {
    List<Table> technicalTables = <Table>[];
    for (var evaleBidResult in evaleBidResults) {
      Table table = Table(
          columnWidths: {
            0: FractionColumnWidth(0.55),
            1: FractionColumnWidth(0.45),
          },
          border: TableBorder.all(
            color: Color(0xFFECECEC),
            width: 1,
          ),
          children: <TableRow>[
            TableRow(
              children: <Widget>[
                Container(
                  constraints: BoxConstraints(minHeight: 33),
                  child: Center(child:Text('单位名称',style: tableTextStyle))
                ),
                Container(
                  constraints: BoxConstraints(minHeight: 33),
                  child: Center(child:Text(evaleBidResult.candidateName,style: tableTextStyle))
                ),
              ],
            ),
          ]
      );
      int idx = 1;
      for (var technicalEvalePoint in evaleBidResult.technicalEvalePoint) {
         TableRow tableRow = TableRow(
            children: <Widget>[
              Container(
                constraints: BoxConstraints(minHeight: 33),
                child: Center(child:Text('评委$idx',style: tableTextStyle))
              ),
              Container(
                constraints: BoxConstraints(minHeight: 33),
                child: Center(child:Text(technicalEvalePoint.toString(),style: tableTextStyle))
              ),
            ],
        );
        table.children.add(tableRow);
        idx = idx + 1;
      }
      technicalTables.add(table);
    }

    Column technicalTableColumn = Column(
      children: <Widget>[

      ],
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.start,
    );
    for(var correctionTable in technicalTables) {
      technicalTableColumn.children.add(
        new Padding(
          padding: EdgeInsets.only(bottom: 12),
          child: correctionTable,
        )
      );
    }
    return technicalTableColumn;
  }

  /// (6)所有投标单位得分情况：
  Widget _createAllEvalPointResult(List<EvaleBidResultModel> evaleBidResults) {
    List<Table> allEvalPointTables = <Table>[];
    for (var evaleBidResult in evaleBidResults) {
      Table table = Table(
          columnWidths: {
            0: FractionColumnWidth(0.55),
            1: FractionColumnWidth(0.45),
          },
          border: TableBorder.all(
            color: Color(0xFFECECEC),
            width: 1,
          ),
          children: <TableRow>[
            TableRow(
              children: <Widget>[
                Container(
                  constraints: BoxConstraints(minHeight: 33),
                  child: Center(child:Text('单位名称',style: tableTextStyle))
                ),
                Container(
                  constraints: BoxConstraints(minHeight: 33),
                  child: Center(child:Text(evaleBidResult.candidateName,style: tableTextStyle))
                ),
              ],
            ),
            TableRow(
              children: <Widget>[
                Container(
                  constraints: BoxConstraints(minHeight: 33),
                  child: Center(child:Text('投标报价(元)',style: tableTextStyle))
                ),
                Container(
                  constraints: BoxConstraints(minHeight: 33),
                  child: Center(child:Text(evaleBidResult.bidPrice.toString(),style: tableTextStyle))
                ),
              ],
            ),
            TableRow(
              children: <Widget>[
                Container(
                  constraints: BoxConstraints(minHeight: 33),
                  child: Center(child:Text('报价分',style: tableTextStyle))
                ),
                Container(
                  constraints: BoxConstraints(minHeight: 33),
                  child: Center(child:Text(evaleBidResult.bidPricePoint.toString(),style: tableTextStyle))
                ),
              ],
            ),
          ]
      );
      allEvalPointTables.add(table);
    }

    Column allEvalePointTableColumn = Column(
      children: <Widget>[

      ],
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.start,
    );
    for(var allEvalPointTable in allEvalPointTables) {
      allEvalePointTableColumn.children.add(
        new Padding(
          padding: EdgeInsets.only(bottom: 12),
          child: allEvalPointTable,
        )
      );
    }
    return allEvalePointTableColumn;

  }


  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: '中标候选人公示',
        actions: <Widget>[
          IconButton(
            icon: Icon(Icons.share, color: Colors.white),
            onPressed: null,
            iconSize: 20,
          )
        ],
      ),
      body: Container(
        color: Colors.white,
        child: Stack(
          children: <Widget>[
            ListView(
              children: <Widget>[
                BulletinDetailHeader(),
                
                Padding(
                  padding: EdgeInsets.fromLTRB(15, 18, 15, 18),
                  child: Row(
                    children: <Widget>[
                      Text(
                        '公告结束时间：',
                        style: normalTextStyle,
                      ),
                      Text(
                        '2019年10月17日',
                        style: normalTextStyle
                      )
                    ],
                  )
                ),
                Padding(
                  padding: EdgeInsets.fromLTRB(15, 0, 15, 12),
                  child: Text(
                   '1.评标情况',
                   style: titleTextStyle, 
                  ),
                ),
                Padding(
                  padding: EdgeInsets.fromLTRB(15, 0, 15, 12),
                  child: Text(
                   '(1)中标候选人基本情况',
                   style: normalTextStyle, 
                  ),
                ),
                _createCandidateList(this.winCandidateBasics),

                Padding(
                  padding: EdgeInsets.fromLTRB(15, 0, 15, 12),
                  child: Text(
                   '(2)项目负责人基本情况',
                   style: normalTextStyle, 
                  ),
                ),

                _createProjectMangerList(this.projectMangers),

                Padding(
                  padding: EdgeInsets.fromLTRB(15, 0, 15, 12),
                  child: Text(
                   '(3)响应招标文件要求的资格能力',
                   style: normalTextStyle, 
                  ),
                ),

                _createRequriementList(this.winCandidateRequirements),

                Padding(
                  padding: EdgeInsets.fromLTRB(15, 0, 15, 12),
                  child: Text(
                   '2. 提出异议的渠道和方式',
                   style: titleTextStyle, 
                  ),
                ),

                Padding(
                  padding: EdgeInsets.fromLTRB(15, 0, 15, 12),
                  child: Text(
                   '公示期间，投标人或其他利害关系人对中标候选人有异议的，请以书面形式加盖单位公章后以纸质文件向招标代理机构提出',
                   style: normalTextStyle, 
                  ),
                ),

                Padding(
                  padding: EdgeInsets.fromLTRB(15, 0, 15, 12),
                  child: Text(
                   '3. 其他',
                   style: titleTextStyle, 
                  ),
                ),

                Padding(
                  padding: EdgeInsets.fromLTRB(15, 0, 15, 12),
                  child: Text(
                   '(1)资格预审结果名单及原因：',
                   style: normalTextStyle, 
                  ),
                ),

                _createPreQualifyResult(this.evaleBidResults),

                Padding(
                  padding: EdgeInsets.fromLTRB(15, 0, 15, 12),
                  child: Text(
                   '(2)资格后审不合格名单及原因：',
                   style: normalTextStyle, 
                  ),
                ),

                _createQuailfyFaildResult(this.evaleBidResults),

                Padding(
                  padding: EdgeInsets.fromLTRB(15, 0, 15, 12),
                  child: Text(
                   '(3)废标及原因：',
                   style: normalTextStyle, 
                  ),
                ),

                _createAbandonResult(this.evaleBidResults),

                Padding(
                  padding: EdgeInsets.fromLTRB(15, 0, 15, 12),
                  child: Text(
                   '(4)报价修正：',
                   style: normalTextStyle, 
                  ),
                ),

                _createCorrectionPriceResult(this.evaleBidResults),

                Padding(
                  padding: EdgeInsets.fromLTRB(15, 0, 15, 12),
                  child: Text(
                   '(5)所有投标人技术标评分情况：',
                   style: normalTextStyle, 
                  ),
                ),

                _createTechnicalEvalePointResult(this.evaleBidResults),

                Padding(
                  padding: EdgeInsets.fromLTRB(15, 0, 15, 12),
                  child: Text(
                   '(6)所有投标单位得分情况：',
                   style: normalTextStyle, 
                  ),
                ),

                _createAllEvalPointResult(this.evaleBidResults),

                Padding(
                  padding: EdgeInsets.fromLTRB(15, 0, 15, 12),
                  child: Text(
                   '(7)拟确定中标人：',
                   style: normalTextStyle, 
                  ),
                ),

                Padding(
                  padding: EdgeInsets.fromLTRB(15, 0, 15, 12),
                  child: Text(
                   '南通华荣建设集团有限公司',
                   style: normalTextStyle, 
                  ),
                ),

                Padding(
                  padding: EdgeInsets.fromLTRB(15, 0, 15, 12),
                  child: Text(
                   '4. 监督部门',
                   style: titleTextStyle, 
                  ),
                ),

                Padding(
                  padding: EdgeInsets.fromLTRB(15, 0, 15, 12),
                  child: Text(
                   '本招标项目的监督部门为江苏瀛洲发展集团有限公司。',
                   style: normalTextStyle, 
                  ),
                ),

                Padding(
                  padding: EdgeInsets.fromLTRB(15, 0, 15, 12),
                  child: Text(
                   '5. 联系方式',
                   style: titleTextStyle, 
                  ),
                ),

                
                BullectinContact(),
          
              ],
              
            ),
            Align(
              alignment: Alignment.bottomCenter,
              child: BulletinBottomBtn(),
            )
          ]
        ),
      ));
  }
}