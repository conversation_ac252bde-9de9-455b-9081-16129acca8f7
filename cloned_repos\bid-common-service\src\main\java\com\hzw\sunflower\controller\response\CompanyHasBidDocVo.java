package com.hzw.sunflower.controller.response;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 已递交投标文件供应商报名信息返回实体
 *
 * <AUTHOR>
 * @version 1.0.0 2021-04-12
 */
@ApiModel(description = "已递交投标文件供应商报名信息返回实体")
@Data
public class CompanyHasBidDocVo {

    @ApiModelProperty(value = "报名表ID")
    private Long applyId;

    @ApiModelProperty(value = "项目ID")
    private Long projectId;

    @ApiModelProperty(value = "标段ID")
    private Long subId;

    @ApiModelProperty(value = "企业ID")
    private Long companyId;

    @ApiModelProperty(value = "企业名称")
    private String companyName;

    @ApiModelProperty(value = "联系人")
    private String contactPerson;

    @ApiModelProperty(value = "联系电话")
    private String userMobile;

    @ApiModelProperty(value = "采购编号")
    private String purchaseNumber;

    @ApiModelProperty(value = "采购名称")
    private String purchaseName;

    @ApiModelProperty(value = "包件(0:无包 1:有包)")
    private Integer packageSegmentStatus;

    @ApiModelProperty(value = "包号")
    private Integer packageNumber;

    @ApiModelProperty(value ="解密时间（分钟）")
    private Integer decryptTime;

    @ApiModelProperty(value ="解密到期时间")
    private String decryptEndTime;
}