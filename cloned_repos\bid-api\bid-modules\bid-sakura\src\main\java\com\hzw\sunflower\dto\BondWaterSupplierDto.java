package com.hzw.sunflower.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @ClassName:BondWaterSupplierDto
 * @Auther: lijinxin
 * @Description: 供应商流水检索dto
 * @Date: 2023/5/16 14:55
 * @Version: v1.0
 */
@Data
public class BondWaterSupplierDto {

    @ApiModelProperty("企业名称")
    private String companyName;

    @ApiModelProperty("已经关联流水id集合")
    private List<Long> relationWaterIds;

    @ApiModelProperty("交易金额")
    private String amount;

    @ApiModelProperty("交易日期")
    private String date;
}
