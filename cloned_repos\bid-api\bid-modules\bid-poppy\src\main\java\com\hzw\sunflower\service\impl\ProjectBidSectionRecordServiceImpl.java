package com.hzw.sunflower.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.constant.constantenum.EngineeringClassEnum;
import com.hzw.sunflower.controller.project.request.MoneyReq;
import com.hzw.sunflower.dao.ProjectBidSectionRecordMapper;
import com.hzw.sunflower.entity.ProjectBidSectionRecord;
import com.hzw.sunflower.service.ProjectBidSectionRecordService;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

/**
 * 项目标段表 Service接口实现
 *
 */
@Service
public class ProjectBidSectionRecordServiceImpl extends ServiceImpl<ProjectBidSectionRecordMapper, ProjectBidSectionRecord>
        implements ProjectBidSectionRecordService {

    @Override
    public BigDecimal calculate_1Moneys(MoneyReq req) {
        BigDecimal result = new BigDecimal(0);
        // 中标金额
        BigDecimal bidAmount = req.getBidAmount();
        BigDecimal r1 = new BigDecimal("1000000");
        BigDecimal r2 = new BigDecimal("5000000");
        BigDecimal r3 = new BigDecimal("10000000");
        BigDecimal r4 = new BigDecimal("50000000");
        BigDecimal r5 = new BigDecimal("100000000");
        BigDecimal r6 = new BigDecimal("1000000000");
        BigDecimal r7 = new BigDecimal("4000000");
        BigDecimal r8 = new BigDecimal("40000000");
        BigDecimal r9 = new BigDecimal("900000000");
        BigDecimal m1 = new BigDecimal("1.5");
        BigDecimal m2 = new BigDecimal("1.1");
        BigDecimal m3 = new BigDecimal("0.8");
        BigDecimal m4 = new BigDecimal("0.5");
        BigDecimal m5 = new BigDecimal("0.25");
        BigDecimal m6 = new BigDecimal("0.05");
        BigDecimal m7 = new BigDecimal("0.01");

        if (req.getType().equals(EngineeringClassEnum.GOODS.getType())) {
            // 3.货物
            m1 = new BigDecimal("1.5");
            m2 = new BigDecimal("1.1");
            m3 = new BigDecimal("0.8");
            m4 = new BigDecimal("0.5");
            m5 = new BigDecimal("0.25");

        } else if (req.getType().equals(EngineeringClassEnum.SERVICE.getType())) {
            //  2.服务
            m1 = new BigDecimal("1.5");
            m2 = new BigDecimal("0.8");
            m3 = new BigDecimal("0.45");
            m4 = new BigDecimal("0.25");
            m5 = new BigDecimal("0.1");

        } else if (req.getType().equals(EngineeringClassEnum.ENGINEERING.getType())) {
            // 1.工程
            m1 = new BigDecimal("1");
            m2 = new BigDecimal("0.7");
            m3 = new BigDecimal("0.55");
            m4 = new BigDecimal("0.35");
            m5 = new BigDecimal("0.2");


        }
        if (bidAmount.compareTo(r1) < 1) {
            result = bidAmount.multiply(m1);
        } else if (bidAmount.compareTo(r2) < 1) {
            result = r1.multiply(m1).add((bidAmount.subtract(r1)).multiply(m2));
        } else if (bidAmount.compareTo(r3) < 1) {
            result = r1.multiply(m1).add(r7.multiply(m2)).add((bidAmount.subtract(r2)).multiply(m3));
        } else if (bidAmount.compareTo(r4) < 1) {
            result = r1.multiply(m1).add(r7.multiply(m2)).add(r2.multiply(m3)).add((bidAmount.subtract(r3)).multiply(m4));
        } else if (bidAmount.compareTo(r5) < 1) {
            result = r1.multiply(m1).add(r7.multiply(m2)).add(r2.multiply(m3)).add(r8.multiply(m4)).add((bidAmount.subtract(r4)).multiply(m5));
        } else if (bidAmount.compareTo(r6) < 1) {
            result = r1.multiply(m1).add(r7.multiply(m2)).add(r2.multiply(m3)).add(r8.multiply(m4)).add(r4.multiply(m5)).add((bidAmount.subtract(r5)).multiply(m6));
        } else if (bidAmount.compareTo(r6) == 1) {
            result = r1.multiply(m1).add(r7.multiply(m2)).add(r2.multiply(m3)).add(r8.multiply(m4)).add(r4.multiply(m5)).add(r9.multiply(m6)).add((bidAmount.subtract(r6)).multiply(m7));
        }

        BigDecimal result1 = result.multiply(new BigDecimal(req.getChargeContent()));
        //Result<BigDecimal> res = Result.ok();
        //res.setData(result1.divide(new BigDecimal(10000)));
        return result1.divide(new BigDecimal(10000));
    }

    @Override
    public BigDecimal calculate_2Moneys(MoneyReq req) {
        BigDecimal result = new BigDecimal(0);
        // 中标金额
        BigDecimal bidAmount = req.getBidAmount();
        // 50W
        BigDecimal r1 = new BigDecimal("500000");
        // 400W
        BigDecimal r2 = new BigDecimal("4000000");

        BigDecimal r1_2 = r2.subtract(r1);
        // 1000W
        BigDecimal r3 = new BigDecimal("10000000");

        BigDecimal r2_3 = r3.subtract(r2);
        // 5000W
        BigDecimal r4 = new BigDecimal("50000000");

        BigDecimal r3_4 = r4.subtract(r3);
        // 1亿
        BigDecimal r5 = new BigDecimal("100000000");

        BigDecimal r4_5 = r5.subtract(r4);
        // 10亿
        BigDecimal r6 = new BigDecimal("1000000000");

        BigDecimal r5_6 = r6.subtract(r5);
        // 50亿
        BigDecimal r7 = new BigDecimal("5000000000");

        BigDecimal r6_7 = r7.subtract(r6);

        // 1W元
        BigDecimal m1 = new BigDecimal("10000");
        BigDecimal m2 = new BigDecimal("1.5");
        BigDecimal m3 = new BigDecimal("1.1");
        BigDecimal m4 = new BigDecimal("0.8");
        BigDecimal m5 = new BigDecimal("0.25");
        BigDecimal m6 = new BigDecimal("0.05");
        BigDecimal m7 = new BigDecimal("0.01");
        BigDecimal m8 = new BigDecimal("0.005");

        if (req.getType().equals(EngineeringClassEnum.GOODS.getType())) {
            // 3.货物
            m1 = new BigDecimal("10000");
            m2 = new BigDecimal("0.015");
            m3 = new BigDecimal("0.011");
            m4 = new BigDecimal("0.008");
            m5 = new BigDecimal("0.0025");
            m6 = new BigDecimal("0.0005");
            m7 = new BigDecimal("0.0001");
            m8 = new BigDecimal("0.00005");

        } else if (req.getType().equals(EngineeringClassEnum.SERVICE.getType())) {
            //  2.服务
            m1 = new BigDecimal("10000");
            m2 = new BigDecimal("0.015");
            m3 = new BigDecimal("0.009");
            m4 = new BigDecimal("0.0045");
            m5 = new BigDecimal("0.0015");
            m6 = new BigDecimal("0.0005");
            m7 = new BigDecimal("0.0001");
            m8 = new BigDecimal("0.00005");

        } else if (req.getType().equals(EngineeringClassEnum.ENGINEERING.getType())) {
            // 1.工程
            m1 = new BigDecimal("10000");
            m2 = new BigDecimal("0.015");
            m3 = new BigDecimal("0.008");
            m4 = new BigDecimal("0.0055");
            m5 = new BigDecimal("0.002");
            m6 = new BigDecimal("0.0005");
            m7 = new BigDecimal("0.0001");
            m8 = new BigDecimal("0.00005");


        }
        if (bidAmount.compareTo(r1) < 1) {
            result =m1;
        } else if (bidAmount.compareTo(r2) < 1) {
            result = m1
                    .add((bidAmount.subtract(r1)).multiply(m2));
        } else if (bidAmount.compareTo(r3) < 1) {
            result = m1
                    .add(r1_2.multiply(m2))
                    .add((bidAmount.subtract(r2)).multiply(m3));
        } else if (bidAmount.compareTo(r4) < 1) {
            result = m1
                    .add(r1_2.multiply(m2))
                    .add(r2_3.multiply(m3))
                    .add((bidAmount.subtract(r3)).multiply(m4));
        } else if (bidAmount.compareTo(r5) < 1) {
            result = m1
                    .add(r1_2.multiply(m2))
                    .add(r2_3.multiply(m3))
                    .add(r3_4.multiply(m4))
                    .add((bidAmount.subtract(r4)).multiply(m5));
        } else if (bidAmount.compareTo(r6) < 1) {
            result = m1
                    .add(r1_2.multiply(m2))
                    .add(r2_3.multiply(m3))
                    .add(r3_4.multiply(m4))
                    .add(r4_5.multiply(m5))
                    .add((bidAmount.subtract(r5)).multiply(m6));
        } else if (bidAmount.compareTo(r7) < 1) {
            result = m1
                    .add(r1_2.multiply(m2))
                    .add(r2_3.multiply(m3))
                    .add(r3_4.multiply(m4))
                    .add(r4_5.multiply(m5))
                    .add(r5_6.multiply(m6))
                    .add((bidAmount.subtract(r6)).multiply(m7));
        }else  if (bidAmount.compareTo(r7) == 1) {
            result = m1
                    .add(r1_2.multiply(m2))
                    .add(r2_3.multiply(m3))
                    .add(r3_4.multiply(m4))
                    .add(r4_5.multiply(m5))
                    .add(r5_6.multiply(m6))
                    .add(r6_7.multiply(m7))
                    .add((bidAmount.subtract(r7)).multiply(m8));
        }

        // 元
        BigDecimal fee = result.multiply(new BigDecimal(req.getChargeContent()).divide(new BigDecimal(100)));
        //Result<BigDecimal> res = Result.ok();
        //res.setData(fee);
//        res.setData(result1.divide(new BigDecimal(10000)));
        return fee;
    }

    @Override
    public BigDecimal calculate_3Moneys(MoneyReq req) {
        // 中标金额
        BigDecimal bidAmount = req.getBidAmount();
        BigDecimal result = bidAmount.multiply(new BigDecimal(req.getChargeContent()).divide(new BigDecimal(100)));
        return result;
    }
}
