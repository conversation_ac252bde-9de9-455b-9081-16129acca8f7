///***
/// * 主体业绩 模型
/// */

import 'package:json_annotation/json_annotation.dart';

part 'SubjectPerformanceModel.g.dart';

@JsonSerializable(explicitToJson: true)
class SubjectPerformanceModel {

  SubjectPerformanceModel({
    this.active,
    this.agentCharge,
    this.caNoDataKey,
    this.consumerNumber,
    this.consumerPerson,
    this.consumerValuation,
    this.contractAmount,
    this.contractContents,
    this.contractDate,
    this.contractFinishDate,
    this.createdDate,
    this.currencyCode,
    this.dataKey,
    this.modifiedDate,
    this.monetaryUnit,
    this.performanceID,
    this.projectLeader,
    this.statusValue,
    this.subjectCode,
    this.subjectDataKey,
    this.subjectID,
    this.subjectType,
    this.tendereeCode,
    this.tendereeName,
    this.tenderingProjectName,
    this.tenderingProjectNo,
  });

  final bool active;

  final String agentCharge;

  final String caNoDataKey;

  final String consumerNumber;

  final String consumerPerson;

  final String consumerValuation;

  final String contractAmount;

  final String contractContents;

  final String contractDate;

  String contractFinishDate;

  final String createdDate;

  final String currencyCode;

  final String dataKey;

  final String modifiedDate;

  final String monetaryUnit;

  final int performanceID;

  final String projectLeader;

  final int statusValue;

  final String subjectCode;

  final String subjectDataKey;

  final int subjectID;

  final String subjectType;

  final String tendereeCode;

  final String tendereeName;

  final String tenderingProjectName;

  final String tenderingProjectNo;

  factory SubjectPerformanceModel.fromJson(Map<String, dynamic> json) => _$SubjectPerformanceModelFromJson(json);

  Map<String, dynamic> toJson() => _$SubjectPerformanceModelToJson(this);

}
