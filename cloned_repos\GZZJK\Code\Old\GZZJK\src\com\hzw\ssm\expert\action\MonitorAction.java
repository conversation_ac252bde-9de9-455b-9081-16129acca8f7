/**
 * 
 */
package com.hzw.ssm.expert.action;

import java.io.IOException;
import java.io.PrintWriter;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import org.apache.struts2.convention.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;

import com.google.gson.Gson;
import com.hzw.ssm.expert.entity.IllegalExtractioninfoEntity;
import com.hzw.ssm.expert.entity.MonitorEntity;
import com.hzw.ssm.expert.service.IllegalExtractioninfoService;
import com.hzw.ssm.fw.base.BaseAction;
import com.hzw.ssm.sys.user.entity.UserEntity;

/**
 * <AUTHOR>
 *
 */
@Namespace("/monitor")
@ParentPackage(value = "default")
@Results({ @Result(	name = "tomonitor",
					location = "/jsp/monitor/monitorList.jsp"),
	@Result(name = "projectmonitor",type ="redirect" ,location = "/project/initList")})
public class MonitorAction extends BaseAction {

	private IllegalExtractioninfoEntity entity;
	private List<IllegalExtractioninfoEntity> Illegallist;
	private String decimationbatch;
	private String isHandle;
	private String handleFile;
	// private MonitorEntity entity;
	@Autowired
	private IllegalExtractioninfoService monitorService;
	
	private String userName;
	@Action("queryDepartmentViolators")
	public String queryDepartmentViolators() {
		this.context();
		if (entity == null) {
			entity = new IllegalExtractioninfoEntity();
		}
		HttpSession session = getRequest().getSession();
		// 当前用户信息
		UserEntity user = (UserEntity) session.getAttribute("userInfo");
		try {
			//处长
			if("20150122103034522026".equals(user.getRole_id())) {
				entity.setDepartmentName(user.getDepartment());
				entity.setIllRoleId("0000");
				//entity.setIsHandle("10");
				if(null==entity.getIsHandle()) {
					entity.setIsHandle("10");
				}
			}
			//综合处
			if("20161227193940035005".equals(user.getRole_id())) {
				if(null==entity.getIsHandle()) {
					entity.setIsHandle("20");
				}
			}
			if("请选择".equals(entity.getIsHandle())) {
				entity.setIsHandle(null);
			}
			entity.setPage(this.getPage());
			if (null == entity.getExtractTimes()) {
				entity.setExtractTimes("3");
				
			}
			if (null == entity.getBeginTime() && null == entity.getEndTime()) {
				Date date = new Date();
				SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
				entity.setEndTime(format.parse(format.format(date)));

				Calendar calendar = Calendar.getInstance();
				calendar.setTime(date);
				calendar.add(Calendar.DAY_OF_MONTH, -30);
				String newDateTime = format.format(calendar.getTime());
				Date date1 = format.parse(newDateTime);
				entity.setBeginTime(date1);
			}
			Illegallist = monitorService.queryMonitor(entity);
			if("0000".equals(entity.getIllRoleId())) {
				entity.setDepartmentName(null);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		return "tomonitor";
	}

	@Action("examineDepartment")
	public String examineDepartment() {
		if (entity == null) {
			entity = new IllegalExtractioninfoEntity();
		}
		this.context();
		// 当前session
		HttpSession session = getRequest().getSession();
		// 当前用户信息
		UserEntity user = (UserEntity) session.getAttribute("userInfo");
		//处长
		if("20150122103034522026".equals(user.getRole_id())) {
			entity.setDepartmentName(user.getDepartment());
			entity.setIllRoleId("0000");
			if("1".equals(entity.getIsHandle())) {
				entity.setIsHandle("20");
			}
			if("3".equals(entity.getIsHandle())) {
				entity.setIsHandle("30");
			}
		}
		//综合处
		if("20161227193940035005".equals(user.getRole_id())) {
			if("1".equals(entity.getIsHandle())) {
				entity.setIsHandle("40");
			}
			if("3".equals(entity.getIsHandle())) {
				entity.setIsHandle("50");
			}
		}
		entity.setExamineName(user.getUser_name());
		entity.setExamineTime(new Date());
		Boolean flag = monitorService.modifyMonitor(entity);
		try {

			PrintWriter out = this.getResponse().getWriter();
			out.print(flag);
			out.close();
		} catch (IOException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return null;
	}

	@Action("examineDepartmentFile")
	public String examineDepartmentFile() throws IOException {
		if (entity == null) {
			entity = new IllegalExtractioninfoEntity();
		}
		this.context();
		// 当前session
		HttpSession session = getRequest().getSession();
		// 当前用户信息
		UserEntity user = (UserEntity) session.getAttribute("userInfo");
		
		entity.setDecimationbatch(decimationbatch);
		entity.setHandleFile(handleFile);
		entity.setExamineName(user.getUser_name());
		entity.setExamineTime(new Date());
		entity.setIsHandle("10");
		//处长
		if("20150122103034522026".equals(user.getRole_id())) {
			entity.setIsHandle("20");		
		}
		Boolean flag = monitorService.modifyMonitorill(entity);
		
		
		return "projectmonitor";
	}	
	
	
	/**
	 * 判断当前用户当前处室是否有未备案的信息
	 * 
	 * @return
	 */
	@Action("/checkExtractFrequenc")
	public String checkExtractFrequenc() {
		if (entity == null) {
			entity = new IllegalExtractioninfoEntity();
		}
		this.context();
		// 当前session
		HttpSession session = getRequest().getSession();
		// 当前用户信息
		UserEntity user = (UserEntity) session.getAttribute("userInfo");
		entity.setDepartmentName(user.getDepartment());
		List<MonitorEntity> monitor = monitorService.checkExtractFrequenc(entity);
		Gson gson1 = new Gson();
		String toString1 = gson1.toJson(monitor);
		try {
			HttpServletResponse respose = this.getResponse();
			respose.setHeader("Content-type", "text/html;charset=utf-8");
			respose.setCharacterEncoding("utf-8");
			PrintWriter out = respose.getWriter();
			out.print(toString1);
			out.close();
		} catch (IOException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return null;
	}

	/**
	 * 根据用户名称查询用户id
	 * @return
	 */
	@Action("/getUserNameToId")
	public String getUserNameToId() {
		this.context();
		UserEntity userentity = monitorService.getUserNameToId(userName);
		Gson gson1 = new Gson();
		String toString1 = gson1.toJson(userentity);
		try {
			HttpServletResponse respose = this.getResponse();
			respose.setHeader("Content-type", "text/html;charset=utf-8");
			respose.setCharacterEncoding("utf-8");
			PrintWriter out = respose.getWriter();
			out.print(toString1);
			out.close();
		} catch (IOException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return null;
	}
	public List<IllegalExtractioninfoEntity> getIllegallist() {
		return Illegallist;
	}

	public void setIllegallist(List<IllegalExtractioninfoEntity> illegallist) {
		Illegallist = illegallist;
	}

	public String getDecimationbatch() {
		return decimationbatch;
	}

	public void setDecimationbatch(String decimationbatch) {
		this.decimationbatch = decimationbatch;
	}

	public IllegalExtractioninfoEntity getEntity() {
		return entity;
	}

	public void setEntity(IllegalExtractioninfoEntity entity) {
		this.entity = entity;
	}

	public String getIsHandle() {
		return isHandle;
	}

	public void setIsHandle(String isHandle) {
		this.isHandle = isHandle;
	}

	public String getHandleFile() {
		return handleFile;
	}

	public void setHandleFile(String handleFile) {
		this.handleFile = handleFile;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	

	
}
