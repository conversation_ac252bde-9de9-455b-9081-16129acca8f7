package com.hzw.ssm.applets.filter;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.hzw.ssm.applets.exception.ExceptionEnum;
import com.hzw.ssm.applets.service.AppService;
import com.hzw.ssm.applets.utils.LztHttpRequest;
import com.hzw.ssm.applets.utils.ResultJson;
import com.hzw.ssm.applets.utils.SpringUtil;
import com.hzw.ssm.expert.entity.ExpertInfoEntity;
import com.hzw.ssm.fw.util.DateUtil;
import net.sf.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.PrintWriter;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2021-02-03 18:33
 * @Version 1.0
 */
public class AppletsFilter implements Filter {

    @Autowired
    private AppService appService;

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        if (appService == null) {
            appService = SpringUtil.getBean(AppService.class);
        }
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {

        HttpServletRequest httpServletRequest = (HttpServletRequest) request;
        HttpServletResponse httpServletResponse = (HttpServletResponse) response;
        JSONObject jsonReqData = getJsonReqData(httpServletRequest);

        // token无效
        try {
            String token = jsonReqData.getString("token");
            String expireTime = jsonReqData.getString("expireTime");
            // 时间过期
            Date date = new Date(System.currentTimeMillis());
            String currentTime = DateUtil.dateToString(date, "yyyy-MM-dd HH:mm:ss");
            int hourDifference = hourDifference(currentTime, expireTime);
            if (hourDifference > 0) {
                ResultJson resultJson = new ResultJson(ExceptionEnum.TIME_OUT.code, ExceptionEnum.TIME_OUT.message);
                getJsonRespData(httpServletResponse, resultJson);
                return;
            }

//            String str = MyMD5Util.AESdecrypt(token);
            ExpertInfoEntity expertInfoEntity = appService.queryExpertByToken(token);
            if (null == expertInfoEntity) {
                ResultJson resultJson = new ResultJson(ExceptionEnum.TOKEN_INVALID.code, ExceptionEnum.TOKEN_INVALID.message);
                getJsonRespData(httpServletResponse, resultJson);
                return;
            }
        } catch (Exception e) {
            ResultJson resultJson = new ResultJson(ExceptionEnum.DECRYPT_INVALID.code, ExceptionEnum.DECRYPT_INVALID.message);
            getJsonRespData(httpServletResponse, resultJson);
            return;
        }
        chain.doFilter(new LztHttpRequest(httpServletRequest, jsonReqData.toString()), response);
    }

    @Override
    public void destroy() {

    }

    public static int hourDifference(String beginTime, String endTime) {
        try {
            SimpleDateFormat simpleFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");//如2016-08-10 20:40
            Date bt = simpleFormat.parse(beginTime);
            //时间过期
            Date et = simpleFormat.parse(endTime);
            if (bt.before(et)) {
                //表示bt小于et
                return -1;
            } else {
                return 1;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return 0;
    }

    public static JSONObject getJsonReqData(HttpServletRequest httpServletRequest) {
        StringBuffer sb = new StringBuffer();
        try {
            String jsonStr = "";
            // 获取application/json格式数据，返回字符流
            BufferedReader reader = httpServletRequest.getReader();
            // 对字符流进行解析
            while ((jsonStr = reader.readLine()) != null) {
                sb.append(jsonStr);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        JSONObject jo = JSONObject.fromObject(sb.toString());
        return jo;
    }

    public static void getJsonRespData(HttpServletResponse response, Object resultJson) {
        try {
            //在对result进行json转换时，new Gson().toJson(result)，遇到null值不进行转换问题，网上搜了下，使用GsonBuilder创建Gson即可解决
            Gson gson = new GsonBuilder().serializeNulls().create();
            String toString = gson.toJson(resultJson);
            response.setContentType("text/html;charset=utf-8");
            PrintWriter out = response.getWriter();
            out.print(toString);
            out.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}
