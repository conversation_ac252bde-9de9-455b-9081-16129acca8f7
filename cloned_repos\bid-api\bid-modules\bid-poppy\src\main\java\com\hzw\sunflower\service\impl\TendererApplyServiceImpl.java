package com.hzw.sunflower.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzw.sunflower.constant.CommonConstants;
import com.hzw.sunflower.constant.constantenum.EntrustUserType;
import com.hzw.sunflower.constant.constantenum.TendererShowApplyInfoEnum;
import com.hzw.sunflower.controller.project.request.AgentMyProjectREQ;
import com.hzw.sunflower.controller.project.request.TendererShowApplyInfoReq;
import com.hzw.sunflower.dao.TendererApplyMapper;
import com.hzw.sunflower.datascope.utils.DataScopeUtil;
import com.hzw.sunflower.dto.MyProjectListDTO;
import com.hzw.sunflower.entity.TendererShowApplyInfo;
import com.hzw.sunflower.service.TendererShowApplyInfoService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class TendererApplyServiceImpl extends ServiceImpl<TendererApplyMapper, TendererShowApplyInfo> implements TendererShowApplyInfoService {
    @Override
    public Boolean batchSettingShow(TendererShowApplyInfoReq req) {
        Boolean flag = true;
        // 查询之前是否有设置过权限
        String idStr = req.getProjectList().stream().map(l -> Convert.toStr(l)).collect(Collectors.joining(","));
        QueryWrapper<TendererShowApplyInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(TendererShowApplyInfo::getUserId,req.getUserId())
                .in(TendererShowApplyInfo::getProjectId,idStr)
                .eq(TendererShowApplyInfo::getDataScope, TendererShowApplyInfoEnum.CHOOSE_PROJECT.getType())
                .or(l->l.eq(TendererShowApplyInfo::getDataScope,TendererShowApplyInfoEnum.ALL_PROJECT.getType()));
        List<TendererShowApplyInfo> list = this.list(queryWrapper);
        if(CollectionUtil.isNotEmpty(list)){
            flag = this.removeBatchByIds(list);
        }
        if(flag){
            List<TendererShowApplyInfo> infoList = new ArrayList<>();
            if(TendererShowApplyInfoEnum.ALL_PROJECT.getType().equals(req.getDataScope())){
                TendererShowApplyInfo tendererShowApplyInfo = new TendererShowApplyInfo();
                tendererShowApplyInfo.setDataScope(req.getDataScope());
                tendererShowApplyInfo.setUserId(req.getUserId());
                tendererShowApplyInfo.setRightCode(req.getRightCode());
                infoList.add(tendererShowApplyInfo);
            } else {
                for (Long id:req.getProjectList()) {
                    TendererShowApplyInfo tendererShowApplyInfo = new TendererShowApplyInfo();
                    tendererShowApplyInfo.setProjectId(id);
                    tendererShowApplyInfo.setDataScope(req.getDataScope());
                    tendererShowApplyInfo.setUserId(req.getUserId());
                    tendererShowApplyInfo.setRightCode(req.getRightCode());
                    infoList.add(tendererShowApplyInfo);
                }
            }
            flag = this.saveBatch(infoList);
        }
        return flag;
    }

    @Override
    public List<MyProjectListDTO> queryOnlyMyProjectPageListByUserId(AgentMyProjectREQ condition) {
        condition.setType(EntrustUserType.PRINCIPAL.getType());
        List<MyProjectListDTO> dtoList = this.baseMapper.queryListOnlyMyProjectByPage(condition);
        return dtoList;
    }

    @Override
    public Boolean chooseShowOrNot(Long userId, String projectId) {
        Boolean b = false;
        // 是否设置过权限
        QueryWrapper<TendererShowApplyInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(TendererShowApplyInfo::getUserId,userId);
        List<TendererShowApplyInfo> list = this.list(queryWrapper);
        if(CollectionUtil.isNotEmpty(list)){
            // 是否有查看全部项目权限的设置
            queryWrapper.lambda().eq(TendererShowApplyInfo::getDataScope,TendererShowApplyInfoEnum.ALL_PROJECT.getType());
            TendererShowApplyInfo one = this.getOne(queryWrapper);
            if(BeanUtil.isNotEmpty(one)){
                if(CommonConstants.YES.equals(one.getRightCode())){
                    b = true;
                }
            } else {
                // 当前项目是否设置过查看权限
                QueryWrapper<TendererShowApplyInfo> infoQueryWrapper = new QueryWrapper<>();
                infoQueryWrapper.lambda().eq(TendererShowApplyInfo::getUserId,userId)
                        .eq(TendererShowApplyInfo::getDataScope,TendererShowApplyInfoEnum.CHOOSE_PROJECT.getType())
                        .eq(TendererShowApplyInfo::getProjectId,projectId);
                TendererShowApplyInfo one1 = this.getOne(infoQueryWrapper);
                if((BeanUtil.isNotEmpty(one1) && CommonConstants.YES.equals(one1.getRightCode()))){
                    b = true;
                }
            }
        }
        return b;
    }

    @Override
    public List<MyProjectListDTO> listMyProject(AgentMyProjectREQ condition) {
        condition.setType(EntrustUserType.PRINCIPAL.getType());
        // 处理数据权限
        String datascopesql= DataScopeUtil.getProjectDataScopeFilterSql("rud","ptp","p");
        condition.setDatascope(datascopesql);
        List<MyProjectListDTO> dtoList = this.baseMapper.listMyProject(condition);
        return dtoList;
    }
}
