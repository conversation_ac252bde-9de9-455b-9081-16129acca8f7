package com.hzw.sunflower.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hzw.sunflower.controller.request.BondSectionReq;
import com.hzw.sunflower.entity.BondRetainRecord;
import com.hzw.sunflower.entity.BondSplit;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 保证金保留记录 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-15
 */
public interface BondRetainRecordService extends IService<BondRetainRecord> {

    /**
     * 查询所有分批次退还记录的利息总和
     * @param refundId
     * @return
     */
    BigDecimal getCountRateByRefundId(Long refundId);
}
