package com.hzw.sunflower.dto;

import com.hzw.sunflower.entity.*;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 新增公告DTO
 *
 * <AUTHOR>
 * @version 1.0.0 2021/07/07
 */
@Data
@Accessors(chain = true)
public class InsertNoticeDTO {

    /**
     * 公告基本信息
     */
    private ProjectBidNotice notice;

    /**
     * 关联附件
     */
    private List<RelevancyAnnex> annexes;

    /**
     * 公告关联包段
     */
    private List<NoticePackageR> packageRS;

    /**
     * 所有使用到的媒体
     */
    private List<NoticeMediaR> mediaRS;

}
