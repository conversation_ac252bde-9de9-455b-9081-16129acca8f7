package com.hzw.ssm.sys.project.action;

import java.io.IOException;
import java.io.PrintWriter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpSession;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import net.sf.json.JsonConfig;
import net.sf.json.util.PropertyFilter;

import org.apache.struts2.convention.annotation.Action;
import org.apache.struts2.convention.annotation.Namespace;
import org.apache.struts2.convention.annotation.ParentPackage;
import org.apache.struts2.convention.annotation.Result;
import org.apache.struts2.convention.annotation.Results;
import org.springframework.beans.factory.annotation.Autowired;

import com.hzw.ssm.expert.entity.ExpertInfoEntity;
import com.hzw.ssm.expert.entity.SpecialtyInfoEntity;
import com.hzw.ssm.expert.service.ExpertInfoService;
import com.hzw.ssm.fw.base.BaseAction;
import com.hzw.ssm.fw.util.CommUtil;
import com.hzw.ssm.fw.util.DateUtil;
import com.hzw.ssm.fw.util.HZWException;
import com.hzw.ssm.fw.util.MessageConstants;
import com.hzw.ssm.fw.util.SysConstants;
import com.hzw.ssm.sys.project.entity.ApplyRecordEntity;
import com.hzw.ssm.sys.project.entity.ConditionEntity;
import com.hzw.ssm.sys.project.entity.ExtractDebarbEntity;
import com.hzw.ssm.sys.project.entity.ProjectAuditEntity;
import com.hzw.ssm.sys.project.entity.ProjectEntity;
import com.hzw.ssm.sys.project.entity.ResultEntity;
import com.hzw.ssm.sys.project.service.DebarbService;
import com.hzw.ssm.sys.project.service.ProjectService;
import com.hzw.ssm.sys.template.entity.TemplateEntity;
import com.hzw.ssm.sys.template.service.TemplateService;
import com.hzw.ssm.sys.user.entity.UserEntity;

/**
 * 待抽取项目
 * 
 * <AUTHOR>
 * 
 */
@Namespace("/waitProject")
@ParentPackage(value = "default")
@Results({ @Result(name = "toProjectWaitList", location = "/jsp/project/projectWaitList.jsp"),
		@Result(name = "toErrorProjectWaitList", location = "/jsp/project/projectErrorWaitList.jsp"),
		@Result(name = "extract", location = "/jsp/project/extractExperts.jsp"),
		@Result(name = "fillExtract", location = "/jsp/project/fillExtractExperts.jsp"),
		@Result(name = "condition", location = "/jsp/project/showExtractInfo.jsp"),
		@Result(name = "majorStr", location = "/jsp/project/majorList.jsp"),
		@Result(name = "smsExtract", location = "/jsp/project/smsExtract.jsp"),
		@Result(name = "showExtract", location = "/jsp/project/showExtractInfo.jsp"),
		@Result(name = "fillPumpExtract", location = "/jsp/project/fillPumpExtractInfo.jsp"),
		@Result(name = "init", location = "/jsp/project/addFactoryProject.jsp"),
		@Result(name = "apply", location = "/jsp/project/applyPointToExperts.jsp"),
		@Result(name = "toWaitList", location = "/waitProject/queryProjectList"),
		@Result(name = "pointTo", location = "/jsp/project/pointToExperts.jsp"),
		@Result(name = "toProjectDetail", location = "/jsp/project/projectWaitDetail.jsp"),
		@Result(name = "projectAuditReason", location = "/jsp/project/projectAuditReason.jsp"),
		@Result(name = "projectAuditReasonList", location = "/jsp/project/projectAuditReasonList.jsp") })
public class ProjectWaitAction extends BaseAction {

	private static final long serialVersionUID = 1L;

	@Autowired
	private ProjectService projectService;

	private ProjectEntity projectEntity;

	private ProjectAuditEntity projectAuditEntity;

	/** 抽取条件 */
	private ConditionEntity conditionEntity;

	private List<ProjectEntity> projectList;

	private List<ProjectAuditEntity> projectAuditList;

	private List<ConditionEntity> conditionList;
	/** 抽取条件 */
	private ConditionEntity conEntity;
	private ProjectEntity project;
	private List<ResultEntity> resultList;// 专家抽取结果列表
	private List<List<ResultEntity>> extractList;// 不同轮次抽取结果集
	
	private List<TemplateEntity> templateEntityList;//短信通知模板
	private List<TemplateEntity> templateSmsList;//短信通知模板
	private List<ExpertInfoEntity> expertList;// 专家信息列表
	List<UserEntity> userList;
	private List<UserEntity> operatorList;// 经办人列表
	private Long agree;// 同意参加的人数
	private String isSave;
	private String majorStr;// 专业选择
	private String applyFlag;// 申请抽取标识
	private ResultEntity result;
	private ApplyRecordEntity apply;
	private String isFill; // 抽取标识

	
	private ExtractDebarbEntity extractDebarbEntity;
	private List<ExtractDebarbEntity> companyDebarbList;//回避机构
	private List<ExtractDebarbEntity> extractDebarbList;//回避专家
		
	@Autowired
	private ExpertInfoService expertInfoService;
	
	
	@Autowired
	private TemplateService templateService;
	
	@Autowired
	private DebarbService debarbService;

	/**
	 * 查询待抽取项目列表
	 * 
	 * @return
	 */
	@Action("queryProjectList")
	public String queryProjectList() {
		this.context();
		// 当前session
		HttpSession session = getRequest().getSession();
		// 当前用户信息
		UserEntity user = (UserEntity) session.getAttribute("userInfo");
		try {
			if (null == projectEntity)
				projectEntity = new ProjectEntity();

			projectEntity.setPage(this.getPage());
			projectEntity.setStas(SysConstants.PROJECT_STATUS.WAITLIST);
			projectEntity.setCreateUser(user.getUser_id());
			projectList = projectService.queryPageProjectList(projectEntity, user);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return "toProjectWaitList";
	}

	@Action("queryErrorProjectList")
	public String queryErrorProjectList() {
		this.context();
		// 当前session
		HttpSession session = getRequest().getSession();
		// 当前用户信息
		UserEntity user = (UserEntity) session.getAttribute("userInfo");
		try {
			if (null == projectEntity)
				projectEntity = new ProjectEntity();

			projectEntity.setPage(this.getPage());
			projectEntity.setStas(SysConstants.PROJECT_STATUS.WAITLIST);
			projectEntity.setCreateUser(user.getUser_id());
			projectList = projectService.queryPageByErrorProjectList(projectEntity, user);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return "toErrorProjectWaitList";
	}

	/**
	 * 查询项目明细
	 * 
	 * @return
	 */
	@Action("toProjectDetail")
	public String toProjectDetail() {
		Long method = projectEntity.getMethod();
		projectList = projectService.queryProjectListById(projectEntity);
		projectEntity = projectList.get(0);
		// 判断是否为申请指定专家页面
		if (null != method && -1L == method) {
			project = projectEntity;
			return "apply";
		}

		if (null == conditionEntity)
			conditionEntity = new ConditionEntity();

		conditionEntity.setProjectId(projectList.get(0).getProjectId());
		conditionList = projectService.queryConditionList(conditionEntity);
		return "toProjectDetail";

	}

	/**
	 * 展示修改项目信息页面
	 * 
	 * @return
	 */
	@Action("showModifyProject")
	public String showModifyProject() {
		project = projectService.queryProjectById(project);
		if (project != null) {
			project.setManagerName(project.getManager());
			project.setOperatorName(project.getOperator());
		}
		if (null == conEntity) {
			conEntity = new ConditionEntity();
		}
		conEntity.setProjectId(project.getProjectId());
		conEntity = projectService.queryConditionById(conEntity);
		isSave = "1";// 更新操作
		return this.addFactoryProject();
	}

	/**
	 * 增加工厂项目
	 * 
	 * @return
	 */
	@Action("addFactoryProject")
	public String addFactoryProject() {
		// 查询项目负责人
		UserEntity user = new UserEntity();
		userList = projectService.queryProjectManager(user);
		// 查询项目经办人
		/*
		 * user.setRole_name(SysConstants.ROLE_NAME.OPERATOR); operatorList =
		 * projectService.queryUserByRole(user);
		 */
		return "init";
	}

	@Action("queryJSGZProject")
	public String queryJSGZProject() {
		this.context();
		this.getResponse().setCharacterEncoding("utf-8");
		PrintWriter out = null;
		if (null == project) {
			project = new ProjectEntity();
		}
		try {
			// 当前session
			HttpSession session = getRequest().getSession();
			// 当前用户信息
			UserEntity user = (UserEntity) session.getAttribute("userInfo");
			//判断当前登录用户的角色(过滤机电中心抽取人)
			if(!(SysConstants.ROLE_NAME.CHOOSER.equals(user.getRole_name())
					||SysConstants.ROLE_NAME.ADMIN.equals(user.getRole_name()))){
				project.setDepartment(user.getDepartment());
			}
			out = this.getResponse().getWriter();
			// project.setProjectNo(this.getRequest().getParameter("term"));
			project.setPage(this.getPage());
			projectList = projectService.queryPageJSGZProjectList(project);
			JSONArray jsonArray = JSONArray.fromObject(projectList);
			out.print(jsonArray);
		} catch (IOException e) {
			out.print("error");
		}finally {
			out.close();
		}
		return null;
	}

	/**
	 * 保存项目信息
	 * 
	 * @return
	 */
	@Action("saveProject")
	public String saveProject() {
		String returnStr = "init";
		try {
			this.setOpaUserAndDate(project);
			this.setOpaUserAndDate(conEntity);
			projectService.saveProject(project, conEntity);
			if ("apply".equals(applyFlag)) {// 申请抽取
				conEntity.setProjectId(project.getProjectId());
				returnStr = this.extractExperts();
			} else {
				this.setAlertMessage(MessageConstants.SAVE_SUCCESS);
			}
			isSave = "1";
		} catch (Exception e) {
			e.printStackTrace();
			if (!"apply".equals(applyFlag)) {
				this.setAlertMessage(MessageConstants.SAVE_FAILED);
			}
		}
		return queryProjectList();
	}

	/**
	 * 更新项目信息
	 * 
	 * @return
	 */
	@Action("updateProject")
	public String updateProject() {
		String returnStr = "init";
		try {
			this.setOpaUserAndDate(project);
			this.setOpaUserAndDate(conEntity);
			List<ProjectEntity> projectList = new ArrayList<ProjectEntity>();
			projectList.add(project);
			projectService.updateProjects(projectList, conEntity);
			if ("apply".equals(applyFlag)) {// 申请抽取
				conEntity.setProjectId(project.getProjectId());
				returnStr = this.extractExperts();
			} else {
				this.setAlertMessage(MessageConstants.SAVE_SUCCESS);
			}
		} catch (Exception e) {
			e.printStackTrace();
			if (!"apply".equals(applyFlag)) {
				this.setAlertMessage(MessageConstants.SAVE_FAILED);
			}
		}
		isSave = "1";
		return returnStr;
	}

	/**
	 * 跳转抽取专家页面
	 * 
	 * @return
	 */
	@Action("extractExperts")
	public String extractExperts() {
		String returnStr = "extract";
		project = new ProjectEntity();
		project.setDecimationBatch(conEntity.getDecimationBatch());
		// 根据批次号查询项目信息
		projectList = projectService.queryProjectListById(project);
		// 更新抽取条件
		for (ProjectEntity newConEntity : projectList) {
			if (conEntity != null && conEntity.getId() != null && !"".equals(conEntity.getId())) {// 已保存过，进行更新操作
				conEntity.setId(newConEntity.getConditionId());
				projectService.updateCondition(conEntity);
			} else {
				projectService.saveCondition(conEntity);
			}
		}
		if (null != resultList && !resultList.isEmpty()) {
			extractList = CommUtil.mergeList(resultList);// 按轮次分隔结果集
			agree = new Long(0L);
			for (ResultEntity e : resultList) {
				if (e.getJoinStatus().intValue() == 0) {// 已参加
					agree++;
				}
			}
		} else {
			try {
				ConditionEntity countConEntity = conEntity;
				// 查询多少专家已经确定参标
				agree = new Long(0L);
				countConEntity.setJoin_status(0L);
				Integer qCount = projectService.countNotExtractResult(countConEntity);
				agree = qCount.longValue();

				// 进行抽取之前判断当前项目是否是第一次抽取或者上一次抽取还有没确定所有专家是否到场
				countConEntity.setJoin_status(2L);
				Integer count = projectService.countNotExtractResult(countConEntity);
				if (count > 0) {
					throw new HZWException("还有部分专家没有通知！请等待全部通知完再继续抽取！");
				}
				
				//新增功能，判断当前抽取时间是否是开标时间半小时内，如果是半小时，将项目改成紧急抽取
				String strType =projectService.checkBidTime(projectList,conEntity);
				
				
				/*
				 * for(ProjectEntity entity : projectList){ ProjectEntity
				 * newProject = new ProjectEntity(); newProject = entity;
				 * 
				 * //未产生抽取结果,仍作为第一次 String tender = ""; if(null !=
				 * newProject.getTender() && !"".equals(newProject.getTender())
				 * && newProject.getTender().indexOf(",") > -1){ String[]
				 * tenderSplit = newProject.getTender().split(","); for (int i =
				 * 0; i < tenderSplit.length; i++) { tender
				 * +="'"+tenderSplit[i]+"',"; } tender =
				 * tender.substring(0,tender.length()-1); }else{ tender =
				 * "'"+newProject.getTender()+"'"; }
				 * conEntity.setTender(tender);
				 * 
				 * expertList=projectService.queryExpertsByRule(conEntity,
				 * newProject); if(expertList.size()<conEntity.getTotal() *3) {
				 * throw new
				 * HZWException(MessageConstants.NOT_ENOUGH_EXPERT+"系统库缺少"+(
				 * conEntity.getTotal().intValue() *3-expertList.size())+"人"); }
				 * 
				 * }
				 */
				String tender = "";

				for (ProjectEntity entity : projectList) {
					ProjectEntity newProject = new ProjectEntity();
					newProject = entity;
					// 未产生抽取结果,仍作为第一次
					if (null != newProject.getTender() && !"".equals(newProject.getTender())
							&& newProject.getTender().indexOf(",") > -1) {
						String[] tenderSplit = newProject.getTender().split(",");
						for (int i = 0; i < tenderSplit.length; i++) {
							tender += "'" + tenderSplit[i] + "',";
						}

					} else {
						tender += "'" + newProject.getTender() + "',";
					}
					conEntity.setBidTimeDay(DateUtil.dateToString(entity.getBidTime()));
				}
				tender = tender.substring(0, tender.length() - 1);
				conEntity.setTender(tender);
				
				if(companyDebarbList!=null &&companyDebarbList.size()>0){
					conEntity.setCompanyDebarbList(companyDebarbList);
				}
				if(extractDebarbList!=null &&extractDebarbList.size()>0){
					conEntity.setExtractDebarbList(extractDebarbList);
				}
				expertList = projectService.queryExpertsToExtraction(conEntity, projectList);
				// 现有同意参加的专家数量
				/*Integer gCount = projectService.queryAgreeExpertCount(conEntity);
				if (expertList.size() < (conEntity.getTotal() - gCount) * SysConstants.EXPERT_EXTRACTION_MULTIPLE) {
					throw new HZWException(MessageConstants.NOT_ENOUGH_EXPERT + "系统库缺少"
							+ ((conEntity.getTotal().intValue() - gCount) * 3 - expertList.size()) + "人");
				}
				 */
			} catch (Exception e) {
				if (e instanceof HZWException) {
					this.setAlertMessage(e.getMessage());
				} else
					e.printStackTrace();
			}
			if (null != isFill && "0".equals(isFill)) {
				returnStr = "fillExtract";
			}
		}

		// SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		// conEntity.setSmsStr("您好：XXX，江苏省招标中心有限公司诚邀您参加【"+project.getProjectName()+"】评标，开标时间："+sdf.format(project.getBidTime())+"。回复：1-参加，2-不参加。");
		// conEntity.setSmsStr("您好：XXX，江苏省招标中心有限公司诚邀您参加评标，开标时间："+sdf.format(project.getBidTime())+"，开标地点："+project.getBidAddress()+"，项目负责人："+project.getManager()+"，电话："+project.getPhone()+"。回复：1-参加，2-不参加。");

		return returnStr;
	}

	/**
	 * 跳转抽取专家页面[语音]
	 * 
	 * @return
	 */
	@Action("extractExpertsToVoice")
	public String extractExpertsToVoice() {
		String returnStr = "extract";
		project = new ProjectEntity();
		project.setDecimationBatch(conEntity.getDecimationBatch());
		// 根据批次号查询项目信息
		projectList = projectService.queryProjectListById(project);
		// 更新抽取条件
		for (ProjectEntity newConEntity : projectList) {
			if (conEntity != null && conEntity.getId() != null && !"".equals(conEntity.getId())) {// 已保存过，进行更新操作
				conEntity.setId(newConEntity.getConditionId());
				projectService.updateCondition(conEntity);
			} else {
				projectService.saveCondition(conEntity);
			}
		}
		if (null != resultList && !resultList.isEmpty()) {
			extractList = CommUtil.mergeList(resultList);// 按轮次分隔结果集
			agree = new Long(0L);
			for (ResultEntity e : resultList) {
				if (e.getJoinStatus().intValue() == 0) {// 已参加
					agree++;
				}
			}
		} else {
			try {
				ConditionEntity countConEntity = conEntity;
				// 查询多少专家已经确定参标
				agree = new Long(0L);
				countConEntity.setJoin_status(0L);
				Integer qCount = projectService.countNotExtractResult(countConEntity);
				agree = qCount.longValue();

				// 进行抽取之前判断当前项目是否是第一次抽取或者上一次抽取还有没确定所有专家是否到场
				countConEntity.setJoin_status(2L);
				Integer count = projectService.countNotExtractResult(countConEntity);
				if (count > 0) {
					throw new HZWException("还有部分专家没有通知！请等待全部通知完再继续抽取！");
				}
				
				//新增功能，判断当前抽取时间是否是开标时间半小时内，如果是半小时，将项目改成紧急抽取
				String strType =projectService.checkBidTime(projectList,conEntity);
				
				
				/*
				 * for(ProjectEntity entity : projectList){ ProjectEntity
				 * newProject = new ProjectEntity(); newProject = entity;
				 * 
				 * //未产生抽取结果,仍作为第一次 String tender = ""; if(null !=
				 * newProject.getTender() && !"".equals(newProject.getTender())
				 * && newProject.getTender().indexOf(",") > -1){ String[]
				 * tenderSplit = newProject.getTender().split(","); for (int i =
				 * 0; i < tenderSplit.length; i++) { tender
				 * +="'"+tenderSplit[i]+"',"; } tender =
				 * tender.substring(0,tender.length()-1); }else{ tender =
				 * "'"+newProject.getTender()+"'"; }
				 * conEntity.setTender(tender);
				 * 
				 * expertList=projectService.queryExpertsByRule(conEntity,
				 * newProject); if(expertList.size()<conEntity.getTotal() *3) {
				 * throw new
				 * HZWException(MessageConstants.NOT_ENOUGH_EXPERT+"系统库缺少"+(
				 * conEntity.getTotal().intValue() *3-expertList.size())+"人"); }
				 * 
				 * }
				 */
				String tender = "";

				for (ProjectEntity entity : projectList) {
					ProjectEntity newProject = new ProjectEntity();
					newProject = entity;
					// 未产生抽取结果,仍作为第一次
					if (null != newProject.getTender() && !"".equals(newProject.getTender())
							&& newProject.getTender().indexOf(",") > -1) {
						String[] tenderSplit = newProject.getTender().split(",");
						for (int i = 0; i < tenderSplit.length; i++) {
							tender += "'" + tenderSplit[i] + "',";
						}

					} else {
						tender += "'" + newProject.getTender() + "',";
					}
					conEntity.setBidTimeDay(DateUtil.dateToString(entity.getBidTime()));
				}
				tender = tender.substring(0, tender.length() - 1);
				conEntity.setTender(tender);
				expertList = projectService.queryExpertsToExtraction(conEntity, projectList);
				// 现有同意参加的专家数量
				/*Integer gCount = projectService.queryAgreeExpertCount(conEntity);
				if (expertList.size() < (conEntity.getTotal() - gCount) * SysConstants.EXPERT_EXTRACTION_MULTIPLE) {
					throw new HZWException(MessageConstants.NOT_ENOUGH_EXPERT + "系统库缺少"
							+ ((conEntity.getTotal().intValue() - gCount) * 3 - expertList.size()) + "人");
				}
				 */
			} catch (Exception e) {
				if (e instanceof HZWException) {
					this.setAlertMessage(e.getMessage());
				} else
					e.printStackTrace();
			}
			if (null != isFill && "0".equals(isFill)) {
				returnStr = "fillExtract";
			}
		}

		// SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		// conEntity.setSmsStr("您好：XXX，江苏省招标中心有限公司诚邀您参加【"+project.getProjectName()+"】评标，开标时间："+sdf.format(project.getBidTime())+"。回复：1-参加，2-不参加。");
		// conEntity.setSmsStr("您好：XXX，江苏省招标中心有限公司诚邀您参加评标，开标时间："+sdf.format(project.getBidTime())+"，开标地点："+project.getBidAddress()+"，项目负责人："+project.getManager()+"，电话："+project.getPhone()+"。回复：1-参加，2-不参加。");

		return returnStr;
	}
	/**
	 * 向专家发送邀请短信
	 * 
	 * @return
	 */
	@Action("smsExperts")
	public String smsExperts() {
		this.context();
		this.getResponse().setContentType("text/html;charset=UTF-8");
		try {
			if (null == conEntity) {
				this.getResponse().getWriter().write("fail");
				return null;
			}
			projectService.sendMessageForExperts(conEntity, expertList);
			this.getResponse().getWriter().write("success");
		} catch (Exception e) {
			if (e instanceof HZWException) {
				this.setAlertMessage(e.getMessage());
			}
			e.printStackTrace();
			try {
				this.getResponse().getWriter().write("fail");
			} catch (IOException e1) {
				e1.printStackTrace();
			}
		}
		return null;
	}

	/**
	 * 回退到抽取条件设置页面
	 * 
	 * @return
	 */
	@Action("backCondition")
	public String backCondition() {
		// Long num=conEntity.getNum();
		conEntity = projectService.queryConditionById(conEntity);
		// conEntity.setNum(num);
		// isSave="1";//抽取条件已保存过
		if (project == null) {
			project = new ProjectEntity();
		}
		project.setProjectId(conEntity.getProjectId());
		projectList = projectService.queryProjectListById(project);
		project = projectList.get(0);
		projectList = projectService.queryProjectListById(project);
		project = projectList.get(0);
		return "condition";
	}

	/**
	 * 再次抽取专家
	 * 
	 * @return
	 */
	@Action("againExtract")
	public String againExtract() {
		this.context();
		// 当前session
		HttpSession session = getRequest().getSession();
		// 当前用户信息
		UserEntity user = (UserEntity) session.getAttribute("userInfo");

		this.getResponse().setContentType("text/html;charset=UTF-8");
		try {
			Integer order = conEntity.getOrder();
			conEntity = projectService.queryConditionById(conEntity);// 重新获得抽取条件的信息信息
			conEntity.setOrder(order);
			project = new ProjectEntity();
			project.setProjectId(conEntity.getProjectId());
			project = projectService.queryProjectById(project);
			//通过项目的批次号查询项目
			List<ProjectEntity> pList = projectService.queryProjectListById(project);
			expertList = projectService.queryExpertsToExtraction(conEntity, pList);
			this.getResponse().getWriter().write(
					CommUtil.appendTable(expertList, conEntity, project.getCreate_id().equals(user.getUser_id())));
			/*
			 * if("1".equals(conEntity.getMethod())){//人工通知 }
			 * if("2".equals(conEntity.getMethod())){//短信
			 * projectService.sendMessageForExperts(conEntity, expertList);
			 * this.getResponse().getWriter().write(
			 * "<span style=\"font-size:18px\"><font color=\"#FF0000\">专家抽取完成。</font>\n<br/>"
			 * + "\n<span>抽取的专家结果详情，请在已抽取项目中查看。</span>\n</span>"); }
			 */
		} catch (Exception e) {
			if (e instanceof HZWException) {
				try {
					Integer expertCount = 0;
					JSONObject jason = null;
					// 地方级专家人数不足
					if (MessageConstants.NOT_ENOUGH_PLACE_EXPERT.equals(e.getMessage())) {
						// this.getResponse().getWriter().write("1");
						expertCount = projectService.queryExpertCountByRule(conEntity, 1);
						Map map = new HashMap();
						map.put("state", 1);
						map.put("expertCount", expertCount);
						jason = JSONObject.fromObject(map);
						this.getResponse().getWriter().write(jason.toString());
					}
					// 国家级专家人数不足
					else if (MessageConstants.NOT_ENOUGH_SENIOR_EXPERT.equals(e.getMessage())) {
						// this.getResponse().getWriter().write("2");
						expertCount = projectService.queryExpertCountByRule(conEntity, 2);
						Map map = new HashMap();
						map.put("state", 2);
						map.put("expertCount", expertCount);
						jason = JSONObject.fromObject(map);
						this.getResponse().getWriter().write(jason.toString());
					}
					// 专家总数不足
					else {
						// this.getResponse().getWriter().write("0");
						expertCount = projectService.queryExpertCountByRule(conEntity, 0);
						Map map = new HashMap();
						map.put("state", 0);
						map.put("expertCount", expertCount);
						jason = JSONObject.fromObject(map);
						this.getResponse().getWriter().write(jason.toString());
					}
				} catch (IOException e1) {
					e1.printStackTrace();
				}
			} else
				e.printStackTrace();
		}
		return null;
	}

	/**
	 * 再次抽取专家
	 * 
	 * @return
	 */
	@Action("validateExpertExperts")
	public String validateExpertExperts() {
		this.context();
		this.getResponse().setContentType("text/html;charset=UTF-8");
		Integer count = projectService.queryAgreeExpertCount(conEntity);
		StringBuffer html = new StringBuffer();
		if (count > conEntity.getExtractNum().intValue()) {
			html.append("您已抽取 " + count + " 位");
			if (null != conEntity.getGrade()) {
				if (1 == conEntity.getGrade()) {
					html.append("地方级");
				} else if (2 == conEntity.getGrade()) {
					html.append("国家级");
				}
			}
			html.append("专家，请重新输入");
		}
		StringBuffer result = new StringBuffer();
		result.append("[{\"message\":\"").append(html.toString()).append("\", \"count\":\"").append(count)
				.append("\"}]");
		try {
			this.getResponse().getWriter().write(result.toString());
		} catch (IOException e) {
			e.printStackTrace();
		}
		return null;
	}

	/**
	 * 更新已抽取的专家
	 * 
	 * @return
	 */
	@Action("updateExtractedExpert")
	public String saveExtractedExpert() {
		projectService.updateExtractedExpert(result);
		// 验证项目指定人数是否满足条件，是的话则自动更新该项目为已抽取
		if (0L == result.getJoinStatus()) {
			conditionEntity = new ConditionEntity();
			conditionEntity.setId(result.getConditionId());
			projectService.autoUpdateProStatus(conditionEntity);
		}
		return "init";
	}

	/**
	 * 更新项目抽取状态
	 * 
	 * @return
	 */
	@Action("saveExtractResult")
	public String saveExtractResult() {
		projectService.saveExtractResult(conEntity);
		return null;
	}

	/**
	 * 抽取专家(待抽取项目)
	 * 
	 * @return
	 */
	@Action("toExtractExperts")
	public String toExtractExperts() {
		projectList = projectService.queryProjectListById(project);
		project = projectList.get(0);
		if (null == conEntity) {
			conEntity = new ConditionEntity();
		}
		try {
			conEntity.setProjectId(project.getProjectId());
			List<ConditionEntity> conditionList = projectService.queryConditionList(conEntity);
			conEntity = conditionList != null && conditionList.size() > 0 ? conditionList.get(0) : conEntity;
			
			//判断当前项目是否是非人工通知
			if(conEntity.getMethod()!='1') {
				if(conEntity.getMethod()==SysConstants.CONDITION_METHOD.METHOD_THREE){
					//默认查询“系统自动语音抽取”模板,且有效的模板
					TemplateEntity entity =  new TemplateEntity();
					entity.setTemplateType(String.valueOf(SysConstants.CONDITION_METHOD.METHOD_THREE));
					entity.setIsValid(SysConstants.IS_VALID.VALID_ZERO);
					//非人工通知根据抽取方式查询模板信息
					templateEntityList = templateService.queryTemplateList(entity);
					
					//默认查询“语音短信”模板，且有效的模板
					entity.setTemplateType(String.valueOf(SysConstants.CONDITION_METHOD.METHOD_TWO));
					//非人工通知根据抽取方式查询模板信息
					templateSmsList = templateService.queryTemplateList(entity);
				}else{
					TemplateEntity entity =  new TemplateEntity();
					entity.setTemplateType(conEntity.getMethod().toString());
					//非人工通知根据抽取方式查询模板信息
					templateEntityList = templateService.queryTemplateList(entity);
				}
				
				companyDebarbList =  new ArrayList<ExtractDebarbEntity>();
				extractDebarbList = new ArrayList<ExtractDebarbEntity>();
				//根据批次号查询回避信息
				ExtractDebarbEntity debarbEntity = new ExtractDebarbEntity();
				debarbEntity.setDecimationbatch(project.getDecimationBatch());
				List<ExtractDebarbEntity> debarbList = debarbService.queryExtractDebarbList(debarbEntity);
				if(debarbList!=null&&debarbList.size()>0){
					for(ExtractDebarbEntity debarb:debarbList){
						if(SysConstants.DEBARB_TYPE.TYPE_1.equals(debarb.getDebarbType())){
							debarb.setCompanyDebarbReason(debarb.getDebarbReason());
							companyDebarbList.add(debarb);
						}else {
							debarb.setExpertDebarbReason(debarb.getDebarbReason());
							extractDebarbList.add(debarb);
						}
					}
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		if (null != isFill && "0".endsWith(isFill)) {
			return "fillPumpExtract";
		}
		return "showExtract";
	}

	/**
	 * 抽取人修改抽取条件
	 * 
	 * @return
	 */
	@Action("updateExtractInfo")
	public String updateExtractInfo() {
		try {
			projectService.updateCondition(conEntity);
			if (project == null) {
				project = new ProjectEntity();
			}
			project.setProjectId(conEntity.getProjectId());
			project = projectService.queryProjectById(project);
			
			
			List<ExtractDebarbEntity> extractDebarbEntityList = disposeDebarbData();
			// 将原有的回避信息删除
			ExtractDebarbEntity extractDebarbEntity = new ExtractDebarbEntity();
			extractDebarbEntity.setDecimationbatch(project.getDecimationBatch());
			debarbService.deleteDebarbData(extractDebarbEntity);

			// 添加新的回避信息
			if (extractDebarbEntityList != null && extractDebarbEntityList.size() > 0) {
				// 保存回避条件
				for (ExtractDebarbEntity debarbEntity : extractDebarbEntityList) {
					debarbEntity.setDecimationbatch(project.getDecimationBatch());
					debarbEntity.setId(CommUtil.getKey());
					debarbService.saveDebarbData(debarbEntity);
				}
			}

			
			
			this.setAlertMessage(MessageConstants.SAVE_SUCCESS);
			
		
		} catch (Exception e) {
			this.setAlertMessage(MessageConstants.SAVE_FAILED);
			e.printStackTrace();
		}
		//return "toExtractExperts";
		return this.toExtractExperts();
	}

	/**
	 * 申请指定专家
	 * 
	 * @return
	 */
	@Action("auditPointToExperts")
	public String auditPointToExperts() {
		this.setOpaUserAndDate(project);
		if ("1".equals(isSave)) {// 已保存过，进行更新操作
			List<ProjectEntity> projectList = new ArrayList<ProjectEntity>();
			projectList.add(project);
			projectService.updateProjects(projectList, null);
		} else {
			project.setStatus(4L);
			projectService.saveProject(project, null);
		}
		return "apply";
	}

	/**
	 * 保存指定专家申请说明
	 * 
	 * @return
	 */
	@Action("saveApplyInfo")
	public String saveApplyInfo() {
		this.context();
		// 当前session
		HttpSession session = getRequest().getSession();
		// 当前用户信息
		UserEntity user = (UserEntity) session.getAttribute("userInfo");
		this.setOpaUserAndDate(apply);
		try {
			projectService.saveApplyInfo(apply, user);
			this.setAlertMessage(MessageConstants.APPLY_SUCCESS);
		} catch (Exception e) {
			this.setAlertMessage(MessageConstants.APPLY_FAILED);
			e.printStackTrace();
		}
		return queryProjectList();
	}

	/**
	 * 提交指定专家
	 * 
	 * @return
	 */
	@Action("submitPointResult")
	public String submitPointResult() {
		this.context();
		this.getResponse().setContentType("text/html;charset=UTF-8");
		try {
			//根据项目ID查询同一个批次下有多少个项目
			ProjectEntity entity = new ProjectEntity();
			entity.setProjectId(result.getProjectId());
			entity=  projectService.queryProjectById(entity);
			if("".equals(entity.getDecimationBatch())||entity.getDecimationBatch()==null) {
				throw new HZWException("批次号为空，请联系管理员！");
			}
			List<ProjectEntity> entityList =  projectService.queryProjectByDecimationBatch(entity);
			//'result.conditionId':conditionId,'result.userId':expertId,'result.projectId':projectId
			for(ProjectEntity project:entityList) {
				result.setProjectId(project.getProjectId());
				projectService.submitPointResult(result);
			}
			
			this.getResponse().getWriter().write("success");
		} catch (Exception e) {
			try {
				this.getResponse().getWriter().write("no");
			} catch (IOException e1) {
				e1.printStackTrace();
			}
			e.printStackTrace();
		}
		return null;
	}

	/**
	 * 查询指定专家
	 * 
	 * @return
	 */
	@Action("queryPagePointExperts")
	public String queryPagePointExperts() {
		ExpertInfoEntity expert = null == conEntity.getExpert() ? new ExpertInfoEntity() : conEntity.getExpert();
		List<ConditionEntity> list = projectService.queryConditionList(conEntity);
		conEntity = list.get(0);
		conEntity.setPage(this.getPage());
		conEntity.getPage().setShowCount(5);
		conEntity.setExpert(expert);
		expertList = projectService.queryPagePointExperts(conEntity);
		conEntity.setJoin_status(0L);// 2015-4-10
		resultList = projectService.queryPointedExperts(conEntity);
		return "pointTo";
	}

	/**
	 * 删除指定专家
	 * 
	 * @return
	 */
	@Action("deletePointResult")
	public String deletePointResult() {
		projectService.deletePointResult(result);
		return null;
	}

	/**
	 * 查询专家除参加当前项目外，在评审当天还参加过是否其他项目
	 * 
	 * @return
	 */
	@Action("valPointExperts")
	public String valPointExperts() {
		this.context();
		this.getResponse().setCharacterEncoding("utf-8");
		PrintWriter out = null;
		try {
			out = this.getResponse().getWriter();
			projectList = projectService.queryExtractExpertList(project);
			StringBuffer resultBuf = new StringBuffer("[");
			for (int i = 0; i < projectList.size(); i++) {
				if (0 < i) {
					resultBuf.append(",");
				}
				ProjectEntity proEntity = projectList.get(i);
				resultBuf.append("{\"expertId\":\"").append(proEntity.getExpertId()).append("\",");
				resultBuf.append("\"expertName\":\"").append(proEntity.getExpertName()).append("\",");
				resultBuf.append("\"projectName\":\"").append(proEntity.getProjectName()).append("\",");
				resultBuf.append("\"bidTime\":\"")
						.append(DateUtil.getFormatDateTime("yyyy-MM-dd HH:mm", proEntity.getBidTime())).append("\",");
				resultBuf.append("\"bidAddress\":\"").append(proEntity.getBidAddress()).append("\"}");
			}

			resultBuf.append("]");
			out.print(resultBuf.toString());
			out.close();
		} catch (IOException e) {
			e.printStackTrace();
			out.print("[]");
		}
		return null;
	}

	/**
	 * 保存指定专家
	 * 
	 * @return
	 */
	@Action("savePointExperts")
	public String savePointExperts() {
		projectService.savePointExperts(result);
		return null;
	}

	/**
	 * 查询评标专业列表
	 * 
	 * @return
	 */
	@Action("queryMajorList")
	public String queryMajorList() {
		List<SpecialtyInfoEntity> specialtyInfoList = expertInfoService.querySpecialtyInfoList();
		JsonConfig config = new JsonConfig();
		// 通过JsonConfig的属性过滤器去除不需要的属性
		config.setJsonPropertyFilter(new PropertyFilter() {
			@Override
			public boolean apply(Object arg0, String arg1, Object arg2) {
				if (arg1.equals("spe_id") || arg1.equals("spe_parent") || arg1.equals("spe_name")) {
					return false;// 不忽略
				}
				return true;
			}
		});
		JSONArray jsonArray = JSONArray.fromObject(specialtyInfoList, config);
		majorStr = jsonArray.toString();
		// 将属性名称修改zTree对应的属性名称
		majorStr = majorStr.replaceAll("spe_parent", "pId").replaceAll("spe_name", "name").replaceAll("spe_id", "id");
		return "majorStr";
	}

	/**
	 * 查询项目退回原因
	 * 
	 * @return
	 */
	@Action("projectReturnReason")
	public String projectReturnReason() {
		if (projectAuditEntity == null) {
			projectAuditEntity = new ProjectAuditEntity();
		}
		projectAuditEntity = projectService.getProAuditInfoByProId(projectAuditEntity);
		return "projectAuditReason";
	}

	/**
	 * 查询项目退回原因
	 * 
	 * @return
	 */
	@Action("projectReturnReasonList")
	public String projectReturnReasonList() {
		if (projectAuditEntity == null) {
			projectAuditEntity = new ProjectAuditEntity();
		}
		projectAuditList = projectService.getAuditInfoById(projectAuditEntity);
		return "projectAuditReasonList";
	}

	/**
	 * 更新项目状态
	 * 
	 * @return
	 */
	@Action("updateProjectStatus")
	public String updateProjectStatus() {
		try {
			// project.setStatus(2L);//抽取中,即为申请成功
			projectService.updateProjectStatus(project);
			this.setAlertMessage(MessageConstants.APPLY_SUCCESS);
		} catch (Exception e) {
			e.printStackTrace();
			this.setAlertMessage(MessageConstants.APPLY_FAILED);
		}
		project = null;
		return this.queryProjectList();
	}

	
	/**
	 * 处理回避数据
	 * 
	 * @return
	 */
	private List<ExtractDebarbEntity> disposeDebarbData() {
		// 获取回避信息
		List<ExtractDebarbEntity> extractDebarbEntityList = new ArrayList<ExtractDebarbEntity>();

		if (companyDebarbList != null) {
			for (int i = 0; i < companyDebarbList.size(); i++) {
				if(companyDebarbList.get(i)!=null){
					ExtractDebarbEntity newEntity = new ExtractDebarbEntity();
					newEntity.setCompany(companyDebarbList.get(i).getCompany());
					newEntity.setDebarbReason(companyDebarbList.get(i).getCompanyDebarbReason());
					newEntity.setDebarbType(SysConstants.DEBARB_TYPE.TYPE_1);
					this.setOpaUserAndDate(newEntity);
					extractDebarbEntityList.add(newEntity);
				}
				
			}
		}
		if (extractDebarbList != null) {
			for (int i = 0; i < extractDebarbList.size(); i++) {
				if(extractDebarbList.get(i)!=null){
					ExtractDebarbEntity newEntity = new ExtractDebarbEntity();
					newEntity.setExpertCode(extractDebarbList.get(i).getExpertCode());
					newEntity.setExpertPhone(extractDebarbList.get(i).getExpertPhone());
					newEntity.setDebarbReason(extractDebarbList.get(i).getExpertDebarbReason());
					newEntity.setDebarbType(SysConstants.DEBARB_TYPE.TYPE_2);
					this.setOpaUserAndDate(newEntity);
					extractDebarbEntityList.add(newEntity);
				}
			}
		}
		return extractDebarbEntityList;
	}
	
	public ProjectEntity getProjectEntity() {
		return projectEntity;
	}

	public void setProjectEntity(ProjectEntity projectEntity) {
		this.projectEntity = projectEntity;
	}

	public List<ProjectEntity> getProjectList() {
		return projectList;
	}

	public void setProjectList(List<ProjectEntity> projectList) {
		this.projectList = projectList;
	}

	public ConditionEntity getConditionEntity() {
		return conditionEntity;
	}

	public void setConditionEntity(ConditionEntity conditionEntity) {
		this.conditionEntity = conditionEntity;
	}

	public List<ConditionEntity> getConditionList() {
		return conditionList;
	}

	public void setConditionList(List<ConditionEntity> conditionList) {
		this.conditionList = conditionList;
	}

	public ConditionEntity getConEntity() {
		return conEntity;
	}

	public void setConEntity(ConditionEntity conEntity) {
		this.conEntity = conEntity;
	}

	public ProjectEntity getProject() {
		return project;
	}

	public void setProject(ProjectEntity project) {
		this.project = project;
	}

	public List<ResultEntity> getResultList() {
		return resultList;
	}

	public void setResultList(List<ResultEntity> resultList) {
		this.resultList = resultList;
	}

	public List<List<ResultEntity>> getExtractList() {
		return extractList;
	}

	public void setExtractList(List<List<ResultEntity>> extractList) {
		this.extractList = extractList;
	}

	public List<ExpertInfoEntity> getExpertList() {
		return expertList;
	}

	public void setExpertList(List<ExpertInfoEntity> expertList) {
		this.expertList = expertList;
	}

	public Long getAgree() {
		return agree;
	}

	public void setAgree(Long agree) {
		this.agree = agree;
	}

	public List<UserEntity> getUserList() {
		return userList;
	}

	public void setUserList(List<UserEntity> userList) {
		this.userList = userList;
	}

	public List<UserEntity> getOperatorList() {
		return operatorList;
	}

	public void setOperatorList(List<UserEntity> operatorList) {
		this.operatorList = operatorList;
	}

	public String getIsSave() {
		return isSave;
	}

	public void setIsSave(String isSave) {
		this.isSave = isSave;
	}

	public String getMajorStr() {
		return majorStr;
	}

	public void setMajorStr(String majorStr) {
		this.majorStr = majorStr;
	}

	public String getApplyFlag() {
		return applyFlag;
	}

	public void setApplyFlag(String applyFlag) {
		this.applyFlag = applyFlag;
	}

	public ResultEntity getResult() {
		return result;
	}

	public void setResult(ResultEntity result) {
		this.result = result;
	}

	public ApplyRecordEntity getApply() {
		return apply;
	}

	public void setApply(ApplyRecordEntity apply) {
		this.apply = apply;
	}

	public String getIsFill() {
		return isFill;
	}

	public void setIsFill(String isFill) {
		this.isFill = isFill;
	}

	public ProjectAuditEntity getProjectAuditEntity() {
		return projectAuditEntity;
	}

	public void setProjectAuditEntity(ProjectAuditEntity projectAuditEntity) {
		this.projectAuditEntity = projectAuditEntity;
	}

	public List<ProjectAuditEntity> getProjectAuditList() {
		return projectAuditList;
	}

	public void setProjectAuditList(List<ProjectAuditEntity> projectAuditList) {
		this.projectAuditList = projectAuditList;
	}

	public List<TemplateEntity> getTemplateEntityList() {
		return templateEntityList;
	}

	public void setTemplateEntityList(List<TemplateEntity> templateEntityList) {
		this.templateEntityList = templateEntityList;
	}

	public ExtractDebarbEntity getExtractDebarbEntity() {
		return extractDebarbEntity;
	}

	public void setExtractDebarbEntity(ExtractDebarbEntity extractDebarbEntity) {
		this.extractDebarbEntity = extractDebarbEntity;
	}

	public List<ExtractDebarbEntity> getCompanyDebarbList() {
		return companyDebarbList;
	}

	public void setCompanyDebarbList(List<ExtractDebarbEntity> companyDebarbList) {
		this.companyDebarbList = companyDebarbList;
	}

	public List<ExtractDebarbEntity> getExtractDebarbList() {
		return extractDebarbList;
	}

	public void setExtractDebarbList(List<ExtractDebarbEntity> extractDebarbList) {
		this.extractDebarbList = extractDebarbList;
	}

}
