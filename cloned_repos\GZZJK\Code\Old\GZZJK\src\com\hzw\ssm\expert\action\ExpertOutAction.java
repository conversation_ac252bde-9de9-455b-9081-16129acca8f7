package com.hzw.ssm.expert.action;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.ArrayList;
import java.util.List;

import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFFont;
import org.apache.poi.hssf.usermodel.HSSFPrintSetup;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.struts2.ServletActionContext;
import org.apache.struts2.convention.annotation.Action;
import org.apache.struts2.convention.annotation.Namespace;
import org.apache.struts2.convention.annotation.ParentPackage;
import org.apache.struts2.convention.annotation.Result;
import org.apache.struts2.convention.annotation.Results;
import org.springframework.beans.factory.annotation.Autowired;

import com.hzw.ssm.expert.entity.ExperOutEntity;
import com.hzw.ssm.expert.service.ExpertOutService;
import com.hzw.ssm.fw.base.BaseAction;
import com.hzw.ssm.fw.util.DateUtil;
import com.hzw.ssm.fw.util.ExcelUtil;
import com.hzw.ssm.sys.project.entity.ProjectEntity;
import com.hzw.ssm.sys.user.entity.UserEntity;


@Namespace("/expertOut")
@ParentPackage(value = "default")
@Results({ @Result(	name = "expertOutCheckList",
					location = "/jsp/expert/expertOutCheckList.jsp"),
		@Result(name = "queryProject",
				location = "/jsp/expert/queryProject.jsp"),
		@Result(name = "outList",
				location = "/jsp/expert/outlist.jsp") })
/**
 * 专家出库相关Action
 */
public class ExpertOutAction extends BaseAction {

	private static final long serialVersionUID = -8823858495799309882L;

	private ExperOutEntity entity;
	/**
	 * 待审核
	 */
	private List<ExperOutEntity> dealAdopt;
	/**
	 * 审核通过
	 */
	private List<ExperOutEntity> adopt;
	/**
	 * 审核未通过
	 */
	private List<ExperOutEntity> noAdopt;
	/**
	 * 待开标项目
	 */
	private List<ProjectEntity> projects;

	private String key;

	@Autowired
	private ExpertOutService outService;


	/**
	 * 专家出库--审核列表
	 * 
	 * @return
	 * @throws Exception
	 */
	@Action("expertOutCheckList")
	public String expertInfoCompleteList() {
		// 1.默认查询赋值
		if (entity == null) {
			entity = new ExperOutEntity();
			// 待出库
			entity.setOutStatus("10");
		}
		entity.setPage(this.getPage());
		if ("20".equals(entity.getOutStatus())) {
			entity.setQueryProjectFlag(true);
		}
		// 2.查询
		List<ExperOutEntity> result = new ArrayList<>();
		result = outService.queryListexpert(entity);
		// 2.1根据查询条件返回结果集
		if ("10".equals(entity.getOutStatus())) {
			dealAdopt = result;
		}
		if ("20".equals(entity.getOutStatus())) {
			adopt = result;
		}
		if ("30".equals(entity.getOutStatus())) {
			noAdopt = result;
		}
		// 3.返回页面
		return "expertOutCheckList";
	}

	/**
	 * 专家出库--已出库列表
	 * 
	 * @return
	 * @throws Exception
	 */
	@Action("outList")
	public String outList() {
		// 1.默认查询赋值
		if (entity == null) {
			entity = new ExperOutEntity();
		}
		
		entity.setPage(this.getPage());

		// 2.查询
		List<ExperOutEntity> result = new ArrayList<>();
		result = outService.queryListout(entity);
		adopt = result;
		// 3.返回页面
		return "outList";
	}

	/**
	 * 专家出库--审核
	 * 
	 * @return
	 * @throws Exception
	 */
	@Action("check")
	public String check() {
		this.context();
		// 1.默认赋值
		if (entity == null) {
			entity = new ExperOutEntity();
		}
		// 当前session
		HttpSession session = getRequest().getSession();
		// 当前用户信息
		UserEntity info = (UserEntity) session.getAttribute("userInfo");
		entity.setCheckId(info.getUser_id());
		entity.setModify_id(info.getUser_id());
		entity.setModifyRole(info.getRole_id());
		// 2.审核
		outService.checkout(entity);
		// 3.返回页面
		return null;
	}
	/**
	 * 专家出库--重新入库
	 * 
	 * @return
	 * @throws Exception
	 */
	@Action("checkIn")
	public String checkIn() {
		this.context();
		// 1.默认赋值
		if (entity == null) {
			entity = new ExperOutEntity();
		}
		// 当前session
		HttpSession session = getRequest().getSession();
		// 当前用户信息
		UserEntity info = (UserEntity) session.getAttribute("userInfo");
		entity.setCheckId(info.getUser_id());
		entity.setModify_id(info.getUser_id());
		entity.setModifyRole(info.getRole_id());
		// 2.审核
		outService.checkIn(entity);
		// 3.返回页面
		return null;
	}

	/**
	 * 专家出库--查询待开标项目
	 * 
	 * @return
	 * @throws Exception
	 */
	@Action("queryProject")
	public String queryProject() {
		// 2.查询
		ExperOutEntity query = new ExperOutEntity();
		query.setUserId(key);
		projects = outService.queryProject(query);
		// 3.返回页面
		return "queryProject";
	}
	/**
	 * 专家出库--导出出库审核pdf
	 * 
	 * @return
	 * @throws Exception
	 */
	@Action("downEXCL")
	public String downEXCL() {
		//1.准备数据
		//1.1根据条件查询数据
		// 1.默认查询赋值
		if (entity == null) {
			entity = new ExperOutEntity();
			// 待出库
			entity.setOutStatus("10");
		}
		List<ExperOutEntity> list=outService.queryexpertsForEXCL(entity);
		//2.申明返回对象
		this.context();
		HttpServletResponse respose = this.getResponse();
		respose.setHeader("Content-type", "text/html;charset=utf-8");
		respose.setCharacterEncoding("utf-8");
		PrintWriter out = null;
		String tableTopStyle = "0";	
		try {
			out = respose.getWriter();
			int totalCols = 0;
			int currentRowNo = 0;
			String fileName="ExpertOut";
			HSSFWorkbook wb = ExcelUtil.createHSSFWorkbook();
			//3.生成结构化excl数据
			String titleName = "专家出库信息统计(汇总)";
			String[] tableTotal={"序号", "专家编号", "专家姓名", "手机号", "专家类型", "评价得分", "行政区域", "评标专业", "出生日期", "出库原因","提交时间"};
			int[] tableCols = { 1, 1, 1, 1, 1, 1 , 1, 1, 1, 1, 1};
			int[] contentCols = { 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1};
			if ("10".equals(entity.getOutStatus())){
				titleName = "专家待审核信息统计";
				totalCols = 11;
			}
			if ("20".equals(entity.getOutStatus())) {
				titleName = "专家审核通过信息统计";
				totalCols = 13;
				tableTotal =new String[]{"序号", "专家编号", "专家姓名", "手机号", "专家类型", "评价得分", "行政区域", "评标专业", "出生日期", "出库原因","提交时间","审核人","审核时间"};
				tableCols=new int[]{1,1, 1, 1, 1, 1, 1 , 1, 1, 1, 1, 1,1};
				contentCols=new int[]{ 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,1,1};
			}
			if ("30".equals(entity.getOutStatus())) {
				titleName = "专家审核不通过统计";
				totalCols = 13;
				tableTotal =new String[]{"序号", "专家编号", "专家姓名", "手机号", "专家类型", "评价得分", "行政区域", "评标专业", "出生日期", "出库原因","提交时间","审核人","审核时间"};
				tableCols=new int[]{1,1, 1, 1, 1, 1, 1 , 1, 1, 1, 1, 1,1};
				contentCols=new int[]{ 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,1,1};
			}

			HSSFCellStyle titleCell = ExcelUtil.createHSSFCellStyle(wb, 30, HSSFFont.BOLDWEIGHT_BOLD,
					HSSFCellStyle.ALIGN_CENTER);
			HSSFSheet sheet = ExcelUtil.createHSSFSheet(wb, titleName, HSSFPrintSetup.A4_PAPERSIZE, tableTopStyle);
			currentRowNo = ExcelUtil.mergeRowsOne2(sheet, titleCell, currentRowNo, totalCols,
					new String[] { titleName }, new int[] { totalCols });

			currentRowNo++;
			HSSFCellStyle tableTotalCell = ExcelUtil.createHSSFCellStyle(wb, 10, HSSFFont.BOLDWEIGHT_NORMAL,
					HSSFCellStyle.ALIGN_CENTER);
			
			
			currentRowNo = ExcelUtil.mergeRowsOne2(sheet, tableTotalCell, currentRowNo, totalCols, tableTotal,
					tableCols);

			

			for (int i = 0; i < list.size(); i++) {
				currentRowNo++;
				List<String> sls = entityToList(i, list.get(i)); //
				// * 动态合并第一步将相同的数据变成空值，保留第一次出现的数据()
				currentRowNo = mergeRowsOne1(sheet, tableTotalCell, currentRowNo, currentRowNo, totalCols, sls,
						contentCols);

			}
			ExcelUtil.autoSheetAutoSizeColumn(sheet, totalCols);
			//4.生成excl表格并返回
			getFile(titleName, wb, out,fileName);
			
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}
	
	/**
	 * 获取文件保存路径
	 * 
	 * @param titleName
	 * @param wb
	 * @param out
	 */
	private void getFile(String titleName, HSSFWorkbook wb, PrintWriter out,String fileName) {
		String savePath = ServletActionContext.getServletContext().getRealPath("") + File.separator + fileName;
		File f1 = new File(savePath);
		if (!f1.exists()) {//
			f1.mkdirs();
		}
		String newName = titleName + "_" + System.currentTimeMillis() + ".xls";
		FileOutputStream fileOut = null;
		try {
			fileOut = new FileOutputStream(savePath + File.separator + newName);
			wb.write(fileOut);
		} catch (Exception e) {
			out.print("error");
			log.error(e);
		} finally {
			if (fileOut != null) {
				try {
					fileOut.close();
					// out.print("zjcqjl/"+projectEntity.getProjectId());
					// newName=new String(newName.getBytes("GB-2312"),"UTF-8");
					out.print("{\"filePath\":\"" + "/" + fileName + "\"," + "\"fileName\":\"" + newName + "\"}");
					out.close();
				} catch (IOException e) {
					out.print("error");
				}
			}
		}
	}
	
	public List<String> entityToList(int i, ExperOutEntity entity) {
		List<String> lt = new ArrayList<String>();
		lt.add((i + 1) + "");
		lt.add(entity.getExpertNum());
		lt.add(entity.getUserName());
		lt.add(entity.getMobilePhone());
		if("1".equals(entity.getExpertType())){
			lt.add("A1高级");
		}else if("2".equals(entity.getExpertType())){
			lt.add("A2中级满8年");
		}else if("3".equals(entity.getExpertType())){
			lt.add("C1中级不满8年");
		}else{
			lt.add("D初级");
		}
		lt.add(entity.getEvalScore());
		lt.add(entity.getDistrict()	);
		lt.add(entity.getMajor());
		lt.add(DateUtil.dateToString(entity.getBirthday()));
		if("1".equals(entity.getOutReason())){
			lt.add("重大疾病");
		}else if("2".equals(entity.getOutReason())){
			lt.add("满70周岁");
		}else if("3".equals(entity.getOutReason())){
			lt.add("已去世");
		}else{
			lt.add("其他");
		}
		if(entity.getDealOutTime()!=null){
			lt.add(DateUtil.dateToString(entity.getDealOutTime(), "yyyy-MM-dd HH:mm:ss"));
		}
		if("20".equals(entity.getOutStatus())||"30".equals(entity.getOutStatus())){
			lt.add(entity.getCheckName());
			if(entity.getCheckTime()!=null){
				lt.add(DateUtil.dateToString(entity.getCheckTime(), "yyyy-MM-dd HH:mm:ss"));
			}
			
		}
		
		return lt;
	}
	/**
	 * 
	 * 函数功能描述：生成结构化EXCEL (主要用在遍历没有跨行EXCEL的内容)
	 * 
	 * @param sheet
	 *            表单对象
	 * @param style
	 *            单元格风格
	 * @param startRowNo
	 *            开始行
	 * @param currentRowNo
	 *            当前行号
	 * @param colspanRowNo
	 *            合并的列
	 * @param totalCols
	 *            每行共有多少单元格
	 * @param contextList
	 *            需要合并的内容 单元格List
	 * @param mergeCols
	 *            内容对应占有多少单元格 new[] margeCols 只需要告知跨多少列
	 * @return
	 */
	public static int mergeRowsOne1(HSSFSheet sheet, HSSFCellStyle style, int startRowNo, int currentRowNo,
			int totalCols, List<String> contextList, int[] mergeCols) {
		if (mergeCols == null || mergeCols.length <= 0) {
			return currentRowNo;
		}
		if (contextList == null) {
			contextList = new ArrayList<String>();
		}
		// 1.创建改行
		HSSFRow row = ExcelUtil.createHSSFRow(style, sheet, currentRowNo, totalCols);
		int firstCols = 0;
		int lastCols = 0;
		for (int i = 0; i < mergeCols.length; i++) {
			lastCols = firstCols + mergeCols[i] - 1;
			HSSFCell cell = row.getCell(firstCols);
			cell.setCellStyle(style);
			String value = null;
			if (contextList.size() > i) {
				value = contextList.get(i);
			}

			if (value == null || value == "") {
				value = "";
			}
			cell.setCellType(HSSFCell.CELL_TYPE_STRING);
			cell.setCellValue(value);
			sheet.setColumnWidth(firstCols, value.getBytes().length * 2);
			/*
			 * sheet.addMergedRegion( new CellRangeAddress(startRowNo,
			 * currentRowNo, firstCols, lastCols));
			 */

			firstCols = lastCols + 1;
		}
		return currentRowNo;
	}
	/**
	 * @return the entity
	 */
	public ExperOutEntity getEntity() {
		return entity;
	}

	/**
	 * @param entity
	 *            the entity to set
	 */
	public void setEntity(ExperOutEntity entity) {
		this.entity = entity;
	}

	/**
	 * @return the dealAdopt
	 */
	public List<ExperOutEntity> getDealAdopt() {
		return dealAdopt;
	}

	/**
	 * @param dealAdopt
	 *            the dealAdopt to set
	 */
	public void setDealAdopt(List<ExperOutEntity> dealAdopt) {
		this.dealAdopt = dealAdopt;
	}

	/**
	 * @return the adopt
	 */
	public List<ExperOutEntity> getAdopt() {
		return adopt;
	}

	/**
	 * @param adopt
	 *            the adopt to set
	 */
	public void setAdopt(List<ExperOutEntity> adopt) {
		this.adopt = adopt;
	}

	/**
	 * @return the noAdopt
	 */
	public List<ExperOutEntity> getNoAdopt() {
		return noAdopt;
	}

	/**
	 * @param noAdopt
	 *            the noAdopt to set
	 */
	public void setNoAdopt(List<ExperOutEntity> noAdopt) {
		this.noAdopt = noAdopt;
	}

	/**
	 * @return the projects
	 */
	public List<ProjectEntity> getProjects() {
		return projects;
	}

	/**
	 * @param projects
	 *            the projects to set
	 */
	public void setProjects(List<ProjectEntity> projects) {
		this.projects = projects;
	}

	/**
	 * @return the key
	 */
	public String getKey() {
		return key;
	}

	/**
	 * @param key
	 *            the key to set
	 */
	public void setKey(String key) {
		this.key = key;
	}

}
