package com.hzw.ssm.sys.sms;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Hashtable;
import java.util.List;

import com.hzw.ssm.sys.project.dao.ProjectMapper;
import com.hzw.ssm.sys.project.entity.ResultEntity;
import com.hzw.ssm.sys.sms.mwutil.MO;


public class ReceiveMsgTimerRunnable implements Runnable {
	
	private ProjectMapper mapper;
	private long time;        // 间隔时间
	public ReceiveMsgTimerRunnable(){}
	
	public ReceiveMsgTimerRunnable(long time,ProjectMapper mapper)
	{
		this.time = time;
		this.mapper=mapper;
	}

	public void run() {
        try { 
            Thread.sleep(time); 
        } catch (InterruptedException e) { 
            e.printStackTrace(); 
        } 
		SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		SimpleDateFormat format1 = new SimpleDateFormat("yyyyMMddHHmmss");
		
		//List<String> msgList = SMSNewUtil.getSMSNewUtil().reviceMsg();//获得回复结果内容(未检出过的)
		List<MO> moList = SMSNewUtil.getSMSNewUtil().getMo(20);
		if (null != moList && !moList.isEmpty()){
			Hashtable<String, SMSEntity> smsHash=new Hashtable<String, SMSEntity>();//封装回复结果对象
			List<ResultEntity> needResult=new ArrayList<ResultEntity>();//匹配到的回复结果
			String replyPhones="'";//所有回复的手机号码
			for (MO mo : moList) {
				SMSEntity sms=new SMSEntity();
				//String[] msgStr = msg.split(",");
				sms.setExtNum(mo.getExno());     // 扩展号
				sms.setPhone(mo.getMobile());                                      // 手机号码
				sms.setContent(mo.getContent());                                     // 短信回复内容
				sms.setReplyTime(mo.getRtime());                                   // 短信回复时间
				smsHash.put(sms.getPhone()+sms.getExtNum(), sms);
				replyPhones+=mo.getMobile()+"','";//
			}
			//根据手机号码查询出所有抽取记录，取得message_no短信批次和call_time通知时间
			ResultEntity result = new ResultEntity();
			result.setPhone(replyPhones.substring(0,replyPhones.lastIndexOf("'")-1));
			List<ResultEntity> list=mapper.queryExtracedResultByPhone(result);
			try {
				if(list!=null){
					for(ResultEntity r:list){
						String smsKey=r.getPhone()+r.getMessageNo().substring(format1.format(r.getCallTime()).length());
						SMSEntity sms=smsHash.get(smsKey);
						if(sms!=null){//匹配到回复结果
							String recieveNO=format1.format(r.getCallTime())+""+sms.getExtNum();
							if(r.getMessageNo().equals(recieveNO)){//匹配到需要更新的结果记录
								//以message_no与call_time+extNum进行比较，相等则是更新改记录的回复结果
								r.setRecieveTime(format.parse(sms.getReplyTime()));
								int joinStatus=-1;
								try {
									joinStatus=Integer.parseInt(sms.getContent().trim());
									if(joinStatus==1){//参加
										joinStatus=0;
									}
									else if(joinStatus==2){//不参加
										joinStatus=1;
									}
								} catch (NumberFormatException e) {
									joinStatus=1;//回复非数字，默认不参加 
								}
								r.setJoinStatus(new Long(joinStatus));
								needResult.add(r);
							}
						}
					}
				}
			} catch (ParseException e) {
				e.printStackTrace();
			}
			if(needResult.size()>0){
				mapper.batchUpdateExtractedExpert(needResult);//更新回复结果
			}
		}
    }

}
