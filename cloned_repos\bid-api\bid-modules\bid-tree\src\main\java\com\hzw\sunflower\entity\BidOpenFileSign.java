package com.hzw.sunflower.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.hzw.sunflower.common.BaseBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Created by zgq on 2021/06/30
 * <AUTHOR>
 */
@ApiModel("线上开标记录表签章表")
@TableName("t_bid_open_file_sign")
@Data
public class BidOpenFileSign extends BaseBean {

    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "标段id")
    private Long sectionId;

    @ApiModelProperty(value = "签章文件ossId")
    private Long fileOssId;

    @ApiModelProperty(value = "源文件ossId")
    private Long originFileOssId;

    @ApiModelProperty(value = "用户ID")
    private Long userId;

    @ApiModelProperty(value = "企业ID")
    private Long companyId;
}
