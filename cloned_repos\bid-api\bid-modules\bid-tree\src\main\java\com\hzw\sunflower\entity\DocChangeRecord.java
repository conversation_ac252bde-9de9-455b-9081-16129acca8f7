package com.hzw.sunflower.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.hzw.sunflower.common.BaseBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * Created by sz on 2021/06/30
 */
@ApiModel("招标文件表更操作记录表")
@TableName("t_doc_change_record")
@Data
public class DocChangeRecord extends BaseBean {

    private static final long serialVersionUID = 1473324777965746989L;
    /**
     * 主键ID
     *
     * @mbg.generated
     */
    private Long id;

    /**
     * 标段ID
     *
     * @mbg.generated
     */
    private Long sectionId;

    /**
     * 招标文件表ID
     *
     * @mbg.generated
     */
    private Long docId;

    /**
     * 标段名称
     *
     * @mbg.generated
     */
    private String sectionName;

    /**
     * 提交时间
     *
     * @mbg.generated
     */
    private Date submitTime;

    /**
     * 文件提交次数
     *
     * @mbg.generated
     */
    private Integer submitNumber;

    /**
     * 标段/包提交次数
     *
     * @mbg.generated
     */
    private Integer bidSubmitNumber;

    /**
     * 变更类型
     *
     * @mbg.generated
     */
    private String changeType;

    /**
     * 关联招标附件oss表ID集合
     *
     * @mbg.generated
     */
    private String annexOssFileIds;

    /**
     * 招标文件发售开始时间
     *
     * @mbg.generated
     */
    private Date saleStartTime;

    /**
     * 招标文件发售结束时间类型（1：确定时间; 2：另行通知；3：投标文件递交截止时间前一天）
     *
     * @mbg.generated
     */
    private Integer saleEndTimeType;

    /**
     * 招标文件发售结束时间
     *
     * @mbg.generated
     */
    private Date saleEndTime;

    /**
     * 投标文件递交截止时间类型（1：确定时间; 2：另行通知）
     *
     * @mbg.generated
     */
    private Integer submitEndTimeType;

    /**
     * 投标文件递交截止时间
     *
     * @mbg.generated
     */
    private Date submitEndTime;

    /**
     * 文件发售方式(0:电子文件线上下载, 1:纸质文件不支持邮寄)
     *
     * @mbg.generated
     */
    private Integer releaseFileType;

    /**
     * 文件发售审核方式(0:无需审核, 1:审核无需材料, 2:审核上传材料)
     *
     * @mbg.generated
     */
    private Integer reviewFileType;

    /**
     * 材料清单
     *
     * @mbg.generated
     */
    private String materialList;

    /**
     * 标书费类型 0不收取 1按项目 2按标段/包
     *
     * @mbg.generated
     */
    private Integer tenderFeeType;

    /**
     * 标书费金额
     *
     * @mbg.generated
     */
    private BigDecimal tenderFee;

    /**
     * 费用类型 1：标书费 2：平台服务费
     *
     * @mbg.generated
     */
    private Integer feeType;

    /**
     * 标书费支付方式 0：线上 1：线下
     *
     * @mbg.generated
     */
    private Integer paymentType;

    /**
     * 保证金类型 0不收取 1定额 2比例
     *
     * @mbg.generated
     */
    private Integer bondType;

    /**
     * 保证金金额
     *
     * @mbg.generated
     */
    private BigDecimal bond;

    /**
     * 保证金金额
     *
     * @mbg.generated
     */
    private BigDecimal bondPercent;
    /**
     * 联系人
     *
     * @mbg.generated
     */
    private String contact;

    /**
     * 联系电话
     *
     * @mbg.generated
     */
    private String phone;

    /**
     * oss文件ID
     */
    private Long ossFileId;

    /**
     * 开标地点
     *
     * @mbg.generated
     */
    private String openBidAddress;

    /**
     * 按套销售信息
     */
    private String groupInfo;

    /**
     * 招标文件售价说明（1：交通；2：水利；3：其他）
     */
    private Integer filePriceState;


    /**
     * 关联标段(冗余字段)
     */
    private String relevancePackageNumber;

    /**
     * 标书费是否收费1 收费 ，2免费
     *
     * @mbg.generated
     */
    private Integer tenderFeeFlag;

    /**
     * 已购买招标文件的投标人本次是否收费
     */
    private Integer free;

    /**
     * 文件售价说明其他时具体描述
     */
    private String otherPriceDescribe;

    @ApiModelProperty(value = "线上澄清（1 支持 2 不支持）")
    private Integer clarifyOnLine;

    @ApiModelProperty(value ="提出澄清截止时间")
    private Date clarifyEndTime;

    @ApiModelProperty(value ="提出澄清截止时间（1：确定时间; 2：投标文件递交截止时间前一天）")
    private Integer clarifyEndTimeType;

    @ApiModelProperty(value = "线上异议（1 支持 2 不支持）")
    private Integer questionOnLine;

    @ApiModelProperty(value ="提出异议截止时间（1：确定时间; 2：投标文件递交截止时间前一天）")
    private Integer questionEndTimeType;

    @ApiModelProperty(value ="提出异议截止时间")
    private Date questionEndTime;

    @ApiModelProperty(value = "线上开标（1 支持 2 不支持）")
    private Integer bidOpenOnline;

    @ApiModelProperty(value ="解密时间（分钟）")
    private Integer decryptTime;

    @ApiModelProperty(value ="供应商少于x家终止开标")
    private Integer supNumEndBid;

    @ApiModelProperty(value ="关联招标开标一览表oss表ID集合")
    private String scheduleOssFileIds;

}
