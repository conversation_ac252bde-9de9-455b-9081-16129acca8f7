package com.hzw.expertfee.api.controller;


import cn.dev33.satoken.stp.SaTokenInfo;
import cn.dev33.satoken.stp.StpUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hzw.expertfee.api.common.Result;
import com.hzw.expertfee.api.controller.request.LoginDTO;
import com.hzw.expertfee.api.entity.SysApp;
import com.hzw.expertfee.api.service.ISysAppService;
import io.swagger.annotations.ApiOperation;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.concurrent.TimeUnit;


/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/")
public class LoginController {
    @Resource
    private ISysAppService iSysAppService;
    @Resource
    private StringRedisTemplate objectRedisTemplate;

    @ApiOperation("登陆")
    @PostMapping("/login")
    public Result<Object> doLogin(@RequestBody LoginDTO record) {
        HashMap<String,Object> retData=new HashMap<>();

        String tokenKey="expertfee_login_token:"+record.getClientId();
        if (objectRedisTemplate.opsForValue().get(tokenKey)!=null){
            retData.put("token",objectRedisTemplate.opsForValue().get(tokenKey));
        }else{
            SysApp sysApp = iSysAppService.getOne(new LambdaQueryWrapper<SysApp>()
                    .eq(SysApp::getSn, record.getClientId())
                    .eq(SysApp::getSecretKey,record.getClientSecret())
                    .eq(SysApp::getIsDelete,0)
            );
            if (sysApp!=null){
                StpUtil.login(sysApp.getSn());
                SaTokenInfo tokenInfo = StpUtil.getTokenInfo();
                String token = tokenInfo.getTokenValue();
                retData.put("token",token);
                objectRedisTemplate.opsForValue().set(tokenKey, token, 2592000, TimeUnit.SECONDS);
            }else {
                return Result.failed(401,"账号或密码不正确",null);
            }
        }


        return Result.ok(retData);

    }
}
