package com.hzw.system.api;

import com.hzw.common.core.domain.R;
import com.hzw.common.core.exception.user.UserException;
import com.hzw.system.api.domain.SysUser;
import com.hzw.system.api.domain.dto.ExpertUserDTO;
import com.hzw.system.api.model.*;

import java.util.List;

/**
 * 用户服务
 *
 * <AUTHOR>
 */
public interface RemoteUserService {

    /**
     * 通过用户名查询用户信息
     *
     * @param username 用户名
     * @return 结果
     */
    LoginUser getUserInfo(String username) throws UserException;

    /**
     * 通过手机号查询用户信息
     *
     * @param phonenumber 手机号
     * @return 结果
     */
    LoginUser getUserInfoByPhonenumber(String phonenumber) throws UserException;

    /**
     * 通过手机号修改用户密码
     *
     * @param userReq
     * @return 结果
     */
    Boolean doChangePwdByPhonenumber(RegisterUserReq userReq) throws UserException;

    /**
     * 通过手机号查询供应商信息
     *
     * @param phonenumber 手机号
     * @return 结果
     */
    SysUser getSupplierInfoByPhonenumber(String phonenumber) throws UserException;


    /**
     * 通过手机号查询审核通过供应商用户
     * @param phonenumber
     * @return
     * @throws UserException
     */
    SysUser getSupplierByPhonenumber(String phonenumber) throws UserException;

    /**
     * 供应商注册
     *
     * @param userReq 个人信息
     * @return 结果
     */
    Boolean doSupplierRegister(RegisterUserReq userReq);

    /**
     * 通过邮箱查询用户信息
     *
     * @param email 邮箱
     * @return 结果
     */
    LoginUser getUserInfoByEmail(String email) throws UserException;

    /**
     * 通过openid查询用户信息
     *
     * @param openid openid
     * @return 结果
     */
    XcxLoginUser getUserInfoByOpenid(String openid) throws UserException;

    TokenLoginUser getUserInfoByClientid(String clientid) throws UserException;
    /**
     * 注册用户信息
     *
     * @param sysUser 用户信息
     * @return 结果
     */
    R registerUserInfo(SysUser sysUser);

    /**
     * 通过userId查询用户账户
     *
     * @param userId 用户id
     * @return 结果
     */
    String selectUserNameById(Long userId);

    /**
     * 根据专家信息新建用户
     * @param dto
     * @return
     */
    String insetUserByExpert(ExpertUserDTO dto);



    /**
     * 根据身份证号或者手机号查询用户
     * @param
     * @return
     */
    SysUser queryByParam(SysUser sysUser);

    /**
     * 更新用户表信息
     * @param
     * @return
     */
    void updateUserById(SysUser sysUser);

    /**
     * 根据id查询用户基本信息
     * @param id
     * @return
     */
    SysUser getUserInfoByUserId(String id);

    /**
     * 判断手机号是否存在 存在返回true 不存在false
     * @param phone
     * @param id
     * @return
     */
    Boolean checkUserPhone(String phone,String id);

    void updateUserPhone(String userId,String phone);

    /**
     * 根据openId查询user
     * @param openid
     * @return
     */
    SysUser querySysUser(String openid);

    /**
     * 获取管理员手机号
     * @return
     */
    List<String> getAdminPhone();
}
