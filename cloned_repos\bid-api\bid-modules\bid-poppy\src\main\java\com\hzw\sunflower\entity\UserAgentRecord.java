package com.hzw.sunflower.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.hzw.sunflower.common.BaseBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 用户创建代理的记录(t_user_agent_record)
 *
 * <AUTHOR>
 * @version 1.0.0 2021-04-14
 */
@ApiModel(description = "用户创建代理的记录")
@TableName("t_user_agent_record")
@Data
public class UserAgentRecord extends BaseBean {
    /**
     * 版本号
     */
    private static final long serialVersionUID = -8268108670722582133L;

    /* This code was generated by TableGo tools, mark 1 begin. */

    @ApiModelProperty(value = "主键", position = 1)
    private Long id;

    @ApiModelProperty(value = "用户id", position = 2)
    private Long userId;

    @ApiModelProperty(value = "用户身份标识（1招标人2.代理机构3投标人/供应商）")
    private Integer userIdentity;

    @ApiModelProperty(value = "代理机构id", position = 3)
    private Long agentId;

    @ApiModelProperty(value = "代理机构名称", position = 4)
    private String agentCompany;

    @ApiModelProperty(value = "代理机构联系人名称", position = 5)
    private String agentChargeName;

    @ApiModelProperty(value = "代理机构联系人id", position = 6)
    private Long agentChargeId;

    @ApiModelProperty(value = "代理机构联系人部门id", position = 6)
    private Long agentDepartId;

    @ApiModelProperty(value = "代理机构联系人手机号", position = 7)
    private String agentChargePhone;



}