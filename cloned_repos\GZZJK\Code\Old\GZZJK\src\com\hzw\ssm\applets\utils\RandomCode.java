package com.hzw.ssm.applets.utils;

import java.util.Random;

/**
 * <AUTHOR>
 * @Date 2021-02-01 13:34
 * @Version 1.0
 */
public class RandomCode {

    public static String getRandom() {
        String code = "";
        Random random = new Random();
        for (int i = 0; i < 6; i++) {
            int r = random.nextInt(10); //每次随机出一个数字（0-9）
            code = code + r;  //把每次随机出的数字拼在一起
        }
        return code;

    }

    /**
     * 获得id
     *
     * @return
     */
    public static String getId() {
        String dateStr = WxDateUtil.stampToDate(System.currentTimeMillis(), "yyyyMMddhhmmss");
        int random = Integer.parseInt(getRandom());
        return dateStr + random;
    }
}
