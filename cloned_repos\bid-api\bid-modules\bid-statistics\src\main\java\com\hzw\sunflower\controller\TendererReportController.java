package com.hzw.sunflower.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hzw.sunflower.common.BaseController;
import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.controller.response.ProjectCountVo;
import com.hzw.sunflower.entity.TendererReport;
import com.hzw.sunflower.entity.condition.TendererReportCondition;
import com.hzw.sunflower.service.TendererReportService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 */
@Api(tags = "招标人报表中心")
@RestController
@RequestMapping("/tendererReportList")
public class TendererReportController extends BaseController {

    @Autowired
    private TendererReportService tendererReportService;

    @ApiOperation(value = "项目数量")
    @ApiImplicitParam(name = "condition", value = "查询条件", required = true, dataType = "TendererReportCondition", paramType = "body")
    @PostMapping({"/projectCount"})
    public Result<List<ProjectCountVo>> projectCount(@RequestBody TendererReportCondition condition) {
        List<ProjectCountVo> vo = tendererReportService.findProjectCount(condition,getJwtUser());
        return Result.ok(vo);
    }

    @ApiOperation(value = "项目开标信息")
    @ApiImplicitParam(name = "condition", value = "查询条件", required = true, dataType = "TendererReportCondition", paramType = "body")
    @PostMapping({"/openBidInfo"})
    public Result<IPage<TendererReport>> openBidInfo(@RequestBody TendererReportCondition condition) {
        IPage<TendererReport> vo = tendererReportService.findOpenBidInfo(condition,getJwtUser());
        return Result.ok(vo);
    }

    @ApiOperation(value = "项目开标信息导出excel")
    @ApiImplicitParam(name = "req", value = "查询条件", required = true, dataType = "TendererReportCondition", paramType = "body")
    @PostMapping("/exportTendererOpenBidList")
    public  void  exportTendererOpenBidList(@RequestBody @Valid TendererReportCondition condition) {
        tendererReportService.exportExcel(condition,response,getJwtUser());
    }

    @ApiOperation(value = "项目报名信息")
    @ApiImplicitParam(name = "condition", value = "查询条件", required = true, dataType = "TendererReportCondition", paramType = "body")
    @PostMapping({"/applyInfoList"})
    public Result<IPage<TendererReport>> applyInfoList(@RequestBody TendererReportCondition condition) {
        IPage<TendererReport> vo = tendererReportService.applyInfoList(condition,getJwtUser());
        return Result.ok(vo);
    }

    @ApiOperation(value = "项目报名信息导出excel")
    @ApiImplicitParam(name = "req", value = "查询条件", required = true, dataType = "TendererReportCondition", paramType = "body")
    @PostMapping("/exportApplyInfoList")
    public  void  exportApplyInfoList(@RequestBody @Valid TendererReportCondition condition) {
        tendererReportService.exportApplyInfoList(condition,response,getJwtUser());
    }

}
