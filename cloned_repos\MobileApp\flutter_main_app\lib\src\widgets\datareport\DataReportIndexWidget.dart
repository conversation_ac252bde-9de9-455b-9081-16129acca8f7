///***
/// * 数据报告 - 首页 - 图表形式
/// */

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import 'package:flutter_main_app/serviceLocator.dart';

import 'package:flutter_main_app/src/viewModels/dataReport/DataReportViewModel.dart';

import 'package:flutter_main_app/src/components/CustomAppBar.dart';
import 'package:flutter_main_app/src/components/CustomCircularProgressIndicator.dart';
import 'package:flutter_main_app/src/components/CustomSafeArea.dart';

import 'package:flutter_main_app/src/widgets/datareport/DataReportChart.dart';

class DataReportIndexWidget extends StatefulWidget {
  const DataReportIndexWidget({
    Key key,
  }) : super(key: key);

  /// * 路由名称
  static const String _routeName = 'DataReportIndex';
  static String get routeName => _routeName;

  @override
  _DataReportIndexWidgetState createState() => _DataReportIndexWidgetState();
}

class _DataReportIndexWidgetState extends State<DataReportIndexWidget> {

  /// * 当前年份
  final int _currentYear = DateTime.now().year;

  /// * 所有可选年份，默认为 [1949, _currentYear]
  final List<int> _years = <int>[];

  final DataReportViewModel _model = serviceLocator<DataReportViewModel>();

  @override
  void initState() {
    super.initState();

    for (int i = 1949; i <= _currentYear; i++) {
      _years.add(i);
    }

    _model.year = _currentYear - 1;

    _model.fetchReportData();
  }

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider<DataReportViewModel>.value(
          value: serviceLocator<DataReportViewModel>(),
        ),
      ],
      child: Scaffold(
        appBar: CustomAppBar(
          title: '数据报告',
          leading: Container(),
          actions: <Widget>[
            /// * 年份下拉选择按钮
            Theme(
              data: ThemeData(
                brightness: Brightness.dark,
                canvasColor: Theme.of(context).primaryColor,
              ),
              child: DropdownButtonHideUnderline(
                child: Consumer<DataReportViewModel>(
                  builder: (context, model, child) {
                    return DropdownButton(
                      value: model.year.toString() + '年',
                      style: TextStyle(
                        fontSize: 16.0,
                      ),
                      items: <DropdownMenuItem<String>>[
                        ...List<DropdownMenuItem<String>>.generate(
                          _years.length,
                          (int index) => DropdownMenuItem(
                            child: Center(
                              child: Text('${_years[index]}年'),
                            ),
                            value: '${_years[index]}年',
                          ),
                        ),
                      ],
                      onChanged: (String year) {
                        model.year = int.parse(year.replaceFirst('年', ''));
                      },
                    );
                  },
                ),
              ),
            ),
          ],
        ),
        body: Consumer<DataReportViewModel>(
          builder: (context, model, child) {
            return model.isLoading
              ? CustomCircularProgressIndicator()
              : CustomSafeArea(
                  backgroundColor: Colors.white,
                  child: DataReportChart(),
                );
          },
        ),
      ),
    );
  }

}
