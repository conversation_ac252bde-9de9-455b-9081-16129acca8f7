package com.hzw.sunflower.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hzw.sunflower.entity.Tenant;
import com.hzw.sunflower.entity.condition.TenantCondition;

import java.util.List;

/**
 * 租户表 Service接口
 *
 * <AUTHOR>
 * @version 1.0.0 2021-04-07
 */
public interface TenantService {
    /**
     * 根据条件分页查询租户表 列表
     *
     * @param condition 查询条件
     * @return 分页信息
     */
    IPage<Tenant> findTenantByCondition(TenantCondition condition);

    /**
     * 根据主键ID查询租户表 信息
     *
     * @param id 主键ID
     * @return 租户表 信息
     */
    Tenant getTenantById(Long id);

    /**
     * 新增租户表 信息
     *
     * @param tenant 租户表 信息
     * @return 是否成功
     */
    Boolean addTenant(Tenant tenant);

    /**
     * 修改租户表 信息
     *
     * @param tenant 租户表 信息
     * @return 是否成功
     */
    Boolean updateTenant(Tenant tenant);

    /**
     * 根据主键ID删除租户表
     *
     * @param id 主键ID
     * @return 是否成功
     */
    Boolean deleteTenantById(Long id);

    /**
     * 根据主键ID列表批量删除租户表
     *
     * @param idList 主键ID列表
     * @return 是否成功
     */
    Boolean deleteTenantByIds(List<Long> idList);
}