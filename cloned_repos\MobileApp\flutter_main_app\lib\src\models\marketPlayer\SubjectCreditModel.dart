///***
/// * 主体信用 模型
/// */

import 'package:json_annotation/json_annotation.dart';

part 'SubjectCreditModel.g.dart';

@JsonSerializable(explicitToJson: true)
class SubjectCreditModel {

  SubjectCreditModel({
    this.active,
    this.approvalDate,
    this.approvalInstitution,
    this.caNoDataKey,
    this.checkContents,
    this.contents,
    this.createdDate,
    this.creditBadType,
    this.creditId,
    this.creditNo,
    this.creditRank,
    this.creditType,
    this.dataKey,
    this.expiryDate,
    this.modifiedDate,
    this.otherExpiryDate,
    this.punishInstitution,
    this.serverity,
    this.statusValue,
    this.subjectDataKey,
    this.subjectID,
    this.subjectType,
    this.validDate,
    this.year,
  });

  final bool active;

  final String approvalDate;

  final String approvalInstitution;

  final String caNoDataKey;

  final String checkContents;

  final String contents;

  final String createdDate;

  final String creditBadType;

  final int creditId;

  final String creditNo;

  final String creditRank;

  final int creditType;

  final String dataKey;

  final String expiryDate;

  final String modifiedDate;

  final String otherExpiryDate;

  final String punishInstitution;

  final String serverity;

  final int statusValue;

  final String subjectDataKey;

  final int subjectID;

  final String subjectType;

  final String validDate;

  final String year;

  factory SubjectCreditModel.fromJson(Map<String, dynamic> json) => _$SubjectCreditModelFromJson(json);

  Map<String, dynamic> toJson() => _$SubjectCreditModelToJson(this);

}
