package com.hzw.sunflower.service.impl;

import com.hzw.sunflower.common.redis.RedisCache;
import com.hzw.sunflower.constant.EntrustConstants;
import com.hzw.sunflower.controller.project.request.ReturnListConditionReq;
import com.hzw.sunflower.dao.WaitRefundMapper;
import com.hzw.sunflower.entity.JwtUser;
import com.hzw.sunflower.service.WaitRefundCountService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

@Service
public class WaitRefundCountServiceImpl implements WaitRefundCountService {


    @Autowired
    private WaitRefundMapper waitRefundMapper;

    @Autowired
    private RedisCache redisCache;


    /**
     * 查询退回待处理事项的数量
     * @param condition
     * @param jwtUser
     * @return
     */
    public Integer getReturnListCount(ReturnListConditionReq condition, JwtUser jwtUser, String datascopesql) {
        Integer count = 0;

        //采购公告退回数量
        count += waitRefundMapper.getTenderBidCount(datascopesql,condition);

        //采购文件退回数量
        count += waitRefundMapper.getTenderDocCount(datascopesql,condition);

        //更正公告数量
        count += waitRefundMapper.getSupplementBidCount(datascopesql,condition);

        //澄清修改数量
        count += waitRefundMapper.getClarifyBidCount(datascopesql,condition);

        //异议回复数量
        count += waitRefundMapper.getObjectionCount(datascopesql,condition);

        //确认中标候选人退回数量
        count += waitRefundMapper.getWinBidCount(datascopesql,condition);

        //资格预审退回数量
        count += waitRefundMapper.getwinBidZgysCount(datascopesql,condition);

        //候选人公示数量
        count += waitRefundMapper.getWinningBidCandidateCount(datascopesql,condition);

        //中标结果公示数量
        count += waitRefundMapper.getWinCandidateBidCount(datascopesql,condition);

        //中标通知书数量
        count += waitRefundMapper.getWinNoticeCount(datascopesql,condition);

        //重新申请中标通知书数量
        count += waitRefundMapper.getReWinNoticeCount(datascopesql,condition);

        //资格预审通知书数量
        count += waitRefundMapper.getBidWinNoticePreCount(datascopesql,condition);

        //重新申请资格预审通知书数量
        count += waitRefundMapper.getReBidWinNoticePreCount(datascopesql,condition);

        //常公告退回、撤回退回数量
        count += waitRefundMapper.getAbnormalNoticeCount(datascopesql,condition);

        //归档退回数量
        count += waitRefundMapper.getArchiveCount(datascopesql,condition);

        //材料申请退回数量
        count += waitRefundMapper.getMaterialApplicationCount(datascopesql,condition);

        //用锁申请退回数量
        count += waitRefundMapper.getWithLockCount(datascopesql,condition);

        //换专票申请退回数量
        count += waitRefundMapper.getSpecialTicketChangeCount(datascopesql,condition);

        //推标书费申请退回数量
        count += waitRefundMapper.getTenderFeesApplyCount(datascopesql,condition);

        //登记供应商退回数量
        count += waitRefundMapper.getRegisteredSuppliersCount(datascopesql,condition);

        //补录付款凭证退回数量
        count += waitRefundMapper.getRerecordPaymentCount(datascopesql,condition);

        //代理服务费修改退回数量
        count += waitRefundMapper.getAgencyServiceCount(datascopesql,condition);

        //数据修改退回数量
        count += waitRefundMapper.getDataModificationCount(datascopesql,condition);

        //保证金退还退回数量
        count += waitRefundMapper.getDepositRefundCount(datascopesql,condition);

        //专家抽取退回数量
        count += waitRefundMapper.getExpertExtractionCount(datascopesql,condition);

        //专家抽取备案退回数量
        count += waitRefundMapper.getExpertsSelectedRecordCount(datascopesql,condition);

        //存入缓存，有效时间10秒
        redisCache.setCacheObject(EntrustConstants.RETURN_LIST_Count_CODE+jwtUser.getUserId(), count, EntrustConstants.RETURN_LIST_VALID_TIME, TimeUnit.SECONDS);

        return count;
    }

}
