package com.hzw.sunflower.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzw.sunflower.common.Paging;
import com.hzw.sunflower.constant.CommonConstants;
import com.hzw.sunflower.constant.constantenum.*;
import com.hzw.sunflower.controller.request.*;
import com.hzw.sunflower.controller.response.*;
import com.hzw.sunflower.dao.BondMapper;
import com.hzw.sunflower.dao.BondRefundMapper;
import com.hzw.sunflower.dao.BondRefundWaterMapper;
import com.hzw.sunflower.dto.BondDetailDTO;
import com.hzw.sunflower.dto.BondProjectDTO;
import com.hzw.sunflower.dto.CommonApplyInfoDto;
import com.hzw.sunflower.dto.ProcessRecordDTO;
import com.hzw.sunflower.entity.*;
import com.hzw.sunflower.entity.condition.BondProjectCondition;
import com.hzw.sunflower.entity.condition.BondRefundCondition;
import com.hzw.sunflower.exception.ParamsNotNullException;
import com.hzw.sunflower.exception.WorkFlowException;
import com.hzw.sunflower.freesia.client.api.FlowApiClient;
import com.hzw.sunflower.freesia.client.constant.FlowClientConstant;
import com.hzw.sunflower.freesia.client.vo.flowable.ReturnVo;
import com.hzw.sunflower.service.*;
import com.hzw.sunflower.util.*;
import com.hzw.sunflower.util.oss.OssFileStorage;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 保证金退还项目表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-27
 */
@Service
public class BondRefundWaterServiceImpl extends ServiceImpl<BondRefundWaterMapper, BondRefundWater> implements BondRefundWaterService {


}
