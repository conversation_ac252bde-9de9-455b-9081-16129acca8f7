package com.hzw.sunflower.controller.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@ApiModel(description = "日程金额统计")
@Data
public class ScheduleAmountVO {

    @ApiModelProperty(value = "本年委托金额")
    private BigDecimal yearEntrustAmount;

    @ApiModelProperty(value = "本年预收入金额")
    private BigDecimal yearToIncomeAmount;

    @ApiModelProperty(value = "本月委托金额")
    private BigDecimal monthEntrustAmount;

    @ApiModelProperty(value = "本月预收入金额")
    private BigDecimal monthToIncomeAmount;

    @ApiModelProperty(value = "本日委托金额")
    private BigDecimal dayEntrustAmount;

    @ApiModelProperty(value = "本日预收入金额")
    private BigDecimal dayToIncomeAmount;

}
