package com.hzw.ssm.sys.call.entity;

/**
 * 接收语音回复信息实体类
 * <AUTHOR>
 *
 */
public class VoiceBodyEntity {
	/**
	 * 业务关键字
	 */
	public String serviceKey;
	/**
	 * 呼叫中心callId (保证唯一) 
	 */
	public String callId;
	/**
	 * 被叫电话
	 */
	public String calledNum;
	/**
	 * 被叫显示号码
	 */
	public String calledDisplayNumber;
	/**
	 * 被叫话单流水号
	 */
	public String calledStreamNo;
	/**
	 * 被叫应答时间
	 */
	public String startCalledTime;
	/**
	 * 被叫通话结束时间
	 */
	public String stopCalledTime;
	/**
	 * 被叫通话时长
	 */
	public String calledDuration;
	/**
	 * 被叫通话费用
	 */
	public String calledCost;
	/**
	 * 被叫结束原因（合并通话状态原因）
		1、正常接通
		2、呼叫遇忙；[通过信令]
		3、用户不在服务区；[通过信令]
		4、用户无应答；[通过信令]
		5、用户关机；[通过信令]
		6、空号；
		7、停机；
		8、号码过期
		9、主叫应答，被叫应答前挂机
		10 、正在通话中
		11、 拒接
		99、其他
		20：主动取消呼叫
	 */
	public String calledRelCause;
	/**
	 * 被叫结束的原始原因值（信令层面
	 */
	public String calledOriRescode;
	/**
	 * 计费号码 
	 */
	public String chargeNumber;
	/**
	 * 被叫释放Reason
	 */
	public String calledRelReason;
	/**
	 * 
	 */
	public String callOutTime;
	/**
	 * 媒体服务器名称
	 */
	public String msServer;
	/**
	 * 计费时长 单位秒
	 */
	public String duration;
	/**
	 * 计费数量
	 */
	public String costCount;
	/**
	 * 企业id
	 */
	public String vccId;
	/**
	 * 按键
	 */
	public String dtmfKey;

	public String getServiceKey() {
		return serviceKey;
	}
	public void setServiceKey(String serviceKey) {
		this.serviceKey = serviceKey;
	}
	public String getCallId() {
		return callId;
	}
	public void setCallId(String callId) {
		this.callId = callId;
	}
	public String getCalledNum() {
		return calledNum;
	}
	public void setCalledNum(String calledNum) {
		this.calledNum = calledNum;
	}
	public String getCalledDisplayNumber() {
		return calledDisplayNumber;
	}
	public void setCalledDisplayNumber(String calledDisplayNumber) {
		this.calledDisplayNumber = calledDisplayNumber;
	}
	public String getCalledStreamNo() {
		return calledStreamNo;
	}
	public void setCalledStreamNo(String calledStreamNo) {
		this.calledStreamNo = calledStreamNo;
	}
	public String getStartCalledTime() {
		return startCalledTime;
	}
	public void setStartCalledTime(String startCalledTime) {
		this.startCalledTime = startCalledTime;
	}
	public String getStopCalledTime() {
		return stopCalledTime;
	}
	public void setStopCalledTime(String stopCalledTime) {
		this.stopCalledTime = stopCalledTime;
	}
	public String getCalledDuration() {
		return calledDuration;
	}
	public void setCalledDuration(String calledDuration) {
		this.calledDuration = calledDuration;
	}
	public String getCalledCost() {
		return calledCost;
	}
	public void setCalledCost(String calledCost) {
		this.calledCost = calledCost;
	}
	public String getCalledRelCause() {
		return calledRelCause;
	}
	public void setCalledRelCause(String calledRelCause) {
		this.calledRelCause = calledRelCause;
	}
	public String getCalledOriRescode() {
		return calledOriRescode;
	}
	public void setCalledOriRescode(String calledOriRescode) {
		this.calledOriRescode = calledOriRescode;
	}
	public String getChargeNumber() {
		return chargeNumber;
	}
	public void setChargeNumber(String chargeNumber) {
		this.chargeNumber = chargeNumber;
	}
	public String getCalledRelReason() {
		return calledRelReason;
	}
	public void setCalledRelReason(String calledRelReason) {
		this.calledRelReason = calledRelReason;
	}
	public String getCallOutTime() {
		return callOutTime;
	}
	public void setCallOutTime(String callOutTime) {
		this.callOutTime = callOutTime;
	}
	public String getMsServer() {
		return msServer;
	}
	public void setMsServer(String msServer) {
		this.msServer = msServer;
	}
	public String getDuration() {
		return duration;
	}
	public void setDuration(String duration) {
		this.duration = duration;
	}
	public String getCostCount() {
		return costCount;
	}
	public void setCostCount(String costCount) {
		this.costCount = costCount;
	}
	public String getVccId() {
		return vccId;
	}
	public void setVccId(String vccId) {
		this.vccId = vccId;
	}
	public String getDtmfKey() {
		return dtmfKey;
	}
	public void setDtmfKey(String dtmfKey) {
		this.dtmfKey = dtmfKey;
	}
}
