package com.hzw.ssm.webService.output;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElements;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;

import com.hzw.ssm.webService.output.impl.ExtractExpertOutput;
import com.hzw.ssm.webService.output.impl.ObtainExpertOutput;

/**
 * 输入根节点
 * <AUTHOR>
 *
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "root")
@XmlType(name = "root", propOrder = { "head", "body" })
public class RootOutput {
	/**
	 * 公共参数部分
	 */
	@XmlElement
	private HeadOutput head;
	/**
	 * 核心请求部分 
	 */
	@XmlElements({
		@XmlElement(name="body",type=ExtractExpertOutput.class),
		@XmlElement(name="body",type=ObtainExpertOutput.class)
	})
	private BodyOutput body;
	public HeadOutput getHead() {
		return head;
	}
	public void setHead(HeadOutput head) {
		this.head = head;
	}
	public BodyOutput getBody() {
		return body;
	}
	public void setBody(BodyOutput body) {
		this.body = body;
	}
	
	
}
