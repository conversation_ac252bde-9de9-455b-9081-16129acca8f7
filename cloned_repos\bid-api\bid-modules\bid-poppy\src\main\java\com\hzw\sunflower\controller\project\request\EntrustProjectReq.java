package com.hzw.sunflower.controller.project.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date ：Created in 2021/4/12 15:50
 * @description：委托项目信息
 * @modified By：`
 * @version: 1.0
 */
@ApiModel(description = "委托项目类")
@Data
public class EntrustProjectReq {

    @ApiModelProperty(value = "委托项目编号", position = 1)
    private String projectNumber;

    @ApiModelProperty(value = "项目id", position = 2)
    private Long projectId;
}
