package com.hzw.sunflower.controller.response;

import com.hzw.sunflower.controller.request.PurchaseTypeReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@ApiModel(description = "项目详细信息设置详情")
@Data
public class ProjectDetailsVo {

    @ApiModelProperty(value = "采购方式")
    private List<PurchaseTypeReq> purchaseTypeReqList;

    @ApiModelProperty(value = "字段必填及规则")
    private List<ProjectColumnRequiredVO> projectColumnRequired;

}
