package com.hzw.sunflower.controller.project.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 项目委托关系人表 请求实体
 *
 * <AUTHOR>
 * @version 1.0.0 2021-04-12
 */
@ApiModel(description = "项目委托关系人表 ")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProjectEntrustUserREQ implements Serializable {

    @ApiModelProperty(value = "项目id", position = 1)
    private Long projectId;

    @ApiModelProperty(value = "委托合同id", position = 2)
    private String uploadFileId;

    @ApiModelProperty(value = "委托单位id", position = 3)
    private Long companyId;

    @ApiModelProperty(value = "委托单位名称", position = 4)
    private String companyName;

    @ApiModelProperty(value = "委托联系信息", position = 5)
    private List<AgentUserReq> agentUser;

}
