<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzw.sunflower.dao.SealRoleMapper">

    <select id="queryNextRole" resultType="com.hzw.sunflower.entity.User">
        select
            t1.id,
            t1.user_name,
            t1.other_user_id
        from
            t_user t1
        left join t_user_identity t2 on t2.user_id = t1.id
        left join r_depart_role t3 on t3.user_id = t1.id
        left join t_role t4 on t4.id = t3.role_id
        left join t_seal_role t5 on t4.id = t5.role_id
        where
            t1.is_delete = 0
            and t2.is_delete = 0
            and t3.is_delete = 0
            and t4.is_delete = 0
            and t5.is_delete = 0
            and t2.status = 1
            and t2.`identity` = 2
            and t4.role_category = 2
            and t5.business_code = #{businessCode}
        order by t5.sort
    </select>
    <select id="queryFGLD" resultType="com.hzw.sunflower.entity.User">
        select distinct
            t3.id,
            t3.user_name,
            t3.other_user_id
        from
            t_role_permission_depart t1
        left join r_depart_role t2 on t1.role_id = t2.role_id
        left join t_user t3 on t3.id = t2.user_id
        left join t_user_identity t4 on t4.user_id = t3.id
        left join t_role t5 on t5.id = t2.role_id
        left join t_seal_role t6 on t5.id = t6.role_id
        where
            t2.is_delete = 0
            and t3.is_delete = 0
            and t4.is_delete = 0
            and t5.is_delete = 0
            and t6.is_delete = 0
            and t4.`identity` = 2
            and t5.role_category = 2
            and t6.business_code = #{seal}
            and t1.depart_id = #{departId}

    </select>
    <select id="querySealLeader" resultType="com.hzw.sunflower.dto.SealRoleDTO">
        select
            tsr.id,
            tsr.role_code,
            tsr.sort,
            tsr.user_id,
            tsr.company_id,
            tu.user_name
        from
            t_seal_role tsr
        left join t_user tu on tsr.user_id = tu.id
        where tsr.is_delete = 0
            and tu.is_delete = 0
            and tsr.company_id = #{companyId}
            and tsr.business_code = #{sealLeader}
        order by tsr.sort asc
    </select>
</mapper>