package com.hzw.sunflower.constant.constantenum;

/**
 * <AUTHOR>
 * @date ：Created in 2022/07/27 9:23
 * @description：退还申请状态
 * @version: 1.0
 */
public enum ApplyRelationStatusEnum {
    //申请关联状态（1.待处理 2.已退回 3.已处理）
    WAIT_HANDLE(1, "待处理"),
    RETRUNED(2, "已退回"),
    HANDLED(3, "已处理");

    private Integer type;
    private String desc;

    ApplyRelationStatusEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
