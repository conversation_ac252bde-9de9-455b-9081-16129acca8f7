package com.hzw.sunflower.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hzw.sunflower.controller.project.request.AgentMyProjectREQ;
import com.hzw.sunflower.controller.project.request.ApplyInfoConditionReq;
import com.hzw.sunflower.controller.project.request.ProjectAcceptanceRep;
import com.hzw.sunflower.controller.project.request.ProjectConditionRep;
import com.hzw.sunflower.controller.project.request.bidder.BidRePurchaseReq;
import com.hzw.sunflower.controller.project.request.bidder.LenovoTenantUserReq;
import com.hzw.sunflower.controller.project.request.bidder.MyselfProjectConditionReq;
import com.hzw.sunflower.controller.project.response.ProjectAcceptanceListVo;
import com.hzw.sunflower.controller.project.response.ProjectBidSectionVO;
import com.hzw.sunflower.controller.project.response.ProjectVo;
import com.hzw.sunflower.controller.project.response.WaitListVo;
import com.hzw.sunflower.controller.project.response.bidder.LenovoTenantUserVo;
import com.hzw.sunflower.controller.project.response.bidder.ReSectionVo;
import com.hzw.sunflower.dto.ApplyInfoListDTO;
import com.hzw.sunflower.dto.MyProjectListDTO;
import com.hzw.sunflower.dto.ProjectListDTO;
import com.hzw.sunflower.dto.ProjectSectionGroupDTO;
import com.hzw.sunflower.dto.bidder.BidderProjectCondition;
import com.hzw.sunflower.entity.Project;
import com.hzw.sunflower.entity.ProjectBidSection;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 项目表 Mapper接口
 *
 * <AUTHOR>
 * @version 1.0.0 2021-04-07
 */
public interface ProjectMapper extends BaseMapper<Project> {

    /**
     * 根据条件分页查询项目表 列表
     *
     * @param page
     * @param condition
     * @return
     */
    IPage<ProjectListDTO> queryProjectByPage(IPage<ProjectListDTO> page, @Param("condition") ProjectConditionRep condition);

    IPage<ApplyInfoListDTO> queryApplyInfoByUserId(IPage<ApplyInfoListDTO> page, @Param("condition") ApplyInfoConditionReq condition, @Param("userId")Long userId);

    /**
     * 根据条件分页查询项目表 列表
     *
     * @param page
     * @param condition
     * @return
     */
    IPage<ProjectListDTO> queryBidderProjectByPage(IPage<ProjectListDTO> page, @Param("condition") BidderProjectCondition condition);


    /**
     * 根据条件分页委托项目 列表
     *
     * @param page
     * @param condition
     * @return
     */
    IPage<ProjectListDTO> queryBidderEntrustProjectByPage(IPage<ProjectListDTO> page, @Param("condition") BidderProjectCondition condition);

    /**
     * 根据条件查询我的项目（包含授权给我的）
     * @param page
     * @param condition
     * @return
     */
    IPage<MyProjectListDTO> queryListMyProjectByPage(IPage<MyProjectListDTO> page, @Param("condition") AgentMyProjectREQ condition);

    /**
     * 根据条件查询我的项目（不包含授权给我的）
     * @param page
     * @param condition
     * @return
     */
    IPage<MyProjectListDTO> queryListOnlyMyProjectByPage(IPage<MyProjectListDTO> page, @Param("condition") AgentMyProjectREQ condition);


    /**
     * 专家库 查询项目集合
     * @return
     */
   List<ProjectVo> getProjectListByExpert(@Param("keyWords") String keyWords,@Param("datascope") String datascopesql);

    /**
     * 专家库 根据项目信息查看标段数据
     * @param projectId
     * @return
     */
    List<ProjectBidSection> getSectionListByExpert(@Param("projectId") Long projectId);


    /**
     * 根据标段集合删除关注供应商关注标段信息
     * @param subList
     * @return
     */
    Integer deleteApplyInfoBySubId(@Param("subList") List<Long> subList);

    /**
     * 根据项目分摊情况查询项目id
     * @param isShare
     * @param ids
     * @return
     */
    List<Long> findShareProject(@Param("isShare") Integer isShare,@Param("ids") List<Long> ids);


    /**
     * 删除招标文件提交的信息
     * @param ids
     */
    void deleteProjectBidDoc(@Param("ids") List<Long> ids);


    /**
     * 删除招标公告
     * @param ids
     */
    void deleteProjectBidNotice(@Param("ids") List<Long> ids);

    /**
     * 删除招标文件关系表提交的信息
     * @param ids
     */
    void deleteProjectBidDocRelation(@Param("ids") List<Long> ids);

    /**
     * 删除招标公告关系表提交的信息
     * @param ids
     */
    void deleteProjectBidNoticeRelation(@Param("ids") List<Long> ids);

    List<Long> findDocId(@Param("section") ProjectBidSection section);

    List<Long> findNoticeId(@Param("section") ProjectBidSection section);

    /**
     * 根据项目id、标段id查询套信息
     * @param projectId
     * @param sectionId
     * @return
     */
    List<ProjectSectionGroupDTO> getSectionGroup(@Param("projectId") Long projectId,@Param("sectionId") Long sectionId);

    /**
     * 删除套信息
     * @param id
     */
    Boolean deleteGroupById(@Param("groupId") Long id);

    /**
     * 修改套信息
     * @param group
     */
    Boolean updateGroupInfo(@Param("group") ProjectSectionGroupDTO group);

    /**
     * 查询已报名待审核供应商数量
     * @param projectId
     */
    Integer countApplyInfoByProjectId(@Param("projectId") Long projectId);

    /**
     * 查询代办事项列表
     * @param page
     * @param condition
     * @return
     */
    IPage<WaitListVo> queryWaitList(Page<WaitListVo> page,@Param("condition") ApplyInfoConditionReq condition,@Param("dataScope")String dataScope);

    /**
     * 获取最大的临时国际表编号
     * @return
     */
    Integer getMaxTempInternational();


    //List<WaitListVo> selectWaitList(@Param("condition") ApplyInfoConditionReq condition,@Param("dataScope") String datascopesql);

    List<WaitListVo> findCount(@Param("condition")ApplyInfoConditionReq condition, @Param("dataScope")String dataScope);

    IPage<WaitListVo> queryWaitList1(Page<WaitListVo> page, @Param("condition")ApplyInfoConditionReq condition,@Param("dataScope") String datascopesql);

    List<WaitListVo> findCount1(@Param("condition")ApplyInfoConditionReq condition,@Param("dataScope") String datascopesql);

    IPage<WaitListVo> queryReturnList(Page<WaitListVo> page, @Param("condition") ApplyInfoConditionReq condition, @Param("dataScope")String datascopesql, @Param("userId") Long userId);

    List<WaitListVo> findReturnCount(ApplyInfoConditionReq condition,@Param("dataScope") String datascopesql, @Param("userId")Long userId);

    List<ProjectBidSectionVO> listDoc(Long projectId);

    /**
     * 查询部门code
     * @param bookkeepId
     * @return
     */
    String selectDepartmentCodeByBookkeepId(Long bookkeepId);


    /**
     * 运管处审核列表
     * @param page
     * @return
     */
    IPage<ProjectAcceptanceListVo> selectYgcProjectList(IPage<ProjectAcceptanceListVo> page,@Param("rep") ProjectAcceptanceRep rep);


    /**
     * 处长项目经理审批列表
     * @param page
     * @return
     */
    IPage<ProjectAcceptanceListVo> selectAcceptanceProjectList(IPage<ProjectAcceptanceListVo> page,@Param("rep") ProjectAcceptanceRep rep);

    /**
     * 查询自采项目列表
     * @param customizeBuildPage
     * @param condition
     * @return
     */
    IPage<ProjectListDTO> queryMyselfProjectByPage(Page<Object> customizeBuildPage, @Param("condition") MyselfProjectConditionReq condition);

    /**
     * 查询可重新招标的标段集合
     * @param req
     * @return
     */
    List<ReSectionVo> queryReSectionList(@Param("req") BidRePurchaseReq req);

    /**
     * 公司搜索
     * @param req
     * @return
     */
    List<LenovoTenantUserVo> queryTenantInfoByCompany(@Param("req") LenovoTenantUserReq req);

    /**
     * 部门搜索
     * @param req
     * @return
     */
    List<LenovoTenantUserVo> queryTenantInfoByDepartment( @Param("req") LenovoTenantUserReq req);

    /**
     * 人员搜索
     * @param req
     * @return
     */
    List<LenovoTenantUserVo> queryTenantInfoByUser( @Param("req") LenovoTenantUserReq req);

    /**
     * 根据项目id查询租户用户id
     * @param projectId
     * @return
     */
    Long queryTenantInfoByProject(@Param("projectId")Long projectId);


    IPage<MyProjectListDTO> queryListSeal(IPage<MyProjectListDTO> page, @Param("condition") AgentMyProjectREQ condition);

    /**
     * 根据标段id查询项目信息
     * @param sectionId
     * @return
     */
    Project getProjectBySectionId(@Param("sectionId")Long sectionId);
}