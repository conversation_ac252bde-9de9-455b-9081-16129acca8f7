import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/services.dart';

import 'package:flutter_main_app/src/app.dart';

void main() {
  // const SystemUiOverlayStyle light = SystemUiOverlayStyle(
  //   systemNavigationBarColor: Color(0xFF000000),
  //   systemNavigationBarDividerColor: null,
  //   statusBarColor: null,
  //   systemNavigationBarIconBrightness: Brightness.light,
  //   statusBarIconBrightness: Brightness.light,
  //   statusBarBrightness: Brightness.light,
  // );

  runApp(_widgetForRoute('root'));

  SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle.light);
} 

//**
// * 主入口
// */
Widget _widgetForRoute(String route) {
  switch (route) {
    case 'root':
      return MainApp();
    default:
      return Center(
        child: Text('Unknown route: $route', textDirection: TextDirection.ltr),
      );
  }
}
