#!/usr/bin/env python3
"""
仓库更新脚本
功能：
1. 遍历所有已克隆的仓库
2. 更新每个仓库的所有分支
3. 处理合并冲突和错误
4. 生成详细的更新报告
"""

import os
import subprocess
import sys
import json
from pathlib import Path
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('update_repos.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

def run_command(command, cwd=None):
    """执行命令并返回结果"""
    try:
        result = subprocess.run(
            command, 
            shell=True, 
            cwd=cwd,
            capture_output=True, 
            text=True, 
            encoding='utf-8'
        )
        return result.returncode == 0, result.stdout, result.stderr
    except Exception as e:
        logger.error(f"执行命令失败: {command}, 错误: {e}")
        return False, "", str(e)

def get_current_branch(repo_path):
    """获取当前分支名"""
    success, stdout, stderr = run_command("git branch --show-current", cwd=repo_path)
    if success:
        return stdout.strip()
    return None

def get_all_branches(repo_path):
    """获取所有本地分支"""
    success, stdout, stderr = run_command("git branch", cwd=repo_path)
    if not success:
        return []
    
    branches = []
    for line in stdout.strip().split('\n'):
        branch = line.strip().replace('* ', '')
        if branch and not branch.startswith('('):
            branches.append(branch)
    return branches

def get_remote_branches(repo_path):
    """获取所有远程分支"""
    success, stdout, stderr = run_command("git branch -r", cwd=repo_path)
    if not success:
        return []
    
    remote_branches = []
    for line in stdout.strip().split('\n'):
        line = line.strip()
        if line and not line.startswith('origin/HEAD'):
            branch = line.replace('origin/', '')
            if branch:
                remote_branches.append(branch)
    return remote_branches

def fetch_updates(repo_path):
    """获取远程更新"""
    logger.info(f"获取远程更新...")
    success, stdout, stderr = run_command("git fetch --all", cwd=repo_path)
    if not success:
        logger.warning(f"获取远程更新失败: {stderr}")
        return False
    return True

def update_branch(repo_path, branch_name):
    """更新指定分支"""
    logger.info(f"更新分支: {branch_name}")
    
    # 切换到指定分支
    success, stdout, stderr = run_command(f"git checkout {branch_name}", cwd=repo_path)
    if not success:
        logger.warning(f"切换到分支 {branch_name} 失败: {stderr}")
        return False, f"切换失败: {stderr}"
    
    # 检查是否有未提交的更改
    success, stdout, stderr = run_command("git status --porcelain", cwd=repo_path)
    if success and stdout.strip():
        logger.warning(f"分支 {branch_name} 有未提交的更改，跳过更新")
        return False, "有未提交的更改"
    
    # 拉取更新
    success, stdout, stderr = run_command(f"git pull origin {branch_name}", cwd=repo_path)
    if not success:
        if "merge conflict" in stderr.lower() or "conflict" in stderr.lower():
            return False, f"合并冲突: {stderr}"
        else:
            return False, f"拉取失败: {stderr}"
    
    # 检查是否有更新
    if "Already up to date" in stdout or "Already up-to-date" in stdout:
        return True, "已是最新"
    else:
        return True, "更新成功"

def update_repository(repo_name, repo_path):
    """更新单个仓库"""
    logger.info(f"开始更新仓库: {repo_name}")
    
    if not os.path.exists(repo_path):
        logger.error(f"仓库路径不存在: {repo_path}")
        return {
            'repo_name': repo_name,
            'status': 'error',
            'message': '仓库路径不存在',
            'branches': {}
        }
    
    # 检查是否是Git仓库
    if not os.path.exists(os.path.join(repo_path, '.git')):
        logger.error(f"不是有效的Git仓库: {repo_path}")
        return {
            'repo_name': repo_name,
            'status': 'error',
            'message': '不是有效的Git仓库',
            'branches': {}
        }
    
    # 获取远程更新
    if not fetch_updates(repo_path):
        return {
            'repo_name': repo_name,
            'status': 'error',
            'message': '获取远程更新失败',
            'branches': {}
        }
    
    # 获取当前分支
    current_branch = get_current_branch(repo_path)
    
    # 获取所有本地分支
    local_branches = get_all_branches(repo_path)
    remote_branches = get_remote_branches(repo_path)
    
    logger.info(f"本地分支: {local_branches}")
    logger.info(f"远程分支: {remote_branches}")
    
    # 更新结果
    update_result = {
        'repo_name': repo_name,
        'status': 'success',
        'message': '更新完成',
        'branches': {},
        'current_branch': current_branch
    }
    
    # 检出新的远程分支
    for remote_branch in remote_branches:
        if remote_branch not in local_branches:
            logger.info(f"检出新分支: {remote_branch}")
            success, stdout, stderr = run_command(
                f"git checkout -b {remote_branch} origin/{remote_branch}", 
                cwd=repo_path
            )
            if success:
                local_branches.append(remote_branch)
                update_result['branches'][remote_branch] = {
                    'status': 'new_branch',
                    'message': '新检出的分支'
                }
            else:
                update_result['branches'][remote_branch] = {
                    'status': 'error',
                    'message': f'检出新分支失败: {stderr}'
                }
    
    # 更新所有本地分支
    for branch in local_branches:
        success, message = update_branch(repo_path, branch)
        update_result['branches'][branch] = {
            'status': 'success' if success else 'error',
            'message': message
        }
    
    # 回到原来的分支
    if current_branch and current_branch in local_branches:
        run_command(f"git checkout {current_branch}", cwd=repo_path)
    
    return update_result

def save_update_report(results, report_file='update_report.json'):
    """保存更新报告"""
    report = {
        'update_time': datetime.now().isoformat(),
        'total_repos': len(results),
        'successful_repos': len([r for r in results if r['status'] == 'success']),
        'failed_repos': len([r for r in results if r['status'] == 'error']),
        'results': results
    }
    
    try:
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        logger.info(f"更新报告已保存到: {report_file}")
    except Exception as e:
        logger.error(f"保存更新报告失败: {e}")

def main():
    """主函数"""
    logger.info("开始执行仓库更新脚本")
    
    base_dir = 'cloned_repos'
    
    if not os.path.exists(base_dir):
        logger.error(f"克隆目录不存在: {base_dir}")
        logger.info("请先运行 clone_repos.py 克隆仓库")
        return
    
    # 获取所有仓库目录
    repo_dirs = [d for d in os.listdir(base_dir) 
                 if os.path.isdir(os.path.join(base_dir, d))]
    
    if not repo_dirs:
        logger.error("未找到任何仓库目录")
        return
    
    logger.info(f"找到 {len(repo_dirs)} 个仓库目录")
    
    # 询问是否继续
    print(f"\n找到 {len(repo_dirs)} 个仓库，是否继续更新？(y/n): ", end='')
    choice = input().strip().lower()
    
    if choice != 'y':
        logger.info("用户取消更新操作")
        return
    
    # 更新所有仓库
    results = []
    success_count = 0
    error_count = 0
    
    for i, repo_name in enumerate(repo_dirs, 1):
        logger.info(f"\n[{i}/{len(repo_dirs)}] 处理仓库: {repo_name}")
        repo_path = os.path.join(base_dir, repo_name)
        
        try:
            result = update_repository(repo_name, repo_path)
            results.append(result)
            
            if result['status'] == 'success':
                success_count += 1
                logger.info(f"✅ {repo_name} 更新成功")
            else:
                error_count += 1
                logger.error(f"❌ {repo_name} 更新失败: {result['message']}")
                
        except Exception as e:
            error_count += 1
            logger.error(f"❌ {repo_name} 更新异常: {e}")
            results.append({
                'repo_name': repo_name,
                'status': 'error',
                'message': f'更新异常: {str(e)}',
                'branches': {}
            })
    
    # 输出统计信息
    logger.info(f"\n更新完成统计:")
    logger.info(f"总仓库数: {len(repo_dirs)}")
    logger.info(f"成功更新: {success_count}")
    logger.info(f"更新失败: {error_count}")
    
    if error_count > 0:
        failed_repos = [r['repo_name'] for r in results if r['status'] == 'error']
        logger.info(f"失败的仓库: {', '.join(failed_repos)}")
    
    # 保存更新报告
    save_update_report(results)
    
    logger.info("仓库更新脚本执行完成")

if __name__ == "__main__":
    main()
