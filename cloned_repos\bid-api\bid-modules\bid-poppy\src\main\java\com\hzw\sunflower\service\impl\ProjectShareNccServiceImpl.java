package com.hzw.sunflower.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzw.sunflower.constant.constantenum.BookKeepConfirmTypeEnum;
import com.hzw.sunflower.controller.project.request.AgentProjectReq;
import com.hzw.sunflower.dao.ProjectShareNccMapper;
import com.hzw.sunflower.dto.ProjectShareDTO;
import com.hzw.sunflower.entity.ProjectShareNcc;
import com.hzw.sunflower.service.ProjectShareNccService;
import com.hzw.sunflower.util.BeanListUtil;
import com.hzw.sunflower.util.LoginUtil;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ProjectShareNccServiceImpl extends ServiceImpl<ProjectShareNccMapper, ProjectShareNcc> implements ProjectShareNccService {


    @Override
    public void updateProjectShareNccList(AgentProjectReq agentProject,List<ProjectShareDTO> allShareList) {
        // 先去删除之前的数据
        QueryWrapper<ProjectShareNcc> qw = new QueryWrapper<>();
        qw.lambda().eq(ProjectShareNcc::getProjectId,agentProject.getProjectId());
        this.getBaseMapper().delete(qw);
        // 分摊的项目按照页面传入的新增
        //if(ProjectShareEnum.YES.getType().equals(isShare)){
        if(null != agentProject.getProjectShareNccList() && agentProject.getProjectShareNccList().size() > 0 && null != agentProject.getProjectShareNccList().get(0).getShareDepartment()){
            for (ProjectShareNcc projectShareNcc : agentProject.getProjectShareNccList()) {
                projectShareNcc.setProjectId(agentProject.getProjectId());
                this.save(projectShareNcc);
                //过滤分摊人是自己的情况
                if(null != projectShareNcc.getShareUser() && !projectShareNcc.getShareUser().equals(LoginUtil.getJwtUser().getUserId())) {
                    ProjectShareDTO shareDTO = BeanListUtil.convert(projectShareNcc, ProjectShareDTO.class);
                    shareDTO.setShareType(BookKeepConfirmTypeEnum.INCOME.getType());
                    allShareList.add(shareDTO);
                }
            }
        }
    }

    @Override
    public List<ProjectShareNcc> findInfo(Long id) {
        return this.baseMapper.findInfo(id);
    }

    @Override
    public List<ProjectShareNcc> listNCC(Long projectId) {
        return this.baseMapper.listNCC(projectId);
    }
}
