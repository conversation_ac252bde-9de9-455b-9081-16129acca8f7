package com.hzw.sunflower.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hzw.sunflower.common.BaseBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 项目参数表 (t_project_param_template)
 *
 * <AUTHOR>
 * @version 1.0.0 2021-04-14
 */
@ApiModel(description = "项目参数表 ")
@TableName("t_project_param_template")
@Data
public class ProjectParamTemplate extends BaseBean {
    /**
     * 版本号
     */
    private static final long serialVersionUID = -8870226821357572658L;

    /* This code was generated by TableGo tools, mark 1 begin. */

    @ApiModelProperty(value = "主键", position = 1)
    private Long id;

    @ApiModelProperty(value = "单位", position = 2)
    private Long companyId;


    @ApiModelProperty(value = "0必选必填   1选填必选其中一个  2全部可以修改", position = 4)
    private Integer notModifyStatus;

    @ApiModelProperty(value = "显示状态 0必填   1选填  2不显示", position = 3)
    private Integer displayState;

    @ApiModelProperty(value = "参数名称", position = 5)
    private String name;

    @ApiModelProperty(value = "参数编码", position = 6)
    private String code;

    @ApiModelProperty(value = "父ID", position = 7)
    private Long parentId;


}