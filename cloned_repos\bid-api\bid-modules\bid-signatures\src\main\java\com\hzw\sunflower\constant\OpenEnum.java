package com.hzw.sunflower.constant;

/**
 * 专家评价状态类型枚举
 *
 * <AUTHOR>
 */
public enum OpenEnum {


    // 停用启用

    OPEN(1, "启用"),
    CLOSE(2, "停用");


    private Integer type;
    private String desc;

    OpenEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static String getValueByKey(Integer key) {
        for (OpenEnum enumValue : OpenEnum.values()) {
            if (enumValue.type.equals(key)) {
                return enumValue.desc;
            }
        }
        return null;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
