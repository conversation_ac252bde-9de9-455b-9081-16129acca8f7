package com.hzw.sunflower.dao;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hzw.sunflower.controller.response.BondChangeRecordsVo;
import com.hzw.sunflower.controller.response.BondChangeRecordsListVo;
import com.hzw.sunflower.entity.BondChangeRecords;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hzw.sunflower.entity.condition.BondChangeCondition;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 保证金调整记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-28
 */
@Mapper
public interface BondChangeRecordsMapper extends BaseMapper<BondChangeRecords> {

    /**
     * 分页查看调整记录列表
     * @param page
     * @param condition
     * @return
     */
    IPage<BondChangeRecordsListVo> getBondChangeRecordsPage(IPage<BondChangeRecordsListVo> page, @Param("condition") BondChangeCondition condition);

    /**
     * 根据标段和供应商id查询调整记录
     * @param sectionId
     * @param companyId
     * @return
     */
    List<BondChangeRecordsVo> getChangeRecords(@Param("sectionId") Long sectionId, @Param("companyId") Long companyId);
}
