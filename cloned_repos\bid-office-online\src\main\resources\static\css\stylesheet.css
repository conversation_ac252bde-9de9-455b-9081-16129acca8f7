﻿/**
 *
 * (c) Copyright Ascensio System SIA 2024
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

html {
    height: 100%;
    width: 100%;
}

body {
    display: block;
    background: #FFFFFF;
    color: #333333;
    font-family: Open Sans;
    font-size: 12px;
    font-style: normal;
    font-weight: normal;
    height: 100%;
    margin: 0;
    overflow-y: overlay;
    padding: 0;
    text-decoration: none;
    overflow-x: hidden;
}

form {
    height: 100%;
}

div {
    margin: 0;
    padding: 0;
}

a,
a:hover,
a:visited {
    color: #333333;
    cursor: pointer;
}

header {
    background: #333333;
    height: 48px;
    margin: 0 auto;
    min-width: 1152px;
    width: auto;
}

header img {
    margin: 10px 0 22px 32px;
}

.center {
    position: relative;
    margin: 0 auto 0;
    width: 1152px;
}

.main{
    display: table;
    height: calc(100% - 112px);
    min-height: 536px;
}

.table-main {
    border-spacing: 0;
    height: 100%;
    min-height: 536px;
}

.section{
    height: 100%;
    padding: 0;
    vertical-align: top;
}

.main-panel {
    box-sizing: border-box;
    -moz-box-sizing: border-box;
    height: 100%;
    list-style: none;
    padding: 48px 32px 24px;
    position: relative;
    width: 896px;
}

#portal-info {
    max-width: 65vw;
}

.portal-name {
    color: #FF6F3D;
    font-size: 24px;
    font-weight: bold;
    line-height: 133%;
    letter-spacing: -0.02em;
}

.portal-descr {
    display: inline-block;
    font-size: 16px;
    line-height: 160%;
    margin-top: 16px;
}

.header-list {
    font-weight: bold;
    font-size: 16px;
    line-height: 133%;
    letter-spacing: -0.02em;
}

label .checkbox {
    margin: 0 5px 3px 0;
    vertical-align: middle;
    cursor: pointer;
}

.try-editor-list {
    list-style: none;
    margin: 0;
    padding: 0;
}

.try-editor-list li {
    margin-bottom: 12px
}

.try-editor {
    background-color: transparent;
    background-repeat: no-repeat;
    display: block;
    font-size: 14px;
    line-height: 40px;
    padding-left: 42px;
    text-decoration: none;
}

.try-editor.word {
    background-image: url("img/file_docx.svg");
}

.try-editor.cell {
    background-image: url("img/file_xlsx.svg");
}

.try-editor.slide {
    background-image: url("img/file_pptx.svg");
}

.try-editor.form {
    background-image: url("img/file_docxf.svg");
}

.side-option {
    color: #666666;
    line-height: 24px;
}

.button,
.button:visited,
.button:hover,
.button:active {
    align-items: center;
    border-radius: 3px;
    box-sizing: border-box;
    cursor:pointer;
    display: inline-block;
    font-weight: 600;
    letter-spacing: 0.08em;
    line-height: 133%;
    padding: 8px 20px;
    text-align: center;
    text-decoration: none;
    text-transform: uppercase;
    vertical-align: middle;
    user-select: none;
    -o-touch-callout: none;
    -moz-touch-callout: none;
    -webkit-touch-callout: none;
    -o-user-select: none;
    -moz-user-select: none;
    -webkit-user-select: none;
}

.button.orange {
    background: #FF6F3D;
    border: 1px solid #FF6F3D;
    color: #FFFFFF;
}

.button.orange.disable {
    background: #EDC2B3;
    border: 1px solid #EDC2B3;
    cursor: default;
}

.button.orange:not(.disable):hover{
    background: #ff7a4b;
}

.button.gray {
    border: 1px solid #AAAAAA;
    margin-left: 8px;
}

.button.gray.disable {
    border: 1px solid #E5E5E5;
    color: #B5B5B5;
    cursor: default;
}

.button.gray:not(.disable):hover {
    border: 1px solid #FF6F3D;
    color: #FF6F3D;
}

.upload-panel {
    float: left;
    padding: 24px 0;
}

.file-upload {
    background: url(img/file_upload.svg) no-repeat 0 transparent;
    cursor: pointer;
    display: block;
    font-size: 14px;
    line-height: 40px;
    overflow: hidden;
    padding-left: 42px;
    position: relative;
    width: 150px;
}

.file-upload input {
    cursor: pointer;
    height: 40px;
    margin: 0;
    opacity: 0;
    opacity: 0;
    position: absolute;
    right: 0;
    top: 0;
    transform: translate(0px, -21px) scale(2);
    width: 192px;
}

.create-panel {
    float: left;
    padding: 16px 0;
}

.upload-panel,
.create-panel {
    width: 100%;
    border-bottom: 1px solid #D0D5DA;
}

#mainProgress {
    color: #333333;
    display: none;
    font-size: 12px;
    margin: 30px 40px
}

#mainProgress .uploadFileName{
    background-position: left center;
    background-repeat: no-repeat;
    display: block;
    font-size: 14px;
    line-height: 160%;
    overflow: hidden;
    padding-left: 28px;
    text-overflow: ellipsis;
    white-space: nowrap;
}

#mainProgress .describeUpload {
    line-height: 150%;
    letter-spacing: -0.02em;
    padding: 16px 0;
}

#mainProgress #embeddedView {
    display: none;
}

#mainProgress.embedded #embeddedView {
    display: block;
}

#mainProgress.embedded #uploadSteps {
    display: none;
}

.error-message {
    background: url(img/error.svg) no-repeat scroll 4px 10px;
    color: #CB0000;
    display: none;
    line-height: 160%;
    letter-spacing: -0.02em;
    margin: 5px 0;
    padding: 10px 10px 10px 30px;
    vertical-align: middle;
    word-wrap: break-word;
}

.step {
    background-repeat: no-repeat;
    background-position: left center;
    background-color: transparent;
    font-weight: bold;
    line-height: 167%;
    padding-left: 35px;
}

.current {
    background-image: url("img/loader16.gif");
}

.done {
    background-image: url("img/done.svg");
}

.error {
    background-image: url("img/notdone.svg");
}

.step-descr {
    display: block;
    margin-left: 35px;
    font-size: 11px;
    line-height: 188%;
}

.progress-descr {
    letter-spacing: -0.02em;
    line-height: 150%;
}

#loadScripts {
    display: none;
}

#iframeScripts {
    position: absolute;
    visibility: hidden;
}

footer {
    background: #333333;
    color: #AAAAAA;
    height: 64px;
    width: 100%;
	position: relative;
	left: 0;
	bottom: 0;
}

footer > .center{
    width: 100%;
}

footer table {
    width: 100%;
    border-spacing: 0;
}

footer table tr {
    position: relative;
    display: flex;
    flex-direction: row;
    align-items: center;
    align-content: center;
    flex-wrap: wrap;
    width: 100vw;
    height: 64px;
}

footer table td {
	display: block;
    position: relative;
    padding-left: 32px;
}

footer a,
footer a:hover,
footer a:visited {
    color: #FF6F3D;
    font-size: 14px;
    line-height: 120%;
}

footer a:hover {
    text-decoration: none;
}

footer table tr td:first-child {
    margin-left: 14%;
}

.copy {
    width: max-content;
    position: relative;
    margin-left: auto;
    margin-right: 14%;
}

.help-block {
    margin: 48px 32px 24px;
}

.help-block span {
    font-size: 14px;
    font-weight: 600;
    line-height: 19px;
}

.stored-list {
    list-style: none;
    padding: 0;
    position: relative;
    height: 100%;
}

.stored-edit {
    background-color: transparent;
    background-position: left center;
    background-repeat: no-repeat;
    display: inline-block;
    height: 16px;
    max-width: 250px;
    overflow: hidden;
    padding: 8px 0 1px 26px;
    text-decoration: none;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.stored-edit.word,
.uploadFileName.word {
    background-image: url("img/icon_docx.svg");
}

.stored-edit.cell,
.uploadFileName.cell {
    background-image: url("img/icon_xlsx.svg");
}

.stored-edit.slide,
.uploadFileName.slide {
    background-image: url("img/icon_pptx.svg");
}

.stored-edit span {
    font-size: 12px;
    line-height: 12px;
}

.stored-edit:hover span {
    text-decoration: underline;
}

.blockTitle {
    background-color: #333333 !important;
    border: none !important;
    border-radius: 0 !important;
    -moz-border-radius: 0 !important;
    -webkit-border-radius: 0 !important;
    color: #F5F5F5 !important;
    font-size: 16px !important;
    font-weight: 600!important;
    line-height: 133%;
    letter-spacing: -0.02em;
    padding: 14px 16px 14px 46px !important;
}

.dialog-close {
    background: url("img/close.svg") no-repeat scroll left top;
    cursor: pointer;
    float: right;
    font-size: 1px;
    height: 14px;
    line-height: 1px;
    margin-top: 4px;
    width: 14px;
}

.blockPage {
    border: none !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
    -moz-box-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
    -webkit-box-shadow:0 2px 4px rgba(0, 0, 0, 0.5);
    padding: 0 !important;
}

.clearFix:after {
    content: ".";
    display: block;
    height: 0;
    clear: both;
    visibility: hidden;
}

.tableRow {
    background: transparent;
    -moz-transition: all 0.2s ease-in-out;
    -webkit-transition: all 0.2s ease-in-out;
    -o-transition: all 0.2s ease-in-out;
    -ms-transition: all 0.2s ease-in-out;
    transition: all 0.2s ease-in-out;
}

.tableRow:hover {
    background-color: #ECECEC;
}

.tableHeader {
    padding-top: 10px;
}

.tableHeader tr{
    background: transparent;
    cursor: default;
    height: 40px;
    -khtml-user-select: none;
    user-select: none;
    -moz-user-select: none;
    -webkit-user-select: none;
}

.tableHeaderCell {
    border-bottom: 1px solid #EFEFEF;
    padding: 2px 4px;
    text-align: center;
}

.tableHeaderCellFileName {
    text-align: left;
    width: 37%;
}

.tableHeaderCellEditors{
    width: 29%;
}

.tableHeaderCellViewers{
    width: 11%;
}

.tableHeaderCellDownload{
    width: 13%;
    text-align: right;
    padding-right: 20px;
}

.tableHeaderCellRemove{
    text-align: left;
}

.contentCells {
	display: block;
    border-bottom: 1px solid #e5e5e5;
    font-family: 'Open Sans', sans-serif;
    font-size: 16px;
    padding: 4px;
    white-space: nowrap;
    -khtml-user-select: none;
    user-select: none;
    -moz-user-select: none;
    -webkit-user-select: none;
}

.contentCells-shift {
    padding-right: 44px;
}

.contentCells-icon {
    width: 4%;
}

.select-user {
    color: #444444;
    font-family: Open Sans;
    font-size: 12px!important;
    font-weight: normal!important;
    line-height: 16px!important;
}

.info{
    cursor: pointer;
    margin: -2px 5px;
}

.user-block-table {
    height: 100%;
    padding-top: 14px;
    width: 100%;
}

.user-block-table td {
    background-color: #F4F4F4;
    padding-top: 10px;
}

.user-block-table td select {
    border: 1px solid #D0D5DA;
    box-sizing: border-box;
    border-radius: 3px;
    cursor: pointer;
    margin-top: 5px;
    padding: 2px 5px;
    width: 100%;
}

.icon-delete {
    cursor: pointer;
}

.left-panel {
    display: flex;
    flex-direction: column;
    flex-wrap: nowrap;
    max-width: 256px;
    width: 100%;
    background: #F5F5F5;
}

.scroll-table-body {
    bottom: 0;
    left: 0;
    margin-top: 0px;
    overflow-x: auto;
    position: absolute;
    right: 0;
    top: 71px;
    scrollbar-color: #D0D5DA transparent;
    scrollbar-width: thin;
}

.scroll-table-body::-webkit-scrollbar {
    width: 4px;
}

.scroll-table-body::-webkit-scrollbar-thumb {
    background: #D0D5DA;
    border-radius: 3px;
}

.descrFilePass {
    display: block;
    font-weight: bold;
    line-height: 167%;
}

#filePass {
    border: 1px solid #D0D5DA;
    border-radius: 3px;
    box-sizing: border-box;
    display: inline-block;
    height: 33px;
    letter-spacing: -0.02em;
    line-height: 150%;
    margin-right: 8px;
    outline: none;
    padding: 7px 8px;
    vertical-align: bottom;
    user-select: none;
    -moz-user-select: none;
    -webkit-user-select: none;
    width: 250px;
}

.errorInput {
    border-color: #CB0000!important;
}

.errorPass {
    color: #CB0000;
    display: block;
    line-height: 160%;
    letter-spacing: -0.02em;
    word-wrap: break-word;
}

html {
    overflow-x: hidden;
}

.tableRow {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    flex-direction: row;
    position: relative;
}

.tableRow td:first-child {
    display: flex;
    flex-grow: 1;
    max-width: 25%;
}

.tableHeaderCellFileName {
    width: 30%;
}

.tableHeaderCellEditors {
    width: 28%;
}

.tableHeaderCellViewers {
    text-align: center;
    width: 18%
}

.firstContentCellViewers {
    margin-left: auto;
}
.tableHeaderCellDownload{
    width: 12%;
}
.tableHeaderCellRemove{
    padding-left: 10px;
}

.user-descr {
    display: inline-table;
    width: 30vw;
    min-width: 200px;
    max-width: 400px;
    margin-top: 20px;
}

.user-descr > b {
    margin-left: 25px;
}

.tooltip {
    background: #FFFFFF;
    border-radius: 5px;
    box-shadow: 0px 7px 25px rgba(85, 85, 85, 0.15);
    color: #666666;
    line-height: 160%;
    max-width: 455px;
    padding: 14px;
    position: absolute;
}

.tooltip ul {
    margin: 0;
}

.arrow {
    border-top: 8px solid transparent;
    border-bottom: 8px solid transparent;
    border-right: 8px solid #FFFFFF;
    left: -4px;
    position: absolute;
    top: 50%;
    transform: translate(-50%, -50%);
}
