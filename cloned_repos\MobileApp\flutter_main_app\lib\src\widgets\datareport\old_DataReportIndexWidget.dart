///***
/// * 数据报告 - 首页 - 数据列表形式
/// */

import 'package:flutter/material.dart';

import 'package:flutter_main_app/src/constants/theme.dart';
import 'package:flutter_main_app/src/utils/ScreenUtils.dart';

import 'package:flutter_main_app/src/components/CustomAppBar.dart';
import 'package:flutter_main_app/src/components/CustomSafeArea.dart';

class DataReportIndexWidget extends StatelessWidget {

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: '数据报告',
        leading: Container(),
      ),
      body: CustomSafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            /// * 关键字输入框
            Container(
              margin: const EdgeInsets.only(bottom: 10.0),
              padding: const EdgeInsets.only(
                left: 21.0,
                top: 8.0,
                right: 21.0,
                bottom: 8.0,
              ),
              width: ScreenUtils.screenWidth,
              height: 56.0,
              color: themeContentBackgroundColor,
              child: Container(
                padding: const EdgeInsets.only(left: 13.0),
                decoration: BoxDecoration(
                  color: themeBackgroundColor,
                  borderRadius: BorderRadius.circular(themeBorderRadius),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.max,
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: <Widget>[
                    Icon(
                      Icons.search,
                      size: 16.0,
                      color: themeHintTextColor,
                    ),
                    Expanded(
                      child: TextField(
                        style: themeTextStyle,
                        decoration: InputDecoration(
                          contentPadding: const EdgeInsets.symmetric(horizontal: 3.0),
                          hintText: '输入关键字',
                          hintStyle: themeHintTextStyle,
                          border: InputBorder.none,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            /// * 数据列表
            Expanded(
              child: ListView(
                physics: BouncingScrollPhysics(),
                children: <Widget>[
                  this._buildDataListTile(industry: '勘察', title: '招标行情监测', startDate: '2019年07月01', endDate: '2019年07月31'),
                  this._buildDataListTile(industry: '设计', title: '招标行情监测', startDate: '2019年07月01', endDate: '2019年07月31'),
                  this._buildDataListTile(industry: '施工', title: '招标行情监测', startDate: '2019年07月01', endDate: '2019年07月31'),
                  this._buildDataListTile(industry: '监理', title: '招标行情监测', startDate: '2019年07月01', endDate: '2019年07月31'),
                  this._buildDataListTile(industry: '设备', title: '招标行情监测', startDate: '2019年07月01', endDate: '2019年07月31'),
                  this._buildDataListTile(industry: '材料', title: '招标行情监测', startDate: '2019年07月01', endDate: '2019年07月31'),
                  this._buildDataListTile(industry: '投资', title: '招标行情监测', startDate: '2019年07月01', endDate: '2019年07月31'),
                  this._buildDataListTile(industry: '规划', title: '招标行情监测', startDate: '2019年07月01', endDate: '2019年07月31'),
                  this._buildDataListTile(industry: '勘察', title: '招标行情监测', startDate: '2019年06月01', endDate: '2019年06月31'),
                  this._buildDataListTile(industry: '设计', title: '招标行情监测', startDate: '2019年06月01', endDate: '2019年06月31'),
                  this._buildDataListTile(industry: '施工', title: '招标行情监测', startDate: '2019年06月01', endDate: '2019年06月31'),
                  this._buildDataListTile(industry: '监理', title: '招标行情监测', startDate: '2019年06月01', endDate: '2019年06月31'),
                  this._buildDataListTile(industry: '设备', title: '招标行情监测', startDate: '2019年06月01', endDate: '2019年06月31'),
                  this._buildDataListTile(industry: '材料', title: '招标行情监测', startDate: '2019年06月01', endDate: '2019年06月31'),
                  this._buildDataListTile(industry: '投资', title: '招标行情监测', startDate: '2019年06月01', endDate: '2019年06月31'),
                  this._buildDataListTile(industry: '规划', title: '招标行情监测', startDate: '2019年06月01', endDate: '2019年06月31'),
                  this._buildDataListTile(industry: '勘察', title: '招标行情监测', startDate: '2019年05月01', endDate: '2019年05月31'),
                  this._buildDataListTile(industry: '设计', title: '招标行情监测', startDate: '2019年05月01', endDate: '2019年05月31'),
                  this._buildDataListTile(industry: '施工', title: '招标行情监测', startDate: '2019年05月01', endDate: '2019年05月31'),
                  this._buildDataListTile(industry: '监理', title: '招标行情监测', startDate: '2019年05月01', endDate: '2019年05月31'),
                  this._buildDataListTile(industry: '设备', title: '招标行情监测', startDate: '2019年05月01', endDate: '2019年05月31'),
                  this._buildDataListTile(industry: '材料', title: '招标行情监测', startDate: '2019年05月01', endDate: '2019年05月31'),
                  this._buildDataListTile(industry: '投资', title: '招标行情监测', startDate: '2019年05月01', endDate: '2019年05月31'),
                  this._buildDataListTile(industry: '规划', title: '招标行情监测', startDate: '2019年05月01', endDate: '2019年05月31'),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  ///***
  /// * 生成数据展示卡条 UI
  /// *
  /// * @params {String} industry
  /// * @params {String} title
  /// * @params {String} startDate
  /// * @params {String} endDate
  /// *
  /// * @return {Widget}
  /// */
  Widget _buildDataListTile({ String industry, String title, String startDate, String endDate }) {

    const double leftPadding = 15.0;
    const double height = 49.0;

    final dataListTile = Column(
      children: <Widget>[
        Container(
          padding: const EdgeInsets.only(left: leftPadding),
          width: ScreenUtils.screenWidth,
          height: height,
          color: themeContentBackgroundColor,
          child: Row(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: <Widget>[
              Text(
                industry,
                style: themeActiveTextStyle,
              ),
              Padding(
                padding: const EdgeInsets.only(left: 6.0),
                child: Text(
                  '$title（$startDate ~ $endDate）',
                  style: themeTitleTextStyle,
                ),
              ),
            ],
          ),
        ),
        Divider(
          height: themeBorderWidth,
          thickness: themeBorderWidth,
          color: themeBorderColor,
        ),
      ],
    );

    return dataListTile;

  }
}
