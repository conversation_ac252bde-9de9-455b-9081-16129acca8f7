package com.hzw.ssm.sys.user.action;

import java.io.IOException;
import java.io.PrintWriter;
import java.util.List;

import org.apache.struts2.convention.annotation.Action;
import org.apache.struts2.convention.annotation.Namespace;
import org.apache.struts2.convention.annotation.ParentPackage;
import org.apache.struts2.convention.annotation.Result;
import org.apache.struts2.convention.annotation.Results;
import org.springframework.beans.factory.annotation.Autowired;

import com.hzw.ssm.fw.base.BaseAction;
import com.hzw.ssm.fw.util.MessageConstants;
import com.hzw.ssm.sys.system.entity.RoleEntity;
import com.hzw.ssm.sys.system.service.RoleService;
import com.hzw.ssm.sys.user.entity.UserEntity;
import com.hzw.ssm.sys.user.service.UserService;

/**
 * 用户操作
 * 
 * @page user
 * <AUTHOR>
 * 
 */
@Namespace("/userinfo")
@ParentPackage(value = "default")
@Results( { @Result(name = "init", location = "/jsp/user/user_list.jsp"),
		@Result(name = "input", location = "/jsp/user/user_edit.jsp"),
		@Result(name = "ajaxDone", location = "/ajaxDone.jsp"),
		@Result(name="initUserAdd",location = "/jsp/user/user_add.jsp")
		})
		
public class UserAction extends BaseAction {

	/**
	 * 
	 */
	private static final long serialVersionUID = -8823858495799309882L;

	/** 用户对象列表 */
	private List<UserEntity> lstUser;

	/** 用户对象列表的查询条件 */
	private UserEntity conUser;

	/** 用户对象 */
	private UserEntity objUser;
	
	/** 角色对象 */
	private RoleEntity conRole;

	/** 需要编辑的用户编号 */
	private String key;
	
	/** 角色列表——新增用户选择角色*/
	private List<RoleEntity> roleList;
	

	@Autowired
	private UserService userService;

	/** 角色 */
	@Autowired
	private RoleService roleService;
	
	

	/**
	 * 初始化页面，查询用户列表 因为分页，所以先按条件查询总的记录条数，刷新分页信息后，再查询当前页对应的用户信息
	 * 也就是说，分页查询，查询条件实体不能为空，传入总记录数后，即可生产翻页信息，根据翻页信息，查询相应位置的记录
	 */
	@Action("init")
	public String init() {
		if (conUser == null) {
			conUser = new UserEntity();
		}
		conUser.setPage(this.getPage());
		if("-1".equals(conUser.getRole_name())){
			conUser.setRole_name("");
		}
		lstUser = userService.queryPageUser(conUser);
		
		if(conRole == null){
			conRole = new RoleEntity();
		}
		roleList = roleService.queryRoleList(conRole);
		return INIT;
	}

	/**
	 * 禁用账户
	 * 
	 * @return
	 * @throws IOException
	 */
	@Action("invalid")
	public String invalid() throws IOException {
		try{
			objUser=new UserEntity();
			objUser.setUser_id(key);
			this.setOpaUserAndDate(objUser);
			userService.invalidUser(objUser);
			this.JSonReturn("sueess", "账户禁用成功！", null, null, false);
		}catch(Exception e){
			this.JSonReturn("error", "账户禁用失败！", null, null, false);
		}
		return null;
	}

	/**
	 * 启用账户
	 * 
	 * @return
	 * @throws IOException
	 */
	@Action("valid")
	public String valid() throws IOException {
		try{
			objUser=new UserEntity();
			objUser.setUser_id(key);
			this.setOpaUserAndDate(objUser);
			userService.validUser(objUser);
			this.JSonReturn("sueess", "账户重新启用成功！", null, null, false);
		}catch(Exception e){
			this.JSonReturn("error", "账户重新启失败！", null, null, false);
		}
			
		return null;
	}

	/**
	 * 重置密码
	 * 
	 * @return
	 */
	@Action("resetPassword")
	public String resetPassword() throws IOException {
		try{
			objUser=new UserEntity();
			objUser.setUser_id(key);
			this.setOpaUserAndDate(objUser);
			userService.resetPassword(objUser);
			this.JSonReturn("sueess", "账户密码重置成功！", null, null, false);
		}catch(Exception e){
			this.JSonReturn("error", "账户密码重置失败！", null, null, false);
		}
		return null;
	}

	/**
	 * 解锁账户
	 * 
	 * @return
	 * @throws IOException
	 */
	@Action("unlock")
	public String unlock() throws IOException {
		try{
			objUser=new UserEntity();
			objUser.setUser_id(key);
			this.setOpaUserAndDate(objUser);
			userService.unlockUser(objUser);
			this.JSonReturn("sueess", "账户解锁成功！", null, null, false);
		}catch(Exception e){
			this.JSonReturn("error", "账户解锁失败！", null, null, false);
		}
			
		return null;
	}
	
	/**
	 * 编辑初始化页面
	 */
	@Action("userInfoInit")
	public String userInfoInit() {
		if (key != null && !key.equals("")) {
			objUser = userService.selectUserById(key);
			roleList=roleService.queryRoleList(new RoleEntity());
			//departmentList=departmentService.queryPageDepartment(new DepartmentEntity());
		}
		return INPUT;
	}

	/**
	 * 编辑保存用户详细信息 
	 * 
	 * @throws IOException
	 */
	@Action("save")
	public String save() throws IOException {
		try{
			this.setOpaUserAndDate(objUser);
			userService.saveUser(key, objUser, this);
			this.setAlertMessage(MessageConstants.SAVE_SUCCESS);
		}catch(Exception e){
			e.printStackTrace();
			this.setAlertMessage(MessageConstants.SAVE_FAILED);
		}
		return this.userInfoInit();
	}

	/**
	 * 删除用户信息
	 * 
	 * @return
	 * @throws IOException
	 */
	@Action("deleteUser")
	public String delete() throws IOException {
		try{
			objUser=new UserEntity();
			objUser.setUser_id(key);
			this.setOpaUserAndDate(objUser);
			userService.deleteUser(objUser);
			this.JSonReturn("sueess", "删除成功！", null, null, false);
		}catch(Exception e){
			e.printStackTrace();
			this.JSonReturn("error", "删除失败！", null, null, false);
		}
		return null;
	}

	/**
	 * 新增用户初始化用户信息
	 * @return
	 * @throws IOException
	 */
	@Action("initUserAdd")
	public String initUserAdd()throws IOException{
		roleList=roleService.queryRoleList(new RoleEntity());
		return "initUserAdd";
	}
	
	/**
	 * 新增用户
	 * @return
	 * @throws IOException
	 */
	@Action("userAdd")
	public String userAdd() throws IOException {
		try{
			this.setOpaUserAndDate(objUser);
			userService.saveUser(key, objUser, this);
			this.setAlertMessage("保存成功");
		}catch(Exception e){
			e.printStackTrace();
			this.setAlertMessage("保存失败");
		}
		return this.initUserAdd();
	}
	
	/**
	 * 验证手机号码是否在数据库中已经存在
	 * @return
	 * @throws IOException 
	 */
	@Action("checkMobile")
	public String checkMobile() throws IOException{
		this.context();
		int result=userService.checkMobile(objUser);
		PrintWriter pw=this.getResponse().getWriter();
		pw.print(result);
		return null;
	}
	
	/**
	 * 验证登录名是否在数据库中已经存在
	 * @return
	 * @throws IOException 
	 */
	@Action("checkLoginCode")
	public String checkLoginCode() throws IOException{
		this.context();
		int result=userService.checkLoginCode(objUser);
		PrintWriter pw=this.getResponse().getWriter();
		pw.print(result);
		return null;
	}
	
	public List<UserEntity> getLstUser() {
		return lstUser;
	}

	public void setLstUser(List<UserEntity> lstUser) {
		this.lstUser = lstUser;
	}

	public UserEntity getConUser() {
		return conUser;
	}

	public void setConUser(UserEntity conUser) {
		this.conUser = conUser;
	}

	public UserEntity getObjUser() {
		return objUser;
	}

	public void setObjUser(UserEntity objUser) {
		this.objUser = objUser;
	}

	public String getKey() {
		return key;
	}

	public void setKey(String key) {
		this.key = key;
	}

	public List<RoleEntity> getRoleList() {
		return roleList;
	}

	public void setRoleList(List<RoleEntity> roleList) {
		this.roleList = roleList;
	}

	public RoleEntity getConRole() {
		return conRole;
	}

	public void setConRole(RoleEntity conRole) {
		this.conRole = conRole;
	}

}
