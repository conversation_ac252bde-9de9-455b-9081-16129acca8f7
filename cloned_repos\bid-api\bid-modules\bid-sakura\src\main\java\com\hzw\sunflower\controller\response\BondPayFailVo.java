package com.hzw.sunflower.controller.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 付款失败返回接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-27
 */
@Data
@ApiModel(value = "BondWater对象", description = "付款失败返回接口")
public class BondPayFailVo implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long refundId;

    @ApiModelProperty("项目编号")
    private String projectNum;

    @ApiModelProperty("处室")
    private String daptName;

    @ApiModelProperty("项目经理")
    private String projectManager;

    @ApiModelProperty("供应商名称")
    private String companyName;

    @ApiModelProperty("供应商付款账户")
    private String companyAccount;

    @ApiModelProperty("供应商id")
    private Long companyId;

    @ApiModelProperty("开户行")
    private String companyBankDeposit;

    @ApiModelProperty("交易金额（保留两位小数）")
    private BigDecimal amount;

    @ApiModelProperty("交易日期")
    private String date;

    @ApiModelProperty("交易时间")
    private String time;

    @ApiModelProperty("退还状态")
    private Integer refundStatus;
}
