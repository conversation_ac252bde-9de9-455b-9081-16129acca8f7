FROM anapsix/alpine-java:8_server-jre_unlimited
ENV TZ=Asia/Shanghai
MAINTAINER HZW

RUN mkdir -p /hzw/auth/logs  \
    /hzw/auth/temp  \
    /hzw/skywalking/agent

WORKDIR /hzw/auth

ENV SERVER_PORT=9210
ENV JAVA_OPTS=""
EXPOSE ${SERVER_PORT}

ADD ./target/portal-auth.jar ./app.jar
ADD ./target/classes/application.yml ./config/application.yml
#ENTRYPOINT ["java", \
#            "-Djava.security.egd=file:/dev/./urandom", \
#            "-Dserver.port=${SERVER_PORT}", \
#            "-Dskywalking.agent.service_name=portal-resource", \
#            "-javaagent:/hzw/skywalking/agent/skywalking-agent.jar", \
#            "-jar", "app.jar"]

ENTRYPOINT exec java $JAVA_OPTS -Djava.security.egd=file:/dev/./urandom -Dserver.port=${SERVER_PORT} -jar app.jar
