package com.hzw.ssm.sys.project.action;

import java.io.PrintWriter;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpSession;

import net.sf.json.JSONArray;

import org.apache.struts2.convention.annotation.Action;
import org.apache.struts2.convention.annotation.Namespace;
import org.apache.struts2.convention.annotation.ParentPackage;
import org.apache.struts2.convention.annotation.Result;
import org.apache.struts2.convention.annotation.Results;
import org.springframework.beans.factory.annotation.Autowired;

import com.hzw.ssm.expert.entity.ExpertInfoEntity;
import com.hzw.ssm.fw.base.BaseAction;
import com.hzw.ssm.fw.util.CommUtil;
import com.hzw.ssm.fw.util.DateUtil;
import com.hzw.ssm.fw.util.HZWException;
import com.hzw.ssm.fw.util.SpringApplicationContext;
import com.hzw.ssm.fw.util.SysConstants;
import com.hzw.ssm.sys.project.entity.ApplyRecordEntity;
import com.hzw.ssm.sys.project.entity.AppointsExpertsEntity;
import com.hzw.ssm.sys.project.entity.ConditionEntity;
import com.hzw.ssm.sys.project.entity.ProjectAuditEntity;
import com.hzw.ssm.sys.project.entity.ProjectEntity;
import com.hzw.ssm.sys.project.entity.ResultEntity;
import com.hzw.ssm.sys.project.service.AppointsExpertsService;
import com.hzw.ssm.sys.project.service.ProjectService;
import com.hzw.ssm.sys.user.entity.UserEntity;
import com.hzw.ssm.sys.user.service.UserService;
import com.hzw.ssm.util.empty.EmptyUtils;

/**
 * 指定专家[申请审核]
 * 
 * <AUTHOR>
 * 
 */
@Namespace("/appointAudit")
@ParentPackage(value = "default")
@Results( { @Result(name = "toProjectList", location = "/jsp/project/projectAppointAuditList.jsp"),
		@Result(name = "toProjectDetail", location = "/jsp/project/projectAppointAuditDetail.jsp"),
		@Result(name = "toProjectDetail2", location = "/jsp/project/projectAppointAuditDetailDR.jsp"),
		@Result(name = "toProjectDetailZR", location = "/jsp/project/projectAppointAuditDetailZR.jsp"),
		@Result(name = "toAppointAudit", location = "/jsp/project/appointAuditReason.jsp"),
		@Result(name = "appointsExpertsDetail", location = "/jsp/project/AppointsExpertsList.jsp")
})
public class ProjectAppointAuditAction extends BaseAction {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	@Autowired
	private ProjectService projectService;
	
	@Autowired
	private AppointsExpertsService appointsExpertsService;
	
	@Autowired
	private UserService userService;

	private ProjectEntity projectEntity;
	
	private ApplyRecordEntity applyRecordEntity;

	/** 项目列表 */
	private List<ProjectEntity> projectList;

	private ProjectAuditEntity projectAuditEntity;
	
	private String auditNum;
	
	private String auditInfo;

	private AppointsExpertsEntity appointsExpertsEntity;
	
	private List<AppointsExpertsEntity> appointsExpertsList;
	/**
	 * 查询指定专家审核列表
	 * 
	 * @return
	 */
	@Action("queryProjectList")
	public String queryProjectList() {
		this.context();
		// 当前session
		HttpSession session = getRequest().getSession();
		// 当前用户信息
		UserEntity user = (UserEntity) session.getAttribute("userInfo");
		try {
			if (null == projectEntity)
				projectEntity = new ProjectEntity();

			projectEntity.setPage(this.getPage());
			//projectEntity.setStatus(SysConstants.PROJECT_STATUS.APPOINT_WAIT_AUDIT); 2015-1-9
			if(user.getRole_name().equals(SysConstants.ROLE.DIRECTOR)){
				//projectEntity.setStatus_("3,5,8,9");
				projectEntity.setStatus_("5,8,9,10");
				this.getRequest().setAttribute("flag", "1");
			}else{
				//projectEntity.setStatus_("3,4,5,6,8,9");
				projectEntity.setStatus_("4,5,6,8,9, 10");
				
				// 查询部门下所有用户ID
				UserEntity ue = new UserEntity();
				ue.setUser_id(user.getUser_id());
				List<UserEntity> lstUser = userService.getUserInfoByUserId(ue);
				if(lstUser != null && lstUser.size()>0){
					String createUsers = "'";
					for (int i = 0; i < lstUser.size(); i++) {
						createUsers+=lstUser.get(i).getUser_id()+"','";
						//createUsers.concat(lstUser.get(i).getUser_id()).concat("','");
					}
					createUsers = createUsers.substring(0, createUsers.length()-2);
					projectEntity.setCreateUsers(createUsers);
				}
				this.getRequest().setAttribute("flag", "2");
			}
			
			//TODO 
			projectList = projectService.queryPageProjectList(projectEntity, user);
			
			Map<String,ProjectEntity> proMap = new HashMap<String,ProjectEntity>();
			List<ProjectEntity> newList = new ArrayList<ProjectEntity>();
			for(ProjectEntity project : projectList){
				//抽取批次
				String key = project.getDecimationBatch();
				if(!proMap.containsKey(key)){
					//按照“抽取批次”将同一批次下的多个项目/标段申请进行合并（这里只取第一条）
					proMap.put(key, project);
					newList.add(project);
				}
			}
			projectList = newList;
		} catch (Exception e) {
			e.printStackTrace();
		}
		return "toProjectList";
	}

	/**
	 * 查询项目明细-处长
	 * 
	 * @return
	 */
	@Action("toProjectDetail")
	public String toProjectDetail() {
		this.context();
		// 当前session
		HttpSession session = getRequest().getSession();
		// 当前用户信息
		UserEntity user = (UserEntity) session.getAttribute("userInfo");
		
		// 查询项目明细
		if(projectAuditEntity == null){
			projectAuditEntity = new ProjectAuditEntity();
		}
		
		projectAuditEntity.setDecimationBatch(projectEntity.getDecimationBatch());
		//由原来的根据项目ID查询改为根据抽取批次号查询该批次的审核结果
		projectAuditEntity = projectService.getProAuditInfoByProId(projectAuditEntity);
		
		//查询该批次所有的项目信息
		projectList = projectService.queryProjectList(projectEntity, user);
		//取第一条信息（只需要其中的项目ID），根据项目ID查询项目负责人相关信息
		projectEntity = projectService.queryProjectById(projectList.get(0));
		
		if(applyRecordEntity == null){
			applyRecordEntity = new ApplyRecordEntity();
		}
		//由原来的根据项目ID查询改为根据抽取批次号查询申请原因等信息
		applyRecordEntity.setDecimationBatch(projectEntity.getDecimationBatch());
		//只查询“指定专家”
		applyRecordEntity.setType(1L);
		applyRecordEntity = projectService.queryNewApplyRecord(applyRecordEntity);
		//获取用户指定的专家列表
		AppointsExpertsEntity appointsExpertsEntity = new AppointsExpertsEntity();
		appointsExpertsEntity.setDecimationBatch(projectEntity.getDecimationBatch());
		appointsExpertsList =  appointsExpertsService.queryDateByDecimationBatch(appointsExpertsEntity);
		
		return "toProjectDetail";
	}
	
	/**
	 * 查询项目明细-主任
	 * 
	 * @return
	 */
	@Action("toProjectDetail2")
	public String toProjectDetail2() {
		// 查询项目明细
		if(projectAuditEntity == null){
			projectAuditEntity = new ProjectAuditEntity();
		}
		projectAuditEntity.setProject_id(projectEntity.getProjectId());
		projectAuditEntity.setDecimationBatch(projectEntity.getDecimationBatch());
		projectAuditEntity = projectService.getProAuditInfoByProId(projectAuditEntity);
		projectEntity = projectService.queryProjectById(projectEntity);
		projectList = projectService.queryProjectListById(projectEntity);
		if(applyRecordEntity == null){
			applyRecordEntity = new ApplyRecordEntity();
		}
		applyRecordEntity.setProjectId(projectEntity.getProjectId());
		applyRecordEntity.setDecimationBatch(projectEntity.getDecimationBatch());
		applyRecordEntity.setType(1L);
		applyRecordEntity = projectService.queryNewApplyRecord(applyRecordEntity);
		return "toProjectDetail2";
	}
	
	/**
	 * 查询项目明细
	 * 
	 * @return
	 */
	@Action("toProjectDetailZR")
	public String toProjectDetailZR() {
		this.context();
		// 当前session
		HttpSession session = getRequest().getSession();
		// 当前用户信息
		UserEntity user = (UserEntity) session.getAttribute("userInfo");
		
		// 查询项目明细
		if(projectAuditEntity == null){
			projectAuditEntity = new ProjectAuditEntity();
		}
		
		projectAuditEntity.setDecimationBatch(projectEntity.getDecimationBatch());
		//由原来的根据项目ID查询改为根据抽取批次号查询该批次的审核结果
		projectAuditEntity = projectService.getProAuditInfoByProId(projectAuditEntity);
		
		//查询该批次所有的项目信息
		projectList = projectService.queryProjectList(projectEntity, user);
		//取第一条信息（只需要其中的项目ID），根据项目ID查询项目负责人相关信息
		projectEntity = projectService.queryProjectById(projectList.get(0));
		
		if(applyRecordEntity == null){
			applyRecordEntity = new ApplyRecordEntity();
		}
		//由原来的根据项目ID查询改为根据抽取批次号查询申请原因等信息
		applyRecordEntity.setDecimationBatch(projectEntity.getDecimationBatch());
		applyRecordEntity.setType(1L);
		applyRecordEntity = projectService.queryNewApplyRecord(applyRecordEntity);
		
		//获取用户指定的专家列表
		AppointsExpertsEntity appointsExpertsEntity = new AppointsExpertsEntity();
		appointsExpertsEntity.setDecimationBatch(projectEntity.getDecimationBatch());
		appointsExpertsList =  appointsExpertsService.queryDateByDecimationBatch(appointsExpertsEntity);
		
		return "toProjectDetailZR";
	}


	/**
	 * 跳转到审核填写理由页面
	 * 
	 * @return
	 */
	@Action("toAppointAudit")
	public String toExtractAudit() {
		if(auditNum!=null&&"1".equals(auditNum)){
			auditInfo = "同意";
		}else if(auditNum!=null&&"2".equals(auditNum)){
			auditInfo = "不同意";
		}
		return "toAppointAudit";
	}

	/**
	 * 审核 指定专家申请
	 * 
	 * @return
	 */
	@Action("doAppointAudit")
	public String doAppointAudit() {
		//projectAuditEntity.setId(CommUtil.getKey());

		this.context();
		// 当前session
		HttpSession session = getRequest().getSession();
		// 当前用户信息
		UserEntity user = (UserEntity) session.getAttribute("userInfo");
		projectAuditEntity.setAudit_user(user.getUser_id());
		projectService.projectAppointAudit(projectAuditEntity, projectEntity,user);
		projectEntity = null;
		return this.queryProjectList();
	}
	
	@Action("doAppointAudit2")
	public String doAppointAudit2() {
		this.context();
		projectAuditEntity.setId(CommUtil.getKey());
		PrintWriter out =null;
		String msg = null;
		try {
			out = this.getResponse().getWriter();
			if(EmptyUtils.isEmpty(projectEntity.getDecimationBatch())) {
				msg="success";
			}
			// 当前session
			HttpSession session = getRequest().getSession();
			// 当前用户信息
			UserEntity user = (UserEntity) session.getAttribute("userInfo");
			projectAuditEntity.setAudit_user(user.getUser_id());
			projectService.projectAppointAudit(projectAuditEntity, projectEntity,user);
			//主任审核通过之后向项目抽取人发送短信
			/*// 申请抽取之后向抽取人发送通知短信
				if (1 == projectEntity.getStatus().intValue())
				{
					this.setOpaUserAndDate(projectEntity);
					projectService.smsExtractMobile(projectEntity);
				}
			*/
			
			projectEntity = null;
			this.getResponse().sendRedirect(this.getRequest().getContextPath()+"/project/queryAuditProjectListDR");
			out.print(msg);
			out.close();
		} catch (Exception e) {
			if(e instanceof HZWException) {
				msg= e.getMessage();
				out.print(msg);
				this.setAlertMessage(e.getMessage());
			}else
			e.printStackTrace();
		}
		return null;
	}

	@Action("deleteExpert")
	public String deleteExpert(){
		this.context();
		this.getResponse().setCharacterEncoding("utf-8");
	
		UserEntity user = (UserEntity)this.getRequest().getSession().getAttribute("userInfo");
		PrintWriter out = null;
		String msg = "success";
		try{
			out=this.getResponse().getWriter();
			appointsExpertsService.deleteExpert(appointsExpertsEntity);
		}catch (Exception e) {
			if(e instanceof HZWException) {
				msg= e.getMessage();
				this.setAlertMessage(e.getMessage());
			}else
			e.printStackTrace();
		}finally{
			out.print(msg);
			out.close();
		}
		return null;
		
	}
	/**
	 * 查询用户申请的指定专家列表
	 * @return
	 */
	@Action("appointsExpertsDetail")
	public String appointsExpertsDetail() {
		projectEntity = projectService.queryProjectById(projectEntity);
		AppointsExpertsEntity entity = new AppointsExpertsEntity();
		entity.setDecimationBatch(projectEntity.getDecimationBatch());
		appointsExpertsList = appointsExpertsService.queryDateByDecimationBatch(entity);
		return "appointsExpertsDetail";
	}
	/**
	 * 校验专家是否存在
	 * @return
	 */
	@Action("checkExpert")
	public String checkExpert() {
		this.context();
		this.getResponse().setCharacterEncoding("utf-8");
		PrintWriter out = null;
		Map<String ,Object> map = new HashMap<String,Object>();
		try {
			out=this.getResponse().getWriter();
			
			if(appointsExpertsEntity.getOfficialId()==null||appointsExpertsEntity.getOfficialId()=="") {
				throw new HZWException("数据有误，请联系管理员！");
			}
			//根据主键ID查询数据
			List<AppointsExpertsEntity> entityList = appointsExpertsService.queryList(appointsExpertsEntity);
			if(entityList.size()==0) {
				throw new HZWException("指定专家有误，请联系管理员！");
			}
			//根据专家ID查询库里是否存在专家
			ConditionEntity conEntity  = new ConditionEntity();
			conEntity.getExpert().setId_no(entityList.get(0).getExpertsCodeId());
			List<ExpertInfoEntity> list=projectService.queryExperts(conEntity);
			SpringApplicationContext.copyProperties(entityList.get(0), appointsExpertsEntity);
			if(list.size()>0) {
				appointsExpertsEntity.setStatus(SysConstants.APPOINTS_EXPERTS_STAUS.EXISTENT);
			}else {
				appointsExpertsEntity.setStatus(SysConstants.APPOINTS_EXPERTS_STAUS.NONEXISTENT);
			}
			
			ConditionEntity	conditionEntity =  new ConditionEntity();
			//校验开标当天是否已经被选择了该专家
			if(projectEntity.getDecimationBatch()==null||projectEntity.getDecimationBatch()==""){
				throw new HZWException("数据有误，请联系管理员！");
			}else{
				//根据批次号获得开标时间
				List<ProjectEntity> projectList =  projectService.queryProjectListById(projectEntity);
				//获取开标时间
				String bidTime_=DateUtil.dateToString(projectList.get(0).getBidTime()); 
				
				conditionEntity.setProjectId(projectList.get(0).getProjectId());
				//判断当前专家在开标当天是否已经参加了或者被选中了(一天同一个专家 参加和未通知只能出现一次)
				ResultEntity resultEntity =projectService.checkExpertByBidTime(bidTime_,entityList.get(0).getExpertsCodeId());
				if(resultEntity!=null){
					appointsExpertsEntity.setStatus(SysConstants.APPOINTS_EXPERTS_STAUS.EXTRACTED);
				}else{
					//缺个判断
					if(list.size()>0){
						//将数据保持到抽泣记录表中
						for(ProjectEntity project:projectList) {
							resultEntity = new ResultEntity();
							resultEntity.setUserId(list.get(0).getUser_id());
							resultEntity.setUserName(list.get(0).getUser_name());
							resultEntity.setId(CommUtil.getKey());// 抽取专家表的主键ID
							resultEntity.setJoinStatus(SysConstants.JOIN_STATUS.ATTEND);
							resultEntity.setConditionId(project.getConditionId());
							resultEntity.setExtractTime(new Date());
							resultEntity.setProjectId(project.getProjectId());
							resultEntity.setDecimationBatch(project.getDecimationBatch());
							resultEntity.setSort(1L);// 抽取的批次
							projectService.submitPointResult(resultEntity);
						}
					}
					
				}
			}
			//存在修改用户指定选取专家表状态
			Integer con =	appointsExpertsService.updateStatus(appointsExpertsEntity);
			map.put("msg", "success");
			map.put("entity", appointsExpertsEntity);
			if(list.size()>0) {
				conditionEntity.setJoinStatus("0");
				List<ExpertInfoEntity> expertInfoList = projectService.queryExtractedResult(conditionEntity);
				map.put("expertInfoList", expertInfoList);
			}
			
			
			JSONArray jsonArray = JSONArray.fromObject( map ); 
			out.print(jsonArray);
			
		}catch (Exception e) {
			if(e instanceof HZWException){
				map.put("msg", "defeated");
				map.put("entity", e.getMessage());
				out.print(e.getMessage());
			}
		}finally {
			out.close();
		}
		
		return null;
	}
	public ProjectEntity getProjectEntity() {
		return projectEntity;
	}

	public void setProjectEntity(ProjectEntity projectEntity) {
		this.projectEntity = projectEntity;
	}

	public List<ProjectEntity> getProjectList() {
		return projectList;
	}

	public void setProjectList(List<ProjectEntity> projectList) {
		this.projectList = projectList;
	}

	public ProjectAuditEntity getProjectAuditEntity() {
		return projectAuditEntity;
	}

	public void setProjectAuditEntity(ProjectAuditEntity projectAuditEntity) {
		this.projectAuditEntity = projectAuditEntity;
	}

	public ApplyRecordEntity getApplyRecordEntity() {
		return applyRecordEntity;
	}

	public void setApplyRecordEntity(ApplyRecordEntity applyRecordEntity) {
		this.applyRecordEntity = applyRecordEntity;
	}

	public String getAuditNum() {
		return auditNum;
	}

	public void setAuditNum(String auditNum) {
		this.auditNum = auditNum;
	}

	public String getAuditInfo() {
		return auditInfo;
	}

	public void setAuditInfo(String auditInfo) {
		this.auditInfo = auditInfo;
	}

	public AppointsExpertsEntity getAppointsExpertsEntity() {
		return appointsExpertsEntity;
	}

	public void setAppointsExpertsEntity(AppointsExpertsEntity appointsExpertsEntity) {
		this.appointsExpertsEntity = appointsExpertsEntity;
	}

	public List<AppointsExpertsEntity> getAppointsExpertsList() {
		return appointsExpertsList;
	}

	public void setAppointsExpertsList(List<AppointsExpertsEntity> appointsExpertsList) {
		this.appointsExpertsList = appointsExpertsList;
	}

	
}
