package com.hzw.sunflower.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hzw.sunflower.common.BaseBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 保证金退还余额表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-28
 */
@Data
@TableName("t_bond_refund_balance")
@ApiModel(value = "RefundBalance对象", description = "保证金退还余额表")
public class BondRefundBalance extends BaseBean implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("付款账户")
    @TableField("company_account")
    private String companyAccount;

    @ApiModelProperty(value = "流水总金额")
    @TableField("total_amount")
    private BigDecimal totalAmount;

    @ApiModelProperty(value = "保证金已退还金额")
    @TableField("refund_money")
    private BigDecimal refundMoney;

}
