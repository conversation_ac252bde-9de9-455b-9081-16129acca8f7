package com.hzw.expertfee.api.util;

import com.hzw.sunflower.constant.GeneralConstants;
import com.hzw.sunflower.entity.JwtUser;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 */
public class SecurityUtils {

    /**
     * 获取当前登录用户
     *
     * @return
     */
    public static JwtUser getJwtUser() {
        RequestAttributes requestAttributes = RequestContextHolder.currentRequestAttributes();
        // 获取请求体 request
        HttpServletRequest request = ((ServletRequestAttributes) requestAttributes).getRequest();
        // 获取 token
        String token = request.getHeader(GeneralConstants.TOKEN);

        String jwtToken = token.replace(GeneralConstants.TOKEN_PREFIX, "");

        JwtUser jwtUser = new JwtUser(jwtToken);
        return jwtUser;
    }
}
