package com.hzw.ssm.sys.project.service;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.hzw.ssm.sys.project.dao.DebarbMapper;
import com.hzw.ssm.sys.project.entity.ExtractDebarbEntity;
@Service
public class DebarbService {

	@Autowired
	private DebarbMapper debarbMapper;
	
	
	/**
	 * 保存回避数据
	 * @param project
	 */
	public void saveDebarbData(ExtractDebarbEntity entity){
		debarbMapper.saveDebarbData(entity);
	}
	
	public List<ExtractDebarbEntity> queryExtractDebarbList(ExtractDebarbEntity entity){
		return debarbMapper.queryExtractDebarbList(entity);
	}
	/**
	 * 根据批次号删除数据
	 * @param entity
	 */
	public void deleteDebarbData(ExtractDebarbEntity entity){
		debarbMapper.deleteDebarbData(entity);
	}
}
