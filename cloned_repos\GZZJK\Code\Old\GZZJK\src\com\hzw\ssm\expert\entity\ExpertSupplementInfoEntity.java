package com.hzw.ssm.expert.entity;

import com.hzw.ssm.fw.base.BaseEntity;
import com.hzw.ssm.fw.util.Page;

public class ExpertSupplementInfoEntity extends BaseEntity {

	/**
	 * 
	 */
	private static final long serialVersionUID = 11111L;

	private String id;

	private String userId;

	private String userName;

	private String mobilePhone;

	private String isProv;

	private String isBasicInfo;

	private String isCertificateInfo;

	/**
	 * 默认.0 1高级 2 完善
	 */
	private String expertGrade;

	private Page page;

	private String remark;

	private String isPerfect;

	public Page getPage() {
		return page;
	}

	public void setPage(Page page) {
		this.page = page;
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public String getMobilePhone() {
		return mobilePhone;
	}

	public void setMobilePhone(String mobilePhone) {
		this.mobilePhone = mobilePhone;
	}

	public String getIsProv() {
		return isProv;
	}

	public void setIsProv(String isProv) {
		this.isProv = isProv;
	}

	public String getIsBasicInfo() {
		return isBasicInfo;
	}

	public void setIsBasicInfo(String isBasicInfo) {
		this.isBasicInfo = isBasicInfo;
	}

	public String getIsCertificateInfo() {
		return isCertificateInfo;
	}

	public void setIsCertificateInfo(String isCertificateInfo) {
		this.isCertificateInfo = isCertificateInfo;
	}

	public String getExpertGrade() {
		return expertGrade;
	}

	public void setExpertGrade(String expertGrade) {
		this.expertGrade = expertGrade;
	}

	public String getUserId() {
		return userId;
	}

	public void setUserId(String userId) {
		this.userId = userId;
	}

	public String getIsPerfect() {
		return isPerfect;
	}

	public void setIsPerfect(String isPerfect) {
		this.isPerfect = isPerfect;
	}

	/**
	 * @return the remark
	 */
	public String getRemark() {
		return remark;
	}

	/**
	 * @param remark
	 *            the remark to set
	 */
	public void setRemark(String remark) {
		this.remark = remark;
	}

}
