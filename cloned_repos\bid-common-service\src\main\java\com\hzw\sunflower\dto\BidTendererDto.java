package com.hzw.sunflower.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @datetime 2024/04/25 15:05
 * @description: 开评标投标人备案表
 * @version: 1.0
 */
@Data
public class BidTendererDto implements Serializable {

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "投标人id")
    private Long tendererId;

    @ApiModelProperty(value = "投标文件初审情况：0：否  1：是")
    private Integer firstTrial;
}
