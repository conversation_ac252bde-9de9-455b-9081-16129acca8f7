package com.hzw.sunflower.config.constantenum;

/**
 * <AUTHOR>
 * @date ：Created in 2022/07/27 9:23
 * @description：招标文件目录类型
 * @version: 1.0
 */
public enum DocCatalogEnum {

    CATALOG_1(1L, "招标公告"),
    CATALOG_2(2L, "投标邀请书"),
    CATALOG_3(3L, "投标人须知"),
    CATALOG_4(4L, "评标办法"),
    CATALOG_5(5L, "合同条款及格式"),
    CATALOG_6(6L, "供货要求"),
    CATALOG_7(7L, "发包人要求"),
    CATALOG_8(8L, "工程量清单"),
    CATALOG_9(9L, "图纸"),
    CATALOG_10(10L, "技术标准和要求"),
    CATALOG_11(11L, "投标文件格式");

    private Long type;
    private String desc;

    DocCatalogEnum(Long type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Long getType() {
        return type;
    }

    public void setType(Long type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
