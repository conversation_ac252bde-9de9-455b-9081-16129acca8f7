package com.hzw.ssm.expert.action;

import com.hzw.ssm.expert.entity.*;
import com.hzw.ssm.expert.service.AppraiseService;
import com.hzw.ssm.expert.service.ExpertInfoService;
import com.hzw.ssm.fw.base.BaseAction;
import com.hzw.ssm.fw.util.CommUtil;
import com.hzw.ssm.fw.util.SysConstants;
import com.hzw.ssm.sys.project.entity.ProjectEntity;
import com.hzw.ssm.sys.project.entity.ResultEntity;
import com.hzw.ssm.sys.user.entity.UserEntity;
import com.hzw.ssm.util.file.FileUtils;
import com.hzw.ssm.util.pdf.PdfUtils;
import com.hzw.ssm.util.string.StringUtils;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.hssf.util.Region;
import org.apache.poi.ss.usermodel.Font;
import org.apache.struts2.ServletActionContext;
import org.apache.struts2.convention.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import javax.servlet.http.HttpSession;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.PrintWriter;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * 专家评价
 * 
 * <AUTHOR>
 * 
 */
@Namespace("/appraise")
@ParentPackage(value = "default")
@Results({ @Result(name = "toProjectAppraise", location = "/jsp/expert/projectAppraiseList.jsp"),
		@Result(name = "toExpertAppraise", location = "/jsp/expert/expertAppraiseList.jsp"),
		@Result(name = "toAppraiseInfo", location = "/jsp/expert/appraiseInfo.jsp"),
		@Result(name = "toAppraiseIllegal", location = "/jsp/expert/appraiseIllegal.jsp"),
		@Result(name = "toProjectIseIllegalAppraise", location = "/jsp/expert/projectIllegalAppraiseList.jsp"),
		@Result(name = "toAppraiseDetail", location = "/jsp/expert/appraiseDetail.jsp") })
public class AppraiseAction extends BaseAction {

	private static final long serialVersionUID = -8823858495799309882L;

	@Autowired
	private AppraiseService appraiseService;

	@Autowired
	private ExpertInfoService expertInfoService;

	/** 评分项 */
	private List<AppraiseInfo> appInfoList;

	private ProjectEntity projectEntity;

	private List<ProjectEntity> projectList;

	private ExpertInfoEntity expertInfoEntity;

	private List<ExpertInfoEntity> expertList;

	private String extractResultId;

	private String appraiseType;

	private List<Appraise> appraiseList;

	/** 专家评价 */
	private Appraise appraise;

	/** 专家评价备注 */
	private AppraiseRemark appraiseRemark;

	private File illegal_file_path;

	private String illegal_file_pathFileName;

	private ResultEntity resultEntity;

	/** 获取违规或其他情况 */
	private String illegalContent;
	/** 违规 */
	private AppraiseIllegal illegal;
	/*@Value("${FilePath}")
	private String filePath;
*/
	@Value("${PdfFileName}")
	private String pdfFileName;

	/**
	 * 操作权限 项目负责人0：可操作奖励，违规操作（第一次） 机电中心抽取人角色1:可操作展示奖励 机电中西抽取人角色2：只可操作可操作违规菜单
	 * 机电中西抽取处长角色3：可操作违规菜单（最后一次） 机电中西抽取抽取人角色4：只可操作奖励
	 * 
	 */
	private String operatingAuthority = "0";

	/**
	 * 查询专家反馈项目列表
	 * 
	 * @return
	 * @throws Exception
	 */
	@Action("toProjectAppraise")
	public String toProjectAppraise() throws Exception {

		this.context();
		// 当前session
		HttpSession session = getRequest().getSession();
		// 当前用户信息
		UserEntity user = (UserEntity) session.getAttribute("userInfo");
		Date dNow = new Date(); // 当前时间
		Date dBefore = new Date();
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		Calendar calendar = Calendar.getInstance(); // 得到日历
		calendar.setTime(dNow);// 把当前时间赋给日历
		calendar.add(Calendar.DAY_OF_MONTH, -3); // 设置为前三天
		dBefore = calendar.getTime(); // 得到前三天的时间

		if (null == projectEntity) {
			projectEntity = new ProjectEntity();
		}

		if (projectEntity.getBidStartTime() == null || "".equals(projectEntity.getBidStartTime())) {
			// 开始时间默认设置为系统时间前三天
			projectEntity.setBidStartTimeStr(sdf.format(dBefore));
		}

		if (projectEntity.getBidEndTime() == null || "".equals(projectEntity.getBidEndTime())) {
			// 当前时间
			projectEntity.setBidEndTimeStr(sdf.format(dNow));

		}
		projectEntity.setPage(this.getPage());
		// 判断是否是机电中心抽取人角色
		if (user.getRole_id().equals("20141103100939150087")) {
			operatingAuthority = "1";
			projectList = appraiseService.queryPageProjectInfoByJD(projectEntity);
		} else {
			projectEntity.setCreateUser(user.getUser_id());
			projectList = appraiseService.queryPageProjectInfo(projectEntity);
		}
		
		return "toProjectAppraise";
	}

	/**
	 * 查询专家反馈项目列表(违规情况录入)
	 * 
	 * @return
	 * @throws Exception
	 */
	@Action("toProjectIseIllegalAppraise")
	public String toProjectIseIllegalAppraise() throws Exception {

		this.context();
		// 当前session
		if (null == projectEntity)
			projectEntity = new ProjectEntity();

		operatingAuthority = "4";
		projectEntity.setPage(this.getPage());
		projectList = appraiseService.queryPageAppraiseIllegalInfo(projectEntity);
		return "toProjectIseIllegalAppraise";
	}

	/**
	 * 查询专家反馈专家列表
	 * 
	 * @return
	 * @throws Exception
	 */
	@Action("toExpertAppraise")
	public String toExpertAppraise() throws Exception {

		this.context();

		// 当前session
		HttpSession session = getRequest().getSession();
		// 当前用户信息
		UserEntity user = (UserEntity) session.getAttribute("userInfo");

		// 只有在机电中心抽取人操作违规录入的情况下，operatingAuthority才等于4，且
		if (operatingAuthority == "4") {
			// 判断是否是机电中心抽取人角色
			if (user.getRole_id().equals("20141103100939150087")) {
				operatingAuthority = "1";// 只可以奖励，不可操作违规和评价
				expertList = appraiseService.queryExpertAppriaiseList(expertInfoEntity);
			} else if (user.getRole_id().equals("20141017094028814021")) {
				operatingAuthority = "2";// 只可以违规，不可以操作评价和奖励
				expertList = appraiseService.queryExpertAppriaiseListByJD(expertInfoEntity);
			} else {
				expertList = appraiseService.queryExpertAppriaiseList(expertInfoEntity);
			}
		} else {
			expertList = appraiseService.queryExpertAppriaiseList(expertInfoEntity);
		}
		// expertList =
		// appraiseService.queryIllegalExpertsList(expertInfoEntity);

		return "toExpertAppraise";

	}

	/**
	 * 加载评分页面
	 * 
	 * @return
	 * @throws Exception
	 */
	@Action("toAppraise")
	public String toAppraise() throws Exception {
		expertInfoEntity = appraiseService.queryBaseAppraise(extractResultId);
		// 查询评分项
		if (appraiseType.equals("1")) {
			expertInfoEntity.setAppraiseType("1");
			appInfoList = appraiseService.queryAppraiseInfoById("-1");
		} else {
			expertInfoEntity.setAppraiseType("0");
			appInfoList = appraiseService.queryAppraiseInfoById("0");
		}

		return "toAppraiseInfo";
	}

	/**
	 * 专家违规录入
	 * 
	 * @return
	 * @throws Exception
	 */
	@Action("saveAppraiseIllegal")
	public String saveAppraiseIllegal() throws Exception {
		this.context();

		// 当前session
		HttpSession session = getRequest().getSession();
		// 当前用户信息
		UserEntity user = (UserEntity) session.getAttribute("userInfo");
		if (user.getRole_id().equals("20141103100939150087")) {
			illegal.setProcessing_status(SysConstants.APPRAISEIllegal_STATUS.dealtWithExpert);
		} else if (user.getRole_id().equals("20141017094028814021")) {
			illegal.setProcessing_status(SysConstants.APPRAISEIllegal_STATUS.dealtWithExperLeader);
		} else {
			illegal.setProcessing_status(SysConstants.APPRAISEIllegal_STATUS.dealtWithManager);
		}

		// 获取违规或其他情况
		this.setOpaUserAndDate(illegal);
		if (illegal.getAppraise_illegal_id() != null && !illegal.getAppraise_illegal_id().equals("")) {
			appraiseService.updateAppraiseIllegal(illegal);
		} else {
			appraiseService.insertAppraiseILLEGAL(illegal);
		}

		return this.toExpertAppraise();
	}

	/**
	 * 专家违规录入
	 * 
	 * @return
	 * @throws Exception
	 */
	@Action("toAppraiseIllegal")
	public String toAppraiseIllegal() throws Exception {

		this.context();

		// 当前session
		HttpSession session = getRequest().getSession();
		// 当前用户信息
		UserEntity user = (UserEntity) session.getAttribute("userInfo");

		// 机电中心抽取人
		if (user.getRole_id().equals("20141103100939150087")) {
			operatingAuthority = "1";
		} else if (user.getRole_id().equals("20141017094028814021")) {
			operatingAuthority = "2";
		} else {
			operatingAuthority = "0";
		}

		expertInfoEntity = appraiseService.queryBaseAppraise(extractResultId);
		expertInfoEntity.setExtractResultId(extractResultId);
		// 查询专家违规操作
		illegal = appraiseService.queryAppraiseIllegal(extractResultId);
		return "toAppraiseIllegal";
	}

	/**
	 * 专家评价
	 * 
	 * @return
	 * @throws Exception
	 */
	@Action("addAppraise")
	public String addappraise() throws Exception {
		List<Appraise> aList = new ArrayList<Appraise>();
		this.context();

		String appraiseType = ServletActionContext.getRequest().getParameter("appraiseType");

		// 获取评标项ID
		String[] appInfoIds = ServletActionContext.getRequest().getParameterValues("appraise.appraise_info_id");
		// 取得评标项是否违规
		String[] reduceSocres = ServletActionContext.getRequest().getParameterValues("appraise.score.reduce");

		// 封装集合（评标项和评标分数一一对应）
		double finalScore = 0;
		for (int i = 0; i < appInfoIds.length; i++) {
			double rScore = 0;
			if (!"否".equals(reduceSocres[i])) {
				rScore = Double.parseDouble(reduceSocres[i]);
			}
			finalScore += rScore;
			// 封装集合对象
			Appraise a = new Appraise();
			a.setAppraise_id(CommUtil.getKey());
			a.setAppraise_info_id(appInfoIds[i]);
			a.setExtract_result_id(extractResultId);
			a.setScore(rScore);
			a.setAppraise_type(Integer.parseInt(appraiseType));
			//评价人
			// 当前登录的用户
			UserEntity user = (UserEntity) this.getRequest().getSession().getAttribute("userInfo");
			a.setAppraiseUser(user.getUser_name());
			//评价时间
			a.setAppraiseTime(new Date());
			aList.add(a);
		}

		// 更新专家分数
		expertInfoEntity = expertInfoService.queryExpertByResultId(extractResultId);
		
		if (appraiseType.equals("0")) {
			if (finalScore >= 20) {
				// 获取服务器当前时间
				Calendar cd = Calendar.getInstance();
				cd.setTime(new Date());
				//判断当前专家是否被暂停过
				if(expertInfoEntity.getPause_number()!=null && expertInfoEntity.getPause_number()>0) {
					//专家在当年被处罚过，再次被一次性扣20分 直接暂停三年处理
					cd.add(Calendar.MONTH, 36);// 增加36个月
					expertInfoEntity.setPause_number(36);// 暂停时间累加
					
				}else {
					cd.add(Calendar.MONTH, 12);// 增加12个月
					expertInfoEntity.setPause_number(12);// 暂停时间累加
				}
				expertInfoEntity.setPause_startTime(new Date());// 暂停开始时间
				expertInfoEntity.setPause_endTime(cd.getTime());// 暂停结束时间
//				expertInfoEntity.setStatus(SysConstants.EXPERT_STATUS.PAUSE_L);// 暂停
			}

			expertInfoEntity.setEval_score(expertInfoEntity.getEval_score() - finalScore);
		} else {
			expertInfoEntity.setEval_score(expertInfoEntity.getEval_score() + finalScore);
		}

		// 新增专家评分记录，更新专家评分状态，更新专家分数
		appraiseService.insertAppraise(aList, extractResultId, appraiseType, expertInfoEntity);

		return this.toProjectAppraise();
	}
	/**
	 * 审批人员审核提交打分的数据
	 */
	@Action("appraiseScore")
	public String appraiseScore(){
//		appraiseService.appraiseScore();
		return "appraiseScore";
	}
	/**
	 * 查看专家评价信息
	 * 
	 * @return
	 * @throws Exception
	 */
	@Action("toAppraiseDetail")
	public String toAppraiseDetail() throws Exception {
		// 专家信息
		expertInfoEntity = appraiseService.queryBaseAppraise(extractResultId);
		if (appraiseType.equals("1")) {
			expertInfoEntity.setAppraiseType("1");
			// 查询评分项
			appInfoList = appraiseService.queryAppraiseInfoById("-1");
			// 查询评分
			appraiseList = appraiseService.queryAppraiseDetail(extractResultId, "1");
		} else {
			expertInfoEntity.setAppraiseType("0");
			// 查询评分项
			appInfoList = appraiseService.queryAppraiseInfoById("0");
			// 查询评分
			appraiseList = appraiseService.queryAppraiseDetail(extractResultId, "0");
		}

		// 评价备注
		appraiseRemark = appraiseService.queryAppraiseRemark(extractResultId);
		return "toAppraiseDetail";
	}

	/**
	 * 函数功能描述：导出违规记录（由原来的导出Excel改为了PDF）
	 * 
	 * @return
	 */
	@Action("exportRecordPdf")
	public String exportRecordPdf() {

		PrintWriter out = null;
		try {
			this.context();
			this.getResponse().setCharacterEncoding("utf-8");
			out = this.getResponse().getWriter();

			// 表单所用到的数据
			expertInfoEntity = appraiseService.queryBaseAppraise(extractResultId);
			// 查询专家违规操作
			illegal = appraiseService.queryAppraiseIllegal(extractResultId);
			if (illegal == null) {
				illegal = new AppraiseIllegal();
			}
			// 发布路径
			String path = ServletActionContext.getServletContext().getRealPath("");
			// 模板文件路径
			String templetePath = path + "\\WEB-INF\\templete\\Violations\\";
			templetePath = FileUtils.replaceSeparator(templetePath);

			// 模板文件路径
			String projectInfoPath = templetePath + "violationInfo.html";
			// 项目信息模板文件内容
			String projectInfoTmpl = PdfUtils.readContent(projectInfoPath, "UTF-8");
			
		//	String violationInfo = "";
			String str ="";
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy 年  M 月 d 日");
			if(illegal!=null && !"".equals(illegal) && illegal.getCreate_time()!=null){
				str=sdf.format(illegal.getCreate_time());
			}else{
				str=sdf.format(new Date());
			}
			if (expertInfoEntity != null) {
				projectInfoTmpl = StringUtils.replace(projectInfoTmpl, "${userName}", expertInfoEntity.getUser_name());
				projectInfoTmpl = StringUtils.replace(projectInfoTmpl, "${expertNum}",
						expertInfoEntity.getExpert_num());
				projectInfoTmpl = StringUtils.replace(projectInfoTmpl, "${projectNo}",
						expertInfoEntity.getProject_no());
				projectInfoTmpl = StringUtils.replace(projectInfoTmpl, "${projectName}",
						expertInfoEntity.getProject_name());
				projectInfoTmpl = StringUtils.replace(projectInfoTmpl, "${tender}", expertInfoEntity.getTender());
				projectInfoTmpl = StringUtils.replace(projectInfoTmpl, "${manager}", expertInfoEntity.getManager());
				projectInfoTmpl = StringUtils.replace(projectInfoTmpl, "${CreateTime}", str);
				projectInfoTmpl = StringUtils.replace(projectInfoTmpl, "${IllegalContent}",
						illegal.getIllegal_content());
				projectInfoTmpl = StringUtils.replace(projectInfoTmpl, "${handlingOpinions}",
						illegal.getHandling_opinions());
				projectInfoTmpl = StringUtils.replace(projectInfoTmpl, "${remark}", illegal.getRemark());
			}

			// String relativePath = "/" +
			// SysConstants.FILE_PATH_ROOT+"violations";
			String savePath = ServletActionContext.getServletContext().getRealPath("") + File.separator + pdfFileName;
			savePath = FileUtils.replaceSeparator(savePath);
			String fileName = "评标专家违规或其他情况记录表_" + System.currentTimeMillis();
			String htmlPath = savePath + "/" + fileName + ".html";
			FileUtils.writeFileWithEncoding(htmlPath, projectInfoTmpl, "UTF-8");

			String pdfPath = savePath + "/" + fileName + ".pdf";
			String footerContent = "";
			PdfUtils.parsePdfByContent(projectInfoTmpl, pdfPath, "UTF-8", footerContent);

			try {
				// 删除html文件
				File file = new File(htmlPath);
				file.delete();
			} catch (Exception e) {
				e.printStackTrace();
			}

			// relativePath = relativePath.replaceAll("\\\\", "/");
			out.print("{\"filePath\":\"" + pdfFileName + "\"," + "\"fileName\":\"" + (fileName + ".pdf") + "\"}");
		} catch (Exception e) {
			e.printStackTrace();
			out.print("error");
		}

		out.close();

		return null;
	}

	/**
	 * 导出违规记录
	 * 
	 * @return
	 * @throws Exception
	 */
	@SuppressWarnings("deprecation")
	@Action("exportArrraiseIllegal")
	public String exportArrraiseIllegal() throws Exception {
		this.context();
		this.getResponse().setCharacterEncoding("utf-8");
		PrintWriter out = null;
		try {
			out = this.getResponse().getWriter();

			// 表单所用到的数据
			expertInfoEntity = appraiseService.queryBaseAppraise(extractResultId);
			// 查询专家违规操作
			illegal = appraiseService.queryAppraiseIllegal(extractResultId);

			if (illegal == null) {
				illegal = new AppraiseIllegal();
			}

			if (expertInfoEntity != null) {

				HSSFWorkbook wb = new HSSFWorkbook(); // --->创建了一个excel文件
				HSSFSheet sheet = wb.createSheet("评标专家违规或其他情况记录表"); // --->创建了一个工作簿

				sheet.setColumnWidth((short) 0, 7 * 256);
				sheet.setColumnWidth((short) 2, 18 * 256);
				sheet.setColumnWidth((short) 4, 12 * 256);
				sheet.setColumnWidth((short) 5, 12 * 256);
				sheet.setColumnWidth((short) 6, 15 * 256);
				sheet.setColumnWidth((short) 7, 15 * 256);
				sheet.setColumnWidth((short) 9, 10 * 256);
				// 样式1
				HSSFCellStyle style = wb.createCellStyle(); // 样式对象
				style.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);// 垂直
				style.setAlignment(HSSFCellStyle.ALIGN_CENTER);// 水平
				// 设置标题字体格式
				Font font = wb.createFont();
				// 设置字体样式
				font.setFontHeightInPoints((short) 15); // --->设置字体大小
				font.setFontName("黑体"); // ---》设置字体，是什么类型例如：宋体
				style.setFont(font); // --->将字体格式加入到style1中
				style.setWrapText(true);

				// 样式2
				HSSFCellStyle style1 = wb.createCellStyle(); // 样式对象
				style1.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);// 垂直
				style1.setAlignment(HSSFCellStyle.ALIGN_CENTER);// 水平
				// 设置标题字体格式
				Font font1 = wb.createFont();
				// 设置字体样式
				font1.setFontHeightInPoints((short) 20); // --->设置字体大小
				font1.setFontName("黑体"); // ---》设置字体，是什么类型例如：宋体
				style1.setFont(font1); // --->将字体格式加入到style1中
				style1.setWrapText(true);

				// 样式3
				HSSFCellStyle style2 = wb.createCellStyle(); // 样式对象
				style2.setVerticalAlignment(HSSFCellStyle.VERTICAL_JUSTIFY);// 垂直
				style2.setAlignment(HSSFCellStyle.ALIGN_RIGHT);// 水平

				// 设置标题字体格式
				Font font2 = wb.createFont();
				// 设置字体样式
				font2.setFontHeightInPoints((short) 12); // --->设置字体大小
				font2.setFontName("宋体"); // ---》设置字体，是什么类型例如：宋体
				style2.setFont(font2); // --->将字体格式加入到style1中
				style2.setWrapText(true); // 设置是否能够换行，能够换行为true
				style2.setBorderBottom((short) 1); // 设置下划线，参数是黑线的宽度
				style2.setBorderLeft((short) 1); // 设置左边框
				style2.setBorderRight((short) 1); // 设置有边框
				style2.setBorderTop((short) 1); // 设置下边框
				style2.setWrapText(true);

				// 样式4
				HSSFCellStyle style3 = wb.createCellStyle(); // 样式对象
				style3.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);// 垂直
				style3.setAlignment(HSSFCellStyle.ALIGN_CENTER);// 水平
				// 设置标题字体格式
				Font font3 = wb.createFont();
				// 设置字体样式
				font3.setFontHeightInPoints((short) 10); // --->设置字体大小
				font3.setFontName("宋体"); // ---》设置字体，是什么类型例如：宋体
				style3.setFont(font3); // --->将字体格式加入到style1中
				style3.setWrapText(true); // 设置是否能够换行，能够换行为true
				style3.setBorderBottom((short) 1); // 设置下划线，参数是黑线的宽度
				style3.setBorderLeft((short) 1); // 设置左边框
				style3.setBorderRight((short) 1); // 设置有边框
				style3.setBorderTop((short) 1); // 设置下边框
				style3.setWrapText(true);

				// 样式5
				HSSFCellStyle style4 = wb.createCellStyle(); // 样式对象
				style4.setVerticalAlignment(HSSFCellStyle.VERTICAL_TOP);// 垂直
				style4.setAlignment(HSSFCellStyle.ALIGN_LEFT);// 水平
				// 设置标题字体格式
				Font font4 = wb.createFont();
				// 设置字体样式
				font4.setFontHeightInPoints((short) 10); // --->设置字体大小
				font4.setFontName("宋体"); // ---》设置字体，是什么类型例如：宋体
				style4.setFont(font4); // --->将字体格式加入到style1中
				style4.setWrapText(true); // 设置是否能够换行，能够换行为true
				style4.setBorderBottom((short) 1); // 设置下划线，参数是黑线的宽度
				style4.setBorderLeft((short) 1); // 设置左边框
				style4.setBorderRight((short) 1); // 设置有边框
				style4.setBorderTop((short) 1); // 设置下边框
				style4.setWrapText(true);

				// 表格第一行
				HSSFRow row1 = sheet.createRow(0); // --->创建一行
				// 四个参数分别是：起始行，起始列，结束行，结束列
				sheet.addMergedRegion(new Region(0, (short) 0, 0, (short) 9));
				row1.setHeightInPoints(30);
				HSSFCell cell1 = row1.createCell((short) 0); // --->创建一个单元格
				cell1.setCellStyle(style);
				cell1.setCellValue("江苏省国际招标公司");

				// 表格第二行
				sheet.addMergedRegion(new Region(1, (short) 0, 1, (short) 9));
				HSSFRow row2 = sheet.createRow(1);
				row2.setHeightInPoints(60);
				HSSFCell cell2 = row2.createCell((short) 0);
				cell2.setCellValue("评标专家违规或其他情况记录表");
				cell2.setCellStyle(style1);

				// 第三行
				sheet.addMergedRegion(new Region(2, (short) 0, 2, (short) 1));
				sheet.addMergedRegion(new Region(2, (short) 2, 2, (short) 5));
				sheet.addMergedRegion(new Region(2, (short) 6, 2, (short) 7));
				sheet.addMergedRegion(new Region(2, (short) 8, 2, (short) 9));
				HSSFRow row3 = sheet.createRow(2);
				row3.setHeightInPoints((short) 20);
				HSSFCell cell3 = row3.createCell((short) 0);
				cell3.setCellStyle(style3);
				cell3.setCellValue("专家姓名");
				HSSFCell cell3_1 = row3.createCell((short) 2);
				cell3_1.setCellStyle(style3);
				cell3_1.setCellValue(expertInfoEntity.getUser_name());
				HSSFCell cell3_2 = row3.createCell((short) 6);
				cell3_2.setCellStyle(style3);
				cell3_2.setCellValue("专家编号");
				HSSFCell cell3_3 = row3.createCell((short) 8);
				cell3_3.setCellStyle(style3);
				cell3_3.setCellValue(expertInfoEntity.getExpert_num());
				row3.createCell((short) 1).setCellStyle(style3);
				row3.createCell((short) 3).setCellStyle(style3);
				row3.createCell((short) 4).setCellStyle(style3);
				row3.createCell((short) 5).setCellStyle(style3);
				row3.createCell((short) 7).setCellStyle(style3);
				row3.createCell((short) 9).setCellStyle(style3);

				// 第四行
				sheet.addMergedRegion(new Region(3, (short) 0, 3, (short) 1));
				sheet.addMergedRegion(new Region(3, (short) 2, 3, (short) 5));
				sheet.addMergedRegion(new Region(3, (short) 6, 3, (short) 7));
				sheet.addMergedRegion(new Region(3, (short) 8, 3, (short) 9));
				HSSFRow row4 = sheet.createRow(3);
				row4.setHeightInPoints((short) 20);
				HSSFCell cell4 = row4.createCell((short) 0);
				cell4.setCellStyle(style3);
				cell4.setCellValue("项目编号");
				HSSFCell cell4_1 = row4.createCell((short) 2);
				cell4_1.setCellStyle(style3);
				cell4_1.setCellValue(expertInfoEntity.getProject_no());
				HSSFCell cell4_2 = row4.createCell((short) 6);
				cell4_2.setCellStyle(style3);
				cell4_2.setCellValue("项目名称");
				HSSFCell cell4_3 = row4.createCell((short) 8);
				cell4_3.setCellStyle(style3);
				cell4_3.setCellValue(expertInfoEntity.getProject_name());
				row4.createCell((short) 1).setCellStyle(style3);
				row4.createCell((short) 3).setCellStyle(style3);
				row4.createCell((short) 4).setCellStyle(style3);
				row4.createCell((short) 5).setCellStyle(style3);
				row4.createCell((short) 7).setCellStyle(style3);
				row4.createCell((short) 9).setCellStyle(style3);

				// 第五行
				sheet.addMergedRegion(new Region(4, (short) 0, 4, (short) 1));
				sheet.addMergedRegion(new Region(4, (short) 2, 4, (short) 5));
				sheet.addMergedRegion(new Region(4, (short) 6, 4, (short) 7));
				sheet.addMergedRegion(new Region(4, (short) 8, 4, (short) 9));
				HSSFRow row5 = sheet.createRow(4);
				row5.setHeightInPoints((short) 20);
				HSSFCell cell5 = row5.createCell((short) 0);
				cell5.setCellStyle(style3);
				cell5.setCellValue("项目委托单位");
				HSSFCell cell5_1 = row5.createCell((short) 2);
				cell5_1.setCellStyle(style3);
				cell5_1.setCellValue(expertInfoEntity.getTender());
				HSSFCell cell5_2 = row5.createCell((short) 6);
				cell5_2.setCellStyle(style3);
				cell5_2.setCellValue("项目负责人");
				HSSFCell cell5_3 = row5.createCell((short) 8);
				cell5_3.setCellStyle(style3);
				cell5_3.setCellValue(expertInfoEntity.getManager());
				row5.createCell((short) 1).setCellStyle(style3);
				row5.createCell((short) 3).setCellStyle(style3);
				row5.createCell((short) 4).setCellStyle(style3);
				row5.createCell((short) 5).setCellStyle(style3);
				row5.createCell((short) 7).setCellStyle(style3);
				row5.createCell((short) 9).setCellStyle(style3);

				// 第六行
				sheet.addMergedRegion(new Region(5, (short) 0, 5, (short) 1));
				sheet.addMergedRegion(new Region(5, (short) 2, 5, (short) 9));
				HSSFRow row6 = sheet.createRow(5);
				row6.setHeightInPoints((short) 60);
				HSSFCell cell6 = row6.createCell((short) 0);
				cell6.setCellStyle(style3);
				cell6.setCellValue("违规或其他情况");
				HSSFCell cell6_1 = row6.createCell((short) 2);
				cell6_1.setCellStyle(style4);
				cell6_1.setCellValue(illegal.getIllegal_content());

				row6.createCell((short) 1).setCellStyle(style3);
				row6.createCell((short) 3).setCellStyle(style4);
				row6.createCell((short) 4).setCellStyle(style4);
				row6.createCell((short) 5).setCellStyle(style4);
				row6.createCell((short) 6).setCellStyle(style4);
				row6.createCell((short) 7).setCellStyle(style4);
				row6.createCell((short) 8).setCellStyle(style4);
				row6.createCell((short) 9).setCellStyle(style4);

				// 第七行
				sheet.addMergedRegion(new Region(6, (short) 0, 6, (short) 1));
				sheet.addMergedRegion(new Region(6, (short) 2, 6, (short) 9));
				HSSFRow row7 = sheet.createRow(6);
				row7.setHeightInPoints((short) 20);
				HSSFCell cell7 = row7.createCell((short) 0);
				cell7.setCellStyle(style3);
				cell7.setCellValue("报告人");
				HSSFCell cell7_1 = row7.createCell((short) 2);
				cell7_1.setCellStyle(style2);
				cell7_1.setCellValue(illegal.getCreate_time());

				row7.createCell((short) 1).setCellStyle(style3);
				row7.createCell((short) 3).setCellStyle(style3);
				row7.createCell((short) 4).setCellStyle(style3);
				row7.createCell((short) 5).setCellStyle(style3);
				row7.createCell((short) 6).setCellStyle(style3);
				row7.createCell((short) 7).setCellStyle(style3);
				row7.createCell((short) 8).setCellStyle(style3);
				row7.createCell((short) 9).setCellStyle(style3);

				// 第八行
				sheet.addMergedRegion(new Region(7, (short) 0, 7, (short) 1));
				sheet.addMergedRegion(new Region(7, (short) 2, 7, (short) 9));
				HSSFRow row8 = sheet.createRow(7);
				row8.setHeightInPoints((short) 60);
				HSSFCell cell8 = row8.createCell((short) 0);
				cell8.setCellStyle(style3);
				cell8.setCellValue("处理意见");
				HSSFCell cell8_1 = row8.createCell((short) 2);
				cell8_1.setCellStyle(style4);
				cell8_1.setCellValue(illegal.getHandling_opinions());

				row8.createCell((short) 1).setCellStyle(style3);
				row8.createCell((short) 3).setCellStyle(style4);
				row8.createCell((short) 4).setCellStyle(style4);
				row8.createCell((short) 5).setCellStyle(style4);
				row8.createCell((short) 6).setCellStyle(style4);
				row8.createCell((short) 7).setCellStyle(style4);
				row8.createCell((short) 8).setCellStyle(style4);
				row8.createCell((short) 9).setCellStyle(style4);

				// 第九行
				sheet.addMergedRegion(new Region(8, (short) 0, 8, (short) 1));
				sheet.addMergedRegion(new Region(8, (short) 2, 8, (short) 9));
				HSSFRow row9 = sheet.createRow(8);
				row9.setHeightInPoints((short) 20);
				HSSFCell cell9 = row9.createCell((short) 0);
				cell9.setCellStyle(style3);
				cell9.setCellValue("处理人");
				HSSFCell cell9_1 = row9.createCell((short) 2);
				cell9_1.setCellStyle(style2);
				cell9_1.setCellValue("  年    月    日");

				row9.createCell((short) 1).setCellStyle(style3);
				row9.createCell((short) 3).setCellStyle(style3);
				row9.createCell((short) 4).setCellStyle(style3);
				row9.createCell((short) 5).setCellStyle(style3);
				row9.createCell((short) 6).setCellStyle(style3);
				row9.createCell((short) 7).setCellStyle(style3);
				row9.createCell((short) 8).setCellStyle(style3);
				row9.createCell((short) 9).setCellStyle(style3);

				// 第十行
				sheet.addMergedRegion(new Region(9, (short) 0, 9, (short) 1));
				sheet.addMergedRegion(new Region(9, (short) 2, 9, (short) 9));
				HSSFRow row10 = sheet.createRow(9);
				row10.setHeightInPoints((short) 20);
				HSSFCell cell10 = row10.createCell((short) 0);
				cell10.setCellStyle(style3);
				cell10.setCellValue("公司领导");
				HSSFCell cell10_1 = row10.createCell((short) 2);
				cell10_1.setCellStyle(style2);
				cell10_1.setCellValue("  年    月    日");

				row10.createCell((short) 1).setCellStyle(style3);
				row10.createCell((short) 3).setCellStyle(style3);
				row10.createCell((short) 4).setCellStyle(style3);
				row10.createCell((short) 5).setCellStyle(style3);
				row10.createCell((short) 6).setCellStyle(style3);
				row10.createCell((short) 7).setCellStyle(style3);
				row10.createCell((short) 8).setCellStyle(style3);

				row10.createCell((short) 9).setCellStyle(style3);

				// 第十一行
				sheet.addMergedRegion(new Region(10, (short) 0, 10, (short) 1));
				sheet.addMergedRegion(new Region(10, (short) 2, 10, (short) 9));
				HSSFRow row11 = sheet.createRow(10);
				row11.setHeightInPoints((short) 40);
				HSSFCell cell11 = row11.createCell((short) 0);
				cell11.setCellStyle(style3);
				cell11.setCellValue("备注");
				HSSFCell cell11_1 = row11.createCell((short) 2);
				cell11_1.setCellStyle(style4);
				cell11_1.setCellValue(illegal.getRemark());

				row11.createCell((short) 1).setCellStyle(style3);
				row11.createCell((short) 3).setCellStyle(style4);
				row11.createCell((short) 4).setCellStyle(style4);
				row11.createCell((short) 5).setCellStyle(style4);
				row11.createCell((short) 6).setCellStyle(style4);
				row11.createCell((short) 7).setCellStyle(style4);
				row11.createCell((short) 8).setCellStyle(style4);
				row11.createCell((short) 9).setCellStyle(style4);

				String savePath = ServletActionContext.getServletContext().getRealPath("") + "/"
						+ SysConstants.FILE_PATH_ROOT + "pbzjwg/";
				File f1 = new File(savePath + expertInfoEntity.getProject_no());
				if (!f1.exists()) {//
					f1.mkdirs();
				}
				String newName = "评标专家违规或其他情况记录表_" + System.currentTimeMillis() + ".xls";
				FileOutputStream fileOut = null;
				try {
					fileOut = new FileOutputStream(savePath + expertInfoEntity.getProject_no() + "/" + newName);
					wb.write(fileOut);
				} catch (Exception e) {
					out.print("error");
					log.error(e);
				} finally {
					if (fileOut != null) {
						try {
							fileOut.close();
							// out.print("zjcqjl/"+projectEntity.getProjectId());
							// newName=new
							// String(newName.getBytes("GB-2312"),"UTF-8");
							out.print("{\"filePath\":\"" + "/" + SysConstants.FILE_PATH_ROOT + "pbzjwg/"
									+ expertInfoEntity.getProject_no() + "\"," + "\"fileName\":\"" + newName + "\"}");
							out.close();
						} catch (IOException e) {
							out.print("error");
						}
					}
				}
			}
		} catch (Exception e) {
			out.print("error");
		}
		return null;
	}

	public List<AppraiseInfo> getAppInfoList() {
		return appInfoList;
	}

	public void setAppInfoList(List<AppraiseInfo> appInfoList) {
		this.appInfoList = appInfoList;
	}

	public ProjectEntity getProjectEntity() {
		return projectEntity;
	}

	public void setProjectEntity(ProjectEntity projectEntity) {
		this.projectEntity = projectEntity;
	}

	public List<ProjectEntity> getProjectList() {
		return projectList;
	}

	public void setProjectList(List<ProjectEntity> projectList) {
		this.projectList = projectList;
	}

	public ExpertInfoEntity getExpertInfoEntity() {
		return expertInfoEntity;
	}

	public void setExpertInfoEntity(ExpertInfoEntity expertInfoEntity) {
		this.expertInfoEntity = expertInfoEntity;
	}

	public List<ExpertInfoEntity> getExpertList() {
		return expertList;
	}

	public void setExpertList(List<ExpertInfoEntity> expertList) {
		this.expertList = expertList;
	}

	public String getExtractResultId() {
		return extractResultId;
	}

	public void setExtractResultId(String extractResultId) {
		this.extractResultId = extractResultId;
	}

	public Appraise getAppraise() {
		return appraise;
	}

	public void setAppraise(Appraise appraise) {
		this.appraise = appraise;
	}

	public List<Appraise> getAppraiseList() {
		return appraiseList;
	}

	public void setAppraiseList(List<Appraise> appraiseList) {
		this.appraiseList = appraiseList;
	}

	public AppraiseRemark getAppraiseRemark() {
		return appraiseRemark;
	}

	public void setAppraiseRemark(AppraiseRemark appraiseRemark) {
		this.appraiseRemark = appraiseRemark;
	}

	public File getIllegal_file_path() {
		return illegal_file_path;
	}

	public void setIllegal_file_path(File illegal_file_path) {
		this.illegal_file_path = illegal_file_path;
	}

	public String getIllegal_file_pathFileName() {
		return illegal_file_pathFileName;
	}

	public void setIllegal_file_pathFileName(String illegal_file_pathFileName) {
		this.illegal_file_pathFileName = illegal_file_pathFileName;
	}

	public ResultEntity getResultEntity() {
		return resultEntity;
	}

	public void setResultEntity(ResultEntity resultEntity) {
		this.resultEntity = resultEntity;
	}

	public Appraise getappraise() {
		return appraise;
	}

	public void setappraise(Appraise appraise) {
		this.appraise = appraise;
	}

	public String getAppraiseType() {
		return appraiseType;
	}

	public void setAppraiseType(String appraiseType) {
		this.appraiseType = appraiseType;
	}

	public String getIllegalContent() {
		return illegalContent;
	}

	public void setIllegalContent(String illegalContent) {
		this.illegalContent = illegalContent;
	}

	public AppraiseIllegal getIllegal() {
		return illegal;
	}

	public void setIllegal(AppraiseIllegal illegal) {
		this.illegal = illegal;
	}

	public String getOperatingAuthority() {
		return operatingAuthority;
	}

	public void setOperatingAuthority(String operatingAuthority) {
		this.operatingAuthority = operatingAuthority;
	}

}
