package com.hzw.sunflower.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hzw.sunflower.controller.project.response.DataUpdateRecordVo;
import com.hzw.sunflower.controller.project.response.ProjectBidSectionVO;
import com.hzw.sunflower.entity.DataUpdateRecord;
import com.hzw.sunflower.entity.OssFile;
import com.hzw.sunflower.entity.condition.DataUpdateRecordCondition;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2022/9/5 10:50
 * @description：
 * @modified By：`
 * @version: 1.0
 */
public interface DataUpdateRecordMapper extends BaseMapper<DataUpdateRecord> {

    IPage<DataUpdateRecordVo> listRecordPage(IPage<DataUpdateRecordVo> page, @Param("condition") DataUpdateRecordCondition condition);

    /**
     * 查询审核列表
     * @param page
     * @param condition
     * @return
     */
    IPage<DataUpdateRecordVo> queryList(IPage<DataUpdateRecordVo> page, @Param("condition") DataUpdateRecordCondition condition);

    String queryBidWinNumber(Long id, Integer bidRound);

    Integer updateBidWin(@Param("sectionId")Long sectionId, @Param("bidRound")Integer bidRound, @Param("winPeopleNumber")Integer winPeopleNumber);

    Integer updateBidWinPeople(@Param("sectionId")Long sectionId, @Param("bidRound")Integer bidRound, @Param("winPeopleNumber")Integer winPeopleNumber);

    Integer updateBidWinPeopleRecord(@Param("sectionId")Long sectionId, @Param("bidRound")Integer bidRound, @Param("winPeopleNumber")Integer winPeopleNumber);

    List<Long> queryBidWinPeople(@Param("sectionId")Long sectionId, @Param("bidRound")Integer bidRound, @Param("winPeopleNumber")Integer winPeopleNumber);

    List<Long> queryBidWinPeopleRecord(@Param("sectionId")Long sectionId, @Param("bidRound")Integer bidRound, @Param("winPeopleNumber")Integer winPeopleNumber);

    ProjectBidSectionVO findDoc(String sectionId);

    Integer updateBidDoc(@Param("sectionId") String sectionId, @Param("ossFileById")OssFile ossFileById);

    Integer updateAnnexFile(@Param("sectionIds")String sectionIds,@Param("annexFile")Long annexFile,@Param("integer")Integer integer);

    Long selectAnnexFile(String sectionIds);

    Integer insertAnnexFile(@Param("sectionId")String sectionId, @Param("projectId")Long projectId, @Param("annexFile")Long annexFile);

    Integer updateBidDocRecord(@Param("sectionId") String sectionId, @Param("ossFileById") OssFile ossFileById);


    List<DataUpdateRecord> getInfoByProjectId(@Param("type")Integer type, @Param("projectId")Long projectId, @Param("sectionId")Long sectionId, @Param("sectionIds")List<String> sectionIds);

    List<DataUpdateRecordVo> queryListForApp(@Param("condition") DataUpdateRecordCondition condition);

    /**
     * 查询审核列表
     * @param page
     * @param condition
     * @return
     */
    IPage<DataUpdateRecordVo> queryListApp(IPage<DataUpdateRecordVo> page, @Param("condition") DataUpdateRecordCondition condition);
}
