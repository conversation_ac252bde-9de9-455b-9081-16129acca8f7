package com.hzw.sunflower.service.impl;

import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzw.sunflower.constant.constantenum.BankTypeEnum;
import com.hzw.sunflower.constant.constantenum.BondPayInfoReqTypeEnum;
import com.hzw.sunflower.constant.constantenum.BondRefundStatusEnum;
import com.hzw.sunflower.constant.constantenum.PayStatusEnum;
import com.hzw.sunflower.controller.request.TranStatusReq;
import com.hzw.sunflower.controller.response.BondPayStatusVo;
import com.hzw.sunflower.dao.BondRefundDetailsMapper;
import com.hzw.sunflower.dto.BondPayInfoDto;
import com.hzw.sunflower.dto.SyncNccRefundInfoDto;
import com.hzw.sunflower.entity.BondPayInfo;
import com.hzw.sunflower.entity.BondRefundDetails;
import com.hzw.sunflower.service.BankService;
import com.hzw.sunflower.service.BondPayInfoService;
import com.hzw.sunflower.service.BondRefundDetailsService;
import com.hzw.sunflower.service.BondRefundService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 保证金退还项目表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-27
 */
@Service
public class BondRefundDetailsServiceImpl extends ServiceImpl<BondRefundDetailsMapper, BondRefundDetails> implements BondRefundDetailsService {

    @Autowired
    BankService bankService;

    @Autowired
    BondRefundService bondRefundService;

    @Autowired
    BondPayInfoService bondPayInfoService;
    /**
     * 查询退还详情（退还成功/处理中的不查）
     * @return
     */
    @Override
    public List<BondRefundDetails> getDetatisForRefund(Long refundId) {
        return this.baseMapper.getDetatisForRefund(refundId);
    }

    /**
     * 查询同步到ncc的退还信息
     * @param id
     * @return
     */
    @Override
    public List<SyncNccRefundInfoDto> getSyncNccRefundInfo(Long id) {
        return this.baseMapper.getSyncNccRefundInfo(id);
    }

    /**
     * 刷新保证金退还状态
     * @param dto
     * @return
     */
    @Override
    public List<Long> refreshBondPayStatus(BondPayInfoDto dto) {
        List<Long> refundIds = new ArrayList<>();
        BondPayStatusVo vo = null;
        if (dto.getType().equals(BankTypeEnum.CHINA.getType())) {
            //"successData": "{\"msg\":\"ok\",\"insid\":\"2210121006223p\",\"code\":\"B001\",\"obssid\":\"***********\"}",
            JSONObject jsonObject = new JSONObject(dto.getSuccessData());
            TranStatusReq req = new TranStatusReq();
            req.setInsid(jsonObject.getStr("insid"));
            req.setObssid(jsonObject.getStr("obssid"));
            vo = bankService.tranStatus(req);
        } else if (dto.getType().equals(BankTypeEnum.NANJING.getType())) {
            vo = bankService.qryPayStatus(dto.getSuccessData());
            //todo 民生银行暂时换中国银行
        /*} else if (dto.getType().equals(BankTypeEnum.MINSHENG.getType())) {
            vo = bankService.qryXfer(dto.getSuccessData());*/
        }else{
            //其他银行的查询中国银行
            JSONObject jsonObject = new JSONObject(dto.getSuccessData());
            TranStatusReq req = new TranStatusReq();
            req.setInsid(jsonObject.getStr("insid"));
            req.setObssid(jsonObject.getStr("obssid"));
            vo = bankService.tranStatus(req);
        }
        // 根据返回状态进行业务处理 返回值为继续查询时不做任何操作
        BondRefundDetails refundDetails = getById(dto.getRefundDetailId());
        if (vo.getStatus().equals(PayStatusEnum.SUCCESS.getType())) {
            // 支付成功 修改refund_detail表状态为退还成功
            if (dto.getReqType().equals(BondPayInfoReqTypeEnum.BOND.getType())) {
                refundDetails.setStatus(BondRefundStatusEnum.SUCCESS.getType());
            } else {
                refundDetails.setAgencyRefundStatus(BondRefundStatusEnum.SUCCESS.getType());
            }
            this.updateById(refundDetails);
        } else if (vo.getStatus().equals(PayStatusEnum.FAIL.getType())) {
            // 支付失败 修改refund_detail表状态为退还失败
            if (dto.getReqType().equals(BondPayInfoReqTypeEnum.BOND.getType())) {
                refundDetails.setStatus(BondRefundStatusEnum.FAIL.getType());
            } else {
                refundDetails.setAgencyRefundStatus(BondRefundStatusEnum.FAIL.getType());
            }
            refundDetails.setFailReason(vo.getMsg());
            this.updateById(refundDetails);

            //获取银行类型
            Integer bankType = bondRefundService.getBankType(refundDetails.getFromOpenBank(), dto.getReqType());
            //退还失败的时候，删除BondPayInfo表中的数据
            LambdaQueryWrapper<BondPayInfo> removeQuery = new LambdaQueryWrapper<>();
            removeQuery.eq(BondPayInfo::getRefundDetailId,refundDetails.getId())
                    .eq(BondPayInfo::getReqType,dto.getReqType())
                    .eq(BondPayInfo::getType,bankType);
            bondPayInfoService.remove(removeQuery);
        }

        if(!refundIds.contains(dto.getRefundId())){
            refundIds.add(dto.getRefundId());
        }
        return refundIds;
    }
}
