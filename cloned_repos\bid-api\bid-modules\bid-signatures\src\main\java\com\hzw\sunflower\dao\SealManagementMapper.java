package com.hzw.sunflower.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hzw.sunflower.dto.SealManagementDTO;
import com.hzw.sunflower.entity.SealManagement;
import com.hzw.sunflower.entity.condition.SealManagementCondition;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* SealManagementMapper接口
*
*/
public interface SealManagementMapper extends BaseMapper<SealManagement> {

    List<String> selectProjectName(@Param("id")Long id);
}