package com.hzw.sunflower.controller;

import com.hzw.sunflower.common.BaseController;
import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.common.redis.ServerIdService;
import com.hzw.sunflower.constant.MessageConstants;
import com.hzw.sunflower.controller.request.LicenseReq;
import com.hzw.sunflower.controller.response.LicenseVO;
import com.hzw.sunflower.dto.LicenseDto;
import com.hzw.sunflower.service.LicenseService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;


/**
 * <AUTHOR>
 * @datetime 2023/02/20 16:09
 * @description: license控制层
 * @version: 1.0
 */
@Api(tags = "license管理")
@RestController
@RequestMapping("/license")
public class LicenseController extends BaseController {

    @Autowired
    private LicenseService licenseService;
    @Resource
    private ServerIdService serverIdService;


    @ApiOperation(value = "获取系统license信息")
    @GetMapping(value = "/getLicense")
    public Result<LicenseVO> getLicense() {
        LicenseVO licenseVO=new LicenseVO();
        LicenseDto licenseDto = licenseService.getLicense();
        BeanUtils.copyProperties(licenseDto,licenseVO);
        return Result.ok(licenseVO);
    }

    @ApiOperation(value = "license激活")
    @ApiImplicitParam(name = "req", value = "license激活请求入参", required = true, dataType = "LicenseReq", paramType = "body")
    @PostMapping(value = "/updateLicense")
    public Result<Boolean> updateLicense(@RequestBody LicenseReq req) {
        if (req.getLicense() == null || "".equals(req.getLicense())) {
            return Result.failed(MessageConstants.PARAMS_NOT_NULL);
        }
        Boolean data = licenseService.updateLicense(req);
        return Result.ok(data);
    }

    @ApiOperation(value = "license激活是否负责激活条件")
    @ApiImplicitParam(name = "req", value = "license激活请求入参", required = true, dataType = "LicenseReq", paramType = "body")
    @PostMapping(value = "/beforeUpdateLicense")
    public Result<Map<String, Object>> beforeUpdateLicense(@RequestBody LicenseReq req) {
        if (req.getLicense() == null || "".equals(req.getLicense())) {
            return Result.failed(MessageConstants.PARAMS_NOT_NULL);
        }
        Map<String, Object> data = licenseService.beforeUpdateLicense(req);
        return Result.ok(data);
    }

    @ApiOperation(value = "获取系统服务器指纹信息")
    @GetMapping(value = "/getMacAddr")
    public Result<Object> getMacAddr() {
        Map<String ,String > macAddr=new HashMap<>(16);
        String encode= serverIdService.getMacAddrCache();
        macAddr.put("macAddr",encode);
        return Result.ok(macAddr);
    }
}
