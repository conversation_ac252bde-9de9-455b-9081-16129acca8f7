package com.hzw.sunflower.client;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.hzw.sunflower.common.redis.RedisCache;
import com.hzw.sunflower.constant.CommonConstants;
import com.hzw.sunflower.constant.GeneralConstants;
import com.hzw.sunflower.constant.ReturnCodeConstants;
import com.hzw.sunflower.constant.constantenum.ProcessStatusEnum;
import com.hzw.sunflower.dto.AllProcessRecordDTO;
import com.hzw.sunflower.dto.ProcessRecordDTO;
import com.hzw.sunflower.dto.WorkFlowDTO;
import com.hzw.sunflower.entity.User;
import com.hzw.sunflower.freesia.client.api.FlowApiClient;
import com.hzw.sunflower.freesia.client.constant.FlowClientConstant;
import com.hzw.sunflower.freesia.client.vo.flowable.ReturnVo;
import com.hzw.sunflower.freesia.client.vo.flowable.task.TaskQueryParamsVo;
import com.hzw.sunflower.freesia.client.vo.flowable.task.TaskUserVo;
import com.hzw.sunflower.freesia.client.vo.flowable.task.TaskVo;
import com.hzw.sunflower.freesia.client.vo.pager.PagerModel;
import com.hzw.sunflower.freesia.client.vo.pager.ParamVo;
import com.hzw.sunflower.freesia.client.vo.pager.Query;
import com.hzw.sunflower.security.utils.SecurityUtils;
import com.hzw.sunflower.service.*;
import com.hzw.sunflower.util.LoginUtil;
import com.hzw.sunflower.util.WorkFlowExceptionUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 流程引擎接口客户端
 *
 * @author: xutong
 * @create: 2021-07-11 15:00
 */
@Component
public class WorkFlowApiClient {


    @Autowired
    private FlowApiClient flowApiClient;

    @Value("${organ.organization_type}")
    private Integer organizationType;


    @Value("${workflow.jumpSameUser}")
    private Boolean jumpSameUser;

    String returnVo = "{'code':100,'msg':'end','success':true,'data':null}";

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private UserOpenService userOpenService;

    @Autowired
    private ProcessRecordService processRecordService;

    @Autowired
    private CompanyFlowConfigService companyFlowConfigService;

    @Autowired
    private CompanyFlowJumpService companyFlowJumpService;

    @Autowired
    private AllProcessRecordService allProcessRecordServices;


    /**
     * 查询下一步审核人姓名
     *
     * @param userCode
     * @param processInstanceId 查询参数
     * @return
     */
    public String getNextUserName(String userCode, String processInstanceId) {
        String userName="";
        String taskUserStr = this.getTaskUserByProcessInstanceId(userCode, processInstanceId);
        JSONObject jsonUser = JSON.parseObject(taskUserStr);
        if (jsonUser.getString("code").equals(ReturnCodeConstants.PROCESS_SUCCESS_CODE)) {
            List<TaskUserVo> taskUserVoList = JSONObject.parseArray(jsonUser.getString("data"), TaskUserVo.class);
            for (TaskUserVo taskUserVo : taskUserVoList) {
                userName += taskUserVo.getUserName() + ",";
            }
        }
        if(!userName.equals("")){
            userName =  userName.substring(0, userName.length() - 1);
        }
        return userName;
    }

    /**
     * 获取下一步审批的otherId
     * @param userCode
     * @param processInstanceId
     * @return
     */
    public String getNextUserNameNew(String userCode, String processInstanceId) {
        String userName="";
        String process = flowApiClient.getProcessActivityVosByProcessInstanceId(userCode, processInstanceId);
        ReturnVo processJson = JSONObject.parseObject(process, ReturnVo.class);
        JSONArray datas = JSON.parseArray(processJson.getData().toString());
        if(datas != null && datas.size()>0){
            for(int i = 1;i<datas.size();i++){
                JSONObject nextJson = datas.getJSONObject(i);
                if(!ProcessStatusEnum.handled.getDesc().equals(nextJson.getString("status"))) {
                    String approverNo = nextJson.getString("approverNo");
                    if(StringUtils.isNotBlank(approverNo) && !approverNo.equals(userCode)){
                        return nextJson.getString("approverNo");
                    }
                }
            }
        }
        return userName;
    }

    /**
     * 判断是否为最后一级审批人
     * @param userCode
     * @param processInstanceId
     * @return
     */
    public Boolean isLastApprover(String userCode, String processInstanceId) {
        Boolean isLastApprover = false;
        // 获取所有的流程步骤
        String process = flowApiClient.getProcessActivityVosByProcessInstanceId(userCode, processInstanceId);
        ReturnVo processJson = JSONObject.parseObject(process, ReturnVo.class);
        JSONArray datas = JSON.parseArray(processJson.getData().toString());
        if(datas != null && datas.size()>0) {
            // 获取所有未处理审批人节点
            List<String> approverNos = new ArrayList<>();
            for (int i = 1; i < datas.size(); i++) {
                JSONObject nextJson = datas.getJSONObject(i);
                if (!ProcessStatusEnum.handled.getDesc().equals(nextJson.getString("status"))) {
                    String approverNo = nextJson.getString("approverNo");
                    approverNos.add(approverNo);
                }
            }
            // 未处理审批人节点过滤当前审批人，数据去重
            List<String> collect = approverNos.stream().filter(approverNo -> !approverNo.contains(userCode)).distinct().collect(Collectors.toList());
            // 没有其他审批人则为最后一级审批人
            isLastApprover = collect.size() == 0;
        }
        return isLastApprover;
    }

    /**
     * 查询我的代办列表
     *
     * @param request
     * @param taskQueryParamsVo 查询参数
     * @return
     */
    public String getAppingTaskList(HttpServletRequest request, @RequestBody ParamVo<TaskQueryParamsVo> taskQueryParamsVo) {
        if(flowIsEnd()){
            return  returnVo;
        }
        return flowApiClient.getAppingTaskList(request,taskQueryParamsVo);
    }

    /**
     * 查询我的代办列表
     *
     * @param taskQueryParamsVo 查询参数
     * @return
     */
    public String getAppingTaskList(@RequestBody ParamVo<TaskQueryParamsVo> taskQueryParamsVo) {
        if(flowIsEnd()){
            return  returnVo;
        }
        return flowApiClient.getAppingTaskList(taskQueryParamsVo);
    }
    /**
     * 查询我已办列表  已经废弃，不要调用
     *
     * @param taskQueryParamsVo 查询参数
     * @return
     */
    public String getApplyedTaskList(@RequestBody ParamVo<TaskQueryParamsVo> taskQueryParamsVo) {
        if(flowIsEnd()){
            return  returnVo;
        }
        return flowApiClient.getApplyedTaskList(taskQueryParamsVo);
    }
    /**
     * 查询处理记录
     *
     * @param request
     * @param processInstanceId 审核实例ID
     * @return
     */
    public String getCommentInfosByProcessInstanceId(HttpServletRequest request, @PathVariable String processInstanceId) {
        if(flowIsEnd()){
            return  returnVo;
        }
        return flowApiClient.getCommentInfosByProcessInstanceId(request,processInstanceId);
    }

    /**
     * 查询处理记录
     *
     * @param request
     * @param businessKey 业务ID
     * @return
     */
    public String getCommentInfosByBusinessKey(HttpServletRequest request, @PathVariable String businessKey) {
        if(flowIsEnd()){
            return  returnVo;
        }
        return flowApiClient.getCommentInfosByBusinessKey(request,businessKey);
    }

    /**
     * 启动流程
     *
     * @param userCode             发起人
     * @param processDefinitionKey 流程类别编号
     * @param formName             审批内容
     * @param businessKey          业务编号 表名:id (notice:123456)
     * @param variables            其它数据
     * @return msg:end 表示结束
     */
    public String startProcessInstanceByKey(String userCode, String processDefinitionKey, String formName, String businessKey, Map<String, Object> variables) {
        return startProcessInstanceByKey(userCode,processDefinitionKey,formName,businessKey,variables,null);
    }


    /**
     * 启动流程
     *
     * @param userCode             发起人
     * @param processDefinitionKey 流程类别编号
     * @param formName             审批内容
     * @param businessKey          业务编号 表名:id (notice:123456)
     * @param variables            其它数据
     * @return msg:end 表示结束
     */
    public String startProcessInstanceByKey(String userCode, String processDefinitionKey, String formName, String businessKey, Map<String, Object> variables,String departId) {
        return startProcessInstanceByKey(userCode,processDefinitionKey,formName,businessKey,variables,departId,true);
    }


    /**
     * 启动流程
     *
     * @param userCode             发起人
     * @param processDefinitionKey 流程类别编号
     * @param formName             审批内容
     * @param businessKey          业务编号 表名:id (notice:123456)
     * @param variables            其它数据
     * @return msg:end 表示结束
     */
    public String startProcessInstanceByKey(String userCode, String processDefinitionKey, String formName, String businessKey, Map<String, Object> variables,String departId,Boolean isJump) {
        if(flowIsEnd()){
            return  returnVo;
        }
        if(StringUtils.isBlank(departId)){
            departId = LoginUtil.getJwtUser().getUser().getDepartOtherId();
        }
        String str = flowApiClient.startProcessInstanceByKey(userCode, processDefinitionKey, formName, businessKey, variables,departId);
        Boolean flowIsJump = companyFlowConfigService.getFlowIsJump(processDefinitionKey);
        if(isJump){
           str = jumpProcess(str,userCode,flowIsJump);
        }
        return str;
    }


    /**
     * 跳过审批流
     * @param str
     * @param userCode
     * @return
     */
    /**
    private String jumpProcess(String str,String userCode){
        String retStr=str;
        if(!jumpSameUser){
            return str;
        }
        //判断推送到流程是否成功
        WorkFlowExceptionUtil.checkFlowResult(str);
        ReturnVo startJson = JSONObject.parseObject(str, ReturnVo.class);
        // 查询 该流程所有节点的处理人
        String strJsonListTask = flowApiClient.getProcessActivityVosByProcessInstanceId(userCode, startJson.getData().toString());
        //判断推送到流程是否成功
        WorkFlowExceptionUtil.checkFlowResult(strJsonListTask);
        ReturnVo listTaskResult = JSONObject.parseObject(strJsonListTask, ReturnVo.class);
        JSONArray arrayTasks = JSON.parseArray(listTaskResult.getData().toString());
        // 提交人
        String submitPhone = redisCache.getCacheObject(startJson.getData()+"_firstSubmitUser");
        // 没有跳过的阶段
        List<String> noJumpList = new ArrayList<>();
        if(arrayTasks != null && arrayTasks.size()>0){

            if(StringUtils.isBlank(submitPhone)){
                //第一条为提交人
                JSONObject submitJson = arrayTasks.getJSONObject(0);
                submitPhone = submitJson.getString("approverNo").split(";")[0];
                redisCache.setCacheObject(startJson.getData()+"_firstSubmitUser",submitPhone, GeneralConstants.REDIS_CACHE_TIME_OUT, TimeUnit.SECONDS);
            }
            String currentTaskUser = "";
            boolean jumpCurrentTaskUser = false;
            boolean jumpNextTaskUser = false;
            for(int i = 0;i<arrayTasks.size();i++){
                JSONObject currentTaskJson = arrayTasks.getJSONObject(i);

                // 排除处理过的
                if(!ProcessStatusEnum.handled.getDesc().equals(currentTaskJson.getString("status"))) {
                    // 未处理 ，第一个未处理节点是 当前待处理
                    String currentApproverNo = currentTaskJson.getString("approverNo");

                    if (StringUtils.isBlank(currentTaskUser)){
                        currentTaskUser = arrayTasks.getJSONObject(i - 1).getString("approverNo");
                    } else {

                    }
                    if (StringUtils.isNotBlank(currentApproverNo)) {
                        List<String> currentPhones = Arrays.asList(currentApproverNo.split(";"));
                        String beforeApproverNo=arrayTasks.getJSONObject(i - 1).getString("approverNo");

                        //判断提交人是否在后面的节点出现，如果出现，则走第一种跳过，提交人后出现的阶段，全部跳过
                        if (!currentPhones.contains(currentTaskUser)) {
                            // 后面的节点没有提交人
                            noJumpList.add(currentTaskJson.getString("approver"));
                            if (i==1){
                                break;
                            }
//                            break;
                        } else {
                           //跳过处理
//                            jumpHandle(submitPhone,startJson.getData().toString());
                            jumpCurrentTaskUser=true;
                            // 判断相邻节点 是否一样
                            if(i<arrayTasks.size()-1){

                                JSONObject nextJson = arrayTasks.getJSONObject(i+1);
                                String nextApproverNo = nextJson.getString("approverNo");

                                if(nextApproverNo.contains(SecurityUtils.getJwtUser().getUserOtherId())){
                                    // 相邻节点相同，跳过
                                    jumpNextTaskUser=true;
                                } else {
                                }
                            }
                            break;
                        }
                    } else {
                        // 节点处理人为空情况
                    }
                } else {
                    // 已处理
                }

            }

            if (jumpCurrentTaskUser){
                retStr = jumpHandle(submitPhone,startJson.getData().toString());
                if (jumpNextTaskUser){
                    jumpProcess(str,userCode);
                }
            }

        } else {
            return returnVo;
        }

        return retStr;
    }
    */

    private String jumpProcess(String str, String userCode, Boolean flowIsJump){
        String retStr=str;
        if(!jumpSameUser){
            return str;
        }
        ReturnVo re = JSONObject.parseObject(retStr, ReturnVo.class);
        // 判断流程是否结束
        if ("end".equals(re.getMsg())) {
            return retStr;
        }
        //判断推送到流程是否成功
        WorkFlowExceptionUtil.checkFlowResult(str);
        ReturnVo startJson = JSONObject.parseObject(str, ReturnVo.class);
        String process = flowApiClient.getProcessActivityVosByProcessInstanceId(userCode, startJson.getData().toString());
        //判断推送到流程是否成功
        WorkFlowExceptionUtil.checkFlowResult(process);
        ReturnVo processJson = JSONObject.parseObject(process, ReturnVo.class);
        JSONArray datas = JSON.parseArray(processJson.getData().toString());
        //没有跳过的阶段
        List<String> noJumpList = new ArrayList<>();
        List<String> jumpProcessIds = new ArrayList<>();
        List<String> jumpIgnoreProcessIds = new ArrayList<>();
        if(datas != null && datas.size()>0){
            JSONObject jsonObject = datas.getJSONObject(0);
            String key = jsonObject.getString("modelKey");
            if (key.contains(":")) {
                key = key.substring(0,key.indexOf(":"));
            }

            // 获取全流程空节点跳过配置
            if(null == flowIsJump){
                 flowIsJump = redisCache.getCacheObject(key);
                if(null == flowIsJump){
                    flowIsJump = companyFlowConfigService.getFlowIsJump(key);
                    redisCache.setCacheObject(key,flowIsJump,14400000L,TimeUnit.MILLISECONDS);
                }
            }

            // 获取单个跳过节点配置
            String jumpKey = key + "jump:";
            jumpProcessIds = redisCache.getCacheObject(jumpKey);
            if(null == jumpProcessIds){
                jumpProcessIds = companyFlowJumpService.getCompanyFlowJumpId(key);
                redisCache.setCacheObject(jumpKey,jumpProcessIds,14400000L,TimeUnit.MILLISECONDS);
            }

            // 获取必审节点配置
            String jumpIgnoreKey = key + "jumpIgnore:";
            jumpIgnoreProcessIds = redisCache.getCacheObject(jumpIgnoreKey);
            if(null == jumpIgnoreProcessIds){
                jumpIgnoreProcessIds = companyFlowJumpService.IgnoreJump(key);
                redisCache.setCacheObject(jumpIgnoreKey,jumpIgnoreProcessIds,14400000L,TimeUnit.MILLISECONDS);
            }

            for(int i = 0;i<datas.size();i++){
                JSONObject nextJson = datas.getJSONObject(i);
                //获取当前审批节点，判断是否为必审节点，是的话直接返回
                if(ProcessStatusEnum.handling.getDesc().equals(nextJson.getString("status"))) {
                    if(jumpIgnoreProcessIds.contains(nextJson.getString("taskDefKey"))){
                        return retStr;
                    }
                }

            }


            // 获取下一个节点数据
            String taskUserlist = flowApiClient.getTaskUserByProcessInstanceId(null, startJson.getData().toString());
            JSONObject jsonObjectTask = JSON.parseObject(taskUserlist);
            List<TaskUserVo> taskUserVoList = JSONObject.parseArray(jsonObjectTask.getString("data"), TaskUserVo.class);
            List<String> nextUserPhone = new ArrayList<>();
            for (TaskUserVo taskUserVo : taskUserVoList) {
                nextUserPhone.add(taskUserVo.getUserCode());
            }

            for(int i = 0;i<datas.size();i++){
                re = JSONObject.parseObject(retStr, ReturnVo.class);
                // 判断流程是否结束
                if ("end".equals(re.getMsg())) {
                    return retStr;
                }
                JSONObject nextJson = datas.getJSONObject(i);
                //排除处理过的
                if(!ProcessStatusEnum.handling.getDesc().equals(nextJson.getString("status"))) {
                    String approverNo = nextJson.getString("approverNo");
                    if (StringUtils.isNotBlank(approverNo)) {
                        List<String> nextPhones = Arrays.asList(approverNo.split(";"));
                        for (String nextPhone : nextPhones){
                            if (nextUserPhone.contains(nextPhone)) {
                                //跳过处理
                                retStr = jumpHandle(userCode,startJson.getData().toString());
                                retStr = jumpProcess(retStr,userCode, flowIsJump);
                                //re = JSONObject.parseObject(retStr, ReturnVo.class);
                                // 判断流程是否结束
                                //if ("end".equals(re.getMsg())) {
                                    return retStr;
                                //}
                            } else {
                                noJumpList.add(nextJson.getString("approver"));
                                //break;
                            }
                        }
                    }else {
                        noJumpList.add(nextJson.getString("approver"));;
                    }
                    // 下一步审批人为空且设置跳过
                    if(null != flowIsJump && flowIsJump && CollectionUtil.isEmpty(nextUserPhone)) {
                        //跳过处理
                        retStr = jumpHandle(userCode,startJson.getData().toString());
                        retStr = jumpProcess(retStr,userCode, flowIsJump);
                        re = JSONObject.parseObject(retStr, ReturnVo.class);
                        // 判断流程是否结束
                        if ("end".equals(re.getMsg())) {
                            return retStr;
                        }
                    }
                }
                // 判断处理中节点是否为可跳过节点，且审批人数据为空
                if(ProcessStatusEnum.handling.getDesc().equals(nextJson.getString("status"))){
                    if(jumpProcessIds.contains(nextJson.getString("taskDefKey"))
                    && StringUtils.isBlank(nextJson.getString("approverNo")) ){
                        //跳过处理
                        retStr = jumpHandle(userCode,startJson.getData().toString());
                        retStr = jumpProcess(retStr,userCode, flowIsJump);
                        re = JSONObject.parseObject(retStr, ReturnVo.class);
                        // 判断流程是否结束
                        if ("end".equals(re.getMsg())) {
                            return retStr;
                        }
                    }

                }
            }
        }
        //说明全部跳过，到达最后节点
        if(noJumpList.size()<1){
            return returnVo;
        }

        return retStr;
    }
    /**
     * 跳过审批流
     * @param str
     * @param userCode
     * @return
     */
    private String jumpProcessV2(String str,String userCode,String nextUserCode,String nextTaskUserDepartCode){
        if(!jumpSameUser){
            return str;
        }
        //判断推送到流程是否成功
        WorkFlowExceptionUtil.checkFlowResult(str);
        ReturnVo startJson = JSONObject.parseObject(str, ReturnVo.class);
        String process = flowApiClient.getProcessActivityVosByProcessInstanceId(userCode, startJson.getData().toString());
        //判断推送到流程是否成功
        WorkFlowExceptionUtil.checkFlowResult(process);
        ReturnVo processJson = JSONObject.parseObject(process, ReturnVo.class);
        JSONArray datas = JSON.parseArray(processJson.getData().toString());
        //提交人
        String submitPhone = redisCache.getCacheObject(startJson.getData()+"_firstSubmitUser");
        //没有跳过的阶段
        List<String> noJumpList = new ArrayList<>();
        if(datas != null && datas.size()>0){
            if(StringUtils.isBlank(submitPhone)){
                //第一条为提交人
                JSONObject submitJson = datas.getJSONObject(0);
                submitPhone = submitJson.getString("approverNo").split(";")[0];
                redisCache.setCacheObject(startJson.getData()+"_firstSubmitUser",submitPhone, GeneralConstants.REDIS_CACHE_TIME_OUT, TimeUnit.SECONDS);
            }
            for(int i = 1;i<datas.size();i++){
                JSONObject nextJson = datas.getJSONObject(i);
                //排除处理过的
                if(!ProcessStatusEnum.handled.getDesc().equals(nextJson.getString("status"))) {
                    String approverNo = nextJson.getString("approverNo");
                    if (StringUtils.isNotBlank(approverNo)) {
                        List<String> nextPhones = Arrays.asList(approverNo.split(";"));
                        //判断提交人是否在后面的节点出现，如果出现，则走第一种跳过，提交人后出现的阶段，全部跳过
                        if (!nextPhones.contains(submitPhone)) {
                            noJumpList.add(nextJson.getString("approver"));
                            break;
                        } else {
                            //跳过处理
                            jumpHandleV2(submitPhone,startJson.getData().toString(),nextUserCode,nextTaskUserDepartCode);
                        }
                    }
                }
            }
        }
        //说明全部跳过，到达最后节点
        if(noJumpList.size()<1){
            return returnVo;
        }

        /******************************************************第二种跳过***********************************************************************/

        List<String> noJumpList2 = new ArrayList<>();
        for(int i = 1;i<datas.size();i++){
            JSONObject nextJson = datas.getJSONObject(i);
            //排除处理过的
            if(!ProcessStatusEnum.handled.getDesc().equals(nextJson.getString("status"))) {
                String approverNo = nextJson.getString("approverNo");
                List<String> nextPhones = new ArrayList<>();
                if(StringUtils.isNotBlank(approverNo)) {
                    nextPhones = Arrays.asList(approverNo.split(";"));
                }
                //判断提交人是否在后面的节点出现，如果出现，则走第一种跳过，提交人后出现的阶段，全部跳过
                if (!nextPhones.contains(submitPhone)) {
                    noJumpList2.add(nextJson.getString("approver"));
                }
            }
        }
        //后面所有的流程中，都不包含提交人，则走第二种跳过，审批节点中出现要重复审批的，前面的都跳过
        if(noJumpList2.size()==datas.size()-1){
            Integer jumpCount = 0;
            Integer num = 1;
            recurrence(datas,userCode,startJson.getData().toString(),jumpCount,num);
        }
        return str;
    }
    /**
     * 递归处理跳过
     * @param datas
     * @param userCode
     * @param processInstanceId
     * @param jumpCount
     * @param num
     */
    private void recurrence(JSONArray datas,String userCode,String processInstanceId,Integer jumpCount,Integer num){
        Boolean intoFor = false;
        for(int i = 1;i<datas.size()-1;i++) {
            intoFor = true;
            //查询下一步审批人
            String taskUserlist = flowApiClient.getTaskUserByProcessInstanceId(null, processInstanceId);
            JSONObject jsonObjectTask = JSON.parseObject(taskUserlist);
            List<TaskUserVo> taskUserVoList = JSONObject.parseArray(jsonObjectTask.getString("data"), TaskUserVo.class);
            List<String> nextUserPhone = new ArrayList<>();
            for (TaskUserVo taskUserVo : taskUserVoList) {
                nextUserPhone.add(taskUserVo.getUserCode());
            }

            if(num < datas.size()-1) {
                JSONObject lastJson = datas.getJSONObject(datas.size() - num);
                String approverNo = lastJson.getString("approverNo");
                if(StringUtils.isNotBlank(approverNo)) {
                    String[] lastUserNos = lastJson.getString("approverNo").split(";");
                    for (String phone : lastUserNos) {
                        if (nextUserPhone.contains(phone)) {
                            jumpCount++;
                            //跳过处理
                            jumpHandle(phone, processInstanceId);
                            break;
                        }
                    }
                }
                if(jumpCount == 0){
                    num++;
                }
            }else{
                jumpCount = 1;
            }
        }
        //一个没有跳过的时候，进入下一个循环
        if(jumpCount == 0 && intoFor){
            num++;
            recurrence(datas,userCode,processInstanceId,jumpCount,num);
        }
    }


    private void recurrenceApproved(JSONArray datas,String userCode,String processInstanceId,Integer jumpCount,Integer num){
        Boolean intoFor = false;
        for(int i = 0;i<datas.size()-1;i++) {
            intoFor = true;
            //查询下一步审批人
            String taskUserlist = flowApiClient.getTaskUserByProcessInstanceId(null, processInstanceId);
            JSONObject jsonObjectTask = JSON.parseObject(taskUserlist);
            List<TaskUserVo> taskUserVoList = JSONObject.parseArray(jsonObjectTask.getString("data"), TaskUserVo.class);
            List<String> nextUserPhone = new ArrayList<>();
            for (TaskUserVo taskUserVo : taskUserVoList) {
                nextUserPhone.add(taskUserVo.getUserCode());
            }

            if(num < datas.size()-1) {
                JSONObject lastJson = new JSONObject();
                if(num == 0){
                    lastJson = datas.getJSONObject(datas.size() - 1);
                } else {
                    lastJson = datas.getJSONObject(datas.size() - num);
                }

                String approverNo = lastJson.getString("approverNo");
                if(StringUtils.isNotBlank(approverNo)) {
                    String[] lastUserNos = lastJson.getString("approverNo").split(";");
                    for (String phone : lastUserNos) {
                        if (nextUserPhone.contains(phone)) {
                            jumpCount++;
                            //跳过处理
                            jumpHandle(phone, processInstanceId);
                            break;
                        }
                    }
                }
                if(jumpCount == 0){
                    num++;
                }
            }else{
                jumpCount = 1;
            }
        }
        //一个没有跳过的时候，进入下一个循环
        if(jumpCount == 0 && intoFor){
            num++;
            recurrence(datas,userCode,processInstanceId,jumpCount,num);
        }
    }
    /**
     * 跳过处理
     * @param submitPhone
     */
    private void jumpHandleV2(String submitPhone,String processInstanceId,String nextUserCode,String nextTaskUserDepartCode){

        ParamVo<TaskQueryParamsVo> paramVo = new ParamVo<>();
        // 查询条件
        TaskQueryParamsVo queryParamsVo = new TaskQueryParamsVo();
        //queryParamsVo.setUserCode(submitPhone);

        //审批流程ids
        List<String> processInstanceIds  = new ArrayList<>();
        processInstanceIds.add(processInstanceId);
        queryParamsVo.setProcessInstanceIds(processInstanceIds);
        paramVo.setEntity(queryParamsVo);
        // 分页
        Query query = new Query();
        query.setPageNum(1);
        query.setPageSize(10);
        paramVo.setQuery(query);
        // 调用流程引擎接口查询代办列表
        String jsonReturnVo = flowApiClient.getAppingTaskList(paramVo);
        ReturnVo returnVo = JSON.parseObject(jsonReturnVo, ReturnVo.class);
        // 获取流程引擎数据
        PagerModel<TaskVo> remoteData =  JSON.parseObject(returnVo.getData().toString(), PagerModel.class);
        // 转换数据
        List<TaskVo> taskVoList = JSON.parseArray(remoteData.getData().toString(), TaskVo.class);
        if(taskVoList != null && taskVoList.size()>0) {
            //跳过当前审批  默认审批通过
            String result = flowApiClient.reviewNew(LoginUtil.getJwtUser().getUserOtherId(), processInstanceId, taskVoList.get(0).getTaskId(), "message", true,null,nextUserCode,nextTaskUserDepartCode);
            WorkFlowExceptionUtil.checkFlowResult(result);
        }
    }


    /**
     * 跳过处理
     * @param submitPhone
     */
    private String jumpHandle(String submitPhone,String processInstanceId){

        ParamVo<TaskQueryParamsVo> paramVo = new ParamVo<>();
        // 查询条件
        TaskQueryParamsVo queryParamsVo = new TaskQueryParamsVo();
        //queryParamsVo.setUserCode(submitPhone);

        //审批流程ids
        List<String> processInstanceIds  = new ArrayList<>();
        processInstanceIds.add(processInstanceId);
        queryParamsVo.setProcessInstanceIds(processInstanceIds);
        paramVo.setEntity(queryParamsVo);
        // 分页
        Query query = new Query();
        query.setPageNum(1);
        query.setPageSize(10);
        paramVo.setQuery(query);
        // 调用流程引擎接口查询代办列表
        String jsonReturnVo = flowApiClient.getAppingTaskList(paramVo);
        ReturnVo returnVo = JSON.parseObject(jsonReturnVo, ReturnVo.class);
        // 获取流程引擎数据
        PagerModel<TaskVo> remoteData =  JSON.parseObject(returnVo.getData().toString(), PagerModel.class);
        // 转换数据
        List<TaskVo> taskVoList = JSON.parseArray(remoteData.getData().toString(), TaskVo.class);
        if(taskVoList != null && taskVoList.size()>0) {
            //跳过当前审批  默认审批通过
            String result = flowApiClient.review(LoginUtil.getJwtUser().getUserOtherId(), processInstanceId, taskVoList.get(0).getTaskId(), "", true);
            WorkFlowExceptionUtil.checkFlowResult(result);
            return result;
        }
        return null;
    }

    /**
     * 审批
     *
     * @param userCode          用户编号
     * @param processInstanceId 审核实例ID
     * @param taskId            任务ID
     * @param message           审批意见
     * @param isAgree           是否同意 true 同意，false 不同意
     * @return
     */
    public String review(String userCode, String processInstanceId, String taskId, String message, Boolean isAgree) {
        return review(userCode,processInstanceId,taskId,message,isAgree,true);
    }


    /**
     * 审批
     *
     * @param userCode          用户编号
     * @param processInstanceId 审核实例ID
     * @param taskId            任务ID
     * @param message           审批意见
     * @param isAgree           是否同意 true 同意，false 不同意
     * @return
     */
    public String review(String userCode, String processInstanceId, String taskId, String message, Boolean isAgree,Boolean isJump) {
        if(flowIsEnd()){
            return  returnVo;
        }
        String review = flowApiClient.review(userCode, processInstanceId, taskId, message, isAgree);
        if(isAgree && isJump){
            //跳过审批流
            review = jumpProcess(review, userCode, null);
        }
        return review;
    }

    /**
     * 审批
     *
     * @param userCode          用户编号
     * @param processInstanceId 审核实例ID
     * @param taskId            任务ID
     * @param message           审批意见
     * @param isAgree           是否同意 true 同意，false 不同意
     * @return
     */
    public String review(String userCode, String processInstanceId, String taskId, String message, Boolean isAgree,Boolean isJump,String nextUserCode) {
        if(flowIsEnd()){
            return  returnVo;
        }
        String review = flowApiClient.review(userCode, processInstanceId, taskId, message, isAgree,null,nextUserCode);
        if(isAgree && isJump){
            //跳过审批流
            review = jumpProcess(review, userCode, null);
        }
        return review;
    }


    /**
     * 审批
     *
     * @param userCode          用户编号
     * @param processInstanceId 审核实例ID
     * @param taskId            任务ID
     * @param message           审批意见
     * @param isAgree           是否同意 true 同意，false 不同意
     * @return
     */
    public String reviewV2(String userCode, String processInstanceId, String taskId, String message, Boolean isAgree,Boolean isJump,String nextUserCode,String nextTaskUserDepartCode) {
        if(flowIsEnd()){
            return  returnVo;
        }
        String review = flowApiClient.reviewNew(userCode, processInstanceId, taskId, message, isAgree,null,nextUserCode,nextTaskUserDepartCode);
        if(isAgree && isJump){
            //跳过审批流
            review = jumpProcessV2(review, userCode,nextUserCode,nextTaskUserDepartCode);
        }
        return review;
    }

    /**
     * 审批任务
     *
     * @param userCode          用户编号
     * @param processInstanceId 审核实例ID
     * @param taskId            任务ID
     * @param message           备注
     * @param variables         扩展参数
     * @return
     */
    public String completeTask(String userCode, String processInstanceId, String taskId, String message, Map<String, Object> variables) {
        if(flowIsEnd()){
            return  returnVo;
        }
        return flowApiClient.completeTask(userCode,processInstanceId,taskId,message,variables);
    }

    /**
     * 暂停审核
     * @param userCode
     * @param businessKey
     * @return
     */
    public String suspendProcessInstanceByBusinessKey(String userCode, String businessKey) {
        if(flowIsEnd()){
            return  returnVo;
        }
        // 删除缓存数据
        deleteKeysByKey(businessKey);
        return flowApiClient.suspendProcessInstanceByBusinessKey(userCode,businessKey);
    }

    /**
     * 激活审核
     * @param userCode
     * @param businessKey
     * @return
     */
    public String activateProcessInstanceByBusinessKey(String userCode, String businessKey) {
        if(flowIsEnd()){
            return  returnVo;
        }
        // 删除缓存数据
        deleteKeysByKey(businessKey);
        return flowApiClient.activateProcessInstanceByBusinessKey(userCode,businessKey);
    }
    /**
     * 查询下一步审核人
     * @param userCode
     * @param processInstanceId
     * @return
     */
    public String getTaskUserByProcessInstanceId(String userCode, String processInstanceId) {
        if(flowIsEnd()){
            return  returnVo;
        }
        return flowApiClient.getTaskUserByProcessInstanceId(userCode,processInstanceId);
    }
    /**
     * 查询下一步审核角色
     * @param userCode
     * @param processInstanceId
     * @return
     */
    public String getTaskRoleByProcessInstanceId(String userCode, String processInstanceId) {
        if(flowIsEnd()){
            return  returnVo;
        }
        return flowApiClient.getTaskRoleByProcessInstanceId(userCode,processInstanceId);
    }
    /**
     * 拒绝
     *
     * @param userCode          用户编号
     * @param processInstanceId 审核实例ID
     * @param taskId            任务ID
     * @param message           备注
     * @return
     */
    public String stopProcess(String userCode, String processInstanceId, String taskId, String message) {
        if(flowIsEnd()){
            return  returnVo;
        }
        return flowApiClient.stopProcess(userCode,processInstanceId,taskId,message);
    }

    /**
     * 撤回
     *
     * @param userCode    用户编号
     * @param businessKey 业务编号 表名:id (notice:123456)
     * @return
     */
    public String revokeProcess(String userCode, String businessKey, String message) {
        if(flowIsEnd()){
            return  returnVo;
        }
        return flowApiClient.revokeProcess(userCode,businessKey,message);
    }

    /**
     *系统撤回
     *
     * @param businessKey 业务编号 表名:id (notice:123456)
     * @param message     备注
     * @return
     */
    public String revokeProcessBySystem(String businessKey, String message) {
        if(flowIsEnd()){
            return  returnVo;
        }
        return flowApiClient.revokeProcessBySystem(businessKey,message);
    }


    /**
     *根据流程实列查询提交人code
     * @param processInstanceId
     * @return
     */
    public String getStartUserCodeByProcessInstanceId(String processInstanceId) {
        if(flowIsEnd()){
            return  returnVo;
        }
        return flowApiClient.getStartUserCodeByProcessInstanceId(processInstanceId);
    }


    /**
     * 判断流程是否结束
     * @return
     */
    public Boolean flowIsEnd(){
        Integer userIdentity = LoginUtil.getJwtUser().getUserIdentity();
        //当前登录人的身份和租户身份不一致，则跳过审批流
        if(!organizationType.equals(userIdentity)){
            return true;
        }else{
            return false;
        }
    }

    /**
     * 审批(定时任务)
     *
     * @param userCode          用户编号
     * @param processInstanceId 审核实例ID
     * @param taskId            任务ID
     * @param message           审批意见
     * @param isAgree           是否同意 true 同意，false 不同意
     * @return
     */
    public String reviewCrocus(String userCode, String processInstanceId, String taskId, String message, Boolean isAgree) {
        return reviewCrocus(userCode,processInstanceId,taskId,message,isAgree,true);
    }

    /**
     * 审批(定时任务)
     *
     * @param userCode          用户编号
     * @param processInstanceId 审核实例ID
     * @param taskId            任务ID
     * @param message           审批意见
     * @param isAgree           是否同意 true 同意，false 不同意
     * @return
     */
    public String reviewCrocus(String userCode, String processInstanceId, String taskId, String message, Boolean isAgree,Boolean isJump) {
        String review = flowApiClient.review(userCode, processInstanceId, taskId, message, isAgree);
        if(isAgree && isJump){
            //跳过审批流
            review = jumpProcess(review, userCode, null);
        }
        return review;
    }

    /**
     * 查询下一步审核人姓名(定时任务)
     *
     * @param userCode
     * @param processInstanceId 查询参数
     * @return
     */
    public String getNextUserNameCrocus(String userCode, String processInstanceId) {
        String userName="";
        String taskUserStr = this.getTaskUserByProcessInstanceIdCrocus(userCode, processInstanceId);
        JSONObject jsonUser = JSON.parseObject(taskUserStr);
        if (jsonUser.getString("code").equals(ReturnCodeConstants.PROCESS_SUCCESS_CODE)) {
            List<TaskUserVo> taskUserVoList = JSONObject.parseArray(jsonUser.getString("data"), TaskUserVo.class);
            for (TaskUserVo taskUserVo : taskUserVoList) {
                userName += taskUserVo.getUserName() + ",";
            }
        }
        if(!userName.equals("")){
            userName =  userName.substring(0, userName.length() - 1);
        }
        return userName;
    }

    /**
     * 查询下一步审核人(定时任务)
     * @param userCode
     * @param processInstanceId
     * @return
     */
    public String getTaskUserByProcessInstanceIdCrocus(String userCode, String processInstanceId) {
        return flowApiClient.getTaskUserByProcessInstanceId(userCode,processInstanceId);
    }


    /**
     * 撤回正在执行中的审批流程
     */
    public void withdrawnFlow(List<String> tableNames, Long businessId, String processCode, String remark){
        if(!tableNames.isEmpty()){
            tableNames.forEach(tableName->{
                flowApiClient.revokeProcess(LoginUtil.getJwtUser().getUserOtherId(), tableName + ":" + businessId, "撤回");
            });
        }

        //插入流程记录
        ProcessRecordDTO recordDTO = new ProcessRecordDTO();
        recordDTO.setBusinessCode(processCode);
        recordDTO.setBusinessId(businessId);
        recordDTO.setOperation("撤回");
        recordDTO.setOperatorId(LoginUtil.getJwtUser().getUserId());
        recordDTO.setOperatorName(LoginUtil.getJwtUser().getUserName());
        recordDTO.setRemark(remark);
        processRecordService.addProcessRecord(recordDTO, LoginUtil.getJwtUser().getUserOtherId(), null);
    }



    /**
     * 撤回正在执行中的审批流程
     */
    public String getProcessInstanceIdByBusinessKey(String businessKey){
        String processInstanceId = flowApiClient.getProcessInstanceIdByBusinessKey(businessKey);
        return processInstanceId;
    }

    /**
     * 删除下一步审批人的缓存数据
     */
    public void deleteKeys(String processInstanceId){
        String nextUserKey = this.getNextUserNameNew(null, processInstanceId);
        for (String s : nextUserKey.split(";")) {
            // 删除数量缓存
            String countKey= FlowClientConstant.APPING_TASKLIST_COUNT +s;
            // 删除数据缓存
            String dataKey= FlowClientConstant.WORKBENCH_USER +s;
            flowApiClient.deleteKeysWithPattern(countKey);
            flowApiClient.deleteKeysWithPattern(dataKey);
        }
    }

    /**
     * 通过key删除下一步审批人缓存数据
     * @param businessKey
     */
    public void deleteKeysByKey(String businessKey){
        String processStr = getProcessInstanceIdByBusinessKey(businessKey);
        if(StringUtils.isNotBlank(processStr)){
            JSONObject process = JSON.parseObject(processStr);
            if ("100".equals(process.getString("code"))) {
                String userCodeStr = getStartUserCodeByProcessInstanceId(process.getString("data"));
                if(StringUtils.isNotBlank(userCodeStr)){
                    JSONObject userCodeJson = JSON.parseObject(userCodeStr);
                    if ("100".equals(userCodeJson.getString("code"))) {
                        //删除下一步审核人的缓存
                        deleteWorkbenchUser(process.getString("data"),true);
                    }
                }
            }
        }
    }

    private void deleteWorkbenchUser(String returnVo,Boolean isProcessId){
        if(StringUtils.isNotBlank(returnVo)) {
            String processId = returnVo;
            if(!isProcessId){
                JSONObject jsonObject = JSON.parseObject(returnVo);
                processId = jsonObject.getString("data");
            }
            //删除缓存
            deleteKeys(processId);
        }

    }

    /**
     * 添加其他流程记录
     * @param user
     * @param processInstanceId
     */
    private void addOtherRecord(User user, String processInstanceId, Boolean isAgree, String message) {
        ParamVo<TaskQueryParamsVo> paramVo = new ParamVo<>();
        //审批流程ids
        List<String> processInstanceIds  = new ArrayList<>();
        processInstanceIds.add(processInstanceId);
        // 查询条件
        TaskQueryParamsVo queryParamsVo = new TaskQueryParamsVo();
        queryParamsVo.setProcessInstanceIds(processInstanceIds);
        paramVo.setEntity(queryParamsVo);
        // 分页
        Query query = new Query();
        query.setPageNum(1);
        query.setPageSize(10);
        paramVo.setQuery(query);
        // 调用流程引擎接口查询代办列表
        String jsonReturnVo = flowApiClient.getAppingTaskList(paramVo);
        ReturnVo returnVo = JSON.parseObject(jsonReturnVo, ReturnVo.class);
        // 获取流程引擎数据
        PagerModel<TaskVo> remoteData =  JSON.parseObject(returnVo.getData().toString(), PagerModel.class);
        // 转换数据
        List<TaskVo> taskVoList = JSON.parseArray(remoteData.getData().toString(), TaskVo.class);
        if(taskVoList != null && taskVoList.size()>0) {
            Boolean isAdd = companyFlowConfigService.getIsAddRecord(taskVoList.get(0).getProcessDefinitionKey());
            if(isAdd) {
                //添加审批记录
                AllProcessRecordDTO dto = new AllProcessRecordDTO();
                dto.setUser(user);
                dto.setProcessDefinitionKey(taskVoList.get(0).getProcessDefinitionKey());
                dto.setBusinessKey(taskVoList.get(0).getBusinessKey());
                dto.setApprovalNode(taskVoList.get(0).getFormKey());
                dto.setOperation(isAgree?"同意":"退回");
                dto.setRemark(message);
                //null, taskVoList.get(0).getProcessDefinitionKey(), taskVoList.get(0).getBusinessKey(), taskVoList.get(0).getFormKey(), user
                allProcessRecordServices.addRecord(dto);
            }
        }
    }

    /**
     * 开启工作流并插入操作记录
     * @param workFlowDTO
     * @return
     */
    public String startProcessInstanceByKeyRecord(WorkFlowDTO workFlowDTO) {
        if(flowIsEnd()){
            return  returnVo;
        }

        if(StringUtils.isBlank(workFlowDTO.getDepartId())){
            workFlowDTO.setDepartId(LoginUtil.getJwtUser().getUser().getDepartOtherId());
        }
        String str = flowApiClient.startProcessInstanceByKey(workFlowDTO.getUserCode(), workFlowDTO.getProcessDefinitionKey()
                , workFlowDTO.getFormName(), workFlowDTO.getBusinessKey(), workFlowDTO.getVariables(),workFlowDTO.getDepartId());
        Boolean flowIsJump = companyFlowConfigService.getFlowIsJump(workFlowDTO.getProcessDefinitionKey());
        Boolean isAdd = companyFlowConfigService.getIsAddRecord(workFlowDTO.getProcessDefinitionKey());
        if(isAdd){
            AllProcessRecordDTO dto = new AllProcessRecordDTO();
            dto.setProcessDefinitionKey(workFlowDTO.getProcessDefinitionKey());
            dto.setBusinessKey(workFlowDTO.getBusinessKey());
            dto.setApprovalNode(StringUtils.isBlank(workFlowDTO.getOperation())?"tjr":null);
            dto.setUser(SecurityUtils.getJwtUser().getUser());
            dto.setOperation(StringUtils.isBlank(workFlowDTO.getOperation())?"提交":workFlowDTO.getOperation());
            dto.setMessage(workFlowDTO.getIsCZ()?"cz":"");
            //null,processDefinitionKey,businessKey,"tjr",SecurityUtils.getJwtUser().getUser()
            allProcessRecordServices.addRecord(dto);
        }
        if(workFlowDTO.getIsJump()){
            str = jumpProcessRecord(str,workFlowDTO.getUserCode(),flowIsJump,isAdd);
        }
        return str;
    }

    private String jumpProcessRecord(String str, String userCode, Boolean flowIsJump, Boolean isAdd) {
        String retStr = str;
        if(!jumpSameUser){
            return str;
        }
        ReturnVo re = JSONObject.parseObject(retStr, ReturnVo.class);
        // 判断流程是否结束
        if ("end".equals(re.getMsg())) {
            return retStr;
        }
        //判断推送到流程是否成功
        WorkFlowExceptionUtil.checkFlowResult(str);
        ReturnVo startJson = JSONObject.parseObject(str, ReturnVo.class);
        String process = flowApiClient.getProcessActivityVosByProcessInstanceId(userCode, startJson.getData().toString());
        //判断推送到流程是否成功
        WorkFlowExceptionUtil.checkFlowResult(process);
        ReturnVo processJson = JSONObject.parseObject(process, ReturnVo.class);
        JSONArray datas = JSON.parseArray(processJson.getData().toString());
        //没有跳过的阶段
        List<String> noJumpList = new ArrayList<>();
        List<String> jumpProcessIds = new ArrayList<>();
        List<String> jumpIgnoreProcessIds = new ArrayList<>();
        if(datas != null && datas.size()>0){
            JSONObject jsonObject = datas.getJSONObject(0);
            String key = jsonObject.getString("modelKey");
            if (key.contains(":")) {
                key = key.substring(0,key.indexOf(":"));
            }
            // 获取全流程空节点跳过配置
            if(null == flowIsJump){
                flowIsJump = redisCache.getCacheObject(key);
                if(null == flowIsJump){
                    flowIsJump = companyFlowConfigService.getFlowIsJump(key);
                    redisCache.setCacheObject(key,flowIsJump,14400000L,TimeUnit.MILLISECONDS);
                }
            }
            // 获取单个跳过节点配置
            String jumpKey = key + "jump:";
            jumpProcessIds = redisCache.getCacheObject(jumpKey);
            if(null == jumpProcessIds){
                jumpProcessIds = companyFlowJumpService.getCompanyFlowJumpId(key);
                redisCache.setCacheObject(jumpKey,jumpProcessIds,14400000L,TimeUnit.MILLISECONDS);
            }
            // 获取必审节点配置
            String jumpIgnoreKey = key + "jumpIgnore:";
            jumpIgnoreProcessIds = redisCache.getCacheObject(jumpIgnoreKey);
            if(null == jumpIgnoreProcessIds){
                jumpIgnoreProcessIds = companyFlowJumpService.IgnoreJump(key);
                redisCache.setCacheObject(jumpIgnoreKey,jumpIgnoreProcessIds,14400000L,TimeUnit.MILLISECONDS);
            }
            for(int i = 0;i<datas.size();i++){
                JSONObject nextJson = datas.getJSONObject(i);
                //获取当前审批节点，判断是否为必审节点，是的话直接返回
                if(ProcessStatusEnum.handling.getDesc().equals(nextJson.getString("status"))) {
                    if(jumpIgnoreProcessIds.contains(nextJson.getString("taskDefKey"))){
                        return retStr;
                    }
                }

            }
            // 获取下一个节点数据
            String taskUserlist = flowApiClient.getTaskUserByProcessInstanceId(null, startJson.getData().toString());
            JSONObject jsonObjectTask = JSON.parseObject(taskUserlist);
            List<TaskUserVo> taskUserVoList = JSONObject.parseArray(jsonObjectTask.getString("data"), TaskUserVo.class);
            List<String> nextUserPhone = new ArrayList<>();
            for (TaskUserVo taskUserVo : taskUserVoList) {
                nextUserPhone.add(taskUserVo.getUserCode());
            }
            for(int i = 0;i<datas.size();i++){
                re = JSONObject.parseObject(retStr, ReturnVo.class);
                // 判断流程是否结束
                if ("end".equals(re.getMsg())) {
                    return retStr;
                }
                JSONObject nextJson = datas.getJSONObject(i);
                //排除处理过的
                if(!ProcessStatusEnum.handling.getDesc().equals(nextJson.getString("status"))) {
                    String approverNo = nextJson.getString("approverNo");
                    if (StringUtils.isNotBlank(approverNo)) {
                        List<String> nextPhones = Arrays.asList(approverNo.split(";"));
                        for (String nextPhone : nextPhones){
                            if (nextUserPhone.contains(nextPhone)) {
                                //跳过处理
                                retStr = jumpHandleRecord(nextPhone,startJson.getData().toString(),isAdd);
                                retStr = jumpProcessRecord(retStr,userCode, flowIsJump,isAdd);
                                // 判断流程是否结束
                                return retStr;
                            } else {
                                noJumpList.add(nextJson.getString("approver"));
                            }
                        }
                    }else {
                        noJumpList.add(nextJson.getString("approver"));;
                    }
                    // 下一步审批人为空且设置跳过
                    if(null != flowIsJump && flowIsJump && CollectionUtil.isEmpty(nextUserPhone)) {
                        //跳过处理
                        retStr = jumpHandleRecord(userCode,startJson.getData().toString(), isAdd);
                        retStr = jumpProcessRecord(retStr,userCode, flowIsJump,isAdd);
                        re = JSONObject.parseObject(retStr, ReturnVo.class);
                        // 判断流程是否结束
                        if ("end".equals(re.getMsg())) {
                            return retStr;
                        }
                    }
                }
                // 判断处理中节点是否为可跳过节点，且审批人数据为空
                if(ProcessStatusEnum.handling.getDesc().equals(nextJson.getString("status"))){
                    if(jumpProcessIds.contains(nextJson.getString("taskDefKey"))
                            && StringUtils.isBlank(nextJson.getString("approverNo")) ){
                        //跳过处理
                        retStr = jumpHandleRecord(userCode,startJson.getData().toString(), isAdd);
                        retStr = jumpProcessRecord(retStr,userCode, flowIsJump, isAdd);
                        re = JSONObject.parseObject(retStr, ReturnVo.class);
                        // 判断流程是否结束
                        if ("end".equals(re.getMsg())) {
                            return retStr;
                        }
                    }
                }
            }
        }
        //说明全部跳过，到达最后节点
        if(noJumpList.size()<1){
            return returnVo;
        }
        return retStr;
    }

    private String jumpHandleRecord(String nextPhone, String processInstanceId, Boolean isAdd){
        ParamVo<TaskQueryParamsVo> paramVo = new ParamVo<>();
        // 查询条件
        TaskQueryParamsVo queryParamsVo = new TaskQueryParamsVo();
        //审批流程ids
        List<String> processInstanceIds  = new ArrayList<>();
        processInstanceIds.add(processInstanceId);
        queryParamsVo.setProcessInstanceIds(processInstanceIds);
        paramVo.setEntity(queryParamsVo);
        // 分页
        Query query = new Query();
        query.setPageNum(1);
        query.setPageSize(10);
        paramVo.setQuery(query);
        // 调用流程引擎接口查询代办列表
        String jsonReturnVo = flowApiClient.getAppingTaskList(paramVo);
        ReturnVo returnVo = JSON.parseObject(jsonReturnVo, ReturnVo.class);
        // 获取流程引擎数据
        PagerModel<TaskVo> remoteData =  JSON.parseObject(returnVo.getData().toString(), PagerModel.class);
        // 转换数据
        List<TaskVo> taskVoList = JSON.parseArray(remoteData.getData().toString(), TaskVo.class);
        if(taskVoList != null && taskVoList.size()>0) {
            //跳过当前审批  默认审批通过
            String result = flowApiClient.review(LoginUtil.getJwtUser().getUserOtherId(), processInstanceId, taskVoList.get(0).getTaskId(), "", true);
            if(isAdd){
                AllProcessRecordDTO dto = new AllProcessRecordDTO();
                dto.setUserCode(nextPhone);
                dto.setProcessDefinitionKey(taskVoList.get(0).getProcessDefinitionKey());
                dto.setBusinessKey(taskVoList.get(0).getBusinessKey());
                dto.setApprovalNode(taskVoList.get(0).getFormKey());
                dto.setOperation("同意");
                dto.setIsJump(CommonConstants.YES);
                //nextPhone, taskVoList.get(0).getProcessDefinitionKey(), taskVoList.get(0).getBusinessKey(), taskVoList.get(0).getFormKey(), null
                //添加审批记录
                allProcessRecordServices.addRecord(dto);
            }
            WorkFlowExceptionUtil.checkFlowResult(result);
            return result;
        }
        return null;
    }

    public String reviewRecord(WorkFlowDTO dto) {
        if(flowIsEnd()){
            return  returnVo;
        }
        addOtherRecord(SecurityUtils.getJwtUser().getUser(),dto.getProcessInstanceId(),dto.getIsAgree(),dto.getMessage());
        String review = null;
        if(null != dto.getNextUserCode() || null != dto.getNextTaskUserDepartCode()){
            review = flowApiClient.reviewNew(dto.getUserCode(), dto.getProcessInstanceId(), dto.getTaskId(), dto.getMessage(), dto.getIsAgree(),null,dto.getNextUserCode(),dto.getNextTaskUserDepartCode());
        } else {
            review = flowApiClient.review(dto.getUserCode(), dto.getProcessInstanceId(), dto.getTaskId(), dto.getMessage(), dto.getIsAgree());
        }
        if(dto.getIsAgree() && dto.getIsJump()){
            if(null != dto.getNextUserCode() || null != dto.getNextTaskUserDepartCode()){
                //跳过审批流
                review = jumpProcessV2(review, dto.getUserCode(),dto.getNextUserCode(),dto.getNextTaskUserDepartCode());
            }else{
                //跳过审批流
                review = jumpProcessRecord(review, dto.getUserCode(),null,true);
            }
        }
        return review;
    }


}
