package com.hzw.sunflower.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date ：Created in 2021/8/27 14:15
 * @description：标段/包按套销售查询信息
 * @version: 1.0
 */
@Data
@EqualsAndHashCode
public class SectionGroupDTO implements Serializable {

    @ApiModelProperty(value = "主键ID", position = 1)
    private Long id;

    @ApiModelProperty(value = "项目ID", position = 2)
    private Long projectId;

    @ApiModelProperty(value = "按套关联标段信息（前端展示）", position = 3)
    private String groupInfo;

    @ApiModelProperty(value = "按套关联标段id集合", position = 4)
    private String sectionIds;
}
