package com.hzw.ssm.expert.dao;

import com.hzw.ssm.sys.user.entity.UserEntity;

/**
 * 专家注册数据库Mapper
 * <AUTHOR> 2014-10-09
 *
 */
public interface RegisterMapper {

	/**
	 * 验证登录名是否存在
	 * @return
	 */
	public int checkLoginCodeExist(String login_code);
	
	/**
	 * 验证手机号码是否存在
	 * @param mobile
	 * @return
	 */
	public int checkMobileExist(String mobile);
	
	/**
	 * 注册保存
	 * @param userEntity
	 */
	public void doRegister(UserEntity userEntity);
}
