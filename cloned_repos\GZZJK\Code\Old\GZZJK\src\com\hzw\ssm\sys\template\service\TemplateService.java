package com.hzw.ssm.sys.template.service;

import java.util.List;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.hzw.ssm.sys.template.dao.TemplateMapper;
import com.hzw.ssm.sys.template.entity.TemplateEntity;

@Service
public class TemplateService {
	
	private static Log log = LogFactory.getLog(TemplateService.class);

	@Autowired
	private TemplateMapper templateMapper;
	/**
	 * 根据ID查询模板内容
	 * @param entity
	 * @return
	 */
	public TemplateEntity queryTemplateById(TemplateEntity entity) {
		
		return templateMapper.queryTemplateById(entity);
	}

	/**
	 * 根据条件查询模板列表
	 * @param entity
	 * @return
	 */
	public List<TemplateEntity> queryTemplateList(TemplateEntity entity){
		return templateMapper.queryTemplateList(entity);
	}
	
	/**
	 * 函数功能描述：根据id修改模板内容
	 * @param entity
	 * @return
	 */
	public int modifyTemplateById(TemplateEntity entity) {
		int count = templateMapper.modifyTemplateById(entity);
		return count;
	}
	
	/**
	 * 函数功能描述：插入模板
	 * @param entity
	 * @return
	 */
	public void insertTemplate(TemplateEntity entity) {
		 templateMapper.insertTemplate(entity);
	}
	
	public int deleteTemplateById(TemplateEntity entity) {
		return templateMapper.deleteTemplateById(entity);
	}
	/**
	 * 函数功能描述：校验在同一个模板类型下是否有其他的生效模板
	 * @param entity
	 * @return
	 */
	public int ajaxCheckIsValid(TemplateEntity entity){
		return  templateMapper.ajaxCheckIsValid(entity);
	}
}
