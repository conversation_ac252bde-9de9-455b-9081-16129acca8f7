<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper 
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hzw.ssm.sys.project.dao.DebarbMapper">
	<resultMap type="com.hzw.ssm.sys.project.entity.ExtractDebarbEntity" id="ExtractDebarbEntity">
        <result property="id" column="ID" javaType="java.lang.String" />
		<result property="decimationbatch" column="DECIMATIONBATCH" javaType="java.lang.String" />
		<result property="company" column="COMPANY" javaType="java.lang.String" />
		<result property="expertCode" column="EXPERT_CODE" javaType="java.lang.String" />
        <result property="expertPhone" column="EXPERT_PHONE" javaType="java.lang.String" />
        <result property="debarbReason" column="DEBARB_REASON" javaType="java.lang.String" />
        <result property="debarbType" column="DEBARB_TYPE" javaType="java.lang.String" />
        <result property="create_id" column="CREATE_USER" javaType="java.lang.String" />
        <result property="create_time" column="CREATE_TIME" javaType="java.util.Date" />
        <result property="DELETE_FLAG" column="delete_flag" javaType="java.lang.Integer" />
        
    </resultMap>
	<insert id="saveDebarbData" parameterType="ExtractDebarbEntity">
		insert into T_EXPERT_DEBARB(
		ID,
		DECIMATIONBATCH,
		COMPANY,
		EXPERT_CODE,
		CREATE_USER,
		CREATE_TIME,
		DEBARB_REASON , 
		DEBARB_TYPE,
		EXPERT_PHONE
		)
		values(
		#{id},
		#{decimationbatch},
		#{company,jdbcType=VARCHAR},
		#{expertCode,jdbcType=VARCHAR},
		#{create_id,jdbcType=VARCHAR},
		#{create_time, jdbcType=TIMESTAMP},
		#{debarbReason,jdbcType=CLOB},
		#{debarbType,jdbcType=VARCHAR},
		#{expertPhone,jdbcType=VARCHAR}
		)
	</insert>
	<select id="queryExtractDebarbList" parameterType="ExtractDebarbEntity" resultType="ExtractDebarbEntity">
		SELECT 
			T.ID,
			T.DECIMATIONBATCH,
			T.COMPANY,
			T.EXPERT_CODE AS expertCode,
			T.EXPERT_PHONE AS expertPhone,
			T.CREATE_USER,
			T.CREATE_TIME,
			T.DEBARB_REASON AS debarbReason,
			T.DEBARB_TYPE AS debarbType,
			ti.user_name AS expertName
		FROM T_EXPERT_DEBARB T
		LEFT JOIN t_Expert_Info ti on ti.id_no = t.expert_code  AND TI.DELETE_FLAG=0
		WHERE T.DECIMATIONBATCH = #{decimationbatch}
		<if test="debarbType!=null and debarbType !=''">
			AND T.DEBARB_TYPE = #{debarbType}
		</if>
		AND T.DELETE_FLAG=0 
	</select>
	
	<update id="deleteDebarbData" parameterType="ExtractDebarbEntity">
		UPDATE T_EXPERT_DEBARB T SET T.DELETE_FLAG=1 WHERE T.DECIMATIONBATCH = #{decimationbatch}
	</update>
</mapper>