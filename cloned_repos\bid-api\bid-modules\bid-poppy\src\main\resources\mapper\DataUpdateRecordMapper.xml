<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<!-- 数据修改记录表 (t_data_update_record) -->
<mapper namespace="com.hzw.sunflower.dao.DataUpdateRecordMapper">
    <insert id="insertAnnexFile">
        INSERT INTO t_relevancy_annex ( project_id, section_id, file_id, file_type, is_delete )
        VALUES ( #{projectId}, #{sectionId}, #{annexFile}, 3, 0 );
    </insert>
    <update id="updateBidWin">
        UPDATE t_bid_win set winbid_people_num = #{winPeopleNumber}
        WHERE section_id = #{sectionId} AND bid_round = #{bidRound} AND is_delete = 0
    </update>
    <update id="updateBidWinPeople">
        UPDATE t_bid_win_people
            SET winbid_people_num = #{winPeopleNumber}
            WHERE id IN (
            SELECT a.id FROM(
            SELECT p.id
            FROM t_bid_win_people p
            LEFT JOIN t_project_bid_section pbs ON p.project_id = pbs.project_id
            WHERE
            p.is_delete = 0
            and FIND_IN_SET( pbs.id, p.section_id )
            AND pbs.is_delete = 0
            AND pbs.id = #{sectionId}
            AND pbs.bid_round = #{bidRound}) a
            )
    </update>
    <update id="updateBidWinPeopleRecord">
        UPDATE t_bid_win_people_record
        SET winbid_people_num = #{winPeopleNumber}
        WHERE id IN ( SELECT a.id
        FROM ( SELECT p.id
        FROM t_bid_win_people_record p LEFT JOIN t_project_bid_section pbs on p.project_id = pbs.project_id
        WHERE p.is_delete = 0
        AND pbs.is_delete = 0
        and FIND_IN_SET( pbs.id, p.section_id )
        AND pbs.id = #{sectionId}
        AND pbs.bid_round = #{bidRound}) a
        )
    </update>
    <update id="updateBidDoc">
        UPDATE t_project_bid_doc
        SET annex_name = #{ossFileById.ossFileName},
        oss_file_id = #{ossFileById.id}
        WHERE id IN (SELECT a.id FROM(
        SELECT d.id FROM t_project_bid_doc d
            LEFT JOIN t_project_bid_doc_relation r ON r.doc_id = d.id
        WHERE r.section_id = #{sectionId} AND d.is_delete = 0 AND r.is_delete = 0) a
        )
    </update>
    <update id="updateAnnexFile">
        UPDATE t_relevancy_annex SET file_id = #{annexFile},is_delete = #{integer} WHERE FIND_IN_SET(section_id,#{sectionIds}) AND file_type = 3;
    </update>
    <update id="updateBidDocRecord">
        UPDATE t_doc_change_record
        SET oss_file_id = #{ossFileById.id}
        WHERE id IN (SELECT a.id FROM
            (SELECT d.id FROM t_doc_change_record d
            WHERE d.section_id = #{sectionId} AND d.is_delete = 0 ORDER BY d.updated_time DESC LIMIT 1 ) a)

    </update>

    <!-- 分页查询 -->
    <select id="listRecordPage" resultType="com.hzw.sunflower.controller.project.response.DataUpdateRecordVo">
        SELECT
            r.*,
            u.user_name
        FROM t_data_update_record r
        LEFT JOIN t_user u ON u.id = r.created_user_id
        WHERE 1 = 1
        and r.is_delete = 0
        and u.is_delete = 0
        <if test="condition.userId !=null and condition.userId != 0">
            and r.apply_user_id = #{condition.userId}
        </if>
        <if test="condition.departId !=null and condition.departId != 0">
            and r.apply_depart_id = #{condition.departId}
        </if>
        <if test="condition.type !=null and condition.type != ''">
            AND r.type = #{condition.type}
        </if>
        <if test="condition.startDate !=null and condition.startDate != ''">
            and DATE_FORMAT(r.created_time, '%Y-%m-%d') &gt;= #{condition.startDate}
        </if>
        <if test="condition.endDate !=null and condition.endDate != ''">
            and DATE_FORMAT(r.created_time, '%Y-%m-%d') &lt;= #{condition.endDate}
        </if>
        <if test="condition.keyWords != null and condition.keyWords != ''">
            and (r.content like CONCAT('%',#{condition.keyWords},'%') or u.user_name like CONCAT('%',#{condition.keyWords},'%'))
        </if>
        order by  r.created_time desc
    </select>


    <select id="queryList" resultType="com.hzw.sunflower.controller.project.response.DataUpdateRecordVo">
        SELECT
        r.*,
        u.user_name
        FROM t_data_update_record r
        LEFT JOIN t_user u ON u.id = r.created_user_id
        WHERE 1 = 1
          and r.is_delete = 0
          and u.is_delete = 0
          and r.process_code is not null
          and r.status = 1
        <if test="condition.type !=null and condition.type != ''">
            AND r.type = #{condition.type}
        </if>
        <if test="condition.keyWords != null and condition.keyWords != ''">
            and (r.content like CONCAT('%',#{condition.keyWords},'%'))
        </if>
        <if test="condition.taskCodes != null and condition.taskCodes.size() > 0 ">
        and r.process_code in
        <foreach collection="condition.taskCodes" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        </if>
        order by  r.created_time desc
    </select>

    <select id="queryListApp" resultType="com.hzw.sunflower.controller.project.response.DataUpdateRecordVo">
        SELECT
        r.*,
        u.user_name
        FROM t_data_update_record r
        LEFT JOIN t_user u ON u.id = r.created_user_id
        WHERE 1 = 1
        and r.is_delete = 0
        and u.is_delete = 0
        and r.process_code is not null
        and r.status = 1
        <if test="condition.type !=null and condition.type != ''">
            AND r.type = #{condition.type}
        </if>
        <if test="condition.keyWords != null and condition.keyWords != ''">
            and (r.content like CONCAT('%',#{condition.keyWords},'%'))
        </if>
        <if test="condition.taskCodes != null and condition.taskCodes.size() > 0 ">
            and r.process_code in
            <foreach collection="condition.taskCodes" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        order by  r.created_time
    </select>

    <!-- 查询app待办 -->
    <select id="queryListForApp" resultType="com.hzw.sunflower.controller.project.response.DataUpdateRecordVo">
        SELECT
        r.*,
        u.user_name
        FROM t_data_update_record r
        LEFT JOIN t_user u ON u.id = r.created_user_id
        WHERE 1 = 1
        and r.is_delete = 0
        and u.is_delete = 0
        and r.process_code is not null
        and r.status = 1
        <if test="condition.type !=null and condition.type != ''">
            AND r.type = #{condition.type}
        </if>
        <if test="condition.keyWords != null and condition.keyWords != ''">
            and (r.content like CONCAT('%',#{condition.keyWords},'%'))
        </if>
        <if test="condition.taskCodes != null and condition.taskCodes.size() > 0 ">
            and r.process_code in
            <foreach collection="condition.taskCodes" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        order by  r.created_time desc
    </select>

    <select id="queryBidWinNumber" resultType="java.lang.String">
        SELECT
            w.winbid_people_num
        FROM
            t_bid_win w
        WHERE
            w.bid_round = #{bidRound}
            AND w.section_id = #{id}
            AND w.is_delete = 0
    </select>
    <select id="queryBidWinPeople" resultType="java.lang.Long">
        SELECT a.id FROM(
            SELECT p.id
            FROM t_bid_win_people p
            LEFT JOIN t_project_bid_section pbs ON p.section_id = pbs.id
            WHERE
            p.is_delete = 0
            AND pbs.is_delete = 0
            AND pbs.id = #{sectionId}
            AND pbs.bid_round = #{bidRound}) a
    </select>
    <select id="queryBidWinPeopleRecord" resultType="java.lang.Long">
        SELECT a.id FROM(
            SELECT p.id
            FROM t_bid_win_people_record p
            LEFT JOIN t_project_bid_section pbs ON p.section_id = pbs.id
            WHERE
            p.is_delete = 0
            AND pbs.is_delete = 0
            AND pbs.id = #{sectionId}
            AND pbs.bid_round = #{bidRound}) a
    </select>
    <select id="findDoc" resultType="com.hzw.sunflower.controller.project.response.ProjectBidSectionVO">
        SELECT
            r.project_id,
            r.section_id id,
            r.doc_id,
            d.package_number packageNumbers,
            d.annex_name docFileName,
            d.oss_file_id docFile,
            o.oss_file_name annexFileName,
            a.file_id annexFile
        FROM
            t_project_bid_doc d
            LEFT JOIN t_project_bid_doc_relation r ON r.doc_id = d.id
            LEFT JOIN t_relevancy_annex a ON a.section_id = r.section_id
            AND a.file_type = 3
            AND a.is_delete = 0
            LEFT JOIN t_oss_file o ON o.id = a.file_id
        WHERE
            r.section_id = #{sectionId}
            AND d.is_delete = 0
            AND r.is_delete = 0
        LIMIT 1
    </select>
    <select id="selectAnnexFile" resultType="java.lang.Long">
        SELECT distinct a.file_id FROM t_relevancy_annex a WHERE FIND_IN_SET(a.section_id,#{sectionIds}) and a.is_delete = 0
    </select>

    <select id="getInfoByProjectId" resultType="com.hzw.sunflower.entity.DataUpdateRecord">
            SELECT * FROM t_data_update_record where is_delete = 0 and status =1
            <if test="projectId !=null">
                and request_json like CONCAT('%','"projectId":',#{projectId},'%')
            </if>
            <if test="sectionId !=null">
                and request_json like CONCAT('%','"sectionId":',#{sectionId},'%')
            </if>
            <if test="type !=null">
                and type =#{type}
            </if>
            <if test="sectionIds !=null">
                <foreach collection="sectionIds"  item="item" open="AND (" separator="or" close=")">
                     request_json like CONCAT('%','sectionIds%',#{item},'%')
                </foreach>
            </if>
    </select>

</mapper>
