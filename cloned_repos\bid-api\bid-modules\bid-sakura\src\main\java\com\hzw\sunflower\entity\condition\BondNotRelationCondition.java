package com.hzw.sunflower.entity.condition;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hzw.sunflower.common.BaseCondition;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.sql.Date;

@ApiModel(description = "未关联流水查询条件")
@Data
public class BondNotRelationCondition extends BaseCondition {

    @ApiModelProperty(value = "交易金额")
    private String amount;

    @ApiModelProperty(value = "交易状态")
    private Integer status;

    @ApiModelProperty(value = "交易日期")
    @JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd")
    private Date date;

    @ApiModelProperty(value = "公司名称")
    private String companyName;

}
