package com.hzw.sunflower.controller.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 公司流程配置表
 */
@Data
public class CompanyFlowConfigReq {

    @ApiModelProperty(value = "企业id")
    private Long companyId;

    @ApiModelProperty(value = "部门id")
    private Long deptId;

    @ApiModelProperty(value = "业务编号")
    private String businessCode;

    @ApiModelProperty(value = "流程归属类型  1 总公司  2 分公司")
    private Integer businessType;
}