package com.hzw.sunflower.controller.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class SystemSettingReq {
    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "系统名称")
    private String systemName;

    @ApiModelProperty(value = "系统简称")
    private String systemAbbreviation;

    @ApiModelProperty(value = "登录页背景")
    private Long pageBackgroundId;

    @ApiModelProperty(value = "登录页url")
    private String pageBackgroundUrl;

    @ApiModelProperty(value = "logo")
    private Long logoId;

    @ApiModelProperty(value = "域名备案")
    private String domainFiling;

    @ApiModelProperty(value = "联网信息备案")
    private String onlineInformationFiling;

    @ApiModelProperty(value = "版权信息")
    private String copyrightInformation;

    @ApiModelProperty(value = "客服电话")
    private String customerServiceTelephone;

    @ApiModelProperty(value = "注册协议")
    private Long registrationAgreementId;

    @ApiModelProperty(value = "系统主色")
    private String systemColor;
}
