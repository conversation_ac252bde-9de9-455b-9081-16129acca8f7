package com.hzw.sunflower.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.hzw.sunflower.common.BaseBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * Created by sz on 2021/06/25
 *
 * <AUTHOR>
 */
@ApiModel("招标文件表")
@TableName("t_project_bid_doc")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProjectBidDoc extends BaseBean {

    private static final long serialVersionUID = -35408232177018412L;

    /**
     * 主键ID
     *
     * @mbg.generated
     */
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 项目ID
     *
     * @mbg.generated
     */
    @ApiModelProperty(value = "项目ID")
    private Long projectId;

    /**
     * 关联标段(冗余字段)
     *
     * @mbg.generated
     */
    @ApiModelProperty(value = "关联标段(冗余字段)")
    private String packageNumber;

    /**
     * 招标文件发售开始时间
     *
     * @mbg.generated
     */
    @ApiModelProperty(value = "招标文件发售开始时间")
    private Date saleStartTime;

    /**
     * 招标文件发售结束时间类型（1：确定时间; 2：另行通知; 3：投标文件递交截止时间前一天）
     *
     * @mbg.generated
     */
    @ApiModelProperty(value = "招标文件发售结束时间类型")
    private Integer saleEndTimeType;

    /**
     * 招标文件发售结束时间
     *
     * @mbg.generated
     */
    @ApiModelProperty(value = "招标文件发售结束时间")
    private Date saleEndTime;

    /**
     * 招标文件进度 1：起草中 2：待确认 3：已撤回 4：已确认 5：已发布 6：已退回
     *
     * @mbg.generated
     */
    @ApiModelProperty(value = "招标文件进度 1：起草中 2：待确认 3：已撤回 4：已确认 5：已发布 6：已退回")
    private Integer noticeProgress;

    /**
     * 招标文件状态 1：正式 2：暂存
     *
     * @mbg.generated
     */
    @ApiModelProperty(value = "招标文件状态 1：正式 2：暂存")
    private Integer state;

    /**
     * 联系人
     *
     * @mbg.generated
     */
    @ApiModelProperty(value = "联系人")
    private String contact;

    /**
     * 手机号
     *
     * @mbg.generated
     */
    @ApiModelProperty(value = "手机号")
    private String phone;

    /**
     * 招标文件名称
     *
     * @mbg.generated
     */
    @ApiModelProperty(value = "招标文件名称")
    private String annexName;

    /**
     * oss文件ID
     */
    @ApiModelProperty(value = "oss文件ID")
    private Long ossFileId;


    /**
     * 费用类型 1.标书费 2.平台服务费
     */
    @ApiModelProperty(value = "费用类型 1.标书费 2.平台服务费")
    private Integer feeType;

    /**
     * 标书费支付方式
     *
     * @mbg.generated
     */
    @ApiModelProperty(value = "标书费支付方式  0:线下 1:线上 2:线下线上")
    private Integer paymentType;

    /**
     * 标书费是否收费1 收费 ，2免费
     *
     * @mbg.generated
     */
    @ApiModelProperty(value = "标书费是否收费1 收费 ，2免费")
    private Integer tenderFeeFlag;

    /**
     * 招标文件售价说明（1：交通；2：水利；3：其他）
     */
    @ApiModelProperty(value = "招标文件售价说明（1：交通；2：水利；3：其他）")
    private Integer filePriceState;

    /**
     * 文件售价说明其他时具体描述
     */
    @ApiModelProperty(value = "文件售价说明其他时具体描述")
    private String otherPriceDescribe;

    /**
     * 撤回待确认保存历史文件进度（2：待确认；4：已确认）
     */
    @ApiModelProperty(value = "撤回待确认保存历史文件进度（2：待确认；4：已确认）")
    private Integer oldProgress;

    /**
     * 已购买招标文件的投标人本次是否收费
     */
    @ApiModelProperty(value = "已购买招标文件的投标人本次是否收费  0:免费 1:收费")
    private Integer free;

    /**
     * 招标文件阶段： 0：默认无阶段； 1：第一轮； 2：第二轮
     */
    @ApiModelProperty(value = "招标文件阶段： 0：默认无阶段； 1：第一轮； 2：第二轮")
    private Integer bidRound;

    /**
     * 挂网时间
     *
     * @mbg.generated
     */
    @ApiModelProperty(value = "挂网时间")
    private Date releaseTime;

    /**
     * 提交时间
     *
     * @mbg.generated
     */
    @ApiModelProperty(value = "提交时间")
    private Date submitTime;
}
