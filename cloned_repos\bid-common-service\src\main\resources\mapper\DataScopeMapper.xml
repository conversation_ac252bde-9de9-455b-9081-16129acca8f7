<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzw.sunflower.dao.DataScopeMapper">

    <select id="queryRoleByMenuId" resultType="com.hzw.sunflower.entity.Role">
        select  distinct
            t3.*
        from
            t_role_menu t1
        left join r_depart_role t2 on t1.role_id = t2.role_id
        left join t_role t3 on t3.id = t2.role_id
        where
            t2.is_delete = 0
        and t1.menu_id = #{menuId}
        and t2.user_id = #{user.id}
        and t2.depart_id = #{user.departId}
    </select>
</mapper>