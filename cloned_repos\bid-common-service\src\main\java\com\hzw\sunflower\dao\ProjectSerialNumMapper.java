package com.hzw.sunflower.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hzw.sunflower.entity.ProjectSerialNum;
import org.springframework.data.repository.query.Param;

/**
 * 项目流水表Mapper接口
 *
 * <AUTHOR>
 * @version 1.0.0 2021-04-13
 */
public interface ProjectSerialNumMapper extends BaseMapper<ProjectSerialNum> {


    Integer getYearMaxSerialNum(@Param("year") String year, @Param("type") Integer type);
    String selectDepartCodeById(@Param("id") Long id);

    String selectDepartProjectCodeById(@Param("id") Long id);

    String selectDepartCodeByUserId(@Param("userId")  Long userId);

    Integer insetMaxNumByYearType(@Param("req")  ProjectSerialNum projectSerialNum);
}