package com.hzw.sunflower.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzw.sunflower.client.WorkFlowApiClient;
import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.constant.*;
import com.hzw.sunflower.constant.constantenum.*;
import com.hzw.sunflower.controller.request.ApprovalOpinionREQ;
import com.hzw.sunflower.controller.request.CommonNoticeREQ;
import com.hzw.sunflower.controller.request.NoticeAuditReq;
import com.hzw.sunflower.controller.response.ProjectBidNoticeVO;
import com.hzw.sunflower.dao.*;
import com.hzw.sunflower.dto.AppOaMsgDto;
import com.hzw.sunflower.dto.ApprovalOpinionDTO;
import com.hzw.sunflower.dto.MediaDTO;
import com.hzw.sunflower.dto.PackageInfoDTO;
import com.hzw.sunflower.entity.*;
import com.hzw.sunflower.entity.condition.ApprovalOpinionCondition;
import com.hzw.sunflower.exception.ExceptionEnum;
import com.hzw.sunflower.exception.SunFlowerException;
import com.hzw.sunflower.exception.WorkFlowException;
import com.hzw.sunflower.freesia.client.constant.FlowClientConstant;
import com.hzw.sunflower.freesia.client.vo.flowable.ReturnVo;
import com.hzw.sunflower.freesia.client.vo.flowable.task.TaskUserVo;
import com.hzw.sunflower.security.utils.SecurityUtils;
import com.hzw.sunflower.service.*;
import com.hzw.sunflower.util.WorkFlowExceptionUtil;
import lombok.extern.java.Log;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;


/**
 * TApprovalOpinionService接口实现
 *
 * <AUTHOR>
 * @version 1.0.0 2021-04-07
 */

@Log
@Service
public class ApprovalOpinionServiceImpl extends ServiceImpl<ApprovalOpinionMapper, ApprovalOpinion> implements ApprovalOpinionService {
    @Autowired
    private ProjectBidNoticeMapper tProjectBidNoticeMapper;

    @Autowired
    private ApprovalOpinionMapper approvalOpinionMapper;

    @Autowired
    private NoticePackageRService packageRService;


    @Autowired
    private NoticeChangeRecordService noticeChangeRecordService;

    @Autowired
    private ProjectBidNoticeService tProjectBidNoticeService;

    @Autowired
    private WorkFlowApiClient workFlowApiClient;

    @Autowired
    private ProjectService projectService;

    @Autowired
    private ProjectBidNoticeService noticeService;

    @Autowired
    private RequestToDTOService dtoService;

    @Autowired
    private AllMediaService allMediaService;
    @Autowired
    private ProjectBidDocService projectBidDocService;

    @Autowired
    private CommonSectionService commonSectionService;

    @Autowired
    private NoticePackageRMapper noticePackageRMapper;

    @Autowired
    private ProjectBidDocRelationMapper projectBidDocRelationMapper;

    @Autowired
    private PendingItemService pendingItemService;

    @Autowired
    private ProjectBidSectionMapper projectBidSectionMapper;

    @Autowired
    private CommonMqService commonMqService;

    /**
     * 根据条件分页查询列表
     *
     * @param condition 查询条件
     * @return 分页数据
     */
    @Override
    public IPage<ApprovalOpinion> findInfoByCondition(ApprovalOpinionCondition condition) {
        IPage<ApprovalOpinion> page = condition.buildPage();
        QueryWrapper<ApprovalOpinion> queryWrapper = condition.buildQueryWrapper(ApprovalOpinion.class);
        return this.page(page, queryWrapper);
    }

    /**
     * 根据主键ID查询
     *
     * @param id 主键ID
     * @return
     */
    @Override
    public ApprovalOpinion getInfoById(Long id) {
        return this.getById(id);
    }



    /**
     * 根据主键ID删除
     *
     * @param id 主键ID
     * @return 是否成功
     */
    @Override
    public Boolean deleteById(Long id) {
        return this.removeById(id);
    }

    /**
     * 根据主键ID列表批量删除
     *
     * @param idList 主键ID列表
     * @return 是否成功
     */
    @Override
    public Boolean deleteByIds(List<Long> idList) {
        return this.removeByIds(idList);
    }

    @Override
    public List<ApprovalOpinionDTO> getInfoByDocId(Long docId) {
        return this.baseMapper.getInfoByDocId(docId);
    }

    /**
     * 查询处理进度
     *
     * @param busId
     * @return
     */
    @Override
    public List<ApprovalOpinionDTO> getInfoByNoticeId(Long busId) {
        return this.baseMapper.getInfoByNoticeId(busId);
    }

    /**
     * 查询待审批公告的详细信息
     *
     * @param req 通用请求实体
     * @return 完整公告信息
     */
    @Override
    public Result<ProjectBidNoticeVO> findApprovalingNotice(CommonNoticeREQ req) {

        ProjectBidNoticeVO vo = tProjectBidNoticeService.queryEditNoticeInfo(req).getData();
        List<PackageInfoDTO> packageInfo = vo.getPackageInfo();
        ArrayList<PackageInfoDTO> actuallyPackages = new ArrayList<>();
        packageInfo.forEach((i) -> {
            if((i.getNoticeId() != null) && (i.getNoticeId().equals(vo.getNoticeId()))){
                actuallyPackages.add(i);
            }
        });
        vo.setPackageInfo(actuallyPackages);

        return Result.ok(vo);
    }


    /**
     * 公告审批记录
     *
     * @param req 公告通用请求
     * @return 公告审批记录的列表
     */
    @Override
    public Result<List<ApprovalOpinionDTO>> findNoticeApprovalRecords(CommonNoticeREQ req) {

        ProjectBidNotice notice = tProjectBidNoticeMapper.selectById(req.getNoticeId());
        if(notice == null){
            throw new SunFlowerException(ExceptionEnum.NOTICE_NOT_FOUND,null);
        }else{
            if(notice.getNoticeProgress() < NoticeProgressEnum.UNDERREVIEW.getValue()){
               // throw new SunFlowerException(ExceptionEnum.NOTICE_OPERATION_FAILED,null);
                // 徐童说改成这样
                return Result.ok();
            }
        }

        List<ApprovalOpinionDTO> opinions = approvalOpinionMapper.getInfoByNoticeId(req.getNoticeId());

        if((opinions == null) || (opinions.size() == 0)){
            throw new SunFlowerException(ExceptionEnum.NOTICE_NOT_FOUND,null);
        }
        return Result.ok(opinions);
    }

    /**
     * 公告提交审批
     *
     * @param  notice 招标公告实体
     * @param packageIds 招标公告关联包段ID集合
     * @return true代表成功，其他代表失败
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> insertSubmitNotice(ProjectBidNotice notice,List<Long> packageIds,NoticeChangeRecord records) {
        // 判断招标公告是否可以提交审核
        if(!canSubmit(notice)){
            throw new SunFlowerException(ExceptionEnum.NOTICE_PARAMS_NOT_COMPLETE,ExceptionEnum.NOTICE_PARAMS_NOT_COMPLETE.getMessage());
        }
        // 更新个人媒体使用次数
//        mediaService.updateMediaUseTimes(notice.getId());

        // 设置公告状态
        notice.setNoticeProgress(NoticeProgressEnum.UNDERREVIEW.getValue());
        notice.setState(1);

        String nextApproves="";
        ApprovalOpinion opinion = initApprovalOpinion(notice).setApprovalType(ApprovalTypeEnum.COMMIT.getValue());
        // 提交流程引擎
        JSONObject jsonObject  = noticeSubmit(notice,FlowClientConstant.PROCESS_DEFINITION_KEY_NOTICE_CONTENT);

        if(!jsonObject.getString("code").equals(ReturnCodeConstants.PROCESS_SUCCESS_CODE)){
            throw new WorkFlowException(ExceptionEnum.SUBMIT_FAILED.getCode(),jsonObject.getString("msg"));
        }else{
            opinion.setApprovalType(ApprovalTypeEnum.COMMIT.getValue());
            // 判断流程是否结束
            if ("end".equals(jsonObject.getString("msg"))){
                notice.setNoticeProgress(NoticeProgressEnum.REVIEW.getValue());

                // 发送app待办
                AppOaMsgDto dto = new AppOaMsgDto();
                dto.setTaskType(AppTaskTypeEnum.TENDER_BID.getCode());
                List<String> recipientList = new ArrayList<>();
                if (notice.getCreatedUserId() != null) {
                    recipientList.add(notice.getCreatedUserId().toString());
                }
                dto.setRecipientList(recipientList);
                commonMqService.sendOaMsg(dto);

            }else {
                // 查询下一步审核人
                String  processInstanceId = jsonObject.getString("data");
                String taskUserlist = workFlowApiClient.getTaskUserByProcessInstanceId(SecurityUtils.getJwtUser().getUserOtherId(), processInstanceId);
                JSONObject jsonObjectTask = JSON.parseObject(taskUserlist);
                List<TaskUserVo> taskUserVoList = JSONObject.parseArray(jsonObjectTask.getString("data"), TaskUserVo.class);

                List<String> recipientList = new ArrayList<>();
                for (TaskUserVo taskUserVo : taskUserVoList) {
                    if (!"".equals(nextApproves)){
                        nextApproves+=","+taskUserVo.getUserName();
                    }else {
                        nextApproves=taskUserVo.getUserName();
                    }
                    recipientList.add(taskUserVo.getUserCode());
                }

                opinion.setNextUserName(nextApproves);

                // 发送app待办
                AppOaMsgDto dto = new AppOaMsgDto();
                dto.setTaskType(AppTaskTypeEnum.NOTICE_CONTENT.getCode());
                dto.setRecipientList(recipientList);
                commonMqService.sendOaMsg(dto);
            }

        }
        // 设置提交时间
        notice.setSubmitTime(new Date());
        // 更新招标公告状态
        if(!tProjectBidNoticeService.updateById(notice)){
            throw new SunFlowerException(ExceptionEnum.SUBMIT_FAILED,ExceptionEnum.SUBMIT_FAILED.getMessage());
        }
        // 提交审批记录
        if(!save(opinion)){
            throw new SunFlowerException(ExceptionEnum.SUBMIT_FAILED,ExceptionEnum.SUBMIT_FAILED.getMessage());
        }

        // 保存提交记录
        if(!noticeChangeRecordService.save(records)){
            throw new SunFlowerException(ExceptionEnum.SUBMIT_FAILED,ExceptionEnum.SUBMIT_FAILED.getMessage());
        }
        // 更新媒体
        allMediaService.backupsMedia(notice.getId(),MediaTypeEnum.TENDER_BULLETIN.getCode(), records.getId());

        // 更新关联的标段表状态
        if(!commonSectionService.updateSectionStatus(packageIds, PackageStatusEnum.READONLY_BY_NOTICE.getValue())){
            throw new SunFlowerException(ExceptionEnum.SUBMIT_FAILED,ExceptionEnum.SUBMIT_FAILED.getMessage());
        }
        return Result.ok(true);
    }

    /**
     * 公告审批结果
     * @param req 审批请求实体
     * @return 公告审批记录的列表
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean insertApprovalNotice(ApprovalOpinionREQ req,ProjectBidNotice notice) {

        ApprovalOpinion opinion = initApprovalOpinion(notice);
        List<Long> ids = packageRService.findNoticePackageIdsByNoticeId(notice.getId());
        // 提交流程引擎，判断是否成功，成功返回码为100
        ReturnVo returnVo = noticeApproval(req);
        // 同意
        if(req.getIsAgree()){
            opinion.setApprovalType(ApprovalTypeEnum.AGREE.getValue());
            //如果流程结束了，则状态为已确认
            if ("end".equals(returnVo.getMsg())) {
                notice.setNoticeProgress(NoticeProgressEnum.REVIEW.getValue());

                // 发送app待办
                AppOaMsgDto dto = new AppOaMsgDto();
                dto.setTaskType(AppTaskTypeEnum.TENDER_BID.getCode());
                List<String> recipientList = new ArrayList<>();
                if (notice.getCreatedUserId() != null) {
                    recipientList.add(notice.getCreatedUserId().toString());
                }
                dto.setRecipientList(recipientList);
                commonMqService.sendOaMsg(dto);
            }else{
                String nextApproves="";
                // 查询下一步审核人
                String  processInstanceId = req.getProcessInstanceId();
                String taskUserlist = workFlowApiClient.getTaskUserByProcessInstanceId(SecurityUtils.getJwtUser().getUserOtherId(), processInstanceId);
                log.info(taskUserlist);
                JSONObject jsonObjectTask = JSON.parseObject(taskUserlist);
                List<TaskUserVo> taskUserVoList = JSONObject.parseArray(jsonObjectTask.getString("data"), TaskUserVo.class);

                List<String> recipientList = new ArrayList<>();
                for (TaskUserVo taskUserVo : taskUserVoList) {
                    if (!"".equals(nextApproves)){
                        nextApproves+=","+taskUserVo.getUserName();
                    }else {
                        nextApproves=taskUserVo.getUserName();
                    }
                    recipientList.add(taskUserVo.getUserCode());
                }

                opinion.setNextUserName(nextApproves);
                // 发送app待办
                AppOaMsgDto dto = new AppOaMsgDto();
                dto.setTaskType(AppTaskTypeEnum.NOTICE_CONTENT.getCode());
                dto.setRecipientList(recipientList);
                commonMqService.sendOaMsg(dto);
            }
        }else {
            // 修改标段状态
            if(!commonSectionService.updateSectionStatus(ids,PackageStatusEnum.EDIT_BY_NOTICE.getValue())){
                throw new SunFlowerException(ExceptionEnum.APPROVAL_FAILED,ExceptionEnum.APPROVAL_FAILED.getMessage());
            }
            notice.setNoticeProgress(NoticeProgressEnum.RETURN.getValue());
            opinion.setApprovalType(ApprovalTypeEnum.DRAWBACK.getValue());
        }

        // 填充审批意见，防止填充null
        if(StringUtils.isNoneEmpty(req.getApprovalOpinion())){
            opinion.setApprovalContent(req.getApprovalOpinion());
        }else{
            opinion.setApprovalContent("");
        }

        // 保存审批记录
        if(!save(opinion)){
            throw new SunFlowerException(ExceptionEnum.APPROVAL_FAILED,ExceptionEnum.APPROVAL_FAILED.getMessage());
        } else {
            // 如果是退回，添加待处理事项表
            if (opinion.getApprovalType() == 2) {
                PendingItem pendingItem = new PendingItem();
                // 采购公告退回
                if (opinion.getNoticeId() != null) {
                    // 查询采购公告关联标段
                    LambdaQueryWrapper<NoticePackageR> noticePackageRLambdaQueryWrapper =  new LambdaQueryWrapper<>();
                    noticePackageRLambdaQueryWrapper.eq(NoticePackageR::getNoticeId,opinion.getNoticeId());
                    List<NoticePackageR> noticePackageRList = noticePackageRMapper.selectList(noticePackageRLambdaQueryWrapper);
                    StringBuilder sectionIds = new StringBuilder();
                    List<Long> sectionList = new ArrayList<>();
                    for (NoticePackageR noticePackageR : noticePackageRList) {
                        sectionList.add(noticePackageR.getSectionId());
                        sectionIds.append(noticePackageR.getSectionId()).append(",");
                    }
                    sectionIds = new StringBuilder(sectionIds.substring(0, sectionIds.length() - 1));

                    pendingItem.setOperationTime(opinion.getApprovalTime());
                    pendingItem.setBusinessCode(ReturnListEnum.TENDER_BID.getCode());
                    pendingItem.setBusinessId(opinion.getNoticeId());
                    pendingItem.setBusinessType(2);
                    pendingItem.setProjectId(opinion.getProjectId());
                    pendingItem.setSectionId(sectionIds.toString());
                    //查询标段轮次
                    LambdaQueryWrapper<ProjectBidSection> projectBidSectionLambdaQueryWrapper = new LambdaQueryWrapper<>();
                    projectBidSectionLambdaQueryWrapper.in(ProjectBidSection::getId,sectionList);
                    List<ProjectBidSection> projectBidSections = projectBidSectionMapper.selectList(projectBidSectionLambdaQueryWrapper);
                    pendingItem.setBidRound(projectBidSections.get(0).getBidRound());
                    // 查询申请人和申请时间
                    ProjectBidNotice projectBidNotice = tProjectBidNoticeMapper.selectById(opinion.getNoticeId());
                    LambdaQueryWrapper<ApprovalOpinion> approvalOpinionLambdaQueryWrapper = new LambdaQueryWrapper<>();
                    approvalOpinionLambdaQueryWrapper.eq(ApprovalOpinion::getApprovalUserId,projectBidNotice.getCreatedUserId());
                    approvalOpinionLambdaQueryWrapper.eq(ApprovalOpinion::getNoticeId,projectBidNotice.getId());
                    approvalOpinionLambdaQueryWrapper.orderByDesc(ApprovalOpinion::getCreatedTime);
                    approvalOpinionLambdaQueryWrapper.last("limit 1");
                    ApprovalOpinion approvalOpinion = approvalOpinionMapper.selectOne(approvalOpinionLambdaQueryWrapper);
                    pendingItem.setApplyTime(approvalOpinion.getApprovalTime());
                    pendingItem.setApplyUserId(approvalOpinion.getApprovalUserId());

                    // 发送app待阅
                    commonMqService.sendOaReadMsg(ReturnListEnum.TENDER_BID.getDesc(), pendingItem);
                }
                pendingItemService.save(pendingItem);
            }
        }
        // 同步项目状态
//       noticeService.syncProjectStatus(notice.getProjectId());
        if(!tProjectBidNoticeService.updateById(notice)){
            throw new SunFlowerException(ExceptionEnum.APPROVAL_FAILED,ExceptionEnum.APPROVAL_FAILED.getMessage());
        }
        return true;
    }

    /**
     * 公告撤回
     *
     * @param notice 招标公告实体
     * @return true代表成功，false代表失败
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean insertWithdrawNotice(ProjectBidNotice notice) {

        Integer progress = notice.getNoticeProgress();

        // 招标公告关联的包段ID集合
        List<Long> packageIds = packageRService.findNoticePackageIdsByNoticeId(notice.getId());

        ApprovalOpinion opinion = new ApprovalOpinion();

        if(progress.equals(NoticeProgressEnum.UNDERREVIEW.getValue())){
            // 待确认状态撤回  被第一个审批借点已确认的公告(处长)撤回要变成撤回待确认
            // 公告表里都是待确认状态无法区分 去查流程引擎状态
            // 没有流程节点查询 只能t_project_bid_notice查询节点状态
            // 已撤回
            notice.setNoticeProgress(NoticeProgressEnum.WITHDRAWAL.getValue());

            // 判断是否成功从流程引擎中撤回
            JSONObject jsonObject = noticeWithdraw(notice,NoticeProgressEnum.WITHDRAWAL.getName());
            if(!jsonObject.getString("code").equals(ReturnCodeConstants.PROCESS_SUCCESS_CODE)){
                throw new WorkFlowException(ExceptionEnum.WITHDRAW_FAILED.getCode(),jsonObject.getString("msg"));
            }else{

                // 撤回成功
                List<ProjectBidSection> sections = new ArrayList<>();
                for(Long sectionId : packageIds){
                    ProjectBidSection bidSection = new ProjectBidSection();
                    bidSection.setId(sectionId);
                    if(projectBidDocService.judgeStatusByDoc(sectionId)){
                        bidSection.setStatus(PackageStatusEnum.EDIT_BY_NOTICE.getValue().toString());
                    }
                    sections.add(bidSection);
                }
                //更新标段状态
                commonSectionService.updateSectionStatus(sections);

                // 同步项目状态
                noticeService.syncProjectStatus(notice.getProjectId());
                // 撤回成功以后刷新项目中的状态
                //commonSectionService.saveOrUpdateProjectPackageFieldDealWith(notice.getProjectId());
            }

            opinion = initApprovalOpinion(notice).setApprovalType(ApprovalTypeEnum.REVIEW.getValue());

        }else if(progress.equals(NoticeProgressEnum.REVIEW.getValue())){

            // 公告审核已经完成，此时项目经理撤回需要发起撤回申请
            JSONObject jsonObject = noticeSubmit(notice,FlowClientConstant.PROCESS_DEFINITION_KEY_CH_NOTICE_CONTENT);
            if(!jsonObject.getString("code").equals(ReturnCodeConstants.PROCESS_SUCCESS_CODE)){
                throw new WorkFlowException(ExceptionEnum.WITHDRAW_FAILED.getCode(),jsonObject.getString("msg"));
            }else {
                // 判断是否结束
                if ("end".equals(jsonObject.getString("msg"))){
                    notice.setNoticeProgress(NoticeProgressEnum.WITHDRAWAL.getValue());
                    // 撤回成功
                    List<ProjectBidSection> sections = new ArrayList<>();
                    for(Long sectionId : packageIds){
                        ProjectBidSection bidSection = new ProjectBidSection();
                        bidSection.setId(sectionId);
                        if(projectBidDocService.judgeStatusByDoc(sectionId)){
                            bidSection.setStatus(PackageStatusEnum.EDIT_BY_NOTICE.getValue().toString());
                        }
                        sections.add(bidSection);
                    }
                    //更新标段状态
                    commonSectionService.updateSectionStatus(sections);
                    opinion = initApprovalOpinion(notice).setApprovalType(ApprovalTypeEnum.REVIEW.getValue());
                }else {
                    //公告状态变为确认中
                    notice.setNoticeProgress(NoticeProgressEnum.WITHDRAWUNDERREVIEW.getValue());
                    //更新标段状态
                    if(!commonSectionService.updateSectionStatus(packageIds,PackageStatusEnum.READONLY_BY_NOTICE.getValue())){
                        throw new SunFlowerException(ExceptionEnum.WITHDRAW_FAILED,ExceptionEnum.WITHDRAW_FAILED.getMessage());
                    }
                    opinion = initApprovalOpinion(notice).setApprovalType(ApprovalTypeEnum.APPLY_REVIEW.getValue());
                    String taskUserlist = workFlowApiClient.getTaskUserByProcessInstanceId(null, jsonObject.getString("data"));
                    JSONObject jsonObjectTask = JSON.parseObject(taskUserlist);
                    List<TaskUserVo> taskUserVoList = JSONObject.parseArray(jsonObjectTask.getString("data"), TaskUserVo.class);
                    String nextApproves="";
                    List<String> recipientList = new ArrayList<>();
                    for (TaskUserVo taskUserVo : taskUserVoList) {
                        if (!"".equals(nextApproves)){
                            nextApproves+=","+taskUserVo.getUserName();
                        }else {
                            nextApproves=taskUserVo.getUserName();
                        }
                        recipientList.add(taskUserVo.getUserCode());
                    }
                    opinion.setNextUserName(nextApproves);

                    // 发送app待办
                    AppOaMsgDto dto = new AppOaMsgDto();
                    dto.setTaskType(AppTaskTypeEnum.NOTICE_CONTENT.getCode());
                    dto.setRecipientList(recipientList);
                    commonMqService.sendOaMsg(dto);
                }
            }

            //如果是再次申请撤回，删除待处理事项表数据
            LambdaQueryWrapper<PendingItem> pendingItemLambdaQueryWrapper = new LambdaQueryWrapper<>();
            pendingItemLambdaQueryWrapper.eq(PendingItem::getBusinessCode, ReturnListEnum.TENDER_BID.getCode());
            pendingItemLambdaQueryWrapper.eq(PendingItem::getBusinessId,notice.getId());
            pendingItemService.remove(pendingItemLambdaQueryWrapper);
        }

        if(!tProjectBidNoticeService.updateById(notice)){
            throw new SunFlowerException(ExceptionEnum.WITHDRAW_FAILED,ExceptionEnum.WITHDRAW_FAILED.getMessage());
        }

        if(!save(opinion)){
            throw new SunFlowerException(ExceptionEnum.WITHDRAW_FAILED,ExceptionEnum.WITHDRAW_FAILED.getMessage());
        }

        return true;
    }

    @Override
    // 判断招标公告状态是否可撤回
    public Boolean confirmWithdrawNotice(NoticeAuditReq req) {

        ProjectBidNotice notice = noticeService.noticeIsAvailable(req.getNoticeId(), NoticeProgressConstants.CAN_WITHDRAW);

//        Integer progress = notice.getNoticeProgress();

        // 招标公告关联的包段ID集合
        List<Long> packageIds = packageRService.findNoticePackageIdsByNoticeId(notice.getId());

        ApprovalOpinion opinion = new ApprovalOpinion();

        String userCode=getJwtUser().getUserOtherId();
        opinion.setApprovalContent(req.getApprovalOpinion());
        //请求工作流返回结果
        String result = "";
        //返回结果实体类
        ReturnVo returnVo = null;
        // 同意撤回
        if(req.getAuditType()==1){
                //待确认状态撤回  被第一个审批借点已确认的公告(处长)撤回要变成撤回待确认
                notice.setNoticeProgress(NoticeProgressEnum.WITHDRAWAL.getValue());
                opinion
                        .setApprovalTime(new Date())
                        .setProjectId(notice.getProjectId())
                        .setNoticeId(notice.getId())
                        .setApprovalUserId(getJwtUser().getUserId())
                        .setApprovalUserName(getJwtUser().getUserName())
                        .setApprovalType(ApprovalTypeEnum.AGREE_WITHDRAW.getValue());
                opinion.setIsDelete(CommonConstants.NO);
                opinion.setVersion(0);
                // 驳回流程
                result = workFlowApiClient.revokeProcessBySystem(TableNameConstants.PROJECT_BID_NOTICE + notice.getId(), req.getApprovalOpinion());
                returnVo = JSONObject.parseObject(result, ReturnVo.class);
                if (null == returnVo || !ReturnCodeConstants.PROCESS_SUCCESS_CODE.equals(returnVo.getCode())) {
                    throw new WorkFlowException(ExceptionEnum.WITHDRAW_FAILED.getCode(),returnVo.getMsg());
                }
                // 审批同意
                result = workFlowApiClient.review(userCode, req.getProcessInstanceId(), req.getTaskId(), req.getApprovalOpinion(), true);
                returnVo = JSONObject.parseObject(result, ReturnVo.class);
                if (null == returnVo || !ReturnCodeConstants.PROCESS_SUCCESS_CODE.equals(returnVo.getCode())) {
                    throw new WorkFlowException(ExceptionEnum.WITHDRAW_FAILED.getCode(),returnVo.getMsg());
                }

        }else{
            //驳回撤回 回到公告已确认
            notice.setNoticeProgress(NoticeProgressEnum.REVIEW.getValue());
            opinion
                    .setApprovalTime(new Date())
                    .setProjectId(notice.getProjectId())
                    .setNoticeId(notice.getId())
                    .setApprovalUserId(getJwtUser().getUserId())
                    .setApprovalUserName(getJwtUser().getUserName())
                    .setApprovalType(ApprovalTypeEnum.DRAWBACK_WITHDRAW.getValue());
            opinion.setIsDelete(CommonConstants.NO);
            opinion.setVersion(0);
            // 审批 不同意
            result = workFlowApiClient.review(userCode, req.getProcessInstanceId(), req.getTaskId(), req.getApprovalOpinion(), false);
            returnVo = JSONObject.parseObject(result, ReturnVo.class);
            if (null == returnVo || !ReturnCodeConstants.PROCESS_SUCCESS_CODE.equals(returnVo.getCode())) {
                throw new WorkFlowException(ExceptionEnum.WITHDRAW_FAILED.getCode(),returnVo.getMsg());
            }

            //退回撤回，添加待处理事项表
            // 查询采购公告关联标段
            LambdaQueryWrapper<NoticePackageR> noticePackageRLambdaQueryWrapper =  new LambdaQueryWrapper<>();
            noticePackageRLambdaQueryWrapper.eq(NoticePackageR::getNoticeId,opinion.getNoticeId());
            List<NoticePackageR> noticePackageRList = noticePackageRMapper.selectList(noticePackageRLambdaQueryWrapper);
            StringBuilder sectionIds = new StringBuilder();
            List<Long> sectionList = new ArrayList<>();
            for (NoticePackageR noticePackageR : noticePackageRList) {
                sectionList.add(noticePackageR.getSectionId());
                sectionIds.append(noticePackageR.getSectionId()).append(",");
            }
            sectionIds = new StringBuilder(sectionIds.substring(0, sectionIds.length() - 1));
            PendingItem pendingItem = new PendingItem();
            pendingItem.setOperationTime(opinion.getApprovalTime());
            pendingItem.setBusinessCode(ReturnListEnum.TENDER_BID.getCode());
            pendingItem.setBusinessId(opinion.getNoticeId());
            pendingItem.setBusinessType(2);
            pendingItem.setProjectId(opinion.getProjectId());
            pendingItem.setSectionId(sectionIds.toString());
            //查询标段轮次
            LambdaQueryWrapper<ProjectBidSection> projectBidSectionLambdaQueryWrapper = new LambdaQueryWrapper<>();
            projectBidSectionLambdaQueryWrapper.in(ProjectBidSection::getId,sectionList);
            List<ProjectBidSection> projectBidSections = projectBidSectionMapper.selectList(projectBidSectionLambdaQueryWrapper);
            pendingItem.setBidRound(projectBidSections.get(0).getBidRound());
            // 查询申请人和申请时间
            ProjectBidNotice projectBidNotice = tProjectBidNoticeMapper.selectById(opinion.getNoticeId());
            LambdaQueryWrapper<ApprovalOpinion> approvalOpinionLambdaQueryWrapper = new LambdaQueryWrapper<>();
            approvalOpinionLambdaQueryWrapper.eq(ApprovalOpinion::getApprovalUserId,projectBidNotice.getCreatedUserId());
            approvalOpinionLambdaQueryWrapper.eq(ApprovalOpinion::getNoticeId,projectBidNotice.getId());
            approvalOpinionLambdaQueryWrapper.orderByDesc(ApprovalOpinion::getCreatedTime);
            approvalOpinionLambdaQueryWrapper.last("limit 1");
            ApprovalOpinion approvalOpinion = approvalOpinionMapper.selectOne(approvalOpinionLambdaQueryWrapper);
            pendingItem.setApplyTime(approvalOpinion.getApprovalTime());
            pendingItem.setApplyUserId(approvalOpinion.getApprovalUserId());
            pendingItemService.save(pendingItem);

            // 发送app待阅
            commonMqService.sendOaReadMsg(ReturnListEnum.TENDER_BID.getDesc(), pendingItem);
        }

        if(!tProjectBidNoticeService.updateById(notice)){
            throw new SunFlowerException(ExceptionEnum.WITHDRAW_FAILED,ExceptionEnum.WITHDRAW_FAILED.getMessage());
        }

        if(!save(opinion)){
            throw new SunFlowerException(ExceptionEnum.WITHDRAW_FAILED,ExceptionEnum.WITHDRAW_FAILED.getMessage());
        }


        List<ProjectBidSection> sections = new ArrayList<>();
        for(Long sectionId : packageIds){
            ProjectBidSection bidSection = new ProjectBidSection();
            bidSection.setId(sectionId);
            if(projectBidDocService.judgeStatusByDoc(sectionId)){
                // 同意撤回
                if(req.getAuditType()==1) {
                    bidSection.setStatus(PackageStatusEnum.EDIT_BY_NOTICE.getValue().toString());
                }else{
                    bidSection.setStatus(PackageStatusEnum.READONLY_BY_NOTICE.getValue().toString());
                }
            }
            sections.add(bidSection);
        }
        //更新标段状态
        commonSectionService.updateSectionStatus(sections);
        // 同步项目状态
        noticeService.syncProjectStatus(notice.getProjectId());
        return true;
    }

    /**
     * 列表提交审核
     * @param req
     * @return
     */
    @Override
    public Result<Boolean> saveReview(CommonNoticeREQ req) {
        // 招标公告是否可提交
        ProjectBidNotice notice = noticeService.noticeIsAvailable(req.getNoticeId(), NoticeProgressConstants.CAN_EDIT);
        // 招标公告关联的包段ID集合
        List<Long> packageIds = packageRService.findNoticePackageIdsByNoticeId(notice.getId());
        ProjectBidNoticeVO noticeVO = noticeService.queryEditNoticeInfo(new CommonNoticeREQ().setProjectId(notice.getProjectId()).setNoticeId(notice.getId())).getData();
        // 招标公告变更记录集合
        NoticeChangeRecord changeRecords = dtoService.toNoticeChangeRecord(noticeVO);
        // 更新记录
        insertSubmitNotice(notice,packageIds,changeRecords);
        projectService.saveOrUpdateProjectRedundantFieldDealWith(req.getProjectId(),false);
        //commonSectionService.saveOrUpdateProjectPackageFieldDealWith(req.getProjectId());
        // 如果是退回后提交，删除待处理表数据
        StringBuilder sectionIds = new StringBuilder();
        for (Long sectionId : packageIds) {
            sectionIds.append(sectionId).append(",");
        }
        sectionIds = new StringBuilder(sectionIds.substring(0, sectionIds.length() - 1));
        LambdaQueryWrapper<PendingItem> pendingItemLambdaQueryWrapper = new LambdaQueryWrapper<>();
        pendingItemLambdaQueryWrapper.eq(PendingItem::getBusinessId,req.getNoticeId());
        pendingItemLambdaQueryWrapper.eq(PendingItem::getProjectId,req.getProjectId());
        pendingItemLambdaQueryWrapper.eq(PendingItem::getSectionId,sectionIds.toString());
        pendingItemLambdaQueryWrapper.eq(PendingItem::getBusinessCode,ReturnListEnum.TENDER_BID.getCode());
        pendingItemService.remove(pendingItemLambdaQueryWrapper);
        return Result.ok(true);
    }

    /**
     * 根据公告ID查询其基本信息
     * @param  noticeId 公共ID的列表
     * @return 公告ID与公告简单信息的映射
     */
//    @Override
//    public Map<Long, AppingTaskVO> findNoticeApprovalMap(List<Long> noticeId) {
//        // 参数判空
//        if((noticeId == null) || (noticeId.size() == 0)){
//            return null;
//        }
//
//        List<AppingTaskNoticeDTO> simpleApproval = noticeChangeRecordMapper.queryAppingTaskMapByIds(noticeId);
//        Map<Long, AppingTaskVO> map = new HashMap<>();
//        simpleApproval.forEach(i->{
//            AppingTaskVO appingTaskVO=new AppingTaskVO();
//            BeanUtils.copyProperties(i,appingTaskVO);
//            map.put(i.getNoticeId(),appingTaskVO);
//        });
//
//        return map;
//    }

    /**
     * 获取当前用户
     */
    private JwtUser getJwtUser(){
        // 获取当前用户
        return (JwtUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
    }

    /**
     * 调用流程引擎
    */
    private JSONObject noticeWithdraw(ProjectBidNotice notice,String message){
        String userCode = getJwtUser().getUserOtherId();
        String businessKey = TableNameConstants.PROJECT_BID_NOTICE +":" + notice.getId();
        // 调用流程引擎撤回
        String res = workFlowApiClient.revokeProcess(userCode, businessKey, message);

        JSONObject jsonObject = JSON.parseObject(res);
        return jsonObject;
    }

    /**
     * 调用流程引擎(撤回待确认)
     */
//    private JSONObject noticeWithdrawCh(ProjectBidNotice notice,String message){
//        String userCode = getJwtUser().getUserOtherId();
//        String businessKey = TableNameConstants.PROJECT_BID_NOTICE_CH +":" + notice.getId();
//        // 调用流程引擎撤回
//        String res = flowApiClient.revokeProcess(userCode, businessKey, message);
//
//        JSONObject jsonObject = JSON.parseObject(res);
//        return jsonObject;
//    }

    /**
     * 向流程引擎提交审批
    */
    private ReturnVo noticeApproval(ApprovalOpinionREQ req){
        // 接入流程
        String result = workFlowApiClient.review(
                getJwtUser().getUserOtherId(),
                req.getProcessInstanceId(),
                req.getTaskId(),
                req.getApprovalOpinion(),
                req.getIsAgree()
        );
        WorkFlowExceptionUtil.checkFlowResult(result);
        ReturnVo returnVo = JSONObject.parseObject(result, ReturnVo.class);
        return returnVo;
    }
    /**
     * 向流程引擎提交招标公告
    */
    private JSONObject noticeSubmit(ProjectBidNotice notice, String processDefinitionKey){
        // 发起流程
        String userCode = getJwtUser().getUserOtherId();
        String formName = notice.getNoticeName();
        String businessKey = TableNameConstants.PROJECT_BID_NOTICE + ":" + notice.getId();
        if(processDefinitionKey==FlowClientConstant.PROCESS_DEFINITION_KEY_CH_NOTICE_CONTENT){
            businessKey = "CH_"+ TableNameConstants.PROJECT_BID_NOTICE + ":" + notice.getId();
        }
        Map<String, Object> variables = new HashMap<>();
       // String res = flowApiClient.startProcessInstanceByKey(userCode, processDefinitionKey, formName, businessKey, variables);
        String res = workFlowApiClient.startProcessInstanceByKey(userCode, processDefinitionKey, formName, businessKey, variables);

        JSONObject jsonObject = JSON.parseObject(res);
        return jsonObject;
    }

    /**
     * 初始化ApprovalOpinion
     */
    private ApprovalOpinion initApprovalOpinion(ProjectBidNotice notice){

        ApprovalOpinion opinion = new ApprovalOpinion();
        opinion
                .setApprovalTime(new Date())
                .setProjectId(notice.getProjectId())
                .setNoticeId(notice.getId())
                .setApprovalUserId(getJwtUser().getUserId())
                .setApprovalUserName(getJwtUser().getUserName())
                .setApprovalContent("");
        opinion.setIsDelete(CommonConstants.NO);
        opinion.setVersion(0);

        return opinion;
    }

    /**
     * 验证数据有效性
    */
    private Boolean canSubmit(ProjectBidNotice notice){
        Result<ProjectBidNoticeVO> noticeInfo = tProjectBidNoticeService.queryEditNoticeInfo(new CommonNoticeREQ().setNoticeId(notice.getId()).setProjectId(notice.getProjectId()));
        List<PackageInfoDTO> packageInfo = noticeInfo.getData().getPackageInfo();
        List<MediaDTO> medias = noticeInfo.getData().getMedias();
        String noticeContent = noticeInfo.getData().getNoticeContent();
        String noticeName = noticeInfo.getData().getNoticeName();
//        Date intendedReleaseTime = noticeInfo.getData().getIntendedReleaseTime();

        boolean section = false;
        // 媒体是否必填
        boolean checkMedia = false;
        if(CollectionUtil.isNotEmpty(packageInfo)){
            for (PackageInfoDTO item : packageInfo) {
                if((item.getNoticeId() != null) && (item.getNoticeId().equals(notice.getId()))){
                    section = true;
                    // 如果是投标邀请 媒体不必填  预审第二轮 不必填
                    //if ((PurchaseStatusEnum.INVITE.getType()+"").equals(item.getPurchaseStatus()) || notice.getBidRound().equals(BidRoundEnum.HS.getType())){
                    if(item.getCanSearch().equals(Integer.parseInt(CanSearchEnum.CAN.getType()))){
                        checkMedia=false;
                    }else {
                        checkMedia=true;
                    }
                    break;
                }
            }
        }

        // 媒体必填判断
        boolean media=true;
        if(!checkMedia) {//不保密项目验证媒体
            if (CollectionUtil.isEmpty(medias)) {
                media = false;
            }
        }
        
//        if (notice.getBidRound().intValue()==0){
//            if(!CollectionUtil.isEmpty(medias)){
//                media = true;
//            }
//        }else{
//            // 投标邀请
//            media=true;
//        }


        boolean name = false;
        if(StrUtil.isNotBlank(noticeName) && (noticeName.length() < 101)){
            name = true;
        }


        boolean content = false;
        if(StrUtil.isNotEmpty(noticeContent) && (noticeContent.length() <= 120000)){
            content = true;
        }
//
//        boolean releaseTime = false;
//        if(intendedReleaseTime != null){
//            releaseTime = RequestUtil.timeIsAvailable(intendedReleaseTime,24);
//        }

//        if(name && content && releaseTime && section && media){
//            return true;
//        }
        if(name && content  && section && media){
            return true;
        }
        return false;
    }

    /**
     * 获取招标公告所关联的包段的ID
    */
    private UpdateWrapper<ProjectBidSection> createSectionUpdateWrapper(Long noticeId){
        List<Long> ids = packageRService.findNoticePackageIdsByNoticeId(noticeId);
        if(CollUtil.isEmpty(ids)){
            throw new SunFlowerException(ExceptionEnum.SECTION_NOT_FOUND,null);
        }

        UpdateWrapper<ProjectBidSection> vo = new UpdateWrapper<>();
        vo
                .eq("is_delete",CommonConstants.NO)
                .in("id",ids);

        return vo;
    }


}
