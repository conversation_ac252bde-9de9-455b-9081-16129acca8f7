package com.hzw.ssm.applets.service;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.hzw.ssm.applets.dao.TechnicalMapper;
import com.hzw.ssm.applets.entity.Technical;
import com.hzw.ssm.fw.base.BaseService;
import com.hzw.ssm.fw.util.DateUtil;

/**
 * 
 * <AUTHOR>
 	*       职称service
 */
@Service
public class TechnicalService extends BaseService {

	@Autowired
	private TechnicalMapper technicalmapper;
	
	public List<Technical> queryListTechnical(Technical entity) {
		
		StringBuffer sb = new StringBuffer();

		if(entity.getOpeningreOrder()!=null&& entity.getOpeningreOrder().equalsIgnoreCase("1")) {
			sb.append(" technical_tital_name asc ,");
		} if( entity.getOpeningreOrder()!=null&&entity.getOpeningreOrder().equalsIgnoreCase("2")){
			sb.append(" technical_tital_name desc ,");
		} if( entity.getExtractNumOrder()!=null&& entity.getExtractNumOrder().equalsIgnoreCase("3")){
			sb.append(" create_time asc ,");

		}  if(  entity.getExtractNumOrder()!=null&& entity.getExtractNumOrder().equalsIgnoreCase("4")){
			sb.append(" create_time desc ,");

		} if(entity.getHandletimeOrder()!=null&&  entity.getHandletimeOrder().equalsIgnoreCase("5")){
			sb.append(" modify_time asc ,");
		}  if(entity.getHandletimeOrder()!=null&&  entity.getHandletimeOrder().equalsIgnoreCase("6")){
			sb.append(" modify_time desc ,");
		}
		entity.setOpeningreOrder(null);
		entity.setExtractNumOrder(null);
		entity.setHandletimeOrder(null);
		if(sb.length()<1) {
			sb.append(" create_time desc ,");
		}
		String order = sb.toString();
		if(!"".equals(order)) {
		order =order.substring(0, order.lastIndexOf(","));
		entity.setReorder(order);
		}
		 
		return technicalmapper.queryPagetechnical(entity);
	}

	/**
	 *  查询单个信息
	 * @param entity
	 * @return
	 */
	public Technical gettoTechnical(Technical entity) {
		
		return technicalmapper.getTechnical(entity);
	}

	/**
	 * 修改状态
	 * @param id
	 * @param type
	 * @return
	 */
	public boolean updateTechnicalType(String id, String type) {
		try {
			//根据类型查询是否有专家选中该专业
			Integer num=technicalmapper.selectTechnicUser(id);
			if(num==0) {
			technicalmapper.updateTechnicalType(id,type);
			}else {
				return false;
			}
		} catch (Exception e) {
			e.printStackTrace();
			return false;
		}
		return true;
	}

	/**
	 * 修改或者新增
	 * @param entity
	 */
	public void addToUpdateTechnical(Technical entity) {
		//新增
		if("1".equals(entity.getLoginType())) {
			//获取最大的id
			int num = technicalmapper.getMaxTechnical();
			String id = (num+1)+"";
			entity.setId(id);
			entity.setIsDel("00");
			entity.setCode(id);
			technicalmapper.addTechnical(entity);
		}else {
		//修改
			entity.setModifyTime(new Date());
			technicalmapper.updateTechnical(entity);
		}
		
		
	}

	public boolean checkedTechnicalName(String technicalName) {
		int count=technicalmapper.checkedTechnicalName(technicalName);
		if(count>0) {
			return true;
		}
		return false;
	}

}
