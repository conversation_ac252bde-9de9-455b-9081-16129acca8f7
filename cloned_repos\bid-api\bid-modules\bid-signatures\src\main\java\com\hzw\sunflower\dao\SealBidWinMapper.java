package com.hzw.sunflower.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hzw.sunflower.controller.request.SealBidWinREQ;
import com.hzw.sunflower.controller.response.SealBidWinVO;
import com.hzw.sunflower.entity.SealBidWin;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* SealBidWinMapper接口
*
*/
public interface SealBidWinMapper extends BaseMapper<SealBidWin> {
    /**
     * 发放中标通知书查询盖章文件
     * @param req
     * @return
     */
    List<SealBidWinVO> listSealFiles(@Param("req") SealBidWinREQ req);
}