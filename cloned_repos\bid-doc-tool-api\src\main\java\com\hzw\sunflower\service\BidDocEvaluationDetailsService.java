package com.hzw.sunflower.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hzw.sunflower.controller.request.BidDocDetailExcelReq;
import com.hzw.sunflower.entity.BidDocEvaluationDetails;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

public interface BidDocEvaluationDetailsService extends IService<BidDocEvaluationDetails> {

    /**
     * 下载模板 详细评审评标办法导入模板
     * @param request
     * @param response
     */
    void downTemplateDetails(HttpServletRequest request, HttpServletResponse response);

    /**
     * 批量导入 详细评审评标办法文件解析
     * @param req
     * @return
     */
    List<List<String>> detailsExcelParse(BidDocDetailExcelReq req);
}
