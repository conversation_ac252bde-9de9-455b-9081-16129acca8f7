package com.hzw.sunflower.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.controller.request.BidDocTemplateListREQ;
import com.hzw.sunflower.controller.responese.BidDocTemplateVO;
import com.hzw.sunflower.dto.BidDocTemplateDTO;
import com.hzw.sunflower.entity.BidDocTemplate;
import com.baomidou.mybatisplus.extension.service.IService;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 招标文件编制模板表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-26
 */
public interface BidDocTemplateService extends IService<BidDocTemplate> {


    /**
     * 查询招标文件编制模版列表
     * @param condition
     * @param condition
     * @return
     */
    IPage<BidDocTemplateVO> selectTemplateList(BidDocTemplateListREQ condition);


    /**
     * 查询招标文件编制模版停用/启用
     * @param dto
     * @return
     */
    Result<Object> onOrOff(BidDocTemplateDTO dto);

}
