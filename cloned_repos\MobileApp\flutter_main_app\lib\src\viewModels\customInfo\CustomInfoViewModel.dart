///***
/// * 信息订阅 视图模型
/// */

import 'package:flutter/material.dart';

import 'package:flutter_main_app/src/utils/ApiUtils.dart';
import 'package:flutter_main_app/src/constants/region.dart';

import 'package:flutter_main_app/src/models/common/HttpResponseDataListModel.dart';
import 'package:flutter_main_app/src/models/bulletin/BulletinListQueryModel.dart';
import 'package:flutter_main_app/src/models/bulletin/BulletinListItemModel.dart';

class CustomInfoViewModel extends ChangeNotifier {

  CustomInfoViewModel();

  /// * 是否正在加载数据
  bool _isLoading = false;

  bool get isLoading => _isLoading;

  set isLoading(bool isLoading) {
    _isLoading = isLoading;

    notifyListeners();
  }

  /// * 页脚信息
  int totalNumber;

  int totalPage;

  /// * 数据列表
  List<BulletinListItemModel> _dataList = [];

  List<BulletinListItemModel> get dataList => _dataList;

  ///***
  /// * 获取数据列表
  /// *
  /// * 因为是与 [DataListView] 组件合用，因此该函数不应该触发 [DataListView] 的销毁重建.
  /// */
  Future<List<BulletinListItemModel>> fetchDataList() async {
    /// ! setState() or markNeedsBuild() called during build.
    // isLoading = true;

    /// * 遮蔽 shadow
    final List<String> keywords = [];

    keywords.addAll(this.keywords);

    if (keywords.length > 1 && keywords[keywords.length - 1].isEmpty) {
      /// * 如果最后一个关键词为空，则删除它
      keywords.removeAt(keywords.length - 1);
    }

    setSpecifiedParam('keyWord', keywords);

    final HttpResponseDataListModel<BulletinListItemModel> httpResponse =
      await ApiUtils.postBulletin(params);

    List<BulletinListItemModel> newDataList;

    if (httpResponse != null) {
      newDataList = httpResponse.data ?? [];

      totalNumber = httpResponse.totalNumber ?? 0;

      totalPage = httpResponse.totalPage ?? 0;

      for (int i = 0; i < newDataList.length; i++) {
        final String code = newDataList[i].regionCode;

        if (code != null && code.isNotEmpty && code.length == 6) {
          for (int j = 0; j < jiangSuRegionList.length; j++) {
            if (code.substring(0, 4) == jiangSuRegionList[j].code.substring(0, 4)) {
              newDataList[i].regionName = jiangSuRegionList[j].name;

              break;
            }

            newDataList[i].regionName = '江苏省';
          }
        } else {
          newDataList[i].regionName = '江苏省';
        }
      }

      concatDataList(newDataList);
    }

    // isLoading = false;

    return newDataList;
  }

  ///***
  /// * 清空数据列表
  /// * 同时通知所有订阅者
  /// */
  void clearDataList() {
    _dataList.clear();

    notifyListeners();
  }

  ///***
  /// * 给数据列表赋值
  /// * 同时通知所有订阅者
  /// *
  /// * @param {List<BulletinListItemModel>} newDataList
  /// */
  void setDataList(List<BulletinListItemModel> newDataList) {
    _dataList = newDataList;

    notifyListeners();
  }

  ///***
  /// * 向数据列表末尾添加新的数据列表
  /// * 同时通知所有订阅者
  /// *
  /// * @param {List<BulletinListItemModel>} newDataList
  /// */
  void concatDataList(List<BulletinListItemModel> newDataList) {
    _dataList?.addAll(newDataList);

    notifyListeners();
  }

  ///************************************* 订阅关键词，最多 3 个 *************************************///

  List<String> _keywords;

  List<String> get keywords => _keywords;

  ///***
  /// * 设置整个数组
  /// *
  /// * @param {List<String>} keywords
  /// */
  set keywords(List<String> keywords) {
    _keywords = keywords;

    notifyListeners();
  }

  ///***
  /// * 新增 keyword
  /// * 同时通知所有订阅者
  /// *
  /// * @param {String} keyword
  /// */
  void addKeyword(String keyword) {
    _keywords.add(keyword);

    notifyListeners();
  }

  ///***
  /// * 替换 index 下标处的元素
  /// * 同时通知所有订阅者
  /// *
  /// * @param {String} keyword
  /// * @param {int} index
  /// */
  void replaceKeywordAt(String keyword, int index) {
    _keywords[index] = keyword;

    notifyListeners();
  }

  ///***
  /// * 删除 index 下标处的元素
  /// * 同时通知所有订阅者
  /// *
  /// * @param {int} index
  /// */
  void removeKeywordAt(int index) {
    _keywords.removeAt(index);

    notifyListeners();
  }

  ///************************************* 筛选条件 *************************************///

  BulletinListQueryModel _params;

  BulletinListQueryModel get params => _params;

  set params(BulletinListQueryModel params) {
    _params = params;

    /// * 随便做点什么
    params = null;
    /// ! setState() or markNeedsBuild() called during build.
    // notifyListeners();
  }

  ///***
  /// * 设置指定的参数对象值
  /// *
  /// * @param {String} paramName
  /// * @param {dynamic} paramValue
  /// */
  void setSpecifiedParam(String paramName, dynamic paramValue) {
    final Map<String, dynamic> tempParams = params.toJson();

    tempParams[paramName] = paramValue;

    params = BulletinListQueryModel.fromJson(tempParams);
  }

  /// * 推送时间
  List<String> _publishTime;

  List<String> get publishTime => _publishTime;

  set publishTime(List<String> publishTime) {
    _publishTime = publishTime;

    notifyListeners();
  }

}
