package com.hzw.sunflower.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.constant.AnalysisStatusEnum;
import com.hzw.sunflower.dao.BidDocCompareMapper;
import com.hzw.sunflower.dto.BidDocCompareDto;
import com.hzw.sunflower.entity.BidFileSimilarity;
import com.hzw.sunflower.service.BidDocCompareService;
import com.hzw.sunflower.vo.BidDocCompareVo;
import org.springframework.amqp.core.AmqpTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * 消息队列 Service接口实现
 *
 * <AUTHOR>
 * @version 1.0.0 2024-04-9
 */
@Service
public class BidDocCompareServiceImpl extends ServiceImpl<BidDocCompareMapper, BidFileSimilarity> implements BidDocCompareService {

    @Autowired
    private AmqpTemplate rabbitTemplate;

    @Override
    public Result<String> bidDocCompare(BidDocCompareDto bidDocCompareDto) {
        List<BidFileSimilarity> list = this.baseMapper.queryApplyInfo(bidDocCompareDto.getSectionId(),bidDocCompareDto.getBidRound());
        boolean flag = this.saveOrUpdateBatch(list);
        if(flag){
            sendMq(bidDocCompareDto);
        }
        return Result.ok();
    }

    @Override
    public void sendMq(BidDocCompareDto bidDocCompareDto) {
        rabbitTemplate.convertAndSend("bid_doc_compare", JSONObject.toJSONString(bidDocCompareDto));
    }

    @Override
    public List<BidDocCompareVo> queryApplyCompany(Long sectionId, Integer bidRound) {
        return this.baseMapper.queryApplyCompany(sectionId,bidRound);
    }

    @Override
    public List<BidDocCompareVo> queryTenderRecord(Long sectionId, Integer bidRound) {
        return this.baseMapper.queryTenderRecord(sectionId,bidRound);
    }

    @Override
    public String queryTenderRecordFile(List<String> ossIdList) {
        return this.baseMapper.queryTenderRecordFile(ossIdList);
    }

    @Override
    public Integer queryProjectOperationFlow(Long projectId) {
        return this.baseMapper.queryProjectOperationFlow(projectId);
    }
}
