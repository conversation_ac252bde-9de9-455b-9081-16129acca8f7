#!/bin/sh
#cp TEMPLATE FILE
docker stop bid-monitor-server || true
docker rm bid-monitor-server || true
docker rmi registry.cn-hangzhou.aliyuncs.com/hzw-jtcc/jtcc:bid-monitor-server-1.0.0 || true
docker pull registry.cn-hangzhou.aliyuncs.com/hzw-jtcc/jtcc:bid-monitor-server-1.0.0
docker run -itd --net=host  --log-opt max-size=100m --log-opt max-file=3  --name bid-monitor-server -v /home/<USER>/plan1/bid-cloud/config/bid-monitor-server:/hzw/monitor/config -v /data/log:/hzw/monitor/logs  registry.cn-hangzhou.aliyuncs.com/hzw-jtcc/jtcc:bid-monitor-server-1.0.0
