package com.hzw.sunflower.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzw.sunflower.client.WorkFlowApiClient;
import com.hzw.sunflower.common.CommonConstants;
import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.constant.FormatConstants;
import com.hzw.sunflower.constant.ReturnCodeConstants;
import com.hzw.sunflower.constant.TableHeadTypeConstants;
import com.hzw.sunflower.constant.TableNameConstants;
import com.hzw.sunflower.constant.constantenum.*;
import com.hzw.sunflower.controller.project.request.SupplierInfoREQ;
import com.hzw.sunflower.controller.request.*;
import com.hzw.sunflower.controller.response.DocDetailVO;
import com.hzw.sunflower.controller.response.ProjectTaskVO;
import com.hzw.sunflower.controller.user.request.PushProjectDataReq;
import com.hzw.sunflower.dao.ApprovalOpinionMapper;
import com.hzw.sunflower.dao.ProjectBidDocMapper;
import com.hzw.sunflower.dao.ProjectBidSectionMapper;
import com.hzw.sunflower.dto.*;
import com.hzw.sunflower.entity.*;
import com.hzw.sunflower.entity.condition.ProjectBidDocCondition;
import com.hzw.sunflower.exception.ExceptionEnum;
import com.hzw.sunflower.exception.ParamsNotNullException;
import com.hzw.sunflower.exception.SunFlowerException;
import com.hzw.sunflower.freesia.client.constant.FlowClientConstant;
import com.hzw.sunflower.freesia.client.vo.flowable.ReturnVo;
import com.hzw.sunflower.freesia.client.vo.flowable.task.TaskUserVo;
import com.hzw.sunflower.security.utils.SecurityUtils;
import com.hzw.sunflower.service.*;
import com.hzw.sunflower.util.BeanListUtil;
import com.hzw.sunflower.util.FileUtils;
import com.hzw.sunflower.util.OssUtil;
import com.hzw.sunflower.util.oss.OssFileStorage;
import com.hzw.sunflower.utils.CompareObjectUtil;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.xwpf.usermodel.*;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.*;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.*;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * TProjectBidDocService接口实现
 *
 * <AUTHOR>
 * @version 1.0.0 2021-06-28
 */
@Service
public class ProjectBidDocServiceImpl extends ServiceImpl<ProjectBidDocMapper, ProjectBidDoc> implements ProjectBidDocService {

    @Autowired
    private DocChangeRecordService docChangeRecordService;

    @Autowired
    private ProjectBidSectionService projectBidSectionService;

    @Autowired
    private ProjectBidDocRelationService projectBidDocRelationService;

    @Autowired
    private RelevancyAnnexService relevancyAnnexService;

    @Autowired
    private WorkFlowApiClient flowApiClient;

    @Autowired
    private ApprovalOpinionService approvalOpinionService;

    @Autowired
    private SectionGroupService sectionGroupService;

    @Autowired
    private ProjectService projectService;

    @Autowired
    private ApplyInfoService applyInfoService;

    @Autowired
    private BidConfirmService bidConfirmService;

    @Autowired
    private RoundDataCopyService roundDataCopeService;

    @Autowired
    private ProjectBidNoticeService projectBidNoticeService;

    @Autowired
    private CommonSectionService commonSectionService;

    @Resource
    private ISectionExpertSpecialtyService iSectionExpertSpecialtyService;

    @Autowired
    private ClarifyQuestionService clarifyQuestionService;

    @Autowired
    private ProjectSectionScheduleService projectSectionScheduleService;

    @Autowired
    private ProjectSectionBidOpenService projectSectionBidOpenService;

    @Autowired
    private ProjectBidDocMapper projectBidDocMapper;

    @Autowired
    private ApprovalOpinionMapper approvalOpinionMapper;

    @Autowired
    private PendingItemService pendingItemService;

    @Autowired
    private ProjectBidSectionMapper projectBidSectionMapper;

    @Autowired
    private CompanyPushDataService companyPushDataService;

    @Autowired
    private ProjectBidDocScheduleService projectBidDocScheduleService;

    @Value("${oss.active}")
    private String ossType;

    @Autowired
    private OssFileService ossFileService;

    @Value("${files.temporary.path}")
    private String tempFilePath;

    @Autowired
    private CommonMqService commonMqService;

    @Autowired
    private ProjectSectionScheduleService sectionScheduleService;

    @Autowired
    private ProjectPromiseFileService projectPromiseFileService;

    /**
     * 根据条件分页查询列表
     *
     * @param condition 查询条件
     * @return 分页数据
     */
    @Override
    public IPage<ProjectBidDocDTO> findInfoByCondition(ProjectBidDocCondition condition) {
        if(null != condition.getNoticeProgress() && !"".equals(condition.getNoticeProgress())){
            condition.setNoticeProgressList(condition.getNoticeProgress().split(","));
        }
        return this.baseMapper.queryBidDocInfo(condition.customizeBuildPage(), condition);
    }

    /**
     * 根据主键ID查询
     *
     * @param id 主键ID
     * @return
     */
    @Override
    public ProjectBidDoc getInfoById(Long id) {
        return this.getById(id);
    }

    /**
     * 新增
     *
     * @param projectBidDoc
     * @return 是否成功
     */
    @Override
    public Boolean addInfo(ProjectBidDoc projectBidDoc) {
        return this.save(projectBidDoc);
    }

    /**
     * 修改
     *
     * @param projectBidDoc
     * @return 是否成功
     */
    @Override
    public Boolean updateInfo(ProjectBidDoc projectBidDoc) {
        return this.updateById(projectBidDoc);
    }

    /**
     * 根据主键ID删除
     *
     * @param id 主键ID
     * @return 是否成功
     */
    @Override
    public Boolean deleteById(Long id) {
        return this.removeById(id);
    }

    /**
     * 根据主键ID列表批量删除
     *
     * @param idList 主键ID列表
     * @return 是否成功
     */
    @Override
    public Boolean deleteByIds(List<Long> idList) {
        return this.removeByIds(idList);
    }

    /**
     * 新增/修改招标文件信息
     *
     * @param dataInfoREQ
     * @return
     */
    @Override
    public Boolean addDocInfo(DocDataInfoREQ dataInfoREQ, JwtUser jwtUser) {


        //yg:不允许同一个项目标包存在两条数据
        List<ProjectBidDocREQ> bidDocInfo = dataInfoREQ.getBidDocInfo();
        for(ProjectBidDocREQ projectBidDocREQ : bidDocInfo){
            //判断是新增还是删除
            if(projectBidDocREQ.getDocId()==null){
                Integer bidRound = projectBidDocREQ.getBidRound();
                Long projectId = projectBidDocREQ.getProjectId();
                Long[] sectionIdArray = projectBidDocREQ.getSectionIdArray();
                if(projectId!=null && sectionIdArray!=null){
                    List<Long> sectionIds = Arrays.stream(sectionIdArray).collect(Collectors.toList());
                    for(Long sectionId:sectionIds){
                        List<ProjectBidDoc> projectBidDocs =  this.baseMapper.getProjectIdInfo(projectId,sectionId, bidRound);
                        //判断是否存在重复数据
                        if (!projectBidDocs.isEmpty()){
                            throw new ParamsNotNullException("该项目的标段包已经存在数据，不允许提交!");
                        }
                    }
                }
            }else{
                //修改
                Long id = projectBidDocREQ.getDocId();
                List<ProjectBidDoc> projectBidDocs =  this.baseMapper.getProjectIdInfoUpdate(id);
                //判断当前状态值是否可以修改
                if (!projectBidDocs.isEmpty()){
                    throw new ParamsNotNullException("该项目的标段包已经存在数据，不可以修改!");
                }
            }

            // 递交截止时间为另行通知，将递交截止时间置空
            if(TimeTypeEnum.UNCONFIRMED.getValue().equals(projectBidDocREQ.getSubmitEndTimeType())){
                projectBidDocREQ.setQuestionEndTimeArray(new Date[0]);
                // 且发售截止时间为递交截止时间前一天，则发售截止时间置空
                if(TimeTypeEnum.PREVIOUS_DAY.getValue().equals(projectBidDocREQ.getSaleEndTimeType())){
                    projectBidDocREQ.setSaleStartTime(null);
                    projectBidDocREQ.setSaleEndTimeArray(new Date[0]);
                }
            } else {
                // 发售截止时间为另行通知，将递交截止时间置空
                if(TimeTypeEnum.UNCONFIRMED.getValue().equals(projectBidDocREQ.getSaleEndTimeType())){
                    projectBidDocREQ.setSaleStartTime(null);
                    projectBidDocREQ.setSaleEndTimeArray(new Date[0]);
                }
            }

        }

        //重新招标退款
        refund(dataInfoREQ);
        boolean flag = true;
        //修改套ID
        String[] split = null;
        if (null != dataInfoREQ.getDeleteGroupId() && !dataInfoREQ.getDeleteGroupId().equals("0")) {
            SectionGroup sectionGroup = sectionGroupService.getById(dataInfoREQ.getDeleteGroupId());
            split = sectionGroup.getSectionIds().split("、");
            //flag = sectionGroupService.deleteById(dataInfoREQ.getDeleteGroupId());
        }
        //多条招标文件信息保存
        List<ProjectBidDocREQ> list = dataInfoREQ.getBidDocInfo();
        if (CollectionUtil.isEmpty(list)) {
            flag = false;
        }
        SectionGroup sectionGroup = null;
        if (flag && null != list.get(0).getSectionGroup().getProjectId()) {
            //保存标段/包按套销售信息
            sectionGroup = list.get(0).getSectionGroup();
            //sectionGroup.setId(null);
            flag = sectionGroupService.updateInfo(sectionGroup);

            //套内加套里的包，删除原来套里的包
            groupAddSectionId(list,sectionGroup);
        }
        if (flag) {
            for (int i = 0; i < list.size(); i++) {
                ProjectBidDocREQ projectBidDocREQ = list.get(i);
                if (projectBidDocREQ.getPreQualificationFlag()) {
                    dealSection(projectBidDocREQ);
                }
                ProjectBidDoc projectBidDoc = new ProjectBidDoc();
                projectBidDoc.setId(projectBidDocREQ.getDocId());
                projectBidDoc.setProjectId(projectBidDocREQ.getProjectId());
                projectBidDoc.setPackageNumber(projectBidDocREQ.getPackageNumber());
                projectBidDoc.setNoticeProgress(NoticeProgressEnum.UNDERREVIEW.getValue());
                projectBidDoc.setState(BidDocStateEnum.FORMAL.getValue());
                projectBidDoc.setPhone(projectBidDocREQ.getPhone());
                projectBidDoc.setContact(projectBidDocREQ.getContact());
                projectBidDoc.setAnnexName(projectBidDocREQ.getAnnexName());
                projectBidDoc.setOssFileId(projectBidDocREQ.getOssFileId());
                projectBidDoc.setFeeType(projectBidDocREQ.getFeeType());
                projectBidDoc.setPaymentType(projectBidDocREQ.getPaymentType());
                projectBidDoc.setFilePriceState(projectBidDocREQ.getFilePriceState());
                projectBidDoc.setOtherPriceDescribe(projectBidDocREQ.getOtherPriceDescribe());
                projectBidDoc.setFree(projectBidDocREQ.getFree());
                projectBidDoc.setBidRound(projectBidDocREQ.getBidRound());
                projectBidDoc.setTenderFeeFlag(projectBidDocREQ.getTenderFeeFlag());
                projectBidDoc.setSubmitTime(new Date());
                flag = saveOrUpdate(projectBidDoc);
                //修改套关联的招标文件的标书费支付方式
                if (flag && null != split) {
                    flag = modifyDocPaymentType(projectBidDocREQ, split);
                }
                if (flag) {
                    //修改标段表 保证金、标书费 信息
                    flag = modifySectionInfo(projectBidDocREQ, sectionGroup);
                }
                if (flag && dataInfoREQ.getReTender() && 1 == projectBidDocREQ.getFree()) {
                    //重新招标项目，且收费，需要修改报名情况表信息
                    flag = modifyReBidApplyInfo(projectBidDocREQ);
                }
                if (flag) {
                    //保存招标文件关联信息
                    flag = saveDocRelationInfo(projectBidDocREQ, projectBidDoc.getId());
                }
                if (flag) {
                    //<标段ID,附件文件ID集合>
                    Map<Long, String> maps = getFileIdMap(dataInfoREQ);
                    // 开标一览表
                    Map<Long, String> scheduleMaps = getScheduleFileIdMap(dataInfoREQ);
                    //招标文件表更操作记录
                    flag = saveDocChangeRecord(projectBidDocREQ, projectBidDoc.getId(), maps, scheduleMaps);
                }

                //加入工作流
                if (flag) {
                    String appContent="";// 下一级审批人
                    Map<String, Object> map = new HashMap<>();
                    map.put("flag", false);
                    //招标文件售价说明为 其他
                    filePriceState(sectionGroup, projectBidDocREQ, map);
                    String userCode = jwtUser.getUserOtherId();
                    String processDefinitionKey = FlowClientConstant.PROCESS_DEFINITION_KEY_NOTICE_DOC;
                    String formName = projectBidDoc.getAnnexName();
                    String businessKey = TableNameConstants.PROJECT_BID_DOC + projectBidDoc.getId();
                    String result = flowApiClient.startProcessInstanceByKey(userCode, processDefinitionKey, formName, businessKey, map);
                    ReturnVo returnVo = JSONObject.parseObject(result, ReturnVo.class);
                    if (null == returnVo || !ReturnCodeConstants.PROCESS_SUCCESS_CODE.equals(returnVo.getCode())) {
                        throw new SunFlowerException(ExceptionEnum.BID_DOC_AUTH_ERROR, returnVo.getMsg());
                    }else{
                        AppOaMsgDto dto = new AppOaMsgDto();
                        List<String> recipientList = new ArrayList<>();
                        // 判断流程是否结束
                        if ("end".equals(returnVo.getMsg())){
                            ProjectBidDoc pbd = new ProjectBidDoc();
                            pbd.setNoticeProgress(NoticeProgressEnum.REVIEW.getValue());
                            pbd.setId(projectBidDoc.getId());
                            updateById(pbd);

                            // 发送app待办
                            dto.setTaskType(AppTaskTypeEnum.TENDER_DOC.getCode());
                            recipientList.add(userCode);
                            dto.setRecipientList(recipientList);
                            commonMqService.sendOaMsg(dto);

                        }else{
                            String taskUserlist = flowApiClient.getTaskUserByProcessInstanceId(SecurityUtils.getJwtUser().getUserOtherId(), returnVo.getData().toString());
                            JSONObject jsonObjectTask = JSON.parseObject(taskUserlist);
                            List<TaskUserVo> taskUserVoList = JSONObject.parseArray(jsonObjectTask.getString("data"), TaskUserVo.class);

                            for (TaskUserVo taskUserVo : taskUserVoList) {
                                if (!"".equals(appContent)){
                                    appContent+=","+taskUserVo.getUserName();
                                }else {
                                    appContent=taskUserVo.getUserName();
                                }
                                recipientList.add(taskUserVo.getUserCode());
                            }

                            // 发送app待办
                            dto.setTaskType(AppTaskTypeEnum.NOTICE_DOC.getCode());
                            dto.setRecipientList(recipientList);
                            commonMqService.sendOaMsg(dto);
                        }

                    }

                    if (flag) {
                        //审批记录
                        flag =saveApprovalOpinion(ApprovalTypeEnum.COMMIT.getValue(), jwtUser,
                                projectBidDocREQ.getProjectId(), projectBidDoc.getId(), null,appContent);
                    }
                }
                if (!flag) {
                    break;
                }
            }
        }
        if (flag && null != dataInfoREQ.getAttachmentInfo()) {
            //保存附件信息
            flag = relevancyAnnexService.saveRelevancyAnnexInfo(dataInfoREQ.getAttachmentInfo());
        }

        // 修改是否支持线上澄清修改
        if(flag){
            flag = modifyClarifyQuestion(dataInfoREQ);
        }

        if (flag) {
            // 保存线上开标信息
            flag = saveBidOpenInfo(dataInfoREQ);
        }

        if (flag) {
            // 保存开标一览表信息
            flag = projectSectionScheduleService.saveScheduleInfo(dataInfoREQ.getBidDocInfo());
        }

        if (!flag) {
            throw new SunFlowerException(ExceptionEnum.BID_DOC_ADD_ERROR, ExceptionEnum.BID_DOC_ADD_ERROR.getMessage());
        }
        // 删除待处理表的退回待处理数据
        if (flag) {
            for (ProjectBidDocREQ projectBidDocREQ : dataInfoREQ.getBidDocInfo()) {
                if (projectBidDocREQ.getDocId() != null) {
                    LambdaQueryWrapper<PendingItem> pendingItemLambdaQueryWrapper = new LambdaQueryWrapper<>();
                    pendingItemLambdaQueryWrapper.eq(PendingItem::getBusinessCode,ReturnListEnum.TENDER_DOC.getCode());
                    pendingItemLambdaQueryWrapper.eq(PendingItem::getBusinessId,projectBidDocREQ.getDocId());
                    pendingItemLambdaQueryWrapper.eq(PendingItem::getProjectId,projectBidDocREQ.getProjectId());
                    pendingItemService.remove(pendingItemLambdaQueryWrapper);
                }
            }

        }
        return flag;
    }

    /**
     * 套内加套里的包，删除原来套里的包
     */
    private void groupAddSectionId(List<ProjectBidDocREQ> list,SectionGroup sectionGroup) {
        //套内加套里的包，删除原来套里的包
        if(list.get(0).getSectionIdArray() != null && sectionGroup.getId() != null){
            Long projectId = list.get(0).getSectionGroup().getProjectId();
            Long[] sectionIdArray = list.get(0).getSectionIdArray();
            for(Long subId :sectionIdArray){
                //查询标段信息
                ProjectBidSection section = projectBidSectionService.getById(subId);
                //根据标包id查询不等于当前套id的套
                SectionGroup group = sectionGroupService.queryGroupInfoPassId(projectId,subId,sectionGroup.getId());
                if(group != null) {
                    //重新封装groupInfo
                    if (StringUtils.isNotBlank(group.getGroupInfo())) {
                        String[] groupSectionIds = group.getGroupInfo().split("、");
                        String groupInfo = "";
                        for (String str : groupSectionIds) {
                            if (!str.equals(section.getPackageNumber().toString())) {
                                groupInfo += str + "、";
                            }
                        }
                        if (!groupInfo.equals("")) {
                            group.setGroupInfo(groupInfo.substring(0, groupInfo.length() - 1));
                        } else {
                            group.setSectionIds(groupInfo);
                        }
                    }
                    //重新封装section_ids
                    if (StringUtils.isNotBlank(group.getSectionIds())) {
                        String[] sectionids = group.getSectionIds().split("、");
                        String section_ids = "";
                        for (String str : sectionids) {
                            if (!str.equals(section.getId().toString())) {
                                section_ids += str + "、";
                            }
                        }
                        if (!section_ids.equals("")) {
                            group.setSectionIds(section_ids.substring(0, section_ids.length() - 1));
                        } else {
                            group.setSectionIds(section_ids);
                        }
                    }
                    //如果重组后的section_ids为"" 则删除，反之修改
                    if (group.getSectionIds().equals("")) {
                        //删除
                        sectionGroupService.deleteById(group.getId());
                    } else {
                        sectionGroupService.updateById(group);
                    }
                }
            }
        }
    }

   /* *//**
     * 文件售价说明
     * 原始逻辑 留存
     * @param sectionGroup
     * @param projectBidDocREQ
     * @param map
     *//*
    private void filePriceState(SectionGroup sectionGroup, ProjectBidDocREQ projectBidDocREQ, Map<String, Object> map) {
        if (FilePriceStateEnum.OTHER.getValue().equals(projectBidDocREQ.getFilePriceState())
                && TenderFeeFlagEnum.CHARGE.getValue().equals(projectBidDocREQ.getTenderFeeFlag())) {
            BigDecimal tenMillion = new BigDecimal(FormatConstants.TEN_MILLION);
            BigDecimal five = new BigDecimal(FormatConstants.FIVE_HUNDRED);
            BigDecimal three = new BigDecimal(FormatConstants.THREE_HUNDRED);
            //按套
            if (TenderFeeTypeEnum.GROUP.getValue().equals(projectBidDocREQ.getTenderFeeType())) {
                BigDecimal entrustMoney = new BigDecimal(FormatConstants.ENTRUST_MONEY);
                LambdaQueryWrapper<ProjectBidSection> groupWrapper = new LambdaQueryWrapper<>();
                groupWrapper.in(ProjectBidSection::getId, sectionGroup.getSectionIds().split("、"));
                List<ProjectBidSection> sectionList = projectBidSectionService.list(groupWrapper);
                for (int j = 0; j < sectionList.size(); j++) {
                    entrustMoney = entrustMoney.add(sectionList.get(j).getEntrustMoney());
                }
                if (-1 == tenMillion.compareTo(entrustMoney) && -1 == five.compareTo(projectBidDocREQ.getTenderFee())) {
                    map.put("flag", true);
                } else if (0 <= tenMillion.compareTo(entrustMoney) && -1 == three.compareTo(projectBidDocREQ.getTenderFee())) {
                    map.put("flag", true);
                }
            } else if (TenderFeeTypeEnum.SECTION.getValue().equals(projectBidDocREQ.getTenderFeeType())) {
                //按标段
                for (int m = 0; m < projectBidDocREQ.getSectionIdArray().length; m++) {
                    ProjectBidSection section = projectBidSectionService.getById(projectBidDocREQ.getSectionIdArray()[m]);
                    if (-1 == tenMillion.compareTo(section.getEntrustMoney()) && -1 == five.compareTo(projectBidDocREQ.getTenderFeeArray()[m])) {
                        map.put("flag", true);
                        break;
                    } else if (0 <= tenMillion.compareTo(section.getEntrustMoney()) && -1 == three.compareTo(projectBidDocREQ.getTenderFeeArray()[m])) {
                        map.put("flag", true);
                        break;
                    }
                }
            } else if (TenderFeeTypeEnum.CHARGE.getValue().equals(projectBidDocREQ.getTenderFeeType())) {
                Project project = projectService.getProjectById(projectBidDocREQ.getProjectId());
                if (SegmentEnum.NO_BAG.getType().equals(project.getPackageSegmentStatus())) {
                    //不划分 项目
                    ProjectBidSection section = projectBidSectionService.getById(projectBidDocREQ.getSectionIdArray()[0]);
                    if (-1 == tenMillion.compareTo(section.getEntrustMoney()) && -1 == five.compareTo(projectBidDocREQ.getTenderFee())) {
                        map.put("flag", true);
                    } else if (0 <= tenMillion.compareTo(section.getEntrustMoney()) && -1 == three.compareTo(projectBidDocREQ.getTenderFee())) {
                        map.put("flag", true);
                    }
                } else {
                    if (projectBidDocREQ.getPreQualificationFlag()) {
                        //资格预审按项目 收取
                        BigDecimal entrustMoney = new BigDecimal(FormatConstants.ENTRUST_MONEY);
                        LambdaQueryWrapper<ProjectBidSection> sectionWrapper = new LambdaQueryWrapper<>();
                        sectionWrapper.in(ProjectBidSection::getId, projectBidDocREQ.getSectionIdArray());
                        List<ProjectBidSection> sectionList = projectBidSectionService.list(sectionWrapper);
                        for (int j = 0; j < sectionList.size(); j++) {
                            entrustMoney = entrustMoney.add(sectionList.get(j).getEntrustMoney());
                        }
                        if (-1 == tenMillion.compareTo(entrustMoney) && -1 == five.compareTo(projectBidDocREQ.getTenderFee())) {
                            map.put("flag", true);
                        } else if (0 <= tenMillion.compareTo(entrustMoney) && -1 == three.compareTo(projectBidDocREQ.getTenderFee())) {
                            map.put("flag", true);
                        }
                    }
                }
            } else {
                map.put("flag", false);
            }
        } else {
            map.put("flag", false);
        }
    }*/

    private void filePriceState(SectionGroup sectionGroup, ProjectBidDocREQ projectBidDocREQ, Map<String, Object> map) {
        if (FilePriceStateEnum.OTHER.getValue().equals(projectBidDocREQ.getFilePriceState())
                && TenderFeeFlagEnum.CHARGE.getValue().equals(projectBidDocREQ.getTenderFeeFlag())) {
            BigDecimal thousand = new BigDecimal(FormatConstants.THOUSAND);
                //按标段
                for (int m = 0; m < projectBidDocREQ.getSectionIdArray().length; m++) {
                    ProjectBidSection section = projectBidSectionService.getById(projectBidDocREQ.getSectionIdArray()[m]);
                    if (-1 == thousand.compareTo(section.getTenderFee())) {
                        map.put("flag", true);
                        break;
                    }
                }
            } else {
                map.put("flag", false);
            }
    }

    /**
     * 重新招标退款
     *
     * @param dataInfoREQ
     */
    private void refund(DocDataInfoREQ dataInfoREQ) {
        if (dataInfoREQ.getRefund()) {
            //获取原项目编号
            List<Long> refundSectionIds = dataInfoREQ.getRefundSectionIds();
            for (Long nowId : refundSectionIds) {
                ProjectBidSection section = projectBidSectionService.getById(nowId);
                Long formerSectionId = section.getFormerSectionId();
                if (formerSectionId != null) {
                    //查询上个项目公告信息，看线上支付还是线下支付
                    ProjectBidDoc doc = this.baseMapper.getDocInfoBySectionId(formerSectionId, section.getBidRound());

                    //线上支付 退款
                    if (doc != null && doc.getPaymentType() != null && doc.getPaymentType().equals(com.hzw.sunflower.constant.CommonConstants.YES) || doc != null && doc.getPaymentType() != null && doc.getPaymentType().equals(com.hzw.sunflower.constant.CommonConstants.NO2)) {
                        if (!OldBidderTypeEnum.EMPTY.getType().equals(section.getOldBidderType()) && !section.getPurchaseStatus().equals(PurchaseStatusEnum.PRE_TRIAL.getType())) {
                            //查询本轮代入的供应商
                            LambdaQueryWrapper<ApplyInfo> queryWrapper = new LambdaQueryWrapper<>();
                            queryWrapper.eq(ApplyInfo::getSubId, nowId)
                                    .eq(ApplyInfo::getBidRound, section.getBidRound());
                            List<ApplyInfo> list = applyInfoService.list(queryWrapper);
                            List<Long> supplierIds = new ArrayList<>();
                            list.forEach(a -> {
                                supplierIds.add(a.getCompanyId());
                            });

                            //查询本次代入的支付过的供应商信息

                            List<ApplyInfo> onlineList = projectBidDocMapper.getOnlinelist(formerSectionId, section.getBidRound(), supplierIds);
                            for(int i = 0; i < onlineList.size(); i++) {
                                onlineList.get(i).setIsRefundFee(com.hzw.sunflower.constant.CommonConstants.YES);
                            }
                            applyInfoService.updateBatchById(onlineList);

//                            LambdaQueryWrapper<ApplyInfo> updateWrapper = new LambdaQueryWrapper<>();
//                            updateWrapper.eq(ApplyInfo::getSubId, formerSectionId)
//                                    .eq(ApplyInfo::getBidRound, section.getBidRound())
//                                    .eq(ApplyInfo::getDownloadFlag, com.hzw.sunflower.constant.CommonConstants.YES)
//                                    .isNotNull(ApplyInfo::getPayId);
//
//                            if (supplierIds != null && supplierIds.size() > 0) {
//                                updateWrapper.in(ApplyInfo::getCompanyId, supplierIds);
//                            }
//                            ApplyInfo applyInfo = new ApplyInfo();
//                            applyInfo.setIsRefundFee(com.hzw.sunflower.constant.CommonConstants.YES);
//                            applyInfoService.update(applyInfo, updateWrapper);
                        }
                    }
                }
            }
        } else {
            //将本轮标识过的退款标识重置为0，防止撤回重新改数据
            LambdaQueryWrapper<ProjectBidDocRelation> docRelationQuery = new LambdaQueryWrapper<>();
            docRelationQuery.eq(ProjectBidDocRelation::getDocId,dataInfoREQ.getBidDocInfo().get(0).getDocId());
            List<ProjectBidDocRelation> docRelations = projectBidDocRelationService.list(docRelationQuery);
            docRelations.forEach(d->{
                ProjectBidSection section = projectBidSectionService.getById(d.getSectionId());
                //查询本轮代入的供应商
                LambdaQueryWrapper<ApplyInfo> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(ApplyInfo::getSubId, section.getId())
                        .eq(ApplyInfo::getBidRound, section.getBidRound());
                List<ApplyInfo> list = applyInfoService.list(queryWrapper);
                List<Long> supplierIds = new ArrayList<>();
                list.forEach(a -> {
                    supplierIds.add(a.getCompanyId());
                });

                //查询本次代入的支付过的供应商信息
                LambdaQueryWrapper<ApplyInfo> updateWrapper = new LambdaQueryWrapper<>();
                updateWrapper.eq(ApplyInfo::getSubId, section.getFormerSectionId())
                        .eq(ApplyInfo::getBidRound, section.getBidRound())
                        .eq(ApplyInfo::getDownloadFlag, com.hzw.sunflower.constant.CommonConstants.YES)
                        .isNotNull(ApplyInfo::getPayId);
                if (supplierIds != null && supplierIds.size() > 0) {
                    updateWrapper.in(ApplyInfo::getCompanyId, supplierIds);
                }
                ApplyInfo applyInfo = new ApplyInfo();
                applyInfo.setIsRefundFee(com.hzw.sunflower.constant.CommonConstants.NO);
                applyInfoService.update(applyInfo, updateWrapper);
            });
        }
    }

    /**
     * 关联标段号
     *
     * @param projectBidDocREQ
     */
    private void dealSection(ProjectBidDocREQ projectBidDocREQ) {
        Long secctionId = projectBidDocREQ.getSectionIdArray()[0];
        ProjectBidSection section = projectBidSectionService.getById(secctionId);
        //资格预审按项目
        LambdaQueryWrapper<ProjectBidSection> bidQueryWrapper = new LambdaQueryWrapper<>();
        bidQueryWrapper.eq(ProjectBidSection::getProjectId, projectBidDocREQ.getProjectId() );
        bidQueryWrapper.eq(ProjectBidSection::getPurchaseStatus, PurchaseStatusEnum.PRE_TRIAL.getType());
        bidQueryWrapper.eq(ProjectBidSection::getPurchaseMode, section.getPurchaseMode());
        bidQueryWrapper.ge(ProjectBidSection::getStatus, PackageStatusEnum.TENDER_INVITATION.getValue());
        bidQueryWrapper.orderByAsc(ProjectBidSection::getPackageNumber);
        List<ProjectBidSection> bidSections = projectBidSectionService.list(bidQueryWrapper);
        //资格预审文件费 收费
        Boolean tenderFeeFlag = false;
        if (TenderFeeFlagEnum.CHARGE.getValue().equals(projectBidDocREQ.getTenderFeeFlag())) {
            tenderFeeFlag = true;
        }
        //保证金按 比例 收取
        Integer bondNumber = 0;
        if (BondTypeEnum.PROPORTION.getValue().equals(projectBidDocREQ.getBondType())) {
            bondNumber = 1;
        } else if (BondTypeEnum.QUOTA.getValue().equals(projectBidDocREQ.getBondType())) {
            //保证金按定额 收取
            bondNumber = 2;
        }
        //招标文件发售结束时间类型为 确定时间
        Boolean saleTimeFlag = true;
        //if (TimeTypeEnum.CONFIRM.getValue().equals(projectBidDocREQ.getSaleEndTimeType())) {
        //    saleTimeFlag = true;
        //}
        Boolean submitTimeFlag = false;
        //投标文件递交截止时间类型为 确定时间
        if (TimeTypeEnum.CONFIRM.getValue().equals(projectBidDocREQ.getSubmitEndTimeType())) {
            submitTimeFlag = true;
        }
        //澄清异议时间
        Boolean clarifyEndTimeFlag = false;
        if (projectBidDocREQ.getClarifyOnLine().equals(TimeTypeEnum.SUPPORT.getValue()) && TimeTypeEnum.CONFIRM.getValue().equals(projectBidDocREQ.getClarifyEndTimeType())) {
            clarifyEndTimeFlag = true;
        }
        Boolean questionEndTimeFlag = false;
        if (projectBidDocREQ.getQuestionOnLine().equals(TimeTypeEnum.SUPPORT.getValue()) && TimeTypeEnum.CONFIRM.getValue().equals(projectBidDocREQ.getQuestionEndTimeType())) {
            questionEndTimeFlag = true;
        }
        //  线上开标
        Boolean onLineFlag = false;
        if (CommonConstants.YES.equals(projectBidDocREQ.getOnlineBidOpen())) {
             onLineFlag = true;
        }

        //包号拼接
        String packageNumber = "";
        //标段ID处理
        List<Long> ids = new ArrayList<>();
        //发售结束时间
        List<Date> saleTime = new ArrayList<>();
        //递交截止时间
        List<Date> submitTime = new ArrayList<>();
        //保证金
        List<BigDecimal> bond = new ArrayList<>();
        List<BigDecimal> bondPercent = new ArrayList<>();
        //资格预审文件费
        List<BigDecimal> tenderFee = new ArrayList<>();
        //澄清异议
        List<Date> questionEndTime = new ArrayList<>();
        List<Date> clarifyEndTime = new ArrayList<>();
        // 解密时间
        List<Integer> decryptionTimeArray = new ArrayList<>();

        // 开标条件-终止开标供应商限制数量
        List<Integer> conditionBidOpenArray = new ArrayList<>();
        List<Integer> systemAutomaticLabelingArray = new ArrayList<>();
        List<Integer> generationArray = new ArrayList<>();
        List<Long> bidOpeningArray = new ArrayList<>();
        for (int j = 0; j < bidSections.size(); j++) {
            ids.add(bidSections.get(j).getId());
            if (saleTimeFlag) {
                saleTime.add(projectBidDocREQ.getSaleEndTimeArray()[0]);
            }
            if (submitTimeFlag) {
                submitTime.add(projectBidDocREQ.getSubmitEndTimeArray()[0]);
            }
            if (tenderFeeFlag) {
                tenderFee.add(projectBidDocREQ.getTenderFee());
            }
            if (1 == bondNumber) {
                //计算保证金
                bond.add(bidSections.get(j).getEntrustMoney().multiply(projectBidDocREQ.getBondPercentArray()[0].divide(new BigDecimal("100"))));
                bondPercent.add(projectBidDocREQ.getBondPercentArray()[0]);
            } else if (2 == bondNumber) {
                bond.add(projectBidDocREQ.getBondArray()[0]);
            }
            packageNumber += bidSections.get(j).getPackageNumber();
            if (j != bidSections.size() - 1) {
                packageNumber += "、";
            }
            if(clarifyEndTimeFlag){
                clarifyEndTime.add(projectBidDocREQ.getClarifyEndTimeArray()[0]);
            }
            if(questionEndTimeFlag){
                questionEndTime.add(projectBidDocREQ.getQuestionEndTimeArray()[0]);
            }
            if(onLineFlag){
                decryptionTimeArray.add(projectBidDocREQ.getDecryptionTimeArray()[0]);
                conditionBidOpenArray.add(projectBidDocREQ.getConditionBidOpenArray()[0]);
                systemAutomaticLabelingArray.add(projectBidDocREQ.getSystemAutomaticLabelingArray()[0]);
                generationArray.add(projectBidDocREQ.getGenerationArray()[0]);
                bidOpeningArray.add(projectBidDocREQ.getBidOpeningArray()[0]);
            }
        }
        projectBidDocREQ.setPackageNumber(packageNumber);
        projectBidDocREQ.setSectionIdArray(ids.stream().distinct().toArray(Long[]::new));
        projectBidDocREQ.setBondArray(bond.stream().toArray(BigDecimal[]::new));
        projectBidDocREQ.setBondPercentArray(bondPercent.stream().toArray(BigDecimal[]::new));
        projectBidDocREQ.setTenderFeeArray(tenderFee.stream().toArray(BigDecimal[]::new));
        projectBidDocREQ.setSaleEndTimeArray(saleTime.stream().toArray(Date[]::new));
        projectBidDocREQ.setSubmitEndTimeArray(submitTime.stream().toArray(Date[]::new));
        projectBidDocREQ.setClarifyEndTimeArray(clarifyEndTime.stream().toArray(Date[]::new));
        projectBidDocREQ.setQuestionEndTimeArray(questionEndTime.stream().toArray(Date[]::new));
        projectBidDocREQ.setDecryptionTimeArray(decryptionTimeArray.stream().toArray(Integer[]::new));
        projectBidDocREQ.setConditionBidOpenArray(conditionBidOpenArray.stream().toArray(Integer[]::new));
        projectBidDocREQ.setSystemAutomaticLabelingArray(systemAutomaticLabelingArray.stream().toArray(Integer[]::new));
        projectBidDocREQ.setGenerationArray(generationArray.stream().toArray(Integer[]::new));
        projectBidDocREQ.setBidOpeningArray(bidOpeningArray.stream().toArray(Long[]::new));
    }

    /**
     * 修改套关联的招标文件的标书费支付方式
     *
     * @param projectBidDocREQ
     * @param split
     */
    private Boolean modifyDocPaymentType(ProjectBidDocREQ projectBidDocREQ, String[] split) {
        Boolean flag = false;
        //查询关联文件信息
        LambdaQueryWrapper<ProjectBidDocRelation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ProjectBidDocRelation::getSectionId, split);
        List<ProjectBidDocRelation> docRelationList = projectBidDocRelationService.list(queryWrapper);
        for (int i = 0; i < docRelationList.size(); i++) {
            ProjectBidDoc projectBidDoc = new ProjectBidDoc();
            projectBidDoc.setId(docRelationList.get(i).getDocId());
            projectBidDoc.setPaymentType(projectBidDocREQ.getPaymentType());
            flag = updateById(projectBidDoc);
        }
        return flag;
    }

    /**
     * 重新招标收费时，修改报名情况信息
     *
     * @param projectBidDocREQ
     * @return
     */
    private boolean modifyReBidApplyInfo(ProjectBidDocREQ projectBidDocREQ) {
        Boolean flag = true;
        List<ApplyInfo> list = new ArrayList<>();
        for (int i = 0; i < projectBidDocREQ.getSectionIdArray().length; i++) {
            LambdaQueryWrapper<ApplyInfo> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(ApplyInfo::getSubId, projectBidDocREQ.getSectionIdArray()[i]);
            List<ApplyInfo> applyInfoList = applyInfoService.list(queryWrapper);
            if (!CollectionUtil.isEmpty(applyInfoList)) {
                for (ApplyInfo applyInfo : applyInfoList) {
                    if (applyInfo != null) {
                        if (applyInfo.getActivityStatus().equals(CommonConstants.ACTIVITY_STATUS_2)) {
                            applyInfo.setActivityStatus(1);
                        }
                        applyInfo.setDownloadFlag(CommonConstants.NO);
                        applyInfo.setPayId(0L);
                        list.add(applyInfo);
                    }
                }
            }
        }
        if (!CollectionUtil.isEmpty(list)) {
            flag = applyInfoService.updateBatchById(list);
        }
        return flag;
    }

    /**
     * 保存审批记录
     *
     * @param type
     * @param jwtUser
     * @param projectId
     * @param docId
     * @param message
     * @return
     */
    private Boolean saveApprovalOpinion(Integer type, JwtUser jwtUser, Long projectId, Long docId, String message,String appContent) {
        ApprovalOpinion approvalOpinion = new ApprovalOpinion();
        approvalOpinion.setApprovalType(type);
        approvalOpinion.setApprovalUserId(jwtUser.getUserId());
        approvalOpinion.setApprovalUserName(jwtUser.getUserName());
        approvalOpinion.setProjectId(projectId);
        approvalOpinion.setApprovalTime(DateUtil.date());
        approvalOpinion.setDocId(docId);
        approvalOpinion.setApprovalContent(message);
        approvalOpinion.setNextUserName(appContent);
        return approvalOpinionService.save(approvalOpinion);
    }

    @Override
    public Boolean validate(List<ProjectBidDocREQ> list) {
        //非空验证
        if (CollectionUtil.isEmpty(list)) {
            return true;
        }
        for (ProjectBidDocREQ bidDocData : list) {
            BigDecimal zero = new BigDecimal(FormatConstants.ENTRUST_MONEY);
            //保证金类型 比例
            if (BondTypeEnum.PROPORTION.getValue().equals(bidDocData.getBondType())) {
                if (bidDocData.getSectionIdArray().length != bidDocData.getBondArray().length ||
                        bidDocData.getSectionIdArray().length != bidDocData.getBondPercentArray().length) {
                    return true;
                }
                //金额验证
                List<BigDecimal> collect = Arrays.stream(bidDocData.getBondArray()).filter(
                        s -> 1 > zero.compareTo(s)).collect(Collectors.toList());
                if (collect.size() != bidDocData.getBondArray().length) {
                    return true;
                }
                BigDecimal money = new BigDecimal(FormatConstants.ENTRUST_MONEY);
                for (int j = 0; j < bidDocData.getSectionIdArray().length; j++) {
                    //获取 单个标段 委托金额
                    BigDecimal entrustMoney = projectBidSectionService.getById(bidDocData.getSectionIdArray()[j]).getEntrustMoney();
                    if (null != entrustMoney) {
                        //保留小数点后六位，四舍五入
                        money = entrustMoney.multiply(bidDocData.getBondPercentArray()[j]).divide(new BigDecimal(100)).setScale(2, RoundingMode.HALF_DOWN);

                    }
                    if (0 != money.compareTo(bidDocData.getBondArray()[j])) {
                        return true;
                    }
                }
            } else if (BondTypeEnum.QUOTA.getValue().equals(bidDocData.getBondType())) {
                //金额验证
                List<BigDecimal> collect = Arrays.stream(bidDocData.getBondArray()).filter(
                        s -> 1 > zero.compareTo(s)).collect(Collectors.toList());
                if (collect.size() != bidDocData.getBondArray().length) {
                    return true;
                }
            }
            //标书费收费
            if (TenderFeeFlagEnum.CHARGE.getValue().equals(bidDocData.getTenderFeeFlag())) {
                //标书费类型 按标段/包
                if (TenderFeeTypeEnum.SECTION.getValue().equals(bidDocData.getTenderFeeType())) {
                    //标书费收费方式不为空
                    if (bidDocData.getPaymentType() == null) {
                        return true;
                    }
                    if (bidDocData.getSectionIdArray().length != bidDocData.getTenderFeeArray().length) {
                        return true;
                    }
                    //金额验证
                    List<BigDecimal> collect = Arrays.stream(bidDocData.getTenderFeeArray()).filter(
                            s -> 0 > zero.compareTo(s)).collect(Collectors.toList());
                    if (collect.size() != bidDocData.getTenderFeeArray().length) {
                        return true;
                    }
                } else if (TenderFeeTypeEnum.GROUP.getValue().equals(bidDocData.getTenderFeeType())) {//按套
                    //标书费收费方式不为空
                    if (bidDocData.getPaymentType() == null) {
                        return true;
                    }
                    if (zero.compareTo(bidDocData.getTenderFee()) >= 0) {
                        return true;
                    }
                } else {//不划分包
                    //标书费收费方式不为空
                    if (bidDocData.getPaymentType() == null) {
                        return true;
                    }
                    if (zero.compareTo(bidDocData.getTenderFee()) >= 0) {
                        return true;
                    }
                }
            }

        }
        return false;
    }

    @Override
    public List<ProjectBidDocDTO> queryDocProgress(DocBidRoundREQ docBidRoundREQ) {
        List<ProjectBidDocDTO> projectBidDocDTOS = this.baseMapper.queryDocProgress(docBidRoundREQ);
        for (ProjectBidDocDTO projectBidDocDTO : projectBidDocDTOS) {
            List<SectionExpertSpecialty> list = iSectionExpertSpecialtyService.list(new LambdaQueryWrapper<SectionExpertSpecialty>()
                    .eq(SectionExpertSpecialty::getSectionId, projectBidDocDTO.getSectionId()));
            List<Long> spids = list.stream()
                    .map(m -> {
                        return m.getExpertSpecialtyId();
                    }).collect(Collectors.toList());
            projectBidDocDTO.setSectionExpertSpecialtyList(spids);
        }
        return projectBidDocDTOS;
    }

    @Override
    public List<ProjectBidDocDTO> queryDocInfo(Long docId) {
        return this.baseMapper.queryDocInfo(docId);
    }

    @Override
    public Boolean updateProgress(NoticeProcessREQ info, JwtUser jwtUser) {
        ProjectBidDoc projectBidDoc = this.getById(info.getDocId());
        if (null == projectBidDoc) {
            return false;
        } else {
            //该文件关联的标段集合
            LambdaQueryWrapper<ProjectBidDocRelation> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(ProjectBidDocRelation::getDocId, projectBidDoc.getId());
            List<ProjectBidDocRelation> docRelationList = projectBidDocRelationService.list(queryWrapper);
            //待修改标段集合
            List<ProjectBidSection> section = new ArrayList<>();
            List<Long> sectionids = new ArrayList<>();
            boolean flag = true;
            String userCode = jwtUser.getUserOtherId();
            //请求工作流返回结果
            String result = "";
            //返回结果实体类
            ReturnVo returnVo = null;
            //3：撤回
            if (NoticeProgressEnum.WITHDRAWAL.getValue().equals(info.getNoticeProgress())) {
                String appContent = "";
                if (flag) {
                    returnVo = withdrawal(userCode, projectBidDoc, docRelationList, section);
                    if (null == returnVo || !ReturnCodeConstants.PROCESS_SUCCESS_CODE.equals(returnVo.getCode())) {
                        throw new SunFlowerException(ExceptionEnum.BID_DOC_AUTH_ERROR, returnVo.getMsg());
                    } else if (ReturnCodeConstants.PROCESS_SUCCESS_CODE.equals(returnVo.getCode())) {
                        if ("end".equalsIgnoreCase(returnVo.getMsg())) {
                            //已确认
                            projectBidDoc.setNoticeProgress(NoticeProgressEnum.WITHDRAWAL.getValue());
                        } else {
                            //确认中
                            if(projectBidDoc.getNoticeProgress().equals(NoticeProgressEnum.UNDERREVIEW.getValue())
                              ||projectBidDoc.getNoticeProgress().equals(NoticeProgressEnum.WITHDRAWAL.getValue())){
                                projectBidDoc.setNoticeProgress(NoticeProgressEnum.WITHDRAWAL.getValue());
                            }else{
                                projectBidDoc.setNoticeProgress(NoticeProgressEnum.WITHDRAWUNDERREVIEW.getValue());
                            }
                            // 撤回删除流程 返回的是null ,撤回启动流程返回的是流程id
                            if(null != returnVo.getData()){
                                String taskUserlist = flowApiClient.getTaskUserByProcessInstanceId(SecurityUtils.getJwtUser().getUserOtherId(), returnVo.getData().toString());
                                JSONObject jsonObjectTask = JSON.parseObject(taskUserlist);
                                List<TaskUserVo> taskUserVoList = JSONObject.parseArray(jsonObjectTask.getString("data"), TaskUserVo.class);
                                List<String> recipientList = new ArrayList<>();
                                for (TaskUserVo taskUserVo : taskUserVoList) {
                                    if (!"".equals(appContent)){
                                        appContent+=","+taskUserVo.getUserName();
                                    }else {
                                        appContent=taskUserVo.getUserName();
                                    }
                                    recipientList.add(taskUserVo.getUserCode());
                                }

                                // 发送app待办
                                AppOaMsgDto dto = new AppOaMsgDto();
                                dto.setTaskType(AppTaskTypeEnum.NOTICE_DOC.getCode());
                                dto.setRecipientList(recipientList);
                                commonMqService.sendOaMsg(dto);
                            }

                        }
                    }
                }
                //保存审批流程记录
                flag = saveApprovalOpinion(NoticeProgressEnum.UNDERREVIEW.getValue().equals(projectBidDoc.getNoticeProgress()) ? ApprovalTypeEnum.REVIEW.getValue() : ApprovalTypeEnum.APPLY_REVIEW.getValue(),
                        jwtUser, info.getProjectId(), info.getDocId(), null,appContent);

                //再次撤回，删除待处理事项表
                LambdaQueryWrapper<PendingItem> pendingItemLambdaQueryWrapper = new LambdaQueryWrapper<>();
                pendingItemLambdaQueryWrapper.eq(PendingItem::getBusinessCode, ReturnListEnum.TENDER_DOC.getCode());
                pendingItemLambdaQueryWrapper.eq(PendingItem::getBusinessId,info.getDocId());
                pendingItemService.remove(pendingItemLambdaQueryWrapper);
            } else if (NoticeProgressEnum.REVIEW.getValue().equals(info.getNoticeProgress())) {
                //4：已确认
                //更新code
                String appContent = "";//下一级审批人
                docRelationList.stream().forEach((e) -> {
                    ProjectBidSection projectBidSection = new ProjectBidSection();
                    projectBidSection.setId(e.getSectionId());
                    projectBidSection.setBidSectionCode(BidSectionCodeEnum.bidding_file_confirm.getCode());
                    section.add(projectBidSection);
                    sectionids.add(e.getSectionId());
                });
                //保存审批流程记录

                if (flag) {
                    result = flowApiClient.review(userCode, info.getProcessInstanceId(), info.getTaskId(), info.getMessage(), true);
                    returnVo = JSONObject.parseObject(result, ReturnVo.class);

                    if (null == returnVo || !ReturnCodeConstants.PROCESS_SUCCESS_CODE.equals(returnVo.getCode())) {
                        throw new SunFlowerException(ExceptionEnum.BID_DOC_AUTH_ERROR, returnVo.getMsg());
                    } else if (ReturnCodeConstants.PROCESS_SUCCESS_CODE.equals(returnVo.getCode())) {
                        List<String> recipientList = new ArrayList<>();
                        if ("end".equalsIgnoreCase(returnVo.getMsg())) {
                            //已确认
                            projectBidDoc.setNoticeProgress(NoticeProgressEnum.REVIEW.getValue());

                            // 发送app待办
                            AppOaMsgDto dto = new AppOaMsgDto();
                            dto.setTaskType(AppTaskTypeEnum.TENDER_DOC.getCode());
                            recipientList.add(projectBidDoc.getCreatedUserId().toString());
                            dto.setRecipientList(recipientList);
                            commonMqService.sendOaMsg(dto);
                        } else {
                            //确认中
                            projectBidDoc.setNoticeProgress(NoticeProgressEnum.CONFIRMING.getValue());
                            String taskUserlist = flowApiClient.getTaskUserByProcessInstanceId(SecurityUtils.getJwtUser().getUserOtherId(), returnVo.getData().toString());
                            JSONObject jsonObjectTask = JSON.parseObject(taskUserlist);
                            List<TaskUserVo> taskUserVoList = JSONObject.parseArray(jsonObjectTask.getString("data"), TaskUserVo.class);

                            for (TaskUserVo taskUserVo : taskUserVoList) {
                                if (!"".equals(appContent)){
                                    appContent+=","+taskUserVo.getUserName();
                                }else {
                                    appContent=taskUserVo.getUserName();
                                }
                                recipientList.add(taskUserVo.getUserCode());
                            }

                            // 发送app待办
                            AppOaMsgDto dto = new AppOaMsgDto();
                            dto.setTaskType(AppTaskTypeEnum.NOTICE_DOC.getCode());
                            dto.setRecipientList(recipientList);
                            commonMqService.sendOaMsg(dto);

                        }
                    }
                }
                flag = saveApprovalOpinion(ApprovalTypeEnum.AGREE.getValue(), jwtUser, info.getProjectId(), info.getDocId(), info.getMessage(),appContent);
            } else if (NoticeProgressEnum.RETURN.getValue().equals(info.getNoticeProgress())) {
                //6：已退回
                String appContent = "";
                //更新code
                docRelationList.stream().forEach((e) -> {
                    ProjectBidSection projectBidSection = new ProjectBidSection();
                    ProjectBidSection bidSection = projectBidSectionService.getById(e.getSectionId());
                    BeanUtils.copyProperties(bidSection,projectBidSection);
                    if (!PackageStatusEnum.NOTICE_ISSUED.getValue().toString().equals(bidSection.getStatus())) {
                        if (projectBidNoticeService.judgeSectionStatusById(e.getSectionId())) {
                            projectBidSection.setStatus(PackageStatusEnum.EDIT_BY_NOTICE.getValue().toString());
                        } else {
                            projectBidSection.setStatus(PackageStatusEnum.READONLY_BY_NOTICE.getValue().toString());
                        }
                    }
                    projectBidSection.setBidSectionCode(BidSectionCodeEnum.bidding_file_return.getCode());
                    section.add(projectBidSection);
                });
                //保存审批流程记录

                if (flag) {
                    //退回
                    projectBidDoc.setNoticeProgress(NoticeProgressEnum.RETURN.getValue());
                    result = flowApiClient.review(userCode, info.getProcessInstanceId(), info.getTaskId(), info.getMessage(), false);
                    returnVo = JSONObject.parseObject(result, ReturnVo.class);
                    if (null == returnVo || !ReturnCodeConstants.PROCESS_SUCCESS_CODE.equals(returnVo.getCode())) {
                        throw new SunFlowerException(ExceptionEnum.BID_DOC_AUTH_ERROR, returnVo.getMsg());
                    }else{
                        if (!"end".equalsIgnoreCase(returnVo.getMsg())) {
                            String taskUserlist = flowApiClient.getTaskUserByProcessInstanceId(SecurityUtils.getJwtUser().getUserOtherId(), returnVo.getData().toString());
                            JSONObject jsonObjectTask = JSON.parseObject(taskUserlist);
                            List<TaskUserVo> taskUserVoList = JSONObject.parseArray(jsonObjectTask.getString("data"), TaskUserVo.class);
                            if(taskUserVoList != null && taskUserVoList.size()>0) {
                                for (TaskUserVo taskUserVo : taskUserVoList) {
                                    if (!"".equals(appContent)) {
                                        appContent += "," + taskUserVo.getUserName();
                                    } else {
                                        appContent = taskUserVo.getUserName();
                                    }
                                }
                            }
                        }
                    }
                    // 退回时添加待处理事项表
                    LambdaQueryWrapper<ProjectBidDocRelation> projectBidDocRelationLambdaQueryWrapper =  new LambdaQueryWrapper<>();
                    projectBidDocRelationLambdaQueryWrapper.eq(ProjectBidDocRelation::getProjectId,info.getProjectId());
                    projectBidDocRelationLambdaQueryWrapper.eq(ProjectBidDocRelation::getDocId,info.getDocId());
                    List<ProjectBidDocRelation> projectBidDocRelationList = projectBidDocRelationService.list(projectBidDocRelationLambdaQueryWrapper);
                    StringBuilder sectionIds = new StringBuilder();
                    List<Long> sectionList = new ArrayList<>();
                    for (ProjectBidDocRelation projectBidDocRelation : projectBidDocRelationList) {
                        sectionList.add(projectBidDocRelation.getSectionId());
                        sectionIds.append(projectBidDocRelation.getSectionId()).append(",");
                    }
                    sectionIds = new StringBuilder(sectionIds.substring(0, sectionIds.length() - 1));
                    PendingItem pendingItem = new PendingItem();
                    pendingItem.setOperationTime(new Date());
                    pendingItem.setBusinessCode(ReturnListEnum.TENDER_DOC.getCode());
                    pendingItem.setBusinessId(info.getDocId());
                    pendingItem.setBusinessType(2);
                    pendingItem.setProjectId(info.getProjectId());
                    pendingItem.setSectionId(sectionIds.toString());
                    //查询标段轮次
                    LambdaQueryWrapper<ProjectBidSection> projectBidSectionLambdaQueryWrapper = new LambdaQueryWrapper<>();
                    projectBidSectionLambdaQueryWrapper.in(ProjectBidSection::getId,sectionList);
                    List<ProjectBidSection> projectBidSections = projectBidSectionMapper.selectList(projectBidSectionLambdaQueryWrapper);
                    pendingItem.setBidRound(projectBidSections.get(0).getBidRound());

                    //查询申请人和申请时间
                    ProjectBidDoc bidDoc = projectBidDocMapper.selectById(info.getDocId());
                    LambdaQueryWrapper<ApprovalOpinion> approvalOpinionLambdaQueryWrapper = new LambdaQueryWrapper<>();
                    approvalOpinionLambdaQueryWrapper.eq(ApprovalOpinion::getApprovalUserId,bidDoc.getCreatedUserId());
                    approvalOpinionLambdaQueryWrapper.eq(ApprovalOpinion::getDocId,bidDoc.getId());
                    approvalOpinionLambdaQueryWrapper.orderByDesc(ApprovalOpinion::getCreatedTime);
                    approvalOpinionLambdaQueryWrapper.last("limit 1");
                    ApprovalOpinion approvalOpinion = approvalOpinionMapper.selectOne(approvalOpinionLambdaQueryWrapper);
                    pendingItem.setApplyTime(approvalOpinion.getApprovalTime());
                    pendingItem.setApplyUserId(approvalOpinion.getApprovalUserId());

                    pendingItemService.save(pendingItem);

                    // 发送app待阅
                    commonMqService.sendOaReadMsg(ReturnListEnum.TENDER_DOC.getDesc(), pendingItem);
                }
                flag = saveApprovalOpinion(ApprovalTypeEnum.DRAWBACK.getValue(), jwtUser, info.getProjectId(), info.getDocId(), info.getMessage(),appContent);
            } else if (NoticeProgressEnum.RELEASE.getValue().equals(info.getNoticeProgress())) {
                //5: 已发布
                projectBidDoc.setNoticeProgress(NoticeProgressEnum.RELEASE.getValue());
                //挂网时间
                projectBidDoc.setReleaseTime(DateTime.now());
                //标段ID集合
                List<Long> list = new ArrayList<>();
                //更新code
                docRelationList.stream().forEach((e) -> {
                    ProjectBidSection projectBidSection = projectBidSectionService.getById(e.getSectionId());
                    projectBidSection.setId(e.getSectionId());
                    list.add(e.getSectionId());
                    projectBidSection.setBidSectionCode(BidSectionCodeEnum.bidding_file_release.getCode());
                    //更改状态为 20：发标
                    projectBidSection.setStatus(PackageStatusEnum.ANNOUNCING.getValue().toString());
                    section.add(projectBidSection);
                    //调用 按套加包接口
                    //ProjectBidSection bidSection = projectBidSectionService.getById(e.getSectionId());
                    //applyInfoService.addSectionGroupId(bidSection);


                    //编辑页面修改修改标书费/按套卖/标书费支付方式后 退回原标段的标书费
                    Long formerSectionId = projectBidSection.getFormerSectionId();
                    if(formerSectionId != null) {
                        //查询需要退回的供应商id
                        LambdaQueryWrapper<ApplyInfo> refundWrapper = new LambdaQueryWrapper<>();
                        refundWrapper.eq(ApplyInfo::getSubId, formerSectionId)
                                .eq(ApplyInfo::getBidRound, projectBidDoc.getBidRound())
                                .eq(ApplyInfo::getIsRefundFee, com.hzw.sunflower.constant.CommonConstants.YES);
                        List<ApplyInfo> refunds = applyInfoService.list(refundWrapper);
                        for(ApplyInfo a :refunds){
                            RefundFeeReq feeReq = new RefundFeeReq();
                            feeReq.setPayId(a.getPayId());
                            feeReq.setRefundReason("重新招标");
                            bidConfirmService.updateRefundFee(feeReq,jwtUser);
                        }
                    }

                    //根据标段ID查询已购买招标文件的投标人本次是否收费
                    String free = applyInfoService.queryFree(projectBidSection.getId(),projectBidSection.getBidRound());

                    //重新招标后把已购买标书的投标人带过来
                    LambdaQueryWrapper<ApplyInfo>  applyQuery = new LambdaQueryWrapper<>();
                    applyQuery.eq(ApplyInfo::getBidRound,projectBidDoc.getBidRound())
                            .eq(ApplyInfo::getProjectId,projectBidDoc.getProjectId())
                            .eq(ApplyInfo::getSubId,e.getSectionId());
                    //查询出原有的招标人，有则不添加
                    long count = applyInfoService.count(applyQuery);
                    if(count<=0 && !OldBidderTypeEnum.EMPTY.getType().equals(projectBidSection.getOldBidderType()) && !projectBidSection.getPurchaseStatus().equals(PurchaseStatusEnum.PRE_TRIAL.getType())) {
                        //修改原来项目的关注的供应商的is_form_up为0
                        LambdaQueryWrapper<ApplyInfo> updateQuery = new LambdaQueryWrapper<>();
                        updateQuery.eq(ApplyInfo::getSubId, projectBidSection.getFormerSectionId());
                        ApplyInfo upapply = new ApplyInfo();
                        upapply.setIsFormerUp(0);
                        applyInfoService.update(upapply,updateQuery);

                        //查询上次投标人信息
                        SupplierInfoREQ supplierInfo = new SupplierInfoREQ();
                        //原标段Id
                        supplierInfo.setFormerSectionId(projectBidSection.getFormerSectionId());
                        //默认已购买标书的投标人
                        supplierInfo.setOldBidderType(OldBidderTypeEnum.BUY.getType());
                        List<SupplierInfoDTO> supplierInfos = projectBidSectionService.getSupplierInfo(supplierInfo);
                        //遍历添加
                        List<ApplyInfo> applyInfos = new ArrayList<>();
                        supplierInfos.forEach(s -> {
                            //修改isFormerUp
                            ApplyInfo apply = applyInfoService.getById(s.getApplyId());
                            apply.setIsFormerUp(com.hzw.sunflower.constant.CommonConstants.YES);
                            applyInfoService.updateById(apply);
                            //插入上次的投标人
                            ApplyInfo a = BeanListUtil.convert(apply,ApplyInfo.class);
                            a.setSubId(e.getSectionId());
                            a.setProjectId(projectBidDoc.getProjectId());
                            a.setFormerApplyId(apply.getApplyId());
                            //已购买招标文件的投标人本次是否收费 收费时购标情况为未购标
                            if("1".equals(free)){
                                a.setPayId(null);
                                a.setDownloadFlag(com.hzw.sunflower.constant.CommonConstants.NO);
                            }
                            applyInfos.add(a);
                        });
                        applyInfoService.saveBatch(applyInfos);
                    }
                });
                roundDataCopeService.copyRoundOneToRoundTwo(info.getProjectId(), list);
                flag = saveApprovalOpinion(ApprovalTypeEnum.RELEASE.getValue(), jwtUser, info.getProjectId(), info.getDocId(), info.getMessage(),"");

                //发布，删除待处理事项表
                LambdaQueryWrapper<PendingItem> pendingItemLambdaQueryWrapper = new LambdaQueryWrapper<>();
                pendingItemLambdaQueryWrapper.eq(PendingItem::getBusinessCode, ReturnListEnum.TENDER_DOC.getCode());
                pendingItemLambdaQueryWrapper.eq(PendingItem::getBusinessId,info.getDocId());
                pendingItemService.remove(pendingItemLambdaQueryWrapper);
            } else {
                projectBidDoc.setNoticeProgress(info.getNoticeProgress());
            }
            if (flag) {
                //更新标段状态
                flag = commonSectionService.updateSectionStatus(section);
            }
            if (flag) {
                flag = this.updateById(projectBidDoc);
                // 资格预审/采购文件挂网
                if(NoticeProgressEnum.RELEASE.getValue().equals(projectBidDoc.getNoticeProgress())){
                    PushProjectDataReq dataReq = new PushProjectDataReq();
                    dataReq.setProjectId(projectBidDoc.getProjectId());
                    if(BidRoundEnum.ZGYS.getType().equals(projectBidDoc.getBidRound())){
                        dataReq.setPushNode(PushNode.ZGYSWJGW.getDesc());
                    } else {
                        dataReq.setPushNode(PushNode.CGWJGW.getDesc());
                    }
                    companyPushDataService.projectPushOrNot(dataReq);
                    // 生成承诺文件
                    projectPromiseFileService.savePromiseFile(projectBidDoc.getProjectId(),docRelationList.get(0).getSectionId());
                }
            }
            if (!flag) {
                throw new SunFlowerException(ExceptionEnum.BID_DOC_AUTH_ERROR, returnVo.getMsg());
            }
            return flag;
        }
    }

    /**
     * 撤回处理
     *
     * @param userCode
     * @param projectBidDoc
     * @return
     */
    private ReturnVo withdrawal(String userCode, ProjectBidDoc projectBidDoc, List<ProjectBidDocRelation> docRelationList, List<ProjectBidSection> section) {
        String result = "";
        if (NoticeProgressEnum.UNDERREVIEW.getValue().equals(projectBidDoc.getNoticeProgress())) {
            //已撤回
            projectBidDoc.setNoticeProgress(NoticeProgressEnum.WITHDRAWAL.getValue());
            //更新code
            docRelationList.stream().forEach((e) -> {
                ProjectBidSection projectBidSection = new ProjectBidSection();
                projectBidSection.setId(e.getSectionId());
                ProjectBidSection bidSection = projectBidSectionService.getById(e.getSectionId());
                if (!PackageStatusEnum.NOTICE_ISSUED.getValue().toString().equals(bidSection.getStatus())) {
                    if (projectBidNoticeService.judgeSectionStatusById(e.getSectionId())) {
                        projectBidSection.setStatus(PackageStatusEnum.EDIT_BY_NOTICE.getValue().toString());
                    } else {
                        projectBidSection.setStatus(PackageStatusEnum.READONLY_BY_NOTICE.getValue().toString());
                    }
                }
                projectBidSection.setBidSectionCode(BidSectionCodeEnum.bidding_file_withdraw.getCode());
                section.add(projectBidSection);
            });
            String businessKey = TableNameConstants.PROJECT_BID_DOC + projectBidDoc.getId();
            result = flowApiClient.revokeProcess(userCode, businessKey, "");
        } else {
            //保存文件最新进度
            if (NoticeProgressEnum.REVIEW.getValue().equals(projectBidDoc.getNoticeProgress())) {
                projectBidDoc.setOldProgress(NoticeProgressEnum.REVIEW.getValue());
            } else {
                projectBidDoc.setOldProgress(NoticeProgressEnum.UNDERREVIEW.getValue());
            }
            //更新code
            docRelationList.stream().forEach((e) -> {
                ProjectBidSection projectBidSection = new ProjectBidSection();
                projectBidSection.setId(e.getSectionId());
                ProjectBidSection bidSection = projectBidSectionService.getById(e.getSectionId());
                if (!PackageStatusEnum.NOTICE_ISSUED.getValue().toString().equals(bidSection.getStatus())) {
                    if (projectBidNoticeService.judgeSectionStatusById(e.getSectionId())) {
                        projectBidSection.setStatus(PackageStatusEnum.EDIT_BY_NOTICE.getValue().toString());
                    } else {
                        projectBidSection.setStatus(PackageStatusEnum.READONLY_BY_NOTICE.getValue().toString());
                    }
                }
                projectBidSection.setBidSectionCode(BidSectionCodeEnum.bidding_file_withdraw_waitfor_confirm.getCode());
                section.add(projectBidSection);
            });
            //撤回待确认
            projectBidDoc.setNoticeProgress(NoticeProgressEnum.WITHDRAWUNDERREVIEW.getValue());
            String key = FlowClientConstant.PROCESS_DEFINITION_KEY_CH_NOTICE_DOC;
            String formName = projectBidDoc.getAnnexName();
            String businessKey = TableNameConstants.PROJECT_BID_DOC_CH + projectBidDoc.getId();
            result = flowApiClient.startProcessInstanceByKey(userCode, key, formName, businessKey, new HashMap<>());
        }
        return JSONObject.parseObject(result, ReturnVo.class);
    }

    @Override
    public List<ReviewBidDocDTO> queryReviewInfo(Long docId) {
        return this.baseMapper.queryReviewInfo(docId);
    }

    /**
     * 修改标段表 保证金、标书费 信息
     *
     * @param projectBidDocREQ
     * @return
     */
    private Boolean modifySectionInfo(ProjectBidDocREQ projectBidDocREQ, SectionGroup sectionGroup) {
        //标段信息集合
        List<ProjectBidSection> list = new ArrayList<>();
        for (int i = 0; i < projectBidDocREQ.getSectionIdArray().length; i++) {
            ProjectBidSection bidSection = new ProjectBidSection();
            bidSection.setId(projectBidDocREQ.getSectionIdArray()[i]);
            bidSection.setReleaseFileType(projectBidDocREQ.getReleaseFileType().toString());
            bidSection.setReviewFileType(projectBidDocREQ.getReviewFileType().toString());
            ProjectBidSection section = projectBidSectionService.getById(projectBidDocREQ.getSectionIdArray()[i]);
            if (!PackageStatusEnum.NOTICE_ISSUED.getValue().toString().equals(section.getStatus())) {
                bidSection.setStatus(PackageStatusEnum.READONLY_BY_NOTICE.getValue().toString());
            }
            bidSection.setMaterialList(projectBidDocREQ.getMaterialList());
            if (TenderFeeFlagEnum.FREE.getValue().equals(projectBidDocREQ.getTenderFeeFlag())) {
                bidSection.setTenderFeeType(TenderFeeTypeEnum.FREE.getValue());
            } else {
                bidSection.setTenderFeeType(projectBidDocREQ.getTenderFeeType());
            }
            bidSection.setBondType(projectBidDocREQ.getBondType());
            //标书费按按 标段/包 收取
            if (TenderFeeTypeEnum.SECTION.getValue().equals(projectBidDocREQ.getTenderFeeType())) {
                bidSection.setTenderFee(projectBidDocREQ.getTenderFeeArray()[i]);
            } else {
                bidSection.setTenderFee(projectBidDocREQ.getTenderFee());
            }
            if (TenderFeeFlagEnum.CHARGE.getValue().equals(projectBidDocREQ.getTenderFeeFlag())) {
                //按套收取标书费
                if (TenderFeeTypeEnum.GROUP.getValue().equals(projectBidDocREQ.getTenderFeeType())) {
                    bidSection.setSectionGroupId(sectionGroup.getId());
                } else {
                    //切换按包 、不收取 时，清空按套ID
                    bidSection.setSectionGroupId(0L);
                }
            }
            //保证金按 比例 收取
            if (BondTypeEnum.PROPORTION.getValue().equals(projectBidDocREQ.getBondType())) {
                bidSection.setBond(projectBidDocREQ.getBondArray()[i]);
                bidSection.setBondPercent(projectBidDocREQ.getBondPercentArray()[i]);
            } else if (BondTypeEnum.QUOTA.getValue().equals(projectBidDocREQ.getBondType())) {
                //保证金按定额 收取
                bidSection.setBond(projectBidDocREQ.getBondArray()[i]);
            } else {
                bidSection.setBond(projectBidDocREQ.getBond());
            }
            //标段code用于判断页面是新增编辑或详情
            bidSection.setBidSectionCode(BidSectionCodeEnum.bidding_file_submint.getCode());
            bidSection.setSaleEndTimeType(projectBidDocREQ.getSaleEndTimeType());
            //招标文件发售结束时间类型为 确定时间
            if (TimeTypeEnum.CONFIRM.getValue().equals(projectBidDocREQ.getSaleEndTimeType())) {
                bidSection.setSaleEndTime(projectBidDocREQ.getSaleEndTimeArray()[i]);
            } else if (TimeTypeEnum.PREVIOUS_DAY.getValue().equals(projectBidDocREQ.getSaleEndTimeType())
                    && TimeTypeEnum.CONFIRM.getValue().equals(projectBidDocREQ.getSubmitEndTimeType())) {
                //招标文件发售结束时间类型为 投标文件递交截止时间前一天，且投标文件递交截止时间类型为 确定时间
                //bidSection.setSaleEndTime(getPreviousDay(projectBidDocREQ.getSubmitEndTimeArray()[i]));
                bidSection.setSaleEndTime(projectBidDocREQ.getSaleEndTimeArray()[i]);
            } else {
                bidSection.setSaleEndTime(null);
            }
            bidSection.setSubmitEndTimeType(projectBidDocREQ.getSubmitEndTimeType());
            //投标文件递交截止时间类型为 确定时间
            if (TimeTypeEnum.CONFIRM.getValue().equals(projectBidDocREQ.getSubmitEndTimeType())) {
                bidSection.setSubmitEndTime(projectBidDocREQ.getSubmitEndTimeArray()[i]);
            } else {
                bidSection.setSubmitEndTime(null);
            }
            list.add(bidSection);
        }
        //清空取消文件关联的标段套ID
        for (int i = 0; i < projectBidDocREQ.getCancelAssociated().size(); i++) {
            ProjectBidSection bidSection = new ProjectBidSection();
            bidSection.setId(projectBidDocREQ.getCancelAssociated().get(i));
            bidSection.setSectionGroupId(0L);
            bidSection.setBidSectionCode(BidSectionCodeEnum.bidding_announcement_release.getCode());
            list.add(bidSection);
        }
        //重新关联其他招标文件的标段套ID
        if (null != sectionGroup) {
            String sectionIds = projectBidDocREQ.getSectionGroup().getSectionIds();
            if (StrUtil.isNotBlank(sectionIds)) {
                String[] split = sectionIds.split("、");
                for (int i = 0; i < split.length; i++) {
                    if(StringUtils.isNotBlank(split[i])) {
                        ProjectBidSection bidSection = new ProjectBidSection();
                        bidSection.setId(Long.parseLong(split[i]));
                        bidSection.setTenderFee(projectBidDocREQ.getTenderFee());
                        bidSection.setReleaseFileType(projectBidDocREQ.getReleaseFileType().toString());
                        bidSection.setReviewFileType(projectBidDocREQ.getReviewFileType().toString());
                        bidSection.setSectionGroupId(sectionGroup.getId());
                        list.add(bidSection);
                    }
                }
            }
        }
        return commonSectionService.updateSectionStatus(list);
    }

    /**
     * 保存招标文件关联信息
     *
     * @param projectBidDocREQ
     * @return
     */
    private Boolean saveDocRelationInfo(ProjectBidDocREQ projectBidDocREQ, Long docId) {
        List<ProjectBidDocRelation> list = new ArrayList<>();
        //逻辑删除已存在的关联信息
        LambdaUpdateWrapper<ProjectBidDocRelation> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ProjectBidDocRelation::getDocId, docId);
        updateWrapper.eq(ProjectBidDocRelation::getProjectId, projectBidDocREQ.getProjectId());
        projectBidDocRelationService.remove(updateWrapper);
        for (int i = 0; i < projectBidDocREQ.getSectionIdArray().length; i++) {
            //保存关联信息
            ProjectBidDocRelation docRelation = new ProjectBidDocRelation();
            docRelation.setDocId(docId);
            docRelation.setProjectId(projectBidDocREQ.getProjectId());
            docRelation.setSectionId(projectBidDocREQ.getSectionIdArray()[i]);
            docRelation.setSaleEndTimeType(projectBidDocREQ.getSaleEndTimeType());
            //招标文件发售结束时间类型为 确定时间
            if (TimeTypeEnum.CONFIRM.getValue().equals(projectBidDocREQ.getSaleEndTimeType())) {
                docRelation.setSaleEndTime(projectBidDocREQ.getSaleEndTimeArray()[i]);
            } else if (TimeTypeEnum.PREVIOUS_DAY.getValue().equals(projectBidDocREQ.getSaleEndTimeType())
                    && TimeTypeEnum.CONFIRM.getValue().equals(projectBidDocREQ.getSubmitEndTimeType())) {
                //招标文件发售结束时间类型为 投标文件递交截止时间前一天，且投标文件递交截止时间类型为 确定时间
                //docRelation.setSaleEndTime(getPreviousDay(projectBidDocREQ.getSubmitEndTimeArray()[i]));
                docRelation.setSaleEndTime(projectBidDocREQ.getSaleEndTimeArray()[i]);
            }
            docRelation.setSubmitEndTimeType(projectBidDocREQ.getSubmitEndTimeType());
            //投标文件递交截止时间类型为 确定时间
            if (TimeTypeEnum.CONFIRM.getValue().equals(projectBidDocREQ.getSubmitEndTimeType())) {
                docRelation.setSubmitEndTime(projectBidDocREQ.getSubmitEndTimeArray()[i]);
            }
            list.add(docRelation);
        }
        return projectBidDocRelationService.saveBatch(list);
    }

    /**
     * 招标附件oss表ID集合
     *
     * @param dataInfoREQ
     * @return
     */
    private Map<Long, String> getFileIdMap(DocDataInfoREQ dataInfoREQ) {
        Map<Long, String> maps = new HashMap<>();
        if (null != dataInfoREQ.getAttachmentInfo() &&
                !CollectionUtil.isEmpty(dataInfoREQ.getAttachmentInfo().getRelevancyAnnex())) {
            List<AttachmentRelation> relevancyAnnex = dataInfoREQ.getAttachmentInfo().getRelevancyAnnex();
            for (AttachmentRelation relation : relevancyAnnex) {
                if (null == maps.get(relation.getSectionId())) {
                    maps.put(relation.getSectionId(), relation.getFileId().toString());
                } else {
                    maps.put(relation.getSectionId(), maps.get(relation.getSectionId()) + "," + relation.getFileId());
                }
            }
        }
        return maps;
    }

    /**
     * 招标开标一览表oss表ID集合
     * @param dataInfoREQ
     * @return
     */
    private Map<Long, String> getScheduleFileIdMap(DocDataInfoREQ dataInfoREQ) {
        Map<Long, String> maps = new HashMap<>();
        if (null != dataInfoREQ.getScheduleREQ() && !CollectionUtil.isEmpty(dataInfoREQ.getScheduleREQ().getScheduleList())) {
            List<ProjectSectionScheduleReq> list = dataInfoREQ.getScheduleREQ().getScheduleList();
            for (ProjectSectionScheduleReq scheduleReq : list) {
                if(null != scheduleReq.getFileOssId()){
                    if (null == maps.get(scheduleReq.getSectionId())) {
                        maps.put(scheduleReq.getSectionId(), scheduleReq.getFileOssId().toString());
                    } else {
                        maps.put(scheduleReq.getSectionId(), maps.get(scheduleReq.getSectionId()) + "," + scheduleReq.getFileOssId());
                    }
                }

            }
        }
        return maps;
    }

    /**
     * 招标文件表更操作记录
     *
     * @param projectBidDocREQ
     * @return
     */
    private Boolean saveDocChangeRecord(ProjectBidDocREQ projectBidDocREQ, Long docId, Map<Long, String> map, Map<Long, String> scheduleMaps) {
        List<DocChangeRecord> list = new ArrayList<>();
        //true: 标书费按按 标段/包 收取
        boolean tenderFlag = TenderFeeTypeEnum.SECTION.getValue().equals(projectBidDocREQ.getTenderFeeType());
        //true: 保证金按 比例 收取
        //查询文件最大操作次数
        LambdaQueryWrapper<DocChangeRecord> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(DocChangeRecord::getDocId, docId);
        lambdaQueryWrapper.orderByDesc(DocChangeRecord::getSubmitNumber);
        List<DocChangeRecord> recordList = docChangeRecordService.list(lambdaQueryWrapper);
        for (int i = 0; i < projectBidDocREQ.getSectionIdArray().length; i++) {
            DocChangeRecord changeRecord = new DocChangeRecord();
            changeRecord.setDocId(docId);
            changeRecord.setSubmitTime(DateTime.now());
            //文件变更次数
            changeRecord.setSubmitNumber(recordList.size() == 0 ? 1 : recordList.get(0).getSubmitNumber() + 1);
            //查询标段最大操作次数
            LambdaQueryWrapper<DocChangeRecord> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(DocChangeRecord::getSectionId, projectBidDocREQ.getSectionIdArray()[i]);
            queryWrapper.orderByDesc(DocChangeRecord::getBidSubmitNumber);
            List<DocChangeRecord> bidRecord = docChangeRecordService.list(queryWrapper);
            //标段变更次数
            changeRecord.setBidSubmitNumber(bidRecord.size() == 0 ? 1 : bidRecord.get(0).getBidSubmitNumber() + 1);
            changeRecord.setSectionId(projectBidDocREQ.getSectionIdArray()[i]);
            changeRecord.setChangeType(projectBidDocREQ.getChangeType());
            changeRecord.setAnnexOssFileIds(map.get(projectBidDocREQ.getSectionIdArray()[i]));
            // 开标一览表oss集合
            changeRecord.setScheduleOssFileIds(scheduleMaps.get(projectBidDocREQ.getSectionIdArray()[i]));
            changeRecord.setSaleStartTime(projectBidDocREQ.getSaleStartTime());
            changeRecord.setSaleEndTimeType(projectBidDocREQ.getSaleEndTimeType());
            //招标文件发售结束时间类型为 确定时间
            if (TimeTypeEnum.CONFIRM.getValue().equals(projectBidDocREQ.getSaleEndTimeType())) {
                changeRecord.setSaleEndTime(projectBidDocREQ.getSaleEndTimeArray()[i]);
            } else if (TimeTypeEnum.PREVIOUS_DAY.getValue().equals(projectBidDocREQ.getSaleEndTimeType())
                    && TimeTypeEnum.CONFIRM.getValue().equals(projectBidDocREQ.getSubmitEndTimeType())) {
                //招标文件发售结束时间类型为 投标文件递交截止时间前一天，且投标文件递交截止时间类型为 确定时间
                //changeRecord.setSaleEndTime(getPreviousDay(projectBidDocREQ.getSubmitEndTimeArray()[i]));
                changeRecord.setSaleEndTime(projectBidDocREQ.getSaleEndTimeArray()[i]);
            }
            changeRecord.setSubmitEndTimeType(projectBidDocREQ.getSubmitEndTimeType());
            //投标文件递交截止时间类型为 确定时间
            if (TimeTypeEnum.CONFIRM.getValue().equals(projectBidDocREQ.getSubmitEndTimeType())) {
                changeRecord.setSubmitEndTime(projectBidDocREQ.getSubmitEndTimeArray()[i]);
            }
            changeRecord.setReleaseFileType(projectBidDocREQ.getReleaseFileType());
            changeRecord.setReviewFileType(projectBidDocREQ.getReviewFileType());
            changeRecord.setMaterialList(projectBidDocREQ.getMaterialList());
            if (TenderFeeFlagEnum.FREE.getValue().equals(projectBidDocREQ.getTenderFeeFlag())) {
                changeRecord.setTenderFeeType(TenderFeeTypeEnum.FREE.getValue());
            } else {
                changeRecord.setTenderFeeType(projectBidDocREQ.getTenderFeeType());
            }
            if (tenderFlag) {
                changeRecord.setTenderFee(projectBidDocREQ.getTenderFeeArray()[i]);
            } else {
                changeRecord.setTenderFee(projectBidDocREQ.getTenderFee());
            }
            //按套收取标书费
            if (TenderFeeTypeEnum.GROUP.getValue().equals(projectBidDocREQ.getTenderFeeType())) {
                changeRecord.setGroupInfo(projectBidDocREQ.getSectionGroup().getGroupInfo());
            }
            changeRecord.setBondType(projectBidDocREQ.getBondType());
            if (BondTypeEnum.PROPORTION.getValue().equals(projectBidDocREQ.getBondType())) {
                changeRecord.setBond(projectBidDocREQ.getBondArray()[i]);
                changeRecord.setBondPercent(projectBidDocREQ.getBondPercentArray()[i]);
            } else if (BondTypeEnum.QUOTA.getValue().equals(projectBidDocREQ.getBondType())) {
                changeRecord.setBond(projectBidDocREQ.getBondArray()[i]);
            } else {
                changeRecord.setBond(projectBidDocREQ.getBond());
            }
            changeRecord.setFeeType(projectBidDocREQ.getFeeType());
            changeRecord.setPaymentType(projectBidDocREQ.getPaymentType());
            changeRecord.setContact(projectBidDocREQ.getContact());
            changeRecord.setPhone(projectBidDocREQ.getPhone());
            changeRecord.setOssFileId(projectBidDocREQ.getOssFileId());
            changeRecord.setFilePriceState(projectBidDocREQ.getFilePriceState());
            changeRecord.setRelevancePackageNumber(projectBidDocREQ.getPackageNumber());
            changeRecord.setTenderFeeFlag(projectBidDocREQ.getTenderFeeFlag());
            changeRecord.setFree(projectBidDocREQ.getFree());
            changeRecord.setOtherPriceDescribe(projectBidDocREQ.getOtherPriceDescribe());

            //是否支持澄清
            changeRecord.setClarifyOnLine(projectBidDocREQ.getClarifyOnLine());
            if(projectBidDocREQ.getClarifyOnLine().equals(TimeTypeEnum.SUPPORT.getValue())){
                //澄清提交截止时间类型
                changeRecord.setClarifyEndTimeType(projectBidDocREQ.getClarifyEndTimeType());
                if(projectBidDocREQ.getClarifyEndTimeType().equals(TimeTypeEnum.C_PREVIOUS_DAY.getValue())
                        && projectBidDocREQ.getSubmitEndTimeType().equals(TimeTypeEnum.CONFIRM.getValue()) ){
                    changeRecord.setClarifyEndTime(getPreviousDay(projectBidDocREQ.getSubmitEndTimeArray()[i]));
                } else if(projectBidDocREQ.getClarifyEndTimeType().equals(TimeTypeEnum.C_CONFIRM.getValue())) {
                    changeRecord.setClarifyEndTime(projectBidDocREQ.getClarifyEndTimeArray()[i]);
                }
            }
            //是否支持异议
            changeRecord.setQuestionOnLine(projectBidDocREQ.getQuestionOnLine());
            if(projectBidDocREQ.getQuestionOnLine().equals(TimeTypeEnum.SUPPORT.getValue())){
                //异议提交截止时间类型
                changeRecord.setQuestionEndTimeType(projectBidDocREQ.getQuestionEndTimeType());
                if(projectBidDocREQ.getQuestionEndTimeType().equals(TimeTypeEnum.C_PREVIOUS_DAY.getValue())
                && projectBidDocREQ.getSubmitEndTimeType().equals(TimeTypeEnum.CONFIRM.getValue())){
                    changeRecord.setQuestionEndTime(getPreviousDay(projectBidDocREQ.getSubmitEndTimeArray()[i]));
                } else if(projectBidDocREQ.getQuestionEndTimeType().equals(TimeTypeEnum.C_CONFIRM.getValue())) {
                    changeRecord.setQuestionEndTime(projectBidDocREQ.getQuestionEndTimeArray()[i]);
                }
            }

            // 是否支持线上开标
            changeRecord.setBidOpenOnline(projectBidDocREQ.getOnlineBidOpen());
            if (CommonConstants.YES.equals(projectBidDocREQ.getOnlineBidOpen())) {
                changeRecord.setDecryptTime(projectBidDocREQ.getDecryptionTimeArray()[i]);
                changeRecord.setSupNumEndBid(projectBidDocREQ.getConditionBidOpenArray()[i]);
            }

            list.add(changeRecord);
        }
        return docChangeRecordService.saveBatch(list);
    }

    /**
     * 根据ID集合查询代办列表
     *
     * @param ids
     * @return
     */
    @Override
    public Map<Long, ProjectTaskVO> queryAppingTaskMapByIds(List<Long> ids) {
        List<AppingTaskDocDTO> appingTaskDocDTOS = this.baseMapper.queryAppingTaskListByDocids(ids);
        Map<Long, ProjectTaskVO> appingTaskVOMap = new HashMap<>();
        appingTaskDocDTOS.forEach(i -> {
            ProjectTaskVO appingTaskVO = new ProjectTaskVO();
            BeanUtils.copyProperties(i, appingTaskVO);
            appingTaskVOMap.put(i.getDocId(), appingTaskVO);
        });
        return appingTaskVOMap;
    }

    @Override
    public List<ProjectBidDoc> queryDocInfoForTask() {
        return this.baseMapper.queryDocInfoForTask();
    }

    @Override
    public List<ProjectBidDocDTO> queryRelevantInfo(Long docId) {
        List<ProjectBidDocDTO> projectBidDocDTOS = this.baseMapper.queryRelevantInfo(docId);
        for (ProjectBidDocDTO projectBidDocDTO : projectBidDocDTOS) {
            List<SectionExpertSpecialty> list = iSectionExpertSpecialtyService.list(new LambdaQueryWrapper<SectionExpertSpecialty>()
                    .eq(SectionExpertSpecialty::getSectionId, projectBidDocDTO.getSectionId()));
            List<Long> spids = list.stream()
                    .map(m -> {
                        return m.getExpertSpecialtyId();
                    }).collect(Collectors.toList());
            projectBidDocDTO.setSectionExpertSpecialtyList(spids);
        }
        return projectBidDocDTOS;
    }

    @Override
    public Boolean modifyWithdrawProgress(NoticeProcessREQ info, JwtUser jwtUser) {
        ProjectBidDoc projectBidDoc = this.getById(info.getDocId());
        if (null == projectBidDoc) {
            return false;
        } else {
            //该文件关联的标段集合
            LambdaQueryWrapper<ProjectBidDocRelation> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(ProjectBidDocRelation::getDocId, projectBidDoc.getId());
            List<ProjectBidDocRelation> docRelationList = projectBidDocRelationService.list(queryWrapper);
            //待修改标段集合
            List<ProjectBidSection> section = new ArrayList<>();
            boolean flag = false;
            String userCode = jwtUser.getUserOtherId();
            //请求工作流返回结果
            String result = "";
            //返回结果实体类
            ReturnVo returnVo = null;
            if (WithdrawProgressEnum.REJECT.getValue().equals(info.getWithdrawProgress())) {
                //退回撤回
                String appContent = "";
                //更新code
                docRelationList.stream().forEach((e) -> {
                    ProjectBidSection projectBidSection = new ProjectBidSection();
                    projectBidSection.setId(e.getSectionId());
                    if (NoticeProgressEnum.REVIEW.getValue().equals(projectBidDoc.getOldProgress())) {
                        projectBidSection.setBidSectionCode(BidSectionCodeEnum.bidding_file_confirm.getCode());
                    } else {
                        projectBidSection.setBidSectionCode(BidSectionCodeEnum.bidding_file_submint.getCode());
                    }
                    section.add(projectBidSection);
                });
                //保存审批流程记录
                //返回撤回前招标文件进度
                projectBidDoc.setNoticeProgress(projectBidDoc.getOldProgress());
                result = flowApiClient.review(userCode, info.getProcessInstanceId(), info.getTaskId(), info.getMessage(), false);
                returnVo = JSONObject.parseObject(result, ReturnVo.class);
                if (null == returnVo || !ReturnCodeConstants.PROCESS_SUCCESS_CODE.equals(returnVo.getCode())) {
                    throw new SunFlowerException(ExceptionEnum.BID_DOC_AUTH_ERROR, returnVo.getMsg());
                }else{
                }
                flag = saveApprovalOpinion(ApprovalTypeEnum.DRAWBACK_WITHDRAW.getValue(), jwtUser, info.getProjectId(), info.getDocId(), info.getMessage(),appContent);

                // 退回时添加待处理事项表
                LambdaQueryWrapper<ProjectBidDocRelation> projectBidDocRelationLambdaQueryWrapper =  new LambdaQueryWrapper<>();
                projectBidDocRelationLambdaQueryWrapper.eq(ProjectBidDocRelation::getProjectId,info.getProjectId());
                projectBidDocRelationLambdaQueryWrapper.eq(ProjectBidDocRelation::getDocId,info.getDocId());
                List<ProjectBidDocRelation> projectBidDocRelationList = projectBidDocRelationService.list(projectBidDocRelationLambdaQueryWrapper);
                StringBuilder sectionIds = new StringBuilder();
                List<Long> sectionList = new ArrayList<>();
                for (ProjectBidDocRelation projectBidDocRelation : projectBidDocRelationList) {
                    sectionList.add(projectBidDocRelation.getSectionId());
                    sectionIds.append(projectBidDocRelation.getSectionId()).append(",");
                }
                sectionIds = new StringBuilder(sectionIds.substring(0, sectionIds.length() - 1));
                PendingItem pendingItem = new PendingItem();
                pendingItem.setOperationTime(new Date());
                pendingItem.setBusinessCode(ReturnListEnum.TENDER_DOC.getCode());
                pendingItem.setBusinessId(info.getDocId());
                pendingItem.setBusinessType(2);
                pendingItem.setProjectId(info.getProjectId());
                pendingItem.setSectionId(sectionIds.toString());
                //查询标段轮次
                LambdaQueryWrapper<ProjectBidSection> projectBidSectionLambdaQueryWrapper = new LambdaQueryWrapper<>();
                projectBidSectionLambdaQueryWrapper.in(ProjectBidSection::getId,sectionList);
                List<ProjectBidSection> projectBidSections = projectBidSectionMapper.selectList(projectBidSectionLambdaQueryWrapper);
                pendingItem.setBidRound(projectBidSections.get(0).getBidRound());

                //查询申请人和申请时间
                ProjectBidDoc bidDoc = projectBidDocMapper.selectById(info.getDocId());
                LambdaQueryWrapper<ApprovalOpinion> approvalOpinionLambdaQueryWrapper = new LambdaQueryWrapper<>();
                approvalOpinionLambdaQueryWrapper.eq(ApprovalOpinion::getApprovalUserId,bidDoc.getCreatedUserId());
                approvalOpinionLambdaQueryWrapper.eq(ApprovalOpinion::getDocId,bidDoc.getId());
                approvalOpinionLambdaQueryWrapper.orderByDesc(ApprovalOpinion::getCreatedTime);
                approvalOpinionLambdaQueryWrapper.last("limit 1");
                ApprovalOpinion approvalOpinion = approvalOpinionMapper.selectOne(approvalOpinionLambdaQueryWrapper);
                pendingItem.setApplyTime(approvalOpinion.getApprovalTime());
                pendingItem.setApplyUserId(approvalOpinion.getApprovalUserId());

                pendingItemService.save(pendingItem);
                // 发送app待阅
                commonMqService.sendOaReadMsg(ReturnListEnum.TENDER_DOC.getDesc(), pendingItem);
            } else {                //同意撤回
                String appContent = "";
                //更新code
                docRelationList.stream().forEach((e) -> {
                    ProjectBidSection projectBidSection = new ProjectBidSection();
                    projectBidSection.setId(e.getSectionId());
                    ProjectBidSection bidSection = projectBidSectionService.getById(e.getSectionId());
                    if (!PackageStatusEnum.NOTICE_ISSUED.getValue().toString().equals(bidSection.getStatus())) {
                        if (projectBidNoticeService.judgeSectionStatusById(e.getSectionId())) {
                            projectBidSection.setStatus(PackageStatusEnum.EDIT_BY_NOTICE.getValue().toString());
                        } else {
                            projectBidSection.setStatus(PackageStatusEnum.READONLY_BY_NOTICE.getValue().toString());
                        }
                    }
                    projectBidSection.setBidSectionCode(BidSectionCodeEnum.bidding_file_withdraw.getCode());
                    section.add(projectBidSection);
                });
                //保存审批流程记录
                flag = saveApprovalOpinion(ApprovalTypeEnum.AGREE_WITHDRAW.getValue(), jwtUser, info.getProjectId(), info.getDocId(), info.getMessage(),"");
                if (flag) {
                    //修改状态为 已撤回
                    projectBidDoc.setNoticeProgress(NoticeProgressEnum.WITHDRAWAL.getValue());
                    //文件流程
                    result = flowApiClient.revokeProcessBySystem(TableNameConstants.PROJECT_BID_DOC + projectBidDoc.getId(), info.getMessage());
                    returnVo = JSONObject.parseObject(result, ReturnVo.class);
                    if (null == returnVo || !ReturnCodeConstants.PROCESS_SUCCESS_CODE.equals(returnVo.getCode())) {
                        throw new SunFlowerException(ExceptionEnum.BID_DOC_AUTH_ERROR, returnVo.getMsg());
                    }
                    //撤回流程
                    result = flowApiClient.review(userCode, info.getProcessInstanceId(), info.getTaskId(), info.getMessage(), true);
                    returnVo = JSONObject.parseObject(result, ReturnVo.class);
                    if (null == returnVo || !ReturnCodeConstants.PROCESS_SUCCESS_CODE.equals(returnVo.getCode())) {
                        throw new SunFlowerException(ExceptionEnum.BID_DOC_AUTH_ERROR, returnVo.getMsg());
                    }
                }

            }
            if (flag) {
                //更新标段状态
                flag = commonSectionService.updateSectionStatus(section);
            }
            if (flag) {
                flag = this.updateById(projectBidDoc);
            }
            if (!flag) {
                throw new SunFlowerException(ExceptionEnum.BID_DOC_AUTH_ERROR, returnVo.getMsg());
            }
            return flag;
        }
    }

    @Override
    public List<ReBidInfoDTO> queryRebidInfo(Long formerSectionId) {
        List<ReBidInfoDTO> dtoList = this.baseMapper.queryRebidInfo(formerSectionId);
        dtoList.stream().forEach((e) -> {
            e.setFormerSectionId(formerSectionId);
            //根据原标段ID查询重新招标后标段
            LambdaQueryWrapper<ProjectBidSection> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(ProjectBidSection::getFormerSectionId, formerSectionId);
            ProjectBidSection projectBidSection = projectBidSectionService.getOne(queryWrapper);
            e.setOldBidderType(projectBidSection.getOldBidderType());
        });
        return dtoList;
    }

    @Override
    public Long queryDocId(DocBidRoundREQ docBidRoundREQ) {
        return this.baseMapper.queryDocId(docBidRoundREQ);
    }

    @Override
    public Boolean updateAllProgress(List<NoticeProcessREQ> list, JwtUser jwtUser) {
        boolean flag = true;
        for (NoticeProcessREQ noticeProcessREQ : list) {
            flag = updateProgress(noticeProcessREQ, jwtUser);
            if (!flag) {
                return false;
            }
        }
        return true;
    }

    @Override
    public Boolean judgeStatusByDoc(Long sectionId) {
        ProjectBidSection projectBidSection = projectBidSectionService.getById(sectionId);
        DocBidRoundREQ docBidRoundREQ = new DocBidRoundREQ();
        docBidRoundREQ.setBidId(sectionId);
        docBidRoundREQ.setBidRound(projectBidSection.getBidRound());
        //根据标段ID和招标阶段查询文件ID
        Long docId = this.baseMapper.queryDocId(docBidRoundREQ);
        if (docId == null) {
            return true;
        } else {
            ProjectBidDoc doc = this.baseMapper.selectById(docId);
            if (null == doc) {
                return true;
            }
            //根据招标文件状态判断时候可修改
            if (NoticeProgressEnum.UNDERREVIEW.getValue().equals(doc.getNoticeProgress())
                    || NoticeProgressEnum.REVIEW.getValue().equals(doc.getNoticeProgress())
                    || NoticeProgressEnum.RELEASE.getValue().equals(doc.getNoticeProgress())
                    || NoticeProgressEnum.WITHDRAWUNDERREVIEW.getValue().equals(doc.getNoticeProgress())) {
                return false;
            } else {
                return true;
            }
        }
    }

    @Override
    public DocDetailVO getDocDetailInfo(Long docId,Integer BidRound) {
        LambdaQueryWrapper<DocChangeRecord> recordQuery = new LambdaQueryWrapper<>();
        recordQuery.eq(DocChangeRecord::getDocId, docId);
        recordQuery.orderByDesc(DocChangeRecord::getSubmitNumber);
        List<DocChangeRecord> list = docChangeRecordService.list(recordQuery);
        List<DocSectionDetailDTO> sectionInfo = this.baseMapper.queryDocSectionInfo(docId,BidRound);
        List<DocDetailDTO> docList = this.baseMapper.queryDocDetailInfo(docId);
        DocDetailDTO docDetail = docList.get(0);
        //最新关联附件信息
        List<RelevancyAnnexDTO> dtoList = relevancyAnnexService.getInfoByDocId(docId);
        List<Object> ids = new ArrayList<>();
        dtoList.stream().forEach((e) -> {
            ids.add(e.getFileId());
        });
        docDetail.setAnnexOssFileIds(StrUtil.join(",", ids.stream().distinct().toArray()));
        // 最新关联开标一览表文件信息
        List<ProjectSectionScheduleDto> scheduleList = projectSectionScheduleService.getInfoByDocId(docId, BidRound);
        List<Object> scheduleIds = new ArrayList<>();
        scheduleList.stream().forEach((e) -> {
            scheduleIds.add(e.getFileOssId());
        });
        docDetail.setScheduleOssFileIds(StrUtil.join(",", scheduleIds.stream().distinct().toArray()));
        //招标文件开标一览表配置
        if (null != sectionInfo && sectionInfo.size() > 0) {
            docDetail.setBidOpeningFileId(sectionInfo.get(0).getBidOpeningFileId());
            docDetail.setGenerationType(sectionInfo.get(0).getGenerationType());
        }

        Integer submitNumber = list.get(0).getSubmitNumber();
        if (list.get(0).getSubmitNumber() > 1) {
            //关联包信息历史记录查询
            LambdaQueryWrapper<DocChangeRecord> query = new LambdaQueryWrapper<>();
            query.eq(DocChangeRecord::getDocId, docId);
            query.eq(DocChangeRecord::getSubmitNumber, submitNumber - 1);
            query.orderByDesc(DocChangeRecord::getSubmitNumber);
            List<DocChangeRecord> records = docChangeRecordService.list(query);
            //历史记录中关联附件信息
            List<Long> relevancyFile = new ArrayList<>();
            records.stream().forEach((e) -> {
                if (StrUtil.isNotEmpty(e.getAnnexOssFileIds())) {
                    String[] split = e.getAnnexOssFileIds().split(",");
                    for (int i = 0; i < split.length; i++) {
                        relevancyFile.add(Long.parseLong(split[i]));
                    }
                }
            });
            Object[] objects = relevancyFile.stream().distinct().toArray();
            String fileIdStr = StrUtil.join(",", objects);
            // 历史记录中开标一览表文件信息
            List<Long> scheduleFile = new ArrayList<>();
            records.stream().forEach((e) -> {
                if (StrUtil.isNotEmpty(e.getScheduleOssFileIds())) {
                    String[] split = e.getScheduleOssFileIds().split(",");
                    for (int i = 0; i < split.length; i++) {
                        scheduleFile.add(Long.parseLong(split[i]));
                    }
                }
            });
            String scheduleFileIdStr = StrUtil.join(",", scheduleFile.stream().distinct().toArray());
            //文件基本信息对比
            DocChangeRecord docChangeRecord = records.get(0);
            DocDetailDTO oldDetail = new DocDetailDTO();
            oldDetail.setDocId(docChangeRecord.getDocId());
            oldDetail.setReleaseFileType(docChangeRecord.getReleaseFileType());
            oldDetail.setReviewFileType(docChangeRecord.getReviewFileType());
            oldDetail.setOssFileId(docChangeRecord.getOssFileId());
            oldDetail.setFilePriceState(docChangeRecord.getFilePriceState());
            oldDetail.setOtherPriceDescribe(docChangeRecord.getOtherPriceDescribe());
            oldDetail.setFeeType(docChangeRecord.getFeeType());
            oldDetail.setPaymentType(docChangeRecord.getPaymentType());
            oldDetail.setTenderFeeFlag(docChangeRecord.getTenderFeeFlag());
            oldDetail.setMaterialList(docChangeRecord.getMaterialList());
            oldDetail.setFree(docChangeRecord.getFree());
            oldDetail.setAnnexOssFileIds(fileIdStr);
            oldDetail.setScheduleOssFileIds(scheduleFileIdStr);

            Map<Object, Boolean> baseInfoMap = CompareObjectUtil.compareObject(oldDetail, docDetail);
            docDetail.setFlag(baseInfoMap);
            List<DocSectionDetailDTO> oldSectionInfo = new ArrayList<>();
            for (int i = 0; i < records.size(); i++) {
                DocSectionDetailDTO section = new DocSectionDetailDTO();
                section.setSectionId(records.get(i).getSectionId());
                section.setBond(records.get(i).getBond());
                section.setBondType(records.get(i).getBondType());
                section.setBondPercent(records.get(i).getBondPercent());
                section.setTenderFee(records.get(i).getTenderFee());
                section.setTenderFeeType(records.get(i).getTenderFeeType());
                section.setSaleEndTimeType(records.get(i).getSaleEndTimeType());
                section.setSaleEndTime(records.get(i).getSaleEndTime());
                section.setSubmitEndTimeType(records.get(i).getSubmitEndTimeType());
                section.setSubmitEndTime(records.get(i).getSubmitEndTime());
                //澄清异议进行判断赋值
                /*if(null != records.get(i).getClarifyOnLine()){
                    oldDetail.setClarifyOnLine(records.get(i).getClarifyOnLine());
                }
                if(null != records.get(i).getClarifyEndTimeType()){
                    oldDetail.setClarifyEndTimeType(records.get(i).getClarifyEndTimeType());
                }
                if(null != records.get(i).getClarifyEndTime()){
                    oldDetail.setClarifyEndTime(records.get(i).getClarifyEndTime());
                }
                if(null != records.get(i).getQuestionOnLine()){
                    oldDetail.setQuestionOnLine(records.get(i).getQuestionOnLine());
                }
                if(null != records.get(i).getQuestionEndTimeType()){
                    oldDetail.setQuestionEndTimeType(records.get(i).getQuestionEndTimeType());
                }
                if(null != records.get(i).getQuestionEndTime()){
                    oldDetail.setQuestionEndTime(records.get(i).getQuestionEndTime());
                }*/
                if(null != records.get(i).getClarifyOnLine()){
                    section.setClarifyOnLine(records.get(i).getClarifyOnLine());
                }
                if(null != records.get(i).getClarifyEndTimeType()){
                    section.setClarifyEndTimeType(records.get(i).getClarifyEndTimeType());
                }
                if(null != records.get(i).getClarifyEndTime()){
                    section.setClarifyEndTime(records.get(i).getClarifyEndTime());
                }
                if(null != records.get(i).getQuestionOnLine()){
                    section.setQuestionOnLine(records.get(i).getQuestionOnLine());
                }
                if(null != records.get(i).getQuestionEndTimeType()){
                    section.setQuestionEndTimeType(records.get(i).getQuestionEndTimeType());
                }
                if(null != records.get(i).getQuestionEndTime()){
                    section.setQuestionEndTime(records.get(i).getQuestionEndTime());
                }
                // 线上开标
                if(null != records.get(i).getBidOpenOnline()){
                    section.setBidOpenOnline(records.get(i).getBidOpenOnline());
                }
                if(null != records.get(i).getDecryptTime()){
                    section.setDecryptTime(records.get(i).getDecryptTime());
                }
                if(null != records.get(i).getSupNumEndBid()){
                    section.setSupNumEndBid(records.get(i).getSupNumEndBid());
                }
                //if(null != records.get(i).getSystemAutomaticLabeling()){
                //    section.setSystemAutomaticLabeling(records.get(i).getSystemAutomaticLabelingArray());
                //}
                oldSectionInfo.add(section);
            }
            //关联包信息对比
            for (int i = 0; i < oldSectionInfo.size(); i++) {
                for (int j = 0; j < sectionInfo.size(); j++) {
                    if (oldSectionInfo.get(i).getSectionId().equals(sectionInfo.get(j).getSectionId())) {
                        Map<Object, Boolean> map = CompareObjectUtil.compareObject(oldSectionInfo.get(i), sectionInfo.get(j));
                        sectionInfo.get(j).setFlag(map);
                    }
                }
            }
        }
        DocDetailVO convert = BeanListUtil.convert(docDetail, DocDetailVO.class);
        convert.setSectionDetailList(sectionInfo);
        return convert;
    }

    @Override
    public List<DocSectionDetailDTO> queryDocSectionInfo(Long docId, Integer bidRound) {
        return this.baseMapper.queryDocSectionInfo(docId,bidRound);
    }

    @Override
    public List<ProjectBidDocDTO> findReleaseTimeBySections(List<Long> sectionIds, Integer bidRound) {
        return this.baseMapper.findReleaseTimeBySections(sectionIds,bidRound);
    }

    @Override
    public List<Long> queryDocIdByBidIds(DocBidRoundREQ docBidRoundREQ) {
        return this.baseMapper.queryDocIdByBidIds(docBidRoundREQ.getSectionIds(),docBidRoundREQ.getBidRound());
    }

//    public static Result<Long> analysisBidDocTest() throws IOException {
//        FileInputStream fis = new FileInputStream(new File("/111.doc"));
//        XWPFDocument document = new XWPFDocument(fis);
//        List<XWPFTable> tables = document.getTables();
//        List<List<Map<String, Object>>> tableParse = new ArrayList<>();
//        //第一步，解析列合并，填充文字，并且把行合并的格式记录下来
//        hMerger(tableParse,tables.get(0).getRows());
//        //第二步，解析行合并，填充文字
//        vMerger(tableParse);
//        //第三步，判断是横表还是竖表
//        Result<Boolean> result = analysisHead(tableParse);
//        if(!result.getFlag()){
//            return Result.failed(result.getMsg());
//        }
//        //第四步，生成模板，若是竖表的话需要转换
//        if(result.getData()){
//            tableParse = changeVertical2Normal(tableParse);
//        }
//        //第五步，转换成html
//        String html = excelToHtml(createTemplate(tableParse));
//        System.out.println(html);
//        //第六步，转换成word（可以省略，具体看实现）
//        return Result.ok();
//    }

    /**
     * 生成模板
     * @param tableParse
     * @return
     */
    private static List<List<Map<String, Object>>> createTemplate(List<List<Map<String, Object>>> tableParse){
        List<List<Map<String, Object>>> template = new ArrayList<>();
        int head = getHeadNum(tableParse);
        //表头
        for(int i=0;i<head;i++){
            List<Map<String,Object>> headList = new ArrayList<>(tableParse.get(i));
            //0-head-1为表头，表头加一列名称为"供应商"
            Map<String,Object> headMap = new HashMap<>();
            headMap.put("text","供应商");
            if(i==0){
                headMap.put("type",1);
                headMap.put("row",head);
                headMap.put("isBold",1);
            }else{
                headMap.put("type",0);
                headMap.put("isBold",0);
            }
            headList.add(0,headMap);
            template.add(headList);
        }
        //表体
        for(int i=1;i<4;i++){
            for(int j=head;j<tableParse.size();j++){
                List<Map<String, Object>> rowList = new ArrayList<>(tableParse.get(j));
                //0-head-1为表头，表头加一列名称为"供应商"
                Map<String,Object> rowMap = new HashMap<>();
                rowMap.put("text","供应商"+i);
                rowMap.put("isBold",0);
                if(j==head){
                    rowMap.put("row",tableParse.size()-head);
                    rowMap.put("type",1);
                }else{
                    rowMap.put("type",0);
                }
                rowList.add(0,rowMap);
                template.add(rowList);
            }
        }
        return template;
    }

    private static int getHeadNum(List<List<Map<String, Object>>> tableParse){
        int headNum = 0;
        for(int i=0;i<tableParse.size();i++){
            if(1==MapUtils.getInteger(tableParse.get(i).get(tableParse.get(i).size()-1),"isBold")){
                headNum++;
            }else{
                break;
            }
        }
        return headNum;
    }

    /**
     * 竖表转横表
     * @param tableParse
     * @return
     */
    private static List<List<Map<String, Object>>> changeVertical2Normal(List<List<Map<String, Object>>> tableParse){
        List<List<Map<String, Object>>> tableParseV = new ArrayList<>();
        for(int i=0;i<tableParse.get(0).size();i++){
            List<Map<String, Object>> list = new ArrayList<>();
            for(int j=0;j<tableParse.size();j++){
                Map<String,Object> map = tableParse.get(j).get(i);
                Integer row = MapUtils.getInteger(map,"row");
                Integer col = MapUtils.getInteger(map,"col");
                //col和row进行调换
                if(null!=row&&null!=col){
                    map.put("col",row);
                    map.put("row",col);
                }else if(null!=row){
                    map.put("col",row);
                    map.remove("row");
                }else if(null!=col){
                    map.put("row",col);
                    map.remove("col");
                }
                list.add(map);
            }
            tableParseV.add(list);
        }
        return tableParseV;
    }

    /**
     * 解析表头情况
     * @param tableParse
     * @return
     */
    private static Result<Boolean> analysisHead(List<List<Map<String, Object>>> tableParse){
        //最后一行第一个单元格文字加粗情况
        Integer lastRow = MapUtils.getInteger(tableParse.get(tableParse.size()-1).get(0),"isBold");
        //最后一列第一个单元格文字加粗情况
        Integer lastCol = MapUtils.getInteger(tableParse.get(0).get(tableParse.get(0).size()-1),"isBold");
        boolean isVertical;
        if(1==lastRow&&1!=lastCol){
            isVertical = true;
        }else if(1!=lastRow&&1==lastCol){
            isVertical = false;
        }else{
            return Result.failed("表头解析失败，请把表头标记为加粗");
        }
        return Result.ok(isVertical);
    }

    /**
     * 拼装成html字符串
     * @param tableParse
     * @return
     */

    private static String excelToHtml(List<List<Map<String, Object>>> tableParse){
        StringBuilder sb = new StringBuilder("<table border='1px' border-color='grey' style='width:100%;border-collapse:collapse'>");
        for(int r = 0;r<tableParse.size();r++){
            sb.append("<tr>");
            List<Map<String,Object>> cells = tableParse.get(r);
            for(int c = 0; c< cells.size();c++){
                tdMaker(sb,cells.get(c));
            }
            sb.append("</tr>");
        }
        sb.append("</table>");
        return sb.toString();
    }

    /**
     * 生成td字符串
     * @param sb
     * @param cell
     */
    private static void tdMaker(StringBuilder sb, Map<String, Object> cell){
        if((int)cell.get("type")==1){
            if(1 == MapUtils.getInteger(cell,"isBold")){
                sb.append("<td style='font-weight: bold'");
            }else{
                sb.append("<td");
            }
            if(cell.get("col")!=null){
                sb.append(" colspan='"+cell.get("col")+"'");
            }
            if(cell.get("row")!=null){
                sb.append(" rowspan='"+cell.get("row")+"'");
            }
            sb.append(">");
            sb.append(cell.get("text"));
            sb.append("</td>");
        }
    }

    /**
     * 如果有单元格横向的合并。会释放td位置
     * 比如一行五列，1-2合并。poi只能解析到1，3，4，5四个cell
     * 为了方便纵向单元格合并的解析。补上被合并的td对象
     *
     * @param tableParse
     * @param rows
     */

    private  static void  hMerger(List<List<Map<String, Object>>> tableParse,List<XWPFTableRow> rows){
        for (int r = 0; r < rows.size(); r++) {
            List<Map<String,Object>> list = new ArrayList<>();
            XWPFTableRow row = rows.get(r);
            List<XWPFTableCell> tableCells = row.getTableCells();
            for (int c = 0; c < tableCells.size(); c++) {
                Map<String, Object> cellMap = new HashMap<>();
                list.add(cellMap);
                XWPFTableCell cell = tableCells.get(c);
                cellMap.put("text", cell.getText());
                cellMap.put("type",1);
                CTTcPr tcPr = cell.getCTTc().getTcPr();
                Map<String,Object> styleMap = getStyle(tcPr,cell);
                cellMap.putAll(styleMap);
                //判断是否跨列
                if (tcPr.getGridSpan() != null) {
                    cellMap.put("col", tcPr.getGridSpan().getVal());
                    for(int g = 0;g<tcPr.getGridSpan().getVal().intValue()-1;g++){
                        Map<String,Object> visCell = new HashMap<>();
                        visCell.put("type",0);
                        visCell.put("text",cell.getText());
                        visCell.putAll(styleMap);
                        list.add(visCell);
                    }
                }
            }
            tableParse.add(list);
        }
    }

    /**
     * 合并情况，vMergeVal=1为开始合并行，vMerge=1，vMergeVal=0为被合并，vMerge=0为非合并
     * 加粗情况
     * @param tcPr
     * @param cell
     */
    private static Map<String,Object> getStyle(CTTcPr tcPr,XWPFTableCell cell){
        Map<String,Object> styleMap = new HashMap<>();
        if(tcPr.getVMerge()!=null){
            if(tcPr.getVMerge().getVal()!=null&&tcPr.getVMerge().getVal() == STMerge.RESTART){
                styleMap.put("vMergeVal",1);
            }else{
                styleMap.put("vMergeVal",0);
            }
            styleMap.put("vMerge",1);
        }else{
            styleMap.put("vMerge",0);
        }
        styleMap.put("isBold",0);
        for(int i=0;i<cell.getParagraphs().size();i++){
            XWPFParagraph paragraph = cell.getParagraphArray(i);
            if(StringUtils.isNotBlank(paragraph.getText())&&paragraph.getRuns().get(0).isBold()){
                styleMap.put("isBold",1);
                break;
            }
        }
        return styleMap;
    }

    /**
     * 纵向单元格合并的解析
     * 纵向单元格合并的解析比较简单。
     * 表明是前面行的合并单元格
     *
     * @param tableParse
     */
    private static void vMerger(List<List<Map<String, Object>>> tableParse){
        for(int i=0;i<tableParse.size();i++){
            List<Map<String, Object>> rows = tableParse.get(i);
            for(int j=0;j<rows.size();j++){
                Map<String,Object> cell = rows.get(j);
                if(1 == MapUtils.getIntValue(cell,"vMerge")&&1 == MapUtils.getIntValue(cell,"vMergeVal")){
                    int d=1;
                    for(int k=i+1;k<tableParse.size();k++){
                        Map<String,Object> mergeMap = tableParse.get(k).get(j);
                        if(1 == MapUtils.getIntValue(mergeMap,"vMerge")&&0 == MapUtils.getIntValue(mergeMap,"vMergeVal")){
                            tableParse.get(k).get(j).put("text",MapUtils.getString(cell,"text"));
                            tableParse.get(k).get(j).put("isBold",MapUtils.getString(cell,"isBold"));
                            tableParse.get(k).get(j).put("type",0);
                            d++;
                        }else{
                            break;
                        }
                    }
                    cell.put("row",d);
                }
            }
        }
    }

//    public static void main(String[] args) throws IOException {
//        analysisBidDocTest();
//    }


    /*    --------------测试用---------------*/


    /**
     * 解析开标一览表
     * @return
     * @throws IOException
     */
//    public Result<Long> analysisBidDoc(Long ossFileId) throws IOException {
//        XWPFTable table;
//        Result<XWPFTable> resultTable =  getBidOpenTable(ossFileId);
//        if(resultTable.getFlag()){
//            table = resultTable.getData();
//        }else{
//            return Result.failed(resultTable.getMsg());
//        }
//        int headFlag = headFlag(table);
//        if(TableHeadTypeConstants.OTHER == headFlag(table)){
//            //解析表头加粗情况失败
//            return Result.failed("解析横竖表头失败");
//        }
//        //解析表头在哪一行
//        int headNum = getHeadNum(table.getRows(),headFlag);
//        if(0==headNum){
//            return Result.failed("解析表头开始行失败");
//        }
//        //解析后的文档
//        XWPFDocument doc = new XWPFDocument();
//        XWPFTable dealTable = doc.createTable();
//        Result<String> result;
//        if(TableHeadTypeConstants.VERTICAL == headFlag){
//            if(headNum > 1){
//                return Result.failed("竖表暂不支持多列表头");
//            }
//            result = dealVertical(headNum,table,dealTable);
//        }else{
//            result = dealNormal(headNum,table,dealTable);
//        }
//        if(!result.getFlag()){
//            return Result.failed(result.getMsg());
//        }
//        //上传到oss，返回ossId
//        String fileName = "开标一览表.docx";
//        String path = tempFilePath + fileName;
//        File file = new File(path);
//        if (!file.exists()) {
//            file.createNewFile();
//        }
//        FileOutputStream out = new FileOutputStream(file);
//        //写入文件
//        doc.write(out);
//        Long fileId = saveFile(path,fileName);
//        return Result.ok(fileId);
//    }

    /**
     * 解析开标一览表
     * @return
     * @throws IOException
     */
    @Override
    public Result<Long> analysisBidDoc(Long ossFileId) throws Exception {
        XWPFTable table;
        Result<XWPFTable> resultTable =  getBidOpenTable(ossFileId);
        if(resultTable.getFlag()){
            table = resultTable.getData();
        }else{
            return Result.failed(resultTable.getMsg());
        }
        List<List<Map<String, Object>>> tableParse = new ArrayList<>();
        //第一步，解析列合并，填充文字，并且把行合并的格式记录下来
        hMerger(tableParse,table.getRows());
        //第二步，解析行合并，填充文字
        vMerger(tableParse);
        //第三步，判断是横表还是竖表
        Result<Boolean> result = analysisHead(tableParse);
        if(!result.getFlag()){
            return Result.failed(result.getMsg());
        }
        //第四步，生成模板，若是竖表的话需要转换
        if(result.getData()){
            tableParse = changeVertical2Normal(tableParse);
        }
        //第五步，转换成html
        String html = excelToHtml(createTemplate(tableParse));
        //第六步，转换成pdf（可以省略，具体看实现）
        Long fileId = ossFileService.editor2PDF(html,"开标记录表.pdf","开标记录表模板");
        return Result.ok(fileId);
    }




    public Result<XWPFTable> getBidOpenTable(Long ossFileId) throws IOException {
        OssFile ossFile = ossFileService.getOssFileById(ossFileId);
        OssFileStorage storage = OssUtil.getOssFileStorage(ossType);
        InputStream inputStream = null;
        if(null != ossFile && null != ossFile.getOssFileKey() ){
            byte[] bytes = storage.downloadFile(ossFile.getOssFileKey());
            inputStream = FileUtils.byte2InputStream(bytes);
        }
        //FileInputStream fis = new FileInputStream(new File("/111.docx"));
        if(inputStream == null){
            return Result.failed("获取开标记录表失败");
        }
        XWPFDocument document = new XWPFDocument(inputStream);
        //获取word的表格
        List<XWPFTable> tables = document.getTables();
        if(CollectionUtil.isEmpty(tables)){
            //读取不到表格解析失败
            return Result.failed("读取开标记录表表格失败");
        }
        return Result.ok(tables.get(0));
    }

//    /**
//     * 判断是横表还是竖表
//     * @param table
//     * @return 0：横表，1竖表，2：无法解析
//     */
//    private int headFlag(XWPFTable table){
//        List<XWPFTableRow> rows = table.getRows();
//        //判断最后一行是否有加粗的，有加粗说明就是竖表
//        boolean vertical = false;
//        List<XWPFTableCell> lastCells = rows.get(rows.size()-1).getTableCells();
//        for(XWPFTableCell cell:lastCells){
//            XWPFParagraph paragraph = cell.getParagraphArray(0);
//            if (StringUtils.isNotBlank(paragraph.getText()) && paragraph.getRuns().get(0).isBold()) {
//                vertical = true;
//                break;
//            }
//        }
//        //判断最后一列是否有加粗的，有加粗说明是横表
//        boolean normal = false;
//        for (XWPFTableRow row : rows) {
//            List<XWPFTableCell> cells = row.getTableCells();
//            XWPFTableCell cell = cells.get(cells.size() - 1);
//            XWPFParagraph paragraph = cell.getParagraphArray(0);
//            if (StringUtils.isNotBlank(paragraph.getText()) && paragraph.getRuns().get(0).isBold()) {
//                normal = true;
//                break;
//            }
//        }
//        if(vertical&&!normal){
//            return TableHeadTypeConstants.VERTICAL;
//        }else if(!vertical&&normal){
//            return TableHeadTypeConstants.NORMAL;
//        }else{
//            return TableHeadTypeConstants.OTHER;
//        }
//    }
//
//    /**
//     * 存解析后开标一览表返回ossid
//     * @param filePath
//     * @param fileName
//     * @return
//     * @throws IOException
//     */
//    public Long saveFile(String filePath,String fileName) {
//        Long ossFileId = null;
//        try {
//            // 保存临时文件
//            ossFileId = ossFileService.saveOssFile(fileName, filePath);
//        } catch (Exception e) {
//            e.printStackTrace();
//        } finally {
//            // 删除临时文件
//            File file = new File(filePath);
//            if (file.exists()) {
//                file.delete();
//            }
//        }
//        return ossFileId;
//    }
//
//    private int getHeadNum(List<XWPFTableRow> rows,int isVertical){
//        int headNum = 0;
//        if(TableHeadTypeConstants.VERTICAL == isVertical){
//            //记录加粗的最大列数
//            for (XWPFTableRow row : rows) {
//                List<XWPFTableCell> cells = row.getTableCells();
//                int temp = 0;
//                for (int j = 0; j < cells.size(); j++) {
//                    XWPFParagraph paragraph = cells.get(j).getParagraphArray(0);
//                    //找出加粗的最大列数
//                    if (StringUtils.isNotBlank(paragraph.getText()) && paragraph.getRuns().get(0).isBold()) {
//                        temp = j+1;
//                    }
//                }
//                if (temp > headNum) {
//                    headNum = temp;
//                }
//            }
//        }else{
//            //记录加粗最大行数
//            for(int i=0;i<rows.size();i++){
//                List<XWPFTableCell> cells = rows.get(i).getTableCells();
//                int cellNum=0;
//                for (XWPFTableCell cell : cells) {
//                    XWPFParagraph paragraph = cell.getParagraphArray(0);
//                    String text = paragraph.getText();
//                    if(StringUtils.isBlank(text)){
//                        cellNum++;
//                    }else{
//                        if(!paragraph.getRuns().get(0).isBold()){
//                            cellNum++;
//                        }
//                    }
//                }
//                if(cellNum == cells.size()){
//                    headNum = i;
//                    break;
//                }
//            }
//        }
//        return headNum;
//    }
//
//    /**
//     * 处理竖表（暂时不支持复杂表头）
//     * @param table 开标一览表
//     * @param dealTable 解析示例
//     */
//    private Result<String> dealVertical(int headNum,XWPFTable table, XWPFTable dealTable){
//        List<XWPFTableRow> rows = table.getRows();
//        for(int i=0;i<headNum;i++){
//            XWPFTableRow newRow = dealTable.getRow(0);
//            for(int j=0;j<rows.size();j++){
//                List<XWPFTableCell> cells = rows.get(j).getTableCells();
//                XWPFTableCell newCell;
//                if(j>0){
//                    newCell = newRow.createCell();
//                    setStyle(newCell,true,cells.get(i).getText());
//                }else{
//                    newCell = newRow.getCell(0);
//                    setStyle(newCell,true,"供应商");
//                }
//            }
//        }
//        for(int i=0;i<3;i++){
//            for(int j=headNum;j<rows.get(0).getTableCells().size();j++){
//                XWPFTableRow newRow = dealTable.createRow();
//                newRow.getTableCells().get(0).setText("供应商"+(i+1));
//            }
//        }
//        //模板三行合并
//        int mainNum = rows.get(0).getTableCells().size()- headNum;
//        for(int i=0;i<3;i++){
//            mergeCellsVertically(dealTable,0,headNum,headNum+mainNum-1);
//            headNum= headNum+mainNum;
//        }
//        return Result.ok();
//    }
//
//    /**
//     * 处理普通表
//     * @param table 开标一览表
//     */
//    private Result<String> dealNormal(int headNum,XWPFTable table,XWPFTable dealTable){
//        List<XWPFTableRow> rows=table.getRows();
//        //组装表头
//        for(int i=0;i<headNum;i++){
//            XWPFTableRow newRow = dealTable.createRow();
//            List<XWPFTableCell> cells = rows.get(i).getTableCells();
//            setStyle(newRow.getCell(0),true,"供应商");
//            for(XWPFTableCell cell:cells){
//                XWPFTableCell newCell =  newRow.createCell();
//                newCell.getCTTc().setTcPr(cell.getCTTc().getTcPr());
//                //表头加粗
//                XWPFRun run = newCell.getParagraphArray(0).createRun();
//                run.setText(cell.getText());
//                run.setBold(true);
//            }
//        }
//        //删除默认行
//        dealTable.removeRow(0);
//        for(int i=0;i<3;i++){
//            for(int j=headNum;j<rows.size();j++){
//                List<XWPFTableCell> cells = rows.get(j).getTableCells();
//                XWPFTableRow newRow = dealTable.createRow();
//                newRow.createCell();
//                setStyle(newRow.getCell(0),false,"供应商"+(i+1));
//                for(int k=0;k<cells.size();k++){
//                    XWPFTableCell newCell =  newRow.getCell(k+1);
//                    CTTcPr tcPr = CTTcPr.Factory.newInstance();
//                    newCell.getCTTc().setTcPr(tcPr);
//                    newCell.setText(cells.get(k).getText());
//                }
//            }
//        }
//        //表头合并
//        mergeCellsVertically(dealTable,0,0,headNum-1);
//        //模板三行合并
//        int mainNum = rows.size()- headNum;
//        for(int i=0;i<3;i++){
//            mergeCellsVertically(dealTable,0,headNum,headNum+mainNum-1);
//            headNum= headNum+mainNum;
//        }
//        return Result.ok();
//    }
//
//    //设置样式
//    private static void setStyle(XWPFTableCell cell,boolean isHead,String text){
//        CTTcPr tcpr = cell.getCTTc().addNewTcPr();
//        //设置宽度
//        CTTblWidth cellWidth = tcpr.addNewTcW();
//        cellWidth.setType(STTblWidth.DXA);
//        cellWidth.setW(BigInteger.valueOf(360*5));
//        //设置居中
//        tcpr.addNewVAlign().setVal(STVerticalJc.Enum.forInt(STVerticalJc.INT_CENTER));
//        if(isHead){
//            //表头需要加粗
//            XWPFRun run = cell.getParagraphArray(0).createRun();
//            run.setText(text);
//            run.setBold(true);
//        }else{
//            cell.setText(text);
//        }
//    }
//
//    /**
//     * 跨行合并单元格
//     * @param table
//     * @param col
//     * @param fromRow
//     * @param toRow
//     */
//    public void mergeCellsVertically(XWPFTable table, int col, int fromRow, int toRow) {
//        for (int rowIndex = fromRow; rowIndex <= toRow; rowIndex++) {
//            XWPFTableCell cell = table.getRow(rowIndex).getCell(col);
//            if ( rowIndex == fromRow ) {
//                // The first merged cell is set with RESTART merge value
//                cell.getCTTc().addNewTcPr().addNewVMerge().setVal(STMerge.RESTART);
//            } else {
//                // Cells which join (merge) the first one, are set with CONTINUE
//                cell.getCTTc().addNewTcPr().addNewVMerge().setVal(STMerge.CONTINUE);
//            }
//        }
//    }

    /**
     * 获取前一天时间
     *
     * @param date
     */
    private Date getPreviousDay(Date date) {
        Calendar previous = Calendar.getInstance();
        previous.setTime(date);
        previous.add(Calendar.DAY_OF_MONTH, -1);
        Date previousTime = previous.getTime();
        return previousTime;
    }

    /**
     * 新增是否支持提出澄清异议
     * @param dataInfoREQ
     * @return
     */
    private boolean modifyClarifyQuestion(DocDataInfoREQ dataInfoREQ) {
        if(null != dataInfoREQ.getAttachmentInfo()){
            if(null != dataInfoREQ.getAttachmentInfo().getDeleteSectionId()){
                for (int i = 0; i < dataInfoREQ.getAttachmentInfo().getDeleteSectionId().size(); i++) {
                    Long sectionId = dataInfoREQ.getAttachmentInfo().getDeleteSectionId().get(i);
                    LambdaQueryWrapper<ClarifyQuestion> wrapper = new LambdaQueryWrapper<>();
                    wrapper.eq(ClarifyQuestion::getSectionId,sectionId);
                    clarifyQuestionService.remove(wrapper);
                }
            }
        }
        List<ClarifyQuestion> clarifyQuestionList = new ArrayList<>();
        for (ProjectBidDocREQ d :dataInfoREQ.getBidDocInfo()) {
            for (int i = 0; i < d.getSectionIdArray().length; i++) {
                ClarifyQuestion clarifyQuestion = new ClarifyQuestion();
                clarifyQuestion.setProjectId(d.getProjectId());
                clarifyQuestion.setSectionId(d.getSectionIdArray()[i]);
                clarifyQuestion.setClarifyOnLine(d.getClarifyOnLine());
                if(d.getClarifyOnLine().equals(TimeTypeEnum.SUPPORT.getValue())){
                    clarifyQuestion.setClarifyEndTimeType(d.getClarifyEndTimeType());
                    if(d.getClarifyEndTimeType().equals(TimeTypeEnum.C_PREVIOUS_DAY.getValue())
                            && d.getSubmitEndTimeType().equals(TimeTypeEnum.CONFIRM.getValue())){
                        clarifyQuestion.setClarifyEndTime(getPreviousDay(d.getSubmitEndTimeArray()[i]));
                    } else if(d.getClarifyEndTimeType().equals(TimeTypeEnum.C_CONFIRM.getValue())){
                        clarifyQuestion.setClarifyEndTime(d.getClarifyEndTimeArray()[i]);
                    }
                }
                clarifyQuestion.setQuestionOnLine(d.getQuestionOnLine());
                if(d.getQuestionOnLine().equals(TimeTypeEnum.SUPPORT.getValue())){
                    clarifyQuestion.setQuestionEndTimeType(d.getQuestionEndTimeType());
                    if(d.getQuestionEndTimeType().equals(TimeTypeEnum.C_PREVIOUS_DAY.getValue())
                            && d.getSubmitEndTimeType().equals(TimeTypeEnum.CONFIRM.getValue())){
                        clarifyQuestion.setQuestionEndTime(getPreviousDay(d.getSubmitEndTimeArray()[i]));
                    } else if(d.getQuestionEndTimeType().equals(TimeTypeEnum.C_CONFIRM.getValue())) {
                        clarifyQuestion.setQuestionEndTime(d.getQuestionEndTimeArray()[i]);
                    }
                }

                LambdaQueryWrapper<ClarifyQuestion> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(ClarifyQuestion::getProjectId,d.getProjectId())
                        .eq(ClarifyQuestion::getSectionId,d.getSectionIdArray()[i]);
                ClarifyQuestion one = clarifyQuestionService.getOne(queryWrapper);
                if(null != one){
                    clarifyQuestion.setId(one.getId());
                }
                clarifyQuestionList.add(clarifyQuestion);
            }
        }
        return clarifyQuestionService.saveOrUpdateBatch(clarifyQuestionList);
    }

    /**
     * 保存线上开标信息
     * @param dataInfoREQ
     * @return
     */
    private boolean saveBidOpenInfo(DocDataInfoREQ dataInfoREQ) {
        // 根据附件信息取消勾选标段删除原有信息
        if(null != dataInfoREQ.getAttachmentInfo() && dataInfoREQ.getAttachmentInfo().getDeleteSectionId().size() > 0){
            projectSectionBidOpenService.remove(new LambdaQueryWrapper<ProjectSectionBidOpen>()
                    .in(ProjectSectionBidOpen::getSectionId, dataInfoREQ.getAttachmentInfo().getDeleteSectionId()));
        }
        // 新增或修改新的开标信息
        List<ProjectSectionBidOpen> list = new ArrayList<>();
        for (ProjectBidDocREQ d :dataInfoREQ.getBidDocInfo()) {
//            dataInfoREQ.getScheduleREQ().setOnlineBidOpen(d.getOnlineBidOpen());
//            if(!CommonConstants.YES.equals(d.getOnlineBidOpen())){
//                List<Long> ids = Arrays.asList(d.getSectionIdArray());
//                dataInfoREQ.getScheduleREQ().setDeleteSectionId(ids);
//            }
            for (int i = 0; i < d.getSectionIdArray().length; i++) {
                Long sectionId = d.getSectionIdArray()[i];
                ProjectSectionBidOpen projectSectionBidOpen = new ProjectSectionBidOpen();
                projectSectionBidOpen.setProjectId(d.getProjectId());
                projectSectionBidOpen.setSectionId(sectionId);
                projectSectionBidOpen.setBidOpenOnline(d.getOnlineBidOpen());
                projectSectionBidOpen.setBidRound(d.getBidRound());
                if (CommonConstants.YES.equals(d.getOnlineBidOpen())) {
                    projectSectionBidOpen.setDecryptTime(d.getDecryptionTimeArray()[i]);
                    projectSectionBidOpen.setSupNumEndBid(d.getConditionBidOpenArray()[i]);
//                    projectSectionBidOpen.setBidOpenOssId(d.getBidOpeningArray()[i]);
//                    projectSectionBidOpen.setBidOpenRecordMode(d.getGenerationArray()[i]);
                    projectSectionBidOpen.setIsAutoChant(d.getSystemAutomaticLabelingArray()[i]);
                }
                // 判断当前标段是否已存在信息
                ProjectSectionBidOpen open = projectSectionBidOpenService.getOne(new LambdaQueryWrapper<ProjectSectionBidOpen>().eq(ProjectSectionBidOpen::getSectionId, sectionId)
                    .eq(ProjectSectionBidOpen::getBidRound, d.getBidRound()));
                if (open != null) {
                    projectSectionBidOpen.setId(open.getId());
                }
                list.add(projectSectionBidOpen);
            }
        }
        return projectSectionBidOpenService.saveOrUpdateBatch(list);
    }



    /**
     * 解析开标一览表
     * @return
     * @throws IOException
     */
    @Override
    public Result<Long> analysisBidDoc(AnalysisBidDocDTO dto) throws Exception {
         LambdaQueryWrapper<ProjectBidDocSchedule> lqw = new LambdaQueryWrapper<>();
        lqw.eq(ProjectBidDocSchedule::getProjectId,dto.getProjectId());
        lqw.eq(ProjectBidDocSchedule::getSectionId,dto.getSectionId());
        projectBidDocScheduleService.remove(lqw);
        ProjectBidDocSchedule pbds = new ProjectBidDocSchedule();
        pbds.setProjectId(dto.getProjectId());
        pbds.setSectionId(dto.getSectionId());
        XWPFTable table;
        Result<XWPFTable> resultTable =  getBidOpenTable(dto.getOssId());
        if(resultTable.getFlag()){
            table = resultTable.getData();
        }else{
            return Result.failed(resultTable.getMsg());
        }
        List<List<Map<String, Object>>> tableParse = new ArrayList<>();
        //第一步，解析列合并，填充文字，并且把行合并的格式记录下来
        hMerger(tableParse,table.getRows());
        //第二步，解析行合并，填充文字
        vMerger(tableParse);
        //第三步，判断是横表还是竖表
        Result<Boolean> result = analysisHead(tableParse);
        if(!result.getFlag()){
            return Result.failed(result.getMsg());
        }
        //第四步，生成模板，若是竖表的话需要转换
        if(result.getData()){
            pbds.setTableType(2);
            tableParse = changeVertical2Normal(tableParse);
        }else{
            pbds.setTableType(1);
        }
        pbds.setHeadHeight(getHeadNum(tableParse));
        pbds.setTableLength(tableParse.get(tableParse.size() -1 ).size() );
        List<List<Map<String, Object>>> template = createTemplate(tableParse);
        pbds.setTableHeadHtml(excelToHtml(template.subList(0,getHeadNum(tableParse))));
        pbds.setTableHead(JSONObject.toJSONString(template.subList(0,getHeadNum(tableParse))));
        //第五步，转换成html
        String html = excelToHtml(template);
        html = sectionScheduleService.assembleHtml(html,dto.getProjectId(),dto.getSectionId());
        projectBidDocScheduleService.save(pbds);
        //第六步，转换成pdf（可以省略，具体看实现）
        Long fileId = ossFileService.editor2PDF(html,"开标记录表.pdf","开标记录表");
        return Result.ok(fileId);
    }

}
