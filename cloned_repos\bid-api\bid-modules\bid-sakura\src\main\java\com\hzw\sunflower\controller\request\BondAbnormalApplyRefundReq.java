package com.hzw.sunflower.controller.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ApiModel(description = "异常退款申请(供应商)")
public class BondAbnormalApplyRefundReq implements Serializable {

    @ApiModelProperty(value = "申请退还id")
    private Long applyId;

    @ApiModelProperty(value = "流水id")
    private Long waterId;

    @ApiModelProperty(value = "付款凭证")
    private Long fileIds;

    @ApiModelProperty(value = "退款说明函")
    private Long refundFiles;

    @ApiModelProperty(value = "开户行")
    private String companyBankDeposit;

    @ApiModelProperty(value = "开户行行号")
    private String companyBankCode;

    @ApiModelProperty(value = "项目标段信息")
    private List<BondApplyRefundProjectReq> projectSections;

}
