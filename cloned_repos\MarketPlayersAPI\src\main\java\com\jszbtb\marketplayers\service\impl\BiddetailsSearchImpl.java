package com.jszbtb.marketplayers.service.impl;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jszbtb.marketplayers.component.MD5;
import com.jszbtb.marketplayers.configuration.ConfirmCallBackHandler;
import com.jszbtb.marketplayers.configuration.ReturnCallBackHandler;
import com.jszbtb.marketplayers.controller.AbstractController;
import com.jszbtb.marketplayers.dao.BidderDao;
import com.jszbtb.marketplayers.dao.SupplierDao;
import com.jszbtb.marketplayers.dao.TenderAgencyDao;
import com.jszbtb.marketplayers.dao.TenderDao;
import com.jszbtb.marketplayers.entity.*;
import com.jszbtb.marketplayers.service.BiddetailsSearch;
import com.rabbitmq.client.Channel;
import org.elasticsearch.ElasticsearchException;
import org.elasticsearch.action.DocWriteResponse;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.action.index.IndexResponse;
import org.elasticsearch.action.search.*;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.common.unit.TimeValue;
import org.elasticsearch.common.xcontent.XContentType;
import org.elasticsearch.index.query.*;
import org.elasticsearch.rest.RestStatus;
import org.elasticsearch.search.Scroll;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.rabbit.support.CorrelationData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.messaging.handler.annotation.Headers;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.validation.Valid;
import java.io.IOException;
import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeoutException;

@Service
public class BiddetailsSearchImpl extends AbstractController implements BiddetailsSearch{

    Logger logger = LoggerFactory.getLogger(BiddetailsSearchImpl.class);

    @Value("${elasticsearch.index}")
    private String index;

    @Value("${elasticsearch.type}")
    private String type;

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    private ConfirmCallBackHandler confirmCallBackHandler;
    @Autowired
    private ReturnCallBackHandler returnCallBackHandler;
    @Autowired
    private RestHighLevelClient restHighLevelClient;



    @Override
    public List<Object> select(String text, String startTime, String endTime, int pageSize, int pageIndex, String types) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Map<String,Float> map = new HashMap<>();
        List<Object> list = new ArrayList<>();
        SearchRequest request = new SearchRequest(index);
        JSONObject jsonObject = null;
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        try {
            test(text,startTime,endTime,pageSize,pageIndex,types, map,sdf,searchSourceBuilder,null);
            searchSourceBuilder.trackTotalHits(true);
            request.source(searchSourceBuilder);
            SearchResponse response = restHighLevelClient.search(request, RequestOptions.DEFAULT);
            SearchHits hits = response.getHits();
            int taotal=(int)hits.getTotalHits().value;
            int taotalPage=taotal % pageSize== 0?(taotal/pageSize):(taotal/pageSize)+1;
            for (SearchHit hit:hits) {
                String source=hit.getSourceAsString();
                jsonObject=JSONObject.parseObject(source);
                jsonObject.put("totalPage",taotalPage);
                jsonObject.put("totalNumber",taotal);
                list.add(jsonObject);
            }
            return list;
        }catch (IOException e){
            logger.error(e.getMessage());
            e.printStackTrace();
        }
        return null;
    }


    @Override
    public Object scroll(String text, String startTime, String endTime, int pageSize, int pageIndex, String types,String scrollId) {
        final Scroll scroll = new Scroll(TimeValue.timeValueMinutes(1L));//设置有效时间
        SearchRequest searchRequest = new SearchRequest(index);//创建es请求
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Map<String,Float> map = new HashMap<>();
        List<Object> list = new ArrayList<>();
        searchRequest.scroll(scroll);//设置为滚动
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder(); //创建es sql容器
        test(text,startTime,endTime,pageSize,pageIndex,types, map,sdf,searchSourceBuilder,"scrollId");
        searchRequest.source(searchSourceBuilder);
        try {
            SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            SearchHit[] searchHits = null;
            JSONObject jsonObject = null;
            if(scrollId != null && !scrollId.equals("")){
                SearchScrollRequest scrollRequest = new SearchScrollRequest(scrollId);
                scrollRequest.scroll(scroll);
                searchHits=searchResponse.getHits().getHits(); //获取查询的对象
                for (SearchHit hit:searchHits) {
                    String source=hit.getSourceAsString();
                    jsonObject=JSONObject.parseObject(source);
                    jsonObject.put("scrollId",searchResponse.getScrollId());
                    list.add(jsonObject);
                }
            }else{
                String sid = searchResponse.getScrollId();//获取返回scroll id
                searchHits=searchResponse.getHits().getHits(); //获取查询的对象
                for (SearchHit hit:searchHits) {
                    String source=hit.getSourceAsString();
                    jsonObject=JSONObject.parseObject(source);
                    jsonObject.put("scrollId",sid);
                    list.add(jsonObject);
                }
            }
            //遍历搜索命中的数据，直到没有数据
         /*  while (searchHits != null && searchHits.length > 0) {
                SearchScrollRequest scrollRequest = new SearchScrollRequest(scrollId);
                scrollRequest.scroll(scroll);
                try {
                    searchResponse = restHighLevelClient.scroll(scrollRequest, RequestOptions.DEFAULT);
                } catch (IOException e) {
                    e.printStackTrace();
                }
                scrollId = searchResponse.getScrollId();
                searchHits = searchResponse.getHits().getHits();
                if (searchHits != null && searchHits.length > 0) {
                    System.out.println("-----下一页-----");
                    for (SearchHit searchHit : searchHits) {
                        System.out.println(searchHit.getSourceAsString());
                    }
                }

            }*/

            //清除滚屏
           /* ClearScrollRequest clearScrollRequest = new ClearScrollRequest();
            clearScrollRequest.addScrollId(scrollId);//也可以选择setScrollIds()将多个scrollId一起使用
            ClearScrollResponse clearScrollResponse = null;
            try {
                clearScrollResponse = restHighLevelClient.clearScroll(clearScrollRequest,RequestOptions.DEFAULT);
            } catch (IOException e) {
                e.printStackTrace();
            }
            boolean succeeded = clearScrollResponse.isSucceeded();
            System.out.println("succeeded:" + succeeded);*/

        } catch (IOException e) {
            logger.error(e.getMessage());
            e.printStackTrace();
        }
        return list;
    }

    @Transactional
//    @RabbitListener(queues={"${spring.rabbitmq.queueName}"})//Tender Bidder
    @Override
    public void insert(@Valid String str, Channel channel, Message message, @Headers Map<String,Object> map){
//        try {
//            String messageBody = new String(message.getBody(),"utf-8");
//            JSONObject jsonObject = JSONObject.parseObject(messageBody);
//            String type = (String) jsonObject.get("type");
//            if(type.equals("tender")){
//                TenderEntity entity = (TenderEntity) getEntity(jsonObject, "tender");
//                tenderDao.insert(entity);
//                String s = entity.getLegalCode().toString() + entity.getType();
//                boolean b =insertIndex(entity, MD5.encode(s));
//                if(b){
//                    channel.basicAck(message.getMessageProperties().getDeliveryTag(),false);
//                }else {
//                    channel.basicNack(message.getMessageProperties().getDeliveryTag(),false,true);
//                    throw new RuntimeException("index create failed");
//                }
//            }else if(type.equals("bidder")){
//                BidderEntity entity = (BidderEntity) getEntity(jsonObject, "bidder");
//                bidderDao.insert(entity);
//                String s = entity.getLegalCode().toString() + entity.getType();
//                boolean b =insertIndex(entity, MD5.encode(s));
//                if(b){
//                    channel.basicAck(message.getMessageProperties().getDeliveryTag(),false);
//                }else {
//                    channel.basicNack(message.getMessageProperties().getDeliveryTag(),false,true);
//                    throw new RuntimeException("index create failed");
//                }
//            }else if(type.equals("suplier")){
//                SupplierEntity entity = (SupplierEntity) getEntity(jsonObject, "suplier");
//                supplierDao.insert(entity);
//                String s = entity.getLegalCode().toString() + entity.getType();
//                boolean b =insertIndex(entity, MD5.encode(s));
//                if(b){
//                    channel.basicAck(message.getMessageProperties().getDeliveryTag(),false);
//                }else {
//                    channel.basicNack(message.getMessageProperties().getDeliveryTag(),false,true);
//                    throw new RuntimeException("index create failed");
//                }
//            }else if(type.equals("tenderagency")){
//                TenderAgencyEntity entity = (TenderAgencyEntity) getEntity(jsonObject, "tenderagency");
//                tenderAgencyDao.insert(entity);
//                String s = entity.getLegalCode().toString() + entity.getType();
//                boolean b =insertIndex(entity, MD5.encode(s));
//                if(b){
//                    channel.basicAck(message.getMessageProperties().getDeliveryTag(),false);
//                }else {
//                    channel.basicNack(message.getMessageProperties().getDeliveryTag(),false,true);
//                    throw new RuntimeException("index create failed");
//                }
//            }
//        }catch (IOException ex){
//            logError(ex);
//            throw  new RuntimeException("rebbitmq 连接失败");
//        }catch (Exception e){
//            logError(e);
//            try {
//                channel.basicNack(message.getMessageProperties().getDeliveryTag(),false,true);
//            }catch (Exception t){
//                logError(t);
//            }
//            throw e;
//        }
    }

    @Override
    public void scopeSend(Map<String, String> map) {
        String json = "";
        RabbitTemplate rabbitTemplate=getScopeRabbitTemplate();
        CorrelationData correlationData = new CorrelationData(UUID.randomUUID().toString().replace("-", ""));
        logger.info("发送者消息id"+UUID.randomUUID().toString().replace("-", ""));
        rabbitTemplate.setConfirmCallback(confirmCallBackHandler);
        rabbitTemplate.setReturnCallback(returnCallBackHandler);
        rabbitTemplate.convertAndSend("exchange","hua1",json,correlationData);
    }

    private RabbitTemplate getScopeRabbitTemplate(){
        RabbitTemplate  rabbitTemplate =(RabbitTemplate) applicationContext.getBean("scopeRabbitTemplate");
        logger.info("获取新的RabbitTemplate:{}",rabbitTemplate);
        return  rabbitTemplate;
    }

    private boolean insertIndex(Object o,String s) throws IOException{
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        IndexRequest request = new IndexRequest(index, type, s);
        JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(o));
        jsonObject.put("createTime",simpleDateFormat.format(new Date()));
        String  json = JSON.toJSONString(jsonObject);
        request.source(json, XContentType.JSON);
        IndexResponse response = null;
        try {
            response =restHighLevelClient.index(request,RequestOptions.DEFAULT);
        }catch (ElasticsearchException e){
            if(e.status() == RestStatus.CONFLICT){//create但文档已存在冲突
                return true;
            }else {
                logger.error("错误："+e.getMessage()+"。 参数:"+json);
                return false;
            }
        }
        if(response != null){
            return true;
        }else {
            logger.error("response is NULL 添加es索引失败:"+json);
            return false;
        }
    }

    private Object getEntity(JSONObject jsonObject,String type){
        String id = UUID.randomUUID().toString();
        if(type.equals("tender")){
            TenderEntity entity = jsonObject.toJavaObject(TenderEntity.class);
            entity.setGuid(id);
            entity.setType("tender");
            entity.setRegCaptialCurrency((String)jsonObject.get("regCapCurrency"));
            entity.setRegCaptialUnit((String)jsonObject.get("regUnit"));
            entity.setRegCaptial((String)jsonObject.get("regCapital"));
            entity.setOrgNo((String)jsonObject.get("organNo"));
            entity.setOrgCertEndDate((String)jsonObject.get("organCertEndDate"));
            entity.setLegalContactPhoneNo((String)jsonObject.get("legalContactPhone"));
            entity.setLegalContact(entity.getLegalRepresent());
            entity.setLegalRepresentPhoneNo((String)jsonObject.get("legalContactPhone"));
            entity.setLegalContactEmail((String)jsonObject.get("legalEmail"));
            entity.setLegalUnitAddress((String)jsonObject.get("legalEmail"));
            return entity;
        }else if(type.equals("bidder")){
            @Valid BidderEntity entity = jsonObject.toJavaObject(BidderEntity.class);
            entity.setGuid(id);
            entity.setType("bidder");
            entity.setRegCaptialCurrency((String)jsonObject.get("regCapCurrency"));
            entity.setRegCaptialUnit((String)jsonObject.get("regUnit"));
            entity.setRegCaptial((String)jsonObject.get("regCapital"));
            entity.setOrgNo((String)jsonObject.get("organNo"));
            entity.setOrgCertEndDate((String)jsonObject.get("organCertEndDate"));
            entity.setLegalContactPhoneNo((String)jsonObject.get("legalContactPhone"));
            entity.setLegalContact(entity.getLegalRepresent());
            entity.setLegalRepresentPhoneNo((String)jsonObject.get("legalContactPhone"));
            entity.setLegalContactEmail((String)jsonObject.get("legalEmail"));
            entity.setLegalUnitAddress(getUnitAddress(jsonObject));
            return entity;
        }else if(type.equals("suplier")){
            SupplierEntity entity = jsonObject.toJavaObject(SupplierEntity.class);
            entity.setGuid(id);
            entity.setType("suplier");
            entity.setRegCaptialCurrency((String)jsonObject.get("regCapCurrency"));
            entity.setRegCaptialUnit((String)jsonObject.get("regUnit"));
            entity.setRegCaptial((String)jsonObject.get("regCapital"));
            entity.setOrgNo((String)jsonObject.get("organNo"));
            entity.setOrgCertEndDate((String)jsonObject.get("organCertEndDate"));
            entity.setLegalContactPhoneNo((String)jsonObject.get("legalContactPhone"));
            entity.setLegalContact(entity.getLegalRepresent());
            entity.setLegalRepresentPhoneNo((String)jsonObject.get("legalContactPhone"));
            entity.setLegalContactEmail((String)jsonObject.get("legalEmail"));
            entity.setLegalUnitAddress(getUnitAddress(jsonObject));
            return entity;
        }else if(type.equals("tenderagency")){
            TenderAgencyEntity entity = jsonObject.toJavaObject(TenderAgencyEntity.class);
            entity.setGuid(id);
            entity.setType("tenderagency");
            entity.setRegCaptialCurrency((String)jsonObject.get("regCapCurrency"));
            entity.setRegCaptialUnit((String)jsonObject.get("regUnit"));
            entity.setRegCaptial((String)jsonObject.get("regCapital"));
            entity.setOrgNo((String)jsonObject.get("organNo"));
            entity.setOrgCertEndDate((String)jsonObject.get("organCertEndDate"));
            entity.setLegalContactPhoneNo((String)jsonObject.get("legalContactPhone"));
            entity.setLegalContact(entity.getLegalRepresent());
            entity.setLegalRepresentPhoneNo((String)jsonObject.get("legalContactPhone"));
            entity.setLegalContactEmail((String)jsonObject.get("legalEmail"));
            entity.setLegalUnitAddress(getUnitAddress(jsonObject));
            return entity;
        }
        return null;
    }

    private String getUnitAddress(JSONObject jsonObject){
        String registerProvince = (String) jsonObject.get("registerProvince");
        String registerCity = (String) jsonObject.get("registerCity");
        return registerProvince+"."+registerCity;
    }


    public void  test(String text, String startTime, String endTime, int pageSize, int pageIndex, String types
            ,Map<String,Float> map,SimpleDateFormat sdf,SearchSourceBuilder searchSourceBuilder,String scrollId){
        try {
            if(scrollId == null){
                if(text!=null && !text.equals("")){
                    if(startTime != null && !startTime.equals("")){
                        if(endTime == null || endTime.equals("")){
                            endTime = startTime;
                        }
                        MultiMatchQueryBuilder multiMatchQueryBuilder = QueryBuilders.multiMatchQuery(text);
                        map.put("legalRepresent",Float.valueOf(1));
                        map.put("legalName",Float.valueOf(3));
                        multiMatchQueryBuilder.fields(map);
                        multiMatchQueryBuilder.type("best_fields");
                        multiMatchQueryBuilder.minimumShouldMatch("66%");
                        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
                        MatchPhrasePrefixQueryBuilder match = new MatchPhrasePrefixQueryBuilder("legalContactPhoneNo",text);
                        boolQueryBuilder.should(multiMatchQueryBuilder).should(match).must(QueryBuilders.rangeQuery("createTime.keyword")
                                .gt(sdf.format(sdf.parse(startTime))).lt(sdf.format(sdf.parse(endTime))).includeLower(true).includeUpper(true)).must(QueryBuilders.termsQuery("type",types));
                        searchSourceBuilder.query(boolQueryBuilder).size(pageSize).from(pageIndex);
                    }else{
                        MultiMatchQueryBuilder multiMatchQueryBuilder = QueryBuilders.multiMatchQuery(text);
                        map.put("legalRepresent",Float.valueOf(1));
                        map.put("legalName",Float.valueOf(3));
                        multiMatchQueryBuilder.fields(map);
                        multiMatchQueryBuilder.type("best_fields");
                        multiMatchQueryBuilder.minimumShouldMatch("66%");
                        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
                        MatchPhrasePrefixQueryBuilder match = new MatchPhrasePrefixQueryBuilder("legalContactPhoneNo",text);
                        boolQueryBuilder.should(multiMatchQueryBuilder).should(match).must(QueryBuilders.termsQuery("type",types));
                        searchSourceBuilder.query(boolQueryBuilder).size(pageSize).from(pageIndex);
                    }
                }else{
                    if(startTime != null && !startTime.equals("")){
                        if(endTime == null || endTime.equals("")){
                            endTime = startTime;
                        }
                        searchSourceBuilder.query(QueryBuilders.boolQuery().must(QueryBuilders.rangeQuery("createTime.keyword")
                                .gt(sdf.format(sdf.parse(startTime))).lt(sdf.format(sdf.parse(endTime))).includeLower(true).includeUpper(true)).must(QueryBuilders
                                .termsQuery("type",types))).size(pageSize).from(pageIndex);
                    }else{
                        searchSourceBuilder.query(QueryBuilders.boolQuery().must(QueryBuilders
                                .termsQuery("type",types))).size(pageSize).from(pageIndex).sort("createTime.keyword", SortOrder.DESC);
                    }
                }
            }else{
                if(text!=null && !text.equals("")){
                    if(startTime != null && !startTime.equals("")){
                        if(endTime == null || endTime.equals("")){
                            endTime = startTime;
                        }
                        MultiMatchQueryBuilder multiMatchQueryBuilder = QueryBuilders.multiMatchQuery(text);
                        map.put("legalRepresent",Float.valueOf(1));
                        map.put("legalName",Float.valueOf(3));
                        multiMatchQueryBuilder.fields(map);
                        multiMatchQueryBuilder.type("best_fields");
                        multiMatchQueryBuilder.minimumShouldMatch("66%");
                        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
                        MatchPhrasePrefixQueryBuilder match = new MatchPhrasePrefixQueryBuilder("legalContactPhoneNo",text);
                        boolQueryBuilder.should(multiMatchQueryBuilder).should(match).must(QueryBuilders.rangeQuery("createTime.keyword")
                                .gt(sdf.format(sdf.parse(startTime))).lt(sdf.format(sdf.parse(endTime))).includeLower(true).includeUpper(true)).must(QueryBuilders.termsQuery("type",types));
                        searchSourceBuilder.query(boolQueryBuilder).size(pageSize);
                    }else{
                        MultiMatchQueryBuilder multiMatchQueryBuilder = QueryBuilders.multiMatchQuery(text);
                        map.put("legalRepresent",Float.valueOf(1));
                        map.put("legalName",Float.valueOf(3));
                        multiMatchQueryBuilder.fields(map);
                        multiMatchQueryBuilder.type("best_fields");
                        multiMatchQueryBuilder.minimumShouldMatch("66%");
                        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
                        MatchPhrasePrefixQueryBuilder match = new MatchPhrasePrefixQueryBuilder("legalContactPhoneNo",text);
                        boolQueryBuilder.should(multiMatchQueryBuilder).should(match).must(QueryBuilders.termsQuery("type",types));
                        searchSourceBuilder.query(boolQueryBuilder).size(pageSize);
                    }
                }else{
                    if(startTime != null && !startTime.equals("")){
                        if(endTime == null || endTime.equals("")){
                            endTime = startTime;
                        }
                        searchSourceBuilder.query(QueryBuilders.boolQuery().must(QueryBuilders.rangeQuery("createTime.keyword")
                                .gt(sdf.format(sdf.parse(startTime))).lt(sdf.format(sdf.parse(endTime))).includeLower(true).includeUpper(true)).must(QueryBuilders
                                .termsQuery("type",types))).size(pageSize);
                    }else{
                        searchSourceBuilder.query(QueryBuilders.boolQuery().must(QueryBuilders
                                .termsQuery("type",types))).size(pageSize).sort("createTime.keyword", SortOrder.DESC);
                    }
                }
            }
            searchSourceBuilder.trackTotalHits(true);
        }catch (ParseException e){
            logger.error(e.getMessage());
            e.printStackTrace();
        }
    }

}
