package com.hzw.ssm.fw.db;

import java.sql.Connection;

import javax.annotation.Resource;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.support.SqlSessionDaoSupport;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * MyBatis获取数据库链接
 * <AUTHOR>
 *
 */
public class BatisConnection extends SqlSessionDaoSupport {
	
	private static final Log log = LogFactory.getLog(BatisConnection.class);
	
	private static SqlSession sqlSession = null;
	
	/** 未关闭的数据库连接的个数 */
	private static int iNotCloseConnectionNum = 0;
	
	/**
	 * 获取SqlSessionFactory
	 * @param sqlSessionFactory
	 */
	@Autowired(required = true)
	@Resource(name = "sqlSessionFactory")
	public void setSuperSqlSessionFactory(SqlSessionFactory sqlSessionFactory){
		super.setSqlSessionFactory(sqlSessionFactory);
	} 
	
	/**
	 * 初始化获取SqlSession
	 * @throws Exception
	 */
	public static void init() throws Exception{
		BatisConnection batisConnection = new BatisConnection();
		sqlSession = batisConnection.getSqlSession();
	}
	
	/**
	 * 获取数据库连接对象
	 * @param autoCommit 是否自动获取数据库链接
	 * @return 数据库连接对象
	 */
	public static Connection openConnection(boolean autoCommit){
		Connection conn = null;
		try {
			conn = sqlSession.getConnection();
			
			//增加链接个数
			iNotCloseConnectionNum++;
			log.info("手工获取数据库链接成功！");
		} catch (Exception e) {
			e.printStackTrace();
			log.info("手工获取数据库链接失败！");
			log.info(e);
		}
		return conn;
	}
	
	/**
	 * 关闭数据库连接
	 * @param conn 数据库连接对象
	 */
	public static void closeConnection(Connection conn){
		try {
			if(conn != null){
				conn.close();
				conn = null;
				
				//关闭后减去链接个数
				iNotCloseConnectionNum--;
				log.info("手工释放数据库链接成功！");
			}
		} catch (Exception e) {
			e.printStackTrace();
			log.info("手工释放数据库链接失败！");
			log.info(e);
		}
	}
}
