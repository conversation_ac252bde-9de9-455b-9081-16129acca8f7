/**
 * 
 */
package com.hzw.ssm.applets.action;

import java.io.IOException;
import java.io.PrintWriter;
import java.util.Date;
import java.util.List;

import javax.servlet.http.HttpSession;

import org.apache.struts2.convention.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;

import com.hzw.ssm.applets.entity.Technical;
import com.hzw.ssm.applets.service.TechnicalService;
import com.hzw.ssm.fw.base.BaseAction;
import com.hzw.ssm.sys.user.entity.UserEntity;

/**
 * <AUTHOR>
 *	专家职业类
 */
@Namespace("/technical")
@ParentPackage(value = "default")
@Results({ @Result(	name = "technical",
location = "/jsp/technical/technical.jsp"),
	@Result(name="addtechnical",location = "/jsp/technical/addtechnical.jsp") })
public class TechnicalAction extends BaseAction{

	@Autowired
	private TechnicalService technicalService;
	
	private Technical entity;
	
	private List<Technical> technList;
	
	private String id;
	
	private String type;
	
	private String technicalName;
	
	@Action("totechnicalList")
	public String totechnicalList() {
		if(entity==null) {
			entity = new Technical();
		}
		entity.setPage(this.getPage());
		technList=technicalService.queryListTechnical(entity);
		
		return "technical";
	}

	@Action("addtechnical")
	public String addtechnical() {
		if(entity==null) {
			entity = new Technical();
		}
		
		if("1".equals(entity.getLoginType())) {
			return "addtechnical";
		}
		entity=technicalService.gettoTechnical(entity);
		entity.setLoginType("2");
		return "addtechnical";
	}
	
	/**
	 * 是否启用
	 * @return
	 */
	@Action("startupTechnical")
	public String startupTechnical() {
		
		this.context();
		boolean flag=technicalService.updateTechnicalType(id,type);
		try {
			PrintWriter out = this.getResponse().getWriter();
			out.print(flag);
			out.close();
		} catch (IOException e) {
			e.printStackTrace();
		}
		return null;
	}
	
	/**
	 * 新增、修改
	 * @return
	 */
	@Action("updateTechnical")
	public String updateTechnical() {
		this.context();
		// 当前session
		HttpSession session = getRequest().getSession();
		// 当前用户信息
		UserEntity user = (UserEntity) session.getAttribute("userInfo");
		if("1".equals(entity.getLoginType())) {
			entity.setCreateId(user.getUser_id());
			entity.setCreateTime(new Date());
		}
		entity.setModifyId(user.getUser_id());
		entity.setModifyTime(new Date());
		technicalService.addToUpdateTechnical(entity);
		return totechnicalList();
	}
	
	
	@Action("checkedTechnicalName")
	public String checkedTechnicalName() {
		this.context();
		boolean flag=technicalService.checkedTechnicalName(technicalName);
		try {
			PrintWriter out = this.getResponse().getWriter();
			out.print(flag);
			out.close();
		} catch (IOException e) {
			e.printStackTrace();
		}
		return null;
	}
	public Technical getEntity() {
		return entity;
	}

	public void setEntity(Technical entity) {
		this.entity = entity;
	}

	public List<Technical> getTechnList() {
		return technList;
	}

	public void setTechnList(List<Technical> technList) {
		this.technList = technList;
	}

	public String getTechnicalName() {
		return technicalName;
	}

	public void setTechnicalName(String technicalName) {
		this.technicalName = technicalName;
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}
	
	
	
}
