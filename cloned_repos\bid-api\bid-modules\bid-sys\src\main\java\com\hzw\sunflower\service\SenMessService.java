package com.hzw.sunflower.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hzw.sunflower.controller.condition.TemCondition;

import com.hzw.sunflower.controller.response.TemplateVo;
import com.hzw.sunflower.entity.Template;


/**
 * <AUTHOR>
 * @since 2022/6/15
 */
public interface SenMessService extends IService<Template> {

    /**
    * 短信模板分页
    */
    IPage<TemplateVo> getSmsList(TemCondition req,Long userId);

    /**
     * 短信模板新增和修改
     */
    Boolean addOrUpdateSms(Template template);
}
