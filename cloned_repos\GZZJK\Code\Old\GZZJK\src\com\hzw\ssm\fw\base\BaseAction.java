package com.hzw.ssm.fw.base;

import java.io.IOException;
import java.util.Date;
import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import net.sf.json.JSONObject;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import com.hzw.ssm.fw.util.ExportEntity;
import com.hzw.ssm.fw.util.FileUtil;
import com.hzw.ssm.fw.util.Page;
import com.hzw.ssm.sys.user.entity.UserEntity;
import com.opensymphony.xwork2.ActionSupport;

@Component
@Scope("prototype")
public class BaseAction extends ActionSupport {

	/** 对象序列化时，该对象的唯一标识 */
	private static final long serialVersionUID = 3210302550692616216L;

	/** 日志对象 */
	protected static final Log log = LogFactory.getLog(BaseAction.class);

	/** 初始化返回结果 */
	protected static final String INIT = "init";

	/** 导出字段定义（多个字段用竖线分隔）： 字典英文名/第一行标题,第二行标题/列宽度,左中右/字典项关键字/其他样式 */
	protected String exportFieldStyle = "";

	/** 导出文件名 */
	protected String exportFileName = "";

	/** 浏览器请求对象 */
	private HttpServletRequest request = null;

	/** 浏览器响应对象 */
	private HttpServletResponse response = null;

	/** 前台jsp中弹出操作结果信息的js方法字符串*/
	private String alertMessage="";
	/**分页对象*/
	private Page page;
	
	/** dwz分页使用 */
	private int pageNum = 1;
	private int numPerPage;

	public int getPageNum() {
		return pageNum;
	}

	public void setPageNum(int pageNum) {
		this.pageNum = pageNum;
	}

	public int getNumPerPage() {
		return numPerPage;
	}

	public void setNumPerPage(int numPerPage) {
		this.numPerPage = numPerPage;
	}

	public HttpServletRequest getRequest() {
		return request;
	}

	public void setRequest(HttpServletRequest request) {
		this.request = request;
	}

	public HttpServletResponse getResponse() {
		return response;
	}

	public void setResponse(HttpServletResponse response) {
		this.response = response;
	}

	public String getExportFieldStyle() {
		return exportFieldStyle;
	}

	public void setExportFieldStyle(String exportFieldStyle) {
		this.exportFieldStyle = exportFieldStyle;
	}

	public String getExportFileName() {
		return exportFileName;
	}

	public void setExportFileName(String exportFileName) {
		this.exportFileName = exportFileName;
	}

	public Page getPage() {
		if(page==null)
			page = new Page();
		return page;
	}

	public void setPage(Page page) {
		this.page = page;
	}

	public String open() {
		return SUCCESS;
	}

	/**
	 * 获取request的方法，也可以通过实现ServletRequestAware接口，在拦截器中注入request的方法
	 * 建议不要直接使用request等对象，尽量保持面向对象的编程习惯：action从页面获取相关对象，直接交给service处理
	 */
	public void context() {
		com.opensymphony.xwork2.ActionContext ctx = com.opensymphony.xwork2.ActionContext
				.getContext();
		request = (javax.servlet.http.HttpServletRequest) ctx
				.get(org.apache.struts2.ServletActionContext.HTTP_REQUEST);
		response = (javax.servlet.http.HttpServletResponse) ctx
				.get(org.apache.struts2.ServletActionContext.HTTP_RESPONSE);
	}

	/**
	 * 设置默认的值，如操作用户编号，更新时间戳等
	 * 
	 * @param entity
	 *            信息实体对象
	 * @throws Exception
	 */
	public void setOpaUserAndDate(BaseEntity entity) {
		this.context();

		//当前session
		HttpSession session = getRequest().getSession();
		
		//当前用户信息
		UserEntity info = (UserEntity)session.getAttribute("userInfo");
		
		String userId = info.getUser_id();
		Date opaDate = new Date();
		entity.setCreate_id(userId);
		entity.setCreate_time(opaDate);
		entity.setModify_id(userId);
		entity.setModify_time(opaDate);
		entity.setDelete_flag(0);
	}

	@SuppressWarnings("rawtypes")
	protected String export(List lstEntity) {
		this.context();
		String ret = "";
		try {
			List<ExportEntity> exp = ExportEntity.parse(exportFieldStyle);
			byte[] buffer = ExportEntity.parse(lstEntity, exp, exportFileName);
			ret = FileUtil.download(exportFileName + ".xls", buffer,
					getResponse());
			exportFieldStyle = "";
			exportFileName = "";
		} catch (Exception e) {
			log.info("文件导出出错！");
			e.printStackTrace();
		}
		return ret;
	}

	/**
	 * navTabAjaxDone是DWZ框架中预定义的表单提交回调函数．
	 * 服务器转回navTabId可以把那个navTab标记为reloadFlag=1, 下次切换到那个navTab时会重新载入内容.
	 * callbackType如果是closeCurrent就会关闭当前tab
	 * 只有callbackType="forward"时需要forwardUrl值
	 * navTabAjaxDone这个回调函数基本可以通用了，如果还有特殊需要也可以自定义回调函数. 如果表单提交只提示操作是否成功,
	 * 就可以不指定回调函数. 框架会默认调用DWZ.ajaxDone() <form action="/user.do?method=save" onsubmit="return validateCallback(this, navTabAjaxDone)">
	 * 
	 * form提交后返回json数据结构statusCode=DWZ.statusCode.ok表示操作成功, 做页面跳转等操作.
	 * statusCode=DWZ.statusCode.error表示操作失败, 提示错误原因.
	 * statusCode=DWZ.statusCode.timeout表示session超时，下次点击时跳转到DWZ.loginUrl
	 * {"statusCode":"200", "message":"操作成功", "navTabId":"navNewsLi", "forwardUrl":"", "callbackType":"closeCurrent", "rel"."xxxId"}
	 * {"statusCode":"300", "message":"操作失败"} 
	 * {"statusCode":"301", "message":"会话超时"}
	 * 
	 */
	public void JSonReturn(String statusCode, String message, String navTabId,
			String forwardUrl, Boolean callback) throws IOException {
		this.context();
		JSONObject json = new JSONObject();

		if ("sueess".equals(statusCode)) {
			json.put("statusCode", "200");
			
			if (null != navTabId)
				json.put("navTabId", navTabId);
			else
				json.put("navTabId", navTabId);
			if (null != forwardUrl)
				json.put("forwardUrl", forwardUrl);
			else
				json.put("forwardUrl", forwardUrl);
			if (callback)
				json.put("callbackType", "closeCurrent");
			else
				json.put("callbackType", "");
			
		} else if ("error".equals(statusCode)) {
			json.put("statusCode", "300");
		} else {
			json.put("statusCode", "301");
		}
		if (null != message)
			json.put("message", message);
		else
			json.put("message", "");
		
		this.getResponse().setContentType("text/html;charset=UTF-8");
		this.getResponse().getWriter().write(json.toString());

	}

	public String getAlertMessage() {
		return alertMessage;
	}

	public void setAlertMessage(String alertMessage) {//需要有jquery的支持
		String function="";
		if(alertMessage!=null&&!alertMessage.trim().equals("")){
			function="<script type=\"text/javascript\" defer>$(function(){alert('"+alertMessage+"');})</script>";
		}
		this.alertMessage = function;
	}

}
