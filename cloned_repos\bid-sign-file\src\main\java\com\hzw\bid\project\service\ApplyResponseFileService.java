package com.hzw.bid.project.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hzw.bid.project.controller.response.DecryptFileVo;
import com.hzw.bid.project.model.ApplyResponseFile;
import java.io.File;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR> Gen
 * @since 2022-06-01
 */
public interface ApplyResponseFileService extends IService<ApplyResponseFile> {

    /**
     * 校验pdf签名是否和对应组织机构代码的企业一样
     * @param temp
     * @param organizationNum
     * @return
     */
    Integer verifySignature(File temp, String organizationNum);

    DecryptFileVo caDecrypt(Long ossId, String pwd);


}
