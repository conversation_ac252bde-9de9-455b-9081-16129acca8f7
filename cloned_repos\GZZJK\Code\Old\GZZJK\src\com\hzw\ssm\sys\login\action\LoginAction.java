package com.hzw.ssm.sys.login.action;

import java.io.IOException;
import java.io.PrintWriter;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.regex.PatternSyntaxException;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;

import com.hzw.ssm.sys.project.entity.LoginRecord;
import com.hzw.ssm.sys.project.service.ProjectService;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.struts2.ServletActionContext;
import org.apache.struts2.convention.annotation.Action;
import org.apache.struts2.convention.annotation.Namespace;
import org.apache.struts2.convention.annotation.ParentPackage;
import org.apache.struts2.convention.annotation.Result;
import org.apache.struts2.convention.annotation.Results;
import org.springframework.beans.factory.annotation.Autowired;

import com.hzw.ssm.expert.entity.ExpertInfoEntity;
import com.hzw.ssm.expert.service.ExpertInfoService;
import com.hzw.ssm.fw.base.BaseAction;
import com.hzw.ssm.fw.util.CommUtil;
import com.hzw.ssm.fw.util.DateUtil;
import com.hzw.ssm.fw.util.MD5Util;
import com.hzw.ssm.fw.util.SysConstants;
import com.hzw.ssm.sys.login.service.LoginService;
import com.hzw.ssm.sys.project.entity.ProjectEntity;
import com.hzw.ssm.sys.system.entity.MenuEntity;
import com.hzw.ssm.sys.system.entity.RoleEntity;
import com.hzw.ssm.sys.system.entity.SystemLogEntity;
import com.hzw.ssm.sys.system.service.RoleService;
import com.hzw.ssm.sys.system.service.SystemLogService;
import com.hzw.ssm.sys.user.entity.HomeQrEntity;
import com.hzw.ssm.sys.user.entity.UserEntity;
import com.hzw.ssm.sys.user.service.UserService;
import com.hzw.ssm.util.empty.EmptyUtils;
import com.hzw.ssm.util.encrypt.Base64Util;
import com.hzw.ssm.util.http.token.JwtUtil;
import com.opensymphony.xwork2.ActionContext;

@Namespace("/login")
@ParentPackage(value = "default")
@Results({ @Result(name = "access", location = "/jsp/login.jsp"),
		@Result(name = "access1", location = "/jsp/login1.jsp"),
		@Result(name = "index",location = "/jsp/index.jsp"),
		@Result(name = "quit", type = "redirect", location = "access"),
		@Result(name = "index1", type = "redirect", location = "index")})
public class LoginAction extends BaseAction {

	private static final long serialVersionUID = -8823858495799309882L;

	/** 用户实体类 */
	private UserEntity userBean;
	/** 提示信息 */
	private String message;
	/** 随机码 */
	private String loginCode;
	/** cookie失效时长（秒） */
	private int cookie_time = 3600;
	/** cookie 路径 */
	private String cookie_path = "/JSJX";
	/** cookie 帐号标识 */
	private String cookie_code = "jsjx_code";
	/** cookie 密码标识 */
	private String cookie_pw = "jsjx_pw";

	/** 登录限制 次数 */
	private final static Integer LOGIN_COUNT_LIMIT = 5;
	/** 登录限制 时长间隔：分钟 */
	private final static Integer LOGIN_TIME_LIMIT = 30;
	/** tookencookie时间 */
	private final static Integer TOKEN_COOKIE_TIME_OUT = 4* 60* 60;

	private List<MenuEntity> menus;

	private SystemLogEntity logEntity = new SystemLogEntity();
	// 专家注册人数
	private Integer registerNum = 0;
	// 专家修改审核
	private Integer modifyNum = 0;
	// 指定专家审核人数
	private Integer designatedExpertsNum = 0;

	private String username;
	private String password;
	private HomeQrEntity entity;
	@Autowired
	private LoginService loginService;
	@Autowired
	private ProjectService projectService;
	@Autowired
	private UserService userService;

	@Autowired
	private SystemLogService logService;

	@Autowired
	private ExpertInfoService expertService;

	@Autowired
	private RoleService roleService;

	/**
	 * 函数功能描述：进入登录页面
	 *
	 * @return
	 */
	@Action("/access")
	public String access() {
		// 进入登录页
		return "access";
	}
	/**
	 * 函数功能描述：进入登首页页面
	 *
	 * @return
	 */
	@Action("/index")
	public String index() {
		// 进入登录页
		// 1.获取Session
		HttpSession session = getSessionObject();
		userBean=(UserEntity)session.getAttribute("userInfo");
		
		try {
			loginSuccess(session);
			query();
		} catch (Exception e) {
			log.error("登录失败", e);
		}

		return "index";
	}
	
	private void query() {
		if(!"20141010095817859054".equals(userBean.getRole_id())) {
		entity = userService.selectUserRecomend(userBean.getUser_id());
		 if(entity.getReferralsSucCount()!=0&& entity.getReferralsCount()!=0) {
			 
			 DecimalFormat decimalFormat= new DecimalFormat( "0.000" ); //构造方法的字符格式这里如果小数不足2位,会以0补足.
			 float  avg = Float.valueOf(entity.getReferralsSucCount())/Float.valueOf(entity.getReferralsCount());
			 //float  avg = (float) (entity.getReferralsSucCount()/entity.getReferralsCount());
			 String p=decimalFormat.format(avg);
			 entity.setAvgrefer(p);
			 }else {
			 entity.setAvgrefer("0.0000");
		 }
		 }
	}
	/**
	 * 函数功能描述：进入登录页面
	 *
	 * @return
	 */
	@Action("/access1")
	public String access1() {
		// 进入登录页1
		return "access1";
	}

	/**
	 * 函数功能描述：登录操作
	 *
	 * @return urlKey String
	 */
/*	@Action("/enter")
	public String enter() {
		// 默认进入登录页
		String urlKey = "access";
		try {
			// 1.获取Session
			HttpSession session = getSessionObject();

			boolean flag = true;
			// 2.参数非空校验（需要校验验证码）
			boolean paramsFlag = checkParams(session, true);
			if (!paramsFlag) {
				flag = false;
			}

			// 3.登录校验
			if (flag) {
				boolean loginFlag = checkLogin(session);
				if (!loginFlag) {
					flag = false;
				}
			}

			// 4.登录成功处理
			if (flag) {
				loginSuccess(session);
				// 登录成功后进入首页
				urlKey = "index";
			}
		} catch (Exception e) {
			log.error("登录失败", e);
		}

		return urlKey;
	}*/

	/**
	 * 函数功能描述：登录操作
	 *
	 * @return urlKey String
	 */
	@Action("/enter1")
	public String enter1() {
		// 默认进入登录页
		String urlKey = "access";
		try {
			if (null==userBean){
				return urlKey;
			}
			// 1.获取Session
			HttpSession session = getSessionObject();
			// 2.参数非空校验（需要校验验证码）
			boolean flag = checkParamsForSms(session);
			// 3.登录校验
			if (flag) {
				flag = checkSmsLogin(session);
				// 4.登录成功处理
				if (flag) {
					loginSuccess(session);
					// 登录成功后进入首页
					urlKey = "index1";
				}
			}
			//保存登陆信息
			saveLoginInfo(userBean,flag);
		} catch (Exception e) {
			log.error("登录失败", e);
		}

		return urlKey;
	}
	/*@SuppressWarnings("unchecked")
	@Action("check")
	public void check(){
		PrintWriter out = null;
		try {
			 out = this.getResponse().getWriter();
			// 1.获取Session
			HttpSession session = getSessionObject();
			// 2.参数非空校验（需要校验验证码）
			boolean flag = checkParamsForSms(session);
			// 3.登录校验
			if (flag) {
				flag = checkSmsLogin(session);
				// 4.登录成功处理
				if (flag) {
					loginSuccess(session);
					// 登录成功后进入首页
//					urlKey = "success";
					out.print("success");
				}
			}
			//保存登陆信息
			saveLoginInfo(userBean,flag);
			//如果登录失败，则不再重新加载页面
			if (!flag){
				out.print("error");
			}
		} catch (Exception e) {
			log.error("登录失败", e);
			out.print("error");
		}
	}*/
	/**
	 * 保存登陆信息
	 */
	public LoginRecord saveLoginInfo(UserEntity userBean,boolean flag){
		LoginRecord loginRecord = new LoginRecord();
		//id
		loginRecord.setId(String.valueOf(System.currentTimeMillis()));
		//用户编号
		loginRecord.setUser_id(userBean.getUser_id());
		//用户名
		loginRecord.setLogin_name(userBean.getLogin_code());
		//登陆时间
		loginRecord.setLogin_time(new Date());
		loginRecord.setCreate_name(userBean.getUser_id());
		loginRecord.setCreate_time(new Date());
		//登陆状态
		if (flag==true){
			loginRecord.setLogin_status(1);
		}else{
			loginRecord.setLogin_status(0);
		}
		projectService.saveLoginInfo(loginRecord);
		return loginRecord;
	}
	/**
	 * 函数功能描述：登录操作
	 * 
	 * @return urlKey String
	 */
	@Action("/sendSmsCode")
	public String sendSmsCode() {
		PrintWriter out = null;
		// 判断手机号是否正确
		HttpServletRequest request = ServletActionContext.getRequest();
		try {
			this.context();
			this.getResponse().setCharacterEncoding("utf-8");
			out = this.getResponse().getWriter();
			String loginCodeStr = request.getParameter("login_code");
			if (EmptyUtils.isEmpty(loginCodeStr) || EmptyUtils.isEmpty(loginCodeStr.trim())) {
				this.setMessage("请输入用户名");
				out.print(this.getMessage());
				out.close();
				return null;
			}
			UserEntity loginUser = userService.queruser(loginCodeStr);
			if (loginUser == null) {
				this.setMessage("该账号不存在");
				out.print(this.getMessage());
				out.close();
				return null;
			}
			String phoneNumber = loginUser.getMobile();
			if (EmptyUtils.isEmpty(phoneNumber) || EmptyUtils.isEmpty(phoneNumber.trim()) || !isChinaPhoneLegal(phoneNumber)) {
				this.setMessage("手机号有误，请联系客服处理4000580203！");
				out.print(this.getMessage());
				out.close();
				return null;
			}
			Date now = new Date();
			//最后一次登录失败时间
			Date loginFailTime = loginUser.getLogin_fail_time();
			Integer count = loginUser.getLogin_fail_count();
			if(loginFailTime != null && now.getTime() - loginFailTime.getTime() > 0 && now.getTime() - loginFailTime.getTime() < 1800000){
				if (count >= 3){
					this.setMessage("您的账号已锁定，请30分钟后重试，若有疑问请联系人工客服4000580203");
					out.print(this.getMessage());
					out.close();
					return null;
				}
			}
			// 对比验证码的发送时间是否在三分钟以内
			String smscode = loginUser.getSms_code();
			Date smsdate = loginUser.getSms_time();
			if (!EmptyUtils.isEmpty(smscode)&& smsdate != null) {
				Long time = now.getTime() - smsdate.getTime();
				if (time > 0 && time < 180000) {
					this.setMessage("您操作过于频繁，请稍后重试");
					out.print(this.getMessage());
					out.close();
					return null;
				}
			}
			// 将短信发送的验证码存入库中，登陆做匹配
			smscode = loginService.sendSMs(loginUser);
			if (!EmptyUtils.isEmpty(smscode)) {
				//System.out.println("---------------------smscode:"+smscode);
				smscode = MD5Util.reverse(smscode);
				loginUser.setSms_code(smscode);
				loginUser.setSms_time(new Date());
				loginUser.setLogin_fail_count(new Integer("0"));
				userService.updatePersonnalUser(loginUser);
				this.setMessage("若您3分钟内未收到验证码，请联系人工客服4000580203Aa");
			} else {
				this.setMessage("发送短信失败，请及时处理！");
			}

		} catch (Exception e) {
			log.error("获取手机验证码失败", e);
			e.printStackTrace();
			out.print("error");
		}
		out.print(this.getMessage());
		out.close();
		return null;
	}

	public boolean isChinaPhoneLegal(String str) throws PatternSyntaxException {
		// ^ 匹配输入字符串开始的位置
		// \d 匹配一个或多个数字，其中 \ 要转义，所以是 \\d
		// $ 匹配输入字符串结尾的位置
		String regExp = "^((13[0-9])|(14[5,7,9])|(15[0-3,5-9])|(166)|(17[3,5,6,7,8])" + "|(18[0-9])|(19[8,9]))\\d{8}$";
		Pattern p = Pattern.compile(regExp);
		Matcher m = p.matcher(str);
		return m.matches();
	}

	/**
	 * 函数功能描述：内部跳转登录入口
	 * 
	 * @return urlKey String
	 */
	@Action("/innerEnter")
	public String innerEnter() {
		// 默认进入登录页
		String urlKey = "access";
		try {
			// 1.获取Session
			HttpSession session = getSessionObject();

			boolean flag = true;
			// 2.参数非空校验（无需校验验证码）
			boolean paramsFlag = checkParams(session, false);
			if (!paramsFlag) {
				flag = false;
			}

			// 3.登录校验
			if (flag) {
				boolean loginFlag = checkLogin(session);
				if (!loginFlag) {
					flag = false;
				}
			}
			// 4.登录成功处理
			if (flag) {
				loginSuccess(session);
				// 登录成功后进入首页
				urlKey = "index";
			}
		} catch (Exception e) {
			log.error("登录失败", e);
		}

		return urlKey;
	}

	/**
	 * 函数功能描述：获取Session对象
	 * 
	 * @return session HttpSession
	 */
	private HttpSession getSessionObject() {
		// 获取request
		this.context();
		// 获取Session
		HttpSession session = getRequest().getSession();

		return session;
	}

	/**
	 * 函数功能描述：登录参数非空校验
	 * 
	 * @param captchaFlag
	 *            验证码校验开关：true.开启校验、false.关闭校验
	 * @return flag Boolean 参数校验结果：true.校验通过、false.校验未通过
	 */
	private Boolean checkParams(HttpSession session, boolean captchaFlag) {
		boolean flag = true;

		// 获取登录名及密码
		String loginCodeStr = userBean.getLogin_code();
		if (EmptyUtils.isEmpty(loginCodeStr) || EmptyUtils.isEmpty(loginCodeStr.trim())) {
			flag = false;
			this.setMessage("请填写登录名");
		} else {
			userBean.setLogin_code(loginCodeStr.trim());
		}

		String passwordStr = userBean.getPassword().trim();
		if (EmptyUtils.isEmpty(passwordStr) || EmptyUtils.isEmpty(passwordStr.trim())) {
			flag = false;
			this.setMessage("请填写密码");
		} else {
			userBean.setPassword(passwordStr.trim());
		}

		// 需要校验验证码时则校验验证码
		if (captchaFlag) {
			if (EmptyUtils.isEmpty(loginCode) || EmptyUtils.isEmpty(loginCode.trim())) {
				flag = false;
				this.setMessage("请填写验证码");
			} else {
				// 获取验证码
				String captcha = (String) session.getAttribute("securityCode");
				// 校验码验证
				if (!(captcha).equals(loginCode.trim())) {
					flag = false;
					this.setMessage("验证码填写错误");
				}
			}
		}

		return flag;
	}

	/**
	 * 函数功能描述：登录参数非空校验
	 * 
	 * @param session
	 *            
	 * @return flag Boolean 参数校验结果：true.校验通过、false.校验未通过
	 */
	private Boolean checkParamsForSms(HttpSession session) {
		if (null==userBean){
			userBean=new UserEntity();
		}
		// 获取登录名及密码
		String loginCodeStr = userBean.getLogin_code();
		if (EmptyUtils.isEmpty(loginCodeStr) || EmptyUtils.isEmpty(loginCodeStr.trim())) {
			this.setMessage("请输入用户名");
			return false;
		} else {
			userBean.setLogin_code(loginCodeStr.trim());
		}
		
		String smsCodeStr = userBean.getSms_code();
		if (EmptyUtils.isEmpty(smsCodeStr) || EmptyUtils.isEmpty(smsCodeStr.trim())) {
			this.setMessage("请输入验证码");
			return false;
		} else {
			userBean.setSms_code(smsCodeStr.trim());
		}
		return true;
	}

	/**
	 * 函数功能描述：登录信息校验
	 * 
	 * @param session
	 *            HttpSession
	 * @return flag Boolean true.校验通过（登录名和密码均正确）、false.校验未通过
	 */
	private Boolean checkLogin(HttpSession session) {
		boolean flag = true;

		try {
			// 获取登录名并对登录名进行Base64解码
			String loginCodeStr = userBean.getLogin_code();
			String loginCodeValue = Base64Util.base64Decode(loginCodeStr, "UTF-8");
			// 获取密码并对密码进行Base64解码，解码后统一转换成大写
			String passwordStr = userBean.getPassword();
			String passwordValue = Base64Util.base64Decode(passwordStr, "UTF-8").toUpperCase();

			// 查询用户：匹配登录名和密码
			UserEntity userInfo = loginService.login(loginCodeValue, passwordValue, 1);
			if (EmptyUtils.isEmpty(userInfo)) {
				// 根据登录名未查询到用户
				flag = false;
				this.setMessage("登录名或密码错误，请确认后重新填写");
			} else {
				// 校验用户密码
				if (userInfo.getPassword().equals(passwordValue)) {

					Integer loginFailCount = userInfo.getLogin_fail_count();

					if (loginFailCount == null) {
						loginFailCount = 0;
					}

					if (loginFailCount == 5) {
						Date loginFailTime = userInfo.getLogin_fail_time();
						Date currTime = new Date();

						long betTime = DateUtil.getMinutes(currTime, loginFailTime);
						if (betTime < LOGIN_COUNT_LIMIT) {
							this.setMessage(
									"您已登录失败" + LOGIN_COUNT_LIMIT + "次，请" + (LOGIN_TIME_LIMIT - betTime) + "分钟后重新登录");
							return false;
						}
					}

					// 密码匹配一致时，验证用户状态
					if (SysConstants.EnableFlag.ENABLE.equals(userInfo.getEnabled_flag())) {
						// 用户状态正常时，标记登录状态：登录成功（已登录）
						userInfo.setLoginStatus(true);
						// 登录成功后，返回用户信息
						userBean = userInfo;
					} else {
						// 用户状态异常，标记登录状态：登录失败（未登录）
						flag = false;
						userInfo.setLoginStatus(false);
						this.setMessage("您的账号已被禁用，请联系管理人员");
					}
				} else {
					// 密码匹配不一致
					flag = false;
					// 标记登录状态：登录失败（未登录）
					userInfo.setLoginStatus(false);
					this.setMessage("登录名或密码错误，请确认后重新填写");

					// 进行登录失败限制校验及处理
					boolean limitFlag = checkLimit(userInfo);
				}
			}
		} catch (Exception e) {
			flag = false;
			log.error("登录信息校验失败", e);
		}

		return flag;
	}

	/**
	 * 函数功能描述：登录信息校验
	 * 
	 * @param session
	 *            HttpSession
	 * @return flag Boolean true.校验通过（登录名和密码均正确）、false.校验未通过
	 */
	private Boolean checkSmsLogin(HttpSession session) {
		try {
			// 获取登录名并对登录名进行Base64解码
			String loginCodeStr = userBean.getLogin_code();
			String loginCodeValue = Base64Util.base64Decode(loginCodeStr, "UTF-8");
			// 获取密码并对密码进行Base64解码，解码后统一转换成大写
			String smsCodeStr = userBean.getSms_code();
			String smsCodeValue = Base64Util.base64Decode(smsCodeStr, "UTF-8");

			userBean = userService.queruser(loginCodeValue);
			//登录失败次数
			Integer loginFailCount = userBean.getLogin_fail_count();
			if(loginFailCount == null){
				loginFailCount = 0;
			}
			//最后一次登录失败时间
			Date loginFailTime = userBean.getLogin_fail_time();
			//发送时间，有效期三分钟
			Date sendTime = userBean.getSms_time();
			if(EmptyUtils.isEmpty(userBean.getSms_code())){
				this.setMessage("验证码已过期，请重新获取验证码");
				return false;
			}
			if(sendTime == null){
				this.setMessage("验证码已过期，请重新获取验证码");
				return false;
			}
			Date now = new Date();
			if(now.getTime() - sendTime.getTime() > 180000){
				this.setMessage("验证码已过期，请重新获取验证码");
				return false;
			}
			if(loginFailCount >= 3){
				if(now.getTime() - loginFailTime.getTime() > 1800000){//用户被锁定，且锁定时间大于30分钟
					if(MD5Util.reverse(smsCodeValue).equals(userBean.getSms_code())){
						return true;
					}else{
						userBean.setLogin_fail_time(now);
						userBean.setLogin_fail_count(new Integer("1"));
						userService.updatePersonnalUser(userBean);
						this.setMessage("信息有误，若有疑问请联系人工客服4000580203，连续3次登录失败将锁定30分钟");
						return false;
					}
				}else if(now.getTime() - loginFailTime.getTime() > 0 && now.getTime() - loginFailTime.getTime() < 1800000){
					//用户被锁定，且锁定时间小于30分钟
					this.setMessage("您的账号已锁定，请30分钟后重试，若有疑问请联系人工客服4000580203");
					return false;
				}
			} else {
				if(MD5Util.reverse(smsCodeValue).equals(userBean.getSms_code())){
					return true;
				}else {
					userBean.setLogin_fail_time(now);
					userBean.setLogin_fail_count(++loginFailCount);
					userService.updatePersonnalUser(userBean);
					this.setMessage("信息有误，若有疑问请联系人工客服4000580203，连续3次登录失败将锁定30分钟");
					return false;
				}
			}

		} catch (Exception e) {
			log.error("登录信息校验失败", e);
			this.setMessage("信息有误，若有疑问请联系人工客服4000580203");
			return false;
		}
		return true;
	}

	/**
	 * 函数功能描述：校验登录失败限制
	 * 
	 * @param user
	 *            UserEntity
	 * @return flag Boolean true.校验通过、false.校验未通过
	 */
	private Boolean checkLimit(UserEntity user) {
		boolean flag = true;
		/*
		 * 登录失败处理机制： 1、登录失败后将记录最新的登录失败时间并累加登录失败次数（默认：初始化0次）
		 * 1.1、如果是首次登录失败（次数为0），则记录登录失败时间并增加1次登录失败次数； 1.2、非首次登录失败：
		 * 1.2.1、如果距上次登录失败时间不足1小时的：
		 * 1.2.1.1、如果登录失败次数未达到限制值（默认是5次），则更新登录失败时间，并累加登录失败次数；
		 * 1.2.1.2、如果登录失败次数已达到限制值（默认是5次），则在自上次登录失败时间之时起的一段时间（默认1小时）内限制该账号登录，
		 * 在此期间，用户登录时只提示“您的账号于YYYY年MM月DD日 HH时mm分已累计登录失败X次，请于Y小时后重新登录”，
		 * 而不更新登录失败时间也不累加失败次数；
		 * 1.2.2、只要距上次登录失败时间超过1小时的（不管已经登录失败几次），则更新登录失败时间，并重置登录失败次数（按第1次失败计）；
		 * 2、只要登录成功，将初始化登录失败次数（默认：初始化0次），并置空登录失败时间。
		 */
		Integer loginFailCount = user.getLogin_fail_count();
		if (EmptyUtils.isEmpty(loginFailCount)) {
			// 登录失败次数初始化
			loginFailCount = 0;
		}

		// 判断是否是首次登录失败
		Date loginFailTime = user.getLogin_fail_time();
		// 当前时间
		Date currTime = new Date();
		userBean.setUser_id(user.getUser_id());
		if (0 == loginFailCount || EmptyUtils.isEmpty(loginFailTime)) {
			flag = false;
			// 首次登录失败，记录登录失败时间并增加1次登录失败次数
			loginFailCount++;
			saveLoginFailedInfo(loginFailCount, currTime);
		} else {
			long minutes = DateUtil.getMinutes(currTime, loginFailTime);
			if (LOGIN_TIME_LIMIT > minutes) {
				// 距上次登录失败时间不足1小时
				if (loginFailCount < LOGIN_COUNT_LIMIT) {
					flag = false;
					// 登录失败次数未达到限制值（默认是5次），则更新登录失败时间，并累加登录失败次数；
					loginFailCount++;
					saveLoginFailedInfo(loginFailCount, currTime);
				} else {
					// 录失败次数已达到限制值（默认是5次），则在自上次登录失败时间之时起的一段时间（默认1小时）内限制该账号登录，
					flag = false;
					// String timeStr = DateUtil.getFormatDateTime("yyyy年MM月dd日
					// HH时mm分", loginFailTime);
					// 此处会覆盖掉密码错误的提示信息
					this.setMessage("您已登录失败" + LOGIN_COUNT_LIMIT + "次，请" + (LOGIN_TIME_LIMIT - minutes) + "分钟后重新登录");
				}
			} else {
				// 距上次登录失败时间超过1小时
				flag = false;
				// （不管已经登录失败几次，但不会超过限制值），则更新登录失败时间，并重置登录失败次数（按第1次失败计）
				saveLoginFailedInfo(1, currTime);
			}
		}

		return flag;
	}

	/**
	 * 函数功能描述：记录登录失败信息
	 * 
	 * @param loginFailCount
	 *            int
	 * @param currTime
	 *            Date
	 */
	private void saveLoginFailedInfo(int loginFailCount, Date currTime) {
		userBean.setLogin_fail_count(loginFailCount);
		userBean.setLogin_fail_time(currTime);
		loginService.loginSet(userBean);
	}

	/**
	 * 函数功能描述：登录成功处理
	 * 
	 * @param session
	 *            HttpSession
	 */
	private void loginSuccess(HttpSession session) throws Exception {
		// 获取登录名：为了与全局变量中定义的验证码（loginCode）区分开来，此处使用login_code命名
		String login_code = userBean.getLogin_code();
		// 方便返回登录名回填到表单中
		this.getRequest().setAttribute("longCode", login_code);
		// 登录名和密码验证成功后保存密码
		this.getRequest().setAttribute("longPwd", userBean.getPassword());

		// 重置登录失败信息
		loginService.loginPass(userBean.getUser_id());

		// 保存角色信息
		RoleEntity re = roleService.getRoleInfoById(userBean.getRole_id());
		if (!EmptyUtils.isEmpty(re)) {
			userBean.setRole_name(re.getRoleName());
		}

		// 用户信息存入session
		session.setAttribute("userInfo", userBean);
		//判断当前用户是否是专家
//		int n=userService.checkExtract(userBean.getUser_id());
		//专家
		/*if (n>0) {
			//一级菜单
			List<String> menuUrlList=userService.selectUserJurisdiction(userBean.getUser_id());
			//二级菜单
			//查看
			menuUrlList.add("expertJoinProject/toProjectDetail");
			menuUrlList.add("/login/getTime");
			//密码修改 提交
			menuUrlList.add("changepassword/execute");
			//信息维护 提交
			menuUrlList.add("expertInfoApply/saveExpertInfo");
			//申请资料 提交
			menuUrlList.add("expertMaintain/saveExpertInfo");
			session.setAttribute("menuUrlList", menuUrlList);
		}*/
		// 全局变量设置
		ActionContext ac = ActionContext.getContext();
		Map<String, Object> applicationMap = ac.getApplication();
		applicationMap.remove(login_code);
		applicationMap.put(login_code, session);
		ac.setApplication(applicationMap);

		// cookie设置
		getResponse().setContentType("text/html;charset=utf-8");
		Cookie nameCookie = new Cookie(cookie_code, login_code);
		nameCookie.setMaxAge(cookie_time);
		nameCookie.setPath(cookie_path);
		getResponse().addCookie(nameCookie);

		Cookie passwordCookie = new Cookie(cookie_pw, userBean.getPassword());
		passwordCookie.setMaxAge(cookie_time);
		passwordCookie.setPath(cookie_path);
		getResponse().addCookie(passwordCookie);

		// 返回token
		String token = JwtUtil.sign(login_code);
		if (token != null) {
			Cookie tokenCookie = new Cookie(login_code + "-accessToken", token);
			// tokencookie设置超时时间15分钟
			tokenCookie.setMaxAge(TOKEN_COOKIE_TIME_OUT);
			tokenCookie.setPath("/");
			this.getResponse().addCookie(tokenCookie);
		}

		// 登录日志
		logEntity.setLOG_ID(CommUtil.getKey());
		logEntity.setOPA_USER(userBean.getUser_name());
		logEntity.setOPA_IP(getRequest().getRemoteAddr());
		logEntity.setOPA_FUN("/login/enter");
		logEntity.setOPA_METHOD("系统登录");
		this.setOpaUserAndDate(logEntity);
		logService.saveSystemLog(logEntity);

		// 初始化信息
		initData(session);
	}

	/**
	 * 函数功能描述：初始化登录信息
	 *
	 * @param session
	 *            HttpSession
	 */
	private void initData(HttpSession session) throws Exception {
		try {
			UserEntity info = (UserEntity) session.getAttribute("userInfo");
			String roleId = info.getRole_id();
			// 查询当前用户角色所对应的菜单
			menus = loginService.getMenuMapOfAuth(roleId);

			// 过滤该角色的菜单（主要是针对专家）
			filterUserMenus(info, menus);

			// 统计各种状态专家的数量
			countExpert(menus, roleId);
			//判断当前登录用户是否是专家
			if (SysConstants.ROLE_ID.EXPERT.equals(roleId)
					|| SysConstants.ROLE_NAME.EXPERT.equals(info.getRole_name())) {
				//获取专家可访问地址
				queryExpertUrl(menus, session);
			}
		} catch (Exception e) {
			log.error("初始化登录信息失败", e);
			throw new Exception("过滤用户菜单失败", e);
		}
	}

	/**
	 * 获取专家可访问地址
	 * @param menus
	 * @param session
	 */
	public void queryExpertUrl(List<MenuEntity> menus,HttpSession session){
		List<String> menuUrlList=new ArrayList<>();
		for (MenuEntity menuEntity:menus){
			if (StringUtils.isNotBlank(menuEntity.getMenu_url())) {
				menuUrlList.add(menuEntity.getMenu_url());
			}
		}
		//二级菜单
		//查看
		menuUrlList.add("/expertJoinProject/toProjectDetail");
		menuUrlList.add("/login/getTime");
		//选择
		menuUrlList.add("/expertInfoApply/querySpecialtyInfoList");
		//密码修改 提交
		menuUrlList.add("/changepassword/execute");
		//信息维护 提交
		menuUrlList.add("/expertInfoApply/saveExpertInfo");
		//申请资料 提交
		menuUrlList.add("/expertMaintain/saveExpertInfo");
		session.setAttribute("menuUrlList", menuUrlList);
	}
	/**
	 * 函数功能描述：过滤用户菜单
	 * 
	 * @param user
	 *            UserEntity
	 * @param menus
	 *            List
	 */
	private void filterUserMenus(UserEntity user, List<MenuEntity> menus) throws Exception {
		try {
			// 获取角色名
			String roleName = user.getRole_name();
			// 判断角色是否是专家
			if (SysConstants.ROLE_ID.EXPERT.equals(user.getRole_id())
					|| SysConstants.ROLE_NAME.EXPERT.equals(roleName)) {
				// 根据userId查询专家信息
				ExpertInfoEntity queryExpert = new ExpertInfoEntity();
				queryExpert.setUser_id(user.getUser_id());
				ExpertInfoEntity expert = expertService.getExpertInfoByUserIdOrId(queryExpert);

				// 遍历菜单
				for (int i = 0; i < menus.size(); i++) {
					MenuEntity menu = menus.get(i);
					String menuId = menu.getMenu_id();
					String parentMenuId = menu.getParent_menu_id();

					if (EmptyUtils.isEmpty(expert)
							|| (!EmptyUtils.isEmpty(expert.getStatus()) && expert.getStatus() < 3)) {
						// 未查询到专家信息或者是入库审批通过前的专家：TODO 专家状态推测：1.新注册（待审批）、2.审批未通过
						if (SysConstants.CommMenus.WORK.equals(menuId) && "-1".equals(parentMenuId)) {
							// 日常工作菜单：去除菜单（含子菜单）
							menus.remove(menu);
							i = i - 1;
							continue;
						} else if (SysConstants.CommMenus.ACCOUNT.equals(menuId) && "-1".equals(parentMenuId)) {
							// 账户信息管理
							List<MenuEntity> childMenus = menu.getSubMenuList();
							// 遍历“账户信息管理”菜单下的子菜单
							for (int j = 0; j < childMenus.size(); j++) {
								MenuEntity child = childMenus.get(j);
								if (SysConstants.CommMenus.INFO.equals(menuId)) {
									// 基本信息维护：去除菜单
									childMenus.remove(child);
									j--;
									// 结束遍历“账户信息管理”菜单下子菜单的循环
									break;
								}
							}
						}
					} else {
						// 是入库审批通过后的专家（状态：正常或暂停）
						if (SysConstants.CommMenus.ACCOUNT.equals(menuId) && "-1".equals(parentMenuId)) {
							// 账户信息管理
							List<MenuEntity> childMenus = menu.getSubMenuList();
							// 遍历“账户信息管理”菜单下的子菜单
							for (int j = 0; j < childMenus.size(); j++) {
								MenuEntity child = childMenus.get(j);
								if (SysConstants.CommMenus.APPLY.equals(menuId)) {
									// 填写申请资料：去除菜单
									childMenus.remove(child);
									j--;
									// 结束遍历“账户信息管理”菜单下子菜单的循环
									break;
								}
							}
						}
					}
				}
			}
		} catch (Exception e) {
			log.error("过滤用户菜单失败", e);
			throw new Exception("过滤用户菜单失败", e);
		}
	}

	/**
	 * 函数功能描述：统计各种状态专家的数量
	 * 
	 * @param menus
	 *            List
	 * @param roleId
	 *            String
	 */
	private void countExpert(List<MenuEntity> menus, String roleId) throws Exception {
		try {
			List<ExpertInfoEntity> list = null;
			String status = null;
			// 遍历菜单
			for (MenuEntity menu : menus) {
				String menuUrl = menu.getMenu_url();
				if (("/expertInfoAudit/expertInfoAuditList").equals(menuUrl)) {
					// 准入审批
					if (SysConstants.ROLE_ID.AUDITOR.equals(roleId)) {
						// 审核员角色：查询所有注册待审核的专家
						status = SysConstants.EXPERT_STATUS.NEW.toString();
					}
					if (SysConstants.ROLE_ID.DIRECTOR.equals(roleId)) {
						// 机电交易中心主任：查询所有注册待复核的专家
						status = SysConstants.EXPERT_STATUS.NEW_AUDIT.toString();
					}

					if (!EmptyUtils.isEmpty(status)) {
						// 查询注册专家
						list = loginService.queryRegisterExpert(status);
						if (!EmptyUtils.isEmpty(list)) {
							registerNum = list.size();
						}
					}
					continue;
				} else if (("/expertModifyAudit/expertModifyAuditList").equals(menuUrl)) {
					// 修改审批
					if (SysConstants.ROLE_ID.AUDITOR.equals(roleId)) {
						// 审核员角色：查询所有修改待审核的专家
						status = SysConstants.EXPERT_STATUS.MODIFY.toString();
					}
					if (SysConstants.ROLE_ID.DIRECTOR.equals(roleId)) {
						// 机电交易中心主任：查询所有修改待复核的专家
						status = SysConstants.EXPERT_STATUS.MODIFY_AUDIT.toString();
					}
					if (!EmptyUtils.isEmpty(status)) {
						// 查询修改专家
						list = loginService.queryRegisterExpert(status);
						if (!EmptyUtils.isEmpty(list)) {
							modifyNum = list.size();
						}
					}
					continue;
				} else if ("/appointAudit/queryProjectList".equals(menuUrl)) {
					// 指定专家审核
					ProjectEntity project = new ProjectEntity();
					if (SysConstants.ROLE_ID.AUDITOR.equals(roleId)
							|| SysConstants.ROLE_ID.COMMISSIONER.equals(roleId)) {
						// 审核人或业务处室处长
						project.setDepartment(userBean.getDepartment());
						project.setCreate_id(userBean.getUser_id());
						project.setStatus(SysConstants.PROJECT_STATUS.APPOINT_WAIT_AUDIT);
						designatedExpertsNum = loginService.queryProjectCount(project).intValue();
						if (SysConstants.ROLE_ID.COMMISSIONER.equals(roleId)) {
							registerNum = -1;
						}
					} else if (SysConstants.ROLE_ID.DIRECTOR.equals(roleId)) {
						// 机电交易中心主任
						project.setStatus(SysConstants.PROJECT_STATUS.APPOINT_AUDIT_PASS);
						designatedExpertsNum = loginService.queryProjectCount(project).intValue();
					}
				}
			}

			this.getResponse().getWriter().write(designatedExpertsNum + ":" + registerNum + ":" + modifyNum);
		} catch (Exception e) {
			log.error("统计各种状态专家的数量失败", e);
			throw new Exception("统计各种状态专家的数量失败", e);
		}
	}

	/**
	 * 注销
	 */
	@Action("logout")
	public String logout() throws IOException {
		this.context();
		// 当前session
		HttpSession session = getRequest().getSession();
		// 当前用户信息
		UserEntity info = (UserEntity) session.getAttribute("userInfo");
		// 服务端application
		ActionContext ac = ActionContext.getContext();
		Map applicationMap = ac.getApplication();
		// 将用户信息从服务端application移除
		applicationMap.remove(session);
		// 登出日志
		if (info != null) {
			logEntity.setLOG_ID(CommUtil.getKey());
			logEntity.setOPA_USER(info.getUser_name());
			logEntity.setOPA_IP(getRequest().getRemoteAddr());
			logEntity.setOPA_FUN("/login/logout");
			logEntity.setOPA_METHOD("系统注销");
			this.setOpaUserAndDate(logEntity);
			logService.saveSystemLog(logEntity);
		}

		// session销毁
		session.invalidate();
		// 销毁cookie
		Cookie[] cookies = getRequest().getCookies();
		if (cookies != null && cookies.length > 0) {
			for (Cookie cookie : cookies) {
				String code = cookie.getName();
				if (code.equals(cookie_pw) || code.equals(cookie_code)) {
					cookie.setMaxAge(0);
					cookie.setPath(cookie_path);
					getResponse().addCookie(cookie);
				} else {
					continue;
				}

			}
		}

		return "quit";
	}

	@Action("getTime")
	public Date getTime() {
		this.context();
		this.getResponse().setCharacterEncoding("utf-8");
		PrintWriter out = null;
		try {
			out = this.getResponse().getWriter();
			String time = DateUtil.getFormatDateTime("yyyy-MM-dd HH:mm:ss", new Date());
			out.print(time);
		} catch (IOException e) {
			e.printStackTrace();
		}
		out.close();
		return null;
	}

	/**
	 * 初始化待办消息提示
	 * 
	 * @return
	 */
	@Action("initMessage")
	public String initMessage() throws Exception {
		this.context();
		// 当前session
		HttpSession session = getRequest().getSession();

		initData(session);

		return null;
	}

	public UserEntity getUserBean() {
		return userBean;
	}

	public void setUserBean(UserEntity userBean) {
		this.userBean = userBean;
	}

	public String getMessage() {
		return message;
	}

	public void setMessage(String message) {
		this.message = message;
	}

	public String getLoginCode() {
		return loginCode;
	}

	public void setLoginCode(String loginCode) {
		this.loginCode = loginCode;
	}

	public int getCookie_time() {
		return cookie_time;
	}

	public void setCookie_time(int cookie_time) {
		this.cookie_time = cookie_time;
	}

	public String getCookie_path() {
		return cookie_path;
	}

	public void setCookie_path(String cookie_path) {
		this.cookie_path = cookie_path;
	}

	public String getCookie_code() {
		return cookie_code;
	}

	public void setCookie_code(String cookie_code) {
		this.cookie_code = cookie_code;
	}

	public String getCookie_pw() {
		return cookie_pw;
	}

	public void setCookie_pw(String cookie_pw) {
		this.cookie_pw = cookie_pw;
	}

	public List<MenuEntity> getMenus() {
		return menus;
	}

	public void setMenus(List<MenuEntity> menus) {
		this.menus = menus;
	}

	public SystemLogEntity getLogEntity() {
		return logEntity;
	}

	public void setLogEntity(SystemLogEntity logEntity) {
		this.logEntity = logEntity;
	}

	public String getUsername() {
		return username;
	}

	public void setUsername(String username) {
		this.username = username;
	}

	public String getPassword() {
		return password;
	}

	public void setPassword(String password) {
		this.password = password;
	}

	/**
	 * @return the registerNum
	 */
	public Integer getRegisterNum() {
		return registerNum;
	}

	/**
	 * @param registerNum
	 *            the registerNum to set
	 */
	public void setRegisterNum(Integer registerNum) {
		this.registerNum = registerNum;
	}

	/**
	 * @return the modifyNum
	 */
	public Integer getModifyNum() {
		return modifyNum;
	}

	/**
	 * @param modifyNum
	 *            the modifyNum to set
	 */
	public void setModifyNum(Integer modifyNum) {
		this.modifyNum = modifyNum;
	}

	/**
	 * @return the designatedExpertsNum
	 */
	public Integer getDesignatedExpertsNum() {
		return designatedExpertsNum;
	}

	/**
	 * @param designatedExpertsNum
	 *            the designatedExpertsNum to set
	 */
	public void setDesignatedExpertsNum(Integer designatedExpertsNum) {
		this.designatedExpertsNum = designatedExpertsNum;
	}
	public HomeQrEntity getEntity() {
		return entity;
	}
	public void setEntity(HomeQrEntity entity) {
		this.entity = entity;
	}

	
}
