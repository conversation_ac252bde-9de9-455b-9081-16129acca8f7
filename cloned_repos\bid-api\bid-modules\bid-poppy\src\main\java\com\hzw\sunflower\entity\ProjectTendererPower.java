package com.hzw.sunflower.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.hzw.sunflower.common.BaseBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
* Created by pj on 2022/06/13
*/
@ApiModel("招标人项目权限表")
@TableName("t_project_tenderee_power")
@Data
public class ProjectTendererPower extends BaseBean implements Serializable  {
    private Long id;

    /**
     * 项目ID
     *
     * @mbg.generated
     */
    @ApiModelProperty("项目ID")
    private Long projectId;


    /**
     *角色名称
     */
    @ApiModelProperty("角色名称")
    private String roleName;

    /**
     * 被授权人id
     *
     * @mbg.generated
     */
    @ApiModelProperty("被授权人id")
    private Long userId;


    /**
     *用户姓名
     */
    @ApiModelProperty("用户姓名")
    private String userName;



    /**
     * 手机号
     *
     * @mbg.generated
     */
    @ApiModelProperty("手机号")
    private String userPhone;



    /**
     * 邮箱
     *
     * @mbg.generated
     */
    @ApiModelProperty("邮箱")
    private String email;


    /**
     *权限类型 1查看 2查看编辑
     *
     * @mbg.generated
     */
    @ApiModelProperty("权限类型 1查看 2查看编辑")
    private Integer  roleType;

    /**
     *是否是管理员  1是 2否
     *
     * @mbg.generated
     */
    @ApiModelProperty("是否是管理员  1是 2否")
    private Integer  isAdmin;


    /**
     *授权人
     *
     * @mbg.generated
     */
    @ApiModelProperty("授权人")
    private Long  fromUserId;




    private static final long serialVersionUID = 1L;
}