package com.hzw.ssm.sys.project.service;

import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.hzw.ssm.fw.base.BaseService;
import com.hzw.ssm.fw.util.CommUtil;
import com.hzw.ssm.fw.util.HZWException;
import com.hzw.ssm.sys.project.dao.AppointsExpertsMapper;
import com.hzw.ssm.sys.project.entity.AppointsExpertsEntity;
import com.hzw.ssm.sys.project.entity.ProjectEntity;

@Service
public class AppointsExpertsService extends BaseService {
	@Autowired
	private AppointsExpertsMapper mapper;

	public void saveAppointsExperts(ProjectEntity project, List<AppointsExpertsEntity> entityList) {
		try {
			if(entityList.size()>0) {
				for(AppointsExpertsEntity entity:entityList) {
					entity.setOfficialId(CommUtil.getKey());
					entity.setDecimationBatch(project.getDecimationBatch());
					
				}
			}
			/*entity.setOfficialId(CommUtil.getKey());
			entity.setDecimationBatch(project.getDecimationBatch());
			mapper.saveInfoList(entity);*/
		}catch (Exception e) {
			e.printStackTrace();
		}
	}
	/**
	 * 根据流水号查询指定专家列表
	 * @param entity
	 * @return
	 */
	public List<AppointsExpertsEntity> queryDateByDecimationBatch(AppointsExpertsEntity entity){
		List<AppointsExpertsEntity> entityList = new ArrayList<AppointsExpertsEntity>();
		if(entity.getDecimationBatch()==null||entity.getDecimationBatch()=="") {
			throw new HZWException("抽取时数据有误，请联系管理员！");
		}
		entityList = mapper.queryDateByDecimationBatch(entity);
		
		return entityList;
	}
	

	/**
	 * 根据批次号查询最大的抽取次数
	 * @param entity
	 * @return
	 */
	public Integer queryMaxCON(String  decimationBatch) {
		AppointsExpertsEntity entity =  new AppointsExpertsEntity();
		if(decimationBatch==null||decimationBatch=="") {
			throw new HZWException("数据有误，请联系管理员！");
		}
		entity.setDecimationBatch(decimationBatch);
		Integer con = mapper.queryMaxCON(entity);
		return con;
	}
	/**
	 * 根据信息
	 * @param entity
	 * @return
	 */
	public List<AppointsExpertsEntity> queryList(AppointsExpertsEntity entity) {
		List<AppointsExpertsEntity> entityList = mapper.queryList(entity);
		return entityList;
	}

	@Transactional
	public Integer updateStatus(AppointsExpertsEntity entity) {
		Integer con = mapper.updateStatus(entity);
		if(con!=1) {
			throw new HZWException("此身份证再库里不止一条信息，请联系管理员！");
		}
		return con;
	}
	@Transactional
	public Integer deleteExpert(AppointsExpertsEntity entity){
		
		Integer con = mapper.deleteExpert(entity);
		if(con!=1){
			throw new HZWException("数据有误，请联系管理员！");
		}
		return con;
	}
	
}
