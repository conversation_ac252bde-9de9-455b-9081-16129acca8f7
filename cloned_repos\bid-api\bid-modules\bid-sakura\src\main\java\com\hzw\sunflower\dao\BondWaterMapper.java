package com.hzw.sunflower.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hzw.sunflower.controller.response.BondSectionExportVo;
import com.hzw.sunflower.controller.response.BondWaterAllVo;
import com.hzw.sunflower.controller.response.BondWaterRefundVO;
import com.hzw.sunflower.controller.response.BondWaterReturnVo;
import com.hzw.sunflower.dto.BondWaterDto;
import com.hzw.sunflower.dto.BondWaterNccDto;
import com.hzw.sunflower.dto.BondWaterSupplierDto;
import com.hzw.sunflower.entity.BondWater;
import com.hzw.sunflower.entity.condition.BondProjectCondition;
import com.hzw.sunflower.entity.condition.BondWaterCondition;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 保证金流水表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-27
 */
@Mapper
public interface BondWaterMapper extends BaseMapper<BondWater> {

    /**
     * 根据标段和项目获取确认关联或已关联的流水
     * @param sectionId
     * @param relationStatus
     * @return
     */
    List<BondWater> getWaterListBySectionId(@Param("sectionId") Long sectionId, @Param("relationStatus") Integer relationStatus, @Param("companyId") Long companyId);

    /**
     * 根据标段和项目获取确认关联或已关联的流水（已办）
     * @param sectionId
     * @param relationStatus
     * @return
     */
    List<BondWater> getWaterListBySectionIdCompleted(@Param("sectionId") Long sectionId, @Param("relationStatus") Integer relationStatus, @Param("companyId") Long companyId, @Param("createdTime") Date createdTime);

    /**
     * 根据公司名称查询未关联流水
     * @param companyName
     * @return
     */
    List<BondWater> getWaterListByCompanyName(String companyName);

    /**
     * 根据公司名称查询未关联流水
     * @param companyName
     * @return
     */
    List<BondWaterAllVo> getWaterListByCompanyNameAll(String companyName);

    /**
     * 分页查询流水
     * @param page
     * @param condition
     * @return
     */
    IPage<BondWaterDto> getListByCondition(IPage<BondWaterDto> page, @Param("condition") BondWaterCondition condition);

    /**
     * 导出流水
     * @param condition
     * @return
     */
    List<BondWaterDto> getListByConditionForExcel(@Param("condition") BondWaterCondition condition);

    /**
     * 分页查询流水
     * @param page
     * @param condition
     * @return
     */
    IPage<BondWaterReturnVo> getListByConditionOther(IPage<BondWaterReturnVo> page, @Param("condition") BondWaterCondition condition);



    /**
     * 查询所有正常的未关联流水信息
     * @return
     */
    List<BondWater> getNotRelationWater();

    /**
     * 查询流水退还情况
     * @param id
     * @return
     */
    List<BondSectionExportVo> getSectionExportList(Long id);

    /**
     * 保证金45天未关联异常列表
     * @param page
     * @param condition
     * @return
     */
    Page<BondWaterRefundVO> abnormalWaterList(Page<BondWaterRefundVO> page, @Param("condition") BondProjectCondition condition);

    /**
     * 根据银行信息分组查询流水信息
     * @param splitIds
     * @return
     */
    List<BondWater> getWaterGroupByAccount(@Param("splitIds") String[] splitIds);

    /**
     * 获取带推送的隔月未关联收款流水
     * @return
     */
    List<BondWaterNccDto> findNccReceiveWater();

    /**
     * 校验当前流水是否关联过
     * @param waterId
     * @return
     */
    Integer checkIsRelation(@Param("waterId") Long waterId);

    /**
     * 月末归集 按项目
     * @param req
     * @return
     */
    Page<BondWater> selectSupplierPage(IPage<BondWater> page,@Param(value = "req") BondWaterSupplierDto req);

    /**
     * 保证金异常待处理数量
     * @return
     */
    Integer abnormalWaterCount();
}
