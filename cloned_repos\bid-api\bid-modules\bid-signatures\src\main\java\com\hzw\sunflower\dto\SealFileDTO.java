package com.hzw.sunflower.dto;

import com.hzw.sunflower.entity.SealFile;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode
public class SealFileDTO extends SealFile {

    @ApiModelProperty(value = "文件key")
    private String ossFileKey;

    @ApiModelProperty(value = "文件name")
    private String ossFileName;

    @ApiModelProperty(value = "中标人id")
    private Long bidWinPeopleId;

    @ApiModelProperty(value = "通知书id")
    private Long noticeId;

    @ApiModelProperty(value = "中标人企业名称")
    private String bidWinPeopleName;

    @ApiModelProperty(value = "是否中标")
    private Integer isWin;

}