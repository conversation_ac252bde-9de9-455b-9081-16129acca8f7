package com.hzw.ssm.fw.listener.action;

import java.io.IOException;

import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.hzw.ssm.util.empty.EmptyUtils;

public class JspInterceptors implements Filter {
	@Override
	public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
			throws IOException, ServletException {
		HttpServletRequest httpServletRequest = (HttpServletRequest) request;
		HttpServletResponse httpServletResponse = (HttpServletResponse) response;
		//http://127.0.0.1:8080/GZZJK/js/ueditor/controller.jsp
		String url = httpServletRequest.getRequestURI();

		if(ComDischarged.ueditor(request,response,chain)){
			chain.doFilter(request, response);
			return;
		}
		if (!EmptyUtils.isEmpty(url) && url.endsWith(".jsp") && !url.contains("CommonPrint.jsp")) {
			httpServletResponse.sendRedirect(httpServletRequest.getContextPath());
			return;
		}
		chain.doFilter(request, response);
	}

	@Override
	public void destroy() {

	}

	@Override
	public void init(FilterConfig arg0) throws ServletException {

	}
}
