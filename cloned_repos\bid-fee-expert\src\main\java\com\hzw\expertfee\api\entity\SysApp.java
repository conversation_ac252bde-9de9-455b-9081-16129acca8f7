package com.hzw.expertfee.api.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR> Gen
 * @since 2022-04-26
 */
@Getter
@Setter
@TableName("t_sys_app")
@ApiModel(value = "外部系统调用接口账号", description = "")
public class SysApp implements Serializable {

    private static final long serialVersionUID = 1L;

    private String id;

    @ApiModelProperty("账号")
    private String sn;

    @ApiModelProperty("密码")
    private String secretKey;

    @ApiModelProperty("是否删除 1是 0否")
    private Integer isDelete;

    @ApiModelProperty("状态 1 正常 ")
    private Integer status;

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("地址")
    private String url;


}
