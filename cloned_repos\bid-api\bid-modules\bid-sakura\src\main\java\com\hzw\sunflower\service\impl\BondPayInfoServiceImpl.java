package com.hzw.sunflower.service.impl;

import com.hzw.sunflower.dto.BondPayInfoDto;
import com.hzw.sunflower.entity.BondPayInfo;
import com.hzw.sunflower.dao.BondPayInfoMapper;
import com.hzw.sunflower.service.BondPayInfoService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 保证金银行支付信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-26
 */
@Service
public class BondPayInfoServiceImpl extends ServiceImpl<BondPayInfoMapper, BondPayInfo> implements BondPayInfoService {

    /**
     * 获取待刷新状态的数据
     * @return
     */
    @Override
    public List<BondPayInfoDto> getRefreshData(Long refundId) {
        return this.baseMapper.getRefreshData(refundId);
    }

    /**
     * 获取待刷新状态的数据
     * @return
     */
    @Override
    public List<BondPayInfoDto> getBatchRefreshData(List<Long> list) {
        return this.baseMapper.getBatchRefreshData(list);
    }
}
