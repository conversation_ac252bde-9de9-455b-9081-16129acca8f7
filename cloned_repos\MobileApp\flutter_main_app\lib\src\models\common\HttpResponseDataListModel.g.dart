// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'HttpResponseDataListModel.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

HttpResponseDataListModel<T> _$HttpResponseDataListModelFromJson<T>(
    Map<String, dynamic> json) {
  return HttpResponseDataListModel<T>(
    totalNumber: json['totalNumber'] as int,
    totalPage: json['totalPage'] as int,
    pageSize: json['pageSize'] as int,
    currentPage: json['currentPage'] as int,
    data: (json['data'] as List)?.map(GenericConverter<T>().fromJson)?.toList(),
  );
}

Map<String, dynamic> _$HttpResponseDataListModelToJson<T>(
        HttpResponseDataListModel<T> instance) =>
    <String, dynamic>{
      'totalNumber': instance.totalNumber,
      'totalPage': instance.totalPage,
      'pageSize': instance.pageSize,
      'currentPage': instance.currentPage,
      'data': instance.data?.map(GenericConverter<T>().toJson)?.toList(),
    };
