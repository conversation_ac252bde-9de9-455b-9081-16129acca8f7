package com.hzw.sunflower.controller.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2022/1/12 10:03
 * @description： 文件进度请求体
 * @version: 1.0
 */
@Data
public class DocBidRoundREQ {

    @ApiModelProperty(value = "项目ID", position = 1, required = true)
    private Long projectId;

    @ApiModelProperty(value = "招标阶段", position = 2, required = true)
    @NotNull(message = "招标阶段不能为空")
    private Integer bidRound;

    @ApiModelProperty(value = "标段ID", position = 3, required = true)
    private Long bidId;

    @ApiModelProperty(value = "标段ID集合", position = 4, required = true)
    private List<Long> sectionIds;
}
