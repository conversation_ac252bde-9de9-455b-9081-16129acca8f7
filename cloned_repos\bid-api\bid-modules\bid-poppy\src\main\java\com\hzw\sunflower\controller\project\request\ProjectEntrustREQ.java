package com.hzw.sunflower.controller.project.request;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *
 */
@ApiModel(description = "项目委托 ")
@Data
public class ProjectEntrustREQ {

    @ApiModelProperty(value = "主键", position = 1)
    private Long id;

    @ApiModelProperty(value = "项目状态 项目状态（0：初始化    1：待接受委托 2：进行中 3：办结  4：中止  5：不接受委托）", position = 2)
    private Integer status;

    @ApiModelProperty(value = "异常原因", position = 3)
    private String refuseReason;

    @ApiModelProperty(value = "省内省外 1:省内 2:省外", position = 4)
    private Integer provinceInOut;
}
