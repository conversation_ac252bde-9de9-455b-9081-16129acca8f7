package com.hzw.sunflower.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.BooleanUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hzw.sunflower.constant.CommonConstants;
import com.hzw.sunflower.constant.PageConstants;
import com.hzw.sunflower.constant.UserConstants;
import com.hzw.sunflower.constant.constantenum.*;
import com.hzw.sunflower.controller.request.LenovoUserQueryReq;
import com.hzw.sunflower.controller.response.*;
import com.hzw.sunflower.dao.CheckBondMapper;
import com.hzw.sunflower.dao.UserOpenMapper;
import com.hzw.sunflower.dto.AgentUserDTO;
import com.hzw.sunflower.entity.JwtUser;
import com.hzw.sunflower.entity.Role;
import com.hzw.sunflower.entity.SealRole;
import com.hzw.sunflower.entity.User;
import com.hzw.sunflower.entity.condition.*;
import com.hzw.sunflower.security.utils.SecurityUtils;
import com.hzw.sunflower.service.CommonOpenService;
import com.hzw.sunflower.service.CommonUserService;
import com.hzw.sunflower.util.BeanListUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * OpenServiceImpl接口实现
 *
 * <AUTHOR>
 * @version 1.0.0 2021-04-07
 */
@Service
public class CommonOpenServiceImpl implements CommonOpenService {


    @Autowired
    private UserOpenMapper userOpenMapper;

    @Autowired
    private CheckBondMapper checkBondMapper;

    @Autowired
    private CommonUserService commonUserService;

    @Value("${organ.organization_type:}")
    private Integer organizationType;

    /**
     * 招标人管理
     * param    keywords(企业名称，信用代码，法定代表人)；
     *
     * @return
     */
    @Override
    public IPage<CompanySuperviseVo> getBidderList(CompanyOpenCondition condition) {
        IPage<CompanySuperviseVo> page = condition.buildPage();
        CompanySuperviseOpenCondition con = BeanListUtil.convert(condition, CompanySuperviseOpenCondition.class);
        con.setIdentity(CompanyEnum.BIDDER.getType());
        IPage<CompanySuperviseVo> page1 = userOpenMapper.getCompanySupervise(page, con);
        return page1;
    }

    /**
     * 专家库抽取-获得过滤企业信息
     * @param condition
     * @return
     */
    @Override
    public IPage<CompanySuperviseVo> getCompanyInfoList(CompanyOpenCondition condition) {
        IPage<CompanySuperviseVo> page = condition.buildPage();
        Long companyId = null;
        // 根据委托人id 获取企业id 用来查询招标人单位信息
        if(null != condition.getPrincipalUserId()){
            companyId = userOpenMapper.getCompanyIdByUserId(condition.getPrincipalUserId());
        }
        return userOpenMapper.getCompanyInfoList(page,condition.getKeywords(),companyId);
    }


    /**
     * 招标人用户列表
     *
     * @param con companyId  keywords（姓名/联系方式/企业名称）
     * @return
     */
    @Override
    public IPage<UserTableVo> getBiddingUserList(TUserIdentityOpenCondition con) {
        TUserIdentityCondition condition = BeanListUtil.convert(con, TUserIdentityCondition.class);
        condition.setIdentity(CompanyEnum.BIDDER.getType());
        IPage<UserTableVo> page = condition.buildPage();
        List<Integer> statusArr = new ArrayList<>();
        if (condition.getStatus() != null && !condition.getStatus().equals(UserStatusEnum.ABNORMAL.getCode())) {
            //如果等于待审核 则查询两个待审核
            if (UserStatusEnum.REGISTER_TO_EXAMINE.getCode().equals(condition.getStatus())
                    || UserStatusEnum.PLATFORM_TO_EXAMINE.getCode().equals(condition.getStatus())) {
                statusArr.add(UserStatusEnum.REGISTER_TO_EXAMINE.getCode());
                statusArr.add(UserStatusEnum.PLATFORM_TO_EXAMINE.getCode());
                //如果等于待完善信息 则查询待完善信息/两个拒绝
            } else if (UserStatusEnum.INFO_PERFECT.getCode().equals(condition.getStatus())) {
                statusArr.add(UserStatusEnum.INFO_PERFECT.getCode());
                statusArr.add(UserStatusEnum.REGISTER_NOT_PASS.getCode());
                statusArr.add(UserStatusEnum.PLATFORM_NOT_PASS.getCode());
                statusArr.add(UserStatusEnum.PLATFORM_NOT_PASS_ABROAD.getCode());
                statusArr.add(UserStatusEnum.JITC_PERFECT.getCode());
            } else {
                statusArr.add(condition.getStatus());
            }
        }
        condition.setStatusArr(statusArr);
        IPage<UserTableVo> page1 = userOpenMapper.getUserTable(page, condition);
        return page1;
    }


    /**
     * 供应商管理
     * param    keywords(企业名称，信用代码，法定代表人)；
     *
     * @return
     */
    @Override
    public IPage<CompanySuperviseVo> getSupplierList(CompanyOpenCondition condition) {
        IPage<CompanySuperviseVo> page = condition.buildPage();
        CompanySuperviseOpenCondition con = BeanListUtil.convert(condition, CompanySuperviseOpenCondition.class);
        con.setIdentity(CompanyEnum.SUPPLIER.getType());
        IPage<CompanySuperviseVo> page1 = userOpenMapper.getCompanySupervise(page, con);
        return page1;
    }


    /**
     * 供应商人员列表
     *
     * @param con companyId  keywords（姓名/联系方式/企业名称）
     * @return
     */
    @Override
    public IPage<UserTableVo> getSupplierUserList(TUserIdentityOpenCondition con) {
        TUserIdentityCondition condition = BeanListUtil.convert(con, TUserIdentityCondition.class);
        condition.setIdentity(CompanyEnum.SUPPLIER.getType());
        IPage<UserTableVo> page = condition.buildPage();
        List<Integer> statusArr = new ArrayList<>();
        if (condition.getStatus() != null && !condition.getStatus().equals(UserStatusEnum.ABNORMAL.getCode())) {
            //如果等于待审核 则查询两个待审核
            if (UserStatusEnum.REGISTER_TO_EXAMINE.getCode().equals(condition.getStatus())
                    || UserStatusEnum.PLATFORM_TO_EXAMINE.getCode().equals(condition.getStatus())) {
                statusArr.add(UserStatusEnum.REGISTER_TO_EXAMINE.getCode());
                statusArr.add(UserStatusEnum.PLATFORM_TO_EXAMINE.getCode());
                //如果等于待完善信息 则查询待完善信息/两个拒绝
            } else if (UserStatusEnum.INFO_PERFECT.getCode().equals(condition.getStatus())) {
                statusArr.add(UserStatusEnum.INFO_PERFECT.getCode());
                statusArr.add(UserStatusEnum.REGISTER_NOT_PASS.getCode());
                statusArr.add(UserStatusEnum.PLATFORM_NOT_PASS.getCode());
                statusArr.add(UserStatusEnum.PLATFORM_NOT_PASS_ABROAD.getCode());
                statusArr.add(UserStatusEnum.JITC_PERFECT.getCode());
            } else {
                statusArr.add(condition.getStatus());
            }
        }
        condition.setStatusArr(statusArr);
        IPage<UserTableVo> page1 = userOpenMapper.getUserTable(page, condition);
        return page1;
    }


    /**
     * 代理机构管理
     * param    keywords(企业名称，信用代码，法定代表人)；
     *
     * @return
     */
    @Override
    public IPage<CompanySuperviseVo> getAgencyList(CompanyOpenCondition condition) {
        IPage<CompanySuperviseVo> page = condition.buildPage();
        CompanySuperviseOpenCondition con = BeanListUtil.convert(condition, CompanySuperviseOpenCondition.class);
        con.setIdentity(CompanyEnum.AGENCY.getType());
        IPage<CompanySuperviseVo> page1 = userOpenMapper.getCompanySupervise(page, con);
        return page1;
    }


    /**
     * 代理机构人员列表
     *
     * @param con companyId  keywords（姓名/联系方式/企业名称）
     * @return
     */
    @Override
    public IPage<UserTableVo> getAgencyUserList(TUserIdentityOpenCondition con) {
        TUserIdentityCondition condition = BeanListUtil.convert(con, TUserIdentityCondition.class);
        condition.setIdentity(CompanyEnum.AGENCY.getType());
        IPage<UserTableVo> page = condition.buildPage();
        List<Integer> statusArr = new ArrayList<>();
        if (condition.getStatus() != null && !condition.getStatus().equals(UserStatusEnum.ABNORMAL.getCode())) {
            //如果等于待审核 则查询两个待审核
            if (UserStatusEnum.REGISTER_TO_EXAMINE.getCode().equals(condition.getStatus())
                    || UserStatusEnum.PLATFORM_TO_EXAMINE.getCode().equals(condition.getStatus())) {
                statusArr.add(UserStatusEnum.REGISTER_TO_EXAMINE.getCode());
                statusArr.add(UserStatusEnum.PLATFORM_TO_EXAMINE.getCode());
                //如果等于待完善信息 则查询待完善信息/两个拒绝
            } else if (UserStatusEnum.INFO_PERFECT.getCode().equals(condition.getStatus())) {
                statusArr.add(UserStatusEnum.INFO_PERFECT.getCode());
                statusArr.add(UserStatusEnum.REGISTER_NOT_PASS.getCode());
                statusArr.add(UserStatusEnum.PLATFORM_NOT_PASS.getCode());
                statusArr.add(UserStatusEnum.PLATFORM_NOT_PASS_ABROAD.getCode());
                statusArr.add(UserStatusEnum.JITC_PERFECT.getCode());
            } else {
                statusArr.add(condition.getStatus());
            }
        }
        condition.setStatusArr(statusArr);
        IPage<UserTableVo> page1 = userOpenMapper.getUserTable(page, condition);
        return page1;
    }


    /**
     * 根据userId获取用户权限
     *
     * @param userId
     * @return
     */
    @Override
    public List<RoleVo> getUserRoleByUserId(Long userId) {
        return userOpenMapper.getUserRoleByUserId(userId);
    }


    /**
     * 根据companyId获取企业信息
     *
     * @param companyId
     * @return
     */
    @Override
    public OpenCompanyVo getCompanyInfoById(Long companyId) {
        return userOpenMapper.getCompanyInfoById(companyId);
    }


    /**
     * 根据用户id查询用户部门企业信息  投标人
     *
     * @param userId
     * @return
     */
    @Override
    public UserDeptCompanyVo getUserInfoByIdForBidder(Long userId) {
        return userOpenMapper.getUserInfoById(userId, CompanyEnum.BIDDER.getType());
    }


    /**
     * 根据用户id查询用户部门企业信息  代理机构人
     *
     * @param userId
     * @return
     */
    @Override
    public UserDeptCompanyVo getUserInfoByIdForAgency(Long userId) {
        return userOpenMapper.getUserInfoById(userId, CompanyEnum.AGENCY.getType());
    }


    /**
     * 根据用户id查询用户部门企业信息  供应商构人
     *
     * @param userId
     * @return
     */
    @Override
    public UserDeptCompanyVo getUserInfoByIdForSupplier(Long userId) {
        return userOpenMapper.getUserInfoById(userId, CompanyEnum.SUPPLIER.getType());
    }


    @Override
    public IPage<UserInfoVo> getCompanyUser(CompanyUserCondition condition) {
        IPage<UserInfoVo> page = condition.buildPage();
        IPage<UserInfoVo> page1 = userOpenMapper.getCompanyUser(page, condition);
        return page1;
    }

    @Override
    public UserInfoVo getCompanyUserInfo(CompanyUserCondition condition) {
        UserInfoVo page1 = userOpenMapper.getCompanyUserInfoByPhone(condition);
        return page1;
    }

    /**
     * 委托人页面联想功能
     *
     * @param lenovoUserQueryReq
     * @return
     */
    @Override
    public List<AgentUserDTO> queryBidderInfo(LenovoUserQueryReq lenovoUserQueryReq, JwtUser jwtUser) {
        List<AgentUserDTO> dtoList = new ArrayList<AgentUserDTO>();
        //1.无单位id 查询单位信息返回
        if (BeanUtil.isEmpty(lenovoUserQueryReq.getCompanyId())) {
            CompanyOpenCondition condition = new CompanyOpenCondition();
            condition.setKeywords(lenovoUserQueryReq.getCompanyName());
            if (BooleanUtil.isTrue(lenovoUserQueryReq.getFilterBySelfCompanyType())) {
                // 过滤自身企业
                if ((jwtUser != null) && (jwtUser.getCompanyId() != null)
                        && (CompanyEnum.BIDDER.getType().equals(jwtUser.getUserIdentity()))) {
                    condition.setCompanyId(jwtUser.getCompanyId());
                }
            }
            condition.setPageSize(PageConstants.PAGE_MAX_SIZE);
            IPage<CompanySuperviseVo> dao = this.getBidderList(condition);
            for (CompanySuperviseVo com : dao.getRecords()) {
                AgentUserDTO dto = new AgentUserDTO();
                dto.setCompanyId(com.getId());
                dto.setCompanyName(com.getCompanyName());
                dtoList.add(dto);
            }
        } else {
            //2.有单位id 根据单位查询用户信息
            CompanyUserCondition userCondition = new CompanyUserCondition();
            userCondition.setCompanyId(lenovoUserQueryReq.getCompanyId());
            userCondition.setUserName(lenovoUserQueryReq.getUserName());
            userCondition.setIdentity(CompanyEnum.BIDDER.getType());
            userCondition.setPageSize(PageConstants.PAGE_MAX_SIZE);
            IPage<UserInfoVo> dao = this.getCompanyUser(userCondition);
            for (UserInfoVo d : dao.getRecords()) {
                AgentUserDTO dto = new AgentUserDTO();
                dto.setCompanyName(lenovoUserQueryReq.getCompanyName());
                dto.setCompanyId(lenovoUserQueryReq.getCompanyId());
                dto.setUserId(d.getUserId());
                dto.setUserName(d.getUserName());
                //执行手机号脱敏
                dto.setUserPhone(phoneDesensitization(d.getUserPhone()));
                dtoList.add(dto);
            }
        }
        return dtoList;
    }


    /**
     * 代理机构联想
     *
     * @param lenovoUserQueryReq
     * @return
     */
    @Override
    public List<AgentUserDTO> queryAgencyInfo(LenovoUserQueryReq lenovoUserQueryReq, JwtUser jwtUser) {
        List<AgentUserDTO> dtoList = new ArrayList<AgentUserDTO>();
        //1.无单位id 查询单位信息返回
        if (BeanUtil.isEmpty(lenovoUserQueryReq.getCompanyId())) {
            CompanyOpenCondition condition = new CompanyOpenCondition();
            condition.setKeywords(lenovoUserQueryReq.getCompanyName());
            if (BooleanUtil.isTrue(lenovoUserQueryReq.getFilterBySelfCompanyType())) {
                // 过滤自身企业
                if ((jwtUser != null) && (jwtUser.getCompanyId() != null)
                        && (CompanyEnum.AGENCY.getType().equals(jwtUser.getUserIdentity()))) {
                    condition.setCompanyId(jwtUser.getCompanyId());
                }
            }
            condition.setPageSize(PageConstants.PAGE_MAX_SIZE);
            IPage<CompanySuperviseVo> dao = this.getAgencyList(condition);
            for (CompanySuperviseVo com : dao.getRecords()) {
                AgentUserDTO dto = new AgentUserDTO();
                dto.setCompanyId(com.getId());
                dto.setCompanyName(com.getCompanyName());
                dtoList.add(dto);
            }
        } else {
            //2.有单位id 根据单位查询用户信息
            CompanyUserCondition userCondition = new CompanyUserCondition();
            userCondition.setCompanyId(lenovoUserQueryReq.getCompanyId());
            userCondition.setUserName(lenovoUserQueryReq.getUserName());
            userCondition.setIdentity(CompanyEnum.AGENCY.getType());
            userCondition.setPageSize(PageConstants.PAGE_MAX_SIZE);
            IPage<UserInfoVo> dao = this.getCompanyUser(userCondition);
            for (UserInfoVo d : dao.getRecords()) {
                AgentUserDTO dto = new AgentUserDTO();
                dto.setCompanyName(lenovoUserQueryReq.getCompanyName());
                dto.setCompanyId(lenovoUserQueryReq.getCompanyId());
                dto.setUserId(d.getUserId());
                // 判断手机号不能为空
                if(null != d.getUserPhone() && !"".equals(d.getUserPhone())){
                    dto.setUserName(d.getUserName()+d.getUserPhone().substring(d.getUserPhone().length() - 4));
                }else{
                    dto.setUserName(d.getUserName());
                }

                //执行手机号脱敏
                dto.setUserPhone(phoneDesensitization(d.getUserPhone()));
                dtoList.add(dto);
            }
        }
        return dtoList;
    }



    /**
     * 代理机构联想
     *
     * @param lenovoUserQueryReq
     * @return
     */
    @Override
    public AgentUserDTO queryAgencyInfoList(LenovoUserQueryReq lenovoUserQueryReq, JwtUser jwtUser) {
            //2.有单位id 根据单位查询用户信息
            CompanyUserCondition userCondition = new CompanyUserCondition();
            userCondition.setCompanyId(lenovoUserQueryReq.getCompanyId());
            userCondition.setUserName(lenovoUserQueryReq.getUserName());
            userCondition.setIdentity(CompanyEnum.AGENCY.getType());
            userCondition.setPageSize(PageConstants.PAGE_MAX_SIZE);
            userCondition.setUserPhone(lenovoUserQueryReq.getUserPhone());
            UserInfoVo d = this.getCompanyUserInfo(userCondition);
            AgentUserDTO dto = new AgentUserDTO();
            if(null != d){
                dto.setCompanyName(lenovoUserQueryReq.getCompanyName());
                dto.setCompanyId(lenovoUserQueryReq.getCompanyId());
                dto.setUserId(d.getUserId());
                // 判断手机号不能为空
                if(null != d.getUserPhone() && !"".equals(d.getUserPhone())){
                    dto.setUserName(d.getUserName()+d.getUserPhone().substring(d.getUserPhone().length() - 4));
                }else{
                    dto.setUserName(d.getUserName());
                }

            }
        return dto;
    }

    /**
     * 供应商联想
     *
     * @param lenovoUserQueryReq
     * @return
     */
    @Override
    public List<AgentUserDTO> querySupplierInfo(LenovoUserQueryReq lenovoUserQueryReq, JwtUser jwtUser) {
        List<AgentUserDTO> dtoList = new ArrayList<AgentUserDTO>();
        //1.无单位id 查询单位信息返回
        if (BeanUtil.isEmpty(lenovoUserQueryReq.getCompanyId())) {
            CompanyOpenCondition condition = new CompanyOpenCondition();
            condition.setKeywords(lenovoUserQueryReq.getCompanyName());
            if (BooleanUtil.isTrue(lenovoUserQueryReq.getFilterBySelfCompanyType())) {
                // 过滤自身企业
                if ((jwtUser != null) && (jwtUser.getCompanyId() != null)
                        && (CompanyEnum.SUPPLIER.getType().equals(jwtUser.getUserIdentity()))) {
                    condition.setCompanyId(jwtUser.getCompanyId());
                }
            }
            condition.setPageSize(PageConstants.PAGE_MAX_SIZE);
            IPage<CompanySuperviseVo> dao = this.getSupplierList(condition);
            for (CompanySuperviseVo com : dao.getRecords()) {
                AgentUserDTO dto = new AgentUserDTO();
                dto.setCompanyId(com.getId());
                dto.setCompanyName(com.getCompanyName());
                dtoList.add(dto);
            }
        } else {
            //2.有单位id 根据单位查询用户信息
            CompanyUserCondition userCondition = new CompanyUserCondition();
            userCondition.setCompanyId(lenovoUserQueryReq.getCompanyId());
            userCondition.setUserName(lenovoUserQueryReq.getUserName());
            userCondition.setIdentity(CompanyEnum.SUPPLIER.getType());
            userCondition.setPageSize(PageConstants.PAGE_MAX_SIZE);
            IPage<UserInfoVo> dao = this.getCompanyUser(userCondition);
            for (UserInfoVo d : dao.getRecords()) {
                AgentUserDTO dto = new AgentUserDTO();
                dto.setCompanyName(lenovoUserQueryReq.getCompanyName());
                dto.setCompanyId(lenovoUserQueryReq.getCompanyId());
                dto.setUserId(d.getUserId());
                dto.setUserName(d.getUserName());
                //执行手机号脱敏
                dto.setUserPhone(phoneDesensitization(d.getUserPhone()));
                dtoList.add(dto);
            }
        }
        return dtoList;
    }


    /**
     * 手机号脱敏 130****1234
     *
     * @return
     */
    private String phoneDesensitization(String phone) {
        StringBuffer sb = new StringBuffer();
        if (!StringUtils.isBlank(phone)) {
            sb.append(phone.substring(0, UserConstants.PHONE_FRONT));
            sb.append(UserConstants.PHONE_DES_CHAR);
            sb.append(phone.substring(UserConstants.PHONE_AFTER));
        }

        return sb.toString();
    }

    @Override
    public Boolean validAutoByUserId(Long userId, String code) {
        UserSmsConfigVo vo = userOpenMapper.getUserSmsConfig(userId, code);
        if (vo != null && vo.getIsAuto().equals(CommonConstants.YES)) {
            return true;
        }
        return false;
    }

    @Override
    public Boolean valid24HourSendByUserId(Long userId, String code) {
        UserSmsConfigVo vo = userOpenMapper.getUserSmsConfig(userId, code);
        //是否设置了24小时再次发送
        if (vo != null && vo.getIsSendRepeat().equals(CommonConstants.YES)) {
            return true;
        }
        return false;
    }

    @Override
    public List<UserInfoVo> getDirectorByUserId(String deptCode) {
        // 判断总分公司 总公司是处长 分公司是分公司总经理
        String roleCode = RoleCodeEnum.CHUZHANG.getType();
        JwtUser loginUser = SecurityUtils.getJwtUser();
        //if (!loginUser.getCompanyId().equals(loginUser.getDepartCompanyId())) {
        //    roleCode = RoleCodeEnum.FGSZJL.getType();
        //}
        List<UserInfoVo> directorByUserId = userOpenMapper.getDirectorByUserId(deptCode, roleCode);
        if(CollectionUtils.isEmpty(directorByUserId)){
            if (!loginUser.getCompanyId().equals(loginUser.getDepartCompanyId())) {
                roleCode = RoleCodeGroupEnum.FGSZJL.getCode();
                directorByUserId = userOpenMapper.getDirectorByUserIdF(deptCode, roleCode);
            }

        }
        //if(CollectionUtils.isEmpty(directorByUserId)){
        //    if (!loginUser.getCompanyId().equals(loginUser.getDepartCompanyId())) {
        //        roleCode = RoleCodeEnum.FGSZJLXZ.getType();
        //    }
        //    directorByUserId = userOpenMapper.getDirectorByUserId(deptCode, roleCode);
        //}
        return directorByUserId;
    }

    @Override
    public EarlySectionTimeVo getEarliestTimeBySectionIds(List<Long> subIdList) {
        return userOpenMapper.getEarliestTimeBySectionIds(subIdList);
    }

    @Override
    public List<CompanyHasBidDocVo> getCompanyHasBidDoc(Long sectionId, Integer bidRound) {
        return userOpenMapper.getCompanyHasBidDoc(sectionId, bidRound);
    }

    /**
     * 根据用户id查询所有处长
     * @param czCodes
     * @param confirmUserId
     * @return
     */
    @Override
    public List<User> getChuZhangByUserId(List<String> czCodes, Long confirmUserId) {
        return userOpenMapper.getChuZhangByUserId(czCodes,confirmUserId,organizationType);
    }

    @Override
    public List<User> getUserByRole(List<String> czCodes) {
        return userOpenMapper.getUserByRole(czCodes);
    }


    /**
     * 查询用户id 下 指定部门的处长
     * @param czCodes
     * @param confirmUserId
     * @param departId
     * @return
     */
    @Override
    public List<User> getChuZhangByUserIdDepartId(List<String> czCodes, Long confirmUserId,Long departId) {
        return userOpenMapper.getChuZhangByUserIdDepartId(czCodes,confirmUserId,organizationType,departId);
    }

    /**
     * 查询部门id所在公司的总经理
     * @param czCodes
     * @param departId
     * @return
     */
    @Override
    public List<User> getLeaderByDepartId(List<String> czCodes, Long departId) {
        return userOpenMapper.getLeaderByDepartId(czCodes,departId);
    }

    @Override
    public Boolean checkIsCz(Long confirmUserId) {
        Map<String, String> map = CZCodeEnum.toMap();
        //是否是处长
        Boolean isCz = false;
        //根据用户id查询用户角色
        List<RoleVo> userRoles = this.getUserRoleByUserId(confirmUserId);
        for(RoleVo r:userRoles){
            if(StringUtils.isNotBlank(r.getRoleCode())){
                String hasCode = map.get(r.getRoleCode());
                if(StringUtils.isNotBlank(hasCode)){
                    isCz = true;
                    break;
                }
            }
        }
        return  isCz;
    }

    /**
     * 判断是否可以线上保证金退还
     * @param sectionIds
     * @return
     */
    @Override
    public Boolean checkOnlineBondRefund(List<Long> sectionIds,Long companyId) {
        //验证线下是否存在退还记录
        Integer count = checkBondMapper.getOfflineBondRefund(sectionIds,companyId);
        return count>0;
    }

    /**
     * 判断是否可以线下保证金退还
     * @param sectionIds
     * @return
     */
    @Override
    public Boolean checkOfflineBondRefund(List<Long> sectionIds,Long companyId) {
        //验证线上是否存在关联记录
        Integer count = checkBondMapper.getOnlineBondRefund(sectionIds,companyId);
        return count>0;
    }

    /**
     * 查询用户登录ncc所需数据
     * @param userId
     * @param identity
     * @return
     */
    @Override
    public UserNccInfoVo queryUserNccInfo(Long userId, Integer identity) {
        return userOpenMapper.queryUserNccInfo(userId, identity);
    }

    /**
     * 判断当前用户是否为分公司总经理
      * @param userId
     * @return
     */
    @Override
    public Boolean checkIsFgszjl(Long userId) {
        Boolean isFgszjl = false;
        // 获取当前登录人的角色
        UserTokenRolesVO userRoles = commonUserService.getUserRoles();
        List<Role> roleList = userRoles.getRoles();
        List<String> collect = roleList.stream().map(Role::getRoleCode).collect(Collectors.toList());
        // 判断当前用户是否为分公司总经理
        //if(collect.contains(RoleCodeEnum.FGSZJL.getType())
        //|| collect.contains(RoleCodeEnum.FGSZJLNJ.getType())
        //|| collect.contains(RoleCodeEnum.FGSZJLXZ.getType())
        //){
        if(collect.stream().anyMatch(roleType ->
                roleType.startsWith(RoleCodeGroupEnum.FGSZJL.getCode()))){
            isFgszjl = true;
        }
        return isFgszjl;
    }

    @Override
    public Boolean checkIsBmfzr(Long userId) {
        Boolean isBmfzr = false;
        // 获取当前登录人的角色
        UserTokenRolesVO userRoles = commonUserService.getUserRoles();
        List<Role> roleList = userRoles.getRoles();
        List<String> collect = roleList.stream().map(Role::getRoleCode).collect(Collectors.toList());
        // 判断当前用户是否为分公司总经理
        //if(collect.contains(RoleCodeEnum.BMFZR.getType())
        //|| collect.contains(RoleCodeEnum.BMFZRNJYB.getType())
        //|| collect.contains(RoleCodeEnum.BMFZR_NJEB.getType())
        //|| collect.contains(RoleCodeEnum.BMFZR_XZYB.getType())
        //|| collect.contains(RoleCodeEnum.BMFZR_XZEB.getType())
        //){
        if(collect.stream().anyMatch(roleType ->
                roleType.startsWith(RoleCodeGroupEnum.BMFZR.getCode()))){
            isBmfzr = true;
        }
        return isBmfzr;
    }

    @Override
    public boolean checkIsRole(Long confirmUserId, List<String> roleCodes) {
        //是否是处长
        boolean isRole = false;
        //根据用户id查询用户角色
        List<RoleVo> userRoles = this.getUserRoleByUserId(confirmUserId);
        for(RoleVo r:userRoles){
            if(StringUtils.isNotBlank(r.getRoleCode())&&roleCodes.contains(r.getRoleCode())){
                isRole = true;
                break;
            }
        }
        return  isRole;
    }

    @Override
    public boolean checkDepartIsRole(Long confirmUserId,Long departId, List<String> roleCodes) {
        //是否是处长
        boolean isRole = false;
        //根据用户id查询用户角色
        List<RoleVo> userRoles = this.getUserRoleByUserId(confirmUserId);
        for(RoleVo r:userRoles){
            if(StringUtils.isNotBlank(r.getRoleCode())&&roleCodes.contains(r.getRoleCode())){
                isRole = true;
                break;
            }
        }
        return  isRole;
    }

    @Override
    public List<User> getUserByDepartId(String code, Long confirmUserId, Long deptId) {
        return userOpenMapper.getUserByDepartId(code,confirmUserId,organizationType,deptId);
    }

    @Override
    public List<User> getZJLDepartId(String code, Long deptId) {
        return userOpenMapper.getZJLDepartId(code,deptId);
    }
}
