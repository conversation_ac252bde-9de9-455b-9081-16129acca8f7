package com.hzw.sunflower.dto;

import com.hzw.sunflower.entity.ProjectSectionSchedule;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


@ApiModel("开标一览表模板及规则")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProjectSectionScheduleDto extends ProjectSectionSchedule {

    @ApiModelProperty(value = "包号")
    private Long packageNumber;

    @ApiModelProperty(value = "ossFileKey")
    private String ossFileKey;

    @ApiModelProperty(value = "ossFileName")
    private String ossFileName;

}
