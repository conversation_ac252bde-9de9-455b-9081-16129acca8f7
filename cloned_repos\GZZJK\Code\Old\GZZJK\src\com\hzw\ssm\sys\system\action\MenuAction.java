package com.hzw.ssm.sys.system.action;

import java.io.IOException;
import java.io.PrintWriter;
import java.util.List;

import javax.servlet.http.HttpServletResponse;

import org.apache.struts2.ServletActionContext;
import org.apache.struts2.convention.annotation.Action;
import org.apache.struts2.convention.annotation.Namespace;
import org.apache.struts2.convention.annotation.ParentPackage;
import org.apache.struts2.convention.annotation.Result;
import org.apache.struts2.convention.annotation.Results;
import org.springframework.beans.factory.annotation.Autowired;

import com.hzw.ssm.fw.base.BaseAction;
import com.hzw.ssm.sys.system.entity.MenuEntity;
import com.hzw.ssm.sys.system.service.MenuService;

/**
 * 菜单操作
 * 
 * @page user
 * <AUTHOR> 
 */
@Namespace("/menuinfo")
@ParentPackage(value = "default")
@Results( { @Result(name = "init", location = "/jsp/sys/menuList.jsp"),
		@Result(name = "toMenuMain", location = "/jsp/sys/menuMain.jsp"),
		@Result(name = "toMenuRight", location = "/jsp/sys/menuRight.jsp"),
		@Result(name = "toMenuLeft", location = "/jsp/sys/menuLeft.jsp"),
		@Result(name = "toMenuAdd", location = "/jsp/sys/menuAdd.jsp") })
public class MenuAction extends BaseAction {

	/**
	 * 
	 */
	private static final long serialVersionUID = -8823858495799309882L;

	/** 菜单对象列表 */
	private List<MenuEntity> menuList;

	/** 菜单对象列表的查询条件 */
	private MenuEntity menuEntity;

	/**
	 * 菜单ID
	 */
	private String menuId;

	/**
	 * 菜单字符串
	 */
	private String menuStr;

	/** 需要编辑的用户编号 */
	private String key;

	/** 提示信息 */
	private String msg;

	private String statusCode;
	private String message;

	@Autowired
	private MenuService menuService;

	@Action("toMenuMain")
	public String toMenuMain() {
		return "toMenuMain";
	}

	/**
	 * 加载树形菜单信息
	 * 
	 * @return
	 */
	@Action("queryMenuList")
	public String queryMenuList() {
		menuStr = menuService.queryMenuList();
		return "toMenuLeft";
	}

	/**
	 * 获取菜单信息
	 */
	@Action("findMenu")
	public String findMenu() {
		if (null == menuEntity) {
			menuEntity = new MenuEntity();
		}
		menuEntity = menuService.findMenu(menuEntity);
		return "toMenuRight";
	}

	/**
	 * 更新菜单信息
	 * 
	 * @return
	 */
	@Action("editMenu")
	public String editMenu() {
		return menuService.editMenu(menuEntity);
	}

	/**
	 * 检查菜单名称是否存在
	 * 
	 * @return
	 */
	@Action("checkMenu")
	public String checkMenu() {
		List<MenuEntity> menuOne = menuService.checkMenu(menuEntity);
		if (null != menuOne && menuOne.size() > 0) {
			HttpServletResponse response = ServletActionContext.getResponse();
			response.setContentType("text/html;charset=utf-8");
			PrintWriter out = null;
			try {
				out = response.getWriter();
				out.print("have");
			} catch (IOException e) {
				e.printStackTrace();
			} finally {
				if (null != out)
					out.close();
			}
		}
		return null;
	}

	/**
	 * 增加菜单信息
	 * 
	 * @return
	 */
	@Action("saveMenu")
	public String saveMenu() {
		this.context();
		try {
			menuService.saveMenu(menuEntity);
			this.getResponse().setContentType("text/html;charset=UTF-8");
			this.getResponse().getWriter().write("<script>window.close();</script>");
		} catch (IOException e) {
			e.printStackTrace();
		}
		return null;
	}

	/**
	 * 修改菜单信息
	 * 
	 * @return
	 */
	@Action("modifyMenu")
	public String modifyMenu() {
		menuService.modifyMenu(menuEntity);
		return "toMenuMain";
	}

	public List<MenuEntity> getMenuList() {
		return menuList;
	}

	public void setMenuList(List<MenuEntity> menuList) {
		this.menuList = menuList;
	}

	public String getKey() {
		return key;
	}

	public void setKey(String key) {
		this.key = key;
	}

	public String getMsg() {
		return msg;
	}

	public void setMsg(String msg) {
		this.msg = msg;
	}

	public String getStatusCode() {
		return statusCode;
	}

	public void setStatusCode(String statusCode) {
		this.statusCode = statusCode;
	}

	public String getMessage() {
		return message;
	}

	public void setMessage(String message) {
		this.message = message;
	}

	public String getMenuId() {
		return menuId;
	}

	public void setMenuId(String menuId) {
		this.menuId = menuId;
	}

	public String getMenuStr() {
		return menuStr;
	}

	public void setMenuStr(String menuStr) {
		this.menuStr = menuStr;
	}

	public MenuEntity getMenuEntity() {
		return menuEntity;
	}

	public void setMenuEntity(MenuEntity menuEntity) {
		this.menuEntity = menuEntity;
	}

}
