<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper 
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hzw.ssm.sys.system.dao.SpecialtyInfoMapper">

	<!-- 如果需要查询缓存，将cache的注释去掉即可
	<cache />
	-->

	<!-- 查询子专业类别 -->
	<select id="querySubSpecialtyBySpecialtyId" parameterType="SpecialtyInfoEntity" resultType="SpecialtyInfoEntity">
		select spe_id, spe_name, spe_code, spe_parent, spe_level, spe_remark from
		t_specialty_info
		where rownum = 1 and
		spe_parent=#{spe_id}
	</select>

	<select id="querySubSpecialtyBySpeParentId" parameterType="SpecialtyInfoEntity" resultType="SpecialtyInfoEntity">
		select
			spe_id, spe_name, spe_code, spe_parent, spe_level, spe_remark
		from
			t_specialty_info
		where
			spe_parent = #{spe_parent}
	</select>

	<select id="queryAll" parameterType="SpecialtyInfoEntity" resultType="SpecialtyInfoEntity">
		select
			spe_id,
			spe_name
		from
			t_specialty_info
	</select>

	<select id="queryCountBySpecialtyId" parameterType="SpecialtyInfoEntity" resultType="int">
		select
			count(spe_id)
		from
			t_specialty_info
		where
			spe_parent = #{spe_parent}
	</select>

	<!-- 加载树形专业类别 -->
	<select id="querySpecialtyList" resultType="SpecialtyInfoEntity">
		select
		spe_id, spe_name, spe_code, spe_parent, spe_level, spe_remark from
		t_specialty_info order by to_number(spe_id)
	</select>

	<!-- 根据id获取一条专业类别信息 -->
	<select id="getSpecialtyById" parameterType="SpecialtyInfoEntity" resultType="SpecialtyInfoEntity">
		select
		spe_id, spe_name, spe_code, spe_parent, spe_level, spe_remark from
		t_specialty_info
		where rownum=1
		<if test="spe_id != null">
			and spe_id = #{spe_id}
		</if>
	</select>

	<!-- 根据专业类别名称获取专业类别信息 -->
	<select id="getSpecialtyByName" parameterType="SpecialtyInfoEntity" resultType="SpecialtyInfoEntity">
		select
		spe_id, spe_name, spe_code, spe_parent, spe_level, spe_remark from
		t_specialty_info
		where spe_name=#{spe_name}
	</select>

	<!-- 根据父专业类别id获取信息 -->
	<select id="getSpecialtyByParentId" parameterType="SpecialtyInfoEntity" resultType="SpecialtyInfoEntity">
		select
		spe_id, spe_name, spe_code, spe_parent, spe_level, spe_remark from
		t_specialty_info
		where rownum=1
		and spe_id = #{spe_parent}
	</select>

	<insert id="saveSpecialty" parameterType="SpecialtyInfoEntity">
		INSERT INTO T_SPECIALTY_INFO(
		SPE_ID, SPE_NAME, SPE_CODE, SPE_PARENT
		) VALUES
		(#{spe_id},#{spe_name},#{spe_code},#{spe_parent})
	</insert>

	<!-- 修改专业类别信息 -->
	<update id="modifySpecialty" parameterType="SpecialtyInfoEntity">
		update T_SPECIALTY_INFO set
		SPE_NAME=#{spe_name},SPE_CODE=#{spe_code},SPE_PARENT=#{spe_parent}
		where
		SPE_ID=#{spe_id}
	</update>

	<!-- 根据id删除专业类别信息 -->
	<delete id="deleteSpecialtyById" parameterType="SpecialtyInfoEntity">
		delete from T_SPECIALTY_INFO where SPE_ID = #{spe_id}
	</delete>
</mapper>