package com.hzw.sunflower.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hzw.sunflower.controller.request.LenovoUserQueryReq;
import com.hzw.sunflower.controller.response.*;
import com.hzw.sunflower.dto.AgentUserDTO;
import com.hzw.sunflower.entity.JwtUser;
import com.hzw.sunflower.entity.User;
import com.hzw.sunflower.entity.condition.CompanyOpenCondition;
import com.hzw.sunflower.entity.condition.CompanyUserCondition;
import com.hzw.sunflower.entity.condition.TUserIdentityOpenCondition;

import java.util.List;


/**
 * OpenService接口
 */
public interface CommonOpenService {

    /**
     * 根据userId获取用户权限
     * @param userId
     * @return
     */
    List<RoleVo> getUserRoleByUserId(Long userId);

    /**
     * 根据companyId获取企业信息
     * @param companyId
     * @return
     */
    OpenCompanyVo getCompanyInfoById(Long companyId);


    /**
     * 根据用户id查询用户部门企业信息  投标人
     * @param userId
     * @return
     */
    UserDeptCompanyVo getUserInfoByIdForBidder(Long userId);

    /**
     * 根据用户id查询用户部门企业信息  代理机构
     * @param userId
     * @return
     */
    UserDeptCompanyVo getUserInfoByIdForAgency(Long userId);

    /**
     * 根据用户id查询用户部门企业信息  供应商
     * @param userId
     * @return
     */
    UserDeptCompanyVo getUserInfoByIdForSupplier(Long userId);


    /**
     * 获取企业下的人员信息
     */
    IPage<UserInfoVo> getCompanyUser(CompanyUserCondition condition);


    /**
     * 获取企业下的人员信息
     */
    public UserInfoVo getCompanyUserInfo(CompanyUserCondition condition);



    /**
     * 招标人管理
     * param    keywords(企业名称，信用代码，法定代表人)；
     * @return
     */
    IPage<CompanySuperviseVo> getBidderList(CompanyOpenCondition condition);

    /**
     *  获得单位信息列表
     * @param condition
     * @return
     */
    IPage<CompanySuperviseVo> getCompanyInfoList(CompanyOpenCondition condition);


    /**
     * 招标人用户列表
     *
     * @param con companyId  keywords（姓名/联系方式/企业名称）
     * @return
     */
    IPage<UserTableVo> getBiddingUserList(TUserIdentityOpenCondition con);

    /**
     * 供应商管理
     * param    keywords(企业名称，信用代码，法定代表人)；
     * @return
     */
    IPage<CompanySuperviseVo> getSupplierList(CompanyOpenCondition condition);


    /**
     * 供应商人员列表
     *
     * @param con companyId  keywords（姓名/联系方式/企业名称）
     * @return
     */
    IPage<UserTableVo> getSupplierUserList(TUserIdentityOpenCondition con);


    /**
     * 代理机构管理
     * param    keywords(企业名称，信用代码，法定代表人)；
     * @return
     */
    IPage<CompanySuperviseVo> getAgencyList(CompanyOpenCondition condition);


    /**
     * 代理机构人员列表
     *
     * @param con companyId  keywords（姓名/联系方式/企业名称）
     * @return
     */
    IPage<UserTableVo> getAgencyUserList(TUserIdentityOpenCondition con);


    /**
     * 委托人联想
     *
     * @param lenovoUserQueryReq
     * @return
     */
    List<AgentUserDTO> queryBidderInfo(LenovoUserQueryReq lenovoUserQueryReq, JwtUser jwtUser);



    /**
     * 代理机构联想
     *
     * @param lenovoUserQueryReq
     * @return
     */
    List<AgentUserDTO> queryAgencyInfo(LenovoUserQueryReq lenovoUserQueryReq, JwtUser jwtUser);

    /**
     * 代理机构联想 未脱敏
     *
     * @param lenovoUserQueryReq
     * @return
     */
    AgentUserDTO queryAgencyInfoList(LenovoUserQueryReq lenovoUserQueryReq, JwtUser jwtUser);

    /**
     * 供应商联想
     *
     * @param lenovoUserQueryReq
     * @return
     */
    List<AgentUserDTO> querySupplierInfo(LenovoUserQueryReq lenovoUserQueryReq, JwtUser jwtUser);

    /**
     * 校验用户自动发送权限
     * @param userId
     * @param code
     * @return
     */
    Boolean validAutoByUserId(Long userId, String code);


    /**
     * 校验用户是否能设置每24小时发送
     * @param userId
     * @param code
     * @return
     */
    Boolean valid24HourSendByUserId(Long userId, String code);

    /**
     * 根据用户获取部门处长
     * @param deptCode
     * @return
     */
    List<UserInfoVo> getDirectorByUserId(String deptCode);

    /**
     * 根据标段列表获取最早的时间
     * @param subIdList
     * @return
     */
    EarlySectionTimeVo getEarliestTimeBySectionIds(List<Long> subIdList);

    /**
     * 获取已递交投标文件供应商
     * @param sectionId
     * @param bidRound
     * @return
     */
    List<CompanyHasBidDocVo> getCompanyHasBidDoc(Long sectionId, Integer bidRound);

    /**
     * 根据用户id查询所有处长
     * @param czCodes
     * @param confirmUserId
     * @return
     */
    List<User> getChuZhangByUserId(List<String> czCodes, Long confirmUserId);

    /**
     * 根据角色查询用户
     * @param czCodes
     * @return
     */
    List<User> getUserByRole(List<String> czCodes);

    /**
     * 查询用户id 下 指定部门的处长
     * @param czCodes
     * @param confirmUserId
     * @param departId
     * @return
     */
    List<User> getChuZhangByUserIdDepartId(List<String> czCodes, Long confirmUserId,Long departId);

    /**
     * 查询部门id所在公司的总经理
     * @param czCodes
     * @param departId
     * @return
     */
    List<User> getLeaderByDepartId(List<String> czCodes, Long departId);

    /**
     * 根据用户id判断是否是处长
     * @param confirmUserId
     * @return
     */
    Boolean checkIsCz(Long confirmUserId);

    /**
     * 判断是否可以线上保证金退还
     * @return
     */
    Boolean checkOnlineBondRefund(List<Long> sectionIds,Long companyId);


    /**
     * 判断是否可以线下保证金退还
     * @return
     */
    Boolean checkOfflineBondRefund(List<Long> sectionIds,Long companyId);

    /**
     * 查询用户登录ncc所需数据
     * @param userId
     * @param userIdentity
     * @return
     */
    UserNccInfoVo queryUserNccInfo(Long userId, Integer userIdentity);

    /**
     * 判断用户是否为分公司总经理
     * @param userId
     * @return
     */
    Boolean checkIsFgszjl(Long userId);

    /**
     * 判断用户是否为部门负责人
     * @param userId
     * @return
     */
    Boolean checkIsBmfzr(Long userId);

    /**
     * 判断用户是否拥有某个角色
     * @param confirmUserId
     * @param roleCodes
     * @return
     */
    boolean checkIsRole(Long confirmUserId,List<String> roleCodes);

    boolean checkDepartIsRole(Long confirmUserId, Long departId, List<String> roleCodes);

    List<User> getUserByDepartId(String code, Long confirmUserId, Long deptId);

    List<User> getZJLDepartId(String code, Long deptId);
}
