package com.hzw.sunflower.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.hzw.sunflower.common.BaseBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
* Created by pj on 2023/11/03
*/
@ApiModel("t_seal_bid_win表")
@TableName("t_seal_bid_win")
@Data
public class SealBidWin  extends BaseBean implements Serializable {
    private Long id;

    /**
     * 项目id
     *
     * @mbg.generated
     */
    @ApiModelProperty("申请id")
    private Long applyId;


    /**
     * 中标人id
     *
     * @mbg.generated
     */
    @ApiModelProperty("中标人id")
    private Long bidWinPeopleId;

    @ApiModelProperty("通知书id")
    private Long noticeId;

    /**
     * 未盖章文件
     *
     * @mbg.generated
     */
    @ApiModelProperty("通知书文件id")
    private Long file;

    @ApiModelProperty("是否中标")
    private Integer isWin;

    private static final long serialVersionUID = 1L;
}