package com.hzw.ssm.applets.entity;

import java.util.Date;

import com.hzw.ssm.fw.base.BaseEntity;
import com.hzw.ssm.fw.util.Page;

public class Technical extends BaseEntity{

	/**
	 * 职称修改状态默认1 新增 2修改
	 */
	private String loginType="1";
	/**
	 * 主键
	 */
	private String id;
	
	/**
	 * code
	 */
	private String code;
	
	/**
	 * 职位名称
	 */
	private String technicalTitalName;
	
	/**
	 * 是否删除
	 */
	private String isDel;
	
	private String createId;
	
	private String createName;
	
	private Date createTime;
	
	private String modifyId;
	
	private String modifyName;
	
	private Date modifyTime;
	/**
	 * 职称等级
	 */
	private String expertType;

	/**
	 * 排序
	 */
	private String reorder;
	
	/**
	 * 开标时间排序
	 */
	private String openingreOrder;
	/**
	 * 抽取次数排序
	 */
	private String extractNumOrder;
	
	/**
	 * 审核时间排序
	 */
	private String handletimeOrder;
	
	private Page page;
	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getTechnicalTitalName() {
		return technicalTitalName;
	}

	public void setTechnicalTitalName(String technicalTitalName) {
		this.technicalTitalName = technicalTitalName;
	}

	public String getIsDel() {
		return isDel;
	}

	public void setIsDel(String isDel) {
		this.isDel = isDel;
	}

	public String getExpertType() {
		return expertType;
	}

	public void setExpertType(String expertType) {
		this.expertType = expertType;
	}

	public Page getPage() {
		return page;
	}

	public void setPage(Page page) {
		this.page = page;
	}

	public String getCreateId() {
		return createId;
	}

	public void setCreateId(String createId) {
		this.createId = createId;
	}

	public String getCreateName() {
		return createName;
	}

	public void setCreateName(String createName) {
		this.createName = createName;
	}

	

	public String getModifyId() {
		return modifyId;
	}

	public void setModifyId(String modifyId) {
		this.modifyId = modifyId;
	}

	public String getModifyName() {
		return modifyName;
	}

	public void setModifyName(String modifyName) {
		this.modifyName = modifyName;
	}


	public String getReorder() {
		return reorder;
	}

	public void setReorder(String reorder) {
		this.reorder = reorder;
	}

	public String getOpeningreOrder() {
		return openingreOrder;
	}

	public void setOpeningreOrder(String openingreOrder) {
		this.openingreOrder = openingreOrder;
	}

	public String getExtractNumOrder() {
		return extractNumOrder;
	}

	public void setExtractNumOrder(String extractNumOrder) {
		this.extractNumOrder = extractNumOrder;
	}

	public String getHandletimeOrder() {
		return handletimeOrder;
	}

	public void setHandletimeOrder(String handletimeOrder) {
		this.handletimeOrder = handletimeOrder;
	}

	public String getLoginType() {
		return loginType;
	}

	public void setLoginType(String loginType) {
		this.loginType = loginType;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public Date getModifyTime() {
		return modifyTime;
	}

	public void setModifyTime(Date modifyTime) {
		this.modifyTime = modifyTime;
	}
	
	
	
	
}
