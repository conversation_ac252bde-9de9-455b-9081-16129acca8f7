/*
 * 公司名称：江苏华招网信息技术有限公司
 * 版权信息：江苏华招网信息技术有限公司版权所有
 * 文件名称：GetExpertStatusQuart.java
 * 修改时间：2021年3月3日
 * 修改人：袁辉
 * 修改内容：
 * 跟踪单号：
 * 修改单号 ：
 */

package com.hzw.ssm.quartz;

import java.util.Date;

import com.hzw.ssm.fw.base.BaseAction;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;

import com.hzw.ssm.expert.service.ExpertInfoService;
import com.hzw.ssm.fw.util.DateUtil;

/**
 * <一句话功能简述> 定时更新专家状态
 * <功能详细描述>
 * <AUTHOR>
 * @version V1.0[产品/模块版本]
 */
public class GetExpertStatusQuart {
	/* ******************************************************************************************* */
	/*                                       序列化代码区域                                                                                                             */
	/* ******************************************************************************************* */

	@Autowired
	private ExpertInfoService expertInfoService;

	/** 日志对象 */
	protected static final Log log = LogFactory.getLog(GetExpertStatusQuart.class);
	
	public void work() {
		log.info("-----恢复专家暂停定时器启动-----");
		//获取当前时间
		String newDate = DateUtil.dateToString(new Date(), "yyyy-MM-dd");
		//如果暂停时间小于等于当前时间，将所有暂停的专家设置为可抽取
		expertInfoService.updateExpertStatusByDate(newDate);
		log.info("-----恢复专家暂停定时器结束-----");
	}
	/* ******************************************************************************************* */
	/*                                       全局变量声明区域                                                                                                         */
	/* ******************************************************************************************* */

	/* ******************************************************************************************* */
	/*                                       公共函数声明区域                                                                                                         */
	/* ******************************************************************************************* */

	/* ******************************************************************************************* */
	/*                                       私有函数声明区域                                                                                                         */
	/* ******************************************************************************************* */
}
