package com.hzw.ssm.expert.action;

import java.util.ArrayList;
import java.util.List;

import javax.servlet.http.HttpSession;

import org.apache.commons.lang3.StringUtils;
import org.apache.struts2.convention.annotation.Action;
import org.apache.struts2.convention.annotation.Namespace;
import org.apache.struts2.convention.annotation.ParentPackage;
import org.apache.struts2.convention.annotation.Result;
import org.apache.struts2.convention.annotation.Results;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

import com.hzw.ssm.expert.entity.ExpertRecommendEntity;
import com.hzw.ssm.expert.service.ExpertRecommendService;
import com.hzw.ssm.fw.base.BaseAction;
import com.hzw.ssm.sys.user.entity.UserEntity;

@Namespace("/expertRecommend")
@ParentPackage(value = "default")
@Results({ @Result(	name = "expertplanadd",
					location = "/jsp/expert/expertplanadd.jsp"),
		@Result(name = "planList",
				location = "/jsp/expert/planList.jsp"),
		@Result(name = "splitRecommend",
				location = "/jsp/expert/splitRecommend.jsp"),
		@Result(name = "expertRecommendQuery",
				location = "/jsp/expert/expertRecommendQuery.jsp"),
		@Result(name = "expertRecommStatics",
		location = "/jsp/expert/expertRecommStatics.jsp"),
		@Result(name = "expertRecommDeatil",
		location = "/jsp/expert/expertRecommDeatil.jsp")})
/**
 * 专家推荐相关Action
 */
public class ExpertRecommendAction extends BaseAction {

	private static final long serialVersionUID = -8823858495799309882L;
	// 入参
	private ExpertRecommendEntity entity;
	// 出参
	private ExpertRecommendEntity result;
	// 出参集合
	private List<ExpertRecommendEntity> resultList;
	// 业务员集合
	private List<UserEntity> salesManList;
	// 各种id
	private String key;

	/**
	 * 推荐业务员名称
	 */
	private String salesName;

	@Autowired
	private ExpertRecommendService recommendService;

	/**
	 * 专家推荐--计划列表
	 * 
	 * @return
	 * @throws Exception
	 */
	@Action("planList")
	public String expertInfoCompleteList() {
		// 1.默认查询赋值
		if (entity == null) {
			entity = new ExpertRecommendEntity();

		}
		entity.setPage(this.getPage());
		// 2.查询
		resultList = recommendService.queryPlans(entity);
		// 3.返回页面
		return "planList";
	}
	/**
	 * 专家推荐--专家推荐查询
	 * 
	 * @return
	 * @throws Exception
	 */
	@Action("queryExpertList")
	public String queryExpertList() {
		this.context();
		// 1.默认查询赋值
		if (entity == null) {
			entity = new ExpertRecommendEntity();
			
		}
		entity.setPage(this.getPage());
		// 2.查询
		
		HttpSession session = getRequest().getSession();
		UserEntity info = (UserEntity) session.getAttribute("userInfo");
		entity.setUserId(info.getUser_id());

		resultList = recommendService.queryExpert(entity);
		// 3.返回页面
		return "expertRecommendQuery";
	}
	/**
	 * 专家推荐--专家推荐统计
	 * 
	 * @return
	 * @throws Exception
	 */
	@Action("queryExpertStatics")
	public String queryExpertStatics() {
		// 1.默认查询赋值
		if (entity == null) {
			entity = new ExpertRecommendEntity();
			
		}
		// 2.查询
		entity.setPage(this.getPage());
		resultList = recommendService.queryExpertStatics(entity);
		// 3.返回页面
		return "expertRecommStatics";
	}
	/**
	 * 专家推荐--专家推荐统计详情
	 * 
	 * @return
	 * @throws Exception
	 */
	@Action("queryExpertDetail")
	public String queryExpertDetail() {
		this.context();
		// 1.默认查询赋值
		if (entity == null) {
			entity = new ExpertRecommendEntity();
			
		}
		entity.setPage(this.getPage());
		// 2.查询
		entity.setStatus("3,8");
		resultList = recommendService.queryExpert(entity);
		// 3.返回页面
		return "expertRecommDeatil";
	}

	/**
	 * 专家推荐--新增/修改单个计划页面
	 * 
	 * @return
	 * @throws Exception
	 */
	@Action("toPlanAdd")
	public String toPlanAdd() {
		// 1.默认查询赋值
		if (entity == null) {
			entity = new ExpertRecommendEntity();

		}
		// 2.查询
		if (StringUtils.isEmpty(key)) {
			// 新增页面
			return "expertplanadd";
		}
		// 修改页面
		entity.setId(key);
		List<ExpertRecommendEntity> entityList = recommendService.queryPlans(entity);
		if (!CollectionUtils.isEmpty(entityList)) {
			entity = entityList.get(0);
		}
		// 3.返回页面
		return "expertplanadd";
	}

	/**
	 * 专家推荐--去拆分推荐人页面页面
	 * 
	 * @return
	 * @throws Exception
	 */
	@Action("toSpiltRecommender")
	public String toSpiltRecommender() {
		// 1.默认查询赋值
		if (entity == null) {
			entity = new ExpertRecommendEntity();

		}
		// 2.查询
		// 2.1查询推荐人记录
		entity.setPlanId(key);
		// 所有的推荐人纪录
		List<ExpertRecommendEntity> resultAllList = recommendService.queryRecommenders(entity);
		List<ExpertRecommendEntity> queryList = new ArrayList<>();
		// 2.2 查询业务员
		UserEntity user = new UserEntity();
		// 所有的业务员
		salesManList = recommendService.querySalesMan(user);
		// 2.3 查询plan
		ExpertRecommendEntity planEntity=new ExpertRecommendEntity();
		planEntity.setId(key);
		List<ExpertRecommendEntity> plans = recommendService.queryPlans(planEntity);
		// 3 计算剩余计划推荐人数
		if (!CollectionUtils.isEmpty(plans)) {
			// 总计划人数
			Integer count = plans.get(0).getRecommendNumber();
			// 已拆分人数
			Integer number = 0;
			if (!CollectionUtils.isEmpty(resultAllList)) {

				for (ExpertRecommendEntity recommend : resultAllList) {
					// 返回集添加
					if (StringUtils.isNotEmpty(salesName) && salesName.equals(recommend.getRecommenderName())) {
						queryList.add(recommend);
					}
					if (recommend.getRecommendNumber() == null) {
						continue;
					}
					number += recommend.getRecommendNumber();
				}
			}
			entity.setSurplusNumber(count - number);
		}
		if (StringUtils.isNotEmpty(salesName)) {
			resultList = queryList;
		} else {
			resultList = resultAllList;
		}
		// 3.返回页面
		return "splitRecommend";
	}

	/**
	 * 专家推荐--新增/修改单个计划
	 * 
	 * @return
	 * @throws Exception
	 */
	@Action("addPlan")
	public String addPlan() {
		this.context();
		// 1.默认查询赋值
		if (entity == null) {
			entity = new ExpertRecommendEntity();

		}
		// 当前session
		HttpSession session = getRequest().getSession();
		// 当前用户信息
		UserEntity info = (UserEntity) session.getAttribute("userInfo");
		entity.setCreate_id(info.getUser_id());
		entity.setModify_id(info.getUser_id());
		// 2.新增
		recommendService.addPlan(entity);
		// 3.返回页面
		return null;
	}

	/**
	 * 专家推荐--新增/修改单个计划
	 * 
	 * @return
	 * @throws Exception
	 */
	@Action("deleteRecommender")
	public String deleteRecommender() {
		this.context();
		// 1.默认查询赋值
		if (entity == null) {
			entity = new ExpertRecommendEntity();

		}
		// 当前session
		HttpSession session = getRequest().getSession();
		// 当前用户信息
		UserEntity info = (UserEntity) session.getAttribute("userInfo");
		entity.setModify_id(info.getUser_id());
		// 2.新增
		recommendService.deleteRecommender(entity);
		// 3.返回页面
		return null;
	}
	/**
	 * 专家推荐--删除单个推荐人
	 * 
	 * @return
	 * @throws Exception
	 */
	@Action("addRecommender")
	public String addRecommender() {
		this.context();
		// 1.默认查询赋值
		if (entity == null) {
			entity = new ExpertRecommendEntity();
			
		}
		// 当前session
		HttpSession session = getRequest().getSession();
		// 当前用户信息
		UserEntity info = (UserEntity) session.getAttribute("userInfo");
		entity.setCreate_id(info.getUser_id());
		entity.setModify_id(info.getUser_id());
		// 2.新增
		recommendService.addRecommender(entity);
		// 3.返回页面
		return null;
	}

	/**
	 * @return the entity
	 */
	public ExpertRecommendEntity getEntity() {
		return entity;
	}

	/**
	 * @param entity
	 *            the entity to set
	 */
	public void setEntity(ExpertRecommendEntity entity) {
		this.entity = entity;
	}

	/**
	 * @return the result
	 */
	public ExpertRecommendEntity getResult() {
		return result;
	}

	/**
	 * @param result
	 *            the result to set
	 */
	public void setResult(ExpertRecommendEntity result) {
		this.result = result;
	}

	/**
	 * @return the resultList
	 */
	public List<ExpertRecommendEntity> getResultList() {
		return resultList;
	}

	/**
	 * @param resultList
	 *            the resultList to set
	 */
	public void setResultList(List<ExpertRecommendEntity> resultList) {
		this.resultList = resultList;
	}

	/**
	 * @return the key
	 */
	public String getKey() {
		return key;
	}

	/**
	 * @param key
	 *            the key to set
	 */
	public void setKey(String key) {
		this.key = key;
	}

	/**
	 * @return the salesManList
	 */
	public List<UserEntity> getSalesManList() {
		return salesManList;
	}

	/**
	 * @param salesManList
	 *            the salesManList to set
	 */
	public void setSalesManList(List<UserEntity> salesManList) {
		this.salesManList = salesManList;
	}

	/**
	 * @return the salesName
	 */
	public String getSalesName() {
		return salesName;
	}

	/**
	 * @param salesName
	 *            the salesName to set
	 */
	public void setSalesName(String salesName) {
		this.salesName = salesName;
	}

}
