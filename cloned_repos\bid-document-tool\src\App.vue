<template>
  <div>
      <el-steps
        class="mb-4 step"
        :space="200"
        :active="activeName"
        simple
      >
        <el-step title="导入招标文件" :icon="UploadFilled" />
        <el-step title="投标文件编制" :icon="Edit" />
        <el-step title="文件生成导出" :icon="Download" />
      </el-steps>
      <!-- 显示组件 -->
      <ImportFile
        v-show="activeName === 1"
        @nextStep="nextStep" 
        @updateHtml="updateHtml" 
        :fileHtml="fileHtml" 
      ></ImportFile>
      <EditFile
        :key="keyCount"
        v-show="activeName === 2"
        @nextStep="nextStep" 
        @updateHtml="updateHtml" 
        :fileHtml="fileHtml" 
      ></EditFile>
      <ExportFile
        :key="keyCount"
        v-show="activeName === 3"
        @nextStep="nextStep" 
        @updateHtml="updateHtml" 
        :fileHtml="fileHtml" 
      ></ExportFile>

      
  </div>
</template>

<script setup>
import ImportFile from './pages/importFile.vue'
import ExportFile from './pages/exportFile.vue'
import EditFile from './pages/editFile.vue'
import { ref } from 'vue'
import { Edit, Download, UploadFilled } from '@element-plus/icons-vue'
const components = [
   {
    value: 1,
    component: ImportFile
   },
    {
      value: 2,
      component: EditFile
    },
    {
      value: 3,
      component: ExportFile
    }
]
const activeName = ref(1)
const nextStep = (val)=>{
    activeName.value = val
}
const keyCount = ref(1)
const fileHtml = ref(null)
const updateHtml = (val)=>{
  fileHtml.value = val
  keyCount.value++
}

</script>

<style>
.step{
  max-width: 100%;
  position: sticky;
  top: 0;
  z-index: 2;
  padding: 40px 8%;
}
</style>
