package com.hzw.sunflower.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date ：Created in 2022/8/1 10:32
 * @description：自动关联dto
 * @modified By：`
 * @version: 1.0
 */
@ApiModel(description = "自动关联dto")
@Data
public class AutoRelationDto implements Serializable {

    @ApiModelProperty("保证金类型 0不收取 1定额 2比例")
    private Integer bondType;

    @ApiModelProperty("是否已过开标时间 0否 1是")
    private Integer isOverSubmitEndTime;

    @ApiModelProperty("保证金到账日期是否超过开标时间 0否 1是")
    private Integer isOverBondTime;

    @ApiModelProperty("标包关联数量")
    private Integer relateCount;

    @ApiModelProperty("是否已购标 0否 1是")
    private Integer downloadFlag;

    @ApiModelProperty("标段状态")
    private Integer status;

}
