///***
/// * 信息订阅 - 设置关键词的页面
/// *
/// * <AUTHOR>
/// * @date 20191016
/// */

import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:provider/provider.dart';

import 'package:flutter_main_app/serviceLocator.dart';
import 'package:flutter_main_app/src/constants/theme.dart';
import 'package:flutter_main_app/src/utils/ScreenUtils.dart';

import 'package:flutter_main_app/src/viewModels/customInfo/CustomInfoViewModel.dart';

import 'package:flutter_main_app/src/models/customInfo/FilterCriteriaUIModel.dart';
import 'package:flutter_main_app/src/models/common/LabelValuePairModel.dart';
import 'package:flutter_main_app/src/models/common/CheckBoxItemModel.dart';

import 'package:flutter_main_app/src/components/CustomAppBar.dart';
import 'package:flutter_main_app/src/components/CustomSafeArea.dart';
import 'package:flutter_main_app/src/components/CustomListTile.dart';

import 'package:flutter_main_app/src/widgets/info/components/FilterCriteria.dart';

final List<LabelValuePairModel<String>> _bulletinTypeNameSetting = [
  new LabelValuePairModel<String>(
    value: '招标公告',
    label: '招标公告',
  ),
  new LabelValuePairModel<String>(
    value: '资格预审公告',
    label: '资格预审公告',
  ),
  new LabelValuePairModel<String>(
    value: '中标候选人公示',
    label: '中标候选人公示',
  ),
  new LabelValuePairModel<String>(
    value: '中标结果公示',
    label: '中标结果公示',
  ),
  new LabelValuePairModel<String>(
    value: '变更公告公示',
    label: '变更公告公示',
  ),
];

final List<LabelValuePairModel<String>> _classifyNameSetting = [
  new LabelValuePairModel<String>(
    value: '勘察',
    label: '勘察',
  ),
  new LabelValuePairModel<String>(
    value: '设计',
    label: '设计',
  ),
  new LabelValuePairModel<String>(
    value: '施工',
    label: '施工',
  ),
  new LabelValuePairModel<String>(
    value: '监理',
    label: '监理',
  ),
  new LabelValuePairModel<String>(
    value: '投资',
    label: '投资',
  ),
  new LabelValuePairModel<String>(
    value: '规划',
    label: '规划',
  ),
  new LabelValuePairModel<String>(
    value: '设备',
    label: '设备',
  ),
  new LabelValuePairModel<String>(
    value: '材料',
    label: '材料',
  ),
  new LabelValuePairModel<String>(
    value: '其他',
    label: '其他',
  ),
];

final List<LabelValuePairModel<String>> _regionCodeSetting = [
  new LabelValuePairModel<String>(
    value: '320100',
    label: '南京市',
  ),
  new LabelValuePairModel<String>(
    value: '320200',
    label: '无锡市',
  ),
  new LabelValuePairModel<String>(
    value: '320300',
    label: '徐州市',
  ),
  new LabelValuePairModel<String>(
    value: '320400',
    label: '常州市',
  ),
  new LabelValuePairModel<String>(
    value: '320500',
    label: '苏州市',
  ),
  new LabelValuePairModel<String>(
    value: '320600',
    label: '南通市',
  ),
  new LabelValuePairModel<String>(
    value: '320700',
    label: '连云港市',
  ),
  new LabelValuePairModel<String>(
    value: '320800',
    label: '淮安市',
  ),
  new LabelValuePairModel<String>(
    value: '320900',
    label: '盐城市',
  ),
  new LabelValuePairModel<String>(
    value: '321000',
    label: '扬州市',
  ),
  new LabelValuePairModel<String>(
    value: '321100',
    label: '镇江市',
  ),
  new LabelValuePairModel<String>(
    value: '321200',
    label: '泰州市',
  ),
  new LabelValuePairModel<String>(
    value: '321300',
    label: '宿迁市',
  ),
];

final List<LabelValuePairModel<String>> _platformCodeSetting = [
  new LabelValuePairModel<String>(
    value: '江苏住建',
    label: '江苏住建',
  ),
  new LabelValuePairModel<String>(
    value: '江苏水利',
    label: '江苏水利',
  ),
  new LabelValuePairModel<String>(
    value: '江苏交通',
    label: '江苏交通',
  ),
  new LabelValuePairModel<String>(
    value: '南京货物',
    label: '南京货物',
  ),
  new LabelValuePairModel<String>(
    value: '江苏悦达集团',
    label: '江苏悦达集团',
  ),
  new LabelValuePairModel<String>(
    value: '徐矿集团',
    label: '徐矿集团',
  ),
  new LabelValuePairModel<String>(
    value: 'e交易平台',
    label: 'e交易平台',
  ),
  new LabelValuePairModel<String>(
    value: '中招联合(江苏)',
    label: '中招联合(江苏)',
  ),
  new LabelValuePairModel<String>(
    value: '江苏交通控股',
    label: '江苏交通控股',
  ),
  new LabelValuePairModel<String>(
    value: '南京鑫智联',
    label: '南京鑫智联',
  ),
];

final List<LabelValuePairModel<String>> _fundRangeSetting = [
  new LabelValuePairModel<String>(
    value: 'A',
    label: '500万以下',
  ),
  new LabelValuePairModel<String>(
    value: 'B',
    label: '1000万',
  ),
  new LabelValuePairModel<String>(
    value: 'C',
    label: '5000万',
  ),
  new LabelValuePairModel<String>(
    value: 'D',
    label: '1亿',
  ),
  new LabelValuePairModel<String>(
    value: 'E',
    label: '1亿以上',
  ),
];

final List<LabelValuePairModel<String>> _fundSourceSetting = [
  new LabelValuePairModel<String>(
    value: '国有资金',
    label: '国有资金',
  ),
  new LabelValuePairModel<String>(
    value: '自筹资金',
    label: '自筹资金',
  ),
  new LabelValuePairModel<String>(
    value: '私有资金',
    label: '私有资金',
  ),
  new LabelValuePairModel<String>(
    value: '国际组织或外国政府资金',
    label: '国际组织或外国政府资金',
  ),
  new LabelValuePairModel<String>(
    value: '境外资金',
    label: '境外资金',
  ),
];

class InfoKeywordSettingsWidget extends StatefulWidget {
  const InfoKeywordSettingsWidget({Key key}) : super(key: key);

  /// * 路由名称
  static const String _routeName = 'InfoKeywordSettings';
  static String get routeName => _routeName;

  @override
  _InfoKeywordSettingsWidgetState createState() => _InfoKeywordSettingsWidgetState();
}

class _InfoKeywordSettingsWidgetState extends State<InfoKeywordSettingsWidget> {

  final GlobalKey<ScaffoldState> _scaffoldStateGlobalKey = GlobalKey<ScaffoldState>();

  final CustomInfoViewModel _model = serviceLocator<CustomInfoViewModel>();

  /// * 暂存订阅关键词
  final List<String> _keywords = [];

  /// * 筛选项的 UI 数据列表
  List<FilterCriteriaUIModel> _filterCriteriaSettings;

  /// * 对应最多 3 个的关键词
  final List<FocusNode> _focusNodes = [
    FocusNode(),
    FocusNode(),
    FocusNode(),
  ];

  @override
  void initState() {
    super.initState();

    this._keywords.addAll(_model.keywords);

    if (this._keywords.isEmpty) {
      this._keywords.add('');
    }

    this._filterCriteriaSettings = [
      FilterCriteriaUIModel(
        type: '信息类型',
        checkboxWidth: 190.0,
        fieldName: 'bulletinTypeName',
        criteria: [...?_model.params.bulletinTypeName],
      ),
      FilterCriteriaUIModel(
        type: '招标专业',
        checkboxWidth: 120.0,
        fieldName: 'classifyName',
        criteria: [...?_model.params.classifyName],
      ),
      FilterCriteriaUIModel(
        type: '项目地区',
        checkboxWidth: 150.0,
        fieldName: 'regionCode',
        criteria: [...?_model.params.regionCode],
      ),
      FilterCriteriaUIModel(
        type: '对接平台',
        checkboxWidth: 200.0,
        fieldName: 'platformCode',
        criteria: [...?_model.params.platformCode],
      ),
      FilterCriteriaUIModel(
        type: '合同估算价',
        checkboxWidth: 160.0,
        fieldName: 'fundRange',
        criteria: [...?_model.params.fundRange],
      ),
      FilterCriteriaUIModel(
        type: '资金来源',
        checkboxWidth: 240.0,
        fieldName: 'fundSource',
        criteria: [...?_model.params.fundSource],
      ),
      FilterCriteriaUIModel(
        type: '推送时间',
        fieldName: 'publishTime',
        criteria: [...?_model.publishTime],
      ),
    ];
  }

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider<CustomInfoViewModel>.value(
          value: serviceLocator<CustomInfoViewModel>(),
        ),
      ],
      child: Scaffold(
        key: _scaffoldStateGlobalKey,
        appBar: CustomAppBar(
          title: '信息订阅',
          leadingOnPressed: () {
            this._handleConfirmButtonClick(context);
          },
        ),
        body: CustomSafeArea(
          backgroundColor: themeContentBackgroundColor,
          child: GestureDetector(
            onTap: () {
              _handleFocusNodeBlur(context);
            },
            child: Container(
              width: ScreenUtils.screenWidth,
              decoration: BoxDecoration(
                color: themeContentBackgroundColor,
              ),
              child: ListView(
                physics: BouncingScrollPhysics(),
                children: <Widget>[
                  /// * 订阅关键词
                  Container(
                    padding: const EdgeInsets.fromLTRB(15.0, 14.0, 15.0, 16.0),
                    width: ScreenUtils.screenWidth,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: <Widget>[
                        Padding(
                          padding: const EdgeInsets.only(bottom: 4.0),
                          child: Text(
                            '订阅关键词',
                            style: TextStyle(
                              color: themeTitleColor,
                              fontSize: 14.0,
                            ),
                          ),
                        ),
                        ...this._buildKeywordInputs(context),
                        Padding(
                          padding: const EdgeInsets.fromLTRB(22.0, 16.0, 0.0, 0.0),
                          child: GestureDetector(
                            onTap: () {
                              this._handleAddKeywordButtonClick();

                              _handleFocusNodeBlur(context);
                            },
                            child: Row(
                              children: <Widget>[
                                Icon(
                                  Icons.add,
                                  color: const Color(0xffeb5e5e),
                                  size: 24.0,
                                ),
                                Text(
                                  '添加关键词',
                                  style: TextStyle(
                                    color: const Color(0xffeb5e5e),
                                    fontSize: 12.0,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  /// * 筛选项
                  Container(
                    width: ScreenUtils.screenWidth,
                    decoration: BoxDecoration(
                      border: Border(
                        top: BorderSide(
                          width: 10.0,
                          color: themeBackgroundColor,
                        ),
                      ),
                    ),
                    child: Consumer<CustomInfoViewModel>(
                      builder: (context, model, child) {
                        return Column(
                          children: <Widget>[
                            ...this._buildFilterCriterion(context, model),
                            /// * 推送时间
                            GestureDetector(
                              onTap: () {
                                this._handlePublishTimeTap(model);

                                _handleFocusNodeBlur(context);
                              },
                              child: Container(
                                decoration: BoxDecoration(
                                  border: Border(
                                    bottom: themeBorderSide,
                                  ),
                                ),
                                child: CustomListTile(
                                  minHeight: 50.0,
                                  title: '推送时间',
                                  infos: <Widget>[
                                    model.publishTime.length == 2
                                      ? Text(
                                          '${model.publishTime[0]}点-${model.publishTime[1]}点实时推送',
                                          style: themeTipsTextStyle,
                                        )
                                      : Container(),
                                  ],
                                  trailing: Icon(
                                    Icons.arrow_forward_ios,
                                    size: 14.0,
                                    color: Color(0xff999999),
                                  ),
                                  isFirstChild: true,
                                ),
                              ),
                            ),
                          ],
                        );
                      },
                    ),
                  ),
                  /// * 确定按钮
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: <Widget>[
                      Container(
                        margin: const EdgeInsets.only(top: 50.0),
                        width: 335.0,
                        height: 40.0,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.all(
                            Radius.circular(themeBorderRadius),
                          ),
                        ),
                        child: FlatButton(
                          color: themeActiveColor,
                          textColor: const Color(0xffffffff),
                          onPressed: () {
                            this._handleConfirmButtonClick(context);
                          },
                          child: Text(
                            '确定',
                            style: TextStyle(
                              fontSize: 14.0,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  ///***
  /// * 生成 订阅关键词 的输入框
  /// *
  /// * @param {BuildContext} context
  /// *
  /// * @return {List<Widget>}
  /// */
  List<Widget> _buildKeywordInputs(
    BuildContext context,
  ) {
    final List<Widget> keywordInputWidgets = [];

    /// * 根据 _keywords 生成对应的 widgets
    for (int i = 0, len = _keywords.length; i < len; i++) {
      final TextEditingController controller = TextEditingController();

      controller.text = _keywords[i];

      /// * 将光标置于文本末尾
      controller.selection = TextSelection.collapsed(offset: _keywords[i].length);

      Widget keywordInputWidget = Padding(
        padding: const EdgeInsets.only(top: 10.0),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            /// * 编号
            SizedBox(
              width: 22.0,
              child: Text(
                '${i + 1}',
                style: TextStyle(
                  color: themeHintTextColor,
                  fontSize: 12.0,
                ),
              ),
            ),
            /// * 输入框
            Expanded(
              child: Container(
                constraints: BoxConstraints.tight(Size.fromHeight(35.0)),
                decoration: BoxDecoration(
                  color: themeBackgroundColor,
                  borderRadius: BorderRadius.circular(themeBorderRadius),
                ),
                child: Row(
                  children: <Widget>[
                    /// * 输入框
                    Expanded(
                      child: TextField(
                        controller: controller,
                        focusNode: _focusNodes[i],
                        onChanged: (keyword) async {
                          setState(() {
                            _keywords[i] = keyword;
                          });
                        },
                        decoration: InputDecoration(
                          contentPadding: const EdgeInsets.only(left: 13.0),
                          hintText: '请输入关键字',
                          hintStyle: TextStyle(
                            fontSize: 12.0,
                            color: themeHintTextColor,
                          ),
                          border: OutlineInputBorder(
                            borderSide: BorderSide.none,
                          ),
                        ),
                        style: TextStyle(
                          fontSize: 12.0,
                          color: themeTextColor,
                        ),
                      ),
                    ),
                    /// * 删除按钮
                    Container(
                      margin: const EdgeInsets.symmetric(vertical: 8.0),
                      constraints: BoxConstraints.tight(Size.fromWidth(38.0)),
                      decoration: BoxDecoration(
                        border: Border(
                          left: themeBorderSide,
                        ),
                      ),
                      child: IconButton(
                        icon: Icon(Icons.close),
                        color: const Color(0xffeb5e5e),
                        iconSize: 16.0,
                        padding: const EdgeInsets.all(0.0),
                        onPressed: () async {
                          if (_keywords.length == 1) {
                            setState(() {
                              _keywords[0] = '';
                            });
                          } else {
                            setState(() {
                              _keywords.removeAt(i);
                            });
                          }
                        },
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      );

      keywordInputWidgets.add(keywordInputWidget);
    }

    return keywordInputWidgets;
  }

  ///***
  /// * 添加关键词 按钮点击事件
  /// *
  /// * @param {CustomInfoViewModel}} customInfoViewModel
  /// */
  void _handleAddKeywordButtonClick() {
    const int maxKeywordsCount = 3;
    final int keywordsCount = _keywords.length;

    /// * 如果没有一个关键词，添加一个空的关键词
    if (keywordsCount == 0) {
      setState(() {
        _keywords.add('');
      });

      return;
    }

    /// * 如果已经有一个空的关键词输入框，则不予回应
    if (_keywords[keywordsCount - 1] == '') {
      _scaffoldStateGlobalKey.currentState.showSnackBar(
        SnackBar(
          content: Text('请先填写当前关键词'),
          duration: Duration(seconds: 1),
        ),
      );

      return;
    }

    /// * 如果已经达到最大可添加关键词数，则不予回应
    if (keywordsCount == maxKeywordsCount) {
      _scaffoldStateGlobalKey.currentState.showSnackBar(
        SnackBar(
          content: Text('最多填写3个关键词'),
          duration: Duration(seconds: 1),
        ),
      );

      return;
    }

    setState(() {
      _keywords.add('');
    });
  }

  ///***
  /// * 生成 筛选项 条目
  /// *
  /// * @param {BuildContext} context
  /// * @param {CustomInfoViewModel} customInfoViewModel
  /// *
  /// * @return {List<Widget>}
  /// */
  List<Widget> _buildFilterCriterion(
    BuildContext context,
    CustomInfoViewModel customInfoViewModel,
  ) {
    final List<Widget> filterCriterionWidgets = [];

    List<LabelValuePairModel<String>> temporarySettings;

    /// * 排除掉 推送时间
    for (int i = 0, len = this._filterCriteriaSettings.length - 1; i < len; i++) {
      final FilterCriteriaUIModel currentSettings = this._filterCriteriaSettings[i];
      final List<CheckBoxItemModel<String>> checkboxSettings = [];

      switch (currentSettings.fieldName) {
        case 'bulletinTypeName':
          temporarySettings = _bulletinTypeNameSetting;
          break;
        case 'classifyName':
          temporarySettings = _classifyNameSetting;
          break;
        case 'regionCode':
          temporarySettings = _regionCodeSetting;
          break;
        case 'platformCode':
          temporarySettings = _platformCodeSetting;
          break;
        case 'fundRange':
          temporarySettings = _fundRangeSetting;
          break;
        case 'fundSource':
          temporarySettings = _fundSourceSetting;
          break;
        default:
          temporarySettings = [];
          break;
      }

      temporarySettings.forEach((temporarySetting) {
        final CheckBoxItemModel<String> setting = CheckBoxItemModel<String>(
          value: temporarySetting.value,
          label: temporarySetting.label,
          isChecked: currentSettings.criteria.indexOf(temporarySetting.value) != -1,
        );

        checkboxSettings.add(setting);
      });

      ///***
      /// * 保存用户选择的筛选条件
      /// *
      /// * @param {List<String>} criteria
      /// */
      void onFilterCriteriaChange(List<String> criteria) {
        customInfoViewModel.setSpecifiedParam(currentSettings.fieldName, criteria);
      }

      Widget filterCriteriaWidget = GestureDetector(
        onTap: () {
          _handleFocusNodeBlur(context);
        },
        child: FilterCriteria(
          type: currentSettings.type,
          checkboxWidth: currentSettings.checkboxWidth,
          criteria: currentSettings.criteria,
          settings: checkboxSettings,
          onChange: onFilterCriteriaChange,
        ),
      );

      filterCriterionWidgets.add(filterCriteriaWidget);
    }

    return filterCriterionWidgets;

  }

  ///***
  /// * 选择推送时间
  /// * 获取当前推送时间 => 打开选择器 => 当选择器返回新的结果时，更新推送时间
  /// *
  /// * @param {CustomInfoViewModel} customInfoViewModel
  /// */
  void _handlePublishTimeTap(
    CustomInfoViewModel customInfoViewModel,
  ) {
    int _startTime = 0;
    int _endTime = 0;

    if (this._model.publishTime.length == 2) {
      _startTime = int.parse(this._model.publishTime[0]);
      _endTime = int.parse(this._model.publishTime[1]) - 1;
    }

    final _TimeRangePicker _timeRangePicker = _TimeRangePicker(
      startTime: _startTime,
      endTime: _endTime,
      scaffoldKey: _scaffoldStateGlobalKey,
    );

    // 时间选择框
    final Future<List<int>> modalPopup = showCupertinoModalPopup<List<int>>(
      context: context,
      builder: (BuildContext context) {
        return _timeRangePicker;
      },
    );

    modalPopup.then((List<int> timeRangeIndices) {
      /// * 确认设置选中项
      if (timeRangeIndices != null) {
        final List<String> criteria = [];

        criteria.add(timeRangeIndices[0].toString());
        criteria.add((timeRangeIndices[1] + 1).toString());

        customInfoViewModel.publishTime = criteria;
      }
    });
  }

  ///***
  /// * 确定 按钮点击事件
  /// *
  /// * @param {BuildContext} context
  /// */
  void _handleConfirmButtonClick(BuildContext context) {
    if (_keywords[_keywords.length - 1].isEmpty) {
      _keywords.removeAt(_keywords.length - 1);
    }

    _model.keywords = _keywords;
    _model.setSpecifiedParam('currentPage', '1');

    Navigator.of(context).pop();
  }

  ///***
  /// * 点击键盘外，则文本框失焦
  /// *
  /// * @param {BuildContext} context
  /// */
  void _handleFocusNodeBlur(BuildContext context) {
    FocusScope.of(context).unfocus();
  }

}

///***
/// * 推送时间的选择器
/// * 更准确的说是两列24小时制小时数的 Picker
/// * 要求结束时间大于开始时间，对应到下标则是结束时间的下标大于等于开始时间的下标
/// */
class _TimeRangePicker extends StatefulWidget {
  _TimeRangePicker({
    Key key,
    this.startTime,
    this.endTime,
    this.scaffoldKey,
  }) : super(key: key);

  final int startTime;

  final int endTime;

  final GlobalKey<ScaffoldState> scaffoldKey;

  @override
  _TimeRangePickerState createState() => _TimeRangePickerState();
}

class _TimeRangePickerState extends State<_TimeRangePicker> {

  int _startTime;
  int _endTime;

  FixedExtentScrollController _startTimeController;
  FixedExtentScrollController _endTimeController;

  @override
  void initState() {
    super.initState();

    this._startTime = widget.startTime;
    this._endTime = widget.endTime;

    this._startTimeController = FixedExtentScrollController(initialItem: _startTime);
    this._endTimeController = FixedExtentScrollController(initialItem: _endTime);
  }

  @override
  void dispose() {
    this._startTimeController.dispose();
    this._endTimeController.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      /// * use Material Widget to override the default text style
      child: Container(
        width: ScreenUtils.screenWidth,
        color: themeContentBackgroundColor,
        child: SafeArea(
          top: false,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              /// * 功能按钮 - 取消|确定
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 30.0),
                width: ScreenUtils.screenWidth,
                height: 42.0,
                color: const Color(0xffe5e5e5),
                child: Row(
                  mainAxisSize: MainAxisSize.max,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: <Widget>[
                    GestureDetector(
                      child: Text(
                        '取消',
                        style: TextStyle(
                          fontSize: 14.0,
                          color: const Color(0xff999999),
                        ),
                      ),
                      onTap: () {
                        Navigator.of(context).pop();
                      },
                    ),
                    GestureDetector(
                      child: Text(
                        '确定',
                        style: TextStyle(
                          fontSize: 14.0,
                          color: themeActiveColor,
                        ),
                      ),
                      onTap: () {
                        if (this._endTime < this._startTime) {
                          final ScaffoldState scaffold = widget.scaffoldKey.currentState;

                          scaffold.showSnackBar(
                            SnackBar(
                              elevation: 100000000000,
                              content: Text(
                                '结束时间不能小于等于开始时间',
                              ),
                              duration: Duration(seconds: 2),
                            ),
                          );

                          Navigator.of(context).pop();
                        } else {
                          Navigator.of(context).pop([this._startTime, this._endTime]);
                        }
                      },
                    ),
                  ],
                ),
              ),
              /// * 提示文字
              Container(
                width: ScreenUtils.screenWidth,
                height: 39.0,
                color: Colors.white,
                child: Row(
                  mainAxisSize: MainAxisSize.max,
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: <Widget>[
                    Text(
                      '开始时间',
                      style: TextStyle(
                        fontSize: 14.0,
                        color: themeTextColor,
                      ),
                    ),
                    Text(
                      '结束时间',
                      style: TextStyle(
                        fontSize: 14.0,
                        color: themeTextColor,
                      ),
                    ),
                  ],
                ),
              ),
              /// * 选项列表
              Container(
                width: ScreenUtils.screenWidth,
                height: 256.0,
                color: Colors.white,
                child: Row(
                  mainAxisSize: MainAxisSize.max,
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Expanded(
                      child: CupertinoPicker.builder(
                        backgroundColor: Colors.white,
                        scrollController: this._startTimeController,
                        itemExtent: 39.0,
                        onSelectedItemChanged: (int index) {
                          setState(() {
                            this._startTime = index;
                          });
                        },
                        itemBuilder: (BuildContext context, int index) {
                          return Center(
                            child: Text('$index'),
                          );
                        },
                        childCount: 24,
                      ),
                    ),
                    Expanded(
                      child: CupertinoPicker.builder(
                        backgroundColor: Colors.white,
                        scrollController: this._endTimeController,
                        itemExtent: 39.0,
                        onSelectedItemChanged: (int index) {
                          setState(() {
                            _endTime = index;
                          });
                        },
                        itemBuilder: (BuildContext context, int index) {
                          return Center(
                            child: Text('${index + 1}'),
                          );
                        },
                        childCount: 24,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

}
