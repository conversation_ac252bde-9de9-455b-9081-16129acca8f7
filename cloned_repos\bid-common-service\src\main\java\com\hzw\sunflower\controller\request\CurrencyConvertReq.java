package com.hzw.sunflower.controller.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2024/5/20 15:54
 * @description：外币转换表
 * @modified By：`
 * @version: 1.0
 */
@Data
public class CurrencyConvertReq {

    @ApiModelProperty(value = "转换表id")
    private Long id;

    @ApiModelProperty(value = "转换金额")
    private BigDecimal convertMoney;

    @ApiModelProperty(value = "现汇买入价")
    private BigDecimal buyingRate;

    @ApiModelProperty(value = "现钞买入价")
    private BigDecimal cashPurchasePrice;

    @ApiModelProperty(value = "现汇卖出价")
    private BigDecimal sellingRate;

    @ApiModelProperty(value = "现钞卖出价")
    private BigDecimal cashSellingPrice;

    @ApiModelProperty("中行折算价")
    private BigDecimal conversionPrice;

    @ApiModelProperty("使用汇率")
    private String useRate;

    @ApiModelProperty(value = "发布时间")
    private String publishTime;

    @ApiModelProperty(value = "汇率凭证文件ID")
    private List<Long> currencyRateList;

}
