package com.hzw.sunflower.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.controller.request.*;
import com.hzw.sunflower.controller.response.ScheduleDataVo;
import com.hzw.sunflower.dto.ProjectSectionScheduleDto;
import com.hzw.sunflower.entity.ProjectSectionSchedule;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

public interface ProjectSectionScheduleService extends IService<ProjectSectionSchedule> {

    /**
     * 保存开标一览表信息
     * @param bidDocInfo
     * @return
     */
    boolean saveScheduleInfo(List<ProjectBidDocREQ> bidDocInfo);

    /**
     * 根据招标文件id获取开标一览表信息
     * @param docId
     * @return
     */
    List<ProjectSectionScheduleDto> getInfoByDocId(Long docId, Integer bidRound);

    /**
     * 导出模板
     * @param projectId
     * @param request
     * @param response
     */
    void exportScheduleTemplate(Long projectId, HttpServletRequest request, HttpServletResponse response);

    /**
     * 根据标段获取开标一览表文件信息
     * @param sectionId
     * @return
     */
    ProjectSectionScheduleDto getInfoBySectionId(Long sectionId);

    /**
     * 开启唱标初始化
     * @param req
     * @return
     */
    ScheduleDataVo startSingInit(StartSingInitReq req);

    /**
     * 开启唱标确认
     * @param req
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    Boolean startSingConfirm(StartSingConfirmReq req) throws Exception;

    /**
     * 查询开标记录
     * @param req
     * @return
     */
    Result<Long> queryBidOpeningRecord(StartDecSmsReq req);

    /**
     * 获取唱标文件类型
     * @param req
     * @return
     */
    Result<Integer> findSingType(SingTypeREQ req);


    /**
     * 组装表头模版
     * @param html
     * @param projectId
     * @param sectionId
     * @return
     */
     String  assembleHtml(String html,Long projectId,Long sectionId);
}
