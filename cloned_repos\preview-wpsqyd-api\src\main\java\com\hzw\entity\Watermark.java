package com.hzw.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;

@Data
public class Watermark {
    // 水印类型
    @JsonProperty("type")
    private Integer type = 0;
    // 水印内容
    @JsonProperty("value")
    private String value;
    // 水印旋转角度
    @JsonProperty("rotate")
    private Integer rotate = 0;
    // 水印是否平铺
    @JsonProperty("tilling")
    private Boolean tilling = false;
    // 水印水平间距
    @JsonProperty("horizontal")
    private Integer horizontal = 0;
    // 水印的不透明度
    @JsonProperty("fillstyle")
    private String fillstyle ;
    // 水印垂直间距
    @JsonProperty("vertical")
    private Integer vertical = 0;
    // 水印位置
    @JsonProperty("place")
    private Integer place = 0;
    // 文字水印字体信息
    @JsonProperty("font")
    private String font;
    // 添加水印操作权限
    @JsonProperty("ctlType")
    private Integer ctlType = 0;
    // 图片水印内容（水印类型为2时此字段必须）
    @JsonProperty("image")
    private Map<String, Object> image = new HashMap<>();
}
