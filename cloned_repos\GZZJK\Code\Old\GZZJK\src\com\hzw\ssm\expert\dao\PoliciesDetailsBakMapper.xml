<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!-- T_POLICIES_DETAILS_BAK -->
<mapper namespace="com.hzw.ssm.expert.dao.PoliciesDetailsBakMapper">
    <!-- This code was generated by TableGo tools, mark 1 begin. -->
    <!-- 字段映射 -->
    <resultMap id="policiesDetailsBakMap" type="com.hzw.ssm.expert.entity.PoliciesDetailsBak">
        <id column="ID" property="id" jdbcType="VARCHAR" />
        <result column="CREATE_USER" property="createUser" jdbcType="VARCHAR" />
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
        <result column="UPDATE_USER" property="updateUser" jdbcType="VARCHAR" />
        <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP" />
        <result column="BEFORE" property="before" jdbcType="VARCHAR" />
        <result column="AFTER" property="after" jdbcType="VARCHAR" />
        <result column="DETAIL_ID" property="detailId" jdbcType="VARCHAR" />
    </resultMap>
    <!-- This code was generated by TableGo tools, mark 1 end. -->

    <!-- This code was generated by TableGo tools, mark 2 begin. -->
    <!-- 表查询字段 -->
    <sql id="allColumns">
        pdb.ID, pdb.CREATE_USER, pdb.CREATE_TIME, pdb.UPDATE_USER, pdb.UPDATE_TIME, pdb.BEFORE, pdb.AFTER, pdb.DETAIL_ID
    </sql>

    <!-- 根据条件参数查询列表 -->
    <select id="findPoliciesDetailsBakByCondition" resultMap="policiesDetailsBakMap" parameterType="map">
        SELECT
        <include refid="allColumns" />
        FROM T_POLICIES_DETAILS_BAK pdb WHERE 1 = 1
        <if test="createUser != null and createUser != ''">
            AND pdb.CREATE_USER LIKE '%' || #{createUser} || '%'
        </if>
        <if test="createTime != null">
            AND pdb.CREATE_TIME = #{createTime}
        </if>
        <if test="updateUser != null and updateUser != ''">
            AND pdb.UPDATE_USER LIKE '%' || #{updateUser} || '%'
        </if>
        <if test="updateTime != null">
            AND pdb.UPDATE_TIME = #{updateTime}
        </if>
        <if test="before != null">
            AND pdb.BEFORE = #{before}
        </if>
        <if test="after != null">
            AND pdb.AFTER = #{after}
        </if>
        <if test="detailId != null and detailId != ''">
            AND pdb.DETAIL_ID LIKE '%' || #{detailId} || '%'
        </if>
        order by pdb.CREATE_TIME  desc
    </select>


    <!-- 根据条件参数查询列表 -->
    <select id="queryPageFindPoliciesDetailsBakByCondition" resultMap="policiesDetailsBakMap" parameterType="map">
        SELECT
        <include refid="allColumns" />
        FROM T_POLICIES_DETAILS_BAK pdb WHERE 1 = 1
        <if test="createUser != null and createUser != ''">
            AND pdb.CREATE_USER LIKE '%' || #{createUser} || '%'
        </if>
        <if test="createTime != null">
            AND pdb.CREATE_TIME = #{createTime}
        </if>
        <if test="updateUser != null and updateUser != ''">
            AND pdb.UPDATE_USER LIKE '%' || #{updateUser} || '%'
        </if>
        <if test="updateTime != null">
            AND pdb.UPDATE_TIME = #{updateTime}
        </if>
        <if test="before != null">
            AND pdb.BEFORE = #{before}
        </if>
        <if test="after != null">
            AND pdb.AFTER = #{after}
        </if>
        <if test="detailId != null and detailId != ''">
            AND pdb.DETAIL_ID LIKE '%' || #{detailId} || '%'
        </if>
    </select>


    <!-- 新增信息 -->
    <insert id="addPoliciesDetailsBak">
        INSERT INTO T_POLICIES_DETAILS_BAK (
            ID, CREATE_USER, CREATE_TIME, UPDATE_USER, UPDATE_TIME, BEFORE, AFTER, DETAIL_ID
        ) VALUES (
            #{id,jdbcType=VARCHAR},
            #{createUser,jdbcType=VARCHAR},
            #{createTime,jdbcType=TIMESTAMP},
            #{updateUser,jdbcType=VARCHAR},
            #{updateTime,jdbcType=TIMESTAMP},
            #{before,jdbcType=VARCHAR},
            #{after,jdbcType=VARCHAR},
            #{detailId,jdbcType=VARCHAR}
        )
    </insert>


    <!-- 根据主键查询信息 -->
    <select id="findPoliciesDetailsBakById" resultMap="policiesDetailsBakMap" parameterType="string">
        SELECT
        <include refid="allColumns" />
        FROM T_POLICIES_DETAILS_BAK pdb WHERE pdb.ID = #{id,jdbcType=VARCHAR}
    </select>

</mapper>