package com.hzw.sunflower.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hzw.sunflower.entity.BondRate;
import com.hzw.sunflower.entity.BondSplit;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 保证金利率 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-15
 */
@Mapper
public interface BondRateMapper extends BaseMapper<BondRate> {

}
