package com.hzw.sunflower.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.controller.response.FileVo;
import com.hzw.sunflower.controller.response.OssKeyVO;
import com.hzw.sunflower.entity.OssFile;
import com.hzw.sunflower.entity.condition.OssFileCondition;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.List;


/**
 * OSS附件 Service接口
 *
 * <AUTHOR>
 * @version 1.0.0 2021-04-21
 */
public interface OssFileService extends IService<OssFile> {
    /**
     * 根据条件分页查询OSS附件 列表
     *
     * @param condition 查询条件
     * @return 分页信息
     */
    IPage<OssFile> findOssFileByCondition(OssFileCondition condition) throws Exception;

    /**
     * 根据主键ID查询OSS附件 信息
     *
     * @param id 主键ID
     * @return OSS附件 信息
     */
    OssFile getOssFileById(Long id);

    /**
     * 新增OSS附件 信息
     *
     * @param ossFile OSS附件 信息
     * @return 是否成功
     */
    Boolean addOssFile(OssFile ossFile) throws Exception;

    /**
     * 修改OSS附件 信息
     *
     * @param ossFile OSS附件 信息
     * @return 是否成功
     */
    Boolean updateOssFile(OssFile ossFile) throws Exception;

    /**
     * 根据主键ID删除OSS附件
     *
     * @param id 主键ID
     * @return 是否成功
     */
    Boolean deleteOssFileById(Long id) throws Exception;

    /**
     * 根据主键ID列表批量删除OSS附件
     *
     * @param idList 主键ID列表
     * @return 是否成功
     */
    Boolean deleteOssFileByIds(List<Long> idList) throws Exception;

    /**
     * 根据fileids查询附件信息
     * @param fileIds
     * @return
     */
    List<FileVo> getOssFileByFileIds(String fileIds);

    /**
     * 将公告内容变成HTML上传到OSS
     * @return
     */
//    public Long uploadHtml(String fileString, String fileName) throws Exception;


    /**
     * 富文本编辑器转pdf并上传到oss
     * @param content
     * @param fileName
     * @param title   pdf标题，可为空
     * @return
     * @throws Exception
     */
    public Long editor2PDF(String content,String fileName,String title) throws Exception;


    /**保存文件
     * @throws Exception
     */
    public Long saveOssFile(String fileName,String filePath) throws Exception;

    /**保存文件流
     * @throws Exception
     */
    public Long saveOssFileInputStream(String fileName, InputStream stream) throws Exception;

    /**
     * 保存base64文件 并上传到oss
     * @param fileName
     * @param fileContent
     * @return
     */
    Long saveOssBase64File(String fileName, String fileContent);

    void outFileByOssKey(HttpServletRequest request, HttpServletResponse response, OssFile ossFile);

    /**
     * 根据文件id集合查询
     * @param ossFileIds
     * @return
     */
    List<OssFile> listByIdsOrder(List<String> ossFileIds);


    /**拷贝文件
     * @throws Exception
     */
    Long saveCopyOssFile(Long ossId) ;

    /**
     * 获取key
     * @return
     */
    public Result<OssKeyVO> getkey() throws Exception;
}