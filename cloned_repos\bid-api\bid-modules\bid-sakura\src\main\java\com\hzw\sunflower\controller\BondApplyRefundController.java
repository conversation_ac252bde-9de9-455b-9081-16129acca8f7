package com.hzw.sunflower.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hzw.sunflower.common.BaseController;
import com.hzw.sunflower.common.Paging;
import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.common.annotation.RepeatSubmit;
import com.hzw.sunflower.controller.request.*;
import com.hzw.sunflower.controller.response.*;
import com.hzw.sunflower.entity.BondApplyRelation;
import com.hzw.sunflower.entity.condition.BondApplyRefundCondition;
import com.hzw.sunflower.entity.condition.BondApplyRelationCondition;
import com.hzw.sunflower.entity.condition.BondNotRelationCondition;
import com.hzw.sunflower.service.BondApplyRefundService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 保证金退还申请表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-27
 */
@Api(tags = "保证金退还申请表")
@RestController
@RequestMapping("/bondApplyRefund")
public class BondApplyRefundController extends BaseController {

    @Autowired
    private BondApplyRefundService bondApplyRefundService;

    @ApiOperation(value = "未关联流水列表分页查询")
    @ApiImplicitParam(name = "condition", value = "未关联流水查询条件", required = true, dataType = "BondNotRelationCondition", paramType = "body")
    @PostMapping(value = "/getBondWaterNotRelationPage")
    public Result<Paging<BondNotRelationVo>> getBondWaterNotRelationPage(@RequestBody BondNotRelationCondition condition) {
        IPage<BondNotRelationVo> page = bondApplyRefundService.getBondWaterNotRelationPageNew(condition, getJwtUser());
        return Result.ok(Paging.buildPaging(page));
    }

    @ApiOperation(value = "获取当前登录供应商所在公司参与的所有项目信息")
    @ApiImplicitParam(name = "req", value = "查询公司参与的所有项目信息入参", required = true, dataType = "MyCompanyProjectReq", paramType = "body")
    @PostMapping(value = "/getMyCompanyProject")
    public Result<List<MyCompanyProjectVo>> getMyCompanyProject(@RequestBody MyCompanyProjectReq req) {
        List<MyCompanyProjectVo> projectVos = bondApplyRefundService.getMyCompanyProject(req, getJwtUser());
        return Result.ok(projectVos);
    }

    @ApiOperation(value = "根据项目id获取当前供应商参与是标包信息")
    @ApiImplicitParam(name = "req", value = "查询当前供应商参与是标包信息入参", required = true, dataType = "MyCompanyProjectReq", paramType = "body")
    @PostMapping(value = "/getMyCompanySection")
    public Result<List<MyCompanyProjectVo>> getMyCompanySection(@RequestBody MyCompanyProjectReq req) {
        List<MyCompanyProjectVo> projectVos = bondApplyRefundService.getMyCompanySection(req, getJwtUser());
        return Result.ok(projectVos);
    }

    @ApiOperation(value = "异常退款申请/编辑(供应商)")
    @ApiImplicitParam(name = "req", value = "异常退款申请", required = true, dataType = "BondAbnormalApplyRefundReq", paramType = "body")
    @PostMapping(value = "/bondAbnormalApplyRefund")
    @RepeatSubmit
    public Result<Boolean> bondAbnormalApplyRefund(@RequestBody BondAbnormalApplyRefundReq req) {
        Boolean bool = bondApplyRefundService.saveBondAbnormalApplyRefund(req);
        return Result.okOrFailed(bool);
    }

    @ApiOperation(value = "根据流水id查询异常退款申请信息")
    @ApiImplicitParam(name = "req", value = "异常退款申请", required = true, dataType = "BondAbnormalApplyRefundReq", paramType = "body")
    @PostMapping(value = "/getBondAbnormalApplyRefund")
    public Result<BondAbnormalApplyRefundVo> getBondAbnormalApplyRefund(@RequestBody BondAbnormalApplyRefundReq req) {
        BondAbnormalApplyRefundVo bondAbnormalApplyRefund = bondApplyRefundService.getBondAbnormalApplyRefund(req);
        return Result.ok(bondAbnormalApplyRefund);
    }

    @ApiOperation(value = "异常退款申请待处理列表(由供应商发起)")
    @ApiImplicitParam(name = "condition", value = "异常申请退款查询条件", required = true, dataType = "BondApplyRefundCondition", paramType = "body")
    @PostMapping(value = "/getBondApplyRefundList")
    public Result<Paging<BondApplyRefundVo>> getBondApplyRefundList(@RequestBody BondApplyRefundCondition condition) {
        IPage<BondApplyRefundVo> page = bondApplyRefundService.getBondApplyRefundList(condition);
        return Result.ok(Paging.buildPaging(page));
    }

    @ApiOperation(value = "异常退款申请(供应商发起)处理详情界面")
    @ApiImplicitParam(name = "req", value = "异常退款申请详情", required = true, dataType = "BondApplyRefundDetailReq", paramType = "body")
    @PostMapping(value = "/bondAbnormalApplyRefundDetail")
    public Result<BondAbnormalApplyRefundDetailVo> bondAbnormalApplyRefundDetail(@RequestBody BondApplyRefundDetailReq req) {
        BondAbnormalApplyRefundDetailVo bondAbnormalApplyRefundDetail = bondApplyRefundService.bondAbnormalApplyRefundDetail(req, getJwtUser());
        return Result.ok(bondAbnormalApplyRefundDetail);
    }

    @ApiOperation(value = "异常退款申请(供应商发起)处理详情参与的项目信息")
    @ApiImplicitParam(name = "req", value = "异常退款申请详情", required = true, dataType = "BondApplyRefundProjectDetailReq", paramType = "body")
    @PostMapping(value = "/bondAbnormalApplyRefundProject")
    public Result<Paging<MyCompanyProjectVo>> bondAbnormalApplyRefundProject(@RequestBody BondApplyRefundProjectDetailReq req) {
        IPage<MyCompanyProjectVo> page = bondApplyRefundService.bondAbnormalApplyRefundProject(req);
        return Result.ok(Paging.buildPaging(page));
    }

    @ApiOperation(value = "异常退款申请(供应商发起)运管处处理")
    @ApiImplicitParam(name = "req", value = "异常退款申请运管处处理", required = true, dataType = "BondAbnormalApplyRefundConfirmReq", paramType = "body")
    @PostMapping(value = "/bondAbnormalApplyRefundConfirm")
    @RepeatSubmit
    public Result<Boolean> bondAbnormalApplyRefundConfirm(@RequestBody BondAbnormalApplyRefundConfirmReq req) {
        Boolean bool = bondApplyRefundService.saveBondAbnormalApplyRefundConfirm(req);
        return Result.okOrFailed(bool);
    }

    @ApiOperation(value = "异常退款申请(供应商发起)处长处理")
    @ApiImplicitParam(name = "req", value = "异常退款申请处长处理", required = true, dataType = "BondAbnormalApplyRefundRegistrarReq", paramType = "body")
    @PostMapping(value = "/bondAbnormalApplyRefundRegistrar")
    @RepeatSubmit
    public Result<Boolean> bondAbnormalApplyRefundRegistrar(@RequestBody BondAbnormalApplyRefundRegistrarReq req) {
        Boolean bool = bondApplyRefundService.saveBondAbnormalApplyRefundRegistrar(req);
        return Result.okOrFailed(bool);
    }

    @ApiOperation(value = "异常关联确认(项目经理发起)列表")
    @ApiImplicitParam(name = "condition", value = "关联申请查询条件", required = true, dataType = "BondApplyRelationCondition", paramType = "body")
    @PostMapping(value = "/bondAbnormalRelationList")
    public Result<Paging<AbnormalApplyRelationVo>> bondAbnormalRelationList(@RequestBody BondApplyRelationCondition condition) {
        IPage<AbnormalApplyRelationVo> page = bondApplyRefundService.getBondAbnormalRelationList(condition);
        return Result.ok(Paging.buildPaging(page));
    }

    @ApiOperation(value = "保证金关联申请(项目经理发起)列表")
    @ApiImplicitParam(name = "condition", value = "关联申请查询条件", required = true, dataType = "BondApplyRelationCondition", paramType = "body")
    @PostMapping("/bondApplyRelationList")
    public Result<Paging<AbnormalApplyRelationVo>> bondApplyRelationList(@RequestBody BondApplyRelationCondition condition) {
        IPage<AbnormalApplyRelationVo> page = bondApplyRefundService.bondApplyRelationList(condition);
        return Result.ok(Paging.buildPaging(page));
    }

    @ApiOperation(value = "根据id查询保证金申请关联(项目经理发起)详情")
    @ApiImplicitParam(name = "req", value = "保证金关联申请同意/退回入参", required = true, dataType = "BondApplyRelationConfirmReq", paramType = "body")
    @PostMapping("/getBondApplyRelationById")
    public Result<BondApplyRelation> getBondApplyRelationById(@RequestBody BondApplyRelationConfirmReq req) {
        BondApplyRelation bondApplyRelation = bondApplyRefundService.getBondApplyRelationById(req);
        return Result.ok(bondApplyRelation);
    }

    @ApiOperation(value = "保证金关联申请同意/退回(项目经理发起)")
    @ApiImplicitParam(name = "req", value = "保证金关联申请同意/退回入参", required = true, dataType = "BondApplyRelationConfirmReq", paramType = "body")
    @PostMapping("/bondApplyRelationConfirm")
    @RepeatSubmit
    public Result<Boolean> bondApplyRelationConfirm(@RequestBody BondApplyRelationConfirmReq req) {
        Boolean bool = bondApplyRefundService.saveBondApplyRelationConfirm(req);
        return Result.okOrFailed(bool);
    }

    @ApiOperation(value = "未关联保证金退还取消")
    @ApiImplicitParam(name = "req", value = "保证金关联申请", required = true, dataType = "BondAbnormalApplyRefundReq", paramType = "body")
    @PostMapping(value = "/deleteBondRefundWater")
    public Result<Boolean> deleteBondRefundWater(@RequestBody BondAbnormalApplyRefundReq req) {
        return bondApplyRefundService.deleteBondRefundWater(req.getWaterId());
    }
}
