package com.hzw.sunflower.constant.constantenum;

/**
 * <AUTHOR>
 * @date ：Created in 2021/4/15 10:36
 * @description：文件递交截止时间更改类型
 * @modified By：`
 * @version: 1.0
 */
public enum TimeUpdateTypeEnum {

    CLARIFY(1, "澄清修改"),
    PC_ANNOUNCEMENT(2, "补充公告");

    private Integer type;
    private String desc;

    TimeUpdateTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

}
