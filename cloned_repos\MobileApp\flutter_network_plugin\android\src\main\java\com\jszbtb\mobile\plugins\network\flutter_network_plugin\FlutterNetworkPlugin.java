package com.jszbtb.mobile.plugins.network.flutter_network_plugin;


import android.content.Context;

import java.util.HashMap;


import io.flutter.embedding.engine.plugins.FlutterPlugin;
import io.flutter.plugin.common.BinaryMessenger;
import io.flutter.plugin.common.EventChannel;
import io.flutter.plugin.common.MethodCall;
import io.flutter.plugin.common.MethodChannel;
import io.flutter.plugin.common.MethodChannel.MethodCallHandler;
import io.flutter.plugin.common.MethodChannel.Result;
import io.flutter.plugin.common.PluginRegistry;
import io.flutter.plugin.common.PluginRegistry.Registrar;
import android.os.Handler;
/** FlutterNetworkPlugin */
public class FlutterNetworkPlugin implements MethodCallHandler, FlutterPlugin {
  // 上下文 Context
  private Context context;
  private Handler handler;
  private HttpClient mHttpClient;
  private MethodChannel methodChannel;

  @Override
  public void onDetachedFromEngine(FlutterPluginBinding binding) {
    this.context = null;
    methodChannel.setMethodCallHandler(null);
    methodChannel = null;
  }

  @Override
  public void onAttachedToEngine(FlutterPluginBinding binding) {
    onAttachedToEngine(
            binding.getApplicationContext(), binding.getFlutterEngine().getDartExecutor());
  }

  private void onAttachedToEngine(Context applicationContext, BinaryMessenger messenger) {
    this.context = applicationContext;
    this.handler = new Handler(applicationContext.getMainLooper());
    this.mHttpClient = HttpClient.getInstance(context);
    methodChannel = new MethodChannel(messenger, "flutter_network_plugin");

    methodChannel.setMethodCallHandler(this);
  }

  /** Plugin registration. */
  public static void registerWith(PluginRegistry.Registrar registrar) {
    final FlutterNetworkPlugin instance = new FlutterNetworkPlugin();
    instance.onAttachedToEngine(registrar.context(), registrar.messenger());
  }

//  /** Plugin registration. */
//  public static void registerWith(PluginRegistry.Registrar registrar) {
//    final FlutterNetworkPlugin instance = new FlutterNetworkPlugin();
//    instance.onAttachedToEngine(registrar.context(), registrar.messenger());
//  }


//  public FlutterNetworkPlugin(Registrar registrar) {
//    this.context = registrar.context();
//    this.handler = new Handler(context.getMainLooper());
//    this.mHttpClient = HttpClient.getInstance(context);
//
//  }






  @Override
  public void onMethodCall(MethodCall call, final Result result) {
    if (call.method.equals("getPlatformVersion")) {
      result.success("Android " + android.os.Build.VERSION.RELEASE);
    } else if(call.method.equals("doGetRequest")) {
      doGetRequest(call, result);
    } else if(call.method.equals("doPostJsonRequest")) {
      doPostJsonRequest(call,result);
    } else if(call.method.equals("doPostFormRequest")) {
      doPostFormRequest(call,result);
    } else if(call.method.equals("doPutJsonRequest")) {
      doPutJsonRequest(call,result);
    } else if(call.method.equals("doPutFormRequest")) {
      doPutFormRequest(call,result);
    } else if(call.method.equals("doDeleteRequest")) {
      doDeleteRequest(call,result);
    }
    else {
      result.notImplemented();
    }
  }

  /**
   * 发出Get请求，返回JSON数据
   * @param call 方法调用
   * @param result 返回结果
   */
  private void doGetRequest(MethodCall call, final Result result) {
    HashMap<String,Object> arguments = (HashMap<String, Object>) call.arguments;
    String url = arguments.get("url").toString();
    HashMap<String,String> param = (HashMap<String, String>)arguments.get("params");

    boolean cachable = false;
    if(arguments.containsKey("isCachable")) {
      cachable = (boolean) arguments.get("isCachable");
    }


    boolean isExternalDomainName = false;
    String externalDomainName = "";
    if(arguments.containsKey("domainName")) {
      isExternalDomainName = true;
      externalDomainName = arguments.get("domainName").toString();
    } else {
      isExternalDomainName = false;
      externalDomainName = "";
    }

    this.mHttpClient.Get(context,
            url,
            param,
            cachable,
            isExternalDomainName,
            externalDomainName,
            new HttpClient.CallBack() {
      @Override
      public void onResponseString(final String responseString) {
        handler.post(new Runnable() {
          @Override
          public void run() {
            result.success(responseString);
          }
        });
      }

      @Override
      public void onFailure(final String message) {
        handler.post(new Runnable() {
          @Override
          public void run() {
            result.error("500",message,null);
          }
        });
      }
    });
  }


  /**
   * 发出Post JSON请求，返回数据
   * @param call 方法调用
   * @param result 返回结果
   */
  private void doPostJsonRequest(MethodCall call,final Result result) {
    HashMap<String,Object> arguments = (HashMap<String, Object>) call.arguments;
    String url = arguments.get("url").toString();
    String json = arguments.get("params").toString();

    boolean cachable = false;
    if(arguments.containsKey("isCachable")) {
      cachable = (boolean) arguments.get("isCachable");
    }


    boolean isExternalDomainName = false;
    String externalDomainName = "";
    if(arguments.containsKey("domainName")) {
      isExternalDomainName = true;
      externalDomainName = arguments.get("domainName").toString();
    } else {
      isExternalDomainName = false;
      externalDomainName = "";
    }

    this.mHttpClient.PostJSON(this.context, url, json, cachable,isExternalDomainName,externalDomainName,new HttpClient.CallBack() {
      @Override
      public void onResponseString(final String responseString) {
        handler.post(new Runnable() {
          @Override
          public void run() {
            result.success(responseString);
          }
        });
      }

      @Override
      public void onFailure(final String message) {
        handler.post(new Runnable() {
          @Override
          public void run() {
            result.error("500",message,null);
          }
        });
      }
    });
  }

  /**
   * 发出Post www-url-encode请求，返回数据
   * @param call 方法调用
   * @param result 返回结果
   */
  private void doPostFormRequest(MethodCall call,final Result result) {
    HashMap<String,Object> arguments = (HashMap<String, Object>) call.arguments;
    String url = arguments.get("url").toString();
    HashMap<String,String> param = (HashMap<String, String>)arguments.get("params");

    boolean cachable = false;
    if(arguments.containsKey("isCachable")) {
      cachable = (boolean) arguments.get("isCachable");
    }


    boolean isExternalDomainName = false;
    String externalDomainName = "";
    if(arguments.containsKey("domainName")) {
      isExternalDomainName = true;
      externalDomainName = arguments.get("domainName").toString();
    } else {
      isExternalDomainName = false;
      externalDomainName = "";
    }

    this.mHttpClient.PostForm(this.context, url, param, cachable,isExternalDomainName,externalDomainName,new HttpClient.CallBack() {
      @Override
      public void onResponseString(final String responseString) {
        handler.post(new Runnable() {
          @Override
          public void run() {
            result.success(responseString);
          }
        });
      }

      @Override
      public void onFailure(final String message) {
        handler.post(new Runnable() {
          @Override
          public void run() {
            result.error("500",message,null);
          }
        });
      }
    });
  }

  /**
   * 发出 PUT JOSN的请求，返回数据
   * @param call 方法调用
   * @param result 返回结果
   */
  private void doPutJsonRequest(MethodCall call,final Result result) {
    HashMap<String,Object> arguments = (HashMap<String, Object>) call.arguments;
    String url = arguments.get("url").toString();
    String json = arguments.get("params").toString();

    boolean isExternalDomainName = false;
    String externalDomainName = "";
    if(arguments.containsKey("domainName")) {
      isExternalDomainName = true;
      externalDomainName = arguments.get("domainName").toString();
    } else {
      isExternalDomainName = false;
      externalDomainName = "";
    }

    this.mHttpClient.PutJSON(this.context, url, json,isExternalDomainName,externalDomainName, new HttpClient.CallBack() {
      @Override
      public void onResponseString(final String responseString) {
        handler.post(new Runnable() {
          @Override
          public void run() {
            result.success(responseString);
          }
        });
      }

      @Override
      public void onFailure(final String message) {
        handler.post(new Runnable() {
          @Override
          public void run() {
            result.error("500",message,null);
          }
        });
      }
    });
  }

  /**
   * 发出 PUT www-url-encode 请求
   * @param call  方法调用
   * @param result 返回结果
   */
  private void doPutFormRequest(MethodCall call,final Result result) {
    HashMap<String,Object> arguments = (HashMap<String, Object>) call.arguments;
    String url = arguments.get("url").toString();
    HashMap<String,String> param = (HashMap<String, String>)arguments.get("params");

    boolean isExternalDomainName = false;
    String externalDomainName = "";
    if(arguments.containsKey("domainName")) {
      isExternalDomainName = true;
      externalDomainName = arguments.get("domainName").toString();
    } else {
      isExternalDomainName = false;
      externalDomainName = "";
    }

    this.mHttpClient.PutForm(this.context, url, param,isExternalDomainName,externalDomainName,new HttpClient.CallBack() {
      @Override
      public void onResponseString(final String responseString) {
        handler.post(new Runnable() {
          @Override
          public void run() {
            result.success(responseString);
          }
        });
      }

      @Override
      public void onFailure(final String message) {
        handler.post(new Runnable() {
          @Override
          public void run() {
            result.error("500",message,null);
          }
        });
      }
    });
  }

  /**
   * 发出 Delete 请求
   * @param call 方法调用
   * @param result 返回结果
   */
  private void doDeleteRequest(MethodCall call,final Result result) {
    HashMap<String,Object> arguments = (HashMap<String, Object>) call.arguments;
    String url = arguments.get("url").toString();
    HashMap<String,String> param = (HashMap<String, String>)arguments.get("params");
    boolean isExternalDomainName = false;
    String externalDomainName = "";
    if(arguments.containsKey("domainName")) {
      isExternalDomainName = true;
      externalDomainName = arguments.get("domainName").toString();
    } else {
      isExternalDomainName = false;
      externalDomainName = "";
    }
    this.mHttpClient.Delete(this.context, url, param, isExternalDomainName,externalDomainName, new HttpClient.CallBack() {
      @Override
      public void onResponseString(final String responseString) {
        handler.post(new Runnable() {
          @Override
          public void run() {
            result.success(responseString);
          }
        });
      }

      @Override
      public void onFailure(final String message) {
        handler.post(new Runnable() {
          @Override
          public void run() {
            result.error("500",message,null);
          }
        });
      }
    });
  }
}
