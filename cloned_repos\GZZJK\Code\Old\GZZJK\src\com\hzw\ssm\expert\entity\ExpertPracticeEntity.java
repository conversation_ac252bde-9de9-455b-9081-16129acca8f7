package com.hzw.ssm.expert.entity;

import com.hzw.ssm.fw.base.BaseEntity;

/**
 * 专家执业资格表
 * <AUTHOR>
 * @date 2014-10-09
 */
public class ExpertPracticeEntity extends BaseEntity {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	private String id;//主键
	
	private String user_id;//用户编号
	
	private String certificate;//执业资格
	
	private String certificate_no;//资格证书号
	
	private String certificate_fileid;//资格证书复印件
	
	private String old_certificate;//资格证书附件名称

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getUser_id() {
		return user_id;
	}

	public void setUser_id(String userId) {
		user_id = userId;
	}

	public String getCertificate() {
		return certificate;
	}

	public void setCertificate(String certificate) {
		this.certificate = certificate;
	}

	public String getCertificate_no() {
		return certificate_no;
	}

	public void setCertificate_no(String certificateNo) {
		certificate_no = certificateNo;
	}

	public String getCertificate_fileid() {
		return certificate_fileid;
	}

	public void setCertificate_fileid(String certificateFileid) {
		certificate_fileid = certificateFileid;
	}

	public String getOld_certificate() {
		return old_certificate;
	}

	public void setOld_certificate(String old_certificate) {
		this.old_certificate = old_certificate;
	}

}
