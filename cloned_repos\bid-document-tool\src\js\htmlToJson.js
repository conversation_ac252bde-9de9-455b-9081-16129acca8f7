/**
 * @description: 将传入的html字符串转为json对象输出
 * @param {*} htmlString html字符串
 */
function htmlToJson(htmlString) {
    // 创建一个新的 DOM 解析器
    const parser = new DOMParser();
    
    // 解析 HTML 字符串为 DOM 树
    const doc = parser.parseFromString(htmlString, 'text/html');
    
    // 递归地将 DOM 树转换为 JSON 对象
    function domToJson(node) {
        // 创建 JSON 对象，包含节点名称和属性
        const obj = {
            tagName: node.tagName || node.nodeName,
            attributes: {},
            children: []
        };
        
        // 添加节点的所有属性到 JSON 对象
        if (node.attributes) {
            for (let attr of node.attributes) {
                obj.attributes[attr.name] = attr.value;
            }
        }
        
        // 递归地处理子节点
        for (let child of node.childNodes) {
            if (child.nodeType === Node.TEXT_NODE) {
                // 如果是文本节点，直接添加文本内容
                obj.children.push(child.textContent.trim());
            } else {
                // 否则，将子节点转换为 JSON 对象
                obj.children.push(dom<PERSON><PERSON><PERSON><PERSON>(child));
            }
        }
        
        return obj;
    }
    
    // 从文档的根元素开始转换
    const json = domToJson(doc.documentElement);
    
    return json;
}

/**
 * @description: 将传入的json对象转为 html输出
 * @param {*} json Json对象
 */
function jsonToHtml(json) {
    // 递归函数，用于生成HTML
    function createElement(node) {
        if (typeof node === 'string') {
            return node; // 如果是文本节点，直接返回
        }
        // 解构获取 tagName, attributes 和 children
        const { tagName, attributes, children } = node;
        const attrString = attributes ? Object.entries(attributes)
            .map(([key, value]) => ` ${key}="${value}"`)
            .join('') : '';

        // 处理子节点
        const childrenString = (children || []).map(createElement).join('');

        // 返回构建的HTML字符串
        return `<${tagName}${attrString}>${childrenString}</${tagName}>`;
    }

    // 从根节点开始转换
    return createElement(json);
}


export {
    htmlToJson,
    jsonToHtml
};