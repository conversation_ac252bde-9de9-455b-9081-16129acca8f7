package com.hzw.ssm.sys.project.entity;

import com.hzw.ssm.fw.base.BaseEntity;

public class AppointsExpertsEntity extends BaseEntity {
	private static final long serialVersionUID = -8151736570527763583L;
	/*
	 * official_id VARCHAR2(50) not null, 主键ID
	 */
	private String officialId;
	/*
	 * experts_name VARCHAR2(254), 专家名称
	 */
	private String expertsName;

	/*
	 * experts_code_id VARCHAR2(254), 专家身份证
	 */
	private String expertsCodeId;
	/*
	 * extract_con NUMBER, 选取次数
	 */
	private int extractCon;

	/*
	 * status NUMBER,
	 * 当前专家的状态  0.保存状态  1.库里已存在状态 2.库里不存在
	 */
	private int status;

	/*
	 * decimationbatch VARCHAR2(254)
	 * 批次号
	 */
	private String decimationBatch;

	private String projectId;
	public String getOfficialId() {
		return officialId;
	}

	public void setOfficialId(String officialId) {
		this.officialId = officialId;
	}

	public String getExpertsName() {
		return expertsName;
	}

	public void setExpertsName(String expertsName) {
		this.expertsName = expertsName;
	}

	public String getExpertsCodeId() {
		return expertsCodeId;
	}

	public void setExpertsCodeId(String expertsCodeId) {
		this.expertsCodeId = expertsCodeId;
	}

	public int getExtractCon() {
		return extractCon;
	}

	public void setExtractCon(int extractCon) {
		this.extractCon = extractCon;
	}

	

	public int getStatus() {
		return status;
	}

	public void setStatus(int status) {
		this.status = status;
	}

	public String getDecimationBatch() {
		return decimationBatch;
	}

	public void setDecimationBatch(String decimationBatch) {
		this.decimationBatch = decimationBatch;
	}

	public String getProjectId() {
		return projectId;
	}

	public void setProjectId(String projectId) {
		this.projectId = projectId;
	}
	
}
