/*
 * 公司名称：江苏华招网信息技术有限公司
 * 版权信息：江苏华招网信息技术有限公司版权所有
 * 文件名称：ExpertLimit.java
 * 修改时间：2021年1月29日
 * 修改人：袁辉
 * 修改内容：
 * 跟踪单号：
 * 修改单号 ：
 */

package com.hzw.ssm.sys.project.entity;

import java.util.Date;

/**
 * <一句话功能简述> TODO
 * <功能详细描述>
 * <AUTHOR>
 * @version V1.0[产品/模块版本]
 */
public class ExpertLimit {
	/* ******************************************************************************************* */
	/*                                       序列化代码区域                                                                                                             */
	/* ******************************************************************************************* */
	/**
	 * EXPERT_ID	VARCHAR2(32)	Y			专家id
	 */
	private String expertId;
	/**
	 * LIMIT_TIME	VARCHAR2(32)	Y			限制的月份（格式yyyy-mm）
	 */
	private String limitTime;
	/**
	 * ADDTIME	DATE	Y			添加的时间
	 */
	private Date addTime;
	/**
	 * DEPARTMENT	VARCHAR2(32)	Y			处室
	 */
	private String department;
	public String getExpertId() {
		return expertId;
	}
	public void setExpertId(String expertId) {
		this.expertId = expertId;
	}
	public String getLimitTime() {
		return limitTime;
	}
	public void setLimitTime(String limitTime) {
		this.limitTime = limitTime;
	}
	public Date getAddTime() {
		return addTime;
	}
	public void setAddTime(Date addTime) {
		this.addTime = addTime;
	}
	public String getDepartment() {
		return department;
	}
	public void setDepartment(String department) {
		this.department = department;
	}
	

	/* ******************************************************************************************* */
	/*                                       全局变量声明区域                                                                                                         */
	/* ******************************************************************************************* */

	/* ******************************************************************************************* */
	/*                                       公共函数声明区域                                                                                                         */
	/* ******************************************************************************************* */

	/* ******************************************************************************************* */
	/*                                       私有函数声明区域                                                                                                         */
	/* ******************************************************************************************* */
}
