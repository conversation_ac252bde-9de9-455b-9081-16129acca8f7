package com.hzw.sunflower.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzw.ncc.client.dto.BondAdjustBodyDto;
import com.hzw.ncc.client.dto.BondAdjustDto;
import com.hzw.ncc.client.dto.BondReceiveBodyDto;
import com.hzw.ncc.client.dto.BondReceiveDto;
import com.hzw.sunflower.constant.CommonConstants;
import com.hzw.sunflower.constant.constantenum.ChangeRecordTypeEnum;
import com.hzw.sunflower.constants.NccConstants;
import com.hzw.sunflower.constants.NccPushTypeEnum;
import com.hzw.sunflower.controller.request.BondSectionReq;
import com.hzw.sunflower.dao.BondSplitMapper;
import com.hzw.sunflower.dto.BondSplitHistoryDto;
import com.hzw.sunflower.dto.BondSplitNccDto;
import com.hzw.sunflower.dto.BondWaterToNccDto;
import com.hzw.sunflower.dto.SectionWatersDto;
import com.hzw.sunflower.entity.*;
import com.hzw.sunflower.dto.*;
import com.hzw.sunflower.security.utils.SecurityUtils;
import com.hzw.sunflower.service.*;
import com.hzw.sunflower.util.BeanListUtil;
import com.hzw.sunflower.util.BondUtils;
import com.hzw.sunflower.util.DateUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * <p>
 * 保证金拆分 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-15
 */
@Service
public class BondSplitServiceImpl extends ServiceImpl<BondSplitMapper, BondSplit> implements BondSplitService {

    @Autowired
    BondWaterService bondWaterService;

    @Autowired
    BondChangeRecordsService bondChangeRecordsService;

    @Autowired
    NccBondPushLogService nccBondPushLogService;

    @Autowired
    BankdataBondToNccService bankdataBondToNccService;

    /**
     * 查询标包的拆分合计金额
     * @param sectionId
     * @param companyId
     * @return
     */
    @Override
    public BondSplit listBySectionId(Long sectionId, Long companyId) {
        return this.baseMapper.listBySectionId(sectionId, companyId);
    }

    /**
     * 拆分流水
     * @param companyId
     * @param sectionIdList
     * @param waterIdList
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean addSplit(Long companyId, List<BondSectionReq> sectionIdList, List<Long> waterIdList) {
        //删除最新
        this.updateSplitForIsLatest(waterIdList);
        // 查询流水信息 按到账时间正序
        List<BondWater> waterList = bondWaterService.list(new LambdaQueryWrapper<BondWater>().in(BondWater::getId, waterIdList).orderByAsc(BondWater::getDate).orderByAsc(BondWater::getTime));
        // 拆分
        List<BondSplit> splitList = split(companyId, sectionIdList, waterList);
        return this.saveBatch(splitList);
    }

    /**
     * 拆分
     * @param companyId
     * @param sectionIdList
     * @param waterList
     * @return
     */
    public static List<BondSplit> split(Long companyId, List<BondSectionReq> sectionIdList, List<BondWater> waterList) {
        List<BondSplit> splitList = new ArrayList<>();
        // 拆分逻辑
        List<BondWater> tempWaterList = waterList;
        int tempIndex = 0;
        BigDecimal tempAmount = new BigDecimal(0);
        for (BondSectionReq req : sectionIdList) {
            BigDecimal amount = req.getAmount(); // 标包分配金额

            for (int i = 0; i < waterList.size() && (amount.compareTo(new BigDecimal(0)) > 0); i++) {
                BigDecimal waterAmount = new BigDecimal(waterList.get(i).getAmount()); // 流水金额
                BondSplit bondSplit = new BondSplit();
                if (amount.compareTo(waterAmount) > 0) {
                    amount = amount.subtract(waterAmount);
//                     System.out.println(">0 :sectionId:"+req.getSectionId()+",amount:"+waterAmount+",waterId:"+waterList.get(i).getId());
                    bondSplit.setProjectId(req.getProjectId());
                    bondSplit.setSectionId(req.getSectionId());
                    bondSplit.setCompanyId(companyId);
                    bondSplit.setAmount(waterAmount);
                    bondSplit.setWaterId(waterList.get(i).getId());
                    splitList.add(bondSplit);
                } else if (amount.compareTo(waterAmount) <= 0) {
//                     System.out.println("<=0 :sectionId:"+req.getSectionId()+",amount:"+amount+",waterId:"+waterList.get(i).getId());
                    bondSplit.setCompanyId(companyId);
                    bondSplit.setAmount(amount);
                    bondSplit.setSectionId(req.getSectionId());
                    bondSplit.setProjectId(req.getProjectId());
                    bondSplit.setWaterId(waterList.get(i).getId());
                    splitList.add(bondSplit);

                    tempAmount = waterAmount.subtract(amount);
                    amount = new BigDecimal(0);
                    tempIndex += i;
                }
            }
            if (tempAmount.compareTo(new BigDecimal(0)) != 0) {
                tempWaterList.get(tempIndex).setAmount(tempAmount.toString());
            }else{
                tempIndex+=1;
            }
            waterList = tempWaterList.subList(tempIndex, tempWaterList.size());
//            if (tempAmount.compareTo(new BigDecimal(0)) == 0) {
//                waterList = tempWaterList.subList(tempIndex + 1, tempWaterList.size());
//            } else {
//                tempWaterList.get(tempIndex).setAmount(tempAmount.toString());
                //这边有bug如果tempWaterList是多条的话下一轮tempIndex依然是0，waterList就不对了
//                waterList = tempWaterList.subList(tempIndex, tempWaterList.size());
//            }
        }
        return splitList;
    }

    /**
     * 根据流水id获取保证金待推送收款数据
     * @param waterId
     * @return
     */
    @Override
    public BondReceiveDto getBondReceiveDtoByWaterId(Long waterId) {
        List<Long> waterIds = new ArrayList<>();
        waterIds.add(waterId);
        List<BondSplitNccDto> list = this.baseMapper.getByWaterIds(waterIds);
        BondReceiveDto dto = new BondReceiveDto();
        dto.setCreationTime(list.get(0).getCreationTime());
        dto.setBillId(UUID.randomUUID().toString().replaceAll("-", ""));
        List<BondReceiveBodyDto> bodys = new ArrayList<>();
        for (BondSplitNccDto split : list) {
            BondReceiveBodyDto body = new BondReceiveBodyDto();
            body.setCustomer(split.getCustomer());
            body.setCustomerId(split.getCustomerId());
            body.setReceiveTime(split.getReceiveTime());
            body.setAmount(split.getAmount().toString());
            body.setPkDeptId(split.getAccountDepartmentCode());
            body.setRecAccount(split.getRecAccount());
            body.setPurchaseNumber(split.getPurchaseNumber());
            body.setWaterId(split.getWaterId());
            bodys.add(body);
        }
        dto.setBody(bodys);
        return dto;
    }


    /**
     * 根据流水id获取保证金带推送调整数据（确认关联）
     * 从其他调到项目
     * @param water
     * @param historySplitList
     * @return
     */
    @Override
    public Boolean getBondAdjustDto(BondWaterToNccDto water, List<BondSplitDto> historySplitList) {
        //查询客商信息
        CustomerToNcc customerInfo = nccBondPushLogService.getCustomerInfo(water.getCompanyName());
        List<NccBondPushLog> pushLogs = new ArrayList<>();
        //进行调账
        for(BondSplitDto splitDto:historySplitList){
            // 表头
            BondAdjustDto bondAdjustDto = new BondAdjustDto();
            bondAdjustDto.setCreationTime(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, water.getCreatedTime()));
            bondAdjustDto.setPkDeptIdAfter(splitDto.getNccProjectDepartId());
            bondAdjustDto.setPurchaseNumberAfter(splitDto.getPurchaseNumber());
            bondAdjustDto.setBillId(UUID.randomUUID().toString().replaceAll("-", ""));
            //表体
            List<BondAdjustBodyDto> bodyList = new ArrayList<>();
            BondAdjustBodyDto bondAdjust = new BondAdjustBodyDto();
            bondAdjust.setCustomer(customerInfo.getCode());
            bondAdjust.setMoneyCr(String.valueOf(splitDto.getAmount()));
            bondAdjust.setPkDeptIdBefore(NccConstants.OTHER_DEPTID);
            bondAdjust.setPurchaseNumberBefore(NccConstants.OTHER_PURCHASE_NUMBER);
            bondAdjust.setProjectIdAfter(splitDto.getProjectId());
            bondAdjust.setSectionIdAfter(splitDto.getSectionId());
            if(StringUtils.isNotBlank(water.getReceiveAcount())){
                bondAdjust.setRecAccount(water.getReceiveAcount());
            }else{
                //获取招标中心的保证金账户
                BankdataBondToNcc bankdataBondToNcc = bankdataBondToNccService.getBankdataBondToNcc(water.getReceiveBank());
                if(bankdataBondToNcc != null){
                    bondAdjust.setRecAccount(bankdataBondToNcc.getAccount());
                }
            }
            bondAdjust.setReceiveTime(water.getDate()+" "+water.getTime());
            bondAdjust.setWaterId(splitDto.getWaterId());
            bodyList.add(bondAdjust);
            saveAdjustChange(bodyList,customerInfo.getId());
            bondAdjustDto.setBody(bodyList);
            //ncc推送其他
            NccBondPushLog pushLog = new NccBondPushLog();
            pushLog.setBillId(bondAdjustDto.getBillId());
            pushLog.setPushResult(CommonConstants.NO2);
            pushLog.setRequestData(JSONObject.toJSONString(bondAdjustDto));
            pushLog.setPushType(NccPushTypeEnum.ADJUST.getType());
            pushLogs.add(pushLog);
        }
        return nccBondPushLogService.saveBatch(pushLogs);
    }

    /**
     * 是否推送调账记录
     * @return
     */
    @Override
    public Boolean isPushAdjust(BondSplitNccDto splitNccDto){
        Boolean isPush = true;
        //R-050-对保证金多次关联但关联数据没有发生变化的情况进行过滤，不再推送保证金调账单据给用友  start
        //查询推送日志
       /* 逻辑有问题
       LambdaQueryWrapper<NccBondPushLog> logWrapper = new LambdaQueryWrapper<>();
        logWrapper.eq(NccBondPushLog::getPushType,NccPushTypeEnum.ADJUST.getType())
                .like(NccBondPushLog::getRequestData,"\"purchaseNumberAfter\":\""+splitNccDto.getPurchaseNumber())
                .orderByDesc(NccBondPushLog::getCreatedTime);
        List<NccBondPushLog> list = nccBondPushLogService.list(logWrapper);
        if(!list.isEmpty()){
            for(NccBondPushLog a:list){
                //{"billId":"55226a5494414cfc819419cc533a69c1","body":[{"customer":"12969","moneyCr":"5000.00","pkDeptIdBefore":"1407","purchaseNumberBefore":"JSTCC2300310619/2","recAccount":"************","receiveTime":"2023-03-01 09:57:51","waterId":1882},{"customer":"12969","moneyCr":"40000.00","pkDeptIdBefore":"1407","purchaseNumberBefore":"JSTCC2300310619/2","recAccount":"************","receiveTime":"2023-03-01 09:57:51","waterId":1883}],"creationTime":"2023-06-14 09:27:15","pkDeptIdAfter":"1407","purchaseNumberAfter":"JSTCC2300310619/3"}
                //{"billId":"231d3ba1d9d840f2a0d2d134423af75a","body":[{"customer":"12969","moneyCr":"35000.00","pkDeptIdBefore":"9999","purchaseNumberBefore":"JSTCC9999","recAccount":"************","receiveTime":"2023-03-01 09:57:51","waterId":1883}],"creationTime":"2023-06-14 09:26:39","pkDeptIdAfter":"1407","purchaseNumberAfter":"JSTCC2300310619/3"}
                JSONObject jsonObject = JSON.parseObject(a.getRequestData());
                JSONArray body = jsonObject.getJSONArray("body");
                JSONObject pushRecord = body.getJSONObject(0);
                if(splitNccDto.getAmount() != null && splitNccDto.getWaterId() != null) {
                    if (splitNccDto.getAmount().toString().equals(pushRecord.getString("moneyCr"))
                            && splitNccDto.getWaterId().toString().equals(pushRecord.getString("waterId"))) {
                        isPush = false;
                        break;
                    }
                }
            }
        }*/
        //R-050-对保证金多次关联但关联数据没有发生变化的情况进行过滤，不再推送保证金调账单据给用友  end
        return  true;
    }

    @Override
    public Boolean updateRelationId(Long waterId, Long relationId) {
        return this.baseMapper.updateRelationId(waterId, relationId);
    }

    @Override
    public Boolean updateRelationIdNull(List<Long> waterIds) {
        return this.baseMapper.updateRelationIdNull(waterIds);
    }

    /**
     * 根据流水查询最新一条非异常已删除拆分数据
     * @param waterId
     * @return
     */
    @Override
    public BondSplitNccDto queryLastDeleteByWaterId(Long waterId) {
        return this.baseMapper.queryLastDeleteByWaterId(waterId);
    }

    /**
     * 根据流水查询最新一条非异常已删除拆分数据
     * @param waterId
     * @return
     */
    @Override
    public List<BondSplitDto> queryLatestSplit(Long waterId) {
        return this.baseMapper.queryLatestSplit(waterId);
    }

    /**
     * 根据流水查询最新一条非异常已删除拆分数据
     * @param waterId
     * @return
     */
    @Override
    public List<BondSplitDto> queryLatestSplitByRelationId(Long waterId,Long relationId) {
        return this.baseMapper.queryLatestSplitByRelationId(waterId,relationId);
    }

    /**
     * 推送ncc
     * @param historyDtoList
     * @param operateType
     * @param companyId
     * @return
     */
    @Override
    public Boolean pushNccData(List<BondSplitHistoryDto> historyDtoList, Integer operateType, Long companyId) {
        Boolean result = true;
        for (BondSplitHistoryDto dto : historyDtoList) {
            BondWater bondWater = bondWaterService.getById(dto.getWaterId());
            //获取保证金拆分数据
            List<SectionWatersDto> sectionWaters = new ArrayList<>();
            LambdaQueryWrapper<BondSplit> splitQuery = new LambdaQueryWrapper<>();
            splitQuery.eq(BondSplit::getWaterId,dto.getWaterId());
            List<BondSplit> splits = this.list(splitQuery);
            if(!splits.isEmpty()){
                for(BondSplit s :splits){
                    sectionWaters.add(BeanListUtil.convert(s,SectionWatersDto.class));
                }
            }
            BondWaterToNccDto item = BeanListUtil.convert(bondWater,BondWaterToNccDto.class);
            //查询客商信息
            CustomerToNcc customerInfo = nccBondPushLogService.getCustomerInfo(item.getCompanyName());
            BondSplitNccDto splitNccDto = this.queryLastDeleteByWaterId(dto.getWaterId());
            //先判断是不是再次关联，若不是再次关联，先看是不是隔月，若隔月的话推送jstcc9999到新包的调账
            if (splitNccDto!=null) {
                List<BondAdjustBodyDto> adjustBodyDtoList = nccBondPushLogService.saveNccAdjustPushLog(item,sectionWaters,dto.getBondSplitDtoList());
                saveAdjustChange(adjustBodyDtoList,customerInfo.getId());
            } else {
                if(BondUtils.isOverMonth(bondWater.getDate())){
                    List<BondSplitDto> splitDtoList = new ArrayList<>();
                    BondSplitDto bodyDto = new BondSplitDto();
                    bodyDto.setNccProjectDepartId(NccConstants.OTHER_DEPTID);
                    bodyDto.setPurchaseNumber(NccConstants.OTHER_PURCHASE_NUMBER);
                    bodyDto.setAmount(new BigDecimal(bondWater.getAmount()));
                    bodyDto.setCompanyId(companyId);
                    bodyDto.setWaterId(dto.getWaterId());
                    splitDtoList.add(bodyDto);
                    List<BondAdjustBodyDto> adjustBodyDtoList = nccBondPushLogService.saveNccAdjustPushLog(item,sectionWaters,splitDtoList);
                    saveAdjustChange(adjustBodyDtoList,customerInfo.getId());
                }else{
                    result = nccBondPushLogService.saveNccReceivePushLog(item,sectionWaters);
                }
            }
        }
        return  result;
    }

    /**
     * 推送收款单据（其他）
     */
    @Override
    public Boolean saveNccReceiveOtherPushLog(BondWaterToNccDto water) {
        //查询客商信息
        CustomerToNcc customerInfo = nccBondPushLogService.getCustomerInfo(water.getCompanyName());
        BondReceiveDto receiveDto = new BondReceiveDto();
        // 表头属性
        receiveDto.setBillId(UUID.randomUUID().toString().replaceAll("-", ""));
        receiveDto.setCreationTime(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, new Date()));
        // 表体
        List<BondReceiveBodyDto> bodys = new ArrayList<>();
        //推送收款单据到ncc
        bodys.add(nccBondPushLogService.setBondReceiveBody(water,customerInfo, NccConstants.OTHER_PURCHASE_NUMBER,NccConstants.OTHER_DEPTID));
        receiveDto.setBody(bodys);
        NccBondPushLog pushLog = new NccBondPushLog();
        pushLog.setBillId(receiveDto.getBillId());
        //收款已处理流水
        pushLog.setPushType(NccPushTypeEnum.RECEIVE.getType());
        pushLog.setRequestData(JSONObject.toJSONString(receiveDto));
        pushLog.setPushResult(CommonConstants.NO2);
        return nccBondPushLogService.save(pushLog);
    }

    /**
     * 推送调账单据（其他）
     */
    @Override
    public Boolean saveNccAdjustOtherPushLog(BondWaterToNccDto water, List<BondSplitDto> historySplitList) {
        //查询客商信息
        CustomerToNcc customerInfo = nccBondPushLogService.getCustomerInfo(water.getCompanyName());
        List<NccBondPushLog> pushLogs = new ArrayList<>();
        //进行调账
        for(BondSplitDto splitDto:historySplitList){
            // 表头
            BondAdjustDto bondAdjustDto = new BondAdjustDto();
            bondAdjustDto.setCreationTime(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, water.getCreatedTime()));
            bondAdjustDto.setPkDeptIdAfter(NccConstants.OTHER_DEPTID);
            bondAdjustDto.setPurchaseNumberAfter(NccConstants.OTHER_PURCHASE_NUMBER);
            bondAdjustDto.setBillId(UUID.randomUUID().toString().replaceAll("-", ""));
            //表体
            List<BondAdjustBodyDto> bodyList = new ArrayList<>();
            BondAdjustBodyDto bondAdjust = new BondAdjustBodyDto();
            bondAdjust.setPurchaseNumberBefore(splitDto.getPurchaseNumber());
            bondAdjust.setMoneyCr(String.valueOf(splitDto.getAmount()));
            bondAdjust.setWaterId(splitDto.getWaterId());
            bondAdjust.setProjectIdBefore(splitDto.getProjectId());
            bondAdjust.setSectionIdBefore(splitDto.getSectionId());
            bondAdjust.setPkDeptIdBefore(splitDto.getNccProjectDepartId());
            bondAdjust.setCustomer(customerInfo.getCode());
            bondAdjust.setReceiveTime(water.getDate()+" "+water.getTime());
            if(StringUtils.isNotBlank(water.getReceiveAcount())){
                bondAdjust.setRecAccount(water.getReceiveAcount());
            }else{
                //获取招标中心的保证金账户
                BankdataBondToNcc bankdataBondToNcc = bankdataBondToNccService.getBankdataBondToNcc(water.getReceiveBank());
                if(bankdataBondToNcc != null){
                    bondAdjust.setRecAccount(bankdataBondToNcc.getAccount());
                }
            }
            bodyList.add(bondAdjust);
            saveAdjustChange(bodyList,customerInfo.getId());
            bondAdjustDto.setBody(bodyList);
            //ncc推送其他
            NccBondPushLog pushLog = new NccBondPushLog();
            pushLog.setBillId(bondAdjustDto.getBillId());
            pushLog.setPushResult(CommonConstants.NO2);
            pushLog.setRequestData(JSONObject.toJSONString(bondAdjustDto));
            pushLog.setPushType(NccPushTypeEnum.ADJUST.getType());
            pushLogs.add(pushLog);
        }
        return nccBondPushLogService.saveBatch(pushLogs);
    }

    /**
     * 保存调整变化表
     * @param adjustBodyDtoList
     * @param companyId 客商id
     * @return
     */
    @Override
    public boolean saveAdjustChange(List<BondAdjustBodyDto> adjustBodyDtoList,Long companyId){
        boolean result = true;
        if (adjustBodyDtoList != null) {
            //保存调账记录t_bond_change_records
            List<BondChangeRecords> bondChangeRecordsList = new ArrayList<>();
            for (BondAdjustBodyDto bondAdjustBodyDto : adjustBodyDtoList) {
                BondChangeRecords bondChangeRecords = new BondChangeRecords();
                bondChangeRecords.setProjectId(bondAdjustBodyDto.getProjectIdAfter());
                bondChangeRecords.setSectionId(bondAdjustBodyDto.getSectionIdAfter());
                bondChangeRecords.setCompanyId(companyId);
                bondChangeRecords.setOperation(0);
                bondChangeRecords.setAmount(new BigDecimal(bondAdjustBodyDto.getMoneyCr()));
                bondChangeRecords.setFormerProjectId(bondAdjustBodyDto.getProjectIdBefore());
                bondChangeRecords.setFormerSectionId(bondAdjustBodyDto.getSectionIdBefore());
                bondChangeRecords.setChangeUserId(SecurityUtils.getJwtUser().getUserId());
                bondChangeRecords.setChangeTime(new Date());
                bondChangeRecordsList.add(bondChangeRecords);
            }
            bondChangeRecordsService.saveBatch(bondChangeRecordsList);
        } else {
            result = false;
        }
        return result;
    }

    /**
     * 推送ncc
     * @param waterIdList
     * @param operateType
     * @param companyId
     * @return
     */
//    @Override
//    public Boolean pushNccData(List<Long> waterIdList, Integer operateType, Long companyId) {
//        List<Long> adjustIds = new ArrayList<>();
//        List<Long> receiveIds = new ArrayList<>();
//        for (Long waterId : waterIdList) {
//            BondWater bondWater = bondWaterService.getById(waterId);
//            BondSplitNccDto splitNccDto = this.queryLastDeleteByWaterId(waterId);
//            // 再次关联-调整 隔月-调整
//            if (splitNccDto != null || BondUtils.isOverMonth(bondWater.getDate())) {
//                adjustIds.add(waterId);
//            } else {
//                // 校验当前流水关联标包是否有过关联记录
//                List<BondSplitNccDto> deleteSectionList = this.baseMapper.queryLastDeleteBySectionId(waterId, companyId);
//                if (deleteSectionList.size() > 0) {
//                    adjustIds.add(waterId);
//                } else {
//                    receiveIds.add(waterId);
//                }
//            }
//        }
//        List<NccBondPushLog> list = new ArrayList<>();
//        // 收款数据
//        if (receiveIds.size() > 0) {
//            for (Long id : receiveIds) {
//                BondReceiveDto receiveDto = this.getBondReceiveDtoByWaterId(id);
//                NccBondPushLog pushLog = new NccBondPushLog();
//                pushLog.setBillId(receiveDto.getBillId());
//                pushLog.setPushResult(CommonConstants.NO2);
//                pushLog.setRequestData(JSONObject.toJSONString(receiveDto));
//                pushLog.setPushType(NccPushTypeEnum.RECEIVE.getType());
//                list.add(pushLog);
//            }
//        }
//        // 调整数据
//        if (adjustIds.size() > 0) {
//            List<BondAdjustDto> dtoList = this.getBondAdjustDtoBatch(adjustIds, operateType);
//            for (BondAdjustDto adjustDto : dtoList) {
//                NccBondPushLog pushLog = new NccBondPushLog();
//                pushLog.setBillId(adjustDto.getBillId());
//                pushLog.setPushResult(CommonConstants.NO2);
//                pushLog.setRequestData(JSONObject.toJSONString(adjustDto));
//                pushLog.setPushType(NccPushTypeEnum.ADJUST.getType());
//                list.add(pushLog);
//            }
//        }
//        return nccBondPushLogService.saveBatch(list);
//    }

    /**
     * 批量修改流水关联标识
     * @param waterIds
     * @return
     */
    @Override
    public boolean updateSplitForExcept(List<Long> waterIds) {
        return this.baseMapper.updateSplitForExcept(waterIds) > 0;
    }

    /**
     * 批量修改流水关联标识
     * @param waterIds
     * @return
     */
    @Override
    public boolean updateSplitForIsLatest(List<Long> waterIds) {
        return this.baseMapper.updateSplitForIsLatest(waterIds) > 0;
    }

    public static void main(String[] args) {
        /**
         * 包    流水              包       流水
         * 1    101               1        101，102
         * 1    102
         * 2    102               2        102，103
         * 2    103
         * 3    103               3        103
         */
        List<BondSplitNccDto> list = new ArrayList<>();
        BondSplitNccDto dto = new BondSplitNccDto();
        dto.setSectionId(1L);
        dto.setWaterId(101L);
        list.add(dto);
        BondSplitNccDto dto1 = new BondSplitNccDto();
        dto1.setSectionId(1L);
        dto1.setWaterId(102L);
        list.add(dto1);
        BondSplitNccDto dto2 = new BondSplitNccDto();
        dto2.setSectionId(2L);
        dto2.setWaterId(102L);
        list.add(dto2);
        BondSplitNccDto dto3 = new BondSplitNccDto();
        dto3.setSectionId(2L);
        dto3.setWaterId(103L);
        list.add(dto3);
        BondSplitNccDto dto4 = new BondSplitNccDto();
        dto4.setSectionId(3L);
        dto4.setWaterId(103L);
        list.add(dto4);



    }





}
