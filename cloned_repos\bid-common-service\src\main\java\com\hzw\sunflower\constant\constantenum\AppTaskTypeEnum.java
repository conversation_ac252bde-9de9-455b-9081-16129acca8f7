package com.hzw.sunflower.constant.constantenum;


import java.io.Serializable;

/**
 * <AUTHOR>
 * @description app待办类型
 * @date 2021/6/28 12:10
 */
public enum AppTaskTypeEnum implements Serializable {

    /**
     * 项目待处理相关类型
     */
    NOTICE_CONTENT("noticeContent", "项目待处理", "采购公告"),
    CH_NOTICE_CONTENT("CH_noticeContent", "项目待处理", "采购公告撤回"),
    NOTICE_DOC("noticeDoc", "项目待处理", "采购文件"),
    CH_NOTICE_DOC("CH_noticeDoc", "项目待处理", "采购文件撤回"),
    SUPPLEMENT_NOTICE("SUPPLEMENT_NOTICE", "项目待处理", "更正公告"),
    CH_SUPPLEMENT_NOTICE("CH_SUPPLEMENT_NOTICE", "项目待处理", "更正公告撤回"),
    CLARIFY_REPLY("CLARIFY_REPLY", "项目待处理", "澄清/修改"),
    CH_CLARIFY_REPLY("CH_CLARIFY_REPLY", "项目待处理", "澄清/修改撤回"),
    OBJECTION("OBJECTION", "项目待处理", "异议回复"),
    CH_OBJECTION("CH_OBJECTION", "项目待处理", "异议回复撤回"),
    BIDWINPEOPLE_ZGYS("BIDWINPEOPLE_ZGYS", "项目待处理", "确认资格预审结果"),
    BIDWINPEOPLE("BIDWINPEOPLE", "项目待处理", "确认评标结果"),
    CH_BIDWINPEOPLE("CH_BIDWINPEOPLE", "项目待处理", "确认评标结果撤回"),
    BID_WIN_CAN_BULLETIN("BID_WIN_CAN_BULLETIN", "项目待处理", "中标候选人公示"),
    CH_BID_WIN_CAN_BULLETIN("CH_BID_WIN_CAN_BULLETIN", "项目待处理", "中标候选人公示撤回"),
    BID_WIN_BULLETIN("BID_WIN_BULLETIN", "项目待处理", "中标结果公示"),
    CH_BID_WIN_BULLETIN("CH_T_BID_WIN_BULLETIN", "项目待处理", "中标结果公示撤回"),
    BID_WIN_NOTICE_PRE("BID_WIN_NOTICE_PRE", "项目待处理", "申请资格预审通知书"),
    CH_BID_WIN_NOTICE_PRE("CH_BID_WIN_NOTICE_PRE", "项目待处理", "申请资格预审通知书撤回"),
    REAPPLY_BID_WIN_NOTICE("REAPPLY_BID_WIN_NOTICE", "项目待处理", "重新申请通知书"),
    CH_REAPPLY_BID_WIN_NOTICE("CH_REAPPLY_BID_WIN_NOTICE", "项目待处理", "重新申请通知书撤回"),
    REAPPLY_BID_WIN_NOTICE_PRE("REAPPLY_BID_WIN_NOTICE_PRE", "项目待处理", "重新申请资格预审通知书"),
    CH_REAPPLY_BID_WIN_NOTICE_PRE("CH_REAPPLY_BID_WIN_NOTICE_PRE", "项目待处理", "重新申请资格预审通知书撤回"),
    BID_WIN_NOTICE("BID_WIN_NOTICE", "项目待处理", "中标通知书"),
    CH_BID_WIN_NOTICE("CH_BID_WIN_NOTICE", "项目待处理", "中标通知书撤回"),
    EXCEPTION_MANAGE("EXCEPTION_MANAGE", "项目待处理", "采购异常"),
    CH_EXCEPTION_MANAGE("CH_EXCEPTION_MANAGE", "项目待处理", "采购异常撤回"),
    ARCHIVE_RECALL("ARCHIVE_RECALL", "项目待处理", "归档撤回"),
    SUPPLIER_REGISTER_APPLY("SUPPLIER_REGISTER_APPLY", "项目待处理", "登记供应商"),
    APPLY_PAY_FILE("APPLY_PAY_FILE", "项目待处理", "支付凭证"),
    AGENCY_FEE_APPLY("AGENCY_FEE_APPLY", "项目待处理", "代理服务费"),
    CH_AGENCY_FEE_APPLY("CH_AGENCY_FEE_APPLY", "项目待处理", "代理服务费撤回"),
    LOCK_APPLICATION("LOCK_APPLICATION", "项目待处理", "用锁"),
    CH_LOCK_APPLICATION("CH_LOCK_APPLICATION", "项目待处理", "用锁撤回"),
    MATERIAL_APPLY("MATERIAL_APPLY", "项目待处理", "材料"),
    CH_MATERIAL_APPLY("CH_MATERIAL_APPLY", "项目待处理", "材料撤回"),
    COMPLAINT_RECOVER("COMPLAINT_RECOVER",  "项目待处理", "投诉回复"),

    /**
     * 待办事项类型
     */
    TENDER_BID("tenderbid", "待办事项", "公告发布"),
    TENDER_DOC("tenderdoc", "待办事项", "文件挂网"),
    SUPPLEMENT_BID("supplementbid", "待办事项","补充公告发布"),
    WINCANDIDATE_BID("wincandidatebid", "待办事项","中标候选人公示发布"),
    WIN_BID("winbid", "待办事项","中标结果公示发布"),
    /**
     * 数据修改类型
     */
    DATA_UPDATE("DATA_UPDATE", "数据修改", "数据修改"),

    /**
     * 保证金异常关联
     */
    BOND_ABNORMAL_RELATION("BOND_ABNORMAL_RELATION", "保证金异常关联", "保证金异常关联"),
    /**
     * 代供应商管理员审核用户
     */
    ADMIN_AUDIT("ADMIN_AUDIT", "代供应商管理员审核用户", "代供应商管理员审核用户"),
    /**
     * 审核平台用户
     */
    USER_AUDIT("USER_AUDIT", "审核平台用户", "审核平台用户"),
    /**
     * 专家库
     */
    EXPERT_EXTRACTION("EXPERT_EXTRACTION", "专家抽取待处理", "专家抽取"),
    EXPERT_APPRAISE("EXPERT_APPRAISE", "专家扣分待处理", "专家扣分"),
//    PROJECT_SHARE("PROJECT_SHARE", "专家库待处理", "专家抽取超时"),
    EXPERT_EXTRACTION_BEIAN("EXPERT_EXTRACTION_BEIAN", "专家抽取备案待处理", "专家抽取备案"),

    /**
     * 劳务费
     */
    EXPERT_FEE_EXPENSE_APPLICATION("EXPERT_FEE_EXPENSE_APPLICATION", "劳务费", "报销申请"),
    EXPERT_FEE_ASSIT("EXPERT_FEE_ASSIT", "劳务费", "协评人员申请"),
    EXPERT_FEE_APPLICATION("EXPERT_FEE_APPLICATION", "劳务费", "直接支付"),
    EXPERT_FEE_APPLICATION_EXTERNAL("EXPERT_FEE_APPLICATION_EXTERNAL", "劳务费", "外库直接支付"),
    EXPERT_FEE_APPLICATION_AGENT("EXPERT_FEE_APPLICATION_AGENT", "劳务费", "进场代付登记确认"),
    EXPERT_FEE_APPLICATION_OFFLINE("EXPERT_FEE_APPLICATION_OFFLINE", "劳务费", "线下支付"),


    /**
     * 项目分摊待确认
     */
    PROJECT_SHARE("PROJECT_SHARE", "项目分摊", "项目分摊"),

    /**
     * 用印审批
     */
    REPORTING_MATTER("REPORTING_MATTER","用印申请-呈报事项","用印申请-呈报事项"),
    REPORTING_MATTER_ONE("REPORTING_MATTER_ONE","用印申请-呈报事项-领导审批","用印申请-呈报事项-领导审批"),
    WITHDRAW_SEAL_APPLICATION("WITHDRAW_SEAL_APPLICATION","用印申请-撤回","用印申请-撤回"),
    CONTRACTUAL_MATTERS("CONTRACTUAL_MATTERS","用印申请-合同事项","用印申请-合同事项"),
    DRAFT_TEXT_CLARIFY("DRAFT_TEXT_CLARIFY","用印申请-拟文-澄清/修改","用印申请-拟文-澄清/修改"),
    DRAFT_TEXT_REPLY("DRAFT_TEXT_REPLY","用印申请-拟文-异议回复","用印申请-拟文-异议回复"),
    DRAFT_TEXT_OTHER("DRAFT_TEXT_OTHER","用印申请-拟文-其他","用印申请-拟文-其他"),
    SEAL_APPROVAL_TENDER("SEAL_APPROVAL_TENDER","用印申请-用印审批-投标文件","用印申请-用印审批-投标文件"),
    SEAL_APPROVAL_REPORT("SEAL_APPROVAL_REPORT","用印申请-用印审批-评标报告","用印申请-用印审批-评标报告"),
    SEAL_APPROVAL_PURCHASING("SEAL_APPROVAL_PURCHASING","用印申请-用印审批-采购文件","用印申请-用印审批-采购文件"),


    /**
     * 非流程类型，仅作展示用
     */
    SHOW_BIDWINPEOPLE("SHOW_BIDWINPEOPLE", "项目待处理", "确认中标情况"),
    SHOW_BID_WIN_BULLETIN("SHOW_BID_WIN_BULLETIN", "项目待处理", "中标公告"),
    SHOW_BID_WIN_NOTICE("SHOW_BID_WIN_NOTICE", "项目待处理", "申请通知书"),
    OTHER("OTHER", "其他类型", "待办");

    private String code;
    private String module;
    private String msg;

    AppTaskTypeEnum(String code, String module, String msg) {
        this.code = code;
        this.module = module;
        this.msg = msg;
    }

    /**
     * 获取对应的枚举
     *
     * @param code 种类
     * @return 枚举值
     */
    public static AppTaskTypeEnum getTaskType(String code) {
        try {
            for (AppTaskTypeEnum taskTypeEnum : AppTaskTypeEnum.values()) {
                if (taskTypeEnum.code.equals(code)) {
                    return taskTypeEnum;
                }
            }
            return OTHER;
        } catch (Exception e) {
            return OTHER;
        }
    }

    public static AppTaskTypeEnum getTaskTypeByMsg(String msg) {
        try {
            for (AppTaskTypeEnum taskTypeEnum : AppTaskTypeEnum.values()) {
                if (taskTypeEnum.msg.equals(msg)) {
                    return taskTypeEnum;
                }
            }
            return OTHER;
        } catch (Exception e) {
            return OTHER;
        }
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public String getModule() {
        return module;
    }

    public void setModule(String module) {
        this.module = module;
    }
}
