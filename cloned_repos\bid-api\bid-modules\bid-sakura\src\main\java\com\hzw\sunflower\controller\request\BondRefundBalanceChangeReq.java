package com.hzw.sunflower.controller.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <p>
 * 保证金退还余额变动
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-28
 */
@Data
public class BondRefundBalanceChangeReq {

    @ApiModelProperty(value = "流水总额增加值")
    private String totalAmountAdd;

    @ApiModelProperty(value = "保证金退还总额增加值")
    private String bondRefundAdd;

    @ApiModelProperty(value = "退还账户")
    private String refundNumber;


}
