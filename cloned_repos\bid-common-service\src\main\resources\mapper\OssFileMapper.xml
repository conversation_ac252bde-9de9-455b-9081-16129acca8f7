<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!-- oss文件表 -->
<mapper namespace="com.hzw.sunflower.dao.OssFileMapper">

    <!-- 根据文件id集合查询 -->
    <select id="listByIdsOrder" resultType="com.hzw.sunflower.entity.OssFile">
        SELECT
            *
        FROM
            t_oss_file
        WHERE id IN
            <foreach collection="ossFileIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        ORDER BY
            FIELD(id,
            <foreach collection="ossFileIds" item="value" separator=",">
                #{value}
            </foreach>
            )
    </select>
</mapper>