package com.hzw.sunflower.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hzw.sunflower.common.BaseBean;
import com.hzw.sunflower.constant.constantenum.ReviewFileTypeEnum;
import com.hzw.sunflower.entity.ApplyResponseFile;
import com.hzw.sunflower.entity.ProjectSectionBidOpen;
import com.hzw.sunflower.entity.ReviewSign;
import com.hzw.sunflower.service.ApplyResponseFileService;
import com.hzw.sunflower.service.CommonBidOpenService;
import com.hzw.sunflower.service.ProjectSectionBidOpenService;
import com.hzw.sunflower.service.ReviewSignService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @datetime 2022/11/17 14:28
 * @description: 线上开标service实现类
 * @version: 1.0
 */
@Service
public class CommonBidOpenServiceImpl implements CommonBidOpenService {

    @Autowired
    ProjectSectionBidOpenService projectSectionBidOpenService;

    @Autowired
    ApplyResponseFileService applyResponseFileService;

    @Autowired
    ReviewSignService reviewSignService;

    /**
     * 获取标段线上开标配置
     * @param sectionId
     * @param bidRound
     * @return
     */
    @Override
    public Map<String, Object> getBidOpenBySectionId(String sectionId, Integer bidRound) {
        ProjectSectionBidOpen projectSectionBidOpen = projectSectionBidOpenService.getOne(new LambdaQueryWrapper<ProjectSectionBidOpen>()
                .eq(ProjectSectionBidOpen::getSectionId, sectionId)
                .eq(ProjectSectionBidOpen::getBidRound, bidRound));
        return projectSectionBidOpen != null ? JSONObject.parseObject(JSONObject.toJSONString(projectSectionBidOpen), Map.class) : null;
    }

    /**
     * 根据报名id获取响应文件
     * @param applyId
     * @return
     */
    @Override
    public List<Map<String, Object>> getResponseFileByApplyId(Long applyId) {
        List<Map<String, Object>> list = new ArrayList<>();
        List<ApplyResponseFile> responseFileList = applyResponseFileService.list(new LambdaQueryWrapper<ApplyResponseFile>().eq(ApplyResponseFile::getApplyId, applyId));
        for (ApplyResponseFile file : responseFileList) {
            list.add(JSONObject.parseObject(JSONObject.toJSONString(file), Map.class));
        }
        return list;
    }

    /**
     * 根据评审id获取最新的评审报告签章文件
     * @param reviewId
     * @return
     */
    @Override
    public Map<String, Object> queryReviewReportLatest(Long reviewId) {
        ReviewSign reviewSign = reviewSignService.getOne(new LambdaQueryWrapper<ReviewSign>().eq(ReviewSign::getReviewId, reviewId)
                .eq(ReviewSign::getFileType, ReviewFileTypeEnum.REPORT.getType()).orderByDesc(ReviewSign::getId).last("limit 1"));
        return reviewSign != null ? JSONObject.parseObject(JSONObject.toJSONString(reviewSign), Map.class) : null;
    }

    /**
     * 根据专家id和评审id查询评审报告签章情况
     * @param userId
     * @param reviewId
     * @return
     */
    @Override
    public Map<String, Object> queryReviewReportSign(Long userId, Long reviewId) {
        ReviewSign reviewSign = reviewSignService.getOne(new LambdaQueryWrapper<ReviewSign>().eq(ReviewSign::getReviewId, reviewId).eq(ReviewSign::getUserId, userId)
                .eq(ReviewSign::getFileType, ReviewFileTypeEnum.REPORT.getType()));
        return reviewSign != null ? JSONObject.parseObject(JSONObject.toJSONString(reviewSign), Map.class) : null;
    }

}
