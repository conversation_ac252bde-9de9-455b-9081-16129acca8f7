package com.hzw.sunflower.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.constant.MessageConstants;
import com.hzw.sunflower.controller.request.BidDocTemplateListREQ;
import com.hzw.sunflower.controller.responese.BidDocTemplateVO;
import com.hzw.sunflower.dto.BidDocTemplateDTO;
import com.hzw.sunflower.entity.BidDocTemplate;
import com.hzw.sunflower.dao.BidDocTemplateMapper;
import com.hzw.sunflower.service.BidDocTemplateService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 招标文件编制模板表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-26
 */
@Service
public class BidDocTemplateServiceImpl extends ServiceImpl<BidDocTemplateMapper, BidDocTemplate> implements BidDocTemplateService {



    /**
     * 招标文件编制模板列表
     * @param condition
     * @return
     */
    @Override
    public IPage<BidDocTemplateVO> selectTemplateList(BidDocTemplateListREQ condition) {
        return this.getBaseMapper().selectTemplateList(condition.buildPage(),condition);
    }

    /**
     * 启用/停用
     * @param dto
     * @return
     */
    @Override
    public Result<Object> onOrOff(BidDocTemplateDTO dto) {
        if(null == dto.getId() || null == dto.getStatus()){
         return  Result.failed(MessageConstants.PARAMS_NOT_NULL);
        }
        BidDocTemplate template = new BidDocTemplateDTO();
        template.setId(dto.getId());
        template.setStatus(dto.getStatus());
        boolean b = this.updateById(template);
        return Result.okOrFailed(b);
    }
}
