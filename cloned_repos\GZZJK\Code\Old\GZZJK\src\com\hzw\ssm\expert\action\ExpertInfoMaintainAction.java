package com.hzw.ssm.expert.action;

import com.alibaba.fastjson.JSON;
import com.hzw.ssm.applets.dao.DictionaryMapper;
import com.hzw.ssm.applets.dao.EducationInfoMapper;
import com.hzw.ssm.applets.dao.TitleInfoMapper;
import com.hzw.ssm.applets.entity.Dictionary;
import com.hzw.ssm.applets.entity.EducationInfo;
import com.hzw.ssm.applets.entity.TitleInfo;
import com.hzw.ssm.expert.entity.*;
import com.hzw.ssm.expert.service.ExpertEducationService;
import com.hzw.ssm.expert.service.ExpertInfoService;
import com.hzw.ssm.fw.base.BaseAction;
import com.hzw.ssm.fw.util.SysConstants;
import com.hzw.ssm.sys.user.entity.UserEntity;
import net.sf.json.JSONArray;
import net.sf.json.JsonConfig;
import net.sf.json.util.PropertyFilter;
import org.apache.struts2.convention.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;

import javax.servlet.http.HttpSession;
import java.io.File;
import java.util.Date;
import java.util.List;

@Namespace("/expertMaintain")
@ParentPackage(value = "default")
@Results({@Result(name = "initExpertEdit", location = "/jsp/expert/expert_info_edit.jsp"),
        @Result(name = "specialtyInfoList", location = "/jsp/expert/specialtyInfoList.jsp")
})
/**
 * 专家基本信息维护Action
 */
public class ExpertInfoMaintainAction extends BaseAction {

    private static final long serialVersionUID = -8823858495799309882L;

    /**
     * 通用key
     */
    private String key;

    /**
     * 提示信息
     */
    private String message;

    /**
     * 专家基本信息
     */
    private ExpertInfoEntity expertInfoEntity;

    /**
     * 专家基本信息封装查询条件
     */
    private ExpertInfoEntity colExpertInfo;

    /**
     * 身份证复印件对应的文件域
     */
    private File idFile;

    /**
     * 身份证复印件对应文件名
     */
    private String idFileFileName;

    /**
     * 毕业证书复印对应的文件域
     */
    private File certificateFile;

    /**
     * 毕业证书复印对应的文件名
     */
    private String certificateFileFileName;

    /**
     * 证件照片对应的文件域
     */
    private File photoFileid;

    /**
     * 证件照片对应的文件名
     */
    private String photoFileidFileName;

    /**
     * 职称证书 对应的文件域
     */
    private File technicalFiled;

    /**
     * 职称证书 对应文件名
     */
    private String technicalFiledFileName;

    /**
     * 执业资格文件列表对应页面多个文件域
     */
    private File practiceFile1;
    private File practiceFile2;
    private File practiceFile3;
    private File practiceFile4;
    private File practiceFile5;

    /**
     * 执业资格文件名列表对应页面多个文件域文件名
     */
    private String practiceFile1FileName;
    private String practiceFile2FileName;
    private String practiceFile3FileName;
    private String practiceFile4FileName;
    private String practiceFile5FileName;

    // 学历
    private File educationFile1;
    private File educationFile2;
    private File educationFile3;
    private File educationFile4;
    private File educationFile5;
    private File educationFile0;

    private String educationFile1FileName;
    private String educationFile2FileName;
    private String educationFile3FileName;
    private String educationFile4FileName;
    private String educationFile5FileName;
    private String educationFile0FileName;

    // 学位
    private File academicFile0;
    private File academicFile1;
    private File academicFile2;
    private File academicFile3;
    private File academicFile4;
    private File academicFile5;


    private String academicFile0FileName;
    private String academicFile1FileName;
    private String academicFile2FileName;
    private String academicFile3FileName;
    private String academicFile4FileName;
    private String academicFile5FileName;


    // 职称
    private File picture0;
    private File picture1;
    private File picture2;
    private File picture3;
    private File picture4;
    private File picture5;

    private String picture1FileName;
    private String picture2FileName;
    private String picture3FileName;
    private String picture4FileName;
    private String picture5FileName;
    private String picture0FileName;

    /**
     * 身份证复印件背面对应的文件域
     */
    private File ICBackFile;
    /**
     * 身份证复印件背面文件名
     */
    private String ICBackFileFileName;

    /**
     * 执业资格
     */
    private List<ExpertPracticeEntity> practiceList;

    /**
     * 评标专业列表集合
     */
    private List<ExpertMajorEntity> majorList;

    /**
     * 工作经历集合列表
     */
    private List<ExpertExperienceEntity> experienceList;
    /**
     * 职称字典
     */
    private List<Dictionary> dictionaryList;
    /**
     * 学历信息
     */
    private List<EducationInfo> educationList;
    /**
     * 职称信息
     */
    private List<TitleInfo> titleInfoList;

    /**
     * 评标专业 json格式字符串
     **/
    private String jsonSpecialtyInfo;

    /**
     * 审核意见实体类
     */
    private ExpertAuditEntity auditEntity;

    @Autowired
    private ExpertInfoService expertInfoService;
    @Autowired
    private EducationInfoMapper educationInfoMapper;
    @Autowired
    private TitleInfoMapper titleInfoMapper;
    @Autowired
    private ExpertEducationService educationService;
    @Autowired
    private DictionaryMapper dictionaryMapper;


    /**
     * 基本信息编辑页面
     *
     * @return
     * @throws Exception
     */
    @Action("initExpertEdit")
    public String toExpertInfoEdit() throws Exception {
        this.context();
        //当前session
        HttpSession session = getRequest().getSession();
        //当前用户信息
        UserEntity info = (UserEntity) session.getAttribute("userInfo");
        if (colExpertInfo == null) {
            colExpertInfo = new ExpertInfoEntity();
            colExpertInfo.setUser_id(info.getUser_id());
        }
        expertInfoEntity = expertInfoService.getExpertInfoByUserIdOrId(colExpertInfo);
        practiceList = expertInfoService.queryPracticeByUserId(info.getUser_id());
        majorList = expertInfoService.queryMajorByUserId(info.getUser_id());
        experienceList = expertInfoService.queryExpertExperienceList(info.getUser_id());
        auditEntity = expertInfoService.getExpertAuditEntityByUserId(info.getUser_id());

        dictionaryList = dictionaryMapper.queryTitle();
        // 学历和职称
        educationList = educationInfoMapper.queryEducationInfoList(colExpertInfo.getUser_id() != null && "".equals(colExpertInfo.getUser_id()) ? colExpertInfo.getUser_id() : info.getUser_id());
        titleInfoList = titleInfoMapper.queryTitleInfoList(colExpertInfo.getUser_id() != null && "".equals(colExpertInfo.getUser_id()) ? colExpertInfo.getUser_id() : info.getUser_id());
        for (TitleInfo titleInfo : titleInfoList) {
            List<String> pictures = JSON.parseArray(titleInfo.getPicture(), String.class);
            titleInfo.setPictures(pictures);
        }
        return "initExpertEdit";
    }

    /**
     * 专家基本信息申请保存
     *
     * @return
     * @throws Exception
     */
    @Action("saveExpertInfo")
    public String expertInfoMaintain() throws Exception {
        try {
            this.context();
            //当前session
            HttpSession session = getRequest().getSession();
            //当前用户信息
            UserEntity info = (UserEntity) session.getAttribute("userInfo");

//			expertInfoService.uploadFile(expertInfoEntity,practiceList,idFile,idFileFileName,certificateFile,certificateFileFileName,photoFileid,photoFileidFileName,technicalFiled,technicalFiledFileName,practiceFile1,practiceFile1FileName,practiceFile2,practiceFile2FileName,practiceFile3,practiceFile3FileName);
            expertInfoService.newUploadFile(expertInfoEntity, practiceList, idFile, idFileFileName, certificateFile, certificateFileFileName, photoFileid, photoFileidFileName, technicalFiled, technicalFiledFileName, practiceFile1, practiceFile1FileName, practiceFile2, practiceFile2FileName, practiceFile3, practiceFile3FileName, ICBackFile, ICBackFileFileName);
            // 学历、学位 文件流处理
            educationService.uploadExpertEducation(expertInfoEntity, educationList, educationFile1, educationFile1FileName, educationFile2, educationFile2FileName, educationFile3, educationFile3FileName, educationFile4, educationFile4FileName, educationFile5, educationFile5FileName, educationFile0, educationFile0FileName);
            educationService.uploadExpertAcademic(expertInfoEntity, educationList, academicFile0, academicFile0FileName, academicFile1, academicFile1FileName, academicFile2, academicFile2FileName, academicFile3, academicFile3FileName, academicFile4, academicFile4FileName, academicFile5, academicFile5FileName);
            educationService.uploadTitleInfo(expertInfoEntity, titleInfoList, picture0, picture0FileName, picture1, picture1FileName, picture2, picture2FileName, picture3, picture3FileName, picture4, picture4FileName, picture5, picture5FileName);

            expertInfoEntity.setModify_time(new Date());
            expertInfoEntity.setModify_user(info.getUser_id());
            expertInfoEntity.setModify_role(info.getRole_id());
            expertInfoEntity.setModify_reason("");
            if (expertInfoEntity.getId() == null || "".equals(expertInfoEntity.getId())) {
                expertInfoEntity.setEnterFlag(SysConstants.ENTER_FLAG.EXPERT_ENTER);//专家录入标识
            }
            expertInfoService.compareExpertInfoManager(expertInfoEntity, practiceList, majorList, experienceList);

            // 学历、学位新增
            educationService.addExpertEducation(expertInfoEntity, educationList, info.getRole_id());
            educationService.addExpertTitle(expertInfoEntity, titleInfoList, info.getRole_id());
        } catch (Exception e) {
            e.printStackTrace();
            expertInfoService.uploadFileDelete(expertInfoEntity, practiceList, idFile, idFileFileName, certificateFile, certificateFileFileName, practiceFile1, practiceFile1FileName, practiceFile2, practiceFile2FileName, practiceFile3, practiceFile3FileName, practiceFile4, practiceFile4FileName, practiceFile5, practiceFile5FileName);
        }
        return this.toExpertInfoEdit();
    }


    /**
     * 查询评标专业列表
     *
     * @return
     */
    @Action("querySpecialtyInfoList")
    public String querySpecialtyInfoList() {
        List<SpecialtyInfoEntity> specialtyInfoList = expertInfoService.querySpecialtyInfoList();
        JsonConfig config = new JsonConfig();
        //通过JsonConfig的属性过滤器去除不需要的属性
        config.setJsonPropertyFilter(new PropertyFilter() {
            @Override
            public boolean apply(Object arg0, String arg1, Object arg2) {
                if (arg1.equals("spe_id") || arg1.equals("spe_parent") || arg1.equals("spe_name")) {
                    return false;//不忽略
                }
                return true;
            }
        });
        JSONArray jsonArray = JSONArray.fromObject(specialtyInfoList, config);
        jsonSpecialtyInfo = jsonArray.toString();
        //将属性名称修改zTree对应的属性名称
        jsonSpecialtyInfo = jsonSpecialtyInfo.replaceAll("spe_parent", "pId").replaceAll("spe_name", "name").replaceAll("spe_id", "id");
        return "specialtyInfoList";
    }

    /**
     * 删除工作经历
     *
     * @return
     */
    @Action("deleteExperienceById")
    public String deleteExperienceById() {
        if (key != null) {
            expertInfoService.deleteExpertExperienceById(key);
        }
        return null;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }


    public ExpertInfoEntity getExpertInfoEntity() {
        return expertInfoEntity;
    }


    public void setExpertInfoEntity(ExpertInfoEntity expertInfoEntity) {
        this.expertInfoEntity = expertInfoEntity;
    }

    public File getIdFile() {
        return idFile;
    }

    public void setIdFile(File idFile) {
        this.idFile = idFile;
    }

    public String getIdFileFileName() {
        return idFileFileName;
    }

    public void setIdFileFileName(String idFileFileName) {
        this.idFileFileName = idFileFileName;
    }

    public File getCertificateFile() {
        return certificateFile;
    }

    public void setCertificateFile(File certificateFile) {
        this.certificateFile = certificateFile;
    }

    public String getCertificateFileFileName() {
        return certificateFileFileName;
    }

    public void setCertificateFileFileName(String certificateFileFileName) {
        this.certificateFileFileName = certificateFileFileName;
    }

    public ExpertInfoEntity getColExpertInfo() {
        return colExpertInfo;
    }

    public void setColExpertInfo(ExpertInfoEntity colExpertInfo) {
        this.colExpertInfo = colExpertInfo;
    }

    public List<ExpertPracticeEntity> getPracticeList() {
        return practiceList;
    }

    public void setPracticeList(List<ExpertPracticeEntity> practiceList) {
        this.practiceList = practiceList;
    }

    public File getPracticeFile1() {
        return practiceFile1;
    }

    public void setPracticeFile1(File practiceFile1) {
        this.practiceFile1 = practiceFile1;
    }

    public File getPracticeFile2() {
        return practiceFile2;
    }

    public void setPracticeFile2(File practiceFile2) {
        this.practiceFile2 = practiceFile2;
    }

    public File getPracticeFile3() {
        return practiceFile3;
    }

    public void setPracticeFile3(File practiceFile3) {
        this.practiceFile3 = practiceFile3;
    }

    public File getPracticeFile4() {
        return practiceFile4;
    }

    public void setPracticeFile4(File practiceFile4) {
        this.practiceFile4 = practiceFile4;
    }

    public File getPracticeFile5() {
        return practiceFile5;
    }

    public void setPracticeFile5(File practiceFile5) {
        this.practiceFile5 = practiceFile5;
    }

    public String getPracticeFile1FileName() {
        return practiceFile1FileName;
    }

    public void setPracticeFile1FileName(String practiceFile1FileName) {
        this.practiceFile1FileName = practiceFile1FileName;
    }

    public String getPracticeFile2FileName() {
        return practiceFile2FileName;
    }

    public void setPracticeFile2FileName(String practiceFile2FileName) {
        this.practiceFile2FileName = practiceFile2FileName;
    }

    public String getPracticeFile3FileName() {
        return practiceFile3FileName;
    }

    public void setPracticeFile3FileName(String practiceFile3FileName) {
        this.practiceFile3FileName = practiceFile3FileName;
    }

    public String getPracticeFile4FileName() {
        return practiceFile4FileName;
    }

    public void setPracticeFile4FileName(String practiceFile4FileName) {
        this.practiceFile4FileName = practiceFile4FileName;
    }

    public String getPracticeFile5FileName() {
        return practiceFile5FileName;
    }

    public void setPracticeFile5FileName(String practiceFile5FileName) {
        this.practiceFile5FileName = practiceFile5FileName;
    }

    public List<ExpertMajorEntity> getMajorList() {
        return majorList;
    }

    public void setMajorList(List<ExpertMajorEntity> majorList) {
        this.majorList = majorList;
    }

    public String getJsonSpecialtyInfo() {
        return jsonSpecialtyInfo;
    }

    public void setJsonSpecialtyInfo(String jsonSpecialtyInfo) {
        this.jsonSpecialtyInfo = jsonSpecialtyInfo;
    }

    public List<ExpertExperienceEntity> getExperienceList() {
        return experienceList;
    }

    public void setExperienceList(List<ExpertExperienceEntity> experienceList) {
        this.experienceList = experienceList;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public ExpertAuditEntity getAuditEntity() {
        return auditEntity;
    }

    public void setAuditEntity(ExpertAuditEntity auditEntity) {
        this.auditEntity = auditEntity;
    }

    public File getPhotoFileid() {
        return photoFileid;
    }

    public void setPhotoFileid(File photoFileid) {
        this.photoFileid = photoFileid;
    }

    public String getPhotoFileidFileName() {
        return photoFileidFileName;
    }

    public void setPhotoFileidFileName(String photoFileidFileName) {
        this.photoFileidFileName = photoFileidFileName;
    }

    public File getTechnicalFiled() {
        return technicalFiled;
    }

    public void setTechnicalFiled(File technicalFiled) {
        this.technicalFiled = technicalFiled;
    }

    public String getTechnicalFiledFileName() {
        return technicalFiledFileName;
    }

    public void setTechnicalFiledFileName(String technicalFiledFileName) {
        this.technicalFiledFileName = technicalFiledFileName;
    }

    public File getEducationFile1() {
        return educationFile1;
    }

    public void setEducationFile1(File educationFile1) {
        this.educationFile1 = educationFile1;
    }

    public File getEducationFile2() {
        return educationFile2;
    }

    public void setEducationFile2(File educationFile2) {
        this.educationFile2 = educationFile2;
    }

    public File getEducationFile3() {
        return educationFile3;
    }

    public void setEducationFile3(File educationFile3) {
        this.educationFile3 = educationFile3;
    }

    public File getEducationFile4() {
        return educationFile4;
    }

    public void setEducationFile4(File educationFile4) {
        this.educationFile4 = educationFile4;
    }

    public File getEducationFile5() {
        return educationFile5;
    }

    public void setEducationFile5(File educationFile5) {
        this.educationFile5 = educationFile5;
    }

    public File getEducationFile0() {
        return educationFile0;
    }

    public void setEducationFile0(File educationFile0) {
        this.educationFile0 = educationFile0;
    }

    public String getEducationFile1FileName() {
        return educationFile1FileName;
    }

    public void setEducationFile1FileName(String educationFile1FileName) {
        this.educationFile1FileName = educationFile1FileName;
    }

    public String getEducationFile2FileName() {
        return educationFile2FileName;
    }

    public void setEducationFile2FileName(String educationFile2FileName) {
        this.educationFile2FileName = educationFile2FileName;
    }

    public String getEducationFile3FileName() {
        return educationFile3FileName;
    }

    public void setEducationFile3FileName(String educationFile3FileName) {
        this.educationFile3FileName = educationFile3FileName;
    }

    public String getEducationFile4FileName() {
        return educationFile4FileName;
    }

    public void setEducationFile4FileName(String educationFile4FileName) {
        this.educationFile4FileName = educationFile4FileName;
    }

    public String getEducationFile5FileName() {
        return educationFile5FileName;
    }

    public void setEducationFile5FileName(String educationFile5FileName) {
        this.educationFile5FileName = educationFile5FileName;
    }

    public String getEducationFile0FileName() {
        return educationFile0FileName;
    }

    public void setEducationFile0FileName(String educationFile0FileName) {
        this.educationFile0FileName = educationFile0FileName;
    }

    public File getAcademicFile0() {
        return academicFile0;
    }

    public void setAcademicFile0(File academicFile0) {
        this.academicFile0 = academicFile0;
    }

    public File getAcademicFile1() {
        return academicFile1;
    }

    public void setAcademicFile1(File academicFile1) {
        this.academicFile1 = academicFile1;
    }

    public File getAcademicFile2() {
        return academicFile2;
    }

    public void setAcademicFile2(File academicFile2) {
        this.academicFile2 = academicFile2;
    }

    public File getAcademicFile3() {
        return academicFile3;
    }

    public void setAcademicFile3(File academicFile3) {
        this.academicFile3 = academicFile3;
    }

    public File getAcademicFile4() {
        return academicFile4;
    }

    public void setAcademicFile4(File academicFile4) {
        this.academicFile4 = academicFile4;
    }

    public File getAcademicFile5() {
        return academicFile5;
    }

    public void setAcademicFile5(File academicFile5) {
        this.academicFile5 = academicFile5;
    }

    public String getAcademicFile0FileName() {
        return academicFile0FileName;
    }

    public void setAcademicFile0FileName(String academicFile0FileName) {
        this.academicFile0FileName = academicFile0FileName;
    }

    public String getAcademicFile1FileName() {
        return academicFile1FileName;
    }

    public void setAcademicFile1FileName(String academicFile1FileName) {
        this.academicFile1FileName = academicFile1FileName;
    }

    public String getAcademicFile2FileName() {
        return academicFile2FileName;
    }

    public void setAcademicFile2FileName(String academicFile2FileName) {
        this.academicFile2FileName = academicFile2FileName;
    }

    public String getAcademicFile3FileName() {
        return academicFile3FileName;
    }

    public void setAcademicFile3FileName(String academicFile3FileName) {
        this.academicFile3FileName = academicFile3FileName;
    }

    public String getAcademicFile4FileName() {
        return academicFile4FileName;
    }

    public void setAcademicFile4FileName(String academicFile4FileName) {
        this.academicFile4FileName = academicFile4FileName;
    }

    public String getAcademicFile5FileName() {
        return academicFile5FileName;
    }

    public void setAcademicFile5FileName(String academicFile5FileName) {
        this.academicFile5FileName = academicFile5FileName;
    }

    public File getPicture0() {
        return picture0;
    }

    public void setPicture0(File picture0) {
        this.picture0 = picture0;
    }

    public File getPicture1() {
        return picture1;
    }

    public void setPicture1(File picture1) {
        this.picture1 = picture1;
    }

    public File getPicture2() {
        return picture2;
    }

    public void setPicture2(File picture2) {
        this.picture2 = picture2;
    }

    public File getPicture3() {
        return picture3;
    }

    public void setPicture3(File picture3) {
        this.picture3 = picture3;
    }

    public File getPicture4() {
        return picture4;
    }

    public void setPicture4(File picture4) {
        this.picture4 = picture4;
    }

    public File getPicture5() {
        return picture5;
    }

    public void setPicture5(File picture5) {
        this.picture5 = picture5;
    }

    public String getPicture1FileName() {
        return picture1FileName;
    }

    public void setPicture1FileName(String picture1FileName) {
        this.picture1FileName = picture1FileName;
    }

    public String getPicture2FileName() {
        return picture2FileName;
    }

    public void setPicture2FileName(String picture2FileName) {
        this.picture2FileName = picture2FileName;
    }

    public String getPicture3FileName() {
        return picture3FileName;
    }

    public void setPicture3FileName(String picture3FileName) {
        this.picture3FileName = picture3FileName;
    }

    public String getPicture4FileName() {
        return picture4FileName;
    }

    public void setPicture4FileName(String picture4FileName) {
        this.picture4FileName = picture4FileName;
    }

    public String getPicture5FileName() {
        return picture5FileName;
    }

    public void setPicture5FileName(String picture5FileName) {
        this.picture5FileName = picture5FileName;
    }

    public String getPicture0FileName() {
        return picture0FileName;
    }

    public void setPicture0FileName(String picture0FileName) {
        this.picture0FileName = picture0FileName;
    }

    public List<Dictionary> getDictionaryList() {
        return dictionaryList;
    }

    public void setDictionaryList(List<Dictionary> dictionaryList) {
        this.dictionaryList = dictionaryList;
    }

    public List<EducationInfo> getEducationList() {
        return educationList;
    }

    public void setEducationList(List<EducationInfo> educationList) {
        this.educationList = educationList;
    }

    public List<TitleInfo> getTitleInfoList() {
        return titleInfoList;
    }

    public void setTitleInfoList(List<TitleInfo> titleInfoList) {
        this.titleInfoList = titleInfoList;
    }

    public File getICBackFile() {
        return ICBackFile;
    }

    public void setICBackFile(File ICBackFile) {
        this.ICBackFile = ICBackFile;
    }

    public String getICBackFileFileName() {
        return ICBackFileFileName;
    }

    public void setICBackFileFileName(String ICBackFileFileName) {
        this.ICBackFileFileName = ICBackFileFileName;
    }
}
