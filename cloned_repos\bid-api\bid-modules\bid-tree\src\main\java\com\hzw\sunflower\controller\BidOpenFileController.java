package com.hzw.sunflower.controller;

import com.hzw.sunflower.common.BaseController;
import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.common.annotation.RepeatSubmit;
import com.hzw.sunflower.constant.MessageConstants;
import com.hzw.sunflower.controller.request.CompleteSignReq;
import com.hzw.sunflower.controller.request.DocDataInfoREQ;
import com.hzw.sunflower.service.BidOpenFileService;
import com.hzw.sunflower.service.BidOpenFileSignService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

/**
 * <AUTHOR>
 * @datetime 2023/03/07 13:33
 * @description: 开标记录表服务
 * @version: 1.0
 */
@Api(tags = "开标记录表服务")
@RestController
@RequestMapping("/bidOpenFile")
public class BidOpenFileController extends BaseController {

    @Autowired
    private BidOpenFileSignService bidOpenFileSignService;

    @ApiOperation(value = "打开oss文件")
    @GetMapping("/openFile/{fileOssId}")
    public void openFile(@PathVariable Long fileOssId, HttpServletResponse response) {
        bidOpenFileSignService.openFile(fileOssId, response);
    }

    @ApiOperation(value = "返回oss文件临时存储路径")
    @GetMapping("/browseFile/{fileOssId}")
    public Result<String> browseFile(@PathVariable Long fileOssId) {
        return Result.ok(bidOpenFileSignService.browseFile(fileOssId));
    }

    @ApiOperation(value = "完成签章")
    @RepeatSubmit
    @ApiImplicitParam(name = "req", value = "完成签章入参", required = true, dataType = "completeSignReq", paramType = "body")
    @PostMapping("/completeSign")
    public Result<Boolean> completeSign(@RequestBody CompleteSignReq req) {
        if (null == req) {
            return Result.failed(MessageConstants.PARAMS_NOT_NULL);
        }
        return Result.okOrFailed(bidOpenFileSignService.completeSign(req, getJwtUser()));
    }

}
