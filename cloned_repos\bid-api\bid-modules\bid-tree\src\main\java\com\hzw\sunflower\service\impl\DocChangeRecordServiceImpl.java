package com.hzw.sunflower.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzw.sunflower.controller.request.DocChangeRecordREQ;
import com.hzw.sunflower.dao.DocChangeRecordMapper;
import com.hzw.sunflower.dto.DocChangeRecordDTO;
import com.hzw.sunflower.entity.DocChangeRecord;
import com.hzw.sunflower.entity.condition.DocChangeRecordCondition;
import com.hzw.sunflower.service.DocChangeRecordService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;


/**
 * TDocChangeRecordService接口实现
 *
 * <AUTHOR>
 * @version 1.0.0 2021-04-07
 */
@Service
public class DocChangeRecordServiceImpl extends ServiceImpl<DocChangeRecordMapper, DocChangeRecord> implements DocChangeRecordService {

    /**
     * 根据条件分页查询列表
     *
     * @param condition 查询条件
     * @return 分页数据
     */
    @Override
    public IPage<DocChangeRecord> findInfoByCondition(DocChangeRecordCondition condition) {
        IPage<DocChangeRecord> page = condition.buildPage();
        QueryWrapper<DocChangeRecord> queryWrapper = condition.buildQueryWrapper(DocChangeRecord.class);
        return this.page(page, queryWrapper);
    }

    /**
     * 根据主键ID查询
     *
     * @param id 主键ID
     * @return
     */
    @Override
    public DocChangeRecord getInfoById(Long id) {
        return this.getById(id);
    }

    /**
     * 新增
     *
     * @param docChangeRecord
     * @return 是否成功
     */
    @Override
    public Boolean addInfo(DocChangeRecord docChangeRecord) {
        return this.save(docChangeRecord);
    }

    /**
     * 修改
     *
     * @param docChangeRecord
     * @return 是否成功
     */
    @Override
    public Boolean updateInfo(DocChangeRecord docChangeRecord) {
        return this.updateById(docChangeRecord);
    }

    /**
     * 根据主键ID删除
     *
     * @param id 主键ID
     * @return 是否成功
     */
    @Override
    public Boolean deleteById(Long id) {
        return this.removeById(id);
    }

    /**
     * 根据主键ID列表批量删除
     *
     * @param idList 主键ID列表
     * @return 是否成功
     */
    @Override
    public Boolean deleteByIds(List<Long> idList) {
        return this.removeByIds(idList);
    }

    @Override
    public List<Long> getTimesByDocId(Long docId) {
        return this.baseMapper.getTimesByDocId(docId);
    }

    @Override
    public List<DocChangeRecordDTO> getDocRecord(DocChangeRecordREQ docChangeRecordREQ) {
        return this.baseMapper.getDocRecord(docChangeRecordREQ);
    }

    @Override
    public List<DocChangeRecordDTO> getListByDocId(Long docId) {
        return this.baseMapper.getListByDocId(docId);
    }

    @Override
    public List<DocChangeRecordDTO> getListByBidId(Long bidId) {
        return this.baseMapper.getListByBidId(bidId);
    }

    @Override
    public List<DocChangeRecordDTO> getBidRecord(DocChangeRecordREQ docChangeRecordREQ) {
        LambdaQueryWrapper<DocChangeRecord> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        //标段ID
        lambdaQueryWrapper.eq(DocChangeRecord::getSectionId, docChangeRecordREQ.getBidId());
        //标段提交次数
        lambdaQueryWrapper.eq(DocChangeRecord::getBidSubmitNumber, docChangeRecordREQ.getBidSubmitNumber());
        DocChangeRecord docChangeRecord = this.getBaseMapper().selectOne(lambdaQueryWrapper);
        if (null == docChangeRecord) {
            return new ArrayList<>();
        } else {
            docChangeRecordREQ.setDocId(docChangeRecord.getDocId());
            docChangeRecordREQ.setSubmitNumber(docChangeRecord.getSubmitNumber());
            return this.baseMapper.getDocRecord(docChangeRecordREQ);
        }
    }
}
