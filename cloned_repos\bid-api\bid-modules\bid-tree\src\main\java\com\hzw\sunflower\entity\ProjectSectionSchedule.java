package com.hzw.sunflower.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.hzw.sunflower.common.BaseBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


@ApiModel("开标一览表模板及规则")
@TableName("t_project_section_schedule")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProjectSectionSchedule extends BaseBean {

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "项目ID")
    private Long projectId;

    @ApiModelProperty(value = "标段id")
    private Long sectionId;

    @ApiModelProperty(value = "开标一览表模板oss文件id")
    private Long fileOssId;

    @ApiModelProperty(value ="生成规则 1.直接拼接开标汇总表 2.从x行解析拼接")
    private Integer generateRule;

    @ApiModelProperty(value ="解析起始行数")
    private Integer rowNum;

    @ApiModelProperty(value ="招标文件阶段：1：第一轮；2：第二轮")
    private Integer bidRound;

}
