<?xml-stylesheet type="text/css" href="icons.css" ?>
<svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
    <linearGradient
       id="linearGradient">
      <stop
         style="stop-color:#ffffff;stop-opacity:0.5"
         offset="0"
         />
      <stop
         style="stop-color:#ffffff;stop-opacity:0"
         offset="1"
         />
    </linearGradient>
  <path
	 class="icn icn--highlight-color"
     d="m 11.9,3.5 a 9.5,8 0 0 0 -9.4,8 9.5,8 0 0 0 9.5,8 9.5,8 0 0 0 9.5,-8 9.5,8 0 0 0 -9.5,-8 9.5,8 0 0 0 -0,0 z m 0,3.5 a 5,4.5 0 0 1 5,4.5 5,4.5 0 0 1 -5,4.5 5,4.5 0 0 1 -5,-4.5 5,4.5 0 0 1 5,-4.5 z"
	 fill="#117acc"
	 stroke="#0063b1"
	 stroke-linecap="round"
	 stroke-linejoin="round"
     />
  <path
     style="fill:url(#linearGradient)"
     d="m 3,12.5 c 0.3,3.1 4.3,6.3 8,6.3 L 11.9,16.5 c -3,0 -5.5,-2.2 -5.5,-5 z"
     />
  <path
     style="fill:url(#linearGradient)"
     d="M 12.9,4 11.9,6.5 c 3,0 5.5,2.2 5.5,5 l 3.6,-0.8 C 20.9,8.1 17.8,4.5 12.9,4 Z"
     />
</svg>
