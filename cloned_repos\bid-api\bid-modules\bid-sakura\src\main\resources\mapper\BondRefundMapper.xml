<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzw.sunflower.dao.BondRefundMapper">
    <select id="findRefundCount" resultType="java.lang.Long">
        select count(DISTINCT br.id)
        from t_bond_refund br
        left join t_bond_split bs
        on bs.section_id = br.section_id
        where bs.is_delete = 0 and br.is_delete = 0
        and bs.section_id = #{sectionId}
        and br.`status` = 3
    </select>

    <select id="bondSupplierList" resultType="com.hzw.sunflower.controller.response.BondSupplierInfoVo">
        SELECT
            pbs.id AS section_id,
            a.apply_id AS applyInfoId,
            a.company_id,
            a.company_name,
            a.download_flag,
            a.bond_remark,
            c.is_company_abroad,
            (case when sum(bs.amount) is null then 0 else sum(bs.amount) end ) as bond_money,
            pbs.agency_fee_obj,
            pbs.agency_cost_free,
            pbs.agency_fee_type AS feeType,
            pbs.bond_type,
            bar.id AS refundId,
            bar.agency_fee_type,
            bar.file_ids,
            (case when bar.refund_money is null then 0 else bar.refund_money end) as refund_money,
            bar.remark,
            bar.rates,
            bar.former_agency_fee,
            bar.refund_mode,
            bar.is_agency_fee,
            bar.refund_process_code,
            (case when bar.`status` is null then 0 else bar.`status` end) AS refundStatus,
            (case when br.`status` is null then 4 else br.`status` end) AS relationStatus,
            (case when t.`status` is null then 0 else t.`status` end) as applyRelationStatus,
            (case when bwn.`status` is null then 0 else bwn.`status` end) as noticeStatus,
            (case when bwp.ss_agency_fee is null then 0 else bwp.ss_agency_fee end) as agencyFee,
            GROUP_CONCAT( DISTINCT bw.id ) AS water_ids,
            GROUP_CONCAT( DISTINCT bs.id ) AS split_ids,
            GROUP_CONCAT( DISTINCT bbr.receipt_oss_file_id ) AS receiptFiles
        FROM
            t_apply_info a
                LEFT JOIN t_project_bid_section pbs ON a.sub_id = pbs.id
                LEFT JOIN ( SELECT * FROM t_bid_win_notice WHERE find_in_set ( #{req.sectionId}, section_id ) and `status` = 5  and bid_round = 2 AND is_delete = 0 ORDER BY order_num LIMIT 1 ) bwn ON find_in_set ( pbs.id, bwn.section_id )
                LEFT JOIN t_bid_win_people bwp ON bwn.id = bwp.winbid_id
                AND a.company_id = bwp.tenderer_id
                AND bwp.win_people_type = 1
                AND bwp.is_delete = 0
                LEFT JOIN t_bond_split bs ON bs.section_id = pbs.id
                AND bs.company_id = a.company_id
                AND bs.is_delete = 0
                LEFT JOIN t_bond_relation br ON br.company_id = bs.company_id
                AND br.water_id = bs.water_id
                AND br.section_id = bs.section_id
                AND br.is_delete = 0
                LEFT JOIN t_bond_water bw ON bw.id = br.water_id
                AND bw.is_delete = 0
                LEFT JOIN t_bond_refund bar ON bar.section_id = pbs.id
                AND bar.company_id = a.company_id
                AND bar.is_delete = 0
                LEFT JOIN t_bond_refund_details brd ON bar.id = brd.refund_id AND brd.is_delete = 0
                LEFT JOIN t_bond_pay_info bpi ON bpi.refund_detail_id = brd.id AND bpi.is_delete = 0
                LEFT JOIN t_bond_bank_receipt bbr ON LEFT(bbr.relation_num ,30) = bpi.req_no AND bbr.is_delete = 0
                LEFT JOIN t_company c ON c.id = a.company_id AND c.is_delete = 0
                LEFT JOIN ( SELECT bar.* FROM t_bond_apply_relation bar WHERE bar.id IN (SELECT MAX( tbar.id ) id FROM t_bond_apply_relation tbar WHERE tbar.section_id = #{req.sectionId} AND tbar.is_delete = 0 GROUP BY tbar.company_id) ) t ON t.company_id = a.company_id
        WHERE
            a.is_delete = 0
          AND pbs.is_delete = 0
          AND a.bid_round = 2
          and a.download_flag = 1
            AND pbs.id = #{req.sectionId}
        <if test="req.keyWords != null and !&quot;&quot;.equals(req.keyWords)">
            and a.company_name like concat('%',#{req.keyWords},'%')
        </if>
        <if test="req.relationStatusOne != null">
            <if test="req.relationStatusOne != 4 and req.relationStatusOne != 5">
                and br.`status` = #{req.relationStatusOne}
            </if>
            <if test="req.relationStatusOne == 4">
                and br.`status` is null
                and (t.`status` is null or t.`status` in (2,3))
            </if>
            <if test="req.relationStatusOne == 5">
                and br.`status` is null
                and t.`status` = 1
            </if>
        </if>
        <if test="req.refundStatus != null and req.refundStatus.size > 0">
            <if test="req.refundNo.equals(true)">
                and (bar.`status` in
                <foreach collection="req.refundStatus" item="refundStatus" open="(" separator="," close=")" index="">
                    #{refundStatus}
                </foreach>
                         or bar.`status` is null)
            </if>
            <if test="req.refundNo.equals(false)">
                and bar.`status` in
                <foreach collection="req.refundStatus" item="refundStatus" open="(" separator="," close=")" index="">
                    #{refundStatus}
                </foreach>
            </if>
        </if>
        GROUP BY
            pbs.id,
            a.apply_id,
            a.company_id,
            a.company_name,
            a.download_flag,
            a.bond_remark,
            c.is_company_abroad,
            pbs.agency_fee_obj,
            pbs.agency_cost_free,
            pbs.agency_fee_type,
            pbs.bond_type,
            bar.id,
            bar.agency_fee_type,
            bar.file_ids,
            bar.refund_money,
            bar.remark,
            bar.rates,
            bar.former_agency_fee,
            bar.refund_mode,
            bar.`status`,
            br.`status`,
            t.`status`,
            bwn.`status`,
            bwp.ss_agency_fee
    </select>

    <resultMap id="RefundListMap" type="com.hzw.sunflower.controller.response.RefundListVo" >
        <id column="refundId" property="refundId"  />
        <result column="projectName" property="projectName" />
        <result column="projectNum" property="projectNum" />
        <result column="supplierName" property="supplierName"  />
        <result column="company_name" property="companyName" />
        <result column="receive_bank" property="receiveBank" />
        <result column="agency_fee_type" property="agencyFeeType" />
        <result column="agency_fee" property="agencyFee"/>
        <result column="former_agency_fee" property="formerAgencyFee" />
        <result column="refund_money" property="refundMoney"  />
        <result column="company_id" property="companyId" />
        <result column="section_id" property="sectionId"  />
        <result column="refund_process_code" property="refundProcessCode"  />
        <result column="STATUS" property="status"  />
        <result column="submit_time" property="submitTime" />
        <result column="amount" property="amount" />
        <result column="department_name" property="departmentName"  />
        <result column="refund_time" property="refundTime"  />
        <result column="rates" property="rates" />
        <result column="abnormal_reson" property="abnormalReson"  />
        <result column="receiptFiles" property="receiptFiles"  />
        <result column="file_ids" property="fileIds" />
        <result column="refund_type" property="refundType"  />
        <result column="refund_files" property="refundFiles"  />
        <result column="bond_type" property="bondType"  />
        <result column="refund_mode" property="refundMode"  />
        <result column="agency_fee_obj" property="agencyFeeObj"  />
        <result column="feeType" property="feeType"  />
        <result column="package_number" property="packageNumber"  />
        <result column="is_agency_fee" property="isAgencyFee"  />
        <result column="remark" property="remark"  />
        <collection property="bondRefundDetails" column="refundId" select="getRefundDetails"/>
    </resultMap>

    <select id="getRefundDetails" resultType="com.hzw.sunflower.entity.BondRefundDetails">
        select * from t_bond_refund_details where refund_id = #{refundId} and is_delete = 0
    </select>

    <select id="bondRefundListNew" resultMap="RefundListMap">
        select distinct t.* from
        (
        SELECT DISTINCT
        p.purchase_name AS projectName,
        p.purchase_number AS projectNum,
        u.user_name,
        p.operation_flow,
        c.id companyId,
        IF(p.operation_flow = 3,GROUP_CONCAT( DISTINCT oc.company_name ),GROUP_CONCAT( DISTINCT c.company_name )) AS supplierName,
        GROUP_CONCAT( DISTINCT brd.refund_company_name ) AS company_name,
        GROUP_CONCAT( DISTINCT brd.from_open_bank ) AS receive_bank,
        bar.agency_fee_type,
        bar.agency_fee,
        bar.former_agency_fee,
        bar.refund_money,
        bar.id as refundId,
        bar.company_id,
        bar.section_id,
        bar.refund_process_code,
        bar.STATUS,
        bar.submit_time,
        bar.bond_money AS amount,
        d.department_name,
        bar.refund_time,
        bar.rates,
        bar.is_abnormal,
        bar.abnormal_time,
        bar.abnormal_reson,
        GROUP_CONCAT( DISTINCT bbr.receipt_oss_file_id ) AS receiptFiles,
        bar.file_ids,
        bar.refund_type,
        bar.refund_files,
        bar.bond_type,
        bar.refund_mode,
        bar.is_agency_fee,
        bar.remark,
        pbs.package_number,
        pbs.agency_fee_obj,
        pbs.agency_fee_type as feeType
        FROM
        t_bond_refund bar
        LEFT JOIN t_bond_refund_details brd ON bar.id = brd.refund_id
        AND brd.is_delete = 0
        LEFT JOIN t_bond_pay_info bpi ON bpi.refund_detail_id = brd.id
        AND brd.is_delete = 0
        LEFT JOIN t_bond_bank_receipt bbr ON LEFT(bbr.relation_num ,30) = bpi.req_no AND bbr.is_delete = 0
        AND bbr.is_delete = 0
        LEFT JOIN t_bond_split bs ON bar.section_id = bs.section_id
        AND bar.company_id = bs.company_id
        AND bs.is_delete = 0
        LEFT JOIN t_company c ON bar.company_id = c.id
        AND c.is_delete = 0
        LEFT JOIN t_offline_company oc ON bar.company_id = oc.id
        AND oc.is_delete = 0
        LEFT JOIN t_project_bid_section pbs ON bar.section_id = pbs.id AND pbs.is_delete = 0
        LEFT JOIN t_project p ON pbs.project_id = p.id AND p.is_delete = 0
        LEFT JOIN t_project_entrust_user peu ON p.id = peu.project_id AND peu.type = 1 AND peu.is_delete = 0
        LEFT JOIN t_user u ON peu.user_id = u.id AND u.is_delete = 0
        LEFT JOIN t_department d ON peu.department_id = d.id
        left join t_role  r on FIND_IN_SET(r.id,bar.approver_roles) and r.is_delete = 0
        WHERE
        <!-- pbs.is_delete = 0 -->
        bar.is_delete = 0
        <!-- AND p.is_delete = 0 -->
        <!-- AND peu.is_delete = 0 -->
        <!-- AND u.is_delete = 0 -->
        <!-- AND peu.type = 1 -->
        <!-- and bar.status != 8 -->
        <if test="condition.type != null and condition.type.equals(1)">
            and bar.`status` = 1
            <if test="condition.taskCodes != null and condition.taskCodes.size > 0 ">
                and  bar.refund_process_code in
                <foreach collection="condition.taskCodes" item="item" index="id" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </if>
        <if test="condition.type != null and condition.type.equals(2)">
            <if test="condition.roleId != null and condition.roleId.length > 0">
                and r.id in
                <foreach collection="condition.roleId" item="item" index="id" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </if>
        GROUP BY
                p.purchase_name,
                p.purchase_number,
                p.operation_flow,
                u.user_name,
                d.department_name,
                bar.agency_fee_type,
                bar.agency_fee,
                bar.former_agency_fee,
                bar.refund_money,
                bar.id,
                bar.company_id,
                bar.section_id,
                bar.refund_process_code,
                bar.STATUS,
                bar.submit_time,
                bar.bond_money,
                bar.refund_time,
                bar.rates,
                bar.is_abnormal,
                bar.abnormal_time,
                bar.abnormal_reson,
                bar.file_ids,
                bar.refund_type,
                bar.refund_files,
                bar.bond_type,
                bar.refund_mode,
                bar.is_agency_fee,
                bar.remark,
                pbs.package_number,
                pbs.agency_fee_obj,
                pbs.agency_fee_type
        ) t
        left join t_bid_win_people ai on ai.section_id = t.section_id and ai.win_people_type = 1 and (if(ai.tenderer_id is null,ai.tenderer_name = t.company_name,t.companyId = ai.tenderer_id) )
        and ai.is_delete = 0 and ai.is_win = 1
        where 1 = 1
        <if test="condition.refundId != null and condition.refundId != ''">
            and t.refundId = #{condition.refundId}
        </if>
        <if test="condition.keywords != null and condition.keywords != ''">
            and (
            t.projectName like concat('%',#{condition.keywords},'%')
            or
            t.projectNum like concat('%',#{condition.keywords},'%')
            or
            t.company_name like concat('%',#{condition.keywords},'%')
            )
        </if>
        <if test="condition.purchaseNumber != null and condition.purchaseNumber != ''">
            and t.projectNum like concat('%',#{condition.purchaseNumber},'%')
        </if>
        <if test="condition.companyName != null and condition.companyName != ''">
            and t.company_name like concat('%',#{condition.companyName},'%')
        </if>
        <if test="condition.status != null and condition.status != 0">
            and t.status = #{condition.status}
        </if>
        <if test="condition.bankName != null and condition.bankName != ''">
            and t.receive_bank like concat('%',#{condition.bankName},'%')
        </if>
        <if test="condition.startApplyTime != null">
            and DATE_FORMAT(t.submit_time,'%Y-%m-%d') &gt;= DATE_FORMAT(#{condition.startApplyTime},'%Y-%m-%d')
        </if>
        <if test="condition.endApplyTime != null">
            and DATE_FORMAT(t.submit_time,'%Y-%m-%d') &lt;= DATE_FORMAT(#{condition.endApplyTime},'%Y-%m-%d')
        </if>
        <if test="condition.refundStartTime != null and condition.refundStartTime != ''">
            and DATE_FORMAT(t.refund_time,'%Y-%m-%d') &gt;= DATE_FORMAT(#{condition.refundStartTime},'%Y-%m-%d')
        </if>
        <if test="condition.refundEndTime != null and condition.refundEndTime != ''">
            and DATE_FORMAT(t.refund_time,'%Y-%m-%d') &lt;= DATE_FORMAT(#{condition.refundEndTime},'%Y-%m-%d')
        </if>
        <if test="condition.hasReceipt != null and condition.hasReceipt.equals(1)">
            and t.receiptFiles is not null
        </if>
        <if test="condition.hasReceipt != null and condition.hasReceipt.equals(2)">
            and t.receiptFiles is null
        </if>
        <if test="condition.bidStatus != null and condition.bidStatus.equals(1)">
<!--            and t.company_id in-->
<!--            ( SELECT-->
<!--            a.company_id-->
<!--            FROM-->
<!--            t_project_bid_section pbs-->
<!--            LEFT JOIN t_apply_info a ON a.project_id = pbs.project_id-->
<!--            AND pbs.bid_round = a.bid_round-->
<!--            LEFT JOIN t_bid_win_notice bw ON bw.project_id = pbs.project_id-->
<!--            AND bw.bid_round = a.bid_round-->
<!--            LEFT JOIN t_bid_win_people p ON p.winbid_id = bw.id-->
<!--            AND a.sub_id = p.section_id-->
<!--            AND ( a.company_id = p.tenderer_id OR p.tenderer_id IS NULL )-->
<!--            WHERE-->
<!--            pbs.id = t.section_id-->
<!--            AND find_in_set( a.sub_id, pbs.id )-->
<!--            AND find_in_set( a.sub_id, bw.section_id )-->
<!--            AND pbs.is_delete = 0-->
<!--            AND bw.is_delete = 0-->
<!--            AND a.is_delete = 0-->
<!--            AND bw.`status` IN ( 5, 8, 9, 10, 11, 12 )-->
<!--            AND a.download_flag = 1-->
<!--            AND p.win_people_type = 1-->
<!--            AND p.IS_DELETE = 0-->
<!--            AND pbs.bid_round = 2-->
<!--            and p.winbid_type != 3-->
<!--            and p.is_win = 1)-->
            and ai.id is not null
        </if>
        <if test="condition.bidStatus != null and condition.bidStatus.equals(2)">
<!--            and t.company_id not in-->
<!--            ( SELECT-->
<!--            a.company_id-->
<!--            FROM-->
<!--            t_project_bid_section pbs-->
<!--            LEFT JOIN t_apply_info a ON a.project_id = pbs.project_id-->
<!--            AND pbs.bid_round = a.bid_round-->
<!--            LEFT JOIN t_bid_win_notice bw ON bw.project_id = pbs.project_id-->
<!--            AND bw.bid_round = a.bid_round-->
<!--            LEFT JOIN t_bid_win_people p ON p.winbid_id = bw.id-->
<!--            AND a.sub_id = p.section_id-->
<!--            AND ( a.company_id = p.tenderer_id OR p.tenderer_id IS NULL )-->
<!--            WHERE-->
<!--            pbs.id = t.section_id-->
<!--            AND find_in_set( a.sub_id, pbs.id )-->
<!--            AND find_in_set( a.sub_id, bw.section_id )-->
<!--            AND pbs.is_delete = 0-->
<!--            AND bw.is_delete = 0-->
<!--            AND a.is_delete = 0-->
<!--            AND bw.`status` IN ( 5, 8, 9, 10, 11, 12 )-->
<!--            AND a.download_flag = 1-->
<!--            AND p.win_people_type = 1-->
<!--            AND p.IS_DELETE = 0-->
<!--            AND pbs.bid_round = 2-->
<!--            and p.winbid_type != 3-->
<!--            and p.is_win = 1)-->
            and ai.id is null
        </if>
        <if test="condition.businessType != null and condition.businessType.equals(1)">
            AND t.is_abnormal = 1
            order by t.abnormal_time
        </if>
        <if test="condition.businessType != null and condition.businessType.equals(2)">
            AND t.is_abnormal != 1
            order by t.submit_time desc
        </if>
        <if test="condition.businessType != null and condition.businessType.equals(3)">
            and t.status = 3
            AND t.is_abnormal != 1
            order by t.refund_time
        </if>
    </select>




    <select id="bondRefundList" resultType="com.hzw.sunflower.controller.response.RefundListVo">
        select
        t.*
        from
        (
        SELECT
        DISTINCT
        p.purchase_name AS projectName,
        p.purchase_number as projectNum,
        u.user_name,
        GROUP_CONCAT(DISTINCT c.company_name) as supplierName,
        GROUP_CONCAT(DISTINCT brd.refund_company_name) as company_name,
        GROUP_CONCAT(DISTINCT brd.from_open_bank) as receive_bank,
        bar.agency_fee_type,
        bar.agency_fee,
        bar.former_agency_fee,
        bar.refund_money,
        bar.id as refundId,
        bar.company_id,
        bar.section_id,
        bar.refund_process_code,
        bar.status,
        bar.submit_time,
        bar.bond_money as amount,
        GROUP_CONCAT(DISTINCT d.department_name) as department_name,
        bar.refund_time,
        bar.rates,
        bar.is_abnormal,
        bar.abnormal_time,
        bar.abnormal_reson,
        GROUP_CONCAT(DISTINCT bbr.receipt_oss_file_id) as receiptFiles,
        bar.file_ids,
        bar.refund_type,
        bar.refund_files,
        bar.bond_type,
        bar.refund_mode
        FROM
        t_bond_refund bar
        left join t_bond_refund_details brd
        on bar.id = brd.refund_id and brd.is_delete = 0
        left join t_bond_pay_info bpi
        on bpi.refund_detail_id = brd.id and brd.is_delete = 0
        left join t_bond_bank_receipt bbr
        on bbr.relation_num = bpi.req_no and bbr.is_delete = 0
        left join t_bond_split bs
        on bar.section_id = bs.section_id and bar.company_id = bs.company_id and bs.is_delete = 0
        left join t_company c on bar.company_id = c.id and c.is_delete = 0
        left join t_project_bid_section pbs on bar.section_id = pbs.id
        LEFT JOIN t_project p ON pbs.project_id = p.id
        LEFT JOIN t_project_entrust_user peu ON p.id = peu.project_id
        LEFT JOIN t_user u ON peu.user_id = u.id
        LEFT JOIN r_user_department  ud on u.id = ud.user_id and peu.department_id = ud.department_id
        left join t_department d on ud.department_id = d.id
        left join t_process_role cpr
        on bar.id = cpr.business_id and cpr.is_delete = 0
        WHERE
        pbs.is_delete = 0
        and ud.is_delete = 0
        AND bar.is_delete = 0
        AND p.is_delete = 0
        AND peu.is_delete = 0
        AND u.is_delete = 0
        AND peu.type = 1
        and bar.refund_type = 1
        <if test="condition.type != null and condition.type.equals(2)">
            and cpr.business_code = 'BOND_APPLY_REFUND'
            and bar.status != 2
            <if test="condition.isLastleader == true">
                and bar.status != 1
            </if>
            <if test="condition.roleId != null and condition.roleId.length > 0">
                and cpr.role_id in
                <foreach collection="condition.roleId" item="item" index="id" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </if>
        group by
        p.purchase_name,
        p.purchase_number,
        u.user_name,
        bar.agency_fee_type,
        bar.agency_fee,
        bar.former_agency_fee,
        bar.refund_money,
        bar.id,
        bar.company_id,
        bar.section_id,
        bar.refund_process_code,
        bar.status,
        bar.submit_time,
        bar.bond_money,
        bar.refund_time,
        bar.rates,
        bar.is_abnormal,
        bar.abnormal_time,
        bar.abnormal_reson,
        bar.file_ids,
        bar.refund_type,
        bar.refund_files,
        bar.bond_type,
        bar.refund_mode
        union
        select
        '-' AS projectName,
        '-' as projectNum,
        '-' as user_name,
        GROUP_CONCAT(DISTINCT c.company_name) as supplierName,
        GROUP_CONCAT(DISTINCT brd.refund_company_name) as company_name,
        GROUP_CONCAT(DISTINCT brd.from_open_bank) as receive_bank,
        bar.agency_fee_type,
        bar.agency_fee,
        bar.former_agency_fee,
        bar.refund_money,
        bar.id as refundId,
        bar.company_id,
        bar.section_id,
        bar.refund_process_code,
        bar.status,
        bar.submit_time,
        bar.bond_money as amount,
        '-' as department_name,
        bar.refund_time,
        bar.rates,
        bar.is_abnormal,
        bar.abnormal_time,
        bar.abnormal_reson,
        GROUP_CONCAT(DISTINCT bbr.receipt_oss_file_id) as receiptFiles,
        bar.file_ids,
        bar.refund_type,
        bar.refund_files,
        bar.bond_type,
        bar.refund_mode
        FROM
        t_bond_refund bar
        left join t_bond_refund_details brd
        on bar.id = brd.refund_id and brd.is_delete = 0
        left join t_bond_pay_info bpi
        on bpi.refund_detail_id = brd.id and brd.is_delete = 0
        left join t_bond_bank_receipt bbr
        on bbr.relation_num = bpi.req_no and bbr.is_delete = 0
        left join t_company c on bar.company_id = c.id and c.is_delete = 0
        left join t_process_role cpr
        on bar.id = cpr.business_id and cpr.is_delete = 0
        where bar.is_delete = 0
        and bar.refund_type != 1
        <if test="condition.type != null and condition.type.equals(2)">
            and cpr.business_code = 'BOND_APPLY_REFUND'
            and bar.status != 2
            <if test="condition.isLastleader == true">
                and bar.status != 1
            </if>
            <if test="condition.roleId != null and condition.roleId.length > 0">
                and cpr.role_id in
                <foreach collection="condition.roleId" item="item" index="id" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </if>
        group by
        bar.agency_fee_type,
        bar.agency_fee,
        bar.former_agency_fee,
        bar.refund_money,
        bar.id,
        bar.company_id,
        bar.section_id,
        bar.refund_process_code,
        bar.status,
        bar.submit_time,
        bar.bond_money,
        bar.refund_time,
        bar.rates,
        bar.is_abnormal,
        bar.abnormal_time,
        bar.abnormal_reson,
        bar.file_ids,
        bar.refund_type,
        bar.refund_files,
        bar.bond_type,
        bar.refund_mode
        ) t
        where 1 = 1
        <if test="condition.refundId != null and condition.refundId != ''">
                  and t.refundId = #{condition.refundId}
        </if>
        <if test="condition.keywords != null and condition.keywords != ''">
            and (
            t.projectName like concat('%',#{condition.keywords},'%')
            or
            t.projectNum like concat('%',#{condition.keywords},'%')
            or
            t.company_name like concat('%',#{condition.keywords},'%')
            )
        </if>
        <if test="condition.status != null and condition.status != 0">
            and t.status = #{condition.status}
        </if>
        <if test="condition.bankName != null and condition.bankName != ''">
            and t.receive_bank like concat('%',#{condition.bankName},'%')
        </if>
        <if test="condition.startApplyTime != null">
            and DATE_FORMAT(t.submit_time,'%Y-%m-%d') &gt;= DATE_FORMAT(#{condition.startApplyTime},'%Y-%m-%d')
        </if>
        <if test="condition.endApplyTime != null">
            and DATE_FORMAT(t.submit_time,'%Y-%m-%d') &lt;= DATE_FORMAT(#{condition.endApplyTime},'%Y-%m-%d')
        </if>
        <if test="condition.refundTime != null">
            and DATE_FORMAT(t.refund_time,'%Y-%m-%d') = DATE_FORMAT(#{condition.refundTime},'%Y-%m-%d')
        </if>
        <if test="condition.taskCodes != null and condition.taskCodes.size > 0 ">
            and t.status = 1
            and  t.refund_process_code in
            <foreach collection="condition.taskCodes" item="item" index="id" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="condition.hasReceipt != null and condition.hasReceipt.equals(1)">
            and t.receiptFiles is not null
        </if>
        <if test="condition.hasReceipt != null and condition.hasReceipt.equals(2)">
            and t.receiptFiles is null
        </if>

        <if test="condition.bidStatus != null and condition.bidStatus.equals(1)">
            and t.company_id in
            ( SELECT
            a.company_id
            FROM
            t_project_bid_section pbs
            LEFT JOIN t_apply_info a ON a.project_id = pbs.project_id
            AND pbs.bid_round = a.bid_round
            LEFT JOIN t_bid_win_notice bw ON bw.project_id = pbs.project_id
            AND bw.bid_round = a.bid_round
            LEFT JOIN t_bid_win_people p ON p.winbid_id = bw.id
            AND a.sub_id = p.section_id
            AND ( a.company_id = p.tenderer_id OR p.tenderer_id IS NULL )
            WHERE
            pbs.id = t.section_id
            AND find_in_set( a.sub_id, pbs.id )
            AND find_in_set( a.sub_id, bw.section_id )
            AND pbs.is_delete = 0
            AND bw.is_delete = 0
            AND a.is_delete = 0
            AND bw.`status` IN ( 5, 8, 9, 10, 11, 12 )
            AND a.download_flag = 1
            AND p.win_people_type = 1
            AND p.IS_DELETE = 0
            AND pbs.bid_round = 2
            and p.winbid_type != 3
            and p.is_win = 1)
        </if>

        <if test="condition.bidStatus != null and condition.bidStatus.equals(2)">
            and t.company_id not in
            ( SELECT
            a.company_id
            FROM
            t_project_bid_section pbs
            LEFT JOIN t_apply_info a ON a.project_id = pbs.project_id
            AND pbs.bid_round = a.bid_round
            LEFT JOIN t_bid_win_notice bw ON bw.project_id = pbs.project_id
            AND bw.bid_round = a.bid_round
            LEFT JOIN t_bid_win_people p ON p.winbid_id = bw.id
            AND a.sub_id = p.section_id
            AND ( a.company_id = p.tenderer_id OR p.tenderer_id IS NULL )
            WHERE
            pbs.id = t.section_id
            AND find_in_set( a.sub_id, pbs.id )
            AND find_in_set( a.sub_id, bw.section_id )
            AND pbs.is_delete = 0
            AND bw.is_delete = 0
            AND a.is_delete = 0
            AND bw.`status` IN ( 5, 8, 9, 10, 11, 12 )
            AND a.download_flag = 1
            AND p.win_people_type = 1
            AND p.IS_DELETE = 0
            AND pbs.bid_round = 2
            and p.winbid_type != 3
            and p.is_win = 1)
        </if>

        <if test="condition.businessType != null and condition.businessType.equals(1)">
            AND t.is_abnormal = 1
            order by t.abnormal_time
        </if>
        <if test="condition.businessType != null and condition.businessType.equals(2)">
            AND t.is_abnormal != 1
            order by t.submit_time desc
        </if>
        <if test="condition.businessType != null and condition.businessType.equals(3)">
            and t.status = 3
            AND t.is_abnormal != 1
            order by t.refund_time
        </if>

    </select>


    <select id="bondRefundListForAll" resultType="com.hzw.sunflower.controller.response.RefundListVo">
        SELECT
        t.*
        FROM
        (
        SELECT
        a.*
        FROM
        (
        SELECT DISTINCT
        p.purchase_name AS projectName,
        p.purchase_number AS projectNum,
        u.user_name,
        GROUP_CONCAT(DISTINCT c.company_name) as supplierName,
        GROUP_CONCAT( DISTINCT brd.refund_company_name ) AS company_name,
        GROUP_CONCAT( DISTINCT brd.from_open_bank ) AS receive_bank,
        bar.agency_fee_type,
        bar.agency_fee,
        bar.former_agency_fee,
        bar.refund_money,
        bar.id AS refundId,
        bar.company_id,
        bar.section_id,
        bar.refund_process_code,
        bar.STATUS,
        bar.submit_time,
        bar.bond_money AS amount,
        GROUP_CONCAT( DISTINCT d.department_name) as department_name,
        bar.refund_time,
        bar.rates,
        bar.is_abnormal,
        bar.abnormal_time,
        bar.abnormal_reson,
        GROUP_CONCAT( DISTINCT bbr.receipt_oss_file_id ) AS receiptFiles,
        bar.file_ids,
        bar.refund_type,
        bar.refund_files,
        bar.bond_type,
        bar.refund_mode
        FROM
        t_bond_refund bar
        LEFT JOIN t_bond_refund_details brd ON bar.id = brd.refund_id
        AND brd.is_delete = 0
        LEFT JOIN t_bond_pay_info bpi ON bpi.refund_detail_id = brd.id
        AND brd.is_delete = 0
        LEFT JOIN t_bond_bank_receipt bbr ON bbr.relation_num = bpi.req_no
        AND bbr.is_delete = 0
        LEFT JOIN t_bond_split bs ON bar.section_id = bs.section_id
        AND bar.company_id = bs.company_id and bs.is_delete = 0
        left join t_company c on bar.company_id = c.id and c.is_delete = 0
        left join t_project_bid_section pbs on bar.section_id = pbs.id
        LEFT JOIN t_project p ON pbs.project_id = p.id
        LEFT JOIN t_project_entrust_user peu ON p.id = peu.project_id
        LEFT JOIN t_user u ON peu.user_id = u.id
        LEFT JOIN r_user_department  ud ON u.id = ud.user_id and peu.department_id = ud.department_id
        LEFT JOIN t_department d ON ud.department_id = d.id
        WHERE
        pbs.is_delete = 0
        AND bar.is_delete = 0
        AND p.is_delete = 0
        AND peu.is_delete = 0
        AND u.is_delete = 0
        AND peu.type = 1
        AND bar.refund_type = 1
        AND bar.STATUS = 1
        <if test="condition.taskCodes != null and condition.taskCodes.size > 0 ">
            and  bar.refund_process_code in
            <foreach collection="condition.taskCodes" item="item" index="id" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        GROUP BY
        p.purchase_name,
        p.purchase_number,
        u.user_name,
        bar.agency_fee_type,
        bar.agency_fee,
        bar.former_agency_fee,
        bar.refund_money,
        bar.id,
        bar.company_id,
        bar.section_id,
        bar.refund_process_code,
        bar.STATUS,
        bar.submit_time,
        bar.bond_money,
        bar.refund_time,
        bar.rates,
        bar.is_abnormal,
        bar.abnormal_time,
        bar.abnormal_reson,
        bar.file_ids,
        bar.refund_type,
        bar.refund_files,
        bar.bond_type,
        bar.refund_mode
        UNION
        SELECT
        '-' AS projectName,
        '-' AS projectNum,
        '-' AS user_name,
        GROUP_CONCAT(DISTINCT c.company_name) as supplierName,
        GROUP_CONCAT( DISTINCT brd.refund_company_name ) AS company_name,
        GROUP_CONCAT( DISTINCT brd.from_open_bank ) AS receive_bank,
        bar.agency_fee_type,
        bar.agency_fee,
        bar.former_agency_fee,
        bar.refund_money,
        bar.id AS refundId,
        bar.company_id,
        bar.section_id,
        bar.refund_process_code,
        bar.STATUS,
        bar.submit_time,
        bar.bond_money AS amount,
        '-' AS department_name,
        bar.refund_time,
        bar.rates,
        bar.is_abnormal,
        bar.abnormal_time,
        bar.abnormal_reson,
        GROUP_CONCAT( DISTINCT bbr.receipt_oss_file_id ) AS receiptFiles,
        bar.file_ids,
        bar.refund_type,
        bar.refund_files,
        bar.bond_type,
        bar.refund_mode
        FROM
        t_bond_refund bar
        LEFT JOIN t_bond_refund_details brd ON bar.id = brd.refund_id
        AND brd.is_delete = 0
        LEFT JOIN t_bond_pay_info bpi ON bpi.refund_detail_id = brd.id
        AND brd.is_delete = 0
        LEFT JOIN t_bond_bank_receipt bbr ON bbr.relation_num = bpi.req_no
        AND bbr.is_delete = 0
        left join t_company c on bar.company_id = c.id and c.is_delete = 0
        WHERE
        bar.is_delete = 0
        AND bar.refund_type != 1
        AND bar.STATUS = 1
        <if test="condition.taskCodes != null and condition.taskCodes.size > 0 ">
            and  bar.refund_process_code in
            <foreach collection="condition.taskCodes" item="item" index="id" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        GROUP BY
        bar.agency_fee_type,
        bar.agency_fee,
        bar.former_agency_fee,
        bar.refund_money,
        bar.id,
        bar.company_id,
        bar.section_id,
        bar.refund_process_code,
        bar.STATUS,
        bar.submit_time,
        bar.bond_money,
        bar.refund_time,
        bar.rates,
        bar.is_abnormal,
        bar.abnormal_time,
        bar.abnormal_reson,
        bar.file_ids,
        bar.refund_type,
        bar.refund_files,
        bar.bond_type,
        bar.refund_mode
        ) a UNION
        SELECT
        b.*
        FROM
        (
        SELECT DISTINCT
        p.purchase_name AS projectName,
        p.purchase_number AS projectNum,
        u.user_name,
        GROUP_CONCAT(DISTINCT c.company_name) as supplierName,
        GROUP_CONCAT( DISTINCT brd.refund_company_name ) AS company_name,
        GROUP_CONCAT( DISTINCT brd.from_open_bank ) AS receive_bank,
        bar.agency_fee_type,
        bar.agency_fee,
        bar.former_agency_fee,
        bar.refund_money,
        bar.id AS refundId,
        bar.company_id,
        bar.section_id,
        bar.refund_process_code,
        bar.STATUS,
        bar.submit_time,
        bar.bond_money AS amount,
        GROUP_CONCAT(DISTINCT d.department_name) as department_name,
        bar.refund_time,
        bar.rates,
        bar.is_abnormal,
        bar.abnormal_time,
        bar.abnormal_reson,
        GROUP_CONCAT( DISTINCT bbr.receipt_oss_file_id ) AS receiptFiles,
        bar.file_ids,
        bar.refund_type,
        bar.refund_files,
        bar.bond_type,
        bar.refund_mode
        FROM
        t_bond_refund bar
        LEFT JOIN t_bond_refund_details brd ON bar.id = brd.refund_id
        AND brd.is_delete = 0
        LEFT JOIN t_bond_pay_info bpi ON bpi.refund_detail_id = brd.id
        AND brd.is_delete = 0
        LEFT JOIN t_bond_bank_receipt bbr ON bbr.relation_num = bpi.req_no
        AND bbr.is_delete = 0
        LEFT JOIN t_bond_split bs ON bar.section_id = bs.section_id
        AND bar.company_id = bs.company_id and bs.is_delete = 0
        left join t_company c on bar.company_id = c.id and c.is_delete = 0
        left join t_project_bid_section pbs on bar.section_id = pbs.id
        LEFT JOIN t_project p ON pbs.project_id = p.id
        LEFT JOIN t_project_entrust_user peu ON p.id = peu.project_id
        LEFT JOIN t_user u ON peu.user_id = u.id
        LEFT JOIN r_user_department  ud ON u.id = ud.user_id and peu.department_id = ud.department_id
        LEFT JOIN t_department d ON ud.department_id = d.id
        LEFT JOIN t_process_role cpr ON bar.id = cpr.business_id
        AND cpr.is_delete = 0
        WHERE
            pbs.is_delete = 0
        AND bar.is_delete = 0
        AND p.is_delete = 0
        AND peu.is_delete = 0
        AND u.is_delete = 0
        AND peu.type = 1
        AND bar.refund_type = 1
        <if test="condition.roleId != null and condition.roleId.length > 0 ">
            and cpr.role_id in
            <foreach collection="condition.roleId" item="item" index="id" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        and cpr.business_code = 'BOND_APPLY_REFUND'
        and bar.status != 2
        <if test="condition.isLastleader == true">
            and bar.status != 1
        </if>
        GROUP BY
        p.purchase_name,
        p.purchase_number,
        u.user_name,
        bar.agency_fee_type,
        bar.agency_fee,
        bar.former_agency_fee,
        bar.refund_money,
        bar.id,
        bar.company_id,
        bar.section_id,
        bar.refund_process_code,
        bar.STATUS,
        bar.submit_time,
        bar.bond_money,
        bar.refund_time,
        bar.rates,
        bar.is_abnormal,
        bar.abnormal_time,
        bar.abnormal_reson,
        bar.file_ids,
        bar.refund_type,
        bar.refund_files,
        bar.bond_type,
        bar.refund_mode
        UNION
        SELECT
        '-' AS projectName,
        '-' AS projectNum,
        '-' AS user_name,
        GROUP_CONCAT(DISTINCT c.company_name) as supplierName,
        GROUP_CONCAT( DISTINCT brd.refund_company_name ) AS company_name,
        GROUP_CONCAT( DISTINCT brd.from_open_bank ) AS receive_bank,
        bar.agency_fee_type,
        bar.agency_fee,
        bar.former_agency_fee,
        bar.refund_money,
        bar.id AS refundId,
        bar.company_id,
        bar.section_id,
        bar.refund_process_code,
        bar.STATUS,
        bar.submit_time,
        bar.bond_money AS amount,
        '-' AS department_name,
        bar.refund_time,
        bar.rates,
        bar.is_abnormal,
        bar.abnormal_time,
        bar.abnormal_reson,
        GROUP_CONCAT( DISTINCT bbr.receipt_oss_file_id ) AS receiptFiles,
        bar.file_ids,
        bar.refund_type,
        bar.refund_files,
        bar.bond_type,
        bar.refund_mode
        FROM
        t_bond_refund bar
        LEFT JOIN t_bond_refund_details brd ON bar.id = brd.refund_id
        AND brd.is_delete = 0
        LEFT JOIN t_bond_pay_info bpi ON bpi.refund_detail_id = brd.id
        AND brd.is_delete = 0
        LEFT JOIN t_bond_bank_receipt bbr ON bbr.relation_num = bpi.req_no
        AND bbr.is_delete = 0
        left join t_company c on bar.company_id = c.id and c.is_delete = 0
        LEFT JOIN t_process_role cpr ON bar.id = cpr.business_id
        AND cpr.is_delete = 0
        WHERE
        bar.is_delete = 0
        AND bar.refund_type != 1
        AND bar.refund_process_code IS NOT NULL
        <if test="condition.roleId != null and condition.roleId.length > 0">
            and cpr.role_id in
            <foreach collection="condition.roleId" item="item" index="id" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        and cpr.business_code = 'BOND_APPLY_REFUND'
        and bar.status != 2
        <if test="condition.isLastleader == true">
            and bar.status != 1
        </if>
        GROUP BY
        bar.agency_fee_type,
        bar.agency_fee,
        bar.former_agency_fee,
        bar.refund_money,
        bar.id,
        bar.company_id,
        bar.section_id,
        bar.refund_process_code,
        bar.STATUS,
        bar.submit_time,
        bar.bond_money,
        bar.refund_time,
        bar.rates,
        bar.is_abnormal,
        bar.abnormal_time,
        bar.abnormal_reson,
        bar.file_ids,
        bar.refund_type,
        bar.refund_files,
        bar.bond_type,
        bar.refund_mode
        ) b
        ) t
        WHERE 1 = 1
        <if test="condition.refundId != null and condition.refundId != ''">
            and t.refundId = #{condition.refundId}
        </if>
        <if test="condition.keywords != null and condition.keywords != ''">
            and (
            t.projectName like concat('%',#{condition.keywords},'%')
            or
            t.projectNum like concat('%',#{condition.keywords},'%')
            or
            t.company_name like concat('%',#{condition.keywords},'%')
            )
        </if>
        <if test="condition.status != null and condition.status != 0">
            and t.status = #{condition.status}
        </if>
        <if test="condition.bankName != null and condition.bankName != ''">
            and t.receive_bank like concat('%',#{condition.bankName},'%')
        </if>
        <if test="condition.startApplyTime != null">
            and DATE_FORMAT(t.submit_time,'%Y-%m-%d') &gt;= #{condition.startApplyTime}
        </if>
        <if test="condition.endApplyTime != null">
            and DATE_FORMAT(t.submit_time,'%Y-%m-%d') &lt;= #{condition.endApplyTime}
        </if>
        <if test="condition.refundTime != null">
            and DATE_FORMAT(t.refund_time,'%Y-%m-%d') = DATE_FORMAT(#{condition.refundTime},'%Y-%m-%d')
        </if>
        <if test="condition.hasReceipt != null and condition.hasReceipt.equals(1)">
            and t.receiptFiles is not null
        </if>
        <if test="condition.hasReceipt != null and condition.hasReceipt.equals(2)">
            and t.receiptFiles is null
        </if>
        AND t.is_abnormal != 1
        order by t.submit_time
    </select>






    <select id="printBondRefundApply" resultType="com.hzw.sunflower.controller.response.BondSupplierVO">
        SELECT
            DISTINCT
            p.id as projectId,
            p.purchase_name as projectName,
            p.purchase_number as projectNum,
            bs.section_id,
            u.user_name,
            bar.company_id as supplierId,
            brd.refund_company_name as companyName
        FROM
            t_bond_refund bar
                left join t_bond_refund_details brd
                on bar.id = brd.refund_id and brd.is_delete = 0
                left join t_bond_split bs
                          on bar.section_id = bs.section_id and bs.is_delete = 0
                left join t_project_bid_section pbs on bar.section_id = pbs.id
                LEFT JOIN t_project p ON pbs.project_id = p.id
                LEFT JOIN t_project_entrust_user peu ON p.id = peu.project_id
                LEFT JOIN r_user_department  ud ON peu.user_id = ud.user_id and peu.department_id = ud.department_id
                LEFT JOIN t_department d ON ud.department_id = d.id
                LEFT JOIN t_user u ON peu.user_id = u.id
                left join t_process_role cpr
                          on bar.id = cpr.business_id and cpr.is_delete = 0
        WHERE
             pbs.is_delete = 0
          AND bar.is_delete = 0
          AND p.is_delete = 0
          AND peu.is_delete = 0
          AND u.is_delete = 0
          and bar.`status` = 3
          AND peu.type = 1
          and bar.refund_type != 3
          and cpr.business_code = 'BOND_APPLY_REFUND'
          <if test="condition.roleId != null and condition.roleId.length > 0">
            and cpr.role_id in
            <foreach collection="condition.roleId" item="item" index="id" open="(" close=")" separator=",">
                #{item}
            </foreach>
          </if>
          and bs.section_id = #{condition.sectionId}
    </select>



    <select id="printBondRefundApply2" resultType="com.hzw.sunflower.controller.response.BondSupplierVO">
        SELECT
            DISTINCT
            p.id as projectId,
            p.purchase_name as projectName,
            p.purchase_number as projectNum,
            bs.section_id,
            u.user_name,
            bar.company_id as supplierId,
            brd.refund_company_name as companyName,
            bar.submit_time
        FROM
            t_bond_refund bar
                left join t_bond_refund_details brd
                on bar.id = brd.refund_id and brd.is_delete = 0
                left join t_bond_split bs
                          on bar.section_id = bs.section_id and bs.is_delete = 0
                left join t_project_bid_section pbs on bar.section_id = pbs.id
                LEFT JOIN t_project p ON pbs.project_id = p.id
                LEFT JOIN t_project_entrust_user peu ON p.id = peu.project_id
                 LEFT JOIN r_user_department  ud ON peu.user_id = ud.user_id and peu.department_id = ud.department_id
                LEFT JOIN t_department d ON ud.department_id = d.id
                LEFT JOIN t_user u ON peu.user_id = u.id
        WHERE
             pbs.is_delete = 0
          AND bar.is_delete = 0
          AND p.is_delete = 0
          AND peu.is_delete = 0
          AND u.is_delete = 0
          AND peu.type = 1
          and bar.refund_type != 3
          and bs.section_id = #{sectionId}
          and bar.company_id = #{companyId}
    </select>

    <!-- 查看流水是否已有退还流程 -->
    <select id="selectListInReturn" resultType="com.hzw.sunflower.entity.BondRefund">
        SELECT
            r.*
        FROM
            t_bond_refund r
                LEFT JOIN t_bond_split s ON r.company_id = s.company_id
                AND r.section_id = s.section_id
                AND s.is_delete = 0
        WHERE
            r.is_delete = 0
          AND (
                r.water_id IN
                <foreach collection="list" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR s.water_id IN
                <foreach collection="list" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
              )
    </select>

    <select id="bankRateList" resultType="com.hzw.sunflower.controller.response.BankRateListVo">
        select
            t1.bank_type as bankType,
            t1.bank_name as bankName,
            DATE_FORMAT(t2.before_date,'%Y-%m-%d') as beforeDate,
            DATE_FORMAT(t2.effect_date,'%Y-%m-%d') as effectDate,
            t2.rate as rate
        from t_dict_bank t1
        left join (select
                          bank_type,effect_date,rate,before_date
                   from t_bond_rate where (effect_date,bank_type) in (select
                                                                        max(effect_date) as effect_date,
                                                                        bank_type
                                                                      from t_bond_rate group by bank_type)) t2
        on t1.bank_type = t2.bank_type
        where 1 = 1
        <if test="condition.bankName != null and condition.bankName != '' ">
            and t1.bank_name like concat('%',#{condition.bankName},'%')
        </if>
    </select>


    <select id="getAgencyInfo2Ncc" resultType="com.hzw.sunflower.controller.response.AgencyInfo2NccVo">
        SELECT
            *
        FROM
            t_bankdata_bond_to_ncc
        WHERE
            is_delete = 0
            AND account_type = #{bussineCode}
            AND bank_type = #{bankType}
    </select>
    <select id="findUnreturnedBond" resultType="com.hzw.sunflower.dto.UnreturnedBondDto">
        SELECT DISTINCT
         p.id projectId,
         p.project_name,
         CONCAT_WS('/',p.purchase_number,s.package_number) purchaseNumber,
         s.id sectionId,
         s.`status`,
         s.purchase_type,
         u.id user_id,
         u.user_name,
         ui.user_phone,
         a.company_name,
         a.company_id,
         IFNULL(b.bond_money,bs.amount) bond_money,
         n.seal_time,
         IFNULL(bp.is_win,0) is_win
        FROM
        t_project p
        LEFT JOIN t_project_bid_section s ON s.project_id = p.id
        LEFT JOIN t_user u ON u.id = p.created_user_id
        LEFT JOIN t_apply_info a ON a.sub_id = s.id
        LEFT JOIN t_bond_refund b ON b.section_id = s.id AND b.company_id = a.company_id AND b.is_delete = 0 AND b.is_delete = 0
        LEFT JOIN (SELECT bs.section_id,bs.company_id,SUM(bs.amount) amount,bs.updated_time FROM t_bond_split bs WHERE bs.is_delete = 0 GROUP BY bs.updated_time,bs.company_id,bs.section_id) bs
        ON bs.section_id = s.id AND bs.company_id = a.company_id
        LEFT JOIN t_bid_win_notice n ON n.section_id = s.id AND n.is_delete = 0
        LEFT JOIN t_bid_win_people bp ON bp.section_id = s.id AND bp.tenderer_id = a.company_id AND bp.win_people_type = 1 AND bp.is_delete = 0
        LEFT JOIN t_user_identity ui ON ui.user_id = u.id AND ui.identity = 2
        WHERE p.is_delete = 0
        AND s.is_delete = 0
        AND u.is_delete = 0
        AND a.is_delete = 0
        AND n.is_delete = 0
        AND ui.is_delete = 0
        AND s.`status` >= 42
        AND IFNULL(b.bond_money,bs.amount) is not null
        AND s.bond_type != 0
        <if test="null != startTime and startTime != ''">
            AND (DATE_FORMAT(s.updated_time,'%Y-%m-%d') &gt;= #{startTime}
            OR DATE_FORMAT(bs.updated_time,'%Y-%m-%d') &gt;= #{startTime}
            OR DATE_FORMAT(b.updated_time,'%Y-%m-%d') &gt;= #{startTime})
        </if>
        <if test="null != endTime and endTime != ''">
            AND (DATE_FORMAT(s.updated_time,'%Y-%m-%d') &lt;= #{endTime}
            OR DATE_FORMAT(bs.updated_time,'%Y-%m-%d') &lt;= #{endTime}
            OR DATE_FORMAT(b.updated_time,'%Y-%m-%d') &lt;= #{endTime})
        </if>
    </select>
    <select id="findDeptCode" resultType="java.lang.String">
        SELECT
        bd.department_code
        from
        t_bookkeep_department bd
        LEFT JOIN t_department d ON d.id = bd.department_id
        WHERE bd.is_delete = 0
        AND d.is_delete = 0
        AND bd.pid = 0
        AND d.`code` = #{substring}
    </select>
    <select id="findDeptName" resultType="java.lang.String">
        SELECT
        bd.department_name
        from
        t_bookkeep_department bd
        LEFT JOIN t_department d ON d.id = bd.department_id
        WHERE bd.is_delete = 0
        AND d.is_delete = 0
        AND bd.pid = 0
        AND d.`code` = #{substring}
    </select>
    <select id="getCandidatePeople" resultType="java.lang.Integer">
        SELECT
            p.is_candidate
        FROM
            t_bid_win_people p
            LEFT JOIN t_bid_win_bulletin b ON FIND_IN_SET( p.section_id, b.related_bid_section )
        WHERE
            p.section_id = #{sectionId}
            AND p.tenderer_id = #{companyId}
            AND p.win_people_type = 3
            AND p.is_candidate = 1
            AND b.`status` = 5
            AND b.bid_win_type = 0
            AND p.is_delete = 0
            AND b.is_delete = 0
    </select>

    <select id="checkWatersInRefund" resultType="java.lang.Integer">
        select
            count(t.water_id)
        from
        (
        SELECT
            DISTINCT
            bs.water_id
        FROM
            t_bond_refund br
            LEFT JOIN t_bond_split bs ON br.section_id = bs.section_id
            AND br.company_id = bs.company_id
        WHERE
            br.is_delete = 0
            AND bs.is_delete = 0
            <if test="refundId != null">
                and br.id != #{refundId}
            </if>
        union
        SELECT
            DISTINCT
            bar.water_id
        FROM
            t_bond_refund br
            LEFT JOIN t_bond_apply_refund bar ON bar.id = br.apply_refund_id
        WHERE
            br.is_delete = 0
            AND bar.is_delete = 0
        <if test="refundId != null">
            and br.id != #{refundId}
        </if>
        union
        select br.water_id from t_bond_refund br
        where br.is_delete = 0
        <if test="refundId != null">
            and br.id != #{refundId}
        </if>
        ) t
        where t.water_id  = #{waterId}
    </select>
    <select id="getProjectSectionByRefundId" resultType="java.lang.String">
        SELECT
            IF(pbs.package_number = NULL, p.purchase_number, CONCAT_WS('/',p.purchase_number,pbs.package_number))
        FROM
            t_bond_refund br
            LEFT JOIN t_project_bid_section pbs ON br.section_id = pbs.id
            LEFT JOIN t_project p ON pbs.project_id = p.id
        WHERE
            br.is_delete = 0
            AND pbs.is_delete = 0
            AND p.is_delete = 0
            AND br.id = #{refundId}
    </select>
    <select id="bondSupplierListOffline" resultType="com.hzw.sunflower.controller.response.BondSupplierInfoVo">
        SELECT
            pbs.id AS section_id,
            boc.id AS bondOfflineCompanyId,
            boc.offline_company_id AS offlineCompanyId,
            oc.company_name,
            oc.is_company_abroad,
            (case when sum(bs.amount) is null then 0 else sum(bs.amount) end ) as bond_money,
            pbs.agency_fee_obj,
            pbs.agency_cost_free,
            pbs.agency_fee_type AS feeType,
            1 AS bondType,
            bar.id AS refundId,
            bar.agency_fee_type,
            bar.file_ids,
            (case when bar.refund_money is null then 0 else bar.refund_money end) as refund_money,
            bar.remark,
            bar.rates,
            bar.former_agency_fee,
            bar.refund_mode,
            bar.is_agency_fee,
            bar.refund_process_code,
            (case when bar.`status` is null then 0 else bar.`status` end) AS refundStatus,
            (case when br.`status` is null then 4 else br.`status` end) AS relationStatus,
            (case when t.`status` is null then 0 else t.`status` end) as applyRelationStatus,
            (case when bwn.`status` is null then 0 else bwn.`status` end) as noticeStatus,
            (case when bwp.ss_agency_fee is null then 0 else bwp.ss_agency_fee end) as agencyFee,
            GROUP_CONCAT( DISTINCT bw.id ) AS water_ids,
            GROUP_CONCAT( DISTINCT bs.id ) AS split_ids,
            GROUP_CONCAT( DISTINCT bbr.receipt_oss_file_id ) AS receiptFiles
        FROM
            t_bond_offline_company boc
            LEFT JOIN t_offline_company oc ON boc.offline_company_id = oc.id AND oc.is_delete = 0
            LEFT JOIN t_project_bid_section pbs ON boc.section_id = pbs.id
            LEFT JOIN ( SELECT * FROM t_bid_win_notice WHERE find_in_set ( #{req.sectionId}, section_id ) and `status` = 5  and bid_round = 2 AND is_delete = 0 ORDER BY order_num LIMIT 1 ) bwn ON find_in_set ( pbs.id, bwn.section_id )
            LEFT JOIN t_bid_win_people bwp ON bwn.id = bwp.winbid_id AND oc.company_name = bwp.tenderer_name AND bwp.win_people_type = 1 AND bwp.is_delete = 0
            LEFT JOIN t_bond_split bs ON bs.section_id = pbs.id AND bs.company_id = oc.id AND bs.is_delete = 0
            LEFT JOIN t_bond_relation br ON br.company_id = bs.company_id AND br.water_id = bs.water_id AND br.section_id = bs.section_id AND br.is_delete = 0
            LEFT JOIN t_bond_water bw ON bw.id = br.water_id AND bw.is_delete = 0
            LEFT JOIN t_bond_refund bar ON bar.section_id = pbs.id AND bar.company_id = oc.id AND bar.is_delete = 0
            LEFT JOIN t_bond_refund_details brd ON bar.id = brd.refund_id AND brd.is_delete = 0
            LEFT JOIN t_bond_pay_info bpi ON bpi.refund_detail_id = brd.id AND bpi.is_delete = 0
            LEFT JOIN t_bond_bank_receipt bbr ON LEFT(bbr.relation_num ,30) = bpi.req_no AND bbr.is_delete = 0
            LEFT JOIN ( SELECT bar.* FROM t_bond_apply_relation bar WHERE bar.id IN (SELECT MAX( tbar.id ) id FROM t_bond_apply_relation tbar WHERE tbar.section_id = #{req.sectionId} AND tbar.is_delete = 0 GROUP BY tbar.company_id) ) t ON t.company_id = oc.id
        WHERE
            boc.is_delete = 0
            AND pbs.is_delete = 0
            AND pbs.id = #{req.sectionId}
        <if test="req.keyWords != null and !&quot;&quot;.equals(req.keyWords)">
            and oc.company_name like concat('%',#{req.keyWords},'%')
        </if>
        <if test="req.relationStatusOne != null">
            <if test="req.relationStatusOne != 4 and req.relationStatusOne != 5">
                and br.`status` = #{req.relationStatusOne}
            </if>
            <if test="req.relationStatusOne == 4">
                and br.`status` is null
                and (t.`status` is null or t.`status` in (2,3))
            </if>
            <if test="req.relationStatusOne == 5">
                and br.`status` is null
                and t.`status` = 1
            </if>
        </if>
        <if test="req.refundStatus != null and req.refundStatus.size > 0">
            <if test="req.refundNo.equals(true)">
                and (bar.`status` in
                <foreach collection="req.refundStatus" item="refundStatus" open="(" separator="," close=")" index="">
                    #{refundStatus}
                </foreach>
                or bar.`status` is null)
            </if>
            <if test="req.refundNo.equals(false)">
                and bar.`status` in
                <foreach collection="req.refundStatus" item="refundStatus" open="(" separator="," close=")" index="">
                    #{refundStatus}
                </foreach>
            </if>
        </if>
        GROUP BY
            pbs.id,
            boc.id,
            boc.offline_company_id,
            oc.company_name,
            oc.is_company_abroad,
            pbs.agency_fee_obj,
            pbs.agency_cost_free,
            pbs.agency_fee_type,
            pbs.bond_type,
            bar.id,
            bar.agency_fee_type,
            bar.file_ids,
            bar.refund_money,
            bar.remark,
            bar.rates,
            bar.former_agency_fee,
            bar.refund_mode,
            bar.`status`,
            br.`status`,
            t.`status`,
            bwn.`status`,
            bwp.ss_agency_fee
    </select>
    <select id="queryBondOfflineCompany"
            resultType="com.hzw.sunflower.controller.response.BondOfflineCompanyVo">
        SELECT
            boc.id AS bondOfflineCompanyId,
            boc.project_id,
            boc.section_id,
            boc.offline_company_id,
            boc.apply_company_name,
            boc.pay_file_id,
            oc.company_name,
            oc.organization_num,
            oc.business_license,
            oc.license_file_id,
            oc.is_company_abroad
        FROM
            t_bond_offline_company boc
            LEFT JOIN t_offline_company oc ON boc.offline_company_id = oc.id
        WHERE
            boc.is_delete = 0
            AND oc.is_delete = 0
            AND boc.section_id = #{sectionId}
            AND oc.id = #{offlineCompanyId}
    </select>
</mapper>
