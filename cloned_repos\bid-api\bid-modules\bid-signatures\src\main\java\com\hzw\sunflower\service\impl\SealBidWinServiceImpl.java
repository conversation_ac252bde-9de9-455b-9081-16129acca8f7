package com.hzw.sunflower.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzw.sunflower.controller.request.SealBidWinREQ;
import com.hzw.sunflower.controller.response.SealBidWinVO;
import com.hzw.sunflower.dao.SealBidWinMapper;
import com.hzw.sunflower.entity.SealBidWin;
import com.hzw.sunflower.service.SealBidWinService;
import org.springframework.stereotype.Service;

import java.util.List;


/**
* SealBidWinService接口实现
*
* <AUTHOR>
* @version 1.0.0 2021-04-07
*/
@Service
public class SealBidWinServiceImpl extends ServiceImpl<SealBidWinMapper, SealBidWin> implements SealBidWinService {
    /**
     * 发放中标通知书查询盖章文件
     * @param req
     * @return
     */
    @Override
    public List<SealBidWinVO> listSealFiles(SealBidWinREQ req) {
        return this.baseMapper.listSealFiles(req);
    }
}