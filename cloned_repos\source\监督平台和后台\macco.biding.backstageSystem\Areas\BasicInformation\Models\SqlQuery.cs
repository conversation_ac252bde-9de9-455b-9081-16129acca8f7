﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using macco.biding.entities.mup007;

namespace macco.biding.backstageSystem.Areas.BasicInformation.Models
{
    public class SqlQuery : BaseEntity
    {
        /// <summary>
        /// sql语句ID
        /// </summary>
        public virtual int Id
        {
            set;
            get;
        }
        /// <summary>
        /// sql功能描述(名称)
        /// </summary>
        public virtual string QueryName
        {
            set;
            get;
        }
        /// <summary>
        /// sql语句
        /// </summary>
        public virtual string QuerySqlString
        {
            set;
            get;
        }
        /// <summary>
        /// sql功能详情
        /// </summary>
        public virtual string Remark
        {
            set;
            get;
        }

        /// <summary>
        /// 参数
        /// </summary>
        public virtual string Parameters
        {
            set;
            get;
        }      
        /// <summary>
        /// 参数列表
        /// </summary>
        public virtual List<SqlParameters> sqlParameters
        {
            set;
            get;
        }
    }

    public class SqlParameters 
    {
        /// <summary>
        /// sql语句ID
        /// </summary>
        public virtual int QueryId
        {
            set;
            get;
        }

        /// <summary>
        /// 参数名
        /// </summary>
        public virtual string ParamName
        {
            set;
            get;
        }

        /// <summary>
        /// 参数描述
        /// </summary>
        public virtual string ParamDesc
        {
            set;
            get;
        }

        /// <summary>
        /// 参数详情
        /// </summary>
        public virtual string Remark
        {
            set;
            get;
        }
        // 是否时间参数
        public virtual bool IsTimeParam
        {
            get;
            set;
        }
    }
}