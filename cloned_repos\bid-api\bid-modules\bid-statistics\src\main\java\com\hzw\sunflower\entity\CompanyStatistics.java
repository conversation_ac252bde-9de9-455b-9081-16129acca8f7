package com.hzw.sunflower.entity;

import cn.hutool.core.date.DateTime;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hzw.sunflower.common.BaseBean;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;


@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("t_statistics_company")
public class CompanyStatistics extends BaseBean implements Serializable {
    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "月份")
    private DateTime month;

    @ApiModelProperty(value = "已开标项目数")
    private Long projectCount;

    @ApiModelProperty(value = "未开标项目数")
    private Long nProjectCount;

    @ApiModelProperty(value = "已开标项目委托金额")
    private BigDecimal entrustedAmount;

    @ApiModelProperty(value = "未开标项目委托金额")
    private BigDecimal nEntrustedAmount;

    @ApiModelProperty(value = "中标金额")
    private BigDecimal winPrice;

    @ApiModelProperty(value = "已收费")
    private BigDecimal account;

    @ApiModelProperty(value = "已开标未收费中标金额预估")
    private BigDecimal estimateAccount;

    @ApiModelProperty(value = "已开标未收费金额")
    private BigDecimal nAccount;
}
