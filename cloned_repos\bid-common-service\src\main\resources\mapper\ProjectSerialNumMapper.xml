<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!-- 项目委托关系人表 (t_project_serial_num) -->
<mapper namespace="com.hzw.sunflower.dao.ProjectSerialNumMapper">

    <!-- 获取当前年份最大的流水号-->
    <select id="getYearMaxSerialNum" resultType="java.lang.Integer" >
        select max(t.serial_Number) from t_project_serial_num t where t.year= #{year}  and serial_type = #{type}
    </select>
    <select id="selectDepartCodeById" resultType="java.lang.String" >
        select code from t_department where id =#{id}
    </select>

    <select id="selectDepartProjectCodeById" resultType="java.lang.String" >
        select project_code from t_department where id =#{id}
    </select>

    <select id="selectDepartCodeByUserId" resultType="java.lang.String" >
         select d.code from t_department d
         left join r_user_department  ud
         on d.id = ud.department_id
         left join t_user u
         on ud.user_id = u.id
         where d.is_delete = 0
         and ud.is_delete = 0
         and u.is_delete = 0
         and u.id = #{userId};
    </select>
    
    <insert id="insetMaxNumByYearType" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO t_project_serial_num (year, serial_type, serial_Number)
        SELECT
            #{year}, #{serialType},
            IFNULL(MAX(serial_Number), 0) + 1
        FROM t_project_serial_num
        WHERE year = #{year} AND serial_type = #{serialType};
    </insert>
</mapper>