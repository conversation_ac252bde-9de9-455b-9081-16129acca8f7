package com.hzw.sunflower.entity;


import com.baomidou.mybatisplus.annotation.TableName;
import com.hzw.sunflower.common.BaseBean;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * Created by zgq on 2021/07/12
 */
@ApiModel("公告变更记录")
@Data
@Accessors(chain = true)
@TableName("t_notice_change_record")
public class NoticeChangeRecord extends BaseBean {

    /**
     * 主键
    */
    private Long id;

    /**
     * 项目ID
     */
    private Long projectId;
    /**
     * 提交次数
     */
    private Long submitNum;
    /**
     * 变更时间
     */
    private Date changeTime;
    /**
     * 变更类型：0 首次提交；1 撤回变更；2 退回变更
     */
    private Integer changeType;
    /**
     * 公告ID
     */
    private Long noticeId;
    /**
     * 拟发布时间
     */
    private Date IntendedReleaseTime;
    /**
     * 公告截止时间
     */
    private Date endTime;
    /**
     * 公告内容
     */
    private String noticeContent;
    /**
     * 关联包段ID
     */
    private String sectionId;
    /**
     * 公告名称
     */
    private String noticeName;
    /**
     * 关联媒体ID
     */
    private Long mediaId;
    /**
     * 公告附件ID
     */
    private String annexId;
}
