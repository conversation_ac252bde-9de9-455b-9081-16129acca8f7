///***
/// * 信息订阅 - 入口页
/// *
/// * 本页面包含 [DataListView] 组件，因此不能使用 Provider 包.
/// */

import 'package:flutter/material.dart';

import 'package:flutter_main_app/serviceLocator.dart';
import 'package:flutter_main_app/src/fonts/fonts.dart';

import 'package:flutter_main_app/src/viewModels/customInfo/CustomInfoViewModel.dart';

import 'package:flutter_main_app/src/models/bulletin/BulletinListQueryModel.dart';

import 'package:flutter_main_app/src/components/CustomAppBar.dart';

import 'package:flutter_main_app/src/widgets/info/InfoNoSubscriptionSettingsWidget.dart';
import 'package:flutter_main_app/src/widgets/info/InfoKeywordSettingsWidget.dart';
import 'package:flutter_main_app/src/widgets/info/InfoDataViewWidget.dart';

class InfoIndexWidget extends StatefulWidget {
  const InfoIndexWidget({Key key}) : super(key: key);

  /// * 路由名称
  static const String _routeName = 'InfoIndex';
  static String get routeName => _routeName;

  @override
  _InfoIndexWidgetState createState() => _InfoIndexWidgetState();
}

class _InfoIndexWidgetState extends State<InfoIndexWidget> {

  final CustomInfoViewModel _model = serviceLocator<CustomInfoViewModel>();

  List<String> _keywords = [];

  bool _isFromKeywordSettingsPage = false;

  @override
  void initState() {
    super.initState();

    _model.keywords = [];
    _model.params = BulletinListQueryModel(
      bidType: [],
      bulletinTypeName: [],
      classifyName: [],
      currentPage: '1',
      fundRange: [],
      fundSource: [],
      industryCode: [],
      keyWord: _model.keywords,
      pageSize: 20,
      platformCode: [],
      regionCode: [],
      uid: '',
    );
    _model.publishTime = [];

    _keywords.addAll(_model.keywords);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      appBar: CustomAppBar(
        title: '信息订阅',
        leading: Container(),
        actions: <Widget>[
          IconButton(
            icon: Icon(
              FontsHelper.settingsIconData,
              color: const Color(0xffffffff),
            ),
            tooltip: '设置',
            onPressed: () {
              this._pushNavigatorToKeywordSettings(context);
            },
          ),
        ],
      ),
      body: _hasKeyword(_keywords) && _isFromKeywordSettingsPage
        ? InfoDataViewWidget()
        : InfoNoSubscriptionSettingsWidget(
            pushNavigatorToKeywordSettings: _pushNavigatorToKeywordSettings,
          ),
    );
  }

  ///***
  /// * 判断是否要存在订阅关键词
  /// *
  /// * @param {List<String>} keywords
  /// *
  /// * @return {bool}
  /// */
  bool _hasKeyword(List<String> keywords) {
    return keywords != null && keywords.isNotEmpty && keywords[0].isNotEmpty;
  }

  ///***
  /// * 路由跳转到 订阅关键词 页面
  /// *
  /// * @param {BuildContext} context
  /// */
  Future<void> _pushNavigatorToKeywordSettings(BuildContext context) async {
    await Navigator.pushNamed(context, InfoKeywordSettingsWidget.routeName);

    setState(() {
      _keywords =_model.keywords;
      _isFromKeywordSettingsPage = true;
    });
  }

}
