package com.hzw.sunflower.controller.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ApiModel(description = "异常退款申请运管处处理")
public class BondAbnormalApplyRefundConfirmReq implements Serializable {

    @ApiModelProperty(value = "申请退还id")
    private Long applyId;

    @ApiModelProperty(value = "处理方式，1.指定处室处长确认 2.所有处室处长确认 3.退回")
    private Integer confirmType;

    @ApiModelProperty(value = "处室id")
    private List<Long> departments;

    @ApiModelProperty(value = "退回原因")
    private String remark;

}
