package com.hzw.sunflower.controller.project.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @description：项目编号
 * @version: 1.0
 */

@ApiModel(description = "项目编号请求体 ")
@Data
public class PackageNumberREQ {

    @ApiModelProperty(value = "标段Id", position = 1)
    private Long id;

    @ApiModelProperty(value = "标段序号", position = 2)
    private Integer packageNumber;
}
