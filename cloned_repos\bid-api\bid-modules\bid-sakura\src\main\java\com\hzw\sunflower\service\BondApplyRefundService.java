package com.hzw.sunflower.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.controller.request.*;
import com.hzw.sunflower.controller.response.*;
import com.hzw.sunflower.entity.BondApplyRefund;
import com.hzw.sunflower.entity.BondApplyRelation;
import com.hzw.sunflower.entity.JwtUser;
import com.hzw.sunflower.entity.condition.*;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>
 * 保证金退还申请表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-27
 */
public interface BondApplyRefundService extends IService<BondApplyRefund> {

    /**
     * 供应商未关联流水查询
     * @param condition
     * @param jwtUser
     * @return
     */
    IPage<BondNotRelationVo> getBondWaterNotRelationPage(BondNotRelationCondition condition, JwtUser jwtUser);

    /**
     * 供应商未关联流水查询
     * @param condition
     * @param jwtUser
     * @return
     */
    IPage<BondNotRelationVo> getBondWaterNotRelationPageNew(BondNotRelationCondition condition, JwtUser jwtUser);

    /**
     * 查询当前登录供应商所在公司参与的所有项目信息
     * @param req
     * @param jwtUser
     * @return
     */
    List<MyCompanyProjectVo> getMyCompanyProject(MyCompanyProjectReq req, JwtUser jwtUser);

    /**
     * 根据项目id获取当前供应商参与是标包信息
     * @param req
     * @param jwtUser
     * @return
     */
    List<MyCompanyProjectVo> getMyCompanySection(MyCompanyProjectReq req, JwtUser jwtUser);

    /**
     * 异常退款申请/编辑(供应商)
     * @param req
     * @return
     */
    Boolean saveBondAbnormalApplyRefund(BondAbnormalApplyRefundReq req);

    /**
     * 根据退款流水id查询异常退款申请信息
     * @param req
     * @return
     */
    BondAbnormalApplyRefundVo getBondAbnormalApplyRefund(BondAbnormalApplyRefundReq req);

    /**
     * 异常退款申请待处理列表(由供应商发起)
     * @param condition
     * @return
     */
    IPage<BondApplyRefundVo> getBondApplyRefundList(BondApplyRefundCondition condition);

    /**
     * 异常退款申请(供应商发起)详情
     * @param req
     * @return
     */
    BondAbnormalApplyRefundDetailVo bondAbnormalApplyRefundDetail(BondApplyRefundDetailReq req,JwtUser jwtUser);

    /**
     * 异常退款申请运管处处理查询供应商参与的项目
     * @param req
     * @return
     */
    IPage<MyCompanyProjectVo> bondAbnormalApplyRefundProject(BondApplyRefundProjectDetailReq req);

    /**
     * 异常退款申请(供应商发起)运管处处理
     * @param req
     * @return
     */
    Boolean saveBondAbnormalApplyRefundConfirm(BondAbnormalApplyRefundConfirmReq req);

    /**
     * 异常退款申请处长处理
     * @param req
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    Boolean saveBondAbnormalApplyRefundRegistrar(BondAbnormalApplyRefundRegistrarReq req);

    /**
     * 异常关联确认(项目经理发起)列表
     * @param condition
     * @return
     */
    IPage<AbnormalApplyRelationVo> getBondAbnormalRelationList(BondApplyRelationCondition condition);

    /**
     * 保证金关联申请(项目经理发起)列表
     * @param condition
     * @return
     */
    IPage<AbnormalApplyRelationVo> bondApplyRelationList(BondApplyRelationCondition condition);

    /**
     * 据id查询保证金申请关联(项目经理发起)详情
     * @param req
     * @return
     */
    BondApplyRelation getBondApplyRelationById(BondApplyRelationConfirmReq req);

    /**
     * 保证金关联申请同意/退回(项目经理发起)
     * @param req
     * @return
     */
    Boolean saveBondApplyRelationConfirm(BondApplyRelationConfirmReq req);

    /**
     * 未关联流水申请取消
     * @param waterId
     * @return
     */
    Result<Boolean> deleteBondRefundWater(Long waterId);
}
