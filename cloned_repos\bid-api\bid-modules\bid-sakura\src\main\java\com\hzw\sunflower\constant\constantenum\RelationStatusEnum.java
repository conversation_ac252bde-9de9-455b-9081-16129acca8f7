package com.hzw.sunflower.constant.constantenum;

/**
 * <AUTHOR>
 * @date ：Created in 2022/07/27 9:23
 * @description：关联状态
 * @version: 1.0
 */
public enum RelationStatusEnum {

    CONFIRM_RELATE(1, "确认关联"),
    HAS_RELATE(2, "已关联"),
    EXCEPTION_RELATE(3, "异常关联"),
    NOT_RELATE(4, "未关联"),

    APPLY_RELATION_WAITE(5, "申请关联待确认");

    private Integer type;
    private String desc;

    RelationStatusEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public static RelationStatusEnum getType(Integer type) {
        try {
            for (RelationStatusEnum v : RelationStatusEnum.values()) {
                if (v.type.equals(type)) {
                    return v;
                }
            }
            return null;
        } catch (Exception e) {
            return null;
        }
    }
}
