package com.hzw.sunflower.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hzw.sunflower.common.BaseBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 保证金退还表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-27
 */
@Data
@TableName("t_bond_refund_details")
@ApiModel(value = "BondWater对象", description = "保证金退还表")
public class BondRefundDetails extends BaseBean implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("退还id")
    private Long refundId;

    @ApiModelProperty(value = "保证金金额")
    private BigDecimal bondMoney;

    @ApiModelProperty(value = "代理服务费")
    private BigDecimal agencyFee;

    @ApiModelProperty(value = "保证金利息")
    private BigDecimal rates;

    @ApiModelProperty(value = "来自哪个开户行")
    private String fromOpenBank;

    @ApiModelProperty(value = "来自哪个银行账号")
    private String fromBankNumber;

    @ApiModelProperty(value = "状态：1：待处理  2：已退回    3：退还成功 4：退还失败 5:退还中")
    private Integer status;

    @ApiModelProperty(value = "代理服务费退还状态:    3：退还成功 4：退还失败 5:退还中")
    private Integer agencyRefundStatus;

    @ApiModelProperty(value = "退款户名")
    private String refundCompanyName;

    @ApiModelProperty(value = "退款账号")
    private String refundNumber;

    @ApiModelProperty(value = "退款开户行")
    private String refundOpenBank;

    @ApiModelProperty(value = "退还银联号")
    private String refundBankCode;

    @ApiModelProperty(value = "退还金额")
    private BigDecimal refundMoney;

    @ApiModelProperty(value = "到账日期")
    private String amountDate;

    @ApiModelProperty(value = "失败原因")
    private String failReason;

}
