package com.hzw.ssm.sys.call.action;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import net.sf.json.JSONObject;

import org.apache.struts2.ServletActionContext;
import org.apache.struts2.convention.annotation.Action;
import org.apache.struts2.convention.annotation.Namespace;
import org.springframework.beans.factory.annotation.Autowired;

import com.hzw.ssm.fw.base.BaseAction;
import com.hzw.ssm.sys.call.service.VoiceService;
import com.hzw.ssm.sys.call.util.JsonTOBean;
import com.itextpdf.text.log.Logger;
/**
 * 短信通知功能
 * <AUTHOR>
 *
 */
@Namespace("/voice")
public class VoiceAction extends BaseAction{
	
	private JSONObject bodys;
	
	private HttpServletResponse resp;
	@Autowired
	private VoiceService voiceService;
	
	@Action("/callback")
	public void callback() throws Exception{
		System.out.println("进入话单接收方法体");
		JsonTOBean jsonToBean = new JsonTOBean();
		//获取Request
		this.context();
		HttpServletRequest request =  ServletActionContext.getRequest();
		StringBuffer sb = new StringBuffer();
		PrintWriter out = this.getResponse().getWriter();
		try{
			String jsonStr = "";
			BufferedReader reader =  request.getReader();
			
			while((jsonStr = reader.readLine())!=null){
				sb.append(jsonStr);
			}
			System.out.println(sb);
			
			if ( null !=sb && !"".equals(sb.toString().trim())){ 
			//获取实例beanmessageId
			//VoiceDateEntity dateEntity= jsonToBean.JsonToJavaBean(sb.toString(), VoiceDateEntity.class);
			
			//返回正确结果
			Map <String, String> headerMap = new HashMap<String, String>();
			Map <String, Object> responseMap = new HashMap<String, Object>();
			
			headerMap.put("serviceName", "ACRQuestResponse");   //IVRQuestRequest
			headerMap.put("messageId", UUID.randomUUID().toString().replaceAll("-", ""));
			responseMap.put("header", headerMap);
			
			Map <String, String> bodyMap = new HashMap<String, String>();
			bodyMap.put("result", "0000");
			bodyMap.put("reason", "succ"); 
			responseMap.put("body",bodyMap);
			JSONObject responseToJson = JSONObject.fromObject(responseMap);
		//	System.out.println(responseToJson);
			
			out.print(responseToJson.toString());
			//resp.getWriter().write(responseToJson.toString());
			//String ss ="{'body':{'serviceKey':'900013','callId':'C20181113204338633AC7AE59A0F07B41769','calledNum':'15371119615','calledDisplayNumber':'02566834713','calledStreamNo':'20181113204303442110AC102A2142482365','startCalledTime':'20181113204255','stopCalledTime':'20181113204303','calledDuration':'8','calledCost':'0','calledRelCause':'1','calledOriRescode':'1','chargeNumber':'02566834713','calledRelReason':'','callOutTime':'20181113204245','msServer':'172.16.35.111','duration':'8','costCount':'0','vccId':'3343','dtmfKey':'2'},'header':{'serviceName':'ACRQuestRequest','messageId':'703449103dc04a398aa10d8625f44a35'}}";
			//ss+="{'body':{'serviceKey':'900013','callId':'C20181113204338633AC7AE59A0F07B41769','calledNum':'15371119615','calledDisplayNumber':'02566834713','calledStreamNo':'20181113204303442110AC102A2142482365','startCalledTime':'20181113204255','stopCalledTime':'20181113204303','calledDuration':'8','calledCost':'0','calledRelCause':'1','calledOriRescode':'1','chargeNumber':'02566834713','calledRelReason':'','callOutTime':'20181113204245','msServer':'172.16.35.111','duration':'8','costCount':'0','vccId':'3343','dtmfKey':'2'},'header':{'serviceName':'ACRQuestRequest','messageId':'703449103dc04a398aa10d8625f44a35'}}";
			//JSONObject jo= JSONObject.fromObject(ss.toString());
			
			//System.out.println(jo.values().toString());
			
			//voiceService.getMessage(jo.toString());
			
			voiceService.getMessage(sb.toString());
			
			}else{
				System.out.println("错误信息");
				//返回正确结果
				Map <String, String> headerMap = new HashMap<String, String>();
				Map <String, Object> responseMap = new HashMap<String, Object>();
				
				headerMap.put("serviceName", "ACRQuestResponse");   //IVRQuestRequest
				headerMap.put("messageId", UUID.randomUUID().toString().replaceAll("-", ""));
				responseMap.put("header", headerMap);
				
				Map <String, String> bodyMap = new HashMap<String, String>();
				bodyMap.put("result", "9999");
				bodyMap.put("reason", "接收错误 "); 
				responseMap.put("body",bodyMap);
				JSONObject responseToJson = JSONObject.fromObject(responseMap);
				out.print(responseToJson.toString());
				//resp.getWriter().write(responseToJson.toString());
			}
		}catch(IOException e){
			e.printStackTrace();
		}finally{
			out.close();
		}
		//log.error("测试返回值："+sb.toString());
		
		//String ss ="{'body':{'serviceKey':'900013','callId':'C20181113204338633AC7AE59A0F07B41769','calledNum':'15371119615','calledDisplayNumber':'02566834713','calledStreamNo':'20181113204303442110AC102A2142482365','startCalledTime':'20181113204255','stopCalledTime':'20181113204303','calledDuration':'8','calledCost':'0','calledRelCause':'1','calledOriRescode':'1','chargeNumber':'02566834713','calledRelReason':'','callOutTime':'20181113204245','msServer':'172.16.35.111','duration':'8','costCount':'0','vccId':'3343','dtmfKey':'2'},'header':{'serviceName':'ACRQuestRequest','messageId':'703449103dc04a398aa10d8625f44a35'}}";
		//JSONObject jo= JSONObject.fromObject(ss.toString());
		
		//System.out.println(jo.values().toString());
		
		//voiceService.getMessage(jo.toString());
		
		//System.out.println("返回结果为："+bodys);
		
		
	}

	@Action("/testSms")
	public void testSms(){
		voiceService.work();
	}
	public JSONObject getBodys() {
		return bodys;
	}

	public void setBodys(JSONObject bodys) {
		this.bodys = bodys;
	}

	public HttpServletResponse getResp() {
		return resp;
	}

	public void setResp(HttpServletResponse resp) {
		this.resp = resp;
	}
}
