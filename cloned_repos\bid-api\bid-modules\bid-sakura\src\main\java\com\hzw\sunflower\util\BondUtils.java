package com.hzw.sunflower.util;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.Calendar;
import java.util.Date;

/**
 * <AUTHOR>
 * @datetime 2022/11/09 14:49
 * @description: 保证金工具类
 * @version: 1.0
 */
public class BondUtils {

    /**
     * 判断是否隔月
     * @param date  日期
     * @return
     */
    public static Boolean isOverMonth(String date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(DateUtils.parseDate(date));
        calendar.set(Calendar.DAY_OF_MONTH,1);
        calendar.add(Calendar.MONTH, 1);
        return new Date().getTime() > calendar.getTime().getTime();
    }

    /**
     * 去掉bean中所有属性为字符串的前后空格
     * @param bean
     * @throws Exception
     */
    public static void beanAttributeValueTrim(Object bean) {
        if(bean != null){
            try {
                Field[] fields = bean.getClass().getDeclaredFields();
                for (int i = 0; i < fields.length; i++) {
                    Field f = fields[i];
                    if (f.getType().getName().equals("java.lang.String")) {
                        //获取字段名
                        String key = f.getName();
                        Object value = getFieldValue(bean, key);
                        if (value == null) {
                            continue;
                        }
                        setFieldValue(bean, key, value.toString().replaceAll("\r\n", "").replaceAll("\t", ""));
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }


    /**
     * 获取bean中字段fieldName的值
     */
    private static Object getFieldValue(Object bean, String fieldName)
            throws Exception {
        StringBuffer result = new StringBuffer();
        String methodName = result.append("get")
                .append(fieldName.substring(0, 1).toUpperCase())
                .append(fieldName.substring(1)).toString();

        Object rObject = null;
        Method method = null;

        // 判断是否有该get方法
        Method[] declaredMethods = bean.getClass().getDeclaredMethods();
        boolean hasGet = false;
        for (Method methods : declaredMethods){
            if (methods.getName().equals(methodName)){
                hasGet = true;
            }
        }

        if (hasGet){
            @SuppressWarnings("rawtypes")
            Class[] classArr = new Class[0];
            method = bean.getClass().getMethod(methodName, classArr);
            rObject = method.invoke(bean, new Object[0]);
        }

        return rObject;
    }

    /**
     * 将value设置到字段
     */
    private static void setFieldValue(Object bean, String fieldName, Object value)
            throws Exception {
        StringBuffer result = new StringBuffer();
        String methodName = result.append("set")
                .append(fieldName.substring(0, 1).toUpperCase())
                .append(fieldName.substring(1)).toString();

        Class[] classArr = new Class[1];
        classArr[0]="java.lang.String".getClass();
        Method method=bean.getClass().getMethod(methodName,classArr);
        method.invoke(bean,value);
    }


}
