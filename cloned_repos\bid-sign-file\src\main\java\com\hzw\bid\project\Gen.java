package com.hzw.bid.project;

import com.baomidou.mybatisplus.generator.FastAutoGenerator;
import com.baomidou.mybatisplus.generator.config.DataSourceConfig;
import com.baomidou.mybatisplus.generator.config.OutputFile;
import com.baomidou.mybatisplus.generator.config.converts.MySqlTypeConvert;
import com.baomidou.mybatisplus.generator.config.querys.MySqlQuery;
import com.baomidou.mybatisplus.generator.engine.FreemarkerTemplateEngine;
import com.baomidou.mybatisplus.generator.keywords.MySqlKeyWordsHandler;

import java.util.Collections;

public class Gen {


    public static void main(String[] args) {
        doGen();
    }


    public static void doGen(){
        FastAutoGenerator
                .create(new DataSourceConfig.Builder("************************************************************************",
                                "bid-test-prod",
                                "bid-test-prod")
                        .dbQuery(new MySqlQuery())
                        .schema("bid-test-prod")
                        .typeConvert(new MySqlTypeConvert())
                        .keyWordsHandler(new MySqlKeyWordsHandler())
                        )

                .globalConfig(builder -> {
                    builder.author("mybatis Gen") // 设置作者
                            .enableSwagger() // 开启 swagger 模式
//                            .fileOverride() // 覆盖已生成文件
                            .outputDir("D://1"); // 指定输出目录
                })

                .packageConfig(builder -> {
                    builder.parent("com.hzw.bid") // 设置父包名
                            .moduleName("project") // 设置父包模块名
                            .entity("model")
                            .service("service")
                            .serviceImpl("service.impl")
                            .mapper("mapper")
                            .xml("mapper.xml")
                            .controller("controller")
                            .other("other")
                            .pathInfo(Collections.singletonMap(OutputFile.xml, "D://1")

                            ); // 设置mapperXml生成路径
                })
                .strategyConfig(builder -> {
                    builder.addInclude("t_app_data_project","t_app_data_project") // 设置需要生成的表名

                            .addTablePrefix("t_")// 设置过滤表前缀
                            .addTableSuffix("_BAK")
                            .controllerBuilder().enableRestStyle()
                            .entityBuilder().enableLombok()

                    ;

                })

                .templateEngine(new FreemarkerTemplateEngine()) // 使用Freemarker引擎模板，默认的是Velocity引擎模板
                .execute();
    }


}
