package com.hzw.sunflower.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.constant.EntrustConstants;
import com.hzw.sunflower.constant.constantenum.EntrustUserType;
import com.hzw.sunflower.controller.project.request.AgentUserProPermREQ;
import com.hzw.sunflower.dao.ProjectTendererPowerMapper;
import com.hzw.sunflower.entity.ProjectEntrustUser;
import com.hzw.sunflower.entity.ProjectTendererPower;
import com.hzw.sunflower.security.utils.SecurityUtils;
import com.hzw.sunflower.service.ProjectEntrustUserService;
import com.hzw.sunflower.service.ProjectTendererPowerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * 招标人项目权限
 *
 * <AUTHOR>
 * @version 1.0.0 2021-04-14
 */
@Service
public class ProjectTendererPowerServiceImpl extends ServiceImpl<ProjectTendererPowerMapper, ProjectTendererPower> implements ProjectTendererPowerService {

    @Autowired
    private ProjectEntrustUserService projectEntrustUserService;

    @Override
    public List<ProjectTendererPower> listProjectPermissions(ProjectTendererPower req) {
        QueryWrapper<ProjectTendererPower> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ProjectTendererPower::getProjectId,req.getProjectId());
        return this.list(queryWrapper);
    }

    @Override
    public Boolean addOrUpdateProjectPermissions(ProjectTendererPower req) {
        Boolean flag = false;
        QueryWrapper<ProjectTendererPower> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ProjectTendererPower::getProjectId,req.getProjectId())
                .eq(ProjectTendererPower::getUserId,req.getUserId());
        ProjectTendererPower one = this.getOne(queryWrapper);
        if(BeanUtil.isEmpty(one)){
            flag =  this.save(req);
            ProjectEntrustUser projectEntrustUser = new ProjectEntrustUser();
            projectEntrustUser.setProjectId(req.getProjectId());
            projectEntrustUser.setUserId(req.getUserId());
            projectEntrustUser.setType(EntrustUserType.PRINCIPAL.getType());
            projectEntrustUser.setCompanyId(SecurityUtils.getJwtUser().getCompanyId());
            projectEntrustUserService.save(projectEntrustUser);
        } else {
            req.setId(one.getId());
            flag = this.saveOrUpdate(req);
        }
        return flag;
    }

    @Override
    public Result<Boolean> deleteById(Long id) {
        ProjectTendererPower power = this.getById(id);
        QueryWrapper<ProjectTendererPower> powerQueryWrapper = new QueryWrapper<>();
        powerQueryWrapper.lambda().eq(ProjectTendererPower::getRoleType,EntrustUserType.READ_UPDATE.getType())
                .eq(ProjectTendererPower::getProjectId,power.getProjectId())
                .ne(ProjectTendererPower::getId,id);
        List<ProjectTendererPower> list = this.list(powerQueryWrapper);
        if(CollectionUtil.isEmpty(list)){
            return Result.failed(EntrustConstants.DELETE_FAILED);
        }
        QueryWrapper<ProjectEntrustUser> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ProjectEntrustUser::getProjectId,power.getProjectId())
                .eq(ProjectEntrustUser::getUserId,power.getUserId())
                .eq(ProjectEntrustUser::getType, EntrustUserType.PRINCIPAL.getType());
        projectEntrustUserService.remove(queryWrapper);
        this.removeById(id);
        return Result.ok();
    }


    @Override
    public List<ProjectTendererPower> selectByProjectId(Long projectId) {
        LambdaQueryWrapper<ProjectTendererPower> lwq = new LambdaQueryWrapper();
        lwq.eq(ProjectTendererPower::getProjectId,projectId);
        return this.getBaseMapper().selectList(lwq);
    }

    @Override
    public Integer updateTetetendereeByAgency(Long projectId) {
        return this.getBaseMapper().updateTetetendereeByAgency(projectId);
    }
}