package com.hzw.ssm.sys.system.service;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.hzw.ssm.fw.base.BaseService;
import com.hzw.ssm.sys.system.dao.SmsRecordMapper;
import com.hzw.ssm.sys.system.entity.SmsRecordEntity;

/**
 * 短信记录处理类
 * <AUTHOR>
 * @date 2015-08-05
 *
 */
@Service
public class SmsRecordService extends BaseService 
{
	@Autowired
	private SmsRecordMapper smsRecordMapper;
	
	/**
	 * 批量添加短信记录
	 * @param smsRecordList
	 */
	@Transactional(propagation=Propagation.NOT_SUPPORTED) 
	public void insertSmsRecordList(List<SmsRecordEntity> smsRecordList)
	{
		smsRecordMapper.insertSmsRecordList(smsRecordList);
	}

	/**
	 * 添加短信记录
	 * @param smsRecordList
	 */
	@Transactional(propagation=Propagation.NOT_SUPPORTED) 
	public void insertSmsRecord(SmsRecordEntity smsRecord)
	{
		smsRecordMapper.insertSmsRecord(smsRecord);
	}
	

	/**
	 * 添加短信记录
	 * @param smsRecordList
	 */
	@Transactional(propagation=Propagation.NOT_SUPPORTED) 
	public void updateSmsRecord(SmsRecordEntity smsRecord)
	{
		try{
			smsRecordMapper.updateSmsRecord(smsRecord);
		}catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	public void updateInform(SmsRecordEntity smsRecord){
		smsRecordMapper.updateInform(smsRecord);
	}
	
	public List<SmsRecordEntity> queryPageUnreceivedSmsRecord(SmsRecordEntity smsRecord){
		return smsRecordMapper.queryPageUnreceivedSmsRecord(smsRecord);
	}
	
	public List<SmsRecordEntity> queryPageUnreceivedSmsRecordList(SmsRecordEntity smsRecord){
		return smsRecordMapper.queryPageUnreceivedSmsRecordList(smsRecord);
	}
	
	/**
	 * 添加短信记录
	 * @param smsRecordList
	 */
	public void updateSmsIsDispose(SmsRecordEntity smsRecord)
	{
		try{
			smsRecordMapper.updateSmsIsDispose(smsRecord);
		}catch (Exception e) {
			e.printStackTrace();
		}
	}
}
