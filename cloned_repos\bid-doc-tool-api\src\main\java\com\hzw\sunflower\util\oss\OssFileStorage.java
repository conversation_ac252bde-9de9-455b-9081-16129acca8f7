package com.hzw.sunflower.util.oss;


import java.io.InputStream;

/**
 * <AUTHOR>
 * @date ：Created in 2022/6/10 14:22
 * @description：
 * @modified By：`
 * @version: 1.0
 */
public interface OssFileStorage {

    /**
     * 文件下载
     * @param key
     * @return
     */
    byte[] downloadFile(String key);

    /**
     * 下载到制定路径
     * @param key
     * @param filePath
     */
    void downloadFilePath(String key,String filePath);

    byte[] downloadFile(String key,String bucketName);

    /**
     * 根据流文件上传
     * @param key 文件key
     * @param is 文件流
     * @return
     */
    String uploadFile(String key, InputStream is);
    /**
     * 根据流文件上传
     * @param key 文件key
     * @param is 文件流
     * @return
     */
    String uploadFile(String key, InputStream is,String bucketName);

    /**
     * 根据路径上传文件
     * @param filePath
     * @return
     */
    String uploadFileByPath(String filePath);

    /**
     * 预览
     * @param key
     * @return
     */
    String generatePresignedUrl(String key);

    String generatePresignedUrl(String key,String bucketName);

    /**
     * 获取元数据
     * @param key
     * @return
     */
    Object getSimplifiedObjectMeta(String key);

}
