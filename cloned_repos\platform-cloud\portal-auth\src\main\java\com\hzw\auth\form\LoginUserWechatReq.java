package com.hzw.auth.form;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class LoginUserWechatReq {
    /**
     * 手机号
     */
  //  @NotBlank(message = "{user.phonenumber.not.blank}")
    private String mobile;

    /**
     * 短信验证码
     */
    //@NotBlank(message = "{sms.code.not.blank}")
    private String smsCode;

    /**
     * 微信code
     */

    private String code;


    /**
     * 身份证号
     */
    private String idNo;

    private String preIdNo;

    /**
     * openid
     */
    private String openId;

    /**
     * 姓名
     */
    private String truename;

    /**
     *
     */
    private String sex;

    /**
     * 年龄
     */
    private String age;

    /**
     * 头像
     */
    private String headimage;

    /**
     * 推荐码
     */
    private String recommender;

}
