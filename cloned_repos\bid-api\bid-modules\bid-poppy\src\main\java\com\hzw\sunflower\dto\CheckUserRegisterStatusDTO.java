package com.hzw.sunflower.dto;

import com.hzw.sunflower.constant.constantenum.EnterSourceEnum;
import com.hzw.sunflower.constant.constantenum.UserStatusEnum;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 用户注册状态数据
 *
 * <AUTHOR>
 * @version 1.0.0 2021-05-13
 */
@ApiModel(description = "用戶验证数据")
@Data
@EqualsAndHashCode
public class CheckUserRegisterStatusDTO {
    /**
     * 用户id
     */
    private Long userId;

    /**
     * 身份标识（1招标人2.代理机构3投标人/供应商）
     */
    private EnterSourceEnum enterSource;

    /**
     * 审核流程状态
     */
    private UserStatusEnum userStatus;

    /**
     * 关联企业id
     */
    private Long companyId;
}
