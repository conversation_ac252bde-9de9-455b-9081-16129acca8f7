package com.hzw.sunflower.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hzw.sunflower.common.BaseBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 保证金退还申请表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-27
 */
@Data
@TableName("t_bond_apply_refund")
@ApiModel(value = "BondWater对象", description = "保证金退还申请表")
public class BondApplyRefund extends BaseBean implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "流水id")
    private Long waterId;

    @ApiModelProperty(value = "保证金退还金额")
    private BigDecimal refundMoney;

    @ApiModelProperty(value = "状态  1：待处理  2：已处理待确认 3：无处室认领  4：处室不同意  5：处室同意  6：已退回（运管处）")
    private Integer status;

    @ApiModelProperty(value = "处理方式：1：指定处室处长确认  2：所有处室处长确认")
    private Integer handleType;

    @ApiModelProperty(value = "申请时间")
    private Date applyTime;

    @ApiModelProperty(value = "付款凭证")
    private String fileIds;

    @ApiModelProperty(value = "退款说明函")
    private String refundFiles;

}
