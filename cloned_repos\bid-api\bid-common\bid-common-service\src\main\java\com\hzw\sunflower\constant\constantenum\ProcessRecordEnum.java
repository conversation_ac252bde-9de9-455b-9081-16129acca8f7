package com.hzw.sunflower.constant.constantenum;


public enum ProcessRecordEnum {

    BID_WIN_BULLETIN("winBULLETIN", "公告流程code"),
    BID_WIN_PEOPLE("bidwinpeople", "确认中标人流程code"),
    BID_WIN_PEOPLE_FOR_TENDERER("bidwinpeopleForTenderer", "招标人中的确认中标人流程code"),
    BID_WIN_NOTICE("winNotice", "通知书流程code"),
    CLARIFY_REPLY_NOTICE("clarifyReply", "澄清答疑流程code"),
    OBJECTION_NOTICE("objection", "异议流程code"),
    SUPPLEMENT_BULLETIN("supplement", "补充流程code"),
    CLARIFY("clarify","澄清问题流程code"),
    RETURN_BOND("returnBond", "退还保证金流程code"),
    OBJECTIONS("objections","异议问题流程code"),
    APPLY_APPLICATION("applyApplication", "支付申请流程code"),
    ASSIST_APPLICATION("assistApplication", "协评申请流程code"),
    CH_BID_WIN_PEOPLE("CH_BIDWINPEOPLE","协评申请流程code"),
    EXCEPT_NOTICE("exceptNoticeLc","异常公告流程code"),
    BOND_APPLY_REFUND("BOND_APPLY_REFUND","保证金退还code");



    private String type;
    private String desc;

    ProcessRecordEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
