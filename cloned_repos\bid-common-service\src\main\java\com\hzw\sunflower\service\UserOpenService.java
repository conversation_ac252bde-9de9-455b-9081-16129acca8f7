package com.hzw.sunflower.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hzw.sunflower.controller.response.DepartmentInfoVo;
import com.hzw.sunflower.entity.User;

import java.util.List;


/**
 * OpenService接口
 */
public interface UserOpenService extends IService<User> {
    /**
     * 获取部门信息
     * @param userId
     * @param identity
     * @return
     */
    public DepartmentInfoVo getDeptInfoByUserId(Long userId, Integer identity);

    /**
     * 获取用户数据
     * @param phone
     * @param identity
     * @return
     */
    User findUserWechatByPhone(Integer identity, String phone);

    /**
     * 根据工作流的用户标识 查询部门信息
     * @param otherUserId
     * @return
     */
    List<DepartmentInfoVo> getDepartInfoByOtherUserId(String otherUserId);

    /**
     * 根据工作流的用户标识 查询用户信息
     * @param userCode
     * @return
     */
    User getByOtherId(String userCode);
}