package com.hzw.sunflower.controller.responese;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @ClassName:BidDocCompileListVO
 * @Auther: lijinxin
 * @Description: 文件编制列表
 * @Date: 2024/6/28 10:25
 * @Version: v1.0
 */
@Data
public class BidDocCompileListVO implements Serializable {

    @ApiModelProperty("模版id")
    private Long id;

    @ApiModelProperty("模版id")
    private Long  templateId;

    @ApiModelProperty("状态：1编辑中，2已完成")
    private Integer status;

    @ApiModelProperty("创建时间")
    private Date createdTime;

    @ApiModelProperty("项目id")
    private Long projectId;

    @ApiModelProperty("项目名称")
    private String projectName;

    @ApiModelProperty("项目编号")
    private String projectNumber;

    @ApiModelProperty("委托编号")
    private String purchaseNumber;

    @ApiModelProperty("包号")
    private String packageNumbers;
}
