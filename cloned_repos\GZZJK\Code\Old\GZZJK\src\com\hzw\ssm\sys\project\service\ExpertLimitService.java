/*
 * 公司名称：江苏华招网信息技术有限公司
 * 版权信息：江苏华招网信息技术有限公司版权所有
 * 文件名称：ExpertLimitService.java
 * 修改时间：2021年1月29日
 * 修改人：袁辉
 * 修改内容：
 * 跟踪单号：
 * 修改单号 ：
 */

package com.hzw.ssm.sys.project.service;

import java.util.List;

import javax.xml.ws.ServiceMode;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.hzw.ssm.sys.project.dao.DebarbMapper;
import com.hzw.ssm.sys.project.dao.ExpertLimitMapper;
import com.hzw.ssm.sys.project.entity.ExpertLimit;

/**
 * <一句话功能简述> TODO
 * <功能详细描述>
 * <AUTHOR>
 * @version V1.0[产品/模块版本]
 */
@Service
public class ExpertLimitService {
	/* ******************************************************************************************* */
	/*                                       序列化代码区域                                                                                                             */
	/* ******************************************************************************************* */
	@Autowired
	private ExpertLimitMapper expertLimitMapper;
	
	/* ******************************************************************************************* */
	/*                                       全局变量声明区域                                                                                                         */
	/* ******************************************************************************************* */

	public List<ExpertLimit> queryExpertByDepartment(ExpertLimit expertlimit){
		return expertLimitMapper.queryExpertByDepartment(expertlimit);
	}
	/* ******************************************************************************************* */
	/*                                       公共函数声明区域                                                                                                         */
	/* ******************************************************************************************* */

	/* ******************************************************************************************* */
	/*                                       私有函数声明区域                                                                                                         */
	/* ******************************************************************************************* */
}
