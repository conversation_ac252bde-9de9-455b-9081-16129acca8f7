package com.hzw.sunflower.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hzw.sunflower.common.BaseController;
import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.constant.MessageConstants;
import com.hzw.sunflower.constant.constantenum.ReviewFileTypeEnum;
import com.hzw.sunflower.controller.request.ReviewSignReq;
import com.hzw.sunflower.entity.OssData;
import com.hzw.sunflower.entity.OssFile;
import com.hzw.sunflower.entity.ReviewSign;
import com.hzw.sunflower.service.ReviewSignService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;


/**
 * <AUTHOR>
 * @date ：Created in 2023/9/11 10:05
 * @description：专家签署服务
 * @version: 1.0
 */
@Api(tags = "专家签署服务")
@RestController
@RequestMapping("/reviewSign")
public class ReviewSignController extends BaseController {

    @Autowired
    private ReviewSignService reviewSignService;

    @ApiOperation(value = "详细评审签章")
//    @ApiImplicitParam(name = "req", value = "评审文件签章请求信息 ", required = true, dataType = "ReviewSignReq", paramType = "body")
    @PostMapping("/signForReview")
    public Result<Boolean> signForReview(MultipartFile file, ReviewSignReq req) {
        if (null == req) {
            return Result.failed(MessageConstants.PARAMS_NOT_NULL);
        }
        ReviewSign reviewSign = reviewSignService.getOne(new LambdaQueryWrapper<ReviewSign>().eq(ReviewSign::getReviewId, req.getReviewId())
                .eq(ReviewSign::getSectionId, req.getSectionId()).eq(ReviewSign::getUserId, req.getUserId())
                .eq(ReviewSign::getFileType, req.getFileType()));
        if (reviewSign != null) {
            return Result.failed("已签章，请勿重复操作！");
        }
        return Result.okOrFailed(reviewSignService.signForReview(req, file));
    }

    @ApiOperation(value = "评审报告签章")
//    @ApiImplicitParam(name = "req", value = "评审报告签章请求信息 ", required = true, dataType = "ReviewSignReq", paramType = "body")
    @PostMapping("/signForReport")
    public Result<Boolean> signForReport(MultipartFile file, ReviewSignReq req) {
        if (null == req) {
            return Result.failed(MessageConstants.PARAMS_NOT_NULL);
        }
        return Result.okOrFailed(reviewSignService.signForReport(req, file));
    }

    @ApiOperation(value = "签章文件上传")
    @PostMapping("/uploadSignFile")
    public Result<Long> uploadSignFile(MultipartFile file) {
        if (null == file) {
            return Result.failed(MessageConstants.PARAMS_NOT_NULL);
        }
        return Result.ok(reviewSignService.uploadSignFile(file));
    }

    @ApiOperation(value = "获取oss文件id")
    @GetMapping("/getOssFile/{fileOssId}")
    public void getOssFile(@PathVariable Long fileOssId, HttpServletResponse response) {
        reviewSignService.getOssFile(fileOssId, response);
    }

    @ApiOperation(value = "获取签章进度")
    @PostMapping("/getSignProcess")
    public Result<OssFile> getSignProcess(@RequestBody ReviewSign sign) {
        if(null == sign.getReviewId() || null == sign.getFileType()){
            return Result.failed("缺失必填参数！");
        }
        return reviewSignService.getSignProcess(sign.getReviewId(),sign.getFileType());
    }

    @ApiOperation(value = "获取签章进度")
    @PostMapping("/querySignFile")
    public Result<Long> querySignFile(@RequestBody ReviewSign sign) {
        return reviewSignService.querySignFile(sign.getReviewId(),sign.getFileType());
    }

}
