package com.hzw.ssm.fw.listener.action;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;


public class ComDischarged  {



   public static Boolean  ueditor ( ServletRequest req, ServletResponse res,
                                    FilterChain chain) throws IOException, ServletException {
       HttpServletRequest request = (HttpServletRequest) req;
       HttpServletResponse response = (HttpServletResponse) res;
       String url = request.getRequestURI();
       if (url.contains("ueditor")) {
           return true;
       }else {
           return false;
       }
   }

}
