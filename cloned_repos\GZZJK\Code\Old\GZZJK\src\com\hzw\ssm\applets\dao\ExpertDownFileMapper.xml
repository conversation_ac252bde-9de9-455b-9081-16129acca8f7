<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hzw.ssm.applets.dao.ExpertDownFileMapper">

    <resultMap id="BaseResultMap" type="ExpertDownFile">
        <id column="id" property="id"/>
        <result column="expert_id" property="expertId"/>
        <result column="file_name" property="fileName"/>
        <result column="file_org_name" property="fileOrgName"/>
        <result column="file_size" property="fileSize"/>
        <result column="delete_flag" property="deleteFlag"/>
        <result column="createTime" property="createTime"/>
    </resultMap>

    <sql id="Base_Column_List">
		 t.id, t.expert_id, t.file_name, t.file_org_name, t.file_size, t.delete_flag, t.create_time
	</sql>

    <insert id="insertExpertDownFile" parameterType="ExpertDownFile">
		insert into t_expert_down_file (
			id,
			expert_id,
			file_name,
			file_org_name,
			file_size,
			delete_flag,
			create_time,
			file_type
		)
		values(
			#{id},
			#{expertId},
			#{fileName},
			#{fileOrgName},
			#{fileSize},
			#{deleteFlag,jdbcType=NUMERIC},
			#{createTime},
			#{fileType}
		)
	</insert>

    <select id="queryPage" parameterType="ExpertDownFile" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List"/>
        from
            t_expert_down_file t
        where
            expert_Id = #{expertId}
            <if test="fileType != null and !&quot;&quot;.equals(fileType)">
                and file_type = #{fileType}
            </if>
            AND delete_flag = 0
        order by
      			t.create_time desc
    </select>

    <update id="deleteExpertDownFile" parameterType="ExpertDownFile">
		UPDATE
			t_expert_down_file
		SET
			delete_flag = 1
		where
			id = #{id}
	</update>

	<select id="queryByExpertIdAndFileId" resultType="ExpertDownFile">
		select
			<include refid="Base_Column_List"/>
		from
			t_expert_down_file t
		where
			delete_flag = 0
			and expert_Id = #{expertId}
			and file_name = #{fileId}
	</select>
</mapper>
