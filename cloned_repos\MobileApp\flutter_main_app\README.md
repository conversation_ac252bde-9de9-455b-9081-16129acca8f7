## FlutterApp 项目说明

1. 开发规范

变量命名

## SDK 版本说明

****** 2019-11-07 以前 ******

* flutter channel - stable
* flutter - v1.9.1+hotfix.5
* dart - v2.5.0

build_runner: ^1.7.2 需要 dart 版本 >= 2.6.0，所以，flutter 切换到 dev channel，升版本至 dart - v2.6.0

****** 2019-11-07 ******

* flutter channel - dev
* flutter - v1.10.15
* dart - v2.6.0

flutter_network_plugin by Lokie 需要 stable channel 的 flutter，所以，flutter 切回到 stable channel，降版本至稳定版最高，同时手动更改 build_runner 版本为 ^1.6.2

****** 2019-11-10 ******

* flutter channel - stable
* flutter - v1.9.1+hotfix.6
* dart - v2.5.0

****** 2019-12-12 ******

* flutter channel - stable
* flutter - v1.12.13+hotfix.5
* dart - v2.7.0

恢复 build_runner 版本至 ^1.7.2

## 依赖包说明

// 已解决
每次执行 `flutter pub get` 以后，flutter 项目下的 `.android` 和 `.ios` 包都会重载，所有手动设置均会被覆盖，需要重新设置。

`flutter make-host-app-editable` 命令可配置

### camera

模拟器跑不了，安卓真机可以（MI6，Android 9）

需要设置 Project/flutterapp/.android/app/build.gradle 的 minSdkVersion 为 21

Project/flutterapp/.ios/Runner/Info.plist 添加：

```swift
<key>NSPhotoLibraryUsageDescription</key>
<string>Can I use the photo please?</string>
<key>NSCameraUsageDescription</key>
<string>Can I use the camera please?</string>
<key>NSMicrophoneUsageDescription</key>
<string>Can I use the mic please?</string>
```

### image_picker

IOS 设置同上
