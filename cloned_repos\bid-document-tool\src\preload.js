const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

contextBridge.exposeInMainWorld('electronAPI', {
  openFileDialog: () => ipcRenderer.invoke('open-file-dialog'),
  uploadFile: (filePath) => ipcRenderer.invoke('upload-file', filePath),
  readFile: (filePath) => ipcRenderer.invoke('read-file', filePath),
  unzipFile: (filePath) => ipcRenderer.invoke('unzip-file', filePath),
  generateDocx: (htmlContent) => ipcRenderer.invoke('generate-docx', htmlContent),
  generatePdf: (htmlContent) => ipcRenderer.invoke('generate-pdf', htmlContent),
});
