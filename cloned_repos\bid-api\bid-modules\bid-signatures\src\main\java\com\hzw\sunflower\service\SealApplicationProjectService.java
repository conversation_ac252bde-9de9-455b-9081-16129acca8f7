package com.hzw.sunflower.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hzw.sunflower.dto.SealApplicationProjectDTO;
import com.hzw.sunflower.entity.SealApplicationProject;
import com.hzw.sunflower.entity.condition.SealApplicationProjectCondition;

import java.util.List;


/**
* SealApplicationProjectService接口
*
*/
public interface SealApplicationProjectService extends IService<SealApplicationProject> {
    /**
    * 根据条件分页查询列表
    *
    * @param condition 查询条件
    * @return 分页信息
    */
    IPage<SealApplicationProject> findInfoByCondition(SealApplicationProjectCondition condition);

    /**
    * 根据主键ID查询信息
    *
    * @param id 主键ID
    * @return SealApplicationProject信息
    */
    SealApplicationProject getInfoById(Long id);

    /**
    * 新增
    *
    * @param sealApplicationProject
    * @return 是否成功
    */
    Boolean addInfo(SealApplicationProjectDTO sealApplicationProject);

    /**
    * 修改单位公司 信息
    *
    * @param sealApplicationProject
    * @return 是否成功
    */
    Boolean updateInfo(SealApplicationProjectDTO sealApplicationProject);

    /**
    * 根据主键ID删除
    *
    * @param id 主键ID
    * @return 是否成功
    */
    Boolean deleteById(Long id);

    /**
    * 根据主键ID列表批量删除
    *
    * @param idList 主键ID列表
    * @return 是否成功
    */
    Boolean deleteByIds(List<Long> idList);

}