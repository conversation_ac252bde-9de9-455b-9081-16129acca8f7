<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hzw.ssm.log.dao.WebServiceLogMapper">
	<!-- 初次插入日志 -->
	<insert id="insertLog" parameterType="WebServiceLogEntity">
		INSERT INTO T_SERVICE_LOG(
			SERVICE_ID,
			SERVICE_TYPE,
			SERVICE_IN_XML,
			CREATE_TIME
		) VALUES (#{serviceId},#{serviceType},#{serviceInXml},#{createTime})
	</insert>
	
	<!-- 补充日志的响应信息 -->
	<update id="updateLog" parameterType="WebServiceLogEntity">
		UPDATE T_SERVICE_LOG 
		SET SERVICE_CID=#{serviceCid},SERVICE_OUT_XML=#{serviceOutXml},UPDATE_TIME=#{updateTime}
		WHERE SERVICE_ID=#{serviceId}
	</update>

</mapper>