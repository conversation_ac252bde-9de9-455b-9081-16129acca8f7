///***
/// * 主题相关常量
/// */

import 'package:flutter/material.dart';

///***
/// * 基本设置
/// */

/// * 通常的文字字号
const double themeFontSize = 13.0;

/// * 通常的文字颜色
const Color themeTextColor = Color(0xff414141);

/// * 占位符文字颜色
const Color themeHintTextColor = Color(0xffababab);

/// * 高亮颜色
const Color themeActiveColor = Color(0xffdd3333);

/// * 标题颜色
const Color themeTitleColor = Color(0xff333333);

/// * 提示信息文字颜色
const Color themeTipsTextColor = Color(0xff717171);

/// * 页面整体背景颜色
const Color themeBackgroundColor = Color(0xfff5f5f5);

/// * 显示内容的背景颜色
const Color themeContentBackgroundColor = Colors.white;

/// * 边框宽度
const double themeBorderWidth = 1.0;

/// * 边框颜色
const Color themeBorderColor = Color(0xffececec);

/// * 通常圆角尺寸
const double themeBorderRadius = 2.0;

///***
/// * 文字样式设置
/// */

/// * 通常的文字样式
const TextStyle themeTextStyle = TextStyle(
  fontSize: themeFontSize,
  color: themeTextColor,
);

/// * 占位符文字样式
const TextStyle themeHintTextStyle = TextStyle(
  fontSize: themeFontSize,
  color: themeHintTextColor,
);

/// * 高亮的文字样式
const TextStyle themeActiveTextStyle = TextStyle(
  fontSize: themeFontSize,
  color: themeActiveColor,
);

/// * 标题的文字样式
const TextStyle themeTitleTextStyle = TextStyle(
  fontSize: themeFontSize,
  color: themeTitleColor,
);

/// * 提示信息文字样式
const TextStyle themeTipsTextStyle = TextStyle(
  fontSize: themeFontSize,
  color: themeTipsTextColor,
);

///***
/// * 边框设置
/// */

/// * 通常的边框样式
const BorderSide themeBorderSide = BorderSide(
  width: themeBorderWidth,
  color: themeBorderColor,
);
