package com.hzw.ssm.sys.project.action;

import java.io.IOException;
import java.io.PrintWriter;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.text.SimpleDateFormat;
import java.util.*;

import javax.servlet.http.HttpSession;

import net.sf.json.JSONArray;
import net.sf.json.JsonConfig;
import net.sf.json.util.PropertyFilter;

import org.apache.commons.lang3.StringUtils;
import org.apache.poi.util.StringUtil;
import org.apache.struts2.convention.annotation.Action;
import org.apache.struts2.convention.annotation.Namespace;
import org.apache.struts2.convention.annotation.ParentPackage;
import org.apache.struts2.convention.annotation.Result;
import org.apache.struts2.convention.annotation.Results;
import org.springframework.beans.factory.annotation.Autowired;

import com.hzw.ssm.expert.entity.Appraise;
import com.hzw.ssm.expert.entity.AppraiseInfo;
import com.hzw.ssm.expert.entity.AppraiseRemark;
import com.hzw.ssm.expert.entity.ExpertInfoEntity;
import com.hzw.ssm.expert.entity.SpecialtyInfoEntity;
import com.hzw.ssm.expert.service.AppraiseService;
import com.hzw.ssm.expert.service.ExpertInfoService;
import com.hzw.ssm.fw.base.BaseAction;
import com.hzw.ssm.fw.util.CommUtil;
import com.hzw.ssm.fw.util.DateUtil;
import com.hzw.ssm.fw.util.HZWException;
import com.hzw.ssm.fw.util.MessageConstants;
import com.hzw.ssm.fw.util.SysConstants;
import com.hzw.ssm.sys.project.entity.ApplyRecordEntity;
import com.hzw.ssm.sys.project.entity.ConditionEntity;
import com.hzw.ssm.sys.project.entity.DeleteRecord;
import com.hzw.ssm.sys.project.entity.ExtractRecordEntity;
import com.hzw.ssm.sys.project.entity.LoginRecord;
import com.hzw.ssm.sys.project.entity.ProjectAuditEntity;
import com.hzw.ssm.sys.project.entity.ProjectEntity;
import com.hzw.ssm.sys.project.entity.ResultEntity;
import com.hzw.ssm.sys.project.service.ProjectService;
import com.hzw.ssm.sys.sms.SMSNewUtil;
import com.hzw.ssm.sys.system.entity.SmsRecordEntity;
import com.hzw.ssm.sys.system.service.SmsRecordService;
import com.hzw.ssm.sys.user.entity.UserEntity;
import com.hzw.ssm.sys.user.service.UserService;

@Namespace("/projectDeleteRecord")
@ParentPackage(value = "default")
@Results( { @Result(name = "init", location = "/jsp/project/addProject.jsp"),
		@Result(name = "apply", location = "/jsp/project/applyPointToExpertsProManager.jsp"),
		@Result(name = "applyAgain", location = "/jsp/project/applyAgainExtract.jsp"),
		@Result(name = "majorStr", location = "/jsp/project/majorList.jsp"),
		@Result(name = "selectManager", location = "/jsp/project/selectManager.jsp"),
		@Result(name = "initList", location = "/jsp/project/projectInitList.jsp"),
		@Result(name = "initDeleteList", location = "/jsp/project/projectDeleteInitList.jsp"),
		@Result(name = "projectDetail", location = "/jsp/project/showProjectDetail.jsp"),
		@Result(name = "toProjectAppointDetail", location = "/jsp/project/projectAppointDetail.jsp"),
		@Result(name = "toProjectAlreadyDetail", location = "/jsp/project/projectDetail.jsp"),
		@Result(name = "selectedRecords", location = "/jsp/project/selectedRecords.jsp"),
		@Result(name = "toAppraiseDetail", location = "/jsp/expert/appraiseDetail.jsp"),
		@Result(name = "pointTo", location = "/jsp/project/pointToExperts.jsp"),
		@Result(name = "projectAuditReason", location = "/jsp/project/projectAuditReason.jsp"),
		@Result(name = "loginRecordList", location = "/jsp/project/loginManage.jsp")})
public class ProjectDeleteRecordAction  extends BaseAction{
	private String isSave;//是否为保存
	private List<ExpertInfoEntity> expertList;//专家信息列表
	private ProjectEntity project;
	private ProjectEntity projectEntity;
	private ProjectAuditEntity projectAuditEntity;
	private ResultEntity result;
	private ApplyRecordEntity apply;
	private List<ResultEntity> resultList;//专家抽取结果列表
	private List<List<ResultEntity>> extractList;//不同轮次抽取结果集
	private ConditionEntity conEntity;
	private ExpertInfoEntity expert;
	private List<ExpertInfoEntity> extractExpertList;//已抽取用于指定专家页面展示
	private String majorStr;//专业选择
	private String expertType;         // 以选中的评标专业
	private String message;//
	List<UserEntity> userList;
	private List<UserEntity> operatorList;//经办人列表
	private List<ProjectEntity> projectList;//专家抽取结果列表
	private List<ConditionEntity> conditionList;//专家抽取结果列表
	private LoginRecord loginRecord;
	private String extractResultId;
	
	private ExpertInfoEntity expertInfoEntity;
	
	/** 评分项 */
	private List<AppraiseInfo> appInfoList;
	
	private List<Appraise> appraiseList;
	
	/** 专家评价备注 */
	private AppraiseRemark appraiseRemark;
	
	/** 指定专家列表 */
	private List<ExpertInfoEntity> expertInfoList;

	/** 抽取记录 */
	private List<ExtractRecordEntity> exRecordList;
	
	/** 本地专家人数by条件 */
	private Integer localExpertCount;
	/** 国家级专家人数by条件 */
	private Integer seniorExpertCount;
	/** 删除记录原因*/
	private String reason;
	/** 删除记录恢复原因*/
	private String restoreReason;
	
	private String lastStep;
	
	private String flag;
	
	private List<DeleteRecord> deleteRecordList;
	private List<LoginRecord> loginRecordList;
	@Autowired
	private ExpertInfoService expertInfoService;
	
	@Autowired
	private ProjectService projectService;
	
	@Autowired
	private AppraiseService appraiseService;
	
	@Autowired
	private UserService userService;

	@Autowired
	private SmsRecordService smsRecordService;
	
	@Action("init")
	public String init() {
		this.context();
		UserEntity user = null;
		if (null == project)
		{
			user = (UserEntity)this.getRequest().getSession().getAttribute("userInfo");
			project = new ProjectEntity();
			// 处室
			project.setDepartment(user.getDepartment());
			// 项目负责人
			project.setManager(user.getUser_id());
			project.setManagerName(user.getUser_name());
			// 经办人
			project.setOperator(user.getUser_id());
			project.setOperatorName(user.getUser_name());
		}
		else
		{
			user = new UserEntity();
			user.setDepartment(project.getDepartment());
		}
		//查询项目负责人
		userList=projectService.queryProjectManager(user);
		// 查询项目经办人
		/*UserEntity user = new UserEntity();
		user.setRole_name(SysConstants.ROLE_NAME.OPERATOR);
		operatorList = projectService.queryUserByRole(user);*/
		
		return "init";
	}
	
	/**
	 * 查询项目负责人
	 * @return
	 */
	@Action("projectManagerList")
	public String projectManagerList()
	{
		this.context();
		this.getResponse().setContentType("text/html;charset=UTF-8");
		StringBuffer result = new StringBuffer("[");
		UserEntity user = new UserEntity();
		user.setDepartment(project.getDepartment());
		userList=projectService.queryProjectManager(user);
		for (int i = 0; i < userList.size(); i++) 
		{
			user = userList.get(i);
			if (0 < i)
			{
				result.append(",");
			}
			result.append("{\"user_id\":\"").append(user.getUser_id()).append("\", \"user_name\":\"").append(user.getUser_name()).append("\"}");
		}
		result.append("]");
		try {
			this.getResponse().getWriter().write(result.toString());
		} catch (IOException e) {
			e.printStackTrace();
		}
		return null;
	}
	
	/**
	 * 专家抽取申请项目列表
	 * @return
	 */
	@Action("initList")
	public String initList() {
		if(project==null){
			project=new ProjectEntity();
		}
		this.context();
		project.setPage(this.getPage());
		UserEntity user=(UserEntity)this.getRequest().getSession().getAttribute("userInfo");
		project.setCreate_id(user.getUser_id());
		Long status = project.getStatus();
		if (null != status && 3L == status)
		{
			project.setStatus(null);
			project.setStatus_("3, 10");
		}
		projectList = projectService.queryProjectList(project);
		project.setStatus(status);
		return "initList";
	}
	/**
	 * 查询项目明细
	 * 
	 * @return
	 */
	@Action("projectDetail")
	public String toProjectDetail() {
		Long method = project.getMethod();
		String returnStr = "projectDetail";
		// -2 标注为项目详细信息
		projectList = projectService.queryDeleteProjectByDecimationbatch(project);
		project = projectList.get(0);
		if (null != method && -1L == method)
		{
			// -1L 标注为跳转到申请指定专家页面
			return "apply";
		}
		
		if (null == conEntity){
			conEntity = new ConditionEntity();
		}
		conEntity.setProjectId(project.getProjectId());
		conEntity=projectService.queryConditionById(conEntity);
		
		// 空 标注为项目指定专家详细信息页面
		if (null == method || 4L == method)
		{
			projectEntity = project;
			ConditionEntity conditionEntity = new ConditionEntity();
			conditionEntity.setProjectId(project.getProjectId());
			conditionEntity.setJoin_status(0L);     // 参标
			expertInfoList = projectService.queryExtractedResult(conditionEntity);
			return "toProjectAppointDetail";
		}
		// 查询参评专家信息
		if (null != method && 4L != method && -2L != method) {
			projectEntity = project;
			ConditionEntity conditionEntity = new ConditionEntity();
			conditionEntity.setProjectId(project.getProjectId());
			conditionEntity.setJoin_status(0L);     // 参标
			expertInfoList = projectService.queryExtractedResult(conditionEntity);
			exRecordList = projectService.queryExtractRecordList(project.getProjectId());
			returnStr = "toProjectAlreadyDetail";
		}
		if("del".equals(flag)){
			flag="del";
		}
		lastStep = "1";
		return returnStr;

	}
	
	/**
	 * 查询项目明细，确认抽取结果
	 * 
	 * @return
	 */
	@Action("selectedRecords")
	public String selectedRecords() {
		Long method = project.getMethod();
		project = projectService.queryProjectById(project);
		if (null == conEntity){
			conEntity = new ConditionEntity();
		}
		conEntity.setProjectId(project.getProjectId());
		conEntity=projectService.queryConditionById(conEntity);

		projectEntity = project;
		ConditionEntity conditionEntity = new ConditionEntity();
		conditionEntity.setProjectId(project.getProjectId());
		conditionEntity.setJoin_status(0L);     // 参标
		expertInfoList = projectService.queryExtractedResult(conditionEntity);
		// 空 标注为项目指定专家详细信息页面
		if (null == method || 4L == method)
		{
			return "toProjectAppointDetail";
		}
		
		exRecordList = projectService.queryExtractRecordList(project.getProjectId());
		if(exRecordList == null || exRecordList.isEmpty()){
			localExpertCount = projectService.queryExpertCountByRule(conditionEntity, 1);
			seniorExpertCount = projectService.queryExpertCountByRule(conditionEntity, 2);
		}
		SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		if("2".equals(conEntity.getMethod())){// 短信抽取
			//conEntity.setSmsStr("您好：XXX，江苏省招标中心有限公司诚邀您参加【"+project.getProjectName()+"】评标，开标时间："+sdf.format(project.getBidTime())+"。回复：1-参加，2-不参加。");
			conEntity.setSmsStr("您好：XXX，江苏省招标中心有限公司诚邀您参加评标，开标时间："+sdf.format(project.getBidTime())+"，开标地点："+project.getBidAddress()+"，项目负责人："+project.getManager()+"，电话："+project.getPhone()+"。回复：1-参加，2-不参加。");
		}else{// 正常抽取
			//conEntity.setSmsStr("您好：XXX，江苏省招标中心有限公司诚邀您参加【"+project.getProjectName()+"】评标，开标时间："+sdf.format(project.getBidTime())+",开标地点："+project.getBidAddress()+",项目负责人："+project.getManager()+",电话："+project.getPhone()+"");
			conEntity.setSmsStr("您好：XXX，江苏省招标中心有限公司诚邀您参加评标，开标时间："+sdf.format(project.getBidTime())+"，开标地点："+project.getBidAddress()+"，项目负责人："+project.getManager()+"，电话："+project.getPhone());
		}
		
		return "selectedRecords";

	}
	
/*	*//**
	 * 根据抽取条件验证是否存在满足条件的专家
	 * @return
	 *//*
	@Action("valExtractionExport")
	public String valExtractionExport()
	{
		this.context();
		this.getResponse().setCharacterEncoding("utf-8");
		PrintWriter out = null;
		try {
			if (null != project.getProjectId() && !project.getProjectId().isEmpty()
					&& (null == project.getProjectName() || project.getProjectName().isEmpty()))
			{
				// 获取项目信息
				project = projectService.queryProjectById(project);
				conEntity = projectService.queryConditionById(conEntity);
			}
			out = this.getResponse().getWriter();
			String tender = "";
			if(null != project.getTender() && !"".equals(project.getTender()) && project.getTender().indexOf(",") > -1){
				String[] tenderSplit = project.getTender().split(",");
				for (int i = 0; i < tenderSplit.length; i++) {
					tender +="'"+tenderSplit[i]+"',";
				}
				tender = tender.substring(0,tender.length()-1);
			}else{
				tender = "'"+project.getTender()+"'";
			}
			conEntity.setTender(tender);
			conEntity.setBidTimeDay(DateUtil.dateToString(project.getBidTime()));
			String msg = projectService.queryExpertToCheck(conEntity);
			out.print(msg);
			out.close();
		} catch (Exception e) {
			if(e instanceof HZWException){
				out.print(e.getMessage());
			}
			else e.printStackTrace();
		}
		return null;
	}*/
	
	/**
	 * 保存项目信息
	 * @return
	 */
	@Action("saveProject")
	public String saveProject() {
		try {
			this.setOpaUserAndDate(project);
			this.setOpaUserAndDate(conEntity);
			projectService.saveProject(project,conEntity);
			this.setAlertMessage(MessageConstants.SAVE_SUCCESS);
			// 申请抽取之后向抽取人发送通知短信
			if (1 == project.getStatus().intValue())
			{
				smsExtractMobile();
			}
		} catch (Exception e) {
			e.printStackTrace();
			this.setAlertMessage(MessageConstants.SAVE_FAILED);
			return init();
		}
		project = null;
		return initList();
	}

	/**
	 * 展示修改项目信息页面
	 * 
	 * @return
	 */
	@Action("showModifyProject")
	public String showModifyProject() {
		project = projectService.queryProjectById(project);
		if (null == conEntity){
			conEntity = new ConditionEntity();
		}
		conEntity.setProjectId(project.getProjectId());
		if (null != conEntity.getId() && !conEntity.getId().isEmpty())
		{
			conEntity=projectService.queryConditionById(conEntity);
		}
		isSave="1";//更新操作
		return this.init();
	}
	/**
	 * 更新项目信息
	 * @return
	 */
	@Action("updateProject")
	public String updateProject() {
		try {
			this.setOpaUserAndDate(project);
			this.setOpaUserAndDate(conEntity);
			List<ProjectEntity> projectList = new ArrayList<ProjectEntity>();
			projectList.add(project);
			projectService.updateProjects(projectList,conEntity);
			this.setAlertMessage(1 == project.getStatus() ? MessageConstants.APPLY_SUCCESS : MessageConstants.SAVE_SUCCESS);
			// 申请抽取之后向抽取人发送通知短信
			if (1 == project.getStatus().intValue())
			{
				smsExtractMobile();
			}
			isSave="1";
		} catch (Exception e) {
			e.printStackTrace();
			this.setAlertMessage(1 == project.getStatus() ? MessageConstants.APPLY_FAILED : MessageConstants.SAVE_FAILED);
		}
		return init();
	}
	/**
	 * 更新项目状态
	 * @return
	 */
	@Action("updateProjectStatus")
	public String updateProjectStatus(){
		try {
			//project.setStatus(2L);//抽取中,即为申请成功
			projectService.updateProjectStatus(project);
			this.setAlertMessage(MessageConstants.APPLY_SUCCESS);
			// 获取项目信息
			project = projectService.queryProjectById(project);
			// 申请抽取之后向抽取人发送通知短信
			if (1 == project.getStatus().intValue())
			{
				smsExtractMobile();
			}
		} catch (Exception e) {
			e.printStackTrace();
			this.setAlertMessage(MessageConstants.APPLY_FAILED);
		}
		project=null;
		return this.initList();
	}
	
	/**
	 * 删除记录
	 * @return
	 */
	@Action("deleteRecord")
	public String deleteRecord(){
		try {
			DeleteRecord deleteRecord = new DeleteRecord();
			deleteRecord.setId(CommUtil.getKey());
			deleteRecord.setProjectId(project.getProjectId());
			deleteRecord.setReason(reason);
			projectService.deleteRecordByProjectId(project,deleteRecord);
			this.setAlertMessage(MessageConstants.DELETE_SUCCESS);
		} catch (Exception e) {
			e.printStackTrace();
			this.setAlertMessage(MessageConstants.DELETE_FAILED);
		}
		project=null;
		return this.initList();
	}
	/**
	 * 展示删除记录
	 * @return
	 */
	@Action("showDeleteRecord")
	public String showDeleteRecord(){
		if(project==null){
			project=new ProjectEntity();
		}
		this.context();
		project.setPage(this.getPage());
		UserEntity user=(UserEntity)this.getRequest().getSession().getAttribute("userInfo");
		if(!"99".equals(user.getRole_id())){
			project.setCreate_id(user.getUser_id());
		}
		Long status = project.getStatus();
		if (null != status && 3L == status)
		{
			project.setStatus(null);
			project.setStatus_("3, 10");
		}
		projectList = projectService.queryPageDeleteProjectList(project);
		project.setStatus(status);
		deleteRecordList = projectService.getDeleteRecordList();
		return "initDeleteList";
	}
	/**
	 * 展示删除原因
	 * @return
	 */
	@Action("showDeleteReason")
	public String showDeleteReason(){
		this.context();
		this.getResponse().setCharacterEncoding("utf-8");
		try {
			DeleteRecord deleteRecord = new DeleteRecord();
			deleteRecord.setProjectId(project.getProjectId());
			String reason = projectService.getDeleteReason(deleteRecord);
			this.getResponse().getWriter().write(reason);
		} catch (IOException e) {
			e.printStackTrace();
		}
		return null;
	}
	
	/**
	 * 展示恢复删除原因
	 * @return
	 */
	@Action("showRestoreDeleteReason")
	public String showRestoreDeleteReason(){
		this.context();
		this.getResponse().setCharacterEncoding("utf-8");
		try {
			DeleteRecord deleteRecord = new DeleteRecord();
			deleteRecord.setProjectId(project.getProjectId());
			String reason = projectService.getRestoreDeleteReason(deleteRecord);
			this.getResponse().getWriter().write(reason);
		} catch (IOException e) {
			e.printStackTrace();
		}
		return null;
	}
	/**
	 * 更新删除记录表为申请恢复状态
	 * @return
	 */
	@Action("modifyDeleteRecord")
	public String modifyDeleteRecord(){
		try {
			DeleteRecord deleteRecord = new DeleteRecord();
			deleteRecord.setDecimationBatch(project.getDecimationBatch());
			deleteRecord.setRestoreReason(restoreReason);
			projectService.modifyDeleteRecord(deleteRecord);
		} catch (Exception e) {
			e.printStackTrace();
		}
		project = null;
		return showDeleteRecord();
	}
	/**
	 * 跳转到设置抽取条件页面
	 * @return
	 */
	@Action("addCondition")
	public String addCondition(){
		this.setOpaUserAndDate(project);
		if("1".equals(isSave)){//已保存过，进行更新操作
			//projectService.updateProject(project);
		}else{
			//projectService.saveProject(project);
		}
		
		if(conEntity!=null&&conEntity.getId()!=null&&!"".equals(conEntity.getId())){
			conEntity=projectService.queryConditionById(conEntity);
			isSave="1";//抽取条件已保存过
		}
		else {
			conEntity=new ConditionEntity();
			conEntity.setMethod(null);
			conEntity.setProjectId(project.getProjectId());
		}
		return "condition";
	} 
	
	/**
	 * 保存抽取条件
	 * @return
	 */
	@Action("saveCondition")
	public String saveCondition(){
		try {
			this.setOpaUserAndDate(conEntity);
			projectService.saveCondition(conEntity);
			this.setAlertMessage(MessageConstants.SAVE_SUCCESS);
		} catch (Exception e) {
			e.printStackTrace();
			this.setAlertMessage(MessageConstants.SAVE_FAILED);
		}
		return "condition";
	}
	/**
	 * 更新抽取条件
	 * @return
	 */
	@Action("updateCondition")
	public String updateCondition(){
		try {
			this.setOpaUserAndDate(conEntity);
			projectService.updateCondition(conEntity);
			this.setAlertMessage(MessageConstants.SAVE_SUCCESS);
		} catch (Exception e) {
			e.printStackTrace();
			this.setAlertMessage(MessageConstants.SAVE_FAILED);
		}
		return "condition";
	}
 
	/**
	 * 回退到项目信息添加页面 
	 * @return
	 */
	@Action("backProject")
	public String backProject(){
		if(this.isCreateCondition()){//是否要保存或更新信息
			if(conEntity!=null&&conEntity.getId()!=null&&!"".equals(conEntity.getId())){//抽取条件是否已保存
				projectService.updateCondition(conEntity);//更新
			}else {
				projectService.saveCondition(conEntity);//未保存过,对已填写的内容进行保存
			}
		}
		project=new ProjectEntity();
		isSave="1";//项目信息已保存,用作信息保存
		project.setProjectId(conEntity.getProjectId());
		project=projectService.queryProjectById(project);
		return init();
	}
	
	
	/**
	 * 申请指定专家
	 * @return
	 */
	@Action("auditPointToExperts")
	public String auditPointToExperts(){
		this.setOpaUserAndDate(project);
		if("1".equals(isSave)){//已保存过，进行更新操作
			List<ProjectEntity> projectList = new ArrayList<ProjectEntity>();
			projectList.add(project);
			projectService.updateProjects(projectList,null);
		}else{
			projectService.saveProject(project,null);
		}
		return "apply";
	}
	/**
	 * 保存指定专家申请说明
	 * @return
	 */
	@Action("saveApplyInfo")
	public String saveApplyInfo(){
		this.context();
		// 当前session
		HttpSession session = getRequest().getSession();
		// 当前用户信息
		UserEntity user = (UserEntity) session.getAttribute("userInfo");
		
		this.setOpaUserAndDate(apply);
		projectService.saveApplyInfo(apply,user);
		this.context();
		try {
			//跳转到专家抽取申请页面
			this.getResponse().sendRedirect(this.getRequest().getContextPath()+"/project/initList");
		} catch (IOException e) {
			e.printStackTrace();
		}
		return null;
	}
	/**
	 * 提交指定专家
	 * @return
	 */
	@Action("submitPointResult")
	public String submitPointResult(){
		this.context();
		this.getResponse().setContentType("text/html;charset=UTF-8");
		try {
			projectService.submitPointResult(result);
			this.getResponse().getWriter().write("success");
		} catch (Exception e) {
			try {
				this.getResponse().getWriter().write("no");
			} catch (IOException e1) {
				e1.printStackTrace();
			}
			e.printStackTrace();
		}
		return null;
	}
	
	/**
	 *  查询指定专家
	 * @return
	 */
	@Action("queryPagePointExperts")
	public String queryPagePointExperts(){
		conEntity.setPage(this.getPage());
		conEntity.getPage().setShowCount(5);
		expertList=projectService.queryPagePointExperts(conEntity);
		resultList=projectService.queryPointedExperts(conEntity);
		return "pointTo";
	}
	/**
	 *  删除指定专家
	 * @return
	 */
	@Action("deletePointResult")
	public String deletePointResult(){
		projectService.deletePointResult(result);
		return null;
	}
	
	/**
	 *  保存指定专家
	 * @return
	 */
	@Action("savePointExperts")
	public String savePointExperts(){
		projectService.savePointExperts(result);
		return null;
	}
	/**
	 * 申请再次抽取（超3次后）
	 * @return
	 */
	@Action("applyAgainExtract")
	public String applyAgainExtract(){
		conEntity=projectService.queryConditionById(conEntity);
		project=new ProjectEntity();
		project.setProjectId(conEntity.getProjectId());
		project=projectService.queryProjectById(project);
		return "applyAgain";
	}
	
	/**
	 * 保存再次抽取申请说明
	 * @return
	 */
	@Action("saveApplyAgainExtract")
	public String saveApplyAgainExtract(){
		this.setOpaUserAndDate(apply);
		this.context();
		try {
			projectService.saveApplyAgainExtract(apply);
			this.setAlertMessage("信息提交成功！");
			this.getResponse().sendRedirect(this.getRequest().getContextPath()+"/waitProject/queryProjectList");
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}
	
	/**
	 * 查询所有项目负责人
	 * @return
	 */
	@Action("queryProjectManager")
	public String queryProjectManager(){
		UserEntity user = new UserEntity();
		userList=projectService.queryProjectManager(user);
		return "selectManager";
	}
	/**
	 * 查询评标专业列表
	 * @return
	 */
	@Action("queryMajorList")
	public String queryMajorList(){
		List<SpecialtyInfoEntity> specialtyInfoList=expertInfoService.querySpecialtyInfoList();
		if (null != expertType && !expertType.isEmpty())
		{
			List<String> expertTypes = Arrays.asList(expertType.split(","));
			//设置默认选择的菜单，即已选中的评标专业
			for(SpecialtyInfoEntity specialtyInfo : specialtyInfoList)
			{
				if(expertTypes.contains(specialtyInfo.getSpe_id()))
				{
					specialtyInfo.setChecked(true);
					setOpen(specialtyInfoList, specialtyInfo.getSpe_parent());
					if (3 == specialtyInfo.getSpe_level().intValue())
					{
						specialtyInfo.setOpen(true);
						setOpenNodes(specialtyInfoList, specialtyInfo.getSpe_id());
					}
				}
			}
		}
		JsonConfig config=new JsonConfig();
		//通过JsonConfig的属性过滤器去除不需要的属性
		config.setJsonPropertyFilter(new PropertyFilter() {
			@Override
			public boolean apply(Object arg0, String arg1, Object arg2) {
				if(arg1.equals("spe_id")||arg1.equals("spe_parent")||arg1.equals("spe_name")
						||arg1.equals("checked")||arg1.equals("open")||arg1.equals("spe_level")){
					return false;//不忽略
				}
				return true;
			}
		});
		JSONArray jsonArray=JSONArray.fromObject(specialtyInfoList,config);
		majorStr=jsonArray.toString();
		//将属性名称修改zTree对应的属性名称
		majorStr=majorStr.replaceAll("spe_parent","pId").replaceAll("spe_name", "name").replaceAll("spe_id", "id");
		return "majorStr";
	}
	
	/**
	 * 打开已选中的父级节点
	 * @param specialtyInfoList
	 * @param speId
	 */
	private void setOpen(List<SpecialtyInfoEntity> specialtyInfoList, String speId)
	{
		for (SpecialtyInfoEntity specialtyInfoEntity : specialtyInfoList) 
		{
			if (specialtyInfoEntity.getSpe_id().equals(speId))
			{
				specialtyInfoEntity.setOpen(true);
				specialtyInfoEntity.setChecked(true);
				if (!"-1".equals(specialtyInfoEntity.getSpe_parent()))
				{
					setOpen(specialtyInfoList, specialtyInfoEntity.getSpe_parent());
				}
				break;
			}
		}
	}
	
	/**
	 * 打开已选中的子级节点
	 * @param specialtyInfoList
	 * @param speId
	 */
	private void setOpenNodes(List<SpecialtyInfoEntity> specialtyInfoList, String speId)
	{
		for (SpecialtyInfoEntity specialtyInfoEntity : specialtyInfoList) 
		{
			if (specialtyInfoEntity.getSpe_parent().equals(speId))
			{
				specialtyInfoEntity.setOpen(true);
				specialtyInfoEntity.setChecked(true);
				if (3 > specialtyInfoEntity.getSpe_level())
				{
					setOpenNodes(specialtyInfoList, specialtyInfoEntity.getSpe_id());
				}
			}
		}
	}
	
	/**
	 * 抽取专家条件是否填写
	 * @return
	 */
	@SuppressWarnings("unchecked")
	private boolean isCreateCondition(){
		Class cl=ConditionEntity.class;
		Method[] ms =cl.getDeclaredMethods();//得到所有方法不含继承的方法
		String exclude=",getProvince,getCity,getZone,getTotal,getExpertType,getScore,getSeniorNum";//用于判断是否填写的字段.
		try {
			for(Method method:ms){
				if(method.getName().startsWith("get")&&exclude.contains(","+method.getName()+",")){
					Object obj=method.invoke(conEntity);
					if(obj!=null&&!obj.equals("")){
						return true;
					}
				}
			}
		} catch (SecurityException e) {
			e.printStackTrace();
		} catch (IllegalArgumentException e) {
			e.printStackTrace();
		} catch (IllegalAccessException e) {
			e.printStackTrace();
		} catch (InvocationTargetException e) {
			e.printStackTrace();
		}
		return false;
	}
	/**
	 * 查询项目退回原因
	 * @return
	 */
	@Action("projectReturnReason")
	public String projectReturnReason(){
		if(projectAuditEntity == null){
			projectAuditEntity = new ProjectAuditEntity();
		}
		projectAuditEntity = projectService.getProAuditInfoByProId(projectAuditEntity);
		return "projectAuditReason";
	}
	
	/**
	 * 查看专家评价信息
	 * 
	 * @return
	 * @throws Exception
	 */
	@Action("toAppraiseDetail")
	public String toAppraiseDetail() throws Exception {
		// 专家信息
		expertInfoEntity = appraiseService.queryBaseAppraise(extractResultId);
		// 查询评分项
		appInfoList = appraiseService.queryAppraiseInfoById("0");
		// 查询评分
		appraiseList = appraiseService.queryAppraiseDetail(extractResultId,"0");
		// 评价备注
		appraiseRemark = appraiseService.queryAppraiseRemark(extractResultId);
		return "toAppraiseDetail";
	}
	
	/**
	 * 批量修改抽取专家结果
	 * @return
	 */
	@Action("submitResult")
	public String submitResult()
	{
		try {
			projectService.submitResult(resultList, conEntity);
			this.setAlertMessage(MessageConstants.SAVE_SUCCESS);
		} catch (Exception e) {
			e.printStackTrace();
			this.setAlertMessage(MessageConstants.SAVE_FAILED);
		}
		return this.initList();
	}
	
	/**
	 * 向专家发送邀请短信
	 * @return
	 */
	@Action("smsExperts")
	public String smsExperts()
	{
		this.context();
		this.getResponse().setContentType("text/html;charset=UTF-8");
		try {
			if (null == conEntity)
			{
				this.getResponse().getWriter().write("fail");
				return null;
			}
			projectService.sendMessageForExperts(conEntity, expertList);
			this.getResponse().getWriter().write("success");
		} catch (Exception e) {
			if(e instanceof HZWException){
				this.setAlertMessage(e.getMessage());
			}
			e.printStackTrace();
			try {
				this.getResponse().getWriter().write("fail");
			} catch (IOException e1) {
				e1.printStackTrace();
			}
		}
		return null;
	}
	
	/**
	 * 保存成功之后向抽取人发送通知短信
	 */
	private void smsExtractMobile() {
		List<UserEntity> extractList = userService.queryExtractMobiles(SysConstants.ROLE.EXTRACT);
		if (!extractList.isEmpty())
		{
			List<SmsRecordEntity> smsRecordList = new ArrayList<SmsRecordEntity>();
			Date currentDate = new Date();
			// 获取项目创建人信息
			UserEntity createUser = userService.selectUserById(project.getCreate_id());
			List<String[]> msgList = new ArrayList<String[]>();
			//拼接短信内容
			for(UserEntity extractUser : extractList){
				String[] msg = new String[3];
				//手机号
				msg[0] = extractUser.getMobile();
				//短信内容
				msg[1] = extractUser.getUser_name() + "你好，" + createUser.getUser_name() + "申请" + project.getProjectName() + "抽取专家。【省交易中心】";
				msgList.add(msg);
				// 记录短信
				SmsRecordEntity smsRecordEntity = new SmsRecordEntity();
				smsRecordEntity.setSms_id(CommUtil.getKey());
				smsRecordEntity.setProject_id(project.getProjectId());
				smsRecordEntity.setSms_user_id(createUser.getUser_id());
				smsRecordEntity.setSms_user_name(extractUser.getUser_name());
				smsRecordEntity.setSms_content(msg[1]);
				smsRecordEntity.setSms_mobile(msg[0]);
				smsRecordEntity.setSms_type(SysConstants.SMS_TYPE.SMS_TWO);
				smsRecordEntity.setSms_time(currentDate);
				smsRecordList.add(smsRecordEntity);
			}
			// 批量添加短信记录
			smsRecordService.insertSmsRecordList(smsRecordList);
			String result_mt=SMSNewUtil.sendMsgByDiffContent(msgList);
			if(result_mt.startsWith("-")||result_mt.equals("")){//发送短信，如果是以负号开头就是发送失败。
				System.out.print("发送失败！返回值为："+result_mt+"请查看webservice返回值对照表");
			}
		}
	}
	
	/**
	 * 展示登录记录
	 * @return
	 */
	@Action("queryLoginRecord")
	public String queryLoginRecord(){
		if(loginRecord==null){
			loginRecord=new LoginRecord();
		}
		try{
		this.context();
		loginRecord.setPage(this.getPage());
		//默认查询前一天的数据
		if(StringUtils.isBlank(loginRecord.getLoginStartTime()) && StringUtils.isBlank(loginRecord.getLoginEndTime())){
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			String endTime=sdf.format(new Date());
			loginRecord.setLoginEndTime(endTime);

			Date day = sdf.parse(endTime);
			long ms = day.getTime() - 1*24*3600*1000L;
			Date prevDay = new Date(ms);
			String startTime = sdf.format(prevDay);
			loginRecord.setLoginStartTime(startTime);
		}
		loginRecordList = projectService.queryPageLoginRecordList(loginRecord);
		}catch(Exception e){
			e.printStackTrace();
		}
		return "loginRecordList";
	}

	public List<LoginRecord> getLoginRecordList() {
		return loginRecordList;
	}

	public void setLoginRecordList(List<LoginRecord> loginRecordList) {
		this.loginRecordList = loginRecordList;
	}

	public LoginRecord getLoginRecord() {
		return loginRecord;
	}

	public void setLoginRecord(LoginRecord loginRecord) {
		this.loginRecord = loginRecord;
	}

	public ProjectEntity getProject() {
		return project;
	}

	public void setProject(ProjectEntity project) {
		this.project = project;
	}

	public ProjectEntity getProjectEntity() {
		return projectEntity;
	}
	public void setProjectEntity(ProjectEntity projectEntity) {
		this.projectEntity = projectEntity;
	}
	public String getIsSave() {
		return isSave;
	}

	public void setIsSave(String isSave) {
		this.isSave = isSave;
	}

	public ConditionEntity getConEntity() {
		return conEntity;
	}

	public void setConEntity(ConditionEntity conEntity) {
		this.conEntity = conEntity;
	}

	public List<ExpertInfoEntity> getExpertList() {
		return expertList;
	}

	public void setExpertList(List<ExpertInfoEntity> expertList) {
		this.expertList = expertList;
	}

	public ResultEntity getResult() {
		return result;
	}

	public void setResult(ResultEntity result) {
		this.result = result;
	}

	public List<ResultEntity> getResultList() {
		return resultList;
	}

	public void setResultList(List<ResultEntity> resultList) {
		this.resultList = resultList;
	}

	public List<List<ResultEntity>> getExtractList() {
		return extractList;
	}

	public void setExtractList(List<List<ResultEntity>> extractList) {
		this.extractList = extractList;
	}

	public ApplyRecordEntity getApply() {
		return apply;
	}

	public void setApply(ApplyRecordEntity apply) {
		this.apply = apply;
	}

	public ProjectService getProjectService() {
		return projectService;
	}

	public void setProjectService(ProjectService projectService) {
		this.projectService = projectService;
	}

	public ExpertInfoEntity getExpert() {
		return expert;
	}

	public void setExpert(ExpertInfoEntity expert) {
		this.expert = expert;
	}

	public List<ExpertInfoEntity> getExtractExpertList() {
		return extractExpertList;
	}

	public void setExtractExpertList(List<ExpertInfoEntity> extractExpertList) {
		this.extractExpertList = extractExpertList;
	}

	public String getMajorStr() {
		return majorStr;
	}

	public void setMajorStr(String majorStr) {
		this.majorStr = majorStr;
	}

	public String getExpertType() {
		return expertType;
	}
	
	public void setExpertType(String expertType) {
		this.expertType = expertType;
	}
	
	public String getMessage() {
		return message;
	}

	public void setMessage(String message) {
		this.message = message;
	}

	public List<UserEntity> getUserList() {
		return userList;
	}

	public void setUserList(List<UserEntity> userList) {
		this.userList = userList;
	}

	public List<UserEntity> getOperatorList() {
		return operatorList;
	}

	public void setOperatorList(List<UserEntity> operatorList) {
		this.operatorList = operatorList;
	}
	public List<ProjectEntity> getProjectList() {
		return projectList;
	}
	public void setProjectList(List<ProjectEntity> projectList) {
		this.projectList = projectList;
	}
	public List<ConditionEntity> getConditionList() {
		return conditionList;
	}
	public void setConditionList(List<ConditionEntity> conditionList) {
		this.conditionList = conditionList;
	}
	public List<ExpertInfoEntity> getExpertInfoList() {
		return expertInfoList;
	}
	public void setExpertInfoList(List<ExpertInfoEntity> expertInfoList) {
		this.expertInfoList = expertInfoList;
	}
	public List<ExtractRecordEntity> getExRecordList() {
		return exRecordList;
	}
	public void setExRecordList(List<ExtractRecordEntity> exRecordList) {
		this.exRecordList = exRecordList;
	}
	public ProjectAuditEntity getProjectAuditEntity() {
		return projectAuditEntity;
	}
	public void setProjectAuditEntity(ProjectAuditEntity projectAuditEntity) {
		this.projectAuditEntity = projectAuditEntity;
	}
	public String getExtractResultId() {
		return extractResultId;
	}
	public void setExtractResultId(String extractResultId) {
		this.extractResultId = extractResultId;
	}
	public ExpertInfoEntity getExpertInfoEntity() {
		return expertInfoEntity;
	}
	public void setExpertInfoEntity(ExpertInfoEntity expertInfoEntity) {
		this.expertInfoEntity = expertInfoEntity;
	}
	public List<AppraiseInfo> getAppInfoList() {
		return appInfoList;
	}
	public void setAppInfoList(List<AppraiseInfo> appInfoList) {
		this.appInfoList = appInfoList;
	}
	public List<Appraise> getAppraiseList() {
		return appraiseList;
	}
	public void setAppraiseList(List<Appraise> appraiseList) {
		this.appraiseList = appraiseList;
	}
	public AppraiseRemark getAppraiseRemark() {
		return appraiseRemark;
	}
	public void setAppraiseRemark(AppraiseRemark appraiseRemark) {
		this.appraiseRemark = appraiseRemark;
	}

	public Integer getLocalExpertCount() {
		return localExpertCount;
	}

	public void setLocalExpertCount(Integer localExpertCount) {
		this.localExpertCount = localExpertCount;
	}

	public Integer getSeniorExpertCount() {
		return seniorExpertCount;
	}

	public void setSeniorExpertCount(Integer seniorExpertCount) {
		this.seniorExpertCount = seniorExpertCount;
	}

	public String getReason() {
		return reason;
	}

	public void setReason(String reason) {
		this.reason = reason;
	}

	public String getRestoreReason() {
		return restoreReason;
	}

	public void setRestoreReason(String restoreReason) {
		this.restoreReason = restoreReason;
	}

	public List<DeleteRecord> getDeleteRecordList() {
		return deleteRecordList;
	}

	public void setDeleteRecordList(List<DeleteRecord> deleteRecordList) {
		this.deleteRecordList = deleteRecordList;
	}

	public String getLastStep() {
		return lastStep;
	}

	public void setLastStep(String lastStep) {
		this.lastStep = lastStep;
	}

	public String getFlag() {
		return flag;
	}

	public void setFlag(String flag) {
		this.flag = flag;
	}
}
