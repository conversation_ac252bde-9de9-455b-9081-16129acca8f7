<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzw.sunflower.dao.BondRetainRecordMapper">

   <select id="getCountRateByRefundId" resultType="java.math.BigDecimal">
       select ( CASE WHEN sum( rates ) IS NULL THEN 0 ELSE sum( rates ) END ) AS total  from t_bond_retain_record where refund_id = #{refundId} and is_delete = 0
   </select>

</mapper>
