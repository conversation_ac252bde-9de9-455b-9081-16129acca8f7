///***
/// * 所有 API 的具体实现
/// */

import 'dart:convert' as convert;

import 'package:flutter_main_app/src/utils/HttpUtils.dart';

import 'package:flutter_main_app/src/models/common/HttpResponseDataListModel.dart';

import 'package:flutter_main_app/src/models/bulletin/BulletinListQueryModel.dart';
import 'package:flutter_main_app/src/models/bulletin/BulletinListItemModel.dart';

import 'package:flutter_main_app/src/models/marketPlayer/SubjectBasicModel.dart';
import 'package:flutter_main_app/src/models/marketPlayer/SubjectPerformanceModel.dart';
import 'package:flutter_main_app/src/models/marketPlayer/SubjectQualificationModel.dart';
import 'package:flutter_main_app/src/models/marketPlayer/SubjectStaffModel.dart';

import 'package:flutter_main_app/src/models/dataReport/DataReportModel.dart';

const String IP_WR = 'http://************';

const String IP = IP_WR;

const String testAPI = 'http://testapi.jszbtb.com/';

// const String _supplierDomain = '$IP:8084/';
const String _supplierDomain = testAPI;

// const String _bulletinDomain = '$IP:8080/';
const String _bulletinDomain = testAPI;

class ApiUtils {

  ///************************************* 公告公示相关 API *************************************/

  ///***
  /// * 获取 公告公示列表
  /// *
  /// * @param {BulletinListQueryModel} params
  /// *
  /// * @return {Future<HttpResponseDataListModel<BulletinListItemModel>>}
  /// */
  static Future<HttpResponseDataListModel<BulletinListItemModel>> postBulletin(
    BulletinListQueryModel params,
  ) async {
    const String url = 'BulletinSearchApi/Bulletin';

    final HttpResponseDataListModel raw =
      await HttpUtils.postJson<HttpResponseDataListModel>(
        url,
        json: convert.json.encode(params.toJson()),
        domainName: _bulletinDomain,
      );

    HttpResponseDataListModel<BulletinListItemModel> result;

    if (raw != null) {
      result = HttpResponseDataListModel<BulletinListItemModel>.fromJson(raw.toJson());
    } else {
      result = _getEmptyHttpResponseDataList<BulletinListItemModel>();
    }

    return result;
  }

  ///***
  /// * 获取给定 UUID 的公告公示
  /// *
  /// * @param {String} uuid
  /// *
  /// * @return {Future<BulletinListItemModel>}
  /// */
  static Future<BulletinListItemModel> getBulletin(String uuid) async {
    final String url = 'BulletinSearchApi/Bulletin/$uuid';

    final BulletinListItemModel result = await HttpUtils.get<BulletinListItemModel>(
      url,
      domainName: _bulletinDomain,
    );

    return result;
  }

  ///***
  /// * 获取用户的搜索历史关键词
  /// *
  /// * @return {Future<List<String>>}
  /// */
  static Future<List<String>> getSearchHistoryKeywords() async {
    final String url = 'BulletinSearchApi/KeyWord';

    final List raw = await HttpUtils.get<List>(
      url,
      domainName: _bulletinDomain,
    );

    List<String> result;

    if (raw != null) {
      result = raw.map<String>(
        (keyword) => keyword as String,
      ).toList();
    } else {
      result = [];
    }

    return result;
  }

  ///***
  /// * 获取热门搜索关键词
  /// *
  /// * @return {Future<List<String>>}
  /// */
  static Future<List<String>> getPopularSearchKeywords() async {
    final String url = 'BulletinSearchApi/KeyWordCount';

    final List raw = await HttpUtils.get<List>(
      url,
      domainName: _bulletinDomain,
    );

    List<String> result;

    if (raw != null) {
      result = raw.map<String>(
        (keyword) => keyword as String,
      ).toList();
    } else {
      result = [];
    }

    return result;
  }

  ///************************************* 市场主体相关 API *************************************/

  ///***
  /// * 获取 市场主体列表
  /// *
  /// * @param {Map<String, dynamic>} params
  /// * @return {Future<HttpResponseDataListModel<SubjectBasicModel>>}
  /// */
  static Future<HttpResponseDataListModel<SubjectBasicModel>> getSubjectBasicList(
    Map<String, dynamic> params,
  ) async {
    const String url = 'supplierapi/SupplierSubjectNew';

    final HttpResponseDataListModel raw =
      await HttpUtils.get<HttpResponseDataListModel>(
        url,
        params: params,
        domainName: _supplierDomain,
      );

    HttpResponseDataListModel<SubjectBasicModel> result;

    if (raw != null) {
      result = HttpResponseDataListModel<SubjectBasicModel>.fromJson(raw.toJson());
    } else {
      result = _getEmptyHttpResponseDataList<SubjectBasicModel>();
    }

    return result;
  }

  ///***
  /// * 根据 ID 获取 主体基本信息
  /// *
  /// * @param {Map<String, int>} params
  /// * @return {Future<SubjectBasicModel>}
  /// */
  static Future<SubjectBasicModel> getSubjectBasic(
    Map<String, int> params,
  ) async {
    const String url = 'supplierapi/SupplierSubjectNewBy';

    final SubjectBasicModel result = await HttpUtils.get<SubjectBasicModel>(
      url,
      params: params,
      domainName: _supplierDomain,
    );

    return result;
  }

  ///***
  /// * 根据 ID 获取 主体业绩
  /// *
  /// * @param {Map<String, int>} params
  /// * @return {Future<List<SubjectPerformanceModel>>}
  /// */
  static Future<List<SubjectPerformanceModel>> getSubjectPerformance(
    Map<String, int> params,
  ) async {
    const String url = 'supplierapi/SupplierPerformance';

    final List raw = await HttpUtils.get<List>(
      url,
      params: params,
      domainName: _supplierDomain,
    );

    final List<SubjectPerformanceModel> result =
      raw?.map<SubjectPerformanceModel>(
        (performance) => SubjectPerformanceModel.fromJson(performance),
      )?.toList() ?? [];

    return result;
  }

  ///***
  /// * 根据 ID 获取 主体资质
  /// *
  /// * @param {Map<String, int>} params
  /// * @return {Future<List<SubjectQualificationModel>>}
  /// */
  static Future<List<SubjectQualificationModel>> getSubjectQualification(
    Map<String, int> params,
  ) async {
    const String url = 'supplierapi/SupplierSubjectQualification';

    final List raw = await HttpUtils.get<List>(
      url,
      params: params,
      domainName: _supplierDomain,
    );

    final List<SubjectQualificationModel> result =
      raw?.map<SubjectQualificationModel>(
        (qualification) => SubjectQualificationModel.fromJson(qualification),
      )?.toList() ?? [];

    return result;
  }

  ///***
  /// * 根据 ID 获取 主体人员
  /// *
  /// * @param {Map<String, int>} params
  /// * @return {Future<List<SubjectStaffModel>>}
  /// */
  static Future<List<SubjectStaffModel>> getSubjectStaff(
    Map<String, int> params,
  ) async {
    const String url = 'supplierapi/SupplierStaff';

    final List raw = await HttpUtils.get<List>(
      url,
      params: params,
      domainName: _supplierDomain,
    );

    final List<SubjectStaffModel> result =
      raw?.map<SubjectStaffModel>(
        (staff) => SubjectStaffModel.fromJson(staff),
      )?.toList() ?? [];

    return result;
  }

  ///************************************* 数据报告相关 API *************************************/

  ///***
  /// * 获取对应年份的报告数据
  /// *
  /// * @param {Map<String, int>} params
  /// */
  static Future<DataReportModel> getReportData(
    int year,
  ) async {
    const String url = 'BulletinSearchApi/ReportController';

    final Map<String, dynamic> params = {
      'yearDate': year,
    };

    final DataReportModel result = await HttpUtils.get<DataReportModel>(
      url,
      params: params,
      domainName: _bulletinDomain,
    );

    return result;
  }

  ///************************************* Common Functions *************************************/

  ///***
  /// * 获取空的后端返回的数据列表
  /// *
  /// * @return {HttpResponseDataListMode<T>}
  /// */
  static HttpResponseDataListModel<T> _getEmptyHttpResponseDataList<T>() {
    return HttpResponseDataListModel<T>(
      totalNumber: 0,
      totalPage: 0,
      pageSize: 20,
      currentPage: 1,
      data: [],
    );
  }

}
