///***
/// * 首页 - 公告公示切换按钮列表
/// */

import "package:flutter/material.dart";
import 'package:provider/provider.dart';

import 'package:flutter_main_app/serviceLocator.dart';
import 'package:flutter_main_app/src/constants/theme.dart';

import 'package:flutter_main_app/src/viewModels/home/<USER>';

class HomePageTabWidget extends StatefulWidget implements PreferredSizeWidget {
  HomePageTabWidget({
    Key key, 
    @required this.callback,
  }) : preferredSize = Size.fromHeight(41),
    super(key: key);

  /// * 公告公示点击的回调
  final void Function() callback;

  @override
  final Size preferredSize;

  @override
  _HomePageTabWidgetState createState() => _HomePageTabWidgetState();
}

class _HomePageTabWidgetState extends State<HomePageTabWidget> {

  _HomePageTabWidgetState();

  /// * 高亮颜色
  static const Color _highlightColor = const Color(0xffe3504a);

  /// * 公告公示名称列表
  static const List<String> _bulletins = [
    '招标公告', '预审公告', '中标候选人公示', '中标结果公示',
  ];

  final List<Color> _tabFontColors = [];

  final List<Color> _tabLineColors = [];

  @override
  void initState() {
    super.initState();

    for (int i = 0; i < _bulletins.length; i++) {
      if (i == 0) {
        _tabFontColors.add(_highlightColor);
        _tabLineColors.add(_highlightColor);
      } else {
        _tabFontColors.add(themeTipsTextColor);
        _tabLineColors.add(Colors.white);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider<HomeIndexViewModel>.value(
          value: serviceLocator<HomeIndexViewModel>(),
        ),
      ],
      child: Consumer<HomeIndexViewModel>(
        builder: (context, model, child) {
          return Container(
            padding: EdgeInsets.only(top: 9.0),
            decoration: BoxDecoration(
              color: themeContentBackgroundColor,
              border: Border(
                bottom: BorderSide(
                  color: themeBorderColor,
                  width: 0.5,
                ),
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.max,
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: _buildBulletinTabs(context),
            ),
          );
        },
      ),
    );
  }

  ///***
  /// * 生成公告公示切换按钮
  /// *
  /// * @param {BuildContext} context
  /// *
  /// * @return {List<Widget>}
  /// */
  List<Widget> _buildBulletinTabs(BuildContext context) {
    final List<Widget> bulletinTabs = [];

    for (int i = 0; i < _bulletins.length; i++) {
      final Widget bulletinTab = InkWell(
        onTap: () {
          _clickTabSwitch(context, i);
        },
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 11.0),
          child: Column(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: <Widget>[
              Text(
                _bulletins[i],
                style: TextStyle(
                  fontSize: themeFontSize,
                  color: _tabFontColors[i],
                ),
              ),
              /// * 底部边线
              Padding(
                padding: const EdgeInsets.only(top: 9.0),
                child: Container(
                  width: 20.0,
                  height: 2.0,
                  color: _tabLineColors[i],
                ),
              ),
            ],
          ),
        ),
      );

      bulletinTabs.add(bulletinTab);
    }

    return bulletinTabs;
  }

  ///***
  /// * 公告类型切换事件
  /// * 要触发父组件传过来的回调函数
  /// *
  /// * @param {BuildContext} context
  /// * @param {int} index: bulletins 下标
  /// */
  void _clickTabSwitch(BuildContext context, int index) {
    setState(() {
      for (int i = 0; i < _bulletins.length; i++) {
        if (i == index) {
          this._tabFontColors[index] = _highlightColor;
          this._tabLineColors[index] = _highlightColor;
        } else {
          this._tabFontColors[i] = themeTipsTextColor;
          this._tabLineColors[i] = Colors.white;
        }
      }
    });

    Provider.of<HomeIndexViewModel>(
      context,
      listen: false,
    ).setSpecifiedParam('bulletinTypeName', [_bulletins[index]]);

    widget.callback();
  }

}
