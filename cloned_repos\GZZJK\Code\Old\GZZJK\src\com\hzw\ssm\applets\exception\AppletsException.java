package com.hzw.ssm.applets.exception;

/**
 * 自定义异常类
 */
public class AppletsException extends RuntimeException {

    private String retCd;  //异常对应的返回码
    private String msgDes;  //异常对应的描述信息

    public AppletsException() {
        super();
    }

    public AppletsException(String message) {
        super(message);
        msgDes = message;
    }

    public AppletsException(String retCd, String msgDes) {
        super();
        this.retCd = retCd;
        this.msgDes = msgDes;
    }

    public String getRetCd() {
        return retCd;
    }

    public String getMsgDes() {
        return msgDes;
    }
}
