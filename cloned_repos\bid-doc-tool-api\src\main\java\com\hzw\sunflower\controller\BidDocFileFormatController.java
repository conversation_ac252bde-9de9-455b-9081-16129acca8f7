package com.hzw.sunflower.controller;
import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.controller.request.BidDocFileFormatReq;
import com.hzw.sunflower.controller.responese.BidDocFileFormatVo;
import com.hzw.sunflower.service.BidDocFileFormatService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2024/6/26 10:54
 * @description：投标文件格式服务控制器
 * @modified By：`
 * @version: 1.0
 */
@Api(tags = "投标文件格式")
@RestController
@RequestMapping("/docFileFormat")
public class BidDocFileFormatController{

    @Autowired
    private BidDocFileFormatService bidDocFileFormatService;

    @ApiOperation(value = "保存投标文件格式")
    @ApiImplicitParam(name = "req", value = "req", required = true, dataType = "BidDocFileFormatReq", paramType = "body")
    @PostMapping(value = "/submitFileFormat")
    public Result<Boolean> submitFileFormat(@RequestBody BidDocFileFormatReq req) {
        if(null == req.getContentName()){
            return Result.error("组成内容名称不能为空");
        }
        if(null == req.getContentFileId()){
            return Result.error("内容模板id不能为空");
        }
        return bidDocFileFormatService.submitFileFormat(req);
    }

    @ApiOperation(value = "查询投标文件格式")
    @ApiImplicitParam(name = "req", value = "req", required = true, dataType = "BidDocDeviceReq", paramType = "body")
    @PostMapping(value = "/queryFileFormat")
    public Result<List<BidDocFileFormatVo>> queryFileFormat(@RequestBody BidDocFileFormatReq req) {
        if(null == req.getDocId()){
            return Result.error("招标文件编制主表id不能为空");
        }
        return bidDocFileFormatService.queryFileFormat(req);
    }
}
