///***
/// * 意见反馈
/// */

import 'package:flutter/material.dart';

import 'package:flutter_main_app/src/constants/theme.dart';
import 'package:flutter_main_app/src/utils/ScreenUtils.dart';

import 'package:flutter_main_app/src/components/CustomAppBar.dart';
import 'package:flutter_main_app/src/components/CustomSafeArea.dart';
// import 'package:flutterapp/src/components/CustomCameraDetector.dart';
import 'package:flutter_main_app/src/components/CustomSubmitButton.dart';

class FeedBack extends StatefulWidget {
  const FeedBack({Key key}) : super(key: key);

  /// * 路由名称
  static const String _routeName = 'FeedBack';
  static String get routeName => _routeName;

  @override
  _FeedBackState createState() => _FeedBackState();
}

class _FeedBackState extends State<FeedBack> {

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      appBar: CustomAppBar(
        title: '意见反馈',
      ),
      body: CustomSafeArea(
        child: Stack(
          children: <Widget>[
            SingleChildScrollView(
              physics: BouncingScrollPhysics(),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  this._buildTitleSection(title: '请简要说明您要反馈的问题', tips: '0/100'),
                  /// * 问题 - 多行文本框
                  Container(
                    margin: const EdgeInsets.symmetric(horizontal: 15.0),
                    height: 120.0,
                    decoration: BoxDecoration(
                      color: themeContentBackgroundColor,
                      borderRadius: BorderRadius.circular(themeBorderRadius),
                    ),
                    child: TextField(
                      maxLines: 10,
                      style: TextStyle(
                        fontSize: 12.0,
                        color: themeTextColor,
                      ),
                      decoration: InputDecoration(
                        contentPadding: const EdgeInsets.only(
                          left: 15.0,
                          top: 10.0,
                          right: 15.0,
                          bottom: 10.0,
                        ),
                        hintText: '请写下您对江苏省招标投标公共服务平台的感受，我们将认真听取您的意见，努力提供更优质的服务。',
                        hintStyle: TextStyle(
                          fontSize: 12.0,
                          color: themeHintTextColor,
                        ),
                        border: InputBorder.none,
                      ),
                    ),
                  ),
                  this._buildTitleSection(title: '可在此添加图片', tips: '0/4'),
                  /// * 添加图片
                  Container(
                    padding: const EdgeInsets.only(
                      left: 15.0,
                      top: 15.0,
                      right: 15.0,
                      bottom: 17.0,
                    ),
                    width: ScreenUtils.screenWidth,
                    height: 15.0 + 17.0 + 78.0,
                    color: themeContentBackgroundColor,
                    child: Row(
                      mainAxisSize: MainAxisSize.max,
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: <Widget>[
                        Container(
                          width: 78.0,
                          height: 78.0,
                          decoration: BoxDecoration(
                            color: themeBackgroundColor,
                            border: Border.all(
                              width: themeBorderWidth,
                              color: themeBorderColor,
                              style: BorderStyle.solid,
                            ),
                            borderRadius: BorderRadius.circular(themeBorderRadius),
                          ),
                          child: Image.network(
                            'https://picsum.photos/250?image=8',
                            fit: BoxFit.cover,
                          ),
                        ),
                        Container(
                          width: 78.0,
                          height: 78.0,
                          decoration: BoxDecoration(
                            color: themeBackgroundColor,
                            border: Border.all(
                              width: themeBorderWidth,
                              color: themeBorderColor,
                              style: BorderStyle.solid,
                            ),
                            borderRadius: BorderRadius.circular(themeBorderRadius),
                          ),
                          child: Image.asset(
                            'assets/images/qq.png',
                            fit: BoxFit.cover,
                          ),
                        ),
                        Container(
                          width: 78.0,
                          height: 78.0,
                          decoration: BoxDecoration(
                            color: themeBackgroundColor,
                            border: Border.all(
                              width: themeBorderWidth,
                              color: themeBorderColor,
                              style: BorderStyle.solid,
                            ),
                            borderRadius: BorderRadius.circular(themeBorderRadius),
                          ),
                          child: Image.asset(
                            'assets/images/qq.png',
                            fit: BoxFit.cover,
                          ),
                        ),
                        /// * 添加按钮
                        Container(
                            width: 78.0,
                            height: 78.0,
                            decoration: BoxDecoration(
                              color: themeBackgroundColor,
                              border: Border.all(
                                width: themeBorderWidth,
                                color: themeBorderColor,
                                style: BorderStyle.solid,
                              ),
                              borderRadius: BorderRadius.circular(themeBorderRadius),
                            ),
                            child: Center(
                              child: Icon(
                                Icons.add,
                                size: 64.0,
                                color: const Color(0xffd9d9d9),
                              ),
                            ),
                          ),
                      ],
                    ),
                  ),
                  this._buildTitleSection(title: '请留下您的联系方式', tips: ''),
                  /// * 联系方式 - 输入框
                  Container(
                    margin: const EdgeInsets.symmetric(horizontal: 15.0),
                    height: 35.0,
                    decoration: BoxDecoration(
                      color: themeContentBackgroundColor,
                      borderRadius: BorderRadius.circular(themeBorderRadius),
                    ),
                    alignment: AlignmentDirectional.center,
                    child: TextField(
                      style: TextStyle(
                        fontSize: 12.0,
                        color: themeTextColor,
                      ),
                      decoration: InputDecoration(
                        contentPadding: const EdgeInsets.symmetric(horizontal: 15.0),
                        hintText: '手机号/邮箱/QQ(选填)',
                        hintStyle: TextStyle(
                          fontSize: 12.0,
                          color: themeHintTextColor,
                        ),
                        border: InputBorder.none,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            /// * 提交按钮
            Positioned(
              left: 0.0,
              bottom: 50.0,
              width: ScreenUtils.screenWidth,
              child: CustomSubmitButton(
                text: '提交',
                onTap: () {},
              ),
            ),
          ],
        ),
      ),
    );
  }

  ///***
  /// * 生成标题部分 UI
  /// *
  /// * @param {String} title
  /// * @param {String} tips
  /// *
  /// * @return {Widget}
  /// */
  Widget _buildTitleSection({ String title, String tips }) {

    const double titleFontSize = 14.0;
    const double tipsFontSize = 12.0;

    final Widget titleSection = Container(
      padding: const EdgeInsets.only(
        left: 15.0,
        top: 20.0,
        right: 15.0,
        bottom: 10.0,
      ),
      width: ScreenUtils.screenWidth,
      child: Row(
        mainAxisSize: MainAxisSize.max,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: <Widget>[
          Text(
            title,
            style: TextStyle(
              fontSize: titleFontSize,
              color: themeTitleColor,
            ),
          ),
          Text(
            tips,
            style: TextStyle(
              fontSize: tipsFontSize,
              color: themeHintTextColor,
            ),
          ),
        ],
      ),
    );

    return titleSection;

  }
}
