package com.hzw.sunflower.controller;


import com.hzw.sunflower.common.BaseController;
import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.common.annotation.Log;
import com.hzw.sunflower.common.annotation.RepeatSubmit;
import com.hzw.sunflower.constant.MessageConstants;
import com.hzw.sunflower.constant.constantenum.BusinessType;
import com.hzw.sunflower.controller.request.UserFieldShowREQ;
import com.hzw.sunflower.controller.response.UserFieldShowVO;
import com.hzw.sunflower.dto.UserFieldShowDTO;
import com.hzw.sunflower.entity.UserFieldShow;
import com.hzw.sunflower.service.UserFieldShowService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


/**
 * 用户字段展示表 Controller
 *
 * <AUTHOR>
 * @version 1.0.0 2021-04-07
 */
@Api(tags = "用户字段展示表 服务")
@RestController
@RequestMapping("/userFieldShow")
public class UserFieldShowController extends BaseController {

    @Autowired
    private UserFieldShowService userFieldShowService;

    @ApiOperation(value = "根据用户字段展示表查询单条数据")
    @ApiImplicitParam(name = "condition", value = "用户字段展示表 查询条件", required = true, dataType = "UserFieldShowREQ", paramType = "body")
    @PostMapping(value = "/getOne")
    public Result<UserFieldShowVO> getOne(@RequestBody UserFieldShowREQ condition) {
        UserFieldShowVO vo = null;
        condition.setUserId(getJwtUser().getUserId());
        condition.setCompanyId(getJwtUser().getCompanyId());
        UserFieldShowDTO dto = userFieldShowService.getOne(condition);
        if (null != dto) {
            vo = new UserFieldShowVO();
            BeanUtils.copyProperties(dto, vo);
        }
        return Result.ok(vo);
    }

    @ApiOperation(value = "新增用户字段展示表 信息")
    @Log(title = "用户字段展示表", description = "新增用户字段展示表", businessType = BusinessType.INSERT)
    @ApiImplicitParam(name = "userFieldShow", value = "用户字段展示表 ", required = true, dataType = "UserFieldShow", paramType = "body")
    @PostMapping("/add")
    @RepeatSubmit
    public Result<Object> add(@RequestBody UserFieldShow userFieldShow) {
        Boolean bool = userFieldShowService.addUserFieldShow(userFieldShow, getJwtUser());
        if (bool) {
            return Result.ok(userFieldShow);
        }
        return Result.failed();
    }

    @ApiOperation(value = "修改用户字段展示表 信息")
    @Log(title = "用户字段展示表", description = "修改用户字段展示表", businessType = BusinessType.UPDATE)
    @ApiImplicitParam(name = "userFieldShow", value = "用户字段展示表 ", required = true, dataType = "UserFieldShow", paramType = "body")
    @PutMapping(value = "/update")
    @RepeatSubmit
    public Result<Object> update(@RequestBody UserFieldShow userFieldShow) {
        Long id = userFieldShow.getId();
        if (id == null) {
            return Result.failed(MessageConstants.UPDATE_NULL);
        }
        Boolean bool = userFieldShowService.updateUserFieldShow(userFieldShow, getJwtUser());
        if (bool) {
            return Result.ok(userFieldShow);
        }
        return Result.failed();
    }

}