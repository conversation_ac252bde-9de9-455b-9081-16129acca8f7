package com.hzw.expertfee.api.common.constant;

/**
 * Message常量
 */
public class MessageConstants {

//    /**
//     * 系统错误
//     */
//    public static final String MESSAGE_SERVER_ERROR = "系统错误!";
//    /**
//     * 新增失败
//     */
//    public static final String ADD_FAILED = "新增失败!";
//    /**
//     * 修改失败
//     */
//    public static final String UPDATE_FAILED = "修改失败!";
//    /**
//     * 文件上传过大
//     */
//    public static final String UPLOAD_TOO_LARGE = "上传文件大小不得超过";
//    /**
//     * 文件单位 M
//     */
//    public static final String FILE_UNIT_M = "M";
//    /**
//     * 必填参数为空
//     */
//    public static final String ADD_FAILED_NULL_PARAM = "必填参数为空，新增失败!";
//
//    /**
//     * 项目信息不存在
//     */
//    public static final String PROJECT_NULL = "项目信息不存在";
//    /**
//     * 项目标段信息不存在
//     */
//    public static final String PROJECT_BID_SECTION_NULL = "项目标段信息不存在";
//    /**
//     * 请输入正确的委托金额
//     */
//    public static final String ENTRUST_MONEY_NULL = "请输入正确的委托金额";
//    /**
//     * 超出项目委托金额
//     */
//    public static final String EXCEED_ENTRUST_MONEY = "EXCEED";
//    /**
//     * 未超出项目委托金额
//     */
//    public static final String NOT_EXCEED_ENTRUST_MONEY = "NOT_EXCEED";
//    /**
//     * 请输入正确的保证金金额
//     */
//    public static final String BOND_MONEY_ERROR = "请输入正确的保证金金额";
//    /**
//     * 请输入正确的代理服务费
//     */
//    public static final String AGENCY_MONEY_ERROR = "请输入正确的代理服务费";
//    /**
//     * 请输入正确的标书费
//     */
//    public static final String TENDER_MONEY_ERROR = "请输入正确的标书费";
//    /**
//     * 项目异常
//     */
//    public static final String PROJRCT_ABNORMAL = "异常";
//    /**
//     * 表单不允许重复提交
//     */
//    public static final String NO_REPEATED_SUBMIT = "不允许重复提交，请稍后再试";
//    /**
//     * 内网IP
//     */
//    public static final String INTRANET_IP = "内网IP";

    /**
     * 常用的返回消息
     */
    public static final String GET_NULL = "请选择需要查询的数据!";
    public static final String UPDATE_NULL = "请选择需要修改的数据!";
    public static final String DELETE_NULL = "请选择需要删除的数据!";
    public static final String SUCCESS = "成功!";
    public static final String FAIL = "失败!";
//    public static final String LOGIN_FAILED = "登录失败!";
//    public static final String PROJECT_REFUSED_NOREASON = "请输入拒绝原因";
//    public static final String NOT_FOUND = "访问路径不存在!";
//    public static final String EMPTY_DATA = "暂无数据!";
//    public static final String ENTRUST_NOT_NULL = "委托信息不能为空!";
//    public static final String USER_DOES_NOT_EXIST = "用户不存在!";
//    public static final String PARAMS_NOT_NULL = "参数不能为空!";
//    public static final String PARAMS_NOT_LESS_THAN = "参数不能小于";
//    public static final String USER_EXITED_SUCCESSFULLY = "用户退出成功!";
//    public static final String UNKNOWN_INTERFACE = "未知接口!";
//
//    public static final String USER_LOGIN_WRONG_VALUE = "用户名/验证码不正确";
//    public static final String USER_LOGIN_WRONG_SOURCE = "请从正确的入口进入系统";
//
//    public static final String SYSTEM_ADMIN_ERROR = "该公司已经存在管理员";
//
//    public static final String USER_REVIEW_STATUS_REJECTED = "当前用户处于%s状态无法登录!";
//
//    public static final String BID_NOTICE_INFORMATION = "请先发布采购公告";
//
//    public static final String DOC_NOT_AUDIT_REMARK = "未及时处理，请修改招标文件发售开始时间";
//
//    public static final String DOC_NOT_RELEASE_REMARK = "未按时发布，请修改招标文件发售开始时间";
//
//    public static final String NOTICE_NOT_AUDIT_REMARK = "未及时处理，请修改招标公告拟发布时间";
//
//    public static final String NOTICE_NOT_RELEASE_REMARK = "未按时发布，请修改招标公告拟发布时间";
//
//    public static final String BID_SECTION_LEVEL_REMARK = "请填写正确的标的物分类信息";
//
//    public static final String SALE_END_TIME_REMARK = "请修改招标文件发售结束时间后进行提交";
//
//    //public static final String SALE_END_TIME_REMARK_5 = "挂网时间到文件发售结束时间大于等于5日";
//
//    public static final String SALE_END_TIME_REMARK_START = "挂网时间到文件发售结束时间大于等于";
//
//    public static final String SALE_END_TIME_REMARK_END = "日";
//
//    public static final String SUBMIT_END_TIME_REMARK = "请修改投标文件递交截止时间后进行提交";
//
//    public static final String AUDIT_ERROR_REMARK = "审核内容错误，请确认审核信息";
//
//    public static final String EXTRACT_EXPERTS_RECORD = "存在%s天未备案情况,无法抽取!";
//
//    public static final String INSUFFICIENT_NUMBER = "人数不足!";
//
//    public static final String FREQUENCY_OVERLOAD = "您目前补充抽取专家已超过3次，需要在5个工作日内进行备案，若未按时备案您所属处室将不允许抽取专家!";
//
//    public static final String CHOOSE_THE_RIGHT_TIME = "请选择已确认开标时间的标段包!";
//
//    public static final String DATA_DUPLICATION_INSERT = "存在重复数据,新增失败!";
//
//    public static final String DATA_DUPLICATION_UPDATE = "存在重复数据，修改失败!";
//
//    public static final String DATA_DUPLICATION_APPROVAL_INSERT = "当前选择人员已存在自动审批授权!";
//
//    public static final String SEND_PAYMENT_FAILURE_SMS = "短信发送失败!";
//
//    public static final String USER_NOT_REGISTERED = "当前用户未注册，请注册后登录";
//
//    public static final String PURCHASE_TYPE_UPDATE = "当前状态不能更新采购方式!";
//
//    public static final String EXTRACT_EXPERTS_RECORD_EXSIT = "您所在的处室存在未备案事项，对应操作人为：%s，请完成备案后再进行专家抽取";

    public static final String GET_CODE = "该科目编码已存在！";

    public static final String NULL_PARAM = "必填参数为空!";

    public static final String SUBJECT_FIND_NULL = "科目不存在或已被删除!";

    public static final String INDEX_FIND_NULL = "处室指标不存在或已被删除!";

    public static final String DEPT_FIND_NULL = "该处室不存在或被删除!";

    public static final String TIME_CONVERSION_ERROR = "时间转换错误！";

    public static final String COMPNAY_COST_DELETE = "公司员工费用不存在或已被删除!";

    public static final String SHARE_COST_DELETE = "分摊运营成本不存在或已被删除!";

    public static final String SHARE_COST_EXCEL_NUM = "分摊运营成本导入最大导入数量1000条";

    public static final String COMPANY_COST_EXCEL_NUM = "公司员工费用导入最大导入数量1000条";
}
