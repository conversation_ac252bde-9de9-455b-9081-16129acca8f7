<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hzw.sunflower.dao.TendererReportMapper" >


    <select id="findProjectCount" resultType="com.hzw.sunflower.controller.response.ProjectCountVo">
        SELECT
        IFNULL(t.total,0) count
        FROM
        (
        SELECT
        sum( CASE WHEN DATE_FORMAT( p.created_time, '%Y-%m-%d' ) &lt;= #{condition.chooseDate} THEN 1 ELSE 0 END ) total
        FROM
        t_project p
        LEFT JOIN (SELECT * FROM t_project_tenderee_power ptp WHERE ptp.is_delete = 0 AND ptp.user_id = #{jwtUser.id}) tp  on p.id = tp.project_id
        LEFT JOIN(SELECT p.id,
        IFNULL((LENGTH(p.project_status_code)-LENGTH(replace(p.project_status_code, '72', '-'))),0) ATotal,
        COUNT(s.id) packageNums
        FROM t_project p
        LEFT JOIN t_project_bid_section s ON s.project_id = p.id AND s.is_delete = 0
        WHERE p.is_delete = 0
        GROUP BY p.id) t ON t.id = p.id
        LEFT JOIN r_user_department  rud on tp.user_id = rud.user_id
        WHERE
        p.is_delete = 0
        AND tp.is_delete = 0
        ${condition.dataScope}
        <if test="type != null and type != '' and type == 2">
            AND p.`status` != 9
            AND IF(t.packageNums = 0,t.ATotal &lt;= t.packageNums,t.ATotal &lt; t.packageNums)
        </if>
        <if test="type != null and type != '' and type == 3">
            AND p.`status` = 9
        </if>
        )t
    </select>
    <select id="findOpenBidInfo" resultType="com.hzw.sunflower.entity.TendererReport">
        SELECT DISTINCT
            p.id projectId,
            p.project_name,
            p.purchase_number,
            s.id,
            s.package_number packageNumber,
            IFNULL( s.entrust_money, 0 ) entrustedAmount,
            s.entrust_currency,
            IFNULL(c.company_name,"-") agentCompany,
            IFNULL(u.user_name,"-") agentManager,
            IFNULL(u.user_phone,"-") agentManagerContact,
            IF(s.submit_end_time_type = 1,s.submit_end_time,"-") submitEndTime
        FROM
            t_project p
            LEFT JOIN t_project_bid_section s ON s.project_id = p.id AND s.is_delete = 0
            LEFT JOIN t_project_entrust_user eu ON eu.project_id = p.id AND eu.type = 1 AND eu.is_delete = 0
            LEFT JOIN (SELECT * FROM t_project_tenderee_power ptp WHERE ptp.is_delete = 0 AND ptp.user_id = #{jwtUser.id}) tp  on p.id = tp.project_id
            LEFT JOIN (select DISTINCT rud.* from r_user_department rud
            where rud.is_delete = 0 )  rud on tp.user_id = rud.user_id
            LEFT JOIN t_company c ON c.id = eu.company_id AND c.is_delete = 0
            LEFT JOIN t_user u ON u.id = eu.user_id AND u.is_delete = 0
            LEFT JOIN t_conference_booking_project bp ON bp.project_id = p.id
            AND IF(s.package_number is null,1=1,FIND_IN_SET( s.package_number, bp.package_nums ) )
            AND bp.is_delete = 0
            LEFT JOIN t_conference_booking b ON bp.booking_id = b.id
            AND b.is_delete = 0 AND b.booking_purpose = 2
        WHERE
            p.is_delete = 0
            AND s.`status` &gt;= 20
            AND tp.is_delete = 0
            AND ((s.submit_end_time_type = 1 AND (s.sale_end_time_type = 1 OR s.sale_end_time_type = 3 ))  OR (s.submit_end_time_type = 2 AND s.sale_end_time_type = 1 ) )
            AND s.STATUS not in(70,71,72)
            ${condition.dataScope}
            <if test="condition.keyWords != null and condition.keyWords != '' ">
                AND (p.project_name LIKE concat('%',#{condition.keyWords},'%')
                OR p.purchase_number LIKE concat('%',#{condition.keyWords},'%'))
            </if>
            <if test="condition.startDate != null and condition.startDate != '' ">
                AND DATE_FORMAT( b.start_time, '%Y-%m-%d' ) &gt;= #{condition.startDate}
            </if>
            <if test="condition.endDate != null and condition.endDate != '' ">
                AND DATE_FORMAT( b.end_time, '%Y-%m-%d' ) &lt;= #{condition.endDate}
            </if>
            <if test="condition.companyId != null and condition.companyId != '' ">
                AND eu.company_id = #{condition.companyId}
            </if>
            <if test="condition.name != null and condition.name != '' ">
                AND u.user_name LIKE concat('%',#{condition.name},'%')
            </if>
        ORDER BY p.id,s.id
    </select>
    <select id="findConferenceAddress" resultType="java.lang.String">
        SELECT
        b.conference_address
        FROM
        t_conference_booking b
        LEFT JOIN t_conference_booking_project p ON p.booking_id = b.id
        WHERE
        b.booking_purpose = #{type}
        AND b.is_delete = 0
        AND p.is_delete = 0
        AND p.project_id = #{projectId}
        <if test="packageNumber !=null and packageNumber != ''">
            AND FIND_IN_SET(#{packageNumber},p.package_nums)
        </if>
        ORDER BY p.booking_id
    </select>
    <select id="findOpenBidInfoList" resultType="com.hzw.sunflower.entity.TendererReport">
        SELECT DISTINCT
            p.id projectId,
            p.project_name,
            p.purchase_number,
            s.id,
            s.package_number packageNumber,
            IFNULL( s.entrust_money, 0 ) entrustedAmount,
            s.entrust_currency,
            d.`name` entrustCurrencyName,
            IFNULL(c.company_name,"-") agentCompany,
            IFNULL(u.user_name,"-") agentManager,
            IFNULL(u.user_phone,"-") agentManagerContact,
            IF(s.submit_end_time_type = 1,s.submit_end_time,"-") submitEndTime
        FROM
        t_project p
        LEFT JOIN t_project_bid_section s ON s.project_id = p.id AND s.is_delete = 0
        LEFT JOIN t_project_entrust_user eu ON eu.project_id = p.id AND eu.type = 1 AND eu.is_delete = 0
        LEFT JOIN (SELECT * FROM t_project_tenderee_power ptp WHERE ptp.is_delete = 0 AND ptp.user_id = #{jwtUser.id}) tp  on p.id = tp.project_id
        LEFT JOIN t_company c ON c.id = eu.company_id AND c.is_delete = 0
        LEFT JOIN t_user u ON u.id = eu.user_id AND u.is_delete = 0
        LEFT JOIN t_conference_booking_project bp ON bp.project_id = p.id
        AND IF(s.package_number is null,1=1,FIND_IN_SET( s.package_number, bp.package_nums ) )
        AND bp.is_delete = 0
        LEFT JOIN t_conference_booking b ON bp.booking_id = b.id
        AND b.is_delete = 0 AND b.booking_purpose = 2
        LEFT JOIN t_dictionary d ON d.id = s.entrust_currency AND d.is_delete = 0
        LEFT JOIN r_user_department  rud on tp.user_id = rud.user_id and eu.department_id = rud.department_id
        WHERE
        p.is_delete = 0
        AND s.`status` &gt;= 20
        AND tp.is_delete = 0
        ${condition.dataScope}
        AND ((s.submit_end_time_type = 1 AND (s.sale_end_time_type = 1 OR s.sale_end_time_type = 3 ))  OR (s.submit_end_time_type = 2 AND s.sale_end_time_type = 1 ) )
        AND s.STATUS not in(70,71,72)
        <if test="condition.keyWords != null and condition.keyWords != '' ">
            AND (p.project_name LIKE concat('%',#{condition.keyWords},'%')
            OR p.purchase_number LIKE concat('%',#{condition.keyWords},'%'))
        </if>
        <if test="condition.startDate != null and condition.startDate != '' ">
            AND DATE_FORMAT( b.start_time, '%Y-%m-%d' ) &gt;= #{condition.startDate}
        </if>
        <if test="condition.endDate != null and condition.endDate != '' ">
            AND DATE_FORMAT( b.end_time, '%Y-%m-%d' ) &lt;= #{condition.endDate}
        </if>
        <if test="condition.companyId != null and condition.companyId != '' ">
            AND eu.company_id = #{condition.companyId}
        </if>
        <if test="condition.name != null and condition.name != '' ">
            AND u.user_name LIKE concat('%',#{condition.name},'%')
        </if>
        ORDER BY p.id,s.id
    </select>
    <select id="applyInfoList" resultType="com.hzw.sunflower.entity.TendererReport">
        SELECT distinct
        p.id projectId,
        s.id sectionId,
        IFNULL(p.purchase_number,"-") purchaseNumber,
        IFNULL(p.project_name,"-") projectName,
        IFNULL(s.package_number,"-") packageNumber,
        IFNULL(s.package_name,"-") packageName,
        IFNULL(s.entrust_money, 0 ) entrustedAmount,
        s.entrust_currency,
        IFNULL(s.purchase_mode_name,"-") purchaseModeName,
        IFNULL(c.company_name,"-") agentCompany,
        IFNULL(u.user_name,"-") agentManager,
        IFNULL( t.count, 0 ) applyInfoNum,
        IF(s.sale_end_time_type = 1,s.sale_end_time,IF(s.sale_end_time_type = 3 AND s.submit_end_time_type = 1,s.sale_end_time,'-')) sale_end_time ,
        IF((ai.data_scope = 1 AND ai.right_code = 1 ),1,ai.right_code) rightCode,
        ae.is_empower isEmpower,
        IFNULL(ts.is_define,2) isDefine
        FROM t_project p
        LEFT JOIN (SELECT * FROM t_project_tenderee_power ptp WHERE ptp.is_delete = 0 AND ptp.user_id = #{jwtUser.id}) tp  on p.id = tp.project_id
        LEFT JOIN t_project_bid_section s ON s.project_id = p.id AND s.is_delete = 0
        LEFT JOIN t_project_entrust_user eu1 ON eu1.is_delete = 0 AND eu1.project_id = p.id AND eu1.type = 1
        LEFT JOIN t_company c ON c.is_delete = 0 AND c.id = eu1.company_id
        LEFT JOIN t_user u ON u.is_delete = 0 AND u.id = eu1.user_id
        LEFT JOIN (SELECT a.sub_id,COUNT(*) count,a.bid_round FROM t_apply_info a
        WHERE a.APPLY_STATUS in ('4','8','9','10') AND a.is_delete = 0 AND a.download_flag = 1
        GROUP BY a.sub_id,a.bid_round ) t ON t.sub_id = s.id AND t.bid_round = s.bid_round
        LEFT JOIN t_user_show_apply_info ai ON ai.user_id = tp.user_id AND ai.is_delete = 0 AND IF(ISNULL(ai.project_id),1=1,ai.project_id = p.id )
        LEFT JOIN t_apply_info a ON a.sub_id = s.id AND a.is_delete = 0 AND s.bid_round = a.bid_round
        LEFT JOIN t_tenderee_apply_empower ae ON ae.project_id = p.id AND ae.is_delete = 0
        LEFT JOIN t_tenderee_apply_secret ts ON ts.project_id = p.id AND ts.is_delete = 0 AND ts.user_id = tp.user_id
        LEFT JOIN r_user_department  rud on tp.user_id = rud.user_id  and eu1.department_id = rud.department_id
        WHERE
        p.is_delete = 0
        AND tp.is_delete = 0
        AND s.`status` &gt;= 20
        ${condition.dataScope}
        AND ((s.submit_end_time_type = 1 AND (s.sale_end_time_type = 1 OR s.sale_end_time_type = 3 ))  OR (s.submit_end_time_type = 2 AND s.sale_end_time_type = 1 ) )
        AND s.STATUS not in(70,71,72)
        <if test="condition.keyWords != null and condition.keyWords != '' ">
            AND (p.project_name LIKE concat('%',#{condition.keyWords},'%')
            OR p.purchase_number LIKE concat('%',#{condition.keyWords},'%')
            OR u.user_name LIKE concat('%',#{condition.keyWords},'%') )
        </if>
        <if test="condition.startDate != null and condition.startDate != '' ">
            AND DATE_FORMAT( s.sale_end_time, '%Y-%m-%d' ) &gt;= #{condition.startDate}
        </if>
        <if test="condition.endDate != null and condition.endDate != '' ">
            AND DATE_FORMAT( s.sale_end_time, '%Y-%m-%d' ) &lt;= #{condition.endDate}
        </if>
        <if test="condition.companyId != null and condition.companyId != '' ">
            AND eu1.company_id = #{condition.companyId}
        </if>
        <if test="condition.name != null and condition.name != ''">
            AND a.company_name like CONCAT("%",#{condition.name},"%")
            AND (ae.is_empower = 1 OR (ae.is_empower is null AND IF((ai.data_scope = 1 AND ai.right_code = 1 ),1,ai.right_code) = 1 ) )
            AND IFNULL(ts.is_define,2) = 1
            AND a.APPLY_STATUS IN ( '4', '8', '9', '10' ) AND a.download_flag = 1
        </if>
        GROUP BY
            p.id,
            s.id,
            p.purchase_number,
            p.project_name,
            s.package_number,
            s.package_name,
            s.entrust_money,
            s.entrust_currency,
            s.purchase_mode_name,
            c.company_name,
            u.user_name,
            t.count,
            s.sale_end_time,
            ai.right_code,
            ai.data_scope,
            ae.is_empower,
            ts.is_define,
            s.sale_end_time_type,
            s.submit_end_time_type
        ORDER BY
            p.id,
            s.id
    </select>
    <select id="findApplyInfos" resultType="com.hzw.sunflower.entity.ApplyInfo">
        SELECT
        a.company_name,
        a.contact_person,
        a.user_mobile
        FROM t_apply_info a
        LEFT JOIN t_project_bid_section s ON a.sub_id = s.id AND a.bid_round = s.bid_round
        WHERE s.id = #{sectionId}
        AND a.project_id = #{projectId}
        AND a.is_delete = 0
        AND s.is_delete = 0
        AND a.APPLY_STATUS in ('4','8','9','10')
        AND a.download_flag = 1
    </select>
    <select id="findApplyInfoList" resultType="com.hzw.sunflower.entity.TendererReport">
        SELECT distinct
        p.id projectId,
        s.id sectionId,
        IFNULL(p.purchase_number,"-") purchaseNumber,
        IFNULL(p.project_name,"-") projectName,
        IFNULL(s.package_number,"-") packageNumber,
        IFNULL(s.package_name,"-") packageName,
        IFNULL(s.entrust_money, 0 ) entrustedAmount,
        s.entrust_currency,
        d.`name` entrustCurrencyName,
        IFNULL(s.purchase_mode_name,"-") purchaseModeName,
        IFNULL(c.company_name,"-") agentCompany,
        IFNULL(u.user_name,"-") agentManager,
        IFNULL( t.count, 0 ) applyInfoNum,
        IF(s.sale_end_time_type = 1,s.sale_end_time,IF(s.sale_end_time_type = 3 AND s.submit_end_time_type = 1,s.sale_end_time,'-')) sale_end_time ,
        IF((ai.data_scope = 1 AND ai.right_code = 1 ),1,ai.right_code) rightCode,
        ae.is_empower isEmpower,
        IFNULL(ts.is_define,2) isDefine
        FROM t_project p
        LEFT JOIN (SELECT * FROM t_project_tenderee_power ptp WHERE ptp.is_delete = 0 AND ptp.user_id = #{jwtUser.id}) tp  on p.id = tp.project_id
        LEFT JOIN t_project_bid_section s ON s.project_id = p.id AND s.is_delete = 0
        LEFT JOIN t_project_entrust_user eu1 ON eu1.is_delete = 0 AND eu1.project_id = p.id AND eu1.type = 1
        LEFT JOIN t_company c ON c.is_delete = 0 AND c.id = eu1.company_id
        LEFT JOIN t_user u ON u.is_delete = 0 AND u.id = eu1.user_id
        LEFT JOIN (SELECT a.sub_id,COUNT(*) count,a.bid_round FROM t_apply_info a
        WHERE a.apply_status = 4 AND a.is_delete = 0 AND a.download_flag = 1
        GROUP BY a.sub_id,a.bid_round ) t ON t.sub_id = s.id AND t.bid_round = s.bid_round
        LEFT JOIN t_user_show_apply_info ai ON ai.user_id = tp.user_id AND ai.is_delete = 0 AND IF(ISNULL(ai.project_id),1=1,ai.project_id = p.id )
        LEFT JOIN t_apply_info a ON a.sub_id = s.id AND a.is_delete = 0 AND s.bid_round = a.bid_round
        LEFT JOIN t_dictionary d ON d.id = s.entrust_currency AND d.is_delete = 0
        LEFT JOIN t_tenderee_apply_empower ae ON ae.project_id = p.id AND ae.is_delete = 0
        LEFT JOIN t_tenderee_apply_secret ts ON ts.project_id = p.id AND ts.is_delete = 0 AND ts.user_id = tp.user_id
        LEFT JOIN r_user_department  rud on tp.user_id = rud.user_id  and eu1.department_id = rud.department_id
        WHERE
        p.is_delete = 0
        AND tp.is_delete = 0
        AND s.`status` &gt;= 20
        ${condition.dataScope}
        AND ((s.submit_end_time_type = 1 AND (s.sale_end_time_type = 1 OR s.sale_end_time_type = 3 ))  OR (s.submit_end_time_type = 2 AND s.sale_end_time_type = 1 ) )
        AND s.STATUS not in(70,71,72)
        <if test="condition.keyWords != null and condition.keyWords != '' ">
            AND (p.project_name LIKE concat('%',#{condition.keyWords},'%')
            OR p.purchase_number LIKE concat('%',#{condition.keyWords},'%')
            OR u.user_name LIKE concat('%',#{condition.keyWords},'%') )
        </if>
        <if test="condition.startDate != null and condition.startDate != '' ">
            AND DATE_FORMAT( s.sale_end_time, '%Y-%m-%d' ) &gt;= #{condition.startDate}
        </if>
        <if test="condition.endDate != null and condition.endDate != '' ">
            AND DATE_FORMAT( s.sale_end_time, '%Y-%m-%d' ) &lt;= #{condition.endDate}
        </if>
        <if test="condition.companyId != null and condition.companyId != '' ">
            AND eu1.company_id = #{condition.companyId}
        </if>
        <if test="condition.name != null and condition.name != ''">
            AND a.company_name like CONCAT("%",#{condition.name},"%")
            AND (ae.is_empower = 1 OR (ae.is_empower is null AND IF((ai.data_scope = 1 AND ai.right_code = 1 ),1,ai.right_code) = 1 ) )
            AND IFNULL(ts.is_define,2) = 1
            AND a.APPLY_STATUS IN ( '4', '8', '9', '10' ) AND a.download_flag = 1
        </if>
        GROUP BY
        p.id,
        s.id,
        p.purchase_number,
        p.project_name,
        s.package_number,
        s.package_name,
        s.entrust_money,
        s.entrust_currency,
        s.purchase_mode_name,
        c.company_name,
        u.user_name,
        t.count,
        s.sale_end_time,
        ai.right_code,
        ai.data_scope,
        ae.is_empower,
        ts.is_define,
        s.sale_end_time_type,
        s.submit_end_time_type
        ORDER BY
        p.id,
        s.id
    </select>
    <select id="findEvaluationTime" resultType="java.lang.String">
        SELECT
        b.start_time
        FROM
        t_conference_booking b
        LEFT JOIN t_conference_booking_project p ON p.booking_id = b.id
        WHERE
        b.booking_purpose = #{type}
        AND b.is_delete = 0
        AND p.is_delete = 0
        AND p.project_id = #{projectId}
        <if test="packageNumber !=null and packageNumber != ''">
            AND FIND_IN_SET(#{packageNumber},p.package_nums)
        </if>
        ORDER BY p.booking_id
    </select>
</mapper>
