package com.hzw.sunflower.controller;

import com.hzw.sunflower.common.BaseController;
import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.common.annotation.RepeatSubmit;
import com.hzw.sunflower.controller.request.PendingItemReq;
import com.hzw.sunflower.service.PendingItemService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 待处理事项表 前端控制器
 * </p>
 *
 * <AUTHOR> Gen
 * @since 2022-12-15
 */
@Api(tags = "待处理事项表 服务")
@RestController
@RequestMapping("/pendingItem")
public class PendingItemController extends BaseController {

    @Autowired
    private PendingItemService pendingItemService;

    @ApiOperation(value = "退回待处理 无需处理")
    @ApiImplicitParam(name = "req", value = "退回待处理 参数", required = true, dataType = "PendingItemReq", paramType = "body")
    @PostMapping("/updateHandRequired")
    @RepeatSubmit
    public Result<Boolean> updateHandRequired(@RequestBody PendingItemReq req) {
        Boolean bool = pendingItemService.updateHandRequired(req);
        return Result.okOrFailed(bool);
    }


}
