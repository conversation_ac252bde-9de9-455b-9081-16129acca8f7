package com.hzw.ssm.fw.util;

import java.io.BufferedInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.util.Properties;
import java.util.Random;

import org.apache.commons.lang.StringUtils;

public class FwUtil {

	/**
	 * 判断字符串是否为空
	 * 
	 * @param str
	 * @return [参数说明]
	 * 
	 * @return boolean [返回类型说明]
	 * @exception throws [违例类型] [违例说明]
	 * @see [类、类#方法、类#成员]
	 */
	public static boolean isEmpty(String str) {
		if (str == null || str.trim().length() <= 0 || str.equals("null")) {
			return true;
		}

		return false;
	}

	/**
	 * 返回大写首字母 <功能详细描述>
	 * 
	 * @param str
	 * @return [参数说明]
	 * 
	 * @return String [返回类型说明]
	 * @exception throws [违例类型] [违例说明]
	 * @see [类、类#方法、类#成员]
	 */
	public static String capitalize(String str) {
		return StringUtils.capitalize(str);
	}

	/**
	 * 获得config.properties文件中key对应的value
	 * 
	 * @param key
	 * @return 返回值
	 */
	public static String getByKey(String key, String fileName) {
		// 返回值
		String retValue = "";
		Properties prop = new Properties();
		InputStream stream = null;
		// 读取文件
		InputStream is = FwUtil.class.getResourceAsStream("/" + fileName);
		try {
			stream = new BufferedInputStream(is);
			prop.load(stream);
			// 取得对应值
			retValue = prop.getProperty(key);
			stream.close();
			is.close();
		} catch (FileNotFoundException fe) {
			fe.printStackTrace();
		} catch (IOException ioe) {
			ioe.printStackTrace();
		}
		return retValue;
	}
	
	/*******
     * 翻页
     * @param curPage
     * @param maxPage
     * @return
     */
	public static String getPageTwo(int curPage, int maxPage) {
		Random random = new Random();
		String str = "当前<font color=red>" + curPage + "</font>/" + maxPage
				+ "页&nbsp;&nbsp;";
		if (maxPage == 0 || maxPage == 1) {
			return "";
		}

		String name = "page" + random.nextInt(100);
		if (curPage == 1) {
			str = str + " <font color=grey>首页</font> ";
		} else {
			str = str + " <span style=\"cursor:hand;\"  onClick=\"turnPage("+1+")\"> 首页 </span>";

		}
		if (curPage == 1) {
			str = str + " <font color=grey>上一页</font> ";
		} else {
			str = str + " <span style=\"cursor:hand;\"   onClick=\"turnPage("+(curPage - 1)+")\"> 上一页 </span>";

		}
		if (curPage == maxPage) {
			str = str + " <font color=grey>下一页</font> ";
		} else {
			str = str + " <span style=\"cursor:hand;\"  onClick=\"turnPage("+(curPage + 1)+")\"> 下一页 </span>";

		}
		if (curPage == maxPage) {
			str = str + " <font color=grey>尾页</font> ";
		} else {
			str = str + " <span style=\"cursor:hand;\" onClick=\"turnPage("+maxPage+")\"> 尾页 </span>";
		}
		str += "&nbsp; 转到:<input type=text name="
				+ name
				+ " size=2 onKeyDown=\"javascript: if(window.event.keyCode == 13&&this.value!=''&&this.value<="
				+ maxPage
				+ ") {turnPage(this.value);return false;}\"><input type=button value=GO! onClick=\""
				+ "if(document.all." + name + ".value!=''&&document.all."
				+ name + ".value<=" + maxPage + "&& document.all." + name + ".value>0){ turnPage(document.all."+name+".value);}\" >";
		return str;
	}
}
