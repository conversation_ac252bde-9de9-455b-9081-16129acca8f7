package com.hzw.sunflower.service.impl;


import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.hzw.sunflower.constant.constantenum.*;
import com.hzw.sunflower.dto.ProjectShareInfoDto;
import com.hzw.sunflower.util.BeanListUtil;
import com.hzw.sunflower.constant.GeneralConstants;
import com.hzw.sunflower.dao.ProjectMapper;
import com.hzw.sunflower.entity.Project;
import com.hzw.sunflower.entity.ProjectBidSection;
import com.hzw.sunflower.entity.ProjectBidSectionRecord;
import com.hzw.sunflower.service.CommonSectionService;
import com.hzw.sunflower.service.ProjectBidSectionRecordService;
import com.hzw.sunflower.service.ProjectBidSectionService;
import com.hzw.sunflower.service.RabbitMqService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.StringJoiner;

@Service
public class CommonSectionServiceImpl implements CommonSectionService {

    @Autowired
    private ProjectBidSectionService projectBidSectionService;

    @Autowired
    private ProjectBidSectionRecordService projectBidSectionRecordService;

    @Autowired
    private ProjectMapper projectMapper;

    @Autowired
    private RabbitMqService rabbitMqService;


    @Override
    public Boolean updateSectionStatus(String sectionIds, String status) {
        String[] ids = sectionIds.split(",");
        List<ProjectBidSection> sections = new ArrayList<>();
        for(int i = 0;i<ids.length;i++){
            ProjectBidSection section = new ProjectBidSection();
            section.setId(Long.parseLong(ids[i]));
            section.setStatus(status);
            sections.add(section);
        }
        Boolean result = updateSections(sections);
        //修改项目冗余字段状态并发送mq
        updateProjectPackageFieldAndSendMQ(sections);
        return result;
    }


    @Override
    public Boolean updateSectionCanSearchByIds(List<Long> ids,Integer status){
        UpdateWrapper<ProjectBidSection> updateWrapper =new UpdateWrapper<>();
        updateWrapper.lambda().in(ProjectBidSection::getId,ids);

        updateWrapper.lambda().set(ProjectBidSection::getCanSearch,status);
        projectBidSectionService.update(updateWrapper);
        return  true;
    }


    @Override
    public Boolean updateSectionStatus(List<Long> sectionIds, Integer status) {
        List<ProjectBidSection> sections = new ArrayList<>();
        for(int i = 0;i<sectionIds.size();i++){
            ProjectBidSection section = new ProjectBidSection();
            section.setId(sectionIds.get(i));
            section.setStatus(status.toString());
            sections.add(section);
        }
        Boolean result = updateSections(sections);
        //修改项目冗余字段状态并发送mq
        updateProjectPackageFieldAndSendMQ(sections);
        return result;
    }


    /**
     * 统一修改标段（标段子表）状态
     * @return
     */
    @Override
    public Boolean updateSectionStatus(List<ProjectBidSection> sections) {
        Boolean result = updateSections(sections);
        //修改项目冗余字段状态并发送mq
        updateProjectPackageFieldAndSendMQ(sections);
        return result;
    }



    /**
     * 统一修改标段（标段子表）状态
     * @return
     */
    @Override
    public Boolean updateSectionStatus(ProjectBidSection section) {
        List<ProjectBidSection> sections = new ArrayList<>();
        sections.add(section);
        Boolean result = updateSections(sections);
        //修改项目冗余字段状态并发送mq
        updateProjectPackageFieldAndSendMQ(sections);
        return result;
    }

    /**
     * 根据type不同，执行特殊操作
     * @param sections
     * @param type
     * @return
     */
    @Override
    public Boolean updateSectionStatus(List<ProjectBidSection> sections, String type) {
        Boolean result = updateSectionsForType(sections,type);
        //修改项目冗余字段状态并发送mq
        updateProjectPackageFieldAndSendMQ(sections);
        return result;
    }


    /**
     * 更新标段
     *
     * @return 结果
     */
    private Boolean updateSections(List<ProjectBidSection> sections) {
        //更新标段状态
        Boolean result = projectBidSectionService.saveOrUpdateBatch(sections);

        for (ProjectBidSection section : sections) {
            if(null != section.getSaleEndTimeType() && null != section.getSubmitEndTimeType() && (null == section.getSaleEndTime() || null == section.getSubmitEndTime())){
                UpdateWrapper<ProjectBidSection> updateWrapper = new UpdateWrapper<>();
                if(null == section.getSaleEndTime()){
                    updateWrapper.set("sale_end_time",null);
                }
                if(null == section.getSubmitEndTime()){
                    updateWrapper.set("submit_end_time",null);
                }
                updateWrapper.eq("id",section.getId());
                projectBidSectionService.update(updateWrapper);
            }
        }

        List<ProjectBidSectionRecord> records = new ArrayList<>();
        for (ProjectBidSection bid :sections) {
            //查询标段信息
            ProjectBidSection section = projectBidSectionService.getById(bid.getId());

            //根据标段id,bidRound查询子表信息
            records = setBidSectionRecords(section,records);
        }
        //更新标段子表信息
        projectBidSectionRecordService.updateBatchById(records);
        for (ProjectBidSectionRecord sectionRecord : records) {
            if(null != sectionRecord.getSaleEndTimeType() && null != sectionRecord.getSubmitEndTimeType() && (null == sectionRecord.getSaleEndTime() || null == sectionRecord.getSubmitEndTime())){
                UpdateWrapper<ProjectBidSectionRecord> updateWrapper = new UpdateWrapper<>();
                if(null == sectionRecord.getSaleEndTime()){
                    updateWrapper.set("sale_end_time",null);
                }
                if(null == sectionRecord.getSubmitEndTime()){
                    updateWrapper.set("submit_end_time",null);
                }
                updateWrapper.eq("id",sectionRecord.getId());
                projectBidSectionRecordService.update(updateWrapper);
            }
        }
        return result;
    }


    /**
     * 更新标段
     *
     * @return 结果
     */
    private Boolean updateSectionsForType(List<ProjectBidSection> sections,String type) {
        Boolean result = false;
        List<ProjectBidSectionRecord> records = new ArrayList<>();
        //归档的时候，几个时间要清空
        if(type.equals("GD")){
            //更新标段状态
            for (ProjectBidSection section : sections) {
                //根据标段id,bidRound查询子表信息
                LambdaQueryWrapper<ProjectBidSectionRecord> recordQuery = new LambdaQueryWrapper<>();
                recordQuery.eq(ProjectBidSectionRecord::getSectionId,section.getId())
                        .eq(ProjectBidSectionRecord::getBidRound, BidRoundEnum.ZGYS.getType());
                //第一轮的记录
                ProjectBidSectionRecord record1 = projectBidSectionRecordService.getOne(recordQuery);
                //修改第一轮的状态为归档
                if(record1 != null){
                    record1.setVersion(null);
                    record1.setStatus(section.getStatus());
                    records.add(record1);
                }

                //更新原标段表状态为10，bidRound = 2
                section.setStatus(PackageStatusEnum.TENDER_INVITATION.getValue().toString());
                LambdaUpdateWrapper<ProjectBidSection> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.set(ProjectBidSection::getSubmitEndTime, null)
                        .set(ProjectBidSection::getSubmitEndTimeType, null)
                        .set(ProjectBidSection::getSaleEndTime, null)
                        .set(ProjectBidSection::getSaleEndTimeType, null)
                        .set(ProjectBidSection::getCanSearch,CanSearchEnum.NOT_CAN.getType())
                        .eq(ProjectBidSection::getId, section.getId());
                result = projectBidSectionService.update(section, updateWrapper);

                //查询标段信息
                ProjectBidSection bid = projectBidSectionService.getById(section.getId());

                //查看第二轮有没有
                LambdaQueryWrapper<ProjectBidSectionRecord> recordQuery2 = new LambdaQueryWrapper<>();
                recordQuery2.eq(ProjectBidSectionRecord::getSectionId,section.getId())
                        .eq(ProjectBidSectionRecord::getBidRound, BidRoundEnum.HS.getType());
                ProjectBidSectionRecord record2 = projectBidSectionRecordService.getOne(recordQuery2);
                if(record2 == null){
                    record2 =  BeanListUtil.convert(bid,ProjectBidSectionRecord.class);
                    record2.setSectionId(section.getId());
                    record2.setId(null);
                }else{
                    Long id = record2.getId();
                    record2 = BeanListUtil.convert(bid,ProjectBidSectionRecord.class);
                    record2.setSectionId(section.getId());
                    record2.setId(id);
                }
                //资格预审第二轮为保密
                record2.setCanSearch(CanSearchEnum.NOT_CAN.getType());
                projectBidSectionRecordService.saveOrUpdate(record2);
            }
        }else if(type.equals("addSection")){
            for (ProjectBidSection section : sections) {
                LambdaUpdateWrapper<ProjectBidSection> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.set(ProjectBidSection::getPackageName, null)
                        .set(ProjectBidSection::getPackageNumber, null)
                        .eq(ProjectBidSection::getId, section.getId());
                result = projectBidSectionService.update(section, updateWrapper);

                //查询标段信息
                ProjectBidSection bid = projectBidSectionService.getById(section.getId());
                //根据标段id,bidRound查询子表信息
                records = setBidSectionRecords(bid,records);
            }
        }
        //更新标段子表信息
        projectBidSectionRecordService.updateBatchById(records);
        return result;
    }


    /**
     * 根据标段id,bidRound查询子表信息
     * @param section
     * @param records
     */
    private List<ProjectBidSectionRecord> setBidSectionRecords(ProjectBidSection section,List<ProjectBidSectionRecord> records){
        //根据标段id,bidRound查询子表信息
        LambdaQueryWrapper<ProjectBidSectionRecord> recordQuery = new LambdaQueryWrapper<>();
        recordQuery.eq(ProjectBidSectionRecord::getSectionId,section.getId())
                .eq(ProjectBidSectionRecord::getBidRound,section.getBidRound());
        ProjectBidSectionRecord record = projectBidSectionRecordService.getOne(recordQuery);
        //把标段信息赋值给子表
        ProjectBidSectionRecord pbsr = BeanListUtil.convert(section,ProjectBidSectionRecord.class);
        pbsr.setSectionId(section.getId());
        if(record != null){
            pbsr.setId(record.getId());
            pbsr.setVersion(null);
            records.add(pbsr);
        }else{
            pbsr.setId(null);
            projectBidSectionRecordService.save(pbsr);
        }
        return  records;
    }

    /**
     * 项目包件新建修改-项目冗余字段处理
     *
     * @param projectId 项目ID
     * @return 是否成功
     */
    @Override
    public Boolean saveOrUpdateProjectPackageFieldDealWith(Long projectId) {
        Project updateProject = new Project();
        updateProject.setId(projectId);

        Project project = projectMapper.selectById(projectId);
        String projectStatus = projectBidSectionService.queryProjectStatusByProjectId(projectId);
//        // 无包
        if (StrUtil.isEmpty(projectStatus)) {
            // 没有标段数据
            ProjectStatusEnum projectStatusEnum = ProjectStatusEnum.matchType(null != project.getStatus()?String.valueOf(project.getStatus()):null);
            if (null == projectStatusEnum) {
                System.out.println("没有匹配上数据");
            }
            updateProject.setProjectStatus(projectStatusEnum.getName());
            updateProject.setProjectStatusCode(projectStatus);
        } else {
            // 有标段数据
            // 设置标段状态
            updateProject.setProjectStatusCode(projectStatus);
            List<String> projectStatusList = Arrays.asList(projectStatus.split(GeneralConstants.SPLICING_PARAMETER_METHOD));
            StringJoiner stringJoiner = new StringJoiner(GeneralConstants.SPLICING_PARAMETER_METHOD);
            //包件状态待维护
            projectStatusList.forEach(item -> {
                PackageStatusEnum bidSectionEnum = PackageStatusEnum.matchType(item);
                if (null == bidSectionEnum) {
                    System.out.println("没有匹配上数据");
                } else {
                    switch (bidSectionEnum) {
                        case INIT:
                            stringJoiner.add(PackageStatusEnum.INIT.getName());
                            break;
                        case PROCESSING:
                            stringJoiner.add(PackageStatusEnum.PROCESSING.getName());
                            break;
                        case DIVIDE_SECTION:
                            stringJoiner.add(PackageStatusEnum.DIVIDE_SECTION.getName());
                            break;
                        case TENDER_INVITATION:
                            stringJoiner.add(PackageStatusEnum.TENDER_INVITATION.getName());
                            break;
                        case ANNOUNCING:
                            stringJoiner.add(PackageStatusEnum.ANNOUNCING.getName());
                            break;
                        case ARCHIVING:
                            stringJoiner.add(PackageStatusEnum.ARCHIVING.getName());
                            break;
                        case OVER:
                            stringJoiner.add(PackageStatusEnum.OVER.getName());
                            break;
                        default:
                            stringJoiner.add(PackageStatusEnum.getPhaseName(bidSectionEnum));
                    }
                }
            });
            updateProject.setProjectStatus(stringJoiner.toString());
        }

        int update = projectMapper.updateById(updateProject);
        if (update > 0) {
            return true;
        }
        return false;
    }

    /**
     * 修改项目冗余字段，并发送消息给mq
     * @param sections
     */
    private void updateProjectPackageFieldAndSendMQ(List<ProjectBidSection> sections){
        Long lastProjectId = null;
        for (ProjectBidSection bid :sections) {
            // 获得当前标段
            ProjectBidSection projectBidSection = projectBidSectionService.getById(bid.getId());

            // 如果项目ID发生变化，先更新旧项目ID的项目状态
            Long projectId = projectBidSection.getProjectId();
            if (!projectId.equals(lastProjectId)) {
                if (lastProjectId != null) {
                    // 更新项目冗余字段
                    saveOrUpdateProjectPackageFieldDealWith(lastProjectId);
                    rabbitMqService.sendMq(projectId);
                }
                lastProjectId = projectId;
            }
        }
        if(lastProjectId != null) {
            // 更新项目冗余字段
            saveOrUpdateProjectPackageFieldDealWith(lastProjectId);
            rabbitMqService.sendMq(lastProjectId);
        }
    }

    @Override
    public Boolean updateProjectName(Long projectId, String projectName) {
        Project project = new Project();
        project.setId(projectId);
        project.setProjectName(projectName);
        return projectMapper.updateById(project) > 0;
    }

    @Override
    public Boolean updatePurchaseName(Long projectId, String purchaseName) {
        Project project = new Project();
        project.setId(projectId);
        project.setPurchaseName(purchaseName);
        return projectMapper.updateById(project) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateSaleEndTime(Long sectionId, String saleEndTime, Integer bidRound) {
        Boolean flag = projectBidSectionService.update(new LambdaUpdateWrapper<ProjectBidSection>().set(ProjectBidSection::getSaleEndTime, saleEndTime)
                .set(ProjectBidSection::getSaleEndTimeType, EndTimeTypeEnum.CLEAR_TIME.getType())
                .eq(ProjectBidSection::getId, sectionId));
        projectBidSectionRecordService.update(new LambdaUpdateWrapper<ProjectBidSectionRecord>().set(ProjectBidSectionRecord::getSaleEndTime, saleEndTime)
                .set(ProjectBidSectionRecord::getSaleEndTimeType, EndTimeTypeEnum.CLEAR_TIME.getType())
                .eq(ProjectBidSectionRecord::getSectionId, sectionId).eq(ProjectBidSectionRecord::getBidRound, bidRound));
        projectBidSectionService.updateDocSaleEndTime(sectionId, saleEndTime);
        return flag;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateSubmitEndTime(Long sectionId, String submitEndTime, Integer bidRound) {
        Boolean flag = projectBidSectionService.update(new LambdaUpdateWrapper<ProjectBidSection>().set(ProjectBidSection::getSubmitEndTime, submitEndTime)
                .set(ProjectBidSection::getSubmitEndTimeType, EndTimeTypeEnum.CLEAR_TIME.getType())
                .eq(ProjectBidSection::getId, sectionId));
        projectBidSectionRecordService.update(new LambdaUpdateWrapper<ProjectBidSectionRecord>().set(ProjectBidSectionRecord::getSubmitEndTime, submitEndTime)
                .set(ProjectBidSectionRecord::getSubmitEndTimeType, EndTimeTypeEnum.CLEAR_TIME.getType())
                .eq(ProjectBidSectionRecord::getSectionId, sectionId).eq(ProjectBidSectionRecord::getBidRound, bidRound));
        projectBidSectionService.updateDocSubmitEndTime(sectionId, submitEndTime);
        return flag;
    }

    /**
     * 查询ncc中的项目编号和项目的所在部门
     * @param sectionId
     * @return
     */
    @Override
    public ProjectShareInfoDto getProjectShareToNccInfoBySectionId(Long sectionId) {
        return projectBidSectionService.getProjectShareToNccInfoBySectionId(sectionId);
    }

}
