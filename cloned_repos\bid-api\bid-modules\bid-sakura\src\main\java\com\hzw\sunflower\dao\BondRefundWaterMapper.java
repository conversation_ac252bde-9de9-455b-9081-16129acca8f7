package com.hzw.sunflower.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hzw.sunflower.controller.response.BondSupplierInfoVo;
import com.hzw.sunflower.controller.response.BondSupplierVO;
import com.hzw.sunflower.controller.response.RefundListVo;
import com.hzw.sunflower.entity.BondRefund;
import com.hzw.sunflower.entity.BondRefundWater;
import com.hzw.sunflower.entity.condition.BondRefundCondition;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 保证金退还流水表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-27
 */
@Mapper
public interface BondRefundWaterMapper extends BaseMapper<BondRefundWater> {

}
