package com.hzw.sunflower.controller.response;

import com.hzw.sunflower.entity.BondRelation;
import com.hzw.sunflower.entity.ProjectBidSection;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2022/7/27 11:31
 * @description：标段流水关联详情返回实体类
 * @modified By：`
 * @version: 1.0
 */
@ApiModel(description = "标段流水关联详情返回实体类")
@Data
public class BondRelationSectionVo extends ProjectBidSection implements Serializable {

    @ApiModelProperty(value = "项目编号")
    private String purchaseNumber;

    @ApiModelProperty(value = "项目名称")
    private String purchaseName;

    @ApiModelProperty(value = "包件数量，0-无包，1-有")
    private String packageSegmentStatus;

    @ApiModelProperty(value = "代理机构")
    private String agentName;

    @ApiModelProperty(value = "项目负责人id")
    private Long projectUserId;

    @ApiModelProperty(value = "初始项目主键")
    private Long baseProjectId;

    @ApiModelProperty("关联状态 1.确认关联 2.已关联 3.异常关联 4.取消关联")
    private Integer relationStatus;

    @ApiModelProperty("是否开通下载权限，用于判断是否已购标 0.否 1.是")
    private Integer downloadFlag;

    @ApiModelProperty("是否展示取消关联按钮 1.是 2.否")
    private Integer showCancelBtn;

    @ApiModelProperty(value = "拆分金额")
    private BigDecimal amount;

    @ApiModelProperty(value = "保证金类型 0不收取 1定额 2比例 ")
    private Integer bondType;

    @ApiModelProperty(value = "保证金")
    private BigDecimal bond;

    @ApiModelProperty("关联list")
    List<BondRelation> relationList;

}
