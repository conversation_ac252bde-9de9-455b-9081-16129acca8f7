package com.hzw.sunflower.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;

import com.hzw.sunflower.common.BaseBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 招标文件编制模板表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-26
 */
@Data
@TableName("t_bid_doc_template")
@ApiModel(value = "BidDocTemplate对象", description = "招标文件编制模板表")
public class BidDocTemplate extends BaseBean implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("模板名称")
    @TableField("template_name")
    private String templateName;

    @ApiModelProperty("模板类型	1.公开招标-标准设备采购招标文件	2.邀请招标-标准设备采购招标文件	3.公开招标-标准设计招标文件	4.邀请招标-标准设计招标文件	5.公开招标-简要标准施工招标文件	6.邀请招标-简要标准施工招标文件")
    @TableField("template_type")
    private Integer templateType;

    @ApiModelProperty("状态 1.启用 2.停用")
    @TableField("status")
    private Integer status;

}
