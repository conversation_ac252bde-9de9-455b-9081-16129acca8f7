package com.hzw.sunflower.constant.constantenum;

/**
 * <AUTHOR>
 * @date ：Created in 2022/07/27 9:23
 * @description：申请类型
 * @version: 1.0
 */
public enum RefundTypeEnum {
    //退还类型：1 ：项目经理申请退还  2：异常保证金退还  3：供应商申请退还
    PROJECT_MANAGER_REFUND(1, "项目经理申请退还"),
    ABNORMAL_REFUND(2, "异常保证金退还"),
    SUPPLIER_REFUND(3, "供应商申请退还");

    private Integer type;
    private String desc;

    RefundTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
