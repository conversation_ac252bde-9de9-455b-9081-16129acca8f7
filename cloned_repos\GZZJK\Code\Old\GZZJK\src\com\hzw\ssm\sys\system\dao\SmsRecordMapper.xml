<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper 
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hzw.ssm.sys.system.dao.SmsRecordMapper">

	<!-- 如果需要查询缓存，将cache的注释去掉即可
	<cache />
	-->
	
	<!-- 批量添加短信记录 -->
	<insert id="insertSmsRecordList" parameterType="java.util.List">
		INSERT INTO T_SMS_RECORD (SMS_ID, PROJECT_ID, SMS_USER_ID, SMS_USER_NAME, SMS_MOBILE, SMS_CONTENT, SMS_TYPE, SMS_TIME, DELETE_FLAG,DECIMATIONBATCH,SMS_VARIETY,SMS_RESULT)
		<foreach collection="list" item="smsRecord" index="index" separator="union">
			select #{smsRecord.sms_id }, #{smsRecord.project_id, jdbcType=VARCHAR }, #{smsRecord.sms_user_id }, #{smsRecord.sms_user_name }, #{smsRecord.sms_mobile }, #{smsRecord.sms_content }, #{smsRecord.sms_type}, #{smsRecord.sms_time }, 0,#{smsRecord.decimationBatch, jdbcType=VARCHAR},#{smsRecord.sms_variety, jdbcType=NUMERIC},#{smsRecord.sms_result, jdbcType=VARCHAR} from dual
		</foreach>
	</insert>
	
	<!-- 添加短信记录 -->
	<insert id="insertSmsRecord" parameterType="SmsRecordEntity">
		INSERT INTO T_SMS_RECORD (SMS_ID, PROJECT_ID, SMS_USER_ID, SMS_USER_NAME, SMS_MOBILE, SMS_CONTENT, SMS_TYPE, SMS_TIME, DELETE_FLAG,DECIMATIONBATCH,SMS_VARIETY,SMS_RESULT,MW_MSGID,mw_result,CHANGE_RESUILT)
		VALUES (#{sms_id }, #{project_id, jdbcType=VARCHAR}, #{sms_user_id }, #{sms_user_name }, #{sms_mobile }, #{sms_content }, #{sms_type}, #{sms_time }, 0,#{decimationBatch, jdbcType=VARCHAR},#{sms_variety, jdbcType=NUMERIC},#{sms_result, jdbcType=VARCHAR},#{mw_msgid, jdbcType=VARCHAR},#{mw_msgid, jdbcType=VARCHAR},#{change_result, jdbcType=VARCHAR})
	</insert>
	
	<update id="updateSmsRecord" parameterType="SmsRecordEntity">
		update T_SMS_RECORD T 
		SET T.IS_ACCEPT = #{is_accept}
		WHERE T.SMS_ID = #{sms_id} 
	</update>
	<!-- 标记专家参加评标，但未收到短信，通知综合处 -->
	 <update id="updateInform" parameterType="SmsRecordEntity">
		update T_SMS_RECORD T 
		SET T.INFORM_ID=#{informId}
		WHERE T.SMS_ID in (
		<foreach collection="sms_ids" index="index" item="item"
			separator=",">
				${item}
		</foreach>
		)
	</update>
	
	<!-- 
	<select id="queryPageUnreceivedSmsRecord" parameterType="SmsRecordEntity" resultType="SmsRecordEntity">
	SELECT TP_S.DECIMATIONBATCH,TP_S.SMS_ID,TP_S.mw_msgid,TP_S.sms_mobile FROM
		(
			SELECT T.DECIMATIONBATCH,T.SMS_ID, TEMP_S.mw_msgid,TEMP_S.sms_mobile  
				,
				Row_Number() OVER (partition by t.sms_id ORDER BY t.expert_id desc) RN
				FROM T_EXTRACT_RESULT T 
				, (
				     SELECT S.SMS_ID,s.mw_msgid,s.sms_mobile FROM T_SMS_RECORD S 
				     WHERE S.SMS_VARIETY = 3
				     <![CDATA[
				     AND S.SMS_TIME+5/(24*60) < sysdate
				      ]]>
				       <![CDATA[
				     AND (S.IS_ACCEPT <>0 or S.IS_ACCEPT is  null)
				     ]]>
				) TEMP_S
				WHERE T.SMS_ID = TEMP_S.SMS_ID AND T.INFORM_ID IS NULL and t.expert_way in (1) 
				and t.sms_id is not null
				and t.join_status = 0
			) TP_S where TP_S.rn =1
	</select>
	 -->
	<select id="queryPageUnreceivedSmsRecord" parameterType="SmsRecordEntity" resultType="SmsRecordEntity">
			SELECT S.SMS_ID,s.mw_msgid,s.sms_mobile FROM T_SMS_RECORD S 
		     WHERE  S.INFORM_ID IS NULL
		     <![CDATA[
		     AND S.SMS_TIME+5/(24*60) < sysdate
		      ]]>
		       <![CDATA[
		     AND (S.IS_ACCEPT <>0 or S.IS_ACCEPT is  null)
		     ]]>
		     AND S.SMS_VARIETY = 3
	</select>
	<!-- 查询 -->
	<select id="queryPageUnreceivedSmsRecordList" parameterType="SmsRecordEntity" resultType="SmsRecordEntity">
			SELECT S.SMS_ID,s.mw_msgid,s.sms_mobile,s.IS_DISPOSE,s.decimationbatch FROM T_SMS_RECORD S 
		     WHERE 
		      <![CDATA[
		      (S.IS_ACCEPT <>0 or S.IS_ACCEPT is  null)
		     ]]>
		     <![CDATA[
		     AND S.SMS_TIME+5/(24*60) < sysdate
		      ]]>
		      <if test="is_dispose !=null and is_dispose !=''">
		    	AND S.IS_DISPOSE = #{is_dispose}
		     </if>
		      <if test="decimationBatch !=null and decimationBatch !=''">
		    	AND S.DECIMATIONBATCH = #{decimationBatch}
		     </if>
		      AND S.SMS_VARIETY = 3
	</select>
	<update id="updateSmsIsDispose" parameterType="SmsRecordEntity">
		update T_SMS_RECORD T 
		SET T.IS_DISPOSE = #{is_dispose}
		WHERE T.SMS_ID = #{sms_id} 
	</update>
</mapper>