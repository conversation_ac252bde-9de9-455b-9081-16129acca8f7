package com.hzw.system.api.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.hzw.common.basebean.web.domain.BaseEntity;
import com.hzw.common.core.constant.UserConstants;
import com.hzw.common.core.xss.Xss;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Email;
import javax.validation.constraints.Size;
import java.util.Date;
import java.util.List;

/**
 * 用户对象 sys_user
 *
 * <AUTHOR>
 */

@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("sys_user")
public class SysUser extends BaseEntity {

    /**
     * 用户ID
     */
    @TableId(value = "user_id")
    private String userId;

    /**
     * 部门ID
     */
    private String deptId;

    /**
     * 用户账号
     */
    @Xss(message = "用户账号不能包含脚本字符")
    @Size(min = 0, max = 100, message = "用户账号长度不能超过100个字符")
    private String userName;

    /**
     * 用户昵称
     */
    @Xss(message = "用户昵称不能包含脚本字符")
    @Size(min = 0, max = 30, message = "用户昵称长度不能超过30个字符")
    private String nickName;

    /**
     * 用户类型（sys_user系统用户）
     */
    private String userType;

    /**
     * 用户邮箱
     */
    //@Sensitive(strategy = SensitiveStrategy.EMAIL)
    @Email(message = "邮箱格式不正确")
    @Size(min = 0, max = 50, message = "邮箱长度不能超过50个字符")
    private String email;

    /**
     * 手机号码
     */
    //@Sensitive(strategy = SensitiveStrategy.PHONE)
    private String phonenumber;

    /**
     * 用户性别
     */
    private String sex;

    /**
     * 用户头像
     */
    private String avatar;

    /**
     * 密码
     */
    private String password;

    @JsonIgnore
    @JsonProperty
    public String getPassword() {
        return password;
    }

    /**
     * 帐号状态（0正常 1停用）
     */
    private Integer status;

    /**
     * 最后登录IP
     */
    private String loginIp;

    /**
     * 最后登录时间
     */
    private Date loginDate;

    /**
     * 身份证号
     */
    private String idNum;

    /**
     * 证件类型（1.身份证 2.军官证 3.护照 4.港澳台身份证
     */
    private Integer idType;

    /**
     * 第三方用户编号 （流程引擎同步的用户编号）
     */
    private String otherUserId;


    /**
     * 微信openid
     */
    private String openId;


    /**
     * 用户身份（1.采购人 2.供应商 3.运营）
     */
    private Integer identity;


    /**
     * 审核状态 0:待审核 1:审核通过 2:审核不通过
     */
    private Integer approveStatus;

    /**
     * 单位id
     */
    private String companyId;


    /**
     * 部门对象
     */
    @TableField(exist = false)
    private SysDept dept;

    /**
     * 角色对象
     */
    @TableField(exist = false)
    private List<SysRole> roles;

    /**
     * 角色组
     */
    @TableField(exist = false)
    private String[] roleIds;

    /**
     * 岗位组
     */
    @TableField(exist = false)
    private String[] postIds;

    /**
     * 数据权限 当前角色ID
     */
    @TableField(exist = false)
    private String roleId;

    /**
     * 企业授权函
     */
    private String certOfAuthFileId;

    /**
     * 数据来源 0:注册 1:初始化
     */
    private Integer dataSource;


    public SysUser(String userId) {
        this.userId = userId;
    }

    /**
     * 是否管理员
     */
    public boolean isAdmin() {
        return UserConstants.ADMIN_ID.equals(this.userId);
    }

    public enum UserApprovetStatusEnum {

        PENDING(0, "待审核"),

        PASS(1, "审核通过"),

        UNPASS(2, "审核不通过"),

        INCOMPLETE(3, "待完善企业信息");


        private Integer type;
        private String desc;

        UserApprovetStatusEnum(Integer type, String desc) {
            this.type = type;
            this.desc = desc;
        }

        public Integer getType() {
            return type;
        }

        public void setType(Integer type) {
            this.type = type;
        }

        public String getDesc() {
            return desc;
        }

        public void setDesc(String desc) {
            this.desc = desc;
        }
    }

}
