package com.hzw.sunflower.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Generated;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date ：Created in 2021/7/13 14:27
 * @description：审核内容返回格式
 * @version: 1.0
 */
@Data
@Generated
public class ReviewBidDocDTO implements Serializable {

    @ApiModelProperty(value = "标段ID", position = 1)
    private Long sectionId;

    @ApiModelProperty(value = "包号", position = 2)
    private Integer packageNumber;

    @ApiModelProperty(value = "标段名称", position = 3)
    private String packageName;

    @ApiModelProperty(value = "标书费类型 0不收取 1按套 2按标段/包", position = 4)
    private Integer tenderFeeType;

    @ApiModelProperty(value = "标书费金额", position = 5)
    private BigDecimal tenderFee;

    @ApiModelProperty(value = "保证金类型 0不收取 1定额 2比例", position = 6)
    private Integer bondType;

    @ApiModelProperty(value = "保证金金额", position = 7)
    private BigDecimal bond;

    @ApiModelProperty(value = "保证金金额比例", position = 8)
    private BigDecimal bondPercent;

    @ApiModelProperty(value = "文件发售方式(0:电子文件线上下载, 1:纸质文件不支持邮寄)", position = 9)
    private Integer releaseFileType;

    @ApiModelProperty(value = "文件发售审核方式(0:无需审核, 1:审核无需材料, 2:审核上传材料)", position = 10)
    private Integer reviewFileType;

    @ApiModelProperty(value = "材料清单", position = 11)
    private String materialList;

    @ApiModelProperty(value = "招标文件发售开始时间", position = 12)
    private Date saleStartTime;

    @ApiModelProperty(value = "招标文件发售结束时间类型（1：确定时间; 2.另行通知）", position = 13)
    private Integer saleEndTimeType;

    @ApiModelProperty(value = "招标文件发售结束时间", position = 14)
    private Date saleEndTime;

    @ApiModelProperty(value = "递交响应文件截止日期", position = 15)
    private Date submitEndTime;

    @ApiModelProperty(value = "标书费支付方式 0：线上 1：线下", position = 16)
    private Integer paymentType;

    @ApiModelProperty(value = "联系人", position = 17)
    private String contact;

    @ApiModelProperty(value = "联系电话", position = 18)
    private String phone;

    @ApiModelProperty(value = "招标文件oss文件ID", position = 19)
    private Long ossFileId;

    @ApiModelProperty(value = "招标文件key", position = 20)
    private String ossFileKey;

    @ApiModelProperty(value = "招标文件名称", position = 21)
    private String ossFileName;

    @ApiModelProperty(value = "项目ID", position = 22)
    private Long projectId;

    @ApiModelProperty(value = "招标文件状态", position = 23)
    private Integer noticeProgress;

    @ApiModelProperty(value = "开标地点", position = 24)
    private String openBidAddress;
}
