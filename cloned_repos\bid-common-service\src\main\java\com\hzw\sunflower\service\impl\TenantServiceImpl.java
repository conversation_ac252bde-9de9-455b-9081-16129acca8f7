package com.hzw.sunflower.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzw.sunflower.dao.TenantMapper;
import com.hzw.sunflower.entity.Tenant;
import com.hzw.sunflower.entity.condition.TenantCondition;
import com.hzw.sunflower.service.TenantService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 租户表 Service接口实现
 *
 * <AUTHOR>
 * @version 1.0.0 2021-04-07
 */
@Service
public class TenantServiceImpl extends ServiceImpl<TenantMapper, Tenant> implements TenantService {

    /**
     * 根据条件分页查询租户表 列表
     *
     * @param condition 查询条件
     * @return 分页数据
     */
    @Override
    public IPage<Tenant> findTenantByCondition(TenantCondition condition) {
        IPage<Tenant> page = condition.buildPage();
        QueryWrapper<Tenant> queryWrapper = condition.buildQueryWrapper(Tenant.class);
        return this.page(page, queryWrapper);
    }

    /**
     * 根据主键ID查询租户表 信息
     *
     * @param id 主键ID
     * @return 租户表 信息
     */
    @Override
    public Tenant getTenantById(Long id) {
        return this.getById(id);
    }

    /**
     * 新增租户表 信息
     *
     * @param tenant 租户表 信息
     * @return 是否成功
     */
    @Override
    public Boolean addTenant(Tenant tenant) {
        return this.save(tenant);
    }

    /**
     * 修改租户表 信息
     *
     * @param tenant 租户表 信息
     * @return 是否成功
     */
    @Override
    public Boolean updateTenant(Tenant tenant) {
        return this.updateById(tenant);
    }

    /**
     * 根据主键ID删除租户表
     *
     * @param id 主键ID
     * @return 是否成功
     */
    @Override
    public Boolean deleteTenantById(Long id) {
        return this.removeById(id);
    }

    /**
     * 根据主键ID列表批量删除租户表
     *
     * @param idList 主键ID列表
     * @return 是否成功
     */
    @Override
    public Boolean deleteTenantByIds(List<Long> idList) {
        return this.removeByIds(idList);
    }
}