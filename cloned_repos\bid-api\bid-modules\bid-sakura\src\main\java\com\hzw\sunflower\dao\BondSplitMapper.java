package com.hzw.sunflower.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hzw.sunflower.dto.BondSplitDto;
import com.hzw.sunflower.dto.BondSplitNccDto;
import com.hzw.sunflower.entity.BondSplit;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 保证金拆分 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-15
 */
@Mapper
public interface BondSplitMapper extends BaseMapper<BondSplit> {

    /**
     * 查询标包的拆分合计金额
     * @param sectionId
     * @param companyId
     * @return
     */
    BondSplit listBySectionId(@Param("sectionId") Long sectionId, @Param("companyId") Long companyId);

    /**
     * 根据流水id获取拆分数据
     * @param waterIds
     * @return
     */
    List<BondSplitNccDto> getByWaterIds(@Param("waterIds") List<Long> waterIds);

    /**
     * 根据流水查询最新一条非异常已删除拆分数据
     * @param waterId
     * @return
     */
    BondSplitNccDto queryLastDeleteByWaterId(@Param("waterId") Long waterId);

    /**
     * 异常关联时将拆分表和关联表关联上
     * @param waterId
     * @return
     */
    Boolean updateRelationId(@Param("waterId") Long waterId,@Param("relationId") Long relationId);

    /**
     * 异常关联时将拆分表和关联表关联上
     * @param waterId
     * @return
     */
    Boolean updateRelationIdNull(@Param("waterIds") List<Long> waterId);

    /**
     * 根据流水查询最新一条非异常已删除拆分数据
     * @param waterId
     * @return
     */
    List<BondSplitDto> queryLatestSplit(@Param("waterId") Long waterId);

    /**
     * 根据流水查询最新一条异常已删除拆分数据
     * @param waterId
     * @return
     */
    List<BondSplitDto> queryLatestSplitByRelationId(@Param("waterId") Long waterId,@Param("relationId") Long relationId);

    /**
     * 获取调整表头数据
     * @param waterId
     * @return
     */
    List<BondSplitNccDto> getHeadByWaterIds(@Param("waterIds") List<Long> waterId);

    /**
     * 批量修改异常数据
     * @param waterIds
     * @return
     */
    int updateSplitForExcept(@Param("waterIds") List<Long> waterIds);

    /**
     * 批量修改is_latest状态
     * @param waterIds
     * @return
     */
    int updateSplitForIsLatest(@Param("waterIds") List<Long> waterIds);

    /**
     * 根据关联流水标段查询最新一条非异常已删除拆分数据
     * @param waterId
     * @param companyId
     * @return
     */
    List<BondSplitNccDto> queryLastDeleteBySectionId(@Param("waterId") Long waterId, @Param("companyId") Long companyId);

}
