import "package:flutter/material.dart";

import 'package:flutter_main_app/src/constants/theme.dart';

class EmptyDataWidget extends StatelessWidget {
  const EmptyDataWidget({Key key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      // color: Colors.red,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: <Widget>[
          // Spacer(
          //   flex:2
          // ),
          Image.asset('assets/images/no_data.png'),
          Text(
            "当前没有搜索数据～",
            style: TextStyle(
              color: themeHintTextColor,
              fontSize: 14
            ),
          ),
          // Image.asset('/assets/images/no_data.png')
        ],
      ),
    );
  }
}