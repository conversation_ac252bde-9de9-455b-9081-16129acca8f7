package com.hzw.sunflower.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hzw.sunflower.common.BaseController;
import com.hzw.sunflower.common.annotation.RepeatSubmit;
import com.hzw.sunflower.controller.request.SealFileSignReq;
import com.hzw.sunflower.util.BeanListUtil;
import com.hzw.sunflower.common.Paging;
import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.constant.MessageConstants;
import com.hzw.sunflower.dto.SealFileDTO;
import com.hzw.sunflower.controller.request.SealFileREQ;
import com.hzw.sunflower.entity.SealFile;
import com.hzw.sunflower.entity.condition.SealFileCondition;
import com.hzw.sunflower.service.SealFileService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
* SealFileController
*/
@Api(tags = "SealFile服务")
@RestController
@RequestMapping("/sealFile")
public class SealFileController extends BaseController {
    @Autowired
    private SealFileService sealFileService;


    @ApiOperation(value = "完成签章")
    @RepeatSubmit
    @ApiImplicitParam(name = "req", value = "完成签章入参", required = true, dataType = "completeSignReq", paramType = "body")
    @PostMapping("/completeSign")
    public Result<SealFile> completeSign(@RequestBody SealFileSignReq req) {
        if (null == req) {
            return Result.failed(MessageConstants.PARAMS_NOT_NULL);
        }
        return sealFileService.completeSign(req, getJwtUser());
    }

}