package com.hzw.sunflower.constant.constantenum;


/**
 * 模板编码枚举
 *
 * <AUTHOR>
 * @date ：Created in 2021/5/12 10:02
 * @description：模板编码枚举
 * @modified By：`
 * @version: 1.0
 */
public enum TemplateCodeEnum {

    TENDER_REGISTER("SR-001", "","招标人注册"),
    SUPPLIER_REGISTER("SR-002", "","投标人/供应商注册"),
    AGENCY_REGISTER("SR-003", "","代理机构注册"),
    TENDER_LOGIN("SL-001", "","招标人登陆"),
    SUPPLIER_LOGIN("SL-002", "","投标人/供应商登陆"),
    AGENCY_LOGIN("SL-003", "","代理机构登陆"),
    INVITE_SUPPLIER("GB-001", "","邀请供应商"),
    SUPPLY_BULLETIN("GB-002", "","补充公告"),
    CLARIFY_ASK_FILE("GB-003", "","澄清答疑文件"),
    YCZT("YC", "YCZT","异常暂停"),
    YCZZ("YC", "YCZZ","异常终止"),
    YCTZ("YC", "YCTZ","中标通知书"),
    YCNTZ("YC", "YCNTZ","未中标通知书"),
    YCHF("YC", "YCHF","异常恢复"),
    CQXG("YC", "CQXG","澄清/修改"),
    ZYCTZ("YC", "ZYCTZ","通过资格预审通知"),
    ZYCNTZ("YC", "ZYCNTZ","未通过资格预审通知"),
    YCCJ("YC","YCCJ","通知供应商参加项目投标"),
    UPDATE_PHONE("UP-001", "","修改手机号"),
    VENDOR_CONCERN_NOTICE_NO_BAG("GZ-001", "","供应商关注短信通知项目经理(无包)"),
    VENDOR_CONCERN_NOTICE_HAVE_BAG("GZ-002", "","供应商关注短信通知项目经理(有包)"),
    YQZGYS("YC", "YQZGYS","邀请供应商资格预审"),
    CQYW("YC", "CQYW","提出澄清疑问"),
    YYHF("YC", "YYHF","异议回复"),
    TCYY("YC", "TCYY","提出异议"),
    TCYY_ZFCG("YC", "TCYY_ZFCG","提出异议(政府采购)"),
    TCYY_FZFCG("YC", "TCYY_FZFCG","提出异议(非政府采购)"),
    BCGG("YC", "BCGG","补充公告"),
    GZWXSH("YC", "GZWXSH","供应商关注（无需审核）"),
    GZSH("YC", "GZSH","供应商关注（需审核）"),
    SCCL("YC", "SCCL","供应商上传材料"),
    SCBSF("YC", "SCBSF","供应商上传标书费底单"),
    KTXZ("YC", "KTXZ","开通下载权限"),
    THBZJ("YC", "THBZJ","退还保证金"),
    SCBZJ("YC", "SCBZJ","供应商上传保证金底单"),
    CBTJSD("YC", "CBTJSD","参标情况预统计（手动）"),
    ZGYSTJSD("YC", "ZGYSTJSD","参与资格预审预统计（手动）"),
    CBTJ("YC", "CBTJ","参标情况预统计（自动）"),
    ZGYSTJ("YC", "ZGYSTJ","参与资格预审预统计（自动）"),
    KPSBTX("YC", "KPSBTX","开票/红冲失败提醒"),
    // 保证金相关模板
    NO_POSTSCRIPT("BOND", "NO_POSTSCRIPT","自动关联无附言"),
    ONLY_PROJECT("BOND", "ONLY_PROJECT","仅匹配项目"),
    VERIFY_FAIL("BOND", "VERIFY_FAIL","关联标段校验不通过"),
    BZJGLTX("BOND", "BZJGLTX","保证金关联提醒"),
    BZJSQGZBG("BOND", "BZJSQGZBG","保证金收取规则变更"),
    RELATION_SUCCESS("BOND", "RELATION_SUCCESS","关联成功"),
    WITHOUT_RELATION_60("BOND", "WITHOUT_RELATION_60","60天未关联"),
    YQZJ("YC","NE-001","邀请专家"),
    QXGZ("QX-001","","供应商取消关注"),
    XYWJDJTX("YC","XYWJDJTX","电子开标响应文件递交提醒"),
    KQJMTX("YC","KQJMTX","开启解密提醒"),
    KQCBTX("YC","KQCBTX","开启唱标提醒"),

    TZGYSKB("YC","TZGYSKB","通知供应商开标"),
    TZPSQD("YC","TZPSQD","通知专家评审签到"),
    PSCQDY("YC","PSCQDY","评审澄清答疑"),

    ZCTG("ZC","ZCTG","注册审核通过"),
    ZCBTG("ZC","ZCBTG","注册审核不通过"),

    // 报价相关
    QUOTED_PRICE_ENC("QR-001","","供应商报价提醒（加密）"),
    QUOTED_PRICE("QR-002","","供应商报价提醒（不加密）"),
    // 公告认领相关
    GGFBTZ("CLAIM","GGFBTZ","公告发布通知"),
    GGTBWJBATX("CLAIM","GGTBWJBATX","公告投标文件备案提醒"),
    CGWJSCTX("CLAIM","CGWJSCTX","采购文件上传提醒"),
    TBWJSCTX("CLAIM","TBWJSCTX","投标文件上传提醒"),
    CGJGSCTX("CLAIM","CGJGSCTX","采购结果上传提醒"),

    // 管理后台提示
    DYYC("reminder","R-01","第三方服务调用异常"),
    FWDQ("reminder","R-02","第三方服务到期"),
    CPBZ("reminder","R-03","服务器磁盘空间不足"),
    CSBZ("reminder","R-04","第三方服务剩余次数不足"),
    HLCXSBYJ("reminder","R-05","汇率查询失败预警"),

    // 质疑异议
    SPDB_FZFCG("OBJECTION","SPDB_FZFCG","质疑审批待办(非政府采购)"),
    SPDB_ZFCG("OBJECTION","SPDB_ZFCG","质疑审批待办(政府采购)"),

    JSWJ_ZFCG("OBJECTION","JSWJ_ZFCG","质疑接收文件(政府采购)"),
    JSWJ_FZFCG("OBJECTION","JSWJ_FZFCG","质疑接收文件(非政府采购)"),
    GZDB("OBJECTION","GZDB","质疑盖章文件待办"),

    ;






    /**
     * 一级编码
     */
    private String code;

    /**
     * 二级编码
     */
    private String secondCode;

    /**
     * 说明
     */
    private String msg;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getSecondCode() {
        return secondCode;
    }

    public void setSecondCode(String secondCode) {
        this.secondCode = secondCode;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    TemplateCodeEnum(String code, String secondCode, String msg) {
        this.code = code;
        this.secondCode = secondCode;
        this.msg = msg;
    }


}
