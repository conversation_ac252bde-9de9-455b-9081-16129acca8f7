<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AnalysisProjectProfileManager">
    <option name="PROJECT_PROFILE" value="Project Default" />
    <option name="USE_PROJECT_LEVEL_SETTINGS" value="false" />
    <scopes />
    <profiles>
      <profile profile_name="Project Default" version="1.0" is_locked="false">
        <coding_rule class="AM_CREATES_EMPTY_JAR_FILE_ENTRY" level="MAJOR" enabled="true" />
        <coding_rule class="AM_CREATES_EMPTY_ZIP_FILE_ENTRY" level="MAJOR" enabled="true" />
        <coding_rule class="AT_OPERATION_SEQUENCE_ON_CONCURRENT_ABSTRACTION" level="CRITICAL" enabled="false" />
        <coding_rule class="AbbreviationAsWordInNameCheck" level="MAJOR" enabled="false" />
        <coding_rule class="AbstractClassNameCheck" level="MAJOR" enabled="false" />
        <coding_rule class="AnnotationLocationCheck" level="MAJOR" enabled="false" />
        <coding_rule class="AnnotationOnSameLineCheck" level="MAJOR" enabled="false" />
        <coding_rule class="AnnotationUseStyleCheck" level="MAJOR" enabled="false" />
        <coding_rule class="AnonInnerLengthCheck" level="MAJOR" enabled="true" />
        <coding_rule class="ArrayTrailingCommaCheck" level="MAJOR" enabled="false" />
        <coding_rule class="ArrayTypeStyleCheck" level="MINOR" enabled="false" />
        <coding_rule class="AtclauseOrderCheck" level="MAJOR" enabled="false" />
        <coding_rule class="AvoidEscapedUnicodeCharactersCheck" level="MAJOR" enabled="false" />
        <coding_rule class="AvoidInlineConditionalsCheck" level="MINOR" enabled="false" />
        <coding_rule class="AvoidNestedBlocksCheck" level="MAJOR" enabled="false" />
        <coding_rule class="AvoidNoArgumentSuperConstructorCallCheck" level="MAJOR" enabled="false" />
        <coding_rule class="AvoidStarImportCheck" level="MINOR" enabled="false" />
        <coding_rule class="AvoidStaticImportCheck" level="MAJOR" enabled="false" />
        <coding_rule class="BAC_BAD_APPLET_CONSTRUCTOR" level="MAJOR" enabled="false" />
        <coding_rule class="BC_BAD_CAST_TO_ABSTRACT_COLLECTION" level="MAJOR" enabled="true" />
        <coding_rule class="BC_BAD_CAST_TO_CONCRETE_COLLECTION" level="CRITICAL" enabled="true" />
        <coding_rule class="BC_EQUALS_METHOD_SHOULD_WORK_FOR_ALL_OBJECTS" level="CRITICAL" enabled="true" />
        <coding_rule class="BC_IMPOSSIBLE_CAST" level="BLOCKER" enabled="true" />
        <coding_rule class="BC_IMPOSSIBLE_DOWNCAST" level="MAJOR" enabled="false" />
        <coding_rule class="BC_IMPOSSIBLE_DOWNCAST_OF_TOARRAY" level="MAJOR" enabled="false" />
        <coding_rule class="BC_IMPOSSIBLE_INSTANCEOF" level="CRITICAL" enabled="true" />
        <coding_rule class="BC_UNCONFIRMED_CAST" level="CRITICAL" enabled="true" />
        <coding_rule class="BC_UNCONFIRMED_CAST_OF_RETURN_VALUE" level="CRITICAL" enabled="false" />
        <coding_rule class="BC_VACUOUS_INSTANCEOF" level="CRITICAL" enabled="true" />
        <coding_rule class="BIT_ADD_OF_SIGNED_BYTE" level="CRITICAL" enabled="true" />
        <coding_rule class="BIT_AND" level="CRITICAL" enabled="true" />
        <coding_rule class="BIT_AND_ZZ" level="CRITICAL" enabled="true" />
        <coding_rule class="BIT_IOR" level="CRITICAL" enabled="true" />
        <coding_rule class="BIT_IOR_OF_SIGNED_BYTE" level="CRITICAL" enabled="true" />
        <coding_rule class="BIT_SIGNED_CHECK" level="CRITICAL" enabled="true" />
        <coding_rule class="BIT_SIGNED_CHECK_HIGH_BIT" level="CRITICAL" enabled="true" />
        <coding_rule class="BOA_BADLY_OVERRIDDEN_ADAPTER" level="CRITICAL" enabled="true" />
        <coding_rule class="BSHIFT_WRONG_ADD_PRIORITY" level="MAJOR" enabled="false" />
        <coding_rule class="BX_BOXING_IMMEDIATELY_UNBOXED" level="MAJOR" enabled="true" />
        <coding_rule class="BX_BOXING_IMMEDIATELY_UNBOXED_TO_PERFORM_COERCION" level="MAJOR" enabled="true" />
        <coding_rule class="BX_UNBOXED_AND_COERCED_FOR_TERNARY_OPERATOR" level="MAJOR" enabled="true" />
        <coding_rule class="BX_UNBOXING_IMMEDIATELY_REBOXED" level="CRITICAL" enabled="false" />
        <coding_rule class="BestPracticesAbstractClassWithoutAbstractMethod" level="MAJOR" enabled="false" />
        <coding_rule class="BestPracticesAccessorClassGeneration" level="MAJOR" enabled="false" />
        <coding_rule class="BestPracticesAccessorMethodGeneration" level="MAJOR" enabled="false" />
        <coding_rule class="BestPracticesArrayIsStoredDirectly" level="CRITICAL" enabled="true" />
        <coding_rule class="BestPracticesAvoidPrintStackTrace" level="MAJOR" enabled="true" />
        <coding_rule class="BestPracticesAvoidReassigningParameters" level="MAJOR" enabled="false" />
        <coding_rule class="BestPracticesAvoidStringBufferField" level="MAJOR" enabled="false" />
        <coding_rule class="BestPracticesAvoidUsingHardCodedIP" level="MAJOR" enabled="false" />
        <coding_rule class="BestPracticesCheckResultSet" level="MAJOR" enabled="false" />
        <coding_rule class="BestPracticesConstantsInInterface" level="MAJOR" enabled="false" />
        <coding_rule class="BestPracticesDefaultLabelNotLastInSwitchStmt" level="MAJOR" enabled="false" />
        <coding_rule class="BestPracticesDontNestJsfInJstlIteration" level="MAJOR" enabled="false" />
        <coding_rule class="BestPracticesForLoopCanBeForeach" level="MAJOR" enabled="false" />
        <coding_rule class="BestPracticesGuardDebugLogging" level="MAJOR" enabled="false" />
        <coding_rule class="BestPracticesGuardLogStatement" level="MAJOR" enabled="false" />
        <coding_rule class="BestPracticesGuardLogStatementJavaUtil" level="MAJOR" enabled="false" />
        <coding_rule class="BestPracticesJUnit4SuitesShouldUseSuiteAnnotation" level="MAJOR" enabled="false" />
        <coding_rule class="BestPracticesJUnit4TestShouldUseAfterAnnotation" level="MAJOR" enabled="false" />
        <coding_rule class="BestPracticesJUnit4TestShouldUseBeforeAnnotation" level="MAJOR" enabled="false" />
        <coding_rule class="BestPracticesJUnit4TestShouldUseTestAnnotation" level="MAJOR" enabled="false" />
        <coding_rule class="BestPracticesJUnitAssertionsShouldIncludeMessage" level="MAJOR" enabled="false" />
        <coding_rule class="BestPracticesJUnitTestContainsTooManyAsserts" level="MAJOR" enabled="false" />
        <coding_rule class="BestPracticesJUnitTestsShouldIncludeAssert" level="MAJOR" enabled="false" />
        <coding_rule class="BestPracticesJUnitUseExpected" level="MAJOR" enabled="false" />
        <coding_rule class="BestPracticesLooseCoupling" level="MAJOR" enabled="true" />
        <coding_rule class="BestPracticesMethodReturnsInternalArray" level="CRITICAL" enabled="false" />
        <coding_rule class="BestPracticesNoClassAttribute" level="MAJOR" enabled="false" />
        <coding_rule class="BestPracticesNoHtmlComments" level="MAJOR" enabled="false" />
        <coding_rule class="BestPracticesNoJspForward" level="MAJOR" enabled="false" />
        <coding_rule class="BestPracticesOneDeclarationPerLine" level="MAJOR" enabled="false" />
        <coding_rule class="BestPracticesPositionLiteralsFirstInCaseInsensitiveComparisons" level="MAJOR" enabled="false" />
        <coding_rule class="BestPracticesPositionLiteralsFirstInComparisons" level="MAJOR" enabled="false" />
        <coding_rule class="BestPracticesPreserveStackTrace" level="MAJOR" enabled="true" />
        <coding_rule class="BestPracticesReplaceEnumerationWithIterator" level="MAJOR" enabled="true" />
        <coding_rule class="BestPracticesReplaceHashtableWithMap" level="MAJOR" enabled="true" />
        <coding_rule class="BestPracticesReplaceVectorWithList" level="MAJOR" enabled="true" />
        <coding_rule class="BestPracticesSwitchStmtsShouldHaveDefault" level="MAJOR" enabled="false" />
        <coding_rule class="BestPracticesSystemPrintln" level="MAJOR" enabled="true" />
        <coding_rule class="BestPracticesUnusedFormalParameter" level="MAJOR" enabled="true" />
        <coding_rule class="BestPracticesUnusedImports" level="INFO" enabled="false" />
        <coding_rule class="BestPracticesUnusedLocalVariable" level="MAJOR" enabled="true" />
        <coding_rule class="BestPracticesUnusedPrivateField" level="MAJOR" enabled="true" />
        <coding_rule class="BestPracticesUnusedPrivateMethod" level="MAJOR" enabled="true" />
        <coding_rule class="BestPracticesUseAssertEqualsInsteadOfAssertTrue" level="MAJOR" enabled="false" />
        <coding_rule class="BestPracticesUseAssertNullInsteadOfAssertTrue" level="MAJOR" enabled="false" />
        <coding_rule class="BestPracticesUseAssertSameInsteadOfAssertTrue" level="MAJOR" enabled="false" />
        <coding_rule class="BestPracticesUseAssertTrueInsteadOfAssertEquals" level="MAJOR" enabled="false" />
        <coding_rule class="BestPracticesUseCollectionIsEmpty" level="MINOR" enabled="false" />
        <coding_rule class="BestPracticesUseVarargs" level="MAJOR" enabled="false" />
        <coding_rule class="BooleanExpressionComplexityCheck" level="MAJOR" enabled="true" />
        <coding_rule class="CAA_COVARIANT_ARRAY_ELEMENT_STORE" level="MAJOR" enabled="false" />
        <coding_rule class="CAA_COVARIANT_ARRAY_FIELD" level="MAJOR" enabled="false" />
        <coding_rule class="CAA_COVARIANT_ARRAY_LOCAL" level="MAJOR" enabled="false" />
        <coding_rule class="CAA_COVARIANT_ARRAY_RETURN" level="MAJOR" enabled="false" />
        <coding_rule class="CD_CIRCULAR_DEPENDENCY" level="MAJOR" enabled="false" />
        <coding_rule class="CI_CONFUSED_INHERITANCE" level="MINOR" enabled="true" />
        <coding_rule class="CNT_ROUGH_CONSTANT_VALUE" level="MAJOR" enabled="false" />
        <coding_rule class="CN_IDIOM" level="MAJOR" enabled="true" />
        <coding_rule class="CN_IDIOM_NO_SUPER_CALL" level="MAJOR" enabled="true" />
        <coding_rule class="CN_IMPLEMENTS_CLONE_BUT_NOT_CLONEABLE" level="MAJOR" enabled="true" />
        <coding_rule class="CO_ABSTRACT_SELF" level="MAJOR" enabled="true" />
        <coding_rule class="CO_COMPARETO_INCORRECT_FLOATING" level="MAJOR" enabled="false" />
        <coding_rule class="CO_COMPARETO_RESULTS_MIN_VALUE" level="CRITICAL" enabled="false" />
        <coding_rule class="CO_SELF_NO_OBJECT" level="MAJOR" enabled="true" />
        <coding_rule class="CatchParameterNameCheck" level="MAJOR" enabled="false" />
        <coding_rule class="ClassDataAbstractionCouplingCheck" level="MAJOR" enabled="false" />
        <coding_rule class="ClassFanOutComplexityCheck" level="MAJOR" enabled="false" />
        <coding_rule class="ClassMemberImpliedModifierCheck" level="MAJOR" enabled="false" />
        <coding_rule class="ClassTypeParameterNameCheck" level="MAJOR" enabled="false" />
        <coding_rule class="CodeStyleAbstractNaming" level="MAJOR" enabled="false" />
        <coding_rule class="CodeStyleAtLeastOneConstructor" level="MAJOR" enabled="false" />
        <coding_rule class="CodeStyleAvoidDollarSigns" level="MINOR" enabled="true" />
        <coding_rule class="CodeStyleAvoidFinalLocalVariable" level="MAJOR" enabled="false" />
        <coding_rule class="CodeStyleAvoidPrefixingMethodParameters" level="MAJOR" enabled="false" />
        <coding_rule class="CodeStyleAvoidProtectedFieldInFinalClass" level="MAJOR" enabled="false" />
        <coding_rule class="CodeStyleAvoidProtectedMethodInFinalClassNotExtending" level="MAJOR" enabled="false" />
        <coding_rule class="CodeStyleAvoidUsingNativeCode" level="MAJOR" enabled="false" />
        <coding_rule class="CodeStyleBooleanGetMethodName" level="MAJOR" enabled="false" />
        <coding_rule class="CodeStyleCallSuperInConstructor" level="MINOR" enabled="false" />
        <coding_rule class="CodeStyleClassNamingConventions" level="MAJOR" enabled="true" />
        <coding_rule class="CodeStyleCommentDefaultAccessModifier" level="MINOR" enabled="false" />
        <coding_rule class="CodeStyleConfusingTernary" level="MAJOR" enabled="false" />
        <coding_rule class="CodeStyleDefaultPackage" level="MINOR" enabled="false" />
        <coding_rule class="CodeStyleDontImportJavaLang" level="MINOR" enabled="true" />
        <coding_rule class="CodeStyleDuplicateImports" level="MINOR" enabled="false" />
        <coding_rule class="CodeStyleDuplicateJspImports" level="MAJOR" enabled="false" />
        <coding_rule class="CodeStyleEmptyMethodInAbstractClassShouldBeAbstract" level="MAJOR" enabled="false" />
        <coding_rule class="CodeStyleExtendsObject" level="MAJOR" enabled="false" />
        <coding_rule class="CodeStyleFieldDeclarationsShouldBeAtStartOfClass" level="MAJOR" enabled="false" />
        <coding_rule class="CodeStyleForLoopShouldBeWhileLoop" level="MINOR" enabled="false" />
        <coding_rule class="CodeStyleForLoopsMustUseBraces" level="MAJOR" enabled="true" />
        <coding_rule class="CodeStyleGenericsNaming" level="MAJOR" enabled="false" />
        <coding_rule class="CodeStyleIfElseStmtsMustUseBraces" level="MAJOR" enabled="true" />
        <coding_rule class="CodeStyleIfStmtsMustUseBraces" level="MAJOR" enabled="true" />
        <coding_rule class="CodeStyleLocalHomeNamingConvention" level="MAJOR" enabled="false" />
        <coding_rule class="CodeStyleLocalInterfaceSessionNamingConvention" level="MAJOR" enabled="false" />
        <coding_rule class="CodeStyleLocalVariableCouldBeFinal" level="MINOR" enabled="false" />
        <coding_rule class="CodeStyleLongVariable" level="MAJOR" enabled="false" />
        <coding_rule class="CodeStyleMDBAndSessionBeanNamingConvention" level="MAJOR" enabled="false" />
        <coding_rule class="CodeStyleMIsLeadingVariableName" level="MAJOR" enabled="false" />
        <coding_rule class="CodeStyleMethodArgumentCouldBeFinal" level="MINOR" enabled="false" />
        <coding_rule class="CodeStyleMethodNamingConventions" level="MAJOR" enabled="false" />
        <coding_rule class="CodeStyleMisleadingVariableName" level="MAJOR" enabled="false" />
        <coding_rule class="CodeStyleNoPackage" level="MAJOR" enabled="false" />
        <coding_rule class="CodeStyleOnlyOneReturn" level="MINOR" enabled="false" />
        <coding_rule class="CodeStylePackageCase" level="MAJOR" enabled="false" />
        <coding_rule class="CodeStylePrematureDeclaration" level="MAJOR" enabled="false" />
        <coding_rule class="CodeStyleRemoteInterfaceNamingConvention" level="MAJOR" enabled="false" />
        <coding_rule class="CodeStyleRemoteSessionInterfaceNamingConvention" level="MAJOR" enabled="false" />
        <coding_rule class="CodeStyleShortClassName" level="MAJOR" enabled="false" />
        <coding_rule class="CodeStyleShortMethodName" level="MAJOR" enabled="false" />
        <coding_rule class="CodeStyleShortVariable" level="MAJOR" enabled="false" />
        <coding_rule class="CodeStyleSuspiciousConstantFieldName" level="MAJOR" enabled="true" />
        <coding_rule class="CodeStyleTooManyStaticImports" level="MAJOR" enabled="false" />
        <coding_rule class="CodeStyleUnnecessaryConstructor" level="MAJOR" enabled="false" />
        <coding_rule class="CodeStyleUnnecessaryFinalModifier" level="INFO" enabled="false" />
        <coding_rule class="CodeStyleUnnecessaryFullyQualifiedName" level="MAJOR" enabled="false" />
        <coding_rule class="CodeStyleUnnecessaryLocalBeforeReturn" level="MAJOR" enabled="true" />
        <coding_rule class="CodeStyleUnnecessaryModifier" level="MAJOR" enabled="false" />
        <coding_rule class="CodeStyleUnnecessaryParentheses" level="MINOR" enabled="false" />
        <coding_rule class="CodeStyleUnnecessaryReturn" level="MINOR" enabled="false" />
        <coding_rule class="CodeStyleUselessParentheses" level="MAJOR" enabled="false" />
        <coding_rule class="CodeStyleUselessQualifiedThis" level="MAJOR" enabled="false" />
        <coding_rule class="CodeStyleVariableNamingConventions" level="MAJOR" enabled="false" />
        <coding_rule class="CodeStyleWhileLoopsMustUseBraces" level="MAJOR" enabled="true" />
        <coding_rule class="CommentsIndentationCheck" level="MAJOR" enabled="false" />
        <coding_rule class="ConstantNameCheck" level="MINOR" enabled="true" />
        <coding_rule class="CovariantEqualsCheck" level="CRITICAL" enabled="false" />
        <coding_rule class="CustomImportOrderCheck" level="MAJOR" enabled="false" />
        <coding_rule class="CyclomaticComplexityCheck" level="MAJOR" enabled="true" />
        <coding_rule class="DB_DUPLICATE_BRANCHES" level="CRITICAL" enabled="true" />
        <coding_rule class="DB_DUPLICATE_SWITCH_CLAUSES" level="CRITICAL" enabled="true" />
        <coding_rule class="DC_DOUBLECHECK" level="MAJOR" enabled="true" />
        <coding_rule class="DC_PARTIALLY_CONSTRUCTED" level="MAJOR" enabled="false" />
        <coding_rule class="DE_MIGHT_DROP" level="MAJOR" enabled="true" />
        <coding_rule class="DE_MIGHT_IGNORE" level="MAJOR" enabled="true" />
        <coding_rule class="DLS_DEAD_LOCAL_INCREMENT_IN_RETURN" level="MAJOR" enabled="false" />
        <coding_rule class="DLS_DEAD_LOCAL_STORE" level="CRITICAL" enabled="true" />
        <coding_rule class="DLS_DEAD_LOCAL_STORE_IN_RETURN" level="CRITICAL" enabled="true" />
        <coding_rule class="DLS_DEAD_LOCAL_STORE_OF_NULL" level="CRITICAL" enabled="true" />
        <coding_rule class="DLS_DEAD_LOCAL_STORE_SHADOWS_FIELD" level="CRITICAL" enabled="false" />
        <coding_rule class="DLS_DEAD_STORE_OF_CLASS_LITERAL" level="CRITICAL" enabled="true" />
        <coding_rule class="DLS_OVERWRITTEN_INCREMENT" level="CRITICAL" enabled="true" />
        <coding_rule class="DL_SYNCHRONIZATION_ON_BOOLEAN" level="CRITICAL" enabled="true" />
        <coding_rule class="DL_SYNCHRONIZATION_ON_BOXED_PRIMITIVE" level="CRITICAL" enabled="true" />
        <coding_rule class="DL_SYNCHRONIZATION_ON_SHARED_CONSTANT" level="CRITICAL" enabled="true" />
        <coding_rule class="DL_SYNCHRONIZATION_ON_UNSHARED_BOXED_PRIMITIVE" level="CRITICAL" enabled="true" />
        <coding_rule class="DMI_ANNOTATION_IS_NOT_VISIBLE_TO_REFLECTION" level="MAJOR" enabled="true" />
        <coding_rule class="DMI_ARGUMENTS_WRONG_ORDER" level="CRITICAL" enabled="false" />
        <coding_rule class="DMI_BAD_MONTH" level="CRITICAL" enabled="true" />
        <coding_rule class="DMI_BIGDECIMAL_CONSTRUCTED_FROM_DOUBLE" level="CRITICAL" enabled="false" />
        <coding_rule class="DMI_BLOCKING_METHODS_ON_URL" level="BLOCKER" enabled="true" />
        <coding_rule class="DMI_CALLING_NEXT_FROM_HASNEXT" level="CRITICAL" enabled="true" />
        <coding_rule class="DMI_COLLECTIONS_SHOULD_NOT_CONTAIN_THEMSELVES" level="CRITICAL" enabled="true" />
        <coding_rule class="DMI_COLLECTION_OF_URLS" level="BLOCKER" enabled="true" />
        <coding_rule class="DMI_CONSTANT_DB_PASSWORD" level="BLOCKER" enabled="true" />
        <coding_rule class="DMI_DOH" level="CRITICAL" enabled="false" />
        <coding_rule class="DMI_EMPTY_DB_PASSWORD" level="CRITICAL" enabled="true" />
        <coding_rule class="DMI_ENTRY_SETS_MAY_REUSE_ENTRY_OBJECTS" level="CRITICAL" enabled="false" />
        <coding_rule class="DMI_FUTILE_ATTEMPT_TO_CHANGE_MAXPOOL_SIZE_OF_SCHEDULED_THREAD_POOL_EXECUTOR" level="MINOR" enabled="true" />
        <coding_rule class="DMI_HARDCODED_ABSOLUTE_FILENAME" level="CRITICAL" enabled="true" />
        <coding_rule class="DMI_INVOKING_HASHCODE_ON_ARRAY" level="CRITICAL" enabled="true" />
        <coding_rule class="DMI_INVOKING_TOSTRING_ON_ANONYMOUS_ARRAY" level="CRITICAL" enabled="true" />
        <coding_rule class="DMI_INVOKING_TOSTRING_ON_ARRAY" level="CRITICAL" enabled="true" />
        <coding_rule class="DMI_LONG_BITS_TO_DOUBLE_INVOKED_ON_INT" level="CRITICAL" enabled="true" />
        <coding_rule class="DMI_NONSERIALIZABLE_OBJECT_WRITTEN" level="CRITICAL" enabled="true" />
        <coding_rule class="DMI_RANDOM_USED_ONLY_ONCE" level="CRITICAL" enabled="true" />
        <coding_rule class="DMI_SCHEDULED_THREAD_POOL_EXECUTOR_WITH_ZERO_CORE_THREADS" level="MINOR" enabled="true" />
        <coding_rule class="DMI_THREAD_PASSED_WHERE_RUNNABLE_EXPECTED" level="MAJOR" enabled="true" />
        <coding_rule class="DMI_UNSUPPORTED_METHOD" level="MAJOR" enabled="true" />
        <coding_rule class="DMI_USELESS_SUBSTRING" level="CRITICAL" enabled="true" />
        <coding_rule class="DMI_USING_REMOVEALL_TO_CLEAR_COLLECTION" level="CRITICAL" enabled="true" />
        <coding_rule class="DMI_VACUOUS_CALL_TO_EASYMOCK_METHOD" level="MINOR" enabled="true" />
        <coding_rule class="DMI_VACUOUS_SELF_COLLECTION_CALL" level="CRITICAL" enabled="true" />
        <coding_rule class="DM_BOOLEAN_CTOR" level="MAJOR" enabled="true" />
        <coding_rule class="DM_BOXED_PRIMITIVE_FOR_COMPARE" level="MAJOR" enabled="false" />
        <coding_rule class="DM_BOXED_PRIMITIVE_FOR_PARSING" level="MAJOR" enabled="false" />
        <coding_rule class="DM_BOXED_PRIMITIVE_TOSTRING" level="MAJOR" enabled="true" />
        <coding_rule class="DM_CONVERT_CASE" level="INFO" enabled="true" />
        <coding_rule class="DM_DEFAULT_ENCODING" level="CRITICAL" enabled="false" />
        <coding_rule class="DM_EXIT" level="MAJOR" enabled="true" />
        <coding_rule class="DM_FP_NUMBER_CTOR" level="MAJOR" enabled="true" />
        <coding_rule class="DM_GC" level="MAJOR" enabled="true" />
        <coding_rule class="DM_INVALID_MIN_MAX" level="MAJOR" enabled="false" />
        <coding_rule class="DM_MONITOR_WAIT_ON_CONDITION" level="MAJOR" enabled="true" />
        <coding_rule class="DM_NEW_FOR_GETCLASS" level="MAJOR" enabled="true" />
        <coding_rule class="DM_NEXTINT_VIA_NEXTDOUBLE" level="MAJOR" enabled="true" />
        <coding_rule class="DM_NUMBER_CTOR" level="CRITICAL" enabled="true" />
        <coding_rule class="DM_RUN_FINALIZERS_ON_EXIT" level="MAJOR" enabled="true" />
        <coding_rule class="DM_STRING_CTOR" level="MAJOR" enabled="true" />
        <coding_rule class="DM_STRING_TOSTRING" level="INFO" enabled="true" />
        <coding_rule class="DM_STRING_VOID_CTOR" level="MAJOR" enabled="true" />
        <coding_rule class="DM_USELESS_THREAD" level="MAJOR" enabled="true" />
        <coding_rule class="DP_CREATE_CLASSLOADER_INSIDE_DO_PRIVILEGED" level="MAJOR" enabled="true" />
        <coding_rule class="DP_DO_INSIDE_DO_PRIVILEGED" level="MAJOR" enabled="true" />
        <coding_rule class="DeclarationOrderCheck" level="INFO" enabled="false" />
        <coding_rule class="DefaultComesLastCheck" level="MAJOR" enabled="true" />
        <coding_rule class="DescendantTokenCheck" level="MAJOR" enabled="false" />
        <coding_rule class="DesignAbstractClassWithoutAnyMethod" level="MAJOR" enabled="false" />
        <coding_rule class="DesignAvoidCatchingGenericException" level="MAJOR" enabled="false" />
        <coding_rule class="DesignAvoidDeeplyNestedIfStmts" level="MAJOR" enabled="false" />
        <coding_rule class="DesignAvoidRethrowingException" level="MAJOR" enabled="true" />
        <coding_rule class="DesignAvoidThrowingNewInstanceOfSameException" level="MAJOR" enabled="false" />
        <coding_rule class="DesignAvoidThrowingNullPointerException" level="MAJOR" enabled="true" />
        <coding_rule class="DesignAvoidThrowingRawExceptionTypes" level="MAJOR" enabled="true" />
        <coding_rule class="DesignClassWithOnlyPrivateConstructorsShouldBeFinal" level="MAJOR" enabled="false" />
        <coding_rule class="DesignCollapsibleIfStatements" level="MINOR" enabled="true" />
        <coding_rule class="DesignCouplingBetweenObjects" level="MAJOR" enabled="false" />
        <coding_rule class="DesignCyclomaticComplexity" level="MAJOR" enabled="false" />
        <coding_rule class="DesignDataClass" level="MAJOR" enabled="false" />
        <coding_rule class="DesignDoNotExtendJavaLangError" level="MAJOR" enabled="false" />
        <coding_rule class="DesignExceptionAsFlowControl" level="MAJOR" enabled="true" />
        <coding_rule class="DesignExcessiveClassLength" level="MAJOR" enabled="false" />
        <coding_rule class="DesignExcessiveImports" level="MAJOR" enabled="false" />
        <coding_rule class="DesignExcessiveMethodLength" level="MAJOR" enabled="false" />
        <coding_rule class="DesignExcessiveParameterList" level="MAJOR" enabled="false" />
        <coding_rule class="DesignExcessivePublicCount" level="MAJOR" enabled="false" />
        <coding_rule class="DesignFinalFieldCouldBeStatic" level="MINOR" enabled="true" />
        <coding_rule class="DesignForExtensionCheck" level="MINOR" enabled="true" />
        <coding_rule class="DesignGodClass" level="MAJOR" enabled="false" />
        <coding_rule class="DesignImmutableField" level="MAJOR" enabled="false" />
        <coding_rule class="DesignLawOfDemeter" level="MAJOR" enabled="false" />
        <coding_rule class="DesignLogicInversion" level="MAJOR" enabled="false" />
        <coding_rule class="DesignLoosePackageCoupling" level="MAJOR" enabled="false" />
        <coding_rule class="DesignModifiedCyclomaticComplexity" level="MAJOR" enabled="false" />
        <coding_rule class="DesignNPathComplexity" level="MAJOR" enabled="false" />
        <coding_rule class="DesignNcssConstructorCount" level="MAJOR" enabled="false" />
        <coding_rule class="DesignNcssCount" level="MAJOR" enabled="false" />
        <coding_rule class="DesignNcssMethodCount" level="MAJOR" enabled="true" />
        <coding_rule class="DesignNcssTypeCount" level="MAJOR" enabled="true" />
        <coding_rule class="DesignNoInlineScript" level="MAJOR" enabled="false" />
        <coding_rule class="DesignNoInlineStyleInformation" level="MAJOR" enabled="false" />
        <coding_rule class="DesignNoLongScripts" level="MAJOR" enabled="false" />
        <coding_rule class="DesignNoScriptlets" level="MAJOR" enabled="false" />
        <coding_rule class="DesignSignatureDeclareThrowsException" level="MAJOR" enabled="true" />
        <coding_rule class="DesignSimplifiedTernary" level="MAJOR" enabled="false" />
        <coding_rule class="DesignSimplifyBooleanAssertion" level="MAJOR" enabled="false" />
        <coding_rule class="DesignSimplifyBooleanExpressions" level="MINOR" enabled="false" />
        <coding_rule class="DesignSimplifyBooleanReturns" level="MINOR" enabled="false" />
        <coding_rule class="DesignSimplifyConditional" level="MAJOR" enabled="true" />
        <coding_rule class="DesignSingularField" level="MINOR" enabled="true" />
        <coding_rule class="DesignStdCyclomaticComplexity" level="MAJOR" enabled="false" />
        <coding_rule class="DesignSwitchDensity" level="MAJOR" enabled="false" />
        <coding_rule class="DesignTooManyFields" level="MAJOR" enabled="false" />
        <coding_rule class="DesignTooManyMethods" level="MAJOR" enabled="false" />
        <coding_rule class="DesignUseObjectForClearerAPI" level="MAJOR" enabled="false" />
        <coding_rule class="DesignUseUtilityClass" level="MAJOR" enabled="false" />
        <coding_rule class="DesignUselessOverridingMethod" level="MAJOR" enabled="true" />
        <coding_rule class="DocumentationCommentContent" level="MAJOR" enabled="false" />
        <coding_rule class="DocumentationCommentRequired" level="MAJOR" enabled="false" />
        <coding_rule class="DocumentationCommentSize" level="MAJOR" enabled="false" />
        <coding_rule class="DocumentationUncommentedEmptyConstructor" level="MAJOR" enabled="false" />
        <coding_rule class="DocumentationUncommentedEmptyMethodBody" level="MAJOR" enabled="false" />
        <coding_rule class="EC_ARRAY_AND_NONARRAY" level="CRITICAL" enabled="true" />
        <coding_rule class="EC_BAD_ARRAY_COMPARE" level="CRITICAL" enabled="true" />
        <coding_rule class="EC_INCOMPATIBLE_ARRAY_COMPARE" level="MAJOR" enabled="false" />
        <coding_rule class="EC_NULL_ARG" level="CRITICAL" enabled="true" />
        <coding_rule class="EC_UNRELATED_CLASS_AND_INTERFACE" level="CRITICAL" enabled="true" />
        <coding_rule class="EC_UNRELATED_INTERFACES" level="CRITICAL" enabled="true" />
        <coding_rule class="EC_UNRELATED_TYPES" level="CRITICAL" enabled="true" />
        <coding_rule class="EC_UNRELATED_TYPES_USING_POINTER_EQUALITY" level="CRITICAL" enabled="true" />
        <coding_rule class="EI_EXPOSE_REP" level="MAJOR" enabled="true" />
        <coding_rule class="EI_EXPOSE_REP2" level="MAJOR" enabled="true" />
        <coding_rule class="EI_EXPOSE_STATIC_REP2" level="MAJOR" enabled="true" />
        <coding_rule class="EQ_ABSTRACT_SELF" level="MAJOR" enabled="true" />
        <coding_rule class="EQ_ALWAYS_FALSE" level="BLOCKER" enabled="true" />
        <coding_rule class="EQ_ALWAYS_TRUE" level="BLOCKER" enabled="true" />
        <coding_rule class="EQ_CHECK_FOR_OPERAND_NOT_COMPATIBLE_WITH_THIS" level="MAJOR" enabled="true" />
        <coding_rule class="EQ_COMPARETO_USE_OBJECT_EQUALS" level="CRITICAL" enabled="true" />
        <coding_rule class="EQ_COMPARING_CLASS_NAMES" level="MAJOR" enabled="true" />
        <coding_rule class="EQ_DOESNT_OVERRIDE_EQUALS" level="MAJOR" enabled="false" />
        <coding_rule class="EQ_DONT_DEFINE_EQUALS_FOR_ENUM" level="MAJOR" enabled="true" />
        <coding_rule class="EQ_GETCLASS_AND_CLASS_CONSTANT" level="CRITICAL" enabled="true" />
        <coding_rule class="EQ_OTHER_NO_OBJECT" level="MAJOR" enabled="true" />
        <coding_rule class="EQ_OTHER_USE_OBJECT" level="MAJOR" enabled="true" />
        <coding_rule class="EQ_OVERRIDING_EQUALS_NOT_SYMMETRIC" level="MAJOR" enabled="true" />
        <coding_rule class="EQ_SELF_NO_OBJECT" level="MAJOR" enabled="true" />
        <coding_rule class="EQ_SELF_USE_OBJECT" level="MAJOR" enabled="true" />
        <coding_rule class="EQ_UNUSUAL" level="MINOR" enabled="true" />
        <coding_rule class="ES_COMPARING_PARAMETER_STRING_WITH_EQ" level="MAJOR" enabled="true" />
        <coding_rule class="ES_COMPARING_STRINGS_WITH_EQ" level="MAJOR" enabled="true" />
        <coding_rule class="ESync_EMPTY_SYNC" level="MAJOR" enabled="true" />
        <coding_rule class="EmptyBlockCheck" level="MAJOR" enabled="false" />
        <coding_rule class="EmptyCatchBlockCheck" level="MAJOR" enabled="false" />
        <coding_rule class="EmptyForInitializerPadCheck" level="MINOR" enabled="false" />
        <coding_rule class="EmptyForIteratorPadCheck" level="MINOR" enabled="false" />
        <coding_rule class="EmptyLineSeparatorCheck" level="MAJOR" enabled="false" />
        <coding_rule class="EmptyStatementCheck" level="MINOR" enabled="true" />
        <coding_rule class="EqualsAvoidNullCheck" level="CRITICAL" enabled="false" />
        <coding_rule class="EqualsHashCodeCheck" level="CRITICAL" enabled="true" />
        <coding_rule class="ErrorProneAssignmentInOperand" level="MAJOR" enabled="false" />
        <coding_rule class="ErrorProneAssignmentToNonFinalStatic" level="MAJOR" enabled="false" />
        <coding_rule class="ErrorProneAvoidAccessibilityAlteration" level="MAJOR" enabled="false" />
        <coding_rule class="ErrorProneAvoidAssertAsIdentifier" level="MAJOR" enabled="true" />
        <coding_rule class="ErrorProneAvoidBranchingStatementAsLastInLoop" level="MAJOR" enabled="false" />
        <coding_rule class="ErrorProneAvoidCallingFinalize" level="MAJOR" enabled="true" />
        <coding_rule class="ErrorProneAvoidCatchingNPE" level="MAJOR" enabled="true" />
        <coding_rule class="ErrorProneAvoidCatchingThrowable" level="CRITICAL" enabled="true" />
        <coding_rule class="ErrorProneAvoidDecimalLiteralsInBigDecimalConstructor" level="MAJOR" enabled="true" />
        <coding_rule class="ErrorProneAvoidDuplicateLiterals" level="MAJOR" enabled="true" />
        <coding_rule class="ErrorProneAvoidEnumAsIdentifier" level="MAJOR" enabled="true" />
        <coding_rule class="ErrorProneAvoidFieldNameMatchingMethodName" level="MAJOR" enabled="false" />
        <coding_rule class="ErrorProneAvoidFieldNameMatchingTypeName" level="MAJOR" enabled="false" />
        <coding_rule class="ErrorProneAvoidInstanceofChecksInCatchClause" level="MINOR" enabled="true" />
        <coding_rule class="ErrorProneAvoidLiteralsInIfCondition" level="MAJOR" enabled="false" />
        <coding_rule class="ErrorProneAvoidLosingExceptionInformation" level="MAJOR" enabled="false" />
        <coding_rule class="ErrorProneAvoidMultipleUnaryOperators" level="MAJOR" enabled="false" />
        <coding_rule class="ErrorProneAvoidUsingOctalValues" level="MAJOR" enabled="false" />
        <coding_rule class="ErrorProneBadComparison" level="MAJOR" enabled="false" />
        <coding_rule class="ErrorProneBeanMembersShouldSerialize" level="MAJOR" enabled="false" />
        <coding_rule class="ErrorProneBrokenNullCheck" level="CRITICAL" enabled="true" />
        <coding_rule class="ErrorProneCallSuperFirst" level="MAJOR" enabled="false" />
        <coding_rule class="ErrorProneCallSuperLast" level="MAJOR" enabled="false" />
        <coding_rule class="ErrorProneCheckSkipResult" level="MAJOR" enabled="false" />
        <coding_rule class="ErrorProneClassCastExceptionWithToArray" level="MAJOR" enabled="true" />
        <coding_rule class="ErrorProneCloneMethodMustBePublic" level="MAJOR" enabled="false" />
        <coding_rule class="ErrorProneCloneMethodMustImplementCloneable" level="MAJOR" enabled="false" />
        <coding_rule class="ErrorProneCloneMethodReturnTypeMustMatchClassName" level="MAJOR" enabled="false" />
        <coding_rule class="ErrorProneCloneThrowsCloneNotSupportedException" level="MAJOR" enabled="true" />
        <coding_rule class="ErrorProneCloseResource" level="MAJOR" enabled="true" />
        <coding_rule class="ErrorProneCompareObjectsWithEquals" level="MAJOR" enabled="true" />
        <coding_rule class="ErrorProneConstructorCallsOverridableMethod" level="MAJOR" enabled="true" />
        <coding_rule class="ErrorProneDataflowAnomalyAnalysis" level="MAJOR" enabled="false" />
        <coding_rule class="ErrorProneDoNotCallGarbageCollectionExplicitly" level="CRITICAL" enabled="false" />
        <coding_rule class="ErrorProneDoNotCallSystemExit" level="MAJOR" enabled="false" />
        <coding_rule class="ErrorProneDoNotHardCodeSDCard" level="MAJOR" enabled="false" />
        <coding_rule class="ErrorProneDoNotThrowExceptionInFinally" level="MAJOR" enabled="false" />
        <coding_rule class="ErrorProneDontImportSun" level="MINOR" enabled="true" />
        <coding_rule class="ErrorProneDontUseFloatTypeForLoopIndices" level="MAJOR" enabled="false" />
        <coding_rule class="ErrorProneEmptyCatchBlock" level="CRITICAL" enabled="false" />
        <coding_rule class="ErrorProneEmptyFinalizer" level="MAJOR" enabled="true" />
        <coding_rule class="ErrorProneEmptyFinallyBlock" level="CRITICAL" enabled="true" />
        <coding_rule class="ErrorProneEmptyIfStmt" level="CRITICAL" enabled="true" />
        <coding_rule class="ErrorProneEmptyInitializer" level="MAJOR" enabled="false" />
        <coding_rule class="ErrorProneEmptyStatementBlock" level="MAJOR" enabled="false" />
        <coding_rule class="ErrorProneEmptyStatementNotInLoop" level="MAJOR" enabled="false" />
        <coding_rule class="ErrorProneEmptyStaticInitializer" level="MAJOR" enabled="true" />
        <coding_rule class="ErrorProneEmptySwitchStatements" level="MAJOR" enabled="true" />
        <coding_rule class="ErrorProneEmptySynchronizedBlock" level="CRITICAL" enabled="true" />
        <coding_rule class="ErrorProneEmptyTryBlock" level="MAJOR" enabled="true" />
        <coding_rule class="ErrorProneEmptyWhileStmt" level="CRITICAL" enabled="true" />
        <coding_rule class="ErrorProneEqualsNull" level="CRITICAL" enabled="true" />
        <coding_rule class="ErrorProneFinalizeDoesNotCallSuperFinalize" level="MAJOR" enabled="true" />
        <coding_rule class="ErrorProneFinalizeOnlyCallsSuperFinalize" level="MAJOR" enabled="false" />
        <coding_rule class="ErrorProneFinalizeOverloaded" level="MAJOR" enabled="true" />
        <coding_rule class="ErrorProneFinalizeShouldBeProtected" level="MAJOR" enabled="false" />
        <coding_rule class="ErrorProneIdempotentOperations" level="MAJOR" enabled="true" />
        <coding_rule class="ErrorProneImportFromSamePackage" level="MINOR" enabled="false" />
        <coding_rule class="ErrorProneInstantiationToGetClass" level="MAJOR" enabled="true" />
        <coding_rule class="ErrorProneInvalidSlf4jMessageFormat" level="MINOR" enabled="false" />
        <coding_rule class="ErrorProneJUnitSpelling" level="MAJOR" enabled="false" />
        <coding_rule class="ErrorProneJUnitStaticSuite" level="MAJOR" enabled="false" />
        <coding_rule class="ErrorProneJspEncoding" level="MAJOR" enabled="false" />
        <coding_rule class="ErrorProneJumbledIncrementer" level="MAJOR" enabled="false" />
        <coding_rule class="ErrorProneLoggerIsNotStaticFinal" level="MAJOR" enabled="false" />
        <coding_rule class="ErrorProneMethodWithSameNameAsEnclosingClass" level="MAJOR" enabled="true" />
        <coding_rule class="ErrorProneMisplacedNullCheck" level="CRITICAL" enabled="false" />
        <coding_rule class="ErrorProneMissingBreakInSwitch" level="CRITICAL" enabled="false" />
        <coding_rule class="ErrorProneMissingSerialVersionUID" level="MAJOR" enabled="false" />
        <coding_rule class="ErrorProneMissingStaticMethodInNonInstantiatableClass" level="MAJOR" enabled="true" />
        <coding_rule class="ErrorProneMoreThanOneLogger" level="MAJOR" enabled="false" />
        <coding_rule class="ErrorProneNonCaseLabelInSwitchStatement" level="MAJOR" enabled="false" />
        <coding_rule class="ErrorProneNonStaticInitializer" level="MAJOR" enabled="false" />
        <coding_rule class="ErrorProneNullAssignment" level="MAJOR" enabled="false" />
        <coding_rule class="ErrorProneOverrideBothEqualsAndHashcode" level="CRITICAL" enabled="false" />
        <coding_rule class="ErrorProneProperCloneImplementation" level="CRITICAL" enabled="false" />
        <coding_rule class="ErrorProneProperLogger" level="MAJOR" enabled="false" />
        <coding_rule class="ErrorProneReturnEmptyArrayRatherThanNull" level="MINOR" enabled="false" />
        <coding_rule class="ErrorProneReturnFromFinallyBlock" level="MAJOR" enabled="false" />
        <coding_rule class="ErrorProneSimpleDateFormatNeedsLocale" level="MAJOR" enabled="false" />
        <coding_rule class="ErrorProneSingleMethodSingleton" level="MAJOR" enabled="false" />
        <coding_rule class="ErrorProneSingletonClassReturningNewInstance" level="MAJOR" enabled="false" />
        <coding_rule class="ErrorProneStaticEJBFieldShouldBeFinal" level="MAJOR" enabled="false" />
        <coding_rule class="ErrorProneStringBufferInstantiationWithChar" level="MAJOR" enabled="true" />
        <coding_rule class="ErrorProneSuspiciousEqualsMethodName" level="CRITICAL" enabled="true" />
        <coding_rule class="ErrorProneSuspiciousHashcodeMethodName" level="MAJOR" enabled="true" />
        <coding_rule class="ErrorProneSuspiciousOctalEscape" level="MAJOR" enabled="false" />
        <coding_rule class="ErrorProneTestClassWithoutTestCases" level="MAJOR" enabled="false" />
        <coding_rule class="ErrorProneUnconditionalIfStatement" level="CRITICAL" enabled="true" />
        <coding_rule class="ErrorProneUnnecessaryBooleanAssertion" level="MAJOR" enabled="false" />
        <coding_rule class="ErrorProneUnnecessaryCaseChange" level="MINOR" enabled="true" />
        <coding_rule class="ErrorProneUnnecessaryConversionTemporary" level="MAJOR" enabled="false" />
        <coding_rule class="ErrorProneUnusedNullCheckInEquals" level="MAJOR" enabled="true" />
        <coding_rule class="ErrorProneUseCorrectExceptionLogging" level="MAJOR" enabled="true" />
        <coding_rule class="ErrorProneUseEqualsToCompareStrings" level="MAJOR" enabled="false" />
        <coding_rule class="ErrorProneUseLocaleWithCaseConversions" level="MAJOR" enabled="false" />
        <coding_rule class="ErrorProneUseProperClassLoader" level="CRITICAL" enabled="false" />
        <coding_rule class="ErrorProneUselessOperationOnImmutable" level="CRITICAL" enabled="true" />
        <coding_rule class="ExecutableStatementCountCheck" level="MAJOR" enabled="false" />
        <coding_rule class="ExplicitInitializationCheck" level="MAJOR" enabled="false" />
        <coding_rule class="FB_MISSING_EXPECTED_WARNING" level="CRITICAL" enabled="false" />
        <coding_rule class="FB_UNEXPECTED_WARNING" level="CRITICAL" enabled="false" />
        <coding_rule class="FE_FLOATING_POINT_EQUALITY" level="CRITICAL" enabled="true" />
        <coding_rule class="FE_TEST_IF_EQUAL_TO_NOT_A_NUMBER" level="CRITICAL" enabled="true" />
        <coding_rule class="FI_EMPTY" level="MAJOR" enabled="true" />
        <coding_rule class="FI_EXPLICIT_INVOCATION" level="MAJOR" enabled="true" />
        <coding_rule class="FI_FINALIZER_NULLS_FIELDS" level="MAJOR" enabled="true" />
        <coding_rule class="FI_FINALIZER_ONLY_NULLS_FIELDS" level="MAJOR" enabled="true" />
        <coding_rule class="FI_MISSING_SUPER_CALL" level="MAJOR" enabled="true" />
        <coding_rule class="FI_NULLIFY_SUPER" level="CRITICAL" enabled="true" />
        <coding_rule class="FI_PUBLIC_SHOULD_BE_PROTECTED" level="MAJOR" enabled="true" />
        <coding_rule class="FI_USELESS" level="MINOR" enabled="true" />
        <coding_rule class="FL_MATH_USING_FLOAT_PRECISION" level="CRITICAL" enabled="true" />
        <coding_rule class="FallThroughCheck" level="MAJOR" enabled="false" />
        <coding_rule class="FileLengthCheck" level="MAJOR" enabled="false" />
        <coding_rule class="FileTabCharacterCheck" level="MAJOR" enabled="false" />
        <coding_rule class="FinalClassCheck" level="MAJOR" enabled="true" />
        <coding_rule class="FinalLocalVariableCheck" level="MINOR" enabled="false" />
        <coding_rule class="FinalParametersCheck" level="MINOR" enabled="false" />
        <coding_rule class="GC_UNCHECKED_TYPE_IN_GENERIC_CALL" level="CRITICAL" enabled="true" />
        <coding_rule class="GC_UNRELATED_TYPES" level="CRITICAL" enabled="true" />
        <coding_rule class="GenericWhitespaceCheck" level="MAJOR" enabled="false" />
        <coding_rule class="HE_EQUALS_NO_HASHCODE" level="MAJOR" enabled="true" />
        <coding_rule class="HE_EQUALS_USE_HASHCODE" level="CRITICAL" enabled="true" />
        <coding_rule class="HE_HASHCODE_NO_EQUALS" level="CRITICAL" enabled="true" />
        <coding_rule class="HE_HASHCODE_USE_OBJECT_EQUALS" level="CRITICAL" enabled="true" />
        <coding_rule class="HE_INHERITS_EQUALS_USE_HASHCODE" level="CRITICAL" enabled="true" />
        <coding_rule class="HE_SIGNATURE_DECLARES_HASHING_OF_UNHASHABLE_CLASS" level="CRITICAL" enabled="true" />
        <coding_rule class="HE_USE_OF_UNHASHABLE_CLASS" level="CRITICAL" enabled="true" />
        <coding_rule class="HRS_REQUEST_PARAMETER_TO_COOKIE" level="MAJOR" enabled="true" />
        <coding_rule class="HRS_REQUEST_PARAMETER_TO_HTTP_HEADER" level="MAJOR" enabled="true" />
        <coding_rule class="HSC_HUGE_SHARED_STRING_CONSTANT" level="CRITICAL" enabled="true" />
        <coding_rule class="HeaderCheck" level="MAJOR" enabled="false" />
        <coding_rule class="HiddenFieldCheck" level="MAJOR" enabled="true" />
        <coding_rule class="HideUtilityClassConstructorCheck" level="MAJOR" enabled="true" />
        <coding_rule class="IA_AMBIGUOUS_INVOCATION_OF_INHERITED_OR_OUTER_METHOD" level="MAJOR" enabled="true" />
        <coding_rule class="ICAST_BAD_SHIFT_AMOUNT" level="CRITICAL" enabled="true" />
        <coding_rule class="ICAST_IDIV_CAST_TO_DOUBLE" level="CRITICAL" enabled="true" />
        <coding_rule class="ICAST_INTEGER_MULTIPLY_CAST_TO_LONG" level="CRITICAL" enabled="true" />
        <coding_rule class="ICAST_INT_2_LONG_AS_INSTANT" level="CRITICAL" enabled="false" />
        <coding_rule class="ICAST_INT_CAST_TO_DOUBLE_PASSED_TO_CEIL" level="CRITICAL" enabled="true" />
        <coding_rule class="ICAST_INT_CAST_TO_FLOAT_PASSED_TO_ROUND" level="CRITICAL" enabled="true" />
        <coding_rule class="ICAST_QUESTIONABLE_UNSIGNED_RIGHT_SHIFT" level="CRITICAL" enabled="true" />
        <coding_rule class="IC_INIT_CIRCULARITY" level="CRITICAL" enabled="true" />
        <coding_rule class="IC_SUPERCLASS_USES_SUBCLASS_DURING_INITIALIZATION" level="MAJOR" enabled="true" />
        <coding_rule class="IIL_ELEMENTS_GET_LENGTH_IN_LOOP" level="MAJOR" enabled="false" />
        <coding_rule class="IIL_PATTERN_COMPILE_IN_LOOP" level="MAJOR" enabled="false" />
        <coding_rule class="IIL_PATTERN_COMPILE_IN_LOOP_INDIRECT" level="MAJOR" enabled="false" />
        <coding_rule class="IIL_PREPARE_STATEMENT_IN_LOOP" level="MAJOR" enabled="false" />
        <coding_rule class="IIO_INEFFICIENT_INDEX_OF" level="MAJOR" enabled="false" />
        <coding_rule class="IIO_INEFFICIENT_LAST_INDEX_OF" level="MAJOR" enabled="false" />
        <coding_rule class="IJU_ASSERT_METHOD_INVOKED_FROM_RUN_METHOD" level="CRITICAL" enabled="true" />
        <coding_rule class="IJU_BAD_SUITE_METHOD" level="CRITICAL" enabled="true" />
        <coding_rule class="IJU_NO_TESTS" level="CRITICAL" enabled="true" />
        <coding_rule class="IJU_SETUP_NO_SUPER" level="CRITICAL" enabled="true" />
        <coding_rule class="IJU_SUITE_NOT_STATIC" level="CRITICAL" enabled="true" />
        <coding_rule class="IJU_TEARDOWN_NO_SUPER" level="CRITICAL" enabled="true" />
        <coding_rule class="IL_CONTAINER_ADDED_TO_ITSELF" level="CRITICAL" enabled="true" />
        <coding_rule class="IL_INFINITE_LOOP" level="CRITICAL" enabled="true" />
        <coding_rule class="IL_INFINITE_RECURSIVE_LOOP" level="CRITICAL" enabled="true" />
        <coding_rule class="IMA_INEFFICIENT_MEMBER_ACCESS" level="MAJOR" enabled="false" />
        <coding_rule class="IMSE_DONT_CATCH_IMSE" level="MAJOR" enabled="true" />
        <coding_rule class="IM_AVERAGE_COMPUTATION_COULD_OVERFLOW" level="CRITICAL" enabled="true" />
        <coding_rule class="IM_BAD_CHECK_FOR_ODD" level="CRITICAL" enabled="true" />
        <coding_rule class="IM_MULTIPLYING_RESULT_OF_IREM" level="CRITICAL" enabled="true" />
        <coding_rule class="INT_BAD_COMPARISON_WITH_INT_VALUE" level="CRITICAL" enabled="false" />
        <coding_rule class="INT_BAD_COMPARISON_WITH_NONNEGATIVE_VALUE" level="CRITICAL" enabled="true" />
        <coding_rule class="INT_BAD_COMPARISON_WITH_SIGNED_BYTE" level="CRITICAL" enabled="true" />
        <coding_rule class="INT_BAD_REM_BY_1" level="CRITICAL" enabled="true" />
        <coding_rule class="INT_VACUOUS_BIT_OPERATION" level="CRITICAL" enabled="true" />
        <coding_rule class="INT_VACUOUS_COMPARISON" level="CRITICAL" enabled="true" />
        <coding_rule class="IO_APPENDING_TO_OBJECT_OUTPUT_STREAM" level="CRITICAL" enabled="true" />
        <coding_rule class="IP_PARAMETER_IS_DEAD_BUT_OVERWRITTEN" level="CRITICAL" enabled="true" />
        <coding_rule class="IS2_INCONSISTENT_SYNC" level="CRITICAL" enabled="true" />
        <coding_rule class="ISC_INSTANTIATE_STATIC_CLASS" level="MAJOR" enabled="true" />
        <coding_rule class="IS_FIELD_NOT_GUARDED" level="CRITICAL" enabled="true" />
        <coding_rule class="ITA_INEFFICIENT_TO_ARRAY" level="CRITICAL" enabled="true" />
        <coding_rule class="IT_NO_SUCH_ELEMENT" level="MINOR" enabled="true" />
        <coding_rule class="IllegalCatchCheck" level="MAJOR" enabled="false" />
        <coding_rule class="IllegalImportCheck" level="MAJOR" enabled="false" />
        <coding_rule class="IllegalInstantiationCheck" level="MAJOR" enabled="false" />
        <coding_rule class="IllegalThrowsCheck" level="MAJOR" enabled="true" />
        <coding_rule class="IllegalTokenCheck" level="MAJOR" enabled="false" />
        <coding_rule class="IllegalTokenTextCheck" level="MAJOR" enabled="false" />
        <coding_rule class="IllegalTypeCheck" level="MAJOR" enabled="false" />
        <coding_rule class="ImportControlCheck" level="MAJOR" enabled="false" />
        <coding_rule class="ImportOrderCheck" level="MINOR" enabled="false" />
        <coding_rule class="IndentationCheck" level="MINOR" enabled="false" />
        <coding_rule class="InnerAssignmentCheck" level="MAJOR" enabled="true" />
        <coding_rule class="InnerTypeLastCheck" level="MAJOR" enabled="true" />
        <coding_rule class="InterfaceIsTypeCheck" level="MAJOR" enabled="false" />
        <coding_rule class="InterfaceMemberImpliedModifierCheck" level="MAJOR" enabled="false" />
        <coding_rule class="InterfaceTypeParameterNameCheck" level="MAJOR" enabled="false" />
        <coding_rule class="InvalidJavadocPositionCheck" level="MAJOR" enabled="false" />
        <coding_rule class="J2EE_STORE_OF_NON_SERIALIZABLE_OBJECT_INTO_SESSION" level="CRITICAL" enabled="true" />
        <coding_rule class="JCIP_FIELD_ISNT_FINAL_IN_IMMUTABLE_CLASS" level="MINOR" enabled="true" />
        <coding_rule class="JLM_JSR166_LOCK_MONITORENTER" level="CRITICAL" enabled="true" />
        <coding_rule class="JLM_JSR166_UTILCONCURRENT_MONITORENTER" level="CRITICAL" enabled="false" />
        <coding_rule class="JML_JSR166_CALLING_WAIT_RATHER_THAN_AWAIT" level="CRITICAL" enabled="false" />
        <coding_rule class="JavaNCSSCheck" level="MAJOR" enabled="false" />
        <coding_rule class="JavadocBlockTagLocationCheck" level="MAJOR" enabled="false" />
        <coding_rule class="JavadocContentLocationCheck" level="MAJOR" enabled="false" />
        <coding_rule class="JavadocMethodCheck" level="MAJOR" enabled="false" />
        <coding_rule class="JavadocPackageCheck" level="MAJOR" enabled="false" />
        <coding_rule class="JavadocParagraphCheck" level="MAJOR" enabled="false" />
        <coding_rule class="JavadocStyleCheck" level="MAJOR" enabled="false" />
        <coding_rule class="JavadocTagContinuationIndentationCheck" level="MAJOR" enabled="false" />
        <coding_rule class="JavadocTypeCheck" level="MAJOR" enabled="false" />
        <coding_rule class="JavadocVariableCheck" level="MAJOR" enabled="false" />
        <coding_rule class="LG_LOST_LOGGER_DUE_TO_WEAK_REFERENCE" level="MAJOR" enabled="false" />
        <coding_rule class="LI_LAZY_INIT_STATIC" level="CRITICAL" enabled="true" />
        <coding_rule class="LI_LAZY_INIT_UPDATE_STATIC" level="CRITICAL" enabled="true" />
        <coding_rule class="LambdaParameterNameCheck" level="MAJOR" enabled="false" />
        <coding_rule class="LeftCurlyCheck" level="MINOR" enabled="false" />
        <coding_rule class="LineLengthCheck" level="MAJOR" enabled="false" />
        <coding_rule class="LocalFinalVariableNameCheck" level="MAJOR" enabled="true" />
        <coding_rule class="LocalVariableNameCheck" level="MAJOR" enabled="true" />
        <coding_rule class="ME_ENUM_FIELD_SETTER" level="MAJOR" enabled="false" />
        <coding_rule class="ME_MUTABLE_ENUM_FIELD" level="MAJOR" enabled="false" />
        <coding_rule class="MF_CLASS_MASKS_FIELD" level="MAJOR" enabled="true" />
        <coding_rule class="MF_METHOD_MASKS_FIELD" level="MAJOR" enabled="true" />
        <coding_rule class="ML_SYNC_ON_FIELD_TO_GUARD_CHANGING_THAT_FIELD" level="MAJOR" enabled="true" />
        <coding_rule class="ML_SYNC_ON_UPDATED_FIELD" level="MAJOR" enabled="true" />
        <coding_rule class="MSF_MUTABLE_SERVLET_FIELD" level="MAJOR" enabled="true" />
        <coding_rule class="MS_CANNOT_BE_FINAL" level="MAJOR" enabled="true" />
        <coding_rule class="MS_EXPOSE_REP" level="CRITICAL" enabled="true" />
        <coding_rule class="MS_FINAL_PKGPROTECT" level="MAJOR" enabled="true" />
        <coding_rule class="MS_MUTABLE_ARRAY" level="MAJOR" enabled="true" />
        <coding_rule class="MS_MUTABLE_COLLECTION" level="MAJOR" enabled="false" />
        <coding_rule class="MS_MUTABLE_COLLECTION_PKGPROTECT" level="MAJOR" enabled="false" />
        <coding_rule class="MS_MUTABLE_HASHTABLE" level="MAJOR" enabled="true" />
        <coding_rule class="MS_OOI_PKGPROTECT" level="MAJOR" enabled="true" />
        <coding_rule class="MS_PKGPROTECT" level="MAJOR" enabled="true" />
        <coding_rule class="MS_SHOULD_BE_FINAL" level="MAJOR" enabled="true" />
        <coding_rule class="MS_SHOULD_BE_REFACTORED_TO_BE_FINAL" level="CRITICAL" enabled="false" />
        <coding_rule class="MTIA_SUSPECT_SERVLET_INSTANCE_FIELD" level="CRITICAL" enabled="true" />
        <coding_rule class="MTIA_SUSPECT_STRUTS_INSTANCE_FIELD" level="CRITICAL" enabled="true" />
        <coding_rule class="MWN_MISMATCHED_NOTIFY" level="CRITICAL" enabled="true" />
        <coding_rule class="MWN_MISMATCHED_WAIT" level="CRITICAL" enabled="true" />
        <coding_rule class="MagicNumberCheck" level="MINOR" enabled="true" />
        <coding_rule class="MemberNameCheck" level="MAJOR" enabled="true" />
        <coding_rule class="MethodCountCheck" level="MAJOR" enabled="false" />
        <coding_rule class="MethodLengthCheck" level="MAJOR" enabled="false" />
        <coding_rule class="MethodNameCheck" level="MAJOR" enabled="true" />
        <coding_rule class="MethodParamPadCheck" level="MAJOR" enabled="false" />
        <coding_rule class="MethodTypeParameterNameCheck" level="MAJOR" enabled="false" />
        <coding_rule class="MissingCtorCheck" level="MAJOR" enabled="false" />
        <coding_rule class="MissingDeprecatedCheck" level="MAJOR" enabled="false" />
        <coding_rule class="MissingJavadocMethodCheck" level="MAJOR" enabled="false" />
        <coding_rule class="MissingJavadocPackageCheck" level="MAJOR" enabled="false" />
        <coding_rule class="MissingJavadocTypeCheck" level="MAJOR" enabled="false" />
        <coding_rule class="MissingOverrideCheck" level="MAJOR" enabled="false" />
        <coding_rule class="MissingSwitchDefaultCheck" level="MAJOR" enabled="false" />
        <coding_rule class="ModifiedControlVariableCheck" level="MAJOR" enabled="false" />
        <coding_rule class="ModifierOrderCheck" level="MINOR" enabled="true" />
        <coding_rule class="MultipleStringLiteralsCheck" level="MAJOR" enabled="false" />
        <coding_rule class="MultipleVariableDeclarationsCheck" level="MAJOR" enabled="false" />
        <coding_rule class="MultithreadingAvoidSynchronizedAtMethodLevel" level="MAJOR" enabled="false" />
        <coding_rule class="MultithreadingAvoidThreadGroup" level="CRITICAL" enabled="false" />
        <coding_rule class="MultithreadingAvoidUsingVolatile" level="MAJOR" enabled="false" />
        <coding_rule class="MultithreadingDoNotUseThreads" level="MAJOR" enabled="false" />
        <coding_rule class="MultithreadingDontCallThreadRun" level="MAJOR" enabled="false" />
        <coding_rule class="MultithreadingDoubleCheckedLocking" level="MAJOR" enabled="false" />
        <coding_rule class="MultithreadingNonThreadSafeSingleton" level="MAJOR" enabled="false" />
        <coding_rule class="MultithreadingUnsynchronizedStaticDateFormatter" level="MAJOR" enabled="false" />
        <coding_rule class="MultithreadingUseConcurrentHashMap" level="MAJOR" enabled="false" />
        <coding_rule class="MultithreadingUseNotifyAllInsteadOfNotify" level="MAJOR" enabled="false" />
        <coding_rule class="MutableExceptionCheck" level="MAJOR" enabled="false" />
        <coding_rule class="NM_BAD_EQUAL" level="MAJOR" enabled="false" />
        <coding_rule class="NM_CLASS_NAMING_CONVENTION" level="MAJOR" enabled="false" />
        <coding_rule class="NM_CLASS_NOT_EXCEPTION" level="MAJOR" enabled="true" />
        <coding_rule class="NM_CONFUSING" level="MAJOR" enabled="true" />
        <coding_rule class="NM_FIELD_NAMING_CONVENTION" level="MAJOR" enabled="false" />
        <coding_rule class="NM_FUTURE_KEYWORD_USED_AS_IDENTIFIER" level="MAJOR" enabled="true" />
        <coding_rule class="NM_FUTURE_KEYWORD_USED_AS_MEMBER_IDENTIFIER" level="MAJOR" enabled="true" />
        <coding_rule class="NM_LCASE_HASHCODE" level="MAJOR" enabled="false" />
        <coding_rule class="NM_LCASE_TOSTRING" level="MAJOR" enabled="false" />
        <coding_rule class="NM_METHOD_CONSTRUCTOR_CONFUSION" level="MAJOR" enabled="true" />
        <coding_rule class="NM_METHOD_NAMING_CONVENTION" level="MAJOR" enabled="false" />
        <coding_rule class="NM_SAME_SIMPLE_NAME_AS_INTERFACE" level="MAJOR" enabled="true" />
        <coding_rule class="NM_SAME_SIMPLE_NAME_AS_SUPERCLASS" level="MAJOR" enabled="true" />
        <coding_rule class="NM_VERY_CONFUSING" level="MAJOR" enabled="true" />
        <coding_rule class="NM_VERY_CONFUSING_INTENTIONAL" level="MAJOR" enabled="true" />
        <coding_rule class="NM_WRONG_PACKAGE" level="MAJOR" enabled="true" />
        <coding_rule class="NM_WRONG_PACKAGE_INTENTIONAL" level="MAJOR" enabled="true" />
        <coding_rule class="NN_NAKED_NOTIFY" level="CRITICAL" enabled="true" />
        <coding_rule class="NOISE_FIELD_REFERENCE" level="MAJOR" enabled="false" />
        <coding_rule class="NOISE_METHOD_CALL" level="MAJOR" enabled="false" />
        <coding_rule class="NOISE_NULL_DEREFERENCE" level="MAJOR" enabled="false" />
        <coding_rule class="NOISE_OPERATION" level="MAJOR" enabled="false" />
        <coding_rule class="NO_NOTIFY_NOT_NOTIFYALL" level="CRITICAL" enabled="true" />
        <coding_rule class="NP_ALWAYS_NULL" level="CRITICAL" enabled="true" />
        <coding_rule class="NP_ALWAYS_NULL_EXCEPTION" level="CRITICAL" enabled="true" />
        <coding_rule class="NP_ARGUMENT_MIGHT_BE_NULL" level="MAJOR" enabled="true" />
        <coding_rule class="NP_BOOLEAN_RETURN_NULL" level="MAJOR" enabled="true" />
        <coding_rule class="NP_CLONE_COULD_RETURN_NULL" level="CRITICAL" enabled="true" />
        <coding_rule class="NP_CLOSING_NULL" level="MAJOR" enabled="false" />
        <coding_rule class="NP_DEREFERENCE_OF_READLINE_VALUE" level="CRITICAL" enabled="true" />
        <coding_rule class="NP_EQUALS_SHOULD_HANDLE_NULL_ARGUMENT" level="CRITICAL" enabled="true" />
        <coding_rule class="NP_GUARANTEED_DEREF" level="BLOCKER" enabled="true" />
        <coding_rule class="NP_GUARANTEED_DEREF_ON_EXCEPTION_PATH" level="CRITICAL" enabled="true" />
        <coding_rule class="NP_IMMEDIATE_DEREFERENCE_OF_READLINE" level="CRITICAL" enabled="true" />
        <coding_rule class="NP_LOAD_OF_KNOWN_NULL_VALUE" level="CRITICAL" enabled="true" />
        <coding_rule class="NP_METHOD_PARAMETER_TIGHTENS_ANNOTATION" level="MAJOR" enabled="false" />
        <coding_rule class="NP_METHOD_RETURN_RELAXING_ANNOTATION" level="MAJOR" enabled="false" />
        <coding_rule class="NP_NONNULL_FIELD_NOT_INITIALIZED_IN_CONSTRUCTOR" level="CRITICAL" enabled="false" />
        <coding_rule class="NP_NONNULL_PARAM_VIOLATION" level="CRITICAL" enabled="true" />
        <coding_rule class="NP_NONNULL_RETURN_VIOLATION" level="CRITICAL" enabled="true" />
        <coding_rule class="NP_NULL_INSTANCEOF" level="BLOCKER" enabled="true" />
        <coding_rule class="NP_NULL_ON_SOME_PATH" level="CRITICAL" enabled="true" />
        <coding_rule class="NP_NULL_ON_SOME_PATH_EXCEPTION" level="CRITICAL" enabled="true" />
        <coding_rule class="NP_NULL_ON_SOME_PATH_FROM_RETURN_VALUE" level="CRITICAL" enabled="true" />
        <coding_rule class="NP_NULL_ON_SOME_PATH_MIGHT_BE_INFEASIBLE" level="CRITICAL" enabled="true" />
        <coding_rule class="NP_NULL_PARAM_DEREF" level="CRITICAL" enabled="true" />
        <coding_rule class="NP_NULL_PARAM_DEREF_ALL_TARGETS_DANGEROUS" level="CRITICAL" enabled="true" />
        <coding_rule class="NP_NULL_PARAM_DEREF_NONVIRTUAL" level="CRITICAL" enabled="true" />
        <coding_rule class="NP_OPTIONAL_RETURN_NULL" level="MAJOR" enabled="false" />
        <coding_rule class="NP_PARAMETER_MUST_BE_NONNULL_BUT_MARKED_AS_NULLABLE" level="CRITICAL" enabled="true" />
        <coding_rule class="NP_STORE_INTO_NONNULL_FIELD" level="CRITICAL" enabled="true" />
        <coding_rule class="NP_SYNC_AND_NULL_CHECK_FIELD" level="MAJOR" enabled="true" />
        <coding_rule class="NP_TOSTRING_COULD_RETURN_NULL" level="CRITICAL" enabled="true" />
        <coding_rule class="NP_UNWRITTEN_FIELD" level="MAJOR" enabled="false" />
        <coding_rule class="NP_UNWRITTEN_PUBLIC_OR_PROTECTED_FIELD" level="CRITICAL" enabled="false" />
        <coding_rule class="NPathComplexityCheck" level="MAJOR" enabled="false" />
        <coding_rule class="NS_DANGEROUS_NON_SHORT_CIRCUIT" level="CRITICAL" enabled="true" />
        <coding_rule class="NS_NON_SHORT_CIRCUIT" level="MAJOR" enabled="true" />
        <coding_rule class="NeedBracesCheck" level="MINOR" enabled="false" />
        <coding_rule class="NestedForDepthCheck" level="MAJOR" enabled="false" />
        <coding_rule class="NestedIfDepthCheck" level="MAJOR" enabled="false" />
        <coding_rule class="NestedTryDepthCheck" level="MAJOR" enabled="false" />
        <coding_rule class="NewlineAtEndOfFileCheck" level="MINOR" enabled="false" />
        <coding_rule class="NoArrayTrailingCommaCheck" level="MAJOR" enabled="false" />
        <coding_rule class="NoCloneCheck" level="MAJOR" enabled="false" />
        <coding_rule class="NoEnumTrailingCommaCheck" level="MAJOR" enabled="false" />
        <coding_rule class="NoFinalizerCheck" level="MAJOR" enabled="false" />
        <coding_rule class="NoLineWrapCheck" level="MAJOR" enabled="false" />
        <coding_rule class="NoWhitespaceAfterCheck" level="MINOR" enabled="false" />
        <coding_rule class="NoWhitespaceBeforeCheck" level="MINOR" enabled="false" />
        <coding_rule class="NonEmptyAtclauseDescriptionCheck" level="MAJOR" enabled="false" />
        <coding_rule class="OBL_UNSATISFIED_OBLIGATION" level="CRITICAL" enabled="false" />
        <coding_rule class="OBL_UNSATISFIED_OBLIGATION_EXCEPTION_EDGE" level="CRITICAL" enabled="false" />
        <coding_rule class="ODR_OPEN_DATABASE_RESOURCE" level="CRITICAL" enabled="true" />
        <coding_rule class="ODR_OPEN_DATABASE_RESOURCE_EXCEPTION_PATH" level="CRITICAL" enabled="true" />
        <coding_rule class="OS_OPEN_STREAM" level="CRITICAL" enabled="true" />
        <coding_rule class="OS_OPEN_STREAM_EXCEPTION_PATH" level="CRITICAL" enabled="true" />
        <coding_rule class="OneStatementPerLineCheck" level="MINOR" enabled="false" />
        <coding_rule class="OneTopLevelClassCheck" level="MAJOR" enabled="false" />
        <coding_rule class="OperatorWrapCheck" level="MINOR" enabled="false" />
        <coding_rule class="OrderedPropertiesCheck" level="MAJOR" enabled="false" />
        <coding_rule class="OuterTypeFilenameCheck" level="MINOR" enabled="false" />
        <coding_rule class="OuterTypeNumberCheck" level="MAJOR" enabled="false" />
        <coding_rule class="OverloadMethodsDeclarationOrderCheck" level="MAJOR" enabled="false" />
        <coding_rule class="PS_PUBLIC_SEMAPHORES" level="CRITICAL" enabled="true" />
        <coding_rule class="PT_ABSOLUTE_PATH_TRAVERSAL" level="CRITICAL" enabled="false" />
        <coding_rule class="PT_RELATIVE_PATH_TRAVERSAL" level="CRITICAL" enabled="false" />
        <coding_rule class="PZLA_PREFER_ZERO_LENGTH_ARRAYS" level="MAJOR" enabled="true" />
        <coding_rule class="PZ_DONT_REUSE_ENTRY_OBJECTS_IN_ITERATORS" level="CRITICAL" enabled="false" />
        <coding_rule class="PackageAnnotationCheck" level="MAJOR" enabled="false" />
        <coding_rule class="PackageDeclarationCheck" level="MAJOR" enabled="false" />
        <coding_rule class="PackageNameCheck" level="MAJOR" enabled="true" />
        <coding_rule class="ParameterAssignmentCheck" level="MAJOR" enabled="true" />
        <coding_rule class="ParameterNameCheck" level="MAJOR" enabled="true" />
        <coding_rule class="ParameterNumberCheck" level="MAJOR" enabled="false" />
        <coding_rule class="ParenPadCheck" level="MINOR" enabled="false" />
        <coding_rule class="PerformanceAddEmptyString" level="MAJOR" enabled="false" />
        <coding_rule class="PerformanceAppendCharacterWithChar" level="MINOR" enabled="false" />
        <coding_rule class="PerformanceAvoidArrayLoops" level="MAJOR" enabled="true" />
        <coding_rule class="PerformanceAvoidInstantiatingObjectsInLoops" level="MINOR" enabled="false" />
        <coding_rule class="PerformanceAvoidUsingShortType" level="MAJOR" enabled="false" />
        <coding_rule class="PerformanceBigIntegerInstantiation" level="MAJOR" enabled="true" />
        <coding_rule class="PerformanceBooleanInstantiation" level="MAJOR" enabled="true" />
        <coding_rule class="PerformanceByteInstantiation" level="MAJOR" enabled="false" />
        <coding_rule class="PerformanceConsecutiveAppendsShouldReuse" level="MAJOR" enabled="false" />
        <coding_rule class="PerformanceConsecutiveLiteralAppends" level="MINOR" enabled="false" />
        <coding_rule class="PerformanceInefficientEmptyStringCheck" level="MAJOR" enabled="false" />
        <coding_rule class="PerformanceInefficientStringBuffering" level="MAJOR" enabled="true" />
        <coding_rule class="PerformanceInsufficientStringBufferDeclaration" level="MAJOR" enabled="false" />
        <coding_rule class="PerformanceIntegerInstantiation" level="MAJOR" enabled="true" />
        <coding_rule class="PerformanceLongInstantiation" level="MAJOR" enabled="false" />
        <coding_rule class="PerformanceOptimizableToArrayCall" level="MAJOR" enabled="false" />
        <coding_rule class="PerformanceRedundantFieldInitializer" level="MAJOR" enabled="false" />
        <coding_rule class="PerformanceShortInstantiation" level="MAJOR" enabled="false" />
        <coding_rule class="PerformanceSimplifyStartsWith" level="MINOR" enabled="false" />
        <coding_rule class="PerformanceStringInstantiation" level="MAJOR" enabled="true" />
        <coding_rule class="PerformanceStringToString" level="MAJOR" enabled="true" />
        <coding_rule class="PerformanceTooFewBranchesForASwitchStatement" level="MINOR" enabled="false" />
        <coding_rule class="PerformanceUnnecessaryWrapperObjectCreation" level="MAJOR" enabled="false" />
        <coding_rule class="PerformanceUseArrayListInsteadOfVector" level="MAJOR" enabled="true" />
        <coding_rule class="PerformanceUseArraysAsList" level="MAJOR" enabled="true" />
        <coding_rule class="PerformanceUseIndexOfChar" level="MAJOR" enabled="true" />
        <coding_rule class="PerformanceUseStringBufferForStringAppends" level="MAJOR" enabled="false" />
        <coding_rule class="PerformanceUseStringBufferLength" level="MINOR" enabled="true" />
        <coding_rule class="PerformanceUselessStringValueOf" level="MINOR" enabled="true" />
        <coding_rule class="QBA_QUESTIONABLE_BOOLEAN_ASSIGNMENT" level="CRITICAL" enabled="true" />
        <coding_rule class="QF_QUESTIONABLE_FOR_LOOP" level="CRITICAL" enabled="true" />
        <coding_rule class="RANGE_ARRAY_INDEX" level="MAJOR" enabled="false" />
        <coding_rule class="RANGE_ARRAY_LENGTH" level="MAJOR" enabled="false" />
        <coding_rule class="RANGE_ARRAY_OFFSET" level="MAJOR" enabled="false" />
        <coding_rule class="RANGE_STRING_INDEX" level="MAJOR" enabled="false" />
        <coding_rule class="RCN_REDUNDANT_COMPARISON_OF_NULL_AND_NONNULL_VALUE" level="CRITICAL" enabled="true" />
        <coding_rule class="RCN_REDUNDANT_COMPARISON_TWO_NULL_VALUES" level="CRITICAL" enabled="true" />
        <coding_rule class="RCN_REDUNDANT_NULLCHECK_OF_NONNULL_VALUE" level="CRITICAL" enabled="true" />
        <coding_rule class="RCN_REDUNDANT_NULLCHECK_OF_NULL_VALUE" level="CRITICAL" enabled="true" />
        <coding_rule class="RCN_REDUNDANT_NULLCHECK_WOULD_HAVE_BEEN_A_NPE" level="CRITICAL" enabled="true" />
        <coding_rule class="RC_REF_COMPARISON" level="CRITICAL" enabled="true" />
        <coding_rule class="RC_REF_COMPARISON_BAD_PRACTICE" level="MAJOR" enabled="false" />
        <coding_rule class="RC_REF_COMPARISON_BAD_PRACTICE_BOOLEAN" level="MAJOR" enabled="false" />
        <coding_rule class="REC_CATCH_EXCEPTION" level="MAJOR" enabled="true" />
        <coding_rule class="RE_BAD_SYNTAX_FOR_REGULAR_EXPRESSION" level="CRITICAL" enabled="true" />
        <coding_rule class="RE_CANT_USE_FILE_SEPARATOR_AS_REGULAR_EXPRESSION" level="CRITICAL" enabled="true" />
        <coding_rule class="RE_POSSIBLE_UNINTENDED_PATTERN" level="CRITICAL" enabled="true" />
        <coding_rule class="RI_REDUNDANT_INTERFACES" level="MAJOR" enabled="true" />
        <coding_rule class="RR_NOT_CHECKED" level="MAJOR" enabled="true" />
        <coding_rule class="RS_READOBJECT_SYNC" level="CRITICAL" enabled="true" />
        <coding_rule class="RU_INVOKE_RUN" level="MAJOR" enabled="true" />
        <coding_rule class="RV_01_TO_INT" level="MAJOR" enabled="true" />
        <coding_rule class="RV_ABSOLUTE_VALUE_OF_HASHCODE" level="CRITICAL" enabled="true" />
        <coding_rule class="RV_ABSOLUTE_VALUE_OF_RANDOM_INT" level="CRITICAL" enabled="true" />
        <coding_rule class="RV_CHECK_COMPARETO_FOR_SPECIFIC_RETURN_VALUE" level="CRITICAL" enabled="false" />
        <coding_rule class="RV_CHECK_FOR_POSITIVE_INDEXOF" level="MINOR" enabled="true" />
        <coding_rule class="RV_DONT_JUST_NULL_CHECK_READLINE" level="MAJOR" enabled="true" />
        <coding_rule class="RV_EXCEPTION_NOT_THROWN" level="CRITICAL" enabled="true" />
        <coding_rule class="RV_NEGATING_RESULT_OF_COMPARETO" level="CRITICAL" enabled="false" />
        <coding_rule class="RV_REM_OF_HASHCODE" level="CRITICAL" enabled="true" />
        <coding_rule class="RV_REM_OF_RANDOM_INT" level="CRITICAL" enabled="true" />
        <coding_rule class="RV_RETURN_VALUE_IGNORED" level="MINOR" enabled="true" />
        <coding_rule class="RV_RETURN_VALUE_IGNORED_BAD_PRACTICE" level="MAJOR" enabled="true" />
        <coding_rule class="RV_RETURN_VALUE_IGNORED_INFERRED" level="CRITICAL" enabled="false" />
        <coding_rule class="RV_RETURN_VALUE_IGNORED_NO_SIDE_EFFECT" level="MAJOR" enabled="false" />
        <coding_rule class="RV_RETURN_VALUE_OF_PUTIFABSENT_IGNORED" level="MAJOR" enabled="false" />
        <coding_rule class="RedundantImportCheck" level="MINOR" enabled="false" />
        <coding_rule class="RedundantModifierCheck" level="MINOR" enabled="true" />
        <coding_rule class="RegexpCheck" level="MAJOR" enabled="false" />
        <coding_rule class="RegexpHeaderCheck" level="MAJOR" enabled="false" />
        <coding_rule class="RegexpMultilineCheck" level="MAJOR" enabled="false" />
        <coding_rule class="RegexpOnFilenameCheck" level="MAJOR" enabled="false" />
        <coding_rule class="RegexpSinglelineCheck" level="MAJOR" enabled="false" />
        <coding_rule class="RegexpSinglelineJavaCheck" level="MAJOR" enabled="false" />
        <coding_rule class="RequireThisCheck" level="MAJOR" enabled="false" />
        <coding_rule class="ReturnCountCheck" level="MAJOR" enabled="false" />
        <coding_rule class="RightCurlyCheck" level="MINOR" enabled="false" />
        <coding_rule class="RpC_REPEATED_CONDITIONAL_TEST" level="MAJOR" enabled="true" />
        <coding_rule class="SA_FIELD_DOUBLE_ASSIGNMENT" level="CRITICAL" enabled="true" />
        <coding_rule class="SA_FIELD_SELF_ASSIGNMENT" level="CRITICAL" enabled="true" />
        <coding_rule class="SA_FIELD_SELF_COMPARISON" level="CRITICAL" enabled="true" />
        <coding_rule class="SA_FIELD_SELF_COMPUTATION" level="CRITICAL" enabled="true" />
        <coding_rule class="SA_LOCAL_DOUBLE_ASSIGNMENT" level="CRITICAL" enabled="true" />
        <coding_rule class="SA_LOCAL_SELF_ASSIGNMENT" level="CRITICAL" enabled="true" />
        <coding_rule class="SA_LOCAL_SELF_ASSIGNMENT_INSTEAD_OF_FIELD" level="CRITICAL" enabled="false" />
        <coding_rule class="SA_LOCAL_SELF_COMPARISON" level="CRITICAL" enabled="true" />
        <coding_rule class="SA_LOCAL_SELF_COMPUTATION" level="CRITICAL" enabled="true" />
        <coding_rule class="SBSC_USE_STRINGBUFFER_CONCATENATION" level="CRITICAL" enabled="true" />
        <coding_rule class="SC_START_IN_CTOR" level="CRITICAL" enabled="true" />
        <coding_rule class="SE_BAD_FIELD" level="MINOR" enabled="false" />
        <coding_rule class="SE_BAD_FIELD_INNER_CLASS" level="MINOR" enabled="true" />
        <coding_rule class="SE_BAD_FIELD_STORE" level="CRITICAL" enabled="true" />
        <coding_rule class="SE_COMPARATOR_SHOULD_BE_SERIALIZABLE" level="MAJOR" enabled="true" />
        <coding_rule class="SE_INNER_CLASS" level="MAJOR" enabled="true" />
        <coding_rule class="SE_METHOD_MUST_BE_PRIVATE" level="MAJOR" enabled="true" />
        <coding_rule class="SE_NONFINAL_SERIALVERSIONID" level="CRITICAL" enabled="true" />
        <coding_rule class="SE_NONLONG_SERIALVERSIONID" level="MAJOR" enabled="true" />
        <coding_rule class="SE_NONSTATIC_SERIALVERSIONID" level="MAJOR" enabled="true" />
        <coding_rule class="SE_NO_SERIALVERSIONID" level="MAJOR" enabled="true" />
        <coding_rule class="SE_NO_SUITABLE_CONSTRUCTOR" level="MAJOR" enabled="true" />
        <coding_rule class="SE_NO_SUITABLE_CONSTRUCTOR_FOR_EXTERNALIZATION" level="MAJOR" enabled="true" />
        <coding_rule class="SE_PRIVATE_READ_RESOLVE_NOT_INHERITED" level="MAJOR" enabled="true" />
        <coding_rule class="SE_READ_RESOLVE_IS_STATIC" level="MAJOR" enabled="true" />
        <coding_rule class="SE_READ_RESOLVE_MUST_RETURN_OBJECT" level="MAJOR" enabled="true" />
        <coding_rule class="SE_TRANSIENT_FIELD_NOT_RESTORED" level="MAJOR" enabled="true" />
        <coding_rule class="SE_TRANSIENT_FIELD_OF_NONSERIALIZABLE_CLASS" level="MAJOR" enabled="true" />
        <coding_rule class="SF_DEAD_STORE_DUE_TO_SWITCH_FALLTHROUGH" level="MAJOR" enabled="false" />
        <coding_rule class="SF_DEAD_STORE_DUE_TO_SWITCH_FALLTHROUGH_TO_THROW" level="MAJOR" enabled="false" />
        <coding_rule class="SF_SWITCH_FALLTHROUGH" level="MAJOR" enabled="false" />
        <coding_rule class="SF_SWITCH_NO_DEFAULT" level="MAJOR" enabled="false" />
        <coding_rule class="SIC_INNER_SHOULD_BE_STATIC" level="MAJOR" enabled="true" />
        <coding_rule class="SIC_INNER_SHOULD_BE_STATIC_ANON" level="MAJOR" enabled="true" />
        <coding_rule class="SIC_INNER_SHOULD_BE_STATIC_NEEDS_THIS" level="MAJOR" enabled="true" />
        <coding_rule class="SIC_THREADLOCAL_DEADLY_EMBRACE" level="MAJOR" enabled="false" />
        <coding_rule class="SIO_SUPERFLUOUS_INSTANCEOF" level="CRITICAL" enabled="true" />
        <coding_rule class="SI_INSTANCE_BEFORE_FINALS_ASSIGNED" level="CRITICAL" enabled="true" />
        <coding_rule class="SP_SPIN_ON_FIELD" level="MAJOR" enabled="true" />
        <coding_rule class="SQL_BAD_PREPARED_STATEMENT_ACCESS" level="CRITICAL" enabled="true" />
        <coding_rule class="SQL_BAD_RESULTSET_ACCESS" level="CRITICAL" enabled="true" />
        <coding_rule class="SQL_NONCONSTANT_STRING_PASSED_TO_EXECUTE" level="CRITICAL" enabled="true" />
        <coding_rule class="SQL_PREPARED_STATEMENT_GENERATED_FROM_NONCONSTANT_STRING" level="CRITICAL" enabled="true" />
        <coding_rule class="SR_NOT_CHECKED" level="MAJOR" enabled="true" />
        <coding_rule class="SS_SHOULD_BE_STATIC" level="MAJOR" enabled="true" />
        <coding_rule class="STCAL_INVOKE_ON_STATIC_CALENDAR_INSTANCE" level="CRITICAL" enabled="true" />
        <coding_rule class="STCAL_INVOKE_ON_STATIC_DATE_FORMAT_INSTANCE" level="CRITICAL" enabled="true" />
        <coding_rule class="STCAL_STATIC_CALENDAR_INSTANCE" level="CRITICAL" enabled="true" />
        <coding_rule class="STCAL_STATIC_SIMPLE_DATE_FORMAT_INSTANCE" level="CRITICAL" enabled="true" />
        <coding_rule class="STI_INTERRUPTED_ON_CURRENTTHREAD" level="CRITICAL" enabled="true" />
        <coding_rule class="STI_INTERRUPTED_ON_UNKNOWNTHREAD" level="CRITICAL" enabled="true" />
        <coding_rule class="ST_WRITE_TO_STATIC_FROM_INSTANCE_METHOD" level="CRITICAL" enabled="true" />
        <coding_rule class="SWL_SLEEP_WITH_LOCK_HELD" level="CRITICAL" enabled="true" />
        <coding_rule class="SW_SWING_METHODS_INVOKED_IN_SWING_THREAD" level="MAJOR" enabled="true" />
        <coding_rule class="SecurityIframeMissingSrcAttribute" level="MAJOR" enabled="false" />
        <coding_rule class="SecurityNoUnsanitizedJSPExpression" level="MAJOR" enabled="false" />
        <coding_rule class="SeparatorWrapCheck" level="MAJOR" enabled="false" />
        <coding_rule class="SimplifyBooleanExpressionCheck" level="MAJOR" enabled="true" />
        <coding_rule class="SimplifyBooleanReturnCheck" level="MAJOR" enabled="true" />
        <coding_rule class="SingleLineJavadocCheck" level="MAJOR" enabled="false" />
        <coding_rule class="SingleSpaceSeparatorCheck" level="MAJOR" enabled="false" />
        <coding_rule class="StaticVariableNameCheck" level="MAJOR" enabled="true" />
        <coding_rule class="StringLiteralEqualityCheck" level="MAJOR" enabled="true" />
        <coding_rule class="SummaryJavadocCheck" level="MAJOR" enabled="false" />
        <coding_rule class="SuperCloneCheck" level="MAJOR" enabled="false" />
        <coding_rule class="SuperFinalizeCheck" level="MAJOR" enabled="false" />
        <coding_rule class="SuppressWarningsCheck" level="MAJOR" enabled="false" />
        <coding_rule class="SuppressWarningsHolder" level="MAJOR" enabled="false" />
        <coding_rule class="TLW_TWO_LOCK_WAIT" level="MAJOR" enabled="true" />
        <coding_rule class="TQ_ALWAYS_VALUE_USED_WHERE_NEVER_REQUIRED" level="CRITICAL" enabled="true" />
        <coding_rule class="TQ_COMPARING_VALUES_WITH_INCOMPATIBLE_TYPE_QUALIFIERS" level="CRITICAL" enabled="false" />
        <coding_rule class="TQ_EXPLICIT_UNKNOWN_SOURCE_VALUE_REACHES_ALWAYS_SINK" level="CRITICAL" enabled="true" />
        <coding_rule class="TQ_EXPLICIT_UNKNOWN_SOURCE_VALUE_REACHES_NEVER_SINK" level="CRITICAL" enabled="true" />
        <coding_rule class="TQ_MAYBE_SOURCE_VALUE_REACHES_ALWAYS_SINK" level="CRITICAL" enabled="true" />
        <coding_rule class="TQ_MAYBE_SOURCE_VALUE_REACHES_NEVER_SINK" level="CRITICAL" enabled="true" />
        <coding_rule class="TQ_NEVER_VALUE_USED_WHERE_ALWAYS_REQUIRED" level="CRITICAL" enabled="true" />
        <coding_rule class="TQ_UNKNOWN_VALUE_USED_WHERE_ALWAYS_STRICTLY_REQUIRED" level="CRITICAL" enabled="false" />
        <coding_rule class="ThrowsCountCheck" level="MAJOR" enabled="false" />
        <coding_rule class="TodoCommentCheck" level="MINOR" enabled="false" />
        <coding_rule class="TrailingCommentCheck" level="MINOR" enabled="false" />
        <coding_rule class="TranslationCheck" level="MINOR" enabled="false" />
        <coding_rule class="TypeNameCheck" level="MAJOR" enabled="false" />
        <coding_rule class="TypecastParenPadCheck" level="MAJOR" enabled="false" />
        <coding_rule class="UCF_USELESS_CONTROL_FLOW" level="CRITICAL" enabled="true" />
        <coding_rule class="UCF_USELESS_CONTROL_FLOW_NEXT_LINE" level="CRITICAL" enabled="true" />
        <coding_rule class="UC_USELESS_CONDITION" level="MAJOR" enabled="false" />
        <coding_rule class="UC_USELESS_CONDITION_TYPE" level="MAJOR" enabled="false" />
        <coding_rule class="UC_USELESS_OBJECT" level="MAJOR" enabled="false" />
        <coding_rule class="UC_USELESS_OBJECT_STACK" level="MAJOR" enabled="false" />
        <coding_rule class="UC_USELESS_VOID_METHOD" level="MAJOR" enabled="false" />
        <coding_rule class="UG_SYNC_SET_UNSYNC_GET" level="MAJOR" enabled="true" />
        <coding_rule class="UI_INHERITANCE_UNSAFE_GETRESOURCE" level="MAJOR" enabled="true" />
        <coding_rule class="UL_UNRELEASED_LOCK" level="CRITICAL" enabled="true" />
        <coding_rule class="UL_UNRELEASED_LOCK_EXCEPTION_PATH" level="CRITICAL" enabled="true" />
        <coding_rule class="UMAC_UNCALLABLE_METHOD_OF_ANONYMOUS_CLASS" level="CRITICAL" enabled="true" />
        <coding_rule class="UM_UNNECESSARY_MATH" level="CRITICAL" enabled="true" />
        <coding_rule class="UPM_UNCALLED_PRIVATE_METHOD" level="CRITICAL" enabled="true" />
        <coding_rule class="URF_UNREAD_FIELD" level="MAJOR" enabled="true" />
        <coding_rule class="URF_UNREAD_PUBLIC_OR_PROTECTED_FIELD" level="CRITICAL" enabled="false" />
        <coding_rule class="UR_UNINIT_READ" level="MAJOR" enabled="true" />
        <coding_rule class="UR_UNINIT_READ_CALLED_FROM_SUPER_CONSTRUCTOR" level="MAJOR" enabled="false" />
        <coding_rule class="USM_USELESS_ABSTRACT_METHOD" level="MAJOR" enabled="false" />
        <coding_rule class="USM_USELESS_SUBCLASS_METHOD" level="MAJOR" enabled="false" />
        <coding_rule class="UUF_UNUSED_FIELD" level="MAJOR" enabled="true" />
        <coding_rule class="UUF_UNUSED_PUBLIC_OR_PROTECTED_FIELD" level="CRITICAL" enabled="false" />
        <coding_rule class="UWF_FIELD_NOT_INITIALIZED_IN_CONSTRUCTOR" level="MAJOR" enabled="false" />
        <coding_rule class="UWF_NULL_FIELD" level="CRITICAL" enabled="true" />
        <coding_rule class="UWF_UNWRITTEN_FIELD" level="MAJOR" enabled="false" />
        <coding_rule class="UWF_UNWRITTEN_PUBLIC_OR_PROTECTED_FIELD" level="CRITICAL" enabled="false" />
        <coding_rule class="UW_UNCOND_WAIT" level="MAJOR" enabled="true" />
        <coding_rule class="UncommentedMainCheck" level="MAJOR" enabled="false" />
        <coding_rule class="UniquePropertiesCheck" level="MAJOR" enabled="false" />
        <coding_rule class="UnnecessaryParenthesesCheck" level="MINOR" enabled="false" />
        <coding_rule class="UnnecessarySemicolonAfterTypeMemberDeclarationCheck" level="MAJOR" enabled="false" />
        <coding_rule class="UnnecessarySemicolonInEnumerationCheck" level="MAJOR" enabled="false" />
        <coding_rule class="UnnecessarySemicolonInTryWithResourcesCheck" level="MAJOR" enabled="false" />
        <coding_rule class="UnusedImportsCheck" level="INFO" enabled="true" />
        <coding_rule class="UpperEllCheck" level="MINOR" enabled="false" />
        <coding_rule class="VA_FORMAT_STRING_BAD_ARGUMENT" level="CRITICAL" enabled="true" />
        <coding_rule class="VA_FORMAT_STRING_BAD_CONVERSION" level="CRITICAL" enabled="true" />
        <coding_rule class="VA_FORMAT_STRING_BAD_CONVERSION_FROM_ARRAY" level="MAJOR" enabled="true" />
        <coding_rule class="VA_FORMAT_STRING_BAD_CONVERSION_TO_BOOLEAN" level="MAJOR" enabled="true" />
        <coding_rule class="VA_FORMAT_STRING_EXPECTED_MESSAGE_FORMAT_SUPPLIED" level="MAJOR" enabled="false" />
        <coding_rule class="VA_FORMAT_STRING_EXTRA_ARGUMENTS_PASSED" level="MAJOR" enabled="true" />
        <coding_rule class="VA_FORMAT_STRING_ILLEGAL" level="CRITICAL" enabled="true" />
        <coding_rule class="VA_FORMAT_STRING_MISSING_ARGUMENT" level="CRITICAL" enabled="true" />
        <coding_rule class="VA_FORMAT_STRING_NO_PREVIOUS_ARGUMENT" level="CRITICAL" enabled="true" />
        <coding_rule class="VA_FORMAT_STRING_USES_NEWLINE" level="CRITICAL" enabled="false" />
        <coding_rule class="VA_PRIMITIVE_ARRAY_PASSED_TO_OBJECT_VARARG" level="CRITICAL" enabled="true" />
        <coding_rule class="VO_VOLATILE_INCREMENT" level="CRITICAL" enabled="false" />
        <coding_rule class="VO_VOLATILE_REFERENCE_TO_ARRAY" level="MAJOR" enabled="true" />
        <coding_rule class="VR_UNRESOLVABLE_REFERENCE" level="MAJOR" enabled="false" />
        <coding_rule class="VariableDeclarationUsageDistanceCheck" level="MAJOR" enabled="false" />
        <coding_rule class="VisibilityModifierCheck" level="MAJOR" enabled="true" />
        <coding_rule class="WA_AWAIT_NOT_IN_LOOP" level="CRITICAL" enabled="true" />
        <coding_rule class="WA_NOT_IN_LOOP" level="CRITICAL" enabled="true" />
        <coding_rule class="WL_USING_GETCLASS_RATHER_THAN_CLASS_LITERAL" level="CRITICAL" enabled="true" />
        <coding_rule class="WMI_WRONG_MAP_ITERATOR" level="CRITICAL" enabled="true" />
        <coding_rule class="WS_WRITEOBJECT_SYNC" level="CRITICAL" enabled="true" />
        <coding_rule class="WhitespaceAfterCheck" level="MINOR" enabled="false" />
        <coding_rule class="WhitespaceAroundCheck" level="MINOR" enabled="false" />
        <coding_rule class="WriteTagCheck" level="MINOR" enabled="false" />
        <coding_rule class="XFB_XML_FACTORY_BYPASS" level="CRITICAL" enabled="true" />
        <coding_rule class="XSS_REQUEST_PARAMETER_TO_JSP_WRITER" level="CRITICAL" enabled="true" />
        <coding_rule class="XSS_REQUEST_PARAMETER_TO_SEND_ERROR" level="CRITICAL" enabled="true" />
        <coding_rule class="XSS_REQUEST_PARAMETER_TO_SERVLET_WRITER" level="CRITICAL" enabled="true" />
      </profile>
    </profiles>
    <list size="0" />
  </component>
</project>