package com.hzw.ssm.expert.entity;

import com.google.gson.annotations.Expose;
import com.hzw.ssm.fw.base.BaseEntity;
import com.hzw.ssm.fw.util.Page;

import java.util.Date;
import java.util.List;

/**
 * 专家信息表
 *
 * <AUTHOR>
 * @date 2014-10-09
 */
public class ExpertInfoEntity extends BaseEntity {

	private static final long serialVersionUID = 1L;

	private String id;// 主键

	private String user_id;// 用户编号
	private String expertId;// 专家编号

	private String open_id;// 小程序openid

	private String login_code;// 登录名,用于专家信息录入

	private String password;// 密码,用于专家信息录入

	private String expert_num;// 专家编号

	private String user_name;// 姓名

	private String sex;// 性别

	private Date birthday;// 出生年月

	private String politics;// 政治面貌

	private String id_type;// 证件类型

	private String id_no;// 证件号码

	private String pre_idNo;// 身份证正面

	private String suffix_idNo;// 身份证后面

	private String id_fileid;// 身份证复印件地址

	private String old_id;// 身份证复印件名称

	private String ICBackName;// 身份证反面照名称

	private String ICBackFileId;// 身份证反面照地址

	private String photo_fileid;// 证件照片

	private String old_photo;	// 证件照片名称

	private String school;// 毕业院校

	private String major;// 所学专业

	private String certificate_fileid;// 毕业证书复印件地址

	private String old_certificate;// 毕业证书复印件名称

	private String educations;// 最高学历

	private String degree;// 最高学位

	private Long grade;// 专家级别条件修改为国家或地方两种级别

	private String province;// 所在省份

	private String city;// 所在城市

	private String zone;// 所在区或县

	private String district;// 区域（省 市 县区）例如：江苏省 南京市 白下区

	private String technical_tital;// 职称
	
	private String titalName;//用户职称名称
	
	private String technical_filed;// 职称证书上传

	private String old_technical;// 职称证书上传名称

	private String position;// 职务

	private String company;// 工作单位

	private String company_addr;// 单位地址

	private String company_phone;// 单位电话

	private String company_zipcode;// 单位邮编

	private String mobilephone;// 移动电话

	private String email;// 电子邮箱

	private String qq_num;// QQ号码

	private String document_no;// 档案号

	private String special_skill;// 学术专长与研究方向

	private String bid_experience;// 评标经验

	private String training_experience;// 培训经历

	private String remark;// 其他说明

	private Long status;// 专家状态 0:注册 1:待审核 2:二次待审核 3:审核通过 4:禁用(不可评标) 5：修改待审核
						// 6：修改待审核二次审核 8:审核通过

	private String status_;

	private String headImage;// 小程序 头像

	private Long oldStatus;// 老状态，用于修改留痕

	@Expose
	private Date create_time;// 创建时间

	@Expose
	private Date modify_time;// 修改时间

	private String certificate;// 系统证书

	private Date audit_time;// 入库时间

	private Date certificate_time;// 发证时间

	private Date expire_time;// 到期时间

	private List<ExpertPracticeEntity> practiceList;// 执业资格

	private List<ExpertMajorEntity> majorList;// 评标专业

	private double eval_score;// 评价得分

	private String specialty_name;// 评标专业，审核时用于列表页面显示

	private String spe_id;// 评标专业id ,用于列表查询条件

	private String spe_parent;// 评标专业父类id，用于列表查询条件

	private String search_begin_time;// 用于列表查询条件

	private String search_end_time;// 用于列表查询条件

	/** 审批意见 */
	private String auditReason;// 审批意见

	private String aArea;// 行政区域
	private String bidMajor;// 评标专业

	private Long join_status;// 是否参加 0:参加 1:不参加 2:未通知

	private Long expertWay;// 记录当前专家的状态是应急抽取还是语音抽取(适用于语音抽取模块)

	private String reason;// 不同意理由

	private String referrer_qrcode;// 业务员二维码编号

	private String qt_reason;// 其他原因

	private Date call_time;// 通知时间

	private String modify_user;// 修改人，用于修改留痕封装数据

	private String modify_role;// 修改角色，用于修改留痕封装数据

	private String modify_reason;// 修改原因，用于修改留痕封装数据

	private String extractResultId;// 专家抽取结果表ID

	private Date appraise_time;// 评价时间

	private Long is_appraise;// 是否已评价(0:否,1:是)

	private String conditionId;// 条件id

	private String project_no;

	private String project_name;

	private String manager;

	private String audit_user;// 审核人

	private String tableField;

	private String tableName;

	private String orderStatus;

	/** 录入入库标识 */
	private Integer enterFlag;// 录入入库标识：1：管理员录入入库 2：专家注册入库3:专家微信小程序注册

	private String roleId;// 角色id
	/**
	 * 是否符合条件
	 */
	private String is_qual;
	/**
	 * 校验内容
	 */
	private String check_msg;

	private Integer isillegal;
	private String illegal_file_path;
	private String illegal_file_name;
	private String illegal_detail;

	private Integer age;           // 年龄
	private Integer illegalCount;  // 违规次数
	private String healthy;        // 健康状况

	private Date outTime;
	private String outReason;
	private String sort_no;

	private String appraiseType;

	private Long is_reward;// 是否已奖励(0:否,1:是)

	private String tender;// 招标人
	private String token;// 小程序token
	private String expirationTime;// 过期时间

	private Date pause_startTime;// 暂停开始时间

	private Date pause_endTime;// 暂停结束时间

	private Integer pause_number;// 累计暂停月数

	private Integer result_num;// 专家抽取次数

	private Integer startNum;// 专家查询条件中抽取次数的范围
	private Integer endNum;// 专家查询条件中抽取次数的范围

	private String expertType;// 专家类型 A1 A2 C
	private String phoneCode;// 小程序登录验证码

	private Date technicalTime;// 专家专家职称获取时间

	private Integer isApprove;// 是否审批 0：未审批；1：已审批；2：审批拒绝

	//基础信息是否完善
	private String isBasicInfo;
	//职称信息是否完善
	private String isCertificateInfo;
	//拒绝原因
	private String rejectionReason;
	//是否重复提交审核
	private String isDuplicateSub;

	/**
	 * 抽取时被抽取出时的专业
	 */
	private String majorselection;
	
	/**
	 * 该专业参加的人数 
	 */
	private Integer count;
	public String getIsDuplicateSub() {
		return isDuplicateSub;
	}

	public void setIsDuplicateSub(String isDuplicateSub) {
		this.isDuplicateSub = isDuplicateSub;
	}

	public String getRejectionReason() {
		return rejectionReason;
	}

	public void setRejectionReason(String rejectionReason) {
		this.rejectionReason = rejectionReason;
	}

	public Integer getIsApprove() {
		return isApprove;
	}

	public void setIsApprove(Integer isApprove) {
		this.isApprove = isApprove;
	}

	public Date getPause_startTime() {
		return pause_startTime;
	}

	public void setPause_startTime(Date pause_startTime) {
		this.pause_startTime = pause_startTime;
	}

	public Date getPause_endTime() {
		return pause_endTime;
	}

	public void setPause_endTime(Date pause_endTime) {
		this.pause_endTime = pause_endTime;
	}

	public Integer getPause_number() {
		return pause_number;
	}

	public void setPause_number(Integer pause_number) {
		this.pause_number = pause_number;
	}

	public String getTender() {
		return tender;
	}

	public void setTender(String tender) {
		this.tender = tender;
	}

	public Long getIs_reward() {
		return is_reward;
	}

	public void setIs_reward(Long is_reward) {
		this.is_reward = is_reward;
	}

	public String getIllegal_detail() {
		return illegal_detail;
	}

	public String getAppraiseType() {
		return appraiseType;
	}

	public void setAppraiseType(String appraiseType) {
		this.appraiseType = appraiseType;
	}

	public void setIllegal_detail(String illegal_detail) {
		this.illegal_detail = illegal_detail;
	}

	/** 分页 */
	private Page page;

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getUser_id() {
		return user_id;
	}

	public void setUser_id(String userId) {
		user_id = userId;
	}

	public String getUser_name() {
		return user_name;
	}

	public void setUser_name(String userName) {
		user_name = userName;
	}

	public String getSex() {
		return sex;
	}

	public void setSex(String sex) {
		this.sex = sex;
	}

	public Date getBirthday() {
		return birthday;
	}

	public void setBirthday(Date birthday) {
		this.birthday = birthday;
	}

	public String getPolitics() {
		return politics;
	}

	public void setPolitics(String politics) {
		this.politics = politics;
	}

	public String getId_type() {
		return id_type;
	}

	public void setId_type(String idType) {
		id_type = idType;
	}

	public String getId_no() {
		return id_no;
	}

	public void setId_no(String idNo) {
		id_no = idNo;
	}

	public String getId_fileid() {
		return id_fileid;
	}

	public void setId_fileid(String idFileid) {
		id_fileid = idFileid;
	}

	public String getSchool() {
		return school;
	}

	public void setSchool(String school) {
		this.school = school;
	}

	public String getMajor() {
		return major;
	}

	public void setMajor(String major) {
		this.major = major;
	}

	public String getCertificate_fileid() {
		return certificate_fileid;
	}

	public void setCertificate_fileid(String certificateFileid) {
		certificate_fileid = certificateFileid;
	}

	public String getEducations() {
		return educations;
	}

	public void setEducations(String educations) {
		this.educations = educations;
	}

	public String getDegree() {
		return degree;
	}

	public void setDegree(String degree) {
		this.degree = degree;
	}

	public Long getGrade() {
		return grade;
	}

	public void setGrade(Long grade) {
		this.grade = grade;
	}

	public String getProvince() {
		return province;
	}

	public void setProvince(String province) {
		this.province = province;
	}

	public String getCity() {
		return city;
	}

	public void setCity(String city) {
		this.city = city;
	}

	public String getZone() {
		return zone;
	}

	public void setZone(String zone) {
		this.zone = zone;
	}

	public String getCompany() {
		return company;
	}

	public void setCompany(String company) {
		this.company = company;
	}

	public String getCompany_addr() {
		return company_addr;
	}

	public void setCompany_addr(String companyAddr) {
		company_addr = companyAddr;
	}

	public String getCompany_phone() {
		return company_phone;
	}

	public void setCompany_phone(String companyPhone) {
		company_phone = companyPhone;
	}

	public String getCompany_zipcode() {
		return company_zipcode;
	}

	public void setCompany_zipcode(String companyZipcode) {
		company_zipcode = companyZipcode;
	}

	public String getMobilephone() {
		return mobilephone;
	}

	public void setMobilephone(String mobilephone) {
		this.mobilephone = mobilephone;
	}

	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public String getSpecial_skill() {
		return special_skill;
	}

	public void setSpecial_skill(String specialSkill) {
		special_skill = specialSkill;
	}

	public String getBid_experience() {
		return bid_experience;
	}

	public void setBid_experience(String bidExperience) {
		bid_experience = bidExperience;
	}

	public String getTraining_experience() {
		return training_experience;
	}

	public void setTraining_experience(String trainingExperience) {
		training_experience = trainingExperience;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public Long getStatus() {
		return status;
	}

	public void setStatus(Long status) {
		this.status = status;
	}

	public Date getCreate_time() {
		return create_time;
	}

	public void setCreate_time(Date createTime) {
		create_time = createTime;
	}

	public Date getModify_time() {
		return modify_time;
	}

	public void setModify_time(Date modifyTime) {
		modify_time = modifyTime;
	}

	public String getCertificate() {
		return certificate;
	}

	public void setCertificate(String certificate) {
		this.certificate = certificate;
	}

	public Date getAudit_time() {
		return audit_time;
	}

	public void setAudit_time(Date auditTime) {
		audit_time = auditTime;
	}

	public void setCertificate_time(Date certificateTime) {
		certificate_time = certificateTime;
	}

	public Date getExpire_time() {
		return expire_time;
	}

	public void setExpire_time(Date expireTime) {
		expire_time = expireTime;
	}

	public List<ExpertMajorEntity> getMajorList() {
		return majorList;
	}

	public void setMajorList(List<ExpertMajorEntity> majorList) {
		this.majorList = majorList;
	}

	public List<ExpertPracticeEntity> getPracticeList() {
		return practiceList;
	}

	public void setPracticeList(List<ExpertPracticeEntity> practiceList) {
		this.practiceList = practiceList;
	}

	public double getEval_score() {
		return eval_score;
	}

	public void setEval_score(double evalScore) {
		eval_score = evalScore;
	}

	public String getSpecialty_name() {
		return specialty_name;
	}

	public void setSpecialty_name(String specialtyName) {
		specialty_name = specialtyName;
	}

	public String getSpe_id() {
		return spe_id;
	}

	public void setSpe_id(String speId) {
		spe_id = speId;
	}

	public String getSpe_parent() {
		return spe_parent;
	}

	public void setSpe_parent(String speParent) {
		spe_parent = speParent;
	}

	public Page getPage() {
		return page;
	}

	public void setPage(Page page) {
		this.page = page;
	}

	public String getSearch_begin_time() {
		return search_begin_time;
	}

	public void setSearch_begin_time(String searchBeginTime) {
		search_begin_time = searchBeginTime;
	}

	public String getSearch_end_time() {
		return search_end_time;
	}

	public void setSearch_end_time(String searchEndTime) {
		search_end_time = searchEndTime;
	}

	public String getAuditReason() {
		return auditReason;
	}

	public void setAuditReason(String auditReason) {
		this.auditReason = auditReason;
	}

	public Date getCertificate_time() {
		return certificate_time;
	}

	public String getaArea() {
		return aArea;
	}

	public void setaArea(String aArea) {
		this.aArea = aArea;
	}

	public String getBidMajor() {
		return bidMajor;
	}

	public void setBidMajor(String bidMajor) {
		this.bidMajor = bidMajor;
	}

	public Long getJoin_status() {
		return join_status;
	}

	public void setJoin_status(Long joinStatus) {
		join_status = joinStatus;
	}

	public String getReason() {
		return reason;
	}

	public void setReason(String reason) {
		this.reason = reason;
	}

	public Date getCall_time() {
		return call_time;
	}

	public void setCall_time(Date callTime) {
		call_time = callTime;
	}

	public String getModify_user() {
		return modify_user;
	}

	public void setModify_user(String modifyUser) {
		modify_user = modifyUser;
	}

	public String getModify_role() {
		return modify_role;
	}

	public void setModify_role(String modifyRole) {
		modify_role = modifyRole;
	}

	public String getModify_reason() {
		return modify_reason;
	}

	public void setModify_reason(String modifyReason) {
		modify_reason = modifyReason;
	}

	public String getExtractResultId() {
		return extractResultId;
	}

	public void setExtractResultId(String extractResultId) {
		this.extractResultId = extractResultId;
	}

	public Date getAppraise_time() {
		return appraise_time;
	}

	public void setAppraise_time(Date appraiseTime) {
		appraise_time = appraiseTime;
	}

	public Long getIs_appraise() {
		return is_appraise;
	}

	public void setIs_appraise(Long isAppraise) {
		is_appraise = isAppraise;
	}

	public String getConditionId() {
		return conditionId;
	}

	public void setConditionId(String conditionId) {
		this.conditionId = conditionId;
	}

	public String getProject_no() {
		return project_no;
	}

	public void setProject_no(String projectNo) {
		project_no = projectNo;
	}

	public String getProject_name() {
		return project_name;
	}

	public void setProject_name(String projectName) {
		project_name = projectName;
	}

	public String getManager() {
		return manager;
	}

	public void setManager(String manager) {
		this.manager = manager;
	}

	public String getLogin_code() {
		return login_code;
	}

	public void setLogin_code(String loginCode) {
		login_code = loginCode;
	}

	public String getPassword() {
		return password;
	}

	public void setPassword(String password) {
		this.password = password;
	}

	public String getDistrict() {
		return district;
	}

	public void setDistrict(String district) {
		this.district = district;
	}

	public String getExpert_num() {
		return expert_num;
	}

	public void setExpert_num(String expertNum) {
		expert_num = expertNum;
	}

	public String getTechnical_tital() {
		return technical_tital;
	}

	public void setTechnical_tital(String technicalTital) {
		technical_tital = technicalTital;
	}
	

	public String getTitalName() {
		return titalName;
	}

	public void setTitalName(String titalName) {
		this.titalName = titalName;
	}

	public String getPosition() {
		return position;
	}

	public void setPosition(String position) {
		this.position = position;
	}

	public String getQq_num() {
		return qq_num;
	}

	public void setQq_num(String qqNum) {
		qq_num = qqNum;
	}

	public String getPhoto_fileid() {
		return photo_fileid;
	}

	public void setPhoto_fileid(String photoFileid) {
		photo_fileid = photoFileid;
	}

	public String getTechnical_filed() {
		return technical_filed;
	}

	public void setTechnical_filed(String technicalFiled) {
		technical_filed = technicalFiled;
	}

	public Long getOldStatus() {
		return oldStatus;
	}

	public void setOldStatus(Long oldStatus) {
		this.oldStatus = oldStatus;
	}

	public String getDocument_no() {
		return document_no;
	}

	public void setDocument_no(String documentNo) {
		document_no = documentNo;
	}

	public String getAudit_user() {
		return audit_user;
	}

	public void setAudit_user(String audit_user) {
		this.audit_user = audit_user;
	}

	public String getStatus_() {
		return status_;
	}

	public void setStatus_(String status_) {
		this.status_ = status_;
	}

	public String getOld_id() {
		return old_id;
	}

	public void setOld_id(String old_id) {
		this.old_id = old_id;
	}

	public String getOld_photo() {
		return old_photo;
	}

	public void setOld_photo(String old_photo) {
		this.old_photo = old_photo;
	}

	public String getOld_certificate() {
		return old_certificate;
	}

	public void setOld_certificate(String old_certificate) {
		this.old_certificate = old_certificate;
	}

	public String getOld_technical() {
		return old_technical;
	}

	public void setOld_technical(String old_technical) {
		this.old_technical = old_technical;
	}

	public String getTableField() {
		return tableField;
	}

	public void setTableField(String tableField) {
		this.tableField = tableField;
	}

	public String getTableName() {
		return tableName;
	}

	public void setTableName(String tableName) {
		this.tableName = tableName;
	}

	public String getOrderStatus() {
		return orderStatus;
	}

	public void setOrderStatus(String orderStatus) {
		this.orderStatus = orderStatus;
	}

	public Integer getEnterFlag() {
		return enterFlag;
	}

	public void setEnterFlag(Integer enterFlag) {
		this.enterFlag = enterFlag;
	}

	public String getRoleId() {
		return roleId;
	}

	public void setRoleId(String roleId) {
		this.roleId = roleId;
	}

	public Integer getIsillegal() {
		return isillegal;
	}

	public void setIsillegal(Integer isillegal) {
		this.isillegal = isillegal;
	}

	public String getIllegal_file_path() {
		return illegal_file_path;
	}

	public void setIllegal_file_path(String illegal_file_path) {
		this.illegal_file_path = illegal_file_path;
	}

	public String getIllegal_file_name() {
		return illegal_file_name;
	}

	public void setIllegal_file_name(String illegal_file_name) {
		this.illegal_file_name = illegal_file_name;
	}

	public Date getOutTime() {
		return outTime;
	}

	public void setOutTime(Date outTime) {
		this.outTime = outTime;
	}

	public String getOutReason() {
		return outReason;
	}

	public void setOutReason(String outReason) {
		this.outReason = outReason;
	}

	public Integer getAge() {
		return age;
	}

	public void setAge(Integer age) {
		this.age = age;
	}

	public Integer getIllegalCount() {
		return illegalCount;
	}

	public void setIllegalCount(Integer illegalCount) {
		this.illegalCount = illegalCount;
	}

	public String getHealthy() {
		return healthy;
	}

	public void setHealthy(String healthy) {
		this.healthy = healthy;
	}

	public String getQt_reason() {
		return qt_reason;
	}

	public void setQt_reason(String qt_reason) {
		this.qt_reason = qt_reason;
	}

	public String getSort_no() {
		return sort_no;
	}

	public void setSort_no(String sort_no) {
		this.sort_no = sort_no;
	}

	public Long getExpertWay() {
		return expertWay;
	}

	public void setExpertWay(Long expertWay) {
		this.expertWay = expertWay;
	}

	public Integer getResult_num() {
		return result_num;
	}

	public void setResult_num(Integer result_num) {
		this.result_num = result_num;
	}

	public Integer getStartNum() {
		return startNum;
	}

	public void setStartNum(Integer startNum) {
		this.startNum = startNum;
	}

	public Integer getEndNum() {
		return endNum;
	}

	public void setEndNum(Integer endNum) {
		this.endNum = endNum;
	}

	public String getIs_qual() {
		return is_qual;
	}

	public void setIs_qual(String is_qual) {
		this.is_qual = is_qual;
	}

	public String getCheck_msg() {
		return check_msg;
	}

	public void setCheck_msg(String check_msg) {
		this.check_msg = check_msg;
	}

	public String getOpen_id() {
		return open_id;
	}

	public void setOpen_id(String open_id) {
		this.open_id = open_id;
	}

	/**
	 * @return the expertType
	 */
	public String getExpertType() {
		return expertType;
	}

	public void setExpertType(String expertType) {
		this.expertType = expertType;
	}

	public String getPre_idNo() {
		return pre_idNo;
	}

	public void setPre_idNo(String pre_idNo) {
		this.pre_idNo = pre_idNo;
	}

	public String getSuffix_idNo() {
		return suffix_idNo;
	}

	public void setSuffix_idNo(String suffix_idNo) {
		this.suffix_idNo = suffix_idNo;
	}

	public String getToken() {
		return token;
	}

	public void setToken(String token) {
		this.token = token;
	}

	public String getExpirationTime() {
		return expirationTime;
	}

	public void setExpirationTime(String expirationTime) {
		this.expirationTime = expirationTime;
	}

	public String getHeadImage() {
		return headImage;
	}

	public void setHeadImage(String headImage) {
		this.headImage = headImage;
	}

	public String getExpertId() {
		return expertId;
	}

	public void setExpertId(String expertId) {
		this.expertId = expertId;
	}

	public String getPhoneCode() {
		return phoneCode;
	}

	public void setPhoneCode(String phoneCode) {
		this.phoneCode = phoneCode;
	}

	/**
	 * @return the technicalTime
	 */
	public Date getTechnicalTime() {
		return technicalTime;
	}

	/**
	 * @param technicalTime
	 *            the technicalTime to set
	 */
	public void setTechnicalTime(Date technicalTime) {
		this.technicalTime = technicalTime;
	}

	/**
	 * @return the isBasicInfo
	 */
	public String getIsBasicInfo() {
		return isBasicInfo;
	}

	/**
	 * @param isBasicInfo the isBasicInfo to set
	 */
	public void setIsBasicInfo(String isBasicInfo) {
		this.isBasicInfo = isBasicInfo;
	}

	/**
	 * @return the isCertificateInfo
	 */
	public String getIsCertificateInfo() {
		return isCertificateInfo;
	}

	/**
	 * @param isCertificateInfo the isCertificateInfo to set
	 */
	public void setIsCertificateInfo(String isCertificateInfo) {
		this.isCertificateInfo = isCertificateInfo;
	}

	/**
	 * @return the iCBackName
	 */
	public String getICBackName() {
		return ICBackName;
	}

	/**
	 * @param iCBackName the iCBackName to set
	 */
	public void setICBackName(String iCBackName) {
		ICBackName = iCBackName;
	}

	/**
	 * @return the iCBackFileId
	 */
	public String getICBackFileId() {
		return ICBackFileId;
	}

	/**
	 * @param iCBackFileId the iCBackFileId to set
	 */
	public void setICBackFileId(String iCBackFileId) {
		ICBackFileId = iCBackFileId;
	}

	public String getMajorselection() {
		return majorselection;
	}

	public void setMajorselection(String majorselection) {
		this.majorselection = majorselection;
	}

	public Integer getCount() {
		return count;
	}

	public void setCount(Integer count) {
		this.count = count;
	}

	public String getReferrer_qrcode() {
		return referrer_qrcode;
	}

	public void setReferrer_qrcode(String referrer_qrcode) {
		this.referrer_qrcode = referrer_qrcode;
	}
}
