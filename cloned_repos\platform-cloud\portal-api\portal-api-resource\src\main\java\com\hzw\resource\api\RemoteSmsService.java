package com.hzw.resource.api;

import com.hzw.common.core.exception.ServiceException;
import com.hzw.resource.api.domain.SmsSendInfo;
import com.hzw.resource.api.domain.SysSms;

import java.util.LinkedHashMap;

/**
 * 短信服务
 *
 * <AUTHOR>
 */
public interface RemoteSmsService {

    /**
     * 发送短信
     *
     * @param phones     电话号(多个逗号分割)
     * @param templateId 模板id
     * @param param      模板对应参数
     */
    SysSms send(String phones, String templateId, LinkedHashMap<String, String> param) throws ServiceException;

    /**
     * 发送短信
     * @param info
     * @return
     */
    Boolean sendMessage(SmsSendInfo info);

    Boolean checkSmsCode(String phoneNumber, String smsCode);
}
