package com.hzw.ssm.report.service;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.hzw.ssm.report.entity.QueryBean;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.hzw.ssm.fw.util.DateUtil;
import com.hzw.ssm.report.dao.ReportMapper;
import com.hzw.ssm.report.entity.ReportEntity;

/**
 * 报表
 * <AUTHOR>
 *
 */
@Service
public class ReportService {
	@Autowired
	private ReportMapper reportMapper;
	/**
	 * 明细分页显示
	 * @param rep
	 * @return
	 */
	public List<ReportEntity> selectReportDetail(ReportEntity rep){
		List<ReportEntity> reportEntityList =new ArrayList<ReportEntity>();
		//获取查询的list数据集合
		List<ReportEntity> reportLst = reportMapper.selectReportDetail(rep);
		if(reportLst !=null) {
			reportEntityList = reportLst;
		}
		return reportEntityList;
	}
	//扣分原因
	public List<ReportEntity>  queryScoreInfo(ReportEntity rep){
		return reportMapper.queryScoreInfo(rep);
	}
	public List<QueryBean>  queryresultId(ReportEntity rep){
		return reportMapper.queryresultId(rep);
	}

	public List<QueryBean> selectDetailResultId(ReportEntity rep){
		List<QueryBean> list = reportMapper.selectDetailResultId(rep);
		return list;
	}
	//考核明细
	public List<ReportEntity> selectReportDetails(ReportEntity rep){
		List<ReportEntity> reportDetailsList = reportMapper.selectReportDetails(rep);
		return reportDetailsList;
	}
	/**
	 * 汇总分页显示
	 * @param rep
	 * @return
	 */
	public List<ReportEntity> queryPageReportSummary(ReportEntity rep){
		List<ReportEntity> reportEntityList = reportMapper.queryPageReportSummary(rep);
		return reportEntityList;
	}	
	/**
	 * 注销分页显示
	 * @param rep
	 * @return
	 */
	public List<ReportEntity> queryPageReportCancellation(ReportEntity rep){
		List<ReportEntity> reportEntityList =new ArrayList<ReportEntity>();
		if(rep.getCancellationWay().equals("1")) {
			reportEntityList = reportMapper.queryPageReportCancellation(rep);
		}else {
			reportEntityList = reportMapper.queryPageReportCancellationT(rep);
		}
		
		return reportEntityList;
	}	
	
	/**
	 * 处理明细数据（导出）
	 * @return
	 */
	public List<List<String>> processData(ReportEntity rep,int index){
		List<List<String>> pList  = new ArrayList<List<String>>();
		List<ReportEntity> reportEntityList =new ArrayList<ReportEntity>();
		//获取查询的list数据集合
		List<ReportEntity> reportLst = reportMapper.queryReportDetail(rep);
		
		// 保留数据
		String savaString = "";
		if(reportLst !=null) {
			for(int i = 0 ;i<reportLst.size();i++) {
				List<String> cellLst = new ArrayList<String>();
				//根据前台选择的分组条件，将相同的列中的数据设置为空
				if(i==0) {
					if(rep.getStatus().equals("1")) {
						savaString = Double.toString(reportLst.get(i).getScore());
					}else if(rep.getStatus().equals("2")) {
						savaString =reportLst.get(i).getExpertId();
					}else {
						savaString =reportLst.get(i).getProjectNo();
					}
				}else {
					if(rep.getStatus().equals("1")) {
						if(Double.toString(reportLst.get(i).getScore()).equals(savaString)) {
							reportLst.get(i).setScore(0);
						}else {
							savaString = Double.toString(reportLst.get(i).getScore());
						}
					}else if(rep.getStatus().equals("2")) {
						if(reportLst.get(i).getExpertId().equals(savaString)) {
							reportLst.get(i).setExpertName("");
						}else {
							savaString =reportLst.get(i).getExpertId();
						}
					}else {
						if(reportLst.get(i).getProjectNo().equals(savaString)) {
							reportLst.get(i).setProjectName("");
						}else {
							savaString =reportLst.get(i).getProjectNo();
						}
					}
				}
				if(reportLst.get(i).getScore()==0) {
					cellLst.add("");
				}else {
					cellLst.add(Double.toString(Math.ceil(reportLst.get(i).getScore())));
				}
				cellLst.add(reportLst.get(i).getExpertName());
				cellLst.add(reportLst.get(i).getPhone());
				cellLst.add(reportLst.get(i).getIdCard());
				cellLst.add(reportLst.get(i).getProjectName());
				cellLst.add(reportLst.get(i).getProjectNo());
				cellLst.add(reportLst.get(i).getManager());
				cellLst.add(DateUtil.dateToString(reportLst.get(i).getBidTime(),"yyyy-MM-dd HH:mm"));
				cellLst.add(reportLst.get(i).getAppraiseColumn());
				pList.add(cellLst);
			}
			
		}
		return pList;
	}
	/**
	 * 汇总数据处理（此方法需要传递2部分值，1.累计分数大于15分 2.一次性扣20分
	 * @param rep
	 * @return
	 */
	public Map<String,List<List<String>>> sumData(ReportEntity rep){
		Map<String,List<List<String>>> map =  new HashMap<String, List<List<String>>>();
		
		//存放累计大于15分(一次性20分的除外)
		List<List<String>> gList  = new ArrayList<List<String>>();
		String IdCode_15 = "";
		String IdCode_20 = "";
		//存放一次性扣20分
//		List<List<String>> sList = new ArrayList<List<String>>();
		String sIdCode="";
		//查询抽取结果ID
		List<ReportEntity> reportLst=new ArrayList<ReportEntity>();
		List<String> list=reportMapper.selectDetailExpertId(rep);
		if (CollectionUtils.isNotEmpty(list)){
			for (String expertId:list){
				rep.setExpertId(expertId);
				List<ReportEntity> reportList = reportMapper.queryReportSummary(rep);
				reportLst.addAll(reportList);
			}
		}
		/*List<QueryBean> list=reportMapper.queryPageDetailResultId(rep);
		if (CollectionUtils.isNotEmpty(list)){
			for (QueryBean bean:list){
				rep.setExtractResultId(bean.getExtractResultId());
				rep.setAppraiseTimeStr(bean.getReviewTimeStr().substring(0,bean.getReviewTimeStr().length()-2));
				List<ReportEntity> reportList = reportMapper.queryReportSummary(rep);
				reportLst.addAll(reportList);
			}
		}*/
		//获取查询的list数据集合
//		List<ReportEntity> reportLst = reportMapper.queryReportSummary(rep);
		if(reportLst !=null) {
			for(int i = 0 ;i<reportLst.size();i++) {
				List<String> cellLst = new ArrayList<String>();
				if (i>0){
					if (reportLst.get(i).getIdCard().equals(reportLst.get(i-1).getIdCard())){
						gList=copy(gList,i,cellLst,reportLst);
						cellLst.set(0, "");
						cellLst.set(1, "");
						cellLst.set(2, "");
						cellLst.set(3, "");
						cellLst.set(4, "");
						/*if (gList.get(i)){
							cellLst.set(9, "暂停评标12个月");
						}else if("暂停评标6个月".equals(cellLst.get(9))){

						}*/

						cellLst.add("");
					}else{
						gList=copy(gList,i,cellLst,reportLst);
					}
				}else{
					gList=copy(gList,i,cellLst,reportLst);
				}
				//总分>=15 <20
				/*if (reportLst.get(i).getSumScore()>=15&&reportLst.get(i).getSumScore()<20){
						cellLst.set(9, "暂停评标3个月");
						cellLst.add("暂停评标3个月");
					gList.add(cellLst);
				}
				if (reportLst.get(i).getSumScore()>=20){
						cellLst.set(9, "暂停评标6个月");
						cellLst.add("暂停评标6个月");
					gList.add(cellLst);
				}
				if (reportLst.get(i).getScore()>=20){
						cellLst.set(9, "暂停评标12个月");
						cellLst.add("暂停评标12个月");
					sList.add(cellLst);
				}*/
				//判断是否是一次性扣20分的
				/*if(reportLst.get(i).getScore()>=20) {
					if (reportLst.get(i).getIdCard().equals(reportLst.get(i-1).getIdCard())){
						gList.get(i-1).set(9,"暂停评标12个月");
					}
						cellLst.set(9, "暂停评标12个月");
						cellLst.add("暂停评标12个月");
						sIdCode = reportLst.get(i).getIdCard();
						IdCode_15=sIdCode;
						IdCode_20=sIdCode;
					 if(sIdCode.equals(reportLst.get(i).getIdCard())) {
						cellLst.set(0, "");
						cellLst.set(1, "");
						cellLst.set(2, "");
						cellLst.set(3, "");
						cellLst.set(4, "");
						cellLst.set(9, "");
						cellLst.add("");
					}else {
						cellLst.set(0, "");
						cellLst.add("");
						sIdCode = reportLst.get(i).getIdCard();
					}
					gList.add(cellLst);
				}else {
					if(reportLst.get(i).getSumScore()>=0&&reportLst.get(i).getSumScore()<20) {
						if(IdCode_15=="") {
							cellLst.set(9, "暂停评标3个月");
							cellLst.add("暂停评标3个月");
							IdCode_15 = reportLst.get(i).getIdCard();
							sIdCode=IdCode_15;
						}else if(IdCode_15.equals(reportLst.get(i).getIdCard())) {
							cellLst.set(0, "");
							cellLst.set(1, "");
							cellLst.set(2, "");
							cellLst.set(3, "");
							cellLst.set(4, "");
							cellLst.set(9, "");
							cellLst.add("");
						}else {
							cellLst.set(0, "");
							cellLst.add("");
							IdCode_15 = reportLst.get(i).getIdCard();
						}
						gList.add(cellLst);
					}else if(reportLst.get(i).getSumScore()>=20){
						if(IdCode_20=="") {
							cellLst.set(9, "暂停评标6个月");
							cellLst.add("暂停评标6个月");
							IdCode_20 = reportLst.get(i).getIdCard();
							sIdCode=IdCode_20;
						}else if(IdCode_20.equals(reportLst.get(i).getIdCard())) {
							cellLst.set(0, "");
							cellLst.set(1, "");
							cellLst.set(2, "");
							cellLst.set(3, "");
							cellLst.set(4, "");
							cellLst.set(9, "");
							cellLst.add("");
						}else {
							cellLst.set(0, "");
							cellLst.add("");
							IdCode_20 = reportLst.get(i).getIdCard();
						}
						gList.add(cellLst);
					}
				}*/
			}
			map.put("gList", gList);
//			map.put("sList", sList);
		}
		return map;
	}
	//赋值
	public List<List<String>> copy(List<List<String>> gList,int i,List<String> cellLst,List<ReportEntity> reportLst){
		cellLst.add(reportLst.get(i).getYear());//统计年度
		cellLst.add(Double.toString(reportLst.get(i).getSumScore()));//扣分累计
		cellLst.add(reportLst.get(i).getExpertName());//姓名
		cellLst.add(reportLst.get(i).getPhone());//手机号
		cellLst.add(reportLst.get(i).getIdCard());//身份证
		cellLst.add(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(reportLst.get(i).getReviewTime()));//时间
		cellLst.add(reportLst.get(i).getAppraiseColumn());//扣分原因
		cellLst.add(String.valueOf(reportLst.get(i).getScore()));//扣分
		cellLst.add(String.valueOf(reportLst.get(i).getDecimationBatch()));//批次号
		//处理意见
		if(reportLst.get(i).getScore()>=20) {
//			cellLst.set(9, "暂停评标12个月");
			cellLst.add("暂停评标12个月");
			gList.add(cellLst);
		}else if (reportLst.get(i).getSumScore()>=20){
//			cellLst.set(9, "暂停评标6个月");
			cellLst.add("暂停评标6个月");
			gList.add(cellLst);
		}else if(reportLst.get(i).getSumScore()>=15&&reportLst.get(i).getSumScore()<20){
//			cellLst.set(9, "暂停评标3个月");
			cellLst.add("暂停评标3个月");
			gList.add(cellLst);
		}else{
			cellLst.add("");
			gList.add(cellLst);
		}
		return gList;
	}
	/**
	 * 注销导出
	 * @param rep
	 * @return
	 */
	public List<List<String>> cancellationDate(ReportEntity rep){
		String idCode ="";
		List<List<String>> pList  = new ArrayList<List<String>>();
		//获取查询的list数据集合
		List<ReportEntity> reportLst = new ArrayList<ReportEntity>();
		if(rep.getCancellationWay().equals("1")) {
			reportLst = reportMapper.queryCancellationDate(rep);
		}else {
			reportLst = reportMapper.queryCancellationTDate(rep);
		}
		if(reportLst !=null) {
			for(int i = 0 ;i<reportLst.size();i++) {
				List<String> cellLst = new ArrayList<String>();
				cellLst.add(String.valueOf(i+1));
				cellLst.add(reportLst.get(i).getExpertName());
				cellLst.add(reportLst.get(i).getPhone());
				cellLst.add(reportLst.get(i).getIdCard());
				if(idCode=="") {
					idCode=reportLst.get(i).getIdCard();
				}else if(reportLst.get(i).getIdCard().equals(idCode)){
					cellLst.set(1, "");
					cellLst.set(2, "");
					cellLst.set(3, "");
				}else {
					idCode=reportLst.get(i).getIdCard();
				}
				if(rep.getCancellationWay().equals("1")) {
					cellLst.add(reportLst.get(i).getOutTime());
					cellLst.add(reportLst.get(i).getOutReason());
					cellLst.add("");
				}else {
					cellLst.add(DateUtil.dateToString(reportLst.get(i).getBidTime(),"yyyy-MM-dd HH:mm"));
					cellLst.add(reportLst.get(i).getOutReason());
					cellLst.add("来自："+reportLst.get(i).getProjectName()+"\n"+"项目负责人："+reportLst.get(i).getManager());
				}
				
				pList.add(cellLst);
			}
		}
		return pList;
	}
	/**
	 * 更新扣分审核状态
	 */
	public void updateScoreStatus(ReportEntity reportEntity){

		//更新评价结果表
		reportMapper.updateScoreStatus(reportEntity);
		//更新评价表
		reportMapper.updateAppraiseStatus(reportEntity);
	}
	public ReportEntity querySumScore(ReportEntity reportEntity){
		return reportMapper.querySumScore(reportEntity);
	}
	public void updateNewScore(ReportEntity reportEntity){
		 reportMapper.updateNewScore(reportEntity);
	}
	public void updateRejectionReason(ReportEntity reportEntity){
		 reportMapper.updateRejectionReason(reportEntity);
	}
	public void updateAppraiseInfo(ReportEntity reportEntity){
		reportMapper.updateAppraiseInfo(reportEntity);
	}
	public String queryExpertId(ReportEntity reportEntity){
		return reportMapper.queryExpertId(reportEntity);
	}
	public String queryThisSumScore(ReportEntity reportEntity){
		return reportMapper.queryThisSumScore(reportEntity);
	}

	public String queryExpertSumScore(ReportEntity reportEntity){
		return reportMapper.queryExpertSumScore(reportEntity);
	}
	public void updateExpertBidStatus(ReportEntity reportEntity){
		reportMapper.updateExpertBidStatus(reportEntity);
	}

	public void updateExpertStatus(String expertId){
		reportMapper.updateExpertStatus(expertId);
	}
}
