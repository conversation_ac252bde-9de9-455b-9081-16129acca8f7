package com.hzw.sunflower.controller.response;

import com.hzw.sunflower.entity.AppToRead;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * <p>
 * app待阅
 * </p>
 *
 * <AUTHOR> Gen
 * @since 2022-12-15
 */
@Data
@ApiModel(value = "AppToRead对象", description = "app待阅表")
public class AppToReadVo extends AppToRead {

    @ApiModelProperty(value = "退回用户名")
    private String userName;

    @ApiModelProperty(value = "项目名称")
    private String purchaseName;

    @ApiModelProperty(value = "项目编号")
    private String purchaseNumber;

    @ApiModelProperty(value = "包号")
    private String packageNumbers;

    @ApiModelProperty(value = "是否划分标段包")
    private Integer packageSegmentStatus;

    @ApiModelProperty(value = "类型 1.待阅 2.通知")
    private Integer noticeType;

    @ApiModelProperty(value = "通知表主键")
    private Long noticeId;

    @ApiModelProperty(value = "数据修改内容")
    private String dataUpdateContent;

    @ApiModelProperty(value = "数据修改内容json")
    private String dataUpdateJson;

    @ApiModelProperty(value = "数据修改类型")
    private Integer dataUpdateType;
}
