package com.hzw.sunflower.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzw.sunflower.constant.constantenum.BidPurchaseModeConfigEnum;
import com.hzw.sunflower.constant.constantenum.PurchaseModeSysEnum;
import com.hzw.sunflower.constant.constantenum.PurchaseTypeSysEnum;
import com.hzw.sunflower.controller.request.PurchaseTypeCheckReq;
import com.hzw.sunflower.dao.ColumnPurchaseConfigMapper;
import com.hzw.sunflower.entity.ColumnCheckConfig;
import com.hzw.sunflower.entity.ColumnPurchaseConfig;
import com.hzw.sunflower.service.ColumnCheckConfigService;
import com.hzw.sunflower.service.ColumnPurchaseConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ColumnPurchaseConfigServiceImpl extends ServiceImpl<ColumnPurchaseConfigMapper, ColumnPurchaseConfig> implements ColumnPurchaseConfigService {

    @Autowired
    private ColumnCheckConfigService columnCheckConfigService;

    /**
     * 根据采购方式获取校验条件
     * @param req
     * @return
     */
    @Override
    public ColumnCheckConfig getPurchaseTypeCheck(PurchaseTypeCheckReq req) {
        ColumnCheckConfig columnCheckConfig = new ColumnCheckConfig();
        Integer purchaseTypeSys = 0;
        Integer purchaseModeSys = 0;
        Integer purchaseStatusSys = 0;
        //一级
        if (BidPurchaseModeConfigEnum.REQUIRED_ACCORDING.getType().equals(req.getPurchaseType())) {
            purchaseTypeSys = PurchaseTypeSysEnum.ACCORD_LAW_RECRUITED.getType();
        } else if (BidPurchaseModeConfigEnum.NOT_REQUIRED_ACCORDING.getType().equals(req.getPurchaseType())) {
            purchaseTypeSys = PurchaseTypeSysEnum.NO_ACCORD_LAW_RECRUITED.getType();
        } else if (BidPurchaseModeConfigEnum.GOVERNMENT_PROCUREMENT.getType().equals(req.getPurchaseType())) {
            purchaseTypeSys = PurchaseTypeSysEnum.GOVERNMENT_PROCUREMENT.getType();
        } else if (BidPurchaseModeConfigEnum.INTERNATIONAL_BIDDING.getType().equals(req.getPurchaseType())) {
            purchaseTypeSys = PurchaseTypeSysEnum.INTERNATIONAL_TENDERS.getType();
        }
        //二级
        if (BidPurchaseModeConfigEnum.REQUIRED_ACCORDING_924091.getType().equals(req.getPurchaseMode()) || BidPurchaseModeConfigEnum.NOT_REQUIRED_ACCORDING_924093.getType().equals(req.getPurchaseMode())
                || BidPurchaseModeConfigEnum.GOVERNMENT_PROCUREMENT_924101.getType().equals(req.getPurchaseMode()) || BidPurchaseModeConfigEnum.INTERNATIONAL_BIDDING_924108.getType().equals(req.getPurchaseMode())) {
            purchaseModeSys = PurchaseModeSysEnum.TENDER.getType();
        } else if (BidPurchaseModeConfigEnum.NOT_REQUIRED_ACCORDING_924094.getType().equals(req.getPurchaseMode()) || BidPurchaseModeConfigEnum.GOVERNMENT_PROCUREMENT_924102.getType().equals(req.getPurchaseMode())) {
            purchaseModeSys = PurchaseModeSysEnum.COMPETITIVE_CONSULTATIONS.getType();
        } else if (BidPurchaseModeConfigEnum.NOT_REQUIRED_ACCORDING_924095.getType().equals(req.getPurchaseMode()) || BidPurchaseModeConfigEnum.GOVERNMENT_PROCUREMENT_924103.getType().equals(req.getPurchaseMode())) {
            purchaseModeSys = PurchaseModeSysEnum.GOVERNMENT_PROCUREMENT.getType();
        } else if (BidPurchaseModeConfigEnum.NOT_REQUIRED_ACCORDING_924096.getType().equals(req.getPurchaseMode()) || BidPurchaseModeConfigEnum.GOVERNMENT_PROCUREMENT_924104.getType().equals(req.getPurchaseMode())) {
            purchaseModeSys = PurchaseModeSysEnum.GOVERNMENT_NEGOTIATION.getType();
        } else if (BidPurchaseModeConfigEnum.NOT_REQUIRED_ACCORDING_924097.getType().equals(req.getPurchaseMode())) {
            purchaseModeSys = PurchaseModeSysEnum.CONSULT.getType();
        } else if (BidPurchaseModeConfigEnum.NOT_REQUIRED_ACCORDING_924098.getType().equals(req.getPurchaseMode()) || BidPurchaseModeConfigEnum.GOVERNMENT_PROCUREMENT_924105.getType().equals(req.getPurchaseMode())) {
            purchaseModeSys = PurchaseModeSysEnum.SINGLE_SOURCE.getType();
        } else if (BidPurchaseModeConfigEnum.NOT_REQUIRED_ACCORDING_924099.getType().equals(req.getPurchaseMode()) || BidPurchaseModeConfigEnum.GOVERNMENT_PROCUREMENT_924106.getType().equals(req.getPurchaseMode())) {
            purchaseModeSys = PurchaseModeSysEnum.OTHER.getType();
        }
        //三级一致不转换
        purchaseStatusSys = req.getPurchaseStatus();
        LambdaQueryWrapper<ColumnPurchaseConfig> columnPurchaseConfigLambdaQueryWrapper = new LambdaQueryWrapper<>();
        columnPurchaseConfigLambdaQueryWrapper.eq(ColumnPurchaseConfig::getPurchaseType,purchaseTypeSys);
        columnPurchaseConfigLambdaQueryWrapper.eq(ColumnPurchaseConfig::getPurchaseMode,purchaseModeSys);
        columnPurchaseConfigLambdaQueryWrapper.eq(ColumnPurchaseConfig::getPurchaseStatus,purchaseStatusSys);
        columnPurchaseConfigLambdaQueryWrapper.last("limit 1");
        ColumnPurchaseConfig purchaseConfig = this.getOne(columnPurchaseConfigLambdaQueryWrapper);
        if (purchaseConfig != null) {
            LambdaQueryWrapper<ColumnCheckConfig> columnCheckConfigLambdaQueryWrapper = new LambdaQueryWrapper<>();
            columnCheckConfigLambdaQueryWrapper.eq(ColumnCheckConfig::getPurchaseConfigId,purchaseConfig.getId());
            columnCheckConfigLambdaQueryWrapper.last("limit 1");
            columnCheckConfig = columnCheckConfigService.getOne(columnCheckConfigLambdaQueryWrapper);
        }
        return columnCheckConfig;
    }

}
