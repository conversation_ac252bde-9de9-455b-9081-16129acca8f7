<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper 
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hzw.ssm.expert.dao.ExpertScoreMapper">

	<!--向专家计分表新增数据  -->
	<insert id="addExpertScoreRecords" parameterType="ExpertScoreEntity">
		insert into T_EXPERT_SCORE (
			id,
			user_id,
			user_name,
			pause_startTime,
			pause_endTime,
			pause_number,
			buckle_score,
			modify_time
		)
		values(
			#{id},
			#{user_id},
			#{user_name,jdbcType=VARCHAR},
			#{pause_startTime,jdbcType=VARCHAR},
			#{pause_endTime,jdbcType=VARCHAR},
			#{pause_number,jdbcType=NUMERIC},
			#{buckle_score,jdbcType=NUMERIC},
			#{modify_time,jdbcType=VARCHAR}
		)
	</insert>
	
	<!-- 定时更新专家信息 -->
	<update id="updateExpertScoreRecords">
		update T_EXPERT_SCORE set modify_time=#{modify_time}
		<if test="null != pause_startTime and '' != pause_startTime">
			,pause_startTime = #{pause_startTime}
		</if>
		<if test="null != pause_endTime and '' != pause_endTime">
			,pause_endTime = #{pause_endTime}
		</if>
		<if test="null != pause_number">
			,pause_number = #{pause_number}
		</if>
		where user_id=#{user_id} and time=(select max(time) from T_EXPERT_SCORE)
	</update>
	
	<select id="queryExpertAppear" parameterType="String" resultType="ExpertScoreEntity">
		select * from T_EXPERT_SCORE T
    	where T.user_id=#{user_id} 
	</select>
	
</mapper>
