<template>
  <div :class="src ? 'hasFile' : ''">
    <div class="importFile" :class="src ? '' : 'nofile'">
      <p>请导入下载的招标文件</p>
      <div
        id="drop_zone"
        @dragover.prevent
        @drop.prevent="handleDrop"
        @click="selectFile"
        class="curp"
        style="border: 2px dashed #cccccc; padding: 20px; text-align: center"
      >
        <el-icon class="uploadFont"><UploadFilled /></el-icon>
        <p class="gray">点击或将文件拖拽到这里上传</p>
        <p class="gray">支持格式：压缩包</p>
      </div>
      <div class="mt20">
        <el-icon
          v-if="fileName"
          color="#67C23A"
          style="vertical-align: text-top"
          ><Document
        /></el-icon>
        <span v-if="fileName" class="A8ABB2 ml10">{{ fileName }}</span>
        <span v-if="fileSize" class="A8ABB2 ml20"
          >{{ (fileSize / 1024).toFixed(1) }}KB</span
        >
      </div>
    </div>
    <div class="office" id="docxContent" v-if="src">
        <vue-office-docx
            :src="src"
            style="height: 80vh;"
        />
    </div>

    <div class="footBtn">
      <el-button @click="nextStep" type="primary">下一步</el-button>
    </div>
  </div>
</template>

<script setup>
import { ref } from "vue";
//引入VueOfficeDocx组件
import VueOfficeDocx from "@vue-office/docx";
//引入相关样式
import "@vue-office/docx/lib/index.css";
import { ElMessage } from "element-plus";

const file = ref(null);
const html = ref("");
const fileName = ref("");
const fileSize = ref(0);
const src = ref("");

const selectFile = async () => {
  const filePaths = await window.electronAPI.openFileDialog();
  if (filePaths && filePaths.length > 0) {
    file.value = await window.electronAPI.readFile(filePaths[0]);
    fileSize.value = file.value.byteLength; // 文件大小
    const fileType = handleFileType(filePaths[0]); // 文件类型
    if (["zip", "rar", "7z"].includes(fileType)) {
      handleFile(filePaths[0]);
    } else {
      html.value = await window.electronAPI.uploadFile(filePaths[0]);
      emits("updateHtml", html.value);
    }
  }
};

const handleDrop = async (event) => {
  const files = event.dataTransfer.files;
  if (files.length > 0) {
    file.value = files[0];
    let fileReader = new FileReader();
    fileReader.readAsArrayBuffer(file.value);
    fileReader.onload = () => {
      src.value = fileReader.result;
    };
    const fileType = handleFileType(files[0].path);
    if (["zip", "rar", "7z"].includes(fileType)) {
      //  ElMessage.warning('请上传word文件');
      handleFile(files[0].path);
    } else {
      html.value = await window.electronAPI.uploadFile(files[0].path);
      fileSize.value = files[0].size; // 文件大小
      emits("updateHtml", html.value);
    }
  }
};

const handleFile = async (filePath) => {
  try {
    const extractedFiles = await window.electronAPI.unzipFile(filePath);
    const docFilePath = extractedFiles.find((item) =>
      item.includes("投标文件")
    );
    if (docFilePath) {
      file.value = await window.electronAPI.readFile(docFilePath);
      const blob = new Blob([file.value], { type: "application/octet-stream" });
      let fileReader = new FileReader();
      fileReader.readAsArrayBuffer(blob);
      fileReader.onload = () => {
        src.value = fileReader.result;
      };
      html.value = await window.electronAPI.uploadFile(docFilePath);
      emits("updateHtml", html.value);
    } else {
      ElMessage.warning("文件解析失败，请重新上传招标文件");
      file.value = null;
      src.value = "";
    }
  } catch (error) {
    console.log(error);
    ElMessage.warning("文件解析失败，请重新上传招标文件");
    file.value = null;
    src.value = "";
  }
};
const docxContent = document.getElementById("docxContent");
const displayDocxContent = (doc) => {
  docxContent.innerHTML = "";
  doc.sections.forEach((section) => {
    section.children.forEach((child) => {
      if (child.type === "paragraph") {
        const paragraph = document.createElement("p");
        child.children.forEach((run) => {
          if (run.type === "text") {
            const textNode = document.createTextNode(run.text);
            paragraph.appendChild(textNode);
          }
        });
        docxContent.appendChild(paragraph);
      }
    });
  });
};
/**
 * @description: 根据文件路径返回最后的文件类型后缀
 * @param {*} filePath 文件路径
 */
const handleFileType = (filePath) => {
  if (!filePath) return "";
  const paths = filePath.split("/");
  fileName.value = paths[paths.length - 1]; // 文件名称
  const fileNames = paths[paths.length - 1].split(".");
  return fileNames[fileNames.length - 1];
};

/**
 * @description: 查找htmlJson中的某个字段
 * @param {*} htmlJson 被查找的数据
 * @param {*} field 要查找的字段
 */
const findFieldInHtmlJson = (htmlJson, field) => {
  let result = null;
  let originHtmlJson = htmlJson.children[1].children.filter(
    (item) => item.tagName === "H1"
  );

  if (originHtmlJson.length === 0) return null;
  for (let i = 0; i < originHtmlJson.length; i++) {
    if (originHtmlJson[i].children.includes(field)) {
      result = originHtmlJson[i];
      break;
    }
    if (originHtmlJson[i].children) {
      result = findFiledInChild(originHtmlJson[i].children, field);
      if (result) {
        break;
      }
    }
  }
  function findFiledInChild(children, field) {
    let result = null;
    for (let i = 0; i < children.length; i++) {
      if (children[i].includes(field)) {
        result = children[i];
        break;
      }
      if (children[i].children) {
        result = findFieldInHtmlJson(children[i].children, field);
        if (result) {
          break;
        }
      }
    }
    return result;
  }

  return result;
};

const emits = defineEmits(["nextStep", "updateHtml"]);
const nextStep = () => {
  if (!file.value) {
    ElMessage.warning("请导入下载的招标文件");
    return;
  }
  emits("nextStep", 2);
};
</script>

<style>
.importFile {
  width: 40%;
}
.list-enter-active,
.list-leave-active {
  transition: all 2.5s;
}
.nofile {
  margin: 20px auto;
  width: 50%;
}
.page-break {
  page-break-before: always;
}
.hasFile {
  display: flex;
  padding: 20px;
}
.uploadFont {
  font-size: 50px;
  color: #c0c4cc;
}
.gray {
  color: #c0c4cc;
}
.A8ABB2 {
  color: #67c23a;
}
.office {
  width: 67%;
  margin-left: 5%;
  background: rgba(0, 0, 0, 0.5);
  border: 1px solid #ccc;
  padding: 10px;
}
.footBtn {
  background: #f5f7fa;
  width: calc(100% + 40px);
  left: -40px;
  padding: 10px;
  text-align: center;
  position: fixed;
  bottom: 0;
  z-index: 1;
}
</style>