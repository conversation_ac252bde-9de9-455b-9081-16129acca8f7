package com.hzw.ssm.applets.service;

import com.hzw.ssm.applets.dao.BidEvaluationMapper;
import com.hzw.ssm.applets.entity.BidEvaluation;
import com.hzw.ssm.applets.exception.ExceptionEnum;
import com.hzw.ssm.applets.utils.RandomCode;
import com.hzw.ssm.applets.utils.ResultJson;
import com.hzw.ssm.expert.dao.ExpertInfoMapper;
import com.hzw.ssm.expert.entity.ExpertInfoEntity;
import com.hzw.ssm.expert.entity.ExpertUpdateRecordEntity;
import com.hzw.ssm.expert.entity.SpecialtyInfoEntity;
import com.hzw.ssm.expert.service.ExpertInfoService;
import com.hzw.ssm.fw.base.BaseService;
import com.hzw.ssm.fw.util.CommUtil;
import com.hzw.ssm.sys.system.dao.SpecialtyInfoMapper;
import com.hzw.ssm.sys.user.dao.UserMapper;
import com.hzw.ssm.sys.user.entity.UserEntity;
import com.hzw.ssm.util.string.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <AUTHOR>
 * @Date 2021-02-03 10:14
 * @Version 1.0
 */
@Service
public class BidEvaluationService extends BaseService {

    private static Logger logger = Logger.getLogger(BidEvaluationService.class);

    @Autowired
    private BidEvaluationMapper bidEvaluationMapper;
    @Autowired
    private SpecialtyInfoMapper specialtyInfoMapper;
    @Autowired
    private ExpertInfoMapper expertInfoMapper;
    @Autowired
    private ExpertInfoService expertInfoService;
    @Autowired
    private UserMapper userMapper;

    public ResultJson insertBidEvaluation(BidEvaluation bidEvaluation) {
        try {
            // 评标专业最少上传1条，最多上传5条
//            int num = bidEvaluationMapper.queryCountByExpertId(bidEvaluation);
//            if (num >= 5) {
//                return new ResultJson(ExceptionEnum.EVALUATION_NUM_OVER.getCode(), ExceptionEnum.EVALUATION_NUM_OVER.getMessage());
//            }
//如果新增，当前用户是正常3-8状态，就要修改为10
            List<Map<String, Object>> list = new ArrayList<>();
            Map<String, Object> map = new HashMap<>();

            //同一评标专业仅允许一条记录
            BidEvaluation evaluation = new BidEvaluation();
            evaluation.setUser_id(bidEvaluation.getUser_id());
            evaluation.setMajor(bidEvaluation.getMajor());
            evaluation = bidEvaluationMapper.queryBidEvaluation(evaluation);
            if (null != evaluation) {
                return new ResultJson(ExceptionEnum.EVALUATION_EXIST.getCode(), ExceptionEnum.EVALUATION_EXIST.getMessage());
            }

            List<BidEvaluation> evaluations = bidEvaluationMapper.getBidEvaluations(bidEvaluation);
            if (null != evaluations && evaluations.size() > 0 && evaluations.size() == 5) {
                for (BidEvaluation alution : evaluations) {
                    String major = alution.getMajor();
                    if (StringUtils.isEmpty(major)) {
                        bidEvaluation.setId(alution.getId());
                        int updates = bidEvaluationMapper.updateBidEvaluation(bidEvaluation);
                        if (updates > 0) {

                            ExpertInfoEntity infoEntity = new ExpertInfoEntity();
                            infoEntity.setUser_id(bidEvaluation.getUser_id());
                            ExpertInfoEntity expertInfoByUserIdOrId = expertInfoMapper.getExpertInfoByUserIdOrId(infoEntity);
                            long status = expertInfoByUserIdOrId.getStatus();
                            if (3 == status || 8 == status) {
                                expertInfoByUserIdOrId.setStatus(10L);
                                expertInfoMapper.updateExpertInfoById(expertInfoByUserIdOrId);
                            }
                            //新增记录
                            if (3 == status || 8 == status || 4 == status || 10 == status) {
                                try {
                                    infoSaveRecord(bidEvaluation);
                                } catch (Exception e) {
                                    e.printStackTrace();
                                }
                            }
                            map.put("id", alution.getId());
                            list.add(map);
                            return new ResultJson(list);
                        }
                    }
                }
            } else {
                System.out.println("else");
                System.out.println("start");
                String id = RandomCode.getId();
                bidEvaluation.setId(id);
                bidEvaluation.setDelete_flag(0);
                int updates = bidEvaluationMapper.insertBidEvaluation(bidEvaluation);
                System.out.println("updates:"+updates);
                if (updates > 0) {
                    System.out.println("evaluations:"+evaluations);
                    System.out.println("evaluations.size():"+evaluations.size());
                    if (null != evaluations && evaluations.size() >= 1) {
                        ExpertInfoEntity infoEntity = new ExpertInfoEntity();
                        infoEntity.setUser_id(bidEvaluation.getUser_id());
                        ExpertInfoEntity expertInfoByUserIdOrId = expertInfoMapper.getExpertInfoByUserIdOrId(infoEntity);
                        long status = expertInfoByUserIdOrId.getStatus();
                        System.out.println("status:"+status);
                        if (3 == status || 8 == status) {
                            expertInfoByUserIdOrId.setStatus(10L);
                            expertInfoMapper.updateExpertInfoById(expertInfoByUserIdOrId);
                            System.out.println("expertInfoByUserIdOrId:"+"10");
                        }
                        //新增记录
                        if (3 == status || 8 == status || 4 == status || 10 == status) {
                            try {

                                infoSaveRecord(bidEvaluation);
                                System.out.println("infoSaveRecord:"+bidEvaluation);
                                System.out.println("end");
                            } catch (Exception e) {
                                e.printStackTrace();
                                System.out.println("Exception:"+e.getMessage());
                            }
                        }
                    }
                    map.put("id", id);
                    list.add(map);
                    return new ResultJson(list);
                }
            }
        } catch (Exception e) {
            logger.error(e.getMessage());
        }
        return new ResultJson(ExceptionEnum.SERVER_IS_BUSY.getCode(), ExceptionEnum.SERVER_IS_BUSY.getMessage());
    }

    private void infoSaveRecord(BidEvaluation after) {
        UserEntity userEntity = userMapper.selectUserById(after.getUser_id());
        String role_id;
        if (userEntity != null) {
            role_id = StringUtils.toStringNull(userEntity.getRole_id());
        } else {
            role_id = "无";
        }
        SpecialtyInfoEntity afterInnerInfo = expertInfoMapper.selectSpecialtyInfoBySpeId(StringUtils.toStringNull(after.getMajor()));
        //新的专业信息
        String af = "评标专业:" + afterInnerInfo.getSpe_name() + "<br/>从事专业时间:" + after.getGetTime();
        addExpertUpdateRecord(after.getUser_id(), "评标专业", null, af, role_id, after.getUser_id(), "评标专业信息", 0);
    }

    private void infoRecord(BidEvaluation before, BidEvaluation after) {
        if (before != null && after != null) {
            UserEntity userEntity = userMapper.selectUserById(before.getUser_id());
            String role_id;
            if (userEntity != null) {
                role_id = StringUtils.toStringNull(userEntity.getRole_id());
            } else {
                role_id = "无";
            }


            SpecialtyInfoEntity beforeOuterInfo = expertInfoMapper.selectSpecialtyInfoBySpeId(StringUtils.toStringNull(before.getMajor()));
            SpecialtyInfoEntity afterInnerInfo = expertInfoMapper.selectSpecialtyInfoBySpeId(StringUtils.toStringNull(after.getMajor()));
            String getTime = "";
            if (!StringUtils.isEmpty(before.getGetTime())) {
                getTime = before.getGetTime();
            }

            //新的专业信息
            String af = "评标专业:" + afterInnerInfo.getSpe_name() + "<br/>从事专业时间:" + after.getGetTime();
            //旧的专业信息
            String be = "评标专业:" + beforeOuterInfo.getSpe_name() + "<br/>从事专业时间:" + getTime;

            if (!afterInnerInfo.getSpe_name().equals(beforeOuterInfo.getSpe_name())) {
                addExpertUpdateRecord(before.getUser_id(), "评标专业", be, af, role_id, before.getUser_id(), "评标专业信息", 0);
            }

        }

    }

    /**
     * 新增专家信息修改记录
     *
     * @param user_id        用户编号
     * @param item           修改项
     * @param content_before 修改前内容
     * @param content_after  修改后内容
     * @param modify_role    修改角色
     * @param modify_user    修改人
     * @param modify_reason  修改原因
     * @param batch_number   修改批次编号
     */
    public void addExpertUpdateRecord(String user_id, String item, String content_before, String content_after, String modify_role, String modify_user, String modify_reason, int batch_number) {
        ExpertUpdateRecordEntity record = new ExpertUpdateRecordEntity();
        record.setId(CommUtil.getKey());
        record.setUser_id(user_id);
        record.setItem(item);
        record.setContent_before(content_before);
        record.setContent_after(content_after);
        record.setModify_role(modify_role);
        record.setModify_user(modify_user);
        record.setModify_reason(modify_reason);
        record.setBatch_number(batch_number);
        record.setModify_time(new Date());
        record.setDelete_flag(0);
        expertInfoMapper.addExpertUpdateRecord(record);
    }

    public ResultJson updateBidEvaluation(BidEvaluation bidEvaluation) {
        try {

            //同一评标专业仅允许一条记录
            BidEvaluation evaluation = new BidEvaluation();
            String user_id = bidEvaluation.getUser_id();
            evaluation.setUser_id(user_id);
            evaluation.setMajor(bidEvaluation.getMajor());
            evaluation = bidEvaluationMapper.queryBidEvaluation(evaluation);
            if (null != evaluation && !evaluation.getId().equals(bidEvaluation.getId())) {
                return new ResultJson(ExceptionEnum.EVALUATION_EXIST.getCode(), ExceptionEnum.EVALUATION_EXIST.getMessage());
            }

            Map<String, Object> map = new HashMap<>();


            BidEvaluation keyId = new BidEvaluation();
            keyId.setId(bidEvaluation.getId());
            BidEvaluation ckey = bidEvaluationMapper.getBidEvaluation(keyId);

            List<Map<String, Object>> list = new ArrayList<>();
            int updates = bidEvaluationMapper.updateBidEvaluation(bidEvaluation);
            if (updates > 0) {

                ExpertInfoEntity infoEntity = new ExpertInfoEntity();
                infoEntity.setUser_id(bidEvaluation.getUser_id());
                ExpertInfoEntity expertInfoByUserIdOrId = expertInfoMapper.getExpertInfoByUserIdOrId(infoEntity);
                long status = expertInfoByUserIdOrId.getStatus();
                if (3 == status || 8 == status) {
                    expertInfoByUserIdOrId.setStatus(10L);
                    expertInfoMapper.updateExpertInfoById(expertInfoByUserIdOrId);
                }
                // 修改记录
                if (3 == status || 8 == status || 4 == status || 10 == status) {
                    try {
                        infoRecord(ckey, bidEvaluation);
                    } catch (Exception e) {
                        System.out.println("记录出错!");
                    }
                }
                map.put("id", bidEvaluation.getId());
                list.add(map);
                return new ResultJson(list);
            }
        } catch (Exception e) {
            logger.error(e.getMessage());
        }
        return new ResultJson(ExceptionEnum.SERVER_IS_BUSY.getCode(), ExceptionEnum.SERVER_IS_BUSY.getMessage());
    }

    public ResultJson deleteBidEvaluation(BidEvaluation bidEvaluation) {
        try {
            List<Map<String, Object>> list = new ArrayList<>();
            Map<String, Object> map = new HashMap<>();
            BidEvaluation info = bidEvaluationMapper.getBidEvaluation(bidEvaluation);

            int updates = bidEvaluationMapper.deleteBidEvaluation(bidEvaluation);
            if (updates > 0) {
                // 如果用户已审核通过，修改需要重新提交审核
                ExpertInfoEntity infoEntity = new ExpertInfoEntity();
                infoEntity.setUser_id(info.getUser_id());
                ExpertInfoEntity entity = expertInfoMapper.getExpertInfoByUserIdOrId(infoEntity);
                Long status = entity.getStatus();
                if (3 == status || 8 == status) {
                    entity.setStatus(10L);
                    expertInfoMapper.updateExpertInfoById(entity);
                }
                // 删除记录
                if (3 == status || 8 == status || 4 == status || 10 == status) {
                    try {
                        deleteBidEvaluationRecord(info);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
                map.put("id", bidEvaluation.getId());
                list.add(map);
                return new ResultJson(list);
            }
        } catch (Exception e) {
            logger.error(e.getMessage());
        }
        return new ResultJson(ExceptionEnum.SERVER_IS_BUSY.getCode(), ExceptionEnum.SERVER_IS_BUSY.getMessage());
    }

    private void deleteBidEvaluationRecord(BidEvaluation info) {
        UserEntity userEntity = userMapper.selectUserById(info.getUser_id());
        SpecialtyInfoEntity afterInnerInfo = expertInfoMapper.selectSpecialtyInfoBySpeId(StringUtils.toStringNull(info.getMajor()));
        String content_after = "删除了【" + afterInnerInfo.getSpe_name() + "】的评标专业信息";
        addExpertUpdateRecord(info.getUser_id(), "评标专业", null, content_after, userEntity.getRole_id(), info.getUser_id(), "评标专业", 0);
    }

    public ResultJson getBidEvaluation(BidEvaluation bidEvaluation) {
        List<SpecialtyInfoEntity> entitys = specialtyInfoMapper.queryAll();
        Map<String, String> map = new HashMap<>(16);
        for (SpecialtyInfoEntity infoEntity : entitys) {
            map.put(infoEntity.getSpe_id(), infoEntity.getSpe_name());
        }
        List<BidEvaluation> list = new ArrayList<>();
        BidEvaluation evaluation = bidEvaluationMapper.getBidEvaluation(bidEvaluation);
        String major = evaluation.getMajor();
        if (map.containsKey(major)) {
            String majorNames = map.get(evaluation.getMajor());
            evaluation.setMajorName(majorNames);
        }
        list.add(evaluation);
        return new ResultJson(list);
    }

    public ResultJson getBidEvaluations(BidEvaluation bidEvaluation) {

        List<BidEvaluation> bidEvaluations = new ArrayList<>();

        List<SpecialtyInfoEntity> entitys = specialtyInfoMapper.queryAll();
        Map<String, String> map = new HashMap<>(16);
        for (SpecialtyInfoEntity infoEntity : entitys) {
            map.put(infoEntity.getSpe_id(), infoEntity.getSpe_name());
        }
        List<BidEvaluation> evaluations = bidEvaluationMapper.getBidEvaluations(bidEvaluation);
        for (BidEvaluation evaluation : evaluations) {
            String major = evaluation.getMajor();
            if (!StringUtils.isEmpty(major)) {
                if (map.containsKey(major)) {
                    String majorNames = map.get(evaluation.getMajor());
                    evaluation.setMajorName(majorNames);
                }
                bidEvaluations.add(evaluation);
            }
        }
        return new ResultJson(bidEvaluations);
    }


    public ResultJson tree(String speParent) {

        List<Map<String, Object>> list = new ArrayList<>();
        SpecialtyInfoEntity entity = new SpecialtyInfoEntity();
        entity.setSpe_parent(speParent);
        List<SpecialtyInfoEntity> entitys = specialtyInfoMapper.querySubSpecialtyBySpeParentId(entity);
        for (SpecialtyInfoEntity info : entitys) {
            Map<String, Object> map = new HashMap<>();
            String spe_id = info.getSpe_id();
            String spe_name = info.getSpe_name();
            map.put("id", spe_id);
            map.put("name", spe_name);
            String speId = info.getSpe_id();
            entity.setSpe_parent(speId);
            int infoEntities = specialtyInfoMapper.queryCountBySpecialtyId(entity);
            map.put("isChildren", 0);
            if (infoEntities > 0) {
                map.put("isChildren", 1);
            }
            list.add(map);
        }
        return new ResultJson(list);
    }


}
