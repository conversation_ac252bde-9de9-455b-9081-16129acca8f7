package com.hzw.sunflower.constant.constantenum;

/**
 * 评价状态枚举
 *
 * <AUTHOR>
 */
public enum AppraiseStatusEnum {

    TO_BE_APPRAISE(1, "待评价"),
    TO_BE_CONFIRM(2, "待确认"),
    HAS_CONFIRM(3, "已确认"),
    HAS_RETURN(4, "已退回");

    private Integer type;
    private String desc;

    AppraiseStatusEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
