package com.hzw.sunflower.controller.request;

import com.hzw.sunflower.common.BaseCondition;
import com.hzw.sunflower.controller.request.AppingTaskREQ;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;


@Data
public class AppTaskCondition extends BaseCondition {

    @ApiModelProperty(value = "关键字")
    private String keyWords;

    @ApiModelProperty(value = "类型")
    private List<String> type;

    @ApiModelProperty(value = "处室")
    private Long departId;


}
