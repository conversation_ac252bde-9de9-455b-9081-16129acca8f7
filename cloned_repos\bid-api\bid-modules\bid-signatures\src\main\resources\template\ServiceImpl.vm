package ${package_name}.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import ${package_name}.dao.${modelname}Mapper;
import ${package_name}.dto.${modelname}DTO;
import ${package_name}.entity.${modelname};
import ${package_name}.entity.condition.${modelname}Condition;
import com.hzw.sunflower.exception.ExceptionEnum;
import com.hzw.sunflower.exception.SunFlowerException;
import ${package_name}.service.${modelname}Service;
import org.springframework.stereotype.Service;

import java.util.List;


/**
* ${modelname}Service接口实现
*
* <AUTHOR>
* @version 1.0.0 2021-04-07
*/
@Service
public class ${modelname}ServiceImpl extends ServiceImpl<${modelname}Mapper, ${modelname}> implements ${modelname}Service {

    /**
    * 根据条件分页查询列表
    *
    * @param condition 查询条件
    * @return 分页数据
    */
    @Override
    public IPage<${modelname}> findInfoByCondition(${modelname}Condition condition) {
        IPage<${modelname}> page = condition.buildPage();
        QueryWrapper<${modelname}> queryWrapper = condition.buildQueryWrapper(${modelname}.class);
        return this.page(page, queryWrapper);
    }

    /**
    * 根据主键ID查询
    *
    * @param id 主键ID
    * @return
    */
    @Override
    public ${modelname} getInfoById(Long id) {
        return this.getById(id);
    }

    /**
    * 新增
    *
    * @param ${lowmodel}
    * @return 是否成功
    */
    @Override
    public Boolean addInfo(${modelname}DTO ${lowmodel}) {
        return this.save(${lowmodel});
    }

    /**
    * 修改
    *
    * @param ${lowmodel}
    * @return 是否成功
    */
    @Override
    public Boolean updateInfo(${modelname}DTO ${lowmodel}) {
        return this.updateById(${lowmodel});
    }

    /**
    * 根据主键ID删除
    *
    * @param id 主键ID
    * @return 是否成功
    */
    @Override
    public Boolean deleteById(Long id) {
        return this.removeById(id);
    }

    /**
    * 根据主键ID列表批量删除
    *
    * @param idList 主键ID列表
    * @return 是否成功
    */
    @Override
    public Boolean deleteByIds(List<Long> idList) {
        return this.removeByIds(idList);
    }

}