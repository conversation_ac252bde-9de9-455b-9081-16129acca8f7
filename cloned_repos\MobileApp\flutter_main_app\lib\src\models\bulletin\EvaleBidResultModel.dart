


class EvaleBidResultModel {
  String candidateName;  // 中标候选人名称
  String preQualificationReviewResult; // 资格预审结果
  String preQualificationReviewComment; // 资格预审备注
  String qualificiationReviewFailedReason; // 资格后审不合格原因
  String abandonReason;  // 废标原因


  String correctionReason; // 修正原因
  String correctionBasis; //  修正依据
  double originalPrice;  // 修正前报价
  double correctionPrice; // 修正后报价

  List<double> technicalEvalePoint; //技术打分情况
  double bidPrice;      // 投标报价
  double bidPricePoint;   // 报价分

  EvaleBidResultModel(
    {
      this.candidateName,
      this.preQualificationReviewResult,
      this.preQualificationReviewComment,
      this.qualificiationReviewFailedReason,
      this.abandonReason,

      this.correctionReason,
      this.correctionBasis,
      this.originalPrice,
      this.correctionPrice,
      this.technicalEvalePoint,
      this.bidPrice,
      this.bidPricePoint
    }
  );
}

