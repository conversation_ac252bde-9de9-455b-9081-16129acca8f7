package com.hzw.ssm.sys.sms.mwutil;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;
/**
 * 此类为测试类，用来测试每一个方法如需了解api逻辑建议可以读main类
 * 1.目前API为了防止http连接SOCKET占满的情况，将所有http连接已经改为长连接
 * 2.为了保证长链接正常使用需要将sp账号设置为长连接方式（具体设置联系梦网接口人）
 * */
public class STest {
	// 日期格式定义
	//private static SimpleDateFormat sdf = new SimpleDateFormat("MMddHHmmss");
	// 用户账号
	public static String userid = "";
	// 用户密码
	public static String pwd = "";
	// 加密标识
	public static boolean isEncFlag = false;

	/**
	 * @description 初始化输入请求地址，ip端口，账号密码及连接池参数并且提供选择调用接口
	 * 需要输入参数:
	 * 1.请求地址 如(/sms/v2/std/)
	 * 2.ip 如(***********)
	 * 3.端口 如(7901)
	 * 4.用户账号如 (UL001)
	 * 5.用户密码 如 (123456)
	 * 6.连接池获取连接超时时间(如设置2秒就输入2000)不输入默认2秒
	 * 7.连接超时时间(如设置2秒就输入2000)不输入默认5秒
	 * 8.响应超时时间(如设置2秒就输入2000)不输入默认60秒
	 * 9.连接池最大连接数如(10)不输入默认10
	 * 10.每个路由最大连接数如(2)不输入默认2
	 * 11.清理空余连接时间如(2秒输入2)不输入默认15
	 * 12.选择发送接口(1-10任意一个数)
	 * */
	public static void main(String args[]) throws IOException {
		System.out.println("1.目前API为了防止http连接SOCKET占满的情况，将所有http连接已经改为长连接");
		System.out.println(" 2.为了保证长链接正常使用需要将sp账号设置为长连接方式（具体设置联系梦网接口人）");
		// 配置http连接池
		setHttp();
		System.out.print("请输入路径(如/sms/v2/std/)：");
		// 从控制台获取输入请求路径
		String requestPath = getInputParam();
		// 判断请求路径不为空，为空重新输入，不为空返回
		requestPath = forStr(requestPath);
		// 设置请求路径
		ConfigManager.setRequestPath(requestPath);
		// 定义ip+端口1
		String ipAddress1 = null;
		// 定义ip+端口2
		String ipAddress2 = null;
		// 定义ip+端口3
		String ipAddress3 = null;
		// 定义ip+端口1
		String ipAddress4 = null;
		// 获取ipAddress1
		ipAddress1 = addIpAndPort();
		// 判断是否添加ipAddress2，ipAddress3，ipAddress4
		for (int i = 2; i < 5; i++) {
			System.out.println("是否继续添加ip+端口Y/N");
			String isAdd = getInputParam();
			// 判断是否是输入的Y或者N
			isAdd = isAppointStr("Y", "N", isAdd);
			// 继续输入为输入的ip值分别赋值给ipAddress2，ipAddress3，ipAddress4
			if ("Y".equalsIgnoreCase(isAdd)) {
				// 继续输入
				switch (i) {
				case 2:
					//为备用ipAddress2赋值
					ipAddress2 = addIpAndPort();
					break;
				case 3:
					//为备用ipAddress3赋值
					ipAddress3 = addIpAndPort();
					break;
				case 4:
					//为备用ipAddress4赋值
					ipAddress4 = addIpAndPort();
				default:
					break;
				}
			} else {
				// 不添加
				break;
			}
		}
		// 设置ip信息
		int errorcode = ConfigManager.setIpInfo(ipAddress1, ipAddress2,ipAddress3, ipAddress4);
		// 判断ip信息是否设置正确
		if (errorcode != 0) {
			System.out.println("ip信息设置失败退出程序");
			System.exit(1);
		}
		// 用户账号
		System.out.println("请输入用户账号");
		// 获取输入的用户账号
		userid = getInputParam();
		// 判断userid不为空，若为空则重新输入
		userid = forStr(userid);
		System.out.println("请输入用户密码");
		// 获取输入的密码
		pwd = getInputParam();
		// 判断pwd不为空，若为空则重新输入
		pwd = forStr(pwd);
		System.out.println("请输入用户密码是否加密加密:Y不加密N");
		// 获取输入值
		String isEnc = getInputParam();
		// 判断是否是输入的Y或者N
		isEnc = isAppointStr("Y", "N", isEnc);
		if ("Y".equalsIgnoreCase(isEnc)) {
			// 加密将标识置为true
			isEncFlag = true;
		}
		// 实例化短信处理对象
		CHttpPost cHttpPost = new CHttpPost();
		// 以下为8个方法的调用例子
		while(true){
		System.out.println("请选择接口类型：");
		System.out.println("1:  单条发送");
		System.out.println("2:  相同内容群发");
		System.out.println("3:  模板发送接口");
		System.out.println("4:  个性化发送接口");
		System.out.println("5:  查询余额接口");
		System.out.println("6:  获取上行接口");
		System.out.println("7:  获取状态报告接口");
		System.out.println("8：     查询剩余金额或条数接口");
		System.out.println("9:  移除账号");
		System.out.println("10: 退出应用程序");
		// 获取用户输入
		String type = getInputParam();
		// 判断用户是否是输入的整数
		type = forInt(type);
		switch (Integer.valueOf(type)) {
		case 1:
			//单条发送方法
			singleSend(cHttpPost);
			break;
		case 2:
			//相同内容群发方法
			batchSend(cHttpPost);
			break;
		case 3:
			//模板发送方法
			templateSend(cHttpPost);
			break;
		case 4:
			//个性化群发方法
			multiSend(cHttpPost);
			break;
		case 5:
			//获取余额方法
			getBalance(cHttpPost);
			break;
		case 6:
			//获取上行方法
			getMo(cHttpPost);
			break;
		case 7:
			//获取状态报告方法
			getRpt(cHttpPost);
			break;
		case 8:
			// 查询剩余金额或条数方法
			getRemains(cHttpPost);
			break;
		case 9:
			//移除账号及ip信息
			ConfigManager.removeAllIpInfo();
			break;
		case 10:
			System.exit(1);
			break;
		default:
			System.exit(1);
		}
		}
	}
	/**
	 * @description 单条发送接口
	 * @param CHttpPost 短信发送对象
	 * @return void
	 * 注需要输入信息
	 * 1.手机号
	 * 2.短信内容
	 * 3.扩展子号(可选)
	 * 4.自定义流水号(可选)
	 * 5.扩展数据(可选)
	 * 6.业务类型(可选)
	 * 7.定时时间(可选)
	 * */
	public static void singleSend(CHttpPost post) {
		try {
			// 获取短信对象设置账号密码时间戳
			Message m = setUser(post);
			// 设置手机号
			m.setMobile(getPhone());
			// 设置短信内容
			m.setContent(getContent());
			// 获取扩展子号
			String strSubPort = getStrSubPort();
			// 当扩展子号不为空设置扩展子号
			if (validateStr(strSubPort)) {
				m.setExno(strSubPort);
			}
			// 获取自定义流水号
			String strUserMsgId = getStrUserMsgId();
			// 当自定义流水号不为空设置自定义流水号
			if (validateStr(strUserMsgId)) {
				m.setCustid(strUserMsgId);
			}
			// 获取扩展数据
			String exData = getExdata();
			// 当扩展数据不为空设置扩展数据
			if (validateStr(exData)) {
				m.setExdata(exData);
			}
			// 获取业务类型
			String svrType = getSvrType();
			// 业务类型不为空设置业务类型
			if (validateStr(svrType)) {
				m.setSvrtype(svrType);
			}
			// 获取定时时间
			String attTime = getAttime();
			// 当定时时间不为空设置定时时间
			if (validateStr(attTime)) {
				m.setAttime(attTime);
			}
			// 定义返回字符串
			StringBuffer msgId = new StringBuffer();
			// 调用方法进行发送返回结果在msgId中
			int result = post.singleSend(m, msgId);
			// 当result为0发送成功打印结果
			if (result == 0) {
				System.out.println("单条发送成功");
				System.out.println(msgId.toString());
			} else {
				// 发送失败打印错误码
				System.out.println(result);
			}
		} catch (Exception e) {
			System.out.println("程序异常单条发送失败");
			e.printStackTrace();
		}

	}
	/**
	 * @description 相同内容群发发送接口
	 * @param CHttpPost 短信发送对象
	 * @return void
	 * 注需要输入信息
	 * 1.手机号
	 * 2.短信内容
	 * 3.扩展子号(可选)
	 * 4.自定义流水号(可选)
	 * 5.扩展数据(可选)
	 * 6.业务类型(可选)
	 * 7.定时时间(可选)
	 * */
	public static void batchSend(CHttpPost post) {
		try {
			// 获取短信对象设置账号密码时间戳
			Message m = setUser(post);
			// 设置手机号
			m.setMobile(getManyPhone());
			// 设置短信内容
			m.setContent(getContent());
			// 获取扩展子号
			String strSubPort = getStrSubPort();
			// 当扩展子号不为空设置扩展子号
			if (validateStr(strSubPort)) {
				m.setExno(strSubPort);
			}
			// 获取自定义流水号
			String strUserMsgId = getStrUserMsgId();
			// 当自定义流水号不为空设置自定义流水号
			if (validateStr(strUserMsgId)) {
				m.setCustid(strUserMsgId);
			}
			// 获取扩展数据
			String exData = getExdata();
			// 当扩展数据不为空设置扩展数据
			if (validateStr(exData)) {
				m.setExdata(exData);
			}
			// 获取业务类型
			String svrType = getSvrType();
			// 业务类型不为空设置业务类型
			if (validateStr(svrType)) {
				m.setSvrtype(svrType);
			}
			// 获取定时时间
			String attTime = getAttime();
			// 当定时时间不为空设置定时时间
			if (validateStr(attTime)) {
				m.setAttime(attTime);
			}
			// 定义返回字符串
			StringBuffer msgId = new StringBuffer();
			// 调用方法进行发送返回结果在msgId中
			int result = post.batchSend(m, msgId);
			// 当result为0发送成功打印结果
			if (result == 0) {
				System.out.println("相同内容群发发送成功");
				System.out.println(msgId.toString());
			} else {
				// 发送失败打印错误码
				System.out.println(result);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	/**
	 * @description 模板发送接口
	 * @param CHttpPost 短信发送对象
	 * @return void
	 * 注需要输入信息
	 * 1.手机号
	 * 2.模板内容
	 * 3.模板编号
	 * 4.扩展子号(可选)
	 * 5.自定义流水号(可选)
	 * 6.扩展数据(可选)
	 * 7.业务类型(可选)
	 * 8.定时时间(可选)
	 * */
	public static void templateSend(CHttpPost post) throws IOException {
		// 设置用户信息获取短信对象
		Message m =setUser(post);
		// 设置手机号
		m.setMobile(getManyPhone());
		// 设置模板内容
		m.setContent(tempContent());
		// 设置短信模板编号
		m.setTmplid(getTmplid());
		// 获取扩展子号
		String strSubPort = getStrSubPort();
		// 当扩展子号不为空设置扩展子号
		if (validateStr(strSubPort)) {
			m.setExno(strSubPort);
		}
		// 获取自定义流水号
		String strUserMsgId = getStrUserMsgId();
		// 当自定义流水号不为空设置自定义流水号
		if (validateStr(strUserMsgId)) {
			m.setCustid(strUserMsgId);
		}
		// 获取扩展数据
		String exData = getExdata();
		// 当扩展数据不为空设置扩展数据
		if (validateStr(exData)) {
			m.setExdata(exData);
		}
		// 获取业务类型
		String svrType = getSvrType();
		// 业务类型不为空设置业务类型
		if (validateStr(svrType)) {
			m.setSvrtype(svrType);
		}
		// 获取定时时间
		String attTime = getAttime();
		// 当定时时间不为空设置定时时间
		if (validateStr(attTime)) {
			m.setAttime(attTime);
		}
		// 定义返回字符串
		StringBuffer msgId = new StringBuffer();
		// 调用方法进行发送返回结果在msgId中
		int result = post.templateSend(m, msgId);
		// 当result为0发送成功打印结果
		if (result == 0) {
			System.out.println("模板发送成功");
			System.out.println(msgId.toString());
		} else {
			// 发送失败打印错误码
			System.out.println(result);
		}
	}
	/**
	 * @description 模板发送接口
	 * @param CHttpPost 短信发送对象
	 * @return void
	 * 注需要输入信息
	 * 1.手机号
	 * 2.短信内容
	 * 3.扩展子号(可选)
	 * 4.自定义流水号(可选)
	 * 5.扩展数据(可选)
	 * 6.业务类型(可选)
	 * 7.定时时间(可选)
	 * */
	public static void multiSend(CHttpPost post) throws IOException {
		// 设置用户信息
		Message message = setUser(post);
		// 定义发送短信集合
		List<MultiMt> multixMts = new ArrayList<MultiMt>();
		// 定义是否继续添加短信标识
		boolean flag = true;
		while (flag) {
			// 定义每条短信短信
			MultiMt m = new MultiMt();
			m.setMobile(getPhone());
			// 设置短信内容
			m.setContent(getContent());
			// 获取扩展子号
			String strSubPort = getStrSubPort();
			// 当扩展子号不为空设置扩展子号
			if (validateStr(strSubPort)) {
				m.setExno(strSubPort);
			}
			// 获取自定义流水号
			String strUserMsgId = getStrUserMsgId();
			// 当自定义流水号不为空设置自定义流水号
			if (validateStr(strUserMsgId)) {
				m.setCustid(strUserMsgId);
			}
			// 获取扩展数据
			String exData = getExdata();
			// 当扩展数据不为空设置扩展数据
			if (validateStr(exData)) {
				m.setExdata(exData);
			}
			// 获取业务类型
			String svrType = getSvrType();
			// 业务类型不为空设置业务类型
			if (validateStr(svrType)) {
				m.setSvrtype(svrType);
			}
			// 获取定时时间
			String attTime = getAttime();
			// 当定时时间不为空设置定时时间
			if (validateStr(attTime)) {
				m.setAttime(attTime);
			}
			// 将每条短信对象加入到短信集合中
			multixMts.add(m);
			System.out.println("是否继续添加短信是:Y，否N");
			// 获取用户输入
			String isContinue = getInputParam();
			isContinue = isAppointStr("Y", "N", isContinue);
			if ("N".equalsIgnoreCase(isContinue)) {
				// 不输入改变标识退出循环
				flag = false;
				continue;
			}
			// 继续输入判断短信对象是否超过1000
			if ("Y".equalsIgnoreCase(isContinue) && multixMts.size() >= 1000) {
				System.out.println("已有1000条短信不能继续添加");
				flag = false;
				continue;
			}
		}
		// 定义返回字符串
		StringBuffer msgId = new StringBuffer();
		// 调用方法进行发送返回结果在msgId中
		int result = post.multiSend(message.getUserid(), message.getPwd(),message.getTimestamp(), multixMts, msgId);
		// 当result为0发送成功打印结果
		if (result == 0) {
			System.out.println("个性化发送成功");
			System.out.println(msgId.toString());
		} else {
			// 发送失败打印错误码
			System.out.println(result);
		}
	}
	/**
	 * @description 获取余额接口
	 * @param CHttpPost 短信发送对象
	 * @return void
	 * */
	public static void getBalance(CHttpPost post) {
		// 设置用户信息
		Message m = setUser(post);
		// 调用查询余额接口。
		int result = post.getBalance(m.getUserid(), m.getPwd(),m.getTimestamp());
		// result查询成功返回余额
		if (result >= 0) {
			System.out.println("查询成功,余额为：" + result + "条");
		} else {
			// 非0失败返回错误码
			System.out.println("查询失败，错误码为：" + result);
		}

	}
	/**
	 * @description 获取上行接口
	 * @param CHttpPost 短信发送对象
	 * @return void
	 * 需要输入信息
	 * 1.获取上行最大条数
	 * */
	public static void getMo(CHttpPost cHttpPost) throws IOException {
		// 设置用户信息
		Message m = setUser(cHttpPost);
		System.out.print("请输入获取上行的最大条数");
		// 获取用户输入
		String size = getInputParam();
		// 定义输入是否合法标识
		boolean isTrue = true;
		while (isTrue) {
			// 验证输入是否合法
			if (validateStr(size) && isUnSignDigit(size)) {
				// 合法退出循环改变标识
				isTrue = false;
			} else {
				// 不合法重新输入
				size = reInput(size);
			}
		}
		// 定义上行对象集合
		List<MO> moList = new ArrayList<MO>();
		// 调用获取上行接口
		int result = cHttpPost.getMo(m.getUserid(), m.getPwd(),m.getTimestamp(), Integer.valueOf(size), moList);
		// result调用接口成功打印结果
		if (result == 0) {
			System.out.println("获取上行成功！获取到的上行有" + moList.size() + "条记录。");
			if (moList != null && moList.size() > 0) {
				// 打印上行信息
				printMoPack(moList);
			}
		} else {
			// 失败打印错误码
			System.out.println("获取上行失败，错误码为:" + result);
		}
	}
	/**
	 * @description 获取状态报告接口
	 * @param CHttpPost 短信发送对象
	 * @return void
	 * 需要输入信息
	 * 1.获取状态报告最大条数
	 * */
	public static void getRpt(CHttpPost post) throws IOException {
		// 设置用户信息
		Message m = setUser(post);
		System.out.print("请输入获取状态报告的最大条数");
		// 获取用户输入
		String size = getInputParam();
		// 定义输入是否合法标识
		boolean isTrue = true;
		while (isTrue) {
			// 验证输入是否合法
			if (validateStr(size) && isUnSignDigit(size)) {
				// 合法退出循环改变标识
				isTrue = false;
			} else {
				// 不合法重新输入
				size = reInput(size);
			}
		}
		// 定义状态报告对象集合
		List<RPT> rptList = new ArrayList<RPT>();
		// 调用获取状态报告接口
		int result = post.getRpt(m.getUserid(), m.getPwd(),m.getTimestamp(), Integer.valueOf(size), rptList);
		// result调用接口成功打印结果
		if (result == 0) {
			System.out.println("获取状态报告成功！获取到的状态报告有" + rptList.size() + "条记录。");
			if (rptList != null && rptList.size() > 0) {
				// 打印状态报告信息
				printRptPack(rptList);
			}
		} else {
			// 失败打印错误码
			System.out.println("获取状态报告失败，错误码为:" + result);
		}
	}
	/**
	 * @description 获取状态报告接口
	 * @param CHttpPost 短信发送对象
	 * @return void
	 * */
	public static void getRemains(CHttpPost cHttpPost){
		// 设置用户信息
		Message m = setUser(cHttpPost);
		// 调用查询余额接口。
		Remains remains = cHttpPost.getRemains(m.getUserid(),m.getPwd(),m.getTimestamp());
		//remains =null查询失败
		if(remains==null){
			System.out.println("查询失败");
			return;
		}
		//返回码不等于0查询失败
		if(remains.getResult()!=0){
			System.out.println("查询失败错误码为"+remains.getResult());
			return;
		}
		//查询成功打印对应信息
		if(remains.getChargetype()==0)
		{
			System.out.println("查询成功,剩余条数为：" + remains.getBalance()+"条");
		}else if(remains.getChargetype()==1)
		{
			System.out.println("查询成功,剩余金额为：" + remains.getMoney()+"元");
		}else
		{
			System.out.println("未知的计费类型,计费类型:"+remains.getChargetype());
		}
	}
	/**
	 * @description 控制台中获取单个手机号
	 * @param 控制台输入单个手机号如(13698523658)
	 * @return String 手机号
	 * */
	public static String getPhone() throws IOException {
		System.out.println("请输入手机号");
		// 获取用户输入
		String phone = getInputParam();
		// 判读手机号不为空且都为数字
		phone = forInt(phone);
		return phone;
	}
	/**
	 * @description 控制台中获取多个个手机号
	 * @param 控制台输入单个手机号如(13698523658,13698547854)
	 * @return String 手机号
	 * */
	public static String getManyPhone() throws IOException {
		System.out.println("请输入手机号码（多个号码用英文逗号分隔，最多1000个号码）");
		// 获取用户输入
		String phone = getInputParam();
		// 输入是否正确标识
		boolean isTrue = true;
		// 判断手机号码是否为空
		while (isTrue) {
			// 验证手机号是否为空
			if (!validateStr(phone)) {
				// 验证不通过重新输入
				phone = reInput(phone);
				continue;
			}
			// 获取手机号数组
			String phones[] = phone.split(",");
			// 验证手机号个数
			if (phones.length <= 0 || phones.length > 1000) {
				// 验证不通过重新输入
				phone = reInput(phone);
				continue;
			}
			// 循环验证每一个手机号是否合法
			for (int i = 0; i < phones.length; i++) {
				if (!validateStr(phones[i]) || !isUnSignDigit(phones[i])) {
					// 验证不通过重新输入
					phone = reInput(phone);
					continue;
				}
			}
			isTrue = false;
		}
		return phone;
	}
	/**
	 * @description 当参数验证失败调用此方法重新输入
	 * @param 失败参数
	 * @return String 控制台输入参数
	 * */
	public static String reInput(String str) throws IOException {
		System.out.println("输入内容不合法请重新输入");
		str = getInputParam();
		return str;
	}
	/**
	 * @description 控制台中获取短信内容
	 * @param 控制台输入短信内容如(abcdefg)
	 * @return String 短信内容
	 * */
	public static String getContent() throws IOException {
		System.out.println("请输入短信内容(内容长度不大于990个汉字(英文短信不大于1980))");
		// 获取用户输入
		String content = getInputParam();
		// 短信内容是否正确标识
		boolean isTrue = true;
		// 验证短信内容
		while (isTrue) {
			// 验证短信内容是否合法
			if (ValidateParamTool.validateMessage(content)) {
				// 输入合法改变标识退出循环
				isTrue = false;
			} else {
				// 输入不和法继续输入
				content = reInput(content);
			}
		}
		return content;
	}
	/**
	 * @description 控制台中获取模板内容
	 * @param 控制台输入模板内容如(#P_1#=梦网科技&#P_2#=201701)
	 * @return String 模板内容
	 * */
	public static String tempContent() throws IOException {
		System.out.println("请输入模板参数内容（输入对应的变量名和变量值，如#P_1#=梦网科技&#P_2#=201701，多个键值对以\"&\"隔开）");
		// 获取用户输入
		String content = getInputParam();
		// 定义是否合法标识
		boolean isTrue = true;
		while (isTrue) {
			// 加一个"&"为了方便正则校验格式
			content += "&";
			if (!"&".equals(content)&& content.matches("^(((#P_\\d+#)=(.*))&)*$")) {
				content = content.substring(0, content.lastIndexOf("&"));
				// 将模板内容，进行两次两次urlencode编码（GBK明文）
				// 第一次只将变量名及变量值进行编码
				StringBuilder contentStr = new StringBuilder();
				for (String str : content.split("&")) {
					String[] strs = str.split("=");
					contentStr.append(URLEncoder.encode(strs[0], "GBK"))
							.append("=")
							.append(URLEncoder.encode(strs[1], "GBK"))
							.append("&");
				}
				content = contentStr.substring(0, contentStr.lastIndexOf("&"));
				// 第二次将整体内容进行编码。
				content = URLEncoder.encode(content, "GBK");
				isTrue = false;
			} else {
				// 验证失败重新输入
				content = reInput(content);
			}
		}
		return content;
	}
	/**
	 * @description 控制台中获取扩展子号 
	 * @param 控制台输入扩展子号 如(159854)
	 * @return String 扩展子号
	 * */
	public static String getStrSubPort() throws IOException {
		System.out.println("请输入扩展子号 （不带无需填写，长度不大于6位）");
		// 获取用户输入
		String strSubPort = getInputParam();
		// 扩展子号合法标识
		boolean isTrue = true;
		// 判断扩展子号是否合法
		while (isTrue) {
			// 验证扩展子号是否合法
			if (ValidateParamTool.validateSubPort(strSubPort)) {
				// 输入合法改变标识退出循环
				isTrue = false;
			} else {
				System.out.println("输入内容不合法请重新输入");
				// 输入不和法继续输入
				strSubPort = reInput(strSubPort);
			}
		}
		return strSubPort;
	}
	/**
	 * @description 控制台中获取自定义流水号
	 * @param 控制台输入自定义流水号如(159854587)
	 * @return String 自定义流水号
	 * */
	public static String getStrUserMsgId() throws IOException {
		System.out.println("请输入用户自定义流水号(不带无需填写)");
		// 获取用户输入
		String strUserMsgId = getInputParam();
		// 自定义流水号合法标识
		boolean isTrue = true;
		// 判断自定义流水号是否合法
		while (isTrue) {
			// 验证自定义流水号是否合法
			if (ValidateParamTool.validateCustId(strUserMsgId)) {
				// 输入合法改变标识退出循环
				isTrue = false;
			} else {
				// 输入不和法继续输入
				strUserMsgId = reInput(strUserMsgId);
			}
		}
		return strUserMsgId;
	}
	/**
	 * @description 控制台中获取自定义扩展数据
	 * @param 控制台输入自定义扩展数据
	 * @return String 自定义扩展数据
	 * */
	public static String getExdata() throws IOException {
		System.out.println("请输入自定义扩展数据(不带无需填写)");
		// 获取用户输入
		String exData = getInputParam();
		// 扩展数据是否合法标识
		boolean isTrue = true;
		// 判断扩展数据是否合法标识
		while (isTrue) {
			// 验证扩展数据是否合法
			if (ValidateParamTool.validateExData(exData)) {
				// 输入合法改变标识退出循环
				isTrue = false;
			} else {
				// 输入不和法继续输入
				exData = reInput(exData);
			}
		}
		return exData;
	}
	/**
	 * @description 控制台中获取业务类型
	 * @param 控制台输入业务类型
	 * @return String 业务类型
	 * */
	public static String getSvrType() throws IOException {
		System.out.println("请输入业务类型(不带无需填写)");
		// 获取用户输入
		String svrType = getInputParam();
		// 业务类型合法标识
		boolean isTrue = true;
		// 判读业务类型是否合法
		while (isTrue) {
			// 验证业务类型是否合法
			if (ValidateParamTool.validateSvrType(svrType)) {
				// 验证通过推出循环
				isTrue = false;
			} else {
				// 输入不和法继续输入
				svrType = reInput(svrType);
			}
		}
		return svrType;
	}
	/**
	 * @description 控制台中获取定时时间
	 * @param 控制台输入定时时间
	 * @return String 定时时间
	 * */
	public static String getAttime() throws IOException {
		System.out.println("请输入定时时间，不能小于当前时间，如20181219120000，不定时无需填写");
		// 获取用户输入
		String attTime = getInputParam();
		// 定时时间合法标识
		boolean isTrue = true;
		// 判断定时时间是否合法
		while (isTrue) {
			// 验证定时时间是否合法
			if (ValidateParamTool.validateAttime(attTime)) {
				// 合法改变标识退出循环
				isTrue = false;
			} else {
				// 输入不和法继续输入
				attTime = reInput(attTime);

			}
		}
		return attTime;
	}
	/**
	 * @description 控制台中获取短信模板编号
	 * @param 控制台输入短信模板编号
	 * @return String 短信模板编号
	 * */
	public static String getTmplid() throws IOException {
		System.out.println("请输入短信模板编号必填且长度最大20位");
		// 获取用户输入
		String tmplId = getInputParam();
		// 输入是否合法标识
		boolean isTrue = true;
		while (isTrue) {
			// 验证短信模板编号是否正确
			if (validateStr(tmplId) && tmplId.length() <= 20) {
				// 验证通过改变标识退出循环
				isTrue = false;
			} else {
				// 验证不同过重新输入
				tmplId = reInput(tmplId);
			}
		}
		return tmplId;
	}
	/**
	 * @description 设置用户账号密码时间戳信息
	 * @param CHttpPost 短信发送对象
	 * @return Message 短信对象
	 * */
	public static Message setUser(CHttpPost post) {
		// 日期格式定义
		SimpleDateFormat sdf = new SimpleDateFormat("MMddHHmmss");
		// 初始化短信对象
		Message message = new Message();
		// 将 userid转成大写,以防大小写不一致
		userid = userid.toUpperCase();
		// 对密码进行加密
		String encryptPwd = null;
		// 判断密码是否加密。
		// 密码加密，则对密码进行加密
		String timestamp = null;
		if (isEncFlag) {
			// 设置时间戳
			timestamp = sdf.format(Calendar.getInstance().getTime());
			// 对密码进行加密
			encryptPwd = post.encryptPwd(userid, pwd, timestamp);
		} else {
			// 不加密，不需要设置时间戳
			timestamp = null;
			encryptPwd = pwd;
		}
		// 设置账号
		message.setUserid(userid);
		// 设置密码
		message.setPwd(encryptPwd);
		// 设置时间戳
		message.setTimestamp(timestamp);
		return message;
	}
	/**
	 * @description 设置连接池配置
	 * @return void
	 * 需要输入信息
	 * 1.连接池获取连接超时时间(如设置2秒就输入2000)不输入默认2秒
	 * 2.连接超时时间(如设置2秒就输入2000)不输入默认5秒
	 * 3.响应超时时间(如设置2秒就输入2000)不输入默认60秒
	 * 4.连接池最大连接数如(10)不输入默认10
	 * 5.每个路由最大连接数如(2)不输入默认2
	 * 6.清理空余连接时间如(2秒输入2)不输入默认15
	 * */
	public static void setHttp() throws IOException {
		System.out.println("是否输入连接池获取连接超时时间输入:Y,不输入N");
		// 是否输入连接池获取连接超时时间标识
		String isRtime = getInputParam();
		// 验证是否是输入Y或者N
		isRtime = isAppointStr("Y", "N", isRtime);
		if ("Y".equalsIgnoreCase(isRtime)) {
			System.out.println("请输入连接池获取连接超时时间(如设置2秒就输入2000)");
			// 获取输入值
			String requestTimeout = getInputParam();
			// 判断是否是整数
			requestTimeout = forInt(requestTimeout);
			// 设置连接池获取连接超时时间
			ConfigManager.setConnectionRequestTimeout(Integer.valueOf(requestTimeout));
		} else {
			// 默认设置连接池获取连接超时时间2秒
			ConfigManager.setConnectionRequestTimeout(30 * 1000);
		}
		System.out.println("是否输入连接超时时间输入:Y,不输入N");
		// 是否输入连接超时时间标识
		String isCtime = getInputParam();
		// 验证是否是输入Y或者N
		isCtime = isAppointStr("Y", "N", isCtime);
		if ("Y".equalsIgnoreCase(isCtime)) {
			System.out.println("请输入连接超时时间(如设置2秒就输入2000)");
			// 获取用户输入
			String connectTimeout = getInputParam();
			// 判断是否是整数
			connectTimeout = forInt(connectTimeout);
			// 设置连接超时时间
			ConfigManager.setConnectTimeout(Integer.valueOf(connectTimeout));
		} else {
			// 默认设置连接超时时间5秒
			ConfigManager.setConnectTimeout(20 * 1000);
		}
		System.out.println("是否输入响应超时时间输入:Y,不输入N");
		// 是否输入响应超时时间标识
		String isStime = getInputParam();
		// 验证是否是输入Y或者N
		isStime = isAppointStr("Y", "N", isStime);
		if ("Y".equalsIgnoreCase(isStime)) {
			System.out.println("请输入响应超时时间(如设置2秒就输入2000)");
			// 获取用户输入
			String socketTimeout = getInputParam();
			// 判断是否是整数
			socketTimeout = forInt(socketTimeout);
			// 设置响应超时时间
			ConfigManager.setSocketTimeout(Integer.valueOf(socketTimeout));
		} else {
			// 默认设置响应超时时间60秒
			ConfigManager.setSocketTimeout(60 * 1000);
		}
		System.out.println("是否输入连接池最大连接数及每个路由最大连接数输入:Y,不输入N(连接池最大数为路由连接最大数的整数倍)");
		// 是否连接池最大连接数标识
		String maxTotalFlag = getInputParam();
		// 验证是否是输入Y或者N
		maxTotalFlag = isAppointStr("Y", "N", maxTotalFlag);
		if ("Y".equalsIgnoreCase(maxTotalFlag)) {
			System.out.println("请输入连接池最大连接数");
			// 获取用户输入
			String maxTotal = getInputParam();
			// 判断是否是整数
			maxTotal = forInt(maxTotal);
			System.out.println("请输入每个路由最大连接数");
			// 每个路由最大连接数默认为2
			String maxRoute = getInputParam();
			// 判断是否是整数
			maxRoute = forInt(maxRoute);
			// 设置连接池最大连接数及每个路由最大连接数
			ConfigManager.setMaxTotal(Integer.valueOf(maxTotal),Integer.valueOf(maxRoute));
		} else {
			ConfigManager.setMaxTotal(20, 10);
		}
		System.out.println("是否输入空余连接清除时间输入:Y,不输入:N");
		// 是否设置空余连接清除时间
		String isClear = getInputParam();
		if ("Y".equalsIgnoreCase(isClear)) {
			System.out.println("请输入空余连接清除时间(如15秒输入15)");
			// 获取用户输入
			String clearTime = getInputParam();
			// 判断是否是整数
			clearTime = forInt(clearTime);
			ConfigManager.setClearTime(Integer.valueOf(clearTime));
		} else {
			ConfigManager.setClearTime(15);
		}
	}
	/**
	 * @description 控制台获取ip加端口信息
	 * @param 控制台先输入ip如(***********)再输入端口(7901)
	 * @return ip:端口
	 */
	public static String addIpAndPort() throws IOException {
		System.out.println("请输入ip");
		// 获取输入的第一个ip和端口
		String ip = getInputParam();
		// 判断请求ip1不为空，为空重新输入，不为空返回
		ip = forStr(ip);
		System.out.println("请输入端口");
		// 获取端口
		String port = getInputParam();
		// 判断端口是否合法，不合法继续获取，合法返回
		port = forStr(port);
		return ip + ":" + port;
	}
	/**
	 * @description 控制台获取输入参数
	 * @param 控制台输入参数
	 * @return String 控制台输入参数
	 */
	public static String getInputParam() throws IOException {
		// 控制台输入流
		BufferedReader breader = new BufferedReader(new InputStreamReader(System.in,"GBK"));
		 //获取输入字符串
		String str = breader.readLine();
		// 返回输入字符串
		return str;
	}
	/**
	 * @description 判断输入参数是否是空字符如果是重新输入
	 * @param 控制台输入参数
	 * @return String 控制台输入参数
	 */
	public static String forStr(String str) throws IOException {
		while (true) {
			// 判断请求路径是否正确
			if (validateStr(str)) {
				// 退出循环
				break;
			} else {
				// 继续获取输入的路径
				str = reInput(str);
			}
		}
		return str;
	}
	/**
	 * @description 判断输入参数是否是数字如果不是重新输入
	 * @param 控制台输入参数
	 * @return String 控制台输入参数
	 */
	public static String forInt(String str) throws IOException {
		while (true) {
			// 判断请求str是符合规则
			if (validateStr(str) && isUnSignDigit(str)) {
				// 符合退出循环
				break;
			} else {
				// 不符合继续获取输入的路径
				str = reInput(str);
			}
		}
		return str;

	}
	/**
	 * @description 判断输入参数是否是Y或者N如果不是重新输入
	 * @param 控制台输入参数
	 * @return String 控制台输入参数
	 */
	public static String isAppointStr(String yStr, String nStr, String str)throws IOException {
		while (true) {
			// 判断请求str是符合规则
			if (validateStr(str)&& (yStr.equalsIgnoreCase(str) || nStr.equalsIgnoreCase(str))) {
				// 符合退出循环
				break;
			} else {
				// 不符合继续获取输入的路径
				str = reInput(str);
			}
		}
		return str;
	}
	/**
	 * @description 验证字符是否是空字符或者null
	 * @param String 字符串
	 * @return booelan true(正确字符串) false(错误字符串)
	 */
	public static boolean validateStr(String str) {
		if (str != null && !"".equals(str.trim())) {
			return true;
		} else {
			return false;
		}
	}

	/**
	 * 验证是否是无符号数字
	 * @param str 要验证的数字
	 * @return true(是数字) false(不是数字)
	 */
	private static boolean isUnSignDigit(String str) {
		char[] num = str.toCharArray();
		for (int i = 0; i < num.length; i++) {
			if (!Character.isDigit(num[i])) {
				return false;
			}
		}
		return true;
	}

	/**
	 * 打印上行信息
	 * @param moList(上行信息集合)
	 * @return void
	 */
	public static void printMoPack(List<MO> moList) {
		if (moList != null && moList.size() > 0) {
			MO mo = null;
			// 循环上行信息
			for (int i = 0; i < moList.size(); i++) {
				mo = moList.get(i);
				System.out.println("msgid:" + mo.getMsgid() + ",mobile:"
						+ mo.getMobile() + ",spno:" + mo.getSpno() + ",exno:"
						+ mo.getExno() + ",rtime:" + mo.getRtime()
						+ ",content:" + mo.getContent());
			}
		} else {
			System.out.println("无上行");
		}
	}

	/**
	 * 打印状态报告信息
	 * @param rptList(状态报告集合)
	 * @return void
	 */
	public static void printRptPack(List<RPT> rptList) {
		if (rptList != null && rptList.size() > 0) {
			RPT rpt = null;
			// 循环状态报告信息
			for (int i = 0; i < rptList.size(); i++) {
				rpt = rptList.get(i);
				System.out.println("msgid:" + rpt.getMsgid() + ",custid:"
						+ rpt.getCustid() + ",pknum:" + rpt.getPknum()
						+ ",pktotal:" + rpt.getPktotal() + ",mobile:"
						+ rpt.getMobile() + ",spno:" + rpt.getSpno() + ",exno:"
						+ rpt.getExno() + ",stime:" + rpt.getStime()
						+ ",rtime:" + rpt.getRtime() + ",status:"
						+ rpt.getStatus() + ",errcode:" + rpt.getErrcode()
						+ ",exdata:" + rpt.getExdata() + ",errdesc:"
						+ rpt.getErrdesc());
			}
		} else {
			System.out.println("无状态报告");
		}
	}

}
