package com.hzw.ssm.sys.user.action;

import java.io.UnsupportedEncodingException;

import org.apache.struts2.convention.annotation.Action;
import org.apache.struts2.convention.annotation.Namespace;
import org.apache.struts2.convention.annotation.ParentPackage;
import org.apache.struts2.convention.annotation.Result;
import org.apache.struts2.convention.annotation.Results;
import org.springframework.beans.factory.annotation.Autowired;

import com.hzw.ssm.fw.base.BaseAction;
import com.hzw.ssm.fw.util.MD5Util;
import com.hzw.ssm.sys.call.util.MD5;
import com.hzw.ssm.sys.user.entity.UserEntity;
import com.hzw.ssm.sys.user.service.UserService;
import com.hzw.ssm.util.encrypt.Base64Util;
import com.opensymphony.xwork2.ActionContext;
/**
 * 修改密码
 *
 */
@Namespace("/changepassword")
@ParentPackage(value = "default")
@Results( { @Result(name = "init", location = "/jsp/user/pwInitial.jsp"),
		@Result(name = "error", location = "/jsp/user/pwInitial.jsp"),
		@Result(name = "success", location = "/jsp/user/pwSuccess.jsp")
		})
public class ChangePasswordAction extends BaseAction{
	/** 原密码 */
	private String oldPassword;
	
	/** 新密码 */
	private String newPassword;
	
	/** 确认密码 */
	private String confirmPassword; 
	
	/** 提示信息 */
	private String message;
	
	@Autowired
	private UserService userService;
	
	@Action("tochange")
	public String tochange(){
		return INIT;
	}
	
	@Action("execute")
	public String execute(){
		oldPassword = oldPassword.trim();
		newPassword = newPassword.trim();
		confirmPassword = confirmPassword.trim();
		try {
		// 获取密码并对密码进行Base64解码，解码后统一转换成大写
		 oldPassword = Base64Util.base64Decode(oldPassword, "UTF-8").toUpperCase();
			newPassword = Base64Util.base64Decode(newPassword, "UTF-8").toUpperCase();
			 confirmPassword = Base64Util.base64Decode(confirmPassword, "UTF-8").toUpperCase();
		// 获取用户信息
		UserEntity user = (UserEntity) ActionContext.getContext().getSession().get("userInfo");
		if(null==oldPassword || ("").equals(oldPassword)){
			this.setMessage("请输入 原密码 ！");
			return ERROR;
		}
		if(null==newPassword || ("").equals(newPassword)){
			this.setMessage("请输入 新密码 ！");
			return ERROR;
		}
		if(null==confirmPassword || ("").equals(confirmPassword)){
			this.setMessage("请输入 确认密码 ！");
			return ERROR;
		}
		if(newPassword.length()<6){
			this.setMessage("请输入六位或六位以上新密码!");
			return ERROR;
		}
		if(!newPassword.equals(confirmPassword)){
			this.setMessage("两次输入密码不同!");
			return ERROR;
		}
		if(!oldPassword.equals(user.getPassword())){
			this.setMessage("您输入的 原密码 错误！");
			return ERROR;
		}
		
		this.setOpaUserAndDate(user);
		//对新密码进行MD5加密
//		confirmPassword=MD5.md5(confirmPassword);
		userService.changePassword(user, confirmPassword);
		} catch (UnsupportedEncodingException e) {
			e.printStackTrace();
		}
		return SUCCESS;
	}

	public String getOldPassword() {
		return oldPassword;
	}

	public void setOldPassword(String oldPassword) {
		this.oldPassword = oldPassword;
	}

	public String getNewPassword() {
		return newPassword;
	}

	public void setNewPassword(String newPassword) {
		this.newPassword = newPassword;
	}

	public String getConfirmPassword() {
		return confirmPassword;
	}

	public void setConfirmPassword(String confirmPassword) {
		this.confirmPassword = confirmPassword;
	}

	public String getMessage() {
		return message;
	}

	public void setMessage(String message) {
		this.message = message;
	}
}
