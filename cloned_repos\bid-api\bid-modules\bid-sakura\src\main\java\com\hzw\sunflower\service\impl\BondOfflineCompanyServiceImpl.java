package com.hzw.sunflower.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzw.sunflower.constant.CommonConstants;
import com.hzw.sunflower.constant.constantenum.*;
import com.hzw.sunflower.controller.request.*;
import com.hzw.sunflower.controller.response.*;
import com.hzw.sunflower.dao.BondOfflineCompanyMapper;
import com.hzw.sunflower.dao.SpecialTicketMapper;
import com.hzw.sunflower.entity.*;
import com.hzw.sunflower.exception.ExceptionEnum;
import com.hzw.sunflower.exception.SunFlowerException;
import com.hzw.sunflower.service.*;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
public class BondOfflineCompanyServiceImpl extends ServiceImpl<BondOfflineCompanyMapper, BondOfflineCompany> implements BondOfflineCompanyService {

    @Autowired
    private TycRecordService tycRecordService;

    @Autowired
    private BondApplyRelationService bondApplyRelationService;

    @Autowired
    private OfflineCompanyService offlineCompanyService;

    @Autowired
    private SpecialTicketService specialTicketService;

    @Autowired
    private CustomerToNccService customerToNccService;


    /**
     * 模糊搜索供应商信息
     * @param req
     * @return
     */
    @Override
    public IPage<OfflineCompanyVo> queryCompany(OfflineCompanyReq req) {
        IPage<OfflineCompanyVo> page = req.buildPage();
        //先匹配历史线下项目保证金供应商数据
        page = this.baseMapper.queryFromBondOfflineCompany(page,req.getCompanyName());
        if (page == null || page.getRecords().size() == 0 ) {
            //匹配企业表数据
            page = this.baseMapper.queryFromCompany(page,req.getCompanyName());
        }
        return page;
    }

    /**
     * 新增/修改供应商信息
     * @param req
     * @return
     */
    @Override
    public Boolean addCompany(BondOfflineCompanyReq req) {
        boolean result = false;
        boolean checkResult = false;
        if (CommonConstants.NO.equals(req.getIsCompanyAbroad())) {
            //境内企业，天眼查校验
            TycCompany company = new TycCompany();
            company.setCompanyName(req.getCompanyName());
            company.setOrganizationNum(req.getOrganizationNum());
            Integer code = tycRecordService.checkNameAndOrganization(company);
            if (ExceptionMsgEnum.SUCCESS.getCode().equals(code)){
                //验证通过
                checkResult = true;
            } else if (ExceptionMsgEnum.COMPANY_NAME.getCode().equals(code)){
                //企业名称不匹配
                throw new SunFlowerException(ExceptionEnum.COMPANY_NAME_ERROR, ExceptionEnum.COMPANY_NAME_ERROR.getMessage());
            } else if (ExceptionMsgEnum.ORGANIZATION_NUM.getCode().equals(code)){
                //企业统一社会信用代码不匹配
                throw new SunFlowerException(ExceptionEnum.ORGANIZATION_NUM_ERROR, ExceptionEnum.ORGANIZATION_NUM_ERROR.getMessage());
            } else {
                throw new SunFlowerException(ExceptionEnum.TYC_CHECK_ERROR, ExceptionEnum.TYC_CHECK_ERROR.getMessage());
            }
        } else {
            //境外企业不校验
            checkResult = true;
        }
        if (checkResult) {
            //保存供应商数据
            OfflineCompany offlineCompany = new OfflineCompany();
            BeanUtils.copyProperties(req, offlineCompany);
            if (null != req.getOfflineCompanyId()) {
                //新增前判断是否已存在
                LambdaQueryWrapper<OfflineCompany> offlineCompanyLambdaQueryWrapper = new LambdaQueryWrapper<>();
                offlineCompanyLambdaQueryWrapper.eq(OfflineCompany::getCompanyName, offlineCompany.getCompanyName());
                offlineCompanyLambdaQueryWrapper.eq(OfflineCompany::getIsCompanyAbroad, offlineCompany.getIsCompanyAbroad());
                if (CommonConstants.NO.equals(req.getIsCompanyAbroad())) {
                    offlineCompanyLambdaQueryWrapper.eq(OfflineCompany::getOrganizationNum, offlineCompany.getOrganizationNum());
                } else {
                    offlineCompanyLambdaQueryWrapper.eq(OfflineCompany::getBusinessLicense, offlineCompany.getBusinessLicense());
                }
                offlineCompanyLambdaQueryWrapper.last("limit 1");
                OfflineCompany offlineCompanyExist = offlineCompanyService.getOne(offlineCompanyLambdaQueryWrapper);
                if (null != offlineCompanyExist) {
                    offlineCompany.setId(offlineCompanyExist.getId());
                }
            }
            offlineCompanyService.saveOrUpdate(offlineCompany);
            req.setOfflineCompanyId(offlineCompany.getId());
            //保存企业专票信息
            LambdaQueryWrapper<SpecialTicket> specialTicketLambdaQueryWrapper = new LambdaQueryWrapper<>();
            specialTicketLambdaQueryWrapper.eq(SpecialTicket::getCompanyId, offlineCompany.getId());
            specialTicketLambdaQueryWrapper.last("limit 1");
            SpecialTicket specialTicket = specialTicketService.getOne(specialTicketLambdaQueryWrapper);
            if (null == specialTicket) {
                specialTicket = new SpecialTicket();
                specialTicket.setCompanyId(offlineCompany.getId());
                specialTicket.setCompanyName(offlineCompany.getCompanyName());
                if (CommonConstants.YES.equals(offlineCompany.getIsCompanyAbroad())) {
                    specialTicket.setOrganizationNum(offlineCompany.getOrganizationNum());
                } else {
                    specialTicket.setOrganizationNum(offlineCompany.getBusinessLicense());
                }
            }
            specialTicketService.saveOrUpdate(specialTicket);
            //保存客商信息
            //查询客商编号
            LambdaQueryWrapper<CustomerToNcc> customerQuery = new LambdaQueryWrapper<>();
            customerQuery.eq(CustomerToNcc::getName,req.getCompanyName())
                    .orderByDesc(CustomerToNcc::getId).last("limit 1");
            CustomerToNcc one = customerToNccService.getOne(customerQuery);
            if(null == one){
                //若客商不存在，新增一条放到待推送列表
                customerToNccService.addCustomer(req.getCompanyName());
            }
            //保存项目供应商数据
            List<BondApplyRelation> bondApplyRelationList = new ArrayList<>();
            for (Long sectionId : req.getSectionIds()) {
                if (null != req.getOfflineCompanyId()) {
                    //该公司在该标段是否存在
                    LambdaQueryWrapper<BondOfflineCompany> bondOfflineCompanyLambdaQueryWrapper = new LambdaQueryWrapper<>();
                    bondOfflineCompanyLambdaQueryWrapper.eq(BondOfflineCompany::getOfflineCompanyId, req.getOfflineCompanyId());
                    bondOfflineCompanyLambdaQueryWrapper.eq(BondOfflineCompany::getSectionId, sectionId);
                    Long count = this.baseMapper.selectCount(bondOfflineCompanyLambdaQueryWrapper);
                    if (count > 0) {
                        //该供应商在该标段已存在
                        throw new SunFlowerException(ExceptionEnum.ID_REPEAT, ExceptionEnum.ID_REPEAT.getMessage());
                    }
                }
                BondOfflineCompany bondOfflineCompany = new BondOfflineCompany();
                BeanUtils.copyProperties(req, bondOfflineCompany);
                bondOfflineCompany.setSectionId(sectionId);
                result = this.save(bondOfflineCompany);
                //供应商名称与付款户名不一致
                if (!req.getCompanyName().equals(req.getApplyCompanyName())) {
                    //申请关联
                    BondApplyRelation bondApplyRelation = new BondApplyRelation();
                    bondApplyRelation.setProjectId(req.getProjectId());
                    bondApplyRelation.setSectionId(sectionId);
                    bondApplyRelation.setCompanyId(offlineCompany.getId());
                    bondApplyRelation.setPayCompanyName(req.getApplyCompanyName());
                    bondApplyRelation.setContent(req.getApplyCompanyName());
                    bondApplyRelation.setStatus(ApplyRelationStatusEnum.WAIT_HANDLE.getType());
                    bondApplyRelation.setType(ApplyTypeEnum.RELATION_APPLY.getType());
                    bondApplyRelation.setApplyTime(new Date());
                    if (null != req.getPayFileId()) {
                        bondApplyRelation.setPaymentVoucherIds(req.getPayFileId().toString());
                    }
                    bondApplyRelationList.add(bondApplyRelation);
                }
            }
            bondApplyRelationService.saveBatch(bondApplyRelationList);
        }
        return result;
    }

    /**
     * 编辑供应商信息
     * @param req
     * @return
     */
    @Override
    public Boolean updateCompany(BondOfflineCompanyReq req) {
        boolean result = false;
        boolean checkResult = false;
        if (CommonConstants.NO.equals(req.getIsCompanyAbroad())) {
            //境内企业，天眼查校验
            TycCompany company = new TycCompany();
            company.setCompanyName(req.getCompanyName());
            company.setOrganizationNum(req.getOrganizationNum());
            Integer code = tycRecordService.checkNameAndOrganization(company);
            if (code.equals(ExceptionMsgEnum.SUCCESS.getCode())){
                //验证通过
                checkResult = true;
            } else if (code.equals(ExceptionMsgEnum.COMPANY_NAME.getCode())){
                //企业名称不匹配
                throw new SunFlowerException(ExceptionEnum.COMPANY_NAME_ERROR, ExceptionEnum.COMPANY_NAME_ERROR.getMessage());
            } else if (code.equals(ExceptionMsgEnum.ORGANIZATION_NUM.getCode())){
                //企业统一社会信用代码不匹配
                throw new SunFlowerException(ExceptionEnum.ORGANIZATION_NUM_ERROR, ExceptionEnum.ORGANIZATION_NUM_ERROR.getMessage());
            } else {
                throw new SunFlowerException(ExceptionEnum.TYC_CHECK_ERROR, ExceptionEnum.TYC_CHECK_ERROR.getMessage());
            }
        } else {
            //境外企业不校验
            checkResult = true;
        }
        if (checkResult) {
            //保存供应商数据
            OfflineCompany offlineCompany = new OfflineCompany();
            BeanUtils.copyProperties(req, offlineCompany);
            if (null != req.getOfflineCompanyId()) {
                offlineCompany.setId(req.getOfflineCompanyId());
            }
            if (null != offlineCompany.getId()) {
                //新增前判断是否已存在
                LambdaQueryWrapper<OfflineCompany> offlineCompanyLambdaQueryWrapper = new LambdaQueryWrapper<>();
                offlineCompanyLambdaQueryWrapper.eq(OfflineCompany::getCompanyName, offlineCompany.getCompanyName());
                offlineCompanyLambdaQueryWrapper.eq(OfflineCompany::getIsCompanyAbroad, offlineCompany.getIsCompanyAbroad());
                if (CommonConstants.NO.equals(req.getIsCompanyAbroad())) {
                    offlineCompanyLambdaQueryWrapper.eq(OfflineCompany::getOrganizationNum, offlineCompany.getOrganizationNum());
                } else {
                    offlineCompanyLambdaQueryWrapper.eq(OfflineCompany::getBusinessLicense, offlineCompany.getBusinessLicense());
                }
                offlineCompanyLambdaQueryWrapper.last("limit 1");
                OfflineCompany offlineCompanyExist = offlineCompanyService.getOne(offlineCompanyLambdaQueryWrapper);
                if (null != offlineCompanyExist) {
                    offlineCompany.setId(offlineCompanyExist.getId());
                }
            }
            offlineCompanyService.saveOrUpdate(offlineCompany);
            req.setOfflineCompanyId(offlineCompany.getId());
            //保存企业专票信息
            LambdaQueryWrapper<SpecialTicket> specialTicketLambdaQueryWrapper = new LambdaQueryWrapper<>();
            specialTicketLambdaQueryWrapper.eq(SpecialTicket::getCompanyId, offlineCompany.getId());
            specialTicketLambdaQueryWrapper.last("limit 1");
            SpecialTicket specialTicket = specialTicketService.getOne(specialTicketLambdaQueryWrapper);
            if (null == specialTicket) {
                specialTicket = new SpecialTicket();
                specialTicket.setCompanyId(offlineCompany.getId());
                specialTicket.setCompanyName(offlineCompany.getCompanyName());
                if (CommonConstants.YES.equals(offlineCompany.getIsCompanyAbroad())) {
                    specialTicket.setOrganizationNum(offlineCompany.getOrganizationNum());
                } else {
                    specialTicket.setOrganizationNum(offlineCompany.getBusinessLicense());
                }
            }
            specialTicketService.saveOrUpdate(specialTicket);
            //保存客商信息
            //查询客商编号
            LambdaQueryWrapper<CustomerToNcc> customerQuery = new LambdaQueryWrapper<>();
            customerQuery.eq(CustomerToNcc::getName,req.getCompanyName())
                    .orderByDesc(CustomerToNcc::getId).last("limit 1");
            CustomerToNcc one = customerToNccService.getOne(customerQuery);
            if(null == one){
                //若客商不存在，新增一条放到待推送列表
                customerToNccService.addCustomer(req.getCompanyName());
            }
            //保存项目供应商数据
            BondOfflineCompany bondOfflineCompany = new BondOfflineCompany();
            BeanUtils.copyProperties(req, bondOfflineCompany);
            bondOfflineCompany.setId(req.getBondOfflineCompanyId());
            result = this.updateById(bondOfflineCompany);
            //供应商名称与付款户名不一致
            if (!req.getCompanyName().equals(req.getApplyCompanyName())) {
                //申请关联
                BondApplyRelation bondApplyRelation = new BondApplyRelation();
                bondApplyRelation.setProjectId(req.getProjectId());
                bondApplyRelation.setSectionId(req.getSectionId());
                bondApplyRelation.setCompanyId(offlineCompany.getId());
                bondApplyRelation.setPayCompanyName(req.getApplyCompanyName());
                bondApplyRelation.setContent(req.getApplyCompanyName());
                bondApplyRelation.setStatus(ApplyRelationStatusEnum.WAIT_HANDLE.getType());
                bondApplyRelation.setType(ApplyTypeEnum.RELATION_APPLY.getType());
                bondApplyRelation.setApplyTime(new Date());
                bondApplyRelation.setPaymentVoucherIds(req.getPayFileId().toString());
                bondApplyRelationService.save(bondApplyRelation);
            }
        }
        return result;
    }

}
