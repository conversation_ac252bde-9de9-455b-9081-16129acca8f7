package com.hzw.sunflower.controller.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@ApiModel(description = "采购方式及条件设置")
@Data
public class PurchaseTypeReq {

    @ApiModelProperty("一级采购方式 1.依法必招 2.非依法必招 3.政府采购 4.国际招标")
    private Integer purchaseType;

    @ApiModelProperty("二级采购方式 1.招标 2.竞争性磋商 3.竞争性谈判 4.询价 5.征询 6.单一来源 7.其他")
    private Integer purchaseMode;

    @ApiModelProperty("三级采购方式 1.预审公开 2.后审公开 3.邀请 4.二阶段 5.政府公开 4.政府邀请")
    private Integer purchaseStatus;

    @ApiModelProperty(value = "采购文件递交截至时间校验方式 1.不限制 2.仅作提示 3.强校验")
    private Integer submitEndTimeType;

    @ApiModelProperty(value = "时间")
    private Integer submitEndTimeDay;

    @ApiModelProperty(value = "单位 1.天 2.工作日")
    private Integer submitEndTimeUnit;

    @ApiModelProperty(value = "响应发售截至时间校验方式 1.不限制 2.仅作提示 3.强校验")
    private Integer saleEndTimeType;

    @ApiModelProperty(value = "时间")
    private Integer saleEndTimeDay;

    @ApiModelProperty(value = "单位 1.天 2.工作日")
    private Integer saleEndTimeUnit;

    @ApiModelProperty("文件售价说明限制金额")
    private BigDecimal amount;

}
