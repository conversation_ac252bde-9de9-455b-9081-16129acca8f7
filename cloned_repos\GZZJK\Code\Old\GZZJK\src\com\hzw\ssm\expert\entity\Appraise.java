package com.hzw.ssm.expert.entity;

import com.hzw.ssm.fw.base.BaseEntity;

import java.util.Date;

/**
 * 专家评价表
 * 
 * <AUTHOR> @date 2014-10-17
 */
public class Appraise extends BaseEntity {
	private static final long serialVersionUID = 1L;

	private String appraise_id;// 主键

	private String appraise_info_id;// 评价信息表ID

	private String extract_result_id;// 专家结果ID

	private double score;// 评价分数

	private String score_;

	private String parent_id;
	
	private String expert_id;
	
	/**
	 * 评价类型：0：加分1：加分
	 */
	private Integer appraise_type;
	//评价人
	private String appraiseUser;
	//评价时间
	private Date appraiseTime;

	public String getAppraiseUser() {
		return appraiseUser;
	}

	public void setAppraiseUser(String appraiseUser) {
		this.appraiseUser = appraiseUser;
	}

	public Date getAppraiseTime() {
		return appraiseTime;
	}

	public void setAppraiseTime(Date appraiseTime) {
		this.appraiseTime = appraiseTime;
	}

	public Integer getAppraise_type() {
		return appraise_type;
	}

	public void setAppraise_type(Integer appraise_type) {
		this.appraise_type = appraise_type;
	}

	public String getAppraise_id() {
		return appraise_id;
	}

	public void setAppraise_id(String appraiseId) {
		appraise_id = appraiseId;
	}

	public String getAppraise_info_id() {
		return appraise_info_id;
	}

	public void setAppraise_info_id(String appraiseInfoId) {
		appraise_info_id = appraiseInfoId;
	}

	public String getExtract_result_id() {
		return extract_result_id;
	}

	public void setExtract_result_id(String extractResultId) {
		extract_result_id = extractResultId;
	}

	public double getScore() {
		return score;
	}

	public void setScore(double score) {
		this.score = score;
	}

	public String getScore_() {
		return score_;
	}

	public void setScore_(String score) {
		score_ = score;
	}

	public String getParent_id() {
		return parent_id;
	}

	public void setParent_id(String parentId) {
		parent_id = parentId;
	}

	public String getExpert_id() {
		return expert_id;
	}

	public void setExpert_id(String expert_id) {
		this.expert_id = expert_id;
	}
}
