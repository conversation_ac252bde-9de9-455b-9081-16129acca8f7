package com.hzw.sunflower.constant.constantenum;

/**
 * <AUTHOR>
 * @date ：Created in 2021/5/12 10:02
 * @description：短信类型
 * @modified By：`
 * @version: 1.0
 */
public enum SmsTypeEnum {

    REGISTER_BIDDER_CODE("SR-001", "招标人注册验证码"),
    REGISTER_SUPPLIER_CODE("SR-002", "投标人/供应商注册验证码"),
    REGISTER_AGENCY_CODE("SR-003", "代理机构注册验证码"),
    CHANGE_PHONE_AGENCY_CODE("SR-004", "代理机构原手机号不可用"),
    REGISTER_EXPERT_CODE("SR-005", "专家注册验证码"),
    SECONDARY_CONFIRMATION_CODE("SR-006", "二次确认验证码"),
    LOGIN_BIDDER_CODE("SL-001", "招标人登陆验证码"),
    LOGIN_SUPPLIER_CODE("SL-002", "投标人/供应商登陆验证码"),
    LOGIN_AGENCY_CODE("SL-003", "代理机构登陆验证码"),
    LOGIN_DEVOPS_CODE("SL-004", "运维登陆验证码"),
    LOGIN_EXPERT_CODE("SL-005", "专家登录"),
    UPDATE_PHONE_CODE("UP-001", "修改手机号的验证码"),
    SUPPLIER_GZ_NO("GZ-003","短信通知项目经理(无包)"),
    SUPPLIER_GZ_YES("GZ-004","供应商关注短信通知项目经理(有包)"),
    NOTICE_EXPERT("NE-001","短信通知抽取专家"),
    QXGZ("QX-001","供应商取消关注"),
    GGTS("GGTS-001","公告推送失败提示"),
    GGTSCG("GGTS-002","公告推送成功提示"),
    FTQR("FTQR","分摊确认"),
    ;



    /**
     * 类型
     */
    private String type;

    /**
     * 说明
     */
    private String msg;

    SmsTypeEnum(String type, String desc) {
        this.type = type;
        this.msg = desc;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsgc(String msg) {
        this.msg = msg;
    }

    /**
     * 根据type判断是否属于该枚举
     *
     * @param type
     * @return
     */
    public static boolean isInclude(String type) {
        boolean include = false;
        for (SmsTypeEnum e : SmsTypeEnum.values()) {
            if (e.type.equals(type)) {
                include = true;
                break;
            }
        }
        return include;
    }
}
