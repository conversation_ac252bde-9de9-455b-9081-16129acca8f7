package com.hzw.sunflower.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hzw.sunflower.controller.project.request.ReturnListConditionReq;
import com.hzw.sunflower.controller.project.response.WaitListVo;
import com.hzw.sunflower.entity.JwtUser;

/**
 * 项目表 Service接口
 *
 * <AUTHOR>
 * @version 1.0.0 2021-04-07
 */
public interface WaitRefundService {

    IPage<WaitListVo> queryReturnListCount(ReturnListConditionReq condition, JwtUser jwtUser);

    /**
     * 查询退回列表列表数量--只查询数量
     * @param condition
     * @param jwtUser
     * @return
     */
    Integer selectReturnCount(ReturnListConditionReq condition, JwtUser jwtUser);

    /**
     * 查询退回列表列表
     * @param condition
     * @param jwtUser
     * @return
     */
    IPage<WaitListVo> queryReturnList(ReturnListConditionReq condition, JwtUser jwtUser);


    /**
     * 查询退回待处理列表（待处理事项表）
     * @param condition
     * @param jwtUser
     * @return
     */
    IPage<WaitListVo> selectReturnList(ReturnListConditionReq condition, JwtUser jwtUser);

}