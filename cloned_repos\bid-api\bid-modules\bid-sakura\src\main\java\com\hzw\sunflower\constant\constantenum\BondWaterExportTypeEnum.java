package com.hzw.sunflower.constant.constantenum;

/**
 * <AUTHOR>
 * @date ：Created in 2022/07/27 9:23
 * @description：申请类型
 * @version: 1.0
 */
public enum BondWaterExportTypeEnum {

    ALL(1, "全部已收款流水"),
    NOT_RETURN(2, "已收款未退还");

    private Integer type;
    private String desc;

    BondWaterExportTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
