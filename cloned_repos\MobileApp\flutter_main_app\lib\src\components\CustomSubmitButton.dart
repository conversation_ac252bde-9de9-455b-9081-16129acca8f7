///***
/// * 提交|确定按钮
/// */

import 'package:flutter/material.dart';

import 'package:flutter_main_app/src/constants/theme.dart';
import 'package:flutter_main_app/src/utils/ScreenUtils.dart';

class CustomSubmitButton extends StatelessWidget {

  const CustomSubmitButton({
    Key key,
    @required this.text,
    this.onTap,
  }) : assert(text != null),
    super(key: key);

  final String text;
  final Function onTap;

  static const double _horizontalMargin = 20.0;
  static const double _buttonHeight = 40.0;
  static const double _fontSize = 14.0;
  static const Color _textColor = Colors.white;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: this.onTap,
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: _horizontalMargin),
        width: ScreenUtils.screenWidth - 2 * _horizontalMargin,
        height: _buttonHeight,
        decoration: BoxDecoration(
          color: themeActiveColor,
          borderRadius: BorderRadius.circular(themeBorderRadius),
        ),
        child: Center(
          child: Text(
            this.text,
            style: TextStyle(
              fontSize: _fontSize,
              color: _textColor,
            ),
          ),
        ),
      ),
    );
  }
}
