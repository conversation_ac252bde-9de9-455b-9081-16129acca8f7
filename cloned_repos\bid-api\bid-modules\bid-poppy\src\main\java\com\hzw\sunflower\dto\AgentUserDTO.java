package com.hzw.sunflower.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date ：Created in 2021/4/15 8:42
 * @description：委托联系dto
 * @modified By：`
 * @version: 1.0
 */
@ApiModel(description = "委托联系人dto")
@Data
public class AgentUserDTO {

    @ApiModelProperty(value = "委托单位id", position = 1)
    private Long companyId;

    @ApiModelProperty(value = "委托单位名称", position = 2)
    private String companyName;

    @ApiModelProperty(value = "委托用户名称", position = 3)
    private String userName;

    @ApiModelProperty(value = "委托用户id", position = 4)
    private Long userId;

    @ApiModelProperty(value = "部门id", position = 4)
    private Long departmentId;

    @ApiModelProperty(value = "委托用户联系方式", position = 5)
    private String userPhone;

    @ApiModelProperty(value = "委托合同id", position = 6)
    private String uploadFileId;

    @ApiModelProperty(value = "来源", position = 7)
    private Integer source;

    @ApiModelProperty(value = "邮箱", position = 8)
    private String email;

    @ApiModelProperty(value = "是不是管理员", position = 9)
    private Integer isAdmin;

    @ApiModelProperty(value = "角色名称", position = 10)
    private String roleName;
}
