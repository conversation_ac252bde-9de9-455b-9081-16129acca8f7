// ///***
// /// * 点击唤起系统相机的套件
// /// */

// import 'package:flutter/material.dart';
// import 'package:camera/camera.dart';

// import 'package:flutterapp/src/components/CustomCameraPreview.dart';
// import 'package:flutterapp/src/components/CustomDisplayPictureScreen.dart';

// class CustomCameraDetector extends StatefulWidget {

//   CustomCameraDetector({
//     Key key,
//     this.child,
//   }) : super(key: key);

//   final Widget child;

//   @override
//   _CustomCameraDetectorState createState() => _CustomCameraDetectorState();

// }

// class _CustomCameraDetectorState extends State<CustomCameraDetector> {

//   ///***
//   /// * 获取相机描述，取第一个
//   /// */
//   Future<CameraDescription> _getFirstCamera() async {
//     final List<CameraDescription> cameras = await availableCameras();

//     final CameraDescription firstCamera = cameras != null && cameras.isNotEmpty
//       ? cameras.first
//       : null;

//     return firstCamera;
//   }

//   @override
//   Widget build(BuildContext context) {
//     return FutureBuilder(
//       future: _getFirstCamera(),
//       builder: (BuildContext context, AsyncSnapshot snapshot) {
//         return GestureDetector(
//           onTap: () {
//             if (snapshot.hasData) {
//               Navigator.push(
//                 context,
//                 MaterialPageRoute(
//                   builder: (context) => CustomCameraPreview(
//                     camera: snapshot.data,
//                     onPictureTaken: (String path) {
//                       Navigator.push(
//                         context,
//                         MaterialPageRoute(
//                           builder: (context) => CustomDisplayPictureScreen(imagePath: path),
//                         ),
//                       );
//                       print('path: $path');
//                     },
//                   ),
//                 ),
//               );
//             }
//           },
//           child: widget.child,
//         );
//       },
//     );
//   }

// }
