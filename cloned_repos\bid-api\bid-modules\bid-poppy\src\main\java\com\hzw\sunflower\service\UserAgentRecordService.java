package com.hzw.sunflower.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hzw.sunflower.dto.UserAgentRecordDTO;
import com.hzw.sunflower.entity.UserAgentRecord;

/**
 * 用户创建代理的记录Service接口
 *
 * <AUTHOR>
 * @version 1.0.0 2021-04-14
 */
public interface UserAgentRecordService extends IService<UserAgentRecord> {

    /**
     * 根据用户id获取上一次填写的代理机构或招标人信息
     * @param userId
     * @return
     */
    UserAgentRecordDTO getByUserId(Integer userIdentity, Long userId);
   
}