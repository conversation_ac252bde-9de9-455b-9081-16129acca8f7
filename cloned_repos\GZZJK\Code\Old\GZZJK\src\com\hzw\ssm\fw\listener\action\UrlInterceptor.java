package com.hzw.ssm.fw.listener.action;


import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import com.hzw.ssm.fw.util.SysConstants;
import com.hzw.ssm.sys.system.entity.MenuEntity;
import com.hzw.ssm.sys.user.entity.UserEntity;
import com.hzw.ssm.sys.user.service.UserService;
import com.opensymphony.xwork2.ActionContext;
import com.opensymphony.xwork2.ActionInvocation;
import com.opensymphony.xwork2.interceptor.MethodFilterInterceptor;
import org.apache.commons.lang.StringUtils;
import org.apache.struts2.ServletActionContext;
import org.springframework.beans.factory.annotation.Autowired;

public class UrlInterceptor extends MethodFilterInterceptor{

	@Autowired
	private UserService userService;

	@Override
	protected String doIntercept(ActionInvocation actionInvocation){
		String result=null;
		try{
		HttpServletRequest request = ServletActionContext.getRequest();
		//获取当前用户的userid
		UserEntity user = (UserEntity) ActionContext.getContext().getSession().get("userInfo");
		if(null==user){
			result = "login";
			return result;
		}
		String userId=user.getUser_id();
		String roleId=user.getRole_id();
		String roleName=user.getRole_name();
		// 获取访问路径
		String currentURL = request.getRequestURI();
		// 获取根路径
		String contextPath = request.getContextPath();
		// 获取访问地址
		String path = currentURL.replace(contextPath, "");
		if (path == null) {
			result = actionInvocation.invoke();
		} else {
			//判断当前登录用户是否是专家
			if (SysConstants.ROLE_ID.EXPERT.equals(roleId)
					|| SysConstants.ROLE_NAME.EXPERT.equals(roleName)) {
				//针对小程序访问判断
				if(path.contains("appAction") || path.contains("applets") || path.contains("fileUploadAction")){
					return actionInvocation.invoke();
				}
				//判断访问路径是否包含在 可访问路径中
				List<String> menuUrlList = (List<String>) ActionContext.getContext().getSession().get("menuUrlList");
				for (String menuUrl : menuUrlList) {
					if (menuUrl.equals(path)) {
						return actionInvocation.invoke();
					}
				}
				return "uv";
			}
			//该判断只是针对 个人信息管理
			if ("/personalInfo/personalInit".equals(path)) {
				result = checkUrl(userId, path, result);
				if (StringUtils.isNotBlank(result)) {
					return result;
				}
				return actionInvocation.invoke();
			} else if ("/personalInfo/personalSave".equals(path)) {
				//个人信息管理 修改数据操作及恶意访问
				path = "/personalInfo/personalInit";
				result = checkUrl(userId, path, result);
				if (StringUtils.isNotBlank(result)) {
					return result;
				}
				//获取前端请求的用户id
				String requestUserId = request.getParameter("objUser.user_id");
				//比较两个用户id 防止用户串改
				if (!userId.equals(requestUserId)) {
					log.info("警告：发现疑似有用户修改user_id！操作用户：" + user.getUser_id());
					result = "person";
				} else {
					result = actionInvocation.invoke();
				}
			} else if (path.startsWith("/userinfo/")) {
				// 用户管理权限check
				String menuPath = "/userinfo/init";
				result = checkUrl(userId, menuPath, result);
				if (StringUtils.isNotBlank(result)) {
					return result;
				}
				return actionInvocation.invoke();
			} else if (path.startsWith("/roleinfo/")) {
				// 角色管理权限check
				String menuPath = "/roleinfo/init";
				result = checkUrl(userId, menuPath, result);
				if (StringUtils.isNotBlank(result)) {
					return result;
				}
				return actionInvocation.invoke();
			} else if (path.startsWith("/menuinfo/")) {
				// 菜单管理权限check
				String menuPath = "/menuinfo/toMenuMain";
				result = checkUrl(userId, menuPath, result);
				if (StringUtils.isNotBlank(result)) {
					return result;
				}
				return actionInvocation.invoke();
			} else if (path.startsWith("/systemLog/")) {
				// 系统日志权限check
				String menuPath = "/systemLog/queryLogList";
				result = checkUrl(userId, menuPath, result);
				if (StringUtils.isNotBlank(result)) {
					return result;
				}
				return actionInvocation.invoke();
			} else {
				result = actionInvocation.invoke();
			}
		}
		}catch(Exception e){
			e.printStackTrace();
		}
		return result;
	}

	public String checkUrl(String userId,String path,String result) throws Exception{
		//判断当前用户是否有访问 个人信息管理的权限
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("userId", userId);
		map.put("url", path);
		int n=userService.queryUrlByIdAndUrl(map);
		if(n==0){
			return "uv";
		} else {
			return null;
		}
	}

}
