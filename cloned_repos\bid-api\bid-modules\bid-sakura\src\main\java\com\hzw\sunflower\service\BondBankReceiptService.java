package com.hzw.sunflower.service;

import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hzw.sunflower.controller.request.BondPrintReq;
import com.hzw.sunflower.dto.NjBankTradeDetDto;
import com.hzw.sunflower.entity.BondBankReceipt;

import java.util.List;

/**
 * <p>
 * 银行回单明细 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-12
 */
public interface BondBankReceiptService extends IService<BondBankReceipt> {

    /**
     * 保存南京银行回单信息
     * @param list
     * @param bankType
     * @return
     */
    Integer saveNjBankTradeList(List<NjBankTradeDetDto> list, Integer bankType);

    /**
     * 解析南京银行回单文件
     */
    Integer dealNjBankTradeDetFile();

    /**
     * 解析南京银行回单文件新
     * @return
     */
    Integer dealNjBankTradeDetFileNew();

    /**
     * 保存中国银行回单文件
     * @param tratype
     */
    void saveChinaBankReceipt(String tratype);

    /**
     * 保存民生银行回单信息
     * @param array
     * @param type
     * @return
     */
    Integer saveCmbcTradeList(JSONArray array, Integer type);

    /**
     * 打印回单
     * @param req
     * @return
     */
    List<String> printReceipt(BondPrintReq req);
}
