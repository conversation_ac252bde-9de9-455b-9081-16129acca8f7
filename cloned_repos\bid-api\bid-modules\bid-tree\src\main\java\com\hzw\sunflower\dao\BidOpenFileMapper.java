package com.hzw.sunflower.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hzw.sunflower.controller.response.BidFileInfo;
import com.hzw.sunflower.dto.BidOpenFileDto;
import com.hzw.sunflower.entity.BidOpenFile;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2021/8/27 11:28
 * @description： 线上开标记录表文件Mapper接口
 * @version: 1.0
 */
public interface BidOpenFileMapper extends BaseMapper<BidOpenFile> {

    /**
     * 根据标段查询开标记录表文件
     * @param sectionId
     * @return
     */
    BidOpenFileDto getOpenFileBySectionId(@Param("sectionId") Long sectionId);

    /**
     * 签章文件
     * @param sectionId
     * @return
     */
    List<BidFileInfo> listBidOpenFile(@Param("sectionId")Long sectionId);
}
