INSERT INTO `sys_dict_type` (`dict_id`, `dict_name`, `dict_type`, `status`, `created_user_id`, `created_time`, `updated_user_id`, `updated_time`, `remark`, `is_delete`) VALUES ('1', '用户性别', 'sys_user_sex', '0', 'admin', '2023-07-14 16:20:48', '', NULL, '用户性别列表', NULL);
INSERT INTO `sys_dict_type` (`dict_id`, `dict_name`, `dict_type`, `status`, `created_user_id`, `created_time`, `updated_user_id`, `updated_time`, `remark`, `is_delete`) VALUES ('10', '系统状态', 'sys_common_status', '0', 'admin', '2023-07-14 16:20:48', '', NULL, '登录状态列表', NULL);
INSERT INTO `sys_dict_type` (`dict_id`, `dict_name`, `dict_type`, `status`, `created_user_id`, `created_time`, `updated_user_id`, `updated_time`, `remark`, `is_delete`) VALUES ('2', '菜单状态', 'sys_show_hide', '0', 'admin', '2023-07-14 16:20:48', '', NULL, '菜单状态列表', NULL);
INSERT INTO `sys_dict_type` (`dict_id`, `dict_name`, `dict_type`, `status`, `created_user_id`, `created_time`, `updated_user_id`, `updated_time`, `remark`, `is_delete`) VALUES ('3', '系统开关', 'sys_normal_disable', '0', 'admin', '2023-07-14 16:20:48', '', NULL, '系统开关列表', NULL);
INSERT INTO `sys_dict_type` (`dict_id`, `dict_name`, `dict_type`, `status`, `created_user_id`, `created_time`, `updated_user_id`, `updated_time`, `remark`, `is_delete`) VALUES ('6', '系统是否', 'sys_yes_no', '0', 'admin', '2023-07-14 16:20:48', '', NULL, '系统是否列表', NULL);
INSERT INTO `sys_dict_type` (`dict_id`, `dict_name`, `dict_type`, `status`, `created_user_id`, `created_time`, `updated_user_id`, `updated_time`, `remark`, `is_delete`) VALUES ('7', '通知类型', 'sys_notice_type', '0', 'admin', '2023-07-14 16:20:48', '', NULL, '通知类型列表', NULL);
INSERT INTO `sys_dict_type` (`dict_id`, `dict_name`, `dict_type`, `status`, `created_user_id`, `created_time`, `updated_user_id`, `updated_time`, `remark`, `is_delete`) VALUES ('8', '通知状态', 'sys_notice_status', '0', 'admin', '2023-07-14 16:20:48', '', NULL, '通知状态列表', NULL);
INSERT INTO `sys_dict_type` (`dict_id`, `dict_name`, `dict_type`, `status`, `created_user_id`, `created_time`, `updated_user_id`, `updated_time`, `remark`, `is_delete`) VALUES ('9', '操作类型', 'sys_oper_type', '0', 'admin', '2023-07-14 16:20:48', '', NULL, '操作类型列表', NULL);
COMMIT;

SET FOREIGN_KEY_CHECKS = 1;
