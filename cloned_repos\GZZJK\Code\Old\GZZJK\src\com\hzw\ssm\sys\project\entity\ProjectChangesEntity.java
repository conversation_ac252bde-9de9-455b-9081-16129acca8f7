package com.hzw.ssm.sys.project.entity;

import java.util.Date;

import com.hzw.ssm.fw.base.BaseEntity;

/**
 * <一句话功能简述> 项目修改信息表对应实体类
 * <功能详细描述>
 * <AUTHOR>
 * @version V1.0[产品/模块版本]
 */
public class ProjectChangesEntity extends BaseEntity {
	private static final long serialVersionUID = 1L;
	/**
	 * 主键
	 */
	private String changeId;
	/**
	 * 抽取批次
	 */
	private String decimationBatch;
	/**
	 * 原开标时间
	 */
	private Date oldBidTime;
	/**
	 * 新开标时间
	 */
	private Date newBidTime;
	/**
	 * 原开标地点
	 */
	private String oldBidAddress;
	/**
	 * 新开标地点
	 */
	private String newBidAddress;
	/**
	 * 变更理由
	 */
	private String changeReason;
	/**
	 * 变更时间
	 */
	private Date changeTime;
	/**
	 * 变更状态
	 */
	private Integer changeStatus;
	/**
	 * 变更人
	 */
	private String changeUser;
	/**
	 * 专家ID
	 */
	private String expertId;
	/**
	 * 专家姓名
	 */
	private String expertName;
	/**
	 * 专家手机号
	 */
	private String mobilePhone;
	/**
	 * 变更次数
	 */
	private Integer sort;
	/**
	 * 项目编号
	 */
	private String projectNo;
	/**
	 * 项目名称
	 */
	private String projectName;
	
	public String getExpertId() {
		return expertId;
	}
	public void setExpertId(String expertId) {
		this.expertId = expertId;
	}
	public String getExpertName() {
		return expertName;
	}
	public void setExpertName(String expertName) {
		this.expertName = expertName;
	}
	public String getMobilePhone() {
		return mobilePhone;
	}
	public void setMobilePhone(String mobilePhone) {
		this.mobilePhone = mobilePhone;
	}
	public Integer getSort() {
		return sort;
	}
	public void setSort(Integer sort) {
		this.sort = sort;
	}
	public String getProjectNo() {
		return projectNo;
	}
	public void setProjectNo(String projectNo) {
		this.projectNo = projectNo;
	}
	public String getProjectName() {
		return projectName;
	}
	public void setProjectName(String projectName) {
		this.projectName = projectName;
	}
	public String getChangeId() {
		return changeId;
	}

	public void setChangeId(String changeId) {
		this.changeId = changeId;
	}

	public String getDecimationBatch() {
		return decimationBatch;
	}

	public void setDecimationBatch(String decimationBatch) {
		this.decimationBatch = decimationBatch;
	}

	public Date getOldBidTime() {
		return oldBidTime;
	}

	public void setOldBidTime(Date oldBidTime) {
		this.oldBidTime = oldBidTime;
	}

	public Date getNewBidTime() {
		return newBidTime;
	}

	public void setNewBidTime(Date newBidTime) {
		this.newBidTime = newBidTime;
	}

	public String getOldBidAddress() {
		return oldBidAddress;
	}

	public void setOldBidAddress(String oldBidAddress) {
		this.oldBidAddress = oldBidAddress;
	}

	public String getNewBidAddress() {
		return newBidAddress;
	}

	public void setNewBidAddress(String newBidAddress) {
		this.newBidAddress = newBidAddress;
	}

	public String getChangeReason() {
		return changeReason;
	}

	public void setChangeReason(String changeReason) {
		this.changeReason = changeReason;
	}

	public Date getChangeTime() {
		return changeTime;
	}

	public void setChangeTime(Date changeTime) {
		this.changeTime = changeTime;
	}

	public Integer getChangeStatus() {
		return changeStatus;
	}

	public void setChangeStatus(Integer changeStatus) {
		this.changeStatus = changeStatus;
	}

	public String getChangeUser() {
		return changeUser;
	}

	public void setChangeUser(String changeUser) {
		this.changeUser = changeUser;
	}

	public static long getSerialversionuid() {
		return serialVersionUID;
	}
}
