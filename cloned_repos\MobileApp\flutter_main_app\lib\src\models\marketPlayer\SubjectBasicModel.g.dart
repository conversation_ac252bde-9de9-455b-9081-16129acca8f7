// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'SubjectBasicModel.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SubjectBasicModel _$SubjectBasicModelFromJson(Map<String, dynamic> json) {
  return SubjectBasicModel(
    address: json['address'] as String ?? '',
    branchCount: json['branchCount'] as int ?? 0,
    businessScope: json['xxx'] as String ?? '',
    capital: json['capital'] as String ?? '',
    contactNumber: json['telcomName'] as String ?? '',
    contactPerson: json['contractName'] as String ?? '',
    dataKey: json['dataKey'] as String,
    enterpriseType: json['type'] as String ?? '',
    legalName: json['legalName'] as String ?? '',
    operationDeadline: json['endTime'] as String ?? '',
    registerTime: json['registerTime'] as String ?? '',
    registrationAuthority: json['registrationAuthority'] as String ?? '',
    subjectCode: json['subjectCode'] as String ?? '',
    subjectID: json['subjectID'] as int,
    subjectName: json['subjectName'] as String ?? '',
    subjectTypeID: json['subjectTypeID'] as int,
    url: json['url'] as String ?? '',
    isexistSupplierCredit: json['isexistSupplierCredit'] as bool ?? false,
    isexistSupplierPerformance:
        json['isexistSupplierPerformance'] as bool ?? false,
    isexistSupplierStaff: json['isexistSupplierStaff'] as bool ?? false,
    isexistSupplierSubjectQualification:
        json['isexistSupplierSubjectQualification'] as bool ?? false,
  );
}

Map<String, dynamic> _$SubjectBasicModelToJson(SubjectBasicModel instance) =>
    <String, dynamic>{
      'address': instance.address,
      'branchCount': instance.branchCount,
      'xxx': instance.businessScope,
      'capital': instance.capital,
      'telcomName': instance.contactNumber,
      'contractName': instance.contactPerson,
      'dataKey': instance.dataKey,
      'type': instance.enterpriseType,
      'legalName': instance.legalName,
      'endTime': instance.operationDeadline,
      'registerTime': instance.registerTime,
      'registrationAuthority': instance.registrationAuthority,
      'subjectCode': instance.subjectCode,
      'subjectID': instance.subjectID,
      'subjectName': instance.subjectName,
      'subjectTypeID': instance.subjectTypeID,
      'url': instance.url,
      'isexistSupplierStaff': instance.isexistSupplierStaff,
      'isexistSupplierSubjectQualification':
          instance.isexistSupplierSubjectQualification,
      'isexistSupplierPerformance': instance.isexistSupplierPerformance,
      'isexistSupplierCredit': instance.isexistSupplierCredit,
    };
