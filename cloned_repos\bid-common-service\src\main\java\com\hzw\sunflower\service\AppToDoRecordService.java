package com.hzw.sunflower.service;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hzw.sunflower.controller.request.AppTaskCondition;
import com.hzw.sunflower.dto.AppToDoRecordDto;
import com.hzw.sunflower.entity.AppToDoRecord;
import com.hzw.sunflower.util.LoginUtil;


/**
 * <AUTHOR>
 * @date ：Created in 2025/01/15 17:24
 * @description：
 * @version: 1.0
 */
public interface AppToDoRecordService extends IService<AppToDoRecord> {

    /**
     * 分页检索
     * @param condition
     * @return
     */
    IPage<AppToDoRecordDto> getListByCondition(AppTaskCondition condition);

    /**
     * 判断是否需要保存已办数据
     * @return
     */
    Boolean checkToSave();

    /**
     * 保存pc端的已办数据
     * @param dto
     * @return
     */
    Boolean savePCToDoRecord(AppToDoRecordDto dto);


}
