package com.hzw.ssm.sys.project.entity;

import java.util.Date;

import com.hzw.ssm.expert.entity.ExpertInfoEntity;
import com.hzw.ssm.fw.base.BaseEntity;

public class ResultEntity extends BaseEntity {
	private static final long serialVersionUID = -8151736570527763583L;

	private String id;//主键id
	private String projectId;// 项目信息主键
	private String conditionId;// 抽取条件id
	private String userId;// 专家id
	private Long sort;// 抽取批次
	private Date callTime;// 通知时间
	private String reason = "";// 不参加原因
	private String qt_reason = "";//其他原因
	private Long joinStatus;// 是否参加
	private Date extractTime;// 抽取时间
	private String messageNo = "";// 短信批次
	private Date recieveTime;// 短信回复时间
	private Long deleteFlag;// 删除标识
	private ExpertInfoEntity expert;//专家信息
	private String phone;//专家手机号码
	
	private Integer is_appraise;//是否已评价
	private Date appraise_time;//评价时间
	private Integer isillegal;//是否违规
	private String illegal_file_path;//违规文件路径	
	private String illegal_file_name;//违规文件名
	private String illegal_detail;//违规详情
	private String decimationBatch;//抽取流水号
	
	private Integer callCount;// 邀请专家人数
	//发送短信主键
	private String smsId;
	//通知综合处短信主键
	private String informId;
	private Long expertWay;//记录当前专家的状态是应急抽取还是语音抽取(适用于语音抽取模块)
	//接收所有语言返回的数据集
	private String resultBody;
	
	private String userName; //用户姓名（用于指定专家）
	/**
	 * 开标时间
	 */
	private Date bidTime;
	/**
	 * 抽取时被抽取出时的专业
	 */
	private String majorselection;
	private ProjectEntity projectEntity;
	public String getId() {
		return id;
	}


	public void setId(String id) {
		this.id = id;
	}


	public String getProjectId() {
		return projectId;
	}


	public void setProjectId(String projectId) {
		this.projectId = projectId;
	}


	public String getConditionId() {
		return conditionId;
	}


	public void setConditionId(String conditionId) {
		this.conditionId = conditionId;
	}


	public String getUserId() {
		return userId;
	}


	public void setUserId(String userId) {
		this.userId = userId;
	}


	public Long getSort() {
		return sort;
	}


	public void setSort(Long sort) {
		this.sort = sort;
	}


	public Date getCallTime() {
		return callTime;
	}


	public void setCallTime(Date callTime) {
		this.callTime = callTime;
	}


	public String getReason() {
		return reason;
	}


	public void setReason(String reason) {
		this.reason = reason;
	}


	public String getQt_reason() {
		return qt_reason;
	}


	public void setQt_reason(String qt_reason) {
		this.qt_reason = qt_reason;
	}


	public Long getJoinStatus() {
		return joinStatus;
	}


	public void setJoinStatus(Long joinStatus) {
		this.joinStatus = joinStatus;
	}


	public Date getExtractTime() {
		return extractTime;
	}


	public void setExtractTime(Date extractTime) {
		this.extractTime = extractTime;
	}


	public String getMessageNo() {
		return messageNo;
	}


	public void setMessageNo(String messageNo) {
		this.messageNo = messageNo;
	}


	public Date getRecieveTime() {
		return recieveTime;
	}


	public void setRecieveTime(Date recieveTime) {
		this.recieveTime = recieveTime;
	}


	public Long getDeleteFlag() {
		return deleteFlag;
	}


	public void setDeleteFlag(Long deleteFlag) {
		this.deleteFlag = deleteFlag;
	}


	public ExpertInfoEntity getExpert() {
		return expert;
	}


	public void setExpert(ExpertInfoEntity expert) {
		this.expert = expert;
	}


	public String getPhone() {
		return phone;
	}


	public void setPhone(String phone) {
		this.phone = phone;
	}


	public Integer getIs_appraise() {
		return is_appraise;
	}


	public void setIs_appraise(Integer is_appraise) {
		this.is_appraise = is_appraise;
	}


	public Date getAppraise_time() {
		return appraise_time;
	}


	public void setAppraise_time(Date appraise_time) {
		this.appraise_time = appraise_time;
	}


	public Integer getIsillegal() {
		return isillegal;
	}


	public void setIsillegal(Integer isillegal) {
		this.isillegal = isillegal;
	}


	public String getIllegal_file_path() {
		return illegal_file_path;
	}


	public void setIllegal_file_path(String illegal_file_path) {
		this.illegal_file_path = illegal_file_path;
	}


	public String getIllegal_file_name() {
		return illegal_file_name;
	}


	public void setIllegal_file_name(String illegal_file_name) {
		this.illegal_file_name = illegal_file_name;
	}


	public String getIllegal_detail() {
		return illegal_detail;
	}


	public void setIllegal_detail(String illegal_detail) {
		this.illegal_detail = illegal_detail;
	}


	public Integer getCallCount() {
		return callCount;
	}


	public void setCallCount(Integer callCount) {
		this.callCount = callCount;
	}

	public String getDecimationBatch() {
		return decimationBatch;
	}


	public void setDecimationBatch(String decimationBatch) {
		this.decimationBatch = decimationBatch;
	}



	public Long getExpertWay() {
		return expertWay;
	}


	public void setExpertWay(Long expertWay) {
		this.expertWay = expertWay;
	}

	

	public String getResultBody() {
		return resultBody;
	}


	public void setResultBody(String resultBody) {
		this.resultBody = resultBody;
	}


	public String getSmsId() {
		return smsId;
	}


	public void setSmsId(String smsId) {
		this.smsId = smsId;
	}


	public String getInformId() {
		return informId;
	}


	public void setInformId(String informId) {
		this.informId = informId;
	}


	@Override
	public String toString() {
		return "ResultEntity [id=" + id + ", projectId=" + projectId + ", conditionId=" + conditionId + ", userId="
				+ userId + ", sort=" + sort + ", callTime=" + callTime + ", reason=" + reason + ", qt_reason="
				+ qt_reason + ", joinStatus=" + joinStatus + ", extractTime=" + extractTime + ", messageNo=" + messageNo
				+ ", recieveTime=" + recieveTime + ", deleteFlag=" + deleteFlag + ", expert=" + expert + ", phone="
				+ phone + ", is_appraise=" + is_appraise + ", appraise_time=" + appraise_time + ", isillegal="
				+ isillegal + ", illegal_file_path=" + illegal_file_path + ", illegal_file_name=" + illegal_file_name
				+ ", illegal_detail=" + illegal_detail + ", callCount=" + callCount + ", decimationbatch="
				+ decimationBatch + "]";
	}


	public ProjectEntity getProjectEntity() {
		return projectEntity;
	}


	public void setProjectEntity(ProjectEntity projectEntity) {
		this.projectEntity = projectEntity;
	}


	public String getUserName() {
		return userName;
	}


	public void setUserName(String userName) {
		this.userName = userName;
	}


	public Date getBidTime() {
		return bidTime;
	}


	public void setBidTime(Date bidTime) {
		this.bidTime = bidTime;
	}


	public String getMajorselection() {
		return majorselection;
	}


	public void setMajorselection(String majorselection) {
		this.majorselection = majorselection;
	}
	
	
}
