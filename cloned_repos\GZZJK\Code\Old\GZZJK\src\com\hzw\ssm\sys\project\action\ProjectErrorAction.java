package com.hzw.ssm.sys.project.action;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import javax.servlet.http.HttpSession;

import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.util.Region;
import org.apache.struts2.convention.annotation.Action;
import org.apache.struts2.convention.annotation.Namespace;
import org.apache.struts2.convention.annotation.ParentPackage;
import org.apache.struts2.convention.annotation.Result;
import org.apache.struts2.convention.annotation.Results;
import org.springframework.beans.factory.annotation.Autowired;

import com.hzw.ssm.expert.entity.ExpertInfoEntity;
import com.hzw.ssm.fw.base.BaseAction;
import com.hzw.ssm.fw.util.XLSExport;
import com.hzw.ssm.sys.project.entity.ApplyRecordEntity;
import com.hzw.ssm.sys.project.entity.ConditionEntity;
import com.hzw.ssm.sys.project.entity.ProjectAuditEntity;
import com.hzw.ssm.sys.project.entity.ProjectEntity;
import com.hzw.ssm.sys.project.entity.ResultEntity;
import com.hzw.ssm.sys.project.service.ProjectService;
import com.hzw.ssm.sys.user.entity.UserEntity;

/**
 * 待抽取项目
 * 
 * <AUTHOR>
 * 
 */
@Namespace("/errorProject")
@ParentPackage(value = "default")
@Results( { @Result(name = "toErrorProjectWaitList", location = "/jsp/project/projectErrorWaitList.jsp")})
public class ProjectErrorAction extends BaseAction {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	@Autowired
	private ProjectService projectService;

	private ProjectEntity projectEntity;
	
	private ProjectAuditEntity projectAuditEntity;

	/** 抽取条件 */
	private ConditionEntity conditionEntity;

	private List<ProjectEntity> projectList;
	
	private List<ProjectAuditEntity> projectAuditList;

	private List<ConditionEntity> conditionList;
	/** 抽取条件 */
	private ConditionEntity conEntity;
	private ProjectEntity project;
	private List<ResultEntity> resultList;//专家抽取结果列表
	private List<List<ResultEntity>> extractList;//不同轮次抽取结果集
	private List<ExpertInfoEntity> expertList;//专家信息列表
	List<UserEntity> userList;
	private List<UserEntity> operatorList;//经办人列表
	private Long agree;//同意参加的人数
	private String isSave;
	private String majorStr;//专业选择 
	private String applyFlag;//申请抽取标识
	private ResultEntity result;
	private ApplyRecordEntity apply;
	private String isFill;         // 抽取标识
	
	@Action("queryErrorProjectList")
	public String queryErrorProjectList() {
		this.context();
		// 当前session
		HttpSession session = getRequest().getSession();
		// 当前用户信息
		UserEntity user = (UserEntity) session.getAttribute("userInfo");
		try {
			if (null == projectEntity){
				projectEntity = new ProjectEntity();
			}
			if(projectEntity.getBidStartTime()==null&&projectEntity.getBidEndTime()==null){
				Calendar ca = Calendar.getInstance();
				int year = ca.get(Calendar.YEAR);
				int month = ca.get(Calendar.MONTH)+1;
				String str = year+"-"+month+"-01 00:00:00";
				SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
				Date now = sdf.parse(str);
				projectEntity.setBidEndTime(new Date());
				projectEntity.setBidStartTime(now);
			}
			projectEntity.setPage(this.getPage());
			//projectEntity.setStas(SysConstants.PROJECT_STATUS.WAITLIST);
			projectList = projectService.queryPageByErrorProjectList(projectEntity, user);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return "toErrorProjectWaitList";
	}
	
	/**
	 * 导出抽取记录Excel
	 * @return
	 */
	@Action("exportErroRecordExcel")
	public void exportErroRecordExcel() {
		
		this.context();
		//projectEntity.setPage(this.getPage());
		this.getResponse().setContentType("text/html;charset=utf-8");
		
		//导出用到的数据
		projectList = projectService.queryPageByErrorProjectList(projectEntity, null);	
		SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm");

		List<ProjectEntity> expertInfoList = projectService.queryErrorProjectList2(projectEntity, null);			
		Integer lastIndex = 1;         // 最后行数
		String fileName = "专家抽取异常记录表.xls";
		// 创建 EXCEL
		XLSExport e = new XLSExport(this.getResponse(), fileName);
		HSSFSheet sheet = e.getSheet();
		e.createRow(0);
		e.setOne(0,16,50,"专家抽取异常记录表");
		e.setOne(1,16,50, "");
		e.setOne(2,16,50, "");
		e.setOne(3,16,50, "");
		e.setOne(4,16,50, "");
		e.setOne(5,16,50, "");
		e.setOne(6,16,50, "");
		sheet.addMergedRegion(new Region(0, (short) 0, 0, (short) (6)));
		// 列名
		e.createRow(1);
		e.setTwo(0,10,30f,2000, "序号");
		e.setTwo(1,10,30f,7500, "项目名称");
		e.setTwo(2,10,30f,4000, "负责人");
		e.setTwo(3,10,30f,7500, "业务处室");
		e.setTwo(4,10,30f,5000, "开标日期");
		e.setTwo(5,10,30f,3700, "状态");
		e.setTwo(6,10,30f,5300, "抽取批次");
		// 存在数据
		if (null != expertInfoList && !expertInfoList.isEmpty()) 
		{
			lastIndex += expertInfoList.size();
			for (int i = 0; i < expertInfoList.size(); i++) {
				ProjectEntity expertInfo = expertInfoList.get(i);
				e.createRow(i + 2);
				e.setCel(0,10,30f, String.valueOf(i+1));
				e.setCel(1,10,30f, expertInfo.getProjectName());
				e.setCel(2,10,30f, expertInfo.getManager());
				e.setCel(3,10,30f, expertInfo.getDepartment());
				e.setCel(4,10,30f, null == expertInfo.getBidTime() ? "" : format.format(expertInfo.getBidTime()));
				e.setCel(5,10,30f, "异常");
				e.setCel(6,10,30f, expertInfo.getDecimationBatch());
			}
		}else{// 不存在数据
			lastIndex++;
			e.createRow(2);
			e.setCell(0, "没有查询到数据");
			e.setCell(1,"");
			e.setCell(2,"");
			e.setCell(3,"");
			e.setCell(4,"");
			e.setCell(5,"");
			e.setCell(6,"");
			sheet.addMergedRegion(new Region(2, (short) 0, 2,(short) (6)));
		}
		
		e.exportXLS();
		
	}

	
	public ProjectEntity getProjectEntity() {
		return projectEntity;
	}

	public void setProjectEntity(ProjectEntity projectEntity) {
		this.projectEntity = projectEntity;
	}

	public List<ProjectEntity> getProjectList() {
		return projectList;
	}

	public void setProjectList(List<ProjectEntity> projectList) {
		this.projectList = projectList;
	}

	public ConditionEntity getConditionEntity() {
		return conditionEntity;
	}

	public void setConditionEntity(ConditionEntity conditionEntity) {
		this.conditionEntity = conditionEntity;
	}

	public List<ConditionEntity> getConditionList() {
		return conditionList;
	}

	public void setConditionList(List<ConditionEntity> conditionList) {
		this.conditionList = conditionList;
	}

	public ConditionEntity getConEntity() {
		return conEntity;
	}

	public void setConEntity(ConditionEntity conEntity) {
		this.conEntity = conEntity;
	}

	public ProjectEntity getProject() {
		return project;
	}

	public void setProject(ProjectEntity project) {
		this.project = project;
	}

	public List<ResultEntity> getResultList() {
		return resultList;
	}

	public void setResultList(List<ResultEntity> resultList) {
		this.resultList = resultList;
	}

	public List<List<ResultEntity>> getExtractList() {
		return extractList;
	}

	public void setExtractList(List<List<ResultEntity>> extractList) {
		this.extractList = extractList;
	}

	public List<ExpertInfoEntity> getExpertList() {
		return expertList;
	}

	public void setExpertList(List<ExpertInfoEntity> expertList) {
		this.expertList = expertList;
	}

	public Long getAgree() {
		return agree;
	}

	public void setAgree(Long agree) {
		this.agree = agree;
	}

	public List<UserEntity> getUserList() {
		return userList;
	}

	public void setUserList(List<UserEntity> userList) {
		this.userList = userList;
	}

	public List<UserEntity> getOperatorList() {
		return operatorList;
	}

	public void setOperatorList(List<UserEntity> operatorList) {
		this.operatorList = operatorList;
	}

	public String getIsSave() {
		return isSave;
	}

	public void setIsSave(String isSave) {
		this.isSave = isSave;
	}

	public String getMajorStr() {
		return majorStr;
	}

	public void setMajorStr(String majorStr) {
		this.majorStr = majorStr;
	}

	public String getApplyFlag() {
		return applyFlag;
	}

	public void setApplyFlag(String applyFlag) {
		this.applyFlag = applyFlag;
	}

	public ResultEntity getResult() {
		return result;
	}

	public void setResult(ResultEntity result) {
		this.result = result;
	}

	public ApplyRecordEntity getApply() {
		return apply;
	}

	public void setApply(ApplyRecordEntity apply) {
		this.apply = apply;
	}

	public String getIsFill() {
		return isFill;
	}

	public void setIsFill(String isFill) {
		this.isFill = isFill;
	}

	public ProjectAuditEntity getProjectAuditEntity() {
		return projectAuditEntity;
	}

	public void setProjectAuditEntity(ProjectAuditEntity projectAuditEntity) {
		this.projectAuditEntity = projectAuditEntity;
	}

	public List<ProjectAuditEntity> getProjectAuditList() {
		return projectAuditList;
	}

	public void setProjectAuditList(List<ProjectAuditEntity> projectAuditList) {
		this.projectAuditList = projectAuditList;
	}
	
}
