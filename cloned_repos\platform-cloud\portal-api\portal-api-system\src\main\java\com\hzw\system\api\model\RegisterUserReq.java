package com.hzw.system.api.model;

import lombok.Data;

import java.io.Serializable;

/**
 * 用户表 请求实体
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
public class RegisterUserReq implements Serializable {


    /**
     * id
     */
    private String id;


    /**
     * 图形验证码
     */
    private String captcha;

    /**
     * 手机验证码
     */
    private String phoneCaptcha;

    /**
     * 用户身份 1招标人2.代理机构3投标人/供应商
     */
    private Integer identity;

    /**
     * 用户名
     */
    private String userName;

    /**
     * 用户手机
     */
    private String userPhone;

    /**
     * 用户身份证
     */
    private String identityNumber;

    /**
     * 电子邮箱
     */
    private String email;

    /**
     * 密码
     */
    private String password;


}
