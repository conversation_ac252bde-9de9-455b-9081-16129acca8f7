package com.hzw.sunflower.controller.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ApiModel(description = "供应商所在公司参与的所有项目")
public class MyCompanyProjectVo implements Serializable {

    @ApiModelProperty("项目id")
    private Long projectId;

    @ApiModelProperty("标段id")
    private Long sectionId;

    @ApiModelProperty("项目编号")
    private String projectNum;

    @ApiModelProperty("项目名称")
    private String projectName;

    @ApiModelProperty("包段编号")
    private Integer packageNumber;

    @ApiModelProperty("包名称")
    private String sectionName;

    @ApiModelProperty("项目负责人")
    private String projectChargeName;

    @ApiModelProperty("项目负责人所属处室")
    private String departmentName;

    @ApiModelProperty("处室id")
    private Long departmentId;

    @ApiModelProperty("供应商联系人")
    private String vendorName;

    @ApiModelProperty("供应商手机号")
    private String vendorPhone;

    @ApiModelProperty(value = "初始项目id")
    private Long  baseProjectId;

    @ApiModelProperty(value = "0 : 未重新招标 1 : 已重新招标")
    private  Integer reTender;

    @ApiModelProperty(value = "是否划分标段包")
    private Integer packageSegmentStatus;

    @ApiModelProperty(value = "0：标段，1：包")
    private Integer bidType;

    @ApiModelProperty(value = "轮次")
    private Integer bidRound;

    @ApiModelProperty(value = "包件状态Code")
    private String packagesStatus;

    @ApiModelProperty(value = "包件状态Code")
    private List<String> packagesStatusCode;

    @ApiModelProperty(value = "跳转权限（1 可跳转 2 不可跳转）")
    private Integer rightCode;
}
