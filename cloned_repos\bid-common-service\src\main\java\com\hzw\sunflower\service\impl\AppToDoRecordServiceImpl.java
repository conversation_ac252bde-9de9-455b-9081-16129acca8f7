package com.hzw.sunflower.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzw.sunflower.controller.request.AppTaskCondition;
import com.hzw.sunflower.controller.response.ProjectInfoVo;
import com.hzw.sunflower.dao.AppToDoRecordMapper;
import com.hzw.sunflower.dto.AppToDoRecordDto;
import com.hzw.sunflower.entity.AppToDoRecord;
import com.hzw.sunflower.security.utils.SecurityUtils;
import com.hzw.sunflower.service.AppToDoRecordService;
import com.hzw.sunflower.util.LoginUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;


/**
 * <AUTHOR>
 * @date ：Created in 2021/8/27 13:51
 * @description：
 * @version: 1.0
 */
@Service
public class AppToDoRecordServiceImpl extends ServiceImpl<AppToDoRecordMapper, AppToDoRecord> implements AppToDoRecordService {

    @Value("${login.token-key-type:}")
    private String tokenKey;

    /**
     * 分页检索
     * @param condition
     * @return
     */
    @Override
    public IPage<AppToDoRecordDto> getListByCondition(AppTaskCondition condition) {
        IPage<AppToDoRecordDto> page = condition.buildPage();
        page.setCurrent(condition.getPage());
        page.setSize(condition.getPageSize());

        condition.setCreatedUserId(SecurityUtils.getJwtUser().getUserId());
        condition.setDepartId(SecurityUtils.getJwtUser().getUser().getDepartId());
        long pageNum = condition.getPage();
        long pageSize = condition.getPageSize();
        long startRow = (pageNum - 1) * pageSize;
        long rowCount = 0L;
      //  IPage<AppToDoRecordDto> listByCondition = this.baseMapper.getListByCondition(page, condition);
        List<AppToDoRecordDto> listByCondition = this.baseMapper.queryListByCondition(condition);
        List<AppToDoRecordDto> appToDoRecordDtoList = new ArrayList<>();
        for (AppToDoRecordDto record : listByCondition) {
            if(null != condition.getKeyWords() && !"".equals(condition.getKeyWords())) {
                if (null != record.getProjectId()) {
                    // 过滤项目名称相同的数据
                    if (record.getPurchaseNumber().contains(condition.getKeyWords()) || record.getPurchaseName().contains(condition.getKeyWords())) {
                        rowCount++;
                        if (rowCount > startRow && appToDoRecordDtoList.size() < pageSize) {
                            appToDoRecordDtoList.add(record);
                        }
                    }
                } else {
                    // 取taskdata里面的数据比较
                    String taskData = record.getTaskData();
                    if(null != taskData) {
                        JSONObject jsonObject = JSON.parseObject(taskData);
                        String projectVOList = jsonObject.getString("projectVOList");
                        if (null != projectVOList) {
                            JSONArray projectVOListJson = JSONArray.parseArray(projectVOList);
                            List<ProjectInfoVo> personList = projectVOListJson.toJavaList(ProjectInfoVo.class);
                            for (ProjectInfoVo projectVo : personList) {
                                if((null != projectVo.getProjectName() && projectVo.getProjectName().contains(condition.getKeyWords())) ||
                                        (null != projectVo.getPurchaseNumber() && projectVo.getPurchaseNumber().contains(condition.getKeyWords()))){
                                    rowCount++;
                                    if (rowCount > startRow && appToDoRecordDtoList.size() < pageSize) {
                                        appToDoRecordDtoList.add(record);
                                    }
                                    break;
                                }
                            }
                        }

                    }
                }
            }else {
                appToDoRecordDtoList.add(record);
            }
        }
        page.setRecords(appToDoRecordDtoList);
        // 设置总条数
        page.setTotal(rowCount);
        return page;
    }

    /**
     * 判断是否需要保存已办数据
     * @return
     */
    @Override
    public Boolean checkToSave() {
        return StringUtils.isBlank(tokenKey);
    }


    @Override
    public Boolean savePCToDoRecord(AppToDoRecordDto dto) {
        Boolean flag = checkToSave();
        if(flag){
            AppToDoRecord record = new AppToDoRecord();
            BeanUtil.copyProperties(dto,record);
            record.setCreatedUserId(LoginUtil.getJwtUser().getUser().getId());
            record.setCreatedUserDepartId(LoginUtil.getJwtUser().getUser().getDepartId());
            String dateJson = JSONObject.toJSONString(dto.getDto());
            record.setTaskData(dateJson);
            // PC端默认为2
            record.setEquipment(2);
            return save(record);
        }
        return false;
    }
}
