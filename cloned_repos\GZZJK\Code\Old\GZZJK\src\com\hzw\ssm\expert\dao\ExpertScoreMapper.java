package com.hzw.ssm.expert.dao;

import java.util.List;

import com.hzw.ssm.expert.entity.ExpertScoreEntity;

public interface ExpertScoreMapper {

	/**
	 * 添加专家扣分详情记录
	 * @param majorEntity
	 */
	public void addExpertScoreRecords(ExpertScoreEntity expertScore);
	
	/**
	 * 定时器更新专家信息
	 * 函数功能描述：TODO
	 * @param expertScore
	 */
	public void updateExpertScoreRecords(ExpertScoreEntity expertScore);
	
	/**
	 * 根据userId查询当前专家出现次数
	 * 函数功能描述：TODO
	 * @return
	 */
	List<ExpertScoreEntity> queryExpertAppear(String userId);
}
