#!/usr/bin/env python3
"""
脚本功能：
1. 读取repos_data.json和repos_data1.json文件
2. 提取所有ssh_url_to_repo地址
3. 克隆所有仓库及其所有分支
"""

import json
import os
import subprocess
import sys
from pathlib import Path
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('clone_repos.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

def read_json_file(file_path):
    """读取JSON文件并返回数据"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        logger.info(f"成功读取文件: {file_path}, 包含 {len(data)} 个仓库")
        return data
    except FileNotFoundError:
        logger.error(f"文件不存在: {file_path}")
        return []
    except json.JSONDecodeError as e:
        logger.error(f"JSON解析错误 {file_path}: {e}")
        return []
    except Exception as e:
        logger.error(f"读取文件错误 {file_path}: {e}")
        return []

def extract_ssh_urls(json_files):
    """从JSON文件中提取所有ssh_url_to_repo"""
    ssh_urls = []
    repo_names = []
    
    for file_path in json_files:
        data = read_json_file(file_path)
        for repo in data:
            if 'ssh_url_to_repo' in repo:
                ssh_url = repo['ssh_url_to_repo']
                repo_name = repo.get('name', 'unknown')
                ssh_urls.append(ssh_url)
                repo_names.append(repo_name)
                logger.info(f"找到仓库: {repo_name} -> {ssh_url}")
    
    return ssh_urls, repo_names

def save_urls_to_file(ssh_urls, repo_names, output_file='ssh_urls.txt'):
    """保存SSH URLs到文件"""
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            for i, (url, name) in enumerate(zip(ssh_urls, repo_names), 1):
                f.write(f"{i}. {name}: {url}\n")
        logger.info(f"SSH URLs已保存到: {output_file}")
    except Exception as e:
        logger.error(f"保存URLs失败: {e}")

def run_command(command, cwd=None):
    """执行命令并返回结果"""
    try:
        result = subprocess.run(
            command, 
            shell=True, 
            cwd=cwd,
            capture_output=True, 
            text=True, 
            encoding='utf-8'
        )
        return result.returncode == 0, result.stdout, result.stderr
    except Exception as e:
        logger.error(f"执行命令失败: {command}, 错误: {e}")
        return False, "", str(e)

def clone_repository(ssh_url, repo_name, base_dir='cloned_repos'):
    """克隆单个仓库及其所有分支"""
    logger.info(f"开始克隆仓库: {repo_name}")
    
    # 创建基础目录
    Path(base_dir).mkdir(exist_ok=True)
    repo_dir = os.path.join(base_dir, repo_name)
    
    # 如果目录已存在，先删除
    if os.path.exists(repo_dir):
        logger.warning(f"目录已存在，跳过: {repo_dir}")
        return True
    
    # 克隆仓库
    clone_cmd = f"git clone {ssh_url} {repo_dir}"
    success, stdout, stderr = run_command(clone_cmd)
    
    if not success:
        logger.error(f"克隆失败 {repo_name}: {stderr}")
        return False
    
    logger.info(f"成功克隆仓库: {repo_name}")
    
    # 获取所有远程分支
    success, stdout, stderr = run_command("git branch -r", cwd=repo_dir)
    if not success:
        logger.error(f"获取远程分支失败 {repo_name}: {stderr}")
        return False
    
    # 解析远程分支
    remote_branches = []
    for line in stdout.strip().split('\n'):
        line = line.strip()
        if line and not line.startswith('origin/HEAD'):
            branch = line.replace('origin/', '')
            if branch:
                remote_branches.append(branch)
    
    logger.info(f"发现 {len(remote_branches)} 个远程分支: {remote_branches}")
    
    # 检出所有远程分支
    for branch in remote_branches:
        if branch == 'master' or branch == 'main':
            continue  # 主分支已经检出
        
        checkout_cmd = f"git checkout -b {branch} origin/{branch}"
        success, stdout, stderr = run_command(checkout_cmd, cwd=repo_dir)
        
        if success:
            logger.info(f"成功检出分支: {branch}")
        else:
            logger.warning(f"检出分支失败 {branch}: {stderr}")
    
    # 回到主分支
    main_branch = 'master' if 'master' in remote_branches else 'main'
    run_command(f"git checkout {main_branch}", cwd=repo_dir)
    
    return True

def main():
    """主函数"""
    logger.info("开始执行仓库克隆脚本")
    
    # JSON文件列表
    json_files = ['repos_data.json', 'repos_data1.json']
    
    # 检查文件是否存在
    existing_files = [f for f in json_files if os.path.exists(f)]
    if not existing_files:
        logger.error("未找到任何JSON文件")
        return
    
    logger.info(f"找到JSON文件: {existing_files}")
    
    # 提取SSH URLs
    ssh_urls, repo_names = extract_ssh_urls(existing_files)
    
    if not ssh_urls:
        logger.error("未找到任何SSH URL")
        return
    
    logger.info(f"总共找到 {len(ssh_urls)} 个仓库")
    
    # 保存URLs到文件
    save_urls_to_file(ssh_urls, repo_names)
    
    # 询问是否继续克隆
    print(f"\n找到 {len(ssh_urls)} 个仓库，是否继续克隆？(y/n): ", end='')
    choice = input().strip().lower()
    
    if choice != 'y':
        logger.info("用户取消克隆操作")
        return
    
    # 克隆所有仓库
    success_count = 0
    failed_repos = []
    
    for ssh_url, repo_name in zip(ssh_urls, repo_names):
        try:
            if clone_repository(ssh_url, repo_name):
                success_count += 1
            else:
                failed_repos.append(repo_name)
        except Exception as e:
            logger.error(f"克隆仓库异常 {repo_name}: {e}")
            failed_repos.append(repo_name)
    
    # 输出统计信息
    logger.info(f"\n克隆完成统计:")
    logger.info(f"成功: {success_count}")
    logger.info(f"失败: {len(failed_repos)}")
    
    if failed_repos:
        logger.info(f"失败的仓库: {', '.join(failed_repos)}")

if __name__ == "__main__":
    main()
