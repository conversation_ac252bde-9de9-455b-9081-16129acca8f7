package com.hzw.sunflower.controller.request;

import com.hzw.sunflower.entity.BidDocColumn;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @datetime 2024/06/27 8:53
 * @description: 编制工具入参
 * @version: 1.0
 */
@Data
public class BidDocCompileReq {

    @ApiModelProperty("编制文件id")
    private Long docId;

    @ApiModelProperty("编制文件字段值列表")
    private List<BidDocColumn> columnList;



}
