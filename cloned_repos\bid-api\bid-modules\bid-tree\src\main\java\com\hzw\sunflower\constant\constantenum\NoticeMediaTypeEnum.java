package com.hzw.sunflower.constant.constantenum;

/**
 * <AUTHOR>
 * @date ：Created in 2021/7/14 10:51
 * @description：招标公告类型
 * @version: 1.0
 */
public enum NoticeMediaTypeEnum {

    PUBLIC_MEDIA(1, "公共媒体"),

    PRIVATE_MEDIA(2, "私有媒体"),

    NOTICE_MEDIA(3,"公告自有媒体");

    NoticeMediaTypeEnum(Integer value, String name){
        this.value = value;
        this.name = name;
    }

    private Integer value;

    private String name;

    public Integer getValue() {
        return value;
    }

    public String getName() {
        return name;
    }
}
