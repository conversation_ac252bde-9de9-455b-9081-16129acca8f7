#!/bin/sh
#cp TEMPLATE FILE
docker stop bid-system-server || true
docker rm bid-system-server || true
docker rmi registry.cn-hangzhou.aliyuncs.com/hzw-jtcc/jtcc:bid-system-server-1.0.0 || true
docker pull registry.cn-hangzhou.aliyuncs.com/hzw-jtcc/jtcc:bid-system-server-1.0.0
docker run -itd --net=host  --log-opt max-size=100m --log-opt max-file=3  --name bid-system-server -v /home/<USER>/plan1/bid-cloud/config/bid-system-server:/hzw/system/config -v /data/log:/hzw/system/logs  registry.cn-hangzhou.aliyuncs.com/hzw-jtcc/jtcc:bid-system-server-1.0.0
