///***
/// * 首页和底部导航
/// */

import 'package:flutter/material.dart';

import 'package:flutter_main_app/serviceLocator.dart';
import 'package:flutter_main_app/src/constants/theme.dart';
import 'package:flutter_main_app/src/utils/ScreenUtils.dart';
import 'package:flutter_main_app/src/fonts/fonts.dart';

import 'package:flutter_main_app/src/widgets/home/<USER>';
import 'package:flutter_main_app/src/widgets/info/InfoIndexWidget.dart';
import 'package:flutter_main_app/src/widgets/market/MarketIndexWidget.dart';
import 'package:flutter_main_app/src/widgets/datareport/DataReportIndexWidget.dart';
import 'package:flutter_main_app/src/widgets/profile/ProfileIndexWidget.dart';

class HomePageWidget extends StatefulWidget {
  const HomePageWidget({Key key}) : super(key: key);

  /// * 路由名称
  static const String _routeName = 'HomePage';
  static String get routeName => _routeName;

  @override
  _HomePageWidget createState() => _HomePageWidget();
}

///***
/// * Stateful Logic
/// */
class _HomePageWidget extends State<HomePageWidget> {  
  // 当前选中的底部导航栏
  int _selectIndex = 0;
  // 底部导航对应的所有Widgets
  final _navWidgets = [
    HomePageMainWidget(),
    InfoIndexWidget(),
    MarketIndexWidget(),
    DataReportIndexWidget(),
    ProfileIndexWidget()
  ];

  @override
  void initState() {
    super.initState();

    /// * 设置 service locator
    setupInfoServiceLocator();
  }

  @override
  Widget build(BuildContext context) {
    ScreenUtils.getInstance()..init(context);

    // 底部导航栏没有选中的颜色
    Color _bottomNavitionIconNormalColor = Color(0xFFAEAEAE);
    // 底部导航栏选中的颜色
    Color _bottomNatitionTextSelectedColor = themeActiveColor;

    return Scaffold(
      body: _navWidgets[_selectIndex],
      bottomNavigationBar: BottomNavigationBar(
        currentIndex: _selectIndex,
        onTap: (int index) {
          setState(() {
            _selectIndex = index;
          });
        },
        type: BottomNavigationBarType.fixed,
        selectedItemColor: _bottomNatitionTextSelectedColor,
        iconSize: 24,
        items: [
          BottomNavigationBarItem(
            icon: Icon(
              FontsHelper.homeIconData,
              color: _bottomNavitionIconNormalColor,
            ),
            activeIcon: Icon(
              FontsHelper.homeIconData,
              color: _bottomNatitionTextSelectedColor,
            ),
            title: Text(
              '首页',
              style: TextStyle(
                fontSize: 10
              ),
            ) 
          ),
          BottomNavigationBarItem(
            icon: Icon(
              FontsHelper.sucscriptionIconData,
              color: _bottomNavitionIconNormalColor,
            ),
            activeIcon: Icon(
              FontsHelper.sucscriptionIconData,
              color: _bottomNatitionTextSelectedColor,
            ),
            title: Text(
              '信息订阅',
              style: TextStyle(
                fontSize: 10
              ),
            ) 
          ),
          BottomNavigationBarItem(
            icon: Icon(
              FontsHelper.marketIconData,
              color: _bottomNavitionIconNormalColor,
            ),
            activeIcon: Icon(
              FontsHelper.marketIconData,
              color: _bottomNatitionTextSelectedColor,
            ),
            title: Text(
              '业主信息',
              style: TextStyle(
                fontSize: 10
              ),
            ) 
          ),
          BottomNavigationBarItem(
            icon: Icon(
              FontsHelper.dataReportIconData,
              color: _bottomNavitionIconNormalColor,
            ),
            activeIcon: Icon(
              FontsHelper.dataReportIconData,
              color: _bottomNatitionTextSelectedColor,
            ),
            title: Text(
              '数据报告',
              style: TextStyle(
                fontSize: 10
              ),
            ) 
          ),
          BottomNavigationBarItem(
            icon: Icon(
              FontsHelper.profileIconData,
              color: _bottomNavitionIconNormalColor,
            ),
            activeIcon: Icon(
              FontsHelper.profileIconData,
              color: _bottomNatitionTextSelectedColor,
            ),
            title: Text(
              '我的',
              style: TextStyle(
                fontSize: 10
              ),
            ) 
          ),
        ],
      ),
    );
  }
}
