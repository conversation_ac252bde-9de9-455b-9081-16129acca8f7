package com.hzw.ssm.sys.project.entity;

import java.util.Date;
import java.util.List;

import com.hzw.ssm.fw.base.BaseEntity;

public class ConditionRecordEntity extends BaseEntity {
	private static final long serialVersionUID = -8151736570527763583L;

	private String id;//主键
	private String projectId;//项目Id
	private Integer oldTotal;//原抽取人数
	private Integer newTotal;//修改后抽取人数
	private Integer person;//评标委员人数
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public String getProjectId() {
		return projectId;
	}
	public void setProjectId(String projectId) {
		this.projectId = projectId;
	}
	public Integer getOldTotal() {
		return oldTotal;
	}
	public void setOldTotal(Integer oldTotal) {
		this.oldTotal = oldTotal;
	}
	public Integer getNewTotal() {
		return newTotal;
	}
	public void setNewTotal(Integer newTotal) {
		this.newTotal = newTotal;
	}
	public Integer getPerson() {
		return person;
	}
	public void setPerson(Integer person) {
		this.person = person;
	}
}
