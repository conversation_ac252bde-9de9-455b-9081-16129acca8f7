package com.hzw.sunflower.controller.response;

import com.hzw.sunflower.common.BaseBean;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * Created by pj on 2021/05/17
 */
@Data
public class DepartmentInfoVo extends BaseBean implements Serializable {
    /**
     * 主键
     *
     * @mbg.generated
     */

    private Long id;

    /**
     * 部门名称
     *
     * @mbg.generated
     */
    @ApiModelProperty(value = "部门名称")
    private String departmentName;

    /**
     * 编码
     *
     * @mbg.generated
     */
    @ApiModelProperty(value = "编码")
    private String code;

    /**
     * 父节点
     *
     * @mbg.generated
     */
    @ApiModelProperty(value = "父节点")
    private Long parentId;

    /**
     * 状态 0 启用  1 禁用
     */
    @ApiModelProperty(value = "状态 0 启用  1 禁用")
    private Integer status;

    @ApiModelProperty(value = "上级列表逗号隔开： 0,11,22")
    private String  ancestors;

    @ApiModelProperty(value = "公司id")
    private Long companyId;

    @ApiModelProperty(value = "流程引擎对应主键")
    private String otherId;

    private static final long serialVersionUID = 1L;
}
