package com.hzw.sunflower.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hzw.sunflower.entity.CompanyFlowJump;

import java.util.List;

public interface CompanyFlowJumpService extends IService<CompanyFlowJump> {

    /**
     * 空节点跳过
     * @param processDefinitionKey
     * @return
     */
    List<String> getCompanyFlowJumpId(String processDefinitionKey);

    /**
     * 必审
     * @param processDefinitionKey
     * @return
     */
    List<String> IgnoreJump(String processDefinitionKey);

}
