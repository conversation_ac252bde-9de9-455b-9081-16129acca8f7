package com.hzw.sunflower.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.hzw.sunflower.common.BaseBean;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * <AUTHOR>
 */
@Data
@TableName("t_user_show_apply_info")
public class TendererShowApplyInfo extends BaseBean {

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "用户id")
    private Long userId;

    @ApiModelProperty(value = "项目Id")
    private Long projectId;

    @ApiModelProperty(value = "数据范围：1 我的全部项目 2 选择项目")
    private Integer dataScope;

    @ApiModelProperty(value = "是否查看1：是，2：否")
    private Integer rightCode;

}
