package com.hzw.sunflower.dto;

import lombok.Data;

/**
 * <AUTHOR>
 * @datetime 2024/06/26 16:37
 * @description: 投标人须知
 * @version: 1.0
 */
@Data
public class BidderNoticeFillDTO {
    /**
     * 工程项目名称
     */
    private String engineeringProjectName;

    /**
     * 资金落实情况
     */
    private String fundingImplementation;

    /**
     * 交货日期
     */
    private String deliveryPeriod;

    /**
     * 计划交货日期
     */
    private String planDeliveryDate;

    /**
     * 交货地址
     */
    private String deliveryAddress;

    /**
     * 技术性能指标
     */
    private String tecPerformanceIndex;

    /**
     * 投标人资质条件、能力、信誉
     * （1）资质要求（对制造商资质有要求的，应分别列出并注明）：
     * （2）财务要求：
     * （3）投标人业绩：
     * 投标设备业绩：
     * （4）信誉要求：
     * （5）其他要求：
     */
    private String bidderQualifications;

    /**
     * 投标人不得存在的其他情形
     */
    private String otherSituationNotAllow;

    /**
     * 是否召开投标预备会
     */
    private String isOpenMeeting;
    private String notOpen;
    private String open;

    /**
     * 召开时间
     */
    private String openTime;

    /**
     * 召开地点
     */
    private String openAddress;

    /**
     * 投标人在投标预备会前提出问题 时间
     */
    private String questionTime;

    /**
     * 投标人在投标预备会前提出问题 形式
     */
    private String questionType;

    /**
     * 招标文件澄清发出的形式
     */
    private String clarifySendType;

    /**
     * 是否允许分包
     */
    private String isAllowSubcontract;
    private String notAllowSubcontract;
    private String allowSubcontract;

    /**
     * 分包要求
     * 分包内容要求：
     * 分包金额要求：
     * 对分包人的资质要求：
     */
    private String subcontractRequirements;

    /**
     * 实质性要求和条件
     */
    private String requirementsAndConditions;

    /**
     * 其他可以被接受的技术支持资料
     */
    private String tecSupportMaterials;

    /**
     * 是否允许偏差
     */
    private String isAllowDeviation;
    private String notAllowDeviation;
    private String allowDeviation;

    /**
     * 偏差范围
     * 偏差范围：
     * 最高项数：
     */
    private String deviationRange;

    /**
     * 构成招标文件的其他资料
     */
    private String otherTenderMateries;

    /**
     * 投标人要求澄清招标文件 时间
     */
    private String clarifyTime;

    /**
     * 投标人要求澄清招标文件 形式
     */
    private String clarifyType;

    /**
     * 投标人确认收到招标文件澄清 时间
     */
    private String receiveClarifyTime;

    /**
     * 投标人确认收到招标文件澄清 形式
     */
    private String receiveClarifyType;

    /**
     * 招标文件修改发出的形式
     */
    private String updateSendType;

    /**
     * 投标人确认收到招标文件修改 时间
     */
    private String receiveUpdateTime;

    /**
     * 投标人确认收到招标文件修改 形式
     */
    private String receiveUpdateType;

    /**
     * 构成投标文件的其他资料
     */
    private String otherBidMaterials;

    /**
     * 增值税税金的计算方法
     */
    private String taxCalculateMethod;

    /**
     * 有无最高投标限价
     */
    private String isMaxBidPrice;
    private String notMaxBidPrice;
    private String hasMaxBidPrice;

    /**
     * 最高投标限价金额
     */
    private String maxBidPriceLimit;

    /**
     * 投标报价的其他要求
     */
    private String bidOtherRequirements;

    /**
     * 投标有效期
     */
    private String bidExpireDate;

    /**
     * 是否要求投标保证金
     */
    private String isNeedBond;
    private String needBond;
    private String notNeedBond;

    /**
     * 投标保证金收取方式
     * 投标保证金的形式：
     * 投标保证金的金额：
     */
    private String bondReceptType;

    /**
     * 其他可以不予退还投标保证金的情形
     */
    private String refuseRefundSituation;

    /**
     * 有无资格审查资料的特殊要求
     */
    private String isSpecialRequirements;
    private String noSpecialRequirements;
    private String hasSpecialRequirements;

    /**
     * 资格审查资料的具体要求
     */
    private String specialRequirements;

    /**
     * 近年财务状况的年份要求
     */
    private String financialYearPeriod;

    /**
     * 近年完成的类似项目情况的时间要求
     */
    private String projectFinishDatePeriod;

    /**
     * 近年发生的诉讼及仲裁情况的时间要求
     */
    private String litigationDatePeriod;

    /**
     * 是否允许递交备选投标方案
     */
    private String isAllowSubmit;
    private String allowSubmit;
    private String noSubmit;

    /**
     * 投标文件副本份数及其他要求
     * 投标文件副本份数：
     * 是否要求提交电子版文件：
     * 其他要求：
     */
    private String copiesOtherRequirements;

    /**
     * 是否需要投标文件分册装订
     */
    private String isNeedVolumeBind;
    private String noVolumeBind;
    private String needVolumnBind;

    /**
     * 投标文件分册装订要求
     */
    private String volumnBindRequirements;

    /**
     * 投标文件所附证书证件要求
     */
    private String certificateRequirements;

    /**
     * 投标文件签字或盖章要求
     */
    private String signRequirements;

    /**
     * 投标文件加密要求
     */
    private String encryptionRequirements;

    /**
     * 封套上应载明的信息
     */
    private String envelopeInfo;

    /**
     * 投标文件是否退还
     */
    private String isReturn;
    private String hasReturn;
    private String noReturn;

    /**
     * 退还时间
     */
    private String returnTime;

    /**
     * 开标地点
     */
    private String bidOpenAddress;

    /**
     * 开标顺序
     */
    private String bidOpenOrder;

    /**
     * 评标委员会的组建
     */
    private String bidEvaluationBuild;

    /**
     * 评标委员会推荐中标候选人的人数
     */
    private String candidatesCounts;

    /**
     * 中标候选人公示媒介及期限
     */
    private String candidatesMediaPeriod;

    /**
     * 是否授权评标委员会确定中标人
     */
    private String isAuthEvaluation;
    private String hasAuthEvaluation;
    private String noAuthEvaluation;

    /**
     * 是否要求履约保证金
     */
    private String bondRequire;
    private String hasBondRequire;
    private String noBondRequire;

    /**
     * 履约保证金形式和金额
     */
    private String performanceBondInfo;

    /**
     * 是否采用电子招标投标
     */
    private String isElecBid;
    private String hasElecBid;
    private String noElecBid;

    /**
     * 电子招标投标具体要求
     */
    private String elecBidRequirements;

    /**
     * 需要补充的其他内容
     */
    private String supplementContent;

    /**
     * 5.1开标时间和地点
     */
    private String openTimeAddressContent;

    /**
     * 5.2开标程序
     */
    private String openProcessContent1;
    private String openProcessContent2;

    /**
     * 项目建设地点
     */
    private String constructionLocation;

    /**
     * 项目建设规模
     */
    private String constructionScale;

    /**
     * 项目投资估算
     */
    private String investmentEstimate;

    /**
     * 设计服务期限
     */
    private String designServicePeriod;

    /**
     * 质量标准
     */
    private String qulityStandard;

    /**
     * 是否组织勘探现场
     */
    private String isSiteExplore;
    private String siteExplore;
    private String noSiteExplore;
    private String exploreInfo;

    /**
     * 报价方式
     */
    private String quoteType;

    /**
     * 计划工期
     */
    private String planPeriod;
    private String planStartDate;
    private String planEndDate;

    /**
     * 质量要求
     */
    private String qualityRequirements;

    /**
     * 招标人书面澄清的时间
     */
    private String tenderClarifyTime;

    /**
     * 近年财务状况的年份要求-简明标准
     */
    private String financialYear;

    /**
     * 近年完成的类似项目的年份要求-简明标准
     */
    private String projectFinishYear;

    /**
     * 投标文件副本份数-简明标准
     */
    private String copiesCount;

}
