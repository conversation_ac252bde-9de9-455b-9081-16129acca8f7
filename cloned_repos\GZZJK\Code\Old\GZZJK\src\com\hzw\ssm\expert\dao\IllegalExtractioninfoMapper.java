/**
 * 
 */
package com.hzw.ssm.expert.dao;

import java.util.List;

import com.hzw.ssm.expert.entity.IllegalExtractioninfoEntity;
import com.hzw.ssm.sys.user.entity.UserEntity;

/**
 * <AUTHOR>
 *
 */
public interface IllegalExtractioninfoMapper {

	/**
	 * 查询预警信息
	 * @param entity
	 * @return
	 */
	public List<IllegalExtractioninfoEntity> queryPageillegalExtraction(IllegalExtractioninfoEntity entity);


	/**
	 * 开标后抽取专家
	 * @param entity
	 * @return
	 */
	public List<IllegalExtractioninfoEntity> queryPageExpertsDrawnAfterBidOpening(IllegalExtractioninfoEntity entity);



	/**
	 * 查询预警信息
	 * @param entity
	 * @return
	 */
	public List<IllegalExtractioninfoEntity> pdfMonitorillegalExtraction(IllegalExtractioninfoEntity entity);



	/**
	 * 查询预警信息(不分页)
	 * @param entity
	 * @return
	 */
	public List<IllegalExtractioninfoEntity> queryillegalExtraction(IllegalExtractioninfoEntity entity);

	/**
	 * 审核修改
	 * @param dao
	 */
	public void modifyMonitor(IllegalExtractioninfoEntity dao);

	public List<IllegalExtractioninfoEntity> checkExtractFrequenc(IllegalExtractioninfoEntity entity);
	
	/**
	 * 提交审批
	 * @param entity
	 */
	public void modifyMonitorFile(IllegalExtractioninfoEntity entity);
	
	/**
	 * 根据用户名查询用户id
	 * @param userName
	 */
	public List<UserEntity> getUserNameToId(String userName);

}
