package com.hzw.sunflower.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hzw.sunflower.controller.request.ProcessRecordReq;
import com.hzw.sunflower.dto.ProcessRecordDTO;
import com.hzw.sunflower.entity.CalibrationProcessRecord;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2021/7/21 16:52
 * @description：定标流程service
 * @modified By：`
 * @version: 1.0
 */
public interface ProcessRecordService extends IService<CalibrationProcessRecord> {

    /**
     * 添加流程信息
     *
     * @param dto
     * @return
     */
    Boolean addProcessRecord(ProcessRecordDTO dto,String userOtherId,String retrunStr);


    /**
     * 添加流程信息（插入下一步审批人）
     *
     * @param dto
     * @return
     */
    Boolean addProcessRecordNextUserName(ProcessRecordDTO dto,String userOtherId,String retrunStr,String nextUserName);

    /**
     * 添加流程信息 无工作流
     *
     * @param dto
     * @return
     */
    Boolean addProcessRecordNotTask(ProcessRecordDTO dto,String userOtherIdr);

    /**
     * 添加备注流程信息
     *
     * @param dto
     * @return
     */
    Boolean addProcessRemarkRecord(ProcessRecordDTO dto);

    /**
     * 查询流程
     *
     * @param req
     * @return
     */
    List<ProcessRecordDTO> queryProcessRecord(ProcessRecordReq req);

    /**
     * 查询流程集合
     * @param req
     * @return
     */
    List<ProcessRecordDTO> queryProcessRecords(ProcessRecordReq req);

    /**
     * 劳务费查询流程记录
     *
     * @param req
     * @return
     */
    List<ProcessRecordDTO> queryProcessRecordExpertFee(ProcessRecordReq req);

    /**
     * 劳务费查询流程记录
     *
     * @param req
     * @return
     */
    Date queryEndTime(ProcessRecordReq req);

    /**
     * 删除分摊操作记录
     */
    Boolean deleteShareRecord(String businessCode, String nccBusinessCode, Long projectId);

    /**
     * 更新保证金退还流程信息
     * @param refundId 退还id
     * @param refundProcessCode 退还流程code：processInstanceId
     * @param refundUserCode 退还流程引擎审批人code：userCode
     * @return
     */
    Boolean updateBondRefundProcessRecord(Long refundId, String refundProcessCode, String refundUserCode);

    /**
     * 判断是否存在下一步审批人
     * @param str
     * @return
     */
    boolean hasNext(String str, String userOtherId);
}
