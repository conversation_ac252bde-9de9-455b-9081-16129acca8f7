package com.hzw.ssm.applets.entity;

import java.util.Date;
import java.util.List;

/**
 *  职称信息
 * <AUTHOR>
 * @Date 2021-02-02 16:00
 * @Version 1.0
 */
public class TitleInfo {

    private String id;
    private String expertId;// 专家主键
    private String titleId;// 职称名称id
    private String getTime;// 获得时间
    private String picture;// 职称图片
    private String pictureName;// 职称图片
    private String deleteFlag;// 状态0-否，1-是
    private String expertType;// 状态0-否，1-是
    private Date createTime;
    private Date updateTime;

    private String titleYears;// 职称年限
    private String titleName;// 职称名称
    private List<String> pictures;// 前段职称图片

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getTitleId() {
        return titleId;
    }

    public void setTitleId(String titleId) {
        this.titleId = titleId;
    }

    public String getGetTime() {
        return getTime;
    }

    public void setGetTime(String getTime) {
        this.getTime = getTime;
    }

    public String getPicture() {
        return picture;
    }

    public void setPicture(String picture) {
        this.picture = picture;
    }

    public String getDeleteFlag() {
        return deleteFlag;
    }

    public void setDeleteFlag(String deleteFlag) {
        this.deleteFlag = deleteFlag;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getTitleYears() {
        return titleYears;
    }

    public void setTitleYears(String titleYears) {
        this.titleYears = titleYears;
    }

    public String getExpertId() {
        return expertId;
    }

    public void setExpertId(String expertId) {
        this.expertId = expertId;
    }

    public String getTitleName() {
        return titleName;
    }

    public void setTitleName(String titleName) {
        this.titleName = titleName;
    }

    public List<String> getPictures() {
        return pictures;
    }

    public void setPictures(List<String> pictures) {
        this.pictures = pictures;
    }

    public String getExpertType() {
        return expertType;
    }

    public void setExpertType(String expertType) {
        this.expertType = expertType;
    }

    public String getPictureName() {
        return pictureName;
    }

    public void setPictureName(String pictureName) {
        this.pictureName = pictureName;
    }
}
