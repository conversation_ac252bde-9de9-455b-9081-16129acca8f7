package com.hzw.ssm.sys.system.service;

import java.util.List;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.hzw.ssm.expert.entity.SpecialtyInfoEntity;
import com.hzw.ssm.fw.base.BaseService;
import com.hzw.ssm.fw.util.CommUtil;
import com.hzw.ssm.fw.util.StringUtil;
import com.hzw.ssm.sys.system.dao.SpecialtyInfoMapper;

/**
 * 专业类别表操作类
 * 
 * <AUTHOR>
 * 
 */
@Service
public class SpecialtyInfoService extends BaseService {

	private static Log log = LogFactory.getLog(SpecialtyInfoService.class);

	@Autowired
	private SpecialtyInfoMapper specialtyMapper;

	/**
	 * 加载树形专业类别
	 */
	public String querySpecialtyList() {
		String str = null;
		List<SpecialtyInfoEntity> dataList = specialtyMapper.querySpecialtyList();
		if (dataList != null && dataList.size() > 0) {
			StringBuffer specialtyStr = new StringBuffer();
			StringBuffer con = new StringBuffer();
			for (int i = 0; i < dataList.size(); i++) {
				SpecialtyInfoEntity speEntity = dataList.get(i);
				con.append("tree.add(" + speEntity.getSpe_id() + "," + speEntity.getSpe_parent() + ",'"
						+ speEntity.getSpe_name() + "','findSpecialty?specialtyInfoEntity.spe_id="
						+ speEntity.getSpe_id() + "','','right','','','');\n");
			}
			specialtyStr.append(con.toString());
			str = specialtyStr.toString();
		}
		return str;
	}

	/**
	 * 显示某个专业类别,这是右边的页面
	 */
	public SpecialtyInfoEntity findSpecialty(SpecialtyInfoEntity specialty) {
		SpecialtyInfoEntity specialtyEntity = new SpecialtyInfoEntity();
		if (null != specialty.getSpe_id()) {// 如果是点击了专业类别则显示相关专业类别的信息
			specialtyEntity = searchSpecialty(specialty);
			return specialtyEntity;
		}
		List<SpecialtyInfoEntity> dataList = specialtyMapper.getSpecialtyById(specialty);
		if (null != dataList && dataList.size() > 0) {// 初始化默认显示一条专业类别的信息
			SpecialtyInfoEntity m = (SpecialtyInfoEntity) dataList.get(0);
			m.setParentSpecialtyName("根专业");
			m.setHasSpecialty(checkChild(m));
			if (null != m.getSpe_parent() && !m.getSpe_parent().equals(-1L))
				m = searchSpecialty(m);
			specialtyEntity = m;
		}
		return specialtyEntity;
	}

	/**
	 * 查找专业类别节点以及取得父节点的名称
	 */
	private SpecialtyInfoEntity searchSpecialty(SpecialtyInfoEntity specialty) {
		SpecialtyInfoEntity m = new SpecialtyInfoEntity();
		List<SpecialtyInfoEntity> dataList = specialtyMapper.getSpecialtyById(specialty);
		if (null != dataList && dataList.size() > 0) {
			m = (SpecialtyInfoEntity) dataList.get(0);
			m.setParentSpecialtyName("根专业");
			m.setHasSpecialty(checkChild(m));
			if (!"-1".equals(m.getSpe_parent())) {
				dataList = specialtyMapper.getSpecialtyByParentId(m);
				m.setParentSpecialtyName(dataList.get(0).getSpe_name());
			}
		}
		return m;
	}

	public String editSpecialty(SpecialtyInfoEntity specialty) {
		String[] flags = specialty.getFlag().split(",");
		String id = flags[0];
		SpecialtyInfoEntity specialtyEntity = new SpecialtyInfoEntity();
		specialtyEntity.setSpe_id(id);

		if (flags[1].equals("0")) {// 添加专业类别默认添加节专业类别的子专业类别
			if ("-1".equals(id)) {// 添加根节点
				specialty.setSpe_parent("-1");
				specialty.setParentSpecialtyName("根节点");
			} else {
				SpecialtyInfoEntity m2 = searchSpecialtys(specialtyEntity);
				specialty.setSpe_parent(m2.getSpe_id());
				specialty.setParentSpecialtyName(m2.getSpe_name());
			}

		} else if (flags[1].equals("1")) {// 修改专业类别
			SpecialtyInfoEntity m2 = searchSpecialty(specialtyEntity);
			specialty.setSpe_id(m2.getSpe_id());
			specialty.setSpe_name(m2.getSpe_name());
			specialty.setSpe_parent(m2.getSpe_parent());
			specialty.setSpe_code(m2.getSpe_code());
			specialty.setParentSpecialtyName(m2.getSpe_name());
		} else {
			/** 首先判断是否为叶子节点。叶子节点的删除直接将其删除标记置为1 */
			specialty.setSpe_id(id);
			specialty.setSpe_parent(flags[2]);
			deleteByObject(specialty);
			return "toSpecialtyMain";
		}
		return "toSpecialtyAdd";
	}

	/** 查找专业类别节点以及取得父节点的名称,非根节点 */
	private SpecialtyInfoEntity searchSpecialtys(SpecialtyInfoEntity specialty) {
		SpecialtyInfoEntity m = new SpecialtyInfoEntity();
		List<SpecialtyInfoEntity> dataList = specialtyMapper.getSpecialtyById(specialty);
		if (null != dataList && dataList.size() > 0) {
			m = (SpecialtyInfoEntity) dataList.get(0);
			m.setParentSpecialtyName(m.getSpe_name());
		}
		return m;
	}

	/**
	 * 判断其是否有子节点
	 */
	private boolean checkChild(SpecialtyInfoEntity specialty) {
		List<SpecialtyInfoEntity> list = specialtyMapper.querySubSpecialtyBySpecialtyId(specialty);
		if (null != list && list.size() > 0) {// 说明它有子节点，设置bean的标记
			return true;
		}
		return false;
	}

	/**
	 * 删除专业类别信息
	 * 
	 * @param menu
	 */
	public void deleteByObject(SpecialtyInfoEntity specialty) {
		specialtyMapper.deleteSpecialtyById(specialty);
	}

	/**
	 * 校验专业类别名称是否存在
	 */
	public List<SpecialtyInfoEntity> checkSpecialty(SpecialtyInfoEntity specialty) {
		specialty.setSpe_name(StringUtil.trim(specialty.getSpe_name()));
		List<SpecialtyInfoEntity> specialtyOne = specialtyMapper.getSpecialtyByName(specialty);
		return specialtyOne;
	}

	/**
	 * 保存专业类别信息
	 * 
	 * @param menu
	 */
	public void saveSpecialty(SpecialtyInfoEntity specialty) {
		specialty.setSpe_id(CommUtil.getKey());
		specialty.setSpe_name(StringUtil.trim(specialty.getSpe_name()));
		specialtyMapper.saveSpecialty(specialty);
	}

	/**
	 * 修改专业类别信息
	 */
	public void modifySpecialty(SpecialtyInfoEntity specialty) {
		specialtyMapper.modifySpecialty(specialty);
	}
}
