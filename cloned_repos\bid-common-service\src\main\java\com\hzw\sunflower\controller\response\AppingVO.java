package com.hzw.sunflower.controller.response;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@ApiModel(description = "代办列表 ")
@Data
public class AppingVO implements Serializable {
    /**
     * 任务id
     */
    @ApiModelProperty(value = "任务id")
    private String taskId;
    /**
     * 审批内容
     */
    @ApiModelProperty(value = "审批内容")
    private String formName;

    /**
     * formKey
     */
    @ApiModelProperty(value = "formKey")
    private String formKey;

    /**
     * 类别
     */
    @ApiModelProperty(value = "类别")
    private String processDefinitionKey;




    /**
     * 流程实例的id
     */
    @ApiModelProperty(value = "流程实例的id", required = true)
    private String processInstanceId;

    /**
     * 业务管理key ,表名:id 例如 notice:123456
     */
    @ApiModelProperty(value = "业务管理key ,表名:id 例如 notice:123456", required = true)
    private String businessKey;



}
