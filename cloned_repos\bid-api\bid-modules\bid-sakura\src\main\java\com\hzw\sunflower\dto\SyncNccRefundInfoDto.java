package com.hzw.sunflower.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @datetime 2022/11/07 9:06
 * @description: 保证金退款实体类
 * @version: 1.0
 */
@Data
public class SyncNccRefundInfoDto {

    @ApiModelProperty(value = "项目编号")
    private String purchaseNumber;

    @ApiModelProperty(value = "客户ID")
    private String customer;

    @ApiModelProperty(value = "部门Id")
    private String pkDeptId;

    @ApiModelProperty(value = "用户code")
    private String pkPsndoc;

    @ApiModelProperty(value = "退款总金额=退保证金金额+退款利息")
    private String moneyDe;

    @ApiModelProperty(value = "付款银行账户")
    private String payAccount;

    @ApiModelProperty(value = "保证金利息")
    private String localTaxDe;

    @ApiModelProperty(value = "保证金不含利息金额")
    private String noTaxDe;

    @ApiModelProperty(value = "退保证金日期")
    private String refundDate;

    @ApiModelProperty(value = "代理服务费")
    private BigDecimal agencyFee;

    @ApiModelProperty(value = "保证金金额")
    private BigDecimal bondMoney;

    @ApiModelProperty(value = "开户行")
    private String fromOpenBank;

    @ApiModelProperty(value = "保证金账户")
    private String fromBankNumber;

    @ApiModelProperty(value = "到账时间")
    private String amountDate;

    @ApiModelProperty(value = "省内省外标识 N：省内，W：省外")
    private String provinceFlag;

    @ApiModelProperty(value = "收款银行账户")
    private String receiveAcount;


}
