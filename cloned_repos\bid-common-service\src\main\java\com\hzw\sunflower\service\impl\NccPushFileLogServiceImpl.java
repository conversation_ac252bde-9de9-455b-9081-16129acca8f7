package com.hzw.sunflower.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzw.sunflower.dao.NccPushFileLogMapper;
import com.hzw.sunflower.entity.NccPushFileLog;
import com.hzw.sunflower.service.NccPushFileLogService;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class NccPushFileLogServiceImpl extends ServiceImpl<NccPushFileLogMapper, NccPushFileLog> implements NccPushFileLogService {
    /**
     * 获取需要推送的文件数据
     * @return
     */
    @Override
    public List<NccPushFileLog> getNccSyncFile() {
        return this.baseMapper.queryNccSyncFile();
    }

    @Override
    public List<NccPushFileLog> queryFileInfo() {
        return this.baseMapper.queryFileInfo();
    }
}
