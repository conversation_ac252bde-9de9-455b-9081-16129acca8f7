package com.hzw.sunflower.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hzw.sunflower.controller.request.PurchaseTypeCheckReq;
import com.hzw.sunflower.entity.ColumnCheckConfig;
import com.hzw.sunflower.entity.ColumnPurchaseConfig;

public interface ColumnPurchaseConfigService extends IService<ColumnPurchaseConfig> {

    /**
     * 根据采购方式获取校验条件
     * @param req
     * @return
     */
    ColumnCheckConfig getPurchaseTypeCheck(PurchaseTypeCheckReq req);

}
