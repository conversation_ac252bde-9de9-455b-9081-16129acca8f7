<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzw.sunflower.dao.ProjectTurnEntrustMapper">

	<select id="findDeptId" resultType="java.lang.Long">
		SELECT
			rud.department_id
		FROM
			r_user_department rud
		WHERE
			rud.user_id = #{userId}
		AND rud.user_identity_id = 2
		AND rud.is_delete = 0
	</select>
    <select id="findInfo" resultType="com.hzw.sunflower.controller.project.response.ProjectTurnEntrustVo">
		SELECT
			eu.project_id,
			c.company_name,
			p.project_name projectName,
			u.user_name submitter,
			u1.id clientId,
			u1.user_name clientName,
			te.`status`,
			te.submit_time
		FROM
			t_project_turn_entrust te
			LEFT JOIN t_project_entrust_user eu ON eu.project_id = te.project_id AND eu.type = 0 AND eu.is_delete = 0
			LEFT JOIN r_user_department rud on eu.user_id = rud.user_id  and eu.department_id = rud.department_id
			LEFT JOIN t_user u ON te.submitter = u.id AND u.is_delete = 0
			LEFT JOIN t_user u1 ON te.des_user_id = u1.id
			LEFT JOIN t_company c ON c.id = eu.company_id AND c.is_delete = 0
			LEFT JOIN t_project p ON te.project_id = p.id
		WHERE
			te.is_delete = 0
			AND u1.is_delete = 0
			AND p.is_delete = 0
			AND te.id = #{id}
		GROUP BY
			eu.project_id,
			c.company_name,
			p.project_name ,
			u.user_name ,
			u1.id,
			u1.user_name ,
			te.`status`,
			te.submit_time
	</select>
	<select id="findUser" resultType="com.hzw.sunflower.entity.User">
		SELECT
			u.user_name,
			u.user_phone
		FROM
			t_project_entrust_user eu
			LEFT JOIN r_user_department rud on eu.user_id = rud.user_id  and eu.department_id = rud.department_id
			LEFT JOIN t_user u ON eu.user_id = u.id
		WHERE
			eu.project_id = #{projectId}
			AND eu.type = 0
			AND eu.is_delete = 0
			AND u.is_delete = 0
	</select>
	<select id="findDeptName" resultType="java.lang.String">
		SELECT
			d.department_name
		FROM
			t_project_turn_entrust te
			LEFT JOIN t_department d ON d.id = te.des_depart_id
		WHERE
			te.des_user_id = #{userId}
			AND te.id = #{id}
			AND d.is_delete = 0
	</select>
    <select id="listTurnEntrust"
            resultType="com.hzw.sunflower.controller.project.response.ProjectTurnEntrustVo">
		SELECT
			distinct
			te.id,
			p.id projectId,
			c.company_name,
			p.project_name,
			u.user_name submitter,
			u1.id clientId,
			u1.user_name clientName,
			te.`status`,
			te.submit_time
		FROM
			t_project_turn_entrust te
			LEFT JOIN t_project_entrust_user eu ON eu.project_id = te.project_id AND eu.type = 0 AND eu.is_delete = 0
			LEFT JOIN t_user u ON te.submitter = u.id AND u.is_delete = 0
			LEFT JOIN t_user u1 ON te.des_user_id = u1.id
			LEFT JOIN t_company c ON c.id = eu.company_id AND c.is_delete = 0
			LEFT JOIN t_project p ON te.project_id = p.id
			LEFT JOIN r_user_department ud ON ud.is_delete = 0 AND ud.user_id = te.src_user_id
            LEFT JOIN r_user_department ud1 ON ud1.is_delete = 0 AND ud1.user_id = te.des_user_id
		WHERE
			te.is_delete = 0
			AND u1.is_delete = 0
			AND p.is_delete = 0
			AND ud.department_id &lt;&gt; ud1.department_id
			AND te.src_user_id = #{jwtUser.id}
		GROUP BY
			te.id,
			c.company_name,
			p.project_name,
			u.user_name,
			u1.id ,
			u1.user_name,
			te.`status`,
			te.submit_time,
			p.id
		ORDER BY te.submit_time  DESC
	</select>
</mapper>
