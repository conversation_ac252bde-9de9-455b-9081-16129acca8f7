package com.hzw.sunflower.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hzw.sunflower.controller.project.request.AgentMyProjectREQ;
import com.hzw.sunflower.controller.project.request.TendererShowApplyInfoReq;
import com.hzw.sunflower.dto.MyProjectListDTO;
import com.hzw.sunflower.entity.TendererShowApplyInfo;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface TendererShowApplyInfoService extends IService<TendererShowApplyInfo> {
    /**
     * 招标人设置是否查看采购情况
     * @param req
     * @return
     */
    Boolean batchSettingShow(TendererShowApplyInfoReq req);

    /**
     * 根据条件查询我的项目
     * @param condition
     * @return
     */
    List<MyProjectListDTO> queryOnlyMyProjectPageListByUserId(AgentMyProjectREQ condition);

    /**
     * 招标人是否查看采购情况
     * @param userId
     * @param projectId
     * @return
     */
    Boolean chooseShowOrNot(Long userId, String projectId);

    List<MyProjectListDTO> listMyProject(AgentMyProjectREQ condition);
}
