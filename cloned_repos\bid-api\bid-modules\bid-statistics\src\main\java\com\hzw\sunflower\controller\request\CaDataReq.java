package com.hzw.sunflower.controller.request;

import com.hzw.sunflower.common.BaseCondition;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2023/3/9 16:45 星期四
 */
@Data
@ApiModel(description = "CA数据查询条件")
public class CaDataReq extends BaseCondition {


    @ApiModelProperty(value = "关键字")
    private String keyWords;

    @ApiModelProperty("所属处室ID")
    private Long deptId;

    @ApiModelProperty("判断是否有选中部门筛选项 0-否 1-是")
    private Integer haveAllDept;

    @ApiModelProperty("进场场地")
    private String processAddress;

    @ApiModelProperty("是否进场 前端不需要给值")
    private Integer isProcess;

    @ApiModelProperty("政府采购 0-否 1-是 2-全部")
    private String isGovernment;

    /**
     *  是否国际标
     */
    @ApiModelProperty("是否国际标 0-否 1-是 2-全部")
    private String isInternational;

    /**
     *  开始时间
     */
    @ApiModelProperty("开始时间")
    private String startDate;

    /**
     *  结束时间
     */
    @ApiModelProperty("结束时间")
    private String endDate;

    @ApiModelProperty(value = "页码")
    private Long pageIndex;

    @ApiModelProperty(value = "每页个数")
    private Long pageSize;



}
