package com.hzw.ssm.applets.utils;
import java.util.UUID;

public class JudgeToken {
	
	/**
	 * 判断token 是否正确
	 * @param inputToken
	 * @param loginToken
	 * @return true 正确 false 错误
	 */
	public static  boolean findToken(String inputToken, String loginToken){
		if(inputToken.equals(loginToken)){
			return true;
		}
		return false;
	}
	
	
	/**
	 * 生成token
	 * @return
	 */
	public static String generateToken(){
		String token = UUID.randomUUID().toString();
		return token;
	}
	
}
