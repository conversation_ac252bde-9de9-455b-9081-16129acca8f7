package com.hzw.sunflower.dto;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;


@Data
public class SingleProjectPermUserDTO {

    @ApiModelProperty(value = "授权项目Id")
    private Long projectId;

    @ApiModelProperty(value = "用户部门id")
    private List<UserDepartDTO> userDeparts;


    @ApiModelProperty(value = "权限码0：查看，1：编辑查看")
    private Integer rightCode;


}
