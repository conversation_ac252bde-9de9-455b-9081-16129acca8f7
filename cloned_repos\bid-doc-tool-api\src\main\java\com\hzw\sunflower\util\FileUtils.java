/*
 * 公司名称：江苏华招网
 * 版权信息：江苏华招网版权
 * 描述：文件相关操作
 * 文件名称：FileUtils.java
 * 修改时间：2017年10月16日
 * 修改人：ypp
 * 修改内容：
 * 跟踪单号：
 * 修改单号 ：
 */
package com.hzw.sunflower.util;
import cn.hutool.core.io.FileUtil;
import com.deepoove.poi.XWPFTemplate;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.*;

/**
 * <一句话功能简述> 文件相关操作
 * <功能详细描述>
 * <AUTHOR>
 * @version V1.0[产品/模块版本]
 */
public class FileUtils {


    /**
     *读取文件以流的形式输出
     * @param filePath  路径
     * @param contentType  输出格式
     * @param fileName  文件名称
     * @throws IOException
     */
    public static void outFile(String filePath, String contentType,String fileName, HttpServletResponse response) throws IOException {
        File file = new File(FileUtils.replaceSeparator(filePath));
        InputStream in = null;
        OutputStream os = null;
        try {
            response.setContentType(contentType); // 设置返回内容格式
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-disposition", "attachment;filename=" + new String(fileName.getBytes("GB2312"),"iso8859-1"));
            in = new FileInputStream(file); //用该文件创建一个输入流
            os = response.getOutputStream(); //创建输出流
            byte[] b = new byte[1024];
            while (in.read(b) != -1) {
                os.write(b);
            }
            in.close();
            os.flush();
            os.close();
        } catch (Exception e) {
            if (null != in) {
                in.close();
            }
            if (null != os) {
                os.close();
            }
        }
    }

    /**
     * 功能描述: byte数组转 InputStream
     *
     * @param bytes byte数组
     * @return java.io.InputStream
     * <AUTHOR>
     * @date 2019/3/28 16:01
     * @version 1.0
     */
    public static InputStream byte2InputStream(byte[] bytes) {
        return new ByteArrayInputStream(bytes);
    }

    /**
     *
     * 函数功能描述：把路径中的分隔符替换成允许系统中的符号
     * @param path
     * @return
     */
    public static String replaceSeparator(String path) {
        if (File.separator.equals("/")) {
            return path.replaceAll("[\\\\/]+", "/");
        }
        return path.replaceAll("[\\\\/]+", "\\\\");
    }

    /**
     *读取文件以流的形式输出
     * @param filePath  路径
     * @param contentType  输出格式
     * @param fileName  文件名称
     * @throws IOException
     */
    public static void outWordFile(String filePath,String fileName, HttpServletResponse response) throws IOException {
        // path是指欲下载的文件的路径。
        File file = new File(filePath);
        // 取得文件名。
        //String filename = file.getName();
        // 取得文件的后缀名。
        //String ext = filename.substring(filename.lastIndexOf(".") + 1).toUpperCase();
        // 以流的形式下载文件。
        InputStream fis = new BufferedInputStream(new FileInputStream(filePath));
        byte[] buffer = new byte[fis.available()];
        fis.read(buffer);
        fis.close();
        // 清空response
        response.reset();
        // 设置response的Header
        response.setHeader("Content-disposition", "attachment;filename=" + new String(fileName.getBytes("GB2312"),"iso8859-1"));
        response.addHeader("Content-Length", "" + file.length());
        OutputStream toClient = new BufferedOutputStream(response.getOutputStream());
        response.setContentType("application/octet-stream");
        toClient.write(buffer);
        toClient.flush();
        toClient.close();
    }

    public static void copyFileUsingStream(File source, File dest) throws IOException {
        InputStream is = null;
        OutputStream os = null;
        try {
            is = new FileInputStream(source);
            os = new FileOutputStream(dest);
            byte[] buffer = new byte[1024];
            int length;
            while ((length = is.read(buffer)) > 0) {
                os.write(buffer, 0, length);
            }
        } finally {
            is.close();
            os.close();
        }
    }
}
