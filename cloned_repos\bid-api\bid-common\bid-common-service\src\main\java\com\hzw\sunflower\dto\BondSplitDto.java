package com.hzw.sunflower.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hzw.sunflower.common.BaseBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 保证金拆分
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-9
 */
@Getter
@Setter
@ApiModel(value = "BondSplit对象", description = "保证金拆分")
public class BondSplitDto extends BaseBean implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("关联项目id")
    private Long projectId;

    @ApiModelProperty("关联包id")
    private Long sectionId;

    @ApiModelProperty("关联流水id")
    private Long waterId;

    @ApiModelProperty("关联供应商id")
    private Long companyId;

    @ApiModelProperty("拆分金额")
    private BigDecimal amount;

    @ApiModelProperty(value = "项目编号/标包号")
    private String purchaseNumber;

    @ApiModelProperty(value = "ncc项目部门id")
    private String nccProjectDepartId;

    @ApiModelProperty("异常关联标识")
    private Integer exceptFlag;

}
