package com.hzw.sunflower.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hzw.ncc.client.dto.BondAdjustBodyDto;
import com.hzw.ncc.client.dto.BondAdjustDto;
import com.hzw.ncc.client.dto.BondReceiveDto;
import com.hzw.sunflower.controller.request.BondSectionReq;
import com.hzw.sunflower.dto.BondSplitDto;
import com.hzw.sunflower.dto.BondSplitHistoryDto;
import com.hzw.sunflower.dto.BondSplitNccDto;
import com.hzw.sunflower.dto.BondWaterToNccDto;
import com.hzw.sunflower.entity.BondSplit;

import java.util.List;

/**
 * <p>
 * 保证金拆分 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-15
 */
public interface BondSplitService extends IService<BondSplit> {

    /**
     * 查询标包的拆分合计金额
     * @param sectionId
     * @param companyId
     * @return
     */
    BondSplit listBySectionId(Long sectionId, Long companyId);

    /**
     * 添加拆分
     * @param companyId
     * @param sectionIdList
     * @param waterIdList
     * @return
     */
    Boolean addSplit(Long companyId, List<BondSectionReq> sectionIdList, List<Long> waterIdList);

    /**
     * 根据流水id获取保证金待推送收款数据
     * @param waterId
     * @return
     */
    BondReceiveDto getBondReceiveDtoByWaterId(Long waterId);


    /**
     * 根据流水id获取保证金带推送调整数据
     * @param water
     * @param historySplitList
     * @return
     */
    Boolean getBondAdjustDto(BondWaterToNccDto water, List<BondSplitDto> historySplitList);

    /**
     * 根据流水查询最新一条已删除拆分数据
     * @param waterId
     * @return
     */
    BondSplitNccDto queryLastDeleteByWaterId(Long waterId);

    /**
     * 根据流水查询最新一组已删除拆分数据
     * @param waterId
     * @return
     */
    List<BondSplitDto> queryLatestSplit(Long waterId);

    /**
     * 根据流水查询最新一组异常已删除拆分数据
     * @param waterId
     * @return
     */
    List<BondSplitDto> queryLatestSplitByRelationId(Long waterId, Long relationId);

    /**
     * 推送ncc
     * @param historyDtoList
     * @param operateType
     * @param companyId
     * @return
     */
    Boolean pushNccData(List<BondSplitHistoryDto> historyDtoList, Integer operateType, Long companyId);

    Boolean saveNccReceiveOtherPushLog(BondWaterToNccDto water);

    Boolean saveNccAdjustOtherPushLog(BondWaterToNccDto water, List<BondSplitDto> historySplitList);

    /**
     * 保存调整记录表
     * @param adjustBodyDtoList
     * @param companyId
     * @return
     */
    boolean saveAdjustChange(List<BondAdjustBodyDto> adjustBodyDtoList, Long companyId);

    /**
     * 修改流水关联标识
     * @param waterIds
     * @return
     */
    boolean updateSplitForExcept(List<Long> waterIds);

    /**
     * 修改流水关联标识
     * @param waterIds
     * @return
     */
    boolean updateSplitForIsLatest(List<Long> waterIds);

    /**
     * 是否推送调账记录
     * @param splitNccDto
     * @return
     */
    Boolean isPushAdjust(BondSplitNccDto splitNccDto);

    /**
     * 异常关联时将拆分表和关联表关联上
     * @param waterId
     * @param relationId
     * @return
     */
    Boolean updateRelationId(Long waterId,Long relationId);

    /**
     * 将拆分表异常关联的关联id置为空
     * @param waterIds
     * @return
     */
    Boolean updateRelationIdNull(List<Long> waterIds);
}
