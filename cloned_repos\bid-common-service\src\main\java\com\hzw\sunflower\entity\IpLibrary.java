package com.hzw.sunflower.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.hzw.sunflower.common.BaseBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @ClassName:IpLibrary
 * @Auther: lijinxin
 * @Description: ip库
 * @Date: 2024/8/27 09:58
 * @Version: v1.0
 */
@Data
@TableName("t_ip_library")
@ApiModel(value = "t_ip_library", description = "ip库")
public class IpLibrary extends BaseBean {

    @ApiModelProperty("主键")
    private Long id;


    @ApiModelProperty("ip地址")
    private String ipAddress;



    @ApiModelProperty("ip归属地址")
    private String ipLocation;

}
