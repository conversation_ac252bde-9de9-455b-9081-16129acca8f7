package com.hzw.sunflower.controller.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 招标文件开标一览表文件请求体
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@ApiModel(description = "招标文件开标一览表文件请求体")
@Data
public class BidOpenScheduleREQ implements Serializable {

    @ApiModelProperty(value = "删除包关联开标一览表")
    private List<Long> deleteFileId;

    @ApiModelProperty(value = "删除标段")
    private List<Long> deleteSectionId;

    @ApiModelProperty(value = "新增开标一览表信息")
    private List<ProjectSectionScheduleReq> scheduleList;

    @ApiModelProperty(value = "招标文件阶段： 0：默认无阶段； 1：第一轮； 2：第二轮")
    private Integer bidRound;

    @ApiModelProperty(value = "线上开标（1 是 2 否）")
    private Integer onlineBidOpen;

}
