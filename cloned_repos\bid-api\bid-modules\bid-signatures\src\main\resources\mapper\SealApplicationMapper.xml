<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzw.sunflower.dao.SealApplicationMapper">

    <select id="listInfo" resultType="com.hzw.sunflower.dto.SealApplicationDTO">
        select
        t1.id,
        t1.`type`,
        t1.apply_item,
        t1.item_describe,
        t1.projectName,
        t1.apply_user_name,
        t1.apply_time,
        t1.seal_type,
        if(find_in_set('2',t1.processing_progress) and t1.approve_status = 2 ,6,t1.approve_status) approve_status,
        t1.electronId,
        t1.entityId,
        t1.created_user_id
        from
        (select
            t1.id,
            t1.`type` ,
            t1.apply_item ,
            t1.item_describe,
            group_concat(distinct if(t3.section_id is null,concat(t3.project_name, t3.project_number),concat(t5.project_name, t5.purchase_number))) projectName,
            t1.apply_user_name ,
            t1.apply_time ,
            t1.seal_type,
            t1.approve_status,
            group_concat(distinct t4.processing_progress) processing_progress,
            t7.id as  electronId,
            t8.id as entityId,
            t1.dept_id department_id,
            t1.dept_id created_user_depart_id,
            t1.apply_user created_user_id
        from
            t_seal_application t1
            left join t_seal_application_project t3 on t3.apply_id = t1.id  and t3.is_delete = 0
            left join t_seal_application_info t4 on t4.apply_id = t1.id
            left join t_project t5 on t5.id = t3.project_id and t5.is_delete = 0
            left join t_project_bid_section t6 on t6.id = t3.section_id and t6.is_delete = 0
           left join t_seal_application_info t7 on t7.apply_id = t1.id  and t7.is_delete = 0 and t7.seal_type = 1
           left join t_seal_application_info t8 on t8.apply_id = t1.id  and t8.is_delete = 0 and t8.seal_type = 2
        where t1.is_delete = 0
            and t4.is_delete = 0
            and t1.approve_status != -1
        <if test="null != userId ">
            and t1.apply_user = #{userId}
        </if>
        <if test="null != departId">
            and t1.dept_id = #{departId}
        </if>
            and (t1.apply_item != 11 or t1.apply_item is null)
        <if test="null != condition.keyWords and '' != condition.keyWords ">
            and (t3.project_number like concat ('%',#{condition.keyWords},'%')
            or t3.project_name like concat ('%',#{condition.keyWords},'%')
            or t1.item_describe like concat ('%',#{condition.keyWords},'%')
            )
        </if>
        <if test="null != condition.type">
            and t1.`type` = #{condition.type}
        </if>

        group by
            t1.id,
            t1.`type` ,
            t1.apply_item ,
            t1.item_describe ,
            t1.apply_user_name ,
            t1.apply_time ,
            t1.seal_type,
            t1.approve_status,
            t7.id,
            t8.id,
            t1.dept_id,
            t1.apply_user
        ) t1
        where 1=1
        <if test="null != datascope and '' != datascope">
            ${datascope}
        </if>
        order by t1.apply_time desc
    </select>



    <select id="listProjectInfo" resultType="com.hzw.sunflower.dto.SealApplicationDTO">
        select
        t1.id,
        t1.`type`,
        t1.apply_item,
        t1.item_describe,
        t1.projectName,
        t1.apply_user_name,
        t1.apply_time,
        t1.seal_type,
        t1.package_number,
        if(find_in_set('2',t1.processing_progress) and t1.approve_status = 2 ,6,t1.approve_status) approve_status
        from
        (select
        t1.id,
        t1.`type` ,
        t1.apply_item ,
        t1.item_describe,
        group_concat(distinct if(t3.section_id is null,concat(t3.project_name, t3.project_number),concat(t5.project_name, t5.purchase_number))) projectName,
        t1.apply_user_name ,
        t1.apply_time ,
        t1.seal_type,
        t1.approve_status,
        group_concat(distinct t4.processing_progress) processing_progress,
        group_concat(distinct t6.package_number) package_number
        from
        t_seal_application t1
        left join t_seal_application_project t3 on t3.apply_id = t1.id  and t3.is_delete = 0
        left join t_seal_application_info t4 on t4.apply_id = t1.id
        left join t_project t5 on t5.id = t3.project_id and t5.is_delete = 0
        left join t_project_bid_section t6 on t6.id = t3.section_id and t6.is_delete = 0
        where t1.is_delete = 0
        and t4.is_delete = 0
        and t5.id = #{condition.projectId}
        <if test="null != condition.sectionId">
            and t6.id = #{condition.sectionId}
        </if>
        and (t1.apply_item != 11 or t1.apply_item is null)
        <if test="null != condition.keyWords and '' != condition.keyWords ">
            and (t3.project_number like concat ('%',#{condition.keyWords},'%')
            or t3.project_name like concat ('%',#{condition.keyWords},'%')
            or t1.item_describe like concat ('%',#{condition.keyWords},'%')
            )
        </if>
        <if test="null != condition.type">
           and t1.`type` = #{condition.type}
        </if>
        group by
        t1.id,
        t1.`type` ,
        t1.apply_item ,
        t1.item_describe ,
        t1.apply_user_name ,
        t1.apply_time ,
        t1.seal_type,
        t1.approve_status
        ) t1
        order by t1.apply_time desc
    </select>



    <select id="queryProjectInfo" resultType="com.hzw.sunflower.dto.SealApplicationProjectDTO">
        select
            t1.apply_id ,
            t1.project_id,
            t1.section_id ,
            if(t1.project_id  is not null or t1.project_id != '',t3.purchase_number,t1.project_number) project_number,
            if(t1.project_id  is not null or t1.project_id != '',t3.project_name,t1.project_name) project_name,
            t2.package_number packageNumber,
            IFNULL(t2.package_name,'-') packageName,
            t2.entrust_money entrustMoney,
            IFNULL(t2.purchase_mode_name,'-') purchaseModeName
        from
            t_seal_application_project t1
            left join t_project_bid_section t2 on t1.section_id = t2.id and t2.is_delete = 0
            left join t_project t3 on t3.id = t1.project_id and t3.is_delete = 0
        where
            t1.is_delete = 0
            and t1.apply_id = #{id}
    </select>
    <select id="queryApplyInfo" resultType="com.hzw.sunflower.controller.response.SealApplicationVO">

    </select>
    <select id="queryFileInfo" resultType="com.hzw.sunflower.dto.SealFileDTO">
        select
            t1.id,
            t1.original_file,
            t1.seal_file ,
            t2.oss_file_key,
            t2.oss_file_name,
            t6.oss_file_key sealFileKey,
            t6.oss_file_name sealFileName,
            t4.tenderer_name bidWinPeopleName
        from
            t_seal_application t5
            left join t_seal_file t1 on t5.id = t1.apply_id and t1.is_delete = 0
            left join t_oss_file t2 on t1.original_file = t2.id and t2.is_delete = 0
            left join t_oss_file t6 on t1.seal_file  = t6.id and t6.is_delete = 0
            left join t_seal_bid_win t3 on t3.apply_id = t5.id and t3.is_delete = 0 and t1.original_file = t3.file
            left join t_bid_win_people t4 on t3.bid_win_people_id = t4.id and t4.is_delete = 0
        where
            t5.is_delete = 0
            and t5.id = #{id}
    </select>

    <select id="selectSealHandleVO" resultType="com.hzw.sunflower.controller.response.SealApplicationHandleVO">
        select distinct
            tsai.id,
            tsa.id as applyId,
            tsa.`type`,
            tsa.item_describe,
            tsa.choose_seal_info,
            tsa.apply_user,
            tsa.apply_time,
            tsa.symbol,
            tsa.dept_name,
            tsa.apply_user_name,
            tsai.seal_type,
            tsai.sealed_user,
            tsai.sealed_time,
            s1.project_info
        from
            t_seal_application tsa
        left join t_seal_application_info tsai on
                tsa.id = tsai.apply_id
        left join(
                select apply_id,group_concat(project_info SEPARATOR '\n') project_info from (
                select apply_id , concat(project_name,project_number)as  project_info from t_seal_application_project   where is_delete = 0
            ) t1  group by apply_id
            )s1 on s1.apply_id = tsa.id
        left join t_seal_application_project tsap on tsa.id = tsap.apply_id
        where tsa.is_delete  = 0
          and tsai.is_delete  = 0
          and tsa.approve_status = 2
          and tsa.approve_status != -1
        <if test="null != req.processingProgress">
          and tsai.processing_progress = #{req.processingProgress}
        </if>
        <if test="null != req.keyWords and '' != req.keyWords">
            and (tsap.project_name like concat ('%',#{req.keyWords},'%')
            or tsap.project_number like concat ('%',#{req.keyWords},'%')
            or tsa.item_describe like concat ('%',#{req.keyWords},'%')
            )
        </if>
        <if test="null != req.type">
          and tsa.type = #{req.type}
        </if>
        <if test="null != req.deptId">
            and tsa.dept_id = #{req.deptId}
        </if>
        <if test="null != req.userName and  '' != req.userName">
            and tsa.apply_user_name = concat ('%',#{userName},'%')
        </if>
        <if test="null != req.startTime and '' !=  req.startTime">
            and date_format(tsa.apply_time,'%Y-%m-%d') &gt;= #{req.startTime}
        </if>
        <if test="null != req.endTime and '' !=  req.endTime">
            and date_format(tsa.apply_time,'%Y-%m-%d') &lt;= #{req.endTime}
        </if>
         <if test="null != req.symbol">
             and tsa.is_symbol =  #{req.symbol}
         </if>
         order by  tsa.apply_time desc
    </select>
    <select id="queryInfo" resultType="com.hzw.sunflower.controller.response.SealAppingTaskVO">
        select
            t1.id busId,
            group_concat( t2.project_id order by t2.project_id desc) projectIds,
            group_concat( if(t2.project_number is null or '' = t2.project_number,'-',t2.project_number ) order by t2.project_id desc) projectNum,
            group_concat(concat(if(t2.project_id is null ,'',concat(t2.project_number,'-')),t2.project_name)) projectName,
            t1.apply_user_name,
            t1.apply_time,
            t1.type,
            t1.apply_item,
            t1.item_describe,
            t1.seal_type,
            t1.dept_name,
            t1.proof_check_dept
        from
            t_seal_application t1
            left join (select
            t2.apply_id,
            t2.project_id,
            group_concat(distinct if(t2.project_number is null or '' = t2.project_number,'-',t2.project_number )) project_number,
            t2.project_name
            from
            t_seal_application_project t2
            where t2.is_delete = 0
            group by t2.apply_id,t2.project_name,t2.project_id) t2 on t2.apply_id  = t1.id
        where t1.is_delete = 0
            and t1.id = #{id}
        group by t1.id
    </select>
    <select id="queryCompanyInfo" resultType="java.lang.String">
        select group_concat(tc.company_name) from t_company tc  where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>


    <select id="getSealFileBySection" resultType="com.hzw.sunflower.controller.response.SealFilesVO">
        select
            distinct
            t2.section_id,
            t2.bid_round,
            t3.seal_file
        from
            t_seal_application  t1
                left join t_seal_application_project t2 on t2.apply_id = t1.id
                left join t_seal_file t3 on t3.apply_id = t1.id
        where  t1.is_delete  = 0
          and  t2.is_delete  = 0
          and t3.seal_file  is not null
          and t1.approve_status =2
          <if test=" null != req.sectionIds and req.sectionIds.size > 0">
              and t2.section_id in
           <foreach collection="req.sectionIds" item="section" separator="," open="(" close=")">
               #{section}
           </foreach>
          </if>

          and t1.apply_item in (1,2)
          and t1.apply_item = #{req.applyItem}
    </select>

    <select id="getSealFileList"  resultType="com.hzw.sunflower.controller.response.SealFileVO">
        select tsf.*,
           tof1.oss_file_key as originalKey,
           tof1.oss_file_name as originalName,
           tof2.oss_file_key as sealKey,
           tof2.oss_file_name as sealName
        from
             t_seal_file tsf
            left join t_oss_file tof1 on tof1.id = tsf.original_file
            left join t_oss_file tof2 on tof2.id = tsf.seal_file
        where tsf.is_delete = 0
        and tsf.apply_id = #{applyId}
    </select>
    <select id="findName" resultType="java.lang.String">
        select
            group_concat(t3.user_name )
        from
            t_dictionary t1
            left join r_user_role t2 on t1.code = t2.role_id
            left join t_user t3 on t3.id = t2.user_id
        where t1.business_type = 'yygl'
            and t2.is_delete = 0
            and t3.is_delete = 0
    </select>
    <select id="getByNoticeId" resultType="com.hzw.sunflower.entity.SealApplication">
        select distinct
            t1.*
        from
            t_seal_application t1
            left join t_seal_bid_win t2 on t1.id = t2.apply_id
            left join t_bid_win_people t3 on t3.winbid_id = t2.notice_id and t3.win_people_type = 1 and if(t2.bid_win_people_id is null,1=1,t3.id = t2.bid_win_people_id)
        where
            t1.is_delete = 0
        and t2.is_delete = 0
        and t3.is_delete = 0
        and t2.notice_id = #{id}
    </select>
</mapper>