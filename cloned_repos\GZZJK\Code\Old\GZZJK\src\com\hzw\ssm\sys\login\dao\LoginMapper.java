package com.hzw.ssm.sys.login.dao;

import java.util.List;
import java.util.Map;

import com.hzw.ssm.expert.entity.ExpertInfoEntity;
import com.hzw.ssm.sys.project.entity.ProjectEntity;
import com.hzw.ssm.sys.system.entity.MenuEntity;
import com.hzw.ssm.sys.user.entity.UserEntity;

public interface LoginMapper {
	/**
	 * 用户登陆验证
	 * 
	 * @param map
	 *            登陆名和密码
	 * @return 用户对象
	 */
	public UserEntity login(Map<String, String> map);
	
	/**
	 * 登录错误限制
	 */
	public void loginSet(UserEntity user);
	
	/**
	 * 登录成功设置
	 */
	public void loginPass(String userId);

	/**
	 * 获得授权的菜单
	 * @param roleId
	 * @return
	 */
	public List<MenuEntity> getMenuListOfAuth(String roleId);

	public List<ExpertInfoEntity> queryRegisterExpert(String status);
	
	/**
	 * 查询项目数量
	 * @param status
	 * @return
	 */
	public Long queryProjectCount(ProjectEntity project);
	
	/**
	 * 查询用户ID（用于转换机电用户）
	 * @param status
	 * @return
	 */
	public String queryUserId(String userName);
	
	
}
