package com.hzw.sunflower.controller;

import com.hzw.sunflower.common.BaseController;
import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.controller.request.CommonNoticeREQ;
import com.hzw.sunflower.controller.response.PersonalMediaVO;
import com.hzw.sunflower.dao.ProjectBidNoticeMapper;
import com.hzw.sunflower.dto.PersonalMediaDTO;
import com.hzw.sunflower.dto.SimpleBidNoticeDTO;
import com.hzw.sunflower.entity.ProjectBidNotice;
import com.hzw.sunflower.exception.ExceptionEnum;
import com.hzw.sunflower.exception.SunFlowerException;
import com.hzw.sunflower.service.NoticeMediaService;
import com.hzw.sunflower.service.ProjectBidNoticeService;
import com.hzw.sunflower.util.RequestUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * 招标公告关联媒体服务
 * （弃用,转到common模块）
 */
@Api(tags = "招标公告关联媒体信息服务")
//@RestController
@RequestMapping("/bidNotice")
public class NoticeMediaController extends BaseController {

    @Autowired
    private NoticeMediaService mediaService;

    @Autowired
    private ProjectBidNoticeService noticeService;

    @Autowired
    private ProjectBidNoticeMapper tProjectBidNoticeMapper;


    /**
     * （弃用）
     * 参考公共媒体模块
     * @param req
     * @return
     */
    @ApiOperation(value = "用户的曾用媒体信息 (废弃 )")
    @ApiImplicitParam(name = "req", value = "招标公告通用请求实体", required = true,dataType = "CommonNoticeREQ",paramType = "body")
    @PostMapping(value = "/personalMedia")
    public Result<PersonalMediaVO> findPersonalMedias(@RequestBody CommonNoticeREQ req){

        List<PersonalMediaDTO> record = mediaService.findPersonalMedias(getJwtUser().getUserId());
        if(RequestUtil.isNotEmpty(req.getSectionId())){
            List<PersonalMediaDTO> noticeMedias = new ArrayList<>();
            if(RequestUtil.isNotEmpty(req.getNoticeId())){
                noticeMedias = mediaService.findNoticeMedias(req.getNoticeId());
            }else{
                SimpleBidNoticeDTO noticeInfo = tProjectBidNoticeMapper.queryNoticeBySectionId(req.getSectionId());
                if(noticeInfo!=null){
                    noticeMedias = mediaService.findNoticeMedias(noticeInfo.getNoticeId());
                }
            }

            HashMap<String, PersonalMediaDTO> dto = new HashMap<>();
            record.forEach((item) -> {
                dto.put(item.getMediaName(),item);
            });

            noticeMedias.forEach((item) -> {
                if(!dto.containsKey(item.getMediaName())){
                    record.add(item);
                }
            });
        }

        record.sort((a,b) -> {
            return (a.getUseTimes() <= b.getUseTimes()) ? -1 : 1;
        });

        boolean canDelete = true;
        if(RequestUtil.isNotEmpty(req.getNoticeId())){
            ProjectBidNotice notice = noticeService.getById(req.getNoticeId());
            if(notice == null){
                throw  new SunFlowerException(ExceptionEnum.ILLEGAL_PARAMETER_EXCEPTION,null);
            }
            switch(notice.getNoticeProgress()){
                case 2:
                case 4:
                case 5:
                    canDelete = false;
            }
        }

        for (PersonalMediaDTO item : record) {
            item.setCanDelete((item.getIsSystem()) ? false : canDelete);
        }

        PersonalMediaVO vo = new PersonalMediaVO().setMedias(record);

        return Result.ok(vo);
    }
}
