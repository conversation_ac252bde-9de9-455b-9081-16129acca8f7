package com.hzw.sunflower.controller.request;

import com.hzw.sunflower.dto.MediaDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class TenderMediaPicsREQ extends MediaDTO {
    /**
     * 主键
     */
    private Long id;

    @ApiModelProperty(value = "公告id")
    private Long noticeId;

    @ApiModelProperty(value = "媒体id 上传公示上传截图用")
    private Long mediaId;

    @ApiModelProperty(value = "发布截图附件ID集合")
    private List<Long> pics;
}
