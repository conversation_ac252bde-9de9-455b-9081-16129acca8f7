package com.hzw.ssm.expert.service;

import com.alibaba.fastjson.JSON;
import com.hzw.ssm.applets.dao.ExpertDownFileMapper;
import com.hzw.ssm.applets.entity.ExpertDownFile;
import com.hzw.ssm.applets.utils.GetJsonRespData;
import com.hzw.ssm.applets.utils.ResultJson;
import com.hzw.ssm.applets.utils.WxDateUtil;
import com.hzw.ssm.expert.dao.PoliciesDetailsBakMapper;
import com.hzw.ssm.expert.dao.PoliciesDetailsMapper;
import com.hzw.ssm.expert.dao.PoliciesMapper;
import com.hzw.ssm.expert.entity.Policies;
import com.hzw.ssm.expert.entity.PoliciesDetails;
import com.hzw.ssm.expert.entity.PoliciesDetailsBak;
import com.hzw.ssm.sys.user.dao.UserMapper;
import com.hzw.ssm.sys.user.entity.UserEntity;
import com.hzw.ssm.util.string.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
public class PoliciesDetailsService {

    @Autowired
    private PoliciesDetailsMapper policiesDetailsMapper;

    @Autowired
    private PoliciesMapper PoliciesMapper;
    @Autowired
    private UserMapper userMapper;
    @Autowired
    private ExpertDownFileMapper downFileMapper;

    public List<PoliciesDetails> getPagePoliciesDetails(PoliciesDetails policiesDetails) {
        String order = "";
        if (policiesDetails.getCreSort() == null || policiesDetails.getCreSort().trim().length() < 2) {
            if (policiesDetails.getOffSort() == null || policiesDetails.getOffSort().trim().length() < 2) {
                if (policiesDetails.getOnSort() == null || policiesDetails.getOnSort().trim().length() < 2) {
                    if (policiesDetails.getUpdSort() == null || policiesDetails.getUpdSort().trim().length() < 2) {
                        order = "ORDER BY  pd.CREATE_TIME DESC";
                    } else {
                        order = " ORDER BY pd.UPDATE_TIME " + policiesDetails.getUpdSort();
                    }
                } else {
                    order = " ORDER BY   pd.ON_TIME " + policiesDetails.getOnSort();
                }
            } else {
                order = " ORDER BY  pd.OFF_TIME " + policiesDetails.getOffSort();
            }
        } else {
            order = "ORDER BY  pd.CREATE_TIME " + policiesDetails.getCreSort();
        }
        policiesDetails.setOrder(order);


        List<PoliciesDetails> policiesDetailsList = policiesDetailsMapper.queryPagePoliciesDetailsByCondition(policiesDetails);
        for (int i = 0; i < policiesDetailsList.size(); i++) {
            String policiesId = policiesDetailsList.get(i).getPoliciesId();
            Policies policiesById = PoliciesMapper.findPoliciesById(policiesId);
            if (policiesById != null) {
                policiesDetailsList.get(i).setPoliciesId(policiesById.getName());
                policiesDetailsList.get(i).setPstatus(policiesById.getStatus());

            }

            Integer top = policiesDetailsList.get(i).getTop();
            Integer status = policiesDetailsList.get(i).getStatus();
            policiesDetailsList.get(i).setTopMsg(top.intValue() == 1 ? "置顶" : "不置顶");
            policiesDetailsList.get(i).setStatusMsg(status.intValue() == 0 ? "待发布" : status.intValue() == 1 ? "编辑中" : status.intValue() == 2 ? "已发布" : status.intValue() == 3 ? "已下架" : "无");
            String createUser = policiesDetailsList.get(i).getCreateUser();
            String updateUser = policiesDetailsList.get(i).getUpdateUser();
            if (createUser != null && createUser.length() > 0) {
                UserEntity createUserObj = userMapper.selectUserById(createUser);
                if (createUserObj != null) {
                    policiesDetailsList.get(i).setCreateUser(createUserObj.getUser_name());
                }
            }
            if (updateUser != null && updateUser.length() > 0) {
                UserEntity updateUserObj = userMapper.selectUserById(updateUser);
                if (updateUserObj != null) {
                    policiesDetailsList.get(i).setUpdateUser(updateUserObj.getUser_name());
                }
            }


        }


        return policiesDetailsList;
    }


    public Integer addPoliciesDetails(PoliciesDetails policiesDetails) {
        return policiesDetailsMapper.addPoliciesDetails(policiesDetails);
    }


    public Integer updatePoliciesDetails(PoliciesDetails policiesDetails) {
        return policiesDetailsMapper.updatePoliciesDetails(policiesDetails);
    }

    public void byIdDelete(String id) {
        Map result = new HashMap();
        if (id != null && id.length() > 0) {
            Integer integer = policiesDetailsMapper.deletePoliciesDetailsById(id);
            if (integer != 0) {
                result.put("msg", "删除成功!");
                result.put("code", 0);
            } else {
                result.put("msg", "删除失败!");
                result.put("code", 1);
            }
        } else {
            result.put("msg", "当前参数不能为空!");
            result.put("code", 1);
        }
        GetJsonRespData.getJsonRespData(result);
    }

    public void byIdRelease(String id, Boolean byIdReleaseFlag, UserEntity user) {
        Map result = new HashMap();
        if (id != null && id.length() > 0) {
            PoliciesDetails details = policiesDetailsMapper.findPoliciesDetailsById(id);
            if (details != null) {

                if (byIdReleaseFlag) {//发布
                    details.setOnTime(new Date());
                    details.setUpdateUser(user.getUser_id());
                    details.setUpdateTime(new Date());
                    details.setStatus(2);
                    Integer integer = policiesDetailsMapper.updatePoliciesDetails(details);
                    if (integer != 0) {
                        result.put("msg", "发布成功!");
                        result.put("code", 0);
                    } else {
                        result.put("msg", "发布失败!");
                        result.put("code", 1);
                    }
                } else {//下架
                    details.setUpdateUser(user.getUser_id());
                    details.setUpdateTime(new Date());
                    details.setOffTime(new Date());
                    details.setStatus(3);
                    Integer integer = policiesDetailsMapper.updatePoliciesDetails(details);
                    if (integer != 0) {
                        result.put("msg", "下架成功!");
                        result.put("code", 0);
                    } else {
                        result.put("msg", "下架失败!");
                        result.put("code", 1);
                    }
                }

            } else {
                result.put("msg", "当前数据不存在!");
                result.put("code", 1);
            }

        } else {
            result.put("msg", "当前参数不能为空!");
            result.put("code", 1);
        }
        GetJsonRespData.getJsonRespData(result);
    }

    public void byIdOperation(String id, Boolean byIdOperationFlag, UserEntity user) {
        Map result = new HashMap();
        if (id != null && id.length() > 0) {
            PoliciesDetails details = policiesDetailsMapper.findPoliciesDetailsById(id);
            if (details != null) {

                if (byIdOperationFlag) {//置顶
                    details.setOnTime(new Date());
                    details.setUpdateUser(user.getUser_id());
                    details.setUpdateTime(new Date());
                    details.setTop(1);
                    Integer integer = policiesDetailsMapper.updatePoliciesDetails(details);
                    if (integer != 0) {
                        result.put("msg", "置顶成功!");
                        result.put("code", 0);
                    } else {
                        result.put("msg", "置顶失败!");
                        result.put("code", 1);
                    }
                } else {//置顶
                    details.setUpdateUser(user.getUser_id());
                    details.setUpdateTime(new Date());
                    details.setOffTime(new Date());
                    details.setTop(0);
                    Integer integer = policiesDetailsMapper.updatePoliciesDetails(details);
                    if (integer != 0) {
                        result.put("msg", "取消置顶成功!");
                        result.put("code", 0);
                    } else {
                        result.put("msg", "取消置顶失败!");
                        result.put("code", 1);
                    }
                }

            } else {
                result.put("msg", "当前数据不存在!");
                result.put("code", 1);
            }

        } else {
            result.put("msg", "当前参数不能为空!");
            result.put("code", 1);
        }
        GetJsonRespData.getJsonRespData(result);
    }

    public PoliciesDetails getByIdPoliciesDetails(String id) {
        PoliciesDetails policiesDetailsById = policiesDetailsMapper.findPoliciesDetailsById(id);
        List<PoliciesDetails> pagePoliciesDetails = new ArrayList<>();
        pagePoliciesDetails.add(policiesDetailsById);

        return pagePoliciesDetails.get(0);


    }

    public List<PoliciesDetails> getByNamePoliciesDetails(PoliciesDetails policiesDetails) {
        return policiesDetailsMapper.findTitlePoliciesDetailsByCondition(policiesDetails);


    }

    public ResultJson policiesQueryPage(PoliciesDetails policies) {
        List<Map<String, Object>> list = new ArrayList<>();
        List<PoliciesDetails> policiesies = policiesDetailsMapper.queryPagePoliciesDetailsByCondition(policies);
        for (PoliciesDetails details : policiesies) {
            Map<String, Object> map = new HashMap<>(16);
            String beginTime = WxDateUtil.dateToStr(details.getCreateTime(), "yyyy-MM-dd");
            String numberNo = "";
            if (!StringUtils.isEmpty(details.getNumberNo())) {
                numberNo = details.getNumberNo();
            }
            map.put("id", details.getId());
            map.put("top", details.getTop());
            map.put("title", details.getTitle());
            map.put("beginTime", beginTime);
            map.put("src", details.getSrc());
            map.put("numberNo", numberNo);
            list.add(map);
        }
        return new ResultJson(list);
    }

    public ResultJson getOne(PoliciesDetails policies) {
        List<PoliciesDetails> policiesies = new ArrayList<>();
        try {
            PoliciesDetails policiesDetails = policiesDetailsMapper.findPoliciesDetailsById(policies.getId());
            String beginTime = WxDateUtil.dateToStr(policiesDetails.getCreateTime(), "yyyy-MM-dd");
            policiesDetails.setBeginTime(beginTime);

            String fileId = policiesDetails.getFileId();
            ExpertDownFile expertDownFile = downFileMapper.queryByExpertIdAndFileId(fileId, policies.getExpertId());
            int collectionStatus = 0;
            if (null != expertDownFile) {
                collectionStatus = 1;
            }
            policiesDetails.setCollectionStatus(collectionStatus);

            policiesies.add(policiesDetails);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return new ResultJson(policiesies);
    }

    public PoliciesDetails byIdDtails(String id) {
        PoliciesDetails policies = new PoliciesDetails();
        policies.setId(id);
        List<PoliciesDetails> pagePolicies = getPagePoliciesDetails(policies);
        return pagePolicies.get(0);
    }

    private String getModifiedTtem(PoliciesDetailsBak policiesDetailsBak) {
        StringBuffer result = new StringBuffer();
        String before = policiesDetailsBak.getBefore();
        String after = policiesDetailsBak.getAfter();
        PoliciesDetails beforeDetails = JSON.parseObject(before, PoliciesDetails.class);
        PoliciesDetails afterDetails = JSON.parseObject(after, PoliciesDetails.class);


        if (!beforeDetails.getStatus().equals(afterDetails.getStatus())) {
            result.append("(状态)");
        }

        if (!beforeDetails.getSrc().equals(afterDetails.getSrc())) {
            result.append("(来源)");
        }


        if (!beforeDetails.getTitle().equals(afterDetails.getTitle())) {
            result.append("(标题)");
        }

        if (!beforeDetails.getTxt().equals(afterDetails.getTxt())) {
            result.append("(内容)");
        }


        if (!beforeDetails.getTop().equals(afterDetails.getTop())) {
            result.append("(是否置顶)");
        }


        if (!beforeDetails.getFileName().equals(afterDetails.getFileName())) {
            result.append("(文件)");
        }

        if (result.length() < 2) {
            result.append("(未改动提交)");
        }


        return result.toString();
    }

    @Autowired
    private PoliciesDetailsBakMapper policiesDetailsBakService;

    public List<PoliciesDetailsBak> lawDes(String id) {
        PoliciesDetailsBak policiesDetailsBak = new PoliciesDetailsBak();
        policiesDetailsBak.setDetailId(id);
        List<PoliciesDetailsBak> policiesDetailsBakByCondition = policiesDetailsBakService.findPoliciesDetailsBakByCondition(policiesDetailsBak);
        for (int i = 0; i < policiesDetailsBakByCondition.size(); i++) {
            PoliciesDetailsBak policiesDetailsBak1 = policiesDetailsBakByCondition.get(i);
            String modifiedTtem = getModifiedTtem(policiesDetailsBak1);
            PoliciesDetails beforeDetails = JSON.parseObject(policiesDetailsBak1.getBefore(), PoliciesDetails.class);
            PoliciesDetails afterDetails = JSON.parseObject(policiesDetailsBak1.getAfter(), PoliciesDetails.class);
            policiesDetailsBakByCondition.get(i).setBeforeDetails(beforeDetails);
            policiesDetailsBakByCondition.get(i).setAfterDetails(afterDetails);


            policiesDetailsBakByCondition.get(i).setModifiedTtem(modifiedTtem);
            String createUser = policiesDetailsBakByCondition.get(i).getCreateUser();
            String updateUser = policiesDetailsBakByCondition.get(i).getUpdateUser();
            if (createUser != null && createUser.length() > 0) {
                UserEntity createUserObj = userMapper.selectUserById(createUser);
                if (createUserObj != null) {
                    policiesDetailsBakByCondition.get(i).setCreateUser(createUserObj.getUser_name());
                }
            }
            if (updateUser != null && updateUser.length() > 0) {
                UserEntity updateUserObj = userMapper.selectUserById(updateUser);
                if (updateUserObj != null) {
                    policiesDetailsBakByCondition.get(i).setUpdateUser(updateUserObj.getUser_name());
                }
            }
        }
        return policiesDetailsBakByCondition;
    }
}


