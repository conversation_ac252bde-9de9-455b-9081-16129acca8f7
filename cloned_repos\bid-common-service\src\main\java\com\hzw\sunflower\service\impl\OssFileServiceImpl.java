package com.hzw.sunflower.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.controller.response.FileVo;
import com.hzw.sunflower.controller.response.OssKeyVO;
import com.hzw.sunflower.dao.OssFileMapper;
import com.hzw.sunflower.entity.OssFile;
import com.hzw.sunflower.entity.condition.OssFileCondition;
import com.hzw.sunflower.exception.ParamsNotNullException;
import com.hzw.sunflower.service.OssFileService;
import com.hzw.sunflower.util.BeanListUtil;
import com.hzw.sunflower.util.FileUtils;
import com.hzw.sunflower.util.OssUtil;
import com.hzw.sunflower.util.StringUtil;
import com.hzw.sunflower.util.oss.OssFileStorage;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.crypto.Cipher;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.security.KeyFactory;
import java.security.PublicKey;
import java.security.spec.X509EncodedKeySpec;
import java.text.SimpleDateFormat;
import java.util.Base64;
import java.util.Date;
import java.util.List;
import java.util.UUID;


/**
 * OSS附件 Service接口实现
 *
 * <AUTHOR>
 * @version 1.0.0 2021-04-21
 */
@Service
public class OssFileServiceImpl extends ServiceImpl<OssFileMapper, OssFile> implements OssFileService {

    @Value("${oss.active}")
    private String ossType;

    @Value("${files.temporary.path}")
    private String temporary;

    @Value("${oss.ctyun.accessKeyId}")
    private String accessKeyId;

    @Value("${oss.ctyun.accessKeySecret}")
    private String accessKeySecret;

    private static final String AES = "AES";
    private static final String RSA = "RSA";
    private static final String AES_MODE_PADDING = "AES/CBC/PKCS5Padding";

    @Value("${oss.publicKey}")
    private   String base64PublicKey;

    @Override
    public IPage<OssFile> findOssFileByCondition(OssFileCondition condition) throws Exception {
        IPage<OssFile> page = condition.buildPage();

        QueryWrapper<OssFile> queryWrapper = condition.buildQueryWrapper(OssFile.class);

        return this.page(page, queryWrapper);

    }

    @Override
    public OssFile getOssFileById(Long id) {
        return this.getById(id);
    }


    @Override
    public Boolean addOssFile(OssFile ossFile) throws Exception {
        return this.save(ossFile);
    }


    @Override
    public Boolean updateOssFile(OssFile ossFile) throws Exception {
        return this.updateById(ossFile);
    }


    @Override
    public Boolean deleteOssFileById(Long id) throws Exception {
        return this.removeById(id);
    }


    @Override
    public Boolean deleteOssFileByIds(List<Long> idList) throws Exception {
        return this.removeByIds(idList);
    }

    /**
     * 根据fileids查询附件信息
     * @param fileIds   1,2,3,4,5
     * @return
     */
    @Override
    public List<FileVo> getOssFileByFileIds(String fileIds) {
        if(StringUtils.isNotEmpty(fileIds)){
            String[] fileArr = fileIds.split(",");
            LambdaQueryWrapper<OssFile> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.in(OssFile::getId,fileArr);
            List<OssFile> list = this.list(queryWrapper);
            List<FileVo> fileVos = BeanListUtil.convertList(list, FileVo.class);
            return fileVos;
        }
        return null;
    }

//    @Override
//    public Long uploadHtml(String fileString, String fileName) throws Exception {
//        OssUtil ossUtil = new OssUtil();
//        String key = ossUtil.uploadHtml(fileString,fileName);
//        OssFile ossFile = new OssFile();
//        ossFile.setOssFileKey(key);
//        ossFile.setOssFileName(fileName);
//        this.save(ossFile);
//        return ossFile.getId();
//    }


    @Override
    public Long editor2PDF(String content,String fileName,String title) throws Exception {
//        OssUtil ossUtil = new OssUtil();
        fileName = StringUtil.stringFilter(fileName);
        title = StringUtil.stringFilter(title);
        OssFileStorage storage = OssUtil.getOssFileStorage(ossType);
        String key = storage.editor2PDF(content,fileName,title);
        if(StringUtils.isNotBlank(key)) {
            OssFile ossFile = new OssFile();
            ossFile.setOssFileKey(key);
            ossFile.setOssFileName(fileName);
            this.save(ossFile);
            return ossFile.getId();
        }else {
            return null;
        }
    }


    @Override
    public Long saveOssFile(String fileName,String filePath) throws Exception {
//        OssUtil ossUtil = new OssUtil();
        OssFileStorage storage = OssUtil.getOssFileStorage(ossType);
        String key = storage.uploadFileByPath(filePath);
        if(StringUtils.isNotBlank(key)) {
            OssFile ossFile = new OssFile();
            ossFile.setOssFileKey(key);
            ossFile.setOssFileName(fileName);
            this.save(ossFile);
            return ossFile.getId();
        }else {
            return null;
        }
    }

    @Override
    public Long saveOssFileInputStream(String fileName, InputStream stream) throws Exception {
//        OssUtil ossUtil = new OssUtil();
        OssFileStorage storage = OssUtil.getOssFileStorage(ossType);

        String key = storage.uploadFile(UUID.randomUUID()+fileName,stream);
        if(StringUtils.isNotBlank(key)) {
            OssFile ossFile = new OssFile();
            ossFile.setOssFileKey(key);
            ossFile.setOssFileName(fileName);
            this.save(ossFile);
            return ossFile.getId();
        }else {
            return null;
        }
    }

    @Override
    public Long saveOssBase64File(String fileName, String fileContent) {
        OssFileStorage storage = OssUtil.getOssFileStorage(ossType);
        String key = storage.uploadBase64(fileContent, fileName);
        if (StringUtils.isNotBlank(key)) {
            OssFile ossFile = new OssFile();
            ossFile.setOssFileKey(key);
            ossFile.setOssFileName(fileName);
            this.save(ossFile);
            return ossFile.getId();
        } else {
            return null;

        }
    }


    public void outFileByOssKey(HttpServletRequest request, HttpServletResponse response, OssFile ossFile) {
        if(StringUtils.isNotBlank(ossFile.getOssFileKey())){
            String filehend = ossFile.getOssFileKey().substring(0, ossFile.getOssFileKey().lastIndexOf("."));
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
            String time = sdf.format(new Date());
            String outPath = temporary+filehend+time+".pdf";
            if(ossFile.getOssFileKey().indexOf("docx")>-1 || ossFile.getOssFileKey().indexOf("doc")>-1){
                outPath = temporary+filehend+time+".docx";
            }
            OssFileStorage storage = OssUtil.getOssFileStorage(ossType);
            byte[] bytes = storage.downloadFile(ossFile.getOssFileKey());
            BufferedOutputStream bos = null;
            try {
                bos = new BufferedOutputStream(new FileOutputStream(outPath));
                bos.write(bytes);
            } catch (FileNotFoundException e) {
                e.printStackTrace();
            }catch (IOException e) {
                e.printStackTrace();
            }finally {
                try {
                    bos.flush();
                    bos.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
                //写入文件到临时文件
            if(ossFile.getOssFileKey().indexOf("docx")>-1 || ossFile.getOssFileKey().indexOf("doc")>-1){
                outPath = FileUtils.word2PDF(outPath, true);
            }
            try {
                FileUtils.outFile(outPath,"application/pdf",time+".pdf",response);

            } catch (IOException e) {
                e.printStackTrace();
            }finally { // 不管成功与否 都删除临时文件
             File file = new File(outPath);
             file.delete();
            }
        }else{
            throw new ParamsNotNullException("非法参数异常");
        }
    }

    /**
     * 根据文件id集合查询
     * @param ossFileIds
     * @return
     */
    @Override
    public List<OssFile> listByIdsOrder(List<String> ossFileIds) {
        return this.baseMapper.listByIdsOrder(ossFileIds);
    }

    /**
     * 拷贝文件id
     * @param ossId
     * @return
     */
    @Override
    public Long saveCopyOssFile(Long ossId) {
        Long ossid = null;
        OssFile byId = this.getById(ossId);
        byId.setId(null);
        boolean save = this.save(byId);
        if(save){
            ossid = byId.getId();
        }
        return ossid;
    }

    @Override
    public Result<OssKeyVO> getkey() throws Exception {
        OssKeyVO key = new OssKeyVO();

        // 你的 Base64 私钥
        // 将Base64字符串转换为公钥对象
        byte[] publicKeyBytes = Base64.getDecoder().decode(base64PublicKey);
        X509EncodedKeySpec keySpec = new X509EncodedKeySpec(publicKeyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        PublicKey publicKey = keyFactory.generatePublic(keySpec);

        // 使用RSA公钥加密
        Cipher cipher = Cipher.getInstance(RSA);
        cipher.init(Cipher.ENCRYPT_MODE, publicKey);
        byte[] encryptedTextAccessKeySecret = cipher.doFinal(accessKeySecret.getBytes());
        byte[] encryptedTextAccessKeyId = cipher.doFinal(accessKeyId.getBytes());

        // 将结果编码为 Base64
        String encryptedTextAccessKeySecretBase64 = Base64.getEncoder().encodeToString(encryptedTextAccessKeySecret);
        String encryptedTextAccessKeyIdBase64 = Base64.getEncoder().encodeToString(encryptedTextAccessKeyId);

        key.setAccessKeyId(encryptedTextAccessKeyIdBase64);
        key.setAccessKeySecret(encryptedTextAccessKeySecretBase64);
        return Result.ok(key);
    }
}
