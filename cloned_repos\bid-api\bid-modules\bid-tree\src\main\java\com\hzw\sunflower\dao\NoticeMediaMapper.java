package com.hzw.sunflower.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hzw.sunflower.dto.NoticeMediaDTO;
import com.hzw.sunflower.dto.PersonalMediaDTO;
import com.hzw.sunflower.entity.NoticeMedia;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Repository
public interface NoticeMediaMapper extends BaseMapper<NoticeMedia> {

    /**
     * 查询发布截图
     * （弃用）
    */
    List<NoticeMediaDTO> findNoticeMediaWithAnnex(@Param("noticeId") Long noticeId);

    /**
     * 查找与对应公告关联的媒体的ID
     */
    List<Long> findNoticeMediaIds(@Param("noticeId") Long noticeId);

    /**
     * 查询公告自有媒体
     */
    List<PersonalMediaDTO> findNoticeMedias(@Param("noticeId") Long noticeId);

    /**
     * 查询用户自有媒体
     */
    List<PersonalMediaDTO> findPersonalMedias(@Param("userId") Long userId);

    /**
     * 个人媒体使用次数+1
     */
    @Transactional(rollbackFor = Exception.class)
    Integer updateMediaUseTimes(@Param("noticeId") Long noticeId);
}
