package com.hzw.sunflower.service;


/**
 * 项目流水表Service接口
 *
 * <AUTHOR>
 * @version 1.0.0 2021-04-13
 */
public interface ProjectSerialNumService {

    /**
     * 生成采购项目编号
     * 生成规则：JSTCC2100510001
     * 租户简称+年份后两位如2021年则展示为21+三位处室编号005+一位省内省外标识（:省内，2:省外）+四位流水号0001
     *
     * @return
     */
    String generateProjectNum(Integer provinceInOut, Long agentDepartId);


    /**
     * 存根编号
     * 租户简称+年份后两位如2021年则展示为21+三位处室编号005+五位流水号00001
     *
     * @return
     */
    String generateStubNum();

    /**
     * 存根编号
     * 租户简称+年份后两位如2021年则展示为21+三位处室编号005+五位流水号00001
     *
     * @return
     */
    String generateStubNum(Long userId,String purchase_number);


    /**
     * 国际标编号
     * 国际标项目编号生成规则：年份后两位（22）+部门编号（比如021）+四位自增数字。示例：220210001
     * @return
     */
    String getInternationalNumber(Long userId,Integer internationalCode);

    /**
     * 国际标编号(根据部门id生产)
     * 国际标项目编号生成规则：年份后两位（22）+部门编号（比如021）+四位自增数字。示例：220210001
     * @param agentDepartId
     * @param internationalCode
     * @return
     */
    String getInternationalNumberByDeptId(Long agentDepartId,Integer internationalCode);

    /**
     * 自采生成存根编号
     * @param departId
     * @return
     */
    String generateStubNumMyPurchase(Long departId);

    /**
     * 自采项目生成采购项目编号
     * 生成规则：
     * 租户简称+年份后两位如2021年则展示为21+处室编号005+五位流水号0001
     *
     * @return
     */
    String generateProjectNumMyPurchase(String systemAbbreviation);

    /**
     * 获取非招标代理项目编号
     * @return
     */
    String generateNoAgentProjectNum(Long projectManagerDepartId);

    /**
     * 获取非招标代理项目id
     * @return
     */
    Long generateNoAgentProjectId();
}