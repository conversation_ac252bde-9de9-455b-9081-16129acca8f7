#!/bin/sh
#cp TEMPLATE FILE
docker stop bid-auth-server || true
docker rm bid-auth-server || true
docker rmi registry.cn-hangzhou.aliyuncs.com/hzw-jtcc/jtcc:bid-auth-server-1.0.0 || true
docker pull registry.cn-hangzhou.aliyuncs.com/hzw-jtcc/jtcc:bid-auth-server-1.0.0
docker run -itd --net=host  --log-opt max-size=100m --log-opt max-file=3  --name bid-auth-server -v /home/<USER>/plan1/bid-cloud/config/bid-auth-server:/hzw/auth/config -v /data/log:/hzw/auth/logs  registry.cn-hangzhou.aliyuncs.com/hzw-jtcc/jtcc:bid-auth-server-1.0.0
