package com.hzw.sunflower.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzw.sunflower.common.BaseBean;
import com.hzw.sunflower.constant.CommonConstants;
import com.hzw.sunflower.dao.NccSyncFileCallMapper;
import com.hzw.sunflower.entity.NccSyncFileCall;
import com.hzw.sunflower.service.NccSyncFileCallService;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

@Service
public class NccSyncFileCallServiceImpl extends ServiceImpl<NccSyncFileCallMapper, NccSyncFileCall> implements NccSyncFileCallService {

    /**
     * 查询ncc同步文件待调用数据
     * @return
     */
    @Override
    public List<NccSyncFileCall> getNccSyncFileCall() {
        // 文件调用为T+2，查询2天前未调用的数据
        String dateEnd = LocalDateTime.now().minusDays(2).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        // 不查询超过5天的数据，防止错误数据堆积
        String dateStart = LocalDateTime.now().minusDays(5).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        LambdaQueryWrapper<NccSyncFileCall> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(NccSyncFileCall::getIsCall, CommonConstants.NO2);
        queryWrapper.ge(NccSyncFileCall::getCreatedTime, dateStart);
        queryWrapper.le(NccSyncFileCall::getCreatedTime, dateEnd);
        queryWrapper.orderByAsc(BaseBean::getCreatedTime);
        return this.list(queryWrapper);
    }

}
