///***
/// * 我的 - 关于我们
/// */

import 'package:flutter/material.dart';

import 'package:flutter_main_app/src/constants/theme.dart';
import 'package:flutter_main_app/src/utils/ScreenUtils.dart';

import 'package:flutter_main_app/src/components/CustomAppBar.dart';
import 'package:flutter_main_app/src/components/CustomSafeArea.dart';

class ProfileAboutUsIndex extends StatelessWidget {

  const ProfileAboutUsIndex({Key key}) : super(key: key);

  /// * 路由名称
  static const String _routeName = 'ProfileAboutUsIndex';
  static String get routeName => _routeName;

  static const String _corporateName = '江苏省招标投标公共服务平台';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: '关于我们',
      ),
      body: CustomSafeArea(
        backgroundColor: themeContentBackgroundColor,
        child: Stack(
          children: <Widget>[
            /// * 标题
            Positioned(
              left: 0.0,
              top: 0.0,
              child: Container(
                width: ScreenUtils.screenWidth,
                height: ScreenUtils.screenHeight / 3,
                child: Center(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: <Widget>[
                      Image.asset(
                        'assets/images/logo.png',
                        width: 144.0,
                        height: 144.0,
                      ),
                      Text(
                        _corporateName,
                        style: TextStyle(
                          fontSize: 20.0,
                          color: themeTitleColor,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            /// * 具体信息
            Center(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  this._buildAboutUsInfo(
                    fieldName: '公司地址',
                    fieldValue: '江苏省南京市中山路338号苏粮国际大厦18楼',
                  ),
                  this._buildAboutUsInfo(
                    fieldName: '客服咨询部电话',
                    fieldValue: '025-85304657',
                  ),
                  this._buildAboutUsInfo(
                    fieldName: '平台咨询QQ号',
                    fieldValue: '807102828',
                  ),
                  this._buildAboutUsInfo(
                    fieldName: '当前版本',
                    fieldValue: 'V1.0.1',
                  ),
                ],
              ),
            ),
            /// * copyright
            Positioned(
              left: 0.0,
              bottom: 20.0,
              width: ScreenUtils.screenWidth,
              child: Center(
                child: Text(
                  'CopyRight @ $_corporateName 2018 - 2020',
                  style: TextStyle(
                    fontSize: 10.0,
                    color: themeHintTextColor,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  ///***
  /// * 生成关于我们的具体信息
  /// *
  /// * @param {String} fieldName
  /// * @param {String} fieldValue
  /// * @return {Widget}
  /// */
  Widget _buildAboutUsInfo({ String fieldName, String fieldValue }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 6.0),
      child: RichText(
        text: TextSpan(
          text: '$fieldName：',
          style: TextStyle(
            fontSize: 12.0,
            color: themeTitleColor,
          ),
          children: <TextSpan>[
            TextSpan(
              text: fieldValue,
              style: TextStyle(
                color: themeTipsTextColor,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
