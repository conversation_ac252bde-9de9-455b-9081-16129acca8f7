package com.hzw.ssm.fw.uploadify.service;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.hzw.ssm.fw.base.BaseService;
import com.hzw.ssm.fw.uploadify.bean.FileEntity;
import com.hzw.ssm.fw.uploadify.dao.UploadMapper;
import com.hzw.ssm.fw.util.CommUtil;

/**
 * 文件上传操作类
 * <AUTHOR>
 * @date 2014-8-1
 */
@Service
public class UploadService extends BaseService {

	private static Log log = LogFactory.getLog(UploadService.class);
	
	@Autowired
	private UploadMapper uploadMapper;

	/**
	 * 文件上传
	 * @param objFile
	 */
	public String uploadFile(FileEntity objFile){
		objFile.setFileId(CommUtil.getKey());
		uploadMapper.insertFile(objFile);
		return objFile.getFileId();
	}
	
}
