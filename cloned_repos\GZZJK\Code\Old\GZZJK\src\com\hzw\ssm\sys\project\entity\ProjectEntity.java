package com.hzw.ssm.sys.project.entity;

import java.util.Date;
import java.util.List;

import com.hzw.ssm.fw.base.BaseEntity;
import com.hzw.ssm.fw.util.Page;

public class ProjectEntity extends BaseEntity {
	private static final long serialVersionUID = -8151736570527763583L;

	private String projectId;//项目信息主键
	private String projectNo;//项目编号
	private List<String> projectNoList;//项目编号
	private String projectName;//项目名称
	private String manager;//项目负责人
	private String managerName;//项目负责人姓名
	private String phone;//联系电话
	private Date bidTime;//开标时间
	private String bidTime_;
	private String bidAddress;//评标地点
	private String 	bidDuration;//评标时长
	private String agent;//代理机构
	private String tender;//招标人
	private String remark;//备注
	private Long status;//项目状态 (1：待抽取 2:抽取中 3：已抽取 4：指定抽取待审核 5：指定抽取审核通过 6：指定抽取审核不通过  )
	private String status_;
	private Long deleteFlag;//删除标识
	private String createUser;
	private String department;//处室
	private String operator;//经办人
	private String operatorName;//经办人
	private Date bidStartTime;//评标开始时间
	private Date bidEndTime;//评标结束时间
	private String stas;
	
	private Long method;//抽取方式 1:人工通知 2：系统自动短信通知 3：短信语音抽取 4：指定专家
	
	private Long appNum;//已评价数
	private Long totalNum;//需要评价总数
	private String conditionId;//条件id
	
	private String expertId;//专家用户id
	private String expertName;  // 专家名称
	private Integer isFillPump;           // 是否补抽   0:是   1:否
	private String createUsers;	//用户组
	private String id;//结果id
	private String is_appraise;
	private Long isTwoCon;//二次抽取方式
	private String decimationBatch;//抽取流水号
	private Long check_type;//审核方式 1或空:人工审核;2:系统自动审核
	private Long purpose;//专家用途  1评标专家- 2文件评审 - 3资格预审
	private String applyReason;//申请原因
	private String bidStartTimeStr;//评标开始时间String
	private String bidEndTimeStr;//评标结束时间String
	private int expertNum;
	private int treatmentDate;
	/** 分页 */
	private Page page;
	//语音抽取审核状态  1.非工作日审核  2.应急审核
	private String auditType;
	
	private String projectStatus;//语音抽取的项目状态(0.系统自动抽取  1.紧急抽取)
	private String tab;//语音抽取tab页的切换
	private String modifyType;//变更状态 1.变更 2.取消
	private Integer noNotifyNum;//未通知专家的数据
	private Integer queryCount;//抽取次数
	//用户名称
	private String userName;
	//抽取状态
	private String join_status;
	//
	private String[] str;
	//专家人数
	private Long zjCount;
	//抽取次数
	private Long cqCount;
	//实际抽取人数
	private Long sjCount;
	//排序
	private String paixu;

	private String ishandle;
	
	private String handleFile;
	private String examineName;
	
	private String total;
	public String getPaixu() {
		return paixu;
	}

	public void setPaixu(String paixu) {
		this.paixu = paixu;
	}

	public Long getZjCount() {
		return zjCount;
	}

	public void setZjCount(Long zjCount) {
		this.zjCount = zjCount;
	}

	public Long getCqCount() {
		return cqCount;
	}

	public void setCqCount(Long cqCount) {
		this.cqCount = cqCount;
	}

	public Long getSjCount() {
		return sjCount;
	}

	public void setSjCount(Long sjCount) {
		this.sjCount = sjCount;
	}

	public String[] getStr() {
		return str;
	}

	public void setStr(String[] str) {
		this.str = str;
	}

	public Integer getQueryCount() {
		return queryCount;
	}

	public void setQueryCount(Integer queryCount) {
		this.queryCount = queryCount;
	}

	public String getJoin_status() {
		return join_status;
	}
	public void setJoin_status(String join_status) {
		this.join_status = join_status;
	}
	public String getUserName() {
		return userName;
	}
	public void setUserName(String userName) {
		this.userName = userName;
	}
	public String getModifyType() {
		return modifyType;
	}
	public void setModifyType(String modifyType) {
		this.modifyType = modifyType;
	}
	public String getBidEndTimeStr() {
		return bidEndTimeStr;
	}
	public void setBidEndTimeStr(String bidEndTimeStr) {
		this.bidEndTimeStr = bidEndTimeStr;
	}
	public String getBidStartTimeStr() {
		return bidStartTimeStr;
	}
	public void setBidStartTimeStr(String bidStartTimeStr) {
		this.bidStartTimeStr = bidStartTimeStr;
	}
	public String getApplyReason() {
		return applyReason;
	}
	public void setApplyReason(String applyReason) {
		this.applyReason = applyReason;
	}
	public String getDecimationBatch() {
		return decimationBatch;
	}
	public void setDecimationBatch(String decimationBatch) {
		this.decimationBatch = decimationBatch;
	}
	public String getProjectId() {
		return projectId;
	}
	public void setProjectId(String projectId) {
		this.projectId = projectId;
	}
	public String getProjectNo() {
		return projectNo;
	}
	public void setProjectNo(String projectNo) {
		this.projectNo = projectNo==null?projectNo:projectNo.trim();
	}
	public String getProjectName() {
		return projectName;
	}
	public void setProjectName(String projectName) {
		this.projectName = projectName==null?projectName:projectName.trim();
	}
	public String getManager() {
		return manager;
	}
	public void setManager(String manager) {
		this.manager = manager;
	}
	public String getPhone() {
		return phone;
	}
	public void setPhone(String phone) {
		this.phone = phone;
	}
	public String getBidAddress() {
		return bidAddress;
	}
	public void setBidAddress(String bidAddress) {
		this.bidAddress = bidAddress;
	}
	public String getBidDuration() {
		return bidDuration;
	}
	public void setBidDuration(String bidDuration) {
		this.bidDuration = bidDuration;
	}
	public String getAgent() {
		return agent;
	}
	public void setAgent(String agent) {
		this.agent = agent;
	}
	public String getTender() {
		return tender;
	}
	public void setTender(String tender) {
		this.tender = tender==null?tender:tender.trim();
	}
	public String getRemark() {
		return remark;
	}
	public void setRemark(String remark) {
		this.remark = remark;
	}
	public Long getStatus() {
		return status;
	}
	public void setStatus(Long status) {
		this.status = status;
	}
	public Long getDeleteFlag() {
		return deleteFlag;
	}
	public void setDeleteFlag(Long deleteFlag) {
		this.deleteFlag = deleteFlag;
	}
	public Date getBidStartTime() {
		return bidStartTime;
	}
	public void setBidStartTime(Date bidStartTime) {
		this.bidStartTime = bidStartTime;
	}
	public Date getBidEndTime() {
		return bidEndTime;
	}
	public void setBidEndTime(Date bidEndTime) {
		this.bidEndTime = bidEndTime;
	}
	public String getCreateUser() {
		return createUser;
	}
	public void setCreateUser(String createUser) {
		this.createUser = createUser;
	}
	
	public String getStas() {
		return stas;
	}
	public void setStas(String stas) {
		this.stas = stas;
	}
	public Page getPage() {
		return page;
	}
	public void setPage(Page page) {
		this.page = page;
	}
	public Long getMethod() {
		return method;
	}
	public void setMethod(Long method) {
		this.method = method;
	}
	public Long getAppNum() {
		return appNum;
	}
	public void setAppNum(Long appNum) {
		this.appNum = appNum;
	}
	public Long getTotalNum() {
		return totalNum;
	}
	public void setTotalNum(Long totalNum) {
		this.totalNum = totalNum;
	}
	public String getConditionId() {
		return conditionId;
	}
	public void setConditionId(String conditionId) {
		this.conditionId = conditionId;
	}
	public String getExpertId() {
		return expertId;
	}
	public void setExpertId(String expertId) {
		this.expertId = expertId;
	}
	public String getDepartment() {
		return department;
	}
	public void setDepartment(String department) {
		this.department = department;
	}
	public String getOperator() {
		return operator;
	}
	public void setOperator(String operator) {
		this.operator = operator;
	}
	public String getManagerName() {
		return managerName;
	}
	public void setManagerName(String managerName) {
		this.managerName = managerName;
	}
	public String getOperatorName() {
		return operatorName;
	}
	public void setOperatorName(String operatorName) {
		this.operatorName = operatorName;
	}
	public Integer getIsFillPump() {
		return isFillPump;
	}
	public void setIsFillPump(Integer isFillPump) {
		this.isFillPump = isFillPump;
	}
	public String getStatus_() {
		return status_;
	}
	public void setStatus_(String status_) {
		this.status_ = status_;
	}
	public String getExpertName() {
		return expertName;
	}
	public void setExpertName(String expertName) {
		this.expertName = expertName;
	}
	public String getCreateUsers() {
		return createUsers;
	}
	public void setCreateUsers(String createUsers) {
		this.createUsers = createUsers;
	}
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public String getIs_appraise() {
		return is_appraise;
	}
	public void setIs_appraise(String is_appraise) {
		this.is_appraise = is_appraise;
	}
	public Date getBidTime() {
		return bidTime;
	}
	public void setBidTime(Date bidTime) {
		this.bidTime = bidTime;
	}
	public String getBidTime_() {
		return bidTime_;
	}
	public void setBidTime_(String bidTime_) {
		this.bidTime_ = bidTime_;
	}
	public Long getIsTwoCon() {
		return isTwoCon;
	}
	public void setIsTwoCon(Long isTwoCon) {
		this.isTwoCon = isTwoCon;
	}
	public List<String> getProjectNoList() {
		return projectNoList;
	}
	public void setProjectNoList(List<String> projectNoList) {
		this.projectNoList = projectNoList;
	}
	public Long getCheck_type() {
		return check_type;
	}
	public void setCheck_type(Long check_type) {
		this.check_type = check_type;
	}
	public Long getPurpose() {
		return purpose;
	}
	public void setPurpose(Long purpose) {
		this.purpose = purpose;
	}
	public String getProjectStatus() {
		return projectStatus;
	}
	public void setProjectStatus(String projectStatus) {
		this.projectStatus = projectStatus;
	}
	public String getTab() {
		return tab;
	}
	public void setTab(String tab) {
		this.tab = tab;
	}
	public int getTreatmentDate() {
		return treatmentDate;
	}
	public void setTreatmentDate(int treatmentDate) {
		this.treatmentDate = treatmentDate;
	}
	public int getExpertNum() {
		return expertNum;
	}
	public void setExpertNum(int expertNum) {
		this.expertNum = expertNum;
	}
	public String getAuditType() {
		return auditType;
	}
	public void setAuditType(String auditType) {
		this.auditType = auditType;
	}
	public Integer getNoNotifyNum() {
		return noNotifyNum;
	}
	public void setNoNotifyNum(Integer noNotifyNum) {
		this.noNotifyNum = noNotifyNum;
	}

	public String getIshandle() {
		return ishandle;
	}

	public void setIshandle(String ishandle) {
		this.ishandle = ishandle;
	}

	public String getExamineName() {
		return examineName;
	}

	public void setExamineName(String examineName) {
		this.examineName = examineName;
	}

	public String getTotal() {
		return total;
	}

	public void setTotal(String total) {
		this.total = total;
	}

	public String getHandleFile() {
		return handleFile;
	}

	public void setHandleFile(String handleFile) {
		this.handleFile = handleFile;
	}

	
}
