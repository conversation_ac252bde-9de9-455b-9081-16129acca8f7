package com.hzw.ssm.fw.util;

/*******************************************************************************
 * Description：消息内容数据宏定义
 * 
 * @file: MessageConstants.java
 * 
 * @version:1.0.0 Copyright
 ******************************************************************************/
public interface MessageConstants {
	/** 参数名称 */
	public final static String SAMPLE_DEPARTMENT_NAME = "存在相同的部门名称！";
	public final static String SAMPLE_ROLE_NAME = "存在相同的角色名称！";

	public final static String SAVE_SUCCESS = "信息保存成功！";
	public final static String SAVE_FAILED = "信息保存失败！";
	
	public final static String EXAMINE_SUCCESS = "信息提交审核成功！";
	public final static String EXAMINE_FAIL = "信息提交审核失败！";
	
	public final static String DELETE_SUCCESS = "信息删除成功！";
	public final static String DELETE_FAILED = "信息删除失败！";
	
	public final static String NO_AUTH = "您没有任何权限！";
	
	public final static String MESSAGE_BOARD_EXPORT_FILE_NAME="留言板信息记录";
	
	public final static String PUBLISH_SUCCESS = "信息发布成功！";
	public final static String PUBLISH_FAILED = "信息发布失败！";
	
	public final static String CANCEL_SUCCESS = "信息撤销成功！";
	public final static String CANCEL_FAILED = "信息撤销失败！";
	
	public final static String APPLY_SUCCESS = "申请成功！";
	public final static String APPLY_FAILED = "申请失败！";
	
	public final static String NOT_ENOUGH_EXPERT="符合条件的专家人数不足！";
	public final static String NOT_ENOUGH_SENIOR_EXPERT="符合条件的国家级专家人数不足！";
	public final static String NOT_ENOUGH_PLACE_EXPERT="符合条件的地方级专家人数不足！";
	public final static String LT_ENOUGH_SENIOR_EXPERT="已同意参加的人数已经大于目前需要的人数！";
	
	
	public final static String NOT_ATTENT_REASON="未填写不参加理由";
	/**
	 * web service系统错误码
	 */
	public final static String ERROR_0000="0000";
	public final static String ERROR_0000_MSG="成功";
	
	public final static String ERROR_0001="0001";
	public final static String ERROR_0001_MSG="平台不存在";
	public final static String ERROR_0002="0002";
	public final static String ERROR_0002_MSG="秘钥错误";
	public final static String ERROR_0003="0003";
	public final static String ERROR_0003_MSG="时间戳格式错误";
	public final static String ERROR_0004="0004";
	public final static String ERROR_0004_MSG="项目编号不存在";
	public final static String ERROR_0005="0005";
	public final static String ERROR_0005_MSG="项目名称不存在";
	public final static String ERROR_0006="0006";
	public final static String ERROR_0006_MSG="委托单位不能为空";
	public final static String ERROR_0007="0007";
	public final static String ERROR_0007_MSG="开标时间必须是开标前一个工作日";
	public final static String ERROR_0007_01="0007_01";
	public final static String ERROR_0007_01_MSG="开标时间必须是开标前一个工作日的正常工作时间内(08-18点)";
	public final static String ERROR_0008="0008";
	public final static String ERROR_0008_MSG="专家人数是否符合";
	public final static String ERROR_0009="0009";
	public final static String ERROR_0009_MSG="专业超过20个";
	public final static String ERROR_0010="0010";  //对应 NOT_ENOUGH_EXPERT
	public final static String ERROR_0011="0011";  //对应 NOT_ENOUGH_SENIOR_EXPERT
	public final static String ERROR_0012="0012";  //对应 NOT_ENOUGH_PLACE_EXPERT
	public final static String ERROR_0013="0013";
	public final static String ERROR_0013_MSG="抽取序号输入异常";
	public final static String ERROR_0014="0014";
	public final static String ERROR_0014_MSG="抽取条件信息不存在";
	public final static String ERROR_0015="0015";
	
	public final static String ERROR_0015_MSG="项目信息不存在";
	public final static String ERROR_0021="0021";
	public final static String ERROR_0021_MSG="待抽取";
	public final static String ERROR_0022="0022";
	public final static String ERROR_0022_MSG="抽取中";
	public final static String ERROR_0023="0023";
	public final static String ERROR_0023_MSG="指定抽取待审核";
	public final static String ERROR_0024="0024";
	public final static String ERROR_0024_MSG="提交领导-处长指定抽取审核通过";
	public final static String ERROR_0025="0025";
	public final static String ERROR_0025_MSG="处长指定抽取审核不通过";
	public final static String ERROR_0026="0026";
	public final static String ERROR_0026_MSG="领导审核通过";
	public final static String ERROR_0027="0027";
	public final static String ERROR_0027_MSG="领导审核不通过";
	
	public final static String ERROR_0101="0101";
	public final static String ERROR_0101_MSG="抽取条件入库失败";
	public final static String ERROR_9999="9999";
	public final static String ERROR_9999_MSG="未知错误，请详细检查报文内容";
	
	public final static String ERROR_1111_MSG="获取批次号为空";
	public final static String ERROR_1110_MSG="当前专家并没有同意参加此项目";
	public final static String ERROR_1113_MSG="填写理由为空";
	public class ErrorType{
		private String code;
		private String msg;
		public String getCode() {
			return code;
		}
		public void setCode(String code) {
			this.code = code;
		}
		public String getMsg() {
			return msg;
		}
		public void setMsg(String msg) {
			this.msg = msg;
		}
		
	}
}