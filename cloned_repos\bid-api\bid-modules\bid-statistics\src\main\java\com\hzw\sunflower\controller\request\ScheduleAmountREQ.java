package com.hzw.sunflower.controller.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class ScheduleAmountREQ {

    @ApiModelProperty(value = "当前月 %y%m(202305)")
    private String month;

    @ApiModelProperty(value = "用户筛选")
    private List<ScheduleUserListREQ> userList;

    @ApiModelProperty(value = "当前时间 %y%m(2023-05-31)")
    private String day;

}
