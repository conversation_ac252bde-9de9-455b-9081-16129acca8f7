package com.hzw.sunflower.constant.constantenum;

/**
 * 采购方式编码
 */
public enum BidPurchaseModeConfigEnum {

    REQUIRED_ACCORDING("924090", "依法必招"),
    REQUIRED_ACCORDING_924091("924091", "依法必招-招标"),

    NOT_REQUIRED_ACCORDING("924092", "非依法必招"),
    NOT_REQUIRED_ACCORDING_924093("924093", "非依法必招-招标"),
    NOT_REQUIRED_ACCORDING_924094("924094", "非依法必招-竞争性磋商"),
    NOT_REQUIRED_ACCORDING_924095("924095", "非依法必招-竞争性谈判"),
    NOT_REQUIRED_ACCORDING_924096("924096", "非依法必招-询价"),
    NOT_REQUIRED_ACCORDING_924097("924097", "非依法必招-征询"),
    NOT_REQUIRED_ACCORDING_924098("924098", "非依法必招-单一来源"),
    NOT_REQUIRED_ACCORDING_924099("924099", "非依法必招-其他"),

    GOVERNMENT_PROCUREMENT("924100", "政府采购"),
    GOVERNMENT_PROCUREMENT_924101("924101", "政府采购-招标"),
    GOVERNMENT_PROCUREMENT_924102("924102", "政府采购-竞争性磋商"),
    GOVERNMENT_PROCUREMENT_924103("924103", "政府采购-竞争性谈判"),
    GOVERNMENT_PROCUREMENT_924104("924104", "政府采购-询价"),
    GOVERNMENT_PROCUREMENT_924105("924105", "政府采购-单一来源"),
    GOVERNMENT_PROCUREMENT_924106("924106", "政府采购-其他"),

    INTERNATIONAL_BIDDING("924107", "国际招标"),
    INTERNATIONAL_BIDDING_924108("924108", "国际招标-招标");

    private String type;

    private String desc;

    BidPurchaseModeConfigEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

}
