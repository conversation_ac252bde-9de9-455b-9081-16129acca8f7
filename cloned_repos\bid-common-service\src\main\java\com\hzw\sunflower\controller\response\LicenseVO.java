package com.hzw.sunflower.controller.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class LicenseVO {
    /**
     * 版本 正式版 试用版
     */
    @ApiModelProperty(value = "版本")
    private String version;

    /**
     * 功能 基础版 专业版 旗舰版
     */
    @ApiModelProperty(value = "功能")
    private String function;

    /**
     * 系统有效期
     */
    @ApiModelProperty(value = "系统有效期")
    private String expireDate;

    /**
     * 剩余天数
     */
    @ApiModelProperty(value = "剩余天数")
    private String leftTime;


    /**
     * 提示信息
     */
    @ApiModelProperty(value = "剩余时间提示信息")
    private String toast;
}
