package com.hzw.sunflower.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hzw.sunflower.controller.response.ProjectPromiseFileVo;
import com.hzw.sunflower.entity.ProjectPromiseFile;
import com.hzw.sunflower.entity.condition.ProjectPromiseFileCondition;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2021/8/27 11:28
 * @description： 项目承诺文件Mapper接口
 * @version: 1.0
 */
public interface ProjectPromiseFileMapper extends BaseMapper<ProjectPromiseFile> {

    /**
     * 分页查询
     * @param page
     * @param condition
     * @return
     */
    IPage<ProjectPromiseFileVo> listPage(IPage<ProjectPromiseFileVo> page, @Param("condition") ProjectPromiseFileCondition condition);

    /**
     * 根据项目查询所有承诺文件
     *
     * @param projectIds
     * @param conferenceId
     * @return
     */
    List<ProjectPromiseFileVo> listSign(@Param("projectIds") List<Long> projectIds, @Param("userId") Long userId, @Param("conferenceId")Long conferenceId);

    /**
     * 查询专家在项目中的评审身份
     * @param conferenceId
     * @param userId
     * @return
     */
    String selectExpertTypes(@Param("conferenceId") Long conferenceId, @Param("userId") Long userId);

    /**
     * 查询会议室关联专家参与的评审
     * @param conferenceId
     * @param userId
     * @return
     */
    List<Long> getProjectIdsByConferenceId(@Param("conferenceId") Long conferenceId, @Param("userId") Long userId);
}
