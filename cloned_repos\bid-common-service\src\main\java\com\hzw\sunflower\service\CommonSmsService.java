package com.hzw.sunflower.service;

import com.hzw.sunflower.controller.response.SmsReplyVo;
import com.hzw.sunflower.dto.SendSmsDto;
import com.hzw.sunflower.entity.ScheduledTaskRetry;

/**
 * <AUTHOR>
 * @date ：Created in 2021/6/29 16:40
 * @description：短信公共业务
 * @modified By：`
 * @version: 1.0
 */
public interface CommonSmsService {

    /**
     * 发送短信
     *
     * @param dto
     * @return
     */
    Boolean sendSms(SendSmsDto dto);

    /**
     * 异常发送短信（自用）
     *
     * @param retry
     * @return
     */
    void smsExceptionSend(ScheduledTaskRetry retry);

    /**
     * 查询短信余额
     * @return
     */
    String getBalance();

    /**
     * 查询JSTCC短信余额
     * @return
     */
    Integer getBalanceJSTCC();

    /**
     * 查询JSTCC测试短信余额
     * @return
     */
    Integer getBalanceTestJSTCC();

    /**
     * 查询CWS测试短信余额
     * @return
     */
    Integer getBalanceCWS();


    /**
     * 获取回复记录
     * @param msgId
     * @return
     */
    SmsReplyVo getReplyRecordInto(String msgId);
}
