package com.hzw.ssm.applets.service;

import com.hzw.ssm.applets.dao.DictionaryMapper;
import com.hzw.ssm.applets.dao.EducationInfoMapper;
import com.hzw.ssm.applets.entity.Dictionary;
import com.hzw.ssm.applets.entity.EducationInfo;
import com.hzw.ssm.applets.exception.ExceptionEnum;
import com.hzw.ssm.applets.utils.ListToMap;
import com.hzw.ssm.applets.utils.RandomCode;
import com.hzw.ssm.applets.utils.ResultJson;
import com.hzw.ssm.expert.dao.ExpertInfoMapper;
import com.hzw.ssm.expert.entity.ExpertInfoEntity;
import com.hzw.ssm.expert.entity.ExpertUpdateRecordEntity;
import com.hzw.ssm.expert.service.ExpertInfoService;
import com.hzw.ssm.fw.base.BaseService;
import com.hzw.ssm.fw.util.CommUtil;
import com.hzw.ssm.sys.user.dao.UserMapper;
import com.hzw.ssm.sys.user.entity.UserEntity;
import com.hzw.ssm.util.string.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <AUTHOR>
 * @Date 2021-02-01 16:04
 * @Version 1.0
 */
@Service
public class EducationInfoService extends BaseService {

    private static Logger logger = Logger.getLogger(EducationInfoService.class);

    @Autowired
    private EducationInfoMapper educationInfoMapper;
    @Autowired
    private DictionaryMapper dictionaryMapper;
    @Autowired
    private ExpertInfoMapper expertInfoMapper;

    @Autowired
    private ExpertInfoService expertInfoService;
    @Autowired
    private UserMapper userMapper;


    public ResultJson insertEducationInfo(EducationInfo educationInfo) {

        try {
            //每个学历只允许添加一条记录
//            EducationInfo info = new EducationInfo();
//            info.setExpertId(educationInfo.getExpertId());
//            info.setEducation(educationInfo.getEducation());
//            info = educationInfoMapper.queryEducationInfo(info);
//            if (null != info) {
//                return new ResultJson(ExceptionEnum.EDUCATION_EXIST.getCode(), ExceptionEnum.EDUCATION_EXIST.getMessage());
//            }

            List<Map<String, Object>> list = new ArrayList<>();
            Map<String, Object> map = new HashMap<>();

            String id = RandomCode.getId();
            educationInfo.setId(id);
            educationInfo.setDeleteFlag(0);
            String educationCertificate = educationInfo.getEducationCertificate();
            String name = educationCertificate.substring(educationCertificate.lastIndexOf("/") + 1);
            educationInfo.setEducationCertificateName(name);
            String academicCertificate = educationInfo.getAcademicCertificate();
            if (!StringUtils.isEmpty(academicCertificate)) {
                String subName = academicCertificate.substring(academicCertificate.lastIndexOf("/") + 1);
                educationInfo.setAcademicCertificateName(subName);
            }
            int updates = educationInfoMapper.insertEducationInfo(educationInfo);
            if (updates > 0) {
                // 如果用户已审核通过，修改需要重新提交审核
                ExpertInfoEntity infoEntity = new ExpertInfoEntity();
                infoEntity.setUser_id(educationInfo.getExpertId());
                ExpertInfoEntity entity = expertInfoMapper.getExpertInfoByUserIdOrId(infoEntity);
                Long status = entity.getStatus();
                if (3 == status || 8 == status) {
                    entity.setStatus(10L);
                    expertInfoMapper.updateExpertInfoById(entity);
                }
                //新增记录
                if (3 == status || 8 == status || 4 == status || 10 == status) {
                    try {
                        infoSaveRecord(educationInfo);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
                map.put("id", id);
                list.add(map);
                return new ResultJson(list);
            }
        } catch (Exception e) {
            logger.error(e.getMessage());
        }
        return new ResultJson(ExceptionEnum.SERVER_IS_BUSY.getCode(), ExceptionEnum.SERVER_IS_BUSY.getMessage());
    }

    public ResultJson updateEducationInfo(EducationInfo educationInfo) {

        try {
            //每个学历只允许添加一条记录
//            EducationInfo info = new EducationInfo();
            String expertId = educationInfo.getExpertId();
//            info.setExpertId(expertId);
//            info.setEducation(educationInfo.getEducation());
//            info = educationInfoMapper.queryEducationInfo(info);
//            if (null != info && !info.getId().equals(educationInfo.getId())) {
//                return new ResultJson(ExceptionEnum.EDUCATION_EXIST.getCode(), ExceptionEnum.EDUCATION_EXIST.getMessage());
//            }

            EducationInfo keyId = new EducationInfo();
            keyId.setId(educationInfo.getId());
            EducationInfo ckey = educationInfoMapper.getEducationInfo(keyId);
            List<Map<String, Object>> list = new ArrayList<>();
            Map<String, Object> map = new HashMap<>();
            educationInfo.setUpdateTime(new Date());

            String educationCertificate = educationInfo.getEducationCertificate();
            String name = educationCertificate.substring(educationCertificate.lastIndexOf("/") + 1);
            educationInfo.setEducationCertificateName(name);
            String academicCertificate = educationInfo.getAcademicCertificate();
            if (!StringUtils.isEmpty(academicCertificate)) {
                String subName = academicCertificate.substring(academicCertificate.lastIndexOf("/") + 1);
                educationInfo.setAcademicCertificateName(subName);
            }
            int updates = educationInfoMapper.updateEducationInfo(educationInfo);
            if (updates > 0) {
                // 如果用户已审核通过，修改需要重新提交审核
                ExpertInfoEntity infoEntity = new ExpertInfoEntity();
                infoEntity.setUser_id(expertId);
                ExpertInfoEntity entity = expertInfoMapper.getExpertInfoByUserIdOrId(infoEntity);
                Long status = entity.getStatus();
                if (3 == status || 8 == status) {
                    entity.setStatus(10L);
                    expertInfoMapper.updateExpertInfoById(entity);
                }
                // 修改记录
                if (3 == status || 8 == status || 4 == status || 10 == status) {
                    try {
                        infoRecord(ckey, educationInfo);
                    } catch (Exception e) {
                        System.out.println("记录出错!");
                    }
                }

                map.put("id", educationInfo.getId());
                list.add(map);
                return new ResultJson(list);
            }
        } catch (Exception e) {
            logger.error(e.getMessage());
        }
        return new ResultJson(ExceptionEnum.SERVER_IS_BUSY.getCode(), ExceptionEnum.SERVER_IS_BUSY.getMessage());

    }


    private void infoSaveRecord(EducationInfo after) {
        UserEntity userEntity = userMapper.selectUserById(after.getExpertId());
        String role_id;
        if (userEntity != null) {
            role_id = StringUtils.toStringNull(userEntity.getRole_id());
        } else {
            role_id = "无";
        }
        addExpertUpdateRecord(after.getExpertId(), "毕业学校", null, StringUtils.toStringNull(after.getGraduateSchool()), role_id, after.getExpertId(), "学历信息", 0);
        addExpertUpdateRecord(after.getExpertId(), "毕业所学专业", null, StringUtils.toStringNull(after.getMajor()), role_id, after.getExpertId(), "学历信息", 0);
        // 学历
        Map<String, String> eduMap = new HashMap<String, String>();
        eduMap.put("-1", "请选择");
        eduMap.put("1", "初中");
        eduMap.put("2", "高中");
        eduMap.put("3", "大专");
        eduMap.put("4", "本科");
        eduMap.put("5", "硕士");
        eduMap.put("6", "博士");
        addExpertUpdateRecord(after.getExpertId(), "毕业学历", null, StringUtils.toStringNull(eduMap.get(after.getEducation())), role_id, after.getExpertId(), "学历信息", 0);
        if (StringUtils.isNotEmpty(after.getAcademicDegree())) {
            Map<String, String> degreeMap = new HashMap<String, String>();
            degreeMap.put("-1", "请选择");
            degreeMap.put("1", "学士");
            degreeMap.put("2", "硕士");
            degreeMap.put("3", "博士");
            addExpertUpdateRecord(after.getExpertId(), "毕业学位", null, StringUtils.toStringNull(degreeMap.get(after.getAcademicDegree())), role_id, after.getExpertId(), "学历信息", 0);
        }

        String afterStyle = "<a style=\"color:blue\" onclick=\"javascript:picPreview(530,450,'/GZZJK/" + StringUtils.toStringNull(after.getEducationCertificate()) + "')\">" + StringUtils.toStringNull(after.getEducationCertificateName()) + "</a>";
        addExpertUpdateRecord(after.getExpertId(), "毕业学历证书", null, StringUtils.toStringNull(afterStyle), role_id, after.getExpertId(), "学历信息", 0);
        if (!StringUtils.isEmpty(after.getAcademicCertificateName()) && !StringUtils.isEmpty(after.getAcademicCertificate())) {
            String afterAcademicCertificate = "<a style=\"color:blue\" onclick=\"javascript:picPreview(530,450,'/GZZJK/" + StringUtils.toStringNull(after.getAcademicCertificate()) + "')\">" + StringUtils.toStringNull(after.getAcademicCertificateName()) + "</a>";
            addExpertUpdateRecord(after.getExpertId(), "毕业学位证书", null, afterAcademicCertificate, role_id, after.getExpertId(), "学历信息", 0);
        }
    }

    private void deleteEducationInfoRecord(EducationInfo info) {
        UserEntity userEntity = userMapper.selectUserById(info.getExpertId());
        String graduateSchool = info.getGraduateSchool();
        String content_after = "删除了【" + graduateSchool + "】的学历信息";
        addExpertUpdateRecord(info.getExpertId(), "学历信息", null, content_after, userEntity.getRole_id(), info.getExpertId(), "学历信息", 0);
    }

    private void infoRecord(EducationInfo before, EducationInfo after) {


        if (before != null && after != null) {

            UserEntity userEntity = userMapper.selectUserById(before.getExpertId());
            String role_id;
            if (userEntity != null) {
                role_id = StringUtils.toStringNull(userEntity.getRole_id());
            } else {
                role_id = "无";
            }

            if (StringUtils.isNotEmpty(after.getGraduateSchool()) && !StringUtils.toStringNull(before.getGraduateSchool()).equalsIgnoreCase(StringUtils.toStringNull(after.getGraduateSchool()))) {
                //毕业学校

                addExpertUpdateRecord(after.getExpertId(), "毕业学校", StringUtils.toStringNull(before.getGraduateSchool()), StringUtils.toStringNull(after.getGraduateSchool()), role_id, after.getExpertId(), "学历信息", 0);

            }
            if (StringUtils.isNotEmpty(after.getMajor()) && !StringUtils.toStringNull(before.getMajor()).equalsIgnoreCase(StringUtils.toStringNull(after.getMajor()))) {
                // 所学专业
                addExpertUpdateRecord(after.getExpertId(), "毕业所学专业", StringUtils.toStringNull(before.getMajor()), StringUtils.toStringNull(after.getMajor()), role_id, after.getExpertId(), "学历信息", 0);

            }
            if (StringUtils.isNotEmpty(after.getEducation()) && !StringUtils.toStringNull(before.getEducation()).equalsIgnoreCase(StringUtils.toStringNull(after.getEducation()))) {
                // 学历
                Map<String, String> eduMap = new HashMap<String, String>();
                eduMap.put("-1", "请选择");
                eduMap.put("1", "初中");
                eduMap.put("2", "高中");
                eduMap.put("3", "大专");
                eduMap.put("4", "本科");
                eduMap.put("5", "硕士");
                eduMap.put("6", "博士");
                addExpertUpdateRecord(after.getExpertId(), "毕业学历", StringUtils.toStringNull(eduMap.get(before.getEducation())), StringUtils.toStringNull(eduMap.get(after.getEducation())), role_id, after.getExpertId(), "学历信息", 0);
            }
            if (StringUtils.isNotEmpty(after.getAcademicDegree()) && !StringUtils.toStringNull(before.getAcademicDegree()).equalsIgnoreCase(StringUtils.toStringNull(after.getAcademicDegree()))) {
                // 学位
                Map<String, String> degreeMap = new HashMap<String, String>();
                degreeMap.put("-1", "请选择");
                degreeMap.put("1", "学士");
                degreeMap.put("2", "硕士");
                degreeMap.put("3", "博士");
                addExpertUpdateRecord(after.getExpertId(), "毕业学位", StringUtils.toStringNull(degreeMap.get(before.getAcademicDegree())), StringUtils.toStringNull(degreeMap.get(after.getAcademicDegree())), role_id, after.getExpertId(), "学历信息", 0);
            }
            if (StringUtils.isNotEmpty(after.getEducationCertificate()) && !StringUtils.toStringNull(before.getEducationCertificate()).equalsIgnoreCase(StringUtils.toStringNull(after.getEducationCertificate()))) {
                // 学历证书

                String befoStyle = "无";
                if (!org.apache.commons.lang.StringUtils.isEmpty(before.getEducationCertificateName())) {
                    befoStyle = "<a style=\"color:blue\" onclick=\"javascript:picPreview(530,450,'/GZZJK/" + com.hzw.ssm.util.string.StringUtils.toStringNull(before.getEducationCertificate()) + "')\">" + com.hzw.ssm.util.string.StringUtils.toStringNull(before.getEducationCertificateName()) + "</a>";
                }
                String afterStyle = "<a style=\"color:blue\" onclick=\"javascript:picPreview(530,450,'/GZZJK/" + StringUtils.toStringNull(after.getEducationCertificate()) + "')\">" + StringUtils.toStringNull(after.getEducationCertificateName()) + "</a>";
                addExpertUpdateRecord(after.getExpertId(), "毕业学历证书", StringUtils.toStringNull(befoStyle), StringUtils.toStringNull(afterStyle), role_id, after.getExpertId(), "学历信息", 0);
            }

            if (StringUtils.isNotEmpty(after.getAcademicCertificate()) && !StringUtils.toStringNull(before.getAcademicCertificate()).equalsIgnoreCase(StringUtils.toStringNull(after.getAcademicCertificate()))) {
                // 学位证书
                String befoStyle = "无";
                if (!org.apache.commons.lang.StringUtils.isEmpty(before.getAcademicCertificateName())) {
                    befoStyle = "<a style=\"color:blue\" onclick=\"javascript:picPreview(530,450,'/GZZJK/" + com.hzw.ssm.util.string.StringUtils.toStringNull(before.getAcademicCertificate()) + "')\">" + com.hzw.ssm.util.string.StringUtils.toStringNull(before.getAcademicCertificateName()) + "</a>";
                }
                if (StringUtils.isNotEmpty(after.getAcademicCertificateName())) {
                    String afterStyle = "<a style=\"color:blue\" onclick=\"javascript:picPreview(530,450,'/GZZJK/" + StringUtils.toStringNull(after.getAcademicCertificate()) + "')\">" + StringUtils.toStringNull(after.getAcademicCertificateName()) + "</a>";
                    addExpertUpdateRecord(after.getExpertId(), "毕业学位证书", StringUtils.toStringNull(befoStyle), StringUtils.toStringNull(afterStyle), role_id, after.getExpertId(), "学历信息", 0);
                }
            }

        }
    }

    /**
     * 新增专家信息修改记录
     *
     * @param user_id        用户编号
     * @param item           修改项
     * @param content_before 修改前内容
     * @param content_after  修改后内容
     * @param modify_role    修改角色
     * @param modify_user    修改人
     * @param modify_reason  修改原因
     * @param batch_number   修改批次编号
     */
    public void addExpertUpdateRecord(String user_id, String item, String content_before, String content_after, String modify_role, String modify_user, String modify_reason, int batch_number) {
        ExpertUpdateRecordEntity record = new ExpertUpdateRecordEntity();
        record.setId(CommUtil.getKey());
        record.setUser_id(user_id);
        record.setItem(item);
        record.setContent_before(content_before);
        record.setContent_after(content_after);
        record.setModify_role(modify_role);
        record.setModify_user(modify_user);
        record.setModify_reason(modify_reason);
        record.setBatch_number(batch_number);
        record.setModify_time(new Date());
        record.setDelete_flag(0);
        expertInfoMapper.addExpertUpdateRecord(record);
    }

    public ResultJson deleteEducationInfo(EducationInfo educationInfo) {

        try {
            List<Map<String, Object>> list = new ArrayList<>();
            Map<String, Object> map = new HashMap<>();
            EducationInfo info = educationInfoMapper.getEducationInfo(educationInfo);

            int updates = educationInfoMapper.deleteEducationInfo(educationInfo);
            if (updates > 0) {
                // 如果用户已审核通过，修改需要重新提交审核
                ExpertInfoEntity infoEntity = new ExpertInfoEntity();
                infoEntity.setUser_id(info.getExpertId());
                ExpertInfoEntity entity = expertInfoMapper.getExpertInfoByUserIdOrId(infoEntity);
                Long status = entity.getStatus();
                if (3 == status || 8 == status) {
                    entity.setStatus(10L);
                    expertInfoMapper.updateExpertInfoById(entity);
                }
                // 删除记录
                if (3 == status || 8 == status || 4 == status || 10 == status) {
                    try {
                        deleteEducationInfoRecord(info);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
                map.put("id", educationInfo.getId());
                list.add(map);
                return new ResultJson(list);
            }
        } catch (Exception e) {
            logger.error(e.getMessage());
        }
        return new ResultJson(ExceptionEnum.SERVER_IS_BUSY.getCode(), ExceptionEnum.SERVER_IS_BUSY.getMessage());
    }

    public ResultJson getEducationInfo(EducationInfo educationInfo) {

        Dictionary dictionary = new Dictionary();
        dictionary.setDataType("education");
        List<Dictionary> dictionaries = dictionaryMapper.queryByType(dictionary);
        Map<String, Object> educationMap = new HashMap<>();
        Map<String, Object> dataDescMap = new HashMap<>();
        if (null != dictionaries && dictionaries.size() > 0) {
            educationMap = ListToMap.listToMap(dictionaries);
            for (Dictionary item : dictionaries) {
                dataDescMap.put(item.getDataCode(), item.getDataDesc());
            }
        }
        Map<String, Object> academiDegreeMap = dictionaryUtils("academi_degree");

        List<EducationInfo> list = new ArrayList<>();
        EducationInfo education = educationInfoMapper.getEducationInfo(educationInfo);

        String educationValue = String.valueOf(educationMap.get(education.getEducation()));
        education.setEducationValue(educationValue);
        String dataDesc = String.valueOf(dataDescMap.get(education.getEducation()));
        education.setDataDesc(dataDesc);

        String academicDegree = education.getAcademicDegree();
        education.setAcademicDegreeValue(null);
        if (!StringUtils.isEmpty(academicDegree)) {
            boolean containsKey = academiDegreeMap.containsKey(academicDegree);
            if (containsKey) {
                String academicDegreeValue = String.valueOf(academiDegreeMap.get(academicDegree));
                education.setAcademicDegreeValue(academicDegreeValue);
            }
        }

        list.add(education);
        return new ResultJson(list);

    }

    public ResultJson getEducationInfos(EducationInfo educationInfo) {

        Map<String, Object> educationMap = dictionaryUtils("education");
        Map<String, Object> academiDegreeMap = dictionaryUtils("academi_degree");

        List<EducationInfo> educationInfos = educationInfoMapper.getEducationInfos(educationInfo);
        for (EducationInfo info : educationInfos) {
            String educationValue = String.valueOf(educationMap.get(info.getEducation()));
            info.setEducationValue(educationValue);

            String academicDegree = info.getAcademicDegree();
            info.setAcademicDegreeValue(null);
            if (!StringUtils.isEmpty(academicDegree)) {
                boolean containsKey = academiDegreeMap.containsKey(academicDegree);
                if (containsKey) {
                    String academicDegreeValue = String.valueOf(academiDegreeMap.get(academicDegree));
                    info.setAcademicDegreeValue(academicDegreeValue);
                }
            }

        }
        return new ResultJson(educationInfos);
    }

    /**
     * 封装公共返回字典map
     *
     * @param dataType
     * @return
     */
    public Map<String, Object> dictionaryUtils(String dataType) {
        Dictionary dictionary = new Dictionary();
        dictionary.setDataType(dataType);
        List<Dictionary> dictionaries = dictionaryMapper.queryByType(dictionary);
        Map<String, Object> dictionaryMap = new HashMap<>();
        if (null != dictionaries && dictionaries.size() > 0) {
            dictionaryMap = ListToMap.listToMap(dictionaries);
        }
        return dictionaryMap;

    }

}
