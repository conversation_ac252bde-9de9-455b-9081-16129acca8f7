import 'package:flutter/material.dart';

class FontsHelper {
  static const _kFontFam = 'MyFlutterApp';
  static const IconData homeIconData = const IconData(0xe81b, fontFamily: _kFontFam);
  static const IconData sucscriptionIconData = const IconData(0xe81a, fontFamily: _kFontFam);
  static const IconData marketIconData = const IconData(0xe81d, fontFamily: _kFontFam);
  static const IconData dataReportIconData = const IconData(0xe81c, fontFamily: _kFontFam);
  static const IconData profileIconData = const IconData(0xe81e, fontFamily: _kFontFam);
  static const IconData newDataLeftIconData = const IconData(0xe80b, fontFamily: _kFontFam);
  static const IconData newDataRightIconData = const IconData(0xe80c, fontFamily: _kFontFam);
  /// * 变更记录
  static const IconData changeLogIconData = const IconData(0xe800, fontFamily: _kFontFam);
  /// * 分支机构
  static const IconData branchIconData = const IconData(0xe803, fontFamily: _kFontFam);
  /// * 提醒
  static const IconData reminderIconData = const IconData(0xe806, fontFamily: _kFontFam);
  /// * 股东信息
  static const IconData shareHolderIconData = const IconData(0xe807, fontFamily: _kFontFam);
  /// * 关于我们
  static const IconData aboutUsIconData = const IconData(0xe808, fontFamily: _kFontFam);
  /// * 基本信息
  static const IconData basicInfoIconData = const IconData(0xe809, fontFamily: _kFontFam);
  /// * 企业年报
  static const IconData annualReportIconData = const IconData(0xe80a, fontFamily: _kFontFam);
  /// * 行政处罚
  static const IconData administrativePenaltyIconData = const IconData(0xe80d, fontFamily: _kFontFam);
  /// * 设置
  static const IconData settingsIconData = const IconData(0xe811, fontFamily: _kFontFam);
  /// * 人员
  static const IconData membersIconData = const IconData(0xe815, fontFamily: _kFontFam);
  /// * 资质
  static const IconData qualificationIconData = const IconData(0xe816, fontFamily: _kFontFam);
  /// * 业绩
  static const IconData achievementIconData = const IconData(0xe817, fontFamily: _kFontFam);
  /// * 时钟
  static const IconData clockIconData = const IconData(0xe818, fontFamily: _kFontFam);
  /// * 五角星 - 边框 //收藏
  static const IconData starBorderIconData = const IconData(0xe819, fontFamily: _kFontFam);
}
