package com.hzw.sunflower.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hzw.sunflower.common.BaseController;
import com.hzw.sunflower.common.Paging;
import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.common.annotation.RepeatSubmit;
import com.hzw.sunflower.constant.MessageConstants;
import com.hzw.sunflower.controller.request.*;
import com.hzw.sunflower.controller.response.SealAppingTaskVO;
import com.hzw.sunflower.controller.response.SealApplicationHandleVO;
import com.hzw.sunflower.controller.response.SealApplicationVO;
import com.hzw.sunflower.controller.response.SealFilesVO;
import com.hzw.sunflower.dto.SealApplicationDTO;
import com.hzw.sunflower.entity.condition.SealApplicationCondition;
import com.hzw.sunflower.service.SealApplicationService;
import com.hzw.sunflower.service.SealManagementService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;


/**
* SealApplicationController
*/
@Api(tags = "印章申请接口")
@RestController
@RequestMapping("/sealApplication")
public class SealApplicationController extends BaseController {
    @Autowired
    private SealApplicationService sealApplicationService;

    @Autowired
    private SealManagementService sealManagementService;

    @ApiOperation(value = "根据条件分页查询用印申请列表")
    @ApiImplicitParam(name = "condition", value = "用户表 查询条件", required = true, dataType = "SealApplicationCondition", paramType = "body")
    @PostMapping("/list")
    public Result<IPage<SealApplicationDTO>> list(@RequestBody SealApplicationCondition condition) {
        IPage<SealApplicationDTO> page = sealApplicationService.findInfoByCondition(condition, getJwtUser());
        return Result.ok(page);
    }

    @ApiOperation(value = "根据条件分页查询用印申请列表-- 处长，分管领导 只可以查看")
    @ApiImplicitParam(name = "condition", value = "用户表 查询条件", required = true, dataType = "SealApplicationCondition", paramType = "body")
    @PostMapping("/listOnly")
    public Result<IPage<SealApplicationDTO>> listOnly(@RequestBody SealApplicationCondition condition) {
        IPage<SealApplicationDTO> page = sealApplicationService.listOnly(condition, getJwtUser());
        return Result.ok(page);
    }


    @ApiOperation(value = "项目检索申请")
    @ApiImplicitParam(name = "condition", value = "用户表 查询条件", required = true, dataType = "SealApplicationCondition", paramType = "body")
    @PostMapping("/listProjectInfo")
    public Result<IPage<SealApplicationDTO>> listProjectInfo(@RequestBody SealApplicationCondition condition) {
         if(null == condition || null == condition.getProjectId()){
             return Result.failed(MessageConstants.PARAMS_NOT_NULL);
         }
        IPage<SealApplicationDTO> page = sealApplicationService.listProjectInfo(condition);
        return Result.ok(page);
    }


    @ApiOperation(value = "根据主键ID查询SealApplication信息")
    @ApiImplicitParam(name = "id", value = "主键ID", required = true)
    @GetMapping(value = "/get/{id}")
    public Result<SealApplicationVO> get(@PathVariable Long id) {
        if (id == null) {
            return Result.failed(MessageConstants.GET_NULL);
        }
        SealApplicationVO sealApplicationVO = sealApplicationService.getInfoById(id);
        return Result.ok(sealApplicationVO);
    }

    @ApiOperation(value = "新增SealApplication信息")
    @ApiImplicitParam(name = "sealApplicationREQ", value = "SealApplication表 ", required = true, dataType = "SealApplicationREQ", paramType = "body")
    @PostMapping("/add")
    @RepeatSubmit
    public Result<Boolean> add(@RequestBody SealApplicationREQ sealApplicationREQ) {
        sealApplicationREQ.setApplyTime(new Date());
        sealApplicationREQ.setApplyUser(getJwtUser().getUserId());
        sealApplicationREQ.setApplyUserName(getJwtUser().getUserName());
        sealApplicationREQ.setDeptId(getJwtUser().getUser().getDepartId());
        sealApplicationREQ.setDeptName(getJwtUser().getUser().getDepartName());
        // 校验选择的印章
        if(StringUtils.isNotBlank(sealApplicationREQ.getChooseSeal())){
            Boolean aBoolean = sealManagementService.checkChooseSeal(sealApplicationREQ);
            if (!aBoolean){
                return Result.failed("该印章类型已变更，请重新选择！");
            }
        }
        return sealApplicationService.addInfo(sealApplicationREQ);
    }

    @ApiOperation(value = "修改SealApplication信息")
    @ApiImplicitParam(name = "req", value = "SealApplication表 ", required = true, dataType = "SealApplicationREQ", paramType = "body")
    @PostMapping(value = "/update")
    @RepeatSubmit
    public Result<Boolean> update(@RequestBody SealApplicationREQ req) {
        Long id = req.getId();
        if (id == null) {
            return Result.failed(MessageConstants.UPDATE_NULL);
        }
        //req.setApplyTime(new Date());
        req.setApplyUser(getJwtUser().getUserId());
        req.setApplyUserName(getJwtUser().getUserName());
        req.setDeptId(getJwtUser().getUser().getDepartId());
        req.setDeptName(getJwtUser().getUser().getDepartName());
        // 校验选择的印章
        if(StringUtils.isNotBlank(req.getChooseSeal())){
            Boolean aBoolean = sealManagementService.checkChooseSeal(req);
            if (!aBoolean){
                return Result.failed("该印章类型已变更，请重新选择！");
            }
        }
        return sealApplicationService.updateInfo(req);
    }

    @ApiOperation(value = "撤回申请")
    @ApiImplicitParam(name = "req", value = "SealApplication表 ", required = true, dataType = "SealApplicationREQ", paramType = "body")
    @GetMapping(value = "/withdrawApplication/{id}")
    @RepeatSubmit
    public Result<Boolean> withdrawApplication(@PathVariable Long id) {
        if (id == null) {
            return Result.failed("请选择需要撤回的数据！");
        }
        return sealApplicationService.withdrawApplication(id);
    }

    @ApiOperation(value = "取消申请 ")
    @ApiImplicitParam(name = "id", value = "主键ID", required = true)
    @GetMapping(value = "/delete/{id}")
    public Result<Boolean> delete(@PathVariable Long id) {
        if (id == null) {
            return Result.failed("请选择需要取消的数据！");
        }
        return sealApplicationService.deleteById(id);
    }

    @ApiOperation(value = "查看处理列表")
    @ApiImplicitParam(name = "sealApplicationREQ", value = "用户表 查询条件", required = true, dataType = "SealApplicationREQ", paramType = "body")
    @PostMapping("/handleList")
    public Result<Paging<SealApplicationHandleVO>> list(@RequestBody SealApplicationHandleREQ req) {
        IPage<SealApplicationHandleVO> page = sealApplicationService.selectSealHandleVO(req);
        return Result.ok(Paging.buildPaging(page));
    }


    @ApiOperation(value = "处理列表导出")
    @PostMapping("/exportSealHandleVO")
    public  void  exportSealHandleVO(HttpServletResponse response, @RequestBody SealApplicationHandleREQ req) {
        sealApplicationService.exportSealHandleVO(response,req);
    }


    @ApiOperation(value = "审批列表")
    @ApiImplicitParam(name = "appingTaskREQ", value = "SealApplication表 ", required = true, dataType = "RequestBody", paramType = "body")
    @PostMapping(value = "/queryApproveList")
    public Result<Paging<SealAppingTaskVO>> queryApproveList(@RequestBody AppingTaskREQ appingTaskREQ) {
        return sealApplicationService.queryApproveList(appingTaskREQ);
    }

    @ApiOperation(value = "审批")
    @ApiImplicitParam(name = "appingTaskREQ", value = "SealApplication表 ", required = true, dataType = "RequestBody", paramType = "body")
    @PostMapping(value = "/reviewSeal")
    public Result<Boolean> reviewSeal(@RequestBody SealApprovalREQ appingTaskREQ) {
        return sealApplicationService.reviewSeal(appingTaskREQ);
    }

    @ApiOperation(value = "判断是否为最后一级审批")
    @ApiImplicitParam(name = "appingTaskREQ", value = "SealApplication表 ", required = true, dataType = "RequestBody", paramType = "body")
    @PostMapping(value = "/isEndReview")
    public Result<Boolean> isEndReview(@RequestBody SealApprovalREQ appingTaskREQ) {
        return sealApplicationService.isEndReview(appingTaskREQ);
    }


    @ApiOperation(value = "获取文件")
    @ApiImplicitParam(name = "SealFileSectionREQ", value = "SealApplication表 ", required = true, dataType = "RequestBody", paramType = "body")
    @PostMapping(value = "/getSealFileBySection")
    public Result<List<SealFilesVO>> getSealFileBySection(@RequestBody SealFileSectionREQ req) {
        if(null == req || null == req.getSectionId() || null ==  req.getApplyItem()){
            return Result.failed(MessageConstants.PARAMS_NOT_NULL);
        }
        return Result.ok(sealApplicationService.getSealFileBySection(req));
    }

    @ApiOperation(value = "判断文号是否重复")
    @ApiImplicitParam(name = "SealFileSectionREQ", value = "SealApplication表 ", required = true, dataType = "RequestBody", paramType = "body")
    @PostMapping(value = "/checkSymbolOnly")
    public Result<Boolean> checkSymbolOnly(@RequestBody SealApplicationREQ req) {
        if(StringUtils.isBlank(req.getSymbol())){
            return Result.ok();
        }
        Boolean check = sealApplicationService.checkSymbolOnly(req.getSymbol(),req.getId());
        return Result.ok(check);
    }

}