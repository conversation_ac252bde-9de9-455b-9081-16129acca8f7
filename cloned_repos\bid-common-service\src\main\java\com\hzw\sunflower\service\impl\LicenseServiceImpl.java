package com.hzw.sunflower.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.alicp.jetcache.anno.CachePenetrationProtect;
import com.alicp.jetcache.anno.CacheRefresh;
import com.alicp.jetcache.anno.Cached;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzw.sunflower.common.redis.RedisCache;
import com.hzw.sunflower.common.redis.ServerIdService;
import com.hzw.sunflower.constant.GeneralConstants;
import com.hzw.sunflower.constant.LicenseConstants;
import com.hzw.sunflower.controller.request.LicenseReq;
import com.hzw.sunflower.dao.LicenseMapper;
import com.hzw.sunflower.dto.LicenseDto;
import com.hzw.sunflower.entity.License;
import com.hzw.sunflower.exception.ExceptionEnum;
import com.hzw.sunflower.exception.ParamsNotNullException;
import com.hzw.sunflower.exception.SunFlowerException;
import com.hzw.sunflower.service.LicenseService;
import com.hzw.sunflower.util.lic.LicUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;


/**
 * <AUTHOR>
 * @datetime 2023/02/20 16:09
 * @description: license接口实现类
 * @version: 1.0
 */
@Slf4j
@Service
public class LicenseServiceImpl extends ServiceImpl<LicenseMapper, License> implements LicenseService {


    @Value("${license.private-key:}")
    private String privateKey;

    @Value("${organ.organization_num:}")
    private String organizationNum;

    @Value("${organ.organization_type:}")
    private Integer organizationType;

    @Autowired
    private RedisCache redisCache;

    @Resource
    private ServerIdService serverIdService;


    /**
     * 获取系统license
     * @return
     */
    @Cached(name = "getLicense", key = "'_system_license'", expire = 1,timeUnit = TimeUnit.DAYS)
    @CacheRefresh(refresh = 1, stopRefreshAfterLastAccess = 2,timeUnit = TimeUnit.DAYS)
    @CachePenetrationProtect
    @Override
    public LicenseDto getLicense() {
        String licenseCacheKey=GeneralConstants.SYS_LICENSE_KEY+":"+serverIdService.getMacAddrCache();

        LicenseDto dto = null;
        // 读取缓存
        String strLicense= redisCache.getCacheObject(licenseCacheKey);
        if (ObjectUtils.isEmpty(strLicense)) {
            log.debug("license缓存未命中");
            License license = this.getOne(new LambdaQueryWrapper<License>().eq(License::getMacAddr, serverIdService.getMacAddrCache())
                    .orderByDesc(License::getCreatedTime).last("limit 1"));
            if (license != null) {
                strLicense=license.getLicense();
                redisCache.setCacheObject(licenseCacheKey, strLicense);
            }else{
                throw new SunFlowerException(ExceptionEnum.DEVICE_NOT_ACTIVATED, ExceptionEnum.DEVICE_NOT_ACTIVATED.getMessage());
            }
        }
        String decLicense = LicUtil.decLicense(strLicense);
        if (!ObjectUtils.isEmpty(decLicense)){
            log.debug("license校验时间");
            dto = JSONObject.parseObject(decLicense, LicenseDto.class);
            // 校验时间 返回提示信息
            if (dto != null && dto.getExpireDate()!=null && dto.getIssuedDate()!=null) {
                LocalDate expireDate = LocalDate.parse(dto.getExpireDate(), DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                LocalDate issuedDate = LocalDate.parse(dto.getIssuedDate(), DateTimeFormatter.ofPattern("yyyy-MM-dd"));

                //算出剩余天数
                Long dayLimit = expireDate.toEpochDay() - LocalDate.now().toEpochDay();
                //存储剩余天数
                dto.setLeftTime(Long.toString(dayLimit));

                //存储开始时间-截至时间
                dto.setExpireDate(issuedDate.toString()+'-'+expireDate.toString());

                if (dayLimit < 0) {
                    dto.setToast("已过期");
                } else if (LicenseConstants.VERSION_TRIAL.equals(dto.getVersion())) {
                    //当前版本为体验版时
                    dto.setToast("License有效期" + dayLimit + "天");
                } else if(LicenseConstants.VERSION_OFFICIAL.equals(dto.getVersion()) && dayLimit <= LicenseConstants.TOAST_DAY_SHOW){
                    //在系统版本为正式版时，license剩余时间少于30天时，系统中所有页面右下角展示license有效期倒计时（按天）
                    dto.setToast("License有效期" + dayLimit + "天");
                }
            }
        }

        return dto;
    }

    /**
     * 判断license激活条件是否一致
     * @param req
     * @return
     */
    @Override
    public Map<String, Object> beforeUpdateLicense(LicenseReq req) {
        Map<String, Object> data = new HashMap<>(16);
        String macAddr = serverIdService.getMacAddrCache();
        License licenseData = this.getOne(new LambdaQueryWrapper<License>().eq(License::getMacAddr, macAddr)
                .orderByDesc(License::getCreatedTime).last("limit 1"));

        //激活码License
        String decLicense = LicUtil.decLicense(req.getLicense());
        if (ObjectUtils.isEmpty(decLicense)) {
            throw new SunFlowerException(ExceptionEnum.LICENSE_DECRYPT_EXCEPTION, ExceptionEnum.LICENSE_DECRYPT_EXCEPTION.getMessage());
        }

        LicenseDto  dto = JSONObject.parseObject(decLicense, LicenseDto.class);

        //1.判断当前激活码中的机器指纹与服务器的机器指纹是否一致，若不一致则提示：“激活码信息不匹配，请核实”，不允许激活
        if (!dto.getMacAddr().equals(macAddr)){
            data.put("msg",LicenseConstants.CONFIRM_INFO_1);
            data.put("code",LicenseConstants.CONFIRM_CODE_1);
            return data;
        }

        //2.验证License信息中有效期是否有效，即有效期结束时间应当大于当前时间，不允许激活
        LocalDate expireDate = LocalDate.parse(dto.getExpireDate(), DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        //算出剩余天数
        Long dayLimit = expireDate.toEpochDay() - LocalDate.now().toEpochDay();
        if(dayLimit<0){
            data.put("msg",LicenseConstants.CONFIRM_INFO_2);
            data.put("code",LicenseConstants.CONFIRM_CODE_2);
            return data;
        }

        //下面的三个都需要在数据库中进行校验
        if (licenseData == null) {
            data.put("code",6);
            return data;
        }else{
            //取出数据库中License
            String dataLicense = LicUtil.decLicense(licenseData.getLicense());
            if (ObjectUtils.isEmpty(dataLicense)) {
                throw new SunFlowerException(ExceptionEnum.LICENSE_DECRYPT_EXCEPTION, ExceptionEnum.LICENSE_DECRYPT_EXCEPTION.getMessage());
            }

            LicenseDto  dataDto = JSONObject.parseObject(dataLicense, LicenseDto.class);

            //3.如果当前输入的License信息与已激活的License信息一致，则提示：“您已激活，无需重复激活”
            if (dataLicense.equals(decLicense)) {
                data.put("msg", LicenseConstants.CONFIRM_INFO_3);
                data.put("code", LicenseConstants.CONFIRM_CODE_3);
                return data;
            }

            //4.如果当前输入的License信息系统版本低于已激活License信息中的系统版本，即已激活正式版，再次激活体验版的情况，进行系统提示，点击确认则进行激活，点击取消则不进行激活
            if (LicenseConstants.VERSION_TRIAL.equals(dto.getVersion()) && LicenseConstants.VERSION_OFFICIAL.equals(dataDto.getVersion())){
                data.put("msg",LicenseConstants.CONFIRM_INFO_4);
                data.put("code",LicenseConstants.CONFIRM_CODE_4);
                return data;
            }

            //5.如果当前输入的License信息中功能版本低于已激活License信息中的功能版本，即已激活旗舰版再次激活专业版，或已激活旗舰版再次激活基础版等情况，进行系统提示,点击确认则进行激活，点击取消则不进行激活
            if ( (LicenseConstants.FUNCTION_Edition.equals(dataDto.getFunction()) && (LicenseConstants.FUNCTION_PROFESSIONAL.equals(dto.getFunction()) || LicenseConstants.FUNCTION_BASE.equals(dto.getFunction())))
                    || ( LicenseConstants.FUNCTION_PROFESSIONAL.equals(dataDto.getFunction()) && LicenseConstants.FUNCTION_BASE.equals(dto.getFunction()))  ){
                data.put("msg",LicenseConstants.CONFIRM_INFO_5);
                data.put("code",LicenseConstants.CONFIRM_CODE_5);
                return data;
            }
        }


        //6.校验通过可以正常
        data.put("code",LicenseConstants.CONFIRM_CODE_6);
        return data;
    }

    /**
     * 修改license信息
     * @param req
     * @return
     */
    @Override
    public Boolean updateLicense(LicenseReq req) {
        String macAddr = serverIdService.getMacAddrCache();
        List<License> licenseDataList = this.list(new LambdaQueryWrapper<License>().eq(License::getMacAddr, macAddr)
                .orderByDesc(License::getCreatedTime));
        License license = new License();
        license.setLicense(req.getLicense());
        //激活码License
        String decLicense = LicUtil.decLicense(req.getLicense());
        if (ObjectUtils.isEmpty(decLicense)) {
            throw new SunFlowerException(ExceptionEnum.LICENSE_DECRYPT_EXCEPTION, ExceptionEnum.LICENSE_DECRYPT_EXCEPTION.getMessage());
        }

        LicenseDto  dto = JSONObject.parseObject(decLicense, LicenseDto.class);

        //数据库里有就判断，没有代表第一次激活
        if (licenseDataList!=null){
            //数据库校验
            for(License licenseData :licenseDataList){
                //取出数据库中License
                String dataLicense = LicUtil.decLicense(licenseData.getLicense());
                LicenseDto  dataDto = JSONObject.parseObject(dataLicense, LicenseDto.class);
                if (dataDto!=null){
                    //当前License和数据库中License是否都是试用期，如果是则不可以继续激活
                    if(LicenseConstants.VERSION_TRIAL.equals(dto.getVersion()) && LicenseConstants.VERSION_TRIAL.equals(dataDto.getVersion())){
                        throw new ParamsNotNullException("您已经激活过一次试用版，不可以再次激活试用版");
                    }
                }
            }
        }

        // 校验组织机构代码
        if (!dto.getOrganizationNum().equals(organizationNum)) {
            throw new SunFlowerException(ExceptionEnum.LICENSE_ACTIVE_FAIL, ExceptionEnum.LICENSE_ACTIVE_FAIL.getMessage());
        }
        // 校验租户
        if (!dto.getIdentity().equals(organizationType)) {
            throw new SunFlowerException(ExceptionEnum.LICENSE_ACTIVE_FAIL, ExceptionEnum.LICENSE_ACTIVE_FAIL.getMessage());
        }
        license.setMacAddr(macAddr);

        // 更新数据库
        license.setId(null);
        boolean flag = this.save(license);

        String licenseCacheKey=GeneralConstants.SYS_LICENSE_KEY+":"+serverIdService.getMacAddrCache();
        // 读取缓存
        String strLicense= redisCache.getCacheObject(licenseCacheKey);
        if (!ObjectUtils.isEmpty(strLicense)) {
            //删除所属的旧缓存
            redisCache.deleteObject(GeneralConstants.SYS_LICENSE_KEY + ":" + macAddr);
        }
        // 更新缓存
        redisCache.setCacheObject(GeneralConstants.SYS_LICENSE_KEY + ":" + macAddr, req.getLicense());

        return flag;
    }

}
