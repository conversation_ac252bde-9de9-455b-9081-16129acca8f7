package com.hzw.sunflower.controller.response;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 单位公司 返回实体
 *
 * <AUTHOR>
 * @version 1.0.0 2021-04-12
 */
@ApiModel(description = "单位公司 ")
@Data
public class OpenCompanyVo {

    @ApiModelProperty(value = "主键", position = 1)

    private Long id;

    @ApiModelProperty(value = "公司名称", position = 2)
    private String companyName;

    @ApiModelProperty(value = "注册资金", position = 3)
    private BigDecimal registerMoney;

    @ApiModelProperty(value = "单位类型 1招标人2代理机构3供应商", position = 4)
    private Integer companyType;

    /**
     * 单位状态  0 正常 1 注销
     */
    @ApiModelProperty(value = "单位状态 ", position = 5)
    private Integer companyStatus;

    @ApiModelProperty(value = "三证合一 是否三证合一（0：否  1：是 ）", position = 6)
    private Integer threeInOne;

    @ApiModelProperty(value = "营业执照", position = 7)
    private String businessLicense;

    @ApiModelProperty(value = "组织机构代码", position = 8)
    private String organizationNum;

    @ApiModelProperty(value = "法定代表人", position = 9)
    private String legalRepresentative;

    @ApiModelProperty(value = "法定代表人身份证", position = 10)
    private String legalRepresentativeIdentity;

    @ApiModelProperty(value = "企业简称", position = 11)
    private String abbreviation;

    @ApiModelProperty(value = "是否被验证过 0:否  1:是", position = 12)
    private Integer isValidate;
}