package com.hzw.sunflower.controller.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@ApiModel(description = "采购文件发售截至时间和响应文件递交截至时间校验参数")
@Data
public class SaleAndSubmitCheckReq {

    @ApiModelProperty(value = "一级采购方式 依法必招，非依法必招，政府采购，国际招标")
    private String purchaseType;

    @ApiModelProperty(value = "二级采购方式 招标，竞争性磋商，竞争性谈判，询价，征询，单一来源，其他")
    private String purchaseMode;

    @ApiModelProperty(value = "三级采购方式 1.预审公开 2.后审公开 3.邀请 4.二阶段 5.政府公开 4.政府邀请")
    private Integer purchaseStatus;

    @ApiModelProperty(value = "招标文件发售结束时间类型（1：确定时间; 2：另行通知;3：投标文件递交截止时间前一天）")
    private Integer saleEndTimeType;

    @ApiModelProperty(value = "招标文件发售结束时间")
    private Date saleEndTime;

    @ApiModelProperty(value = "投标文件递交截止时间类型（1：确定时间; 2：另行通知）")
    private Integer submitEndTimeType;

    @ApiModelProperty(value = "投标文件递交截止时间")
    private Date submitEndTime;

    @ApiModelProperty(value = "开始时间（补充公告校验开始时间为招标文件挂网时间，其他校验为当前时间）")
    private Date startTime;

}
