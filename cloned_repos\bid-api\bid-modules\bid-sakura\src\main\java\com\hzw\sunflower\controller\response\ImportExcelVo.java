package com.hzw.sunflower.controller.response;

import com.hzw.sunflower.entity.BondWater;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2022/8/1 15:21
 * @description：自定义导出返回
 * @modified By：`
 * @version: 1.0
 */
@ApiModel(description = "标包关联情况返回实体类")
@Data
public class ImportExcelVo implements Serializable {

    @ApiModelProperty("导入失败收款流水列表")
    List<BondWater> failRows;

    @ApiModelProperty("导入失败数量")
    Integer failCount;

    @ApiModelProperty("导入失败类型 1.格式错误 2.数据重复")
    Integer failType;

    @ApiModelProperty("导入总条数")
    Integer count;

    @ApiModelProperty("导入金额总计")
    BigDecimal amount;

}
