package com.hzw.expertfee.api.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 页面类型
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum PageType {
    /**
     * 页面类型
     */


    REVENUE_COST_BREAKDOWN(30,"收入成本明细表（用友数据源）"),
    COST_BREAKDOWN(31,"成本明细表（用友数据源）"),
    EXPENSES_BREAKDOWN(32,"费用明细表（用友数据源）")
    ;

    private final Integer type;
    private final String desc;

}
