package com.hzw.sunflower.dao;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hzw.sunflower.entity.OperationLog;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 操作日志 数据层
 *
 * <AUTHOR>
 */
public interface OperationLogMapper extends BaseMapper<OperationLog> {

    /**
     * 新增操作日志
     *
     * @param operLog 操作日志对象
     */
    void insertOperlog(@Param("operlog") OperationLog operLog);

    /**
     * 查询系统操作日志集合
     *
     * @param operLog 操作日志对象
     * @return 操作日志集合
     */
    List<OperationLog> selectOperLogList(@Param("operlog") OperationLog operLog);

    /**
     * 批量删除系统操作日志
     *
     * @param operIds 需要删除的操作日志ID
     * @return 结果
     */
    int deleteOperLogByIds(@Param("operIds") Long[] operIds);

    /**
     * 查询操作日志详细
     *
     * @param operId 操作ID
     * @return 操作日志对象
     */
    OperationLog selectOperLogById(@Param("operId") Long operId);

    /**
     * 清空操作日志
     */
    void cleanOperLog();
}
