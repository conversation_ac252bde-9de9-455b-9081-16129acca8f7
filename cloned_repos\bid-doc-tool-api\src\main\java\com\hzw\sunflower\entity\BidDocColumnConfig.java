package com.hzw.sunflower.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;

import com.hzw.sunflower.common.BaseBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 招标文件编制字段配置展示表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-26
 */
@Getter
@Setter
@TableName("t_bid_doc_column_config")
@ApiModel(value = "BidDocColumnConfig对象", description = "招标文件编制字段配置展示表")
public class BidDocColumnConfig extends BaseBean implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("模板id")
    @TableField("template_id")
    private Long templateId;

    @ApiModelProperty("目录id")
    @TableField("catalog_id")
    private Long catalogId;

    @ApiModelProperty("字段名称")
    @TableField("column_name")
    private String columnName;

    @ApiModelProperty("字段")
    @TableField("column_code")
    private String columnCode;

    @ApiModelProperty("字段类型")
    @TableField("column_type")
    private String columnType;

    @ApiModelProperty("字段长度")
    @TableField("column_length")
    private Integer columnLength;

    @ApiModelProperty("字段默认值")
    @TableField("column_default_value")
    private String columnDefaultValue;

    @ApiModelProperty("是否必填 1.是 2.否")
    @TableField("is_required")
    private Integer isRequired;

    @ApiModelProperty("校验规则")
    @TableField("valid_rule")
    private String validRule;

    @ApiModelProperty("招标文件是否回显 1 是 2 否")
    @TableField("is_show")
    private Integer isShow;

}
