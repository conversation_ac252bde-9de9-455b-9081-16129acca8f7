package com.hzw.sunflower.controller.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 保证金退还表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-27
 */
@Data
public class AgencyInfo2NccVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("户名")
    private String accountName;

    @ApiModelProperty("账户")
    private String account;

    @ApiModelProperty("开户行")
    private String bankDeposit;

    @ApiModelProperty("银行类型")
    private Integer bankType;

}
