package com.hzw.sunflower.service;

import com.hzw.sunflower.controller.request.BidOpenDetailReq;
import com.hzw.sunflower.controller.request.ScheduleAmountREQ;
import com.hzw.sunflower.controller.response.ScheduleAmountVO;
import com.hzw.sunflower.entity.BidOpenDetail;

import java.util.List;
import java.util.Map;

public interface BidOpenDetailService {
    /**
     * 查询开标一览表
     * @param req
     * @param keyWords
     * @param startDate
     * @param endDate
     * @param pageIndex
     * @param pageSize
     * @return
     */
    Map<String,Object> selectBidOpenDetailList(BidOpenDetailReq req, String keyWords, String startDate, String endDate, int pageIndex, int pageSize);

    /**
     * 导出excel查询
     * @param req
     * @param keyWords
     * @param startDate
     * @param endDate
     * @return
     */
    List<BidOpenDetail> queryBidOpenDetailList(BidOpenDetailReq req,String keyWords, String startDate, String endDate);

    List<BidOpenDetail> queryBidOpenDetailList(BidOpenDetailReq req,String keyWords, String startDate, String endDate,Integer pageIndex, Integer pageSize );
    List<BidOpenDetail> queryBidOpenDetailList(BidOpenDetailReq req,String keyWords, String startDate, String endDate,Integer pageIndex, Integer pageSize ,Boolean hasConferenceAddress);
    /**
     * 根据筛选条件查询日程金额统计（年月）
     * @param req
     * @return
     */
    ScheduleAmountVO getScheduleAmount(ScheduleAmountREQ req);
}
