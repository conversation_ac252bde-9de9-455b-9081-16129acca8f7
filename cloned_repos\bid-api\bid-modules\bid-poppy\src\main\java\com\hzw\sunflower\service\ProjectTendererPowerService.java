package com.hzw.sunflower.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.controller.project.request.AgentUserProPermREQ;
import com.hzw.sunflower.entity.ProjectTendererPower;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * 招标人查看项目权限表
 *
 * <AUTHOR>
 * @version 1.0.0 2021-04-14
 */
public interface ProjectTendererPowerService extends IService<ProjectTendererPower> {

    /**
     * 项目授权信息
     * @param req
     * @return
     */
    List<ProjectTendererPower> listProjectPermissions(ProjectTendererPower req);

    /**
     * 新增/修改项目权限
     * @param req
     * @return
     */
    Boolean addOrUpdateProjectPermissions(ProjectTendererPower req);

    /**
     * 根据主键ID删除
     * @param id
     * @return
     */
    Result<Boolean> deleteById(Long id);

    /**
     * 根据主键ID删除
     * @param projectId
     * @return
     */
    List<ProjectTendererPower> selectByProjectId(Long projectId);

    /**
     * 删除招标人权限
     * @param projectId
     * @return
     */
    Integer updateTetetendereeByAgency(Long projectId);
}