package com.hzw.sunflower.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hzw.sunflower.common.BaseController;
import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.common.annotation.RepeatSubmit;
import com.hzw.sunflower.controller.request.SysAppReq;
import com.hzw.sunflower.entity.SysApp;
import com.hzw.sunflower.exception.ParamsNotNullException;
import com.hzw.sunflower.service.SysAppService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "外部系统连接管理")
@RestController
@RequestMapping("/sysapp")
public class SysAppController extends BaseController {

    @Resource
    private SysAppService sysAppService;


    @ApiOperation(value = "列表")
    @ApiImplicitParam(name = "req", value = "参数", required = true, dataType = "SysAppReq", paramType = "body")
    @PostMapping("/list")
    public Result<List<SysApp>> list(@RequestBody SysAppReq req) {
        LambdaQueryWrapper<SysApp> queryWrapper = new LambdaQueryWrapper<>();
        if(StringUtils.isNotBlank(req.getKeywords())){
            queryWrapper.and(
                    wrapper->wrapper.like(SysApp::getName,req.getKeywords())
                            .or(p->p.like(SysApp::getSn,req.getKeywords()))
                            .or(p->p.like(SysApp::getUrl,req.getKeywords()))
                            .or(p->p.like(SysApp::getIndexUrl,req.getKeywords()))
            );
        }
        if(req.getStatus() != null && !req.getStatus().equals(-1)){
            queryWrapper.eq(SysApp::getStatus,req.getStatus());
        }
        List<SysApp> list = sysAppService.list(queryWrapper);
        return Result.ok(list);
    }

    @ApiOperation(value = "启用禁用")
    @ApiImplicitParam(name = "req", value = "参数", required = true, dataType = "SysAppReq", paramType = "body")
    @PostMapping("/updateStatus")
    @RepeatSubmit
    public Object updateStatus(@RequestBody SysAppReq req) {
        Boolean bool  = sysAppService.updateStatus(req);
        return Result.okOrFailed(bool);
    }


    @ApiOperation(value = "查询")
    @GetMapping("/get/{id}")
    public Result<SysApp> get(@PathVariable Long id) {
        SysApp sysApp = sysAppService.getById(id);
        return Result.ok(sysApp);
    }

    @ApiOperation(value = "更新")
    @PostMapping("/update")
    @RepeatSubmit
    public Result<Object> update(@RequestBody SysApp sysApp) {
        LambdaQueryWrapper<SysApp> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysApp::getSn,sysApp.getSn());
        queryWrapper.ne(SysApp::getId,sysApp.getId());
        long count = sysAppService.count(queryWrapper);
        if(count>0){
            throw new ParamsNotNullException("系统标识重复！");
        }
        sysAppService.updateById(sysApp);
        return Result.ok();
    }

    @ApiOperation(value = "添加")
    @PostMapping("/add")
    @RepeatSubmit
    public Result<Object> add(@RequestBody SysApp sysApp) {
        LambdaQueryWrapper<SysApp> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysApp::getSn,sysApp.getSn());
        long count = sysAppService.count(queryWrapper);
        if(count>0){
            throw new ParamsNotNullException("系统标识重复！");
        }
        sysAppService.save(sysApp);
        return Result.ok();
    }

    @ApiOperation(value = "删除")
    @DeleteMapping("/del/{id}")
    @RepeatSubmit
    public Result<SysApp> del(@PathVariable Long id) {
        sysAppService.removeById(id);
        return Result.ok();
    }

}
