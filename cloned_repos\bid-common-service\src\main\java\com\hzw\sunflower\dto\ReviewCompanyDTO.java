package com.hzw.sunflower.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 单位公司 (t_company)
 *
 * <AUTHOR>
 * @version 1.0.0 2021-04-12
 */
@ApiModel(description = "单位公司 ")
@Data
public class ReviewCompanyDTO {

    @ApiModelProperty(value = "企业id")
    private Long id;

    @ApiModelProperty(value = "用户id")
    private Long userId;

    @ApiModelProperty(value = "标段id")
    private Long subId;

    @ApiModelProperty(value = "轮次")
    private Integer bidRound;

    @ApiModelProperty(value = "项目id")
    private Long projectId;

    @ApiModelProperty(value = "企业名称")
    private String companyName;

    @ApiModelProperty(value = "下载次数")
    private Integer downLoadNum;

}