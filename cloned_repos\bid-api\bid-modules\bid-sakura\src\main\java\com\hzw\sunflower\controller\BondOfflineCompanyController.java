package com.hzw.sunflower.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hzw.sunflower.common.BaseController;
import com.hzw.sunflower.common.Paging;
import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.common.annotation.RepeatSubmit;
import com.hzw.sunflower.controller.request.BondOfflineCompanyReq;
import com.hzw.sunflower.controller.request.OfflineCompanyReq;
import com.hzw.sunflower.controller.response.OfflineCompanyVo;
import com.hzw.sunflower.service.BondOfflineCompanyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;



@Api(tags = "全流程线下项目保证金处理")
@RestController
@RequestMapping("/bondOfflineCompany")
public class BondOfflineCompanyController  extends BaseController {

    @Autowired
    BondOfflineCompanyService bondOfflineCompanyService;

    @ApiOperation(value = "模糊搜索供应商信息")
    @ApiImplicitParam(name = "req", value = "查询供应商信息参数", required = true, dataType = "OfflineCompanyReq", paramType = "body")
    @PostMapping(value = "/queryCompany")
    public Result<Paging<OfflineCompanyVo>> queryCompany(@RequestBody OfflineCompanyReq req) {
        IPage<OfflineCompanyVo> page = bondOfflineCompanyService.queryCompany(req);
        return Result.ok(Paging.buildPaging(page));
    }

    @RepeatSubmit
    @ApiOperation(value = "新增供应商信息")
    @ApiImplicitParam(name = "req", value = "新增供应商信息参数", required = true, dataType = "BondOfflineCompanyReq", paramType = "body")
    @PostMapping(value = "/addCompany")
    public Result<Boolean> addCompany(@RequestBody BondOfflineCompanyReq req) {
        Boolean bool = bondOfflineCompanyService.addCompany(req);
        return Result.okOrFailed(bool);
    }

    @RepeatSubmit
    @ApiOperation(value = "编辑供应商信息")
    @ApiImplicitParam(name = "req", value = "编辑供应商信息参数", required = true, dataType = "BondOfflineCompanyReq", paramType = "body")
    @PostMapping(value = "/updateCompany")
    public Result<Boolean> updateCompany(@RequestBody BondOfflineCompanyReq req) {
        Boolean bool = bondOfflineCompanyService.updateCompany(req);
        return Result.okOrFailed(bool);
    }

}
