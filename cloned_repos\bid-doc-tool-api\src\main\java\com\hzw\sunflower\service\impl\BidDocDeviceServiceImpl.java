package com.hzw.sunflower.service.impl;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.data.Includes;
import com.deepoove.poi.data.Rows;
import com.deepoove.poi.data.Tables;
import com.deepoove.poi.data.style.BorderStyle;
import com.deepoove.poi.xwpf.NiceXWPFDocument;
import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.controller.request.BidDocDeviceReq;
import com.hzw.sunflower.controller.responese.BidDocDeviceVo;
import com.hzw.sunflower.dao.BidDocDeviceMapper;
import com.hzw.sunflower.dto.BidDocDeviceExcelDto;
import com.hzw.sunflower.entity.BidDocDevice;
import com.hzw.sunflower.entity.OssFile;
import com.hzw.sunflower.service.BidDocDeviceService;
import com.hzw.sunflower.service.OssFileService;
import com.hzw.sunflower.util.*;
import com.hzw.sunflower.util.oss.OssFileStorage;
import com.hzw.sunflower.util.oss.OssUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.xwpf.usermodel.XWPFTable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.HashMap;
import java.util.List;


/**
 * <p>
 * 设备需求 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-15
 */
@Service
@Slf4j
public class BidDocDeviceServiceImpl extends ServiceImpl<BidDocDeviceMapper, BidDocDevice> implements BidDocDeviceService {

    @Value("${files.template.path:}")
    private String templatePath;

    @Value("${files.temporary.path:}")
    private String tempFilePath;

    @Autowired
    private OssFileService ossFileService;

    @Value("${oss.active}")
    private String ossType;

    @Override
    public Result<Boolean> submitDevice(List<BidDocDeviceReq> reqList) {
        List<BidDocDevice> list = BeanListUtil.convertList(reqList, BidDocDevice.class);
        boolean flag = this.saveOrUpdateBatch(list);
        if(flag){
            return Result.ok();
        }
        return Result.error("设备需求保存失败");
    }

    @Override
    public Result<List<BidDocDeviceVo>> queryDevice(BidDocDeviceReq req) {
        LambdaQueryWrapper<BidDocDevice> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BidDocDevice::getDocId,req.getDocId());
        List<BidDocDevice> list = this.list(queryWrapper);
        List<BidDocDeviceVo> resultList = BeanListUtil.convertList(list, BidDocDeviceVo.class);
        return Result.ok(resultList);
    }

    @Override
    public void exportDeviceTemplate(HttpServletResponse response, HttpServletRequest request) throws IOException {
        String fileName = "设备需求导入模板.xlsx";
        String sourcePath = templatePath + "deviceTemplate.xlsx";
        String destPath = tempFilePath + System.currentTimeMillis() + ".xlsx";
        //复制文件
        FileUtils.copyFileUsingStream(new File(sourcePath),new File(destPath));
        try {
            FileUtils.outFile(destPath,"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",fileName,response);
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            // 删除临时文件
            File file = new File(destPath);
            if (file.exists()) {
                file.delete();
            }
        }
    }

    @Override
    public Result<List<BidDocDeviceVo>> importDevice(Long ossId) {
        //下载文件
        OssFile ossFile = ossFileService.getOssFileById(ossId);
        OssFileStorage storage = OssUtil.getOssFileStorage(ossType);
        InputStream inputStream = null;
        if(null != ossFile && null != ossFile.getOssFileKey() ){
            byte[] bytes = storage.downloadFile(ossFile.getOssFileKey());
            inputStream = FileUtils.byte2InputStream(bytes);
        }
        if(null == inputStream){
            return Result.error("文件不存在");
        }
        BidDocDeviceExcelListener listener = new BidDocDeviceExcelListener();
        // 这里需要指定读用哪个class去读，然后读取第一个sheet 文件流会自动关闭
        EasyExcel.read(inputStream, BidDocDeviceExcelDto.class, listener).sheet().headRowNumber(1).doRead();
        return listener.getResultRow();
    }

    @Override
    public void insertDeviceForWord(Long docId, String path) {
        //创建表格
        Tables.TableBuilder tabData = Tables.ofPercentWidth("80%").center().border(BorderStyle.builder().withType(XWPFTable.XWPFBorderType.SINGLE).build());
        //生成表头
        tabData.addRow(Rows.of("设备名称", "规格", "数量及单位", "交货期", "交货地点").center().create());
        //生成表格数据
        LambdaQueryWrapper<BidDocDevice> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BidDocDevice::getDocId,docId);
        List<BidDocDevice> list = this.list(queryWrapper);
        for(BidDocDevice docDevice:list){
            String[] rowData = new String[]{docDevice.getDeviceName(),docDevice.getSpecifications(),docDevice.getQuantityUnit(),docDevice.getDeliveryDate(),docDevice.getDeliveryAddress()};
            tabData.addRow(Rows.of(rowData).create());
        }
        XWPFTemplate tmp = XWPFTemplate.compile(path);
        tmp.render(new HashMap<String, Object>() {{put("docDevice", tabData.create());}});
    }
}
