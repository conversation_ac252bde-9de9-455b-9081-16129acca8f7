package com.hzw.ssm.sys.login.service;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.hzw.ssm.expert.entity.ExpertInfoEntity;
import com.hzw.ssm.fw.base.BaseService;
import com.hzw.ssm.fw.util.CommUtil;
import com.hzw.ssm.fw.util.HZWException;
import com.hzw.ssm.fw.util.MD5Util;
import com.hzw.ssm.fw.util.MessageConstants;
import com.hzw.ssm.fw.util.SysConstants;
import com.hzw.ssm.sys.login.dao.LoginMapper;
import com.hzw.ssm.sys.project.entity.ProjectEntity;
import com.hzw.ssm.sys.sms.mwutil.MWSmsUtil;
import com.hzw.ssm.sys.system.entity.MenuEntity;
import com.hzw.ssm.sys.system.entity.SmsRecordEntity;
import com.hzw.ssm.sys.user.dao.UserMapper;
import com.hzw.ssm.sys.user.entity.UserEntity;
import com.hzw.ssm.util.empty.EmptyUtils;

@Service
public class LoginService extends BaseService{
	
	private static Log log = LogFactory.getLog(LoginService.class);
	
	@Autowired
	private LoginMapper loginMapper;
	
		//梦网短信账号
		@Value("${sms_userId}")
		private String userId;
		//梦网短信密码
		@Value("${sms_userPwd}")
		private String userPwd;
		//梦网主IP
		@Value("${sms_masterIpAddress}")
		private String masterIpAddress;
		
		//梦网备用IP1
		@Value("${sms_ipAddress1}")
		private String ipAddress1;
		//梦网备用IP2
		@Value("${sms_ipAddress2}")
		private String ipAddress2;
	
	/**
	 * 函数功能描述：用户登陆验证
	 * @param login_code 登陆名
	 * @param password 密码
	 * @param state 密码是否已经加密处理：1.已加密（默认）、2.未加密 
	 * @return
	 */
	public UserEntity login(String login_code, String password,int state) {
		Map<String, String> map = new HashMap<String, String>();
		map.put("login_code", login_code);
		
		UserEntity userBean = loginMapper.login(map);
		
		return userBean;
	}
	
	/**
	 * 登录错误限制
	 */
	public void loginSet(UserEntity user){
		loginMapper.loginSet(user);
	}
	
	/**
	 * 登录成功设置
	 */
	public void loginPass(String code){
		loginMapper.loginPass(code);
	}

	/**
	 * 获得授权的菜单
	 * @param roleId
	 * @return
	 */
	public List<MenuEntity> getMenuMapOfAuth(String roleId) {
		List<MenuEntity> menus=loginMapper.getMenuListOfAuth(roleId);
		if(menus==null||menus.size()<=0){
			throw new HZWException(MessageConstants.NO_AUTH);
		}
		Collections.sort(menus,new Comparator<MenuEntity>() {//对菜单进行排序,排序字段为升序
			@Override
			public int compare(MenuEntity o1, MenuEntity o2) {
				if(o2.getSortNo()==null||o1.getSortNo()==null){//如果存在sortno为空，则默认排在前面
					return 1;
				}
				return o1.getSortNo().intValue()-o2.getSortNo().intValue();
			}
		});
		for(MenuEntity m:menus){
			if(SysConstants.ROOT_MENU.equals(m.getParent_menu_id())){//顶级菜单
				m.setSubMenuList(this.getSubMenuList(m.getMenu_id(),menus));//获得下级菜单
			}
		}
		return menus;
	}
	/**
	 * 获得下级子菜单
	 * @param menuId
	 * @param menus
	 * @return
	 */
	private List<MenuEntity> getSubMenuList(String menuId,List<MenuEntity> menus) {
		List<MenuEntity> subList=new ArrayList<MenuEntity>();
		for(MenuEntity m:menus){
			if(menuId.equals(m.getParent_menu_id())){
				subList.add(m);
				m.setSubMenuList(this.getSubMenuList(m.getMenu_id(),menus));//针对多级子菜单
			}
		}
		return subList;
	}

	public List<ExpertInfoEntity> queryRegisterExpert(String status) {
		return loginMapper.queryRegisterExpert(status);
	}

	/**
	 * 查询专家修改待审核
	 * @return
	 */
	public List<ExpertInfoEntity> queryModifyExpert() {
		// TODO Auto-generated method stub
		return null;
	}
	
	/**
	 * 查询项目数量
	 * @param status
	 * @return
	 */
	public Long queryProjectCount(ProjectEntity project)
	{
		return loginMapper.queryProjectCount(project);
	}
	
	/**
	 * 查询项目数量
	 * @param status
	 * @return
	 */
	public String queryUserId(String userName)
	{
		return loginMapper.queryUserId(userName);
	}

	/**
	 * 发送短信验证码
	 * @param username
	 * @return
	 */
	public String sendSMs(UserEntity loginUser) {
		MWSmsUtil util = new MWSmsUtil();
		SmsRecordEntity smsRecordEntity = new SmsRecordEntity();
		smsRecordEntity.setSms_id(CommUtil.getKey());
		smsRecordEntity.setSms_mobile(loginUser.getMobile());
		
		String smscode = String.valueOf((int)((Math.random()*9+1)*100000));
		//String smscode ="123456";
		smsRecordEntity.setSms_content("<"+smscode+"> (专家库管理平台验证码)，有效期为3分钟。为了保护您的账号安全，验证短信请勿转告他人。");
		int result = util.singleSend(userId, userPwd, masterIpAddress, ipAddress1, ipAddress2,smsRecordEntity);
		//int result =0;
		if(result == 0) {
			return smscode;
		}else {
			return null;
		}
		
	}
	
	
}
