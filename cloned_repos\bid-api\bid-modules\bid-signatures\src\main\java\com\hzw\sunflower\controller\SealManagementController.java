package com.hzw.sunflower.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hzw.sunflower.common.BaseController;
import com.hzw.sunflower.entity.SealManagementOperateLog;
import com.hzw.sunflower.util.BeanListUtil;
import com.hzw.sunflower.common.Paging;
import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.constant.MessageConstants;
import com.hzw.sunflower.dto.SealManagementDTO;
import com.hzw.sunflower.controller.request.SealManagementREQ;
import com.hzw.sunflower.entity.SealManagement;
import com.hzw.sunflower.entity.condition.SealManagementCondition;
import com.hzw.sunflower.service.SealManagementService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
* SealManagementController
*/
@Api(tags = "印章管理")
@RestController
@RequestMapping("/sealManagement")
public class SealManagementController extends BaseController {
    @Autowired
    private SealManagementService sealManagementService;

    @ApiOperation(value = "根据条件分页查询SealManagement列表")
    @ApiImplicitParam(name = "sealManagementREQ", value = "用户表 查询条件", required = true, dataType = "SealManagementREQ", paramType = "body")
    @PostMapping("/list")
    public Result<List<SealManagement>>list(@RequestBody  SealManagementCondition condition) {
       // SealManagementCondition condition = BeanListUtil.convert(sealManagementREQ,SealManagementCondition.class);
        List<SealManagement> page = sealManagementService.findInfoByCondition(condition);
            return Result.ok(page);
    }


    @ApiOperation(value = "根据主键ID查询SealManagement信息")
    @ApiImplicitParam(name = "id", value = "主键ID", required = true)
    @GetMapping(value = "/get/{id}")
    public Result<SealManagement> get(@PathVariable Long id) {
        if (id == null) {
            return Result.failed(MessageConstants.GET_NULL);
        }
        SealManagement sealManagement = sealManagementService.getInfoById(id);
        return Result.ok(sealManagement);
    }

    @ApiOperation(value = "新增/修改")
    @ApiImplicitParam(name = "sealManagementDTO", value = "SealManagement表 ", required = true, dataType = "SealManagementDTO", paramType = "body")
    @PostMapping("/add")
    public Result<Boolean> add(@RequestBody SealManagementDTO sealManagementDTO) {
        return sealManagementService.addInfo(sealManagementDTO);
    }


    @ApiOperation(value = "停用/启用")
    @ApiImplicitParam(name = "sealManagementDTO", value = "SealManagement表 ", required = true, dataType = "SealManagementDTO", paramType = "body")
    @PostMapping("/updateStatus")
    public Result<Boolean> updateStatus(@RequestBody SealManagementDTO sealManagementDTO) {
        return sealManagementService.updateStatus(sealManagementDTO);
    }



    @ApiOperation(value = "根据主键ID删除SealManagement表 ")
    @ApiImplicitParam(name = "id", value = "主键ID", required = true)
    @DeleteMapping(value = "/delete/{id}")
    public Result<Boolean> delete(@PathVariable Long id) {
        if (id == null) {
            return Result.failed(MessageConstants.DELETE_NULL);
        }
        Boolean bool = sealManagementService.deleteById(id);
        return Result.okOrFailed(bool);
    }


    @ApiOperation(value = "根据主键ID查询SealManagement操作日志")
    @ApiImplicitParam(name = "id", value = "主键ID", required = true)
    @GetMapping(value = "/getLog/{id}")
    public Result<List<SealManagementOperateLog>> getLog(@PathVariable Long id) {
        if (id == null) {
            return Result.failed(MessageConstants.GET_NULL);
        }
        return  Result.ok(sealManagementService.getLog(id));
    }

}