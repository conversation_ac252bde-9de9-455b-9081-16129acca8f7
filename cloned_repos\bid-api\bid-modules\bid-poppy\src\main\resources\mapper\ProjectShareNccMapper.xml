<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="com.hzw.sunflower.dao.ProjectShareNccMapper">


    <select id="findInfo" resultType="com.hzw.sunflower.entity.ProjectShareNcc">
        SELECT
            s.*,
            IF( s.share_department_name LIKE CONCAT( '%', s.share_user_name, '%' ), d.department_name, s.share_department_name ) shareDepartmentName
        FROM
            t_project_share_ncc s
            LEFT JOIN t_department d ON s.share_department = d.id
            WHERE s.project_id = #{id}
            and s.is_delete = 0
            and d.is_delete = 0
    </select>
    <select id="listNCC" resultType="com.hzw.sunflower.entity.ProjectShareNcc">
        SELECT
            n.bookkeep_id,
            d.department_name shareUserName,
            d1.department_name shareDepartmentName,
            n.proportion
        FROM
            t_project_share_ncc n
            LEFT JOIN t_bookkeep_department d ON n.bookkeep_id = d.id
            LEFT JOIN t_bookkeep_department d1 ON d.pid = d1.id
        WHERE n.project_id = #{projectId}
        and n.is_delete = 0
        and d.is_delete = 0
        and d1.is_delete = 0
    </select>
</mapper>
