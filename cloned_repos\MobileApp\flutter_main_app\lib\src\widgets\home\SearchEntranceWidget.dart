///***
/// * 搜索入口页面
/// */

import "package:flutter/material.dart";
import 'package:flutter_main_app/serviceLocator.dart';

import 'package:flutter_main_app/src/constants/theme.dart';
import 'package:flutter_main_app/src/utils/ScreenUtils.dart';

import 'package:flutter_main_app/src/viewModels/home/<USER>';

import 'package:flutter_main_app/src/components/CustomTab.dart';

import 'package:flutter_main_app/src/widgets/home/<USER>/SearchHeader.dart';
import 'package:flutter_main_app/src/widgets/home/<USER>';

class SearchEntranceWidget extends StatefulWidget {
  const SearchEntranceWidget({Key key}) : super(key: key);

  /// * 路由名称
  static const String _routeName = 'SearchEntrance';
  static String get routeName => _routeName;

  @override
  _SearchEntranceWidgetState createState() => _SearchEntranceWidgetState();
}

class _SearchEntranceWidgetState extends State<SearchEntranceWidget> {

  final FocusNode _focusNode = FocusNode();

  HomeSearchViewModel _model = serviceLocator<HomeSearchViewModel>();

  String _keyword;

  /// * 搜索历史 - FutureBuilder 用
  Future<List<String>> _searchHistoryFuture;

  /// * 热门搜索 - FutureBuilder 用
  Future<List<String>> _popularSearchFuture;

  @override
  void initState() {
    super.initState();

    _keyword = _model.keyword;

    _searchHistoryFuture = _model.fetchSearchHistory();
  
    _popularSearchFuture = _model.fetchPopularSearch();
  }

  @override
  void dispose() {
    _focusNode.dispose();

    _searchHistoryFuture = null;
    _popularSearchFuture = null;

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        _unfocusTextField();
      },
      child: Scaffold(
        body: SingleChildScrollView(
          child: Column(
            children: <Widget>[
              /// * 头部
              SearchHeader(
                keyword: _keyword,
                onSubmittedCallback: (String keyword) {
                  setState(() {
                    _handleKeywordConfirmed(context, keyword);
                  });
                },
                focusNode: _focusNode,
              ),
              /// * 词条
              SingleChildScrollView(
                physics: BouncingScrollPhysics(),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: <Widget>[
                    /// * 搜索历史
                    Container(
                      color: themeContentBackgroundColor,
                      child: Column(
                        children: <Widget>[
                          /// * 标题
                          Padding(
                            padding: const EdgeInsets.fromLTRB(15, 16, 15, 15),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: <Widget>[
                                Text(
                                  '搜索历史',
                                  style: themeHintTextStyle,
                                ),
                                InkWell(
                                  onTap: () {},
                                  child: Text(
                                    '清除',
                                    style: themeHintTextStyle,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          /// * 词条列表
                          Container(
                            padding: const EdgeInsets.fromLTRB(30, 0, 30, 15),
                            width: ScreenUtils.screenWidth,
                            child: FutureBuilder(
                              future: _searchHistoryFuture,
                              builder: (BuildContext context, AsyncSnapshot snapshot) {
                                switch (snapshot.connectionState) {
                                  case ConnectionState.none:
                                  case ConnectionState.active:
                                  case ConnectionState.waiting:
                                    return Center(
                                      child: CircularProgressIndicator(),
                                    );
                                  case ConnectionState.done:
                                    if (snapshot.hasError) {
                                      return Text('Error: ${snapshot.error}');
                                    }

                                    return Wrap(
                                      spacing: 8.0, // 主轴(水平)方向间距
                                      runSpacing: 12.0, // 纵轴（垂直）方向间距
                                      alignment: WrapAlignment.start, //沿主轴方向居中
                                      children: _buildSearchHistory(context, snapshot.data),
                                    );
                                  default:
                                    return Container();
                                }
                              },
                            ),
                          )
                        ],
                      ),
                    ),
                    /// * 热门搜��
                    Padding(
                      padding: const EdgeInsets.only(top: 10),
                      child: Container(
                        color: themeContentBackgroundColor,
                        child: Column(
                          children: <Widget>[
                            /// * 标题
                            Padding(
                              padding: const EdgeInsets.fromLTRB(15, 16, 15, 15),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                children: <Widget>[
                                  Text(
                                    '热门搜索',
                                    style: TextStyle(
                                      fontSize: themeFontSize,
                                      color: themeHintTextColor,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            /// * 词条列表
                            Container(
                              width: ScreenUtils.screenWidth,
                              padding: EdgeInsets.fromLTRB(30, 0, 30, 15),
                              child: FutureBuilder(
                                future: _popularSearchFuture,
                                builder: (BuildContext context, AsyncSnapshot snapshot) {
                                  switch (snapshot.connectionState) {
                                    case ConnectionState.none:
                                    case ConnectionState.active:
                                    case ConnectionState.waiting:
                                      return Center(
                                        child: CircularProgressIndicator(),
                                      );
                                    case ConnectionState.done:
                                      if (snapshot.hasError) {
                                        return Text('Error: ${snapshot.error}');
                                      }

                                      return Wrap(
                                        spacing: 8.0, // 主轴(水平)方向间距
                                        runSpacing: 12.0, // 纵轴（垂直）方向间距
                                        alignment: WrapAlignment.start, //沿主轴方向居中
                                        children: _buildPopularSearch(context, snapshot.data),
                                      );
                                    default:
                                      return Container();
                                  }
                                },
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  ///***
  /// * 生成 搜索历史 词条列表 UI
  /// *
  /// * @param {BuildContext} context
  /// * @param {List<String>} keywords
  /// *
  /// * @return {List<Widget>}
  /// */
  List<Widget> _buildSearchHistory(BuildContext context, List<String> keywords) {
    final List<Widget> searchHistory = [];

    for (int i = 0; i < keywords.length; i++) {
      final Widget searchHistoryItem = InkWell(
        onTap: () {
          this._handleKeywordConfirmed(context, keywords[i]);
        },
        child: CustomTab(
          text: keywords[i],
          height: 28.0,
          horizontalPadding: 15.0,
          color: themeTipsTextColor,
          backgroundColor: themeBackgroundColor,
          borderColor: Colors.transparent,
          borderRadius: BorderRadius.zero,
        ),
      );

      searchHistory.add(searchHistoryItem);
    }

    return searchHistory;
  }

  ///***
  /// * 生成 热门搜索 词条列表 UI
  /// *
  /// * @param {BuildContext} context
  /// * @param {List<String>} keywords
  /// *
  /// * @return {List<Widget>}
  /// */
  List<Widget> _buildPopularSearch(BuildContext context, List<String> keywords) {
    final List<Widget> popularSearch = [];

    for (int i = 0; i < keywords.length; i++) {
      final Widget popularSearchItem = InkWell(
        onTap: () {
          this._handleKeywordConfirmed(context, keywords[i]);
        },
        child: CustomTab(
          text: keywords[i],
          height: 28.0,
          horizontalPadding: 15.0,
          color: themeTipsTextColor,
          backgroundColor: themeBackgroundColor,
          borderColor: Colors.transparent,
          borderRadius: BorderRadius.zero,
        ),
      );

      popularSearch.add(popularSearchItem);
    }

    return popularSearch;
  }

  ///***
  /// * 词条点击事件
  /// *
  /// * @param {BuildContext} context
  /// * @param {String} keyword
  /// */
  void _handleKeywordConfirmed(BuildContext context, String keyword) {
    _model.keyword = keyword;

    setState(() {
      _keyword = keyword;
    });

    _unfocusTextField();

    Navigator.pushNamed(context, SearchResultWidget.routeName);
  }

  ///***
  /// * 使文本框失焦
  /// */
  void _unfocusTextField() {
    _focusNode.unfocus();
  }

}
