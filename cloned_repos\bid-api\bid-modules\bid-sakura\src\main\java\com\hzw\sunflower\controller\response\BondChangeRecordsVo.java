package com.hzw.sunflower.controller.response;

import com.hzw.sunflower.entity.BondChangeRecords;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date ：Created in 2022/7/27 11:31
 * @description：查看调账记录返回实体类
 * @modified By：`
 * @version: 1.0
 */
@ApiModel(description = "查看调账记录返回实体类")
@Data
public class BondChangeRecordsVo extends BondChangeRecords implements Serializable {

    @ApiModelProperty(value = "操作人")
    private String operatorName;

}
