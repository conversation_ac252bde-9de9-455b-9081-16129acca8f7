package com.hzw.sunflower.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.hzw.sunflower.common.BaseBean;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@TableName("t_ncc_push_file_log")
public class NccPushFileLog extends BaseBean implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "业务id")
    private Long businessId;

    @ApiModelProperty(value = "业务类型 1.标书费 2.保证金")
    private Integer businessType;

    @ApiModelProperty(value = "文件类型 10.标书费蓝票 11.标书费红票 12.标书费换票申请单 13.标书费退款申请单 14.发票信息统计表 20.保证金银行回单 21.保证金退款申请单 22.保证金中标通知书存根 23.保证金代理费发票")
    private Integer fileType;

    @ApiModelProperty(value = "单据推送成功返回单据号")
    private String pkBill;

    @ApiModelProperty(value = "文件id")
    private Long fileId;

    @ApiModelProperty(value = "OSS文件key")
    private String ossFileKey;

    @ApiModelProperty(value = "OSS文件名称")
    private String ossFileName;

    @ApiModelProperty(value = "oss类型 1.天翼云")
    private String ossType;

    @ApiModelProperty(value = "推送结果 1待推送 2成功 3失败")
    private Integer pushResult;

    @ApiModelProperty(value = "推送次数")
    private Integer pushCount;

}
