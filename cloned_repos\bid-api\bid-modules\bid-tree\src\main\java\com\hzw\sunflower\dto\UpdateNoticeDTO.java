package com.hzw.sunflower.dto;

import com.hzw.sunflower.controller.request.NoticeMediaREQ;
import com.hzw.sunflower.entity.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 更新公告DTO
 *
 * <AUTHOR>
 * @version 1.0.0 2021/07/08
 */
@Data
@Accessors(chain = true)
public class UpdateNoticeDTO {
    /**
     * 公告基本信息
     */
    private ProjectBidNotice notice;
    /**
     * 需要新增的包段
     */
    private List<NoticePackageR> insertPackages;
    /**
     * 需要新增的关联附件
     */
    private List<RelevancyAnnex> insertAnnexes;
    /**
     * 需要新增的媒体关联
     */
//    private List<NoticeMediaR> insertMedias;

    @ApiModelProperty(value = "媒体信息")
    private List<NoticeMediaREQ> medias;
    /**
     * 需要和招标公告解除关联关系的包段ID集合，用于更新包段状态
     */
    private List<Long> deletePackageIds;
    /**
     * 需要更新和招标公告关联关系的包段ID集合，用于更新包段状态
     */
    private List<Long> updatePackageIds;
//
//    /**
//     * 招标文件阶段： 0：默认无阶段； 1：第一轮； 2：第二轮
//     */
//    private Integer bidRound;
}
