package com.hzw.sunflower.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzw.sunflower.dao.NccSyncFileMapper;
import com.hzw.sunflower.entity.NccSyncFile;
import com.hzw.sunflower.service.NccSyncFileService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @ClassName:NccSyncFileServiceImpl
 * @Auther: lijinxin
 * @Description: ncc推送文件
 * @Date: 2024/11/13 16:15
 * @Version: v1.0
 */

@Service
public class NccSyncFileServiceImpl extends ServiceImpl<NccSyncFileMapper, NccSyncFile> implements NccSyncFileService {
    @Override
    public List<NccSyncFile> getNccSyncFile() {
        //  查询没有推送成功 并且推送次数少少于三次的
        final Integer count = 3;
        LambdaQueryWrapper<NccSyncFile> lqw = new LambdaQueryWrapper<>();
        lqw.ne(NccSyncFile::getPushStatus,2);
        lqw.lt(NccSyncFile::getPushCount,count);
        return this.list(lqw);
    }
}
