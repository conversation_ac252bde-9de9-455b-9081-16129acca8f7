package com.hzw.sunflower.util;

import cn.hutool.core.bean.BeanUtil;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date ：Created in 2021/4/13 11:12
 * @description：对象相互赋值工具类
 * @modified By：`
 * @version: 1.0
 */
public class BeanListUtil extends BeanUtil {

    /**
     * 单个类转换
     *
     * @param sourceObj
     * @param targetClass
     * @param <T>
     * @return
     */
    public static <T> T convert(Object sourceObj, Class<T> targetClass) {
        if (sourceObj == null || targetClass == null) {
            return null;
        }
        T targetObj = null;
        try {
            targetObj = targetClass.getDeclaredConstructor().newInstance();
        } catch (Exception e) {
            return null;
        }
        BeanListUtil.copyProperties(sourceObj, targetObj);
        return targetObj;
    }

    /**
     * List之间转换
     *
     * @param sourceList
     * @param targetClass
     * @param <T>
     * @return
     */
    public static <T> List<T> convertList(List<?> sourceList, Class<T> targetClass) {
        if (CollectionUtils.isEmpty(sourceList) || targetClass == null) {
            return Collections.emptyList();
        }
        return sourceList.stream().map(sourceObj -> convert(sourceObj, targetClass)).collect(Collectors.toList());
    }
}
