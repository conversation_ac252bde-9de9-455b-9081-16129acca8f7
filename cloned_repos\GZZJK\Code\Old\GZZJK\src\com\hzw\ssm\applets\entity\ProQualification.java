package com.hzw.ssm.applets.entity;

/**
 * 执业资格
 *
 * <AUTHOR>
 * @Date 2021-02-03 9:59
 * @Version 1.0
 */
public class ProQualification {

    private String id;
    private String user_id;// 用户编号
    private String certificate;// 执业资格
    private String certificate_no;// 资格证书号
    private String certificate_fileid;// 资格证书复印件
    private int delete_flag;// 删除状态
    private String old_certificate;// 资格证书附件名称
    private String getTime;//获得时间

    private String picture;//图片

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getUser_id() {
        return user_id;
    }

    public void setUser_id(String user_id) {
        this.user_id = user_id;
    }

    public String getCertificate() {
        return certificate;
    }

    public void setCertificate(String certificate) {
        this.certificate = certificate;
    }

    public String getCertificate_no() {
        return certificate_no;
    }

    public void setCertificate_no(String certificate_no) {
        this.certificate_no = certificate_no;
    }

    public String getCertificate_fileid() {
        return certificate_fileid;
    }

    public void setCertificate_fileid(String certificate_fileid) {
        this.certificate_fileid = certificate_fileid;
    }

    public int getDelete_flag() {
        return delete_flag;
    }

    public void setDelete_flag(int delete_flag) {
        this.delete_flag = delete_flag;
    }

    public String getOld_certificate() {
        return old_certificate;
    }

    public void setOld_certificate(String old_certificate) {
        this.old_certificate = old_certificate;
    }

    public String getGetTime() {
        return getTime;
    }

    public void setGetTime(String getTime) {
        this.getTime = getTime;
    }

    public String getPicture() {
        return picture;
    }

    public void setPicture(String picture) {
        this.picture = picture;
    }
}