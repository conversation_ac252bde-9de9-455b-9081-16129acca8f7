package com.hzw.sunflower.constant.constantenum;

/**
 * 未关联保证金流水状态枚举
 */
public enum BondWaterNotRelationEnum {

    WAIT_APPLY_REFUND(1, "待申请退款"),
    WAIT_DISPOSE(2, "待处理"),
    IN_REFUND(3, "退款中"),
    ALREADY_REFUND(4,"已退款"),
    ALREADY_RETURN(5,"已退回");

    private Integer type;
    private String desc;

    BondWaterNotRelationEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

}
