package com.hzw.sunflower.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.hzw.sunflower.common.BaseBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * Created by zgq on 2021/06/30
 */
@ApiModel("媒体表")
@TableName("t_media")
@Data
@Accessors(chain = true)
public class NoticeMedia extends BaseBean {

    @ApiModelProperty(value = "媒体ID", position = 1)
    private Long id;

    @ApiModelProperty(value = "媒体名", position = 2)
    private String mediaName;

    @ApiModelProperty(value = "媒体链接", position = 3)
    private String mediaUrl;

    @ApiModelProperty(value = "是否公共：1 是；2 否", position = 4)
    private Integer state;

    @ApiModelProperty(value = "使用次数", position = 5)
    private Integer numberUse;
}
