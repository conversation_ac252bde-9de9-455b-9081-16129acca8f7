package com.hzw.ssm.applets.service;

import com.alibaba.fastjson.JSON;
import com.hzw.ssm.applets.dao.DictionaryMapper;
import com.hzw.ssm.applets.dao.TitleInfoMapper;
import com.hzw.ssm.applets.entity.Dictionary;
import com.hzw.ssm.applets.entity.TitleInfo;
import com.hzw.ssm.applets.exception.ExceptionEnum;
import com.hzw.ssm.applets.utils.ListToMap;
import com.hzw.ssm.applets.utils.RandomCode;
import com.hzw.ssm.applets.utils.ResultJson;
import com.hzw.ssm.expert.dao.ExpertInfoMapper;
import com.hzw.ssm.expert.entity.ExpertInfoEntity;
import com.hzw.ssm.expert.entity.ExpertUpdateRecordEntity;
import com.hzw.ssm.expert.service.ExpertInfoService;
import com.hzw.ssm.fw.base.BaseService;
import com.hzw.ssm.fw.util.CommUtil;
import com.hzw.ssm.sys.user.dao.UserMapper;
import com.hzw.ssm.sys.user.entity.UserEntity;
import com.hzw.ssm.util.string.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <AUTHOR>
 * @Date 2021-02-02 14:46
 * @Version 1.0
 */
@Service
public class TitleInfoService extends BaseService {

    private static Logger logger = Logger.getLogger(TitleInfoService.class);

    @Autowired
    private TitleInfoMapper titleInfoMapper;
    @Autowired
    private DictionaryMapper dictionaryMapper;
    @Autowired
    private ExpertInfoMapper expertInfoMapper;

    @Autowired
    private ExpertInfoService expertInfoService;
    @Autowired
    private UserMapper userMapper;

    public ResultJson insertTitleInfo(TitleInfo titleInfo) {

        try {
            //同一个职称仅允许添加一条记录
            TitleInfo info = new TitleInfo();
            info.setExpertId(titleInfo.getExpertId());
            info.setTitleId(titleInfo.getTitleId());
            info = titleInfoMapper.queryTitleInfo(info);
            if (null != info) {
                return new ResultJson(ExceptionEnum.TITLE_EXIST.getCode(), ExceptionEnum.TITLE_EXIST.getMessage());
            }

            List<Map<String, Object>> list = new ArrayList<>();
            Map<String, Object> map = new HashMap<>();

            String id = RandomCode.getId();
            titleInfo.setId(id);
            titleInfo.setCreateTime(new Date());
            titleInfo.setDeleteFlag("0");
            titleInfo.setPictureName("职称图片");
            // 处理图片
            List<String> pictures = titleInfo.getPictures();
            String json = JSON.toJSONString(pictures);
            titleInfo.setPicture(json);

            int updates = titleInfoMapper.insertTitleInfo(titleInfo);
            if (updates > 0) {

                // 如果用户已审核通过，修改需要重新提交审核
                ExpertInfoEntity infoEntity = new ExpertInfoEntity();
                infoEntity.setUser_id(titleInfo.getExpertId());
                ExpertInfoEntity entity = expertInfoMapper.getExpertInfoByUserIdOrId(infoEntity);
                Long status = entity.getStatus();
                if (3 == status || 8 == status) {
                    entity.setStatus(10L);
                    expertInfoMapper.updateExpertInfoById(entity);
                }
                // 新增记录
                if (3 == status || 8 == status || 4 == status || 10 == status) {
                    try {
                        infoSaveRecord(titleInfo);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
                map.put("id", id);
                list.add(map);
                return new ResultJson(list);
            }
        } catch (Exception e) {
            logger.error(e.getMessage());
        }
        return new ResultJson(ExceptionEnum.SERVER_IS_BUSY.getCode(), ExceptionEnum.SERVER_IS_BUSY.getMessage());
    }

    public ResultJson updateTitleInfo(TitleInfo titleInfo) {

        try {
            //同一个职称仅允许添加一条记录
            TitleInfo info = new TitleInfo();
            String expertId = titleInfo.getExpertId();
            info.setExpertId(expertId);
            info.setTitleId(titleInfo.getTitleId());
            info = titleInfoMapper.queryTitleInfo(info);
            if (null != info && !info.getId().equals(titleInfo.getId())) {
                return new ResultJson(ExceptionEnum.TITLE_EXIST.getCode(), ExceptionEnum.TITLE_EXIST.getMessage());
            }

            List<Map<String, Object>> list = new ArrayList<>();
            Map<String, Object> map = new HashMap<>();
            titleInfo.setUpdateTime(new Date());
            titleInfo.setPictureName("职称图片");

            // 处理图片
            List<String> pictures = titleInfo.getPictures();
            String json = JSON.toJSONString(pictures);
            titleInfo.setPicture(json);

            info = new TitleInfo();
            info.setId(titleInfo.getId());
            info = titleInfoMapper.getTitleInfo(info);
            int updates = titleInfoMapper.updateTitleInfo(titleInfo);
            if (updates > 0) {
                // 如果用户已审核通过，修改需要重新提交审核
                ExpertInfoEntity infoEntity = new ExpertInfoEntity();
                infoEntity.setUser_id(expertId);
                ExpertInfoEntity entity = expertInfoMapper.getExpertInfoByUserIdOrId(infoEntity);
                Long status = entity.getStatus();
                if (3 == status || 8 == status) {
                    entity.setStatus(10L);
                    expertInfoMapper.updateExpertInfoById(entity);
                }
                // 修改记录
                if (3 == status || 8 == status || 4 == status || 10 == status) {
                    try {
                        infoRecord(info, titleInfo);
                    } catch (Exception e) {
                        System.out.println("记录出错!");
                    }
                }
                map.put("id", titleInfo.getId());
                list.add(map);
                return new ResultJson(list);
            }
        } catch (Exception e) {
            logger.error(e.getMessage());
        }
        return new ResultJson(ExceptionEnum.SERVER_IS_BUSY.getCode(), ExceptionEnum.SERVER_IS_BUSY.getMessage());
    }

    private void infoSaveRecord(TitleInfo after) {
        UserEntity userEntity = userMapper.selectUserById(after.getExpertId());
        String role_id;
        if (userEntity != null) {
            role_id = StringUtils.toStringNull(userEntity.getRole_id());
        } else {
            role_id = "无";
        }

        HashMap<String, String> techMap = new HashMap<>();
        List<Dictionary> dictionaries = dictionaryMapper.queryTitle();
        for (Dictionary dictionary : dictionaries) {
            techMap.put(dictionary.getDataCode(), dictionary.getDataValue());
        }

        if (StringUtils.isNotEmpty(after.getTitleId())) {// 职称名称id
            addExpertUpdateRecord(after.getExpertId(), "职称名称", null, StringUtils.toStringNull(techMap.get(after.getTitleId())), role_id, after.getExpertId(), "职称信息", 0);
        }
        if (StringUtils.isNotEmpty(after.getGetTime())) {// 获得时间
            addExpertUpdateRecord(after.getExpertId(), "职称获得时间", null, StringUtils.toStringNull(after.getGetTime()), role_id, after.getExpertId(), "职称信息", 0);
        }
        if (StringUtils.isNotEmpty(after.getPicture())) {// 职称图片
            String afterStyle = "<a style=\"color:blue\" onclick=\"javascript:picPreview(530,450,'/GZZJK/" + StringUtils.toStringNull(after.getPictures().get(0)) + "')\">" + StringUtils.toStringNull(after.getPictureName()) + "</a>";
            addExpertUpdateRecord(after.getExpertId(), "职称图片", null, afterStyle, role_id, after.getExpertId(), "职称信息", 0);
        }
    }

    private void infoRecord(TitleInfo before, TitleInfo after) {
        if (before != null && after != null) {
            UserEntity userEntity = userMapper.selectUserById(before.getExpertId());
            String role_id;
            if (userEntity != null) {
                role_id = StringUtils.toStringNull(userEntity.getRole_id());
            } else {
                role_id = "无";
            }

            HashMap<String, String> techMap = new HashMap<>();
            List<Dictionary> dictionaries = dictionaryMapper.queryTitle();
            for (Dictionary dictionary : dictionaries) {
                techMap.put(dictionary.getDataCode(), dictionary.getDataValue());
            }

            if (StringUtils.isNotEmpty(after.getTitleId()) && !StringUtils.toStringNull(after.getTitleId()).equalsIgnoreCase(StringUtils.toStringNull(before.getTitleId()))) {// 职称名称id
                addExpertUpdateRecord(after.getExpertId(), "职称名称", StringUtils.toStringNull(techMap.get(before.getTitleId())), StringUtils.toStringNull(techMap.get(after.getTitleId())), role_id, after.getExpertId(), "职称信息", 0);
            }
            if (StringUtils.isNotEmpty(after.getGetTime()) && !StringUtils.toStringNull(after.getGetTime()).equalsIgnoreCase(StringUtils.toStringNull(before.getGetTime()))) {// 获得时间
                addExpertUpdateRecord(after.getExpertId(), "职称获得时间", StringUtils.toStringNull(before.getGetTime()), StringUtils.toStringNull(after.getGetTime()), role_id, after.getExpertId(), "职称信息", 0);
            }
            if (StringUtils.isNotEmpty(after.getPicture()) && !StringUtils.toStringNull(after.getPicture()).equalsIgnoreCase(StringUtils.toStringNull(before.getPicture()))) {// 职称图片
                List<String> beforePic = JSON.parseArray(before.getPicture(), String.class);
                String befoStyle = "<a style=\"color:blue\" onclick=\"javascript:picPreview(530,450,'/GZZJK/" + StringUtils.toStringNull(beforePic.get(0)) + "')\">" + StringUtils.toStringNull(before.getPictureName()) + "</a>";
                String afterStyle = "<a style=\"color:blue\" onclick=\"javascript:picPreview(530,450,'/GZZJK/" + StringUtils.toStringNull(after.getPictures().get(0)) + "')\">" + StringUtils.toStringNull(after.getPictureName()) + "</a>";
                addExpertUpdateRecord(after.getExpertId(), "职称图片", befoStyle, afterStyle, role_id, after.getExpertId(), "职称信息", 0);
            }
        }
    }

    /**
     * 新增专家信息修改记录
     *
     * @param user_id        用户编号
     * @param item           修改项
     * @param content_before 修改前内容
     * @param content_after  修改后内容
     * @param modify_role    修改角色
     * @param modify_user    修改人
     * @param modify_reason  修改原因
     * @param batch_number   修改批次编号
     */
    public void addExpertUpdateRecord(String user_id, String item, String content_before, String content_after, String modify_role, String modify_user, String modify_reason, int batch_number) {
        ExpertUpdateRecordEntity record = new ExpertUpdateRecordEntity();
        record.setId(CommUtil.getKey());
        record.setUser_id(user_id);
        record.setItem(item);
        record.setContent_before(content_before);
        record.setContent_after(content_after);
        record.setModify_role(modify_role);
        record.setModify_user(modify_user);
        record.setModify_reason(modify_reason);
        record.setBatch_number(batch_number);
        record.setModify_time(new Date());
        record.setDelete_flag(0);
        expertInfoMapper.addExpertUpdateRecord(record);
    }

    public ResultJson deleteTitleInfo(TitleInfo titleInfo) {
        try {
            List<Map<String, Object>> list = new ArrayList<>();
            Map<String, Object> map = new HashMap<>();
            TitleInfo info = titleInfoMapper.getTitleInfo(titleInfo);

            int updates = titleInfoMapper.deleteTitleInfo(titleInfo);
            if (updates > 0) {

                // 如果用户已审核通过，修改需要重新提交审核
                ExpertInfoEntity infoEntity = new ExpertInfoEntity();
                infoEntity.setUser_id(info.getExpertId());
                ExpertInfoEntity entity = expertInfoMapper.getExpertInfoByUserIdOrId(infoEntity);
                Long status = entity.getStatus();
                if (3 == status || 8 == status) {
                    entity.setStatus(10L);
                    expertInfoMapper.updateExpertInfoById(entity);
                }
                // 删除记录
                if (3 == status || 8 == status || 4 == status || 10 == status) {
                    try {
                        deleteTitleInfoRecord(info);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
                map.put("id", titleInfo.getId());
                list.add(map);
                return new ResultJson(list);
            }
        } catch (Exception e) {
            logger.error(e.getMessage());
        }
        return new ResultJson(ExceptionEnum.SERVER_IS_BUSY.getCode(), ExceptionEnum.SERVER_IS_BUSY.getMessage());
    }

    private void deleteTitleInfoRecord(TitleInfo info) {
        UserEntity userEntity = userMapper.selectUserById(info.getExpertId());

        HashMap<String, String> techMap = new HashMap<>();
        List<Dictionary> dictionaries = dictionaryMapper.queryTitle();
        for (Dictionary dictionary : dictionaries) {
            techMap.put(dictionary.getDataCode(), dictionary.getDataValue());
        }

        String titleName = techMap.get(info.getTitleId());
        String content_after = "删除了【" + titleName + "】的职称信息";
        addExpertUpdateRecord(info.getExpertId(), "职称信息", null, content_after, userEntity.getRole_id(), info.getExpertId(), "职称信息", 0);
    }

    public ResultJson getTitleInfo(TitleInfo titleInfo) {

        Map<String, Object> titleMap = dictionaryUtils();

        List<TitleInfo> list = new ArrayList<>();
        TitleInfo title = titleInfoMapper.getTitleInfo(titleInfo);
        String titleName = String.valueOf(titleMap.get(title.getTitleId()));
        Dictionary dictionary = dictionaryMapper.queryTitleByCode(title.getTitleId());
        String expertType = dictionary.getExpertType();
        title.setExpertType(expertType);

        // 处理图片
        String picture = title.getPicture();
        List<String> pictures = JSON.parseArray(picture, String.class);
        title.setPictures(pictures);

        title.setTitleName(titleName);
        list.add(title);
        return new ResultJson(list);
    }

    public ResultJson getTitleInfos(TitleInfo titleInfo) {

        Map<String, Object> titleMap = dictionaryUtils();

        List<TitleInfo> titleInfos = titleInfoMapper.getTitleInfos(titleInfo);
        for (TitleInfo info : titleInfos) {
            String titleName = String.valueOf(titleMap.get(info.getTitleId()));
            info.setTitleName(titleName);
            // 处理图片
            String picture = info.getPicture();
            List<String> pictures = JSON.parseArray(picture, String.class);
            info.setPictures(pictures);
        }
        return new ResultJson(titleInfos);
    }


    /**
     * 封装公共返回字典map
     *
     * @return
     */
    public Map<String, Object> dictionaryUtils() {
        List<Dictionary> dictionaries = dictionaryMapper.queryTitle();
        Map<String, Object> dictionaryMap = new HashMap<>();
        if (null != dictionaries && dictionaries.size() > 0) {
            dictionaryMap = ListToMap.listToMap(dictionaries);
        }
        return dictionaryMap;

    }

}
