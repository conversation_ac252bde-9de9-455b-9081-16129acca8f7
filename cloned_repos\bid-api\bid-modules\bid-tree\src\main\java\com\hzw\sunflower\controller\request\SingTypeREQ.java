package com.hzw.sunflower.controller.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @ClassName:SingTypeREQ
 * @Auther: lijinxin
 * @Description: 唱标类型
 * @Date: 2023/11/15 17:24
 * @Version: v1.0
 */
@Data
public class SingTypeREQ {

    @ApiModelProperty(value = "标段id")
    private Long sectionId;

    @ApiModelProperty(value = "项目id")
    private Long projectId;

    @ApiModelProperty(value ="招标文件阶段：1：第一轮；2：第二轮")
    private Integer bidRound;

}
