package com.hzw.ssm.sys.project.entity;

import com.hzw.ssm.fw.base.BaseEntity;
/**
 * 专家回避
 * <AUTHOR>
 *
 */
public class ExtractDebarbEntity extends BaseEntity{
	private static final long serialVersionUID = -8151736570527763583L;
	/**
	 * 主键ID
	 */
	private String id;
	/**
	 * 批次号
	 */
	private String decimationbatch;
	/**
	 * 回避公司
	 */
	private String company;
	/**
	 * 回避专家身份证
	 */
	private String expertCode;
	
	/**
	 *	回避专家手机号码
	 */
	private String expertPhone;
	/**
	 * 专家姓名
	 */
	private String expertName;
	/**
	 * 回避理由
	 */
	private String debarbReason;
	/**
	 * 公司回避理由
	 */
	private String companyDebarbReason;
	/**
	 * 专家回避理由
	 */
	private String expertDebarbReason;
	/**
	 * 多个机构
	 */
	private String debarbCompanys;
	/**
	 * 多个专家
	 */
	private String debarbExpertCodes;
	/**
	 * 回避类型
	 */
	private String debarbType;
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public String getDecimationbatch() {
		return decimationbatch;
	}
	public void setDecimationbatch(String decimationbatch) {
		this.decimationbatch = decimationbatch;
	}
	public String getCompany() {
		return company;
	}
	public void setCompany(String company) {
		this.company = company;
	}

	public String getExpertCode() {
		return expertCode;
	}
	public void setExpertCode(String expertCode) {
		this.expertCode = expertCode;
	}
	public String getCompanyDebarbReason() {
		return companyDebarbReason;
	}
	public void setCompanyDebarbReason(String companyDebarbReason) {
		this.companyDebarbReason = companyDebarbReason;
	}
	public String getExpertDebarbReason() {
		return expertDebarbReason;
	}
	public void setExpertDebarbReason(String expertDebarbReason) {
		this.expertDebarbReason = expertDebarbReason;
	}
	public String getDebarbCompanys() {
		return debarbCompanys;
	}
	public void setDebarbCompanys(String debarbCompanys) {
		this.debarbCompanys = debarbCompanys;
	}
	public String getDebarbExpertCodes() {
		return debarbExpertCodes;
	}
	public void setDebarbExpertCodes(String debarbExpertCodes) {
		this.debarbExpertCodes = debarbExpertCodes;
	}
	public String getDebarbReason() {
		return debarbReason;
	}
	public void setDebarbReason(String debarbReason) {
		this.debarbReason = debarbReason;
	}
	public String getDebarbType() {
		return debarbType;
	}
	public void setDebarbType(String debarbType) {
		this.debarbType = debarbType;
	}
	public String getExpertName() {
		return expertName;
	}
	public void setExpertName(String expertName) {
		this.expertName = expertName;
	}
	public String getExpertPhone() {
		return expertPhone;
	}
	public void setExpertPhone(String expertPhone) {
		this.expertPhone = expertPhone;
	}
	
}
