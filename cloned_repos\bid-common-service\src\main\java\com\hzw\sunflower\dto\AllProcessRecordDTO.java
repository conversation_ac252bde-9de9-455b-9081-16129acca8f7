package com.hzw.sunflower.dto;

import com.hzw.sunflower.entity.User;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class AllProcessRecordDTO implements Serializable {

    @ApiModelProperty(value = "用户otherId")
    private String userCode;

    @ApiModelProperty(value = "流程key")
    private String processDefinitionKey;

    @ApiModelProperty(value = "业务code")
    private String businessKey;

    @ApiModelProperty(value = "节点名称")
    private String approvalNode;

    @ApiModelProperty(value = "操作人")
    private User user;

    @ApiModelProperty(value = "操作")
    private String operation;

    @ApiModelProperty(value = "是否为跳过节点  1 是  2 否")
    private Integer isJump;

    @ApiModelProperty(value = "审批备注")
    private String remark;

    //@ApiModelProperty(value = "节点备注")
    private String message;



}
