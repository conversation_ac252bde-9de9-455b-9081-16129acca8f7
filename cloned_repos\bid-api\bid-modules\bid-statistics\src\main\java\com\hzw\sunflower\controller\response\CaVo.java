package com.hzw.sunflower.controller.response;

import cn.afterturn.easypoi.excel.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2023/3/13 15:50 星期一
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CaVo {

    @ApiModelProperty("CA供应商今日绑定数量")
    private Long caUserTodaySum;

    @ApiModelProperty("CA供应商全部绑定数量")
    private Long caUserSum;

    @ApiModelProperty("CA扫码登录今日数量")
    private Long caLoginTodaySum;

    @ApiModelProperty("今日加密次数")
    private Long encTodaySum;

    @ApiModelProperty("今日解密次数")
    private Long decTodaySum;

    @ApiModelProperty("CA扫码登录全部数量")
    private Long caLoginSum;

    @ApiModelProperty("全部加密次数")
    private Long encSum;

    @ApiModelProperty("全部解密次数")
    private Long decSum;



}
