package com.hzw.sunflower.service.bidder;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.controller.project.request.ProjectAcceptanceRep;
import com.hzw.sunflower.controller.project.request.bidder.*;
import com.hzw.sunflower.controller.project.response.ProjectAcceptanceListVo;
import com.hzw.sunflower.controller.project.response.ProjectBidSectionVO;
import com.hzw.sunflower.controller.project.response.bidder.LenovoTenantUserVo;
import com.hzw.sunflower.controller.project.response.bidder.MyselfProjectVo;
import com.hzw.sunflower.dto.AgentProjectDTO;
import com.hzw.sunflower.dto.ProjectListDTO;
import com.hzw.sunflower.dto.UserAgentRecordDTO;
import com.hzw.sunflower.entity.JwtUser;
import com.hzw.sunflower.entity.Project;
import com.hzw.sunflower.entity.ProjectAgentApproval;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2021/6/10 9:53
 * @description：招标人项目service
 * @modified By：`
 * @version: 1.0
 */
public interface BidderProjectService {

    /**
     * 根据条件分页查询项目表 列表
     *
     * @param condition 查询条件
     * @return 分页信息
     */
    IPage<ProjectListDTO> queryProjectByPage(ProjectConditionRep condition, Long userId);

    /**
     * 根据条件分页查询项目表 列表
     *
     * @param condition 查询条件
     * @return 分页信息
     */
    IPage<ProjectListDTO> queryEntrustProjectByPage(ProjectConditionRep condition, Long userId);

    /**
     * 代理机构新增委托项目入口
     *
     * @param agentProject
     * @return
     */
    Boolean addAgentProject(BidderProjectReq agentProject, JwtUser user);

    /**
     * 获取用户上一次填写的代理机构信息
     *
     * @return
     */
    UserAgentRecordDTO selectAgent(JwtUser user);

    /**
     * 修改招标人新建项目
     *
     * @param agentProject
     * @param jwtUser
     * @return
     */
    Boolean updateProject(BidderProjectReq agentProject, JwtUser jwtUser);

    /**
     * 获取项目信息
     *
     * @param id
     * @return
     */
    AgentProjectDTO getProject(Long id);


    /**
     * 删除项目信息
     *
     * @param id
     * @return
     */
    Result<Boolean> deleteProject(Long id);

    /**
     * 招标人撤回项目
     *
     * @param id
     * @return
     */
    Result<Boolean> updateRecall(Long id);

    /**
     * 招标人转授权
     *
     * @param req
     * @return
     */
    Result<Boolean> updateSubmandate(ProjectSubmandateReq req);


    /**
     * 获取项目信息
     *
     * @param id
     * @return
     */
    Result<AgentProjectDTO> getProjectEntrust(Long id);

    /**
     * D-006 招标人项目退回
     * @param projectId
     * @param reason
     * @return
     */
    Boolean updateProjectAgencyNoPass(Long projectId, String reason);

    /**
     * D-006 招标人项目确认
     * @param record
     * @return
     */
    Boolean updateProjectAgencyPass(ProjectAgentApproval record);

    /**
     * 运管处审核列表
     * @param projectAcceptanceRep
     * @return
     */
    IPage<ProjectAcceptanceListVo> selectYgcProjectList(ProjectAcceptanceRep projectAcceptanceRep);


    /**
     * 处长项目经理审批列表
     * @param projectAcceptanceRep
     * @return
     */
    IPage<ProjectAcceptanceListVo> selectAcceptanceProjectList(ProjectAcceptanceRep projectAcceptanceRep);

    /**
     * 根据条件分页查询项目列表
     * @param req
     * @param jwtUser
     * @return
     */
    IPage<ProjectListDTO> queryMyselfProjectByPage(MyselfProjectConditionReq req, JwtUser jwtUser);

    /**
     * 新建自采项目
     * @param req
     * @param jwtUser
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    Boolean addMyselfProject(MyselfProjectReq req, JwtUser jwtUser);

    /**
     * 招标人重新招标查询项目详情
     * @param req
     * @return
     */
    MyselfProjectVo queryRePurchaseProject(BidRePurchaseReq req);

    /**
     * 自采项目重新组织采购
     * @param req
     * @param jwtUser
     * @return
     */
    Boolean addReProjectMyself(MyselfReProjectReq req, JwtUser jwtUser);

    /**
     * 修改自采项目信息
     * @param req
     * @param jwtUser
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    Boolean updateMyselfProject(BidUpdateProjectReq req, JwtUser jwtUser);

    /**
     * 当前租户信息联想功能
     * @param req
     * @return
     */
    List<LenovoTenantUserVo> queryTenantInfo(LenovoTenantUserReq req);

    /**
     * 根据标段ID查询标段信息
     * @param req
     * @return
     */
    ProjectBidSectionVO querySection(ProjectSectionIdReq req);

    /**
     * 根据项目ID查询信息
     * @param req
     * @return
     */
    Project queryProject(ProjectSectionIdReq req);

    /**
     * 查询上一次填写代理信息（采购经理）
     * @param jwtUser
     * @return
     */
    UserAgentRecordDTO selectLastAgent(JwtUser jwtUser);
}
