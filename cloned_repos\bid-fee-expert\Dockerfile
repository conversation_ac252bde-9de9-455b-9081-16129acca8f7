FROM registry.cn-hangzhou.aliyuncs.com/hzw-jtcc/openjdk:11.0.4
ENV TZ=Asia/Shanghai
ENV LANG C.UTF-8
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone
VOLUME /log
COPY target/bid-expertFee-api-1.0.0.jar bid-expertFee-api.jar
COPY target/classes/application-prod.yml /config/application-prod.yml
COPY target/classes/application.yml /config/application.yml
EXPOSE 28929
#docker run -p 主机端口:容器端口 -it  --name 指定启动容器名称 -v /export:/export -d 镜像名称
ENTRYPOINT ["sh","-c", "java $JAVA_OPTS -Dspring.profiles.active=prod -jar /bid-expertFee-api.jar "]
