package com.hzw.common.mybatis.handler;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpStatus;
import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.hzw.common.core.exception.ServiceException;
import com.hzw.common.core.utils.StringUtils;
import com.hzw.common.basebean.web.domain.BaseEntity;
import com.hzw.common.satoken.utils.LoginHelper;
import com.hzw.system.api.model.LoginUser;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.reflection.MetaObject;

import java.util.Date;

/**
 * MP注入处理器
 *
 * <AUTHOR>
 * @date 2021/4/25
 */
@Slf4j
public class CreateAndUpdateMetaObjectHandler implements MetaObjectHandler {

    @Override
    public void insertFill(MetaObject metaObject) {
        try {
            if (ObjectUtil.isNotNull(metaObject) && metaObject.getOriginalObject() instanceof BaseEntity) {
                BaseEntity baseEntity = (BaseEntity) metaObject.getOriginalObject();
                Date current = ObjectUtil.isNotNull(baseEntity.getCreatedTime())
                    ? baseEntity.getCreatedTime() : new Date();
                baseEntity.setCreatedTime(current);
                baseEntity.setUpdatedTime(current);
//                String username = StringUtils.isNotBlank(baseEntity.getCreatedUserId())
//                    ? baseEntity.getCreatedUserId() : getLoginUsername();
                // 当前已登录 且 创建人为空 则填充
//                baseEntity.setCreatedUserId(username);
                // 当前已登录 且 更新人为空 则填充
//                baseEntity.setUpdatedUserId(username);
               String userid  = StringUtils.isNotBlank(baseEntity.getCreatedUserId())
                   ? baseEntity.getCreatedUserId() : getLoginUserId();
                // 当前已登录 且 创建人为空 则填充
                baseEntity.setCreatedUserId(userid);
                // 当前已登录 且 更新人为空 则填充
                baseEntity.setUpdatedUserId(userid);
                baseEntity.setIsDelete(0);

            }
        } catch (Exception e) {
            throw new ServiceException("自动注入异常 => " + e.getMessage(), HttpStatus.HTTP_UNAUTHORIZED);
        }
    }

    @Override
    public void updateFill(MetaObject metaObject) {
        try {
            if (ObjectUtil.isNotNull(metaObject) && metaObject.getOriginalObject() instanceof BaseEntity) {
                BaseEntity baseEntity = (BaseEntity) metaObject.getOriginalObject();
                Date current = new Date();
                // 更新时间填充(不管为不为空)
                baseEntity.setUpdatedTime(current);
/*                String username = getLoginUsername();
                // 当前已登录 更新人填充(不管为不为空)
                if (StringUtils.isNotBlank(username)) {
                    baseEntity.setUpdatedUserId(username);
                }*/
                String userid = getLoginUserId();
                // 当前已登录 更新人填充(不管为不为空)
                if (StringUtils.isNotBlank(userid)) {
                    baseEntity.setUpdatedUserId(userid);
                }
            }
        } catch (Exception e) {
            throw new ServiceException("自动注入异常 => " + e.getMessage(), HttpStatus.HTTP_UNAUTHORIZED);
        }
    }

    /**
     * 获取登录用户名
     */
    private String getLoginUsername() {
        LoginUser loginUser;
        try {
            loginUser = LoginHelper.getLoginUser();
        } catch (Exception e) {
            log.warn("自动注入警告 => 用户未登录");
            return null;
        }
        return ObjectUtil.isNotNull(loginUser) ? loginUser.getUsername() : null;
    }

    /**
     * 获取登录ID
     */
    private String getLoginUserId() {
        LoginUser loginUser;
        try {
            loginUser = LoginHelper.getLoginUser();
        } catch (Exception e) {
            log.warn("自动注入警告 => 用户未登录");
            return null;
        }
        return ObjectUtil.isNotNull(loginUser) ? loginUser.getUserId() : null;
    }

}
