package com.hzw.sunflower.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.hzw.sunflower.constant.CommonConstants;
import com.hzw.sunflower.constant.constantenum.*;
import com.hzw.sunflower.dao.NoticePackageRMapper;
import com.hzw.sunflower.dao.ProjectBidNoticeMapper;
import com.hzw.sunflower.dao.RelevancyAnnexMapper;
import com.hzw.sunflower.entity.*;
import com.hzw.sunflower.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


import java.util.List;
import java.util.stream.Collectors;


@Service(value = "NoticePreQualificationServiceImpl")
public class NoticePreQualificationServiceImpl implements PreQualificationService {


    @Autowired
    ProjectService projectService;
    @Autowired
    ProjectBidNoticeMapper projectBidNoticeMapper;
    @Autowired
    private NoticePackageRService packageRService;
    @Autowired
    private NoticePackageRMapper noticePackageRMapper;
    @Autowired
    private ProjectBidSectionService projectBidSectionService;
    @Autowired
    private ProjectBidDocService projectBidDocService;
    @Autowired
    private RelevancyAnnexMapper relevancyAnnexMapper;


    /**
     * 资格预审按项目完善信息
     *
     * @param projectId
     * @param projectBidSection
     * @return
     */
    @Override
    public Boolean savePreQualificationInfo(Long projectId, ProjectBidSection projectBidSection) {
       Long sectionId = projectBidSection.getId();
       // 判断是否发布过公告 如果是就返回不追加
        ProjectBidNotice projectBidNotice=projectBidNoticeMapper.selectBySectionIdAndRound(projectId,sectionId, BidRoundEnum.ZGYS.getType());
        if(projectBidNotice!=null){
            return true;
        }
        Boolean f=false;
        // 查询资格预审按项目公告 追加标段到此公告
        LambdaQueryWrapper bidQueryWrapper=new LambdaQueryWrapper<ProjectBidSection>()
                .notIn(ProjectBidSection::getId,sectionId)
                .eq(ProjectBidSection::getProjectId,projectId)
                .eq(ProjectBidSection::getPurchaseStatus, PurchaseStatusEnum.PRE_TRIAL.getType())
                .eq(ProjectBidSection::getPurchaseMode,projectBidSection.getPurchaseMode());
        List<ProjectBidSection> list = projectBidSectionService.list(bidQueryWrapper);

        List<Long> newSectionIds =list.stream().map(m->{
            return m.getId();
        }).collect(Collectors.toList());

//        QueryWrapper noticePackageRQueryWrapper=new QueryWrapper<NoticePackageR>()
//                .in("section_id",newSectionIds)
//                .eq("is_delete","0");
//
//        List<NoticePackageR> noticePackageList= packageRService.list(noticePackageRQueryWrapper);


//       if (noticePackageList!=null&&noticePackageList.size()>0){
       if (list!=null&&list.size()>0){

//            NoticePackageR preNoticePackage=noticePackageList.get(0);
//            ProjectBidNotice projectBidNotice= projectBidNoticeMapper.selectById(preNoticePackage.getNoticeId());

              List<ProjectBidNotice> projectBidNotices = projectBidNoticeMapper.selectBySectionIdsAndRound(projectId,newSectionIds, BidRoundEnum.ZGYS.getType());


//            noticePackageRQueryWrapper=new QueryWrapper<NoticePackageR>()
//                    .in("section_id",newSectionIds)
//                    .eq("is_delete","0");
//
//           noticePackageRMapper.delete(noticePackageRQueryWrapper);
           // 将标段加入公告
//           List<NoticePackageR> batchSaveList=new ArrayList<>();
//           for (ProjectBidSection bidSection : list) {
//               NoticePackageR noticePackageR=new NoticePackageR();
//               noticePackageR.setId(null);
//               noticePackageR.setNoticeId(projectBidNotice.getNoticeId());
//               noticePackageR.setSectionId(bidSection.getId());
//               noticePackageR.setState(NoticeTypeEnum.PURCHASE_NOTICE.getValue());
//               noticePackageR.setIsDelete(0);
//               noticePackageR.setVersion(0);
//               batchSaveList.add(noticePackageR);
//           }
//
//           if (batchSaveList.size()>0){
//               f=packageRService.saveBatch(batchSaveList);
//           }
           if(projectBidNotices!=null&&projectBidNotices.size()>0){
               projectBidNotice=projectBidNotices.get(0);
               NoticePackageR noticePackageR=new NoticePackageR();
               noticePackageR.setId(null);
               noticePackageR.setNoticeId(projectBidNotice.getId());
               noticePackageR.setSectionId(sectionId);
               noticePackageR.setState(NoticeTypeEnum.PURCHASE_NOTICE.getValue());
               noticePackageR.setIsDelete(CommonConstants.NO);
               noticePackageR.setVersion(0);
               packageRService.save(noticePackageR);
               // 更新标段状态
               UpdateWrapper<ProjectBidSection> projectBidSectionUpdateWrapper = new UpdateWrapper<>();
               projectBidSectionUpdateWrapper
                       .eq("id",sectionId)
                       .eq("is_delete",CommonConstants.NO);
               if (NoticeProgressEnum.DRAFTING.getValue().equals(projectBidNotice.getNoticeProgress())||
                       NoticeProgressEnum.WITHDRAWAL.getValue().equals(projectBidNotice.getNoticeProgress())||
                       NoticeProgressEnum.RETURN.getValue().equals(projectBidNotice.getNoticeProgress())){
                   projectBidSectionUpdateWrapper.set("status", PackageStatusEnum.EDIT_BY_NOTICE.getValue().toString());
               }else if(NoticeProgressEnum.RELEASE.getValue().equals(projectBidNotice.getNoticeProgress())){
                   projectBidSectionUpdateWrapper.set("status", PackageStatusEnum.NOTICE_ISSUED.getValue().toString());
               }else {
                   projectBidSectionUpdateWrapper.set("status", PackageStatusEnum.READONLY_BY_NOTICE.getValue().toString());
               }
               if(projectBidDocService.judgeStatusByDoc(sectionId)){
                   projectBidSectionService.update(projectBidSectionUpdateWrapper);
               }

           }else {

           }
            f=true;
        }else {
            f=true;
        }


        return f;
    }

    @Override
    public Boolean deletePreQualificationInfo(Long projectId, Long sectionId) {
        boolean f=true;
        // 查询公告关系表
        QueryWrapper noticePackageRQueryWrapper = new QueryWrapper<NoticePackageR>()
                .eq("section_id",sectionId)
                .eq("is_delete",CommonConstants.NO);
        NoticePackageR  noticePackageR = noticePackageRMapper.selectOne(noticePackageRQueryWrapper);
        if (noticePackageR!=null){
            // 删除标段公告关联关系
            noticePackageRMapper.deleteById(noticePackageR.getId());
            // 查询该公告的关联的所有标段
            noticePackageRQueryWrapper = new QueryWrapper<NoticePackageR>()
                    .eq("notice_id",noticePackageR.getNoticeId())
//                    .notIn("section_id",sectionId)
                    .eq("is_delete",CommonConstants.NO);
            List<NoticePackageR> list = noticePackageRMapper.selectList(noticePackageRQueryWrapper);
            if (list==null){
                // 删除公共关联的附件
                QueryWrapper annexQueryWrapper=new QueryWrapper<RelevancyAnnex>();
                annexQueryWrapper.eq("bus_id",noticePackageR.getNoticeId());
                annexQueryWrapper.eq("file_type",1);
                annexQueryWrapper.eq("is_delete",CommonConstants.NO);
                relevancyAnnexMapper.delete(annexQueryWrapper);
                // 删除公告
                projectBidNoticeMapper.deleteById(noticePackageR.getNoticeId());
            }
        }
            // 更新标段状态
//            UpdateWrapper<ProjectBidSection> projectBidSectionUpdateWrapper = new UpdateWrapper<>();
//            projectBidSectionUpdateWrapper
//                    .eq("id",sectionId)
//                    .eq("is_delete",0);
//            projectBidSectionUpdateWrapper.set("status", PackageStatusEnum.EDIT_BY_NOTICE.getValue().toString());
//
//            if(projectBidDocService.judgeStatusByDoc(sectionId)){
//                sectionService.update(projectBidSectionUpdateWrapper);
//            }
        return f;
    }


}
