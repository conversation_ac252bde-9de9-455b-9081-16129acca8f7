package com.hzw.sunflower.dao;

import com.hzw.sunflower.entity.PendingItem;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 待处理事项表 Mapper 接口
 * </p>
 *
 * <AUTHOR> Gen
 * @since 2022-12-15
 */
public interface PendingItemMapper extends BaseMapper<PendingItem> {

    /**
     * 更正公告退回
     * @param businessId
     * @return
     */
    PendingItem getSupplementBulletin(@Param("businessId") Long businessId);

    /**
     * 澄清修改退回
     * @param businessId
     * @return
     */
    PendingItem getQuestionReply(@Param("businessId") Long businessId);

    /**
     * 异议回复退回
     * @param businessId
     * @return
     */
    PendingItem getProposeObjection(@Param("businessId") Long businessId);

    /**
     * 中标候选人公示退回
     * @param businessId
     * @return
     */
    PendingItem getWinBulletin(@Param("businessId") Long businessId);

    /**
     * 中标结果公示退回
     * @param businessId
     * @return
     */
    PendingItem getWinBulletinResult(@Param("businessId") Long businessId);

    /**
     * 申请中标通知书退回
     * @param businessId
     * @return
     */
    PendingItem getWinNotice(@Param("businessId") Long businessId);

    /**
     * 重新申请中标通知书退回
     * @param businessId
     * @return
     */
    PendingItem getWinNoticeNew(@Param("businessId") Long businessId);

    /**
     * 申请资格预审通知书退回
     * @param businessId
     * @return
     */
    PendingItem getBidWinNoticePre(@Param("businessId") Long businessId);

    /**
     * 重新申请资格预审通知书退回
     * @param businessId
     * @return
     */
    PendingItem getBidWinNoticePreNew(@Param("businessId") Long businessId);

    /**
     * 确认评标结果退回资格预审
     * @param businessId
     * @return
     */
    PendingItem getBidWinPeople(@Param("businessId") Long businessId);

    /**
     * 确认评审结果退回后审
     * @param businessId
     * @return
     */
    PendingItem getBidWinPeopleHou(Long businessId);

    /**
     * 异常公告退回
     * @param businessId
     * @return
     */
    PendingItem getExceptNotice(@Param("businessId") Long businessId);

    /**
     * 换专票申请退回
     * @param businessId
     * @return
     */
    PendingItem getPayAll(@Param("businessId") Long businessId);

    /**
     * 退标书费申请退回
     * @param businessId
     * @return
     */
    PendingItem getPayAllTenderFees(@Param("businessId") Long businessId);

    /**
     * 登记供应商退回
     * @param businessId
     * @return
     */
    PendingItem getSupplierRegister(@Param("businessId") Long businessId);

    /**
     * 补录付款凭证退回
     * @param businessId
     * @return
     */
    PendingItem getApplyPayFile(@Param("businessId") Long businessId);

    /**
     * 代理服务费修改退回
     * @param businessId
     * @return
     */
    PendingItem getAgencyFeeApply(@Param("businessId")  Long businessId);

    /**
     * 数据修改退回
     * @param businessId
     * @return
     */
    PendingItem getDataUpdate(@Param("businessId")  Long businessId);

    /**
     * 保证金退还退回
     * @param businessId
     * @return
     */
    PendingItem getBondApplyReturn(@Param("businessId") Long businessId);

    /**
     * 专家抽取退回
     * @param businessId
     * @return
     */
    PendingItem getExpertExtraction(@Param("businessId") Long businessId);

    /**
     * 专家抽取备案退回
     * @param businessId
     * @return
     */
    PendingItem getExpertExtractionBeian(@Param("businessId") Long businessId);
}
