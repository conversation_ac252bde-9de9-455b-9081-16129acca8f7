package com.hzw.expertfee.api.mybatis;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpStatus;
import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.hzw.expertfee.api.common.dto.LoginUser;
import com.hzw.expertfee.api.core.expcetion.ServiceException;
import com.hzw.expertfee.api.satoken.utils.LoginHelper;
import com.hzw.sunflower.common.BaseBean;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.reflection.MetaObject;

import java.util.Date;

/**
 * MP注入处理器
 *
 * <AUTHOR>
 * @date 2022/1/6
 */
@Slf4j
public class CreateAndUpdateMetaObjectHandler implements MetaObjectHandler {

    @Override
    public void insertFill(MetaObject metaObject) {
        try {
            if (ObjectUtil.isNotNull(metaObject) && metaObject.getOriginalObject() instanceof BaseBean) {
                BaseBean baseEntity = (BaseBean) metaObject.getOriginalObject();
                Date current = ObjectUtil.isNotNull(baseEntity.getCreatedTime())
                    ? baseEntity.getCreatedTime() : new Date();
                baseEntity.setCreatedTime(current);
                baseEntity.setUpdatedTime(current);
                Long userId = baseEntity.getCreatedUserId()!=null
                    ? baseEntity.getCreatedUserId() : getLoginUserId();
                // 当前已登录 且 创建人为空 则填充
                baseEntity.setCreatedUserId(userId);
                // 当前已登录 且 更新人为空 则填充
                baseEntity.setUpdatedUserId(userId);
            }
        } catch (Exception e) {
            throw new ServiceException("自动注入异常 => " + e.getMessage(), HttpStatus.HTTP_UNAUTHORIZED);
        }
    }

    @Override
    public void updateFill(MetaObject metaObject) {
        try {
            if (ObjectUtil.isNotNull(metaObject) && metaObject.getOriginalObject() instanceof BaseBean) {
                BaseBean baseBean = (BaseBean) metaObject.getOriginalObject();
                Date current = new Date();
                // 更新时间填充(不管为不为空)
                baseBean.setUpdatedTime(current);
                Long userId = getLoginUserId();
                // 当前已登录 更新人填充(不管为不为空)
                if (userId!=null) {
                    baseBean.setUpdatedUserId(userId);
                }
            }
        } catch (Exception e) {
            throw new ServiceException("自动注入异常 => " + e.getMessage(), HttpStatus.HTTP_UNAUTHORIZED);
        }
    }

    /**
     * 获取登录用户名
     */
    private String getLoginUsername() {
        LoginUser loginUser;
        try {
            loginUser = LoginHelper.getLoginUser();
        } catch (Exception e) {
            log.warn("自动注入警告 => 用户未登录");
            return null;
        }
        return loginUser.getUsername();
    }
    /**
     * 获取登录用户名
     */
    private Long getLoginUserId() {
        LoginUser loginUser;
        try {
            loginUser = LoginHelper.getLoginUser();
        } catch (Exception e) {
            log.warn("自动注入警告 => 用户未登录");
            return null;
        }
        return loginUser.getUserId();
    }
}
