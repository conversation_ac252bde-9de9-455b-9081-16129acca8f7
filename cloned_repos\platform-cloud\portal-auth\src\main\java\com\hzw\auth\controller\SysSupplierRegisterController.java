package com.hzw.auth.controller;

import cn.hutool.core.util.ObjectUtil;
import com.hzw.auth.controller.response.RegisterPersonalInfoVO;
import com.hzw.auth.properties.SmsValidateProperties;
import com.hzw.auth.service.SysLoginService;
import com.hzw.common.core.domain.R;
import com.hzw.common.core.web.controller.BaseController;
import com.hzw.common.idempotent.annotation.RepeatSubmit;
import com.hzw.common.log.annotation.Log;
import com.hzw.common.log.enums.BusinessType;
import com.hzw.resource.api.RemoteFileService;
import com.hzw.resource.api.RemoteSmsService;
import com.hzw.system.api.RemoteUserService;
import com.hzw.system.api.domain.SysUser;
import com.hzw.system.api.model.RegisterUserReq;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Validated
@RequiredArgsConstructor
@RestController
@Slf4j
@RequestMapping("/supplier")
public class SysSupplierRegisterController extends BaseController {

    @DubboReference
    private final RemoteSmsService remoteSmsService;

    @DubboReference
    private final RemoteUserService remoteUserService;

    @DubboReference
    private final RemoteFileService remoteFileService;

    private final SysLoginService sysLoginService;

    @Autowired
    private SmsValidateProperties smsValidateProperties;

    private static HashMap<String, String> fileSuffixContentTypeMap = new HashMap<>();


    static {
        fileSuffixContentTypeMap.put("jpg", "image/jpeg");
        fileSuffixContentTypeMap.put("png", "image/png");
        fileSuffixContentTypeMap.put("jpeg", "image/jpeg");
        fileSuffixContentTypeMap.put("pdf", "application/pdf");
    }

    /**
     * 用户注册接口
     *
     * @param userReq
     * @return
     */
    @Log(title = "供应商注册",  businessType = BusinessType.INSERT)
    @RepeatSubmit
    @PostMapping(value = "/supplierRegister")
    public R<RegisterPersonalInfoVO> register(@RequestBody RegisterUserReq userReq) throws Exception {
        String phonenumber = userReq.getUserPhone();
        SysUser phoneUser = remoteUserService.getSupplierByPhonenumber(phonenumber);
        if(null != phoneUser){
            log.error("供应商注册：用户已经存在 => {}", phonenumber);
            return R.fail("手机号已存在！");
        }
        try{
           // Boolean captchaResult = remoteSmsService.checkSmsCode(userReq.getUserPhone(), userReq.getPhoneCaptcha());
            Boolean captchaResult = sysLoginService.checkSmsCode(userReq.getUserPhone(), userReq.getPhoneCaptcha());
        if (!captchaResult) {
            return R.fail("请输入正确的验证码");
        }
            RegisterPersonalInfoVO vo = new RegisterPersonalInfoVO();
            SysUser sysUser = remoteUserService.getSupplierInfoByPhonenumber(userReq.getUserPhone());
            Boolean registerRs = false;
            if (null == sysUser) {
                registerRs = remoteUserService.doSupplierRegister(userReq);
                if (registerRs){
                    sysUser = remoteUserService.getSupplierInfoByPhonenumber(userReq.getUserPhone());
                    vo.setApproveStatus(sysUser.getApproveStatus());
                    String tempToken = sysLoginService.buildTempToken(sysUser.getPhonenumber());
                    vo.setTempToken(tempToken);
                }else{
                    return R.fail("注册失败");
                }
            }else{
                vo.setApproveStatus(sysUser.getApproveStatus());
                if (sysUser.getApproveStatus() == 3 || sysUser.getApproveStatus() == 1 || sysUser.getApproveStatus() == 0 || sysUser.getApproveStatus() == 2){
                    String tempToken = sysLoginService.buildTempToken(sysUser.getPhonenumber());
                    vo.setTempToken(tempToken);
                }
            }
            return R.ok(vo);
        }catch (Exception e){
            log.error(e.getMessage(), e);
            return R.fail(e.getMessage());
        }
    }

    /**
     * 用户修改密码接口
     *
     * @param userReq
     * @return
     */
    @RepeatSubmit
    @PostMapping(value = "/changePassword")
    public R<Boolean> changePassword(@RequestBody RegisterUserReq userReq) throws Exception {
        String phonenumber = userReq.getUserPhone();
        SysUser phoneUser = remoteUserService.getSupplierByPhonenumber(phonenumber);
        if(null == phoneUser){
            log.error("手机号未注册或注册未审批完成 => {}", phonenumber);
            return R.fail("手机号未注册或注册未审批完成");
        } else {}
        if (phoneUser.getPassword() == null || phoneUser.equals("")) {
            return R.fail("该账号尚未设置密码，请使用手机验证码登陆后设置密码");
        } else {}
        // 从忘记密码进行密码修改，需要进行手机验证码校验
        Boolean captchaResult = sysLoginService.checkChangePwdSmsCode(userReq.getUserPhone(), userReq.getPhoneCaptcha());
        if (!captchaResult) {
            return R.fail("请输入正确的验证码");
        }
        Boolean changePwdRs = remoteUserService.doChangePwdByPhonenumber(userReq);
        return R.ok(changePwdRs);

    }

    /**
     * 用户修改密码接口
     *
     * @param userReq
     * @return
     */
    @RepeatSubmit
    @PostMapping(value = "/changePasswordByUser")
    public R<Boolean> changePasswordByUser(@RequestBody RegisterUserReq userReq) throws Exception {
        String phonenumber = userReq.getUserPhone();
        SysUser phoneUser = remoteUserService.getSupplierByPhonenumber(phonenumber);
        if(null == phoneUser){
            log.error("手机号未注册或注册未审批完成 => {}", phonenumber);
            return R.fail("手机号未注册或注册未审批完成");
        } else {}
        if (phoneUser.getPassword() == null || phoneUser.equals("")) {
            return R.fail("该账号尚未设置密码，请使用手机验证码登陆后设置密码");
        } else {}
        Boolean changePwdRs = remoteUserService.doChangePwdByPhonenumber(userReq);
        return R.ok(changePwdRs);

    }

    @Log(title = "上传营业执照", businessType = BusinessType.INSERT)
    @PostMapping(value = "/uploadLicenseAndAuthFile", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public R<Map<String, String>> uploadLicenseAndAuthFile(@RequestPart("file") MultipartFile file) {
        if (ObjectUtil.isNull(file)) {
            return R.fail("上传文件不能为空");
        }
        if (!validFileSuffix(file) || !validFileContentType(file)){
            return R.fail("只支持上传jpg/png/jpeg/pdf格式的文件，请修改后重新上传");
        }
        if (!validFileSuffixIsNormalContentType(file)) {
            return R.fail("疑似非法文件，请重新上传");
        }
        if (!validFileSize(file)){
            return R.fail("文件过大，请修改后重新上传");
        }

        R<Map<String, String>> result = null;
        try {
            result = remoteFileService.uploadFile(file.getOriginalFilename(), file.getBytes(), file.getContentType());
        } catch (IOException e) {
            log.error("文件上传失败");
            throw new RuntimeException(e);
        }
        return result;
    }

    private boolean validFileSuffix(MultipartFile file) {
        List<String> writeList = new ArrayList<>();
        writeList.add("jpg");
        writeList.add("png");
        writeList.add("jpeg");
        writeList.add("pdf");
        String originalFileName = file.getOriginalFilename();
        int lastDotIndex = originalFileName.lastIndexOf(".");
        String suffix = originalFileName.substring(lastDotIndex + 1);
        if (!writeList.contains(suffix.toLowerCase())){
            return false;
        }
        return true;
    }

    private boolean validFileContentType(MultipartFile file) {
        List<String> writeList = new ArrayList<>();
        writeList.add("image/jpeg");
        writeList.add("image/png");
        writeList.add("application/pdf");
        String contentType = file.getContentType();
        if (!writeList.contains(contentType.trim().toLowerCase())){
            return false;
        }
        return true;
    }

    private boolean validFileSize(MultipartFile file) {
        long fileSize = file.getSize();
        long fileSizeLimit = 1024 * 1024;
        if (fileSize > fileSizeLimit){
            return false;
        }
        return true;
    }

    private boolean validFileSuffixIsNormalContentType(MultipartFile file){
        String originalFileName = file.getOriginalFilename();
        int lastDotIndex = originalFileName.lastIndexOf(".");
        String suffix = originalFileName.substring(lastDotIndex + 1);
        String contentType = file.getContentType();
        String allowContentType = fileSuffixContentTypeMap.get(suffix);
        if (!contentType.equals(allowContentType)) {
            return false;
        }
        return true;
    }

}
