package com.hzw.expertfee.api.util;

import cn.hutool.core.builder.EqualsBuilder;
import cn.hutool.core.builder.HashCodeBuilder;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;


/**
 * 重写Bean的equals、hashCode、toString方法，用于继承
 *
 * <AUTHOR>
 * @version 1.0.0 2021-03-31
 */
public abstract class AbstractOverrideBeanMethods implements Serializable {
    /**
     * 版本号
     */
    private static final long serialVersionUID = -5994804308648842978L;

    @Override
    public boolean equals(Object obj) {
        return EqualsBuilder.reflectionEquals(obj, this);
    }

    @Override
    public int hashCode() {
        return HashCodeBuilder.reflectionHashCode(this);
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.JSON_STYLE);
    }
}
