package com.hzw.sunflower.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.hzw.sunflower.common.BaseBean;
import com.hzw.sunflower.controller.request.UpdateNoticeREQ;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * Created by zgq on 2021/07/01
 */
@ApiModel("包段 公告映射")
@TableName("t_section_notice")
@Data
@Accessors(chain = true)
public class NoticePackageR extends BaseBean {

    @ApiModelProperty(value = "主键", position = 1)
    private Long id;

    @ApiModelProperty(value = "公告ID", position = 2)
    private Long noticeId;

    @ApiModelProperty(value = "包段ID", position = 3)
    private Long sectionId;

    @ApiModelProperty(value = "公告类型：1 采购公告；2 补充公告；3 采购结果公示；4 中标候选人公示", position = 4)
    private Integer state;

}
