<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hzw.ssm.expert.dao.ExpertRecommendMapper">
	<!-- 新增推荐计划 -->
	<insert id="addPlanById" parameterType="ExpertRecommendEntity">
		insert into T_RECOMMEND_PLAN
		(
			ID,
			PLAN_NAME,
			EXPERT_NUMBER,
			YEAR,
			CREATE_ID,
			MODIFY_ID,
			create_time,
			modify_time,
			REMARK,
			DELETE_FLAG
		)
		values(
			#{id},
			#{planName,jdbcType=VARCHAR},
			#{recommendNumber,jdbcType=NUMERIC},
			#{year,jdbcType=VARCHAR},
			#{create_id,jdbcType=VARCHAR},
			#{modify_id,jdbcType=VARCHAR},
			#{create_time,jdbcType = TIMESTAMP},
			#{modify_time,jdbcType = TIMESTAMP},
			#{remark,jdbcType=VARCHAR},
			#{delete_flag,jdbcType=VARCHAR}
		)
	</insert>
	<!-- 新增推荐人记录 -->
	<insert id="addRecommendById" parameterType="ExpertRecommendEntity">
		insert into T_RECOMMEND
		(
			ID,
			PLAN_ID,
			RECOMMENDER_ID,
			PLAN_NUMBER,
			MODIFY_ID,
			CREATE_ID,
			CREATE_TIME,
			MODIFY_TIME,
			REMARK,
			DELETE_FLAG
		)
		values(
			#{id},
			#{planId,jdbcType=VARCHAR},
			#{recommenderId,jdbcType=VARCHAR},
			#{recommendNumber,jdbcType=NUMERIC},
			#{modify_id,jdbcType=VARCHAR},
			#{create_id,jdbcType=VARCHAR},
			#{create_time,jdbcType = TIMESTAMP},
			#{modify_time,jdbcType = TIMESTAMP},
			#{remark,jdbcType=VARCHAR},
			#{delete_flag,jdbcType=VARCHAR}
		)
	</insert>
	<!-- 修改计划 -->
	<update id="updatePlanById" parameterType="ExpertRecommendEntity">
		  update
				T_RECOMMEND_PLAN
		  set
				EXPERT_NUMBER = #{recommendNumber},
				MODIFY_ID = #{modify_id,jdbcType=VARCHAR},
				MODIFY_TIME = #{modify_time,jdbcType = TIMESTAMP}
		  where
				id = #{id}
	</update>
	<!-- 修改推荐人记录表 -->
	<update id="updateRecommendById" parameterType="ExpertRecommendEntity">
		  update
				T_RECOMMEND
		  set
		  		MODIFY_ID = #{modify_id,jdbcType=VARCHAR},
				MODIFY_TIME = #{modify_time,jdbcType = TIMESTAMP},
				PLAN_NUMBER = #{recommendNumber}
		  where
				id = #{id}
	</update>
	<!-- 删除记录表 -->
	<update id="removeRecommend" parameterType="ExpertRecommendEntity">
		  update
				T_RECOMMEND
		  set
		  		MODIFY_ID = #{modify_id,jdbcType=VARCHAR},
				MODIFY_TIME = #{modify_time,jdbcType = TIMESTAMP},
				delete_flag = #{delete_flag}
		  where
				id = #{id}
	</update>
	<!-- 分页查询计划 -->
	<select id="queryPagePlanList" resultType="ExpertRecommendEntity">
	  select
	  		t.id,
	  		t.PLAN_NAME,
	  		t.EXPERT_NUMBER recommendNumber,
	  		t.YEAR,
	  		t.CREATE_TIME,
	  		t.CREATE_ID,
	  		t.MODIFY_ID,
	  		t.MODIFY_TIME,
	  		t.DELETE_FLAG,
	  		t.REMARK,
	  		u.user_name createName,
	  		u1.user_name modifierName
      from
      		T_RECOMMEND_PLAN t
      		left join ts_user u
      		on t.CREATE_ID=u.user_id
      		left join ts_user u1
      		on t.MODIFY_ID=u1.user_id
      where
      		t.DELETE_FLAG=0
	      <if test="year !=null and year !=''">
	      		and t.YEAR = #{year}
	      </if>
	      <if test="id !=null and id!=''">
	      		and t.id=#{id}
	      </if>
	 order by to_number(year) desc
		 	 
	</select>
	<!-- 查询记录-->
	<select id="queryRecommenderList" resultType="ExpertRecommendEntity">
	  select
	  		t.ID,
	  		t.PLAN_ID planId,
	  		t.RECOMMENDER_ID recommenderId,
	  		t.PLAN_NUMBER recommendNumber,
	  		t.CREATE_ID,
	  		t.MODIFY_ID,
	  		t.MODIFY_TIME,
	  		t.CREATE_TIME,
	  		t.DELETE_FLAG,
	  		t.REMARK,
	  		u.USER_NAME recommenderName,
	  		u.DEPARTMENT department
      from
      		T_RECOMMEND t
      left join 
      		TS_USER u
      on 
      t.RECOMMENDER_ID=u.user_id
      where
      		t.DELETE_FLAG=0

		 	<if test="recommenderName !=null and recommenderName !=''">
		 		AND INSTR(u.USER_NAME,#{recommenderName})>0
	      	</if>
	      	<if test="id !=null and id!=''">
	      		and t.id=#{id}
	      	</if>
	      	<if test="planId !=null and planId!=''">
	      		and t.PLAN_ID=#{planId}
	      	</if>
	      	<if test="recommenderId !=null and recommenderId !=''">
	      		and t.RECOMMENDER_ID=#{recommenderId}
	      	</if>
	</select>
	<!-- 查询业务员-->
	<select id="querySalesMan" parameterType="UserEntity" resultType="UserEntity">
	  select t.USER_NAME,t.ROLE_ID,t.DEPARTMENT,t.USER_ID from TS_USER t
	  
	  where t.DELETE_FLAG=0
	  and t.ENABLED_FLAG=1
	  and t.role_id=#{role_id}
	  <if test="user_name !=null and user_name !=''">
		 		AND INSTR(t.USER_NAME,#{user_name})>0
	  </if>
	  
	</select>
	<select id="queryPageExpertList" parameterType="ExpertRecommendEntity" resultType="ExpertRecommendEntity">
		SELECT 
		u.USER_NAME recommenderName,
		T.USER_NAME expertName,
		T.EXPERT_NUM expertNum,
		T.MOBILEPHONE mobilePhone,
		T.STATUS,
		T.referrer_qrcode qrCode,
		T.CREATE_TIME
		FROM t_expert_info t LEFT JOIN ts_user u
		ON t.REFERRER_QRCODE=u.qr_code
		WHERE 
		T.DELETE_FLAG=0
		and t.referrer_qrcode is not NULL
		
		<if test="statusList!=null and statusList.size>0 ">
			and t.status in
	  		<foreach collection="statusList" item="item" index="index" open="(" close=")" separator=",">
			#{statusList[${index}]}
        	</foreach>
		</if>
		<if test="expertName !=null and expertName !=''">
		 		AND INSTR(t.USER_NAME,#{expertName})>0
	  	</if>
		<if test="mobilePhone !=null and mobilePhone !=''">
				and INSTR(t.MOBILEPHONE,#{mobilePhone})>0
	  	</if>
		<if test="recommenderName !=null and recommenderName !=''">
				and INSTR(u.USER_NAME,#{recommenderName})>0
	  	</if>
		<if test="userId !=null and userId !=''">
				and u.USER_ID=#{userId}
	  	</if>
	  	<if test="startTime!=null">
			and t.CREATE_TIME>=#{startTime}
		</if>
		<if test="endTime!=null">
			and t.CREATE_TIME<![CDATA[<=]]>#{endTime}
		</if>
		<if test="year!=null and year !=''">
			and EXTRACT(YEAR FROM t.CREATE_TIME) = #{year}
		</if>
		
	  	
	  	
	</select>
	<select id="queryPageExpertStaticsList" parameterType="ExpertRecommendEntity" resultType="ExpertRecommendEntity">
		SELECT P.YEAR,
	       U.USER_NAME recommenderName,
	       U.MOBILE mobilePhone,
	       u.DEPARTMENT department,
	       D.PLAN_NUMBER recommendNumber,
	       U.USER_ID userId,
	       (SELECT COUNT(USER_ID)
	          FROM T_EXPERT_INFO I
	         WHERE I.REFERRER_QRCODE = U.QR_CODE
	           AND I.REFERRER_QRCODE IS NOT NULL
	           AND EXTRACT(YEAR FROM I.CREATE_TIME) = P.YEAR
	           AND I.STATUS IN('3','8')) isNumber
	           
	  	FROM T_RECOMMEND D
	  	LEFT JOIN TS_USER U
	    ON D.RECOMMENDER_ID = U.USER_ID
	  	LEFT JOIN T_RECOMMEND_PLAN P
	    ON D.PLAN_ID = P.ID
	 	WHERE D.DELETE_FLAG = 0
	 	<if test="year !=null and year !=''">
		 		AND INSTR(p.year,#{year})>0
	  	</if>
	 	<if test="recommenderName !=null and recommenderName !=''">
		 		AND INSTR(U.USER_NAME,#{recommenderName})>0
	  	</if>
	 	<if test="mobilePhone !=null and mobilePhone !=''">
		 		AND INSTR(U.MOBILE,#{mobilePhone})>0
	  	</if>
	</select>
	 
</mapper>
