<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzw.sunflower.dao.BondApplyRefundMapper">

    <select id="selectCompanyName" resultType="java.lang.String">
        SELECT
            c.company_name
        FROM
            t_company c
                LEFT JOIN t_user_identity ui ON c.id = ui.company_id AND ui.identity = 3
        WHERE
            c.is_delete = 0
            AND ui.is_delete = 0
            AND ui.user_id = #{userId}
            LIMIT 1
    </select>

    <select id="bondApplyWaitDisposeList" resultType="com.hzw.sunflower.entity.BondWater">
        SELECT
            w.*
        FROM
            t_bond_water w
            LEFT JOIN t_bond_apply_refund r ON w.id = r.water_id
        WHERE
            w.is_delete = 0
            AND r.is_delete = 0
            AND w.company_name = #{companyName}
            <if test="relationWaterIds != null and relationWaterIds.size > 0 ">
                AND w.id NOT IN
                <foreach collection="relationWaterIds" item="relationWaterIds" open="(" close=")" separator=",">
                    #{relationWaterIds}
                </foreach>
            </if>
            <if test="condition.status != null and condition.status != ''">
                <if test="condition.status == 2">
                    AND r.`status` IN (1,2,3)
                </if>
                <if test="condition.status == 5">
                    AND r.`status` IN (4,6)
                </if>
            </if>
        <if test="condition.amount != null and condition.amount != ''">
            AND w.amount = #{condition.amount}
        </if>
        <if test="condition.date != null">
            AND w.date = #{condition.date,jdbcType=DATE}
        </if>
        ORDER BY
            w.date ASC,
            w.time ASC
    </select>
    <select id="bondApplyRefundList" resultType="com.hzw.sunflower.entity.BondWater">
        SELECT
        w.*
        FROM
        t_bond_water w
        LEFT JOIN t_bond_apply_refund ar ON w.id = ar.water_id
        LEFT JOIN t_bond_refund r ON ar.id = r.apply_refund_id
        WHERE
        w.is_delete = 0
        AND ar.is_delete = 0
        AND r.is_delete = 0
        AND w.company_name = #{companyName}
        <if test="relationWaterIds != null and relationWaterIds.size > 0 ">
            AND w.id NOT IN
            <foreach collection="relationWaterIds" item="relationWaterIds" open="(" close=")" separator=",">
                #{relationWaterIds}
            </foreach>
        </if>
        <if test="condition.status != null and condition.status != ''">
            AND ar.`status` = 5
            <if test="condition.status == 3">
                AND r.`status` IN (1,2,5)
            </if>
            <if test="condition.status == 4">
                AND r.`status` = 3
            </if>
        </if>
        <if test="condition.amount != null and condition.amount != ''">
            AND w.amount = #{condition.amount}
        </if>
        <if test="condition.date != null">
            AND w.date = #{condition.date,jdbcType=DATE}
        </if>
        ORDER BY
        w.date ASC,
        w.time ASC
    </select>
    <select id="getMyCompanyProject" resultType="com.hzw.sunflower.controller.response.MyCompanyProjectVo">
        SELECT
            DISTINCT
            p.id AS projectId,
            p.purchase_number AS projectNum,
            p.purchase_name AS projectName
        FROM
            t_apply_info a
            LEFT JOIN t_user_identity i ON a.company_id = i.company_id
            LEFT JOIN t_project p ON a.project_id = p.id
        WHERE
            a.is_delete = 0
            AND i.is_delete = 0
            AND i.identity = 3
            AND i.user_id = #{userId}
            AND p.is_delete = 0
            AND p.re_tender = 0
            <if test="req != null and req.projectNum != null and req.projectNum != ''">
                AND p.purchase_number like concat('%',#{req.projectNum},'%')
            </if>
    </select>
    <select id="getMyCompanySection" resultType="com.hzw.sunflower.controller.response.MyCompanyProjectVo">
        SELECT DISTINCT
            s.id AS sectionId,
            s.package_number AS packageNumber,
            s.package_name AS sectionName
        FROM
            t_apply_info a
            LEFT JOIN t_user_identity i ON a.company_id = i.company_id
            LEFT JOIN t_project_bid_section s ON a.sub_id = s.id
        WHERE
            a.is_delete = 0
            AND i.is_delete = 0
            AND i.identity = 3
            AND i.user_id = #{userId}
            AND s.is_delete = 0
            <if test="req != null and req.projectId != null and req.projectId != ''">
                AND s.project_id= #{req.projectId}
            </if>
    </select>
    <select id="getReturnList" resultType="com.hzw.sunflower.entity.BondApplyRefundConfirm">
        select * from t_bond_apply_refund_confirm where department_dispose_status = 6 and apply_refund_id = #{applyRefundId} order by created_time desc limit 1
    </select>
    <select id="selectBondApplyRefundPage"
            resultType="com.hzw.sunflower.controller.response.BondApplyRefundVo">
        SELECT * FROM (
        SELECT
            w.company_name AS companyName,
            u.user_name AS userName,
            ui.user_phone AS userPhone,
            ar.id,
            ar.water_id,
            ar.refund_money,
            ( CASE WHEN ((ar.`status` = 5 OR br.`status` IS NOT NULL) AND br.`status` = 1) THEN 11
                   WHEN ((ar.`status` = 5 OR br.`status` IS NOT NULL) AND br.`status` = 2) THEN 12
                   WHEN ((ar.`status` = 5 OR br.`status` IS NOT NULL) AND br.`status` = 3) THEN 13
                   WHEN ((ar.`status` = 5 OR br.`status` IS NOT NULL) AND br.`status` = 4) THEN 14
                   WHEN ((ar.`status` = 5 OR br.`status` IS NOT NULL) AND br.`status` = 5) THEN 15
                   ELSE ar.`status` END ) AS `status`,
            ar.handle_type,
            ar.apply_time,
            ar.file_ids,
            ar.refund_files,
            br.`status` AS payStatus
        FROM
            t_bond_apply_refund ar
            LEFT JOIN t_bond_water w ON ar.water_id = w.id
            LEFT JOIN t_user_identity ui ON ar.created_user_id = ui.user_id
            LEFT JOIN t_user u ON ar.created_user_id = u.id
            LEFT JOIN t_bond_refund br ON ar.id = br.apply_refund_id AND br.is_delete = 0
        WHERE
            ar.is_delete = 0
            AND w.is_delete = 0
            AND ar.`status` IN (1,2,3,4,5)
            <if test="condition != null and condition.keyWords != null and condition.keyWords != ''">
                AND w.company_name like concat('%',#{condition.keyWords},'%')
            </if>
            AND ui.is_delete = 0
            AND u.is_delete = 0
            ORDER BY ar.apply_time ASC
        ) t
        WHERE 1=1
        <if test=" condition.status != null and condition.status.size > 0 ">
            AND t.`status` IN
            <foreach item="item" collection="condition.status" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
    </select>
    <select id="getVendor" resultType="com.hzw.sunflower.controller.response.VendorVo">
        SELECT
            c.company_name AS companyName,
            c.organization_num AS organizationNum,
            c.legal_representative AS legalRepresentative,
            c.legal_representative_identity AS legalRepresentativeIdentity,
            u.user_name AS userName,
            ui.user_phone AS userPhone,
            u.identity_number AS userIdentity,
            u.email AS userEmail,
            ui.legal_person_authorization AS legalPersonAuthorization
        FROM
            t_bond_apply_refund ar
            LEFT JOIN t_user_identity ui ON ar.created_user_id = ui.user_id
            LEFT JOIN t_user u ON ui.user_id = u.id
            LEFT JOIN t_company c ON ui.company_id = c.id
        WHERE
            ar.is_delete = 0
            AND ar.id = #{applyId}
            AND ui.is_delete = 0
            AND u.is_delete = 0
            AND c.is_delete = 0
    </select>
    <select id="getChooseProjects" resultType="com.hzw.sunflower.controller.response.MyCompanyProjectVo">
        SELECT
            p.id AS projectId,
            p.purchase_number AS projectNum,
            p.purchase_name AS projectName,
            bs.id AS sectionId,
            bs.package_number AS packageNumber,
            bs.package_name AS sectionName
        FROM
            t_bond_apply_refund_project rp
            LEFT JOIN t_project p ON rp.project_id = p.id
            LEFT JOIN t_project_bid_section bs ON rp.section_id = bs.id
        WHERE
            rp.is_delete = 0
            AND rp.apply_refund_id = #{applyId}
            AND p.is_delete = 0
            AND bs.is_delete = 0
    </select>
    <select id="getVendorProjects" resultType="com.hzw.sunflower.controller.response.MyCompanyProjectVo">
        SELECT DISTINCT
            p.id AS projectId,
            p.purchase_number AS projectNum,
            p.purchase_name AS projectName,
            p.base_project_id,
            p.re_tender,
            p.package_segment_status,
            p.bid_type_name bidType,
            p.project_status_code packagesStatus,
            bs.id AS sectionId,
            bs.package_number AS packageNumber,
            bs.package_name AS sectionName,
            bs.bid_round,
            u.user_name AS projectChargeName,
            d.department_name AS departmentName,
            d.id AS departmentId,
            a.contact_person AS vendorName,
            a.user_mobile AS vendorPhone,
            IFNULL(t1.rightCode,2) rightCode
        FROM
            t_apply_info a
            LEFT JOIN t_user_identity i ON a.company_id = i.company_id
            LEFT JOIN t_project p ON a.project_id = p.id
            LEFT JOIN t_project_entrust_user eu ON eu.project_id = p.id
            LEFT JOIN t_user u ON u.id = eu.user_id
            LEFT JOIN r_user_department ud ON ud.user_id = u.id
            LEFT JOIN t_department d ON eu.department_id = d.id
            LEFT JOIN t_project_bid_section bs ON bs.id = a.sub_id
            LEFT JOIN (SELECT distinct  p.id,1 rightCode FROM t_project p LEFT JOIN t_project_entrust_user eu ON eu.project_id = p.id
                LEFT JOIN t_user u ON u.id = eu.user_id
               LEFT JOIN r_user_department ud ON ud.user_id = u.id
                WHERE
                p.is_delete = 0
                AND eu.is_delete = 0
                AND u.is_delete = 0
                AND ud.is_delete = 0
                ${dataScope}
            ) t1 ON t1.id = p.id
        WHERE
            a.is_delete = 0
            AND i.is_delete = 0
            AND i.identity = 3
            AND i.user_id = #{userId}
            AND p.is_delete = 0
            AND p.re_tender = 0
            AND eu.is_delete = 0
            AND eu.type = 1
            AND u.is_delete = 0
            AND ud.is_delete = 0
            AND d.is_delete = 0
            AND bs.is_delete = 0
    </select>
    <select id="getDepartments" resultType="com.hzw.sunflower.controller.response.DepartmentUserVo">
        SELECT
            d.id AS departmentId,
            d.department_name AS departmentName,
            u.id AS userId,
            u.user_name AS userName
        FROM
            t_user u
            LEFT JOIN r_depart_role dr ON dr.user_id = u.id
            LEFT JOIN t_department d ON dr.depart_id = d.id
            LEFT JOIN t_role r ON dr.role_id = r.id
        WHERE
            u.is_delete = 0
            AND dr.is_delete = 0
            AND d.is_delete = 0
            and r.is_delete = 0
            <if test="departmentIds != null and departmentIds.size > 0 ">
                AND d.id IN
                <foreach collection="departmentIds" item="departmentIds" open="(" close=")" separator=",">
                    #{departmentIds}
                </foreach>
            </if>
            <if test="roleCodes != null and roleCodes.size > 0 ">
                and r.role_code in
                <foreach collection="roleCodes" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            ORDER BY d.id
    </select>
    <select id="getDepartmentResult" resultType="com.hzw.sunflower.controller.response.DepartmentUserVo">
        SELECT
            d.id AS departmentId,
            d.department_name AS departmentName,
            u.id AS userId,
            u.user_name AS userName,
            c.department_dispose_status AS departmentDisposeStatus
        FROM
            t_bond_apply_refund_confirm c
            LEFT JOIN t_department d ON d.id = c.department_id
            LEFT JOIN t_user u ON u.id = c.user_id
        WHERE
            c.is_delete = 0
            AND c.apply_refund_id = #{applyId}
            AND d.is_delete = 0
            AND u.is_delete = 0
    </select>
    <select id="getBondRefundReq" resultType="com.hzw.sunflower.controller.request.BondRefundFeq">
        SELECT
            w.id AS waterId,
            ar.id AS applyRefundId,
            c.id AS companyId,
            w.company_name AS refundCompanyName,
            w.company_account AS refundNumber,
            w.company_bank_deposit AS refundOpenBank,
            w.amount AS bondMoney,
            ar.refund_money AS refundMoney,
            ar.file_ids AS fileIds,
            ar.apply_time AS submitTime,
            ar.refund_files as refundFiles
        FROM
            t_bond_apply_refund ar
            LEFT JOIN t_bond_water w ON ar.water_id = w.id
            LEFT JOIN t_company c ON REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(w.company_name,char(32),''),char(40),''),char(41),''),char(44),''),char(46),''),'（',''),'）',''),'，','')
                                        =
                                     REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(c.company_name,char(32),''),char(40),''),char(41),''),char(44),''),char(46),''),'（',''),'）',''),'，','')
        WHERE
            ar.id = #{applyRefundId}
            AND ar.is_delete = 0
            AND w.is_delete = 0
            AND c.is_delete = 0
            LIMIT 1
    </select>
    <select id="getBondAbnormalRelationList"
            resultType="com.hzw.sunflower.controller.response.AbnormalApplyRelationVo">
        SELECT
            ar.id AS applyRelationId,
            p.id as project_id,
            p.purchase_number AS projectNum,
            p.purchase_name AS projectName,
            bs.package_number AS packageNum,
            bs.package_name AS packageName,
            u.user_name AS userName,
            GROUP_CONCAT(DISTINCT d.department_name) AS departmentName,
            ar.created_time AS applyTime
        FROM
            t_bond_apply_relation ar
            LEFT JOIN t_project p ON ar.project_id = p.id
            LEFT JOIN t_project_bid_section bs ON ar.section_id = bs.id
            LEFT JOIN t_user u ON ar.created_user_id = u.id
        LEFT JOIN r_user_department  ud ON u.id = ud.user_id
            LEFT JOIN t_department d ON ud.department_id = d.id
        WHERE
            ar.is_delete = 0
            AND ar.type = 2
            AND ar.`status` = 1
            AND p.is_delete = 0
            <if test="condition != null and condition.keywords != null and condition.keywords != ''">
                AND (p.purchase_number like concat('%',#{condition.keywords},'%')
                OR p.purchase_name like concat('%',#{condition.keywords},'%'))
            </if>
            AND bs.is_delete = 0
            AND u.is_delete = 0
            AND ud.is_delete = 0
            AND d.is_delete = 0
            group by
            ar.id,
            p.id,
            p.purchase_number,
            p.purchase_name,
            bs.package_number,
            bs.package_name,
            u.user_name,
            ar.created_time
            ORDER BY ar.created_time DESC
    </select>
    <select id="bondApplyRelationList"
            resultType="com.hzw.sunflower.controller.response.AbnormalApplyRelationVo">
        SELECT
            ar.id AS applyRelationId,
            p.purchase_number AS projectNum,
            p.purchase_name AS projectName,
            bs.package_number AS packageNum,
            bs.package_name AS packageName,
            bs.submit_end_time AS submitEndTime,
            ar.content AS content,
            ar.created_time AS applyTime,
            ar.payment_voucher_ids,
            IFNULL(c.company_name, oc.company_name) AS companyName
        FROM
            t_bond_apply_relation ar
            LEFT JOIN t_project p ON ar.project_id = p.id
            LEFT JOIN t_project_bid_section bs ON ar.section_id = bs.id
            LEFT JOIN t_company c ON ar.company_id = c.id AND c.is_delete = 0
            LEFT JOIN t_offline_company oc ON ar.company_id = oc.id AND oc.is_delete = 0
        WHERE
            ar.is_delete = 0
            AND ar.type = 1
            AND ar.`status` = 1
            AND p.is_delete = 0
            <if test="condition != null and condition.keywords != null and condition.keywords != ''">
                AND (p.purchase_number like concat('%',#{condition.keywords},'%')
                OR p.purchase_name like concat('%',#{condition.keywords},'%'))
            </if>
            AND bs.is_delete = 0
            ORDER BY ar.created_time ASC
    </select>
    <select id="getBondWaterNotRelationPageNew"
            resultType="com.hzw.sunflower.controller.response.BondNotRelationVo">
        SELECT * FROM (SELECT DISTINCT
                           w.*,
                           bar.remark as applyRemark,
                           ( CASE WHEN (bar.id IS NULL AND r.id IS NULL) THEN 1
                                  WHEN (bar.id IS NOT NULL AND bar.`status` IN (1,2,3,4)) THEN 2
                                  WHEN ((bar.id IS NULL OR bar.`status` = 5) AND r.`status` IN (1,4,5)) THEN 3
                                  WHEN ((bar.id IS NULL OR bar.`status` = 5) AND r.`status` = 3) THEN 4
                                  WHEN (bar.id IS NOT NULL AND bar.`status` = 6) THEN 5
                                  ELSE 0 END ) AS `status`
                       FROM
                           t_bond_water w
                               LEFT JOIN t_bond_relation br ON br.water_id = w.id and br.is_delete = 0
                               LEFT JOIN t_bond_apply_refund bar on bar.water_id = w.id and bar.is_delete = 0
                               LEFT JOIN t_bond_refund r on r.water_id = w.id and r.is_delete = 0
                               LEFT JOIN(
                               SELECT
                                   *
                               FROM
                                   t_bond_water_label
                               WHERE
                                   id IN ( SELECT MAX( id ) FROM t_bond_water_label GROUP BY water_id )
                           )wl on wl.water_id = w.id
                       WHERE
                               REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(w.company_name,char(32),''),char(40),''),char(41),''),char(44),''),char(46),''),'（',''),'）',''),'，','')
                               =
                               REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(#{condition.companyName},char(32),''),char(40),''),char(41),''),char(44),''),char(46),''),'（',''),'）',''),'，','')
                         AND( wl.label = 1 or wl.label is null)
                         AND br.id IS NULL) t
                 WHERE 1 = 1
                    <if test="condition.status != null and condition.status != ''">
                        AND t.`status` = #{condition.status}
                    </if>
                    <if test="condition.amount != null and condition.amount != ''">
                        AND t.amount =  #{condition.amount}
                    </if>
                    <if test="condition.date != null">
                        AND t.date = #{condition.date,jdbcType=DATE}
                    </if>
                       ORDER BY t.date DESC, t.time DESC
    </select>
    <select id="queryDepartCZByProject" resultType="com.hzw.sunflower.controller.response.DepartmentUserVo">
        SELECT DISTINCT
            d.id AS departmentId,
            d.department_name AS departmentName,
            u.id AS userId,
            u.user_name AS userName
        FROM
            t_project p
            LEFT JOIN t_project_entrust_user tpe ON p.id = tpe.project_id
            LEFT JOIN t_department d ON tpe.department_id = d.id
            LEFT JOIN r_depart_role dr ON d.id = dr.depart_id
            LEFT JOIN t_role r ON dr.role_id = r.id
            LEFT JOIN t_user u ON dr.user_id = u.id
        WHERE
            p.is_delete = 0
            AND tpe.is_delete = 0
            AND d.is_delete = 0
            AND dr.is_delete = 0
            AND r.is_delete = 0
            AND u.is_delete = 0
            <if test="roleCodes != null and roleCodes.size > 0 ">
                AND r.role_code IN
                <foreach collection="roleCodes" item="roleCode" open="(" close=")" separator=",">
                    #{roleCode}
                </foreach>
            </if>
            <if test="projectIdList != null and projectIdList.size > 0 ">
                AND p.id IN
                <foreach collection="projectIdList" item="projectId" open="(" close=")" separator=",">
                    #{projectId}
                </foreach>
            </if>
        ORDER BY d.id
    </select>
    <select id="queryApplyUserCompanyId" resultType="java.lang.Long">
        SELECT DISTINCT
            ui.company_id
        FROM
            t_bond_apply_refund bar
            LEFT JOIN t_user_identity ui ON bar.created_user_id = ui.user_id
        WHERE
            bar.is_delete = 0
            AND ui.is_delete = 0
            AND bar.id = #{applyId}
    </select>
    <select id="queryVendorProjects" resultType="com.hzw.sunflower.controller.response.MyCompanyProjectVo">
        SELECT DISTINCT
            p.id AS projectId,
            p.purchase_number AS projectNum,
            p.purchase_name AS projectName,
            p.re_tender,
            pbs.id AS sectionId,
            pbs.package_number AS packageNumber,
            pbs.package_name AS sectionName,
            u.user_name AS projectChargeName,
            d.department_name AS departmentName,
            d.id AS departmentId,
            a.contact_person AS vendorName,
            a.user_mobile AS vendorPhone
        FROM
            t_project p
            LEFT JOIN t_project_bid_section pbs ON p.id = pbs.project_id
            LEFT JOIN t_apply_info a ON p.id = a.project_id AND pbs.id = a.sub_id
            LEFT JOIN t_project_entrust_user tpe ON p.id = tpe.project_id
            LEFT JOIN t_user u ON tpe.user_id = u.id
            LEFT JOIN t_department d ON tpe.department_id = d.id
        WHERE
            p.is_delete = 0
            AND pbs.is_delete = 0
            AND tpe.is_delete = 0
            AND tpe.type = 1
            AND u.is_delete = 0
            AND d.is_delete = 0
        <if test="req.status != null and req.status == 0">
            AND ((a.is_delete = 0 AND a.apply_status = 4) OR a.company_id is null)
        </if>
        <if test="req.status != null and req.status == 1">
            AND a.is_delete = 0
            AND a.apply_status = 4
            AND a.company_id = #{req.companyId}
        </if>
        <if test="req.status != null and req.status == 2">
            AND ((a.is_delete = 0 AND a.apply_status = 4 AND a.company_id != #{req.companyId}) OR a.company_id is null)
        </if>
        <if test="req.keyWords != null and req.keyWords != ''">
            AND (p.purchase_number like concat('%',#{req.keyWords},'%')
            OR p.purchase_name like concat('%',#{req.keyWords},'%'))
        </if>
    </select>

</mapper>
