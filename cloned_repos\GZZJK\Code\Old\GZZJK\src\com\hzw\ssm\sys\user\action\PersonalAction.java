package com.hzw.ssm.sys.user.action;

import java.io.IOException;
import java.util.List;

import javax.servlet.http.HttpSession;

import org.apache.struts2.convention.annotation.Action;
import org.apache.struts2.convention.annotation.Namespace;
import org.apache.struts2.convention.annotation.ParentPackage;
import org.apache.struts2.convention.annotation.Result;
import org.apache.struts2.convention.annotation.Results;
import org.springframework.beans.factory.annotation.Autowired;

import com.hzw.ssm.fw.base.BaseAction;
import com.hzw.ssm.fw.util.MessageConstants;
import com.hzw.ssm.sys.system.entity.RoleEntity;
import com.hzw.ssm.sys.system.service.RoleService;
import com.hzw.ssm.sys.user.entity.UserEntity;
import com.hzw.ssm.sys.user.service.UserService;

/**
 * 用户个人信息操作
 * 
 * @page user
 * <AUTHOR>
 * 
 */
@Namespace("/personalInfo")
@ParentPackage(value = "default")
@Results( {
		@Result(name = "personalInit", location = "/jsp/user/userself_edit.jsp")
		})
		
public class PersonalAction extends BaseAction {

	/**
	 * 
	 */
	private static final long serialVersionUID = -8823858495799309882L;

	/** 用户对象 */
	private UserEntity objUser;

	/** 需要编辑的用户编号 */
	private String key;
	
	/** 角色列表——新增用户选择角色*/
	private List<RoleEntity> roleList;
	

	@Autowired
	private UserService userService;

	/** 角色 */
	@Autowired
	private RoleService roleService;
	/**
	 * 初始化用户个人信息编辑页面
	 * 
	 * @throws IOException
	 */
	@Action("personalInit")
	public String personalInfoInit(){
		this.context();
		//当前session
		HttpSession session = getRequest().getSession();
		//当前用户信息
		UserEntity info = (UserEntity)session.getAttribute("userInfo");
		objUser = userService.selectUserById(info.getUser_id());
		roleList=roleService.queryRoleList(new RoleEntity());
		return "personalInit";
	}
	
	/**
	 * 编辑保存用户个人信息 
	 * 
	 * @throws IOException
	 */
	@Action("personalSave")
	public String personalSave() throws IOException {
		try{
			this.setOpaUserAndDate(objUser);
			userService.updatePersonnalUser(objUser);
			this.setAlertMessage(MessageConstants.SAVE_SUCCESS);
		}catch(Exception e){
			e.printStackTrace();
			this.setAlertMessage(MessageConstants.SAVE_FAILED);
		}
		return this.personalInfoInit();
	}
	

	public UserEntity getObjUser() {
		return objUser;
	}

	public void setObjUser(UserEntity objUser) {
		this.objUser = objUser;
	}

	public String getKey() {
		return key;
	}

	public void setKey(String key) {
		this.key = key;
	}

	public List<RoleEntity> getRoleList() {
		return roleList;
	}

	public void setRoleList(List<RoleEntity> roleList) {
		this.roleList = roleList;
	}

	
	
}
