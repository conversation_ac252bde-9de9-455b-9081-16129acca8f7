package com.hzw.sunflower.dto;

import com.hzw.sunflower.entity.OssFile;
import lombok.Data;

/**
 * <AUTHOR>
 * @datetime 2024/04/02 9:58
 * @description: pdf签名解析信息DTO
 * @version: 1.0
 */
@Data
public class PdfSignatureDto extends OssFile {

    /**
     * 是否含有电子签章 1.是 2.否
     */
    private Integer hasElecSign;

    /**
     * 电子签章解析是否为本公司章 1.是 2.否
     */
    private Integer isSameSign;

    /**
     * 上传ip
     */
    private String uploadIp;

    /**
     * 编制机器
     */
    private String compilationMachine;

    /**
     * 文件作者
     */
    private String fileAuthor;

}
