package com.hzw.common.core.utils.file;


import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;

import java.nio.file.Files;
import java.nio.file.Paths;


public class Img2Base64Utils {

    /**
     * 本地图片转base64编码
     *
     * @param filePath 文件图片所在路径
     *
     * @return base64编码
     */
    public static String imageToBase64(String filePath) throws Exception {
        if(StringUtils.isBlank(filePath)){
            return null;
        }
        String encode="";
        try{
            byte[] bytes = Files.readAllBytes(Paths.get(filePath));
            encode = Base64.encodeBase64String(bytes);
        }catch (Exception e){
            throw e;
        }
        return encode;
    }
}
