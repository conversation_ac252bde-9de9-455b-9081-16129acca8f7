package com.hzw.ssm.report.entity;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.hzw.ssm.fw.base.BaseEntity;
import com.hzw.ssm.fw.util.Page;

/**
 * 统计报表实体类
 * <AUTHOR>
 */
public class ReportEntity extends BaseEntity{

	private static final long serialVersionUID = 1L;
	/**
	 * 项目名称
	 */
	private String projectName;
	/**
	 * 项目编号
	 */
	private String projectNo;
	/**
	 * 专家ID
	 */
	private String expertId;
	/**
	 * 评价分
	 */
	private double score;
	/**
	 * 专家姓名
	 */
	private String expertName;
	/**
	 * 手机号码
	 */
	private String phone;
	/**
	 * 身份证号
	 */
	private  String idCard;
	/**
	 * 项目负责人
	 */
	private String manager;
	/**
	 * 开标时间
	 */
	private Date bidTime;
	/**
	 * 扣分原由
	 */
	private String appraiseColumn;
	/**
	 * 专家总求和
	 */
	private double sumScore;
	/**
	 * 专家根据每个项目所对应的分数
	 */
	private double singleScore;
	/**
	 * 处理意见
	 */
	private String opinion;
	/** 分页 */
	private Page page;
	/**
	 * 专家出库时间
	 */
	private String outTime;
	/**
	 * 出库原因
	 */
	private String outReason;
	/** 查询条件中时间区域 开始*/
	private String startTime;
	/** 查询条件中时间区域 结束*/
	private String endTime;
	/**
	 * 页面搜索条件中的状态(明显中的排序方式、注销页面中的原因)
	 */
	private String status;
	/**
	 * 查询时需要拼接的排序方式
	 */
	private String sortWay;
	/**
	 * 页面传递的注销方式(1.已注销 2.待注销)
	 */
	private String  cancellationWay;
	//评价人
	private String  appraiseUser;
	//评价时间
	private Date  appraiseTime;
	private String  appraiseTimeStr;
	//审核开始时间
	private String  startAppraiseTime;
	//审核结束时间
	private String  endAppraiseTime;
	//审核人
	private String  reviewUser;
	//审核时间
	private Date  reviewTime;
	private String  reviewTimeStr;
	//审核开始时间
	private String  startReviewTime;
	//审核结束时间
	private String  endReviewTime;
	//抽取批次号
	private String  decimationBatch;
	private String  year;
	private String  isApprove;
	private String  tab;
	//专家结果ID
	private String  extractResultId;
	//评价表ID
	private String  appraiseId;
	private String  evalScore;
	private String  reviewStatus;
	private String  rejectionReason;
	//暂停评标时间段
	private String  bidDate;
	//暂停评标开始时间
	private Date  startBidDate;
	//暂停评标结束时间
	private Date  endBidDate;
	//是否暂停评标 0:否；1：是
	private Integer  isBid;
	/**
	 * 待注销页面搜索条件（原因）
	 */
	private List<String> reasonLst = new ArrayList<String>();
	public String getProjectName() {
		return projectName;
	}
	public void setProjectName(String projectName) {
		this.projectName = projectName;
	}
	public String getExpertName() {
		return expertName;
	}
	public void setExpertName(String expertName) {
		this.expertName = expertName;
	}

	public Integer getIsBid() {
		return isBid;
	}

	public void setIsBid(Integer isBid) {
		this.isBid = isBid;
	}

	public Date getStartBidDate() {
		return startBidDate;
	}

	public void setStartBidDate(Date startBidDate) {
		this.startBidDate = startBidDate;
	}

	public Date getEndBidDate() {
		return endBidDate;
	}

	public void setEndBidDate(Date endBidDate) {
		this.endBidDate = endBidDate;
	}

	public String getBidDate() {
		return bidDate;
	}

	public void setBidDate(String bidDate) {
		this.bidDate = bidDate;
	}

	public String getRejectionReason() {
		return rejectionReason;
	}

	public String getReviewTimeStr() {
		return reviewTimeStr;
	}

	public String getAppraiseTimeStr() {
		return appraiseTimeStr;
	}

	public void setAppraiseTimeStr(String appraiseTimeStr) {
		this.appraiseTimeStr = appraiseTimeStr;
	}

	public void setReviewTimeStr(String reviewTimeStr) {
		this.reviewTimeStr = reviewTimeStr;
	}

	public void setRejectionReason(String rejectionReason) {
		this.rejectionReason = rejectionReason;
	}

	public String getReviewStatus() {
		return reviewStatus;
	}

	public void setReviewStatus(String reviewStatus) {
		this.reviewStatus = reviewStatus;
	}

	public String getPhone() {
		return phone;
	}
	public void setPhone(String phone) {
		this.phone = phone;
	}

	public String getExtractResultId() {
		return extractResultId;
	}

	public void setExtractResultId(String extractResultId) {
		this.extractResultId = extractResultId;
	}

	public String getAppraiseId() {
		return appraiseId;
	}

	public void setAppraiseId(String appraiseId) {
		this.appraiseId = appraiseId;
	}

	public String getIdCard() {
		return idCard;
	}
	public void setIdCard(String idCard) {
		this.idCard = idCard;
	}
	public String getManager() {
		return manager;
	}
	public void setManager(String manager) {
		this.manager = manager;
	}

	public String getTab() {
		return tab;
	}

	public void setTab(String tab) {
		this.tab = tab;
	}

	public String getIsApprove() {
		return isApprove;
	}

	public String getEvalScore() {
		return evalScore;
	}

	public void setEvalScore(String evalScore) {
		this.evalScore = evalScore;
	}

	public void setIsApprove(String isApprove) {
		this.isApprove = isApprove;
	}

	public String getStartAppraiseTime() {
		return startAppraiseTime;
	}

	public void setStartAppraiseTime(String startAppraiseTime) {
		this.startAppraiseTime = startAppraiseTime;
	}

	public String getEndAppraiseTime() {
		return endAppraiseTime;
	}

	public void setEndAppraiseTime(String endAppraiseTime) {
		this.endAppraiseTime = endAppraiseTime;
	}

	public String getYear() {
		return year;
	}

	public void setYear(String year) {
		this.year = year;
	}

	public String getDecimationBatch() {
		return decimationBatch;
	}

	public void setDecimationBatch(String decimationBatch) {
		this.decimationBatch = decimationBatch;
	}

	public String getStartReviewTime() {
		return startReviewTime;
	}

	public void setStartReviewTime(String startReviewTime) {
		this.startReviewTime = startReviewTime;
	}

	public String getEndReviewTime() {
		return endReviewTime;
	}

	public void setEndReviewTime(String endReviewTime) {
		this.endReviewTime = endReviewTime;
	}

	public String getReviewUser() {
		return reviewUser;
	}

	public void setReviewUser(String reviewUser) {
		this.reviewUser = reviewUser;
	}

	public Date getReviewTime() {
		return reviewTime;
	}

	public void setReviewTime(Date reviewTime) {
		this.reviewTime = reviewTime;
	}

	public String getAppraiseUser() {
		return appraiseUser;
	}

	public void setAppraiseUser(String appraiseUser) {
		this.appraiseUser = appraiseUser;
	}

	public Date getAppraiseTime() {
		return appraiseTime;
	}

	public void setAppraiseTime(Date appraiseTime) {
		this.appraiseTime = appraiseTime;
	}

	public Date getBidTime() {
		return bidTime;
	}
	public void setBidTime(Date bidTime) {
		this.bidTime = bidTime;
	}
	public String getAppraiseColumn() {
		return appraiseColumn;
	}
	public void setAppraiseColumn(String appraiseColumn) {
		this.appraiseColumn = appraiseColumn;
	}
	
	public String getExpertId() {
		return expertId;
	}
	public void setExpertId(String expertId) {
		this.expertId = expertId;
	}

	public double getScore() {
		return score;
	}
	public void setScore(double score) {
		this.score = score;
	}
	public double getSumScore() {
		return sumScore;
	}
	public void setSumScore(double sumScore) {
		this.sumScore = sumScore;
	}
	public double getSingleScore() {
		return singleScore;
	}
	public void setSingleScore(double singleScore) {
		this.singleScore = singleScore;
	}
	public String getOpinion() {
		return opinion;
	}
	public void setOpinion(String opinion) {
		this.opinion = opinion;
	}
	public Page getPage() {
		return page;
	}
	public void setPage(Page page) {
		this.page = page;
	}
	
	public String getStatus() {
		return status;
	}
	public void setStatus(String status) {
		this.status = status;
	}
	public String getSortWay() {
		return sortWay;
	}
	public void setSortWay(String sortWay) {
		this.sortWay = sortWay;
	}
	public String getStartTime() {
		return startTime;
	}
	public void setStartTime(String startTime) {
		this.startTime = startTime;
	}
	public String getEndTime() {
		return endTime;
	}
	public void setEndTime(String endTime) {
		this.endTime = endTime;
	}
	public String getProjectNo() {
		return projectNo;
	}
	public void setProjectNo(String projectNo) {
		this.projectNo = projectNo;
	}
	
	public String getOutTime() {
		return outTime;
	}
	public void setOutTime(String outTime) {
		this.outTime = outTime;
	}
	public String getOutReason() {
		return outReason;
	}
	public void setOutReason(String outReason) {
		this.outReason = outReason;
	}
	public String getCancellationWay() {
		return cancellationWay;
	}
	public void setCancellationWay(String cancellationWay) {
		this.cancellationWay = cancellationWay;
	}
	public List<String> getReasonLst() {
		return reasonLst;
	}
	public void setReasonLst(List<String> reasonLst) {
		this.reasonLst = reasonLst;
	}

	

	
	
	
}
