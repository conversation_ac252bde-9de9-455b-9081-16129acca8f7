package com.hzw.sunflower.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hzw.sunflower.controller.project.request.ProjectParamTemplateREQ;
import com.hzw.sunflower.dto.ProjectParamTemplateDTO;
import com.hzw.sunflower.entity.ProjectParamTemplate;
import com.hzw.sunflower.entity.condition.ProjectParamTemplateCondition;

import java.util.List;


/**
 * 项目参数表 Service接口
 *
 * <AUTHOR>
 * @version 1.0.0 2021-04-14
 */
public interface ProjectParamTemplateService {
    /**
     * 根据条件分页查询项目参数表 列表
     *
     * @param condition 查询条件
     * @return 分页信息
     */
    IPage<ProjectParamTemplate> findProjectParamTemplateByCondition(ProjectParamTemplateCondition condition) throws Exception;

    /**
     * 根据主键ID查询项目参数表 信息
     *
     * @param id 主键ID
     * @return 项目参数表 信息
     */
    ProjectParamTemplate getProjectParamTemplateById(Long id) throws Exception;

    /**
     * 新增项目参数表 信息
     *
     * @param projectParamTemplate 项目参数表 信息
     * @return 是否成功
     */
    Boolean addProjectParamTemplate(ProjectParamTemplate projectParamTemplate) throws Exception;

    /**
     * 修改项目参数表 信息
     *
     * @param projectParamTemplate 项目参数表 信息
     * @return 是否成功
     */
    Boolean updateProjectParamTemplate(ProjectParamTemplate projectParamTemplate) throws Exception;

    /**
     * 根据主键ID删除项目参数表
     *
     * @param id 主键ID
     * @return 是否成功
     */
    Boolean deleteProjectParamTemplateById(Long id) throws Exception;

    /**
     * 根据主键ID列表批量删除项目参数表
     *
     * @param idList 主键ID列表
     * @return 是否成功
     */
    Boolean deleteProjectParamTemplateByIds(List<Long> idList) throws Exception;

    /**
     * @param condition 条件对象
     * <AUTHOR>
     * @Description 多条件查询模板参数
     * @Return java.util.List<com.hzw.sunflower.dto.ProjectParamTemplateDTO>
     * @Date 2021/4/28 9:59
     */
    List<ProjectParamTemplateDTO> listByConditionProjectParamTemplate(ProjectParamTemplateCondition condition);

    /**
     * 批量更新
     *
     * @param projectParamTemplates
     * @return
     */
    Boolean updatesProjectParamTemplate(List<ProjectParamTemplateREQ> projectParamTemplates);

}