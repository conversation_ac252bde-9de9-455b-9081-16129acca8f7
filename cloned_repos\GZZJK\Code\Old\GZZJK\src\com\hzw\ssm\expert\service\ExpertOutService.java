/*
 * 公司名称：江苏华招网信息技术有限公司
 * 版权信息：江苏华招网信息技术有限公司版权所有
 * 文件名称：ExpertOutService.java
 * 修改时间：2021年2月23日
 * 修改人：宋伟
 * 修改内容：
 * 跟踪单号：
 * 修改单号 ：
 */

package com.hzw.ssm.expert.service;

import com.hzw.ssm.expert.dao.ExpertInfoMapper;
import com.hzw.ssm.expert.entity.ExperOutEntity;
import com.hzw.ssm.fw.base.BaseService;
import com.hzw.ssm.sys.project.entity.ProjectEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import javax.annotation.Resource;

/**
 * <一句话功能简述> 专家出库相关service <功能详细描述>
 *
 * <AUTHOR>
 * @version V1.0[产品/模块版本]
 */
@Service
public class ExpertOutService extends BaseService {
	@Autowired
	private ExpertInfoMapper expertInfoMapper;

	@Resource
	private ExpertInfoService expertInfoService;

	/**
	 *
	 * 函数功能描述：分页查询
	 *
	 * @param exertEntity
	 * @return
	 */
	public List<ExperOutEntity> queryListexpert(ExperOutEntity exertEntity) {
		// 1.申明返回值
		List<ExperOutEntity> result = new ArrayList<>();
		// 2.查询专家
		result = expertInfoMapper.queryPageExpertOutList(exertEntity);
		// 3.查询该专家是否存在正在开标项目
		if ("20".equals(exertEntity.getOutStatus()) && exertEntity.isQueryProjectFlag()) {
			for (ExperOutEntity entity : result) {
				List<ProjectEntity> project = expertInfoMapper.queryProjectByExpert(entity);
				if (!project.isEmpty()) {
					entity.setProjectFlag("1");
				}
			}
		}
		// 3.返回
		return result;
	}

	/**
	 *
	 * 函数功能描述：分页查询出库专家
	 *
	 * @param exertEntity
	 * @return
	 */
	public List<ExperOutEntity> queryListout(ExperOutEntity exertEntity) {
		// 1.申明返回值
		List<ExperOutEntity> result = new ArrayList<>();
		// 2.查询专家
		result = expertInfoMapper.queryPageOutList(exertEntity);
		// 3.查询该专家是否存在正在开标项目

		// 3.返回
		return result;
	}

	/**
	 *
	 * 函数功能描述：查询
	 *
	 * @param exertEntity
	 * @return
	 */
	public List<ExperOutEntity> queryexpertsForEXCL(ExperOutEntity exertEntity) {
		// 1.申明返回值
		List<ExperOutEntity> result = new ArrayList<>();
		// 2.查询专家
		result = expertInfoMapper.queryExpertOutList(exertEntity);
		// 3.返回
		return result;
	}

	/**
	 *
	 * 函数功能描述：审核
	 *
	 * @param exertEntity
	 * @return
	 */
	public void checkout(ExperOutEntity exertEntity) {
		try {
			List<String> userIdList = Arrays.asList(exertEntity.getUserId().split(","));
			exertEntity.setUserIds(userIdList);
			expertInfoMapper.updateExpertOutStatus(exertEntity);
			// 新增插入记录表
			if (!CollectionUtils.isEmpty(userIdList)) {
				for (String userId : userIdList) {
					int maxBeachNum = expertInfoMapper.selectMaxBatchNumFromRecord(userId);
					if (maxBeachNum == 0) {
						maxBeachNum = 1;
					} else {
						maxBeachNum++;
					}
					if ("20".equals(exertEntity.getOutStatus())) {
						expertInfoService.addExpertUpdateRecord(userId, "专家出库(审核通过)", "正常", "已出库",
								exertEntity.getModifyRole(), exertEntity.getCheckId(), "", maxBeachNum);
					} else {
						expertInfoService.addExpertUpdateRecord(userId, "专家出库(审核不通过)", "正常", "正常",
								exertEntity.getModifyRole(), exertEntity.getCheckId(), "", maxBeachNum);
					}

				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
	 *
	 * 函数功能描述：重新入库
	 *
	 * @param exertEntity
	 * @return
	 */
	public void checkIn(ExperOutEntity exertEntity) {
		try {
			List<String> userIdList = Arrays.asList(exertEntity.getUserId().split(","));
			exertEntity.setUserIds(userIdList);
			expertInfoMapper.updateExpertOutIn(exertEntity);
			// 新增插入记录表
			if (!CollectionUtils.isEmpty(userIdList)) {
				for (String userId : userIdList) {
					int maxBeachNum = expertInfoMapper.selectMaxBatchNumFromRecord(userId);
					if (maxBeachNum == 0) {
						maxBeachNum = 1;
					} else {
						maxBeachNum++;
					}
					expertInfoService.addExpertUpdateRecord(userId, "专家重新入库", "已出库", "正常", exertEntity.getModifyRole(),
							exertEntity.getCheckId(), "", maxBeachNum);

				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
	 *
	 * 函数功能描述：查询项目
	 *
	 * @param exertEntity
	 * @return
	 */
	public List<ProjectEntity> queryProject(ExperOutEntity exertEntity) {
		List<ProjectEntity> project = expertInfoMapper.queryProjectByExpert(exertEntity);
		return project;
	}

}
