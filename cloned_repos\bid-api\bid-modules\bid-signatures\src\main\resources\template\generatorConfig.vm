<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE generatorConfiguration PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN" "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd" >
<generatorConfiguration>

    <!-- 配置文件 -->
    <properties resource="generator.properties"></properties>

    <context id="MyContext" targetRuntime="MyBatis3" defaultModelType="flat">

        <property name="javaFileEncoding" value="UTF-8"/>
        <!-- 由于beginningDelimiter和endingDelimiter的默认值为双引号(")，在Mysql中不能这么写，所以还要将这两个默认值改为`  -->
        <property name="beginningDelimiter" value="`"/>
        <property name="endingDelimiter" value="`"/>
        <!-- 为生成的Java模型创建一个toString方法 -->
       #* <plugin type="io.renren.common.utils.plugin.ToStringPlugin"></plugin>*#
##        <plugin type="org.mybatis.generator.plugins.ToStringPlugin"></plugin>

        <!-- 为生成的Java模型类添加序列化接口，并生成serialVersionUID字段 -->
        <plugin type="com.hzw.sunflower.util.plugin.SerializablePlugin">
            <property name="suppressJavaInterface" value="false"/>
        </plugin>


        <!-- 去掉get set方法 -->
        <plugin type="com.hzw.sunflower.util.plugin.IngoreSetterAndGetterPlugin" />

        <!-- 生成一个新的selectByExample方法，这个方法可以接收offset和limit参数，主要用来实现分页，只支持mysql(已使用pagehelper代替) -->
        <!--<plugin type="com.zheng.common.plugin.PaginationPlugin"></plugin>-->

        <!-- 生成在XML中的<cache>元素 -->
        #*<plugin type="org.mybatis.generator.plugins.CachePlugin">
            <!-- 使用ehcache -->
##            <property name="cache_type" value="org.mybatis.caches.ehcache.LoggingEhcache" />
            <property name="cache_type" value="org.mybatis.caches.redis.RedisCache" />
            <!-- 内置cache配置 -->
            <!--
            <property name="cache_eviction" value="LRU" />
            <property name="cache_flushInterval" value="60000" />
            <property name="cache_readOnly" value="true" />
            <property name="cache_size" value="1024" />
            -->
        </plugin>*#

        <!-- Java模型生成equals和hashcode方法 -->
       #* <plugin type="org.mybatis.generator.plugins.EqualsHashCodePlugin"></plugin>
        <plugin type="io.renren.common.utils.plugin.WhereSqlTextPlugin"/>*#

        <!-- 生成的代码去掉注释 -->
        <commentGenerator type="com.hzw.sunflower.util.plugin.CommentGenerator">
            <property name="suppressAllComments" value="true" />
            <property name="suppressDate" value="true"/>
        </commentGenerator>

        <!-- 数据库连接 -->
        <jdbcConnection driverClass="${generator.jdbc.driver}"
                        connectionURL="${generator.jdbc.url}"
                        userId="${generator.jdbc.username}"
                        password="${generator_jdbc_password}" />
        <javaTypeResolver type="org.mybatis.generator.internal.types.JavaTypeResolverDefaultImpl">
            <!--
                true：使用BigDecimal对应DECIMAL和 NUMERIC数据类型
                false：默认,
                    scale>0;length>18：使用BigDecimal;
                    scale=0;length[10,18]：使用Long；
                    scale=0;length[5,9]：使用Integer；
                    scale=0;length<5：使用Short；
             -->
            <property name="forceBigDecimals" value="false"/>

        </javaTypeResolver>
        <!-- model生成 -->
        <javaModelGenerator targetPackage="${generator_javaModelGenerator_targetPackage}" targetProject="${targetProject_model}" />

        <!-- MapperXML生成 -->

##        <sqlMapGenerator targetPackage="${generator_sqlMapGenerator_targetPackage}" targetProject="${targetProject_sqlMap}/resources/mapper" />
        <!-- 生成map.xml文件存放地址 -->
##        <sqlMapGenerator targetPackage="${generator_sqlMapGenerator_targetPackage}" targetProject="${targetProject_sqlMap}/resources/mapper">
##            <property name="enableSubPackages" value="true"/>
##        </sqlMapGenerator>


        <!-- 需要映射的表 -->
        #foreach($table in $tables)
##            #if($last_insert_id_tables.containsKey($!table.table_name) == true)
                <table schema="$!table.table_schema" tableName="$!table.table_name" domainObjectName="$!table.model_name" enableCountByExample="false" enableUpdateByExample="false" enableSelectByExample="false" selectByExampleQueryId="false" enableDeleteByExample="false">
##            <table schema="$!table.table_schema" tableName="$!table.table_name" domainObjectName="$!table.model_name">
##                    <generatedKey column="$!last_insert_id_tables.get($!table.table_name)" sqlStatement="$!table.table_name_seq" identity="false" type="pre"/>

                        <generatedKey column="$!table.selectkey_id" sqlStatement="$!table.table_name_seq" identity="false" type="pre" />
                </table>
##            #else
##                <table schema="$!table.table_schema" tableName="$!table.table_name" domainObjectName="$!table.model_name"></table>
##            #end
        #end
    </context>
</generatorConfiguration>
