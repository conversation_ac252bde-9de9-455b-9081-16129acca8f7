package com.hzw.sunflower.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hzw.sunflower.dto.BondTemplateDto;
import com.hzw.sunflower.entity.BondTemplate;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hzw.sunflower.entity.condition.BondTemplateCondition;

/**
 * <p>
 * 保证金流水模板 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-28
 */
public interface BondTemplateService extends IService<BondTemplate> {

    /**
     * 分页查询
     * @param condition
     * @return
     */
    IPage<BondTemplateDto> listPage(BondTemplateCondition condition);

    /**
     * 新增编辑模板
     * @param bondTemplate
     * @return
     */
    Boolean addOrUpdateTemplate(BondTemplate bondTemplate);

    /**
     * 修改模板配置
     * @param bondTemplate
     * @return
     */
    Boolean updateTemplateDetail(BondTemplate bondTemplate);
}
