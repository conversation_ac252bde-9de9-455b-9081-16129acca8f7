package com.hzw.depositfee.api.constant;

import java.math.BigDecimal;

public class CommonConstant {
    public static final Integer NO = 2;
    public static final Integer YES = 1;

    // 是否删除
    public static final Integer IS_DELETE = 0;

    // 数据来源
    public static final Integer SYSTEM = 1;
    public static final Integer USER = 2;

    /** 2022年 收入数据年限区分 */
    public static final Integer YEAR_2022 = 2022;

    /** 造价处和外贸处编码 */
    public static final String DEPT_CODE_COST_TENDER = "141901";
    public static final String DEPT_CODE_COST = "141902";
    public static final String DEPT_CODE_FOREIGN_TENDER = "142101";
    public static final String DEPT_CODE_FOREIGN = "142102";
    public static final String DEPT_CODE_COST_OFFICE = "1419";
    public static final String DEPT_CODE_FOREIGN_OFFICE = "1421";
    public static final String DEPT_CODE_COST_TENDER_NAME = "造价处室（招标）";
    public static final String DEPT_CODE_COST_NAME = "造价处室（造价）";

    /** 外贸处（外贸）下记账单元外贸销售设备code */
    public static final String DEPT_CODE_FOREIGN_SELL_EQUIPMENT= "***************";

    /** 费用科目code */
    public static final String EXPENSE_ACCOUNTS_CODE= "640199";

    /** 税金计算小数 */
    public static final BigDecimal VARIABLE_0_06 = BigDecimal.valueOf(0.06);
    public static final BigDecimal VARIABLE_0_005 = BigDecimal.valueOf(0.005);
    public static final BigDecimal VARIABLE_0_04 = BigDecimal.valueOf(0.04);
    public static final BigDecimal VARIABLE_0_12 = BigDecimal.valueOf(0.12);
    public static final BigDecimal VARIABLE_0_1 = BigDecimal.valueOf(0.1);
    public static final BigDecimal VARIABLE_0_25 = BigDecimal.valueOf(0.25);

    /** 2022年三处和九处报表收入特殊处理 */
    public static final String SPLIT_PROJECT = "射阳港发电项目";

    /** 三处和九处编码 */
    public static final String DEPT_CODE_THREE = "1407";
    public static final String DEPT_CODE_NINE = "1413";

    /** 收入成本明细操作类型 1.编辑 2.标记 */
    public static final Integer SYN_INCOME_COST_UPDATE = 1;
    public static final Integer SYN_INCOME_COST_TAG = 2;

}
