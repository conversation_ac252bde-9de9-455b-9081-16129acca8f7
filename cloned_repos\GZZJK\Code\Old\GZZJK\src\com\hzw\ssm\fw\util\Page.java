package com.hzw.ssm.fw.util;

public class Page {
	private int showCount = 10; //每页显示记录数
	private int totalPage;		//总页数
	private int totalResult;	//总记录数
	private int currentPage;	//当前页
	private int currentResult;	//当前记录起始索引
	private boolean entityOrField;	//true:需要分页的地方，传入的参数就是Page实体；false:需要分页的地方，传入的参数所代表的实体拥有Page属性
	private String pageStr;		//最终页面显示的底部翻页导航，详细见：getPageStr();
	public int getTotalPage() {
		if(totalResult%showCount==0)
			totalPage = totalResult/showCount;
		else
			totalPage = totalResult/showCount+1;
		return totalPage;
	}
	public void setTotalPage(int totalPage) {
		this.totalPage = totalPage;
	}
	public int getTotalResult() {
		return totalResult;
	}
	public void setTotalResult(int totalResult) {
		this.totalResult = totalResult;
	}
	public int getCurrentPage() {
		if(currentPage<=0)
			currentPage = 1;
		if(currentPage>getTotalPage())
			currentPage = getTotalPage();
		return currentPage;
	}
	public void setCurrentPage(int currentPage) {
		this.currentPage = currentPage;
	}
	public String getPageStr() {
		StringBuffer sb = new StringBuffer();
		if(totalResult>0){
			sb.append("<ul class='pageTextBefore'>当前：").append(currentPage).append("/").append(totalPage).append("页&nbsp;&nbsp;");
			sb.append("共").append(totalResult).append("条&nbsp;&nbsp;");
			sb.append("每页显示<input id='showCount' maxlength=\"6\" name='showCount' onkeyup=\"value=value.replace(/[^\\d]/g,'')\" onbeforepaste=\"clipboardData.setData('text',clipboardData.getData('text').replace(/[^\\d]/g,''))\" type='text' value='").append(showCount).append("' size='2'/>条&nbsp;&nbsp;");
			sb.append("<input type='button' value='GO' class='pagebtn' onclick=\"jumpPageSize()\">&nbsp;&nbsp;</ul>");
			sb.append("	<ul>\n");
			if(currentPage==1){
				sb.append("	<li class=\"pageinfo\">首页</li>\n");
				sb.append("	<li class=\"pageinfo\">上页</li>\n");
			}else{	
				sb.append("	<li><a href=\"#@\" onclick=\"nextPage(1,"+showCount+")\">首页</a></li>\n");
				sb.append("	<li><a href=\"#@\" onclick=\"nextPage("+(currentPage-1)+","+showCount+")\">上页</a></li>\n");
			}
			int showTag = 10;	//分页标签显示数量
			int startTag = 1;
			if(currentPage>showTag){
				startTag = currentPage-9;
			}
			int endTag = startTag+showTag-1;
			for(int i=startTag; i<=totalPage && i<=endTag; i++){
				if(currentPage==i)
					sb.append("<li class=\"current\">"+i+"</li>\n");
				else
					sb.append("	<li><a href=\"#@\" onclick=\"nextPage("+i+","+showCount+")\">"+i+"</a></li>\n");
			}
			if(currentPage==totalPage){
				sb.append("	<li class=\"pageinfo\">下页</li>\n");
				sb.append("	<li class=\"pageinfo\">尾页</li>\n");
			}else{
				sb.append("	<li><a href=\"#@\" onclick=\"nextPage("+(currentPage+1)+","+showCount+")\">下页</a></li>\n");
				sb.append("	<li><a href=\"#@\" onclick=\"nextPage("+totalPage+","+showCount+")\">尾页</a></li>\n");
			}
			sb.append("</ul>\n");
			sb.append("<div class='pageText'>跳转到<input maxlength=\"6\" onkeyup=\"value=value.replace(/[^\\d]/g,'')\" onbeforepaste=\"clipboardData.setData('text',clipboardData.getData('text').replace(/[^\\d]/g,''))\" id='curPage' name='curPage' type='text' value='").append(currentPage).append("' size='2'/>页&nbsp;&nbsp;");
			sb.append("<input type='button' value='GO' class='pagebtn' onclick=\"jumpCurrentPage()\">&nbsp;&nbsp;</div>");
			sb.append("<script type=\"text/javascript\">\n");
			
			sb.append("function jumpCurrentPage(){\n");
			sb.append("var pageSize = $(\"#showCount\").val();\n");
			sb.append("var curPage = $(\"#curPage\").val();\n");
			sb.append("nextPage(curPage,pageSize);\n");
			sb.append("}\n");
			
			sb.append("function jumpPageSize(){\n");
			sb.append("var pageSize = $(\"#showCount\").val();\n");
			sb.append("nextPage(1,pageSize);\n");
			sb.append("}\n");
			
			sb.append("function nextPage(page,showCount){");
			sb.append("	if(true && document.forms[0]){\n");
			sb.append("		var url = document.forms[0].getAttribute(\"action\");\n");
			sb.append("		if(url.indexOf('?')>-1){url += \"&"+(entityOrField?"currentPage":"page.currentPage")+"=\";}\n");
			sb.append("		else{url += \"?"+(entityOrField?"currentPage":"page.currentPage")+"=\";}\n");
			sb.append("     url += page+\"&"+(entityOrField?"showCount":"page.showCount")+"=\"+showCount;");
			sb.append("		document.forms[0].action = url;\n");
			sb.append("		document.forms[0].submit();\n");
			sb.append("	}else{\n");
			sb.append("		var url = document.location+'';\n");
			sb.append("		if(url.indexOf('?')>-1){\n");
			sb.append("			url=url.substring(0,url.indexOf('?')+1);");
			sb.append("			if(url.indexOf('currentPage')>-1){\n");
			sb.append("				var reg = /currentPage=\\d*/g;\n");
			sb.append("				url = url.replace(reg,'currentPage=');\n");
			sb.append("			}else{\n");
			sb.append("				url += \"&"+(entityOrField?"currentPage":"page.currentPage")+"=\";\n");
			sb.append("			}\n");
			sb.append("		}else{url += \"?"+(entityOrField?"currentPage":"page.currentPage")+"=\";}\n");
			sb.append("     url += page+\"&"+(entityOrField?"showCount":"page.showCount")+"=\"+showCount;");
			sb.append("		document.location = url;\n");
			sb.append("	}\n");
			sb.append("}\n");/*
			sb.append("function checkRate(id){");
			sb.append("var number = $(\"#\"+id).val();\n");
			sb.append("if(number != '' || isNaN(number)){\n");
			sb.append("number = parseInt(number);\n");
			sb.append("if(number<=0||!(/^\\d+$/.test(number))){");
			sb.append("alert(\"只允许输入整数\");\n");
			sb.append("$(\"#\"+id).val('');\n");
			sb.append("return false;\n");
			sb.append("}\n");
			sb.append("}\n");
			sb.append("}\n");*/
			sb.append("</script>\n");
		}
		pageStr = sb.toString();
		return pageStr;
	}
	public void setPageStr(String pageStr) {
		this.pageStr = pageStr;
	}
	public int getShowCount() {
		return showCount;
	}
	public void setShowCount(int showCount) {
		this.showCount = showCount;
	}
	public int getCurrentResult() {
		currentResult = (getCurrentPage()-1)*getShowCount();
		if(currentResult<0)
			currentResult = 0;
		return currentResult;
	}
	public void setCurrentResult(int currentResult) {
		this.currentResult = currentResult;
	}
	public boolean isEntityOrField() {
		return entityOrField;
	}
	public void setEntityOrField(boolean entityOrField) {
		this.entityOrField = entityOrField;
	}
	
}
