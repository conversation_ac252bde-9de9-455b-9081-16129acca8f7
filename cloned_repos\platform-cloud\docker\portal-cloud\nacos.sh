#!/bin/sh
#cp TEMPLATE FILE
docker stop portal-nacos || true
docker rm portal-nacos || true
#docker rmi nacos/nacos-server:v2.2.1 || true
#docker pull nacos/nacos-server:v2.2.1
docker run -itd -p 8848:8848 --net=host -e MODE=standalone -e PREFER_HOST_MODE=hostname  --log-opt max-size=100m --log-opt max-file=3  --name portal-nacos -v /home/<USER>/plan1/portal-cloud/config/nacos:/home/<USER>/conf -v /data/log/nacos:/home/<USER>/logs  nacos/nacos-server:v2.2.1
