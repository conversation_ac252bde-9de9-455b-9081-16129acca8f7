package com.hzw.sunflower.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hzw.sunflower.controller.response.BondCompanyBankVo;
import com.hzw.sunflower.entity.BondCompanyBank;

import java.util.List;

/**
 * <p>
 * 供应商行号维护表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-28
 */
public interface BondCompanyBankService extends IService<BondCompanyBank> {

    /**
     * 根据companyId去供应商行号维护表中查询行号信息
     * @param companyId
     * @return
     */
    List<BondCompanyBankVo> getPayFileCompanyInfo(Long companyId,Long refundId);
}
