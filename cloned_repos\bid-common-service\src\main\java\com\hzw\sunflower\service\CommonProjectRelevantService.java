package com.hzw.sunflower.service;

import com.hzw.sunflower.dto.*;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2021/7/14 18:58
 * @description：项目相关公共接口
 * @modified By：`
 * @version: 1.0
 */
public interface CommonProjectRelevantService {

    /**
     * 根据项目id查询项目信息
     *
     * @param projectId
     * @return
     */
    ProjectInfoDTO getProjectById(Long projectId);

    /**
     * 根据标段id查询标段信息
     *
     * @param sectionId
     * @return
     */
    SectionInfoDTO getSectionById(Long sectionId);

    /**
     * 根据项目id查询标段信息
     *
     * @param projectId
     * @return
     */
    List<SectionInfoDTO> getProjectIdBySection(Long projectId, Integer bidRound,Integer clarifyType,Integer userIdentity);

    /**
     * 根据项目id查询标段信息
     *
     * @param projectNumber 项目编号
     * @param isVague       是否模糊查询
     * @return
     */
    List<SectionInfoDTO> getProjectIdBySection(String projectNumber, boolean isVague,Long userId);

    /**
     * 根据项目id查询委托信息
     *
     * @param projectId
     * @return
     */
    List<ProjectEntrustInfoDTO> getProjectEntrustById(Long projectId);

    /**
     * 根据项目id查询委托信息
     *
     * @param projectId
     * @return
     */
//    ProjectBidDocRelationInfoDto getProjectBidDocRelationById(Long projectId, Long sectionId);

    /**
     * 修改标段时间
     *
     * @param dtoList
     * @return
     */
    Boolean updateSectionTime(List<SectionTimeDTO> dtoList,Integer bidRound);

    /**
     * 根据标段id查询标段信息
     *
     * @param sectionIds
     * @return
     */
    List<SectionInfoDTO> getProjectIdBySection(List<String> sectionIds);


    /**
     * 根据项目id查询资格预审方式 0：按包 1：按项目，和标段信息
     * @return
     */
    List<ProjectSectionDTO> getProjectSectionInfoById(Long projectId,String purchaseMode,String purchaseStatus,Integer bidRound);

    /**
     * 检验标段是否异常
     * @param sectionInfo
     * @return
     */
    Boolean checkSectionAbnormal(SectionInfoDTO sectionInfo);


    /**
     * 根据项目id,标段id查询项目委托人信息
     *
     * @param projectId
     * @return
     */
    ProjectInfoDTO getProjectUserInfo(Long projectId);

    /**
     * 校验是否为资格预审第二轮
     * @param subIdList
     * @return
     */
    Boolean isPreQualification(List<Long> subIdList);

    /**
     * 校验标段文件发售审核方式
     * @param sectionIds
     * @param code
     * @return
     */
    Boolean validReviewFileType(String sectionIds, String code);

    /**
     * 根据项目id查询项目当前负责人(被委托人)所在处室code
     * @param projectId
     * @return
     */
    String selectDepartCodeByProjectId(Long projectId);

    /**
     * 根据项目归属获取各模板归属
     * @param projectId
     * @return
     */
    ProjectBelongDto getAllBelong(Long projectId);

    /**
     * 判断是否是总公司
     * @param companyId
     * @return
     */
    Integer isMainCompany(Long companyId);

    /**
     * 判断是否是特殊分公司
     * @param companyId
     * @return
     */
    boolean isSpecialCompany(Long companyId);

    /**
     * 查询标段表
     * @param projectId
     * @return
     */
    List<SectionInfoDTO> getSectionInfos(Long projectId);


}
