package com.hzw.sunflower.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hzw.sunflower.controller.request.DictionaryReq;
import com.hzw.sunflower.controller.response.DictionaryVO;
import com.hzw.sunflower.entity.Dictionary;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 字典表 Mapper接口
 *
 * <AUTHOR>
 * @version 1.0.0 2021-04-07
 */
public interface DictionaryMapper extends BaseMapper<Dictionary> {

    List<DictionaryVO> selectDictionaryByParentId(@Param("contion") DictionaryReq dictionaryReq);

    List<Dictionary> selectDictionaryForCurrency();

    List<Dictionary> selectAllCountry();

    List<DictionaryVO> getByHistoryDicList(Long userId);

    Boolean getChildrenList(Long id);
}
