///***
/// * 固定格式的字段名 - 字段值显示 UI
/// *
/// * 字段名不能超过 4 个字符，否则之后的字符会被丢弃
/// * 可通过传入 maxFieldNameLength && fieldNameWidth，
/// * 来扩充默认字段名的 字符数 和 宽度
/// */

import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';

import 'package:flutter_main_app/src/constants/theme.dart';

import 'package:flutter_main_app/src/components/CustomMonospacedText.dart';

class CustomFieldDisplay extends StatelessWidget {

  /// * 推测默认行高为：lingHeight = fontSize + 5.0
  /// * 因此：fontPadding = lingHeight - fontSize = 5.0
  /// * 正确性待验证，目前表现没有问题
  static const double _fontPadding = 5.0;

  const CustomFieldDisplay({
    Key key,
    @required this.fieldName,
    @required this.fieldValue,
    this.height = 37.0,
    this.fontSize = 12.0,
    this.fieldNameColor = themeHintTextColor,
    this.fieldValueColor = themeTextColor,
    this.maxFieldNameLength = 4,
    final double fieldNameWidth,
    this.isFieldValueMultiLine = false,
  }) : assert(fieldName != null),
    assert(fieldValue != null),
    assert(height != null),
    assert(height - fontSize >= 4.0),
    assert(fontSize != null),
    assert(fontSize >= 10.0),
    assert(fieldNameColor != null),
    assert(fieldValueColor != null),
    assert(maxFieldNameLength != null && maxFieldNameLength > 0),
    assert(fieldNameWidth == null || fieldNameWidth >= fontSize * (maxFieldNameLength + 1 + 0.5)),
    /// * 传入的字段名宽度 ?? 字号 * (最大字数 + 冒号占 1 格 + 0.5 的富余)
    fieldNameWidth = fieldNameWidth ?? fontSize * (maxFieldNameLength + 1 + 0.5),
    assert(isFieldValueMultiLine != null),
    super(key: key);

  /// * 字段名
  final String fieldName;
  /// * 字段值
  final String fieldValue;
  /// * 高度（特指单行时的高度）
  final double height;
  /// * 字号
  final double fontSize;
  /// * 字段名字体颜色
  final Color fieldNameColor;
  /// * 字段值字体颜色
  final Color fieldValueColor;
  /// * 仅用于适配少量字段名称长于 4 个字符的情况
  /// * 字段名最大字符数
  final int maxFieldNameLength;
  /// * 字段名宽度
  final double fieldNameWidth;
  /// * 右侧字段值是否多行
  final bool isFieldValueMultiLine;

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: this.isFieldValueMultiLine
        ? CrossAxisAlignment.start
        : CrossAxisAlignment.center,
      children: <Widget>[
        /// * 字段名
        Container(
          height: height,
          child: CustomMonospacedText(
            width: fieldNameWidth,
            text: fieldName,
            textStyle: TextStyle(
              fontSize: this.fontSize,
              color: this.fieldNameColor,
            ),
            needColon: true,
            maxTextLengthLimit: maxFieldNameLength,
          ),
        ),
        /// * 字段值
        Expanded(
          child: Padding(
            padding: this.isFieldValueMultiLine
              ? EdgeInsets.only(top: (this.height - this.fontSize - _fontPadding) / 2)
              : const EdgeInsets.only(top: 0.0),
            child: Text(
              this.fieldValue,
              style: TextStyle(
                fontSize: this.fontSize,
                color: this.fieldValueColor,
              ),
            ),
          ),
        ),
      ],
    );
  }

}
