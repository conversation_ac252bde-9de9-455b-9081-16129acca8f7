package com.hzw.sunflower.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzw.ncc.client.dto.*;
import com.hzw.sunflower.client.WorkFlowApiClient;
import com.hzw.sunflower.common.BaseBean;
import com.hzw.sunflower.common.Paging;
import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.config.BondBankProperties;
import com.hzw.sunflower.constant.CommonConstants;
import com.hzw.sunflower.constant.MessageConstants;
import com.hzw.sunflower.constant.constantenum.*;
import com.hzw.sunflower.constants.NccConstants;
import com.hzw.sunflower.constants.NccPushTypeEnum;
import com.hzw.sunflower.controller.request.*;
import com.hzw.sunflower.controller.response.*;
import com.hzw.sunflower.controller.user.response.SpecialTicketVo;
import com.hzw.sunflower.dao.*;
import com.hzw.sunflower.dto.*;
import com.hzw.sunflower.entity.*;
import com.hzw.sunflower.entity.condition.BankRateCondition;
import com.hzw.sunflower.entity.condition.BondProjectCondition;
import com.hzw.sunflower.entity.condition.BondRefundCondition;
import com.hzw.sunflower.exception.ExceptionEnum;
import com.hzw.sunflower.exception.ParamsNotNullException;
import com.hzw.sunflower.exception.SunFlowerException;
import com.hzw.sunflower.exception.WorkFlowException;
import com.hzw.sunflower.freesia.client.constant.FlowClientConstant;
import com.hzw.sunflower.freesia.client.vo.flowable.ReturnVo;
import com.hzw.sunflower.freesia.client.vo.flowable.task.TaskRoleVo;
import com.hzw.sunflower.service.*;
import com.hzw.sunflower.util.*;
import com.hzw.sunflower.util.oss.OssFileStorage;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.io.PrintStream;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 保证金退还项目表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-27
 */
@Service
public class BondRefundServiceImpl extends ServiceImpl<BondRefundMapper, BondRefund> implements BondRefundService {


    @Autowired
    private BondService bondService;

    @Autowired
    private BondMapper bondMapper;

    @Autowired
    private CommonApplyInfoService commonApplyInfoService;

    @Autowired
    private BidTendererRecordService bidTendererRecordService;

    @Autowired
    private BidNoticeSendRecordService bidNoticeSendRecordService;

    @Autowired
    private WorkFlowApiClient flowApiClient;

    @Autowired
    private ProcessRecordService processRecordService;

    @Autowired
    private BondCompanyBankService bondCompanyBankService;

    @Autowired
    private WorkflowCacheService workflowCacheService;

    @Autowired
    private BondRateService bondRateService;

    @Autowired
    private BondApplyRefundService bondApplyRefundService;

    @Autowired
    private ProjectBidSectionService projectBidSectionService;

    @Autowired
    private BidWinPeopleService bidWinPeopleService;

    @Autowired
    private OssFileService ossFileService;

    @Autowired
    private BondRetainRecordService bondRetainRecordService;

    @Autowired
    private BondWaterService bondWaterService;

    @Autowired
    private BondRefundDetailsService bondRefundDetailsService;

    @Autowired
    private BondApplyRelationService bondApplyRelationService;

    @Autowired
    BondRelationService bondRelationService;

    @Autowired
    private BondSplitService bondSplitService;

    @Autowired
    private BankService bankService;

    @Autowired
    private BondPayInfoService bondPayInfoService;

    @Autowired
    private BondBankProperties properties;

    @Autowired
    private ProcessRoleService processRoleService;

    //@Autowired
   // private NccApiClient nccApiClient;

    @Autowired
    private NccBondPushLogService pushLogService;

    @Autowired
    private CommonOpenService commonOpenService;

    @Autowired
    private BankdataBondToNccService bankdataBondToNccService;

    @Value("${oss.active}")
    private String ossType;

    @Value("${bond.china_bank.collectNo}")
    private String chinaBankPayNo;

    @Value("${bond.nj-bank.anco}")
    private String njBankPayNo;

    @Value("${bond.cmbc.acntNo}")
    private String msBankPayNo;

    @Autowired
    private BondRefundMapper bondRefundMapper;

    @Autowired
    private PendingItemService pendingItemService;

    @Autowired
    private BondWaterlabelService bondWaterlabelService;

    @Autowired
    private CompanyService companyService;

    @Autowired
    private BondExamineFailRecordService bondExamineFailRecordService;

    @Autowired
    private BondRefundBalanceService bondRefundBalanceService;

    @Autowired
    private SpecialTicketService specialTicketService;

    @Autowired
    private CnapsBankCodeService cnapsBankCodeService;

    @Autowired
    private BidWinMapper bidWinMapper;

    @Autowired
    private BondRefundService bondRefundService;

    @Autowired
    private CommonProjectRelevantService commonProjectRelevantService;

    @Autowired
    private BondOfflineCompanyService bondOfflineCompanyService;

    @Autowired
    private ProjectService projectService;

    @Autowired
    private BondOfflineCompanyMapper bondOfflineCompanyMapper;

    @Autowired
    private OfflineCompanyMapper offlineCompanyMapper;

    @Autowired
    private UserService userService;

   /**
     * 保证金退款项目列表
     * @param condition
     * @return
     */
    @Override
    public IPage<BondProjectVO> bondProjectList(BondProjectCondition condition) {
        IPage<BondProjectVO> page = condition.buildPage();
        page = bondService.bondProjectList(condition);
        List<BondProjectVO> records = page.getRecords();
        records.forEach(p->{
            //查询已经下载的供应商数量
            List<CommonApplyInfoDto> applys = commonApplyInfoService.queryApplyListByDownLoad(condition.getProjectId(), p.getSectionId(),null, BidRoundEnum.HS.getType());
            p.setTotal(applys.size());
            //查询已经退还供应商情况
            Long refundNum = this.baseMapper.findRefundCount(p.getSectionId());
            p.setRefundNum(Integer.parseInt(refundNum.toString()));
        });
        page.setRecords(records);
        return page;
    }

    @Override
    public IPage<BondProjectVO> bondProjectListOffline(BondProjectCondition condition) {
        IPage<BondProjectVO> page = bondMapper.bondProjectList(condition.buildPage(), condition);
        List<BondProjectVO> records = page.getRecords();
        //查询项目信息
        ProjectInfoDTO project = commonProjectRelevantService.getProjectUserInfo(condition.getProjectId());
        records.forEach(p->{
            //查询供应商总数
            LambdaQueryWrapper<BondOfflineCompany> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(BondOfflineCompany :: getProjectId,p.getProjectId());
            queryWrapper.eq(BondOfflineCompany :: getSectionId,p.getSectionId());
            p.setTotal((int)bondOfflineCompanyService.count(queryWrapper));
            //查询已经退还供应商情况
            Long refundNum = this.baseMapper.findRefundCount(p.getSectionId());
            p.setRefundNum(Integer.parseInt(refundNum.toString()));
            if(project != null){
                p.setProjectName(project.getProjectName()==null?"":project.getProjectName());
                p.setProjectNum(project.getPurchaseNumber()==null?"":project.getPurchaseNumber());
                p.setDeptName(project.getDepartmentName()==null?"":project.getDepartmentName());
                p.setUserName(project.getUserName()==null?"":project.getUserName());
                p.setPurchaseName(project.getPurchaseName()==null?"":project.getPurchaseName());
            }
        });
        page.setRecords(records);
        return page;
    }

    /**
     * 保证金退还标供应商列表
     * @param req
     * @param page
     * @return
     */
    @Override
    public List<BondSupplierInfoVo> bondSupplierList(ProjectSectionReq req, IPage<BondSupplierInfoVo> page) {
        //区分项目流程
        ProjectBidSection projectBidSection = projectBidSectionService.getById(req.getSectionId());
        Project project = projectService.getById(projectBidSection.getProjectId());
        //全流程线下项目取保证金线下项目供应商表，非全流程线下项目取企业表
        if (ProjectOperationFlowEnum.Flow3.getValue().equals(project.getOperationFlow())) {
            //关联状态与退还状态特殊判断
            if (null != req.getRelationStatus() && req.getRelationStatus().size() > 0) {
                req.setRelationStatusOne(req.getRelationStatus().get(0));
            }
            //0：未退还
            if (null != req.getRefundStatus() && req.getRefundStatus().size() > 0) {
                for (Integer refundStatus : req.getRefundStatus()) {
                    if (CommonConstants.NO.equals(refundStatus)) {
                        req.setRefundNo(true);
                    }
                }
            }
            List<BondSupplierInfoVo> bondCompanyList = this.baseMapper.bondSupplierListOffline(req,page);
            bondCompanyList.forEach(s->{
                s.setBidWinInfo(getBidWinInfoOffline(s.getSectionId(), s.getOfflineCompanyId()));
                List<BondRefundDetailsVo> detailsVos = new ArrayList<>();
                BigDecimal bondMoney = new BigDecimal(0);
                //封装退还详情信息
                if(s.getRefundId() != null && !s.getRefundId().equals(0L)){
                    LambdaQueryWrapper<BondRefundDetails> detailQuery = new LambdaQueryWrapper<>();
                    detailQuery.eq(BondRefundDetails::getRefundId,s.getRefundId());
                    List<BondRefundDetails> list = bondRefundDetailsService.list(detailQuery);
                    detailsVos = BeanListUtil.convertList(list,BondRefundDetailsVo.class);

                    BigDecimal agencyFee = new BigDecimal(0);
                    for(BondRefundDetails d:list){
                        agencyFee = agencyFee.add(d.getAgencyFee());
                        bondMoney = bondMoney.add(d.getBondMoney());
                    }
                    s.setBondMoney(bondMoney);
                    //已经中标的时候并且代理服务费不是分批次的时候，返回实收代理服务费
                    if(s.getBidWinInfo() != null && s.getBidWinInfo().getBidStatus() != null && BidTypeEnum.BID_WINED.getType().equals(s.getBidWinInfo().getBidStatus()) && !AgencyFeeTypeEnum.BATCH.getType().equals(s.getFeeType())){
                        s.setFormerAgencyFee(s.getAgencyFee());
                    }else{
                        s.setAgencyFee(agencyFee);
                    }
                }else{
                    //查询流水记录
                    if(StringUtils.isNotBlank(s.getSplitIds())){
                        String[] splitIds = s.getSplitIds().split(",");
                        List<BondWater> waters = bondWaterService.getWaterGroupByAccount(splitIds);
                        for(BondWater water:waters){
                            BondRefundDetailsVo vo = new BondRefundDetailsVo();
                            vo.setBondMoney(new BigDecimal(water.getAmount()));
                            bondMoney = bondMoney.add(new BigDecimal(water.getAmount()));
                            vo.setRefundCompanyName(water.getCompanyName());
                            vo.setRefundNumber(water.getCompanyAccount());
                            vo.setRefundOpenBank(water.getCompanyBankDeposit());
                            vo.setAmountDate(water.getDate());
                            vo.setFromOpenBank(water.getReceiveBank());
                            vo.setFromBankNumber(water.getReceiveAcount());
                            vo.setRefundBankCode(water.getCompanyBankCode());
                            detailsVos.add(vo);
                        }
                    }
                    s.setBondMoney(bondMoney);
                }
                s.setBondRefundDetails(detailsVos);
                // 封装异常关联状态数据
                if (s.getRelationStatus().equals(RelationStatusEnum.EXCEPTION_RELATE.getType()) && s.getWaterIds() != null) {
                    // 查询异常关联的所有包
                    List<Long> waterIds = Arrays.asList(s.getWaterIds().split(",")).stream()
                            .map(m -> {
                                return Long.parseLong(m);
                            }).collect(Collectors.toList());
                    List<BondRelationSectionVo> relationSectionVoList = bondRelationService.getSectionListByWaterIds(waterIds, RelationStatusEnum.EXCEPTION_RELATE.getType());
                    List<Long> sectionIds = relationSectionVoList.stream()
                            .map(m -> {
                                return m.getId();
                            }).collect(Collectors.toList());
                    if(!relationSectionVoList.isEmpty()) {
                        BondApplyRelation applyRelation = bondApplyRelationService.getOne(new LambdaQueryWrapper<BondApplyRelation>().eq(BondApplyRelation::getType, ApplyTypeEnum.RELATION_EXCEPTION.getType())
                                .eq(BondApplyRelation::getCompanyId, s.getOfflineCompanyId()).in(BondApplyRelation::getSectionId, sectionIds).orderByDesc(BaseBean::getCreatedTime)
                                .last("limit 1"));
                        s.setApplyRelationStatus(applyRelation.getStatus());
                    }
                }
                // 未关联时判断申请关联
                if (s.getRelationStatus().equals(RelationStatusEnum.NOT_RELATE.getType())) {
                    BondApplyRelation applyRelation = bondApplyRelationService.getOne(new LambdaQueryWrapper<BondApplyRelation>().eq(BondApplyRelation::getType, ApplyTypeEnum.RELATION_APPLY.getType())
                            .eq(BondApplyRelation::getCompanyId, s.getOfflineCompanyId()).eq(BondApplyRelation::getSectionId, s.getSectionId()).orderByDesc(BaseBean::getCreatedTime)
                            .last("limit 1"));
                    if (applyRelation != null) {
                        s.setApplyRelationStatus(applyRelation.getStatus());
                        //申请关联待确认，关联状态设置为5
                        if (ApplyRelationStatusEnum.WAIT_HANDLE.getType().equals(applyRelation.getStatus())) {
                            s.setRelationStatus(RelationStatusEnum.APPLY_RELATION_WAITE.getType());
                        }
                    }
                }
                SpecialTicketVo specialTicket = specialTicketService.getSpecialTicketInfo( s.getCompanyId(), s.getSectionId());
                s.setSpecialTicket(specialTicket);
                BondOfflineCompanyVo bondOfflineCompany = this.baseMapper.queryBondOfflineCompany(s.getSectionId(), s.getOfflineCompanyId());
                s.setBondOfflineCompany(bondOfflineCompany);
            });

            List<BondSupplierInfoVo> resultVo1 = new ArrayList<>();
            //退还失败的
            List<BondSupplierInfoVo> refundFails = bondCompanyList.stream().filter(x -> BondRefundStatusEnum.FAIL.getType().equals(x.getRefundStatus())).collect(Collectors.toList());
            resultVo1.addAll(refundFails);
            List<BondSupplierInfoVo> refundIng = bondCompanyList.stream().filter(x -> !BondRefundStatusEnum.FAIL.getType().equals(x.getRefundStatus()) && !BondRefundStatusEnum.SUCCESS.getType().equals(x.getRefundStatus())).collect(Collectors.toList());
            resultVo1.addAll(refundIng);
            //退还成功
            List<BondSupplierInfoVo> refundSuccess = bondCompanyList.stream().filter(x -> BondRefundStatusEnum.SUCCESS.getType().equals(x.getRefundStatus())).collect(Collectors.toList());
            // 退还成功数据加入退还时间数据
            ProcessRecordReq processRecordReq = new ProcessRecordReq();
            processRecordReq.setBusinessCode(FlowClientConstant.BOND_APPLY_REFUND);
            if(null != refundSuccess && refundSuccess.size() > 0){
                for (BondSupplierInfoVo success : refundSuccess) {
                    processRecordReq.setBusinessId(success.getRefundId().toString());
                    success.setReturnTime(processRecordService.queryEndTime(processRecordReq));
                }
            }
            resultVo1.addAll(refundSuccess);
            //查询下一步审批角色，财务经办岗才能有撤回按钮
            for(BondSupplierInfoVo vo:resultVo1){
                if(null!=vo.getRefundProcessCode()){
                    String taskRole = flowApiClient.getTaskRoleByProcessInstanceId(null, vo.getRefundProcessCode());
                    JSONObject taskRoleJsonObject = JSON.parseObject(taskRole);
                    List<TaskRoleVo> taskRoleVoList = JSONObject.parseArray(taskRoleJsonObject.getString("data"), TaskRoleVo.class);
                    if(CollectionUtils.isNotEmpty(taskRoleVoList)){
                        if (null != taskRoleVoList.get(0).getRoleCode() && taskRoleVoList.get(0).getRoleCode().equals(RoleCodeEnum.CWSJCW.getType())){
                            vo.setCwjbFlag(1);
                        }
                    }
                }
            }
            return resultVo1;
        } else {
        //关联状态与退还状态特殊判断
        if (null != req.getRelationStatus() && req.getRelationStatus().size() > 0) {
            if (null != req.getRelationStatus() && req.getRelationStatus().size() > 0) {
                req.setRelationStatusOne(req.getRelationStatus().get(0));
            }
        }
        //0：未退还
        if (null != req.getRefundStatus() && req.getRefundStatus().size() > 0) {
            for (Integer refundStatus : req.getRefundStatus()) {
                if (CommonConstants.NO.equals(refundStatus)) {
                    req.setRefundNo(true);
                }
            }
        }
        Integer projectBidSectionStatus = Integer.valueOf(projectBidSection.getStatus());
        List<BondSupplierInfoVo> bondSupplierInfoVos = this.baseMapper.bondSupplierList(req,page);
        bondSupplierInfoVos.forEach(s->{
            s.setBidWinInfo(getBidWinInfo(s.getSectionId(),s.getCompanyId()));
            List<BondRefundDetailsVo> detailsVos = new ArrayList<>();
            BigDecimal bondMoney = new BigDecimal(0);
            //封装退还详情信息
            if(s.getRefundId() != null && !s.getRefundId().equals(0L)){
                LambdaQueryWrapper<BondRefundDetails> detailQuery = new LambdaQueryWrapper<>();
                detailQuery.eq(BondRefundDetails::getRefundId,s.getRefundId());
                List<BondRefundDetails> list = bondRefundDetailsService.list(detailQuery);
                detailsVos = BeanListUtil.convertList(list,BondRefundDetailsVo.class);

                BigDecimal agencyFee = new BigDecimal(0);
                for(BondRefundDetails d:list){
                    agencyFee = agencyFee.add(d.getAgencyFee());
                    bondMoney = bondMoney.add(d.getBondMoney());
                }
                s.setBondMoney(bondMoney);
                //已经中标的时候并且代理服务费不是分批次的时候，返回实收代理服务费
                if(s.getBidWinInfo() != null && s.getBidWinInfo().getBidStatus() != null && BidTypeEnum.BID_WINED.getType().equals(s.getBidWinInfo().getBidStatus()) && !AgencyFeeTypeEnum.BATCH.getType().equals(s.getFeeType())){
                    s.setFormerAgencyFee(s.getAgencyFee());
                }else{
                    s.setAgencyFee(agencyFee);
                }

            }else{
                //查询流水记录
                if(StringUtils.isNotBlank(s.getSplitIds())){
                    String[] splitIds = s.getSplitIds().split(",");
                    List<BondWater> waters = bondWaterService.getWaterGroupByAccount(splitIds);
                    for(BondWater water:waters){
                        BondRefundDetailsVo vo = new BondRefundDetailsVo();
                        vo.setBondMoney(new BigDecimal(water.getAmount()));
                        bondMoney = bondMoney.add(new BigDecimal(water.getAmount()));
                        vo.setRefundCompanyName(water.getCompanyName());
                        vo.setRefundNumber(water.getCompanyAccount());
                        vo.setRefundOpenBank(water.getCompanyBankDeposit());
                        vo.setAmountDate(water.getDate());
                        vo.setFromOpenBank(water.getReceiveBank());
                        vo.setFromBankNumber(water.getReceiveAcount());
                        vo.setRefundBankCode(water.getCompanyBankCode());
                        detailsVos.add(vo);
                    }
                }
                s.setBondMoney(bondMoney);
            }
            s.setBondRefundDetails(detailsVos);
            // 封装异常关联状态数据
            if (s.getRelationStatus().equals(RelationStatusEnum.EXCEPTION_RELATE.getType()) && s.getWaterIds() != null) {
                // 查询异常关联的所有包
                List<Long> waterIds = Arrays.asList(s.getWaterIds().split(",")).stream()
                        .map(m -> {
                            return Long.parseLong(m);
                        }).collect(Collectors.toList());
                List<BondRelationSectionVo> relationSectionVoList = bondRelationService.getSectionListByWaterIds(waterIds, RelationStatusEnum.EXCEPTION_RELATE.getType());
                List<Long> sectionIds = relationSectionVoList.stream()
                        .map(m -> {
                            return m.getId();
                        }).collect(Collectors.toList());
                if(!relationSectionVoList.isEmpty()) {
                    BondApplyRelation applyRelation = bondApplyRelationService.getOne(new LambdaQueryWrapper<BondApplyRelation>().eq(BondApplyRelation::getType, ApplyTypeEnum.RELATION_EXCEPTION.getType())
                            .eq(BondApplyRelation::getCompanyId, s.getCompanyId()).in(BondApplyRelation::getSectionId, sectionIds).orderByDesc(BaseBean::getCreatedTime)
                            .last("limit 1"));
                    s.setApplyRelationStatus(applyRelation.getStatus());
                }
            }
            // 未关联时判断申请关联
            if (s.getRelationStatus().equals(RelationStatusEnum.NOT_RELATE.getType())) {
                BondApplyRelation applyRelation = bondApplyRelationService.getOne(new LambdaQueryWrapper<BondApplyRelation>().eq(BondApplyRelation::getType, ApplyTypeEnum.RELATION_APPLY.getType())
                        .eq(BondApplyRelation::getCompanyId, s.getCompanyId()).eq(BondApplyRelation::getSectionId, s.getSectionId()).orderByDesc(BaseBean::getCreatedTime)
                        .last("limit 1"));
                if (applyRelation != null) {
                    s.setApplyRelationStatus(applyRelation.getStatus());
                    //申请关联待确认，关联状态设置为5
                    if (ApplyRelationStatusEnum.WAIT_HANDLE.getType().equals(applyRelation.getStatus())) {
                        s.setRelationStatus(RelationStatusEnum.APPLY_RELATION_WAITE.getType());
                    }
                }
            }
            SpecialTicketVo specialTicket = specialTicketService.getSpecialTicketInfo( s.getCompanyId(), s.getSectionId());
            s.setSpecialTicket(specialTicket);
            //若为中标人/中标候选人且中标通知书未盖章，且无备注，则默认备注为“未开中标通知书”
            BidWinSupplierVo bidWinInfo = s.getBidWinInfo();
            if (projectBidSectionStatus < PackageStatusEnum.SUBMITTING_WINNER_NOTICE.getValue() &&
                    StringUtils.isEmpty(s.getBondRemark()) &&
                    (BidTypeEnum.BID_CANDIDATE.getType().equals(bidWinInfo.getBidType()) ||
                            BidTypeEnum.BID_WIN_PEOPLE.getType().equals(bidWinInfo.getBidType()) ||
                            BidTypeEnum.BID_WINED.getType().equals(bidWinInfo.getBidType()) ||
                            BidTypeEnum.SIGNED.getType().equals(bidWinInfo.getBidType()) ||
                            BidTypeEnum.NO_SIGN.getType().equals(bidWinInfo.getBidType()) )) {
                s.setBondRemark("未开中标通知书");
            }
        });
        //按条件排序
        List<BondSupplierInfoVo> resultVo = new ArrayList<>();
        //未知状态
        List<BondSupplierInfoVo> unknown = bondSupplierInfoVos.stream().filter(x -> BidTypeEnum.UNKNOWN.getType().equals(x.getBidWinInfo().getBidType())).collect(Collectors.toList());
        resultVo.addAll(unknown);
        //无中标人
        List<BondSupplierInfoVo> nobidWin = bondSupplierInfoVos.stream().filter(x -> BidTypeEnum.NO_WIN_PEOPLE.getType().equals(x.getBidWinInfo().getBidType())).collect(Collectors.toList());
        resultVo.addAll(nobidWin);
        //非中标候选人
        List<BondSupplierInfoVo> noCandidate = bondSupplierInfoVos.stream().filter(x -> BidTypeEnum.NOT_BID_CANDIDATE.getType().equals(x.getBidWinInfo().getBidType())).collect(Collectors.toList());
        resultVo.addAll(noCandidate);
        //中标候选人
        List<BondSupplierInfoVo> candidate = bondSupplierInfoVos.stream().filter(x -> BidTypeEnum.BID_CANDIDATE.getType().equals(x.getBidWinInfo().getBidType())).collect(Collectors.toList());
        //中标候选人中的未中标数据
        resultVo.addAll(candidate.stream().filter(x -> !BidTypeEnum.BID_WINED.getType().equals(x.getBidWinInfo().getBidStatus())).collect(Collectors.toList()));
        //中标候选人中的已中标数据
        resultVo.addAll( candidate.stream().filter(x -> BidTypeEnum.BID_WINED.getType().equals(x.getBidWinInfo().getBidStatus())).collect(Collectors.toList()));
        //中标人
        List<BondSupplierInfoVo> bidWinPeople = bondSupplierInfoVos.stream().filter(x -> BidTypeEnum.BID_WIN_PEOPLE.getType().equals(x.getBidWinInfo().getBidType())).collect(Collectors.toList());
        resultVo.addAll(bidWinPeople);


        List<BondSupplierInfoVo> resultVo1 = new ArrayList<>();
        //退还失败的
        List<BondSupplierInfoVo> refundFails = resultVo.stream().filter(x -> BondRefundStatusEnum.FAIL.getType().equals(x.getRefundStatus())).collect(Collectors.toList());
        resultVo1.addAll(refundFails);
        List<BondSupplierInfoVo> refundIng = resultVo.stream().filter(x -> !BondRefundStatusEnum.FAIL.getType().equals(x.getRefundStatus()) && !BondRefundStatusEnum.SUCCESS.getType().equals(x.getRefundStatus())).collect(Collectors.toList());
        resultVo1.addAll(refundIng);
        //退还成功
        List<BondSupplierInfoVo> refundSuccess = resultVo.stream().filter(x -> BondRefundStatusEnum.SUCCESS.getType().equals(x.getRefundStatus())).collect(Collectors.toList());
        // 退还成功数据加入退还时间数据
        ProcessRecordReq processRecordReq = new ProcessRecordReq();
        processRecordReq.setBusinessCode(FlowClientConstant.BOND_APPLY_REFUND);
        if(null != refundSuccess && refundSuccess.size() > 0){
            for (BondSupplierInfoVo success : refundSuccess) {
                processRecordReq.setBusinessId(success.getRefundId().toString());
                success.setReturnTime(processRecordService.queryEndTime(processRecordReq));
            }
        }
        resultVo1.addAll(refundSuccess);
        //查询下一步审批角色，财务经办岗才能有撤回按钮
        for(BondSupplierInfoVo vo:resultVo1){
            if(null!=vo.getRefundProcessCode()){
                String taskRole = flowApiClient.getTaskRoleByProcessInstanceId(null, vo.getRefundProcessCode());
                JSONObject taskRoleJsonObject = JSON.parseObject(taskRole);
                List<TaskRoleVo> taskRoleVoList = JSONObject.parseArray(taskRoleJsonObject.getString("data"), TaskRoleVo.class);
                if(CollectionUtils.isNotEmpty(taskRoleVoList)){
                    if (null != taskRoleVoList.get(0).getRoleCode() && taskRoleVoList.get(0).getRoleCode().equals(RoleCodeEnum.CWSJCW.getType())){
                        vo.setCwjbFlag(1);
                    }
                }
            }
        }
        return resultVo1;
        }
    }




    /**
     * 鼠标悬停查看供应商中标情况
     * @param sectionId companyId
     * @return
     */
    @Override
    public BidWinSupplierVo getBidWinInfo(Long sectionId, Long companyId) {
        BidWinSupplierVo  bondSupplierVO = new BidWinSupplierVo();
        //是否是中标人（中标通知书种确认的中标人）
        Boolean iswin = false;
        //评标情况
        String bidType = "";
        //中标情况
        String bidStatus = "";
        //确认中标人
        List<BondSupplierVO> bidWinPeople = bondMapper.findBidWinPeopleSuppliers(sectionId,companyId);
        if(bidWinPeople == null || bidWinPeople.size()<=0){
            //查看在不在初审通过的里面，在初审通过的供应商里面，展示非中标候选人，不在里面展示-
            LambdaQueryWrapper<BidTendererRecord> firstQuery = new LambdaQueryWrapper<>();
            firstQuery.eq(BidTendererRecord::getSectionId,sectionId)
                    //.eq(BidTendererRecord::getFirstTrial, CommonConstants.YES)
                    .eq(BidTendererRecord::getSignedIn,CommonConstants.YES)
                    .eq(BidTendererRecord::getTendererId,companyId);
            long hasFirst = bidTendererRecordService.count(firstQuery);
            if(hasFirst>0){
                bidType = BidTypeEnum.NOT_BID_CANDIDATE.getType();
            }else{
                bidType = BidTypeEnum.UNKNOWN.getType();
            }
            //查询是否确认中标人中都没有数据
            List<BondSupplierVO> allPeoples = bondMapper.findBidWinPeopleSuppliers(sectionId,null);
            if(allPeoples == null || allPeoples.size()<1){
                bidType = BidTypeEnum.UNKNOWN.getType();
            }
        }else{
            if(BidTypeEnum.NO_WIN_PEOPLE.getType().equals(bidWinPeople.get(0).getWinBidType())){
                bidType = BidTypeEnum.NOT_BID_CANDIDATE.getType();
            }else{
                bidType = bidWinPeople.get(0).getWinBidType();
            }
        }

        bondSupplierVO.setBidType(bidType);

        List<BondSupplierVO> peoples = new ArrayList<>();
        //查询已经盖章的中标通知书
        List<BondSupplierVO> notice = bondMapper.findBidWinNotcieSuppliers(sectionId);
        for(BondSupplierVO n:notice){
            if(BidWinTypeEnum.BID_TYPE_NO_WIN.getType().toString().equals(n.getWinBidType())){
                n.setWinBidType(BidTypeEnum.NO_WIN_PEOPLE.getType());
            }else{
                if(CommonConstants.YES.equals(n.getIsWin())){
                    n.setWinBidType(BidTypeEnum.BID_WIN_PEOPLE.getType());
                    //是中标人并且是当前供应商
                    if(n.getSupplierId().equals(companyId)){
                        iswin = true;
                    }
                }else{
                    n.setWinBidType(BidTypeEnum.BID_CANDIDATE.getType());
                }
            }
        }
        peoples = notice;
        if(notice == null || notice.size()<1){
            //查询已发布中标结果公示
            List<BondSupplierVO> result = bondMapper.findBidWinResultSuppliers(sectionId);
            peoples = result;
            if(result == null || result.size()<1){
                //查询已发布中标候选人公示
                List<BondSupplierVO> bulletin = bondMapper.findBidWinBulletinSuppliers(sectionId);
                peoples = bulletin;
                if(bulletin == null || bulletin.size()<1){
                    //查询已发布确认中标人
                    List<BondSupplierVO> bidPeople = bondMapper.findBidWinPeopleSuppliers(sectionId,null);
                    peoples = bidPeople;
                }
            }
        }

        for(BondSupplierVO p :peoples){
            //如果供应商为中标人，则在中标通知书发放30天后展示‘已签订’，30天内展示‘未签订’；不是中标人展示‘-’
            if(iswin){
                if(p.getSupplierId().equals(companyId)){
                    //中标情况
                    bondSupplierVO.setBidStatus(BidTypeEnum.BID_WINED.getType());
                    //中标价
                    if(p.getBidMoney() != null && !p.getBidMoney().toString().equals("0")){
                        bondSupplierVO.setBondMoney(p.getBidMoney());
                    }
                    bondSupplierVO.setBidType(BidTypeEnum.BID_WIN_PEOPLE.getType());
                }

                //查询发动中标通知书
                LambdaQueryWrapper<BidNoticeSendRecord> sendWrapper = new LambdaQueryWrapper<>();
                sendWrapper.eq(BidNoticeSendRecord::getSectionId,sectionId).last("limit 1");
                BidNoticeSendRecord sendRecord = bidNoticeSendRecordService.getOne(sendWrapper);
                if(sendRecord != null) {
                    Date sendTime = sendRecord.getCreatedTime();
                    //提交(修改)时间<截止时间
                    int day = DateUtils.differentDaysByMillisecond(sendTime, new Date());
                    if (day < 30) {
                        bondSupplierVO.setIsSign(BidTypeEnum.NO_SIGN.getType());
                    } else {
                        bondSupplierVO.setIsSign(BidTypeEnum.SIGNED.getType());
                    }
                    bondSupplierVO.setSendTime(sendTime);
                }
            }else{
                if(p.getSupplierId().equals(companyId)){
                    bondSupplierVO.setBidType(p.getWinBidType());
                }
                //有中标人的时候，其他人不是中标人的中标情况为未中标，没有中标人的时候，都为-
                if(notice.size()>0){
                    bondSupplierVO.setBidStatus(BidTypeEnum.NOT_BID_WIN.getType());
                }
                bondSupplierVO.setIsSign(BidTypeEnum.UNKNOWN.getType());
            }
        }
        return bondSupplierVO;
    }


    /**
     * 线下项目判断中标信息
     * @param sectionId offlineCompanyId
     * @return
     */
    public BidWinSupplierVo getBidWinInfoOffline(Long sectionId, Long offlineCompanyId) {
        OfflineCompany offlineCompany = offlineCompanyMapper.selectById(offlineCompanyId);
        BidWinSupplierVo  bondSupplierVO = new BidWinSupplierVo();
        //是否是中标人（中标通知书种确认的中标人）
        Boolean iswin = false;
        //评标情况
        String bidType = "";
        //中标情况
        String bidStatus = "";
        //确认中标人
        List<BondSupplierVO> bidWinPeople = bondMapper.findBidWinPeopleSuppliersOffline(sectionId,offlineCompanyId);
        if(bidWinPeople == null || bidWinPeople.size() == 0){
            bidType = BidTypeEnum.UNKNOWN.getType();
            //查询是否确认中标人中都没有数据
            bidType = BidTypeEnum.UNKNOWN.getType();
        }else{
            if(BidTypeEnum.NO_WIN_PEOPLE.getType().equals(bidWinPeople.get(0).getWinBidType())){
                bidType = BidTypeEnum.NOT_BID_CANDIDATE.getType();
            }else{
                bidType = bidWinPeople.get(0).getWinBidType();
            }
        }
        bondSupplierVO.setBidType(bidType);
        List<BondSupplierVO> peoples = new ArrayList<>();
        //查询已经盖章的中标通知书
        List<BondSupplierVO> notice = bondMapper.findBidWinNoticeSuppliersOffline(sectionId);
        for(BondSupplierVO n:notice){
            if(BidWinTypeEnum.BID_TYPE_NO_WIN.getType().toString().equals(n.getWinBidType())){
                n.setWinBidType(BidTypeEnum.NO_WIN_PEOPLE.getType());
            }else{
                if(CommonConstants.YES.equals(n.getIsWin())){
                    n.setWinBidType(BidTypeEnum.BID_WIN_PEOPLE.getType());
                    //是中标人并且是当前供应商
                    if(n.getCompanyName().equals(offlineCompany.getCompanyName())){
                        iswin = true;
                    }
                }else{
                    n.setWinBidType(BidTypeEnum.BID_CANDIDATE.getType());
                }
            }
        }
        peoples = notice;

        for(BondSupplierVO p :peoples){
            //如果供应商为中标人，则在中标通知书发放30天后展示‘已签订’，30天内展示‘未签订’；不是中标人展示‘-’
            if(iswin){
                if(p.getCompanyName().equals(offlineCompany.getCompanyName())){
                    //中标情况
                    bondSupplierVO.setBidStatus(BidTypeEnum.BID_WINED.getType());
                    //中标价
                    if(p.getBidMoney() != null && !p.getBidMoney().equals("0")){
                        bondSupplierVO.setBondMoney(p.getBidMoney());
                    }
                    bondSupplierVO.setBidType(BidTypeEnum.BID_WIN_PEOPLE.getType());
                }

            }else{
                if(p.getCompanyName().equals(offlineCompany.getCompanyName())){
                    bondSupplierVO.setBidType(p.getWinBidType());
                }
                //有中标人的时候，其他人不是中标人的中标情况为未中标，没有中标人的时候，都为-
                if(notice.size()>0){
                    bondSupplierVO.setBidStatus(BidTypeEnum.NOT_BID_WIN.getType());
                }
                bondSupplierVO.setIsSign(BidTypeEnum.UNKNOWN.getType());
            }
        }
        return bondSupplierVO;
    }



    /**
     * 保证金退还验证参数合法性
     * @param req
     * @return
     */
    private Boolean checkBondRefund(BondRefundFeq req){
        List<String> watersList = new ArrayList<>();
        // 校验流水标签能否进行其他流水退还保证金
        if(req.getWaterIds() != null) {
            String[] waters = req.getWaterIds().split(",");
            watersList = Arrays.asList(waters);
            for(String water:watersList){
                if (!bondWaterlabelService.getWaterlabelCheck(Long.parseLong(water))) {
                    return false;
                }
            }
        }else{
            //先不往core通用异常枚举中维护，在saas2.2.0中统一维护
            throw new ParamsNotNullException("流水信息不能为空");
        }

        // ProjectBidSection section = projectBidSectionService.getById(req.getSectionId());
        //代理服务费大于保证金金额时，不可选择保证金转代理服务费
     /*   if(BondAgencyFeeTypeEnum.BOND_TO_AGENCY.getType().equals(req.getAgencyFeeType())
                && req.getAgencyFee().doubleValue()>req.getBondMoney().doubleValue()
        ){
            throw new ParamsNotNullException("代理服务费大于保证金金额时，不可选择保证金转代理服务费");
        }
        //代理服务费小于保证金金额时，不可选择保证金转代理服务费并补差额
        if(BondAgencyFeeTypeEnum.BOND_TO_AGENCY_SUPPLEMENT.getType().equals(req.getAgencyFeeType())
                && req.getAgencyFee().doubleValue()<req.getBondMoney().doubleValue()
        ){
            throw new ParamsNotNullException("代理服务费小于保证金金额时，不可选择保证金转代理服务费并补差额");
        }*/

        ProjectBidSection section = null;

        if(req.getSectionId() != null){
            //验证线下是否存在退还记录
            List<Long> sectionIds = new ArrayList<>();
            sectionIds.add(req.getSectionId());
            if(commonOpenService.checkOnlineBondRefund(sectionIds,req.getCompanyId())){
                throw new SunFlowerException(ExceptionEnum.HAVE_OFFLINE_BOND_REFUND, ExceptionEnum.HAVE_OFFLINE_BOND_REFUND.getMessage());
            }

            //判断标段是否合法
            section = projectBidSectionService.getById(req.getSectionId());
            if(section == null){
                //判断标段是否合法
                throw new SunFlowerException(ExceptionEnum.SECTION_NOT_FOUND, ExceptionEnum.SECTION_NOT_FOUND.getMessage());
            }
        }

        //D-027取消供应商校验
//        if(req.getCompanyId() != null){
//            Company company = companyService.getById(req.getCompanyId());
//            if(company == null){
//                //先不往core通用异常枚举中维护，在saas2.2.0中统一维护
//                throw new ParamsNotNullException("供应商企业信息不存在");
//            }
//        }

        BondRefund refund = null;
        BigDecimal allMoney = new BigDecimal("0");
        if(section != null && req.getCompanyId() != null ) {
            //查询标段+供应商信息是否在退回表中存在
            LambdaQueryWrapper<BondRefund> refundLambdaQueryWrapper = new LambdaQueryWrapper<>();
            refundLambdaQueryWrapper.eq(BondRefund::getSectionId, req.getSectionId())
                    .eq(BondRefund::getCompanyId, req.getCompanyId());
            refund = getOne(refundLambdaQueryWrapper);
            if (refund != null) {
                //收取并支持再次收取的时候，支持状态不等于支持成功的，拦截
                if (RefundModeEnum.RETAIN_SURPLUS.getType().equals(refund.getRefundMode())) {
                    if (!BondRefundStatusEnum.SUCCESS.getType().equals(refund.getStatus()) &&
                            !BondRefundStatusEnum.RETURNED.getType().equals(refund.getStatus())
                            && !BondRefundStatusEnum.WITHDRAWED.getType().equals(refund.getStatus())) {
                        throw new SunFlowerException(ExceptionEnum.WATER_IN_RETURN, ExceptionEnum.WATER_IN_RETURN.getMessage());
                    }
                } else {
                    if (!BondRefundStatusEnum.RETURNED.getType().equals(refund.getStatus())
                            && !BondRefundStatusEnum.WITHDRAWED.getType().equals(refund.getStatus())) {
                        //其他情况，拦截
                        throw new SunFlowerException(ExceptionEnum.WATER_IN_RETURN, ExceptionEnum.WATER_IN_RETURN.getMessage());
                    }
                }
                req.setId(refund.getId());
            }
            //判断该数据是否已经关联和拆分
            LambdaQueryWrapper<BondRelation> relationQuery = new LambdaQueryWrapper<>();
            relationQuery.eq(BondRelation::getCompanyId, req.getCompanyId())
                    .eq(BondRelation::getSectionId, req.getSectionId());
            List<BondRelation> relationList = bondRelationService.list(relationQuery);

            LambdaQueryWrapper<BondSplit> splitQuery = new LambdaQueryWrapper<>();
            splitQuery.eq(BondSplit::getCompanyId, req.getCompanyId())
                    .eq(BondSplit::getSectionId, req.getSectionId());
            List<BondSplit> splitList = bondSplitService.list(splitQuery);
            if (relationList.isEmpty() || splitList.isEmpty()) {
                //先不往core通用异常枚举中维护，在saas2.2.0中统一维护
                throw new ParamsNotNullException("该标段的供应商还未进行关联操作，不允许退还保证金!");
            } else {
                int count = 0;
                for (BondRelation r : relationList) {
                    for (String w : watersList) {
                        if (w.equals(r.getWaterId().toString())) {
                            count++;
                        }
                    }
                }
                if (count != watersList.size()) {
                    throw new ParamsNotNullException("该流水与提交关联的流水不一致，请检查流水的是否正确!");
                }

                for (BondSplit s : splitList) {
                    allMoney = allMoney.add(s.getAmount());
                }
            }

            //查询是否是中标人
          /*  BidWinSupplierVo bidWinInfo = getBidWinInfo(req.getSectionId(), req.getCompanyId());
            if(BidTypeEnum.BID_WINED.getType().equals(bidWinInfo.getBidStatus())
            ){
                //标段中的代理费为收取的时候  且 为向投标人收取的时候
                if(CommonConstants.YES.equals(section.getAgencyCostFree()) && CommonConstants.NO.equals(section.getAgencyFeeObj())){
                    //代理服务费类型
                    if(!BondAgencyFeeTypeEnum.BOND_TO_AGENCY.getType().equals(req.getAgencyFeeType())
                            && !BondAgencyFeeTypeEnum.BOND_TO_AGENCY_SUPPLEMENT.getType().equals(req.getAgencyFeeType())
                            && !BondAgencyFeeTypeEnum.BOND_REFUND.getType().equals(req.getAgencyFeeType())){
                        throw new ParamsNotNullException("请检查代理服务费收取方式(1：保证金转代理服务费 2：保证金全额转代理服务费并补差价 3：保证金全额退还，代理服务费另汇)是否合法");
                    }
                }
            }else{
                //非中标人 存在代理服务费
                if((req.getAgencyFee() != null && req.getAgencyFee().doubleValue() != 0)
                    || (req.getFormerAgencyFee() != null && req.getFormerAgencyFee().doubleValue() != 0)){
                    throw new ParamsNotNullException("该供应商是非中标人，不允许收取代理服务费!");
                }
            }
        }*/

            if (req.getWaterId() != null) {
                Integer waterCount = this.baseMapper.checkWatersInRefund(req.getWaterId(), req.getId());
                if (waterCount > 0) {
                    throw new SunFlowerException(ExceptionEnum.WATER_IN_RETURN, ExceptionEnum.WATER_IN_RETURN.getMessage());
                }
            }

            if (req.getApplyRefundId() != null) {
                BondApplyRefund applyRefund = bondApplyRefundService.getById(req.getApplyRefundId());
                if (applyRefund == null) {
                    //先不往core通用异常枚举中维护，在saas2.2.0中统一维护
                    throw new ParamsNotNullException("该未关联流水还未申请退还,不允许退还保证金!");
                } else {
                    if (!req.getWaterId().equals(applyRefund.getWaterId())) {
                        throw new ParamsNotNullException("该流水与未关联申请退还中的流水不一致，请检查流水的是否正确!");
                    }
                }
            }

       /* if(req.getBondRefundDetails().isEmpty()){
            throw new ParamsNotNullException("保证金退还详情数据为空!");
        }else{
            BigDecimal allDetailsTotal = req.getBondRefundDetails().stream().map(i -> {
                if (i.getBondMoney() == null) {
                    return BigDecimal.ZERO;
                } else {
                    return i.getBondMoney();
                }
            }).reduce(BigDecimal.ZERO, BigDecimal::add);

            //验证主表金额和子表金额是否一致
            if(allDetailsTotal.doubleValue() != req.getBondMoney().doubleValue()){
                throw new ParamsNotNullException("保证金退还金额不一致，请检查金额是否正确!");
            }
        }
*/

            //退还方式 1：收取并退还剩余保证金  2：收取并保留剩余保证金
      /*  if(req.getRefundMode() == null){
            throw new ParamsNotNullException("保证金退还方式不能为空!");
        }else {
            //分批次退还
            if (!RefundModeEnum.RETAIN_SURPLUS.getType().equals(req.getRefundMode())
                    && !RefundModeEnum.REFUND_SURPLUS.getType().equals(req.getRefundMode())) {
                throw new ParamsNotNullException("请检查保证金退还方式( 1：收取并退还剩余保证金  2：收取并保留剩余保证金)是否合法");
            }


            //不等于分批次的时候，验证流水金额
            if (!RefundModeEnum.RETAIN_SURPLUS.getType().equals(req.getRefundMode())) {
                if(refund == null || RefundModeEnum.REFUND_SURPLUS.getType().equals(refund.getRefundMode())){
                    if (req.getWaterId() != null) {
                            BondWater water = bondWaterService.getById(req.getWaterId());
                            if (water != null) {
                                BigDecimal waterAmount = new BigDecimal(water.getAmount());
                                if (waterAmount.doubleValue() != req.getBondMoney().doubleValue()) {
                                    throw new ParamsNotNullException("请检查保证金金额和流水金额是否一致!");
                                }
                            } else {
                                throw new ParamsNotNullException("流水信息不存在!");
                            }
                        } else {
                            if (allMoney.doubleValue() != req.getBondMoney().doubleValue()) {
                                throw new ParamsNotNullException("请检查保证金金额和流水金额是否一致!");
                            }
                        }
                    }
                }
        }*/


            //退还类型：1 ：项目经理申请退还  2：异常保证金退还  3：供应商申请退还
      /*  if(req.getRefundType() == null){
            throw new ParamsNotNullException("保证金退还类型不能为空!");
        }else{
            if(!RefundTypeEnum.PROJECT_MANAGER_REFUND.getType().equals(req.getRefundType())
                && !RefundTypeEnum.ABNORMAL_REFUND.getType().equals(req.getRefundType())
                && !RefundTypeEnum.SUPPLIER_REFUND.getType().equals(req.getRefundType())){
                throw new ParamsNotNullException("请检查保证金退还类型(1：项目经理申请退还 2：异常保证金退还 3：供应商申请退还)是否合法");
            }
        }*/
        }
        return true;
    }

    @Override
    public Boolean saveApplyRefund(BondRefundFeq req) {
        Boolean result = true;
        if(req.getWaterId() != null){
            req.setWaterIds(req.getWaterId().toString());
        }
        //校验保证金账户余额是否足够
        List<BondRefundDetails> details = BeanListUtil.convertList(req.getBondRefundDetails(),BondRefundDetails.class);
        for(BondRefundDetails detail:details){
            if(detail.getBondMoney()!=null&&detail.getBondMoney().compareTo(BigDecimal.ZERO)<0){
                return false;
            }
        }
        if(!bondRefundBalanceService.checkBondBalance(details)){
            return false;
        }
        //验证保证金参数合法性
        if(!checkBondRefund(req)){
            return  false;
        }
        BondRefund applyRefund = BeanListUtil.convert(req,BondRefund.class);
        if (BondAgencyFeeTypeEnum.BOND_TO_AGENCY.getType().equals(req.getAgencyFeeType())) {
            applyRefund.setIsAgencyFee(CommonConstants.NO);
            applyRefund.setRemark("");
            applyRefund.setFileIds("");
        }
        if (req.getIsAgencyFee() == null) {
            applyRefund.setIsAgencyFee(CommonConstants.NO);
        }
        if (CommonConstants.YES.equals(req.getIsAgencyFee())) {
            applyRefund.setRemark("");
        }
        if (CommonConstants.NO2.equals(req.getIsAgencyFee())) {
            applyRefund.setFileIds("");
        }
        applyRefund.setStatus(BondRefundStatusEnum.WAIT.getType());
        applyRefund.setIsCalculation(CommonConstants.YES);
        applyRefund.setApproverRoles("");
        if(req.getSubmitTime() == null){
            applyRefund.setSubmitTime(new Date());
        }
        //修改
        if(req.getId() != null){
            applyRefund.setId(req.getId());
        }
        applyRefund.setRates(new BigDecimal("0"));
        result = saveOrUpdate(applyRefund);

        //先删除后增
        LambdaQueryWrapper<BondRefundDetails> detailsQuery = new LambdaQueryWrapper<>();
        detailsQuery.eq(BondRefundDetails::getRefundId,applyRefund.getId());
        List<BondRefundDetails> historyDetails = bondRefundDetailsService.list(detailsQuery);
        if(!historyDetails.isEmpty()){
            bondRefundDetailsService.removeBatchByIds(historyDetails);
        }
        //插入退还详情表
        for (BondRefundDetails detail : details) {
            detail.setRates(new BigDecimal(0));
            detail.setRefundId(applyRefund.getId());
            detail.setStatus(BondRefundStatusEnum.WAIT.getType());
            BigDecimal refundDetaisMoney = detail.getBondMoney();
            if(detail.getAgencyFee() != null){
                refundDetaisMoney = detail.getBondMoney().subtract(detail.getAgencyFee());
            }
            detail.setRefundMoney(refundDetaisMoney.doubleValue()<0?new BigDecimal(0):refundDetaisMoney);
        }

        //计算退还金额
        calculationRates(applyRefund,details,false);

        bondRefundDetailsService.saveBatch(details);

        updateById(applyRefund);
        //开启流程
        startFlow(applyRefund.getId());

        //如果是退回后申请退还，删除待处理事项表数据
        LambdaQueryWrapper<PendingItem> pendingItemLambdaQueryWrapper = new LambdaQueryWrapper<>();
        pendingItemLambdaQueryWrapper.eq(PendingItem::getBusinessCode, ReturnListEnum.ONLINE_BOND_RETURN.getCode());
        pendingItemLambdaQueryWrapper.eq(PendingItem::getBusinessId,req.getId());
        pendingItemService.remove(pendingItemLambdaQueryWrapper);

        //维护专票数据(允许修改除统一社会信用代码（纳税人识别号）外其他数据)
        if (req.getSpecialTicket() != null && req.getSpecialTicket().getId() != null) {
            SpecialTicket specialTicket = specialTicketService.getById(req.getSpecialTicket().getId());
            specialTicket.setCompanyName(req.getSpecialTicket().getCompanyName());
            specialTicket.setRegisteredAddress(req.getSpecialTicket().getRegisteredAddress());
            specialTicket.setPhone(req.getSpecialTicket().getPhone());
            specialTicket.setBankDeposit(req.getSpecialTicket().getBankDeposit());
            specialTicket.setBankAccount(req.getSpecialTicket().getBankAccount());
            specialTicketService.updateById(specialTicket);
            // 维护用户邮箱
            if (StringUtils.isNotBlank(req.getSpecialTicket().getEmail()) && null != req.getSpecialTicket().getUserId()) {
                User user = new User();
                user.setId(req.getSpecialTicket().getUserId());
                user.setEmail(req.getSpecialTicket().getEmail());
                userService.updateById(user);
            }
        }
        return result;
    }

    /**
     * 退还失败异常列表
     * @param condition
     * @return
     */
    @Override
    public IPage<RefundListVo> payFailList(BondProjectCondition condition) {
        BondRefundCondition convert = BeanListUtil.convert(condition, BondRefundCondition.class);
        convert.setBusinessType(RefundBusinessTypeEnum.ABNORMAL_LIST.getType());
        Page<RefundListVo> page = convert.buildPage();
        IPage<RefundListVo> refundListVoIPage = this.baseMapper.bondRefundList(page, convert);
       /* List<RefundListVo> records = refundListVoIPage.getRecords();
        records.forEach(p->{
            LambdaQueryWrapper<BondRefundDetails> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(BondRefundDetails::getRefundId,p.getRefundId())
                    .eq(BondRefundDetails::getStatus,BondRefundStatusEnum.FAIL.getType());
            p.setBondRefundDetails(bondRefundDetailsService.list(queryWrapper));
        });
        refundListVoIPage.setRecords(records);*/
        return refundListVoIPage;
    }

    /**
     * 编辑行号信息
     * @param req
     * @return
     */
    @Override
    public Boolean saveCompanyBankInfo(List<BondCompanyBankReq> req) {
        Boolean result = false;
        for(BondCompanyBankReq bankReq:req) {
            //根据南京银行给到的库判断正误，（如无南京银行库或无法查询到则不做校验）
            LambdaQueryWrapper<CnapsBankCode> cnapsBankCodeLambdaQueryWrapper = new LambdaQueryWrapper<>();
            cnapsBankCodeLambdaQueryWrapper.eq(CnapsBankCode::getOrgFullname,bankReq.getCompanyBankDeposit()).or().eq(CnapsBankCode::getBankCode,bankReq.getCompanyBankCode());
            List<CnapsBankCode> cnapsBankCodeList = cnapsBankCodeService.list(cnapsBankCodeLambdaQueryWrapper);
            if (cnapsBankCodeList.size() == 1) {
                CnapsBankCode cnapsBankCode = cnapsBankCodeList.get(0);
                if (!cnapsBankCode.getOrgFullname().equals(bankReq.getCompanyBankDeposit()) || !cnapsBankCode.getBankCode().equals(bankReq.getCompanyBankCode())) {
                    throw new SunFlowerException(ExceptionEnum.ACCOUNT_BANK_NUMBER_ERROR, ExceptionEnum.ACCOUNT_BANK_NUMBER_ERROR.getMessage());
                }
            }
            if (cnapsBankCodeList.size() > 1) {
                throw new SunFlowerException(ExceptionEnum.ACCOUNT_BANK_NUMBER_ERROR, ExceptionEnum.ACCOUNT_BANK_NUMBER_ERROR.getMessage());
            }
            //1.编辑银行行号
            BondCompanyBank bank = BeanListUtil.convert(bankReq, BondCompanyBank.class);
            result = bondCompanyBankService.updateById(bank);

            //修改明细表
            LambdaQueryWrapper<BondRefundDetails> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(BondRefundDetails::getRefundId,req.get(0).getRefundId())
                        .eq(BondRefundDetails::getRefundCompanyName,bankReq.getCompanyName())
                        .eq(BondRefundDetails::getRefundNumber,bankReq.getCompanyAccount())
                        .and(
                                w->w.eq(BondRefundDetails::getStatus,BondRefundStatusEnum.FAIL.getType())
                                        .or(p->p.eq(BondRefundDetails::getAgencyRefundStatus,BondRefundStatusEnum.FAIL.getType()))
                        );
            List<BondRefundDetails> list = bondRefundDetailsService.list(queryWrapper);
            list.forEach(d -> {
                if(BondRefundStatusEnum.FAIL.getType().equals(d.getStatus())) {
                    d.setStatus(BondRefundStatusEnum.WAIT.getType());
                }
                if(BondRefundStatusEnum.FAIL.getType().equals(d.getAgencyRefundStatus())) {
                    d.setAgencyRefundStatus(BondRefundStatusEnum.WAIT.getType());
                }
                d.setRefundOpenBank(bankReq.getCompanyBankDeposit());
               // d.setRefundBankCode(bankReq.getCompanyBankCode());
                d.setFailReason("");
            });
            bondRefundDetailsService.updateBatchById(list);
        }

        //2.修改退还状态为带财务审批，重新开启审批流
        BondRefund applyRefund = getById(req.get(0).getRefundId());
        applyRefund.setStatus(BondRefundStatusEnum.WAIT.getType());
        applyRefund.setIsAbnormal(CommonConstants.NO2);
        this.updateById(applyRefund);

        //重新开启流程引擎
        startFlow(req.get(0).getRefundId());

        return result;
    }

    @Override
    public Result<String> complementBankInfo(List<BondCompanyBankReq> req) {
        for(BondCompanyBankReq bankReq:req) {
            //根据南京银行给到的库判断正误，（如无南京银行库或无法查询到则不做校验）
            LambdaQueryWrapper<CnapsBankCode> bankCodeLambdaQueryWrapper = new LambdaQueryWrapper<>();
            bankCodeLambdaQueryWrapper.eq(CnapsBankCode::getOrgFullname,bankReq.getCompanyBankDeposit()).or().eq(CnapsBankCode::getBankCode,bankReq.getCompanyBankCode());
            List<CnapsBankCode> bankCodeList = cnapsBankCodeService.list(bankCodeLambdaQueryWrapper);
            if (bankCodeList.size() == 1) {
                CnapsBankCode cnapsBankCode = bankCodeList.get(0);
                if (!cnapsBankCode.getOrgFullname().equals(bankReq.getCompanyBankDeposit()) || !cnapsBankCode.getBankCode().equals(bankReq.getCompanyBankCode())) {
                    return Result.failed("供应商行名信息校验不通过");
                }
            }
            if (bankCodeList.size() > 1) {
                return Result.failed("供应商行名信息重复");
            }
            //1.编辑银行行号
            LambdaUpdateWrapper<BondCompanyBank> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.set(BondCompanyBank::getCompanyBankDeposit, bankReq.getCompanyBankDeposit())
                    .set(BondCompanyBank::getCompanyBankCode, bankReq.getCompanyBankCode())
                    .eq(BondCompanyBank::getCompanyId, bankReq.getCompanyId());
            bondCompanyBankService.update(updateWrapper);
        }
        return Result.ok();
    }


    /**
     * 财务退还列表
     *    keywords "关键字：项目编号/项目名称/供应商名称"
     *    applyTime "申请时间"
     *    bankName
     *    type "0：全部 1：待处理 2：已处理")
     * @return
     */
    @Override
    public IPage<RefundListVo> bondRefundList(BondRefundCondition condition) {
        IPage<RefundListVo> page = condition.buildPage();

        //封装流程引擎查询参数
        AppingTaskREQ appingTaskREQ = new AppingTaskREQ();
        appingTaskREQ.setPage(1);
        appingTaskREQ.setPageSize(1000);
        appingTaskREQ.setProcessDefinitionKey(FlowClientConstant.BOND_APPLY_REFUND);

        //查询方式 0：全部，1：待处理  2：已处理
        //默认查询待处理
        if(condition.getType() == null){
            condition.setType(1);
        }
        condition.setBusinessType(RefundBusinessTypeEnum.EXAMINE_LIST.getType());
        Map<String,AppingTaskVO> map = new HashMap<>();


        //查询待审批的流程数据
        Paging<AppingTaskVO> paddingList = workflowCacheService.getPaddingList(appingTaskREQ, LoginUtil.getJwtUser().getUserOtherId());
        List<AppingTaskVO> tasks = paddingList.getRecords();
        List<String> taskCodes = new ArrayList<>();
        //封装流程code集合
        tasks.forEach(p->{
            taskCodes.add(p.getProcessInstanceId());
            map.put(p.getProcessInstanceId(), p);
        });
        Long[] roleIds = Object2OtherUtil.Object2Long(LoginUtil.getJwtUser().getUser().getRoles().stream().map(Role::getId).collect(Collectors.toList()).toArray());
        switch (condition.getType()){
            //全部
            case 0:
               // condition.setTaskCodes(taskCodes);
                condition.setRoleId(roleIds);
                page = getPageBondRefundList(page,condition);
                page.getRecords().forEach(p->{
                    String refundProcessCode = p.getRefundProcessCode();
                    AppingTaskVO refundTask = map.get(refundProcessCode);
                    //退款按钮
                    if(refundTask != null){
                        p.setRefundTask(refundTask);
                    }
                });
                break;
            //待处理
            case 1:
                //根据流程code集合，查询业务数据
                condition.setTaskCodes(taskCodes);
                page = getPageBondRefundList(page,condition);
                page.getRecords().forEach(p->{
                    String refundProcessCode = p.getRefundProcessCode();
                    AppingTaskVO refundTask = map.get(refundProcessCode);
                    //退款按钮
                    if(refundTask != null){
                        p.setRefundTask(refundTask);
                    }
                });
                break;
                //已处理
            case 2:
                condition.setRoleId(roleIds);
                page = getPageBondRefundList(page,condition);
                break;
        }
        return page;
    }

    @Override
    public IPage<BankRateListVo> bankRateList(BankRateCondition condition) {
        IPage<BankRateListVo> page = condition.buildPage();
        page.setRecords(this.baseMapper.bankRateList(page, condition));
        return page;
    }

    @Override
    public Result<List<BankRateHistoryListVo>> bankRateHistoryList(Integer bankType) {
        //查询历史记录
        LambdaQueryWrapper<BondRate> bondRateQuery = new LambdaQueryWrapper<>();
        bondRateQuery.eq(BondRate::getBankType,bankType);
        bondRateQuery.orderByDesc(BondRate::getEffectDate);
        List<BondRate> bondRateList = bondRateService.list(bondRateQuery);
        List<BankRateHistoryListVo> list = new ArrayList<>();
        for(BondRate rate:bondRateList){
            BankRateHistoryListVo vo = new BankRateHistoryListVo();
            vo.setRate(rate.getRate());
            if(null!=rate.getBeforeDate()){
                vo.setBeforeDate(DateUtils.dateTime(rate.getBeforeDate()));
            }
            vo.setEffectDate(DateUtils.dateTime(rate.getEffectDate()));
            list.add(vo);
        }
        return Result.ok(list);
    }

    /**
     * 审核
     * @param req
     * @param jwtUser
     * @return
     */
    @Override
    public Boolean updateExamineForRefund(ExamineReq req, JwtUser jwtUser) {
        Boolean result = false;
        String operation = "";
        Boolean isAgree = false;
        List<Role> roles = jwtUser.getUser().getRoles();
        //同意
        if (req.getOperation().equals("1")) {
            operation = "同意";
            isAgree = true;
        } else {
            operation = "退回";
        }
        BondRefund bondRefund = getById(req.getId());
        if(!bondRefund.getStatus().equals(BondRefundStatusEnum.WAIT.getType())){
            throw new ParamsNotNullException("该记录已经处理过!");
        }

        //查询明细记录
        LambdaQueryWrapper<BondRefundDetails> detailsQuery = new LambdaQueryWrapper<>();
        detailsQuery.eq(BondRefundDetails::getRefundId,bondRefund.getId());
        List<BondRefundDetails> details = bondRefundDetailsService.list(detailsQuery);
        //判断账户中余额是否足够
//        if(!bondRefundBalanceService.checkBondBalance(details)){
//            throw new ParamsNotNullException("保证金账户余额不足!");
//        }
        ProcessRecordDTO recordDTO = new ProcessRecordDTO();
        recordDTO.setBusinessId(bondRefund.getId());
        recordDTO.setOperation(operation);
        recordDTO.setOperatorId(jwtUser.getUserId());
        recordDTO.setOperatorName(jwtUser.getUserName());
        recordDTO.setRemark(req.getMessage());
        //拒绝后 状态为已退回
        if (req.getOperation().equals("2")) {
            //退回后删除已经推送到ncc的凭证
            //删除保证金凭证
            removeNcc(NccConstants.NCC_REFUND_KEY+bondRefund.getId());
            //删除代理服务费凭证
            if(bondRefund.getAgencyFee().doubleValue() != 0){
                removeNcc(NccConstants.NCC_AGENCY_FEE_KEY+bondRefund.getId());
            }
            bondRefund.setStatus(BondRefundStatusEnum.RETURNED.getType());
            //退回后，判断是否是供应商申请退还的数据，如果是，则删除,申请数据打回
            if(bondRefund.getRefundType().equals(RefundTypeEnum.SUPPLIER_REFUND.getType())){
                BondApplyRefund bondApplyRefund = new BondApplyRefund();
                bondApplyRefund.setId(bondRefund.getApplyRefundId());
                bondApplyRefund.setStatus(BondApplyRefundStatus.RETURNED.getType());
                bondApplyRefundService.updateById(bondApplyRefund);

               bondRefund.setIsDelete(CommonConstants.YES);
            }
            bondRefund.setRates(new BigDecimal(0));
            bondRefund.setRefundMoney(new BigDecimal(0));
            //拒绝后恢复计算后的值
            recoveryBondMondy(bondRefund,details);

            //拒绝后添加待处理事项表
            //查询保证金退回数据
            BondRefund refund = bondRefundMapper.selectById(req.getId());
            if (refund.getSectionId() != null) {
                PendingItem pendingItem = new PendingItem();
                pendingItem.setBusinessCode(ReturnListEnum.ONLINE_BOND_RETURN.getCode());
                pendingItem.setBusinessId(req.getId());
                pendingItem.setOperationTime(new Date());
                pendingItem.setApplyTime(refund.getSubmitTime());
                pendingItem.setApplyUserId(refund.getCreatedUserId());
                pendingItem.setBusinessType(2);
                //根据标段id查询项目及标段轮次
                ProjectBidSection projectBidSection = projectBidSectionService.getById(refund.getSectionId());
                pendingItem.setProjectId(projectBidSection.getProjectId());
                pendingItem.setSectionId(refund.getSectionId().toString());
                pendingItem.setBidRound(projectBidSection.getBidRound());
                pendingItemService.save(pendingItem);
            }

        }else{
            //先删除流程角色表数据
            LambdaQueryWrapper<ProcessRole> processRoleQuery = new LambdaQueryWrapper<>();
            processRoleQuery.eq(ProcessRole::getBusinessId,bondRefund.getId())
                    .eq(ProcessRole::getBusinessCode,FlowClientConstant.BOND_APPLY_REFUND);
            processRoleService.remove(processRoleQuery);

            if(!roles.isEmpty()){
                roles.forEach(r->{
                    //插入流程角色表
                    ProcessRole processRole = new ProcessRole();
                    processRole.setBusinessCode(FlowClientConstant.BOND_APPLY_REFUND);
                    processRole.setBusinessId(bondRefund.getId());
                    processRole.setRoleId(r.getId());
                    processRoleService.save(processRole);
                });
            }
        }
        recordDTO.setBusinessCode(FlowClientConstant.BOND_APPLY_REFUND);

        //流程引擎审批
        String str = flowApiClient.review(jwtUser.getUserOtherId(), req.getProcessInstanceId() + "", req.getTaskId(), req.getMessage(), isAgree,false);
        //判断推送到流程是否成功
        ReturnVo returnVo = JSONObject.parseObject(str, ReturnVo.class);
        WorkFlowExceptionUtil.checkFlowResult(str);

        //插入流程记录
        processRecordService.addProcessRecord(recordDTO,jwtUser.getUserOtherId(),str);
        //经办岗同意的时候，计算利息
        if("同意".equals(operation) && !"end".equals(returnVo.getMsg())){
            //之前算过利息的，下次进来不需要重新算利息（转异常又回来）
            if (CommonConstants.YES.equals(bondRefund.getIsCalculation())) {
                calculationRates(bondRefund, details,true);
            }
            //往行号维护表中增加信息
            for(BondRefundDetails d:details) {
                addCompanyBankInfo(bondRefund.getCompanyId(), d.getRefundBankCode(), d.getRefundOpenBank(), d.getRefundCompanyName(), d.getRefundNumber());
            }
        }

        if(!roles.isEmpty()){
            List<Long> roleIds = roles.stream().map(r -> r.getId()).collect(Collectors.toList());
            String approverRoles = roleIds.stream()
                    .map(Object::toString)
                    .collect(Collectors.joining(", "));
            if(StringUtils.isBlank(bondRefund.getApproverRoles())){
                bondRefund.setApproverRoles(approverRoles);
            }else{
                bondRefund.setApproverRoles(bondRefund.getApproverRoles()+","+approverRoles);
            }
        }
        result = updateById(bondRefund);
        //退回后，判断是否是供应商申请退还的数据，如果是，则删除,申请数据打回
        if("2".equals(req.getOperation()) && RefundTypeEnum.SUPPLIER_REFUND.getType().equals(bondRefund.getRefundType())){
            //删除退还主表
            this.removeById(bondRefund);
            //删除退还详情表
            LambdaQueryWrapper<BondRefundDetails> detailsDel = new LambdaQueryWrapper<>();
            detailsDel.eq(BondRefundDetails::getRefundId,bondRefund.getId());
            bondRefundDetailsService.remove(detailsDel);
        }
        //同步详细表
        bondRefundDetailsService.updateBatchById(details);

        if("同意".equals(operation) && !"end".equals(returnVo.getMsg())) {
            SimpleDateFormat sfm = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String creationTime = sfm.format(new Date());
            List<SyncNccRefundInfoDto> syncNccRefunds = bondRefundDetailsService.getSyncNccRefundInfo(bondRefund.getId());
            if(syncNccRefunds != null && syncNccRefunds.size()>0) {
                for (SyncNccRefundInfoDto info : syncNccRefunds) {
                    info.setRefundDate(creationTime);
                    if(org.apache.commons.lang3.StringUtils.isBlank(info.getReceiveAcount())){
                        //获取招标中心的保证金账户
                        BankdataBondToNcc bankdataBondToNcc = bankdataBondToNccService.getBankdataBondToNcc(info.getFromOpenBank());
                        if(bankdataBondToNcc != null){
                            info.setReceiveAcount(bankdataBondToNcc.getAccount());
                        }
                    }
                }
            }

            //若是未关联保证金退还，先看有没有推过收款，推过收款推调账
            pushNccReceive(bondRefund);

            //有代理服务费的数据的，不推送保证金单据
            if(bondRefund.getAgencyFee() != null && bondRefund.getAgencyFee().doubleValue() != 0) {
                //经办岗同意后，推送代理服务费数据到ncc
                syncNccAgencyFee(syncNccRefunds, bondRefund.getId(), creationTime);
            }else{
                if(bondRefund.getRefundMoney() != null && bondRefund.getRefundMoney().doubleValue() != 0) {
                    //经办岗同意后，推送保证金退还数据到ncc
                    syncNccRefund(syncNccRefunds, bondRefund.getId(), creationTime);
                }
            }
        }

        if("同意".equals(operation) && "end".equals(returnVo.getMsg())){
            //最后一级审核的时候调用退款接口
            refund(bondRefund,jwtUser);
        }
        return result;
    }

    /**
     * 保证金同意退还的时候，判断有没有收款单据，如果没有，则推送收款单据
     * @param bondRefund
     */
    private void pushNccReceive(BondRefund bondRefund) {
        if(bondRefund.getWaterId() != null){
            saveSplitWaterToPushNcc(bondRefund.getWaterId());
        }
    }

    /**
     * 获取拆分的流水信息推送到ncc
     * @param waterId
     */
    private void saveSplitWaterToPushNcc(Long waterId){
        BondWater water = bondWaterService.getById(waterId);
        BondWaterToNccDto item = BeanListUtil.convert(water,BondWaterToNccDto.class);
        //判断该流水是否是关联后取消关联,是收款其他，否，调账其他
        BondSplitNccDto splitNccDto = bondSplitService.queryLastDeleteByWaterId(waterId);
        if(splitNccDto!=null){
            List<BondSplitDto> historySplitList =  bondSplitService.queryLatestSplit(waterId);
            bondSplitService.saveNccAdjustOtherPushLog(item,historySplitList);
        }else{
            //判断是否隔月，流水隔月必定推过收款
            if(!BondUtils.isOverMonth(water.getDate())){
                bondSplitService.saveNccReceiveOtherPushLog(item);
            }
        }
    }

    @Override
    public Result<BatchRefundRecordVo> batchExamineForRefund(List<ExamineReq> reqList, JwtUser jwtUser) {
        //校验待审核数据
        Result<String> checkDataResult = checkBatchRefundData(reqList);
        if(!checkDataResult.getFlag()){
            return Result.failed(checkDataResult.getMsg());
        }
        //待刷新refundId
        List<Long> refundIdList = new ArrayList<>();
        //保证金退还表
        List<BondRefund> updateBondRefundList = new ArrayList<>();
        //保证金退还详情表
        List<BondRefundDetails> updateBondRefundDetails = new ArrayList<>();
        //流程角色Id
        List<Long> processRoleIds = new ArrayList<>();
        //流程角色
        List<ProcessRole> processRoleList = new ArrayList<>();
        //查询用户角色
        List<Role> roles = jwtUser.getUser().getRoles();
        //角色id
        String approveRoles = "";
        if(CollectionUtils.isNotEmpty(roles)){
            List<Long> roleIds = roles.stream().map(Role::getId).collect(Collectors.toList());
            approveRoles = roleIds.stream().map(Object::toString).collect(Collectors.joining(", "));
        }
        String batch = UUID.randomUUID().toString();
        //记录成功失败条数
        BatchRefundRecordVo batchRefundRecordVo = new BatchRefundRecordVo();
        int successCount = 0;
        int failCount = 0;
        //循环处理
        for(ExamineReq req:reqList){
            try{
                //推送到流程引擎
                ReturnVo returnVo = refundWorkFlow(req,jwtUser);
                //查询退款记录
                BondRefund bondRefund = getById(req.getId());
                //记录角色id
                if(StringUtils.isBlank(bondRefund.getApproverRoles())){
                    bondRefund.setApproverRoles(approveRoles);
                }else{
                    bondRefund.setApproverRoles(bondRefund.getApproverRoles()+","+approveRoles);
                }
                //查询明细记录
                LambdaQueryWrapper<BondRefundDetails> detailsQuery = new LambdaQueryWrapper<>();
                detailsQuery.eq(BondRefundDetails::getRefundId,bondRefund.getId());
                List<BondRefundDetails> details = bondRefundDetailsService.list(detailsQuery);
                processRoleIds.add(bondRefund.getId());
                if(CollectionUtils.isNotEmpty(roles)){
                    roles.forEach(r->{
                        //插入流程角色表
                        ProcessRole processRole = new ProcessRole();
                        processRole.setBusinessCode(FlowClientConstant.BOND_APPLY_REFUND);
                        processRole.setBusinessId(bondRefund.getId());
                        processRole.setRoleId(r.getId());
                        processRoleList.add(processRole);
                    });
                }
                //若为end，表示最后一级审批，若不是end，则表示经办岗审批
                if("end".equals(returnVo.getMsg())){
                    //最后一级审核的时候调用退款接口
                    batchRefund(bondRefund,details);
                    refundIdList.add(bondRefund.getId());
                }else{
                    //经办岗同意的时候，计算利息,之前算过利息的，下次进来不需要重新算利息（转异常又回来）
                    if (CommonConstants.YES.equals(bondRefund.getIsCalculation())) {
                        calculationRates(bondRefund, details,true);
                    }
                    //往行号维护表中增加信息
                    for(BondRefundDetails d:details) {
                        addCompanyBankInfo(bondRefund.getCompanyId(), d.getRefundBankCode(), d.getRefundOpenBank(), d.getRefundCompanyName(), d.getRefundNumber());
                    }
                    //先更新数据，不然后面查的利息为空
                    this.updateById(bondRefund);
                    //同步详细表
                    bondRefundDetailsService.updateBatchById(details);
                    //经办岗同意要推送到ncc
                    pushRefund2Ncc(bondRefund);
                    //推送单据
                    pushNccReceive(bondRefund);
                }
                //保证金退还表
                updateBondRefundList.add(bondRefund);
                //保证金退还详情表
                updateBondRefundDetails.addAll(details);
                successCount++;
            }catch(Exception e){
                log.error("<======批量退还保证金异常：{}======>",e);
                //插入保证金审核失败表
                BondExamineFailRecord failRecord = new BondExamineFailRecord();
                failRecord.setRefundId(req.getId());
                failRecord.setFailReason(e.getMessage());
                failRecord.setBatch(batch);
                bondExamineFailRecordService.save(failRecord);
                failCount++;
            }
        }
        //批量保存流程角色表，先删后插
        saveProcessRoles(processRoleIds,processRoleList);
        //批量更新保证金退还信息
        this.updateBatchById(updateBondRefundList);
        //批量更新保证金退还详情信息
        bondRefundDetailsService.updateBatchById(updateBondRefundDetails);
        //刷新退还状态
        if(CollectionUtils.isNotEmpty(refundIdList)){
            this.batchRefreshBondRefundStatus(refundIdList);
        }
        batchRefundRecordVo.setSuccessCount(successCount);
        batchRefundRecordVo.setFailCount(failCount);
        //批量保存流程角色表
        return Result.ok(batchRefundRecordVo);
    }

    private Result<String> checkBatchRefundData(List<ExamineReq> reqList){
        if(CollectionUtils.isEmpty(reqList)){
            return Result.failed("无待审核数据，请检查！");
        }
        //校验是否有记录已审核过
        List<Long> ids = reqList.stream().map(ExamineReq::getId).collect(Collectors.toList());
        LambdaQueryWrapper<BondRefund> bondRefundQuery = new LambdaQueryWrapper<>();
        bondRefundQuery.in(BondRefund::getId,ids);
        bondRefundQuery.ne(BondRefund::getStatus,BondRefundStatusEnum.WAIT.getType());
        if(this.count(bondRefundQuery)>0){
            return Result.failed("存在已处理过记录请检查！");
        }
//        //校验是否超过保证金余额
//        LambdaQueryWrapper<BondRefundDetails> detailsQuery = new LambdaQueryWrapper<>();
//        detailsQuery.in(BondRefundDetails::getRefundId,ids);
//        List<BondRefundDetails> detailList = bondRefundDetailsService.list(detailsQuery);
//        //判断账户中余额是否足够
//        if(!bondRefundBalanceService.checkBondBalance(detailList)){
//            return Result.failed("保证金账户余额不足！");
//        }
        return Result.ok();
    }

    private void saveProcessRoles(List<Long> processRoleIds,List<ProcessRole> processRoleList){
        //先删除流程角色表数据
        LambdaQueryWrapper<ProcessRole> processRoleQuery = new LambdaQueryWrapper<>();
        processRoleQuery.in(ProcessRole::getBusinessId,processRoleIds)
                .eq(ProcessRole::getBusinessCode,FlowClientConstant.BOND_APPLY_REFUND);
        processRoleService.remove(processRoleQuery);
        //插入流程角色表
        processRoleService.saveBatch(processRoleList);
    }

    private ReturnVo refundWorkFlow(ExamineReq req,JwtUser jwtUser){
        String str = flowApiClient.review(jwtUser.getUserOtherId(), req.getProcessInstanceId() + "", req.getTaskId(), req.getMessage(), true,false);
        //判断推送到流程是否成功
        ReturnVo returnVo = JSONObject.parseObject(str, ReturnVo.class);
        WorkFlowExceptionUtil.checkFlowResult(str);
        ProcessRecordDTO recordDTO = new ProcessRecordDTO();
        recordDTO.setBusinessId(req.getId());
        recordDTO.setOperation("同意");
        recordDTO.setOperatorId(jwtUser.getUserId());
        recordDTO.setOperatorName(jwtUser.getUserName());
        recordDTO.setRemark(req.getMessage());
        recordDTO.setBusinessCode(FlowClientConstant.BOND_APPLY_REFUND);
        //插入流程记录
        processRecordService.addProcessRecord(recordDTO,jwtUser.getUserOtherId(),str);
        return returnVo;
    }

    private void pushRefund2Ncc(BondRefund bondRefund){
        SimpleDateFormat sfm = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String creationTime = sfm.format(new Date());
        List<SyncNccRefundInfoDto> syncNccRefunds = bondRefundDetailsService.getSyncNccRefundInfo(bondRefund.getId());
        syncNccRefunds.forEach(b->{
            b.setRefundDate(creationTime);
            if(org.apache.commons.lang3.StringUtils.isBlank(b.getReceiveAcount())){
                //获取招标中心的保证金账户
                BankdataBondToNcc bankdataBondToNcc = bankdataBondToNccService.getBankdataBondToNcc(b.getFromOpenBank());
                if(bankdataBondToNcc != null){
                    b.setReceiveAcount(bankdataBondToNcc.getAccount());
                }
            }
        });
        //有代理服务费的数据的，不推送保证金单据
        if(bondRefund.getAgencyFee() != null && bondRefund.getAgencyFee().doubleValue() != 0) {
            //经办岗同意后，推送代理服务费数据到ncc
            syncNccAgencyFee(syncNccRefunds, bondRefund.getId(), creationTime);
        }else{
            if(bondRefund.getRefundMoney() != null && bondRefund.getRefundMoney().doubleValue() != 0) {
                //经办岗同意后，推送保证金退还数据到ncc
                syncNccRefund(syncNccRefunds, bondRefund.getId(), creationTime);
            }
        }
    }


    private void removeNcc(String billId){
        BondRefundDeleteDto dto = new BondRefundDeleteDto();
        dto.setBillId(billId);
        NccBondPushLog pushLog = new NccBondPushLog();
        pushLog.setPushType(NccPushTypeEnum.REFUND_DELETE.getType());
        pushLog.setRequestData(JSONObject.toJSONString(dto));
        pushLog.setBillId(billId);
        pushLog.setPushResult(CommonConstants.NO2);
       /* String nccResult = nccApiClient.deleteBondRefund(dto);
        JSONObject jsonObject = JSONObject.parseObject(nccResult);
        if (StringUtils.isNotBlank(nccResult) && ((ReturnCodeConstants.COMMON_SUCCESS_CODE).equals(jsonObject.getString("code")))) {
            pushLog.setPushResult(CommonConstants.YES);
        } else {
            pushLog.setPushResult(CommonConstants.NO2);
            String message = jsonObject.getString("message");
            throw new ParamsNotNullException(message);
        }*/
        pushLogService.save(pushLog);

        //删除没有推送成功给日志
       /* LambdaQueryWrapper<NccBondPushLog> removeWrapper = new LambdaQueryWrapper<>();
        removeWrapper.eq(NccBondPushLog::getBillId,billId)
                .eq(NccBondPushLog::getPushResult,CommonConstants.NO2);
        pushLogService.remove(removeWrapper);*/
    }

    /**
     * 经办岗同意后，推送保证金退还数据到ncc
     * @param syncNccRefunds
     */
    private void syncNccRefund(List<SyncNccRefundInfoDto> syncNccRefunds,Long refundId,String creationTime){
        BondRefund bondRefund = getById(refundId);
        if(bondRefund.getRefundMoney().doubleValue() != 0) {
            List<BondRefundBodyDto> bodys = BeanListUtil.convertList(syncNccRefunds, BondRefundBodyDto.class);
            bodys.forEach(b->{
                BondAgentAccount bondAgentAccount = getBondAgentAccount(b.getFromOpenBank(), BankAccountTypeEnum.BOND.getType());
                if(bondAgentAccount != null) {
                    b.setPayAccount(bondAgentAccount.getAgentAcount());
                }
            });
            BondRefundDto dto = new BondRefundDto();
            //创建时间
            dto.setCreationTime(creationTime);
            if (org.apache.commons.lang3.StringUtils.isNotBlank(syncNccRefunds.get(0).getPurchaseNumber())) {
                dto.setPurchaseNumber(syncNccRefunds.get(0).getPurchaseNumber());
            } else {
                dto.setPurchaseNumber(NccConstants.OTHER_PURCHASE_NUMBER);
            }
            dto.setBillId(NccConstants.NCC_REFUND_KEY + refundId);
            dto.setBody(bodys);
            NccBondPushLog pushLog = new NccBondPushLog();
            pushLog.setBillId(NccConstants.NCC_REFUND_KEY + refundId);
            pushLog.setPushType(NccPushTypeEnum.REFUND.getType());
            pushLog.setRequestData(JSONObject.toJSONString(dto));
//            String nccResult = nccApiClient.syncBondRefund(dto);
//            if (StringUtils.isNotBlank(nccResult) && ((ReturnCodeConstants.COMMON_SUCCESS_CODE).equals(JSONObject.parseObject(nccResult).getString("code")))) {
//                pushLog.setPushResult(CommonConstants.YES);
//            } else {
//                pushLog.setPushResult(CommonConstants.NO2);
//            }
            pushLog.setPushResult(CommonConstants.NO2);
            pushLogService.save(pushLog);
        }
    }


    /**
     * 经办岗同意后，推送代理服务费数据到ncc
     * @param syncNccRefunds
     * @param refundId
     */
    private void syncNccAgencyFee(List<SyncNccRefundInfoDto> syncNccRefunds,Long refundId,String creationTime){
        BondRefund bondRefund = getById(refundId);
        //代理服务费不等于0的时候推送
        if(bondRefund.getFormerAgencyFee() != null && bondRefund.getFormerAgencyFee().doubleValue() != 0) {
            BondAgencyFeeDto dto = new BondAgencyFeeDto();
            dto.setProvinceFlag(syncNccRefunds.get(0).getProvinceFlag());
            dto.setBillId(NccConstants.NCC_AGENCY_FEE_KEY + refundId);
            dto.setCreationTime(creationTime);
            dto.setPkDeptId(syncNccRefunds.get(0).getPkDeptId());
            dto.setPkPsndoc(syncNccRefunds.get(0).getPkPsndoc());
            if (org.apache.commons.lang3.StringUtils.isNotBlank(syncNccRefunds.get(0).getPurchaseNumber())) {
                dto.setPurchaseNumber(syncNccRefunds.get(0).getPurchaseNumber());
            } else {
                dto.setPurchaseNumber(NccConstants.OTHER_PURCHASE_NUMBER);
            }
            //保证金总金额
            BigDecimal transferAmount = bondRefund.getBondMoney().add(bondRefund.getAgencyFee());
            //转出保证金金额（保证金总金额）
            dto.setTransferAmount(transferAmount.toString());
            //退保证金金额（收代理服务费后退还的保证金金额）
            dto.setRefundAmount(bondRefund.getRefundMoney().toString());
            //补代理费金额（供应商补的代理服务费金额
            if (transferAmount.compareTo(bondRefund.getFormerAgencyFee()) == -1) {
                dto.setSupplyAgencyFeeAmount(bondRefund.getFormerAgencyFee().subtract(transferAmount).toString());
            }
            //保证金利息
            dto.setInterestAmount(bondRefund.getRates().toString());
            BigDecimal localTaxCrAll = new BigDecimal("0");
            List<BondAgencyFeeBodyDto> bodys = new ArrayList<BondAgencyFeeBodyDto>();
            syncNccRefunds.forEach(s -> {
                //保证金账户
                BondAgentAccount bondAgentAccount = getBondAgentAccount(s.getFromOpenBank(), BankAccountTypeEnum.BOND.getType());
                BondAgencyFeeBodyDto body = new BondAgencyFeeBodyDto();
                body.setCustomer(s.getCustomer());
                String sComment = "";
                //获取代理服务费账号
               /* Integer  bankType = BankTypeEnum.CHINA.getType();
                if(syncNccRefunds.get(0).getFromOpenBank().indexOf(BankTypeEnum.CHINA.getDesc())>-1){
                    bankType = BankTypeEnum.CHINA.getType();
                }else if(syncNccRefunds.get(0).getFromOpenBank().indexOf(BankTypeEnum.NANJING.getDesc())>-1){
                    bankType = BankTypeEnum.NANJING.getType();
                }else if(syncNccRefunds.get(0).getFromOpenBank().indexOf(BankTypeEnum.MINSHENG.getDesc())>-1){
                    bankType = BankTypeEnum.MINSHENG.getType();
                }*/
               //AgencyInfo2NccVo nccAgency = baseMapper.getAgencyInfo2Ncc(bankType);

                //获取代理服务费账号
                BondAgentAccount agentAccout = getBondAgentAccount(s.getFromOpenBank(),BankAccountTypeEnum.AGENCT.getType());
                //1）	保证金全额转代理费  保证金金额 == 代理服务费金额
                if (transferAmount.compareTo(bondRefund.getFormerAgencyFee()) == 0) {
                    sComment = CommentEnum.ALL_REFUND.getDesc();
                } else if (transferAmount.compareTo(bondRefund.getFormerAgencyFee()) == -1) {//保证金全额转代理费-补差额
                    sComment = CommentEnum.ALL_REFUND_BKE.getDesc();
                } else if (transferAmount.compareTo(bondRefund.getFormerAgencyFee()) == 1) {//保证金转代理费-退余额
                    sComment = CommentEnum.BF_REFUND.getDesc();
                }
                body.setSComment(sComment);
                //收款账户
                body.setRecAccount(agentAccout.getAgentAcount());
                //代理服务费税费：代理服务费/1.06*0.06
                BigDecimal divide = bondRefund.getFormerAgencyFee().divide(new BigDecimal(1.06), 2, BigDecimal.ROUND_HALF_UP);
                BigDecimal localTaxCr = (divide.multiply(new BigDecimal(0.06))).setScale(2,BigDecimal.ROUND_HALF_UP);

                BigDecimal divide1 = bondRefund.getFormerAgencyFee().divide(new BigDecimal(1.06), 6, BigDecimal.ROUND_HALF_UP);
                BigDecimal localTaxCr1 = (divide1.multiply(new BigDecimal(0.06))).setScale(6,BigDecimal.ROUND_HALF_UP);
                localTaxCrAll.add(localTaxCr1);
                body.setLocalTaxCr(localTaxCr.toString());
                //代理服务费金额含税
//                body.setMoneyCr(bondRefund.getFormerAgencyFee().add(localTaxCr).toString());
                //代理服务费无税费
//                body.setNoTaxCr(bondRefund.getFormerAgencyFee().toString());

                //代理服务费金额含税
                body.setMoneyCr(bondRefund.getFormerAgencyFee().toString());
                //代理服务费无税费
                body.setNoTaxCr(bondRefund.getFormerAgencyFee().subtract(localTaxCr).toString());
                //付款银行 招标中心保证金对公账户（对应银行
                if(bondAgentAccount != null) {
                    body.setPayAccount(bondAgentAccount.getAgentAcount());
                }
                //保证金到账时间
                body.setReceiveTime(syncNccRefunds.get(0).getAmountDate());
                body.setReceiveAcount(s.getReceiveAcount());
                bodys.add(body);
            });
            //处理累加后的结果，保留2为小数
            dto.setTaxAmount(localTaxCrAll.setScale(2,BigDecimal.ROUND_HALF_UP).toString());
            dto.setBody(bodys);
            NccBondPushLog pushLog = new NccBondPushLog();
            pushLog.setBillId(NccConstants.NCC_AGENCY_FEE_KEY + refundId);
            pushLog.setPushType(NccPushTypeEnum.AGENCY_FEE.getType());
            pushLog.setRequestData(JSONObject.toJSONString(dto));
            pushLog.setPushResult(CommonConstants.NO2);
           /* String result = nccApiClient.syncBondAgencyFee(dto);
            if (StringUtils.isNotBlank(result) && ((ReturnCodeConstants.COMMON_SUCCESS_CODE).equals(JSONObject.parseObject(result).getString("code")))) {
                pushLog.setPushResult(CommonConstants.YES);
            } else {
                pushLog.setPushResult(CommonConstants.NO2);
            }*/
            pushLogService.save(pushLog);
        }
    }

    /**
     * 拒绝后恢复计算后的值
     */
    private void recoveryBondMondy(BondRefund bondRefund,List<BondRefundDetails> details){
        BigDecimal returnBondMoney = new BigDecimal(0);
        BigDecimal returnAgencyFee = new BigDecimal(0);
        //恢复计算后的更改的值
        for(BondRefundDetails d:details){
            //查询收取并保留剩余保证金记录表的数据
            LambdaQueryWrapper<BondRetainRecord> recordWrapper = new LambdaQueryWrapper<>();
            recordWrapper.eq(BondRetainRecord::getRefundDetailId,d.getId());
            BondRetainRecord one = bondRetainRecordService.getOne(recordWrapper);
            if(one != null){
                d.setBondMoney(one.getBondMoney());
                d.setAgencyFee(one.getAgencyFee());
                bondRetainRecordService.removeById(one.getId());
            }
            d.setStatus(BondRefundStatusEnum.RETURNED.getType());
            d.setRates(new BigDecimal(0));
            d.setRefundMoney(new BigDecimal(0));
            returnBondMoney = returnBondMoney.add(d.getBondMoney());
            returnAgencyFee = returnAgencyFee.add(d.getAgencyFee());

        }
        bondRefund.setBondMoney(returnBondMoney);
        bondRefund.setAgencyFee(returnAgencyFee);
    }

    /**
     * 计算利息
     */
    private void calculationRates(BondRefund bondRefund,List<BondRefundDetails> details,Boolean iscalculationRates){
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        //查询收款流水
        Long waterId;
        //判断是不是未关联流水
        if(null!=bondRefund.getWaterId()){
            waterId = bondRefund.getWaterId();
        }else{
            LambdaQueryWrapper<BondRelation> queryRelationWrapper = new LambdaQueryWrapper<>();
            queryRelationWrapper.eq(BondRelation::getSectionId,bondRefund.getSectionId());
            queryRelationWrapper.eq(BondRelation::getCompanyId,bondRefund.getCompanyId()).last("limit 1");
            BondRelation bondRelation = bondRelationService.getOne(queryRelationWrapper);
            waterId = bondRelation.getWaterId();
        }
        BondWater water = bondWaterService.getById(waterId);
        //查询银行类型
        LambdaQueryWrapper<BankdataBondToNcc> queryBondToNccWrapper = new LambdaQueryWrapper<>();
        queryBondToNccWrapper.eq(BankdataBondToNcc::getAccount,water.getReceiveAcount()).last("limit 1");
        BankdataBondToNcc bondToNcc = bankdataBondToNccService.getOne(queryBondToNccWrapper);
        Integer bankType = bondToNcc.getBankType();
        //查询利率
        LambdaQueryWrapper<BondRate> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BondRate::getType,RateTypeEnum.BOND.getType());
        queryWrapper.eq(BondRate::getBankType,bankType).orderByAsc(BondRate::getEffectDate);
        List<BondRate> rates = bondRateService.list(queryWrapper);
        if(CollectionUtils.isEmpty(rates)){
            throw new ParamsNotNullException("利率查询为空，不可退款");
        }
        Boolean isZfcg = false;
        //判断是否是政府采购项目
        if(bondRefund.getSectionId() != null && bondRefund.getSectionId() != 0){
            ProjectBidSection section = projectBidSectionService.getById(bondRefund.getSectionId());
            if (section != null  && BidPurchaseModeEnum.GOVERNMENT_PROCUREMENT.getType().equals(section.getPurchaseMode())
                    || BidPurchaseModeEnum.GOVERNMENT_PROCUREMENT_924101.getType().equals(section.getPurchaseMode())
                    || BidPurchaseModeEnum.GOVERNMENT_PROCUREMENT_924102.getType().equals(section.getPurchaseMode())
                    || BidPurchaseModeEnum.GOVERNMENT_PROCUREMENT_924103.getType().equals(section.getPurchaseMode())
                    || BidPurchaseModeEnum.GOVERNMENT_PROCUREMENT_924104.getType().equals(section.getPurchaseMode())
                    || BidPurchaseModeEnum.GOVERNMENT_PROCUREMENT_924105.getType().equals(section.getPurchaseMode())
                    || BidPurchaseModeEnum.GOVERNMENT_PROCUREMENT_924106.getType().equals(section.getPurchaseMode())
            ) {
                isZfcg = true;
            }
        }

        for(BondRefundDetails d:details){
            //到账时间
            Date accountTime = null;
            if(StringUtils.isNotBlank(d.getAmountDate())){
                try {
                    accountTime = sdf.parse(d.getAmountDate());
                } catch (ParseException e) {
                    e.printStackTrace();
                }
            }
            //计算利息
            calculationRatesForRefund(accountTime,rates,bondRefund,d,iscalculationRates,isZfcg);
        }

        //主数据金额等于明细相加
        BigDecimal returnBondMoney = new BigDecimal(0);
        BigDecimal returnAgencyFee = new BigDecimal(0);
        BigDecimal refurnRates = new BigDecimal(0);
        BigDecimal refundMoney = new BigDecimal(0);
        //恢复计算后的更改的值
        for(BondRefundDetails d:details){
            returnBondMoney = returnBondMoney.add(d.getBondMoney());
            returnAgencyFee = returnAgencyFee.add(d.getAgencyFee());
            refurnRates = refurnRates.add(d.getRates());
            refundMoney = refundMoney.add(d.getRefundMoney());
        }
        //1：收取并退还剩余保证金
        if(iscalculationRates && bondRefund.getRefundMode().equals(RefundModeEnum.REFUND_SURPLUS.getType())) {
            //判断收取并保留剩余保证金记录中是否有记录，有的话，利息求和
            BigDecimal allRates = bondRetainRecordService.getCountRateByRefundId(bondRefund.getId());
            if(allRates.doubleValue() != 0){
                refurnRates = refurnRates.add(allRates);
                refundMoney = refundMoney.add(refurnRates);
            }
        }
        bondRefund.setBondMoney(returnBondMoney);
        bondRefund.setAgencyFee(returnAgencyFee);
        bondRefund.setRates(refurnRates);
        bondRefund.setRefundMoney(refundMoney);
    }


    /**
     * 编辑行号信息
     * @param companyId
     * @param bankCode
     * @param openBank
     * @param companyName
     * @param companyAccount
     */
    private void addCompanyBankInfo(Long companyId,String bankCode,String openBank,String companyName,String companyAccount){
        LambdaQueryWrapper<BondCompanyBank> bankQuery = new LambdaQueryWrapper<>();
        bankQuery.eq(BondCompanyBank::getCompanyName,companyName)
                .eq(BondCompanyBank::getCompanyAccount,companyAccount)
                .eq(BondCompanyBank::getCompanyId,companyId)
                .orderByDesc(BondCompanyBank::getId)
                .last("limit 1");
        BondCompanyBank one = bondCompanyBankService.getOne(bankQuery);
        //库里不存在时，插入银行信息
        if(one == null){
            BondCompanyBank companyBank = new BondCompanyBank();
            companyBank.setCompanyId(companyId);
            //不是中国银行的，设置行号
            if(StringUtils.isNotBlank(openBank) && openBank.indexOf(BankTypeEnum.CHINA.getDesc())<0){
                companyBank.setCompanyBankCode(bankCode);
            }
            companyBank.setCompanyBankDeposit(openBank);
            companyBank.setCompanyName(companyName);
            companyBank.setCompanyAccount(companyAccount);
            bondCompanyBankService.saveOrUpdate(companyBank);
        }

    }


    /**
     * 计算利息
     * @param accountTime
     * @param rates
     * @param bondRefund
     * @param d
     */
    private void calculationRatesForRefund(Date accountTime,List<BondRate> rates,BondRefund bondRefund,BondRefundDetails d,Boolean iscalculationRates,Boolean isZfcg){
        /*
          保证金利息计算公式：
          ------------------收取并退还剩余保证金----------------------------------------------
          利息 = 保证金金额×（第一次利息调整时间-保证金到账时间）×调整前的利率÷365 +
                   保证金金额×（第二次利息调整时间-第一次利息的调整时间）×调整前的利率÷365 +
                   ...... +
                   保证金金额×（审批岗审批时间-最后一次利息的调整时间）×最后一次调整的利率÷365
           -----------------收取并保留剩余保证金-----------------------------------------------
          利息 =  代理服务费*(第一次调整时间-到账时间)*调整前利率/365 +
                           代理服务费*(第二次调整时间-第一次调整时间)*调整前利率/365 +
                           ......... +
                           代理服务费*(审批岗审批时间-最后一次调整时间)*最后一次调整的利率/365
         */
        BigDecimal bondMoney = d.getBondMoney();//50000
        BigDecimal agencyFee = d.getAgencyFee() == null ? new BigDecimal(0) : d.getAgencyFee();
        BigDecimal calculationMoney = new BigDecimal(0);
        //1：收取并退还剩余保证金
        if (RefundModeEnum.REFUND_SURPLUS.getType().equals(bondRefund.getRefundMode())) {
            calculationMoney = bondMoney;
        }else if (RefundModeEnum.RETAIN_SURPLUS.getType().equals(bondRefund.getRefundMode())) {//2：收取并保留剩余保证金
            calculationMoney = agencyFee;
        }else if (RefundModeEnum.AGAIN_GATHER.getType().equals(bondRefund.getRefundMode())) {//3：收取并再次收取
            calculationMoney = agencyFee;
        }else{//4：收取并结束收取
            calculationMoney = agencyFee;
        }
        BigDecimal rate = new BigDecimal(0);
        //政府采购的不计算利息
        if(iscalculationRates && !isZfcg) {
            if (accountTime != null) {
                if (rates.size() == 1) {
                    int day = DateUtils.differentDaysByMillisecond(accountTime, new Date());
                    day = day < 0 ? 0 : day;
                    BigDecimal temp = calculationMoney.multiply(new BigDecimal(String.valueOf(day))).multiply(rates.get(0).getRate()).multiply(new BigDecimal("0.01"));
                    rate = temp.divide(new BigDecimal("365"), 2, BigDecimal.ROUND_HALF_UP);
                } else {
                    //第一次调整
                    int day1 = DateUtils.differentDaysByMillisecond(accountTime, rates.get(0).getEffectDate());
                    day1 = day1 < 0 ? 0 : day1;
                    BigDecimal temp1 = calculationMoney.multiply(new BigDecimal(String.valueOf(day1))).multiply(rates.get(0).getRate()).multiply(new BigDecimal("0.01"));
                    rate = temp1.divide(new BigDecimal("365"), 2, BigDecimal.ROUND_HALF_UP);
                    int last = rates.size();
                    for (int i = 1; i < rates.size(); i++) {
                        //若利率生效日期大于当前日期则不计算，并记录最后一个大于当前日期
                        if(rates.get(i).getEffectDate().before(new Date())){
                            //第N次调整
                            int day2 = 0;
                            //到账时间在N-1次调整之前，并且在n次调整之后 || 到账时间在n次调整之后
                            if ((rates.get(i-1).getEffectDate().before(accountTime) && accountTime.before(rates.get(i).getEffectDate()))
                                    || rates.get(i).getEffectDate().before(accountTime)) {
                                day2 = DateUtils.differentDaysByMillisecond(accountTime, rates.get(i).getEffectDate());
                            } else {
                                day2 = DateUtils.differentDaysByMillisecond(rates.get(i-1).getEffectDate(), rates.get(i).getEffectDate());
                            }
                            day2 = day2 < 0 ? 0 : day2;
                            BigDecimal temp2 = calculationMoney.multiply(new BigDecimal(String.valueOf(day2))).multiply(rates.get(i-1).getRate()).multiply(new BigDecimal("0.01"));
                            BigDecimal n_rates = temp2.divide(new BigDecimal("365"), 2, BigDecimal.ROUND_HALF_UP);
                            rate = rate.add(n_rates);
                        }else{
                            last = i;
                            break;
                        }
                    }
                    //审批岗位确认
                    int day3 = 0;
                    //最后一次调整时间在到账时间之前，则用到账时间计算
                    if (rates.get(last - 1).getEffectDate().before(accountTime)) {
                        day3 = DateUtils.differentDaysByMillisecond(accountTime, new Date());
                    } else {
                        day3 = DateUtils.differentDaysByMillisecond(rates.get(last - 1).getEffectDate(), new Date());
                    }
                    day3 = day3 < 0 ? 0 : day3;
                    BigDecimal temp3 = calculationMoney.multiply(new BigDecimal(String.valueOf(day3))).multiply(rates.get(last - 1).getRate()).multiply(new BigDecimal("0.01"));
                    BigDecimal sp_rates = temp3.divide(new BigDecimal("365"), 2, BigDecimal.ROUND_HALF_UP);
                    rate = rate.add(sp_rates);
                }
            }
        }

        //保证金转代理服务费 (50000 -3000 = 47000)： 退47000给供应商  3000到自有账号
        ////保证金转代理服务费 (50000 -3000 = 47000)：  47000保留  3000到自有账号
        if (BondAgencyFeeTypeEnum.BOND_TO_AGENCY.getType().equals(bondRefund.getAgencyFeeType())) {
            if(iscalculationRates) {
                d.setBondMoney(bondMoney.subtract(agencyFee));
            }
            d.setAgencyFee(agencyFee);
            d.setRates(rate);
        } else if (BondAgencyFeeTypeEnum.BOND_REFUND.getType().equals(bondRefund.getAgencyFeeType())) {
            //保证金全额退还，代理费另汇。保证金50000,代理服务费3000：，50000全额退还给供应商  3000另汇（不处理）
            //保证金全额退还，代理费另汇。保证金50000,代理服务费3000，50000保留  3000另汇（不处理）
            d.setBondMoney(bondMoney);
            d.setAgencyFee(new BigDecimal(0));
            d.setRates(rate);
        } else if (BondAgencyFeeTypeEnum.BOND_TO_AGENCY_SUPPLEMENT.getType().equals(bondRefund.getAgencyFeeType())) {
            //保证金转代理服务费并补差额  保证金：50000，代理服务费60000 ： 50000到自有账号 + 10000另汇（不处理）
            if(iscalculationRates) {
                d.setBondMoney(new BigDecimal(0));
            }
            d.setAgencyFee(bondMoney);
            d.setRates(new BigDecimal(0));
        }else{
            d.setRates(rate);
        }
        BondRetainRecord retainRecord = new BondRetainRecord();
        retainRecord.setRefundId(bondRefund.getId());
        retainRecord.setBondMoney(bondMoney);
        retainRecord.setAgencyFee(agencyFee);
        retainRecord.setRefundDetailId(d.getId());

        //1：收取并退还剩余保证金
        if (bondRefund.getRefundMode().equals(RefundModeEnum.REFUND_SURPLUS.getType())) {
            retainRecord.setRates(new BigDecimal(0));
            BigDecimal refundMoney = bondMoney.add(d.getRates()).subtract(d.getAgencyFee());
            d.setRefundMoney(refundMoney.doubleValue()<0?new BigDecimal(0):refundMoney);
        }else if (RefundModeEnum.RETAIN_SURPLUS.getType().equals(bondRefund.getRefundMode())) {//2：收取并保留剩余保证金
            d.setRefundMoney(new BigDecimal(0));
            retainRecord.setRates(d.getRates());
        }else if (RefundModeEnum.AGAIN_GATHER.getType().equals(bondRefund.getRefundMode())) {//3：收取并再次收取
            d.setRefundMoney(new BigDecimal(0));
            retainRecord.setRates(d.getRates());
        }else{//4：收取并结束收取
            d.setRefundMoney(new BigDecimal(0));
            retainRecord.setRates(d.getRates());
        }
        if(iscalculationRates) {
            //插入收取并保留剩余保证金记录表
            bondRetainRecordService.save(retainRecord);
        }
    }

    /**
     * 再次付款
     * @param req
     * @param jwtUser
     * @return
     */
    @Override
    public boolean saveAgainRefund(RefundIdReq req, JwtUser jwtUser) {
        BondRefund refund = getById(req.getId());
        if(!refund.getStatus().equals(BondRefundStatusEnum.FAIL.getType())){
            throw new ParamsNotNullException("只有支付失败的数据才能再次支付!");
        }
        Boolean result = refund(refund, jwtUser);
        return result;
    }

    @Override
    public Boolean saveOfflineRefund(BondRefundFeq req) {
        BondRefund history = getById(req.getId());
        if(!history.getStatus().equals(BondRefundStatusEnum.FAIL.getType())){
            throw new ParamsNotNullException("只有支付失败的数据才能线下退还!");
        }
        //修改数据为线下支付
        BondRefund refund = BeanListUtil.convert(req,BondRefund.class);
        refund.setStatus(BondRefundStatusEnum.SUCCESS.getType());
        refund.setRefundWay(RefundWayEnum.OFFLINE.getType());
        refund.setRefundProcessCode("");
        Boolean result = updateById(refund);

        //把详情表中的记录都设置为退还成功
        LambdaQueryWrapper<BondRefundDetails> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(BondRefundDetails::getRefundId,refund.getId());
        BondRefundDetails details = new BondRefundDetails();
        details.setStatus(BondRefundStatusEnum.SUCCESS.getType());
        details.setAgencyRefundStatus(BondRefundStatusEnum.SUCCESS.getType());
        bondRefundDetailsService.update(details,lambdaQueryWrapper);

        //插入流程记录
        ProcessRecordDTO recordDTO = new ProcessRecordDTO();
        recordDTO.setBusinessCode(FlowClientConstant.BOND_APPLY_REFUND);
        recordDTO.setBusinessId(refund.getId());
        recordDTO.setOperation("线下退还");
        recordDTO.setOperatorId(LoginUtil.getJwtUser().getUserId());
        recordDTO.setOperatorName(LoginUtil.getJwtUser().getUserName());
        processRecordService.addProcessRecord(recordDTO,LoginUtil.getJwtUser().getUserOtherId(),null);
        return result;
    }

    /**
     * 收款流水退还列表
     * @param condition
     * @return
     */
    @Override
    public IPage<RefundListVo> bondWaterRefundList(BondRefundCondition condition) {
        Page<RefundListVo> page = condition.buildPage();
        condition.setBusinessType(RefundBusinessTypeEnum.REFUND_LIST.getType());
        return getPageBondRefundList(page,condition);
    }

    /**
     * 查询保证金退还申请表
     * @param condition
     * @return
     */
    @Override
    public List<BondProjectDTO> printBondRefundApply(BondRefundCondition condition) {
        Page<RefundListVo> page = condition.buildPage();
        Long[] roleIds = Object2OtherUtil.Object2Long(LoginUtil.getJwtUser().getUser().getRoles().stream().map(Role::getId).collect(Collectors.toList()).toArray());
        condition.setType(2);
        condition.setRoleId(roleIds);
        condition.setBusinessType(RefundBusinessTypeEnum.EXAMINE_LIST.getType());
        //待处理列表的数据
        IPage<RefundListVo> pageBondRefundList = getPageBondRefundList(page, condition);
        Map<Long,List<BondSupplierVO>> sectionMap = new HashMap<>();
        pageBondRefundList.getRecords().forEach(p->{
            if(p.getSectionId() != null && BondRefundStatusEnum.SUCCESS.getType().equals(p.getStatus())) {
                //查询同一个标段的供应商信息
                condition.setSectionId(p.getSectionId());
                List<BondSupplierVO> suppliers = this.baseMapper.printBondRefundApply(condition);
                sectionMap.put(p.getSectionId(), suppliers);
            }
        });
        return getPrintBondRefundApplys(sectionMap);
    }


    /**
     * 打印中标通知书存根
     * @param condition
     * @return
     */
    @Override
    public List<String> printBidWinNoticeStub(BondRefundCondition condition) {
        List<Long> stubFiles = new ArrayList<>();
        Page<RefundListVo> page = condition.buildPage();
        Long[] roleIds = Object2OtherUtil.Object2Long(LoginUtil.getJwtUser().getUser().getRoles().stream().map(Role::getId).collect(Collectors.toList()).toArray());
        condition.setType(2);
        condition.setRoleId(roleIds);
        condition.setBusinessType(RefundBusinessTypeEnum.EXAMINE_LIST.getType());
        //待处理列表的数据
        IPage<RefundListVo> pageBondRefundList = getPageBondRefundList(page, condition);
        pageBondRefundList.getRecords().forEach(p->{
            //查询相同供应商的存根信息
            Map<String, Object> param = new HashMap<>();
            param.put("sectionId", p.getSectionId());
            param.put("bidRound", BidRoundEnum.HS.getType());
            param.put("tendererId", p.getCompanyId());
            StubVo stubVo = bidWinPeopleService.getSupplierStubNo(param);
            if(stubVo != null && stubVo.getStubFile() != null){
                if(!stubFiles.contains(stubVo.getStubFile())){
                    stubFiles.add(stubVo.getStubFile());
                }
            }
        });

        List<String> allFiles = new ArrayList<>();
        stubFiles.forEach(f->{
            OssFile ossFile = ossFileService.getById(f);
            //下载文件
            OssFileStorage ossFileStorage = OssUtil.getOssFileStorage(ossType);
            byte[] bytes = ossFileStorage.downloadFile(ossFile.getOssFileKey());
            List<String> jpgs = FileUtils.pdf2Base64(bytes);

            allFiles.addAll(jpgs);
            bytes=null;
        });
        return allFiles;
    }


    /**
     * 校验状态
     * @param list
     * @return
     */
    @Override
    public Integer judgeStatus(List<BondRefundDetails> list) {
        Integer refundStatus = BondRefundStatusEnum.REFUNDING.getType();
        List<Integer> statusList = new ArrayList<>();
        for (BondRefundDetails details : list) {
            if (details.getStatus() != null) {
                statusList.add(details.getStatus());
            }
            if (details.getAgencyRefundStatus() != null) {
                statusList.add(details.getAgencyRefundStatus());
            }
        }
        // 只要有一个为退还中，主表状态仍为退还中;无退还中状态时，有一个失败即为失败;全部退还成功，则状态为成功
        List<Integer> successCount = statusList.stream().filter(s -> s.equals(BondRefundStatusEnum.SUCCESS.getType())).collect(Collectors.toList());
        if (statusList.contains(BondRefundStatusEnum.REFUNDING.getType())) {
            refundStatus = BondRefundStatusEnum.REFUNDING.getType();
        } else if (statusList.contains(BondRefundStatusEnum.FAIL.getType())) {
            refundStatus = BondRefundStatusEnum.FAIL.getType();
        } else if (successCount.size() == statusList.size()) {
            refundStatus = BondRefundStatusEnum.SUCCESS.getType();
        }
        return refundStatus;
    }

    /**
     * 转异常
     * @param req
     * @return
     */
    @Override
    public boolean saveToAbnormal(RefundIdReq req) {
        Boolean result = false;
        BondRefund refund = new BondRefund();
        refund.setId(req.getId());
        refund.setIsAbnormal(CommonConstants.YES);
        refund.setAbnormalTime(new Date());
        refund.setAbnormalReson(req.getAbnormalReson());
        refund.setApproverRoles("");
        result = this.updateById(refund);

        //插入流程记录
        ProcessRecordDTO recordDTO = new ProcessRecordDTO();
        recordDTO.setBusinessId(refund.getId());
        recordDTO.setOperation("转异常");
        recordDTO.setOperatorId(LoginUtil.getJwtUser().getUserId());
        recordDTO.setOperatorName(LoginUtil.getJwtUser().getUserName());
        recordDTO.setBusinessCode(FlowClientConstant.BOND_APPLY_REFUND);
        //recordDTO.setRemark();
        processRecordService.addProcessRecord(recordDTO,LoginUtil.getJwtUser().getUserOtherId(),"");
        return result;
    }

    /**
     * 退款
     * @param bondRefund
     * @param jwtUser
     */
    private Boolean refund(BondRefund bondRefund,JwtUser jwtUser){
        Boolean result = false;
        bondRefund.setIsCalculation(CommonConstants.NO2);
        //退款中
        bondRefund.setStatus(BondRefundStatusEnum.REFUNDING.getType());
        //按流水退还
        //if(bondRefund.getBondType().equals(BondWaterTypeEnum.WATER.getType())) {
            //查询退还详情（退还成功/处理中的不查）
            List<BondRefundDetails> details = bondRefundDetailsService.getDetatisForRefund(bondRefund.getId());
            for(BondRefundDetails d:details){
                //获取代理服务费账号
                BondAgentAccount agentAccout = getBondAgentAccount(d.getFromOpenBank(),BankAccountTypeEnum.AGENCT.getType());
                BigDecimal refundMoney = d.getRefundMoney();
                BigDecimal agencyFee = d.getAgencyFee();

                //保证金账户
                BondAgentAccount bondAccount = getBondAgentAccount(d.getFromOpenBank(),BankAccountTypeEnum.BOND.getType());
                TransferDataReq req = new TransferDataReq();
                req.setFribkn("");
                req.setFractnActacn(bondAccount.getAgentAcount()); //付款账号
                req.setActnam(bondAccount.getAgentName());//付款人名称
                //转账给供应商
                if(refundMoney != null && refundMoney.doubleValue() != 0){
                    //退还中和退还成功的数据，不进行退还操作
                    if(!BondRefundStatusEnum.SUCCESS.getType().equals(d.getStatus())
                            && !BondRefundStatusEnum.REFUNDING.getType().equals(d.getStatus())){
                        //查询行号信息
                        LambdaQueryWrapper<BondCompanyBank> bankQuery = new LambdaQueryWrapper<>();
                        bankQuery.eq(BondCompanyBank::getCompanyName,d.getRefundCompanyName())
                                .eq(BondCompanyBank::getCompanyAccount,d.getRefundNumber())
                                .eq(BondCompanyBank::getCompanyId,bondRefund.getCompanyId())
                                .orderByDesc(BondCompanyBank::getId)
                                .last("limit 1");
                        BondCompanyBank one = bondCompanyBankService.getOne(bankQuery);
                        if(one != null){
                            req.setToactnActacn(one.getCompanyAccount()); //收款账号
                            req.setToname(one.getCompanyName()); //收款人名称
                            req.setTobknm(one.getCompanyBankDeposit()); //收款人开户行
                            if(StringUtils.isNotBlank(one.getCompanyBankCode())){
                                req.setToibkn(one.getCompanyBankCode());//收款人行联行号
                            }else{
                                //req.setTobknm(d.getRefundOpenBank()); //收款人开户行
                                req.setToibkn(d.getRefundBankCode()==null?"":d.getRefundBankCode());//收款人行联行号
                            }
                        }else{
                            req.setToactnActacn(d.getRefundNumber()); //收款账号
                            req.setToname(d.getRefundCompanyName()); //收款人名称
                            req.setTobknm(d.getRefundOpenBank()); //收款人开户行
                            if(StringUtils.isNotBlank(d.getRefundBankCode())){
                                req.setToibkn(d.getRefundBankCode());//收款人行联行号
                            }
                        }
                        req.setTrnamt(refundMoney.toString());//转账金额
                        req.setFurinfo("保证金退还");
                        //退“保证金入账日期”保证金“项目编号/包号”，利息X元
                        String projectSection = this.baseMapper.getProjectSectionByRefundId(d.getRefundId());
                        req.setPostScript("退"+d.getAmountDate()+"保证金"+(projectSection == null ? "" : projectSection)+"，利息"+d.getRates()+"元");//附言
                        //转账到银行
                        refund2Bank(agentAccout,d,req,BondPayInfoReqTypeEnum.BOND.getType());
                    }
                }else{
                    d.setStatus(BondRefundStatusEnum.SUCCESS.getType());
                }
                //转账到代理服务账户
                if(agencyFee != null && agencyFee.doubleValue() != 0) {
                    //退还中和退还成功的数据，不进行退还操作
                    if(!BondRefundStatusEnum.SUCCESS.getType().equals(d.getAgencyRefundStatus())
                        && !BondRefundStatusEnum.REFUNDING.getType().equals(d.getAgencyRefundStatus())){
                        req.setToactnActacn(agentAccout.getAgentAcount()); //收款账号
                        req.setToname(agentAccout.getAgentName()); //收款人名称
                        req.setTobknm(agentAccout.getAgentBankName()); //收款人开户行
                        req.setTrnamt(agencyFee.toString());//转账金额
                        if(StringUtils.isNotBlank(agentAccout.getAgentBankCode())){
                            req.setToibkn(agentAccout.getAgentBankCode());//收款人行联行号
                        }else{
                            req.setToibkn("");//收款人行联行号
                        }
                        if(StringUtils.isNotBlank(agentAccout.getAgentBankAddr())){
                            req.setToaddr(agentAccout.getAgentBankAddr()); //收款人地址
                        }else{
                            req.setToaddr(""); //收款人地址
                        }
                        req.setFurinfo("打款代理服务费到自有账户");
                        req.setPostScript(null);
                        //转账到银行
                        refund2Bank(agentAccout,d,req,BondPayInfoReqTypeEnum.AGENT.getType());
                    }
                }else{
                    d.setAgencyRefundStatus(BondRefundStatusEnum.SUCCESS.getType());
                }
            }
            bondRefundDetailsService.updateBatchById(details);
        /*}else{
            bondRefund.setStatus(BondRefundStatusEnum.SUCCESS.getType());
        }*/
        bondRefund.setRefundTime(new Date());
        result = updateById(bondRefund);
        //刷新退还状态
        this.refreshBondRefundStatus(bondRefund.getId());
        return result;
    }

    /**
     * 批量退款
     * @param bondRefund
     * @param details
     */
    private void batchRefund(BondRefund bondRefund,List<BondRefundDetails> details){
        //Boolean result = false;
        bondRefund.setIsCalculation(CommonConstants.NO2);
        //退款中
        bondRefund.setStatus(BondRefundStatusEnum.REFUNDING.getType());
        //按流水退还
        //if(bondRefund.getBondType().equals(BondWaterTypeEnum.WATER.getType())) {
        //查询退还详情（退还成功/处理中的不查）
        //List<BondRefundDetails> details = bondRefundDetailsService.getDetatisForRefund(bondRefund.getId());
        for(BondRefundDetails d:details){
            //退还成功/处理中的排除
            if(BondRefundStatusEnum.SUCCESS.getType().equals(d.getStatus())
                ||BondRefundStatusEnum.REFUNDING.getType().equals(d.getStatus())
                ||BondRefundStatusEnum.SUCCESS.getType().equals(d.getAgencyRefundStatus())
                ||BondRefundStatusEnum.REFUNDING.getType().equals(d.getAgencyRefundStatus())){
                continue;
            }
            //获取代理服务费账号
            BondAgentAccount agentAccout = getBondAgentAccount(d.getFromOpenBank(),BankAccountTypeEnum.AGENCT.getType());
            BigDecimal refundMoney = d.getRefundMoney();
            BigDecimal agencyFee = d.getAgencyFee();
            //保证金账户
            BondAgentAccount bondAccount = getBondAgentAccount(d.getFromOpenBank(),BankAccountTypeEnum.BOND.getType());
            TransferDataReq req = new TransferDataReq();
            req.setFribkn("");
            req.setFractnActacn(bondAccount.getAgentAcount()); //付款账号
            req.setActnam(bondAccount.getAgentName());//付款人名称
            //转账给供应商
            if(refundMoney != null && refundMoney.doubleValue() != 0){
                //查询行号信息
                LambdaQueryWrapper<BondCompanyBank> bankQuery = new LambdaQueryWrapper<>();
                bankQuery.eq(BondCompanyBank::getCompanyName,d.getRefundCompanyName())
                        .eq(BondCompanyBank::getCompanyAccount,d.getRefundNumber())
                        .eq(BondCompanyBank::getCompanyId,bondRefund.getCompanyId())
                        .orderByDesc(BondCompanyBank::getId)
                        .last("limit 1");
                BondCompanyBank one = bondCompanyBankService.getOne(bankQuery);
                if(one != null){
                    req.setToactnActacn(one.getCompanyAccount()); //收款账号
                    req.setToname(one.getCompanyName()); //收款人名称
                    req.setTobknm(one.getCompanyBankDeposit()); //收款人开户行
                    if(StringUtils.isNotBlank(one.getCompanyBankCode())){
                        req.setToibkn(one.getCompanyBankCode());//收款人行联行号
                    }else{
                        //req.setTobknm(d.getRefundOpenBank()); //收款人开户行
                        req.setToibkn(d.getRefundBankCode()==null?"":d.getRefundBankCode());//收款人行联行号
                    }
                }else{
                    req.setToactnActacn(d.getRefundNumber()); //收款账号
                    req.setToname(d.getRefundCompanyName()); //收款人名称
                    req.setTobknm(d.getRefundOpenBank()); //收款人开户行
                    if(StringUtils.isNotBlank(d.getRefundBankCode())){
                        req.setToibkn(d.getRefundBankCode());//收款人行联行号
                    }
                }
                req.setTrnamt(refundMoney.toString());//转账金额
                req.setFurinfo("保证金退还");
                //转账到银行
                refund2Bank(agentAccout,d,req,BondPayInfoReqTypeEnum.BOND.getType());
            }else{
                d.setStatus(BondRefundStatusEnum.SUCCESS.getType());
            }
            //转账到代理服务账户
            if(agencyFee != null && agencyFee.doubleValue() != 0) {
                req.setToactnActacn(agentAccout.getAgentAcount()); //收款账号
                req.setToname(agentAccout.getAgentName()); //收款人名称
                req.setTobknm(agentAccout.getAgentBankName()); //收款人开户行
                req.setTrnamt(agencyFee.toString());//转账金额
                if(StringUtils.isNotBlank(agentAccout.getAgentBankCode())){
                    req.setToibkn(agentAccout.getAgentBankCode());//收款人行联行号
                }else{
                    req.setToibkn("");//收款人行联行号
                }
                if(StringUtils.isNotBlank(agentAccout.getAgentBankAddr())){
                    req.setToaddr(agentAccout.getAgentBankAddr()); //收款人地址
                }else{
                    req.setToaddr(""); //收款人地址
                }
                req.setFurinfo("打款代理服务费到自有账户");
                //转账到银行
                refund2Bank(agentAccout,d,req,BondPayInfoReqTypeEnum.AGENT.getType());
            }else{
                d.setAgencyRefundStatus(BondRefundStatusEnum.SUCCESS.getType());
            }
        }
        //bondRefundDetailsService.updateBatchById(details);
        /*}else{
            bondRefund.setStatus(BondRefundStatusEnum.SUCCESS.getType());
        }*/
        bondRefund.setRefundTime(new Date());
        //result = updateById(bondRefund);
        //刷新退还状态
        //this.refreshBondRefundStatus(bondRefund.getId());
    }


    /**
     * 获取代理服务费账号
     * @param refundOpenBank
     * @return
     */
    private BondAgentAccount getBondAgentAccount(String refundOpenBank,Integer bussineCode){
        Integer bankTypeForAgency = BankTypeEnum.CHINA.getType();
        Integer bankType = getBankType(refundOpenBank,bussineCode);
        if(!BankTypeEnum.OTHER.getType().equals(bankType)){
            bankTypeForAgency = bankType;
        }
        AgencyInfo2NccVo nccAgency = baseMapper.getAgencyInfo2Ncc(bankTypeForAgency,bussineCode);
        BondAgentAccount one = new BondAgentAccount();
        if(nccAgency != null){
            one.setBankType(bankType);
            one.setAgentAcount(nccAgency.getAccount());
            one.setAgentBankName(nccAgency.getBankDeposit());
            one.setAgentName(nccAgency.getAccountName());
        }
        if(BankTypeEnum.OTHER.getType().equals(bankType)){
            one.setBankType(BankTypeEnum.OTHER.getType());
        }
        return one;
    }


    /**
     * 获取银行类型
     * @param refundOpenBank
     * @param bussineCode
     * @return
     */
    @Override
    public Integer getBankType(String refundOpenBank,Integer bussineCode){
        Integer bankType = BankTypeEnum.OTHER.getType();
        if(refundOpenBank.indexOf(BankTypeEnum.CHINA.getDesc())>-1){
            bankType = BankTypeEnum.CHINA.getType();
        }else if(refundOpenBank.indexOf(BankTypeEnum.NANJING.getDesc())>-1){
            bankType = BankTypeEnum.NANJING.getType();
        }else if(refundOpenBank.indexOf(BankTypeEnum.MINSHENG.getDesc())>-1){
            //todo 后期民生银行上线改回去
            //保证金的时候，民生银行没有对接，走中国银行
            if(bussineCode.equals(BankAccountTypeEnum.BOND.getType())){
                bankType = BankTypeEnum.CHINA.getType();
            }else{
                bankType = BankTypeEnum.MINSHENG.getType();
            }
        }
        return  bankType;
    }

    /**
     * 批量审核
     * @param list
     * @param jwtUser
     * @return
     */
   /* @Override
    public Map<String,Long> updateBatchExamine(List<ExamineReq> list, JwtUser jwtUser) {
        Map<String,Long> map = new HashMap<>();
        String batch = UUID.randomUUID().toString();
        list.forEach(e->{
            try {
                updateExamineForRefund(e,jwtUser);
            }catch (Exception o){
                o.printStackTrace();
                //插入保证金审核失败表
                BondExamineFailRecord failRecord = new BondExamineFailRecord();
                failRecord.setRefundId(e.getId());
                failRecord.setFailReason(o.getMessage());
                failRecord.setBatch(batch);
                bondExamineFailRecordService.save(failRecord);
            }
        });

        //查询成功多少条，失败多少条，返回给前端
        Long totoal = Long.parseLong(String.valueOf(list.size()));
        LambdaQueryWrapper<BondExamineFailRecord> failQuery = new LambdaQueryWrapper<>();
        failQuery.eq(BondExamineFailRecord::getBatch,batch);
        Long failCount = bondExamineFailRecordService.count(failQuery);
        map.put("successCount",(totoal-failCount));
        map.put("failCount",failCount);
        return map;
    }*/


    /**
     * 获取异常详细信息
     * @param ex
     * @return
     */
    public static String getExceptionAllinformation(Exception ex) {
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        PrintStream pout = new PrintStream(out);
        ex.printStackTrace(pout);
        String ret = new String(out.toByteArray());
        pout.close();
        try {
            out.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return ret;
    }

    /**
     * 退还到银行 调用银行转账接口
     * @param agentAccount
     */
    private void refund2Bank(BondAgentAccount agentAccount,BondRefundDetails d,TransferDataReq req,Integer reqType){
        BondPayInfoVo refund = new BondPayInfoVo();
        switch (BankTypeEnum.getBankType(agentAccount.getBankType())){
            case CHINA://中国银行
                if(d.getRefundOpenBank().indexOf(BankTypeEnum.CHINA.getDesc())>-1){
                    req.setToibkn("");//行内转账不需要银联号
                }
                refund = bankService.refund((TransferAccountReq)CommonUtil.toBean(agentAccount.getBankType(),req));
                break;
            case NANJING://南京银行
                if(d.getRefundOpenBank().indexOf(BankTypeEnum.NANJING.getDesc())>-1){
                    req.setBankFlag(NjBankConstans.BANK_FLAG_SELF);//银行标识 0-本行 1-他行
                }
                //南京银行的代理服务费，都走行内转账
                if(BondPayInfoReqTypeEnum.AGENT.getType().equals(reqType)){
                    req.setBankFlag(NjBankConstans.BANK_FLAG_SELF);//银行标识 0-本行 1-他行
                }
                refund =  bankService.acctPayTrade((NjBankDataReq)CommonUtil.toBean(agentAccount.getBankType(),req));
                break;
            //todo 民生银行暂时换中国银行
           /* case MINSHENG://民生银行
                if(d.getRefundOpenBank().indexOf(BankTypeEnum.MINSHENG.getDesc())>-1){
                    req.setToibkn("");//行内转账不需要银联号
                }
                refund = bankService.xfer((CmbcDataReq)CommonUtil.toBean(agentAccount.getBankType(),req));
                break;*/
            default://中国银行
                req.setFractnActacn(properties.getBondCollectNo());
                req.setActnam(properties.getBondCollectName());
                refund = bankService.refund((TransferAccountReq)CommonUtil.toBean(agentAccount.getBankType(),req));
                break;
        }

        //保证金类型
        if(BondPayInfoReqTypeEnum.BOND.getType().equals(reqType)){
            d.setStatus(BondRefundStatusEnum.FAIL.getType());
        }else if(BondPayInfoReqTypeEnum.AGENT.getType().equals(reqType)){//代理服务费
            d.setAgencyRefundStatus(BondRefundStatusEnum.FAIL.getType());
        }

        //删除上一次退还失败的数据
        LambdaQueryWrapper<BondPayInfo> removeQuery = new LambdaQueryWrapper<>();
        removeQuery.eq(BondPayInfo::getRefundDetailId,d.getId())
                .eq(BondPayInfo::getReqType,reqType)
                .eq(BondPayInfo::getType,agentAccount.getBankType())
                .eq(BondPayInfo::getStatus,PayStatusEnum.FAIL.getType());
        bondPayInfoService.remove(removeQuery);

        //插入转账记录表bond_pay_info
        BondPayInfo payInfo = new BondPayInfo();
        payInfo.setReqType(reqType);
        payInfo.setRefundDetailId(d.getId());
        payInfo.setType(agentAccount.getBankType());
        //成功
        if(PayStatusEnum.SUCCESS.getType().equals(refund.getStatus())){
            payInfo.setStatus(PayStatusEnum.SUCCESS.getType());
            payInfo.setSuccessData(refund.getSuccessData());
            payInfo.setReqNo(refund.getReqNo());

            //设置退还状态
            if(BondPayInfoReqTypeEnum.BOND.getType().equals(reqType)){
                d.setStatus(BondRefundStatusEnum.REFUNDING.getType());
            }else if(BondPayInfoReqTypeEnum.AGENT.getType().equals(reqType)){//代理服务费
                d.setAgencyRefundStatus(BondRefundStatusEnum.REFUNDING.getType());
            }
        }else{
            payInfo.setStatus(PayStatusEnum.FAIL.getType());
            payInfo.setErrorMsg(refund.getErrorMsg());
            //失败原因
            //{"msg":"联行号格式非法,请修改","insid":"221013135438ov","code":"B190","obssid":""}
            //{"tr_acdt":"********","tr_time":"110428","ret_info":"[IbE2003]账号户名不一致","tr_code":"800915","ret_code":"IbE2003","req_no":"48b2cb225bac4ba1849b30620ff4b9"}
            if (refund.getErrorMsg() != null || !refund.getErrorMsg().trim().equals("")) {
                cn.hutool.json.JSONObject fail = null;
                try {
                    fail = new cn.hutool.json.JSONObject(refund.getErrorMsg());
                }catch (Exception e)
                {
                    d.setFailReason(ExceptionEnum.SERVER_IS_BUSY.getMessage());
                }
                if(fail != null) {
                    if (BankTypeEnum.CHINA.getType().equals(agentAccount.getBankType())
                            || BankTypeEnum.OTHER.getType().equals(agentAccount.getBankType())
                            || BankTypeEnum.MINSHENG.getType().equals(agentAccount.getBankType())){
                        d.setFailReason(fail.getStr("msg"));
                    }
                    if (BankTypeEnum.NANJING.getType().equals(agentAccount.getBankType())) {
                        d.setFailReason(fail.getStr("ret_info"));
                    }
                   /* if (BankTypeEnum.MINSHENG.getType().equals(agentAccount.getBankType())) {
                        d.setFailReason(fail.getJSONArray("responseHeader").getJSONObject(0).getJSONArray("status").getJSONObject(0).getStr("message"));
                    }*/
                }
            }
        }
        bondPayInfoService.save(payInfo);
    }



    /**
     * 全部列表
     * @param page
     * @param condition
     * @return
     */
    private IPage<RefundListVo> getPageBondRefundList(IPage<RefundListVo> page,BondRefundCondition condition){
        IPage<RefundListVo> refundListVoIPage = null;
        refundListVoIPage = this.baseMapper.bondRefundListNew(page, condition);
       /* if(condition.getType() != null && condition.getType().equals(0)){
            //全部
            refundListVoIPage = this.baseMapper.bondRefundListForAll(page, condition);
        }else{
            refundListVoIPage = this.baseMapper.bondRefundListNew(page, condition);
        }*/
        BigDecimal totalAmount = new BigDecimal(0);
        List<RefundListVo> records = refundListVoIPage.getRecords();
        if (records.size() > 0) {
            totalAmount = records.stream().map(RefundListVo::getRefundMoney).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            records.get(0).setTotalAmount(totalAmount);
        }
        records.forEach(p->{
            //todo 中标情况导致加载太慢
         /*   if(p.getSectionId() != null && p.getCompanyId() != null) {
                BidWinSupplierVo bidWinInfo = getBidWinInfo(p.getSectionId(), p.getCompanyId());
                if(bidWinInfo != null) {
                    p.setBidType(bidWinInfo.getBidType());
                    p.setBidStatus(bidWinInfo.getBidStatus());
                }
            }*/

/*            if(p.getSectionId() != null){
                ProjectBidSection section = projectBidSectionService.getById(p.getSectionId());
                if(section != null){
                    p.setAgencyFeeObj(section.getAgencyFeeObj());
                    p.setFeeType(section.getAgencyFeeType());
                }
            }*/

           /* LambdaQueryWrapper<BondRefundDetails> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(BondRefundDetails::getRefundId,p.getRefundId());
            p.setBondRefundDetails(bondRefundDetailsService.list(queryWrapper));*/
        });
        refundListVoIPage.setRecords(records);
        return refundListVoIPage;
    }


    /**
     * 开启流程
     * @param id
     */
    private void startFlow(Long id){
        //开启流程引擎
        String str = flowApiClient.startProcessInstanceByKey(LoginUtil.getJwtUser().getUserOtherId(), FlowClientConstant.BOND_APPLY_REFUND, "保证金退还", "t_bond_refund:" + id, null,LoginUtil.getJwtUser().getUser().getDepartOtherId(),false);
        //判断推送到流程是否成功
        WorkFlowExceptionUtil.checkFlowResult(str);
        ReturnVo returnVo = JSONObject.parseObject(str, ReturnVo.class);
        if (returnVo != null && "100".equals(returnVo.getCode())) {
            String data = (String) returnVo.getData();
            BondRefund updateData = new BondRefund();
            updateData.setRefundProcessCode(data);
            updateData.setId(id);
            updateData.setStatus(BondRefundStatusEnum.WAIT.getType());
            updateById(updateData);
        } else {
            throw new WorkFlowException(500,returnVo.getMsg());
        }

        //插入流程记录
        ProcessRecordDTO recordDTO = new ProcessRecordDTO();
        recordDTO.setBusinessCode(FlowClientConstant.BOND_APPLY_REFUND);
        recordDTO.setBusinessId(id);
        recordDTO.setOperation("申请退还");
        recordDTO.setOperatorId(LoginUtil.getJwtUser().getUserId());
        recordDTO.setOperatorName(LoginUtil.getJwtUser().getUserName());
        processRecordService.addProcessRecord(recordDTO,LoginUtil.getJwtUser().getUserOtherId(),str);
    }

    /**
     * 判断流水是否已有退还流程
     * @param waterIdList
     * @return
     */
    @Override
    public Boolean judgeWaterInReturn(List<Long> waterIdList) {
        List<BondRefund> returnList = this.baseMapper.selectListInReturn(waterIdList);
        List<BondApplyRefund> applyRefundList = bondApplyRefundService.list(new LambdaQueryWrapper<BondApplyRefund>().in(BondApplyRefund::getWaterId, waterIdList).ne(BondApplyRefund::getStatus,BondApplyRefundStatus.RETURNED.getType()));
        if (returnList.size() > 0 || applyRefundList.size() > 0) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 刷新主表退还状态
     * @param refundId
     * @return
     */
    @Override
    public void refreshBondRefundStatus(Long refundId) {
        List<Long> refundIds = new ArrayList<>();
        // 查询退还中的数据
        List<BondPayInfoDto> list = bondPayInfoService.getRefreshData(refundId);
        for (BondPayInfoDto dto : list) {
            List<Long> ids = bondRefundDetailsService.refreshBondPayStatus(dto);
            refundIds.addAll(ids);
        }
        if(refundId != null && !refundIds.contains(refundId)){
            refundIds.add(refundId);
        }
        for (Long id : refundIds) {
            LambdaQueryWrapper<BondRefundDetails> detailsLambdaQueryWrapper = new LambdaQueryWrapper<>();
            detailsLambdaQueryWrapper.eq(BondRefundDetails::getRefundId, id);
            List<BondRefundDetails> details = bondRefundDetailsService.list(detailsLambdaQueryWrapper);
            Integer refundStatus = this.judgeStatus(details);
            this.update(new LambdaUpdateWrapper<BondRefund>().set(BondRefund::getStatus, refundStatus).eq(BondRefund::getId, id));
            if(BondRefundStatusEnum.SUCCESS.getType().equals(refundStatus)){
                //若主表状态成功，更新保证金余额表
                bondRefundBalanceService.batchUpdateRefund(details);
            }
        }
    }

    /**
     * 批量刷新主表退还状态
     * @param refundIdList
     * @return
     */
    public void batchRefreshBondRefundStatus(List<Long> refundIdList) {
        // 查询退还中的数据
//        List<BondPayInfoDto> list = bondPayInfoService.getBatchRefreshData(refundIdList);
//        for (BondPayInfoDto dto : list) {
//            bondRefundDetailsService.refreshBondPayStatus(dto);
//        }
//        for (Long id : refundIdList) {
//            LambdaQueryWrapper<BondRefundDetails> detailsLambdaQueryWrapper = new LambdaQueryWrapper<>();
//            detailsLambdaQueryWrapper.eq(BondRefundDetails::getRefundId, id);
//            List<BondRefundDetails> details = bondRefundDetailsService.list(detailsLambdaQueryWrapper);
//            bondRefundBalanceService.batchUpdateRefund(details);
//            Integer refundStatus = this.judgeStatus(details);
//            this.update(new LambdaUpdateWrapper<BondRefund>().set(BondRefund::getStatus, refundStatus).eq(BondRefund::getId, id));
//        }
        for(Long refundId:refundIdList){
            this.refreshBondRefundStatus(refundId);
        }
    }

    /**
     * 打印保证金退还申请表
     * @param req
     * @return
     */
    @Override
    public List<BondProjectDTO> printBondRefundApply2(RefundSectionReq req) {
        //Map<Long,List<BondSupplierVO>> sectionMap = new HashMap<>();
       // req.getCompanySection().forEach(c-> {
          //          List<BondSupplierVO> suppliers = this.baseMapper.printBondRefundApply2(c.getSectionId(), c.getCompanyId());
        //查询map中是否存在供应商信息   老需求，一个标段对应多个供应商信息
           /* List<BondSupplierVO> supplierList = sectionMap.get(c.getSectionId());
            if(supplierList != null && supplierList.size()>0){
                supplierList.addAll(suppliers);
            }else{
                supplierList = suppliers;
            }
            sectionMap.put(c.getSectionId(), supplierList);
                }
                getPrintBondRefundApplys(sectionMap);*/
        List<BondProjectDTO> applys = new ArrayList<>();
        req.getCompanySection().forEach(c-> {
                List<BondSupplierVO> suppliers = this.baseMapper.printBondRefundApply2(c.getSectionId(), c.getCompanyId());
                List<BondDetailDTO> details = new ArrayList<>();
                BidWinSupplierVo bidWinInfo = getBidWinInfo(c.getSectionId(), suppliers.get(0).getSupplierId());
                BondDetailDTO bondDetailDTO = new BondDetailDTO();
                bondDetailDTO.setCompanyName(suppliers.get(0).getCompanyName());
                bondDetailDTO.setType(bidWinInfo.getBidStatus());
                bondDetailDTO.setPayMethond(BondPayMethondEnum.E_BANK.getDesc());
                details.add(bondDetailDTO);
                //获取保证金退还封装数据
                BondProjectDTO datas = bondService.getBondRefundApplyDatas(suppliers.get(0).getProjectId(), c.getSectionId());
                datas.setBondDetail(details);
                datas.setSubmitTime(suppliers.get(0).getSubmitTime());
                applys.add(datas);
        });
        //设置按照提交时间倒序
        applys.stream().sorted(Comparator.comparing(BondProjectDTO::getSubmitTime).reversed());
        return applys;
    }

    /**
     * 打印中标通知书存根
     * @param req
     * @return
     */
    @Override
    public List<String> printBidWinNoticeStub2(RefundSectionReq req) {
        List<Long> stubFiles = new ArrayList<>();

        req.getCompanySection().forEach(p->{
            //查询相同供应商的存根信息
            Map<String, Object> param = new HashMap<>();
            param.put("sectionId", p.getSectionId());
            param.put("bidRound", BidRoundEnum.HS.getType());
            param.put("tendererId", p.getCompanyId());
            StubVo stubVo = bidWinPeopleService.getSupplierStubNoNotNull(param);
            if(stubVo != null && stubVo.getStubFile() != null){
                if(!stubFiles.contains(stubVo.getStubFile())){
                    stubFiles.add(stubVo.getStubFile());
                }
            }
        });
        List<String> allFiles = new ArrayList<>();
        stubFiles.forEach(f->{
            OssFile ossFile = ossFileService.getById(f);
            //下载文件
            OssFileStorage ossFileStorage = OssUtil.getOssFileStorage(ossType);
            byte[] bytes = ossFileStorage.downloadFile(ossFile.getOssFileKey());
            List<String> jpgs = FileUtils.pdf2Base64(bytes);
            allFiles.addAll(jpgs);
            bytes=null;
        });
        return allFiles;
    }


    /**
     * 查询保证金退还申请表
     * @param sectionMap
     * @return
     */
    private  List<BondProjectDTO> getPrintBondRefundApplys(Map<Long,List<BondSupplierVO>> sectionMap){
        List<BondProjectDTO> applys = new ArrayList<>();
        Set<Map.Entry<Long, List<BondSupplierVO>>> es = sectionMap.entrySet();
        Iterator<Map.Entry<Long, List<BondSupplierVO>>> it = es.iterator();
        while(it.hasNext()){
            Map.Entry<Long, List<BondSupplierVO>> map = it.next();
            Long sectionId = map.getKey();
            List<BondSupplierVO> supplierList = map.getValue();
            for (BondSupplierVO supplierVO : supplierList) {
                List<BondDetailDTO> details = new ArrayList<>();
                BidWinSupplierVo bidWinInfo = getBidWinInfo(sectionId, supplierVO.getSupplierId());
                BondDetailDTO bondDetailDTO = new BondDetailDTO();
                bondDetailDTO.setCompanyName(supplierVO.getCompanyName());
                bondDetailDTO.setType(bidWinInfo.getBidStatus());
                bondDetailDTO.setPayMethond(BondPayMethondEnum.E_BANK.getDesc());
                details.add(bondDetailDTO);
            //获取保证金退还封装数据
            BondProjectDTO datas = bondService.getBondRefundApplyDatas(supplierList.get(0).getProjectId(), sectionId);
            datas.setBondDetail(details);
            applys.add(datas);
            }
        }
        return applys;
    }





    @Override
    public List<UnreturnedBondDto> findUnreturnedBond(String startTime, String endTime) {
        List<UnreturnedBondDto> list = this.baseMapper.findUnreturnedBond(startTime,endTime);
        for (UnreturnedBondDto d : list) {
            String substring = d.getPurchaseNumber().substring(7, 10);
            d.setDeptCode(this.baseMapper.findDeptCode(substring));
            d.setDeptName(this.baseMapper.findDeptName(substring));
            //中标候选人
            //先查是否发布中标候选人公示
            Integer isCandidate = this.baseMapper.getCandidatePeople(d.getSectionId(),d.getCompanyId());
            //未发布，则查询确认中标候选人
            if(null == isCandidate){
                QueryWrapper<BidWinPeople> queryWrapper = new QueryWrapper<>();
                queryWrapper.lambda().eq(BidWinPeople::getSectionId,d.getSectionId()).eq(BidWinPeople::getWinPeopleType,BidWinPeopleTypeEnum.OPEN_BID.getCode()).eq(BidWinPeople::getTendererId,d.getCompanyId());
                BidWinPeople one = bidWinPeopleService.getOne(queryWrapper);
                if(BeanUtil.isNotEmpty(one)){
                    isCandidate = one.getIsCandidate();
                }
            }
            d.setIsCandidate(null != isCandidate?isCandidate:CommonConstants.NO2);
        }
        return list;
    }

    @Override
    public Result<BatchRefundRecordVo> applyRefundBatch(List<BondRefundFeq> reqList) {
        //校验参数
        Result<String> checkResult = checkApplyBatchParams(reqList);
        if(!checkResult.getFlag()){
            return Result.failed(checkResult.getMsg());
        }
        //退还表Id
        List<Long> applyRefundIds = new ArrayList<>();
        //退还表实体
        List<BondRefund> applyRefunds = new ArrayList<>();
        //增加退还详情
        List<BondRefundDetails> addDetails = new ArrayList<>();
        //返回参数
        BatchRefundRecordVo batchRefundRecordVo = new BatchRefundRecordVo();
        Integer successCount = 0;
        Integer failCount = 0;
        for(BondRefundFeq req:reqList){
            try{
                BondRefund applyRefund = BeanListUtil.convert(req,BondRefund.class);
                applyRefund.setStatus(BondRefundStatusEnum.WAIT.getType());
                applyRefund.setIsCalculation(CommonConstants.YES);
                if(req.getSubmitTime() == null){
                    applyRefund.setSubmitTime(new Date());
                }
                //修改
                if(req.getId() != null){
                    applyRefund.setId(req.getId());
                }
                applyRefund.setRates(new BigDecimal("0"));
                //需要先生成退款id
                saveOrUpdate(applyRefund);

                //插入退款详情表
                List<BondRefundDetails> details = BeanListUtil.convertList(req.getBondRefundDetails(),BondRefundDetails.class);
                for (BondRefundDetails detail : details) {
                    detail.setRates(new BigDecimal(0));
                    detail.setRefundId(applyRefund.getId());
                    detail.setStatus(BondRefundStatusEnum.WAIT.getType());
                    BigDecimal refundDetaisMoney = detail.getBondMoney();
                    if(detail.getAgencyFee() != null){
                        refundDetaisMoney = detail.getBondMoney().subtract(detail.getAgencyFee());
                    }
                    detail.setRefundMoney(refundDetaisMoney.doubleValue()<0?new BigDecimal(0):refundDetaisMoney);
                }
                //计算退还金额
                calculationRates(applyRefund,details,false);
                applyRefundIds.add(applyRefund.getId());
                applyRefunds.add(applyRefund);
                //插入退款详情
                addDetails.addAll(details);
                successCount++;
            }catch (Exception e){
                log.error("退还保证金批量申请失败:{}",e);
                failCount++;
            }
        }
        //删除退款详情表
        LambdaQueryWrapper<BondRefundDetails> detailsQuery = new LambdaQueryWrapper<>();
        detailsQuery.in(BondRefundDetails::getRefundId,applyRefundIds);
        List<BondRefundDetails> historyDetails = bondRefundDetailsService.list(detailsQuery);
        bondRefundDetailsService.removeBatchByIds(historyDetails);
        //插入退款详情表
        bondRefundDetailsService.saveBatch(addDetails);

        updateBatchById(applyRefunds);
        //开启流程
        applyRefunds.forEach(r->{
            startFlow(r.getId());
        });
        //如果是退回后申请退还，删除待处理事项表数据
        LambdaQueryWrapper<PendingItem> pendingItemLambdaQueryWrapper = new LambdaQueryWrapper<>();
        pendingItemLambdaQueryWrapper.eq(PendingItem::getBusinessCode, ReturnListEnum.ONLINE_BOND_RETURN.getCode());
        pendingItemLambdaQueryWrapper.in(PendingItem::getBusinessId,applyRefundIds);
        pendingItemService.remove(pendingItemLambdaQueryWrapper);
        batchRefundRecordVo.setSuccessCount(successCount);
        batchRefundRecordVo.setFailCount(failCount);
        return Result.ok(batchRefundRecordVo);
    }

    /**
     * 补缴代理服务费
     * @param req
     * @return
     */
    @Override
    public Boolean saveAgencyFeeFile(BondAgencyFeeFileReq req) {
        BondRefund bondRefund = this.getById(req.getRefundId());
        bondRefund.setFileIds(req.getFileId().toString());
        bondRefund.setIsAgencyFee(CommonConstants.YES);
        return this.updateById(bondRefund);
    }

    @Override
    public Result<String> withdrawRefund(BondWithdrawFeq req) {
        //校验财务是否已审批
        BondRefund bondRefund = bondRefundService.getById(req.getRefundId());
//        if(bondRefund!=null&&1!=bondRefund.getStatus()){
//            return Result.failed("财务已审批，无法撤回！");
//        }
        if(null!=bondRefund.getRefundProcessCode()){
            String taskRole = flowApiClient.getTaskRoleByProcessInstanceId(null, bondRefund.getRefundProcessCode());
            JSONObject taskRoleJsonObject = JSON.parseObject(taskRole);
            List<TaskRoleVo> taskRoleVoList = JSONObject.parseArray(taskRoleJsonObject.getString("data"), TaskRoleVo.class);
            if(CollectionUtils.isNotEmpty(taskRoleVoList)){
                if (null != taskRoleVoList.get(0).getRoleCode() && !taskRoleVoList.get(0).getRoleCode().equals(RoleCodeEnum.CWSJCW.getType())){
                    return Result.failed("财务已审批，无法撤回！");
                }
            }
        }
        //修改这些数据的状态为已撤回
        bidWinMapper.updateBondStatusWithdrawn(req.getRefundId());
        bidWinMapper.updateBondStatusWithdrawnForDetails(req.getRefundId());
        List<String> tableNames = new ArrayList<>();
        tableNames.add(FlowClientConstant.BOND_APPLY_REFUND);
        //撤回正在执行中的审批流程
        withdrawnFlow(tableNames,req.getRefundId(),ProcessRecordEnum.BOND_APPLY_REFUND.getType());
        return Result.ok();
    }

    /**
     * 撤回正在执行中的审批流程
     */
    private void withdrawnFlow(List<String> tableNames,Long businessId,String processCode){
        if(!tableNames.isEmpty()){
            tableNames.forEach(tableName->{
                flowApiClient.revokeProcess(LoginUtil.getJwtUser().getUserOtherId(), tableName + ":" + businessId, "撤回");
            });
        }

        //插入流程记录
        ProcessRecordDTO recordDTO = new ProcessRecordDTO();
        recordDTO.setBusinessCode(processCode);
        recordDTO.setBusinessId(businessId);
        recordDTO.setOperation("撤回");
        recordDTO.setOperatorId(LoginUtil.getJwtUser().getUserId());
        recordDTO.setOperatorName(LoginUtil.getJwtUser().getUserName());
        recordDTO.setRemark("撤回保证金退还");
        processRecordService.addProcessRecord(recordDTO, LoginUtil.getJwtUser().getUserOtherId(), null);
    }

    private Result<String> checkApplyBatchParams(List<BondRefundFeq> reqList){
        List<BondRefundDetailsVo> detailList = new ArrayList<>();
        for(BondRefundFeq req:reqList){
            // 判断是否为非中标人
            LambdaQueryWrapper<BidWinPeople> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(BidWinPeople::getSectionId,req.getSectionId()).eq(BidWinPeople::getTendererId,req.getCompanyId())
                    .eq(BidWinPeople::getIsWin,CommonConstants.YES).eq(BidWinPeople::getWinPeopleType,BidWinPeopleTypeEnum.NOTICE.getCode());
            BidWinPeople one = bidWinPeopleService.getOne(queryWrapper);
            if(BeanUtil.isNotEmpty(one)){
                return Result.failed(MessageConstants.PARAMS_NOT_NULL);
            }
            // 判断流水是否关联
            LambdaQueryWrapper<BondRelation> relationQueryWrapper = new LambdaQueryWrapper<>();
            relationQueryWrapper.eq(BondRelation::getCompanyId,req.getCompanyId()).eq(BondRelation::getSectionId,req.getSectionId());
            List<BondRelation> list = bondRelationService.list(relationQueryWrapper);
            relationQueryWrapper.eq(BondRelation::getStatus,RelationStatusEnum.HAS_RELATE.getType());
            List<BondRelation> list1 = bondRelationService.list(relationQueryWrapper);
            if(CollectionUtils.isEmpty(list) && CollectionUtils.isEmpty(list1) && list.size()!= list1.size() ){
                return Result.failed(MessageConstants.PARAMS_NOT_NULL);
            }
            LambdaQueryWrapper<BondRefund> refundQueryWrapper = new LambdaQueryWrapper<>();
            refundQueryWrapper.eq(BondRefund::getCompanyId,req.getCompanyId()).eq(BondRefund::getSectionId,req.getSectionId());
            BondRefund one2 = this.getOne(refundQueryWrapper);
            if(BeanUtil.isNotEmpty(one2) && !BondRefundStatusEnum.RETURNED.getType().equals(one2.getStatus())){
                if(!BondRefundStatusEnum.WITHDRAWED.getType().equals(one2.getStatus())){
                    return Result.failed(MessageConstants.PARAMS_NOT_NULL);
                }
            }
            if(req.getWaterId() != null){
                req.setWaterIds(req.getWaterId().toString());
            }
            //验证保证金参数合法性
            if(!checkBondRefund(req)){
                return Result.failed(MessageConstants.PARAMS_NOT_NULL);
            }
            detailList.addAll(req.getBondRefundDetails());
        }
        //验证保证金账户余额
        List<BondRefundDetails> details = BeanListUtil.convertList(detailList,BondRefundDetails.class);
        for(BondRefundDetails detail:details){
            if(detail.getBondMoney()!=null&&detail.getBondMoney().compareTo(BigDecimal.ZERO)<0){
                return Result.failed("金额不能为负数");
            }
        }
        if(!bondRefundBalanceService.checkBondBalance(details)){
            return Result.failed("保证金账户余额不足，存在重复退款风险，不允许退款，若仍需退款请联系技术人员处理");
        }
        return Result.ok();
    }

}
