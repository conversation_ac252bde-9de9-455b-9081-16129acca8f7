package com.hzw.sunflower.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.controller.request.*;
import com.hzw.sunflower.controller.response.BondRelationDetailVo;
import com.hzw.sunflower.controller.response.BondRelationSectionVo;
import com.hzw.sunflower.controller.response.BondRelationVo;
import com.hzw.sunflower.entity.BondRelation;
import com.hzw.sunflower.entity.BondWater;
import com.hzw.sunflower.entity.JwtUser;
import com.hzw.sunflower.entity.condition.BondRelationCondition;
import org.apache.ibatis.annotations.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 保证金关联关系表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-27
 */
public interface BondRelationService extends IService<BondRelation> {

    /**
     * 分页查询供应商关注标段保证金关联列表
     * @param condition
     * @param jwtUser
     * @return
     */
    IPage<BondRelationSectionVo> getSectionRelationList(BondRelationCondition condition, JwtUser jwtUser);

    /**
     * 查询标段关联情况
     * @param condition
     * @return
     */
    BondRelationVo getSectionRelationDetail(BondRelationCondition condition);

    /**
     * 根据流水ids查询关联标段
     * @param waterIds
     * @param relationStatus
     * @return
     */
    List<BondRelationSectionVo> getSectionListByWaterIds(List<Long> waterIds, Integer relationStatus);

    /**
     * 根据流水ids查询关联标段
     * @param waterIds
     * @param relationStatus
     * @return
     */
    List<BondRelationSectionVo> getSectionListByWaterIdsCompleted(List<Long> waterIds, Integer relationStatus, Date createdTime);

    /**
     * 取消删除
     * @param condition
     * @return
     */
    Boolean cancelRelation(BondRelationCondition condition);

    /**
     * 确认关联
     * @param condition
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    Boolean confirmRelation(BondRelationCondition condition);

    /**
     * 校验标段是否超过开标时间和是否已购标
     * @param sectionId
     * @param companyId
     */
    void checkSection(String sectionId, Long companyId);

    /**
     * 项目经理校验是否可以进入关联页面
     * @param request
     * @return
     */
    Boolean validRelationSectionInRefund(BondRelationReq request);

    /**
     * 供应商/项目经理 关联详情
     * @param request
     * @return
     */
    BondRelationDetailVo getRelationDetail(BondRelationReq request);

    /**
     * 运管处关联详情
     * @param request
     * @return
     */
    BondRelationDetailVo getRelationDetailByAgent(BondRelationAgentReq request);

    /**
     * 新增关联
     * @param req
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    Boolean addRelation(AddRelationReq req);

    /**
     * 自动关联
     * @param water
     * @return
     */
    Boolean autoRelate(BondWater water);

    /**
     * 新增异常关联
     * @param req
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    Boolean addExceptionRelation(ExceptionRelationReq req);

    /**
     * 运管处搜索关联标段
     * @param request
     * @return
     */
    List<BondRelationSectionVo> getSectionListByProjectId(BondRelationAgentReq request);

    /**
     * 运管处关联详情-模糊搜索关注项目
     * @param request
     * @return
     */
    List<BondRelationSectionVo> getSectionListByPurchaseNum(BondRelationAgentReq request);

    /**
     * 异常关联退回
     * @param req
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    Boolean applyRelationReturn(ExceptionRelationApplyReq req);

    /**
     * 异常关联同意
     * @param req
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    Boolean applyRelationAgree(ExceptionRelationApplyReq req);

    /**
     * 异常关联处理详情
     * @param applyId
     * @return
     */
    BondRelationVo getExceptionRelationDetail(Long applyId);

    /**
     * 异常关联处理详情(已办)
     * @param applyId
     * @return
     */
    BondRelationVo getExceptionRelationDetailCompleted (Long applyId);

    /**
     * 查询关联表中是否有删除记录
     * @param waterId
     * @return
     */
    Integer getDeleteCountByWaterId(Long waterId);
    /**
     * 校验是够关联
     * @param waterId
     * @return
     */
    Integer checkBondRelationByWaterId( Long waterId);

    /**
     * 校验开户行信息
     * @param waterIds
     * @return
     */
    Result<List<BondWater>> checkOpenAccount(List<Long> waterIds);

}
