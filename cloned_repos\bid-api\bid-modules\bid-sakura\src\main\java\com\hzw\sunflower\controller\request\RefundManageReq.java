package com.hzw.sunflower.controller.request;

import com.hzw.sunflower.common.BaseCondition;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date ：Created in 2023/8/15 15:01
 * @description：审核req
 * @modified By：`
 * @version: 1.0
 */
@Data
@ApiModel(description = "RefundManageReq")
public class RefundManageReq extends BaseCondition {

    @ApiModelProperty(value = "关键字")
    private String keyWords;

    @ApiModelProperty(value = "权限sql")
    private String dataScope;
}
