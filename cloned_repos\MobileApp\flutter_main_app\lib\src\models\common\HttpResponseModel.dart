///***
/// * HTTP 请求返回类型
/// */

import 'package:flutter/material.dart';
import 'package:json_annotation/json_annotation.dart';

import 'package:flutter_main_app/src/utils/GenericConverter.dart';

part 'HttpResponseModel.g.dart';

@JsonSerializable(explicitToJson: true)
class HttpResponseModel<T> {

  const HttpResponseModel({
    @required this.success,
    @required this.data,
    @required this.errorMessage,
    @required this.code,
  });

  final bool success;

  @GenericConverter()
  final T data;

  final String errorMessage;

  final int code;

  factory HttpResponseModel.fromJson(Map<String, dynamic> json) => _$HttpResponseModelFromJson<T>(json);

  Map<String, dynamic> toJson() => _$HttpResponseModelToJson(this);

}
