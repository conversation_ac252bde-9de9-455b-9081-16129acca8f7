package com.hzw.ssm.applets.action;

import com.hzw.ssm.applets.entity.ProQualification;
import com.hzw.ssm.applets.service.ProQualificationService;
import com.hzw.ssm.applets.utils.GetJsonReqData;
import com.hzw.ssm.applets.utils.GetJsonRespData;
import com.hzw.ssm.applets.utils.JsonToObject;
import com.hzw.ssm.applets.utils.ResultJson;
import com.hzw.ssm.fw.base.BaseAction;
import net.sf.json.JSONObject;
import org.apache.struts2.convention.annotation.Action;
import org.apache.struts2.convention.annotation.Namespace;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 执业资格
 *
 * <AUTHOR>
 * @Date 2021-02-03 10:17
 * @Version 1.0
 */
@Namespace("/applets/proQualificationAction")
public class ProQualificationAction extends BaseAction {

    @Autowired
    private ProQualificationService proQualificationService;

    /**
     * 新增
     *
     * @return
     */
    @Action("save")
    public String save() {

        JSONObject jsonReqData = GetJsonReqData.getJsonReqData();
        String toString = jsonReqData.getJSONObject("formData").toString();
        ProQualification proQualification = JsonToObject.jsonToObject(toString, ProQualification.class);
        ResultJson resultJson = proQualificationService.insertProQualification(proQualification);
        GetJsonRespData.getJsonRespData(resultJson);
        return null;
    }

    /**
     * 编辑
     *
     * @return
     */
    @Action("update")
    public String update() {

        JSONObject jsonReqData = GetJsonReqData.getJsonReqData();
        String toString = jsonReqData.getJSONObject("formData").toString();
        ProQualification proQualification = JsonToObject.jsonToObject(toString, ProQualification.class);
        ResultJson resultJson = proQualificationService.updateProQualification(proQualification);
        GetJsonRespData.getJsonRespData(resultJson);
        return null;
    }

    /**
     * 删除
     *
     * @return
     */
    @Action("delete")
    public String delete() {

        JSONObject jsonReqData = GetJsonReqData.getJsonReqData();
        String toString = jsonReqData.getJSONObject("formData").toString();
        ProQualification proQualification = JsonToObject.jsonToObject(toString, ProQualification.class);
        ResultJson resultJson = proQualificationService.deleteProQualification(proQualification);
        GetJsonRespData.getJsonRespData(resultJson);
        return null;
    }

    /**
     * 查询单个
     *
     * @return
     */
    @Action("getOne")
    public String getOne() {

        JSONObject jsonReqData = GetJsonReqData.getJsonReqData();
        String toString = jsonReqData.getJSONObject("formData").toString();
        ProQualification proQualification = JsonToObject.jsonToObject(toString, ProQualification.class);
        ResultJson resultJson = proQualificationService.getProQualification(proQualification);
        GetJsonRespData.getJsonRespData(resultJson);
        return null;
    }

    /**
     * 查询所有
     *
     * @return
     */
    @Action("all")
    public String all() {

        JSONObject jsonReqData = GetJsonReqData.getJsonReqData();
        String toString = jsonReqData.getJSONObject("formData").toString();
        ProQualification proQualification = JsonToObject.jsonToObject(toString, ProQualification.class);
        ResultJson resultJson = proQualificationService.getProQualifications(proQualification);
        GetJsonRespData.getJsonRespData(resultJson);
        return null;
    }
}
