server:
  port: 28925
  servlet:
    context-path: /filecompare
  max-http-header-size: 100000
#datasource
spring:
  main:
    allow-circular-references: true
  application:
    name: bid-filecompare
  datasource:
    url: ************************************************************************************************************************************************************************
    username: bid_test
    password: rDrMbEhD6GbXejbp
    driver-class-name: com.mysql.cj.jdbc.Driver
    #Spring Boot 默认是不注入这些属性值的，需要自己绑定
    #druid 数据源专有配置
    initialSize: 5
    minIdle: 5
    maxActive: 20
    maxWait: 60000
    timeBetweenEvictionRunsMillis: 60000
    minEvictableIdleTimeMillis: 300000
    validationQuery: SELECT 1 FROM DUAL
    testWhileIdle: true
    testOnBorrow: false
    testOnReturn: false
    poolPreparedStatements: true

    #配置监控统计拦截的filters，stat:监控统计、log4j：日志记录、wall：防御sql注入
    #如果允许时报错  java.lang.ClassNotFoundException: org.apache.log4j.Priority
    #则导入 log4j 依赖即可，Maven 地址：https://mvnrepository.com/artifact/log4j/log4j
    filters: stat,wall,log4j
    maxPoolPreparedStatementPerConnectionSize: 20
    useGlobalDataSourceStat: true
    connectionProperties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=500
  jackson:
    # 全局设置@JsonFormat的格式pattern
    date-format: yyyy-MM-dd HH:mm:ss
    # 当地时区
    locale: zh
    # 设置全局时区
    time-zone: GMT+8
    # 常用，全局设置pojo或被@JsonInclude注解的属性的序列化方式
    #    default-property-inclusion: non_null #不为空的属性才会序列化,具体属性可看JsonInclude.Include
    # 常规默认,枚举类SerializationFeature中的枚举属性为key，值为boolean设置jackson序列化特性,具体key请看SerializationFeature源码
    serialization:
      write-dates-as-timestamps: false # 返回的java.util.date转换成timestamp
      FAIL_ON_EMPTY_BEANS: true # 对象为空时是否报错，默认true
  servlet:
    multipart:
      # 开启 multipart 上传功能
      enabled: true
      # 文件写入磁盘的阈值
      file-size-threshold: 2KB
      # 最大文件大小
      max-file-size: 200MB
      # 最大请求大小
      max-request-size: 215MB

  resources:
    add-mappings: true
  mvc:
    pathmatch:
      matching-strategy: ANT_PATH_MATCHER
  throw-exception-if-no-handler-found: true
  redis:
    # 地址
    host: **************
    # 端口，默认为6379
    port: 26379
    # 数据库索引
    database: 0
    # 密码
    password: TR0Y9s27Oda6oqKs
    # 连接超时时间
    timeout: 10s
    lettuce:
      pool:
        # 连接池中的最小空闲连接
        min-idle: 0
        # 连接池中的最大空闲连接
        max-idle: 8
        # 连接池的最大数据库连接数
        max-active: 8
        # #连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1ms
  rabbitmq:
    # 地址
    host: **************
    # 端口
    port: 25672
    # 账号
    username: hzw
    # 密码
    password: TR0Y9s27Oda6oqKs
    publisher-confirm-type: correlated
    publisher-returns: true
    listener:
      simple:
        retry:
          enabled: true
          max-attempts: 3
          initial-interval: 2000
        default-requeue-rejected: false

#log
logging:
  # 过滤开关
  enabled: true
  level:
    root: INFO
    com:
      hzw:
        sunflower: trace
  file:
    path: /log
  logback:
    rollingpolicy:
      max-file-size: 100MB
      max-history: 600

swagger:
  urlPrefix: http://
  termsOfServiceUrl: /doc.html
knife4j:
  enable: true
  openapi:
    title: 接口
    description: "接口"
    email: xxxx
    concat: noticeClaim
    url: https://127.0.0.1
    version: v2.3
    license: Apache 2.0
    license-url: https://127.0.0.1
    terms-of-service-url: https://127.0.0.1
    group:
      test1:
        group-name: 分组
        api-rule: package
        api-rule-resources:
          - com.hzw.sunflower

mybatis-plus:
  global-config:
    #数据库相关配置
    db-config:
      #主键类型  AUTO:"数据库ID自增", INPUT:"用户输入ID", ID_WORKER:"全局唯一ID (数字类型唯一ID)", UUID:"全局唯一ID UUID";
      id-type: AUTO
  configuration:
    map-underscore-to-camel-case: true
    typeAliasesPackage: com.hzw.sunflower.entity
    mapper-locations: mapper/**/*Mapper.xml
    config-location: mybatis/mybatis-config.xml
    #logic-delete-field: isDelete  # 全局逻辑删除的实体字段名(since 3.3.0,配置后可以忽略不配置步骤2)
    logic-delete-value: 1 # 逻辑已删除值(默认为 1)
    logic-not-delete-value: 0 # 逻辑未删除值(默认为 0)
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    default-scripting-language: com.hzw.sunflower.mybatis.SunflowerMybatisLanguageDriver

# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /system/notice/*
  # 匹配链接（多个用逗号分隔）
  urlPatterns: /company/*,/monitor/*,/tool/*

#模板文件配置
files:
  template:
    path: /sunflower/template/
  temporary:
    path: /sunflower/temporary/
  sign:
    path: /data/esign/
email:
  qq:
    mailServerHost: imap.qq.com
    mailServerPort: 993
  temporary:
    path: /sunflower/temporary/

#短信配置
sms:
  url: http://**************:29015/sms/v2/std/
  token: 3f89deea35258fa9ab3c629751e963f9b5a9113f926f576d07c44b283729275d
  validate: false


#当前系统租户信息标志当前系统的管理方（组织机构代码）
organ:
  organization_num: 91320000134750085T
  organization_type: 2
  org_phone: ************
  org_website: www.jstcc.cn

oss:
  active: ctyun
  aliyun:
    endpoint: https://oss-cn-hangzhou.aliyuncs.com
    accessKeyId: xxxxxx
    accessKeySecret: xxxxxxxxxxxxxxx
    bucketName: jtcc-dev
  ctyun:
    endpoint: oos-js.ctyunapi.cn
    accessKeyId: xxxxxxxxxxxxxxx
    accessKeySecret: xxxxxxxxxxxxxxx
    bucketName: jtcc-dev

invoice:
  retryCount: 100

#运维人员手机号发送
smsMember:
  member0: 18005160092
  member1: 18626438914
  member2: 18501600548
  memberCw: 13912941328
