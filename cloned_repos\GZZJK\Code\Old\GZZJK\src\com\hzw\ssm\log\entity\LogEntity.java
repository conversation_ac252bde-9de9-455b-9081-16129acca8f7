package com.hzw.ssm.log.entity;

import java.util.Date;

public class LogEntity {

	private String id; // 日志编号

	private String userId; // 用户编号

	private Date opaDate; // 操作时间

	private String opaIp; // IP地址

	private String pageName; // 操作页面编号

	private String methodName; // 操作方法名称

	private String opaType; // 操作类型

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getUserId() {
		return userId;
	}

	public void setUserId(String userId) {
		this.userId = userId;
	}

	public Date getOpaDate() {
		return opaDate;
	}

	public void setOpaDate(Date opaDate) {
		this.opaDate = opaDate;
	}

	public String getOpaIp() {
		return opaIp;
	}

	public void setOpaIp(String opaIp) {
		this.opaIp = opaIp;
	}

	public String getPageName() {
		return pageName;
	}

	public void setPageName(String pageName) {
		this.pageName = pageName;
	}

	public String getMethodName() {
		return methodName;
	}

	public void setMethodName(String methodName) {
		this.methodName = methodName;
	}

	public String getOpaType() {
		return opaType;
	}

	public void setOpaType(String opaType) {
		this.opaType = opaType;
	}

}
