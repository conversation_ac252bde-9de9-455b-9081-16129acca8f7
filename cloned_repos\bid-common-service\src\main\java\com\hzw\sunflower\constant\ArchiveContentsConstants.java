package com.hzw.sunflower.constant;

/**
 * <AUTHOR>
 * @date ：Created in 2021/7/30
 * @description：归档文件目录常量
 * @modified By：`
 * @version: 1.0
 */
public class ArchiveContentsConstants {

    /**
     * 委托协议书/委托代理合同
     */
    public static final String ENTRUST = "entrust";

    /**
     * 招标公告/招标邀请函
     */
    public static final String BID_NOTICE_CONTENT = "bid_notice_content";

    /**
     * 报名公告
     */
    public static final String BID_SIGN_UP_NOTICE_CONTENT = "sign_up_notice_content";

    /**
     * 招标文件（含修改内容）
     */
    public static final String BID_NOTICE_DOC = "bid_notice_doc";

    /**
     * 报名文件（含修改内容）
     */
    public static final String BID_SIGN_UP_NOTICE_DOC = "sign_up_notice_doc";
    /**
     * 出售招标文件登记表
     */
    public static final String SALE_BID_REGISTRATION_FORM = "sale_bid_registration_form";
    /**
     * 出售报名文件登记表
     */
    public static final String SALE_SIGN_UP_REGISTRATION_FORM = "sale_sign_up_registration_form";
    /**
     * 投标登记表
     */
    public static final String BID_REGISTRATION_FORM = "bid_registration_form";

    /**
     * 开标一览表
     */
    public static final String BID_OPENING_SCHEDULE = "bid_opening_schedule";

    /**
     * 开标记录表
     */
    public static final String BID_OPEN_RECORD = "bid_open_record";

    /**
     * 询标记录
     */
    public static final String BID_INQUIRY_RECORD = "bid_inquiry_record";

    /**
     * 专家抽取记录、签到表
     */
    public static final String EXPERT_EXTRACT_SIGN = "expert_extract_sign";

    /**
     * 评委签到表
     */
    public static final String JUDGES_SIGN = "judges_sign";

    /**
     * 招标人代表参加评委会的授权函
     */
    public static final String TENDEREE_DELEGATE_JURY_LETTER = "tenderee_delegate_jury_letter";

    /**
     * 评标记录
     */
    public static final String BID_EVALUATION_RECORD = "bid_evaluation_record";

    /**
     * 评标报告
     */
    public static final String BID_EVALUATION_REPORT = "bid_evaluation_report";

    /**
     * 招标人同意继续开标或改变采购方式的说明材料
     */
    public static final String DESCRIPTIVE_MATERIAL = "descriptive_material";

    /**
     * 中标通知书
     */
    public static final String BID_WIN_NOTICE = "bid_win_notice";

    /**
     * 报名通知书
     */
    public static final String SIGN_UP_WIN_NOTICE = "sign_up_win_notice";

    /**
     * 招标结果通知书
     */
    public static final String TENDER_BID_WIN_NOTICE = "tender_bid_win_notice";

    /**
     * 中标人的投标文件（正本）
     */
    public static final String BID_WIN_PEOPLE = "bid_win_people";

    /**
     * 中标人的投标文件（正本）
     */
    public static final String SIGN_UP_PASS_FILE = "sign_up_pass_file";

    /**
     * 非中标人的投标文件（正本）
     */
    public static final String NO_BID_WIN_PEOPLE = "no_bid_win_people";

    /**
     * 录音录像电子文件
     */
    public static final String VIDEOTAPE = "videotape";

    /**
     * 其他文件
     */
    public static final String OTHERFILE = "other_file";

    /**
     * 其他文件
     */
    public static final String APPLYINFOFILE = "apply_info_file";

    /**
     * 二阶段相关-征集公告/征集邀请书
     */
    public static final String BID_NOTICE_CONTENT_SECOND = "bid_notice_content_second";

    /**
     * 二阶段相关-征集文件（含修改内容）
     */
    public static final String BID_NOTICE_DOC_SECOND = "bid_notice_doc_second";

    /**
     * 二阶段相关-出售征集文件登记表
     */
    public static final String SALE_BID_REGISTRATION_FORM_SECOND = "sale_bid_registration_form_second";

    /**
     * 二阶段相关-资格审查表
     */
    public static final String BID_REGISTRATION_FORM_SECOND = "bid_registration_form_second";

    /**
     * 二阶段相关-已通过征集通知书
     */
    public static final String BID_WIN_NOTICE_SECOND = "bid_win_notice_second";

    /**
     * 二阶段相关-通过征集供应商的技术建议文件（正本）
     */
    public static final String TEC_ADVICE_SECOND = "tec_advice_second";

    /**
     * 二阶段相关-未通过征集通知书
     */
    public static final String TENDER_BID_WIN_NOTICE_SECOND = "tender_bid_win_notice_second";

    /**
     * 二阶段相关-未通过征集供应商的技术建议文件（正本）
     */
    public static final String NO_TEC_ADVICE_SECOND = "no_tec_advice_second";

}
