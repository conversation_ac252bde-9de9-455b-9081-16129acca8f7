package com.hzw.ssm.applets.dao;

import com.hzw.ssm.applets.entity.ProQualification;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021-02-03 10:04
 * @Version 1.0
 */
public interface ProQualificationMapper {

    int insertProQualification(ProQualification proQualification);

    int updateProQualification(ProQualification proQualification);

    ProQualification getProQualification(ProQualification proQualification);

    int deleteProQualification(ProQualification proQualification);

    List<ProQualification> getProQualifications(ProQualification proQualification);

    ProQualification queryProQualification(ProQualification qualification);

    int queryQualificationNumById(@Param("expertId") String expertId);
}
