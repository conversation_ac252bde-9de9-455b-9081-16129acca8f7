package com.hzw.sunflower.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hzw.sunflower.controller.project.request.AgentProjectReq;
import com.hzw.sunflower.dto.ProjectShareDTO;
import com.hzw.sunflower.entity.ProjectShare;
import com.hzw.sunflower.entity.condition.ProjectShareCondition;

import java.util.List;


/**
* ProjectShareService接口
*
*/
public interface ProjectShareService extends IService<ProjectShare> {
    /**
    * 根据条件分页查询列表
    *
    * @param condition 查询条件
    * @return 分页信息
    */
    IPage<ProjectShare> findInfoByCondition(ProjectShareCondition condition);

    /**
    * 根据主键ID查询信息
    *
    * @param id 主键ID
    * @return ProjectShare信息
    */
    ProjectShare getInfoById(Long id);

    /**
    * 新增
    *
    * @param projectShare
    * @return 是否成功
    */
    Boolean addInfo(ProjectShareDTO projectShare);

    /**
    * 修改单位公司 信息
    *
    * @param projectShare
    * @return 是否成功
    */
    Boolean updateInfo(ProjectShareDTO projectShare);

    /**
    * 根据主键ID删除
    *
    * @param id 主键ID
    * @return 是否成功
    */
    Boolean deleteById(Long id);

    /**
    * 根据主键ID列表批量删除
    *
    * @param idList 主键ID列表
    * @return 是否成功
    */
    Boolean deleteByIds(List<Long> idList);


    /**
     * 更新项目分摊成本信息
     */
    void updateShareList(AgentProjectReq agentProject,List<ProjectShareDTO> allShareList);


    /**
     * 判断项目集合 和来源项目的分摊人是否完全一致
     * @param projectId
     * @param ids
     * @return
     */
    List<Long> findProjectShareIdentical(Long projectId,List<Long> ids);

    List<ProjectShare> findInfo(Long id);
}