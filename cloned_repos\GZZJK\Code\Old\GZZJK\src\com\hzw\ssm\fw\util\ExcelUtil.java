/*
 * 公司名称：江苏华招网信息技术有限公司
 * 版权信息：江苏华招网信息技术有限公司版权所有
 * 描述：导出EXCEL公共类
 * 文件名称：ExcelUtil.java
 * 修改时间：2017-4-8
 * 修改人：袁辉
 * 修改内容：
 * 跟踪单号：
 * 修改单号 ：
 */

package com.hzw.ssm.fw.util;

import java.util.ArrayList;
import java.util.List;

import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFFont;
import org.apache.poi.hssf.usermodel.HSSFPrintSetup;
import org.apache.poi.hssf.usermodel.HSSFRichTextString;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.util.CellRangeAddress;

/**
 * <一句话功能简述> 生成Excel公共类
 * <功能详细描述>
 * <AUTHOR>
 * @version V1.0[产品/模块版本]
 */
public class ExcelUtil {
	

    /**
     * 第一步
     * 函数功能描述：创建HSSFWorkbook对象
     * @return
     */
    public static HSSFWorkbook createHSSFWorkbook() {
        HSSFWorkbook wb = new HSSFWorkbook();
        return wb;
    }

    /**
     * 第二步
     * 函数功能描述：创建HSSFSheet对象
     * @param wb          工作簿
     * @param sheetName   sheet名称
     * @param pageSize    打印纸张类型   例如：HSSFPrintSetup.A4_PAPERSIZE   A4纸
     * @return
     */
    public static HSSFSheet createHSSFSheet(HSSFWorkbook wb, String sheetName, short pageSize,String tableTopStyle) {
        HSSFSheet sheet = null;
        if (sheetName==""||sheetName==null) {
            sheet = wb.createSheet();
        } else {
            sheet = wb.createSheet(sheetName);
        }
        sheet.setHorizontallyCenter(true); //设置打印页面为水平居中
        sheet.setVerticallyCenter(true);   // 设置打印页面为垂直居中
        if(tableTopStyle.equals("0")) {
        	//冰冻第一行和第二行
            sheet.createFreezePane( 0, 3, 0, 3 );
        }
        
        //设置打印参数  
        sheet.setMargin(HSSFSheet.TopMargin, 0.15);// 页边距（上）  
        sheet.setMargin(HSSFSheet.BottomMargin, 0.15);// 页边距（下）  
        sheet.setMargin(HSSFSheet.LeftMargin, 0.15);// 页边距（左）  
        sheet.setMargin(HSSFSheet.RightMargin, 0.15);// 页边距（右 
        //打印
        HSSFPrintSetup ps = sheet.getPrintSetup();
        ps.setLandscape(true); // 打印方向，true：横向，false：纵向(默认)  
        ps.setHeaderMargin(1); //页眉
        ps.setPaperSize(pageSize);//A3、A4纸格式
        ps.setScale((short) 75);//打印缩放比例
        return sheet;
    }

    /**
     * 
     * 函数功能描述：生成样式
     * @param wb          工作簿
     * @param fontSize    字体大小
     * @param isBold      加粗
     * @param align       对其方式
     * @return
     */
    public static HSSFCellStyle createHSSFCellStyle(HSSFWorkbook wb, int fontSize, short isBold,
            short align) {
        HSSFCellStyle style = wb.createCellStyle();
        HSSFFont font = creatFont(wb, fontSize, isBold);
        style.setFont(font);
        //例如：HSSFCellStyle.ALIGN_CENTER 左右居中对齐
        style.setAlignment(align);
        style.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);// 上下居中    
        style.setBorderTop(HSSFCellStyle.BORDER_THIN);
        style.setBorderBottom(HSSFCellStyle.BORDER_THIN);
        style.setBorderLeft(HSSFCellStyle.BORDER_THIN);
        style.setBorderRight(HSSFCellStyle.BORDER_THIN);
        //
       // HSSFDataFormat format = wb.createDataFormat();
       // style.setDataFormat(format.getFormat("@"));
        //自适应换行
        style.setWrapText(true);
        return style;
    }

    /**
     * 
     * 函数功能描述：生成样式
     * @param wb          工作簿
     * @param fontSize    字体大小
     * @param isBold      加粗
     * @param align       对其方式
     * @return
     */
    public static HSSFCellStyle createHSSFCellStyle2(HSSFWorkbook wb, int fontSize, short isBold,
            short align) {
        HSSFCellStyle style = wb.createCellStyle();
        HSSFFont font = creatFont2(wb, fontSize, isBold);
        style.setFont(font);
        //例如：HSSFCellStyle.ALIGN_CENTER 左右居中对齐
        style.setAlignment(align);
        style.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);// 上下居中    
        style.setBorderTop(HSSFCellStyle.BORDER_THIN);
        style.setBorderBottom(HSSFCellStyle.BORDER_THIN);
        style.setBorderLeft(HSSFCellStyle.BORDER_THIN);
        style.setBorderRight(HSSFCellStyle.BORDER_THIN);
       // HSSFDataFormat format = wb.createDataFormat();
       // style.setDataFormat(format.getFormat("@"));
        //自适应换行
        style.setWrapText(true);
        return style;
    }
    /**
     * 
     * 函数功能描述：创建行
     * @param style        单元格
     * @param sheet        表单
     * @param currentRows  当前行
     * @param totalCols    每行多少单元格
     * @return
     */
    public static HSSFRow createHSSFRow(HSSFCellStyle style, HSSFSheet sheet, int currentRows,
            int totalCols) {
        HSSFRow row = sheet.createRow(currentRows);
        for (int i = 0; i < totalCols; i++) {
            HSSFCell cell = row.createCell(i);
            cell.setCellStyle(style);
        }
        //short fontHeight=style.getFont(row.getSheet().getWorkbook()).getFontHeight();
        // short height = row.getHeight();
        // row.setHeight((short) (height*(Math.ceil(fontHeight/height)+1)));
        return row;
    }

    /**
     * 
     * 函数功能描述：生成非结构化Excel列表
     * @param sheet             表单对象
     * @param style             单元格风格
     * @param currentRowNo      当前行号
     * @param colspanRowNo      合并的列
     * @param totalCols         每行共有多少单元格
     * @param mergeTitle        需要合并的内容
     * @param mergeCols         内容对应占有多少单元格   传递过来{1，2，3}分别表示： 合并多少行，合并多少列，从几列开始
     * @return
     */
    public static int mergeRowsOne(HSSFSheet sheet, HSSFCellStyle style, int currentRowNo,
            int totalCols, String[] mergeTitle, int[][] mergeCols) {
        if (mergeCols == null || mergeCols.length <= 0) {
            return currentRowNo;
        }
        if (mergeTitle == null) {
            mergeTitle = new String[mergeCols.length];
        }
        //1.创建改行
        HSSFRow row = createHSSFRow(style, sheet, currentRowNo, totalCols);
        int minIndex = 0;
        for (int i = 0; i < mergeCols.length; i++) {
            //lastCols = firstCols+mergeCols[i][1]-1;
            HSSFCell cell = row.getCell(mergeCols[i][2] - 1);
            cell.setCellStyle(style);
            String value = "";
            if (mergeTitle.length > i) {
                value = mergeTitle[i];
            }
            cell.setCellValue(new HSSFRichTextString(value));
            cell.setCellType(HSSFCell.CELL_TYPE_STRING);
            sheet.addMergedRegion(
                    new CellRangeAddress(currentRowNo, currentRowNo + mergeCols[i][0] - 1,
                            mergeCols[i][2] - 1, mergeCols[i][2] - 1 + mergeCols[i][1] - 1));
            //设置自适应宽度
//            sheet.autoSizeColumn(mergeCols[i][2] - 1, true);
        }
        minIndex = mergeCols[0][0];
        for (int j = 0; j < mergeCols.length; j++) {
            if (mergeCols[j][0] <= minIndex) {
                minIndex = mergeCols[j][0];
            }
        }
        return currentRowNo + minIndex - 1;

    }

    /**
     * 
     * 函数功能描述：生成结构化EXCEL (主要用在遍历没有跨行EXCEL的内容)
     * @param sheet             表单对象
     * @param style             单元格风格
     * @param currentRowNo      当前行号
     * @param colspanRowNo      合并的列
     * @param totalCols         每行共有多少单元格
     * @param contextList        需要合并的内容                                 单元格List
     * @param mergeCols         内容对应占有多少单元格       new[] margeCols  只需要告知跨多少列
     * @return
     */
    public static int mergeRowsOne1(HSSFSheet sheet, HSSFCellStyle style, int currentRowNo,
            int totalCols, List<String> contextList, int[] mergeCols) {
        if (mergeCols == null || mergeCols.length <= 0) {
            return currentRowNo;
        }
        if (contextList == null) {
            contextList = new ArrayList<String>();
        }
        //1.创建改行
        HSSFRow row = createHSSFRow(style, sheet, currentRowNo, totalCols);
        int firstCols = 0;
        int lastCols = 0;
        for (int i = 0; i < mergeCols.length; i++) {
            lastCols = firstCols + mergeCols[i] - 1;
            HSSFCell cell = row.getCell(firstCols);
            cell.setCellStyle(style);
            String value = null;
            if (contextList.size() > i) {
                value = contextList.get(i);
            }
            if(value==null||value ==""){
                value = "";
            }
            cell.setCellType(HSSFCell.CELL_TYPE_STRING);
            cell.setCellValue(value);
            sheet.setColumnWidth(firstCols, value.getBytes().length*2);
            if (mergeCols[i] > 1) {
                sheet.addMergedRegion(
                        new CellRangeAddress(currentRowNo, currentRowNo, firstCols, lastCols));
               /* sheet.autoSizeColumn(firstCols, true);*/
              
            }
            
            firstCols = lastCols + 1;
        }
        return currentRowNo;
    }

    /**
     * 
     * 函数功能描述：生成结构化EXCEL(主要用在没有跨行的表头)
     * @param sheet             表单对象
     * @param style             单元格风格
     * @param currentRowNo      当前行号
     * @param colspanRowNo      合并的列
     * @param totalCols         每行共有多少单元格
     * @param mergeTitle        需要合并的内容
     * @param mergeCols         内容对应占有多少单元格
     * @return
     */
    public static int mergeRowsOne2(HSSFSheet sheet, HSSFCellStyle style, int currentRowNo,
            int totalCols, String[] mergeTitle, int[] mergeCols) {
        if (mergeCols == null || mergeCols.length <= 0) {
            return currentRowNo;
        }
        if (mergeTitle == null) {
            mergeTitle = new String[mergeCols.length];
        }
        //1.创建改行
        HSSFRow row = createHSSFRow(style, sheet, currentRowNo, totalCols);
        int firstCols = 0;
        int lastCols = 0;
        for (int i = 0; i < mergeCols.length; i++) {
            lastCols = firstCols + mergeCols[i] - 1;
            HSSFCell cell = row.getCell(firstCols);
            cell.setCellStyle(style);
            String value = "";
            if (mergeTitle.length > i) {
                value = mergeTitle[i];
            }
            cell.setCellValue(new HSSFRichTextString(value));
           /* cell.setCellType(HSSFCell.CELL_TYPE_STRING);*/

            if (mergeCols[i] > 1) {
                sheet.addMergedRegion(
                        new CellRangeAddress(currentRowNo, currentRowNo, firstCols, lastCols));
            }
            //设置自适应宽度
           /* sheet.autoSizeColumn(firstCols, true);*/
            firstCols = lastCols + 1;
        }
        return currentRowNo;
    }

    public static int mergeRowsOne3(HSSFSheet sheet, HSSFCellStyle style, int currentRowNo,
            int totalCols, List<String> contextList, int[][] mergeCols) {

        if (mergeCols == null || mergeCols.length <= 0) {
            return currentRowNo;
        }
        if (contextList == null) {
            contextList = new ArrayList<String>();
        }
        //1.创建改行
        HSSFRow row = createHSSFRow(style, sheet, currentRowNo, totalCols);
        int minIndex = 0;
        for (int i = 0; i < mergeCols.length; i++) {
            //lastCols = firstCols+mergeCols[i][1]-1;
            HSSFCell cell = row.getCell(mergeCols[i][2] - 1);
            cell.setCellStyle(style);
            String value = "";
            if (contextList.size() > i) {
                value = contextList.get(i);
            }
            cell.setCellValue(new HSSFRichTextString(value));
            cell.setCellType(HSSFCell.CELL_TYPE_STRING);
            sheet.addMergedRegion(
                    new CellRangeAddress(currentRowNo, currentRowNo + mergeCols[i][0] - 1,
                            mergeCols[i][2] - 1, mergeCols[i][2] - 1 + mergeCols[i][1] - 1));
            //设置自适应宽度
           // sheet.setColumnWidth(3, 50);
            /*sheet.autoSizeColumn(mergeCols[i][2] - 1, true);*/
        }
        minIndex = mergeCols[0][0];
        for (int j = 0; j < mergeCols.length; j++) {
            if (mergeCols[j][0] <= minIndex) {
                minIndex = mergeCols[j][0];
            }
        }
        return currentRowNo + minIndex - 1;
    }

    /**
     * 
     * 函数功能描述：创建字体
     * @param wb            工作簿
     * @param fontSize      字体大小
     * @param isBold        是否加粗
     * @return
     */
    private static HSSFFont creatFont(HSSFWorkbook wb, int fontSize, short isBold) {
        HSSFFont font = wb.createFont();
        font.setFontName("宋体");
        //例如:20
        font.setFontHeightInPoints((short) fontSize);
        //例如：HSSFFont.BOLDWEIGHT_BOLD 加粗
        font.setBoldweight(isBold);
        return font;
    }
    
    /**
     * 
     * 函数功能描述：创建字体带颜色
     * @param wb            工作簿
     * @param fontSize      字体大小
     * @param isBold        是否加粗
     * @return
     */
    private static HSSFFont creatFont2(HSSFWorkbook wb, int fontSize, short isBold) {
        HSSFFont font = wb.createFont();
        font.setFontName("宋体");
        //例如:20
        font.setFontHeightInPoints((short) fontSize);
        //例如：HSSFFont.BOLDWEIGHT_BOLD 加粗
        font.setBoldweight(isBold);
        font.setColor(HSSFColor.RED.index);
        return font;
    }
    
    /**
     * 
     * 函数功能描述：给sheet的列设置自动宽度
     * @param sheet 一个excel表单
     * @param totalCols 共多少列
     */
    public static void autoSheetAutoSizeColumn(Sheet sheet,int totalCols){
        try{
            for(int i=0; i<totalCols; i++){
                sheet.autoSizeColumn(i, true);
            }
        }catch(Exception e){
            
        }
    }
}
