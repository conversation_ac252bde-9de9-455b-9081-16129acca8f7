/**
 * <AUTHOR>
 */

import "package:flutter/material.dart";
import 'package:flutter/cupertino.dart';
import 'package:flutter_localizations/flutter_localizations.dart';

// import 'package:flutterapp/src/utils/ChineseCupertinoLocalizations.dart';

import 'package:flutter_main_app/src/constants/theme.dart';

import 'package:flutter_main_app/src/models/common/RouterModel.dart';

import "package:flutter_main_app/src/widgets/root.dart";

class MainApp extends StatelessWidget  {

  const MainApp({Key key}) : super(key: key);

  @override
  Widget build(BuildContext context) {

    Widget materailApp = MaterialApp(
      title: '',
      debugShowCheckedModeBanner: false,
      localizationsDelegates: <LocalizationsDelegate<dynamic>>[
        ///***
        /// * 国际化
        /// *
        /// * 实际效果是使得 CupertinoDatePicker 显示中国地区的时间格式
        /// */
        /// * ... app-specific localization delegate[s] here
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        /// * 会使 Cupertino 风格的组件显示中文
        /// * 这里指的是 CupertinoDatePicker
        GlobalCupertinoLocalizations.delegate,
        /// * 报错：The method 'datePickerDayOfMonth' was called on null.
        /// * 报错：A RenderFlex overflowed by 99180 pixels on the bottom.
        // DefaultCupertinoLocalizations.delegate,
        /// * 不需要？不生效
        // ChineseCupertinoLocalizations.delegate,
      ],
      supportedLocales: <Locale>[
        /// * 会使 CupertinoDatePicker 显示风格固化为英文格式
        // Locale('en', 'US'),
        Locale.fromSubtags(languageCode: 'zh', scriptCode: 'Hans', countryCode: 'CN'),
        /// * other locales the app supports
      ],
      theme: ThemeData(
        primaryColor: themeActiveColor,
        ///***
        /// * 将安卓的路由切换动画转换为 IOS 的对应动画
        /// * 同时对 IOS 做相同设置，使得安卓手机可以识别页面回退手势
        /// */
        pageTransitionsTheme: PageTransitionsTheme(
          builders: {
            TargetPlatform.android: CupertinoPageTransitionsBuilder(),
            TargetPlatform.iOS: CupertinoPageTransitionsBuilder(),
          },
        ),
        // accentColor: Color.fromARGB(255, 255, 255, 255),
        // buttonTheme: ButtonThemeData(
        //   buttonColor: Color.fromARGB(255, 255, 51, 51),
        //   textTheme: ButtonTextTheme.accent,
        //   height: 40
        // ),
      ),
      home: HomePageWidget(),
      onGenerateRoute: RouterModel.generateRoute,
    );

    return materailApp;

  }

}
