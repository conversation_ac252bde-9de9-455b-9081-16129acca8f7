package com.hzw.ssm.expert.action;

import com.alibaba.fastjson.JSON;
import com.hzw.ssm.applets.utils.GetJsonReqData;
import com.hzw.ssm.applets.utils.GetJsonRespData;
import com.hzw.ssm.expert.dao.PoliciesDetailsBakMapper;
import com.hzw.ssm.expert.entity.Policies;
import com.hzw.ssm.expert.entity.PoliciesDetails;
import com.hzw.ssm.expert.entity.PoliciesDetailsBak;
import com.hzw.ssm.expert.service.PoliciesDetailsService;
import com.hzw.ssm.expert.service.PoliciesService;
import com.hzw.ssm.fw.base.BaseAction;
import com.hzw.ssm.sys.user.entity.UserEntity;
import net.sf.json.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.apache.struts2.ServletActionContext;
import org.apache.struts2.convention.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;

import javax.servlet.ServletException;
import javax.servlet.http.HttpSession;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.*;

@Namespace("/policiesDetails")
@ParentPackage(value = "default")
@Results({

        @Result(name = "initList",
                location = "/jsp/policies/details/lawRegulation.jsp"),

        @Result(name = "addPoliciesDetails",
                location = "/jsp/policies/details/changLawRegulation.jsp"),

        @Result(name = "updatePoliciesDetails",
                location = "/jsp/policies/details/update.jsp"),

        @Result(name = "lawRegulationDetail",
                location = "/jsp/policies/details/lawRegulationDetail.jsp"),
        @Result(name = "lawRegulationDetail2",
                location = "/jsp/policies/details/lawRegulationDetail2.jsp"),
        @Result(name = "notFile",
                location = "/jsp/policies/details/not_file.jsp"),
})
public class PoliciesDetailsAction extends BaseAction {

    @Autowired
    private PoliciesDetailsService service;
    @Autowired
    private PoliciesDetailsBakMapper policiesDetailsBakService;


    @Autowired
    private PoliciesService policiesService;


    private List<PoliciesDetailsBak> policiesDetailsBaks;

    private List<PoliciesDetails> policiesList = new ArrayList<>();

    private List<Policies> policiesNames = new ArrayList<>();

    private PoliciesDetails entity = new PoliciesDetails();

    private File UserFile;
    private Integer status;

    private String id;
    private String afId;
    private String bfId;
    private String UserFileFileName;
    private String UserFileContentType;


    private String updSort;
    private String creSort;

    private String onSort;
    private String offSort;




    @Action("/getNotFile")
    public String getNotFile() {
        return "notFile";
    }

    @Action("/getPagePoliciesDetails")
    public String getPagePoliciesDetails() {
        policiesNames = policiesService.findPoliciesAll(null);
        if (entity == null) {
            entity = new PoliciesDetails();
        }

        this.context();

        entity.setPage(this.getPage());

        if (StringUtils.isNotBlank(updSort)) {
            entity.setUpdSort(updSort);
        }
        if (StringUtils.isNotBlank(creSort)) {
            entity.setCreSort(creSort);
        }


        if (StringUtils.isNotBlank(onSort)) {
            entity.setOnSort(onSort);
        }
        if (StringUtils.isNotBlank(offSort)) {
            entity.setOffSort(offSort);
        }

        policiesList = service.getPagePoliciesDetails(entity);

        return "initList";
    }


    @Action("/updatePoliciesDetails")
    public String updatePoliciesDetails() {
        entity = service.getByIdPoliciesDetails(id);
        policiesNames = policiesService.findPoliciesAll(null);
        id = null;
        return "updatePoliciesDetails";
    }


    @Action("/addPoliciesDetails")
    public String addPoliciesDetails() {
        policiesNames = policiesService.findPoliciesAll(null);
        return "addPoliciesDetails";
    }


    @Action("/byIdDtails")
    public String byIdDtails() {
        entity = null;
        entity = service.byIdDtails(id);
        policiesDetailsBaks = null;
        policiesDetailsBaks = service.lawDes(id);
        return "lawRegulationDetail";
    }


    @Action("/byIdBakAf")
    public String byIdBakAf() {
        PoliciesDetailsBak policiesDetailsBakById = policiesDetailsBakService.findPoliciesDetailsBakById(afId);
        String after = policiesDetailsBakById.getAfter();
        entity = JSON.parseObject(after, PoliciesDetails.class);
        String policiesId = entity.getPoliciesId();
        Policies policiesById = policiesService.findPoliciesById(policiesId);
        if (policiesById != null) {
            entity.setPoliciesId(policiesById.getName());
        }


        entity.setTopMsg(entity.getTop() == 1 ? "置顶" : "不置顶");
        entity.setStatusMsg(entity.getStatus() == 0 ? "待发布" : entity.getStatus() == 1 ? "编辑中" : entity.getStatus() == 2 ? "已发布" : entity.getStatus() == 3 ? "已下架" : "无");

        return "lawRegulationDetail2";
    }

    @Action("/byIdBakBf")
    public String byIdBakBf() {
        PoliciesDetailsBak policiesDetailsBakById = policiesDetailsBakService.findPoliciesDetailsBakById(bfId);
        String after = policiesDetailsBakById.getBefore();
        entity = JSON.parseObject(after, PoliciesDetails.class);
        String policiesId = entity.getPoliciesId();
        Policies policiesById = policiesService.findPoliciesById(policiesId);
        if (policiesById != null) {
            entity.setPoliciesId(policiesById.getName());
        }
        entity.setTopMsg(entity.getTop() == 1 ? "置顶" : "不置顶");
        entity.setStatusMsg(entity.getStatus() == 0 ? "待发布" : entity.getStatus() == 1 ? "编辑中" : entity.getStatus() == 2 ? "已发布" : entity.getStatus() == 3 ? "已下架" : "无");
        return "lawRegulationDetail2";
    }


    @Action("/addPoliciesDetailsDb")
    public String addPoliciesDetailsDb() throws ServletException, IOException {
        this.context();
        // 当前session
        HttpSession session = getRequest().getSession();
        // 当前用户信息
        UserEntity user = (UserEntity) session.getAttribute("userInfo");

        String newFilePath = null;
        if (UserFileFileName != null && UserFile != null) {
            newFilePath = newFile();
            entity.setFileId(newFilePath);
            entity.setFileName(UserFileFileName);
            entity.setFileSize(UserFile.length());
        } else {
            entity.setFileId("policiesDetails/getNotFile");
            entity.setFileName("无");
            entity.setFileSize(null);
        }

        entity.setId(UUID.randomUUID().toString().replaceAll("-", ""));
        entity.setCreateTime(new Date());
        //entity.setUpdateTime(new Date());
        entity.setTop(0);
        entity.setCreateUser(user.getUser_id());
        //entity.setUpdateUser(user.getUser_id());

        entity.setStatus(status);


        service.addPoliciesDetails(entity);
        entity = null;
        return getPagePoliciesDetails();
    }

    @Action("/updatePoliciesDetailsDb")
    public String updatePoliciesDetailsDb() {
        PoliciesDetailsBak policiesDetailsBak = new PoliciesDetailsBak();
        this.context();
        // 当前session
        HttpSession session = getRequest().getSession();
        // 当前用户信息
        UserEntity user = (UserEntity) session.getAttribute("userInfo");
        PoliciesDetails one = service.getByIdPoliciesDetails(entity.getId());
        policiesDetailsBak.setBefore(JSON.toJSONString(one));// 修改前
        one.setTitle(entity.getTitle());
        one.setSrc(entity.getSrc());
        one.setTxt(entity.getTxt());
        one.setStatus(status);
        if (UserFile != null&&UserFileFileName!=null) {
            String newFilePath = newFile();
            one.setFileId(newFilePath);
            one.setFileName(UserFileFileName);
            one.setFileSize(UserFile.length());
        }
        one.setUpdateTime(new Date());
        one.setUpdateUser(user.getUser_id());
        service.updatePoliciesDetails(one);
        policiesDetailsBak.setAfter(JSON.toJSONString(one));// 修改后
        policiesDetailsBak.setCreateTime(new Date());
        policiesDetailsBak.setCreateUser(one.getCreateUser());
        policiesDetailsBak.setUpdateTime(new Date());
        policiesDetailsBak.setUpdateUser(one.getUpdateUser());
        policiesDetailsBak.setDetailId(one.getId());
        policiesDetailsBak.setId(UUID.randomUUID().toString().replaceAll("-", ""));


        policiesDetailsBakService.addPoliciesDetailsBak(policiesDetailsBak);
        entity = null;
        return getPagePoliciesDetails();
    }


    //删除
    @Action("/byIdDelete")
    public Object byIdDelete() {
        //数据
        JSONObject jsonReqData = GetJsonReqData.getJsonReqData();
        String id = jsonReqData.getString("id");
        service.byIdDelete(id);
        return SUCCESS;
    }


    //新增校验
    @Action("/byNameFlag")
    public Object byNameFlag() {
        Map result = new HashMap();
        //数据
        JSONObject jsonReqData = GetJsonReqData.getJsonReqData();
        boolean byNameFlag = jsonReqData.getBoolean("byNameFlag");
        String title = jsonReqData.getString("title");
        String id = jsonReqData.getString("id");
        if (byNameFlag) {//根据名称查询
            PoliciesDetails policiesDetails = new PoliciesDetails();
            policiesDetails.setTitle(title);
            List<PoliciesDetails> byNamePoliciesDetails = service.getByNamePoliciesDetails(policiesDetails);
            if (byNamePoliciesDetails != null && !byNamePoliciesDetails.isEmpty()) {
                result.put("code", 1);
                result.put("msg", "当前标题不能重复!");
            } else {
                result.put("code", 0);
                result.put("msg", "可以通过!");
            }
        } else {
            PoliciesDetails policiesDetails = new PoliciesDetails();
            policiesDetails.setTitle(title);
            List<PoliciesDetails> byNamePoliciesDetails = service.getByNamePoliciesDetails(policiesDetails);
            if (!byNamePoliciesDetails.isEmpty()) {
                for (PoliciesDetails item : byNamePoliciesDetails) {
                    if (item.getId().equalsIgnoreCase(id)) {
                        result.put("code", 0);
                        result.put("msg", "可以通过!");
                        break;
                    } else {
                        result.put("code", 1);
                        result.put("msg", "当前标题不能重复!");

                    }
                }


            } else {
                result.put("code", 0);
                result.put("msg", "可以通过!");
            }
        }

        GetJsonRespData.getJsonRespData(result);
        return null;
    }


    //发布下架
    @Action("/byIdRelease")
    public Object byIdRelease() {
        PoliciesDetailsBak policiesDetailsBak = new PoliciesDetailsBak();
        this.context();
        // 当前session
        HttpSession session = getRequest().getSession();
        // 当前用户信息
        UserEntity user = (UserEntity) session.getAttribute("userInfo");

        //数据
        JSONObject jsonReqData = GetJsonReqData.getJsonReqData();
        String id = jsonReqData.getString("id");
        Boolean byIdReleaseFlag = jsonReqData.getBoolean("byIdReleaseFlag");


        PoliciesDetails be = service.getByIdPoliciesDetails(id);

        service.byIdRelease(id, byIdReleaseFlag, user);

        PoliciesDetails af = service.getByIdPoliciesDetails(id);

        policiesDetailsBak.setBefore(JSON.toJSONString(be));
        policiesDetailsBak.setAfter(JSON.toJSONString(af));// 修改后
        policiesDetailsBak.setCreateTime(new Date());
        policiesDetailsBak.setCreateUser(af.getCreateUser());
        policiesDetailsBak.setUpdateTime(new Date());
        policiesDetailsBak.setUpdateUser(af.getUpdateUser());
        policiesDetailsBak.setDetailId(af.getId());
        policiesDetailsBak.setId(UUID.randomUUID().toString().replaceAll("-", ""));
        policiesDetailsBakService.addPoliciesDetailsBak(policiesDetailsBak);
        return SUCCESS;
    }


    //置顶
    @Action("/byIdOperation")
    public Object byIdOperation() {
        PoliciesDetailsBak policiesDetailsBak = new PoliciesDetailsBak();
        this.context();
        // 当前session
        HttpSession session = getRequest().getSession();
        // 当前用户信息
        UserEntity user = (UserEntity) session.getAttribute("userInfo");

        //数据
        JSONObject jsonReqData = GetJsonReqData.getJsonReqData();
        String id = jsonReqData.getString("id");
        Boolean byIdOperationFlag = jsonReqData.getBoolean("byIdOperationFlag");
        PoliciesDetails be = service.getByIdPoliciesDetails(id);
        service.byIdOperation(id, byIdOperationFlag, user);
        PoliciesDetails af = service.getByIdPoliciesDetails(id);

        policiesDetailsBak.setBefore(JSON.toJSONString(be));
        policiesDetailsBak.setAfter(JSON.toJSONString(af));// 修改后
        policiesDetailsBak.setCreateTime(new Date());
        policiesDetailsBak.setCreateUser(af.getCreateUser());
        policiesDetailsBak.setUpdateTime(new Date());
        policiesDetailsBak.setUpdateUser(af.getUpdateUser());
        policiesDetailsBak.setDetailId(af.getId());
        policiesDetailsBak.setId(UUID.randomUUID().toString().replaceAll("-", ""));
        policiesDetailsBakService.addPoliciesDetailsBak(policiesDetailsBak);
        return SUCCESS;
    }


    public String getUserFileFileName() {
        return UserFileFileName;
    }

    public void setUserFileFileName(String userFileFileName) {
        UserFileFileName = userFileFileName;
    }

    public String getUserFileContentType() {
        return UserFileContentType;
    }

    public void setUserFileContentType(String userFileContentType) {
        UserFileContentType = userFileContentType;
    }

    public List<PoliciesDetailsBak> getPoliciesDetailsBaks() {
        return policiesDetailsBaks;
    }

    public void setPoliciesDetailsBaks(List<PoliciesDetailsBak> policiesDetailsBaks) {
        this.policiesDetailsBaks = policiesDetailsBaks;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String newFile() {
        File dir = new File(ServletActionContext.getServletContext().getRealPath("expertFile") + "\\file");
        //判断文件是否上传，如果上传的话将会创建该目录
        if (!dir.exists()) {
            dir.mkdir(); //创建该目录
        }
        //第一种文件上传的方法
        //声明文件输入流，为输入流指定文件路径
        //获取输出流，获取文件的文件地址及名称
        FileInputStream in = null;
        FileOutputStream out = null;
        long fileNameSys = System.currentTimeMillis();
        String filePath = fileNameSys + UserFileFileName.substring(UserFileFileName.indexOf("."));
        try {

            in = new FileInputStream(UserFile);
            out = new FileOutputStream(dir + "\\" + filePath);

            byte[] b = new byte[1024 * 1024];//每次写入的大小
            int i = 0;
            while ((i = in.read(b)) > 0) {
                out.write(b, 0, i);
            }
            in.close();
            out.close();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
        }
        String path = dir.getPath();
        path = path.substring(path.indexOf("expertFile"));
        path = path + "/" + filePath;
        return path.replaceAll("\\\\", "/");
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public File getUserFile() {
        return UserFile;
    }

    public void setUserFile(File userFile) {
        UserFile = userFile;
    }

    public PoliciesDetails getEntity() {
        return entity;
    }

    public void setEntity(PoliciesDetails entity) {
        this.entity = entity;
    }

    public List<Policies> getPoliciesNames() {
        return policiesNames;
    }

    public void setPoliciesNames(List<Policies> policiesNames) {
        this.policiesNames = policiesNames;
    }

    public List<PoliciesDetails> getPoliciesList() {
        return policiesList;
    }

    public void setPoliciesList(List<PoliciesDetails> policiesList) {
        this.policiesList = policiesList;
    }

    public String getUpdSort() {
        return updSort;
    }

    public void setUpdSort(String updSort) {
        this.updSort = updSort;
    }

    public String getCreSort() {
        return creSort;
    }

    public void setCreSort(String creSort) {
        this.creSort = creSort;
    }

    public String getOnSort() {
        return onSort;
    }

    public void setOnSort(String onSort) {
        this.onSort = onSort;
    }

    public String getOffSort() {
        return offSort;
    }

    public void setOffSort(String offSort) {
        this.offSort = offSort;
    }

    public String getAfId() {
        return afId;
    }

    public void setAfId(String afId) {
        this.afId = afId;
    }

    public String getBfId() {
        return bfId;
    }

    public void setBfId(String bfId) {
        this.bfId = bfId;
    }
}
