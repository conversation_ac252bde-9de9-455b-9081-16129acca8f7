package com.hzw.sunflower.entity.condition;

import com.hzw.sunflower.common.BaseCondition;
import io.swagger.annotations.ApiModel;


/**
 * 项目转委托人表查询条件
 *
 * <AUTHOR>
 * @version 1.0.0 2021-04-14
 */
@ApiModel(description = "项目转委托人表查询条件")
public class ProjectTurnEntrustCondition extends BaseCondition {
    /**
     * 版本号
     */
    private static final long serialVersionUID = -3636207281352683969L;
}