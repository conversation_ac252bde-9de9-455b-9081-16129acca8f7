
DROP TABLE IF EXISTS `gen_table`;
CREATE TABLE `gen_table` (
                             `table_id` bigint NOT NULL COMMENT '编号',
                             `table_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT '' COMMENT '表名称',
                             `table_comment` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT '' COMMENT '表描述',
                             `sub_table_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT NULL COMMENT '关联子表的表名',
                             `sub_table_fk_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT NULL COMMENT '子表关联的外键名',
                             `class_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT '' COMMENT '实体类名称',
                             `tpl_category` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT 'crud' COMMENT '使用的模板（crud单表操作 tree树表操作）',
                             `package_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT NULL COMMENT '生成包路径',
                             `module_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT NULL COMMENT '生成模块名',
                             `business_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT NULL COMMENT '生成业务名',
                             `function_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT NULL COMMENT '生成功能名',
                             `function_author` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT NULL COMMENT '生成功能作者',
                             `gen_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT '0' COMMENT '生成代码方式（0zip压缩包 1自定义路径）',
                             `gen_path` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT '/' COMMENT '生成路径（不填默认项目路径）',
                             `options` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT NULL COMMENT '其它生成选项',
                             `created_user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT '' COMMENT '创建者',
                             `created_time` datetime DEFAULT NULL COMMENT '创建时间',
                             `updated_user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT '' COMMENT '更新者',
                             `updated_time` datetime DEFAULT NULL COMMENT '更新时间',
                             `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT NULL COMMENT '备注',
                             `is_delete` int DEFAULT NULL,
                             PRIMARY KEY (`table_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_bin COMMENT='代码生成业务表';

-- ----------------------------
-- Table structure for gen_table_column
-- ----------------------------
DROP TABLE IF EXISTS `gen_table_column`;
CREATE TABLE `gen_table_column` (
                                    `column_id` bigint NOT NULL COMMENT '编号',
                                    `table_id` bigint DEFAULT NULL COMMENT '归属表编号',
                                    `column_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT NULL COMMENT '列名称',
                                    `column_comment` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT NULL COMMENT '列描述',
                                    `column_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT NULL COMMENT '列类型',
                                    `java_type` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT NULL COMMENT 'JAVA类型',
                                    `java_field` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT NULL COMMENT 'JAVA字段名',
                                    `is_pk` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT NULL COMMENT '是否主键（1是）',
                                    `is_increment` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT NULL COMMENT '是否自增（1是）',
                                    `is_required` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT NULL COMMENT '是否必填（1是）',
                                    `is_insert` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT NULL COMMENT '是否为插入字段（1是）',
                                    `is_edit` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT NULL COMMENT '是否编辑字段（1是）',
                                    `is_list` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT NULL COMMENT '是否列表字段（1是）',
                                    `is_query` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT NULL COMMENT '是否查询字段（1是）',
                                    `query_type` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT 'EQ' COMMENT '查询方式（等于、不等于、大于、小于、范围）',
                                    `html_type` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT NULL COMMENT '显示类型（文本框、文本域、下拉框、复选框、单选框、日期控件）',
                                    `dict_type` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT '' COMMENT '字典类型',
                                    `sort` int DEFAULT NULL COMMENT '排序',
                                    `created_user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT '' COMMENT '创建者',
                                    `created_time` datetime DEFAULT NULL COMMENT '创建时间',
                                    `updated_user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT '' COMMENT '更新者',
                                    `updated_time` datetime DEFAULT NULL COMMENT '更新时间',
                                    `is_delete` int DEFAULT NULL,
                                    PRIMARY KEY (`column_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_bin COMMENT='代码生成业务表字段';

-- ----------------------------
-- Table structure for s_system_setting
-- ----------------------------
DROP TABLE IF EXISTS `s_system_setting`;
CREATE TABLE `s_system_setting` (
                                    `id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '主键',
                                    `system_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '系统名称',
                                    `system_abbreviation` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '系统简称',
                                    `page_background_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '登录页背景',
                                    `page_background_url` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin COMMENT '登录页url',
                                    `logo_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'logo',
                                    `domain_filing` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '域名备案',
                                    `online_information_filing` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '联网信息备案',
                                    `copyright_information` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '版权信息',
                                    `customer_service_telephone` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '客服电话',
                                    `registration_agreement_id` varchar(32) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '注册协议',
                                    `system_color` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '系统主色',
                                    `created_user_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '创建人',
                                    `created_time` datetime DEFAULT NULL COMMENT '创建时间',
                                    `updated_user_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '修改人',
                                    `updated_time` datetime DEFAULT NULL COMMENT '修改时间',
                                    `is_delete` int DEFAULT '0' COMMENT '逻辑删除0正常1删除',
                                    `remark` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '备注',
                                    `version` int DEFAULT '0' COMMENT '版本',
                                    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='系统基本信息管理表';

-- ----------------------------
-- Table structure for sys_config
-- ----------------------------
DROP TABLE IF EXISTS `sys_config`;
CREATE TABLE `sys_config` (
                              `config_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin NOT NULL COMMENT '参数主键',
                              `config_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT '' COMMENT '参数名称',
                              `config_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT '' COMMENT '参数键名',
                              `config_value` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT '' COMMENT '参数键值',
                              `config_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT 'N' COMMENT '系统内置（Y是 N否）',
                              `created_user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT '' COMMENT '创建者',
                              `created_time` datetime DEFAULT NULL COMMENT '创建时间',
                              `updated_user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT '' COMMENT '更新者',
                              `updated_time` datetime DEFAULT NULL COMMENT '更新时间',
                              `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT NULL COMMENT '备注',
                              `is_delete` int DEFAULT NULL,
                              PRIMARY KEY (`config_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_bin COMMENT='参数配置表';

-- ----------------------------
-- Table structure for sys_dept
-- ----------------------------
DROP TABLE IF EXISTS `sys_dept`;
CREATE TABLE `sys_dept` (
                            `dept_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin NOT NULL COMMENT '部门id',
                            `parent_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT '0' COMMENT '父部门id',
                            `ancestors` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT '' COMMENT '祖级列表',
                            `dept_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT '' COMMENT '部门名称',
                            `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT '0' COMMENT '部门状态（0正常 1停用）',
                            `code` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '编码',
                            `order_num` int DEFAULT '0' COMMENT '显示顺序',
                            `leader` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT NULL COMMENT '负责人',
                            `phone` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT NULL COMMENT '联系电话',
                            `email` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT NULL COMMENT '邮箱',
                            `other_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT NULL COMMENT '流程引擎部门ID',
                            `is_delete` int DEFAULT '0' COMMENT '删除标志（0代表存在  1代表删除）',
                            `created_user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT '' COMMENT '创建者',
                            `created_time` datetime DEFAULT NULL COMMENT '创建时间',
                            `updated_user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT '' COMMENT '更新者',
                            `updated_time` datetime DEFAULT NULL COMMENT '更新时间',
                            `remark` varchar(1024) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '备注',
                            `version` int DEFAULT '0' COMMENT '版本号控制并发',
                            PRIMARY KEY (`dept_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_bin COMMENT='部门表';

-- ----------------------------
-- Table structure for sys_dept_user
-- ----------------------------
DROP TABLE IF EXISTS `sys_dept_user`;
CREATE TABLE `sys_dept_user` (
                                 `id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin NOT NULL,
                                 `user_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT NULL COMMENT '用户id',
                                 `dept_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT NULL COMMENT '部门id',
                                 `created_user_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT NULL COMMENT '创建人',
                                 `created_time` datetime DEFAULT NULL COMMENT '创建时间',
                                 `updated_user_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT NULL COMMENT '更新人',
                                 `updated_time` datetime DEFAULT NULL COMMENT '更新时间',
                                 `is_delete` int DEFAULT '0' COMMENT '逻辑删除0正常1删除',
                                 `remark` varchar(1024) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '备注',
                                 `version` int DEFAULT '0' COMMENT '版本号控制并发',
                                 PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_bin COMMENT='部门用户表';

-- ----------------------------
-- Table structure for sys_dict_data
-- ----------------------------
DROP TABLE IF EXISTS `sys_dict_data`;
CREATE TABLE `sys_dict_data` (
                                 `id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin NOT NULL,
                                 `dict_sort` int DEFAULT '0' COMMENT '字典排序',
                                 `dict_label` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT '' COMMENT '字典标签',
                                 `dict_value` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT '' COMMENT '字典键值',
                                 `dict_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT '' COMMENT '字典类型',
                                 `css_class` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT NULL COMMENT '样式属性（其他样式扩展）',
                                 `list_class` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT NULL COMMENT '表格回显样式',
                                 `is_default` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT 'N' COMMENT '是否默认（Y是 N否）',
                                 `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT '0' COMMENT '状态（0正常 1停用）',
                                 `parent_code` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT '-1' COMMENT '父节点',
                                 `show_status` int DEFAULT NULL COMMENT '是否展示',
                                 `created_user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT '' COMMENT '创建者',
                                 `created_time` datetime DEFAULT NULL COMMENT '创建时间',
                                 `updated_user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT '' COMMENT '更新者',
                                 `updated_time` datetime DEFAULT NULL COMMENT '更新时间',
                                 `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT NULL COMMENT '备注',
                                 `is_delete` int DEFAULT '0',
                                 `version` int DEFAULT '0' COMMENT '版本号控制并发',
                                 PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_bin COMMENT='字典数据表';

-- ----------------------------
-- Table structure for sys_dict_type
-- ----------------------------
DROP TABLE IF EXISTS `sys_dict_type`;
CREATE TABLE `sys_dict_type` (
                                 `dict_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin NOT NULL COMMENT '字典主键',
                                 `dict_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT '' COMMENT '字典名称',
                                 `dict_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT '' COMMENT '字典类型',
                                 `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT '0' COMMENT '状态（0正常 1停用）',
                                 `created_user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT '' COMMENT '创建者',
                                 `created_time` datetime DEFAULT NULL COMMENT '创建时间',
                                 `updated_user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT '' COMMENT '更新者',
                                 `updated_time` datetime DEFAULT NULL COMMENT '更新时间',
                                 `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT NULL COMMENT '备注',
                                 `is_delete` int DEFAULT NULL,
                                 PRIMARY KEY (`dict_id`) USING BTREE,
                                 UNIQUE KEY `dict_type` (`dict_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_bin COMMENT='字典类型表';

-- ----------------------------
-- Table structure for sys_logininfor
-- ----------------------------
DROP TABLE IF EXISTS `sys_logininfor`;
CREATE TABLE `sys_logininfor` (
                                  `info_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin NOT NULL COMMENT '访问ID',
                                  `user_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT '' COMMENT '用户账号',
                                  `ipaddr` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT '' COMMENT '登录IP地址',
                                  `login_location` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT '' COMMENT '登录地点',
                                  `browser` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT '' COMMENT '浏览器类型',
                                  `os` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT '' COMMENT '操作系统',
                                  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT '0' COMMENT '登录状态（0成功 1失败）',
                                  `msg` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT '' COMMENT '提示消息',
                                  `login_time` datetime DEFAULT NULL COMMENT '访问时间',
                                  PRIMARY KEY (`info_id`),
                                  KEY `idx_sys_logininfor_s` (`status`),
                                  KEY `idx_sys_logininfor_lt` (`login_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_bin COMMENT='系统访问记录';

-- ----------------------------
-- Table structure for sys_menu
-- ----------------------------
DROP TABLE IF EXISTS `sys_menu`;
CREATE TABLE `sys_menu` (
                            `menu_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin NOT NULL COMMENT '菜单ID',
                            `menu_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin NOT NULL COMMENT '菜单名称',
                            `name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '前端页面名称',
                            `parent_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT '0' COMMENT '父菜单ID',
                            `order_num` int DEFAULT '0' COMMENT '显示顺序',
                            `path` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT '' COMMENT '路由地址',
                            `component` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT NULL COMMENT '组件路径',
                            `query_param` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT NULL COMMENT '路由参数',
                            `is_frame` int DEFAULT '1' COMMENT '是否为外链（0是 1否）',
                            `is_cache` int DEFAULT '0' COMMENT '是否缓存（0缓存 1不缓存）',
                            `menu_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT '' COMMENT '菜单类型（M目录 C菜单 F按钮）',
                            `visible` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT '0' COMMENT '显示状态（0显示 1隐藏）',
                            `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT '0' COMMENT '菜单状态（0正常 1停用）',
                            `perms` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT NULL COMMENT '权限标识',
                            `icon` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT '#' COMMENT '菜单图标',
                            `redirect` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '跳转地址',
                            `created_user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT '' COMMENT '创建者',
                            `created_time` datetime DEFAULT NULL COMMENT '创建时间',
                            `updated_user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT '' COMMENT '更新者',
                            `updated_time` datetime DEFAULT NULL COMMENT '更新时间',
                            `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT '' COMMENT '备注',
                            `is_delete` int DEFAULT '0',
                            `breadcrumb` tinyint(1) NOT NULL DEFAULT '0' COMMENT '面包屑是否展示 0 false 1 true',
                            `keep_alive` tinyint(1) NOT NULL DEFAULT '0' COMMENT '长链接,0 false 1 true',
                            `is_menu` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否菜单 1 是 0 不是',
                            `permission` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '权限Code',
                            `hidden` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否隐藏,0 false 1 true',
                            `menu_code` varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '菜单编码',
                            PRIMARY KEY (`menu_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_bin COMMENT='菜单权限表';

-- ----------------------------
-- Table structure for sys_notice
-- ----------------------------
DROP TABLE IF EXISTS `sys_notice`;
CREATE TABLE `sys_notice` (
                              `notice_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin NOT NULL COMMENT '公告ID',
                              `notice_title` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin NOT NULL COMMENT '公告标题',
                              `notice_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin NOT NULL COMMENT '公告类型（1通知 2公告）',
                              `notice_content` longblob COMMENT '公告内容',
                              `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT '0' COMMENT '公告状态（0正常 1关闭）',
                              `created_user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT '' COMMENT '创建者',
                              `created_time` datetime DEFAULT NULL COMMENT '创建时间',
                              `updated_user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT '' COMMENT '更新者',
                              `updated_time` datetime DEFAULT NULL COMMENT '更新时间',
                              `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT NULL COMMENT '备注',
                              `is_delete` int DEFAULT NULL,
                              PRIMARY KEY (`notice_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_bin COMMENT='通知公告表';

-- ----------------------------
-- Table structure for sys_oper_log
-- ----------------------------
DROP TABLE IF EXISTS `sys_oper_log`;
CREATE TABLE `sys_oper_log` (
                                `oper_id` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin NOT NULL COMMENT '日志主键',
                                `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT '' COMMENT '模块标题',
                                `business_type` int DEFAULT '0' COMMENT '业务类型（0其它 1新增 2修改 3删除）',
                                `method` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT '' COMMENT '方法名称',
                                `request_method` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT '' COMMENT '请求方式',
                                `operator_type` int DEFAULT '0' COMMENT '操作类别（0其它 1后台用户 2手机端用户）',
                                `oper_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT '' COMMENT '操作人员',
                                `dept_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT '' COMMENT '部门名称',
                                `oper_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT '' COMMENT '请求URL',
                                `oper_ip` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT '' COMMENT '主机地址',
                                `oper_location` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT '' COMMENT '操作地点',
                                `oper_param` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT '' COMMENT '请求参数',
                                `json_result` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT '' COMMENT '返回参数',
                                `status` int DEFAULT '0' COMMENT '操作状态（0正常 1异常）',
                                `error_msg` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT '' COMMENT '错误消息',
                                `oper_time` datetime DEFAULT NULL COMMENT '操作时间',
                                PRIMARY KEY (`oper_id`),
                                KEY `idx_sys_oper_log_bt` (`business_type`),
                                KEY `idx_sys_oper_log_s` (`status`),
                                KEY `idx_sys_oper_log_ot` (`oper_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_bin COMMENT='操作日志记录';

-- ----------------------------
-- Table structure for sys_oss
-- ----------------------------
DROP TABLE IF EXISTS `sys_oss`;
CREATE TABLE `sys_oss` (
                           `oss_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin NOT NULL COMMENT '对象存储主键',
                           `file_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin NOT NULL DEFAULT '' COMMENT '文件名',
                           `original_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin NOT NULL DEFAULT '' COMMENT '原名',
                           `file_suffix` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin NOT NULL DEFAULT '' COMMENT '文件后缀名',
                           `url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin NOT NULL COMMENT 'URL地址',
                           `created_time` datetime DEFAULT NULL COMMENT '创建时间',
                           `created_user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT '' COMMENT '上传人',
                           `updated_time` datetime DEFAULT NULL COMMENT '更新时间',
                           `updated_user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT '' COMMENT '更新人',
                           `service` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin NOT NULL DEFAULT 'minio' COMMENT '服务商',
                           `is_delete` int DEFAULT '0',
                           `remark` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT NULL,
                           PRIMARY KEY (`oss_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_bin COMMENT='OSS对象存储表';

-- ----------------------------
-- Table structure for sys_oss_config
-- ----------------------------
DROP TABLE IF EXISTS `sys_oss_config`;
CREATE TABLE `sys_oss_config` (
                                  `oss_config_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin NOT NULL COMMENT '主建',
                                  `config_key` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin NOT NULL DEFAULT '' COMMENT '配置key',
                                  `access_key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT '' COMMENT 'accessKey',
                                  `secret_key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT '' COMMENT '秘钥',
                                  `bucket_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT '' COMMENT '桶名称',
                                  `prefix` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT '' COMMENT '前缀',
                                  `endpoint` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT '' COMMENT '访问站点',
                                  `domain` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT '' COMMENT '自定义域名',
                                  `is_https` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT 'N' COMMENT '是否https（Y=是,N=否）',
                                  `region` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT '' COMMENT '域',
                                  `access_policy` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin NOT NULL DEFAULT '1' COMMENT '桶权限类型(0=private 1=public 2=custom)',
                                  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT '1' COMMENT '是否默认（0=是,1=否）',
                                  `ext1` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT '' COMMENT '扩展字段',
                                  `created_user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT '' COMMENT '创建者',
                                  `created_time` datetime DEFAULT NULL COMMENT '创建时间',
                                  `updated_user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT '' COMMENT '更新者',
                                  `updated_time` datetime DEFAULT NULL COMMENT '更新时间',
                                  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT NULL COMMENT '备注',
                                  `is_delete` int DEFAULT NULL,
                                  PRIMARY KEY (`oss_config_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_bin COMMENT='对象存储配置表';

-- ----------------------------
-- Table structure for sys_post
-- ----------------------------
DROP TABLE IF EXISTS `sys_post`;
CREATE TABLE `sys_post` (
                            `post_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin NOT NULL COMMENT '岗位ID',
                            `post_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin NOT NULL COMMENT '岗位编码',
                            `post_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin NOT NULL COMMENT '岗位名称',
                            `post_sort` int NOT NULL COMMENT '显示顺序',
                            `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin NOT NULL COMMENT '状态（0正常 1停用）',
                            `created_user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT '' COMMENT '创建者',
                            `created_time` datetime DEFAULT NULL COMMENT '创建时间',
                            `updated_user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT '' COMMENT '更新者',
                            `updated_time` datetime DEFAULT NULL COMMENT '更新时间',
                            `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT NULL COMMENT '备注',
                            `is_delete` int DEFAULT NULL,
                            PRIMARY KEY (`post_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_bin COMMENT='岗位信息表';

-- ----------------------------
-- Table structure for sys_role
-- ----------------------------
DROP TABLE IF EXISTS `sys_role`;
CREATE TABLE `sys_role` (
                            `role_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin NOT NULL COMMENT '角色ID',
                            `role_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin NOT NULL COMMENT '角色名称',
                            `role_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT NULL COMMENT '角色权限字符串',
                            `role_sort` int DEFAULT NULL COMMENT '显示顺序',
                            `data_scope` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT '1' COMMENT '数据范围（1：全部数据权限 2：自定数据权限 3：本部门数据权限 4：本部门及以下数据权限）',
                            `menu_check_strictly` tinyint(1) DEFAULT '1' COMMENT '菜单树选择项是否关联显示',
                            `dept_check_strictly` tinyint(1) DEFAULT '1' COMMENT '部门树选择项是否关联显示',
                            `status` int NOT NULL COMMENT '角色状态（0正常 1停用）',
                            `description` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '描述',
                            `is_delete` int DEFAULT '0' COMMENT '删除标志（0代表存在 1代表删除）',
                            `created_user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT '' COMMENT '创建者',
                            `created_time` datetime DEFAULT NULL COMMENT '创建时间',
                            `updated_user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT '' COMMENT '更新者',
                            `updated_time` datetime DEFAULT NULL COMMENT '更新时间',
                            `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT NULL COMMENT '备注',
                            `version` int DEFAULT '0' COMMENT '版本号控制并发',
                            PRIMARY KEY (`role_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_bin COMMENT='角色信息表';

-- ----------------------------
-- Table structure for sys_role_dept
-- ----------------------------
DROP TABLE IF EXISTS `sys_role_dept`;
CREATE TABLE `sys_role_dept` (
                                 `role_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin NOT NULL COMMENT '角色ID',
                                 `dept_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin NOT NULL COMMENT '部门ID',
                                 PRIMARY KEY (`role_id`,`dept_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_bin COMMENT='角色和部门关联表';

-- ----------------------------
-- Table structure for sys_role_menu
-- ----------------------------
DROP TABLE IF EXISTS `sys_role_menu`;
CREATE TABLE `sys_role_menu` (
                                 `id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '主键',
                                 `role_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin NOT NULL COMMENT '角色ID',
                                 `menu_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin NOT NULL COMMENT '菜单ID',
                                 `created_user_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
                                 `created_time` datetime DEFAULT NULL COMMENT '创建时间',
                                 `updated_user_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '更新人',
                                 `updated_time` datetime DEFAULT NULL COMMENT '更新时间',
                                 `is_delete` int NOT NULL DEFAULT '0' COMMENT '逻辑删除 0正常1删除',
                                 `remark` varchar(1024) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '备注',
                                 `version` int NOT NULL DEFAULT '0' COMMENT '版本号控制并发',
                                 PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_bin COMMENT='角色和菜单关联表';

-- ----------------------------
-- Table structure for sys_user
-- ----------------------------
DROP TABLE IF EXISTS `sys_user`;
CREATE TABLE `sys_user` (
                            `user_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin NOT NULL COMMENT '用户ID',
                            `dept_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT NULL COMMENT '部门ID',
                            `user_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin NOT NULL COMMENT '用户账号',
                            `nick_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT NULL COMMENT '用户昵称',
                            `user_type` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT NULL COMMENT '用户类型（sys_user系统用户）',
                            `email` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT '' COMMENT '用户邮箱',
                            `phonenumber` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT '' COMMENT '手机号码',
                            `sex` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT '0' COMMENT '用户性别（0男 1女 2未知）',
                            `avatar` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT '' COMMENT '头像地址',
                            `password` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT '' COMMENT '密码',
                            `status` int DEFAULT '1' COMMENT '帐号状态（1正常 2停用）',
                            `is_delete` int DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
                            `login_ip` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT '' COMMENT '最后登录IP',
                            `login_date` datetime DEFAULT NULL COMMENT '最后登录时间',
                            `created_user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT '' COMMENT '创建者',
                            `created_time` datetime DEFAULT NULL COMMENT '创建时间',
                            `updated_user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT '' COMMENT '更新者',
                            `updated_time` datetime DEFAULT NULL COMMENT '更新时间',
                            `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT NULL COMMENT '备注',
                            `qr_code` varchar(5) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '用户二维码对应的编码（用户推荐码）',
                            `qr_code_base` longtext CHARACTER SET utf8 COLLATE utf8_general_ci COMMENT '用户推荐码图片',
                            `referral_code` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '推荐码（注册时填写）',
                            `open_id` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '微信openid',
                            `id_type` int DEFAULT NULL COMMENT '证件类型（1.身份证 2.军官证 3.护照 4.港澳台身份证',
                            `id_num` varchar(18) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '证件号',
                            `other_user_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '第三方用户编号 （流程引擎同步的用户编号）',
                            `version` int NOT NULL DEFAULT '0' COMMENT '版本号控制并发',
                            `identity` int DEFAULT NULL COMMENT '用户身份（1.专家 2.业务员）',
                            PRIMARY KEY (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_bin COMMENT='用户信息表';

-- ----------------------------
-- Table structure for sys_user_post
-- ----------------------------
DROP TABLE IF EXISTS `sys_user_post`;
CREATE TABLE `sys_user_post` (
                                 `user_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin NOT NULL COMMENT '用户ID',
                                 `post_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin NOT NULL COMMENT '岗位ID',
                                 PRIMARY KEY (`user_id`,`post_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_bin COMMENT='用户与岗位关联表';

-- ----------------------------
-- Table structure for sys_user_role
-- ----------------------------
DROP TABLE IF EXISTS `sys_user_role`;
CREATE TABLE `sys_user_role` (
                                 `id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin NOT NULL,
                                 `user_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin NOT NULL COMMENT '用户ID',
                                 `role_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin NOT NULL COMMENT '角色ID',
                                 `created_user_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
                                 `created_time` datetime DEFAULT NULL COMMENT '创建时间',
                                 `updated_user_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '更新人',
                                 `updated_time` datetime DEFAULT NULL COMMENT '更新时间',
                                 `is_delete` int NOT NULL DEFAULT '0' COMMENT '逻辑删除0正常1删除',
                                 `remark` varchar(1024) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '备注',
                                 `version` int DEFAULT '0' COMMENT '版本号控制并发',
                                 PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_bin COMMENT='用户和角色关联表';

-- ----------------------------
-- Table structure for t_call_interface_log
-- ----------------------------
DROP TABLE IF EXISTS `t_call_interface_log`;
CREATE TABLE `t_call_interface_log` (
                                        `id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
                                        `oper_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '操作人员id',
                                        `oper_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT '' COMMENT '操作人员名称',
                                        `oper_url` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT '' COMMENT '请求URL',
                                        `oper_ip` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT '' COMMENT '主机地址',
                                        `oper_param` text CHARACTER SET utf8 COLLATE utf8_general_ci COMMENT '请求参数',
                                        `json_result` text CHARACTER SET utf8 COLLATE utf8_general_ci COMMENT '返回参数',
                                        `status` int DEFAULT '0' COMMENT '操作状态（0正常 1异常）',
                                        `error_msg` text CHARACTER SET utf8 COLLATE utf8_general_ci COMMENT '错误消息',
                                        `oper_time` datetime DEFAULT NULL COMMENT '操作时间',
                                        `system` int DEFAULT NULL COMMENT '操作系统（1.jstcc）',
                                        PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='调用外部接口日志';

-- ----------------------------
-- Table structure for t_crocus_scheduled_cron
-- ----------------------------
DROP TABLE IF EXISTS `t_crocus_scheduled_cron`;
CREATE TABLE `t_crocus_scheduled_cron` (
                                           `cron_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '主键id',
                                           `cron_key` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '定时任务完整类名',
                                           `cron_expression` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'cron表达式',
                                           `task_explain` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '任务描述',
                                           `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态,1:正常;2:停用',
                                           PRIMARY KEY (`cron_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;

-- ----------------------------
-- Table structure for t_expert_appraise
-- ----------------------------
DROP TABLE IF EXISTS `t_expert_appraise`;
CREATE TABLE `t_expert_appraise` (
                                     `id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '主键',
                                     `appraise_info_id` varchar(2000) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT '0' COMMENT '专家库评价信息表ID',
                                     `extraction_record_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '专家结果表id',
                                     `score` int DEFAULT '0' COMMENT '分数',
                                     `total_score` int DEFAULT NULL COMMENT '本次评价后分数',
                                     `appraise_content` text CHARACTER SET utf8 COLLATE utf8_general_ci COMMENT '评价内容',
                                     `appraise_type` int DEFAULT '2' COMMENT '评价方式    1：加分 2：减分',
                                     `status` int DEFAULT '2' COMMENT '状态 10:待评价  20:待确认 30：已确认 40：已退回',
                                     `appraise_time` datetime DEFAULT NULL COMMENT '评价时间',
                                     `review_time` datetime DEFAULT NULL COMMENT '审核时间',
                                     `process_code` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '流程code',
                                     `appraise_user` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '评价人',
                                     `created_user_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
                                     `created_time` datetime DEFAULT NULL COMMENT '创建时间',
                                     `updated_user_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '更新人',
                                     `updated_time` datetime DEFAULT NULL COMMENT '更新时间',
                                     `is_delete` int DEFAULT '0' COMMENT '逻辑删除0正常1删除',
                                     `remark` varchar(1024) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '备注',
                                     `version` int DEFAULT '0' COMMENT '版本号控制并发',
                                     PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='专家评价表';

-- ----------------------------
-- Table structure for t_expert_appraise_info
-- ----------------------------
DROP TABLE IF EXISTS `t_expert_appraise_info`;
CREATE TABLE `t_expert_appraise_info` (
                                          `id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
                                          `score` int DEFAULT NULL COMMENT '分数',
                                          `appraise_column` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '评价具体信息',
                                          `sort` int DEFAULT NULL COMMENT '排序',
                                          `parent_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '父级id',
                                          `created_user_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
                                          `created_time` datetime DEFAULT NULL COMMENT '创建时间',
                                          `updated_user_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '更新人',
                                          `updated_time` datetime DEFAULT NULL COMMENT '更新时间',
                                          `is_delete` int NOT NULL DEFAULT '0' COMMENT '逻辑删除0正常1删除',
                                          `remark` varchar(1024) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '备注',
                                          `version` int NOT NULL DEFAULT '0' COMMENT '版本号控制并发',
                                          `submit_user_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '提交人',
                                          PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='专家评价具体名称表';

-- ----------------------------
-- Table structure for t_expert_change_record
-- ----------------------------
DROP TABLE IF EXISTS `t_expert_change_record`;
CREATE TABLE `t_expert_change_record` (
                                          `id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '主键',
                                          `type` int DEFAULT NULL COMMENT '变更类型(1.准入 2.编辑信息 3.出库   4.暂停  5.恢复)',
                                          `status` int DEFAULT NULL COMMENT '状态（10.已通过   20.待审核    30. 已退回）',
                                          `expert_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '专家id',
                                          `submit_user` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '操作人id',
                                          `submit_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '提交人姓名',
                                          `submit_time` datetime DEFAULT NULL COMMENT '提交时间',
                                          `reason` text CHARACTER SET utf8 COLLATE utf8_general_ci COMMENT '修改原因(出库原因 1,满70周岁 2,重大疾病 3,已去世 4,其他）',
                                          `changes_concat` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改内容',
                                          `change_code` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '业务code',
                                          `source` int DEFAULT NULL COMMENT '申请来源（1.专家库  2.jstcc）',
                                          `extraction_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '抽取申请id',
                                          `created_user_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
                                          `created_time` datetime DEFAULT NULL COMMENT '创建时间',
                                          `updated_user_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '更新人',
                                          `updated_time` datetime DEFAULT NULL COMMENT '更新时间',
                                          `is_delete` int NOT NULL DEFAULT '0' COMMENT '逻辑删除 0正常1删除',
                                          `remark` varchar(1024) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '备注',
                                          PRIMARY KEY (`id`) USING BTREE,
                                          KEY `index_expert_id` (`expert_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='专家信息变更记录表';

-- ----------------------------
-- Table structure for t_expert_down_file
-- ----------------------------
DROP TABLE IF EXISTS `t_expert_down_file`;
CREATE TABLE `t_expert_down_file` (
                                      `id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin NOT NULL,
                                      `expert_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT NULL COMMENT '专家id',
                                      `file_type` int DEFAULT NULL COMMENT '0其他1doc2xsl',
                                      `file_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT NULL COMMENT '文件名',
                                      `created_user_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
                                      `created_time` datetime DEFAULT NULL COMMENT '创建时间',
                                      `updated_user_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '更新人',
                                      `updated_time` datetime DEFAULT NULL COMMENT '更新时间',
                                      `is_delete` int NOT NULL DEFAULT '0' COMMENT '逻辑删除 0正常1删除',
                                      `remark` varchar(1024) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '备注',
                                      `version` int NOT NULL DEFAULT '0' COMMENT '版本号控制并发',
                                      `original_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin NOT NULL DEFAULT '' COMMENT '原名',
                                      `oss_id` bigint DEFAULT NULL COMMENT '文件id',
                                      PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_bin;

-- ----------------------------
-- Table structure for t_expert_education_info
-- ----------------------------
DROP TABLE IF EXISTS `t_expert_education_info`;
CREATE TABLE `t_expert_education_info` (
                                           `id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
                                           `expert_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '专家id',
                                           `education_background` int DEFAULT NULL COMMENT '学历（70.博士 60.硕士 50.本科 40.大专 30.高中 20.初中）(对应值按从高到低排序，学历越高，对应的code值越高)',
                                           `degree` int DEFAULT NULL COMMENT '学位（1.博士 2.硕士 3.学士）',
                                           `graduation_school` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '毕业学校',
                                           `major` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '所学专业',
                                           `graduation_certificate` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '毕业证书',
                                           `degree_certificate` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '学位证书',
                                           `created_user_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
                                           `created_time` datetime DEFAULT NULL COMMENT '创建时间',
                                           `updated_user_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '更新人',
                                           `updated_time` datetime DEFAULT NULL COMMENT '更新时间',
                                           `is_delete` int NOT NULL DEFAULT '0' COMMENT '逻辑删除 0正常1删除',
                                           `remark` varchar(1024) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '备注',
                                           `version` int NOT NULL DEFAULT '0' COMMENT '版本号控制并发',
                                           PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='专家学历信息';

-- ----------------------------
-- Table structure for t_expert_experience
-- ----------------------------
DROP TABLE IF EXISTS `t_expert_experience`;
CREATE TABLE `t_expert_experience` (
                                       `id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
                                       `expert_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '专家id',
                                       `is_retire` int DEFAULT NULL COMMENT '是否退休 1是 2否',
                                       `company` varchar(150) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '单位名称',
                                       `organization_num` varchar(18) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '统一社会信用代码',
                                       `duties` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '职务',
                                       `start_time` datetime DEFAULT NULL COMMENT '开始年月',
                                       `end_time` datetime DEFAULT NULL COMMENT '结束年月',
                                       `license_file_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '营业执照文件id',
                                       `company_parent` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '父级单位名称',
                                       `organization_num_parent` varchar(18) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '父级单位统一社会信用代码',
                                       `created_user_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
                                       `created_time` datetime DEFAULT NULL COMMENT '创建时间',
                                       `updated_user_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '更新人',
                                       `updated_time` datetime DEFAULT NULL COMMENT '更新时间',
                                       `is_delete` int NOT NULL DEFAULT '0' COMMENT '逻辑删除 0正常1删除',
                                       `remark` varchar(1024) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '备注',
                                       `version` int NOT NULL DEFAULT '0' COMMENT '版本号控制并发',
                                       PRIMARY KEY (`id`) USING BTREE,
                                       KEY `indexes_expert_id` (`expert_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='专家工作经历表';

-- ----------------------------
-- Table structure for t_expert_extraction
-- ----------------------------
DROP TABLE IF EXISTS `t_expert_extraction`;
CREATE TABLE `t_expert_extraction` (
                                       `id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
                                       `extract_batch` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '抽取批次',
                                       `review_time` datetime DEFAULT NULL COMMENT '评审时间',
                                       `review_location` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '评审地点',
                                       `extract_mode` int DEFAULT '1' COMMENT '通知方式 1人工通知  3语音抽取',
                                       `extract_type` int DEFAULT NULL COMMENT '抽取方式  1.直接抽取  2.二次抽取',
                                       `extract_frequency` int DEFAULT NULL COMMENT '抽取次数',
                                       `extract_status` int DEFAULT NULL COMMENT '抽取状态  1起草中、2待抽取、3抽取待确认、4抽取已退回、5抽取中、6待联系、7已抽取、8人数不足、',
                                       `purpose` int DEFAULT NULL COMMENT '专家用途 1.评审 2.复议 3.单一来源论证 4.招标文件评审 5.采购需求论证 6.其他',
                                       `purpose_explain` varchar(800) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '专家用途说明',
                                       `submit_time` datetime DEFAULT NULL COMMENT '提交时间',
                                       `submit_user` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '提交人id',
                                       `submit_user_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '提交人姓名',
                                       `submit_user_phone` varchar(11) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '提交人手机号',
                                       `extraction_pdf_file_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '专家抽取pdf文件',
                                       `join_pdf_file_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '专家参与pdf文件',
                                       `depart_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '部门id',
                                       `depart_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '部门名称',
                                       `source` int DEFAULT '1' COMMENT '数据来源（ 1.专家库  2.jstcc）',
                                       `is_delete` int DEFAULT '0' COMMENT '删除标志(0:正常,1:删除 )',
                                       `updated_user_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
                                       `updated_time` datetime DEFAULT NULL COMMENT '修改时间',
                                       `created_user_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
                                       `created_time` datetime DEFAULT NULL COMMENT '创建时间',
                                       `remark` varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '备注',
                                       `version` int DEFAULT '0' COMMENT '版本号控制并发',
                                       `start_time` datetime DEFAULT NULL COMMENT '抽取开始时间',
                                       `end_time` datetime DEFAULT NULL COMMENT '抽取结束时间',
                                       PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='专家抽取申请表';

-- ----------------------------
-- Table structure for t_expert_extraction_avoid
-- ----------------------------
DROP TABLE IF EXISTS `t_expert_extraction_avoid`;
CREATE TABLE `t_expert_extraction_avoid` (
                                             `id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
                                             `extraction_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '抽取申请id',
                                             `expert_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '专家id',
                                             `expert_no` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '专家编号',
                                             `user_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '专家名称',
                                             `id_no` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '专家身份证',
                                             `mobile_phone` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '专家联系电话',
                                             `avoid_reason` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '回避原因',
                                             `is_delete` int DEFAULT '0' COMMENT '删除标志(0:正常,1:删除 )',
                                             `updated_user_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
                                             `updated_time` datetime DEFAULT NULL COMMENT '修改时间',
                                             `created_user_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
                                             `created_time` datetime DEFAULT NULL COMMENT '创建时间',
                                             `remark` varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '备注',
                                             `version` int DEFAULT '0' COMMENT '版本号控制并发',
                                             PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='专家抽取回避专家表';

-- ----------------------------
-- Table structure for t_expert_extraction_avoid_company
-- ----------------------------
DROP TABLE IF EXISTS `t_expert_extraction_avoid_company`;
CREATE TABLE `t_expert_extraction_avoid_company` (
                                                     `id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
                                                     `extraction_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '抽取申请id',
                                                     `company_name` varchar(800) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '单位名称',
                                                     `avoid_reason` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '回避原因',
                                                     `is_delete` int DEFAULT '0' COMMENT '删除标志(0:正常,1:删除 )',
                                                     `updated_user_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
                                                     `updated_time` datetime DEFAULT NULL COMMENT '修改时间',
                                                     `created_user_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
                                                     `created_time` datetime DEFAULT NULL COMMENT '创建时间',
                                                     `remark` varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '备注',
                                                     `version` int DEFAULT '0' COMMENT '版本号控制并发',
                                                     PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='专家抽取回避企业表';

-- ----------------------------
-- Table structure for t_expert_extraction_code
-- ----------------------------
DROP TABLE IF EXISTS `t_expert_extraction_code`;
CREATE TABLE `t_expert_extraction_code` (
                                            `id` varchar(32) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
                                            `year` varchar(4) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL COMMENT '年',
                                            `month` varchar(4) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL COMMENT '月',
                                            `day` varchar(4) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL COMMENT '日',
                                            `code` int DEFAULT '1' COMMENT '每日的流水code。每天都从1开始',
                                            `is_delete` int DEFAULT '0' COMMENT '删除标志(0:正常,1:删除 )',
                                            `updated_user_id` varchar(32) COLLATE utf8_bin DEFAULT NULL COMMENT '修改人',
                                            `updated_time` datetime DEFAULT NULL COMMENT '修改时间',
                                            `created_user_id` varchar(32) COLLATE utf8_bin DEFAULT NULL COMMENT '创建人',
                                            `created_time` datetime DEFAULT NULL COMMENT '创建时间',
                                            `remark` varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '备注',
                                            `version` int DEFAULT '0' COMMENT '版本号控制并发',
                                            PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8_bin ROW_FORMAT=DYNAMIC COMMENT='专家费付款流水code记录表';

-- ----------------------------
-- Table structure for t_expert_extraction_file
-- ----------------------------
DROP TABLE IF EXISTS `t_expert_extraction_file`;
CREATE TABLE `t_expert_extraction_file` (
                                            `id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin NOT NULL,
                                            `extraction_id` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '抽取申请id',
                                            `file_type` int DEFAULT NULL COMMENT '参标专家确认文件',
                                            `oss_id` varchar(32) COLLATE utf8mb4_0900_bin DEFAULT NULL COMMENT '文件id',
                                            `is_delete` int DEFAULT '0' COMMENT '删除标志(0:正常,1:删除 )',
                                            `updated_user_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
                                            `updated_time` datetime DEFAULT NULL COMMENT '修改时间',
                                            `created_user_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
                                            `created_time` datetime DEFAULT NULL COMMENT '创建时间',
                                            `remark` varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '备注',
                                            `version` int DEFAULT '0' COMMENT '版本号控制并发',
                                            PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_bin COMMENT='抽取申请文件';

-- ----------------------------
-- Table structure for t_expert_extraction_filter
-- ----------------------------
DROP TABLE IF EXISTS `t_expert_extraction_filter`;
CREATE TABLE `t_expert_extraction_filter` (
                                              `id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
                                              `extraction_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '抽取申请id',
                                              `expert_title` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '专家职称 等级 默认0 1 高级 2正高级 3中级 4助理级 5员级',
                                              `expert_region_provincial` text CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '省份',
                                              `expert_region_city` text CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '城市',
                                              `extract_type` int DEFAULT NULL COMMENT '抽取方式  1.直接抽取  2.二次抽取',
                                              `extract_mode` int DEFAULT '1' COMMENT '通知方式 1人工通知  3语音抽取',
                                              `extraction_ratio` int DEFAULT NULL COMMENT '抽取比例',
                                              `company` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '单位名称',
                                              `start_time` datetime DEFAULT NULL COMMENT '抽取开始时间',
                                              `end_time` datetime DEFAULT NULL COMMENT '抽取结束时间',
                                              `start_age` int DEFAULT NULL COMMENT '专家年龄（起）',
                                              `end_age` int DEFAULT NULL COMMENT '专家年龄（止）',
                                              `frequency` int DEFAULT NULL COMMENT '抽取频次',
                                              `is_delete` int DEFAULT '0' COMMENT '删除标志(0:正常,1:删除 )',
                                              `updated_user_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
                                              `updated_time` datetime DEFAULT NULL COMMENT '修改时间',
                                              `created_user_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
                                              `created_time` datetime DEFAULT NULL COMMENT '创建时间',
                                              `remark` varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '备注',
                                              `version` int DEFAULT '0' COMMENT '版本号控制并发',
                                              PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='专家抽取条件表';

-- ----------------------------
-- Table structure for t_expert_extraction_filter_details
-- ----------------------------
DROP TABLE IF EXISTS `t_expert_extraction_filter_details`;
CREATE TABLE `t_expert_extraction_filter_details` (
                                                      `id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
                                                      `filter_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '抽取条件id',
                                                      `major_ids` text CHARACTER SET utf8 COLLATE utf8_general_ci COMMENT '专业id集合',
                                                      `major_name` text CHARACTER SET utf8 COLLATE utf8_general_ci COMMENT '专业名称集合',
                                                      `industry_ids` text CHARACTER SET utf8 COLLATE utf8_general_ci COMMENT '行业id集合',
                                                      `industry_name` text CHARACTER SET utf8 COLLATE utf8_general_ci COMMENT '行业名称',
                                                      `people_number` int DEFAULT NULL COMMENT '人数',
                                                      `is_delete` int DEFAULT '0' COMMENT '删除标志(0:正常,1:删除 )',
                                                      `updated_user_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
                                                      `updated_time` datetime DEFAULT NULL COMMENT '修改时间',
                                                      `created_user_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
                                                      `created_time` datetime DEFAULT NULL COMMENT '创建时间',
                                                      `remark` varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '备注',
                                                      `version` int DEFAULT '0' COMMENT '版本号控制并发',
                                                      PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='专家抽取条件明细表';

-- ----------------------------
-- Table structure for t_expert_extraction_limit
-- ----------------------------
DROP TABLE IF EXISTS `t_expert_extraction_limit`;
CREATE TABLE `t_expert_extraction_limit` (
                                             `id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
                                             `limit_type` int DEFAULT NULL COMMENT '限制方式（1.部门 2.公司）',
                                             `limit_frequency` int DEFAULT NULL COMMENT '参标次数',
                                             `limit_rate` int DEFAULT NULL COMMENT '参标比例',
                                             `is_delete` int DEFAULT '0' COMMENT '删除标志(0:正常,1:删除 )',
                                             `updated_user_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
                                             `updated_time` datetime DEFAULT NULL COMMENT '修改时间',
                                             `created_user_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
                                             `created_time` datetime DEFAULT NULL COMMENT '创建时间',
                                             `remark` varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '备注',
                                             `version` int DEFAULT '0' COMMENT '版本号控制并发',
                                             PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='专家抽取设置';

-- ----------------------------
-- Table structure for t_expert_extraction_limit_area
-- ----------------------------
DROP TABLE IF EXISTS `t_expert_extraction_limit_area`;
CREATE TABLE `t_expert_extraction_limit_area` (
                                                  `id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
                                                  `limit_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '抽取设置id',
                                                  `province` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '省份',
                                                  `city` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '城市',
                                                  `city_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '区域名称',
                                                  `limit_times` int DEFAULT NULL COMMENT '参标次数',
                                                  `created_user_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
                                                  `created_time` datetime DEFAULT NULL COMMENT '创建时间',
                                                  `updated_user_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '更新人',
                                                  `updated_time` datetime DEFAULT NULL COMMENT '更新时间',
                                                  `is_delete` int NOT NULL DEFAULT '0' COMMENT '逻辑删除 0正常1删除',
                                                  `remark` varchar(1024) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '备注',
                                                  `version` int NOT NULL DEFAULT '0' COMMENT '版本号控制并发',
                                                  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='专家抽取设置参评区域';

-- ----------------------------
-- Table structure for t_expert_extraction_limit_specialty
-- ----------------------------
DROP TABLE IF EXISTS `t_expert_extraction_limit_specialty`;
CREATE TABLE `t_expert_extraction_limit_specialty` (
                                                       `id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
                                                       `limit_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '抽取设置id',
                                                       `specialty_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '评标专业',
                                                       `specialty_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '专业名称',
                                                       `limit_times` int DEFAULT NULL COMMENT '参标次数',
                                                       `created_user_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
                                                       `created_time` datetime DEFAULT NULL COMMENT '创建时间',
                                                       `updated_user_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '更新人',
                                                       `updated_time` datetime DEFAULT NULL COMMENT '更新时间',
                                                       `is_delete` int NOT NULL DEFAULT '0' COMMENT '逻辑删除 0正常1删除',
                                                       `remark` varchar(1024) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '备注',
                                                       `version` int NOT NULL DEFAULT '0' COMMENT '版本号控制并发',
                                                       PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='专家抽取设置评标专业';

-- ----------------------------
-- Table structure for t_expert_extraction_project
-- ----------------------------
DROP TABLE IF EXISTS `t_expert_extraction_project`;
CREATE TABLE `t_expert_extraction_project` (
                                               `id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
                                               `extraction_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '抽取申请id',
                                               `project_name` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '项目名称',
                                               `purchase_number` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '采购编号',
                                               `dept_name` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '所属处室',
                                               `master_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '项目经理名称',
                                               `master_phone` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '项目经理手机号',
                                               `principal_company` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '委托单位',
                                               `organization_num` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '社会统一信用代码',
                                               `submit_end_time` datetime DEFAULT NULL COMMENT '文件响应截止时间',
                                               `package_number` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '包段编号 ',
                                               `package_name` varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '包名称',
                                               `bid_round` smallint DEFAULT '1' COMMENT '招标阶段：\r\n1：第一轮；\r\n2：第二轮',
                                               `is_min_time` int DEFAULT '0' COMMENT '是否是最小时间 0 否 1是',
                                               `source` int DEFAULT NULL COMMENT '数据来源（ 1.专家库  2.jstcc）',
                                               `is_delete` int DEFAULT '0' COMMENT '删除标志(0:正常,1:删除 )',
                                               `updated_user_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
                                               `updated_time` datetime DEFAULT NULL COMMENT '修改时间',
                                               `created_user_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
                                               `created_time` datetime DEFAULT NULL COMMENT '创建时间',
                                               `remark` varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '备注',
                                               `version` int DEFAULT '0' COMMENT '版本号控制并发',
                                               PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='专家抽取项目信息表';

-- ----------------------------
-- Table structure for t_expert_extraction_record
-- ----------------------------
DROP TABLE IF EXISTS `t_expert_extraction_record`;
CREATE TABLE `t_expert_extraction_record` (
                                              `id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
                                              `extraction_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '抽取申请id',
                                              `filter_detail_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '抽取条件详情表id',
                                              `frequency` int DEFAULT NULL COMMENT '抽取次数',
                                              `expert_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '专家id',
                                              `expert_no` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT '' COMMENT '专家id',
                                              `verification_code` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '验证码',
                                              `expert_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '专家名称',
                                              `identity_card` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '专家身份证',
                                              `id_file` varchar(300) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '身份证正面文件地址',
                                              `id_back_file` varchar(300) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '身份证反面文件地址',
                                              `contact_number` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '专家联系电话',
                                              `position` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '职务',
                                              `province` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '所在省份',
                                              `city` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '所在城市',
                                              `expert_title` varchar(300) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '专家职称级别 默认0 1 高级 2正高级 3中级 4助理级 5员级',
                                              `expert_title_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '专家职称名称',
                                              `expert_type` int DEFAULT NULL COMMENT '专家类型 1 A1; 2 A2 ;3 C1;',
                                              `company_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '所在单位',
                                              `company_phone` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '单位电话',
                                              `expert_major_id` text CHARACTER SET utf8 COLLATE utf8_general_ci COMMENT '专家专业id',
                                              `expert_major` text CHARACTER SET utf8 COLLATE utf8_general_ci COMMENT '专家专业',
                                              `join_type` int DEFAULT NULL COMMENT '参加状态 1 参加 2 不参加',
                                              `join_reason` int DEFAULT NULL COMMENT '不参加原因 1无应答 2已通知无法参加 3已抽满 4超时未联系 5待出库处理 6 已抽满',
                                              `notification_time` datetime DEFAULT NULL COMMENT '通知时间',
                                              `leave_reason` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '请假原因',
                                              `spenames_id` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin COMMENT '专业id',
                                              `spenames` text CHARACTER SET utf8 COLLATE utf8_general_ci COMMENT '专家评标专业',
                                              `industry_id` text COMMENT '专家行业id',
                                              `industry` text COMMENT '专家行业',
                                              `expire_time` datetime DEFAULT NULL COMMENT '过期时间',
                                              `liaison_time` int DEFAULT NULL COMMENT '默认联络时间(分钟)',
                                              `extraction_time` datetime DEFAULT NULL COMMENT '抽取时间',
                                              `is_emergency` int DEFAULT NULL COMMENT '是否应急',
                                              `is_delete` int DEFAULT '0' COMMENT '删除标志(0:正常,1:删除 )',
                                              `updated_user_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
                                              `updated_time` datetime DEFAULT NULL COMMENT '修改时间',
                                              `created_user_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
                                              `created_time` datetime DEFAULT NULL COMMENT '创建时间',
                                              `remark` varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '备注',
                                              `version` int DEFAULT '0' COMMENT '版本号控制并发',
                                              PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='专家抽取结果记录';

-- ----------------------------
-- Table structure for t_expert_industry
-- ----------------------------
DROP TABLE IF EXISTS `t_expert_industry`;
CREATE TABLE `t_expert_industry` (
                                     `id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '主键',
                                     `expert_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '专家id',
                                     `industry_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '评标行业id',
                                     `year` int DEFAULT NULL COMMENT '从事专业年限',
                                     `get_time` datetime DEFAULT NULL COMMENT '获得时间',
                                     `created_user_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
                                     `created_time` datetime DEFAULT NULL COMMENT '创建时间',
                                     `updated_user_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '更新人',
                                     `updated_time` datetime DEFAULT NULL COMMENT '更新时间',
                                     `is_delete` int DEFAULT '0' COMMENT '逻辑删除0正常1删除',
                                     `remark` varchar(1024) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '备注',
                                     `version` int DEFAULT '0' COMMENT '版本号控制并发',
                                     PRIMARY KEY (`id`) USING BTREE,
                                     KEY `indexes_expert_id` (`expert_id`) USING BTREE,
                                     KEY `indexes_industry_id` (`industry_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='专家评标行业表';

-- ----------------------------
-- Table structure for t_expert_info
-- ----------------------------
DROP TABLE IF EXISTS `t_expert_info`;
CREATE TABLE `t_expert_info` (
                                 `id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '主键(32位uuid)',
                                 `user_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '用户id',
                                 `user_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '姓名',
                                 `old_expert_id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '老专家id',
                                 `old_user_id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '老用户id',
                                 `sex` int DEFAULT NULL COMMENT '性别（1.男 2.女）',
                                 `birthday` date DEFAULT NULL COMMENT '出生年月',
                                 `phone` varchar(11) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '手机号',
                                 `phone_bak` varchar(11) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '备用电话',
                                 `qq` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'QQ号码',
                                 `e_mail` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '电子邮箱',
                                 `province` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '所在省份',
                                 `city` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '所在城市',
                                 `is_emergency` int DEFAULT NULL COMMENT '是否为应急专家（1.是  2.否）',
                                 `id_type` int DEFAULT NULL COMMENT '证件类型（1.身份证 2.军官证 3.护照 4.港澳台身份证）',
                                 `id_num` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '证件号',
                                 `id_card_front` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '身份证正面',
                                 `id_card_reverse` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '身份证反面',
                                 `certificate` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '证照文件',
                                 `bank_card` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '银行卡号',
                                 `bank_type` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '所属银行',
                                 `referral_code` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '推荐码',
                                 `is_retire` int DEFAULT NULL COMMENT '是否退休 1是 2否',
                                 `status` int DEFAULT NULL COMMENT '专家状态\r\n10：待提交（已注册，但未提交基本信息等信息到专家库管理员审核）\r\n20：待审核（已提交基本信息等信息至专家库管理员审核）\r\n21：暂停待审核\r\n30：正常（审核通过）\r\n40：退回（审核退回）\r\n50：暂停（专家扣分后被系统自动暂停，或由专家库管理员手动暂停）\r\n60：出库（专家出库审核通过）\r\n70：待出库（待出库）',
                                 `start_bid_time` date DEFAULT NULL COMMENT '开始暂停时间',
                                 `end_bid_time` date DEFAULT NULL COMMENT '结束暂停时间',
                                 `audit_time` datetime DEFAULT NULL COMMENT '入库时间',
                                 `out_reason` varchar(50) DEFAULT NULL COMMENT '出库原因',
                                 `out_time` datetime DEFAULT NULL COMMENT '出库时间',
                                 `pause_type` int DEFAULT NULL COMMENT '暂停类型（1.指定日期，2.永久）',
                                 `duration` int DEFAULT NULL COMMENT '暂停持续时间',
                                 `score` int DEFAULT '100' COMMENT '专家分数',
                                 `expert_type` int DEFAULT NULL COMMENT '专家类型 1 A1; 2 A2 ;3 C1;',
                                 `created_user_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
                                 `created_time` datetime DEFAULT NULL COMMENT '创建时间',
                                 `updated_user_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '更新人',
                                 `updated_time` datetime DEFAULT NULL COMMENT '更新时间',
                                 `is_delete` int NOT NULL DEFAULT '0' COMMENT '逻辑删除 0正常1删除',
                                 `remark` varchar(1024) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '备注',
                                 `version` int NOT NULL DEFAULT '0' COMMENT '版本号控制并发',
                                 `is_send_sms` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '上线后是否发送评标专业修改短信 0：不发；1：发',
                                 `is_update` int DEFAULT NULL COMMENT '1入库2变更',
                                 PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='专家身份信息';

-- ----------------------------
-- Table structure for t_expert_info_update_apply
-- ----------------------------
DROP TABLE IF EXISTS `t_expert_info_update_apply`;
CREATE TABLE `t_expert_info_update_apply` (
                                              `id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
                                              `extraction_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '抽取申请id',
                                              `expert_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '专家id',
                                              `source` int DEFAULT '1' COMMENT '数据来源（ 1.专家库  2.jstcc）',
                                              `status` int DEFAULT NULL COMMENT '状态（10.已通过   20.待审核    30. 已退回）',
                                              `reason` text CHARACTER SET utf8 COLLATE utf8_general_ci COMMENT '修改原因',
                                              `submit_time` datetime DEFAULT NULL COMMENT '提交时间',
                                              `submit_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '提交人姓名',
                                              `is_delete` int DEFAULT '0' COMMENT '删除标志(0:正常,1:删除 )',
                                              `updated_user_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
                                              `updated_time` datetime DEFAULT NULL COMMENT '修改时间',
                                              `created_user_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
                                              `created_time` datetime DEFAULT NULL COMMENT '创建时间',
                                              `remark` varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '备注',
                                              `version` int DEFAULT '0' COMMENT '版本号控制并发',
                                              `changes_concat` varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改内容',
                                              PRIMARY KEY (`id`) USING BTREE,
                                              KEY `index_expert_id` (`expert_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='专家信息修改申请';

-- ----------------------------
-- Table structure for t_expert_info_update_log
-- ----------------------------
DROP TABLE IF EXISTS `t_expert_info_update_log`;
CREATE TABLE `t_expert_info_update_log` (
                                            `id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin NOT NULL,
                                            `update_apply_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT NULL COMMENT '修改申请id',
                                            `expert_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT NULL COMMENT '专家id',
                                            `update_json` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin COMMENT '修改前信息',
                                            `submit_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT NULL COMMENT '提交人姓名',
                                            `source` int DEFAULT '1' COMMENT '数据来源（ 1.专家库  2.jstcc）',
                                            `is_delete` int DEFAULT '0' COMMENT '删除标志(0:正常,1:删除 )',
                                            `updated_user_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT NULL COMMENT '修改人',
                                            `updated_time` datetime DEFAULT NULL COMMENT '修改时间',
                                            `created_user_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT NULL COMMENT '创建人',
                                            `created_time` datetime DEFAULT NULL COMMENT '创建时间',
                                            `remark` varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '备注',
                                            `version` int DEFAULT '0' COMMENT '版本号控制并发',
                                            PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_bin COMMENT='专家修改记录';

-- ----------------------------
-- Table structure for t_expert_info_wechat_update_log
-- ----------------------------
DROP TABLE IF EXISTS `t_expert_info_wechat_update_log`;
CREATE TABLE `t_expert_info_wechat_update_log` (
                                                   `id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin NOT NULL,
                                                   `update_apply_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT NULL COMMENT '修改申请id',
                                                   `expert_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT NULL COMMENT '专家id',
                                                   `update_json` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin COMMENT '修改前信息',
                                                   `submit_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT NULL COMMENT '提交人姓名',
                                                   `source` int DEFAULT '1' COMMENT '数据来源（ 1.专家库  2.jstcc）',
                                                   `is_delete` int DEFAULT '0' COMMENT '删除标志(0:正常,1:删除 )',
                                                   `updated_user_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT NULL COMMENT '修改人',
                                                   `updated_time` datetime DEFAULT NULL COMMENT '修改时间',
                                                   `created_user_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT NULL COMMENT '创建人',
                                                   `created_time` datetime DEFAULT NULL COMMENT '创建时间',
                                                   `remark` varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '备注',
                                                   `version` int DEFAULT '0' COMMENT '版本号控制并发',
                                                   PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_bin COMMENT='专家修改记录';

-- ----------------------------
-- Table structure for t_expert_practice
-- ----------------------------
DROP TABLE IF EXISTS `t_expert_practice`;
CREATE TABLE `t_expert_practice` (
                                     `id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '执业资格id',
                                     `expert_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '专家id',
                                     `certificate` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '执业资格',
                                     `certificate_no` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '资格证书号',
                                     `get_time` datetime DEFAULT NULL COMMENT '获得时间',
                                     `certificate_file_id` varchar(255) DEFAULT NULL COMMENT '资格证书复印件',
                                     `created_user_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
                                     `created_time` datetime DEFAULT NULL COMMENT '创建时间',
                                     `updated_user_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '更新人',
                                     `updated_time` datetime DEFAULT NULL COMMENT '更新时间',
                                     `is_delete` int NOT NULL DEFAULT '0' COMMENT '逻辑删除 0正常1删除',
                                     `remark` varchar(1024) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '备注',
                                     `version` int NOT NULL DEFAULT '0' COMMENT '版本号控制并发',
                                     PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='执业资格表';

-- ----------------------------
-- Table structure for t_expert_social_experience
-- ----------------------------
DROP TABLE IF EXISTS `t_expert_social_experience`;
CREATE TABLE `t_expert_social_experience` (
                                              `id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
                                              `expert_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '专家id',
                                              `academic` text CHARACTER SET utf8 COLLATE utf8_general_ci COMMENT '学术专长与研究方向',
                                              `experience` text CHARACTER SET utf8 COLLATE utf8_general_ci COMMENT '评标经验',
                                              `training` text CHARACTER SET utf8 COLLATE utf8_general_ci COMMENT '培训经历',
                                              `other` text CHARACTER SET utf8 COLLATE utf8_general_ci COMMENT '其他说明',
                                              `created_user_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
                                              `created_time` datetime DEFAULT NULL COMMENT '创建时间',
                                              `updated_user_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '更新人',
                                              `updated_time` datetime DEFAULT NULL COMMENT '更新时间',
                                              `is_delete` int NOT NULL DEFAULT '0' COMMENT '逻辑删除 0正常1删除',
                                              `remark` varchar(1024) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '备注',
                                              `version` int NOT NULL DEFAULT '0' COMMENT '版本号控制并发',
                                              PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='专家社会经验';

-- ----------------------------
-- Table structure for t_expert_specialty
-- ----------------------------
DROP TABLE IF EXISTS `t_expert_specialty`;
CREATE TABLE `t_expert_specialty` (
                                      `id` varchar(32) NOT NULL COMMENT '主键',
                                      `expert_id` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '专家id',
                                      `specialty_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '评标专业id',
                                      `year` int DEFAULT NULL COMMENT '从事专业年限',
                                      `get_time` datetime DEFAULT NULL COMMENT '获得时间',
                                      `created_user_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
                                      `created_time` datetime DEFAULT NULL COMMENT '创建时间',
                                      `updated_user_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '更新人',
                                      `updated_time` datetime DEFAULT NULL COMMENT '更新时间',
                                      `is_delete` int DEFAULT '0' COMMENT '逻辑删除0正常1删除',
                                      `remark` varchar(1024) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '备注',
                                      `version` int DEFAULT '0' COMMENT '版本号控制并发',
                                      PRIMARY KEY (`id`) USING BTREE,
                                      KEY `indexes_expert_id` (`expert_id`) USING BTREE,
                                      KEY `indexes_specialty_id` (`specialty_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='专家评标专业表';

-- ----------------------------
-- Table structure for t_expert_title_info
-- ----------------------------
DROP TABLE IF EXISTS `t_expert_title_info`;
CREATE TABLE `t_expert_title_info` (
                                       `id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
                                       `expert_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '专家id',
                                       `professional_title` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '职称',
                                       `get_time` date DEFAULT NULL COMMENT '获得证书日期',
                                       `title_certificate` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '职称证书',
                                       `created_user_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
                                       `created_time` datetime DEFAULT NULL COMMENT '创建时间',
                                       `updated_user_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '更新人',
                                       `updated_time` datetime DEFAULT NULL COMMENT '更新时间',
                                       `is_delete` int NOT NULL DEFAULT '0' COMMENT '逻辑删除 0正常1删除',
                                       `remark` varchar(1024) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '备注',
                                       `version` int NOT NULL DEFAULT '0' COMMENT '版本号控制并发',
                                       PRIMARY KEY (`id`) USING BTREE,
                                       KEY `indexes_expert_id` (`expert_id`) USING BTREE,
                                       KEY `indexes_professional_title` (`professional_title`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='专家职称信息';

-- ----------------------------
-- Table structure for t_id_verify_record
-- ----------------------------
DROP TABLE IF EXISTS `t_id_verify_record`;
CREATE TABLE `t_id_verify_record` (
                                      `id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
                                      `order_number` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '订单号',
                                      `user_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '名字',
                                      `identity_number` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '身份证号码',
                                      `validate_result` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '验证结果',
                                      `is_delete` int DEFAULT '0' COMMENT '删除标志(0:正常,1:删除 )',
                                      `updated_user_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
                                      `updated_time` datetime DEFAULT NULL COMMENT '修改时间',
                                      `created_user_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
                                      `created_time` datetime DEFAULT NULL COMMENT '创建时间',
                                      `remark` varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '备注',
                                      `version` int DEFAULT '0' COMMENT '版本号控制并发',
                                      PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='实名验证结果';

-- ----------------------------
-- Table structure for t_industry_info
-- ----------------------------
DROP TABLE IF EXISTS `t_industry_info`;
CREATE TABLE `t_industry_info` (
                                   `id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '评标行业id',
                                   `industry_parent_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '行业父类id',
                                   `industry_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '行业名称',
                                   `industry_parent_name` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '行业父级名称',
                                   `industry_code` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '行业代码',
                                   `industry_level` int DEFAULT NULL COMMENT '行业级别',
                                   `created_user_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
                                   `created_time` datetime DEFAULT NULL COMMENT '创建时间',
                                   `updated_user_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '更新人',
                                   `updated_time` datetime DEFAULT NULL COMMENT '更新时间',
                                   `is_delete` int NOT NULL DEFAULT '0' COMMENT '逻辑删除 0正常1删除',
                                   `remark` varchar(1024) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '备注',
                                   `version` int NOT NULL DEFAULT '0' COMMENT '版本号控制并发',
                                   `submit_user_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '提交人name',
                                   PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='评标行业字典表';

-- ----------------------------
-- Table structure for t_policies
-- ----------------------------
DROP TABLE IF EXISTS `t_policies`;
CREATE TABLE `t_policies` (
                              `id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
                              `policies_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '目录名称',
                              `policies_code` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '目录编号',
                              `policies_type` int DEFAULT NULL COMMENT '目录类型（1.国家级 2.地方级）',
                              `created_user_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
                              `created_time` datetime DEFAULT NULL COMMENT '创建时间',
                              `updated_user_id` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '更新人',
                              `updated_time` datetime DEFAULT NULL COMMENT '更新时间',
                              `submit_user_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '提交人name',
                              `submit_user_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '提交人id',
                              `submit_time` datetime DEFAULT NULL COMMENT '提交时间',
                              `is_delete` int NOT NULL DEFAULT '0' COMMENT '逻辑删除 0正常1删除',
                              `remark` varchar(1024) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '备注',
                              `version` int NOT NULL DEFAULT '0' COMMENT '版本号控制并发',
                              PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='政策法规目录表';

-- ----------------------------
-- Table structure for t_policies_details
-- ----------------------------
DROP TABLE IF EXISTS `t_policies_details`;
CREATE TABLE `t_policies_details` (
                                      `id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
                                      `title` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '标题',
                                      `policies_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '目录',
                                      `issuing_unit` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '发文单位',
                                      `symbol` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '文号',
                                      `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin COMMENT '政策内容',
                                      `file` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '附件',
                                      `created_user_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
                                      `created_time` datetime DEFAULT NULL COMMENT '创建时间',
                                      `updated_user_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '更新人',
                                      `updated_time` datetime DEFAULT NULL COMMENT '更新时间',
                                      `submit_user_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '提交人name',
                                      `submit_user_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '提交人id',
                                      `submit_time` datetime DEFAULT NULL COMMENT '提交时间',
                                      `is_delete` int NOT NULL DEFAULT '0' COMMENT '逻辑删除 0正常1删除',
                                      `remark` varchar(1024) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '备注',
                                      `version` int NOT NULL DEFAULT '0' COMMENT '版本号控制并发',
                                      `status` int DEFAULT NULL COMMENT '状态 0 起草中 10 已发布',
                                      PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='政策法规明细表';

-- ----------------------------
-- Table structure for t_policies_details_file
-- ----------------------------
DROP TABLE IF EXISTS `t_policies_details_file`;
CREATE TABLE `t_policies_details_file` (
                                           `id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
                                           `policies_details_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '明细id',
                                           `oss_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '附件',
                                           `created_user_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
                                           `created_time` datetime DEFAULT NULL COMMENT '创建时间',
                                           `updated_user_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '更新人',
                                           `updated_time` datetime DEFAULT NULL COMMENT '更新时间',
                                           `is_delete` int NOT NULL DEFAULT '0' COMMENT '逻辑删除 0正常1删除',
                                           `remark` varchar(1024) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '备注',
                                           `version` int NOT NULL DEFAULT '0' COMMENT '版本号控制并发',
                                           PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='政策法规明细附件表';

-- ----------------------------
-- Table structure for t_process_record
-- ----------------------------
DROP TABLE IF EXISTS `t_process_record`;
CREATE TABLE `t_process_record` (
                                    `id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
                                    `change_code` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '业务code',
                                    `change_id` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '业务id',
                                    `operator_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '操作人id',
                                    `operator_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '操作人姓名',
                                    `operation` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '操作（中文）',
                                    `file_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '附件id',
                                    `next_user_role_code` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '下一步审核人角色code',
                                    `created_user_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
                                    `created_time` datetime DEFAULT NULL COMMENT '创建时间',
                                    `updated_user_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
                                    `updated_time` datetime DEFAULT NULL COMMENT '修改时间',
                                    `is_delete` int DEFAULT '0' COMMENT '逻辑删除0正常1删除',
                                    `remark` varchar(1024) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '备注',
                                    `version` int DEFAULT '0' COMMENT '版本',
                                    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='操作流程记录表';

-- ----------------------------
-- Table structure for t_qr_code
-- ----------------------------
DROP TABLE IF EXISTS `t_qr_code`;
CREATE TABLE `t_qr_code` (
                             `id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin NOT NULL,
                             `qr_code` varchar(5) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT NULL COMMENT '推荐码',
                             `use_system` int DEFAULT NULL COMMENT '使用系统（1.专家库  2.jstcc）',
                             `is_delete` int DEFAULT '0' COMMENT '删除标志(0:正常,1:删除 )',
                             `updated_user_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
                             `updated_time` datetime DEFAULT NULL COMMENT '修改时间',
                             `created_user_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
                             `created_time` datetime DEFAULT NULL COMMENT '创建时间',
                             `remark` varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '备注',
                             `version` int DEFAULT '0' COMMENT '版本号控制并发',
                             PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_bin COMMENT='推荐码';

-- ----------------------------
-- Table structure for t_recommend
-- ----------------------------
DROP TABLE IF EXISTS `t_recommend`;
CREATE TABLE `t_recommend` (
                               `id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
                               `year` varchar(4) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '年份',
                               `user_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '用户id',
                               `old_user_id` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '用户编号',
                               `index_num` varchar(5) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '指标人数',
                               `created_user_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
                               `created_time` datetime DEFAULT NULL COMMENT '创建时间',
                               `updated_user_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '更新人',
                               `updated_time` datetime DEFAULT NULL COMMENT '更新时间',
                               `is_delete` int NOT NULL DEFAULT '0' COMMENT '逻辑删除 0正常1删除',
                               `remark` varchar(1024) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '备注',
                               `version` int NOT NULL DEFAULT '0' COMMENT '版本号控制并发',
                               PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='专家推荐指标表';

-- ----------------------------
-- Table structure for t_sms_record
-- ----------------------------
DROP TABLE IF EXISTS `t_sms_record`;
CREATE TABLE `t_sms_record` (
                                `id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '主键',
                                `sms_user_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '发送用户id',
                                `sms_user_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '发送用户姓名',
                                `sms_phone` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '用户手机号',
                                `sms_content` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '短信内容',
                                `sms_type` int DEFAULT NULL COMMENT '短信类型  1：邀请专家    2：通知抽取人抽取专家   3：通知项目负责人修改条件 ',
                                `sms_time` datetime DEFAULT NULL COMMENT '发送短信时间',
                                `sms_variety` int DEFAULT NULL COMMENT '属于短信发送还是语音发送(1.人工，2.语音,3.语音短信)',
                                `sms_result` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '发送之后返回信息',
                                `is_accept` int DEFAULT NULL COMMENT '专家是否成功接收',
                                `mw_msgid` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '梦网短信对接ID',
                                `mw_result` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '梦网发送短信是否成功记录',
                                `change_result` int DEFAULT NULL COMMENT '当前短信状态1.通知专家参加评标 2.项目变更 3.项目取消',
                                `inform_id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '通知综合处的短信ID',
                                `is_dispose` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '是否处理1.处理 0.未处理（用于语音通知时，专家未收到短信，由综合处处理）',
                                `created_user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT '' COMMENT '创建者',
                                `created_time` datetime DEFAULT NULL COMMENT '创建时间',
                                `updated_user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT '' COMMENT '更新者',
                                `updated_time` datetime DEFAULT NULL COMMENT '更新时间',
                                `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT NULL COMMENT '备注',
                                `is_delete` int DEFAULT NULL,
                                PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='短信记录表';

-- ----------------------------
-- Table structure for t_specialty_info
-- ----------------------------
DROP TABLE IF EXISTS `t_specialty_info`;
CREATE TABLE `t_specialty_info` (
                                    `id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '主键',
                                    `specialty_parent_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '评标专业父类id',
                                    `specialty_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '评标专业名称',
                                    `specialty_parent_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '评标专业父类名称',
                                    `specialty_code` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '评标专业代码',
                                    `specialty_level` int DEFAULT NULL COMMENT '评标专业级别',
                                    `is_visible` int DEFAULT NULL COMMENT '专家是否可见(0：专家可见；1：不可见)',
                                    `old_specialty_id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '老专家库id',
                                    `old_specialty_code` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '老专家库code',
                                    `old_specialty_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '老专家库名称',
                                    `created_user_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
                                    `created_time` datetime DEFAULT NULL COMMENT '创建时间',
                                    `updated_user_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '更新人',
                                    `updated_time` datetime DEFAULT NULL COMMENT '更新时间',
                                    `is_delete` int NOT NULL DEFAULT '0' COMMENT '逻辑删除 0正常1删除',
                                    `remark` varchar(1024) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '备注',
                                    `version` int NOT NULL DEFAULT '0' COMMENT '版本号控制并发',
                                    `submit_user_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '提交人name',
                                    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='评标专业字典表';

-- ----------------------------
-- Table structure for t_specialty_info_old
-- ----------------------------
DROP TABLE IF EXISTS `t_specialty_info_old`;
CREATE TABLE `t_specialty_info_old` (
                                        `id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin NOT NULL COMMENT '评标专业id',
                                        `specialty_parent_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT NULL COMMENT '评标专业父类id',
                                        `specialty_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT NULL COMMENT '评标专业名称',
                                        `specialty_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT NULL COMMENT '评标专业代码',
                                        `specialty_level` int DEFAULT NULL COMMENT '评标专业级别',
                                        `is_visible` int DEFAULT NULL COMMENT '专家是否可见',
                                        `created_user_id` bigint DEFAULT NULL COMMENT '创建人',
                                        `created_time` datetime DEFAULT NULL COMMENT '创建时间',
                                        `updated_user_id` bigint DEFAULT NULL COMMENT '更新人',
                                        `updated_time` datetime DEFAULT NULL COMMENT '更新时间',
                                        `is_delete` int NOT NULL DEFAULT '0' COMMENT '逻辑删除 0正常1删除',
                                        `remark` varchar(1024) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '备注',
                                        `version` int NOT NULL DEFAULT '0' COMMENT '版本号控制并发',
                                        PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_bin COMMENT='评标专业字典表';

-- ----------------------------
-- Table structure for t_sys_app
-- ----------------------------
DROP TABLE IF EXISTS `t_sys_app`;
CREATE TABLE `t_sys_app` (
                             `id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
                             `name` varchar(100) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL COMMENT '名称',
                             `status` int DEFAULT '0' COMMENT '状态 0:启用  1：禁用',
                             `sn` varchar(100) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL COMMENT '系统标示',
                             `secret_key` varchar(1000) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL COMMENT '密码',
                             `url` varchar(1000) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL COMMENT '系统url前缀',
                             `index_url` varchar(1000) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL COMMENT '系统首页URL',
                             `image` blob COMMENT '系统的图标',
                             `order_no` int DEFAULT NULL COMMENT '排序',
                             `created_user_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
                             `created_time` datetime DEFAULT NULL COMMENT '创建时间',
                             `updated_user_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '更新人',
                             `is_delete` int DEFAULT '0' COMMENT '逻辑删除 0正常1删除',
                             `updated_time` datetime DEFAULT NULL COMMENT '更新时间',
                             `remark` varchar(1024) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '备注',
                             `version` int DEFAULT '0' COMMENT '版本号控制并发',
                             `current_sys` int DEFAULT '1' COMMENT '是否当前系统 1是0 否',
                             PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;

-- ----------------------------
-- Table structure for t_technical_tital
-- ----------------------------
DROP TABLE IF EXISTS `t_technical_tital`;
CREATE TABLE `t_technical_tital` (
                                     `id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
                                     `title_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '职称名称',
                                     `title_code` varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '职称code',
                                     `level` int DEFAULT '0' COMMENT '等级 默认0 1 高级 2正高级 3中级 4助理级 5员级级别',
                                     `old_id` bigint DEFAULT NULL,
                                     `created_user_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
                                     `created_time` datetime DEFAULT NULL COMMENT '创建时间',
                                     `updated_user_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '更新人',
                                     `updated_time` datetime DEFAULT NULL COMMENT '更新时间',
                                     `is_delete` int NOT NULL DEFAULT '0' COMMENT '逻辑删除 0正常1删除',
                                     `remark` varchar(1024) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '备注',
                                     `version` int NOT NULL DEFAULT '0' COMMENT '版本号控制并发',
                                     `submit_user_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '提交人name',
                                     PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='职称表';

-- ----------------------------
-- Table structure for t_template
-- ----------------------------
DROP TABLE IF EXISTS `t_template`;
CREATE TABLE `t_template` (
                              `id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
                              `template_code` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '模板code',
                              `template_second_code` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '模板二级CODE',
                              `template_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '模板名称',
                              `template_content` longtext CHARACTER SET utf8 COLLATE utf8_general_ci COMMENT '模板内容',
                              `created_user_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
                              `created_time` datetime DEFAULT NULL COMMENT '创建时间',
                              `updated_user_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '更新人',
                              `updated_time` datetime DEFAULT NULL COMMENT '更新时间',
                              `is_delete` int DEFAULT '0' COMMENT '逻辑删除0正常1删除',
                              `remark` varchar(1024) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '备注',
                              `version` int DEFAULT '0' COMMENT '版本号控制并发',
                              PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='短信模板表';

-- ----------------------------
-- Table structure for t_voice_body
-- ----------------------------
DROP TABLE IF EXISTS `t_voice_body`;
CREATE TABLE `t_voice_body` (
                                `id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin NOT NULL COMMENT '主键',
                                `service_key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT NULL COMMENT '业务关键字',
                                `call_id` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT NULL COMMENT '呼叫中心callId (保证唯一)',
                                `called_num` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT NULL COMMENT '被叫电话',
                                `called_display_number` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT NULL COMMENT '被叫显示号码',
                                `called_stream_no` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT NULL COMMENT '被叫话单流水号',
                                `start_called_time` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT NULL COMMENT '被叫应答时间',
                                `stop_called_time` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT NULL COMMENT '被叫通话结束时间',
                                `called_duration` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT NULL COMMENT '被叫通话时长',
                                `called_cost` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT NULL COMMENT '被叫通话费用',
                                `called_rel_cause` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT NULL COMMENT '被叫结束原因（合并通话状态原因）\r\n		1、正常接通\r\n		2、呼叫遇忙；[通过信令]\r\n		3、用户不在服务区；[通过信令]\r\n		4、用户无应答；[通过信令]\r\n		5、用户关机；[通过信令]\r\n		6、空号；\r\n		7、停机；\r\n		8、号码过期\r\n		9、主叫应答，被叫应答前挂机\r\n		10 、正在通话中\r\n		11、 拒接\r\n		99、其他\r\n		20：主动取消呼叫',
                                `called_ori_rescode` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT NULL COMMENT '被叫结束的原始原因值（信令层面)',
                                `charge_number` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT NULL COMMENT '计费号码 ',
                                `called_rel_reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT NULL COMMENT '被叫释放Reason',
                                `call_out_time` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT NULL,
                                `ms_server` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT NULL COMMENT '媒体服务器名称',
                                `duration` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT NULL COMMENT '计费时长 单位秒',
                                `cost_count` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT NULL COMMENT '计费数量',
                                `vcc_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT NULL COMMENT '企业id',
                                `dtmf_key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT NULL COMMENT '按键',
                                `message_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT NULL COMMENT '业务流水号',
                                `created_user_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
                                `created_time` datetime DEFAULT NULL COMMENT '创建时间',
                                `updated_user_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '更新人',
                                `updated_time` datetime DEFAULT NULL COMMENT '更新时间',
                                `is_delete` int NOT NULL DEFAULT '0' COMMENT '逻辑删除 0正常1删除',
                                `remark` varchar(1024) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '备注',
                                `version` int NOT NULL DEFAULT '0' COMMENT '版本号控制并发',
                                PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_bin COMMENT='ctd语音回复记录';

-- ----------------------------
-- Table structure for t_voice_entity
-- ----------------------------
DROP TABLE IF EXISTS `t_voice_entity`;
CREATE TABLE `t_voice_entity` (
                                  `id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin NOT NULL,
                                  `message_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin NOT NULL COMMENT '业务流水号',
                                  `called_num` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT NULL COMMENT '呼叫人手机号码',
                                  `media_content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin COMMENT '呼叫内容',
                                  `extraction_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT NULL COMMENT '专家抽取申请id',
                                  `record_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT NULL COMMENT '专家抽取记录id',
                                  `is_send_sms` int DEFAULT '2' COMMENT '通知短信是否发送（1 已发送 2未发送 3不参加 4重新通知 5已抽满）',
                                  `times` int DEFAULT '0' COMMENT '联系次数',
                                  `created_user_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
                                  `created_time` datetime DEFAULT NULL COMMENT '创建时间',
                                  `updated_user_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '更新人',
                                  `updated_time` datetime DEFAULT NULL COMMENT '更新时间',
                                  `is_delete` int DEFAULT '0' COMMENT '逻辑删除0正常1删除',
                                  `remark` varchar(1024) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '备注',
                                  `version` int DEFAULT '0' COMMENT '版本号控制并发',
                                  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_bin COMMENT='ctd语音发送记录';

-- ----------------------------
-- Table structure for t_work_process
-- ----------------------------
DROP TABLE IF EXISTS `t_work_process`;
CREATE TABLE `t_work_process` (
                                  `id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin NOT NULL,
                                  `business_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT NULL COMMENT '流程code',
                                  `submit_role` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT NULL COMMENT '提交人角色',
                                  `next_role` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT NULL COMMENT '下一步审批人角色',
                                  `created_user_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
                                  `created_time` datetime DEFAULT NULL COMMENT '创建时间',
                                  `updated_user_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '更新人',
                                  `updated_time` datetime DEFAULT NULL COMMENT '更新时间',
                                  `is_delete` int NOT NULL DEFAULT '0' COMMENT '逻辑删除 0正常1删除',
                                  `remark` varchar(1024) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '备注',
                                  `version` int NOT NULL DEFAULT '0' COMMENT '版本号控制并发',
                                  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_bin COMMENT='审批流程';

-- ----------------------------
-- Table structure for undo_log
-- ----------------------------
DROP TABLE IF EXISTS `undo_log`;
CREATE TABLE `undo_log` (
                            `branch_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin NOT NULL COMMENT 'branch transaction id',
                            `xid` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin NOT NULL COMMENT 'global transaction id',
                            `context` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin NOT NULL COMMENT 'undo_log context,such as serialization',
                            `rollback_info` longblob NOT NULL COMMENT 'rollback info',
                            `log_status` int NOT NULL COMMENT '0:normal status,1:defense status',
                            `log_created` datetime(6) NOT NULL COMMENT 'create datetime',
                            `log_modified` datetime(6) NOT NULL COMMENT 'modify datetime',
                            UNIQUE KEY `ux_undo_log` (`xid`,`branch_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_bin COMMENT='AT transaction mode undo table';

