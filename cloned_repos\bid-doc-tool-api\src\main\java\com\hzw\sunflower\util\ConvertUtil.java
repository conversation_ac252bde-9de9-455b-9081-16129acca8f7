package com.hzw.sunflower.util;

import com.hzw.sunflower.entity.BidDocColumn;

import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @datetime 2024/06/27 14:39
 * @description: 转换工具类
 * @version: 1.0
 */
public class ConvertUtil {

    /**
     * 转换数据
     * @param columnList
     * @param clazz
     * @return
     */
    public static Map<String, String> convertToMap(List<BidDocColumn> columnList, Class<?> clazz) {
        Map<String, String> map = new HashMap<>();
        for (BidDocColumn column : columnList) {
            if (hasProperty(clazz, column.getColumnCode())) {
                map.put(column.getColumnCode(), column.getColumnValue());
            }
        }
        return map;
    }

    private static boolean hasProperty(Class<?> clazz, String columnCode) {
        try {
            Field field = clazz.getDeclaredField(columnCode);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

}
