package com.hzw.sunflower.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.hzw.sunflower.common.BaseBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * Created by zgq on 2021/06/30
 */
@ApiModel("线上开标记录表")
@TableName("t_bid_open_file")
@Data
public class BidOpenFile extends BaseBean {

    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "项目ID")
    private Long projectId;

    @ApiModelProperty(value = "标段id")
    private Long sectionId;

    @ApiModelProperty(value = "oss文件ID")
    private Long fileOssId;

}
