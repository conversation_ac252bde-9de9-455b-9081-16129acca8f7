package com.hzw.sunflower.constant.constantenum;

/**
 * <AUTHOR>
 * @date ：Created in 2023/9/11 10:02
 * @description： 签署文件类型枚举
 * @version: 1.0
 */
public enum ReviewFileTypeEnum {

    // 文件类型 1.评审文件/打分文件 2.评审报告文件
    REVIEW(1, "评审文件"),
    REPORT(4, "评审报告文件"),
    SUMMARY_SCORE(2, "打分汇总文件"),
    PRELIMINARY(3, "初审文件"),
    ;

    private Integer type;

    private String name;

    ReviewFileTypeEnum(Integer type, String name) {
        this.type = type;
        this.name = name;
    }

    public Integer getType() {
        return type;
    }

    public String getName() {
        return name;
    }

}
