package com.hzw.ssm.applets.dao;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.hzw.ssm.applets.entity.Technical;

/**
 * 
 * <AUTHOR>
 * 职称mapper
 */
public interface TechnicalMapper {

	/**
	 * 查询职称信息
	 * @param techn
	 * @return
	 */
	List<Technical> queryPagetechnical(Technical techn);

	/**
	 * 查询单个信息
	 * @param entity
	 * @return
	 */
	Technical getTechnical(Technical entity);

	void updateTechnicalType(@Param("id")String id, @Param("type")String type);

	int getMaxTechnical();

	/**
	 * 新增职称
	 * @param entity
	 */
	void addTechnical(Technical entity);

	/**
	 * 修改职称
	 * @param entity
	 */
	void updateTechnical(Technical entity);

	int checkedTechnicalName(String technicalName);

	Integer selectTechnicUser(@Param("id") String id);
}
