package com.hzw.sunflower.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hzw.sunflower.controller.response.AbnormalApplyRelationVo;
import com.hzw.sunflower.controller.response.BondApplyRelationVo;
import com.hzw.sunflower.entity.BondApplyRelation;
import com.hzw.sunflower.entity.JwtUser;
import com.hzw.sunflower.entity.condition.BondApplyRelationCondition;
import com.hzw.sunflower.entity.condition.BondProjectCondition;

/**
 * <p>
 * 保证金申请关联表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-27
 */
public interface BondApplyRelationService extends IService<BondApplyRelation> {

    /**
     * 异常关联申请列表
     * @param condition
     * @return
     */
    IPage<AbnormalApplyRelationVo> applyRelationList(BondProjectCondition condition);

    /**
     * 分页查询保证金关联申请列表(包含异常关联)
     * @param condition
     * @return
     */
    IPage<BondApplyRelationVo> getBondApplyRelationPage(BondApplyRelationCondition condition, JwtUser jwtUser);
}
