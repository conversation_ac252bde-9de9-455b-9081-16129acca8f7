package com.hzw.ssm.sys.project.action;

import java.io.IOException;
import java.io.PrintWriter;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;

import net.sf.json.JSONArray;
import net.sf.json.JsonConfig;
import net.sf.json.util.PropertyFilter;

import org.apache.struts2.ServletActionContext;
import org.apache.struts2.convention.annotation.Action;
import org.apache.struts2.convention.annotation.Namespace;
import org.apache.struts2.convention.annotation.ParentPackage;
import org.apache.struts2.convention.annotation.Result;
import org.apache.struts2.convention.annotation.Results;
import org.springframework.beans.factory.annotation.Autowired;

import com.hzw.ssm.date.service.ZJKCalendarService;
import com.hzw.ssm.expert.entity.Appraise;
import com.hzw.ssm.expert.entity.AppraiseInfo;
import com.hzw.ssm.expert.entity.AppraiseRemark;
import com.hzw.ssm.expert.entity.ExpertInfoEntity;
import com.hzw.ssm.expert.entity.SpecialtyInfoEntity;
import com.hzw.ssm.expert.service.AppraiseService;
import com.hzw.ssm.expert.service.ExpertInfoService;
import com.hzw.ssm.fw.base.BaseAction;
import com.hzw.ssm.fw.util.CommUtil;
import com.hzw.ssm.fw.util.DateUtil;
import com.hzw.ssm.fw.util.HZWException;
import com.hzw.ssm.fw.util.MessageConstants;
import com.hzw.ssm.fw.util.SpringApplicationContext;
import com.hzw.ssm.fw.util.SysConstants;
import com.hzw.ssm.fw.util.SysConstants.DATA_SOURCE;
import com.hzw.ssm.sys.call.service.VoiceService;
import com.hzw.ssm.sys.project.entity.ApplyRecordEntity;
import com.hzw.ssm.sys.project.entity.AppointsExpertsEntity;
import com.hzw.ssm.sys.project.entity.ConditionEntity;
import com.hzw.ssm.sys.project.entity.DeleteRecord;
import com.hzw.ssm.sys.project.entity.ExtractDebarbEntity;
import com.hzw.ssm.sys.project.entity.ExtractRecordEntity;
import com.hzw.ssm.sys.project.entity.ProjectAuditEntity;
import com.hzw.ssm.sys.project.entity.ProjectChangesEntity;
import com.hzw.ssm.sys.project.entity.ProjectEntity;
import com.hzw.ssm.sys.project.entity.ResultEntity;
import com.hzw.ssm.sys.project.service.AppointsExpertsService;
import com.hzw.ssm.sys.project.service.DebarbService;
import com.hzw.ssm.sys.project.service.ProjectService;
import com.hzw.ssm.sys.project.service.VoiceProjectService;
import com.hzw.ssm.sys.sms.mwutil.MWSmsUtil;
import com.hzw.ssm.sys.sms.mwutil.RPT;
import com.hzw.ssm.sys.system.entity.SmsRecordEntity;
import com.hzw.ssm.sys.system.service.SmsRecordService;
import com.hzw.ssm.sys.template.entity.TemplateEntity;
import com.hzw.ssm.sys.template.service.TemplateService;
import com.hzw.ssm.sys.user.entity.UserEntity;
import com.hzw.ssm.sys.user.service.UserService;
import com.hzw.ssm.util.empty.EmptyUtils;

/**
 * 语音通知项目
 * 
 * <AUTHOR>
 *
 */
@Namespace("/voiceProject")
@ParentPackage(value = "default")

@Results({ @Result(	name = "initList",
					location = "/jsp/voice/voiceProjectInitList.jsp"),
		@Result(name = "init",
				location = "/jsp/voice/addVoiceProject.jsp"),
		@Result(name = "toProjectList2",
				location = "/jsp/project/projectAuditByDirector.jsp"),
		@Result(name = "apply",
				location = "/jsp/project/applyPointToExpertsProManager.jsp"),
		@Result(name = "removeRecords",
				location = "/jsp/project/removeRecords.jsp"),
		@Result(name = "applyAgain",
				location = "/jsp/project/applyAgainExtract.jsp"),
		@Result(name = "majorStr",
				location = "/jsp/project/majorList.jsp"),
		@Result(name = "selectManager",
				location = "/jsp/project/selectManager.jsp"),

		@Result(name = "projectDetail",
				location = "/jsp/project/showProjectDetail.jsp"),
		@Result(name = "toProjectAppointDetail",
				location = "/jsp/project/projectAppointDetail.jsp"),
		@Result(name = "toProjectAlreadyDetail",
				location = "/jsp/voice/projectDetail.jsp"),
		/*
		 * @Result(name = "toProjectAlreadyDetail", location =
		 * "/jsp/project/projectDetail.jsp"),
		 */
		@Result(name = "selectedRecords",
				location = "/jsp/voice/selectedVoiceRecords.jsp"),
		@Result(name = "toAppraiseDetail",
				location = "/jsp/expert/appraiseDetail.jsp"),
		@Result(name = "pointTo",
				location = "/jsp/project/pointToExperts.jsp"),
		@Result(name = "showProjects",
				location = "/jsp/project/showProjects.jsp"),
		@Result(name = "projectAuditReason",
				location = "/jsp/project/projectAuditReason.jsp"),
		@Result(name = "projectUpdateEx",
				location = "/jsp/voice/updateVoiceMajor.jsp"),
		@Result(name = "debarbCompany",
				location = "/jsp/voice/debarbCompanyList.jsp"),
		@Result(name = "debarbExpertCode",
				location = "/jsp/voice/debarbExpertList.jsp"),
		@Result(name = "projectChange",
				location = "/jsp/voice/projectChange.jsp"),
		@Result(name = "projectCancel",
				location = "/jsp/voice/projectCancel.jsp"),
		@Result(name = "proChangeDetail",
				location = "/jsp/voice/proChangeDetail.jsp") })
public class VoiceProjectAction extends BaseAction {

	private static final long serialVersionUID = -8823858495799309882L;
	private String isSave;// 是否为保存
	private List<ExpertInfoEntity> expertList;// 专家信息列表
	private ProjectEntity project;
	private ProjectEntity projectEntity;
	private ProjectAuditEntity projectAuditEntity;
	private ResultEntity result;
	private ApplyRecordEntity apply;
	private List<ResultEntity> resultList;// 专家抽取结果列表
	private List<List<ResultEntity>> extractList;// 不同轮次抽取结果集
	private ConditionEntity conEntity;
	private ExpertInfoEntity expert;
	private List<ExpertInfoEntity> extractExpertList;// 已抽取用于指定专家页面展示
	private String majorStr;// 专业选择
	private String expertType;         // 以选中的评标专业
	private String message;//
	List<UserEntity> userList;
	private List<UserEntity> operatorList;// 经办人列表
	private List<ProjectEntity> projectList;// 专家抽取结果列表
	private List<ConditionEntity> conditionList;// 专家抽取结果列表
	private List<TemplateEntity> templateEntityList;// 语音模板
	private List<TemplateEntity> templateSmsList;// 语音短信模板
	private List<DeleteRecord> deleteRecordList;
	private ExtractDebarbEntity extractDebarbEntity;

	private List<ExtractDebarbEntity> companyDebarbList;// 回避机构
	private List<ExtractDebarbEntity> extractDebarbList;// 回避专家

	/** 项目变更记录表实体类 */
	private ProjectChangesEntity projectChange;
	/** 项目变更记录表实体类集合 */
	private List<ProjectChangesEntity> projectChangeList;

	private TemplateEntity templateEntity;

	/**
	 * 指定专家
	 */
	private AppointsExpertsEntity appointsEntity;
	private List<AppointsExpertsEntity> appointsList;

	private String extractResultId;

	private ExpertInfoEntity expertInfoEntity;

	/** 评分项 */
	private List<AppraiseInfo> appInfoList;

	private List<Appraise> appraiseList;

	/** 专家评价备注 */
	private AppraiseRemark appraiseRemark;

	private List<ExpertInfoEntity> expertInfoList;

	/** 抽取记录 */
	private List<ExtractRecordEntity> exRecordList;

	/** 本地专家人数by条件 */
	private Integer localExpertCount;
	/** 国家级专家人数by条件 */
	private Integer seniorExpertCount;
	/** 删除记录原因 */
	private String reason;

	private boolean applyFlag = false;

	private String applyDate;

	private boolean isHoliday = false;

	private boolean isLastWorkDay = false;

	private boolean toShow = true;

	private Date bidDate;

	private String currDate2;
	@Autowired
	private ExpertInfoService expertInfoService;

	@Autowired
	private ProjectService projectService;

	@Autowired
	private VoiceProjectService voiceProjectService;

	@Autowired
	private AppointsExpertsService appointsExpertsService;
	@Autowired
	private AppraiseService appraiseService;

	@Autowired
	private UserService userService;

	@Autowired
	private ZJKCalendarService zjkCalendarService;

	@Autowired
	private SmsRecordService smsRecordService;

	@Autowired
	private TemplateService templateService;

	@Autowired
	private VoiceService voiceService;

	@Autowired
	private DebarbService debarbService;

	/**
	 * 初始化查询项目负责人所有等待抽取的项目
	 * 
	 * @return
	 */
	@Action("initList")
	public String initList() {

		if (project == null) {
			project = new ProjectEntity();
			project.setTab("1");
		}
		project.setMethod(SysConstants.CONDITION_METHOD.METHOD_THREE);
		this.context();
		project.setPage(this.getPage());
		UserEntity user = (UserEntity) this.getRequest().getSession().getAttribute("userInfo");
		project.setCreate_id(user.getUser_id());

		// 此处用于区分是哪个tab,区分的结果在SQL中显示
		if (project.getTab().equals("1")) {
			// 等于1:表示待抽取项目且当前时间在开标半小时之前
			/*
			 * 项目状态 1：提交申请待抽取 2:抽取中 20：人数不足 3：已抽取 10:指定专家已抽取 4：指定抽取待审核
			 * 5：提交领导-处长指定抽取审核通过 6：处长指定抽取审核不通过 7;信息保存 8：主任审核指定专家通过 9：主任审核指定专家不通过
			 * 90:主任审核中(时间审核) ,91:主任审核通过(时间审核) ,92:主任审核不通过(时间审核)
			 */
			project.setStatus_("1,7,90,91,92");
		} else if (project.getTab().equals("2")) {
			// 等于2表示抽取中
			project.setStatus_("2,20");
		} else if (project.getTab().equals("3")) {
			// 等于3表示已抽取
			project.setStatus_("3");
		} else if (project.getTab().equals("4")) {
			// 等于4表示应急抽取
			project.setStatus_("1,2,20,7,90,91,92");
		} else if (project.getTab().equals("5")) {
			projectList = voiceProjectService.queryPageProjectChangesList(project);
			return "initList";
		}
		projectList = voiceProjectService.queryVoiceProjectList(project);
		return "initList";
	}

	/**
	 * 跳转到新增页面(语音自动默认“系统语音抽取”方式)
	 * 
	 * @return
	 */
	@Action("init")
	public String init() {
		this.context();
		UserEntity user = null;
		if (null == project) {
			user = (UserEntity) this.getRequest().getSession().getAttribute("userInfo");
			project = new ProjectEntity();
			// 处室
			project.setDepartment(user.getDepartment());
			// 项目负责人
			project.setManager(user.getUser_id());
			project.setManagerName(user.getUser_name());
			// 经办人
			project.setOperator(user.getUser_id());
			project.setOperatorName(user.getUser_name());
		} else {
			user = new UserEntity();
			user.setDepartment(project.getDepartment());
		}
		// 查询项目负责人
		userList = projectService.queryProjectManager(user);
		// 查询项目经办人
		/*
		 * UserEntity user = new UserEntity();
		 * user.setRole_name(SysConstants.ROLE_NAME.OPERATOR); operatorList =
		 * projectService.queryUserByRole(user);
		 */
		// 默认查询“系统自动语音抽取”模板,且有效的模板
		TemplateEntity entity = new TemplateEntity();
		entity.setTemplateType(String.valueOf(SysConstants.CONDITION_METHOD.METHOD_THREE));
		entity.setIsValid(SysConstants.IS_VALID.VALID_ZERO);
		// 非人工通知根据抽取方式查询模板信息
		templateEntityList = templateService.queryTemplateList(entity);

		// 默认查询“语音短信”模板，且有效的模板
		entity.setTemplateType(String.valueOf(SysConstants.CONDITION_METHOD.METHOD_TWO));
		// 非人工通知根据抽取方式查询模板信息
		templateSmsList = templateService.queryTemplateList(entity);
		return "init";
	}

	/**
	 * 提交到主任审核
	 * 
	 * @return
	 */
	@Action("auditByDirector")
	public String auditByDirector() {

		this.context();
		// 当前session
		HttpSession session = getRequest().getSession();
		// 当前用户信息
		UserEntity user = (UserEntity) session.getAttribute("userInfo");
		try {
			boolean applyFlag = false;
			// 2018/6/15将功能修改为流水号执行
			List<ProjectEntity> pList = projectService.queryProjectListById(project);
			String reason = java.net.URLDecoder.decode(project.getApplyReason(), "utf-8");
			for (ProjectEntity entity : pList) {
				int count = projectService.saveAuditByDirector(user, entity, reason);
				applyFlag = count > 0 ? true : false;
			}
			// 跳转到专家抽取申请页面
			this.getResponse()
					.sendRedirect(this.getRequest().getContextPath() + "/voiceProject/initList?applyFlag=" + applyFlag);
		} catch (IOException e) {
			e.printStackTrace();
		}
		return null;
	}

	/**
	 * 检查当前是否为指定开标日期的前一个工作日或者是开标当天
	 * 
	 * @return
	 */
	@Action("valApplyDate2")
	public String valApplyDate2() {
		this.context();
		String resStatus = "success";
		PrintWriter out = null;
		Date bidTime = null;
		try {
			out = this.getResponse().getWriter();

			List<ProjectEntity> pList = projectService.queryProjectListById(project);
			if (pList != null && !pList.isEmpty() && pList.size() > 0) {
				bidTime = pList.get(0).getBidTime();
			} else {
				throw new Exception("查询开标时间失败");
			}
			boolean flag = true;

			// 获取当前时间
			Calendar cal = Calendar.getInstance();
			// 在当前时间上减去2小时
			cal.add(Calendar.HOUR, -2);

			// 判断是否已经超过2个小时了
			if (bidTime.before(cal.getTime())) {
				flag = false;
				resStatus = "timeOut";
			}
			Calendar calendar = Calendar.getInstance();
			if (flag) {
				// 不在工作时间段
				int hour = calendar.get(Calendar.HOUR_OF_DAY);
				if (hour < 8 || hour > 18) {
					flag = false;
					resStatus = "notWorkTime";
				}
			}

			if (flag) {
				SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
				String bidTimeStr = sdf.format(bidTime);
				String nowStr = sdf.format(calendar.getTime());
				// 判断当天是否是节假日
				boolean dateStatus = zjkCalendarService.isHoliday(nowStr);
				// 判断开标时间是否是节假日
				boolean dateStatus2 = zjkCalendarService.isHoliday(bidTimeStr);
				if (dateStatus || dateStatus2) {
					flag = false;
					resStatus = "notWorkDay";
				} else {
					// 判断当前时间是否为节假日
					isHoliday = zjkCalendarService.isLastWorkDay(nowStr, bidTimeStr);
					if (!(nowStr.equals(bidTimeStr) || isHoliday)) {
						flag = false;
						resStatus = "notWorkDay";
					}
				}

			}

			// 判断当前的项目是不是系统自动抽取
			if (pList.get(0).getMethod().equals(SysConstants.CONDITION_METHOD.METHOD_THREE)) {

				// 判断当前项目是否有抽取记录
				ConditionEntity conditionEntity = new ConditionEntity();
				conditionEntity.setDecimationBatch(project.getDecimationBatch());
				List<ExpertInfoEntity> expertList = projectService.queryProjectExpertInfo(conditionEntity);
				// 判断开标时间是否在半个小时之前
				if ("timeOut".equals(resStatus)) {
					// 判断当前项目是否已经有审核通过的记录
					String auditType = SysConstants.AUDIT_TYPE.TYPE_2;
					ProjectAuditEntity projectAuditEntity = queryProAuditInfo(auditType);
					// 如果等于空，表示当前的项目（指定的审核状态下）未经被审批过
					if (projectAuditEntity == null) {
						// 已经过了开标时间需要主任审批。需判断是否存在抽取记录：如果无抽取记录，则直接转为人工
						resStatus = "auditAndConvert";
					} else {
						resStatus = "success";
					}
				} else {
					// 判断是否是正常工作日及工作时间
					if (flag) {
						// 是正常工作日及工作时间
						// 判断当前时间是否在开标前半个小时之内（未过开标时间）
						Calendar now = Calendar.getInstance();
						long afterTimeValue = now.getTimeInMillis() + 30 * 60 * 1000;
						Date afterTime = new Date(afterTimeValue);
						if (bidTime.after(cal.getTime()) && bidTime.before(afterTime)) {
							if (expertList != null && expertList.size() > 0) {
								// 直接继续应急抽取
								resStatus = "exigence";
							} else {
								// 直接转人工
								resStatus = "artificial";
							}
						}

					} else {
						// 非正常工作日或者非工作时间
						String auditType = SysConstants.AUDIT_TYPE.TYPE_1;
						// 判断当前项目是否已经有审核通过的记录
						ProjectAuditEntity projectAuditEntity = queryProAuditInfo(auditType);
						Calendar now = Calendar.getInstance();
						long afterTimeValue = now.getTimeInMillis() + 30 * 60 * 1000;
						Date afterTime = new Date(afterTimeValue);
						// 判断是否有抽取记录
						if (expertList != null && expertList.size() > 0) {
							// 判断当前时间是否在开标前半个小时之内（未过开标时间）
							if (bidTime.after(cal.getTime()) && bidTime.before(afterTime)) {
								// 直接继续应急抽取
								resStatus = "exigence";
							} else {
								if (projectAuditEntity != null) {
									// 有开标记录，但是不在非工作时间，且在半个小时前（结果：继续抽取）
									resStatus = "success";
								}
								// 否则无审核记录，有开标记录，不在非工作时间内，审核之后刘红抽取
							}
						} else {
							// 非工作时间、无开标记录、时间在开标后2小时之前
							// 判断当前项目是否已经有审核通过的记录
							if (projectAuditEntity != null) {
								// 直接继续应急抽取
								resStatus = "exigence";
							}
						}
					}
				}
			}
		} catch (Exception e) {
			resStatus = "failed";
			e.printStackTrace();
		} finally {
			out.print(resStatus);
			out.close();
		}

		return "initList";
	}

	private ProjectAuditEntity queryProAuditInfo(String auditType) {
		ProjectAuditEntity projectAuditEntity = new ProjectAuditEntity();
		projectAuditEntity.setDecimationBatch(project.getDecimationBatch());
		projectAuditEntity.setAuditType(project.getAuditType());
		projectAuditEntity.setStatus(SysConstants.AUDIT_STATUS.STATUS_1);
		projectAuditEntity.setAuditType(auditType);
		ProjectAuditEntity auditEntity = projectService.getProAuditInfoByProIdToVoice(projectAuditEntity);

		return auditEntity;
	}

	/**
	 * 查询项目负责人
	 * 
	 * @return
	 */
	@Action("projectManagerList")
	public String projectManagerList() {
		this.context();
		this.getResponse().setContentType("text/html;charset=UTF-8");
		StringBuffer result = new StringBuffer("[");
		UserEntity user = new UserEntity();
		user.setDepartment(project.getDepartment());
		userList = projectService.queryProjectManager(user);
		for (int i = 0; i < userList.size(); i++) {
			user = userList.get(i);
			if (0 < i) {
				result.append(",");
			}
			result.append("{\"user_id\":\"").append(user.getUser_id()).append("\", \"user_name\":\"")
					.append(user.getUser_name()).append("\"}");
		}
		result.append("]");
		try {
			this.getResponse().getWriter().write(result.toString());
		} catch (IOException e) {
			e.printStackTrace();
		}
		return null;
	}

	/**
	 * 查询项目明细
	 * 
	 * @return
	 */
	@Action("projectDetail")
	public String toProjectDetail() {
		Long method = project.getMethod();
		String returnStr = "projectDetail";       // -2 标注为项目详细信息
		// project = projectService.queryProjectById(project);
		projectList = projectService.queryProjectListById(project);
		project = projectList.get(0);
		projectEntity = project;
		if (null != method && -1L == method) {
			// -1L 标注为跳转到申请指定专家页面
			// 跳转之前先查询当前项目指定专家库里状态
			AppointsExpertsEntity entity = new AppointsExpertsEntity();
			entity.setDecimationBatch(project.getDecimationBatch());
			appointsList = appointsExpertsService.queryDateByDecimationBatch(entity);
			return "apply";
		}

		if (null != method && -3L == method) {
			// -3L 标注为跳转到删除记录页面
			return "removeRecords";
		}

		if (null == conEntity) {
			conEntity = new ConditionEntity();
		}
		conEntity.setProjectId(project.getProjectId());
		conEntity = projectService.queryConditionById(conEntity);

		// 空 标注为项目指定专家详细信息页面
		if (null == method || 4L == method) {
			ConditionEntity conditionEntity = new ConditionEntity();
			conditionEntity.setProjectId(project.getProjectId());
			conditionEntity.setJoin_status(0L);     // 参标5AM0128
			expertInfoList = projectService.queryExtractedResult(conditionEntity);
			return "toProjectAppointDetail";
		}
		// 查询参评专家信息
		if (null != method && 4L != method && -2L != method) {
			ConditionEntity conditionEntity = new ConditionEntity();
			conditionEntity.setProjectId(project.getProjectId());
			conditionEntity.setJoin_status(0L);     // 参标
			// 判断当前时间是否是半个小时以内
			Calendar calendar = Calendar.getInstance();
			long currentTime = calendar.getTimeInMillis() + 30 * 60 * 1000;
			Date date = new Date(currentTime);
			// 判断当前时间是否在开标前半个小时
			if (projectEntity.getBidTime().after(date)) {
				toShow = false;
			}
			expertInfoList = projectService.queryExtractedResult(conditionEntity);
			exRecordList = projectService.queryExtractRecordList(project.getProjectId());
			returnStr = "toProjectAlreadyDetail";
		}

		return returnStr;

	}

	/**
	 * 查询项目明细，确认抽取结果
	 * 
	 * @return
	 */
	@Action("selectedRecords")
	public String selectedRecords() {
		Long method = project.getMethod();
		projectList = projectService.queryProjectListById(project);
		project = projectList.get(0);
		if (null == conEntity) {
			conEntity = new ConditionEntity();
		}
		conEntity.setProjectId(project.getProjectId());
		conEntity = projectService.queryConditionById(conEntity);
		projectEntity = project;

		ConditionEntity conditionEntity = new ConditionEntity();
		conditionEntity.setProjectId(project.getProjectId());
		conditionEntity.setJoin_status(0L);     // 参标
		expertInfoList = projectService.queryExtractedResult(conditionEntity);
		// 判断当前项目是否是半个小时以内
		Calendar calendar = Calendar.getInstance();
		long currentTime = calendar.getTimeInMillis() + 30 * 60 * 1000;
		Date date = new Date(currentTime);
		// 判断当前时间是否在开标前半个小时
		if (projectEntity.getBidTime().after(date)) {
			toShow = false;
			exRecordList = projectService.copyQueryExtractRecordList(project.getProjectId());
			/*
			 * if(exRecordList == null || exRecordList.isEmpty()){
			 * localExpertCount =
			 * projectService.queryExpertCountByRule(conEntity, 1);
			 * seniorExpertCount =
			 * projectService.queryExpertCountByRule(conEntity, 2); }
			 */
		} else {
			exRecordList = projectService.copyQueryExtractRecordList(project.getProjectId());
			if (exRecordList == null || exRecordList.isEmpty()) {
				localExpertCount = projectService.queryExpertCountByRule(conEntity, 1);
				seniorExpertCount = projectService.queryExpertCountByRule(conEntity, 2);
			}
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			if ("2".equals(conEntity.getMethod().toString())) {// 短信抽取
				// conEntity.setSmsStr("您好：XXX，江苏省招标中心有限公司诚邀您参加【"+project.getProjectName()+"】评标，开标时间："+sdf.format(project.getBidTime())+"。回复：1-参加，2-不参加。");
				conEntity.setSmsStr("您好：XXX，江苏省招标中心有限公司诚邀您参加评标，开标时间：" + sdf.format(project.getBidTime()) + "，开标地点："
						+ project.getBidAddress() + "，项目负责人：" + project.getManager() + "，电话：" + project.getPhone()
						+ "。回复：1-参加，2-不参加。");
			} else {// 正常抽取
				String projectName = "";
				for (ProjectEntity p : projectList) {
					projectName += p.getProjectName() + ",";
				}
				projectName = projectName.substring(0, projectName.length() - 2);
				// conEntity.setSmsStr("您好：XXX，江苏省招标中心有限公司诚邀您参加【"+project.getProjectName()+"】评标，开标时间："+sdf.format(project.getBidTime())+",开标地点："+project.getBidAddress()+",项目负责人："+project.getManager()+",电话："+project.getPhone()+"");
				conEntity.setSmsStr("您好：XXX，江苏省招标中心有限公司诚邀您参加评标，抽取批次:" + project.getDecimationBatch() + "(" + projectName
						+ ")" + ",开标时间：" + sdf.format(project.getBidTime()) + "，开标地点：" + project.getBidAddress()
						+ "，项目负责人：" + project.getManager() + "，电话：" + project.getPhone());
			}
		}

		return "selectedRecords";

	}

	/**
	 * 根据抽取条件验证是否存在满足条件的专家
	 * 
	 * @return
	 */
	@Action("valExtractionExport")
	public String valExtractionExport() {
		this.context();
		this.getResponse().setCharacterEncoding("utf-8");
		UserEntity user = (UserEntity) this.getRequest().getSession().getAttribute("userInfo");

		PrintWriter out = null;
		boolean flage = false;
		List<ProjectEntity> pList = new ArrayList<ProjectEntity>();
		String msg = null;
		try {
			if (null != project.getDecimationBatch() && !project.getDecimationBatch().isEmpty()) {
				pList = projectService.queryProjectListById(project);
			}

			// 保存项目中的委托单位
			String tender = "";
			// 判断当前操作是否是修改
			if (conEntity != null && "update".equals(conEntity.getOptType())) {
				out = this.getResponse().getWriter();
				if (null != project.getTender() && !"".equals(project.getTender())
						&& project.getTender().indexOf(",") > -1) {
					String[] tenderSplit = project.getTender().split(",");
					for (int i = 0; i < tenderSplit.length; i++) {
						tender += "'" + tenderSplit[i] + "',";
					}
					tender = tender.substring(0, tender.length() - 1);
				} else {
					tender = "'" + project.getTender() + "'";
				}
				conEntity.setTender(tender);
				conEntity.setBidTimeDay(DateUtil.dateToString(project.getBidTime()));

				// 获取处理过的回避信息
				if (companyDebarbList == null) {
					companyDebarbList = new ArrayList<ExtractDebarbEntity>();
				}
				conEntity.setCompanyDebarbList(companyDebarbList);

				if (extractDebarbList == null) {
					extractDebarbList = new ArrayList<ExtractDebarbEntity>();
				}
				conEntity.setExtractDebarbList(extractDebarbList);

				msg = projectService.queryExpertToCheck(conEntity,pList);
			} else {
				if (pList.size() > 0) {
					conEntity = new ConditionEntity();
					for (ProjectEntity entity : pList) {
						conEntity.setId(entity.getConditionId());
						// 查询抽取条件
						conEntity = projectService.queryConditionById(conEntity);
						out = this.getResponse().getWriter();
						if (null != entity.getTender() && !"".equals(entity.getTender())
								&& entity.getTender().indexOf(",") > -1) {
							String[] tenderSplit = entity.getTender().split(",");
							for (int i = 0; i < tenderSplit.length; i++) {
								tender += "'" + tenderSplit[i] + "',";
							}
							tender = tender.substring(0, tender.length() - 1);
						} else {
							tender = "'" + entity.getTender() + "'";
						}
						conEntity.setBidTimeDay(DateUtil.dateToString(entity.getBidTime()));
					}
					conEntity.setTender(tender);

					// 新增查询回避条件
					ExtractDebarbEntity debarbEntity = new ExtractDebarbEntity();
					debarbEntity.setDecimationbatch(pList.get(0).getDecimationBatch());
					companyDebarbList = new ArrayList<ExtractDebarbEntity>();
					extractDebarbList = new ArrayList<ExtractDebarbEntity>();
					List<ExtractDebarbEntity> debarbList = debarbService.queryExtractDebarbList(debarbEntity);
					if (debarbList != null && debarbList.size() > 0) {
						for (ExtractDebarbEntity debarb : debarbList) {
							if (SysConstants.DEBARB_TYPE.TYPE_1.equals(debarb.getDebarbType())) {
								companyDebarbList.add(debarb);
							} else {
								extractDebarbList.add(debarb);

							}
						}
					}
					conEntity.setCompanyDebarbList(companyDebarbList);
					conEntity.setExtractDebarbList(extractDebarbList);
					msg = projectService.queryExpertToCheck(conEntity,pList);
				} else {
					out = this.getResponse().getWriter();
					if (null != project.getTender() && !"".equals(project.getTender())
							&& project.getTender().indexOf(",") > -1) {
						String[] tenderSplit = project.getTender().split(",");
						for (int i = 0; i < tenderSplit.length; i++) {
							tender += "'" + tenderSplit[i] + "',";
						}
						tender = tender.substring(0, tender.length() - 1);
					} else {
						tender = "'" + project.getTender() + "'";
					}
					conEntity.setBidTimeDay(DateUtil.dateToString(project.getBidTime()));
					conEntity.setTender(tender);
					
					//获取处理过的回避信息
					if(companyDebarbList==null){
						companyDebarbList = new ArrayList<ExtractDebarbEntity>();
					}
					conEntity.setCompanyDebarbList(companyDebarbList);
					
					if(extractDebarbList==null){
						extractDebarbList = new ArrayList<ExtractDebarbEntity>();
					}
					conEntity.setExtractDebarbList(extractDebarbList);
					
					
					msg = projectService.queryExpertToCheck(conEntity,pList);

				}
			}
			if (!"success".equals(msg)) {
				// 判断当前用户是否是中心抽取人(当存在某个项目抽取未确认，而库里专家人数不足时，中心抽取人抽取需要提示哪个项目没有确认)
				if (user.getRole_id().equals(SysConstants.ROLE_ID.EXTRACT)) {
					List<ConditionEntity> conList = projectService.queryDecimationbatchByBidTime(conEntity);
					String tig = "，还有部分项目业务员未确认，抽取批次为：";
					if (conList.size() > 0) {
						for (ConditionEntity con : conList) {
							tig += con.getDecimationBatch() + ",";
						}
						tig = tig.substring(0, tig.length() - 1);
						msg = msg + tig;
					}
				}
			}

			out.print(msg);

			out.close();
		} catch (Exception e) {
			if (e instanceof HZWException) {
				out.print(e.getMessage());
			} else
				e.printStackTrace();
		}
		return null;
	}

	/**
	 * 删除记录
	 * 
	 * @return
	 */
	@SuppressWarnings("null")
	@Action("deleteRecord")
	public String deleteRecord() {
		this.context();
		this.getResponse().setCharacterEncoding("utf-8");
		PrintWriter out = null;
		Integer codeNum = null;
		try {
			this.getResponse().getWriter();
			DeleteRecord deleteRecord = new DeleteRecord();
			deleteRecord.setReason(reason);
			projectService.deleteRecordByProjectId(project, deleteRecord);
			out = this.getResponse().getWriter();
			codeNum = 0;
			out.print(codeNum);
			out.close();
		} catch (Exception e) {
			e.printStackTrace();
		}

		return null;
	}

	/**
	 * 保存项目信息
	 * 
	 * @return
	 */
	@Action("saveProject")
	public String saveProject() {
		try {
			// 声明要新增的项目List
			List<ProjectEntity> proList = new ArrayList<ProjectEntity>();
			// 1、设置条件开始查询
			HttpServletRequest request = ServletActionContext.getRequest();
			// 招标项目编号
			String projectNum = request.getParameter("tenderProjectNum");
			// 招标项目名称
			String projectName = request.getParameter("tenderProjectName");
			String tenders = request.getParameter("tenders");
			//
			String[] tenderProjectNum = projectNum.split(",");
			String[] tenderProjectName = projectName.split(",");
			String[] tender = tenders.split(",");
			for (int i = 0; i < tenderProjectNum.length; i++) {
				ProjectEntity newEntity = new ProjectEntity();
				SpringApplicationContext.copyProperties(project, newEntity); // 新对象赋值
				newEntity.setProjectNo(tenderProjectNum[i]);
				// String str=new
				// String(tenderProjectName[i].getBytes("ISO-8859-1"),"UTF-8");
				newEntity.setProjectName(tenderProjectName[i]);
				newEntity.setTender(tender[i]);
				this.setOpaUserAndDate(newEntity);
				proList.add(newEntity);
			}

			List<ExtractDebarbEntity> extractDebarbEntityList = disposeDebarbData();

			conEntity.setSourceCode(DATA_SOURCE.ZJK);
			projectService.saveVoiceProjects(proList, conEntity, extractDebarbEntityList);

			this.setAlertMessage(MessageConstants.SAVE_SUCCESS);

			// TODO 发送短信内容待修改部分
			// 申请抽取之后向抽取人发送通知短信
			String saveProjectNo = "";
			for (ProjectEntity entity : proList) {
				saveProjectNo += entity.getProjectNo() + ",";
			}
			if (saveProjectNo == "") {
				throw new HZWException("数据有误！请联系管理员！");
			} else {
				saveProjectNo.substring(0, saveProjectNo.length() - 1);

			}
			// 申请抽取之后向抽取人发送通知短信
			if (1 == project.getStatus().intValue()) {
				// TODO 给抽取人发短信功能展示开放
				// projectService.smsExtractMobile(proList,saveProjectNo);
			}

			/*
			 * if (1 == project.getStatus().intValue()) {
			 * this.setOpaUserAndDate(project);
			 * projectService.smsExtractMobile(project); }
			 */
		} catch (Exception e) {
			e.printStackTrace();
			this.setAlertMessage(MessageConstants.SAVE_FAILED);
			return init();
		}
		project = null;
		return initList();
	}

	/**
	 * 处理回避数据
	 * 
	 * @return
	 */
	private List<ExtractDebarbEntity> disposeDebarbData() {
		// 获取回避信息
		List<ExtractDebarbEntity> extractDebarbEntityList = new ArrayList<ExtractDebarbEntity>();

		if (companyDebarbList != null) {
			for (int i = 0; i < companyDebarbList.size(); i++) {
				if(companyDebarbList.get(i)!=null){
					ExtractDebarbEntity newEntity = new ExtractDebarbEntity();
					newEntity.setCompany(companyDebarbList.get(i).getCompany());
					newEntity.setDebarbReason(companyDebarbList.get(i).getCompanyDebarbReason());
					newEntity.setDebarbType(SysConstants.DEBARB_TYPE.TYPE_1);
					this.setOpaUserAndDate(newEntity);
					extractDebarbEntityList.add(newEntity);
				}
				
			}
		}

		if (extractDebarbList != null) {
			for (int i = 0; i < extractDebarbList.size(); i++) {
				if(extractDebarbList.get(i)!=null){
					ExtractDebarbEntity newEntity = new ExtractDebarbEntity();
					newEntity.setExpertCode(extractDebarbList.get(i).getExpertCode());
					newEntity.setExpertPhone(extractDebarbList.get(i).getExpertPhone());
					newEntity.setDebarbReason(extractDebarbList.get(i).getExpertDebarbReason());
					newEntity.setDebarbType(SysConstants.DEBARB_TYPE.TYPE_2);
					this.setOpaUserAndDate(newEntity);
					extractDebarbEntityList.add(newEntity);
				}
			}
		}
		return extractDebarbEntityList;
	}

	/**
	 * 展示修改项目信息页面
	 * 
	 * @return
	 */
	@Action("showModifyProject")
	public String showModifyProject() {
		projectList = projectService.queryProjectListById(project);
		if (projectList != null && projectList.size() > 0) {
			project = projectList.get(0);
			projectList.remove(0);
			if (null == conEntity) {
				conEntity = new ConditionEntity();
			}
			conEntity.setProjectId(project.getProjectId());
			if (null != conEntity.getId() && !conEntity.getId().isEmpty()) {
				conEntity = projectService.queryConditionById(conEntity);
			}

			// 判断当前项目是否是非人工通知
			if (conEntity.getMethod() != '1') {

				// 默认查询“系统自动语音抽取”模板,且有效的模板
				TemplateEntity entity = new TemplateEntity();
				entity.setTemplateType(String.valueOf(SysConstants.CONDITION_METHOD.METHOD_THREE));
				entity.setIsValid(SysConstants.IS_VALID.VALID_ZERO);
				// 非人工通知根据抽取方式查询模板信息
				templateEntityList = templateService.queryTemplateList(entity);

				// 默认查询“语音短信”模板，且有效的模板
				entity.setTemplateType(String.valueOf(SysConstants.CONDITION_METHOD.METHOD_TWO));
				// 非人工通知根据抽取方式查询模板信息
				templateSmsList = templateService.queryTemplateList(entity);
			}
			companyDebarbList = new ArrayList<ExtractDebarbEntity>();
			extractDebarbList = new ArrayList<ExtractDebarbEntity>();
			// 根据批次号查询回避信息
			ExtractDebarbEntity debarbEntity = new ExtractDebarbEntity();
			debarbEntity.setDecimationbatch(project.getDecimationBatch());
			List<ExtractDebarbEntity> debarbList = debarbService.queryExtractDebarbList(debarbEntity);
			if (debarbList != null && debarbList.size() > 0) {
				for (ExtractDebarbEntity debarb : debarbList) {
					if (SysConstants.DEBARB_TYPE.TYPE_1.equals(debarb.getDebarbType())) {
						debarb.setCompanyDebarbReason(debarb.getDebarbReason());
						companyDebarbList.add(debarb);
					} else {
						debarb.setExpertDebarbReason(debarb.getDebarbReason());
						extractDebarbList.add(debarb);

					}
				}
			}
			isSave = "1";// 更新操作
			return this.init();
		} else {
			return this.initList();
		}
	}

	/**
	 * 更新项目信息
	 * 
	 * @return
	 */
	@Action("updateProject")
	public String updateProject() {
		try {

			// 1、设置条件开始查询
			HttpServletRequest request = ServletActionContext.getRequest();
			// 招标项目编号
			String tenderProjectIds = request.getParameter("tenderProjectIds");
			// 招标项目名称
			String projectName = request.getParameter("tenderProjectName");
			// 获取抽取批次
			String decimationBatch = request.getParameter("decimationBatch");
			String tenders = request.getParameter("tenders");
			// 循环修改项目信息
			String[] tenderProjectId = tenderProjectIds.split(",");
			String[] tenderProjectName = projectName.split(",");
			String[] tender = tenders.split(",");
			List<ProjectEntity> proList = new ArrayList<ProjectEntity>();
			for (int i = 0; i < tenderProjectId.length; i++) {
				// String str=new
				// String(tenderProjectName[i].getBytes("ISO-8859-1"),"UTF-8");
				ProjectEntity newEntity = new ProjectEntity();
				SpringApplicationContext.copyProperties(project, newEntity); // 新对象赋值
				newEntity.setProjectId(tenderProjectId[i]);
				newEntity.setProjectName(tenderProjectName[i]);
				newEntity.setTender(tender[i]);
				this.setOpaUserAndDate(newEntity);
				projectService.updateProject(newEntity);
				proList.add(newEntity);
				this.setAlertMessage(
						1 == newEntity.getStatus() ? MessageConstants.APPLY_SUCCESS : MessageConstants.SAVE_SUCCESS);
				/*
				 * // 申请抽取之后向抽取人发送通知短信 if (1 ==
				 * newEntity.getStatus().intValue()) {
				 * projectService.smsExtractMobile(newEntity); }
				 */
			}

			// TODO 发送短信内容待修改部分
			// 申请抽取之后向抽取人发送通知短信
			String saveProjectName = "";
			for (ProjectEntity entity : proList) {
				saveProjectName += entity.getProjectNo() + ",";
			}
			if (saveProjectName == "") {
				throw new HZWException("数据有误！请联系管理员！");
			} else {
				saveProjectName.substring(0, saveProjectName.length() - 1);

			}
			// 申请抽取之后向抽取人发送通知短信
			if (1 == project.getStatus().intValue()) {
				// TODO 给抽取人发短信功能展示开放
				// projectService.smsExtractMobile(proList,saveProjectName);
			}
			List<ExtractDebarbEntity> extractDebarbEntityList = disposeDebarbData();
			// 将原有的回避信息删除
			ExtractDebarbEntity extractDebarbEntity = new ExtractDebarbEntity();
			extractDebarbEntity.setDecimationbatch(decimationBatch);
			debarbService.deleteDebarbData(extractDebarbEntity);

			// 添加新的回避信息
			if (extractDebarbEntityList != null && extractDebarbEntityList.size() > 0) {
				// 保存回避条件
				for (ExtractDebarbEntity debarbEntity : extractDebarbEntityList) {
					debarbEntity.setDecimationbatch(decimationBatch);
					debarbEntity.setId(CommUtil.getKey());
					debarbService.saveDebarbData(debarbEntity);
				}
			}

			// 循环修改条件信息
			project.setDecimationBatch(decimationBatch);
			List<ProjectEntity> list = projectService.queryConditionIdByBatch(project);
			for (ProjectEntity entity : list) {
				conEntity.setId(entity.getConditionId());
				this.setOpaUserAndDate(conEntity);
				projectService.updateCondition(conEntity);
			}
			isSave = "1";
		} catch (Exception e) {
			e.printStackTrace();
			this.setAlertMessage(
					1 == project.getStatus() ? MessageConstants.APPLY_FAILED : MessageConstants.SAVE_FAILED);
		}
		project = null;
		return initList();
	}

	/**
	 * 更新项目信息（语音抽取时，人数不足修改完之后直接抽取）
	 * 
	 * @return
	 */
	@Action("updateProjectMajor")
	public String updateProjectMajor() {
		try {
			// 1、设置条件开始查询
			HttpServletRequest request = ServletActionContext.getRequest();
			// 招标项目编号
			String tenderProjectIds = request.getParameter("tenderProjectIds");
			// 招标项目名称
			String projectName = request.getParameter("tenderProjectName");
			// 获取抽取批次
			String decimationBatch = request.getParameter("decimationBatch");
			String tenders = request.getParameter("tenders");
			// 循环修改项目信息
			String[] tenderProjectId = tenderProjectIds.split(",");
			String[] tenderProjectName = projectName.split(",");
			String[] tender = tenders.split(",");
			List<ProjectEntity> proList = new ArrayList<ProjectEntity>();

			for (int i = 0; i < tenderProjectId.length; i++) {
				// String str=new
				// String(tenderProjectName[i].getBytes("ISO-8859-1"),"UTF-8");
				ProjectEntity newEntity = new ProjectEntity();
				SpringApplicationContext.copyProperties(project, newEntity); // 新对象赋值
				newEntity.setProjectId(tenderProjectId[i]);
				newEntity.setProjectName(tenderProjectName[i]);
				newEntity.setTender(tender[i]);
				this.setOpaUserAndDate(newEntity);
				projectService.updateProject(newEntity);
				proList.add(newEntity);
				this.setAlertMessage(
						1 == newEntity.getStatus() ? MessageConstants.APPLY_SUCCESS : MessageConstants.SAVE_SUCCESS);
				/*
				 * // 申请抽取之后向抽取人发送通知短信 if (1 ==
				 * newEntity.getStatus().intValue()) {
				 * projectService.smsExtractMobile(newEntity); }
				 */
			}
			
			// 循环修改条件信息
			project.setDecimationBatch(decimationBatch);
			List<ProjectEntity> list = projectService.queryConditionIdByBatch(project);
			for (ProjectEntity entity : list) {
				conEntity.setId(entity.getConditionId());
				this.setOpaUserAndDate(conEntity);
				projectService.updateCondition(conEntity);
			}
			
			// 查询是否已经过了开标时间
			ProjectEntity newEntity = new ProjectEntity();
			newEntity.setDecimationBatch(decimationBatch);
			List<ProjectEntity> pList = projectService.queryProjectListById(newEntity);
			if (pList != null && !pList.isEmpty() && pList.size() > 0) {

				// 根据流水号查询项目信息
				ProjectEntity pEntity = new ProjectEntity();
				pEntity.setDecimationBatch(decimationBatch);
				// 根据流水号查询抽取记录
				List<ConditionEntity> conditionList = projectService.queryConditionListByBatch(pEntity);

				projectService.checkBidTime(pList, conditionList.get(0));
				
				// 当重新申请的专家需要主任审核时，重新抽取
				if (project.getStatus() != 90) {
					voiceService.getExpert(pEntity, conditionList.get(0));
				}

			} else {
				throw new Exception("查询开标时间失败");
			}

			// 申请抽取之后向抽取人发送通知短信
			String saveProjectName = "";
			for (ProjectEntity entity : proList) {
				saveProjectName += entity.getProjectNo() + ",";
			}
			if (saveProjectName == "") {
				throw new HZWException("数据有误！请联系管理员！");
			} else {
				saveProjectName.substring(0, saveProjectName.length() - 1);
			}
			// 申请抽取之后向抽取人发送通知短信
			if (1 == project.getStatus().intValue()) {
				// TODO 给抽取人发短信功能展示开放
				// projectService.smsExtractMobile(proList,saveProjectName);
			}
			

			isSave = "1";
		} catch (Exception e) {
			e.printStackTrace();
			this.setAlertMessage(
					1 == project.getStatus() ? MessageConstants.APPLY_FAILED : MessageConstants.SAVE_FAILED);
		}
		project = null;
		return initList();
	}

	/**
	 * 直接抽取专家 （只适合在应急抽取页面的提交按钮使用）
	 * 
	 * @return
	 */
	@Action("queryContinueExtract")
	public String queryContinueExtract() {
		try {
			// 查询项目信息
			if (project.getDecimationBatch() != null && project.getDecimationBatch() != "") {
				// 根据批次号查询项目信息
				projectList = projectService.queryProjectListById(project);
				if (projectList != null && projectList.size() > 0) {
					ConditionEntity entity = new ConditionEntity();
					entity.setProjectId(projectList.get(0).getProjectId());
					List<ConditionEntity> conditionList = projectService.queryConditionList(entity);

					String strType = projectService.checkBidTime(projectList, conditionList.get(0));

					/*
					 * Calendar end = Calendar.getInstance(); long currentTime =
					 * end.getTimeInMillis()+30*60*1000; Date date = new
					 * Date(currentTime); // 已经超过开标时间 if
					 * (projectList.get(0).getBidTime().before(date)) {
					 */
					// 直接抽取专家
					voiceService.getExpert(projectList.get(0), conditionList.get(0));
					/* } */
				} else {
					throw new Exception("查询项目列表失败");
				}
			} else {
				throw new Exception("无批次号，请查证后提交！");
			}

		} catch (Exception e) {
			e.printStackTrace();
		}
		return this.selectedRecords();
	}

	/**
	 * 修改抽取条件中的抽取方式并且留痕
	 */
	@Action("updateConditionCurrentStatus")
	public String updateConditionCurrentStatus() {
		this.context();
		String resStatus = "success";
		PrintWriter out = null;
		try {
			out = this.getResponse().getWriter();
			if (project.getDecimationBatch() == null || project.getDecimationBatch() == "") {
				throw new HZWException("数据有误！请联系管理员！");
			}
			List<ProjectEntity> pList = projectService.queryProjectListById(project);
			// 判断当前项目是否是语音通知
			if (pList != null && !pList.isEmpty() && pList.size() > 0) {
				// 判断当前的项目是不是系统自动抽取
				if (pList.get(0).getMethod().longValue() == (SysConstants.CONDITION_METHOD.METHOD_THREE)) {
					Calendar end = Calendar.getInstance();
					long currentTime = end.getTimeInMillis() + 30 * 60 * 1000;
					Date date = new Date(currentTime);

					Calendar startTime = Calendar.getInstance();
					startTime.add(startTime.HOUR, -2);
					// 根据查询条件ID查询专家未参加的
					ConditionEntity conditionEntity = new ConditionEntity();
					conditionEntity.setDecimationBatch(project.getDecimationBatch());
					// 获取当前流水号下的抽取信息
					List<ExpertInfoEntity> expertList = projectService.queryProjectExpertInfo(conditionEntity);
					// 判断当前时间是否在开标前半个小时(在开标半个小时内将项目转成人工抽取)
					if (pList.get(0).getBidTime().after(startTime.getTime())
							&& pList.get(0).getBidTime().before(date)) {
						for (ProjectEntity pEntity : pList) {
							// 将抽取记录表中留痕
							ConditionEntity conEntity = new ConditionEntity();

							// 判断当前的批次号是否有抽取专家记录
							if (expertList != null && expertList.size() > 0) {
								// 存在抽取记录表示，当前的项目是人数不足
								// 将语音的状态留痕
								conEntity.setCurrentStatus(SysConstants.CONDITION_CURRENT_STATUS.METHOD_TWO);
							} else {
								// 将项目抽取方式改成人工抽取
								conEntity.setMethod(SysConstants.CONDITION_METHOD.METHOD_ONE);
								// 将语音的状态留痕
								conEntity.setCurrentStatus(SysConstants.CONDITION_CURRENT_STATUS.METHOD_TWO);
							}
							conEntity.setId(pEntity.getConditionId());
							projectService.updateConditionCurrentStatus(conEntity);
						}
					}
				}
			} else {
				throw new HZWException("数据有误！请联系管理员！");
			}
			out.print(resStatus);
			out.close();
		} catch (Exception e) {
			if (e instanceof HZWException) {
				out.print(e.getMessage());
			} else
				e.printStackTrace();
		}
		return null;
	}

	/**
	 * 更新项目状态
	 * 
	 * @return
	 */
	@Action("updateProjectStatus")
	public String updateProjectStatus() {
		try {
			if (project.getDecimationBatch() == null || project.getDecimationBatch() == "") {
				throw new HZWException("数据有误！请联系管理员！");
			}
			// 2018/6/15将功能修改为流水号执行
			// 记录当前批次号下的所有项目名称，用逗号隔开
			String saveProjectNo = "";
			List<ProjectEntity> pList = projectService.queryProjectListById(project);
			for (ProjectEntity entity : pList) {
				// project.setStatus(2L);//抽取中,即为申请成功
				entity.setStatus(project.getStatus());
				projectService.updateProjectStatus(entity);
				this.setAlertMessage(MessageConstants.APPLY_SUCCESS);
				saveProjectNo += entity.getProjectNo() + ",";
				// 获取项目信息
				// entity = projectService.queryProjectById(entity);

			}
			if (saveProjectNo == "") {
				throw new HZWException("数据有误！请联系管理员！");
			} else {
				saveProjectNo.substring(0, saveProjectNo.length() - 1);

			}
			// 申请抽取之后向抽取人发送通知短信
			if (1 == project.getStatus().intValue()) {
				// TODO 给抽取人发短信功能展示开放
				// projectService.smsExtractMobile(pList,saveProjectNo);
			}
		} catch (Exception e) {
			e.printStackTrace();
			this.setAlertMessage(MessageConstants.APPLY_FAILED);
		}
		project = null;
		return this.initList();
	}

	/**
	 * 跳转到设置抽取条件页面
	 * 
	 * @return
	 */
	@Action("addCondition")
	public String addCondition() {
		this.setOpaUserAndDate(project);
		if ("1".equals(isSave)) {// 已保存过，进行更新操作
			// projectService.updateProject(project);
		} else {
			// projectService.saveProject(project);
		}

		if (conEntity != null && conEntity.getId() != null && !"".equals(conEntity.getId())) {
			conEntity = projectService.queryConditionById(conEntity);
			isSave = "1";// 抽取条件已保存过
		} else {
			conEntity = new ConditionEntity();
			conEntity.setMethod(null);
			conEntity.setProjectId(project.getProjectId());
		}
		return "condition";
	}

	/**
	 * 保存抽取条件
	 * 
	 * @return
	 */
	@Action("saveCondition")
	public String saveCondition() {
		try {
			this.setOpaUserAndDate(conEntity);
			projectService.saveCondition(conEntity);
			this.setAlertMessage(MessageConstants.SAVE_SUCCESS);
		} catch (Exception e) {
			e.printStackTrace();
			this.setAlertMessage(MessageConstants.SAVE_FAILED);
		}
		return "condition";
	}

	/**
	 * 更新抽取条件
	 * 
	 * @return
	 */
	@Action("updateCondition")
	public String updateCondition() {
		try {
			this.setOpaUserAndDate(conEntity);
			projectService.updateCondition(conEntity);
			this.setAlertMessage(MessageConstants.SAVE_SUCCESS);
		} catch (Exception e) {
			e.printStackTrace();
			this.setAlertMessage(MessageConstants.SAVE_FAILED);
		}
		return "condition";
	}

	/**
	 * 回退到项目信息添加页面
	 * 
	 * @return
	 */
	@Action("backProject")
	public String backProject() {
		if (this.isCreateCondition()) {// 是否要保存或更新信息
			if (conEntity != null && conEntity.getId() != null && !"".equals(conEntity.getId())) {// 抽取条件是否已保存
				projectService.updateCondition(conEntity);// 更新
			} else {
				projectService.saveCondition(conEntity);// 未保存过,对已填写的内容进行保存
			}
		}
		project = new ProjectEntity();
		isSave = "1";// 项目信息已保存,用作信息保存
		project.setProjectId(conEntity.getProjectId());
		project = projectService.queryProjectById(project);
		return init();
	}

	// TODO

	/**
	 * 申请指定专家
	 * 
	 * @return
	 */
	@Action("auditPointToExperts")
	public String auditPointToExperts() {
		this.setOpaUserAndDate(project);
		if ("1".equals(isSave)) {// 已保存过，进行更新操作
			projectService.updateProject(project);
		} else {
			// 声明要新增的项目List
			List<ProjectEntity> proList = new ArrayList<ProjectEntity>();
			// 1、设置条件开始查询
			HttpServletRequest request = ServletActionContext.getRequest();
			// 招标项目编号
			String projectNum = request.getParameter("tenderProjectNum");
			// 招标项目名称
			String projectName = request.getParameter("tenderProjectName");
			String tenders = request.getParameter("tenders");
			String[] tenderProjectNum = projectNum.split(",");
			String[] tenderProjectName = projectName.split(",");
			String[] tender = tenders.split(",");
			for (int i = 0; i < tenderProjectNum.length; i++) {
				ProjectEntity newEntity = new ProjectEntity();
				SpringApplicationContext.copyProperties(project, newEntity); // 新对象赋值
				newEntity.setProjectNo(tenderProjectNum[i]);
				// String str=new
				// String(tenderProjectName[i].getBytes("ISO-8859-1"),"UTF-8");
				newEntity.setProjectName(tenderProjectName[i]);
				newEntity.setTender(tender[i]);
				this.setOpaUserAndDate(newEntity);
				proList.add(newEntity);
			}
			projectService.saveProjects(proList, null);
			project.setDecimationBatch(proList.get(0).getDecimationBatch());
			projectList = projectService.queryProjectListById(project);
		}
		projectEntity = project;
		return "apply";
	}

	/**
	 * 保存指定专家申请说明
	 * 
	 * @return
	 */
	@Action("saveApplyInfo")
	public String saveApplyInfo() {
		this.context();
		// 当前session
		HttpSession session = getRequest().getSession();
		// 当前用户信息
		UserEntity user = (UserEntity) session.getAttribute("userInfo");
		this.setOpaUserAndDate(apply);

		// 处理用户指定专家列表
		// 根据批次号查询指定专家的最大抽取次数
		Integer con = appointsExpertsService.queryMaxCON(apply.getDecimationBatch());
		if (con == null) {
			con = 0;
		}
		con += 1;
		// 声明要新增的项目List
		List<AppointsExpertsEntity> appList = new ArrayList<AppointsExpertsEntity>();
		// 1、设置条件开始查询 HttpServletRequest
		HttpServletRequest request = ServletActionContext.getRequest();
		String[] appiontsExpertsName = request.getParameterValues("expertsName");
		String[] appiontsExpertsCodeId = request.getParameterValues("expertsCodeId");

		for (int i = 0; i < appiontsExpertsName.length; i++) {
			AppointsExpertsEntity newEntity = new AppointsExpertsEntity();
			newEntity.setOfficialId(CommUtil.getKey());
			newEntity.setExpertsName(appiontsExpertsName[i]);
			newEntity.setExpertsCodeId(appiontsExpertsCodeId[i]);
			newEntity.setDecimationBatch(apply.getDecimationBatch());
			newEntity.setExtractCon(con);
			newEntity.setStatus(SysConstants.APPOINTS_EXPERTS_STAUS.SAVE);
			this.setOpaUserAndDate(newEntity);
			appList.add(newEntity);
		}
		projectService.copySaveApplyInfo(apply, user, appList);

		this.context();
		try {
			// 跳转到专家抽取申请页面
			this.getResponse().sendRedirect(this.getRequest().getContextPath() + "/project/initList");
		} catch (IOException e) {
			e.printStackTrace();
		}
		return null;
	}

	/**
	 * 提交指定专家
	 * 
	 * @return
	 */
	@Action("submitPointResult")
	public String submitPointResult() {
		this.context();
		this.getResponse().setContentType("text/html;charset=UTF-8");
		try {
			projectService.submitPointResult(result);
			this.getResponse().getWriter().write("success");
		} catch (Exception e) {
			try {
				this.getResponse().getWriter().write("no");
			} catch (IOException e1) {
				e1.printStackTrace();
			}
			e.printStackTrace();
		}
		return null;
	}

	/**
	 * 查询指定专家
	 * 
	 * @return
	 */
	@Action("queryPagePointExperts")
	public String queryPagePointExperts() {
		conEntity.setPage(this.getPage());
		conEntity.getPage().setShowCount(5);
		expertList = projectService.queryPagePointExperts(conEntity);
		resultList = projectService.queryPointedExperts(conEntity);
		return "pointTo";
	}

	/**
	 * 删除指定专家
	 * 
	 * @return
	 */
	@Action("deletePointResult")
	public String deletePointResult() {
		projectService.deletePointResult(result);
		return null;
	}

	/**
	 * 保存指定专家
	 * 
	 * @return
	 */
	@Action("savePointExperts")
	public String savePointExperts() {
		projectService.savePointExperts(result);
		return null;
	}

	/**
	 * 申请再次抽取（超3次后）
	 * 
	 * @return
	 */
	@Action("applyAgainExtract")
	public String applyAgainExtract() {
		conEntity = projectService.queryConditionById(conEntity);
		project = new ProjectEntity();
		project.setProjectId(conEntity.getProjectId());
		project = projectService.queryProjectById(project);
		return "applyAgain";
	}

	/**
	 * 保存再次抽取申请说明
	 * 
	 * @return
	 */
	@Action("saveApplyAgainExtract")
	public String saveApplyAgainExtract() {
		this.setOpaUserAndDate(apply);
		this.context();
		try {
			projectService.saveApplyAgainExtract(apply);
			this.setAlertMessage("信息提交成功！");
			this.getResponse().sendRedirect(this.getRequest().getContextPath() + "/waitProject/queryProjectList");
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}

	/**
	 * 查询所有项目负责人
	 * 
	 * @return
	 */
	@Action("queryProjectManager")
	public String queryProjectManager() {
		UserEntity user = new UserEntity();
		userList = projectService.queryProjectManager(user);
		return "selectManager";
	}

	/**
	 * 查询评标专业列表
	 * 
	 * @return
	 */
	@Action("queryMajorList")
	public String queryMajorList() {
		List<SpecialtyInfoEntity> specialtyInfoList = expertInfoService.querySpecialtyInfoList();
		if (null != expertType && !expertType.isEmpty()) {
			List<String> expertTypes = Arrays.asList(expertType.split(","));
			// 设置默认选择的菜单，即已选中的评标专业
			for (SpecialtyInfoEntity specialtyInfo : specialtyInfoList) {
				if (expertTypes.contains(specialtyInfo.getSpe_id())) {
					specialtyInfo.setChecked(true);
					setOpen(specialtyInfoList, specialtyInfo.getSpe_parent());
					if (specialtyInfo.getSpe_level() != null && 3 == specialtyInfo.getSpe_level().intValue()) {
						specialtyInfo.setOpen(true);
						setOpenNodes(specialtyInfoList, specialtyInfo.getSpe_id());
					}
				}
			}
		}
		JsonConfig config = new JsonConfig();
		// 通过JsonConfig的属性过滤器去除不需要的属性
		config.setJsonPropertyFilter(new PropertyFilter() {
			@Override
			public boolean apply(Object arg0, String arg1, Object arg2) {
				if (arg1.equals("spe_id") || arg1.equals("spe_parent") || arg1.equals("spe_name")
						|| arg1.equals("checked") || arg1.equals("open") || arg1.equals("spe_level")) {
					return false;// 不忽略
				}
				return true;
			}
		});
		JSONArray jsonArray = JSONArray.fromObject(specialtyInfoList, config);
		majorStr = jsonArray.toString();
		// 将属性名称修改zTree对应的属性名称
		majorStr = majorStr.replaceAll("spe_parent", "pId").replaceAll("spe_name", "name").replaceAll("spe_id", "id");
		return "majorStr";
	}

	/**
	 * 打开已选中的父级节点
	 * 
	 * @param specialtyInfoList
	 * @param speId
	 */
	private void setOpen(List<SpecialtyInfoEntity> specialtyInfoList, String speId) {
		for (SpecialtyInfoEntity specialtyInfoEntity : specialtyInfoList) {
			if (specialtyInfoEntity.getSpe_id().equals(speId)) {
				specialtyInfoEntity.setOpen(true);
				specialtyInfoEntity.setChecked(true);
				if (!"-1".equals(specialtyInfoEntity.getSpe_parent())) {
					setOpen(specialtyInfoList, specialtyInfoEntity.getSpe_parent());
				}
				break;
			}
		}
	}

	/**
	 * 打开已选中的子级节点
	 * 
	 * @param specialtyInfoList
	 * @param speId
	 */
	private void setOpenNodes(List<SpecialtyInfoEntity> specialtyInfoList, String speId) {
		for (SpecialtyInfoEntity specialtyInfoEntity : specialtyInfoList) {
			if (specialtyInfoEntity.getSpe_parent().equals(speId)) {
				specialtyInfoEntity.setOpen(true);
				specialtyInfoEntity.setChecked(true);
				if (null == specialtyInfoEntity.getSpe_level()) {
					continue;
				} else if (3 > specialtyInfoEntity.getSpe_level()) {
					setOpenNodes(specialtyInfoList, specialtyInfoEntity.getSpe_id());
				}
			}
		}
	}

	/**
	 * 抽取专家条件是否填写
	 * 
	 * @return
	 */
	@SuppressWarnings("unchecked")
	private boolean isCreateCondition() {
		Class cl = ConditionEntity.class;
		Method[] ms = cl.getDeclaredMethods();// 得到所有方法不含继承的方法
		String exclude = ",getProvince,getCity,getZone,getTotal,getExpertType,getScore,getSeniorNum";// 用于判断是否填写的字段.
		try {
			for (Method method : ms) {
				if (method.getName().startsWith("get") && exclude.contains("," + method.getName() + ",")) {
					Object obj = method.invoke(conEntity);
					if (obj != null && !obj.equals("")) {
						return true;
					}
				}
			}
		} catch (SecurityException e) {
			e.printStackTrace();
		} catch (IllegalArgumentException e) {
			e.printStackTrace();
		} catch (IllegalAccessException e) {
			e.printStackTrace();
		} catch (InvocationTargetException e) {
			e.printStackTrace();
		}
		return false;
	}

	/**
	 * 查询项目退回原因
	 * 
	 * @return
	 */
	@Action("projectReturnReason")
	public String projectReturnReason() {
		if (projectAuditEntity == null) {
			projectAuditEntity = new ProjectAuditEntity();
		}
		projectAuditEntity = projectService.getProAuditInfoByProId(projectAuditEntity);
		return "projectAuditReason";
	}

	/**
	 * 查看专家评价信息
	 * 
	 * @return
	 * @throws Exception
	 */
	@Action("toAppraiseDetail")
	public String toAppraiseDetail() throws Exception {
		// 专家信息
		expertInfoEntity = appraiseService.queryBaseAppraise(extractResultId);
		// 查询评分项
		appInfoList = appraiseService.queryAppraiseInfoById("0");
		// 查询评分
		appraiseList = appraiseService.queryAppraiseDetail(extractResultId, "0");
		// 评价备注
		appraiseRemark = appraiseService.queryAppraiseRemark(extractResultId);
		return "toAppraiseDetail";
	}

	/**
	 * 批量修改抽取专家结果
	 * 
	 * @return
	 */
	@Action("submitResult")
	public String submitResult() {
		this.context();
		String msg = "fail";
		PrintWriter out = null;
		try {
			out = this.getResponse().getWriter();
			msg = projectService.submitResultToVoice(resultList, conEntity);
			/*
			 * if(msg.equals("success")) {
			 * this.setAlertMessage(MessageConstants.SAVE_SUCCESS); }else{
			 * this.setAlertMessage(msg); project = new ProjectEntity();
			 * project.setDecimationBatch(conEntity.getDecimationBatch());
			 * return this.updateMajor(); }
			 */

		} catch (Exception e) {
			e.printStackTrace();
			this.setAlertMessage(MessageConstants.SAVE_FAILED);
		} finally {
			out.print(msg);
			out.close();
		}
		return null;
	}

	/**
	 * 再次申请
	 * 
	 * @return
	 */
	@Action("updateMajor")
	public String updateMajor() {
		projectList = projectService.queryProjectListById(project);
		if (projectList != null && projectList.size() > 0) {
			project = projectList.get(0);
			projectList.remove(0);
			if (null == conEntity) {
				conEntity = new ConditionEntity();
			}
			conEntity.setProjectId(project.getProjectId());
			if (null != conEntity.getId() && !conEntity.getId().isEmpty()) {
				conEntity = projectService.queryConditionById(conEntity);
			}

			// 默认查询“系统自动语音抽取”模板,且有效的模板
			TemplateEntity entity = new TemplateEntity();
			entity.setTemplateType(String.valueOf(SysConstants.CONDITION_METHOD.METHOD_THREE));
			entity.setIsValid(SysConstants.IS_VALID.VALID_ZERO);
			// 非人工通知根据抽取方式查询模板信息
			templateEntityList = templateService.queryTemplateList(entity);
			// 默认查询“语音短信”模板，且有效的模板
			entity.setTemplateType(String.valueOf(SysConstants.CONDITION_METHOD.METHOD_TWO));
			// 非人工通知根据抽取方式查询模板信息
			templateSmsList = templateService.queryTemplateList(entity);
			
			companyDebarbList =  new ArrayList<ExtractDebarbEntity>();
			extractDebarbList = new ArrayList<ExtractDebarbEntity>();
			//根据批次号查询回避信息
			ExtractDebarbEntity debarbEntity = new ExtractDebarbEntity();
			debarbEntity.setDecimationbatch(project.getDecimationBatch());
			List<ExtractDebarbEntity> debarbList = debarbService.queryExtractDebarbList(debarbEntity);
			if(debarbList!=null&&debarbList.size()>0){
				for(ExtractDebarbEntity debarb:debarbList){
					if(SysConstants.DEBARB_TYPE.TYPE_1.equals(debarb.getDebarbType())){
						debarb.setCompanyDebarbReason(debarb.getDebarbReason());
						companyDebarbList.add(debarb);
					} else {
						debarb.setExpertDebarbReason(debarb.getDebarbReason());
						extractDebarbList.add(debarb);
					
					}
				}
			}
			isSave = "1";// 更新操作
		}
		return "projectUpdateEx";
	}

	/**
	 * 向专家发送邀请短信
	 * 
	 * @return
	 */
	@Action("smsExperts")
	public String smsExperts() {
		this.context();
		this.getResponse().setContentType("text/html;charset=UTF-8");
		try {
			if (null == conEntity) {
				this.getResponse().getWriter().write("fail");
				return null;
			}
			projectService.sendMessageForExperts(conEntity, expertList);
			this.getResponse().getWriter().write("success");
		} catch (Exception e) {
			if (e instanceof HZWException) {
				this.setAlertMessage(e.getMessage());
			}
			e.printStackTrace();
			try {
				this.getResponse().getWriter().write("fail");
			} catch (IOException e1) {
				e1.printStackTrace();
			}
		}
		return null;
	}

	/**
	 * 根据抽取批次查询项目
	 * 
	 * @return
	 */
	@Action("showProjects")
	public String showProjects() {
		try {
			projectList = projectService.queryProListByBatch(project);
			deleteRecordList = projectService.getDeleteRecordList();
			this.setAlertMessage(MessageConstants.SAVE_SUCCESS);

		} catch (Exception e) {
			e.printStackTrace();
			this.setAlertMessage(MessageConstants.SAVE_FAILED);
		}
		return "showProjects";
	}

	/**
	 * 该项目是否能被删除
	 * 
	 * @return
	 */
	@Action("queryConfirmExpert")
	public void queryConfirmExpert() {
		this.context();
		this.getResponse().setContentType("text/html;charset=UTF-8");
		try {
			Integer count = projectService.queryConfirmExpertCount(conEntity);
			this.getResponse().getWriter().write(count.toString());
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
	 * 专家请假功能(语音专用) 函数功能描述：
	 * @return
	 */
	@Action("expertsLeave")
	public String expertsLeave() {
		this.context();
		this.getResponse().setCharacterEncoding("utf-8");
		PrintWriter out = null;
		String msg = "";
		ExpertInfoEntity entity = null;
		try {
			out = this.getResponse().getWriter();
			if (expertInfoEntity.getMobilephone() != null && !"".equals(expertInfoEntity.getMobilephone())) {
				entity = expertInfoService.queryExpertByMobile(expertInfoEntity.getMobilephone());
			}
			if (entity != null) {
				// 根据专家id和批次号,修改理由和设置不参加状态
				ResultEntity rEntity = new ResultEntity();
				if (result.getDecimationBatch() != null && !"".equals(result.getDecimationBatch())) {
					rEntity.setDecimationBatch(result.getDecimationBatch().trim());
					// 第一步：判断当前的项目批次号是否是语音
					ProjectEntity pEntity = new ProjectEntity();
					;
					pEntity.setDecimationBatch(result.getDecimationBatch().trim());
					// 根据流水号查询抽取记录
					List<ConditionEntity> conditionList = projectService.queryConditionListByBatch(pEntity);
					if (conditionList == null || conditionList.size() == 0) {
						msg = "当前批次号不存在";
					} else {
						if (conditionList.get(0).getMethod()
								.longValue() != SysConstants.CONDITION_METHOD.METHOD_THREE) {
							msg = "当前项目不是语音抽取项目";
						} else {
							if (conditionList.size() > 0) {
								// 第二步：判断当前专家是否已同意参标
								ResultEntity resultEntity = new ResultEntity();
								resultEntity.setDecimationBatch(result.getDecimationBatch().trim());
								resultEntity.setUserId(entity.getUser_id());
								resultEntity.setJoinStatus(SysConstants.JOIN_STATUS.ATTEND);
								List<ResultEntity> rLst = projectService.queryAllExtractedExperts(resultEntity);
								if (rLst != null && rLst.size() > 0) {
									rEntity.setUserId(entity.getUser_id());// 专家id
									rEntity.setJoinStatus(1L);// 设置专家状态为不参加
									if (result.getReason() != null && !"".equals(result.getReason())) {
										rEntity.setReason(result.getReason());
										// 修改专家状态
										expertInfoService.updateExpertByIdAndBatch(rEntity);
										// 修改当前项目状态
										pEntity.setStatus(SysConstants.PROJECT_STATUS.DOING);
										projectService.updateProjectStatus(pEntity);

										projectList = projectService.queryProjectListById(pEntity);
										String msgType = projectService.checkBidTime(projectList, conditionList.get(0));
										if (msgType.equals("success")) {
											//判断当前的专家是否都通知过，如果没有通知的继续通知
											//并且将未通知的专家的状态改成“不参加” 理由 “ 其他-人数以满足”
											ResultEntity resultDetail = new ResultEntity();
											resultDetail.setDecimationBatch(result.getDecimationBatch().trim());
											resultDetail.setJoinStatus(SysConstants.JOIN_STATUS.NOINFORM);
											projectService.updateProjectEndExpertTOVoice(resultDetail);
											
											
											
											// 直接抽取专家
											voiceService.disposeSendExpert(conditionList.get(0));
											msg = "success";
										} else {
											msg = msgType;
										}
									} else {
										msg = "请假理由为空";
									}
								} else {
									msg = "当前专家并没有同意参加此项目";
								}
							}
						}
					}
				} else {
					msg = "emptyBatch";// 批次号为空
				}
			} else {
				msg = "手机号码为空,或者手机号码填写有误！";
			}
			out.print(msg);
			out.close();
		} catch (Exception e) {
			if (e instanceof HZWException) {
				out.print(e.getMessage());
			}
		}
		return null;
	}

	/**
	 * 查询所有专家所在的机构集合
	 * 
	 * @return
	 */
	@Action("queryExpertCompany")
	public String queryExpertCompany() {
		this.context();
		try {
			this.getResponse().setCharacterEncoding("utf-8");
			if (null == extractDebarbEntity) {
				extractDebarbEntity = new ExtractDebarbEntity();
			}else{
				// out = this.getResponse().getWriter();
				expertInfoEntity = new ExpertInfoEntity();
				expertInfoEntity.setCompany(extractDebarbEntity.getCompany());
				expertInfoEntity.setPage(this.getPage());
	
				expertInfoList = expertInfoService.queryExpertCompany(expertInfoEntity);
			}
			// JSONArray jsonArray = JSONArray.fromObject(expertInfoList);
			// out.print(jsonArray);
		} catch (Exception e) {
			e.printStackTrace();
			this.setAlertMessage("查询回避公司失败");

		}
		return "debarbCompany";
	}

	/**
	 * 查询所有专家所在的机构集合
	 * 
	 * @return
	 */
	@Action("queryExpertCode")
	public String queryExpertCode() {
		this.context();
		try {
			this.getResponse().setCharacterEncoding("utf-8");
			if (null == extractDebarbEntity) {
				extractDebarbEntity = new ExtractDebarbEntity();
			}else{
			// out = this.getResponse().getWriter();
			expertInfoEntity = new ExpertInfoEntity();
			expertInfoEntity.setId_no(extractDebarbEntity.getExpertCode());
			expertInfoEntity.setMobilephone(extractDebarbEntity.getExpertPhone());
			expertInfoEntity.setPage(this.getPage());
			expertInfoList = expertInfoService.queryExpertCode(expertInfoEntity);
			// JSONArray jsonArray = JSONArray.fromObject(expertInfoList);
			// out.print(jsonArray);
			}
		} catch (Exception e) {
			e.printStackTrace();
			this.setAlertMessage("查询回避公司失败");
		}
		return "debarbExpertCode";
	}

	/**
	 * 
	 * 函数功能描述：查出项目详细信息并回显页面
	 * 
	 * @return
	 */
	@Action("projectChange")
	public String projectChange() {
		try {
			if (null == project) {
				project = new ProjectEntity();
			}
			// 获得原项目信息，在变更项目弹出层回显
			project = projectService.queryProjectById(project);
			if (null == projectChange) {
				projectChange = new ProjectChangesEntity();
			}
			projectChange.setDecimationBatch(project.getDecimationBatch());
			// 通过项目批次号，查询该项目变更记录集合
			projectChangeList = voiceProjectService.queryProChangeList(projectChange);

			// 如果该集合为null或者集合长度为0，则该项目没有变更记录，弹出层展示原项目信息
			if (null != projectChangeList && projectChangeList.size() > 0) {
				// 否则展示最新批次的变更记录信息
				projectChange = projectChangeList.get(projectChangeList.size() - 1);

				project.setBidTime(projectChange.getNewBidTime());
				project.setBidAddress(projectChange.getNewBidAddress());
			}

			// 默认查询“系统自动语音抽取”模板,且有效的模板
			TemplateEntity entity = new TemplateEntity();
			entity.setTemplateType(String.valueOf(SysConstants.TEMPLATE_TYPE.TYPE_FOUR));
			entity.setIsValid(SysConstants.IS_VALID.VALID_ZERO);
			// 非人工通知根据抽取方式查询模板信息
			templateEntityList = templateService.queryTemplateList(entity);
			// 查出有效、未删除的变更项目短信模板
			// 从短信模板表，查出有效、未删除的变更项目短信模板回显页面
			templateEntity = templateEntityList.get(0);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return "projectChange";
	}

	/**
	 * 
	 * 函数功能描述：变更项目后，把变更信息语音短信发送给专家
	 * 
	 * @return
	 */
	@Action("sendChangeProjectForExperts")
	public String sendVoiceChangeProForExperts() {
		this.context();
		String resStatus = "";
		PrintWriter out = null;
		try {
			out = this.getResponse().getWriter();
			if (null == result) {
				result = new ResultEntity();
			}
			// 查询该项目所有参加的专家集合
			result.setJoinStatus(SysConstants.JOIN_STATUS.ATTEND);
			expertInfoList = voiceProjectService.queryExpertList(result);
			if (null == projectEntity) {
				projectEntity = new ProjectEntity();
			}
			projectEntity.setDecimationBatch(result.getDecimationBatch());
			// 通过项目批次号查询项目修改过的项目信息
			List<ProjectEntity> proList = projectService.queryProjectByDecimationBatch(projectEntity);
			projectEntity = proList.get(0);
			//
			if (null == projectChange) {
				projectChange = new ProjectChangesEntity();
			}
			// 查询该项目最新变更记录信息
			projectChange.setDecimationBatch(result.getDecimationBatch());
			projectChangeList = voiceProjectService.queryProChangeList(projectChange);
			projectChange = projectChangeList.get(projectChangeList.size() - 1);
			// 将最新变更信息封装到projectEntity对象，成为发送语音短信接口的参数
			projectEntity.setBidTime(projectChange.getNewBidTime());
			projectEntity.setBidAddress(projectChange.getNewBidAddress());
			//
			if (null == conEntity) {
				conEntity = new ConditionEntity();
			}
			// conEntity.setIsValid(SysConstants.IS_VALID.VALID_ZERO);
			// conEntity.setDeleteFlag(0L);
			// conEntity.setTemplateType("4");
			// conditionList =
			// voiceProjectService.querySysMessageTemplateList(conEntity);
			// conEntity = conditionList.get(0);
			// 将页面通知短信模板存入conEntity对象
			conEntity.setTemplateType(String.valueOf(SysConstants.TEMPLATE_TYPE.TYPE_FOUR));
			conEntity.setSmsContent(projectChange.getChangeReason());
			// 发送语音短息给专家
			projectService.changeProjectSmsExperts(projectEntity, expertInfoList, conEntity);
			
			resStatus = "success";
		} catch (Exception e) {
			e.printStackTrace();
			out.print("error");
		} finally {
			out.write(resStatus);
			if (out != null) {
				out.close();
			}
		}
		return null;
	}

	/**
	 * 
	 * 函数功能描述：变更项目后，把变更项目信息存入变更记录表
	 * 
	 * @return
	 */
	@Action("addProjectChangeRecodes")
	public String addProjectChangeRecodes() {
		this.context();
		// 当前session
		HttpSession session = getRequest().getSession();
		String resStatus = "";
		PrintWriter out = null;
		try {
			out = this.getResponse().getWriter();
			// 获得当前用户信息
			UserEntity user = (UserEntity) session.getAttribute("userInfo");
			if (null == projectChange) {
				projectChange = new ProjectChangesEntity();
			}
			// 获得变更项目记录表该项目当前最大修改批次,如果没有修改则sort为0
			if (null != projectChange.getDecimationBatch()) {

			}
			Integer sort = voiceProjectService.getMaxSort(projectChange);
			if (null == sort) {
				sort = 0;
			}
			// List<String> expertIdList =
			// voiceProjectService.queryExpertIdList(projectChange);
			// 项目变更记录信息赋值
			projectChange.setChangeId(CommUtil.getKey());
			projectChange.setChangeStatus(1);
			projectChange.setChangeTime(new Date());
			projectChange.setChangeUser(user.getUser_id());
			projectChange.setSort(sort + 1);
			// 当前项目变更信息添加到变更项目记录表
			Integer row = voiceProjectService.addProjectChangeRecords(projectChange);
			if (null == project) {
				project = new ProjectEntity();
			}
			project.setDecimationBatch(projectChange.getDecimationBatch());
			// 修改时间
			project.setModify_time(new Date());
			// 项目修改状态变为变更
			project.setModifyType("1");
			// 变更项目后，修改原项目状态
			Integer row1 = voiceProjectService.updateChangeProject(project);
			if (row > 0 && row1 > 0) {
				resStatus = "success";
			}
		} catch (Exception e) {
			e.printStackTrace();
			out.print("error");
		} finally {
			out.write(resStatus);
			if (out != null) {
				out.close();
			}
		}
		return null;
	}

	// /**
	// *
	// * 函数功能描述：变更项目后，修改原项目信息
	// *
	// * @return
	// */
	// @Action("updateChangeProject")
	// public String updateChangeProject() {
	// this.context();
	// // 当前session
	// HttpSession session = getRequest().getSession();
	// // 当前用户信息
	// UserEntity user = (UserEntity) session.getAttribute("userInfo");
	// String resStatus = "";
	// PrintWriter out = null;
	// try {
	// out = this.getResponse().getWriter();
	// if (null == project) {
	// project = new ProjectEntity();
	// }
	// // 得到修改人id
	// project.setModify_id(user.getUser_id());
	// // 修改时间
	// project.setModify_time(new Date());
	// // 项目修改状态变为变更
	// project.setModifyType("1");
	// // 变更项目后，修改原项目信息
	// Integer row = voiceProjectService.updateChangeProject(project);
	// if (row > 0) {
	// resStatus = "success";
	// }
	// } catch (IOException e) {
	// e.printStackTrace();
	// } finally {
	// out.write(resStatus);
	// if (out != null) {
	// out.close();
	// }
	// }
	// return null;
	// }

	/**
	 * 
	 * 函数功能描述：展示变更记录详情
	 * 
	 * @return
	 */
	@Action("proChangeDetail")

	public String proChangeDetail() {
		try {
			if (null == project) {
				project = new ProjectEntity();
			}
			if (null == projectChange) {
				projectChange = new ProjectChangesEntity();
			}
			project.setDecimationBatch(projectChange.getDecimationBatch());
			// 通过项目批次号查询项目编号项目名称
			List<ProjectEntity> proList = projectService.queryProjectByDecimationBatch(project);
			project = proList.get(0);
			// 通过项目批次号查询该项目的变更记录详情
			projectChangeList = voiceProjectService.queryProChangeList(projectChange);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return "proChangeDetail";
	}

	/**
	 * 
	 * 函数功能描述：去取消页面
	 * 
	 * @return
	 */
	@Action("toProjectCancel")
	public String projectCancel() {

		try {
			if (null == project) {
				project = new ProjectEntity();
			}
			// 获得原项目信息，在变更项目弹出层回显
			project = projectService.queryProjectById(project);
			if (null == projectChange) {
				projectChange = new ProjectChangesEntity();
			}
			// 默认查询“系统自动语音抽取”模板,且有效的模板
			TemplateEntity entity = new TemplateEntity();
			entity.setTemplateType(String.valueOf(SysConstants.TEMPLATE_TYPE.TYPE_FIVE));
			entity.setIsValid(SysConstants.IS_VALID.VALID_ZERO);
			// 非人工通知根据抽取方式查询模板信息
			templateEntityList = templateService.queryTemplateList(entity);
			// 查出有效、未删除的变更项目短信模板
			// 从短信模板表，查出有效、未删除的变更项目短信模板回显页面
			templateEntity = templateEntityList.get(0);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return "projectCancel";
	}

	/**
	 * 函数功能描述：取消项目
	 * 发送短信给参加的专家，将参加的专家状态修改为不参加T_EXTRACT_RESULT，在变更记录表中添加一条数据T_Project_CHANGES
	 * 
	 * @return
	 */
	@Action("cancelProject")
	public String cancelProject() {
		this.context();
		this.getResponse().setCharacterEncoding("utf-8");
		PrintWriter out = null;
		try {
			out = this.getResponse().getWriter();
			if (this.project == null) {
				message = "取消失败：项目批次号不存在";
			} else {
				// 给专家发送短信
				// 查询出需要发送的专家信息
				if (result == null) {
					result = new ResultEntity();
				}
				// 根据项目号
				result.setDecimationBatch(project.getDecimationBatch());
				// 且是参加的，
				result.setJoinStatus(SysConstants.JOIN_STATUS.ATTEND);
				// 查询出要发送短信的专家集合
				expertList = voiceProjectService.queryExpertList(result);
				// 短信模板
				conEntity = new ConditionEntity();
				conEntity.setSmsContent(projectChange.getChangeReason());
				// 调用发送短信接口,如果查询到当前取消的项目有参加的专家，则发送短信
				if (expertList != null && expertList.size() > 0) {
					conEntity.setTemplateType(String.valueOf(SysConstants.TEMPLATE_TYPE.TYPE_FIVE));
					// projectService.sendVoiceForExperts(project, conEntity,
					// expertList);
					project = projectService.queryProjectByDecimationBatch(project).get(0);
					projectService.changeProjectSmsExperts(project, expertList, conEntity);
				}
				// 主键
				projectChange.setChangeId(CommUtil.getKey());
				// 流水编号
				projectChange.setDecimationBatch(this.project.getDecimationBatch());
				// 取消时间
				projectChange.setChangeTime(new Date());
				// 变更状态
				projectChange.setChangeStatus(2);
				// 当前次序号
				projectChange.setDecimationBatch(project.getDecimationBatch());
				// 项目变更第几次，null为第一次
				Integer sort = voiceProjectService.getMaxSort(projectChange) == null ? 1
						: voiceProjectService.getMaxSort(projectChange) + 1;
				projectChange.setSort(sort);
				// 当前登录的用户
				UserEntity user = (UserEntity) this.getRequest().getSession().getAttribute("userInfo");
				// 当前session登录的用户主键
				projectChange.setChangeUser((user.getUser_id()));
				// 变更表中添加一条数据
				voiceProjectService.addProjectChangeRecords(projectChange);
				// 修改抽取专家结果表中的参加状态
				projectService.cancelProject(project);
				// 修改项目取消状态
				Integer result = projectService.modifyJoinStaus(project);
				if (result >= 0) {
					message = "取消成功";
				} else {
					message = "取消失败";
				}
			}
		} catch (Exception e) {
			if (e instanceof HZWException) {
				message = e.getMessage();
			} else
				e.printStackTrace();
		} finally {
			out.write(message);
			out.close();
		}
		return null;
	}
	
	
	/**
	 * 函数功能描述：取消项目
	 * 发送短信给参加的专家，将参加的专家状态修改为不参加T_EXTRACT_RESULT，在变更记录表中添加一条数据T_Project_CHANGES
	 * 
	 * @return
	 */
	@Action("fasongduanxin")
	public String fasongduanxin() {
		this.context();
		this.getResponse().setCharacterEncoding("utf-8");
		PrintWriter out = null;
		try {
			out = this.getResponse().getWriter();
			
			String msg ="13451920956,13505140602";
			
			
			
			if (this.project == null) {
				message = "取消失败：项目批次号不存在";
			} else {
				// 给专家发送短信
				// 查询出需要发送的专家信息
				if (result == null) {
					result = new ResultEntity();
				}
				// 根据项目号
				result.setDecimationBatch(project.getDecimationBatch());
				// 且是参加的，
				result.setJoinStatus(SysConstants.JOIN_STATUS.ATTEND);
				// 查询出要发送短信的专家集合
				expertList = voiceProjectService.queryExpertList(result);
				// 短信模板
				conEntity = new ConditionEntity();
				conEntity.setSmsContent(projectChange.getChangeReason());
				// 调用发送短信接口,如果查询到当前取消的项目有参加的专家，则发送短信
				if (expertList != null && expertList.size() > 0) {
					conEntity.setTemplateType(String.valueOf(SysConstants.TEMPLATE_TYPE.TYPE_FIVE));
					// projectService.sendVoiceForExperts(project, conEntity,
					// expertList);
					project = projectService.queryProjectByDecimationBatch(project).get(0);
					projectService.changeProjectSmsExperts(project, expertList, conEntity);
				}
				// 主键
				projectChange.setChangeId(CommUtil.getKey());
				// 流水编号
				projectChange.setDecimationBatch(this.project.getDecimationBatch());
				// 取消时间
				projectChange.setChangeTime(new Date());
				// 变更状态
				projectChange.setChangeStatus(2);
				// 当前次序号
				projectChange.setDecimationBatch(project.getDecimationBatch());
				// 项目变更第几次，null为第一次
				Integer sort = voiceProjectService.getMaxSort(projectChange) == null ? 1
						: voiceProjectService.getMaxSort(projectChange) + 1;
				projectChange.setSort(sort);
				// 当前登录的用户
				UserEntity user = (UserEntity) this.getRequest().getSession().getAttribute("userInfo");
				// 当前session登录的用户主键
				projectChange.setChangeUser((user.getUser_id()));
				// 变更表中添加一条数据
				voiceProjectService.addProjectChangeRecords(projectChange);
				// 修改抽取专家结果表中的参加状态
				projectService.cancelProject(project);
				// 修改项目取消状态
			
			}
		} catch (Exception e) {
			if (e instanceof HZWException) {
				message = e.getMessage();
			} else
				e.printStackTrace();
		} finally {
			out.write(message);
			out.close();
		}
		return null;
	}
	
	/**
	 * 定时器功能
	 * 接收短信的报告
	 */
	@Action("test")
	public void test(){
		String mobiles="13805175365,15995882498,15951926904,13813846699," +
				"13913809415,13705180755,13851540465,13814015616,13914736904," +
				"13605151639,18751859996,13451920956,18694978005,13813885232," +
				"13901598728,13905191197,13952002026,13601468104,15952027732,13913823477," +
				"13584060515,13905165478,15005144285,13814015616,13584096066,13913942080," +
				"13451920956,13951609676,18551720186,15005159836,13505153528,13851857177," +
				"13805188602,13770682912,15996207277,15062285675,13003423108,13605186336," +
				"13776500376,13770666491,13338610398,18013011561,15077888060,13912941328,13851714318," +
				"13901593710,13260998201,13851886022,18262602768,13851876930,13813063505,13913897036," +
				"13905171013,13851601859,17366338658,13809003931,15651628898,13951036118,13505140602," +
				"18652064510,13901593710,18651880226,13951767623,15850603280,13951870205,13951947689," +
				"13809022633,13605160212,13809009056,13913856033,13814006656,13851833296,13813871222," +
				"13585208420,18552508828,15261808623,15996219587,13815867574,13809036715,18761648740," +
				"18851815566,13813867667,18652055089,13951704207,13851799956,13605173093,13913919188," +
				"13605140341,15195798599,13851661498,13851989572,13770325893,18651814296,13951953185," +
				"15850650779,13605160212,13951609395,13770751462,13813851979,13218090122,15950466665,13701450286," +
				"13851603916,13814036274,18013012716,13814177009,13851700578,13951861907,15951021888,18013878629,13814002758," +
				"15151818335,13770843218,13776616620,18851194999,18625249788,13851538648，13851661498,13951953185,13951028218," +
				"18652091031,18051982828,13952044556,14751920756,13851922279,13611591969,13770588366,19822606522,13605161898," +
				"13805167038,13901594452,18205179906,13505140602,15295378180";
		String[] contactInfoArr = mobiles.split(",");
		String msg ="2020年元旦、春节将至，根据中共中央办公厅、国务院办公厅通知精神以及省工信厅党组和驻厅纪检监察组有关要求，现就做好节日期间严明纪律廉洁过节工作，发布廉洁提醒通知如下：中心及所属单位全体同志要严格遵守廉洁纪律，严格执行中央八项规定及其实施细则精神，强化自我约束、坚守纪律底线，严禁用公款购买赠送贺年卡、烟花爆竹、烟酒等年货节礼；严禁违规公款吃喝或用公款组织各种聚餐、宴请、酒会；严禁违反规定滥发津贴、补贴、奖金和实物；严禁组织和参与用公款支付的旅游度假、与公务无关的消费活动；严禁公车私用、“私车公养”或违规借用、占用管理和服务对象车辆；严禁违规收受礼品、礼金、消费卡和有价证券、股权、其他金融产品等财物；严禁违规接受宴请或旅游、健身、娱乐等活动安排；严禁违规操办婚丧喜庆事宜；严禁违反有关规定出入私人会所；严禁酒驾、醉驾等。（省招标中心党总支）";
		 if (!EmptyUtils.isEmpty(contactInfoArr)) {
			//循环遍历所有的联系人
             for ( int i = 0 ; i < contactInfoArr.length ; i++ ) {
                 if (EmptyUtils.isEmpty(contactInfoArr[i])) {
                     continue;
                 }
                 voiceService.smsTongzhi(contactInfoArr[i],msg);
             }
		 }
		System.out.println("程序结束");
	  }
	
	public ProjectEntity getProject() {
		return project;
	}

	public void setProject(ProjectEntity project) {
		this.project = project;
	}

	public ProjectEntity getProjectEntity() {
		return projectEntity;
	}

	public void setProjectEntity(ProjectEntity projectEntity) {
		this.projectEntity = projectEntity;
	}

	public String getIsSave() {
		return isSave;
	}

	public void setIsSave(String isSave) {
		this.isSave = isSave;
	}

	public ConditionEntity getConEntity() {
		return conEntity;
	}

	public void setConEntity(ConditionEntity conEntity) {
		this.conEntity = conEntity;
	}

	public List<ExpertInfoEntity> getExpertList() {
		return expertList;
	}

	public void setExpertList(List<ExpertInfoEntity> expertList) {
		this.expertList = expertList;
	}

	public ResultEntity getResult() {
		return result;
	}

	public void setResult(ResultEntity result) {
		this.result = result;
	}

	public List<ResultEntity> getResultList() {
		return resultList;
	}

	public void setResultList(List<ResultEntity> resultList) {
		this.resultList = resultList;
	}

	public List<List<ResultEntity>> getExtractList() {
		return extractList;
	}

	public void setExtractList(List<List<ResultEntity>> extractList) {
		this.extractList = extractList;
	}

	public ApplyRecordEntity getApply() {
		return apply;
	}

	public void setApply(ApplyRecordEntity apply) {
		this.apply = apply;
	}

	public ProjectService getProjectService() {
		return projectService;
	}

	public void setProjectService(ProjectService projectService) {
		this.projectService = projectService;
	}

	public ExpertInfoEntity getExpert() {
		return expert;
	}

	public void setExpert(ExpertInfoEntity expert) {
		this.expert = expert;
	}

	public List<ExpertInfoEntity> getExtractExpertList() {
		return extractExpertList;
	}

	public void setExtractExpertList(List<ExpertInfoEntity> extractExpertList) {
		this.extractExpertList = extractExpertList;
	}

	public String getMajorStr() {
		return majorStr;
	}

	public void setMajorStr(String majorStr) {
		this.majorStr = majorStr;
	}

	public String getExpertType() {
		return expertType;
	}

	public void setExpertType(String expertType) {
		this.expertType = expertType;
	}

	public String getMessage() {
		return message;
	}

	public void setMessage(String message) {
		this.message = message;
	}

	public List<UserEntity> getUserList() {
		return userList;
	}

	public void setUserList(List<UserEntity> userList) {
		this.userList = userList;
	}

	public List<UserEntity> getOperatorList() {
		return operatorList;
	}

	public void setOperatorList(List<UserEntity> operatorList) {
		this.operatorList = operatorList;
	}

	public List<ProjectEntity> getProjectList() {
		return projectList;
	}

	public void setProjectList(List<ProjectEntity> projectList) {
		this.projectList = projectList;
	}

	public List<ConditionEntity> getConditionList() {
		return conditionList;
	}

	public void setConditionList(List<ConditionEntity> conditionList) {
		this.conditionList = conditionList;
	}

	public List<ExpertInfoEntity> getExpertInfoList() {
		return expertInfoList;
	}

	public void setExpertInfoList(List<ExpertInfoEntity> expertInfoList) {
		this.expertInfoList = expertInfoList;
	}

	public List<ExtractRecordEntity> getExRecordList() {
		return exRecordList;
	}

	public void setExRecordList(List<ExtractRecordEntity> exRecordList) {
		this.exRecordList = exRecordList;
	}

	public ProjectAuditEntity getProjectAuditEntity() {
		return projectAuditEntity;
	}

	public void setProjectAuditEntity(ProjectAuditEntity projectAuditEntity) {
		this.projectAuditEntity = projectAuditEntity;
	}

	public String getExtractResultId() {
		return extractResultId;
	}

	public void setExtractResultId(String extractResultId) {
		this.extractResultId = extractResultId;
	}

	public ExpertInfoEntity getExpertInfoEntity() {
		return expertInfoEntity;
	}

	public void setExpertInfoEntity(ExpertInfoEntity expertInfoEntity) {
		this.expertInfoEntity = expertInfoEntity;
	}

	public List<AppraiseInfo> getAppInfoList() {
		return appInfoList;
	}

	public void setAppInfoList(List<AppraiseInfo> appInfoList) {
		this.appInfoList = appInfoList;
	}

	public List<Appraise> getAppraiseList() {
		return appraiseList;
	}

	public void setAppraiseList(List<Appraise> appraiseList) {
		this.appraiseList = appraiseList;
	}

	public AppraiseRemark getAppraiseRemark() {
		return appraiseRemark;
	}

	public void setAppraiseRemark(AppraiseRemark appraiseRemark) {
		this.appraiseRemark = appraiseRemark;
	}

	public Integer getLocalExpertCount() {
		return localExpertCount;
	}

	public void setLocalExpertCount(Integer localExpertCount) {
		this.localExpertCount = localExpertCount;
	}

	public Integer getSeniorExpertCount() {
		return seniorExpertCount;
	}

	public void setSeniorExpertCount(Integer seniorExpertCount) {
		this.seniorExpertCount = seniorExpertCount;
	}

	public String getReason() {
		return reason;
	}

	public void setReason(String reason) {
		this.reason = reason;
	}

	public boolean isApplyFlag() {
		return applyFlag;
	}

	public void setApplyFlag(boolean applyFlag) {
		this.applyFlag = applyFlag;
	}

	public String getApplyDate() {
		return applyDate;
	}

	public void setApplyDate(String applyDate) {
		this.applyDate = applyDate;
	}

	public boolean isHoliday() {
		return isHoliday;
	}

	public void setHoliday(boolean isHoliday) {
		this.isHoliday = isHoliday;
	}

	public ZJKCalendarService getZjkCalendarService() {
		return zjkCalendarService;
	}

	public void setZjkCalendarService(ZJKCalendarService zjkCalendarService) {
		this.zjkCalendarService = zjkCalendarService;
	}

	public boolean isLastWorkDay() {
		return isLastWorkDay;
	}

	public void setLastWorkDay(boolean isLastWorkDay) {
		this.isLastWorkDay = isLastWorkDay;
	}

	public String getCurrDate2() {
		return currDate2;
	}

	public void setCurrDate2(String currDate2) {
		this.currDate2 = currDate2;
	}

	public AppointsExpertsEntity getAppointsEntity() {
		return appointsEntity;
	}

	public void setAppointsEntity(AppointsExpertsEntity appointsEntity) {
		this.appointsEntity = appointsEntity;
	}

	public List<AppointsExpertsEntity> getAppointsList() {
		return appointsList;
	}

	public void setAppointsList(List<AppointsExpertsEntity> appointsList) {
		this.appointsList = appointsList;
	}

	public List<TemplateEntity> getTemplateEntityList() {
		return templateEntityList;
	}

	public void setTemplateEntityList(List<TemplateEntity> templateEntityList) {
		this.templateEntityList = templateEntityList;
	}

	public boolean isToShow() {
		return toShow;
	}

	public void setToShow(boolean toShow) {
		this.toShow = toShow;
	}

	public List<TemplateEntity> getTemplateSmsList() {
		return templateSmsList;
	}

	public void setTemplateSmsList(List<TemplateEntity> templateSmsList) {
		this.templateSmsList = templateSmsList;
	}

	public List<DeleteRecord> getDeleteRecordList() {
		return deleteRecordList;
	}

	public void setDeleteRecordList(List<DeleteRecord> deleteRecordList) {
		this.deleteRecordList = deleteRecordList;
	}

	public ExtractDebarbEntity getExtractDebarbEntity() {
		return extractDebarbEntity;
	}

	public void setExtractDebarbEntity(ExtractDebarbEntity extractDebarbEntity) {
		this.extractDebarbEntity = extractDebarbEntity;
	}

	public List<ExtractDebarbEntity> getCompanyDebarbList() {
		return companyDebarbList;
	}

	public void setCompanyDebarbList(List<ExtractDebarbEntity> companyDebarbList) {
		this.companyDebarbList = companyDebarbList;
	}

	public List<ExtractDebarbEntity> getExtractDebarbList() {
		return extractDebarbList;
	}

	public void setExtractDebarbList(List<ExtractDebarbEntity> extractDebarbList) {
		this.extractDebarbList = extractDebarbList;
	}

	public Date getBidDate() {
		return bidDate;
	}

	public void setBidDate(Date bidDate) {
		this.bidDate = bidDate;
	}

	public ExpertInfoService getExpertInfoService() {
		return expertInfoService;
	}

	public void setExpertInfoService(ExpertInfoService expertInfoService) {
		this.expertInfoService = expertInfoService;
	}

	public VoiceProjectService getVoiceProjectService() {
		return voiceProjectService;
	}

	public void setVoiceProjectService(VoiceProjectService voiceProjectService) {
		this.voiceProjectService = voiceProjectService;
	}

	public AppointsExpertsService getAppointsExpertsService() {
		return appointsExpertsService;
	}

	public void setAppointsExpertsService(AppointsExpertsService appointsExpertsService) {
		this.appointsExpertsService = appointsExpertsService;
	}

	public AppraiseService getAppraiseService() {
		return appraiseService;
	}

	public void setAppraiseService(AppraiseService appraiseService) {
		this.appraiseService = appraiseService;
	}

	public UserService getUserService() {
		return userService;
	}

	public void setUserService(UserService userService) {
		this.userService = userService;
	}

	public SmsRecordService getSmsRecordService() {
		return smsRecordService;
	}

	public void setSmsRecordService(SmsRecordService smsRecordService) {
		this.smsRecordService = smsRecordService;
	}

	public TemplateService getTemplateService() {
		return templateService;
	}

	public void setTemplateService(TemplateService templateService) {
		this.templateService = templateService;
	}

	public VoiceService getVoiceService() {
		return voiceService;
	}

	public void setVoiceService(VoiceService voiceService) {
		this.voiceService = voiceService;
	}

	public ProjectChangesEntity getProjectChange() {
		return projectChange;
	}

	public void setProjectChange(ProjectChangesEntity projectChange) {
		this.projectChange = projectChange;
	}

	public List<ProjectChangesEntity> getProjectChangeList() {
		return projectChangeList;
	}

	public void setProjectChangeList(List<ProjectChangesEntity> projectChangeList) {
		this.projectChangeList = projectChangeList;
	}

	public TemplateEntity getTemplateEntity() {
		return templateEntity;
	}

	public void setTemplateEntity(TemplateEntity templateEntity) {
		this.templateEntity = templateEntity;
	}

	public static void main(String[] args) {
		if(0<0){
			System.out.println("2222");
		}else{
			System.out.println("111");
		}
	}
}
