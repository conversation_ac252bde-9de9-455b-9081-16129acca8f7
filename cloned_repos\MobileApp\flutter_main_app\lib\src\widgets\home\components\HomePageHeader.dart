///***
/// * 首页 - 图标按钮 & 今日新增
/// */

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import 'package:flutter_main_app/serviceLocator.dart';
import 'package:flutter_main_app/src/constants/theme.dart';
import 'package:flutter_main_app/src/fonts/fonts.dart';

import 'package:flutter_main_app/src/viewModels/home/<USER>';

class HomePageHeaderWidget extends StatelessWidget implements PreferredSizeWidget {
  
  HomePageHeaderWidget({
    Key key,
    @required this.callback,
  }) : preferredSize = Size.fromHeight(217),
    super(key: key);

  /// * 图标按钮点击的回调
  final void Function() callback;

  @override
  final Size preferredSize;

  /// * 第一行图标按钮
  static const List<Map<String, String>> _firstRowIcons = [
    {
      'src': 'assets/images/equipment.png',
      'name': '设备',
    },
    {
      'src': 'assets/images/material.png',
      'name': '材料',
    },
    {
      'src': 'assets/images/operation.png',
      'name': '施工',
    },
    {
      'src': 'assets/images/monitor.png',
      'name': '监理',
    },
  ];

  /// * 第二行图标按钮
  static const List<Map<String, String>> _secondRowIcons = [
    {
      'src': 'assets/images/survey.png',
      'name': '勘察',
    },
    {
      'src': 'assets/images/planning.png',
      'name': '规划',
    },
    {
      'src': 'assets/images/design.png',
      'name': '设计',
    },
    {
      'src': 'assets/images/other.png',
      'name': '其他',
    },
  ];

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider<HomeIndexViewModel>.value(
          value: serviceLocator<HomeIndexViewModel>(),
        ),
      ],
      child: Consumer<HomeIndexViewModel>(
        builder: (context, model, child) {
          return Column(
            children: <Widget>[
              // 第一排按钮
              Padding(
                padding: const EdgeInsets.only(top: 20.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: _buildIconButtons(context, _firstRowIcons),
                ),
              ),
              // 第二排按钮
              Padding(
                padding: const EdgeInsets.only(top: 25.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: _buildIconButtons(context, _secondRowIcons),
                ),
              ),
              // 今日新增
              Padding(
                padding: EdgeInsets.only(top: 20.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: <Widget>[
                    Padding(
                      padding: const EdgeInsets.only(right: 4),
                      child: Icon(
                        FontsHelper.newDataLeftIconData,
                        color: const Color(0xFFEB5E5E),
                        size: 12,
                      ),
                    ),
                    Text(
                      '今日新增:',
                      style: TextStyle(fontSize: 12, color: themeHintTextColor),
                    ),
                    Padding(
                      padding: const EdgeInsets.fromLTRB(5, 0, 10, 5),
                      child: Text(
                        '37319',
                        style: TextStyle(fontSize: themeFontSize, color: Color(0xFFE3504A)),
                      ),
                    ),
                    Text(
                      '条信息',
                      style: TextStyle(fontSize: 12, color: themeHintTextColor),
                    ),
                    Padding(
                      padding: const EdgeInsets.fromLTRB(4, 0, 0, 0),
                      child: Icon(
                        FontsHelper.newDataRightIconData,
                        color: Color(0xFFEB5E5E),
                        size: 12,
                      ),
                    ),
                  ],
                ),
              ),          
            ],
          );
        },
      ),
    );
  }

  ///***
  /// * 按行生成图标按钮列表
  /// *
  /// * @param {BuildContext} context
  /// * @param {List<Map<String, String>>} icons: 图标预设数据
  /// * @parameter {String} icons[i]['src']: 图标资源地址
  /// * @parameter {String} icons[i]['name']: 图标中文名称
  /// *
  /// * @return {List<Widget>} iconButtons: 图标按钮列表
  /// */
  List<Widget> _buildIconButtons(
    BuildContext context,
    List<Map<String, String>> icons,
  ) {
    final List<Widget> iconButtons = [];

    for (int i = 0; i < icons.length; i++) {
      final Widget iconButton = InkWell(
        onTap: () {
          /// * 要触发父组件传过来的回调函数
          Provider.of<HomeIndexViewModel>(
            context,
            listen: false,
          ).setSpecifiedParam('classifyName', [icons[i]['name']]);

          this.callback();
        },
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 28.0),
          child: Column(
            children: <Widget>[
              SizedBox(
                width: 36.0,
                height: 36.0,
                child: Image.asset(
                  icons[i]['src'],
                  fit: BoxFit.cover,
                ),
              ),
              Padding(
                padding: const EdgeInsets.only(top: 6.0),
                child: Text(
                  icons[i]['name'],
                  style: TextStyle(
                    color: themeTextColor,
                    fontSize: 12.0,
                  ),
                ),
              )
            ],
          ),
        ),
      );

      iconButtons.add(iconButton);
    }

    return iconButtons;
  }

}
