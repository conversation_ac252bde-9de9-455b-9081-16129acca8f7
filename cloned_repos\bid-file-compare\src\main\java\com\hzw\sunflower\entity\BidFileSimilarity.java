package com.hzw.sunflower.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.hzw.sunflower.common.BaseBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <p>
 * 投标文件相似度对比
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-12
 */
@Getter
@Setter
@TableName("t_bid_file_similarity")
@ApiModel(value = "BidFileSimilarity对象", description = "投标文件相似度对比表")
public class BidFileSimilarity extends BaseBean implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("标段ID")
    @TableField("section_id")
    private Long sectionId;

    @ApiModelProperty("供应商ID")
    @TableField("company_id")
    private Long companyId;

    @ApiModelProperty("解析状态 1.对比中 2.对比完成")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Integer analysisStatus;

    @ApiModelProperty("招标阶段（2：默认，1：第一轮）")
    @TableField("bid_round")
    private Integer bidRound;

    @ApiModelProperty("文件相似度")
    @TableField("similarity")
    private String similarity;
}
