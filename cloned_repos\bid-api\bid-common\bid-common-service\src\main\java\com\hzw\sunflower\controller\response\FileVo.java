package com.hzw.sunflower.controller.response;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.hzw.sunflower.dto.OssFileDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * OSS附件 返回实体
 *
 * <AUTHOR>
 * @version 1.0.0 2021-04-12
 */
@ApiModel(description = "OSS附件 ")
@Data
public class FileVo  {
    @ApiModelProperty(value = "主键", position = 1)

    private Long id;

    @ApiModelProperty(value = "OSS文件key", position = 2)
    private String ossFileKey;

    @ApiModelProperty(value = "OSS文件名称", position = 3)
    private String ossFileName;
}