package com.hzw.sunflower.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hzw.sunflower.controller.request.CaDataReq;
import com.hzw.sunflower.controller.response.CaDataVo;
import com.hzw.sunflower.entity.DepartProject;

import com.hzw.sunflower.controller.response.ProcessAddress;
import com.hzw.sunflower.entity.JwtUser;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/3/9 15:55 星期四
 */

public interface CaDataService {


    IPage<CaDataVo> findInfoByCondition(CaDataReq req, JwtUser jwtUser);

    List<DepartProject> getDeptIds();


    List<ProcessAddress> getRelatedProjectAddress();

    List<CaDataVo> exportCaList(CaDataReq req, JwtUser jwtUser);

    Map<String, Object> getCaBidOpeningList();
}
