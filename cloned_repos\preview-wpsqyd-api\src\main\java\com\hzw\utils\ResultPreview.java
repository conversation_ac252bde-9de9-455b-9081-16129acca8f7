package com.hzw.utils;

import org.springframework.http.HttpStatus;

import java.io.Serializable;

/**
 * 结果数据封装
 *
 * @param <T> t
 * <AUTHOR>
 * @version 1.0.0 2021-03-31
 */
public class ResultPreview<T> implements Serializable {
    private static final long serialVersionUID = -7857956965424828813L;

    private boolean flag = true;

    private Integer code;

    private String msg;

    private String exceptionMsg;

    private T data;

    /**
     * 返回成功结果数据
     *
     * @return 成功结果数据
     */
    public static <T> ResultPreview<T> ok() {
        return buildOkResultPreview("成功", null);
    }


    /**
     * 返回成功结果数据
     *
     * @param data 返回数据
     * @return 成功结果数据
     */


    public static <T> ResultPreview<T> ok(T data) {
        return buildOkResultPreview("成功", data);
    }

    /**
     * 返回成功结果数据
     *
     * @param msg  成功消息
     * @param data 返回数据
     * @return 成功结果数据
     */
    public static <T> ResultPreview<T> ok(String msg, T data) {
        return buildOkResultPreview(msg, data);
    }

    /**
     * 返回失败结果数据
     *
     * @return 失败结果数据
     */
    public static <T> ResultPreview<T> failed() {
        return buildFailedResultPreview("失败", null);
    }

    /**
     * 返回失败结果数据
     *
     * @param msg 失败消息
     * @return 失败结果数据
     */
    public static <T> ResultPreview<T> failed(String msg) {
        return buildFailedResultPreview(msg, null);
    }

    /**
     * 返回失败结果数据
     *
     * @param msg 失败消息
     * @return 失败结果数据
     */
    public static <T> ResultPreview<T> failed(Integer code, String msg) {
        return buildFailedResultPreview(code,msg, null);
    }

    /**
     * 返回失败结果数据
     *
     * @param msg  失败消息
     * @param data 返回数据
     * @return 失败结果数据
     */
    public static <T> ResultPreview<T> failed(String msg, T data) {
        return buildFailedResultPreview(msg, data);
    }


    /**
     * 返回成功或失败的结果数据
     *
     * @param isOk 否成功是
     * @return 结果数据
     */
    public static <T> ResultPreview<T> okOrFailed(boolean isOk) {
        if (isOk) {
            return ResultPreview.ok();
        } else {
            return ResultPreview.failed();
        }
    }

    /**
     * 创建成功结果
     *
     * @param msg  成功消息
     * @param data 返回数据
     * @return 结果数据
     */
    private static <T> ResultPreview<T> buildOkResultPreview(String msg, T data) {
        ResultPreview<T> ResultPreview = new ResultPreview<>();
        return ResultPreview.setFlag(true).setCode(HttpStatus.OK.value()).setMsg(msg).setData(data);
    }

    /**
     * 创建失败结果
     *
     * @param msg  失败消息
     * @param data 返回数据
     * @return 结果数据
     */
    private static <T> ResultPreview<T> buildFailedResultPreview(String msg, T data) {
        ResultPreview<T> ResultPreview = new ResultPreview<>();
        return ResultPreview.setFlag(false).setCode(HttpStatus.INTERNAL_SERVER_ERROR.value()).setMsg(msg).setData(data);
    }

    /**
     * 创建失败结果
     *
     * @param msg  失败消息
     * @param data 返回数据
     * @return 结果数据
     */
    private static <T> ResultPreview<T> buildFailedResultPreview(Integer code, String msg, T data) {
        ResultPreview<T> ResultPreview = new ResultPreview<>();
        return ResultPreview.setFlag(false).setCode(code).setMsg(msg).setData(data);
    }


    /**
     * 获取是否成功标识
     *
     * @return 是否成功标识
     */
    public boolean getFlag() {
        return flag;
    }

    /**
     * 设置是否成功标识
     *
     * @param flag 是否成功标识
     * @return 结果数据
     */
    public ResultPreview<T> setFlag(boolean flag) {
        this.flag = flag;
        return this;
    }

    /**
     * 获取成功或错误的编码
     *
     * @return 成功或错误的编码
     */
    public Integer getCode() {
        return code;
    }

    /**
     * 设置成功或错误的编码
     *
     * @param code 成功或错误的编码
     * @return 结果数据
     */
    public ResultPreview<T> setCode(Integer code) {
        this.code = code;
        return this;
    }

    /**
     * 获取返回的提示消息
     *
     * @return 返回的提示消息
     */
    public String getMsg() {
        return msg;
    }

    /**
     * 设置返回的提示消息
     *
     * @param msg 返回的提示消息
     * @return 结果数据
     */
    public ResultPreview<T> setMsg(String msg) {
        this.msg = msg;
        return this;
    }

    /**
     * 获取返回的异常消息
     *
     * @return 返回的异常消息
     */
    public String getExceptionMsg() {
        return exceptionMsg;
    }

    /**
     * 设置返回的异常消息
     *
     * @param exceptionMsg 返回的异常消息
     * @return 结果数据
     */
    public ResultPreview<T> setExceptionMsg(String exceptionMsg) {
        this.exceptionMsg = exceptionMsg;
        return this;
    }

    /**
     * 获取返回的数据对象
     *
     * @return 返回的数据对象
     */
    public T getData() {
        return data;
    }

    /**
     * 设置返回的数据对象
     *
     * @param data 返回的数据对象
     * @return 结果数据
     */
    public ResultPreview<T> setData(T data) {
        this.data = data;
        return this;
    }
}
