package com.hzw.sunflower.service.impl;

import cn.hutool.core.convert.Convert;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzw.ncc.client.api.NccApiClient;
import com.hzw.sunflower.constant.CommonConstants;
import com.hzw.sunflower.constant.GeneralConstants;
import com.hzw.sunflower.constant.ReturnCodeConstants;
import com.hzw.sunflower.constant.constantenum.EntrustUserType;
import com.hzw.sunflower.constants.NccConstants;
import com.hzw.sunflower.constants.NccPushTypeEnum;
import com.hzw.sunflower.controller.project.response.ProjectBidSectionVO;
import com.hzw.sunflower.controller.response.OpenCompanyVo;
import com.hzw.sunflower.dao.ProjectMapper;
import com.hzw.sunflower.dto.ProjectInfoDTO;
import com.hzw.sunflower.dto.ProjectSectionGroupDTO;
import com.hzw.sunflower.dto.ncc.ProjectDto;
import com.hzw.sunflower.entity.NccBondPushLog;
import com.hzw.sunflower.entity.Project;
import com.hzw.sunflower.entity.ProjectBidSection;
import com.hzw.sunflower.entity.ProjectEntrPersNum;
import com.hzw.sunflower.service.*;
import com.hzw.sunflower.util.BeanListUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.StringJoiner;

/**
 * 项目表 Service接口实现
 *
 * <AUTHOR>
 * @version 1.0.0 2021-04-07
 */
@Service
@Slf4j
public class ProjectServiceImpl extends ServiceImpl<ProjectMapper, Project> implements ProjectService {

    @Autowired
    private ProjectEntrPersNumService entrPersNumService;
    @Autowired
    private ProjectEntrustUserService entrustUserService;

    @Autowired
    private CommonOpenService commonOpenService;

    @Autowired
    private NccApiClient nccApiClient;

    @Autowired
    private NccBondPushLogService pushLogService;

    @Autowired
    private CommonProjectRelevantService commonProjectRelevantService;


    /**
     * 根据主键ID查询项目表 信息
     *
     * @param id 主键ID
     * @return 项目表 信息
     */
    @Override
    public Project getProjectById(Long id) {
        return this.getById(id);
    }

    /**
     * 根据主键ID查询项目表 信息
     *
     * @param id 主键ID
     * @return 项目表 信息
     */
    @Override
    public Boolean deleteProjectById(Long id) {
        return this.baseMapper.deleteById(id)>0?true:false;
    }


    /**
     * 修改项目表 信息
     *
     * @param project 项目表 信息
     * @return 是否成功
     */
    @Override
    public Boolean updateProject(Project project) {
        return this.updateById(project);
    }


    /**
     * 项目转被委托人(代理机构)-项目冗余字段处理
     *
     * @param id 主键ID
     * @return 是否成功
     */
    @Override
    public Boolean updateAgentProjectRedundantFieldDealWith(Long id) {
        Project project = new Project();
        project.setId(id);
        return handlerAgentRedundancyField(id, project,true);
    }

    /**
     * 项目转委托人(招标人)-项目冗余字段处理
     *
     * @param id 主键ID
     * @return 是否成功
     */
    @Override
    public Boolean updateTendererProjectRedundantFieldDealWith(Long id) {
        Project project = new Project();
        project.setId(id);
        return handlerTendererRedundancyField(id, project);
    }

    /**
     * 项目新建修改-项目冗余字段处理
     *
     * @param id 主键ID
     * @return 是否成功
     */
    @Override
    public Boolean saveOrUpdateProjectRedundantFieldDealWith(Long id,Boolean isNewAddOrChangeEntrust) {
        Project project = new Project();
        project.setId(id);

        // 项目编号处理
        QueryWrapper<ProjectEntrPersNum> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("project_id", id);
        List<ProjectEntrPersNum> persNums = entrPersNumService.list(queryWrapper);
        if (null != persNums && persNums.size() > 0) {
            StringJoiner joiner = new StringJoiner(GeneralConstants.SPLICING_PARAMETER_METHOD);
            persNums.forEach(item -> {
                joiner.add(item.getProjectNumber());
            });
            project.setProjectNumber(joiner.toString());
        } else {
            log.error("没有委托编号，数据错误");
        }

        // 处理招标人冗余字段
        Boolean redundancyField = handlerTendererRedundancyField(id, project);
        if (!redundancyField) {
            return false;
        }
        // 处理代理机构冗余字段
        Boolean agentRedundancyField = handlerAgentRedundancyField(id, project,isNewAddOrChangeEntrust);
        return agentRedundancyField;
    }

    @Override
    public Integer deleteApplyInfoBySubId(List<Long> subList) {
        return this.baseMapper.deleteApplyInfoBySubId(subList);
    }

    @Override
    public List<Long> findShareProject(Integer isShare, List<Long> ids) {
        return this.baseMapper.findShareProject(isShare,ids);
    }

    @Override
    public void deleteProjectBidDoc(ProjectBidSection section) {
        List<Long> docId = this.baseMapper.findDocId(section);
        if(docId != null && docId.size()>0) {
            this.baseMapper.deleteProjectBidDocRelation(docId);
            this.baseMapper.deleteProjectBidDoc(docId);
        }
    }

    @Override
    public void deleteProjectBidNotice(ProjectBidSection section) {
        List<Long> noticeId = this.baseMapper.findNoticeId(section);
        if(noticeId != null && noticeId.size()>0) {
            this.baseMapper.deleteProjectBidNoticeRelation(noticeId);
            this.baseMapper.deleteProjectBidNotice(noticeId);
        }
    }

    /**
     * 删除套信息
     * @param projectId
     * @param sectionId
     */
    @Override
    public void deleteSectionGroup(Long projectId, Long sectionId) {
        //根据项目id、标段id查询套信息
        List<ProjectSectionGroupDTO> groups =  this.baseMapper.getSectionGroup(projectId,sectionId);
        for(ProjectSectionGroupDTO g:groups){
            String sectionStr = "";
            String groupStr = "";
            String[] groupInfo = g.getGroupInfo().split("、");
            String[] sectionIds = g.getSectionIds().split("、");
            for(int i = 0;i<sectionIds.length;i++){
                if(!sectionIds[i].equals(sectionId.toString())){
                    sectionStr += sectionIds[i]+"、";
                    groupStr +=groupInfo[i]+"、";
                }
            }
            if(sectionStr.equals("")){
                //删除套信息
                this.baseMapper.deleteGroupById(g.getId());
            }else{
                //修改套信息
                ProjectSectionGroupDTO group = BeanListUtil.convert(g,ProjectSectionGroupDTO.class);
                group.setGroupInfo(groupStr.substring(0,groupStr.length()-1));
                group.setSectionIds(sectionStr.substring(0,sectionStr.length()-1));
                this.baseMapper.updateGroupInfo(group);
            }
        }

    }

    /**
     * 根据项目id、标段id查询套信息
     * @param projectId
     * @param sectionId
     * @return
     */
    @Override
    public List<ProjectSectionGroupDTO> getSectionGroup(Long projectId, Long sectionId) {
        return this.baseMapper.getSectionGroup(projectId,sectionId);
    }

    /**
     * 获取最大的临时国际表编号
     * @return
     */
    @Override
    public Integer getMaxTempInternational() {
        return this.baseMapper.getMaxTempInternational();
    }

    @Override
    public List<ProjectBidSectionVO> listDoc(Long projectId) {
        return this.baseMapper.listDoc(projectId);
    }

    /**
     * 推送项目到ncc
     * @param projectId
     */
    @Override
    public void pushProjectNcc(Long projectId,Integer packageNumber) {
        /*ScheduledExecutorService scheduledThreadPool = Executors.newScheduledThreadPool(5);
        scheduledThreadPool.schedule(new Runnable() {
            @Override
            public void run() {*/
                ProjectInfoDTO project = commonProjectRelevantService.getProjectUserInfo(projectId);
                ProjectDto dto = new ProjectDto();
                String  billId = "";
                if(packageNumber != null){
                    billId = NccConstants.NCC_PROJECT_KEY+projectId+"_"+packageNumber;
                    dto.setCode(project.getPurchaseNumber()+"/"+packageNumber);
                    dto.setName(project.getPurchaseNumber()+"/"+packageNumber);
                }else {
                    billId = NccConstants.NCC_PROJECT_KEY+projectId;
                    dto.setCode(project.getPurchaseNumber());
                    dto.setName(project.getPurchaseNumber());
                }
                dto.setBillId(billId);
                dto.setMemo(project.getUserName());
                dto.setPkOrg("14");
                NccBondPushLog pushLog = new NccBondPushLog();
                pushLog.setPushType(NccPushTypeEnum.PROJECT.getType());
                pushLog.setRequestData(JSONObject.toJSONString(dto));
                pushLog.setBillId(billId);
//                String nccResult = nccApiClient.syncProject(dto);
//                JSONObject jsonObject = JSONObject.parseObject(nccResult);
//                if (StringUtils.isNotBlank(nccResult) && ((ReturnCodeConstants.COMMON_SUCCESS_CODE).equals(jsonObject.getString("code")))) {
//                    pushLog.setPushResult(CommonConstants.YES);
//                } else {
//                    pushLog.setPushResult(CommonConstants.NO2);
//                }
                pushLog.setPushResult(CommonConstants.NO2);
                pushLogService.save(pushLog);
           // }
      //  }, 3, TimeUnit.SECONDS);
    }

    /**
     * 根据项目id查询租户部门id
     * @param projectId
     * @return
     */
    @Override
    public Long queryTenantInfoByProject(Long projectId) {
        return this.baseMapper.queryTenantInfoByProject(projectId);
    }

    @Override
    public Project getProjectBySectionId(Long sectionId) {
        return this.baseMapper.getProjectBySectionId(sectionId);
    }

    /**
     * 处理代理机构冗余字段
     *
     * @param id
     * @param project
     */
    public Boolean handlerAgentRedundancyField(Long id, Project project,Boolean isNewAddOrChangeEntrust) {
        //被委托人
        List<Map<String, String>> entrustedPerson = entrustUserService.queryPrincipalByIds(id, EntrustUserType.ENTRUSTED_PERSON.getType());
        if (null != entrustedPerson && entrustedPerson.size() > 0) {
            if(isNewAddOrChangeEntrust){
                // 查询单位
                OpenCompanyVo company = commonOpenService.getCompanyInfoById(Convert.toLong(entrustedPerson.get(0).get("companyId")));
                project.setAgentCompany(company.getCompanyName());
                // 人员和手机
                StringJoiner agentChargeName = new StringJoiner(GeneralConstants.SPLICING_PARAMETER_METHOD);
                StringJoiner agentChargePhone = new StringJoiner(GeneralConstants.SPLICING_PARAMETER_METHOD);
                entrustedPerson.forEach(item -> {
                    agentChargeName.add(item.get("userName"));
                    agentChargePhone.add(item.get("userPhone"));
                });
                project.setAgentChargeName(agentChargeName.toString());
                project.setAgentChargePhone(agentChargePhone.toString());
            }

        } else {
            log.error("查询不到被委托人，数据问题！");
        }
        int update = this.baseMapper.updateById(project);
        if (update > 0) {
            return true;
        }
        return false;
    }

    /**
     * 处理招标人冗余字段
     *
     * @param id
     * @param project
     */
    public Boolean handlerTendererRedundancyField(Long id, Project project) {
        // 委托人
        List<Map<String, String>> principals = entrustUserService.queryPrincipalByIds(id, EntrustUserType.PRINCIPAL.getType());
        if (null != principals && principals.size() > 0) {
            // 查询单位
            OpenCompanyVo company = commonOpenService.getCompanyInfoById(Convert.toLong(principals.get(0).get("companyId")));
            project.setPrincipalCompany(company.getCompanyName());
            // 人员和手机
            StringJoiner principalChargeName = new StringJoiner(GeneralConstants.SPLICING_PARAMETER_METHOD);
            StringJoiner principalChargePhone = new StringJoiner(GeneralConstants.SPLICING_PARAMETER_METHOD);
            principals.forEach(item -> {
                principalChargeName.add(item.get("userName"));
                principalChargePhone.add(item.get("userPhone"));
            });
            project.setPrincipalChargeName(principalChargeName.toString());
            project.setPrincipalChargePhone(principalChargePhone.toString());
        } else {
            log.error("查询不到委托人，数据问题！");
        }
        int update = this.baseMapper.updateById(project);
        if (update > 0) {
            return true;
        }
        return false;
    }
}
