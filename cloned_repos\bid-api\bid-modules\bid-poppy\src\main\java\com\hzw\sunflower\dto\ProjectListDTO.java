package com.hzw.sunflower.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2021年04月12日 13:50
 * @Version 1.0
 */
@Data
@ApiModel(description = "项目列表DTO")
public class ProjectListDTO implements Serializable {

    @ApiModelProperty(value = "项目主键")
    private Long id;
    @ApiModelProperty(value = "项目名称")
    private String projectName;
    @ApiModelProperty(value = "采购名称")
    private String purchaseName;
    @ApiModelProperty(value = "委托负责人")
    private String principalChargeName;
    @ApiModelProperty(value = "采购编号")
    private String purchaseNumber;
    @ApiModelProperty(value = "异常原因")
    private String refuseReason;
    @ApiModelProperty(value = "最原始项目ID")
    private Long baseProjectId;
    @ApiModelProperty(value = "是否重新招标过 0:未 1:已")
    private Integer reTender;
    @ApiModelProperty(value = "用户ID")
    private Long userId;
    @ApiModelProperty(value = "部门ID")
    private Long departmentId;

    @ApiModelProperty(value = "包件状态")
    private String packagesStatus;
    @ApiModelProperty(value = "包件状态Code")
    private String packagesStatusCode;
    @ApiModelProperty(value = "包件bidround")
    private List<String> packagesBidRound;
    @ApiModelProperty(value = "包件bidround")
    private  List<String> packagesBidRoundOne;
    @ApiModelProperty(value = "包件bidround")
    private  List<String> packagesBidRoundTwo;
    @ApiModelProperty(value = "包件数量，0-无包，1-有")
    private String packageSegmentStatus;
    @ApiModelProperty(value = "项目状态")
    private Map<String, Object> projectStatus;
    @ApiModelProperty(value = "项目状态code")
    private String status;
    @ApiModelProperty(value = "代理单位")
    private String agentCompany;
    @ApiModelProperty(value = "委托单位")
    private String principalCompany;
    @ApiModelProperty(value = "项目编号")
    private String projectNumber;
    @ApiModelProperty(value = "代理机构负责人")
    private String agentChargeName;
    @ApiModelProperty(value = "包件数量")
    private int segmentNum;
    @ApiModelProperty(value = "报名待处理供应商数量")
    private int applyInfoNum;
    @ApiModelProperty(value = "创建时间")
    private String createdTime;
    @ApiModelProperty(value = "递交截止时间")
    private List<String> submitEndTime;
    @ApiModelProperty(value = "分摊确认状态")
    private Integer confirmStatus;
    @ApiModelProperty(value = "委托金额")
    private BigDecimal entrustedAmount;
    @ApiModelProperty(value = "权限类型 1查看 2查看编辑")
    private Integer roleType;
    @ApiModelProperty(value = "项目受理状态")
    private Integer agentStatus;
    @ApiModelProperty(value = "项目来源 1代理机构 2招标人")
    private Integer projectSource;
    @ApiModelProperty(value = "招标人是否是管理员  1是 2否")
    private Integer isAdmin;
    @ApiModelProperty(value = "转委托状态")
    private Integer delegateStatus;
    @ApiModelProperty(value = "已购标数")
    private Integer purchasedBids;
    @ApiModelProperty(value = "业务流程（0全流程线上 1线上+线下开评标 2线上+云开云评 3 全流程线下+线上归档）")
    private Integer operationFlow;

}
