package com.hzw.sunflower.controller.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2021/7/7 15:57
 * @description：招标文件请求体
 * @version: 1.0
 */
@Data
public class DocDataInfoREQ {

    @Valid
    @ApiModelProperty(value = "招标文件信息", position = 1)
    private List<ProjectBidDocREQ> bidDocInfo;

    @ApiModelProperty(value = "附件信息", position = 2)
    private RelevancyAnnexREQ attachmentInfo;

    @ApiModelProperty(value = "是否为重新招标项目(true:是，false:否)", position = 3, required = true)
    @NotNull(message = "是否为重新招标项目值不能为空")
    private Boolean reTender;

    @ApiModelProperty(value = "套ID", position = 4)
    private Long deleteGroupId;

    @ApiModelProperty(value = "是否退款(true:是，false:否)", position = 5, required = true)
    @NotNull(message = "是否退款值不能为空")
    private Boolean refund;

    @ApiModelProperty(value = "待退款标段结合集合", position = 6)
    private List<Long> refundSectionIds;

    @ApiModelProperty(value = "开标一览表信息", position = 7)
    private BidOpenScheduleREQ scheduleREQ;
}
