package com.hzw.system.api.model;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class DictionaryCondition implements Serializable {

    private Long id;
    /**
     * 名称
     */
    private String name;
    /**
     * 编码
     */
    private String code;
    /**
     * 值
     */
    private String value;
    /**
     * 父节点
     */
    private String parentCode;
    /**
     * 是否禁用 0否1是
     */
    private Integer isDisable;
    /**
     * 删除顶级节点 true删除  false不删除
     */
    private Boolean deleteParent = false;
}
