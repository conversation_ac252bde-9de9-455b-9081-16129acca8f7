package com.hzw.sunflower;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.core.env.Environment;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;

import java.net.InetAddress;
import java.net.UnknownHostException;

/**
 * <AUTHOR>
 */
@SpringBootApplication
@MapperScan(basePackages = "com.hzw.sunflower.dao")
@Slf4j
@EnableAsync
@EnableScheduling
public class BidDocToolsApiApplication {


    public static void main(String[] args) {
        ConfigurableApplicationContext applicationContext = SpringApplication.run(BidDocToolsApiApplication.class, args);
        Environment env = applicationContext.getEnvironment();
        String ip = null;
        try {
            ip = InetAddress.getLocalHost().getHostAddress();
        } catch (UnknownHostException e) {
            e.printStackTrace();
        }
        String port = env.getProperty("server.port");
        String path = env.getProperty("server.servlet.context-path");
        if (StrUtil.isEmpty(path)) {
            path = "";
        }
        log.info("\n----------------------------------------------------------\n\t" +
                "Application  is running! Access URLs:\n\t" +
                "Local访问网址: \t\thttp://localhost:" + port + path + "\n\t" +
                "External访问网址: \thttp://" + ip + ":" + port + path + "\n\t" +
                "----------------------------------------------------------");
        String swagger = env.getProperty("swagger.termsOfServiceUrl");
        String urlPrefix = env.getProperty("swagger.urlPrefix");
        log.info("\n----------------------------------------------------------\n\t" +
                "Application_Swagger  is running! Access URLs:\n\t" +
                "Local访问网址: \t\t" + urlPrefix + "localhost:" + port + path + swagger + "\n\t" +
                "External访问网址: \t" + urlPrefix + ip + ":" + port + path + swagger + "\n\t" +
                "----------------------------------------------------------");
        String datasource = env.getProperty("spring.datasource.url");
        log.info("\n----------------------------------------------------------\n\t" +
                "Application_Mysql  is running! Access URLs:\n\t" +
                "数据库连接地址: \t\t" + datasource + "\n\t" +
                "----------------------------------------------------------");
    }
    @Bean
    public BCryptPasswordEncoder dCryptPasswordEncoder() {
        return new BCryptPasswordEncoder();
    }
}
