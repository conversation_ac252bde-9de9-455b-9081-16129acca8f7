package com.hzw.sunflower.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hzw.sunflower.common.BaseController;
import com.hzw.sunflower.common.Paging;
import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.common.annotation.RepeatSubmit;
import com.hzw.sunflower.controller.request.*;
import com.hzw.sunflower.controller.response.*;
import com.hzw.sunflower.entity.*;
import com.hzw.sunflower.entity.condition.BondProjectCondition;
import com.hzw.sunflower.service.*;
import com.hzw.sunflower.util.BeanListUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 异常保证金
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-27
 */
@Api(tags = "异常保证金")
@RestController
@RequestMapping("/bondAbnormal")
public class BondAbnormalController extends BaseController {

    @Autowired
    private BondWaterService bondWaterService;

    @Autowired
    private BondContactRecordsService bondContactRecordsService;

    @Autowired
    private BondCompanyBankService bondCompanyBankService;

    @Autowired
    private BondRefundService bondRefundService;

    @Autowired
    private UserService userService;

    @Autowired
    private CnapsBankCodeService cnapsBankCodeService;

    /**
     * 保证金异常待处理数量（运管处）
     * @return
     */
    @ApiOperation(value = "保证金异常待处理数量")
    @PostMapping("/abnormalWaterCount")
    public Result<Object> abnormalWaterCount() {
        Integer size = bondWaterService.abnormalWaterCount();
        return Result.ok(size);
    }

    /**
     * 异常保证金
     * keywords  付款户名
     * @return
     */
    @ApiOperation(value = "异常保证金")
    @ApiImplicitParam(name = "condition", value = "保证金拆分列表", required = true, dataType = "BondProjectCondition", paramType = "body")
    @PostMapping("/abnormalWaterList")
    public Result<Paging<BondWaterRefundVO>> abnormalWaterList(@RequestBody BondProjectCondition condition) {
        IPage<BondWaterRefundVO> bondSplitList = bondWaterService.abnormalWaterList(condition);
        return Result.ok(Paging.buildPaging(bondSplitList));
    }

    /**
     * 异常保证金更新备注
     * @return
     */
    @ApiOperation(value = "异常保证金更新备注")
    @ApiImplicitParam(name = "req", value = "异常保证金更新备注", required = true, dataType = "BondWaterRemarkReq", paramType = "body")
    @PostMapping("/updateAbnormalWaterRemark")
    public Result<String> updateAbnormalWaterRemark(@RequestBody BondWaterRemarkReq bondWaterRemarkReq) {
        return bondWaterService.updateAbnormalWaterRemark(bondWaterRemarkReq);
    }

    /**
     * 新增编辑联系记录接口
     * businessId
     * businessType
     * companyId
     * contactsUserId
     * contactsUser
     * contactsPhone
     * contactTime
     * content
     * id  编辑的时候
     * @return
     */
    @ApiOperation(value = "新增编辑联系记录接口")
    @ApiImplicitParam(name = "req", value = "新增编辑联系记录接口", required = true, dataType = "BondContactRecordsReq", paramType = "body")
    @PostMapping("/saveContactRecords")
    @RepeatSubmit
    public Result saveContactRecords(@RequestBody BondContactRecordsReq req) {
        BondContactRecords record = BeanListUtil.convert(req,BondContactRecords.class);
        Boolean result = bondContactRecordsService.saveOrUpdate(record);
        return Result.okOrFailed(result);
    }


    /**
     * 查看联系记录
     * businessId
     * businessType
     * @return
     */
    @ApiOperation(value = "查看联系记录")
    @PostMapping("/getContactRecordsList")
    public Result<List<BondContactRecordsVo>> getContactRecordsList(@RequestBody BondContactRecordsReq req) {
        LambdaQueryWrapper<BondContactRecords> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BondContactRecords::getBusinessId,req.getBusinessId())
                .eq(BondContactRecords::getBusinessType,req.getBusinessType());
        List<BondContactRecords> list = bondContactRecordsService.list(queryWrapper);
        List<BondContactRecordsVo> recordsVos = BeanListUtil.convertList(list,BondContactRecordsVo.class);
        recordsVos.forEach(b->{
            User user = userService.getById(b.getCreatedUserId());
            b.setUserName(user.getUserName());
        });
        return Result.ok(recordsVos);
    }



    /**
     * 退还失败异常列表
     * keywords  付款户名
     * @return
     */
    @ApiOperation(value = "退还失败异常列表")
    @ApiImplicitParam(name = "condition", value = "退还失败异常列表", required = true, dataType = "BondProjectCondition", paramType = "body")
    @PostMapping("/payFailList")
    public Result<Paging<RefundListVo>> payFailList(@RequestBody BondProjectCondition condition) {
        IPage<RefundListVo> list = bondRefundService.payFailList(condition);
        return Result.ok(Paging.buildPaging(list));
    }



    /**
     * 根据companyId去供应商行号维护表中查询行号信息
     * companyId
     * refundId
     * @return
     */
    @ApiOperation(value = "根据companyId去供应商行号维护表中查询行号信息")
    @ApiImplicitParam(name = "req", value = "companyId", required = true, dataType = "ProjectSectionReq", paramType = "body")
    @PostMapping("/getCompanyBankInfo")
    public Result<List<BondCompanyBankVo>> getCompanyBankInfo(@RequestBody ProjectSectionReq req) {
        List<BondCompanyBankVo> bank = bondCompanyBankService.getPayFileCompanyInfo(req.getCompanyId(),req.getRefundId());
        return Result.ok(bank);
    }



    /**
     * 编辑行号信息
     * [{
     *    id
     *    refundId
     *    companyName
     *    companyAccount
     *    companyBankDeposit
     * }]
     * @return
     */
    @ApiOperation(value = "编辑行号信息")
    @ApiImplicitParam(name = "req", value = "编辑行号信息", required = true, dataType = "BondCompanyBankReq", paramType = "body")
    @PostMapping("/saveCompanyBankInfo")
    @RepeatSubmit
    public Result saveCompanyBankInfo(@RequestBody List<BondCompanyBankReq> req) {
        Boolean result = bondRefundService.saveCompanyBankInfo(req);
        return Result.okOrFailed(result);
    }

    /**
     * 确认关联补全行号
     * [{
     *    id
     *    refundId
     *    companyName
     *    companyAccount
     *    companyBankDeposit
     * }]
     * @return
     */
    @ApiOperation(value = "补全行号信息")
    @ApiImplicitParam(name = "req", value = "编辑行号信息", required = true, dataType = "BondCompanyBankReq", paramType = "body")
    @PostMapping("/complementBankInfo")
    @RepeatSubmit
    public Result<String> complementBankInfo(@RequestBody List<BondCompanyBankReq> req) {
        return bondRefundService.complementBankInfo(req);
    }


    /**
     * 根据行名去南京银行行号映射表中查询行名行号信息
     * @return
     */
    @ApiOperation(value = "根据行名去南京银行行号映射表中查询行名行号信息")
    @ApiImplicitParam(name = "req", required = true, dataType = "ProjectSectionReq", paramType = "body")
    @PostMapping("/getBankInfoByName")
    public Result<Paging<CnapsBankCode>> getBankInfoByName(@RequestBody CnapsBankCodeReq req) {
        IPage<CnapsBankCode> bankCodePage = cnapsBankCodeService.getBankInfoByName(req);
        return Result.ok(Paging.buildPaging(bankCodePage));
    }
}
