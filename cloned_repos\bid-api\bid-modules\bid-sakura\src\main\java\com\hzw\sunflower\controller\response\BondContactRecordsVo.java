package com.hzw.sunflower.controller.response;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 联系记录
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-27
 */
@Data
@ApiModel(value = "BondWater对象", description = "联系记录")
public class BondContactRecordsVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "业务id")
    private Long businessId;

    @ApiModelProperty(value = "业务类型：1：异常保证金  2：付款失败")
    private Integer businessType;

    @ApiModelProperty(value = "供应商id")
    private Long companyId;

    @ApiModelProperty(value = "联系人id")
    private Long contactsUserId;

    @ApiModelProperty(value = "联系人名称")
    private String contactsUser;

    @ApiModelProperty(value = "联系电话")
    private String contactsPhone;

    @ApiModelProperty(value = "联系时间")
    private Date contactTime;

    @ApiModelProperty(value = "联系内容")
    private String content;

    @ApiModelProperty(value = "创建人id")
    private Long createdUserId;

    @ApiModelProperty(value = "操作人")
    private String userName;

}
