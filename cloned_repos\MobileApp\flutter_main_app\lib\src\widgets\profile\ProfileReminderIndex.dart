///***
/// * 我的 - 我的跟踪 - 入口页
/// */

import 'package:flutter/material.dart';

import 'package:flutter_main_app/src/components/CustomAppBar.dart';
import 'package:flutter_main_app/src/components/CustomSafeArea.dart';

class ProfileReminderIndex extends StatefulWidget {
  const ProfileReminderIndex({Key key}) : super(key: key);

  /// * 路由名称
  static const String _routeName = 'ProfileReminderIndex';
  static String get routeName => _routeName;

  @override
  _ProfileReminderIndexState createState() => _ProfileReminderIndexState();
}

class _ProfileReminderIndexState extends State<ProfileReminderIndex> {

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: '我的跟踪',
      ),
      body: CustomSafeArea(
        child: ListView(
          physics: BouncingScrollPhysics(),
          children: <Widget>[],
        ),
      ),
    );
  }
}
