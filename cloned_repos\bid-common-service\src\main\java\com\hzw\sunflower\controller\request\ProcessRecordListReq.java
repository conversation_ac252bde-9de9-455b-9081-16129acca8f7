package com.hzw.sunflower.controller.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @ClassName:ProcessRecordListReq
 * @Auther: lijinxin
 * @Description:
 * @Date: 2024/1/5 19:47
 * @Version: v1.0
 */
@Data
public class ProcessRecordListReq {
    @ApiModelProperty(value = "业务code （winBULLETIN公告流程code，bidwinpeople确认中标人流程code，winNotice通知书流程code）")
    private String businessCode;


    @ApiModelProperty(value = "业务主键")
    private String businessId;
}
