package com.hzw.common.core.utils.file;

import lombok.extern.slf4j.Slf4j;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;

/**
 * <AUTHOR>
 */

@Slf4j
public class PictureUtil {

    
    /**  
     * 导入本地图片到缓冲区  
     */  
    public BufferedImage loadImageLocal(String imgName) {
        try {  
            return ImageIO.read(new File(imgName));
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;  
    }

    /**
     * 导入本地图片到缓冲区
     */
    public BufferedImage loadImageLocal(InputStream in) {
        try {
            return ImageIO.read(in);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    public BufferedImage modifyImagetogeter(BufferedImage b, BufferedImage d, BufferedImage c) {
        Graphics2D g   = null;
        try {  
            int w = b.getWidth();  
            int h = b.getHeight();       
            g = d.createGraphics();  
            
            g.drawImage(b, 30, 195, w, h, null);
            
            g.drawImage(c, 87, 260, 120, 120, null);  
            g.dispose();  
        } catch (Exception e) {
            e.printStackTrace();
        }
  
        return d;  
    } 
    
    /**  
     * 生成新图片到本地  
     */  
    public void writeImageLocal(String newImage, BufferedImage img) {
        if (newImage != null && img != null) {  
            try {  
                File outputfile = new File(newImage);
               ImageIO.setUseCache(false);
                ImageIO.write(img, "png", outputfile);
            } catch (IOException e) {
                e.printStackTrace();
            }
        }  
    } 
    
   public BufferedImage addPicture(String fontPath, String code) {
	 try {
		 BufferedImage bi = new BufferedImage(220, 32, BufferedImage.TYPE_INT_RGB);
         // INT精确度达到一定,RGB三原色，高度70,宽度150  // 得到图片缓冲区
		 
	        Graphics2D graphics = (Graphics2D) bi.getGraphics();
            // 得到它的绘制环境(这张图片的笔)
	        bi = graphics.getDeviceConfiguration().createCompatibleImage(220, 32, Transparency.TRANSLUCENT);
	        graphics.dispose();
	        graphics = (Graphics2D)bi.getGraphics();
	        graphics.setColor(new Color(83,111,236));
	        graphics.fillRect(0, 0, 220, 32); 
	        graphics.setColor(Color.WHITE);

	        graphics.setFont(loadStyleFont(fontPath,Font.BOLD,25));
	        graphics.drawString("推荐码：", 5, 25);
	        graphics.drawString(code, 120, 25);
	        
	        return bi;

	} catch (Exception e) {
		e.printStackTrace();
	} 
	   return null;
   }

    public BufferedImage addPicture(InputStream inputStream, String code) {
        try {
            BufferedImage bi = new BufferedImage(220, 32, BufferedImage.TYPE_INT_RGB);
            // INT精确度达到一定,RGB三原色，高度70,宽度150  // 得到图片缓冲区

            Graphics2D graphics = (Graphics2D) bi.getGraphics();
            // 得到它的绘制环境(这张图片的笔)
            bi = graphics.getDeviceConfiguration().createCompatibleImage(220, 32, Transparency.TRANSLUCENT);
            graphics.dispose();
            graphics = (Graphics2D)bi.getGraphics();
            graphics.setColor(new Color(83,111,236));
            graphics.fillRect(0, 0, 220, 32);
            graphics.setColor(Color.WHITE);

            graphics.setFont(loadStyleFont(inputStream,Font.BOLD,25));
            graphics.drawString("推荐码：", 5, 25);
            graphics.drawString(code, 120, 25);

            return bi;

        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public  Font loadStyleFont(InputStream inputStream ,int style, float fontSize) {

        try{
            Font dynamicFont = Font.createFont(Font.TRUETYPE_FONT, inputStream);
            Font dynamicFontPt =  dynamicFont.deriveFont(style,fontSize);
            return dynamicFontPt;
        }catch(Exception e) {
            e.printStackTrace();
            return new java.awt.Font("宋体", Font.PLAIN, 25);
        }
    }
    // 修改的加载方法
    public  Font loadStyleFont(String fontFileName,int style, float fontSize) {

        try{
            File file = new File(fontFileName);
            if (!file.exists()) {
                log.info("外部字体文件不存在！");
                //文件不存在
                return new java.awt.Font("宋体", Font.PLAIN, 25);
            } else {
                //文件存在！
                log.info("外部字体文件存在！");
            }
            FileInputStream in = new FileInputStream(file);
            Font dynamicFont = Font.createFont(Font.TRUETYPE_FONT, in);
            Font dynamicFontPt =  dynamicFont.deriveFont(style,fontSize);
            in.close();
            return dynamicFontPt;
        }catch(Exception e) {
            e.printStackTrace();
            return new java.awt.Font("宋体", Font.PLAIN, 25);
        }
    }

//    public static void main(String[] args) {
//
//        try {
//            PictureUtil tt = new PictureUtil();
////        String simsunPath = PictureUtil.class.getClassLoader().getResource("font/simsun.ttc").getPath();
////        BufferedImage processDiagram = tt.addPicture(simsunPath, "222");
////            PictureUtil.class.getClassLoader().getResource("font/simsun.ttc").openStream();
//            InputStream resourceAsStream = PictureUtil.class.getClassLoader().getResource("font/simsun.ttc").openStream();
//            BufferedImage processDiagram = tt.addPicture(resourceAsStream,"333");
//            File outFile = new File("333.png");
//            ImageIO.write(processDiagram, "png", outFile);
//        } catch (Exception e) {
//            e.printStackTrace();
//        }// 写图片
//
//    }
    
}
