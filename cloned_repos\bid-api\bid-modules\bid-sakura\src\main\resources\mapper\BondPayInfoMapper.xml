<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzw.sunflower.dao.BondPayInfoMapper">

    <!-- 获取待刷新状态的数据 -->
    <select id="getRefreshData" resultType="com.hzw.sunflower.dto.BondPayInfoDto">
        SELECT
            i.*,d.refund_id
        FROM
            t_bond_pay_info i
        LEFT JOIN t_bond_refund_details d ON d.id = i.refund_detail_id
        LEFT JOIN t_bond_refund r ON r.id = d.refund_id
        WHERE
            i.`status` = 1
          AND (d.`status` = 5 or d.agency_refund_status = 5)
          AND r.`status` = 5
          AND i.is_delete = 0
          AND d.is_delete = 0
          AND r.is_delete = 0
          <if test="refundId != null and refundId != 0 ">
              and r.id = #{refundId}
          </if>
    </select>

    <!-- 获取待刷新状态的批量数据 -->
    <select id="getBatchRefreshData" resultType="com.hzw.sunflower.dto.BondPayInfoDto">
        SELECT
        i.*,d.refund_id
        FROM
        t_bond_pay_info i
        LEFT JOIN t_bond_refund_details d ON d.id = i.refund_detail_id
        LEFT JOIN t_bond_refund r ON r.id = d.refund_id
        WHERE
        i.`status` = 1
        AND (d.`status` = 5 or d.agency_refund_status = 5)
        AND r.`status` = 5
        AND i.is_delete = 0
        AND d.is_delete = 0
        AND r.is_delete = 0
        <if test="list != null and list.size > 0 ">
            and r.id  in
            <foreach collection="list" item="id" separator="," open="(" close=")" index="">
                #{id}
            </foreach>
        </if>
    </select>
</mapper>
