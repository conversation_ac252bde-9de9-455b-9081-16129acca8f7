package com.hzw.sunflower.service;

import com.hzw.sunflower.dto.BidTendererDto;
import com.hzw.sunflower.dto.PurchaseOfTenderDto;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface CommonOpeningAndEvaluationService {

    Boolean saveBidTendererRecord(Long projectId, Long sectionId, Integer bidRound, Integer tempStorage);

    /**
     * 获取开评标投标人列表
     * @param sectionId
     * @param bidRound
     * @return
     */
    List<BidTendererDto> getTendererList(Long sectionId, Integer bidRound);

    /**
     * 更新开评标投标人列表
     * @param tendererDtoList
     * @return
     */
    Boolean updateBidTendererRecord(List<BidTendererDto> tendererDtoList);

    /**
     * 保存或更新投标情况
     * @param list
     * @return
     */
    boolean saveOrupdateTendererRecord(List<PurchaseOfTenderDto> list);
}
