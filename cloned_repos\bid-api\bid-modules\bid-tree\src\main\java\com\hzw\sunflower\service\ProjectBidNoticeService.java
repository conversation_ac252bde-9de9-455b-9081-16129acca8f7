package com.hzw.sunflower.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.controller.request.CommonNoticeREQ;
import com.hzw.sunflower.controller.request.NoticeSectionREQ;
import com.hzw.sunflower.controller.request.QueryPublicNoticeListREQ;
import com.hzw.sunflower.controller.request.UpdateNoticeREQ;
import com.hzw.sunflower.controller.response.NoticeTimeVO;
import com.hzw.sunflower.controller.response.ProjectBidNoticeListVO;
import com.hzw.sunflower.controller.response.ProjectBidNoticeVO;
import com.hzw.sunflower.dto.PersonalMediaDTO;
import com.hzw.sunflower.dto.UpdateNoticeDTO;
import com.hzw.sunflower.entity.JwtUser;
import com.hzw.sunflower.entity.ProjectBidNotice;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;


/**
* 公告招标公告基本服务
*
*/
public interface ProjectBidNoticeService extends IService<ProjectBidNotice> {
    /**
    * 根据条件分页查询列表
    *
    * @param condition 查询条件
    * @return 公告列表
    */
    ProjectBidNoticeListVO findNoticeListPage(QueryPublicNoticeListREQ condition);

    /**
     * 根据条件查询公告信息
     *
     * @param req 通用请求条件
     * @return 公告信息
     */
    Result<ProjectBidNoticeVO> queryEditNoticeInfo(CommonNoticeREQ req);
    /**
     * 根据条件查询查看页面招标公告信息
     *
     * @param req 通用请求条件
     * @return 公告信息
     */
    Result<ProjectBidNoticeVO> queryNoticeInfo(CommonNoticeREQ req);
    /**
     * 根据条件插入公告信息
     *
     * @param noticeDTO 新建公告DTO
     * @return 提示信息，true代表成功，其他失败
     */
    @Transactional(rollbackFor = Exception.class)
    Result<Boolean> insertNoticeInfo(UpdateNoticeDTO noticeDTO);

    /**
     * 根据条件更新公告信息
     *
     * @param updateNoticeDTO 更新公告DTO
     * @return 提示信息，true代表成功，其他失败
     */
    @Transactional(rollbackFor = Exception.class)
    Result<Boolean> updateNoticeInfo(UpdateNoticeDTO updateNoticeDTO);

    /**
     * 发布招标公告
     *
     * @param  notice 招标公告实体
     * @param ids 招标公告关联的包段ID集合
     * @return 提示信息，true代表成功，其他失败
     */
    @Transactional(rollbackFor = Exception.class)
    Boolean updatePublishNotice(ProjectBidNotice notice,List<Long> ids);


    /**
     * 根据条件删除公告
     *
     * @param notice 招标公告实体
     * @param packageIds 招标公告关联的项目包段ID集合
     * @return Result<Boolean>，成功返回true，失败返回false
     */
    @Transactional(rollbackFor = Exception.class)
    Boolean deleteById(ProjectBidNotice notice,List<Long> packageIds);

    /**
     * 根据条件删除公告
     *
     * @param userId 当前用户ID
     * @return List<PersonalMediaDTO> 个人媒体列表
     */
    @Transactional(rollbackFor = Exception.class)
    List<PersonalMediaDTO> initPersonalMedias(Long userId);

    /**
     * 判断包段所对应的公告的发布状态
     *
     * @param packageIds 包段ID的集合
     * @return 全部已发布为true，有任何一个没发布则为false
     */
    Boolean isPublish(List<Long> packageIds, Integer bidRound);

    /**
     * 用于辅助定时任取，查找进度处于审核中和已审核的所有公告
     * @return 全部已发布为true，有任何一个没发布则为false
     */
    List<ProjectBidNotice> findNoticeForScheduleTask();

    /**
     * 根据项目包段状态自动同步项目状态
     *
     * @param projectId 项目ID
     * @return 受影响的记录行数
     */
    @Transactional(rollbackFor = Exception.class)
    Boolean syncProjectStatus(Long projectId);

    /**
     * 判断招标公告是否属于对应状态
     *
     * @param noticeId 招标公告ID
     * @param progressList 在什么进度下的公告是可以操作的
     * @return 招标公告实体
     */
    ProjectBidNotice noticeIsAvailable(Long noticeId,List<Integer> progressList);

    /**
     * 判断所有包段是否可以发布招标公告
     *
     * @param projectId 项目id
     * @return 全部已发布为true，有任何一个没发布则为false
     */
    Boolean isInsertButton(Long projectId);

    /**
     * 新建或修改招标公告信息
     * @param req
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    Result<Boolean> saveOrUpdate(UpdateNoticeREQ req, JwtUser user);

    /**
     * 根据标段ID查询公告信息判断标段状态
     * @param sectionId
     * @return
     */
    Boolean judgeSectionStatusById(Long sectionId);

    /**
     * 根据标段id查询公告信息
     * @param req
     * @return
     */
    List<NoticeTimeVO> getNoticeTime(NoticeSectionREQ req);
}
