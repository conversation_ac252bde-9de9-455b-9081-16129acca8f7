///***
/// * 主体资质 视图模型
/// */

import 'package:flutter/material.dart';

import 'package:flutter_main_app/serviceLocator.dart';
import 'package:flutter_main_app/src/utils/ApiUtils.dart';

import 'package:flutter_main_app/src/viewModels/marketPlayer/MarketPlayerListViewModel.dart';

import 'package:flutter_main_app/src/models/marketPlayer/SubjectQualificationModel.dart';

class SubjectQualificationViewModel extends ChangeNotifier {

  SubjectQualificationViewModel();

  /// * 主要用于获取当前 subjectID
  final MarketPlayerListViewModel _marketPlayerListViewModel = serviceLocator<MarketPlayerListViewModel>();

  /// * 是否正在加载数据
  bool _isLoading = false;

  bool get isLoading => _isLoading;

  set isLoading(bool isLoading) {
    _isLoading = isLoading;

    notifyListeners();
  }

  /// * 主体资质列表
  List<SubjectQualificationModel> _subjectQualificationList = [];

  List<SubjectQualificationModel> get subjectQualificationList => _subjectQualificationList;

  ///***
  /// * 给主体资质赋值
  /// * 同时通知所有订阅者
  /// *
  /// * @param {List<SubjectQualificationModel>} subjectQualificationList
  /// */
  set subjectQualificationList(List<SubjectQualificationModel> subjectQualificationList) {
    _subjectQualificationList = subjectQualificationList;

    notifyListeners();
  }

  ///***
  /// * 获取主体资质列表
  /// */
  Future<void> fetchSubjectQualificationList() async {
    isLoading = true;

    final Map<String, int> params = {
      'subjectID': _marketPlayerListViewModel.subjectID,
    };

    List<SubjectQualificationModel> newSubjectQualificationList =
      await ApiUtils.getSubjectQualification(params);

    if (newSubjectQualificationList != null && newSubjectQualificationList.isNotEmpty) {
      /// * 数据处理
      newSubjectQualificationList = newSubjectQualificationList.map<SubjectQualificationModel>(
        (qualification) {
          if (qualification.expiryDate != null && qualification.expiryDate.length >= 10) {
            qualification.expiryDate = qualification.expiryDate.substring(0, 10);
          }

          return qualification;
        },
      ).toList();
    }

    subjectQualificationList = newSubjectQualificationList;

    isLoading = false;
  }

}
