<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzw.sunflower.dao.BondRelationMapper">

    <!-- 分页查询供应商关注标段保证金关联列表 -->
    <select id="getSectionRelationList" resultType="com.hzw.sunflower.controller.response.BondRelationSectionVo">
        SELECT
            s.*,
            p.purchase_name,
            p.purchase_number,
            p.package_segment_status,
            c.company_name agent_name,
            a.download_flag
        FROM
            t_apply_info a
            LEFT JOIN t_project p ON a.project_id = p.id
            LEFT JOIN t_project_entrust_user eu ON eu.project_id = p.id
            LEFT JOIN t_company c ON c.id = eu.company_id
            LEFT JOIN t_project_bid_section s ON s.id = a.sub_id
        WHERE
            a.is_delete = 0
            AND a.project_id = #{condition.projectId}
            AND a.user_id = #{condition.userId}
            AND eu.type = 1
            AND eu.is_delete = 0
            and a.bid_round = 2
        <!--  and s.abnormal_status = 0  -->
    </select>

    <!-- 校验当前包关联的所有流水均只关联了当前标包 -->
    <select id="checkSectionRelationOnly" resultType="java.lang.Integer">
        SELECT
            count(1)
        FROM
            t_bond_relation
        WHERE
                water_id IN ( SELECT water_id FROM t_bond_relation WHERE section_id = #{sectionId} AND company_id = #{companyId} and is_delete = 0 )
          AND `status` = 2
          AND section_id != #{sectionId}
          and is_delete = 0
          AND company_id = #{companyId}
    </select>

    <!-- 根据项目id、开标状态查询供应商所有待关联标段 已购标 已设置退还保证金 -->
    <select id="getNotRelateSection" resultType="com.hzw.sunflower.controller.response.BondRelationSectionVo">
        SELECT
            s.*,
            p.purchase_name,
            p.purchase_number,
            p.package_segment_status,
            a.download_flag
        FROM
            t_apply_info a
                LEFT JOIN t_project_bid_section s ON s.id = a.sub_id
                LEFT JOIN t_project p ON p.id = a.project_id
                LEFT JOIN t_bond_relation br on br.section_id = a.sub_id and br.is_delete = 0 AND br.company_id = a.company_id
        WHERE
            p.id = #{projectId}
          AND a.is_delete = 0
          AND a.company_id = #{companyId}
        <if test="operateRole == 1 ">
            AND CASE WHEN s.submit_end_time_type = 1 THEN s.submit_end_time > NOW() ELSE 1 = 1 END
        </if>
        <if test="operateRole == 2 ">
            AND s.submit_end_time_type = 1 AND NOW() > s.submit_end_time
        </if>
          AND a.download_flag = 1
          and a.bid_round = 2
          AND s.bond_type != 0
          and br.id is null
        <!-- and (s.former_abnormal_status is null or s.former_abnormal_status >= 30) -->
        <!-- AND s.`status` &lt; 50 -->
    </select>

    <!-- 根据流水ids查询关联标段 -->
    <select id="getSectionListByWaterIds" resultType="com.hzw.sunflower.controller.response.BondRelationSectionVo">
        SELECT DISTINCT
        s.*,
        p.purchase_number,
        p.purchase_name,
        p.package_segment_status
        FROM
        t_project_bid_section s
        LEFT JOIN t_bond_relation r ON s.id = r.section_id
        LEFT JOIN t_project p ON p.id = r.project_id
        WHERE
        r.water_id IN
        <foreach collection="waterIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND r.`status` = #{relationStatus}
        AND r.is_delete = 0
        ORDER BY s.package_number asc
    </select>

    <!-- 根据标段id和供应商id获取标段详情 -->
    <select id="getSectionById" resultType="com.hzw.sunflower.controller.response.BondRelationSectionVo">
        SELECT s.*,
               p.purchase_number,
               p.purchase_name,
               p.package_segment_status,
               a.download_flag
        from t_project_bid_section s
                 LEFT JOIN t_project p on p.id = s.project_id
                 LEFT JOIN t_apply_info a on a.sub_id = s.id
        where s.id = #{sectionId}
          and a.is_delete = 0
          and a.company_id = #{companyId}
          and a.bid_round = 2
          and a.apply_status = 4
    </select>

    <!-- 根据供应商名称和附言匹配 委托编号、项目编号、进场编号、国际标编号 -->
    <select id="selectProjectByPurchaseNumber" resultType="com.hzw.sunflower.entity.Project">
        SELECT
            p.*
        FROM
            t_apply_info a
                LEFT JOIN t_project p ON a.project_id = p.id
        WHERE
            REPLACE(REPLACE(REPLACE(REPLACE(a.company_name,'(',''),')',''),'（',''),'）','') = REPLACE(REPLACE(REPLACE(REPLACE(#{companyName},'(',''),')',''),'（',''),'）','')
          and a.bid_round = 2
          and (
                FIND_IN_SET(#{purchaseNumber}, p.project_number)
                or
                p.purchase_number = #{purchaseNumber}
                or
                p.international_number = #{purchaseNumber}
                or
                p.process_code = #{purchaseNumber}
            )
        GROUP BY
            p.id;
    </select>

    <!-- 查询自动关联判断结果 -->
    <select id="checkAutoRelation" resultType="com.hzw.sunflower.dto.AutoRelationDto">
        SELECT
            s.bond_type, s.status,
            IF(s.submit_end_time_type = 1, IF(DATE_FORMAT( s.submit_end_time, '%Y-%m-%d %H:%i:%S' ) >= DATE_FORMAT(NOW(), '%Y-%m-%d %H:%i:%S' ),0,1), 0) is_over_submit_end_time,
            IF(s.submit_end_time_type = 1, IF(DATE_FORMAT( s.submit_end_time, '%Y-%m-%d %H:%i:%S' ) >= DATE_FORMAT(#{dateTime}, '%Y-%m-%d %H:%i:%S' ),0,1), 0) is_over_bond_time,
            (SELECT count(1) from t_bond_relation where section_id = #{sectionId} and company_id = #{companyId} and is_delete = 0) relate_count,
            a.download_flag
        from t_project_bid_section s
        LEFT JOIN t_apply_info a on a.sub_id = s.id
        where a.company_id = #{companyId} and s.id = #{sectionId} and a.is_delete = 0 and a.bid_round = 2 and a.apply_status = 4
    </select>

    <!-- 运管处根据企业id、采购编号搜索待关联标段 -->
    <select id="getSectionListByPurchaseNum" resultType="com.hzw.sunflower.controller.response.BondRelationSectionVo">
        SELECT DISTINCT
            a.project_id,
            p.purchase_name,
            p.purchase_number,
            p.package_segment_status,
            a.download_flag,
            p.base_project_id
        FROM
            t_apply_info a
            LEFT JOIN t_project p ON p.id = a.project_id
        WHERE
            a.is_delete = 0
          and a.bid_round = 2
          AND a.company_id = #{companyId}
          AND a.download_flag = 1
          AND (p.purchase_number like CONCAT('%',#{purchaseNumber},'%') or p.purchase_name like CONCAT('%',#{purchaseNumber},'%'))
          limit 50
    </select>

    <!-- 供应商内所有存在已购标但未开标标段的联系人 -->
    <select id="getNoPostScript" resultType="com.hzw.sunflower.dto.BondSmsDto">
        SELECT DISTINCT a.contact_person, a.user_mobile from t_apply_info a
        LEFT JOIN t_project_bid_section s on s.id = a.sub_id
        where a.company_id = #{companyId}
        and a.download_flag = 1
        and a.is_delete = 0
        and case when S.submit_end_time_type = 1 then S.submit_end_time > SYSDATE() else 1 = 1 end
    </select>

    <!-- 获取查询项目下供应商关注/购标联系人 -->
    <select id="getOnlyProject" resultType="com.hzw.sunflower.dto.BondSmsDto">
        SELECT DISTINCT contact_person, user_mobile from t_apply_info
        where company_id = #{companyId} and project_id = #{projectId} and is_delete = 0 and download_flag = 1
    </select>

    <!-- 查询该供应商标段下所有购标联系人 -->
    <select id="getRelationResult" resultType="com.hzw.sunflower.dto.BondSmsDto">
        SELECT DISTINCT contact_person, user_mobile from t_apply_info
        where company_id = #{companyId} and project_id = #{projectId} and is_delete = 0 and download_flag = 1
        and sub_id in
        <foreach collection="sectionIdList" item="subId" open="(" separator="," close=")" index="">
            #{subId}
        </foreach>
    </select>



    <select id="getDeleteCountByWaterId" resultType="java.lang.Integer">
        select count(1) from  t_bond_relation where is_delete = 1 and water_id = #{waterId}
    </select>

    <select id="checkBondRelationByWaterId" resultType="java.lang.Integer">
        SELECT
            count(1)
        FROM
            t_bond_relation
        where
            is_delete = 0
          and `status` != 4
          and water_id = #{waterId}
    </select>
    <select id="selectUserIdByMenu" resultType="java.lang.String">
        SELECT
            DISTINCT
            ur.user_id
        FROM
            t_menu m
            LEFT JOIN t_role_menu rm ON m.id = rm.menu_id
            left join  r_user_role ur on ur.role_id = rm.role_id
            where m.path = #{path}
    </select>
    <select id="getSectionListByWaterIdsCompleted"
            resultType="com.hzw.sunflower.controller.response.BondRelationSectionVo">
        SELECT DISTINCT
        s.*,
        p.purchase_number,
        p.purchase_name,
        p.package_segment_status
        FROM
        t_project_bid_section s
        LEFT JOIN t_bond_relation r ON s.id = r.section_id
        LEFT JOIN t_project p ON p.id = r.project_id
        WHERE
        r.created_time = #{createdTime}
        and
        r.water_id IN
        <foreach collection="waterIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        ORDER BY s.package_number asc
    </select>
    <select id="getNotRelateSectionOffline"
            resultType="com.hzw.sunflower.controller.response.BondRelationSectionVo">
        SELECT
            s.*,
            p.purchase_name,
            p.purchase_number,
            p.package_segment_status
        FROM
            t_bond_offline_company boc
            LEFT JOIN t_offline_company oc ON boc.offline_company_id = oc.id
            LEFT JOIN t_project_bid_section s ON s.id = boc.section_id
            LEFT JOIN t_project p ON p.id = boc.project_id
            LEFT JOIN t_bond_relation br ON br.section_id = boc.section_id AND br.is_delete = 0 AND br.company_id = boc.id
        WHERE
            p.id = #{projectId}
            AND boc.is_delete = 0
            AND oc.is_delete = 0
            AND oc.company_name = #{companyName}
            AND s.submit_end_time_type = 1 AND NOW() > s.submit_end_time
            AND s.`status` >= '30'
	        AND br.id IS NULL
    </select>
    <select id="getSectionByIdOffline"
            resultType="com.hzw.sunflower.controller.response.BondRelationSectionVo">
        SELECT
            s.*,
            p.purchase_number,
            p.purchase_name,
            p.package_segment_status
        FROM
            t_project_bid_section s
            LEFT JOIN t_project p ON p.id = s.project_id
            LEFT JOIN t_bond_offline_company boc ON boc.section_id = s.id
            LEFT JOIN t_offline_company oc ON boc.offline_company_id = oc.id
        WHERE
            s.id = #{sectionId}
            AND boc.is_delete = 0
            AND oc.is_delete = 0
            AND oc.id = #{offlineCompanyId}
    </select>

</mapper>
