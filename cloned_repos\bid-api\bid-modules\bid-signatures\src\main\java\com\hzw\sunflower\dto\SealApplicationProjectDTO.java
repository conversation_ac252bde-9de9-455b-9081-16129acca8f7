package com.hzw.sunflower.dto;

import com.hzw.sunflower.entity.SealApplicationProject;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

@Data
@EqualsAndHashCode
public class SealApplicationProjectDTO extends SealApplicationProject {

    @ApiModelProperty(value = "委托金额")
    private BigDecimal entrustMoney;

    @ApiModelProperty(value = "采购方式")
    private String purchaseModeName;

    @ApiModelProperty(value = "中标人id")
    private Long bidWinPeopleId;

}