package com.hzw.sunflower.controller.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel(description = "线下项目供应商")
public class OfflineCompanyVo implements Serializable {

    @ApiModelProperty("项目id")
    private Long companyId;

    @ApiModelProperty("公司名称")
    private String companyName;

    @ApiModelProperty("统一社会信用代码")
    private String organizationNum;

    @ApiModelProperty("营业执照")
    private String businessLicense;

    @ApiModelProperty("营业执照文件id")
    private Long licenseFileId;

    @ApiModelProperty("是否为境外企业 0.否 1.是")
    private Integer isCompanyAbroad;

}
