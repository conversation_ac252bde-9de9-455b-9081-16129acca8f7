﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Http;

namespace macco.biding.backstageSystem
{
    public static class WebApiConfig
    {
        public static void Register(HttpConfiguration config)
        {
            config.Routes.MapHttpRoute(
                name: "Default<PERSON><PERSON>",
                routeTemplate: "api/{controller}/{id}",
                defaults: new { id = RouteParameter.Optional }
            );
        }
    }
}
