package com.hzw.ssm.sys.project.action;

import java.io.PrintWriter;
import java.util.List;

import org.apache.struts2.convention.annotation.Action;
import org.apache.struts2.convention.annotation.Namespace;
import org.apache.struts2.convention.annotation.ParentPackage;
import org.apache.struts2.convention.annotation.Result;
import org.apache.struts2.convention.annotation.Results;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import com.hzw.ssm.fw.base.BaseAction;
import com.hzw.ssm.fw.util.SysConstants;
import com.hzw.ssm.sys.project.entity.ProjectEntity;
import com.hzw.ssm.sys.project.service.ProjectService;
import com.hzw.ssm.sys.project.service.VoiceProjectService;
import com.hzw.ssm.sys.user.entity.UserEntity;

/**
 * 语音异常项目紧急处理
 * <AUTHOR>
 *
 */
@Namespace("/emergencyTreatment")
@ParentPackage(value = "default")
@Results( { 
	@Result(name = "initList", location = "/jsp/voice/voiceEmergencyTreatmentList.jsp"),
			
})
public class EmergencyTreatmentAction extends BaseAction {
	private static final long serialVersionUID = -8823858495799309882L;
	
	private ProjectEntity project;
	private List<ProjectEntity> projectList;//专家抽取结果列表

	@Autowired
	private VoiceProjectService voiceProjectService;
	
	@Value("${treatmentDate}")
	private int treatmentDate;
	@Autowired
	private ProjectService projectService;
	/**
	 * 初始化页面
	 * 
	 */
	@Action("initList")
	public String initList() {
		this.context();
		//初始化查询项目状态为抽取中的项目
		if(project==null){
			project=new ProjectEntity();
			
		}
		project.setStatus(SysConstants.PROJECT_STATUS.DOING);
		project.setMethod(SysConstants.CONDITION_METHOD.METHOD_THREE);
		
		project.setPage(this.getPage());
		UserEntity user=(UserEntity)this.getRequest().getSession().getAttribute("userInfo");
		project.setCreate_id(user.getUser_id());
		project.setTreatmentDate(treatmentDate);
		System.out.println("执行SQL");
		//先判断当前项目是否有问题
		projectList = voiceProjectService.queryPageEmergencyTreatmentList(project);
		return "initList";
	}
	/**
	 * 将语音转人工处理的项目状态修改为人工
	 * @return
	 */
	@Action("updateProjectStatus")
	public String updateProjectStatus(){
		this.context();
		this.getResponse().setCharacterEncoding("utf-8");
		PrintWriter out = null;
		String msg = "";
		try {
			out = this.getResponse().getWriter();
			if(project.getDecimationBatch()=="" || project.getDecimationBatch()==null){
				msg = "批次号不能为空！";
			}else{
				this.getResponse().getWriter();
				project.setMethod(SysConstants.CONDITION_METHOD.METHOD_ONE);
				msg =projectService.updateConditionStatusByDecimationBatch(project);
			}
			out.print(msg);
			
			out.close();
		} catch (Exception e) {
			e.printStackTrace();
			this.setAlertMessage("转人工抽取失败！");
		}
		return null;
	}
	
	
	
	public ProjectEntity getProject() {
		return project;
	}
	public void setProject(ProjectEntity project) {
		this.project = project;
	}
	public List<ProjectEntity> getProjectList() {
		return projectList;
	}
	public void setProjectList(List<ProjectEntity> projectList) {
		this.projectList = projectList;
	}
	
	
}
