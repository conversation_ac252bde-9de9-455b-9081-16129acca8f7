/*
 * 公司名称：江苏华招网信息技术有限公司
 * 版权信息：江苏华招网信息技术有限公司版权所有
 * 文件名称：Base64.java
 * 修改时间：2020年8月18日
 * 修  改  人：朱加健
 * 修改内容：
 * 跟踪单号：
 * 修改单号：
 */

package com.hzw.ssm.util.encrypt;

import java.io.UnsupportedEncodingException;

import org.apache.commons.codec.binary.Base64;

/**
 * <一句话功能简述> TODO <功能详细描述>
 * 
 * <AUTHOR>
 * @version V1.0[产品/模块版本]
 */
public class Base64Util {

	/**
	 * 字符串默认编码格式
	 */
	private static final String DEFAULT_ENCODING = "utf-8";

	/**
	 * 函数功能描述：base64编码
	 * 
	 * @param str
	 *            原字符串
	 * @param encoding
	 *            字符编码格式
	 * @return
	 * @throws UnsupportedEncodingException
	 */
	public static String base64Encode(String str, String encoding) throws UnsupportedEncodingException {
		byte[] bytes = str.getBytes(encoding);

		return new String(Base64.encodeBase64(bytes), encoding);
	}

	/**
	 * 函数功能描述：base64编码（字符串默认编码格式：utf-8)
	 * 
	 * @param str
	 *            原字符串
	 * @return
	 * @throws UnsupportedEncodingException
	 */
	public static String base64EncodeUtf8(String str) throws UnsupportedEncodingException {
		return base64Encode(str, DEFAULT_ENCODING);
	}

	/**
	 * 
	 * 函数功能描述：base64解码
	 * 
	 * @param str
	 *            原字符串
	 * @param encoding
	 *            字符编码格式
	 * @return
	 * @throws UnsupportedEncodingException
	 */
	public static String base64Decode(String str, String encoding) throws UnsupportedEncodingException {
		byte[] srcBytes = str.getBytes(encoding);

		byte[] dsrBytes = Base64.decodeBase64(srcBytes);

		return new String(dsrBytes, encoding);
	}

	/**
	 * 函数功能描述：base64解码（字符串默认编码格式：utf-8)
	 * @param str 原字符串
	 * @return
	 * @throws UnsupportedEncodingException
	 */
	public static String base64DecodeUtf8(String str) throws UnsupportedEncodingException {

		return base64Decode(str, DEFAULT_ENCODING);
	}

	public static void main(String args[]) throws UnsupportedEncodingException {
		String pwd = "123456";

		String dst = Base64Util.base64Encode(pwd, "utf-8");
		System.out.println("dst=" + dst);

		String src = Base64Util.base64Decode(dst, "utf-8");
		System.out.println("src=" + src);

	}
}
