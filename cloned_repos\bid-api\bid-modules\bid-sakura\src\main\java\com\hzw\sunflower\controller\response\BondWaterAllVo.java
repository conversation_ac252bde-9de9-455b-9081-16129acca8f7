package com.hzw.sunflower.controller.response;

import com.hzw.sunflower.entity.BondWater;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "保证金收款流水分页返回实体类")
public class BondWaterAllVo extends BondWater {

    /**
     * 1.关联待确认、
     * 2.未关联、
     * 4.异常关联待运管处确认、
     * 5.未关联退款待运管处确认、
     * 6.未关联退款待处长确认、
     * 3.未关联退款已退回、
     * 7.已关联、
     * 8.退款中
     */
    @ApiModelProperty("流水状态")
    private Integer waterStatus;

    @ApiModelProperty("退还id")
    private Long refundId;

}
