package com.hzw.sunflower.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzw.sunflower.controller.request.SysAppReq;
import com.hzw.sunflower.dao.SysAppMapper;
import com.hzw.sunflower.entity.SysApp;
import com.hzw.sunflower.service.SysAppService;
import com.hzw.sunflower.util.BeanListUtil;
import org.springframework.stereotype.Service;

@Service
public class SysAppServiceImpl extends ServiceImpl<SysAppMapper, SysApp> implements SysAppService {
    /**
     * 启用禁用
     * @param req
     * @return
     */
    @Override
    public Boolean updateStatus(SysAppReq req) {
        SysApp sysApp = BeanListUtil.convert(req,SysApp.class);
        return this.baseMapper.updateById(sysApp)>0;
    }
}
