package com.hzw.ssm.expert.action;

import java.util.List;

import org.apache.struts2.convention.annotation.Action;
import org.apache.struts2.convention.annotation.Namespace;
import org.apache.struts2.convention.annotation.ParentPackage;
import org.apache.struts2.convention.annotation.Result;
import org.apache.struts2.convention.annotation.Results;
import org.springframework.beans.factory.annotation.Autowired;

import com.hzw.ssm.expert.entity.ExpertInfoEntity;
import com.hzw.ssm.expert.service.ExpertInfoService;
import com.hzw.ssm.fw.base.BaseAction;

@Namespace("/expertInfoReport")
@ParentPackage(value = "default")
@Results( { 
	@Result(name = "init", location = "/jsp/expert/expertLibraryList.jsp")
})

/**
 * 导出关于专家信息的报表
 */
public class ExpertInfoReportAction extends BaseAction {
	
	private static final long serialVersionUID = -882385849579930002L;
	
	/** 专家信息*/
	private ExpertInfoEntity colExpertInfo;
	
	private List<ExpertInfoEntity> expertInfoList;
	
	@Autowired
	private ExpertInfoService expertInfoService;
	
	/**
	 * 出库专家汇总及专家库标记记录表
	 * @return
	 * @throws Exception
	 */
	@Action("expertLibraryList")
	public String expertLibrary() throws Exception {
		this.context();
		
		if (null == colExpertInfo)
		{
			colExpertInfo = new ExpertInfoEntity();
		}
		colExpertInfo.setAge(70);
		colExpertInfo.setPage(this.getPage());
		expertInfoList = expertInfoService.queryPageByExpertLibrary(colExpertInfo);
		
		return INIT;
	}
	
	/**
	 * 出库专家汇总及专家库标记记录表
	 * @return
	 * @throws Exception
	 */
	@Action("exportExpertLibrary")
	public String exportExpertLibrary() throws Exception {
		this.context();
		if (null == colExpertInfo)
		{
			colExpertInfo = new ExpertInfoEntity();
		}
		colExpertInfo.setAge(70);
		expertInfoService.exportExpertInfo(colExpertInfo, this.getResponse());
		return null;
	}

	public ExpertInfoEntity getColExpertInfo() {
		return colExpertInfo;
	}

	public void setColExpertInfo(ExpertInfoEntity colExpertInfo) {
		this.colExpertInfo = colExpertInfo;
	}

	public List<ExpertInfoEntity> getExpertInfoList() {
		return expertInfoList;
	}

	public void setExpertInfoList(List<ExpertInfoEntity> expertInfoList) {
		this.expertInfoList = expertInfoList;
	}

}
