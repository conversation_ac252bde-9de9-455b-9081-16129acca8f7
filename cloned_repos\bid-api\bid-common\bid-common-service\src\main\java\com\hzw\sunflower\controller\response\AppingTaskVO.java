package com.hzw.sunflower.controller.response;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@ApiModel(description = "代办列表 ")
@Data
public class AppingTaskVO implements Serializable {
    /**
     * 任务id
     */
    @ApiModelProperty(value = "任务id")
    private String taskId;
    /**
     * 审批内容
     */
    @ApiModelProperty(value = "审批内容")
    private String formName;

    /**
     * 表单的标识
     */
    private String formKey;

    /**
     * 关联标段
     */
    @ApiModelProperty(value = "项目ID")
    private Long projectId;
    /**
     * 项目编名称
     */
    @ApiModelProperty(value = "项目编名称")
    private String projectName;

    /**
     * 项目编号
     */
    @ApiModelProperty(value = "项目编号")
    private String projectNum;

    /**
     * 类别
     */
    @ApiModelProperty(value = "类别")
    private String processDefinitionKey;
    /**
     * 关联标段
     */
    @ApiModelProperty(value = "关联标段")
    private String projectBidSections;


    /**
     * 提交时间
     */
    @ApiModelProperty(value = "提交时间")
    private Date submitTime;


    /**
     * 流程实例的id
     */
    @ApiModelProperty(value = "流程实例的id", required = true)
    private String processInstanceId;

    /**
     * 业务管理key ,表名:id 例如 notice:123456
     */
    @ApiModelProperty(value = "业务管理key ,表名:id 例如 notice:123456", required = true)
    private String businessKey;

    /**
     * 阶段： 0：默认无阶段； 1：第一轮； 2：第二轮
     */
    @ApiModelProperty(value = "阶段", required = true)
    private Integer bidRound;

    /**
     * 阶段： 0：默认无阶段； 1：第一轮； 2：第二轮
     */
    @ApiModelProperty(value = "标段Id", required = true)
    private Long sectionId;

    @ApiModelProperty(value = "申请id")
    private Long id;

    @ApiModelProperty(value = "标段Id集合", required = true)
    private String sectionIds;

    @ApiModelProperty(value = "数据类别")
    private String formData;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "原id")
    private Long baseId;

    /**
     * 流程创建人姓名
     */
    @ApiModelProperty(value = "流程创建人姓名")
    private String startPersonName;

}
