package com.hzw.bid.util;

/**
 * <AUTHOR>
 * @Date 2021年04月08日 15:42
 * @Version 1.0
 */
public class GeneralConstants {

    /**
     * 令牌
     */
    public static final String TOKEN = "Authorization";
    public static final String AUTHORIZATION = "JSTCC";
    public static final String REFRESH_TOKEN = "refreshToken";
    public static final String SIGNING_KEY = "spring-security-@Jwt!&Secret^#";
    public static final String ISS = "hzw";
    public static final String ROLE_CLAIMS = "rol";
    public static final String STATUS_CLAIMS = "sta";
    public static final String ENTER_CLAIMS = "ent";
    public static final String COMPANY_CLAIMS = "cmp";
    public static final String USER_NAME_CLAIMS = "una";
    public static final String USER_NAME_OTHER_CLAIMS = "uno";
    public static final String USER_INFO_CLAIMS = "uio";
    public static final Integer REFRESH_EXPIRATION_TIME = 5;
    public static final Integer REFRESH_EXPIRATION_TIME_VALIDITY = 2;
    // 目前是4个小时自己根据情况调整
    public static final Long TOKEN_VALID_TIME = 1000 * 60 * 60 * 4L;

    // 目前是TOKEN失效时间
    public static final Long TOKEN_TIME = 1000 * 60 * 60 * 24 * 30L;

    // token 失效前多久更新
    public static final Long REFRESH_TOKEN_VALID_TIME = 30L;

    // 工作台缓存时间(1分钟)
    public static final Long WORKBENCH_CACHE_VALID_TIME = 1000 * 60 * 1L;

    /**
     * 令牌前缀
     */
    public static final String TOKEN_PREFIX = "Bearer ";

    /**
     * 跨域允许时间
     */
    public static final int CORS_MAX_AGE = 3600;
    /**
     * fileSize
     */
    public static final int FILE_SIZE = 1024;
    /**
     * 参数拼接方式
     */
    public static final String SPLICING_PARAMETER_METHOD = ",";

    /**
     * UTF-8 字符集
     */
    public static final String UTF8 = "UTF-8";

    /**
     * CONTENT_TYPE
     */
    public static final String CONTENT_TYPE = "application/json; charset=utf-8";
    /**
     * GBK-8 字符集
     */
    public static final String GBK = "GBK";
    /**
     * 防重提交 redis key
     */
    public static final String REPEAT_SUBMIT_KEY = "repeat_submit:";
    /**
     * 系统license redis key
     */
    public static final String SYS_LICENSE_KEY = "SYS_LICENSE";
    /**
     * 防重提交 repeatParams
     */
    public static final String REPEAT_PARAMS = "repeatParams";
    /**
     * 防重提交 repeatTime
     */
    public static final String REPEAT_TIME = "repeatTime";
    /**
     * 防重提交 间隔时间，单位:秒 默认10秒
     */
    public static final int INTERVAL_TIME = 10;
    /**
     * String 截取长度
     */
    public static final int SUBSTRING_LENGTH = 6000;
    /**
     * s 秒
     */
    public static final int SECOND = 1000;
    public static final int TWO = 2;
    public static final int THREE = 3;
    public static final int FOUR = 4;
    public static final int SIX = 6;
    public static final int EIGHT = 8;
    public static final int SIX_TEEN = 16;
    public static final int TWENTY_FOUR = 24;
    public static final int SIXTY_FOUR = 64;
    public static final int ONE_HUNDRED_AND_TWENTY = 120;
    public static final long MAX_LENGTH = 4294967295L;
    public static final long HEXADECIMAL_0XFF = 0xFF;
    public static final long HEXADECIMAL_0XFFFFFF = 0xFFFFFF;
    public static final long HEXADECIMAL_0XFFFF = 0xFFFF;
    public static final long HEXADECIMAL_FFFFFF = 16777215L;
    public static final long HEXADECIMAL_FFFF = 65535L;

    /**
     * 模板长度限制
     */
    public static final int TEMPLATE_CONTENT_LIMIT = 500;
    public static final int TEMPLATE_NAME_LIMIT = 20;

    /**
     * 权限角色
     */
    public static final String ROLE_CODE_ADMIN = "ROLE_ADMIN";

    /**
     * 缓存超时时间（毫秒）
     */
    public static final long REDIS_CACHE_TIME_OUT = 5 * 60 * 1000;

    public static final String JSTCC_URL = "http://www.jstcc.cn";
    public static final String JSTCC_NAME = "江苏省招标中心有限公司";
}
