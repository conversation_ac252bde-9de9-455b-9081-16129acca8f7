package com.hzw.sunflower.controller.request;

import com.hzw.sunflower.controller.response.PurchaseVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.security.core.parameters.P;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class BidOpenDetailReq {
    @ApiModelProperty(value = "关键字")
    private String keyWords;

    @ApiModelProperty(value = "起始日期")
    private String startDate;

    @ApiModelProperty(value = "结束日期")
    private String endDate;

    @ApiModelProperty(value = "页码")
    private int pageIndex;

    @ApiModelProperty(value = "每页个数")
    private int pageSize;

    @ApiModelProperty(value = "部门id集合")
    private List<Long> deptIds;

    @ApiModelProperty(value = "用户id集合")
    private List<Long> userIds;

    @ApiModelProperty(value = "采购类型集合")
    private List<PurchaseVo> purchaseVos;

    @ApiModelProperty(value = "委托金额最大值")
    private String maxPrice;

    @ApiModelProperty(value = "委托金额最小值")
    private String minPrice;

    @ApiModelProperty(value = "处室菜单判断true有 false 无")
    private Boolean flag;

    @ApiModelProperty(value = "标段状态 1.全部 2.已开标(包含异常前已开标) 3.未开标 4.异常")
    private String bidStatus;

    private String dataScope;

    private Double dollar;//美元汇率
    private Double euro;//欧元汇率
    private Double yen;//日元汇率

    private String year;
    private String month;
    private String day;


}
