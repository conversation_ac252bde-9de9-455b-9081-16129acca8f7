package com.hzw.sunflower.controller.response;

import  com.hzw.sunflower.dto.SealBidWinDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
* SealBidWin 返回实体
*
* <AUTHOR>
* @version 1.0.0
*/
@ApiModel(description = "SealBidWin ")
@Data
public class SealBidWinVO extends SealBidWinDTO {

    @ApiModelProperty("候选人id")
    private Long tendererId;

    @ApiModelProperty("候选人企业名称")
    private String tendererName;

    @ApiModelProperty("候选人联系人id")
    private String bidContactsId;

    @ApiModelProperty("候选人联系人名称")
    private String bidContactsName;

    @ApiModelProperty("原始文件")
    private String originalFile;

    @ApiModelProperty("盖章文件")
    private String sealFile;

    @ApiModelProperty("盖章文件key")
    private String sealFileKey;

    @ApiModelProperty("盖章文件name")
    private String sealFileName;
}