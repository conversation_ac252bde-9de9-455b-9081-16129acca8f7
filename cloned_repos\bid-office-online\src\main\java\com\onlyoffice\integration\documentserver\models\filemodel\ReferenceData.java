/**
 *
 * (c) Copyright Ascensio System SIA 2024
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

package com.onlyoffice.integration.documentserver.models.filemodel;

import java.util.HashMap;
import java.util.Map;
import com.onlyoffice.integration.documentserver.storage.FileStoragePathBuilder;
import org.springframework.beans.factory.annotation.Autowired;

public class ReferenceData {
    @Autowired
    private FileStoragePathBuilder storagePathBuilder;
    private final String instanceId;
    private final Map<String, String> fileKey;
    public ReferenceData(final String fileName, final String curUserHostAddress, final User user) {
        instanceId = storagePathBuilder.getServerUrl(true);
        Map<String, String> fileKeyList = new HashMap<>();
        if (!user.getId().equals("uid-0")) {
            fileKeyList.put("fileName", fileName);
            fileKeyList.put("userAddress", curUserHostAddress);
        } else {
            fileKeyList = null;
        }
        fileKey = fileKeyList;
    }
}
