///***
/// * 圆形波纹图表层
/// *
/// * 本来是没有必要单独拿出来一层，但是波纹的动画对页面中的日期时间图表造成了影响
/// * 使其报错：'_drawAreaBoundsOutdated == false': is not true
/// * 因此，把动画控制单独拎出来做一层
/// */

import 'package:flutter/material.dart';

import 'package:flutter_main_app/src/components/canvas/CustomCircularRipplePainter.dart';

class CustomCircularRippleChart extends StatefulWidget {

  CustomCircularRippleChart({
    @required this.number,
    this.unit,
    @required this.yearOnYear,
  }) : assert(number != null && number >= 0),
    assert(yearOnYear != null);

  /// * 数值
  final num number;

  /// * 单位
  final String unit;

  /// * 同比
  final double yearOnYear;

  @override
  _CustomCircularRippleChartState createState() => _CustomCircularRippleChartState();

}

class _CustomCircularRippleChartState extends State<CustomCircularRippleChart> with TickerProviderStateMixin {

  /// * 年度累计圆形波纹画布边长 - 正方形
  static const double _circularRippleSide = 130.0;

  /// * y 轴上的位移比
  static const double _rippleYAxisMaxRatio = 0.65;

  /// * 波纹高度动画控制
  Animation<double> _rippleGrowAnimation;

  AnimationController _rippleGrowController;

  /// * 波纹波动动画控制
  Animation<double> _rippleWaveAnimation;

  AnimationController _rippleWaveController;

  @override
  void initState() {
    super.initState();

    /// * 波纹高度动画控制
    _rippleGrowController = AnimationController(
      duration: Duration(milliseconds: 2000),
      vsync: this,
    );

    _rippleGrowAnimation = Tween(
      begin: 0.0,
      end: _rippleYAxisMaxRatio,
    ).animate(
      CurveTween(
        curve: Curves.easeInOutBack,
      ).animate(_rippleGrowController),
    )
      ..addListener(() {
        /// * 保证 UI 更新
        setState(() {});
      })
      ..addStatusListener((AnimationStatus status) async {
        if (status == AnimationStatus.completed) {
          /// * 2 个动画之间的间隔
          await Future.delayed(Duration(milliseconds: 50));

          ///***
          /// * 启动波纹波动动画
          /// * 重复动画至无限
          /// *
          /// ! 必须加上 `await`，否则动画不启动，原因不清楚
          /// */
          try {
            await _rippleWaveController
              .repeat()
              .orCancel;
          } on TickerCanceled {
            /// * nothing to do, just to prevent the error stopping the app
          }
        }
      });

    _rippleGrowController.forward();

    /// * 波纹波动动画控制
    _rippleWaveController = AnimationController(
      duration: Duration(milliseconds: 3000),
      vsync: this,
    );

    _rippleWaveAnimation = Tween(
      begin: 0.0,
      end: _circularRippleSide,
    ).animate(_rippleWaveController)
      ..addListener(() {
        /// * 保证 UI 更新
        setState(() {});
      });
  }

  @override
  void dispose() {
    _rippleGrowController.dispose();

    _rippleWaveController.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return CustomPaint(
      size: Size(_circularRippleSide, _circularRippleSide),
      painter: CustomCircularRipplePainter(
        number: widget.number,
        unit: widget.unit,
        yearOnYear: widget.yearOnYear,
        yAxisRatio: _rippleGrowAnimation.value,
        xAxisTranslate: _rippleWaveAnimation.value,
      ),
    );
  }

}
