<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzw.sunflower.dao.ColumnConfigMapper">

    <select id="selectPurchaseTypeReqList" resultType="com.hzw.sunflower.controller.request.PurchaseTypeReq">
        SELECT
            t1.purchase_type AS purchaseType,
            t1.purchase_mode AS purchaseMode,
            t1.purchase_status AS purchaseStatus,
            t2.submit_end_time_type AS submitEndTimeType,
            t2.submit_end_time_day AS submitEndTimeDay,
            t2.submit_end_time_unit AS submitEndTimeUnit,
            t2.sale_end_time_type AS saleEndTimeType,
            t2.sale_end_time_day AS saleEndTimeDay,
            t2.sale_end_time_unit AS saleEndTimeUnit,
            t2.amount AS amount
        FROM
            s_column_purchase_config t1
            LEFT JOIN s_column_check_config t2 ON t1.id = t2.purchase_config_id
        WHERE
            t1.is_delete = 0
            AND t2.is_delete = 0
    </select>
    <select id="selectDepartmentCodeOne" resultType="java.lang.String">
        SELECT
            d.`code`
        FROM
            t_department d
        WHERE
            d.is_delete = 0
        ORDER BY
            d.id
        LIMIT 1
    </select>
    <select id="selectProjectColumn"
            resultType="com.hzw.sunflower.controller.response.ProjectColumnRequiredVO">
        SELECT
            t1.module_name AS moduleName,
            t1.module_code AS moduleCode,
            t1.column_name AS columnName,
            t2.id AS id,
            t2.column_required_id AS columnRequiredId,
            t2.is_required AS isRequired,
            t2.gen_type AS genType,
            t2.gen_rules AS genRules,
            t2.year_len AS yearLen,
            t2.incre_number_len AS increNumberLen,
            t2.show_process AS showProcess,
            t1.disabled AS disabled,
            t1.code
        FROM
            s_column_config t2
            LEFT JOIN s_column_required t1 ON t1.id = t2.column_required_id
        WHERE
            t1.is_delete = 0
            AND t2.is_delete = 0
            AND t1.is_required_agent = 1
            AND t1.id IN
            <foreach item="item" collection="longList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        ORDER BY t1.module_code,t1.sort
    </select>
    <select id="selectLicenseFunctionModule" resultType="java.lang.Integer">
        SELECT t.module FROM s_license_relation t WHERE t.is_delete = 0 AND t.license_function = #{licenseFunction};
    </select>
</mapper>