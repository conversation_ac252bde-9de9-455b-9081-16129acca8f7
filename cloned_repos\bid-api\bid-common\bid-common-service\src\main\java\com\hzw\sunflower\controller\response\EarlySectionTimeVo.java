package com.hzw.sunflower.controller.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * <AUTHOR>
 * @date ：Created in 2022/9/2 8:37
 * @description：项目标段时间vo
 * @modified By：`
 * @version: 1.0
 */
@ApiModel(description = "标段时间实体")
@Data
public class EarlySectionTimeVo {

    @ApiModelProperty(value = "招标文件发售截止时间")
    private String saleEndTime;

    @ApiModelProperty(value = "递交响应文件截止时间")
    private String submitEndTime;
}
