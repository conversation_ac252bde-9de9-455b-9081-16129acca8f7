package com.hzw.sunflower.controller.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class BondOfflineCompanyVo {

    @ApiModelProperty(value = "保证金线下项目供应商表id")
    private Long bondOfflineCompanyId;

    @ApiModelProperty("项目id")
    private Long projectId;

    @ApiModelProperty(value = "标段id")
    private Long sectionId;

    @ApiModelProperty(value = "线下公司id")
    private Long offlineCompanyId;

    @ApiModelProperty(value = "申请户名")
    private String applyCompanyName;

    @ApiModelProperty(value = "支付凭证")
    private Long payFileId;

    @ApiModelProperty(value = "供应商名称")
    private String companyName;

    @ApiModelProperty(value = "组织机构代码")
    private String organizationNum;

    @ApiModelProperty(value = "营业执照")
    private String businessLicense;

    @ApiModelProperty(value = "营业执照文件id")
    private Long licenseFileId;

    @ApiModelProperty(value = "是否为境外企业 0.否 1.是")
    private Integer isCompanyAbroad;

}
