package com.hzw.ssm.expert.entity;

/**
 * 外呼语音入参
 * 
 * <AUTHOR>
 * @date 2018-03-27
 */
public class OutboundTTS {

	/**
	 * 客户id
	 */
	private String client_id;
	
	/**
	 * 客户密码
	 */
	private String client_pass;
	
	/**
	 * 该条tts记录的唯一标识
	 */
	private String synchro_id;
	
	/**
	 * 按键提示内容
	 */
	private String tts_content;

	public String getClient_id() {
		return client_id;
	}

	public void setClient_id(String client_id) {
		this.client_id = client_id;
	}

	public String getClient_pass() {
		return client_pass;
	}

	public void setClient_pass(String client_pass) {
		this.client_pass = client_pass;
	}

	public String getSynchro_id() {
		return synchro_id;
	}

	public void setSynchro_id(String synchro_id) {
		this.synchro_id = synchro_id;
	}

	public String getTts_content() {
		return tts_content;
	}

	public void setTts_content(String tts_content) {
		this.tts_content = tts_content;
	}
	
	
}
