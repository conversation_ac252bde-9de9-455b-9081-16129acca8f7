package com.hzw.sunflower.dao;

import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2021/7/21 17:04
 * @description：定标流程mapper
 * @modified By：`
 * @version: 1.0
 */
public interface CheckBondMapper {

    /**
     * 验证线下是否存在退还记录
     * @param sectionIds
     * @return
     */
    Integer getOfflineBondRefund(@Param("sectionIds") List<Long> sectionIds,@Param("companyId") Long companyId);

    /**
     * 验证线上是否存在关联记录
     * @param sectionIds
     * @return
     */
    Integer getOnlineBondRefund(@Param("sectionIds") List<Long> sectionIds,@Param("companyId") Long companyId);
}
