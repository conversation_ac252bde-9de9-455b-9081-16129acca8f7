///***
/// * common datalist widget
/// *
/// * comment： must use scroll notification listener and dispatch
/// * to implement nested scroll view body
/// *
/// * <AUTHOR>
/// * @date 20101024
/// */

import 'dart:async';
import "package:flutter/material.dart";

import 'package:flutter_main_app/src/components/DefaultRefreshReleaseTipWidget.dart';
import 'package:flutter_main_app/src/components/DefaultRefreshPullDownTipWidget.dart';
import 'package:flutter_main_app/src/components/DefaultRefreshLoadingWidget.dart';
import 'package:flutter_main_app/src/components/DefaultLoadingMoreDataWidget.dart';

/// * widget builder for data list item
typedef ItemWidgetBuilder<T> = Widget Function(BuildContext context, int index, List<T> dataItems);

/// * callback function to interact with user clicking on the list item
typedef ItemClickFunction<T> = void Function(BuildContext context, int index, List<T> dataItems);

/// * widget builder for empty data list view
typedef EmptyDataListWidgetBuilder = Widget Function(BuildContext context);

/// * widget builder for refresh tip
typedef RefreshTipWidgetBuilder = Widget Function(BuildContext context, bool isRefresh);

/// * widget builder for loading view while refreshing
typedef RefreshLoadingWidgetBuilder = Widget Function(BuildContext context);

/// * function to get refreshed data
typedef RefreshDataFunction<T> = Future<List<T>> Function();

/// * widget builder for loading more data
typedef LoadingMoreDataWidgetBuilder = Widget Function(BuildContext context);

/// * function to load More data
typedef LoadingMoreDataFunction<T> = Future<List<T>> Function();

///***
/// * default widget builder for empty data list
/// *
/// * @param {BuildContext} context
/// * @return {Widget} default empty data list widget
/// */
Widget _defaultEmptyDataListWidgetBuilder(BuildContext context) {
  return Center(
    child: Image.asset(
      'assets/images/no_data.png',
      fit: BoxFit.contain,
    ),
  );
}

///***
/// * default widget builder for refresh tip
/// *
/// * @param {BuildContext} context
/// * @param {bool} isRefresh
/// * @return {Widget} default refresh tip widget
/// */
Widget _defaultRefreshTipWidgetBuilder(BuildContext context, bool isRefresh) {
  if (isRefresh) {
    return DefaultRefreshReleaseTipWidget();
  } else {
    return DefaultRefreshPullDownTipWidget();
  }
}

///***
/// * default widget builder for refresh loading view
/// *
/// * @param {BuildContext} context
/// * @return {Widget} default refresh loading view
/// */
Widget _defaultRefreshLoadingWidgetBuilder(BuildContext context) {
  return DefaultRefreshLoadingWidget();
}

///***
/// * default widget builder for loading more data
/// *
/// * @param {BuildContext} context
/// * @return {Widget} default loading more data widget
/// */
Widget _defaultLoadingMoreDataWidgetBuilder(BuildContext context) {
  return DefaultLoadingMoreDataWidget();
}

///***
/// * Data List View
/// * support endless scroll and refresh data
/// */
class DataListView<T> extends StatefulWidget {
  DataListView({
    Key key,
    @required this.initialDataItems,
    @required this.itemBuilder,
    this.itemClickFunction,
    this.emptyDataListWidgetBuilder = _defaultEmptyDataListWidgetBuilder,
    this.refreshTipWidgetBuilder = _defaultRefreshTipWidgetBuilder,
    this.refreshLoadingWidgetBuilder = _defaultRefreshLoadingWidgetBuilder,
    this.refreshDataFunction,
    this.loadingMoreDataWidgetBuilder = _defaultLoadingMoreDataWidgetBuilder,
    this.loadingMoreDataFunction,
  }) : assert(initialDataItems != null),
    assert(itemBuilder != null),
    super(key: key);

  /// * initial data items
  final List<T> initialDataItems;

  /// * data item builder
  final ItemWidgetBuilder<T> itemBuilder;

  /// * event on item click
  final ItemClickFunction<T> itemClickFunction;

  /// * placeholder of no data
  final EmptyDataListWidgetBuilder emptyDataListWidgetBuilder;

  /// * refresh tip widget builder
  final RefreshTipWidgetBuilder refreshTipWidgetBuilder;

  /// * widget builder for loading view while refreshing
  final RefreshLoadingWidgetBuilder refreshLoadingWidgetBuilder;

  /// * refresh data delegate
  final RefreshDataFunction<T> refreshDataFunction;

  /// * loading more WidgetBuilder delegate when scroll to the bottom of list
  final LoadingMoreDataWidgetBuilder loadingMoreDataWidgetBuilder;

  /// * loading more data delegate function
  final LoadingMoreDataFunction<T> loadingMoreDataFunction;

  @override
  _DataListViewState createState() => _DataListViewState<T>(
    dataItems: initialDataItems,
    itemBuilder: itemBuilder,
    itemClickFunction: itemClickFunction,
  );
}

class _DataListViewState<T> extends State<DataListView> {

  _DataListViewState({
    @required this.dataItems,
    @required this.itemBuilder,
    @required this.itemClickFunction,
  });

  final List<T> dataItems;

  final ItemWidgetBuilder<T> itemBuilder;

  ///***
  /// * the generic will be lost if we don't pass this as a parameter,
  /// * but use widget.itemClickFunction directly
  /// * and dart will report errors like:
  /// * type '(BuildContext, int, List<BulletinModel>) => Null' is not a subtype of type '(BuildContext, int, List<dynamic>) => void'
  /// */
  final ItemClickFunction<T> itemClickFunction;

  /// * how many top widget when scroll down
  int _extentCount = 0;

  /// *
  bool _isRefresh = false;

  /// * is During refresh use to judge relase or not
  bool _isDuringRefresh = false;

  /// * the flag of isRefreshing animation
  bool _isStartWaitting = false;

  /// * current scroll offset
  double _currentOffset = 0;

  /// * is scroll dwon or not
  bool _isScrollDown = false;

  /// * Refresh Loading widget
  Widget loadingWidget;

  /// * Timer of refresh animation
  Timer _timeoutTimer;

  /// * 底部的超出布局
  int _bottomExtentCount = 0;

  /// * bottom loadding more widget
  Widget loadingMoreWidget;

  /// * Timer of Loading more data time out
  Timer _loadingMoreTimer;

  @override
  void dispose() {
    this._timeoutTimer?.cancel();
    this._loadingMoreTimer?.cancel();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (dataItems.length > 0) {
      return NotificationListener<ScrollNotification>(
        onNotification: _onNotification,
        child: ListView.builder(
          physics: BouncingScrollPhysics(),
          padding: EdgeInsets.zero,
          itemCount: dataItems.length + _extentCount + _bottomExtentCount,
          itemBuilder: (BuildContext buildContext, int index) {
            if (this._extentCount == 0) {
              if (this._bottomExtentCount == 0) {
                return _buildNormalDataItem(buildContext, index);
              } else {
                if (index > 0 && index < dataItems.length + _extentCount) {
                  return _buildNormalDataItem(buildContext, index - 1);
                } else {
                  return _buildLoadingMoreDataWidget(buildContext);
                }
              }
            } else {
              if (index == 0) {
                return _buildRefreshWidget(buildContext);
              } else {
                if (_bottomExtentCount == 0) {
                  return _buildNormalDataItem(buildContext, index - 1);
                } else {
                  if (index > 0 && index < dataItems.length + _extentCount) {
                    return _buildNormalDataItem(buildContext, index - 1);
                  } else {
                    return Container(
                      color: Colors.red,
                      constraints: BoxConstraints.expand(),
                      child: Text('加载更多'),
                    );
                  }
                }
              }
            }
          },
        ),
      );
    } else {
      if (widget.emptyDataListWidgetBuilder != null) {
        return LayoutBuilder(
          builder: (context,constraints) {
            return _createEmptyNotifcationListener(constraints.maxHeight);
          },
        );
      } else {
        return Container();
      }
    }
  }

  ///***
  /// * build normal data item
  /// *
  /// * @param {BuildContext} context
  /// * @param {int} index
  /// * @return {Widget} normal data item
  /// */
  Widget _buildNormalDataItem(BuildContext context, int index) {
    return InkWell(
      child: this.itemBuilder(context, index, this.dataItems),
      onTap: () {
        if (this.itemClickFunction != null) {
          this.itemClickFunction(context, index, this.dataItems);
        }
      },
    );
  }

  ///***
  /// * build widget depending on which period it is during refreshing periods
  /// *
  /// * @param {BuildContext} context
  /// * @return {Widget}
  /// */
  Widget _buildRefreshWidget(BuildContext context) {
    if (!this._isStartWaitting) {
      final RefreshTipWidgetBuilder refreshTipWidgetBuilder =
        widget.refreshTipWidgetBuilder ?? _defaultRefreshTipWidgetBuilder;

      return refreshTipWidgetBuilder(context, this._isRefresh);
    } else {
      final RefreshLoadingWidgetBuilder refreshLoadingWidgetBuilder =
        widget.refreshLoadingWidgetBuilder ?? _defaultRefreshLoadingWidgetBuilder;

      final Widget loadingWidget = this.loadingWidget ?? refreshLoadingWidgetBuilder(context);

      if (this.loadingWidget == null && widget.refreshDataFunction != null) {
        this._timeoutTimer.cancel();

        widget.refreshDataFunction().then((value) {
          List<T> refreshedData = value;
          if (mounted) {
            setState(() {
              this.dataItems.clear();
              this.dataItems.addAll(refreshedData);
              this._isStartWaitting = false;
              this._extentCount = 0;
              this.loadingWidget = null;
            });
          }
        });
      }

      return loadingWidget;
    }
  }

  ///***
  /// * build the widget of loading more data
  /// *
  /// * @param {BuildContext} context
  /// * @return {Widget}
  /// */
  Widget _buildLoadingMoreDataWidget(BuildContext context) {
    final LoadingMoreDataWidgetBuilder loadingMoreDataWidgetBuilder =
      widget.loadingMoreDataWidgetBuilder ?? _defaultLoadingMoreDataWidgetBuilder;

    final Widget loadingMoreDataWidget =
      this.loadingMoreWidget ?? loadingMoreDataWidgetBuilder(context);

    if (this.loadingMoreWidget == null && widget.loadingMoreDataFunction != null) {
      widget.loadingMoreDataFunction().then((moreData) {
        if (mounted) {
          setState(() {
            this._bottomExtentCount = 0;
            this.loadingMoreWidget = null;
            this.dataItems.addAll(moreData as List<T>);
          });
          this._loadingMoreTimer.cancel();
        }
      });
    }

    return loadingMoreDataWidget;
  }

  ///***
  /// *
  /// *
  /// * @param {double} height: max height constraint
  /// * @return {Widget}
  /// */
  Widget _createEmptyNotifcationListener(double height) {
    return NotificationListener<ScrollNotification>(
      onNotification: _onNotification,
      child: ListView.builder(
        physics: BouncingScrollPhysics(),
        padding: EdgeInsets.zero,
        itemCount: 1 + _extentCount,
        itemBuilder: (BuildContext buildContext, int index) {
          if (this._extentCount != 0 && index == 0) {
            return _buildRefreshWidget(buildContext);
          }

          return Padding(
            padding: const EdgeInsets.only(bottom: 20),
            child: Container(
              height: height,
              child: Center(
                child: widget.emptyDataListWidgetBuilder(buildContext),
              ),
            ),
          );
        },
      ),
    );
  }

  ///***
  /// *
  /// *
  /// * @param {ScrollNotification} scrollNotification
  /// * @return {bool}
  /// */
  bool _onNotification(ScrollNotification scrollNotification) {
    if (scrollNotification is ScrollStartNotification) {
      ScrollStartNotification(
        context: context,
        metrics: scrollNotification.metrics,
      ).dispatch(context);
    } else if (scrollNotification is ScrollUpdateNotification) {
      ScrollUpdateNotification(
        context: context,
        metrics: scrollNotification.metrics,
      ).dispatch(context);
    } else if (scrollNotification is ScrollEndNotification) {
      ScrollEndNotification(
        context: context,
        metrics: scrollNotification.metrics,
      ).dispatch(context);
    }

    double maxScrollExtent = scrollNotification.metrics.maxScrollExtent;
    double offset = scrollNotification.metrics.pixels;

    this._scrollListener(offset, maxScrollExtent);

    return true;
  }

  ///***
  /// * scroll listener
  /// *
  /// * @param {double} offset
  /// * @param {double} maxScrollExtent
  /// */
  void _scrollListener(double offset, double maxScrollExtent) {
    if (offset < _currentOffset) {
      if (mounted) {
        setState(() {
          _isScrollDown = true;
        });
      }
    } else {
      if (mounted) {
        setState(() {
          _isScrollDown = false;
        });
      }
    }

    _currentOffset = offset;

    if (offset <= -4 && offset > -10) {
      if (!this._isStartWaitting) {
        if (_isScrollDown && mounted) {
          setState(() {
            this._isStartWaitting = false;
            this._isDuringRefresh = false;
            this._isRefresh = false;
            this._extentCount = 1;
          });
        }
      }
    } else if (offset <= -10) {
      if (!this._isStartWaitting) {
        if (_isScrollDown) {
          if (mounted) {
            setState(() {
              this._isRefresh = true;
              this._extentCount = 1;
              this._isDuringRefresh = true;
            });
          }
        } else {
          if (this._isDuringRefresh && mounted) {
            setState(() {
              this._isStartWaitting = true;
              this._isDuringRefresh = false;

              if (this._timeoutTimer != null) {
                this._timeoutTimer.cancel();
              }

              this._timeoutTimer = Timer(Duration(seconds: 15), () {
                if (mounted) {
                  setState(() {
                    this._isStartWaitting = false;
                    this._extentCount = 0;
                  });
                }
              });
            });
          }
        }
      }
    } else {
      if (!this._isStartWaitting && mounted) {
        if (mounted) {
          setState(() {
            this._extentCount = 0;
          });
        }
        if (offset >= maxScrollExtent - 10 && dataItems.length > 0) {
          if (mounted) {
            setState(() {
              this._bottomExtentCount = 1;

              if (this._loadingMoreTimer != null) {
                _loadingMoreTimer.cancel();
              }

              this._loadingMoreTimer = Timer(Duration(seconds: 15), () {
                if (mounted) {
                  setState(() {
                    this._bottomExtentCount = 0;
                    this.loadingMoreWidget = null;
                  });
                }
              });
            });
          }
        }
      } else {
        if (mounted) {
          setState(() {
            this._extentCount = 1;
          });
        }
      }
    }
  }

}
