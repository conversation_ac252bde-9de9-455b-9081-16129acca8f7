package com.hzw.sunflower.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.data.Includes;
import com.deepoove.poi.xwpf.NiceXWPFDocument;
import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.controller.request.BidDocFileFormatReq;
import com.hzw.sunflower.controller.responese.BidDocFileFormatVo;
import com.hzw.sunflower.dao.BidDocFileFormatMapper;
import com.hzw.sunflower.entity.BidDocFileFormat;
import com.hzw.sunflower.entity.OssFile;
import com.hzw.sunflower.service.BidDocFileFormatService;
import com.hzw.sunflower.service.OssFileService;
import com.hzw.sunflower.util.BeanListUtil;
import com.hzw.sunflower.util.FileUtils;
import com.hzw.sunflower.util.WordUtil;
import com.hzw.sunflower.util.oss.OssFileStorage;
import com.hzw.sunflower.util.oss.OssUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.*;
import java.util.HashMap;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * <p>
 * 招标文件编制投标文件格式 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-26
 */
@Service
public class BidDocFileFormatServiceImpl extends ServiceImpl<BidDocFileFormatMapper, BidDocFileFormat> implements BidDocFileFormatService {

    @Autowired
    private OssFileService ossFileService;

    @Value("${oss.active}")
    private String ossType;

    @Value("${files.temporary.path}")
    private String temporaryPath;

    @Override
    public Result<Boolean> submitFileFormat(BidDocFileFormatReq req) {
        BidDocFileFormat bidDocFileFormat = new BidDocFileFormat();
        BeanUtils.copyProperties(req, bidDocFileFormat);
        boolean flag = this.saveOrUpdate(bidDocFileFormat);
        if(flag){
            return Result.ok();
        }
        return Result.error("保存失败");
    }

    @Override
    public Result<List<BidDocFileFormatVo>> queryFileFormat(BidDocFileFormatReq req) {
        LambdaQueryWrapper<BidDocFileFormat> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BidDocFileFormat::getDocId,req.getDocId());
        List<BidDocFileFormat> list = this.list(queryWrapper);
        List<BidDocFileFormatVo> resultList = BeanListUtil.convertList(list,BidDocFileFormatVo.class);
        return Result.ok(resultList);
    }

    @Override
    public void insertFileFormatForWord(Long docId, String path) {
        //查询投标文件格式列表
        LambdaQueryWrapper<BidDocFileFormat> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BidDocFileFormat::getDocId,docId);
        List<BidDocFileFormat> list = this.list(queryWrapper);
        //生成文档目录
        List<String> titleList = list.stream().map(BidDocFileFormat::getContentName).collect(Collectors.toList());
        String formatFilePath = temporaryPath+UUID.randomUUID().toString().replace("-","")+".docx";
        try {
            NiceXWPFDocument directory = WordUtil.createDirectory(titleList);
            for (int i = 0; i < list.size(); i++) {
                //下载投标文件格式
                OssFile ossFile = ossFileService.getOssFileById(list.get(i).getContentFileId());
                OssFileStorage storage = OssUtil.getOssFileStorage(ossType);
                InputStream inputStream = null;
                if(null != ossFile && null != ossFile.getOssFileKey() ){
                    byte[] bytes = storage.downloadFile(ossFile.getOssFileKey());
                    inputStream = FileUtils.byte2InputStream(bytes);
                }
                if(null!=inputStream){
                    NiceXWPFDocument subDoc = new NiceXWPFDocument(inputStream);
                    String title = WordUtil.convertToChinese(i+1)+"、"+list.get(i).getContentName();
                    NiceXWPFDocument subDocTitle = WordUtil.addTitleForWord(title, subDoc);
                    directory = directory.merge(subDocTitle);
                }
            }
            directory.write(new FileOutputStream(formatFilePath));
            directory.close();
            //获取模板
            XWPFTemplate tmp = XWPFTemplate.compile(path);
            tmp.render(
                    new HashMap<String, Object>() {{
                        put("bidFile", Includes.ofLocal(formatFilePath).create());
                    }}
            );
        } catch (Exception e) {
            log.error("投标文件格式文件合并失败");
        } finally {
            //删除投标文件格式文件
            File file = new File(formatFilePath);
            if(file.exists()){
                file.delete();
            }
            //是否删除模板文件待定
        }
    }
}
