package com.hzw.sunflower;

import com.hzw.sunflower.util.MybatisGeneratorUtil;
import com.hzw.sunflower.util.PropertiesFileUtil;

import java.util.HashMap;
import java.util.Map;

/**
 * 代码生成类
 */
public class Generator {

	// 根据命名规范，只修改此常量值即可
	private static String MODULE = "src/main/java/com/hzw/sunflower";
	private static String RESOURCES_MODULE = "src/main/resources/mapper";
	private static String DATABASE = "bid-test52";
	private static String TABLE_PREFIX = "T_";
	private static String TABLE_PREFIX_NO="";
	private static String PACKAGE_NAME = "com.hzw.sunflower";
	private static String JDBC_DRIVER = PropertiesFileUtil.getInstance("generator").get("generator.jdbc.driver");
	private static String JDBC_URL = PropertiesFileUtil.getInstance("generator").get("generator.jdbc.url");
	private static String JDBC_USERNAME = PropertiesFileUtil.getInstance("generator").get("generator.jdbc.username");
	private static String JDBC_PASSWORD = PropertiesFileUtil.getInstance("generator").get("generator.jdbc.password");
	// 需要insert后返回主键的表配置，key:表名,value:主键名
	private static Map<String, String> LAST_INSERT_ID_TABLES = new HashMap<>();

	private static Map<String, String> TABLES = new HashMap<>();
	static {
		//多张表就put多个

/*		TABLES.put("T_SEAL_APPROVE","T_SEAL_APPROVE");
		TABLES.put("T_SEAL_APPLICATION","T_SEAL_APPLICATION");
		TABLES.put("T_SEAL_APPLICATION_INFO","T_SEAL_APPLICATION_INFO");
		TABLES.put("T_SEAL_APPLICATION_PROJECT","T_SEAL_APPLICATION_PROJECT");
		TABLES.put("T_SEAL_BID_WIN","T_SEAL_BID_WIN");
		TABLES.put("T_SEAL_FILE","T_SEAL_FILE");
		TABLES.put("T_SEAL_MANAGEMENT","T_SEAL_MANAGEMENT");*/
//		TABLES.put("T_SEAL_FILE_LOG","T_SEAL_FILE_LOG");
		TABLES.put("T_SEAL_MANAGEMENT_OPERATE_LOG","T_SEAL_MANAGEMENT_OPERATE_LOG");
	}
	/**
	 * 自动代码生成
	 * @param args
	 */
	public static void main(String[] args) throws Exception {
		MybatisGeneratorUtil.TABLE_PREFIX_NO= Generator.TABLE_PREFIX_NO;
		MybatisGeneratorUtil.PREFIX=TABLE_PREFIX;
		Map ret = MybatisGeneratorUtil.generator(JDBC_DRIVER, JDBC_URL, JDBC_USERNAME, JDBC_PASSWORD, MODULE, DATABASE, TABLE_PREFIX, PACKAGE_NAME, LAST_INSERT_ID_TABLES,TABLES,RESOURCES_MODULE);
		MybatisGeneratorUtil.generatorApiController(MODULE, PACKAGE_NAME, ret);
	}

}
