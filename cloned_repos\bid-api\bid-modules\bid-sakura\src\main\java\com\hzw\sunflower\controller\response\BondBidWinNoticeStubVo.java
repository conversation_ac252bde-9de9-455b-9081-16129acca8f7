package com.hzw.sunflower.controller.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2022/7/27 11:31
 * @description：保证金中标通知书存根
 * @modified By：`
 * @version: 1.0
 */
@ApiModel(description = "保证金中标通知书存根")
@Data
public class BondBidWinNoticeStubVo implements Serializable {

    @ApiModelProperty("存根号")
    private String stubNo;

    @ApiModelProperty("项目编号")
    private String projectNum;

    @ApiModelProperty("开标时间")
    private String openBidTime;

    @ApiModelProperty("委托单位")
    private String entrustDept;

    @ApiModelProperty("中标单位")
    private String bidCompany;

    @ApiModelProperty("项目名称")
    private String projectName;

    @ApiModelProperty("中标内容")
    private String bidContent;

    @ApiModelProperty("收费标准")
    private String feeTemplate;

    @ApiModelProperty("收费金额（实收）")
    private String agencyFee;

    @ApiModelProperty("中标金额")
    private String bidMoney;

    @ApiModelProperty("中标金额(大写)")
    private String bigMoney;

    @ApiModelProperty("实收中标费金额")
    private String ssBidMoney;

    @ApiModelProperty("销项税税额")
    private String xxsTax;

    @ApiModelProperty("开标地点")
    private String openBidAddress;

    @ApiModelProperty("付款单位")
    private String payCompany;

    @ApiModelProperty("项目所在地")
    private String projectLocation;

    @ApiModelProperty("备注")
    private String remark;

}
