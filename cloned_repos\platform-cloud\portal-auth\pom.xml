<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.hzw</groupId>
        <artifactId>portal-cloud</artifactId>
        <version>1.0.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>portal-auth</artifactId>

    <description>
        portal-auth 认证授权中心
    </description>

    <dependencies>

        <!-- SpringCloud Alibaba Nacos -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>

        <!-- SpringCloud Alibaba Nacos Config -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>

        <dependency>
            <groupId>com.hzw</groupId>
            <artifactId>portal-common-sentinel</artifactId>
        </dependency>

        <!-- hzw Common Security-->
        <dependency>
            <groupId>com.hzw</groupId>
            <artifactId>portal-common-security</artifactId>
        </dependency>

        <!-- hzw Common Log -->
        <dependency>
            <groupId>com.hzw</groupId>
            <artifactId>portal-common-log</artifactId>
        </dependency>

<!--        <dependency>-->
<!--            <groupId>com.hzw</groupId>-->
<!--            <artifactId>portal-common-core</artifactId>-->
<!--        </dependency>-->

        <dependency>
            <groupId>com.hzw</groupId>
            <artifactId>portal-common-doc</artifactId>
        </dependency>

        <dependency>
            <groupId>com.hzw</groupId>
            <artifactId>portal-common-web</artifactId>
        </dependency>

        <dependency>
            <groupId>com.hzw</groupId>
            <artifactId>portal-common-dubbo</artifactId>
        </dependency>

        <dependency>
            <groupId>com.hzw</groupId>
            <artifactId>portal-common-seata</artifactId>
        </dependency>

        <dependency>
            <groupId>com.hzw</groupId>
            <artifactId>portal-common-loadbalancer</artifactId>
        </dependency>
        <dependency>
            <groupId>com.hzw</groupId>
            <artifactId>portal-api-externalApi</artifactId>
            <version>1.0.0</version>
<!--            <scope>compile</scope>-->
        </dependency>

        <dependency>
            <groupId>com.hzw</groupId>
            <artifactId>portal-common-basebean</artifactId>
        </dependency>
        <dependency>
            <groupId>com.hzw</groupId>
            <artifactId>portal-common-idempotent</artifactId>
        </dependency>

        <dependency>
            <groupId>com.hzw</groupId>
            <artifactId>portal-common-encrypt</artifactId>
        </dependency>

        <dependency>
            <groupId>com.hzw</groupId>
            <artifactId>portal-api-system</artifactId>
        </dependency>

        <dependency>
            <groupId>com.hzw</groupId>
            <artifactId>portal-api-resource</artifactId>
        </dependency>

        <!-- 自定义负载均衡(多团队开发使用) -->
<!--        <dependency>-->
<!--            <groupId>com.hzw</groupId>-->
<!--            <artifactId>portal-common-loadbalancer</artifactId>-->
<!--        </dependency>-->

        <!-- ELK 日志收集 -->
<!--        <dependency>-->
<!--            <groupId>com.hzw</groupId>-->
<!--            <artifactId>portal-common-logstash</artifactId>-->
<!--        </dependency>-->

        <!-- skywalking 日志收集 -->
<!--        <dependency>-->
<!--            <groupId>com.hzw</groupId>-->
<!--            <artifactId>portal-common-skylog</artifactId>-->
<!--        </dependency>-->

        <!-- prometheus 监控 -->
<!--        <dependency>-->
<!--            <groupId>com.hzw</groupId>-->
<!--            <artifactId>portal-common-prometheus</artifactId>-->
<!--        </dependency>-->

    </dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring-boot.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>
