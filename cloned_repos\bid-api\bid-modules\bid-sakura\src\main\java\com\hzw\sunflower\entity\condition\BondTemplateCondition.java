package com.hzw.sunflower.entity.condition;

import com.hzw.sunflower.common.BaseCondition;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date ：Created in 2022/7/27 11:18
 * @description：
 * @modified By：`
 * @version: 1.0
 */
@ApiModel(description = "流水模板")
@Data
public class BondTemplateCondition extends BaseCondition {

    @ApiModelProperty(value = "关键字")
    private String keyWords;

}
