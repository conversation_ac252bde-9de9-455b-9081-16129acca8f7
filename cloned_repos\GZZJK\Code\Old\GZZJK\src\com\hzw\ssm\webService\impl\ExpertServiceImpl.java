package com.hzw.ssm.webService.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.jws.WebService;

import org.springframework.beans.factory.annotation.Autowired;

import com.hzw.ssm.expert.entity.ExpertInfoEntity;
import com.hzw.ssm.fw.base.BaseEntity;
import com.hzw.ssm.fw.util.CommUtil;
import com.hzw.ssm.fw.util.DateUtil;
import com.hzw.ssm.fw.util.JaxbUtil;
import com.hzw.ssm.fw.util.MessageConstants;
import com.hzw.ssm.fw.util.MessageConstants.ErrorType;
import com.hzw.ssm.fw.util.SpringApplicationContext;
import com.hzw.ssm.log.service.WebServiceLogService;
import com.hzw.ssm.sys.login.service.LoginService;
import com.hzw.ssm.sys.project.entity.ConditionEntity;
import com.hzw.ssm.sys.project.entity.ProjectEntity;
import com.hzw.ssm.sys.project.service.ProjectService;
import com.hzw.ssm.webService.ExpertService;
import com.hzw.ssm.webService.input.HeadInput;
import com.hzw.ssm.webService.input.RootInputE;
import com.hzw.ssm.webService.input.RootInputO;
import com.hzw.ssm.webService.input.impl.ExtractExpertInput;
import com.hzw.ssm.webService.input.impl.ObtainExpertInput;
import com.hzw.ssm.webService.output.HeadOutput;
import com.hzw.ssm.webService.output.RootOutput;
import com.hzw.ssm.webService.output.impl.ExpertInfo;
import com.hzw.ssm.webService.output.impl.ExtractExpertOutput;
import com.hzw.ssm.webService.output.impl.ObtainExpertOutput;

/**
 * 对外提供接口实现
 * 
 * <AUTHOR>
 *
 */
@WebService
public class ExpertServiceImpl implements ExpertService {

	@Autowired
	private ProjectService projectService;
	@Autowired
	private WebServiceLogService logService;
	@Autowired
	private LoginService loginService; 

	@Override
	public String extractExpertApply(String input) {
		ErrorType result = null;
		String seqId = "";
		// 1 解析入參
		// 1.1保存入参日志记录
		String serviceId = CommUtil.getKey();
		logService.insertLog(serviceId, "ExtractExpertApply", input);

		// 1.2 入参转换为java bean
		RootInputE rooti = JaxbUtil.converyToJavaBean(input, RootInputE.class);
		// 1.3 入参改为入库对象
		ProjectEntity project = new ProjectEntity();
		ConditionEntity conEntity = new ConditionEntity();
		converyToExtractExpert(rooti, project, conEntity);

		// 2 保存抽取信息，并且生成抽取序号
		// 2.1 抽取条件验证
		ProjectEntity projectval = new ProjectEntity();
		ConditionEntity conEntityval = new ConditionEntity();
		SpringApplicationContext.copyProperties(project, projectval); // 新对象赋值	
		SpringApplicationContext.copyProperties(conEntity, conEntityval);
		project.setOperator("");
		result = valiCondition(rooti, projectval, conEntityval);
		// 2.2 根据序号查询抽取
		if (MessageConstants.ERROR_0000.equals(result.getCode())) {
			result = saveApply(project, conEntity);
			seqId = conEntity.getId().toString();
		}

		// 3 构造出参
		// 3.1构造参数
		RootOutput rooto = new RootOutput();
		HeadOutput heado = new HeadOutput();
		heado.setErrorCode(result.getCode());
		heado.setErrorMsg(result.getMsg());
		heado.setTimeStamp(DateUtil.getCurrentDateTime("yyyyMMddhhmmssSSS"));
		ExtractExpertOutput bodyo = new ExtractExpertOutput();
		bodyo.setExtSeq(seqId);
		rooto.setHead(heado);
		rooto.setBody(bodyo);
		String result_msg = JaxbUtil.convertToXml(rooto);
		// 3.2保存出参日志
		logService.updateLog(serviceId, conEntity.getId(), result_msg);
		return result_msg;
	}

	@Override
	public String obtainExpert(String input) {
		ErrorType result = new ErrorType();
		/** 专家列表 */
		List<ExpertInfoEntity> expertInfoList = null;
		// 1 解析入參
		// 1.1保存入参日志记录
		String serviceId = CommUtil.getKey();
		logService.insertLog(serviceId, "ObtainExpert", input);

		// 1.2 入参转换为java bean
		RootInputO rooti = JaxbUtil.converyToJavaBean(input, RootInputO.class);
		// 1.3 入参改为入库对象
		ProjectEntity project = new ProjectEntity();
		ConditionEntity conEntity = new ConditionEntity();
		converyToObtain(rooti, project, conEntity);

		// 2 保存查询条件
		// 2.1 查询条件验证
		result = valiSeq(rooti, project, conEntity);
		// 2.2 抽取条件入库
		if (MessageConstants.ERROR_0000.equals(result.getCode())) {
			Long method = project.getMethod();
			conEntity = projectService.queryConditionById(conEntity);
			if (conEntity != null) {
				project.setProjectId(conEntity.getProjectId());
				project = projectService.queryProjectById(project);
				if (project == null) { // 项目信息不存在
					result.setCode(MessageConstants.ERROR_0015);
					result.setMsg(MessageConstants.ERROR_0015_MSG);
				} else if (project.getStatus().intValue() == 1) { // 项目状态 1：待抽取
					result.setCode(MessageConstants.ERROR_0021);
					result.setMsg(MessageConstants.ERROR_0021_MSG);
				} else if (project.getStatus().intValue() == 2) { // 项目状态 //
																	// 2:抽取中
					result.setCode(MessageConstants.ERROR_0022);
					result.setMsg(MessageConstants.ERROR_0022_MSG);
				} else if (project.getStatus().intValue() == 4) { // 项目状态 //
																	// 4：指定抽取待审核
					result.setCode(MessageConstants.ERROR_0023);
					result.setMsg(MessageConstants.ERROR_0023_MSG);
				} else if (project.getStatus().intValue() == 5) { // 项目状态 //
																	// 5：提交领导-处长指定抽取审核通过
					result.setCode(MessageConstants.ERROR_0024);
					result.setMsg(MessageConstants.ERROR_0024_MSG);
				} else if (project.getStatus().intValue() == 6) { // 项目状态 //
																	// 6：处长指定抽取审核不通过
					result.setCode(MessageConstants.ERROR_0025);
					result.setMsg(MessageConstants.ERROR_0025_MSG);
				} else if (project.getStatus().intValue() == 8) { // 项目状态 //
																	// 8：领导审核通过
					result.setCode(MessageConstants.ERROR_0026);
					result.setMsg(MessageConstants.ERROR_0026_MSG);
				} else if (project.getStatus().intValue() == 9) { // 项目状态 //
																	// 9：领导审核不通过
					result.setCode(MessageConstants.ERROR_0027);
					result.setMsg(MessageConstants.ERROR_0027_MSG);
				} else if (project.getStatus().intValue() == 3 || project.getStatus().intValue() == 10) { // 3：已抽取
																											// 10：指定专家已抽取
					result.setCode(MessageConstants.ERROR_0000);
					result.setMsg(MessageConstants.ERROR_0000_MSG);
					// 项目抽取成功，获取专家名单
					// 标注为项目指定专家详细信息页面
					if (null == method || 4L == method) {
						ConditionEntity conditionEntity = new ConditionEntity();
						conditionEntity.setProjectId(project.getProjectId());
						conditionEntity.setJoin_status(0L); // 参标
						expertInfoList = projectService.queryExtractedResult(conditionEntity);
					}
					// 查询参评专家信息
					if (null != method && 4L != method && -2L != method) {
						ConditionEntity conditionEntity = new ConditionEntity();
						conditionEntity.setProjectId(project.getProjectId());
						conditionEntity.setJoin_status(0L); // 参标
						expertInfoList = projectService.queryExtractedResult(conditionEntity);
					}
				} else { // 其它未知情况 数据库中出现了类型11 但是代码中没有查到
					result.setCode(MessageConstants.ERROR_9999);
					result.setMsg(MessageConstants.ERROR_9999_MSG);
				}

			} else {
				// 抽取条件不存在
				result.setCode(MessageConstants.ERROR_0014);
				result.setMsg(MessageConstants.ERROR_0014_MSG);
			}

		}

		// 3 构造出参
		// 3.1构造参数
		RootOutput rooto = new RootOutput();
		HeadOutput heado = new HeadOutput();
		heado.setErrorCode(result.getCode());
		heado.setErrorMsg(result.getMsg());
		heado.setTimeStamp(DateUtil.getCurrentDateTime("yyyyMMddhhmmssSSS"));
		ObtainExpertOutput bodyo = new ObtainExpertOutput();
		List<ExpertInfo> exprtList = new ArrayList<ExpertInfo>();
		if (expertInfoList != null) {
			for (ExpertInfoEntity expertInfoEntity : expertInfoList) { // 封装出参数据类型
				ExpertInfo expertInfo = new ExpertInfo();
				expertInfo.setExpertName(expertInfoEntity.getUser_name());
				expertInfo.setExpertPhone(expertInfoEntity.getMobilephone());
				expertInfo.setNoticeType(new Integer(conEntity.getMethod().toString()));
				expertInfo.setNoticeTime(DateUtil.getFormatDateTime("yyyy-MM-dd HH:mm:ss",
						expertInfoEntity.getCall_time()));
				expertInfo.setIdType(expertInfoEntity.getId_type());
				expertInfo.setIdNo(expertInfoEntity.getId_no());
				expertInfo.setUserId(expertInfoEntity.getId());
				expertInfo.setUserName(expertInfoEntity.getUser_id());
				expertInfo.setCompany(expertInfoEntity.getCompany());
				exprtList.add(expertInfo);
			}
		}
		bodyo.setExprtList(exprtList);
		rooto.setHead(heado);
		rooto.setBody(bodyo);
		String result_msg = JaxbUtil.convertToXml(rooto);
		// 3.2保存出参日志
		logService.updateLog(serviceId, conEntity!=null?conEntity.getId():"", result_msg);
		return result_msg;
	}

	@Override
	public String appraiseExpert(String input) {
		// TODO Auto-generated method stub
		return null;
	}

	/**
	 * 设置默认的值，如操作用户编号，更新时间戳等
	 * 
	 * @param entity
	 *            信息实体对象
	 * @throws Exception
	 */
	public void setOpaUserAndDate(BaseEntity entity) {

		Date opaDate = new Date();
		entity.setCreate_id("");
		entity.setCreate_time(opaDate);
		entity.setModify_id("");
		entity.setModify_time(opaDate);
		entity.setDelete_flag(0);
	}

	/**
	 * 验证抽取条件 函数功能描述：TODO
	 * 
	 * @param project
	 * @param conEntity
	 */
	private ErrorType valiSeq(RootInputO rooti, ProjectEntity project, ConditionEntity conEntity) {
		ErrorType errorType = new ErrorType();
		errorType.setCode(MessageConstants.ERROR_0000);
		errorType.setMsg(MessageConstants.ERROR_0000_MSG);
		// 1 参数合法性校验
		HeadInput head = rooti.getHead();
		ObtainExpertInput body = rooti.getBody();
		try {

			// 1.1校验平台是否符合规范 0001
			if (head.getPlatformcode() == null || !head.getPlatformcode().equals("szyc")) {
				errorType.setCode(MessageConstants.ERROR_0001);
				errorType.setMsg(MessageConstants.ERROR_0001_MSG);
				return errorType;
			}
			// 1.2校验秘钥是否符合规范 0002
			if (head.getPlatformkey() == null || !head.getPlatformkey().equals("aaaa")) {
				errorType.setCode(MessageConstants.ERROR_0002);
				errorType.setMsg(MessageConstants.ERROR_0002_MSG);
				return errorType;
			}
			// 1.3校验时间戳是否符合规范 0003
			/*if (head.getTimeStamp() == null || head.getTimeStamp().length() != 17
					|| !head.getTimeStamp().matches("^\\d{17}")) {
				errorType.setCode(MessageConstants.ERROR_0003);
				errorType.setMsg(MessageConstants.ERROR_0003_MSG);
				return errorType;
			}*/
			// 1.4校验项目编号是否存在 0004
			if (body.getProjectNum() == null || body.getProjectNum().equals("")) {
				errorType.setCode(MessageConstants.ERROR_0004);
				errorType.setMsg(MessageConstants.ERROR_0004_MSG);
				return errorType;
			}
			// 1.5校验项目名称是否存在 0005
			if (body.getProjectName() == null || body.getProjectName().equals("")) {
				errorType.setCode(MessageConstants.ERROR_0005);
				errorType.setMsg(MessageConstants.ERROR_0005_MSG);
				return errorType;
			}

			// 1.6校验查询条件是否合法
			if (body.getExtSeq() == null || body.getExtSeq().equals("")) {
				errorType.setCode(MessageConstants.ERROR_0005);
				errorType.setMsg(MessageConstants.ERROR_0005_MSG);
				return errorType;
			}

		} catch (Exception e) {
			errorType.setCode(MessageConstants.ERROR_9999);
			errorType.setMsg(MessageConstants.ERROR_9999_MSG);
		}
		return errorType;
	}

	/**
	 * 验证抽取条件 函数功能描述：TODO
	 * 
	 * @param project
	 * @param conEntity
	 */
	private ErrorType valiCondition(RootInputE rooti, ProjectEntity project, ConditionEntity conEntity) {
		ErrorType errorType = new ErrorType();
		errorType.setCode(MessageConstants.ERROR_0000);
		errorType.setMsg(MessageConstants.ERROR_0000_MSG);
		// 1 参数合法性校验
		HeadInput head = rooti.getHead();
		ExtractExpertInput body = rooti.getBody();
		try {

			// 1.1校验平台是否符合规范 0001
			if (head.getPlatformcode() == null || !head.getPlatformcode().equals("szyc")) {
				errorType.setCode(MessageConstants.ERROR_0001);
				errorType.setMsg(MessageConstants.ERROR_0001_MSG);
				return errorType;
			}
			// 1.2校验秘钥是否符合规范 0002
			if (head.getPlatformkey() == null || !head.getPlatformkey().equals("aaaa")) {
				errorType.setCode(MessageConstants.ERROR_0002);
				errorType.setMsg(MessageConstants.ERROR_0002_MSG);
				return errorType;
			}
			// 1.3校验时间戳是否符合规范 0003
			if (head.getTimeStamp() == null || head.getTimeStamp().length() != 17
					|| !head.getTimeStamp().matches("^\\d{17}")) {
				errorType.setCode(MessageConstants.ERROR_0003);
				errorType.setMsg(MessageConstants.ERROR_0003_MSG);
				return errorType;
			}
			// 1.4校验项目编号是否存在 0004
			if (body.getProjectNum() == null || body.getProjectNum().equals("")) {
				errorType.setCode(MessageConstants.ERROR_0004);
				errorType.setMsg(MessageConstants.ERROR_0004_MSG);
				return errorType;
			}
			// 1.5校验项目名称是否存在 0005
			if (body.getProjectName() == null || body.getProjectName().equals("")) {
				errorType.setCode(MessageConstants.ERROR_0005);
				errorType.setMsg(MessageConstants.ERROR_0005_MSG);
				return errorType;
			}
			// 1.6校验委托单位不能为空 0006
			if (body.getEntrustingUnit() == null || body.getEntrustingUnit().equals("")) {
				errorType.setCode(MessageConstants.ERROR_0006);
				errorType.setMsg(MessageConstants.ERROR_0006_MSG);
				return errorType;
			}
			// 1.7校验开标时间必须是开标前一个工作日 0007

			// 1.8校验专家人数是否符合 0008
			if (body.getSeniorNum() + body.getLocalNum() != body.getTotal()) {
				errorType.setCode(MessageConstants.ERROR_0008);
				errorType.setMsg(MessageConstants.ERROR_0008_MSG);
				return errorType;
			}

			// 1.9校验专业是否超过20个 0009
			String[] types = body.getExpertType().split(";");
			if (types.length > 20) {
				errorType.setCode(MessageConstants.ERROR_0009);
				errorType.setMsg(MessageConstants.ERROR_0009_MSG);
				return errorType;
			}
			// 2 库中专家是否满足要求
			// 具体功能copy自ProjectAction valExtractionExport方法
			String tender = "";
			if (null != project.getTender() && !"".equals(project.getTender()) && project.getTender().indexOf(",") > -1) {
				String[] tenderSplit = project.getTender().split(",");
				for (int i = 0; i < tenderSplit.length; i++) {
					tender += "'" + tenderSplit[i] + "',";
				}
				tender = tender.substring(0, tender.length() - 1);
			} else {
				tender = "'" + project.getTender() + "'";
			}
			conEntity.setBidTimeDay(DateUtil.dateToString(project.getBidTime()));
			conEntity.setTender(tender);
			// 为了兼容以前的方法减少改动，没有重新定义错误信息，而是采用转译的方式解决
			List<ProjectEntity> projectList = new ArrayList<ProjectEntity>();
			projectList.add(project);
			String msg = projectService.queryExpertToCheck(conEntity,projectList);
			if (MessageConstants.NOT_ENOUGH_EXPERT.equals(msg)) {
				errorType.setCode(MessageConstants.ERROR_0010);
				errorType.setMsg(MessageConstants.NOT_ENOUGH_EXPERT);
			} else if (MessageConstants.NOT_ENOUGH_SENIOR_EXPERT.equals(msg)) {
				errorType.setCode(MessageConstants.ERROR_0011);
				errorType.setMsg(MessageConstants.NOT_ENOUGH_SENIOR_EXPERT);
			} else if (MessageConstants.NOT_ENOUGH_PLACE_EXPERT.equals(msg)) {
				errorType.setCode(MessageConstants.ERROR_0012);
				errorType.setMsg(MessageConstants.NOT_ENOUGH_PLACE_EXPERT);
			} else if ("success".equals(msg)) {
				errorType.setCode(MessageConstants.ERROR_0000);
				errorType.setMsg(MessageConstants.ERROR_0000_MSG);
			}
		} catch (Exception e) {
			errorType.setCode(MessageConstants.ERROR_9999);
			errorType.setMsg(MessageConstants.ERROR_9999_MSG);
		}
		return errorType;
	}

	/**
	 * java bean类型转化 函数功能描述：将web service类型改为抽取条件信息 (这里还需要注意，后期可能改为多项目多分包)
	 * 
	 * @param rooti
	 *            web service入参
	 * @param project
	 *            项目信息
	 * @param conEntity
	 *            抽取条件信息
	 */
	private void converyToExtractExpert(RootInputE rooti, ProjectEntity project, ConditionEntity conEntity) {
		// 获取请求核心内容
		ExtractExpertInput body = rooti.getBody();
		HeadInput head = rooti.getHead();
				
		// 1 构造项目对象
		if (project == null)
			project = new ProjectEntity();
		// 项目编号
		project.setProjectNo(body.getProjectNum());
		// 项目名称
		project.setProjectName(body.getProjectName());
		// 委托单位
		project.setTender(body.getEntrustingUnit());
		// 项目负责人（必须是负责人员唯一标识符）
		project.setManager(body.getChargePerson());
		// 联系方式
		
		project.setPhone(body.getContactWay());
		// 处室（部门）
		project.setDepartment(body.getDepartment());
		// 经办人
		project.setOperator(body.getHandlder());
		// 开标时间
		project.setBidTime(DateUtil.formatStringByChar(body.getOpenBidTime(), "yyyyMMddHHmmss"));
		// 开标地点
		project.setBidAddress(body.getOpenBidPlace());
		// 代理机构
		project.setAgent(body.getAgency());
		// 备注
		project.setRemark(body.getRemarks());
		// 状态 此状态默认为1
		project.setStatus(1L);
		// 项目编号
		project.setProjectId(CommUtil.getKey());

		// 2 构造条件对象
		if (conEntity == null)
			conEntity = new ConditionEntity();
		// 专家抽取总人数
		conEntity.setTotal(new Long(body.getTotal()));
		// 地区编码 根据地区名称去数据库查询
		conEntity.setZone(body.getZone());
		// 地区名称
		conEntity.setZoneName(body.getZoneName());
		// 专家级别 国家级专家人数
		conEntity.setSeniorNum(new Long(body.getSeniorNum()));
		// 专家级别 地方级专家人数 
		conEntity.setLocalNum(new Long(body.getLocalNum()));
		// 专业类别
		conEntity.setExpertType(body.getExpertType());
		conEntity.setExpertTypeName(body.getExpertTypeName());
		// 抽取方式
		// 目前还没有语音通知所以抽取目前默认都是2 重要
		conEntity.setMethod(2L);
		// 抽取编号
		conEntity.setId(CommUtil.getKey());
		//项目id
		conEntity.setProjectId(project.getProjectId());
		//请求来源
		conEntity.setSourceCode(head.getPlatformcode());

	}

	/**
	 * java bean类型转化 函数功能描述：将web service类型改为抽取条件信息 (这里还需要注意，后期可能改为多项目多分包)
	 * 
	 * @param rooti
	 *            web service入参
	 * @param project
	 *            项目信息
	 * @param conEntity
	 *            抽取条件信息
	 */
	private void converyToObtain(RootInputO rooti, ProjectEntity project, ConditionEntity conEntity) {
		// 获取请求核心内容
		ObtainExpertInput body = rooti.getBody();
		if (project == null) {
			project = new ProjectEntity();
		}
		// 项目名称
		project.setProjectNo(body.getProjectNum());
		// 项目编号
		project.setProjectName(body.getProjectName());

		if (conEntity == null) {
			conEntity = new ConditionEntity();
		}
		// 抽取条件编号
		conEntity.setId(body.getExtSeq());
	}

	/**
	 * 保存抽取申请
	 * 
	 * @param root
	 */
	private ErrorType saveApply(ProjectEntity project, ConditionEntity conEntity) {
		ErrorType result = new ErrorType();
		try {
			this.setOpaUserAndDate(project);
			this.setOpaUserAndDate(conEntity);
			projectService.saveProject(project, conEntity);
			// 申请抽取之后向抽取人发送通知短信
			if (1 == project.getStatus().intValue()) {
				projectService.smsExtractMobileService(project);
			}
			result.setCode(MessageConstants.ERROR_0000);
			result.setMsg(MessageConstants.ERROR_0000_MSG);
		} catch (Exception e) {
			// 保存抽取条件失败
			result.setCode(MessageConstants.ERROR_0101);
			result.setMsg(MessageConstants.ERROR_0101_MSG);
		}

		return result;
	}

	/**
	 * 转换项目信息
	 * 
	 * @param root
	 * @return
	 */
	private ProjectEntity converyToProject(RootInputE root) {
		ProjectEntity project = new ProjectEntity();
		ExtractExpertInput input = root.getBody();
		project.setProjectNo(input.getProjectNum());
		project.setProjectName(input.getProjectName());
		// project.set
		return project;
	}

}
