package com.hzw.sunflower.constant.constantenum;

/**
 * 招标阶段
 *
 * <AUTHOR>
 */
public enum ProcessStatusEnum {
    handled(1, "已处理"),
    handling(2, "处理中"),
    wait_handle(3, "待处理");
    private Integer type;
    private String desc;

    ProcessStatusEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
