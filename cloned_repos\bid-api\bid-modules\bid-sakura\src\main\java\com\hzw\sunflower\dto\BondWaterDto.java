package com.hzw.sunflower.dto;

import com.hzw.sunflower.controller.response.BondSectionExportVo;
import com.hzw.sunflower.entity.BondWater;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2022/8/3 17:26
 * @description：
 * @modified By：`
 * @version: 1.0
 */
@ApiModel(description = "收款流水")
@Data
public class BondWaterDto extends BondWater implements Serializable {

    @ApiModelProperty("项目编号")
    private String purchaseNumber;

    @ApiModelProperty("处室")
    private String departmentName;

    @ApiModelProperty("关联状态（1.确认关联 2.已关联 3.异常关联 4.未关联）")
    private Integer relationStatus;

    @ApiModelProperty("关联时间")
    private String relationTime;

    @ApiModelProperty("已收款未退还关联标包")
    List<BondSectionExportVo> sectionExportList;

    @ApiModelProperty("序号")
    private Integer rowNumber;

    @ApiModelProperty("包号")
    private Integer packageNumber;

    @ApiModelProperty("标包拆分金额")
    private BigDecimal splitAmount;

    @ApiModelProperty("退还状态 1：待处理  2：已退回    3：退还成功 4：退还失败  5.退还中")
    private Integer returnStatus;

    @ApiModelProperty("回单oss文件id")
    private Integer receiptOssFileId;

    @ApiModelProperty("存在在线退还数量")
    private Integer returnaApplyNumer;

    @ApiModelProperty("存在退还数量")
    private Integer returnaNumer;

    @ApiModelProperty("包号集合")
    private String packageNumberGroup;

}
