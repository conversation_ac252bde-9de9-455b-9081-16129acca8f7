package com.hzw.sunflower.controller.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 招标文件变更记录请求实体
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@ApiModel(description = "招标文件变更记录请求实体")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DocChangeRecordREQ {

    @ApiModelProperty(value = "招标文件主键", position = 1)
    private Long docId;

    @ApiModelProperty(value = "标段主键", position = 2)
    private Long bidId;

    @ApiModelProperty(value = "文件变更次数", position = 3)
    private Integer submitNumber;

    @ApiModelProperty(value = "标段变更次数", position = 4)
    private Integer bidSubmitNumber;
}
