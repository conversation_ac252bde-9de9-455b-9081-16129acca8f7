package com.hzw.sunflower.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date ：Created in 2021/9/10 10:53
 * @description：
 * @modified By：`
 * @version: 1.0
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class SupplierInfoDTO {

    @ApiModelProperty(value = "报名情况ID", position = 1)
    private Long applyId;

    @ApiModelProperty(value = "报名公司名称", position = 2)
    private String appplyCompany;

    @ApiModelProperty(value = "报名状态", position = 3)
    private Integer applyStatus;

    @ApiModelProperty(value = "付款情况ID", position = 4)
    private Long payId;

    @ApiModelProperty(value = "付款公司名称", position = 5)
    private String payCompany;

    @ApiModelProperty(value = "付款状态", position = 6)
    private Integer payStatus;

    @ApiModelProperty(value = "原报名情况ID", position = 7)
    private Long formerApplyId;
}
