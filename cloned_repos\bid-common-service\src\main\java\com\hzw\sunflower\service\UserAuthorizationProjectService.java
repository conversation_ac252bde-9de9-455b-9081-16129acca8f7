package com.hzw.sunflower.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hzw.sunflower.entity.UserAuthorizationProject;


/**
 * OpenService接口
 */
public interface UserAuthorizationProjectService extends IService<UserAuthorizationProject> {

    /**
     * 修改用户法人授权函
     * @param userId
     * @param identity
     * @param legalPersonAuthorization
     * @return
     */
    Boolean updateUserPersonAuthorization(Long userId, Integer identity, Long legalPersonAuthorization);
}