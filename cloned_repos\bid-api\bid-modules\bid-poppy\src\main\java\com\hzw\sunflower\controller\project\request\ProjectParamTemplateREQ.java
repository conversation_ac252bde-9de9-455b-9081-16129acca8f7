package com.hzw.sunflower.controller.project.request;


import com.hzw.sunflower.controller.project.response.ProjectParamTemplateVO;
import io.swagger.annotations.ApiModel;
import lombok.Builder;
import lombok.Data;

/**
 * 项目参数表 请求实体
 *
 * <AUTHOR>
 * @version 1.0.0 2021-04-14
 */
@ApiModel(description = "项目参数表 ")
@Builder
@Data
public class ProjectParamTemplateREQ extends ProjectParamTemplateVO {

}