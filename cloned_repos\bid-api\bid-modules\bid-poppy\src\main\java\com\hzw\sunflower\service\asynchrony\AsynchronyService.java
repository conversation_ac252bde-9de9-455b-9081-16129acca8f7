package com.hzw.sunflower.service.asynchrony;

import com.hzw.sunflower.controller.project.request.ReturnListConditionReq;
import com.hzw.sunflower.entity.JwtUser;
import org.springframework.scheduling.annotation.Async;

/**
 * 异步调用 Service
 */
public interface AsynchronyService {

    /**
     * 异步刷新缓存的数据-退回待处理事项的数量
     * @param condition
     * @param jwtUser
     * @param datascopesql
     */
    @Async
    void refreshRedis(ReturnListConditionReq condition, JwtUser jwtUser, String datascopesql);

}
