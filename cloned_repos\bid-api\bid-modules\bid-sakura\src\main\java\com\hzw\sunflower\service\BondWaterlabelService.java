package com.hzw.sunflower.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.entity.BondWaterlabel;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @ClassName:BondWaterlabelService
 * @Auther: lijinxin
 * @Description: 保证金流水标签
 * @Date: 2023/5/10 16:47
 * @Version: v1.0
 */
public interface BondWaterlabelService extends IService<BondWaterlabel> {

    public Result<Object> add(BondWaterlabel bondWaterlabel);

    /**
     * 保证金流水标签校验
     * 判断最新的标签是不是收款标签
     * 最新的状态不是收款返回true
     * @param waterId
     * @return
     */
    Boolean getWaterlabelCheck(@Param("waterId") Long waterId);

    /**
     * 校验批量流水标签
     * 返回不是收款流水的数量
     * @param waterIds
     * @return
     */
    Integer chekecWaterLabel(@Param("waterIds") List<Long> waterIds);
}
