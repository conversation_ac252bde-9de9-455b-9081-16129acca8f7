package com.hzw.sunflower.controller.response;


import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProcessAddress  {

    @ApiModelProperty(value = "主键")
    private Long code;

    @ApiModelProperty(value = "进场场地")
    private String address;

}