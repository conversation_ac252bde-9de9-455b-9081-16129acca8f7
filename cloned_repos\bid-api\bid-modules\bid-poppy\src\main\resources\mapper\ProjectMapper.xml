<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!-- 项目表 (t_project) -->
<mapper namespace="com.hzw.sunflower.dao.ProjectMapper">

    <sql id="queryWaitListSql">
        select * from
        (
            SELECT
            DISTINCT
            t.*
            FROM
            v_pending_item t
            LEFT JOIN (
                SELECT DISTINCT
                v.project_id
                FROM
                v_user_edit_project v
                WHERE
                    1 = 1  ${dataScope}
            ) AS subquery ON t.project_id = subquery.project_id
        where t.applyRefundUserId = #{condition.userId}
           OR subquery.project_id IS NOT NULL

        ) tt
        where 1 = 1
        <if test="condition.keyWords != null and condition.keyWords != ''">
            and (
            tt.projectName like concat('%',#{condition.keyWords},'%')
            or
            tt.projectNum like concat('%',#{condition.keyWords},'%')
            )
        </if>
        <if test="condition.type != null and condition.type != ''">
            and tt.type = #{condition.type}
        </if>
        <if test="condition.types != null and condition.types.size > 0 ">
            and tt.type in
            <foreach collection="condition.types" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="condition.projectId != null and condition.projectId != ''">
            and tt.project_id = #{condition.projectId}
        </if>
        order by tt.orderNum asc , tt.time desc
    </sql>

    <!--根据条件分页查询项目表 列表-->
    <select id="queryProjectByPage" resultType="com.hzw.sunflower.dto.ProjectListDTO">
        SELECT * from (

        SELECT a.*,count(tps.id) has_open_count from (

        SELECT
            distinct p.id,
            p.project_name projectName,
            p.project_number projectNumber,
            p.purchase_name purchaseName,
            p.purchase_number purchaseNumber,
            p.principal_company principalCompany,
            p.principal_charge_name principalChargeName,
            p.agent_company agentCompany,
            p.agent_charge_name agentChargeName,
            p.refuse_reason refuseReason,
            p.package_segment_status packageSegmentStatus,
            p.status status,
            p.operation_flow,
            p.project_status packagesStatus,
            p.project_status_code packagesStatusCode,
            p.created_time created_time ,
            p.base_project_id,
            p.re_tender reTender,
            tpe.user_id userId,
            tpe.department_id,
            te.`status` delegateStatus
        FROM
            t_project p
        left join t_project_entrust_user tpe  on p.id = tpe.project_id
        LEFT JOIN r_user_department rud on tpe.user_id = rud.user_id  and tpe.department_id = rud.department_id
        LEFT JOIN t_project_bid_section s ON p.id = s.project_id AND s.is_delete = 0
        LEFT JOIN t_bond b ON b.project_id = p.id AND b.is_delete = 0
        LEFT JOIN (SELECT max( te.id ) id,te.project_id FROM t_project_turn_entrust te WHERE te.is_delete = 0 GROUP BY te.project_id) t ON t.project_id = p.id
        LEFT JOIN t_project_turn_entrust te ON te.id = t.id AND te.is_delete = 0
        WHERE
        p.is_delete = 0
        and tpe.is_delete = 0
        and tpe.type= #{condition.type}
        and p.status not in (1,5)
        <if test="null != condition.projectSource and condition.projectSource == 1">
            and tpe.department_id = #{condition.userDepartId}
            and tpe.user_id = #{condition.userId}
        </if>
        <if test="null != condition.projectSource  and condition.projectSource == 2">
            and (tpe.user_id != #{condition.userId} or tpe.department_id != #{condition.userDepartId})
        </if>
        <if test="null != condition.departmentIds and condition.departmentIds.length>0">
            and rud.department_id in
            <foreach collection="condition.departmentIds" item="departmentIds" open="(" separator="," close=")">
                #{departmentIds}
            </foreach>
        </if>
        <if test="condition.keyWords != null and !&quot;&quot;.equals(condition.keyWords)">
            and CONCAT(IFNULL(`project_name`,''), IFNULL(`project_number`,''), IFNULL(`purchase_name`,''),
            IFNULL(`purchase_number`,'')) LIKE concat('%',#{condition.keyWords},'%')
        </if>
        <if test="condition.projectName != null and !&quot;&quot;.equals(condition.projectName)">
            and p.project_name like concat('%',#{condition.projectName},'%')
        </if>
        <if test="condition.projectNumber != null and !&quot;&quot;.equals(condition.projectNumber)">
            and p.project_number like concat('%',#{condition.projectNumber},'%')
        </if>
        <if test="condition.purchaseName != null and !&quot;&quot;.equals(condition.purchaseName)">
            and p.purchase_name like concat('%',#{condition.purchaseName},'%')
        </if>
        <if test="condition.purchaseNumber != null and !&quot;&quot;.equals(condition.purchaseNumber)">
            and p.purchase_number like concat('%',#{condition.purchaseNumber},'%')
        </if>
        <if test="condition.addressProvince != null and !&quot;&quot;.equals(condition.addressProvince) and condition.addressProvince != '0' ">
            and p.address_province = #{condition.addressProvince}
        </if>
        <if test="condition.addressCity != null and !&quot;&quot;.equals(condition.addressCity)  and condition.addressCity != '0'">
            and p.address_city = #{condition.addressCity}
        </if>
        <if test="condition.agentCompany != null and !&quot;&quot;.equals(condition.agentCompany)">
            and p.agent_company like concat('%',#{condition.agentCompany},'%')
        </if>
        <if test="condition.agentChargeName != null and !&quot;&quot;.equals(condition.agentChargeName)">
            and p.agent_charge_name like concat('%',#{condition.agentChargeName},'%')
        </if>
        <if test="condition.agentChargePhone != null and !&quot;&quot;.equals(condition.agentChargePhone)">
            and p.agent_charge_phone like concat('%',#{condition.agentChargePhone},'%')
        </if>
        <if test="condition.principalCompany != null and !&quot;&quot;.equals(condition.principalCompany)">
            and p.principal_company like concat('%',#{condition.principalCompany},'%')
        </if>
        <if test="condition.principalChargeName != null and !&quot;&quot;.equals(condition.principalChargeName)">
            and p.principal_charge_name like concat('%',#{condition.principalChargeName},'%')
        </if>
        <if test="condition.principalChargePhone != null and !&quot;&quot;.equals(condition.principalChargePhone)">
            and p.principal_charge_phone like concat('%',#{condition.principalChargePhone},'%')
        </if>
        <if test="condition.projectStatus != null and !&quot;&quot;.equals(condition.projectStatus)">
            and p.project_status like concat('%',#{condition.projectStatus},'%')
        </if>
        <if test="condition.projectStatusCode != null and !&quot;&quot;.equals(condition.projectStatusCode)">
            and s.status &gt;= #{condition.projectStatusCode}
        </if>
        <if test="condition.beginTime != null and !&quot;&quot;.equals(condition.beginTime)">
            and DATE_FORMAT(p.created_time,'%Y-%m-%d') &gt;= #{condition.beginTime}
        </if>
        <if test="condition.endTime != null and !&quot;&quot;.equals(condition.endTime)">
            and DATE_FORMAT(p.created_time,'%Y-%m-%d') &lt;= #{condition.endTime}
        </if>
        <if test="condition.submitBeginTime != null and !&quot;&quot;.equals(condition.submitBeginTime)">
            and DATE_FORMAT(s.submit_end_time,'%Y-%m-%d') &gt;= #{condition.submitBeginTime}
        </if>
        <if test="condition.submitEndTime != null and !&quot;&quot;.equals(condition.submitEndTime)">
            and DATE_FORMAT(s.submit_end_time,'%Y-%m-%d') &lt;= #{condition.submitEndTime}
        </if>
        <if test="condition.bidOpenType != null  and condition.bidOpenType == 1">
            AND (s.`status` &gt;=30 AND s.`status` NOT IN ( 70, 71, 72 ))
            AND (b.id is null or b.`status` != 3)
        </if>
        <if test="condition.bidOpenType != null  and condition.bidOpenType == 2">
            AND (s.`status` &gt;= 30 AND s.`status` &lt; 46)
        </if>
        <if test="condition.dataScope != null  and condition.dataScope !=''">
            ${condition.dataScope}
        </if>
            GROUP BY
                p.id,
            p.project_name ,
            p.project_number ,
            p.purchase_name ,
            p.purchase_number ,
            p.principal_company ,
            p.principal_charge_name ,
            p.agent_company ,
            p.agent_charge_name ,
            p.refuse_reason ,
            p.operation_flow,
            p.package_segment_status ,
            p.STATUS ,
            p.project_status ,
            p.project_status_code ,
            p.created_time ,
            p.base_project_id,
            p.re_tender ,
            tpe.user_id ,
            tpe.department_id,
            te.`status`
        ) a left join t_project_bid_section tps on tps.project_id = a.id
            AND (
            (
            tps.`status` >= 30
            AND tps.`status` NOT IN ( 70, 71, 72 ))
            OR (
            tps.former_abnormal_status >= 30
            AND tps.`status` IN ( 70, 71, 72 ))
            )

        GROUP BY
            a.id,
        a.projectName,
        a.projectNumber,
        a.purchaseName,
        a.purchaseNumber,
        a.principalCompany,
        a.principalChargeName,
        a.agentCompany,
        a.agentChargeName,
        a.refuseReason,
        a.operation_flow,
        a.packageSegmentStatus,
        a.STATUS,
        a.packagesStatus,
        a.packagesStatusCode,
        a.created_time,
        a.base_project_id,
        a.reTender,
        a.userId,
        a.department_id,
        a.delegateStatus

        )  pbst where 1=1
        and `status` not in (1,5)
        <if test="condition.searchType != null  and condition.searchType == 1">
            and `status` != 9
            AND has_open_count = 0
        </if>
        <if test="condition.searchType != null  and condition.searchType == 2">
            and `status` != 9
            AND has_open_count > 0
        </if>
        <if test="condition.searchType != null  and condition.searchType == 3">
            AND (
                `status` = 9
                OR (
                    id IN (
                            select a.project_id from
                            (
                            select pbs.project_id,count(pbs.id) as totalNum from t_project_bid_section pbs
                            where pbs.is_delete = 0
                            GROUP BY pbs.project_id
                            ) a
                            left join
                            (
                            select pbs.project_id,count(pbs.id) as sectionNum from t_project_bid_section pbs
                            where pbs.is_delete = 0
                            and pbs.`status` = 54
                            and pbs.bid_round = 2
                            GROUP BY pbs.project_id
                            ) b
                            on a.project_id = b.project_id
                            where a.totalNum = b.sectionNum
                            order by a.project_id desc
                    )
                )
            )
        </if>
        <if test="condition.sortOrderSql != null and !&quot;&quot;.equals(condition.sortOrderSql)">
            order by ${condition.sortOrderSql}
        </if>
    </select>

    <!--招标人根据条件分页查询项目表 列表-->
    <select id="queryBidderProjectByPage" resultType="com.hzw.sunflower.dto.ProjectListDTO">
        SELECT
        distinct
        p.id,
        p.project_name projectName,
        p.project_number projectNumber,
        p.purchase_name purchaseName,
        p.purchase_number purchaseNumber,
        p.principal_company principalCompany,
        p.principal_charge_name principalChargeName,
        p.agent_company agentCompany,
        p.agent_charge_name agentChargeName,
        p.refuse_reason refuseReason,
        p.package_segment_status packageSegmentStatus,
        CASE
        p.package_segment_status
        WHEN 0 THEN 0 ELSE t.segmentNum
        END segmentNum,
        p.status status,
        p.project_status packagesStatus,
        p.project_status_code packagesStatusCode,
        p.created_time created_time,
        p.base_project_id,
        p.re_tender reTender
        FROM
        t_project p
        LEFT JOIN (
        SELECT
        count( pb.id ) segmentNum,
        pb.project_id
        FROM
        t_project_bid_section pb
        WHERE
        pb.is_delete = 0
        GROUP BY
        pb.project_id
        ORDER BY
        NULL
        ) t ON t.project_id = p.id
        left join t_project_entrust_user tpe
        on p.id = tpe.project_id
        LEFT JOIN r_user_department rud on tpe.user_id = rud.user_id  and tpe.department_id = rud.department_id
        WHERE
        p.is_delete = 0 and tpe.is_delete = 0
        <if test="condition.keyWords != null and !&quot;&quot;.equals(condition.keyWords)">
            and CONCAT(IFNULL(`project_name`,''), IFNULL(`project_number`,''), IFNULL(`purchase_name`,''),
            IFNULL(`purchase_number`,'')) LIKE concat('%',#{condition.keyWords},'%')
        </if>
        <if test="condition.projectName != null and !&quot;&quot;.equals(condition.projectName)">
            and p.project_name like concat('%',#{condition.projectName},'%')
        </if>
        <if test="condition.projectNumber != null and !&quot;&quot;.equals(condition.projectNumber)">
            and p.project_number like concat('%',#{condition.projectNumber},'%')
        </if>
        <if test="condition.purchaseName != null and !&quot;&quot;.equals(condition.purchaseName)">
            and p.purchase_name like concat('%',#{condition.purchaseName},'%')
        </if>
        <if test="condition.purchaseNumber != null and !&quot;&quot;.equals(condition.purchaseNumber)">
            and p.purchase_number like concat('%',#{condition.purchaseNumber},'%')
        </if>
        <if test="condition.addressProvince != null and !&quot;&quot;.equals(condition.addressProvince)">
            and p.address_province = #{condition.addressProvince}
        </if>
        <if test="condition.addressCity != null and !&quot;&quot;.equals(condition.addressCity)">
            and p.address_city = #{condition.addressCity}
        </if>
        <if test="condition.agentCompany != null and !&quot;&quot;.equals(condition.agentCompany)">
            and p.agent_company like concat('%',#{condition.agentCompany},'%')
        </if>
        <if test="condition.agentChargeName != null and !&quot;&quot;.equals(condition.agentChargeName)">
            and p.agent_charge_name like concat('%',#{condition.agentChargeName},'%')
        </if>
        <if test="condition.agentChargePhone != null and !&quot;&quot;.equals(condition.agentChargePhone)">
            and p.agent_charge_phone like concat('%',#{condition.agentChargePhone},'%')
        </if>
        <if test="condition.principalCompany != null and !&quot;&quot;.equals(condition.principalCompany)">
            and p.principal_company like concat('%',#{condition.principalCompany},'%')
        </if>
        <if test="condition.principalChargeName != null and !&quot;&quot;.equals(condition.principalChargeName)">
            and p.principal_charge_name like concat('%',#{condition.principalChargeName},'%')
        </if>
        <if test="condition.principalChargePhone != null and !&quot;&quot;.equals(condition.principalChargePhone)">
            and p.principal_charge_phone like concat('%',#{condition.principalChargePhone},'%')
        </if>
        <if test="condition.projectStatus != null and !&quot;&quot;.equals(condition.projectStatus)">
            and p.project_status like concat('%',#{condition.projectStatus},'%')
        </if>
        <if test="condition.beginTime != null and !&quot;&quot;.equals(condition.beginTime)">
            and DATE_FORMAT(p.created_time,'%Y-%m-%d') &gt;= #{condition.beginTime}
        </if>
        <if test="condition.endTime != null and !&quot;&quot;.equals(condition.endTime)">
            and DATE_FORMAT(p.created_time,'%Y-%m-%d') &lt;= #{condition.endTime}
        </if>
        and tpe.user_id=#{condition.userId} and tpe.type= #{condition.type}
        <if test="condition.departId != null and !&quot;&quot;.equals(condition.departId)">
            and tpe.department_id= #{condition.departId}
        </if>
        <if test="condition.sortOrderSql != null and !&quot;&quot;.equals(condition.sortOrderSql)">
            order by ${condition.sortOrderSql}
        </if>
    </select>


    <!--招标人根据条件分页查询项目表 列表-->
    <select id="queryBidderEntrustProjectByPage" resultType="com.hzw.sunflower.dto.ProjectListDTO">
        SELECT DISTINCT
        p.id,
        p.project_name projectName,
        p.project_number projectNumber,
        p.purchase_name purchaseName,
        p.purchase_number purchaseNumber,
        p.principal_company principalCompany,
        p.principal_charge_name principalChargeName,
        p.agent_company agentCompany,
        p.agent_charge_name agentChargeName,
        p.refuse_reason refuseReason,
        p.package_segment_status packageSegmentStatus,
        CASE
        p.package_segment_status
        WHEN 0 THEN 0 ELSE t.segmentNum
        END segmentNum,
        p.status status,
        p.project_status packagesStatus,
        p.project_status_code packagesStatusCode,
        p.created_time created_time,
        p.base_project_id,
        p.re_tender reTender,
        p.entrusted_amount,
        p.project_source,
        IFNULL(ptp.role_type,vp.right_code+1) role_type,
        IFNULL(ptp.is_admin,2) is_admin,
        ptp.user_id,
        paa.status as agent_status,
        t2.has_open_count
        FROM
        t_project p
        LEFT JOIN (
        SELECT
        count( pb.id ) segmentNum,
        pb.project_id
        FROM
        t_project_bid_section pb
        WHERE
        pb.is_delete = 0
        GROUP BY
        pb.project_id
        ) t ON t.project_id = p.id
        left join (SELECT * FROM t_project_tenderee_power ptp WHERE ptp.is_delete = 0 AND ptp.user_id = #{condition.userId}) ptp  on p.id = ptp.project_id
        left join t_project_agent_approval paa on p.id = paa.project_id and paa.is_delete = 0
        LEFT JOIN r_user_department rud  on ptp.user_id = rud.user_id  and rud.is_delete =0
        LEFT JOIN v_project_user_perm vp on p.id = vp.project_id and vp.user_id=#{condition.userId}
        <if test="condition.departId != null and !&quot;&quot;.equals(condition.departId)">
            and vp.depart_id=#{condition.departId}
        </if>
        LEFT JOIN (
        SELECT p.id,(SELECT COUNT(*) FROM t_project_bid_section tps
        WHERE tps.project_id = p.id AND tps.is_delete = 0
        AND ((`status` &gt;= 30 AND `status` NOT IN ( 70, 71, 72 ))
        OR (former_abnormal_status &gt;= 30 AND `status` IN ( 70, 71, 72 )))) has_open_count
        FROM t_project p WHERE p.is_delete = 0
        ) t2 ON t2.id = p.id
        LEFT JOIN t_project_bid_section pb ON pb.project_id = p.id
        WHERE
        p.is_delete = 0
        and p.procurement_method = 1
        and (ptp.is_delete = 0
        or  (1=1  ${condition.dataScope}))
        <if test="condition.keyWords != null and !&quot;&quot;.equals(condition.keyWords)">
            and CONCAT(IFNULL(`project_name`,''), IFNULL(`project_number`,''), IFNULL(`purchase_name`,''),
            IFNULL(`purchase_number`,'')) LIKE concat('%',#{condition.keyWords},'%')
        </if>
        <if test="condition.projectName != null and !&quot;&quot;.equals(condition.projectName)">
            and p.project_name like concat('%',#{condition.projectName},'%')
        </if>
        <if test="condition.projectNumber != null and !&quot;&quot;.equals(condition.projectNumber)">
            and p.project_number like concat('%',#{condition.projectNumber},'%')
        </if>
        <if test="condition.purchaseName != null and !&quot;&quot;.equals(condition.purchaseName)">
            and p.purchase_name like concat('%',#{condition.purchaseName},'%')
        </if>
        <if test="condition.purchaseNumber != null and !&quot;&quot;.equals(condition.purchaseNumber)">
            and p.purchase_number like concat('%',#{condition.purchaseNumber},'%')
        </if>
        <if test="condition.addressProvince != null and !&quot;&quot;.equals(condition.addressProvince)">
            and p.address_province = #{condition.addressProvince}
        </if>
        <if test="condition.addressCity != null and !&quot;&quot;.equals(condition.addressCity)">
            and p.address_city = #{condition.addressCity}
        </if>
        <if test="condition.agentCompany != null and !&quot;&quot;.equals(condition.agentCompany)">
            and p.agent_company like concat('%',#{condition.agentCompany},'%')
        </if>
        <if test="condition.agentChargeName != null and !&quot;&quot;.equals(condition.agentChargeName)">
            and p.agent_charge_name like concat('%',#{condition.agentChargeName},'%')
        </if>
        <if test="condition.agentChargePhone != null and !&quot;&quot;.equals(condition.agentChargePhone)">
            and p.agent_charge_phone like concat('%',#{condition.agentChargePhone},'%')
        </if>
        <if test="condition.principalCompany != null and !&quot;&quot;.equals(condition.principalCompany)">
            and p.principal_company like concat('%',#{condition.principalCompany},'%')
        </if>
        <if test="condition.principalChargeName != null and !&quot;&quot;.equals(condition.principalChargeName)">
            and p.principal_charge_name like concat('%',#{condition.principalChargeName},'%')
        </if>
        <if test="condition.principalChargePhone != null and !&quot;&quot;.equals(condition.principalChargePhone)">
            and p.principal_charge_phone like concat('%',#{condition.principalChargePhone},'%')
        </if>
        <if test="condition.projectStatus != null and !&quot;&quot;.equals(condition.projectStatus)">
            and p.project_status like concat('%',#{condition.projectStatus},'%')
        </if>
        <if test="condition.beginTime != null and !&quot;&quot;.equals(condition.beginTime)">
            and DATE_FORMAT(p.created_time,'%Y-%m-%d') &gt;= #{condition.beginTime}
        </if>
        <if test="condition.endTime != null and !&quot;&quot;.equals(condition.endTime)">
            and DATE_FORMAT(p.created_time,'%Y-%m-%d') &lt;= #{condition.endTime}
        </if>
        <if test="condition.submitBeginTime != null and !&quot;&quot;.equals(condition.submitBeginTime)">
            and DATE_FORMAT(pb.submit_end_time,'%Y-%m-%d') &gt;= #{condition.submitBeginTime}
        </if>
        <if test="condition.submitEndTime != null and !&quot;&quot;.equals(condition.submitEndTime)">
            and DATE_FORMAT(pb.submit_end_time,'%Y-%m-%d') &lt;= #{condition.submitEndTime}
        </if>
        <if test="condition.searchType != null  and condition.searchType == 1">
            AND p.status != 9
            AND t2.has_open_count = 0
        </if>
        <if test="condition.searchType != null  and condition.searchType == 2">
            AND p.status != 9
            AND t2.has_open_count > 0
        </if>
        <if test="condition.searchType != null  and condition.searchType == 3">
            AND p.status = 9
        </if>
        <if test="condition.searchType != null  and condition.searchType == 4">
            AND <![CDATA[ pb.abnormal_status <>0 ]]>
        </if>
        <if test="condition.sortOrderSql != null and !&quot;&quot;.equals(condition.sortOrderSql)">
            order by ${condition.sortOrderSql}
        </if>
    </select>

    <!--根据条件分页查询项目表 列表-->
    <select id="queryListMyProjectByPage" resultType="com.hzw.sunflower.dto.MyProjectListDTO">
        SELECT
            distinct
            p.id,
            p.project_name projectName,
            p.project_number projectNumber,
            p.purchase_name purchaseName,
            p.purchase_number purchaseNumber,
            p.status,
            p.is_process isProcess,
            p.process_address processAddress,
            p.process_code processCode,
            p.package_segment_status packageSegmentStatus,
            p.bid_type_name bidTypeName,
            p.re_tender reTender,
            p.base_project_id baseProjectId
        FROM
        t_project p
        LEFT JOIN t_project_bid_section s on p.id = s.project_id
        LEFT JOIN t_project_entrust_user tpe  on s.project_id = tpe.project_id
        LEFT JOIN r_user_department rud on tpe.user_id=rud.user_id and tpe.department_id = rud.department_id
        LEFT JOIN t_bid_win_notice n ON n.section_id = s.id AND n.is_delete = 0 AND n.bid_round = 2
        WHERE
            p.is_delete = 0
            and tpe.is_delete = 0
        <if test="condition.updateType != 7">
            and s.`status` &lt;= #{condition.sectionSatatus}
            and s.abnormal_status = 0
        </if>
            and tpe.type= #{condition.type}
           <!-- and (
                    tpe.user_id = #{condition.userId}
                    or p.id in (  SELECT project_id from v_project_user_perm where user_id = #{condition.userId} )
                )-->
        <if test="condition.datascope != null and !&quot;&quot;.equals(condition.datascope)">
            ${condition.datascope}
        </if>
        <if test="condition.isProcess != null ">
            and p.is_process =#{condition.isProcess}
        </if>
        <if test="condition.keyWords != null and !&quot;&quot;.equals(condition.keyWords)">
            and CONCAT( IFNULL(`project_number`,''), IFNULL(`purchase_name`,''),
            IFNULL(`purchase_number`,'')) LIKE concat('%',#{condition.keyWords},'%')
        </if>
        <if test="condition.updateType == 6">
            AND (n.id is null OR n.`status` in (3,6,11,20))
            AND (s.`status` &gt;= 42 AND s.`status` &lt;=45)
        </if>
        <if test="condition.status !=null and condition.status !=''">
            AND s.status > 13 and s.status  &lt; 46
        </if>
        ORDER BY p.id DESC

    </select>

    <!--根据条件分页查询项目表 列表-->
    <select id="queryListOnlyMyProjectByPage" resultType="com.hzw.sunflower.dto.MyProjectListDTO">
        SELECT
        distinct
            p.id,
            p.project_name projectName,
            p.project_number projectNumber,
            p.purchase_name purchaseName,
            p.purchase_number purchaseNumber,
            p.status,
            p.re_tender reTender,
            p.base_project_id baseProjectId
        FROM
        t_project p
        left join t_project_entrust_user tpe  on p.id = tpe.project_id
        LEFT JOIN r_user_department rud on tpe.user_id = rud.user_id  and tpe.department_id = rud.department_id
        WHERE
        p.is_delete = 0
        and tpe.is_delete = 0
        and tpe.type= #{condition.type}
        and tpe.user_id=#{condition.userId}
        and tpe.department_id = #{condition.userDepartId}
        <if test="condition.keyWords != null and !&quot;&quot;.equals(condition.keyWords)">
            and CONCAT( IFNULL(`project_number`,''), IFNULL(`purchase_name`,''),
            IFNULL(`purchase_number`,'')) LIKE concat('%',#{condition.keyWords},'%')
        </if>
        ORDER BY p.id DESC

    </select>

    <select id="getProjectListByExpert" resultType="com.hzw.sunflower.controller.project.response.ProjectVo">
<!--        SELECT DISTINCT
        t1.* ,
        t4.user_name as managerName
        FROM
        t_project t1
        LEFT JOIN t_project_bid_section t2 ON t1.id = t2.project_id
        LEFT JOIN t_project_entrust_user t3 ON t1.id = t3.project_id
        LEFT JOIN t_user t4 on t3.user_id = t4.id
        WHERE
        t1.is_delete = 0
        AND t2.is_delete = 0
        AND t3.type = 1
        AND t2.`status` &lt; 53
        AND t2.submit_end_time IS NOT NULL
        <if test="null != keyWords and !&quot;&quot;.equals(keyWords)">
          and(
            t1.project_name like concat('%',#{keyWords},'%')
            or t1.purchase_number like concat('%',#{keyWords},'%')
            )
        </if>-->
        SELECT
        distinct
        p.*,
        u.user_name as managerName
        FROM
        t_project p
        LEFT JOIN t_project_bid_section s on p.id = s.project_id
        LEFT JOIN t_project_entrust_user tpe  on s.project_id = tpe.project_id
        LEFT JOIN r_user_department rud on tpe.user_id=rud.user_id and tpe.department_id = rud.department_id
        LEFT JOIN t_user u on rud.user_id = u.id
        WHERE
        p.is_delete = 0
        AND tpe.type = 1
        and tpe.is_delete = 0
        and s.`status` &lt;= 53
        and s.abnormal_status = 0
        <if test="datascope != null and !&quot;&quot;.equals(datascope)">
            ${datascope}
        </if>
        <!-- and (
         tpe.user_id = #{userId}
         or p.id in (  SELECT project_id from v_project_user_perm where user_id = #{userId} )
         )-->
         <if test="keyWords != null and !&quot;&quot;.equals(keyWords)">
             and CONCAT( IFNULL(`project_number`,''), IFNULL(`purchase_name`,''),
             IFNULL(`purchase_number`,'')) LIKE concat('%',#{keyWords},'%')
         </if>
         ORDER BY p.id DESC
     </select>

     <select id="getSectionListByExpert" resultType="com.hzw.sunflower.entity.ProjectBidSection">
         SELECT DISTINCT
         *
         FROM
          t_project_bid_section
         WHERE
         is_delete = 0
         AND `status` &lt; 53
         <if test="null != projectId">
             AND  project_id = #{projectId}
         </if>
     </select>


    <update id="deleteApplyInfoBySubId">
        UPDATE t_apply_info
        SET is_delete = 1
        WHERE is_delete = 0 AND  sub_id IN
        <foreach collection="subList" item="subId" open="(" close=")" separator=",">
             #{subId}
        </foreach>
    </update>

     <update id="deleteProjectBidDoc">
         update t_project_bid_doc set is_delete = 1 where id in
         <foreach collection="ids" index="index" item="item" open="(" separator="," close=")">
             #{item}
         </foreach>
     </update>
    <select id="findShareProject" resultType="java.lang.Long">
        SELECT
            id
        FROM
            t_project
        WHERE 1=1
          AND id IN
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>


     <select id="findDocId" resultType="java.lang.Long">
          SELECT
                 t.id
             FROM
                 t_project_bid_doc t
                 LEFT JOIN t_project_bid_doc_relation r ON t.id = r.doc_id
                 LEFT JOIN t_project_bid_section pbs ON r.section_id = pbs.id
                 AND t.bid_round = pbs.bid_round
             WHERE
                 t.project_id = #{section.projectId}
                 AND t.bid_round = #{section.bidRound}
                 and pbs.purchase_mode = #{section.purchaseMode}
                 and pbs.purchase_type = #{section.purchaseType}
                 and pbs.purchase_status = #{section.purchaseStatus}
                 AND t.is_delete = 0
                 AND r.is_delete = 0
                 AND pbs.is_delete = 0
     </select>

     <select id="findNoticeId" resultType="java.lang.Long">
            SELECT
                 t.id
             FROM
                 t_project_bid_notice t
                 LEFT JOIN t_section_notice r ON t.id = r.notice_id
                 LEFT JOIN t_project_bid_section pbs ON r.section_id = pbs.id
                 AND t.bid_round = pbs.bid_round
             WHERE
                 t.project_id = #{section.projectId}
                 AND t.bid_round = #{section.bidRound}
                 and pbs.purchase_mode = #{section.purchaseMode}
                 and pbs.purchase_type = #{section.purchaseType}
                 and pbs.purchase_status = #{section.purchaseStatus}
                 AND t.is_delete = 0
                 AND r.is_delete = 0
                 AND pbs.is_delete = 0
     </select>

     <update id="deleteProjectBidDocRelation">
         update t_project_bid_doc_relation set is_delete = 1  where doc_id in
         <foreach collection="ids" index="index" item="item" open="(" separator="," close=")">
             #{item}
         </foreach>
     </update>

     <update id="deleteProjectBidNotice">
         update t_project_bid_notice set is_delete = 1 where id in
         <foreach collection="ids" index="index" item="item" open="(" separator="," close=")">
             #{item}
         </foreach>
     </update>

     <update id="deleteProjectBidNoticeRelation">
         update t_section_notice set is_delete = 1 where notice_id in
         <foreach collection="ids" index="index" item="item" open="(" separator="," close=")">
             #{item}
         </foreach>
     </update>

     <select id="getSectionGroup" resultType="com.hzw.sunflower.dto.ProjectSectionGroupDTO">
         select sg.* from t_section_group  sg
         left join t_project_bid_section pbs
         on sg.project_id = pbs.project_id
         where sg.is_delete = 0 and pbs.is_delete = 0
         and find_in_set(pbs.id,REPLACE(sg.section_ids,'、',','))
         and sg.project_id = #{projectId} and pbs.id = #{sectionId}
     </select>

     <update id="deleteGroupById">
         update t_section_group set is_delete = 1 where id = #{groupId}
     </update>

     <update id="updateGroupInfo">
         update t_section_group set group_info = #{group.groupInfo},section_ids = #{group.sectionIds} where id = #{group.id}
     </update>
    <select id="countApplyInfoByProjectId" resultType="java.lang.Integer">
        SELECT
            count(1)
        from t_apply_info where apply_status in (1,3) and is_delete = 0 and project_id = #{projectId}
    </select>

    <select id="queryApplyInfoByUserId" resultType="com.hzw.sunflower.dto.ApplyInfoListDTO">

        SELECT DISTINCT
        tai.apply_id,
        tai.sub_id AS bid_id,
        tai.project_id,
        tai.apply_status,
        p.project_name,
        p.purchase_name,
        p.package_segment_status,
        p.project_number,
        p.purchase_number,
        tpbs.package_number,
        tpbs.purchase_mode,
        tpbs.bid_round,
        p.re_tender,
        p.base_project_id,
        t.segmentNum
        FROM
        t_apply_info tai
        LEFT JOIN t_project p ON tai.project_id = p.id
        LEFT JOIN t_project_entrust_user tpe ON tai.project_id = tpe.project_id
        LEFT JOIN t_project_bid_section tpbs ON tai.sub_id = tpbs.id
        LEFT JOIN r_user_departmentrud on tpe.user_id = rud.user_id and tpe.department_id = rud.department_id
        LEFT JOIN (
        SELECT
        count( pb.id ) segmentNum,
        pb.project_id
        FROM
        t_project_bid_section pb
        WHERE
        pb.is_delete = 0
        GROUP BY
        pb.project_id
        ORDER BY
        NULL
        ) t ON t.project_id = tai.project_id
        WHERE
        tai.apply_status IN ( 1, 3 )
        AND tai.is_delete = 0
        AND p.is_delete = 0
        AND tpe.is_delete = 0
        AND tpbs.is_delete = 0
        AND tpe.type = 1

        <if test="condition.keyWords != null and !&quot;&quot;.equals(condition.keyWords)">
            and CONCAT( IFNULL(p.purchase_number,''), IFNULL(p.purchase_name,''),
            IFNULL(p.project_number,''), IFNULL(p.project_name,'')) LIKE concat('%',#{condition.keyWords},'%')
        </if>
        <if test="condition.datascope != null and !&quot;&quot;.equals(condition.datascope)">
            ${condition.datascope}
        </if>
        ORDER BY tai.apply_id
    </select>

    <select id="queryWaitList1" resultType="com.hzw.sunflower.controller.project.response.WaitListVo">
      <!--  select * from
        (
            SELECT
                t.*
            FROM
                v_pending_item t
                LEFT JOIN t_project p ON t.project_id = p.id
                LEFT JOIN t_project_bid_section pbs ON p.id = pbs.project_id
                LEFT JOIN t_project_entrust_user tpe ON pbs.project_id = tpe.project_id
                LEFT JOIN r_user_department rud on tpe.user_id = rud.user_id  and tpe.department_id = rud.department_id
        WHERE
              1 = 1
              and p.is_delete = 0
              and pbs.is_delete = 0
              and tpe.is_delete = 0
              and find_in_set( pbs.id, t.section_id )
              and tpe.type = 1
              and rud.is_delete = 0
                ${dataScope}
             or(
                t.applyRefundUserId = #{condition.userId}
             ) 
        ) tt
        where 1 = 1
        <if test="condition.keyWords != null and condition.keyWords != ''">
            and (
            tt.projectName like concat('%',#{condition.keyWords},'%')
            or
            tt.projectNum like concat('%',#{condition.keyWords},'%')
            )
        </if>
        <if test="condition.type != null and condition.type != ''">
            and tt.type = #{condition.type}
        </if>
        <if test="condition.projectId != null and condition.projectId != ''">
            and tt.project_id = #{condition.projectId}
        </if>
        order by tt.orderNum asc , tt.time desc
         -->
        <include refid="queryWaitListSql"></include>
    </select>


    <select id="findCount1" resultType="com.hzw.sunflower.controller.project.response.WaitListVo">
         SELECT
        type,
        IFNULL( COUNT( ttt.type ), 0 ) countNum
        FROM(
            <include refid="queryWaitListSql"></include>
        ) ttt
        GROUP BY ttt.type
    </select>

    <select id="queryWaitList" resultType="com.hzw.sunflower.controller.project.response.WaitListVo">
        select * from
        (
        <!-- 供应商提出澄清疑问 -->
        SELECT
            t.created_time AS time,
            'qcyw' AS type,
            p.purchase_name AS projectName,
            p.purchase_number AS projectNum,
            t.project_id,
            min( pbs.id ) AS sectionId,
            group_concat( DISTINCT pbs.package_number ) AS package_number,
            pbs.bid_round,
            p.base_project_id,
            p.re_tender,
            p.package_segment_status AS packageSegmentStatus,
            p.bid_type_name as bidType,
            null as applyRefundIdConfirmId,
            2 as orderNum
        FROM
            t_e_clarify_objections t
            left join t_project p
            on t.project_id = p.id
            left join t_project_bid_section pbs
            on p.id = pbs.project_id
            left join t_project_entrust_user tpe  on pbs.project_id = tpe.project_id
            LEFT JOIN r_user_department  rud on tpe.user_id=rud.user_id and tpe.department_id = rud.department_id
        WHERE
            t.clarify_type = 1
          AND t.submit_type = 1
            AND t.`status` = 1
            AND t.is_delete = 0
            and p.is_delete = 0
            and pbs.is_delete = 0
            and find_in_set(pbs.id,t.section_ids)
            and tpe.is_delete = 0
            and tpe.type = 1
            and rud.is_delete = 0
            ${dataScope}
            GROUP BY
            t.created_time,
            type,
            p.purchase_name,
            p.purchase_number,
            t.project_id,
            pbs.bid_round,
            p.base_project_id,
            p.re_tender,
            p.package_segment_status,
            p.bid_type_name,
            orderNum

        union
        <!-- 供应商提出异议 -->
        SELECT
            t.created_time AS time,
            'yyyw' AS type,
            p.purchase_name AS projectName,
            p.purchase_number AS projectNum,
            t.project_id,
            min( pbs.id ) section_id,
            group_concat( DISTINCT pbs.package_number ) AS package_number,
            pbs.bid_round,
            p.base_project_id,
            p.re_tender,
            p.package_segment_status AS packageSegmentStatus,
            p.bid_type_name as bidType,
            null as applyRefundIdConfirmId,
            2 as orderNum
        FROM
            t_e_propose_objection t
            left join t_project p
            on t.project_id = p.id
            left join t_project_bid_section pbs
            on p.id = pbs.project_id
            left join t_user_identity ui
            on t.contacts_id = ui.user_id
            left join t_project_entrust_user tpe  on pbs.project_id = tpe.project_id
        LEFT JOIN r_user_department  rud on tpe.user_id=rud.user_id  and tpe.department_id = rud.department_id
        WHERE
             t.is_delete = 0
            and p.is_delete = 0
            and pbs.is_delete = 0
            and ui.is_delete = 0
            and ui.identity = 3
            and t.`status` = 1
            and tpe.is_delete = 0
            and tpe.type = 1
            and find_in_set(pbs.id,t.section_ids)
            and rud.is_delete = 0
            ${dataScope}
        GROUP BY
            t.created_time,
            type,
            p.purchase_name,
            p.purchase_number,
            t.project_id,
            pbs.bid_round,
            p.base_project_id,
            p.re_tender,
            p.package_segment_status,
            p.bid_type_name,
            orderNum

            union
        <!-- 供应商关注（需审核） -->
        select
                t.apply_time as time,
                'gz_xsh' as type,
                p.purchase_name as projectName,
                p.purchase_number as projectNum,
                t.project_id,
                pbs.id as sectionId,
                pbs.package_number,
                pbs.bid_round,
                p.base_project_id,
                p.re_tender,
                (case when pbs.package_number is null then 0 else 1 end) packageSegmentStatus,
                p.bid_type_name as bidType,
                null as applyRefundIdConfirmId,
                1 as orderNum
        from t_apply_info t
        left join t_project p
        on t.project_id = p.id
        left join t_project_bid_section pbs
        on p.id = pbs.project_id and t.sub_id = pbs.id
        left join t_project_entrust_user tpe  on pbs.project_id = tpe.project_id
        LEFT JOIN r_user_department  rud on tpe.user_id=rud.user_id and tpe.department_id = rud.department_id
        where t.is_delete = 0 and p.is_delete = 0 and  pbs.is_delete = 0
        and  t.apply_status = 1 and pbs.review_file_type = 1
        and tpe.is_delete = 0
        and tpe.type = 1
        and rud.is_delete = 0
        ${dataScope}

        union
        <!--  供应商关注（上传材料）-->
        select
                t.apply_time as time,
                'gz_sccl' as type,
                p.purchase_name as projectName,
                p.purchase_number as projectNum,
                t.project_id,
                pbs.id as sectionId,
                pbs.package_number,
                pbs.bid_round,
                p.base_project_id,
                p.re_tender,
                (case when pbs.package_number is null then 0 else 1 end) packageSegmentStatus,
                p.bid_type_name as bidType,
                null as applyRefundIdConfirmId,
                1 as orderNum
        from t_apply_info t
        left join t_project p
        on t.project_id = p.id
        left join t_project_bid_section pbs
        on p.id = pbs.project_id and t.sub_id = pbs.id
        left join t_project_entrust_user tpe  on pbs.project_id = tpe.project_id
        LLEFT JOIN r_user_department rud on tpe.user_id=rud.user_id and tpe.department_id = rud.department_id
        where t.is_delete = 0 and p.is_delete = 0 and  pbs.is_delete = 0
        and  t.apply_status = 3 and pbs.review_file_type = 3
        and tpe.is_delete = 0
        and tpe.type = 1
        and rud.is_delete = 0
        ${dataScope}

        union
        <!--  供应商上传标书费底单 -->
        select
                t.created_time as time,
                'cws' as type,
                p.purchase_name as projectName,
                p.purchase_number as projectNum,
                t.project_id,
                pbs.id as sectionId,
                pbs.package_number,
                pbs.bid_round,
                p.base_project_id,
                p.re_tender,
                (case when pbs.package_number is null then 0 else 1 end) packageSegmentStatus,
                p.bid_type_name as bidType,
                null as applyRefundIdConfirmId,
                2 as orderNum
        from t_pay_all t
        left join t_project p
        on t.project_id = p.id
        left join t_project_bid_section pbs
        on p.id = pbs.project_id
        left join t_apply_info a
        on pbs.id = a.sub_id and t.id = a.pay_id
        left join t_project_entrust_user tpe  on pbs.project_id = tpe.project_id
        LEFT JOIN r_user_department  rud on tpe.user_id=rud.user_id and tpe.department_id = rud.department_id
        where t.is_delete = 0 and p.is_delete = 0 and pbs.is_delete = 0 and a.is_delete = 0
        and t.tt_file_id is not null and t.pay_way = 0 and a.download_flag != 1
        and find_in_set(pbs.id,t.section_ids)
        and tpe.is_delete = 0
        and tpe.type = 1
        and rud.is_delete = 0
        and a.apply_status in (1,2,3,4)
        ${dataScope}

        union
        <!-- 供应商上传保证金底单 -->
        <!--
        select
                max(t.created_time) as time,
                'bond' as type,
                p.purchase_name as projectName,
                p.purchase_number as projectNum,
                t.project_id,
                0 as sectionId,
                group_concat(  DISTINCT pbs.package_number ) as package_number ,
                pbs.bid_round,
                p.base_project_id,
                p.re_tender,
                (case when pbs.package_number is null then 0 else 1 end) packageSegmentStatus,
                p.bid_type_name as bidType,
                null as applyRefundIdConfirmId,
                3 as orderNum
        from t_bond t
        left join t_project p
        on t.project_id = p.id
        left join t_project_bid_section pbs
        on p.id = pbs.project_id and t.section_id =  pbs.id
        left join t_project_entrust_user tpe  on pbs.project_id = tpe.project_id
        LEFT JOIN r_user_department rud on tpe.user_id=rud.user_id
        where t.is_delete = 0 and p.is_delete = 0 and pbs.is_delete = 0
        and t.bond_file_id is not null
        and t.status = 0
        and tpe.is_delete = 0
        and tpe.type = 1
        and rud.is_delete = 0
        and NOW() > pbs.submit_end_time
        ${dataScope}
        group by
        type,
        p.purchase_name ,
        p.purchase_number,
        t.project_id,
        pbs.bid_round,
        p.base_project_id,
        p.re_tender,
        p.package_segment_status,
        p.bid_type_name,
        orderNum

         union
         -->
        <!-- 招标公告审核通过 -->
        SELECT
        t.created_time AS time,
        'tenderbid' AS type,
        p.purchase_name AS projectName,
        p.purchase_number AS projectNum,
        t.project_id,
        min(sn.section_id) section_id,
        group_concat(DISTINCT pbs.package_number ) as package_number ,
        pbs.bid_round,
        p.base_project_id,
        p.re_tender,
        p.package_segment_status as packageSegmentStatus,
        p.bid_type_name AS bidType,
        null as applyRefundIdConfirmId,
        2 as orderNum
        FROM
        t_project_bid_notice t
        LEFT JOIN t_section_notice sn ON t.id = sn.notice_id
        LEFT JOIN t_project p ON t.project_id = p.id
        LEFT JOIN t_project_bid_section pbs ON p.id = pbs.project_id
        AND sn.section_id = pbs.id
        LEFT JOIN t_project_entrust_user tpe ON pbs.project_id = tpe.project_id
        LEFT JOIN r_user_department  rud ON tpe.user_id = rud.user_id and tpe.department_id = rud.department_id
        WHERE
        t.is_delete = 0
        AND p.is_delete = 0
        AND pbs.is_delete = 0
        AND t.notice_progress = 4
        AND tpe.is_delete = 0
        AND tpe.type = 1
        AND rud.is_delete = 0
        ${dataScope}
        group by
        t.created_time,
        type,
        p.purchase_name ,
        p.purchase_number,
        t.project_id,
        pbs.bid_round,
        p.base_project_id,
        p.re_tender,
        p.package_segment_status,
        p.bid_type_name,
        orderNum
        union

        <!-- 招标文件审核通过 -->
        SELECT
        t.created_time AS time,
        'tenderdoc' AS type,
        p.purchase_name AS projectName,
        p.purchase_number AS projectNum,
        t.project_id,
        min(sn.section_id) section_id,
        group_concat(DISTINCT pbs.package_number ) as package_number ,
        pbs.bid_round,
        p.base_project_id,
        p.re_tender,
        p.package_segment_status as  packageSegmentStatus,
        p.bid_type_name AS bidType,
        null as applyRefundIdConfirmId,
        2 as orderNum
        FROM
        t_project_bid_doc t
        LEFT JOIN t_project_bid_doc_relation sn ON t.id = sn.doc_id
        LEFT JOIN t_project p ON t.project_id = p.id
        LEFT JOIN t_project_bid_section pbs ON p.id = pbs.project_id
        AND sn.section_id = pbs.id
        LEFT JOIN t_project_entrust_user tpe ON pbs.project_id = tpe.project_id
        LEFT JOIN r_user_department  rud ON tpe.user_id = rud.user_id and tpe.department_id = rud.department_id
        WHERE
        t.is_delete = 0
        AND p.is_delete = 0
        AND pbs.is_delete = 0
        AND t.notice_progress = 4
        AND tpe.is_delete = 0
        AND tpe.type = 1
        AND rud.is_delete = 0
        and sn.is_delete=0
        ${dataScope}
        group by
        t.created_time,
        type,
        p.purchase_name ,
        p.purchase_number,
        t.project_id,
        pbs.bid_round,
        p.base_project_id,
        p.re_tender,
        p.package_segment_status,
        p.bid_type_name,
        orderNum

        union
        <!-- 补充公告/更正公告审核通过 -->
        SELECT
        distinct
        max(t.created_time) AS time,
        'supplementbid' AS type,
        p.purchase_name AS projectName,
        p.purchase_number AS projectNum,
        t.project_id,
        min(pbs.id) section_id,
        group_concat(DISTINCT pbs.package_number  ) as package_number ,
        pbs.bid_round,
        p.base_project_id,
        p.re_tender,
        p.package_segment_status as packageSegmentStatus,
        p.bid_type_name AS bidType,
        null as applyRefundIdConfirmId,
        2 as orderNum
        FROM
        t_supplement_bulletin t
        LEFT JOIN t_project p ON t.project_id = p.id
        LEFT JOIN t_project_bid_section pbs ON p.id = pbs.project_id
        LEFT JOIN t_project_entrust_user tpe ON pbs.project_id = tpe.project_id
        LEFT JOIN r_user_department rud on tpe.user_id = rud.user_id  and tpe.department_id = rud.department_id
        WHERE
        t.is_delete = 0
        AND p.is_delete = 0
        AND pbs.is_delete = 0
        AND t.`status` = 4
        AND tpe.is_delete = 0
        AND tpe.type = 1
        AND find_in_set( pbs.id, t.related_bid_section )
        AND rud.is_delete = 0
        ${dataScope}
        group by
        type,
        p.purchase_name ,
        p.purchase_number,
        t.project_id,
        pbs.bid_round,
        p.base_project_id,
        p.re_tender,
        p.package_segment_status,
        p.bid_type_name,
        orderNum
        union
        <!-- 中标人公示审核通过 -->
        SELECT
        t.created_time AS time,
        'winbid' AS type,
        p.purchase_name AS projectName,
        p.purchase_number AS projectNum,
        t.project_id,
        min(pbs.id) section_id,
        group_concat(DISTINCT pbs.package_number  ) as package_number ,
        pbs.bid_round,
        p.base_project_id,
        p.re_tender,
        p.package_segment_status as packageSegmentStatus,
        p.bid_type_name AS bidType,
        null as applyRefundIdConfirmId,
        2 as orderNum
        FROM
        t_bid_win_bulletin t
        LEFT JOIN t_project p ON t.project_id = p.id
        LEFT JOIN t_project_bid_section pbs ON p.id = pbs.project_id
        LEFT JOIN t_project_entrust_user tpe ON pbs.project_id = tpe.project_id
        LEFT JOIN r_user_department  rud ON tpe.user_id = rud.user_id and tpe.department_id = rud.department_id
        WHERE
        t.is_delete = 0
        and t.bid_win_type=1
        AND p.is_delete = 0
        AND pbs.is_delete = 0
        AND t.`status` = 4
        AND tpe.is_delete = 0
        AND tpe.type = 1
        AND find_in_set( pbs.id, t.related_bid_section )
        AND rud.is_delete = 0
        ${dataScope}
        group by
        t.created_time,
        type,
        p.purchase_name ,
        p.purchase_number,
        t.project_id,
        pbs.bid_round,
        p.base_project_id,
        p.re_tender,
        p.package_segment_status,
        p.bid_type_name,
        orderNum
        union
        <!-- 中标候选人公示审核通过 -->
        SELECT
        t.created_time AS time,
        'wincandidatebid' AS type,
        p.purchase_name AS projectName,
        p.purchase_number AS projectNum,
        t.project_id,
        min(pbs.id) section_id,
        group_concat(DISTINCT pbs.package_number  ) as package_number ,
        pbs.bid_round,
        p.base_project_id,
        p.re_tender,
        p.package_segment_status as packageSegmentStatus,
        p.bid_type_name AS bidType,
        null as applyRefundIdConfirmId,
        2 as orderNum
        FROM
        t_bid_win_bulletin t
        LEFT JOIN t_project p ON t.project_id = p.id
        LEFT JOIN t_project_bid_section pbs ON p.id = pbs.project_id
        LEFT JOIN t_project_entrust_user tpe ON pbs.project_id = tpe.project_id
        LEFT JOIN r_user_department  rud ON tpe.user_id = rud.user_id and tpe.department_id = rud.department_id
        WHERE
        t.is_delete = 0
        and t.bid_win_type=0
        AND p.is_delete = 0
        AND pbs.is_delete = 0
        AND t.`status` = 4
        AND tpe.is_delete = 0
        AND tpe.type = 1
        AND find_in_set( pbs.id, t.related_bid_section )
        AND rud.is_delete = 0
        ${dataScope}
        group by
        t.created_time,
        type,
        p.purchase_name ,
        p.purchase_number,
        t.project_id,
        pbs.bid_round,
        p.base_project_id,
        p.re_tender,
        p.package_segment_status,
        p.bid_type_name,
        orderNum
        <!--  union
           申请中标通知书审核通过
        SELECT
        t.created_time AS time,
        'winnotice' AS type,
        p.purchase_name AS projectName,
        p.purchase_number AS projectNum,
        t.project_id,
        pbs.id AS section_id,
        pbs.package_number,
        pbs.bid_round,
        ( CASE WHEN pbs.package_number IS NULL THEN 0 ELSE 1 END ) packageSegmentStatus,
        p.bid_type_name AS bidType
        FROM
        t_bid_win_notice t
        LEFT JOIN t_project p ON t.project_id = p.id
        LEFT JOIN t_project_bid_section pbs ON p.id = pbs.project_id
        AND find_in_set( pbs.id, t.section_id )
        LEFT JOIN t_project_entrust_user tpe ON pbs.project_id = tpe.project_id
        LEFT JOIN r_user_department rud ON tpe.user_id = rud.user_id
        WHERE
        t.is_delete = 0
        AND p.is_delete = 0
        AND pbs.is_delete = 0
        AND t.`status` = 4
        AND tpe.is_delete = 0
        AND tpe.type = 1
        AND rud.is_delete = 0
        ${dataScope}
        -->

        union
        <!-- 澄清/修改审核通过 -->
        SELECT
        t.created_time AS time,
        (if(t.`status` = 5,'clarifybid','objection')) AS type,
        p.purchase_name AS projectName,
        p.purchase_number AS projectNum,
        t.project_id,
        min(pbs.id) section_id,
        group_concat(DISTINCT pbs.package_number  ) as package_number ,
        pbs.bid_round,
        p.base_project_id,
        p.re_tender,
        p.package_segment_status as packageSegmentStatus,
        p.bid_type_name AS bidType,
        null as applyRefundIdConfirmId,
        2 as orderNum
        FROM
        t_e_question_reply t
        LEFT JOIN t_project p ON t.project_id = p.id
        LEFT JOIN t_project_bid_section pbs ON p.id = pbs.project_id
        LEFT JOIN t_project_entrust_user tpe ON pbs.project_id = tpe.project_id
        LEFT JOIN r_user_department  rud ON tpe.user_id = rud.user_id and tpe.department_id = rud.department_id
        WHERE
        t.is_delete = 0
        AND p.is_delete = 0
        AND pbs.is_delete = 0
        AND (t.`status` = 5 )
        AND tpe.is_delete = 0
        AND tpe.type = 1
        AND find_in_set( pbs.id, t.section_ids )
        AND rud.is_delete = 0
        ${dataScope}
        group by
        t.created_time,
        type,
        p.purchase_name ,
        p.purchase_number,
        t.project_id,
        pbs.bid_round,
        p.base_project_id,
        p.re_tender,
        p.package_segment_status,
        p.bid_type_name,
        orderNum
        union
        <!-- 供应商保证金退还申请处长确认 -->
        select
            c.created_time as time,
            'bond_apply_refund' as type,
            null as projectName,
            null as projectNum,
            null as projectId,
            null as sectionId,
            null as packageNumber,
            null as bidRound,
            null as base_project_id,
            null as re_tender,
            null as packageSegmentStatus,
            null as bidType,
            c.id as applyRefundIdConfirmId,
            2 as orderNum
        from
            t_bond_apply_refund_confirm c
        where
            c.is_delete = 0
            and c.user_id = #{condition.userId}
            and c.department_dispose_status = 1

        union
        <!--  异议回复审核通过-->
         SELECT
         t.created_time AS time,
         'objection' AS type,
         p.purchase_name AS projectName,
         p.purchase_number AS projectNum,
         t.project_id,
        min(pbs.id) section_id,
        group_concat(DISTINCT pbs.package_number  ) as package_number ,
         pbs.bid_round,
         p.base_project_id,
         p.re_tender,
         p.package_segment_status as packageSegmentStatus,
         p.bid_type_name AS bidType,
         null as applyRefundIdConfirmId,
         2 as orderNum
         FROM
         t_e_propose_objection t
        LEFT JOIN (
        SELECT
        *
        FROM
        ( SELECT RANK() OVEr ( PARTITION BY clarify_objection_id ORDER BY order_num DESC ) num, a.* FROM t_e_clarify_reply a ) a
        WHERE
        a.num = 1
        ) te ON te.clarify_objection_id = t.id
         LEFT JOIN t_project p ON t.project_id = p.id
         LEFT JOIN t_project_bid_section pbs ON p.id = pbs.project_id
         LEFT JOIN t_project_entrust_user tpe ON pbs.project_id = tpe.project_id
        LEFT JOIN r_user_department  rud ON tpe.user_id = rud.user_id and tpe.department_id = rud.department_id
         WHERE
         t.is_delete = 0
         AND p.is_delete = 0
         AND pbs.is_delete = 0
         AND tpe.is_delete = 0
         AND tpe.type = 1
         AND find_in_set( pbs.id, t.section_ids )
         AND rud.is_delete = 0
         and te.status ='10'
         ${dataScope}
            group by
            t.created_time,
            type,
            p.purchase_name ,
            p.purchase_number,
            t.project_id,
            pbs.bid_round,
            p.base_project_id,
            p.re_tender,
            p.package_segment_status,
            p.bid_type_name,
            orderNum
        ) tt
        where 1 = 1
        <if test="condition.keyWords != null and condition.keyWords != ''">
            and (
                tt.projectName like concat('%',#{condition.keyWords},'%')
                or
                tt.projectNum like concat('%',#{condition.keyWords},'%')
            )
        </if>
        <if test="condition.type != null and condition.type != ''">
            and tt.type = #{condition.type}
        </if>
        <if test="condition.projectId != null and condition.projectId != ''">
            and tt.project_id = #{condition.projectId}
        </if>
        order by tt.orderNum asc , tt.time desc
    </select>

    <select id="getMaxTempInternational" resultType="java.lang.Integer">
        SELECT
            ( CASE WHEN max( temp_international ) IS NULL THEN 0 ELSE max( temp_international ) END ) as num
        FROM
            t_project
        WHERE
            is_delete = 0
    </select>

    <select id="findCount" resultType="com.hzw.sunflower.controller.project.response.WaitListVo">
        SELECT
        type,
        IFNULL( COUNT( ttt.type ), 0 ) countNum
        FROM(
        select * from
        (
        <!-- 供应商提出澄清疑问 -->
        SELECT
        t.created_time as time,
        'qcyw' as type,
        p.purchase_name as projectName,
        p.purchase_number as projectNum,
        t.project_id,
        min( pbs.id ) as sectionId,
        group_concat( DISTINCT pbs.package_number ) as package_number,
        pbs.bid_round,
        p.base_project_id,
        p.re_tender,
        p.package_segment_status as packageSegmentStatus,
        p.bid_type_name as bidType,
        2 as orderNum
        FROM
        t_e_clarify_objections t
        left join t_project p
        on t.project_id = p.id
        left join t_project_bid_section pbs
        on p.id = pbs.project_id
        left join t_project_entrust_user tpe  on pbs.project_id = tpe.project_id
        LEFT JOIN r_user_department rud on tpe.user_id=rud.user_id and tpe.department_id = rud.department_id
        WHERE
        t.clarify_type = 1
        AND t.submit_type = 1
        AND t.`status` = 1
        AND t.is_delete = 0
        and p.is_delete = 0
        and pbs.is_delete = 0
        and tpe.is_delete = 0
        and tpe.type = 1
        and find_in_set(pbs.id,t.section_ids)
        and rud.is_delete = 0
        ${dataScope}
        GROUP BY
        t.created_time,
        type,
        p.purchase_name,
        p.purchase_number,
        t.project_id,
        pbs.bid_round,
        p.base_project_id,
        p.re_tender,
        p.package_segment_status,
        p.bid_type_name,
        orderNum
        union
        <!-- 供应商提出异议 -->
        SELECT
        t.created_time as time,
        'yyyw' as type,
        p.purchase_name as projectName,
        p.purchase_number as projectNum,
        t.project_id,
        min( pbs.id ) section_id,
        group_concat( DISTINCT pbs.package_number ) as package_number,
        pbs.bid_round,
        p.base_project_id,
        p.re_tender,
        p.package_segment_status as packageSegmentStatus,
        p.bid_type_name as bidType,
        2 as orderNum
        FROM
        t_e_propose_objection t
        left join t_project p
        on t.project_id = p.id
        left join t_project_bid_section pbs
        on p.id = pbs.project_id
        left join t_user_identity ui
        on t.contacts_id = ui.user_id
        left join t_project_entrust_user tpe  on pbs.project_id = tpe.project_id
        LEFT JOIN r_user_department rud on tpe.user_id=rud.user_id and tpe.department_id = rud.department_id
        WHERE
        t.is_delete = 0
        and p.is_delete = 0
        and pbs.is_delete = 0
        and ui.is_delete = 0
        and ui.identity = 3
        and t.`status` = 1
        and tpe.is_delete = 0
        and tpe.type = 1
        and find_in_set(pbs.id,t.section_ids)
        and rud.is_delete = 0
        ${dataScope}
        GROUP BY
        t.created_time,
        type,
        p.purchase_name,
        p.purchase_number,
        t.project_id,
        pbs.bid_round,
        p.base_project_id,
        p.re_tender,
        p.package_segment_status,
        p.bid_type_name,
        orderNum
        union
        <!-- 供应商关注（需审核） -->
        select
        t.apply_time as time,
        'gz_xsh' as type,
        p.purchase_name as projectName,
        p.purchase_number as projectNum,
        t.project_id,
        pbs.id as sectionId,
        pbs.package_number,
        pbs.bid_round,
        p.base_project_id,
        p.re_tender,
        (case when pbs.package_number is null then 0 else 1 end) packageSegmentStatus,
        p.bid_type_name as bidType,
        1 as orderNum
        from t_apply_info t
        left join t_project p
        on t.project_id = p.id
        left join t_project_bid_section pbs
        on p.id = pbs.project_id and t.sub_id = pbs.id
        left join t_project_entrust_user tpe  on pbs.project_id = tpe.project_id
        LEFT JOIN r_user_department rud on tpe.user_id=rud.user_id and tpe.department_id = rud.department_id
        where t.is_delete = 0 and p.is_delete = 0 and  pbs.is_delete = 0
        and  t.apply_status = 1 and pbs.review_file_type = 1
        and tpe.is_delete = 0
        and tpe.type = 1
        and rud.is_delete = 0
        ${dataScope}
        union
        <!--  供应商关注（上传材料）-->
        select
        t.apply_time as time,
        'gz_sccl' as type,
        p.purchase_name as projectName,
        p.purchase_number as projectNum,
        t.project_id,
        pbs.id as sectionId,
        pbs.package_number,
        pbs.bid_round,
        p.base_project_id,
        p.re_tender,
        (case when pbs.package_number is null then 0 else 1 end) packageSegmentStatus,
        p.bid_type_name as bidType,
        1 as orderNum
        from t_apply_info t
        left join t_project p
        on t.project_id = p.id
        left join t_project_bid_section pbs
        on p.id = pbs.project_id and t.sub_id = pbs.id
        left join t_project_entrust_user tpe  on pbs.project_id = tpe.project_id
        LEFT JOIN r_user_department rud on tpe.user_id=rud.user_id and tpe.department_id = rud.department_id
        where t.is_delete = 0 and p.is_delete = 0 and  pbs.is_delete = 0
        and  t.apply_status = 3 and pbs.review_file_type = 3
        and tpe.is_delete = 0
        and tpe.type = 1
        and rud.is_delete = 0
        ${dataScope}
        union
        <!--  供应商上传标书费底单 -->
        select
        t.created_time as time,
        'cws' as type,
        p.purchase_name as projectName,
        p.purchase_number as projectNum,
        t.project_id,
        pbs.id as sectionId,
        pbs.package_number,
        pbs.bid_round,
        p.base_project_id,
        p.re_tender,
        (case when pbs.package_number is null then 0 else 1 end) packageSegmentStatus,
        p.bid_type_name as bidType,
        2 as orderNum
        from t_pay_all t
        left join t_project p
        on t.project_id = p.id
        left join t_project_bid_section pbs
        on p.id = pbs.project_id
        left join t_apply_info a
        on pbs.id = a.sub_id and t.id = a.pay_id
        left join t_project_entrust_user tpe  on pbs.project_id = tpe.project_id
        LEFT JOIN r_user_department rud on tpe.user_id=rud.user_id and tpe.department_id = rud.department_id
        where t.is_delete = 0 and p.is_delete = 0 and pbs.is_delete = 0 and a.is_delete = 0
        and t.tt_file_id is not null and t.pay_way = 0 and a.download_flag != 1
        and tpe.is_delete = 0
        and tpe.type = 1
        and find_in_set(pbs.id,t.section_ids)
        and rud.is_delete = 0
        and a.apply_status in (1,2,3,4)
        ${dataScope}
        union
        <!-- 招标公告审核通过 -->
        SELECT
        t.created_time AS time,
        'tenderbid' AS type,
        p.purchase_name AS projectName,
        p.purchase_number AS projectNum,
        t.project_id,
        min(sn.section_id) section_id,
        group_concat(DISTINCT pbs.package_number ) as package_number ,
        pbs.bid_round,
        p.base_project_id,
        p.re_tender,
        p.package_segment_status as packageSegmentStatus,
        p.bid_type_name AS bidType,
        2 as orderNum
        FROM
        t_project_bid_notice t
        LEFT JOIN t_section_notice sn ON t.id = sn.notice_id
        LEFT JOIN t_project p ON t.project_id = p.id
        LEFT JOIN t_project_bid_section pbs ON p.id = pbs.project_id
        AND sn.section_id = pbs.id
        LEFT JOIN t_project_entrust_user tpe ON pbs.project_id = tpe.project_id
        LEFT JOIN r_user_department rud on tpe.user_id = rud.user_id  and tpe.department_id = rud.department_id
        WHERE
        t.is_delete = 0
        AND p.is_delete = 0
        AND pbs.is_delete = 0
        AND t.notice_progress = 4
        AND tpe.is_delete = 0
        AND tpe.type = 1
        AND rud.is_delete = 0
        ${dataScope}
        group by
        t.created_time,
        type,
        p.purchase_name ,
        p.purchase_number,
        t.project_id,
        pbs.bid_round,
        p.base_project_id,
        p.re_tender,
        p.package_segment_status,
        p.bid_type_name,
        orderNum
        union
        <!-- 招标文件审核通过 -->
        SELECT
        t.created_time AS time,
        'tenderdoc' AS type,
        p.purchase_name AS projectName,
        p.purchase_number AS projectNum,
        t.project_id,
        min(sn.section_id) section_id,
        group_concat(DISTINCT pbs.package_number ) as package_number ,
        pbs.bid_round,
        p.base_project_id,
        p.re_tender,
        p.package_segment_status as packageSegmentStatus,
        p.bid_type_name AS bidType,
        2 as orderNum
        FROM
        t_project_bid_doc t
        LEFT JOIN t_project_bid_doc_relation sn ON t.id = sn.doc_id
        LEFT JOIN t_project p ON t.project_id = p.id
        LEFT JOIN t_project_bid_section pbs ON p.id = pbs.project_id
        AND sn.section_id = pbs.id
        LEFT JOIN t_project_entrust_user tpe ON pbs.project_id = tpe.project_id
        LEFT JOIN r_user_department rud on tpe.user_id = rud.user_id  and tpe.department_id = rud.department_id
        WHERE
        t.is_delete = 0
        AND p.is_delete = 0
        AND pbs.is_delete = 0
        AND t.notice_progress = 4
        AND tpe.is_delete = 0
        AND tpe.type = 1
        AND rud.is_delete = 0
        and sn.is_delete=0
        ${dataScope}
        group by
        t.created_time,
        type,
        p.purchase_name ,
        p.purchase_number,
        t.project_id,
        pbs.bid_round,
        p.base_project_id,
        p.re_tender,
        p.package_segment_status,
        p.bid_type_name,
        orderNum
        union
        <!-- 补充公告/更正公告审核通过 -->
        SELECT
        max(t.created_time) AS time,
        'supplementbid' AS type,
        p.purchase_name AS projectName,
        p.purchase_number AS projectNum,
        t.project_id,
        min(pbs.id) section_id,
        group_concat(DISTINCT pbs.package_number  ) as package_number ,
        pbs.bid_round,
        p.base_project_id,
        p.re_tender,
        p.package_segment_status as packageSegmentStatus,
        p.bid_type_name AS bidType,
        2 as orderNum
        FROM
        t_supplement_bulletin t
        LEFT JOIN t_project p ON t.project_id = p.id
        LEFT JOIN t_project_bid_section pbs ON p.id = pbs.project_id
        LEFT JOIN t_project_entrust_user tpe ON pbs.project_id = tpe.project_id
        LEFT JOIN r_user_department rud on tpe.user_id = rud.user_id  and tpe.department_id = rud.department_id
        WHERE
        t.is_delete = 0
        AND p.is_delete = 0
        AND pbs.is_delete = 0
        AND t.`status` = 4
        AND tpe.is_delete = 0
        AND tpe.type = 1
        AND find_in_set( pbs.id, t.related_bid_section )
        AND rud.is_delete = 0
        ${dataScope}
        group by
        type,
        p.purchase_name ,
        p.purchase_number,
        t.project_id,
        pbs.bid_round,
        p.base_project_id,
        p.re_tender,
        p.package_segment_status,
        p.bid_type_name,
        orderNum
        union
        <!-- 中标人公示审核通过 -->
        SELECT
        t.created_time AS time,
        'winbid' AS type,
        p.purchase_name AS projectName,
        p.purchase_number AS projectNum,
        t.project_id,
        min(pbs.id) section_id,
        group_concat(DISTINCT pbs.package_number  ) as package_number ,
        pbs.bid_round,
        p.base_project_id,
        p.re_tender,
        p.package_segment_status as packageSegmentStatus,
        p.bid_type_name AS bidType,
        2 as orderNum
        FROM
        t_bid_win_bulletin t
        LEFT JOIN t_project p ON t.project_id = p.id
        LEFT JOIN t_project_bid_section pbs ON p.id = pbs.project_id
        LEFT JOIN t_project_entrust_user tpe ON pbs.project_id = tpe.project_id
        LEFT JOIN r_user_department rud on tpe.user_id = rud.user_id  and tpe.department_id = rud.department_id
        WHERE
        t.is_delete = 0
        and t.bid_win_type=1
        AND p.is_delete = 0
        AND pbs.is_delete = 0
        AND t.`status` = 4
        AND tpe.is_delete = 0
        AND tpe.type = 1
        AND find_in_set( pbs.id, t.related_bid_section )
        AND rud.is_delete = 0
        ${dataScope}
        group by
        t.created_time,
        type,
        p.purchase_name ,
        p.purchase_number,
        t.project_id,
        pbs.bid_round,
        p.base_project_id,
        p.re_tender,
        p.package_segment_status,
        p.bid_type_name,
        orderNum
        union
        <!-- 中标候选人公示审核通过 -->
        SELECT
        t.created_time AS time,
        'wincandidatebid' AS type,
        p.purchase_name AS projectName,
        p.purchase_number AS projectNum,
        t.project_id,
        min(pbs.id) section_id,
        group_concat(DISTINCT pbs.package_number  ) as package_number ,
        pbs.bid_round,
        p.base_project_id,
        p.re_tender,
        p.package_segment_status as packageSegmentStatus,
        p.bid_type_name AS bidType,
        2 as orderNum
        FROM
        t_bid_win_bulletin t
        LEFT JOIN t_project p ON t.project_id = p.id
        LEFT JOIN t_project_bid_section pbs ON p.id = pbs.project_id
        LEFT JOIN t_project_entrust_user tpe ON pbs.project_id = tpe.project_id
        LEFT JOIN r_user_department rud on tpe.user_id = rud.user_id  and tpe.department_id = rud.department_id
        WHERE
        t.is_delete = 0
        and t.bid_win_type=0
        AND p.is_delete = 0
        AND pbs.is_delete = 0
        AND t.`status` = 4
        AND tpe.is_delete = 0
        AND tpe.type = 1
        AND find_in_set( pbs.id, t.related_bid_section )
        AND rud.is_delete = 0
        ${dataScope}
        group by
        t.created_time,
        type,
        p.purchase_name ,
        p.purchase_number,
        t.project_id,
        pbs.bid_round,
        p.base_project_id,
        p.re_tender,
        p.package_segment_status,
        p.bid_type_name,
        orderNum
        union
        <!-- 澄清/修改审核通过 -->
        SELECT
        t.created_time AS time,
        (if(t.`status` = 5,'clarifybid','objection')) AS type,
        p.purchase_name AS projectName,
        p.purchase_number AS projectNum,
        t.project_id,
        min(pbs.id) section_id,
        group_concat(DISTINCT pbs.package_number  ) as package_number ,
        pbs.bid_round,
        p.base_project_id,
        p.re_tender,
        p.package_segment_status as packageSegmentStatus,
        p.bid_type_name AS bidType,
        2 as orderNum
        FROM
        t_e_question_reply t
        LEFT JOIN t_project p ON t.project_id = p.id
        LEFT JOIN t_project_bid_section pbs ON p.id = pbs.project_id
        LEFT JOIN t_project_entrust_user tpe ON pbs.project_id = tpe.project_id
        LEFT JOIN r_user_department rud on tpe.user_id = rud.user_id  and tpe.department_id = rud.department_id
        WHERE
        t.is_delete = 0
        AND p.is_delete = 0
        AND pbs.is_delete = 0
        AND (t.`status` = 5 )
        AND tpe.is_delete = 0
        AND tpe.type = 1
        AND find_in_set( pbs.id, t.section_ids )
        AND rud.is_delete = 0
        ${dataScope}
        group by
        t.created_time,
        type,
        p.purchase_name ,
        p.purchase_number,
        t.project_id,
        pbs.bid_round,
        p.base_project_id,
        p.re_tender,
        p.package_segment_status,
        p.bid_type_name,
        orderNum
        union
        <!--  异议回复审核通过-->
        SELECT
        t.created_time AS time,
        'objection' AS type,
        p.purchase_name AS projectName,
        p.purchase_number AS projectNum,
        t.project_id,
        min(pbs.id) section_id,
        group_concat(DISTINCT pbs.package_number  ) as package_number ,
        pbs.bid_round,
        p.base_project_id,
        p.re_tender,
        p.package_segment_status as packageSegmentStatus,
        p.bid_type_name AS bidType,
        2 as orderNum
        FROM
        t_e_propose_objection t
        LEFT JOIN (
        SELECT
        *
        FROM
        ( SELECT RANK() OVEr ( PARTITION BY clarify_objection_id ORDER BY order_num DESC ) num, a.* FROM t_e_clarify_reply a ) a
        WHERE
        a.num = 1
        ) te ON te.clarify_objection_id = t.id
        LEFT JOIN t_project p ON t.project_id = p.id
        LEFT JOIN t_project_bid_section pbs ON p.id = pbs.project_id
        LEFT JOIN t_project_entrust_user tpe ON pbs.project_id = tpe.project_id
        LEFT JOIN r_user_department rud on tpe.user_id = rud.user_id  and tpe.department_id = rud.department_id
        WHERE
        t.is_delete = 0
        AND p.is_delete = 0
        AND pbs.is_delete = 0
        AND tpe.is_delete = 0
        AND tpe.type = 1
        AND find_in_set( pbs.id, t.section_ids )
        AND rud.is_delete = 0
        and te.status ='10'
        ${dataScope}
        group by
        t.created_time,
        type,
        p.purchase_name ,
        p.purchase_number,
        t.project_id,
        pbs.bid_round,
        p.base_project_id,
        p.re_tender,
        p.package_segment_status,
        p.bid_type_name,
        orderNum
        ) tt
        order by tt.orderNum asc , tt.time desc) ttt
        GROUP BY ttt.type
    </select>


    <select id="queryReturnList" resultType="com.hzw.sunflower.controller.project.response.WaitListVo">
        SELECT
        tt.*
        FROM
        v_return_list tt
        LEFT JOIN t_project p ON tt.project_id = p.id and p.is_delete = 0
        LEFT JOIN t_project_bid_section pbs ON p.id = pbs.project_id
        AND find_in_set( pbs.id, tt.section_id ) AND pbs.is_delete = 0
        LEFT JOIN t_project_entrust_user tpe ON pbs.project_id = tpe.project_id  AND tpe.is_delete = 0 AND tpe.type = 1
        LEFT JOIN r_user_department rud on tpe.user_id = rud.user_id  and tpe.department_id = rud.department_id  AND rud.is_delete = 0
        LEFT JOIN r_user_department rud1 on tt.created_user_id = rud1.user_id
        where
        1 = 1
        and rud1.is_delete = 0
        <if test="condition.keyWords != null and condition.keyWords != ''">
            and (
            tt.projectName like concat('%',#{condition.keyWords},'%')
            or
            tt.projectNum like concat('%',#{condition.keyWords},'%')
            )
        </if>
        <if test="condition.type != null and condition.type != ''">
            and tt.type = #{condition.type}
        </if>
        AND (
        1 = 1 ${dataScope}
        OR ( tt.project_id = '' AND rud1.department_id IN ( SELECT department_id FROM r_user_department WHERE user_id = #{userId} ) )
        OR tt.created_user_id = #{userId})
        order by tt.refundTime desc
    </select>


    <select id="findReturnCount" resultType="com.hzw.sunflower.controller.project.response.WaitListVo">
    SELECT type,IFNULL( COUNT( ttt.type ), 0 ) countNum
    FROM
    (SELECT
        tt.*
        FROM
        v_return_list tt
        LEFT JOIN t_project p ON tt.project_id = p.id and p.is_delete = 0
        LEFT JOIN t_project_bid_section pbs ON p.id = pbs.project_id
        AND find_in_set( pbs.id, tt.section_id ) AND pbs.is_delete = 0
        LEFT JOIN t_project_entrust_user tpe ON pbs.project_id = tpe.project_id  AND tpe.is_delete = 0 AND tpe.type = 1
        LEFT JOIN r_user_department  rud ON tpe.user_id = rud.user_id  and tpe.department_id = rud.department_id AND rud.is_delete = 0
        LEFT JOIN r_user_department  rud1 on tt.created_user_id = rud1.user_id
        where
        1 = 1
        and rud1.is_delete = 0
        AND (
        1 = 1 ${dataScope}
        OR ( tt.project_id = '' AND rud1.department_id IN ( SELECT department_id FROM r_user_department WHERE user_id = #{userId} ) )
        OR tt.created_user_id = #{userId})
        order by tt.refundTime desc
        )ttt
        GROUP BY ttt.type
    </select>
    <select id="listDoc" resultType="com.hzw.sunflower.controller.project.response.ProjectBidSectionVO">
        SELECT
            r.project_id,
            s.package_number,
            s.package_name,
            r.section_id,
            r.doc_id,
            d.oss_file_id docFile,
            d.annex_name docFileName,
            a.file_id annexFile,
            o.oss_file_name annexFileName
        FROM
            t_project_bid_doc d
            LEFT JOIN t_project_bid_doc_relation r ON r.doc_id = d.id
            LEFT JOIN t_relevancy_annex a ON a.section_id = r.section_id AND a.file_type = 3 AND a.is_delete = 0
            LEFT JOIN t_oss_file o ON o.id = a.file_id
            LEFT JOIN t_project_bid_section s ON s.id = r.section_id
        WHERE
            r.project_id = #{projectId}
            AND s.bid_round = d.bid_round
            AND d.is_delete = 0
            AND r.is_delete = 0
            AND d.notice_progress = 5
            AND (s.`status` &lt; 46 OR s.`status`  &gt; 60)
--             AND s.abnormal_status = 0
            AND d.id not in (
                SELECT DISTINCT r.doc_id
                FROM t_project_bid_doc_relation r
                LEFT JOIN t_project_bid_section s ON s.id = r.section_id
                where s.`status` &gt;= 46 AND s.`status` &lt;= 60 AND r.project_id = #{projectId})
    </select>
    <select id="selectDepartmentCodeByBookkeepId" resultType="java.lang.String">
        select department_code from t_bookkeep_department where id= #{bookkeepId}
    </select>
    
    
    <select id="selectYgcProjectList" resultType="com.hzw.sunflower.controller.project.response.ProjectAcceptanceListVo">
        SELECT
            DISTINCT
            p.id as projectId,
            p.purchase_number,
            p.project_name,
            u.user_name,
            ui.user_phone,
            uar.agent_department_id,
            c.company_name,
            uar.status,
            uar.created_time
        FROM
            t_project p
            LEFT JOIN(select * from t_project_tenderee_power  where is_delete = 0 and is_admin = 1 ) ptp on ptp.project_id = p.id
            LEFT JOIN t_user u ON u.id = ptp.user_id
            LEFT JOIN (select * from t_user_identity where is_delete = 0 and identity = 1) ui  ON ui.user_id = u.id
            LEFT JOIN t_project_entrust_user peu ON peu.user_id = ptp.user_id
            LEFT JOIN r_user_department rud on rud.user_id = peu.user_id and peu.department_id = rud.department_id
            LEFT JOIN t_company c ON c.id = peu.company_id
            LEFT JOIN t_project_agent_approval uar ON uar.project_id = p.id
        where p.is_delete = 0
        and ptp.id is NOT null
        and ptp.id is NOT null
          and ((uar.is_skip_department = 2 and  uar.status in(12,15)) or (uar.is_skip_department = 1 and  uar.status in(12,17)))
        <if test="null != rep.keyWords and rep.keyWords != ''">
            and (p.project_name like concat('%',#{rep.keyWords},'%')
            or  p.purchase_number like concat('%',#{rep.keyWords},'%'))
        </if>
        ORDER BY uar.created_time desc
    </select>

    <select id="selectAcceptanceProjectList" resultType="com.hzw.sunflower.controller.project.response.ProjectAcceptanceListVo">
        SELECT
            DISTINCT
            p.id as projectId,
            p.purchase_number,
            p.project_name,
            u.user_name,
            uar.agent_department_id,
            ui.user_phone,
            c.company_name,
            uar.status,
            uar.created_time
        FROM
            t_project p
                LEFT JOIN(select * from t_project_tenderee_power  where is_delete = 0 and is_admin = 1 ) ptp on ptp.project_id = p.id
                LEFT JOIN t_user u ON u.id = ptp.user_id
                LEFT JOIN (select * from t_user_identity where is_delete = 0 and identity = 1) ui  ON ui.user_id = u.id
                LEFT JOIN t_project_entrust_user peu ON peu.user_id = ptp.user_id AND peu.project_id = p.id
                                                            <if test="null != rep.departId and rep.departId != ''">
                                                                AND peu.department_id = #{rep.departId}
                                                            </if>
                LEFT JOIN t_company c ON c.id = peu.company_id
                LEFT JOIN t_project_agent_approval uar ON uar.project_id = p.id
                LEFT JOIN r_user_department  ud ON ud.department_id = uar.agent_department_id  and peu.user_id = ud.user_id
                LEFT JOIN (select * from r_user_role where is_delete = 0 ) ur ON ur.user_id = ud.user_id
                LEFT JOIN (select * from t_role where is_delete = 0 ) r on ur.role_id = r.id
        where p.is_delete = 0
          and peu.id is NOT null
          and ptp.id is NOT null
          and(
          (uar.agent_charge_id = #{rep.userId}  and uar.status  in(16,20) )
                  or
          ( r.role_code= #{rep.roleCode}
            and ur.user_id = #{rep.userId}
            and (uar.status = 14  or (uar.status = 17 and uar.is_confirm = 2 and uar.is_skip_department = 2 )) )
        )
        <if test="null != rep.keyWords and rep.keyWords != ''">
            and (p.project_name like concat('%',#{rep.keyWords},'%')
                  or  p.project_number like concat('%',#{rep.keyWords},'%'))
        </if>
        ORDER BY uar.created_time desc

    </select>

    <select id="queryMyselfProjectByPage" resultType="com.hzw.sunflower.dto.ProjectListDTO">
        SELECT * FROM ( SELECT
        DISTINCT p.id,
        p.project_name projectName,
        p.project_number projectNumber,
        p.purchase_name purchaseName,
        p.purchase_number purchaseNumber,
        p.principal_company principalCompany,
        p.principal_charge_name principalChargeName,
        p.agent_company agentCompany,
        p.agent_charge_name agentChargeName,
        p.refuse_reason refuseReason,
        p.package_segment_status packageSegmentStatus,
        p.status status,
        p.project_status packagesStatus,
        p.project_status_code packagesStatusCode,
        p.created_time created_time ,
        MAX( s.submit_end_time ) submit_end_time,
        MAX( s.sale_end_time ) sale_end_time,
        CASE
        p.package_segment_status
        WHEN 0 THEN 0 ELSE COUNT( s.id )
        END segmentNum,
        p.base_project_id,
        p.re_tender reTender,
        tpe.user_id userId
        FROM
        t_project p
        LEFT JOIN t_project_entrust_user tpe  ON p.id = tpe.project_id
        LEFT JOIN r_user_department rud on tpe.user_id = rud.user_id  and tpe.department_id = rud.department_id
        LEFT JOIN t_project_bid_section s ON p.id = s.project_id AND s.is_delete = 0
        WHERE
        p.is_delete = 0
        AND tpe.is_delete = 0
        AND p.procurement_method = 2
        AND tpe.type= #{condition.type}
        AND tpe.department_id = #{condition.userDepartId}
        AND tpe.user_id = #{condition.userId}
        <if test="condition.keyWords != null and !&quot;&quot;.equals(condition.keyWords)">
            AND CONCAT(IFNULL( p.purchase_name ,''),IFNULL( p.purchase_number ,''))
            LIKE concat('%',#{condition.keyWords},'%')
        </if>
        <if test="condition.purchaseNum != null and !&quot;&quot;.equals(condition.purchaseNum)">
            AND p.purchase_number like concat('%',#{condition.purchaseNum},'%')
        </if>
        <if test="condition.purchaseName != null and !&quot;&quot;.equals(condition.purchaseName)">
            AND p.purchase_name like concat('%',#{condition.purchaseName},'%')
        </if>
        <if test="condition.purchaseManger != null and !&quot;&quot;.equals(condition.purchaseManger)">
            AND p.agent_charge_name like concat('%',#{condition.purchaseManger},'%')
        </if>
        <if test="condition.departmentId != null ">
            AND rud.department_id = #{condition.departmentId}
        </if>
        <if test="condition.addressProvince != null and !&quot;&quot;.equals(condition.addressProvince) and condition.addressProvince != '0' ">
            AND p.address_province = #{condition.addressProvince}
        </if>
        <if test="condition.addressCity != null and !&quot;&quot;.equals(condition.addressCity)  and condition.addressCity != '0'">
            AND p.address_city = #{condition.addressCity}
        </if>
        <if test="condition.projectStatus != null and !&quot;&quot;.equals(condition.projectStatus)">
            AND p.project_status like concat('%',#{condition.projectStatus},'%')
        </if>
        <if test="condition.createBeginTime != null and !&quot;&quot;.equals(condition.createBeginTime)">
            AND DATE_FORMAT(p.created_time,'%Y-%m-%d') &gt;= #{condition.createBeginTime}
        </if>
        <if test="condition.createEndTime != null and !&quot;&quot;.equals(condition.createEndTime)">
            AND DATE_FORMAT(p.created_time,'%Y-%m-%d') &lt;= #{condition.createEndTime}
        </if>
        <if test="condition.submitBeginTime != null and !&quot;&quot;.equals(condition.submitBeginTime)">
            AND DATE_FORMAT(s.submit_end_time,'%Y-%m-%d') &gt;= #{condition.submitBeginTime}
        </if>
        <if test="condition.submitEndTime != null and !&quot;&quot;.equals(condition.submitEndTime)">
            AND DATE_FORMAT(s.submit_end_time,'%Y-%m-%d') &lt;= #{condition.submitEndTime}
        </if>
        ${condition.dataScope}
        GROUP BY
        p.id,
        p.project_name,
        p.project_number,
        p.purchase_name,
        p.purchase_number,
        p.principal_company,
        p.principal_charge_name,
        p.agent_company,
        p.agent_charge_name,
        p.refuse_reason,
        p.package_segment_status,
        p.`status`,
        p.project_status,
        p.project_status_code,
        p.created_time,
        p.base_project_id,
        p.re_tender,
        tpe.user_id
        )  pbst WHERE 1=1
        <if test="condition.sortOrderSql != null and !&quot;&quot;.equals(condition.sortOrderSql)">
            order by ${condition.sortOrderSql}
        </if>
    </select>
    <select id="queryReSectionList"
            resultType="com.hzw.sunflower.controller.project.response.bidder.ReSectionVo">
        SELECT
            a.id AS projectId,
            a.package_segment_status AS packageSegmentStatus,
            pbs.id AS sectionId,
            pbs.package_name AS packageName,
            pbs.package_number AS packageNumber,
            pbs.STATUS AS status,
            pbs1.former_section_id AS formerSectionId,
            sn.notice_id AS noticeId,
            pbn.notice_progress AS noticeProgress
        FROM
            t_project a
                LEFT JOIN t_project_bid_section pbs ON a.id = pbs.project_id
                LEFT JOIN t_project_bid_section pbs1 on pbs.id = pbs1.former_section_id
                LEFT JOIN t_section_notice sn ON pbs.id = sn.section_id AND sn.is_delete = 0
                LEFT JOIN t_project_bid_notice pbn ON pbn.id = sn.notice_id AND pbn.notice_progress = 5 AND pbn.is_delete = 0 AND pbn.bid_round = pbs.bid_round
        WHERE
            a.purchase_number = #{req.purchaseNumber}
          AND a.is_delete = 0
          AND pbs.is_delete = 0
          AND pbs1.base_section_id is null
          AND pbs.abnormal_status = 2
    </select>
    <select id="queryTenantInfoByCompany"
            resultType="com.hzw.sunflower.controller.project.response.bidder.LenovoTenantUserVo">
        SELECT
        t1.id AS companyId,
        t1.company_name AS companyName
        FROM
        t_company t1
        WHERE
        t1.is_delete = 0
        AND t1.organization_num = #{req.organizationNum}
        <if test="req.companyName != null and req.companyName != ''">
            AND t1.company_name like concat('%',#{req.companyName},'%')
        </if>
    </select>
    <select id="queryTenantInfoByDepartment"
            resultType="com.hzw.sunflower.controller.project.response.bidder.LenovoTenantUserVo">
        SELECT
        t1.id AS companyId,
        t1.company_name AS companyName,
        t3.id AS departmentId,
        t3.department_name AS departmentName
        FROM
        t_company t1
        LEFT JOIN t_department t3 ON t1.id = t3.company_id
        WHERE
        t1.is_delete = 0
        AND t3.is_delete = 0
        AND t1.id = #{req.companyId}
        <if test="req.departmentName != null and req.departmentName != ''">
            AND t3.department_name like concat('%',#{req.departmentName},'%')
        </if>
        ORDER BY
        t1.id,
        t3.id
    </select>
    <select id="queryTenantInfoByUser"
            resultType="com.hzw.sunflower.controller.project.response.bidder.LenovoTenantUserVo">
        SELECT
        t1.id AS companyId,
        t1.company_name AS companyName,
        t3.id AS departmentId,
        t3.department_name AS departmentName,
        t5.id AS userId,
        t5.user_name AS userName,
        t6.user_phone AS userPhone
        FROM
        t_company t1
        LEFT JOIN t_department t3 ON t1.id = t3.company_id
        LEFT JOIN r_user_department  t4 ON t4.department_id = t3.id
        LEFT JOIN t_user t5 ON t4.user_id = t5.id
        LEFT JOIN t_user_identity t6 ON t6.user_id = t5.id AND t1.id = t6.company_id
        WHERE
        t1.is_delete = 0
        AND t3.is_delete = 0
        AND t4.is_delete = 0
        AND t5.is_delete = 0
        AND t6.is_delete = 0
        AND t6.`status` = 1
        AND t6.identity = #{req.organizationType}
        AND t1.id = #{req.companyId}
        AND t3.id = #{req.departmentId}
        <if test="req.userName != null and req.userName != ''">
            AND t5.user_name like concat('%',#{req.userName},'%')
        </if>
        ORDER BY
        t5.id
    </select>

    <!-- 根据项目id查询租户用户id -->
    <select id="queryTenantInfoByProject" resultType="java.lang.Long">
        SELECT
            eu.department_id
        FROM
            t_project_entrust_user eu
            LEFT JOIN r_user_department rud on eu.user_id = rud.user_id  and eu.department_id = rud.department_id
        WHERE
            eu.project_id = #{projectId}
          AND eu.department_id IS NOT NULL
          and eu.is_delete = 0
            LIMIT 1
    </select>
    <select id="queryListSeal" resultType="com.hzw.sunflower.dto.MyProjectListDTO">
        select distinct
        p.id,
        p.project_name projectName,
        p.project_number projectNumber,
        p.purchase_name purchaseName,
        p.purchase_number purchaseNumber,
        p.status,
        p.is_process isProcess,
        p.process_address processAddress,
        p.process_code processCode,
        p.package_segment_status packageSegmentStatus,
        p.bid_type_name bidTypeName,
        p.re_tender reTender,
        p.base_project_id baseProjectId
        from
        t_project p
        left join t_project_bid_section s on p.id = s.project_id and s.is_delete = 0
        left join t_project_entrust_user tpe on s.project_id = tpe.project_id and tpe.is_delete = 0
        left join r_user_department rud on tpe.user_id = rud.user_id and tpe.department_id = rud.department_id and rud.is_delete = 0
        left join t_bid_win_notice n on n.section_id = s.id and n.is_delete = 0 and n.bid_round = 2 and n.is_delete = 0
        where
        p.is_delete = 0
        and tpe.is_delete = 0
        and tpe.type = #{condition.type}
        <if test = "condition.datascope != null and '' != condition.datascope">
            ${condition.datascope}
        </if>
        <if test = "condition.keyWords != null and '' != condition.keyWords">
            and (p.purchase_number like concat('%', #{condition.keyWords},'%')
            or p.project_name like concat('%', #{condition.keyWords},'%'))
        </if>
        and s.status > 5
        and s.abnormal_status = 0
        order by p.id desc
    </select>
    <select id="getProjectBySectionId" resultType="com.hzw.sunflower.entity.Project">
        SELECT
            p.*
        from t_project p
        left join t_project_bid_section pbs on pbs.project_id = p.id
        where pbs.id = #{sectionId}
          and p.is_delete = 0
          and pbs.is_delete = 0
    </select>
</mapper>
