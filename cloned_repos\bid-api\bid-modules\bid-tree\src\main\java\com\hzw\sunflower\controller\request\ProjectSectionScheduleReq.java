package com.hzw.sunflower.controller.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date ：Created in 2021/7/7 14:21
 * @description：附件信息
 * @version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProjectSectionScheduleReq implements Serializable {

    @ApiModelProperty(value = "开标一览表ID")
    private Long scheduleId;

    @ApiModelProperty(value = "项目ID")
    private Long projectId;

    @ApiModelProperty(value = "标段ID")
    private Long sectionId;

    @ApiModelProperty(value = "开标一览表文件ID")
    private Long fileOssId;

    @ApiModelProperty(value = "生成规则 1.直接拼接开标汇总表 2.从x行解析拼接")
    private Integer generateRule;

    @ApiModelProperty(value = "解析起始行数")
    private Integer rowNum;

}
