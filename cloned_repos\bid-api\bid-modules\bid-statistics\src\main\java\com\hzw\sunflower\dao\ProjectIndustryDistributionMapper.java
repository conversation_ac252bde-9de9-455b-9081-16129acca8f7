package com.hzw.sunflower.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hzw.sunflower.entity.ProjectIndustryDistribution;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ProjectIndustryDistributionMapper extends BaseMapper<ProjectIndustryDistribution> {
    int deleteCurrentMonth(@Param("submitEndTime") String submitEndTime);

    int insertBatch(List<ProjectIndustryDistribution> industryList);

    List<ProjectIndustryDistribution> selectProjectIndustryList(@Param("startDate") String startDate, @Param("endDate") String endDate);
}