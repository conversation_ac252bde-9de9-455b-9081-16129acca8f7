package com.hzw.sunflower.entity;


import com.baomidou.mybatisplus.annotation.TableName;
import com.hzw.sunflower.common.BaseBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 项目委托关系人表 (t_project_entrust_user)
 *
 * <AUTHOR>
 * @version 1.0.0 2021-04-12
 */
@ApiModel(description = "项目委托关系人表 ")
@TableName("t_project_entrust_user")
@Data
public class ProjectEntrustUser extends BaseBean {
    /**
     * 版本号
     */
    private static final long serialVersionUID = 443620864424133118L;


    @ApiModelProperty(value = "主键", position = 1)
    private Long id;

    @ApiModelProperty(value = "类型 0委托人1被委托人", position = 2)
    private Integer type;

    @ApiModelProperty(value = "人", position = 3)
    private Long userId;

    @ApiModelProperty(value = "单位", position = 4)
    private Long companyId;

    @ApiModelProperty(value = "项目主键", position = 5)
    private Long projectId;

    @ApiModelProperty(value = "项目来源 1代理机构 2招标人", position = 6)
    private Integer source;

    @ApiModelProperty(value = "部门", position = 7)
    private Long departmentId;

}