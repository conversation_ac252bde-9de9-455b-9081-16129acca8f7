package com.hzw.sunflower.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.alibaba.excel.write.style.column.SimpleColumnWidthStyleStrategy;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzw.sunflower.constant.constantenum.TendererReportEnum;
import com.hzw.sunflower.constant.constantenum.TendererShowApplyInfoEnum;
import com.hzw.sunflower.controller.response.ProjectCountVo;
import com.hzw.sunflower.dao.TendererReportMapper;
import com.hzw.sunflower.datascope.utils.DataScopeUtil;
import com.hzw.sunflower.entity.ApplyInfo;
import com.hzw.sunflower.entity.JwtUser;
import com.hzw.sunflower.entity.TendererReport;
import com.hzw.sunflower.entity.condition.TendererReportCondition;
import com.hzw.sunflower.service.TendererReportService;
import com.hzw.sunflower.util.CustomMergeStrategy;
import lombok.Cleanup;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.BufferedOutputStream;
import java.io.ByteArrayOutputStream;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class TendererReportServiceImpl extends ServiceImpl<TendererReportMapper, TendererReport> implements TendererReportService {

    @Override
    public List<ProjectCountVo> findProjectCount(TendererReportCondition condition, JwtUser jwtUser) {
        List<ProjectCountVo> vos = new ArrayList<>();
        //数据权限
        String datascopesql= DataScopeUtil.getProjectDataScopeFilterSql("rud","tp","p");
        condition.setDataScope(datascopesql);
        ProjectCountVo vo = this.baseMapper.findProjectCount(condition,jwtUser, TendererReportEnum.ALL.getType());
        if(BeanUtil.isNotEmpty(vo)){
            vo.setType(TendererReportEnum.ALL.getType());
            vos.add(vo);
        }
        ProjectCountVo voProgressing = this.baseMapper.findProjectCount(condition,jwtUser, TendererReportEnum.PROGRESSING.getType());
        if(BeanUtil.isNotEmpty(voProgressing)){
            voProgressing.setType(TendererReportEnum.PROGRESSING.getType());
            vos.add(voProgressing);
        }
        ProjectCountVo voOver = this.baseMapper.findProjectCount(condition,jwtUser, TendererReportEnum.OVER.getType());
        if(BeanUtil.isNotEmpty(voOver)){
            voOver.setType(TendererReportEnum.OVER.getType());
            vos.add(voOver);
        }
        return vos;
    }

    @Override
    public IPage<TendererReport> findOpenBidInfo(TendererReportCondition condition, JwtUser jwtUser) {
        IPage<TendererReport> page = condition.buildPage();
        IPage<TendererReport> iPage = null;
        //数据权限
        String datascopesql= DataScopeUtil.getProjectDataScopeFilterSql("rud","tp","p");
        condition.setDataScope(datascopesql);
        iPage = this.baseMapper.findOpenBidInfo(page,condition,jwtUser);
        for (TendererReport report:iPage.getRecords()) {
            report.setConferenceAddress(this.baseMapper.findConferenceAddress(TendererReportEnum.OPEN_BID.getType(), report.getProjectId(),report.getPackageNumber()));
            report.setEvaluationTime(this.baseMapper.findEvaluationTime(TendererReportEnum.EVALUATE_BID.getType(), report.getProjectId(),report.getPackageNumber()));
            report.setEvaluationAddress(this.baseMapper.findConferenceAddress(TendererReportEnum.EVALUATE_BID.getType(), report.getProjectId(),report.getPackageNumber()));
            if(StringUtils.isEmpty(report.getPackageNumber())){
                report.setPackageNumber("-");
            }
        }
        return iPage;
    }

    @Override
    public void exportExcel(TendererReportCondition condition, HttpServletResponse response, JwtUser jwtUser) {
        List<TendererReport> records = findInfoList(condition,jwtUser);
        //文件名    表头名
        String fileName = "项目开标信息";
        //xlsx类型
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        String sheetName = "";
        try {
            if (CollectionUtils.isNotEmpty(records)) {
                @Cleanup ByteArrayOutputStream os = new ByteArrayOutputStream();
                ExcelWriter excelWrite = EasyExcel.write(os).build();
                //全部
                sheetName = "全部";
                excelWrite = makeExcel(sheetName, fileName, records, excelWrite);
                excelWrite.finish();
                // 导出到前端下载
                byte[] content = os.toByteArray();
                OutputStream outputStream = new BufferedOutputStream(response.getOutputStream());
                outputStream.write(content);
                outputStream.flush();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public IPage<TendererReport> applyInfoList(TendererReportCondition condition, JwtUser jwtUser) {
        IPage<TendererReport> page = condition.buildPage();
        IPage<TendererReport> iPage = null;
        //数据权限
        String datascopesql= DataScopeUtil.getProjectDataScopeFilterSql("rud","tp","p");
        condition.setDataScope(datascopesql);
        iPage = this.baseMapper.applyInfoList(page,condition,jwtUser);
        for (TendererReport report:iPage.getRecords()) {
            List<ApplyInfo> applyInfos = new ArrayList<>();
            // 代理机构有设置时，不管招标人是否设置，以代理机构设置为准
            // 代理机构没有设置时，若招标人设置，以招标人为准
            // 代理机构与招标人都没有设置时，不展示
            Boolean flag = TendererShowApplyInfoEnum.YES.getType().equals(report.getIsEmpower()) && TendererShowApplyInfoEnum.YES.getType().equals(report.getIsDefine());
            Boolean flag1 = null == report.getIsEmpower() && TendererShowApplyInfoEnum.YES.getType().equals(report.getRightCode()) && TendererShowApplyInfoEnum.YES.getType().equals(report.getIsDefine());
            if(flag || flag1){
                applyInfos = this.baseMapper.findApplyInfos(report.getProjectId(),report.getSectionId());
            }
            report.setApplyInfos(applyInfos);
        }
        return iPage;
    }

    @Override
    public void exportApplyInfoList(TendererReportCondition condition, HttpServletResponse response, JwtUser jwtUser) {
        List<TendererReport> records = findApplyInfoList(condition,jwtUser);
        //文件名    表头名
        String fileName = "项目报名信息";
        //xlsx类型
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        String sheetName = "";
        try {
            if (CollectionUtils.isNotEmpty(records)) {
                @Cleanup ByteArrayOutputStream os = new ByteArrayOutputStream();
                ExcelWriter excelWrite = EasyExcel.write(os).build();
                //全部
                sheetName = "全部";
                excelWrite = makeExcelApplyInfoList(sheetName, fileName, records, excelWrite);
                excelWrite.finish();
                // 导出到前端下载
                byte[] content = os.toByteArray();
                OutputStream outputStream = new BufferedOutputStream(response.getOutputStream());
                outputStream.write(content);
                outputStream.flush();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private List<TendererReport> findApplyInfoList(TendererReportCondition condition, JwtUser jwtUser) {
        //数据权限
        String datascopesql= DataScopeUtil.getProjectDataScopeFilterSql("rud","tp","p");
        condition.setDataScope(datascopesql);
        List<TendererReport> list = this.baseMapper.findApplyInfoList(condition,jwtUser);
        for (TendererReport report:list) {
            List<ApplyInfo> applyInfos = new ArrayList<>();
            Boolean flag = TendererShowApplyInfoEnum.YES.getType().equals(report.getIsEmpower()) && TendererShowApplyInfoEnum.YES.getType().equals(report.getIsDefine());
            Boolean flag1 = null == report.getIsEmpower() && TendererShowApplyInfoEnum.YES.getType().equals(report.getIsDefine()) && TendererShowApplyInfoEnum.YES.getType().equals(report.getIsDefine());
            Boolean flag2 = null == report.getIsEmpower() && null == report.getIsDefine() && TendererShowApplyInfoEnum.YES.getType().equals(report.getIsDefine());
            if(flag || flag1 || flag2){
                applyInfos = this.baseMapper.findApplyInfos(report.getProjectId(),report.getSectionId());
            }
            report.setApplyInfos(applyInfos);
        }
        return list;
    }

    public List<TendererReport> findInfoList(TendererReportCondition condition, JwtUser jwtUser) {
        //数据权限
        String datascopesql= DataScopeUtil.getProjectDataScopeFilterSql("rud","tp","p");
        condition.setDataScope(datascopesql);
        List<TendererReport> list = this.baseMapper.findOpenBidInfoList(condition,jwtUser);
        for (TendererReport report:list) {
            report.setConferenceAddress(this.baseMapper.findConferenceAddress(TendererReportEnum.OPEN_BID.getType(), report.getProjectId(),report.getPackageNumber()));
            report.setEvaluationTime(this.baseMapper.findEvaluationTime(TendererReportEnum.EVALUATE_BID.getType(), report.getProjectId(),report.getPackageNumber()));
            report.setEvaluationAddress(this.baseMapper.findConferenceAddress(TendererReportEnum.EVALUATE_BID.getType(), report.getProjectId(),report.getPackageNumber()));
            if(StringUtils.isEmpty(report.getPackageNumber())){
                report.setPackageNumber("-");
            }
        }
        return list;
    }

    public ExcelWriter makeExcel(String sheetName, String fileName, List<TendererReport> records, ExcelWriter excelWrite) {
        try {
            // 所有行的集合
            List<List<Object>> list = new ArrayList<List<Object>>();
            int a = 1;
            for (TendererReport dto : records) {
                List<Object> row = new ArrayList<Object>();
                row.add(a);
                a++;
                row.add(dto.getProjectName());
                row.add(dto.getPurchaseNumber());
                row.add(dto.getPackageNumber());
                row.add(dto.getEntrustedAmount() + dto.getEntrustCurrencyName());
                row.add(dto.getAgentCompany());
                row.add(dto.getAgentManager());
                row.add(dto.getAgentManagerContact());
                row.add(dto.getSubmitEndTime());
                String address = "";
                if(CollectionUtil.isEmpty(dto.getConferenceAddress())){
                    address = "-";
                }else {
                    for (int i = 0; i < dto.getConferenceAddress().size(); i++) {
                        address += dto.getConferenceAddress().get(i) + "\r\n";
                    }
                }
                row.add(address);
                String evaluationTime = "";
                if(CollectionUtil.isEmpty(dto.getEvaluationTime())){
                    evaluationTime = "-";
                }else {
                    for (int i = 0; i < dto.getEvaluationTime().size(); i++) {
                        evaluationTime += dto.getEvaluationTime().get(i) + "\r\n";
                    }
                }
                row.add(evaluationTime);
                String evaluationAddress = "";
                if(CollectionUtil.isEmpty(dto.getEvaluationAddress())){
                    evaluationAddress = "-";
                }else {
                    for (int i = 0; i < dto.getEvaluationAddress().size(); i++) {
                        evaluationAddress += dto.getEvaluationAddress().get(i) + "\r\n";
                    }
                }
                row.add(evaluationAddress);
                list.add(row);
            }
            // 动态添加 表头 headList --> 所有表头行集合
            List<List<String>> headList = new ArrayList<List<String>>();
            // 表头--按专家
            String[] title = {"序号", "项目名称", "项目编号", "关联标段包", "委托金额","代理机构","代理机构项目经理", "联系方式", "开标时间", "开标地点","评标时间","评标地点"};
            for (int i = 0; i < title.length; i++) {
                List<String> headTitle = new ArrayList<String>();
                headTitle.add(fileName);
                headTitle.add(title[i]);
                // 组装标题头
                headList.add(headTitle);
            }
            // 头的策略
            WriteCellStyle headWriteCellStyle = new WriteCellStyle();
            // 背景色
            headWriteCellStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
            headWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
            WriteFont headWriteFont = new WriteFont();
            headWriteFont.setFontHeightInPoints((short) 12);
            headWriteCellStyle.setWriteFont(headWriteFont);
            // 内容的策略
            WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
            WriteFont contentWriteFont = new WriteFont();
            // 字体大小
            contentWriteFont.setFontHeightInPoints((short) 12);
            contentWriteCellStyle.setWriteFont(contentWriteFont);
            //设置 自动换行
            contentWriteCellStyle.setWrapped(true);
            //设置 居中
            contentWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            contentWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
            // 这个策略是 头是头的样式 内容是内容的样式 其他的策略可以自己实现
            HorizontalCellStyleStrategy horizontalCellStyleStrategy =
                    new HorizontalCellStyleStrategy(headWriteCellStyle, contentWriteCellStyle);
            WriteSheet writeSheet = EasyExcel.writerSheet(sheetName).head(headList)
                    .registerWriteHandler(horizontalCellStyleStrategy)
                    // 简单的列宽策略
                    .registerWriteHandler(new SimpleColumnWidthStyleStrategy(16))
                    .build();
            excelWrite.write(list, writeSheet);

        } catch (Exception e) {
            e.printStackTrace();
            log.error("导出异常");
        }
        return excelWrite;
    }

    private ExcelWriter makeExcelApplyInfoList(String sheetName, String fileName, List<TendererReport> records, ExcelWriter excelWrite) {
        try {
            // 所有行的集合
            List<List<Object>> list = new ArrayList<List<Object>>();
            List<Object> list1 = new ArrayList<>();
            int a = 1;
            for (TendererReport dto : records) {
                if(CollectionUtil.isNotEmpty(dto.getApplyInfos())){
                    int b = a;
                    a++;
                    for (ApplyInfo u:dto.getApplyInfos()) {
                        List<Object> rowNew = new ArrayList<Object>();
                        rowNew.add(b);
                        list1.add(b);
                        rowNew.add(dto.getProjectName());
                        rowNew.add(dto.getPurchaseNumber());
                        rowNew.add(dto.getPackageNumber());
                        rowNew.add(dto.getPackageName());
                        rowNew.add(dto.getEntrustedAmount() + dto.getEntrustCurrencyName());
                        rowNew.add(dto.getPurchaseModeName());
                        rowNew.add(dto.getAgentCompany());
                        rowNew.add(dto.getAgentManager());
                        rowNew.add(dto.getApplyInfoNum());
                        rowNew.add(dto.getSaleEndTime());
                        rowNew.add(u.getCompanyName());
                        rowNew.add(u.getContactPerson());
                        rowNew.add(u.getUserMobile());
                        list.add(rowNew);
                    }
                } else {
                    List<Object> row = new ArrayList<Object>();
                    row.add(a);
                    list1.add(a);
                    a++;
                    row.add(dto.getProjectName());
                    row.add(dto.getPurchaseNumber());
                    row.add(dto.getPackageNumber());
                    row.add(dto.getPackageName());
                    row.add(dto.getEntrustedAmount());
                    row.add(dto.getPurchaseModeName());
                    row.add(dto.getAgentCompany());
                    row.add(dto.getAgentManager());
                    row.add(dto.getApplyInfoNum());
                    row.add(dto.getSaleEndTime());
                    row.add("-");
                    row.add("-");
                    row.add("-");
                    list.add(row);
                }
            }
            // 动态添加 表头 headList --> 所有表头行集合
            List<List<String>> headList = new ArrayList<List<String>>();
            // 表头--按专家
            String[] title = {"序号", "项目名称", "项目编号", "包号","包名称", "委托金额","采购方式","代理机构","项目经理", "已购标供应商数量", "购标截止时间", "已购标供应商名称","联系人","联系方式"};
            for (int i = 0; i < title.length; i++) {
                List<String> headTitle = new ArrayList<String>();
                headTitle.add(fileName);
                headTitle.add(title[i]);
                // 组装标题头
                headList.add(headTitle);
            }
            // 头的策略
            WriteCellStyle headWriteCellStyle = new WriteCellStyle();
            // 背景色
            headWriteCellStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
            headWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
            WriteFont headWriteFont = new WriteFont();
            headWriteFont.setFontHeightInPoints((short) 12);
            headWriteCellStyle.setWriteFont(headWriteFont);
            // 内容的策略
            WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
            WriteFont contentWriteFont = new WriteFont();
            // 字体大小
            contentWriteFont.setFontHeightInPoints((short) 12);
            contentWriteCellStyle.setWriteFont(contentWriteFont);
            //设置 自动换行
            contentWriteCellStyle.setWrapped(true);
            //设置 居中
            contentWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            contentWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
            // 这个策略是 头是头的样式 内容是内容的样式 其他的策略可以自己实现
            HorizontalCellStyleStrategy horizontalCellStyleStrategy =
                    new HorizontalCellStyleStrategy(headWriteCellStyle, contentWriteCellStyle);
            WriteSheet writeSheet = EasyExcel.writerSheet(sheetName).head(headList)
                    .registerWriteHandler(horizontalCellStyleStrategy)
                    // 简单的列宽策略
                    .registerWriteHandler(new SimpleColumnWidthStyleStrategy(16))
                    .registerWriteHandler(new CustomMergeStrategy(list1, new int[]{0,10}))
                    .build();
            excelWrite.write(list, writeSheet);

        } catch (Exception e) {
            e.printStackTrace();
            log.error("导出异常");
        }
        return excelWrite;
    }
}
