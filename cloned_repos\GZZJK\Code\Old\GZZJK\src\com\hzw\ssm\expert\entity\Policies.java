package com.hzw.ssm.expert.entity;

import com.hzw.ssm.fw.base.BaseEntity;
import com.hzw.ssm.fw.util.Page;

import java.util.Date;

/**
 * 政策法规目录(T_POLICIES)
 * 
 * <AUTHOR>
 * @version 1.0.0 2021-02-01
 */
public class Policies  extends BaseEntity {
    /** 版本号 */
    private static final long serialVersionUID = -7041512836206821227L;

    /* This code was generated by TableGo tools, mark 1 begin. */

    /** ID主键 */
    private String id;
    /** 状态  名字*/
    private String name;

    /** 状态  0启用 1禁用 */
    private String status;

    /** 排序 */
    private Integer sort;

    /** 创建人 */
    private String createUser;

    /** 创建时间 */
    private Date createTime;

    /** 修改人 */
    private String updateUser;

    /** 支持排序 */
    private Date updateTime;

 /** 支持排序 */
    private Integer  policiesDetailsCount;




    /** 分页 */
    private Page page;


    private String updSort;
    private String creSort;


    /** 支持排序 */
    private String  order;


    public String getOrder() {
        return order;
    }

    public void setOrder(String order) {
        this.order = order;
    }

    public String getUpdSort() {
        return updSort;
    }

    public void setUpdSort(String updSort) {
        this.updSort = updSort;
    }

    public String getCreSort() {
        return creSort;
    }

    public void setCreSort(String creSort) {
        this.creSort = creSort;
    }

    public Integer getPoliciesDetailsCount() {
        return policiesDetailsCount;
    }

    public void setPoliciesDetailsCount(Integer policiesDetailsCount) {
        this.policiesDetailsCount = policiesDetailsCount;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Page getPage() {
        return page;
    }

    public void setPage(Page page) {
        this.page = page;
    }
    /* This code was generated by TableGo tools, mark 1 end. */

    /* This code was generated by TableGo tools, mark 2 begin. */

    /**
     * 获取ID主键
     * 
     * @return ID主键
     */
    public String getId() {
        return this.id;
    }

    /**
     * 设置ID主键
     * 
     * @param id
     *          ID主键
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * 获取状态  0启用 1禁用
     * 
     * @return 状态  0启用 1禁用
     */
    public String getStatus() {
        return this.status;
    }

    /**
     * 设置状态  0启用 1禁用
     * 
     * @param status
     *          状态  0启用 1禁用
     */
    public void setStatus(String status) {
        this.status = status;
    }

    /**
     * 获取排序
     * 
     * @return 排序
     */
    public Integer getSort() {
        return this.sort;
    }

    /**
     * 设置排序
     * 
     * @param sort
     *          排序
     */
    public void setSort(Integer sort) {
        this.sort = sort;
    }

    /**
     * 获取创建人
     * 
     * @return 创建人
     */
    public String getCreateUser() {
        return this.createUser;
    }

    /**
     * 设置创建人
     * 
     * @param createUser
     *          创建人
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    /**
     * 获取创建时间
     * 
     * @return 创建时间
     */
    public Date getCreateTime() {
        return this.createTime;
    }

    /**
     * 设置创建时间
     * 
     * @param createTime
     *          创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取修改人
     * 
     * @return 修改人
     */
    public String getUpdateUser() {
        return this.updateUser;
    }

    /**
     * 设置修改人
     * 
     * @param updateUser
     *          修改人
     */
    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }

    /**
     * 获取支持排序
     * 
     * @return 支持排序
     */
    public Date getUpdateTime() {
        return this.updateTime;
    }

    /**
     * 设置支持排序
     * 
     * @param updateTime
     *          支持排序
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }


}