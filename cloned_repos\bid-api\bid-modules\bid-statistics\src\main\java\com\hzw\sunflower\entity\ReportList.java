package com.hzw.sunflower.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
public class ReportList implements Serializable {



    @Excel(name = "负责人",orderNum = "1",width=25)
    @ApiModelProperty("负责人（委托人）")
    private String userName;

    @Excel(name = "项目编号",orderNum = "2",width=25)
    @ApiModelProperty("项目编号（前端展示）")
    private String projectNo;

    @ApiModelProperty("项目编号")
    private String purchaseNumber;

    @Excel(name = "国际标编号",orderNum = "3",width=25,replace = {"-_null"})
    @ApiModelProperty("国际编号")
    private String internationalNumber;

    @ApiModelProperty("包编号")
    private String packageNumber;

    @Excel(name = "委托单位",orderNum = "4",width=25)
    @ApiModelProperty("委托单位")
    private String companyName;

    @Excel(name = "项目名称",orderNum = "5",width=25)
    @ApiModelProperty("项目名称")
    private String projectName;

    @ApiModelProperty("委托金额")
    private BigDecimal entrustedAmount;

    @ApiModelProperty("委托金额币种")
    private Float entrustCurrency;

    @ApiModelProperty("采购方式")
    private String purchaseModeName;

    @Excel(name = "开标时间",orderNum = "6",width=25)
    @ApiModelProperty("开标时间")
    private String submitEndTime;

    @Excel(name = "中标通知书盖章时间",orderNum = "7",width=25,replace = {"-_null"})
    @ApiModelProperty("中标通知书盖章时间")
    private String sealTime;

    @Excel(name = "归档时间",orderNum = "8",width=25,replace = {"-_null"})
    @ApiModelProperty("归档时间")
    private String archiveTime;

    @Excel(name = "存放位置",orderNum = "10",width=25,replace = {"-_null"})
    @ApiModelProperty("存放位置")
    private String storageLocation;

    @Excel(name = "档案编号",orderNum = "11",width=25,replace = {"-_null"})
    @ApiModelProperty("档案编号")
    private String archiveNum;

    @Excel(name = "档案备注",orderNum = "12",width=25,replace = {"-_null"})
    @ApiModelProperty("档案备注")
    private String remark;

    @ApiModelProperty("开标地点")
    private List<String> conferenceAddress;

    @ApiModelProperty("项目立项时间(项目创建时间)")
    private String createdTime;

    @ApiModelProperty("采购类型")
    private Integer purchaseType;

    @ApiModelProperty("政府采购")
    private String isGovernment;

    @ApiModelProperty("是否国际标")
    private Integer isInternational;

    @Excel(name = "逾期天数",orderNum = "9",width=25,replace = {"-_null"})
    @ApiModelProperty("逾期时间")
    private String overdueTime;

}
