2025-09-01 16:22:13,554 - INFO - ��ʼִ�вֿ��¡�ű�
2025-09-01 16:22:13,558 - INFO - �ҵ�JSON�ļ�: ['repos_data.json', 'repos_data1.json']
2025-09-01 16:22:13,559 - INFO - �ɹ���ȡ�ļ�: repos_data.json, ���� 100 ���ֿ�
2025-09-01 16:22:13,560 - INFO - �ҵ��ֿ�: bid-common-service -> *********************:hzw/JSTCCUP/bid-common-service.git
2025-09-01 16:22:13,560 - INFO - �ҵ��ֿ�: bid-file-compare -> *********************:hzw/JSTCCUP/bid-file-compare.git
2025-09-01 16:22:13,560 - INFO - �ҵ��ֿ�: bid-file-tasks -> *********************:hzw/JSTCCUP/bid-file-tasks.git
2025-09-01 16:22:13,560 - INFO - �ҵ��ֿ�: bussiness-notice -> *********************:hzw/JSFGW/bussiness-notice.git
2025-09-01 16:22:13,560 - INFO - �ҵ��ֿ�: preview-wpsqyd-client -> *********************:hzw/JSTCCUP/preview-wpsqyd-client.git
2025-09-01 16:22:13,564 - INFO - �ҵ��ֿ�: preview-wpsqyd-api -> *********************:hzw/JSTCCUP/preview-wpsqyd-api.git
2025-09-01 16:22:13,564 - INFO - �ҵ��ֿ�: preview-wps -> *********************:hzw/JSTCCUP/preview-wps.git
2025-09-01 16:22:13,564 - INFO - �ҵ��ֿ�: platform-index-ui -> *********************:hzw/PORTAL_NEW/platform-index-ui.git
2025-09-01 16:22:13,564 - INFO - �ҵ��ֿ�: platform-cloud -> *********************:hzw/PORTAL_NEW/platform-cloud.git
2025-09-01 16:22:13,564 - INFO - �ҵ��ֿ�: platform-ui -> *********************:hzw/PORTAL_NEW/platform-ui.git
2025-09-01 16:22:13,564 - INFO - �ҵ��ֿ�: heterogeneous-api -> *********************:hzw/Heterogeneous/heterogeneous-api.git
2025-09-01 16:22:13,564 - INFO - �ҵ��ֿ�: heterogeneous-ui -> *********************:hzw/Heterogeneous/heterogeneous-ui.git
2025-09-01 16:22:13,564 - INFO - �ҵ��ֿ�: yunxin-callback-api -> *********************:hzw/JSTCCUP/yunxin-callback-api.git
2025-09-01 16:22:13,564 - INFO - �ҵ��ֿ�: bid-invoice-client -> *********************:hzw/JSTCCUP/bid-invoice-client.git
2025-09-01 16:22:13,564 - INFO - �ҵ��ֿ�: bid-encryption-tool -> *********************:hzw/JSTCCUP/bid-encryption-tool.git
2025-09-01 16:22:13,564 - INFO - �ҵ��ֿ�: bid-doc-maintenance -> *********************:hzw/JSTCCUP/bid-doc-maintenance.git
2025-09-01 16:22:13,564 - INFO - �ҵ��ֿ�: bid-invoice-api -> *********************:hzw/JSTCCUP/bid-invoice-api.git
2025-09-01 16:22:13,565 - INFO - �ҵ��ֿ�: HZW-TA -> *********************:hzw/Test/HZW-TA.git
2025-09-01 16:22:13,565 - INFO - �ҵ��ֿ�: bid-fee-deposit-client -> *********************:hzw/JSTCCUP/bid-fee-deposit-client.git
2025-09-01 16:22:13,565 - INFO - �ҵ��ֿ�: bid-fee-expert-client -> *********************:hzw/JSTCCUP/bid-fee-expert-client.git
2025-09-01 16:22:13,565 - INFO - �ҵ��ֿ�: bid-fee-deposit -> *********************:hzw/JSTCCUP/bid-fee-deposit.git
2025-09-01 16:22:13,565 - INFO - �ҵ��ֿ�: bid-fee-expert -> *********************:hzw/JSTCCUP/bid-fee-expert.git
2025-09-01 16:22:13,565 - INFO - �ҵ��ֿ�: bid-doc-tool-api -> *********************:hzw/JSTCCUP/bid-doc-tool-api.git
2025-09-01 16:22:13,565 - INFO - �ҵ��ֿ�: bid-document-tool -> *********************:hzw/JSTCCUP/bid-document-tool.git
2025-09-01 16:22:13,565 - INFO - �ҵ��ֿ�: bid-office-online -> *********************:hzw/JSTCCUP/bid-office-online.git
2025-09-01 16:22:13,565 - INFO - �ҵ��ֿ�: bid-sign-file -> *********************:hzw/JSTCCUP/bid-sign-file.git
2025-09-01 16:22:13,565 - INFO - �ҵ��ֿ�: bid-spider-doc -> *********************:hzw/JSTCCUP/bid-spider-doc.git
2025-09-01 16:22:13,565 - INFO - �ҵ��ֿ�: bid-spider-client -> *********************:hzw/JSTCCUP/bid-spider-client.git
2025-09-01 16:22:13,565 - INFO - �ҵ��ֿ�: push-tool-api -> *********************:hzw/PUSHTOOL/push-tool-api.git
2025-09-01 16:22:13,565 - INFO - �ҵ��ֿ�: push-tool-ui -> *********************:hzw/PUSHTOOL/push-tool-ui.git
2025-09-01 16:22:13,566 - INFO - �ҵ��ֿ�: bid-textToSpeech -> *********************:hzw/JSTCCUP/bid-textToSpeech.git
2025-09-01 16:22:13,566 - INFO - �ҵ��ֿ�: bid-spider-api -> *********************:hzw/JSTCCUP/bid-spider-api.git
2025-09-01 16:22:13,566 - INFO - �ҵ��ֿ�: spider-main -> *********************:hzw/JITC/spider-main.git
2025-09-01 16:22:13,566 - INFO - �ҵ��ֿ�: ctbpsp_crawler -> *********************:hzw/DATASCRAPING/ctbpsp_crawler.git
2025-09-01 16:22:13,566 - INFO - �ҵ��ֿ�: portal-index-ui -> *********************:hzw/SAASPORTAL/portal-index-ui.git
2025-09-01 16:22:13,566 - INFO - �ҵ��ֿ�: portal-index-ui -> *********************:hzw/PORTAL/portal-index-ui.git
2025-09-01 16:22:13,566 - INFO - �ҵ��ֿ�: portal-ui -> *********************:hzw/PORTAL/portal-ui.git
2025-09-01 16:22:13,566 - INFO - �ҵ��ֿ�: portal-cloud -> *********************:hzw/PORTAL/portal-cloud.git
2025-09-01 16:22:13,567 - INFO - �ҵ��ֿ�: bid-clear-tool-client -> *********************:hzw/JSTCCUP/bid-clear-tool-client.git
2025-09-01 16:22:13,567 - INFO - �ҵ��ֿ�: bid-clear-tool -> *********************:hzw/JSTCCUP/bid-clear-tool.git
2025-09-01 16:22:13,567 - INFO - �ҵ��ֿ�: publishTool -> *********************:hzw/JSFGW/publishTool.git
2025-09-01 16:22:13,567 - INFO - �ҵ��ֿ�: bid-common-ratelimiter -> *********************:hzw/JSTCCUP/bid-common-ratelimiter.git
2025-09-01 16:22:13,567 - INFO - �ҵ��ֿ�: devops -> *********************:hzw/JSFGW/devops.git
2025-09-01 16:22:13,567 - INFO - �ҵ��ֿ�: bid-omr-api -> *********************:hzw/JSTCCUP/bid-omr-api.git
2025-09-01 16:22:13,567 - INFO - �ҵ��ֿ�: bid-ui-omr -> *********************:hzw/JSTCCUP/bid-ui-omr.git
2025-09-01 16:22:13,567 - INFO - �ҵ��ֿ�: datascrenn2.0 -> *********************:hzw/JSFGW/datascrenn2.0.git
2025-09-01 16:22:13,567 - INFO - �ҵ��ֿ�: bid-sign-expert -> *********************:hzw/JSTCCUP/bid-sign-expert.git
2025-09-01 16:22:13,567 - INFO - �ҵ��ֿ�: bid-oa-msgpush -> *********************:hzw/JSTCCUP/bid-oa-msgpush.git
2025-09-01 16:22:13,567 - INFO - �ҵ��ֿ�: bid-oa -> *********************:hzw/JSTCCUP/bid-oa.git
2025-09-01 16:22:13,568 - INFO - �ҵ��ֿ�: bid-expert-client -> *********************:hzw/EXPERT/bid-expert-client.git
2025-09-01 16:22:13,568 - INFO - �ҵ��ֿ�: bid-cloud -> *********************:hzw/EXPERT/bid-cloud.git
2025-09-01 16:22:13,568 - INFO - �ҵ��ֿ�: expert-wechat-app -> *********************:hzw/EXPERT/expert-wechat-app.git
2025-09-01 16:22:13,568 - INFO - �ҵ��ֿ�: expert-api -> *********************:hzw/EXPERT/expert-api.git
2025-09-01 16:22:13,568 - INFO - �ҵ��ֿ�: expert-ui -> *********************:hzw/EXPERT/expert-ui.git
2025-09-01 16:22:13,568 - INFO - �ҵ��ֿ�: data-file-tran-api -> *********************:hzw/NJBK/data-file-tran-api.git
2025-09-01 16:22:13,568 - INFO - �ҵ��ֿ�: bid-config -> *********************:hzw/JSTCCUP/bid-config.git
2025-09-01 16:22:13,568 - INFO - �ҵ��ֿ�: source -> *********************:hzw/NJFGW/source.git
2025-09-01 16:22:13,568 - INFO - �ҵ��ֿ�: bid-hub-client-sdk -> *********************:hzw/BIDHUB/bid-hub-client-sdk.git
2025-09-01 16:22:13,568 - INFO - �ҵ��ֿ�: source -> *********************:hzw/NJBK/source.git
2025-09-01 16:22:13,568 - INFO - �ҵ��ֿ�: bid-hub-client -> *********************:hzw/BIDHUB/bid-hub-client.git
2025-09-01 16:22:13,568 - INFO - �ҵ��ֿ�: bid-hub-server-api-client -> *********************:hzw/BIDHUB/bid-hub-server-api-client.git
2025-09-01 16:22:13,568 - INFO - �ҵ��ֿ�: bid-file -> *********************:hzw/JSTCCUP/bid-file.git
2025-09-01 16:22:13,568 - INFO - �ҵ��ֿ�: bid-file-decrypt -> *********************:hzw/JSTCCUP/bid-file-decrypt.git
2025-09-01 16:22:13,568 - INFO - �ҵ��ֿ�: bid-hub-server-ui -> *********************:hzw/BIDHUB/bid-hub-server-ui.git
2025-09-01 16:22:13,568 - INFO - �ҵ��ֿ�: bid-hub-server-api -> *********************:hzw/BIDHUB/bid-hub-server-api.git
2025-09-01 16:22:13,568 - INFO - �ҵ��ֿ�: bid-sign -> *********************:hzw/JSTCCUP/bid-sign.git
2025-09-01 16:22:13,568 - INFO - �ҵ��ֿ�: bid-docs -> *********************:hzw/JSTCCUP/bid-docs.git
2025-09-01 16:22:13,568 - INFO - �ҵ��ֿ�: bid-tenderee-ui -> *********************:hzw/SAAS/bid-tenderee-ui.git
2025-09-01 16:22:13,568 - INFO - �ҵ��ֿ�: cpms -> *********************:hzw/NJBK/cpms.git
2025-09-01 16:22:13,569 - INFO - �ҵ��ֿ�: bid-license -> *********************:hzw/JSTCCUP/bid-license.git
2025-09-01 16:22:13,569 - INFO - �ҵ��ֿ�: bid-chain -> *********************:hzw/BIDHUB/bid-chain.git
2025-09-01 16:22:13,569 - INFO - �ҵ��ֿ�: zjk-python-test -> *********************:hzw/LKM/zjk-python-test.git
2025-09-01 16:22:13,569 - INFO - �ҵ��ֿ�: JSTCC -> *********************:hzw/testing/JSTCC.git
2025-09-01 16:22:13,569 - INFO - �ҵ��ֿ�: bid-amoeba -> *********************:hzw/JSTCCUP/bid-amoeba.git
2025-09-01 16:22:13,569 - INFO - �ҵ��ֿ�: GZZJK_REACT -> *********************:hzw/LKM/GZZJK_REACT.git
2025-09-01 16:22:13,569 - INFO - �ҵ��ֿ�: GZZJK -> *********************:hzw/JITC/GZZJK.git
2025-09-01 16:22:13,569 - INFO - �ҵ��ֿ�: bid-chain-client-ui -> *********************:hzw/JSTCCUP/bid-chain-client-ui.git
2025-09-01 16:22:13,569 - INFO - �ҵ��ֿ�: bid-chain-client-sdk -> *********************:hzw/JSTCCUP/bid-chain-client-sdk.git
2025-09-01 16:22:13,569 - INFO - �ҵ��ֿ�: bid-api-open-client -> *********************:hzw/JSTCCUP/bid-api-open-client.git
2025-09-01 16:22:13,569 - INFO - �ҵ��ֿ�: bid-api-open -> *********************:hzw/JSTCCUP/bid-api-open.git
2025-09-01 16:22:13,569 - INFO - �ҵ��ֿ�: bid-chain-client -> *********************:hzw/JSTCCUP/bid-chain-client.git
2025-09-01 16:22:13,569 - INFO - �ҵ��ֿ�: dataScreen -> *********************:hzw/JSFGW/dataScreen.git
2025-09-01 16:22:13,569 - INFO - �ҵ��ֿ�: bid-expert-ui -> *********************:hzw/JSTCCUP/bid-expert-ui.git
2025-09-01 16:22:13,570 - INFO - �ҵ��ֿ�: expert-manage-mp-ui -> *********************:hzw/JSTCCUP/expert-manage-mp-ui.git
2025-09-01 16:22:13,570 - INFO - �ҵ��ֿ�: expert-manage-ui -> *********************:hzw/JSTCCUP/expert-manage-ui.git
2025-09-01 16:22:13,570 - INFO - �ҵ��ֿ�: bid-ncc-api -> *********************:hzw/JSTCCUP/bid-ncc-api.git
2025-09-01 16:22:13,570 - INFO - �ҵ��ֿ�: cws-api -> *********************:hzw/JSTCCUP/cws-api.git
2025-09-01 16:22:13,570 - INFO - �ҵ��ֿ�: bid-azalea-ui -> *********************:hzw/JSTCCUP/bid-azalea-ui.git
2025-09-01 16:22:13,570 - INFO - �ҵ��ֿ�: bid-common-util -> *********************:hzw/JSTCCUP/bid-common-util.git
2025-09-01 16:22:13,570 - INFO - �ҵ��ֿ�: bid-common-bean -> *********************:hzw/JSTCCUP/bid-common-bean.git
2025-09-01 16:22:13,570 - INFO - �ҵ��ֿ�: bid-ocr-server -> *********************:hzw/JSTCCUP/bid-ocr-server.git
2025-09-01 16:22:13,570 - INFO - �ҵ��ֿ�: WechatGatwayApi -> *********************:hzw/JSFGW/WechatGatwayApi.git
2025-09-01 16:22:13,571 - INFO - �ҵ��ֿ�: UserApi -> *********************:hzw/JSFGW/UserApi.git
2025-09-01 16:22:13,571 - INFO - �ҵ��ֿ�: SyncApi -> *********************:hzw/JSFGW/SyncApi.git
2025-09-01 16:22:13,572 - INFO - �ҵ��ֿ�: SupplierApi -> *********************:hzw/JSFGW/SupplierApi.git
2025-09-01 16:22:13,572 - INFO - �ҵ��ֿ�: SuperviseDataApiClient -> *********************:hzw/JSFGW/SuperviseDataApiClient.git
2025-09-01 16:22:13,572 - INFO - �ҵ��ֿ�: ServerConfigure -> *********************:hzw/JSFGW/ServerConfigure.git
2025-09-01 16:22:13,572 - INFO - �ҵ��ֿ�: RuleSet10MessageProducer -> *********************:hzw/JSFGW/RuleSet10MessageProducer.git
2025-09-01 16:22:13,572 - INFO - �ҵ��ֿ�: PublicServiceWebSite -> *********************:hzw/JSFGW/PublicServiceWebSite.git
2025-09-01 16:22:13,572 - INFO - �ҵ��ֿ�: PublicServiceMini -> *********************:hzw/JSFGW/PublicServiceMini.git
2025-09-01 16:22:13,573 - INFO - �ɹ���ȡ�ļ�: repos_data1.json, ���� 35 ���ֿ�
2025-09-01 16:22:13,573 - INFO - �ҵ��ֿ�: PublicServiceDashBoard -> *********************:hzw/JSFGW/PublicServiceDashBoard.git
2025-09-01 16:22:13,573 - INFO - �ҵ��ֿ�: PlatformApi -> *********************:hzw/JSFGW/PlatformApi.git
2025-09-01 16:22:13,573 - INFO - �ҵ��ֿ�: MonitorUserApi -> *********************:hzw/JSFGW/MonitorUserApi.git
2025-09-01 16:22:13,574 - INFO - �ҵ��ֿ�: MonitorUI -> *********************:hzw/JSFGW/MonitorUI.git
2025-09-01 16:22:13,574 - INFO - �ҵ��ֿ�: MonitorBusinessApi -> *********************:hzw/JSFGW/MonitorBusinessApi.git
2025-09-01 16:22:13,574 - INFO - �ҵ��ֿ�: MobileApp -> *********************:hzw/JSFGW/MobileApp.git
2025-09-01 16:22:13,574 - INFO - �ҵ��ֿ�: MiniWebApp -> *********************:hzw/JSFGW/MiniWebApp.git
2025-09-01 16:22:13,574 - INFO - �ҵ��ֿ�: MarketPlayersAPI -> *********************:hzw/JSFGW/MarketPlayersAPI.git
2025-09-01 16:22:13,574 - INFO - �ҵ��ֿ�: DataSyncApi -> *********************:hzw/JSFGW/DataSyncApi.git
2025-09-01 16:22:13,574 - INFO - �ҵ��ֿ�: ContentManageAPI -> *********************:hzw/JSFGW/ContentManageAPI.git
2025-09-01 16:22:13,574 - INFO - �ҵ��ֿ�: BulletinSearchApi -> *********************:hzw/JSFGW/BulletinSearchApi.git
2025-09-01 16:22:13,574 - INFO - �ҵ��ֿ�: BulletinMessageBroker -> *********************:hzw/JSFGW/BulletinMessageBroker.git
2025-09-01 16:22:13,574 - INFO - �ҵ��ֿ�: bid-ocr -> *********************:hzw/JSTCCUP/bid-ocr.git
2025-09-01 16:22:13,575 - INFO - �ҵ��ֿ�: bid-expert-api -> *********************:hzw/JSTCCUP/bid-expert-api.git
2025-09-01 16:22:13,576 - INFO - �ҵ��ֿ�: file-preview -> *********************:hzw/JSTCCUP/file-preview.git
2025-09-01 16:22:13,576 - INFO - �ҵ��ֿ�: sms -> *********************:hzw/JSTCCUP/sms.git
2025-09-01 16:22:13,576 - INFO - �ҵ��ֿ�: workflow -> *********************:hzw/JSTCCUP/workflow.git
2025-09-01 16:22:13,576 - INFO - �ҵ��ֿ�: bid-ui-tenderee -> *********************:hzw/JSTCCUP/bid-ui-tenderee.git
2025-09-01 16:22:13,576 - INFO - �ҵ��ֿ�: bid-ui-maintain -> *********************:hzw/JSTCCUP/bid-ui-maintain.git
2025-09-01 16:22:13,576 - INFO - �ҵ��ֿ�: bid-ui-supplier -> *********************:hzw/JSTCCUP/bid-ui-supplier.git
2025-09-01 16:22:13,576 - INFO - �ҵ��ֿ�: bid-ui-agency -> *********************:hzw/JSTCCUP/bid-ui-agency.git
2025-09-01 16:22:13,577 - INFO - �ҵ��ֿ�: bid-common-security -> *********************:hzw/JSTCCUP/bid-common-security.git
2025-09-01 16:22:13,577 - INFO - �ҵ��ֿ�: bid-openapi-client -> *********************:hzw/JSTCCUP/bid-openapi-client.git
2025-09-01 16:22:13,578 - INFO - �ҵ��ֿ�: bid-common-datascope -> *********************:hzw/JSTCCUP/bid-common-datascope.git
2025-09-01 16:22:13,578 - INFO - �ҵ��ֿ�: bid-common-core -> *********************:hzw/JSTCCUP/bid-common-core.git
2025-09-01 16:22:13,578 - INFO - �ҵ��ֿ�: bid-cws-client -> *********************:hzw/JSTCCUP/bid-cws-client.git
2025-09-01 16:22:13,578 - INFO - �ҵ��ֿ�: bid-anemone-wechat -> *********************:hzw/JSTCCUP/bid-anemone-wechat.git
2025-09-01 16:22:13,579 - INFO - �ҵ��ֿ�: bid-iris -> *********************:hzw/JSTCCUP/bid-iris.git
2025-09-01 16:22:13,579 - INFO - �ҵ��ֿ�: bid-crocus -> *********************:hzw/JSTCCUP/bid-crocus.git
2025-09-01 16:22:13,580 - INFO - �ҵ��ֿ�: bid-expert-client -> *********************:hzw/JSTCCUP/bid-expert-client.git
2025-09-01 16:22:13,580 - INFO - �ҵ��ֿ�: bid-api -> *********************:hzw/JSTCCUP/bid-api.git
2025-09-01 16:22:13,580 - INFO - �ҵ��ֿ�: bid-freesia -> *********************:hzw/JSTCCUP/bid-freesia.git
2025-09-01 16:22:13,580 - INFO - �ҵ��ֿ�: licenseDemo -> *********************:hzw/LicenseKey/clientDemo.git
2025-09-01 16:22:13,581 - INFO - �ҵ��ֿ�: generatorGUI -> *********************:hzw/LicenseKey/generatorGUI.git
2025-09-01 16:22:13,581 - INFO - �ҵ��ֿ�: generatorAPI -> *********************:hzw/LicenseKey/generatorAPI.git
2025-09-01 16:22:13,581 - INFO - �ܹ��ҵ� 135 ���ֿ�
2025-09-01 16:22:13,582 - INFO - SSH URLs�ѱ��浽: ssh_urls.txt
2025-09-01 16:26:34,035 - INFO - ��ʼ��¡�ֿ�: bid-common-service
2025-09-01 16:26:34,869 - ERROR - ��¡ʧ�� bid-common-service: Cloning into 'cloned_repos\bid-common-service'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:26:34,869 - INFO - ��ʼ��¡�ֿ�: bid-file-compare
2025-09-01 16:26:35,774 - ERROR - ��¡ʧ�� bid-file-compare: Cloning into 'cloned_repos\bid-file-compare'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:26:35,775 - INFO - ��ʼ��¡�ֿ�: bid-file-tasks
2025-09-01 16:26:36,616 - ERROR - ��¡ʧ�� bid-file-tasks: Cloning into 'cloned_repos\bid-file-tasks'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:26:36,616 - INFO - ��ʼ��¡�ֿ�: bussiness-notice
2025-09-01 16:26:37,571 - ERROR - ��¡ʧ�� bussiness-notice: Cloning into 'cloned_repos\bussiness-notice'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:26:37,571 - INFO - ��ʼ��¡�ֿ�: preview-wpsqyd-client
2025-09-01 16:26:38,463 - ERROR - ��¡ʧ�� preview-wpsqyd-client: Cloning into 'cloned_repos\preview-wpsqyd-client'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:26:38,463 - INFO - ��ʼ��¡�ֿ�: preview-wpsqyd-api
2025-09-01 16:26:39,344 - ERROR - ��¡ʧ�� preview-wpsqyd-api: Cloning into 'cloned_repos\preview-wpsqyd-api'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:26:39,344 - INFO - ��ʼ��¡�ֿ�: preview-wps
2025-09-01 16:26:40,227 - ERROR - ��¡ʧ�� preview-wps: Cloning into 'cloned_repos\preview-wps'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:26:40,227 - INFO - ��ʼ��¡�ֿ�: platform-index-ui
2025-09-01 16:26:40,997 - ERROR - ��¡ʧ�� platform-index-ui: Cloning into 'cloned_repos\platform-index-ui'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:26:40,997 - INFO - ��ʼ��¡�ֿ�: platform-cloud
2025-09-01 16:26:41,838 - ERROR - ��¡ʧ�� platform-cloud: Cloning into 'cloned_repos\platform-cloud'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:26:41,838 - INFO - ��ʼ��¡�ֿ�: platform-ui
2025-09-01 16:26:42,691 - ERROR - ��¡ʧ�� platform-ui: Cloning into 'cloned_repos\platform-ui'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:26:42,691 - INFO - ��ʼ��¡�ֿ�: heterogeneous-api
2025-09-01 16:26:43,609 - ERROR - ��¡ʧ�� heterogeneous-api: Cloning into 'cloned_repos\heterogeneous-api'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:26:43,610 - INFO - ��ʼ��¡�ֿ�: heterogeneous-ui
2025-09-01 16:26:44,442 - ERROR - ��¡ʧ�� heterogeneous-ui: Cloning into 'cloned_repos\heterogeneous-ui'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:26:44,442 - INFO - ��ʼ��¡�ֿ�: yunxin-callback-api
2025-09-01 16:26:45,182 - ERROR - ��¡ʧ�� yunxin-callback-api: Cloning into 'cloned_repos\yunxin-callback-api'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:26:45,183 - INFO - ��ʼ��¡�ֿ�: bid-invoice-client
2025-09-01 16:26:46,145 - ERROR - ��¡ʧ�� bid-invoice-client: Cloning into 'cloned_repos\bid-invoice-client'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:26:46,145 - INFO - ��ʼ��¡�ֿ�: bid-encryption-tool
2025-09-01 16:26:47,030 - ERROR - ��¡ʧ�� bid-encryption-tool: Cloning into 'cloned_repos\bid-encryption-tool'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:26:47,030 - INFO - ��ʼ��¡�ֿ�: bid-doc-maintenance
2025-09-01 16:26:47,868 - ERROR - ��¡ʧ�� bid-doc-maintenance: Cloning into 'cloned_repos\bid-doc-maintenance'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:26:47,870 - INFO - ��ʼ��¡�ֿ�: bid-invoice-api
2025-09-01 16:26:48,711 - ERROR - ��¡ʧ�� bid-invoice-api: Cloning into 'cloned_repos\bid-invoice-api'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:26:48,711 - INFO - ��ʼ��¡�ֿ�: HZW-TA
2025-09-01 16:26:49,589 - ERROR - ��¡ʧ�� HZW-TA: Cloning into 'cloned_repos\HZW-TA'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:26:49,590 - INFO - ��ʼ��¡�ֿ�: bid-fee-deposit-client
2025-09-01 16:26:50,390 - ERROR - ��¡ʧ�� bid-fee-deposit-client: Cloning into 'cloned_repos\bid-fee-deposit-client'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:26:50,390 - INFO - ��ʼ��¡�ֿ�: bid-fee-expert-client
2025-09-01 16:26:51,246 - ERROR - ��¡ʧ�� bid-fee-expert-client: Cloning into 'cloned_repos\bid-fee-expert-client'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:26:51,246 - INFO - ��ʼ��¡�ֿ�: bid-fee-deposit
2025-09-01 16:26:52,073 - ERROR - ��¡ʧ�� bid-fee-deposit: Cloning into 'cloned_repos\bid-fee-deposit'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:26:52,073 - INFO - ��ʼ��¡�ֿ�: bid-fee-expert
2025-09-01 16:26:52,927 - ERROR - ��¡ʧ�� bid-fee-expert: Cloning into 'cloned_repos\bid-fee-expert'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:26:52,928 - INFO - ��ʼ��¡�ֿ�: bid-doc-tool-api
2025-09-01 16:26:53,771 - ERROR - ��¡ʧ�� bid-doc-tool-api: Cloning into 'cloned_repos\bid-doc-tool-api'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:26:53,771 - INFO - ��ʼ��¡�ֿ�: bid-document-tool
2025-09-01 16:26:54,685 - ERROR - ��¡ʧ�� bid-document-tool: Cloning into 'cloned_repos\bid-document-tool'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:26:54,685 - INFO - ��ʼ��¡�ֿ�: bid-office-online
2025-09-01 16:26:55,585 - ERROR - ��¡ʧ�� bid-office-online: Cloning into 'cloned_repos\bid-office-online'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:26:55,585 - INFO - ��ʼ��¡�ֿ�: bid-sign-file
2025-09-01 16:26:56,407 - ERROR - ��¡ʧ�� bid-sign-file: Cloning into 'cloned_repos\bid-sign-file'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:26:56,407 - INFO - ��ʼ��¡�ֿ�: bid-spider-doc
2025-09-01 16:26:57,226 - ERROR - ��¡ʧ�� bid-spider-doc: Cloning into 'cloned_repos\bid-spider-doc'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:26:57,227 - INFO - ��ʼ��¡�ֿ�: bid-spider-client
2025-09-01 16:26:58,040 - ERROR - ��¡ʧ�� bid-spider-client: Cloning into 'cloned_repos\bid-spider-client'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:26:58,041 - INFO - ��ʼ��¡�ֿ�: push-tool-api
2025-09-01 16:26:58,967 - ERROR - ��¡ʧ�� push-tool-api: Cloning into 'cloned_repos\push-tool-api'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:26:58,968 - INFO - ��ʼ��¡�ֿ�: push-tool-ui
2025-09-01 16:26:59,866 - ERROR - ��¡ʧ�� push-tool-ui: Cloning into 'cloned_repos\push-tool-ui'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:26:59,866 - INFO - ��ʼ��¡�ֿ�: bid-textToSpeech
2025-09-01 16:27:00,740 - ERROR - ��¡ʧ�� bid-textToSpeech: Cloning into 'cloned_repos\bid-textToSpeech'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:27:00,740 - INFO - ��ʼ��¡�ֿ�: bid-spider-api
2025-09-01 16:27:01,571 - ERROR - ��¡ʧ�� bid-spider-api: Cloning into 'cloned_repos\bid-spider-api'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:27:01,572 - INFO - ��ʼ��¡�ֿ�: spider-main
2025-09-01 16:27:02,492 - ERROR - ��¡ʧ�� spider-main: Cloning into 'cloned_repos\spider-main'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:27:02,492 - INFO - ��ʼ��¡�ֿ�: ctbpsp_crawler
2025-09-01 16:27:03,330 - ERROR - ��¡ʧ�� ctbpsp_crawler: Cloning into 'cloned_repos\ctbpsp_crawler'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:27:03,330 - INFO - ��ʼ��¡�ֿ�: portal-index-ui
2025-09-01 16:27:04,211 - ERROR - ��¡ʧ�� portal-index-ui: Cloning into 'cloned_repos\portal-index-ui'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:27:04,211 - INFO - ��ʼ��¡�ֿ�: portal-index-ui
2025-09-01 16:27:05,015 - ERROR - ��¡ʧ�� portal-index-ui: Cloning into 'cloned_repos\portal-index-ui'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:27:05,015 - INFO - ��ʼ��¡�ֿ�: portal-ui
2025-09-01 16:27:05,900 - ERROR - ��¡ʧ�� portal-ui: Cloning into 'cloned_repos\portal-ui'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:27:05,900 - INFO - ��ʼ��¡�ֿ�: portal-cloud
2025-09-01 16:27:06,824 - ERROR - ��¡ʧ�� portal-cloud: Cloning into 'cloned_repos\portal-cloud'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:27:06,824 - INFO - ��ʼ��¡�ֿ�: bid-clear-tool-client
2025-09-01 16:27:07,646 - ERROR - ��¡ʧ�� bid-clear-tool-client: Cloning into 'cloned_repos\bid-clear-tool-client'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:27:07,646 - INFO - ��ʼ��¡�ֿ�: bid-clear-tool
2025-09-01 16:27:08,478 - ERROR - ��¡ʧ�� bid-clear-tool: Cloning into 'cloned_repos\bid-clear-tool'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:27:08,478 - INFO - ��ʼ��¡�ֿ�: publishTool
2025-09-01 16:27:09,313 - ERROR - ��¡ʧ�� publishTool: Cloning into 'cloned_repos\publishTool'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:27:09,313 - INFO - ��ʼ��¡�ֿ�: bid-common-ratelimiter
2025-09-01 16:27:10,196 - ERROR - ��¡ʧ�� bid-common-ratelimiter: Cloning into 'cloned_repos\bid-common-ratelimiter'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:27:10,196 - INFO - ��ʼ��¡�ֿ�: devops
2025-09-01 16:27:11,126 - ERROR - ��¡ʧ�� devops: Cloning into 'cloned_repos\devops'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:27:11,126 - INFO - ��ʼ��¡�ֿ�: bid-omr-api
2025-09-01 16:27:12,016 - ERROR - ��¡ʧ�� bid-omr-api: Cloning into 'cloned_repos\bid-omr-api'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:27:12,016 - INFO - ��ʼ��¡�ֿ�: bid-ui-omr
2025-09-01 16:27:12,907 - ERROR - ��¡ʧ�� bid-ui-omr: Cloning into 'cloned_repos\bid-ui-omr'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:27:12,907 - INFO - ��ʼ��¡�ֿ�: datascrenn2.0
2025-09-01 16:27:13,677 - ERROR - ��¡ʧ�� datascrenn2.0: Cloning into 'cloned_repos\datascrenn2.0'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:27:13,677 - INFO - ��ʼ��¡�ֿ�: bid-sign-expert
2025-09-01 16:27:14,508 - ERROR - ��¡ʧ�� bid-sign-expert: Cloning into 'cloned_repos\bid-sign-expert'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:27:14,508 - INFO - ��ʼ��¡�ֿ�: bid-oa-msgpush
2025-09-01 16:27:15,363 - ERROR - ��¡ʧ�� bid-oa-msgpush: Cloning into 'cloned_repos\bid-oa-msgpush'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:27:15,364 - INFO - ��ʼ��¡�ֿ�: bid-oa
2025-09-01 16:27:16,258 - ERROR - ��¡ʧ�� bid-oa: Cloning into 'cloned_repos\bid-oa'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:27:16,259 - INFO - ��ʼ��¡�ֿ�: bid-expert-client
2025-09-01 16:27:17,110 - ERROR - ��¡ʧ�� bid-expert-client: Cloning into 'cloned_repos\bid-expert-client'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:27:17,110 - INFO - ��ʼ��¡�ֿ�: bid-cloud
2025-09-01 16:27:17,889 - ERROR - ��¡ʧ�� bid-cloud: Cloning into 'cloned_repos\bid-cloud'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:27:17,889 - INFO - ��ʼ��¡�ֿ�: expert-wechat-app
2025-09-01 16:27:18,741 - ERROR - ��¡ʧ�� expert-wechat-app: Cloning into 'cloned_repos\expert-wechat-app'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:27:18,741 - INFO - ��ʼ��¡�ֿ�: expert-api
2025-09-01 16:27:19,647 - ERROR - ��¡ʧ�� expert-api: Cloning into 'cloned_repos\expert-api'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:27:19,648 - INFO - ��ʼ��¡�ֿ�: expert-ui
2025-09-01 16:27:20,584 - ERROR - ��¡ʧ�� expert-ui: Cloning into 'cloned_repos\expert-ui'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:27:20,584 - INFO - ��ʼ��¡�ֿ�: data-file-tran-api
2025-09-01 16:27:21,424 - ERROR - ��¡ʧ�� data-file-tran-api: Cloning into 'cloned_repos\data-file-tran-api'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:27:21,424 - INFO - ��ʼ��¡�ֿ�: bid-config
2025-09-01 16:27:22,255 - ERROR - ��¡ʧ�� bid-config: Cloning into 'cloned_repos\bid-config'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:27:22,255 - INFO - ��ʼ��¡�ֿ�: source
2025-09-01 16:27:23,076 - ERROR - ��¡ʧ�� source: Cloning into 'cloned_repos\source'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:27:23,076 - INFO - ��ʼ��¡�ֿ�: bid-hub-client-sdk
2025-09-01 16:27:23,926 - ERROR - ��¡ʧ�� bid-hub-client-sdk: Cloning into 'cloned_repos\bid-hub-client-sdk'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:27:23,927 - INFO - ��ʼ��¡�ֿ�: source
2025-09-01 16:27:24,777 - ERROR - ��¡ʧ�� source: Cloning into 'cloned_repos\source'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:27:24,778 - INFO - ��ʼ��¡�ֿ�: bid-hub-client
2025-09-01 16:27:25,606 - ERROR - ��¡ʧ�� bid-hub-client: Cloning into 'cloned_repos\bid-hub-client'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:27:25,606 - INFO - ��ʼ��¡�ֿ�: bid-hub-server-api-client
2025-09-01 16:27:26,467 - ERROR - ��¡ʧ�� bid-hub-server-api-client: Cloning into 'cloned_repos\bid-hub-server-api-client'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:27:26,467 - INFO - ��ʼ��¡�ֿ�: bid-file
2025-09-01 16:27:27,291 - ERROR - ��¡ʧ�� bid-file: Cloning into 'cloned_repos\bid-file'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:27:27,292 - INFO - ��ʼ��¡�ֿ�: bid-file-decrypt
2025-09-01 16:27:28,170 - ERROR - ��¡ʧ�� bid-file-decrypt: Cloning into 'cloned_repos\bid-file-decrypt'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:27:28,170 - INFO - ��ʼ��¡�ֿ�: bid-hub-server-ui
2025-09-01 16:27:49,986 - ERROR - ��¡ʧ�� bid-hub-server-ui: Cloning into 'cloned_repos\bid-hub-server-ui'...
ssh: connect to host codeup.aliyun.com port 22: Connection timed out
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:27:49,986 - INFO - ��ʼ��¡�ֿ�: bid-hub-server-api
2025-09-01 16:27:50,890 - ERROR - ��¡ʧ�� bid-hub-server-api: Cloning into 'cloned_repos\bid-hub-server-api'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:27:50,890 - INFO - ��ʼ��¡�ֿ�: bid-sign
2025-09-01 16:27:51,640 - ERROR - ��¡ʧ�� bid-sign: Cloning into 'cloned_repos\bid-sign'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:27:51,640 - INFO - ��ʼ��¡�ֿ�: bid-docs
2025-09-01 16:27:52,432 - ERROR - ��¡ʧ�� bid-docs: Cloning into 'cloned_repos\bid-docs'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:27:52,433 - INFO - ��ʼ��¡�ֿ�: bid-tenderee-ui
2025-09-01 16:27:53,185 - ERROR - ��¡ʧ�� bid-tenderee-ui: Cloning into 'cloned_repos\bid-tenderee-ui'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:27:53,185 - INFO - ��ʼ��¡�ֿ�: cpms
2025-09-01 16:27:53,911 - ERROR - ��¡ʧ�� cpms: Cloning into 'cloned_repos\cpms'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:27:53,911 - INFO - ��ʼ��¡�ֿ�: bid-license
2025-09-01 16:27:54,697 - ERROR - ��¡ʧ�� bid-license: Cloning into 'cloned_repos\bid-license'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:27:54,697 - INFO - ��ʼ��¡�ֿ�: bid-chain
2025-09-01 16:27:55,485 - ERROR - ��¡ʧ�� bid-chain: Cloning into 'cloned_repos\bid-chain'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:27:55,486 - INFO - ��ʼ��¡�ֿ�: zjk-python-test
2025-09-01 16:27:56,257 - ERROR - ��¡ʧ�� zjk-python-test: Cloning into 'cloned_repos\zjk-python-test'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:27:56,257 - INFO - ��ʼ��¡�ֿ�: JSTCC
2025-09-01 16:27:57,057 - ERROR - ��¡ʧ�� JSTCC: Cloning into 'cloned_repos\JSTCC'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:27:57,057 - INFO - ��ʼ��¡�ֿ�: bid-amoeba
2025-09-01 16:27:57,849 - ERROR - ��¡ʧ�� bid-amoeba: Cloning into 'cloned_repos\bid-amoeba'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:27:57,850 - INFO - ��ʼ��¡�ֿ�: GZZJK_REACT
2025-09-01 16:27:58,520 - ERROR - ��¡ʧ�� GZZJK_REACT: Cloning into 'cloned_repos\GZZJK_REACT'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:27:58,521 - INFO - ��ʼ��¡�ֿ�: GZZJK
2025-09-01 16:27:59,320 - ERROR - ��¡ʧ�� GZZJK: Cloning into 'cloned_repos\GZZJK'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:27:59,320 - INFO - ��ʼ��¡�ֿ�: bid-chain-client-ui
2025-09-01 16:28:00,153 - ERROR - ��¡ʧ�� bid-chain-client-ui: Cloning into 'cloned_repos\bid-chain-client-ui'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:28:00,153 - INFO - ��ʼ��¡�ֿ�: bid-chain-client-sdk
2025-09-01 16:28:00,987 - ERROR - ��¡ʧ�� bid-chain-client-sdk: Cloning into 'cloned_repos\bid-chain-client-sdk'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:28:00,988 - INFO - ��ʼ��¡�ֿ�: bid-api-open-client
2025-09-01 16:28:01,852 - ERROR - ��¡ʧ�� bid-api-open-client: Cloning into 'cloned_repos\bid-api-open-client'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:28:01,853 - INFO - ��ʼ��¡�ֿ�: bid-api-open
2025-09-01 16:28:02,745 - ERROR - ��¡ʧ�� bid-api-open: Cloning into 'cloned_repos\bid-api-open'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:28:02,746 - INFO - ��ʼ��¡�ֿ�: bid-chain-client
2025-09-01 16:28:03,621 - ERROR - ��¡ʧ�� bid-chain-client: Cloning into 'cloned_repos\bid-chain-client'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:28:03,622 - INFO - ��ʼ��¡�ֿ�: dataScreen
2025-09-01 16:28:04,407 - ERROR - ��¡ʧ�� dataScreen: Cloning into 'cloned_repos\dataScreen'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:28:04,407 - INFO - ��ʼ��¡�ֿ�: bid-expert-ui
2025-09-01 16:28:05,284 - ERROR - ��¡ʧ�� bid-expert-ui: Cloning into 'cloned_repos\bid-expert-ui'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:28:05,284 - INFO - ��ʼ��¡�ֿ�: expert-manage-mp-ui
2025-09-01 16:28:06,144 - ERROR - ��¡ʧ�� expert-manage-mp-ui: Cloning into 'cloned_repos\expert-manage-mp-ui'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:28:06,145 - INFO - ��ʼ��¡�ֿ�: expert-manage-ui
2025-09-01 16:28:07,031 - ERROR - ��¡ʧ�� expert-manage-ui: Cloning into 'cloned_repos\expert-manage-ui'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:28:07,031 - INFO - ��ʼ��¡�ֿ�: bid-ncc-api
2025-09-01 16:28:07,782 - ERROR - ��¡ʧ�� bid-ncc-api: Cloning into 'cloned_repos\bid-ncc-api'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:28:07,782 - INFO - ��ʼ��¡�ֿ�: cws-api
2025-09-01 16:28:08,797 - ERROR - ��¡ʧ�� cws-api: Cloning into 'cloned_repos\cws-api'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:28:08,797 - INFO - ��ʼ��¡�ֿ�: bid-azalea-ui
2025-09-01 16:28:09,651 - ERROR - ��¡ʧ�� bid-azalea-ui: Cloning into 'cloned_repos\bid-azalea-ui'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:28:09,651 - INFO - ��ʼ��¡�ֿ�: bid-common-util
2025-09-01 16:28:10,440 - ERROR - ��¡ʧ�� bid-common-util: Cloning into 'cloned_repos\bid-common-util'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:28:10,440 - INFO - ��ʼ��¡�ֿ�: bid-common-bean
2025-09-01 16:28:11,293 - ERROR - ��¡ʧ�� bid-common-bean: Cloning into 'cloned_repos\bid-common-bean'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:28:11,293 - INFO - ��ʼ��¡�ֿ�: bid-ocr-server
2025-09-01 16:28:12,105 - ERROR - ��¡ʧ�� bid-ocr-server: Cloning into 'cloned_repos\bid-ocr-server'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:28:12,105 - INFO - ��ʼ��¡�ֿ�: WechatGatwayApi
2025-09-01 16:28:12,784 - ERROR - ��¡ʧ�� WechatGatwayApi: Cloning into 'cloned_repos\WechatGatwayApi'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:28:12,784 - INFO - ��ʼ��¡�ֿ�: UserApi
2025-09-01 16:28:13,592 - ERROR - ��¡ʧ�� UserApi: Cloning into 'cloned_repos\UserApi'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:28:13,592 - INFO - ��ʼ��¡�ֿ�: SyncApi
2025-09-01 16:28:14,382 - ERROR - ��¡ʧ�� SyncApi: Cloning into 'cloned_repos\SyncApi'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:28:14,382 - INFO - ��ʼ��¡�ֿ�: SupplierApi
2025-09-01 16:28:15,249 - ERROR - ��¡ʧ�� SupplierApi: Cloning into 'cloned_repos\SupplierApi'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:28:15,249 - INFO - ��ʼ��¡�ֿ�: SuperviseDataApiClient
2025-09-01 16:28:16,158 - ERROR - ��¡ʧ�� SuperviseDataApiClient: Cloning into 'cloned_repos\SuperviseDataApiClient'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:28:16,158 - INFO - ��ʼ��¡�ֿ�: ServerConfigure
2025-09-01 16:28:16,938 - ERROR - ��¡ʧ�� ServerConfigure: Cloning into 'cloned_repos\ServerConfigure'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:28:16,938 - INFO - ��ʼ��¡�ֿ�: RuleSet10MessageProducer
2025-09-01 16:28:17,704 - ERROR - ��¡ʧ�� RuleSet10MessageProducer: Cloning into 'cloned_repos\RuleSet10MessageProducer'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:28:17,704 - INFO - ��ʼ��¡�ֿ�: PublicServiceWebSite
2025-09-01 16:28:18,521 - ERROR - ��¡ʧ�� PublicServiceWebSite: Cloning into 'cloned_repos\PublicServiceWebSite'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:28:18,522 - INFO - ��ʼ��¡�ֿ�: PublicServiceMini
2025-09-01 16:28:19,306 - ERROR - ��¡ʧ�� PublicServiceMini: Cloning into 'cloned_repos\PublicServiceMini'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:28:19,307 - INFO - ��ʼ��¡�ֿ�: PublicServiceDashBoard
2025-09-01 16:28:20,176 - ERROR - ��¡ʧ�� PublicServiceDashBoard: Cloning into 'cloned_repos\PublicServiceDashBoard'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:28:20,176 - INFO - ��ʼ��¡�ֿ�: PlatformApi
2025-09-01 16:28:21,019 - ERROR - ��¡ʧ�� PlatformApi: Cloning into 'cloned_repos\PlatformApi'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:28:21,019 - INFO - ��ʼ��¡�ֿ�: MonitorUserApi
2025-09-01 16:28:21,870 - ERROR - ��¡ʧ�� MonitorUserApi: Cloning into 'cloned_repos\MonitorUserApi'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:28:21,870 - INFO - ��ʼ��¡�ֿ�: MonitorUI
2025-09-01 16:28:22,679 - ERROR - ��¡ʧ�� MonitorUI: Cloning into 'cloned_repos\MonitorUI'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:28:22,679 - INFO - ��ʼ��¡�ֿ�: MonitorBusinessApi
2025-09-01 16:28:23,433 - ERROR - ��¡ʧ�� MonitorBusinessApi: Cloning into 'cloned_repos\MonitorBusinessApi'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:28:23,433 - INFO - ��ʼ��¡�ֿ�: MobileApp
2025-09-01 16:28:24,267 - ERROR - ��¡ʧ�� MobileApp: Cloning into 'cloned_repos\MobileApp'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:28:24,267 - INFO - ��ʼ��¡�ֿ�: MiniWebApp
2025-09-01 16:28:25,017 - ERROR - ��¡ʧ�� MiniWebApp: Cloning into 'cloned_repos\MiniWebApp'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:28:25,017 - INFO - ��ʼ��¡�ֿ�: MarketPlayersAPI
2025-09-01 16:28:25,768 - ERROR - ��¡ʧ�� MarketPlayersAPI: Cloning into 'cloned_repos\MarketPlayersAPI'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:28:25,768 - INFO - ��ʼ��¡�ֿ�: DataSyncApi
2025-09-01 16:28:26,657 - ERROR - ��¡ʧ�� DataSyncApi: Cloning into 'cloned_repos\DataSyncApi'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:28:26,657 - INFO - ��ʼ��¡�ֿ�: ContentManageAPI
2025-09-01 16:28:27,469 - ERROR - ��¡ʧ�� ContentManageAPI: Cloning into 'cloned_repos\ContentManageAPI'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:28:27,470 - INFO - ��ʼ��¡�ֿ�: BulletinSearchApi
2025-09-01 16:28:28,364 - ERROR - ��¡ʧ�� BulletinSearchApi: Cloning into 'cloned_repos\BulletinSearchApi'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:28:28,365 - INFO - ��ʼ��¡�ֿ�: BulletinMessageBroker
2025-09-01 16:28:29,267 - ERROR - ��¡ʧ�� BulletinMessageBroker: Cloning into 'cloned_repos\BulletinMessageBroker'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:28:29,267 - INFO - ��ʼ��¡�ֿ�: bid-ocr
2025-09-01 16:28:30,182 - ERROR - ��¡ʧ�� bid-ocr: Cloning into 'cloned_repos\bid-ocr'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:28:30,182 - INFO - ��ʼ��¡�ֿ�: bid-expert-api
2025-09-01 16:28:31,019 - ERROR - ��¡ʧ�� bid-expert-api: Cloning into 'cloned_repos\bid-expert-api'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:28:31,020 - INFO - ��ʼ��¡�ֿ�: file-preview
2025-09-01 16:28:31,843 - ERROR - ��¡ʧ�� file-preview: Cloning into 'cloned_repos\file-preview'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:28:31,843 - INFO - ��ʼ��¡�ֿ�: sms
2025-09-01 16:28:32,674 - ERROR - ��¡ʧ�� sms: Cloning into 'cloned_repos\sms'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:28:32,675 - INFO - ��ʼ��¡�ֿ�: workflow
2025-09-01 16:28:33,531 - ERROR - ��¡ʧ�� workflow: Cloning into 'cloned_repos\workflow'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:28:33,531 - INFO - ��ʼ��¡�ֿ�: bid-ui-tenderee
2025-09-01 16:28:34,341 - ERROR - ��¡ʧ�� bid-ui-tenderee: Cloning into 'cloned_repos\bid-ui-tenderee'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:28:34,341 - INFO - ��ʼ��¡�ֿ�: bid-ui-maintain
2025-09-01 16:28:35,155 - ERROR - ��¡ʧ�� bid-ui-maintain: Cloning into 'cloned_repos\bid-ui-maintain'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:28:35,156 - INFO - ��ʼ��¡�ֿ�: bid-ui-supplier
2025-09-01 16:28:35,970 - ERROR - ��¡ʧ�� bid-ui-supplier: Cloning into 'cloned_repos\bid-ui-supplier'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:28:35,970 - INFO - ��ʼ��¡�ֿ�: bid-ui-agency
2025-09-01 16:28:36,811 - ERROR - ��¡ʧ�� bid-ui-agency: Cloning into 'cloned_repos\bid-ui-agency'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:28:36,811 - INFO - ��ʼ��¡�ֿ�: bid-common-security
2025-09-01 16:28:37,557 - ERROR - ��¡ʧ�� bid-common-security: Cloning into 'cloned_repos\bid-common-security'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:28:37,558 - INFO - ��ʼ��¡�ֿ�: bid-openapi-client
2025-09-01 16:28:38,479 - ERROR - ��¡ʧ�� bid-openapi-client: Cloning into 'cloned_repos\bid-openapi-client'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:28:38,479 - INFO - ��ʼ��¡�ֿ�: bid-common-datascope
2025-09-01 16:28:39,263 - ERROR - ��¡ʧ�� bid-common-datascope: Cloning into 'cloned_repos\bid-common-datascope'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:28:39,263 - INFO - ��ʼ��¡�ֿ�: bid-common-core
2025-09-01 16:28:40,057 - ERROR - ��¡ʧ�� bid-common-core: Cloning into 'cloned_repos\bid-common-core'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:28:40,057 - INFO - ��ʼ��¡�ֿ�: bid-cws-client
2025-09-01 16:28:40,929 - ERROR - ��¡ʧ�� bid-cws-client: Cloning into 'cloned_repos\bid-cws-client'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:28:40,929 - INFO - ��ʼ��¡�ֿ�: bid-anemone-wechat
2025-09-01 16:28:41,942 - ERROR - ��¡ʧ�� bid-anemone-wechat: Cloning into 'cloned_repos\bid-anemone-wechat'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:28:41,942 - INFO - ��ʼ��¡�ֿ�: bid-iris
2025-09-01 16:28:42,882 - ERROR - ��¡ʧ�� bid-iris: Cloning into 'cloned_repos\bid-iris'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:28:42,882 - INFO - ��ʼ��¡�ֿ�: bid-crocus
2025-09-01 16:28:43,801 - ERROR - ��¡ʧ�� bid-crocus: Cloning into 'cloned_repos\bid-crocus'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:28:43,801 - INFO - ��ʼ��¡�ֿ�: bid-expert-client
2025-09-01 16:28:44,553 - ERROR - ��¡ʧ�� bid-expert-client: Cloning into 'cloned_repos\bid-expert-client'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:28:44,554 - INFO - ��ʼ��¡�ֿ�: bid-api
2025-09-01 16:28:45,291 - ERROR - ��¡ʧ�� bid-api: Cloning into 'cloned_repos\bid-api'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:28:45,292 - INFO - ��ʼ��¡�ֿ�: bid-freesia
2025-09-01 16:28:46,161 - ERROR - ��¡ʧ�� bid-freesia: Cloning into 'cloned_repos\bid-freesia'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:28:46,162 - INFO - ��ʼ��¡�ֿ�: licenseDemo
2025-09-01 16:28:47,036 - ERROR - ��¡ʧ�� licenseDemo: Cloning into 'cloned_repos\licenseDemo'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:28:47,037 - INFO - ��ʼ��¡�ֿ�: generatorGUI
2025-09-01 16:28:47,995 - ERROR - ��¡ʧ�� generatorGUI: Cloning into 'cloned_repos\generatorGUI'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:28:47,995 - INFO - ��ʼ��¡�ֿ�: generatorAPI
2025-09-01 16:28:48,921 - ERROR - ��¡ʧ�� generatorAPI: Cloning into 'cloned_repos\generatorAPI'...
Host key verification failed.
fatal: Could not read from remote repository.

Please make sure you have the correct access rights
and the repository exists.

2025-09-01 16:28:48,923 - INFO - 
��¡���ͳ��:
2025-09-01 16:28:48,923 - INFO - �ɹ�: 0
2025-09-01 16:28:48,924 - INFO - ʧ��: 135
2025-09-01 16:28:48,924 - INFO - ʧ�ܵĲֿ�: bid-common-service, bid-file-compare, bid-file-tasks, bussiness-notice, preview-wpsqyd-client, preview-wpsqyd-api, preview-wps, platform-index-ui, platform-cloud, platform-ui, heterogeneous-api, heterogeneous-ui, yunxin-callback-api, bid-invoice-client, bid-encryption-tool, bid-doc-maintenance, bid-invoice-api, HZW-TA, bid-fee-deposit-client, bid-fee-expert-client, bid-fee-deposit, bid-fee-expert, bid-doc-tool-api, bid-document-tool, bid-office-online, bid-sign-file, bid-spider-doc, bid-spider-client, push-tool-api, push-tool-ui, bid-textToSpeech, bid-spider-api, spider-main, ctbpsp_crawler, portal-index-ui, portal-index-ui, portal-ui, portal-cloud, bid-clear-tool-client, bid-clear-tool, publishTool, bid-common-ratelimiter, devops, bid-omr-api, bid-ui-omr, datascrenn2.0, bid-sign-expert, bid-oa-msgpush, bid-oa, bid-expert-client, bid-cloud, expert-wechat-app, expert-api, expert-ui, data-file-tran-api, bid-config, source, bid-hub-client-sdk, source, bid-hub-client, bid-hub-server-api-client, bid-file, bid-file-decrypt, bid-hub-server-ui, bid-hub-server-api, bid-sign, bid-docs, bid-tenderee-ui, cpms, bid-license, bid-chain, zjk-python-test, JSTCC, bid-amoeba, GZZJK_REACT, GZZJK, bid-chain-client-ui, bid-chain-client-sdk, bid-api-open-client, bid-api-open, bid-chain-client, dataScreen, bid-expert-ui, expert-manage-mp-ui, expert-manage-ui, bid-ncc-api, cws-api, bid-azalea-ui, bid-common-util, bid-common-bean, bid-ocr-server, WechatGatwayApi, UserApi, SyncApi, SupplierApi, SuperviseDataApiClient, ServerConfigure, RuleSet10MessageProducer, PublicServiceWebSite, PublicServiceMini, PublicServiceDashBoard, PlatformApi, MonitorUserApi, MonitorUI, MonitorBusinessApi, MobileApp, MiniWebApp, MarketPlayersAPI, DataSyncApi, ContentManageAPI, BulletinSearchApi, BulletinMessageBroker, bid-ocr, bid-expert-api, file-preview, sms, workflow, bid-ui-tenderee, bid-ui-maintain, bid-ui-supplier, bid-ui-agency, bid-common-security, bid-openapi-client, bid-common-datascope, bid-common-core, bid-cws-client, bid-anemone-wechat, bid-iris, bid-crocus, bid-expert-client, bid-api, bid-freesia, licenseDemo, generatorGUI, generatorAPI
2025-09-01 16:34:56,374 - INFO - ��ʼִ�вֿ��¡�ű�
2025-09-01 16:34:56,374 - INFO - �ҵ�JSON�ļ�: ['repos_data.json', 'repos_data1.json']
2025-09-01 16:34:56,376 - INFO - �ɹ���ȡ�ļ�: repos_data.json, ���� 100 ���ֿ�
2025-09-01 16:34:56,376 - INFO - �ҵ��ֿ�: bid-common-service -> *********************:hzw/JSTCCUP/bid-common-service.git
2025-09-01 16:34:56,376 - INFO - �ҵ��ֿ�: bid-file-compare -> *********************:hzw/JSTCCUP/bid-file-compare.git
2025-09-01 16:34:56,376 - INFO - �ҵ��ֿ�: bid-file-tasks -> *********************:hzw/JSTCCUP/bid-file-tasks.git
2025-09-01 16:34:56,376 - INFO - �ҵ��ֿ�: bussiness-notice -> *********************:hzw/JSFGW/bussiness-notice.git
2025-09-01 16:34:56,376 - INFO - �ҵ��ֿ�: preview-wpsqyd-client -> *********************:hzw/JSTCCUP/preview-wpsqyd-client.git
2025-09-01 16:34:56,376 - INFO - �ҵ��ֿ�: preview-wpsqyd-api -> *********************:hzw/JSTCCUP/preview-wpsqyd-api.git
2025-09-01 16:34:56,376 - INFO - �ҵ��ֿ�: preview-wps -> *********************:hzw/JSTCCUP/preview-wps.git
2025-09-01 16:34:56,376 - INFO - �ҵ��ֿ�: platform-index-ui -> *********************:hzw/PORTAL_NEW/platform-index-ui.git
2025-09-01 16:34:56,376 - INFO - �ҵ��ֿ�: platform-cloud -> *********************:hzw/PORTAL_NEW/platform-cloud.git
2025-09-01 16:34:56,376 - INFO - �ҵ��ֿ�: platform-ui -> *********************:hzw/PORTAL_NEW/platform-ui.git
2025-09-01 16:34:56,376 - INFO - �ҵ��ֿ�: heterogeneous-api -> *********************:hzw/Heterogeneous/heterogeneous-api.git
2025-09-01 16:34:56,376 - INFO - �ҵ��ֿ�: heterogeneous-ui -> *********************:hzw/Heterogeneous/heterogeneous-ui.git
2025-09-01 16:34:56,376 - INFO - �ҵ��ֿ�: yunxin-callback-api -> *********************:hzw/JSTCCUP/yunxin-callback-api.git
2025-09-01 16:34:56,377 - INFO - �ҵ��ֿ�: bid-invoice-client -> *********************:hzw/JSTCCUP/bid-invoice-client.git
2025-09-01 16:34:56,377 - INFO - �ҵ��ֿ�: bid-encryption-tool -> *********************:hzw/JSTCCUP/bid-encryption-tool.git
2025-09-01 16:34:56,377 - INFO - �ҵ��ֿ�: bid-doc-maintenance -> *********************:hzw/JSTCCUP/bid-doc-maintenance.git
2025-09-01 16:34:56,377 - INFO - �ҵ��ֿ�: bid-invoice-api -> *********************:hzw/JSTCCUP/bid-invoice-api.git
2025-09-01 16:34:56,377 - INFO - �ҵ��ֿ�: HZW-TA -> *********************:hzw/Test/HZW-TA.git
2025-09-01 16:34:56,377 - INFO - �ҵ��ֿ�: bid-fee-deposit-client -> *********************:hzw/JSTCCUP/bid-fee-deposit-client.git
2025-09-01 16:34:56,377 - INFO - �ҵ��ֿ�: bid-fee-expert-client -> *********************:hzw/JSTCCUP/bid-fee-expert-client.git
2025-09-01 16:34:56,377 - INFO - �ҵ��ֿ�: bid-fee-deposit -> *********************:hzw/JSTCCUP/bid-fee-deposit.git
2025-09-01 16:34:56,377 - INFO - �ҵ��ֿ�: bid-fee-expert -> *********************:hzw/JSTCCUP/bid-fee-expert.git
2025-09-01 16:34:56,377 - INFO - �ҵ��ֿ�: bid-doc-tool-api -> *********************:hzw/JSTCCUP/bid-doc-tool-api.git
2025-09-01 16:34:56,377 - INFO - �ҵ��ֿ�: bid-document-tool -> *********************:hzw/JSTCCUP/bid-document-tool.git
2025-09-01 16:34:56,377 - INFO - �ҵ��ֿ�: bid-office-online -> *********************:hzw/JSTCCUP/bid-office-online.git
2025-09-01 16:34:56,377 - INFO - �ҵ��ֿ�: bid-sign-file -> *********************:hzw/JSTCCUP/bid-sign-file.git
2025-09-01 16:34:56,377 - INFO - �ҵ��ֿ�: bid-spider-doc -> *********************:hzw/JSTCCUP/bid-spider-doc.git
2025-09-01 16:34:56,377 - INFO - �ҵ��ֿ�: bid-spider-client -> *********************:hzw/JSTCCUP/bid-spider-client.git
2025-09-01 16:34:56,378 - INFO - �ҵ��ֿ�: push-tool-api -> *********************:hzw/PUSHTOOL/push-tool-api.git
2025-09-01 16:34:56,378 - INFO - �ҵ��ֿ�: push-tool-ui -> *********************:hzw/PUSHTOOL/push-tool-ui.git
2025-09-01 16:34:56,378 - INFO - �ҵ��ֿ�: bid-textToSpeech -> *********************:hzw/JSTCCUP/bid-textToSpeech.git
2025-09-01 16:34:56,378 - INFO - �ҵ��ֿ�: bid-spider-api -> *********************:hzw/JSTCCUP/bid-spider-api.git
2025-09-01 16:34:56,378 - INFO - �ҵ��ֿ�: spider-main -> *********************:hzw/JITC/spider-main.git
2025-09-01 16:34:56,378 - INFO - �ҵ��ֿ�: ctbpsp_crawler -> *********************:hzw/DATASCRAPING/ctbpsp_crawler.git
2025-09-01 16:34:56,378 - INFO - �ҵ��ֿ�: portal-index-ui -> *********************:hzw/SAASPORTAL/portal-index-ui.git
2025-09-01 16:34:56,378 - INFO - �ҵ��ֿ�: portal-index-ui -> *********************:hzw/PORTAL/portal-index-ui.git
2025-09-01 16:34:56,378 - INFO - �ҵ��ֿ�: portal-ui -> *********************:hzw/PORTAL/portal-ui.git
2025-09-01 16:34:56,378 - INFO - �ҵ��ֿ�: portal-cloud -> *********************:hzw/PORTAL/portal-cloud.git
2025-09-01 16:34:56,378 - INFO - �ҵ��ֿ�: bid-clear-tool-client -> *********************:hzw/JSTCCUP/bid-clear-tool-client.git
2025-09-01 16:34:56,378 - INFO - �ҵ��ֿ�: bid-clear-tool -> *********************:hzw/JSTCCUP/bid-clear-tool.git
2025-09-01 16:34:56,378 - INFO - �ҵ��ֿ�: publishTool -> *********************:hzw/JSFGW/publishTool.git
2025-09-01 16:34:56,378 - INFO - �ҵ��ֿ�: bid-common-ratelimiter -> *********************:hzw/JSTCCUP/bid-common-ratelimiter.git
2025-09-01 16:34:56,378 - INFO - �ҵ��ֿ�: devops -> *********************:hzw/JSFGW/devops.git
2025-09-01 16:34:56,379 - INFO - �ҵ��ֿ�: bid-omr-api -> *********************:hzw/JSTCCUP/bid-omr-api.git
2025-09-01 16:34:56,379 - INFO - �ҵ��ֿ�: bid-ui-omr -> *********************:hzw/JSTCCUP/bid-ui-omr.git
2025-09-01 16:34:56,379 - INFO - �ҵ��ֿ�: datascrenn2.0 -> *********************:hzw/JSFGW/datascrenn2.0.git
2025-09-01 16:34:56,379 - INFO - �ҵ��ֿ�: bid-sign-expert -> *********************:hzw/JSTCCUP/bid-sign-expert.git
2025-09-01 16:34:56,379 - INFO - �ҵ��ֿ�: bid-oa-msgpush -> *********************:hzw/JSTCCUP/bid-oa-msgpush.git
2025-09-01 16:34:56,379 - INFO - �ҵ��ֿ�: bid-oa -> *********************:hzw/JSTCCUP/bid-oa.git
2025-09-01 16:34:56,379 - INFO - �ҵ��ֿ�: bid-expert-client -> *********************:hzw/EXPERT/bid-expert-client.git
2025-09-01 16:34:56,379 - INFO - �ҵ��ֿ�: bid-cloud -> *********************:hzw/EXPERT/bid-cloud.git
2025-09-01 16:34:56,379 - INFO - �ҵ��ֿ�: expert-wechat-app -> *********************:hzw/EXPERT/expert-wechat-app.git
2025-09-01 16:34:56,379 - INFO - �ҵ��ֿ�: expert-api -> *********************:hzw/EXPERT/expert-api.git
2025-09-01 16:34:56,379 - INFO - �ҵ��ֿ�: expert-ui -> *********************:hzw/EXPERT/expert-ui.git
2025-09-01 16:34:56,379 - INFO - �ҵ��ֿ�: data-file-tran-api -> *********************:hzw/NJBK/data-file-tran-api.git
2025-09-01 16:34:56,379 - INFO - �ҵ��ֿ�: bid-config -> *********************:hzw/JSTCCUP/bid-config.git
2025-09-01 16:34:56,379 - INFO - �ҵ��ֿ�: source -> *********************:hzw/NJFGW/source.git
2025-09-01 16:34:56,379 - INFO - �ҵ��ֿ�: bid-hub-client-sdk -> *********************:hzw/BIDHUB/bid-hub-client-sdk.git
2025-09-01 16:34:56,379 - INFO - �ҵ��ֿ�: source -> *********************:hzw/NJBK/source.git
2025-09-01 16:34:56,380 - INFO - �ҵ��ֿ�: bid-hub-client -> *********************:hzw/BIDHUB/bid-hub-client.git
2025-09-01 16:34:56,380 - INFO - �ҵ��ֿ�: bid-hub-server-api-client -> *********************:hzw/BIDHUB/bid-hub-server-api-client.git
2025-09-01 16:34:56,380 - INFO - �ҵ��ֿ�: bid-file -> *********************:hzw/JSTCCUP/bid-file.git
2025-09-01 16:34:56,380 - INFO - �ҵ��ֿ�: bid-file-decrypt -> *********************:hzw/JSTCCUP/bid-file-decrypt.git
2025-09-01 16:34:56,380 - INFO - �ҵ��ֿ�: bid-hub-server-ui -> *********************:hzw/BIDHUB/bid-hub-server-ui.git
2025-09-01 16:34:56,380 - INFO - �ҵ��ֿ�: bid-hub-server-api -> *********************:hzw/BIDHUB/bid-hub-server-api.git
2025-09-01 16:34:56,380 - INFO - �ҵ��ֿ�: bid-sign -> *********************:hzw/JSTCCUP/bid-sign.git
2025-09-01 16:34:56,380 - INFO - �ҵ��ֿ�: bid-docs -> *********************:hzw/JSTCCUP/bid-docs.git
2025-09-01 16:34:56,380 - INFO - �ҵ��ֿ�: bid-tenderee-ui -> *********************:hzw/SAAS/bid-tenderee-ui.git
2025-09-01 16:34:56,381 - INFO - �ҵ��ֿ�: cpms -> *********************:hzw/NJBK/cpms.git
2025-09-01 16:34:56,381 - INFO - �ҵ��ֿ�: bid-license -> *********************:hzw/JSTCCUP/bid-license.git
2025-09-01 16:34:56,381 - INFO - �ҵ��ֿ�: bid-chain -> *********************:hzw/BIDHUB/bid-chain.git
2025-09-01 16:34:56,381 - INFO - �ҵ��ֿ�: zjk-python-test -> *********************:hzw/LKM/zjk-python-test.git
2025-09-01 16:34:56,381 - INFO - �ҵ��ֿ�: JSTCC -> *********************:hzw/testing/JSTCC.git
2025-09-01 16:34:56,381 - INFO - �ҵ��ֿ�: bid-amoeba -> *********************:hzw/JSTCCUP/bid-amoeba.git
2025-09-01 16:34:56,381 - INFO - �ҵ��ֿ�: GZZJK_REACT -> *********************:hzw/LKM/GZZJK_REACT.git
2025-09-01 16:34:56,381 - INFO - �ҵ��ֿ�: GZZJK -> *********************:hzw/JITC/GZZJK.git
2025-09-01 16:34:56,381 - INFO - �ҵ��ֿ�: bid-chain-client-ui -> *********************:hzw/JSTCCUP/bid-chain-client-ui.git
2025-09-01 16:34:56,381 - INFO - �ҵ��ֿ�: bid-chain-client-sdk -> *********************:hzw/JSTCCUP/bid-chain-client-sdk.git
2025-09-01 16:34:56,382 - INFO - �ҵ��ֿ�: bid-api-open-client -> *********************:hzw/JSTCCUP/bid-api-open-client.git
2025-09-01 16:34:56,382 - INFO - �ҵ��ֿ�: bid-api-open -> *********************:hzw/JSTCCUP/bid-api-open.git
2025-09-01 16:34:56,382 - INFO - �ҵ��ֿ�: bid-chain-client -> *********************:hzw/JSTCCUP/bid-chain-client.git
2025-09-01 16:34:56,382 - INFO - �ҵ��ֿ�: dataScreen -> *********************:hzw/JSFGW/dataScreen.git
2025-09-01 16:34:56,382 - INFO - �ҵ��ֿ�: bid-expert-ui -> *********************:hzw/JSTCCUP/bid-expert-ui.git
2025-09-01 16:34:56,382 - INFO - �ҵ��ֿ�: expert-manage-mp-ui -> *********************:hzw/JSTCCUP/expert-manage-mp-ui.git
2025-09-01 16:34:56,382 - INFO - �ҵ��ֿ�: expert-manage-ui -> *********************:hzw/JSTCCUP/expert-manage-ui.git
2025-09-01 16:34:56,382 - INFO - �ҵ��ֿ�: bid-ncc-api -> *********************:hzw/JSTCCUP/bid-ncc-api.git
2025-09-01 16:34:56,382 - INFO - �ҵ��ֿ�: cws-api -> *********************:hzw/JSTCCUP/cws-api.git
2025-09-01 16:34:56,382 - INFO - �ҵ��ֿ�: bid-azalea-ui -> *********************:hzw/JSTCCUP/bid-azalea-ui.git
2025-09-01 16:34:56,382 - INFO - �ҵ��ֿ�: bid-common-util -> *********************:hzw/JSTCCUP/bid-common-util.git
2025-09-01 16:34:56,382 - INFO - �ҵ��ֿ�: bid-common-bean -> *********************:hzw/JSTCCUP/bid-common-bean.git
2025-09-01 16:34:56,382 - INFO - �ҵ��ֿ�: bid-ocr-server -> *********************:hzw/JSTCCUP/bid-ocr-server.git
2025-09-01 16:34:56,383 - INFO - �ҵ��ֿ�: WechatGatwayApi -> *********************:hzw/JSFGW/WechatGatwayApi.git
2025-09-01 16:34:56,383 - INFO - �ҵ��ֿ�: UserApi -> *********************:hzw/JSFGW/UserApi.git
2025-09-01 16:34:56,383 - INFO - �ҵ��ֿ�: SyncApi -> *********************:hzw/JSFGW/SyncApi.git
2025-09-01 16:34:56,383 - INFO - �ҵ��ֿ�: SupplierApi -> *********************:hzw/JSFGW/SupplierApi.git
2025-09-01 16:34:56,383 - INFO - �ҵ��ֿ�: SuperviseDataApiClient -> *********************:hzw/JSFGW/SuperviseDataApiClient.git
2025-09-01 16:34:56,383 - INFO - �ҵ��ֿ�: ServerConfigure -> *********************:hzw/JSFGW/ServerConfigure.git
2025-09-01 16:34:56,383 - INFO - �ҵ��ֿ�: RuleSet10MessageProducer -> *********************:hzw/JSFGW/RuleSet10MessageProducer.git
2025-09-01 16:34:56,383 - INFO - �ҵ��ֿ�: PublicServiceWebSite -> *********************:hzw/JSFGW/PublicServiceWebSite.git
2025-09-01 16:34:56,383 - INFO - �ҵ��ֿ�: PublicServiceMini -> *********************:hzw/JSFGW/PublicServiceMini.git
2025-09-01 16:34:56,384 - INFO - �ɹ���ȡ�ļ�: repos_data1.json, ���� 35 ���ֿ�
2025-09-01 16:34:56,384 - INFO - �ҵ��ֿ�: PublicServiceDashBoard -> *********************:hzw/JSFGW/PublicServiceDashBoard.git
2025-09-01 16:34:56,384 - INFO - �ҵ��ֿ�: PlatformApi -> *********************:hzw/JSFGW/PlatformApi.git
2025-09-01 16:34:56,384 - INFO - �ҵ��ֿ�: MonitorUserApi -> *********************:hzw/JSFGW/MonitorUserApi.git
2025-09-01 16:34:56,385 - INFO - �ҵ��ֿ�: MonitorUI -> *********************:hzw/JSFGW/MonitorUI.git
2025-09-01 16:34:56,385 - INFO - �ҵ��ֿ�: MonitorBusinessApi -> *********************:hzw/JSFGW/MonitorBusinessApi.git
2025-09-01 16:34:56,385 - INFO - �ҵ��ֿ�: MobileApp -> *********************:hzw/JSFGW/MobileApp.git
2025-09-01 16:34:56,385 - INFO - �ҵ��ֿ�: MiniWebApp -> *********************:hzw/JSFGW/MiniWebApp.git
2025-09-01 16:34:56,385 - INFO - �ҵ��ֿ�: MarketPlayersAPI -> *********************:hzw/JSFGW/MarketPlayersAPI.git
2025-09-01 16:34:56,385 - INFO - �ҵ��ֿ�: DataSyncApi -> *********************:hzw/JSFGW/DataSyncApi.git
2025-09-01 16:34:56,385 - INFO - �ҵ��ֿ�: ContentManageAPI -> *********************:hzw/JSFGW/ContentManageAPI.git
2025-09-01 16:34:56,385 - INFO - �ҵ��ֿ�: BulletinSearchApi -> *********************:hzw/JSFGW/BulletinSearchApi.git
2025-09-01 16:34:56,385 - INFO - �ҵ��ֿ�: BulletinMessageBroker -> *********************:hzw/JSFGW/BulletinMessageBroker.git
2025-09-01 16:34:56,385 - INFO - �ҵ��ֿ�: bid-ocr -> *********************:hzw/JSTCCUP/bid-ocr.git
2025-09-01 16:34:56,385 - INFO - �ҵ��ֿ�: bid-expert-api -> *********************:hzw/JSTCCUP/bid-expert-api.git
2025-09-01 16:34:56,386 - INFO - �ҵ��ֿ�: file-preview -> *********************:hzw/JSTCCUP/file-preview.git
2025-09-01 16:34:56,386 - INFO - �ҵ��ֿ�: sms -> *********************:hzw/JSTCCUP/sms.git
2025-09-01 16:34:56,386 - INFO - �ҵ��ֿ�: workflow -> *********************:hzw/JSTCCUP/workflow.git
2025-09-01 16:34:56,386 - INFO - �ҵ��ֿ�: bid-ui-tenderee -> *********************:hzw/JSTCCUP/bid-ui-tenderee.git
2025-09-01 16:34:56,386 - INFO - �ҵ��ֿ�: bid-ui-maintain -> *********************:hzw/JSTCCUP/bid-ui-maintain.git
2025-09-01 16:34:56,386 - INFO - �ҵ��ֿ�: bid-ui-supplier -> *********************:hzw/JSTCCUP/bid-ui-supplier.git
2025-09-01 16:34:56,386 - INFO - �ҵ��ֿ�: bid-ui-agency -> *********************:hzw/JSTCCUP/bid-ui-agency.git
2025-09-01 16:34:56,387 - INFO - �ҵ��ֿ�: bid-common-security -> *********************:hzw/JSTCCUP/bid-common-security.git
2025-09-01 16:34:56,387 - INFO - �ҵ��ֿ�: bid-openapi-client -> *********************:hzw/JSTCCUP/bid-openapi-client.git
2025-09-01 16:34:56,387 - INFO - �ҵ��ֿ�: bid-common-datascope -> *********************:hzw/JSTCCUP/bid-common-datascope.git
2025-09-01 16:34:56,387 - INFO - �ҵ��ֿ�: bid-common-core -> *********************:hzw/JSTCCUP/bid-common-core.git
2025-09-01 16:34:56,387 - INFO - �ҵ��ֿ�: bid-cws-client -> *********************:hzw/JSTCCUP/bid-cws-client.git
2025-09-01 16:34:56,387 - INFO - �ҵ��ֿ�: bid-anemone-wechat -> *********************:hzw/JSTCCUP/bid-anemone-wechat.git
2025-09-01 16:34:56,387 - INFO - �ҵ��ֿ�: bid-iris -> *********************:hzw/JSTCCUP/bid-iris.git
2025-09-01 16:34:56,388 - INFO - �ҵ��ֿ�: bid-crocus -> *********************:hzw/JSTCCUP/bid-crocus.git
2025-09-01 16:34:56,388 - INFO - �ҵ��ֿ�: bid-expert-client -> *********************:hzw/JSTCCUP/bid-expert-client.git
2025-09-01 16:34:56,388 - INFO - �ҵ��ֿ�: bid-api -> *********************:hzw/JSTCCUP/bid-api.git
2025-09-01 16:34:56,388 - INFO - �ҵ��ֿ�: bid-freesia -> *********************:hzw/JSTCCUP/bid-freesia.git
2025-09-01 16:34:56,388 - INFO - �ҵ��ֿ�: licenseDemo -> *********************:hzw/LicenseKey/clientDemo.git
2025-09-01 16:34:56,388 - INFO - �ҵ��ֿ�: generatorGUI -> *********************:hzw/LicenseKey/generatorGUI.git
2025-09-01 16:34:56,388 - INFO - �ҵ��ֿ�: generatorAPI -> *********************:hzw/LicenseKey/generatorAPI.git
2025-09-01 16:34:56,389 - INFO - �ܹ��ҵ� 135 ���ֿ�
2025-09-01 16:34:56,389 - INFO - SSH URLs�ѱ��浽: ssh_urls.txt
2025-09-01 16:35:07,251 - INFO - ��ʼ��¡�ֿ�: bid-common-service
2025-09-01 16:35:10,040 - INFO - �ɹ���¡�ֿ�: bid-common-service
2025-09-01 16:35:10,858 - INFO - ���� 4 ��Զ�̷�֧: ['2.14.6.2', 'E-009', 'P004', 'master']
2025-09-01 16:35:11,536 - INFO - �ɹ������֧: 2.14.6.2
2025-09-01 16:35:12,314 - INFO - �ɹ������֧: E-009
2025-09-01 16:35:13,301 - INFO - �ɹ������֧: P004
2025-09-01 16:35:14,209 - INFO - ��ʼ��¡�ֿ�: bid-file-compare
2025-09-01 16:35:19,000 - INFO - �ɹ���¡�ֿ�: bid-file-compare
2025-09-01 16:35:19,978 - INFO - ���� 3 ��Զ�̷�֧: ['6yue', 'master', 'realse']
2025-09-01 16:35:21,310 - INFO - �ɹ������֧: 6yue
2025-09-01 16:35:22,311 - INFO - �ɹ������֧: realse
2025-09-01 16:35:23,114 - INFO - ��ʼ��¡�ֿ�: bid-file-tasks
2025-09-01 16:35:25,812 - INFO - �ɹ���¡�ֿ�: bid-file-tasks
2025-09-01 16:35:26,731 - INFO - ���� 1 ��Զ�̷�֧: ['master']
2025-09-01 16:35:27,805 - INFO - ��ʼ��¡�ֿ�: bussiness-notice
2025-09-01 16:35:31,198 - INFO - �ɹ���¡�ֿ�: bussiness-notice
2025-09-01 16:35:32,263 - INFO - ���� 2 ��Զ�̷�֧: ['main', 'master']
2025-09-01 16:35:33,202 - INFO - ��ʼ��¡�ֿ�: preview-wpsqyd-client
2025-09-01 16:35:36,382 - INFO - �ɹ���¡�ֿ�: preview-wpsqyd-client
2025-09-01 16:35:37,512 - INFO - ���� 2 ��Զ�̷�֧: ['develop', 'master']
2025-09-01 16:35:38,591 - INFO - �ɹ������֧: develop
2025-09-01 16:35:39,779 - INFO - ��ʼ��¡�ֿ�: preview-wpsqyd-api
2025-09-01 16:35:43,103 - INFO - �ɹ���¡�ֿ�: preview-wpsqyd-api
2025-09-01 16:35:44,140 - INFO - ���� 2 ��Զ�̷�֧: ['develop', 'master']
2025-09-01 16:35:45,459 - WARNING - �����֧ʧ�� develop: fatal: a branch named 'develop' already exists

2025-09-01 16:35:46,593 - INFO - ��ʼ��¡�ֿ�: preview-wps
2025-09-01 16:35:54,746 - ERROR - ��¡ʧ�� preview-wps: Cloning into 'cloned_repos\preview-wps'...
error: couldn't set 'HEAD'
fatal: unable to update HEAD

2025-09-01 16:35:54,746 - INFO - ��ʼ��¡�ֿ�: platform-index-ui
2025-09-01 16:35:58,787 - INFO - �ɹ���¡�ֿ�: platform-index-ui
2025-09-01 16:35:59,845 - INFO - ���� 3 ��Զ�̷�֧: ['dev', 'dev_zhr', 'master']
2025-09-01 16:36:00,918 - INFO - �ɹ������֧: dev
2025-09-01 16:36:01,936 - INFO - �ɹ������֧: dev_zhr
2025-09-01 16:36:03,123 - INFO - ��ʼ��¡�ֿ�: platform-cloud
2025-09-01 16:36:08,964 - INFO - �ɹ���¡�ֿ�: platform-cloud
2025-09-01 16:36:09,937 - INFO - ���� 4 ��Զ�̷�֧: ['E027', 'E028', 'realse', 'vip']
2025-09-01 16:36:11,675 - INFO - �ɹ������֧: E027
2025-09-01 16:36:14,518 - WARNING - �����֧ʧ�� E028: error: could not write config file .git/config: Permission denied
error: could not write config file .git/config: Permission denied
error: couldn't set 'HEAD'
fatal: unable to update HEAD

2025-09-01 16:36:15,552 - WARNING - �����֧ʧ�� realse: fatal: a branch named 'realse' already exists

2025-09-01 16:36:16,745 - WARNING - �����֧ʧ�� vip: error: Your local changes to the following files would be overwritten by checkout:
	portal-api/platform-api-vip/src/main/java/com/hzw/bid/api/model/ResultCodeEnum.java
	portal-api/platform-api-vip/src/main/java/com/hzw/bid/api/model/SupplierQuotationDTO.java
	portal-api/platform-api-vip/src/main/java/com/hzw/bid/api/model/SupplierQuotationReq.java
	portal-api/pom.xml
	portal-api/portal-api-resource/src/main/java/com/hzw/resource/api/RemoteFileService.java
	portal-api/portal-api-system/src/main/java/com/hzw/system/api/RemoteCompanyService.java
	portal-common/portal-common-core/pom.xml
	portal-modules/platform-vip/Dockerfile
	portal-modules/platform-vip/pom.xml
	portal-modules/platform-vip/src/main/java/com/hzw/vip/controller/VipOrderAuditController.java
	portal-modules/platform-vip/src/main/java/com/hzw/vip/controller/request/HandAdjustVipReq.java
	portal-modules/platform-vip/src/main/java/com/hzw/vip/domain/Vip.java
	portal-modules/platform-vip/src/main/java/com/hzw/vip/domain/VipOrder.java
	portal-modules/platform-vip/src/main/java/com/hzw/vip/domain/dto/VipOrderAuditDTO.java
	portal-modules/platform-vip/src/main/java/com/hzw/vip/domain/vo/PersonalVipVO.java
	portal-modules/platform-vip/src/main/java/com/hzw/vip/domain/vo/VipLoginTipsVO.java
	portal-modules/platform-vip/src/main/java/com/hzw/vip/domain/vo/VipOpenRecordVO.java
	portal-modules/platform-vip/src/main/java/com/hzw/vip/domain/vo/VipUpVO.java
	portal-modules/platform-vip/src/main/java/com/hzw/vip/mapper/VipMapper.java
	portal-modules/platform-vip/src/main/java/com/hzw/vip/service/VipOrderAuditService.java
	portal-modules/platform-vip/src/main/java/com/hzw/vip/service/VipOrderService.java
	portal-modules/platform-vip/src/main/java/com/hzw/vip/service/VipService.java
	portal-modules/platform-vip/src/main/java/com/hzw/vip/service/VipSettingInfoService.java
	portal-modules/platform-vip/src/main/java/com/hzw/vip/service/VipSettingService.java
	portal-modules/platform-vip/src/main/java/com/hzw/vip/service/impl/VipLoginTipsServiceImpl.java
	portal-modules/platform-vip/src/main/java/com/hzw/vip/service/impl/VipOpenRecordServiceImpl.java
	portal-modules/platform-vip/src/main/java/com/hzw/vip/service/impl/VipOrderAuditServiceImpl.java
	portal-modules/platform-vip/src/main/java/com/hzw/vip/service/impl/VipOrderServiceImpl.java
	portal-modules/platform-vip/src/main/java/com/hzw/vip/service/impl/VipPurchaserSettingServiceImpl.java
	portal-modules/platform-vip/src/main/java/com/hzw/vip/service/impl/VipServiceImpl.java
	portal-modules/platform-vip/src/main/java/com/hzw/vip/service/impl/VipSettingInfoServiceImpl.java
	portal-modules/platform-vip/src/main/java/com/hzw/vip/service/impl/VipSettingServiceImpl.java
	portal-modules/platform-vip/src/main/resources/mapper/vip/VipMapper.xml
	portal-modules/platform-vip/src/main/resources/mapper/vip/VipOpenRecordMapper.xml
	portal-modules/platform-vip/src/main/resources/mapper/vip/VipOrderAuditMapper.xml
	portal-modules/platform-vip/src/main/resources/mapper/vip/VipOrderMapper.xml
	portal-modules/platform-vip/src/main/resources/mapper/vip/VipSettingDetailMapper.xml
	portal-modules/platform-vip/src/main/resources/mapper/vip/VipSettingMapper.xml
	portal-modules/pom.xml
	portal-modules/portal-resource/src/main/java/com/hzw/resource/dubbo/RemoteFileServiceImpl.java
	portal-modules/portal-system/src/main/java/com/hzw/system/controller/response/SupplierLibraryVO.java
	portal-modules/portal-system/src/main/java/com/hzw/system/domain/condition/SupplierLibraryCondition.java
	portal-modules/portal-system/src/main/java/com/hzw/system/dubbo/RemoteCompanyServiceImpl.java
	portal-modules/portal-system/src/main/resources/mapper/system/SysUserMapper.xml
	sql/vip.sql
Please commit your changes or stash them before you switch branches.
Aborting

2025-09-01 16:36:17,633 - INFO - ��ʼ��¡�ֿ�: platform-ui
2025-09-01 16:36:22,192 - INFO - �ɹ���¡�ֿ�: platform-ui
2025-09-01 16:36:23,206 - INFO - ���� 10 ��Զ�̷�֧: ['0303_zhr', 'E027', 'E027_2025_03_03', 'E027_2025_1_21', 'E028_yhy', 'main', 'main_zhr', 'master', 'rease1.0', 'rease_E028']
2025-09-01 16:36:24,559 - INFO - �ɹ������֧: 0303_zhr
2025-09-01 16:36:25,956 - INFO - �ɹ������֧: E027
2025-09-01 16:36:27,180 - INFO - �ɹ������֧: E027_2025_03_03
2025-09-01 16:36:28,288 - INFO - �ɹ������֧: E027_2025_1_21
2025-09-01 16:36:28,962 - INFO - �ɹ������֧: E028_yhy
2025-09-01 16:36:30,013 - INFO - �ɹ������֧: main_zhr
2025-09-01 16:36:30,546 - WARNING - �����֧ʧ�� rease1.0: fatal: a branch named 'rease1.0' already exists

2025-09-01 16:36:31,189 - INFO - �ɹ������֧: rease_E028
2025-09-01 16:36:32,145 - INFO - ��ʼ��¡�ֿ�: heterogeneous-api
2025-09-01 16:36:35,294 - INFO - �ɹ���¡�ֿ�: heterogeneous-api
2025-09-01 16:36:36,266 - INFO - ���� 1 ��Զ�̷�֧: ['master']
2025-09-01 16:36:37,156 - INFO - ��ʼ��¡�ֿ�: heterogeneous-ui
2025-09-01 16:36:40,136 - INFO - �ɹ���¡�ֿ�: heterogeneous-ui
2025-09-01 16:36:41,102 - INFO - ���� 2 ��Զ�̷�֧: ['dev', 'master']
2025-09-01 16:36:42,222 - INFO - �ɹ������֧: dev
2025-09-01 16:36:43,163 - INFO - ��ʼ��¡�ֿ�: yunxin-callback-api
2025-09-01 16:36:46,181 - INFO - �ɹ���¡�ֿ�: yunxin-callback-api
2025-09-01 16:36:47,243 - INFO - ���� 5 ��Զ�̷�֧: ['6yue', 'develop', 'master', 'realse', 'realse-yunxin']
2025-09-01 16:36:48,095 - INFO - �ɹ������֧: 6yue
2025-09-01 16:36:49,144 - INFO - �ɹ������֧: develop
2025-09-01 16:36:50,196 - INFO - �ɹ������֧: realse
2025-09-01 16:36:51,158 - INFO - �ɹ������֧: realse-yunxin
2025-09-01 16:36:52,265 - INFO - ��ʼ��¡�ֿ�: bid-invoice-client
2025-09-01 16:36:55,587 - INFO - �ɹ���¡�ֿ�: bid-invoice-client
2025-09-01 16:36:56,595 - INFO - ���� 3 ��Զ�̷�֧: ['E-013bzj', 'feature-E021', 'master']
2025-09-01 16:36:57,565 - INFO - �ɹ������֧: E-013bzj
2025-09-01 16:36:58,575 - INFO - �ɹ������֧: feature-E021
2025-09-01 16:36:59,534 - INFO - ��ʼ��¡�ֿ�: bid-encryption-tool
2025-09-01 16:37:02,831 - INFO - �ɹ���¡�ֿ�: bid-encryption-tool
2025-09-01 16:37:03,881 - INFO - ���� 2 ��Զ�̷�֧: ['master', 'p4']
2025-09-01 16:37:04,925 - INFO - �ɹ������֧: p4
2025-09-01 16:37:06,148 - INFO - ��ʼ��¡�ֿ�: bid-doc-maintenance
2025-09-01 16:37:09,301 - INFO - �ɹ���¡�ֿ�: bid-doc-maintenance
2025-09-01 16:37:10,299 - INFO - ���� 1 ��Զ�̷�֧: ['master']
2025-09-01 16:37:11,339 - INFO - ��ʼ��¡�ֿ�: bid-invoice-api
2025-09-01 16:37:14,559 - INFO - �ɹ���¡�ֿ�: bid-invoice-api
2025-09-01 16:37:15,410 - INFO - ���� 8 ��Զ�̷�֧: ['E-013bzj', 'E021-p1', 'P-009', 'P-009-lmk', 'feature-branch', 'master', 'realse', 'realse-02-13']
2025-09-01 16:37:16,643 - INFO - �ɹ������֧: E-013bzj
2025-09-01 16:37:17,587 - INFO - �ɹ������֧: E021-p1
2025-09-01 16:37:18,517 - INFO - �ɹ������֧: P-009
2025-09-01 16:37:19,534 - INFO - �ɹ������֧: P-009-lmk
2025-09-01 16:37:20,542 - INFO - �ɹ������֧: feature-branch
2025-09-01 16:37:21,586 - INFO - �ɹ������֧: realse
2025-09-01 16:37:22,827 - INFO - �ɹ������֧: realse-02-13
2025-09-01 16:37:23,760 - INFO - ��ʼ��¡�ֿ�: HZW-TA
2025-09-01 16:37:27,459 - INFO - �ɹ���¡�ֿ�: HZW-TA
2025-09-01 16:37:28,592 - INFO - ���� 1 ��Զ�̷�֧: ['master']
2025-09-01 16:37:29,485 - INFO - ��ʼ��¡�ֿ�: bid-fee-deposit-client
2025-09-01 16:37:33,073 - INFO - �ɹ���¡�ֿ�: bid-fee-deposit-client
2025-09-01 16:37:34,578 - INFO - ���� 2 ��Զ�̷�֧: ['feature-branch', 'master']
2025-09-01 16:37:35,846 - INFO - �ɹ������֧: feature-branch
2025-09-01 16:37:36,992 - INFO - ��ʼ��¡�ֿ�: bid-fee-expert-client
2025-09-01 16:37:40,486 - INFO - �ɹ���¡�ֿ�: bid-fee-expert-client
2025-09-01 16:37:41,268 - INFO - ���� 3 ��Զ�̷�֧: ['021', '021-push-0219', 'master']
2025-09-01 16:37:42,395 - INFO - �ɹ������֧: 021
2025-09-01 16:37:43,551 - INFO - �ɹ������֧: 021-push-0219
2025-09-01 16:37:44,630 - INFO - ��ʼ��¡�ֿ�: bid-fee-deposit
2025-09-01 16:37:48,145 - INFO - �ɹ���¡�ֿ�: bid-fee-deposit
2025-09-01 16:37:49,152 - INFO - ���� 10 ��Զ�̷�֧: ['6yue', 'develop', 'feature-20241216', 'feature-branch', 'feature-refundStatus', 'master', 'realse', 'realse-fix', 'v2.14.4.3', 'v2.14.6.2']
2025-09-01 16:37:50,150 - INFO - �ɹ������֧: 6yue
2025-09-01 16:37:51,245 - INFO - �ɹ������֧: develop
2025-09-01 16:37:52,238 - INFO - �ɹ������֧: feature-20241216
2025-09-01 16:37:53,372 - INFO - �ɹ������֧: feature-branch
2025-09-01 16:37:54,440 - INFO - �ɹ������֧: feature-refundStatus
2025-09-01 16:37:55,287 - INFO - �ɹ������֧: realse
2025-09-01 16:37:56,280 - INFO - �ɹ������֧: realse-fix
2025-09-01 16:37:57,293 - INFO - �ɹ������֧: v2.14.4.3
2025-09-01 16:37:58,306 - INFO - �ɹ������֧: v2.14.6.2
2025-09-01 16:37:59,468 - INFO - ��ʼ��¡�ֿ�: bid-fee-expert
2025-09-01 16:38:05,030 - INFO - �ɹ���¡�ֿ�: bid-fee-expert
2025-09-01 16:38:06,006 - INFO - ���� 8 ��Զ�̷�֧: ['0822', '6yue', 'E-021-push-0219', 'dev_china', 'develop', 'master', 'realse', 'v2.14.6.2']
2025-09-01 16:38:07,494 - INFO - �ɹ������֧: 0822
2025-09-01 16:38:08,790 - INFO - �ɹ������֧: 6yue
2025-09-01 16:38:09,878 - INFO - �ɹ������֧: E-021-push-0219
2025-09-01 16:38:10,863 - INFO - �ɹ������֧: dev_china
2025-09-01 16:38:11,618 - INFO - �ɹ������֧: develop
2025-09-01 16:38:13,154 - INFO - �ɹ������֧: realse
2025-09-01 16:38:14,610 - INFO - �ɹ������֧: v2.14.6.2
2025-09-01 16:38:16,076 - INFO - ��ʼ��¡�ֿ�: bid-doc-tool-api
2025-09-01 16:38:21,355 - INFO - �ɹ���¡�ֿ�: bid-doc-tool-api
2025-09-01 16:38:22,424 - INFO - ���� 2 ��Զ�̷�֧: ['develop', 'master']
2025-09-01 16:38:24,176 - INFO - �ɹ������֧: develop
2025-09-01 16:38:25,623 - INFO - ��ʼ��¡�ֿ�: bid-document-tool
2025-09-01 16:38:30,260 - INFO - �ɹ���¡�ֿ�: bid-document-tool
2025-09-01 16:38:31,470 - INFO - ���� 3 ��Զ�̷�֧: ['master', 'p4', 'p4-yhy']
2025-09-01 16:38:32,685 - INFO - �ɹ������֧: p4
2025-09-01 16:38:34,048 - INFO - �ɹ������֧: p4-yhy
2025-09-01 16:38:35,137 - INFO - ��ʼ��¡�ֿ�: bid-office-online
2025-09-01 16:38:38,360 - INFO - �ɹ���¡�ֿ�: bid-office-online
2025-09-01 16:38:39,069 - INFO - ���� 3 ��Զ�̷�֧: ['1.0.0', 'develop', 'master']
2025-09-01 16:38:40,248 - INFO - �ɹ������֧: 1.0.0
2025-09-01 16:38:41,494 - INFO - �ɹ������֧: develop
2025-09-01 16:38:42,773 - INFO - ��ʼ��¡�ֿ�: bid-sign-file
2025-09-01 16:38:46,129 - INFO - �ɹ���¡�ֿ�: bid-sign-file
2025-09-01 16:38:47,248 - INFO - ���� 9 ��Զ�̷�֧: ['2.12.0.1', '6yue', 'develop', 'feature-20240725', 'master', 'realse', 'realse-02-13', 'realse-yunxin', 'v2.12.4']
2025-09-01 16:38:48,455 - INFO - �ɹ������֧: 2.12.0.1
2025-09-01 16:38:49,694 - INFO - �ɹ������֧: 6yue
2025-09-01 16:38:50,859 - INFO - �ɹ������֧: develop
2025-09-01 16:38:51,817 - INFO - �ɹ������֧: feature-20240725
2025-09-01 16:38:52,770 - INFO - �ɹ������֧: realse
2025-09-01 16:38:53,910 - INFO - �ɹ������֧: realse-02-13
2025-09-01 16:38:55,009 - INFO - �ɹ������֧: realse-yunxin
2025-09-01 16:38:55,944 - INFO - �ɹ������֧: v2.12.4
2025-09-01 16:38:56,958 - INFO - ��ʼ��¡�ֿ�: bid-spider-doc
2025-09-01 16:39:03,117 - INFO - �ɹ���¡�ֿ�: bid-spider-doc
2025-09-01 16:39:04,134 - INFO - ���� 2 ��Զ�̷�֧: ['develop', 'master']
2025-09-01 16:39:06,641 - INFO - �ɹ������֧: develop
2025-09-01 16:39:09,727 - INFO - ��ʼ��¡�ֿ�: bid-spider-client
2025-09-01 16:39:13,323 - INFO - �ɹ���¡�ֿ�: bid-spider-client
2025-09-01 16:39:14,274 - INFO - ���� 3 ��Զ�̷�֧: ['develop', 'master', 'v2.13.1.9']
2025-09-01 16:39:15,431 - INFO - �ɹ������֧: develop
2025-09-01 16:39:16,389 - INFO - �ɹ������֧: v2.13.1.9
2025-09-01 16:39:17,603 - INFO - ��ʼ��¡�ֿ�: push-tool-api
2025-09-01 16:39:27,923 - INFO - �ɹ���¡�ֿ�: push-tool-api
2025-09-01 16:39:28,982 - INFO - ���� 5 ��Զ�̷�֧: ['dev-tool-yhs', 'dev_tool', 'dev_tool_zhr', 'develop', 'master']
2025-09-01 16:39:32,743 - INFO - �ɹ������֧: dev-tool-yhs
2025-09-01 16:39:34,004 - INFO - �ɹ������֧: dev_tool
2025-09-01 16:39:35,096 - INFO - �ɹ������֧: dev_tool_zhr
2025-09-01 16:39:35,698 - INFO - �ɹ������֧: develop
2025-09-01 16:39:37,094 - INFO - ��ʼ��¡�ֿ�: push-tool-ui
2025-09-01 16:39:43,065 - INFO - �ɹ���¡�ֿ�: push-tool-ui
2025-09-01 16:39:44,092 - INFO - ���� 6 ��Զ�̷�֧: ['D-010', 'D-010-desktop', 'D-010-desktop-zhr', 'develop', 'master', 'rease1.0']
2025-09-01 16:39:45,450 - INFO - �ɹ������֧: D-010
2025-09-01 16:39:46,222 - INFO - �ɹ������֧: D-010-desktop
2025-09-01 16:39:46,799 - INFO - �ɹ������֧: D-010-desktop-zhr
2025-09-01 16:39:47,611 - INFO - �ɹ������֧: develop
2025-09-01 16:39:48,868 - INFO - �ɹ������֧: rease1.0
2025-09-01 16:39:49,504 - INFO - ��ʼ��¡�ֿ�: bid-textToSpeech
2025-09-01 16:39:52,652 - INFO - �ɹ���¡�ֿ�: bid-textToSpeech
2025-09-01 16:39:53,652 - INFO - ���� 9 ��Զ�̷�֧: ['2.10.0', '2.12.5', '6yue', 'E-007p2', 'develop', 'master', 'realse', 'v2.12.4', 'v2.12.4-bug']
2025-09-01 16:39:54,636 - INFO - �ɹ������֧: 2.10.0
2025-09-01 16:39:55,717 - INFO - �ɹ������֧: 2.12.5
2025-09-01 16:39:56,784 - INFO - �ɹ������֧: 6yue
2025-09-01 16:39:57,632 - INFO - �ɹ������֧: E-007p2
2025-09-01 16:39:58,721 - INFO - �ɹ������֧: develop
2025-09-01 16:39:59,782 - INFO - �ɹ������֧: realse
2025-09-01 16:40:00,918 - INFO - �ɹ������֧: v2.12.4
2025-09-01 16:40:02,325 - INFO - �ɹ������֧: v2.12.4-bug
2025-09-01 16:40:03,417 - INFO - ��ʼ��¡�ֿ�: bid-spider-api
2025-09-01 16:40:07,080 - INFO - �ɹ���¡�ֿ�: bid-spider-api
2025-09-01 16:40:08,140 - INFO - ���� 6 ��Զ�̷�֧: ['2.12.0', 'develop', 'develop-sharding', 'master', 'url_change', 'v2.13.1.9']
2025-09-01 16:40:09,248 - INFO - �ɹ������֧: 2.12.0
2025-09-01 16:40:10,347 - INFO - �ɹ������֧: develop
2025-09-01 16:40:11,239 - INFO - �ɹ������֧: develop-sharding
2025-09-01 16:40:12,299 - INFO - �ɹ������֧: url_change
2025-09-01 16:40:13,363 - INFO - �ɹ������֧: v2.13.1.9
2025-09-01 16:40:14,603 - INFO - ��ʼ��¡�ֿ�: spider-main
2025-09-01 16:40:18,332 - INFO - �ɹ���¡�ֿ�: spider-main
2025-09-01 16:40:19,352 - INFO - ���� 2 ��Զ�̷�֧: ['develop', 'master']
2025-09-01 16:40:20,377 - INFO - �ɹ������֧: develop
2025-09-01 16:40:21,555 - INFO - ��ʼ��¡�ֿ�: ctbpsp_crawler
2025-09-01 16:40:24,686 - INFO - �ɹ���¡�ֿ�: ctbpsp_crawler
2025-09-01 16:40:25,728 - INFO - ���� 1 ��Զ�̷�֧: ['master']
2025-09-01 16:40:26,734 - INFO - ��ʼ��¡�ֿ�: portal-index-ui
2025-09-01 16:40:30,017 - INFO - �ɹ���¡�ֿ�: portal-index-ui
2025-09-01 16:40:31,128 - INFO - ���� 2 ��Զ�̷�֧: ['dev', 'master']
2025-09-01 16:40:32,334 - INFO - �ɹ������֧: dev
2025-09-01 16:40:33,528 - INFO - ��ʼ��¡�ֿ�: portal-index-ui
2025-09-01 16:40:33,528 - WARNING - Ŀ¼�Ѵ��ڣ�����: cloned_repos\portal-index-ui
2025-09-01 16:40:33,528 - INFO - ��ʼ��¡�ֿ�: portal-ui
2025-09-01 16:40:38,765 - INFO - �ɹ���¡�ֿ�: portal-ui
2025-09-01 16:40:39,752 - INFO - ���� 16 ��Զ�̷�֧: ['2024_11_29', '2025_01_09', '2025_01_13', '2025_02_20', 'E-001', 'E-001_yhy', 'E-005', 'cgy', 'dev_V1.1.1', 'develop', 'develop-mulu', 'master', 'pageOptimization_zhr', 'realse', 'realse-1.0.1', 'v2024_08_12']
2025-09-01 16:40:40,944 - INFO - �ɹ������֧: 2024_11_29
2025-09-01 16:40:42,027 - INFO - �ɹ������֧: 2025_01_09
2025-09-01 16:40:42,955 - INFO - �ɹ������֧: 2025_01_13
2025-09-01 16:40:44,030 - INFO - �ɹ������֧: 2025_02_20
2025-09-01 16:40:45,077 - INFO - �ɹ������֧: E-001
2025-09-01 16:40:46,108 - INFO - �ɹ������֧: E-001_yhy
2025-09-01 16:40:47,164 - INFO - �ɹ������֧: E-005
2025-09-01 16:40:48,058 - INFO - �ɹ������֧: cgy
2025-09-01 16:40:49,249 - INFO - �ɹ������֧: dev_V1.1.1
2025-09-01 16:40:50,372 - INFO - �ɹ������֧: develop
2025-09-01 16:40:51,452 - INFO - �ɹ������֧: develop-mulu
2025-09-01 16:40:52,667 - INFO - �ɹ������֧: pageOptimization_zhr
2025-09-01 16:40:53,887 - INFO - �ɹ������֧: realse
2025-09-01 16:40:55,162 - INFO - �ɹ������֧: realse-1.0.1
2025-09-01 16:40:56,407 - INFO - �ɹ������֧: v2024_08_12
2025-09-01 16:40:57,620 - INFO - ��ʼ��¡�ֿ�: portal-cloud
2025-09-01 16:41:04,643 - INFO - �ɹ���¡�ֿ�: portal-cloud
2025-09-01 16:41:05,684 - INFO - ���� 23 ��Զ�̷�֧: ['0116', '1.1.0.1', '1.1.1-fix-safe', '1.1.1-fix-safe-sms', '1.1.1-fix-sms', 'E005', 'V1.1.1_zhr', 'cherry-pick-cc3fe1d0fd29da97cb96f3deafa9b6f317c9cac7', 'dev-xt', 'develop', 'master', 'pageOptimization_zhr', 'realse', 'realse-0109', 'realse-1022', 'realse-1105', 'realse-1126', 'realse-1204', 'realse-anquan', 'realse-fix-noticeurl', 'v1.1.1', 'v1.1.1-fanqh', 'v1.1.1-fix']
2025-09-01 16:41:07,102 - INFO - �ɹ������֧: 0116
2025-09-01 16:41:08,275 - INFO - �ɹ������֧: 1.1.0.1
2025-09-01 16:41:09,557 - INFO - �ɹ������֧: 1.1.1-fix-safe
2025-09-01 16:41:10,898 - INFO - �ɹ������֧: 1.1.1-fix-safe-sms
2025-09-01 16:41:11,896 - INFO - �ɹ������֧: 1.1.1-fix-sms
2025-09-01 16:41:12,769 - INFO - �ɹ������֧: E005
2025-09-01 16:41:13,737 - INFO - �ɹ������֧: V1.1.1_zhr
2025-09-01 16:41:14,744 - INFO - �ɹ������֧: cherry-pick-cc3fe1d0fd29da97cb96f3deafa9b6f317c9cac7
2025-09-01 16:41:16,501 - INFO - �ɹ������֧: dev-xt
2025-09-01 16:41:18,945 - INFO - �ɹ������֧: develop
2025-09-01 16:41:19,573 - INFO - �ɹ������֧: pageOptimization_zhr
2025-09-01 16:41:20,352 - INFO - �ɹ������֧: realse
2025-09-01 16:41:21,324 - INFO - �ɹ������֧: realse-0109
2025-09-01 16:41:21,854 - INFO - �ɹ������֧: realse-1022
2025-09-01 16:41:22,403 - INFO - �ɹ������֧: realse-1105
2025-09-01 16:41:22,901 - INFO - �ɹ������֧: realse-1126
2025-09-01 16:41:23,597 - INFO - �ɹ������֧: realse-1204
2025-09-01 16:41:24,598 - INFO - �ɹ������֧: realse-anquan
2025-09-01 16:41:25,580 - INFO - �ɹ������֧: realse-fix-noticeurl
2025-09-01 16:41:26,553 - INFO - �ɹ������֧: v1.1.1
2025-09-01 16:41:27,438 - INFO - �ɹ������֧: v1.1.1-fanqh
2025-09-01 16:41:28,429 - INFO - �ɹ������֧: v1.1.1-fix
2025-09-01 16:41:29,358 - INFO - ��ʼ��¡�ֿ�: bid-clear-tool-client
2025-09-01 16:41:32,431 - INFO - �ɹ���¡�ֿ�: bid-clear-tool-client
2025-09-01 16:41:33,472 - INFO - ���� 2 ��Զ�̷�֧: ['develop', 'master']
2025-09-01 16:41:34,664 - INFO - �ɹ������֧: develop
2025-09-01 16:41:35,746 - INFO - ��ʼ��¡�ֿ�: bid-clear-tool
2025-09-01 16:41:39,522 - INFO - �ɹ���¡�ֿ�: bid-clear-tool
2025-09-01 16:41:40,764 - INFO - ���� 5 ��Զ�̷�֧: ['6yue', 'develop', 'master', 'realse', 'realse-oos']
2025-09-01 16:41:41,642 - INFO - �ɹ������֧: 6yue
2025-09-01 16:41:42,514 - INFO - �ɹ������֧: develop
2025-09-01 16:41:43,530 - INFO - �ɹ������֧: realse
2025-09-01 16:41:44,509 - INFO - �ɹ������֧: realse-oos
2025-09-01 16:41:45,528 - INFO - ��ʼ��¡�ֿ�: publishTool
2025-09-01 16:41:48,726 - INFO - �ɹ���¡�ֿ�: publishTool
2025-09-01 16:41:49,781 - INFO - ���� 1 ��Զ�̷�֧: ['master']
2025-09-01 16:41:51,077 - INFO - ��ʼ��¡�ֿ�: bid-common-ratelimiter
2025-09-01 16:41:54,721 - INFO - �ɹ���¡�ֿ�: bid-common-ratelimiter
2025-09-01 16:41:55,750 - INFO - ���� 2 ��Զ�̷�֧: ['develop', 'master']
2025-09-01 16:41:56,609 - INFO - �ɹ������֧: develop
2025-09-01 16:41:57,715 - INFO - ��ʼ��¡�ֿ�: devops
2025-09-01 16:42:00,746 - INFO - �ɹ���¡�ֿ�: devops
2025-09-01 16:42:01,793 - INFO - ���� 1 ��Զ�̷�֧: ['main']
2025-09-01 16:42:02,838 - INFO - ��ʼ��¡�ֿ�: bid-omr-api
2025-09-01 16:42:06,211 - INFO - �ɹ���¡�ֿ�: bid-omr-api
2025-09-01 16:42:07,188 - INFO - ���� 23 ��Զ�̷�֧: ['2.12.03', '2.12.5', '2.15.1.1', '6yue', 'E007P2', 'P004', 'P011', 'develop', 'master', 'realse', 'realse-1219', 'realse-conference-code', 'realse-cx20250407', 'realse-enc', 'realse-encryption', 'realse-oos', 'realse-preview', 'realse-security', 'realse-yunxin', 'v2.12.4', 'v2.13.2', 'v2.14.1-dev-1226', 'v2.14.6.2']
2025-09-01 16:42:08,475 - INFO - �ɹ������֧: 2.12.03
2025-09-01 16:42:09,760 - INFO - �ɹ������֧: 2.12.5
2025-09-01 16:42:10,972 - INFO - �ɹ������֧: 2.15.1.1
2025-09-01 16:42:12,203 - INFO - �ɹ������֧: 6yue
2025-09-01 16:42:13,148 - INFO - �ɹ������֧: E007P2
2025-09-01 16:42:13,914 - INFO - �ɹ������֧: P004
2025-09-01 16:42:14,939 - INFO - �ɹ������֧: P011
2025-09-01 16:42:15,878 - INFO - �ɹ������֧: develop
2025-09-01 16:42:16,839 - INFO - �ɹ������֧: realse
2025-09-01 16:42:17,891 - INFO - �ɹ������֧: realse-1219
2025-09-01 16:42:18,812 - INFO - �ɹ������֧: realse-conference-code
2025-09-01 16:42:19,837 - INFO - �ɹ������֧: realse-cx20250407
2025-09-01 16:42:20,580 - INFO - �ɹ������֧: realse-enc
2025-09-01 16:42:21,788 - INFO - �ɹ������֧: realse-encryption
2025-09-01 16:42:23,033 - INFO - �ɹ������֧: realse-oos
2025-09-01 16:42:24,161 - INFO - �ɹ������֧: realse-preview
2025-09-01 16:42:25,425 - INFO - �ɹ������֧: realse-security
2025-09-01 16:42:26,778 - INFO - �ɹ������֧: realse-yunxin
2025-09-01 16:42:28,135 - INFO - �ɹ������֧: v2.12.4
2025-09-01 16:42:29,089 - INFO - �ɹ������֧: v2.13.2
2025-09-01 16:42:29,957 - INFO - �ɹ������֧: v2.14.1-dev-1226
2025-09-01 16:42:31,015 - INFO - �ɹ������֧: v2.14.6.2
2025-09-01 16:42:32,035 - INFO - ��ʼ��¡�ֿ�: bid-ui-omr
2025-09-01 16:42:35,869 - INFO - �ɹ���¡�ֿ�: bid-ui-omr
2025-09-01 16:42:36,861 - INFO - ���� 53 ��Զ�̷�֧: ['0819', '0925-sdk', '109fix', '2.10.0', '2.12.0', '2.12.0.1', '2.12.0.3', '2.12.2', '2.12.3.1', '2.12.4-fixclound', '2.15.1.1', '2024-11-12', '2024_12_19_V2.14.1', '2024_12_26_zhr', '2025-2-13-V2.14.3', '2025.1.13', '2025_01_09', '6yue', 'D020-326', 'D020-41', 'D020-tourist', 'E-007', 'E-007_v2.0', 'E-011', 'NERTC', 'P-004', 'V2.14.5.1', 'V2.14.6.2', 'change_icon', 'conference_fixed', 'crSDK', 'd020_0325', 'develop', 'djkpb66', 'find_losing_code', 'fix_word', 'getMeetingConfig', 'hotfix', 'hotfixed', 'master', 'nertc_zhr', 'qingyuedu', 'realse', 'realse-cx20250407', 'room20250702', 'v2.12.3', 'v2.12.4', 'v2.12.4.2-sdk', 'v2.12.5', 'v2.12.5-lineoff', 'v2.14.1-dev-1226', 'v2.5.0', 'v2.8.1']
2025-09-01 16:42:37,934 - INFO - �ɹ������֧: 0819
2025-09-01 16:42:39,171 - INFO - �ɹ������֧: 0925-sdk
2025-09-01 16:42:40,100 - INFO - �ɹ������֧: 109fix
2025-09-01 16:42:41,241 - INFO - �ɹ������֧: 2.10.0
2025-09-01 16:42:42,442 - INFO - �ɹ������֧: 2.12.0
2025-09-01 16:42:43,909 - INFO - �ɹ������֧: 2.12.0.1
2025-09-01 16:42:45,026 - INFO - �ɹ������֧: 2.12.0.3
2025-09-01 16:42:46,134 - INFO - �ɹ������֧: 2.12.2
2025-09-01 16:42:47,323 - INFO - �ɹ������֧: 2.12.3.1
2025-09-01 16:42:48,520 - INFO - �ɹ������֧: 2.12.4-fixclound
2025-09-01 16:42:49,585 - INFO - �ɹ������֧: 2.15.1.1
2025-09-01 16:42:50,766 - INFO - �ɹ������֧: 2024-11-12
2025-09-01 16:42:51,865 - INFO - �ɹ������֧: 2024_12_19_V2.14.1
2025-09-01 16:42:52,777 - INFO - �ɹ������֧: 2024_12_26_zhr
2025-09-01 16:42:53,782 - INFO - �ɹ������֧: 2025-2-13-V2.14.3
2025-09-01 16:42:54,810 - INFO - �ɹ������֧: 2025.1.13
2025-09-01 16:42:55,847 - INFO - �ɹ������֧: 2025_01_09
2025-09-01 16:42:56,764 - INFO - �ɹ������֧: 6yue
2025-09-01 16:42:58,077 - INFO - �ɹ������֧: D020-326
2025-09-01 16:42:59,226 - INFO - �ɹ������֧: D020-41
2025-09-01 16:43:00,542 - INFO - �ɹ������֧: D020-tourist
2025-09-01 16:43:01,927 - INFO - �ɹ������֧: E-007
2025-09-01 16:43:03,363 - INFO - �ɹ������֧: E-007_v2.0
2025-09-01 16:43:04,710 - INFO - �ɹ������֧: E-011
2025-09-01 16:43:06,149 - INFO - �ɹ������֧: NERTC
2025-09-01 16:43:07,324 - INFO - �ɹ������֧: P-004
2025-09-01 16:43:08,551 - INFO - �ɹ������֧: V2.14.5.1
2025-09-01 16:43:09,464 - INFO - �ɹ������֧: V2.14.6.2
2025-09-01 16:43:10,555 - INFO - �ɹ������֧: change_icon
2025-09-01 16:43:11,524 - INFO - �ɹ������֧: conference_fixed
2025-09-01 16:43:12,509 - INFO - �ɹ������֧: crSDK
2025-09-01 16:43:13,581 - INFO - �ɹ������֧: d020_0325
2025-09-01 16:43:14,804 - INFO - �ɹ������֧: develop
2025-09-01 16:43:16,016 - INFO - �ɹ������֧: djkpb66
2025-09-01 16:43:17,225 - INFO - �ɹ������֧: find_losing_code
2025-09-01 16:43:18,547 - INFO - �ɹ������֧: fix_word
2025-09-01 16:43:19,749 - INFO - �ɹ������֧: getMeetingConfig
2025-09-01 16:43:20,959 - INFO - �ɹ������֧: hotfix
2025-09-01 16:43:21,991 - INFO - �ɹ������֧: hotfixed
2025-09-01 16:43:23,186 - INFO - �ɹ������֧: nertc_zhr
2025-09-01 16:43:24,182 - INFO - �ɹ������֧: qingyuedu
2025-09-01 16:43:25,201 - INFO - �ɹ������֧: realse
2025-09-01 16:43:26,259 - INFO - �ɹ������֧: realse-cx20250407
2025-09-01 16:43:27,373 - INFO - �ɹ������֧: room20250702
2025-09-01 16:43:28,538 - INFO - �ɹ������֧: v2.12.3
2025-09-01 16:43:29,512 - INFO - �ɹ������֧: v2.12.4
2025-09-01 16:43:30,448 - INFO - �ɹ������֧: v2.12.4.2-sdk
2025-09-01 16:43:31,314 - INFO - �ɹ������֧: v2.12.5
2025-09-01 16:43:32,353 - INFO - �ɹ������֧: v2.12.5-lineoff
2025-09-01 16:43:33,536 - INFO - �ɹ������֧: v2.14.1-dev-1226
2025-09-01 16:43:34,556 - INFO - �ɹ������֧: v2.5.0
2025-09-01 16:43:35,649 - INFO - �ɹ������֧: v2.8.1
2025-09-01 16:43:36,737 - INFO - ��ʼ��¡�ֿ�: datascrenn2.0
2025-09-01 16:43:40,646 - INFO - �ɹ���¡�ֿ�: datascrenn2.0
2025-09-01 16:43:41,951 - INFO - ���� 1 ��Զ�̷�֧: ['main']
2025-09-01 16:43:42,675 - INFO - ��ʼ��¡�ֿ�: bid-sign-expert
2025-09-01 16:43:47,201 - INFO - �ɹ���¡�ֿ�: bid-sign-expert
2025-09-01 16:43:48,156 - INFO - ���� 26 ��Զ�̷�֧: ['2.10.0', '2.12.0.1', '2.12.5', '2.12.5_log', '6yue', 'D-020', 'E007p2', 'P-004', 'dev_2.8.1', 'develop', 'down-fix-0605', 'feature-D015', 'feature-D015-20240621', 'feature-E006', 'feature-E007', 'master', 'realse', 'realse-open', 'realse-yunxin', 'v2.12.4', 'v2.12.4-fix', 'v2.13.1.5', 'v2.15.2.3', 'v2.15.2.3-formal', 'v2.3.6_0118', 'v2.9.0']
2025-09-01 16:43:49,032 - INFO - �ɹ������֧: 2.10.0
2025-09-01 16:43:49,941 - INFO - �ɹ������֧: 2.12.0.1
2025-09-01 16:43:50,653 - INFO - �ɹ������֧: 2.12.5
2025-09-01 16:43:51,701 - INFO - �ɹ������֧: 2.12.5_log
2025-09-01 16:43:52,684 - INFO - �ɹ������֧: 6yue
2025-09-01 16:43:53,759 - INFO - �ɹ������֧: D-020
2025-09-01 16:43:54,911 - INFO - �ɹ������֧: E007p2
2025-09-01 16:43:55,930 - INFO - �ɹ������֧: P-004
2025-09-01 16:43:57,095 - INFO - �ɹ������֧: dev_2.8.1
2025-09-01 16:43:57,997 - INFO - �ɹ������֧: develop
2025-09-01 16:43:58,970 - INFO - �ɹ������֧: down-fix-0605
2025-09-01 16:44:00,039 - INFO - �ɹ������֧: feature-D015
2025-09-01 16:44:00,911 - INFO - �ɹ������֧: feature-D015-20240621
2025-09-01 16:44:02,053 - INFO - �ɹ������֧: feature-E006
2025-09-01 16:44:03,166 - INFO - �ɹ������֧: feature-E007
2025-09-01 16:44:04,386 - INFO - �ɹ������֧: realse
2025-09-01 16:44:05,584 - INFO - �ɹ������֧: realse-open
2025-09-01 16:44:06,799 - INFO - �ɹ������֧: realse-yunxin
2025-09-01 16:44:07,828 - INFO - �ɹ������֧: v2.12.4
2025-09-01 16:44:08,970 - INFO - �ɹ������֧: v2.12.4-fix
2025-09-01 16:44:09,889 - INFO - �ɹ������֧: v2.13.1.5
2025-09-01 16:44:10,924 - INFO - �ɹ������֧: v2.15.2.3
2025-09-01 16:44:11,982 - INFO - �ɹ������֧: v2.15.2.3-formal
2025-09-01 16:44:12,923 - INFO - �ɹ������֧: v2.3.6_0118
2025-09-01 16:44:13,877 - INFO - �ɹ������֧: v2.9.0
2025-09-01 16:44:14,841 - INFO - ��ʼ��¡�ֿ�: bid-oa-msgpush
2025-09-01 16:44:18,568 - INFO - �ɹ���¡�ֿ�: bid-oa-msgpush
2025-09-01 16:44:19,589 - INFO - ���� 6 ��Զ�̷�֧: ['dev-0702', 'develop', 'master', 'realse', 'realse-fix-20240703', 'realse-fix-20241012']
2025-09-01 16:44:20,929 - INFO - �ɹ������֧: dev-0702
2025-09-01 16:44:22,036 - INFO - �ɹ������֧: develop
2025-09-01 16:44:22,698 - INFO - �ɹ������֧: realse
2025-09-01 16:44:23,633 - INFO - �ɹ������֧: realse-fix-20240703
2025-09-01 16:44:24,587 - INFO - �ɹ������֧: realse-fix-20241012
2025-09-01 16:44:25,601 - INFO - ��ʼ��¡�ֿ�: bid-oa
2025-09-01 16:44:29,914 - INFO - �ɹ���¡�ֿ�: bid-oa
2025-09-01 16:44:31,054 - INFO - ���� 26 ��Զ�̷�֧: ['6yue', 'D-016', 'D016_V1.1.0', 'E-002_v1.1.1', 'E-011', 'E002', 'E004', 'E004_yhd', 'P-006+E-004', 'P009-lmk', 'master', 'qingyuedu', 'realse', 'release-v1.0.0', 'release-v1.1.0', 'release-v1.2.0', 'release-v1.2.3', 'release-v1.3.0', 'release-v1.3.1', 'release-v1.3.2', 'release-v1.3.3', 'release-v1.3.5', 'release-v1.3.6', 'release-v1.3.7', 'release-v1.3.8', 'v_1.1.0_hotfix']
2025-09-01 16:44:32,407 - INFO - �ɹ������֧: 6yue
2025-09-01 16:44:33,772 - INFO - �ɹ������֧: D-016
2025-09-01 16:44:35,064 - INFO - �ɹ������֧: D016_V1.1.0
2025-09-01 16:44:36,234 - INFO - �ɹ������֧: E-002_v1.1.1
2025-09-01 16:44:37,393 - INFO - �ɹ������֧: E-011
2025-09-01 16:44:38,615 - INFO - �ɹ������֧: E002
2025-09-01 16:44:39,851 - INFO - �ɹ������֧: E004
2025-09-01 16:44:40,938 - INFO - �ɹ������֧: E004_yhd
2025-09-01 16:44:41,940 - INFO - �ɹ������֧: P-006+E-004
2025-09-01 16:44:42,996 - INFO - �ɹ������֧: P009-lmk
2025-09-01 16:44:44,173 - INFO - �ɹ������֧: qingyuedu
2025-09-01 16:44:45,115 - INFO - �ɹ������֧: realse
2025-09-01 16:44:46,273 - INFO - �ɹ������֧: release-v1.0.0
2025-09-01 16:44:47,109 - INFO - �ɹ������֧: release-v1.1.0
2025-09-01 16:44:48,256 - INFO - �ɹ������֧: release-v1.2.0
2025-09-01 16:44:49,420 - INFO - �ɹ������֧: release-v1.2.3
2025-09-01 16:44:50,457 - INFO - �ɹ������֧: release-v1.3.0
2025-09-01 16:44:51,583 - INFO - �ɹ������֧: release-v1.3.1
2025-09-01 16:44:52,822 - INFO - �ɹ������֧: release-v1.3.2
2025-09-01 16:44:54,030 - INFO - �ɹ������֧: release-v1.3.3
2025-09-01 16:44:55,401 - INFO - �ɹ������֧: release-v1.3.5
2025-09-01 16:44:56,406 - INFO - �ɹ������֧: release-v1.3.6
2025-09-01 16:44:57,610 - INFO - �ɹ������֧: release-v1.3.7
2025-09-01 16:44:58,813 - INFO - �ɹ������֧: release-v1.3.8
2025-09-01 16:44:59,899 - INFO - �ɹ������֧: v_1.1.0_hotfix
2025-09-01 16:45:01,047 - INFO - ��ʼ��¡�ֿ�: bid-expert-client
2025-09-01 16:45:04,375 - INFO - �ɹ���¡�ֿ�: bid-expert-client
2025-09-01 16:45:05,056 - INFO - ���� 18 ��Զ�̷�֧: ['023-sy', '0327', '082', '2.13.1', '2301-2', 'D020', 'E-16', 'E002', 'E023', 'P-004', 'P-011', 'P008', 'dev-lwf', 'develop', 'master', 'realse', 'realse-preview', 'yhs']
2025-09-01 16:45:06,177 - INFO - �ɹ������֧: 023-sy
2025-09-01 16:45:07,454 - INFO - �ɹ������֧: 0327
2025-09-01 16:45:08,742 - INFO - �ɹ������֧: 082
2025-09-01 16:45:09,919 - INFO - �ɹ������֧: 2.13.1
2025-09-01 16:45:11,265 - INFO - �ɹ������֧: 2301-2
2025-09-01 16:45:12,573 - INFO - �ɹ������֧: D020
2025-09-01 16:45:13,710 - INFO - �ɹ������֧: E-16
2025-09-01 16:45:14,348 - INFO - �ɹ������֧: E002
2025-09-01 16:45:14,970 - INFO - �ɹ������֧: E023
2025-09-01 16:45:15,590 - INFO - �ɹ������֧: P-004
2025-09-01 16:45:16,404 - INFO - �ɹ������֧: P-011
2025-09-01 16:45:17,783 - INFO - �ɹ������֧: P008
2025-09-01 16:45:18,613 - INFO - �ɹ������֧: dev-lwf
2025-09-01 16:45:19,900 - INFO - �ɹ������֧: develop
2025-09-01 16:45:20,930 - INFO - �ɹ������֧: realse
2025-09-01 16:45:21,913 - INFO - �ɹ������֧: realse-preview
2025-09-01 16:45:23,167 - INFO - �ɹ������֧: yhs
2025-09-01 16:45:24,403 - INFO - ��ʼ��¡�ֿ�: bid-cloud
2025-09-01 16:45:24,404 - WARNING - Ŀ¼�Ѵ��ڣ�����: cloned_repos\bid-cloud
2025-09-01 16:45:24,413 - INFO - ��ʼ��¡�ֿ�: expert-wechat-app
2025-09-01 16:45:27,770 - INFO - �ɹ���¡�ֿ�: expert-wechat-app
2025-09-01 16:45:28,505 - INFO - ���� 6 ��Զ�̷�֧: ['dev', 'dev-zhr', 'expertApp-yhd', 'expertApp-yhy', 'master', 'release1.0.0']
2025-09-01 16:45:29,848 - INFO - �ɹ������֧: dev
2025-09-01 16:45:30,970 - INFO - �ɹ������֧: dev-zhr
2025-09-01 16:45:32,230 - INFO - �ɹ������֧: expertApp-yhd
2025-09-01 16:45:33,667 - INFO - �ɹ������֧: expertApp-yhy
2025-09-01 16:45:34,927 - INFO - �ɹ������֧: release1.0.0
2025-09-01 16:45:36,178 - INFO - ��ʼ��¡�ֿ�: expert-api
2025-09-01 16:45:39,513 - INFO - �ɹ���¡�ֿ�: expert-api
2025-09-01 16:45:40,581 - INFO - ���� 1 ��Զ�̷�֧: ['master']
2025-09-01 16:45:41,753 - INFO - ��ʼ��¡�ֿ�: expert-ui
2025-09-01 16:45:48,461 - INFO - �ɹ���¡�ֿ�: expert-ui
2025-09-01 16:45:49,384 - INFO - ���� 26 ��Զ�̷�֧: ['0819', '2.14.4.2_03_20', '2.15.1.1', '2025_03_06Fixed', '2025_04_01bugFixed', '6yue', 'E-013', 'E016', 'develop', 'expertDatabase', 'expert_yhd', 'getMeetingConfig', 'master', 'qingyuedu', 'realse', 'realse-1027', 'realse-1101', 'realse-1103', 'realse-1109', 'threeStar-0816', 'v2.3.5_0111', 'v2.4.1_0125', 'v2.5.1', 'v2.6.1', 'v2.6.2', 'v2.9.0.4']
2025-09-01 16:45:50,586 - INFO - �ɹ������֧: 0819
2025-09-01 16:45:51,757 - INFO - �ɹ������֧: 2.14.4.2_03_20
2025-09-01 16:45:52,551 - INFO - �ɹ������֧: 2.15.1.1
2025-09-01 16:45:53,699 - INFO - �ɹ������֧: 2025_03_06Fixed
2025-09-01 16:45:54,889 - INFO - �ɹ������֧: 2025_04_01bugFixed
2025-09-01 16:45:56,234 - INFO - �ɹ������֧: 6yue
2025-09-01 16:45:57,461 - INFO - �ɹ������֧: E-013
2025-09-01 16:45:58,719 - INFO - �ɹ������֧: E016
2025-09-01 16:45:59,785 - INFO - �ɹ������֧: develop
2025-09-01 16:46:01,194 - INFO - �ɹ������֧: expertDatabase
2025-09-01 16:46:02,378 - INFO - �ɹ������֧: expert_yhd
2025-09-01 16:46:03,680 - INFO - �ɹ������֧: getMeetingConfig
2025-09-01 16:46:05,052 - INFO - �ɹ������֧: qingyuedu
2025-09-01 16:46:06,184 - INFO - �ɹ������֧: realse
2025-09-01 16:46:07,134 - INFO - �ɹ������֧: realse-1027
2025-09-01 16:46:07,997 - INFO - �ɹ������֧: realse-1101
2025-09-01 16:46:08,828 - INFO - �ɹ������֧: realse-1103
2025-09-01 16:46:10,041 - INFO - �ɹ������֧: realse-1109
2025-09-01 16:46:11,161 - INFO - �ɹ������֧: threeStar-0816
2025-09-01 16:46:12,080 - INFO - �ɹ������֧: v2.3.5_0111
2025-09-01 16:46:13,138 - INFO - �ɹ������֧: v2.4.1_0125
2025-09-01 16:46:14,183 - INFO - �ɹ������֧: v2.5.1
2025-09-01 16:46:15,062 - INFO - �ɹ������֧: v2.6.1
2025-09-01 16:46:16,228 - INFO - �ɹ������֧: v2.6.2
2025-09-01 16:46:17,388 - INFO - �ɹ������֧: v2.9.0.4
2025-09-01 16:46:18,296 - INFO - ��ʼ��¡�ֿ�: data-file-tran-api
2025-09-01 16:46:21,483 - INFO - �ɹ���¡�ֿ�: data-file-tran-api
2025-09-01 16:46:22,650 - INFO - ���� 4 ��Զ�̷�֧: ['develop', 'master', 'realse', 'xwp']
2025-09-01 16:46:23,911 - INFO - �ɹ������֧: develop
2025-09-01 16:46:25,280 - INFO - �ɹ������֧: realse
2025-09-01 16:46:26,553 - INFO - �ɹ������֧: xwp
2025-09-01 16:46:27,977 - INFO - ��ʼ��¡�ֿ�: bid-config
2025-09-01 16:46:31,087 - INFO - �ɹ���¡�ֿ�: bid-config
2025-09-01 16:46:31,758 - INFO - ���� 1 ��Զ�̷�֧: ['master']
2025-09-01 16:46:33,229 - INFO - ��ʼ��¡�ֿ�: source
2025-09-01 16:47:18,001 - INFO - �ɹ���¡�ֿ�: source
2025-09-01 16:47:18,933 - INFO - ���� 1 ��Զ�̷�֧: ['master']
2025-09-01 16:47:20,230 - INFO - ��ʼ��¡�ֿ�: bid-hub-client-sdk
2025-09-01 16:47:24,178 - INFO - �ɹ���¡�ֿ�: bid-hub-client-sdk
2025-09-01 16:47:25,327 - INFO - ���� 3 ��Զ�̷�֧: ['develop', 'master', 'njbk_1.0.2']
2025-09-01 16:47:26,259 - INFO - �ɹ������֧: develop
2025-09-01 16:47:27,513 - INFO - �ɹ������֧: njbk_1.0.2
2025-09-01 16:47:28,471 - INFO - ��ʼ��¡�ֿ�: source
2025-09-01 16:47:28,471 - WARNING - Ŀ¼�Ѵ��ڣ�����: cloned_repos\source
2025-09-01 16:47:28,472 - INFO - ��ʼ��¡�ֿ�: bid-hub-client
2025-09-01 16:47:32,332 - INFO - �ɹ���¡�ֿ�: bid-hub-client
2025-09-01 16:47:38,298 - INFO - ���� 7 ��Զ�̷�֧: ['develop', 'develop-cut', 'develop-jstcc', 'develop-jsyx', 'develop-jsyx-xt', 'master', 'saas-njbk']
2025-09-01 16:47:38,897 - INFO - �ɹ������֧: develop
2025-09-01 16:47:39,809 - INFO - �ɹ������֧: develop-cut
2025-09-01 16:47:41,433 - INFO - �ɹ������֧: develop-jstcc
2025-09-01 16:47:42,581 - INFO - �ɹ������֧: develop-jsyx
2025-09-01 16:47:43,633 - INFO - �ɹ������֧: develop-jsyx-xt
2025-09-01 16:47:44,830 - INFO - �ɹ������֧: saas-njbk
2025-09-01 16:47:45,996 - INFO - ��ʼ��¡�ֿ�: bid-hub-server-api-client
2025-09-01 16:47:48,861 - INFO - �ɹ���¡�ֿ�: bid-hub-server-api-client
2025-09-01 16:47:49,385 - INFO - ���� 2 ��Զ�̷�֧: ['develop', 'master']
2025-09-01 16:47:49,950 - INFO - �ɹ������֧: develop
2025-09-01 16:47:50,587 - INFO - ��ʼ��¡�ֿ�: bid-file
2025-09-01 16:47:52,557 - INFO - �ɹ���¡�ֿ�: bid-file
2025-09-01 16:47:53,576 - INFO - ���� 2 ��Զ�̷�֧: ['develop', 'master']
2025-09-01 16:47:54,642 - INFO - �ɹ������֧: develop
2025-09-01 16:47:55,615 - INFO - ��ʼ��¡�ֿ�: bid-file-decrypt
2025-09-01 16:47:58,709 - INFO - �ɹ���¡�ֿ�: bid-file-decrypt
2025-09-01 16:48:00,083 - INFO - ���� 2 ��Զ�̷�֧: ['develop', 'master']
2025-09-01 16:48:01,464 - INFO - �ɹ������֧: develop
2025-09-01 16:48:02,617 - INFO - ��ʼ��¡�ֿ�: bid-hub-server-ui
2025-09-01 16:48:05,776 - INFO - �ɹ���¡�ֿ�: bid-hub-server-ui
2025-09-01 16:48:06,679 - INFO - ���� 4 ��Զ�̷�֧: ['develop', 'develop-R020-SX', 'master', 'realse']
2025-09-01 16:48:07,729 - INFO - �ɹ������֧: develop
2025-09-01 16:48:08,526 - INFO - �ɹ������֧: develop-R020-SX
2025-09-01 16:48:09,468 - INFO - �ɹ������֧: realse
2025-09-01 16:48:10,444 - INFO - ��ʼ��¡�ֿ�: bid-hub-server-api
2025-09-01 16:48:14,335 - INFO - �ɹ���¡�ֿ�: bid-hub-server-api
2025-09-01 16:48:15,477 - INFO - ���� 8 ��Զ�̷�֧: ['develop', 'develop-R020-SX', 'develop-cut', 'develop-fix-fileext', 'master', 'realse', 'realse-fix', 'saas-njbk']
2025-09-01 16:48:16,775 - INFO - �ɹ������֧: develop
2025-09-01 16:48:17,764 - INFO - �ɹ������֧: develop-R020-SX
2025-09-01 16:48:18,858 - INFO - �ɹ������֧: develop-cut
2025-09-01 16:48:19,975 - INFO - �ɹ������֧: develop-fix-fileext
2025-09-01 16:48:21,036 - INFO - �ɹ������֧: realse
2025-09-01 16:48:21,984 - INFO - �ɹ������֧: realse-fix
2025-09-01 16:48:22,957 - INFO - �ɹ������֧: saas-njbk
2025-09-01 16:48:23,929 - INFO - ��ʼ��¡�ֿ�: bid-sign
2025-09-01 16:48:27,019 - INFO - �ɹ���¡�ֿ�: bid-sign
2025-09-01 16:48:27,926 - INFO - ���� 18 ��Զ�̷�֧: ['2.5.0-fix', '2.5.0-fix-test', '6yue', 'D-022', 'E-007p2', 'E-007p2-E016P4', 'E-007p2-E016P4-test', 'E016-P4', 'develop', 'develop-rabbitmq', 'feature-E016', 'master', 'realse', 'release-0617', 'v2.12.4', 'v2.12.4-ppe', 'v2.3.6_0118', 'v2.5.0']
2025-09-01 16:48:28,989 - INFO - �ɹ������֧: 2.5.0-fix
2025-09-01 16:48:30,101 - INFO - �ɹ������֧: 2.5.0-fix-test
2025-09-01 16:48:31,353 - INFO - �ɹ������֧: 6yue
2025-09-01 16:48:32,499 - INFO - �ɹ������֧: D-022
2025-09-01 16:48:33,561 - INFO - �ɹ������֧: E-007p2
2025-09-01 16:48:34,300 - INFO - �ɹ������֧: E-007p2-E016P4
2025-09-01 16:48:35,042 - INFO - �ɹ������֧: E-007p2-E016P4-test
2025-09-01 16:48:35,960 - INFO - �ɹ������֧: E016-P4
2025-09-01 16:48:37,061 - INFO - �ɹ������֧: develop
2025-09-01 16:48:37,845 - INFO - �ɹ������֧: develop-rabbitmq
2025-09-01 16:48:38,824 - INFO - �ɹ������֧: feature-E016
2025-09-01 16:48:39,765 - INFO - �ɹ������֧: realse
2025-09-01 16:48:40,722 - INFO - �ɹ������֧: release-0617
2025-09-01 16:48:41,822 - INFO - �ɹ������֧: v2.12.4
2025-09-01 16:48:42,814 - INFO - �ɹ������֧: v2.12.4-ppe
2025-09-01 16:48:43,448 - INFO - �ɹ������֧: v2.3.6_0118
2025-09-01 16:48:44,478 - INFO - �ɹ������֧: v2.5.0
2025-09-01 16:48:45,445 - INFO - ��ʼ��¡�ֿ�: bid-docs
2025-09-01 16:48:48,934 - INFO - �ɹ���¡�ֿ�: bid-docs
2025-09-01 16:48:50,218 - INFO - ���� 1 ��Զ�̷�֧: ['master']
2025-09-01 16:48:51,283 - INFO - ��ʼ��¡�ֿ�: bid-tenderee-ui
2025-09-01 16:48:56,401 - INFO - �ɹ���¡�ֿ�: bid-tenderee-ui
2025-09-01 16:48:57,205 - INFO - ���� 68 ��Զ�̷�֧: ['0819', '0925-sdk', '1.0.18-fixUserCode', '1.0.18-jinjifabu', '109fix', '2.10.0', '2.12.0', '2.12.0.1', '2.12.0.3', '2.12.2', '2.12.3.1', '2.12.4-fixclound', '2.15.1.1', '2024-11-12', '2024_12_19_V2.14.1', '2025-2-13-V2.14.3', '2025.1.13', '2025_01_09', '6yue', 'E-007', 'E-007_v2.0', 'E-011', 'E-021', 'E016', 'E016_P3', 'NERTC', 'NJBK', 'P-004', 'P-006', 'R012_yhd', 'V2.14.5.1', 'V2.14.6.2', 'change_icon', 'conference_fixed', 'crSDK', 'd020-0325', 'd020-tender', 'develop', 'djkpb66', 'find_losing_code', 'fix_word', 'getMeetingConfig', 'hotfix', 'hotfixed', 'master', 'qingyuedu', 'realse', 'realse-1.0.18', 'realse-1.0.18-0914', 'realse-1.0.18-back-6-21', 'room20250702', 'saas-tenderee', 'saas-tenderee-0.0.2', 'saas-tenderee-0.01', 'saasv2.1.0_tenderee_r012', 'saasv2.1.0_tenderee_r100', 'tender-saas-template', 'threeStar-0816', 'threeStar_yhd', 'v-2.8.1', 'v-2.9.0', 'v2.12.3', 'v2.12.4', 'v2.12.4.2-sdk', 'v2.12.5', 'v2.12.5-lineoff', 'v2.14.1-dev-1226', 'v2.5.0']
2025-09-01 16:48:58,545 - INFO - �ɹ������֧: 0819
2025-09-01 16:48:59,588 - INFO - �ɹ������֧: 0925-sdk
2025-09-01 16:49:00,651 - INFO - �ɹ������֧: 1.0.18-fixUserCode
2025-09-01 16:49:01,442 - INFO - �ɹ������֧: 1.0.18-jinjifabu
2025-09-01 16:49:02,567 - INFO - �ɹ������֧: 109fix
2025-09-01 16:49:03,674 - INFO - �ɹ������֧: 2.10.0
2025-09-01 16:49:04,418 - INFO - �ɹ������֧: 2.12.0
2025-09-01 16:49:05,490 - INFO - �ɹ������֧: 2.12.0.1
2025-09-01 16:49:06,449 - INFO - �ɹ������֧: 2.12.0.3
2025-09-01 16:49:07,396 - INFO - �ɹ������֧: 2.12.2
2025-09-01 16:49:08,137 - INFO - �ɹ������֧: 2.12.3.1
2025-09-01 16:49:09,201 - INFO - �ɹ������֧: 2.12.4-fixclound
2025-09-01 16:49:10,298 - INFO - �ɹ������֧: 2.15.1.1
2025-09-01 16:49:11,455 - INFO - �ɹ������֧: 2024-11-12
2025-09-01 16:49:12,755 - INFO - �ɹ������֧: 2024_12_19_V2.14.1
2025-09-01 16:49:13,812 - INFO - �ɹ������֧: 2025-2-13-V2.14.3
2025-09-01 16:49:14,519 - INFO - �ɹ������֧: 2025.1.13
2025-09-01 16:49:15,443 - INFO - �ɹ������֧: 2025_01_09
2025-09-01 16:49:16,356 - INFO - �ɹ������֧: 6yue
2025-09-01 16:49:17,417 - INFO - �ɹ������֧: E-007
2025-09-01 16:49:18,142 - INFO - �ɹ������֧: E-007_v2.0
2025-09-01 16:49:19,337 - INFO - �ɹ������֧: E-011
2025-09-01 16:49:20,386 - INFO - �ɹ������֧: E-021
2025-09-01 16:49:21,377 - INFO - �ɹ������֧: E016
2025-09-01 16:49:22,103 - INFO - �ɹ������֧: E016_P3
2025-09-01 16:49:23,315 - INFO - �ɹ������֧: NERTC
2025-09-01 16:49:24,463 - INFO - �ɹ������֧: NJBK
2025-09-01 16:49:25,775 - INFO - �ɹ������֧: P-004
2025-09-01 16:49:27,041 - INFO - �ɹ������֧: P-006
2025-09-01 16:49:28,157 - INFO - �ɹ������֧: R012_yhd
2025-09-01 16:49:28,993 - INFO - �ɹ������֧: V2.14.5.1
2025-09-01 16:49:29,990 - INFO - �ɹ������֧: V2.14.6.2
2025-09-01 16:49:30,616 - INFO - �ɹ������֧: change_icon
2025-09-01 16:49:31,242 - INFO - �ɹ������֧: conference_fixed
2025-09-01 16:49:31,870 - INFO - �ɹ������֧: crSDK
2025-09-01 16:49:32,560 - INFO - �ɹ������֧: d020-0325
2025-09-01 16:49:33,408 - INFO - �ɹ������֧: d020-tender
2025-09-01 16:49:34,197 - INFO - �ɹ������֧: develop
2025-09-01 16:49:35,209 - INFO - �ɹ������֧: djkpb66
2025-09-01 16:49:36,340 - INFO - �ɹ������֧: find_losing_code
2025-09-01 16:49:37,549 - INFO - �ɹ������֧: fix_word
2025-09-01 16:49:38,558 - INFO - �ɹ������֧: getMeetingConfig
2025-09-01 16:49:39,424 - INFO - �ɹ������֧: hotfix
2025-09-01 16:49:40,763 - INFO - �ɹ������֧: hotfixed
2025-09-01 16:49:41,817 - INFO - �ɹ������֧: qingyuedu
2025-09-01 16:49:42,954 - INFO - �ɹ������֧: realse
2025-09-01 16:49:44,091 - INFO - �ɹ������֧: realse-1.0.18
2025-09-01 16:49:44,730 - INFO - �ɹ������֧: realse-1.0.18-0914
2025-09-01 16:49:45,362 - INFO - �ɹ������֧: realse-1.0.18-back-6-21
2025-09-01 16:49:46,153 - INFO - �ɹ������֧: room20250702
2025-09-01 16:49:47,076 - INFO - �ɹ������֧: saas-tenderee
2025-09-01 16:49:47,834 - INFO - �ɹ������֧: saas-tenderee-0.0.2
2025-09-01 16:49:48,928 - INFO - �ɹ������֧: saas-tenderee-0.01
2025-09-01 16:49:49,634 - INFO - �ɹ������֧: saasv2.1.0_tenderee_r012
2025-09-01 16:49:50,379 - INFO - �ɹ������֧: saasv2.1.0_tenderee_r100
2025-09-01 16:49:51,575 - INFO - �ɹ������֧: tender-saas-template
2025-09-01 16:49:52,737 - INFO - �ɹ������֧: threeStar-0816
2025-09-01 16:49:53,457 - INFO - �ɹ������֧: threeStar_yhd
2025-09-01 16:49:54,044 - INFO - �ɹ������֧: v-2.8.1
2025-09-01 16:49:54,569 - INFO - �ɹ������֧: v-2.9.0
2025-09-01 16:49:55,617 - INFO - �ɹ������֧: v2.12.3
2025-09-01 16:49:56,551 - INFO - �ɹ������֧: v2.12.4
2025-09-01 16:49:57,459 - INFO - �ɹ������֧: v2.12.4.2-sdk
2025-09-01 16:49:58,526 - INFO - �ɹ������֧: v2.12.5
2025-09-01 16:49:59,532 - INFO - �ɹ������֧: v2.12.5-lineoff
2025-09-01 16:50:00,390 - INFO - �ɹ������֧: v2.14.1-dev-1226
2025-09-01 16:50:01,568 - INFO - �ɹ������֧: v2.5.0
2025-09-01 16:50:02,725 - INFO - ��ʼ��¡�ֿ�: cpms
2025-09-01 16:50:05,664 - INFO - �ɹ���¡�ֿ�: cpms
2025-09-01 16:50:06,169 - INFO - ���� 1 ��Զ�̷�֧: ['master']
2025-09-01 16:50:06,752 - INFO - ��ʼ��¡�ֿ�: bid-license
2025-09-01 16:50:09,709 - INFO - �ɹ���¡�ֿ�: bid-license
2025-09-01 16:50:10,722 - INFO - ���� 3 ��Զ�̷�֧: ['develop', 'develop-maven', 'master']
2025-09-01 16:50:11,492 - INFO - �ɹ������֧: develop
2025-09-01 16:50:12,634 - INFO - �ɹ������֧: develop-maven
2025-09-01 16:50:13,697 - INFO - ��ʼ��¡�ֿ�: bid-chain
2025-09-01 16:50:17,031 - INFO - �ɹ���¡�ֿ�: bid-chain
2025-09-01 16:50:17,856 - INFO - ���� 2 ��Զ�̷�֧: ['develop', 'master']
2025-09-01 16:50:19,042 - INFO - �ɹ������֧: develop
2025-09-01 16:50:20,006 - INFO - ��ʼ��¡�ֿ�: zjk-python-test
2025-09-01 16:50:22,957 - INFO - �ɹ���¡�ֿ�: zjk-python-test
2025-09-01 16:50:23,925 - INFO - ���� 1 ��Զ�̷�֧: ['master']
2025-09-01 16:50:24,697 - INFO - ��ʼ��¡�ֿ�: JSTCC
2025-09-01 16:50:27,987 - INFO - �ɹ���¡�ֿ�: JSTCC
2025-09-01 16:50:29,004 - INFO - ���� 3 ��Զ�̷�֧: ['dev', 'duanyuxiang', 'master']
2025-09-01 16:50:30,057 - INFO - �ɹ������֧: dev
2025-09-01 16:50:31,066 - INFO - �ɹ������֧: duanyuxiang
2025-09-01 16:50:32,200 - INFO - ��ʼ��¡�ֿ�: bid-amoeba
2025-09-01 16:50:35,052 - INFO - �ɹ���¡�ֿ�: bid-amoeba
2025-09-01 16:50:35,946 - INFO - ���� 34 ��Զ�̷�֧: ['0127-analyse', '0221-analyse', '0401', '0712-empty', '1016', '2.11.0fix-bug', '2.12.3', '2.15.1.1-bug', '2023-2024-bug', '6yue', 'E-013-lmk', 'E009', 'P-009', 'delete', 'develop', 'feature-D017add', 'feature-E021', 'feature-E021-12-10', 'feature-E023', 'master', 'ppe', 'realse', 'realse-0109', 'realse-0113', 'realse-0116', 'realse-0822', 'realse-E0131-1', 'realse-E0131-init', 'realse-cx20250819', 'realse-yh', 'v2.12.4', 'v2.12.5', 'v2.13.0', 'v2.13.3.2']
2025-09-01 16:50:36,988 - INFO - �ɹ������֧: 0127-analyse
2025-09-01 16:50:37,846 - INFO - �ɹ������֧: 0221-analyse
2025-09-01 16:50:38,607 - INFO - �ɹ������֧: 0401
2025-09-01 16:50:39,810 - INFO - �ɹ������֧: 0712-empty
2025-09-01 16:50:40,736 - INFO - �ɹ������֧: 1016
2025-09-01 16:50:41,499 - INFO - �ɹ������֧: 2.11.0fix-bug
2025-09-01 16:50:42,485 - INFO - �ɹ������֧: 2.12.3
2025-09-01 16:50:43,475 - INFO - �ɹ������֧: 2.15.1.1-bug
2025-09-01 16:50:44,701 - INFO - �ɹ������֧: 2023-2024-bug
2025-09-01 16:50:45,523 - INFO - �ɹ������֧: 6yue
2025-09-01 16:50:46,685 - INFO - �ɹ������֧: E-013-lmk
2025-09-01 16:50:47,712 - INFO - �ɹ������֧: E009
2025-09-01 16:50:48,777 - INFO - �ɹ������֧: P-009
2025-09-01 16:50:49,920 - INFO - �ɹ������֧: delete
2025-09-01 16:50:51,068 - INFO - �ɹ������֧: develop
2025-09-01 16:50:52,421 - INFO - �ɹ������֧: feature-D017add
2025-09-01 16:50:53,488 - INFO - �ɹ������֧: feature-E021
2025-09-01 16:50:54,586 - INFO - �ɹ������֧: feature-E021-12-10
2025-09-01 16:50:55,499 - INFO - �ɹ������֧: feature-E023
2025-09-01 16:50:56,501 - INFO - �ɹ������֧: ppe
2025-09-01 16:50:57,903 - INFO - �ɹ������֧: realse
2025-09-01 16:50:58,839 - INFO - �ɹ������֧: realse-0109
2025-09-01 16:50:59,666 - INFO - �ɹ������֧: realse-0113
2025-09-01 16:51:00,512 - INFO - �ɹ������֧: realse-0116
2025-09-01 16:51:01,589 - INFO - �ɹ������֧: realse-0822
2025-09-01 16:51:02,612 - INFO - �ɹ������֧: realse-E0131-1
2025-09-01 16:51:03,739 - INFO - �ɹ������֧: realse-E0131-init
2025-09-01 16:51:04,822 - INFO - �ɹ������֧: realse-cx20250819
2025-09-01 16:51:06,039 - INFO - �ɹ������֧: realse-yh
2025-09-01 16:51:07,184 - INFO - �ɹ������֧: v2.12.4
2025-09-01 16:51:08,191 - INFO - �ɹ������֧: v2.12.5
2025-09-01 16:51:09,387 - INFO - �ɹ������֧: v2.13.0
2025-09-01 16:51:10,322 - INFO - �ɹ������֧: v2.13.3.2
2025-09-01 16:51:11,486 - INFO - ��ʼ��¡�ֿ�: GZZJK_REACT
2025-09-01 16:51:14,448 - INFO - �ɹ���¡�ֿ�: GZZJK_REACT
2025-09-01 16:51:15,515 - INFO - ���� 1 ��Զ�̷�֧: ['master']
2025-09-01 16:51:16,715 - INFO - ��ʼ��¡�ֿ�: GZZJK
2025-09-01 16:51:25,032 - INFO - �ɹ���¡�ֿ�: GZZJK
2025-09-01 16:51:25,676 - INFO - ���� 1 ��Զ�̷�֧: ['master']
2025-09-01 16:51:27,180 - INFO - ��ʼ��¡�ֿ�: bid-chain-client-ui
2025-09-01 16:51:30,824 - INFO - �ɹ���¡�ֿ�: bid-chain-client-ui
2025-09-01 16:51:31,879 - INFO - ���� 2 ��Զ�̷�֧: ['develop', 'master']
2025-09-01 16:51:33,421 - INFO - �ɹ������֧: develop
2025-09-01 16:51:34,366 - INFO - ��ʼ��¡�ֿ�: bid-chain-client-sdk
2025-09-01 16:51:37,260 - INFO - �ɹ���¡�ֿ�: bid-chain-client-sdk
2025-09-01 16:51:38,488 - INFO - ���� 2 ��Զ�̷�֧: ['develop', 'master']
2025-09-01 16:51:39,641 - INFO - �ɹ������֧: develop
2025-09-01 16:51:40,960 - INFO - ��ʼ��¡�ֿ�: bid-api-open-client
2025-09-01 16:51:44,732 - INFO - �ɹ���¡�ֿ�: bid-api-open-client
2025-09-01 16:51:45,814 - INFO - ���� 4 ��Զ�̷�֧: ['develop', 'develop-jsyx', 'master', 'njbk']
2025-09-01 16:51:46,776 - INFO - �ɹ������֧: develop
2025-09-01 16:51:47,830 - INFO - �ɹ������֧: develop-jsyx
2025-09-01 16:51:49,033 - INFO - �ɹ������֧: njbk
2025-09-01 16:51:50,063 - INFO - ��ʼ��¡�ֿ�: bid-api-open
2025-09-01 16:51:53,584 - INFO - �ɹ���¡�ֿ�: bid-api-open
2025-09-01 16:51:54,368 - INFO - ���� 8 ��Զ�̷�֧: ['develop', 'develop-cut', 'develop-encryption', 'develop-expertInfo', 'develop-jstcc', 'develop-jsyx', 'develop-wh', 'master']
2025-09-01 16:51:55,661 - INFO - �ɹ������֧: develop
2025-09-01 16:51:56,864 - INFO - �ɹ������֧: develop-cut
2025-09-01 16:51:58,147 - INFO - �ɹ������֧: develop-encryption
2025-09-01 16:51:59,581 - INFO - �ɹ������֧: develop-expertInfo
2025-09-01 16:52:00,677 - INFO - �ɹ������֧: develop-jstcc
2025-09-01 16:52:01,888 - INFO - �ɹ������֧: develop-jsyx
2025-09-01 16:52:02,943 - INFO - �ɹ������֧: develop-wh
2025-09-01 16:52:03,626 - INFO - ��ʼ��¡�ֿ�: bid-chain-client
2025-09-01 16:52:06,644 - INFO - �ɹ���¡�ֿ�: bid-chain-client
2025-09-01 16:52:07,261 - INFO - ���� 2 ��Զ�̷�֧: ['develop', 'master']
2025-09-01 16:52:08,262 - INFO - �ɹ������֧: develop
2025-09-01 16:52:09,334 - INFO - ��ʼ��¡�ֿ�: dataScreen
2025-09-01 16:52:12,401 - INFO - �ɹ���¡�ֿ�: dataScreen
2025-09-01 16:52:13,551 - INFO - ���� 2 ��Զ�̷�֧: ['master', 'testte_s']
2025-09-01 16:52:14,492 - INFO - �ɹ������֧: testte_s
2025-09-01 16:52:15,606 - INFO - ��ʼ��¡�ֿ�: bid-expert-ui
2025-09-01 16:52:19,817 - INFO - �ɹ���¡�ֿ�: bid-expert-ui
2025-09-01 16:52:20,881 - INFO - ���� 59 ��Զ�̷�֧: ['0802-tianyanc', '1024fixed', '109fix', '2.12.0', '2.12.0.1', '2.12.0.2', '2.12.4-fixclound', '2.15.1.1', '2024-12-23Fixed', '2024_12_19_V2.14.1', '2025-2-13-V2.14.3', '2025_02_21_kaduan', '2025_03_06Fixed', '2025_03_17Fixed', '24_11_5', '6yue', 'D014', 'D015', 'D015-0621', 'D020-0326', 'D020-41', 'E-007-yhy', 'E-007_v2.0', 'E-011', 'E016', 'E021', 'NERTC', 'P-006', 'V2.14.5.1', 'V2.14.6.2', 'chandaobugFixed', 'change_icon', 'conference_fixed', 'crSDK', 'd020_yhd', 'develop', 'develop-2.0.0', 'djkpb66', 'fix_word', 'getMeetingConfig', 'hotfix', 'hotfixed', 'hotfixed-new', 'jstcc-saas', 'master', 'p-004', 'p-004-yhy', 'qingyuedu', 'realse', 'realse-1.0.0', 'room20250702', 'saas-018', 'threeStar-0816', 'tset_y', 'v2.11.0', 'v2.12.3', 'v2.12.4.2-sdk', 'v2.5.1', 'v2.9.0']
2025-09-01 16:52:21,785 - INFO - �ɹ������֧: 0802-tianyanc
2025-09-01 16:52:23,225 - INFO - �ɹ������֧: 1024fixed
2025-09-01 16:52:24,584 - INFO - �ɹ������֧: 109fix
2025-09-01 16:52:25,934 - INFO - �ɹ������֧: 2.12.0
2025-09-01 16:52:27,108 - INFO - �ɹ������֧: 2.12.0.1
2025-09-01 16:52:28,066 - INFO - �ɹ������֧: 2.12.0.2
2025-09-01 16:52:29,160 - INFO - �ɹ������֧: 2.12.4-fixclound
2025-09-01 16:52:30,807 - INFO - �ɹ������֧: 2.15.1.1
2025-09-01 16:52:31,935 - INFO - �ɹ������֧: 2024-12-23Fixed
2025-09-01 16:52:33,053 - INFO - �ɹ������֧: 2024_12_19_V2.14.1
2025-09-01 16:52:34,069 - INFO - �ɹ������֧: 2025-2-13-V2.14.3
2025-09-01 16:52:35,207 - INFO - �ɹ������֧: 2025_02_21_kaduan
2025-09-01 16:52:36,322 - INFO - �ɹ������֧: 2025_03_06Fixed
2025-09-01 16:52:37,366 - INFO - �ɹ������֧: 2025_03_17Fixed
2025-09-01 16:52:38,480 - INFO - �ɹ������֧: 24_11_5
2025-09-01 16:52:39,497 - INFO - �ɹ������֧: 6yue
2025-09-01 16:52:40,553 - INFO - �ɹ������֧: D014
2025-09-01 16:52:41,485 - INFO - �ɹ������֧: D015
2025-09-01 16:52:42,551 - INFO - �ɹ������֧: D015-0621
2025-09-01 16:52:43,683 - INFO - �ɹ������֧: D020-0326
2025-09-01 16:52:44,356 - INFO - �ɹ������֧: D020-41
2025-09-01 16:52:45,284 - INFO - �ɹ������֧: E-007-yhy
2025-09-01 16:52:46,515 - INFO - �ɹ������֧: E-007_v2.0
2025-09-01 16:52:47,808 - INFO - �ɹ������֧: E-011
2025-09-01 16:52:49,287 - INFO - �ɹ������֧: E016
2025-09-01 16:52:50,489 - INFO - �ɹ������֧: E021
2025-09-01 16:52:51,881 - INFO - �ɹ������֧: NERTC
2025-09-01 16:52:52,938 - INFO - �ɹ������֧: P-006
2025-09-01 16:52:53,617 - INFO - �ɹ������֧: V2.14.5.1
2025-09-01 16:52:54,272 - INFO - �ɹ������֧: V2.14.6.2
2025-09-01 16:52:54,912 - INFO - �ɹ������֧: chandaobugFixed
2025-09-01 16:52:55,586 - INFO - �ɹ������֧: change_icon
2025-09-01 16:52:56,295 - INFO - �ɹ������֧: conference_fixed
2025-09-01 16:52:56,952 - INFO - �ɹ������֧: crSDK
2025-09-01 16:52:57,712 - INFO - �ɹ������֧: d020_yhd
2025-09-01 16:52:58,390 - INFO - �ɹ������֧: develop
2025-09-01 16:52:59,481 - INFO - �ɹ������֧: develop-2.0.0
2025-09-01 16:53:00,156 - INFO - �ɹ������֧: djkpb66
2025-09-01 16:53:00,894 - INFO - �ɹ������֧: fix_word
2025-09-01 16:53:01,604 - INFO - �ɹ������֧: getMeetingConfig
2025-09-01 16:53:02,286 - INFO - �ɹ������֧: hotfix
2025-09-01 16:53:03,017 - INFO - �ɹ������֧: hotfixed
2025-09-01 16:53:03,627 - INFO - �ɹ������֧: hotfixed-new
2025-09-01 16:53:04,241 - INFO - �ɹ������֧: jstcc-saas
2025-09-01 16:53:05,228 - INFO - �ɹ������֧: p-004
2025-09-01 16:53:06,119 - INFO - �ɹ������֧: p-004-yhy
2025-09-01 16:53:06,726 - INFO - �ɹ������֧: qingyuedu
2025-09-01 16:53:07,323 - INFO - �ɹ������֧: realse
2025-09-01 16:53:08,002 - INFO - �ɹ������֧: realse-1.0.0
2025-09-01 16:53:08,748 - INFO - �ɹ������֧: room20250702
2025-09-01 16:53:09,603 - INFO - �ɹ������֧: saas-018
2025-09-01 16:53:10,598 - INFO - �ɹ������֧: threeStar-0816
2025-09-01 16:53:11,941 - INFO - �ɹ������֧: tset_y
2025-09-01 16:53:12,494 - INFO - �ɹ������֧: v2.11.0
2025-09-01 16:53:13,052 - INFO - �ɹ������֧: v2.12.3
2025-09-01 16:53:13,707 - INFO - �ɹ������֧: v2.12.4.2-sdk
2025-09-01 16:53:14,515 - INFO - �ɹ������֧: v2.5.1
2025-09-01 16:53:15,441 - INFO - �ɹ������֧: v2.9.0
2025-09-01 16:53:16,253 - INFO - ��ʼ��¡�ֿ�: expert-manage-mp-ui
2025-09-01 16:53:18,804 - INFO - �ɹ���¡�ֿ�: expert-manage-mp-ui
2025-09-01 16:53:19,322 - INFO - ���� 3 ��Զ�̷�֧: ['develop', 'master', 'yhy']
2025-09-01 16:53:19,871 - INFO - �ɹ������֧: develop
2025-09-01 16:53:20,863 - INFO - �ɹ������֧: yhy
2025-09-01 16:53:21,431 - INFO - ��ʼ��¡�ֿ�: expert-manage-ui
2025-09-01 16:53:23,336 - INFO - �ɹ���¡�ֿ�: expert-manage-ui
2025-09-01 16:53:24,100 - INFO - ���� 4 ��Զ�̷�֧: ['082-lq', 'develop', 'master', 'yhy']
2025-09-01 16:53:25,582 - INFO - �ɹ������֧: 082-lq
2025-09-01 16:53:26,720 - INFO - �ɹ������֧: develop
2025-09-01 16:53:27,760 - INFO - �ɹ������֧: yhy
2025-09-01 16:53:28,505 - INFO - ��ʼ��¡�ֿ�: bid-ncc-api
2025-09-01 16:53:32,580 - INFO - �ɹ���¡�ֿ�: bid-ncc-api
2025-09-01 16:53:33,794 - INFO - ���� 19 ��Զ�̷�֧: ['2.11.0', '6yue', 'E002', 'E021-P1-merge', 'E021-P1-merge-push', 'E021-bookkeeper', 'E021-p1', 'branch_office', 'd-025', 'develop', 'develop-D017', 'master', 'p2301-bug', 'realse', 'realse1.0.0', 'realse1.0.0-0720', 'realse1.0.0-ppe', 'v2.3.5_0111', 'zy']
2025-09-01 16:53:35,146 - INFO - �ɹ������֧: 2.11.0
2025-09-01 16:53:36,376 - INFO - �ɹ������֧: 6yue
2025-09-01 16:53:37,537 - INFO - �ɹ������֧: E002
2025-09-01 16:53:38,646 - INFO - �ɹ������֧: E021-P1-merge
2025-09-01 16:53:39,839 - INFO - �ɹ������֧: E021-P1-merge-push
2025-09-01 16:53:40,828 - INFO - �ɹ������֧: E021-bookkeeper
2025-09-01 16:53:41,580 - INFO - �ɹ������֧: E021-p1
2025-09-01 16:53:42,675 - INFO - �ɹ������֧: branch_office
2025-09-01 16:53:43,777 - INFO - �ɹ������֧: d-025
2025-09-01 16:53:44,723 - INFO - �ɹ������֧: develop
2025-09-01 16:53:45,356 - INFO - �ɹ������֧: develop-D017
2025-09-01 16:53:46,408 - INFO - �ɹ������֧: p2301-bug
2025-09-01 16:53:47,598 - INFO - �ɹ������֧: realse
2025-09-01 16:53:48,398 - INFO - �ɹ������֧: realse1.0.0
2025-09-01 16:53:49,155 - INFO - �ɹ������֧: realse1.0.0-0720
2025-09-01 16:53:49,690 - INFO - �ɹ������֧: realse1.0.0-ppe
2025-09-01 16:53:50,214 - INFO - �ɹ������֧: v2.3.5_0111
2025-09-01 16:53:50,880 - INFO - �ɹ������֧: zy
2025-09-01 16:53:52,172 - INFO - ��ʼ��¡�ֿ�: cws-api
2025-09-01 16:53:57,067 - INFO - �ɹ���¡�ֿ�: cws-api
2025-09-01 16:53:58,165 - INFO - ���� 40 ��Զ�̷�֧: ['2.10.0-0524', '2.11.0', '2.11.0-invoiceNo', '2.12.2', '2.12.2-fix', '2.13.1', '2.13.1-fix', '20240703addmessage', '20240711', '2301', '6yue', 'D024-fix', 'E-013bzj', 'E002', 'develop', 'develop-baiwang', 'feature-E013', 'feature-E016P3', 'future-D024', 'future-E023', 'master', 'p081', 'realse', 'realse-02-13', 'realse-03-06', 'realse-file-push', 'realse-log', 'realse-redNo', 'realse1.0.0', 'saas', 'saas_p', 'saas_p-0713', 'saas_p_develop', 'saasp_wangzg', 'v2.6.0', 'v2.6.0-fix', 'v2.6.0-fixv2', 'v2.6.1', 'v2.6.1-0314', 'v2.6.2']
2025-09-01 16:53:59,442 - INFO - �ɹ������֧: 2.10.0-0524
2025-09-01 16:54:00,614 - INFO - �ɹ������֧: 2.11.0
2025-09-01 16:54:01,645 - INFO - �ɹ������֧: 2.11.0-invoiceNo
2025-09-01 16:54:02,642 - INFO - �ɹ������֧: 2.12.2
2025-09-01 16:54:03,502 - INFO - �ɹ������֧: 2.12.2-fix
2025-09-01 16:54:04,779 - INFO - �ɹ������֧: 2.13.1
2025-09-01 16:54:06,088 - INFO - �ɹ������֧: 2.13.1-fix
2025-09-01 16:54:07,286 - INFO - �ɹ������֧: 20240703addmessage
2025-09-01 16:54:08,296 - INFO - �ɹ������֧: 20240711
2025-09-01 16:54:09,454 - INFO - �ɹ������֧: 2301
2025-09-01 16:54:10,454 - INFO - �ɹ������֧: 6yue
2025-09-01 16:54:11,578 - INFO - �ɹ������֧: D024-fix
2025-09-01 16:54:12,270 - INFO - �ɹ������֧: E-013bzj
2025-09-01 16:54:13,287 - INFO - �ɹ������֧: E002
2025-09-01 16:54:14,134 - INFO - �ɹ������֧: develop
2025-09-01 16:54:14,674 - INFO - �ɹ������֧: develop-baiwang
2025-09-01 16:54:15,259 - INFO - �ɹ������֧: feature-E013
2025-09-01 16:54:16,010 - INFO - �ɹ������֧: feature-E016P3
2025-09-01 16:54:17,049 - INFO - �ɹ������֧: future-D024
2025-09-01 16:54:18,222 - INFO - �ɹ������֧: future-E023
2025-09-01 16:54:19,360 - INFO - �ɹ������֧: p081
2025-09-01 16:54:20,411 - INFO - �ɹ������֧: realse
2025-09-01 16:54:21,088 - INFO - �ɹ������֧: realse-02-13
2025-09-01 16:54:22,105 - INFO - �ɹ������֧: realse-03-06
2025-09-01 16:54:23,168 - INFO - �ɹ������֧: realse-file-push
2025-09-01 16:54:24,517 - INFO - �ɹ������֧: realse-log
2025-09-01 16:54:25,906 - INFO - �ɹ������֧: realse-redNo
2025-09-01 16:54:27,177 - INFO - �ɹ������֧: realse1.0.0
2025-09-01 16:54:28,421 - INFO - �ɹ������֧: saas
2025-09-01 16:54:29,518 - INFO - �ɹ������֧: saas_p
2025-09-01 16:54:30,197 - INFO - �ɹ������֧: saas_p-0713
2025-09-01 16:54:30,832 - INFO - �ɹ������֧: saas_p_develop
2025-09-01 16:54:32,309 - INFO - �ɹ������֧: saasp_wangzg
2025-09-01 16:54:33,516 - INFO - �ɹ������֧: v2.6.0
2025-09-01 16:54:34,607 - INFO - �ɹ������֧: v2.6.0-fix
2025-09-01 16:54:35,522 - INFO - �ɹ������֧: v2.6.0-fixv2
2025-09-01 16:54:36,539 - INFO - �ɹ������֧: v2.6.1
2025-09-01 16:54:37,186 - INFO - �ɹ������֧: v2.6.1-0314
2025-09-01 16:54:38,029 - INFO - �ɹ������֧: v2.6.2
2025-09-01 16:54:39,296 - INFO - ��ʼ��¡�ֿ�: bid-azalea-ui
2025-09-01 16:54:43,777 - INFO - �ɹ���¡�ֿ�: bid-azalea-ui
2025-09-01 16:54:45,014 - INFO - ���� 5 ��Զ�̷�֧: ['develop', 'master', 'release1.0', 'release1.0-origin', 'release1.1']
2025-09-01 16:54:46,079 - WARNING - �����֧ʧ�� develop: fatal: a branch named 'develop' already exists

2025-09-01 16:54:46,973 - INFO - �ɹ������֧: release1.0
2025-09-01 16:54:47,612 - INFO - �ɹ������֧: release1.0-origin
2025-09-01 16:54:48,260 - INFO - �ɹ������֧: release1.1
2025-09-01 16:54:48,945 - INFO - ��ʼ��¡�ֿ�: bid-common-util
2025-09-01 16:54:51,066 - INFO - �ɹ���¡�ֿ�: bid-common-util
2025-09-01 16:54:51,749 - INFO - ���� 2 ��Զ�̷�֧: ['develop', 'master']
2025-09-01 16:54:52,447 - INFO - �ɹ������֧: develop
2025-09-01 16:54:53,225 - INFO - ��ʼ��¡�ֿ�: bid-common-bean
2025-09-01 16:54:56,022 - INFO - �ɹ���¡�ֿ�: bid-common-bean
2025-09-01 16:54:57,047 - INFO - ���� 24 ��Զ�̷�֧: ['1.0.32-009', '1.0.7', '1.0.8', 'D-009from2.11.0', 'D-025', 'D009', 'D009-D026-merge', 'E-016', 'E-021', 'E009', 'P-009', 'develop', 'develop-016-3', 'develop-wh', 'master', 'njbk', 'p-081', 'realse', 'realse-1.0.27', 'realse-1.0.28', 'realse-D017-V2', 'saas-1.0.8', 'xwp', 'xwp7']
2025-09-01 16:54:58,149 - INFO - �ɹ������֧: 1.0.32-009
2025-09-01 16:54:59,448 - INFO - �ɹ������֧: 1.0.7
2025-09-01 16:55:00,471 - INFO - �ɹ������֧: 1.0.8
2025-09-01 16:55:01,361 - INFO - �ɹ������֧: D-009from2.11.0
2025-09-01 16:55:02,347 - INFO - �ɹ������֧: D-025
2025-09-01 16:55:03,463 - INFO - �ɹ������֧: D009
2025-09-01 16:55:04,497 - INFO - �ɹ������֧: D009-D026-merge
2025-09-01 16:55:05,143 - INFO - �ɹ������֧: E-016
2025-09-01 16:55:06,398 - INFO - �ɹ������֧: E-021
2025-09-01 16:55:07,782 - INFO - �ɹ������֧: E009
2025-09-01 16:55:09,025 - INFO - �ɹ������֧: P-009
2025-09-01 16:55:10,283 - INFO - �ɹ������֧: develop
2025-09-01 16:55:11,548 - INFO - �ɹ������֧: develop-016-3
2025-09-01 16:55:12,871 - INFO - �ɹ������֧: develop-wh
2025-09-01 16:55:14,019 - INFO - �ɹ������֧: njbk
2025-09-01 16:55:15,358 - INFO - �ɹ������֧: p-081
2025-09-01 16:55:16,654 - INFO - �ɹ������֧: realse
2025-09-01 16:55:17,323 - INFO - �ɹ������֧: realse-1.0.27
2025-09-01 16:55:18,125 - INFO - �ɹ������֧: realse-1.0.28
2025-09-01 16:55:19,274 - INFO - �ɹ������֧: realse-D017-V2
2025-09-01 16:55:20,269 - INFO - �ɹ������֧: saas-1.0.8
2025-09-01 16:55:21,522 - INFO - �ɹ������֧: xwp
2025-09-01 16:55:22,632 - INFO - �ɹ������֧: xwp7
2025-09-01 16:55:23,726 - INFO - ��ʼ��¡�ֿ�: bid-ocr-server
2025-09-01 16:55:27,162 - INFO - �ɹ���¡�ֿ�: bid-ocr-server
2025-09-01 16:55:28,521 - INFO - ���� 2 ��Զ�̷�֧: ['develop', 'master']
2025-09-01 16:55:29,616 - INFO - �ɹ������֧: develop
2025-09-01 16:55:30,981 - INFO - ��ʼ��¡�ֿ�: WechatGatwayApi
2025-09-01 16:55:34,734 - INFO - �ɹ���¡�ֿ�: WechatGatwayApi
2025-09-01 16:55:35,804 - INFO - ���� 4 ��Զ�̷�֧: ['dev', 'hzwtest', 'master', 'ppe']
2025-09-01 16:55:36,812 - INFO - �ɹ������֧: dev
2025-09-01 16:55:37,913 - INFO - �ɹ������֧: hzwtest
2025-09-01 16:55:38,938 - INFO - �ɹ������֧: ppe
2025-09-01 16:55:39,830 - INFO - ��ʼ��¡�ֿ�: UserApi
2025-09-01 16:55:43,096 - INFO - �ɹ���¡�ֿ�: UserApi
2025-09-01 16:55:44,232 - INFO - ���� 7 ��Զ�̷�֧: ['dev', 'dev_hzw', 'master', 'ppe', 'ppe_hzw', 'ppe_hzw_weak20241218', 'realse_hzw']
2025-09-01 16:55:45,249 - INFO - �ɹ������֧: dev
2025-09-01 16:55:46,274 - INFO - �ɹ������֧: dev_hzw
2025-09-01 16:55:47,374 - INFO - �ɹ������֧: ppe
2025-09-01 16:55:48,733 - INFO - �ɹ������֧: ppe_hzw
2025-09-01 16:55:49,814 - INFO - �ɹ������֧: ppe_hzw_weak20241218
2025-09-01 16:55:50,696 - INFO - �ɹ������֧: realse_hzw
2025-09-01 16:55:51,910 - INFO - ��ʼ��¡�ֿ�: SyncApi
2025-09-01 16:55:55,952 - INFO - �ɹ���¡�ֿ�: SyncApi
2025-09-01 16:55:57,214 - INFO - ���� 20 ��Զ�̷�֧: ['cherry-pick-1a675994-f465f90d', 'dev', 'dev_hzw', 'dev_hzw_1118', 'dev_hzw_1209', 'dev_hzw_wh', 'master', 'master_hzw', 'ppe', 'ppe_hzw', 'realse_hzw', 'realse_hzw-20231226', 'realse_hzw0618', 'realse_hzw_1223', 'realse_hzw_20240704', 'realse_hzw_cx20250408', 'realse_hzw_max_active0409', 'realse_hzw_rsa', 'realse_hzwcx20250408', 'release_hzw_login_fix']
2025-09-01 16:55:58,416 - INFO - �ɹ������֧: cherry-pick-1a675994-f465f90d
2025-09-01 16:55:59,679 - INFO - �ɹ������֧: dev
2025-09-01 16:56:00,581 - INFO - �ɹ������֧: dev_hzw
2025-09-01 16:56:01,297 - INFO - �ɹ������֧: dev_hzw_1118
2025-09-01 16:56:02,456 - INFO - �ɹ������֧: dev_hzw_1209
2025-09-01 16:56:03,336 - INFO - �ɹ������֧: dev_hzw_wh
2025-09-01 16:56:04,409 - INFO - �ɹ������֧: master_hzw
2025-09-01 16:56:05,479 - INFO - �ɹ������֧: ppe
2025-09-01 16:56:06,353 - INFO - �ɹ������֧: ppe_hzw
2025-09-01 16:56:07,140 - INFO - �ɹ������֧: realse_hzw
2025-09-01 16:56:08,222 - INFO - �ɹ������֧: realse_hzw-20231226
2025-09-01 16:56:09,217 - INFO - �ɹ������֧: realse_hzw0618
2025-09-01 16:56:10,242 - INFO - �ɹ������֧: realse_hzw_1223
2025-09-01 16:56:11,084 - INFO - �ɹ������֧: realse_hzw_20240704
2025-09-01 16:56:11,901 - INFO - �ɹ������֧: realse_hzw_cx20250408
2025-09-01 16:56:12,979 - INFO - �ɹ������֧: realse_hzw_max_active0409
2025-09-01 16:56:13,959 - INFO - �ɹ������֧: realse_hzw_rsa
2025-09-01 16:56:15,057 - INFO - �ɹ������֧: realse_hzwcx20250408
2025-09-01 16:56:16,034 - INFO - �ɹ������֧: release_hzw_login_fix
2025-09-01 16:56:17,132 - INFO - ��ʼ��¡�ֿ�: SupplierApi
2025-09-01 16:56:20,410 - INFO - �ɹ���¡�ֿ�: SupplierApi
2025-09-01 16:56:21,749 - INFO - ���� 2 ��Զ�̷�֧: ['dev', 'master']
2025-09-01 16:56:23,097 - INFO - �ɹ������֧: dev
2025-09-01 16:56:24,423 - INFO - ��ʼ��¡�ֿ�: SuperviseDataApiClient
2025-09-01 16:56:27,453 - INFO - �ɹ���¡�ֿ�: SuperviseDataApiClient
2025-09-01 16:56:28,327 - INFO - ���� 1 ��Զ�̷�֧: ['master']
2025-09-01 16:56:29,523 - INFO - ��ʼ��¡�ֿ�: ServerConfigure
2025-09-01 16:56:32,792 - INFO - �ɹ���¡�ֿ�: ServerConfigure
2025-09-01 16:56:33,786 - INFO - ���� 1 ��Զ�̷�֧: ['master']
2025-09-01 16:56:34,959 - INFO - ��ʼ��¡�ֿ�: RuleSet10MessageProducer
2025-09-01 16:56:38,896 - INFO - �ɹ���¡�ֿ�: RuleSet10MessageProducer
2025-09-01 16:56:39,782 - INFO - ���� 2 ��Զ�̷�֧: ['dev', 'master']
2025-09-01 16:56:40,466 - INFO - �ɹ������֧: dev
2025-09-01 16:56:41,265 - INFO - ��ʼ��¡�ֿ�: PublicServiceWebSite
2025-09-01 16:56:49,094 - INFO - �ɹ���¡�ֿ�: PublicServiceWebSite
2025-09-01 16:56:50,311 - INFO - ���� 8 ��Զ�̷�֧: ['2025.1.8', '2025.4.18', 'E-017', 'E-017-zhr', 'develop', 'master', 'p014', 'p014-2']
2025-09-01 16:56:51,608 - INFO - �ɹ������֧: 2025.1.8
2025-09-01 16:56:52,289 - INFO - �ɹ������֧: 2025.4.18
2025-09-01 16:56:53,736 - INFO - �ɹ������֧: E-017
2025-09-01 16:56:54,901 - INFO - �ɹ������֧: E-017-zhr
2025-09-01 16:56:55,938 - INFO - �ɹ������֧: develop
2025-09-01 16:56:56,926 - INFO - �ɹ������֧: p014
2025-09-01 16:56:57,468 - INFO - �ɹ������֧: p014-2
2025-09-01 16:56:58,018 - INFO - ��ʼ��¡�ֿ�: PublicServiceMini
2025-09-01 16:57:01,280 - INFO - �ɹ���¡�ֿ�: PublicServiceMini
2025-09-01 16:57:02,161 - INFO - ���� 2 ��Զ�̷�֧: ['dev', 'master']
2025-09-01 16:57:03,329 - INFO - �ɹ������֧: dev
2025-09-01 16:57:04,424 - INFO - ��ʼ��¡�ֿ�: PublicServiceDashBoard
2025-09-01 16:57:08,996 - INFO - �ɹ���¡�ֿ�: PublicServiceDashBoard
2025-09-01 16:57:09,939 - INFO - ���� 7 ��Զ�̷�֧: ['E-017', 'dev-lqz', 'dev-yhy', 'develop', 'master', 'p014', 'p014-2']
2025-09-01 16:57:11,367 - INFO - �ɹ������֧: E-017
2025-09-01 16:57:12,408 - INFO - �ɹ������֧: dev-lqz
2025-09-01 16:57:13,845 - INFO - �ɹ������֧: dev-yhy
2025-09-01 16:57:15,266 - INFO - �ɹ������֧: develop
2025-09-01 16:57:16,280 - INFO - �ɹ������֧: p014
2025-09-01 16:57:17,170 - INFO - �ɹ������֧: p014-2
2025-09-01 16:57:17,817 - INFO - ��ʼ��¡�ֿ�: PlatformApi
2025-09-01 16:57:20,187 - INFO - �ɹ���¡�ֿ�: PlatformApi
2025-09-01 16:57:20,920 - INFO - ���� 7 ��Զ�̷�֧: ['dev', 'dev_hzw', 'master', 'ppe', 'ppe_hzw', 'realse_hzw', 'zk']
2025-09-01 16:57:21,966 - INFO - �ɹ������֧: dev
2025-09-01 16:57:23,600 - INFO - �ɹ������֧: dev_hzw
2025-09-01 16:57:24,763 - INFO - �ɹ������֧: ppe
2025-09-01 16:57:25,782 - INFO - �ɹ������֧: ppe_hzw
2025-09-01 16:57:26,988 - INFO - �ɹ������֧: realse_hzw
2025-09-01 16:57:27,595 - INFO - �ɹ������֧: zk
2025-09-01 16:57:28,569 - INFO - ��ʼ��¡�ֿ�: MonitorUserApi
2025-09-01 16:57:32,280 - INFO - �ɹ���¡�ֿ�: MonitorUserApi
2025-09-01 16:57:33,417 - INFO - ���� 2 ��Զ�̷�֧: ['dev', 'master']
2025-09-01 16:57:34,756 - INFO - �ɹ������֧: dev
2025-09-01 16:57:36,028 - INFO - ��ʼ��¡�ֿ�: MonitorUI
2025-09-01 16:57:39,024 - INFO - �ɹ���¡�ֿ�: MonitorUI
2025-09-01 16:57:40,096 - INFO - ���� 3 ��Զ�̷�֧: ['dev', 'hzwtest', 'master']
2025-09-01 16:57:41,333 - INFO - �ɹ������֧: dev
2025-09-01 16:57:42,279 - INFO - �ɹ������֧: hzwtest
2025-09-01 16:57:43,143 - INFO - ��ʼ��¡�ֿ�: MonitorBusinessApi
2025-09-01 16:57:46,485 - INFO - �ɹ���¡�ֿ�: MonitorBusinessApi
2025-09-01 16:57:47,837 - INFO - ���� 2 ��Զ�̷�֧: ['dev', 'master']
2025-09-01 16:57:49,382 - INFO - �ɹ������֧: dev
2025-09-01 16:57:50,636 - INFO - ��ʼ��¡�ֿ�: MobileApp
2025-09-01 16:57:55,246 - INFO - �ɹ���¡�ֿ�: MobileApp
2025-09-01 16:57:56,297 - INFO - ���� 2 ��Զ�̷�֧: ['dev', 'master']
2025-09-01 16:57:57,972 - INFO - �ɹ������֧: dev
2025-09-01 16:57:59,468 - INFO - ��ʼ��¡�ֿ�: MiniWebApp
2025-09-01 16:58:01,893 - INFO - �ɹ���¡�ֿ�: MiniWebApp
2025-09-01 16:58:02,426 - INFO - ���� 2 ��Զ�̷�֧: ['dev', 'master']
2025-09-01 16:58:03,064 - INFO - �ɹ������֧: dev
2025-09-01 16:58:04,072 - INFO - ��ʼ��¡�ֿ�: MarketPlayersAPI
2025-09-01 16:58:07,146 - INFO - �ɹ���¡�ֿ�: MarketPlayersAPI
2025-09-01 16:58:07,999 - INFO - ���� 5 ��Զ�̷�֧: ['dev', 'master', 'ppe', 'ppe_weak20241218', 'realse_hzw']
2025-09-01 16:58:09,200 - INFO - �ɹ������֧: dev
2025-09-01 16:58:10,456 - INFO - �ɹ������֧: ppe
2025-09-01 16:58:11,544 - INFO - �ɹ������֧: ppe_weak20241218
2025-09-01 16:58:12,796 - INFO - �ɹ������֧: realse_hzw
2025-09-01 16:58:14,148 - INFO - ��ʼ��¡�ֿ�: DataSyncApi
2025-09-01 16:58:17,893 - INFO - �ɹ���¡�ֿ�: DataSyncApi
2025-09-01 16:58:19,470 - INFO - ���� 14 ��Զ�̷�֧: ['dev', 'dev_hzw', 'dev_hzw_fix', 'master', 'ppe', 'ppe_hzw', 'ppe_hzw_fix', 'realse_hzw', 'realse_hzw-20231226', 'realse_hzw0617', 'realse_hzw_0108', 'realse_hzw_1223', 'realse_hzw_ai20250805', 'realse_hzw_rsa']
2025-09-01 16:58:21,022 - INFO - �ɹ������֧: dev
2025-09-01 16:58:22,195 - INFO - �ɹ������֧: dev_hzw
2025-09-01 16:58:23,249 - INFO - �ɹ������֧: dev_hzw_fix
2025-09-01 16:58:24,477 - INFO - �ɹ������֧: ppe
2025-09-01 16:58:25,434 - INFO - �ɹ������֧: ppe_hzw
2025-09-01 16:58:26,031 - INFO - �ɹ������֧: ppe_hzw_fix
2025-09-01 16:58:26,936 - INFO - �ɹ������֧: realse_hzw
2025-09-01 16:58:28,110 - INFO - �ɹ������֧: realse_hzw-20231226
2025-09-01 16:58:29,311 - INFO - �ɹ������֧: realse_hzw0617
2025-09-01 16:58:30,585 - INFO - �ɹ������֧: realse_hzw_0108
2025-09-01 16:58:31,758 - INFO - �ɹ������֧: realse_hzw_1223
2025-09-01 16:58:32,814 - INFO - �ɹ������֧: realse_hzw_ai20250805
2025-09-01 16:58:33,934 - INFO - �ɹ������֧: realse_hzw_rsa
2025-09-01 16:58:34,624 - INFO - ��ʼ��¡�ֿ�: ContentManageAPI
2025-09-01 16:58:38,325 - INFO - �ɹ���¡�ֿ�: ContentManageAPI
2025-09-01 16:58:39,346 - INFO - ���� 12 ��Զ�̷�֧: ['E017', 'E017-1', 'E017-2', 'E017-2weak20241218', 'dev', 'dev_hzw', 'dev_hzw_xinyong', 'dev_hzw_xt', 'master', 'ppe', 'ppe_hzw', 'realse_hzw']
2025-09-01 16:58:40,350 - INFO - �ɹ������֧: E017
2025-09-01 16:58:41,608 - INFO - �ɹ������֧: E017-1
2025-09-01 16:58:42,761 - INFO - �ɹ������֧: E017-2
2025-09-01 16:58:43,782 - INFO - �ɹ������֧: E017-2weak20241218
2025-09-01 16:58:44,964 - INFO - �ɹ������֧: dev
2025-09-01 16:58:45,766 - INFO - �ɹ������֧: dev_hzw
2025-09-01 16:58:46,325 - INFO - �ɹ������֧: dev_hzw_xinyong
2025-09-01 16:58:46,859 - INFO - �ɹ������֧: dev_hzw_xt
2025-09-01 16:58:47,386 - INFO - �ɹ������֧: ppe
2025-09-01 16:58:47,983 - INFO - �ɹ������֧: ppe_hzw
2025-09-01 16:58:48,554 - INFO - �ɹ������֧: realse_hzw
2025-09-01 16:58:49,473 - INFO - ��ʼ��¡�ֿ�: BulletinSearchApi
2025-09-01 16:58:52,739 - INFO - �ɹ���¡�ֿ�: BulletinSearchApi
2025-09-01 16:58:53,542 - INFO - ���� 6 ��Զ�̷�֧: ['dev', 'dev_hzw', 'hzw_ppe', 'master', 'ppe', 'realse_hzw']
2025-09-01 16:58:54,714 - INFO - �ɹ������֧: dev
2025-09-01 16:58:55,869 - INFO - �ɹ������֧: dev_hzw
2025-09-01 16:58:57,155 - INFO - �ɹ������֧: hzw_ppe
2025-09-01 16:58:58,244 - INFO - �ɹ������֧: ppe
2025-09-01 16:58:59,539 - INFO - �ɹ������֧: realse_hzw
2025-09-01 16:59:00,746 - INFO - ��ʼ��¡�ֿ�: BulletinMessageBroker
2025-09-01 16:59:04,566 - INFO - �ɹ���¡�ֿ�: BulletinMessageBroker
2025-09-01 16:59:05,412 - INFO - ���� 2 ��Զ�̷�֧: ['dev', 'master']
2025-09-01 16:59:06,368 - INFO - �ɹ������֧: dev
2025-09-01 16:59:07,464 - INFO - ��ʼ��¡�ֿ�: bid-ocr
2025-09-01 16:59:10,846 - INFO - �ɹ���¡�ֿ�: bid-ocr
2025-09-01 16:59:11,847 - INFO - ���� 2 ��Զ�̷�֧: ['develop', 'master']
2025-09-01 16:59:12,903 - INFO - �ɹ������֧: develop
2025-09-01 16:59:14,003 - INFO - ��ʼ��¡�ֿ�: bid-expert-api
2025-09-01 16:59:17,459 - INFO - �ɹ���¡�ֿ�: bid-expert-api
2025-09-01 16:59:17,999 - INFO - ���� 7 ��Զ�̷�֧: ['dev-081', 'develop', 'feature-089', 'master', 'ppe', 'realse1.0.2', 'realse1.0.2-jinjifabu']
2025-09-01 16:59:19,172 - INFO - �ɹ������֧: dev-081
2025-09-01 16:59:20,512 - INFO - �ɹ������֧: develop
2025-09-01 16:59:21,856 - INFO - �ɹ������֧: feature-089
2025-09-01 16:59:23,119 - INFO - �ɹ������֧: ppe
2025-09-01 16:59:24,411 - INFO - �ɹ������֧: realse1.0.2
2025-09-01 16:59:25,609 - INFO - �ɹ������֧: realse1.0.2-jinjifabu
2025-09-01 16:59:26,928 - INFO - ��ʼ��¡�ֿ�: file-preview
2025-09-01 16:59:40,997 - INFO - �ɹ���¡�ֿ�: file-preview
2025-09-01 16:59:42,146 - INFO - ���� 3 ��Զ�̷�֧: ['develop', 'master', 'realse1.0.0']
2025-09-01 16:59:52,354 - INFO - �ɹ������֧: develop
2025-09-01 16:59:53,571 - INFO - �ɹ������֧: realse1.0.0
2025-09-01 16:59:56,039 - INFO - ��ʼ��¡�ֿ�: sms
2025-09-01 17:00:00,801 - INFO - �ɹ���¡�ֿ�: sms
2025-09-01 17:00:02,195 - INFO - ���� 5 ��Զ�̷�֧: ['changeSms', 'develop', 'master', 'ppe', 'realse1.0.0']
2025-09-01 17:00:03,259 - INFO - �ɹ������֧: changeSms
2025-09-01 17:00:04,015 - INFO - �ɹ������֧: develop
2025-09-01 17:00:04,846 - INFO - �ɹ������֧: ppe
2025-09-01 17:00:06,301 - INFO - �ɹ������֧: realse1.0.0
2025-09-01 17:00:07,322 - INFO - ��ʼ��¡�ֿ�: workflow
2025-09-01 17:00:21,574 - INFO - �ɹ���¡�ֿ�: workflow
2025-09-01 17:00:22,217 - INFO - ���� 22 ��Զ�̷�֧: ['2.12.1', '2.12.1.2', '2.12.1.3', '2.12.2', '2.2.0.2', '2.3.0', '2.8.0', '6yue', 'D-016-3', 'develop', 'develop-wh', 'develop-xt', 'feature-D017', 'feature-E021', 'master', 'njbk_yhd', 'ppe', 'realse', 'realse-encryption', 'realse1.0.2', 'realse1.0.2-0724', 'relase-1.0.2-1113']
2025-09-01 17:00:24,468 - INFO - �ɹ������֧: 2.12.1
2025-09-01 17:00:25,883 - INFO - �ɹ������֧: 2.12.1.2
2025-09-01 17:00:26,625 - INFO - �ɹ������֧: 2.12.1.3
2025-09-01 17:00:28,029 - INFO - �ɹ������֧: 2.12.2
2025-09-01 17:00:29,388 - INFO - �ɹ������֧: 2.2.0.2
2025-09-01 17:00:31,008 - INFO - �ɹ������֧: 2.3.0
2025-09-01 17:00:31,849 - INFO - �ɹ������֧: 2.8.0
2025-09-01 17:00:32,818 - INFO - �ɹ������֧: 6yue
2025-09-01 17:00:33,842 - INFO - �ɹ������֧: D-016-3
2025-09-01 17:00:34,634 - INFO - �ɹ������֧: develop
2025-09-01 17:00:35,574 - INFO - �ɹ������֧: develop-wh
2025-09-01 17:00:36,721 - INFO - �ɹ������֧: develop-xt
2025-09-01 17:00:37,402 - INFO - �ɹ������֧: feature-D017
2025-09-01 17:00:38,511 - INFO - �ɹ������֧: feature-E021
2025-09-01 17:00:39,599 - INFO - �ɹ������֧: njbk_yhd
2025-09-01 17:00:40,723 - INFO - �ɹ������֧: ppe
2025-09-01 17:00:41,718 - INFO - �ɹ������֧: realse
2025-09-01 17:00:42,376 - INFO - �ɹ������֧: realse-encryption
2025-09-01 17:00:42,924 - INFO - �ɹ������֧: realse1.0.2
2025-09-01 17:00:43,477 - INFO - �ɹ������֧: realse1.0.2-0724
2025-09-01 17:00:44,089 - INFO - �ɹ������֧: relase-1.0.2-1113
2025-09-01 17:00:45,395 - INFO - ��ʼ��¡�ֿ�: bid-ui-tenderee
2025-09-01 17:00:47,555 - INFO - �ɹ���¡�ֿ�: bid-ui-tenderee
2025-09-01 17:00:48,112 - INFO - ���� 6 ��Զ�̷�֧: ['R005', 'R005-lq', 'R005-yhy', 'R005_yhd', 'develop', 'master']
2025-09-01 17:00:48,811 - INFO - �ɹ������֧: R005
2025-09-01 17:00:49,419 - INFO - �ɹ������֧: R005-lq
2025-09-01 17:00:50,052 - INFO - �ɹ������֧: R005-yhy
2025-09-01 17:00:51,136 - INFO - �ɹ������֧: R005_yhd
2025-09-01 17:00:52,236 - INFO - �ɹ������֧: develop
2025-09-01 17:00:53,282 - INFO - ��ʼ��¡�ֿ�: bid-ui-maintain
2025-09-01 17:00:56,737 - INFO - �ɹ���¡�ֿ�: bid-ui-maintain
2025-09-01 17:00:58,056 - INFO - ���� 26 ��Զ�̷�֧: ['0819', '2.13.1-fix', '2024-11-12', '2025_01_09', 'D009', 'E021-p1', 'NJBK', 'SaaS-v2.1.0', 'auth-0730', 'develop', 'feature-saas', 'fix_word', 'jstcc-sass', 'master', 'realse', 'realse-1.0.13-0724', 'realse-1.0.3', 'realse-1.0.3-0829', 'realse-1.0.3-ppe', 'realse-1.0.3_2.5.1', 'saasv2.10_56', 'v2.11.0', 'v2.12.4', 'v2.12.5', 'v2.13.0', 'v2.13.0_yhd']
2025-09-01 17:00:59,455 - INFO - �ɹ������֧: 0819
2025-09-01 17:01:00,817 - INFO - �ɹ������֧: 2.13.1-fix
2025-09-01 17:01:01,916 - INFO - �ɹ������֧: 2024-11-12
2025-09-01 17:01:02,672 - INFO - �ɹ������֧: 2025_01_09
2025-09-01 17:01:03,508 - INFO - �ɹ������֧: D009
2025-09-01 17:01:04,296 - INFO - �ɹ������֧: E021-p1
2025-09-01 17:01:05,174 - INFO - �ɹ������֧: NJBK
2025-09-01 17:01:05,850 - INFO - �ɹ������֧: SaaS-v2.1.0
2025-09-01 17:01:06,494 - INFO - �ɹ������֧: auth-0730
2025-09-01 17:01:07,170 - INFO - �ɹ������֧: develop
2025-09-01 17:01:07,820 - INFO - �ɹ������֧: feature-saas
2025-09-01 17:01:08,482 - INFO - �ɹ������֧: fix_word
2025-09-01 17:01:09,151 - INFO - �ɹ������֧: jstcc-sass
2025-09-01 17:01:09,940 - INFO - �ɹ������֧: realse
2025-09-01 17:01:10,735 - INFO - �ɹ������֧: realse-1.0.13-0724
2025-09-01 17:01:11,369 - INFO - �ɹ������֧: realse-1.0.3
2025-09-01 17:01:12,011 - INFO - �ɹ������֧: realse-1.0.3-0829
2025-09-01 17:01:12,612 - INFO - �ɹ������֧: realse-1.0.3-ppe
2025-09-01 17:01:13,210 - INFO - �ɹ������֧: realse-1.0.3_2.5.1
2025-09-01 17:01:13,891 - INFO - �ɹ������֧: saasv2.10_56
2025-09-01 17:01:14,920 - INFO - �ɹ������֧: v2.11.0
2025-09-01 17:01:15,750 - INFO - �ɹ������֧: v2.12.4
2025-09-01 17:01:16,379 - INFO - �ɹ������֧: v2.12.5
2025-09-01 17:01:17,001 - INFO - �ɹ������֧: v2.13.0
2025-09-01 17:01:17,667 - INFO - �ɹ������֧: v2.13.0_yhd
2025-09-01 17:01:18,355 - INFO - ��ʼ��¡�ֿ�: bid-ui-supplier
2025-09-01 17:01:21,951 - INFO - �ɹ���¡�ֿ�: bid-ui-supplier
2025-09-01 17:01:23,129 - INFO - ���� 75 ��Զ�̷�֧: ['109fix', '2.12.0', '2.12.1', '2.12.4-fixclound', '2.15.1.1', '2.15.2.1', '2.15.2.3', '2024_12_19_V2.14.1', '2025-2-13-V2.14.3', '6yue', 'D009', 'D014', 'D020-41', 'D024', 'E-007', 'E-009-lmk', 'E-011', 'E-013', 'E-016', 'E004', 'E012_p1', 'E013-bond', 'E016_p3', 'M004', 'NERTC', 'P-004', 'P-006', 'P-009', 'P-009-lmk', 'R008', 'SaaS-v2.1.0-lqz', 'SaaS-v2.1.0_supplier', 'V2.14.5.1', 'V2.14.6.2', 'ambV2', 'change_icon', 'conference_fixed', 'd020', 'd020-0325', 'd020-new', 'd020-yhy', 'd020_yhd', 'd027', 'develop', 'feature-018', 'feature-091', 'feature-092', 'feature-D017', 'feature-saas', 'feature-saas-0.0.1', 'fix_word', 'getMeetingConfig', 'hotfixed', 'jstcc-saas', 'loading', 'master', 'p4', 'qingyuedu', 'realse', 'realse-1.0.19', 'realse-1.0.19-1127', 'realse-1.0.19-jinjifabu', 'realse_bug_zhr', 'realse_fixed', 'realse_i18n_yhd', 'room20250702', 'saas-0.0.1-lqz', 'saas-018', 'saas-v2.2.0.2', 'threeStar-0816', 'v-2.2.10', 'v2.12.3', 'v2.12.4.2-sdk', 'v2.14.4.7', 'v2.6.0-kb']
2025-09-01 17:01:24,379 - INFO - �ɹ������֧: 109fix
2025-09-01 17:01:25,566 - INFO - �ɹ������֧: 2.12.0
2025-09-01 17:01:26,773 - INFO - �ɹ������֧: 2.12.1
2025-09-01 17:01:27,778 - INFO - �ɹ������֧: 2.12.4-fixclound
2025-09-01 17:01:28,752 - INFO - �ɹ������֧: 2.15.1.1
2025-09-01 17:01:29,977 - INFO - �ɹ������֧: 2.15.2.1
2025-09-01 17:01:30,857 - INFO - �ɹ������֧: 2.15.2.3
2025-09-01 17:01:31,585 - INFO - �ɹ������֧: 2024_12_19_V2.14.1
2025-09-01 17:01:32,863 - INFO - �ɹ������֧: 2025-2-13-V2.14.3
2025-09-01 17:01:34,376 - INFO - �ɹ������֧: 6yue
2025-09-01 17:01:35,862 - INFO - �ɹ������֧: D009
2025-09-01 17:01:37,261 - INFO - �ɹ������֧: D014
2025-09-01 17:01:38,598 - INFO - �ɹ������֧: D020-41
2025-09-01 17:01:39,854 - INFO - �ɹ������֧: D024
2025-09-01 17:01:41,052 - INFO - �ɹ������֧: E-007
2025-09-01 17:01:42,222 - INFO - �ɹ������֧: E-009-lmk
2025-09-01 17:01:43,314 - INFO - �ɹ������֧: E-011
2025-09-01 17:01:44,367 - INFO - �ɹ������֧: E-013
2025-09-01 17:01:45,557 - INFO - �ɹ������֧: E-016
2025-09-01 17:01:46,704 - INFO - �ɹ������֧: E004
2025-09-01 17:01:47,827 - INFO - �ɹ������֧: E012_p1
2025-09-01 17:01:48,504 - INFO - �ɹ������֧: E013-bond
2025-09-01 17:01:49,639 - INFO - �ɹ������֧: E016_p3
2025-09-01 17:01:50,937 - INFO - �ɹ������֧: M004
2025-09-01 17:01:52,366 - INFO - �ɹ������֧: NERTC
2025-09-01 17:01:53,906 - INFO - �ɹ������֧: P-004
2025-09-01 17:01:55,218 - INFO - �ɹ������֧: P-006
2025-09-01 17:01:56,465 - INFO - �ɹ������֧: P-009
2025-09-01 17:01:57,427 - INFO - �ɹ������֧: P-009-lmk
2025-09-01 17:01:58,150 - INFO - �ɹ������֧: R008
2025-09-01 17:01:58,876 - INFO - �ɹ������֧: SaaS-v2.1.0-lqz
2025-09-01 17:01:59,544 - INFO - �ɹ������֧: SaaS-v2.1.0_supplier
2025-09-01 17:02:00,309 - INFO - �ɹ������֧: V2.14.5.1
2025-09-01 17:02:00,959 - INFO - �ɹ������֧: V2.14.6.2
2025-09-01 17:02:01,716 - INFO - �ɹ������֧: ambV2
2025-09-01 17:02:02,388 - INFO - �ɹ������֧: change_icon
2025-09-01 17:02:03,133 - INFO - �ɹ������֧: conference_fixed
2025-09-01 17:02:04,252 - INFO - �ɹ������֧: d020
2025-09-01 17:02:05,557 - INFO - �ɹ������֧: d020-0325
2025-09-01 17:02:06,161 - INFO - �ɹ������֧: d020-new
2025-09-01 17:02:06,743 - INFO - �ɹ������֧: d020-yhy
2025-09-01 17:02:07,323 - INFO - �ɹ������֧: d020_yhd
2025-09-01 17:02:07,979 - INFO - �ɹ������֧: d027
2025-09-01 17:02:08,677 - INFO - �ɹ������֧: develop
2025-09-01 17:02:09,362 - INFO - �ɹ������֧: feature-018
2025-09-01 17:02:10,188 - INFO - �ɹ������֧: feature-091
2025-09-01 17:02:10,875 - INFO - �ɹ������֧: feature-092
2025-09-01 17:02:11,825 - INFO - �ɹ������֧: feature-D017
2025-09-01 17:02:13,019 - INFO - �ɹ������֧: feature-saas
2025-09-01 17:02:13,533 - INFO - �ɹ������֧: feature-saas-0.0.1
2025-09-01 17:02:14,112 - INFO - �ɹ������֧: fix_word
2025-09-01 17:02:14,690 - INFO - �ɹ������֧: getMeetingConfig
2025-09-01 17:02:15,295 - INFO - �ɹ������֧: hotfixed
2025-09-01 17:02:15,896 - INFO - �ɹ������֧: jstcc-saas
2025-09-01 17:02:16,586 - INFO - �ɹ������֧: loading
2025-09-01 17:02:17,168 - INFO - �ɹ������֧: p4
2025-09-01 17:02:18,081 - INFO - �ɹ������֧: qingyuedu
2025-09-01 17:02:19,342 - INFO - �ɹ������֧: realse
2025-09-01 17:02:20,468 - INFO - �ɹ������֧: realse-1.0.19
2025-09-01 17:02:21,519 - INFO - �ɹ������֧: realse-1.0.19-1127
2025-09-01 17:02:22,305 - INFO - �ɹ������֧: realse-1.0.19-jinjifabu
2025-09-01 17:02:23,477 - INFO - �ɹ������֧: realse_bug_zhr
2025-09-01 17:02:24,852 - INFO - �ɹ������֧: realse_fixed
2025-09-01 17:02:26,280 - INFO - �ɹ������֧: realse_i18n_yhd
2025-09-01 17:02:27,693 - INFO - �ɹ������֧: room20250702
2025-09-01 17:02:29,064 - INFO - �ɹ������֧: saas-0.0.1-lqz
2025-09-01 17:02:29,886 - INFO - �ɹ������֧: saas-018
2025-09-01 17:02:31,204 - INFO - �ɹ������֧: saas-v2.2.0.2
2025-09-01 17:02:33,559 - INFO - �ɹ������֧: threeStar-0816
2025-09-01 17:02:35,845 - INFO - �ɹ������֧: v-2.2.10
2025-09-01 17:02:37,612 - INFO - �ɹ������֧: v2.12.3
2025-09-01 17:02:38,430 - INFO - �ɹ������֧: v2.12.4.2-sdk
2025-09-01 17:02:39,497 - INFO - �ɹ������֧: v2.14.4.7
2025-09-01 17:02:40,382 - INFO - �ɹ������֧: v2.6.0-kb
2025-09-01 17:02:41,125 - INFO - ��ʼ��¡�ֿ�: bid-ui-agency
2025-09-01 17:03:00,619 - INFO - �ɹ���¡�ֿ�: bid-ui-agency
2025-09-01 17:03:01,999 - INFO - ���� 95 ��Զ�̷�֧: ['009-424', '109fix', '2.12.0.1', '2.12.2_room', '2.12.4-fixclound', '2.14.4.2_faban', '2.14.4.6', '2.15.1.1', '2.15.2.1', '2.15.3.1', '2025_03wps', '6yue', 'D001-saasTest', 'D009-D026-realse', 'D009-d026', 'D014', 'D014-0426', 'D015', 'D015-0621', 'D017', 'D020-326', 'D020-41', 'D023', 'D024', 'E-002', 'E-002-New', 'E-002-yhy', 'E-007-yhy', 'E-011', 'E-013', 'E-016', 'E001-E002', 'E004', 'E009', 'E013', 'E013-bond', 'E021_p3_costing', 'NERTC', 'NERTC-BACKUP', 'NERTC_1', 'OA_V1.2.0', 'P-004', 'P-004-yhy', 'P-006-2', 'P009', 'P009-lmk', 'R015', 'V2.14.4.7', 'V2.14.4.7_qingyuedu', 'V2.14.5.1', 'V2.14.8', 'V2.6.0.2', '_E-008/D-009', 'amb2', 'amb2-0513', 'amb611', 'bond-0702', 'brUpdate', 'chatroom_realse', 'conference_fixed', 'costing_fix', 'crSDK', 'd020', 'd020-ncc', 'd026', 'dev-oa-d022', 'develop', 'feature-D017', 'feature-E016P3', 'feature-saas-0.0.1', 'feature-saas-0.0.1-test', 'hotfixed', 'jstcc-saas', 'master', 'neroom', 'noticePush', 'oos-6.0', 'oss_power', 'p4', 'qingyuedu', 'realse', 'realse-1.0.22', 'realse_bug_zhr', 'realse_fixed', 'room20250702', 'saas-tenderee', 'saasv2.1.0_agency', 'test-vite', 'test2.12.5', 'threeStar-0816', 'tuikuan', 'updateSing', 'v2.12.4.2-sdk', 'v2.14.6.2', 'wenjianshangchuan']
2025-09-01 17:03:03,595 - INFO - �ɹ������֧: 009-424
2025-09-01 17:03:04,983 - INFO - �ɹ������֧: 109fix
2025-09-01 17:03:06,122 - INFO - �ɹ������֧: 2.12.0.1
2025-09-01 17:03:07,577 - INFO - �ɹ������֧: 2.12.2_room
2025-09-01 17:03:08,947 - INFO - �ɹ������֧: 2.12.4-fixclound
2025-09-01 17:03:10,239 - INFO - �ɹ������֧: 2.14.4.2_faban
2025-09-01 17:03:11,159 - INFO - �ɹ������֧: 2.14.4.6
2025-09-01 17:03:12,788 - INFO - �ɹ������֧: 2.15.1.1
2025-09-01 17:03:14,178 - INFO - �ɹ������֧: 2.15.2.1
2025-09-01 17:03:15,511 - INFO - �ɹ������֧: 2.15.3.1
2025-09-01 17:03:16,885 - INFO - �ɹ������֧: 2025_03wps
2025-09-01 17:03:18,235 - INFO - �ɹ������֧: 6yue
2025-09-01 17:03:19,569 - INFO - �ɹ������֧: D001-saasTest
2025-09-01 17:03:20,612 - INFO - �ɹ������֧: D009-D026-realse
2025-09-01 17:03:21,562 - INFO - �ɹ������֧: D009-d026
2025-09-01 17:03:22,651 - INFO - �ɹ������֧: D014
2025-09-01 17:03:23,434 - INFO - �ɹ������֧: D014-0426
2025-09-01 17:03:24,090 - INFO - �ɹ������֧: D015
2025-09-01 17:03:24,904 - INFO - �ɹ������֧: D015-0621
2025-09-01 17:03:26,118 - INFO - �ɹ������֧: D017
2025-09-01 17:03:27,024 - INFO - �ɹ������֧: D020-326
2025-09-01 17:03:27,969 - INFO - �ɹ������֧: D020-41
2025-09-01 17:03:28,707 - INFO - �ɹ������֧: D023
2025-09-01 17:03:29,494 - INFO - �ɹ������֧: D024
2025-09-01 17:03:30,316 - INFO - �ɹ������֧: E-002
2025-09-01 17:03:31,319 - INFO - �ɹ������֧: E-002-New
2025-09-01 17:03:32,324 - INFO - �ɹ������֧: E-002-yhy
2025-09-01 17:03:33,122 - INFO - �ɹ������֧: E-007-yhy
2025-09-01 17:03:34,007 - INFO - �ɹ������֧: E-011
2025-09-01 17:03:35,144 - INFO - �ɹ������֧: E-013
2025-09-01 17:03:36,480 - INFO - �ɹ������֧: E-016
2025-09-01 17:03:37,419 - INFO - �ɹ������֧: E001-E002
2025-09-01 17:03:38,139 - INFO - �ɹ������֧: E004
2025-09-01 17:03:39,173 - INFO - �ɹ������֧: E009
2025-09-01 17:03:40,538 - INFO - �ɹ������֧: E013
2025-09-01 17:03:41,455 - INFO - �ɹ������֧: E013-bond
2025-09-01 17:03:42,315 - INFO - �ɹ������֧: E021_p3_costing
2025-09-01 17:03:43,040 - INFO - �ɹ������֧: NERTC
2025-09-01 17:03:43,667 - INFO - �ɹ������֧: NERTC-BACKUP
2025-09-01 17:03:44,347 - INFO - �ɹ������֧: NERTC_1
2025-09-01 17:03:45,076 - INFO - �ɹ������֧: OA_V1.2.0
2025-09-01 17:03:45,921 - INFO - �ɹ������֧: P-004
2025-09-01 17:03:46,774 - INFO - �ɹ������֧: P-004-yhy
2025-09-01 17:03:47,454 - INFO - �ɹ������֧: P-006-2
2025-09-01 17:03:48,107 - INFO - �ɹ������֧: P009
2025-09-01 17:03:48,743 - INFO - �ɹ������֧: P009-lmk
2025-09-01 17:03:49,529 - INFO - �ɹ������֧: R015
2025-09-01 17:03:50,407 - INFO - �ɹ������֧: V2.14.4.7
2025-09-01 17:03:51,253 - INFO - �ɹ������֧: V2.14.4.7_qingyuedu
2025-09-01 17:03:52,040 - INFO - �ɹ������֧: V2.14.5.1
2025-09-01 17:03:53,172 - INFO - �ɹ������֧: V2.14.8
2025-09-01 17:03:54,601 - INFO - �ɹ������֧: V2.6.0.2
2025-09-01 17:03:55,332 - INFO - �ɹ������֧: _E-008/D-009
2025-09-01 17:03:56,074 - INFO - �ɹ������֧: amb2
2025-09-01 17:03:56,865 - INFO - �ɹ������֧: amb2-0513
2025-09-01 17:03:57,606 - INFO - �ɹ������֧: amb611
2025-09-01 17:03:58,252 - INFO - �ɹ������֧: bond-0702
2025-09-01 17:03:59,016 - INFO - �ɹ������֧: brUpdate
2025-09-01 17:03:59,752 - INFO - �ɹ������֧: chatroom_realse
2025-09-01 17:04:00,352 - INFO - �ɹ������֧: conference_fixed
2025-09-01 17:04:01,084 - INFO - �ɹ������֧: costing_fix
2025-09-01 17:04:01,935 - INFO - �ɹ������֧: crSDK
2025-09-01 17:04:02,870 - INFO - �ɹ������֧: d020
2025-09-01 17:04:03,556 - INFO - �ɹ������֧: d020-ncc
2025-09-01 17:04:04,233 - INFO - �ɹ������֧: d026
2025-09-01 17:04:04,963 - INFO - �ɹ������֧: dev-oa-d022
2025-09-01 17:04:05,880 - INFO - �ɹ������֧: develop
2025-09-01 17:04:06,741 - INFO - �ɹ������֧: feature-D017
2025-09-01 17:04:07,582 - INFO - �ɹ������֧: feature-E016P3
2025-09-01 17:04:08,528 - INFO - �ɹ������֧: feature-saas-0.0.1
2025-09-01 17:04:09,212 - INFO - �ɹ������֧: feature-saas-0.0.1-test
2025-09-01 17:04:10,511 - INFO - �ɹ������֧: hotfixed
2025-09-01 17:04:12,069 - INFO - �ɹ������֧: jstcc-saas
2025-09-01 17:04:13,036 - INFO - �ɹ������֧: neroom
2025-09-01 17:04:14,132 - INFO - �ɹ������֧: noticePush
2025-09-01 17:04:14,868 - INFO - �ɹ������֧: oos-6.0
2025-09-01 17:04:15,684 - INFO - �ɹ������֧: oss_power
2025-09-01 17:04:16,427 - INFO - �ɹ������֧: p4
2025-09-01 17:04:17,230 - INFO - �ɹ������֧: qingyuedu
2025-09-01 17:04:18,303 - INFO - �ɹ������֧: realse
2025-09-01 17:04:19,653 - INFO - �ɹ������֧: realse-1.0.22
2025-09-01 17:04:20,562 - INFO - �ɹ������֧: realse_bug_zhr
2025-09-01 17:04:21,318 - INFO - �ɹ������֧: realse_fixed
2025-09-01 17:04:21,884 - INFO - �ɹ������֧: room20250702
2025-09-01 17:04:22,744 - INFO - �ɹ������֧: saas-tenderee
2025-09-01 17:04:23,488 - INFO - �ɹ������֧: saasv2.1.0_agency
2025-09-01 17:04:24,171 - INFO - �ɹ������֧: test-vite
2025-09-01 17:04:25,100 - INFO - �ɹ������֧: test2.12.5
2025-09-01 17:04:25,819 - INFO - �ɹ������֧: threeStar-0816
2025-09-01 17:04:27,093 - INFO - �ɹ������֧: tuikuan
2025-09-01 17:04:28,003 - INFO - �ɹ������֧: updateSing
2025-09-01 17:04:29,257 - INFO - �ɹ������֧: v2.12.4.2-sdk
2025-09-01 17:04:30,065 - INFO - �ɹ������֧: v2.14.6.2
2025-09-01 17:04:30,760 - INFO - �ɹ������֧: wenjianshangchuan
2025-09-01 17:04:31,576 - INFO - ��ʼ��¡�ֿ�: bid-common-security
2025-09-01 17:04:33,555 - INFO - �ɹ���¡�ֿ�: bid-common-security
2025-09-01 17:04:34,110 - INFO - ���� 2 ��Զ�̷�֧: ['develop', 'master']
2025-09-01 17:04:34,729 - INFO - �ɹ������֧: develop
2025-09-01 17:04:35,306 - INFO - ��ʼ��¡�ֿ�: bid-openapi-client
2025-09-01 17:04:37,722 - INFO - �ɹ���¡�ֿ�: bid-openapi-client
2025-09-01 17:04:38,238 - INFO - ���� 27 ��Զ�̷�֧: ['1.0.4', '2.13.1-fix', 'D-015', 'D-020', 'E-002', 'E002-ncc', 'E007p2', 'E021', 'E021-push', 'P-009', 'develop', 'develop-20241028cx', 'develop-cx', 'feature-E009', 'feature-E013', 'feature-E016', 'feature-R015', 'master', 'p2301', 'realse', 'realse-D017-V2', 'realse-P006', 'realse-file-push', 'realse-mager-push', 'sy-bug', 'v2.1.0', 'v2.13.1.9']
2025-09-01 17:04:38,820 - INFO - �ɹ������֧: 1.0.4
2025-09-01 17:04:39,362 - INFO - �ɹ������֧: 2.13.1-fix
2025-09-01 17:04:39,873 - INFO - �ɹ������֧: D-015
2025-09-01 17:04:40,432 - INFO - �ɹ������֧: D-020
2025-09-01 17:04:40,975 - INFO - �ɹ������֧: E-002
2025-09-01 17:04:41,503 - INFO - �ɹ������֧: E002-ncc
2025-09-01 17:04:42,109 - INFO - �ɹ������֧: E007p2
2025-09-01 17:04:43,363 - INFO - �ɹ������֧: E021
2025-09-01 17:04:43,935 - INFO - �ɹ������֧: E021-push
2025-09-01 17:04:44,529 - INFO - �ɹ������֧: P-009
2025-09-01 17:04:45,140 - INFO - �ɹ������֧: develop
2025-09-01 17:04:45,709 - INFO - �ɹ������֧: develop-20241028cx
2025-09-01 17:04:46,238 - INFO - �ɹ������֧: develop-cx
2025-09-01 17:04:46,777 - INFO - �ɹ������֧: feature-E009
2025-09-01 17:04:47,316 - INFO - �ɹ������֧: feature-E013
2025-09-01 17:04:47,809 - INFO - �ɹ������֧: feature-E016
2025-09-01 17:04:48,350 - INFO - �ɹ������֧: feature-R015
2025-09-01 17:04:48,852 - INFO - �ɹ������֧: p2301
2025-09-01 17:04:49,400 - INFO - �ɹ������֧: realse
2025-09-01 17:04:49,925 - INFO - �ɹ������֧: realse-D017-V2
2025-09-01 17:04:50,461 - INFO - �ɹ������֧: realse-P006
2025-09-01 17:04:51,021 - INFO - �ɹ������֧: realse-file-push
2025-09-01 17:04:51,571 - INFO - �ɹ������֧: realse-mager-push
2025-09-01 17:04:52,091 - INFO - �ɹ������֧: sy-bug
2025-09-01 17:04:52,664 - INFO - �ɹ������֧: v2.1.0
2025-09-01 17:04:53,231 - INFO - �ɹ������֧: v2.13.1.9
2025-09-01 17:04:53,766 - INFO - ��ʼ��¡�ֿ�: bid-common-datascope
2025-09-01 17:04:55,700 - INFO - �ɹ���¡�ֿ�: bid-common-datascope
2025-09-01 17:04:56,235 - INFO - ���� 6 ��Զ�̷�֧: ['2.8.1', 'develop', 'develop-wh', 'develop_pr', 'master', 'realse']
2025-09-01 17:04:56,790 - INFO - �ɹ������֧: 2.8.1
2025-09-01 17:04:57,519 - INFO - �ɹ������֧: develop
2025-09-01 17:04:58,038 - INFO - �ɹ������֧: develop-wh
2025-09-01 17:04:58,548 - INFO - �ɹ������֧: develop_pr
2025-09-01 17:04:59,059 - INFO - �ɹ������֧: realse
2025-09-01 17:04:59,596 - INFO - ��ʼ��¡�ֿ�: bid-common-core
2025-09-01 17:05:02,714 - INFO - �ɹ���¡�ֿ�: bid-common-core
2025-09-01 17:05:03,383 - INFO - ���� 8 ��Զ�̷�֧: ['E-004', 'develop', 'master', 'oss-token', 'realse', 'realse-20250506', 'realse-repeatsubmittime', 'realse-sessionToken']
2025-09-01 17:05:04,080 - INFO - �ɹ������֧: E-004
2025-09-01 17:05:04,710 - INFO - �ɹ������֧: develop
2025-09-01 17:05:05,359 - INFO - �ɹ������֧: oss-token
2025-09-01 17:05:05,999 - INFO - �ɹ������֧: realse
2025-09-01 17:05:06,655 - INFO - �ɹ������֧: realse-20250506
2025-09-01 17:05:07,170 - INFO - �ɹ������֧: realse-repeatsubmittime
2025-09-01 17:05:07,796 - INFO - �ɹ������֧: realse-sessionToken
2025-09-01 17:05:08,343 - INFO - ��ʼ��¡�ֿ�: bid-cws-client
2025-09-01 17:05:10,279 - INFO - �ɹ���¡�ֿ�: bid-cws-client
2025-09-01 17:05:10,772 - INFO - ���� 1 ��Զ�̷�֧: ['master']
2025-09-01 17:05:11,294 - INFO - ��ʼ��¡�ֿ�: bid-anemone-wechat
2025-09-01 17:05:13,152 - INFO - �ɹ���¡�ֿ�: bid-anemone-wechat
2025-09-01 17:05:13,699 - INFO - ���� 1 ��Զ�̷�֧: ['master']
2025-09-01 17:05:14,267 - INFO - ��ʼ��¡�ֿ�: bid-iris
2025-09-01 17:05:16,224 - INFO - �ɹ���¡�ֿ�: bid-iris
2025-09-01 17:05:16,741 - INFO - ���� 1 ��Զ�̷�֧: ['master']
2025-09-01 17:05:17,245 - INFO - ��ʼ��¡�ֿ�: bid-crocus
2025-09-01 17:05:19,560 - INFO - �ɹ���¡�ֿ�: bid-crocus
2025-09-01 17:05:20,066 - INFO - ���� 74 ��Զ�̷�֧: ['082-expert', '2.10.0', '2.11.0', '2.11.0-invoiceNo', '2.12.0', '2.12.0.1', '2.12.0.1-xt', '2.12.2', '2.12.4-bug', '2.13.1-fix', '6yue', 'D-020', 'D024-fix', 'E-002', 'E-021-P2', 'E002-ncc', 'E007p2', 'E021', 'E021-push', 'E021-push-0219', 'V2.12.3', 'V2.13.0', 'develop', 'develop-cws', 'develop-cx', 'develop-multi-dept', 'feature-20241028cx', 'feature-D015', 'feature-D020bugfix', 'feature-E013', 'feature-E016', 'feature-E021', 'feature-E024', 'feature-R015', 'future-009', 'future-2.2.7', 'future-2.2.8', 'future-D017', 'future-D023', 'future-D024', 'future-E023', 'master', 'p2301', 'ppe', 'realse', 'realse-0312', 'realse-0713', 'realse-0724', 'realse-0829', 'realse-1019', 'realse-25-02-13', 'realse-D009bugfix20241122', 'realse-D017', 'realse-P006', 'realse-bug', 'realse-cws', 'realse-encryption', 'realse-file-push', 'realse-online-fix', 'realse-retry', 'realse_push', 'saas', 'sy-bug', 'v2.1.0', 'v2.12.4', 'v2.13.1.9', 'v2.2.0', 'v2.3.6_0118', 'v2.6.0', 'v2.6.0-fix', 'v2.6.1', 'v2.6.1-0314', 'v2.6.1-fix', 'v2.6.2']
2025-09-01 17:05:20,654 - INFO - �ɹ������֧: 082-expert
2025-09-01 17:05:21,256 - INFO - �ɹ������֧: 2.10.0
2025-09-01 17:05:21,780 - INFO - �ɹ������֧: 2.11.0
2025-09-01 17:05:22,314 - INFO - �ɹ������֧: 2.11.0-invoiceNo
2025-09-01 17:05:22,843 - INFO - �ɹ������֧: 2.12.0
2025-09-01 17:05:23,368 - INFO - �ɹ������֧: 2.12.0.1
2025-09-01 17:05:23,883 - INFO - �ɹ������֧: 2.12.0.1-xt
2025-09-01 17:05:24,469 - INFO - �ɹ������֧: 2.12.2
2025-09-01 17:05:25,175 - INFO - �ɹ������֧: 2.12.4-bug
2025-09-01 17:05:25,721 - INFO - �ɹ������֧: 2.13.1-fix
2025-09-01 17:05:26,310 - INFO - �ɹ������֧: 6yue
2025-09-01 17:05:26,865 - INFO - �ɹ������֧: D-020
2025-09-01 17:05:27,427 - INFO - �ɹ������֧: D024-fix
2025-09-01 17:05:27,994 - INFO - �ɹ������֧: E-002
2025-09-01 17:05:28,538 - INFO - �ɹ������֧: E-021-P2
2025-09-01 17:05:29,031 - INFO - �ɹ������֧: E002-ncc
2025-09-01 17:05:29,589 - INFO - �ɹ������֧: E007p2
2025-09-01 17:05:30,203 - INFO - �ɹ������֧: E021
2025-09-01 17:05:30,834 - INFO - �ɹ������֧: E021-push
2025-09-01 17:05:31,419 - INFO - �ɹ������֧: E021-push-0219
2025-09-01 17:05:32,069 - INFO - �ɹ������֧: V2.12.3
2025-09-01 17:05:32,716 - INFO - �ɹ������֧: V2.13.0
2025-09-01 17:05:33,344 - INFO - �ɹ������֧: develop
2025-09-01 17:05:33,867 - INFO - �ɹ������֧: develop-cws
2025-09-01 17:05:34,387 - INFO - �ɹ������֧: develop-cx
2025-09-01 17:05:34,922 - INFO - �ɹ������֧: develop-multi-dept
2025-09-01 17:05:35,470 - INFO - �ɹ������֧: feature-20241028cx
2025-09-01 17:05:36,003 - INFO - �ɹ������֧: feature-D015
2025-09-01 17:05:36,552 - INFO - �ɹ������֧: feature-D020bugfix
2025-09-01 17:05:37,106 - INFO - �ɹ������֧: feature-E013
2025-09-01 17:05:37,650 - INFO - �ɹ������֧: feature-E016
2025-09-01 17:05:38,285 - INFO - �ɹ������֧: feature-E021
2025-09-01 17:05:39,161 - INFO - �ɹ������֧: feature-E024
2025-09-01 17:05:39,922 - INFO - �ɹ������֧: feature-R015
2025-09-01 17:05:40,820 - INFO - �ɹ������֧: future-009
2025-09-01 17:05:41,618 - INFO - �ɹ������֧: future-2.2.7
2025-09-01 17:05:42,274 - INFO - �ɹ������֧: future-2.2.8
2025-09-01 17:05:43,069 - INFO - �ɹ������֧: future-D017
2025-09-01 17:05:43,643 - INFO - �ɹ������֧: future-D023
2025-09-01 17:05:44,227 - INFO - �ɹ������֧: future-D024
2025-09-01 17:05:44,747 - INFO - �ɹ������֧: future-E023
2025-09-01 17:05:45,273 - INFO - �ɹ������֧: p2301
2025-09-01 17:05:45,888 - INFO - �ɹ������֧: ppe
2025-09-01 17:05:47,050 - INFO - �ɹ������֧: realse
2025-09-01 17:05:48,127 - INFO - �ɹ������֧: realse-0312
2025-09-01 17:05:48,735 - INFO - �ɹ������֧: realse-0713
2025-09-01 17:05:49,301 - INFO - �ɹ������֧: realse-0724
2025-09-01 17:05:49,845 - INFO - �ɹ������֧: realse-0829
2025-09-01 17:05:50,508 - INFO - �ɹ������֧: realse-1019
2025-09-01 17:05:51,658 - INFO - �ɹ������֧: realse-25-02-13
2025-09-01 17:05:52,552 - INFO - �ɹ������֧: realse-D009bugfix20241122
2025-09-01 17:05:53,246 - INFO - �ɹ������֧: realse-D017
2025-09-01 17:05:53,843 - INFO - �ɹ������֧: realse-P006
2025-09-01 17:05:54,835 - INFO - �ɹ������֧: realse-bug
2025-09-01 17:05:55,771 - INFO - �ɹ������֧: realse-cws
2025-09-01 17:05:56,631 - INFO - �ɹ������֧: realse-encryption
2025-09-01 17:05:57,173 - INFO - �ɹ������֧: realse-file-push
2025-09-01 17:05:57,749 - INFO - �ɹ������֧: realse-online-fix
2025-09-01 17:05:58,778 - INFO - �ɹ������֧: realse-retry
2025-09-01 17:05:59,854 - INFO - �ɹ������֧: realse_push
2025-09-01 17:06:01,004 - INFO - �ɹ������֧: saas
2025-09-01 17:06:01,601 - INFO - �ɹ������֧: sy-bug
2025-09-01 17:06:02,405 - INFO - �ɹ������֧: v2.1.0
2025-09-01 17:06:03,503 - INFO - �ɹ������֧: v2.12.4
2025-09-01 17:06:04,623 - INFO - �ɹ������֧: v2.13.1.9
2025-09-01 17:06:05,757 - INFO - �ɹ������֧: v2.2.0
2025-09-01 17:06:06,985 - INFO - �ɹ������֧: v2.3.6_0118
2025-09-01 17:06:08,395 - INFO - �ɹ������֧: v2.6.0
2025-09-01 17:06:09,564 - INFO - �ɹ������֧: v2.6.0-fix
2025-09-01 17:06:10,683 - INFO - �ɹ������֧: v2.6.1
2025-09-01 17:06:11,398 - INFO - �ɹ������֧: v2.6.1-0314
2025-09-01 17:06:12,116 - INFO - �ɹ������֧: v2.6.1-fix
2025-09-01 17:06:13,358 - INFO - �ɹ������֧: v2.6.2
2025-09-01 17:06:14,735 - INFO - ��ʼ��¡�ֿ�: bid-expert-client
2025-09-01 17:06:14,742 - WARNING - Ŀ¼�Ѵ��ڣ�����: cloned_repos\bid-expert-client
2025-09-01 17:06:14,743 - INFO - ��ʼ��¡�ֿ�: bid-api
2025-09-01 17:06:14,744 - WARNING - Ŀ¼�Ѵ��ڣ�����: cloned_repos\bid-api
2025-09-01 17:06:14,744 - INFO - ��ʼ��¡�ֿ�: bid-freesia
2025-09-01 17:06:17,294 - INFO - �ɹ���¡�ֿ�: bid-freesia
2025-09-01 17:06:17,924 - INFO - ���� 8 ��Զ�̷�֧: ['E021', 'develop', 'develop-timeout-fix', 'develop-wh', 'master', 'realse', 'realse-1.0.4', 'realse-1.0.5']
2025-09-01 17:06:18,707 - INFO - �ɹ������֧: E021
2025-09-01 17:06:19,334 - INFO - �ɹ������֧: develop
2025-09-01 17:06:20,387 - INFO - �ɹ������֧: develop-timeout-fix
2025-09-01 17:06:21,283 - INFO - �ɹ������֧: develop-wh
2025-09-01 17:06:22,372 - INFO - �ɹ������֧: realse
2025-09-01 17:06:23,299 - INFO - �ɹ������֧: realse-1.0.4
2025-09-01 17:06:23,880 - INFO - �ɹ������֧: realse-1.0.5
2025-09-01 17:06:24,985 - INFO - ��ʼ��¡�ֿ�: licenseDemo
2025-09-01 17:06:29,070 - INFO - �ɹ���¡�ֿ�: licenseDemo
2025-09-01 17:06:30,130 - INFO - ���� 1 ��Զ�̷�֧: ['master']
2025-09-01 17:06:31,247 - INFO - ��ʼ��¡�ֿ�: generatorGUI
2025-09-01 17:06:34,966 - INFO - �ɹ���¡�ֿ�: generatorGUI
2025-09-01 17:06:36,019 - INFO - ���� 1 ��Զ�̷�֧: ['master']
2025-09-01 17:06:37,031 - INFO - ��ʼ��¡�ֿ�: generatorAPI
