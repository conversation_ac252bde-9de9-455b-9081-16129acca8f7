<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hzw.ssm.applets.dao.TechnicalMapper">

<select id="queryPagetechnical" parameterType="Technical" resultType="Technical">
select id,
       code,
       technical_tital_name technicalTitalName,
       is_del isDel,
       expert_type expertType,
       create_id createId,
         (select user_name from ts_user where user_id= t.create_id) createName,
       create_time createTime,
       modify_id modifyId,
        (select user_name from ts_user where user_id= t.modify_id)modifyName,
       modify_time modifyTime
  from t_technical_tital t
   <where>
   	<if test="technicalTitalName != null and technicalTitalName != '' ">
   		AND technical_tital_name LIKE '%' || #{technicalTitalName} || '%'
   	</if>
   	<if test="isDel != null and isDel != ''">
   	 and is_del = #{isDel}
   	</if>
   	<if test="expertType != null and expertType != '' ">
   		and expert_type = #{expertType}
   	</if>
   </where>
   <if test="reorder !=null and reorder !=''">
				order by ${reorder}
			</if>
</select>

<select id="getTechnical" parameterType="Technical" resultType="Technical">
select id,
       code,
       technical_tital_name technicalTitalName,
       is_del isDel,
       expert_type expertType,
       create_id createId,
         (select user_name from ts_user where user_id= t.create_id) createName,
       create_time createTime,
       modify_id modifyId,
        (select user_name from ts_user where user_id= t.modify_id)modifyName,
       modify_time modifyTime
  from t_technical_tital t
  where t.id = #{id}
</select>

<select id="getMaxTechnical" resultType="int">
select max(to_number(id)) from t_technical_tital
</select>

<update id="updateTechnicalType" parameterType="String">
	update t_technical_tital set is_del = #{type} where id= #{id}
</update>
<insert id="addTechnical" parameterType="Technical">
	insert into t_technical_tital
  (id,
   code,
   technical_tital_name,
   is_del,
   expert_type,
   create_id,
   create_time,
   modify_id,
   modify_time)
values
  (
   #{id},
   #{code},
   #{technicalTitalName},
   #{isDel},
   #{expertType},
   #{createId},
   #{createTime},
   #{modifyId},
   #{modifyTime}
  )
</insert>

<update id="updateTechnical" parameterType="Technical">
update t_technical_tital set 
technical_tital_name = #{technicalTitalName} ,
expert_type = #{expertType},
modify_id = #{modifyId},
modify_time = #{modifyTime}
where id= #{id}
</update>
<select id="checkedTechnicalName" resultType="int" parameterType="String">
	select count(1) from t_technical_tital where technical_tital_name = #{technicalName}
</select>

<select id="selectTechnicUser" resultType="int" parameterType="String">
select count(1) from t_title_info tt where tt.title_id =(select code from t_technical_tital where id= #{id}) and tt.delete_flag=0
</select>
</mapper>