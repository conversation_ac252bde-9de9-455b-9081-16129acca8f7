package com.hzw.sunflower.controller.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2021/7/7 15:57
 * @description：承诺文件请求体
 * @version: 1.0
 */
@Data
public class PromiseFileReq {

    @ApiModelProperty(value = "文件名")
    private String fileName;

    @ApiModelProperty(value = "oss文件Id")
    private Long fileOssId;

    @ApiModelProperty(value = "项目ID")
    private Long projectId;

    @ApiModelProperty(value = "承诺文件id")
    private Long id;

    @ApiModelProperty(value = "文件类型 1.评委宣誓词 2.协评人员宣誓词 3.健康情况说明 4.其他")
    private Integer fileType;
}
