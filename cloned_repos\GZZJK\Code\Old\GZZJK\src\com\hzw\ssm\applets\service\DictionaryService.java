package com.hzw.ssm.applets.service;

import com.hzw.ssm.applets.dao.DictionaryMapper;
import com.hzw.ssm.applets.entity.Dictionary;
import com.hzw.ssm.applets.utils.ResultJson;
import com.hzw.ssm.fw.base.BaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021-02-02 14:46
 * @Version 1.0
 */
@Service
public class DictionaryService extends BaseService {

    @Autowired
    private DictionaryMapper dictionaryMapper;

    public ResultJson queryByType(Dictionary dictionary) {
        List<Dictionary> dictionaries = dictionaryMapper.queryByType(dictionary);
        return new ResultJson(dictionaries);
    }

    public ResultJson queryTitle() {
        List<Dictionary> list = dictionaryMapper.queryTitle();
        return new ResultJson(list);
    }
}
