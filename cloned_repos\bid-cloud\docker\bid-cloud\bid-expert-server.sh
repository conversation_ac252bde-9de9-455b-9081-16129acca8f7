#!/bin/sh
#cp TEMPLATE FILE
docker stop bid-expert-server || true
docker rm bid-expert-server || true
docker rmi registry.cn-hangzhou.aliyuncs.com/hzw-jtcc/jtcc:bid-expert-server-1.0.0 || true
docker pull registry.cn-hangzhou.aliyuncs.com/hzw-jtcc/jtcc:bid-expert-server-1.0.0
docker run -itd --net=host  --log-opt max-size=100m --log-opt max-file=3  --name bid-expert-server -v /home/<USER>/plan1/bid-cloud/config/bid-expert-server:/hzw/expert/config -v /data/log:/hzw/expert/logs  registry.cn-hangzhou.aliyuncs.com/hzw-jtcc/jtcc:bid-expert-server-1.0.0
