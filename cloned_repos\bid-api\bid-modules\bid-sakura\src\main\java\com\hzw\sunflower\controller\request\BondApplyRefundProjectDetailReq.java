package com.hzw.sunflower.controller.request;

import com.hzw.sunflower.common.BaseCondition;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "异常退款申请(供应商)详情")
public class BondApplyRefundProjectDetailReq extends BaseCondition {

    @ApiModelProperty(value = "保证金申请退还id")
    private Long applyId;

    @ApiModelProperty(value = "关键字搜索（名称/编号）")
    private String keyWords;

    @ApiModelProperty(value = "类型 0全部，1已参与，2未参与")
    private Integer status;

    @ApiModelProperty(value = "公司id")
    private Long companyId;

}
