package com.hzw.sunflower.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.controller.request.ReviewSignReq;
import com.hzw.sunflower.entity.OssFile;
import com.hzw.sunflower.entity.ReviewSign;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @date ：Created in 2023/9/11 11:31
 * @description：
 * @version: 1.0
 */
public interface ReviewSignService extends IService<ReviewSign> {

    /**
     * 评审文件签章
     * @param req
     * @param file
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    Boolean signForReview(ReviewSignReq req, MultipartFile file);

    /**
     * 评审报告签章
     * @param req
     * @param file
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    Boolean signForReport(ReviewSignReq req, MultipartFile file);

    /**
     * 签章文件上传
     * @param file
     * @return
     */
    Long uploadSignFile(MultipartFile file);

    /**
     * 下载oss文件
     * @param fileOssId
     * @param response
     */
    void getOssFile(Long fileOssId, HttpServletResponse response);

    /**
     * 获取签章进度
     * @param id
     * @param fileType
     * @return
     */
    Result<OssFile> getSignProcess(Long id, Integer fileType);

    /**
     * 获取签章文件
     * @param reviewId
     * @param fileType
     * @return
     */
    Result<Long> querySignFile(Long reviewId, Integer fileType);
}
