<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!-- 项目委托关系人表 (t_project_entrust_user) -->
<mapper namespace="com.hzw.sunflower.dao.ProjectEntrustUserMapper">

    <!--查询委托人-->
    <select id="queryPrincipalByIds" resultType="java.util.Map">
       <!-- SELECT
            u.company_id companyId,
            u.user_name userName,
            u.user_phone userPhone
        FROM
            t_project_entrust_user eu
            INNER JOIN t_project p ON p.id = eu.project_id
            INNER JOIN t_user u ON u.id = eu.user_id
        WHERE
            eu.is_delete = 0
            AND eu.type = #{type}
            AND p.id = #{id}
            -->

            SELECT
                DISTINCT
                u.company_id companyId,
                u.user_name userName,
                u.user_phone userPhone
            FROM
                t_project_entrust_user eu
                LEFT JOIN r_user_department rud on eu.user_id = rud.user_id  and eu.department_id = rud.department_id
                INNER JOIN t_project p ON p.id = eu.project_id
                INNER JOIN t_user u ON u.id = eu.user_id
            WHERE
                eu.is_delete = 0
                and p.is_delete = 0
                and u.is_delete = 0
                AND eu.type = #{type}
                AND p.id = #{id}
    </select>

    <!-- 根据项目ID  用户ID 获取信息-->
    <select id="getEntrustUser" resultType="com.hzw.sunflower.entity.ProjectEntrustUser">
        <!-- SELECT
            *
        FROM
            t_project_entrust_user t
        WHERE
            t.user_id = #{userId}
        AND t.project_id = #{projectId}
        AND t.is_delete = 0
        AND t.type = 1   -->

        SELECT
        DISTINCT
        peu.*
        FROM
        t_project_entrust_user peu
        LEFT JOIN r_user_department rud on peu.user_id = rud.user_id
        and  peu.department_id = rud.department_id
        WHERE
        peu.is_delete = 0
        and rud.is_delete = 0
        and peu.project_id = #{projectId}
        <if test="userId != null">
            AND peu.user_id = #{userId}
        </if>
        <if test="deptId != null">
            AND peu.department_id = #{deptId}
        </if>
        and peu.type = 1
    </select>
    <select id="getEntrustUserByProjectIdAndType" resultType="com.hzw.sunflower.dto.AgentUserDTO">
        select
            DISTINCT
            eu.company_id companyId,
             eu.source,
			tc.company_name companyName,
            u.user_name userName,
            u.id userId,
            ui.user_phone userPhone,
			eu.project_id,
			eu.department_id,
			p.upload_file_id uploadFileId,
            ptp.email,
            ptp.is_admin,
            ptp.role_name
        from t_project_entrust_user  eu
        LEFT JOIN r_user_department rud on eu.user_id = rud.user_id  and eu.department_id = rud.department_id
        LEFT JOIN t_user u ON u.id = eu.user_id
        LEFT JOIN t_user_identity ui ON u.id = ui.user_id
<!--        <if test="type == 0">
            and ui.identity = 1
        </if>
        <if test="type == 1">
            and ui.identity = 2
        </if> -->
        LEFT JOIN t_company tc ON tc.id = eu.company_id
        LEFT JOIN t_project p ON p.id = eu.project_id
        LEFT JOIN t_project_tenderee_power ptp ON p.id = ptp.project_id and ptp.user_id = eu.user_id
        where
         eu.is_delete = 0
        <if test="type == 0">
         AND ptp.is_delete = 0
        </if>
        and u.is_delete = 0
        and ui.is_delete = 0
        and tc.is_delete = 0
        and p.is_delete = 0
         and eu.project_id = #{projectId}
         and eu.type = #{type}
    </select>

    <select id="getOldEntrustUserId" resultType="com.hzw.sunflower.entity.ProjectEntrustUser">
        SELECT
	        peu.*
        FROM
	        t_project_entrust_user peu
        WHERE
            peu.is_delete = 0
	        and  peu.id = ( SELECT MIN( eu.id ) FROM t_project_entrust_user eu
	                        LEFT JOIN r_user_department rud on eu.user_id = rud.user_id  and eu.department_id = rud.department_id   and eu.department_id = rud.department_id
	                        WHERE eu.project_id = #{projectId} AND eu.type = 1 );
    </select>

    <update id="updateProjectEntrustUserById">
        update t_project_entrust_user set user_id = #{userId},updated_user_id = #{userId} where id = #{id}
    </update>

    <select id="selectEntrustUserAndDepartment" resultType="com.hzw.sunflower.controller.project.response.ProjectEntrustUserVo">
        SELECT
            t1.*,
            t2.department_id,
            t3.user_name,
            t4.department_name
        FROM
            t_project_entrust_user t1
                LEFT JOIN r_user_department  t2 ON t1.user_id = t2.user_id  and t1.department_id = rud.department_id
                LEFT JOIN t_user t3 on  t1.user_id = t3.id
                LEFT JOIN t_department t4 on t2.department_id = t4.id
        where
            t1.is_delete = 0
            and t2.is_delete = 0
            and t3.is_delete = 0
            and t4.is_delete = 0
          and t1.project_id = #{projectId}
          and t1.type = 1 and t1.is_delete = 0
    </select>
</mapper>