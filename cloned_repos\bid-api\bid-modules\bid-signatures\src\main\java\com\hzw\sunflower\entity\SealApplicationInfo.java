package com.hzw.sunflower.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.hzw.sunflower.common.BaseBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
* Created by pj on 2023/11/03
*/
@ApiModel("t_seal_application_info表")
@TableName("t_seal_application_info")
@Data
public class SealApplicationInfo extends BaseBean implements Serializable {
    private Long id;

    /**
     * 申请id
     *
     * @mbg.generated
     */
    @ApiModelProperty("申请id")
    private Long applyId;

    /**
     * 用印方式（1.电子章 2.实体章）
     *
     * @mbg.generated
     */
    @ApiModelProperty("用印方式（1.电子章 2.实体章）")
    private Integer sealType;

    @ApiModelProperty("处理状态 1 未处理 2已处理")
    private Integer processingProgress;



    /**
     * 盖章人
     *
     * @mbg.generated
     */
    @ApiModelProperty("盖章人")
    private String sealedUser;

    /**
     * 盖章人id
     *
     * @mbg.generated
     */
    @ApiModelProperty("盖章人id")
    private Long sealedUserId;


    /**
     * 盖章人时间
     *
     * @mbg.generated
     */
    @ApiModelProperty("盖章人时间")
    private Date sealedTime;


    /**
     * 盖章处理
     *
     * @mbg.generated
     */
    @ApiModelProperty("盖章处理")
    private String handlingSituation;


    private static final long serialVersionUID = 1L;
}