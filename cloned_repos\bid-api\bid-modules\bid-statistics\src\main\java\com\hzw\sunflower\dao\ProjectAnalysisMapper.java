package com.hzw.sunflower.dao;

import com.hzw.sunflower.controller.response.ProjectIndustryDistributionVo;
import com.hzw.sunflower.entity.*;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ProjectAnalysisMapper {
    List<ProjectAnalysis> queryProjectAnalysisList(@Param("startDate") String startDate, @Param("endDate") String endDate);

    ProjectAnalysis queryAll(@Param("startDate") String startDate, @Param("endDate") String endDate);

    List<ProjectBidSection> queryProjectBidSectionList(@Param("startDate") String startDate, @Param("endDate") String endDate);

    List<ProjectAnalysisArea> queryByAreaGroup(@Param("startDate") String startDate, @Param("endDate") String endDate);

    List<SectionExpertSpecialty> queryExpertSpecialtyBySectionId(Long id);

    Dictionary querySecondClassByThree(Long id);
    Dictionary querySecondClassByFour(Long id);
    Dictionary queryFirstClass(Long id);

    List<DepartProject> queryProjectByDepartment(@Param("startDate") String startDate, @Param("endDate") String endDate);

    List<ProjectInfo> queryProjectInfoByDate(@Param("startDate") String startDate, @Param("endDate") String endDate);

    List<LargeProject> queryLargeProjectList(@Param("startDate") String startDate, @Param("endDate") String endDate);



    List<ProjectIndustryDistributionVo> selectProjectIndustryListForUpdate(@Param("nowParameter") String nowParameter);


    List<ProjectAnalysis> queryProjectIndustryDistributionList(@Param("startDate") String startDate, @Param("endDate") String endDate);

    List<ProjectAnalysisArea> queryProjectListWithArea(@Param("startDate") String startDate, @Param("endDate") String endDate);

    ProjectAnalysis  queryAllForArea(@Param("startDate") String startDate, @Param("endDate") String endDate);

    List<ProjectAnalysis> statisticalIndustryDistribution(@Param("startDate") String startDate, @Param("endDate") String endDate);

    List<SectionSpecialty> statisticalIndustryDistributionNew(@Param("startDate") String startDate, @Param("endDate") String endDate);

    List<CollectInfo> collectExpertList(@Param("startDate") String startDate, @Param("endDate") String endDate);

    Industry queryParentIndustryById(@Param("industryId") Long industryId);

    List<ProjectAnalysisArea> queryProjectListWithAreaNew(@Param("startDate") String startDate, @Param("endDate") String endDate);

    ProjectAnalysisArea queryAllAreaNew (@Param("startDate") String startDate, @Param("endDate") String endDate);

    List<DepartProject> queryProjectByDepartmentNew(@Param("startDate") String startDate, @Param("endDate") String endDate,@Param("lastYearStartDate") String lastYearStartDate, @Param("lastYearEndDate") String lastYearEndDate);

    List<LargeProject> queryLargeProjectListNew(@Param("startDate") String startDate, @Param("endDate") String endDate);
}
