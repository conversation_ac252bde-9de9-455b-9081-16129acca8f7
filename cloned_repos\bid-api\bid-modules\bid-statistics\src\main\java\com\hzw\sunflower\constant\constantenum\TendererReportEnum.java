package com.hzw.sunflower.constant.constantenum;

/**
 * <AUTHOR>
 */

public enum TendererReportEnum {

    ALL(1,"总数"),
    PROGRESSING(2,"进行中"),
    OVER(3,"完结"),
    OPEN_BID(1,"开标"),
    EVALUATE_BID(2,"评标"),
    ;

    private Integer type;
    private String desc;

    TendererReportEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
