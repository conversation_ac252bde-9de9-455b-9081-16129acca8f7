(function(){const o=document.createElement("link").relList;if(o&&o.supports&&o.supports("modulepreload"))return;for(const e of document.querySelectorAll('link[rel="modulepreload"]'))s(e);new MutationObserver(e=>{for(const t of e)if(t.type==="childList")for(const l of t.addedNodes)l.tagName==="LINK"&&l.rel==="modulepreload"&&s(l)}).observe(document,{childList:!0,subtree:!0});function n(e){const t={};return e.integrity&&(t.integrity=e.integrity),e.referrerPolicy&&(t.referrerPolicy=e.referrerPolicy),e.crossOrigin==="use-credentials"?t.credentials="include":e.crossOrigin==="anonymous"?t.credentials="omit":t.credentials="same-origin",t}function s(e){if(e.ep)return;e.ep=!0;const t=n(e);fetch(e.href,t)}})();const{app:i,BrowserWindow:c,dialog:u,ipcMain:d}=require("electron"),a=require("path"),p=require("fs"),h=require("decompress");function f(){const r=new c({width:800,height:600,webPreferences:{preload:a.join(__dirname,"preload.js"),contextIsolation:!0,enableRemoteModule:!1,nodeIntegration:!1}});r.loadURL("http://localhost:3000"),r.webContents.openDevTools()}d.handle("open-file-dialog",async r=>(await u.showOpenDialog({properties:["openFile"],filters:[{name:"ZIP Files",extensions:["zip"]}]})).filePaths);d.handle("unzip-file",async(r,o)=>{const n=a.join(i.getPath("temp"),"extracted");return await h(o,n),p.readdirSync(n).map(e=>a.join(n,e))});i.whenReady().then(f);i.on("window-all-closed",()=>{process.platform!=="darwin"&&i.quit()});i.on("activate",()=>{c.getAllWindows().length===0&&f()});
