package com.hzw.ssm.sys.project.action;

import java.util.List;

import javax.servlet.http.HttpSession;

import org.apache.struts2.convention.annotation.Action;
import org.apache.struts2.convention.annotation.Namespace;
import org.apache.struts2.convention.annotation.ParentPackage;
import org.apache.struts2.convention.annotation.Result;
import org.apache.struts2.convention.annotation.Results;
import org.springframework.beans.factory.annotation.Autowired;

import com.hzw.ssm.fw.base.BaseAction;
import com.hzw.ssm.fw.util.CommUtil;
import com.hzw.ssm.fw.util.SysConstants;
import com.hzw.ssm.sys.project.entity.ApplyRecordEntity;
import com.hzw.ssm.sys.project.entity.ConditionEntity;
import com.hzw.ssm.sys.project.entity.ProjectAuditEntity;
import com.hzw.ssm.sys.project.entity.ProjectEntity;
import com.hzw.ssm.sys.project.service.ProjectService;
import com.hzw.ssm.sys.user.entity.UserEntity;

/**
 * 项目抽取审核[次数大于3次审核]
 * 
 * <AUTHOR>
 * 
 */
@Namespace("/extractAudit")
@ParentPackage(value = "default")
@Results( { @Result(name = "toProjectList", location = "/jsp/project/projectExtractAuditList.jsp"),
		@Result(name = "toProjectDetail", location = "/jsp/project/projectExtractAuditDetail.jsp"),
		@Result(name = "toExtractAudit", location = "/jsp/project/extractAuditReason.jsp") })
public class ProjectExtractAuditAction extends BaseAction {

	@Autowired
	private ProjectService projectService;

	private ProjectEntity projectEntity;

	/** 抽取条件 */
	private ConditionEntity conditionEntity;

	private List<ProjectEntity> projectList;

	private List<ConditionEntity> conditionList;

	private ApplyRecordEntity applyRecordEntity;

	private ProjectAuditEntity projectAuditEntity;

	/**
	 * 查询项目抽取审核列表[次数大于3次审核]
	 * 
	 * @return
	 */
	@Action("queryProjectList")
	public String queryProjectList() {
		this.context();
		// 当前session
		HttpSession session = getRequest().getSession();
		// 当前用户信息
		UserEntity user = (UserEntity) session.getAttribute("userInfo");
		try {
			if (null == projectEntity)
				projectEntity = new ProjectEntity();

			projectEntity.setPage(this.getPage());
			projectEntity.setStatus(SysConstants.PROJECT_STATUS.SAVE);
//			projectEntity.setCreateUser(user.getUser_id());
			projectList = projectService.queryPageProjectList(projectEntity, user);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return "toProjectList";
	}

	/**
	 * 查询项目明细
	 * 
	 * @return
	 */
	@Action("toProjectDetail")
	public String toProjectDetail() {
		projectEntity = projectService.queryProjectById(projectEntity);

		// 查询项目申请记录表
		if (null == applyRecordEntity)
			applyRecordEntity = new ApplyRecordEntity();

		applyRecordEntity.setProjectId(projectEntity.getProjectId());
		applyRecordEntity.setType(2L);// 申请类型1：指定专家申请 2：抽取次数超过3次申请
		applyRecordEntity = projectService.queryApplyRecord(applyRecordEntity);

		if (null == conditionEntity)
			conditionEntity = new ConditionEntity();

		conditionEntity.setProjectId(projectEntity.getProjectId());
		conditionList = projectService.queryConditionList(conditionEntity);
		return "toProjectDetail";
	}

	/**
	 * 跳转到审核填写理由页面
	 * 
	 * @return
	 */
	@Action("toExtractAudit")
	public String toExtractAudit() {
		return "toExtractAudit";
	}

	/**
	 * 审核项目申请
	 * 
	 * @return
	 */
	@Action("doExtractAudit")
	public String doExtractAudit() {
		projectAuditEntity.setId(CommUtil.getKey());

		this.context();
		// 当前session
		HttpSession session = getRequest().getSession();
		// 当前用户信息
		UserEntity user = (UserEntity) session.getAttribute("userInfo");
		projectAuditEntity.setAudit_user(user.getUser_id());

		projectService.projectExtractAudit(projectAuditEntity, projectEntity);

		return this.queryProjectList();
	}

	public ProjectEntity getProjectEntity() {
		return projectEntity;
	}

	public void setProjectEntity(ProjectEntity projectEntity) {
		this.projectEntity = projectEntity;
	}

	public List<ProjectEntity> getProjectList() {
		return projectList;
	}

	public void setProjectList(List<ProjectEntity> projectList) {
		this.projectList = projectList;
	}

	public ConditionEntity getConditionEntity() {
		return conditionEntity;
	}

	public void setConditionEntity(ConditionEntity conditionEntity) {
		this.conditionEntity = conditionEntity;
	}

	public List<ConditionEntity> getConditionList() {
		return conditionList;
	}

	public void setConditionList(List<ConditionEntity> conditionList) {
		this.conditionList = conditionList;
	}

	public ApplyRecordEntity getApplyRecordEntity() {
		return applyRecordEntity;
	}

	public void setApplyRecordEntity(ApplyRecordEntity applyRecordEntity) {
		this.applyRecordEntity = applyRecordEntity;
	}

	public ProjectAuditEntity getProjectAuditEntity() {
		return projectAuditEntity;
	}

	public void setProjectAuditEntity(ProjectAuditEntity projectAuditEntity) {
		this.projectAuditEntity = projectAuditEntity;
	}

}
