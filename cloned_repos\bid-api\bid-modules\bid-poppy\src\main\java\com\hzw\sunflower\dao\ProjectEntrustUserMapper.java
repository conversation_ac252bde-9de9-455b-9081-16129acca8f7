package com.hzw.sunflower.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hzw.sunflower.controller.project.response.ProjectEntrustUserVo;
import com.hzw.sunflower.dto.AgentUserDTO;
import com.hzw.sunflower.entity.ProjectEntrustUser;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 项目委托关系人表 Mapper接口
 *
 * <AUTHOR>
 * @version 1.0.0 2021-04-07
 */
public interface ProjectEntrustUserMapper extends BaseMapper<ProjectEntrustUser> {

    /**
     * 查询委托人
     *
     * @param id
     * @param type
     * @return
     */
    List<Map<String, String>> queryPrincipalByIds(@Param("id") Long id, @Param("type") Integer type);

    /**
     * 根据项目ID，用户ID获取信息
     *
     * @param userId
     * @param projectId
     * @return
     */
    ProjectEntrustUser getEntrustUser(@Param("userId") Long userId, @Param("projectId") Long projectId,@Param("deptId") Long deptId);

    /**
     * 根据项目id 查询委托人信息
     *
     * @param projectId
     * @param type
     * @return
     */
    List<AgentUserDTO> getEntrustUserByProjectIdAndType(@Param("projectId") Long projectId, @Param("type") Integer type);

    ProjectEntrustUser getOldEntrustUserId (@Param("projectId") Long projectId);

    Boolean updateProjectEntrustUserById(@Param("id") Long id,@Param("userId") Long desUserId);


    /**
     * 获取项目被委托人信息
     * @param projectId
     * @return
     */
   ProjectEntrustUserVo selectEntrustUserAndDepartment(@Param("projectId") Long projectId);
}