package com.hzw.sunflower.controller.response;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@ApiModel(description = "代办列表中的项目信息 ")
@Data
public class ProjectTaskVO implements Serializable {

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "项目ID")
    private Long projectId;
    /**
     * 项目编名称
     */
    @ApiModelProperty(value = "项目编名称")
    private String projectName;

    /**
     * 项目编号
     */
    @ApiModelProperty(value = "项目编号")
    private String projectNum;


    @ApiModelProperty(value = "关联标段")
    private String projectBidSections;

    /**
     * 阶段： 0：默认无阶段； 1：第一轮； 2：第二轮
     */
    @ApiModelProperty(value = "阶段")
    private Integer bidRound;

    @ApiModelProperty(value = "标段id")
    private Long sectionId;

    @ApiModelProperty(value = "标段id集合")
    private String sectionIds;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "原id")
    private Long baseId;

}
