///***
/// * 自定义各种图例形状
/// */

import 'package:flutter/material.dart';
import 'package:charts_flutter/flutter.dart' as charts;

///***
/// * 自定义图例形状 - 长方形
/// */
class RectangleSymbolRenderer extends charts.CustomSymbolRenderer {
  @override
  Widget build(BuildContext context, {Color color, Size size, bool enabled}) {
    return Container(
      // width: size.width * 1.5,
      // height: size.height / 2.0,
      width: 12.0,
      height: 4.0,
      color: enabled ? color : color.withAlpha(0x66),
    );
  }
}

///***
/// * 自定义图例形状 - 空心圆
/// */
class HollowCircleSymbolRenderer extends charts.CustomSymbolRenderer {
  @override
  Widget build(BuildContext context, {Color color, Size size, bool enabled}) {
    return Container(
      width: size.width / 2,
      height: size.height / 2,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(size.width / 2),
        border: Border.all(
          color: enabled ? color : color.withAlpha(0x66),
          width: size.width / 8,
          style: BorderStyle.solid,
        ),
      ),
    );
  }
}

///***
/// * 自定义图例形状 - 实心圆
/// */
class DiscSymbolRenderer extends charts.CustomSymbolRenderer {
  @override
  Widget build(BuildContext context, {Color color, Size size, bool enabled}) {
    return SizedBox(
      width: size.width * 2,
      height: size.height,
      child: Stack(
        children: <Widget>[
          Positioned(
            left: size.width / 2 + size.width / 8,
            top: size.width / 8,
            width: size.width / 4 * 3,
            height: size.height / 4 * 3,
            child: Container(
              decoration: BoxDecoration(
                color: enabled ? color : color.withAlpha(0x66),
                borderRadius: BorderRadius.circular(size.width / 4 * 3),
              ),
            ),
          ),
          Positioned(
            left: size.width / 4,
            top: size.height / 2 - 0.5,
            width: size.width / 2 * 3,
            height: 1.0,
            child: Container(
              color: enabled ? color : Colors.transparent,
            ),
          ),
        ],
      ),
    );
  }
}
