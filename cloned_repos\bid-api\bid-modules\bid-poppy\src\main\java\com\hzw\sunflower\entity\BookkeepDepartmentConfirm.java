package com.hzw.sunflower.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.hzw.sunflower.common.BaseBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;


@ApiModel(description = "记账部门分摊确认表")
@TableName("t_bookkeep_department_confirm")
@Data
public class BookkeepDepartmentConfirm extends BaseBean {

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "项目id")
    private Long projectId;

    @ApiModelProperty(value = "分摊表id")
    private Long projectShareId;

    @ApiModelProperty(value = "审核人")
    private String confirmUserName;

    @ApiModelProperty(value = "用户Id")
    private String confirmUserId;

    @ApiModelProperty(value = "用户类型 1：普通员工  2：处长")
    private Integer userType;

    @ApiModelProperty(value = "确认类型 1:成本记账部门，2:收入记账部门")
    private Integer confirmType;

    @ApiModelProperty(value = "确认状态 1待确认 2：已确认，3：已退回")
    private Integer confirmStatus;

    @ApiModelProperty(value = "退回原因")
    private String returnReason;

    @ApiModelProperty(value = "申请时间")
    private Date applyTime;

}
