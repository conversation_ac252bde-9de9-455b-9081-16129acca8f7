package com.hzw.common.core.utils;



import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.Random;

public class CommUtil {

	/**
	 * 使用17位日期时间加4位流水号创建一个至少21位的主键值
	 * 
	 * @param len
	 *            主键位数，必须在21~32之间，否则返回的主键只有20位
	 * @return 返回主键
	 */
	public synchronized static String getKey(int len) {

		StringBuffer ret = new StringBuffer(32);
		dfDate.setLenient(false);
		ret.append(dfDate.format(new java.util.Date()));

		ret.append(dfSerialNumFour.format(serial++));
		if (serial >= 10000)
			serial = 0;

		if (len > 21 && len < 33) {
			int seed = Integer.valueOf(ret.substring(15, 18)).intValue();
			Random random = new Random(seed);
			String ran = dfRandomNum.format(Math.abs(random.nextLong()));
			ret.append(ran.substring(0, len - 21));
		}
		return ret.toString();
	}

	/** 最大三位的流水号 */
	private static int serial = 0;
	/** 日期生成字符串格式 */
	private static SimpleDateFormat dfDate = new SimpleDateFormat(
			"yyyyMMddHHmmssSSS"); // yyyy-MM-dd HH:mm:ss.SSS
	/** 三位流水号生成字符串格式 */
	private static DecimalFormat dfSerialNumThree = new DecimalFormat("000");
	/** 四位流水号生成字符串格式 */
	private static DecimalFormat dfSerialNumFour = new DecimalFormat("0000");
	/** 随机数生成字符串格式 */
	private static DecimalFormat dfRandomNum = new DecimalFormat("00000000000");

	/**
	 * 使用17位日期时间加3位流水号创建一个20位的主键值
	 * 
	 * @return 返回主键
	 */
	public synchronized static String getKey() {

		StringBuffer ret = new StringBuffer(20);
		dfDate.setLenient(false);
		ret.append(dfDate.format(new java.util.Date()));

		ret.append(dfSerialNumThree.format(serial++));
		if (serial >= 1000) {
			serial = 0;
		}
		return ret.toString();
	}

	/**
	 * 短信系统自动发送自定义短信批次
	 * @return 返回主键
	 */
	public synchronized static String getSmsKey() {
		Random random = new Random();
		String result="";
		for (int i=0;i<6;i++)
		{
			result+=random.nextInt(10);
		}
		return result;
	}
	
	
	/**
	 * 返回错误的堆栈信息
	 * 
	 * @param e
	 *            错误对象
	 * @param message
	 *            是否返回错误描述
	 * @return 返回堆栈信息
	 */
	public static String getStackTraceInfo(Exception e, boolean message) {
		StringBuffer buf = new StringBuffer("");
		if (message) {
			buf.append(e.getMessage());
		}
		StackTraceElement stack[] = e.getStackTrace();
		for (int i = 0; i < stack.length; i++) {
			buf.append("\n\t").append(stack[i].toString());
		}
		return buf.toString();
	}


	/**
	 * <pre>
	 *     卸载Session中的所有信息并使Session失效。
	 * </pre>
	 * 
	 * @param request
	 *            请求对象
	 */
	public static void destroyTempSession(HttpServletRequest request, String key) {
		HttpSession httpSession = request.getSession();
		if (httpSession != null) {
			String linked = (String) httpSession.getAttribute(key);
			if (linked != null && linked.equals("true")) {
				httpSession.setAttribute(key, null);
				httpSession.invalidate();
			}
		}
	}

	/**
	 * 将对象转换成数值
	 * 
	 * @param val
	 *            对象
	 * @param def
	 *            默认值
	 * @return 数值
	 */
	public static int changeStringToInt(Object val, int def) {
		int ret = def;
		try {
			if (val != null && !val.equals("")) {
				ret = Integer.valueOf(String.valueOf(val).trim()).intValue();
			}
		} catch (Exception e) {
			e.printStackTrace();
			ret = def;
		}
		return ret;
	}

	/**
	 * 过滤html脚本中的特殊字符
	 * 
	 * @param strInfo
	 *            html脚本
	 * @return 过滤后的脚本
	 */
	public static String filterHtml(String strInfo) {
		if (strInfo == null) {
			return "";
		}
		strInfo = strInfo.replaceAll("&", "&amp;");
		strInfo = strInfo.replaceAll(">", "&gt;");
		strInfo = strInfo.replaceAll("<", "&lt;");
		strInfo = strInfo.replaceAll("\"", "&quot;");
		return strInfo;
	}

}
