package com.hzw.sunflower.constant.constantenum;

/**
 * 三级采购方式
 */
public enum PurchaseStatusSysEnum {
    PRETRIAL_PUBLIC(1,"预审公开"),
    POST_TRIAL_PUBLIC(2,"后审公开"),
    INVITE(3,"邀请"),
    TWO_PHASES(4,"二阶段"),
    GOVERNMENT_PUBLIC(5,"政府公开"),
    GOVERNMENT_INVITE(6,"政府邀请");

    private Integer type;
    private String desc;

    PurchaseStatusSysEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }


    public static PurchaseStatusSysEnum matchType(Integer type) {
        PurchaseStatusSysEnum result = null;
        for (PurchaseStatusSysEnum s : values()) {
            if (s.getType().equals(type)) {
                result = s;
                break;
            }
        }
        return result;
    }

    public static PurchaseStatusSysEnum catchDesc(String desc) {
        PurchaseStatusSysEnum result = null;
        for (PurchaseStatusSysEnum s : values()) {
            if (s.getDesc().equals(desc)) {
                result = s;
                break;
            }
        }
        return result;
    }
}
