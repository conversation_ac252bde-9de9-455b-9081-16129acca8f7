package com.hzw.sunflower.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.hzw.sunflower.common.BaseBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
* Created by pj on 2023/11/03
*/
@ApiModel("t_seal_application表")
@TableName("t_seal_application")
@Data
public class SealApplication extends BaseBean implements Serializable  {
    private Long id;

    /**
     * 用印申请类型（1.呈报事项 2.合同事项 3.拟文 4.用印审批）
     *
     * @mbg.generated
     */
    @ApiModelProperty("用印申请类型（1.呈报事项 2.合同事项 3.拟文 4.用印审批")
    private Integer type;

    /**
     * 用印申请状态（1.待审批 2.已通过 3.已退回 4.撤回待审批 5.已撤回 ）
     *
     * @mbg.generated
     */
    @ApiModelProperty("用印申请状态（1.待审批 2.已通过 3.已退回 4.撤回待审批 5.已撤回 ）")
    private Integer approveStatus;

    /**
     * 申请事项(1.澄清/修改 2.异议回复 3.通知 4.函件 5.请示 6.报告 7.投标文件 8.评标报告 9.采购文件 10.其他)
     *
     * @mbg.generated
     */
    @ApiModelProperty("1.澄清/修改 2.异议回复 3.通知 4.函件 5.请示 6.报告 7.投标文件 8.评标报告 9.采购文件 10.其他 11.中标通知书")
    private Integer applyItem;

    /**
     * 申请事项描述
     *
     * @mbg.generated
     */
    @ApiModelProperty("申请事项描述")
    private String itemDescribe;

    /**
     * 选择印章
     *
     * @mbg.generated
     */
    @ApiModelProperty("选择印章")
    private String chooseSeal;


    /**
     * 选择印章信息
     *
     * @mbg.generated
     */
    @ApiModelProperty("选择印章信息")
    private String chooseSealInfo;

    /**
     * 提交人
     *
     * @mbg.generated
     */
    @ApiModelProperty("提交人")
    private Long applyUser;

    @ApiModelProperty("提交人")
    private String applyUserName;

    /**
     * 提交时间
     *
     * @mbg.generated
     */
    @ApiModelProperty("提交时间")
    private Date applyTime;

    @ApiModelProperty("部门id")
    private Long deptId;

    @ApiModelProperty("部门名称")
    private String deptName;

    /**
     * 用印方式（1.电子章 2.实体章）
     *
     * @mbg.generated
     */
    @ApiModelProperty("用印方式（1.电子章 2.实体章）")
    private String sealType;

    /**
     * 用印形式（1.当前页面签章 2.全部页面批量签章 3.骑缝签章 4.关键字签章）
     *
     * @mbg.generated
     */
    @ApiModelProperty("用印形式（1.当前页面签章 2.全部页面批量签章 3.骑缝签章 4.关键字签章）")
    private String sealMethod;

    /**
     * 用印形式说明
     *
     * @mbg.generated
     */
    @ApiModelProperty("用印形式说明")
    private String sealMethodDescribe;

    /**
     * 归口审批部门(1.办公室 2.运管处 3.财审处)
     *
     * @mbg.generated
     */
    @ApiModelProperty("归口审批部门(1.办公室 2.运管处 3.财审处)")
    private String approveDept;

    /**
     * 清样校对部门
     *
     * @mbg.generated
     */
    @ApiModelProperty("清样校对部门")
    private Long proofCheckDept;

    @ApiModelProperty("清样校对部门名称")
    private String proofCheckDeptName;

    /**
     * 清样校对人员
     *
     * @mbg.generated
     */
    @ApiModelProperty("清样校对人员")
    private Long proofCheckUser;

    @ApiModelProperty("清样校对人员名称")
    private String proofCheckUserName;

    /**
     * 审批领导（1.王建专 2.叶逢春 3.凌文 4.姚荣政 5.宋航）
     *
     * @mbg.generated
     */
    @ApiModelProperty("审批领导（1.王建专 2.叶逢春 3.凌文 4.姚荣政 5.宋航）")
    private String approveLeader;

    @ApiModelProperty("主送企业id")
    private String masterSend;
    /**
     * 主送企业名称
     *
     * @mbg.generated
     */
    @ApiModelProperty("主送企业名称")
    private String masterName;

    @ApiModelProperty("抄送企业id")
    private String copySend;
    /**
     * 抄送企业名称
     *
     * @mbg.generated
     */
    @ApiModelProperty("抄送企业名称")
    private String copySendName;

    /**
     * 是否有文号（1.是 2.否）
     *
     * @mbg.generated
     */
    @ApiModelProperty("是否有文号（1.是 2.否）")
    private Integer isSymbol;

    /**
     * 文号
     *
     * @mbg.generated
     */
    @ApiModelProperty("文号")
    private String symbol;

    /**
     * 是否公开（1.是 2否）
     *
     * @mbg.generated
     */
    @ApiModelProperty("是否公开（1.是 2否）")
    private Integer isPublic;


    /**
     * 流程实例id
     *
     * @mbg.generated
     */
    @ApiModelProperty("流程实例id")
    private String processinstanceId;

    @ApiModelProperty("发往单位")
    private String sendCompany;

    @ApiModelProperty("清样校对前端回显")
    private String proofCheckShow;

    private static final long serialVersionUID = 1L;
}