package com.hzw.sunflower.controller.response;

import com.hzw.sunflower.dto.AnnexDTO;
import com.hzw.sunflower.dto.MediaDTO;
import com.hzw.sunflower.dto.PackageInfoDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;
import java.util.Map;

@ApiModel(description = "公告变更记录 返回实体")
@Data
@Accessors(chain = true)
public class NoticeChangeRecordVO {

    @ApiModelProperty(value = "提交次数", position = 1)
    private Long submitNum;

    @ApiModelProperty(value = "变更时间", position = 2)
    private Date changeTime;

    @ApiModelProperty(value = "变更类型：0 首次提交；1 撤回变更；2 退回变更", position = 3)
    private Integer changeType;

    @ApiModelProperty(value = "招标公告内容变更标识：false代表发生变更；true代表未发生变更", position = 4)
    private Map<Object,Boolean> flags;

    @ApiModelProperty(value = "招标公告名称", position = 5)
    private String noticeName;

    @ApiModelProperty(value = "招标公告内容", position = 6)
    private String noticeContent;

    @ApiModelProperty(value = "招标公告拟发布时间", position = 7)
    private Date intendedReleaseTime;

    @ApiModelProperty(value = "招标公告截止时间", position =8)
    private Date endTime;

    @ApiModelProperty(value = "招标公告关联包段信息", position = 9)
    private List<PackageInfoDTO> packageInfo;

    @ApiModelProperty(value = "招标公告关联附件", position = 10)
    private List<AnnexDTO> annexes;

    @ApiModelProperty(value = "招标公告关联媒体", position = 11)
    private List<MediaDTO> medias;

    @ApiModelProperty(value = "项目是否划分报端：true代表划分；false代表未划分", position = 12)
    private Boolean hasPackage;

    @ApiModelProperty(value = "招标公告进度", position = 13)
    private String noticeProgress;

    @ApiModelProperty(value = "公告id", position = 14)
    private Long noticeId;

    @ApiModelProperty(value = "变更编号", position = 15)
    private Long id;
}
