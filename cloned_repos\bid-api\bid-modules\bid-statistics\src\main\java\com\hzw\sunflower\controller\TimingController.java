package com.hzw.sunflower.controller;

import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.service.ProjectAnalysisService;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Api(tags = "临时测试")
@RestController
@RequestMapping("/timingController")
public class TimingController {
    @Autowired
    ProjectAnalysisService projectAnalysisService;
    @RequestMapping("/timingUpdate")
    public Result<Boolean> timingUpdate(@RequestParam(value="dateString",required = false) String dateString){
        try {
            projectAnalysisService.updateProjectIndustryDistributionList(dateString);
            return Result.ok(true);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.ok(false);
        }
    }
}
