package com.hzw.externalApi.api.domain.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class VoiceEntityDTO implements Serializable {
    public String id;

    //业务流水号
    public String messageId;

    //呼叫人手机号码
    public String calledNum;

    //呼叫内容
    public String mediaContent;

    /**
     * 专家抽取申请id
     */
    public String extractionId;
    /**
     * 专家抽取记录id
     */
    public String recordId;
    /**
     * 企业名称
     */
    public String companyName;
    /**
     * 通知短信是否发送（1 已发送 2未发送 3不参加 4重新通知 5已抽满）
     */
    public Integer isSendSms;
    /**
     * 联系次数
     */
    public Integer times;

    public List<VoiceEntityDTO> entities;

}
