package com.hzw.sunflower.constant.constantenum;

/**
 * <AUTHOR>
 * @date ：Created in 2021/9/7 10:14
 * @description：撤回待确认流程状态值
 * @version: 1.0
 */
public enum WithdrawProgressEnum {
    REJECT(1, "退回撤回 "),

    AGREE(2, "同意撤回");

    private Integer value;

    private String name;

    WithdrawProgressEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    public Integer getValue() {
        return value;
    }

    public String getName() {
        return name;
    }
}
