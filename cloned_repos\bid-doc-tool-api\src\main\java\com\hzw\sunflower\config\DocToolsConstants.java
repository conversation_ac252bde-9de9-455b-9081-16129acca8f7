package com.hzw.sunflower.config;

/**
 * <AUTHOR>
 * @datetime 2024/06/26 13:59
 * @description: 常量类
 * @version: 1.0
 */
public class DocToolsConstants {

    /**
     * 是否
     */
    public static final String YES = "1";
    public static final String YES_CODE = "☑";
    public static final String NO = "2";
    public static final String NO_CODE = "□";

    /**
     * 招标文件获取方式 纸质文件线下获取/电子文件线上发售
     */
    public static final String OBTAIN_TYPE_OFFLINE = "1";
    public static final String OBTAIN_TYPE_ONLINE = "2";

    /**
     * 投标文件递交方式 线下递交/线上平台递交
     */
    public static final String SUBMIT_TYPE_OFFLINE = "1";
    public static final String SUBMIT_TYPE_ONLINE = "2";

    /**
     * 招标文件固定段落内容
     */
    // 3.7投标文件的编制
    public static final String OFFLINE_BID_COMPILE_CONTENT = "（1）投标文件应用不褪色的材料书写或打印，投标函及对投标文件的澄清、说明和补正应由投标人的法定代表人（单位负责人）或其授权的代理人签字或盖单位章。由投标人的法定代表人（单位负责人）签字的，应附法定代表人（单位负责人）身份证明，由代理人签字的，应附授权委托书，身份证明或授权委托书应符合第六章“投标文件格式”的要求。投标文件应尽量避免涂改、行间插字或删除。如果出现上述情况，改动之处应由投标人的法定代表人（单位负责人）或其授权的代理人签字或盖单位章。\n" +
            "  （2）投标文件正本一份，副本份数见投标人须知前附表。正本和副本的封面右上角上应清楚地标记“正本”或“副本”的字样。投标人应根据投标人须知前附表要求提供电子版文件。当副本和正本不一致或电子版文件和纸质正本文件不一致时，以纸质正本文件为准。\n" +
            "  （3）投标文件的正本与副本应分别装订，并编制目录，投标文件需分册装订的，具体分册装订要求见投标人须知前附表规定。";
    public static final String ONLINE_BID_COMPILE_CONTENT = "投标文件全部采用电子文档，除投标人须知前附表另有规定外，投标文件所附证书证件均为原件扫描件，并采用单位和个人数字证书，按招标文件要求在相应位置加盖电子印章。由投标人的法定代表人（单位负责人）签字或加盖电子印章的，应附法定代表人（单位负责人）身份证明，由代理人签字或加盖电子印章的，应附由法定代表人（单位负责人）签署的授权委托书。签字或盖章的具体要求见投标人须知前附表。";
    // 4.1投标文件的密封和标记
    public static final String OFFLINE_BID_MARKING_CONTENT = "投标文件应密封包装，并在封套的封口处加盖投标人单位章或由投标人的法定代表人（单位负责人）或其授权的代理人签字。";
    public static final String ONLINE_BID_MARKING_CONTENT = "投标人应当按照招标文件和电子招标投标交易平台的要求加密投标文件，具体要求见投标人须知前附表。";
    // 4.2投标文件的递交
    public static final String OFFLINE_BID_SUBMIT_CONTENT_1 = "投标人递交投标文件的地点：见投标人须知前附表。";
    public static final String OFFLINE_BID_SUBMIT_CONTENT_2 = "招标人收到投标文件后，向投标人出具签收凭证。";
    public static final String OFFLINE_BID_SUBMIT_CONTENT_3 = "逾期送达的投标文件，招标人将予以拒收。";
    public static final String ONLINE_BID_SUBMIT_CONTENT_1 = "投标人通过下载招标文件的电子招标投标交易平台递交电子投标文件。";
    public static final String ONLINE_BID_SUBMIT_CONTENT_2 = "投标人完成电子投标文件上传后，电子招标投标交易平台即时向投标人发出递交回执通知。递交时间以递交回执通知载明的传输完成时间为准。";
    public static final String ONLINE_BID_SUBMIT_CONTENT_3 = "逾期送达的投标文件，电子招标投标交易平台将予以拒收。";
    // 4.3投标文件的撤回和修改
    public static final String OFFLINE_UPDATE_WITHDRAW_CONTENT = "投标人修改或撤回已递交投标文件的书面通知应按照本章第 3.7.3项的要求签字或盖章。招标人收到书面通知后，向投标人出具签收凭证。";
    public static final String ONLINE_UPDATE_WITHDRAW_CONTENT = "投标人修改或撤回已递交投标文件的通知，应按照本章第 3.7.3项的要求加盖电子印章。电子招标投标交易平台收到通知后，即时向投标人发出确认回执通知。";
    // 5.1开标地点和时间
    public static final String OFFLINE_OPEN_TIME_ADDRESS_CONTENT = "招标人在本章第 4.2.1 项规定的投标截止时间（开标时间）和投标人须知前附表规定的地点公开开标，并邀请所有投标人的法定代表人（单位负责人）或其委托代理人准时参加。";
    public static final String ONLINE_OPEN_TIME_ADDRESS_CONTENT = "招标人在本章第 4.2.1 项规定的投标截止时间（开标时间）,通过电子招标投标交易平台公开开标，所有投标人的法定代表人（单位负责人）或其委托代理人应当准时参加。";
    // 5.2开标程序
    public static final String OFFLINE_OPEN_PROCESS_CONTENT_1 = "检查投标文件的密封情况，按照投标人须知前附表规定的开标顺序当众开标，公布招标项目名称、投标人名称、投标保证金的递交情况、投标报价、交货期、交货地点及其他内容，并记录在案；";
    public static final String OFFLINE_OPEN_PROCESS_CONTENT_2 = "投标人代表、招标人代表、监标人、记录人等有关人员在开标记录上签字确认；";
    public static final String ONLINE_OPEN_PROCESS_CONTENT_1 = "投标人通过电子招标投标交易平台对已递交的电子投标文件进行解密，公布招标项目名称、投标人名称、投标保证金的递交情况、投标报价、交货期、交货地点及其他内容， 并记录在案；";
    public static final String ONLINE_OPEN_PROCESS_CONTENT_2 = "投标人代表、招标人代表、监标人、记录人等有关人员使用本人的电子印章在开标记录上签字确认；";



}
