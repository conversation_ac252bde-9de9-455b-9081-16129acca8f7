<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!-- 数据权限通用mapper -->
<mapper namespace="com.hzw.sunflower.dao.CommonDepartRoleMapper">

    <select id="getDepartRoleInfoById" resultType="com.hzw.sunflower.dto.CommonDeptRolesDTO">
        select * from
        (
        SELECT
        d.id AS deptId,
        d.other_id as departOtherId,
        d.department_name as departName,
        d.parent_id,
        GROUP_CONCAT(DISTINCT dr.is_main) as isMain,
        GROUP_CONCAT(r.id order by dr.is_main,r.id) as roleStrIds,
        GROUP_CONCAT(r.role_name order by dr.is_main,r.id) as roleStrN<PERSON>s,
        GROUP_CONCAT(r.role_code order by dr.is_main,r.id) as roleStrCodes
        FROM
        r_depart_role dr
        LEFT JOIN t_role r ON dr.role_id = r.id
        LEFT JOIN t_department d ON dr.depart_id = d.id
        WHERE
        dr.is_delete = 0
        AND r.is_delete = 0
        AND d.is_delete = 0
        <if test="deptId != null">
            AND dr.depart_id = #{deptId}
        </if>
        and dr.user_id = #{userId}
        GROUP BY
        d.id,d.other_id,d.department_name,d.parent_id
        ) t
        order by t.isMain,t.deptId
    </select>


    <select id="getUserRoleByIdentity" resultType="com.hzw.sunflower.controller.response.CommonUserRoleRelationVO">
        SELECT
            t.id,
            t.user_id,
            t.role_id,
            tr.role_code,
            t.created_user_id,
            t.created_time,
            t.updated_user_id,
            t.updated_time,
            t.is_delete,
            t.remark,
            t.version
        FROM
            r_user_role t
                INNER JOIN t_role tr
                           ON t.role_id = tr.id
        WHERE
            t.is_delete = 0
          and tr.is_delete = 0
          and t.user_id = #{userId}
          AND tr.role_category = #{userIdentity}
    </select>

    <select id="getRoleList" resultType="com.hzw.sunflower.entity.Role">
        select
            id,
            role_name ,
            role_code ,
            role_category ,
            description ,
            data_permission ,
            status,
            company_id ,
            other_id
        from
            t_role
        where
            is_delete = 0
        <if test="roleids != null and roleids.size > 0">
            AND id in
            <foreach collection="roleids" item="roleid" open="(" separator="," close=")">
                #{roleid}
            </foreach>
        </if>
    </select>

</mapper>
