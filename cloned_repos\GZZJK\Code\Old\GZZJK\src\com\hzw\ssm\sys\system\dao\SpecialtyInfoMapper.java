package com.hzw.ssm.sys.system.dao;

import com.hzw.ssm.expert.entity.SpecialtyInfoEntity;

import java.util.List;

public interface SpecialtyInfoMapper {

	/**
	 * 加载树形专业类别
	 * 
	 * @param SpecialtyEntity
	 *            专业类别查询条件实体对象
	 * @return 实体对象集合
	 */
	public List<SpecialtyInfoEntity> querySpecialtyList();

	/**
	 * 根据id获取一条专业类别信息
	 * 
	 * @return
	 */
	public List<SpecialtyInfoEntity> getSpecialtyById(SpecialtyInfoEntity entity);


	/**
	 * 根据专业类别名称获取专业类别信息
	 * 
	 * @return
	 */
	public List<SpecialtyInfoEntity> getSpecialtyByName(SpecialtyInfoEntity entity);

	/**
	 * 根据父专业类别id获取信息
	 * 
	 * @param parentId
	 * @return
	 */
	public List<SpecialtyInfoEntity> getSpecialtyByParentId(SpecialtyInfoEntity entity);

	/**
	 * 查询子专业类别
	 * 
	 * @param parentId
	 * @return
	 */
	public List<SpecialtyInfoEntity> querySubSpecialtyBySpecialtyId(SpecialtyInfoEntity entity);

	/**
	 * 新增专业类别对象
	 * 
	 * @param SpecialtyEntity
	 *            专业类别实体对象
	 */
	public void saveSpecialty(SpecialtyInfoEntity entity);

	/**
	 * 更新专业类别对象
	 * 
	 * @param userEntity
	 *            专业类别实体对象
	 */
	public void modifySpecialty(SpecialtyInfoEntity entity);

	/**
	 * 删除专业类别信息
	 * 
	 * @param SpecialtyEntity
	 */
	public void deleteSpecialtyById(SpecialtyInfoEntity entity);

    int queryCountBySpecialtyId(SpecialtyInfoEntity entity);

	List<SpecialtyInfoEntity> querySubSpecialtyBySpeParentId(SpecialtyInfoEntity entity);

	List<SpecialtyInfoEntity> queryAll();

}
