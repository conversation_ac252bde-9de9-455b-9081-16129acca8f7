package com.hzw.common.core.constant;

/**
 * 缓存常量信息
 *
 * <AUTHOR>
 */
public interface CacheConstants {

    /**
     * 在线用户 redis key
     */
    String ONLINE_TOKEN_KEY = "online_tokens:";

    /**
     * loginid构造拼接字符串
     */
    String LOGINID_JOIN_CODE = ":";

    /**
     * 验证码 redis key
     */
    String CAPTCHA_CODE_KEY = "captcha_codes:";

    /**
     * 供应商注册验证码 redis key
     */
    String SUPPLIER_REGISTER_CAPTCHA_CODE_KEY = "supplier_register_captcha_codes:";

    /**
     * 供应商注册图形验证码 redis key
     */
    String SUPPLIER_REGISTER_IMAGE_CODE_KEY = "supplier_register_image_codes:";

    /**
     * 供应商登录验证码 redis key
     */
    String SUPPLIER_LOGIN_CAPTCHA_CODE_KEY = "supplier_login_captcha_codes:";

    /**
     * 供应商手机号登录图形验证码 redis key
     */
    String SUPPLIER_PHONE_LOGIN_IMAGE_CODE_KEY = "supplier_phone_login_image_codes:";

    /**
     * 供应商密码登录图形验证码 redis key
     */
    String SUPPLIER_PWD_LOGIN_IMAGE_CODE_KEY = "supplier_pwd_login_image_codes:";

    /**
     * 供应商修改密码验证码 redis key
     */
    String SUPPLIER_CHANGE_PWD_CAPTCHA_CODE_KEY = "supplier_change_pwd_captcha_codes:";

    /**
     * 供应商修改密码图形验证码 redis key
     */
    String SUPPLIER_CHANGE_PWD_IMAGE_CODE_KEY = "supplier_change_pwd_image_codes:";

    /**
     * 供应商注册临时token redis key
     */
    String SUPPLIER_REGISTER_TEMP_TOKEN_KEY = "supplier_register_temp_token:";

    /**
     * 参数管理 cache key
     */
    String SYS_CONFIG_KEY = "sys_config:";

    /**
     * 字典管理 cache key
     */
    String SYS_DICT_KEY = "sys_dict:";

    /**
     * 登录账户密码错误次数 redis key
     */
    String PWD_ERR_CNT_KEY = "pwd_err_cnt:";
}
