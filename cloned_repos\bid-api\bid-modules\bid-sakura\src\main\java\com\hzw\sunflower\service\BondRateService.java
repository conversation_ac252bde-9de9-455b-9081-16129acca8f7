package com.hzw.sunflower.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.controller.request.BondRateReq;
import com.hzw.sunflower.controller.request.BondSectionReq;
import com.hzw.sunflower.entity.BondRate;
import com.hzw.sunflower.entity.BondSplit;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>
 * 保证金利率 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-15
 */
public interface BondRateService extends IService<BondRate> {

    /**
     * 查询利率
     * @param req
     * @return
     */
    BondRate getRateByType(BondRateReq req);

    /**
     * 保存利率
     * @return
     */
    Result<String> saveBankRate(BondRateReq req);
}
