package com.hzw.sunflower.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hzw.sunflower.controller.request.PurchaseTypeReq;
import com.hzw.sunflower.controller.response.ProjectColumnRequiredVO;
import com.hzw.sunflower.entity.ColumnConfig;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ColumnConfigMapper extends BaseMapper<ColumnConfig> {

    List<PurchaseTypeReq> selectPurchaseTypeReqList();

    String selectDepartmentCodeOne();

    /**
     * 查询项目字段
     * @return
     */
    List<ProjectColumnRequiredVO> selectProjectColumn(@Param("longList") List<Long> longList);

    List<Integer> selectLicenseFunctionModule(@Param("licenseFunction") Integer licenseFunction);
}
