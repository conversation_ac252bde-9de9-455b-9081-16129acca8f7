package com.hzw.ssm.expert.entity;

import java.util.List;

import com.hzw.ssm.fw.base.BaseEntity;

/**
 * 专家评价信息表
 * 
 * <AUTHOR> @date 2014-10-17
 */
public class AppraiseInfo extends BaseEntity {
	private static final long serialVersionUID = 1L;

	private String appraise_info_id;// 主键

	private String appraise_column;// 评价列

	private double max_column;// 最高分数
	
	private String max_column_temp;

	private String parent_id;// 父ID

	private Long sort;// 排序号

	private Long delete_flag;// 删除标识

	private List<AppraiseInfo> childList;

	private double score;// 评分

	public String getAppraise_info_id() {
		return appraise_info_id;
	}

	public void setAppraise_info_id(String appraiseInfoId) {
		appraise_info_id = appraiseInfoId;
	}

	public String getAppraise_column() {
		return appraise_column;
	}

	public void setAppraise_column(String appraiseColumn) {
		appraise_column = appraiseColumn;
	}

	public double getMax_column() {
		return max_column;
	}

	public void setMax_column(double maxColumn) {
		max_column = maxColumn;
	}

	public String getParent_id() {
		return parent_id;
	}

	public void setParent_id(String parentId) {
		parent_id = parentId;
	}

	public Long getSort() {
		return sort;
	}

	public void setSort(Long sort) {
		this.sort = sort;
	}

	public List<AppraiseInfo> getChildList() {
		return childList;
	}

	public void setChildList(List<AppraiseInfo> childList) {
		this.childList = childList;
	}

	public double getScore() {
		return score;
	}

	public void setScore(double score) {
		this.score = score;
	}

	public String getMax_column_temp() {
		return max_column_temp;
	}

	public void setMax_column_temp(String max_column_temp) {
		this.max_column_temp = max_column_temp;
	}

}
