package com.hzw.sunflower.controller.response;

import com.hzw.sunflower.common.OverrideBeanMethods;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.Date;

/**
 * 单位公司 返回实体
 *
 * <AUTHOR>
 * @version 1.0.0 2021-04-12
 */
@ApiModel(description = "单位公司 ")
@Data
public class CompanySuperviseVo extends OverrideBeanMethods {

    /**
     * 企业id
     */
    private Long id;

    /**
     * 企业名称
     */
    private String companyName;

    /**
     * 组织机构代码
     */
    private String organizationNum;

    /**
     * 法人
     */
    private String legalRepresentative;


    private Date createdTime;

    private Integer companyStatus;

    /**
     * 身份标识
     */
    private Integer identity;

    private Integer userCount;
}