package com.hzw.controller;

import com.hzw.Service.LightReadingCallbackService;
import com.hzw.entity.InfoResponse;
import com.hzw.entity.OssFileInfo;
import com.hzw.utils.ResultPreview;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


@RestController
@RequestMapping("/preview")
@Log4j2
public class LightReadingCallbackController {

    @Autowired
    private LightReadingCallbackService lightReadingCallbackService;

    /**
     * 生成token
     * @return
     */
    @PostMapping("/getFileToken")
    public ResultPreview<String> getFileToken(@RequestBody OssFileInfo ossFileInfo , @RequestHeader("Authorization") String token){
        return lightReadingCallbackService.getFileToken(ossFileInfo, token);
    }

    //@GetMapping("/info/{token}/{ossKey}")
    public InfoResponse getFileInfoV2(@PathVariable String token, @PathVariable String ossKey) {
        return lightReadingCallbackService.getFileInfoV2(token,ossKey);

    }

    @GetMapping("/info/{token}")
    public InfoResponse getFileInfo(@PathVariable String token) {
        return lightReadingCallbackService.getFileInfo(token);

    }


}