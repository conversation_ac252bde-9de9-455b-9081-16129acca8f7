package com.hzw.sunflower.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@EqualsAndHashCode
public class ProjectBidDocDTO implements Serializable {

    @ApiModelProperty(value = "招标文件ID", position = 1)
    private Long docId;

    @ApiModelProperty(value = "文件名称", position = 2)
    private String annexName;

    @ApiModelProperty(value = "关联标段", position = 3)
    private String packageNumber;

    @ApiModelProperty(value = "上传人名称", position = 4)
    private String userName;

    @ApiModelProperty(value = "招标文件进度", position = 5)
    private Integer noticeProgress;

    @ApiModelProperty(value = "标段ID", position = 6)
    private Long sectionId;

    @ApiModelProperty(value = "标段名称", position = 7)
    private String packageName;

    @ApiModelProperty(value = "项目ID", position = 8)
    private Long projectId;

    @ApiModelProperty(value = "保证金类型 0不收取 1定额 2比例 ", position = 9)
    private Integer bondType;

    @ApiModelProperty(value = "保证金金额）", position = 10)
    private BigDecimal bond;

    @ApiModelProperty(value = "保证金金额比例", position = 11)
    private BigDecimal bondPercent;

    @ApiModelProperty(value = "标书费类型 0不收取 1按项目 2按标段/包", position = 12)
    private Integer tenderFeeType;

    @ApiModelProperty(value = "标书费", position = 13)
    private BigDecimal tenderFee;

    @ApiModelProperty(value = "文件发售方式(0:电子文件线上下载, 1:纸质文件不支持邮寄)", position = 14)
    private Integer releaseFileType;

    @ApiModelProperty(value = "文件发售审核方式(0:无需审核, 1:审核无需材料, 2:审核上传材料)", position = 15)
    private Integer reviewFileType;

    @ApiModelProperty(value = "材料清单", position = 16)
    private String materialList;

    @ApiModelProperty(value = "委托金额", position = 17)
    private BigDecimal entrustMoney;

    @ApiModelProperty(value = "招标文件发售开始时间", position = 18)
    private Date saleStartTime;

    @ApiModelProperty(value = "招标文件发售结束时间类型（1：确定时间; 2.另行通知; 3：投标文件递交截止时间前一天）", position = 19)
    private Integer saleEndTimeType;

    @ApiModelProperty(value = "招标文件发售结束时间", position = 20)
    private Date saleEndTime;

    @ApiModelProperty(value = "招标文件递交截止时间", position = 21)
    private Date submitEndTime;

    @ApiModelProperty(value = "联系人", position = 22)
    private String contact;

    @ApiModelProperty(value = "手机号", position = 23)
    private String phone;

    @ApiModelProperty(value = "费用类型 1.标书费 2.平台服务费")
    private Integer feeType;

    @ApiModelProperty(value = "标书费支付方式", position = 24)
    private Integer paymentType;

    @ApiModelProperty(value = "标段状态", position = 25)
    private String status;

    @ApiModelProperty(value = "oss文件Key", position = 26)
    private String ossFileKey;

    @ApiModelProperty(value = "oss文件名称", position = 27)
    private String ossFileName;

    @ApiModelProperty(value = "oss文件ID", position = 28)
    private Long ossFileId;

    @ApiModelProperty(value = "包件状态（0-无包，1-有包）", position = 29)
    private Integer packageSegmentStatus;

    @ApiModelProperty(value = "开标地点", position = 30)
    private String openBidAddress;

    @ApiModelProperty(value = "按套销售信息", position = 31)
    private Long sectionGroupId;

    @ApiModelProperty(value = "招标文件售价说明（1：交通；2：水利；3：其他）", position = 32)
    private Integer filePriceState;

    @ApiModelProperty(value = "投标文件递交截止时间类型（1：确定时间; 2：另行通知）", position = 33)
    private Integer submitEndTimeType;

    @ApiModelProperty(value = "采购方式", position = 34)
    private String purchaseMode;

    @ApiModelProperty(value = "1-预审公开 2-后审公开 3-邀请 4-二阶段 5-政府公开 6-政府邀请", position = 35)
    private Integer purchaseStatus;

    @ApiModelProperty(value = "1 依法必招  2 非依法必招  3 政府采购  4 国际招标", position = 36)
    private Integer purchaseType;

    @ApiModelProperty(value = "原标段ID", position = 37)
    private Integer formerSectionId;

    @ApiModelProperty(value = "版本号", position = 38)
    private Integer version;

    @ApiModelProperty(value = "异常状态 0.正常 1.暂停 2.终止", position = 39)
    private Integer abnormalStatus;

    @ApiModelProperty(value = "已购买招标文件的投标人本次是否收费", position = 40)
    private Integer free;

    @ApiModelProperty(value = "招标文件阶段： 0：默认无阶段； 1：第一轮； 2：第二轮", position = 41)
    private Integer bidRound;

    @ApiModelProperty(value = "标书费是否收费1 收费 ，2免费", position = 42)
    private Integer tenderFeeFlag;

    @ApiModelProperty(value = "1-预审公开 2-后审公开 3-邀请 4-二阶段 5-政府公开 6-政府邀请", position = 43)
    private Integer formerPurchaseStatus;

    @ApiModelProperty(value = "挂网时间", position = 44)
    private Date releaseTime;

    @ApiModelProperty(value = "文件售价说明其他时具体描述", position = 45)
    private String otherPriceDescribe;

    @ApiModelProperty(value = "提交时间", position = 46)
    private Date submitTime;

    @ApiModelProperty(value = "资格预审方式 0 按包，1 按项目", position = 47)
    private String preQualificationMtd;

    @ApiModelProperty(value = "标的物分类级别一", position = 48)
    private Long bidFirstLevel;

    @ApiModelProperty(value = "标的物分类级别二", position = 49)
    private Long bidSecondLevel;

    @ApiModelProperty(value = "标的物分类级别三", position = 50)
    private Long bidThirdLevel;

    @ApiModelProperty(value = "标的物多选")
    @TableField(exist = false)
    private List<Long> sectionExpertSpecialtyList;
}
