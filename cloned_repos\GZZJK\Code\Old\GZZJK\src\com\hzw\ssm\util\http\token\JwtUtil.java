/*
 * 公司名称：江苏华招网信息技术有限公司
 * 版权信息：江苏华招网信息技术有限公司版权所有
 * 文件名称：JwtUtil.java
 * 修改时间：2020年8月18日
 * 修改人：陈山杉
 * 修改内容：
 * 跟踪单号：
 * 修改单号 ：
 */

package com.hzw.ssm.util.http.token;

import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTVerifier;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.exceptions.JWTDecodeException;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.hzw.ssm.sys.user.entity.UserEntity;
import com.opensymphony.xwork2.ActionContext;

/**
 * <一句话功能简述> jwt工具类 <功能详细描述>
 * 
 * <AUTHOR>
 * @version V0.0.1-SNAPSHOT
 */
public class JwtUtil {

	/**
	 * 过期时间一天， TODO 正式运行时修改为15分钟
	 */
	private static final long EXPIRE_TIME = 4*60 * 60 * 1000;

	/**
	 * token私钥
	 */
	private static String tokenSecret = "f26e587c28064d0e855e72c0a6a0e618";

	private static HashMap<String, Object> map = new HashMap<String, Object>();
	
	/**
	 * 校验token是否正确
	 *
	 * @param token
	 *            密钥
	 * @return 是否正确
	 */
	public static boolean verify(String token) {
		try {
			
			UserEntity user = (UserEntity) ActionContext.getContext().getSession().get("userInfo");
			if(user!=null){
				tokenSecret = map.get(user.getLogin_code()+"tokenSecret").toString();
			}
			if (tokenSecret != null) {
				Algorithm algorithm = Algorithm.HMAC256(tokenSecret);
				JWTVerifier verifier = JWT.require(algorithm).build();
				DecodedJWT jwt = verifier.verify(token);
				return true;
			} else {
				return false;
			}
		} catch (Exception exception) {
			return false;
		}
	}

	/**
	 * 获得token中的信息无需secret解密也能获得
	 *
	 * @return token中包含的用户名
	 */
	public static String getUsername(String token) {
		try {
			DecodedJWT jwt = JWT.decode(token);
			return jwt.getClaim("loginName").asString();
		} catch (JWTDecodeException e) {
			return null;
		}
	}

	/**
	 * 获取登陆用户ID
	 * 
	 * @param token
	 * @return
	 */
	public static String getUserId(String token) {
		try {
			DecodedJWT jwt = JWT.decode(token);
			return jwt.getClaim("userId").asString();
		} catch (JWTDecodeException e) {
			return null;
		}
	}

	/**
	 * 生成签名,15min后过期
	 *
	 * @param username
	 *            用户名
	 * @return 加密的token
	 */
	public static String sign(String username) {
		try {
			
			UserEntity user = (UserEntity) ActionContext.getContext().getSession().get("userInfo");
			if(user!=null){
				// 过期时间
				Date date = new Date(System.currentTimeMillis() + EXPIRE_TIME);
				
				tokenSecret = UUID.randomUUID().toString();
				map.put(user.getLogin_code()+"tokenSecret", tokenSecret);
				
				// 私钥及加密算法
				Algorithm algorithm = Algorithm.HMAC256(tokenSecret);
				// 设置头部信息
				Map<String, Object> header = new HashMap<String, Object>(2);
				header.put("typ", "JWT");
				header.put("alg", "HS256");
				// 附带username，userId信息，生成签名
				return JWT.create().withHeader(header).withClaim("loginName", username).withExpiresAt(date).sign(algorithm);
			}
			return null;
		} catch (UnsupportedEncodingException e) {
			return null;
		}
	}

}
