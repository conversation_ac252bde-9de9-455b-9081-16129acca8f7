package com.hzw.sunflower.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hzw.sunflower.controller.response.BondChangeRecordsVo;
import com.hzw.sunflower.controller.response.BondChangeRecordsListVo;
import com.hzw.sunflower.dto.BondSplitNccDto;
import com.hzw.sunflower.entity.BondChangeRecords;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hzw.sunflower.entity.condition.BondChangeCondition;

import java.util.List;

/**
 * <p>
 * 保证金调整记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-28
 */
public interface BondChangeRecordsService extends IService<BondChangeRecords> {

    /**
     * 分页查询调账日志
     * @param condition
     * @return
     */
    IPage<BondChangeRecordsListVo> getBondChangeRecordsPage(BondChangeCondition condition);



    /**
     * 根据拆分数据新增调整记录
     * @param list
     * @param operateType
     * @return
     */
    Boolean addRecordBySplit(List<BondSplitNccDto> list, Integer operateType);

    /**
     * 根据标段和供应商id查询调整记录
     * @param sectionId
     * @param companyId
     * @return
     */
    List<BondChangeRecordsVo> getChangeRecords(Long sectionId, Long companyId);
}
