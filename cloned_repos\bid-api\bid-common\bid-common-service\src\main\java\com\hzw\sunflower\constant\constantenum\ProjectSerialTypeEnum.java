package com.hzw.sunflower.constant.constantenum;

/**
 * <AUTHOR>
 * @date 2021-04-13
 */
public enum ProjectSerialTypeEnum {

    PROJECT(1, "项目编号"),

    STUB(2, "存根编号");

    private Integer value;

    private String name;

    ProjectSerialTypeEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    public Integer getValue() {
        return value;
    }

    public String getName() {
        return name;
    }

    /*
     * 匹配操作码
     *
     **/
    public static ProjectSerialTypeEnum matchType(String type) {
        for (ProjectSerialTypeEnum projectStatusEnum : ProjectSerialTypeEnum.values()) {
            if (projectStatusEnum.value.toString().equals(type)) {
                return projectStatusEnum;
            }
        }
        return null;
    }
}
