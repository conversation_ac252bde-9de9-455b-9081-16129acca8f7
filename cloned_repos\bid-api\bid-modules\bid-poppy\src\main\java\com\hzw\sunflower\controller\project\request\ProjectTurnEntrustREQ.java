package com.hzw.sunflower.controller.project.request;

import com.hzw.sunflower.entity.ProjectTurnEntrust;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 项目转委托人表请求实体
 *
 * <AUTHOR>
 * @version 1.0.0 2021-04-14
 */
@ApiModel(description = "项目转委托人表")
@Data
public class ProjectTurnEntrustREQ extends ProjectTurnEntrust {

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "项目ID")
    private Long projectId;

    @ApiModelProperty(value = "原负责人")
    private Long srcUserId;

    @ApiModelProperty(value = "现负责人")
    private Long desUserId;

    @ApiModelProperty(value = "现负责人部门")
    private Long desDepartId;

    @ApiModelProperty(value = "单位")
    private Long companyId;

}