package com.hzw.sunflower.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hzw.sunflower.controller.request.AppTaskCondition;
import com.hzw.sunflower.controller.response.AppToReadVo;
import com.hzw.sunflower.entity.AppToRead;
import org.apache.ibatis.annotations.Param;

import java.util.Map;

/**
 * <p>
 * app待阅 Mapper 接口
 * </p>
 *
 * <AUTHOR> Gen
 * @since 2022-12-15
 */
public interface AppToReadMapper extends BaseMapper<AppToRead> {

    /**
     * 分页检索
     * @param page
     * @param condition
     * @return
     */
    IPage<AppToReadVo> listPage(IPage<AppToReadVo> page, @Param("condition") AppTaskCondition condition);

    /**
     * 分页检索
     * @param page
     * @param condition
     * @return
     */
    IPage<AppToReadVo> listAllPage(IPage<AppToReadVo> page, @Param("condition") AppTaskCondition condition);


    /**
     * 待阅数量
     * @param condition
     * @return
     */
    Long countToRead(@Param("condition") AppTaskCondition condition);

    /**
     * 通知数量
     * @param condition
     * @return
     */
    Long countNotice(@Param("condition") AppTaskCondition condition);
}
