package com.jszbtb.mobile.plugins.network.flutter_network_plugin;


import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.PrimaryKey;

import java.util.Date;

@Entity(tableName = "caches")
public class CacheEntity {

    @PrimaryKey(autoGenerate = true)
    public int id;

    @ColumnInfo(name = "cacheName")
    public String cacheName;

    @ColumnInfo(name = "cacheValue")
    public String cacheValue;

    @ColumnInfo(name = "createDate")
    public Date createDate;
}
