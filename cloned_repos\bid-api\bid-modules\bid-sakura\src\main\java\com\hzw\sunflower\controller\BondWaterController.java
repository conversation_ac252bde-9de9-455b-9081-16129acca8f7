package com.hzw.sunflower.controller;

import com.hzw.sunflower.common.BaseController;
import com.hzw.sunflower.common.Paging;
import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.common.annotation.RepeatSubmit;
import com.hzw.sunflower.controller.request.BondImportReq;
import com.hzw.sunflower.controller.response.BondWaterPageVo;
import com.hzw.sunflower.controller.response.BondWaterReturnVo;
import com.hzw.sunflower.controller.response.ImportExcelVo;
import com.hzw.sunflower.entity.BondWater;
import com.hzw.sunflower.entity.condition.BondWaterCondition;
import com.hzw.sunflower.service.BondWaterService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;


/**
 * <p>
 * 保证金流水表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-27
 */
@Api(tags = "保证金流水")
@RestController
@RequestMapping("/bondWater")
public class BondWaterController extends BaseController {

    @Autowired
    BondWaterService bondWaterService;

    @ApiOperation(value = "流水详情")
    @ApiImplicitParam(name = "waterId", value = "流水id", required = true)
    @GetMapping(value = "/getWaterById/{waterId}")
    public Result<BondWater> getWaterById(@PathVariable Long waterId) {
        return Result.ok(bondWaterService.getById(waterId));
    }

    @ApiOperation(value = "excel流水导入")
    @ApiImplicitParam(name = "req", value = "导入参数", required = true, dataType = "BondImportReq", paramType = "body")
    @PostMapping("/importExcel")
    @ResponseBody
    @RepeatSubmit
    public Result<ImportExcelVo> importExcel(BondImportReq req) throws IOException {
        return bondWaterService.importExcel(req.getOssFileId(), req.getTemplateId());
    }

    @ApiOperation(value = "分页查询收款流水")
    @ApiImplicitParam(name = "condition", value = "收款流水列表页查询条件", required = true, dataType = "BondWaterCondition", paramType = "body")
    @PostMapping(value = "/list")
    public Result<BondWaterPageVo> list(@RequestBody BondWaterCondition condition) {
        return Result.ok(bondWaterService.getListByCondition(condition));
    }

    @ApiOperation(value = "分页查询其他收款流水")
    @ApiImplicitParam(name = "condition", value = "收款流水列表页查询条件", required = true, dataType = "BondWaterCondition", paramType = "body")
    @PostMapping(value = "/listOther")
    public Result<Paging<BondWaterReturnVo>> listOther(@RequestBody BondWaterCondition condition) {
        return Result.ok(bondWaterService.getListByConditionOther(condition));
    }

    @ApiOperation(value = "导出")
    @ApiImplicitParam(name = "condition", value = "收款流水列表页查询条件", required = true, dataType = "BondWaterCondition", paramType = "body")
    @PostMapping(value = "/exportExcel")
    public void exportExcel(@RequestBody BondWaterCondition condition){
        bondWaterService.exportExcel(condition, response);
    }


/*    @ApiOperation(value = "刷数历史收款数据")
    @PostMapping(value = "/saveHistoryReceiveWater")
    public Result<Boolean> saveHistoryReceiveWater(){
        Boolean result =  bondWaterService.saveHistoryReceiveWater();
        return Result.okOrFailed(result);
    }*/
}
