package com.hzw.sunflower.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.hzw.sunflower.common.BaseBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * Created by zgq on 2021/06/30
 */
@ApiModel("公告媒体映射")
@TableName("t_notice_media_relation")
@Data
@Accessors(chain = true)
public class NoticeMediaR extends BaseBean {

    @ApiModelProperty(value = "主键", position = 1)
    private Long id;

    @ApiModelProperty(value = "关联公告ID", position = 2)
    private Long noticeId;

    @ApiModelProperty(value = "媒体ID", position = 3)
    private Long mediaId;

    @ApiModelProperty(value = "附件ID", position = 4)
    private Long annexId;

    @ApiModelProperty(value = "媒体状态：1 通用；2 私有", position = 1)
    private Integer state;
}
