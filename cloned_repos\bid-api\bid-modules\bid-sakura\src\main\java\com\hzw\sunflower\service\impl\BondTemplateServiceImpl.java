package com.hzw.sunflower.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hzw.sunflower.dto.BondTemplateDto;
import com.hzw.sunflower.entity.BondTemplate;
import com.hzw.sunflower.dao.BondTemplateMapper;
import com.hzw.sunflower.entity.condition.BondTemplateCondition;
import com.hzw.sunflower.exception.ExceptionEnum;
import com.hzw.sunflower.exception.SunFlowerException;
import com.hzw.sunflower.service.BondTemplateService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 保证金流水模板 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-28
 */
@Service
public class BondTemplateServiceImpl extends ServiceImpl<BondTemplateMapper, BondTemplate> implements BondTemplateService {

    /**
     * 分页查询模板
     * @param condition
     * @return
     */
    @Override
    public IPage<BondTemplateDto> listPage(BondTemplateCondition condition) {
        IPage<BondTemplateDto> page = condition.buildPage();
//        return this.baseMapper.listPage(page, new LambdaQueryWrapper<BondTemplate>().like(BondTemplate::getTemplateName, condition.getKeyWords()));
        return this.baseMapper.listPage(page, condition);
    }

    /**
     * 新增或修改模板
     * @param bondTemplate
     * @return
     */
    @Override
    public Boolean addOrUpdateTemplate(BondTemplate bondTemplate) {
        if (StringUtils.isBlank(bondTemplate.getTemplateName()) || StringUtils.isBlank(bondTemplate.getBankName())) {
            throw new SunFlowerException(ExceptionEnum.ILLEGAL_PARAMETER_EXCEPTION, ExceptionEnum.ILLEGAL_PARAMETER_EXCEPTION.getMessage());
        }
        BondTemplate template = new BondTemplate();
        template.setBankName(bondTemplate.getBankName());
        template.setTemplateName(bondTemplate.getTemplateName());
        // 模板名称查重
        LambdaQueryWrapper<BondTemplate> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BondTemplate::getTemplateName, bondTemplate.getTemplateName());
        // 修改
        if (bondTemplate.getId() != null) {
            template.setId(bondTemplate.getId());
            queryWrapper.notIn(BondTemplate::getId, bondTemplate.getId());
        }
        List<BondTemplate> list = this.list(queryWrapper);
        if (list.size() > 0) {
            throw new SunFlowerException(ExceptionEnum.ID_REPEAT, ExceptionEnum.ID_REPEAT.getMessage());
        }
        return this.saveOrUpdate(template);
    }

    /**
     * 修改模板详情
     * @param bondTemplate
     * @return
     */
    @Override
    public Boolean updateTemplateDetail(BondTemplate bondTemplate) {
        return this.updateById(bondTemplate);
    }
}
