package com.hzw.sunflower.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hzw.sunflower.entity.NccPushFileLog;

import java.util.List;

public interface NccPushFileLogService extends IService<NccPushFileLog> {

    /**
     * 获取需要推送的文件数据
     * @return
     */
    List<NccPushFileLog> getNccSyncFile();

    /**
     * 补查文件
     */
    List<NccPushFileLog> queryFileInfo();
}
