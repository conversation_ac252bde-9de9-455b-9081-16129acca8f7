package com.hzw.sunflower.controller.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
* BidOpen 结束开标页面返回
*
* <AUTHOR>
* @version 1.0.0
*/
@ApiModel(description = "结束开标页面返回")
@Data
public class BidOpenEndVo {

    @ApiModelProperty("供应商无异议列表")
     private List<BidOpenSupplierListVO> supplierList;

    @ApiModelProperty("开标一览表文件")
    private Long fileOssId;

    @ApiModelProperty(value = "开标一览表文件key")
    private String ossFileKey;

    @ApiModelProperty(value = "开标一览表文件名称")
    private String ossFileName;

    @ApiModelProperty(value = "开标结束/终止时间")
    private Date endTime;

}