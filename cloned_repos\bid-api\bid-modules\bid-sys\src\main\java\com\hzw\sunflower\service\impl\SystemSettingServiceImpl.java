package com.hzw.sunflower.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzw.sunflower.common.redis.RedisCache;
import com.hzw.sunflower.constant.ConfigConstants;
import com.hzw.sunflower.controller.response.SystemSettingVo;
import com.hzw.sunflower.dao.SystemSettingMapper;
import com.hzw.sunflower.entity.OssFile;
import com.hzw.sunflower.entity.SystemSetting;
import com.hzw.sunflower.service.OssFileService;
import com.hzw.sunflower.service.SystemSettingService;
import com.hzw.sunflower.util.BeanListUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

@Service
public class SystemSettingServiceImpl extends ServiceImpl<SystemSettingMapper, SystemSetting> implements SystemSettingService {

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private OssFileService ossFileService;
    /**
     * 保存
     * @param systemSetting
     * @return
     */
    @Override
    public Boolean insertSystemSetting(SystemSetting systemSetting) {
        Boolean flag = this.saveOrUpdate(systemSetting);
        SystemSettingVo systemSettingVo = BeanListUtil.convert(systemSetting, SystemSettingVo.class);
        if(systemSetting != null && systemSetting.getRegistrationAgreementId() != null){
            OssFile ossFile = ossFileService.getOssFileById(systemSetting.getRegistrationAgreementId());
            if(ossFile != null){
                systemSettingVo.setRegistrationAgreementKey(ossFile.getOssFileKey());
            }
        }
        //存入缓存
        redisCache.setCacheObject(ConfigConstants.SYS_SETTING, systemSettingVo, ConfigConstants.SYS_SETTING_TIME, TimeUnit.DAYS);
        return flag;
    }

    /**
     * 回显
     * @param req
     * @return
     */
    @Override
    public Object querySystemSetting(SystemSetting req) {
        //先去缓存查询
        Object value = redisCache.getCacheObject(ConfigConstants.SYS_SETTING);
        if(value != null){
            return value;
        }else {
            LambdaQueryWrapper<SystemSetting> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.orderByDesc(SystemSetting::getId).last("limit 1");
            SystemSetting systemSetting = this.getOne(lambdaQueryWrapper);
            SystemSettingVo systemSettingVo = BeanListUtil.convert(systemSetting, SystemSettingVo.class);
            if(systemSetting != null && systemSetting.getRegistrationAgreementId() != null){
                OssFile ossFile = ossFileService.getOssFileById(systemSetting.getRegistrationAgreementId());
                if(ossFile != null){
                    systemSettingVo.setRegistrationAgreementKey(ossFile.getOssFileKey());
                }
            }
            redisCache.setCacheObject(ConfigConstants.SYS_SETTING, systemSettingVo, ConfigConstants.SYS_SETTING_TIME, TimeUnit.DAYS);
            return systemSettingVo;
        }
    }

}
