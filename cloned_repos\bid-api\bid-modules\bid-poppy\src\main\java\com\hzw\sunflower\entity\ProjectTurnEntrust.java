package com.hzw.sunflower.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hzw.sunflower.common.BaseBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 项目转委托人表(t_project_turn_entrust)
 *
 * <AUTHOR>
 * @version 1.0.0 2021-04-14
 */
@ApiModel(description = "项目转委托人表")
@TableName("t_project_turn_entrust")
@Data
public class ProjectTurnEntrust extends BaseBean {
    /**
     * 版本号
     */
    private static final long serialVersionUID = 2304155224328814665L;

    /* This code was generated by TableGo tools, mark 1 begin. */

    @ApiModelProperty(value = "主键", position = 1)
    private Long id;

    @ApiModelProperty(value = "项目ID", position = 2)
    private Long projectId;

    @ApiModelProperty(value = "原负责人", position = 3)
    private Long srcUserId;

    @ApiModelProperty(value = "现负责人", position = 4)
    private Long desUserId;

    @ApiModelProperty(value = "现负责人部门")
    private Long desDepartId;

    @ApiModelProperty(value = "单位", position = 5)
    private Long companyId;

    @ApiModelProperty(value = "状态")
    private Integer status;

    @ApiModelProperty(value = "流程实列")
    private String processInstanceId;

    @ApiModelProperty("提交时间")
    private Date submitTime;

    @ApiModelProperty("提交人")
    private Long submitter;

    /* This code was generated by TableGo tools, mark 1 end. */

    /* This code was generated by TableGo tools, mark 2 begin. */


    /* This code was generated by TableGo tools, mark 2 end. */
}