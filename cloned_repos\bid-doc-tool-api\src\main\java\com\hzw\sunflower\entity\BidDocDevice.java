package com.hzw.sunflower.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.hzw.sunflower.common.BaseBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 招标文件编制设备需求表
 */
@ApiModel(description = "招标文件编制设备需求表")
@TableName("t_bid_doc_device")
@Data
public class BidDocDevice extends BaseBean {

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "招标文件编制表id")
    private Long docId;

    @ApiModelProperty(value = "设备名称")
    private String deviceName;

    @ApiModelProperty(value = "规格")
    private String specifications;

    @ApiModelProperty(value = "数量及单位")
    private String quantityUnit;

    @ApiModelProperty(value = "交货期")
    private String deliveryDate;

    @ApiModelProperty(value = "交货地点")
    private String deliveryAddress;
}
