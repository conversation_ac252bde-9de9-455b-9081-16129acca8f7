package com.hzw.sunflower.controller.response;

import com.hzw.sunflower.dto.AnnexDTO;
import com.hzw.sunflower.dto.MediaDTO;
import com.hzw.sunflower.dto.PackageInfoDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * TProjectBidNotice 返回实体
 *
 * <AUTHOR> 2021/07/01
 * @version 1.0.0
 */
@ApiModel(description = "公告信息 响应实体 ")
@Data
@Accessors(chain = true)
public class ProjectBidNoticeVO {

    @ApiModelProperty(value = "项目ID", position = 1)
    private Long projectId;

    @ApiModelProperty(value = "公告ID", position = 2)
    private Long noticeId;

    @ApiModelProperty(value = "公告类型;1 采购公告；2 补充公告；3 采购结果公示；4 中标候选人公示", position = 3)
    private Integer noticeType;

    @ApiModelProperty(value = "采购方式中文名称", position = 4)
    private String purchaseModeName;

    @ApiModelProperty(value = "公告进度:1：起草中 2：审核中 3：已撤回 4：已审核 5：已发布 6：已退回", position = 5)
    private Integer noticeProgress;

    @ApiModelProperty(value = "项目关联包段的基本信息", position = 6)
    private List<PackageInfoDTO> packageInfo;

    @ApiModelProperty(value = "公告名称", position = 7)
    private String noticeName;

    @ApiModelProperty(value = "公告内容", position = 8)
    private String noticeContent;

    @ApiModelProperty(value = "公告关联附件的基本信息", position = 9)
    private List<AnnexDTO> annexes;

    @ApiModelProperty(value = "公告拟发布时间", position = 10)
    private Date intendedReleaseTime;

    @ApiModelProperty(value = "公告截止时间", position = 11)
    private Date endTime;

    @ApiModelProperty(value = "公告关联的媒体的信息", position = 12)
    private List<MediaDTO> medias;

    @ApiModelProperty(value = "项目是否划分报端：true代表划分；false代表未划分", position = 13)
    private Boolean hasPackage;

    @ApiModelProperty(value = "招标公告内容变更标识：false代表发生变更；true代表未发生变更", position = 14)
    private Map<Object,Boolean> flags;

    /**
     * 招标公告阶段： 0：默认无阶段； 1：第一轮； 2：第二轮
     */
    @ApiModelProperty(value = "招标公告阶段", position = 15)
    private Integer bidRound;
}
