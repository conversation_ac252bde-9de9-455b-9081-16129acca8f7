package com.hzw.ssm.applets.service;

import com.hzw.ssm.applets.dao.*;
import com.hzw.ssm.applets.entity.PhoneCode;
import com.hzw.ssm.applets.exception.ExceptionEnum;
import com.hzw.ssm.applets.utils.JudgeToken;
import com.hzw.ssm.applets.utils.RandomCode;
import com.hzw.ssm.applets.utils.ResultJson;
import com.hzw.ssm.applets.utils.SmallWxUtil;
import com.hzw.ssm.expert.dao.ExpertInfoMapper;
import com.hzw.ssm.expert.dao.RegisterMapper;
import com.hzw.ssm.expert.entity.ExpertAuditEntity;
import com.hzw.ssm.expert.entity.ExpertInfoEntity;
import com.hzw.ssm.expert.entity.ExpertUpdateRecordEntity;
import com.hzw.ssm.fw.base.BaseService;
import com.hzw.ssm.fw.util.CommUtil;
import com.hzw.ssm.fw.util.DateUtil;
import com.hzw.ssm.fw.util.MD5Util;
import com.hzw.ssm.fw.util.SysConstants;
import com.hzw.ssm.sys.sms.mwutil.MWSmsUtil;
import com.hzw.ssm.sys.system.entity.SmsRecordEntity;
import com.hzw.ssm.sys.user.dao.UserMapper;
import com.hzw.ssm.sys.user.entity.UserEntity;
import com.hzw.ssm.util.string.StringUtils;
import net.sf.json.JSONObject;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.*;

@Service
public class AppService extends BaseService {

    private static final Logger logger = Logger.getLogger(AppService.class);

    //梦网短信账号
    @Value("${sms_userId}")
    private String userId;
    //梦网短信密码
    @Value("${sms_userPwd}")
    private String userPwd;
    //梦网主IP
    @Value("${sms_masterIpAddress}")
    private String masterIpAddress;
    //梦网备用IP1
    @Value("${sms_ipAddress1}")
    private String ipAddress1;
    //梦网备用IP2
    @Value("${sms_ipAddress2}")
    private String ipAddress2;


    /**
     * 过期时间2小时
     */
    private static final long EXPIRE_TIME = 2 * 60 * 60 * 1000;

    @Autowired
    private ExpertInfoMapper expertInfoMapper;
    @Autowired
    private EducationInfoMapper educationInfoMapper;
    @Autowired
    private TitleInfoMapper titleInfoMapper;
    @Autowired
    private ProQualificationMapper proQualificationMapper;
    @Autowired
    private BidEvaluationMapper bidEvaluationMapper;
    @Autowired
    private CompanyMapper companyMapper;
    @Autowired
    private PhoneCodeMapper phoneCodeMapper;
    @Autowired
    private RegisterMapper registerMapper;

    @Autowired
    private UserMapper userMapper;

    /**
     * 发送验证码
     *
     * @param jsonReqData
     * @return
     */
    public ResultJson sendSmsCode(JSONObject jsonReqData) {

        try {
            String mobilePhone = jsonReqData.getString("mobilephone");
            if (StringUtils.isEmpty(mobilePhone)) {
                return new ResultJson(ExceptionEnum.PHONE_IS_NULL.code, ExceptionEnum.PHONE_IS_NULL.getMessage());
            }

//            int sendCount = phoneCodeMapper.querySendCount(mobilePhone);
//            if (sendCount > 5) {
//                return new ResultJson(ExceptionEnum.SEND_CODE_OVER.code, ExceptionEnum.SEND_CODE_OVER.getMessage());
//            }

            PhoneCode checkPhone = phoneCodeMapper.checkPhone(mobilePhone);

            if (null != checkPhone) {
                SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                Date endTime = checkPhone.getCreateTime();
                Date startTime = df.parse(df.format(new Date()));
                int t = (int) ((startTime.getTime() - endTime.getTime()) / 1000 / 60);
                if (t < 1) {
                    return new ResultJson(ExceptionEnum.SEND_CODE_MORE.code, ExceptionEnum.SEND_CODE_MORE.getMessage());
                }
            }

            // 发送
            //todo
//            String code = sendSMs(mobilePhone);
            String code = "123456";
            PhoneCode phoneCode = new PhoneCode();
            phoneCode.setId(RandomCode.getId());
            phoneCode.setCodeStatus("0");
            phoneCode.setDeleteFlag("0");
            phoneCode.setPhone(mobilePhone);
            phoneCode.setCreateTime(new Date());
            phoneCode.setVerificationCode(code);
            int updates = phoneCodeMapper.insertSelective(phoneCode);
            if (updates > 0) {
                List<PhoneCode> list = new ArrayList<>();
                return new ResultJson(list);
            }
        } catch (Exception e) {
            logger.error(e.getMessage());
        }
        return new ResultJson(ExceptionEnum.SERVER_IS_BUSY.code, ExceptionEnum.SERVER_IS_BUSY.message);
    }

    /**
     * 登录
     *
     * @param jsonReqData
     * @return
     */
    public ResultJson wxSmallLogin(JSONObject jsonReqData) {

        try {

            String code = jsonReqData.getString("Code");
            JSONObject body = jsonReqData.getJSONObject("Body");

            // 获取opendid
            Map<String, Object> oauthMap = SmallWxUtil.oauth2GetOpenid(code);
            if (null == oauthMap || oauthMap.size() == 0) {
                return new ResultJson(ExceptionEnum.INVALID_CODE.code, ExceptionEnum.INVALID_CODE.message);
            }

            List<Map<String, Object>> list = new ArrayList<>();
            Map<String, Object> map = new HashMap<>(16);

            String openid = String.valueOf(oauthMap.get("openid"));
//            String sessionKey = String.valueOf(oauthMap.get("sessionKey"));
            map.put("openid", openid);
//            map.put("sessionKey", sessionKey);
            map.put("id", "");

            // 获取用户信息
//            Map<String, Object> wxUserInfo = SmallWxUtil.getWXUserInfo(openid);
            map.put("UserName", body.getString("truename"));
            String headimage = body.getString("headimage");
            map.put("headimage", headimage);

            ExpertInfoEntity expertInfoEntity = expertInfoMapper.queryExpertInfoByOpenId(openid);
            int isBand = 0;
            if (null != expertInfoEntity) {

                // 专家状态 0:注册 1:待审核 2:二次待审核 3:审核通过 4:禁用(不可评标) 5：修改待审核 6：修改待审核二次审核
                String status = String.valueOf(expertInfoEntity.getStatus());
                if ("7".equals(status) || "9".equals(status) || "20".equals(status)) {
                    return new ResultJson(ExceptionEnum.USER_DISABLED.code, ExceptionEnum.USER_DISABLED.message);
                }

                String user_id = expertInfoEntity.getUser_id();
                map.put("id", user_id);
                isBand = 1;

                Date date = new Date(System.currentTimeMillis() + EXPIRE_TIME);
                String expireTime = DateUtil.dateToString(date, "yyyy-MM-dd HH:mm:ss");

                String token = JudgeToken.generateToken();
//                String s = new String(token + "@" + 0 + "@wechat");
                // 加密token
//                String str = MyMD5Util.AESencrypt(s);
                map.put("token", token);
                map.put("expireTime", expireTime);

                expertInfoEntity.setToken(token);
                expertInfoEntity.setExpirationTime(expireTime);
                expertInfoEntity.setHeadImage(headimage);
                expertInfoMapper.updateTokenByOpenId(expertInfoEntity);
            }
            map.put("isBand", isBand);
            list.add(map);
            return new ResultJson(list);
        } catch (Exception e) {
            logger.error(e.getMessage());
            return new ResultJson(ExceptionEnum.SERVER_IS_BUSY.code, ExceptionEnum.SERVER_IS_BUSY.message);
        }
    }

    /**
     * 绑定
     *
     * @param jsonReqData
     * @return
     */
    public ResultJson binding(JSONObject jsonReqData) {

        try {

            List<Map<String, Object>> list = new ArrayList<>();
            Map<String, Object> map = new HashMap<>();

            String code = jsonReqData.getString("code");
            String mobilephone = jsonReqData.getString("mobilephone");
            // 校验验证码
            String checkPhoneCode = checkPhoneCode(mobilephone, code);
            if ("1".equals(checkPhoneCode)) {
                return new ResultJson(ExceptionEnum.VERIFIC_CODE_WAIT.code, ExceptionEnum.VERIFIC_CODE_WAIT.message);
            } else if ("2".equals(checkPhoneCode)) {
                return new ResultJson(ExceptionEnum.VERIFIC_CODE_ATYPISM.code, ExceptionEnum.VERIFIC_CODE_ATYPISM.message);
            } else if ("3".equals(checkPhoneCode)) {
                return new ResultJson(ExceptionEnum.VERIFIC_CODE_INVALID.code, ExceptionEnum.VERIFIC_CODE_INVALID.message);
            }

            String idNo = jsonReqData.getString("idNo");
            String openid = jsonReqData.getString("openid");
            String preIdNo = jsonReqData.getString("preIdNo");

            JSONObject body = jsonReqData.getJSONObject("Body");
            String headImage = body.getString("headimage");

            ExpertInfoEntity infoEntity = new ExpertInfoEntity();
            infoEntity.setId_no(idNo);
            ExpertInfoEntity infoOne = expertInfoMapper.queryByParam(infoEntity);
            infoEntity.setId_no(null);

            infoEntity.setMobilephone(mobilephone);
            ExpertInfoEntity infoTwo = expertInfoMapper.queryByParam(infoEntity);

            // 都不存在
            if (null == infoOne && null == infoTwo) {
                return new ResultJson(ExceptionEnum.NO_REGISTER.code, ExceptionEnum.NO_REGISTER.message);
                // 手机号存在，身份证不存在
            } else if (null == infoOne && null != infoTwo) {
                return new ResultJson(ExceptionEnum.NO_REGISTER.code, ExceptionEnum.NO_REGISTER.message);
                // 身份证存在，手机不存在
            } else if (null != infoOne && null == infoTwo) {
                String phone = infoOne.getMobilephone();
                return new ResultJson(ExceptionEnum.CARD_PHONE_BOUND.code, "您填写的身份证号已绑定手机尾号" + phone.substring(7) + ",请修改手机号完成绑定,如有问题请联系人工客服4000580203");
                // 都存在，即绑定
            } else if (null != infoOne && null != infoTwo) {
                String id = infoOne.getId();
                if (id.equals(infoTwo.getId())) {
                    infoEntity.setId(id);
                    infoEntity.setOpen_id(openid);
                    infoEntity.setPre_idNo(preIdNo);
                    infoEntity.setHeadImage(headImage);
                    expertInfoMapper.updateExpertInfoById(infoEntity);
                    map.put("id", infoOne.getUser_id());
                    map.put("isBand", 1);
                    list.add(map);
                    return new ResultJson(list);
                } else {
                    String phone = infoOne.getMobilephone();
                    return new ResultJson(ExceptionEnum.CARD_PHONE_BOUND.code, "您填写的身份证号已绑定手机尾号" + phone.substring(7) + ",请修改手机号完成绑定,如有问题请联系人工客服4000580203");
                }
            }
        } catch (Exception e) {
            logger.error(e.getMessage());
        }
        return new ResultJson(ExceptionEnum.SERVER_IS_BUSY.code, ExceptionEnum.SERVER_IS_BUSY.message);
    }

    /**
     * 注册
     *
     * @param jsonReqData
     * @return
     */
    public ResultJson register(JSONObject jsonReqData) {
        try {

            String mobilephone = jsonReqData.getString("mobilephone");
            String code = jsonReqData.getString("code");
            // 校验验证码
            String checkPhoneCode = checkPhoneCode(mobilephone, code);
            if ("1".equals(checkPhoneCode)) {
                return new ResultJson(ExceptionEnum.VERIFIC_CODE_WAIT.code, ExceptionEnum.VERIFIC_CODE_WAIT.message);
            } else if ("2".equals(checkPhoneCode)) {
                return new ResultJson(ExceptionEnum.VERIFIC_CODE_ATYPISM.code, ExceptionEnum.VERIFIC_CODE_ATYPISM.message);
            } else if ("3".equals(checkPhoneCode)) {
                return new ResultJson(ExceptionEnum.VERIFIC_CODE_INVALID.code, ExceptionEnum.VERIFIC_CODE_INVALID.message);
            }

            JSONObject body = jsonReqData.getJSONObject("Body");
            String headImage = body.getString("headimage");
            String userName = jsonReqData.getString("userName");
            String idNo = jsonReqData.getString("idNo");
            String sex = jsonReqData.getString("sex");
            String openid = jsonReqData.getString("openid");
            String preIdNo = jsonReqData.getString("preIdNo");
            String suffixIdNo = jsonReqData.getString("suffixIdNo");
            String recommendCode = jsonReqData.getString("recommendCode");
            if (StringUtils.isEmpty(recommendCode)) {
                recommendCode = "JSZB99";
            }
            String birthday = getBirthday(idNo);

            ExpertInfoEntity infoEntity = new ExpertInfoEntity();
            infoEntity.setId_no(idNo);
            ExpertInfoEntity infoOne = expertInfoMapper.queryByParam(infoEntity);
            infoEntity.setId_no(null);

            infoEntity.setMobilephone(mobilephone);
            ExpertInfoEntity infoTwo = expertInfoMapper.queryByParam(infoEntity);

            // 都不存在
            if (null == infoOne && null == infoTwo) {
                String userId = CommUtil.getKey();

                // 用户表
                UserEntity user = new UserEntity();
                user.setUser_id(userId);
                user.setLogin_code(mobilephone);
                user.setUser_name(userName);
                user.setPassword(MD5Util.reverse("123456"));
                user.setMobile(mobilephone);
                user.setCreateTime(new Date());
                user.setRole_id(SysConstants.ROLE_ID.EXPERT);//专家
                registerMapper.doRegister(user);

                // 专家表
                List<Map<String, Object>> list = new ArrayList<>();
                Map<String, Object> map = new HashMap<>();
                infoEntity.setId(CommUtil.getKey());
                infoEntity.setUser_id(userId);
                infoEntity.setUser_name(userName);
                infoEntity.setId_no(idNo);
                infoEntity.setSex(sex);
                infoEntity.setOpen_id(openid);
                infoEntity.setPre_idNo(preIdNo);
                infoEntity.setId_fileid(preIdNo);//原身份证
                infoEntity.setOld_id("身份证正面");
                infoEntity.setSuffix_idNo(suffixIdNo);
                infoEntity.setICBackFileId(suffixIdNo);
                infoEntity.setICBackName("身份证反面");
                infoEntity.setStatus(0L);
                infoEntity.setEnterFlag(3);
                infoEntity.setId_type("1");
                infoEntity.setReferrer_qrcode(recommendCode);
                infoEntity.setHeadImage(headImage);
                infoEntity.setCreate_time(new Date());
                infoEntity.setGrade(1L);
                infoEntity.setBirthday(DateUtil.formatStringByChar(birthday, "yyyy-MM-dd HH:mm:ss"));

                expertInfoMapper.saveExpertInfoBySmall(infoEntity);

                expertInfoMapper.saveExpertInfoBak(infoEntity);
                map.put("id", userId);
                list.add(map);
                return new ResultJson(list);
                // 手机号存在，身份证不存在
            } else if (null == infoOne && null != infoTwo) {
                return new ResultJson(ExceptionEnum.PHONE_BOUND.code, ExceptionEnum.PHONE_BOUND.message);

                // 身份证存在，手机不存在
            } else if (null != infoOne && null == infoTwo) {
                String phone = infoOne.getMobilephone();
                return new ResultJson(ExceptionEnum.CARD_PHONE_BOUND.code, "您填写的身份证号已绑定手机尾号" + phone.substring(7) + ",不能使用此身份证号重复注册,请直接完成绑定,如有疑问请联系人工客服4000580203");
                // 都存在
            } else if (null != infoOne && null != infoTwo) {
                String phone = infoOne.getMobilephone();
                return new ResultJson(ExceptionEnum.CARD_PHONE_BOUND.code, "您填写的身份证号已绑定手机尾号" + phone.substring(7) + ",不能使用此身份证号重复注册,请直接完成绑定,如有疑问请联系人工客服4000580203");
            }
        } catch (Exception e) {
            e.printStackTrace();
            logger.error(e.getMessage());
        }
        return new ResultJson(-1, "请求异常");
    }

    /**
     * 基本信息
     *
     * @param jsonReqData
     * @return
     */
    public ResultJson getOne(JSONObject jsonReqData) {

        List<Map<String, Object>> list = new ArrayList<>();
        Map<String, Object> map = new HashMap<>();
        String id = jsonReqData.getString("id");
        ExpertInfoEntity infoEntity = new ExpertInfoEntity();
        infoEntity.setUser_id(id);

        // 个人信息
        ExpertInfoEntity entity = expertInfoMapper.getExpertInfoByUserId(infoEntity);

        map.put("id", entity.getId());
        map.put("userId", entity.getUser_id());
        map.put("userName", entity.getUser_name());
        String mobilePhone = entity.getMobilephone();
        String headImage = entity.getHeadImage();
        map.put("headImage", headImage);

        // 手机号处理
        String start = mobilePhone.substring(0, 3);
        String end = mobilePhone.substring(mobilePhone.length() - 4, mobilePhone.length());
        StringBuilder sb = new StringBuilder();
        sb.append(start).append("****").append(end);
        String result = sb.toString();
        map.put("mobilePhone", result);

        map.put("status", entity.getStatus());
        map.put("userNum", 1);

        if (StringUtils.isEmpty(entity.getSex())) {
            map.put("userNum", 0);
        }
        if (StringUtils.isEmpty(entity.getMobilephone())) {
            map.put("userNum", 0);
        }
        if (StringUtils.isEmpty(entity.getProvince()) || StringUtils.isEmpty(entity.getCity())) {
            map.put("userNum", 0);
        }
        if (StringUtils.isEmpty(entity.getSuffix_idNo())) {
            map.put("userNum", 0);
        }
        if (StringUtils.isEmpty(entity.getId_type())) {
            map.put("userNum", 0);
        }

        // 学历数量
        int educationNum = educationInfoMapper.queryEducationNumById(id);
        map.put("educationNum", educationNum);
        // 职称数量
        int titleInfoNum = titleInfoMapper.queryTitleInfoNumById(id);
        map.put("titleInfoNum", titleInfoNum);
        // 执业资格数量
        int qualificationNum = proQualificationMapper.queryQualificationNumById(id);
        map.put("qualificationNum", qualificationNum);
        // 评标数量
        int evaluationNum = bidEvaluationMapper.queryEvaluationNumById(id);
        map.put("evaluationNum", evaluationNum);
        // 单位数量

        int companyNum = 1;
        String company = entity.getCompany();
        if (StringUtils.isEmpty(company)) {
            companyNum = 0;
        }
        map.put("companyNum", companyNum);

        // 查询专家职称等级是否满足中级满8年以上
        map.put("isHighJobStatus", false);
        List<Map<String, Object>> maps = expertInfoMapper.queryTitleStatus(id);
        if (null != maps && maps.size() > 0) {
            map.put("isHighJobStatus", true);
        }

        String auditTime = "";
        String reason = "";
        Long status = entity.getStatus();
        if (0L == status || 4L == status) {
            ExpertAuditEntity expertAuditEntity = expertInfoMapper.getExpertAuditEntityByUserId(entity.getUser_id());
            if (null != expertAuditEntity) {
                auditTime = DateUtil.getFormatDateTime("yyyy-MM-dd HH:ss:mm", expertAuditEntity.getAudit_time());
                reason = expertAuditEntity.getReason();
            }
        }
        map.put("auditTime", auditTime);
        map.put("reason", reason);

        list.add(map);
        return new ResultJson(list);
    }

    /**
     * 提交审核
     *
     * @param jsonReqData
     * @return
     */
    public ResultJson submitExamine(JSONObject jsonReqData) {

        List<Map<String, Object>> list = new ArrayList<>();
        Map<String, Object> map = new HashMap<>();
        String id = jsonReqData.getString("id");
        // 个人信息
        try {

            // 查询专家职称等级是否满足中级满8年以上
            List<Map<String, Object>> maps = expertInfoMapper.queryTitleStatus(id);
            if (null == maps || maps.size() == 0) {
                return new ResultJson(ExceptionEnum.TITLE_NUM_OVER.code, ExceptionEnum.TITLE_NUM_OVER.message);
            }

            // 专家状态 0:注册 1:待审核 2:二次待审核 3:注册审核通过 4:禁用(不可评标) 5：修改待审核
            // 6：修改待审核二次审核  7：黑名单  8：修改审核通过 9：出库   10：小程序修改待提交 20：待出库
            ExpertInfoEntity infoEntity = new ExpertInfoEntity();
            infoEntity.setUser_id(id);
            ExpertInfoEntity expertInfoEntity = expertInfoMapper.getExpertInfoByUserIdOrId(infoEntity);
            String status = "1";
            if (10L == expertInfoEntity.getStatus() || 4L == expertInfoEntity.getStatus()) {
                status = "5";
            }

            String modifyTime = DateUtil.dateToString(new Date(), "yyyy-MM-dd HH:mm:ss");
            int updates = expertInfoMapper.submitExamine(id, status, modifyTime);
            if (updates > 0) {
                map.put("id", id);
                list.add(map);
                return new ResultJson(list);
            }
        } catch (Exception e) {
            logger.error(e.getMessage());
        }
        return new ResultJson(ExceptionEnum.SERVER_IS_BUSY.code, ExceptionEnum.SERVER_IS_BUSY.message);
    }

    /**
     * 获取基本信息-个人信息
     *
     * @return
     */
    public ResultJson personalInformation(JSONObject jsonReqData) {
        List<Map<String, Object>> list = new ArrayList<>();
        Map<String, Object> map = new HashMap<>();
        String id = jsonReqData.getString("id");
        ExpertInfoEntity infoEntity = new ExpertInfoEntity();
        infoEntity.setUser_id(id);
        ExpertInfoEntity entity = expertInfoMapper.getExpertInfoByUserId(infoEntity);

        map.put("id", entity.getId());
        map.put("userId", entity.getUser_id());
        map.put("userName", entity.getUser_name());
        map.put("sex", entity.getSex());
        map.put("mobilePhone", entity.getMobilephone());
        map.put("email", entity.getEmail());
        map.put("province", entity.getProvince());
        map.put("city", entity.getCity());
        map.put("idType", entity.getId_type());
        map.put("idNo", entity.getId_no());
        map.put("preIdNo", entity.getPre_idNo());
        map.put("suffixIdNo", entity.getSuffix_idNo());
        map.put("status", entity.getStatus());
        map.put("grade", entity.getGrade());
        map.put("district", entity.getDistrict());

        list.add(map);
        return new ResultJson(list);
    }

    /**
     * 修改个人新信息 专家修改资料审核
     *
     * @param educationInfo
     * @return
     */
    public ResultJson updateExpertInfo(ExpertInfoEntity educationInfo) {
        try {
            ExpertInfoEntity expertInfoByUserIdOrId = expertInfoMapper.getExpertInfoByUserIdOrId(educationInfo);
            long status = expertInfoByUserIdOrId.getStatus();
            if (3 == status || 8 == status) {
                educationInfo.setStatus(10L);
            }

            List<Map<String, Object>> list = new ArrayList<>();
            Map<String, Object> map = new HashMap<>();
            int updates = expertInfoMapper.updateExpertInfoById(educationInfo);
            if (updates > 0) {
                if (3 == status || 8 == status || 4 == status || 10 == status) {
                    try {
                        infoRecord(educationInfo, expertInfoByUserIdOrId);
                    } catch (Exception e) {
                        System.out.println("记录出错!");
                    }
                }
                map.put("id", educationInfo.getId());
                list.add(map);
                return new ResultJson(list);
            }
        } catch (Exception e) {
            logger.error(e.getMessage());
        }
        return new ResultJson(ExceptionEnum.SERVER_IS_BUSY.code, ExceptionEnum.SERVER_IS_BUSY.message);
    }

    /**
     * 记录个人信息修改记录
     *
     * @param upd
     * @param befoUpd
     */
    private void infoRecord(ExpertInfoEntity upd, ExpertInfoEntity befoUpd) {//个人信息记录
        if (befoUpd != null && upd != null) {


            UserEntity userEntity = userMapper.selectUserById(befoUpd.getUser_id());
            String role_id;
            if (userEntity != null) {
                role_id = StringUtils.toStringNull(userEntity.getRole_id());
            } else {
                role_id = "无";
            }

            ;
            String upd_user_name = upd.getUser_name();// 姓名
            String upd_sex = upd.getSex();// 性别
            String upd_mobilephone = upd.getMobilephone();// 手机号码
            String upd_email = upd.getEmail();//电子邮箱
            String upd_aArea = upd.getDistrict();//所在行政区域
            String upd_id_type = upd.getId_type();// 证件类型
            String upd_id_no = upd.getId_no();// 证件号码
            String upd_pre_idNo = upd.getPre_idNo();//身份证正面
            String upd_suffix_idNo = upd.getSuffix_idNo();//身份证反面
            Long upd_grade = upd.getGrade();
            String upd_grade_str = "地方专家";
            if (upd_grade == 2) {
                upd_grade_str = "国家专家";
            }

            //-------------------------------------------------------//
            String befoUpd_user_name = befoUpd.getUser_name();// 姓名
            String befoUpd_sex = befoUpd.getSex();// 性别
            String befoUpd_mobilephone = befoUpd.getMobilephone();// 手机号码
            String befoUpd_email = befoUpd.getEmail();//电子邮箱
            String befoUpd_aArea = befoUpd.getDistrict();//所在行政区域
            String befoUpd_id_type = befoUpd.getId_type();// 证件类型
            String befoUpd_id_no = befoUpd.getId_no();// 证件号码
            String befoUpd_pre_idNo = befoUpd.getPre_idNo();//身份证正面
            String befoUpd_suffix_idNo = befoUpd.getSuffix_idNo();//身份证反面
            Long befoUpd_grade = befoUpd.getGrade();
            String befoUpd_grade_str = "地方专家";
            if (befoUpd_grade == 2) {
                befoUpd_grade_str = "国家专家";
            }

            if (StringUtils.isNotEmpty(upd_user_name) && !StringUtils.toStringNull(upd_user_name).equalsIgnoreCase(StringUtils.toStringNull(befoUpd_user_name))) {

                addExpertUpdateRecord(befoUpd.getUser_id(), "个人姓名", StringUtils.toStringNull(befoUpd_user_name), StringUtils.toStringNull(upd_user_name), role_id, befoUpd.getUser_id(), "个人信息", 0);
            }
            if (StringUtils.isNotEmpty(upd_sex) && !StringUtils.toStringNull(upd_sex).equalsIgnoreCase(StringUtils.toStringNull(befoUpd_sex))) {
                addExpertUpdateRecord(befoUpd.getUser_id(), "个人性别", StringUtils.toStringNull(befoUpd_sex).equals("1") ? "男" : "女", StringUtils.toStringNull(upd_sex).equals("1") ? "男" : "女", role_id, befoUpd.getUser_id(), "个人信息", 0);
            }
//            if (StringUtils.isNotEmpty(upd_mobilephone) && !StringUtils.toStringNull(upd_mobilephone).equalsIgnoreCase(StringUtils.toStringNull(befoUpd_mobilephone))) {
//                addExpertUpdateRecord(befoUpd.getUser_id(), "个人手机号", StringUtils.toStringNull(befoUpd_mobilephone), StringUtils.toStringNull(upd_mobilephone), role_id, befoUpd.getUser_id(), "个人信息", 0);
//            }
            if (StringUtils.isNotEmpty(upd_email) && !StringUtils.toStringNull(upd_email).equalsIgnoreCase(StringUtils.toStringNull(befoUpd_email))) {
                addExpertUpdateRecord(befoUpd.getUser_id(), "个人邮箱", StringUtils.toStringNull(befoUpd_email), StringUtils.toStringNull(upd_email), role_id, befoUpd.getUser_id(), "个人信息", 0);
            }
            if (StringUtils.isNotEmpty(upd_aArea) && !StringUtils.toStringNull(upd_aArea).equalsIgnoreCase(StringUtils.toStringNull(befoUpd_aArea))) {
                addExpertUpdateRecord(befoUpd.getUser_id(), "个人行政区域", StringUtils.toStringNull(befoUpd_aArea), StringUtils.toStringNull(upd_aArea), role_id, befoUpd.getUser_id(), "个人信息", 0);
            }
            if (!upd_grade_str.equals(befoUpd_grade_str)) {
                addExpertUpdateRecord(befoUpd.getUser_id(), "专家级别", befoUpd_grade_str, upd_grade_str, role_id, befoUpd.getUser_id(), "个人信息", 0);
            }
            if (StringUtils.isNotEmpty(upd_id_type) && !StringUtils.toStringNull(upd_id_type).equalsIgnoreCase(StringUtils.toStringNull(befoUpd_id_type))) {
                Map<String, String> idMap = new HashMap<String, String>();
                idMap.put("1", "身份证");
                idMap.put("2", "警官证");
                idMap.put("3", "军人证");
                idMap.put("4", "其他");
                String one = idMap.get(befoUpd_id_type);
                String two = idMap.get(upd_id_type);
                addExpertUpdateRecord(befoUpd.getUser_id(), "个人证件类型", StringUtils.toStringNull(one), StringUtils.toStringNull(two), role_id, befoUpd.getUser_id(), "个人信息", 0);
            }
            if (StringUtils.isNotEmpty(upd_id_no) && !StringUtils.toStringNull(upd_id_no).equalsIgnoreCase(StringUtils.toStringNull(befoUpd_id_no))) {
                addExpertUpdateRecord(befoUpd.getUser_id(), "个人身份证号码", StringUtils.toStringNull(befoUpd_id_no), StringUtils.toStringNull(upd_id_no), role_id, befoUpd.getUser_id(), "个人信息", 0);
            }
            if (StringUtils.isNotEmpty(upd_pre_idNo) && !StringUtils.toStringNull(upd_pre_idNo).equalsIgnoreCase(StringUtils.toStringNull(befoUpd_pre_idNo))) {

                String befoStyle = "<a style=\"color:blue\" onclick=\"javascript:picPreview(530,450,'/GZZJK/" + StringUtils.toStringNull(befoUpd_pre_idNo) + "')\">" + (befoUpd_pre_idNo) + "</a>";
                String afterStyle = "<a style=\"color:blue\" onclick=\"javascript:picPreview(530,450,'/GZZJK/" + StringUtils.toStringNull(upd_pre_idNo) + "')\">" + (upd_pre_idNo) + "</a>";

                addExpertUpdateRecord(befoUpd.getUser_id(), "个人身份证正面照", StringUtils.toStringNull(befoStyle), (afterStyle), role_id, befoUpd.getUser_id(), "个人信息", 0);
            }
            if (StringUtils.isNotEmpty(upd_suffix_idNo) && !StringUtils.toStringNull(upd_suffix_idNo).equalsIgnoreCase(StringUtils.toStringNull(befoUpd_suffix_idNo))) {
                String befoStyle = "<a style=\"color:blue\" onclick=\"javascript:picPreview(530,450,'/GZZJK/" + StringUtils.toStringNull(befoUpd_suffix_idNo) + "')\">" + StringUtils.toStringNull(befoUpd_suffix_idNo) + "</a>";
                String afterStyle = "<a style=\"color:blue\" onclick=\"javascript:picPreview(530,450,'/GZZJK/" + StringUtils.toStringNull(upd_suffix_idNo) + "')\">" + StringUtils.toStringNull(upd_suffix_idNo) + "</a>";
                addExpertUpdateRecord(befoUpd.getUser_id(), "个人身份证反面照", StringUtils.toStringNull(befoStyle), (afterStyle), role_id, befoUpd.getUser_id(), "个人信息", 0);
            }


        }
    }


    /**
     * 新增专家信息修改记录
     *
     * @param user_id        用户编号
     * @param item           修改项
     * @param content_before 修改前内容
     * @param content_after  修改后内容
     * @param modify_role    修改角色
     * @param modify_user    修改人
     * @param modify_reason  修改原因
     * @param batch_number   修改批次编号
     */
    public void addExpertUpdateRecord(String user_id, String item, String content_before, String content_after, String modify_role, String modify_user, String modify_reason, int batch_number) {
        ExpertUpdateRecordEntity record = new ExpertUpdateRecordEntity();
        record.setId(CommUtil.getKey());
        record.setUser_id(user_id);
        record.setItem(item);
        record.setContent_before(content_before);
        record.setContent_after(content_after);
        record.setModify_role(modify_role);
        record.setModify_user(modify_user);
        record.setModify_reason(modify_reason);
        record.setBatch_number(batch_number);
        record.setModify_time(new Date());
        record.setDelete_flag(0);
        expertInfoMapper.addExpertUpdateRecord(record);
    }

    /**
     * 修改手机
     *
     * @param expertInfoEntity
     * @return
     */
    public ResultJson modifyPhone(ExpertInfoEntity expertInfoEntity) {

        try {

            List<Map<String, String>> list = new ArrayList<>();
            String user_id = expertInfoEntity.getUser_id();
            // 查询手机号是否已存在
            String mobilephone = expertInfoEntity.getMobilephone();
            ExpertInfoEntity info = expertInfoMapper.queryInfoByMobilephone(mobilephone);
            if (null != info && !user_id.equals(info.getUser_id())) {
                return new ResultJson(ExceptionEnum.PHONE_BOUND.code, ExceptionEnum.PHONE_BOUND.message);
            }

            ExpertInfoEntity expertInfoByUserIdOrId = expertInfoMapper.getExpertInfoByUserIdOrId(expertInfoEntity);
            Long status = expertInfoByUserIdOrId.getStatus();
            if (3 == status || 8 == status) {
                expertInfoEntity.setStatus(5L);
            }
            // 校验验证码
            String phoneCode = expertInfoEntity.getPhoneCode();
            String checkPhoneCode = checkPhoneCode(mobilephone, phoneCode);
            if ("1".equals(checkPhoneCode)) {
                return new ResultJson(ExceptionEnum.VERIFIC_CODE_WAIT.code, ExceptionEnum.VERIFIC_CODE_WAIT.message);
            } else if ("2".equals(checkPhoneCode)) {
                return new ResultJson(ExceptionEnum.VERIFIC_CODE_ATYPISM.code, ExceptionEnum.VERIFIC_CODE_ATYPISM.message);
            } else if ("3".equals(checkPhoneCode)) {
                return new ResultJson(ExceptionEnum.VERIFIC_CODE_INVALID.code, ExceptionEnum.VERIFIC_CODE_INVALID.message);
            }

            //修改
            int updates = expertInfoMapper.updateInfoByUserId(expertInfoEntity);
            if (updates > 0) {

                UserEntity userEntity = userMapper.selectUserById(expertInfoByUserIdOrId.getUser_id());
                String beforePhone = expertInfoByUserIdOrId.getMobilephone();
                if (!StringUtils.toStringNull(mobilephone).equalsIgnoreCase(StringUtils.toStringNull(beforePhone))) {
                    addExpertUpdateRecord(expertInfoByUserIdOrId.getUser_id(), "个人手机号", StringUtils.toStringNull(beforePhone), StringUtils.toStringNull(mobilephone), userEntity.getRole_id(), expertInfoByUserIdOrId.getUser_id(), "个人信息", 0);
                }

                Map<String, String> map = new HashMap<>();
                map.put("mobilephone", mobilephone);
                list.add(map);
                return new ResultJson(list);
            }
        } catch (Exception e) {
            logger.error(e.getMessage());
        }
        return new ResultJson(ExceptionEnum.SERVER_IS_BUSY.code, ExceptionEnum.SERVER_IS_BUSY.message);
    }

    /**
     * 根据token查询用户
     *
     * @param token
     * @return
     */
    public ExpertInfoEntity queryExpertByToken(String token) {
        return expertInfoMapper.queryExpertByToken(token);
    }


    /**
     * 发送短信验证码
     *
     * @param
     * @return
     */
    public String sendSMs(String mobilePhone) {
        MWSmsUtil util = new MWSmsUtil();
        SmsRecordEntity smsRecordEntity = new SmsRecordEntity();
        smsRecordEntity.setSms_id(CommUtil.getKey());
        smsRecordEntity.setSms_mobile(mobilePhone);
        //todo
        String smscode = String.valueOf((int) ((Math.random() * 9 + 1) * 100000));
        smsRecordEntity.setSms_content("<" + smscode + "> (专家库管理平台验证码)，有效期为3分钟。为了保护您的账号安全，验证短信请勿转告他人。");
        int result = util.singleSend(userId, userPwd, masterIpAddress, ipAddress1, ipAddress2, smsRecordEntity);
        if (result == 0) {
            return smscode;
        } else {
            return null;
        }

    }

    /**
     * 注册 绑定 验证码校验
     *
     * @param mobilephone
     * @param code
     * @return
     */
    public String checkPhoneCode(String mobilephone, String code) {

        String checkPhoneCodeStatus = "0";
        PhoneCode phoneCode = phoneCodeMapper.queryByPhone(mobilephone);
        if (null == phoneCode) {
            checkPhoneCodeStatus = "1";
        } else {
            String verificationCode = phoneCode.getVerificationCode();
            if (!code.equals(verificationCode)) {
                checkPhoneCodeStatus = "2";
            } else {
                long currentTime = System.currentTimeMillis();
                long createTime = phoneCode.getCreateTime().getTime();
                long diff = (currentTime - createTime) / 1000 / 60;
                if (diff > 15) {
                    checkPhoneCodeStatus = "3";
                }
            }
        }
        return checkPhoneCodeStatus;
    }

    /**
     * 获取出生日期  yyyy年MM月dd日
     *
     * @param IDCard
     * @return
     */
    public static String getBirthday(String IDCard) {
        String birthday = "";
        String year = "";
        String month = "";
        String day = "";
        if (!StringUtils.isEmpty(IDCard)) {
            //15位身份证号
            if (IDCard.length() == 15) {
                // 身份证上的年份(15位身份证为1980年前的)
                year = "19" + IDCard.substring(6, 8);
                //身份证上的月份
                month = IDCard.substring(8, 10);
                //身份证上的日期
                day = IDCard.substring(10, 12);
                //18位身份证号
            } else if (IDCard.length() == 18) {
                // 身份证上的年份
                year = IDCard.substring(6).substring(0, 4);
                // 身份证上的月份
                month = IDCard.substring(10).substring(0, 2);
                //身份证上的日期
                day = IDCard.substring(12).substring(0, 2);
            }
            birthday = year + "-" + month + "-" + day + " 00:00:00";
        }
        return birthday;
    }

}
