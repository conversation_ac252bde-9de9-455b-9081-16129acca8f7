package com.hzw.system.api.domain.dto;

import lombok.Data;

import java.io.Serializable;

/**
 *
 * 专家新建用户
 * @ClassName:ExpertUserDTO
 * @Auther: lijinxin
 * @Description: 专家新建用户
 * @Date: 2023/7/27 14:57
 * @Version: v1.0
 */

@Data
public class ExpertUserDTO implements Serializable {


    /**
     * 手机号
     */
    private String phonenumber;

    /**
     * 姓名
     */
    private String userName;

    /**
     * 邮箱
     */
    private String email;


    /**
     * 邮箱
     */
    private Integer sex;

    /**
     * 证件号
     */
    private String idNum;


    /**
     * 证件类型
     */
    private Integer idType;


}
