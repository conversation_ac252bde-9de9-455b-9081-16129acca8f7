package com.hzw.sunflower.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzw.sunflower.common.redis.RedisCache;
import com.hzw.sunflower.dao.DeptdataToNccMapper;
import com.hzw.sunflower.entity.DeptdataToNcc;
import com.hzw.sunflower.service.DeptDataToNccService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/10/10 10:33
 * @Version 1.0
 */
@Service
public class DeptdataToNccServiceImpl extends ServiceImpl<DeptdataToNccMapper, DeptdataToNcc> implements DeptDataToNccService {

    @Autowired
    private RedisCache redisCache;

    @Resource
    private DeptdataToNccMapper deptdataToNccMapper;

    @Override
    public List<DeptdataToNcc> queryDeptdataToNccTreeList(){
        List<DeptdataToNcc>  tree = redisCache.getCacheList("cache_account_dept_ncc");
        if (tree!=null&&tree.size()>0) {

        }else {
            tree=new ArrayList<>();
            LambdaQueryWrapper<DeptdataToNcc> answerWrapper = new LambdaQueryWrapper<>();
            answerWrapper.eq( DeptdataToNcc::getIsUse,0 );
            answerWrapper.orderByAsc(DeptdataToNcc::getDeptCode);
            answerWrapper
                    .and(x->x.isNull(DeptdataToNcc::getParentDeptName).or().eq(DeptdataToNcc::getParentDeptName,""));
            List<DeptdataToNcc> deptdataToNccList = deptdataToNccMapper.selectList(answerWrapper);

            for (DeptdataToNcc deptdataToNcc : deptdataToNccList) {
                tree.add(deptdataToNcc);

                List<DeptdataToNcc> selectList = deptdataToNccMapper.selectList(new LambdaQueryWrapper<DeptdataToNcc>()
                        .eq(DeptdataToNcc::getParentDeptName, deptdataToNcc.getDeptName())
                        .eq(DeptdataToNcc::getIsUse,0).orderByAsc(DeptdataToNcc::getDeptCode));
                tree.addAll(selectList);
            }
            redisCache.setCacheList("cache_account_dept_ncc",tree);
        }
        return tree;
    }
}
