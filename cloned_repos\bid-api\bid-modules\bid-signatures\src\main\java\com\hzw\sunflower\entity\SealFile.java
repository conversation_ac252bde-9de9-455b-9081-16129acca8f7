package com.hzw.sunflower.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.hzw.sunflower.common.BaseBean;
import io.swagger.annotations.ApiModel;
import java.io.Serializable;
import java.util.Date;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
* Created by pj on 2023/11/03
*/
@ApiModel("t_seal_file表")
@TableName("t_seal_file")
@Data
public class SealFile  extends BaseBean implements Serializable {
    private Long id;

    /**
     * 申请id
     *
     * @mbg.generated
     */
    @ApiModelProperty("申请id")
    private Long applyId;

    /**
     * 原始文件
     *
     * @mbg.generated
     */
    @ApiModelProperty("原始文件")
    private Long originalFile;

    /**
     * 盖章文件
     *
     * @mbg.generated
     */
    @ApiModelProperty("盖章文件")
    private Long sealFile;



    private static final long serialVersionUID = 1L;
}