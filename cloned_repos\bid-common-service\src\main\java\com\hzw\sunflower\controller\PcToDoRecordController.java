package com.hzw.sunflower.controller;

import com.hzw.sunflower.common.BaseController;
import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.entity.AppToDoRecord;
import com.hzw.sunflower.security.utils.SecurityUtils;
import com.hzw.sunflower.service.AppToDoRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 *
 * <AUTHOR>
 * @since 2025-01-15
 *
 */
@Api(tags = "待处理事项表 服务")
@RestController
@RequestMapping("/todorecord")
public class PcToDoRecordController extends BaseController {

    @Autowired(required=false)
    private AppToDoRecordService appToDoRecordService;

    @ApiOperation(value = "新增已办数据")
    @ApiImplicitParam(name = "req", value = "已办 记录", required = true, dataType = "PCToDoRecordReq", paramType = "body")
    @PostMapping(value = "/add")
    public Result<Boolean> addToDoRecordPC(@RequestBody AppToDoRecord record) {
        record.setCreatedUserDepartId(SecurityUtils.getJwtUser().getUser().getDepartId());
        // PC端默认为2
        record.setEquipment(2);
        return Result.okOrFailed(appToDoRecordService.save(record));
    }


}
