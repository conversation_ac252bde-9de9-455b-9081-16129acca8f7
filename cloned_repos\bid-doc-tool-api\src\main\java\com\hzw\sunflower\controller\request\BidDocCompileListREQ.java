package com.hzw.sunflower.controller.request;

import com.hzw.sunflower.common.BaseCondition;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;



/**
 * @ClassName:BidDocCompileListREQ
 * @Auther: lijinxin
 * @Description: 文件编制列表
 * @Date: 2024/6/28 10:33
 * @Version: v1.0
 */
@Data
public class BidDocCompileListREQ extends BaseCondition {

    @ApiModelProperty(value = "关键字")
    private String keywords;

    @ApiModelProperty(value = "类型")
    private Integer type;

    @ApiModelProperty(value = "数据权限")
    private String dataScope;

    @ApiModelProperty(value = "状态")
    private Integer status;

    @ApiModelProperty(value = "开始时间")
    private String beginTime;
    @ApiModelProperty(value = "结束时间")
    private String endTime;

}
