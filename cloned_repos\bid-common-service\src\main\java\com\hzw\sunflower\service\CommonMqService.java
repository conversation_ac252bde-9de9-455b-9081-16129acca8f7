package com.hzw.sunflower.service;

import com.hzw.sunflower.dto.AppOaMsgDto;
import com.hzw.sunflower.dto.AppOaNoticeDto;
import com.hzw.sunflower.entity.PendingItem;

import java.util.List;

/**
 * @description: rabbitq队列服务
 * @author: fanqh
 * @create: 2023-09-18 10:20
 * @Version 1.0.0
 **/
public interface CommonMqService {

    /**
     * 发送oa待办消息
     * @param dto
     */
    void sendOaMsg(AppOaMsgDto dto);

    /**
     * 发送oa待阅消息
     * @param msg
     * @param pendingItem
     */
    void sendOaReadMsg(String msg, PendingItem pendingItem);

    /**
     * 发送oa通知消息
     *userId
     * @param appNoticeDto
     */
    void sendOaNoticeMsg(List<String> recipientList, AppOaNoticeDto appNoticeDto);
}
