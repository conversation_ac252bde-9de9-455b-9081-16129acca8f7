package com.hzw.sunflower.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hzw.sunflower.controller.request.ProcessRecordReq;
import com.hzw.sunflower.entity.CalibrationProcessRecord;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2021/7/21 17:04
 * @description：定标流程mapper
 * @modified By：`
 * @version: 1.0
 */
public interface ProcessRecordMapper extends BaseMapper<CalibrationProcessRecord> {

    List<CalibrationProcessRecord> queryProcessRecordExpertFee(@Param("req") ProcessRecordReq req);


    CalibrationProcessRecord queryEndTime(@Param("req") ProcessRecordReq req);

    List<CalibrationProcessRecord> selectProcessRecordList(@Param("req") ProcessRecordReq req);
}
