package com.hzw.sunflower.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.constant.MessageConstants;
import com.hzw.sunflower.controller.request.SealFileSignReq;
import com.hzw.sunflower.dao.SealFileMapper;
import com.hzw.sunflower.dto.SealFileDTO;
import com.hzw.sunflower.entity.JwtUser;
import com.hzw.sunflower.entity.OssFile;
import com.hzw.sunflower.entity.SealFile;
import com.hzw.sunflower.entity.SealFileLog;
import com.hzw.sunflower.entity.condition.SealFileCondition;
import com.hzw.sunflower.exception.ExceptionEnum;
import com.hzw.sunflower.exception.SunFlowerException;
import com.hzw.sunflower.service.OssFileService;
import com.hzw.sunflower.service.SealFileLogService;
import com.hzw.sunflower.service.SealFileService;
import com.hzw.sunflower.util.OssUtil;
import com.hzw.sunflower.util.oss.OssFileStorage;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.List;


/**
* SealFileService接口实现
*
* <AUTHOR>
* @version 1.0.0 2021-04-07
*/
@Service
public class SealFileServiceImpl extends ServiceImpl<SealFileMapper, SealFile> implements SealFileService {


    @Autowired
    private OssFileService ossFileService;


    @Autowired
    private SealFileService sealFileService;

    @Autowired
    private SealFileLogService sealFileLogService;

    @Value("${oss.active}")
    private String ossType;

    @Value("${files.sign.path}")
    private String signPath;

    /**
    * 修改
    *
    * @param sealFile
    * @return 是否成功
    */
    @Override
    public Boolean updateInfo(SealFileDTO sealFile) {
        return this.updateById(sealFile);
    }


    @Override
    public Result<SealFile> completeSign(SealFileSignReq req, JwtUser user) {
        String fileName = req.getFileId() + ".pdf";
        String filePath = signPath + fileName;
        File file = new File(filePath);
        if (!file.exists()) {
            return Result.failed(MessageConstants.MESSAGE_SERVER_ERROR);
        }
        Result r = new Result();
        try {
            // 更新oss文件
            OssFileStorage storage = OssUtil.getOssFileStorage(ossType);
            String key = storage.uploadFileByPath(filePath);
            if (StringUtils.isNotBlank(key)) {
                OssFile ossFile = new OssFile();
                ossFile.setOssFileKey(key);
                ossFile.setOssFileName(fileName);
                ossFileService.save(ossFile);

                // 增加签章文件
                SealFile sealFile = sealFileService.getById(req.getId());
                if (null != sealFile) {
                    sealFile.setSealFile(ossFile.getId());
                    sealFileService.updateById(sealFile);

                    // 新增签章记录
                    SealFileLog sealFileLog = new SealFileLog();
                    sealFileLog.setFileId(sealFile.getId());
                    sealFileLog.setSealFile(ossFile.getId());
                    sealFileLogService.getBaseMapper().insert(sealFileLog);
                }
                r =  Result.ok(sealFile);
            } else {
                r =  Result.failed(MessageConstants.MESSAGE_SERVER_ERROR);
            }
        } catch (Exception e) {
            log.debug(e.toString());
        } finally {
            file.delete();
        }
        return r;
    }



}