package com.hzw.sunflower.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hzw.sunflower.common.BaseBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@TableName("t_bond_offline_company")
@ApiModel(value = "BondOfflineCompany对象", description = "保证金线下项目供应商表")
public class BondOfflineCompany extends BaseBean implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("项目id")
    @TableField("project_id")
    private Long projectId;

    @ApiModelProperty(value = "标段id")
    @TableField("section_id")
    private Long sectionId;

    @ApiModelProperty(value = "线下公司id")
    @TableField("offline_company_id")
    private Long offlineCompanyId;

    @ApiModelProperty(value = "申请户名")
    @TableField("apply_company_name")
    private String applyCompanyName;

    @ApiModelProperty(value = "支付凭证")
    @TableField("pay_file_id")
    private Long payFileId;

}
