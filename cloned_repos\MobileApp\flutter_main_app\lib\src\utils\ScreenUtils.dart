import "package:flutter/material.dart";

class ScreenUtils {
  
  static MediaQueryData _mediaQueryData;

  static double screenWidth;

  static double screenHeight;

  static EdgeInsets padding;

  static ScreenUtils _instance = new ScreenUtils(); 

  static ScreenUtils getInstance() {
    return _instance;
  }

  void init(BuildContext context) {
    MediaQueryData mediaQuery = MediaQuery.of(context);
    _mediaQueryData = mediaQuery;
    screenWidth = _mediaQueryData.size.width;
    screenHeight = _mediaQueryData.size.height;
    padding = _mediaQueryData.padding; // dafault as EdgeInsets(0.0, 44.0, 0.0, 34.0)
  }
}
