<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hzw.sunflower.dao.UserAuthorizationProjectMapper">

    <update id="updateUserPersonAuthorization">
        update t_user_identity u set u.legal_person_authorization = #{legalPersonAuthorization}
        where u.user_id = #{userId} and u.identity = #{identity}
    </update>
</mapper>
