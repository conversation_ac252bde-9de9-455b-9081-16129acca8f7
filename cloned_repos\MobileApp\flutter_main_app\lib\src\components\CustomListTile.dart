///***
/// * 自定义的 ListTile
/// *
/// * leading + title + infos + trailing
/// * 除 title 以外，均可省略
/// */

import 'package:flutter/material.dart';

import 'package:flutter_main_app/src/constants/theme.dart';
import 'package:flutter_main_app/src/utils/ScreenUtils.dart';

class CustomListTile extends StatelessWidget {

  /// * 右侧内边距
  static const double _rightPadding = 18.0;
  /// * infos 组件间的水平间距
  static const double _infoWidgetHorizontalPadding = 6.0;

  const CustomListTile({
    Key key,
    @required this.title,
    this.fontSize = themeFontSize,
    this.color = themeTextColor,
    this.onTap,
    this.padding = const EdgeInsets.only(left: 15.0),
    this.minHeight = 40.0,
    this.backgroundColor = themeContentBackgroundColor,
    this.leading,
    this.infos,
    this.trailing,
    this.isFirstChild = false,
  }) : assert(title != null),
    assert(fontSize != null && fontSize >= 10.0),
    assert(color != null),
    assert(padding != null),
    assert(minHeight != null && minHeight > 0.0),
    assert(infos == null || infos.length > 0),
    assert(isFirstChild != null),
    super(key: key);

  /// * 标题
  final String title;
  /// * 标题字号
  final double fontSize;
  /// * 标题颜色
  final Color color;

  /// * 整个组件的点击事件回调函数
  final Function onTap;

  /// * 内边距
  final EdgeInsets padding;

  /// * 最小高度
  final double minHeight;

  /// * 背景颜色
  final Color backgroundColor;

  /// * 头部图标组件
  final Widget leading;

  /// * 尾部信息组件列表
  final List<Widget> infos;

  /// * 尾部图标组件
  final Widget trailing;

  /// * 是否是第一个子元素
  /// * 主要用于判断是否要添加顶部分隔符
  final bool isFirstChild;

  @override
  Widget build(BuildContext context) {

    final Widget listTileWithoutLeading = this._buildListTileWithoutLeading();

    final Widget listTile = GestureDetector(
      onTap: this.onTap,
      child: Container(
        padding: padding,
        width: ScreenUtils.screenWidth,
        color: this.backgroundColor,
        child: this.leading == null
          ? listTileWithoutLeading
          : Row(
              mainAxisSize: MainAxisSize.max,
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: <Widget>[
                this.leading,
                Expanded(
                  child: listTileWithoutLeading,
                ),
              ],
            ),
      ),
    );

    return listTile;
  }

  ///***
  /// * 生成不带 leading 部分的 ListTile UI
  /// * 如果不是第一个子元素，则添加顶部分隔符
  /// *
  /// * @return {Widget}
  /// */
  Widget _buildListTileWithoutLeading() {
    Widget listTileWithoutLeading = Container(
      constraints: BoxConstraints(
        minHeight: this.minHeight,
      ),
      child: Row(
        mainAxisSize: MainAxisSize.max,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: <Widget>[
          Text(
            this.title,
            style: TextStyle(
              fontSize: this.fontSize,
              color: this.color,
            ),
          ),
          if (this.infos != null || this.trailing != null)
            this._buildRightPart(),
        ],
      ),
    );

    if (!this.isFirstChild) {
      listTileWithoutLeading = Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Divider(
            height: themeBorderWidth,
            thickness: themeBorderWidth,
            color: themeBorderColor,
          ),
          listTileWithoutLeading,
        ],
      );
    }

    return listTileWithoutLeading;
  }

  ///***
  /// * 生成右侧 UI
  /// *
  /// * @return {Widget}
  /// */
  Widget _buildRightPart() {
    final Widget rightPart = Container(
      padding: const EdgeInsets.only(right: _rightPadding),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: <Widget>[
          if (this.infos != null)
            ...this._buildInfos(),
          if (this.trailing != null)
            this.trailing,
        ],
      ),
    );

    return rightPart;
  }

  ///***
  /// * 生成右侧 infos 的 UI 列表
  /// *
  /// * @return {List<Widget>}
  /// */
  List<Widget> _buildInfos() {
    final List<Widget> infoWidgets = [];

    if (this.infos == null) {
      return infoWidgets;
    }

    for (int i = 0, len = this.infos.length; i < len; i++) {
      Widget info = Padding(
        padding: const EdgeInsets.only(right: _infoWidgetHorizontalPadding),
        child: this.infos[i],
      );

      infoWidgets.add(info);
    }

    return infoWidgets;
  }
}
