package com.hzw.sunflower.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzw.sunflower.dao.ProjectBidDocRelationMapper;
import com.hzw.sunflower.dto.ProjectBidDocRelationDTO;
import com.hzw.sunflower.entity.ProjectBidDocRelation;
import com.hzw.sunflower.entity.condition.ProjectBidDocRelationCondition;
import com.hzw.sunflower.service.ProjectBidDocRelationService;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * TProjectBidDocRelationService接口实现
 *
 * <AUTHOR>
 * @version 1.0.0 2021-04-07
 */
@Service
public class ProjectBidDocRelationServiceImpl extends ServiceImpl<ProjectBidDocRelationMapper, ProjectBidDocRelation> implements ProjectBidDocRelationService {

    /**
     * 根据条件分页查询列表
     *
     * @param condition 查询条件
     * @return 分页数据
     */
    @Override
    public IPage<ProjectBidDocRelation> findInfoByCondition(ProjectBidDocRelationCondition condition) {
        IPage<ProjectBidDocRelation> page = condition.buildPage();
        QueryWrapper<ProjectBidDocRelation> queryWrapper = condition.buildQueryWrapper(ProjectBidDocRelation.class);
        return this.page(page, queryWrapper);
    }

    /**
     * 根据主键ID查询
     *
     * @param id 主键ID
     * @return
     */
    @Override
    public ProjectBidDocRelation getInfoById(Long id) {
        return this.getById(id);
    }

    /**
     * 新增
     *
     * @param tProjectBidDocRelation
     * @return 是否成功
     */
    @Override
    public Boolean addInfo(ProjectBidDocRelationDTO tProjectBidDocRelation) {
        return this.save(tProjectBidDocRelation);
    }

    /**
     * 修改
     *
     * @param tProjectBidDocRelation
     * @return 是否成功
     */
    @Override
    public Boolean updateInfo(ProjectBidDocRelationDTO tProjectBidDocRelation) {
        return this.updateById(tProjectBidDocRelation);
    }

    /**
     * 根据主键ID删除
     *
     * @param id 主键ID
     * @return 是否成功
     */
    @Override
    public Boolean deleteById(Long id) {
        return this.removeById(id);
    }

    /**
     * 根据主键ID列表批量删除
     *
     * @param idList 主键ID列表
     * @return 是否成功
     */
    @Override
    public Boolean deleteByIds(List<Long> idList) {
        return this.removeByIds(idList);
    }

}
