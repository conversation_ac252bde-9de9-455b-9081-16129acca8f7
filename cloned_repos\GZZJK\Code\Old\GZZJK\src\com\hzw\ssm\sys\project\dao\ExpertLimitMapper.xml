<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper 
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hzw.ssm.sys.project.dao.ExpertLimitMapper">

	<!-- 查询数据-->
	<select id="queryExpertByDepartment" parameterType="ExpertLimit"
		resultType="ExpertLimit">
		SELECT 
		T.EXPERT_ID AS expertId, 
		T.LIMIT_TIME AS limitTime,
		T.ADDTIME AS addTime,
		T.DEPARTMENT AS department
		 FROM T_EXPERT_LIMIT t
		where t.LIMIT_TIME=#{limitTime}
		AND T.DEPARTMENT = #{department}
	</select>
</mapper>