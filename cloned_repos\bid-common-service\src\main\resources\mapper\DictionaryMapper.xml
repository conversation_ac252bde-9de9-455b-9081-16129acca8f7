<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!-- 字典表 (t_dictionary) -->
<mapper namespace="com.hzw.sunflower.dao.DictionaryMapper">

    <select id="selectDictionaryByParentId" resultType="com.hzw.sunflower.controller.response.DictionaryVO">
        SELECT
            d.*,
            (case when p.NAME is null then '根目录' else p.name end) AS parentName
        FROM
            t_dictionary d
            LEFT JOIN t_dictionary p ON d.parent_id = p.id
        WHERE
            1 = 1 and d.is_delete = 0 and p.is_delete = 0
            <if test="contion.pid != null">
                and  p.parent_id = #{contion.pid}
            </if>
            <if test="contion.isDisable != null and contion.isDisable != -1 ">
                and  d.is_disable = #{contion.isDisable}
            </if>
            <if test="contion.keywords != null and contion.keywords != ''">
                and (
                    d.name like concat('%',#{contion.keywords},'%')
                    or
                    d.code like concat('%',#{contion.keywords},'%')
                )
            </if>
    </select>

    <select id="selectDictionaryForCurrency" resultType="com.hzw.sunflower.entity.Dictionary" >
        SELECT * from t_dictionary
        where parent_id in (SELECT id from t_dictionary where code = 'CURRENCY')
        and is_delete = 0
    </select>
    <select id="selectAllCountry" resultType="com.hzw.sunflower.entity.Dictionary">
        select t.name,
               t.code,
               t.parent_id,
               t.value,
               t.created_user_id,
               t.created_time,
               t.updated_user_id,
               t.updated_time,
               t.is_delete,
               t.remark,
               t.version,
               t.is_disable
        from  t_dictionary t where parent_id in (SELECT ID from t_dictionary where code = 'NATION')
    </select>
    <select id="getDicByCode" resultType="java.lang.String">
        select t.dept_id
        from  t_dictionary_department t where type = #{type} and is_delete = 0 order by t.dept_id
    </select>
    <select id="getByHistoryDicList" resultType="com.hzw.sunflower.controller.response.DictionaryVO">
        SELECT
            d.*
        FROM
            (
            SELECT
                t.id
            FROM
                (
                SELECT DISTINCT
                    SUBSTRING_INDEX( SUBSTRING_INDEX( t.province, ',', help_topic_id + 1 ), ',',- 1 ) id,
                    time
                FROM
                    mysql.help_topic,
                    (
                    SELECT
                        p.address_province province,
                        p.updated_time time
                    FROM
                        t_project p
                    WHERE
                        ( p.address_province IS NOT NULL AND p.address_province != 0 )
                        AND p.is_delete = 0
                        AND p.created_user_id = #{userId} UNION
                    SELECT
                        s.bid_address_province province,
                        s.updated_time time
                    FROM
                        t_project_bid_section s
                    WHERE
                        ( s.bid_address_province IS NOT NULL AND s.bid_address_province != 0 )
                        AND s.is_delete = 0
                        AND s.created_user_id = #{userId} UNION
                    SELECT
                        t.expert_region_provincial province,
                        t.updated_time time
                    FROM
                        t_expert_extraction_filter t
                    WHERE
                        ( t.expert_region_provincial IS NOT NULL AND t.expert_region_provincial != 0 )
                        AND t.is_delete = 0
                        AND t.created_user_id = #{userId}
                    ) t
                ORDER BY
                    time DESC
                ) t
            GROUP BY
                t.id
            ) t
            LEFT JOIN t_dictionary d ON t.id = d.id
            AND d.is_delete = 0
            AND d.is_disable = 0
            WHERE d.id is not null
            LIMIT 3
    </select>
    <select id="getChildrenList" resultType="java.lang.Boolean">
        SELECT
            count(*)>0
        FROM
            t_dictionary t
        WHERE
            parent_id = #{id}
    </select>

    <!-- 查询所有缓存时间 -->
    <select id="selectDictionaryForCacheRefreshTime" resultType="com.hzw.sunflower.entity.Dictionary">
        SELECT * from t_dictionary where code = 'CACHE_REFRESH_TIME' and is_delete = 0 limit 1
    </select>

    <!-- 根据code获取字典 -->
    <select id="getDictionaryByCode" resultType="com.hzw.sunflower.entity.Dictionary">
        SELECT * from t_dictionary where code = #{code} and is_delete = 0 limit 1
    </select>

</mapper>
