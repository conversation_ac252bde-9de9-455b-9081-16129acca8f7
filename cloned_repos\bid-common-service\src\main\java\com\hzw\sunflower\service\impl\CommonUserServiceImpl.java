package com.hzw.sunflower.service.impl;

import com.hzw.sunflower.common.redis.RedisCache;
import com.hzw.sunflower.config.aspect.SysteamProperties;
import com.hzw.sunflower.constant.GeneralConstants;
import com.hzw.sunflower.constant.constantenum.EnterSourceEnum;
import com.hzw.sunflower.controller.response.CommonUserRoleRelationVO;
import com.hzw.sunflower.controller.response.UserTokenRolesVO;
import com.hzw.sunflower.dto.CommonDeptRolesDTO;
import com.hzw.sunflower.entity.Role;
import com.hzw.sunflower.security.utils.SecurityUtils;
import com.hzw.sunflower.service.CommonDepartRoleService;
import com.hzw.sunflower.service.CommonUserService;
import com.hzw.sunflower.util.Object2OtherUtil;
import com.hzw.sunflower.util.spring.SpringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @ClassName:CommonUserServiceImpl
 * @Auther: lijinxin
 * @Description: 用户共用
 * @Date: 2024/7/23 14:46
 * @Version: v1.0
 */
@Service
public class CommonUserServiceImpl  implements CommonUserService {


    @Autowired
    private CommonDepartRoleService departRoleService;


    @Autowired
    private RedisCache redisCache;

    @Override
    public UserTokenRolesVO getUserRoles(){
        Integer userIdentity = SecurityUtils.getJwtUser().getUserIdentity();
        Long userId = SecurityUtils.getJwtUser().getUserId();
        Long deptId = SecurityUtils.getJwtUser().getUser().getDepartId();
        return getUserRolesConvert(userId,deptId,userIdentity, true);
    }

    @Override
    public UserTokenRolesVO getUserRolesByparameter(Long userId,Long deptId,Integer userIdentity){
        return getUserRolesConvert(userId,deptId,userIdentity, false);
    }

    public UserTokenRolesVO getUserRolesConvert(Long userId,Long deptId,Integer userIdentity, boolean isCache){
        SysteamProperties properties = SpringUtils.getBean(SysteamProperties.class);
        UserTokenRolesVO user = new UserTokenRolesVO();
        UserTokenRolesVO userTokenRolesVO = redisCache.getCacheObject(properties.getTokenKey()+"user_token_role_"+userId + "_" + deptId);
        if(null != userTokenRolesVO && isCache){
            return userTokenRolesVO;
        }else{
            List<Long> roleIds=new ArrayList<>();
            //查询用户部门角色关系 （有主角色用户，则第一个是主角色用户，没有则查询第一个录入的）
            List<CommonDeptRolesDTO> deptRolesDTOS = new ArrayList<>();
            if(!EnterSourceEnum.EXPERT.getType().equals(userIdentity)){
                deptRolesDTOS = departRoleService.getDepartRoles(userId);
            }
            if(!deptRolesDTOS.isEmpty()) {
                roleIds.addAll(Arrays.asList(deptRolesDTOS.get(0).getRoleIds()));
            }else {
                //角色
                List<CommonUserRoleRelationVO> roleRelations = departRoleService.getUserRoleByIdentity(userId, userIdentity);
                if(!roleRelations.isEmpty()) {
                    for (CommonUserRoleRelationVO userRoleRelation : roleRelations) {
                        roleIds.add(userRoleRelation.getRoleId());
                    }
                }
            }
            // 查询角色列表
            if (roleIds.size()>0){
                List<Role> roleList = departRoleService.getRoleList(roleIds);
                if (roleList!=null && roleList.size()>0){
                    user.setRoleStrIds(String.join(",", Object2OtherUtil.Object2String(roleList.stream().map(Role::getId).collect(Collectors.toList()).toArray())));
                    user.setRoleStrCodes(roleList.stream().map(Role::getRoleCode).collect(Collectors.joining()));
                    user.setRoleStrNames(roleList.stream().map(Role::getRoleName).collect(Collectors.joining()));
                    user.setRoles(roleList);
                }
            }else {
                user.setRoleStrNames("");
                user.setRoleStrCodes("");
                user.setRoleStrIds("");
                user.setRoles(new ArrayList<>());
            }
            redisCache.setCacheObject(properties.getTokenKey()+"user_token_role_"+userId + "_" + deptId,user, GeneralConstants.TOKEN_VALID_TIME, TimeUnit.MILLISECONDS);
        }

        return user;
    }
}
