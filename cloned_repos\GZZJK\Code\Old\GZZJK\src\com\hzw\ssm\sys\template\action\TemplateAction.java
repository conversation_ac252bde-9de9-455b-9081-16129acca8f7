package com.hzw.ssm.sys.template.action;

import java.io.PrintWriter;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpSession;

import net.sf.json.JSONObject;

import org.apache.struts2.convention.annotation.Action;
import org.apache.struts2.convention.annotation.Namespace;
import org.apache.struts2.convention.annotation.ParentPackage;
import org.apache.struts2.convention.annotation.Result;
import org.apache.struts2.convention.annotation.Results;
import org.springframework.beans.factory.annotation.Autowired;

import com.hzw.ssm.fw.base.BaseAction;
import com.hzw.ssm.fw.util.SysConstants;
import com.hzw.ssm.sys.template.entity.TemplateEntity;
import com.hzw.ssm.sys.template.service.TemplateService;
import com.hzw.ssm.sys.user.entity.UserEntity;

@Namespace("/template")
@ParentPackage(value = "default")
@Results( { @Result(name = "init", location = "/jsp/template/template_list.jsp"),
	@Result(name = "input", location = "/jsp/template/template_add.jsp"),
	@Result(name = "edit", location = "/jsp/template/template_edit.jsp")
	})
public class TemplateAction extends BaseAction {
	private static final long serialVersionUID = -8823858495799309882L;

	private TemplateEntity entity;
	//模板集合
	private List <TemplateEntity> templateList;
	
	@Autowired
	private TemplateService templateService;

	//只用于按钮切换
	@Action("ajaxQueryTemplateByIsVaild")
	public String ajaxQueryTemplateByIsVaild() {
		this.context();
		this.getResponse().setCharacterEncoding("utf-8");
		PrintWriter out = null;
		Map<String, Object> map = new HashMap<String, Object>();
		try {
			out = this.getResponse().getWriter();
			entity.setIsValid(SysConstants.IS_VALID.VALID_ZERO);
			List<TemplateEntity> templateList = templateService.queryTemplateList(entity);
			if (templateList != null && !templateList.isEmpty() && templateList.size() > 0) {
				map.put("templateList", templateList);
			} else {
				templateList =  new ArrayList<TemplateEntity>();
				map.put("templateList", templateList);
			}
			
		} catch (Exception e) {
			map.put("msg", "查询数据有误，请联系管理员！");
		} finally {
			JSONObject jsonobject=JSONObject.fromObject(map);
			out.print(jsonobject);
			out.close();
		}
		return null;
	}
	
	
	@Action("ajaxQueryTemplateList")
	public String ajaxQueryTemplateList() {
		this.context();
		this.getResponse().setCharacterEncoding("utf-8");
		PrintWriter out = null;
		Map<String, Object> map = new HashMap<String, Object>();
		try {
			out = this.getResponse().getWriter();
			List<TemplateEntity> templateList = templateService.queryTemplateList(entity);
			if (templateList != null && !templateList.isEmpty() && templateList.size() > 0) {
				map.put("templateList", templateList);
			} else {
				map.put("msg", "没有可用模板，请联系管理员添加！");
			}
			
		} catch (Exception e) {
			map.put("msg", "查询数据有误，请联系管理员！");
		} finally {
			JSONObject jsonobject=JSONObject.fromObject(map);
			out.print(jsonobject);
			out.close();
		}
		return null;
	}

	@Action("ajaxQueryTemplate")
	public String ajaxQueryTemplate() {
		this.context();
		this.getResponse().setCharacterEncoding("utf-8");
		PrintWriter out = null;
		Map<String, Object> map = new HashMap<String, Object>();
		try {
			out = this.getResponse().getWriter();
			TemplateEntity template = templateService.queryTemplateById(entity);
			if(template==null) {
				map.put("msg", "查询数据有误，请联系管理员！");
			}else {
				map.put("template", template);
			}
		} catch (Exception e) {
			map.put("msg", "查询数据有误，请联系管理员！");
		} finally {
			JSONObject jsonobject=JSONObject.fromObject(map);
			out.print(jsonobject);
			out.close();
		}
		return null;
	}
	
	/**
	 * 函数功能描述：查询模板的集合
	 * @return
	 */
	@Action("getTemplateEntityList")
	public String getTemplateEntityList(){
		if (entity == null) {
			entity = new TemplateEntity();
		}
		entity.setPage(this.getPage());
		templateList = templateService.queryTemplateList(entity);
		return INIT;
	}
	
	/**
	 * 函数功能描述：新增模板
	 * @return
	 */
	@Action("addTemplate")
	public String addTemplate(){
		
		return "input";
	}
	
	@Action("saveTemplate")
	public String saveTemplate(){
		this.context();
		// 当前session
		HttpSession session = getRequest().getSession();
		// 当前用户信息
		UserEntity user = (UserEntity) session.getAttribute("userInfo");
		entity.setCreate_id(user.getUser_id());
		if (entity == null) {
			entity = new TemplateEntity();
		}
		 templateService.insertTemplate(entity);
		 entity= new TemplateEntity();
		 return this.getTemplateEntityList();
	}
	/**
	 * 函数功能描述：删除模板信息
	 * @return
	 */
	@Action("deleteTemplate")
	public String deleteTemplate(){
		this.context();
		this.getResponse().setCharacterEncoding("utf-8");
		PrintWriter out = null;
		Map<String, Object> map = new HashMap<String, Object>();
		String flag= "success";
		//设置删除标识(1为删除数据)
		entity.setDelete_flag(1);
		try {
			out = this.getResponse().getWriter();
			int count = templateService.modifyTemplateById(entity);
			if(count==0) {
				map.put("msg", "删除数据有误，请联系管理员！");
			}else {
				map.put("msg",flag);
			}
		} catch (Exception e) {
			map.put("msg", "删除数据有误，请联系管理员！");
		}finally {
			JSONObject jsonobject=JSONObject.fromObject(map);
			out.print(jsonobject);
			out.close();
		}
		 
		return null;
	}
	
	/**
	 * 函数功能描述：进入详情页
	 * @return
	 */
	@Action("getTemplateDetail")
	public String getTemplateDetail(){
		
		if (entity == null) {
			entity = new TemplateEntity();
		}
		//根据id查询对应的模板信息
		entity= templateService.queryTemplateById(entity);
		
		return "edit";
	}
	
	
	
	@Action("modifyTemplate")
	public String modifyTemplate(){
		this.context();
		// 当前session
		HttpSession session = getRequest().getSession();
		// 当前用户信息
		UserEntity user = (UserEntity) session.getAttribute("userInfo");
		entity.setModify_id(user.getUser_id());
		entity.setModify_time(new Date());
		templateService.modifyTemplateById(entity);
		entity = new TemplateEntity();
		return this.getTemplateEntityList();
	}
	
	
	/**
	 * 函数功能描述：校验在同一个模板类型下是否有其他的生效模板
	 * @return
	 */
	@Action("ajaxCheckIsValid")
	public String ajaxCheckIsValid(){
		this.context();
		this.getResponse().setCharacterEncoding("utf-8");
		PrintWriter out = null;
		int count =0;
		try {
			out = this.getResponse().getWriter();
			count = templateService.ajaxCheckIsValid(entity);
			
		} catch (Exception e) {
			e.printStackTrace();
		}finally {
			out.print(count);
			out.close();
		}
		 
		return null;
	}
	
	public TemplateEntity getEntity() {
		return entity;
	}

	public void setEntity(TemplateEntity entity) {
		this.entity = entity;
	}

	public List<TemplateEntity> getTemplateList() {
		return templateList;
	}

	public void setTemplateList(List<TemplateEntity> templateList) {
		this.templateList = templateList;
	}
	
	
	
	
}
