package com.hzw.ssm.log.service;

import java.util.Date;

import org.springframework.beans.factory.annotation.Autowired;

import com.hzw.ssm.log.dao.LogMapper;
import com.hzw.ssm.log.entity.LogEntity;

/**
 * 日志记录操作服务共通
 * 
 * <AUTHOR>
 * @version 1.0
 * 
 */
public class LogService {

	@Autowired
	private LogMapper logMapper;

	private LogEntity getEntity(String id, String userId, String opaIp,
			String pageName, String methodName) {
		LogEntity entity = new LogEntity();
		entity.setId(id);
		entity.setUserId(userId);
		entity.setOpaIp(opaIp);
		entity.setPageName(pageName);
		entity.setMethodName(methodName);
		return entity;
	}

	/**
	 * 
	 * @param id
	 * @param userId
	 * @param opaIp
	 * @param pageName
	 * @param methodName
	 */
	public void insertLog(String id, String userId, String opaIp,
			String pageName, String methodName) {
		LogEntity entity = getEntity(id, userId, opaIp, pageName, methodName);
		entity.setOpaType("10");
		entity.setOpaDate(new Date());
		logMapper.insertLog(entity);
	}
}
