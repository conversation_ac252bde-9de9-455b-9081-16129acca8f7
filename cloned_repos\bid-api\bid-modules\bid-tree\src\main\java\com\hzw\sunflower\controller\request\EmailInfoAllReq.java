package com.hzw.sunflower.controller.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @ClassName:EmailInfoAllReq
 * @Auther: lijinxin
 * @Description: 全部邮件查询请求参数
 * @Date: 2023/3/16 16:08
 * @Version: v1.0
 */
@Data
public class EmailInfoAllReq {


    @ApiModelProperty("关键字")
    private String keyWords;

    @ApiModelProperty("业务类型")
    private Integer bussinessType;

    @ApiModelProperty("业务id")
    private Long bussinessId;
}
