package com.hzw.constant;

public enum ApiEnum {

    // 1 jstcc  2 专家库 3 电厂

    JSTCC(1, "jstcc"),
    EXPERT(2, "专家库"),
    ELECTRIC_PLANT(3, "电厂");

    private Integer type;
    private String desc;

    ApiEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
