package com.hzw.sunflower.entity.condition;

import com.hzw.sunflower.common.BaseCondition;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@ApiModel(description = "异常申请退款查询条件")
@Data
public class BondApplyRefundCondition extends BaseCondition {

    @ApiModelProperty(value = "关键字，付款户名")
    private String keyWords;

    @ApiModelProperty(value = "状态")
    private List<Integer> status;

}
