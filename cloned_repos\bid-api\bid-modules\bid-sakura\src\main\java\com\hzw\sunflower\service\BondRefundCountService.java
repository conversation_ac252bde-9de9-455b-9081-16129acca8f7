package com.hzw.sunflower.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hzw.sunflower.controller.request.RefundManageReq;
import com.hzw.sunflower.controller.request.RelationNoRefundReq;
import com.hzw.sunflower.dto.BondRefundManageDto;
import com.hzw.sunflower.dto.BondRelationNoRefundDto;

import javax.servlet.http.HttpServletResponse;

/**
 * <p>
 * 保证金退还统计 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-15
 */
public interface BondRefundCountService {

    /**
     * 保证金退还管理
     * @param req
     * @return
     */
    IPage<BondRefundManageDto> queryBondRefundManagePage(RefundManageReq req);

    /**
     * 保证金退还管理
     * @param req
     * @return
     */
    IPage<BondRelationNoRefundDto> queryBondRelationNoRefundPage(RelationNoRefundReq req);

    /**
     * 导出excel
     * @param condition
     * @param response
     */
    void exportBondRelationNoRefund(RelationNoRefundReq condition, HttpServletResponse response);
}
