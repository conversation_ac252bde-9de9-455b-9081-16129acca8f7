package com.hzw.sunflower.entity;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class ProjectAnalysisArea implements Serializable {

    //城市编码
    @ExcelIgnore
    private Integer cityCode;
    //所属城市
    @ExcelProperty(value = "所属城市", index = 0)
    private String city;
    //项目个数
    @ExcelProperty(value = "项目数(个)", index = 1)
    private int projectCount;

    //委托金额
    @ExcelProperty(value = "委托金额(万元)", index = 2)
    private BigDecimal entrustMoney;

    //占比
    @ExcelProperty(value = "占比(%)", index = 3)
    private BigDecimal proportion;
}
