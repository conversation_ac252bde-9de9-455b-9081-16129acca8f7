package com.hzw.sunflower.util;

import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.usermodel.DateUtil;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;

/**
 * excel解析工具类
 */
public class ExcelParserUtil {

    /**
     * 解析Excel文件，获取指定范围内的数据
     *
     * @param inputStream  输入流
     * @param maxRows       最大行数
     * @param maxColumns    最大列数
     * @param rowStart      起始行数
     * @return 解析后的数据列表
     */
    public static List<List<String>> parseExcelSheet0(InputStream inputStream, Integer maxRows, Integer maxColumns, Integer rowStart) {
        List<List<String>> data = new ArrayList<>();
        try (Workbook workbook = new XSSFWorkbook(inputStream)) {
            // 获取第一个工作表
            Sheet sheet = workbook.getSheetAt(0);
            for (int rowIndex = rowStart; rowIndex < Math.min(sheet.getLastRowNum() + 1, maxRows); rowIndex++) {
                Row row = sheet.getRow(rowIndex);
                if (row == null) {
                    // 跳过空行
                    continue;
                }
                List<String> rowData = new ArrayList<>();
                boolean hasData = false;
                for (int colIndex = 0; colIndex < maxColumns; colIndex++) {
                    Cell cell = row.getCell(colIndex);
                    if (cell == null || cell.getCellType() == CellType.BLANK) {
                        // 空单元格处理为空字符串
                        rowData.add("");
                    } else {
                        String cellValue = getCellValue(cell);
                        rowData.add(cellValue);
                        if (!cellValue.isEmpty()) {
                            // 记录行是否有数据
                            hasData = true;
                        }
                    }
                }
                if (hasData) {
                    // 只添加有数据的行
                    data.add(rowData);
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return data;
    }

    /**
     * 获取单元格值
     * @param cell
     * @return
     */
    private static String getCellValue(Cell cell) {
        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue().toString();
                } else {
                    return String.valueOf(cell.getNumericCellValue());
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                return cell.getCellFormula();
            default:
                return "";
        }
    }

}
