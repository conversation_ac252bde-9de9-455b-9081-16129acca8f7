package com.hzw.sunflower.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hzw.sunflower.controller.request.ProjectSectionReq;
import com.hzw.sunflower.controller.response.*;
import com.hzw.sunflower.dto.UnreturnedBondDto;
import com.hzw.sunflower.entity.BondRefund;
import com.hzw.sunflower.entity.condition.BankRateCondition;
import com.hzw.sunflower.entity.condition.BondRefundCondition;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 保证金退还表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-27
 */
@Mapper
public interface BondRefundMapper extends BaseMapper<BondRefund> {

    /**
     * 查询已经退还供应商情况
     * @param sectionId
     * @return
     */
    Long findRefundCount(@Param("sectionId") Long sectionId);

    /**
     * 保证金退还标供应商列表
     * @param req
     * @param page
     * @return
     */
    List<BondSupplierInfoVo> bondSupplierList(@Param("req") ProjectSectionReq req, @Param("page")IPage<BondSupplierInfoVo> page);

    /**
     * 财务退还列表
     * @param page
     * @param condition
     * @return
     */
    IPage<RefundListVo> bondRefundList(IPage<RefundListVo> page,@Param("condition")  BondRefundCondition condition);

    /**
     * 打印保证金退还表
     * @param condition
     * @return
     */
    List<BondSupplierVO> printBondRefundApply(@Param("condition") BondRefundCondition condition);

    /**
     * 全部列表
     * @param page
     * @param condition
     * @return
     */
    IPage<RefundListVo> bondRefundListForAll(IPage<RefundListVo> page,@Param("condition") BondRefundCondition condition);

    /**
     * 查看流水是否已处于退还状态
     * @param waterIdList
     * @return
     */
    List<BondRefund> selectListInReturn(@Param("list") List<Long> waterIdList);

    /**
     * 获取ncc代理服务费账户
     * @param bankType
     * @return
     */
    AgencyInfo2NccVo getAgencyInfo2Ncc(@Param("bankType") Integer bankType,@Param("bussineCode") Integer bussineCode );

    /**
     * 查询同一个标段的供应商信息
     * @param sectionId
     * @return
     */
    List<BondSupplierVO> printBondRefundApply2(@Param("sectionId") Long sectionId,@Param("companyId") Long companyId);

    /**
     * 查询未退保证金项目数据
     * @return
     */
    List<UnreturnedBondDto> findUnreturnedBond(@Param("startTime")String startTime, @Param("endTime")String endTime);

    /**
     * 查询部门code
     * @param substring
     * @return
     */
    String findDeptCode(String substring);

    /**
     * 查询部门名称
     * @param substring
     * @return
     */
    String findDeptName(String substring);

    /**
     * 中标候选人公示
     * @param sectionId
     * @param companyId
     * @return
     */
    Integer getCandidatePeople(@Param("sectionId")Long sectionId, @Param("companyId")Long companyId);

    /**
     * 验证流水是否在退还表中
     * @param waterId
     * @return
     */
    Integer checkWatersInRefund(@Param("waterId") Long waterId,@Param("refundId") Long refundId);

    /**
     * 保证金退还列表
     * @param page
     * @param condition
     * @return
     */
    IPage<RefundListVo> bondRefundListNew(IPage<RefundListVo> page,@Param("condition") BondRefundCondition condition);

    /**
     * 保证金退还列表
     * @param page
     * @param condition
     * @return
     */
    List<BankRateListVo> bankRateList(IPage<BankRateListVo> page, @Param("condition") BankRateCondition condition);


    /**
     * 根据退还id查询项目标段数据
     * @param refundId
     * @return
     */
    String getProjectSectionByRefundId(@Param("refundId") Long refundId);

    /**
     * 线下项目退还保证金供应商列表
     * @param req
     * @param page
     * @return
     */
    List<BondSupplierInfoVo> bondSupplierListOffline(@Param("req") ProjectSectionReq req, @Param("page") IPage<BondSupplierInfoVo> page);

    /**
     * 查询线下项目标段供应商数据
     * @param sectionId
     * @param offlineCompanyId
     * @return
     */
    BondOfflineCompanyVo queryBondOfflineCompany(@Param("sectionId") Long sectionId, @Param("offlineCompanyId") Long offlineCompanyId);

}
