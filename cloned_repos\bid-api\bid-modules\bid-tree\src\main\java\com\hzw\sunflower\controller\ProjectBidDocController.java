package com.hzw.sunflower.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hzw.sunflower.common.BaseController;
import com.hzw.sunflower.common.CommonConstants;
import com.hzw.sunflower.common.Paging;
import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.common.annotation.RepeatSubmit;
import com.hzw.sunflower.constant.MessageConstants;
import com.hzw.sunflower.constant.constantenum.*;
import com.hzw.sunflower.controller.request.DocBidRoundREQ;
import com.hzw.sunflower.controller.request.DocDataInfoREQ;
import com.hzw.sunflower.controller.request.NoticeProcessREQ;
import com.hzw.sunflower.controller.response.*;
import com.hzw.sunflower.dto.*;
import com.hzw.sunflower.entity.*;
import com.hzw.sunflower.entity.condition.ProjectBidDocCondition;
import com.hzw.sunflower.service.*;
import com.hzw.sunflower.util.BeanListUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;


/**
 * TProjectBidDocController
 */
@Api(tags = "招标文件服务")
@RestController
@Slf4j
@RequestMapping("/projectBidDoc")
public class ProjectBidDocController extends BaseController {
    @Autowired
    private ProjectBidDocService projectBidDocService;

    @Autowired
    private ProjectBidNoticeService projectBidNoticeService;

    @Autowired
    private OssFileService ossFileService;

    @Autowired
    private RelevancyAnnexService relevancyAnnexService;

    @Autowired
    private ProjectBidDocRelationService projectBidDocRelationService;

    @Autowired
    private ProjectSubService projectSubService;

    @Autowired
    private CommonSmsService commonSmsService;

    @Autowired
    private CommonOpenService commonOpenService;

    @Autowired
    private CommonProjectRelevantService commonProjectRelevantService;

    @Autowired
    private ApplyInfoService applyInfoService;

    @Autowired
    private ClarifyQuestionService clarifyQuestionService;

    @Autowired
    private ProjectSectionBidOpenService projectSectionBidOpenService;

    @Autowired
    private ProjectSectionScheduleService projectSectionScheduleService;


    @ApiOperation(value = "根据条件分页查询招标文件列表")
    @ApiImplicitParam(name = "condition", value = "招标文件表查询条件", required = true, dataType = "ProjectBidDocCondition", paramType = "body")
    @PostMapping("/list")
    public Result<Paging<ProjectBidDocVO>> list(@RequestBody ProjectBidDocCondition condition) {
        IPage<ProjectBidDocVO> voPage = new Page<>();
        //当前用户
        condition.setUserId(getJwtUser().getUserId());
        IPage<ProjectBidDocDTO> page = projectBidDocService.findInfoByCondition(condition);
        List<ProjectBidDocVO> vo = BeanListUtil.convertList(page.getRecords(), ProjectBidDocVO.class);
        //获取标段ID
        vo.stream().forEach((e) -> {
            LambdaQueryWrapper<ProjectBidDocRelation> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(ProjectBidDocRelation::getDocId, e.getDocId());
            List<ProjectBidDocRelation> list = projectBidDocRelationService.list(queryWrapper);
            List<Long> sectionIdList = new ArrayList<>();
            List<ProjectBidSection> sectionList = new ArrayList<>();
            List<Date> submitEndTimeArray = new ArrayList<>();
            for (ProjectBidDocRelation projectBidDocRelation : list) {
                sectionIdList.add(projectBidDocRelation.getSectionId());
                submitEndTimeArray.add(projectBidDocRelation.getSaleEndTime());
                sectionList.add(projectSubService.getById(projectBidDocRelation.getSectionId()));
            }
            e.setSectionArray(sectionList);
            e.setSectionIdArray(sectionIdList);
            e.setSaleEndTimeArray(submitEndTimeArray);
        });
        //类型转换
        voPage.setTotal(page.getTotal());
        voPage.setSize(page.getSize());
        voPage.setCurrent(page.getCurrent());
        voPage.setPages(page.getPages());
        voPage.setRecords(vo);
        return Result.ok(Paging.buildPaging(voPage));
    }

    @ApiOperation(value = "新建招标文件时，查询已到招标文件阶段且未发布招标文件的标段")
    @ApiImplicitParam(name = "docProgressREQ", value = "文件进度请求体", required = true)
    @PostMapping(value = "/queryDocProgress")
    public Result<List<ProjectBidDocVO>> queryDocProgress(@RequestBody @Valid DocBidRoundREQ docBidRoundREQ) {
        if (docBidRoundREQ.getProjectId() == null) {
            return Result.failed(MessageConstants.GET_NULL);
        }
        List<ProjectBidDocDTO> dtoList = projectBidDocService.queryDocProgress(docBidRoundREQ);
        List<ProjectBidDocVO> voList = BeanListUtil.convertList(dtoList, ProjectBidDocVO.class);
        return Result.ok(voList);
    }

    @ApiOperation(value = "新增招标文件信息")
    @RepeatSubmit
    @ApiImplicitParam(name = "addInfo", value = "招标文件信息 ", required = true, dataType = "DocDataInfoREQ", paramType = "body")
    @PostMapping("/addInfo")
    public Result<Boolean> addInfo(@RequestBody @Valid DocDataInfoREQ dataInfoREQ) {
        if (null == dataInfoREQ) {
            return Result.failed(MessageConstants.ADD_FAILED);
        }
        //验证信息
        if (projectBidDocService.validate(dataInfoREQ.getBidDocInfo())) {
            return Result.failed(MessageConstants.ADD_FAILED);
        }
        Boolean bool = projectBidDocService.addDocInfo(dataInfoREQ, getJwtUser());
        return Result.okOrFailed(bool);
    }

    @ApiOperation(value = "编辑文件时，查询项目所有标段信息及文件进度")
    @ApiImplicitParam(name = "docId", value = "招标文件ID", required = true)
    @GetMapping(value = "/queryDocInfo/{docId}")
    public Result<ProjectBidDocVO> queryDocInfo(@PathVariable("docId") Long docId) {
        if (docId == null) {
            return Result.failed(MessageConstants.GET_NULL);
        }
        List<ProjectBidDocDTO> dtoList = projectBidDocService.queryDocInfo(docId);
        if (CollectionUtil.isEmpty(dtoList)) {
            return Result.failed(MessageConstants.GET_NULL);
        }
        //类型转换
        ProjectBidDocVO projectBidDocVO = getProjectBidDocVO(docId, dtoList);
        return Result.ok(projectBidDocVO);
    }

    @ApiOperation(value = "解析开标一览表")
    @ApiImplicitParam(name = "ossId", value = "开标一览表ossId", required = true)
    @GetMapping(value = "/analysisBidDoc/{ossId}")
    public Result<Long> analysisBidDoc(@PathVariable("ossId") Long ossId) throws Exception {
        return projectBidDocService.analysisBidDoc(ossId);
    }

    @ApiOperation(value = "解析开标一览表")
    @ApiImplicitParam(name = "ossId", value = "开标一览表ossId", required = true)
    @PostMapping (value = "/analysisBidDoc")
    public Result<Long> analysisBidDoc(@RequestBody AnalysisBidDocDTO dto) throws Exception {
        if (null == dto.getOssId()) {
            return Result.failed("无开标一览表模板文件!");
        }
        return projectBidDocService.analysisBidDoc(dto);
    }

    @ApiOperation(value = "招标文件流程处理请求")
    @ApiImplicitParam(name = "noticeProcessREQ", value = "流程处理请求 ", required = true, dataType = "NoticeProcessREQ", paramType = "body")
    @PutMapping(value = "/updateProgress")
    @RepeatSubmit
    public Result<Boolean> update(@RequestBody @Valid NoticeProcessREQ noticeProcessREQ) {
        if (null == noticeProcessREQ) {
            return Result.failed(MessageConstants.UPDATE_FAILED);
        }
        if (NoticeProgressEnum.RELEASE.getValue().equals(noticeProcessREQ.getNoticeProgress())) {
            //发布文件前，判断公告是否发布,招标文件时间规则
            String info = checkDocInfo(noticeProcessREQ);
            if (StrUtil.isNotBlank(info)) {
                return Result.failed(info);
            }
        }
        Boolean bool = projectBidDocService.updateProgress(noticeProcessREQ, getJwtUser());

        // 资格预审第二轮挂网 自动发送短信
        if (NoticeProgressEnum.RELEASE.getValue().equals(noticeProcessREQ.getNoticeProgress()) && bool) {
            // 获取该文件关联的标段集合
            LambdaQueryWrapper<ProjectBidDocRelation> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(ProjectBidDocRelation::getDocId, noticeProcessREQ.getDocId());
            List<ProjectBidDocRelation> docRelationList = projectBidDocRelationService.list(queryWrapper);
            List<Long> subIdList = docRelationList.stream().map(relation -> relation.getSectionId()).collect(Collectors.toList());
            ProjectInfoDTO project = commonProjectRelevantService.getProjectUserInfo(noticeProcessREQ.getProjectId());
            // 校验当前项目负责人是否支持自动发送、校验是否为资格预审第二轮
            if (commonProjectRelevantService.isPreQualification(subIdList) && commonOpenService.validAutoByUserId(project.getUserId(), TemplateCodeEnum.INVITE_SUPPLIER.getCode())) {
                SendSmsDto smsDto = new SendSmsDto();
                smsDto.setTemplateCode(TemplateCodeEnum.INVITE_SUPPLIER.getCode());
                smsDto.setIsTemplate(true);
                smsDto.setProjectId(noticeProcessREQ.getProjectId());
                String sectionIds = StringUtils.join(subIdList.toArray(), ",");
                smsDto.setSectionIds(sectionIds);
                // 模板替换内容
                Map<String, Object> map = new HashMap<>();
                map.put("projectName", project.getProjectName());
                map.put("projectNum", project.getPurchaseNumber());
                map.put("contactPerson", project.getUserName());
                map.put("userMobile", project.getUserPhone());
                // 取所选标段最早的时间
                EarlySectionTimeVo timeVo = commonOpenService.getEarliestTimeBySectionIds(subIdList);
                map.put("saleEndTime", timeVo.getSaleEndTime());
                map.put("submitEndTime", timeVo.getSubmitEndTime());
                smsDto.setSmsPms(map);
                // 查询此项目、标段下所有参标供应商 遍历发送短信
                List<String> phoneList = applyInfoService.getUserPhoneList(noticeProcessREQ.getProjectId(), sectionIds);
                try {
                    for (String phone : phoneList) {
                        smsDto.setSendPhone(phone);
                        commonSmsService.sendSms(smsDto);
                    }
                } catch (Exception e) {
                    log.error("资格预审第二轮短信发送失败,请检查短信发送模块报错信息");
                }
            }
        }
        return Result.okOrFailed(bool);
    }

    @ApiOperation(value = "全部审批所有待确认的文件")
    @ApiImplicitParam(name = "updateAllProgress", value = "全部审批请求 ", required = true, dataType = "list", paramType = "body")
    @PutMapping(value = "/updateAllProgress")
    @RepeatSubmit
    public Result<Boolean> updateAllProgress(@RequestBody @Valid List<NoticeProcessREQ> list) {
        if (CollectionUtil.isEmpty(list)) {
            return Result.failed(MessageConstants.UPDATE_FAILED);
        }
        //全部确认、全部退回
        List<NoticeProcessREQ> collect = list.stream().filter(e -> e.getNoticeProgress().equals(NoticeProgressEnum.REVIEW.getValue())
                || e.getNoticeProgress().equals(NoticeProgressEnum.RETURN.getValue())).collect(Collectors.toList());
        if (list.size() != collect.size()) {
            return Result.failed(MessageConstants.AUDIT_ERROR_REMARK);
        }
        Boolean bool = projectBidDocService.updateAllProgress(list, getJwtUser());
        return Result.okOrFailed(bool);
    }


    /**
     * 发布前判断公告是否发布，文件时间是否符合规则
     *
     * @param noticeProcessREQ
     * @return
     */
    private String checkDocInfo(NoticeProcessREQ noticeProcessREQ) {
        LambdaQueryWrapper<ProjectBidDocRelation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjectBidDocRelation::getDocId, noticeProcessREQ.getDocId());
        List<ProjectBidDocRelation> docRelationList = projectBidDocRelationService.list(queryWrapper);
        //招标文件阶段
        ProjectBidDoc projectBidDoc = projectBidDocService.getById(noticeProcessREQ.getDocId());
        //调用判断公告是否发布接口
        List<Long> objects = new ArrayList<>();
        for (ProjectBidDocRelation docRelation : docRelationList) {
            objects.add(docRelation.getSectionId());
        }
        Boolean flag = projectBidNoticeService.isPublish(objects, projectBidDoc.getBidRound());
        if (!flag) {
            return MessageConstants.BID_NOTICE_INFORMATION;
        }
        for (ProjectBidDocRelation docRelation : docRelationList) {
            //递交响应截止时间判断
            if (TimeTypeEnum.CONFIRM.getValue().equals(docRelation.getSubmitEndTimeType())
                    && System.currentTimeMillis() >= docRelation.getSubmitEndTime().getTime()) {
                return MessageConstants.SUBMIT_END_TIME_REMARK;
            }
            //发售结束时间判断
            if (TimeTypeEnum.CONFIRM.getValue().equals(docRelation.getSaleEndTimeType())
                    && System.currentTimeMillis() >= docRelation.getSaleEndTime().getTime()) {
                return MessageConstants.SALE_END_TIME_REMARK;
            }
            if (TimeTypeEnum.CONFIRM.getValue().equals(docRelation.getSubmitEndTimeType())
                    && TimeTypeEnum.PREVIOUS_DAY.getValue().equals(docRelation.getSaleEndTimeType())
                    && System.currentTimeMillis() >= docRelation.getSaleEndTime().getTime()) {
                return MessageConstants.SALE_END_TIME_REMARK;
            }
            // 如果是文件挂网的话,那么进行时间判断...挂网必须有文件递交截止时间
//            if(TimeTypeEnum.CONFIRM.getValue().equals(docRelation.getSubmitEndTimeType()) && NoticeProgressEnum.RELEASE.getValue().equals(noticeProcessREQ.getNoticeProgress())){
//                // 不能超过多少天
//                int maxday = 5 ;
//                // 政府采购项目
//                // 招标：挂网时间到文件发售结束时间大于等于20日
//                // 竞争性磋商：挂网时间到文件发售结束时间大于等于10日
//                DocSectionDetailDTO docSectionDetailDTO = projectBidDocService.queryDocSectionInfo(projectBidDoc.getId(), projectBidDoc.getBidRound()).get(0);
//                if(docSectionDetailDTO.getPurchaseMode().equals(BidPurchaseModeEnum.GOVERNMENT_PROCUREMENT_924101)){
//                    maxday = 20;
//                }else if(docSectionDetailDTO.getPurchaseMode().equals(BidPurchaseModeEnum.GOVERNMENT_PROCUREMENT_924102)){
//                    maxday = 10;
//                }
//                LocalDate now = LocalDate.now();
//                if(TimeTypeEnum.PREVIOUS_DAY.getValue().equals(docRelation.getSaleEndTimeType())){
//                    LocalDate submitEndTime = docRelation.getSubmitEndTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
//                    //int day =  (int)((docRelation.getSubmitEndTime().getTime() - System.currentTimeMillis())/ (1000*3600*24)) -1;
//                    long day = submitEndTime.toEpochDay() - now.toEpochDay() - 1;
//                    if(day < maxday){
//                        return MessageConstants.SALE_END_TIME_REMARK_START+maxday+MessageConstants.SALE_END_TIME_REMARK_END;
//                    }
//                }else{
//                    LocalDate saleEndTime = docRelation.getSaleEndTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
//                    //int day =  (int)((docRelation.getSaleEndTime().getTime() - System.currentTimeMillis())/ (1000*3600*24));
//                    long day = saleEndTime.toEpochDay() - now.toEpochDay();
//                    if(day < maxday){
//                        return MessageConstants.SALE_END_TIME_REMARK_START+maxday+MessageConstants.SALE_END_TIME_REMARK_END;
//                    }
//                }
//
//            }
        }
        return null;
    }

//    public static void main(String[] args) {
//       Date d =new Date("Mon 1 Jun 2022 13:3:00");
//        LocalDate submitEndTime = d.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
//        LocalDate now = LocalDate.now();
//        long l = submitEndTime.toEpochDay() - now.toEpochDay();
//        System.out.println(l);
//    }

    @ApiOperation(value = "查询招标文件审核内容")
    @ApiImplicitParam(name = "docId", value = "招标文件ID", required = true)
    @GetMapping(value = "/getReviewInfo/{docId}")
    public Result<List<ReviewBidDocVO>> getReviewInfo(@PathVariable("docId") Long docId) {
        if (docId == null) {
            return Result.failed(MessageConstants.GET_NULL);
        }
        //招标文件信息
        List<ReviewBidDocDTO> reviewInfo = projectBidDocService.queryReviewInfo(docId);
        if (CollectionUtil.isEmpty(reviewInfo)) {
            return Result.failed(MessageConstants.GET_NULL);
        }
        List<ReviewBidDocVO> info = BeanListUtil.convertList(reviewInfo, ReviewBidDocVO.class);
        //附件文件信息
        List<RelevancyAnnexDTO> dtoList = relevancyAnnexService.getInfoByDocId(docId);
        Map<Long, String> map = new HashMap<>();
        for (RelevancyAnnexDTO relevancyAnnex : dtoList) {
            if (null == map.get(relevancyAnnex.getSectionId())) {
                map.put(relevancyAnnex.getSectionId(), relevancyAnnex.getFileId().toString());
            } else {
                map.put(relevancyAnnex.getSectionId(), map.get(relevancyAnnex.getSectionId()) + "," + relevancyAnnex.getFileId());
            }
        }
        info.stream().forEach(e -> e.setAnnexList(ossFileService.getOssFileByFileIds(map.get(e.getSectionId()))));
        return Result.ok(info);
    }

    @ApiOperation(value = "撤回流程处理请求")
    @ApiImplicitParam(name = "noticeProgress", value = "流程处理请求 ", required = true, dataType = "noticeProcessREQ", paramType = "body")
    @PutMapping(value = "/modifyWithdrawProgress")
    @RepeatSubmit
    public Result<Boolean> modifyWithdrawProgress(@RequestBody @Valid NoticeProcessREQ noticeProcessREQ) {
        if (null == noticeProcessREQ) {
            return Result.failed(MessageConstants.UPDATE_FAILED);
        }
        Boolean bool = projectBidDocService.modifyWithdrawProgress(noticeProcessREQ, getJwtUser());
        return Result.okOrFailed(bool);
    }

    @ApiOperation(value = "查询重新招标标段标书费和付款状态")
    @ApiImplicitParam(name = "formerSectionId", value = "原标段ID", required = true)
    @GetMapping(value = "/queryRebidInfo/{formerSectionId}")
    public Result<List<ReBidInfoVO>> queryRebidInfo(@PathVariable("formerSectionId") Long formerSectionId) {
        if (formerSectionId == null) {
            return Result.failed(MessageConstants.GET_NULL);
        }
        List<ReBidInfoDTO> dtoList = projectBidDocService.queryRebidInfo(formerSectionId);
        List<ReBidInfoVO> voList = BeanListUtil.convertList(dtoList, ReBidInfoVO.class);
        return Result.ok(voList);
    }

    @ApiOperation(value = "根据标段ID查询文件信息")
    @ApiImplicitParam(name = "bidId", value = "标段ID", required = true)
    @PostMapping(value = "/queryDocInfoByBidId")
    public Result<ProjectBidDocVO> queryDocInfoByBidId(@RequestBody @Valid DocBidRoundREQ docBidRoundREQ) {
        if (docBidRoundREQ.getBidId() == null) {
            return Result.failed(MessageConstants.GET_NULL);
        }
        Long docId = projectBidDocService.queryDocId(docBidRoundREQ);
        if (null == docId) {
            return Result.ok();
        } else {
            List<ProjectBidDocDTO> dtoList = projectBidDocService.queryDocInfo(docId);
            if (CollectionUtil.isEmpty(dtoList)) {
                return Result.ok();
            }
            ProjectBidDocVO projectBidDocVO = getProjectBidDocVO(docId, dtoList);
            return Result.ok(projectBidDocVO);
        }
    }

    @ApiOperation(value = "根据标段ID集合查询文件信息")
    @ApiImplicitParam(name = "sectionIds", value = "标段ID集合", required = true)
    @PostMapping(value = "/queryDocInfoByBidIds")
    public Result<List<ProjectBidDocVO>> queryDocInfoByBidIds(@RequestBody @Valid DocBidRoundREQ docBidRoundREQ) {
        List<ProjectBidDocVO> projectBidDocVOList = new ArrayList<>();
        if (docBidRoundREQ.getSectionIds() == null) {
            return Result.failed(MessageConstants.GET_NULL);
        }
        List<Long> docIds = projectBidDocService.queryDocIdByBidIds(docBidRoundREQ);
        if (0 == docIds.size()) {
            return Result.ok();
        } else {
            for (Long docId : docIds) {
                List<ProjectBidDocDTO> dtoList = projectBidDocService.queryDocInfo(docId);
                if (CollectionUtil.isEmpty(dtoList)) {
                    return Result.ok();
                }
                ProjectBidDocVO projectBidDocVO = getProjectBidDocVO(docId, dtoList);
                projectBidDocVOList.add(projectBidDocVO);

            }
            return Result.ok(projectBidDocVOList);
        }
    }


    @ApiOperation(value = "文件详情页面信息")
    @ApiImplicitParam(name = "docId", value = "docId", required = true)
    @GetMapping(value = "/getDocDetailInfo/{docId}/{bidRound}")
    public Result<DocDetailVO> getDocDetailInfo(@PathVariable("docId") Long docId,@PathVariable("bidRound") Integer  bidRound) {
        if (docId == null) {
            return Result.failed(MessageConstants.GET_NULL);
        }
        DocDetailVO vo = projectBidDocService.getDocDetailInfo(docId,bidRound);
        return Result.ok(vo);
    }

    /**
     * 处理返回数据格式
     *
     * @param docId
     * @param dtoList
     * @return
     */
    private ProjectBidDocVO getProjectBidDocVO(Long docId, List<ProjectBidDocDTO> dtoList) {
        //类型转换
        ProjectBidDocVO projectBidDocVO = BeanListUtil.convert(dtoList.get(0), ProjectBidDocVO.class);
        //关联标段
        projectBidDocVO.setRelevantSection(projectBidDocService.queryRelevantInfo(docId));
        //递交截止时间
        List<Date> submitTime = new ArrayList<>();
        //递交截止时间
        List<Date> saleEndTime = new ArrayList<>();
        //保证金
        List<BigDecimal> bond = new ArrayList<>();
        //保证金比例
        List<BigDecimal> bondPercent = new ArrayList<>();
        //标书费
        List<BigDecimal> tenderFee = new ArrayList<>();
        //标段ID
        List<Long> section = new ArrayList<>();

        //澄清截止时间
        List<Date> clarifyEndTime = new ArrayList<>();
        //异议截止时间
        List<Date> questionEndTime = new ArrayList<>();

        // 解密时间
        List<Integer> decryptionTimeArray = new ArrayList<>();
        // 开标条件
        List<Integer> conditionBidOpenArray = new ArrayList<>();
        // 开标一览表文件
        List<Long> bidOpeningArray = new ArrayList<>();
        // 开标记录表生成方式
        List<Integer> generationArray = new ArrayList<>();
        // 系统自动唱标数组
        List<Integer> systemAutomaticLabelingArray = new ArrayList<>();

        for (ProjectBidDocDTO projectBidDocDTO : dtoList) {
            section.add(projectBidDocDTO.getSectionId());
            submitTime.add(projectBidDocDTO.getSubmitEndTime());
            saleEndTime.add(projectBidDocDTO.getSaleEndTime());
            //保证金类型 比例
            if (BondTypeEnum.PROPORTION.getValue().equals(projectBidDocDTO.getBondType())) {
                bond.add(projectBidDocDTO.getBond());
                bondPercent.add(projectBidDocDTO.getBondPercent());
            } else if (BondTypeEnum.QUOTA.getValue().equals(projectBidDocDTO.getBondType())) {
                bond.add(projectBidDocDTO.getBond());
            }
            //标书费类型 按标段/包
            if (TenderFeeTypeEnum.SECTION.getValue().equals(projectBidDocDTO.getTenderFeeType())) {
                tenderFee.add(projectBidDocDTO.getTenderFee());
            }

            //澄清异议截止时间
            LambdaQueryWrapper<ClarifyQuestion> cQueryWrapper= new LambdaQueryWrapper<>();
            cQueryWrapper.eq(ClarifyQuestion::getSectionId,projectBidDocDTO.getSectionId());
            ClarifyQuestion clarifyQuestion = clarifyQuestionService.getOne(cQueryWrapper);
            //判断该标段是否支持澄清异议
            if(null != clarifyQuestion){
                projectBidDocVO.setClarifyOnLine(clarifyQuestion.getClarifyOnLine());
                projectBidDocVO.setQuestionOnLine(clarifyQuestion.getQuestionOnLine());
                if(clarifyQuestion.getClarifyOnLine().equals(TimeTypeEnum.SUPPORT.getValue())){
                    projectBidDocVO.setClarifyEndTimeType(clarifyQuestion.getClarifyEndTimeType());
                    clarifyEndTime.add(clarifyQuestion.getClarifyEndTime());
                }
                if(clarifyQuestion.getQuestionOnLine().equals(TimeTypeEnum.SUPPORT.getValue())){
                    projectBidDocVO.setQuestionEndTimeType(clarifyQuestion.getQuestionEndTimeType());
                    questionEndTime.add(clarifyQuestion.getQuestionEndTime());
                }
            }

            // 线上开标
            ProjectSectionBidOpen projectSectionBidOpen = projectSectionBidOpenService.getOne(new LambdaQueryWrapper<ProjectSectionBidOpen>()
                    .eq(ProjectSectionBidOpen::getSectionId, projectBidDocDTO.getSectionId())
                    .eq(ProjectSectionBidOpen::getBidRound, projectBidDocDTO.getBidRound()));
            if (projectSectionBidOpen != null) {
                projectBidDocVO.setOnlineBidOpen(projectSectionBidOpen.getBidOpenOnline());
                if (CommonConstants.YES.equals(projectSectionBidOpen.getBidOpenOnline())) {
                    decryptionTimeArray.add(projectSectionBidOpen.getDecryptTime());
                    conditionBidOpenArray.add(projectSectionBidOpen.getSupNumEndBid());
                    ProjectSectionSchedule schedule = projectSectionScheduleService.getOne(new LambdaQueryWrapper<ProjectSectionSchedule>().eq(ProjectSectionSchedule::getSectionId, projectBidDocDTO.getSectionId())
                            .eq(ProjectSectionSchedule::getBidRound, projectBidDocDTO.getBidRound()));
                    if(schedule!=null){
                        bidOpeningArray.add(schedule.getFileOssId());
                        generationArray.add(schedule.getGenerateRule());
                    }
                    //全流程线上增加新字段
//                    bidOpeningArray.add(projectSectionBidOpen.getBidOpenOssId());
//                    generationArray.add(projectSectionBidOpen.getBidOpenRecordMode());
                    systemAutomaticLabelingArray.add(projectSectionBidOpen.getIsAutoChant());
                }
            }

        }
        projectBidDocVO.setSectionIdArray(section);
        projectBidDocVO.setSubmitEndTimeArray(submitTime);
        projectBidDocVO.setSaleEndTimeArray(saleEndTime);
        projectBidDocVO.setBondArray(bond);
        projectBidDocVO.setBondPercentArray(bondPercent);
        projectBidDocVO.setTenderFeeArray(tenderFee);
        projectBidDocVO.setClarifyEndTimeArray(clarifyEndTime);
        projectBidDocVO.setQuestionEndTimeArray(questionEndTime);
        projectBidDocVO.setDecryptionTimeArray(decryptionTimeArray);
        projectBidDocVO.setConditionBidOpenArray(conditionBidOpenArray);
        projectBidDocVO.setBidOpeningArray(bidOpeningArray);
        projectBidDocVO.setGenerationArray(generationArray);
        projectBidDocVO.setSystemAutomaticLabelingArray(systemAutomaticLabelingArray);
        //前端定义招标文件框数据格式
        BidFileInfo bidFileInfo = new BidFileInfo();
        bidFileInfo.setDocId(projectBidDocVO.getDocId());
        // 关联包
        String [] amount = new String[dtoList.size()];
        for (int i = 0; i < dtoList.size(); i++) {
            amount[i] = dtoList.get(i).getPackageNumber();
        }
        bidFileInfo.setAmount(amount);
        bidFileInfo.setKey(projectBidDocVO.getOssFileKey());
        bidFileInfo.setName(projectBidDocVO.getOssFileName());
        bidFileInfo.setFileType(AnnexTypeEnum.BIDNOTICE.getValue());
        bidFileInfo.setFileId(projectBidDocVO.getOssFileId());
        projectBidDocVO.setFileInfo(bidFileInfo);
        return projectBidDocVO;
    }



    @ApiOperation(value = "根据标段集合获取标段的挂网时间")
    @ApiImplicitParam(name = "docProgressREQ", value = "文件进度请求体", required = true)
    @PostMapping(value = "/findReleaseTimeBySections")
    public Result<Object> findReleaseTimeBySections(@RequestBody @Valid DocBidRoundREQ docBidRoundREQ) {
        List<ProjectBidDocDTO> releaseTimeBySections = projectBidDocService.findReleaseTimeBySections(docBidRoundREQ.getSectionIds(), docBidRoundREQ.getBidRound());
        return Result.ok(releaseTimeBySections);
    }
}
