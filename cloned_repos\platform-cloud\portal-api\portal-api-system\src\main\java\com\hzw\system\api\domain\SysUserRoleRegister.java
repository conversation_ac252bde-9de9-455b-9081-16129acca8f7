package com.hzw.system.api.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.hzw.common.core.constant.UserConstants;
import com.hzw.common.basebean.web.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;


/**
 * 用户注册对象 sys_user_role
 *
 * <AUTHOR>
 */

@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class SysUserRoleRegister extends BaseEntity {


    @TableId(value = "id")
    private String id;
    /**
     * 用户ID
     */
    private String userId;

    /**
     * 角色ID
     */
    private String roleId;


    public SysUserRoleRegister(String userId) {
        this.userId = userId;
    }

    /**
     * 是否管理员
     */
    public boolean isAdmin() {
        return UserConstants.ADMIN_ID.equals(this.userId);
    }

}
