/**
 * 
 */
package com.hzw.ssm.expert.action;

import com.google.gson.Gson;
import com.hzw.ssm.expert.entity.IllegalExtractioninfoEntity;
import com.hzw.ssm.expert.entity.MonitorEntity;
import com.hzw.ssm.expert.service.IllegalExtractioninfoService;
import com.hzw.ssm.fw.base.BaseAction;
import com.hzw.ssm.fw.util.DateUtil;
import com.hzw.ssm.sys.user.entity.UserEntity;
import com.hzw.ssm.util.empty.EmptyUtils;
import com.hzw.ssm.util.file.FileUtils;
import com.hzw.ssm.util.pdf.PdfUtils;
import com.hzw.ssm.util.string.StringUtils;
import org.apache.struts2.ServletActionContext;
import org.apache.struts2.convention.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.File;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 *
 */
@Namespace("/drawafterbid")
@ParentPackage(value = "default")
@Results({

        @Result(	name = "tomonitor",
location = "/jsp/drawafterbid/drawafterbidList.jsp")



})
public class DrawAfterBidAction extends BaseAction{

	private IllegalExtractioninfoEntity entity;
	private List<IllegalExtractioninfoEntity> Illegallist;
	private String decimationbatch;
	private String isHandle;
	//private MonitorEntity entity;
	@Autowired
	private IllegalExtractioninfoService monitorService;
	@Value("${PdfFileName}")
	private String pdfFileName;

	@Action("/getPageDrawAfterBid")
	public String getPageDrawAfterBid() {
		this.context();
		if(entity==null){
			entity=new IllegalExtractioninfoEntity();
		}

		entity.setPage(this.getPage());
		Illegallist = monitorService.queryPageExpertsDrawnAfterBidOpening(entity);
		return "tomonitor";
	}


	@Action("downDrawafterbidListPDF")
	public void downDrawafterbidListPDF(){
		PrintWriter out = null;
		try {
			this.context();
			this.getResponse().setCharacterEncoding("utf-8");
			this.getResponse().setContentType("text/html; charset=utf-8");
			out = this.getResponse().getWriter();
			// 发布路径
			String path = ServletActionContext.getServletContext().getRealPath("");
			// 模板文件路径
			String templetePath = path + "\\WEB-INF\\templete\\choose\\";
			//抽取记录模板文件路径
			String chooseRecordPath = null;
			//抽取记录模板文件内容
			String chooseRecordTmpl =null;
			//专家信息模板文件路径
			String expertInfoPath =null;
			//专家信息模板文件内容
			String expertInfoTmpl = null;
			templetePath = FileUtils.replaceSeparator(templetePath);
			//抽取记录模板文件路径
			chooseRecordPath = templetePath + "warnInfo4.html";
			//抽取记录模板文件内容
			chooseRecordTmpl = PdfUtils.readContent(chooseRecordPath, "UTF-8");

			//专家信息模板文件路径
			expertInfoPath = templetePath + "warnInfo3.html";
			//专家信息模板文件内容
			expertInfoTmpl = PdfUtils.readContent(expertInfoPath, "UTF-8");
			if(entity!=null&&entity.getUserName()!=null&&entity.getUserName().trim().length()>2){
			String	uploadifyFileName=	entity.getUserName();
			String fileName =new String(uploadifyFileName.getBytes("ISO8859-1"),"UTF-8");
				if(fileName.contains("?")||fileName.contains("\u0095")){
					uploadifyFileName = new String(uploadifyFileName.getBytes("UTF-8"),"UTF-8");
					entity.setUserName(uploadifyFileName);
				}else {
					uploadifyFileName=fileName;
					entity.setUserName(uploadifyFileName);
				}

			}

			List<IllegalExtractioninfoEntity> illegalExtractioninfoEntities = monitorService.pdfMonitor(entity);

			String expertInfo = "";
			if(!EmptyUtils.isEmpty(illegalExtractioninfoEntities)){
				int counter = 1;
				for(IllegalExtractioninfoEntity illegalExtractioninfoEntity :illegalExtractioninfoEntities){
					String tmpExpInfo = expertInfoTmpl;
					//抽取批次号
					tmpExpInfo = StringUtils.replace(tmpExpInfo, "${decimationBatch}", illegalExtractioninfoEntity.getDecimationbatch());
					//项目编号
					tmpExpInfo = StringUtils.replace(tmpExpInfo, "${projectNo}", illegalExtractioninfoEntity.getProjectCode());
					//项目名称
					tmpExpInfo = StringUtils.replace(tmpExpInfo, "${projectName}", illegalExtractioninfoEntity.getProjectName());
					//项目负责人
					tmpExpInfo = StringUtils.replace(tmpExpInfo, "${manager}", illegalExtractioninfoEntity.getUserName());
					//评标时间
					tmpExpInfo = StringUtils.replace(tmpExpInfo, "${bidTime}", DateUtil.dateToString2(illegalExtractioninfoEntity.getOpeningTime()));
					//开标地点
					tmpExpInfo = StringUtils.replace(tmpExpInfo, "${bidAddress}", illegalExtractioninfoEntity.getOpeningAddress());
					/*//专家人数
					tmpExpInfo = StringUtils.replace(tmpExpInfo, "${zjCount}", String.valueOf(entity.getZjCount()));
					//实际抽取人数
					tmpExpInfo = StringUtils.replace(tmpExpInfo, "${sjCount}", String.valueOf(entity.getSjCount()));
*/
					//抽取次数
					tmpExpInfo = StringUtils.replace(tmpExpInfo, "${cqCount}", String.valueOf(illegalExtractioninfoEntity.getExtractTimes()));
					expertInfo += tmpExpInfo;
					counter++;
				}
				chooseRecordTmpl = StringUtils.replace(chooseRecordTmpl, "${expertInfo}", expertInfo);
			}
			String savePath = ServletActionContext.getServletContext().getRealPath("") + File.separator + pdfFileName;
			savePath = FileUtils.replaceSeparator(savePath);
			String fileName = "开标抽取预警";
			String htmlPath = savePath + "/" + fileName + ".html";
			FileUtils.writeFileWithEncoding(htmlPath, chooseRecordTmpl, "UTF-8");

			String pdfPath = savePath + "/" + fileName + ".pdf";
			String footerContent = "";
			PdfUtils.parsePdfByContent(chooseRecordTmpl, pdfPath, "UTF-8", footerContent);
			try {
				// 删除html文件
				File file = new File(htmlPath);
				file.delete();
			} catch (Exception e) {
				e.printStackTrace();
			}
			out.print("{\"filePath\":\"" + pdfFileName + "\"," + "\"fileName\":\"" + (fileName + ".pdf") + "\"}");
		} catch (Exception e) {
			e.printStackTrace();
			out.print("error");
		}
		out.close();
	}



	public List<IllegalExtractioninfoEntity> getIllegallist() {
		return Illegallist;
	}

	public void setIllegallist(List<IllegalExtractioninfoEntity> illegallist) {
		Illegallist = illegallist;
	}

	public String getDecimationbatch() {
		return decimationbatch;
	}

	public void setDecimationbatch(String decimationbatch) {
		this.decimationbatch = decimationbatch;
	}

	public IllegalExtractioninfoEntity getEntity() {
		return entity;
	}

	public void setEntity(IllegalExtractioninfoEntity entity) {
		this.entity = entity;
	}

	public String getIsHandle() {
		return isHandle;
	}

	public void setIsHandle(String isHandle) {
		this.isHandle = isHandle;
	}




}
