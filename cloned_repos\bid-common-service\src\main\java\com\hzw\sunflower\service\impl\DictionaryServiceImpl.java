package com.hzw.sunflower.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzw.sunflower.common.redis.RedisCache;
import com.hzw.sunflower.constant.CommonConstants;
import com.hzw.sunflower.constant.RedisCacheConstants;
import com.hzw.sunflower.constant.constantenum.DictionaryTypeEnum;
import com.hzw.sunflower.constant.constantenum.SubjectMatterTypeLengthEnum;
import com.hzw.sunflower.controller.request.DictionaryReq;
import com.hzw.sunflower.controller.response.DictionaryVO;
import com.hzw.sunflower.controller.response.SubjectMatterVo;
import com.hzw.sunflower.dao.DictionaryMapper;
import com.hzw.sunflower.dto.DictionaryDTO;
import com.hzw.sunflower.entity.Dictionary;
import com.hzw.sunflower.entity.condition.DictionaryCondition;
import com.hzw.sunflower.exception.ExceptionEnum;
import com.hzw.sunflower.exception.ParamsNotNullException;
import com.hzw.sunflower.exception.SunFlowerException;
import com.hzw.sunflower.service.DictionaryService;
import com.hzw.sunflower.util.BeanListUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import static com.hzw.sunflower.util.LoginUtil.getJwtUser;

/**
 * 字典表 Service接口实现
 *
 * <AUTHOR>
 * @version 1.0.0 2021-04-07
 */
@Service
@Slf4j
public class DictionaryServiceImpl extends ServiceImpl<DictionaryMapper, Dictionary> implements DictionaryService {

    @Autowired
    private RedisCache redisCache;

    @Resource
    private DictionaryMapper dictionaryMapper;


    /**
     * 根据条件分页查询字典表 列表
     *
     * @param condition 查询条件
     * @return 分页数据
     */
    @Override
    public IPage<Dictionary> findDictionaryByCondition(DictionaryCondition condition) {
        IPage<Dictionary> page = condition.buildPage();
        QueryWrapper<Dictionary> queryWrapper = condition.buildQueryWrapper(Dictionary.class);
        return this.page(page, queryWrapper);
    }

    @Override
    public List<DictionaryVO> selectDictionaryByParentId(DictionaryReq dictionaryReq) {
        List<DictionaryVO> list =  this.baseMapper.selectDictionaryByParentId(dictionaryReq);
        return list;
    }

    @Override
    public List<Dictionary> selectTreeListByParentId(DictionaryReq dictionaryReq) {


        List<Dictionary>   tree = redisCache.getCacheList("cache_section_expert_specialty");
        if (tree!=null&&tree.size()>0) {
        }else {
            tree= dictionaryMapper.selectList(new LambdaQueryWrapper<Dictionary>().eq(Dictionary::getBusinessType, DictionaryTypeEnum.SUBJECT_MATTER.getType()));
            /*List<Dictionary> dictionaryList = dictionaryMapper.selectList(new LambdaQueryWrapper<Dictionary>().eq(Dictionary::getParentId, dictionaryReq.getId()));

            for (Dictionary dictionary : dictionaryList) {
                tree.add(dictionary);

                List<Dictionary> selectList = dictionaryMapper.selectList(new LambdaQueryWrapper<Dictionary>().likeRight(Dictionary::getCode, dictionary.getCode() + "0"));
                tree.addAll(selectList);
            }*/
            redisCache.setCacheList("cache_section_expert_specialty",tree);
        }
        return tree;
    }

    /**
     * 根据主键ID查询字典表 信息
     *
     * @param id 主键ID
     * @return 字典表 信息
     */
    @Override
    public Dictionary getDictionaryById(Long id) {
        return this.getById(id);
    }

    /**
     * 新增字典表 信息
     *
     * @param dictionary 字典表 信息
     * @return 是否成功
     */
    @Override
    public Boolean addDictionary(Dictionary dictionary) {
        //判断code是否存在
        LambdaQueryWrapper<Dictionary> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Dictionary::getCode,dictionary.getCode());
        Long count = this.baseMapper.selectCount(queryWrapper);
        if(count>0){
            throw new ParamsNotNullException("code重复");
        }
        boolean flag = this.save(dictionary);
        // 删除父节点缓存
         this.deleteCacheByParnetId(dictionary.getId(),dictionary.getParentId());
        return flag;
    }

    /**
     * 修改字典表 信息
     *
     * @param dictionary 字典表 信息
     * @return 是否成功
     */
    @Override
    public Boolean updateDictionary(Dictionary dictionary) {
        //判断code是否存在
        LambdaQueryWrapper<Dictionary> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Dictionary::getCode,dictionary.getCode());
        queryWrapper.ne(Dictionary::getId,dictionary.getId());
        Long count = this.baseMapper.selectCount(queryWrapper);
        if(count>0){
            throw new ParamsNotNullException("code重复");
        }
        boolean flag = this.updateById(dictionary);
        // 删除父节点缓存
        this.deleteCacheByParnetId(dictionary.getId(),dictionary.getParentId());
        return flag;
    }

    /**
     * 根据主键ID删除字典表
     *
     * @param id 主键ID
     * @return 是否成功
     */
    @Override
    public Boolean deleteDictionaryById(Long id) {
        // 查询删除数据的父id
        Dictionary idc = this.getById(id);
        boolean b = this.removeById(id);
        // 删除父节点缓存
        this.deleteCacheByParnetId(idc.getId(),idc.getParentId());
        return b;
    }

    /**
     * 根据主键ID列表批量删除字典表
     *
     * @param idList 主键ID列表
     * @return 是否成功
     */
    @Override
    public Boolean deleteDictionaryByIds(List<Long> idList) {
        return this.removeByIds(idList);
    }

    /**
     * <AUTHOR>
     * @Description 多条件查询子单对象
     * @param condition 条件对象
     * @Return java.util.List<com.hzw.sunflower.dto.DictionaryDTO>
     * @Date 2021/4/28 9:56
     */
    @Override
    public List<DictionaryDTO> getByConditionDicList(DictionaryCondition condition) throws Exception {
        //redis 缓存查询进行返回
//        String key = JSONUtil.toJsonStr(condition);
//        key = redisCache.replace(key);
        // 如果传入 partentId 那么直接更新 如果没有那么通过 code 查找id  更新
        String key = "";
        if(null != condition.getParentId()){
            key = condition.getParentId().toString();
        }else{
            QueryWrapper<Dictionary> dic = new QueryWrapper<>();
            dic.lambda().eq(Dictionary::getCode, condition.getCode())
                                  .eq(Dictionary::getIsDelete,0)
                                  .eq(Dictionary::getParentId,-1);
            key = this.getOne(dic).getId().toString();
        }
        key = RedisCacheConstants.BY_CONDITION_DIC_LIST + key;
        String cacheObject = redisCache.getCacheObject(key);
        log.info("cacheKey: {}", key);
        if (StrUtil.isNotEmpty(cacheObject)) {
            log.info("字典缓存hitKey:{}", key);
            return JSONUtil.toList(JSONUtil.toJsonStr(cacheObject), DictionaryDTO.class);
        }

        // 补充对象字段
        Dictionary dictionary = new Dictionary();
        BeanUtil.copyProperties(condition, dictionary);
        QueryWrapper<Dictionary> dictionaryMapper = null;
        List<Dictionary> list = null;
        //  父ID为空就只差第一级
        if (Objects.isNull(condition.getParentId())) {
            condition.setParentId(-1L);
        }
        // 查找父节点
        dictionaryMapper = condition.buildQueryWrapper(Dictionary.class);
        list = this.list(dictionaryMapper);
        List<DictionaryDTO> dictionaryDTOS = null;
        //  原子性保证对象安全
        AtomicReference<DictionaryDTO> dictionaryPDTO = new AtomicReference<>(new DictionaryDTO());
        //一级节点,和下面有没有子节点
        dictionaryDTOS = getDictionaryDTOS(condition, list, dictionaryDTOS, dictionaryPDTO);
        //要不要删除父节点,把子节点封装返回
        dictionaryDTOS = getDictionaryDTOS(condition, dictionaryDTOS, dictionaryPDTO);
        redisCache.setCacheObject(key, JSONUtil.toJsonStr(dictionaryDTOS));
        return dictionaryDTOS;
    }

    /**
     * <AUTHOR>
     * @Description 根据code查询字段
     * @param code 条件对象
     * @Date 2021/4/28 9:56
     */
    @Override
    public List<String> getDicByCode(Integer code){
        return this.baseMapper.getDicByCode(code);
    }

    /**
     * @param condition      条件
     * @param list           字典集合
     * @param dictionaryDTOS 封装对象
     * @param dictionaryPDTO 父节点
     * <AUTHOR>
     * @Description 父节点查询
     * @Return java.util.List<com.hzw.sunflower.dto.DictionaryDTO>
     * @Date 2021/4/28 9:48
     */
    private List<DictionaryDTO> getDictionaryDTOS(DictionaryCondition condition, List<Dictionary> list, List<DictionaryDTO> dictionaryDTOS, AtomicReference<DictionaryDTO> dictionaryPDTO) {
        if (!CollectionUtils.isEmpty(list)) {
            // 副本引用
            AtomicReference<DictionaryDTO> finalDictionaryPDTO1 = dictionaryPDTO;
            // 循环父节点 返回一个集合
            dictionaryDTOS = list.stream().map(item -> {
                DictionaryDTO dictionaryDTO = new DictionaryDTO();
                Long id = item.getId();
                //  查询子节点
                QueryWrapper<Dictionary> notItemMapper = new QueryWrapper<>();
                notItemMapper.lambda().eq(Dictionary::getParentId, id).eq(Dictionary::getIsDelete,0);
                List<Dictionary> notItem = this.list(notItemMapper);
                // 赋值 设置下面有子节点
                BeanUtil.copyProperties(item, dictionaryDTO);
                if (Objects.nonNull(notItem) && !notItem.isEmpty()) {
                    dictionaryDTO.setNotItemFlag(Boolean.TRUE);
                }
                //  传入条件删除 排除父节点 把子节点放入
                if (condition.getDeleteParent()) {
                    if (item.getParentId().longValue() < 0) {
                        finalDictionaryPDTO1.set(dictionaryDTO);
                    }
                }
                // 返回对象
                return dictionaryDTO;
                //形成结果集
            }).collect(Collectors.toList());
        }
        return dictionaryDTOS;
    }

    /***
     * <AUTHOR>
     * @Description 子节点查询并删除一级父节点
     * @param condition 条件
     * @param dictionaryDTOS 字典集合
     * @param dictionaryPDTO  父节点
     * @Return java.util.List<com.hzw.sunflower.dto.DictionaryDTO>
     * @Date 2021/4/28 9:51
     */
    private List<DictionaryDTO> getDictionaryDTOS(DictionaryCondition condition, List<DictionaryDTO> dictionaryDTOS, AtomicReference<DictionaryDTO> dictionaryPDTO) {
        QueryWrapper<Dictionary> dictionaryMapper;
        List<Dictionary> list;
        if (Objects.nonNull(dictionaryPDTO.get()) && condition.getDeleteParent()) {
            //设置条件进行查询父节点下的子节点
            Long pId = dictionaryPDTO.get().getId();
            dictionaryDTOS.remove(dictionaryPDTO.get());
            condition.setParentId(pId);
            condition.setCode(null);
            dictionaryMapper = condition.buildQueryWrapper(Dictionary.class);
            // 查找所有子节点进行封装
            list = this.list(dictionaryMapper);
            dictionaryDTOS = null;
            dictionaryPDTO = new AtomicReference<>(new DictionaryDTO());
            if (!CollectionUtils.isEmpty(list)) {
                AtomicReference<DictionaryDTO> finalDictionaryPDTO = dictionaryPDTO;
                //遍历封装子节点
                dictionaryDTOS = list.stream().map(item -> {
                    DictionaryDTO dictionaryDTO = new DictionaryDTO();
                    Long id = item.getId();
                    QueryWrapper<Dictionary> notItemMapper = new QueryWrapper<>();
                    notItemMapper.lambda().eq(Dictionary::getParentId, id);
                    List<Dictionary> notItem = this.list(notItemMapper);
                    BeanUtil.copyProperties(item, dictionaryDTO);
                    //设置下面是否有子节点
                    if (Objects.nonNull(notItem) && !notItem.isEmpty()) {
                        dictionaryDTO.setNotItemFlag(Boolean.TRUE);
                    }
                    //父当前对象如果有父节点就放入对象暂存器
                    if (condition.getDeleteParent()) {
                        if (item.getParentId().equals(-1L)) {
                            finalDictionaryPDTO.set(dictionaryDTO);
                        }

                    }
                    return dictionaryDTO;
                }).collect(Collectors.toList());
            }
        }
        return dictionaryDTOS;
    }

    /**
     * <AUTHOR>
     * @Description 删除缓存
     * @Return java.lang.Boolean
     * @Date 2021/4/28 9:51
     */
    @Override
    public Boolean deleteCache() {
        return redisCache.deletePrefixObject(RedisCacheConstants.BY_CONDITION_DIC_LIST);
    }

    /**
     * 设置启用禁用
     * @param condition
     * @return
     */
    @Override
    public Boolean updateDisable(DictionaryCondition condition) {
        Dictionary dictionary = new Dictionary();
        dictionary.setId(condition.getId());
        dictionary.setIsDisable(condition.getIsDisable());
        return this.baseMapper.updateById(dictionary)>0;
    }

    @Override
    public List<Dictionary> selectDictionaryForCurrency() {
        return this.baseMapper.selectDictionaryForCurrency();
    }

    @Override
    public List<Dictionary> selectAllCountry() {
        return dictionaryMapper.selectAllCountry();
    }

    /**
     * 查询历史地点（上一次选择地点）
     * @return
     */
    @Override
    public List<DictionaryVO> getByHistoryDicList() {
        List<DictionaryVO> dicList = this.baseMapper.getByHistoryDicList(getJwtUser().getUserId());
        if(!dicList.isEmpty() && null != dicList.get(0)){
            for (DictionaryVO d :dicList) {
                d.setNotItemFlag(this.baseMapper.getChildrenList(d.getId()));
            }
        }
        return dicList;
    }

    /**
     * 新增标的物信息
     * @param dictionary
     * @return
     */
    @Override
    public Boolean saveSubjectMatter(Dictionary dictionary) {
        boolean result = true;
        if(dictionary.getParentId() == null){
            throw new SunFlowerException(ExceptionEnum.ILLEGAL_PARAMETER_EXCEPTION, ExceptionEnum.ILLEGAL_PARAMETER_EXCEPTION.getMessage());
        }
        //父类
        Dictionary parent = getById(dictionary.getParentId());
        dictionary.setIsUserAdd(CommonConstants.YES);
        dictionary.setBusinessType(DictionaryTypeEnum.SUBJECT_MATTER.getType());
        //新增
        if(dictionary.getId() == null || dictionary.getId() == 0){
            dictionary.setCode(getSubjectMatterCode(parent));
            result = save(dictionary);
        }else{//修改
            Dictionary nowDic = getById(dictionary.getId());
            //如果父级id改变了，重新生成code
            if(!nowDic.getParentId().equals(dictionary.getParentId())){
                dictionary.setCode(getSubjectMatterCode(parent));
            }
            result = updateById(dictionary);
        }
        //每次新增后删除缓存
        redisCache.deleteObject("cache_section_expert_specialty");
        return result;
    }


    /**
     * 生成code
     * @param parent
     * @return
     */
    private String getSubjectMatterCode(Dictionary parent){
        String code = parent.getCode()+"01";
        LambdaQueryWrapper<Dictionary> childrenQuery = new LambdaQueryWrapper<>();
        childrenQuery.eq(Dictionary::getParentId,parent.getId())
                .orderByDesc(Dictionary::getCode);
        List<Dictionary> children = list(childrenQuery);
        if(children != null && children.size()>0){
            //获取该子集下最大的code
            String maxCode = children.get(0).getCode();//A0101
            String lastCode = maxCode.substring(1, maxCode.length());//0101
            String newCode = (Integer.valueOf(lastCode) + 1)+"";//101
            if(lastCode.length() != newCode.length()){
                newCode = "0"+newCode;
            }
            code = maxCode.substring(0,1)+newCode;
        }
        return code;
    }

    /**
     * 标的物分类数量
     * @param dictionaryReq
     * @return
     */
    @Override
    public List<SubjectMatterVo> getSubjectMatterCount(DictionaryReq dictionaryReq) {
        List<SubjectMatterVo>   tree = new ArrayList<>();
        LambdaQueryWrapper<Dictionary> query = new LambdaQueryWrapper<>();
        query.eq(Dictionary::getParentId, dictionaryReq.getId());
        List<Dictionary> parent = list(query);
        tree = BeanListUtil.convertList(parent, SubjectMatterVo.class);
        for (SubjectMatterVo dictionary : tree) {
            LambdaQueryWrapper<Dictionary> allquery = new LambdaQueryWrapper<Dictionary>();
            allquery.likeRight(Dictionary::getCode, dictionary.getCode())
                    .eq(Dictionary::getBusinessType,DictionaryTypeEnum.SUBJECT_MATTER.getType());
            List<Dictionary> selectList = dictionaryMapper.selectList(allquery);
            List<Dictionary> two = selectList.stream().filter(x -> SubjectMatterTypeLengthEnum.TWO.getLength().equals(x.getCode().length())).collect(Collectors.toList());
            List<Dictionary> three = selectList.stream().filter(x -> SubjectMatterTypeLengthEnum.THREE.getLength().equals(x.getCode().length())).collect(Collectors.toList());
            List<Dictionary> four = selectList.stream().filter(x -> SubjectMatterTypeLengthEnum.FOUR.getLength().equals(x.getCode().length())).collect(Collectors.toList());
            dictionary.setTwoCount(two.size());
            dictionary.setThreeCount(three.size());
            dictionary.setFourCount(four.size());
        }
        return tree;
    }

    /**
     * 查询所有缓存时间
     * @return
     */
    @Override
    public Dictionary selectDictionaryForCacheRefreshTime() {
        return this.baseMapper.selectDictionaryForCacheRefreshTime();
    }

    /**
     * 根据code获取字典
     * @param code
     * @return
     */
    @Override
    public Dictionary getDictionaryByCode(String code) {
        return this.baseMapper.getDictionaryByCode(code);
    }


    /**
     * <AUTHOR>
     * @Description 删除父id下的数据缓存
     * @Return java.lang.Boolean
     * @Date 2021/4/28 9:51
     */
    public void deleteCacheByParnetId(Long id,Long partentId) {
        if(partentId != -1){
            redisCache.deleteObject(RedisCacheConstants.BY_CONDITION_DIC_LIST + partentId);
        }else{
            //修改的根目录 按要求 不需要删除缓存。等待缓存自然失效即可
;
        }
    }


}
