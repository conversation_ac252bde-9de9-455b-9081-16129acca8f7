package com.hzw.sunflower.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/**
 * 可重新招标标段信息
 *
 * <AUTHOR> 2021/09/09
 * @version 1.0.0
 */

@ApiModel(description = "可以重新招标标段")
@Data
@Accessors(chain = true)
public class ReSectionCanDTO {

    @ApiModelProperty(value = "项目ID", position = 1)
    private Long projectId;

    @ApiModelProperty(value = "标段ID", position = 2)
    private Long sectionId;

    @ApiModelProperty(value = "标段名称", position = 3)
    private String packageName;

    @ApiModelProperty(value = "标段包号", position = 4)
    private Integer packageNumber;

    @ApiModelProperty(value = "标段状态", position = 5)
    private Integer status;

    @ApiModelProperty(value = "公告ID", position = 6)
    private Long noticeId;

    @ApiModelProperty(value = "公告进度", position = 7)
    private Integer noticeProgress;

    @ApiModelProperty(value = "是否划分标段包(0:无包 1:有包)", position = 8)
    private Integer packageSegmentStatus;
}
