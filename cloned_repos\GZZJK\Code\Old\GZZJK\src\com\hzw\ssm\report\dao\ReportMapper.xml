<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper 
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hzw.ssm.report.dao.ReportMapper">
	<resultMap type="ReportEntity" id="ReportEntity">
		<result column="PROJECT_NAME" property="projectName"></result>
		<result column="USER_NAME" property="expertName"></result>
		<result column="MOBILEPHONE" property="phone"></result>
		<result column="ID_NO" property="idCard"></result>
		<result column="MANAGER" property="manager"></result>
		<result column="BID_TIME" property="bidTime"></result>
		<result column="APPRAISE_COLUMN" property="appraiseColumn"></result>
		<result column="score" property="score"></result>
		<result column="EXPERT_ID" property="expertId"></result>
		<result column="PROJECT_NO" property="projectNo"></result>
		<result column="OUT_TIME" property="outTime"></result>
		<result column="OUT_REASON" property="outReason"></result>
		<result column="APPRAISE_USER" property="appraiseUser"></result>
		<result column="APPRAISE_TIME" property="appraiseTime"></result>
		<result column="REVIEW_USER" property="reviewUser"></result>
		<result column="REVIEW_TIME" property="reviewTime"></result>
		<result column="user_id" property="expertId"></result>
		<result column="APPRAISE_ID" property="appraiseId"></result>
		<result column="EXTRACT_RESULT_ID" property="extractResultId"></result>
		<result column="is_approve" property="isApprove"></result>
		<result column="EVAL_SCORE" property="evalScore"></result>
		<result column="REVIEW_STATUS" property="reviewStatus"></result>
	</resultMap>
	<resultMap type="HashMap" id="testMap">  
	    <result column="score" property="key" />  
	    <association property="value" resultMap="ReportEntity"></association>  
	  </resultMap>
	<resultMap type="QueryBean" id="QueryBean">
		<result column="extract_result_id" property="extractResultId" />
		<result column="review_time" property="reviewTimeStr" />
	</resultMap>
	<!-- 明细查询 -->
	<select id="selectReportDetail" resultMap="ReportEntity" parameterType="ReportEntity">
		select distinct a.sumScore,
		a.user_id,a.REVIEW_STATUS,
		a.user_name,
		a.mobilephone,
		a.Id_No,
		a.is_approve,
		a.PROJECT_NAME,
		a.PROJECT_NO,
		a.MANAGER,
		a.BID_TIME,
		a.APPRAISE_USER,
		a.appraise_time,
		a.EXTRACT_RESULT_ID,a.REVIEW_USER,a.REVIEW_TIME,
		a.Decimationbatch
		from (SELECT distinct sum(t.score) over(partition by TXI.USER_ID) sumScore,
		txi.user_id,T.REVIEW_STATUS,
		txi.user_name,
		txi.mobilephone,
		txi.Id_No,
		TP.PROJECT_NAME,
		TP.PROJECT_NO,
		TP.MANAGER,
		TP.BID_TIME,
		T.APPRAISE_USER,
		t.appraise_time,
		T.EXTRACT_RESULT_ID,
		t.score,
		TXR.is_approve,
		T.REVIEW_USER,
		T.REVIEW_TIME,
		txr.Decimationbatch
		FROM T_PROJECT TP
		LEFT JOIN t_condition TC ON TC.PROJECT_ID = TP.PROJECT_ID
		LEFT JOIN t_extract_result  TXR      ON TXR.CONDITION_ID=TC.ID
		LEFT JOIN t_appraise T  ON  TXR.ID=T.EXTRACT_RESULT_ID
		LEFT JOIN T_EXPERT_INFO TXI ON  TXI.USER_ID = TXR.EXPERT_ID
		LEFT JOIN T_APPRAISE_INFO TAI ON TAI.APPRAISE_INFO_ID = T.APPRAISE_INFO_ID
		WHERE 
		<![CDATA[ 
			T.SCORE<>0
		 ]]>
		 <if test="expertName != null and expertName != ''" >
		 <![CDATA[ 
			AND TXI.USER_NAME like '%${expertName}%'
			]]>
		</if>
		<if test="extractResultId != null and extractResultId != ''" >
			AND t.extract_result_id = #{extractResultId}
		</if>
		<if test="isApprove != 0">
			and T.REVIEW_STATUS  = #{isApprove}
		</if>
		<if test="isApprove == 0">
			and T.REVIEW_STATUS is null
		</if>
		<if test="projectName != null and projectName != ''" >
			AND TP.PROJECT_NAME like '%${projectName}%'
		</if>
		<if test="startTime != null and startTime !='' and endTime != null and endTime !=''" >
		<![CDATA[
			AND  TO_CHAR(TP.BID_TIME,'yyyy-MM-dd') BETWEEN #{startTime} AND #{endTime}
			]]>
		</if>
		<if test="appraiseUser != null and appraiseUser != ''" >
			AND T.APPRAISE_USER  like '%${appraiseUser}%'
		</if>
		<if test="reviewTimeStr != null " >
			<![CDATA[
		and to_char(t.REVIEW_TIME,'YYYY-MM-DD HH24:MI:SS')= #{reviewTimeStr}
			]]>
		</if>
		<if test="startAppraiseTime != null and startAppraiseTime !='' and endAppraiseTime != null and endAppraiseTime !=''" >
			<![CDATA[
			AND  TO_CHAR(t.appraise_time,'yyyy-MM-dd') BETWEEN #{startAppraiseTime} AND #{endAppraiseTime}
			]]>
		</if>
		) a
		 ORDER BY 
		 ${sortWay}
	</select>
	<select id="queryresultId" parameterType="ReportEntity" resultMap="QueryBean">
		select  distinct t.extract_result_id as extractResultId,t.review_time as reviewTimeStr
		FROM T_PROJECT TP
		LEFT JOIN t_condition TC
		ON TC.PROJECT_ID = TP.PROJECT_ID
		LEFT JOIN t_extract_result TXR
		ON TXR.CONDITION_ID = TC.ID
		LEFT JOIN t_appraise T
		ON TXR.ID = T.EXTRACT_RESULT_ID
		LEFT JOIN T_EXPERT_INFO TXI
		ON TXI.USER_ID = TXR.EXPERT_ID
		WHERE
		<![CDATA[
			T.SCORE<>0
		 ]]>
		and TXR.Join_Status='0'
		<if test="expertName != null and expertName != ''" >
			<![CDATA[
			AND TXI.USER_NAME  like '%${expertName}%'
			]]>
		</if>
		<if test="isApprove != 0">
			and T.REVIEW_STATUS  = #{isApprove}
		</if>
		<if test="isApprove == 0">
			and T.REVIEW_TIME is null and t.APPRAISE_TIME is not null
		</if>
		<if test="projectName != null and projectName != ''" >
			AND TP.PROJECT_NAME like '%${projectName}%'
		</if>
		<if test="startTime != null and startTime !='' and endTime != null and endTime !=''" >
			<![CDATA[
			AND  TO_CHAR(TP.BID_TIME,'yyyy-MM-dd') BETWEEN #{startTime} AND #{endTime}
			]]>
		</if>
		<if test="appraiseUser != null and appraiseUser != ''" >
			AND T.APPRAISE_USER like '%${appraiseUser}%'
		</if>
		<if test="startAppraiseTime != null and startAppraiseTime !='' and endAppraiseTime != null and endAppraiseTime !=''" >
			<![CDATA[
			AND  TO_CHAR(t.appraise_time,'yyyy-MM-dd') BETWEEN #{startAppraiseTime} AND #{endAppraiseTime}
			]]>
		</if>
	</select>
	<!--专家考核明细-->
	<select id="selectReportDetails" resultMap="ReportEntity" parameterType="ReportEntity">
		SELECT distinct
		TXR.EXPERT_ID,T.REVIEW_USER,TAI.APPRAISE_COLUMN,t.review_time,txr.decimationbatch, t.score,
		TXI.USER_NAME,TXI.MOBILEPHONE,TXI.ID_NO,TP.PROJECT_NAME,TP.MANAGER,TXR.is_approve,
		TP.BID_TIME,TP.PROJECT_NO,TXI.USER_ID
		FROM T_PROJECT TP
		LEFT JOIN t_condition TC ON TC.PROJECT_ID = TP.PROJECT_ID
		LEFT JOIN t_extract_result  TXR      ON TXR.CONDITION_ID=TC.ID
		LEFT JOIN t_appraise T  ON  TXR.ID=T.EXTRACT_RESULT_ID
		LEFT JOIN T_EXPERT_INFO TXI ON  TXI.USER_ID = TXR.EXPERT_ID
		LEFT JOIN T_APPRAISE_INFO TAI
		ON TAI.APPRAISE_INFO_ID = T.APPRAISE_INFO_ID
		WHERE
		<![CDATA[
			T.SCORE<>0
		 ]]>
		and TXR.IS_APPROVE='1'
		<if test="expertName != null and expertName != ''" >
			<![CDATA[
			AND TXI.USER_NAME = #{expertName}
			]]>
		</if>
		<if test="extractResultId != null and extractResultId != ''" >
			AND t.extract_result_id = #{extractResultId}
		</if>
		<if test="appraiseTimeStr != null " >
			<![CDATA[
		and to_char(t.appraise_time,'YYYY-MM-DD HH24:MI:SS')= #{appraiseTimeStr}
			]]>
		</if>
		<if test="projectName != null and projectName != ''" >
			<![CDATA[
			AND TP.PROJECT_NAME = #{projectName}
			]]>
		</if>
		<if test="startTime != null and startTime !='' and endTime != null and endTime !=''" >
			<![CDATA[
			AND  TO_CHAR(TP.BID_TIME,'yyyy-MM-dd') BETWEEN #{startTime} AND #{endTime}
			]]>
		</if>
		<if test="startReviewTime != null and startReviewTime !='' and endReviewTime != null and endReviewTime !=''" >
			<![CDATA[
			AND  TO_CHAR(t.review_time,'yyyy-MM-dd') BETWEEN #{startReviewTime} AND #{endReviewTime}
			]]>
		</if>
		<if test="reviewUser != null and reviewUser != ''">
			AND T.REVIEW_USER = #{reviewUser}
		</if>
		<if test="decimationBatch != null and decimationBatch != ''">
			AND txr.decimationbatch = #{decimationBatch}
		</if>
		<if test="expertId != null and expertId != ''">
			AND TXI.USER_ID = #{expertId}
		</if>
		<if test="year != null and year != ''">
			AND TO_CHAR(t.review_time,'yyyy') = #{year}
		</if>
		ORDER BY
		${sortWay} asc
	</select>
	<select id="selectDetailResultId" parameterType="ReportEntity" resultType="QueryBean">
		select  distinct t.extract_result_id as extractResultId,t.appraise_time as reviewTimeStr
		FROM T_PROJECT TP
		LEFT JOIN t_condition TC
		ON TC.PROJECT_ID = TP.PROJECT_ID
		LEFT JOIN t_extract_result TXR
		ON TXR.CONDITION_ID = TC.ID
		LEFT JOIN t_appraise T
		ON TXR.ID = T.EXTRACT_RESULT_ID
		LEFT JOIN T_EXPERT_INFO TXI
		ON TXI.USER_ID = TXR.EXPERT_ID
		WHERE <![CDATA[
			T.SCORE<>0
		 ]]>
		and t.review_status='1'
		<if test="expertName != null and expertName != ''" >
			<![CDATA[
			AND TXI.USER_NAME = #{expertName}
			]]>
		</if>
		<if test="projectName != null and projectName != ''" >
			<![CDATA[
			AND TP.PROJECT_NAME = #{projectName}
			]]>
		</if>
		<if test="startTime != null and startTime !='' and endTime != null and endTime !=''" >
			<![CDATA[
			AND  TO_CHAR(TP.BID_TIME,'yyyy-MM-dd') BETWEEN #{startTime} AND #{endTime}
			]]>
		</if>
		<if test="startReviewTime != null and startReviewTime !='' and endReviewTime != null and endReviewTime !=''" >
			<![CDATA[
			AND  TO_CHAR(t.review_time,'yyyy-MM-dd') BETWEEN #{startReviewTime} AND #{endReviewTime}
			]]>
		</if>
		<if test="reviewUser != null and reviewUser != ''">
			AND T.REVIEW_USER = #{reviewUser}
		</if>
		<if test="decimationBatch != null and decimationBatch != ''">
			AND txr.decimationbatch = #{decimationBatch}
		</if>
		<if test="expertId != null and expertId != ''">
			AND TXI.USER_ID = #{expertId}
		</if>
		<if test="year != null and year != ''">
		AND TO_CHAR(t.review_time,'yyyy') = #{year}
	</if>
	</select>
		<!-- 明细查询 -->
	<select id="queryReportDetail" resultMap="ReportEntity" parameterType="ReportEntity">
	SELECT
		 TXR.EXPERT_ID, t.score,TXI.USER_NAME,TXI.MOBILEPHONE,TXI.ID_NO,TP.PROJECT_NAME,TP.PROJECT_NO,
		 TP.MANAGER,TP.BID_TIME,TAI.APPRAISE_COLUMN
		  ,row_number()over(partition by ${sortWay} ORDER BY TP.BID_TIME desc) rn
		 FROM T_PROJECT TP
	    LEFT JOIN t_condition TC ON TC.PROJECT_ID = TP.PROJECT_ID
	    LEFT JOIN t_extract_result  TXR      ON TXR.CONDITION_ID=TC.ID
	    LEFT JOIN t_appraise T  ON  TXR.ID=T.EXTRACT_RESULT_ID
	    LEFT JOIN T_EXPERT_INFO TXI ON  TXI.USER_ID = TXR.EXPERT_ID
	    LEFT JOIN T_APPRAISE_INFO TAI ON TAI.APPRAISE_INFO_ID = T.APPRAISE_INFO_ID
		WHERE 
		<![CDATA[ 
			T.SCORE<>0
		 ]]>
		 <if test="expertName != null and expertName != ''">
		 <![CDATA[ 
			AND instr(TXI.USER_NAME , #{expertName})>0
			]]>
		</if>
		<if test="projectName != null and projectName !=''" >
		<![CDATA[
			AND instr(TP.PROJECT_NAME , #{projectName})>0 
			]]>
		</if>
		<if test="startTime != null and startTime !='' and endTime != null and endTime !=''" >
		<![CDATA[
			AND  TO_CHAR(TP.BID_TIME,'yyyy-MM-dd') BETWEEN #{startTime} AND #{endTime}
			]]>
		</if>
		
		 ORDER BY 
		  ${sortWay} asc
	</select>
	
	
		<!-- 明细查询 -->
	<select id="queryReportDetailMap" resultMap="ReportEntity" parameterType="ReportEntity">
	SELECT
		 TXR.EXPERT_ID, t.score,TXI.USER_NAME,TXI.MOBILEPHONE,TXI.ID_NO,TP.PROJECT_NAME,TP.PROJECT_NO,TP.MANAGER,TP.BID_TIME,TAI.APPRAISE_COLUMN
		  ,row_number()over(partition by ${sortWay} ORDER BY TP.BID_TIME desc) rn
		 FROM T_PROJECT TP
	    LEFT JOIN t_condition TC ON TC.PROJECT_ID = TP.PROJECT_ID
	    LEFT JOIN t_extract_result  TXR      ON TXR.CONDITION_ID=TC.ID
	    LEFT JOIN t_appraise T  ON  TXR.ID=T.EXTRACT_RESULT_ID
	    LEFT JOIN T_EXPERT_INFO TXI ON  TXI.USER_ID = TXR.EXPERT_ID
	    LEFT JOIN T_APPRAISE_INFO TAI ON TAI.APPRAISE_INFO_ID = T.APPRAISE_INFO_ID
		WHERE 
		<![CDATA[ 
			T.SCORE<>0
		 ]]>
		 <if test="expertName != null and expertName != ''">
		 <![CDATA[ 
			AND instr(TXI.USER_NAME , #{expertName})>0
			]]>
		</if>
		<if test="projectName != null and projectName !=''" >
		<![CDATA[
			AND instr(TP.PROJECT_NAME , #{projectName})>0 
			]]>
		</if>
		<if test="startTime != null and startTime !='' and endTime != null and endTime !=''" >
		<![CDATA[
			AND  TO_CHAR(TP.BID_TIME,'yyyy-MM-dd') BETWEEN #{startTime} AND #{endTime}
			]]>
		</if>
		
		 ORDER BY 
		  ${sortWay} asc
	</select>
	
			<!-- 汇总查询 -->
 	<select id="queryPageReportSummary" resultMap="ReportEntity" parameterType="ReportEntity">

		SELECT distinct A.sumScore ,A.EXPERT_ID,A.year,
		A.USER_NAME,A.USER_ID,
		A.MOBILEPHONE,A.bidDate,A.startBidDate,A.endBidDate,
		A.ID_NO
		FROM (SELECT distinct TXR.EXPERT_ID,to_char(t.REVIEW_TIME,'yyyy') year,
		TXI.USER_NAME,TXI.USER_ID,
		TXI.MOBILEPHONE,TXI.START_BID_DATE startBidDate,TXI.END_BID_DATE endBidDate,
		concat(concat(to_char(TXI.START_BID_DATE,'yyyy.MM.dd'),'~'),to_char(TXI.END_BID_DATE,'yyyy.MM.dd')) bidDate,
		TXI.ID_NO,
		sum(t.score) over(partition by TXI.USER_ID) sumScore
		FROM T_PROJECT TP
		LEFT JOIN t_condition TC
		ON TC.PROJECT_ID = TP.PROJECT_ID
		LEFT JOIN t_extract_result TXR
		ON TXR.CONDITION_ID = TC.ID
		LEFT JOIN t_appraise T
		ON TXR.ID = T.EXTRACT_RESULT_ID
		LEFT JOIN T_EXPERT_INFO TXI
		ON TXI.USER_ID = TXR.EXPERT_ID
		LEFT JOIN T_APPRAISE_INFO TAI
		ON TAI.APPRAISE_INFO_ID = T.APPRAISE_INFO_ID
			WHERE 
			<![CDATA[ 
				T.SCORE<>0
			 ]]>
		and TXR.IS_APPROVE='1'
		and t.review_status='1'
			 <if test="expertName != null and expertName != ''">
					AND TXI.USER_NAME like '%${expertName}%'
			</if>
		<if test="phone != null and phone != ''">
					AND TXI.MOBILEPHONE = #{phone}
		</if>
		<if test="year != null and year != ''">
			AND to_char(t.REVIEW_TIME, 'yyyy') = #{year}
		</if>
		<if test="extractResultId != null and extractResultId != ''" >
			AND t.extract_result_id = #{extractResultId}
		</if>

		<if test="expertId != null and expertId != ''" >
			AND TXI.USER_ID = #{expertId}
		</if>

		<if test="appraiseTimeStr != null " >
			<![CDATA[
		and to_char(t.appraise_time,'YYYY-MM-DD HH24:MI:SS')= #{appraiseTimeStr}
			]]>
		</if>
		) A WHERE 
		<![CDATA[ 
			A.SUMSCORE >0
		 ]]>
		order by ${sortWay}
	</select>
	<select id="queryPageDetailExtractId" parameterType="ReportEntity" resultType="String">
		select distinct TXI.USER_ID as expertId
		FROM T_PROJECT TP
		LEFT JOIN t_condition TC
		ON TC.PROJECT_ID = TP.PROJECT_ID
		LEFT JOIN t_extract_result TXR
		ON TXR.CONDITION_ID = TC.ID
		LEFT JOIN t_appraise T
		ON TXR.ID = T.EXTRACT_RESULT_ID
		LEFT JOIN T_EXPERT_INFO TXI
		ON TXI.USER_ID = TXR.EXPERT_ID
		WHERE
		<![CDATA[
				T.SCORE<>0
			 ]]>
		<![CDATA[
				AND  TO_CHAR(TP.BID_TIME,'yyyy-MM-dd') BETWEEN #{startTime} AND #{endTime}
			]]>
		and TXR.IS_APPROVE='1'
		<if test="expertName != null and expertName != ''">
			<![CDATA[
					AND instr(TXI.USER_NAME , #{expertName})>0
				]]>
		</if>
		<if test="year != null and year != ''">
			<![CDATA[
					AND to_char(t.REVIEW_TIME, 'yyyy')= #{year}
				]]>
		</if>

	</select>
	<select id="selectDetailExpertId" parameterType="ReportEntity" resultType="String">
		SELECT distinct A.EXPERT_ID
		FROM (SELECT distinct TXR.EXPERT_ID,to_char(t.REVIEW_TIME,'yyyy') year,
		TXI.USER_NAME,TXI.USER_ID,
		TXI.MOBILEPHONE,
		TXI.ID_NO,
		sum(t.score) over(partition by TXI.USER_ID) sumScore
		FROM T_PROJECT TP
		LEFT JOIN t_condition TC
		ON TC.PROJECT_ID = TP.PROJECT_ID
		LEFT JOIN t_extract_result TXR
		ON TXR.CONDITION_ID = TC.ID
		LEFT JOIN t_appraise T
		ON TXR.ID = T.EXTRACT_RESULT_ID
		LEFT JOIN T_EXPERT_INFO TXI
		ON TXI.USER_ID = TXR.EXPERT_ID
		LEFT JOIN T_APPRAISE_INFO TAI
		ON TAI.APPRAISE_INFO_ID = T.APPRAISE_INFO_ID
		WHERE
		<![CDATA[
				T.SCORE<>0
			 ]]>
		and TXR.IS_APPROVE='1'
		and t.review_status='1'
		<if test="expertName != null and expertName != ''">
			AND TXI.USER_NAME like '%${expertName}%'
		</if>
		<if test="phone != null and phone != ''">
			AND TXI.MOBILEPHONE = #{phone}
		</if>
		<if test="year != null and year != ''">
			AND to_char(t.REVIEW_TIME, 'yyyy') = #{year}
		</if>
		<if test="extractResultId != null and extractResultId != ''" >
			AND t.extract_result_id = #{extractResultId}
		</if>
		<if test="appraiseTimeStr != null " >
			<![CDATA[
		and to_char(t.appraise_time,'YYYY-MM-DD HH24:MI:SS')= #{appraiseTimeStr}
			]]>
		</if>
		) A WHERE
		<![CDATA[
			A.SUMSCORE >0
		 ]]>
	</select>
		<!-- 汇总查询 -->
 	<select id="queryReportSummary" resultMap="ReportEntity" parameterType="ReportEntity">
		SELECT to_char(t.REVIEW_TIME, 'yyyy') year,t.REVIEW_TIME,
		t.score,txr.DECIMATIONBATCH,
		TXI.USER_NAME,
		TXI.MOBILEPHONE,
		TXI.ID_NO,
		TP.PROJECT_NAME,
		TP.MANAGER,
		TP.BID_TIME,
		TAI.APPRAISE_COLUMN,
		sum(t.score) over(partition by TXI.USER_ID) sumScore
		FROM T_PROJECT TP
		LEFT JOIN t_condition TC ON TC.PROJECT_ID = TP.PROJECT_ID
		LEFT JOIN t_extract_result  TXR      ON TXR.CONDITION_ID=TC.ID
		LEFT JOIN t_appraise T  ON  TXR.ID=T.EXTRACT_RESULT_ID
		LEFT JOIN T_EXPERT_INFO TXI ON  TXI.USER_ID = TXR.EXPERT_ID
		LEFT JOIN T_APPRAISE_INFO TAI ON TAI.APPRAISE_INFO_ID = T.APPRAISE_INFO_ID
		WHERE
		<![CDATA[
				T.SCORE<>0
			 ]]>

		and TXR.IS_APPROVE='1'  and t.review_status = '1'
		<if test="expertName != null and expertName != ''" >
			<![CDATA[
			AND TXI.USER_NAME = #{expertName}
			]]>
		</if>
		<if test="expertId != null and expertId != ''" >
			AND TXI.USER_ID = #{expertId}
		</if>

		<if test="extractResultId != null and extractResultId != ''" >
			AND t.extract_result_id = #{extractResultId}
		</if>
		<if test="appraiseTimeStr != null " >
			<![CDATA[
		and to_char(t.appraise_time,'YYYY-MM-DD HH24:MI:SS')= #{appraiseTimeStr}
			]]>
		</if>
		<if test="projectName != null and projectName != ''" >
			<![CDATA[
			AND TP.PROJECT_NAME = #{projectName}
			]]>
		</if>

		<if test="reviewUser != null and reviewUser != ''">
			AND T.REVIEW_USER = #{reviewUser}
		</if>
		<if test="decimationBatch != null and decimationBatch != ''">
			AND txr.decimationbatch = #{decimationBatch}
		</if>
		<if test="expertId != null and expertId != ''">
			AND TXI.USER_ID = #{expertId}
		</if>
		<if test="year != null and year != ''">
			AND TO_CHAR(t.review_time,'yyyy') = #{year}
		</if>
	</select>
	
	<!-- 已注销 -->
 	<select id="queryPageReportCancellation" resultMap="ReportEntity" parameterType="ReportEntity">
	SELECT t.user_name,T.MOBILEPHONE,T.ID_NO,T.OUT_TIME,T.OUT_REASON 
	FROM T_EXPERT_INFO T
	WHERE T.OUT_REASON  IS NOT NULL
	 <![CDATA[
				AND  TO_CHAR(T.OUT_TIME,'yyyy-MM-dd') BETWEEN #{startTime} AND #{endTime}
			]]>
	 <if test="expertName != null and expertName != ''">
		 <![CDATA[ 
			AND instr(T.USER_NAME , #{expertName})>0
		]]>
		
	</if>
	ORDER BY T.ID_NO
	</select>
	<!-- 待注销 -->
 	<select id="queryPageReportCancellationT" resultMap="ReportEntity" parameterType="ReportEntity">
		SELECT 
		 TXR.EXPERT_ID,TXI.USER_NAME,TXI.MOBILEPHONE,TXI.ID_NO,TP.PROJECT_NAME,TP.MANAGER,TP.BID_TIME,TXR.REASON AS outReason
		FROM T_PROJECT TP 
	    LEFT JOIN t_condition TC ON TC.PROJECT_ID = TP.PROJECT_ID        
	    LEFT JOIN t_extract_result  TXR ON TXR.CONDITION_ID=TC.ID                      
        LEFT JOIN T_EXPERT_INFO TXI ON  TXI.USER_ID = TXR.EXPERT_ID
       WHERE 
       	TXR.REASON IN 
	       	<foreach collection="reasonLst" index="index" item="item" open="(" separator="," close=")" >
	       		'${item}'
	   		</foreach>
	   		<![CDATA[
				AND  TO_CHAR(TP.BID_TIME,'yyyy-MM-dd') BETWEEN #{startTime} AND #{endTime}
			]]>
       <if test="expertName != null and expertName != ''">
			 <![CDATA[ 
				AND instr(TXI.USER_NAME , #{expertName})>0
			]]>
		</if>
		ORDER BY TXI.ID_NO
	</select>
	<!-- 待注销 -->
 	<select id="queryCancellationTDate" resultMap="ReportEntity" parameterType="ReportEntity">
		SELECT 
		 TXR.EXPERT_ID,TXI.USER_NAME,TXI.MOBILEPHONE,TXI.ID_NO,TP.PROJECT_NAME,TP.MANAGER,TP.BID_TIME,TXR.REASON AS outReason
		FROM T_PROJECT TP 
	    LEFT JOIN t_condition TC ON TC.PROJECT_ID = TP.PROJECT_ID        
	    LEFT JOIN t_extract_result  TXR ON TXR.CONDITION_ID=TC.ID                      
        LEFT JOIN T_EXPERT_INFO TXI ON  TXI.USER_ID = TXR.EXPERT_ID
       WHERE 
       	TXR.REASON IN 
	       	<foreach collection="reasonLst" index="index" item="item" open="(" separator="," close=")" >
	       		'${item}'
	   		</foreach>
     	 <![CDATA[
				AND  TO_CHAR(TP.BID_TIME,'yyyy-MM-dd') BETWEEN #{startTime} AND #{endTime}
			]]>
       <if test="expertName != null and expertName != ''">
			 <![CDATA[ 
				AND instr(TXI.USER_NAME , #{expertName})>0
			]]>
		</if>
		ORDER BY TXI.ID_NO
	</select>
	
	<!-- 注销 -->
 	<select id="queryCancellationDate" resultMap="ReportEntity" parameterType="ReportEntity">
	SELECT t.user_name,T.MOBILEPHONE,T.ID_NO,T.OUT_TIME,T.OUT_REASON 
	FROM T_EXPERT_INFO T
	WHERE T.OUT_REASON  IS NOT NULL
	 AND  to_char(T.OUT_TIME,'yyyy')=#{startTime}
	 <if test="expertName != null and expertName != ''">
		 <![CDATA[ 
			AND instr(T.USER_NAME , #{expertName})>0
		]]>
	</if>
	ORDER BY T.ID_NO
	</select>
	<!--更新专家表-->
	<update id="updateScoreStatus" parameterType="ReportEntity">
		update t_extract_result set IS_APPROVE=#{isApprove} where ID = #{extractResultId}
	</update>
	<!--更新评价表-->
	<update id="updateAppraiseStatus" parameterType="ReportEntity">
		update T_APPRAISE set REVIEW_USER = #{reviewUser} , REVIEW_TIME = #{reviewTime},REVIEW_STATUS=#{isApprove}
		where extract_result_id = #{extractResultId} and REVIEW_STATUS is null
	</update>

	<select id="queryScoreInfo" parameterType="ReportEntity" resultType="ReportEntity">
		SELECT  t.score as score,concat('扣分原因:',TAI.APPRAISE_COLUMN) as appraiseColumn
		FROM T_PROJECT TP
		LEFT JOIN t_condition TC ON TC.PROJECT_ID = TP.PROJECT_ID
		LEFT JOIN t_extract_result  TXR      ON TXR.CONDITION_ID=TC.ID
		LEFT JOIN t_appraise T  ON  TXR.ID=T.EXTRACT_RESULT_ID
		LEFT JOIN T_EXPERT_INFO TXI ON  TXI.USER_ID = TXR.EXPERT_ID
		LEFT JOIN T_APPRAISE_INFO TAI ON TAI.APPRAISE_INFO_ID = T.APPRAISE_INFO_ID
		WHERE  <![CDATA[
			T.SCORE<>0
		]]>
		<if test="extractResultId != null and extractResultId != ''">
				AND T.EXTRACT_RESULT_ID = #{extractResultId}
		</if>
		<if test="appraiseTimeStr != null and appraiseTimeStr != ''">
			and to_char(t.APPRAISE_TIME, 'YYYY-MM-DD HH24:MI:SS') = #{appraiseTimeStr}
		</if>
		<if test="reviewStatus != null and reviewStatus != ''">
			AND T.REVIEW_STATUS = #{reviewStatus}
		</if>
		<if test="reviewStatus == null or reviewStatus == ''">
			AND T.REVIEW_STATUS is null
		</if>
	</select>
	<select id="querySumScore" parameterType="ReportEntity" resultType="ReportEntity">
		SELECT sum(t.score) as score,TXI.EVAL_SCORE as evalScore,TXR.Decimationbatch as decimationBatch
		FROM T_PROJECT TP
		LEFT JOIN t_condition TC ON TC.PROJECT_ID = TP.PROJECT_ID
		LEFT JOIN t_extract_result  TXR      ON TXR.CONDITION_ID=TC.ID
		LEFT JOIN t_appraise T  ON  TXR.ID=T.EXTRACT_RESULT_ID
		LEFT JOIN T_EXPERT_INFO TXI ON  TXI.USER_ID = TXR.EXPERT_ID
		LEFT JOIN T_APPRAISE_INFO TAI ON TAI.APPRAISE_INFO_ID = T.APPRAISE_INFO_ID
		WHERE  <![CDATA[
			T.SCORE<>0
		]]>
		<if test="extractResultId != null and extractResultId != ''">
			AND T.EXTRACT_RESULT_ID = #{extractResultId}
		</if>
		group by TXI.EVAL_SCORE,TXR.Decimationbatch
	</select>
	<update id="updateNewScore" parameterType="ReportEntity">
		update t_expert_info set EVAL_SCORE=#{score} where USER_ID = #{expertId}
	</update>
	<update id="updateAppraiseInfo" parameterType="ReportEntity">
		update T_EXTRACT_RESULT set IS_APPRAISE = '0' where  EXPERT_ID = #{expertId} and Decimationbatch = #{decimationBatch}
	</update>
	<select id="queryExpertId" parameterType="ReportEntity" resultType="String">
		select EXPERT_ID as expertId from T_EXTRACT_RESULT where ID=#{extractResultId}
	</select>
	<select id="updateRejectionReason" parameterType="ReportEntity">
		update T_EXTRACT_RESULT set REJECTION_REASON = #{rejectionReason} where ID=#{extractResultId}
	</select>
	<select id="queryThisSumScore" parameterType="String" resultType="String">
		select sum(SCORE) from T_APPRAISE where extract_result_id = #{extractResultId} and to_char(review_time,'yyyy-mm-dd hh24:mi:ss')=#{reviewTimeStr}
	</select>
	<update id="updateExpertBidStatus" parameterType="ReportEntity">
		update T_EXPERT_INFO
		<trim prefix="SET" suffixOverrides=",">
		<if test ="isBid!=null">
			IS_BID=#{isBid},
		</if>
		<if test ="startBidDate!=null">
			START_BID_DATE=#{startBidDate},
		</if>
		<if test ="endBidDate!=null">
			END_BID_DATE=#{endBidDate},
		</if>
		</trim>
		where USER_ID = #{expertId}
	</update>
	<select id="queryExpertSumScore" parameterType="ReportEntity" resultType="String">
		SELECT distinct
		sum(t.score) over(partition by TXI.USER_ID) sumScore
		FROM T_PROJECT TP
		LEFT JOIN t_condition TC
		ON TC.PROJECT_ID = TP.PROJECT_ID
		LEFT JOIN t_extract_result TXR
		ON TXR.CONDITION_ID = TC.ID
		LEFT JOIN t_appraise T
		ON TXR.ID = T.EXTRACT_RESULT_ID
		LEFT JOIN T_EXPERT_INFO TXI
		ON TXI.USER_ID = TXR.EXPERT_ID
		LEFT JOIN T_APPRAISE_INFO TAI
		ON TAI.APPRAISE_INFO_ID = T.APPRAISE_INFO_ID
		WHERE <![CDATA[ T.SCORE <> 0 ]]>
		and TXR.IS_APPROVE = '1'
		and t.review_status = '1'
		<if test ="expertId!=null and expertId!='' ">
			AND TXI.USER_ID =#{expertId}
		</if>
	</select>
	<update id="updateExpertStatus" parameterType="String">
		update T_EXPERT_INFO set STATUS='11',MODIFY_TIME=sysdate where USER_ID=#{expertId}
	</update>
</mapper>
