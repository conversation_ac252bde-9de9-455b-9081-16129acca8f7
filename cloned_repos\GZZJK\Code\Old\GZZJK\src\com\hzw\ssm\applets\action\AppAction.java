package com.hzw.ssm.applets.action;

import com.hzw.ssm.applets.service.AppService;
import com.hzw.ssm.applets.service.DictionaryService;
import com.hzw.ssm.applets.utils.GetJsonReqData;
import com.hzw.ssm.applets.utils.GetJsonRespData;
import com.hzw.ssm.applets.utils.ResultJson;
import com.hzw.ssm.fw.base.BaseAction;
import net.sf.json.JSONObject;
import org.apache.struts2.convention.annotation.Action;
import org.apache.struts2.convention.annotation.Namespace;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 小程序
 */
@Namespace("/appAction")
public class AppAction extends BaseAction {

    @Autowired
    private AppService appService;
    @Autowired
    private DictionaryService dictionaryService;

    private static final long serialVersionUID = -8823858495799399882L;

    /**
     * 登录
     *
     * @return
     */
    @Action("wxSmallLogin")
    public String wxSmallLogin() {

        JSONObject jsonReqData = GetJsonReqData.getJsonReqData();
        ResultJson resultJson = appService.wxSmallLogin(jsonReqData);
        GetJsonRespData.getJsonRespData(resultJson);
        return null;
    }

    /**
     * 绑定
     *
     * @return
     */
    @Action("binding")
    public String binding() {

        JSONObject jsonReqData = GetJsonReqData.getJsonReqData();
        ResultJson resultJson = appService.binding(jsonReqData);
        GetJsonRespData.getJsonRespData(resultJson);
        return null;
    }

    /**
     * 注册
     *
     * @return
     */
    @Action("register")
    public String register() {

        JSONObject jsonReqData = GetJsonReqData.getJsonReqData();
        ResultJson resultJson = appService.register(jsonReqData);
        GetJsonRespData.getJsonRespData(resultJson);
        return null;
    }

    /**
     * 发送短信
     *
     * @return
     */
    @Action("/wxSendSmsCode")
    public String sendSmsCode() {
        JSONObject jsonReqData = GetJsonReqData.getJsonReqData();
        ResultJson resultJson = appService.sendSmsCode(jsonReqData);
        GetJsonRespData.getJsonRespData(resultJson);
        return null;
    }

    /**
     * 职称
     *
     * @return
     */
    @Action("title")
    public String title() {
        ResultJson resultJson = dictionaryService.queryTitle();
        GetJsonRespData.getJsonRespDataNoNull(resultJson);
        return null;
    }
}
