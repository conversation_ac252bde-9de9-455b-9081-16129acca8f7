package com.hzw.sunflower.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.constant.constantenum.ChangeRecordTypeEnum;
import com.hzw.sunflower.constant.constantenum.RelationStatusEnum;
import com.hzw.sunflower.controller.request.BondRateReq;
import com.hzw.sunflower.controller.request.BondSectionReq;
import com.hzw.sunflower.dao.BondRateMapper;
import com.hzw.sunflower.dao.BondSplitMapper;
import com.hzw.sunflower.entity.BondChangeRecords;
import com.hzw.sunflower.entity.BondRate;
import com.hzw.sunflower.entity.BondSplit;
import com.hzw.sunflower.entity.BondWater;
import com.hzw.sunflower.service.BondChangeRecordsService;
import com.hzw.sunflower.service.BondRateService;
import com.hzw.sunflower.service.BondSplitService;
import com.hzw.sunflower.service.BondWaterService;
import com.hzw.sunflower.util.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 保证金拆分 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-15
 */
@Service
public class BondRateServiceImpl extends ServiceImpl<BondRateMapper, BondRate> implements BondRateService {

    @Override
    public BondRate getRateByType(BondRateReq req) {
        LambdaQueryWrapper<BondRate> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BondRate::getType,req.getType()).orderByDesc(BondRate::getId)
                .last("limit 1");
        BondRate bondRate = this.baseMapper.selectOne(queryWrapper);
        return bondRate;
    }

    @Override
    public Result<String> saveBankRate(BondRateReq req) {
        for(int i=0;i<req.getBondRateBefore().size();i++){
            //查询银行日期是否已经有利率，有则更新没有则新增
            LambdaQueryWrapper<BondRate> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(BondRate::getType,req.getType());
            queryWrapper.eq(BondRate::getBankType,req.getBondRateBefore().get(i).getBankType());
            queryWrapper.eq(BondRate::getEffectDate,DateUtils.parseDate(req.getEffectDate()));
            if(this.count(queryWrapper) > 0){
                LambdaUpdateWrapper<BondRate> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.eq(BondRate::getBankType,req.getBondRateBefore().get(i).getBankType());
                updateWrapper.eq(BondRate::getEffectDate,DateUtils.parseDate(req.getEffectDate()));
                updateWrapper.set(BondRate::getRate,req.getRate());
                this.update(updateWrapper);
            }else{
                BondRate rate = new BondRate();
                rate.setRate(req.getRate());
                rate.setBeforeRate(req.getBondRateBefore().get(i).getBeforeRate());
                rate.setBeforeDate(DateUtils.parseDate(req.getBondRateBefore().get(i).getBeforeDate()));
                rate.setEffectDate(DateUtils.parseDate(req.getEffectDate()));
                rate.setBankType(req.getBondRateBefore().get(i).getBankType());
                rate.setType(req.getType());
                this.save(rate);
            }
        }
        return Result.ok();
    }
}
