[{"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2025-06-23T16:39:52+08:00", "creator_id": 880849, "default_branch": "master", "http_url_to_repo": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-common-service.git", "id": 5360063, "issues_enabled": true, "last_activity_at": "2025-08-25T10:28:42+08:00", "merge_requests_enabled": true, "name": "bid-common-service", "name_with_namespace": "hzw / JSTCCUP / bid-common-service", "namespace": {"created_at": "2022-08-02T16:29:33+08:00", "description": "JSTCC", "id": 833002, "name": "JSTCCUP", "owner_id": 880849, "path": "JSTCCUP", "public": false, "updated_at": "2022-08-02T16:29:33+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/11261002b8a7613983e6e3be3e41501742ba/w/100/h/100", "id": 880849, "name": "dingtalk_fefkma", "state": "active", "username": "aliyun:dingt<PERSON>_fefkma_9qp", "web_url": ""}, "path": "bid-common-service", "path_with_namespace": "hzw/JSTCCUP/bid-common-service", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/JSTCCUP/bid-common-service.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-common-service", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2025-06-23T16:37:35+08:00", "creator_id": 880849, "default_branch": "master", "http_url_to_repo": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-file-compare.git", "id": 5360052, "issues_enabled": true, "last_activity_at": "2025-08-21T09:41:10+08:00", "merge_requests_enabled": true, "name": "bid-file-compare", "name_with_namespace": "hzw / JSTCCUP / bid-file-compare", "namespace": {"created_at": "2022-08-02T16:29:33+08:00", "description": "JSTCC", "id": 833002, "name": "JSTCCUP", "owner_id": 880849, "path": "JSTCCUP", "public": false, "updated_at": "2022-08-02T16:29:33+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/11261002b8a7613983e6e3be3e41501742ba/w/100/h/100", "id": 880849, "name": "dingtalk_fefkma", "state": "active", "username": "aliyun:dingt<PERSON>_fefkma_9qp", "web_url": ""}, "path": "bid-file-compare", "path_with_namespace": "hzw/JSTCCUP/bid-file-compare", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/JSTCCUP/bid-file-compare.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-file-compare", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2025-06-09T16:23:58+08:00", "creator_id": 880849, "default_branch": "master", "http_url_to_repo": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-file-tasks.git", "id": 5318304, "issues_enabled": true, "last_activity_at": "2025-06-09T16:24:21+08:00", "merge_requests_enabled": true, "name": "bid-file-tasks", "name_with_namespace": "hzw / JSTCCUP / bid-file-tasks", "namespace": {"created_at": "2022-08-02T16:29:33+08:00", "description": "JSTCC", "id": 833002, "name": "JSTCCUP", "owner_id": 880849, "path": "JSTCCUP", "public": false, "updated_at": "2022-08-02T16:29:33+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/11261002b8a7613983e6e3be3e41501742ba/w/100/h/100", "id": 880849, "name": "dingtalk_fefkma", "state": "active", "username": "aliyun:dingt<PERSON>_fefkma_9qp", "web_url": ""}, "path": "bid-file-tasks", "path_with_namespace": "hzw/JSTCCUP/bid-file-tasks", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/JSTCCUP/bid-file-tasks.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-file-tasks", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2025-06-04T17:24:09+08:00", "creator_id": 881075, "default_branch": "master", "description": "定时获取前一天公告，根据关键字推荐公告", "http_url_to_repo": "https://codeup.aliyun.com/hzw/JSFGW/bussiness-notice.git", "id": 5303303, "issues_enabled": true, "last_activity_at": "2025-06-06T09:08:15+08:00", "merge_requests_enabled": true, "name": "bussiness-notice", "name_with_namespace": "hzw / JSFGW / bussiness-notice", "namespace": {"created_at": "2022-08-15T14:07:51+08:00", "description": "江苏省招标投标公共服务平台", "id": 861313, "name": "JSFGW", "owner_id": 881075, "path": "JSFGW", "public": false, "updated_at": "2022-08-15T14:07:51+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/112k6ff167e76a698c621bb236aec0bddbce/w/200/h/200", "id": 881075, "name": "qzw", "state": "active", "username": "aliyun:dingtalk_vzxhqg_Xbiwy", "web_url": ""}, "path": "bussiness-notice", "path_with_namespace": "hzw/JSFGW/bussiness-notice", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/JSFGW/bussiness-notice.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/JSFGW/bussiness-notice", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2025-04-27T15:09:37+08:00", "creator_id": 880849, "default_branch": "master", "http_url_to_repo": "https://codeup.aliyun.com/hzw/JSTCCUP/preview-wpsqyd-client.git", "id": 5203174, "issues_enabled": true, "last_activity_at": "2025-05-09T15:29:35+08:00", "merge_requests_enabled": true, "name": "preview-wpsqyd-client", "name_with_namespace": "hzw / JSTCCUP / preview-wpsqyd-client", "namespace": {"created_at": "2022-08-02T16:29:33+08:00", "description": "JSTCC", "id": 833002, "name": "JSTCCUP", "owner_id": 880849, "path": "JSTCCUP", "public": false, "updated_at": "2022-08-02T16:29:33+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/11261002b8a7613983e6e3be3e41501742ba/w/100/h/100", "id": 880849, "name": "dingtalk_fefkma", "state": "active", "username": "aliyun:dingt<PERSON>_fefkma_9qp", "web_url": ""}, "path": "preview-wpsqyd-client", "path_with_namespace": "hzw/JSTCCUP/preview-wpsqyd-client", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/JSTCCUP/preview-wpsqyd-client.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/JSTCCUP/preview-wpsqyd-client", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2025-04-27T14:31:07+08:00", "creator_id": 880849, "default_branch": "develop", "http_url_to_repo": "https://codeup.aliyun.com/hzw/JSTCCUP/preview-wpsqyd-api.git", "id": 5202963, "issues_enabled": true, "last_activity_at": "2025-05-26T14:52:37+08:00", "merge_requests_enabled": true, "name": "preview-wpsqyd-api", "name_with_namespace": "hzw / JSTCCUP / preview-wpsqyd-api", "namespace": {"created_at": "2022-08-02T16:29:33+08:00", "description": "JSTCC", "id": 833002, "name": "JSTCCUP", "owner_id": 880849, "path": "JSTCCUP", "public": false, "updated_at": "2022-08-02T16:29:33+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/11261002b8a7613983e6e3be3e41501742ba/w/100/h/100", "id": 880849, "name": "dingtalk_fefkma", "state": "active", "username": "aliyun:dingt<PERSON>_fefkma_9qp", "web_url": ""}, "path": "preview-wpsqyd-api", "path_with_namespace": "hzw/JSTCCUP/preview-wpsqyd-api", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/JSTCCUP/preview-wpsqyd-api.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/JSTCCUP/preview-wpsqyd-api", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2025-04-08T15:28:25+08:00", "creator_id": 881000, "default_branch": "master", "http_url_to_repo": "https://codeup.aliyun.com/hzw/JSTCCUP/preview-wps.git", "id": 5140721, "issues_enabled": true, "last_activity_at": "2025-04-09T09:42:06+08:00", "merge_requests_enabled": true, "name": "preview-wps", "name_with_namespace": "hzw / JSTCCUP / preview-wps", "namespace": {"created_at": "2022-08-02T16:29:33+08:00", "description": "JSTCC", "id": 833002, "name": "JSTCCUP", "owner_id": 880849, "path": "JSTCCUP", "public": false, "updated_at": "2022-08-02T16:29:33+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/112k481bf1240c2eaa600b6f9532526342b8/w/200/h/200", "id": 881000, "name": "李金鑫", "state": "active", "username": "aliyun:dingtalk_mjynvn_fO6sW", "web_url": ""}, "path": "preview-wps", "path_with_namespace": "hzw/JSTCCUP/preview-wps", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/JSTCCUP/preview-wps.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/JSTCCUP/preview-wps", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2024-12-20T16:47:27+08:00", "creator_id": 880849, "default_branch": "master", "http_url_to_repo": "https://codeup.aliyun.com/hzw/PORTAL_NEW/platform-index-ui.git", "id": 4872254, "issues_enabled": true, "last_activity_at": "2025-01-10T17:04:46+08:00", "merge_requests_enabled": true, "name": "platform-index-ui", "name_with_namespace": "hzw / PORTAL_NEW / platform-index-ui", "namespace": {"created_at": "2024-11-25T14:36:53+08:00", "description": "", "id": 1472522, "name": "PORTAL_NEW", "owner_id": 880849, "path": "PORTAL_NEW", "public": false, "updated_at": "2024-11-25T14:36:53+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/11261002b8a7613983e6e3be3e41501742ba/w/100/h/100", "id": 880849, "name": "dingtalk_fefkma", "state": "active", "username": "aliyun:dingt<PERSON>_fefkma_9qp", "web_url": ""}, "path": "platform-index-ui", "path_with_namespace": "hzw/PORTAL_NEW/platform-index-ui", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/PORTAL_NEW/platform-index-ui.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/PORTAL_NEW/platform-index-ui", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2024-11-25T15:33:19+08:00", "creator_id": 881000, "default_branch": "realse", "http_url_to_repo": "https://codeup.aliyun.com/hzw/PORTAL_NEW/platform-cloud.git", "id": 4814723, "issues_enabled": true, "last_activity_at": "2025-04-30T11:19:20+08:00", "merge_requests_enabled": true, "name": "platform-cloud", "name_with_namespace": "hzw / PORTAL_NEW / platform-cloud", "namespace": {"created_at": "2024-11-25T14:36:53+08:00", "description": "", "id": 1472522, "name": "PORTAL_NEW", "owner_id": 880849, "path": "PORTAL_NEW", "public": false, "updated_at": "2024-11-25T14:36:53+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/112k481bf1240c2eaa600b6f9532526342b8/w/200/h/200", "id": 881000, "name": "李金鑫", "state": "active", "username": "aliyun:dingtalk_mjynvn_fO6sW", "web_url": ""}, "path": "platform-cloud", "path_with_namespace": "hzw/PORTAL_NEW/platform-cloud", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/PORTAL_NEW/platform-cloud.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/PORTAL_NEW/platform-cloud", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2024-11-25T15:31:06+08:00", "creator_id": 881000, "default_branch": "rease1.0", "http_url_to_repo": "https://codeup.aliyun.com/hzw/PORTAL_NEW/platform-ui.git", "id": 4814712, "issues_enabled": true, "last_activity_at": "2025-04-14T15:46:04+08:00", "merge_requests_enabled": true, "name": "platform-ui", "name_with_namespace": "hzw / PORTAL_NEW / platform-ui", "namespace": {"created_at": "2024-11-25T14:36:53+08:00", "description": "", "id": 1472522, "name": "PORTAL_NEW", "owner_id": 880849, "path": "PORTAL_NEW", "public": false, "updated_at": "2024-11-25T14:36:53+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/112k481bf1240c2eaa600b6f9532526342b8/w/200/h/200", "id": 881000, "name": "李金鑫", "state": "active", "username": "aliyun:dingtalk_mjynvn_fO6sW", "web_url": ""}, "path": "platform-ui", "path_with_namespace": "hzw/PORTAL_NEW/platform-ui", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/PORTAL_NEW/platform-ui.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/PORTAL_NEW/platform-ui", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2024-11-11T14:17:02+08:00", "creator_id": 1136003, "default_branch": "master", "http_url_to_repo": "https://codeup.aliyun.com/hzw/Heterogeneous/heterogeneous-api.git", "id": 4781568, "issues_enabled": true, "last_activity_at": "2025-07-08T09:02:27+08:00", "merge_requests_enabled": true, "name": "heterogeneous-api", "name_with_namespace": "hzw / Heterogeneous / heterogeneous-api", "namespace": {"created_at": "2024-11-11T13:54:57+08:00", "description": "异构项目", "id": 1465195, "name": "Heterogeneous", "owner_id": 890194, "path": "Heterogeneous", "public": false, "updated_at": "2024-11-11T13:54:57+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/112vbccf724849c3465f69f981380012cc22/w/100/h/100", "id": 1136003, "name": "dt_5370640490", "state": "active", "username": "aliyun:dt_5370640490_FGYMq", "web_url": ""}, "path": "heterogeneous-api", "path_with_namespace": "hzw/Heterogeneous/heterogeneous-api", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/Heterogeneous/heterogeneous-api.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/Heterogeneous/heterogeneous-api", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2024-11-11T13:57:34+08:00", "creator_id": 890194, "default_branch": "master", "description": "异构项目前端页面", "http_url_to_repo": "https://codeup.aliyun.com/hzw/Heterogeneous/heterogeneous-ui.git", "id": 4781420, "issues_enabled": true, "last_activity_at": "2025-07-08T09:02:43+08:00", "merge_requests_enabled": true, "name": "heterogeneous-ui", "name_with_namespace": "hzw / Heterogeneous / heterogeneous-ui", "namespace": {"created_at": "2024-11-11T13:54:57+08:00", "description": "异构项目", "id": 1465195, "name": "Heterogeneous", "owner_id": 890194, "path": "Heterogeneous", "public": false, "updated_at": "2024-11-11T13:54:57+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/1121865dc820790bc86863ab99ab54c81643/w/100/h/100", "id": 890194, "name": "尹辉杨·", "state": "active", "username": "aliyun:尹辉杨·_6Eg", "web_url": ""}, "path": "heterogeneous-ui", "path_with_namespace": "hzw/Heterogeneous/heterogeneous-ui", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/Heterogeneous/heterogeneous-ui.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/Heterogeneous/heterogeneous-ui", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2024-11-06T15:19:36+08:00", "creator_id": 880849, "default_branch": "master", "description": "网易云信回调", "http_url_to_repo": "https://codeup.aliyun.com/hzw/JSTCCUP/yunxin-callback-api.git", "id": 4771345, "issues_enabled": true, "last_activity_at": "2025-06-24T17:47:58+08:00", "merge_requests_enabled": true, "name": "yunxin-callback-api", "name_with_namespace": "hzw / JSTCCUP / yunxin-callback-api", "namespace": {"created_at": "2022-08-02T16:29:33+08:00", "description": "JSTCC", "id": 833002, "name": "JSTCCUP", "owner_id": 880849, "path": "JSTCCUP", "public": false, "updated_at": "2022-08-02T16:29:33+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/11261002b8a7613983e6e3be3e41501742ba/w/100/h/100", "id": 880849, "name": "dingtalk_fefkma", "state": "active", "username": "aliyun:dingt<PERSON>_fefkma_9qp", "web_url": ""}, "path": "yunxin-callback-api", "path_with_namespace": "hzw/JSTCCUP/yunxin-callback-api", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/JSTCCUP/yunxin-callback-api.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/JSTCCUP/yunxin-callback-api", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2024-08-20T08:47:41+08:00", "creator_id": 880849, "default_branch": "master", "http_url_to_repo": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-invoice-client.git", "id": 4589868, "issues_enabled": true, "last_activity_at": "2025-06-26T14:09:13+08:00", "merge_requests_enabled": true, "name": "bid-invoice-client", "name_with_namespace": "hzw / JSTCCUP / bid-invoice-client", "namespace": {"created_at": "2022-08-02T16:29:33+08:00", "description": "JSTCC", "id": 833002, "name": "JSTCCUP", "owner_id": 880849, "path": "JSTCCUP", "public": false, "updated_at": "2022-08-02T16:29:33+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/11261002b8a7613983e6e3be3e41501742ba/w/100/h/100", "id": 880849, "name": "dingtalk_fefkma", "state": "active", "username": "aliyun:dingt<PERSON>_fefkma_9qp", "web_url": ""}, "path": "bid-invoice-client", "path_with_namespace": "hzw/JSTCCUP/bid-invoice-client", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/JSTCCUP/bid-invoice-client.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-invoice-client", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2024-08-16T14:08:24+08:00", "creator_id": 880849, "default_branch": "master", "description": "投标文件加密工具", "http_url_to_repo": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-encryption-tool.git", "id": 4582632, "issues_enabled": true, "last_activity_at": "2024-10-11T15:16:05+08:00", "merge_requests_enabled": true, "name": "bid-encryption-tool", "name_with_namespace": "hzw / JSTCCUP / bid-encryption-tool", "namespace": {"created_at": "2022-08-02T16:29:33+08:00", "description": "JSTCC", "id": 833002, "name": "JSTCCUP", "owner_id": 880849, "path": "JSTCCUP", "public": false, "updated_at": "2022-08-02T16:29:33+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/11261002b8a7613983e6e3be3e41501742ba/w/100/h/100", "id": 880849, "name": "dingtalk_fefkma", "state": "active", "username": "aliyun:dingt<PERSON>_fefkma_9qp", "web_url": ""}, "path": "bid-encryption-tool", "path_with_namespace": "hzw/JSTCCUP/bid-encryption-tool", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/JSTCCUP/bid-encryption-tool.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-encryption-tool", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2024-08-15T09:27:40+08:00", "creator_id": 880849, "default_branch": "master", "description": "线上问题处理sql等维护", "http_url_to_repo": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-doc-maintenance.git", "id": 4578474, "issues_enabled": true, "last_activity_at": "2024-12-06T09:46:28+08:00", "merge_requests_enabled": true, "name": "bid-doc-maintenance", "name_with_namespace": "hzw / JSTCCUP / bid-doc-maintenance", "namespace": {"created_at": "2022-08-02T16:29:33+08:00", "description": "JSTCC", "id": 833002, "name": "JSTCCUP", "owner_id": 880849, "path": "JSTCCUP", "public": false, "updated_at": "2022-08-02T16:29:33+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/11261002b8a7613983e6e3be3e41501742ba/w/100/h/100", "id": 880849, "name": "dingtalk_fefkma", "state": "active", "username": "aliyun:dingt<PERSON>_fefkma_9qp", "web_url": ""}, "path": "bid-doc-maintenance", "path_with_namespace": "hzw/JSTCCUP/bid-doc-maintenance", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/JSTCCUP/bid-doc-maintenance.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-doc-maintenance", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2024-08-14T09:11:40+08:00", "creator_id": 880849, "default_branch": "master", "description": "代理服务费发票，支持分公司", "http_url_to_repo": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-invoice-api.git", "id": 4574967, "issues_enabled": true, "last_activity_at": "2025-07-08T08:51:37+08:00", "merge_requests_enabled": true, "name": "bid-invoice-api", "name_with_namespace": "hzw / JSTCCUP / bid-invoice-api", "namespace": {"created_at": "2022-08-02T16:29:33+08:00", "description": "JSTCC", "id": 833002, "name": "JSTCCUP", "owner_id": 880849, "path": "JSTCCUP", "public": false, "updated_at": "2022-08-02T16:29:33+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/11261002b8a7613983e6e3be3e41501742ba/w/100/h/100", "id": 880849, "name": "dingtalk_fefkma", "state": "active", "username": "aliyun:dingt<PERSON>_fefkma_9qp", "web_url": ""}, "path": "bid-invoice-api", "path_with_namespace": "hzw/JSTCCUP/bid-invoice-api", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/JSTCCUP/bid-invoice-api.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-invoice-api", "wiki_enabled": true}, {"archived": false, "avatar_url": "", "builds_enabled": true, "checkemail": false, "created_at": "2024-08-09T10:42:20+08:00", "creator_id": 1415506, "default_branch": "master", "http_url_to_repo": "https://codeup.aliyun.com/hzw/Test/HZW-TA.git", "id": 4563739, "issues_enabled": true, "last_activity_at": "2025-08-14T09:49:05+08:00", "merge_requests_enabled": true, "name": "HZW-TA", "name_with_namespace": "hzw / Test / HZW-TA", "namespace": {"created_at": "2024-08-09T10:42:08+08:00", "description": "", "id": 1417313, "name": "Test", "owner_id": 1415506, "path": "Test", "public": false, "updated_at": "2024-08-09T13:33:27+08:00", "visibility_level": "10"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/1138e8134fb92a5745644de5168378242e3c/w/100/h/100", "id": 1415506, "name": "<EMAIL>", "state": "active", "username": "aliyun:whetxu@163.com_sWgnj", "web_url": ""}, "path": "HZW-TA", "path_with_namespace": "hzw/Test/HZW-TA", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/Test/HZW-TA.git", "tag_list": [], "visibility_level": "10", "web_url": "https://codeup.aliyun.com/hzw/Test/HZW-TA", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2024-08-06T10:26:16+08:00", "creator_id": 880849, "default_branch": "master", "description": "保证金sdk", "http_url_to_repo": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-fee-deposit-client.git", "id": 4553680, "issues_enabled": true, "last_activity_at": "2024-10-28T15:44:44+08:00", "merge_requests_enabled": true, "name": "bid-fee-deposit-client", "name_with_namespace": "hzw / JSTCCUP / bid-fee-deposit-client", "namespace": {"created_at": "2022-08-02T16:29:33+08:00", "description": "JSTCC", "id": 833002, "name": "JSTCCUP", "owner_id": 880849, "path": "JSTCCUP", "public": false, "updated_at": "2022-08-02T16:29:33+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/11261002b8a7613983e6e3be3e41501742ba/w/100/h/100", "id": 880849, "name": "dingtalk_fefkma", "state": "active", "username": "aliyun:dingt<PERSON>_fefkma_9qp", "web_url": ""}, "path": "bid-fee-deposit-client", "path_with_namespace": "hzw/JSTCCUP/bid-fee-deposit-client", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/JSTCCUP/bid-fee-deposit-client.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-fee-deposit-client", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2024-08-06T10:25:21+08:00", "creator_id": 880849, "default_branch": "master", "description": "劳务费sdk", "http_url_to_repo": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-fee-expert-client.git", "id": 4553675, "issues_enabled": true, "last_activity_at": "2025-04-24T17:55:48+08:00", "merge_requests_enabled": true, "name": "bid-fee-expert-client", "name_with_namespace": "hzw / JSTCCUP / bid-fee-expert-client", "namespace": {"created_at": "2022-08-02T16:29:33+08:00", "description": "JSTCC", "id": 833002, "name": "JSTCCUP", "owner_id": 880849, "path": "JSTCCUP", "public": false, "updated_at": "2022-08-02T16:29:33+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/11261002b8a7613983e6e3be3e41501742ba/w/100/h/100", "id": 880849, "name": "dingtalk_fefkma", "state": "active", "username": "aliyun:dingt<PERSON>_fefkma_9qp", "web_url": ""}, "path": "bid-fee-expert-client", "path_with_namespace": "hzw/JSTCCUP/bid-fee-expert-client", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/JSTCCUP/bid-fee-expert-client.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-fee-expert-client", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2024-08-05T08:51:24+08:00", "creator_id": 880849, "default_branch": "master", "description": "保证金", "http_url_to_repo": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-fee-deposit.git", "id": 4550515, "issues_enabled": true, "last_activity_at": "2025-07-09T10:01:34+08:00", "merge_requests_enabled": true, "name": "bid-fee-deposit", "name_with_namespace": "hzw / JSTCCUP / bid-fee-deposit", "namespace": {"created_at": "2022-08-02T16:29:33+08:00", "description": "JSTCC", "id": 833002, "name": "JSTCCUP", "owner_id": 880849, "path": "JSTCCUP", "public": false, "updated_at": "2022-08-02T16:29:33+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/11261002b8a7613983e6e3be3e41501742ba/w/100/h/100", "id": 880849, "name": "dingtalk_fefkma", "state": "active", "username": "aliyun:dingt<PERSON>_fefkma_9qp", "web_url": ""}, "path": "bid-fee-deposit", "path_with_namespace": "hzw/JSTCCUP/bid-fee-deposit", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/JSTCCUP/bid-fee-deposit.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-fee-deposit", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2024-07-30T14:47:03+08:00", "creator_id": 880849, "default_branch": "master", "description": "劳务费", "http_url_to_repo": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-fee-expert.git", "id": 4538833, "issues_enabled": true, "last_activity_at": "2025-08-22T16:25:12+08:00", "merge_requests_enabled": true, "name": "bid-fee-expert", "name_with_namespace": "hzw / JSTCCUP / bid-fee-expert", "namespace": {"created_at": "2022-08-02T16:29:33+08:00", "description": "JSTCC", "id": 833002, "name": "JSTCCUP", "owner_id": 880849, "path": "JSTCCUP", "public": false, "updated_at": "2022-08-02T16:29:33+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/11261002b8a7613983e6e3be3e41501742ba/w/100/h/100", "id": 880849, "name": "dingtalk_fefkma", "state": "active", "username": "aliyun:dingt<PERSON>_fefkma_9qp", "web_url": ""}, "path": "bid-fee-expert", "path_with_namespace": "hzw/JSTCCUP/bid-fee-expert", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/JSTCCUP/bid-fee-expert.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-fee-expert", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2024-07-02T14:52:46+08:00", "creator_id": 880849, "default_branch": "master", "description": "招标文件编制", "http_url_to_repo": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-doc-tool-api.git", "id": 4460784, "issues_enabled": true, "last_activity_at": "2024-11-06T15:12:48+08:00", "merge_requests_enabled": true, "name": "bid-doc-tool-api", "name_with_namespace": "hzw / JSTCCUP / bid-doc-tool-api", "namespace": {"created_at": "2022-08-02T16:29:33+08:00", "description": "JSTCC", "id": 833002, "name": "JSTCCUP", "owner_id": 880849, "path": "JSTCCUP", "public": false, "updated_at": "2022-08-02T16:29:33+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/11261002b8a7613983e6e3be3e41501742ba/w/100/h/100", "id": 880849, "name": "dingtalk_fefkma", "state": "active", "username": "aliyun:dingt<PERSON>_fefkma_9qp", "web_url": ""}, "path": "bid-doc-tool-api", "path_with_namespace": "hzw/JSTCCUP/bid-doc-tool-api", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/JSTCCUP/bid-doc-tool-api.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-doc-tool-api", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2024-06-25T16:15:21+08:00", "creator_id": 880849, "default_branch": "master", "description": "投标文件制作工具", "http_url_to_repo": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-document-tool.git", "id": 4438925, "issues_enabled": true, "last_activity_at": "2024-10-11T15:18:24+08:00", "merge_requests_enabled": true, "name": "bid-document-tool", "name_with_namespace": "hzw / JSTCCUP / bid-document-tool", "namespace": {"created_at": "2022-08-02T16:29:33+08:00", "description": "JSTCC", "id": 833002, "name": "JSTCCUP", "owner_id": 880849, "path": "JSTCCUP", "public": false, "updated_at": "2022-08-02T16:29:33+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/11261002b8a7613983e6e3be3e41501742ba/w/100/h/100", "id": 880849, "name": "dingtalk_fefkma", "state": "active", "username": "aliyun:dingt<PERSON>_fefkma_9qp", "web_url": ""}, "path": "bid-document-tool", "path_with_namespace": "hzw/JSTCCUP/bid-document-tool", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/JSTCCUP/bid-document-tool.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-document-tool", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2024-06-24T10:12:48+08:00", "creator_id": 880849, "default_branch": "master", "description": "在线office", "http_url_to_repo": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-office-online.git", "id": 4434618, "issues_enabled": true, "last_activity_at": "2024-07-04T19:51:32+08:00", "merge_requests_enabled": true, "name": "bid-office-online", "name_with_namespace": "hzw / JSTCCUP / bid-office-online", "namespace": {"created_at": "2022-08-02T16:29:33+08:00", "description": "JSTCC", "id": 833002, "name": "JSTCCUP", "owner_id": 880849, "path": "JSTCCUP", "public": false, "updated_at": "2022-08-02T16:29:33+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/11261002b8a7613983e6e3be3e41501742ba/w/100/h/100", "id": 880849, "name": "dingtalk_fefkma", "state": "active", "username": "aliyun:dingt<PERSON>_fefkma_9qp", "web_url": ""}, "path": "bid-office-online", "path_with_namespace": "hzw/JSTCCUP/bid-office-online", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/JSTCCUP/bid-office-online.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-office-online", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2024-06-07T17:45:06+08:00", "creator_id": 880849, "default_branch": "master", "http_url_to_repo": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-sign-file.git", "id": 4401245, "issues_enabled": true, "last_activity_at": "2025-06-24T17:21:59+08:00", "merge_requests_enabled": true, "name": "bid-sign-file", "name_with_namespace": "hzw / JSTCCUP / bid-sign-file", "namespace": {"created_at": "2022-08-02T16:29:33+08:00", "description": "JSTCC", "id": 833002, "name": "JSTCCUP", "owner_id": 880849, "path": "JSTCCUP", "public": false, "updated_at": "2022-08-02T16:29:33+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/11261002b8a7613983e6e3be3e41501742ba/w/100/h/100", "id": 880849, "name": "dingtalk_fefkma", "state": "active", "username": "aliyun:dingt<PERSON>_fefkma_9qp", "web_url": ""}, "path": "bid-sign-file", "path_with_namespace": "hzw/JSTCCUP/bid-sign-file", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/JSTCCUP/bid-sign-file.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-sign-file", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2024-06-03T11:12:30+08:00", "creator_id": 880849, "default_branch": "master", "description": "汇率工具文档", "http_url_to_repo": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-spider-doc.git", "id": 4388413, "issues_enabled": true, "last_activity_at": "2024-06-17T13:34:05+08:00", "merge_requests_enabled": true, "name": "bid-spider-doc", "name_with_namespace": "hzw / JSTCCUP / bid-spider-doc", "namespace": {"created_at": "2022-08-02T16:29:33+08:00", "description": "JSTCC", "id": 833002, "name": "JSTCCUP", "owner_id": 880849, "path": "JSTCCUP", "public": false, "updated_at": "2022-08-02T16:29:33+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/11261002b8a7613983e6e3be3e41501742ba/w/100/h/100", "id": 880849, "name": "dingtalk_fefkma", "state": "active", "username": "aliyun:dingt<PERSON>_fefkma_9qp", "web_url": ""}, "path": "bid-spider-doc", "path_with_namespace": "hzw/JSTCCUP/bid-spider-doc", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/JSTCCUP/bid-spider-doc.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-spider-doc", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2024-05-16T09:32:51+08:00", "creator_id": 880849, "default_branch": "master", "description": "汇率工具sdk", "http_url_to_repo": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-spider-client.git", "id": 4349304, "issues_enabled": true, "last_activity_at": "2024-11-27T09:31:41+08:00", "merge_requests_enabled": true, "name": "bid-spider-client", "name_with_namespace": "hzw / JSTCCUP / bid-spider-client", "namespace": {"created_at": "2022-08-02T16:29:33+08:00", "description": "JSTCC", "id": 833002, "name": "JSTCCUP", "owner_id": 880849, "path": "JSTCCUP", "public": false, "updated_at": "2022-08-02T16:29:33+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/11261002b8a7613983e6e3be3e41501742ba/w/100/h/100", "id": 880849, "name": "dingtalk_fefkma", "state": "active", "username": "aliyun:dingt<PERSON>_fefkma_9qp", "web_url": ""}, "path": "bid-spider-client", "path_with_namespace": "hzw/JSTCCUP/bid-spider-client", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/JSTCCUP/bid-spider-client.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-spider-client", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2024-05-13T11:20:50+08:00", "creator_id": 880849, "default_branch": "master", "description": "接口服务", "http_url_to_repo": "https://codeup.aliyun.com/hzw/PUSHTOOL/push-tool-api.git", "id": 4341100, "issues_enabled": true, "last_activity_at": "2024-09-06T18:48:07+08:00", "merge_requests_enabled": true, "name": "push-tool-api", "name_with_namespace": "hzw / PUSHTOOL / push-tool-api", "namespace": {"created_at": "2024-05-13T11:17:08+08:00", "description": "省平台发布工具", "id": 1369111, "name": "PUSHTOOL", "owner_id": 880849, "path": "PUSHTOOL", "public": false, "updated_at": "2024-05-13T11:17:08+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/11261002b8a7613983e6e3be3e41501742ba/w/100/h/100", "id": 880849, "name": "dingtalk_fefkma", "state": "active", "username": "aliyun:dingt<PERSON>_fefkma_9qp", "web_url": ""}, "path": "push-tool-api", "path_with_namespace": "hzw/PUSHTOOL/push-tool-api", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/PUSHTOOL/push-tool-api.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/PUSHTOOL/push-tool-api", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2024-05-13T11:17:53+08:00", "creator_id": 880849, "default_branch": "master", "description": "页面", "http_url_to_repo": "https://codeup.aliyun.com/hzw/PUSHTOOL/push-tool-ui.git", "id": 4341087, "issues_enabled": true, "last_activity_at": "2025-02-13T15:20:03+08:00", "merge_requests_enabled": true, "name": "push-tool-ui", "name_with_namespace": "hzw / PUSHTOOL / push-tool-ui", "namespace": {"created_at": "2024-05-13T11:17:08+08:00", "description": "省平台发布工具", "id": 1369111, "name": "PUSHTOOL", "owner_id": 880849, "path": "PUSHTOOL", "public": false, "updated_at": "2024-05-13T11:17:08+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/11261002b8a7613983e6e3be3e41501742ba/w/100/h/100", "id": 880849, "name": "dingtalk_fefkma", "state": "active", "username": "aliyun:dingt<PERSON>_fefkma_9qp", "web_url": ""}, "path": "push-tool-ui", "path_with_namespace": "hzw/PUSHTOOL/push-tool-ui", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/PUSHTOOL/push-tool-ui.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/PUSHTOOL/push-tool-ui", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2024-04-29T13:31:27+08:00", "creator_id": 880849, "default_branch": "master", "description": "文本转语音", "http_url_to_repo": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-textToSpeech.git", "id": 4315532, "issues_enabled": true, "last_activity_at": "2025-06-24T17:48:00+08:00", "merge_requests_enabled": true, "name": "bid-textToSpeech", "name_with_namespace": "hzw / JSTCCUP / bid-textToSpeech", "namespace": {"created_at": "2022-08-02T16:29:33+08:00", "description": "JSTCC", "id": 833002, "name": "JSTCCUP", "owner_id": 880849, "path": "JSTCCUP", "public": false, "updated_at": "2022-08-02T16:29:33+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/11261002b8a7613983e6e3be3e41501742ba/w/100/h/100", "id": 880849, "name": "dingtalk_fefkma", "state": "active", "username": "aliyun:dingt<PERSON>_fefkma_9qp", "web_url": ""}, "path": "bid-textToSpeech", "path_with_namespace": "hzw/JSTCCUP/bid-textToSpeech", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/JSTCCUP/bid-textToSpeech.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-textToSpeech", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2024-04-26T11:02:28+08:00", "creator_id": 880849, "default_branch": "master", "description": "汇率工具接口", "http_url_to_repo": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-spider-api.git", "id": 4309405, "issues_enabled": true, "last_activity_at": "2024-12-09T11:24:07+08:00", "merge_requests_enabled": true, "name": "bid-spider-api", "name_with_namespace": "hzw / JSTCCUP / bid-spider-api", "namespace": {"created_at": "2022-08-02T16:29:33+08:00", "description": "JSTCC", "id": 833002, "name": "JSTCCUP", "owner_id": 880849, "path": "JSTCCUP", "public": false, "updated_at": "2022-08-02T16:29:33+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/11261002b8a7613983e6e3be3e41501742ba/w/100/h/100", "id": 880849, "name": "dingtalk_fefkma", "state": "active", "username": "aliyun:dingt<PERSON>_fefkma_9qp", "web_url": ""}, "path": "bid-spider-api", "path_with_namespace": "hzw/JSTCCUP/bid-spider-api", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/JSTCCUP/bid-spider-api.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-spider-api", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2024-04-25T16:48:49+08:00", "creator_id": 880849, "default_branch": "master", "description": "汇率工具", "http_url_to_repo": "https://codeup.aliyun.com/hzw/JITC/spider-main.git", "id": 4308028, "issues_enabled": true, "last_activity_at": "2024-11-27T09:16:13+08:00", "merge_requests_enabled": true, "name": "spider-main", "name_with_namespace": "hzw / JITC / spider-main", "namespace": {"created_at": "2022-12-12T14:40:02+08:00", "description": "", "id": 1038117, "name": "JITC", "owner_id": 880849, "path": "JITC", "public": false, "updated_at": "2022-12-12T14:40:02+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/11261002b8a7613983e6e3be3e41501742ba/w/100/h/100", "id": 880849, "name": "dingtalk_fefkma", "state": "active", "username": "aliyun:dingt<PERSON>_fefkma_9qp", "web_url": ""}, "path": "spider-main", "path_with_namespace": "hzw/JITC/spider-main", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/JITC/spider-main.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/JITC/spider-main", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2024-03-19T13:39:44+08:00", "creator_id": 880849, "default_branch": "master", "description": "国家平台数据爬取", "http_url_to_repo": "https://codeup.aliyun.com/hzw/DATASCRAPING/ctbpsp_crawler.git", "id": 4220356, "issues_enabled": true, "last_activity_at": "2024-03-19T13:39:45+08:00", "merge_requests_enabled": true, "name": "ctbpsp_crawler", "name_with_namespace": "hzw / DATASCRAPING / ctbpsp_crawler", "namespace": {"created_at": "2024-03-19T13:38:17+08:00", "description": "国家平台数据爬取", "id": 1339991, "name": "DATASCRAPING", "owner_id": 880849, "path": "DATASCRAPING", "public": false, "updated_at": "2024-03-19T13:38:17+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/11261002b8a7613983e6e3be3e41501742ba/w/100/h/100", "id": 880849, "name": "dingtalk_fefkma", "state": "active", "username": "aliyun:dingt<PERSON>_fefkma_9qp", "web_url": ""}, "path": "ctbpsp_crawler", "path_with_namespace": "hzw/DATASCRAPING/ctbpsp_crawler", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/DATASCRAPING/ctbpsp_crawler.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/DATASCRAPING/ctbpsp_crawler", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2024-03-12T16:19:58+08:00", "creator_id": 880849, "default_branch": "master", "http_url_to_repo": "https://codeup.aliyun.com/hzw/SAASPORTAL/portal-index-ui.git", "id": 4205117, "issues_enabled": true, "last_activity_at": "2024-03-12T17:30:34+08:00", "merge_requests_enabled": true, "name": "portal-index-ui", "name_with_namespace": "hzw / SAASPORTAL / portal-index-ui", "namespace": {"created_at": "2024-03-12T16:19:16+08:00", "description": "saas门户网站代码", "id": 1336257, "name": "SAASPORTAL", "owner_id": 880849, "path": "SAASPORTAL", "public": false, "updated_at": "2024-03-12T16:19:16+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/11261002b8a7613983e6e3be3e41501742ba/w/100/h/100", "id": 880849, "name": "dingtalk_fefkma", "state": "active", "username": "aliyun:dingt<PERSON>_fefkma_9qp", "web_url": ""}, "path": "portal-index-ui", "path_with_namespace": "hzw/SAASPORTAL/portal-index-ui", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/SAASPORTAL/portal-index-ui.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/SAASPORTAL/portal-index-ui", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2024-02-26T09:37:01+08:00", "creator_id": 880849, "default_branch": "master", "description": "电厂门户首页", "http_url_to_repo": "https://codeup.aliyun.com/hzw/PORTAL/portal-index-ui.git", "id": 4157403, "issues_enabled": true, "last_activity_at": "2025-01-10T18:16:07+08:00", "merge_requests_enabled": true, "name": "portal-index-ui", "name_with_namespace": "hzw / PORTAL / portal-index-ui", "namespace": {"created_at": "2024-02-22T11:19:05+08:00", "description": "电厂平台", "id": 1326129, "name": "PORTAL", "owner_id": 880849, "path": "PORTAL", "public": false, "updated_at": "2024-02-22T11:19:05+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/11261002b8a7613983e6e3be3e41501742ba/w/100/h/100", "id": 880849, "name": "dingtalk_fefkma", "state": "active", "username": "aliyun:dingt<PERSON>_fefkma_9qp", "web_url": ""}, "path": "portal-index-ui", "path_with_namespace": "hzw/PORTAL/portal-index-ui", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/PORTAL/portal-index-ui.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/PORTAL/portal-index-ui", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2024-02-22T11:24:57+08:00", "creator_id": 880849, "default_branch": "master", "description": "电厂页面", "http_url_to_repo": "https://codeup.aliyun.com/hzw/PORTAL/portal-ui.git", "id": 4151805, "issues_enabled": true, "last_activity_at": "2025-04-30T10:07:05+08:00", "merge_requests_enabled": true, "name": "portal-ui", "name_with_namespace": "hzw / PORTAL / portal-ui", "namespace": {"created_at": "2024-02-22T11:19:05+08:00", "description": "电厂平台", "id": 1326129, "name": "PORTAL", "owner_id": 880849, "path": "PORTAL", "public": false, "updated_at": "2024-02-22T11:19:05+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/11261002b8a7613983e6e3be3e41501742ba/w/100/h/100", "id": 880849, "name": "dingtalk_fefkma", "state": "active", "username": "aliyun:dingt<PERSON>_fefkma_9qp", "web_url": ""}, "path": "portal-ui", "path_with_namespace": "hzw/PORTAL/portal-ui", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/PORTAL/portal-ui.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/PORTAL/portal-ui", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2024-02-22T11:21:39+08:00", "creator_id": 880849, "default_branch": "master", "description": "电厂接口服务", "http_url_to_repo": "https://codeup.aliyun.com/hzw/PORTAL/portal-cloud.git", "id": 4151791, "issues_enabled": true, "last_activity_at": "2025-08-01T22:14:37+08:00", "merge_requests_enabled": true, "name": "portal-cloud", "name_with_namespace": "hzw / PORTAL / portal-cloud", "namespace": {"created_at": "2024-02-22T11:19:05+08:00", "description": "电厂平台", "id": 1326129, "name": "PORTAL", "owner_id": 880849, "path": "PORTAL", "public": false, "updated_at": "2024-02-22T11:19:05+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/11261002b8a7613983e6e3be3e41501742ba/w/100/h/100", "id": 880849, "name": "dingtalk_fefkma", "state": "active", "username": "aliyun:dingt<PERSON>_fefkma_9qp", "web_url": ""}, "path": "portal-cloud", "path_with_namespace": "hzw/PORTAL/portal-cloud", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/PORTAL/portal-cloud.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/PORTAL/portal-cloud", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2024-01-25T13:49:25+08:00", "creator_id": 880849, "default_branch": "master", "description": "清标工具sdk", "http_url_to_repo": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-clear-tool-client.git", "id": 4104310, "issues_enabled": true, "last_activity_at": "2024-02-01T14:19:23+08:00", "merge_requests_enabled": true, "name": "bid-clear-tool-client", "name_with_namespace": "hzw / JSTCCUP / bid-clear-tool-client", "namespace": {"created_at": "2022-08-02T16:29:33+08:00", "description": "JSTCC", "id": 833002, "name": "JSTCCUP", "owner_id": 880849, "path": "JSTCCUP", "public": false, "updated_at": "2022-08-02T16:29:33+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/11261002b8a7613983e6e3be3e41501742ba/w/100/h/100", "id": 880849, "name": "dingtalk_fefkma", "state": "active", "username": "aliyun:dingt<PERSON>_fefkma_9qp", "web_url": ""}, "path": "bid-clear-tool-client", "path_with_namespace": "hzw/JSTCCUP/bid-clear-tool-client", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/JSTCCUP/bid-clear-tool-client.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-clear-tool-client", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2024-01-24T10:08:09+08:00", "creator_id": 880849, "default_branch": "master", "description": "清标工具", "http_url_to_repo": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-clear-tool.git", "id": 4101127, "issues_enabled": true, "last_activity_at": "2025-07-08T09:03:15+08:00", "merge_requests_enabled": true, "name": "bid-clear-tool", "name_with_namespace": "hzw / JSTCCUP / bid-clear-tool", "namespace": {"created_at": "2022-08-02T16:29:33+08:00", "description": "JSTCC", "id": 833002, "name": "JSTCCUP", "owner_id": 880849, "path": "JSTCCUP", "public": false, "updated_at": "2022-08-02T16:29:33+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/11261002b8a7613983e6e3be3e41501742ba/w/100/h/100", "id": 880849, "name": "dingtalk_fefkma", "state": "active", "username": "aliyun:dingt<PERSON>_fefkma_9qp", "web_url": ""}, "path": "bid-clear-tool", "path_with_namespace": "hzw/JSTCCUP/bid-clear-tool", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/JSTCCUP/bid-clear-tool.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-clear-tool", "wiki_enabled": true}, {"archived": false, "avatar_url": "", "builds_enabled": true, "checkemail": false, "created_at": "2024-01-23T10:56:13+08:00", "creator_id": 881075, "default_branch": "master", "description": "江苏省招标投标公共服务平台发布工具v2.0，⚠️已废弃", "http_url_to_repo": "https://codeup.aliyun.com/hzw/JSFGW/publishTool.git", "id": 4098505, "issues_enabled": true, "last_activity_at": "2024-01-24T17:13:55+08:00", "merge_requests_enabled": true, "name": "publishTool", "name_with_namespace": "hzw / JSFGW / publishTool", "namespace": {"created_at": "2022-08-15T14:07:51+08:00", "description": "江苏省招标投标公共服务平台", "id": 861313, "name": "JSFGW", "owner_id": 881075, "path": "JSFGW", "public": false, "updated_at": "2022-08-15T14:07:51+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/112k6ff167e76a698c621bb236aec0bddbce/w/200/h/200", "id": 881075, "name": "qzw", "state": "active", "username": "aliyun:dingtalk_vzxhqg_Xbiwy", "web_url": ""}, "path": "publishTool", "path_with_namespace": "hzw/JSFGW/publishTool", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/JSFGW/publishTool.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/JSFGW/publishTool", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2024-01-22T17:39:39+08:00", "creator_id": 880849, "default_branch": "master", "description": "流量限制", "http_url_to_repo": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-common-ratelimiter.git", "id": 4097438, "issues_enabled": true, "last_activity_at": "2024-01-23T18:06:38+08:00", "merge_requests_enabled": true, "name": "bid-common-ratelimiter", "name_with_namespace": "hzw / JSTCCUP / bid-common-ratelimiter", "namespace": {"created_at": "2022-08-02T16:29:33+08:00", "description": "JSTCC", "id": 833002, "name": "JSTCCUP", "owner_id": 880849, "path": "JSTCCUP", "public": false, "updated_at": "2022-08-02T16:29:33+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/11261002b8a7613983e6e3be3e41501742ba/w/100/h/100", "id": 880849, "name": "dingtalk_fefkma", "state": "active", "username": "aliyun:dingt<PERSON>_fefkma_9qp", "web_url": ""}, "path": "bid-common-ratelimiter", "path_with_namespace": "hzw/JSTCCUP/bid-common-ratelimiter", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/JSTCCUP/bid-common-ratelimiter.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-common-ratelimiter", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2024-01-19T13:48:34+08:00", "creator_id": 881075, "default_branch": "main", "description": "江苏省招标投标公共服务平台运维页面", "http_url_to_repo": "https://codeup.aliyun.com/hzw/JSFGW/devops.git", "id": 4092245, "issues_enabled": true, "last_activity_at": "2024-01-19T13:55:00+08:00", "merge_requests_enabled": true, "name": "devops", "name_with_namespace": "hzw / JSFGW / devops", "namespace": {"created_at": "2022-08-15T14:07:51+08:00", "description": "江苏省招标投标公共服务平台", "id": 861313, "name": "JSFGW", "owner_id": 881075, "path": "JSFGW", "public": false, "updated_at": "2022-08-15T14:07:51+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/112k6ff167e76a698c621bb236aec0bddbce/w/200/h/200", "id": 881075, "name": "qzw", "state": "active", "username": "aliyun:dingtalk_vzxhqg_Xbiwy", "web_url": ""}, "path": "devops", "path_with_namespace": "hzw/JSFGW/devops", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/JSFGW/devops.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/JSFGW/devops", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2024-01-17T15:12:13+08:00", "creator_id": 880849, "default_branch": "master", "description": "电子开评标游客接口", "http_url_to_repo": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-omr-api.git", "id": 4087170, "issues_enabled": true, "last_activity_at": "2025-08-13T17:31:30+08:00", "merge_requests_enabled": true, "name": "bid-omr-api", "name_with_namespace": "hzw / JSTCCUP / bid-omr-api", "namespace": {"created_at": "2022-08-02T16:29:33+08:00", "description": "JSTCC", "id": 833002, "name": "JSTCCUP", "owner_id": 880849, "path": "JSTCCUP", "public": false, "updated_at": "2022-08-02T16:29:33+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/11261002b8a7613983e6e3be3e41501742ba/w/100/h/100", "id": 880849, "name": "dingtalk_fefkma", "state": "active", "username": "aliyun:dingt<PERSON>_fefkma_9qp", "web_url": ""}, "path": "bid-omr-api", "path_with_namespace": "hzw/JSTCCUP/bid-omr-api", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/JSTCCUP/bid-omr-api.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-omr-api", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2024-01-17T15:11:08+08:00", "creator_id": 880849, "default_branch": "master", "description": "电子开评标游客页面", "http_url_to_repo": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-ui-omr.git", "id": 4087166, "issues_enabled": true, "last_activity_at": "2025-08-12T10:25:07+08:00", "merge_requests_enabled": true, "name": "bid-ui-omr", "name_with_namespace": "hzw / JSTCCUP / bid-ui-omr", "namespace": {"created_at": "2022-08-02T16:29:33+08:00", "description": "JSTCC", "id": 833002, "name": "JSTCCUP", "owner_id": 880849, "path": "JSTCCUP", "public": false, "updated_at": "2022-08-02T16:29:33+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/11261002b8a7613983e6e3be3e41501742ba/w/100/h/100", "id": 880849, "name": "dingtalk_fefkma", "state": "active", "username": "aliyun:dingt<PERSON>_fefkma_9qp", "web_url": ""}, "path": "bid-ui-omr", "path_with_namespace": "hzw/JSTCCUP/bid-ui-omr", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/JSTCCUP/bid-ui-omr.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-ui-omr", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2023-11-15T09:18:59+08:00", "creator_id": 881075, "default_branch": "main", "description": "江苏省发改委数据大屏项目", "http_url_to_repo": "https://codeup.aliyun.com/hzw/JSFGW/datascrenn2.0.git", "id": 3934415, "issues_enabled": true, "last_activity_at": "2025-04-07T17:35:51+08:00", "merge_requests_enabled": true, "name": "datascrenn2.0", "name_with_namespace": "hzw / JSFGW / datascrenn2.0", "namespace": {"created_at": "2022-08-15T14:07:51+08:00", "description": "江苏省招标投标公共服务平台", "id": 861313, "name": "JSFGW", "owner_id": 881075, "path": "JSFGW", "public": false, "updated_at": "2022-08-15T14:07:51+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/112k6ff167e76a698c621bb236aec0bddbce/w/200/h/200", "id": 881075, "name": "qzw", "state": "active", "username": "aliyun:dingtalk_vzxhqg_Xbiwy", "web_url": ""}, "path": "datascrenn2.0", "path_with_namespace": "hzw/JSFGW/datascrenn2.0", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/JSFGW/datascrenn2.0.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/JSFGW/datascrenn2.0", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2023-10-23T16:09:33+08:00", "creator_id": 880849, "default_branch": "master", "description": "专家客户端签章", "http_url_to_repo": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-sign-expert.git", "id": 3876021, "issues_enabled": true, "last_activity_at": "2025-08-26T15:34:50+08:00", "merge_requests_enabled": true, "name": "bid-sign-expert", "name_with_namespace": "hzw / JSTCCUP / bid-sign-expert", "namespace": {"created_at": "2022-08-02T16:29:33+08:00", "description": "JSTCC", "id": 833002, "name": "JSTCCUP", "owner_id": 880849, "path": "JSTCCUP", "public": false, "updated_at": "2022-08-02T16:29:33+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/11261002b8a7613983e6e3be3e41501742ba/w/100/h/100", "id": 880849, "name": "dingtalk_fefkma", "state": "active", "username": "aliyun:dingt<PERSON>_fefkma_9qp", "web_url": ""}, "path": "bid-sign-expert", "path_with_namespace": "hzw/JSTCCUP/bid-sign-expert", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/JSTCCUP/bid-sign-expert.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-sign-expert", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2023-09-12T09:25:11+08:00", "creator_id": 880849, "default_branch": "master", "description": "oa消息推送", "http_url_to_repo": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-oa-msgpush.git", "id": 3786553, "issues_enabled": true, "last_activity_at": "2024-10-18T21:16:36+08:00", "merge_requests_enabled": true, "name": "bid-oa-msgpush", "name_with_namespace": "hzw / JSTCCUP / bid-oa-msgpush", "namespace": {"created_at": "2022-08-02T16:29:33+08:00", "description": "JSTCC", "id": 833002, "name": "JSTCCUP", "owner_id": 880849, "path": "JSTCCUP", "public": false, "updated_at": "2022-08-02T16:29:33+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/11261002b8a7613983e6e3be3e41501742ba/w/100/h/100", "id": 880849, "name": "dingtalk_fefkma", "state": "active", "username": "aliyun:dingt<PERSON>_fefkma_9qp", "web_url": ""}, "path": "bid-oa-msgpush", "path_with_namespace": "hzw/JSTCCUP/bid-oa-msgpush", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/JSTCCUP/bid-oa-msgpush.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-oa-msgpush", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2023-08-22T16:44:09+08:00", "creator_id": 880849, "default_branch": "master", "description": "oa", "http_url_to_repo": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-oa.git", "id": 3737393, "issues_enabled": true, "last_activity_at": "2025-07-10T08:58:27+08:00", "merge_requests_enabled": true, "name": "bid-oa", "name_with_namespace": "hzw / JSTCCUP / bid-oa", "namespace": {"created_at": "2022-08-02T16:29:33+08:00", "description": "JSTCC", "id": 833002, "name": "JSTCCUP", "owner_id": 880849, "path": "JSTCCUP", "public": false, "updated_at": "2022-08-02T16:29:33+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/11261002b8a7613983e6e3be3e41501742ba/w/100/h/100", "id": 880849, "name": "dingtalk_fefkma", "state": "active", "username": "aliyun:dingt<PERSON>_fefkma_9qp", "web_url": ""}, "path": "bid-oa", "path_with_namespace": "hzw/JSTCCUP/bid-oa", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/JSTCCUP/bid-oa.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-oa", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2023-08-12T11:21:28+08:00", "creator_id": 880849, "default_branch": "master", "description": "专家库接口调用sdk", "http_url_to_repo": "https://codeup.aliyun.com/hzw/EXPERT/bid-expert-client.git", "id": 3716592, "issues_enabled": true, "last_activity_at": "2025-08-08T08:57:19+08:00", "merge_requests_enabled": true, "name": "bid-expert-client", "name_with_namespace": "hzw / EXPERT / bid-expert-client", "namespace": {"created_at": "2023-06-29T20:57:31+08:00", "description": "专家库", "id": 1189435, "name": "EXPERT", "owner_id": 880849, "path": "EXPERT", "public": false, "updated_at": "2023-06-29T20:57:31+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/11261002b8a7613983e6e3be3e41501742ba/w/100/h/100", "id": 880849, "name": "dingtalk_fefkma", "state": "active", "username": "aliyun:dingt<PERSON>_fefkma_9qp", "web_url": ""}, "path": "bid-expert-client", "path_with_namespace": "hzw/EXPERT/bid-expert-client", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/EXPERT/bid-expert-client.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/EXPERT/bid-expert-client", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2023-07-14T14:52:07+08:00", "creator_id": 880849, "default_branch": "master", "http_url_to_repo": "https://codeup.aliyun.com/hzw/EXPERT/bid-cloud.git", "id": 3647250, "issues_enabled": true, "last_activity_at": "2025-08-28T18:34:53+08:00", "merge_requests_enabled": true, "name": "bid-cloud", "name_with_namespace": "hzw / EXPERT / bid-cloud", "namespace": {"created_at": "2023-06-29T20:57:31+08:00", "description": "专家库", "id": 1189435, "name": "EXPERT", "owner_id": 880849, "path": "EXPERT", "public": false, "updated_at": "2023-06-29T20:57:31+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/11261002b8a7613983e6e3be3e41501742ba/w/100/h/100", "id": 880849, "name": "dingtalk_fefkma", "state": "active", "username": "aliyun:dingt<PERSON>_fefkma_9qp", "web_url": ""}, "path": "bid-cloud", "path_with_namespace": "hzw/EXPERT/bid-cloud", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/EXPERT/bid-cloud.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/EXPERT/bid-cloud", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2023-07-10T17:10:05+08:00", "creator_id": 880849, "default_branch": "master", "description": "专家库小程序", "http_url_to_repo": "https://codeup.aliyun.com/hzw/EXPERT/expert-wechat-app.git", "id": 3633909, "issues_enabled": true, "last_activity_at": "2025-02-13T11:23:35+08:00", "merge_requests_enabled": true, "name": "expert-wechat-app", "name_with_namespace": "hzw / EXPERT / expert-wechat-app", "namespace": {"created_at": "2023-06-29T20:57:31+08:00", "description": "专家库", "id": 1189435, "name": "EXPERT", "owner_id": 880849, "path": "EXPERT", "public": false, "updated_at": "2023-06-29T20:57:31+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/11261002b8a7613983e6e3be3e41501742ba/w/100/h/100", "id": 880849, "name": "dingtalk_fefkma", "state": "active", "username": "aliyun:dingt<PERSON>_fefkma_9qp", "web_url": ""}, "path": "expert-wechat-app", "path_with_namespace": "hzw/EXPERT/expert-wechat-app", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/EXPERT/expert-wechat-app.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/EXPERT/expert-wechat-app", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2023-06-29T21:01:36+08:00", "creator_id": 880849, "default_branch": "master", "description": "专家库服务", "http_url_to_repo": "https://codeup.aliyun.com/hzw/EXPERT/expert-api.git", "id": 3606330, "issues_enabled": true, "last_activity_at": "2023-08-01T08:59:28+08:00", "merge_requests_enabled": true, "name": "expert-api", "name_with_namespace": "hzw / EXPERT / expert-api", "namespace": {"created_at": "2023-06-29T20:57:31+08:00", "description": "专家库", "id": 1189435, "name": "EXPERT", "owner_id": 880849, "path": "EXPERT", "public": false, "updated_at": "2023-06-29T20:57:31+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/11261002b8a7613983e6e3be3e41501742ba/w/100/h/100", "id": 880849, "name": "dingtalk_fefkma", "state": "active", "username": "aliyun:dingt<PERSON>_fefkma_9qp", "web_url": ""}, "path": "expert-api", "path_with_namespace": "hzw/EXPERT/expert-api", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/EXPERT/expert-api.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/EXPERT/expert-api", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2023-06-29T20:59:48+08:00", "creator_id": 880849, "default_branch": "master", "description": "专家库", "http_url_to_repo": "https://codeup.aliyun.com/hzw/EXPERT/expert-ui.git", "id": 3606318, "issues_enabled": true, "last_activity_at": "2025-08-12T10:17:06+08:00", "merge_requests_enabled": true, "name": "expert-ui", "name_with_namespace": "hzw / EXPERT / expert-ui", "namespace": {"created_at": "2023-06-29T20:57:31+08:00", "description": "专家库", "id": 1189435, "name": "EXPERT", "owner_id": 880849, "path": "EXPERT", "public": false, "updated_at": "2023-06-29T20:57:31+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/11261002b8a7613983e6e3be3e41501742ba/w/100/h/100", "id": 880849, "name": "dingtalk_fefkma", "state": "active", "username": "aliyun:dingt<PERSON>_fefkma_9qp", "web_url": ""}, "path": "expert-ui", "path_with_namespace": "hzw/EXPERT/expert-ui", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/EXPERT/expert-ui.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/EXPERT/expert-ui", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2023-05-09T17:44:26+08:00", "creator_id": 880849, "default_branch": "master", "description": "南京银行ftp文件中转服务", "http_url_to_repo": "https://codeup.aliyun.com/hzw/NJBK/data-file-tran-api.git", "id": 3421965, "issues_enabled": true, "last_activity_at": "2023-12-05T14:36:38+08:00", "merge_requests_enabled": true, "name": "data-file-tran-api", "name_with_namespace": "hzw / NJBK / data-file-tran-api", "namespace": {"created_at": "2023-02-16T09:56:20+08:00", "description": "南京银行", "id": 1073376, "name": "NJBK", "owner_id": 880849, "path": "NJBK", "public": false, "updated_at": "2023-02-16T09:56:34+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/11261002b8a7613983e6e3be3e41501742ba/w/100/h/100", "id": 880849, "name": "dingtalk_fefkma", "state": "active", "username": "aliyun:dingt<PERSON>_fefkma_9qp", "web_url": ""}, "path": "data-file-tran-api", "path_with_namespace": "hzw/NJBK/data-file-tran-api", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/NJBK/data-file-tran-api.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/NJBK/data-file-tran-api", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2023-04-27T19:57:13+08:00", "creator_id": 880849, "default_branch": "master", "description": "线上配置信息", "http_url_to_repo": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-config.git", "id": 3397963, "issues_enabled": true, "last_activity_at": "2023-05-19T21:29:11+08:00", "merge_requests_enabled": true, "name": "bid-config", "name_with_namespace": "hzw / JSTCCUP / bid-config", "namespace": {"created_at": "2022-08-02T16:29:33+08:00", "description": "JSTCC", "id": 833002, "name": "JSTCCUP", "owner_id": 880849, "path": "JSTCCUP", "public": false, "updated_at": "2022-08-02T16:29:33+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/11261002b8a7613983e6e3be3e41501742ba/w/100/h/100", "id": 880849, "name": "dingtalk_fefkma", "state": "active", "username": "aliyun:dingt<PERSON>_fefkma_9qp", "web_url": ""}, "path": "bid-config", "path_with_namespace": "hzw/JSTCCUP/bid-config", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/JSTCCUP/bid-config.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-config", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2023-04-13T09:40:51+08:00", "creator_id": 880849, "default_branch": "master", "http_url_to_repo": "https://codeup.aliyun.com/hzw/NJFGW/source.git", "id": 3350181, "issues_enabled": true, "last_activity_at": "2023-08-04T15:19:38+08:00", "merge_requests_enabled": true, "name": "source", "name_with_namespace": "hzw / NJFGW / source", "namespace": {"created_at": "2022-10-18T10:03:13+08:00", "description": "南京市货物招标投标监督平台", "id": 992583, "name": "NJFGW", "owner_id": 881075, "path": "NJFGW", "public": false, "updated_at": "2022-10-18T10:03:13+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/11261002b8a7613983e6e3be3e41501742ba/w/100/h/100", "id": 880849, "name": "dingtalk_fefkma", "state": "active", "username": "aliyun:dingt<PERSON>_fefkma_9qp", "web_url": ""}, "path": "source", "path_with_namespace": "hzw/NJFGW/source", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/NJFGW/source.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/NJFGW/source", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2023-03-20T14:56:55+08:00", "creator_id": 880849, "default_branch": "master", "description": "bid-hub-client-sdk", "http_url_to_repo": "https://codeup.aliyun.com/hzw/BIDHUB/bid-hub-client-sdk.git", "id": 3269030, "issues_enabled": true, "last_activity_at": "2024-01-11T18:03:58+08:00", "merge_requests_enabled": true, "name": "bid-hub-client-sdk", "name_with_namespace": "hzw / BIDHUB / bid-hub-client-sdk", "namespace": {"created_at": "2023-02-07T09:07:47+08:00", "description": "BIDHUB区块链技术", "id": 1065946, "name": "BIDHUB", "owner_id": 880849, "path": "BIDHUB", "public": false, "updated_at": "2023-02-07T09:07:47+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/11261002b8a7613983e6e3be3e41501742ba/w/100/h/100", "id": 880849, "name": "dingtalk_fefkma", "state": "active", "username": "aliyun:dingt<PERSON>_fefkma_9qp", "web_url": ""}, "path": "bid-hub-client-sdk", "path_with_namespace": "hzw/BIDHUB/bid-hub-client-sdk", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/BIDHUB/bid-hub-client-sdk.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/BIDHUB/bid-hub-client-sdk", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2023-03-20T10:58:24+08:00", "creator_id": 880849, "default_branch": "master", "description": "南京银行交接源码", "http_url_to_repo": "https://codeup.aliyun.com/hzw/NJBK/source.git", "id": 3267858, "issues_enabled": true, "last_activity_at": "2023-04-19T09:56:54+08:00", "merge_requests_enabled": true, "name": "source", "name_with_namespace": "hzw / NJBK / source", "namespace": {"created_at": "2023-02-16T09:56:20+08:00", "description": "南京银行", "id": 1073376, "name": "NJBK", "owner_id": 880849, "path": "NJBK", "public": false, "updated_at": "2023-02-16T09:56:34+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/11261002b8a7613983e6e3be3e41501742ba/w/100/h/100", "id": 880849, "name": "dingtalk_fefkma", "state": "active", "username": "aliyun:dingt<PERSON>_fefkma_9qp", "web_url": ""}, "path": "source", "path_with_namespace": "hzw/NJBK/source", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/NJBK/source.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/NJBK/source", "wiki_enabled": true}, {"archived": false, "avatar_url": "", "builds_enabled": true, "checkemail": false, "created_at": "2023-03-16T13:39:52+08:00", "creator_id": 880849, "default_branch": "master", "description": "bid-hub-client 数据同步客户端接口", "http_url_to_repo": "https://codeup.aliyun.com/hzw/BIDHUB/bid-hub-client.git", "id": 3257160, "issues_enabled": true, "last_activity_at": "2024-08-23T23:30:09+08:00", "merge_requests_enabled": true, "name": "bid-hub-client", "name_with_namespace": "hzw / BIDHUB / bid-hub-client", "namespace": {"created_at": "2023-02-07T09:07:47+08:00", "description": "BIDHUB区块链技术", "id": 1065946, "name": "BIDHUB", "owner_id": 880849, "path": "BIDHUB", "public": false, "updated_at": "2023-02-07T09:07:47+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/11261002b8a7613983e6e3be3e41501742ba/w/100/h/100", "id": 880849, "name": "dingtalk_fefkma", "state": "active", "username": "aliyun:dingt<PERSON>_fefkma_9qp", "web_url": ""}, "path": "bid-hub-client", "path_with_namespace": "hzw/BIDHUB/bid-hub-client", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/BIDHUB/bid-hub-client.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/BIDHUB/bid-hub-client", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2023-03-16T10:02:15+08:00", "creator_id": 880849, "default_branch": "master", "description": "调用数据中心接口sdk", "http_url_to_repo": "https://codeup.aliyun.com/hzw/BIDHUB/bid-hub-server-api-client.git", "id": 3256195, "issues_enabled": true, "last_activity_at": "2024-01-25T09:43:41+08:00", "merge_requests_enabled": true, "name": "bid-hub-server-api-client", "name_with_namespace": "hzw / BIDHUB / bid-hub-server-api-client", "namespace": {"created_at": "2023-02-07T09:07:47+08:00", "description": "BIDHUB区块链技术", "id": 1065946, "name": "BIDHUB", "owner_id": 880849, "path": "BIDHUB", "public": false, "updated_at": "2023-02-07T09:07:47+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/11261002b8a7613983e6e3be3e41501742ba/w/100/h/100", "id": 880849, "name": "dingtalk_fefkma", "state": "active", "username": "aliyun:dingt<PERSON>_fefkma_9qp", "web_url": ""}, "path": "bid-hub-server-api-client", "path_with_namespace": "hzw/BIDHUB/bid-hub-server-api-client", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/BIDHUB/bid-hub-server-api-client.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/BIDHUB/bid-hub-server-api-client", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2023-03-14T15:20:10+08:00", "creator_id": 880849, "default_branch": "master", "description": "文件处理", "http_url_to_repo": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-file.git", "id": 3248517, "issues_enabled": true, "last_activity_at": "2023-03-16T16:46:52+08:00", "merge_requests_enabled": true, "name": "bid-file", "name_with_namespace": "hzw / JSTCCUP / bid-file", "namespace": {"created_at": "2022-08-02T16:29:33+08:00", "description": "JSTCC", "id": 833002, "name": "JSTCCUP", "owner_id": 880849, "path": "JSTCCUP", "public": false, "updated_at": "2022-08-02T16:29:33+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/11261002b8a7613983e6e3be3e41501742ba/w/100/h/100", "id": 880849, "name": "dingtalk_fefkma", "state": "active", "username": "aliyun:dingt<PERSON>_fefkma_9qp", "web_url": ""}, "path": "bid-file", "path_with_namespace": "hzw/JSTCCUP/bid-file", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/JSTCCUP/bid-file.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-file", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2023-03-14T14:57:08+08:00", "creator_id": 880849, "default_branch": "master", "description": "投标文件解密", "http_url_to_repo": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-file-decrypt.git", "id": 3248386, "issues_enabled": true, "last_activity_at": "2023-03-14T14:58:00+08:00", "merge_requests_enabled": true, "name": "bid-file-decrypt", "name_with_namespace": "hzw / JSTCCUP / bid-file-decrypt", "namespace": {"created_at": "2022-08-02T16:29:33+08:00", "description": "JSTCC", "id": 833002, "name": "JSTCCUP", "owner_id": 880849, "path": "JSTCCUP", "public": false, "updated_at": "2022-08-02T16:29:33+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/11261002b8a7613983e6e3be3e41501742ba/w/100/h/100", "id": 880849, "name": "dingtalk_fefkma", "state": "active", "username": "aliyun:dingt<PERSON>_fefkma_9qp", "web_url": ""}, "path": "bid-file-decrypt", "path_with_namespace": "hzw/JSTCCUP/bid-file-decrypt", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/JSTCCUP/bid-file-decrypt.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-file-decrypt", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2023-03-14T14:10:04+08:00", "creator_id": 880849, "default_branch": "master", "description": "bid-hub 运维页面", "http_url_to_repo": "https://codeup.aliyun.com/hzw/BIDHUB/bid-hub-server-ui.git", "id": 3248071, "issues_enabled": true, "last_activity_at": "2023-12-28T15:49:58+08:00", "merge_requests_enabled": true, "name": "bid-hub-server-ui", "name_with_namespace": "hzw / BIDHUB / bid-hub-server-ui", "namespace": {"created_at": "2023-02-07T09:07:47+08:00", "description": "BIDHUB区块链技术", "id": 1065946, "name": "BIDHUB", "owner_id": 880849, "path": "BIDHUB", "public": false, "updated_at": "2023-02-07T09:07:47+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/11261002b8a7613983e6e3be3e41501742ba/w/100/h/100", "id": 880849, "name": "dingtalk_fefkma", "state": "active", "username": "aliyun:dingt<PERSON>_fefkma_9qp", "web_url": ""}, "path": "bid-hub-server-ui", "path_with_namespace": "hzw/BIDHUB/bid-hub-server-ui", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/BIDHUB/bid-hub-server-ui.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/BIDHUB/bid-hub-server-ui", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2023-03-13T15:00:14+08:00", "creator_id": 880849, "default_branch": "master", "http_url_to_repo": "https://codeup.aliyun.com/hzw/BIDHUB/bid-hub-server-api.git", "id": 3244515, "issues_enabled": true, "last_activity_at": "2025-05-13T11:21:16+08:00", "merge_requests_enabled": true, "name": "bid-hub-server-api", "name_with_namespace": "hzw / BIDHUB / bid-hub-server-api", "namespace": {"created_at": "2023-02-07T09:07:47+08:00", "description": "BIDHUB区块链技术", "id": 1065946, "name": "BIDHUB", "owner_id": 880849, "path": "BIDHUB", "public": false, "updated_at": "2023-02-07T09:07:47+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/11261002b8a7613983e6e3be3e41501742ba/w/100/h/100", "id": 880849, "name": "dingtalk_fefkma", "state": "active", "username": "aliyun:dingt<PERSON>_fefkma_9qp", "web_url": ""}, "path": "bid-hub-server-api", "path_with_namespace": "hzw/BIDHUB/bid-hub-server-api", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/BIDHUB/bid-hub-server-api.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/BIDHUB/bid-hub-server-api", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2023-03-08T16:35:14+08:00", "creator_id": 880849, "default_branch": "master", "description": "电子签章", "http_url_to_repo": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-sign.git", "id": 3230739, "issues_enabled": true, "last_activity_at": "2025-07-08T09:03:57+08:00", "merge_requests_enabled": true, "name": "bid-sign", "name_with_namespace": "hzw / JSTCCUP / bid-sign", "namespace": {"created_at": "2022-08-02T16:29:33+08:00", "description": "JSTCC", "id": 833002, "name": "JSTCCUP", "owner_id": 880849, "path": "JSTCCUP", "public": false, "updated_at": "2022-08-02T16:29:33+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/11261002b8a7613983e6e3be3e41501742ba/w/100/h/100", "id": 880849, "name": "dingtalk_fefkma", "state": "active", "username": "aliyun:dingt<PERSON>_fefkma_9qp", "web_url": ""}, "path": "bid-sign", "path_with_namespace": "hzw/JSTCCUP/bid-sign", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/JSTCCUP/bid-sign.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-sign", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2023-03-08T15:42:05+08:00", "creator_id": 880849, "default_branch": "master", "description": "文档，报表统计sql等", "http_url_to_repo": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-docs.git", "id": 3230367, "issues_enabled": true, "last_activity_at": "2023-05-23T11:04:58+08:00", "merge_requests_enabled": true, "name": "bid-docs", "name_with_namespace": "hzw / JSTCCUP / bid-docs", "namespace": {"created_at": "2022-08-02T16:29:33+08:00", "description": "JSTCC", "id": 833002, "name": "JSTCCUP", "owner_id": 880849, "path": "JSTCCUP", "public": false, "updated_at": "2022-08-02T16:29:33+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/11261002b8a7613983e6e3be3e41501742ba/w/100/h/100", "id": 880849, "name": "dingtalk_fefkma", "state": "active", "username": "aliyun:dingt<PERSON>_fefkma_9qp", "web_url": ""}, "path": "bid-docs", "path_with_namespace": "hzw/JSTCCUP/bid-docs", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/JSTCCUP/bid-docs.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-docs", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2023-02-16T13:49:42+08:00", "creator_id": 880849, "default_branch": "master", "description": "招标人页面", "http_url_to_repo": "https://codeup.aliyun.com/hzw/SAAS/bid-tenderee-ui.git", "id": 3153648, "issues_enabled": true, "last_activity_at": "2025-08-12T10:22:35+08:00", "merge_requests_enabled": true, "name": "bid-tenderee-ui", "name_with_namespace": "hzw / SAAS / bid-tenderee-ui", "namespace": {"created_at": "2023-01-03T16:18:23+08:00", "description": "saas", "id": 1049130, "name": "SAAS", "owner_id": 880849, "path": "SAAS", "public": false, "updated_at": "2023-01-03T16:18:23+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/11261002b8a7613983e6e3be3e41501742ba/w/100/h/100", "id": 880849, "name": "dingtalk_fefkma", "state": "active", "username": "aliyun:dingt<PERSON>_fefkma_9qp", "web_url": ""}, "path": "bid-tenderee-ui", "path_with_namespace": "hzw/SAAS/bid-tenderee-ui", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/SAAS/bid-tenderee-ui.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/SAAS/bid-tenderee-ui", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2023-02-16T09:57:15+08:00", "creator_id": 880849, "default_branch": "master", "http_url_to_repo": "https://codeup.aliyun.com/hzw/NJBK/cpms.git", "id": 3152802, "issues_enabled": true, "last_activity_at": "2023-05-23T11:04:58+08:00", "merge_requests_enabled": true, "name": "cpms", "name_with_namespace": "hzw / NJBK / cpms", "namespace": {"created_at": "2023-02-16T09:56:20+08:00", "description": "南京银行", "id": 1073376, "name": "NJBK", "owner_id": 880849, "path": "NJBK", "public": false, "updated_at": "2023-02-16T09:56:34+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/11261002b8a7613983e6e3be3e41501742ba/w/100/h/100", "id": 880849, "name": "dingtalk_fefkma", "state": "active", "username": "aliyun:dingt<PERSON>_fefkma_9qp", "web_url": ""}, "path": "cpms", "path_with_namespace": "hzw/NJBK/cpms", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/NJBK/cpms.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/NJBK/cpms", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2023-02-15T16:08:45+08:00", "creator_id": 880849, "default_branch": "master", "http_url_to_repo": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-license.git", "id": 3150934, "issues_enabled": true, "last_activity_at": "2023-03-07T20:35:46+08:00", "merge_requests_enabled": true, "name": "bid-license", "name_with_namespace": "hzw / JSTCCUP / bid-license", "namespace": {"created_at": "2022-08-02T16:29:33+08:00", "description": "JSTCC", "id": 833002, "name": "JSTCCUP", "owner_id": 880849, "path": "JSTCCUP", "public": false, "updated_at": "2022-08-02T16:29:33+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/11261002b8a7613983e6e3be3e41501742ba/w/100/h/100", "id": 880849, "name": "dingtalk_fefkma", "state": "active", "username": "aliyun:dingt<PERSON>_fefkma_9qp", "web_url": ""}, "path": "bid-license", "path_with_namespace": "hzw/JSTCCUP/bid-license", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/JSTCCUP/bid-license.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-license", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2023-02-07T09:08:50+08:00", "creator_id": 880849, "default_branch": "master", "http_url_to_repo": "https://codeup.aliyun.com/hzw/BIDHUB/bid-chain.git", "id": 3124547, "issues_enabled": true, "last_activity_at": "2023-02-28T09:49:59+08:00", "merge_requests_enabled": true, "name": "bid-chain", "name_with_namespace": "hzw / BIDHUB / bid-chain", "namespace": {"created_at": "2023-02-07T09:07:47+08:00", "description": "BIDHUB区块链技术", "id": 1065946, "name": "BIDHUB", "owner_id": 880849, "path": "BIDHUB", "public": false, "updated_at": "2023-02-07T09:07:47+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/11261002b8a7613983e6e3be3e41501742ba/w/100/h/100", "id": 880849, "name": "dingtalk_fefkma", "state": "active", "username": "aliyun:dingt<PERSON>_fefkma_9qp", "web_url": ""}, "path": "bid-chain", "path_with_namespace": "hzw/BIDHUB/bid-chain", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/BIDHUB/bid-chain.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/BIDHUB/bid-chain", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2023-01-05T13:35:59+08:00", "creator_id": 989543, "default_branch": "master", "http_url_to_repo": "https://codeup.aliyun.com/hzw/LKM/zjk-python-test.git", "id": 3064336, "issues_enabled": true, "last_activity_at": "2023-02-28T09:49:59+08:00", "merge_requests_enabled": true, "name": "zjk-python-test", "name_with_namespace": "hzw / LKM / zjk-python-test", "namespace": {"created_at": "2022-12-16T08:41:24+08:00", "description": "", "id": 1040785, "name": "LKM", "owner_id": 989543, "path": "LKM", "public": false, "updated_at": "2022-12-16T08:41:24+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/112oe094e22723b4009499bc5f6189029ec8/w/100/h/100", "id": 989543, "name": "李康民", "state": "active", "username": "aliyun:dt_1240330039_zFTT4", "web_url": ""}, "path": "zjk-python-test", "path_with_namespace": "hzw/LKM/zjk-python-test", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/LKM/zjk-python-test.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/LKM/zjk-python-test", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2023-01-05T09:14:22+08:00", "creator_id": 881075, "default_branch": "master", "description": "jstcc自动化测试代码", "http_url_to_repo": "https://codeup.aliyun.com/hzw/testing/JSTCC.git", "id": 3063414, "issues_enabled": true, "last_activity_at": "2023-06-07T10:51:10+08:00", "merge_requests_enabled": true, "name": "JSTCC", "name_with_namespace": "hzw / testing / JSTCC", "namespace": {"created_at": "2023-01-05T09:13:41+08:00", "description": "测试组自动化测试相关代码", "id": 1050120, "name": "testing", "owner_id": 881075, "path": "testing", "public": false, "updated_at": "2023-01-05T09:13:41+08:00", "visibility_level": "10"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/112k6ff167e76a698c621bb236aec0bddbce/w/200/h/200", "id": 881075, "name": "qzw", "state": "active", "username": "aliyun:dingtalk_vzxhqg_Xbiwy", "web_url": ""}, "path": "JSTCC", "path_with_namespace": "hzw/testing/JSTCC", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/testing/JSTCC.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/testing/JSTCC", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2023-01-04T08:49:31+08:00", "creator_id": 880849, "default_branch": "master", "description": "阿米巴系统", "http_url_to_repo": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-amoeba.git", "id": 3060559, "issues_enabled": true, "last_activity_at": "2025-08-28T14:08:53+08:00", "merge_requests_enabled": true, "name": "bid-amoeba", "name_with_namespace": "hzw / JSTCCUP / bid-amoeba", "namespace": {"created_at": "2022-08-02T16:29:33+08:00", "description": "JSTCC", "id": 833002, "name": "JSTCCUP", "owner_id": 880849, "path": "JSTCCUP", "public": false, "updated_at": "2022-08-02T16:29:33+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/11261002b8a7613983e6e3be3e41501742ba/w/100/h/100", "id": 880849, "name": "dingtalk_fefkma", "state": "active", "username": "aliyun:dingt<PERSON>_fefkma_9qp", "web_url": ""}, "path": "bid-amoeba", "path_with_namespace": "hzw/JSTCCUP/bid-amoeba", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/JSTCCUP/bid-amoeba.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-amoeba", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2022-12-16T08:41:28+08:00", "creator_id": 989543, "default_branch": "master", "description": "专家库重构项目", "http_url_to_repo": "https://codeup.aliyun.com/hzw/LKM/GZZJK_REACT.git", "id": 3021103, "issues_enabled": true, "last_activity_at": "2023-02-28T09:49:59+08:00", "merge_requests_enabled": true, "name": "GZZJK_REACT", "name_with_namespace": "hzw / LKM / GZZJK_REACT", "namespace": {"created_at": "2022-12-16T08:41:24+08:00", "description": "", "id": 1040785, "name": "LKM", "owner_id": 989543, "path": "LKM", "public": false, "updated_at": "2022-12-16T08:41:24+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/112oe094e22723b4009499bc5f6189029ec8/w/100/h/100", "id": 989543, "name": "李康民", "state": "active", "username": "aliyun:dt_1240330039_zFTT4", "web_url": ""}, "path": "GZZJK_REACT", "path_with_namespace": "hzw/LKM/GZZJK_REACT", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/LKM/GZZJK_REACT.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/LKM/GZZJK_REACT", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2022-12-12T14:40:38+08:00", "creator_id": 880849, "default_branch": "master", "http_url_to_repo": "https://codeup.aliyun.com/hzw/JITC/GZZJK.git", "id": 3009308, "issues_enabled": true, "last_activity_at": "2023-02-28T09:49:59+08:00", "merge_requests_enabled": true, "name": "GZZJK", "name_with_namespace": "hzw / JITC / GZZJK", "namespace": {"created_at": "2022-12-12T14:40:02+08:00", "description": "", "id": 1038117, "name": "JITC", "owner_id": 880849, "path": "JITC", "public": false, "updated_at": "2022-12-12T14:40:02+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/11261002b8a7613983e6e3be3e41501742ba/w/100/h/100", "id": 880849, "name": "dingtalk_fefkma", "state": "active", "username": "aliyun:dingt<PERSON>_fefkma_9qp", "web_url": ""}, "path": "GZZJK", "path_with_namespace": "hzw/JITC/GZZJK", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/JITC/GZZJK.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/JITC/GZZJK", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2022-12-02T11:26:23+08:00", "creator_id": 880849, "default_branch": "master", "http_url_to_repo": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-chain-client-ui.git", "id": 2976817, "issues_enabled": true, "last_activity_at": "2023-10-21T03:27:53+08:00", "merge_requests_enabled": true, "name": "bid-chain-client-ui", "name_with_namespace": "hzw / JSTCCUP / bid-chain-client-ui", "namespace": {"created_at": "2022-08-02T16:29:33+08:00", "description": "JSTCC", "id": 833002, "name": "JSTCCUP", "owner_id": 880849, "path": "JSTCCUP", "public": false, "updated_at": "2022-08-02T16:29:33+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/11261002b8a7613983e6e3be3e41501742ba/w/100/h/100", "id": 880849, "name": "dingtalk_fefkma", "state": "active", "username": "aliyun:dingt<PERSON>_fefkma_9qp", "web_url": ""}, "path": "bid-chain-client-ui", "path_with_namespace": "hzw/JSTCCUP/bid-chain-client-ui", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/JSTCCUP/bid-chain-client-ui.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-chain-client-ui", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2022-11-25T15:21:54+08:00", "creator_id": 880849, "default_branch": "master", "http_url_to_repo": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-chain-client-sdk.git", "id": 2952852, "issues_enabled": true, "last_activity_at": "2023-03-16T09:55:53+08:00", "merge_requests_enabled": true, "name": "bid-chain-client-sdk", "name_with_namespace": "hzw / JSTCCUP / bid-chain-client-sdk", "namespace": {"created_at": "2022-08-02T16:29:33+08:00", "description": "JSTCC", "id": 833002, "name": "JSTCCUP", "owner_id": 880849, "path": "JSTCCUP", "public": false, "updated_at": "2022-08-02T16:29:33+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/11261002b8a7613983e6e3be3e41501742ba/w/100/h/100", "id": 880849, "name": "dingtalk_fefkma", "state": "active", "username": "aliyun:dingt<PERSON>_fefkma_9qp", "web_url": ""}, "path": "bid-chain-client-sdk", "path_with_namespace": "hzw/JSTCCUP/bid-chain-client-sdk", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/JSTCCUP/bid-chain-client-sdk.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-chain-client-sdk", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2022-11-18T10:13:30+08:00", "creator_id": 880849, "default_branch": "master", "description": "jstcc restful客户端", "http_url_to_repo": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-api-open-client.git", "id": 2929748, "issues_enabled": true, "last_activity_at": "2024-01-11T11:26:25+08:00", "merge_requests_enabled": true, "name": "bid-api-open-client", "name_with_namespace": "hzw / JSTCCUP / bid-api-open-client", "namespace": {"created_at": "2022-08-02T16:29:33+08:00", "description": "JSTCC", "id": 833002, "name": "JSTCCUP", "owner_id": 880849, "path": "JSTCCUP", "public": false, "updated_at": "2022-08-02T16:29:33+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/11261002b8a7613983e6e3be3e41501742ba/w/100/h/100", "id": 880849, "name": "dingtalk_fefkma", "state": "active", "username": "aliyun:dingt<PERSON>_fefkma_9qp", "web_url": ""}, "path": "bid-api-open-client", "path_with_namespace": "hzw/JSTCCUP/bid-api-open-client", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/JSTCCUP/bid-api-open-client.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-api-open-client", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2022-11-17T18:03:15+08:00", "creator_id": 880849, "default_branch": "master", "description": "bid数据同步接口", "http_url_to_repo": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-api-open.git", "id": 2928772, "issues_enabled": true, "last_activity_at": "2025-05-16T14:52:22+08:00", "merge_requests_enabled": true, "name": "bid-api-open", "name_with_namespace": "hzw / JSTCCUP / bid-api-open", "namespace": {"created_at": "2022-08-02T16:29:33+08:00", "description": "JSTCC", "id": 833002, "name": "JSTCCUP", "owner_id": 880849, "path": "JSTCCUP", "public": false, "updated_at": "2022-08-02T16:29:33+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/11261002b8a7613983e6e3be3e41501742ba/w/100/h/100", "id": 880849, "name": "dingtalk_fefkma", "state": "active", "username": "aliyun:dingt<PERSON>_fefkma_9qp", "web_url": ""}, "path": "bid-api-open", "path_with_namespace": "hzw/JSTCCUP/bid-api-open", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/JSTCCUP/bid-api-open.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-api-open", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2022-11-17T16:10:41+08:00", "creator_id": 880849, "default_branch": "master", "description": "区块链客户端，项目数据上链", "http_url_to_repo": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-chain-client.git", "id": 2928148, "issues_enabled": true, "last_activity_at": "2023-06-21T09:40:25+08:00", "merge_requests_enabled": true, "name": "bid-chain-client", "name_with_namespace": "hzw / JSTCCUP / bid-chain-client", "namespace": {"created_at": "2022-08-02T16:29:33+08:00", "description": "JSTCC", "id": 833002, "name": "JSTCCUP", "owner_id": 880849, "path": "JSTCCUP", "public": false, "updated_at": "2022-08-02T16:29:33+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/11261002b8a7613983e6e3be3e41501742ba/w/100/h/100", "id": 880849, "name": "dingtalk_fefkma", "state": "active", "username": "aliyun:dingt<PERSON>_fefkma_9qp", "web_url": ""}, "path": "bid-chain-client", "path_with_namespace": "hzw/JSTCCUP/bid-chain-client", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/JSTCCUP/bid-chain-client.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-chain-client", "wiki_enabled": true}, {"archived": false, "avatar_url": "", "builds_enabled": true, "checkemail": false, "created_at": "2022-11-15T09:00:56+08:00", "creator_id": 881075, "default_branch": "master", "description": "江苏省发改委数据大屏项目  已废弃", "http_url_to_repo": "https://codeup.aliyun.com/hzw/JSFGW/dataScreen.git", "id": 2917908, "issues_enabled": true, "last_activity_at": "2023-08-14T16:43:18+08:00", "merge_requests_enabled": true, "name": "dataScreen", "name_with_namespace": "hzw / JSFGW / dataScreen", "namespace": {"created_at": "2022-08-15T14:07:51+08:00", "description": "江苏省招标投标公共服务平台", "id": 861313, "name": "JSFGW", "owner_id": 881075, "path": "JSFGW", "public": false, "updated_at": "2022-08-15T14:07:51+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/112k6ff167e76a698c621bb236aec0bddbce/w/200/h/200", "id": 881075, "name": "qzw", "state": "active", "username": "aliyun:dingtalk_vzxhqg_Xbiwy", "web_url": ""}, "path": "dataScreen", "path_with_namespace": "hzw/JSFGW/dataScreen", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/JSFGW/dataScreen.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/JSFGW/dataScreen", "wiki_enabled": true}, {"archived": false, "avatar_url": "", "builds_enabled": true, "checkemail": false, "created_at": "2022-10-27T11:11:30+08:00", "creator_id": 881000, "default_branch": "master", "description": "jstcc专家登陆", "http_url_to_repo": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-expert-ui.git", "id": 2862234, "issues_enabled": true, "last_activity_at": "2025-08-14T18:32:26+08:00", "merge_requests_enabled": true, "name": "bid-expert-ui", "name_with_namespace": "hzw / JSTCCUP / bid-expert-ui", "namespace": {"created_at": "2022-08-02T16:29:33+08:00", "description": "JSTCC", "id": 833002, "name": "JSTCCUP", "owner_id": 880849, "path": "JSTCCUP", "public": false, "updated_at": "2022-08-02T16:29:33+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/112k481bf1240c2eaa600b6f9532526342b8/w/200/h/200", "id": 881000, "name": "李金鑫", "state": "active", "username": "aliyun:dingtalk_mjynvn_fO6sW", "web_url": ""}, "path": "bid-expert-ui", "path_with_namespace": "hzw/JSTCCUP/bid-expert-ui", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/JSTCCUP/bid-expert-ui.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-expert-ui", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2022-10-25T09:11:04+08:00", "creator_id": 881000, "default_branch": "master", "description": "专家库小程序前端代码", "http_url_to_repo": "https://codeup.aliyun.com/hzw/JSTCCUP/expert-manage-mp-ui.git", "id": 2854597, "issues_enabled": true, "last_activity_at": "2022-10-25T09:40:20+08:00", "merge_requests_enabled": true, "name": "expert-manage-mp-ui", "name_with_namespace": "hzw / JSTCCUP / expert-manage-mp-ui", "namespace": {"created_at": "2022-08-02T16:29:33+08:00", "description": "JSTCC", "id": 833002, "name": "JSTCCUP", "owner_id": 880849, "path": "JSTCCUP", "public": false, "updated_at": "2022-08-02T16:29:33+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/112k481bf1240c2eaa600b6f9532526342b8/w/200/h/200", "id": 881000, "name": "李金鑫", "state": "active", "username": "aliyun:dingtalk_mjynvn_fO6sW", "web_url": ""}, "path": "expert-manage-mp-ui", "path_with_namespace": "hzw/JSTCCUP/expert-manage-mp-ui", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/JSTCCUP/expert-manage-mp-ui.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/JSTCCUP/expert-manage-mp-ui", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2022-10-21T18:16:18+08:00", "creator_id": 880849, "default_branch": "master", "description": "专家库前端代码", "http_url_to_repo": "https://codeup.aliyun.com/hzw/JSTCCUP/expert-manage-ui.git", "id": 2848074, "issues_enabled": true, "last_activity_at": "2023-10-21T03:27:53+08:00", "merge_requests_enabled": true, "name": "expert-manage-ui", "name_with_namespace": "hzw / JSTCCUP / expert-manage-ui", "namespace": {"created_at": "2022-08-02T16:29:33+08:00", "description": "JSTCC", "id": 833002, "name": "JSTCCUP", "owner_id": 880849, "path": "JSTCCUP", "public": false, "updated_at": "2022-08-02T16:29:33+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/11261002b8a7613983e6e3be3e41501742ba/w/100/h/100", "id": 880849, "name": "dingtalk_fefkma", "state": "active", "username": "aliyun:dingt<PERSON>_fefkma_9qp", "web_url": ""}, "path": "expert-manage-ui", "path_with_namespace": "hzw/JSTCCUP/expert-manage-ui", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/JSTCCUP/expert-manage-ui.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/JSTCCUP/expert-manage-ui", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2022-09-29T09:09:16+08:00", "creator_id": 880849, "default_branch": "master", "http_url_to_repo": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-ncc-api.git", "id": 2756274, "issues_enabled": true, "last_activity_at": "2025-07-08T08:53:13+08:00", "merge_requests_enabled": true, "name": "bid-ncc-api", "name_with_namespace": "hzw / JSTCCUP / bid-ncc-api", "namespace": {"created_at": "2022-08-02T16:29:33+08:00", "description": "JSTCC", "id": 833002, "name": "JSTCCUP", "owner_id": 880849, "path": "JSTCCUP", "public": false, "updated_at": "2022-08-02T16:29:33+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/11261002b8a7613983e6e3be3e41501742ba/w/100/h/100", "id": 880849, "name": "dingtalk_fefkma", "state": "active", "username": "aliyun:dingt<PERSON>_fefkma_9qp", "web_url": ""}, "path": "bid-ncc-api", "path_with_namespace": "hzw/JSTCCUP/bid-ncc-api", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/JSTCCUP/bid-ncc-api.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-ncc-api", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2022-09-09T10:29:42+08:00", "creator_id": 880849, "default_branch": "master", "http_url_to_repo": "https://codeup.aliyun.com/hzw/JSTCCUP/cws-api.git", "id": 2663490, "issues_enabled": true, "last_activity_at": "2025-07-08T08:53:57+08:00", "merge_requests_enabled": true, "name": "cws-api", "name_with_namespace": "hzw / JSTCCUP / cws-api", "namespace": {"created_at": "2022-08-02T16:29:33+08:00", "description": "JSTCC", "id": 833002, "name": "JSTCCUP", "owner_id": 880849, "path": "JSTCCUP", "public": false, "updated_at": "2022-08-02T16:29:33+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/11261002b8a7613983e6e3be3e41501742ba/w/100/h/100", "id": 880849, "name": "dingtalk_fefkma", "state": "active", "username": "aliyun:dingt<PERSON>_fefkma_9qp", "web_url": ""}, "path": "cws-api", "path_with_namespace": "hzw/JSTCCUP/cws-api", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/JSTCCUP/cws-api.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/JSTCCUP/cws-api", "wiki_enabled": true}, {"archived": false, "avatar_url": "", "builds_enabled": true, "checkemail": false, "created_at": "2022-08-29T10:54:35+08:00", "creator_id": 880849, "default_branch": "develop", "description": "劳务费小程序前端代码", "http_url_to_repo": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-azalea-ui.git", "id": 2603663, "issues_enabled": true, "last_activity_at": "2024-04-24T16:40:21+08:00", "merge_requests_enabled": true, "name": "bid-azalea-ui", "name_with_namespace": "hzw / JSTCCUP / bid-azalea-ui", "namespace": {"created_at": "2022-08-02T16:29:33+08:00", "description": "JSTCC", "id": 833002, "name": "JSTCCUP", "owner_id": 880849, "path": "JSTCCUP", "public": false, "updated_at": "2022-08-02T16:29:33+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/11261002b8a7613983e6e3be3e41501742ba/w/100/h/100", "id": 880849, "name": "dingtalk_fefkma", "state": "active", "username": "aliyun:dingt<PERSON>_fefkma_9qp", "web_url": ""}, "path": "bid-azalea-ui", "path_with_namespace": "hzw/JSTCCUP/bid-azalea-ui", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/JSTCCUP/bid-azalea-ui.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-azalea-ui", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2022-08-26T17:19:55+08:00", "creator_id": 880849, "default_branch": "master", "http_url_to_repo": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-common-util.git", "id": 2594100, "issues_enabled": true, "last_activity_at": "2022-08-31T10:44:49+08:00", "merge_requests_enabled": true, "name": "bid-common-util", "name_with_namespace": "hzw / JSTCCUP / bid-common-util", "namespace": {"created_at": "2022-08-02T16:29:33+08:00", "description": "JSTCC", "id": 833002, "name": "JSTCCUP", "owner_id": 880849, "path": "JSTCCUP", "public": false, "updated_at": "2022-08-02T16:29:33+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/11261002b8a7613983e6e3be3e41501742ba/w/100/h/100", "id": 880849, "name": "dingtalk_fefkma", "state": "active", "username": "aliyun:dingt<PERSON>_fefkma_9qp", "web_url": ""}, "path": "bid-common-util", "path_with_namespace": "hzw/JSTCCUP/bid-common-util", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/JSTCCUP/bid-common-util.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-common-util", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2022-08-26T16:39:33+08:00", "creator_id": 880849, "default_branch": "master", "http_url_to_repo": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-common-bean.git", "id": 2593886, "issues_enabled": true, "last_activity_at": "2025-08-22T09:37:41+08:00", "merge_requests_enabled": true, "name": "bid-common-bean", "name_with_namespace": "hzw / JSTCCUP / bid-common-bean", "namespace": {"created_at": "2022-08-02T16:29:33+08:00", "description": "JSTCC", "id": 833002, "name": "JSTCCUP", "owner_id": 880849, "path": "JSTCCUP", "public": false, "updated_at": "2022-08-02T16:29:33+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/11261002b8a7613983e6e3be3e41501742ba/w/100/h/100", "id": 880849, "name": "dingtalk_fefkma", "state": "active", "username": "aliyun:dingt<PERSON>_fefkma_9qp", "web_url": ""}, "path": "bid-common-bean", "path_with_namespace": "hzw/JSTCCUP/bid-common-bean", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/JSTCCUP/bid-common-bean.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-common-bean", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2022-08-23T11:12:00+08:00", "creator_id": 880849, "default_branch": "master", "http_url_to_repo": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-ocr-server.git", "id": 2575393, "issues_enabled": true, "last_activity_at": "2022-08-25T14:28:23+08:00", "merge_requests_enabled": true, "name": "bid-ocr-server", "name_with_namespace": "hzw / JSTCCUP / bid-ocr-server", "namespace": {"created_at": "2022-08-02T16:29:33+08:00", "description": "JSTCC", "id": 833002, "name": "JSTCCUP", "owner_id": 880849, "path": "JSTCCUP", "public": false, "updated_at": "2022-08-02T16:29:33+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/11261002b8a7613983e6e3be3e41501742ba/w/100/h/100", "id": 880849, "name": "dingtalk_fefkma", "state": "active", "username": "aliyun:dingt<PERSON>_fefkma_9qp", "web_url": ""}, "path": "bid-ocr-server", "path_with_namespace": "hzw/JSTCCUP/bid-ocr-server", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/JSTCCUP/bid-ocr-server.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/JSTCCUP/bid-ocr-server", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2022-08-19T11:09:52+08:00", "creator_id": 881075, "default_branch": "master", "description": "江苏省招标投标公共服务平台微信服务号网关", "http_url_to_repo": "https://codeup.aliyun.com/hzw/JSFGW/WechatGatwayApi.git", "id": 2557400, "issues_enabled": true, "last_activity_at": "2022-08-19T11:10:38+08:00", "merge_requests_enabled": true, "name": "WechatGatwayApi", "name_with_namespace": "hzw / JSFGW / WechatGatwayApi", "namespace": {"created_at": "2022-08-15T14:07:51+08:00", "description": "江苏省招标投标公共服务平台", "id": 861313, "name": "JSFGW", "owner_id": 881075, "path": "JSFGW", "public": false, "updated_at": "2022-08-15T14:07:51+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/112k6ff167e76a698c621bb236aec0bddbce/w/200/h/200", "id": 881075, "name": "qzw", "state": "active", "username": "aliyun:dingtalk_vzxhqg_Xbiwy", "web_url": ""}, "path": "WechatGatwayApi", "path_with_namespace": "hzw/JSFGW/WechatGatwayApi", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/JSFGW/WechatGatwayApi.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/JSFGW/WechatGatwayApi", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2022-08-19T11:04:03+08:00", "creator_id": 881075, "default_branch": "master", "description": "UserApi", "http_url_to_repo": "https://codeup.aliyun.com/hzw/JSFGW/UserApi.git", "id": 2557367, "issues_enabled": true, "last_activity_at": "2025-07-03T10:42:02+08:00", "merge_requests_enabled": true, "name": "UserApi", "name_with_namespace": "hzw / JSFGW / UserApi", "namespace": {"created_at": "2022-08-15T14:07:51+08:00", "description": "江苏省招标投标公共服务平台", "id": 861313, "name": "JSFGW", "owner_id": 881075, "path": "JSFGW", "public": false, "updated_at": "2022-08-15T14:07:51+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/112k6ff167e76a698c621bb236aec0bddbce/w/200/h/200", "id": 881075, "name": "qzw", "state": "active", "username": "aliyun:dingtalk_vzxhqg_Xbiwy", "web_url": ""}, "path": "UserApi", "path_with_namespace": "hzw/JSFGW/UserApi", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/JSFGW/UserApi.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/JSFGW/UserApi", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2022-08-19T11:02:56+08:00", "creator_id": 881075, "default_branch": "master", "description": "十号令同步相关API", "http_url_to_repo": "https://codeup.aliyun.com/hzw/JSFGW/SyncApi.git", "id": 2557362, "issues_enabled": true, "last_activity_at": "2025-06-30T14:24:10+08:00", "merge_requests_enabled": true, "name": "SyncApi", "name_with_namespace": "hzw / JSFGW / SyncApi", "namespace": {"created_at": "2022-08-15T14:07:51+08:00", "description": "江苏省招标投标公共服务平台", "id": 861313, "name": "JSFGW", "owner_id": 881075, "path": "JSFGW", "public": false, "updated_at": "2022-08-15T14:07:51+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/112k6ff167e76a698c621bb236aec0bddbce/w/200/h/200", "id": 881075, "name": "qzw", "state": "active", "username": "aliyun:dingtalk_vzxhqg_Xbiwy", "web_url": ""}, "path": "SyncApi", "path_with_namespace": "hzw/JSFGW/SyncApi", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/JSFGW/SyncApi.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/JSFGW/SyncApi", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2022-08-19T11:00:54+08:00", "creator_id": 881075, "default_branch": "master", "description": "App供应商库API", "http_url_to_repo": "https://codeup.aliyun.com/hzw/JSFGW/SupplierApi.git", "id": 2557350, "issues_enabled": true, "last_activity_at": "2022-08-19T11:01:53+08:00", "merge_requests_enabled": true, "name": "SupplierApi", "name_with_namespace": "hzw / JSFGW / SupplierApi", "namespace": {"created_at": "2022-08-15T14:07:51+08:00", "description": "江苏省招标投标公共服务平台", "id": 861313, "name": "JSFGW", "owner_id": 881075, "path": "JSFGW", "public": false, "updated_at": "2022-08-15T14:07:51+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/112k6ff167e76a698c621bb236aec0bddbce/w/200/h/200", "id": 881075, "name": "qzw", "state": "active", "username": "aliyun:dingtalk_vzxhqg_Xbiwy", "web_url": ""}, "path": "SupplierApi", "path_with_namespace": "hzw/JSFGW/SupplierApi", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/JSFGW/SupplierApi.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/JSFGW/SupplierApi", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2022-08-19T10:59:21+08:00", "creator_id": 881075, "default_branch": "master", "description": "监督通道消息队列处理", "http_url_to_repo": "https://codeup.aliyun.com/hzw/JSFGW/SuperviseDataApiClient.git", "id": 2557337, "issues_enabled": true, "last_activity_at": "2022-08-19T11:00:05+08:00", "merge_requests_enabled": true, "name": "SuperviseDataApiClient", "name_with_namespace": "hzw / JSFGW / SuperviseDataApiClient", "namespace": {"created_at": "2022-08-15T14:07:51+08:00", "description": "江苏省招标投标公共服务平台", "id": 861313, "name": "JSFGW", "owner_id": 881075, "path": "JSFGW", "public": false, "updated_at": "2022-08-15T14:07:51+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/112k6ff167e76a698c621bb236aec0bddbce/w/200/h/200", "id": 881075, "name": "qzw", "state": "active", "username": "aliyun:dingtalk_vzxhqg_Xbiwy", "web_url": ""}, "path": "SuperviseDataApiClient", "path_with_namespace": "hzw/JSFGW/SuperviseDataApiClient", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/JSFGW/SuperviseDataApiClient.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/JSFGW/SuperviseDataApiClient", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2022-08-19T10:57:50+08:00", "creator_id": 881075, "default_branch": "master", "description": "服务器配置文件", "http_url_to_repo": "https://codeup.aliyun.com/hzw/JSFGW/ServerConfigure.git", "id": 2557327, "issues_enabled": true, "last_activity_at": "2022-08-19T10:58:47+08:00", "merge_requests_enabled": true, "name": "ServerConfigure", "name_with_namespace": "hzw / JSFGW / ServerConfigure", "namespace": {"created_at": "2022-08-15T14:07:51+08:00", "description": "江苏省招标投标公共服务平台", "id": 861313, "name": "JSFGW", "owner_id": 881075, "path": "JSFGW", "public": false, "updated_at": "2022-08-15T14:07:51+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/112k6ff167e76a698c621bb236aec0bddbce/w/200/h/200", "id": 881075, "name": "qzw", "state": "active", "username": "aliyun:dingtalk_vzxhqg_Xbiwy", "web_url": ""}, "path": "ServerConfigure", "path_with_namespace": "hzw/JSFGW/ServerConfigure", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/JSFGW/ServerConfigure.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/JSFGW/ServerConfigure", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2022-08-19T10:56:17+08:00", "creator_id": 881075, "default_branch": "master", "description": "10号令生产者", "http_url_to_repo": "https://codeup.aliyun.com/hzw/JSFGW/RuleSet10MessageProducer.git", "id": 2557320, "issues_enabled": true, "last_activity_at": "2022-08-19T10:57:11+08:00", "merge_requests_enabled": true, "name": "RuleSet10MessageProducer", "name_with_namespace": "hzw / JSFGW / RuleSet10MessageProducer", "namespace": {"created_at": "2022-08-15T14:07:51+08:00", "description": "江苏省招标投标公共服务平台", "id": 861313, "name": "JSFGW", "owner_id": 881075, "path": "JSFGW", "public": false, "updated_at": "2022-08-15T14:07:51+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/112k6ff167e76a698c621bb236aec0bddbce/w/200/h/200", "id": 881075, "name": "qzw", "state": "active", "username": "aliyun:dingtalk_vzxhqg_Xbiwy", "web_url": ""}, "path": "RuleSet10MessageProducer", "path_with_namespace": "hzw/JSFGW/RuleSet10MessageProducer", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/JSFGW/RuleSet10MessageProducer.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/JSFGW/RuleSet10MessageProducer", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2022-08-19T10:52:49+08:00", "creator_id": 881075, "default_branch": "master", "description": "PublicServiceWebSite", "http_url_to_repo": "https://codeup.aliyun.com/hzw/JSFGW/PublicServiceWebSite.git", "id": 2557297, "issues_enabled": true, "last_activity_at": "2025-07-01T10:58:40+08:00", "merge_requests_enabled": true, "name": "PublicServiceWebSite", "name_with_namespace": "hzw / JSFGW / PublicServiceWebSite", "namespace": {"created_at": "2022-08-15T14:07:51+08:00", "description": "江苏省招标投标公共服务平台", "id": 861313, "name": "JSFGW", "owner_id": 881075, "path": "JSFGW", "public": false, "updated_at": "2022-08-15T14:07:51+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/112k6ff167e76a698c621bb236aec0bddbce/w/200/h/200", "id": 881075, "name": "qzw", "state": "active", "username": "aliyun:dingtalk_vzxhqg_Xbiwy", "web_url": ""}, "path": "PublicServiceWebSite", "path_with_namespace": "hzw/JSFGW/PublicServiceWebSite", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/JSFGW/PublicServiceWebSite.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/JSFGW/PublicServiceWebSite", "wiki_enabled": true}, {"archived": false, "builds_enabled": true, "checkemail": false, "created_at": "2022-08-19T10:51:24+08:00", "creator_id": 881075, "default_branch": "master", "description": "The PublicService project on WeChat and maybe on app, using Taro.js including react, redux, react native, typescript, etc.", "http_url_to_repo": "https://codeup.aliyun.com/hzw/JSFGW/PublicServiceMini.git", "id": 2557288, "issues_enabled": true, "last_activity_at": "2022-08-19T10:52:18+08:00", "merge_requests_enabled": true, "name": "PublicServiceMini", "name_with_namespace": "hzw / JSFGW / PublicServiceMini", "namespace": {"created_at": "2022-08-15T14:07:51+08:00", "description": "江苏省招标投标公共服务平台", "id": 861313, "name": "JSFGW", "owner_id": 881075, "path": "JSFGW", "public": false, "updated_at": "2022-08-15T14:07:51+08:00", "visibility_level": "0"}, "owner": {"avatar_url": "https://tcs-devops.aliyuncs.com/thumbnail/112k6ff167e76a698c621bb236aec0bddbce/w/200/h/200", "id": 881075, "name": "qzw", "state": "active", "username": "aliyun:dingtalk_vzxhqg_Xbiwy", "web_url": ""}, "path": "PublicServiceMini", "path_with_namespace": "hzw/JSFGW/PublicServiceMini", "public": false, "snippets_enabled": true, "ssh_url_to_repo": "*********************:hzw/JSFGW/PublicServiceMini.git", "tag_list": [], "visibility_level": "0", "web_url": "https://codeup.aliyun.com/hzw/JSFGW/PublicServiceMini", "wiki_enabled": true}]