package com.hzw.ssm.sys.sms;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;

import com.hzw.ssm.sys.sms.mwutil.CHttpPost;
import com.hzw.ssm.sys.sms.mwutil.ConfigManager;
import com.hzw.ssm.sys.sms.mwutil.MO;
import com.hzw.ssm.sys.sms.mwutil.Message;
import com.hzw.ssm.sys.sms.mwutil.MultiMt;
import com.hzw.ssm.sys.sms.mwutil.RPT;




public class SMSNewUtil {
	
	//梦网短信账号
		//@Value("${sms_userId}"JI2356)
		//private static String userId ="JI2356";
		//梦网短信密码
		//@Value("${sms_userPwd}"260015)
		//private static String userPwd ="260015";
		//梦网主IP
		//@Value("${sms_masterIpAddress}")*************:8901
		private static String userId ="JI2356";
			//梦网短信密码
			//@Value("${sms_userPwd}"260015)
		private static String userPwd ="260015";
			//梦网主IP
			//@Value("${sms_masterIpAddress}")*************:8901
	
		private static String masterIpAddress ="***************:8901";
		
		//梦网备用IP1
		//@Value("${sms_ipAddress1}")
		private  static String ipAddress1 ="*************:8901";
		//梦网备用IP2
		//@Value("${sms_ipAddress2}")
		private static String ipAddress2 = "*************:8901";
		//梦网发送短信接口
		private static SMSNewUtil smsNewUtil;

		public static SMSNewUtil getSMSNewUtil() {
			if (smsNewUtil == null) {
				smsNewUtil = new SMSNewUtil();
			}
			return smsNewUtil;
		}
		/**
		 * 
		 * @description  单条发送  
		 * @param userid  用户账号
		 * @param pwd 用户密码
		 * @param isEncryptPwd 密码是否加密   true：密码加密;false：密码不加密
		 */
		public  int singleSend(List<String[]> msgList)
		{
			// 返回值
			int result = -310099;
			/*try
			{   // 日期格式定义
				SimpleDateFormat	sdf	= new SimpleDateFormat("MMddHHmmss");
				// 参数类
				Message message = new Message();
				// 实例化短信处理对象
				CHttpPost cHttpPost = new CHttpPost();
				
				// 设置账号   将 userid转成大写,以防大小写不一致
				message.setUserid(userId.toUpperCase());
				boolean isEncryptPwd=publicModel(masterIpAddress,ipAddress1,ipAddress2);
				//判断密码是否加密。
				//密码加密，则对密码进行加密
				if(isEncryptPwd)
				{
					// 设置时间戳
					String timestamp = sdf.format(Calendar.getInstance().getTime());
					message.setTimestamp(timestamp);
					
					// 对密码进行加密
					String encryptPwd = cHttpPost.encryptPwd(message.getUserid(),userPwd, message.getTimestamp());
					// 设置加密后的密码
					message.setPwd(encryptPwd);
					
				}else
				{
					// 设置密码
					message.setPwd(userPwd);
				}
				
				// 设置手机号码 此处只能设置一个手机号码
				message.setMobile(smsRecordEntity.getSms_mobile());
				// 设置内容
				message.setContent(smsRecordEntity.getSms_content());
				// 设置扩展号
				message.setExno("");
				// 用户自定义流水编号
				message.setCustid(smsRecordEntity.getSms_id());
				// 自定义扩展数据
				message.setExdata("");
				//业务类型
				message.setSvrtype("");

				// 返回的平台流水编号等信息
				StringBuffer msgId = new StringBuffer();
				
				// 发送短信
				result = cHttpPost.singleSend(message, msgId);
				// result为0:成功;非0:失败
				smsRecordEntity.setMw_msgid(msgId.toString());
				smsRecordEntity.setMw_result(result);
				if(result == 0)
				{
					System.out.println("单条发送提交成功！");
					
					System.out.println(msgId.toString());

				}
				else
				{
					System.out.println("单条发送提交失败,错误码：" + result);
				}
			}
			catch (Exception e)
			{
				//异常处理
				e.printStackTrace();
			}*/
			return result;
		}
		
		/**
		 * 
		 * @description  相同内容群发
		 * @param userid  用户账号
		 * @param pwd 用户密码
		 * @param isEncryptPwd 密码是否加密   true：密码加密;false：密码不加密
		 */
		public  void batchSend(List<String[]> msgList)
		{
			/*try
			{
				// 日期格式定义
				SimpleDateFormat	sdf	= new SimpleDateFormat("MMddHHmmss");
				// 参数类
				Message message = new Message();
				
				// 实例化短信处理对象
				CHttpPost cHttpPost = new CHttpPost();
				
				// 设置账号   将 userid转成大写,以防大小写不一致
				message.setUserid(userId.toUpperCase());
				
				//判断密码是否加密。
				boolean isEncryptPwd=publicModel(masterIpAddress,ipAddress1,ipAddress2);
				//密码加密，则对密码进行加密
				if(isEncryptPwd)
				{
					// 设置时间戳
					String timestamp = sdf.format(Calendar.getInstance().getTime());
					message.setTimestamp(timestamp);
					
					// 对密码进行加密
					String encryptPwd = cHttpPost.encryptPwd(message.getUserid(),userPwd, message.getTimestamp());
					// 设置加密后的密码
					message.setPwd(encryptPwd);
					
				}else
				{
					// 设置密码
					message.setPwd(userPwd);
				}
				
				// 设置手机号码
				message.setMobile(smsRecordEntity.getSms_mobile());
				// 设置内容
				message.setContent(smsRecordEntity.getSms_content());
				// 设置扩展号
				message.setExno("");
				// 用户自定义流水编号
				message.setCustid("");
				// 自定义扩展数据
				message.setExdata("");
				//业务类型
				message.setSvrtype("");

				// 返回的平台流水编号等信息
				StringBuffer msgId = new StringBuffer();
				// 返回值
				int result = -310099;
				// 发送短信
				result = cHttpPost.batchSend(message, msgId);
				// result为0:成功;非0:失败
				if(result == 0)
				{
					System.out.println("相同内容发送提交成功！");

					System.out.println(msgId.toString());
				}
				else
				{
					System.out.println("相同内容发送提交失败,错误码：" + result);
				}
			}
			catch (Exception e)
			{
				//异常处理
				e.printStackTrace();
			}*/
		}

		//接收短信报文
		public List<RPT> getRpt(int retsize){
			//状态报告集合  本集合临时存储状态报告，需要将收到的状态报告保存在一个队列中，由另外一个线程去处理
			List<RPT> rpts=new ArrayList<RPT>();
			//处理后的密码
			String handlePwd=null;
			// 初始化返回值
			int result = -310099;
			//时间戳声明
			String timestamp=null;
			// 日期格式定义
			SimpleDateFormat	sdf	= new SimpleDateFormat("MMddHHmmss");
			
			// 发送管理类
			CHttpPost	cHttpPost	= new CHttpPost();
			boolean isEncryptPwd=publicModel(masterIpAddress,ipAddress1,ipAddress2);
			try
			{
				// 清空状态报告集合中的对象
				rpts.clear();
				// 将 userid转成大写,以防大小写不一致
				String userid = userId.toUpperCase();
				//判断密码是否加密。
				//密码加密，则对密码进行加密
				if(isEncryptPwd)
				{
					// 设置时间戳
				    timestamp = sdf.format(Calendar.getInstance().getTime());
					
					// 对密码进行加密
				    handlePwd = cHttpPost.encryptPwd(userId.toUpperCase(),userPwd, timestamp);
				}else
				{
					//不加密，不需要设置时间戳
					timestamp=null;
					//密码不加密，使用不加密的密码
					handlePwd=userPwd;
				}
				
				// 调用获取状态报告接口
				result = cHttpPost.getRpt(userid, handlePwd,timestamp,retsize,rpts);
				// 如果获取状态报告成功，并且有状态报告
				if(result != 0){
					System.out.println("获取状态报告失败，错误码为:" + result);
				}
				
			}
			catch (Exception e)
			{
				e.printStackTrace();
			}
			return rpts;
		}
		
		/**
		 * 发送短信（允许多个手机号发送，短信内容需与手机号码个数一致）
		 * @param msgList 手机号、发送内容、扩展码 
		 * @return result_mt 返回标识
		 */
		public static String sendMsgByDiffContent(List<String[]> msgList)
		{
			// 返回值
				/*int result = -310099;
				try
				{   // 日期格式定义
					SimpleDateFormat	sdf	= new SimpleDateFormat("MMddHHmmss");
					// 参数类
					Message message = new Message();
					// 实例化短信处理对象
					CHttpPost cHttpPost = new CHttpPost();
					
					// 设置账号   将 userid转成大写,以防大小写不一致
					message.setUserid(userId.toUpperCase());
					boolean isEncryptPwd=publicModel(masterIpAddress,ipAddress1,ipAddress2);
					//判断密码是否加密。
					//密码加密，则对密码进行加密
					if(isEncryptPwd)
					{
						// 设置时间戳
						String timestamp = sdf.format(Calendar.getInstance().getTime());
						message.setTimestamp(timestamp);
						
						// 对密码进行加密
						String encryptPwd = cHttpPost.encryptPwd(message.getUserid(),userPwd, message.getTimestamp());
						// 设置加密后的密码
						message.setPwd(encryptPwd);
						
					}else
					{
						// 设置密码
						message.setPwd(userPwd);
					}
					if (!msgList.isEmpty())
					{
						for (String[] msg : msgList) {
							
							Message message1 = new Message();
							message1.setUserid(message.getUserid());
							message1.setPwd(message.getPwd());
							message1.setTimestamp(message.getTimestamp());
							// 设置手机号码 此处只能设置一个手机号码
							message1.setMobile(msg[0]);
							// 设置内容
							message1.setContent(URLEncoder.encode(msg[1], "UTF-8"));
							System.out.println();
							// 设置扩展号
							message1.setExno("");
							// 用户自定义流水编号
							message1.setCustid("");
							// 自定义扩展数据
							message1.setExdata("");
							//业务类型
							message1.setSvrtype("");
							
							// 返回的平台流水编号等信息
							StringBuffer msgId = new StringBuffer();
							
							// 发送短信
							result = cHttpPost.singleSend(message1, msgId);
							
							if(result == 0)
							{
								System.out.println("单条发送提交成功！");
								
								System.out.println(msgId.toString());

							}
							else
							{
								System.out.println("单条发送提交失败,错误码：" + result);
							}
						}
						
					}
					
					// result为0:成功;非0:失败
					
				}
				catch (Exception e)
				{
					//异常处理
					e.printStackTrace();
				}
				return  result+"";*/
			// 返回值
			int result = -310099;
			// 返回的平台流水编号等信息
			StringBuffer msgId = new StringBuffer();
			try
			{
				// 日期格式定义
				SimpleDateFormat	sdf	= new SimpleDateFormat("MMddHHmmss");
				// 参数类
				Message message = new Message();
				
				// 实例化短信处理对象
				CHttpPost cHttpPost = new CHttpPost();
				
				// 设置账号   将 userid转成大写,以防大小写不一致
				message.setUserid(userId.toUpperCase());
				
				//判断密码是否加密。
				boolean isEncryptPwd=publicModel(masterIpAddress,ipAddress1,ipAddress2);
				//密码加密，则对密码进行加密
				if(isEncryptPwd)
				{
					// 设置时间戳
					String timestamp = sdf.format(Calendar.getInstance().getTime());
					message.setTimestamp(timestamp);
					
					// 对密码进行加密
					String encryptPwd = cHttpPost.encryptPwd(message.getUserid(),userPwd, message.getTimestamp());
					// 设置加密后的密码
					message.setPwd(encryptPwd);
					
				}else
				{
					// 设置密码
					message.setPwd(userPwd);
				}
				
				List<MultiMt> multiMtList = new ArrayList<MultiMt>();
				
				//短信发送
				// 手机号码、短信内容不能为空，且个数必须一致
				if (!msgList.isEmpty())
				{
						for (String[] msg : msgList) {
							MultiMt mt = new MultiMt();
							mt.setMobile(msg[0]);
							mt.setContent(msg[1]);
							mt.setExno(msg[2]);
							mt.setCustid("");
							mt.setExdata("");
							mt.setSvrtype("");
							multiMtList.add(mt);
						}
						 result = cHttpPost.multiSend(message.getUserid(), message.getPwd(),message.getTimestamp(), multiMtList, msgId);
						if(result == 0)
						{
							System.out.println("单条发送提交成功！"+msgId.toString());
							
							System.out.println(msgId.toString());

						}
						else
						{
							System.out.println("单条发送提交失败,错误码：" + result);
							msgId.append(result);
						}
				}
				
			}
			catch (Exception e)
			{
				//异常处理
				e.printStackTrace();
			}     // 自定义：手机号码或短信内容为空，不允许发送;
			
			return  msgId.toString();	
		}
		
		
		/**
		 * 发送短信（允许多个手机号发送）
		 * @param mobiles 手机号以英文逗号隔开
		 * @param content 发送内容若含有小逗号，整条内容GB2312编码后，以英文逗号隔开
		 * @param extNum  扩展码可以为空，单个，或多个（以英文逗号隔开，个数和手机号个数一致）
		 * @return result_mt 返回标识
		 */
		public static String sendMsgBySameContent(String mobiles, String content, String extNum)
		{
			// 返回值
			int result = -310099;
			// 返回的平台流水编号等信息
			StringBuffer msgId = new StringBuffer();
			try
			{
				// 日期格式定义
				SimpleDateFormat	sdf	= new SimpleDateFormat("MMddHHmmss");
				// 参数类
				Message message = new Message();
				
				// 实例化短信处理对象
				CHttpPost cHttpPost = new CHttpPost();
				
				// 设置账号   将 userid转成大写,以防大小写不一致
				message.setUserid(userId.toUpperCase());
				
				//判断密码是否加密。
				boolean isEncryptPwd=publicModel(masterIpAddress,ipAddress1,ipAddress2);
				//密码加密，则对密码进行加密
				if(isEncryptPwd)
				{
					// 设置时间戳
					String timestamp = sdf.format(Calendar.getInstance().getTime());
					message.setTimestamp(timestamp);
					
					// 对密码进行加密
					String encryptPwd = cHttpPost.encryptPwd(message.getUserid(),userPwd, message.getTimestamp());
					// 设置加密后的密码
					message.setPwd(encryptPwd);
					
				}else
				{
					// 设置密码
					message.setPwd(userPwd);
				}
				//我们的Demo最后是拼成xml了，所以要按照xml的语法来转义
				if(content.indexOf("&")>=0) {
					content = content.replace("&","&amp;");
				}
				
				if(content.indexOf("<")>=0) {
					
					content = content.replace("<","&lt;");
					
				}
				if(content.indexOf(">")>=0) {
					content = content.replace(">","&gt;");
				}
				// 设置手机号码
				message.setMobile(mobiles);
				// 设置内容
				message.setContent(content);
				// 设置扩展号
				message.setExno("");
				// 用户自定义流水编号
				message.setCustid("");
				// 自定义扩展数据
				message.setExdata("");
				//业务类型
				message.setSvrtype("");

				result = cHttpPost.batchSend(message, msgId);
				// result为0:成功;非0:失败
				if(result == 0)
				{
					System.out.println("相同内容发送提交成功！"+msgId.toString());

				}
				else
				{
					System.out.println("相同内容发送提交失败,错误码：" + result);
					msgId.append(result);
				}
				
			}catch (Exception e) {//异常处理
				e.printStackTrace();// TODO: handle exception
			}
			
			return msgId.toString();         // 自定义：手机号码或短信内容为空，不允许发送;
		}
		
		private static boolean publicModel(String masterIpAddress,String ipAddress1,String ipAddress2){
			//主IP信息，请前往您的控制台获取请求域名(IP)或联系梦网客服进行获取
			String ipAddress=masterIpAddress;
			//备IP1  选填
			String ip1=ipAddress1;
			//备IP2  选填
			String ip2=ipAddress2;
			//备IP3  选填
			String ip3=null;
			//设置IP
			ConfigManager.setIpInfo(ipAddress, ip1, ip2, ip3);
			// 设置从连接池获取连接超时时间30秒
			ConfigManager.setConnectionRequestTimeout(30 * 1000);
			// 设置连接超时时间20秒
			ConfigManager.setConnectTimeout(20 * 1000);
			// 设置响应超时时间60秒
			ConfigManager.setSocketTimeout(60 * 1000);
			// 设置连接最大数20和每个ip连接最大数10
			ConfigManager.setMaxTotal(20, 10);
			//设置超时时间15秒
			ConfigManager.setClearTime(15);
			//密码是否加密   true：密码加密;false：密码不加密
			ConfigManager.IS_ENCRYPT_PWD=true;
			
			return ConfigManager.IS_ENCRYPT_PWD;
		}
	
		/**
		 * @description 获取上行接口
		 * @param CHttpPost 短信发送对象
		 * @return void
		 * 需要输入信息
		 * 1.获取上行最大条数
		 * */
		public List<MO> getMo(int size){
			
			// 实例化短信处理对象
			CHttpPost cHttpPost = new CHttpPost();
			Message message = setUser(cHttpPost);
			// 定义上行对象集合
			List<MO> moList = new ArrayList<MO>();
			try
			{
				// 调用获取上行接口
				int result = cHttpPost.getMo(message.getUserid(), message.getPwd(),message.getTimestamp(), Integer.valueOf(size), moList);
				// result调用接口成功打印结果
				if(result != 0){
					System.out.println("获取上行失败，错误码为:" + result);
				}else {
					System.out.println("获取上行成功！获取到的上行有" + moList.size() + "条记录。");
				}
			}catch (Exception e)
			{
				e.printStackTrace();
			}
			return moList;
		}
		private Message setUser(CHttpPost cHttpPost) {
			// 日期格式定义
			SimpleDateFormat	sdf	= new SimpleDateFormat("MMddHHmmss");
			
			
			// 参数类
			Message message = new Message();
			// 设置账号   将 userid转成大写,以防大小写不一致
			message.setUserid(userId.toUpperCase());
			
			//判断密码是否加密。
			boolean isEncryptPwd=publicModel(masterIpAddress,ipAddress1,ipAddress2);
			//密码加密，则对密码进行加密
			if(isEncryptPwd)
			{
				// 设置时间戳
				String timestamp = sdf.format(Calendar.getInstance().getTime());
				message.setTimestamp(timestamp);
				
				// 对密码进行加密
				String encryptPwd = cHttpPost.encryptPwd(message.getUserid(),userPwd, message.getTimestamp());
				// 设置加密后的密码
				message.setPwd(encryptPwd);
				
			}else
			{
				// 设置密码
				message.setPwd(userPwd);
			}
			return message;
		}
	/*//软件序列号 真实账号SDK-WSS-010-09421
	private static String SN = "SDK-WSS-010-09421";
	// 密码 真实密码 eea517-F
	private static String PWD = "eea517-F";
	
	private static SMSNewUtil smsNewUtil;

	public static SMSNewUtil getSMSNewUtil() {
		if (smsNewUtil == null) {
			smsNewUtil = new SMSNewUtil();
		}	
		return smsNewUtil;
	}
	
	*//**
	 * 发送短信（允许多个手机号发送）
	 * @param mobiles 手机号以英文逗号隔开
	 * @param content 发送内容若含有小逗号，整条内容GB2312编码后，以英文逗号隔开
	 * @param extNum  扩展码可以为空，单个，或多个（以英文逗号隔开，个数和手机号个数一致）
	 * @return result_mt 返回标识
	 *//*
	public static String sendMsgBySameContent(String mobiles, String content, String extNum)
	{
		SMSClient client = null;
		try {
			client = new SMSClient(SN, PWD);
		} catch (UnsupportedEncodingException e) {
			System.out.println("初始化发送短信基类失败！");
			e.printStackTrace();
		}
		//我们的Demo最后是拼成xml了，所以要按照xml的语法来转义
		if(content.indexOf("&")>=0) {
			content = content.replace("&","&amp;");
		}
		
		if(content.indexOf("<")>=0) {
			
			content = content.replace("<","&lt;");
			
		}
		if(content.indexOf(">")>=0) {
			content = content.replace(">","&gt;");
		}
		
		//短信发送
		if (!mobiles.isEmpty() && !content.isEmpty())
		{
			String result_mt = client.mt(mobiles, content, extNum, "", "");
			return result_mt;
		}
		return "-99";         // 自定义：手机号码或短信内容为空，不允许发送;
	}
	public static void main(String[] args) {
		SMSClient client = null;
		try {
			client = new SMSClient(SN, PWD);
		} catch (UnsupportedEncodingException e) {
			System.out.println("初始化发送短信基类失败！");
			e.printStackTrace();
		}
		String result_mt = client.getBalance();
		System.out.println(result_mt);
	}
	*//**
	 * 发送短信（允许多个手机号发送，短信内容需与手机号码个数一致）
	 * @param msgList 手机号、发送内容、扩展码 
	 * @return result_mt 返回标识
	 *//*
	public static String sendMsgByDiffContent(List<String[]> msgList)
	{
		SMSClient client = null;
		try {
			client = new SMSClient(SN, PWD);
		} catch (UnsupportedEncodingException e) {
			System.out.println("初始化发送短信基类失败！");
			e.printStackTrace();
		}
		
		//短信发送
		// 手机号码、短信内容不能为空，且个数必须一致
		if (!msgList.isEmpty())
		{
			String mobiles = "";   // 手机号以英文逗号隔开
			String contents = "";  // 以英文逗号隔开，个数和手机号个数一致
			String extNums = "";   // 扩展码可以为空，单个，或多个（以英文逗号隔开，个数和手机号个数一致）
			
			try {
				for (String[] msg : msgList) {
					mobiles += msg[0] + ",";
					contents += URLEncoder.encode(msg[1], "GB2312") + ",";
					if (null != msg[2] && !msg[2].isEmpty())
					{
						extNums += msg[2] + ",";
					}
				}
			} catch (UnsupportedEncodingException e) {
				e.printStackTrace();
			}
			mobiles = mobiles.substring(0, mobiles.length() - 1);
			contents = contents.substring(0, contents.length() - 1);
			extNums = extNums.isEmpty() ? "" : extNums.substring(0, extNums.length() - 1);
			
			String result_mt = client.gxmt(mobiles, contents, extNums, "", "");
			return result_mt;
		}
		return "-99";         // 自定义：手机号码或短信内容为空，不允许发送;
	}
	
	*//**
	 * 接收短信
	 * @return msgList 短信数组
	 *//*
	public static List<String> reviceMsg()
	{
		List<String> msgList = null;
		SMSClient client = null;
		try {
			client = new SMSClient(SN, PWD);
		} catch (UnsupportedEncodingException e) {
			System.out.println("初始化发送短信基类失败！");
			e.printStackTrace();
		}
		
		//接收短信
		String result_mo = client.mo();
		if (!result_mo.startsWith("-") && !"1".equals(result_mo)) {
			msgList = new ArrayList<String>();
			//多条信息的情况，以回车换行分割
			String[] result = result_mo.split("\r\n");
			try {
				for(int i=0;i<result.length;i++)
				{
					//内容做了url编码，在此解码，编码方式gb2312
					msgList.add(URLDecoder.decode(result[i], "gb2312"));
				}
			} catch (UnsupportedEncodingException e) {
				// 接受短信内容解码失败！
				e.printStackTrace();
			}
		}
		return msgList;
	}*/
	
}

