package com.hzw.sunflower.controller.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date ：Created in 2022/8/2 8:54
 * @description：回单打印入参
 * @modified By：`
 * @version: 1.0
 */
@ApiModel(description = "回单打印入参")
@Data
public class BondPrintReq implements Serializable {

    @ApiModelProperty(value = "文件集合，以逗号隔开")
    private String ossFileIds;

}
