// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'BulletinListItemModel.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

BulletinListItemModel _$BulletinListItemModelFromJson(
    Map<String, dynamic> json) {
  return BulletinListItemModel(
    bidOpenTime: json['BidOpenTime'] as String,
    bidSectionID: json['bidSectionId'] as String,
    bulletinID: json['bulletinID'] as String,
    bulletinName: json['BulletinName'] as String,
    bulletinTypeName: json['BulletinTypeName'] as String,
    classifyName: json['ClassifyName'] as String,
    currentPageCursor: json['CurrentPage'] as String,
    docGetStartTime: json['docGetStartTime'] as String,
    docReferEndTime: json['docReferEndTime'] as String,
    isFav: json['IsFav'] as bool,
    noticeSendTime: json['NoticeSendTime'] as String,
    platformCode: json['platformcode'] as String,
    platformName: json['platformName'] as String,
    projectID: json['ProjectId'] as String,
    regionCode: json['RegionCode'] as String,
    regionName: json['regionName'] as String,
    ruleSpecial: json['RuleSpecial'] as String,
    uuid: json['UUID'] as String,
    viewCountOnPC: json['ViewPcCount'] as int,
    viewCountOnWeAPP: json['ViewMiniCount'] as int,
    webviewURL: json['realUrl'] as String,
    webviewURL_2: json['URL'] as String,
  );
}

Map<String, dynamic> _$BulletinListItemModelToJson(
        BulletinListItemModel instance) =>
    <String, dynamic>{
      'BidOpenTime': instance.bidOpenTime,
      'bidSectionId': instance.bidSectionID,
      'bulletinID': instance.bulletinID,
      'BulletinName': instance.bulletinName,
      'BulletinTypeName': instance.bulletinTypeName,
      'ClassifyName': instance.classifyName,
      'CurrentPage': instance.currentPageCursor,
      'docGetStartTime': instance.docGetStartTime,
      'docReferEndTime': instance.docReferEndTime,
      'IsFav': instance.isFav,
      'NoticeSendTime': instance.noticeSendTime,
      'platformcode': instance.platformCode,
      'platformName': instance.platformName,
      'ProjectId': instance.projectID,
      'RegionCode': instance.regionCode,
      'regionName': instance.regionName,
      'RuleSpecial': instance.ruleSpecial,
      'UUID': instance.uuid,
      'ViewPcCount': instance.viewCountOnPC,
      'ViewMiniCount': instance.viewCountOnWeAPP,
      'realUrl': instance.webviewURL,
      'URL': instance.webviewURL_2,
    };
