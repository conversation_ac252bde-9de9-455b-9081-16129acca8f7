package com.hzw.ssm.util;
import java.awt.Color;
import java.awt.Font;
import java.awt.Graphics;
import java.awt.Graphics2D;
import java.awt.Image;
import java.awt.Transparency;
import java.awt.font.FontRenderContext;
import java.awt.geom.Rectangle2D;
import java.awt.image.BufferedImage;
import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;

import javax.imageio.ImageIO;
import javax.swing.ImageIcon;

import com.hzw.ssm.applets.utils.SmallWxUtil;
import com.sun.image.codec.jpeg.ImageFormatException;
import com.sun.image.codec.jpeg.JPEGCodec;
public class PictureUtil {
	private Graphics2D g        = null;  
    
    /**  
     * 导入本地图片到缓冲区  
     */  
    public BufferedImage loadImageLocal(String imgName) {  
        try {  
            return ImageIO.read(new File(imgName));  
        } catch (IOException e) {  
            System.out.println(e.getMessage());  
        }  
        return null;  
    } 
    
    public BufferedImage modifyImagetogeter(BufferedImage b, BufferedImage d,BufferedImage c) {  
        
        try {  
            int w = b.getWidth();  
            int h = b.getHeight();       
            g = d.createGraphics();  
            
            g.drawImage(b, 30, 195, w, h, null);
            
            g.drawImage(c, 87, 260, 120, 120, null);  
            g.dispose();  
        } catch (Exception e) {  
            System.out.println(e.getMessage());  
        }  
  
        return d;  
    } 
    
    /**  
     * 生成新图片到本地  
     */  
    public void writeImageLocal(String newImage, BufferedImage img) {  
        if (newImage != null && img != null) {  
            try {  
                File outputfile = new File(newImage); 
               ImageIO.setUseCache(false);
                ImageIO.write(img, "png", outputfile);  
            } catch (IOException e) {  
                System.out.println(e.getMessage());  
            }  
        }  
    } 
    
   public BufferedImage addPicture(String path, String code) {
	 try {
		 BufferedImage bi = new BufferedImage(220, 32, BufferedImage.TYPE_INT_RGB);// INT精确度达到一定,RGB三原色，高度70,宽度150  // 得到图片缓冲区
		 
	        Graphics2D graphics = (Graphics2D) bi.getGraphics();   // 得到它的绘制环境(这张图片的笔)
	        bi = graphics.getDeviceConfiguration().createCompatibleImage(220, 32, Transparency.TRANSLUCENT);
	        graphics.dispose();
	        graphics = (Graphics2D)bi.getGraphics();
	        graphics.setColor(new Color(83,111,236));    
	        graphics.fillRect(0, 0, 220, 32); 
	        graphics.setColor(Color.WHITE); 
	        graphics.setFont(new Font("宋体", Font.BOLD, 25));           
	        graphics.drawString("推荐码：", 5, 25);
	        graphics.setFont(new Font("宋体", Font.BOLD, 25));           
	        graphics.drawString(code, 120, 25);
	        
	        return bi;
//	        FileOutputStream fos = new FileOutputStream("E:\\6\\333\\14.jpg");  
//	        BufferedOutputStream bos = new BufferedOutputStream(fos);  
//	        JPEGImageEncoder encoder = JPEGCodec.createJPEGEncoder(bos);  
//	        encoder.encode(bi);  
//	        bos.close();  
//	        fos.close();
	} catch (Exception e) {
		// TODO Auto-generated catch block
		e.printStackTrace();
	} 
	   return null;
   }
    
    public static void main(String[] args) {  
    	
    	
    	try {
			PictureUtil tt = new PictureUtil();  
			SmallWxUtil small = new SmallWxUtil();
			BufferedImage c= small.wechatAppletCode("1234");
			BufferedImage d =   tt.loadImageLocal("E:\\6\\333\\11.png");
			BufferedImage b = tt.addPicture("","1234"); 
			//BufferedImage c = tt.loadImageLocal("E:\\6\\333\\5.png");
			tt.writeImageLocal("E:\\6\\333\\81.png", tt.modifyImagetogeter(b, d,c));    
			//将多张图片合在一起    
			System.out.println("success");
		} catch (Exception e) {
			e.printStackTrace();
		}
    } 

}
