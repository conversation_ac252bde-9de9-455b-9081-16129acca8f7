package com.hzw.sunflower.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode
public class AppingTaskNoticeDTO {
    /**
     * 公告ID
     */
    @ApiModelProperty(value = "公告ID")
    private Long noticeId;

    @ApiModelProperty(value = "审批内容")
    private String formName;

    /**
     * 关联标段
     */
    @ApiModelProperty(value = "项目ID")
    private Long projectId;
    /**
     * 项目编名称
     */
    @ApiModelProperty(value = "项目编名称")
    private String projectName;

    /**
     * 项目编号
     */
    @ApiModelProperty(value = "项目编号")
    private String projectNum;

    /**
     * 关联标段
     */
    @ApiModelProperty(value = "关联标段")
    private String projectBidSections;
    /**
     * 阶段： 0：默认无阶段； 1：第一轮； 2：第二轮
     */
    @ApiModelProperty(value = "阶段", required = true)
    private Integer bidRound;

    @ApiModelProperty(value = "包件状态（0-无包，1-有包）")
    private Integer packageSegmentStatus;
}
