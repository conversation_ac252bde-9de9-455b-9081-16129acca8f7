<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzw.sunflower.dao.BondAgentAccountMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hzw.sunflower.entity.BondAgentAccount">
        <id column="id" property="id" />
        <result column="agent_acount" property="agentAcount" />
        <result column="agent_name" property="agentName" />
        <result column="agent_bank_code" property="agentBankCode" />
        <result column="agent_bank_name" property="agentBankName" />
        <result column="agent_bank_addr" property="agentBankAddr" />
        <result column="bank_type" property="bankType" />
        <result column="created_user_id" property="createdUserId" />
        <result column="created_time" property="createdTime" />
        <result column="updated_user_id" property="updatedUserId" />
        <result column="updated_time" property="updatedTime" />
        <result column="is_delete" property="isDelete" />
        <result column="remark" property="remark" />
        <result column="version" property="version" />
    </resultMap>

</mapper>
