///***
/// * 数据加载提示组件，统一封装处理
/// */

import 'package:flutter/material.dart';

import 'package:flutter_main_app/src/components/CustomSafeArea.dart';

class CustomCircularProgressIndicator extends StatelessWidget {

  const CustomCircularProgressIndicator({Key key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return CustomSafeArea(
      child: Center(
        child: CircularProgressIndicator(),
      ),
    );
  }

}
