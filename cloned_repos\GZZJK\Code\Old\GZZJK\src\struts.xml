<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE struts PUBLIC "-//Apache Software Foundation//DTD Struts Configuration 2.1.7//EN" "struts-2.1.7.dtd" >
<struts>
	<!-- 加载struts2的DMI(动态调用) -->
	<constant name="struts.enable.DynamicMethodInvocation" value="true" />
	<!--修改上文件的默认大小 -->
	<constant name="struts.multipart.maxSize" value="20480000" />
	<constant name="struts.action.excludePattern" value="/services.*" />
	<!-- 继承默认的 struts2 配置文件 -->
	<package name="default" extends="struts-default">

		<!-- 将分页拦截器栈设置默认的拦截器 <default-interceptor-ref name="pageInterceptorStack"></default-interceptor-ref> -->
		<interceptors>
			<interceptor name="SysInterceptor"
				class="com.hzw.ssm.fw.listener.action.LoginInterceptors" />
			<interceptor name="TokenInterceptor"
				class="com.hzw.ssm.util.http.token.TokenInterceptor" />
			<interceptor name="UrlInterceptor" class="com.hzw.ssm.fw.listener.action.UrlInterceptor"/>
			<interceptor-stack name="crbtStack">
				<interceptor-ref name="defaultStack" />
				<interceptor-ref name="SysInterceptor">
					<param name="excludeMethods">access,access1,sendSmsCode,enter,enter1,wxSmallLogin,expertImages,innerEnter,logout,index,download,uploadFiles,downLoadFile,queryMajorList,initMessage,deleteFiles,queryByProjectPoint</param>
				</interceptor-ref>
				<interceptor-ref name="TokenInterceptor">
					<param name="excludeMethods">access,access1,sendSmsCode,enter,enter1,wxSmallLogin,expertImages,innerEnter,logout,index,download,uploadFiles,downLoadFile,queryMajorList,initMessage,deleteFiles,queryByProjectPoint,findMenu,findSpecialty</param>
				</interceptor-ref>
				<interceptor-ref name="UrlInterceptor">
					<param name="excludeMethods">access,access1,sendSmsCode,enter,enter1,wxSmallLogin,expertImages,innerEnter,logout,index,download,uploadFiles,downLoadFile,queryMajorList,initMessage,deleteFiles,queryByProjectPoint,findMenu,findSpecialty</param>
				</interceptor-ref>
			</interceptor-stack>

		</interceptors>
		<default-interceptor-ref name="crbtStack" />
		<!-- 全局错误页面 -->
		<global-results>
			<result name="uv">/jsp/ultra_vires.jsp</result>
			<result name="has">/jsp/wrong.jsp</result>
			<result name="login">/jsp/error.jsp</result>
			<result name="warning">/jsp/warning.jsp</result>
			<result name="exception">/error/systemError.jsp</result>
			<result name="person">/jsp/person.jsp</result>
		</global-results>
		<global-exception-mappings>
			<exception-mapping result="exception" exception="java.lang.Exception" />
		</global-exception-mappings>
	</package>
</struts>
