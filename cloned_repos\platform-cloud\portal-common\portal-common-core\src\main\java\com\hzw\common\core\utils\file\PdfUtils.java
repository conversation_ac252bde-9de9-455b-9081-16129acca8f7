/*
 * 公司名称：江苏华招网信息技术有限公司
 * 版权信息：江苏华招网信息技术有限公司版权所有
 * 文件名称：PdfUtils.java
 * 修改时间：2018年5月22日
 * 修改人：朱加健
 * 修改内容：
 * 跟踪单号：
 * 修改单号 ：
 */

package com.hzw.common.core.utils.file;

import com.hzw.common.core.config.SystemPathConfig;
import com.itextpdf.html2pdf.ConverterProperties;
import com.itextpdf.html2pdf.HtmlConverter;

import com.itextpdf.html2pdf.resolver.font.DefaultFontProvider;
import com.itextpdf.kernel.pdf.PdfAConformanceLevel;
import com.itextpdf.kernel.pdf.PdfOutputIntent;
import com.itextpdf.kernel.pdf.PdfVersion;
import com.itextpdf.kernel.pdf.WriterProperties;
import com.itextpdf.pdfa.PdfADocument;
import com.itextpdf.text.*;
import com.itextpdf.text.pdf.*;
import com.itextpdf.tool.xml.XMLWorkerHelper;
import org.apache.poi.poifs.filesystem.DirectoryEntry;
import org.apache.poi.poifs.filesystem.DocumentEntry;
import org.apache.poi.poifs.filesystem.POIFSFileSystem;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.MalformedURLException;
import java.net.URL;
import java.nio.charset.Charset;

import org.apache.poi.xwpf.converter.pdf.PdfConverter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;

/**
 * <一句话功能简述> PDF文件工具类
 * <功能详细描述>
 * <AUTHOR>
 * @version V1.0[产品/模块版本]
 */
public class PdfUtils {
    /**
     * 主机的样式文件所在的路径
     */
    public static String prefixFont ;

    static {
        //根据系统自动匹配默认路径
        String os = System.getProperties().getProperty("os.name");
        if(os.startsWith("win") || os.startsWith("Win")){
            //TODO 说明：如果主机的系统不是安装在C盘，则需要修改此处的路径
            prefixFont = "C:\\Windows\\Fonts" + File.separator;
        }else {
            prefixFont = "/usr/share/fonts/chinese" + File.separator;
        }
    }


    /**
     * 函数功能描述：将读取到的HTML文件内容转换成PDF文件
     * @param content String html文件内容
     * @param encode String 编码格式（默认UTF-8）
     * @throws Exception
     */
    public static void htmlToPdf(String tempFilePath,String content, String encode, String footerContent)
            throws Exception {
        if (null == encode || "".equals(encode)) {
            //默认UTF-8格式
            encode = "UTF-8";
        }
        tempFilePath = FileUtils.replaceSeparator(tempFilePath);
        //创建“文档”，默认A4纸大小（纵向版式），左、右、上边距30，下边距70（PageSize.A4.rotate()表示横向版式）
        Document document = new Document(PageSize.A4,30,30,30,70);
        FileOutputStream fileOutputStream = new FileOutputStream(tempFilePath);
        //加载pdf文件
        PdfWriter writer = PdfWriter.getInstance(document,fileOutputStream);
        writer.setViewerPreferences(PdfWriter.PageLayoutOneColumn);
        // 为这篇文档设置页面事件(X/Y)
        writer.setPageEvent(new PageEvent());

        BaseFont baseFont1 = BaseFont.createFont(PdfUtils.prefixFont + "simsun.ttc,0",
                BaseFont.IDENTITY_H, BaseFont.NOT_EMBEDDED);
        Font songti09 = new Font(baseFont1, 9f); //宋体 小五
        //“打开”文档
        document.open();
        document.newPage();
        PdfPTable pdfPTable = new PdfPTable(1);
        //为文档添加页脚，事件的发生是在生成文档之后，写入到硬盘之前
        Footer footerTable = new Footer(pdfPTable);
        footerTable.setTableFooter(writer, songti09, footerContent);
        document.add(pdfPTable);
        XMLWorkerHelper.getInstance().parseXHtml(writer, document,
                new ByteArrayInputStream(content.getBytes(encode)), Charset.forName(encode));
        //关闭“文档”
        document.close();
        writer.close();
        fileOutputStream.close();
    }



    //    public static final String RESOURCES_SOURCE_FOLDER = "portal-common/portal-common-core/src/main/resources/fonts/";
//    public static final String SOURCE_FOLDER = "portal-common/portal-common-core/src/main/resources/HtmlConverterPdfA4Test/";
    public static void htmlToPdf2(String destinationPdf,String sourceHtml)
            throws Exception,IOException {
        ApplicationContext context = new AnnotationConfigApplicationContext(SystemPathConfig.class);
        SystemPathConfig config = context.getBean(SystemPathConfig.class);
        ConverterProperties properties = new ConverterProperties();
        DefaultFontProvider fontProvider = new DefaultFontProvider(false, false, false);
        fontProvider.addFont(config.getTemplatePath() + "NotoSansSC-Regular.ttf");
        properties.setFontProvider(fontProvider);
        com.itextpdf.kernel.pdf.PdfWriter writer = new com.itextpdf.kernel.pdf.PdfWriter(destinationPdf,
                new WriterProperties()
                        .setPdfVersion(PdfVersion.PDF_2_0));
        PdfADocument pdfDocument = new PdfADocument(writer, PdfAConformanceLevel.PDF_A_4E,
                new PdfOutputIntent("Custom", "", "http://www.color.org", "sRGB IEC61966-2.1",
                        new FileInputStream(config.getTemplatePath()+"sRGBColorSpaceProfile.icm")));

        try (FileInputStream fileInputStream = new FileInputStream(sourceHtml)) {
            HtmlConverter.convertToPdf(fileInputStream, pdfDocument, properties);
        }

//        compareAndCheckCompliance(destinationPdf, cmpPdf);


    }
//    protected static void compareAndCheckCompliance(String destinationPdf, String cmpPdf) throws IOException, InterruptedException {
//        Assert.assertNull(new CompareTool().compareByContent(destinationPdf, cmpPdf, DESTINATION_FOLDER,
//                "diff_simple_"));
//        VeraPdfValidator veraPdfValidator = new VeraPdfValidator();
//        Assert.assertNull(veraPdfValidator.validate(destinationPdf));
//    }


    /**
     * 加水印
     * @param oldPath
     * @param newPath
     * @throws Exception
     */
    public static void addwaterName(String oldPath, String newPath,String waterName)
            throws Exception {
        BaseFont bfChinese = BaseFont.createFont(prefixFont +"simsun.ttc,0", BaseFont.IDENTITY_H, BaseFont.NOT_EMBEDDED);
        Font font1 = new Font(bfChinese, 34, Font.NORMAL, new GrayColor(0.5f));
        Phrase p = new Phrase(waterName,font1);

        File file = new File(oldPath);
        InputStream is = new FileInputStream(file);
        PdfReader reader = new PdfReader(is);
        PdfStamper stamper = new PdfStamper(reader, new FileOutputStream(newPath));
        int n = reader.getNumberOfPages();
        PdfContentByte over;
        for (int i = 1; i <= n; i++) {
            over = stamper.getUnderContent(i);
            over.saveState();
            // set transparency
            PdfGState state = new PdfGState();
            state.setFillOpacity(0.2f);
            over.setGState(state);
//                ColumnText.showTextAligned(over, Element.ALIGN_CENTER, p, x, y, 0);
            //控制水印的显示效果，可以自己调整看看
            float beginPositionX = 50, beginPositionY = 0, distance = 180;
            for (int i2 = 0; i2 < 1; i2++) {
                for (int j = 0; j < 4; j++) {
                    ColumnText.showTextAligned(over, Element.ALIGN_LEFT,
                            p, beginPositionX + distance*i2, beginPositionY + distance*j, 30);
                }
            }
            over.restoreState();
        }
        stamper.close();
        reader.close();
        is.close();
    }


    public static void exportWord(HttpServletRequest request, HttpServletResponse response, String content, String fileName) throws Exception {
        byte[] b = content.getBytes("GBK"); //这里是必须要设置编码的，不然导出中文就会乱码。
        ByteArrayInputStream bais = new ByteArrayInputStream(b);//将字节数组包装到流中
        POIFSFileSystem poifs = new POIFSFileSystem();
        DirectoryEntry directory = poifs.getRoot();
        DocumentEntry documentEntry = directory.createDocument("WordDocument", bais); //该步骤不可省略，否则会出现乱码。
        //输出文件
        request.setCharacterEncoding("utf-8");
        response.setContentType("application/msword");//导出word格式
        response.addHeader("Content-Disposition", "attachment;filename=" +
                new String(fileName.getBytes("GB2312"),"iso8859-1") + ".doc");
        ServletOutputStream ostream = response.getOutputStream();
        poifs.writeFilesystem(ostream);
        bais.close();
        ostream.close();
        poifs.close();
    }


    /**
     * 函数功能描述：读取指定位置文件的内容
     * @param path String 文件路径
     * @param path String 编码格式
     * @return content String 文件内容
     * @throws IOException
     */
    public static String readContent(String path, String encode) throws IOException {
        //根据系统环境自动匹配文件路径分隔符
        path = FileUtils.replaceSeparator(path);

        @SuppressWarnings("resource")
        BufferedReader reader = new BufferedReader(
                new InputStreamReader(new FileInputStream(path), encode));

        String content = "";
        String line = "";
        while ( (line = reader.readLine()) != null) {
            content += line;
        }

        return content;
    }


    /**
     * <一句话功能简述> 内部类，实现页码事件
     * <功能详细描述>
     * <AUTHOR>
     * @version V1.0[产品/模块版本]
     */
    public static class PageEvent extends PdfPageEventHelper {
        /** The PdfTemplate that contains the total number of pages. */
        public PdfTemplate total;

        public BaseFont bfChinese;

        /**
         * 重写PdfPageEventHelper中的onOpenDocument方法
         */
        @Override
        public void onOpenDocument(PdfWriter writer, Document document) {
            // 得到文档的内容并为该内容新建一个模板
            total = writer.getDirectContent().createTemplate(500, 500);
            try {
                // 设置字体对象为Windows系统默认的字体
                bfChinese = BaseFont.createFont(prefixFont + "simsun.ttc,0",
                        BaseFont.IDENTITY_H, BaseFont.NOT_EMBEDDED);
            } catch (Exception e) {
                throw new ExceptionConverter(e);
            }
        }

        /**
         * 重写PdfPageEventHelper中的onEndPage方法
         */
        @Override
        public void onEndPage(PdfWriter writer, Document document) {
            // 新建获得用户页面文本和图片内容位置的对象
            PdfContentByte pdfContentByte = writer.getDirectContent();
            // 保存图形状态
            pdfContentByte.saveState();
            String text = writer.getPageNumber() + "/";
            // 获取点字符串的宽度
            float textSize = bfChinese.getWidthPoint(text, 9);
            pdfContentByte.beginText();
            // 设置随后的文本内容写作的字体和字号
            pdfContentByte.setFontAndSize(bfChinese, 9);

            // 定位'X/'
            float x = (document.right() + document.left()) / 2;
            float y = 56f;
            pdfContentByte.setTextMatrix(x, y);
            pdfContentByte.showText(text);
            pdfContentByte.endText();

            // 将模板加入到内容（content）中- // 定位'Y'
            pdfContentByte.addTemplate(total, x + textSize, y);

            pdfContentByte.restoreState();
        }

        /**
         * 重写PdfPageEventHelper中的onCloseDocument方法
         */
        @Override
        public void onCloseDocument(PdfWriter writer, Document document) {
            total.beginText();
            try {
                bfChinese = BaseFont.createFont(prefixFont + "simsun.ttc,0",
                        BaseFont.IDENTITY_H, BaseFont.NOT_EMBEDDED);
                total.setFontAndSize(bfChinese, 9);
            } catch (DocumentException e) {
                e.printStackTrace();
            } catch (IOException e) {
                e.printStackTrace();
            }

            total.setTextMatrix(0, 0);
            // 设置总页数的值到模板上，并应用到每个界面
            total.showText(String.valueOf(writer.getPageNumber()));
            total.endText();
        }
    }



    //页眉事件
    @SuppressWarnings("unused")
    private static class Header extends PdfPageEventHelper {
        public static PdfPTable header;

        public Header(PdfPTable header) {
            Header.header = header;
        }

        @Override
        public void onEndPage(PdfWriter writer, Document document) {
            //把页眉表格定位
            header.writeSelectedRows(0, -1, 36, 806, writer.getDirectContent());
        }


        //页脚事件
        private static class Footer extends PdfPageEventHelper {
            public static PdfPTable footer;

            @SuppressWarnings("static-access")
            public Footer(PdfPTable footer) {
                this.footer = footer;
            }

            @Override
            public void onEndPage(PdfWriter writer, Document document) {
                //把页脚表格定位
                footer.writeSelectedRows(0, -1, 38, 50, writer.getDirectContent());
            }


            /**
             * 页脚是文字
             *
             * @param writer
             * @param songti09
             */
            public void setTableFooter(PdfWriter writer, Font songti09, String footerContent) {
                //添加页脚文字
                Paragraph p = new Paragraph(footerContent, songti09);

                //实例化一个PDF表格
                PdfPTable table = new PdfPTable(1);
                //设置页脚表格宽度
                table.setTotalWidth(520);
                //实例化一个PDF单元格
                PdfPCell cell = new PdfPCell();
                //显示横线
                cell.setBorder(1);
                cell.setPaddingLeft(380f);
                cell.setPaddingTop(-2f);
                cell.addElement(p);
                table.addCell(cell);
                Footer event = new Footer(table);
                writer.setPageEvent(event);
            }
        }
    }


    //页脚事件
    private static class Footer extends PdfPageEventHelper {
        public static PdfPTable footer;

        @SuppressWarnings("static-access")
        public Footer(PdfPTable footer) {
            this.footer = footer;
        }

        @Override
        public void onEndPage(PdfWriter writer, Document document) {
            //把页脚表格定位
            footer.writeSelectedRows(0, -1, 38, 50, writer.getDirectContent());
        }

        /**
         * 页脚是图片
         * @param writer
         * @throws MalformedURLException
         * @throws IOException
         * @throws DocumentException
         */
        @SuppressWarnings("unused")
        public void setTableFooter(PdfWriter writer)
                throws MalformedURLException, IOException, DocumentException {
            //页脚水印
        	/*
            PdfPTable table = new PdfPTable(1);
            table.setTotalWidth(523);
            PdfPCell cell = new PdfPCell();
            cell.setBorder(1);
            Image image01;
            image01 = Image.getInstance(imageAddress + "E:\\MyWorkspace\\Test\\excel.png"); //图片自己传
            image01.scaleAbsoluteWidth(523);
            image01.scaleAbsoluteHeight(30f);
            image01.setWidthPercentage(100);
            cell.addElement(image01);
            table.addCell(cell);
            Footer event = new Footer(table);
            writer.setPageEvent(event);*/
        }

        /**
         * 页脚是文字
         * @param writer
         * @param songti09
         */
        public void setTableFooter(PdfWriter writer, Font songti09,String footerContent) {
            //添加页脚文字
            Paragraph p = new Paragraph(footerContent, songti09);

            //实例化一个PDF表格
            PdfPTable table = new PdfPTable(1);
            //设置页脚表格宽度
            table.setTotalWidth(520);
            //实例化一个PDF单元格
            PdfPCell cell = new PdfPCell();
            //显示横线
            cell.setBorder(1);
            cell.setPaddingLeft(380f);
            cell.setPaddingTop(-2f);
            cell.addElement(p);
            table.addCell(cell);
            Footer event = new Footer(table);
            writer.setPageEvent(event);
        }
    }

}
