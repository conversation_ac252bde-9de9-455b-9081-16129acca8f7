package com.hzw.sunflower.service.impl.login;

import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.security.core.GrantedAuthority;

/**
 * 权限类型，负责存储权限和角色
 */
@Data
@NoArgsConstructor
public class GrantedAuthorityImpl implements GrantedAuthority {

    private String authority;


    public GrantedAuthorityImpl(String authority) {
        this.authority = authority;
    }

    @Override
    public String getAuthority() {
        return this.authority;
    }

    @Override
    public String toString() {
        return authority;
    }
}
