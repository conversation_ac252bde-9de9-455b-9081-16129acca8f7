package com.hzw.sunflower.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hzw.sunflower.dto.PackageInfoDTO;
import com.hzw.sunflower.entity.NoticePackageR;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface NoticePackageRMapper extends BaseMapper<NoticePackageR> {

    /**
     * 查询包段-公告的简要关联包信息
    */
    List<PackageInfoDTO> queryPackageInfoByProjectIdAndRound(@Param("projectId") Long projectId,@Param("bidRound")Integer bidRound);

    List<PackageInfoDTO> queryPackageInfoByNoticeId(@Param("noticeId") Long noticeId);
    /**
     * 查询包段-公告的简要关联信息
     */
    List<PackageInfoDTO> selectNoticePackageInfoListByProjectId(@Param("projectId") Long projectId);
    /**
     * 根据招标公告ID获取其相关联的包段ID
     */
    List<Long> findNoticePackageIds(@Param("noticeId") Long noticeId);

    /**
     * 根据项目ID查询所有处于进行中和划分包段状态的包段的ID
     */
    List<Long> findAllAvailablePackageIds(@Param("projectId") Long projectId);
}
