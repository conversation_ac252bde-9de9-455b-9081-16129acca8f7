package com.elgin.spring.webservice;

import java.io.IOException;

import javax.xml.namespace.QName;

import org.apache.axis2.AxisFault;
import org.apache.axis2.addressing.EndpointReference;
import org.apache.axis2.client.Options;
import org.apache.axis2.rpc.client.RPCServiceClient;

public class RPCClient {

	public static String address2 = "http://localhost:8080/GZZJK/services/ExpertService?wsdl";

	public static void main(String[] args) throws IOException {

		testWebDemo2();

	}

	/**
	 * 函数功能描述：TODO
	 * 
	 * @param method
	 * @param params
	 * @param classes
	 * @return
	 * @throws AxisFault
	 */
	@SuppressWarnings("rawtypes")
	public static Object[] invoke(String method, Object[] params, Class[] classes) throws AxisFault {
		// 使用RPC方式调用WebService
		RPCServiceClient client = new RPCServiceClient();
		Options option = client.getOptions();

		// 指定调用的URL
		EndpointReference reference = new EndpointReference(address2);
		option.setTo(reference);

		/*
		 * 设置要调用的方法 http://ws.apache.org/axis2 为默认的（无package的情况）命名空间， 如果有包名，则为
		 * http://axis2.webservice.elgin.com ,包名倒过来即可 method为方法名称
		 */
		QName qname = new QName("http://impl.webService.ssm.hzw.com", method);

		// 调用远程方法,并指定方法参数以及返回值类型
		Object[] result = client.invokeBlocking(qname, params, classes);

		return result;

	}

	public static void testWebDemo1() throws AxisFault {
		String input = "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?>"
				+ "<root>"
				+ "   <head>"
				+ "       <platformcode>szyc</platformcode>"
				+ "      <platformkey>aaaa</platformkey>"
				+ "     <timeStamp>20171103101257483</timeStamp>"
				+ " </head>"
				+ "<body>"
				+ "   <projectName>项目AAAA</projectName>"
				+ "  <projectNum>jitc-AAAA</projectNum>"
				+ " <entrustingUnit>民生银行</entrustingUnit>"
				+ "<chargePerson>张三</chargePerson>"
				+ "<contactWay>02599663355</contactWay>"
				+ "<department>综合处</department>"
				+ "<handlder>张三</handlder>"
				+ "<agency>江苏省国际招标公司</agency>"
				+ "<openBidTime>2017-11-02 11:00:00</openBidTime>"
				+ "<openBidPlace>江苏省国际招标公司4楼</openBidPlace>"
				+ "<appointExpert>0</appointExpert>"
				+ "<remarks></remarks>"
				+ "<total>5</total>"
				+ "<zone>320100,320200,320500</zone>"
				+ "<zoneName>南京市,无锡市,苏州市</zoneName>"
				+ "<seniorNum>0</seniorNum>"
				+ "<localNum>5</localNum>"
				+ "<expertType>1025,1128,1359,157,1731,643,804,952</expertType>"
				+ "<expertTypeName>A040114消防；A080113消防设施工程；A081911森林消防工程；A090604文物安全消防；B010203防灾报警设备及消防；B011108森林消防机械；B014120消防设备；C120102消防技术</expertTypeName>"
				+ "<method>2</method>" + "</body>" + "</root>";
		Object[] result = invoke("extractExpertApply", new Object[] { input }, new Class[] { String.class });
		System.out.println(result[0]);
	}

	public static void testWebDemo2() throws AxisFault {
		String input = "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?>" + "<root>" + "<head>"
				+ "  <platformcode>szyc</platformcode>" + "<platformkey>aaaa</platformkey>"
				+ "<timeStamp>20171103101357687</timeStamp>" + "</head>" + "<body>"
				+ "   <projectName>项目AAAA</projectName>" + " <projectNum>jitc-AAAA</projectNum>"
				+ " <extSeq>20161104102105734963</extSeq>" + " </body>" + "</root>";
		Object[] result = invoke("obtainExpert", new Object[] { input }, new Class[] { String.class });
		System.out.println(result[0]);
	}
}