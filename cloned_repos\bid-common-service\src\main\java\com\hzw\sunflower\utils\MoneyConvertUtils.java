package com.hzw.sunflower.utils;

import java.math.BigDecimal;

/**
 * @ClassName: MoneyConvertUtils
 * @Auther: lijinxin
 * @Description: 金额转换工具类：将阿拉伯数字金额转换为繁体中文大写金额
 * @Date: 2024/11/15 17:04
 * @Version: v1.0
 */
public class MoneyConvertUtils {

    // 阿拉伯数字到繁体中文大写数字的映射
    private static final String[] CHINESE_NUM = {"零", "壹", "贰", "叁", "肆", "伍", "陆", "柒", "捌", "玖"};
    private static final String[] CHINESE_UNIT = {"", "拾", "佰", "仟", "万", "拾", "佰", "仟", "亿"};
    private static final String[] CHINESE_DECIMAL = {"", "角", "分"}; // 只保留两位小数

    /**
     * 将金额转换为繁体中文大写形式（入口方法，接受 BigDecimal 类型以避免精度问题）
     *
     * @param num 输入金额（BigDecimal 类型）
     * @return 中文大写金额
     */
    public static String convertToChinese(BigDecimal num) {
        if (num == null) {
            return "";
        }

        // 分割整数和小数部分
        String numStr = num.stripTrailingZeros().toPlainString();
        String integerPartStr;
        String decimalPartStr = "";

        if (numStr.contains(".")) {
            String[] parts = numStr.split("\\.");
            integerPartStr = parts[0];
            decimalPartStr = parts.length > 1 ? parts[1] : "";
        } else {
            integerPartStr = numStr;
        }

        // 处理整数部分
        String integerChinese = convertInteger(integerPartStr);

        // 处理小数部分（最多两位）
        String decimalChinese = convertDecimal(decimalPartStr);

        // 拼接结果
        if (decimalChinese.isEmpty()) {
            return integerChinese + "元整";
        } else {
            return integerChinese + "元" + decimalChinese;
        }
    }

    /**
     * 转换整数部分（字符串输入，防止精度问题）
     */
    private static String convertInteger(String integerStr) {
        char[] digits = integerStr.toCharArray();
        int length = digits.length;

        StringBuilder result = new StringBuilder();
        boolean zeroInserted = false;

        for (int i = 0; i < length; i++) {
            int digit = digits[i] - '0';
            int unitIndex = length - i - 1;

            if (digit != 0) {
                // 对于 "1" 在十位上，必须显示为 "壹"
                if (digit == 1 && unitIndex == 1 && result.length() == 0) {
                    result.append(CHINESE_NUM[1]);
                }

                result.append(CHINESE_NUM[digit]);

                // 添加单位（个、拾、佰、仟、万、亿等）
                if (unitIndex > 0) {
                    result.append(CHINESE_UNIT[unitIndex]);
                }

                zeroInserted = false;
            } else {
                // 如果不是连续零，并且不是“万”或“亿”的边界，则插入一个“零”
                if (!zeroInserted && i > 0 && (length - i) % 4 != 0) {
                    result.append(CHINESE_NUM[0]);
                    zeroInserted = true;
                }
            }
        }

        // 去除结尾多余的“零”
        if (result.length() > 1 && result.charAt(result.length() - 1) == '零') {
            result.deleteCharAt(result.length() - 1);
        }

        // 特殊情况：输入为 "0"
        if (result.length() == 0) {
            return CHINESE_NUM[0];
        }

        return result.toString();
    }

    /**
     * 转换小数部分（字符串输入，防止精度问题）
     */
    private static String convertDecimal(String decimalStr) {
        if (decimalStr == null || decimalStr.isEmpty()) {
            return "";
        }

        // 只取前两位
        if (decimalStr.length() > 2) {
            decimalStr = decimalStr.substring(0, 2);
        }

        StringBuilder result = new StringBuilder();

        for (int i = 0; i < decimalStr.length(); i++) {
            char c = decimalStr.charAt(i);
            if (c >= '0' && c <= '9') {
                int digit = c - '0';
                if (digit > 0) {
                    result.append(CHINESE_NUM[digit]).append(CHINESE_DECIMAL[i]);
                }
            }
        }

        return result.toString();
    }

    public static void main(String[] args) {
        testConvert(new BigDecimal("123456789.876"));   // 壹亿贰仟叁佰肆拾伍万陆仟柒佰捌拾玖元捌角柒分
        testConvert(new BigDecimal("123456789"));       // 壹亿贰仟叁佰肆拾伍万陆仟柒佰捌拾玖元整
        testConvert(new BigDecimal("123456.50"));       // 壹拾贰万叁仟肆佰伍拾陆元伍角整
        testConvert(new BigDecimal("0"));                 // 零元整
        testConvert(new BigDecimal("0.00"));              // 零元整
        testConvert(new BigDecimal("100000000"));        // 壹亿元整
        //testConvert(new BigDecimal("10000000000"));      // 壹佰亿元整
        //testConvert(new BigDecimal("1000000000000"));    // 壹拾万亿元整
       // testConvert(new BigDecimal("123456789.01"));     // 壹亿贰仟叁佰肆拾伍万陆仟柒佰捌拾玖元零壹分
        testConvert(new BigDecimal("400.00"));           // 肆佰元整
        testConvert(new BigDecimal("0.05"));              // 零元零伍分
        testConvert(new BigDecimal("400.00"));  // 壹拾万亿整
        testConvert(new BigDecimal("720.00"));  // 壹拾万亿整
        testConvert(new BigDecimal("721.00"));  // 壹拾万亿整
        testConvert(new BigDecimal("1"));
    }

    private static void testConvert(BigDecimal number) {
        System.out.println("原始金额: " + number);
        System.out.println("中文大写: " + convertToChinese(number));
        System.out.println("------------------------");
    }
}
