///***
/// * 主体业绩 视图模型
/// */

import 'package:flutter/material.dart';

import 'package:flutter_main_app/serviceLocator.dart';
import 'package:flutter_main_app/src/utils/ApiUtils.dart';

import 'package:flutter_main_app/src/viewModels/marketPlayer/MarketPlayerListViewModel.dart';

import 'package:flutter_main_app/src/models/marketPlayer/SubjectPerformanceModel.dart';

class SubjectPerformanceViewModel extends ChangeNotifier {

  SubjectPerformanceViewModel();

  /// * 主要用于获取当前 subjectID
  final MarketPlayerListViewModel _marketPlayerListViewModel = serviceLocator<MarketPlayerListViewModel>();

  /// * 是否正在加载数据
  bool _isLoading = false;

  bool get isLoading => _isLoading;

  set isLoading(bool isLoading) {
    _isLoading = isLoading;

    notifyListeners();
  }

  /// * 主体业绩列表
  List<SubjectPerformanceModel> _subjectPerformanceList = [];

  List<SubjectPerformanceModel> get subjectPerformanceList => _subjectPerformanceList;

  ///***
  /// * 给主体业绩赋值
  /// * 同时通知所有订阅者
  /// *
  /// * @param {List<SubjectPerformanceModel>} subjectPerformanceList
  /// */
  set subjectPerformanceList(List<SubjectPerformanceModel> subjectPerformanceList) {
    _subjectPerformanceList = subjectPerformanceList;

    notifyListeners();
  }

  ///***
  /// * 获取主体业绩列表
  /// */
  Future<void> fetchSubjectPerformanceList() async {
    isLoading = true;

    final Map<String, int> params = {
      'subjectID': _marketPlayerListViewModel.subjectID,
    };

    List<SubjectPerformanceModel> newSubjectPerformanceList =
      await ApiUtils.getSubjectPerformance(params);

    if (newSubjectPerformanceList != null && newSubjectPerformanceList.isNotEmpty) {
      /// * 数据处理
      newSubjectPerformanceList = newSubjectPerformanceList.map<SubjectPerformanceModel>(
        (performance) {
          if (performance.contractFinishDate != null && performance.contractFinishDate.length >= 10) {
            performance.contractFinishDate = performance.contractFinishDate.substring(0, 10);
          }

          return performance;
        },
      ).toList();
    }

    subjectPerformanceList = newSubjectPerformanceList;

    isLoading = false;
  }

}
