package com.hzw.sunflower.controller.response;

import com.hzw.sunflower.dto.DictionaryDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 字典表 返回实体
 *
 * <AUTHOR>
 * @version 1.0.0 2021-04-12
 */
@ApiModel(description = "字典表 ")
@Data
public class DictionaryVO extends DictionaryDTO {

    @ApiModelProperty("父类名称")
    private String parentName;
}