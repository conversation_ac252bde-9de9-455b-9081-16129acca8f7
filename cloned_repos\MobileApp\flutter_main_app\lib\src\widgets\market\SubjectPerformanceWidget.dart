///***
/// * 主体人|机构信息 - 业绩
/// */

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import 'package:flutter_main_app/serviceLocator.dart';
import 'package:flutter_main_app/src/constants/theme.dart';

import 'package:flutter_main_app/src/viewModels/marketPlayer/SubjectPerformanceViewModel.dart';

import 'package:flutter_main_app/src/models/marketPlayer/SubjectPerformanceModel.dart';

import 'package:flutter_main_app/src/components/CustomAppBar.dart';
import 'package:flutter_main_app/src/components/CustomCircularProgressIndicator.dart';
import 'package:flutter_main_app/src/components/CustomSafeArea.dart';
import 'package:flutter_main_app/src/components/CustomInfoCard.dart';
import 'package:flutter_main_app/src/components/CustomTab.dart';

class SubjectPerformanceWidget extends StatefulWidget {
  const SubjectPerformanceWidget({Key key}) : super(key: key);

  /// * 路由名称
  static const String _routeName = 'SubjectPerformance';
  static String get routeName => _routeName;

  @override
  _SubjectPerformanceWidgetState createState() =>
      _SubjectPerformanceWidgetState();
}

class _SubjectPerformanceWidgetState
    extends State<SubjectPerformanceWidget> {

  /// * 主体业绩 视图模型 单例
  final SubjectPerformanceViewModel _model = serviceLocator<SubjectPerformanceViewModel>();

  @override
  void initState() {
    super.initState();

    _model.fetchSubjectPerformanceList();
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider<SubjectPerformanceViewModel>.value(
      value: serviceLocator<SubjectPerformanceViewModel>(),
      child: Consumer<SubjectPerformanceViewModel>(
        builder: (context, model, child) {
          return Scaffold(
            appBar: CustomAppBar(
              title: '业绩',
            ),
            body: model.isLoading
              ? CustomCircularProgressIndicator()
              : CustomSafeArea(
                  backgroundColor: themeContentBackgroundColor,
                  child: ListView.builder(
                    physics: BouncingScrollPhysics(),
                    itemCount: model.subjectPerformanceList.length,
                    itemBuilder: (context, index) {
                      final SubjectPerformanceModel performance = model.subjectPerformanceList[index];

                      return CustomInfoCard(
                        title: performance.tenderingProjectName != null && performance.tenderingProjectName.length > 0
                          ? performance.tenderingProjectName
                          : '---',
                        tabs: performance.tendereeName != null && performance.tendereeName.length > 0
                          ? [
                              CustomTab(
                                text: performance.tendereeName,
                              ),
                            ]
                          : [],
                        dataFields: [
                          {
                            'fieldName': '建设单位',
                            'fieldValue': performance.tendereeName,
                          },
                          {
                            'fieldName': '合同金额',
                            'fieldValue': '${performance.contractAmount ?? "0"} ${performance.monetaryUnit ?? ""}',
                          },
                          {
                            'fieldName': '竣工时间',
                            'fieldValue': performance.contractFinishDate,
                          },
                        ],
                        isFirstChild: index == 0,
                      );
                    },
                  ),
                ),
          );
        },
      ),
    );
  }

}
