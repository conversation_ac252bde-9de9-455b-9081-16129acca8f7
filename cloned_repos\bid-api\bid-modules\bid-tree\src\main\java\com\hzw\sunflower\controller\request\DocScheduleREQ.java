package com.hzw.sunflower.controller.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 开标一览表文件请求
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@ApiModel(description = "开标一览表文件请求实体")
@Data
public class DocScheduleREQ {

    @ApiModelProperty(value = "采购文件id", position = 1)
    private Long docId;

    @ApiModelProperty(value = "轮次", position = 2)
    private Integer bidRound;

}
