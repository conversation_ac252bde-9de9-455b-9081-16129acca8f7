package com.hzw.sunflower.entity.condition;

import com.hzw.sunflower.common.BaseCondition;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "查询条件")
public class ReportListCondition extends BaseCondition {
    @ApiModelProperty("关键字")
    private String keyWords;

    @ApiModelProperty("负责人")
    private String userName;

    @ApiModelProperty("项目编号")
    private String purchaseNumber;

    @ApiModelProperty("项目名称")
    private String projectName;

    @ApiModelProperty("委托单位")
    private String companyName;
    /**
     *  1、已开标：当前时间已过项目标包的开标时间 2、 未开标：当前时间未过项目标包的开标时间
     */
    @ApiModelProperty("开标状态(1已开标,2未开标)")
    private Integer openingBid;

    @ApiModelProperty("所属处室")
    private Integer deptId;

    @ApiModelProperty("进场场地")
    private String processAddress;

    @ApiModelProperty("政府采购")
    private Integer purchaseType;

    @ApiModelProperty("归档情况（1未归档、2待归档、3已归档）")
    private String status;

    /**
     *  是否国际标
     *
     */
    @ApiModelProperty("是否国际标")
    private Integer isInternational;

    /**
     *  开始时间
     *
     */
    @ApiModelProperty("开始时间")
    private String startDate;


    /**
     *  结束时间
     *
     */
    @ApiModelProperty("结束时间")
    private String endDate;

    @ApiModelProperty("是否为运管处判断")
    private Boolean code;

}
