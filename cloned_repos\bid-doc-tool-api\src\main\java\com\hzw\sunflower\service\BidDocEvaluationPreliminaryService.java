package com.hzw.sunflower.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hzw.sunflower.controller.request.BidDocPreliminaryExcelReq;
import com.hzw.sunflower.entity.BidDocEvaluationPreliminary;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

public interface BidDocEvaluationPreliminaryService extends IService<BidDocEvaluationPreliminary> {

    /**
     * 下载模板 初步评审评标办法导入模板
     * @param request
     * @param response
     */
    void downTemplatePreliminary(HttpServletRequest request, HttpServletResponse response);

    /**
     * 批量导入 初步评审评标办法文件解析
     * @param req
     * @return
     */
    List<List<String>> preliminaryExcelParse(BidDocPreliminaryExcelReq req);
}
