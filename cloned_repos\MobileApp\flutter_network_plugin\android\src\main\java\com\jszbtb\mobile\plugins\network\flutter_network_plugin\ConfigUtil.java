package com.jszbtb.mobile.plugins.network.flutter_network_plugin;

import android.content.Context;

import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.Properties;

public class ConfigUtil {
    public static String getConfig(Context context, String key) {
        String value = "";
        try {
            InputStream in = context.getAssets().open("config.properties");
            Properties prop = new Properties();
            prop.load( new InputStreamReader(in,"utf-8"));
            value = prop.getProperty(key);
        } catch (IOException ex) {

        }

        return value;
    }
}
