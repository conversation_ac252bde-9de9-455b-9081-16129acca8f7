/*
 * 公司名称：江苏华招网
 * 版权信息：江苏华招网版权
 * 描述：http请求接口配置信息
 * 文件名称：HttpConfig.java
 * 修改时间：2016年9月27日
 * 修改人：尤拍拍
 * 修改内容：
 * 跟踪单号：
 * 修改单号 ：
 */

package com.hzw.ssm.util.http;

/**
 * <一句话功能简述> http请求接口配置信息
 * <功能详细描述>
 * <AUTHOR>
 * @version V1.0[产品/模块版本]
 */
public class HttpConfig {
    /**
     * 请求地址前缀接口请求url
     */
    private String url;

    /**
     * 字符编码格式，默认utf-8
     */
    private String dataEncoding = "utf-8";

    /**
     * 连接接口服务超时设置，单位秒
     */
    private int conntectionTimeOut = 5;
    /**
     * 连接接口服务器，读取数据超时时间设置，单位秒
     */
    private int readTimeOut = 5;
    
    /**
     * contentType 内容类型
     */
    private String contentType = "application/x-www-form-urlencoded";

    /**
     * @return the url
     */
    public String getUrl()
    {
        return url;
    }

    /**
     * @param url the url to set
     */
    public void setUrl(String url)
    {
        this.url = url;
    }

    /**
     * @return the dataEncoding
     */
    public String getDataEncoding()
    {
        return dataEncoding;
    }

    /**
     * @param dataEncoding the dataEncoding to set
     */
    public void setDataEncoding(String dataEncoding)
    {
        this.dataEncoding = dataEncoding;
    }

    /**
     * @return the conntectionTimeOut
     */
    public int getConntectionTimeOut()
    {
        return conntectionTimeOut;
    }

    /**
     * @param conntectionTimeOut the conntectionTimeOut to set
     */
    public void setConntectionTimeOut(int conntectionTimeOut)
    {
        this.conntectionTimeOut = conntectionTimeOut;
    }

    /**
     * @return the readTimeOut
     */
    public int getReadTimeOut()
    {
        return readTimeOut;
    }

    /**
     * @param readTimeOut the readTimeOut to set
     */
    public void setReadTimeOut(int readTimeOut)
    {
        this.readTimeOut = readTimeOut;
    }

    /**
     * @return the contentType
     */
    public String getContentType()
    {
        return contentType;
    }

    /**
     * @param contentType the contentType to set
     */
    public void setContentType(String contentType)
    {
        this.contentType = contentType;
    }
    
}
