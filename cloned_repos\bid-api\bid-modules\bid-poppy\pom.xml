<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>bid-modules</artifactId>
        <groupId>com.hzw</groupId>
        <version>0.0.4</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>bid-poppy</artifactId>

    <name>bid-poppy</name>
    <dependencies>
        <dependency>
            <groupId>com.hzw</groupId>
            <artifactId>bid-common-core</artifactId>
<!--            <version>0.0.4</version>-->
        </dependency>
        <dependency>
            <groupId>com.hzw</groupId>
            <artifactId>bid-common-service</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
        </dependency>


        <dependency>
            <groupId>jakarta.validation</groupId>
            <artifactId>jakarta.validation-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.hzw</groupId>
            <artifactId>bid-common-datascope</artifactId>
        </dependency>

        <dependency>
            <groupId>com.hzw</groupId>
            <artifactId>bid-datura</artifactId>
        </dependency>

        <dependency>
            <groupId>com.hzw</groupId>
            <artifactId>bid-common-enterprise-certification</artifactId>
        </dependency>
        <dependency>
            <groupId>com.hzw</groupId>
            <artifactId>bid-ncc-client</artifactId>
<!--            <version>0.0.4</version>-->
<!--            <scope>compile</scope>-->
        </dependency>
        <dependency>
            <groupId>com.hzw</groupId>
            <artifactId>bid-sys</artifactId>
<!--            <version>0.0.4</version>-->
<!--            <scope>compile</scope>-->
        </dependency>
        <dependency>
            <groupId>com.alicp.jetcache</groupId>
            <artifactId>jetcache-anno-api</artifactId>
        </dependency>

    </dependencies>
</project>
