///***
/// * 首页 搜索 视图模型
/// */

import 'package:flutter/material.dart';

import 'package:flutter_main_app/src/utils/ApiUtils.dart';
import 'package:flutter_main_app/src/constants/region.dart';

import 'package:flutter_main_app/src/models/common/HttpResponseDataListModel.dart';
import 'package:flutter_main_app/src/models/bulletin/BulletinListQueryModel.dart';
import 'package:flutter_main_app/src/models/bulletin/BulletinListItemModel.dart';

class HomeSearchViewModel extends ChangeNotifier {

  HomeSearchViewModel();

  /// * 页脚信息
  int totalNumber = 0;

  int totalPage = 0;

  /// * 筛选条件
  BulletinListQueryModel params;

  ///***
  /// * 设置指定的参数对象值
  /// *
  /// * @param {String} paramName
  /// * @param {dynamic} paramValue
  /// */
  void setSpecifiedParam(String paramName, dynamic paramValue) {
    final Map<String, dynamic> tempParams = params.toJson();

    tempParams[paramName] = paramValue;

    params = BulletinListQueryModel.fromJson(tempParams);
  }

  /// * 关键词
  String keyword = '';

  /// * 数据列表
  List<BulletinListItemModel> _dataList = [];

  List<BulletinListItemModel> get dataList => _dataList;

  ///***
  /// * 获取数据列表
  /// */
  Future<List<BulletinListItemModel>> fetchDataList() async {
    final BulletinListQueryModel params = this.params;

    params.keyWord = this.keyword.isNotEmpty ? [this.keyword] : [];

    final HttpResponseDataListModel<BulletinListItemModel> httpResponse =
      await ApiUtils.postBulletin(params);

    List<BulletinListItemModel> newDataList;

    if (httpResponse != null) {
      newDataList = httpResponse.data ?? [];

      totalNumber = httpResponse.totalNumber ?? 0;

      totalPage = httpResponse.totalPage ?? 0;

      for (int i = 0; i < newDataList.length; i++) {
        final String code = newDataList[i].regionCode;

        if (code != null && code.isNotEmpty && code.length == 6) {
          for (int j = 0; j < jiangSuRegionList.length; j++) {
            if (code.substring(0, 4) == jiangSuRegionList[j].code.substring(0, 4)) {
              newDataList[i].regionName = jiangSuRegionList[j].name;

              break;
            }

            newDataList[i].regionName = '江苏省';
          }
        } else {
          newDataList[i].regionName = '江苏省';
        }
      }

      concatDataList(newDataList);
    }

    return newDataList;
  }

  ///***
  /// * 清空数据列表
  /// */
  void clearDataList() {
    _dataList.clear();
  }

  ///***
  /// * 向数据列表末尾添加新的数据列表
  /// *
  /// * @param {List<BulletinListItemModel>} newDataList
  /// */
  void concatDataList(List<BulletinListItemModel> newDataList) {
    _dataList?.addAll(newDataList);
  }

  ///************************************* 搜索历史 *************************************///
  final List<String> searchHistory = [];

  ///***
  /// * 获取用户的搜索历史关键词
  /// */
  Future<List<String>> fetchSearchHistory() async {
    final List<String> keywords = await ApiUtils.getSearchHistoryKeywords();

    searchHistory.clear();
    searchHistory.addAll(keywords);

    return keywords;
  }

  ///************************************* 热门搜索 *************************************///
  final List<String> popularSearch = [];

  ///***
  /// * 获取热门搜索关键词
  /// */
  Future<List<String>> fetchPopularSearch() async {
    final List<String> keywords = await ApiUtils.getPopularSearchKeywords();

    popularSearch.clear();
    popularSearch.addAll(keywords);

    return keywords;
  }

}
