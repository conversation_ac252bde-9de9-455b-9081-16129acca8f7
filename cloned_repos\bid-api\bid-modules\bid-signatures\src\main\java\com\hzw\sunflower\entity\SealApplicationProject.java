package com.hzw.sunflower.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.hzw.sunflower.common.BaseBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
* Created by pj on 2023/11/03
*/
@ApiModel("t_seal_application_project表")
@TableName("t_seal_application_project")
@Data
public class SealApplicationProject extends BaseBean implements Serializable {
    private Long id;

    /**
     * 申请id
     *
     * @mbg.generated
     */
    @ApiModelProperty("申请id")
    private Long applyId;

    /**
     * 项目id
     *
     * @mbg.generated
     */
    @ApiModelProperty("项目id")
    private Long projectId;

    /**
     * 标段id
     *
     * @mbg.generated
     */
    @ApiModelProperty("标段id")
    private Long sectionId;

    /**
     * 项目名称
     *
     * @mbg.generated
     */
    @ApiModelProperty("项目名称")
    private String projectName;

    /**
     * 项目编号
     *
     * @mbg.generated
     */
    @ApiModelProperty("项目编号")
    private String projectNumber;

    /**
     * 包段编号 
     *
     * @mbg.generated
     */
    @ApiModelProperty("包段编号")
    private Integer packageNumber;

    /**
     * 包名称
     *
     * @mbg.generated
     */
    @ApiModelProperty("包名称")
    private String packageName;

    @ApiModelProperty(value = "招标阶段：1.第一轮;2.第二轮")
    private Integer bidRound;



    private static final long serialVersionUID = 1L;
}