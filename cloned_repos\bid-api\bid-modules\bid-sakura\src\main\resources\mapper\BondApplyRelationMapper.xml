<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzw.sunflower.dao.BondApplyRelationMapper">
    <select id="applyRelationList" resultType="com.hzw.sunflower.controller.response.AbnormalApplyRelationVo">
        SELECT
            bar.id AS applyRelationId,
            p.purchase_name as projectName,
            p.purchase_number as projectNum,
            pbs.package_name,
            pbs.submit_end_time,
            bar.content,
            u.user_name,
			d.department_name,
			bar.created_time as applyTime
        FROM
            t_bond_apply_relation bar
            LEFT JOIN t_project p ON bar.project_id = p.id
            LEFT JOIN t_project_bid_section pbs ON p.id = pbs.project_id
            AND bar.section_id = pbs.id
            LEFT JOIN t_user u
            on bar.created_user_id = u.id
            LEFT JOIN r_user_department ud
            on u.id = ud.user_id
            left join t_department d
            on ud.department_id = d.id
        WHERE
            bar.is_delete = 0
            AND p.is_delete = 0
            AND pbs.is_delete = 0
            and u.is_delete = 0
            and ud.is_delete = 0
            and d.is_delete = 0
            and pbs.bid_round = 2
            and ud.user_identity_id = 2
            and bar.type = #{condition.type}
        order by bar.created_time
    </select>
    <select id="getBondApplyRelationPage" resultType="com.hzw.sunflower.controller.response.BondApplyRelationVo">
        SELECT
            distinct
            p.purchase_number AS projectNum,
            p.purchase_name AS projectName,
            s.package_name AS packageName,
            s.submit_end_time AS submitEndTime,
            r.content AS content,
            r.type AS type,
            r.`status` AS `status`,
            r.return_msg AS returnMsg,
            r.id AS id,
            s.package_number AS packageNum,
            r.project_id AS projectId,
            r.section_id AS sectionId,
            r.company_id AS companyId,
            r.apply_time,
            r.payment_voucher_ids
        FROM
            t_bond_apply_relation r
            LEFT JOIN t_project p ON r.project_id = p.id
            LEFT JOIN t_project_entrust_user peu ON p.id = peu.project_id
            AND peu.type = 1
            LEFT JOIN r_user_department  rud ON peu.user_id = rud.user_id and peu.department_id = rud.department_id
            LEFT JOIN t_project_bid_section s ON r.section_id = s.id
        WHERE
            r.is_delete = 0
            AND p.is_delete = 0
            AND peu.is_delete = 0
            AND s.is_delete = 0
            AND peu.user_id = #{condition.userId}
            AND peu.department_id = #{condition.departId}
        <if test="condition.keywords != null and condition.keywords != ''">
            AND (p.purchase_number like  concat('%',#{condition.keywords},'%')
                 OR p.purchase_name like concat('%',#{condition.keywords},'%'))
        </if>
        order by r.apply_time desc
    </select>
    <select id="queryByIdCompleted" resultType="com.hzw.sunflower.entity.BondApplyRelation">
        select * from t_bond_apply_relation where id = #{applyId}
    </select>
</mapper>
