// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'SubjectPerformanceModel.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SubjectPerformanceModel _$SubjectPerformanceModelFromJson(
    Map<String, dynamic> json) {
  return SubjectPerformanceModel(
    active: json['active'] as bool,
    agentCharge: json['agentCharge'] as String,
    caNoDataKey: json['caNoDataKey'] as String,
    consumerNumber: json['consumerNumber'] as String,
    consumerPerson: json['consumerPerson'] as String,
    consumerValuation: json['consumerValuation'] as String,
    contractAmount: json['contractAmount'] as String,
    contractContents: json['contractContents'] as String,
    contractDate: json['contractDate'] as String,
    contractFinishDate: json['contractFinishDate'] as String,
    createdDate: json['createdDate'] as String,
    currencyCode: json['currencyCode'] as String,
    dataKey: json['dataKey'] as String,
    modifiedDate: json['modifiedDate'] as String,
    monetaryUnit: json['monetaryUnit'] as String,
    performanceID: json['performanceID'] as int,
    projectLeader: json['projectLeader'] as String,
    statusValue: json['statusValue'] as int,
    subjectCode: json['subjectCode'] as String,
    subjectDataKey: json['subjectDataKey'] as String,
    subjectID: json['subjectID'] as int,
    subjectType: json['subjectType'] as String,
    tendereeCode: json['tendereeCode'] as String,
    tendereeName: json['tendereeName'] as String,
    tenderingProjectName: json['tenderingProjectName'] as String,
    tenderingProjectNo: json['tenderingProjectNo'] as String,
  );
}

Map<String, dynamic> _$SubjectPerformanceModelToJson(
        SubjectPerformanceModel instance) =>
    <String, dynamic>{
      'active': instance.active,
      'agentCharge': instance.agentCharge,
      'caNoDataKey': instance.caNoDataKey,
      'consumerNumber': instance.consumerNumber,
      'consumerPerson': instance.consumerPerson,
      'consumerValuation': instance.consumerValuation,
      'contractAmount': instance.contractAmount,
      'contractContents': instance.contractContents,
      'contractDate': instance.contractDate,
      'contractFinishDate': instance.contractFinishDate,
      'createdDate': instance.createdDate,
      'currencyCode': instance.currencyCode,
      'dataKey': instance.dataKey,
      'modifiedDate': instance.modifiedDate,
      'monetaryUnit': instance.monetaryUnit,
      'performanceID': instance.performanceID,
      'projectLeader': instance.projectLeader,
      'statusValue': instance.statusValue,
      'subjectCode': instance.subjectCode,
      'subjectDataKey': instance.subjectDataKey,
      'subjectID': instance.subjectID,
      'subjectType': instance.subjectType,
      'tendereeCode': instance.tendereeCode,
      'tendereeName': instance.tendereeName,
      'tenderingProjectName': instance.tenderingProjectName,
      'tenderingProjectNo': instance.tenderingProjectNo,
    };
