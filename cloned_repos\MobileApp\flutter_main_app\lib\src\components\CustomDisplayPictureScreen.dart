///***
/// * 图片展示页面
/// */

import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_main_app/src/utils/ScreenUtils.dart';

class CustomDisplayPictureScreen extends StatelessWidget {

  CustomDisplayPictureScreen({
    Key key,
    this.imagePath,
  }) : super(key: key);

  final String imagePath;

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Container(
        width: ScreenUtils.screenWidth,
        height: ScreenUtils.screenHeight,
        child: Center(
          child: Image.file(
            File(imagePath),
          ),
        ),
      ),
    );
  }

}
