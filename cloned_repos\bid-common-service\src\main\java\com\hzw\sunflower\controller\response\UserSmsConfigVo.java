package com.hzw.sunflower.controller.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date ：Created in 2022/8/31 16:53
 * @description：
 * @modified By：`
 * @version: 1.0
 */
@ApiModel(description = "用户短信配置")
@Data
public class UserSmsConfigVo implements Serializable {

    @ApiModelProperty(value = "主键", position = 1)
    private Long id;

    @ApiModelProperty(value = "用户id", position = 2)
    private Long userId;

    @ApiModelProperty(value = "模板id", position = 3)
    private Long templateId;

    @ApiModelProperty(value = "是否自动发送 （1.是 2.否）", position = 4)
    private Integer isAuto;

    @ApiModelProperty(value = "是否每24小时再次发送 （1.是 2.否）", position = 5)
    private Integer isSendRepeat;

    @ApiModelProperty(value = "短信重复发送截止日期", position = 6)
    private Integer endDay;
}
