package com.hzw.sunflower.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.hzw.sunflower.common.BaseBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel("t_company_flow_jump")
@TableName("t_company_flow_jump")
@Data
public class CompanyFlowJump extends BaseBean {

    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("流程名称")
    private String flowName;

    @ApiModelProperty("流程编码")
    private String processDefinitionKey;

    @ApiModelProperty("流程节点")
    private String processId;

    @ApiModelProperty("跳过类型  1 跳过  2 忽略跳过（必审）")
    private String jumpType;

}
