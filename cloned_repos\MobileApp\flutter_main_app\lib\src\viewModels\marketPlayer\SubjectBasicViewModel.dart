///***
/// * 主体基本信息 视图模型
/// */

import 'package:flutter/material.dart';

import 'package:flutter_main_app/serviceLocator.dart';
import 'package:flutter_main_app/src/utils/ApiUtils.dart';

import 'package:flutter_main_app/src/viewModels/marketPlayer/MarketPlayerListViewModel.dart';

import 'package:flutter_main_app/src/models/marketPlayer/SubjectBasicModel.dart';

class SubjectBasicViewModel extends ChangeNotifier {

  SubjectBasicViewModel();

  /// * 主要用于获取当前 subjectID
  final MarketPlayerListViewModel _marketPlayerListViewModel = serviceLocator<MarketPlayerListViewModel>();

  /// * 是否正在加载数据
  bool _isLoading = false;

  bool get isLoading => _isLoading;

  set isLoading(bool isLoading) {
    _isLoading = isLoading;

    notifyListeners();
  }

  /// * 主体基本信息
  SubjectBasicModel _subjectBasic;

  SubjectBasicModel get subjectBasic => _subjectBasic;

  ///***
  /// * 给主体基本信息赋值
  /// * 同时通知所有订阅者
  /// *
  /// * @param {SubjectBasicModel} subjectBasic
  /// */
  set subjectBasic(SubjectBasicModel subjectBasic) {
    _subjectBasic = subjectBasic;

    notifyListeners();
  }

  ///***
  /// * 获取主体基本信息
  /// */
  Future<void> fetchSubjectBasicInfo() async {
    isLoading = true;

    final Map<String, int> params = {
      'subjectID': _marketPlayerListViewModel.subjectID,
    };

    final SubjectBasicModel newSubjectBasic =
      await ApiUtils.getSubjectBasic(params);

    if (newSubjectBasic != null) {
      /// * 数据处理
      newSubjectBasic.operationDeadline = newSubjectBasic.operationDeadline != null && newSubjectBasic.operationDeadline.length >= 10
        ? newSubjectBasic.operationDeadline.substring(0, 10)
        : newSubjectBasic.operationDeadline ?? '';
      newSubjectBasic.registerTime = newSubjectBasic.registerTime != null && newSubjectBasic.registerTime.length >= 10
        ? newSubjectBasic.registerTime.substring(0, 10)
        : newSubjectBasic.registerTime ?? '';
    }

    subjectBasic = newSubjectBasic;

    isLoading = false;
  }

}
