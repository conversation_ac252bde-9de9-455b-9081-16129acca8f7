package com.hzw.ssm.applets.action;

import com.hzw.ssm.applets.entity.Company;
import com.hzw.ssm.applets.service.CompanyService;
import com.hzw.ssm.applets.utils.GetJsonReqData;
import com.hzw.ssm.applets.utils.GetJsonRespData;
import com.hzw.ssm.applets.utils.JsonToObject;
import com.hzw.ssm.applets.utils.ResultJson;
import com.hzw.ssm.expert.entity.ExpertInfoEntity;
import com.hzw.ssm.fw.base.BaseAction;
import net.sf.json.JSONObject;
import org.apache.struts2.convention.annotation.Action;
import org.apache.struts2.convention.annotation.Namespace;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @Date 2021-02-03 10:17
 * @Version 1.0
 */
@Namespace("/applets/companyAction")
public class CompanyAction extends BaseAction {

    @Autowired
    private CompanyService companyService;

    /**
     * 新增
     *
     * @return
     */
    @Action("save")
    public String save() {

        JSONObject jsonReqData = GetJsonReqData.getJsonReqData();
        String toString = jsonReqData.getJSONObject("formData").toString();
        Company company = JsonToObject.jsonToObject(toString, Company.class);
        ResultJson resultJson = companyService.insertCompany(company);
        GetJsonRespData.getJsonRespData(resultJson);
        return null;
    }

    /**
     * 编辑
     *
     * @return
     */
    @Action("update")
    public String update() {

        JSONObject jsonReqData = GetJsonReqData.getJsonReqData();
        String toString = jsonReqData.getJSONObject("formData").toString();
        ExpertInfoEntity company = JsonToObject.jsonToObject(toString, ExpertInfoEntity.class);
        ResultJson resultJson = companyService.updateCompany(company);
        GetJsonRespData.getJsonRespData(resultJson);
        return null;
    }

    /**
     * 删除
     *
     * @return
     */
    @Action("delete")
    public String delete() {

        JSONObject jsonReqData = GetJsonReqData.getJsonReqData();
        String toString = jsonReqData.getJSONObject("formData").toString();
        Company company = JsonToObject.jsonToObject(toString, Company.class);
        ResultJson resultJson = companyService.deleteCompany(company);
        GetJsonRespData.getJsonRespData(resultJson);
        return null;
    }

    /**
     * 查询单个
     *
     * @return
     */
    @Action("getOne")
    public String getOne() {

        JSONObject jsonReqData = GetJsonReqData.getJsonReqData();
        String toString = jsonReqData.getJSONObject("formData").toString();
        Company company = JsonToObject.jsonToObject(toString, Company.class);
        ResultJson resultJson = companyService.getCompany(company);
        GetJsonRespData.getJsonRespData(resultJson);
        return null;
    }

    /**
     * 查询所有
     *
     * @return
     */
    @Action("all")
    public String all() {

        JSONObject jsonReqData = GetJsonReqData.getJsonReqData();
        String toString = jsonReqData.getJSONObject("formData").toString();
        Company company = JsonToObject.jsonToObject(toString, Company.class);
        ResultJson resultJson = companyService.getCompanys(company);
        GetJsonRespData.getJsonRespData(resultJson);
        return null;
    }
}
