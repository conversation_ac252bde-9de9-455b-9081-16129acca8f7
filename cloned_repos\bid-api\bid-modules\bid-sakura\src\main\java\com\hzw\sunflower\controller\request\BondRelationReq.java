package com.hzw.sunflower.controller.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date ：Created in 2022/7/29 11:10
 * @description：
 * @modified By：`
 * @version: 1.0
 */
@ApiModel(description = "关联页面详情查询入参")
@Data
public class BondRelationReq implements Serializable {

    @ApiModelProperty(value = "标段id")
    private Long sectionId;

    @ApiModelProperty(value = "项目id")
    private Long projectId;

    @ApiModelProperty(value = "公司id")
    private Long companyId;

    @ApiModelProperty(value = "操作角色 1.供应商 2.项目经理")
    private Integer operateRole;

    @ApiModelProperty(value = "关联状态 2.已关联 3.异常关联")
    private Integer relationStatus;

    @ApiModelProperty(value = "保证金线下项目供应商表id")
    private Long bondOfflineCompanyId;
}
