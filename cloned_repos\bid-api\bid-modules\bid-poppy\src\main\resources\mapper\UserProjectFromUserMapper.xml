<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="com.hzw.sunflower.dao.UserProjectFromUserMapper">


    <select id="selectListByProjectIdAndFromUserId" resultType="com.hzw.sunflower.dto.UserProjectFromUserDTO" >

        SELECT
               *
         from
            v_project_user_perm a
        where
              a.from_user_id=#{fromUserId}
          and a.project_id=#{projectId}
    </select>

    <select id="selectListWithPermByProjectIdAndUserId" resultType="com.hzw.sunflower.dto.MyProjectListDTO" >

        SELECT * FROM
            (
                SELECT
                    p.id,
                    p.project_name projectName,
                    p.project_number projectNumber,
                    p.purchase_name purchaseName,
                    p.purchase_number purchaseNumber,
                    p.status,
                    vpup.right_code rightCode,
                    vpup.user_id userId
                FROM v_project_user_perm vpup LEFT JOIN t_project p on vpup.project_id = p.id
                where
                    vpup.user_id = #{userId}
                <if test="departId !=null">
                    and vpup.depart_id = #{departId}
                </if>

        UNION

                SELECT
                    distinct
                    p.id,
                    p.project_name projectName,
                    p.project_number projectNumber,
                    p.purchase_name purchaseName,
                    p.purchase_number purchaseNumber,
                    p.status,
                    '1' as rightCode,
                    tpe.user_id userId
                FROM
                    t_project p
                    left join t_project_entrust_user tpe  on p.id = tpe.project_id
                    LEFT JOIN r_user_department rud on tpe.user_id = rud.user_id  and tpe.department_id = rud.department_id
                WHERE
                    p.is_delete = 0
                  and tpe.is_delete = 0
                  and tpe.type=#{type}
                ${dataScope}
            ) a  where
             id in
        <foreach collection="projectIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="getEmpowerProjectFoeEdit" resultType="java.lang.Integer">
        select count(1) from v_project_user_perm where user_id = #{userId} and project_id = #{projectId} and depart_id = #{departId} and right_code = 1
    </select>
</mapper>