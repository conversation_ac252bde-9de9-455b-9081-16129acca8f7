package com.hzw.sunflower.service.impl;

import com.hzw.sunflower.constant.ProjectAnalysisConstants;
import com.hzw.sunflower.controller.response.ProjectIndustryDistributionVo;
import com.hzw.sunflower.controller.request.ProjectAnalysisReq;
import com.hzw.sunflower.dao.ProjectAnalysisMapper;
import com.hzw.sunflower.entity.Dictionary;
import com.hzw.sunflower.entity.*;
import com.hzw.sunflower.service.ProjectAnalysisService;
import com.hzw.sunflower.service.ProjectIndustryDistributionService;
import com.hzw.sunflower.util.BeanListUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.PredicateUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class ProjectAnalysisServiceImpl implements ProjectAnalysisService {


    @Autowired
    ProjectAnalysisMapper projectAnalysisMapper;
    @Autowired
    ProjectIndustryDistributionService projectIndustryDistributionService;
    @Override
    public ProjectAnalysis queryAll(String startDate, String endDate) {
        ProjectAnalysis projectAnalyses = projectAnalysisMapper.queryAll(startDate, endDate);
        return projectAnalyses;
    }

    /**
     * 查询地区分布一览表
     * @param startDate
     * @param endDate
     * @return
     */
    @Override
    public ProjectAnalysis queryAllForArea(String startDate, String endDate) {
        ProjectAnalysis projectAnalyses = projectAnalysisMapper.queryAllForArea(startDate, endDate);
        return projectAnalyses;
    }

    /**
     * 导出行业项目分布（附表）
     * @param condition
     * @return
     */
    @Override
    public List<CollectInfo> collectExpertList(ProjectAnalysisReq condition) {
        List<CollectInfo> collectExperts = projectAnalysisMapper.collectExpertList(condition.getStartDate(),condition.getEndDate());
        return collectExperts;
    }

    @Override
    public List<DepartProject> queryProjectByDepartmentNew(String startDate, String endDate) {
        String lastYearStartDate = (Integer.parseInt(startDate.substring(0,4)) - 1) + ""+startDate.substring(4);
        String lastYearEndDate = (Integer.parseInt(endDate.substring(0,4)) - 1) + ""+endDate.substring(4);
        List<DepartProject> list = projectAnalysisMapper.queryProjectByDepartmentNew(startDate,endDate,lastYearStartDate,lastYearEndDate);
        if(CollectionUtils.isNotEmpty(list)){
            for (DepartProject departProject : list) {
                Integer yearCount = departProject.getYearCount();
                Integer lastYearCount = departProject.getLastYearCount();
                if(!yearCount.equals(0) && !lastYearCount.equals(0)){
                    departProject.setCountCompared(new BigDecimal(yearCount).subtract(new BigDecimal(lastYearCount)).divide(new BigDecimal(lastYearCount),2, RoundingMode.HALF_UP).multiply(new BigDecimal("100")));
                }else{
                    departProject.setCountCompared(new BigDecimal("0.00"));
                }
                BigDecimal yearAmount = departProject.getYearAmount();
                BigDecimal lastYearAmount = departProject.getLastYearAmount();
                if (yearAmount.compareTo(new BigDecimal("0.00")) == 1  && lastYearAmount.compareTo(new BigDecimal("0.00")) == 1){
                    departProject.setAmountCompared((yearAmount.subtract(lastYearAmount)).divide(lastYearAmount,4, RoundingMode.HALF_UP).multiply(new BigDecimal("100")).setScale(2,RoundingMode.HALF_UP));
                }else {
                    departProject.setAmountCompared(new BigDecimal("0.00"));
                }
                //转万元
                departProject.setMonthAmount(departProject.getMonthAmount().divide(ProjectAnalysisConstants.TEN_THOUSAND,6, RoundingMode.HALF_UP));
                departProject.setLastYearAmount(departProject.getLastYearAmount().divide(ProjectAnalysisConstants.TEN_THOUSAND,6, RoundingMode.HALF_UP));
                departProject.setYearAmount(departProject.getYearAmount().divide(ProjectAnalysisConstants.TEN_THOUSAND,6, RoundingMode.HALF_UP));
            }
        }
        return list;
    }

    @Override
    public List<LargeProject> queryLargeProjectListNew(String startDate, String endDate) {
        List<LargeProject> list = projectAnalysisMapper.queryLargeProjectListNew(startDate,endDate);

        List<LargeProject> values = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(list)){
            //处理兼岗人员项目重复统计
            Map<String,LargeProject> mapCount = new HashMap<>();

            for (LargeProject largeProject : list) {
                mapCount.put(largeProject.getProjectNumber()+largeProject.getPackageNumber()+largeProject.getUserName(),largeProject);
            }

            values = new ArrayList<>(mapCount.values());
            int i = 1;
            for (LargeProject largeProject : values) {
                if(largeProject.getEntrustMoney() != null){
                    //元转换成万元
                    largeProject.setEntrustMoney(largeProject.getEntrustMoney().divide(ProjectAnalysisConstants.TEN_THOUSAND,6, RoundingMode.HALF_UP));
                    if(null != largeProject.getPackageNumber()){
                        largeProject.setProjectNumber(largeProject.getProjectNumber()+"/"+largeProject.getPackageNumber());
                    }
                }
                largeProject.setSeq(i++);
            }
        }
        return values;
    }

    //    @Override
//    public List<ProjectAnalysis> queryProjectAnalysisList(String startDate, String endDate) {
//        List<ProjectAnalysis> projectAnalyses = new ArrayList<>();
//        List<ProjectBidSection> list = projectAnalysisMapper.queryProjectBidSectionList(startDate, endDate);
//        Map<String,ProjectAnalysis> map  = new HashMap<>();
//        if(CollectionUtils.isNotEmpty(list)){
//            for (ProjectBidSection p : list) {
//                Long id = p.getId();
//                List<SectionExpertSpecialty> sectionExpertSpecialties = projectAnalysisMapper.queryExpertSpecialtyBySectionId(id);
//                for (SectionExpertSpecialty sectionExpertSpecialty : sectionExpertSpecialties) {
//                    Long expertSpecialtyId = sectionExpertSpecialty.getExpertSpecialtyId();//对应字典表
//                    Dictionary dictionary2class = projectAnalysisMapper.querySecondClass(expertSpecialtyId);
//                    Dictionary dictionary1class = projectAnalysisMapper.queryFirstClass(dictionary2class.getParentId());
//
//                    String secondCode = dictionary2class.getCode();//二级分类编码
//                    String secondName = dictionary2class.getName();//二级分类名称
////                String firstCode = dictionary1class.getCode();//一级分类编码
//                    String firstName = dictionary1class.getName();//一级分类名称
//
//                    if(map.get(secondCode) == null){
//                        ProjectAnalysis projectAnalysis = new ProjectAnalysis();
//                        projectAnalysis.setEngineeringClass(firstName);
//                        projectAnalysis.setProjectIndustry(secondName);
//                        projectAnalysis.setProjectCount(1);
//                        projectAnalysis.setEntrustMoney(p.getEntrustMoney());
//                        map.put(secondCode,projectAnalysis);
//                    }else{
//                        ProjectAnalysis projectAnalysis = map.get(secondCode);
//                        projectAnalysis.setProjectCount(projectAnalysis.getProjectCount()+1);
//                        projectAnalysis.setEntrustMoney(projectAnalysis.getEntrustMoney().add(p.getEntrustMoney()));
//                        map.put(secondCode,projectAnalysis);
//                    }
//
//                }
//            }
//            Iterator <Map.Entry< String, ProjectAnalysis >> iterator = map.entrySet().iterator();
//            while (iterator.hasNext()) {
//                Map.Entry< String, ProjectAnalysis > entry = iterator.next();
//                projectAnalyses.add(entry.getValue());
//            }
//        }
//        return projectAnalyses;
//    }

//    @Override
//    public List<ProjectAnalysisArea> queryByAreaGroup(String startDate, String endDate) {
//        List<ProjectAnalysisArea> projectBidSections = projectAnalysisMapper.queryByAreaGroup(startDate, endDate);
//        return projectBidSections;
//    }
    /**
     * 查询地区分布一览表
     * @param startDate
     * @param endDate
     * @return
     */
    @Override
    public List<ProjectAnalysisArea> queryProjectListWithArea(String startDate, String endDate) {
        List<ProjectAnalysisArea> projectBidSections = projectAnalysisMapper.queryProjectListWithArea(startDate,endDate);
        if(CollectionUtils.isNotEmpty(projectBidSections)){
//            for (ProjectAnalysisArea p : projectBidSections){
//                ////元转换成万元
//                p.setEntrustMoney(p.getEntrustMoney().divide(ProjectAnalysisConstants.TEN_THOUSAND,6, RoundingMode.HALF_UP));
//            }
        }
        return projectBidSections;
    }

    /**
     * 查询地区分布一览表New
     * @param startDate
     * @param endDate
     * @return
     */
    @Override
    public List<ProjectAnalysisArea> queryProjectListWithAreaNew(String startDate, String endDate) {
        List<ProjectAnalysisArea> projectBidSections = projectAnalysisMapper.queryProjectListWithAreaNew(startDate,endDate);
        ProjectAnalysisArea all = projectAnalysisMapper.queryAllAreaNew(startDate,endDate);
        List<ProjectAnalysisArea> orderedProjectAnalysisArea = null;
        //计算占比及金额转万元
        if (CollectionUtils.isNotEmpty(projectBidSections)){
            orderedProjectAnalysisArea = order(projectBidSections);
            for (ProjectAnalysisArea projectBidSection : orderedProjectAnalysisArea) {
                projectBidSection.setProportion(projectBidSection.getEntrustMoney().divide(all.getEntrustMoney(),4, RoundingMode.HALF_UP).multiply(new BigDecimal(100)));

                projectBidSection.setEntrustMoney(projectBidSection.getEntrustMoney().divide(ProjectAnalysisConstants.TEN_THOUSAND,6, RoundingMode.HALF_UP));
            }
        }
        return orderedProjectAnalysisArea;
    }

    @Override
    public List<ProjectAnalysisArea> queryProjectListWithAreaNewForExport(String startDate, String endDate) {
        List<ProjectAnalysisArea> projectBidSections = projectAnalysisMapper.queryProjectListWithAreaNew(startDate,endDate);
        ProjectAnalysisArea all = projectAnalysisMapper.queryAllAreaNew(startDate,endDate);
        List<ProjectAnalysisArea> orderedProjectAnalysisArea = null;
        //计算占比及金额转万元
        if (CollectionUtils.isNotEmpty(projectBidSections)){
            orderedProjectAnalysisArea = order(projectBidSections);
            for (ProjectAnalysisArea projectBidSection : orderedProjectAnalysisArea) {
                //计算金额占比
                projectBidSection.setProportion(projectBidSection.getEntrustMoney().divide(all.getEntrustMoney(),4, RoundingMode.HALF_UP).multiply(new BigDecimal(100)));
                //金额转万元
                projectBidSection.setEntrustMoney(projectBidSection.getEntrustMoney().divide(ProjectAnalysisConstants.TEN_THOUSAND,6, RoundingMode.HALF_UP));
            }
            all.setProportion(new BigDecimal(100));
            all.setEntrustMoney(all.getEntrustMoney().divide(ProjectAnalysisConstants.TEN_THOUSAND,6, RoundingMode.HALF_UP));
            all.setCity("全公司");
            projectBidSections.add(all);
        }
        return projectBidSections;
    }


    //地区分布排序
    private List<ProjectAnalysisArea> order(List<ProjectAnalysisArea> raw){
        ProjectAnalysisArea[] orderedList = new ProjectAnalysisArea[17];
        for (ProjectAnalysisArea projectAnalysisArea : raw) {
            String city = projectAnalysisArea.getCity();
            switch(city) {
                case ProjectAnalysisConstants.NANJING_STR:
                    orderedList[0] = projectAnalysisArea;
                    break;
                case ProjectAnalysisConstants.WUXI_STR:
                    orderedList[1] = projectAnalysisArea;
                    break;
                case ProjectAnalysisConstants.CHANGZHOU_STR:
                    orderedList[2] = projectAnalysisArea;
                    break;
                case ProjectAnalysisConstants.SUZHOU_STR:
                    orderedList[3] = projectAnalysisArea;
                    break;
                case ProjectAnalysisConstants.ZHENJIANG_STR:
                    orderedList[4] = projectAnalysisArea;
                    break;
                case ProjectAnalysisConstants.SOUTHSU:
                    orderedList[5] = projectAnalysisArea;
                    break;
                case ProjectAnalysisConstants.YANGZHOU_STR:
                    orderedList[6] = projectAnalysisArea;
                    break;
                case ProjectAnalysisConstants.TAIZHOU_STR:
                    orderedList[7] = projectAnalysisArea;
                    break;
                case ProjectAnalysisConstants.NANTONG_STR:
                    orderedList[8] = projectAnalysisArea;
                    break;
                case ProjectAnalysisConstants.MIDSU:
                    orderedList[9] = projectAnalysisArea;
                    break;
                case ProjectAnalysisConstants.SUQIAN_STR:
                    orderedList[10] = projectAnalysisArea;
                    break;
                case ProjectAnalysisConstants.HUAIAN_STR:
                    orderedList[11] = projectAnalysisArea;
                    break;
                case ProjectAnalysisConstants.YANCHENG_STR:
                    orderedList[12] = projectAnalysisArea;
                    break;
                case ProjectAnalysisConstants.XUZHOU_STR:
                    orderedList[13] = projectAnalysisArea;
                    break;
                case ProjectAnalysisConstants.LIANYUNGANG_STR:
                    orderedList[14] = projectAnalysisArea;
                    break;
                case ProjectAnalysisConstants.NORTHSU:
                    orderedList[15] = projectAnalysisArea;
                    break;
                case ProjectAnalysisConstants.OUTSU:
                    orderedList[16] = projectAnalysisArea;
                    break;
            }

        }

        List<ProjectAnalysisArea> projectAnalysisAreas = Arrays.asList(orderedList);
        List<ProjectAnalysisArea> collect = projectAnalysisAreas.parallelStream().filter(Objects::nonNull).collect(Collectors.toList());
        return collect;
    }


    /**
     * 查询大项目处室排名
     * @param startDate
     * @param endDate
     * @return
     */
    @Override
    public List<DepartProject> queryProjectByDepartment(String startDate, String endDate) {
        List<DepartProject> result = new ArrayList<>();
        String startMonth = startDate.substring(startDate.length() -2,startDate.length());
        String start01 = "";
        if(!"01".equals(startMonth)){//将入参startDate转成当年1月
            String temp = startDate.split("-")[0];
            start01 = temp+"-01";
        }
        List<ProjectInfo> projectInfosUp3000CurrentYear;
        List<ProjectInfo> projectInfosUp3000LastYear;
        Map<Long,DepartProject> map = new HashMap<>();
        //当年1月至endDate范围内所有项目
        List<ProjectInfo> projectInfosCurrentYear = projectAnalysisMapper.queryProjectInfoByDate(start01, endDate);
        if(CollectionUtils.isNotEmpty(projectInfosCurrentYear)){
            projectInfosUp3000CurrentYear = filtrateUp3000(projectInfosCurrentYear);
            //根据部门合并
            if(CollectionUtils.isNotEmpty(projectInfosUp3000CurrentYear)){
                for (ProjectInfo projectInfo : projectInfosUp3000CurrentYear) {
                    DepartProject old = map.get(projectInfo.getDepartmentId());
                    Date projectDate = projectInfo.getProjectDate();
                    if(old == null){
                        DepartProject dp = new DepartProject();
                        dp.setDepartmentId(projectInfo.getDepartmentId());
                        dp.setDepartmentName(projectInfo.getDepartmentName());
                        dp.setYearCount(1);
                        dp.setYearAmount(projectInfo.getEntrustedAmount());
                        if(isbetween(projectDate,startDate,endDate)){//项目时间在查询范围内
                            dp.setMonthCount(1);
                            dp.setMonthAmount(projectInfo.getEntrustedAmount());
                        }else{
                            dp.setMonthCount(0);
                            dp.setMonthAmount(ProjectAnalysisConstants.ZERO4);
                        }
                        dp.setLastYearCount(0);
                        dp.setLastYearAmount(ProjectAnalysisConstants.ZERO4);
                        map.put(projectInfo.getDepartmentId(),dp);
                    }else{
                        if(isbetween(projectDate,startDate,endDate)){//项目时间在查询范围内
                            old.setMonthCount(old.getMonthCount()+1);
                            old.setMonthAmount(old.getMonthAmount().add(projectInfo.getEntrustedAmount()));
                        }
                        old.setYearCount(old.getYearCount()+1);
                        old.setYearAmount(old.getYearAmount().add(projectInfo.getEntrustedAmount()));
                        map.put(projectInfo.getDepartmentId(),old);
                    }
                }
            }
        }
        //去年同期累计所有项目
        String lastYearStart = (Integer.parseInt(startDate.split("-")[0])-1)+"-01";
        String lastYearEnd = (Integer.parseInt(endDate.split("-")[0])-1)+"-"+endDate.split("-")[1];
        List<ProjectInfo> projectInfosLastYear = projectAnalysisMapper.queryProjectInfoByDate(lastYearStart, lastYearEnd);
        if(CollectionUtils.isNotEmpty(projectInfosLastYear)){
            projectInfosUp3000LastYear = filtrateUp3000(projectInfosLastYear);
            if(CollectionUtils.isNotEmpty(projectInfosUp3000LastYear)){
                for (ProjectInfo projectInfo : projectInfosUp3000LastYear) {
                    DepartProject old = map.get(projectInfo.getDepartmentId());
                    if(old == null){
                        DepartProject dp = new DepartProject();
                        dp.setYearCount(0);
                        dp.setYearAmount(ProjectAnalysisConstants.ZERO4);
                        dp.setMonthCount(0);
                        dp.setMonthAmount(ProjectAnalysisConstants.ZERO4);
                        dp.setLastYearCount(1);
                        dp.setLastYearAmount(projectInfo.getEntrustedAmount());
                        dp.setDepartmentId(projectInfo.getDepartmentId());
                        dp.setDepartmentName(projectInfo.getDepartmentName());
                        map.put(projectInfo.getDepartmentId(),dp);
                    }else{
                        old.setLastYearCount(old.getLastYearCount()+1);
                        old.setLastYearAmount(old.getLastYearAmount().add(projectInfo.getEntrustedAmount()));
                        map.put(projectInfo.getDepartmentId(),old);
                    }
                }
            }
        }

        List<DepartProject> mapValueList = calculateAndSort(new ArrayList<>(map.values()));
        int i = 1;
        for (DepartProject departProject : mapValueList) {
            //元转换成万元
            if(departProject.getMonthAmount() != null){
                departProject.setMonthAmount(departProject.getMonthAmount().divide(ProjectAnalysisConstants.TEN_THOUSAND,6, RoundingMode.HALF_UP));
            }

            if(departProject.getYearAmount() != null){
                departProject.setYearAmount(departProject.getYearAmount().divide(ProjectAnalysisConstants.TEN_THOUSAND,6, RoundingMode.HALF_UP));
            }

            if(departProject.getLastYearAmount() != null){
                departProject.setLastYearAmount(departProject.getLastYearAmount().divide(ProjectAnalysisConstants.TEN_THOUSAND,6, RoundingMode.HALF_UP));
            }

            departProject.setSeq(i++);
        }
        return mapValueList;
    }

    /**
     * 查询大项目明细
     * @param startDate
     * @param endDate
     * @return
     */
    @Override
    public List<LargeProject> queryLargeProjectList(String startDate, String endDate) {
        List<LargeProject> list = projectAnalysisMapper.queryLargeProjectList(startDate,endDate);
        List<LargeProject> largeProjectList = filtrateLargeProject(list);
        if(CollectionUtils.isNotEmpty(largeProjectList)){
            int i = 1;
            for (LargeProject largeProject : largeProjectList) {
                if(largeProject.getEntrustMoney() != null){
                    //元转换成万元
                    largeProject.setEntrustMoney(largeProject.getEntrustMoney().divide(ProjectAnalysisConstants.TEN_THOUSAND,6, RoundingMode.HALF_UP));
                }
                largeProject.setSeq(i++);
            }
        }
        return largeProjectList;
    }

    /**
     * 项目分析同步
     * @param dateString
     */
    @Override
    public void updateProjectIndustryDistributionList(String dateString) {

        Date now = new Date();
        String nowParameter = DateFormatUtils.format(now,"yyyy-MM");
        if(StringUtils.isNotEmpty(dateString)){
            nowParameter = dateString;
        }
        List<ProjectIndustryDistributionVo> projectIndustryDistributions = projectAnalysisMapper.selectProjectIndustryListForUpdate(nowParameter);
        if(CollectionUtils.isNotEmpty(projectIndustryDistributions)){
            for (ProjectIndustryDistributionVo projectIndustryDistribution : projectIndustryDistributions) {
                Long expertSpecialtyId = projectIndustryDistribution.getExpertSpecialtyId();
                Dictionary secondClass;
                //字典表查询一级行业分类及二级行业分类
                if(projectIndustryDistribution.getClassCode().length() == 5){//三级分类编码
                    secondClass = projectAnalysisMapper.querySecondClassByThree(expertSpecialtyId);
                }else{//四级分类编码
                    secondClass = projectAnalysisMapper.querySecondClassByFour(expertSpecialtyId);
                }

                Dictionary firstClass = projectAnalysisMapper.queryFirstClass(secondClass.getParentId());
                projectIndustryDistribution.setSecondLevelCode(secondClass.getCode());
                projectIndustryDistribution.setSecondLevelName(secondClass.getName());
                projectIndustryDistribution.setFirstLevelCode(firstClass.getCode());
                projectIndustryDistribution.setFirstLevelName(firstClass.getName());

                //按汇率计算委托金额
                if(projectIndustryDistribution.getEntrustCurrency() != ProjectAnalysisConstants.RMB_CODE){//非人民币
                    BigDecimal exchangeRate = projectIndustryDistribution.getExchangeRate();
                    BigDecimal entrustMoney  = projectIndustryDistribution.getEntrustMoney();
                    projectIndustryDistribution.setEntrustMoneyOfYuan(entrustMoney.multiply(exchangeRate));
                }else{
                    projectIndustryDistribution.setEntrustMoneyOfYuan(projectIndustryDistribution.getEntrustMoney());
                }
            }

            List<ProjectIndustryDistribution> infos = BeanListUtil.convertList(projectIndustryDistributions,ProjectIndustryDistribution.class);
            projectIndustryDistributionService.deleteCurrentMonth(nowParameter);
            projectIndustryDistributionService.saveBatch(infos);
        }
        System.out.println("-------------------执行成功了!---------------------------");
    }

    /**
     * 查询标的物行业分布一览表
     * @param startDate
     * @param endDate
     * @return
     */
    @Override
    public List<ProjectAnalysisVo> queryProjectIndustryDistributionList(String startDate, String endDate) {
//        List<ProjectAnalysisVo> projectAnalysisVoList = new ArrayList<>();
//        //统计期限内所有数据
//        List<ProjectAnalysis> list = projectAnalysisMapper.queryProjectIndustryDistributionList(startDate, endDate);
//        List<ProjectAnalysis> list1 = new ArrayList<>();
//        List<ProjectAnalysis> list2 = new ArrayList<>();
//        List<ProjectAnalysis> list3 = new ArrayList<>();
//        if(CollectionUtils.isNotEmpty(list)){
//            ProjectAnalysis projectAnalysTotal = projectAnalysisMapper.queryAll(startDate, endDate);
//            for (ProjectAnalysis p : list) {
//                if(projectAnalysTotal.getEntrustMoney().compareTo(BigDecimal.ZERO) > 0){
//                    p.setProportion(p.getEntrustMoney().divide(projectAnalysTotal.getEntrustMoney(),4, RoundingMode.HALF_UP).multiply(new BigDecimal("100")).setScale(2,RoundingMode.HALF_UP));
//                }
//                if(ProjectAnalysisConstants.ENGINEERING.equals(p.getEngineeringClass())){
//                    list1.add(p);
//                }else if(ProjectAnalysisConstants.GOODS.equals(p.getEngineeringClass())){
//                    list2.add(p);
//                }else if(ProjectAnalysisConstants.SERVICE.equals(p.getEngineeringClass())){
//                    list3.add(p);
//                }
//            }
//            ProjectAnalysisVo p1 = new ProjectAnalysisVo();
//            p1.setFirdtLevel(ProjectAnalysisConstants.ENGINEERING);
//            p1.setList(list1);
//            ProjectAnalysisVo p2 = new ProjectAnalysisVo();
//            p2.setFirdtLevel(ProjectAnalysisConstants.GOODS);
//            p2.setList(list2);
//            ProjectAnalysisVo p3 = new ProjectAnalysisVo();
//            p3.setFirdtLevel(ProjectAnalysisConstants.SERVICE);
//            p3.setList(list3);
//            projectAnalysisVoList.add(p1);
//            projectAnalysisVoList.add(p2);
//            projectAnalysisVoList.add(p3);
//
//        }
//        return projectAnalysisVoList;

        List<ProjectAnalysisVo> projectAnalysisVoList = new ArrayList<>();
        List<ProjectAnalysis> list = projectAnalysisMapper.statisticalIndustryDistribution(startDate, endDate);
        if(CollectionUtils.isNotEmpty(list)){
            List<ProjectAnalysis> list1 = new ArrayList<>();
            List<ProjectAnalysis> list2 = new ArrayList<>();
            List<ProjectAnalysis> list3 = new ArrayList<>();
            ProjectAnalysis projectAnalysTotal = new ProjectAnalysis();
            projectAnalysTotal.setEntrustMoney(ProjectAnalysisConstants.ZERO4);
            for (ProjectAnalysis projectAnalysis : list) {
                projectAnalysTotal.setEntrustMoney(projectAnalysTotal.getEntrustMoney().add(projectAnalysis.getEntrustMoney()));
                projectAnalysTotal.setProjectCount(projectAnalysTotal.getProjectCount() + projectAnalysis.getProjectCount());
            }
            if (projectAnalysTotal.getEntrustMoney() != null){
                //元转换成万元
                projectAnalysTotal.setEntrustMoney(projectAnalysTotal.getEntrustMoney().divide(ProjectAnalysisConstants.TEN_THOUSAND,6, RoundingMode.HALF_UP));
            }
            for (ProjectAnalysis projectAnalysis : list) {
                if (projectAnalysis.getEntrustMoney() != null){
                    //元转换成万元
                    projectAnalysis.setEntrustMoney(projectAnalysis.getEntrustMoney().divide(ProjectAnalysisConstants.TEN_THOUSAND,6, RoundingMode.HALF_UP));
                }
                projectAnalysis.setProportion(projectAnalysis.getEntrustMoney().divide(projectAnalysTotal.getEntrustMoney(),4, RoundingMode.HALF_UP).multiply(new BigDecimal("100")).setScale(2,RoundingMode.HALF_UP));
                if(ProjectAnalysisConstants.ENGINEERING.equals(projectAnalysis.getEngineeringClass())){
                    list1.add(projectAnalysis);
                }else if(ProjectAnalysisConstants.GOODS.equals(projectAnalysis.getEngineeringClass())){
                    list2.add(projectAnalysis);
                }else if(ProjectAnalysisConstants.SERVICE.equals(projectAnalysis.getEngineeringClass())){
                    list3.add(projectAnalysis);
                }

            }
            ProjectAnalysisVo p1 = new ProjectAnalysisVo();
            p1.setFirdtLevel(ProjectAnalysisConstants.ENGINEERING);
            p1.setList(list1);
            ProjectAnalysisVo p2 = new ProjectAnalysisVo();
            p2.setFirdtLevel(ProjectAnalysisConstants.GOODS);
            p2.setList(list2);
            ProjectAnalysisVo p3 = new ProjectAnalysisVo();
            p3.setFirdtLevel(ProjectAnalysisConstants.SERVICE);
            p3.setList(list3);
            projectAnalysisVoList.add(p1);
            projectAnalysisVoList.add(p2);
            projectAnalysisVoList.add(p3);
        }
        return projectAnalysisVoList;
    }

    @Override
    public List<ProjectAnalysisVo> queryProjectIndustryDistributionListNew(String startDate, String endDate) {
        List<ProjectAnalysisVo> projectAnalysisVoList = new ArrayList<>();

        List<SectionSpecialty> listRaw = projectAnalysisMapper.statisticalIndustryDistributionNew(startDate, endDate);
        if(CollectionUtils.isNotEmpty(listRaw)){
            Map<String,SectionSpecialty> map = new HashMap<>();
            for (SectionSpecialty sectionSpecialty : listRaw) {
                Long expertSpecialtyId = sectionSpecialty.getExpertSpecialtyId();
                Industry industry = projectAnalysisMapper.queryParentIndustryById(expertSpecialtyId);
                //code长度为5代表是三级分类,需要往上再查一层
                if(industry.getCode().length() == 5){
                    industry = projectAnalysisMapper.queryParentIndustryById(industry.getId());
                }
                sectionSpecialty.setSecondLevelSpecialtyCode(industry.getCode());
                sectionSpecialty.setSecondLevelSpecialtyName(industry.getName());
                //过滤掉同一标段选择多个二级分类相同的行业
                map.put(sectionSpecialty.getSectionId()+sectionSpecialty.getSecondLevelSpecialtyCode(),sectionSpecialty);
            }
            ProjectAnalysisVo eng = new ProjectAnalysisVo();
            ProjectAnalysisVo good = new ProjectAnalysisVo();
            ProjectAnalysisVo service = new ProjectAnalysisVo();
            List<ProjectAnalysis> listEng = new ArrayList<>();
            List<ProjectAnalysis> listGood = new ArrayList<>();
            List<ProjectAnalysis> listService = new ArrayList<>();
            Map<String,ProjectAnalysis> mapProjectAnalysis = new HashMap<>();

            BigDecimal totalMoney = new BigDecimal(0);

            for (Map.Entry<String,SectionSpecialty> entry: map.entrySet()){

                SectionSpecialty specialty = entry.getValue();
                //累计总金额
                totalMoney = totalMoney.add(specialty.getEntrustMoney());

                ProjectAnalysis projectAnalysis = mapProjectAnalysis.get(specialty.getSecondLevelSpecialtyCode());
                if(null == projectAnalysis){
                    projectAnalysis = new ProjectAnalysis();
                    if(specialty.getSecondLevelSpecialtyCode().startsWith(ProjectAnalysisConstants.ENGINEERING_CODE)){//工程
                        projectAnalysis.setEngineeringClass(ProjectAnalysisConstants.ENGINEERING);
                    } else if (specialty.getSecondLevelSpecialtyCode().startsWith(ProjectAnalysisConstants.GOODS_CODE)) {//货物
                        projectAnalysis.setEngineeringClass(ProjectAnalysisConstants.GOODS);
                    } else if (specialty.getSecondLevelSpecialtyCode().startsWith(ProjectAnalysisConstants.SERVICE_CODE)) {//服务
                        projectAnalysis.setEngineeringClass(ProjectAnalysisConstants.SERVICE);
                    }
                    projectAnalysis.setProjectIndustry(specialty.getSecondLevelSpecialtyName());
                    projectAnalysis.setProjectCount(1);
                    projectAnalysis.setEntrustMoney(specialty.getEntrustMoney());
                    mapProjectAnalysis.put(specialty.getSecondLevelSpecialtyCode(),projectAnalysis);

                }else {
                    projectAnalysis.setProjectCount(projectAnalysis.getProjectCount() + 1);
                    projectAnalysis.setEntrustMoney(projectAnalysis.getEntrustMoney().add(specialty.getEntrustMoney()));
                }


            }
            //总金额转万元
            totalMoney = totalMoney.divide(ProjectAnalysisConstants.TEN_THOUSAND,6, RoundingMode.HALF_UP);
            for (Map.Entry<String,ProjectAnalysis> entry: mapProjectAnalysis.entrySet()){
                ProjectAnalysis pas = entry.getValue();
                pas.setEntrustMoney(pas.getEntrustMoney().compareTo(BigDecimal.ZERO) != 0 ? pas.getEntrustMoney().divide(ProjectAnalysisConstants.TEN_THOUSAND,6, RoundingMode.HALF_UP) : pas.getEntrustMoney());
                pas.setProportion(pas.getEntrustMoney().compareTo(BigDecimal.ZERO) != 0 && totalMoney.compareTo(BigDecimal.ZERO) != 0 ? pas.getEntrustMoney().divide(totalMoney,4, RoundingMode.HALF_UP).multiply(new BigDecimal("100")) : pas.getEntrustMoney());

                if(pas.getEngineeringClass().equals(ProjectAnalysisConstants.ENGINEERING)){//工程
                    listEng.add(pas);
                }else if (pas.getEngineeringClass().equals(ProjectAnalysisConstants.GOODS)) {//货物
                    listGood.add(pas);
                } else if (pas.getEngineeringClass().equals(ProjectAnalysisConstants.SERVICE)) {//服务
                    listService.add(pas);
                }
            }
            eng.setFirdtLevel(ProjectAnalysisConstants.ENGINEERING);
            eng.setList(listEng);
            good.setFirdtLevel(ProjectAnalysisConstants.GOODS);
            good.setList(listGood);
            service.setFirdtLevel(ProjectAnalysisConstants.SERVICE);
            service.setList(listService);
            projectAnalysisVoList.add(eng);
            projectAnalysisVoList.add(good);
            projectAnalysisVoList.add(service);
        }
        return projectAnalysisVoList;
    }

    //筛选大项目
    private List<LargeProject> filtrateLargeProject(List<LargeProject> list){
        List<LargeProject> largeProjectList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(list)){
            for (LargeProject projectInfo : list) {
                if(projectInfo.getEntrustCurrency() != ProjectAnalysisConstants.RMB_CODE){//外币
                    //金额转成人民币
                    projectInfo.setEntrustMoney(projectInfo.getEntrustMoney().multiply(projectInfo.getExchangeRate()));
                }
                if(projectInfo.getEntrustMoney().compareTo(ProjectAnalysisConstants.LARGE_PROJECT_DEMARCATION) > -1){//筛选出委托金额大于3000万元的项目
                    largeProjectList.add(projectInfo);
                }
            }
        }
        return largeProjectList;
    }

    //字符串转日期
    private Date parseDateFromString(String str) {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM");
        Date date = null;
        try {
            date = format.parse(str);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return date;
    }

    //判断日期
    private boolean isbetween(Date target,String startDate,String endDate){
        String format1 = DateFormatUtils.format(target, "yyyy-MM");
        target = parseDateFromString(format1);

        Date start = parseDateFromString(startDate);
        Date end = parseDateFromString(endDate);
        return target.getTime() >= start.getTime() && target.getTime() <= end.getTime();
    }

    //筛选出大项目(3000万及以上)
    private List<ProjectInfo> filtrateUp3000(List<ProjectInfo> list){
        List<ProjectInfo> projectInfosUp3000LastYear = new ArrayList<>();
        for (ProjectInfo projectInfo : list) {
//            if(projectInfo.getEntrustedCurrency() != ProjectAnalysisConstants.RMB_CODE){//外币
//                //金额转成人民币
//                projectInfo.setEntrustedAmount(projectInfo.getEntrustedAmount().multiply(new BigDecimal(projectInfo.getExchangeRate())));
//            }
            if(projectInfo.getEntrustedAmount().compareTo(ProjectAnalysisConstants.LARGE_PROJECT_DEMARCATION) > -1){
                projectInfosUp3000LastYear.add(projectInfo);
            }
        }
        return projectInfosUp3000LastYear;
    }

    //计算并排序
   private List<DepartProject> calculateAndSort(List<DepartProject> list){
       for (DepartProject departProject : list) {
           departProject.setMonthCount(departProject.getMonthCount()  == null ? 0 : departProject.getMonthCount());
           departProject.setMonthAmount(departProject.getMonthAmount() == null ? ProjectAnalysisConstants.ZERO4 : departProject.getMonthAmount());
           departProject.setYearCount(departProject.getYearCount() == null ? 0 : departProject.getYearCount());
           departProject.setLastYearCount(departProject.getLastYearCount() == null ? 0 : departProject.getLastYearCount());
           departProject.setYearAmount(departProject.getYearAmount() == null ? ProjectAnalysisConstants.ZERO4 : departProject.getYearAmount());
           departProject.setLastYearAmount(departProject.getLastYearAmount() == null ? ProjectAnalysisConstants.ZERO4 : departProject.getLastYearAmount());
           departProject.setAmountCompared(departProject.getAmountCompared() == null ? ProjectAnalysisConstants.ZERO4 : departProject.getAmountCompared());
           departProject.setCountCompared(departProject.getCountCompared() == null ? ProjectAnalysisConstants.ZERO4 : departProject.getCountCompared());
       }
       for (DepartProject departProject : list) {
           Integer yearCount = departProject.getYearCount();
           Integer lastYearCount = departProject.getLastYearCount();
           BigDecimal yearAmount = departProject.getYearAmount();
           BigDecimal lastYearAmount = departProject.getLastYearAmount();
           if(lastYearCount != 0){
               BigDecimal countCompared = new BigDecimal(yearCount).subtract(new BigDecimal(lastYearCount)).divide(new BigDecimal(lastYearCount),2, RoundingMode.HALF_UP).multiply(new BigDecimal("100"));
               departProject.setCountCompared(countCompared);
           }
           if(lastYearAmount.compareTo(BigDecimal.ZERO) > 0){
               departProject.setAmountCompared((yearAmount.subtract(lastYearAmount)).divide(lastYearAmount,4, RoundingMode.HALF_UP).multiply(new BigDecimal("100")).setScale(2,RoundingMode.HALF_UP));
           }
       }
       Collections.sort(list, (dp1, dp2) -> {
           if (dp1.getAmountCompared().subtract(dp2.getAmountCompared()).intValue() > 0) {
               return -1;
           } else if (dp1.getAmountCompared().subtract(dp2.getAmountCompared()).intValue() < 0) {
               return 1;
           } else {
               if (dp1.getCountCompared().subtract(dp2.getCountCompared()).intValue() < 0) {
                   return 1;
               } else {
                   return -1;
               }
           }
       });
       return list;
   }


}
