package com.hzw.sunflower.controller.response;

import com.hzw.sunflower.dto.PackageInfoDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@ApiModel(description = "包段关联信息 返回实体")
@Data
@Accessors(chain = true)
public class PackageInfoVO {
    @ApiModelProperty(value = "项目是否分包：0 无包；1 有包", position = 1)
    private Integer projectPackageState;

    @ApiModelProperty(value = "包段简要信息列表", position = 2)
    private List<PackageInfoDTO> packages;
}
