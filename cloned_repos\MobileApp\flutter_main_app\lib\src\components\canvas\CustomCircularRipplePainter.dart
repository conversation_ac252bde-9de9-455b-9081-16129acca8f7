///***
/// * 圆形波纹图表画笔
/// */

import 'dart:math';
import 'package:flutter/material.dart';

class CustomCircularRipplePainter extends CustomPainter {

  CustomCircularRipplePainter({
    @required this.number,
    this.unit,
    @required this.yearOnYear,
    @required this.yAxisRatio,
    @required this.xAxisTranslate,
  }) : assert(number != null && number >= 0),
    assert(yearOnYear != null),
    assert(yAxisRatio != null),
    assert(xAxisTranslate != null);

  /// * 数值
  final num number;

  /// * 单位
  final String unit;

  /// * 同比
  final double yearOnYear;

  /// * 上升动画 - y 轴上的位移比
  final double yAxisRatio;

  /// * 右移动画 - x 轴上的位移量
  final double xAxisTranslate;

  /// * 内容区域的占比，108 / 130
  static const double _scale = 0.83;

  @override
  void paint(Canvas canvas, Size size) {

    /// * 需要一个正方形
    final double _side = min(size.width, size.height);
    final double _radius = _side / 2;
    final Size _size = Size(_side, _side);

    /// * 内容部分四角坐标及宽高
    var _contentRect = {
      'topLeft': Offset(_size.width * (1 - _scale) / 2, _size.height * (1 - _scale) / 2),
      'topRight': Offset(_size.width * (1 + _scale) / 2, _size.height * (1 - _scale) / 2),
      'bottomLeft': Offset(_size.width * (1 - _scale) / 2, _size.height * (1 + _scale) / 2),
      'bottomRight': Offset(_size.width * (1 + _scale) / 2, _size.height * (1 + _scale) / 2),
      'width': _size.width * _scale,
      'height': _size.height * _scale,
    };

    _drawGradientBorder(canvas, _size, _radius);

    _drawBackground(canvas, _radius);

    _drawNumberComparison(canvas, _size);

    /// * 裁剪，只显示内容区域
    canvas.clipRRect(
      RRect.fromRectAndRadius(
        Rect.fromCenter(
          center: Offset(_radius, _radius),
          width: size.width * _scale,
          height: size.height * _scale,
        ),
        Radius.circular(_radius * _scale),
      ),
    );

    /// * 波纹波动
    canvas.translate(xAxisTranslate, 0);

    _drawPrimaryRipple(canvas, _size, _radius, _contentRect);

    /// * 抵消波纹波动
    canvas.translate(-xAxisTranslate, 0);

    _drawText(canvas, _radius);

  }

  @override
  bool shouldRepaint(CustomPainter old) => old != this;

  ///***
  /// * 绘制外层边框，圆形，渐变色
  /// *
  /// * @param {Canvas} canvas
  /// * @param {Size} size
  /// * @param {double} radius
  /// */
  void _drawGradientBorder(Canvas canvas, Size size, double radius) {
    /// * 边框渐变色
    const Color _borderGradientStartColor = Color(0xff84a0ef);
    const Color _borderGradientEndColor = Color(0xffdd4545);

    final Paint paint = Paint();

    paint
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.0
      ..shader = LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        tileMode: TileMode.clamp,
        colors: [_borderGradientStartColor, _borderGradientEndColor],
        stops: [0.0, 1.0],
      ).createShader(Rect.fromLTWH(0.0, 0.0, size.width, size.height));

    canvas.drawCircle(Offset(radius, radius), radius, paint);
  }

  ///***
  /// * 绘制内容区域底色，圆形，_scale
  /// *
  /// * @param {Canvas} canvas
  /// * @param {double} radius
  /// */
  void _drawBackground(Canvas canvas, double radius) {
    /// * 背景色
    const Color _backgroundColor = Color(0xfffff8f8);

    final Paint paint = Paint();

    paint
      ..style = PaintingStyle.fill
      ..color = _backgroundColor;

    canvas.drawCircle(Offset(radius, radius), radius * _scale, paint);
  }

  ///***
  /// * 绘制数量对比
  /// *
  /// * @param {Canvas} canvas
  /// * @param {Size} size
  /// */
  void _drawNumberComparison(Canvas canvas, Size size) {
    /// * 下降和上升对应的颜色
    const Color increaseColor = Color(0xffdd3333);
    const Color decreaseColor = Color(0xff4aa827);

    Color color = yearOnYear >= 0 ? increaseColor : decreaseColor;

    /// * 画数字
    final TextSpan textSpan = TextSpan(
      text: '${(yearOnYear.abs() * 100).round()}%',
      style: TextStyle(
        fontSize: 10.0,
        fontWeight: FontWeight.w500,
        color: color,
      ),
    );

    final TextPainter textPainter = TextPainter(
      text: textSpan,
      textDirection: TextDirection.ltr,
      maxLines: 1,
    );

    textPainter.layout();

    textPainter.paint(canvas, Offset(size.width - 0.0, size.height - 15.0));

    /// * 画箭头
    final Paint paint = Paint();

    final Path path = Path();

    paint
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.0
      ..color = color;

    const double arrowSide = 4.0;

    final Offset stickStartPoint = Offset(size.width - 8.0, size.height - 15.0);

    final Offset stickEndPoint = Offset(size.width - 8.0, size.height - 4.0);

    Offset arrowStartPoint;
    Offset arrowLeftEndPoint;
    Offset arrowRightEndPoint;

    if (yearOnYear >= 0) {
      arrowStartPoint = stickStartPoint;
      arrowLeftEndPoint = Offset(arrowStartPoint.dx - arrowSide, arrowStartPoint.dy + arrowSide);
      arrowRightEndPoint = Offset(arrowStartPoint.dx + arrowSide, arrowStartPoint.dy + arrowSide);
    } else {
      arrowStartPoint = stickEndPoint;
      arrowLeftEndPoint = Offset(arrowStartPoint.dx - arrowSide, arrowStartPoint.dy - arrowSide);
      arrowRightEndPoint = Offset(arrowStartPoint.dx + arrowSide, arrowStartPoint.dy - arrowSide);
    }

    path.moveTo(stickStartPoint.dx, stickStartPoint.dy);

    path.lineTo(stickEndPoint.dx, stickEndPoint.dy);

    path.moveTo(arrowStartPoint.dx, arrowStartPoint.dy);

    path.lineTo(arrowLeftEndPoint.dx, arrowLeftEndPoint.dy);

    path.moveTo(arrowStartPoint.dx, arrowStartPoint.dy);

    path.lineTo(arrowRightEndPoint.dx, arrowRightEndPoint.dy);

    canvas.drawPath(path, paint);
  }

  ///***
  /// * 绘制主波纹
  /// *
  /// * @param {Canvas} canvas
  /// * @param {Size} size
  /// * @param {double} radius
  /// */
  void _drawPrimaryRipple(Canvas canvas, Size size, double radius, contentRect) {
    /// * 主波纹底渐变色
    const Color _primaryRippleStartColor = Color(0xff84a0ef);
    const Color _primaryRippleEndColor = Color(0xffdd4545);

    final Paint paint = Paint();

    final Path path = Path();

    /// * y 轴上的位移量
    Offset _yAxisOffset = Offset(0, -yAxisRatio * size.height);

    paint
      ..style = PaintingStyle.fill
      ..shader = LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        tileMode: TileMode.clamp,
        colors: [_primaryRippleStartColor, _primaryRippleEndColor],
        stops: [0.0, 1.0],
      ).createShader(Rect.fromLTWH(
        contentRect['topLeft'].dx - xAxisTranslate,
        contentRect['topLeft'].dy,
        contentRect['width'],
        contentRect['height'],
      ));

    /// * 定义面积左下角
    path.moveTo(-1 * size.width, contentRect['bottomLeft'].dy);

    /// * 主波纹路径
    /// * 
    Offset startPoint = Offset(
      -1 * size.width,
      contentRect['bottomLeft'].dy,
    ) + _yAxisOffset;

    path.lineTo(startPoint.dx, startPoint.dy);

    void _cubicTo(int index) {
      const double fraction = 0.25;

      const double controllPointXAxisOffset = 0.035;

      const double controllPointYAxisOffsetRatio = 0.05;

      final int quadrant = index % 2 == 0 ? -1 : 1;

      final double centerXAxisRatio = fraction * (index +  0.5);

      final double controlPointRatio = 1 + quadrant * controllPointYAxisOffsetRatio;

      Offset leftControllPoint = Offset(
        size.width * (centerXAxisRatio - controllPointXAxisOffset),
        contentRect['bottomLeft'].dy * controlPointRatio,
      ) + _yAxisOffset;

      Offset rightControllPoint = Offset(
        size.width * (centerXAxisRatio + controllPointXAxisOffset),
        contentRect['bottomLeft'].dy * controlPointRatio,
      ) + _yAxisOffset;

      Offset endPoint = Offset(
        size.width * fraction * (index + 1),
        contentRect['bottomLeft'].dy,
      ) + _yAxisOffset;

      path.cubicTo(
        leftControllPoint.dx, leftControllPoint.dy,
        rightControllPoint.dx, rightControllPoint.dy,
        endPoint.dx, endPoint.dy,
      );
    }

    for (int i = -4; i < 4; i++) {
      _cubicTo(i);
    }

    /// * 定义面积右下角
    path.lineTo(size.width, contentRect['bottomRight'].dy);

    canvas.drawPath(path, paint);
  }

  ///***
  /// * 绘制文本
  /// *
  /// * @param {Canvas} canvas
  /// * @param {double} radius
  /// */
  void _drawText(Canvas canvas, double radius) {
    final TextSpan textSpan = TextSpan(
      text: '$number$unit',
      style: TextStyle(
        height: 1.4,
        fontSize: 13.0,
        fontWeight: FontWeight.bold,
        color: Colors.white,
      ),
    );

    final TextPainter textPainter = TextPainter(
      text: textSpan,
      textAlign: TextAlign.center,
      textDirection: TextDirection.ltr,
      maxLines: 1,
    );

    textPainter.layout(
      minWidth: radius * 2,
      maxWidth: radius * 2,
    );

    textPainter.paint(canvas, Offset(0.0, radius - 13.0 * 1.4 / 2));
  }

}
