package com.hzw.sunflower.controller.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Map;


@Data
@AllArgsConstructor
@NoArgsConstructor
public class UserStatisticsReq {
    @ApiModelProperty(value = "开始时间")
    private String startTime;
    @ApiModelProperty(value = "结束时间")
    private String endTime;
    @ApiModelProperty(value = "去年开始时间")
    private String startTimeAgo;
    @ApiModelProperty(value = "去年结束时间")
    private String endTimeAgo;
    @ApiModelProperty(value = "关键字搜索 1：全公司  2：处室  3：人员")
    private String keyWords;
    @ApiModelProperty(value = "总计")
    private Map<String,String> summary;
    @ApiModelProperty(value = "排序（1.正序 2.倒叙）")
    private String order ;
    @ApiModelProperty(value = "排序字段（1.projectCountBasis 2.entrustedAmountBasis）")
    private String prop ;

}
