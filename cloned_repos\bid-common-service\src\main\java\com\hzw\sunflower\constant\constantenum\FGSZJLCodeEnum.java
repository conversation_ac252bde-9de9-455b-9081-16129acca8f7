package com.hzw.sunflower.constant.constantenum;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public enum FGSZJLCodeEnum {

    FGSZJL(RoleCodeEnum.FGSZJL.getType(),RoleCodeEnum.FGSZJL.getDesc()),
    FGSZJLNJ(RoleCodeEnum.FGSZJLNJ.getType(),RoleCodeEnum.FGSZJLNJ.getDesc()),
    FGSZJLXZ(RoleCodeEnum.FGSZJLXZ.getType(),RoleCodeEnum.FGSZJLXZ.getDesc()),
    FGSZJLXM(RoleCodeEnum.FGSZJLXM.getType(),RoleCodeEnum.FGSZJLXM.getDesc()),
    ;

    private String code;
    private String desc;

    FGSZJLCodeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String type) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    //讲枚举转换成list格式，这样前台遍历的时候比较容易，列如 下拉框 后台调用toList方法，你就可以得到code 和name了
    public static Map<String, String> toMap() {
        Map<String, String> map = new HashMap<String, String>();
        for (FGSZJLCodeEnum airlineTypeEnum : FGSZJLCodeEnum.values()) {
            map.put(airlineTypeEnum.getCode(), airlineTypeEnum.getCode());
        }
        return map;
    }


    public static List<String> toList() {
        List<String> list = new ArrayList<>();
        for (FGSZJLCodeEnum airlineTypeEnum : FGSZJLCodeEnum.values()) {
            list.add(airlineTypeEnum.getCode());
        }
        return list;
    }
}
