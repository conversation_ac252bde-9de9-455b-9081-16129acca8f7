package com.hzw.sunflower.constant;

/**
 * 投标文件相似度对比状态 1对比中 2对比完成
 */
public enum AnalysisStatusEnum {

    COMPARE_ING(1, "对比中"),
    COMPARE_ALREADY(2, "对比完成");

    private Integer value;
    private String name;

    AnalysisStatusEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    public Integer getValue() {
        return value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
