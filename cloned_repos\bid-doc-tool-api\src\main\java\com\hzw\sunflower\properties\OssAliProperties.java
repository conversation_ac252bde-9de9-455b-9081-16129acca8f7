package com.hzw.sunflower.properties;


import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 */
@Data
@Configuration
public class OssAliProperties {

    @Value("${oss.aliyun.endpoint:}")
    private String endpoint ;

    @Value("${oss.aliyun.accessKeyId:}")
    private String accessKeyId ;

    @Value("${oss.aliyun.accessKeySecret:}")
    private String accessKeySecret;

    @Value("${oss.aliyun.bucketName:}")
    private String bucketName;

}
