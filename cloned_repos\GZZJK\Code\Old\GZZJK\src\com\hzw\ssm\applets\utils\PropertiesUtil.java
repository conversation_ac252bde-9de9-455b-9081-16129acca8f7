package com.hzw.ssm.applets.utils;

import java.io.InputStream;
import java.util.Properties;

public class PropertiesUtil {

    public static String get(String key) {
        try {
            Properties versionProperties = new Properties();
            InputStream in = PropertiesUtil.class.getClassLoader().getResourceAsStream("config.properties");
            versionProperties.load(in);
            return versionProperties.getProperty(key);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
}
