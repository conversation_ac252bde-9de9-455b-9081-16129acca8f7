package com.hzw.sunflower.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hzw.sunflower.controller.project.request.AgentMyProjectREQ;
import com.hzw.sunflower.controller.request.RefundManageReq;
import com.hzw.sunflower.controller.request.RelationNoRefundReq;
import com.hzw.sunflower.dto.BondRefundManageDto;
import com.hzw.sunflower.dto.BondRelationNoRefundDto;
import com.hzw.sunflower.dto.MyProjectListDTO;
import com.hzw.sunflower.entity.BidWinPeople;
import com.hzw.sunflower.entity.BondApplyRefund;
import com.hzw.sunflower.entity.BondRelation;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * <p>
 * 保证金退还申请表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-27
 */
@Mapper
public interface BondRefundCountMapper extends BaseMapper<BondApplyRefund> {

    /**
     * 查询保证金退还管理项目
     * @param page
     * @param condition
     * @return
     */
    IPage<BondRefundManageDto> queryBondRefundManagePage(IPage<BondRefundManageDto> page, @Param("condition") RefundManageReq condition);

    /**
     * 查询保证金已关联未退回数据
     * @param page
     * @param condition
     * @return
     */
    IPage<BondRelationNoRefundDto> queryBondRelationNoRefundPage(IPage<BondRelationNoRefundDto> page, @Param("condition") RelationNoRefundReq condition);
    /**
     * 查询保证金已关联未退回excel导出数据
     * @param condition
     * @return
     */
    List<BondRelationNoRefundDto> queryBondRelationNoRefundExcel(@Param("condition") RelationNoRefundReq condition);

    /**
     * 查询关联供应商总数
     * @param sectionId
     * @return
     */
    List<BondRelation> relationCompanyCount(@Param("sectionId") Long sectionId);

}
