package com.hzw.sunflower.service;

import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hzw.ncc.client.dto.BondReceiveDto;
import com.hzw.sunflower.common.Paging;
import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.controller.request.BondWaterRemarkReq;
import com.hzw.sunflower.controller.response.*;
import com.hzw.sunflower.dto.NjBankWaterDetailDto;
import com.hzw.sunflower.entity.BondTemplate;
import com.hzw.sunflower.entity.BondWater;
import com.hzw.sunflower.entity.condition.BondProjectCondition;
import com.hzw.sunflower.entity.condition.BondWaterCondition;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 保证金流水表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-27
 */
public interface BondWaterService extends IService<BondWater> {

    /**
     * 根据标段和项目获取确认关联或已关联的流水
     * @param sectionId
     * @param relationStatus
     * @param companyId
     * @return
     */
    List<BondWater> getWaterListBySectionId(Long sectionId, Integer relationStatus, Long companyId);

    /**
     * 根据标段和项目获取确认关联或已关联的流水（已办）
     * @param sectionId
     * @param relationStatus
     * @param companyId
     * @return
     */
    List<BondWater> getWaterListBySectionIdCompleted(Long sectionId, Integer relationStatus, Long companyId, Date createdTime);

    /**
     * 根据公司id精准搜索流水
     * @param companyName
     * @return
     */
    List<BondWater> getWaterListByCompanyName(String companyName);

    /**
     * 根据公司名称获取全部流水
     * @param companyName
     * @return
     */
    List<BondWaterAllVo> getWaterListByCompanyNameAll(String companyName);


    /**
     * 自定义导入
     * @param inputStream
     * @param templateId
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    Result<ImportExcelVo> importExcel(Long ossFileId, Long templateId);

    /**
     * 校验重复
     * @param rows
     * @return
     */
    List<BondWater> selectRepeat(List<BondWater> rows, BondTemplate template);

    /**
     * 分页检索收款流水
     * @param condition
     * @return
     */
    BondWaterPageVo getListByCondition(BondWaterCondition condition);

    /**
     * 分页检索其他收款流水
     * @param condition
     * @return
     */
    Paging<BondWaterReturnVo> getListByConditionOther(BondWaterCondition condition);

    /**
     * 导出收款明细（流水）
     * @param condition
     * @param response
     */
    void exportExcel(BondWaterCondition condition, HttpServletResponse response);

    /**
     * 插入保证金流水---中国银行
     * @param jsonArray
     * @return
     */
    Boolean insertChinaBankWaterInfo(JSONArray jsonArray,String bankNo);

    /**
     * 刷新异常流水状态
     * @return
     */
    Boolean saveAbnormalWater();

    /**
     * 异常保证金
     * @param condition
     * @return
     */
    IPage<BondWaterRefundVO> abnormalWaterList(BondProjectCondition condition);

    /**
     * 南京银行保证金流水入库
     * @param detailDtoList
     * @return
     */
    Integer insertNjBankWaterInfo(List<NjBankWaterDetailDto> detailDtoList);

    /**
     * 根据银行信息分组查询流水信息
     * @param splitIds
     * @return
     */
    List<BondWater> getWaterGroupByAccount(String[] splitIds);

    /**
     * 民生银行流水入库
     * @param jsonArray
     * @return
     */
    Integer insertCmbcWaterInfo(JSONArray jsonArray);

    /**
     * 获取上个月未关联流水
     * @return
     */
    List<BondReceiveDto> findNccReceiveWater();

    /**
     * 保证金异常待处理数量
     * @return
     */
    Integer abnormalWaterCount();

    /**
     * 更新保证金流水备注
     * @param bondWaterRemarkReq
     * @return
     */
    Result<String> updateAbnormalWaterRemark(BondWaterRemarkReq bondWaterRemarkReq);
  //  Boolean saveHistoryReceiveWater();
}
