#!/bin/sh
#cp TEMPLATE FILE
docker stop portal-seata-server || true
docker rm portal-seata-server || true
docker rmi registry.cn-hangzhou.aliyuncs.com/hzw-jtcc/jtcc:portal-seata-server-1.0.0 || true
docker pull registry.cn-hangzhou.aliyuncs.com/hzw-jtcc/jtcc:portal-seata-server-1.0.0
docker run -itd --net=host -e SEATA_IP=************** --log-opt max-size=100m --log-opt max-file=3  --name portal-seata-server -v /home/<USER>/plan1/portal-cloud/config/portal-seata-server:/hzw/seata-server/config -v /data/log:/hzw/seata-server/logs  registry.cn-hangzhou.aliyuncs.com/hzw-jtcc/jtcc:portal-seata-server-1.0.0
