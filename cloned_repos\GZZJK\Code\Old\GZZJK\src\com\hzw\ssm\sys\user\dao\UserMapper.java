package com.hzw.ssm.sys.user.dao;

import java.util.List;
import java.util.Map;

import com.hzw.ssm.sys.system.entity.MenuEntity;
import org.apache.ibatis.annotations.Param;

import com.hzw.ssm.sys.user.entity.HomeQrEntity;
import com.hzw.ssm.sys.user.entity.UserEntity;

public interface UserMapper {

	/**
	 * 查询用户列表
	 *
	 * @param userEntity
	 *            用户查询条件实体对象
	 * @return 实体对象集合
	 */
	public List<UserEntity> queryPageUser(UserEntity userEntity);

	/**
	 * 查询用户列表记录条数
	 *
	 * @param userEntity
	 *            用户查询条件实体对象
	 * @return 记录条数
	 */
	public int selectUserCount(UserEntity userEntity);

	/**
	 * 查询用户实体，给页面展示并编辑
	 *
	 * @param id
	 *            根据前一画面传入的用户编号
	 * @return 根据用户编号查询出的用户实体对象
	 */
	public UserEntity selectUserById(String id);

	/**
	 * 查询用户编号是否存在
	 *
	 * @param userEntity
	 *            用户实体对象
	 * @return 用户编号
	 */
	public String selectUserByLoginId(UserEntity userEntity);

	/**
	 * 新增用户对象
	 *
	 * @param userEntity
	 *            用户实体对象
	 */
	public void insertUser(UserEntity userEntity);

	/**
	 * 更新用户对象
	 *
	 * @param userEntity
	 *            用户实体对象
	 */
	public void updateUser(UserEntity userEntity);

	/**
	 * 删除用户信息
	 * @param ids 用户编号
	 */
	public void deleteUser(UserEntity userEntity);

	/**
	 * 禁用用户
	 *
	 * @param id
	 *            用户编号
	 */
	public void invalidUser(UserEntity userEntity);

	/**
	 * 重新启用用户
	 *
	 * @param id
	 *            用户编号
	 */
	public void validUser(UserEntity userEntity);

	/**
	 * 重置用户密码
	 *
	 * @param entity
	 *            用户对象
	 */
	public void resetPassword(UserEntity userEntity);

	/**
	 * 用户解锁
	 * @param id
	 */
	public void unlockUser(UserEntity userEntity);

	/**
	 * 用户修改密码
	 */
	public void changePassword(UserEntity userEntity);

	/**
	 * 发送邮件时查询用户
	 *
	 * @param userEntity
	 * @return
	 */
	public List<UserEntity> queryUserList(UserEntity userEntity);

	/**
	 * 根据用户多个id查询用户信息
	 *
	 * @param ids
	 * @return
	 */
	public List<UserEntity> queryUserListByIds(String ids);

	/**
	 * 用户信息自己信息
	 * @param objUser
	 */
	public void updatePersonnalUser(UserEntity objUser);

	/**
	 * 验证登录名是否存在
	 * @return
	 */
	public int checkLoginCode(UserEntity objUser);

	/**
	 * 验证手机号码是否存在
	 * @param mobile
	 * @return
	 */
	public int checkMobile(UserEntity objUser);

	/**
	 * 根据用户编号查询用户信息
	 * @param objUser
	 * @return
	 */
	public List<UserEntity> getUserInfoByUserId(UserEntity objUser);

	/**
	 * 查询机电交易中心抽取人的手机号码
	 * @param role_name
	 * @return
	 */
	public List<UserEntity> queryExtractMobiles(String role_name);
	/**
	 * 查询用户权限
	 * @param map
	 * @return
	 */
	public int queryUrlByIdAndUrl(Map<String,Object> map);

	/**
	 * 查询用户by usernmae
	 * @return
	 */
	public UserEntity queryUser (String userName);
	/**
	 * 验证短信验证码是否正确
	 * @return
	 */
	public UserEntity checkLogin(UserEntity userInfo);

	public int selectUserRcode(String code);

	public void updateQCode(UserEntity user);

	public List<HomeQrEntity> selectRecommendExamine(String qrCode);

	public Integer selectRecommend(@Param("user_id")String user_id, @Param("newdate")String newdate);

	public int checkExtract(String userId);

	public List<String> selectUserJurisdiction(String userId);
}
