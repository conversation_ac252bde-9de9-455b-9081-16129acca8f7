package com.hzw.sunflower.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzw.ncc.client.dto.BondReceiveBodyDto;
import com.hzw.ncc.client.dto.BondReceiveDto;
import com.hzw.sunflower.common.Paging;
import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.constant.CommonConstants;
import com.hzw.sunflower.constant.constantenum.*;
import com.hzw.sunflower.constants.NccBankAccountTypeEnum;
import com.hzw.sunflower.constants.NccBankTypeEnum;
import com.hzw.sunflower.constants.NccConstants;
import com.hzw.sunflower.constants.NccPushTypeEnum;
import com.hzw.sunflower.controller.request.BondRefundBalanceChangeReq;
import com.hzw.sunflower.controller.request.BondWaterRemarkReq;
import com.hzw.sunflower.controller.response.*;
import com.hzw.sunflower.dao.BondWaterMapper;
import com.hzw.sunflower.dto.*;
import com.hzw.sunflower.entity.*;
import com.hzw.sunflower.entity.condition.BondProjectCondition;
import com.hzw.sunflower.entity.condition.BondWaterCondition;
import com.hzw.sunflower.service.*;
import com.hzw.sunflower.util.*;
import com.hzw.sunflower.util.excel.CustomMergeStrategy;
import com.hzw.sunflower.util.excel.ExcelUtil;
import com.hzw.sunflower.util.oss.OssFileStorage;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 保证金流水表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-27
 */
@Service
public class BondWaterServiceImpl extends ServiceImpl<BondWaterMapper, BondWater> implements BondWaterService {

    @Autowired
    private BondRelationService bondRelationService;

    @Autowired
    BondTemplateService templateService;

    @Autowired
    private UserService userService;

    @Autowired
    private BondRefundWaterService bondRefundWaterService;

    @Autowired
    private CustomerToNccService customerToNccService;

    @Autowired
    private CompanyService companyService;

    @Autowired
    private BankdataBondToNccService bankdataBondToNccService;

    @Autowired
    private BondApplyRefundService bondApplyRefundService;

    @Autowired
    private NccBondPushLogService pushLogService;

    @Autowired
    private BondRefundService bondRefundService;

    @Autowired
    private OssFileService ossFileService;

    @Value("${oss.active}")
    private String ossType;

    @Autowired
    private BondRefundBalanceService bondRefundBalanceService;

    @Autowired
    private BondSplitService bondSplitService;

    @Autowired
    private CnapsBankCodeService cnapsBankCodeService;


    /**
     * 根据标段和项目获取确认关联或已关联的流水
     * @param sectionId
     * @param relationStatus
     * @param companyId
     * @return
     */
    @Override
    public List<BondWater> getWaterListBySectionId(Long sectionId, Integer relationStatus, Long companyId) {
        return this.baseMapper.getWaterListBySectionId(sectionId, relationStatus, companyId);
    }

    /**
     * 根据标段和项目获取确认关联或已关联的流水（已办）
     * @param sectionId
     * @param relationStatus
     * @param companyId
     * @return
     */
    @Override
    public List<BondWater> getWaterListBySectionIdCompleted(Long sectionId, Integer relationStatus, Long companyId,Date createdTime ) {
        return this.baseMapper.getWaterListBySectionIdCompleted(sectionId, relationStatus, companyId,createdTime);
    }

    /**
     * 根据公司名称获取未关联流水
     * @param companyName
     * @return
     */
    @Override
    public List<BondWater> getWaterListByCompanyName(String companyName) {
        return this.baseMapper.getWaterListByCompanyName(companyName);
    }

    /**
     * 根据公司名称获取全部流水
     * @param companyName
     * @return
     */
    @Override
    public List<BondWaterAllVo> getWaterListByCompanyNameAll(String companyName) {
        return this.baseMapper.getWaterListByCompanyNameAll(companyName);
    }


    /**
     * 自定义导入
     * @param
     * @param templateId
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<ImportExcelVo> importExcel(Long ossFileId, Long templateId) {
        BondTemplate template = templateService.getById(templateId);
        // 当模板只有一个时间时，日期和时间表头都填写了同一个字段，将时间置为空
        if (template.getTime() != null && template.getTime().equals(template.getDate())) {
            template.setTime("");
        }
        ImportExcelVo vo = new ImportExcelVo();
        //下载文件
        OssFile ossFile = ossFileService.getOssFileById(ossFileId);
        OssFileStorage storage = OssUtil.getOssFileStorage(ossType);
        InputStream inputStream = null;
        if(null != ossFile && null != ossFile.getOssFileKey() ){
            byte[] bytes = storage.downloadFile(ossFile.getOssFileKey());
            inputStream = FileUtils.byte2InputStream(bytes);
        }
        // 读取excel，初次格式过滤
        Map<String, List<BondWater>> map = ExcelUtil.read(inputStream, BondWaterExcelDto.class, template);
        List<BondWater> rows = BeanListUtil.convertList(map.get("rows"), BondWater.class);
        List<BondWater> failRows = BeanListUtil.convertList(map.get("failRows"), BondWater.class);
        vo.setCount(rows.size() + failRows.size());
        if (failRows.size() > 0) {
            vo.setFailRows(failRows);
            vo.setFailCount(failRows.size());
            vo.setFailType(ImportFailTypeEnum.FAIL_FORMAT_ERROR.getType());
            return Result.failed("收款流水上传失败！", vo);
        }
        // 二次过滤 校验数据重复
        List<BondWater> repeatRows = this.selectRepeat(rows,template);
        if (repeatRows.size() > 0) {
            vo.setFailRows(repeatRows);
            vo.setFailCount(repeatRows.size());
            vo.setFailType(ImportFailTypeEnum.FAIL_REPEAT.getType());
            return Result.failed("收款流水上传失败！", vo);
        }
        // 入库 查询流水模板对应的收款账号
        LambdaQueryWrapper<BankdataBondToNcc> queryWrapper = new LambdaQueryWrapper<BankdataBondToNcc>();
        queryWrapper.eq(BankdataBondToNcc::getAccountType, NccBankAccountTypeEnum.BOND.getType());
        // todo 动态获取银行类型
        queryWrapper.eq(BankdataBondToNcc::getBankType, NccBankTypeEnum.getEnum(template.getBankName()).getType());
        BankdataBondToNcc bankData = bankdataBondToNccService.getOne(queryWrapper);
        rows.forEach((item) -> {
            item.setSource(BondWaterSourceEnum.EXCEL.getType());
            if (StringUtils.isBlank(item.getReceiveBank())) {
                item.setReceiveBank(template.getBankName());
            }
            // 当收款账号为空时，填入ncc银行配置表对应账号
            if (StringUtils.isBlank(item.getReceiveAcount()) && bankData != null) {
                item.setReceiveAcount(bankData.getAccount());
            }
            if(StringUtils.isBlank(item.getTime())){
                item.setTime("00:00:00");
            }
            //上传后，根据开户行行名去南京银行库中查询对应行号并补全行号，若无库或库中无数据则不做处理
            if (StringUtils.isNotEmpty(item.getCompanyBankDeposit())) {
                LambdaQueryWrapper<CnapsBankCode> cnapsBankCodeLambdaQueryWrapper = new LambdaQueryWrapper<>();
                cnapsBankCodeLambdaQueryWrapper.eq(CnapsBankCode::getOrgFullname,item.getCompanyBankDeposit());
                cnapsBankCodeLambdaQueryWrapper.last("limit 1");
                CnapsBankCode cnapsBankCodeServiceOne = cnapsBankCodeService.getOne(cnapsBankCodeLambdaQueryWrapper);
                if (cnapsBankCodeServiceOne != null && cnapsBankCodeServiceOne.getBankCode() != null) {
                    item.setCompanyBankCode(cnapsBankCodeServiceOne.getBankCode());
                }
            }
            //付款户名去前后空格
            item.setCompanyName(item.getCompanyName().trim());

        });
        //重复流水不导入
        BigDecimal amount = new BigDecimal(0);
        //
        for(BondWater water:rows){
            LambdaQueryWrapper<BondWater> repeatWrapper = new LambdaQueryWrapper<>();
            if (StringUtils.isBlank(water.getWaterNumber())) {
                repeatWrapper.eq(BondWater::getAmount, water.getAmount()).eq(BondWater::getDate, water.getDate())
                        .eq(BondWater::getTime, water.getTime()).eq(BondWater::getCompanyName, water.getCompanyName());
            } else {
                repeatWrapper.eq(BondWater::getWaterNumber, water.getWaterNumber()).eq(BondWater::getDate, water.getDate())
                        .eq(BondWater::getTime, water.getTime()).eq(BondWater::getReceiveBank, water.getReceiveBank());
            }
            if (this.count(repeatWrapper) == 0) {
                //付款户名为“江苏省招标中心有限公司”的收款流水，不记录到系统
                if(water.getCompanyName().equals(NjBankConstans.COMPANY_NAME_SELF)){
                    BondWaterToNccDto item = BeanListUtil.convert(water,BondWaterToNccDto.class);
                    bondSplitService.saveNccReceiveOtherPushLog(item);
                    continue;
                }
                //保存流水
                boolean insertFlag = this.save(water);
                if (insertFlag) {
                    BondRefundBalanceChangeReq req = new BondRefundBalanceChangeReq();
                    req.setRefundNumber(water.getCompanyAccount());
                    req.setTotalAmountAdd(water.getAmount());
                    // 计算总金额、自动关联
                    bondRelationService.autoRelate(water);
                    amount = amount.add(new BigDecimal(water.getAmount()));
                    //对于手动导入的流水，判断流水到账时间如果是上个月的数据，则推送隔月收款的单据
                    if(DateUtil.isUpMonth(water.getDate())){
                        pushUpMonthReceipt(water);
                    }
                }
            }
        }
        //更新余额表
        bondRefundBalanceService.batchSaveOrUpdateChange(rows);
        // 保存ncc客商信息
        List<String> customerNameList = rows.stream().map(m -> {
            return m.getCompanyName();
        }).distinct().collect(Collectors.toList());
        customerToNccService.addCustomerBatch(customerNameList);
        vo.setAmount(amount);
        return Result.ok(vo);
    }



    /**
     * 推送隔月收款的单据
     * @param item
     */
    private void pushUpMonthReceipt(BondWater item){
        //查询客商编号
        LambdaQueryWrapper<CustomerToNcc> customerQuery = new LambdaQueryWrapper<>();
        customerQuery.eq(CustomerToNcc::getName,item.getCompanyName())
                .orderByDesc(CustomerToNcc::getId).last("limit 1");
        CustomerToNcc one = customerToNccService.getOne(customerQuery);
        CustomerToNcc customerToNcc;
        if(one != null){
            customerToNcc = one;
        }else{
            //若客商不存在，新增一条放到待推送列表
            customerToNcc = customerToNccService.addCustomer(item.getCompanyName());
        }

        BondReceiveDto receiveDto = new BondReceiveDto();
        // 表头属性
        receiveDto.setBillId(UUID.randomUUID().toString().replaceAll("-", ""));
        receiveDto.setCreationTime(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, new Date()));
        // 表体
        List<BondReceiveBodyDto> bodys = new ArrayList<>();
        BondReceiveBodyDto body = new BondReceiveBodyDto();
        body.setPurchaseNumber(NccConstants.OTHER_PURCHASE_NUMBER);
        body.setPkDeptId(NccConstants.OTHER_DEPTID);
        body.setReceiveTime(item.getDate()+" "+item.getTime());
        body.setRecAccount(item.getReceiveAcount());
        body.setAmount(item.getAmount());
        body.setCustomer(customerToNcc.getCode());
        //用于重新查询code
        body.setCustomerId(customerToNcc.getId());
        body.setWaterId(item.getId());
        bodys.add(body);
        receiveDto.setBody(bodys);

        NccBondPushLog pushLog = new NccBondPushLog();
        pushLog.setBillId(receiveDto.getBillId());
        pushLog.setPushType(NccPushTypeEnum.RECEIVE.getType());
        pushLog.setRequestData(JSONObject.toJSONString(receiveDto));
        pushLog.setPushResult(CommonConstants.NO2);
        pushLogService.save(pushLog);
    }


    /**
     * 查询导入的重复流水
     * @param rows
     * @return
     */
    @Override
    public List<BondWater> selectRepeat(List<BondWater> rows,BondTemplate template) {
        List<BondWater> repeatRows = new ArrayList<>();
        // 有流水号校验流水号，无流水号则校验时间+金额+供应商
        // 流水号：流水号+日期时间+银行编码
        for (BondWater water : rows) {
            String s = water.getWaterNumber() + water.getDate() + water.getTime() + template.getId();
            String replace = s.replace("-", "").replace(":", "");
            water.setWaterNumber(replace);
            LambdaQueryWrapper<BondWater> queryWrapper = new LambdaQueryWrapper<>();
            if (StringUtils.isBlank(water.getWaterNumber())) {
                queryWrapper.eq(BondWater::getAmount, water.getAmount()).eq(BondWater::getDate, water.getDate())
                        .eq(BondWater::getTime, water.getTime()).eq(BondWater::getCompanyName, water.getCompanyName());
            } else {
                queryWrapper.eq(BondWater::getWaterNumber, water.getWaterNumber());
            }
            if (this.count(queryWrapper) > 0) {
                repeatRows.add(water);
            }
        }
        return repeatRows;
    }

    /**
     * 分页检索收款流水
     * @param condition
     * @return
     */
    @Override
    public BondWaterPageVo getListByCondition(BondWaterCondition condition) {
        BondWaterPageVo vo = new BondWaterPageVo();
        IPage<BondWaterDto> page = condition.buildPage();
        // 获取检索流水总金额
        page = this.getBaseMapper().getListByCondition(page, condition);
        List<BondWaterDto> records = page.getRecords();
        if(null != records && records.size() > 0){
            for (BondWaterDto record : records) {
                //查询是否有申请退回记录
                LambdaQueryWrapper<BondApplyRefund> lwq  =new LambdaQueryWrapper<>();
                lwq.eq(BondApplyRefund::getWaterId,record.getId());
                long count = bondApplyRefundService.count(lwq);
                record.setReturnaApplyNumer((int)count);
                //查询是否退回数据
                LambdaQueryWrapper<BondRefund> lwqr  =new LambdaQueryWrapper<>();
                lwqr.eq(BondRefund::getWaterId,record.getId());
                long countr = bondRefundService.count(lwqr);
                record.setReturnaNumer((int)countr);
            }
        }
        vo.setTotalAmount(getAllMountFromWater(this.baseMapper.getListByConditionForExcel(condition)));
        vo.setPageData(Paging.buildPaging(page));
        return vo;
    }

    /**
     * 分页检索其他收款流水
     * @param condition
     * @return
     */
    @Override
    public Paging<BondWaterReturnVo> getListByConditionOther(BondWaterCondition condition) {
        BondWaterPageVo vo = new BondWaterPageVo();
        IPage<BondWaterReturnVo> page = condition.buildPage();
        // 获取检索流水总金额
        page = this.getBaseMapper().getListByConditionOther(page, condition);
        Paging<BondWaterReturnVo> paging = Paging.buildPaging(page);
        return paging;
    }

    /**
     * 计算总金额
     * @param list
     * @return
     */
    public BigDecimal getAllMountFromWater (List<BondWaterDto> list) {
        BigDecimal amount = new BigDecimal(0);
        for (BondWaterDto dto : list) {
            amount = amount.add(new BigDecimal(dto.getAmount()));
        }
        return amount;
    }

    /**
     * 导出收款流水
     * @param condition
     * @param response
     */
    @Override
    public void exportExcel(BondWaterCondition condition, HttpServletResponse response) {
        // 获取总流水数据
        List<BondWaterDto> list = this.getBaseMapper().getListByConditionForExcel(condition);
        // 文件名    表头名
        String fileName = "全部已收款流水明细";
        // 未退还导出时，查询关联标包数据，并过滤已退还数据
        if (condition.getExportType().equals(BondWaterExportTypeEnum.NOT_RETURN.getType())) {
            fileName = "已收款未退还流水明细";
            // 查询关联包数据
            list = completeWater(list);
            // 拆分数据合并单元格
            list = splitData(list);
        }
        // xlsx类型
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        String sheetName = "";
        // 填充表格
        try {
            if (CollectionUtils.isNotEmpty(list)) {
                ExcelWriter excelWrite = EasyExcel.write(response.getOutputStream()).build();
                //全部
                sheetName = "全部";
                excelWrite = makeExcel(sheetName, fileName, list, excelWrite, condition.getExportType());
                excelWrite.finish();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 拆分数据合并单元格
     * @param list
     * @return
     */
    public List<BondWaterDto> splitData(List<BondWaterDto> list) {
        List<BondWaterDto> waterList = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            for (BondSectionExportVo vo : list.get(i).getSectionExportList()) {
                BondWaterDto dto = new BondWaterDto();
                BeanUtils.copyProperties(list.get(i), dto);
                dto.setPackageNumber(vo.getPackageNumber());
                dto.setSplitAmount(vo.getSplitAmount());
                dto.setReturnStatus(vo.getReturnStatus());
                dto.setRowNumber(i+1);
                waterList.add(dto);
            }
        }
        return waterList;
    }

    /**
     * 完善关联标包数据，过滤已退还数据
     * @param list
     * @return
     */
    public List<BondWaterDto> completeWater(List<BondWaterDto> list) {
        List<BondWaterDto> waterList = new ArrayList<>();
        for (BondWaterDto dto : list) {
            // 查询流水退还情况
            List<BondSectionExportVo> sectionExportList = this.baseMapper.getSectionExportList(dto.getId());
            if (!checkHasRefund(sectionExportList)) {
                dto.setSectionExportList(sectionExportList);
                waterList.add(dto);
            }
        }
        return waterList;
    }

    /**
     * 校验是否均已退还
     * @param sectionExportList
     * @return
     */
    public Boolean checkHasRefund(List<BondSectionExportVo> sectionExportList) {
        if (sectionExportList.size() == 0) {
            return false;
        }
        Boolean flag = true;
        // 遍历集合 只要有一个不为退还成功就返回
        for (BondSectionExportVo vo : sectionExportList) {
            if (vo.getReturnStatus() == null || !vo.getReturnStatus().equals(BondRefundStatusEnum.SUCCESS.getType())) {
                flag = false;
                break;
            }
        }
        return flag;
    }

    /**
     * 导出
     * @param sheetName
     * @param fileName
     * @param waterList
     * @param excelWrite
     * @param exportType
     * @return
     */
    public ExcelWriter makeExcel(String sheetName, String fileName, List<BondWaterDto> waterList, ExcelWriter excelWrite, Integer exportType) {
        try {
            // 所有行的集合
            List<List<Object>> list = new ArrayList<List<Object>>();
            int n = 1;
            for (BondWaterDto dto : waterList) {
                // 第 n 行的数据
                List<Object> row = new ArrayList<Object>();
                // 序号
                row.add(dto.getRowNumber() == null ? n : dto.getRowNumber());
                // 交易流水
                row.add(dto.getWaterNumber() == null ? "-" : dto.getWaterNumber());
                // 项目编号
                row.add(dto.getPurchaseNumber() == null ? "/" : dto.getPurchaseNumber());
                // 所属处室
                row.add(dto.getDepartmentName() == null ? "/" : dto.getDepartmentName());
                // 付款账号
                row.add(dto.getCompanyAccount());
                // 付款户名
                row.add(dto.getCompanyName());
                // 收款银行
                row.add(dto.getReceiveBank());
                // 到账时间
                row.add(dto.getDate() + " " + dto.getTime());
                // 到账金额
                row.add(dto.getAmount());

                // 已收款未退还需额外展示字段
                if (exportType.equals(BondWaterExportTypeEnum.NOT_RETURN.getType())) {
                    row.add(dto.getPackageNumber() == null ? "/" : dto.getPackageNumber());
                    row.add(dto.getSplitAmount() == null ? "/" : dto.getSplitAmount());
                    if (null != dto.getReturnStatus() && dto.getReturnStatus().equals(BondRefundStatusEnum.SUCCESS.getType())) {
                        row.add("已退还");
                    } else {
                        row.add("未退还");
                    }

                }

                list.add(row);
                n++;
            }
            // 动态添加 表头 headList --> 所有表头行集合
            List<List<String>> headList = new ArrayList<List<String>>();
            String[] title = {"序号", "交易流水", "项目编号", "所属处室", "付款账号", "付款户名", "收款银行", "到账时间", "到账金额"};
            if (exportType.equals(BondWaterExportTypeEnum.NOT_RETURN.getType())) {
                title = new String[]{"序号", "交易流水", "项目编号", "所属处室", "付款账号", "付款户名", "收款银行", "到账时间", "到账总金额"
                , "标包", "标包保证金金额", "退还状态"};
            }
//            String totalAmountDesc = "总金额：" + totalAmount.toString();
            for (int i = 0; i < title.length; i++) {
                List<String> headTitle = new ArrayList<String>();
                headTitle.add(fileName);
//                headTitle.add(totalAmountDesc);
                headTitle.add(title[i]);
                // 组装标题头
                headList.add(headTitle);
            }

            // 头的策略
            WriteCellStyle headWriteCellStyle = new WriteCellStyle();
            // 背景色
            headWriteCellStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
            WriteFont headWriteFont = new WriteFont();
            headWriteFont.setFontHeightInPoints((short) 12);
            headWriteCellStyle.setWriteFont(headWriteFont);
            // 内容的策略
            WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
            WriteFont contentWriteFont = new WriteFont();
            // 字体大小
            contentWriteFont.setFontHeightInPoints((short) 12);
            contentWriteCellStyle.setWriteFont(contentWriteFont);
            //设置 自动换行
            contentWriteCellStyle.setWrapped(true);
            //设置 垂直居中
            contentWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);


            // 这个策略是 头是头的样式 内容是内容的样式 其他的策略可以自己实现
            HorizontalCellStyleStrategy horizontalCellStyleStrategy =
                    new HorizontalCellStyleStrategy(headWriteCellStyle, contentWriteCellStyle);
            if (exportType.equals(BondWaterExportTypeEnum.NOT_RETURN.getType())) {
                List<Integer> rowNumbers = list.stream().map( s -> {
                    return (int) s.get(0);
                }).collect(Collectors.toList());

                WriteSheet writeSheet = EasyExcel.writerSheet(sheetName).head(headList)
                        .registerWriteHandler(horizontalCellStyleStrategy)
                        .registerWriteHandler(new CustomMergeStrategy(rowNumbers, new int[]{0,8}))
                        .build();
                excelWrite.write(list, writeSheet);
            } else {
                WriteSheet writeSheet = EasyExcel.writerSheet(sheetName).head(headList)
                        .registerWriteHandler(horizontalCellStyleStrategy)
                        .build();
                excelWrite.write(list, writeSheet);
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("导出异常");
        }
        return excelWrite;
    }



    /**
     * 插入保证金流水---中国银行
     * @param rs
     * @return
     */
    @Override
    public Boolean insertChinaBankWaterInfo(JSONArray rs,String bankNo) {
        log.debug("插入保证金流水---中国银行");
        Boolean result = true;
        List<BondWater> bondWaters = new ArrayList<>();
        List<BondRefundWater> refundWaters = new ArrayList<>();
        for (int i = 0; i < rs.size(); i++) {
            //付款人信息
            JSONArray fractn = JSONArray.parseArray(rs.getJSONObject(i).getString("fractn"));
            //收款人信息
            JSONArray toactn = JSONArray.parseArray(rs.getJSONObject(i).getString("toactn"));
            String fkactacn = fractn.getJSONObject(0).getString("actacn");//付款账号

            BondWater water = new BondWater();
            water.setWaterNumber(rs.getJSONObject(i).getString("vchnum"));//流水号
            water.setDate(rs.getJSONObject(i).getString("txndate"));//交易日期
            water.setTime(rs.getJSONObject(i).getString("txntime"));//交易时间
            water.setCompanyName(fractn.getJSONObject(0).getString("acntname").trim());//付款人
            water.setCompanyAccount(fractn.getJSONObject(0).getString("actacn"));//供应商付款账户
            water.setCompanyBankDeposit(fractn.getJSONObject(0).getString("ibkname"));//付款开户行
            water.setCompanyBankCode(fractn.getJSONObject(0).getString("ibknum"));//付款银行行号
            water.setAmount(rs.getJSONObject(i).getString("txnamt"));//交易金额
            water.setReceiveAcount(toactn.getJSONObject(0).getString("actacn"));//收款账户
            water.setReceiveBank(toactn.getJSONObject(0).getString("tobank"));//收款银行
            //F://A:OBSS016006167618GIRO1203093244zk//U:JITC-8465AW2997/1//R:
            String furinfo = rs.getJSONObject(i).getString("furinfo");
            if (StringUtils.isNotBlank(furinfo)) {
                water.setPostScript(furinfo);//附言
            }
            water.setSource(BondWaterSourceEnum.BANK_WATER.getType());
            water.setAbnormalStatus(WaterAbnormalStatusEnum.NORMAL.getType());

            //转账记录
            if(fkactacn.equals(bankNo)){
                BondRefundWater refundWater = BeanListUtil.convert(water,BondRefundWater.class);
                refundWater.setInsid(rs.getJSONObject(i).getString("reserve3"));//insid 退还标识
                //根据流水号查询
                LambdaQueryWrapper<BondRefundWater> refundWaterQuery = new LambdaQueryWrapper<>();
                refundWaterQuery.eq(BondRefundWater::getWaterNumber, rs.getJSONObject(i).getString("vchnum"));
                refundWaterQuery.last("limit 1");
                Long hasWater = bondRefundWaterService.count(refundWaterQuery);
                if (hasWater < 1) {
                    refundWaters.add(refundWater);
                }
            }else{//入账记录
                //根据流水号查询
                LambdaQueryWrapper<BondWater> waterQuery = new LambdaQueryWrapper<>();
                waterQuery.eq(BondWater::getWaterNumber, rs.getJSONObject(i).getString("vchnum"));
                waterQuery.last("limit 1");
                Long hasWater = baseMapper.selectCount(waterQuery);
                // 付款户名为“江苏省招标中心有限公司”的收款流水，不记录到系统，只推收款
                if (hasWater < 1) {
                    if(water.getCompanyName().equals(NjBankConstans.COMPANY_NAME_SELF)){
                        //日期转换
                        BondWaterToNccDto item = BeanListUtil.convert(water,BondWaterToNccDto.class);
                        item.setDateTime(item.getDate()+item.getTime());
                        bondSplitService.saveNccReceiveOtherPushLog(item);
                    }else{
                        bondWaters.add(water);
                    }
                }
            }
        }
        //插入保证金流水表
        result = this.saveBatch(bondWaters);
        //插入保证金退还流水表
        bondRefundWaterService.saveBatch(refundWaters);
        //插入保证金余额表
        bondRefundBalanceService.batchSaveOrUpdateChange(bondWaters);
        //自动关联逻辑
        bondWaters.forEach(b->{
            bondRelationService.autoRelate(b);
        });

        // 保存ncc客商信息
        List<String> customerNameList = bondWaters.stream().map(m -> {
            return m.getCompanyName();
        }).distinct().collect(Collectors.toList());
        customerToNccService.addCustomerBatch(customerNameList);

        return  result;
    }

    /**
     * 刷新异常流水状态
     * @return
     */
    @Override
    public Boolean saveAbnormalWater() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        //查询所有正常的未关联流水信息
        List<BondWater> list = this.baseMapper.getNotRelationWater();
        list.forEach(w->{
            int day = 0;
            try {
                day = DateUtils.differentDaysByMillisecond(sdf.parse(w.getDate()), new Date());
            } catch (ParseException e) {
                e.printStackTrace();
            }
            if(day>=45 && day<60){
                //45天未关联状态
                w.setAbnormalStatus(WaterAbnormalStatusEnum.DAY45.getType());
                updateById(w);
                //删除关联待确认数据
                deleteRelation(w.getId());
            }else if(day >= 60){
                w.setAbnormalStatus(WaterAbnormalStatusEnum.DAY60.getType());
                updateById(w);
                //删除关联待确认数据
                deleteRelation(w.getId());
                /*
                //60天未关联，发短信通知主任
                //查询主任的用户手机号
                List<UserIdentity> headPhone = userService.getHeadPhone();
                headPhone.forEach(p->{
                    w.setAbnormalStatus(WaterAbnormalStatusEnum.DAY60.getType());
                    updateById(w);
                    //删除关联待确认数据
                    deleteRelation(w.getId());
                    //发动短信
                    SendSmsDto dto = new SendSmsDto();
                    dto.setIsTemplate(true);
                    dto.setTemplateCode("WITHOUT_RELATION_60");
                    dto.setSendPhone(p.getUserPhone());
                    dto.setSmsContent("存在流水到账超过60天未关联，请您知悉");
                    CommonSmsService commonSmsService= SpringUtils.getBean(CommonSmsService.class);
                    commonSmsService.sendSms(dto);
                });
                 */
            }
        });
        return true;
    }

    /**
     * 删除关联待确认的数据
     * @param waterId
     */
    private void deleteRelation(Long waterId) {
        LambdaQueryWrapper<BondRelation> bondRelationLambdaQueryWrapper = new LambdaQueryWrapper<>();
        bondRelationLambdaQueryWrapper.eq(BondRelation::getWaterId, waterId);
        bondRelationLambdaQueryWrapper.eq(BondRelation::getStatus,RelationStatusEnum.CONFIRM_RELATE.getType());
        List<BondRelation> bondRelationList = bondRelationService.list(bondRelationLambdaQueryWrapper);
        if (bondRelationList.size() > 0) {
            List<Long> relationIds = bondRelationList.stream().map(BondRelation::getId).collect(Collectors.toList());
            bondRelationService.removeBatchByIds(relationIds);
        }
    }

    /**
     * 异常保证金
     * @param condition
     * @return
     */
    @Override
    public IPage<BondWaterRefundVO> abnormalWaterList(BondProjectCondition condition) {
        //1.当前页面查询数据为所有保证金到账时间超过44天的流水数据，即流水到账45天还未关联到标包的数据
        //2. 列表数据按到账时间正序排序
        Page<BondWaterRefundVO> page = condition.buildPage();
        page.setOptimizeCountSql(false);
        Page<BondWaterRefundVO> resultPage = this.baseMapper.abnormalWaterList(page,condition);
        resultPage.getRecords().forEach(p->{
            //根据企业名称查询企业信息
            LambdaQueryWrapper<Company> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(Company::getCompanyName,p.getCompanyName()).last("limit 1");
            Company company = companyService.getOne(queryWrapper);
            if(company != null){
                p.setCompanyId(company.getId());
            }
        });
        return resultPage;
    }

    /**
     * 南京银行保证金流水入库
     * @param detailDtoList
     * @return
     */
    @Override
    public Integer insertNjBankWaterInfo(List<NjBankWaterDetailDto> detailDtoList) {
        List<BondWater> list = new ArrayList<>();
        // 转换成流水实体类
        for (NjBankWaterDetailDto dto : detailDtoList) {
            // 贷方数据
            if (dto.getJiedaibz().equals(BankLoanFlagEnum.LOAN.getCode())) {
                BondWater water = new BondWater();

                String waterNumber = dto.getGuiylius();
                String date = dto.getJiaoyirq(); // ********
                String time = dto.getJysjzifu(); // 13:51:55

                water.setWaterNumber(waterNumber);//流水号
                water.setDate(date);//交易日期
                water.setTime(time);//交易时间
                water.setCompanyName(dto.getDuifminc().trim());//付款人
                water.setCompanyAccount(dto.getDuifkhzh());//供应商付款账户
                water.setCompanyBankDeposit(dto.getDuifjgmc());//付款开户行
                water.setCompanyBankCode(dto.getDuifjgdm());//付款银行行号
                water.setAmount(dto.getDffashee());//交易金额
                water.setReceiveAcount(dto.getKehuzhao());//收款账户
                water.setReceiveBank("南京银行");//收款银行
                water.setPostScript(dto.getBeizhuxx());//附言
                water.setSource(BondWaterSourceEnum.BANK_WATER.getType());
                water.setAbnormalStatus(WaterAbnormalStatusEnum.NORMAL.getType());
                // 根据流水号查询数据是否重复
                LambdaQueryWrapper<BondWater> waterQuery = new LambdaQueryWrapper<>();
                waterQuery.eq(BondWater::getWaterNumber, waterNumber);
                waterQuery.eq(BondWater::getDate, DateUtil.changeStrDate(date, "yyyyMMdd","yyyy-MM-dd"));
                waterQuery.last("limit 1");
                Long hasWater = baseMapper.selectCount(waterQuery);
                if (hasWater < 1 && !water.getCompanyName().equals(NjBankConstans.COMPANY_NAME_SELF)) {
                    list.add(water);
                }
            }
        }
        // 入库
        boolean result = this.saveBatch(list);
        if (!result) {
            return 0;
        }
        //插入保证金余额表
        bondRefundBalanceService.batchSaveOrUpdateChange(list);
        //自动关联
        list.forEach(b->{
            bondRelationService.autoRelate(b);
        });

        // 保存ncc客商信息
        List<String> customerNameList = list.stream().map(m -> {
            return m.getCompanyName();
        }).distinct().collect(Collectors.toList());
        customerToNccService.addCustomerBatch(customerNameList);

        return list.size();
    }

    /**
     * 根据银行信息分组查询流水信息
     * @param splitIds
     * @return
     */
    @Override
    public List<BondWater> getWaterGroupByAccount(String[] splitIds) {
        return this.baseMapper.getWaterGroupByAccount(splitIds);
    }

    /**
     * 民生银行流水入库
     * @param jsonArray
     * @return
     */
    @Override
    public Integer insertCmbcWaterInfo(JSONArray jsonArray) {
        List<BondWater> list = new ArrayList<>();
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject jsonObject = jsonArray.getJSONObject(i);
            // 借贷标志 1-借 2-贷
            String type = jsonObject.getString("type");
            // 只入库收款流水
            if (type.equals("2")) {
                BondWater water = new BondWater();
                water.setWaterNumber(jsonObject.getString("svrId"));
                water.setDate(jsonObject.getString("actDate"));
                String time = jsonObject.getString("timestamp");
                water.setTime(time.substring(0,2) + ":" + time.substring(2,4) + ":" + time.substring(4,6));
                water.setCompanyName(jsonObject.getString("opAcntName").trim());
                water.setCompanyAccount(jsonObject.getString("opAcntNo"));
                water.setCompanyBankDeposit(jsonObject.getString("opBankName"));
//                water.setCompanyBankCode();
                water.setAmount(jsonObject.getString("amount"));
                water.setReceiveAcount(jsonObject.getString("acntNo"));
                water.setReceiveBank("中国民生银行");
                water.setPostScript(jsonObject.getString("remark"));
                water.setSource(BondWaterSourceEnum.BANK_WATER.getType());
                water.setAbnormalStatus(WaterAbnormalStatusEnum.NORMAL.getType());

                // 根据流水号查询去重
                LambdaQueryWrapper<BondWater> waterQuery = new LambdaQueryWrapper<>();
                waterQuery.eq(BondWater::getWaterNumber, water.getWaterNumber());
                waterQuery.last("limit 1");
                Long hasWater = baseMapper.selectCount(waterQuery);
                // 付款户名为“江苏省招标中心有限公司”的收款流水，不记录到系统，只推收款
                if (hasWater < 1 ) {
                    if(water.getCompanyName().equals(NjBankConstans.COMPANY_NAME_SELF)){
                        BondWaterToNccDto item = BeanListUtil.convert(water,BondWaterToNccDto.class);
                        bondSplitService.saveNccReceiveOtherPushLog(item);
                    }else{
                        list.add(water);
                    }
                }
            }
        }
        // 入库
        boolean result = this.saveBatch(list);
        if (!result) {
            return 0;
        }
        //插入保证金余额表
        bondRefundBalanceService.batchSaveOrUpdateChange(list);
        //自动关联
        list.forEach(b->{
            bondRelationService.autoRelate(b);
        });

        // 保存ncc客商信息
        List<String> customerNameList = list.stream().map(m -> {
            return m.getCompanyName();
        }).distinct().collect(Collectors.toList());
        customerToNccService.addCustomerBatch(customerNameList);

        return list.size();
    }

    /**
     * 获取上个月未关联流水
     * @return
     */
    @Override
    public List<BondReceiveDto> findNccReceiveWater() {
        List<BondReceiveDto> list = new ArrayList<>();
        List<BondWaterNccDto> dtoList = this.baseMapper.findNccReceiveWater();
        for (BondWaterNccDto dto : dtoList) {
            //判断流水是否之前关联过
            Integer isRelationCount = this.baseMapper.checkIsRelation(dto.getId());
            if(isRelationCount > 0){
                continue;
            }
            /* start 对于已推送过收款单据的保证金流水，应该不生成隔月收款的单据*/
            // {"billId":"2e899c32956d4654b4990039e3b5aa45","body":[{"water":11111,"amount":"41500.00","customer":"KS22120600006","pkDeptId":"9999","purchaseNumber":"JSTCC9999","recAccount":"93040078801600001550","receiveTime":"2023-04-15 14:37:13"}],"creationTime":"2023-05-12 13:40:01"}
            List<Integer> pushTypeList = new ArrayList<>();
            pushTypeList.add(NccPushTypeEnum.RECEIVE.getType());
            pushTypeList.add(NccPushTypeEnum.ADJUST.getType());

            String waterQuery = "\"waterId\":"+dto.getId();
            //            //查询关联表中是否有删除记录
            LambdaQueryWrapper<NccBondPushLog> logWrapper = new LambdaQueryWrapper<>();
            logWrapper.like(NccBondPushLog::getRequestData,waterQuery)
                    .in(NccBondPushLog::getPushType,pushTypeList);
            long hasWaterCount = pushLogService.count(logWrapper);
            if(hasWaterCount>0){
                continue;
            }
            /* end 对于已推送过收款单据的保证金流水，应该不生成隔月收款的单据*/

            BondReceiveDto receiveDto = new BondReceiveDto();
            // 表头属性
            receiveDto.setBillId(UUID.randomUUID().toString().replaceAll("-", ""));
            receiveDto.setCreationTime(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, dto.getCreatedTime()));
            // 表体
            List<BondReceiveBodyDto> bodys = new ArrayList<>();
            BondReceiveBodyDto body = new BondReceiveBodyDto();
            body.setPurchaseNumber(NccConstants.OTHER_PURCHASE_NUMBER);
            body.setPkDeptId(NccConstants.OTHER_DEPTID);
//            body.setPkPsndoc(NccConstants.OTHER_USER);
            body.setReceiveTime(dto.getReceiveTime());
            body.setRecAccount(dto.getRecAccount());
            body.setAmount(dto.getAmount());
            body.setCustomer(dto.getCustomer());
            body.setCustomerId(dto.getCustomerId());
            body.setWaterId(dto.getId());
            bodys.add(body);
            receiveDto.setBody(bodys);
            list.add(receiveDto);
        }
        return list;
    }



   /* @Override
    public Boolean saveHistoryReceiveWater() {
        *//**
         *         {"billId":"6ace0d1b55d84795b9c9079f5a69596c",
         *         "body":[{"amount":"30000.00","customer":"KS22120600001","pkDeptId":"9999","
         *         purchaseNumber":"JSTCC9999","recAccount":"93040078801600001550",
         *         "receiveTime":"2023-04-01 14:37:02","waterId":2153}],
         *         "creationTime":"2023-05-15 17:51:21"}
         *//*
        List<NccBondPushLog> list = pushLogService.getHistoryReceiveWater();
        List<BondWaterPushRecord> pushRecords = new ArrayList<>();
        if(!list.isEmpty()){
            list.forEach(w->{
                JSONObject allJosn =JSONObject.parseObject(w.getRequestData());
                JSONArray bodys = allJosn.getJSONArray("body");
                for(int i = 0;i<bodys.size();i++){
                    JSONObject body = bodys.getJSONObject(i);
                    BondWaterPushRecord pushRecord = new BondWaterPushRecord();
                    pushRecord.setAmount(body.getBigDecimal("amount"));
                    pushRecord.setWaterId(body.getLong("waterId"));
                    pushRecord.setDeptCode(body.getString("pkDeptId"));
                    pushRecord.setPurchaseNumber(body.getString("purchaseNumber"));
                    pushRecord.setPushBatch(allJosn.getString("billId"));
                    pushRecord.setPushType(NccPushTypeEnum.RECEIVE.getType());
                    pushRecord.setCustomer(body.getString("customer"));
                    pushRecord.setCreatedTime(allJosn.getDate("creationTime"));
                    pushRecords.add(pushRecord);
                }
            });
        }
        return bondWaterPushRecordService.saveBatch(pushRecords);
    }*/

    /**
     * 保证金异常待处理数量（运管处）
     * @return
     */
    @Override
    public Integer abnormalWaterCount() {
        //1.当前页面查询数据为所有保证金到账时间超过44天的流水数据，即流水到账45天还未关联到标包的数据
        //2.包含运管处未处理和财务已退回
        Integer size = this.baseMapper.abnormalWaterCount();
        return size;
    }

    @Override
    public Result<String> updateAbnormalWaterRemark(BondWaterRemarkReq bondWaterRemarkReq) {
        LambdaUpdateWrapper<BondWater> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(BondWater::getId,bondWaterRemarkReq.getWaterId());
        updateWrapper.set(BondWater::getRemark,bondWaterRemarkReq.getRemark());
        this.update(updateWrapper);
        return Result.ok();
    }
}
