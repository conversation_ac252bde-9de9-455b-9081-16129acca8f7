package com.hzw.expertfee.api.config;


import cn.dev33.satoken.exception.NotLoginException;
import cn.dev33.satoken.exception.NotPermissionException;
import cn.dev33.satoken.exception.NotRoleException;
import cn.dev33.satoken.exception.NotSafeException;
import com.hzw.expertfee.api.exception.CustomException;
import com.hzw.sunflower.common.Result;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestControllerAdvice;

/**
 * 全局异常拦截
 * <AUTHOR>
 */
@RestControllerAdvice
public class GlobalException {
    /**
     * 业务异常
     * @param e
     * @return
     */
    @ExceptionHandler(CustomException.class)
    public Result<?> bindExceptionHandler(CustomException e) {
        e.printStackTrace();
        return Result.failed(e.getExceptionEnum().getCode(), e.getExceptionEnum().getMessage());
    }
    /** 全局异常拦截  */
    @ResponseBody
    @ExceptionHandler
    public Result<Object> handlerException(Exception e) {
        e.printStackTrace();
        return Result.failed(e.getMessage());
    }

    @ExceptionHandler(NotLoginException.class)
    public Result<Object> handlerNotLoginException(NotLoginException nle) {
        // 不同异常返回不同状态码
        String message = "";
        Integer code=401;
        if (nle.getType().equals(NotLoginException.NOT_TOKEN)) {
            message = "未提供Token";
        } else if (nle.getType().equals(NotLoginException.INVALID_TOKEN)) {
            message = "未提供有效的Token";
        } else if (nle.getType().equals(NotLoginException.TOKEN_TIMEOUT)) {
            message = "登录信息已过期，请重新登录";
        } else if (nle.getType().equals(NotLoginException.BE_REPLACED)) {
//            code=40101;
            message = "您的账户已在另一台设备上登录，如非本人操作，请立即修改密码";
        } else if (nle.getType().equals(NotLoginException.KICK_OUT)) {
//            code=40102;
            message = "已被系统强制下线";
        } else {
            message = "当前会话未登录";
        }
        // 返回给前端
        return Result.failed(code, message);
    }

    @ExceptionHandler
    public Result<Object> handlerNotRoleException(NotRoleException e) {
        return Result.failed(403, "无此角色：" + e.getRole());
    }

    @ExceptionHandler
    public Result<Object> handlerNotPermissionException(NotPermissionException e) {
        return Result.failed(403, "无此权限：" + e.getCode());
    }
//
//    @ExceptionHandler
//    public Result<Object> handlerDisableLoginException(DisableLoginException e) {
//        return Result.failed(401, "账户被封禁：" + e.getDisableTime() + "秒后解封");
//    }

    @ExceptionHandler
    public Result<Object> handlerNotSafeException(NotSafeException e) {
        return Result.failed(401, "二级认证异常：" + e.getMessage());
    }

}
