package com.hzw.sunflower.controller.request;

import com.hzw.sunflower.controller.response.BondRefundDetailsVo;
import com.hzw.sunflower.entity.SpecialTicket;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 保证金退还表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-08
 */
@Data
public class BondWaterRemarkReq implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("流水id")
    private Long waterId;

    @ApiModelProperty(value = "备注")
    private String remark;

}
