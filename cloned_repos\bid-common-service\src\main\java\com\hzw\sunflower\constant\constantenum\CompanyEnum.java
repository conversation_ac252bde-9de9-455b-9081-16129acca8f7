package com.hzw.sunflower.constant.constantenum;

/**
 * <AUTHOR>
 * @date ：Created in 2021/4/15 10:36
 * @description：单位常量
 * @modified By：`
 * @version: 1.0
 */
public enum CompanyEnum {

    BIDDER(1, "委托人"),
    AGENCY(2, "代理机构"),
    SUPPLIER(3, "供应商");

    private Integer type;
    private String desc;

    CompanyEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

}
