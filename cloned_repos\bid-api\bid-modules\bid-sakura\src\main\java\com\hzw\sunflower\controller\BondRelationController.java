package com.hzw.sunflower.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hzw.sunflower.common.BaseController;
import com.hzw.sunflower.common.Paging;
import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.common.annotation.Log;
import com.hzw.sunflower.common.annotation.RepeatSubmit;
import com.hzw.sunflower.constant.MessageConstants;
import com.hzw.sunflower.constant.constantenum.BusinessType;
import com.hzw.sunflower.controller.request.*;
import com.hzw.sunflower.controller.response.BondRelationDetailVo;
import com.hzw.sunflower.controller.response.BondRelationSectionVo;
import com.hzw.sunflower.controller.response.BondRelationVo;
import com.hzw.sunflower.entity.BondWater;
import com.hzw.sunflower.entity.condition.BondRelationCondition;
import com.hzw.sunflower.service.BondRelationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * <p>
 * 保证金关联 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-27
 */
@Api(tags = "保证金关联")
@RestController
@RequestMapping("/bondRelation")
public class BondRelationController extends BaseController {

    @Autowired
    BondRelationService bondRelationService;

    @ApiOperation(value = "分页查询关注标包关联情况")
    @ApiImplicitParam(name = "condition", value = "标包关联列表页查询条件", required = true, dataType = "BondRelationCondition", paramType = "body")
    @PostMapping(value = "/getSectionRelationPage")
    public Result<Paging<BondRelationSectionVo>> getSectionRelationPage(@RequestBody BondRelationCondition condition) {
        IPage<BondRelationSectionVo> page = bondRelationService.getSectionRelationList(condition, getJwtUser());
        return Result.ok(Paging.buildPaging(page));
    }

    @ApiOperation(value = "供应商关联情况和确认关联情况查询")
    @ApiImplicitParam(name = "condition", value = "标包关联列表页查询条件", required = true, dataType = "BondRelationCondition", paramType = "body")
    @PostMapping(value = "/getSectionRelationDetail")
    public Result<BondRelationVo> getSectionRelationDetail(@RequestBody BondRelationCondition condition) {
        if (condition.getSectionId() == null) {
            return Result.failed(MessageConstants.PARAMS_NOT_NULL);
        }
        return Result.ok(bondRelationService.getSectionRelationDetail(condition));
    }


    @ApiOperation(value = "单包取消关联、确认关联弹框取消关联")
    @ApiImplicitParam(name = "condition", value = "取消关联条件", required = true, dataType = "BondRelationCondition", paramType = "body")
    @PostMapping(value = "/cancelRelation")
    @RepeatSubmit
    public Result<Boolean> cancelRelation(@RequestBody BondRelationCondition condition) {
        if (condition.getSectionId() == null) {
            return Result.failed(MessageConstants.PARAMS_NOT_NULL);
        }
        return Result.okOrFailed(bondRelationService.cancelRelation(condition));
    }

    @ApiOperation(value = "确认关联")
    @ApiImplicitParam(name = "condition", value = "确认关联条件", required = true, dataType = "BondRelationCondition", paramType = "body")
    @PostMapping(value = "/confirmRelation")
    @RepeatSubmit
    public Result<Boolean> confirmRelation(@RequestBody BondRelationCondition condition) {
        if (condition.getWaterId() == null) {
            return Result.failed(MessageConstants.PARAMS_NOT_NULL);
        }
        return Result.okOrFailed(bondRelationService.confirmRelation(condition));
    }

    @ApiOperation(value = "项目经理校验是否可以进入关联页面")
    @ApiImplicitParam(name = "request", value = "关联页面详情查询条件", required = true, dataType = "BondRelationReq", paramType = "body")
    @PostMapping(value = "/validRelationSectionInRefund")
    public Result<Boolean> validRelationSectionInRefund(@RequestBody BondRelationReq request) {
        if (request.getSectionId() == null) {
            return Result.failed(MessageConstants.PARAMS_NOT_NULL);
        }
        Boolean flag = bondRelationService.validRelationSectionInRefund(request);
        if (flag) {
            return Result.ok();
        } else {
            return Result.failed("此包关联标段已处于退还流程，无法再次进行关联操作");
        }
    }

    @ApiOperation(value = "供应商/项目经理关联详情")
    @ApiImplicitParam(name = "request", value = "关联页面详情查询条件", required = true, dataType = "BondRelationReq", paramType = "body")
    @PostMapping(value = "/getRelationDetail")
    public Result<BondRelationDetailVo> getRelationDetail(@RequestBody BondRelationReq request) {
        if (request.getSectionId() == null) {
            return Result.failed(MessageConstants.PARAMS_NOT_NULL);
        }
        return Result.ok(bondRelationService.getRelationDetail(request));
    }

    @ApiOperation(value = "运管处关联详情")
    @ApiImplicitParam(name = "request", value = "关联页面详情查询条件", required = true, dataType = "BondRelationAgentReq", paramType = "body")
    @PostMapping(value = "/getRelationDetailByAgent")
    public Result<BondRelationDetailVo> getRelationDetailByAgent(@RequestBody BondRelationAgentReq request) {
        if (request.getWaterId() == null) {
            return Result.failed(MessageConstants.PARAMS_NOT_NULL);
        }
        return Result.ok(bondRelationService.getRelationDetailByAgent(request));
    }

    @ApiOperation(value = "运管处关联详情-模糊搜索关注项目")
    @ApiImplicitParam(name = "request", value = "关联页面详情标段搜索条件", required = true, dataType = "BondRelationAgentReq", paramType = "body")
    @PostMapping(value = "/getSectionListByPurchaseNum")
    public Result<List<BondRelationSectionVo>> getSectionListByPurchaseNum(@RequestBody BondRelationAgentReq request) {
        if (request.getCompanyId() == null || request.getPurchaseNumber() == null || request.getPurchaseNumber().equals("")) {
            return Result.failed(MessageConstants.PARAMS_NOT_NULL);
        }
        return Result.ok(bondRelationService.getSectionListByPurchaseNum(request));
    }

    @ApiOperation(value = "运管处关联详情-搜索标段")
    @ApiImplicitParam(name = "request", value = "关联页面详情标段搜索条件", required = true, dataType = "BondRelationAgentReq", paramType = "body")
    @PostMapping(value = "/getSectionListByProjectId")
    public Result<List<BondRelationSectionVo>> getSectionListByProjectId(@RequestBody BondRelationAgentReq request) {
        if (request.getCompanyId() == null || request.getProjectId() == null) {
            return Result.failed(MessageConstants.PARAMS_NOT_NULL);
        }
        return Result.ok(bondRelationService.getSectionListByProjectId(request));
    }

    @ApiOperation(value = "校验开户信息")
    @ApiImplicitParam(name = "request", value = "流水ID", required = true, dataType = "waterIds", paramType = "body")
    @PostMapping(value = "/checkOpenAccount")
    public Result<List<BondWater>> checkOpenAccount(@RequestBody List<Long> waterIds) {
        return bondRelationService.checkOpenAccount(waterIds);
    }

    @RepeatSubmit
    @Log(title = "新增关联", description = "新增关联", businessType = BusinessType.INSERT)
    @ApiOperation(value = "关联页面-确认关联")
    @ApiImplicitParam(name = "req", value = "新增关联条件", required = true, dataType = "AddRelationReq", paramType = "body")
    @PostMapping(value = "/addRelation")
    public Result<Boolean> addRelation(@RequestBody AddRelationReq req) {
        if (req.getWaterIdList() == null || req.getWaterIdList().size() == 0 || req.getSectionIdList() == null || req.getSectionIdList().size() == 0) {
            return Result.failed(MessageConstants.PARAMS_NOT_NULL);
        }
        return Result.okOrFailed(bondRelationService.addRelation(req));
    }

    @RepeatSubmit
    @Log(title = "新增异常关联", description = "新增异常关联", businessType = BusinessType.INSERT)
    @ApiOperation(value = "项目经理关联页面-异常关联")
    @ApiImplicitParam(name = "req", value = "异常关联条件", required = true, dataType = "ExceptionRelationReq", paramType = "body")
    @PostMapping(value = "/addExceptionRelation")
    public Result<Boolean> addExceptionRelation(@RequestBody ExceptionRelationReq req) {
        if (req.getWaterIdList() == null || req.getWaterIdList().size() == 0 || req.getSectionIdList() == null || req.getSectionIdList().size() == 0) {
            return Result.failed(MessageConstants.PARAMS_NOT_NULL);
        }
        return Result.okOrFailed(bondRelationService.addExceptionRelation(req));
    }

    @ApiOperation(value = "异常关联处理详情")
    @ApiImplicitParam(name = "applyId", value = "申请id", required = true)
    @PostMapping(value = "/getExceptionRelationDetail/{applyId}")
    public Result<BondRelationVo> getExceptionRelationDetail(@PathVariable Long applyId) {
        if (applyId == null) {
            return Result.failed(MessageConstants.PARAMS_NOT_NULL);
        }
        return Result.ok(bondRelationService.getExceptionRelationDetail(applyId));
    }

    @ApiOperation(value = "异常关联退回")
    @ApiImplicitParam(name = "req", value = "异常关联条件", required = true, dataType = "ExceptionRelationApplyReq", paramType = "body")
    @PostMapping("/applyRelationReturn")
    @RepeatSubmit
    public Result<Boolean> applyRelationReturn(@RequestBody ExceptionRelationApplyReq req) {
        return Result.okOrFailed(bondRelationService.applyRelationReturn(req));
    }

    @ApiOperation(value = "异常关联同意")
    @ApiImplicitParam(name = "req", value = "异常关联条件", required = true, dataType = "ExceptionRelationApplyReq", paramType = "body")
    @PostMapping("/applyRelationAgree")
    @RepeatSubmit
    public Result<Boolean> applyRelationAgree(@RequestBody ExceptionRelationApplyReq req) {
        return Result.okOrFailed(bondRelationService.applyRelationAgree(req));
    }


}
