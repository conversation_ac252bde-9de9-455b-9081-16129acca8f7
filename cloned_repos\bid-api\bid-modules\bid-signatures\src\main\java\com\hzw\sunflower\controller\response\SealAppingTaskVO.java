package com.hzw.sunflower.controller.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@ApiModel(description = "代办列表 ")
@Data
public class SealAppingTaskVO implements Serializable {

    @ApiModelProperty(value = "业务id")
    private Long busId;

    @ApiModelProperty(value = "任务id")
    private String taskId;

    @ApiModelProperty(value = "审批内容")
    private String formName;

    /**
     * 表单的标识
     */
    private String formKey;

    @ApiModelProperty(value = "项目IDs")
    private String projectIds;

    @ApiModelProperty(value = "项目编名称")
    private String projectName;

    @ApiModelProperty(value = "项目编号")
    private String projectNum;

    @ApiModelProperty(value = "类别")
    private String processDefinitionKey;

    @ApiModelProperty(value = "关联标段")
    private String projectBidSections;

    @ApiModelProperty(value = "提交时间")
    private Date submitTime;

    @ApiModelProperty(value = "流程实例的id", required = true)
    private String processInstanceId;

    @ApiModelProperty(value = "业务管理key ,表名:id 例如 notice:123456", required = true)
    private String businessKey;

    @ApiModelProperty(value = "阶段", required = true)
    private Integer bidRound;

    @ApiModelProperty(value = "标段Id", required = true)
    private String sectionId;

    @ApiModelProperty(value = "数据类别")
    private String formData;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "申请人")
    private String applyUserName;

    @ApiModelProperty(value = "申请时间")
    private Date applyTime;

    @ApiModelProperty("用印申请类型（1.呈报事项 2.合同事项 3.拟文 4.用印审批")
    private Integer type;

    @ApiModelProperty("1.澄清/修改 2.异议回复 3.通知 4.函件 5.请示 6.报告 7.投标文件 8.评标报告 9.采购文件 10.其他 11.中标通知书")
    private Integer applyItem;

    @ApiModelProperty("申请事项描述")
    private String itemDescribe;

    @ApiModelProperty("用印方式（1.电子章 2.实体章）")
    private String sealType;

    @ApiModelProperty("所在处室")
    private String deptName;

    @ApiModelProperty("清样校对选择部门")
    private Long proofCheckDept;
}
