package com.hzw.sunflower.constant.constantenum;

/**
 * <AUTHOR>
 * @date ：Created in 2021/4/15 10:36
 * @description：标的物分类
 * @modified By：`
 * @version: 1.0
 */
public enum DictionaryTypeEnum {

    SUBJECT_MATTER("subject_matter", "标的物分类");

    private String type;
    private String desc;

    DictionaryTypeEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

}
