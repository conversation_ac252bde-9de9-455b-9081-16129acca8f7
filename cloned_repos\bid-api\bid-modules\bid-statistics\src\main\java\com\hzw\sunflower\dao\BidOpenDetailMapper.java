package com.hzw.sunflower.dao;

import com.hzw.sunflower.controller.request.BidOpenDetailReq;
import com.hzw.sunflower.controller.response.ScheduleAmountVO;
import com.hzw.sunflower.dto.ConferenceAddress;
import com.hzw.sunflower.entity.*;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface BidOpenDetailMapper {
    List<BidOpenDetail> selectBidOpenDetailList(@Param("req") BidOpenDetailReq req,@Param("keyWords")String keyWords, @Param("startDate")String startDate, @Param("endDate") String endDate, @Param("datascopesql") String datascopesql, @Param("startIndex")int startIndex, @Param("pageSize")int pageSize);

//    int selectCountOfBidOpenDetailList(@Param("req") BidOpenDetailReq req,@Param("keyWords")String keyWords,@Param("startDate")String startDate,@Param("endDate") String endDate,@Param("datascopesql") String datascopesql);

//    List<BidOpenDetail> queryBidOpenDetailList(@Param("req") BidOpenDetailReq req,@Param("keyWords")String keyWords,@Param("startDate")String startDate,@Param("endDate") String endDate,@Param("datascopesql") String datascopesql);

    List<String> selectConferenceAddress(@Param("projectNumber")String projectNumber, @Param("bidPackage")String bidPackage);


    List<String> selectConferenceAddressNew(@Param("projectId")Long projectId, @Param("bidPackage")String bidPackage);
    List<ConferenceAddress> selectConferenceAddressList(Long projectId, String bidPackage);

    ScheduleAmountVO  queryInComeAmount(@Param("req")BidOpenDetailReq bidOpenDetailReq, @Param("datascopesql") String datascopesql);

    List<BidOpenDetail> selectBidOpenDetailListAll(@Param("req") BidOpenDetailReq req,@Param("keyWords")String keyWords, @Param("startDate")String startDate, @Param("endDate") String endDate, @Param("datascopesql") String datascopesql);
}
