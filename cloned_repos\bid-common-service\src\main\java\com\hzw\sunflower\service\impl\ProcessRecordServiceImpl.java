package com.hzw.sunflower.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzw.sunflower.client.WorkFlowApiClient;
import com.hzw.sunflower.constant.CommonConstants;
import com.hzw.sunflower.constant.ReturnCodeConstants;
import com.hzw.sunflower.constant.constantenum.AppTaskTypeEnum;
import com.hzw.sunflower.constant.constantenum.ProcessRecordEnum;
import com.hzw.sunflower.controller.request.ProcessRecordReq;
import com.hzw.sunflower.dao.ProcessRecordMapper;
import com.hzw.sunflower.dto.AppOaMsgDto;
import com.hzw.sunflower.dto.ProcessRecordDTO;
import com.hzw.sunflower.entity.CalibrationProcessRecord;
import com.hzw.sunflower.freesia.client.constant.FlowClientConstant;
import com.hzw.sunflower.freesia.client.vo.flowable.task.TaskUserVo;
import com.hzw.sunflower.service.CommonMqService;
import com.hzw.sunflower.service.PendingItemService;
import com.hzw.sunflower.service.ProcessRecordService;
import com.hzw.sunflower.util.BeanListUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2021/7/21 16:53
 * @description：流程serviceimpl
 * @modified By：`
 * @version: 1.0
 */
@Service
public class ProcessRecordServiceImpl extends ServiceImpl<ProcessRecordMapper, CalibrationProcessRecord> implements ProcessRecordService {


    @Autowired
    private WorkFlowApiClient flowApiClient;

    @Autowired
    private PendingItemService pendingItemService;

    @Autowired
    private CommonMqService commonMqService;


    /**
     * 添加流程信息
     *
     * @param dto
     * @return
     */
    @Override
    public Boolean addProcessRecord(ProcessRecordDTO dto,String userOtherId,String retrunStr) {
        String userName="";
        if(StringUtils.isNotBlank(retrunStr)) {
            JSONObject jsonObject = JSON.parseObject(retrunStr);
            String processId = jsonObject.getString("data");
            //查询下一步审核人
            String taskUserStr = flowApiClient.getTaskUserByProcessInstanceId(userOtherId, processId);
            JSONObject jsonUser = JSON.parseObject(taskUserStr);
            if (jsonUser.getString("code").equals(ReturnCodeConstants.PROCESS_SUCCESS_CODE)) {
                List<TaskUserVo> taskUserVoList = JSONObject.parseArray(jsonUser.getString("data"), TaskUserVo.class);
                List<String> recipientList = new ArrayList<>();
                if (null != taskUserVoList && taskUserVoList.size() > 0) {
                    for (TaskUserVo taskUserVo : taskUserVoList) {
                        userName += taskUserVo.getUserName() + ",";
                        recipientList.add(taskUserVo.getUserCode());
                    }
                }

                // 发送app待办
                AppOaMsgDto msgDto = new AppOaMsgDto();
                msgDto.setTaskType(getTypeByCode(dto.getBusinessCode()));
                msgDto.setRecipientList(recipientList);
                commonMqService.sendOaMsg(msgDto);
            }
        }
        if(!userName.equals("")){
            userName =  userName.substring(0, userName.length() - 1);
        }
        CalibrationProcessRecord entity = BeanListUtil.convert(dto, CalibrationProcessRecord.class);
        entity.setNextUserName(userName);

        //退回时添加待处理事项表
        if (entity.getOperation().contains("退回")) {
            pendingItemService.addPendingItem(dto);
        }
        //退回后重新提交删除退回待处理
        if ((entity.getOperation().contains("提交") || entity.getOperation().contains("申请")
                || entity.getOperation().contains("换票") || entity.getOperation().contains("回复")
                || entity.getOperation().contains("修改") || entity.getOperation().contains("撤回")
                || entity.getOperation().contains("发布")) && !entity.getOperation().equals("退回撤回")){
            pendingItemService.deletePendingItem(dto);
        }
        return this.save(entity);
    }


    /**
     * 添加流程信息
     *
     * @param dto
     * @return
     */
    @Override
    public Boolean addProcessRecordNextUserName(ProcessRecordDTO dto,String userOtherId,String retrunStr,String nextUserName) {
        String userName="";
        if(StringUtils.isNotBlank(retrunStr)) {
            JSONObject jsonObject = JSON.parseObject(retrunStr);
            String processId = jsonObject.getString("data");
            //查询下一步审核人
            String taskUserStr = flowApiClient.getTaskUserByProcessInstanceId(userOtherId, processId);
            JSONObject jsonUser = JSON.parseObject(taskUserStr);
            if (jsonUser.getString("code").equals(ReturnCodeConstants.PROCESS_SUCCESS_CODE)) {
                List<TaskUserVo> taskUserVoList = JSONObject.parseArray(jsonUser.getString("data"), TaskUserVo.class);
                List<String> recipientList = new ArrayList<>();
                if (null != taskUserVoList && taskUserVoList.size() > 0) {
                    for (TaskUserVo taskUserVo : taskUserVoList) {
                        userName += taskUserVo.getUserName() + ",";
                        recipientList.add(taskUserVo.getUserCode());
                    }
                }

                // 发送app待办
                AppOaMsgDto msgDto = new AppOaMsgDto();
                msgDto.setTaskType(getTypeByCode(dto.getBusinessCode()));
                msgDto.setRecipientList(recipientList);
                commonMqService.sendOaMsg(msgDto);
            }
        }
        if(!userName.equals("")){
            userName =  userName.substring(0, userName.length() - 1);
        }else {
            if(StringUtils.isNotEmpty(nextUserName)) {
                userName = nextUserName;
            }
        }
        CalibrationProcessRecord entity = BeanListUtil.convert(dto, CalibrationProcessRecord.class);
        entity.setNextUserName(userName);

        //退回时添加待处理事项表
        if (entity.getOperation().contains("退回")) {
            pendingItemService.addPendingItem(dto);
        }
        //退回后重新提交删除退回待处理
        if ((entity.getOperation().contains("提交") || entity.getOperation().contains("申请")
                || entity.getOperation().contains("换票") || entity.getOperation().contains("回复")
                || entity.getOperation().contains("修改") || entity.getOperation().contains("撤回")
                || entity.getOperation().contains("发布")) && !entity.getOperation().equals("退回撤回")){
            pendingItemService.deletePendingItem(dto);
        }
        return this.save(entity);
    }
    /**
     * 添加流程信息 无工作流
     *
     * @param dto
     * @return
     */
    @Override
    public Boolean addProcessRecordNotTask(ProcessRecordDTO dto,String userOtherIdr) {
        CalibrationProcessRecord entity = BeanListUtil.convert(dto, CalibrationProcessRecord.class);
        //退回时添加待处理事项表
        if (entity.getOperation().contains("退回")) {
            pendingItemService.addPendingItem(dto);
        }
        //退回后重新提交删除退回待处理
        if ((entity.getOperation().contains("提交") || entity.getOperation().contains("申请")
                || entity.getOperation().contains("换票") || entity.getOperation().contains("回复")
                || entity.getOperation().contains("修改") || entity.getOperation().contains("撤回")
                || entity.getOperation().contains("发布")) && !entity.getOperation().equals("退回撤回")){
            pendingItemService.deletePendingItem(dto);
        }
        return this.save(entity);
    }


    @Override
    public Boolean addProcessRemarkRecord(ProcessRecordDTO dto) {
        //调用红冲确认单申请接口
        CalibrationProcessRecord entity = BeanListUtil.convert(dto, CalibrationProcessRecord.class);
        entity.setMessage(dto.getMessage());
        //退回时添加待处理事项表
        if (entity.getOperation().contains("退回")) {
            pendingItemService.addPendingItem(dto);
        }
        //退回后重新提交删除退回待处理
        if ((entity.getOperation().contains("提交") || entity.getOperation().contains("申请")
                || entity.getOperation().contains("换票") || entity.getOperation().contains("回复")
                || entity.getOperation().contains("修改") || entity.getOperation().contains("撤回")
                || entity.getOperation().contains("发布")) && !entity.getOperation().equals("退回撤回")){
            pendingItemService.deletePendingItem(dto);
        }
        return this.save(entity);
    }

    /**
     * 获取待办类型
     * @param businessCode
     * @return
     */
    private String getTypeByCode(String businessCode) {
        // 更正公告
        if (businessCode.equals(ProcessRecordEnum.SUPPLEMENT_BULLETIN.getType())) {
            return AppTaskTypeEnum.SUPPLEMENT_NOTICE.getCode();
        }
        // 澄清修改
        if (businessCode.equals(ProcessRecordEnum.CLARIFY.getType())) {
            return AppTaskTypeEnum.CLARIFY_REPLY.getCode();
        }
        // 异议回复
        if (businessCode.equals(ProcessRecordEnum.OBJECTION_NOTICE.getType())) {
            return AppTaskTypeEnum.OBJECTION.getCode();
        }
        // 确认资格预审、确认中标候选人/中标人
        if (businessCode.equals(ProcessRecordEnum.BID_WIN_PEOPLE.getType())) {
            return AppTaskTypeEnum.SHOW_BIDWINPEOPLE.getCode();
        }
        // 中标候选人公示、中标结果公示
        if (businessCode.equals(ProcessRecordEnum.BID_WIN_BULLETIN.getType())) {
            return AppTaskTypeEnum.SHOW_BID_WIN_BULLETIN.getCode();
        }
        // 申请资格预审通知书、重新申请通知书、重新申请资格预审通知书、中标通知书
        if (businessCode.equals(ProcessRecordEnum.BID_WIN_NOTICE.getType())) {
            return AppTaskTypeEnum.SHOW_BID_WIN_NOTICE.getCode();
        }
        // 暂停/终止
        if (businessCode.equals(ProcessRecordEnum.EXCEPT_NOTICE.getType())) {
            return AppTaskTypeEnum.EXCEPTION_MANAGE.getCode();
        }
        // 无需转换的类型：支付凭证、登记供应商、代理服务费、数据修改、劳务费所有类型、专家抽取、专家扣分
        return businessCode;
    }

    /**
     * 查询流程记录
     *
     * @param req
     * @return
     */
    @Override
    public List<ProcessRecordDTO> queryProcessRecord(ProcessRecordReq req) {
        LambdaQueryWrapper<CalibrationProcessRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CalibrationProcessRecord::getBusinessCode, req.getBusinessCode());
        queryWrapper.eq(CalibrationProcessRecord::getBusinessId, req.getBusinessId());
        if(req.getIsShowExamine() == 0){
            queryWrapper.eq(CalibrationProcessRecord::getIsShowExamine, CommonConstants.YES);
        }
        queryWrapper.orderByDesc(CalibrationProcessRecord::getId);
        List<CalibrationProcessRecord> list = this.list(queryWrapper);
        return BeanListUtil.convertList(list, ProcessRecordDTO.class);
    }

    /**
     * 查询流程记录s
     * @param req
     * @return
     */
    @Override
    public List<ProcessRecordDTO> queryProcessRecords(ProcessRecordReq req) {
        List<CalibrationProcessRecord> list = this.baseMapper.selectProcessRecordList(req);
        return BeanListUtil.convertList(list, ProcessRecordDTO.class);
    }

    /**
     * 劳务费查询流程记录
     *
     * @param req
     * @return
     */
    @Override
    public List<ProcessRecordDTO> queryProcessRecordExpertFee(ProcessRecordReq req) {
        List<CalibrationProcessRecord> list = this.baseMapper.queryProcessRecordExpertFee(req);
        return BeanListUtil.convertList(list, ProcessRecordDTO.class);
    }

    @Override
    public Date queryEndTime(ProcessRecordReq req) {
        Date d = null;
        CalibrationProcessRecord processRecord = this.baseMapper.queryEndTime(req);
        if(null != processRecord){
            d = processRecord.getCreatedTime();
        }
        return d;
    }

    @Override
    public Boolean deleteShareRecord(String businessCode, String nccBusinessCode, Long projectId) {
        Boolean flag = true;
        if(!"".equals(businessCode)){
            LambdaQueryWrapper<CalibrationProcessRecord> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(CalibrationProcessRecord::getBusinessCode, businessCode)
                    .eq(CalibrationProcessRecord::getBusinessId, projectId);
            flag = remove(queryWrapper);
        }
        if(!"".equals(nccBusinessCode)){
            LambdaQueryWrapper<CalibrationProcessRecord> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(CalibrationProcessRecord::getBusinessCode, nccBusinessCode)
                    .eq(CalibrationProcessRecord::getBusinessId, projectId);
            flag = remove(queryWrapper);
        }
        return flag;
    }

    /**
     * 更新保证金退还流程信息
     * @param refundId 退还id
     * @param refundProcessCode 退还流程code：processInstanceId
     * @param refundUserCode 退还流程引擎审批人code：userCode
     * @return
     */
    @Override
    public Boolean updateBondRefundProcessRecord(Long refundId, String refundProcessCode, String refundUserCode) {
        boolean result = false;
        LambdaQueryWrapper<CalibrationProcessRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CalibrationProcessRecord::getBusinessCode, FlowClientConstant.BOND_APPLY_REFUND);
        queryWrapper.eq(CalibrationProcessRecord::getBusinessId, refundId);
        queryWrapper.orderByDesc(CalibrationProcessRecord::getCreatedTime);
        queryWrapper.last("limit 1");
        CalibrationProcessRecord calibrationProcessRecord = this.getOne(queryWrapper);
        if (calibrationProcessRecord != null) {
            String userName = "";
            //查询下一步审核人
            String taskUserStr = flowApiClient.getTaskUserByProcessInstanceIdCrocus(refundUserCode, refundProcessCode);
            JSONObject jsonUser = JSON.parseObject(taskUserStr);
            if (jsonUser.getString("code").equals(ReturnCodeConstants.PROCESS_SUCCESS_CODE)) {
                List<TaskUserVo> taskUserVoList = JSONObject.parseArray(jsonUser.getString("data"), TaskUserVo.class);
                List<String> recipientList = new ArrayList<>();
                if (null != taskUserVoList && taskUserVoList.size() > 0) {
                    for (TaskUserVo taskUserVo : taskUserVoList) {
                        userName += taskUserVo.getUserName() + ",";
                        recipientList.add(taskUserVo.getUserCode());
                    }
                }

                if(!"".equals(userName)){
                    userName =  userName.substring(0, userName.length() - 1);
                }

                calibrationProcessRecord.setNextUserName(userName);
                result = this.updateById(calibrationProcessRecord);

                // 发送app待办
                AppOaMsgDto msgDto = new AppOaMsgDto();
                msgDto.setTaskType(getTypeByCode(FlowClientConstant.BOND_APPLY_REFUND));
                msgDto.setRecipientList(recipientList);
                commonMqService.sendOaMsg(msgDto);
            }
        }
        return result;
    }

    @Override
    public boolean hasNext(String retrunStr, String userOtherId) {
        boolean flag = false;
        if(StringUtils.isNotBlank(retrunStr)) {
            JSONObject jsonObject = JSON.parseObject(retrunStr);
            String processId = jsonObject.getString("data");
            //查询下一步审核人
            String taskUserStr = flowApiClient.getTaskUserByProcessInstanceId(userOtherId, processId);
            JSONObject jsonUser = JSON.parseObject(taskUserStr);
            if (jsonUser.getString("code").equals(ReturnCodeConstants.PROCESS_SUCCESS_CODE)) {
                List<TaskUserVo> taskUserVoList = JSONObject.parseArray(jsonUser.getString("data"), TaskUserVo.class);
                if (null != taskUserVoList && taskUserVoList.size() > 0) {
                    flag = true;
                }
            }
        }
        return flag;
    }
}
