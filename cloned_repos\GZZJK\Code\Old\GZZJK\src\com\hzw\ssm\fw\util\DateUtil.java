package com.hzw.ssm.fw.util;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.Map;

import org.apache.struts2.util.StrutsTypeConverter;

public class DateUtil extends StrutsTypeConverter {

	private static final String FORMATDATE = "yyyy-MM-dd";
	private static final String FORMATTIME = "yyyy-MM-dd HH:mm:ss";

	@SuppressWarnings("rawtypes")
	@Override
	public Object convertFromString(Map context, String[] values, Class toClass) {
		if (values == null || values.length == 0) {
			return null;
		}
		// 有时分秒的要先转换
		SimpleDateFormat sdf = new SimpleDateFormat(FORMATTIME);
		Date date = null;
		String dateString = values[0];
		if (dateString != null) {
			try {
				date = sdf.parse(dateString);
			} catch (ParseException e) {
				date = null;
			}
			if (date == null) {
				sdf = new SimpleDateFormat(FORMATDATE);
				try {
					date = sdf.parse(dateString);
				} catch (ParseException e) {
					date = null;
				}
			}
		}
		return date;
	}

	@SuppressWarnings("rawtypes")
	@Override
	public String convertToString(Map context, Object o) {
		if (o instanceof Date) {
			SimpleDateFormat sdf = new SimpleDateFormat(FORMATTIME);
			return sdf.format((Date) o);
		}
		return "";
	}

	/**
	 * 返回当前年月日
	 * 
	 * @param linkChar
	 *            日期分隔符号
	 * @return 返回当前年月日
	 */
	public static String getCurrentYearMonthDay(String linkChar) {
		String formatString = "yyyy" + linkChar + "MM" + linkChar + "dd";
		Calendar calendar = Calendar.getInstance();
		SimpleDateFormat df = new SimpleDateFormat(formatString);
		return df.format(calendar.getTime());

	}

	/**
	 * 根据指定格式返回当前年月日时分秒
	 * 
	 * @param format
	 *            指定格式
	 * @return 返回当前年月日
	 */
	public static String getCurrentDateTime(String format) {
		Calendar calendar = Calendar.getInstance();
		SimpleDateFormat df = new SimpleDateFormat(format);
		return df.format(calendar.getTime());
	}

	/**
	 * 根据指定格式返回当前年月日时分秒
	 * 
	 * @param format
	 *            指定格式
	 * @return 返回当前年月日
	 */
	public static String getFormatDateTime(String format, Date date) {
		SimpleDateFormat df = new SimpleDateFormat(format);
		return df.format(date.getTime());
	}
	
	/**
	 * 获取两个日期之间的工作日天数
	 * 
	 * @param startDate
	 * @param endDate
	 * @return 间隔的天数
	 */
	public static int getDutyDays(Date startDate, Date endDate) {
		if (null == startDate)
		{
			startDate = new Date();
		}
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm");
		int result = 0;
		java.text.SimpleDateFormat df = new java.text.SimpleDateFormat("yyyy-MM-dd ");
		while (startDate.compareTo(endDate) <= 0) {
			if (startDate.getDay() != 6 && startDate.getDay() != 0)
			{
				result++;
			}
			startDate.setDate(startDate.getDate() + 1);
		}
		System.err.println(result);
		return result;
	}
	
	/**
	 * 格式化字符串，返回时间类型
	 * 函数功能描述：TODO
	 * @param date 时间字符串
	 * @param format 指定的类型
	 * @return
	 */
	public static Date formatStringByChar(String date,String format){
		Date d=null;
		SimpleDateFormat formatter = new SimpleDateFormat(format);
		try {
			 d=formatter.parse(date);
		} catch (ParseException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return d;
	}
	
    /**
     * 函数功能描述：将传入的date对象按照yyyy-MM-dd 转成String后返回
     * @param Date 传给方法的date对象
     * @return String 返回字符串
     */
    public static String dateToString(Date date)
    {
        String strDateTime = "";
        String formatStr = "yyyy-MM-dd";
        strDateTime = DateUtil.dateToString(date, formatStr);
        return strDateTime;
    }
	public static String dateToString2(Date date)
	{
		String strDateTime = "";
		String formatStr = "yyyy-MM-dd hh:mm:ss";
		strDateTime = DateUtil.dateToString(date, formatStr);
		return strDateTime;
	}
    /**
     * 函数功能描述：将date对象按照某种格式进行转换，返回转换后的String
     * @param Date 传给方法的date对象
     * @param formatStr  需要转换的类型格式
     * @return String  返回字符串
     */
    public static String dateToString(Date date, String formatStr)
    {
        String strDateTime = "";
        SimpleDateFormat sdf = new SimpleDateFormat(formatStr);
        strDateTime = sdf.format(date);
        return strDateTime;

    }
    
    public static String getDatePoor(Date endDate, Date nowDate) {
    	long nd = 1000 * 24 * 60 * 60;
    	long nh = 1000 * 60 * 60;
    	long nm = 1000 * 60;
    	// long ns = 1000;
    	// 获得两个时间的毫秒时间差异
    	long diff = endDate.getTime() - nowDate.getTime();
    	// 计算bai差多du少天
    	long day = diff / nd;
    	// 计算差多少小时
    	long hour = diff % nd / nh;
    	// 计算差多少分钟
    	long min = diff % nd % nh / nm;
    	// 计算差多少秒//输出结果
    	// long sec = diff % nd % nh % nm / ns;
    	return day + "天" + hour + "小时" + min + "分钟";
    }
    
    public static long getMinutes(Date nowDate,Date endDate) {
    	long nm = 1000 * 60;
    	// 获得两个时间的毫秒时间差异
    	long diff = nowDate.getTime() - endDate.getTime();
    	// 计算差多少分钟
    	long min = diff / nm;
    	
    	return min;
    }
    
    public static void main(String[] args) throws ParseException {
		Date endDate = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse("2020-08-20 10:00:00");
		Date nowDate = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse("2020-08-20 11:15:15");
		
		long diff = getMinutes(nowDate, endDate);
		
		System.out.println(diff);
	}
}
