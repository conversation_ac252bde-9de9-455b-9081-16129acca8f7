package com.hzw.sunflower.service;

import com.hzw.sunflower.entity.ExpertInfo;
import com.hzw.sunflower.entity.ExpertInfoFile;

/**
 * @ClassName:CommonExpertInfoService
 * @Auther: lijinxin
 * @Description: 专家信息公用接口
 * @Date: 2022/12/7 14:06
 * @Version: v1.0
 */
public interface CommonExpertInfoService{

    /**
     * 根据身份证查询一条数据
     * @param IdentityCard
     * @return
     */
     ExpertInfo getOne(String IdentityCard);


    /**
     * 根据id 修改数据
     * @param info
     * @return
     */
    boolean updateById(ExpertInfo info);


    /**
     * 新增
     * @param info
     * @return
     */
    boolean save(ExpertInfo info);


    /**
     * 新增文件
     * @param file
     * @return
     */
    boolean fileSave(ExpertInfoFile file);

    /**
     * 删除文件
     * @param infoId
     * @return
     */
    Integer deleteFile(Long infoId);

    /**
     * 更新专家id
     * @param path
     * @return
     */
    Long updateFileKeyByPath(String path) throws Exception;

    Long  updateFileKeyByOssId(String ossId) throws Exception;
}
