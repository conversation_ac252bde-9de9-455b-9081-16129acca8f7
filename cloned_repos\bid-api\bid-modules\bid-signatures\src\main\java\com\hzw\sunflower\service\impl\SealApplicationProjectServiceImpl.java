package com.hzw.sunflower.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzw.sunflower.dao.SealApplicationProjectMapper;
import com.hzw.sunflower.dto.SealApplicationProjectDTO;
import com.hzw.sunflower.entity.SealApplicationProject;
import com.hzw.sunflower.entity.condition.SealApplicationProjectCondition;
import com.hzw.sunflower.exception.ExceptionEnum;
import com.hzw.sunflower.exception.SunFlowerException;
import com.hzw.sunflower.service.SealApplicationProjectService;
import org.springframework.stereotype.Service;

import java.util.List;


/**
* SealApplicationProjectService接口实现
*
* <AUTHOR>
* @version 1.0.0 2021-04-07
*/
@Service
public class SealApplicationProjectServiceImpl extends ServiceImpl<SealApplicationProjectMapper, SealApplicationProject> implements SealApplicationProjectService {

    /**
    * 根据条件分页查询列表
    *
    * @param condition 查询条件
    * @return 分页数据
    */
    @Override
    public IPage<SealApplicationProject> findInfoByCondition(SealApplicationProjectCondition condition) {
        IPage<SealApplicationProject> page = condition.buildPage();
        QueryWrapper<SealApplicationProject> queryWrapper = condition.buildQueryWrapper(SealApplicationProject.class);
        return this.page(page, queryWrapper);
    }

    /**
    * 根据主键ID查询
    *
    * @param id 主键ID
    * @return
    */
    @Override
    public SealApplicationProject getInfoById(Long id) {
        return this.getById(id);
    }

    /**
    * 新增
    *
    * @param sealApplicationProject
    * @return 是否成功
    */
    @Override
    public Boolean addInfo(SealApplicationProjectDTO sealApplicationProject) {
        return this.save(sealApplicationProject);
    }

    /**
    * 修改
    *
    * @param sealApplicationProject
    * @return 是否成功
    */
    @Override
    public Boolean updateInfo(SealApplicationProjectDTO sealApplicationProject) {
        return this.updateById(sealApplicationProject);
    }

    /**
    * 根据主键ID删除
    *
    * @param id 主键ID
    * @return 是否成功
    */
    @Override
    public Boolean deleteById(Long id) {
        return this.removeById(id);
    }

    /**
    * 根据主键ID列表批量删除
    *
    * @param idList 主键ID列表
    * @return 是否成功
    */
    @Override
    public Boolean deleteByIds(List<Long> idList) {
        return this.removeByIds(idList);
    }

}