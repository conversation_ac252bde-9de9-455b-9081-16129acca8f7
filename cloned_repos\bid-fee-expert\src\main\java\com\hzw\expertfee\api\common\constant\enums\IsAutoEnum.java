package com.hzw.expertfee.api.common.constant.enums;

/**
 * 是否自动
 */
public enum IsAutoEnum {

    // 是否自动：1.自动，2.手动
    AUTO(1, "自动"),
    HAND_MOVEMENT(2, "手动")
    ;

    private Integer type;
    private String desc;

    IsAutoEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

}
