package com.hzw.sunflower.constant.constantenum;

/**
 * 业务分类
 *
 * <AUTHOR>
 */
public enum EngineeringClassEnum {

    ENGINEERING(1, "工程"),
    GOODS(3, "货物"),
    SERVICE(2, "服务");

    private Integer type;
    private String desc;

    EngineeringClassEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
