package com.hzw.ssm.sys.system.action;

import java.util.List;

import org.apache.struts2.convention.annotation.Action;
import org.apache.struts2.convention.annotation.Namespace;
import org.apache.struts2.convention.annotation.ParentPackage;
import org.apache.struts2.convention.annotation.Result;
import org.apache.struts2.convention.annotation.Results;
import org.springframework.beans.factory.annotation.Autowired;

import com.hzw.ssm.fw.base.BaseAction;
import com.hzw.ssm.sys.system.entity.SystemLogEntity;
import com.hzw.ssm.sys.system.service.SystemLogService;

/**
 * 系统日志操作
 * 
 * @page user
 * <AUTHOR> 
 */
@Namespace("/systemLog")
@ParentPackage(value = "default")
@Results( { @Result(name = "toLogList", location = "/jsp/sys/logList.jsp") })
public class SystemLogAction extends BaseAction {

	/**
	 * 
	 */
	private static final long serialVersionUID = -8823858495799309882L;

	/** 菜单对象列表 */
	private List<SystemLogEntity> logList;

	/** 菜单对象列表的查询条件 */
	private SystemLogEntity logEntity;

	@Autowired
	private SystemLogService logService;

	/**
	 * 加载树形菜单信息
	 * 
	 * @return
	 */
	@Action("queryLogList")
	public String queryLogList() {
		if (logEntity == null) {
			logEntity = new SystemLogEntity();
		}
		logEntity.setPage(this.getPage());
		logList = logService.queryPageLog(logEntity);
		return "toLogList";
	}

	/**
	 * 保存日志信息
	 * 
	 * @return
	 */
	public String saveLog() {
		logService.saveSystemLog(logEntity);
		return null;
	}

	public List<SystemLogEntity> getLogList() {
		return logList;
	}

	public void setLogList(List<SystemLogEntity> logList) {
		this.logList = logList;
	}

	public SystemLogEntity getLogEntity() {
		return logEntity;
	}

	public void setLogEntity(SystemLogEntity logEntity) {
		this.logEntity = logEntity;
	}

	public SystemLogService getLogService() {
		return logService;
	}

	public void setLogService(SystemLogService logService) {
		this.logService = logService;
	}

}
