package com.hzw.sunflower.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.hzw.sunflower.common.BaseBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(description = "详细评审评标办法表")
@TableName("t_bid_doc_evaluation_details")
@Data
public class BidDocEvaluationDetails extends BaseBean {

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "评标办法表id")
    private Long docEvaluationId;

    @ApiModelProperty(value = "评分方法")
    private String scoreApproach;

    @ApiModelProperty(value = "评分因素")
    private String scoreFactor;

    @ApiModelProperty(value = "评分标准")
    private String scoreCriteria;

}
