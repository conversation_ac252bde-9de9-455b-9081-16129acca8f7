package com.hzw.ssm.applets.service;

import com.hzw.ssm.applets.dao.ExpertDownFileMapper;
import com.hzw.ssm.applets.entity.ExpertDownFile;
import com.hzw.ssm.applets.exception.ExceptionEnum;
import com.hzw.ssm.applets.utils.RandomCode;
import com.hzw.ssm.applets.utils.ResultJson;
import com.hzw.ssm.expert.dao.PoliciesDetailsMapper;
import com.hzw.ssm.expert.entity.PoliciesDetails;
import com.hzw.ssm.fw.base.BaseService;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.context.ServletContextAware;

import javax.servlet.ServletContext;
import java.util.*;

/**
 * <AUTHOR>
 * @Date 2021-02-07 15:27
 * @Version 1.0
 */
@Service
public class ExpertDownFileService extends BaseService implements ServletContextAware {

    private static final Logger logger = Logger.getLogger(ExpertDownFileService.class);

    private ServletContext servletContext;

    @Override
    public void setServletContext(ServletContext servletContext) {
        this.servletContext = servletContext;
    }

    @Autowired
    private ExpertDownFileMapper expertDownFileMapper;
    @Autowired
    private PoliciesDetailsMapper policiesDetailsMapper;


    public ResultJson insertExpertDownFile(ExpertDownFile expertDownFile) {

        try {
            PoliciesDetails details = policiesDetailsMapper.findPoliciesDetailsById(expertDownFile.getDownloadId());
            String fileName = details.getFileId();

            ExpertDownFile downFile = expertDownFileMapper.queryByExpertIdAndFileId(fileName, expertDownFile.getExpertId());
            if (null != downFile) {
                return new ResultJson(ExceptionEnum.ATTACHMENT_DOWNLOADED.getCode(), ExceptionEnum.ATTACHMENT_DOWNLOADED.getMessage());
            }

            String suffixName = fileName.substring(fileName.lastIndexOf("."));
            //其他文件类型
            String fileType = "0";
            if (".doc".equals(suffixName) || ".docx".equals(suffixName)) {
                fileType = "1";
            } else if (".xls".equals(suffixName) || ".xlsx".equals(suffixName)) {
                fileType = "2";
            }
            expertDownFile.setFileType(fileType);
            List<Map<String, Object>> list = new ArrayList<>();
            String id = RandomCode.getId();
            expertDownFile.setId(id);
            expertDownFile.setDeleteFlag("0");
            expertDownFile.setCreateTime(new Date());
            expertDownFile.setFileName(details.getFileId());
            expertDownFile.setFileOrgName(details.getFileName());
            expertDownFile.setFileSize(String.valueOf(details.getFileSize()));
            int updates = expertDownFileMapper.insertExpertDownFile(expertDownFile);
            if (updates > 0) {
                Map<String, Object> map = new HashMap<>();
                map.put("id", id);
                return new ResultJson(list);
            }
        } catch (Exception e) {
            logger.error(e.getMessage());
        }
        return new ResultJson(ExceptionEnum.SERVER_IS_BUSY.getCode(), ExceptionEnum.SERVER_IS_BUSY.getMessage());
    }

    public ResultJson queryPage(ExpertDownFile expertDownFile) {
        List<ExpertDownFile> policiesies = expertDownFileMapper.queryPage(expertDownFile);
        return new ResultJson(policiesies);
    }

}
