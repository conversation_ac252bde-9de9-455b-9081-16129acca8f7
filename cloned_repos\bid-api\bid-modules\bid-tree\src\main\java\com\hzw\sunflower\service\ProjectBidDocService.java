package com.hzw.sunflower.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.controller.request.DocDataInfoREQ;
import com.hzw.sunflower.controller.request.DocBidRoundREQ;
import com.hzw.sunflower.controller.request.NoticeProcessREQ;
import com.hzw.sunflower.controller.request.ProjectBidDocREQ;
import com.hzw.sunflower.controller.response.DocDetailVO;
import com.hzw.sunflower.controller.response.ProjectTaskVO;
import com.hzw.sunflower.dto.*;
import com.hzw.sunflower.entity.JwtUser;
import com.hzw.sunflower.entity.ProjectBidDoc;
import com.hzw.sunflower.entity.condition.ProjectBidDocCondition;

import java.io.IOException;
import java.util.List;
import java.util.Map;


/**
 * TProjectBidDocService接口
 */
public interface ProjectBidDocService extends IService<ProjectBidDoc> {
    /**
     * 根据条件分页查询列表
     *
     * @param condition 查询条件
     * @return 分页信息
     */
    IPage<ProjectBidDocDTO> findInfoByCondition(ProjectBidDocCondition condition);

    /**
     * 根据主键ID查询信息
     *
     * @param id 主键ID
     * @return TProjectBidDoc信息
     */
    ProjectBidDoc getInfoById(Long id);

    /**
     * 新增
     *
     * @param projectBidDoc
     * @return 是否成功
     */
    Boolean addInfo(ProjectBidDoc projectBidDoc);

    /**
     * 修改单位公司 信息
     *
     * @param projectBidDoc
     * @return 是否成功
     */
    Boolean updateInfo(ProjectBidDoc projectBidDoc);

    /**
     * 根据主键ID删除
     *
     * @param id 主键ID
     * @return 是否成功
     */
    Boolean deleteById(Long id);

    /**
     * 根据主键ID列表批量删除
     *
     * @param idList 主键ID列表
     * @return 是否成功
     */
    Boolean deleteByIds(List<Long> idList);

    /**
     * 新增/修改招标文件信息
     *
     * @param dataInfoREQ
     * @param jwtUser
     * @return 是否成功
     */
    Boolean addDocInfo(DocDataInfoREQ dataInfoREQ, JwtUser jwtUser);

    /**
     * 数据验证
     *
     * @param list
     * @return
     */
    Boolean validate(List<ProjectBidDocREQ> list);

    /**
     * 查询已到发布文件阶段且未关联文件的标段/包
     *
     * @param docBidRoundREQ
     * @return
     */
    List<ProjectBidDocDTO> queryDocProgress(DocBidRoundREQ docBidRoundREQ);


    /**
     * 根据招标文件Id查询招标信息
     *
     * @param docId
     * @return
     */
    List<ProjectBidDocDTO> queryDocInfo(Long docId);

    /**
     * 修改招标文件进度
     *
     * @param noticeProcessREQ
     * @param jwtUser
     * @return
     */
    Boolean updateProgress(NoticeProcessREQ noticeProcessREQ, JwtUser jwtUser);

    /**
     * 招标文件审核内容查询
     *
     * @param docId
     * @return
     */
    List<ReviewBidDocDTO> queryReviewInfo(Long docId);

    /**
     * 根据ID集合查询代办列表
     *
     * @param ids
     * @return
     */
    public Map<Long, ProjectTaskVO> queryAppingTaskMapByIds(List<Long> ids);

    /**
     * 查询公告拟发布时间前24小时未审批结束和到公告拟发布时间尚未发布
     *
     * @return
     */
    List<ProjectBidDoc> queryDocInfoForTask();

    /**
     * 招标文件关联信息
     *
     * @param docId
     * @return
     */
    List<ProjectBidDocDTO> queryRelevantInfo(Long docId);

    /**
     * 撤回待确认流程处理
     *
     * @param noticeProcessREQ
     * @param jwtUser
     * @return
     */
    Boolean modifyWithdrawProgress(NoticeProcessREQ noticeProcessREQ, JwtUser jwtUser);

    /**
     * 查询原标段标书费信息和付款状态
     *
     * @param formerSectionId
     * @return
     */
    List<ReBidInfoDTO> queryRebidInfo(Long formerSectionId);

    /**
     * 根据标段ID查询招标文件id
     *
     * @param docBidRoundREQ
     * @return
     */
    Long queryDocId(DocBidRoundREQ docBidRoundREQ);

    /**
     * 待确认文件全部审批
     *
     * @param list
     * @param jwtUser
     * @return
     */
    Boolean updateAllProgress(List<NoticeProcessREQ> list, JwtUser jwtUser);

    /**
     * 根据文件判断标段状态
     *
     * @param sectionId
     * @return
     */
    Boolean judgeStatusByDoc(Long sectionId);

    /**
     * 详情页面信息
     *
     * @param docId
     * @return
     */
    DocDetailVO getDocDetailInfo(Long docId,Integer BidRound);

    /**
     * 根据文档id 和轮次查询标段信息
     * @param docId
     * @param bidRound
     * @return
     */
    List<DocSectionDetailDTO> queryDocSectionInfo(Long docId, Integer bidRound);

    /**
     * 根据标段集合获取挂网时间
     * @param sectionIds
     * @param bidRound
     * @return
     */
    List<ProjectBidDocDTO> findReleaseTimeBySections(List<Long> sectionIds, Integer bidRound);

    /**
     * 根据标段ID查询招标文件id
     *
     * @param docBidRoundREQ
     * @return
     */
    List<Long> queryDocIdByBidIds(DocBidRoundREQ docBidRoundREQ);

    /**
     * 解析开标一览表，生成模板
     *
     * @param ossId
     * @return
     */
    Result<Long> analysisBidDoc(Long ossId) throws Exception;

    /**
     * 解析开标一览表，生成模板
     *
     * @param dto
     * @return
     */
    public Result<Long> analysisBidDoc(AnalysisBidDocDTO dto)throws Exception ;
}
