package com.hzw.ssm.sys.system.entity;

import java.util.Date;
import java.util.List;

import com.hzw.ssm.fw.base.BaseEntity;
import com.hzw.ssm.fw.util.Page;

public class MenuEntity extends BaseEntity {

	/**
	 * 菜单实体类
	 */
	private static final long serialVersionUID = 8042703708357357837L;

	private String menu_id;// 菜单id
	private String menu_name; // 菜单名称
	private String menu_url; // 路径
	private String parent_menu_id;// 菜单父节点
	private Long sortNo; // 添加人
	private Date create_time;// 添加时间
	private Long deleteFlag;
	private String menu_image_url;

	private String flag;
	private List<MenuEntity> subMenuList;

	private boolean hasMenu = false;

	private String parentMenuName;
	private boolean checked;// 是否默认选中
	private boolean open = true;// 是否默认展开
	private String roleId;
	/** 分页 */
	private Page page;

	public String getMenu_id() {
		return menu_id;
	}

	public void setMenu_id(String menuId) {
		menu_id = menuId;
	}

	public String getMenu_name() {
		return menu_name;
	}

	public void setMenu_name(String menuName) {
		menu_name = menuName;
	}

	public String getMenu_url() {
		return menu_url;
	}

	public void setMenu_url(String menuUrl) {
		menu_url = menuUrl;
	}

	public String getParent_menu_id() {
		return parent_menu_id;
	}

	public void setParent_menu_id(String parentMenuId) {
		parent_menu_id = parentMenuId;
	}

	public Long getSortNo() {
		return sortNo;
	}

	public void setSortNo(Long sortNo) {
		this.sortNo = sortNo;
	}

	public Date getCreate_time() {
		return create_time;
	}

	public void setCreate_time(Date createTime) {
		create_time = createTime;
	}

	public Long getDeleteFlag() {
		return deleteFlag;
	}

	public void setDeleteFlag(Long deleteFlag) {
		this.deleteFlag = deleteFlag;
	}

	public String getMenu_image_url() {
		return menu_image_url;
	}

	public void setMenu_image_url(String menuImageUrl) {
		menu_image_url = menuImageUrl;
	}

	public List<MenuEntity> getSubMenuList() {
		return subMenuList;
	}

	public void setSubMenuList(List<MenuEntity> subMenuList) {
		this.subMenuList = subMenuList;
	}

	public boolean isHasMenu() {
		return hasMenu;
	}

	public void setHasMenu(boolean hasMenu) {
		this.hasMenu = hasMenu;
	}

	public Page getPage() {
		return page;
	}

	public void setPage(Page page) {
		this.page = page;
	}

	public String getParentMenuName() {
		return parentMenuName;
	}

	public void setParentMenuName(String parentMenuName) {
		this.parentMenuName = parentMenuName;
	}

	public String getFlag() {
		return flag;
	}

	public void setFlag(String flag) {
		this.flag = flag;
	}

	public boolean isChecked() {
		return checked;
	}

	public void setChecked(boolean checked) {
		this.checked = checked;
	}

	public boolean isOpen() {
		return open;
	}

	public void setOpen(boolean open) {
		this.open = open;
	}

	public String getRoleId() {
		return roleId;
	}

	public void setRoleId(String roleId) {
		this.roleId = roleId;
	}

}
