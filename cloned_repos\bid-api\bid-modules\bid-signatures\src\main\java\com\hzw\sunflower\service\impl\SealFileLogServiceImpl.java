package com.hzw.sunflower.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzw.sunflower.dao.SealFileLogMapper;
import com.hzw.sunflower.dto.SealFileLogDTO;
import com.hzw.sunflower.entity.SealFileLog;
import com.hzw.sunflower.entity.condition.SealFileLogCondition;
import com.hzw.sunflower.exception.ExceptionEnum;
import com.hzw.sunflower.exception.SunFlowerException;
import com.hzw.sunflower.service.SealFileLogService;
import org.springframework.stereotype.Service;

import java.util.List;


/**
* SealFileLogService接口实现
*
* <AUTHOR>
* @version 1.0.0 2021-04-07
*/
@Service
public class SealFileLogServiceImpl extends ServiceImpl<SealFileLogMapper, SealFileLog> implements SealFileLogService {

    /**
    * 根据条件分页查询列表
    *
    * @param condition 查询条件
    * @return 分页数据
    */
    @Override
    public IPage<SealFileLog> findInfoByCondition(SealFileLogCondition condition) {
        IPage<SealFileLog> page = condition.buildPage();
        QueryWrapper<SealFileLog> queryWrapper = condition.buildQueryWrapper(SealFileLog.class);
        return this.page(page, queryWrapper);
    }

    /**
    * 根据主键ID查询
    *
    * @param id 主键ID
    * @return
    */
    @Override
    public SealFileLog getInfoById(Long id) {
        return this.getById(id);
    }

    /**
    * 新增
    *
    * @param sealFileLog
    * @return 是否成功
    */
    @Override
    public Boolean addInfo(SealFileLogDTO sealFileLog) {
        return this.save(sealFileLog);
    }

    /**
    * 修改
    *
    * @param sealFileLog
    * @return 是否成功
    */
    @Override
    public Boolean updateInfo(SealFileLogDTO sealFileLog) {
        return this.updateById(sealFileLog);
    }

    /**
    * 根据主键ID删除
    *
    * @param id 主键ID
    * @return 是否成功
    */
    @Override
    public Boolean deleteById(Long id) {
        return this.removeById(id);
    }

    /**
    * 根据主键ID列表批量删除
    *
    * @param idList 主键ID列表
    * @return 是否成功
    */
    @Override
    public Boolean deleteByIds(List<Long> idList) {
        return this.removeByIds(idList);
    }

}