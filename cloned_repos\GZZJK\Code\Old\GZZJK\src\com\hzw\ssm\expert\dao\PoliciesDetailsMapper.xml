<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!-- 政策法规目录详情(T_POLICIES_DETAILS) -->
<mapper namespace="com.hzw.ssm.expert.dao.PoliciesDetailsMapper">
    <!-- This code was generated by TableGo tools, mark 1 begin. -->
    <!-- 字段映射 -->
    <resultMap id="policiesDetailsMap" type="com.hzw.ssm.expert.entity.PoliciesDetails">
        <id column="ID" property="id" jdbcType="VARCHAR" />
        <result column="TITLE" property="title" jdbcType="VARCHAR" />
        <result column="POLICIES_ID" property="policiesId" jdbcType="VARCHAR" />
        <result column="STATUS" property="status" jdbcType="INTEGER" />
        <result column="TOP" property="top" jdbcType="INTEGER" />
        <result column="FILE_ID" property="fileId" jdbcType="VARCHAR" />
        <result column="ON_TIME" property="onTime" jdbcType="TIMESTAMP" />
        <result column="OFF_TIME" property="offTime" jdbcType="TIMESTAMP" />
        <result column="CREATE_USER" property="createUser" jdbcType="VARCHAR" />
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
        <result column="UPDATE_USER" property="updateUser" jdbcType="VARCHAR" />
        <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP" />
        <result column="SRC" property="src" jdbcType="VARCHAR" />
        <result column="FILE_NAME" property="fileName" jdbcType="VARCHAR" />
        <result column="FILE_SIZE" property="fileSize" jdbcType="BIGINT" />

        <result column="TXT" property="txt" jdbcType="VARCHAR" />
        <result column="NUMBER_NO" property="numberNo" jdbcType="VARCHAR" />
    </resultMap>
    <!-- This code was generated by TableGo tools, mark 1 end. -->

    <!-- This code was generated by TableGo tools, mark 2 begin. -->
    <!-- 表查询字段 -->

    <sql id="allColumns">
        pd.ID, pd.TITLE, pd.POLICIES_ID, pd.STATUS, pd.TOP, pd.FILE_ID, pd.ON_TIME, pd.OFF_TIME, 
        pd.CREATE_USER, pd.CREATE_TIME, pd.UPDATE_USER, pd.UPDATE_TIME, pd.SRC, pd.TXT,pd.FILE_NAME,pd.FILE_SIZE,pd.NUMBER_NO
    </sql>
    <!-- This code was generated by TableGo tools, mark 2 end. -->

    <!-- 查询所有政策法规目录详情 -->
    <select id="findPoliciesDetailsAll" resultMap="policiesDetailsMap">
        SELECT
            <include refid="allColumns" />
        FROM T_POLICIES_DETAILS pd
    </select>

    <!-- 根据条件参数查询政策法规目录详情列表 -->
    <select id="findPoliciesDetailsByCondition" resultMap="policiesDetailsMap" parameterType="map">
        SELECT
        <include refid="allColumns" />
        FROM T_POLICIES_DETAILS pd WHERE 1 = 1
        <if test="title != null and title != ''">
            AND pd.TITLE LIKE '%' || #{title} || '%'
        </if>
        <if test="policiesId != null and policiesId != ''">
            AND pd.POLICIES_ID LIKE '%' || #{policiesId} || '%'
        </if>
        <if test="status != null">
            AND pd.STATUS = #{status}
        </if>
        <if test="top != null">
            AND pd.TOP = #{top}
        </if>
        <if test="fileId != null and fileId != ''">
            AND pd.FILE_ID LIKE '%' || #{fileId} || '%'
        </if>
        <if test="onTime != null">
            AND pd.ON_TIME = #{onTime}
        </if>
        <if test="offTime != null">
            AND pd.OFF_TIME = #{offTime}
        </if>
        <if test="createUser != null and createUser != ''">
            AND pd.CREATE_USER LIKE '%' || #{createUser} || '%'
        </if>
        <if test="createTime != null">
            AND pd.CREATE_TIME = #{createTime}
        </if>
        <if test="updateUser != null and updateUser != ''">
            AND pd.UPDATE_USER LIKE '%' || #{updateUser} || '%'
        </if>
        <if test="updateTime != null">
            AND pd.UPDATE_TIME = #{updateTime}
        </if>
        <if test="src != null and src != ''">
            AND pd.SRC LIKE '%' || #{src} || '%'
        </if>
        <if test="txt != null and txt != ''">
            AND pd.TXT LIKE '%' || #{txt} || '%'
        </if>
    </select>



    <!-- 根据条件参数查询政策法规目录详情列表 -->
    <select id="findTitlePoliciesDetailsByCondition" resultMap="policiesDetailsMap" parameterType="map">
        SELECT
        <include refid="allColumns" />
        FROM T_POLICIES_DETAILS pd WHERE 1 = 1
        <if test="title != null and title != ''">
            AND pd.TITLE = #{title}
        </if>

    </select>
    <!-- 根据条件参数查询政策法规目录详情列表 -->
    <select id="queryPagePoliciesDetailsByCondition" resultMap="policiesDetailsMap" parameterType="map">
        SELECT
        <include refid="allColumns" />
        FROM T_POLICIES_DETAILS pd WHERE 1 = 1
        <if test="title != null and title != ''">
            AND pd.TITLE LIKE '%' || #{title} || '%'
        </if>
        <if test="policiesId != null and policiesId != ''">
            AND pd.POLICIES_ID LIKE '%' || #{policiesId} || '%'
        </if>
        <if test="status != null">
            AND pd.STATUS = #{status}
        </if>

        <if test="id != null">
            AND pd.ID = #{id}
        </if>

        <if test="top != null">
            AND pd.TOP = #{top}
        </if>
        <if test="fileId != null and fileId != ''">
            AND pd.FILE_ID LIKE '%' || #{fileId} || '%'
        </if>
        <if test="onTime != null">
            and to_char(pd.ON_TIME,'yyyy-mm-dd')  <![CDATA[  >=  ]]> to_char(#{onTime},'yyyy-mm-dd')
        </if>
        <if test="offTime != null">
            and to_char(pd.OFF_TIME,'yyyy-mm-dd')  <![CDATA[  >=  ]]> to_char(#{offTime},'yyyy-mm-dd')
        </if>
        <if test="createUser != null and createUser != ''">
            AND pd.CREATE_USER LIKE '%' || #{createUser} || '%'
        </if>
        <if test="createTime != null">
            AND pd.CREATE_TIME = #{createTime}
        </if>
        <if test="updateUser != null and updateUser != ''">
            AND pd.UPDATE_USER LIKE '%' || #{updateUser} || '%'
        </if>
        <if test="updateTime != null">
            AND pd.UPDATE_TIME = #{updateTime}
        </if>
        <if test="src != null and src != ''">
            AND pd.SRC LIKE '%' || #{src} || '%'
        </if>
        <if test="txt != null and txt != ''">
            AND pd.TXT LIKE '%' || #{txt} || '%'
        </if>
        <choose>
            <when test="order != null and order != ''">
                ${order}
            </when>
            <otherwise>
                order by pd.TOP desc, pd.UPDATE_TIME desc
            </otherwise>
        </choose>
    </select>


    <!-- 根据主键查询政策法规目录详情信息 -->
    <select id="findPoliciesDetailsByIds" resultMap="policiesDetailsMap" parameterType="list">
        SELECT
            <include refid="allColumns" />
        FROM T_POLICIES_DETAILS pd WHERE pd.ID IN
        <foreach collection="list" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>



    <!-- 根据主键查询政策法规目录详情信息 -->
    <select id="findPoliciesDetailsById" resultMap="policiesDetailsMap" parameterType="list">
        SELECT
        <include refid="allColumns" />
        FROM T_POLICIES_DETAILS pd WHERE pd.ID=
            #{id}

    </select>
    <!-- 新增政策法规目录详情信息 -->
    <insert id="addPoliciesDetails">
        INSERT INTO T_POLICIES_DETAILS (
            ID, TITLE, POLICIES_ID, STATUS, TOP, FILE_ID, ON_TIME, OFF_TIME, 
            CREATE_USER, CREATE_TIME, UPDATE_USER, UPDATE_TIME, SRC, TXT,FILE_NAME,FILE_SIZE,NUMBER_NO
        ) VALUES (
            #{id,jdbcType=VARCHAR},
            #{title,jdbcType=VARCHAR},
            #{policiesId,jdbcType=VARCHAR},
            #{status,jdbcType=INTEGER},
            #{top,jdbcType=INTEGER},
            #{fileId,jdbcType=VARCHAR},
            #{onTime,jdbcType=TIMESTAMP},
            #{offTime,jdbcType=TIMESTAMP},
            #{createUser,jdbcType=VARCHAR},
            #{createTime,jdbcType=TIMESTAMP},
            #{updateUser,jdbcType=VARCHAR},
            #{updateTime,jdbcType=TIMESTAMP},
            #{src,jdbcType=VARCHAR},
            #{txt,jdbcType=CLOB},
            #{fileName,jdbcType=VARCHAR},
            #{fileSize,jdbcType=BIGINT},
            #{numberNo,jdbcType=VARCHAR}

        )
    </insert>

    <!-- 批量新增政策法规目录详情信息 -->
    <insert id="addPoliciesDetailss" parameterType="list">
        INSERT INTO T_POLICIES_DETAILS (
            ID, TITLE, POLICIES_ID, STATUS, TOP, FILE_ID, ON_TIME, OFF_TIME,
            CREATE_USER, CREATE_TIME, UPDATE_USER, UPDATE_TIME, SRC, TXT,NUMBER_NO
        )
        <foreach collection="list" index="index" item="item" separator="UNION ALL">
            SELECT
                #{item.id,jdbcType=VARCHAR},
                #{item.title,jdbcType=VARCHAR},
                #{item.policiesId,jdbcType=VARCHAR},
                #{item.status,jdbcType=INTEGER},
                #{item.top,jdbcType=INTEGER},
                #{item.fileId,jdbcType=VARCHAR},
                #{item.onTime,jdbcType=TIMESTAMP},
                #{item.offTime,jdbcType=TIMESTAMP},
                #{item.createUser,jdbcType=VARCHAR},
                #{item.createTime,jdbcType=TIMESTAMP},
                #{item.updateUser,jdbcType=VARCHAR},
                #{item.updateTime,jdbcType=TIMESTAMP},
                #{item.src,jdbcType=VARCHAR},
                #{item.txt,jdbcType=CLOB},
                #{item.numberNo,jdbcType=VARCHAR}
            FROM DUAL
        </foreach>
    </insert>

    <!-- 修改政策法规目录详情信息 -->
    <update id="updatePoliciesDetails">
        UPDATE T_POLICIES_DETAILS
        <set>
            <if test="title != null">
                TITLE = #{title,jdbcType=VARCHAR},
            </if>
            <if test="policiesId != null">
                POLICIES_ID = #{policiesId,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                STATUS = #{status,jdbcType=INTEGER},
            </if>
            <if test="top != null">
                TOP = #{top,jdbcType=INTEGER},
            </if>
            <if test="fileId != null">
                FILE_ID = #{fileId,jdbcType=VARCHAR},
            </if>
            <if test="onTime != null">
                ON_TIME = #{onTime,jdbcType=TIMESTAMP},
            </if>
            <if test="offTime != null">
                OFF_TIME = #{offTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createUser != null">
                CREATE_USER = #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateUser != null">
                UPDATE_USER = #{updateUser,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="src != null">
                SRC = #{src,jdbcType=VARCHAR},
            </if>
            <if test="txt != null">
                TXT = #{txt,jdbcType=CLOB},
            </if>
            <if test="fileName != null">
                FILE_NAME = #{fileName,jdbcType=VARCHAR},
            </if>
            <if test="fileSize != null">
                FILE_SIZE = #{fileSize,jdbcType=BIGINT},
            </if>
            <if test="numberNo != null">
                NUMBER_NO=  #{numberNo,jdbcType=VARCHAR}
            </if>

        </set>
        WHERE ID = #{id}
    </update>

    <!-- 批量修改政策法规目录详情信息 -->
    <update id="updatePoliciesDetailss" parameterType="list">
        <foreach collection="list" index="index" item="item" open="BEGIN" close="END;">
            UPDATE T_POLICIES_DETAILS
            <set>
                <if test="item.title != null">
                    TITLE = #{item.title, jdbcType=VARCHAR},
                </if>
                <if test="item.policiesId != null">
                    POLICIES_ID = #{item.policiesId, jdbcType=VARCHAR},
                </if>
                <if test="item.status != null">
                    STATUS = #{item.status, jdbcType=INTEGER},
                </if>
                <if test="item.top != null">
                    TOP = #{item.top, jdbcType=INTEGER},
                </if>
                <if test="item.fileId != null">
                    FILE_ID = #{item.fileId, jdbcType=VARCHAR},
                </if>
                <if test="item.onTime != null">
                    ON_TIME = #{item.onTime, jdbcType=TIMESTAMP},
                </if>
                <if test="item.offTime != null">
                    OFF_TIME = #{item.offTime, jdbcType=TIMESTAMP},
                </if>
                <if test="item.createUser != null">
                    CREATE_USER = #{item.createUser, jdbcType=VARCHAR},
                </if>
                <if test="item.createTime != null">
                    CREATE_TIME = #{item.createTime, jdbcType=TIMESTAMP},
                </if>
                <if test="item.updateUser != null">
                    UPDATE_USER = #{item.updateUser, jdbcType=VARCHAR},
                </if>
                <if test="item.updateTime != null">
                    UPDATE_TIME = #{item.updateTime, jdbcType=TIMESTAMP},
                </if>
                <if test="item.src != null">
                    SRC = #{item.src, jdbcType=VARCHAR},
                </if>
                <if test="item.txt != null">
                    TXT = #{item.txt, jdbcType=CLOB}
                </if>
            </set>
            WHERE ID = #{item.id};
        </foreach>
    </update>

    <!-- 根据主键删除政策法规目录详情 -->
    <delete id="deletePoliciesDetailsById" parameterType="string">
        DELETE FROM T_POLICIES_DETAILS WHERE ID = #{id}
    </delete>

    <!-- 根据主键批量删除政策法规目录详情 -->
    <delete id="deletePoliciesDetailsByIds" parameterType="list">
        DELETE FROM T_POLICIES_DETAILS WHERE ID IN
        <foreach collection="list" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>