package com.hzw.ssm.applets.action;

import com.hzw.ssm.applets.service.AppService;
import com.hzw.ssm.applets.utils.GetJsonReqData;
import com.hzw.ssm.applets.utils.GetJsonRespData;
import com.hzw.ssm.applets.utils.JsonToObject;
import com.hzw.ssm.applets.utils.ResultJson;
import com.hzw.ssm.expert.entity.ExpertInfoEntity;
import net.sf.json.JSONObject;
import org.apache.struts2.convention.annotation.Action;
import org.apache.struts2.convention.annotation.Namespace;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @Date 2021-02-04 13:16
 * @Version 1.0
 */
@Namespace("/applets/wxExpertAction")
public class WxExpertAction {

    @Autowired
    private AppService appService;

    /**
     * 获取基本信息-首页
     *
     * @return
     */
    @Action("personalCenter")
    public String personalCenter() {

        JSONObject jsonReqData = GetJsonReqData.getJsonReqData();
        ResultJson resultJson = appService.getOne(jsonReqData);
        GetJsonRespData.getJsonRespData(resultJson);
        return null;
    }

    /**
     * 第一次提交审核
     *
     * @return
     */
    @Action("submitExamine")
    public String submitExamine() {

        JSONObject jsonReqData = GetJsonReqData.getJsonReqData();
        ResultJson resultJson = appService.submitExamine(jsonReqData);
        GetJsonRespData.getJsonRespData(resultJson);
        return null;
    }

    /**
     * 获取基本信息-个人信息
     *
     * @return
     */
    @Action("personalInformation")
    public String personalInformation() {

        JSONObject jsonReqData = GetJsonReqData.getJsonReqData();
        ResultJson resultJson = appService.personalInformation(jsonReqData);
        GetJsonRespData.getJsonRespData(resultJson);
        return null;
    }

    /**
     * 修改个人信息
     *
     * @return
     */
    @Action("updateInfo")
    public String updateInfo() {

        JSONObject jsonReqData = GetJsonReqData.getJsonReqData();
        String toString = jsonReqData.getJSONObject("formData").toString();
        ExpertInfoEntity educationInfo = JsonToObject.jsonToObject(toString, ExpertInfoEntity.class);
        ResultJson resultJson = appService.updateExpertInfo(educationInfo);
        GetJsonRespData.getJsonRespData(resultJson);
        return null;
    }

    /**
     * 修改手机
     *
     * @return
     */
    @Action("modifyPhone")
    public String modifyPhone() {

        JSONObject jsonReqData = GetJsonReqData.getJsonReqData();
        String toString = jsonReqData.getJSONObject("formData").toString();
        ExpertInfoEntity expertInfoEntity = JsonToObject.jsonToObject(toString, ExpertInfoEntity.class);
        ResultJson resultJson = appService.modifyPhone(expertInfoEntity);
        GetJsonRespData.getJsonRespData(resultJson);
        return null;
    }

}
