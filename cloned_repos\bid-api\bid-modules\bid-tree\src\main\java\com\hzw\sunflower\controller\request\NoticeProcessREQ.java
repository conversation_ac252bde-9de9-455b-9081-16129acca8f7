package com.hzw.sunflower.controller.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date ：Created in 2021/7/9 10:29
 * @description：修改招标文件进度请求体
 * @version: 1.0
 */
@Data
public class NoticeProcessREQ implements Serializable {

    @ApiModelProperty(value = "招标文件ID", position = 1)
    @NotNull(message = "招标文件主键不能为空")
    private Long docId;

    @ApiModelProperty(value = "招标文件进度 2：待确认 3：已撤回 4：已确认 5：已发布 6：已退回 7：撤回待确认", position = 2)
    private Integer noticeProgress;

    @ApiModelProperty(value = "项目ID", position = 3)
    @NotNull(message = "项目主键不能为空")
    private Long projectId;

    @ApiModelProperty(value = "任务ID", position = 4)
    private String taskId;

    @ApiModelProperty(value = "处理意见", position = 5)
    private String message;

    @ApiModelProperty(value = "审核实例ID", position = 6)
    private String processInstanceId;

    @ApiModelProperty(value = "撤回待确认流程 1：退回撤回 2：同意撤回", position = 7)
    private Integer withdrawProgress;
}
