///***
/// * picker - 单选
/// */

import 'package:flutter/material.dart';

import 'package:flutter_main_app/src/constants/theme.dart';
import 'package:flutter_main_app/src/utils/ScreenUtils.dart';

import 'package:flutter_main_app/src/models/common/CheckBoxItemModel.dart';

class CustomSingleSelectPicker extends StatefulWidget {
  CustomSingleSelectPicker({
    Key key,
    @required this.settings,
    this.checkboxWidth = 240.0,
  }) : assert(settings != null),
    assert(checkboxWidth != null && checkboxWidth > 0.0),
    super(key: key);

  final List<CheckBoxItemModel<String>> settings;

  final double checkboxWidth;

  @override
  _CustomSingleSelectPickerState createState() => _CustomSingleSelectPickerState();
}

class _CustomSingleSelectPickerState extends State<CustomSingleSelectPicker> {

  List<CheckBoxItemModel<String>> _settings;

  @override
  void initState() {
    super.initState();

    this._settings = List.from(widget.settings);
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      child: Container(
        width: ScreenUtils.screenWidth,
        color: themeContentBackgroundColor,
        child: SafeArea(
          top: false,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              /// * 功能按钮 - 取消|确定
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 30.0),
                width: ScreenUtils.screenWidth,
                height: 42.0,
                color: const Color(0xffe5e5e5),
                child: Row(
                  mainAxisSize: MainAxisSize.max,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: <Widget>[
                    GestureDetector(
                      child: Text(
                        '取消',
                        style: TextStyle(
                          fontSize: 14.0,
                          color: const Color(0xff999999),
                        ),
                      ),
                      onTap: () {
                        this._checkboxCanceled(context);
                      },
                    ),
                    GestureDetector(
                      child: Text(
                        '确定',
                        style: TextStyle(
                          fontSize: 14.0,
                          color: themeActiveColor,
                        ),
                      ),
                      onTap: () {
                        this._checkboxConfirmed(context);
                      },
                    ),
                  ],
                ),
              ),
              /// * 选项列表
              Container(
                constraints: BoxConstraints(
                  minHeight: 0.0,
                  maxHeight: 195.0,
                ),
                child: ListView.builder(
                  shrinkWrap: true,
                  physics: BouncingScrollPhysics(),
                  itemCount: this._settings.length,
                  itemBuilder: (BuildContext context, int index) {
                    return Center(
                      child: Container(
                        width: widget.checkboxWidth,
                        child: Center(
                          child: CheckboxListTile(
                            value: this._settings[index].isChecked,
                            onChanged: (bool isChecked) {
                              this._checkboxValueChanged(isChecked, index);
                            },
                            activeColor: themeActiveColor,
                            controlAffinity: ListTileControlAffinity.leading,
                            dense: true,
                            isThreeLine: false,
                            title: Text(
                              this._settings[index].label,
                            ),
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  ///**
  /// * 点击取消
  /// * 路由弹出，什么都不返回
  /// *
  /// * @param {BuildContext} context
  /// */
  void _checkboxCanceled(BuildContext context) {
    Navigator.of(context).pop();
  }

  ///**
  /// * 点击确定
  /// * 路由弹出，并返回当前设置
  /// *
  /// * @param {BuildContext} context
  /// */
  void _checkboxConfirmed(BuildContext context) {
    CheckBoxItemModel setting;

    for (int i = 0, len = this._settings.length; i < len; i++) {
      if (this._settings[i].isChecked) {
        setting = this._settings[i];
        break;
      }
    }

    Navigator.of(context).pop(setting);
  }

  ///**
  /// * 多选框的值改变事件
  /// *
  /// * @param {bool} isChecked
  /// * @param {int} index
  /// */
  void _checkboxValueChanged(bool isChecked, int index) {
    if (isChecked) {
      /// * 选中
      final List<CheckBoxItemModel<String>> settings = List.from(this._settings);

      for (int i = 0, len = settings.length; i < len; i++) {
        if (i == index) {
          settings[i].isChecked = true;
        } else {
          settings[i].isChecked = false;
        }
      }

      setState(() {
        this._settings = List.from(settings);
      });
    } else {
      /// * 取消选中
      setState(() {
        this._settings[index].isChecked = isChecked;
      });
    }
  }

}
