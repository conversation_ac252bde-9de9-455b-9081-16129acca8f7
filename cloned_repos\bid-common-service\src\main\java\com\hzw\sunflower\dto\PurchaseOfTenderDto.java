package com.hzw.sunflower.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * <AUTHOR>
 * @Date 2022/11/24 18:24
 * @Version 1.0
 */
@ApiModel(description = "购标信息DTO")
@Data
@EqualsAndHashCode
public class PurchaseOfTenderDto {

    @ApiModelProperty("关联项目id")
    private Long projectId;

    @ApiModelProperty("包id")
    private Long sectionId;

    @ApiModelProperty("轮次")
    private Integer bidRound;

    @ApiModelProperty("供应商id")
    private Long tendererId;

    @ApiModelProperty("供应商名称")
    private String supplierName;

    @ApiModelProperty(value = "投标联系人id")
    private Long bidContactsId;

    @ApiModelProperty(value = "投标联系人名称")
    private String bidContactsName;

    @ApiModelProperty(value = "联系人手机号")
    private String contactsPhone;

    @ApiModelProperty(value = "是否到场：0：否  1：是")
    private Integer signedIn;

    @ApiModelProperty(value = "投标文件初审情况：0：否  1：是")
    private Integer firstTrial;

    @ApiModelProperty(value = "开标一览表 多个逗号隔开：1,2,3")
    private String openBidFileIds;

    @ApiModelProperty(value = "附件ids 以逗号隔开")
    private String fileIds;

    @ApiModelProperty(value = "已标价工程量清单文件id")
    private String boqFileIds;

}
