package com.hzw.sunflower.constant.constantenum;

public enum RoleCodeGroupEnum {

    BMFZR("bmfzr","部门负责人"),
    FGSZJL("fgszjl","分公司总经理"),
            ;

    private String code;
    private String desc;

    RoleCodeGroupEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String type) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

}
