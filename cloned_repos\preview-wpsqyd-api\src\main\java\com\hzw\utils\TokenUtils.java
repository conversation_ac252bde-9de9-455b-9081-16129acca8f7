package com.hzw.utils;

import com.hzw.redis.RedisCache;
import io.jsonwebtoken.*;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;

public class TokenUtils {
    private static final String SECRET_KEY = "preview_token"; // 需要替换为更安全的密钥
    private static final long EXPIRE_TIME = 30 * 60 * 1000; // 30分钟有效期

    // 生成一次性Token
    public static String generateToken(String value) {
        String jws = Jwts.builder()
                .claim("key", value)
                .setExpiration(new Date(System.currentTimeMillis() + EXPIRE_TIME))
                .signWith(SignatureAlgorithm.HS256, SECRET_KEY)
                .compact();
        return jws;
    }

    // 校验Token并获取值
    public static String validateToken(String token) {
        Jws<Claims> jws = Jwts.parser()
                .setSigningKey(SECRET_KEY)
                .parseClaimsJws(token);
        return jws.getBody().get("key", String.class);
    }

    public static void main(String[] args) {
        // 生成Token
        String token = TokenUtils.generateToken("3GAK-C24-3D4-306-G08江苏省招标中心互联网渗透撒打算打算打算打算打算打算打算打算打算打算打算打算打算打算打算大大撒打算打算大大是大地测试脆弱性汇总表江苏省招标中心互联网渗透撒打算打算打算打算打算打算打算打算打算打算打算打算打算打算打算大大撒打算打算大大是大地测试脆弱性汇总表江苏省招标中心互联网渗透撒打算打算打算打算打算打算打算打算打算打算打算打算打算打算打算大大撒打算打算大大是大地测试脆弱性汇总表打算打算打算打算爱大.docx");
        System.out.println("Generated token: " + token);
        // 校验Token
        String value = TokenUtils.validateToken(token);
        if (value != null) {
            System.out.println("Valid value: " + value);
        } else {
            System.out.println("Invalid or expired token");
        }
    }
}

