package com.hzw.sunflower.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Data
@ApiModel(description = "报名待处理列表DTO")
public class ApplyInfoListDTO implements Serializable {

    @ApiModelProperty(value = "项目编码")
    private String projectNumber;
    @ApiModelProperty(value = "项目id")
    private Long projectId;
    @ApiModelProperty(value = "标段id")
    private Long bidId;
    @ApiModelProperty(value = "初始项目主键")
    private Long baseProjectId;
    @ApiModelProperty(value = "状态(1.关注待确认3.材料待确认)")
    private String applyStatus;
    @ApiModelProperty(value = "项目名称")
    private String projectName;
    @ApiModelProperty(value = "采购编码")
    private String purchaseNumber;
    @ApiModelProperty(value = "采购名称")
    private String purchaseName;
    @ApiModelProperty(value = "标段包编号")
    private String packageNumber;
    @ApiModelProperty(value = "创建时间")
    private String createdTime;
    @ApiModelProperty(value = "是否重新招标 0.未重新招标 1.已重新招标")
    private Integer reTender;
    @ApiModelProperty(value = "包件数量")
    private int segmentNum;
    @ApiModelProperty(value = "包件bidround")
    private Integer bidRound;
    @ApiModelProperty(value = "包件bidround")
    private  List<String> packagesBidRoundOne;
    @ApiModelProperty(value = "包件bidround")
    private  List<String> packagesBidRoundTwo;
    @ApiModelProperty(value = "是否划分标段，0-无，1-有")
    private String packageSegmentStatus;
    @ApiModelProperty(value = "采购方式")
    private List<String> purchaseMode;

}
