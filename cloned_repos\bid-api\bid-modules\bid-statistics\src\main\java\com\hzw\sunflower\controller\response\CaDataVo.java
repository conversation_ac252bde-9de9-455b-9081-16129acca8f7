package com.hzw.sunflower.controller.response;

import cn.afterturn.easypoi.excel.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/3/9 16:25 星期四
 */
@Data
public class CaDataVo implements Serializable {


    @ApiModelProperty("项目Id")
    private Integer projectId;

    @Excel(name = "项目编号",orderNum = "1",width=25)
    @ApiModelProperty("项目编号")
    private String projectNo;

    @Excel(name = "项目名称",orderNum = "2",width=25)
    @ApiModelProperty("项目名称")
    private String projectName;

    @Excel(name = "标段包号",orderNum = "3",width=25,replace = {"-_null"})
    @ApiModelProperty("标段包号")
    private String packageNumber;

    @Excel(name = "标段包名称",orderNum = "4",width=25,replace = {"-_null"})
    @ApiModelProperty("标段包名称")
    private String packageName;

    @ApiModelProperty("投标文件递交截止时间")
    private Date submitEndTime;

    @Excel(name = "已递交响应文件供应商数量",orderNum = "5",width=25)
    @ApiModelProperty("已递交响应文件供应商数量")
    private Integer count;

    @ApiModelProperty("国际标")
    private String isInternational;

    @ApiModelProperty("政府采购  924100 政府采购 ")
    private String isGovernment;


}
