package com.hzw.sunflower.controller.request;

import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import lombok.Data;

/**
 * <AUTHOR>
 * @date ：Created in 2021/5/11 17:16
 * @description：模板信息表
 * @modified By：`
 * @version: 1.0
 */
@ApiModel(description = "短信记录表")
@Data
public class TemplateReq{

    private Long id;

    /**
     * 模板code
     */
    private String templateCode;

    /**
     * 模板名称
     */
    private String templateName;

    /**
     * 模板内容
     */
    private String templateContent;

    /**
     * 模板二级code
     */
    private String templateSecondCode;
}
