package com.hzw.sunflower.service;

import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.dto.CommonApplyInfoDto;
import com.hzw.sunflower.dto.ProjectBidCountsDTO;
import com.hzw.sunflower.dto.ReviewCompanyDTO;

import java.util.List;
import java.util.Map;

public interface CommonApplyInfoService {

    public List<CommonApplyInfoDto> queryApplyListByDownLoad(Long project, Long subId,String tendererName,Integer bidRound);

    /**
     *
     * @param project
     * @param subId
     * @param tendererName
     * @param bidRound
     * @param companyIds  用户排除的供应商id
     * @return
     */
    public List<CommonApplyInfoDto> queryApplyListByDownLoad(Long project, Long subId,String tendererName,Integer bidRound,String[] companyIds);

    /**
     * 根据标段ID查询供应商信息
     * @param project
     * @param subIds
     * @param tendererName
     * @return
     */
    public List<CommonApplyInfoDto> queryApplyListBySubId(Long project, List<Long> subIds,Map<String,Object> map);


    /**
     *  保存重新招标供应商报名信息
     * @param oldBidderType
     * @param projectId
     * @param bidId
     * @param applyIds
     * @return
     */
    public Result<Object> saveReProjectSupplierApplyInfo(Integer oldBidderType,Long projectId,Long bidId,List<Long> applyIds,Integer bidRound);

    List<CommonApplyInfoDto> queryApplyListByDownLoadSign(Long project, Long subId, String tendererName, Integer bidRound, String[] companyIds);

    /**
     * 根据项目id查询已购标数
     * @param id
     * @return
     */
    Integer queryPurchasedBids( Long id,Integer bidRound);

    /**
     * 判断招标人是否有权限
     * @param projectId
     * @return
     */
    Boolean showOrNot(Long userId,String projectId);

    /**
     * 复制采购前期项目标段的报名信息到对应采购实施项目的标段
     * @param sectionIdList 采购实施项目id
     * @return
     */
    Boolean copyApplyInfoFromPreSection(List<Long> sectionIdList);

    /**
     * 查询无包项目购标数量
     * @param projectIds
     * @return
     */
    List<ProjectBidCountsDTO> queryNoBagPurchasedBidsByProjectIds(List<Long> projectIds);

    /**
     * 判断文件下载情况
     * @param companyNameList
     * @return
     */
    List<ReviewCompanyDTO> countFileDownload(List<ReviewCompanyDTO> companyNameList);
}
