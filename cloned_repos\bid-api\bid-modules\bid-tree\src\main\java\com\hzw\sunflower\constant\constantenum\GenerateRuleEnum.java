package com.hzw.sunflower.constant.constantenum;

/**
 * <AUTHOR>
 * @date ：Created in 2021/6/29 10:02
 * @description： 生成规则
 * @version: 1.0
 */
public enum GenerateRuleEnum {

    MERGE(1, "直接拼接开标汇总表"),
    JOIN(2, "从x行解析拼接");

    private Integer type;

    private String name;

    GenerateRuleEnum(Integer type, String name) {
        this.type = type;
        this.name = name;
    }

    public Integer getType() {
        return type;
    }

    public String getName() {
        return name;
    }

}
