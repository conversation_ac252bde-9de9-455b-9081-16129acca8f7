package com.hzw.sunflower.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.hzw.sunflower.constant.constantenum.EnterSourceEnum;
import com.hzw.sunflower.constant.constantenum.EntrustUserType;
import com.hzw.sunflower.constant.constantenum.PackageStatusEnum;
import com.hzw.sunflower.constant.constantenum.TemplateCodeEnum;
import com.hzw.sunflower.dao.ProjectRelevantMapper;
import com.hzw.sunflower.dto.*;
import com.hzw.sunflower.entity.Project;
import com.hzw.sunflower.exception.ExceptionEnum;
import com.hzw.sunflower.exception.SunFlowerException;
import com.hzw.sunflower.service.CommonProjectRelevantService;
import com.hzw.sunflower.util.LoginUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2021/7/14 18:58
 * @description：项目相关接口
 * @modified By：`
 * @version: 1.0
 */
@Service
public class CommonProjectRelevantServiceImpl implements CommonProjectRelevantService {

    @Autowired
    private ProjectRelevantMapper projectRelevantMapper;

    @Value("${organ.organization_type}")
    private Integer organizationType;


    /**
     * 查询项目信息
     *
     * @param projectId
     */
    @Override
    public ProjectInfoDTO getProjectById(Long projectId) {
        ProjectInfoDTO dto = projectRelevantMapper.getProjectById(projectId);
        return dto;
    }

    /**
     * 查询标段信息
     *
     * @param sectionId
     */
    @Override
    public SectionInfoDTO getSectionById(Long sectionId) {
        SectionInfoDTO dto = projectRelevantMapper.getSectionById(sectionId);
        return dto;
    }

    /**
     * 根据项目id查询标段
     *
     * @param projectId
     * @return
     */
    @Override
    public List<SectionInfoDTO> getProjectIdBySection(Long projectId, Integer bidRound,Integer clarifyType,Integer userIdentity) {
        List<SectionInfoDTO> dto = projectRelevantMapper.getProjectIdBySection(projectId, bidRound,clarifyType,userIdentity);
        return dto;

    }

    /**
     * 根据项目id查询标段
     *
     * @param projectNumber
     * @return
     */
    @Override
    public List<SectionInfoDTO> getProjectIdBySection(String projectNumber, boolean isVague,Long userId) {

        QueryWrapper<Project> queryWrapper = new QueryWrapper<>();
        queryWrapper.orderByDesc("created_time");
        queryWrapper.lambda().eq(Project::getPurchaseNumber, projectNumber);
        //获取项目信息
        List<Project> projectList = projectRelevantMapper.selectprojectNumber(projectNumber);
        if (CollectionUtils.isEmpty(projectList)) {
            throw new SunFlowerException(ExceptionEnum.PROJECT_NOT_FOUND, ExceptionEnum.PROJECT_NOT_FOUND.getMessage());
        }


        //新需求  修改getNoticeByPurchaseNumber  xml文件，存在标段终止的情况，才可以重新招标
        List<SectionInfoDTO> dto = projectRelevantMapper.getNoticeByPurchaseNumber(projectNumber,userId);
//        if (dto.size() == 0) {
//            throw new ParamsNotNullException("暂无可关联标段/包");
//        }

        return dto;

    }

    /**
     * 查询委托信息
     *
     * @param projectId
     */
    @Override
    public List<ProjectEntrustInfoDTO> getProjectEntrustById(Long projectId) {
        List<ProjectEntrustInfoDTO> dto = projectRelevantMapper.getProjectEntrustById(projectId);
        return dto;
    }

//    @Override
//    public ProjectBidDocRelationInfoDto getProjectBidDocRelationById(Long projectId, Long sectionId) {
//        ProjectBidDocRelationInfoDto dto = projectRelevantMapper.getProjectBidDocRelationById(projectId, sectionId);
//        return dto;
//    }

    /**
     * 修改标段时间
     *
     * @param dtoList
     * @return
     */
    @Override
    public Boolean updateSectionTime(List<SectionTimeDTO> dtoList,Integer bidRound) {
        // 修改标段时间
        projectRelevantMapper.updateSectionTime(dtoList);
        // 修改标段时间
        projectRelevantMapper.updateSectionRecordTime(dtoList,bidRound);
        return true;
    }

    @Override
    public List<SectionInfoDTO> getProjectIdBySection(List<String> sectionIds) {
        List<SectionInfoDTO> dto = projectRelevantMapper.getSectionsInfo(sectionIds);
        return dto;
    }

    /**
     * 根据项目id查询资格预审方式 0：按包 1：按项目，和标段信息
     * @return
     */
    @Override
    public List<ProjectSectionDTO> getProjectSectionInfoById(Long projectId,String purchaseMode,String purchaseStatus,Integer bidRound) {
        return projectRelevantMapper.getProjectSectionInfoById(projectId,purchaseMode,purchaseStatus,bidRound);
    }

    @Override
    public Boolean checkSectionAbnormal(SectionInfoDTO sectionById) {
        //异常、暂停、终止状态拦截
        if(sectionById.getStatus() != null &&
                (sectionById.getStatus().equals(PackageStatusEnum.EXCEPT.getValue())
                        || sectionById.getStatus().equals(PackageStatusEnum.EXCEPT_SUSPEND.getValue())
                        || sectionById.getStatus().equals(PackageStatusEnum.EXCEPT_STOP.getValue())
                ) ){
            return false;
        }else {
            return true;
        }
    }

    @Override
    public ProjectInfoDTO getProjectUserInfo(Long projectId) {
        Integer entrustType = organizationType.equals(EnterSourceEnum.AGENCY.getType()) ? EntrustUserType.ENTRUSTED_PERSON.getType() : EntrustUserType.PRINCIPAL.getType();
        //organizationType这个参数并没有在sql中使用先传着不敢改原来代码
        return this.projectRelevantMapper.getProjectUserInfoByType(projectId,organizationType, entrustType);
//        return this.projectRelevantMapper.getProjectUserInfo(projectId);
    }

    /**
     * 校验标段是否都为资格预审第二轮
     * @param subIdList
     * @return
     */
    @Override
    public Boolean isPreQualification(List<Long> subIdList) {
        List<SectionInfoDTO> list = projectRelevantMapper.getSectionsSecondRound(subIdList);
        return list.size() == subIdList.size();
    }

    @Override
    public Boolean validReviewFileType(String sectionIds, String code) {
        List<String> sectionList = Arrays.asList(sectionIds.split(","));
        List<SectionInfoDTO> list = projectRelevantMapper.getSectionsInfo(sectionList);
        boolean flag = false;
        if (code.equals(TemplateCodeEnum.GZWXSH.getSecondCode())) {
            for (SectionInfoDTO dto : list) {
                if (dto.getReviewFileType().equals("0") || dto.getReviewFileType().equals("3")) {
                    flag = true;
                    break;
                }
            }
        } else {
            for (SectionInfoDTO dto : list) {
                if (dto.getReviewFileType().equals("1") || dto.getReviewFileType().equals("2")) {
                    flag = true;
                    break;
                }
            }
        }
        return flag;
    }

    /**
     * 根据项目id查询项目当前负责人(被委托人)所在处室code
     * @param projectId
     * @return
     */
    @Override
    public String selectDepartCodeByProjectId(Long projectId) {
        return projectRelevantMapper.selectDepartCodeByProjectId(projectId);
    }


}
