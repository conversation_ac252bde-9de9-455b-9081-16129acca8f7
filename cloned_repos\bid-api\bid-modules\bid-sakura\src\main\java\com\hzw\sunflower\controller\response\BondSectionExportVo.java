package com.hzw.sunflower.controller.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @datetime 2022/09/21 10:22
 * @description: 导出收款明细关联标包返回
 * @version: 1.0
 */
@ApiModel(description = "导出收款明细关联标包返回")
@Data
public class BondSectionExportVo {

    @ApiModelProperty("流水id")
    private Long waterId;

    @ApiModelProperty("标段id")
    private Long sectionId;

    @ApiModelProperty("包号")
    private Integer packageNumber;

    @ApiModelProperty("标包拆分金额")
    private BigDecimal splitAmount;

    @ApiModelProperty("退还状态 1：待处理  2：已退回    3：退还成功 4：退还失败  5.退还中")
    private Integer returnStatus;

}
