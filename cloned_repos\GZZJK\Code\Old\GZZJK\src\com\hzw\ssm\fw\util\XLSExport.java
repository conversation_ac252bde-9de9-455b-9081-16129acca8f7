package com.hzw.ssm.fw.util;

import java.io.OutputStream;
import java.io.UnsupportedEncodingException;
import java.util.Calendar;

import javax.servlet.http.HttpServletResponse;

import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFDataFormat;
import org.apache.poi.hssf.usermodel.HSSFFont;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.CellStyle;


public   class  XLSExport  {

    //  设置cell编码解决中文高位字节截断
//    private   static   short  XLS_ENCODING  =  HSSFWorkbook.ENCODING_UTF_16;

    //  定制日期格式
    private   static  String DATE_FORMAT  =   " m/d/yy " ;  //  "m/d/yy h:mm"

    //  定制浮点数格式
    private   static  String NUMBER_FORMAT  =   " #,##0.00 " ;

    private  HSSFWorkbook workbook;

    private  HSSFSheet sheet;

    private  HSSFRow row;
    
    
    private  HttpServletResponse response;
    /**
    * 初始化Excel
    * 
    *  @param  fileName
    *            导出文件名
     */
    public  XLSExport(HttpServletResponse response,String fileName)  {
        this .workbook  =   new  HSSFWorkbook();
        this .sheet  =  workbook.createSheet();
        HSSFCellStyle cellStyle = workbook.createCellStyle(); 
        cellStyle.setAlignment(HSSFCellStyle.ALIGN_CENTER);
        try {
        	
        	response.reset();
            response.setContentType("application/vnd.ms-excel");
			response.setHeader("Content-Disposition", "attachment;filename="+new String(fileName.getBytes("gb2312"), "iso8859-1"));
			this.response=response;
		} catch (UnsupportedEncodingException e) {
			e.printStackTrace();
		}
   }


    public HSSFWorkbook getWorkbook() {
		return workbook;
	}

	public void setWorkbook(HSSFWorkbook workbook) {
		this.workbook = workbook;
	}

	/**
    * 增加一行
    * 
    *  @param  index
    *            行号
     */
    public   void  createRow( int  index)  {
        this .row  =   this .sheet.createRow(index);
   }

    /**
    * 设置单元格
    * 
    *  @param  index
    *            列号
    *  @param  value
    *            单元格填充值
     */
    //无字体样式设置单元格
    public   void  setCelll( int  index, String value)  {
        HSSFCell cell  =   this .row.createCell(index);
        //this.row.setHeightInPoints(height);//设置行高
        
//        cell.setCellType(HSSFCell.CELL_TYPE_STRING);
        HSSFCellStyle style = workbook.createCellStyle();
        style.setWrapText(true);//设置自动换行
        style.setAlignment(HSSFCellStyle.ALIGN_CENTER);
        style.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);
        style.setBorderRight(HSSFCellStyle.BORDER_THIN);
        style.setBorderLeft(HSSFCellStyle.BORDER_THIN);
        style.setBorderTop(HSSFCellStyle.BORDER_THIN);
        style.setBorderBottom(HSSFCellStyle.BORDER_THIN);
        cell.setCellType(HSSFCell.ENCODING_UTF_16);
        cell.setCellStyle(style);
        cell.setCellValue(value);
    }
    //==================================根据传参区别报表===================================
    public   void  setCel_( int  index,int size,float height, String value)  {//列，字体大小，行高，列宽，传值
        HSSFFont font = workbook.createFont();
        font.setFontName("黑体"); //字体//设置字体类型
        font.setFontHeightInPoints((short) size); //字体高度//设置字体大小
        HSSFCell cell  =   this .row.createCell(index);
        this.row.setHeightInPoints(height);//设置行高
//        cell.setCellType(HSSFCell.CELL_TYPE_STRING);
        HSSFCellStyle style = workbook.createCellStyle();
        style.setWrapText(true);//设置自动换行
        style.setAlignment(HSSFCellStyle.ALIGN_CENTER);
        style.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);
        style.setBorderRight(HSSFCellStyle.BORDER_THIN);
        style.setBorderLeft(HSSFCellStyle.BORDER_THIN);
        style.setBorderTop(HSSFCellStyle.BORDER_THIN);
        style.setBorderBottom(HSSFCellStyle.BORDER_THIN);
        style.setFont(font);
        cell.setCellType(HSSFCell.ENCODING_UTF_16);
        cell.setCellStyle(style);
        cell.setCellValue(value);
    }   
  //==================================根据传参区别报表===================================
    public   void  setCel( int  index,int size,float height, String value)  {//列，字体大小，行高，列宽，传值
    	setCel(index, size, height, value, HSSFCellStyle.ALIGN_CENTER, true);
    }
    
    /**
     * //列，字体大小，行高，列宽，传值
     * @param index  列
     * @param size   字体大小
     * @param height 行高
     * @param value  传值
     * @param align  对齐方式
     * @param border 是否有边框
     */
    public   void  setCel( int  index,int size,float height, String value, short align, boolean border)  {
        HSSFFont font = workbook.createFont();
        font.setFontName("宋体"); //字体//设置字体类型
        font.setFontHeightInPoints((short) size); //字体高度//设置字体大小
        HSSFCell cell  =   this .row.createCell(index);
        this.row.setHeightInPoints(height);//设置行高
//        cell.setCellType(HSSFCell.CELL_TYPE_STRING);
        HSSFCellStyle style = workbook.createCellStyle();
        style.setWrapText(true);//设置自动换行
        style.setAlignment(align);
        style.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);
        if (border)
        {
	        style.setBorderRight(HSSFCellStyle.BORDER_THIN);
	        style.setBorderLeft(HSSFCellStyle.BORDER_THIN);
	        style.setBorderTop(HSSFCellStyle.BORDER_THIN);
	        style.setBorderBottom(HSSFCellStyle.BORDER_THIN);
        }
        style.setFont(font);
        cell.setCellType(HSSFCell.ENCODING_UTF_16);
        cell.setCellStyle(style);
        cell.setCellValue(value);
    }
    
    public void setCel( int  index,int size,float height, String value, boolean border)  {//列，字体大小，行高，列宽，传值
    	setCel(index, size, height, value, HSSFCellStyle.ALIGN_CENTER, border);
    }
    
     //第一行标题栏
     public   void  setOne( int  index,int size,float height, String value)  {//列，字体大小，行高，列宽，传值
     	HSSFFont font = workbook.createFont();
         HSSFCell cell  =   this .row.createCell(index);
         this.row.setHeightInPoints(height);//设置行高
//         cell.setCellType(HSSFCell.CELL_TYPE_STRING);
         HSSFCellStyle style = workbook.createCellStyle();
         //sheet.setColumnWidth(index, 300);//设置列宽
         style.setAlignment(HSSFCellStyle.ALIGN_CENTER);
         style.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);
         font.setFontName("宋体"); //字体//设置字体类型
         font.setFontHeightInPoints((short) size); //字体高度//设置字体大小
         font.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD);//设置字体加粗
         style.setFont(font);
         cell.setCellType(HSSFCell.ENCODING_UTF_16);
         cell.setCellStyle(style);
         cell.setCellValue(value);
     }
     //第一行标题栏
     public   void  setOne_( int  index,int size,float height, String value)  {//列，字体大小，行高，列宽，传值
     	HSSFFont font = workbook.createFont();
         HSSFCell cell  =   this .row.createCell(index);
         this.row.setHeightInPoints(height);//设置行高
//         cell.setCellType(HSSFCell.CELL_TYPE_STRING);
         HSSFCellStyle style = workbook.createCellStyle();
         //sheet.setColumnWidth(index, 300);//设置列宽
         style.setAlignment(HSSFCellStyle.ALIGN_CENTER);
         style.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);
         font.setFontName("黑体"); //字体//设置字体类型
         font.setFontHeightInPoints((short) size); //字体高度//设置字体大小
         font.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD);//设置字体加粗
         style.setFont(font);
         cell.setCellType(HSSFCell.ENCODING_UTF_16);
         cell.setCellStyle(style);
         cell.setCellValue(value);
     }
   //第二行标题栏
     public   void  setTwo_( int  index,int size,float height,int width, String value)  {//列，字体大小，行高，列宽，传值
     	 HSSFFont font = workbook.createFont();
         HSSFCell cell  =   this .row.createCell(index);
         this.row.setHeightInPoints(height);//设置行高
//         cell.setCellType(HSSFCell.CELL_TYPE_STRING);
         HSSFCellStyle style = workbook.createCellStyle();
         sheet.setColumnWidth(index, width);//设置行宽
         style.setWrapText(true);//设置自动换行
         style.setAlignment(HSSFCellStyle.ALIGN_CENTER);
         style.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);
         style.setBorderRight(HSSFCellStyle.BORDER_THIN);
         style.setBorderLeft(HSSFCellStyle.BORDER_THIN);
         style.setBorderTop(HSSFCellStyle.BORDER_THIN);
         style.setBorderBottom(HSSFCellStyle.BORDER_THIN);
         font.setFontName("黑体"); //字体//设置字体类型
         font.setFontHeightInPoints((short) size); //字体高度//设置字体大小
         font.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD);//设置字体加粗
         style.setFont(font);
         cell.setCellType(HSSFCell.ENCODING_UTF_16);
         cell.setCellStyle(style);
         cell.setCellValue(value);
     }
   //第二行标题栏
     public   void  setTwo( int  index,int size,float height,int width, String value)  {//列，字体大小，行高，列宽，传值
     	 HSSFFont font = workbook.createFont();
         HSSFCell cell  =   this .row.createCell(index);
         this.row.setHeightInPoints(height);//设置行高
//         cell.setCellType(HSSFCell.CELL_TYPE_STRING);
         HSSFCellStyle style = workbook.createCellStyle();
         sheet.setColumnWidth(index, width);//设置行宽
         style.setWrapText(true);//设置自动换行
         style.setAlignment(HSSFCellStyle.ALIGN_CENTER);
         style.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);
         style.setBorderRight(HSSFCellStyle.BORDER_THIN);
         style.setBorderLeft(HSSFCellStyle.BORDER_THIN);
         style.setBorderTop(HSSFCellStyle.BORDER_THIN);
         style.setBorderBottom(HSSFCellStyle.BORDER_THIN);
         font.setFontName("宋体"); //字体//设置字体类型
         font.setFontHeightInPoints((short) size); //字体高度//设置字体大小
         font.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD);//设置字体加粗
         style.setFont(font);
         cell.setCellType(HSSFCell.ENCODING_UTF_16);
         cell.setCellStyle(style);
         cell.setCellValue(value);
     }
     public   void  setTwo( int  index,int size,float height,int width, String value, short align)  {//列，字体大小，行高，列宽，左右对齐 传值
     	 HSSFFont font = workbook.createFont();
         HSSFCell cell  =   this .row.createCell(index);
         this.row.setHeightInPoints(height);//设置行高
         HSSFCellStyle style = workbook.createCellStyle();
         sheet.setColumnWidth(index, width);//设置行宽
         style.setWrapText(true);//设置自动换行
         style.setAlignment(align);
         style.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);
         style.setBorderRight(HSSFCellStyle.BORDER_THIN);
         style.setBorderLeft(HSSFCellStyle.BORDER_THIN);
         style.setBorderTop(HSSFCellStyle.BORDER_THIN);
         style.setBorderBottom(HSSFCellStyle.BORDER_THIN);
         font.setFontName("宋体"); //字体//设置字体类型
         font.setFontHeightInPoints((short) size); //字体高度//设置字体大小
         font.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD);//设置字体加粗
         style.setFont(font);
         cell.setCellType(HSSFCell.ENCODING_UTF_16);
         cell.setCellStyle(style);
         cell.setCellValue(value);
     }
   //第三行标题栏
     public   void  setThree(int  index,int size,float height,int width, String value)  {//列，字体大小，行高，列宽，传值
     	HSSFFont font = workbook.createFont();
         HSSFCell cell  =   this .row.createCell(index);
         this.row.setHeightInPoints(height);//设置行高
//         cell.setCellType(HSSFCell.CELL_TYPE_STRING);
         HSSFCellStyle style = workbook.createCellStyle();
         sheet.setColumnWidth(index, width);//设置列宽
         style.setWrapText(true);//设置自动换行
         style.setAlignment(HSSFCellStyle.ALIGN_CENTER);
         style.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);
         style.setBorderRight(HSSFCellStyle.BORDER_THIN);
         style.setBorderLeft(HSSFCellStyle.BORDER_THIN);
         style.setBorderTop(HSSFCellStyle.BORDER_THIN);
         style.setBorderBottom(HSSFCellStyle.BORDER_THIN);
         font.setFontName("宋体"); //字体//设置字体类型
         font.setFontHeightInPoints((short) size); //字体高度//设置字体大小
         font.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD);//设置字体加粗
         style.setFont(font);
         cell.setCellType(HSSFCell.ENCODING_UTF_16);
         cell.setCellStyle(style);
         cell.setCellValue(value);
     }
    
    //==========================================列少===========================================
    public   void  setCell( int  index, String value)  
    {
       setCell(index, value, HSSFCellStyle.ALIGN_CENTER);
    }
    
    public void setCell(int  index, String value, short align)
    {
    	HSSFFont font = workbook.createFont();
        font.setFontName("宋体"); //字体//设置字体类型
        font.setFontHeightInPoints((short) 14); //字体高度//设置字体大小
        HSSFCell cell  =   this .row.createCell(index);
        this.row.setHeightInPoints(18.75f);//设置行高
//        cell.setCellType(HSSFCell.CELL_TYPE_STRING);
        HSSFCellStyle style = workbook.createCellStyle();
        style.setAlignment(align);
        style.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);
        style.setBorderRight(HSSFCellStyle.BORDER_THIN);
        style.setBorderLeft(HSSFCellStyle.BORDER_THIN);
        style.setBorderTop(HSSFCellStyle.BORDER_THIN);
        style.setBorderBottom(HSSFCellStyle.BORDER_THIN);
        style.setFont(font);
        cell.setCellType(HSSFCell.ENCODING_UTF_16);
        cell.setCellStyle(style);
        cell.setCellValue(value);
    	
    }
    //第一行标题栏
    public   void  setFirstRow( int  index,String value)  {
    	HSSFFont font = workbook.createFont();
        HSSFCell cell  =   this .row.createCell(index);
        this.row.setHeightInPoints(50);//设置行高
//        cell.setCellType(HSSFCell.CELL_TYPE_STRING);
        HSSFCellStyle style = workbook.createCellStyle();
        //sheet.setColumnWidth(index, 300);//设置列宽
        style.setAlignment(HSSFCellStyle.ALIGN_CENTER);
        style.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);
        font.setFontName("宋体"); //字体//设置字体类型
        font.setFontHeightInPoints((short) 18); //字体高度//设置字体大小
        font.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD);//设置字体加粗
        style.setFont(font);
        cell.setCellType(HSSFCell.ENCODING_UTF_16);
        cell.setCellStyle(style);
        cell.setCellValue(value);
    }
  //第二行标题栏
    public   void  setSecondRow( int  index,String value)  {
    	HSSFFont font = workbook.createFont();
        HSSFCell cell  =   this .row.createCell(index);
        if(index==0){sheet.setColumnWidth(index, 3800);}else{//设置列宽
        	sheet.setColumnWidth(index, 6500);
        }
        this.row.setHeightInPoints(18.75f);//设置行高
//        cell.setCellType(HSSFCell.CELL_TYPE_STRING);
        HSSFCellStyle style = workbook.createCellStyle();
        //sheet.setColumnWidth(index, 300);//设置列宽
        style.setAlignment(HSSFCellStyle.ALIGN_CENTER);
        style.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);
        style.setBorderRight(HSSFCellStyle.BORDER_THIN);
        style.setBorderLeft(HSSFCellStyle.BORDER_THIN);
        style.setBorderTop(HSSFCellStyle.BORDER_THIN);
        style.setBorderBottom(HSSFCellStyle.BORDER_THIN);
        font.setFontName("宋体"); //字体//设置字体类型
        font.setFontHeightInPoints((short) 14); //字体高度//设置字体大小
        font.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD);//设置字体加粗
        style.setFont(font);
        cell.setCellType(HSSFCell.ENCODING_UTF_16);
        cell.setCellStyle(style);
        cell.setCellValue(value);
    }
    
  //==========================================列多===========================================
    public   void  setCells( int  index, String value)  {
        HSSFFont font = workbook.createFont();
        font.setFontName("宋体"); //字体//设置字体类型
        font.setFontHeightInPoints((short) 10); //字体高度//设置字体大小
        HSSFCell cell  =   this .row.createCell(index);
        this.row.setHeightInPoints(12);//设置行高
//        cell.setCellType(HSSFCell.CELL_TYPE_STRING);
        HSSFCellStyle style = workbook.createCellStyle();
        style.setAlignment(HSSFCellStyle.ALIGN_CENTER);
        style.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);
        style.setBorderRight(HSSFCellStyle.BORDER_THIN);
        style.setBorderLeft(HSSFCellStyle.BORDER_THIN);
        style.setBorderTop(HSSFCellStyle.BORDER_THIN);
        style.setBorderBottom(HSSFCellStyle.BORDER_THIN);
        style.setFont(font);
        cell.setCellType(HSSFCell.ENCODING_UTF_16);
        cell.setCellStyle(style);
        cell.setCellValue(value);
    }
     //第一行标题栏
     public   void  setFirstRows( int  index,String value)  {
     	HSSFFont font = workbook.createFont();
         HSSFCell cell  =   this .row.createCell(index);
         this.row.setHeightInPoints(33);//设置行高
//         cell.setCellType(HSSFCell.CELL_TYPE_STRING);
         HSSFCellStyle style = workbook.createCellStyle();
         //sheet.setColumnWidth(index, 300);//设置列宽
         style.setAlignment(HSSFCellStyle.ALIGN_CENTER);
         style.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);
         font.setFontName("宋体"); //字体//设置字体类型
         font.setFontHeightInPoints((short) 16); //字体高度//设置字体大小
         font.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD);//设置字体加粗
         style.setFont(font);
         cell.setCellType(HSSFCell.ENCODING_UTF_16);
         cell.setCellStyle(style);
         cell.setCellValue(value);
     }
   //第二行标题栏
     public   void  setSecondRows( int  index,String value)  {
     	HSSFFont font = workbook.createFont();
         HSSFCell cell  =   this .row.createCell(index);
         if(index==0){sheet.setColumnWidth(index, 3000);}else{
         sheet.setColumnWidth(index, 4000);}
         this.row.setHeightInPoints(12);//设置行高
//         cell.setCellType(HSSFCell.CELL_TYPE_STRING);
         HSSFCellStyle style = workbook.createCellStyle();
         //sheet.setColumnWidth(index, 300);//设置列宽
         style.setAlignment(HSSFCellStyle.ALIGN_CENTER);
         style.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);
         style.setBorderRight(HSSFCellStyle.BORDER_THIN);
         style.setBorderLeft(HSSFCellStyle.BORDER_THIN);
         style.setBorderTop(HSSFCellStyle.BORDER_THIN);
         style.setBorderBottom(HSSFCellStyle.BORDER_THIN);
         font.setFontName("宋体"); //字体//设置字体类型
         font.setFontHeightInPoints((short) 12); //字体高度//设置字体大小
         font.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD);//设置字体加粗
         style.setFont(font);
         cell.setCellType(HSSFCell.ENCODING_UTF_16);
         cell.setCellStyle(style);
         cell.setCellValue(value);
     }
     
   //==================================经济指标===================================
     public   void  setParam( int  index, String value)  {
         HSSFFont font = workbook.createFont();
         font.setFontName("宋体"); //字体//设置字体类型
         font.setFontHeightInPoints((short) 10); //字体高度//设置字体大小
         HSSFCell cell  =   this .row.createCell(index);
         this.row.setHeightInPoints(12);//设置行高
//         cell.setCellType(HSSFCell.CELL_TYPE_STRING);
         HSSFCellStyle style = workbook.createCellStyle();
         style.setAlignment(HSSFCellStyle.ALIGN_CENTER);
         style.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);
         style.setBorderRight(HSSFCellStyle.BORDER_THIN);
         style.setBorderLeft(HSSFCellStyle.BORDER_THIN);
         style.setBorderTop(HSSFCellStyle.BORDER_THIN);
         style.setBorderBottom(HSSFCellStyle.BORDER_THIN);
         style.setFont(font);
         cell.setCellType(HSSFCell.ENCODING_UTF_16);
         cell.setCellStyle(style);
         cell.setCellValue(value);
     }
      //第一行标题栏
      public   void  setOneRow( int  index,String value)  {
      	HSSFFont font = workbook.createFont();
          HSSFCell cell  =   this .row.createCell(index);
          this.row.setHeightInPoints(50);//设置行高
//          cell.setCellType(HSSFCell.CELL_TYPE_STRING);
          HSSFCellStyle style = workbook.createCellStyle();
          //sheet.setColumnWidth(index, 300);//设置列宽
          style.setAlignment(HSSFCellStyle.ALIGN_CENTER);
          style.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);
          font.setFontName("宋体"); //字体//设置字体类型
          font.setFontHeightInPoints((short) 16); //字体高度//设置字体大小
          font.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD);//设置字体加粗
          style.setFont(font);
          cell.setCellType(HSSFCell.ENCODING_UTF_16);
          cell.setCellStyle(style);
          cell.setCellValue(value);
      }
    //第二行标题栏
      public   void  setTwoRow( int  index,String value)  {
      	HSSFFont font = workbook.createFont();
          HSSFCell cell  =   this .row.createCell(index);
          this.row.setHeightInPoints(15);//设置行高
//          cell.setCellType(HSSFCell.CELL_TYPE_STRING);
          HSSFCellStyle style = workbook.createCellStyle();
          //sheet.setColumnWidth(index, 300);//设置列宽
          style.setAlignment(HSSFCellStyle.ALIGN_CENTER);
          style.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);
          style.setBorderRight(HSSFCellStyle.BORDER_THIN);
          style.setBorderLeft(HSSFCellStyle.BORDER_THIN);
          style.setBorderTop(HSSFCellStyle.BORDER_THIN);
          style.setBorderBottom(HSSFCellStyle.BORDER_THIN);
          font.setFontName("宋体"); //字体//设置字体类型
          font.setFontHeightInPoints((short) 12); //字体高度//设置字体大小
          font.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD);//设置字体加粗
          style.setFont(font);
          cell.setCellType(HSSFCell.ENCODING_UTF_16);
          cell.setCellStyle(style);
          cell.setCellValue(value);
      }
    //第三行标题栏
      public   void  setThreeRow(int  index,String value)  {
      	HSSFFont font = workbook.createFont();
          HSSFCell cell  =   this .row.createCell(index);
          if(index==0||index==1||index==2||index==3){sheet.setColumnWidth(index, 2200);}else{
          sheet.setColumnWidth(index, 3500);}
          this.row.setHeightInPoints(15);//设置行高
//          cell.setCellType(HSSFCell.CELL_TYPE_STRING);
          HSSFCellStyle style = workbook.createCellStyle();
          //sheet.setColumnWidth(index, 300);//设置列宽
          style.setAlignment(HSSFCellStyle.ALIGN_CENTER);
          style.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);
          style.setBorderRight(HSSFCellStyle.BORDER_THIN);
          style.setBorderLeft(HSSFCellStyle.BORDER_THIN);
          style.setBorderTop(HSSFCellStyle.BORDER_THIN);
          style.setBorderBottom(HSSFCellStyle.BORDER_THIN);
          font.setFontName("宋体"); //字体//设置字体类型
          font.setFontHeightInPoints((short) 11); //字体高度//设置字体大小
          font.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD);//设置字体加粗
          style.setFont(font);
          cell.setCellType(HSSFCell.ENCODING_UTF_16);
          cell.setCellStyle(style);
          cell.setCellValue(value);
      }
      
    
     
    /**
     * 设置单元格
     * 
     *  @param  index
     *            列号
     *  @param  value
     *            单元格填充值
      */
    public   void  setCell( int  index, String value,boolean left,boolean right,boolean top,boolean bottom)  {
        HSSFCell cell  =   this .row.createCell(index);
        
        HSSFCellStyle style = workbook.createCellStyle();
        style.setAlignment(CellStyle.ALIGN_CENTER);//水平居中  
        if(right)
        style.setBorderRight(HSSFCellStyle.BORDER_THIN);
        if(left)
        style.setBorderLeft(HSSFCellStyle.BORDER_THIN);
        if(top)
        style.setBorderTop(HSSFCellStyle.BORDER_THIN);
        if(bottom)
        style.setBorderBottom(HSSFCellStyle.BORDER_THIN);
        cell.setCellType(HSSFCell.ENCODING_UTF_16);
        cell.setCellValue(value);       
        cell.setCellStyle(style);
       
    }

    /**
    * 设置单元格
    * 
    *  @param  index
    *            列号
    *  @param  value
    *            单元格填充值
     */
    public   void  setCell( int  index, Calendar value)  {
       HSSFCell cell  =   this .row.createCell(index);
       //cell.setEncoding(XLS_ENCODING);
       cell.setCellValue(value.getTime());
       HSSFCellStyle cellStyle  =  workbook.createCellStyle();  //  建立新的cell样式
       cellStyle.setDataFormat(HSSFDataFormat.getBuiltinFormat(DATE_FORMAT));  //  设置cell样式为定制的日期格式
       cell.setCellStyle(cellStyle);  //  设置该cell日期的显示格式
    
   }

    public HSSFSheet getSheet() {
		return sheet;
	}


	public void setSheet(HSSFSheet sheet) {
		this.sheet = sheet;
	}


	/**
    * 设置单元格
    * 
    *  @param  index
    *            列号
    *  @param  value
    *            单元格填充值
     */
    public   void  setCell( int  index,  int  value)  {
       HSSFCell cell  =   this .row.createCell(index);
       cell.setCellType(HSSFCell.CELL_TYPE_NUMERIC);
       cell.setCellValue(value);
     
   }

    /**
    * 设置单元格
    * 
    *  @param  index
    *            列号
    *  @param  value
    *            单元格填充值
     */
    public   void  setCell( int  index,  double  value,boolean left,boolean right,boolean top,boolean bottom)  {
       HSSFCell cell  =   this .row.createCell(index);
       cell.setCellType(HSSFCell.CELL_TYPE_NUMERIC);
       cell.setCellValue(value);
       HSSFCellStyle cellStyle  =  workbook.createCellStyle();  //  建立新的cell样式
       HSSFDataFormat format  =  workbook.createDataFormat();
       cellStyle.setDataFormat(format.getFormat(NUMBER_FORMAT));  //  设置cell样式为定制的浮点数格式
       cell.setCellStyle(cellStyle);  //  设置该cell浮点数的显示格式
       //加边框线
       HSSFCellStyle style = workbook.createCellStyle();
       if(right)
    	   style.setBorderRight(HSSFCellStyle.BORDER_THIN);
       if(left)
    	   style.setBorderLeft(HSSFCellStyle.BORDER_THIN);
       if(top)
    	   style.setBorderTop(HSSFCellStyle.BORDER_THIN);
       if(bottom)
    	   style.setBorderBottom(HSSFCellStyle.BORDER_THIN);
       cell.setCellType(HSSFCell.ENCODING_UTF_16);
       cell.setCellStyle(style);
   }

   
    /**
     * 导出Excel文件
     * 
     *  @throws  XLSException
      */
     public   void  exportXLS() {
    	 
    	 try   {
             OutputStream os = response.getOutputStream();
             this.workbook.write(os);
             os.close();
              System.out.println( " 导出Excel文件[成功] " );
          }   catch  (Exception e1)  {
              System.out.println( " 导出Excel文件[失败] " );
              e1.printStackTrace();
          } 
 //   	 org.apache.commons.logging.LogFactory
    }
     
}
