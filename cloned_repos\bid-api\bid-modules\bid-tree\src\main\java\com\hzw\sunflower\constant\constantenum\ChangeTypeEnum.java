package com.hzw.sunflower.constant.constantenum;

/**
 * <AUTHOR>
 * @date ：Created in 2021/7/14 10:51
 * @description：变更类型
 * @version: 1.0
 */
public enum ChangeTypeEnum {

    SUBMIT(0, "首次提交"),

    WITHDRAW(1, "撤回变更"),

    RETURN(2, "退回变更");

    ChangeTypeEnum(Integer value, String name){
        this.value = value;
        this.name = name;
    }

    private Integer value;

    private String name;

    public Integer getValue() {
        return value;
    }

    public String getName() {
        return name;
    }
}
