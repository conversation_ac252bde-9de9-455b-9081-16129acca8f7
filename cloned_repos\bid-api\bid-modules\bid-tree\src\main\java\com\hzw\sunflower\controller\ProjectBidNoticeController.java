package com.hzw.sunflower.controller;

import com.hzw.sunflower.common.BaseController;
import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.common.annotation.RepeatSubmit;
import com.hzw.sunflower.constant.MessageConstants;
import com.hzw.sunflower.constant.NoticeProgressConstants;
import com.hzw.sunflower.controller.request.CommonNoticeREQ;
import com.hzw.sunflower.controller.request.NoticeSectionREQ;
import com.hzw.sunflower.controller.request.QueryPublicNoticeListREQ;
import com.hzw.sunflower.controller.request.UpdateNoticeREQ;
import com.hzw.sunflower.controller.response.NoticeTimeVO;
import com.hzw.sunflower.controller.response.PackageInfoVO;
import com.hzw.sunflower.controller.response.ProjectBidNoticeListVO;
import com.hzw.sunflower.controller.response.ProjectBidNoticeVO;
import com.hzw.sunflower.entity.Project;
import com.hzw.sunflower.entity.ProjectBidNotice;
import com.hzw.sunflower.exception.ExceptionEnum;
import com.hzw.sunflower.exception.SunFlowerException;
import com.hzw.sunflower.service.NoticePackageRService;
import com.hzw.sunflower.service.ProjectBidNoticeService;
import com.hzw.sunflower.service.ProjectService;
import com.hzw.sunflower.util.RequestUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
* 招标公告服务
*/
@Api(tags = "招标公告服务")
@RestController
@RequestMapping("/bidNotice")
public class ProjectBidNoticeController extends BaseController {

    @Autowired
    private ProjectBidNoticeService tProjectBidNoticeService;

    @Autowired
    private NoticePackageRService packageRService;

    @Autowired
    private ProjectService projectService;

    @Autowired
    private NoticePackageRService noticePackageRService;



    @ApiOperation(value = "根据条件查询招标公告列表")
    @ApiImplicitParam(name = "req", value = "列表查询条件", required = true, dataType = "QueryPublicNoticeListREQ", paramType = "body")
    @PostMapping("/noticeList")
    public Result<ProjectBidNoticeListVO> findBidNoticeInfo(@RequestBody QueryPublicNoticeListREQ req) {

        if(RequestUtil.isEmpty(req.getProjectId())){
            throw new SunFlowerException(ExceptionEnum.ILLEGAL_PARAMETER_EXCEPTION,null);
        }

        return Result.ok(tProjectBidNoticeService.findNoticeListPage(req));
    }

    @ApiOperation(value = "获取项目包段简要信息")
    @ApiImplicitParam(name = "req", value = "通用查询条件，项目ID必填", required = true, dataType = "CommonNoticeREQ", paramType = "body")
    @PostMapping("/packageInfo")
    public Result<PackageInfoVO> findPackageInfo(@RequestBody CommonNoticeREQ req) {
        if(RequestUtil.isEmpty(req.getProjectId())){
            throw new SunFlowerException(ExceptionEnum.ILLEGAL_PARAMETER_EXCEPTION,null);
        }

        Project project = projectService.getProjectById(req.getProjectId());
        if(project == null){
            throw new SunFlowerException(ExceptionEnum.ILLEGAL_PARAMETER_EXCEPTION,null);
        }

        PackageInfoVO vo = new PackageInfoVO()
                .setPackages(noticePackageRService.findPackageInfoByProjectIdAndRound(req.getProjectId(),req.getBidRound()))
                .setProjectPackageState(project.getPackageSegmentStatus());

        return Result.ok(vo);
    }


    @ApiOperation(value = "获取查看页面招标公告信息")
    @ApiImplicitParam(name = "req", value = "通用公告信息,公告ID必填", required = true, dataType = "CommonNoticeREQ", paramType = "body")
    @PostMapping(value = "/queryNoticeInfo")
    public Result<ProjectBidNoticeVO> queryNoticeInfo(@RequestBody CommonNoticeREQ req) {
        if(RequestUtil.isEmpty(req.getNoticeId())){
            throw new SunFlowerException(ExceptionEnum.NOTICE_NOT_FOUND,null);
        }

        return tProjectBidNoticeService.queryNoticeInfo(req);
    }
    @ApiOperation(value = "获取编辑页面招标公告信息")
    @ApiImplicitParam(name = "req", value = "通用公告信息,项目ID与公告ID均必填", required = true, dataType = "CommonNoticeREQ", paramType = "body")
    @PostMapping(value = "/noticeInfo")
    public Result<ProjectBidNoticeVO> getNoticeInfo(@RequestBody CommonNoticeREQ req) {
        if(RequestUtil.isEmpty(req.getNoticeId())){
            throw new SunFlowerException(ExceptionEnum.NOTICE_NOT_FOUND,null);
        }

        return tProjectBidNoticeService.queryEditNoticeInfo(req);
    }

    @ApiOperation(value = "删除招标公告")
    @ApiImplicitParam(name = "req", value = "通用公告信息，项目ID与公告ID均必填", required = true,dataType = "CommonNoticeREQ",paramType = "body")
    @DeleteMapping(value = "/deleteNotice")
    @RepeatSubmit
    public Result<Boolean> delete(@RequestBody CommonNoticeREQ req) {

        if(RequestUtil.isEmpty(req.getNoticeId())){
            throw new SunFlowerException(ExceptionEnum.NOTICE_NOT_FOUND,null);
        }
        // 判断招标公告是否可删除
        ProjectBidNotice notice = tProjectBidNoticeService.noticeIsAvailable(req.getNoticeId(), NoticeProgressConstants.CAN_DELETE);
        // 获取招标公告关联的项目包段ID集合
        List<Long> packageIds = noticePackageRService.findNoticePackageIdsByNoticeId(req.getNoticeId());
        // 删除招标公告及其关联信息
        tProjectBidNoticeService.deleteById(notice,packageIds);

        return Result.ok(true);
    }

    @ApiOperation(value = "新建/修改招标公告信息")
    @ApiImplicitParam(name = "req", value = "变更公告请求实体", required = true,dataType = "UpdateNoticeREQ",paramType = "body")
    @PutMapping(value = "/updateNotice")
    @RepeatSubmit
    public Result<Boolean> update(@RequestBody UpdateNoticeREQ req){
        if(RequestUtil.isEmpty(req.getProjectId())){
            throw new SunFlowerException(ExceptionEnum.ILLEGAL_PARAMETER_EXCEPTION,null);
        }
        Result<Boolean> result = tProjectBidNoticeService.saveOrUpdate(req,getJwtUser());
        return result;
    }

    @ApiOperation(value = "招标公告发布")
    @ApiImplicitParam(name = "req", value = "招标公告通用请求实体", required = true,dataType = "CommonNoticeREQ",paramType = "body")
    @PostMapping(value = "/publishNotice")
    @RepeatSubmit
    public Result<Boolean> publish(@RequestBody CommonNoticeREQ req){

        if(RequestUtil.isEmpty(req.getNoticeId())){
            throw new SunFlowerException(ExceptionEnum.NOTICE_NOT_FOUND,ExceptionEnum.NOTICE_NOT_FOUND.getMessage());
        }
        // 判断招标公告是否可发布
        ProjectBidNotice notice = tProjectBidNoticeService.noticeIsAvailable(req.getNoticeId(), NoticeProgressConstants.CAN_PUBLISH);
//        requestToDTOService.endTimeIsAcailable(notice);
        List<Long> packageIds = packageRService.findNoticePackageIdsByNoticeId(notice.getId());
        // 更新招标公告及所关联包段的状态
        tProjectBidNoticeService.updatePublishNotice(notice,packageIds);
        return Result.ok(true);
    }



    @ApiOperation(value = "判断是否展示招标公告新增按钮")
    @ApiImplicitParam(name = "projectId", value = "项目id", required = true)
    @GetMapping(value = "/isInsertButton/{projectId}")
    public Result<Boolean> getTimesByDocId(@PathVariable Long projectId) {
        if (projectId == null) {
            return Result.failed(MessageConstants.GET_NULL);
        }
        return Result.ok(true).setData(tProjectBidNoticeService.isInsertButton(projectId));
    }

    @ApiOperation(value = "根据标段id查询公告信息")
    @ApiImplicitParam(name = "req", value = "招标公告通用请求实体", required = true,dataType = "NoticeSectionREQ",paramType = "body")
    @PostMapping(value = "/getNoticeTime")
    public Result<List<NoticeTimeVO>> getNoticeTime(@RequestBody NoticeSectionREQ req) {
        List<NoticeTimeVO> noticeTime = tProjectBidNoticeService.getNoticeTime(req);
        return Result.ok(noticeTime);
    }

}