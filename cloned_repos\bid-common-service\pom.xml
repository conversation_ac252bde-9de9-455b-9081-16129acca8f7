<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <groupId>com.hzw</groupId>
    <version>${version}</version>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>bid-common-service</artifactId>
    <packaging>jar</packaging>
    <name>bid-common-service</name>
    <properties>
        <java.version>11</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <!--        <maven.compiler.source>1.8</maven.compiler.source>-->
        <!--        <maven.compiler.target>1.8</maven.compiler.target>-->
        <!--        <maven.compiler.compilerVersion>1.8</maven.compiler.compilerVersion>-->
        <spring-boot.version>2.6.3</spring-boot.version>
        <fastjson.version>1.2.75</fastjson.version>
        <swagger-annotations.version>1.5.22</swagger-annotations.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <swagger-annotations.version>1.6.11</swagger-annotations.version>
        <sa-token.version>1.35.0.RC</sa-token.version>
        <bid-common-core.version>1.0.47.3</bid-common-core.version>
        <bid-common-bean.version>1.0.34.2</bid-common-bean.version>
        <bid-freesia.version>1.0.26</bid-freesia.version>
        <bid-common-security.version>1.0.1</bid-common-security.version>
        <jetcache.latest.version>2.7.4</jetcache.latest.version>
    </properties>
    <dependencies>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
            <version>5.3.8</version>
        </dependency>
        <dependency>
            <groupId>com.aliyun.oss</groupId>
            <artifactId>aliyun-sdk-oss</artifactId>
            <version>3.10.2</version>
        </dependency>
        <!-- 天翼云 -->
        <dependency>
            <groupId>cn.ctyun</groupId>
            <artifactId>ctyun-sdk-oss</artifactId>
            <version>6.5.0</version>
        </dependency>
        <dependency>
            <groupId>joda-time</groupId>
            <artifactId>joda-time</artifactId>
            <version>2.10</version>
        </dependency>

        <!-- itext5 start -->
        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>itextpdf</artifactId>
            <version>5.5.10</version>
        </dependency>
        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>itext-asian</artifactId>
            <version>5.2.0</version>
        </dependency>
        <dependency>
            <groupId>com.itextpdf.tool</groupId>
            <artifactId>xmlworker</artifactId>
            <version>5.5.11</version>
        </dependency>
        <!-- itext5 end -->
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>21.0</version>
        </dependency>
        <dependency>
            <groupId>io.swagger</groupId>
            <artifactId>swagger-annotations</artifactId>
            <!--            <version>1.5.22</version>-->
            <version>${swagger-annotations.version}</version>
        </dependency>
        <dependency>
            <groupId>cn.dev33</groupId>
            <artifactId>sa-token-core</artifactId>
            <version>${sa-token.version}</version>
        </dependency>
        <dependency>
            <groupId>com.hzw</groupId>
            <artifactId>bid-freesia</artifactId>
            <version>${bid-freesia.version}</version>
        </dependency>
        <dependency>
            <groupId>com.hzw</groupId>
            <artifactId>bid-common-core</artifactId>
            <version>${bid-common-core.version}</version>
        </dependency>
        <dependency>
            <groupId>com.hzw</groupId>
            <artifactId>bid-common-bean</artifactId>
            <version>${bid-common-bean.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
            <version>4.1.2</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.freemarker</groupId>
            <artifactId>freemarker</artifactId>
            <version>2.3.28</version>
        </dependency>
        <dependency>
            <groupId>com.deepoove</groupId>
            <artifactId>poi-tl</artifactId>
            <version>1.12.0</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
            <version>2.11.0</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
            <version>5.2.2</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>5.2.2</version>
        </dependency>
        <!-- 转pdf -->
        <dependency>
            <groupId>fr.opensagres.xdocreport</groupId>
            <artifactId>xdocreport</artifactId>
            <version>1.0.6</version>
        </dependency>
        <dependency>
            <groupId>com.lowagie</groupId>
            <artifactId>itext</artifactId>
            <version>2.1.7</version>
        </dependency>
        <dependency>
            <groupId>com.lowagie</groupId>
            <artifactId>itext-rtf</artifactId>
            <version>2.1.7</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>ooxml-schemas</artifactId>
            <version>1.4</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml-schemas</artifactId>
            <version>4.0.0</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>4.1.0</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-scratchpad</artifactId>
            <version>4.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.deepoove</groupId>
            <artifactId>poi-tl</artifactId>
            <version>1.7.2</version>
        </dependency>

        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
            <version>4.1.0</version>
        </dependency>
        <dependency>
            <groupId>com.hzw</groupId>
            <artifactId>bid-common-security</artifactId>
            <version>${bid-common-security.version}</version>
        </dependency>
        <!--   pdf     -->
        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>forms</artifactId>
            <version>8.0.4</version>
        </dependency>
        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>layout</artifactId>
            <version>7.2.5</version>
        </dependency>
        <!--        <dependency>-->
        <!--            <groupId>com.itextpdf</groupId>-->
        <!--            <artifactId>svg</artifactId>-->
        <!--            <version>7.2.2</version>-->
        <!--        </dependency>-->
        <!--        <dependency>-->
        <!--            <groupId>com.itextpdf</groupId>-->
        <!--            <artifactId>pdfa</artifactId>-->
        <!--            <version>7.1.12</version>-->
        <!--        </dependency>-->
        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>html2pdf</artifactId>
            <version>5.0.4</version>
        </dependency>
        <!-- JSoup HTML Parser -->
        <!--        <dependency>-->
        <!--            <groupId>org.jsoup</groupId>-->
        <!--            <artifactId>jsoup</artifactId>-->
        <!--            <version>1.15.4</version>-->
        <!--        </dependency>-->
        <dependency>
            <groupId>com.alicp.jetcache</groupId>
            <artifactId>jetcache-anno-api</artifactId>
            <version>${jetcache.latest.version}</version>
        </dependency>
        <!--        <dependency>-->
        <!--            <groupId>com.itextpdf</groupId>-->
        <!--            <artifactId>styled-xml-parser</artifactId>-->
        <!--            <version>8.0.3</version>-->
        <!--        </dependency>-->
        <!--        <dependency>-->
        <!--            <groupId>com.itextpdf</groupId>-->
        <!--            <artifactId>font-asian</artifactId>-->
        <!--            <version>7.1.13</version>-->
        <!--        </dependency>-->
    </dependencies>
    <profiles>
        <profile>
            <id>dev</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <version>1.0.1-SNAPSHOT</version>
                <repository-id>rdc-snapshots</repository-id>
                <repository-url>https://packages.aliyun.com/maven/repository/2260816-snapshot-GzzKEy/</repository-url>
            </properties>
        </profile>
        <profile>
            <id>pro</id>
            <properties>
                <version>1.0.0</version>
                <repository-id>rdc-releases</repository-id>
                <repository-url>https://packages.aliyun.com/maven/repository/2260816-release-35QHzL/</repository-url>
            </properties>
        </profile>
    </profiles>

    <!-- 配置远程发布到私服，mvn deploy -->
    <distributionManagement>
        <!-- 定义releases库的坐标 -->
        <repository>
            <id>rdc-releases</id>
            <url>https://packages.aliyun.com/maven/repository/2260816-release-35QHzL/</url>
        </repository>
        <!-- 定义snapshots库 -->
        <snapshotRepository>
            <id>rdc-snapshots</id>
            <url>https://packages.aliyun.com/maven/repository/2260816-snapshot-GzzKEy/</url>
        </snapshotRepository>
    </distributionManagement>
    <build>
        <pluginManagement><!-- lock down plugins versions to avoid using Maven defaults (may be moved to parent pom) -->
            <plugins>

                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>3.8.1</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-resources-plugin</artifactId>
                    <version>2.6</version>
                </plugin>


            </plugins>
        </pluginManagement>

        <plugins>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>${project.build.sourceEncoding}</encoding>
                    <parameters>true</parameters>
                </configuration>
            </plugin>


            <!-- allatori 文件copy -->
            <!-- Allatori plugin start -->
            <!--            <plugin>-->
            <!--                <groupId>org.apache.maven.plugins</groupId>-->
            <!--                <artifactId>maven-resources-plugin</artifactId>-->
            <!--                <version>2.6</version>-->
            <!--                <executions>-->
            <!--                    <execution>-->
            <!--                        <id>copy-and-filter-allatori-config</id>-->
            <!--                        <phase>package</phase>-->
            <!--                        <goals>-->
            <!--                            <goal>copy-resources</goal>-->
            <!--                        </goals>-->
            <!--                        <configuration>-->
            <!--                            <outputDirectory>${basedir}/target</outputDirectory>-->
            <!--                            <resources>-->
            <!--                                <resource>-->
            <!--                                    <directory>${basedir}/lib</directory>-->
            <!--                                    <includes>-->
            <!--                                        <include>config.xml</include>-->
            <!--                                    </includes>-->
            <!--                                    <filtering>true</filtering>-->
            <!--                                </resource>-->
            <!--                            </resources>-->
            <!--                        </configuration>-->
            <!--                    </execution>-->
            <!--                </executions>-->
            <!--            </plugin>-->
            <!--            &lt;!&ndash; exec 执行allatori jar 混淆处理 &ndash;&gt;-->
            <!--            <plugin>-->
            <!--                <groupId>org.codehaus.mojo</groupId>-->
            <!--                <artifactId>exec-maven-plugin</artifactId>-->
            <!--                <version>1.6.0</version>-->
            <!--                <executions>-->
            <!--                    <execution>-->
            <!--                        <id>run-allatori</id>-->
            <!--                        <phase>package</phase>-->
            <!--                        <goals>-->
            <!--                            <goal>exec</goal>-->
            <!--                        </goals>-->
            <!--                    </execution>-->
            <!--                </executions>-->
            <!--                <configuration>-->
            <!--                    <executable>java</executable>-->
            <!--                    <arguments>-->
            <!--                        <argument>-Xms128m</argument>-->
            <!--                        <argument>-Xmx512m</argument>-->
            <!--                        <argument>-jar</argument>-->
            <!--                        <argument>${basedir}/lib/allatori.jar</argument>-->
            <!--                        <argument>${basedir}/target/config.xml</argument>-->
            <!--                    </arguments>-->
            <!--                </configuration>-->
            <!--            </plugin>-->
            <!--&lt;!&ndash;             deploy时只上传jar包到远程仓库的配置&ndash;&gt;-->
            <!--            <plugin>-->
            <!--                <groupId>org.apache.maven.plugins</groupId>-->
            <!--                <artifactId>maven-deploy-plugin</artifactId>-->
            <!--                <version>3.0.0</version>-->
            <!--                <executions>-->
            <!--                    <execution>-->
            <!--                        <id>default-deploy</id>-->
            <!--                        <phase>deploy</phase>-->
            <!--                        <goals>-->
            <!--                            <goal>deploy</goal>-->
            <!--                        </goals>-->
            <!--                        &lt;!&ndash; skip默认deploy插件的执行 &ndash;&gt;-->
            <!--                        <configuration>-->
            <!--                            <skip>true</skip>-->
            <!--                        </configuration>-->
            <!--                    </execution>-->
            <!--                    <execution>-->
            <!--                        <id>deploy-file</id>-->
            <!--                        <phase>deploy</phase>-->
            <!--                        <goals>-->
            <!--                            <goal>deploy-file</goal>-->
            <!--                        </goals>-->
            <!--                        <configuration>-->
            <!--                            &lt;!&ndash; 开发阶段上传到snapshot仓库，上线阶段上传到release仓库 &ndash;&gt;-->
            <!--                            <repositoryId>${repository-id}</repositoryId>-->
            <!--                            <url>${repository-url}</url>-->
            <!--                            <file>${project.build.directory}/${project.artifactId}-${version}-obfuscated.jar</file>-->
            <!--                            <groupId>${project.groupId}</groupId>-->
            <!--                            <artifactId>${project.artifactId}</artifactId>-->
            <!--                            <version>${version}</version>-->
            <!--                        </configuration>-->
            <!--                    </execution>-->
            <!--                </executions>-->
            <!--            </plugin>-->
        </plugins>
    </build>
</project>
