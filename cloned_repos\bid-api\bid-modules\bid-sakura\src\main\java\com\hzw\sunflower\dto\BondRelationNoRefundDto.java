package com.hzw.sunflower.dto;

import com.hzw.sunflower.entity.BondWater;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date ：Created in 2023/8/15 17:26
 * @description：
 * @modified By：`
 * @version: 1.0
 */
@ApiModel(description = "已关联保证金未退回")
@Data
public class BondRelationNoRefundDto extends BondWater implements Serializable {

    @ApiModelProperty(value = "供应商名称")
    private String companyName;

    @ApiModelProperty(value = "供应商id")
    private String companyId;

    @ApiModelProperty("项目编号")
    private String purchaseNumber;

    @ApiModelProperty("项目ID")
    private String projectId;

    @ApiModelProperty("标段id")
    private String sectionId;

    @ApiModelProperty("标段/包号")
    private Integer packageNumber;

    @ApiModelProperty("标段/包状态")
    private String sectionStatus;

    @ApiModelProperty(value = "到账时间")
    private String receiveTime;

    @ApiModelProperty(value = "余额")
    private BigDecimal balance;

    @ApiModelProperty("是否是中标人 0：否  1：是")
    private Integer isWin;

    @ApiModelProperty("是否是候选人 0：否  1：是")
    private Integer isCandidate;

    @ApiModelProperty("中标通知书发放时间")
    private String sendTime;

    @ApiModelProperty("备注")
    private String remark;


}
