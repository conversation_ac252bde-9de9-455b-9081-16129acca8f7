import "package:flutter/material.dart";

import 'package:flutter_main_app/src/constants/theme.dart';

class ResetPasswordPageWidget extends StatefulWidget {
  const ResetPasswordPageWidget({Key key}) : super(key: key);

  /// * 路由名称
  static const String _routeName = 'ResetPasswordPage';
  static String get routeName => _routeName;

  @override
  _ResetPasswordPageSate createState() => _ResetPasswordPageSate();
}

class _ResetPasswordPageSate extends State<ResetPasswordPageWidget> {
  @override
  Widget build(BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    return Scaffold(
      appBar: AppBar(
          // Here we take the value from the MyHomePage object that was created by
          // the App.build method, and use it to set our appbar title.
          title: Text(
            '重置密码',
            style: TextStyle(color: themeTitleColor),
          ),
          backgroundColor: Color.fromARGB(255, 255, 255, 255),
          leading: Container(
            child: <PERSON><PERSON><PERSON><PERSON>on(
              icon: const Icon(Icons.arrow_back_ios),
              color: themeTitleColor,
              focusColor: themeTitleColor,
              onPressed: () {
                Navigator.pop(context);
              },
            ),
          )),
      body: Stack(
        children: <Widget>[
          Column(
            children: <Widget>[
              Container(
                  width: screenWidth,
                  height: 200,
                  // color: Colors.green,
                  child: Center(
                      child: Image.asset(
                    'assets/images/logo.png',
                    width: 144,
                    height: 144,
                  ))),
              Column(
                children: <Widget>[
                  // 用户名
                  Padding(
                      padding: EdgeInsets.fromLTRB(20, 30, 25, 0),
                      child: Container(
                          decoration: BoxDecoration(
                              border: Border(
                                bottom: themeBorderSide,
                              )),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceAround,
                            children: <Widget>[
                              Padding(
                                padding: EdgeInsets.fromLTRB(0, 0, 34, 0),
                                child: Text(
                                  '+86',
                                  style: TextStyle(
                                      color: themeTitleColor, fontSize: 14),
                                ),
                              ),
                              Flexible(
                                child: TextField(
                                  decoration: InputDecoration.collapsed(
                                      hintText: '请输入您的手机号'),
                                ),
                              ),
                            ],
                          ))),
                  // 验证码
                  Padding(
                      padding: EdgeInsets.fromLTRB(20, 30, 25, 0),
                      child: Container(
                          decoration: BoxDecoration(
                              border: Border(
                                bottom: themeBorderSide,
                              )),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceAround,
                            children: <Widget>[
                              Padding(
                                padding: EdgeInsets.fromLTRB(0, 0, 19, 0),
                                child: Text(
                                  '验证码',
                                  style: TextStyle(
                                      color: themeTitleColor, fontSize: 14),
                                ),
                              ),
                              Flexible(
                                child: TextField(
                                  decoration: InputDecoration.collapsed(
                                      hintText: '请输入验证码'),
                                ),
                              ),
                              FlatButton(
                                onPressed: null,
                                child: Text(
                                  '获取验证码',
                                  style: TextStyle(
                                      color: Color(0xFFE85549), fontSize: themeFontSize),
                                ),
                              )
                            ],
                          ))),
                  // 密码
                  Padding(
                      padding: EdgeInsets.fromLTRB(20, 30, 25, 0),
                      child: Container(
                          decoration: BoxDecoration(
                              border: Border(
                                bottom: themeBorderSide,
                              )),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceAround,
                            children: <Widget>[
                              Padding(
                                padding: EdgeInsets.fromLTRB(0, 0, 19, 0),
                                child: Text(
                                  '密    码',
                                  style: TextStyle(
                                      color: themeTitleColor, fontSize: 14),
                                ),
                              ),
                              Flexible(
                                child: TextField(
                                  decoration: InputDecoration.collapsed(
                                      hintText: '请输入密码'),
                                ),
                              ),
                            ],
                          ))),
                  // 完成按钮
                  Padding(
                      padding: EdgeInsets.fromLTRB(25, 45, 25, 0),
                      child: SizedBox(
                          width: 325,
                          height: 40,
                          child: RaisedButton(
                            child: Text(
                              "完成",
                              style: TextStyle(color: Colors.white),
                            ),
                            onPressed: null,
                            disabledColor: Color(0xFFD5D5D5),
                            textColor: Colors.white,
                          ))),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }
}
