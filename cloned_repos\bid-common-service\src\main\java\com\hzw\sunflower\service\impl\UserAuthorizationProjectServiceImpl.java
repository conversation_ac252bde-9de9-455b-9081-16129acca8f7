package com.hzw.sunflower.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzw.sunflower.dao.UserAuthorizationProjectMapper;
import com.hzw.sunflower.entity.UserAuthorizationProject;
import com.hzw.sunflower.service.UserAuthorizationProjectService;
import org.springframework.stereotype.Service;


/**
 *UserService接口实现
 *
 * <AUTHOR>
 * @version 1.0.0 2021-04-21
 */
@Service
public class UserAuthorizationProjectServiceImpl extends ServiceImpl<UserAuthorizationProjectMapper, UserAuthorizationProject> implements UserAuthorizationProjectService {

    @Override
    public Boolean updateUserPersonAuthorization(Long userId, Integer identity, Long legalPersonAuthorization) {
        return this.baseMapper.updateUserPersonAuthorization(userId,identity,legalPersonAuthorization);
    }

}