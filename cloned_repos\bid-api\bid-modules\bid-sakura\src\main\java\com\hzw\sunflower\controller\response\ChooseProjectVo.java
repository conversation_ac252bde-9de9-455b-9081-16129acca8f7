package com.hzw.sunflower.controller.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@ApiModel(description = "保证金异常退款申请详情-选择的项目信息")
@Data
public class ChooseProjectVo {

    @ApiModelProperty("项目信息")
    private List<MyCompanyProjectVo> myCompanyProjectVos;

    @ApiModelProperty("付款凭证")
    private String fileIds;

    @ApiModelProperty("付款凭证key")
    private String fileKey;

    @ApiModelProperty("退款说明函")
    private String refundFiles;

    @ApiModelProperty("退款说明函key")
    private String refundFileKey;

}
