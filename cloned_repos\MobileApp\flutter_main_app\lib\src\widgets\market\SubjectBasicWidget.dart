///***
/// * 主体人|机构信息 - 基本信息
/// */

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import 'package:flutter_main_app/serviceLocator.dart';
import 'package:flutter_main_app/src/constants/theme.dart';
import 'package:flutter_main_app/src/utils/ScreenUtils.dart';

import 'package:flutter_main_app/src/viewModels/marketPlayer/SubjectBasicViewModel.dart';

import 'package:flutter_main_app/src/models/marketPlayer/SubjectBasicModel.dart';

import 'package:flutter_main_app/src/components/CustomAppBar.dart';
import 'package:flutter_main_app/src/components/CustomCircularProgressIndicator.dart';
import 'package:flutter_main_app/src/components/CustomSafeArea.dart';
import 'package:flutter_main_app/src/components/CustomTab.dart';
import 'package:flutter_main_app/src/components/CustomFieldDisplay.dart';

class SubjectBasicWidget extends StatefulWidget {
  const SubjectBasicWidget({Key key}) : super(key: key);

  /// * 路由名称
  static const String _routeName = 'SubjectBasic';
  static String get routeName => _routeName;

  @override
  _SubjectBasicWidgetState createState() =>
      _SubjectBasicWidgetState();
}

class _SubjectBasicWidgetState
    extends State<SubjectBasicWidget> {

  static const int _topInfoGridCrossAxisCount = 4;
  static const double _topInfoGridItemHeight = 36.0;

  static final double _topInfoGridItemWidth = (ScreenUtils.screenWidth - 15.0 - 15.0) / _topInfoGridCrossAxisCount;

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider<SubjectBasicViewModel>.value(
      value: serviceLocator<SubjectBasicViewModel>(),
      child: Consumer<SubjectBasicViewModel>(
        builder: (context, model, child) {
          final SubjectBasicModel subjectBasic = model.subjectBasic;

          return Scaffold(
            appBar: CustomAppBar(
              title: '基本信息',
            ),
            body: model.isLoading
              ? CustomCircularProgressIndicator()
              : CustomSafeArea(
                  backgroundColor: themeContentBackgroundColor,
                  child: ListView(
                    physics: BouncingScrollPhysics(),
                    children: <Widget>[
                      /// * 主要信息
                      Column(
                        children: <Widget>[
                          /// * 头部
                          Container(
                            padding: const EdgeInsets.only(
                              left: 15.0,
                              top: 18.0,
                              right: 15.0,
                              bottom: 20.0,
                            ),
                            width: ScreenUtils.screenWidth,
                            decoration: BoxDecoration(
                              border: Border(
                                bottom: BorderSide(
                                  width: themeBorderWidth,
                                  color: themeBorderColor,
                                ),
                              ),
                            ),
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.start,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: <Widget>[
                                Text(
                                  '${model.subjectBasic.subjectName}',
                                  style: TextStyle(
                                    fontSize: 16.0,
                                    color: themeActiveColor,
                                  ),
                                ),
                                Padding(
                                  padding: const EdgeInsets.only(top: 10.0),
                                  child: CustomTab(
                                    text: '存续',
                                  ),
                                ),
                                SizedBox(
                                  width: _topInfoGridItemWidth * _topInfoGridCrossAxisCount,
                                  height: _topInfoGridItemHeight + 16.0,
                                  child: GridView.count(
                                    padding: const EdgeInsets.only(top: 16.0),
                                    crossAxisCount: _topInfoGridCrossAxisCount,
                                    childAspectRatio: _topInfoGridItemWidth / _topInfoGridItemHeight,
                                    children: this._buildTopGridChildrenWidgets(context, model.subjectBasic),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          /// * 键值对
                          Container(
                            padding: const EdgeInsets.only(
                              left: 15.0,
                              top: 14.0,
                              right: 15.0,
                              bottom: 14.0,
                            ),
                            width: ScreenUtils.screenWidth,
                            decoration: BoxDecoration(
                              border: Border(
                                bottom: BorderSide(
                                  width: 6.0,
                                  color: themeBackgroundColor,
                                ),
                              ),
                            ),
                            child: Column(
                              children: <Widget>[
                                CustomFieldDisplay(
                                  fieldName: '统一社会信用代码',
                                  fieldValue: '${subjectBasic.subjectCode}',
                                  height: 29.0,
                                  fieldNameColor: themeTextColor,
                                  /// * 更新版本的 米6 上 MIUI v11.0.2 会超出 3px
                                  /// * 所以多算一个字
                                  maxFieldNameLength: 9,
                                  // fieldNameWidth: 12.0 * (8 + 1 + 0.5),
                                ),
                                CustomFieldDisplay(
                                  fieldName: '法定代表',
                                  fieldValue: '${subjectBasic.legalName}',
                                  height: 29.0,
                                  fieldNameColor: themeTextColor,
                                ),
                                CustomFieldDisplay(
                                  fieldName: '成立时间',
                                  fieldValue: '${subjectBasic.registerTime}',
                                  height: 29.0,
                                  fieldNameColor: themeTextColor,
                                ),
                                CustomFieldDisplay(
                                  fieldName: '企业地址',
                                  fieldValue: '${subjectBasic.address}',
                                  height: 29.0,
                                  fieldNameColor: themeTextColor,
                                  isFieldValueMultiLine: true,
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                      /// * 次要信息
                      Container(
                        padding: const EdgeInsets.only(
                          left: 15.0,
                          top: 11.0,
                          right: 15.0,
                        ),
                        width: ScreenUtils.screenWidth,
                        child: Column(
                          children: <Widget>[
                            CustomFieldDisplay(
                              fieldName: '企业类型',
                              fieldValue: '${subjectBasic.enterpriseType}',
                              height: 35.0,
                            ),
                            CustomFieldDisplay(
                              fieldName: '登记机关',
                              fieldValue: '${subjectBasic.registrationAuthority}',
                              height: 35.0,
                            ),
                            CustomFieldDisplay(
                              fieldName: '注册资本',
                              fieldValue: '${subjectBasic.capital}',
                              height: 35.0,
                            ),
                            CustomFieldDisplay(
                              fieldName: '经营期限',
                              fieldValue: '${subjectBasic.registerTime} 至 ${subjectBasic.operationDeadline}',
                              height: 35.0,
                            ),
                            CustomFieldDisplay(
                              fieldName: '安许证号',
                              fieldValue: '',
                              height: 35.0,
                            ),
                            CustomFieldDisplay(
                              fieldName: '安许期限',
                              fieldValue: '',
                              height: 35.0,
                            ),
                            CustomFieldDisplay(
                              fieldName: '经营范围',
                              fieldValue: '${subjectBasic.businessScope}',
                              height: 35.0,
                              isFieldValueMultiLine: true,
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
          );
        },
      ),
    );
  }

  ///***
  /// * 生成顶部网格状信息组件 UI
  /// *
  /// * @param {BuildContext} context
  /// * @param {SubjectBasicModel} subjectBasic
  /// * @return {List<Widget>}
  /// */
  List<Widget> _buildTopGridChildrenWidgets(BuildContext context, SubjectBasicModel subjectBasic) {
    const double dividerHeight = 20.0;

    const List<String> topInfoFieldName= [
      '法定代表人', '注册资本', '成立日期', '分支机构',
    ];

    /// * 处理字段值
    final RegExp chineseRe = RegExp(
      r'[\u4e00-\u9fa5]+$',
      caseSensitive: false,
      multiLine: false,
    );

    final String legalName = subjectBasic.legalName;

    String capital = subjectBasic.capital;

    if (capital != null) {
      final String capitalUnit = chineseRe.stringMatch(capital) ?? '';

      final String capitalNumberStr = capital.replaceFirst(capitalUnit, '');

      final String capitalNumber = capitalNumberStr.length > 0
        ? double.parse(capitalNumberStr).toStringAsFixed(2)
        : '';

      capital = capitalNumber + capitalUnit;
    }

    final String registerTime = subjectBasic.registerTime?.replaceAll('-', '.');

    final int branchCount = subjectBasic.branchCount;

    final List<String> topInfoFieldValue = [
      '$legalName',
      '$capital',
      '$registerTime',
      '$branchCount 家',
    ];

    final List<Widget> gridChildren = [];

    for (int i = 0, len = topInfoFieldName.length; i < len; i++) {
      final Widget gridChild = Stack(
        alignment: AlignmentDirectional.topStart,
        children: <Widget>[
          i % _topInfoGridCrossAxisCount != 0
            ? VerticalDivider(
                width: themeBorderWidth,
                thickness: themeBorderWidth,
                color: themeBorderColor,
                indent: (_topInfoGridItemHeight - dividerHeight) / 2,
                endIndent: (_topInfoGridItemHeight - dividerHeight) / 2,
              )
            : Container(),
          Container(
            width: _topInfoGridItemWidth,
            height: _topInfoGridItemHeight,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: <Widget>[
                Text(
                  topInfoFieldName[i],
                  style: TextStyle(
                    height: 1.4,
                    fontSize: 10.0,
                    color: themeHintTextColor,
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.only(top: 3.0),
                  child: Text(
                    topInfoFieldValue[i],
                    maxLines: 1,
                    style: TextStyle(
                      height: 1.4,
                      fontSize: 12.0,
                      color: themeTextColor,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      );

      gridChildren.add(gridChild);
    }

    return gridChildren;
  }

}
