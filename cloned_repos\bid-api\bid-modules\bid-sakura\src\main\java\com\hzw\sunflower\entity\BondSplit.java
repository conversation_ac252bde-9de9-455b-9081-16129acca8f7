package com.hzw.sunflower.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.hzw.sunflower.common.BaseBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 保证金拆分
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-15
 */
@Getter
@Setter
@TableName("t_bond_split")
@ApiModel(value = "BondSplit对象", description = "保证金拆分")
public class BondSplit extends BaseBean implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    @TableId("id")
    private Long id;

    @ApiModelProperty("关联项目id")
    @TableField("project_id")
    private Long projectId;

    @ApiModelProperty("关联包id")
    @TableField("section_id")
    private Long sectionId;

    @ApiModelProperty("关联流水id")
    @TableField("water_id")
    private Long waterId;

    @ApiModelProperty("关联供应商id")
    @TableField("company_id")
    private Long companyId;

    @ApiModelProperty("拆分金额")
    @TableField("amount")
    private BigDecimal amount;

    @ApiModelProperty("异常关联标识")
    @TableField("except_flag")
    private Integer exceptFlag;

    @ApiModelProperty("是否最新（ncc推送使用）")
    @TableField("is_latest")
    private Integer isLatest;

}
