package com.hzw.bid.util.oss;

import com.amazonaws.ClientConfiguration;
import com.amazonaws.Protocol;
import com.amazonaws.auth.PropertiesCredentials;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3Client;
import com.amazonaws.services.s3.S3ClientOptions;
import com.amazonaws.services.s3.model.GeneratePresignedUrlRequest;
import com.amazonaws.services.s3.model.GetObjectRequest;
import com.amazonaws.services.s3.model.ObjectMetadata;
import com.amazonaws.services.s3.model.S3Object;
import com.hzw.bid.properties.OssCtyunProperties;
import com.hzw.bid.util.SpringUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Value;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.net.URL;
import java.util.Date;
import java.util.UUID;

/**
 * <AUTHOR>
 * @date ：Created in 2022/6/10 15:29
 * @description：
 * @modified By：`
 * @version: 1.0
 */
@Slf4j
@Data
public class CtyunFileStorage implements OssFileStorage {

    @Value("${oss.ctyun.endpoint}")
    private String endpoint;

    @Value("${oss.ctyun.accessKeyId}")
    private String accessKeyId;

    @Value("${oss.ctyun.accessKeySecret}")
    private String accessKeySecret;

    @Value("${oss.ctyun.bucketName}")
    private String bucketName;

    public AmazonS3 getAmazonS3() {

        OssCtyunProperties properties = SpringUtils.getBean(OssCtyunProperties.class);
        this.endpoint = properties.getEndpoint();
        this.accessKeyId = properties.getAccessKeyId();
        this.accessKeySecret = properties.getAccessKeySecret();
        this.bucketName = properties.getBucketName();

        ClientConfiguration clientConfig = new ClientConfiguration();
        // 设置连接的超时时间，单位毫秒
        clientConfig.setConnectionTimeout(30 * 1000);
        // 设置 socket 超时时间，单位毫秒
        clientConfig.setSocketTimeout(30 * 1000);
        clientConfig.setProtocol(Protocol.HTTP); //设置 http
        // 设置 V4 签名算法中负载是否参与签名，关于签名部分请参看《OOS 开发者文档》
        S3ClientOptions options = new S3ClientOptions();
        options.setPayloadSigningEnabled(true);
        // 创建 client
        AmazonS3 oosClient = new AmazonS3Client(
                new PropertiesCredentials(accessKeyId, accessKeySecret), clientConfig);
        // 设置 endpoint
        oosClient.setEndpoint(endpoint);
        //设置选项
        oosClient.setS3ClientOptions(options);
        if (!oosClient.doesBucketExist(bucketName)) {
            oosClient.createBucket(bucketName);
        }
        return oosClient;
    }

    /**
     * 关闭
     * @param s3
     */
    public void shutdown(AmazonS3 s3){
        if (s3 != null) {
            ((AmazonS3Client) s3).shutdown();
        }
    }


    @Override
    public byte[] downloadFile(String key) {
        AmazonS3 s3 = getAmazonS3();
        try {
            S3Object object = s3.getObject(bucketName, key);
            return IOUtils.toByteArray(object.getObjectContent());
        } catch (Exception e) {
            e.printStackTrace();
            return new byte[]{};
        } finally {
            shutdown(s3);
        }
    }

    @Override
    public void downloadFilePath(String key,String filePath) {
        AmazonS3 s3 = getAmazonS3();
        try {
            GetObjectRequest objectRequest = new GetObjectRequest(bucketName, key);
            ObjectMetadata object1 = s3.getObject(objectRequest, new File(filePath));
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            shutdown(s3);
        }
    }


    @Override
    public byte[] downloadFile(String key,String bucketName) {
        AmazonS3 s3 = getAmazonS3();
        try {
            S3Object object = s3.getObject(bucketName, key);
            return IOUtils.toByteArray(object.getObjectContent());
        } catch (Exception e) {
            e.printStackTrace();
            return new byte[]{};
        } finally {
            shutdown(s3);
        }
    }

    @Override
    public String uploadFile(String key, InputStream is) {
        AmazonS3 s3 = getAmazonS3();
        try {
            ObjectMetadata objectMetadata = new ObjectMetadata();
            objectMetadata.setCacheControl("no-cache");
            objectMetadata.setHeader("Pragma", "no-cache");
            objectMetadata.setContentLength(is.available());
            s3.putObject(bucketName, key, is, objectMetadata);
            return key;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        } finally {
            shutdown(s3);
        }
    }

    @Override
    public String uploadFile(String key, InputStream is,String bucketName) {
        AmazonS3 s3 = getAmazonS3();
        try {
            ObjectMetadata objectMetadata = new ObjectMetadata();
            objectMetadata.setCacheControl("no-cache");
            objectMetadata.setHeader("Pragma", "no-cache");
            objectMetadata.setContentLength(is.available());
            s3.putObject(bucketName, key, is, objectMetadata);
            return key;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        } finally {
            shutdown(s3);
        }
    }

    @Override
    public String uploadFileByPath(String filePath) {
        AmazonS3 s3 = getAmazonS3();
        try {
            String key = "";
            //上传到oss
            File ossFile = new File(filePath);
            key = UUID.randomUUID() + ossFile.getName();
//            s3.putObject(bucketName, key, ossFile);
            InputStream is = new FileInputStream(ossFile);
            ObjectMetadata objectMetadata = new ObjectMetadata();
            objectMetadata.setCacheControl("no-cache");
            objectMetadata.setHeader("Pragma", "no-cache");
            objectMetadata.setContentLength(is.available());
            s3.putObject(bucketName, key, is, objectMetadata);
            // 关闭文件流
            is.close();
            return key;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        } finally {
            shutdown(s3);
        }
    }

    @Override
    public String generatePresignedUrl(String key) {
        AmazonS3 s3 = getAmazonS3();
        GeneratePresignedUrlRequest request = new
                GeneratePresignedUrlRequest(bucketName,key);
        Date expireDate = new Date(199,12,10);
        request.setExpiration(expireDate);
        URL url = s3.generatePresignedUrl(request);
        return url.toString();
    }

    @Override
    public String generatePresignedUrl(String key,String bucketName) {
        AmazonS3 s3 = getAmazonS3();
        GeneratePresignedUrlRequest request = new
                GeneratePresignedUrlRequest(bucketName,key);
        Date expireDate = new Date(199,12,10);
        request.setExpiration(expireDate);
        URL url = s3.generatePresignedUrl(request);
        return url.toString();
    }


    @Override
    public Object getSimplifiedObjectMeta(String key) {
        AmazonS3 s3 = getAmazonS3();
        try {
            return s3.getObjectMetadata(bucketName, key);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        } finally {
            shutdown(s3);
        }
    }
}
