package com.hzw.sunflower.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hzw.sunflower.controller.request.BidDocumentPurchaseTypeReq;
import com.hzw.sunflower.entity.BidDocumentPurchaseType;


/**
 * <AUTHOR>
 * @date ：Created in 2021/7/8 13:53
 * @description：标段包与文件目录关系表service
 * @modified By：`
 * @version: 1.0
 */
public interface BidDocumentPurchaseTypeService extends IService<BidDocumentPurchaseType> {

    /**
     * 保存开评标备案采购方式
     * @param bidDocumentPurchaseType
     * @return
     */
    Boolean savePurchaseType(BidDocumentPurchaseTypeReq bidDocumentPurchaseType);
}
