package com.hzw.ssm.fw.util;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.web.context.ContextLoader;

/**
 * spring 上下文工具
 * <AUTHOR>
 *
 */
public class SpringApplicationContext implements ApplicationContextAware {
	
	/**
	 * spring上下文
	 */
	private static ApplicationContext applicationContext;

	/**
	 * spring注入上下文
	 */
	@Override
	public void setApplicationContext(ApplicationContext arg0) throws BeansException {
		// TODO Auto-generated method stub
		this.applicationContext=applicationContext;
	}
	
	/**
	 * 获取spring bean
	 * @param id 利用id获取上下文中的bean
	 * @return
	 */
	public static Object getBean(String id){
		if(applicationContext==null){
			applicationContext=ContextLoader.getCurrentWebApplicationContext();
		}
		return applicationContext.getBean(id);
	}

	/**
	 * 获取上下文
	 * @return
	 */
	public static ApplicationContext getApplicationContext() {
		return applicationContext;
	}
	
    /**
     * 函数功能描述：javaBean赋值
     * @param source 源对象
     * @param target 目标对象
     * @return 返回目标对象
     */
    public static <T> T copyProperties(Object source, T target) {

        BeanUtils.copyProperties(source, target);

        return target;
    }
	

}
