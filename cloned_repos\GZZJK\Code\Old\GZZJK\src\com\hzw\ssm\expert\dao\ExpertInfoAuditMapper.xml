<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper 
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hzw.ssm.expert.dao.ExpertInfoAuditMapper">
	
	<!--获取专家信息列表  -->
	<select id="queryPageExpertInfoAuditList" parameterType="ExpertInfoEntity" resultType="ExpertInfoEntity">
		<!-- select 
			expi.id,
			expi.user_id,
			expi.user_name,
			expi.province,
			expi.city,
			expi.zone,
			expi.district,
			expi.modify_time,
			expi.status,
			(select wm_concat(tsi.spe_name) 
			 from t_specialty_info tsi 
			 where tsi.spe_id in 
				(select expm.major from t_expert_major expm 
					where expm.delete_flag=0 and expm.major is not null and expm.user_id=expi.user_id)
			) as specialty_name
		from t_expert_info expi 
		where expi.delete_flag=0 
			<if test="user_name!=null and user_name!=''">
				and expi.user_name like '%${user_name}%'
			</if>
			<if test="status!=null and status!=''">
				and expi.status=#{status}
			</if>
			<if test="province!=null and province!=''">
				and expi.province like '%${province}%'
			</if>
			<if test="city!=null and city!=''">
				and expi.city like '%${city}%'
			</if>
			<if test="search_begin_time!=null and search_begin_time!=''">
				and to_date(to_char(expi.modify_time,'yyyy-mm-dd'),'yyyy-mm-dd')>=to_date(#{search_begin_time},'yyyy-mm-dd')
			</if>
			<if test="search_end_time!=null and search_end_time!=''">
				and to_date(to_char(expi.modify_time,'yyyy-mm-dd'),'yyyy-mm-dd')<![CDATA[<=]]>to_date(#{search_end_time},'yyyy-mm-dd')
			</if>
			<if test="spe_id!=null and spe_id!=''">
				and expi.user_id in (select tm.user_id from t_expert_major tm where tm.major=#{spe_id})
			</if>
			order by expi.modify_time desc -->
			
			select DISTINCT expi.* from(
				select i.id, i.user_id, i.user_name, i.province, i.city, i.zone, i.district, i.modify_time, i.status, i.enter_flag enterFlag, i.expert_type expertType, max(i.specialty_name) specialty_name from (
		       select a.id, a.user_id, a.user_name, a.province, a.city, a.zone, a.district, a.modify_time, a.status, a.enter_flag, a.expert_type,
		              wm_concat(tsi.spe_name) over (partition by a.user_id order by to_number(tsi.spe_id)) specialty_name
		        from t_expert_info a
		        left join t_expert_major expm on expm.user_id = a.user_id
                left join t_specialty_info tsi on tsi.spe_id = expm.major
		       where a.delete_flag = 0 and expm.delete_flag = 0 and expm.major is not null
		         <if test="user_name!=null and user_name!=''">
					and a.user_name like '%${user_name}%'
				</if>
				<if test="status!=null and status!=''">
					and a.status=#{status}
				</if>
				<if test="province!=null and province!=''">
					and a.province like '%${province}%'
				</if>
				<if test="city!=null and city!=''">
					and a.city like '%${city}%'
				</if>
				<if test="search_begin_time!=null and search_begin_time!=''">
					and to_date(to_char(a.modify_time,'yyyy-mm-dd'),'yyyy-mm-dd')>=to_date(#{search_begin_time},'yyyy-mm-dd')
				</if>
				<if test="search_end_time!=null and search_end_time!=''">
					and to_date(to_char(a.modify_time,'yyyy-mm-dd'),'yyyy-mm-dd')<![CDATA[<=]]>to_date(#{search_end_time},'yyyy-mm-dd')
				</if>
				<if test="spe_id!=null and spe_id!=''">
					and expm.major=#{spe_id}
				</if>
				) i
                group by i.id, i.user_id, i.user_name, i.province, i.city, i.zone, i.district, i.status, i.enter_flag, i.expert_type, i.modify_time
		       union
		       select k.id, k.user_id, k.user_name, k.province, k.city, k.zone, k.district, k.modify_time, k.status, k.enter_flag enterFlag, k.expert_type expertType, max(specialty_name) specialty_name from (
		       select c.id, c.user_id, c.user_name, c.province, c.city, c.zone, c.district, c.modify_time, c.status, c.enter_flag, c.expert_type,
		       		wm_concat(tsi.spe_name) over (partition by c.user_id order by to_number(tsi.spe_id)) specialty_name from(
		       select 
		             b.id,
		             b.user_id,
		             b.user_name,
		             b.province,
		             b.city,
		             b.zone,
		             b.district,
		             b.modify_time,
		             b.status,
		             b.expert_type,
		             b.enter_flag,
		             max(a.audit_time) audit_time
		      from t_expert_audit a
		      left join t_expert_info b on b.user_id = a.expert_id
		      where b.delete_flag = 0 
		       <if test="user_name!=null and user_name!=''">
					and b.user_name like '%${user_name}%'
				</if>
				<if test="status_!=null and status_!=''">
					and b.status in (${status_})
				</if>
				<if test="audit_user!=null and audit_user!=''">
					and a.audit_user = #{audit_user}
				</if>
				<if test="province!=null and province!=''">
					and b.province like '%${province}%'
				</if>
				<if test="city!=null and city!=''">
					and b.city like '%${city}%'
				</if>
				<if test="search_begin_time!=null and search_begin_time!=''">
					and to_date(to_char(b.modify_time,'yyyy-mm-dd'),'yyyy-mm-dd')>=to_date(#{search_begin_time},'yyyy-mm-dd')
				</if>
				<if test="search_end_time!=null and search_end_time!=''">
					and to_date(to_char(b.modify_time,'yyyy-mm-dd'),'yyyy-mm-dd')<![CDATA[<=]]>to_date(#{search_end_time},'yyyy-mm-dd')
				</if>
				<if test="spe_id!=null and spe_id!=''">
					and expm.major=#{spe_id}
				</if>
		      group by b.id,
		             b.user_id,
		             b.user_name,
		             b.province,
		             b.city,
		             b.zone,
		             b.district,
		             b.modify_time,
		             b.status,
		             b.expert_type,
		             b.enter_flag) c
		             left join t_expert_major expm on expm.user_id = c.user_id
                     left join t_specialty_info tsi on tsi.spe_id = expm.major
		             where expm.delete_flag = 0 and expm.major is not null) k
                     group by k.id, k.user_id, k.user_name, k.province, k.city, k.zone, k.district, k.status, k.enter_flag, k.expert_type, k.modify_time) expi
		             <if test="orderStatus!=null and orderStatus!=''">
			             order by case status 
			             		${orderStatus}
				　　　　         end,modify_time desc
					</if>
					<if test="orderStatus==null or orderStatus==''">
						order by modify_time desc
					</if>
	</select>
	
	<!--获取准入审批专家信息列表  -->
	<select id="queryPageExpInfoAuditList" parameterType="ExpertInfoEntity" resultType="ExpertInfoEntity">
		select DISTINCT expi.* from(
		       select i.id, i.user_id, i.user_name, i.province, i.city, i.zone, i.district, i.status, i.modify_time, i.enter_flag enterFlag, i.expert_type expertType, max(i.specialty_name) specialty_name from (
                  select a.id, a.user_id, a.user_name, a.province, a.city, a.zone, a.district, a.status, a.modify_time, a.enter_flag, a.expert_type,
		            wm_concat(tsi.spe_name) over (partition by a.user_id order by to_number(tsi.spe_id)) specialty_name
		        from t_expert_info a
		        left join t_expert_major expm on expm.user_id = a.user_id
                left join t_specialty_info tsi on tsi.spe_id = expm.major
		       where a.delete_flag = 0 and expm.delete_flag = 0 and a.enter_flag between 2 and 3
		         <if test="user_name!=null and user_name!=''">
					and a.user_name like '%${user_name}%'
				</if>
				<if test="status!=null and status!=''">
					and a.status=#{status}
				</if>
				<if test="province!=null and province!=''">
					and a.province like '%${province}%'
				</if>
				<if test="city!=null and city!=''">
					and a.city like '%${city}%'
				</if>
				<if test="search_begin_time!=null and search_begin_time!=''">
					and to_date(to_char(a.modify_time,'yyyy-mm-dd'),'yyyy-mm-dd')>=to_date(#{search_begin_time},'yyyy-mm-dd')
				</if>
				<if test="search_end_time!=null and search_end_time!=''">
					and to_date(to_char(a.modify_time,'yyyy-mm-dd'),'yyyy-mm-dd')<![CDATA[<=]]>to_date(#{search_end_time},'yyyy-mm-dd')
				</if>
				<if test="spe_id!=null and spe_id!=''">
					and expm.major=#{spe_id}
				</if>
				 and expm.delete_flag = 0 and expm.major is not null) i
                 group by i.id, i.user_id, i.user_name, i.province, i.city, i.zone, i.district, i.status, i.modify_time, i.enter_flag, i.expert_type
		       union
		       select k.id, k.user_id, k.user_name, k.province, k.city, k.zone, k.district, k.status, k.modify_time, k.enter_flag enterFlag, k.expert_type expertType, max(k.specialty_name) specialty_name from (
		       	select c.id, c.user_id, c.user_name, c.province, c.city, c.zone, c.district, c.status, c.modify_time, c.enter_flag, c.expert_type,
	              wm_concat(tsi.spe_name) over (partition by c.user_id order by to_number(tsi.spe_id)) specialty_name  from(
	             select e.id, e.user_id, e.user_name, e.province, e.city, e.zone, e.district, e.status, e.modify_time, e.enter_flag, e.expert_type, max(a.audit_time) audit_time from t_expert_audit a
					left join t_expert_info e on e.user_id = a.expert_id
					 where e.delete_flag = 0 and e.enter_flag between 2 and 3 and a.expert_id not in
					(select distinct expert_id from t_expert_audit 
					where status = 0 and audit_user in (select user_id from ts_user where role_id = #{roleId}))
					and a.audit_user = #{audit_user}
					group by e.id, e.user_id, e.user_name, e.province, e.city, e.zone, e.district, e.status, e.modify_time, e.enter_flag, e.expert_type
					union all
					select e.id, e.user_id, e.user_name, e.province, e.city, e.zone, e.district, 3 status, e.modify_time, e.enter_flag enterFlag, e.expert_type expertType, min(a.audit_time) audit_time from t_expert_audit a
					 left join t_expert_info e on e.user_id = a.expert_id
					where e.delete_flag = 0 and e.enter_flag between 2 and 3 and a.status = 0 and a.audit_user in (select user_id from ts_user where role_id = #{roleId})
					and expert_id in (select expert_id from t_expert_audit where audit_user = #{audit_user})
					group by e.id, e.user_id, e.user_name, e.province, e.city, e.zone, e.district, e.status, e.modify_time, e.enter_flag, e.expert_type) c
					left join t_expert_major expm on expm.user_id = c.user_id
                    left join t_specialty_info tsi on tsi.spe_id = expm.major
					where 1=1
		       <if test="user_name!=null and user_name!=''">
					and c.user_name like '%${user_name}%'
				</if>
				<if test="province!=null and province!=''">
					and c.province like '%${province}%'
				</if>
				<if test="city!=null and city!=''">
					and c.city like '%${city}%'
				</if>
				<if test="search_begin_time!=null and search_begin_time!=''">
					and to_date(to_char(c.modify_time,'yyyy-mm-dd'),'yyyy-mm-dd')>=to_date(#{search_begin_time},'yyyy-mm-dd')
				</if>
				<if test="search_end_time!=null and search_end_time!=''">
					and to_date(to_char(c.modify_time,'yyyy-mm-dd'),'yyyy-mm-dd')<![CDATA[<=]]>to_date(#{search_end_time},'yyyy-mm-dd')
				</if>
				<if test="spe_id!=null and spe_id!=''">
					and expm.major=#{spe_id}
				</if>
				 and expm.delete_flag = 0 and expm.major is not null) k
                 group by k.id, k.user_id, k.user_name, k.province, k.city, k.zone, k.district, k.status, k.modify_time, k.enter_flag, k.expert_type
		      ) expi
			<if test="orderStatus!=null and orderStatus!=''">
				order by case status
				${orderStatus}
				　　　　         end,modify_time desc
			</if>
			<if test="orderStatus==null or orderStatus==''">
				order by modify_time desc
			</if>
	</select>
	
	<!--添加专家审核记录  -->
	<insert id="addExpertAuditReason" parameterType="ExpertAuditEntity">
		insert into t_expert_audit(
			id,
			expert_id,
			reason,
			status,
			audit_time,
			delete_flag,
			audit_user
		) values(
			#{id},
			#{expert_id},
			#{reason,jdbcType=VARCHAR},
			#{status},
			#{audit_time},
			#{delete_flag},
			#{audit_user}
		)
	</insert>
	
	<!--修改专家信息表状态  -->
	<update id="auditExpertInfo" parameterType="ExpertInfoEntity">
		update t_expert_info ti set ti.status=#{status}
		<if test="audit_time!=null and audit_time!=''">
			,ti.audit_time=#{audit_time}
		</if>
		where ti.user_id=#{user_id}
	</update>
	
	<!--专家审核通过则为专家生成专家编号  -->
	<update id="createExpertForExpertNum" parameterType="ExpertInfoEntity">
		update t_expert_info ti set ti.expert_num=#{expert_num} where ti.user_id=#{user_id}
	</update>
	
	<!--专家审核信息列表  -->
	<select id="queryExpertAuditEntityList" parameterType="String" resultType="ExpertAuditEntity">
		select tea.id,tea.expert_id,tea.reason,tea.status,tea.audit_time,tu.user_name as audit_user_name from t_expert_audit tea
 		left join ts_user tu on tu.user_id=tea.audit_user
		 where tea.delete_flag=0 and tea.expert_id=#{user_id} order by tea.audit_time desc
	</select>
	
	<!-- 最终审核通过审核记录delete_flag=1 -->
	<update id="updateAuditEntityByUserId" parameterType="String">
		update t_expert_audit t set t.delete_flag=1 where t.expert_id=#{user_id}
	</update>
</mapper>
