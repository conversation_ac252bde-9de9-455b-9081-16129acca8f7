package com.hzw.sunflower.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date ：Created in 2021/9/14 16:12
 * @description：重新招标标段标书费、付款状态
 * @version: 1.0
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class ReBidInfoDTO {

    @ApiModelProperty(value = "原标段ID", position = 1)
    private Long formerSectionId;

    @ApiModelProperty(value = "标书费类型 0不收取 1按套 2按标段/包", position = 2)
    private Integer tenderFeeType;

    @ApiModelProperty(value = "标书费金额", position = 3)
    private BigDecimal tenderFee;

    @ApiModelProperty(value = "付款状态（0.未付款 1.付款确认中（线下）2.已付款 3.退款确认中 4.退款成功 5.退款失败 6.退款驳回）", position = 4)
    private Integer payStatus;

    @ApiModelProperty(value = "上次项目投标人(1.已购买招标文件的投标人 2.已通过关注审核的投标人 3.指定投标人 4.不带入)", position = 5)
    private Integer oldBidderType;

    @ApiModelProperty(value = "标书费支付方式", position = 3)
    private Integer paymentType;
}
