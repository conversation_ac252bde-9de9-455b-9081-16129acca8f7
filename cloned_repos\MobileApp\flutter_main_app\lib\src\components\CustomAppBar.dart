///***
/// * 当前项目固定样式的导航栏
/// */

import 'package:flutter/material.dart';

class CustomAppBar extends StatelessWidget implements PreferredSizeWidget {
  const CustomAppBar({
    Key key,
    @required this.title,
    this.leading,
    this.leadingIconData = Icons.arrow_back_ios,
    this.leadingOnPressed,
    this.actions = const <Widget>[],
  }) : assert(title != null && title.length > 0),
    assert(leadingIconData != null),
    assert(actions != null),
    preferredSize = const Size.fromHeight(kToolbarHeight),
    super(key: key);

  final String title;
  final Widget leading;
  final IconData leadingIconData;
  final Function leadingOnPressed;
  final List<Widget> actions;

  @override
  final Size preferredSize; // default is 56.0

  @override
  Widget build(BuildContext context) {
    return AppBar(
      title: Text(
        this.title,
      ),
      centerTitle: true,
      leading: this.leading ?? IconButton(
        icon: Icon(
          this.leadingIconData,
        ),
        onPressed: this.leadingOnPressed ?? () {
          Navigator.of(context).pop();
        },
      ),
      actions: <Widget>[
        ...this.actions,
      ],
    );
  }
}
