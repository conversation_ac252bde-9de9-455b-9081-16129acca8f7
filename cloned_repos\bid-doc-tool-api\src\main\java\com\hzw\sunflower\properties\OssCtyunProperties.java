package com.hzw.sunflower.properties;


import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 */
@Data
@Configuration
public class OssCtyunProperties {

    @Value("${oss.ctyun.endpoint:oos-js.ctyunapi.cn}")
    private String endpoint ;

    @Value("${oss.ctyun.accessKeyId:}")
    private String accessKeyId ;

    @Value("${oss.ctyun.accessKeySecret:}")
    private String accessKeySecret;

    @Value("${oss.ctyun.bucketName:}")
    private String bucketName;

}
