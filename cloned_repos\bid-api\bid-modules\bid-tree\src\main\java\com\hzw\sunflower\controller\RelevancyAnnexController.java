package com.hzw.sunflower.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hzw.sunflower.common.BaseController;
import com.hzw.sunflower.common.annotation.RepeatSubmit;
import com.hzw.sunflower.util.BeanListUtil;
import com.hzw.sunflower.common.Paging;
import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.constant.MessageConstants;
import com.hzw.sunflower.constant.constantenum.AnnexTypeEnum;
import com.hzw.sunflower.controller.request.RelevancyAnnexREQ;
import com.hzw.sunflower.controller.response.BidFileInfo;
import com.hzw.sunflower.dto.AnnexDTO;
import com.hzw.sunflower.dto.RelevancyAnnexDTO;
import com.hzw.sunflower.entity.OssFile;
import com.hzw.sunflower.entity.ProjectBidNotice;
import com.hzw.sunflower.entity.RelevancyAnnex;
import com.hzw.sunflower.entity.condition.RelevancyAnnexCondition;
import com.hzw.sunflower.service.OssFileService;
import com.hzw.sunflower.service.ProjectBidNoticeService;
import com.hzw.sunflower.service.RelevancyAnnexService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * RelevancyAnnexController
 */
@Api(tags = "附件服务")
@RestController
@RequestMapping("/relevancyAnnex")
public class RelevancyAnnexController extends BaseController {
    @Autowired
    private RelevancyAnnexService relevancyAnnexService;
    @Autowired
    private ProjectBidNoticeService tProjectBidNoticeService;
    @Autowired
    private OssFileService ossFileService;

    
    @ApiOperation(value = "根据条件分页查询RelevancyAnnex列表")
    @ApiImplicitParam(name = "tRelevancyAnnexREQ", value = "用户表 查询条件", required = true, dataType = "RelevancyAnnexREQ", paramType = "body")
    @PostMapping("/list")
    public Paging<RelevancyAnnex> list(@RequestBody RelevancyAnnexREQ relevancyAnnexREQ) {
        RelevancyAnnexCondition condition = BeanListUtil.convert(relevancyAnnexREQ, RelevancyAnnexCondition.class);
        IPage<RelevancyAnnex> page = relevancyAnnexService.findInfoByCondition(condition);
        return Paging.buildPaging(page);
    }


    @ApiOperation(value = "根据主键ID查询RelevancyAnnex信息")
    @ApiImplicitParam(name = "id", value = "主键ID", required = true)
    @GetMapping(value = "/get/{id}")
    public Result<RelevancyAnnex> get(@PathVariable Long id) {
        if (id == null) {
            return Result.failed(MessageConstants.GET_NULL);
        }
        RelevancyAnnex tRelevancyAnnex = relevancyAnnexService.getInfoById(id);
        return Result.ok(tRelevancyAnnex);
    }

    @ApiOperation(value = "根据招标公告ID查询附件信息")
    @ApiImplicitParam(name = "noticeId", value = "招标公告ID", required = true)
    @GetMapping(value = "/getByNoticeId/{noticeId}")
    public Result<List<AnnexDTO>> getByNoticeId(@PathVariable Long noticeId) {
        List<AnnexDTO> list=relevancyAnnexService.findNoticeAnnexsByNoticeId(noticeId);
        ProjectBidNotice notice = tProjectBidNoticeService.getById(noticeId);
        OssFile ossFile = ossFileService.getOssFileById(notice.getNoticeOssKey());
        if (ossFile!=null){
            AnnexDTO annexDTO=new AnnexDTO();
            annexDTO.setId(ossFile.getId());
            annexDTO.setKey(ossFile.getOssFileKey());
            annexDTO.setName(ossFile.getOssFileName());
            list.add(annexDTO);
        }
        return Result.ok(list);
    }
    
    @ApiOperation(value = "根据招标文件ID查询附件信息")
    @ApiImplicitParam(name = "docId", value = "招标文件ID", required = true)
    @GetMapping(value = "/getByDocId/{docId}")
    public Result<List<BidFileInfo>> getByDocId(@PathVariable Long docId) {
        if (docId == null) {
            return Result.failed(MessageConstants.GET_NULL);
        }
        List<RelevancyAnnexDTO> dtoList = relevancyAnnexService.getInfoByDocId(docId);
        if (CollectionUtil.isEmpty(dtoList)) {
            return Result.ok(new ArrayList<>());
        }
        //类型转换成前端定义数据格式
        Map<Long, BidFileInfo> map = new HashMap<>();
        Map<Long, String> numberMap = new HashMap<>();
        Map<Long, String> sectionIdMap = new HashMap<>();
        Map<Long, String> annexIdMap = new HashMap<>();
        dtoList.stream().forEach((e) -> {
            if (null == map.get(e.getFileId())) {
                numberMap.put(e.getFileId(), e.getPackageNumber() + "");
                sectionIdMap.put(e.getFileId(), e.getSectionId().toString());
                annexIdMap.put(e.getFileId(), e.getId().toString());
            } else {
                numberMap.put(e.getFileId(), numberMap.get(e.getFileId()) + "、" + e.getPackageNumber());
                sectionIdMap.put(e.getFileId(), sectionIdMap.get(e.getFileId()) + "、" + e.getSectionId());
                annexIdMap.put(e.getFileId(), annexIdMap.get(e.getFileId()) + "、" + e.getId());
            }
            BidFileInfo bidFileInfo = new BidFileInfo();
            bidFileInfo.setKey(e.getOssFileKey());
            bidFileInfo.setName(e.getOssFileName());
            bidFileInfo.setFileType(AnnexTypeEnum.BIDDOC.getValue());
            bidFileInfo.setFileId(e.getFileId());
            bidFileInfo.setAmount(new String[]{numberMap.get(e.getFileId())});
            bidFileInfo.setSectionIdArray(sectionIdMap.get(e.getFileId()).split("、"));
            bidFileInfo.setAnnexIdArray(annexIdMap.get(e.getFileId()).split("、"));
            map.put(e.getFileId(), bidFileInfo);
        });
        List<BidFileInfo> collect = map.values().stream().collect(Collectors.toList());
        return Result.ok(collect);
    }

    @ApiOperation(value = "根据主键ID删除RelevancyAnnex表 ")
    @ApiImplicitParam(name = "id", value = "主键ID", required = true)
    @DeleteMapping(value = "/delete/{id}")
    @RepeatSubmit
    public Result<Boolean> delete(@PathVariable Long id) {
        if (id == null) {
            return Result.failed(MessageConstants.DELETE_NULL);
        }
        Boolean bool = relevancyAnnexService.deleteById(id);
        return Result.okOrFailed(bool);
    }

    @ApiOperation(value = "根据主键ID列表批量删除RelevancyAnnex表 ")
    @ApiImplicitParam(name = "idList", value = "主键ID列表", required = true, allowMultiple = true, paramType = "body")
    @DeleteMapping("/deleteList")
    @RepeatSubmit
    public Result<Boolean> deleteList(@RequestBody List<Long> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return Result.failed(MessageConstants.DELETE_NULL);
        }
        Boolean bool = relevancyAnnexService.deleteByIds(idList);
        return Result.okOrFailed(bool);
    }
}
