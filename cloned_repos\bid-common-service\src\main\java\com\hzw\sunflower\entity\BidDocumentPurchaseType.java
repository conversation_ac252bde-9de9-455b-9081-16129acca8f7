package com.hzw.sunflower.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.hzw.sunflower.common.BaseBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 开评标备案采购方式展示表实体类
 *
 * <AUTHOR>
 * @since 2021-07-08 19:14:12
 */
@ApiModel(description = "开评标备案采购方式展示表")
@TableName("t_bid_document_purchase_type")
@Data
public class BidDocumentPurchaseType extends BaseBean implements Serializable {
    private static final long serialVersionUID = 801203439537486936L;

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "标段包id")
    private String sectionId;

    @ApiModelProperty(value = "项目id")
    private Long projectId;

    @ApiModelProperty(value = "招标阶段 1：第一轮  2：第二轮")
    private Integer bidRound;

    @ApiModelProperty(value = "1 依法必招  2 非依法必招  3 政府采购  4 国际招标")
    private Integer purchaseType;

    @ApiModelProperty(value = "采购方式")
    private String purchaseMode;

    @ApiModelProperty(value = "采购方式中文名称")
    private String purchaseModeName;

    @ApiModelProperty(value = "1-预审公开 2-后审公开 3-邀请 4-二阶段 5-政府公开 6-政府邀请")
    private Integer purchaseStatus;

    @ApiModelProperty("评标次数")
    private Integer evaluationCount;
}