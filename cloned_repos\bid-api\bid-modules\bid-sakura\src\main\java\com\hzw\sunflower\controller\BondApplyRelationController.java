package com.hzw.sunflower.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hzw.sunflower.common.BaseController;
import com.hzw.sunflower.common.Paging;
import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.common.annotation.RepeatSubmit;
import com.hzw.sunflower.constant.constantenum.ApplyRelationStatusEnum;
import com.hzw.sunflower.controller.request.BondApplyRelationReq;
import com.hzw.sunflower.controller.response.BondApplyRelationVo;
import com.hzw.sunflower.entity.BondApplyRelation;
import com.hzw.sunflower.entity.condition.BondApplyRelationCondition;
import com.hzw.sunflower.exception.ExceptionEnum;
import com.hzw.sunflower.exception.SunFlowerException;
import com.hzw.sunflower.service.BondApplyRelationService;
import com.hzw.sunflower.service.CommonOpenService;
import com.hzw.sunflower.util.BeanListUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 保证金申请关联 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-27
 */
@Api(tags = "保证金申请关联")
@RestController
@RequestMapping("/bondApplyRelation")
public class BondApplyRelationController extends BaseController {

    @Autowired
    private BondApplyRelationService bondApplyRelationService;

    @Autowired
    private CommonOpenService commonOpenService;
    /**
     * 保证金申请关联
     * projectId
     * sectionId
     * companyId
     * content
     * payCompanyName  付款户名
     * id  编辑的时候
     * @return
     */
    @ApiOperation(value = "保证金申请关联")
    @ApiImplicitParam(name = "req", value = "保证金申请关联", required = true, dataType = "BondApplyRelationReq", paramType = "body")
    @PostMapping("/applyRelation")
    @RepeatSubmit
    public Result applyRelation(@RequestBody BondApplyRelationReq req) {
        //验证线下是否存在退还记录
        List<Long> sectionIds = new ArrayList<>();
        sectionIds.add(req.getSectionId());
        if(commonOpenService.checkOnlineBondRefund(sectionIds,req.getCompanyId())){
            throw new SunFlowerException(ExceptionEnum.HAVE_OFFLINE_BOND_REFUND, ExceptionEnum.HAVE_OFFLINE_BOND_REFUND.getMessage());
        }
        BondApplyRelation relation = BeanListUtil.convert(req,BondApplyRelation.class);
        relation.setStatus(ApplyRelationStatusEnum.WAIT_HANDLE.getType());
        relation.setApplyTime(new Date());
        String paymentVoucherIds = req.getFileIds().stream().map(String::valueOf).collect(Collectors.joining(","));
        relation.setPaymentVoucherIds(paymentVoucherIds);
        Boolean result = bondApplyRelationService.saveOrUpdate(relation);
        return Result.okOrFailed(result);
    }

    @ApiOperation(value = "关联申请列表分页查询(包含异常关联)")
    @ApiImplicitParam(name = "condition", value = "保证金关联申请列表查询条件", required = true, dataType = "BondApplyRelationCondition", paramType = "body")
    @PostMapping(value = "/getBondApplyRelationPage")
    public Result<Paging<BondApplyRelationVo>> getBondApplyRelationPage(@RequestBody BondApplyRelationCondition condition) {
        IPage<BondApplyRelationVo> page = bondApplyRelationService.getBondApplyRelationPage(condition, getJwtUser());
        return Result.ok(Paging.buildPaging(page));
    }

}
