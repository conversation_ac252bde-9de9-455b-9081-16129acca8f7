package com.hzw.ssm.sys.project.action;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.PrintWriter;
import java.text.SimpleDateFormat;
import java.util.*;

import javax.servlet.http.HttpSession;

import com.hzw.ssm.expert.entity.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.hssf.util.Region;
import org.apache.poi.ss.usermodel.Font;
import org.apache.struts2.ServletActionContext;
import org.apache.struts2.convention.annotation.Action;
import org.apache.struts2.convention.annotation.Namespace;
import org.apache.struts2.convention.annotation.ParentPackage;
import org.apache.struts2.convention.annotation.Result;
import org.apache.struts2.convention.annotation.Results;
import org.dom4j.DocumentException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import com.hzw.ssm.expert.service.AppraiseService;
import com.hzw.ssm.expert.service.IllegalExtractioninfoService;
import com.hzw.ssm.fw.base.BaseAction;
import com.hzw.ssm.fw.util.DateUtil;
import com.hzw.ssm.fw.util.SysConstants;
import com.hzw.ssm.sys.project.entity.ConditionEntity;
import com.hzw.ssm.sys.project.entity.ExtractRecordEntity;
import com.hzw.ssm.sys.project.entity.ProjectEntity;
import com.hzw.ssm.sys.project.service.ProjectService;
import com.hzw.ssm.sys.user.entity.UserEntity;
import com.hzw.ssm.util.empty.EmptyUtils;
import com.hzw.ssm.util.file.FileUtils;
import com.hzw.ssm.util.pdf.PdfUtils;
import com.hzw.ssm.util.string.StringUtils;



/**
 * 已抽取项目
 * 
 * <AUTHOR>
 * 
 */
@SuppressWarnings("deprecation")
@Namespace("/alreadyProject")
@ParentPackage(value = "default")
@Results( { @Result(name = "toProjectAlreadyList", location = "/jsp/project/projectAlreadytList.jsp"),
		@Result(name = "toProjectAppointDetail", location = "/jsp/project/projectAppointDetail.jsp"),
		@Result(name = "toProjectAlreadyDetail", location = "/jsp/project/projectAlreadyDetail.jsp"),
		@Result(name = "toAppraiseDetail", location = "/jsp/expert/appraiseDetail.jsp") })
public class ProjectAlreadyAction extends BaseAction {

	private static final long serialVersionUID = 7202413559101105893L;
	
	//@Value("${FilePath}")
	//private String filePath;
	
	@Value("${PdfFileName}")
	private String pdfFileName;

	@Autowired
	private ProjectService projectService;
	@Autowired
	private IllegalExtractioninfoService monitorService;

	@Autowired
	private AppraiseService appraiseService;
	private String queryCount;
	private ProjectEntity projectEntity;
	/** 抽取条件 */
	private ConditionEntity conditionEntity;

	private List<IllegalExtractioninfoEntity> Illegallist;
	/** 项目列表 */
	private List<ProjectEntity> projectList;

	/** 抽取条件列表 */
	private List<ConditionEntity> conditionList;

	/** 指定专家列表 */
	private List<ExpertInfoEntity> expertInfoList;

	/** 抽取记录 */
	private List<ExtractRecordEntity> exRecordList;

	private ExpertInfoEntity expertInfoEntity;

	private List<Appraise> appraiseList;

	/** 评分项 */
	private List<AppraiseInfo> appInfoList;

	/** 专家评价备注 */
	private AppraiseRemark appraiseRemark;

	private String extractResultId;
	
	private String operateType;
	private List<ProjectEntity> queryWarnList;
	/**
	 * 查询已抽取项目列表
	 * 
	 * @return
	 */
	@Action("queryProjectList")
	public String queryProjectList() {
		this.context();
		// 当前session
		HttpSession session = getRequest().getSession();
		// 当前用户信息
		UserEntity user = (UserEntity) session.getAttribute("userInfo");
		try {
			if (null == projectEntity)
				projectEntity = new ProjectEntity();
			projectEntity.setPage(this.getPage());
			//projectEntity.setStatus(SysConstants.PROJECT_STATUS.ALREADY);
			projectEntity.setStas(SysConstants.PROJECT_STATUS.ALREADYLIST);
			projectEntity.setCreateUser(user.getUser_id());
			projectList = projectService.queryPageProjectList(projectEntity, user);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return "toProjectAlreadyList";
	}

	/**
	 * 查询已抽取指定明细
	 * 
	 * @return
	 */
	@Action("toProjectDetail")
	public String toProjectDetail() {
		Long method = projectEntity.getMethod();
		String returnStr = "toProjectAppointDetail";
		// 查询项目明细
		projectList = projectService.queryProjectListById(projectEntity);
		projectEntity = projectList.get(0);
		if (null == conditionEntity)
			conditionEntity = new ConditionEntity();

		conditionEntity.setProjectId(projectEntity.getProjectId());
		if(operateType!=null && operateType!=""){
			conditionEntity.setJoin_status(2L);     //未通知
		}else{
			conditionEntity.setJoin_status(0L);     // 参标
		}
		conditionEntity.setJoinStatus("0");
		expertInfoList = projectService.queryExtractedResult(conditionEntity);

		// 查询参评专家信息
		if (null != method && 4L != method) {
			exRecordList = projectService.queryExtractRecordList(projectEntity.getProjectId());
			returnStr = "toProjectAlreadyDetail";
		}

		return returnStr;
	}

	/**
	 * 查看专家评价信息
	 * 
	 * @return
	 * @throws Exception
	 */
	@Action("toAppraiseDetail")
	public String toAppraiseDetail() throws Exception {
		// 专家信息
		expertInfoEntity = appraiseService.queryBaseAppraise(extractResultId);
		// 查询评分项
		appInfoList = appraiseService.queryAppraiseInfoById("0");
		// 查询评分
		appraiseList = appraiseService.queryAppraiseDetail(extractResultId,"0");
		// 评价备注
		appraiseRemark = appraiseService.queryAppraiseRemark(extractResultId);
		return "toAppraiseDetail";
	}

	/**
	 * 导出抽取记录Excel
	 * @return
	 */
	@Action("exportRecordExcel")
	public String exportRecordExcel() {
		this.context();
		this.getResponse().setCharacterEncoding("utf-8");
		PrintWriter out = null;
		try {
			out = this.getResponse().getWriter();
			
			if(projectEntity == null){
				projectEntity = new ProjectEntity();
			}
			
			if (null == conditionEntity){
				conditionEntity = new ConditionEntity();
			}
			if(projectEntity.getProjectId() != null && !"".equals(projectEntity.getProjectId())){
				// 项目明细
				projectEntity = projectService.queryProjectById(projectEntity);
				
				// 专家信息
				conditionEntity = new ConditionEntity();
				conditionEntity.setProjectId(projectEntity.getProjectId());
				// 抽取条件
				conditionList = projectService.queryConditionList(conditionEntity);
				
				conditionEntity.setOrderFlag("1");
				expertInfoList = projectService.queryExtractedResult(conditionEntity);
				
				// 抽取时间
				String extractTime  = projectService.queryMinExtractTime(conditionList.get(0).getId());
				
				HSSFWorkbook wb = new HSSFWorkbook();  //--->创建了一个excel文件  
		        HSSFSheet sheet = wb.createSheet("评标专家抽取记录表");   //--->创建了一个工作簿  
		        
		        sheet.setColumnWidth((short)0, 7* 256);
		        sheet.setColumnWidth((short)2, 18* 256);
		        sheet.setColumnWidth((short)4, 12* 256);
		        sheet.setColumnWidth((short)5, 12* 256);
		        sheet.setColumnWidth((short)6, 15* 256);
		        sheet.setColumnWidth((short)7, 15* 256);
		        sheet.setColumnWidth((short)9, 10* 256);
		        //样式1  
		        HSSFCellStyle style = wb.createCellStyle(); // 样式对象  
		        style.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);// 垂直  
		        style.setAlignment(HSSFCellStyle.ALIGN_CENTER);// 水平  
		        //设置标题字体格式  
		        Font font = wb.createFont();     
		        //设置字体样式   
		        font.setFontHeightInPoints((short)15);   //--->设置字体大小  
		        font.setFontName("黑体");   //---》设置字体，是什么类型例如：宋体  
		        style.setFont(font);     //--->将字体格式加入到style1中    
		        style.setWrapText(true);
		        
		        //样式2  
		        HSSFCellStyle style1 = wb.createCellStyle(); // 样式对象  
		        style1.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);// 垂直  
		        style1.setAlignment(HSSFCellStyle.ALIGN_CENTER);// 水平  
		        //设置标题字体格式  
		        Font font1 = wb.createFont();     
		        //设置字体样式   
		        font1.setFontHeightInPoints((short)20);   //--->设置字体大小  
		        font1.setFontName("黑体");   //---》设置字体，是什么类型例如：宋体  
		        style1.setFont(font1);     //--->将字体格式加入到style1中    
		        style1.setWrapText(true);
		        
		        //样式3  
		        HSSFCellStyle style2 = wb.createCellStyle(); // 样式对象  
		        style2.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);// 垂直  
		        style2.setAlignment(HSSFCellStyle.ALIGN_CENTER);// 水平  
		        //设置标题字体格式  
		        Font font2 = wb.createFont();     
		        //设置字体样式   
		        font2.setFontHeightInPoints((short)12);   //--->设置字体大小  
		        font2.setFontName("宋体");   //---》设置字体，是什么类型例如：宋体  
		        style2.setFont(font2);     //--->将字体格式加入到style1中    
		        style2.setWrapText(true);   //设置是否能够换行，能够换行为true  
		        style2.setBorderBottom((short)1);   //设置下划线，参数是黑线的宽度  
		        style2.setBorderLeft((short)1);   //设置左边框  
		        style2.setBorderRight((short)1);   //设置有边框  
		        style2.setBorderTop((short)1);   //设置下边框  
		        style2.setWrapText(true);
		        
		        //样式4  
		        HSSFCellStyle style3 = wb.createCellStyle(); // 样式对象  
		        style3.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);// 垂直  
		        style3.setAlignment(HSSFCellStyle.ALIGN_CENTER);// 水平  
		        //设置标题字体格式  
		        Font font3 = wb.createFont();     
		        //设置字体样式   
		        font3.setFontHeightInPoints((short)10);   //--->设置字体大小  
		        font3.setFontName("宋体");   //---》设置字体，是什么类型例如：宋体  
		        style3.setFont(font3);     //--->将字体格式加入到style1中    
		        style3.setWrapText(true);   //设置是否能够换行，能够换行为true  
		        style3.setBorderBottom((short)1);   //设置下划线，参数是黑线的宽度  
		        style3.setBorderLeft((short)1);   //设置左边框  
		        style3.setBorderRight((short)1);   //设置有边框  
		        style3.setBorderTop((short)1);   //设置下边框 
		        style3.setWrapText(true);
		        
		        //样式5  
		        HSSFCellStyle style4 = wb.createCellStyle(); // 样式对象  
		        style4.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);// 垂直  
		        style4.setAlignment(HSSFCellStyle.ALIGN_LEFT);// 水平  
		        //设置标题字体格式  
		        Font font4 = wb.createFont();     
		        //设置字体样式   
		        font4.setFontHeightInPoints((short)10);   //--->设置字体大小  
		        font4.setFontName("宋体");   //---》设置字体，是什么类型例如：宋体  
		        style4.setFont(font4);     //--->将字体格式加入到style1中    
		        style4.setWrapText(true);   //设置是否能够换行，能够换行为true  
		        style4.setBorderBottom((short)1);   //设置下划线，参数是黑线的宽度  
		        style4.setBorderLeft((short)1);   //设置左边框  
		        style4.setBorderRight((short)1);   //设置有边框  
		        style4.setBorderTop((short)1);   //设置下边框 
		        style4.setWrapText(true);
		        
		        //表格第一行  
		        HSSFRow row1 = sheet.createRow(0);   //--->创建一行  
		        // 四个参数分别是：起始行，起始列，结束行，结束列  
		        sheet.addMergedRegion(new Region(0, (short) 0, 0, (short) 9));  
		        row1.setHeightInPoints(30);  
		        HSSFCell cell1 = row1.createCell((short)0);   //--->创建一个单元格  
		        cell1.setCellStyle(style);  
		        cell1.setCellValue("江苏省招标中心有限公司"); 
		        
		        //表格第二行  
		        sheet.addMergedRegion(new Region(1,(short)0,1,(short)9));  
		        HSSFRow row2 = sheet.createRow(1);  
		        row2.setHeightInPoints(60);  
		        HSSFCell cell2 = row2.createCell((short)0);  
		        cell2.setCellValue("评 标 专 家 抽 取 记 录 表");  
		        cell2.setCellStyle(style1);  
		        
		        //表格第三行  
		        sheet.addMergedRegion(new Region(2,(short)0,2,(short)9));  
		        HSSFRow row3 = sheet.createRow(2);  
		        row3.setHeightInPoints((short)20);  
		        HSSFCell cell3 = row3.createCell((short)0);  
		        cell3.setCellValue("项 目 基 本 情 况");  
		        cell3.setCellStyle(style2);  
		        row3.createCell((short)1).setCellStyle(style2);
		        row3.createCell((short)2).setCellStyle(style2);
		        row3.createCell((short)3).setCellStyle(style2);
		        row3.createCell((short)4).setCellStyle(style2);
		        row3.createCell((short)5).setCellStyle(style2);
		        row3.createCell((short)6).setCellStyle(style2);
		        row3.createCell((short)7).setCellStyle(style2);
		        row3.createCell((short)8).setCellStyle(style2);
		        row3.createCell((short)9).setCellStyle(style2);
		        
		        //表格第四行  
		        sheet.addMergedRegion(new Region(3, (short)0, 3, (short)1));  
		        sheet.addMergedRegion(new Region(3, (short)2, 3, (short)9));  
		        HSSFRow row4 = sheet.createRow(3);  
		        row4.setHeightInPoints((short)20);  
		        HSSFCell cell4 = row4.createCell((short)0);  
		        cell4.setCellStyle(style3);  
		        cell4.setCellValue("项目名称");  
		        row4.createCell((short)1).setCellStyle(style2);
		        row4.createCell((short)2).setCellStyle(style2);
		        row4.createCell((short)3).setCellStyle(style2);
		        row4.createCell((short)4).setCellStyle(style2);
		        row4.createCell((short)5).setCellStyle(style2);
		        row4.createCell((short)6).setCellStyle(style2);
		        row4.createCell((short)7).setCellStyle(style2);
		        row4.createCell((short)8).setCellStyle(style2);
		        row4.createCell((short)9).setCellStyle(style2);
		        
		        HSSFCell cell4_1 = row4.createCell((short)2);  
		        cell4_1.setCellStyle(style4);  
		        cell4_1.setCellValue(projectEntity.getProjectName());  
		        
		        //表格第五行  
		        sheet.addMergedRegion(new Region(4, (short)0, 4, (short)1));  
		        sheet.addMergedRegion(new Region(4, (short)2, 4, (short)9));  
		        HSSFRow row5 = sheet.createRow(4);  
		        row5.setHeightInPoints((short)20);  
		        HSSFCell cell5 = row5.createCell((short)0);  
		        cell5.setCellStyle(style3);  
		        cell5.setCellValue("委托单位");  
		        row5.createCell((short)1).setCellStyle(style2);
		        row5.createCell((short)2).setCellStyle(style2);
		        row5.createCell((short)3).setCellStyle(style2);
		        row5.createCell((short)4).setCellStyle(style2);
		        row5.createCell((short)5).setCellStyle(style2);
		        row5.createCell((short)6).setCellStyle(style2);
		        row5.createCell((short)7).setCellStyle(style2);
		        row5.createCell((short)8).setCellStyle(style2);
		        row5.createCell((short)9).setCellStyle(style2);
		        HSSFCell cell5_1 = row5.createCell((short)2);  
		        cell5_1.setCellStyle(style4);  
		        cell5_1.setCellValue(projectEntity.getTender());  
		        
		        //表格第六行  
		        sheet.addMergedRegion(new Region(5, (short)0, 5, (short)1));  
		        sheet.addMergedRegion(new Region(5, (short)2, 5, (short)5)); 
		        sheet.addMergedRegion(new Region(5, (short)6, 5, (short)7)); 
		        sheet.addMergedRegion(new Region(5, (short)8, 5, (short)9)); 
		        HSSFRow row6 = sheet.createRow(5);  
		        row6.setHeightInPoints((short)20);  
		        HSSFCell cell6 = row6.createCell((short)0);  
		        cell6.setCellStyle(style3);  
		        cell6.setCellValue("项目编号");  
		        HSSFCell cell6_1 = row6.createCell((short)2); 
		        cell6_1.setCellStyle(style3);  
		        cell6_1.setCellValue(projectEntity.getProjectNo()); 
		        HSSFCell cell6_2 = row6.createCell((short)6);  
		        cell6_2.setCellStyle(style3);  
		        cell6_2.setCellValue("项目负责人");
		        HSSFCell cell6_3 = row6.createCell((short)8);  
		        cell6_3.setCellStyle(style3);  
		        cell6_3.setCellValue(projectEntity.getManager());
		        row6.createCell((short)1).setCellStyle(style3);
		        row6.createCell((short)3).setCellStyle(style3);
		        row6.createCell((short)4).setCellStyle(style3);
		        row6.createCell((short)5).setCellStyle(style3);
		        row6.createCell((short)7).setCellStyle(style3);
		        row6.createCell((short)9).setCellStyle(style3);
		        
		        //表格第七行  
		        sheet.addMergedRegion(new Region(6, (short)0, 6, (short)1));  
		        sheet.addMergedRegion(new Region(6, (short)2, 6, (short)5)); 
		        sheet.addMergedRegion(new Region(6, (short)6, 6, (short)7)); 
		        sheet.addMergedRegion(new Region(6, (short)8, 6, (short)9)); 
		        HSSFRow row7 = sheet.createRow(6);  
		        row7.setHeightInPoints((short)20);  
		        HSSFCell cell7 = row7.createCell((short)0);  
		        cell7.setCellStyle(style3);  
		        cell7.setCellValue("开标时间");  
		        HSSFCell cell7_1 = row7.createCell((short)2); 
		        cell7_1.setCellStyle(style3);  
		        cell7_1.setCellValue(DateUtil.getFormatDateTime("yyyy-MM-dd HH:mm:ss", projectEntity.getBidTime())); 
		        HSSFCell cell7_2 = row7.createCell((short)6);  
		        cell7_2.setCellStyle(style3);  
		        cell7_2.setCellValue("开标地点");
		        HSSFCell cell7_3 = row7.createCell((short)8);  
		        cell7_3.setCellStyle(style3);  
		        cell7_3.setCellValue(projectEntity.getBidAddress());  
		        row7.createCell((short)1).setCellStyle(style3);
		        row7.createCell((short)3).setCellStyle(style3);
		        row7.createCell((short)4).setCellStyle(style3);
		        row7.createCell((short)5).setCellStyle(style3);
		        row7.createCell((short)7).setCellStyle(style3);
		        row7.createCell((short)9).setCellStyle(style3);
		        
		        //表格第八行  
		        sheet.addMergedRegion(new Region(7, (short)0, 7, (short)9));  
		        HSSFRow row8 = sheet.createRow(7);  
		        row8.setHeightInPoints((short)20);  
		        HSSFCell cell8 = row8.createCell((short)0);  
		        cell8.setCellStyle(style2);  
		        cell8.setCellValue("专 家 抽 取 情 况");  
		        row8.createCell((short)1).setCellStyle(style2);  
		        row8.createCell((short)2).setCellStyle(style2);  
		        row8.createCell((short)3).setCellStyle(style2);  
		        row8.createCell((short)4).setCellStyle(style2);  
		        row8.createCell((short)5).setCellStyle(style2);  
		        row8.createCell((short)6).setCellStyle(style2);  
		        row8.createCell((short)7).setCellStyle(style2);  
		        row8.createCell((short)8).setCellStyle(style2);
		        row8.createCell((short)9).setCellStyle(style2);
		        
		        //表格第九行  
		        sheet.addMergedRegion(new Region(8, (short)0, 8, (short)1));  
		        sheet.addMergedRegion(new Region(8, (short)2, 8, (short)5)); 
		        sheet.addMergedRegion(new Region(8, (short)6, 8, (short)7)); 
		        sheet.addMergedRegion(new Region(8, (short)8, 8, (short)9)); 
		        HSSFRow row9 = sheet.createRow(8);  
		        row9.setHeightInPoints((short)30);  
		        HSSFCell cell9 = row9.createCell((short)0);  
		        cell9.setCellStyle(style3);  
		        cell9.setCellValue("专业");  
		        HSSFCell cell9_1 = row9.createCell((short)2); 
		        cell9_1.setCellStyle(style4);  
		        cell9_1.setCellValue(null != conditionList.get(0).getExpertTypeName() && !"".equals(conditionList.get(0).getExpertTypeName())?conditionList.get(0).getExpertTypeName():""); 
		        HSSFCell cell9_2 = row9.createCell((short)6);  
		        cell9_2.setCellStyle(style3);  
		        cell9_2.setCellValue("抽取人数");
		        HSSFCell cell9_3 = row9.createCell((short)8);  
		        cell9_3.setCellStyle(style3);  
		        if(null != conditionList.get(0).getTotal() && !"".equals(conditionList.get(0).getTotal())){
		        	cell9_3.setCellValue(conditionList.get(0).getTotal());  
		        }else{
		        	cell9_3.setCellValue("");  
		        }
		        row9.createCell((short)1).setCellStyle(style3);
		        row9.createCell((short)3).setCellStyle(style3);
		        row9.createCell((short)4).setCellStyle(style3);
		        row9.createCell((short)5).setCellStyle(style3);
		        row9.createCell((short)7).setCellStyle(style3);
		        row9.createCell((short)9).setCellStyle(style3);
		        
		        //表格第十行  
		        sheet.addMergedRegion(new Region(9, (short)0, 9, (short)1));  
		        sheet.addMergedRegion(new Region(9, (short)2, 9, (short)5)); 
		        sheet.addMergedRegion(new Region(9, (short)6, 9, (short)7)); 
		        sheet.addMergedRegion(new Region(9, (short)8, 9, (short)9)); 
		        HSSFRow row10 = sheet.createRow(9);  
		        row10.setHeightInPoints((short)20);  
		        HSSFCell cell10 = row10.createCell((short)0);  
		        cell10.setCellStyle(style3);  
		        cell10.setCellValue("抽取时间");  
		        HSSFCell cell10_1 = row10.createCell((short)2); 
		        cell10_1.setCellStyle(style3);  
		        cell10_1.setCellValue(extractTime); 
		        HSSFCell cell10_2 = row10.createCell((short)6);  
		        cell10_2.setCellStyle(style3);  
		        cell10_2.setCellValue("项目负责人");
		        HSSFCell cell10_3 = row10.createCell((short)8);  
		        cell10_3.setCellStyle(style3);  
		        cell10_3.setCellValue("");  
		        row10.createCell((short)1).setCellStyle(style3);
		        row10.createCell((short)3).setCellStyle(style3);
		        row10.createCell((short)4).setCellStyle(style3);
		        row10.createCell((short)5).setCellStyle(style3);
		        row10.createCell((short)7).setCellStyle(style3);
		        row10.createCell((short)9).setCellStyle(style3);
		        
		        //表格第十一行  
		        sheet.addMergedRegion(new Region(10, (short)0, 10, (short)1));  
		        sheet.addMergedRegion(new Region(10, (short)2, 10, (short)5)); 
		        sheet.addMergedRegion(new Region(10, (short)6, 10, (short)7)); 
		        sheet.addMergedRegion(new Region(10, (short)8, 10, (short)9)); 
		        HSSFRow row11 = sheet.createRow(10);  
		        row11.setHeightInPoints((short)20);  
		        HSSFCell cell11 = row11.createCell((short)0);  
		        cell11.setCellStyle(style3);  
		        cell11.setCellValue("监抽人");  
		        HSSFCell cell11_1 = row11.createCell((short)2); 
		        cell11_1.setCellStyle(style3);  
		        cell11_1.setCellValue(""); 
		        HSSFCell cell11_2 = row11.createCell((short)6);  
		        cell11_2.setCellStyle(style3);  
		        cell11_2.setCellValue("申请人");
		        HSSFCell cell11_3 = row11.createCell((short)8);  
		        cell11_3.setCellStyle(style3);  
		        cell11_3.setCellValue("");  
		        row11.createCell((short)1).setCellStyle(style3);
		        row11.createCell((short)3).setCellStyle(style3);
		        row11.createCell((short)4).setCellStyle(style3);
		        row11.createCell((short)5).setCellStyle(style3);
		        row11.createCell((short)7).setCellStyle(style3);
		        row11.createCell((short)9).setCellStyle(style3);
		        
		        //表格第十三行  
		        sheet.addMergedRegion(new Region(12, (short)0, 12, (short)9)); 
		        HSSFRow row13 = sheet.createRow(12);  
		        row13.setHeightInPoints((short)20);  
		        HSSFCell cell13 = row13.createCell((short)0);  
		        cell13.setCellValue("抽 取 信 息");  
		        cell13.setCellStyle(style2);  
		        row13.createCell((short)1).setCellStyle(style2);
		        row13.createCell((short)2).setCellStyle(style2);
		        row13.createCell((short)3).setCellStyle(style2);
		        row13.createCell((short)4).setCellStyle(style2);
		        row13.createCell((short)5).setCellStyle(style2);
		        row13.createCell((short)6).setCellStyle(style2);
		        row13.createCell((short)7).setCellStyle(style2);
		        row13.createCell((short)8).setCellStyle(style2);
		        row13.createCell((short)9).setCellStyle(style2);
		        
		        //表格第十四行  
		        sheet.addMergedRegion(new Region(13, (short)2, 13, (short)3)); 
		        HSSFRow row14 = sheet.createRow(13);  
		        row14.setHeightInPoints((short)25);  
		        HSSFCell cell14_1 = row14.createCell((short)0);  
		        cell14_1.setCellValue("序号");  
		        cell14_1.setCellStyle(style3);  
		        HSSFCell cell14_2 = row14.createCell((short)1);  
		        cell14_2.setCellValue("姓名");  
		        cell14_2.setCellStyle(style3); 
		        HSSFCell cell14_3 = row14.createCell((short)2);  
		        cell14_3.setCellValue("单位");  
		        cell14_3.setCellStyle(style3); 
		        row14.createCell((short)3).setCellStyle(style3); 
		        HSSFCell cell14_4 = row14.createCell((short)4);  
		        cell14_4.setCellValue("职务");  
		        cell14_4.setCellStyle(style3);
		        HSSFCell cell14_5 = row14.createCell((short)5);  
		        cell14_5.setCellValue("职称");  
		        cell14_5.setCellStyle(style3);
		        HSSFCell cell14_6 = row14.createCell((short)6);  
		        cell14_6.setCellValue("单位电话");  
		        cell14_6.setCellStyle(style3);
		        HSSFCell cell14_7 = row14.createCell((short)7);  
		        cell14_7.setCellValue("手机");  
		        cell14_7.setCellStyle(style3);
		        HSSFCell cell14_8 = row14.createCell((short)8);  
		        cell14_8.setCellValue("状态");  
		        cell14_8.setCellStyle(style3);
		        //HSSFCell cell14_9 = row14.createCell((short)9);  
		        //cell14_9.setCellValue("轮次");  
		        //cell14_9.setCellStyle(style3);
		        HSSFCell cell14_10 = row14.createCell((short)9);  
		        cell14_10.setCellValue("备注");  
		        cell14_10.setCellStyle(style3);
		        int j = 14;
		        for (int i = 0; i < expertInfoList.size(); i++) {
		        	ExpertInfoEntity eie = expertInfoList.get(i);
		        	sheet.addMergedRegion(new Region(j, (short)2, j, (short)3)); 
		            HSSFRow row = sheet.createRow(j);  
		            row.setHeightInPoints((short)25);  
		            HSSFCell cell0_0 = row.createCell((short)0); 
		            cell0_0.setCellValue(i+1);  
		            cell0_0.setCellStyle(style3);  
		            HSSFCell cell0_1 = row.createCell((short)1); 
		            cell0_1.setCellValue(eie.getUser_name());  
		            cell0_1.setCellStyle(style3); 
		            HSSFCell cell0_2 = row.createCell((short)2); 
		            cell0_2.setCellValue(eie.getCompany());  
		            cell0_2.setCellStyle(style3); 
		            row.createCell((short)3).setCellStyle(style3); 
		            HSSFCell cell0_4 = row.createCell((short)4); 
		            cell0_4.setCellValue(eie.getPosition());  
		            cell0_4.setCellStyle(style3); 
		            HSSFCell cell0_5 = row.createCell((short)5); 
		            cell0_5.setCellValue(eie.getTitalName()); 
		           
		            cell0_5.setCellStyle(style3); 
		            HSSFCell cell0_6 = row.createCell((short)6); 
		            cell0_6.setCellValue(eie.getCompany_phone());  
		            cell0_6.setCellStyle(style3); 
		            HSSFCell cell0_7 = row.createCell((short)7); 
		            cell0_7.setCellValue(eie.getMobilephone());  
		            cell0_7.setCellStyle(style3); 
		            HSSFCell cell0_8 = row.createCell((short)8); 
		            if(eie.getJoin_status()==0){
		            	cell0_8.setCellValue("参加");  
		            }else if(eie.getJoin_status()==1){
		            	cell0_8.setCellValue("不参加");  
		            }else if(eie.getJoin_status()==2){
		            	cell0_8.setCellValue("未通知");  
		            }else if(eie.getJoin_status()==3){
		            	cell0_8.setCellValue("短信已通知");  
		            }
		            cell0_8.setCellStyle(style3); 
		            //HSSFCell cell0_9 = row.createCell((short)9); 
		            //cell0_9.setCellValue("第"+eie.getSort_no()+"轮");  
		            //cell0_9.setCellStyle(style3); 
		            HSSFCell cell0_10 = row.createCell((short)9); 
		            if(eie.getJoin_status()==1){
		            	if("其他".equals(eie.getReason())){
		            		cell0_10.setCellValue(eie.getReason()+"-"+eie.getQt_reason()); 
		            	}else{
		            		cell0_10.setCellValue(eie.getReason()); 
		            	}
		            }else{
		            	cell0_10.setCellValue("");  
		            }
		            cell0_10.setCellStyle(style3); 
		            j++;
				}
		        
		        String savePath = ServletActionContext.getServletContext().getRealPath("")+ "/"+SysConstants.FILE_PATH_ROOT+"zjcqjl/";
				File f1 = new File(savePath+projectEntity.getProjectId());
				if (!f1.exists()) {//
				    f1.mkdirs();
				}
				String newName="评标专家抽取记录表_"+System.currentTimeMillis()+".xls";
		        FileOutputStream fileOut = null;  
		        try{              
		            fileOut = new FileOutputStream(savePath+projectEntity.getProjectId()+"/"+newName);  
		            wb.write(fileOut);  
		        }catch(Exception e){  
		        	out.print("error");
		        	log.error(e);
		        }  
		        finally{  
		            if(fileOut != null){  
		                try {  
		                    fileOut.close();  
		                    //out.print("zjcqjl/"+projectEntity.getProjectId());
		                    //newName=new String(newName.getBytes("GB-2312"),"UTF-8");
		                    out.print("{\"filePath\":\"" +"/"+SysConstants.FILE_PATH_ROOT+"zjcqjl/"+projectEntity.getProjectId() + "\"," + "\"fileName\":\"" + newName + "\"}");
		                    out.close();
		                } catch (IOException e) {  
		                	out.print("error");
		                }  
		            }  
		        }  
			}
		} catch (Exception e) {
			out.print("error");
		}
		return null;
	}
	
	/**
	 * 函数功能描述：专家抽取记录表导出（由原来的导出Excel改为了PDF）
	 * @return
	 */
	@SuppressWarnings("unchecked")
	@Action("exportRecordPdf")
	public String exportRecordPdf() {
		PrintWriter out = null;
		String htmlPath="";
		try {
			this.context();
			this.getResponse().setCharacterEncoding("utf-8");
			out = this.getResponse().getWriter();
			Map<String,Object> dataMap = new HashMap<String,Object>();
			dataMap.put("decimationBatch", projectEntity.getDecimationBatch());
			queryExtractedRecordInfo(dataMap);
			//发布路径
			String path = ServletActionContext.getServletContext().getRealPath("");
			//模板文件路径
			String templetePath = path + "\\WEB-INF\\templete\\choose\\";
			//判断当前抽取类型
			//专业
			List<ConditionEntity> conditionLst = (List<ConditionEntity>)dataMap.get("conditionList");
			//项目信息模板文件路径
			String projectInfoPath =null;
			//项目信息模板文件内容
			String projectInfoTmpl =null;
			//专家信息模板文件路径
			String expertInfoPath =null;
			//专家信息模板文件内容
			String expertInfoTmpl = null;
			//抽取记录模板文件路径
			String chooseRecordPath = null;
			//抽取记录模板文件内容
			String chooseRecordTmpl =null;
			if(conditionLst.get(0).getMethod()==null||conditionLst.get(0).getMethod().toString().equals("4")) {
				templetePath = FileUtils.replaceSeparator(templetePath);
				//项目信息模板文件路径
				projectInfoPath = templetePath + "projectInfo2.html";
				//项目信息模板文件内容
				projectInfoTmpl = PdfUtils.readContent(projectInfoPath, "UTF-8");
				
				//专家信息模板文件路径
				 expertInfoPath = templetePath + "expertInfo2.html";
				//专家信息模板文件内容
				expertInfoTmpl = PdfUtils.readContent(expertInfoPath, "UTF-8");

				//抽取记录模板文件路径
				chooseRecordPath = templetePath + "chooseRecord2.html";
				//抽取记录模板文件内容
				chooseRecordTmpl = PdfUtils.readContent(chooseRecordPath, "UTF-8");
			}else {
				templetePath = FileUtils.replaceSeparator(templetePath);
				
				//项目信息模板文件路径
				projectInfoPath = templetePath + "projectInfo.html";
				//项目信息模板文件内容
				projectInfoTmpl = PdfUtils.readContent(projectInfoPath, "UTF-8");
				
				//专家信息模板文件路径
				 expertInfoPath = templetePath + "expertInfo.html";
				//专家信息模板文件内容
				expertInfoTmpl = PdfUtils.readContent(expertInfoPath, "UTF-8");
				
				//抽取记录模板文件路径
				chooseRecordPath = templetePath + "chooseRecord.html";
				//抽取记录模板文件内容
				chooseRecordTmpl = PdfUtils.readContent(chooseRecordPath, "UTF-8");
			}
			String openTime = "";
			String openAddress = "";
			String purpose = null;
			//项目信息
			List<ProjectEntity> projectList = (List<ProjectEntity>)dataMap.get("projectList");
			String projectInfo = "";
			String manage = "";
			String userName="";
			if(!EmptyUtils.isEmpty(projectList)){
				for(ProjectEntity entity : projectList){
					manage  = entity.getManager();
					userName = entity.getUserName();
					String tmpProInfo = projectInfoTmpl;
					String projectName = entity.getProjectName();
					tmpProInfo = StringUtils.replace(tmpProInfo, "${projectName}", projectName);
					
					String projectNum = entity.getProjectNo();
					tmpProInfo = StringUtils.replace(tmpProInfo, "${projectNum}", projectNum);
					
					String tenderOrg = entity.getTender();
					tmpProInfo = StringUtils.replace(tmpProInfo, "${tenderOrg}", tenderOrg);
					projectInfo += tmpProInfo;
					
					if(!EmptyUtils.isEmpty(entity.getBidTime())){
						openTime = DateUtil.getFormatDateTime("yyyy-MM-dd HH:mm:ss", entity.getBidTime());
					}
					openAddress = entity.getBidAddress();
					if(entity.getPurpose()!=null&&entity.getPurpose()==2){
						purpose = "文件评审";
					}else if(entity.getPurpose()!=null&&entity.getPurpose()==3){
						purpose = "资格预审";
					}else{
						purpose = "评标专家";
					}
				}
			}
			chooseRecordTmpl = StringUtils.replace(chooseRecordTmpl, "${projectInfo}", projectInfo);
			//开标时间
			chooseRecordTmpl = StringUtils.replace(chooseRecordTmpl, "${openTime}", openTime);
			//项目负责人
			chooseRecordTmpl = StringUtils.replace(chooseRecordTmpl, "${manage}", manage);
			//开标地点
			chooseRecordTmpl = StringUtils.replace(chooseRecordTmpl, "${openAddress}", openAddress);	
			chooseRecordTmpl = StringUtils.replace(chooseRecordTmpl, "${purpose}", purpose);
			//评标时间
			chooseRecordTmpl = StringUtils.replace(chooseRecordTmpl, "${bidTime}", openTime);
			
			//专业
			List<ConditionEntity> conditionList = (List<ConditionEntity>)dataMap.get("conditionList");
			String major = conditionList.get(0).getShowExpertType();
			chooseRecordTmpl = StringUtils.replace(chooseRecordTmpl, "${major}", major);
			
			String extractTime = (String)dataMap.get("extractTime");
			chooseRecordTmpl = StringUtils.replace(chooseRecordTmpl, "${extractTime}", extractTime);
			
			//抽取人数
			String chooseNum ="";
			//抽取数量为空或者指定专家类型时，抽取人数为空
			if(EmptyUtils.isEmpty(conditionList.get(0).getTotal())||conditionLst.get(0).getMethod()==null||conditionLst.get(0).getMethod().toString().equals("4")) {
				chooseNum = "";
			}else {
				chooseNum= conditionList.get(0).getTotal().toString();
			}
			chooseRecordTmpl = StringUtils.replace(chooseRecordTmpl, "${chooseNum}", chooseNum + "");
			
			//监抽人
			chooseRecordTmpl = StringUtils.replace(chooseRecordTmpl, "${regulator}", "");
			
			//申请人
			chooseRecordTmpl = StringUtils.replace(chooseRecordTmpl, "${proposer}", userName);
			
			//专家信息
			List<ExpertInfoEntity> expertInfoList = (List<ExpertInfoEntity>)dataMap.get("expertInfoList");
			String expertInfo = "";
			if(!EmptyUtils.isEmpty(expertInfoList)){
				int counter = 1;
				for(ExpertInfoEntity entity : expertInfoList){
					String tmpExpInfo = expertInfoTmpl;
					
					//序号
					tmpExpInfo = StringUtils.replace(tmpExpInfo, "${no}", counter + "");
					
					//专家姓名
					String expertName = entity.getUser_name();
					tmpExpInfo = StringUtils.replace(tmpExpInfo, "${expertName}", expertName);
					
					//单位
					String orgName = entity.getCompany();
					tmpExpInfo = StringUtils.replace(tmpExpInfo, "${orgName}", orgName);
					
					//职务
					String position = entity.getPosition();
					tmpExpInfo = StringUtils.replace(tmpExpInfo, "${position}", position);
					
					//职称
					String techTitalName = getTechTitalName(entity);
					tmpExpInfo = StringUtils.replace(tmpExpInfo, "${techTitalName}", techTitalName);
					
					//办公电话
					String officePhone = entity.getCompany_phone();
					tmpExpInfo = StringUtils.replace(tmpExpInfo, "${officePhone}", officePhone);
					
					String mobilePhone = entity.getMobilephone();
					tmpExpInfo = StringUtils.replace(tmpExpInfo, "${mobilePhone}", mobilePhone);
					
					long joinStatus = entity.getJoin_status();
					String status = "";
					if(joinStatus==0){
						status = "参加";  
		            }else if(joinStatus==1){
		            	status = "不参加";  
		            }else if(joinStatus==2){
		            	status = "未通知";  
		            }else if(joinStatus==3){
		            	status = "短信已通知";  
		            }
					tmpExpInfo = StringUtils.replace(tmpExpInfo, "${status}", status);
					
					String remark = "";
					if(joinStatus==1){
		            	if("其他".equals(entity.getReason())){
		            		if(entity.getQt_reason() ==null||entity.getQt_reason().isEmpty()||"".equals(entity.getQt_reason())) {
		            			remark = entity.getReason();
		            		}else {
		            			remark = entity.getReason()+"-"+entity.getQt_reason();
		            		}
		            	}else{
		            		remark = entity.getReason();
		            	}
		            }
					tmpExpInfo = StringUtils.replace(tmpExpInfo, "${remark}", remark);
					
					expertInfo += tmpExpInfo;
					counter++;
				}
				
				chooseRecordTmpl = StringUtils.replace(chooseRecordTmpl, "${expertInfo}", expertInfo);
			}
			
			String savePath = ServletActionContext.getServletContext().getRealPath("") + File.separator+pdfFileName;
			savePath = FileUtils.replaceSeparator(savePath);
			
			String fileName = "评标专家抽取记录表_"+System.currentTimeMillis();
			htmlPath = savePath + "/" + fileName + ".html";
			FileUtils.writeFileWithEncoding(htmlPath, chooseRecordTmpl, "UTF-8");
			
			String pdfPath = savePath + "/" + fileName + ".pdf";
			String footerContent = "抽取批次：" + projectEntity.getDecimationBatch();
			PdfUtils.parsePdfByContent(chooseRecordTmpl, pdfPath, "UTF-8",footerContent);
           // relativePath = relativePath.replaceAll("\\\\", "/");
			
			//System.out.println("AAA");
            out.print("{\"filePath\":\"" +pdfFileName + "\"," + "\"fileName\":\"" + (fileName + ".pdf") + "\"}");
		}catch(Exception e){
			e.printStackTrace();
			out.print("error");
		}finally{
			//删除html文件
			File file = new File(htmlPath);
            file.delete();
		}
		
		out.close();
		
		return null;
	}
	
	/**
	 * 函数功能描述：查询专家抽取相关信息
	 */
	private void queryExtractedRecordInfo(Map<String,Object> map){
		if(projectEntity == null){
			projectEntity = new ProjectEntity();
		}

		if (null == conditionEntity){
			conditionEntity = new ConditionEntity();
		}

		//合并后的项目信息集合
		List<ProjectEntity> projectList = new ArrayList<ProjectEntity>();
		// 项目明细（TODO 查询条件projectEntity中的抽取批次decimationBatch必须有值，此处需要完善）
		List<ProjectEntity> projectEntityList = projectService.queryProjectListById(projectEntity);
		//项目ID
		String projectId = projectEntity.getProjectId();
		String join_status = projectEntity.getJoin_status();
		if(!EmptyUtils.isEmpty(projectEntityList)){
			//合并后的项目集合
			Map<String,ProjectEntity> projectMap = new HashMap<String,ProjectEntity>();
			//项目以及标段的对应关系集合
			Map<String,List<Integer>> subMap = new HashMap<String,List<Integer>>();
			//遍历所有项目
			for(ProjectEntity entity : projectEntityList){
				if(null == projectId){
					//取第一个项目ID
					projectId = entity.getProjectId();
				}

				String projectNo = entity.getProjectNo();
				if(projectNo.contains("/")){
					//有标段项目，项目编号与标段号以"/"分隔，例如：JITC-1805CC0063/1，JITC-1805CC0063/1，JITC-1805CC0063/1
					String[] proNumArr = projectNo.split("/");
					if(subMap.containsKey(proNumArr[0])){
						//根据项目编号取出对应的标段号
						List<Integer> subNoList = subMap.get(proNumArr[0]);
						if(!subNoList.contains(Integer.valueOf(proNumArr[1]))){
							subNoList.add(Integer.valueOf(proNumArr[1]));
							subMap.put(proNumArr[0], subNoList);
						}
					}else{
						//保存该项目的标段号
						List<Integer> subNoList = new ArrayList<Integer>();
						subNoList.add(Integer.parseInt(proNumArr[1]));
						subMap.put(proNumArr[0], subNoList);
					}

					if(!projectMap.containsKey(proNumArr[0])){
						//保存当前项目
						projectMap.put(proNumArr[0], entity);
					}
				}else{
					//无标段项目
					subMap.put(projectNo, new ArrayList<Integer>());
					if(!projectMap.containsKey(projectNo)){
						//保存当前项目
						projectMap.put(projectNo, entity);
					}
				}
			}

			//遍历过滤掉了编号相同的项目后的集合中的所有项目
			for(String proKey : projectMap.keySet()){
				//遍历所有项目对应的标段号集合
				for(String subKey : subMap.keySet()){
					if(proKey.equals(subKey)){
						ProjectEntity project = projectMap.get(proKey);
						List<Integer> subNoList = subMap.get(subKey);
						if(!EmptyUtils.isEmpty(subNoList)){
							//排序
							Collections.sort(subNoList);
							String projectNum = proKey;
							for(Integer subNo : subNoList){
								//将项目编号和标段号组合在一起，以"/"分隔
								projectNum += "/" + subNo;
							}
							//将原来的项目编号替换成组合后的
							project.setProjectNo(projectNum);
						}
						projectList.add(project);
					}
				}
			}
		}

		map.put("projectList", projectList);

		if(null != projectId){
			// 专家信息
			conditionEntity = new ConditionEntity();
			conditionEntity.setProjectId(projectId);

			conditionEntity.setJoinStatus(join_status);

			// 抽取条件
			conditionList = projectService.queryConditionList(conditionEntity);
			map.put("conditionList", conditionList);

			conditionEntity.setOrderFlag("1");
		//	conditionEntity.setJoinStatus("0");
			expertInfoList = projectService.queryExtractedResult(conditionEntity);
			map.put("expertInfoList", expertInfoList);

			// 抽取时间
			String extractTime  = projectService.queryMinExtractTime(conditionList.get(0).getId());
			map.put("extractTime", extractTime);
		}
	}
	
	private String getTechTitalName(ExpertInfoEntity entity){
		return entity.getTitalName();
	}
	
	@SuppressWarnings("unchecked")
	@Action("downWarnInfoPDF")
	public void downWarnInfoPDF(){
			PrintWriter out = null;
			try {
				this.context();
				this.getResponse().setCharacterEncoding("utf-8");
				out = this.getResponse().getWriter();
				// 表单所用到的数据
				Map<String,Object> dataMap = new HashMap<String,Object>();
				dataMap.put("sjCount", projectEntity.getSjCount());
				dataMap.put("bidTime", projectEntity.getBidTime());
				queryWarnRecordInfo(dataMap);
				// 发布路径
				String path = ServletActionContext.getServletContext().getRealPath("");
				// 模板文件路径
				String templetePath = path + "\\WEB-INF\\templete\\choose\\";
				//抽取记录模板文件路径
				String chooseRecordPath = null;
				//抽取记录模板文件内容
				String chooseRecordTmpl =null;
				//专家信息模板文件路径
				String expertInfoPath =null;
				//专家信息模板文件内容
				String expertInfoTmpl = null;
				templetePath = FileUtils.replaceSeparator(templetePath);
				//抽取记录模板文件路径
				chooseRecordPath = templetePath + "warnInfo.html";
				//抽取记录模板文件内容
				chooseRecordTmpl = PdfUtils.readContent(chooseRecordPath, "UTF-8");

				//专家信息模板文件路径
				expertInfoPath = templetePath + "warnInfo2.html";
				//专家信息模板文件内容
				expertInfoTmpl = PdfUtils.readContent(expertInfoPath, "UTF-8");

				List<ProjectEntity> queryWarnList = (List<ProjectEntity>) dataMap.get("queryWarnList");
				String expertInfo = "";
				if(!EmptyUtils.isEmpty(queryWarnList)){
					int counter = 1;
					for(ProjectEntity entity : queryWarnList){
						String tmpExpInfo = expertInfoTmpl;
						//抽取批次号
						tmpExpInfo = StringUtils.replace(tmpExpInfo, "${decimationBatch}", entity.getDecimationBatch());
						//项目编号
						tmpExpInfo = StringUtils.replace(tmpExpInfo, "${projectNo}", entity.getProjectNo());
						//项目名称
						tmpExpInfo = StringUtils.replace(tmpExpInfo, "${projectName}", entity.getProjectName());
						//项目负责人
						tmpExpInfo = StringUtils.replace(tmpExpInfo, "${manager}", entity.getManager());
						//评标时间
						tmpExpInfo = StringUtils.replace(tmpExpInfo, "${bidTime}", DateUtil.dateToString2(entity.getBidTime()));
						//开标地点
						tmpExpInfo = StringUtils.replace(tmpExpInfo, "${bidAddress}", entity.getBidAddress());
						//专家人数
						tmpExpInfo = StringUtils.replace(tmpExpInfo, "${zjCount}", String.valueOf(entity.getZjCount()));
						//实际抽取人数
						tmpExpInfo = StringUtils.replace(tmpExpInfo, "${sjCount}", String.valueOf(entity.getSjCount()));
						//抽取次数
						tmpExpInfo = StringUtils.replace(tmpExpInfo, "${cqCount}", String.valueOf(entity.getCqCount()));
						expertInfo += tmpExpInfo;
						counter++;
					}
					chooseRecordTmpl = StringUtils.replace(chooseRecordTmpl, "${expertInfo}", expertInfo);
				}
				String savePath = ServletActionContext.getServletContext().getRealPath("") + File.separator + pdfFileName;
				savePath = FileUtils.replaceSeparator(savePath);
				String fileName = "抽取次数预警记录表";
				String htmlPath = savePath + "/" + fileName + ".html";
				FileUtils.writeFileWithEncoding(htmlPath, chooseRecordTmpl, "UTF-8");

				String pdfPath = savePath + "/" + fileName + ".pdf";
				String footerContent = "";
				PdfUtils.parsePdfByContent(chooseRecordTmpl, pdfPath, "UTF-8", footerContent);
				try {
					// 删除html文件
					File file = new File(htmlPath);
					file.delete();
				} catch (Exception e) {
					e.printStackTrace();
				}
				out.print("{\"filePath\":\"" + pdfFileName + "\"," + "\"fileName\":\"" + (fileName + ".pdf") + "\"}");
			} catch (Exception e) {
				e.printStackTrace();
				out.print("error");
			}
			out.close();
	}
	@SuppressWarnings("unchecked")
	@Action("downMonitorInfoPDF")
	public void downMonitorInfoPDF(){
		PrintWriter out = null;
		try {
			this.context();
			this.getResponse().setCharacterEncoding("utf-8");
			out = this.getResponse().getWriter();
			// 表单所用到的数据
			Map<String,Object> dataMap = new HashMap<String,Object>();
			/*dataMap.put("sjCount", projectEntity.getSjCount());
			dataMap.put("bidTime", projectEntity.getBidTime());*/
			queryMonitorRecordInfo(dataMap);
			// 发布路径
			String path = ServletActionContext.getServletContext().getRealPath("");
			// 模板文件路径
			String templetePath = path + "\\WEB-INF\\templete\\choose\\";
			//抽取记录模板文件路径
			String chooseRecordPath = null;
			//抽取记录模板文件内容
			String chooseRecordTmpl =null;
			//专家信息模板文件路径
			String expertInfoPath =null;
			//专家信息模板文件内容
			String expertInfoTmpl = null;
			templetePath = FileUtils.replaceSeparator(templetePath);
			//抽取记录模板文件路径
			chooseRecordPath = templetePath + "warnInfo.html";
			//抽取记录模板文件内容
			chooseRecordTmpl = PdfUtils.readContent(chooseRecordPath, "UTF-8");
			
			//专家信息模板文件路径
			expertInfoPath = templetePath + "warnInfo2.html";
			//专家信息模板文件内容
			expertInfoTmpl = PdfUtils.readContent(expertInfoPath, "UTF-8");
			
			List<IllegalExtractioninfoEntity> queryWarnList = (List<IllegalExtractioninfoEntity>) dataMap.get("queryWarnList");
			String expertInfo = "";
			if(!EmptyUtils.isEmpty(queryWarnList)){
				int counter = 1;
				for(IllegalExtractioninfoEntity entity : queryWarnList){
					String tmpExpInfo = expertInfoTmpl;
					//抽取批次号
					tmpExpInfo = StringUtils.replace(tmpExpInfo, "${decimationBatch}", entity.getDecimationbatch());
					//项目编号
					tmpExpInfo = StringUtils.replace(tmpExpInfo, "${projectNo}", entity.getProjectCode());
					//项目名称
					tmpExpInfo = StringUtils.replace(tmpExpInfo, "${projectName}", entity.getProjectName());
					//项目负责人
					tmpExpInfo = StringUtils.replace(tmpExpInfo, "${manager}", entity.getUserName());
					//处室
					tmpExpInfo = StringUtils.replace(tmpExpInfo, "${departmentName}", entity.getDepartmentName());
					//评标时间
					tmpExpInfo = StringUtils.replace(tmpExpInfo, "${bidTime}", DateUtil.dateToString2(entity.getOpeningTime()));
					//开标地点
					tmpExpInfo = StringUtils.replace(tmpExpInfo, "${bidAddress}", entity.getOpeningAddress());
					//专家人数
					tmpExpInfo = StringUtils.replace(tmpExpInfo, "${zjCount}", String.valueOf(entity.getNeedNum()));
					//实际抽取人数
					tmpExpInfo = StringUtils.replace(tmpExpInfo, "${sjCount}", String.valueOf(entity.getExtractNum()));
					//抽取次数
					tmpExpInfo = StringUtils.replace(tmpExpInfo, "${cqCount}", String.valueOf(entity.getExtractTimes()));
					//是否备案
					tmpExpInfo = StringUtils.replace(tmpExpInfo, "${isHandle}", "40".equals(entity.getIsHandle())?"是":"否");
					expertInfo += tmpExpInfo;
					counter++;
				}
				chooseRecordTmpl = StringUtils.replace(chooseRecordTmpl, "${expertInfo}", expertInfo);
			}
			String savePath = ServletActionContext.getServletContext().getRealPath("") + File.separator + pdfFileName;
			savePath = FileUtils.replaceSeparator(savePath);
			String fileName = "专家抽取预警记录表";
			String htmlPath = savePath + "/" + fileName + ".html";
			FileUtils.writeFileWithEncoding(htmlPath, chooseRecordTmpl, "UTF-8");
			
			String pdfPath = savePath + "/" + fileName + ".pdf";
			String footerContent = "";
			PdfUtils.parsePdfByContent(chooseRecordTmpl, pdfPath, "UTF-8", footerContent);
			try {
				// 删除html文件
				File file = new File(htmlPath);
				file.delete();
			} catch (Exception e) {
				e.printStackTrace();
			}
			out.print("{\"filePath\":\"" + pdfFileName + "\"," + "\"fileName\":\"" + (fileName + ".pdf") + "\"}");
		} catch (Exception e) {
			e.printStackTrace();
			out.print("error");
		}
		out.close();
	}
	public void queryMonitorRecordInfo(Map<String,Object> map){
		IllegalExtractioninfoEntity info=new IllegalExtractioninfoEntity();
		try {
			if (null==projectEntity.getQueryCount()){
				queryCount="3";
				info.setExtractTimes(queryCount);
			}else{
				info.setExtractTimes(projectEntity.getQueryCount().toString());
			}
			this.context();
			// 当前session
			HttpSession session = getRequest().getSession();
			// 当前用户信息
			UserEntity user = (UserEntity) session.getAttribute("userInfo");
			//处长
			if("20150122103034522026".equals(user.getRole_id())) {
				info.setDepartmentName(user.getDepartment());
				info.setIllRoleId("0000");
			}
			
			info.setIsHandle(projectEntity.getIshandle());
			info.setBeginTime(projectEntity.getBidStartTime());
			info.setEndTime(projectEntity.getBidEndTime());
			info.setPaixu(projectEntity.getPaixu());
			//根据抽取批次查询对应的项目信息
			Illegallist = monitorService.queryILLMonitor(info);
		}catch (Exception e){
			e.printStackTrace();
		}
		map.put("queryWarnList",Illegallist);
	}
	public void queryWarnRecordInfo(Map<String,Object> map){
		if(projectEntity == null){
			projectEntity = new ProjectEntity();
		}
		try {
			if (null==projectEntity.getQueryCount()){
				queryCount="4";
				projectEntity.setQueryCount(Integer.valueOf(queryCount));
			}
			//根据抽取批次查询对应的项目信息
			queryWarnList = projectService.queryPDFProjectInfo(projectEntity);
		}catch (Exception e){
			e.printStackTrace();
		}
		map.put("queryWarnList",queryWarnList);
	}

	/*public static void main(String[] args) {
        Map<String,String> map = new HashMap();
        map.put("name","张三");
        map.put("creatdate","2018年1月1日");
        map.put("weather","晴朗");
        map.put("sports","打羽毛球");

        Map<String,Object> o=new HashMap();
        o.put("datemap",map);
        pdfout(o);
    }*/
	// 利用模板生成pdf
   /* public static void pdfout(Map<String,Object> o) {
        // 模板路径
//        String templatePath = "C:/mytest.pdf";
    	//模板文件路径
//		String templatePath = path + "\\WEB-INF\\templete\\choose\\";
        // 生成的新文件路径
        String templatePath = "C:/testout1.pdf";
		String savePath = ServletActionContext.getServletContext().getRealPath("")
				+ File.separator+pdfFileName;
		String fileName = "评标专家抽取记录表_"+System.currentTimeMillis();
		String pdfPath = savePath + "/" + fileName + ".pdf";
		String footerContent = "抽取批次：" + projectEntity.getDecimationBatch();
		String chooseRecordTmpl = PdfUtils.readContent(chooseRecordPath, "UTF-8");
		PdfUtils.parsePdfByContent(chooseRecordTmpl, pdfPath, "UTF-8",footerContent);
        PdfReader reader;
        FileOutputStream out;
        ByteArrayOutputStream bos;
        PdfStamper stamper;
        try {
            BaseFont bf = BaseFont.createFont("c://windows//fonts//simsun.ttc,1" , BaseFont.IDENTITY_H, BaseFont.EMBEDDED);
            Font FontChinese = new Font(bf, 5, Font.NORMAL);
            out = new FileOutputStream(newPDFPath);// 输出流
            reader = new PdfReader(templatePath);// 读取pdf模板
            bos = new ByteArrayOutputStream();
            stamper = new PdfStamper(reader, bos);
            AcroFields form = stamper.getAcroFields();
            //文字类的内容处理
            Map<String,String> datemap = (Map<String,String>)o.get("datemap");
            form.addSubstitutionFont(bf);
            for(String key : datemap.keySet()){
                String value = datemap.get(key);
                form.setField(key,value);
            }
            stamper.setFormFlattening(true);// 如果为false，生成的PDF文件可以编辑，如果为true，生成的PDF文件不可以编辑
            stamper.close();
            Document doc = new Document();
            Font font = new Font(bf, 32);
            PdfCopy copy = new PdfCopy(doc, out);
            doc.open();
            PdfImportedPage importPage = copy.getImportedPage(new PdfReader(bos.toByteArray()), 1);
            copy.addPage(importPage);
            doc.close();

        } catch (IOException e) {
            System.out.println(e);
        } catch (DocumentException e) {
            System.out.println(e);
        }
}*/
	public ProjectEntity getProjectEntity() {
		return projectEntity;
	}

	public void setProjectEntity(ProjectEntity projectEntity) {
		this.projectEntity = projectEntity;
	}

	public List<ProjectEntity> getProjectList() {
		return projectList;
	}

	public void setProjectList(List<ProjectEntity> projectList) {
		this.projectList = projectList;
	}

	public ConditionEntity getConditionEntity() {
		return conditionEntity;
	}

	public void setConditionEntity(ConditionEntity conditionEntity) {
		this.conditionEntity = conditionEntity;
	}

	public List<ConditionEntity> getConditionList() {
		return conditionList;
	}

	public void setConditionList(List<ConditionEntity> conditionList) {
		this.conditionList = conditionList;
	}

	public List<ExpertInfoEntity> getExpertInfoList() {
		return expertInfoList;
	}

	public void setExpertInfoList(List<ExpertInfoEntity> expertInfoList) {
		this.expertInfoList = expertInfoList;
	}

	public List<ExtractRecordEntity> getExRecordList() {
		return exRecordList;
	}

	public void setExRecordList(List<ExtractRecordEntity> exRecordList) {
		this.exRecordList = exRecordList;
	}

	public ExpertInfoEntity getExpertInfoEntity() {
		return expertInfoEntity;
	}

	public void setExpertInfoEntity(ExpertInfoEntity expertInfoEntity) {
		this.expertInfoEntity = expertInfoEntity;
	}

	public List<Appraise> getAppraiseList() {
		return appraiseList;
	}

	public void setAppraiseList(List<Appraise> appraiseList) {
		this.appraiseList = appraiseList;
	}

	public List<AppraiseInfo> getAppInfoList() {
		return appInfoList;
	}

	public void setAppInfoList(List<AppraiseInfo> appInfoList) {
		this.appInfoList = appInfoList;
	}

	public AppraiseRemark getAppraiseRemark() {
		return appraiseRemark;
	}

	public void setAppraiseRemark(AppraiseRemark appraiseRemark) {
		this.appraiseRemark = appraiseRemark;
	}

	public String getExtractResultId() {
		return extractResultId;
	}

	public void setExtractResultId(String extractResultId) {
		this.extractResultId = extractResultId;
	}
	public String getOperateType() {
		return operateType;
	}

	public void setOperateType(String operateType) {
		this.operateType = operateType;
	}

}
