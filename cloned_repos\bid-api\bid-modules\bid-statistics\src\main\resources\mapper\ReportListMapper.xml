<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hzw.sunflower.dao.ReportListMapper" >

    <select id="findInfoByCondition" resultType="com.hzw.sunflower.entity.ReportList">
    SELECT
        s.id,
        u.user_name,
        c.company_name,
        p.purchase_name as project_name,
        p.purchase_number,
        p.created_time,
        s.package_number,
        s.entrust_money  entrustedAmount,
        s.entrust_currency,
        s.purchase_mode_name,
        s.submit_end_time,
        s.purchase_type,
        p.is_international
    FROM
        t_project_bid_section s
        LEFT JOIN t_project p ON s.project_id = p.id
        LEFT JOIN (
        SELECT
            u.id,
            u.user_name,
            e.project_id
        FROM
            t_user u
            LEFT JOIN t_project_entrust_user e ON e.user_id = u.id
            LEFT JOIN r_user_department rud on e.user_id = rud.user_id and e.department_id = rud.department_id
        WHERE
            e.type = 1
            AND e.is_delete = 0
            AND u.is_delete = 0
        ) u ON u.project_id = s.project_id
        LEFT JOIN (
        SELECT
            distinct
            c.company_name,
            e.project_id
        FROM
            t_company c
            LEFT JOIN t_project_entrust_user e ON e.company_id = c.id
            LEFT JOIN r_user_department rud on e.user_id = rud.user_id and e.department_id = rud.department_id
        WHERE
            e.type = 0
            AND e.is_delete = 0
            AND c.is_delete = 0
        ) c ON c.project_id = s.project_id
        LEFT JOIN t_project_entrust_user tpe ON p.id = tpe.project_id
        LEFT JOIN t_department d ON d.id = tpe.department_id
    WHERE
        s.is_delete = 0
        AND p.is_delete = 0
        AND tpe.type = 1
        <if test="condition.keyWords !=null and condition.keyWords !=''">
        AND (p.purchase_name like concat('%',#{condition.keyWords},'%')
				OR p.purchase_number like concat('%',#{condition.keyWords},'%') )
        </if>
        <if test="condition.userName !=null and condition.userName !=''">
            AND u.user_name like concat('%',#{condition.userName},'%')
        </if>
        <if test="condition.openingBid != null and condition.openingBid == 1">
            AND unix_timestamp(s.submit_end_time) &lt; unix_timestamp(NOW())
        </if>
        <if test="condition.openingBid != null and condition.openingBid == 2">
            AND unix_timestamp(s.submit_end_time) &gt; unix_timestamp(NOW())
        </if>
        <if test="condition.companyName !=null and condition.companyName !=''">
            AND c.company_name like concat('%',#{condition.companyName},'%')
        </if>
        <if test="condition.deptId !=null and condition.deptId !=''">
            AND d.id = #{condition.deptId}
        </if>
        <if test="condition.processAddress !=null and condition.processAddress !=''">
            AND (p.process_address = #{condition.processAddress}
             OR p.is_process = #{condition.processAddress})
        </if>
        <if test="condition.purchaseType !=null and condition.purchaseType == 924100 ">
            AND s.purchase_type = #{condition.purchaseType}
        </if>
        <if test="condition.isInternational !=null and condition.isInternational !='' and condition.isInternational !=0">
            AND p.is_international = #{condition.isInternational}
        </if>
        <if test="condition.isInternational ==0">
            AND p.is_international = #{condition.isInternational}
        </if>
        <if test="condition.purchaseType !=null and condition.purchaseType != 924100 ">
            AND s.purchase_type != 924100
        </if>
    GROUP BY
        s.id,
        u.user_name,
        c.company_name,
        p.purchase_name,
        p.purchase_number,
        p.created_time,
        s.package_number,
        s.entrust_money,
        s.entrust_currency,
        s.purchase_mode_name,
        s.submit_end_time,
        s.purchase_type,
        p.is_international
        ORDER BY p.created_time DESC
    </select>
    <select id="findConferenceAddress" resultType="java.lang.String">
    SELECT
        b.conference_address
    FROM
        t_conference_booking b
        LEFT JOIN t_conference_booking_project p ON p.booking_id = b.id
    WHERE
        b.booking_purpose = 1
        AND b.is_delete = 0
        AND p.is_delete = 0
        AND P.purchase_num = #{purchaseNumber}
        <if test="packageNum !=null and packageNum != '' ">
            AND FIND_IN_SET(#{packageNum},p.package_nums)
        </if>
    </select>
    <select id="findDeptId" resultType="java.lang.Long">
    SELECT
        d.id
    FROM
        t_department d
    LEFT JOIN r_user_department r ON d.id = r.department_id
    LEFT JOIN t_user u ON u.id = r.user_id
    WHERE
        u.id = #{userId}
        AND d.is_delete = 0
        AND u.is_delete = 0
        AND r.is_delete = 0
    </select>
    <select id="findAllInfo" resultType="com.hzw.sunflower.entity.ReportList">
        SELECT
        u.user_name,
        c.company_name,
        p.purchase_name as project_name,
        p.purchase_number,
        p.international_number,
        s.id,
        s.package_number,
        s.submit_end_time,
        a.seal_time,
        IF(s.`status` = 56,a.submit_time,l.archiveTime) archiveTime,
        a.storage_location,
        a.archive_num,
        a.remark
        FROM
        t_project_bid_section s
        LEFT JOIN t_project p ON s.project_id = p.id
        LEFT JOIN (
        SELECT
        u.id,
        u.user_name,
        e.project_id
        FROM
        t_user u
        LEFT JOIN t_project_entrust_user e ON e.user_id = u.id
        LEFT JOIN r_user_department rud on e.user_id = rud.user_id and e.department_id = rud.department_id
        WHERE
        e.type = 1
        AND e.is_delete = 0
        AND u.is_delete = 0
        ) u ON u.project_id = s.project_id
        LEFT JOIN (
        SELECT
        c.company_name,
        e.project_id
        FROM
        t_company c
        LEFT JOIN t_project_entrust_user e ON e.company_id = c.id
        LEFT JOIN r_user_department rud on e.user_id = rud.user_id and e.department_id = rud.department_id
        WHERE
        e.type = 0
        AND e.is_delete = 0
        AND c.is_delete = 0
        ) c ON c.project_id = s.project_id
        LEFT JOIN t_archive_project a ON s.id = a.section_id
        LEFT JOIN (SELECT MAX(created_time) archiveTime,bid_id FROM t_archive_audit GROUP BY bid_id ) l ON l.bid_id = s.id
        LEFT JOIN t_project_entrust_user tpe ON p.id = tpe.project_id
        LEFT JOIN r_user_department rud on  rud.user_id = u.id AND rud.department_id = tpe.department_id
        WHERE
        s.is_delete = 0
        AND p.is_delete = 0
        AND tpe.type = 1
        AND unix_timestamp( s.submit_end_time ) &lt; unix_timestamp(NOW())
        <if test="condition.startDate !=null and condition.startDate !=''">
            AND DATE_FORMAT( a.seal_time, '%Y-%m-%d' ) &gt;= '${condition.startDate}'
        </if>
        <if test="condition.endDate !=null and condition.endDate !=''">
            AND DATE_FORMAT( a.seal_time, '%Y-%m-%d' ) &lt;= '${condition.endDate}'
        </if>
        <if test="condition.deptId !=null and condition.deptId !=''">
            AND rud.department_id = #{condition.deptId}
        </if>
        <if test="condition.deptId !=null and condition.deptId !=''">
            AND rud.department_id = #{condition.deptId}
        </if>
        <if test="condition.userName !=null and condition.userName !=''">
            AND u.user_name like concat('%',#{condition.userName},'%')
        </if>
        <if test="condition.purchaseNumber !=null and condition.purchaseNumber !=''">
            AND p.purchase_number like concat('%',#{condition.purchaseNumber},'%')
        </if>
        <if test="condition.projectName !=null and condition.projectName !=''">
            AND p.purchase_name like concat('%',#{condition.projectName},'%')
        </if>
        <if test="condition.status != null and condition.status == 1">
            AND (s.`status` &lt; 50 OR s.`status` &gt;= 70)
        </if>
        <if test="condition.status != null and condition.status == 2">
            AND (s.`status` BETWEEN 50 AND 53
            OR s.`status` = 55 OR s.`status` = 57)
        </if>
        <if test="condition.status != null and condition.status == 3">
            AND (s.`status` = 54 OR s.`status` = 56
            OR s.`status` = 58 OR s.`status` = 60)
        </if>
        GROUP BY
        s.id,
        u.user_name,
        c.company_name,
        p.purchase_name,
        p.purchase_number,
        p.international_number,
        s.package_number,
        s.submit_end_time,
        a.seal_time,
        l.archiveTime,
        a.storage_location,
        a.archive_num,
        a.remark ,
        s.`status`
        ORDER BY
        s.submit_end_time ASC
    </select>
    <select id="findInfoByDept" resultType="com.hzw.sunflower.entity.ReportList">
        SELECT
        u.user_name,
        c.company_name,
        p.purchase_name as project_name,
        p.purchase_number,
        p.international_number,
        s.id,
        s.package_number,
        s.submit_end_time,
        a.seal_time,
        IF(s.`status` = 56,a.submit_time,l.archiveTime) archiveTime,
        a.storage_location,
        a.archive_num,
        a.remark
        FROM
        t_project_bid_section s
        LEFT JOIN t_project p ON s.project_id = p.id
        LEFT JOIN (
        SELECT
        u.id,
        u.user_name,
        e.project_id
        FROM
        t_user u
        LEFT JOIN t_project_entrust_user e ON e.user_id = u.id
        LEFT JOIN r_user_department rud on e.user_id = rud.user_id and e.department_id = rud.department_id
        WHERE
        e.type = 1
        AND e.is_delete = 0
        AND u.is_delete = 0
        ) u ON u.project_id = s.project_id
        LEFT JOIN (
        SELECT
        distinct
        c.company_name,
        e.project_id
        FROM
        t_company c
        LEFT JOIN t_project_entrust_user e ON e.company_id = c.id
        LEFT JOIN r_user_department rud on e.user_id = rud.user_id and e.department_id = rud.department_id
        WHERE
        e.type = 0
        AND e.is_delete = 0
        AND c.is_delete = 0
        ) c ON c.project_id = s.project_id
        LEFT JOIN t_archive_project a ON s.id = a.section_id
        LEFT JOIN t_project_entrust_user tpe ON p.id = tpe.project_id
        LEFT JOIN t_department d ON d.id = tpe.department_id
        LEFT JOIN (SELECT MAX(created_time) archiveTime,bid_id FROM t_archive_audit GROUP BY bid_id ) l ON l.bid_id = s.id
        WHERE
        s.is_delete = 0
        AND p.is_delete = 0
        AND tpe.type = 1
        AND unix_timestamp( s.submit_end_time ) &lt; unix_timestamp(NOW())
        AND FIND_IN_SET(d.id,#{deptId})
        <if test="condition.startDate !=null and condition.startDate !=''">
            AND DATE_FORMAT( a.seal_time, '%Y-%m-%d' ) &gt;= '${condition.startDate}'
        </if>
        <if test="condition.endDate !=null and condition.endDate !=''">
            AND DATE_FORMAT( a.seal_time, '%Y-%m-%d' ) &lt;= '${condition.endDate}'
        </if>
        <if test="condition.userName !=null and condition.userName !=''">
            AND u.user_name like concat('%',#{condition.userName},'%')
        </if>
        <if test="condition.purchaseNumber !=null and condition.purchaseNumber !=''">
            AND p.purchase_number like concat('%',#{condition.purchaseNumber},'%')
        </if>
        <if test="condition.projectName !=null and condition.projectName !=''">
            AND p.purchase_name like concat('%',#{condition.projectName},'%')
        </if>
        <if test="condition.status != null and condition.status == 1">
            AND ((s.`status` &lt; 50 OR s.`status` &gt;= 70) or s.`status` = 65)
        </if>
        <if test="condition.status != null and condition.status == 2">
            AND (s.`status` BETWEEN 50 AND 53
            OR s.`status` = 55 OR s.`status` = 57)
        </if>
        <if test="condition.status != null and condition.status == 3">
            AND (s.`status` = 54 OR s.`status` = 56
            OR s.`status` = 58 OR s.`status` = 60 OR s.`status` = 59)
        </if>
        GROUP BY
        s.id,
        u.user_name,
        c.company_name,
        p.purchase_name,
        p.purchase_number,
        p.international_number,
        s.package_number,
        s.submit_end_time,
        a.seal_time,
        l.archiveTime,
        a.storage_location,
        a.archive_num,
        a.remark ,
        s.`status`
        ORDER BY
        s.submit_end_time ASC
    </select>
    <select id="findAllInfoList" resultType="com.hzw.sunflower.controller.response.ReportListVO">
        SELECT
        distinct
        u.user_name,
        c.company_name,
        p.purchase_name as project_name,
        p.purchase_number,
        p.international_number,
        s.id,
        s.package_number,
        s.submit_end_time,
        a.seal_time,
        IF(s.`status` = 56,a.submit_time,l.archiveTime) archiveTime,
        a.storage_location,
        a.archive_num,
        a.remark
        FROM
        t_project_bid_section s
        LEFT JOIN t_project p ON s.project_id = p.id
        LEFT JOIN (
        SELECT
        u.id,
        u.user_name,
        e.project_id
        FROM
        t_user u
        LEFT JOIN t_project_entrust_user e ON e.user_id = u.id
        LEFT JOIN r_user_department rud on e.user_id = rud.user_id and e.department_id = rud.department_id
        WHERE
        e.type = 1
        AND e.is_delete = 0
        AND u.is_delete = 0
        ) u ON u.project_id = s.project_id
        LEFT JOIN (
        SELECT
        distinct
        c.company_name,
        e.project_id
        FROM
        t_company c
        LEFT JOIN t_project_entrust_user e ON e.company_id = c.id
        LEFT JOIN r_user_department rud on e.user_id = rud.user_id and e.department_id = rud.department_id
        WHERE
        e.type = 0
        AND e.is_delete = 0
        AND c.is_delete = 0
        ) c ON c.project_id = s.project_id
        LEFT JOIN t_archive_project a ON s.id = a.section_id
        LEFT JOIN (SELECT MAX(created_time) archiveTime,bid_id FROM t_archive_audit GROUP BY bid_id ) l ON l.bid_id = s.id
        LEFT JOIN r_user_department rud on  rud.user_id = u.id
        WHERE
        s.is_delete = 0
        AND p.is_delete = 0
        AND unix_timestamp( s.submit_end_time ) &lt; unix_timestamp(NOW())
        <if test="condition.startDate !=null and condition.startDate !=''">
            AND DATE_FORMAT( a.seal_time, '%Y-%m-%d' ) &gt;= '${condition.startDate}'
        </if>
        <if test="condition.endDate !=null and condition.endDate !=''">
            AND DATE_FORMAT( a.seal_time, '%Y-%m-%d' ) &lt;= '${condition.endDate}'
        </if>
        <if test="condition.deptId !=null and condition.deptId !=''">
            AND rud.department_id = #{condition.deptId}
        </if>
        <if test="condition.userName !=null and condition.userName !=''">
            AND u.user_name like concat('%',#{condition.userName},'%')
        </if>
        <if test="condition.purchaseNumber !=null and condition.purchaseNumber !=''">
            AND p.purchase_number like concat('%',#{condition.purchaseNumber},'%')
        </if>
        <if test="condition.projectName !=null and condition.projectName !=''">
            AND p.purchase_name like concat('%',#{condition.projectName},'%')
        </if>
        <if test="condition.status != null and condition.status == 1">
            AND (s.`status` &lt; 50 OR s.`status` &gt;= 70)
        </if>
        <if test="condition.status != null and condition.status == 2">
            AND (s.`status` BETWEEN 50 AND 53
            OR s.`status` = 55 OR s.`status` = 57)
        </if>
        <if test="condition.status != null and condition.status == 3">
            AND (s.`status` = 54 OR s.`status` = 56
            OR s.`status` = 58 OR s.`status` = 60)
        </if>
        GROUP BY
        s.id,
        u.user_name,
        c.company_name,
        p.purchase_name,
        p.purchase_number,
        p.international_number,
        s.package_number,
        s.submit_end_time,
        a.seal_time,
        l.archiveTime,
        a.storage_location,
        a.archive_num,
        s.`status`,
        a.remark
        ORDER BY
        s.submit_end_time ASC
    </select>
    <select id="findInfoListByDept" resultType="com.hzw.sunflower.controller.response.ReportListVO">
        SELECT
        u.user_name,
        c.company_name,
        p.purchase_name as project_name,
        p.purchase_number,
        p.international_number,
        s.id,
        s.package_number,
        s.submit_end_time,
        a.seal_time,
        IF(s.`status` = 56,a.submit_time,l.archiveTime) archiveTime,
        a.storage_location,
        a.archive_num,
        a.remark
        FROM
        t_project_bid_section s
        LEFT JOIN t_project p ON s.project_id = p.id
        LEFT JOIN (
        SELECT
        u.id,
        u.user_name,
        e.project_id
        FROM
        t_user u
        LEFT JOIN t_project_entrust_user e ON e.user_id = u.id
        LEFT JOIN r_user_department rud on e.user_id = rud.user_id and e.department_id = rud.department_id
        WHERE
        e.type = 1
        AND e.is_delete = 0
        AND u.is_delete = 0
        ) u ON u.project_id = s.project_id
        LEFT JOIN (
        SELECT
        distinct
        c.company_name,
        e.project_id
        FROM
        t_company c
        LEFT JOIN t_project_entrust_user e ON e.company_id = c.id
        LEFT JOIN r_user_department rud on e.user_id = rud.user_id and e.department_id = rud.department_id
        WHERE
        e.type = 0
        AND e.is_delete = 0
        AND c.is_delete = 0
        ) c ON c.project_id = s.project_id
        LEFT JOIN t_archive_project a ON s.id = a.section_id
        LEFT JOIN r_user_department r ON r.user_id = u.id
        LEFT JOIN t_department d ON d.id = r.department_id
        LEFT JOIN (SELECT MAX(created_time) archiveTime,bid_id FROM t_archive_audit GROUP BY bid_id ) l ON l.bid_id = s.id
        WHERE
        s.is_delete = 0
        AND p.is_delete = 0
        AND unix_timestamp( s.submit_end_time ) &lt; unix_timestamp(NOW())
        AND FIND_IN_SET(d.id,#{deptId})
        <if test="condition.startDate !=null and condition.startDate !=''">
            AND DATE_FORMAT( a.seal_time, '%Y-%m-%d' ) &gt;= '${condition.startDate}'
        </if>
        <if test="condition.endDate !=null and condition.endDate !=''">
            AND DATE_FORMAT( a.seal_time, '%Y-%m-%d' ) &lt;= '${condition.endDate}'
        </if>
        <if test="condition.userName !=null and condition.userName !=''">
            AND u.user_name like concat('%',#{condition.userName},'%')
        </if>
        <if test="condition.purchaseNumber !=null and condition.purchaseNumber !=''">
            AND p.purchase_number like concat('%',#{condition.purchaseNumber},'%')
        </if>
        <if test="condition.projectName !=null and condition.projectName !=''">
            AND p.purchase_name like concat('%',#{condition.projectName},'%')
        </if>
        <if test="condition.status != null and condition.status == 1">
            AND s.`status` &lt; 50 OR s.`status` &gt;= 70
        </if>
        <if test="condition.status != null and condition.status == 2">
            AND (s.`status` BETWEEN 50 AND 53
            OR s.`status` = 55 OR s.`status` = 57)
        </if>
        <if test="condition.status != null and condition.status == 3">
            AND (s.`status` = 54 OR s.`status` = 56
            OR s.`status` = 58 OR s.`status` = 60)
        </if>
        GROUP BY
        s.id,
        u.user_name,
        c.company_name,
        p.purchase_name,
        p.purchase_number,
        p.international_number,
        s.package_number,
        s.submit_end_time,
        a.seal_time,
        l.archiveTime,
        a.storage_location,
        a.archive_num,
        s.`status`,
        a.remark
        ORDER BY
        s.submit_end_time ASC
    </select>

    <select id="archiveRecallList" resultType="com.hzw.sunflower.entity.ArchiveRecallList">
        select * from (
        SELECT
        t.purchase_number purchaseNumber,
        tp.id sectionId,
        t.project_name projectName,
        tp.package_number packageNumber,
        tp.package_name packageName,
        ta.remark,
        ta.operation operation,
        u.user_name operatorName,
        ta.created_time
        FROM
        t_archive_project_log ta
        JOIN (
                SELECT DISTINCT
                tp.id sectionId,
                max( ta.created_time ) created_time
                FROM
                t_archive_project_log ta
                LEFT JOIN t_project_bid_section tp ON ta.business_id = tp.id
                LEFT JOIN t_project t ON tp.project_id = t.id
                LEFT JOIN t_user u ON tp.created_user_id = u.id AND u.is_delete = 0
                LEFT JOIN r_user_department  ud ON u.id = ud.user_id AND ud.is_delete = 0
                LEFT JOIN t_department td ON ud.department_id = td.id AND td.is_delete = 0
                WHERE
                ta.is_delete = 0
                AND t.is_delete = 0
                AND tp.is_delete = 0
                <if test="condition.startDate !=null and condition.startDate !=''">
                    AND DATE_FORMAT( ta.created_time, '%Y-%m-%d' ) &gt;= #{condition.startDate}
                </if>
                <if test="condition.endDate !=null and condition.endDate !=''">
                    AND DATE_FORMAT( ta.created_time, '%Y-%m-%d' ) &lt;= #{condition.endDate}
                </if>
                <if test="str !=null and str !=''">
                    ${str}
                </if>
                <if test="condition.keyWords !=null and condition.keyWords !=''">
                    AND (t.project_name like concat('%',#{condition.keyWords},'%')
                    OR t.purchase_number like concat('%',#{condition.keyWords},'%') )
                </if>
                    AND (ta.operation = '撤回待确认' or ta.operation = '已撤回' or  ta.operation = '撤回已退回')
                GROUP BY
                tp.id
        ) b ON ta.business_id = b.sectionId
        AND ta.created_time = b.created_time
        LEFT JOIN t_project_bid_section tp ON ta.business_id = tp.id and tp.is_delete = 0
        LEFT JOIN t_project t ON tp.project_id = t.id  and t.is_delete = 0
        LEFT JOIN t_user u ON tp.created_user_id = u.id  and u.is_delete = 0
        ORDER BY
        ta.created_time DESC) t
        where 1=1
        <if test="condition.status != null and condition.status == 1">
            AND (t.operation = '撤回待确认')
        </if>
        <if test="condition.status != null and condition.status == 2">
            AND t.operation = '已撤回'
        </if>
        <if test="condition.status != null and condition.status == 3">
            AND t.operation = '撤回已退回'
        </if>
    </select>

    <select id="exportArchiveRecallList" resultType="com.hzw.sunflower.entity.ArchiveRecallList">
        select * from (
        SELECT
        t.purchase_number purchaseNumber,
        tp.id sectionId,
        t.project_name projectName,
        tp.package_number packageNumber,
        tp.package_name packageName,
        ta.remark,
        ta.operation operation,
        u.user_name operatorName,
        ta.created_time
        FROM
        t_archive_project_log ta
        JOIN (
        SELECT DISTINCT
        tp.id sectionId,
        max( ta.created_time ) created_time
        FROM
        t_archive_project_log ta
        LEFT JOIN t_project_bid_section tp ON ta.business_id = tp.id
        LEFT JOIN t_project t ON tp.project_id = t.id
        LEFT JOIN t_user u ON tp.created_user_id = u.id AND u.is_delete = 0
        LEFT JOIN r_user_department  ud ON u.id = ud.user_id AND ud.is_delete = 0
        LEFT JOIN t_department td ON ud.department_id = td.id AND td.is_delete = 0
        WHERE
        ta.is_delete = 0
        AND t.is_delete = 0
        AND tp.is_delete = 0
        <if test="condition.startDate !=null and condition.startDate !=''">
            AND DATE_FORMAT( ta.created_time, '%Y-%m-%d' ) &gt;= #{condition.startDate}
        </if>
        <if test="condition.endDate !=null and condition.endDate !=''">
            AND DATE_FORMAT( ta.created_time, '%Y-%m-%d' ) &lt;= #{condition.endDate}
        </if>
        <if test="str !=null and str !=''">
            ${str}
        </if>
        <if test="condition.keyWords !=null and condition.keyWords !=''">
            AND (t.project_name like concat('%',#{condition.keyWords},'%')
            OR t.purchase_number like concat('%',#{condition.keyWords},'%') )
        </if>
        AND (ta.operation = '撤回待确认' or ta.operation = '已撤回' or  ta.operation = '撤回已退回')
        GROUP BY
        tp.id
        ) b ON ta.business_id = b.sectionId
        AND ta.created_time = b.created_time
        LEFT JOIN t_project_bid_section tp ON ta.business_id = tp.id and tp.is_delete = 0
        LEFT JOIN t_project t ON tp.project_id = t.id  and t.is_delete = 0
        LEFT JOIN t_user u ON tp.created_user_id = u.id  and u.is_delete = 0
        ORDER BY
        ta.created_time DESC) t
        where 1=1
        <if test="condition.status != null and condition.status == 1">
            AND (t.operation = '撤回待确认')
        </if>
        <if test="condition.status != null and condition.status == 2">
            AND t.operation = '已撤回'
        </if>
        <if test="condition.status != null and condition.status == 3">
            AND t.operation = '撤回已退回'
        </if>
    </select>
</mapper>
