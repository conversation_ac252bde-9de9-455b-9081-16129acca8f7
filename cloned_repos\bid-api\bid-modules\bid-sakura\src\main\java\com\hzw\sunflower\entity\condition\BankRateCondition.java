package com.hzw.sunflower.entity.condition;

import com.hzw.sunflower.common.BaseCondition;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date ：Created in 2023/9/26 11:10
 * @description：
 * @modified By：`
 * @version: 1.0
 */
@ApiModel(description = "利率管理查询条件")
@Data
public class BankRateCondition extends BaseCondition {

    @ApiModelProperty("银行名称")
    private String bankName;

}
