///***
/// * 主体人员 视图模型
/// */

import 'package:flutter/material.dart';

import 'package:flutter_main_app/serviceLocator.dart';
import 'package:flutter_main_app/src/utils/ApiUtils.dart';

import 'package:flutter_main_app/src/viewModels/marketPlayer/MarketPlayerListViewModel.dart';

import 'package:flutter_main_app/src/models/marketPlayer/SubjectStaffModel.dart';

class SubjectStaffViewModel extends ChangeNotifier {

  SubjectStaffViewModel();

  /// * 主要用于获取当前 subjectID
  final MarketPlayerListViewModel _marketPlayerListViewModel = serviceLocator<MarketPlayerListViewModel>();

  /// * 是否正在加载数据
  bool _isLoading = false;

  bool get isLoading => _isLoading;

  set isLoading(bool isLoading) {
    _isLoading = isLoading;

    notifyListeners();
  }

  /// * 主体人员列表
  List<SubjectStaffModel> _subjectStaffList = [];

  List<SubjectStaffModel> get subjectStaffList => _subjectStaffList;

  ///***
  /// * 给主体人员赋值
  /// * 同时通知所有订阅者
  /// *
  /// * @param {List<SubjectStaffModel>} subjectStaffList
  /// */
  set subjectStaffList(List<SubjectStaffModel> subjectStaffList) {
    _subjectStaffList = subjectStaffList;

    notifyListeners();
  }

  ///***
  /// * 获取主体人员列表
  /// */
  Future<void> fetchSubjectStaffList() async {
    isLoading = true;

    final Map<String, int> params = {
      'subjectID': _marketPlayerListViewModel.subjectID,
    };

    final List<SubjectStaffModel> newSubjectStaffList =
      await ApiUtils.getSubjectStaff(params);

    if (newSubjectStaffList != null && newSubjectStaffList.isNotEmpty) {
      /// * 数据处理
    }

    subjectStaffList = newSubjectStaffList;

    isLoading = false;
  }

}
