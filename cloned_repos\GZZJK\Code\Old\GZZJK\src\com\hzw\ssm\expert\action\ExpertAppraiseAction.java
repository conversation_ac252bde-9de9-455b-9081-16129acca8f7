package com.hzw.ssm.expert.action;

import java.util.List;

import org.apache.struts2.convention.annotation.Action;
import org.apache.struts2.convention.annotation.Namespace;
import org.apache.struts2.convention.annotation.ParentPackage;
import org.apache.struts2.convention.annotation.Result;
import org.apache.struts2.convention.annotation.Results;
import org.springframework.beans.factory.annotation.Autowired;

import com.hzw.ssm.expert.entity.Appraise;
import com.hzw.ssm.expert.entity.AppraiseInfo;
import com.hzw.ssm.expert.entity.AppraiseRemark;
import com.hzw.ssm.expert.entity.ExpertInfoEntity;
import com.hzw.ssm.expert.service.AppraiseService;
import com.hzw.ssm.expert.service.ExpertInfoService;
import com.hzw.ssm.fw.base.BaseAction;
import com.hzw.ssm.sys.project.entity.ProjectEntity;
import com.hzw.ssm.sys.project.service.ProjectService;

/**
 * 专家评价管理
 * 
 * <AUTHOR> 
 */
@Namespace("/expertEvaluate")
@ParentPackage(value = "default")
@Results( { @Result(name = "evaluateExpertList", location = "/jsp/expert/expert_evaluate_list.jsp"),
	@Result(name = "evaluateProjectList", location = "/jsp/expert/projectEvaluateList.jsp"),
	@Result(name = "toAppraiseDetail", location = "/jsp/expert/evaluateDetail.jsp")})
public class ExpertAppraiseAction extends BaseAction {

	private static final long serialVersionUID = -8823858495799309882L;

	@Autowired
	private ExpertInfoService expertInfoService;
	
	@Autowired
	private ProjectService projectService;
	
	@Autowired
	private AppraiseService appraiseService;
	
	private ExpertInfoEntity expertInfoEntity;
	
	private ProjectEntity projectEntity;

	private List<ExpertInfoEntity> expertList;
	
	private List<ProjectEntity> projectList;
	
	/** 评分项 */
	private List<AppraiseInfo> appInfoList;
	private List<Appraise> appraiseList;
	/** 专家评价备注 */
	private AppraiseRemark appraiseRemark;

	
	/**
	 * 查询参加评标的专家
	 * @return
	 */
	@Action("evaluateExpertList")
	public String appraiseExpertList(){
		if(expertInfoEntity == null){
			expertInfoEntity = new ExpertInfoEntity();
		}
		expertInfoEntity.setPage(this.getPage());
		expertList = expertInfoService.queryPageBidExpertInfo(expertInfoEntity);
		return "evaluateExpertList";
	}
	
	/**
	 * 查询参加评标的专家，评标的项目
	 * @return
	 */
	@Action("evaluateProjectList")
	public String evaluateProjectList(){
		if(projectEntity == null){
			projectEntity = new ProjectEntity();
		}
		projectEntity.setPage(this.getPage());
		projectList = projectService.queryPageExpertPro(projectEntity);
		return "evaluateProjectList";
	}
	
	/**
	 * 查看专家评价信息
	 * 
	 * @return
	 * @throws Exception
	 */
	@Action("toAppraiseDetail")
	public String toAppraiseDetail() throws Exception {
		// 专家信息
		expertInfoEntity = appraiseService.queryBaseAppraise(projectEntity.getId());
		// 查询评分项
		appInfoList = appraiseService.queryAppraiseInfoById("0");
		// 查询评分
		appraiseList = appraiseService.queryAppraiseDetail(projectEntity.getId(),"0");
		// 评价备注
		appraiseRemark = appraiseService.queryAppraiseRemark(projectEntity.getId());
		return "toAppraiseDetail";
	}
	
	public ExpertInfoEntity getExpertInfoEntity() {
		return expertInfoEntity;
	}
	public void setExpertInfoEntity(ExpertInfoEntity expertInfoEntity) {
		this.expertInfoEntity = expertInfoEntity;
	}
	public List<ExpertInfoEntity> getExpertList() {
		return expertList;
	}
	public void setExpertList(List<ExpertInfoEntity> expertList) {
		this.expertList = expertList;
	}

	public ProjectEntity getProjectEntity() {
		return projectEntity;
	}

	public void setProjectEntity(ProjectEntity projectEntity) {
		this.projectEntity = projectEntity;
	}

	public List<ProjectEntity> getProjectList() {
		return projectList;
	}

	public void setProjectList(List<ProjectEntity> projectList) {
		this.projectList = projectList;
	}

	public List<AppraiseInfo> getAppInfoList() {
		return appInfoList;
	}

	public void setAppInfoList(List<AppraiseInfo> appInfoList) {
		this.appInfoList = appInfoList;
	}

	public List<Appraise> getAppraiseList() {
		return appraiseList;
	}

	public void setAppraiseList(List<Appraise> appraiseList) {
		this.appraiseList = appraiseList;
	}

	public AppraiseRemark getAppraiseRemark() {
		return appraiseRemark;
	}

	public void setAppraiseRemark(AppraiseRemark appraiseRemark) {
		this.appraiseRemark = appraiseRemark;
	}
}
