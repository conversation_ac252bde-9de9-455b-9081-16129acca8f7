///***
/// * json_annotation 的泛型转换类
/// */

import 'package:json_annotation/json_annotation.dart';

import 'package:flutter_main_app/src/models/common/HttpResponseDataListModel.dart';

import 'package:flutter_main_app/src/models/bulletin/BulletinListItemModel.dart';

import 'package:flutter_main_app/src/models/marketPlayer/SubjectBasicModel.dart';

import 'package:flutter_main_app/src/models/dataReport/DataReportModel.dart';

class GenericConverter<T> implements JsonConverter<T, Object> {

  const GenericConverter();

  @override
  T fromJson(Object json) {
    ///***
    /// * 自定义模型
    /// */
    if (json is Map<String, dynamic>) {

      /// * 数据列表
      if (json.containsKey('data') && json.containsKey('totalPage')) {
        return HttpResponseDataListModel.fromJson(json) as T;
      }

      /// * 公告公示
      if (
        json.containsKey('UUID') &&
        json.containsKey('bulletinID') &&
        json.containsKey('bidSectionId')
      ) {
        return BulletinListItemModel.fromJson(json) as T;
      }

      /// * 市场主体
      if (json.containsKey('subjectID')) {
        if (json.containsKey('subjectTypeID')) {
          /// * 基本信息
          return SubjectBasicModel.fromJson(json) as T;
        }
      }

      /// * 数据报告
      if (
        json.containsKey('annualTransaction') &&
        json.containsKey('projectTransaction') &&
        json.containsKey('regionalTender')
      ) {
        return DataReportModel.fromJson(json) as T;
      }

    }

    ///***
    /// * This will only work if `json` is a native JSON type:
    /// *   num, String, bool, null, etc.
    /// * *and* is assignable to `T`.
    /// */
    return json as T;
  }

  @override
  Object toJson(T object) {
    ///***
    /// * This will only work if `object` is a native JSON type:
    /// *   num, String, bool, null, etc.
    /// * Or if it has a `toJson()` function.
    /// */
    return object;
  }

}
