package com.hzw.sunflower.controller;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hzw.sunflower.common.BaseController;
import com.hzw.sunflower.common.Paging;
import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.controller.request.CaDataReq;
import com.hzw.sunflower.controller.response.CaDataVo;
import com.hzw.sunflower.entity.DepartProject;
import com.hzw.sunflower.controller.response.ProcessAddress;
import com.hzw.sunflower.service.CaDataService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/3/9 15:52 星期四
 */

@Api(tags = "Ca数据")
@RestController
@RequestMapping("/caData")
public class CaDataController extends BaseController {

    @Resource
    private CaDataService caDataService;


    @ApiOperation(value = "CA数据列表")
    @ApiImplicitParam(name = "req", value = "查询条件", required = true, dataType = "CaDataReq", paramType = "body")
    @PostMapping({"/caDataList"})
    public Result<Paging<CaDataVo>> list(@RequestBody CaDataReq req) {
        IPage<CaDataVo> page = caDataService.findInfoByCondition(req,getJwtUser());
        return Result.ok(Paging.buildPaging(page));
    }

    @ApiOperation(value = "查询所属处室列表")
    @PostMapping("/getDeptIds")
    public Result<List<DepartProject>> deptIdsList() {
        List<DepartProject> list = caDataService.getDeptIds();
        return Result.ok(list);
    }

    @ApiOperation(value = "电子开标数据查看")
    @PostMapping("/getCaBidOpeningList")
    public Result<Map<String,Object>> getCaBidOpeningList() {
        Map<String,Object> caBidOpeningList = caDataService.getCaBidOpeningList();
        return  Result.ok(caBidOpeningList);
    }

    @ApiOperation(value = "查询进场场地列表")
    @PostMapping("/getRelatedProjectAddress")
    public Result<List<ProcessAddress>> getRelatedProjectAddress() {
        List<ProcessAddress> list = caDataService.getRelatedProjectAddress();
        return Result.ok(list);
    }



    @ApiOperation(value = "导出CA数据excel")
    @ApiImplicitParam(name = "req", value = "查询条件", required = true, dataType = "CaDataReq", paramType = "body")
    @PostMapping("/exportCaList")
    public  void  exportCaList(HttpServletResponse response, @RequestBody CaDataReq req) {
        List<CaDataVo> caDataVos = caDataService.exportCaList(req,getJwtUser());
        //设置标题信息
        Workbook workbook;
        String title;
        //        DateFormat sdf= new SimpleDateFormat("yyyy-MM");
        //        String startTime= null;
        //        String endTime= null;
        //        try {
        //            startTime = sdf.format(sdf.parse(req.getStartDate()));
        //            endTime = sdf.format(sdf.parse(req.getEndDate()));
        //        } catch (ParseException e) {
        //            e.printStackTrace();
        //        }

        title="CA数据一览表";
        workbook= ExcelExportUtil.exportExcel(new ExportParams(title,"CA数据表"), CaDataVo.class,caDataVos);
        try {
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("统计.xls", "UTF-8"));
            response.setContentType("application/vnd.ms-excel;charset=UTF-8");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        try {
            workbook.write(response.getOutputStream());
            response.getOutputStream().close();
            workbook.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }



}
