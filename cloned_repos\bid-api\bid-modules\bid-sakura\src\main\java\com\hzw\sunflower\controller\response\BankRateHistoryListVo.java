package com.hzw.sunflower.controller.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 银行利率历史记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-27
 */
@Data
public class BankRateHistoryListVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("利率")
    private BigDecimal rate;

    @ApiModelProperty("调整之前的日期")
    private String beforeDate;

    @ApiModelProperty("生效日期")
    private String effectDate;

}
