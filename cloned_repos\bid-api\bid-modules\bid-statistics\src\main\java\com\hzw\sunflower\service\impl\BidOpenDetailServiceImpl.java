package com.hzw.sunflower.service.impl;

import com.hzw.sunflower.constant.ProjectAnalysisConstants;
import com.hzw.sunflower.constant.ReturnCodeConstants;
import com.hzw.sunflower.constant.constantenum.BidOpenDetailStatusEnum;
import com.hzw.sunflower.constant.constantenum.ExchangeRateEnum;
import com.hzw.sunflower.controller.request.BidOpenDetailReq;
import com.hzw.sunflower.controller.request.ScheduleAmountREQ;
import com.hzw.sunflower.controller.request.ScheduleUserListREQ;
import com.hzw.sunflower.controller.response.ScheduleAmountVO;
import com.hzw.sunflower.dao.BidOpenDetailMapper;
import com.hzw.sunflower.datascope.utils.DataScopeUtil;
import com.hzw.sunflower.entity.BidOpenDetail;
import com.hzw.sunflower.service.BidOpenDetailService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class BidOpenDetailServiceImpl implements BidOpenDetailService {
    @Autowired
    BidOpenDetailMapper bidOpenDetailMapper;

    /**
     * 查询开标一览表
     * @param req
     * @param keyWords
     * @param startDate
     * @param endDate
     * @param pageIndex
     * @param pageSize
     * @return
     */
    @Override
    public Map<String,Object>  selectBidOpenDetailList(BidOpenDetailReq req, String keyWords, String startDate, String endDate, int pageIndex, int pageSize) {
        Map<String,Object> map = new HashMap<>();
        Integer count = 0;
        List<BidOpenDetail> bidOpenDetails = queryBidOpenDetailList(req, keyWords, startDate, endDate, pageIndex, pageSize);
        if(bidOpenDetails != null && !bidOpenDetails.isEmpty()){
            count = bidOpenDetails.get(0).getCount();
        }
        map.put("flag", true);
        map.put("code", ReturnCodeConstants.JSTCC_CODE_OK);
        map.put("msg", "成功!");
        map.put("exceptionMsg",null);
        map.put("total",count);
        map.put("data",bidOpenDetails);

        return map;
    }



    /**
     * 导出excel查询
     * @param req
     * @param keyWords
     * @param startDate
     * @param endDate
     * @return
     */
    @Override
    public List<BidOpenDetail> queryBidOpenDetailList(BidOpenDetailReq req,String keyWords, String startDate, String endDate,Integer pageIndex, Integer pageSize , Boolean hasConferenceAddress) {
        // 处理数据权限
        // String datascopesql= DataScopeUtil.getProjectDataScopeFilterDepartSql("tpe","eu","tp");
        String datascopesql="";
        if(null !=req.getFlag() && req.getFlag()){
            // 运管处
            datascopesql="";
        }else {
            // 普通处室
            datascopesql= DataScopeUtil.getProjectDataScopeFilterDepartSql("rud","eu","tp");
        }

        int count = 0;
        List<BidOpenDetail> bidOpenDetailsCount = bidOpenDetailMapper.selectBidOpenDetailList(req,keyWords, startDate, endDate, datascopesql,0,0);
        //处理兼岗人员项目重复统计
        Map<String,BidOpenDetail> mapCount = new HashMap<>();
        if(CollectionUtils.isNotEmpty(bidOpenDetailsCount)){
            for (BidOpenDetail bidOpenDetail : bidOpenDetailsCount) {
                mapCount.put(bidOpenDetail.getProjectId()+bidOpenDetail.getUserName()+bidOpenDetail.getBidPackage(),bidOpenDetail);
            }
            count = mapCount.size();
        }
        List<BidOpenDetail> bidOpenDetails = null;
        if(pageIndex != null && pageSize != null){
            List<BidOpenDetail> values = new ArrayList<>(mapCount.values());
            //开标时间,标段id排序
            values = values.stream().sorted(Comparator.comparing(BidOpenDetail::getOpenTime)
                    .thenComparing(BidOpenDetail::getSectionId)).collect(Collectors.toList());
            int totalPage = 0;
            if(CollectionUtils.isNotEmpty(values)){
                if(values.size() % pageSize == 0){
                    totalPage = values.size() / pageSize;
                }else {
                    totalPage = (values.size() / pageSize) + 1;
                }

                if(pageIndex == totalPage){
                    bidOpenDetails = values.subList((pageIndex-1)*pageSize,values.size());
                }else {
                    bidOpenDetails = values.subList((pageIndex-1)*pageSize,(pageIndex*pageSize));
                }
            }
        }else{
            bidOpenDetails = new ArrayList<>(mapCount.values());
            //开标时间,标段id排序
            bidOpenDetails = bidOpenDetails.stream().sorted(Comparator.comparing(BidOpenDetail::getOpenTime)
                    .thenComparing(BidOpenDetail::getSectionId)).collect(Collectors.toList());

        }
        if(CollectionUtils.isNotEmpty(bidOpenDetails)){
            for (BidOpenDetail bidOpenDetail : bidOpenDetails) {
                if(bidOpenDetail.getEntrustCurrency() != null && !ProjectAnalysisConstants.RMB_CODE.equals(bidOpenDetail.getEntrustCurrency())){//外币
                    //金额转成人民币
                    bidOpenDetail.setEntrustMoney(bidOpenDetail.getEntrustMoney().multiply(bidOpenDetail.getExchangeRate()));
                }
                if(bidOpenDetail.getEntrustMoney() != null){
                    //元转换成万元
                    bidOpenDetail.setEntrustMoney(bidOpenDetail.getEntrustMoney().divide(ProjectAnalysisConstants.TEN_THOUSAND,6, RoundingMode.HALF_UP));
                }
                //添加开标地点
                Long projectId = bidOpenDetail.getProjectId();
//                String projectNumber = bidOpenDetail.getProjectNumber();
                String bidPackage = bidOpenDetail.getBidPackage();
//                bidOpenDetail.setConferenceAddress(bidOpenDetailMapper.selectConferenceAddress(projectNumber,bidPackage));
                if (hasConferenceAddress){
                    bidOpenDetail.setConferenceAddress(bidOpenDetailMapper.selectConferenceAddressNew(projectId,bidPackage));
                }
                bidOpenDetail.setCount(count);
            }
        }
        return bidOpenDetails;
    }

    @Override
    public List<BidOpenDetail> queryBidOpenDetailList(BidOpenDetailReq req, String keyWords, String startDate, String endDate, Integer pageIndex, Integer pageSize) {
        return queryBidOpenDetailList(req,keyWords,startDate,endDate,pageIndex,pageSize,true);
    }

    /**
     * 导出excel查询
     * @param req
     * @param keyWords
     * @param startDate
     * @param endDate
     * @return
     */
    @Override
    public List<BidOpenDetail> queryBidOpenDetailList(BidOpenDetailReq req,String keyWords, String startDate, String endDate) {
        return  queryBidOpenDetailList(req,keyWords,startDate,endDate,null,null);
    }

    /**
     * 根据筛选条件查询日程金额统计（年月）
     * @param req
     * @return
     */
    @Override
    public ScheduleAmountVO getScheduleAmount(ScheduleAmountREQ req) {
        ScheduleAmountVO scheduleAmountVO = new ScheduleAmountVO();
        SimpleDateFormat monthSdf = new SimpleDateFormat("yyyyMM");
        SimpleDateFormat daySdf = new SimpleDateFormat("yyyy-MM-dd");
        String datascopesql= DataScopeUtil.getProjectDataScopeFilterDepartSql("rud","eu","tp");
        //部门及人员筛选
        BidOpenDetailReq bidOpenDetailReq = new BidOpenDetailReq();
        bidOpenDetailReq.setBidStatus(BidOpenDetailStatusEnum.ALL.getType());
        bidOpenDetailReq.setDollar(ExchangeRateEnum.DOLLAR.getType());
        bidOpenDetailReq.setEuro(ExchangeRateEnum.EURO.getType());
        bidOpenDetailReq.setYen(ExchangeRateEnum.YEN.getType());
        if(!req.getUserList().isEmpty()){
            bidOpenDetailReq.setDeptIds(req.getUserList().stream().distinct().map(ScheduleUserListREQ::getDepartId).collect(Collectors.toList()));
            bidOpenDetailReq.setUserIds(req.getUserList().stream().distinct().map(ScheduleUserListREQ::getUserId).collect(Collectors.toList()));
        }

        String startDate = "";
        String endDate = "";
        //年
        BigDecimal yearEntrustAmount = new BigDecimal("0");
        String year = req.getMonth().substring(0,4);
        bidOpenDetailReq.setYear(year);
        bidOpenDetailReq.setMonth(req.getMonth());
        startDate = year+"-01-01";
        endDate = year+"-12-31";
        //委托金额
//        List<BidOpenDetail> yearDetails = queryBidOpenDetailList(bidOpenDetailReq, bidOpenDetailReq.getKeyWords(), startDate, endDate);
        List<BidOpenDetail> yearDetails = queryBidOpenDetailListAll(bidOpenDetailReq,bidOpenDetailReq.getKeyWords(),startDate,endDate);
        if(!yearDetails.isEmpty()){
            yearEntrustAmount = yearDetails.stream().map(i -> {
                if (i.getEntrustMoney()==null){
                    return BigDecimal.ZERO;
                }else {
                    return i.getEntrustMoney();
                }
            }).reduce(BigDecimal.ZERO,BigDecimal::add);
        }
        //月
        BigDecimal monthEntrustAmount = new BigDecimal("0");
        //月收入金额
        List<BidOpenDetail> monthDetails = yearDetails.stream().filter(d -> (d.getOpenTime() != null && monthSdf.format(d.getOpenTime()).equals(req.getMonth())) ).collect(Collectors.toList());
        if(!monthDetails.isEmpty()){
            monthEntrustAmount = monthDetails.stream().map(i -> {
                if (i.getEntrustMoney()==null){
                    return BigDecimal.ZERO;
                }else {
                    return i.getEntrustMoney();
                }
            }).reduce(BigDecimal.ZERO,BigDecimal::add);
        }
        //日 为空的时候 默认传当前日
        if(StringUtils.isBlank(req.getDay())){
            req.setDay(daySdf.format(new Date()));
        }
        bidOpenDetailReq.setDay(req.getDay());
        BigDecimal dayEntrustAmount = new BigDecimal("0");
        List<BidOpenDetail> dayDetails = yearDetails.stream().filter(d -> (d.getOpenTime() != null && daySdf.format(d.getOpenTime()).equals(req.getDay())) ).collect(Collectors.toList());
        if(!dayDetails.isEmpty()){
            dayEntrustAmount = dayDetails.stream().map(i -> {
                if (i.getEntrustMoney()==null){
                    return BigDecimal.ZERO;
                }else {
                    return i.getEntrustMoney();
                }
            }).reduce(BigDecimal.ZERO,BigDecimal::add);
        }
        //收入金额查询
        ScheduleAmountVO inComeAmount  = bidOpenDetailMapper.queryInComeAmount(bidOpenDetailReq,datascopesql);
        BigDecimal yearIncome = new BigDecimal("0");
        if(inComeAmount.getYearToIncomeAmount().doubleValue() != 0){
            yearIncome = inComeAmount.getYearToIncomeAmount().divide(ProjectAnalysisConstants.TEN_THOUSAND, 6, RoundingMode.HALF_UP);
        }
        scheduleAmountVO.setYearToIncomeAmount(yearIncome);

        BigDecimal monthIncome = new BigDecimal("0");
        if(inComeAmount.getMonthToIncomeAmount().doubleValue() != 0){
            monthIncome = inComeAmount.getMonthToIncomeAmount().divide(ProjectAnalysisConstants.TEN_THOUSAND, 6, RoundingMode.HALF_UP);
        }
        scheduleAmountVO.setMonthToIncomeAmount(monthIncome);

        BigDecimal dayIncome = new BigDecimal("0");
        if(inComeAmount.getDayToIncomeAmount().doubleValue() != 0){
            dayIncome = inComeAmount.getDayToIncomeAmount().divide(ProjectAnalysisConstants.TEN_THOUSAND, 6, RoundingMode.HALF_UP);
        }
        scheduleAmountVO.setDayToIncomeAmount(dayIncome);

        scheduleAmountVO.setYearEntrustAmount(yearEntrustAmount);
        scheduleAmountVO.setMonthEntrustAmount(monthEntrustAmount);
        scheduleAmountVO.setDayEntrustAmount(dayEntrustAmount);
        return scheduleAmountVO;
    }

    public List<BidOpenDetail> queryBidOpenDetailListAll(BidOpenDetailReq req,String keyWords, String startDate, String endDate) {
        // 处理数据权限
        String datascopesql="";
        if(null !=req.getFlag() && req.getFlag()){
            // 运管处
            datascopesql="";
        }else {
            // 普通处室
            datascopesql= DataScopeUtil.getProjectDataScopeFilterDepartSql("rud","eu","tp");
        }
        List<BidOpenDetail> bidOpenDetailsCount = bidOpenDetailMapper.selectBidOpenDetailListAll(req,keyWords, startDate, endDate, datascopesql);
        //处理兼岗人员项目重复统计
        Map<String,BidOpenDetail> mapCount = new HashMap<>();
        if(CollectionUtils.isNotEmpty(bidOpenDetailsCount)){
            for (BidOpenDetail bidOpenDetail : bidOpenDetailsCount) {
                mapCount.put(bidOpenDetail.getProjectId()+bidOpenDetail.getUserName()+bidOpenDetail.getBidPackage(),bidOpenDetail);
            }
        }
        List<BidOpenDetail> bidOpenDetails = new ArrayList<>(mapCount.values());
        //开标时间,标段id排序
        bidOpenDetails = bidOpenDetails.stream().sorted(Comparator.comparing(BidOpenDetail::getOpenTime)
                .thenComparing(BidOpenDetail::getSectionId)).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(bidOpenDetails)){
            for (BidOpenDetail bidOpenDetail : bidOpenDetails) {
                if(null != bidOpenDetail.getEntrustCurrency() && !ProjectAnalysisConstants.RMB_CODE.equals(bidOpenDetail.getEntrustCurrency())){//外币
                    //金额转成人民币
                    bidOpenDetail.setEntrustMoney(bidOpenDetail.getEntrustMoney().multiply(bidOpenDetail.getExchangeRate()));
                }
                if(null != bidOpenDetail.getEntrustMoney()){
                    //元转换成万元
                    bidOpenDetail.setEntrustMoney(bidOpenDetail.getEntrustMoney().divide(ProjectAnalysisConstants.TEN_THOUSAND,6, RoundingMode.HALF_UP));
                }
            }
        }
        return bidOpenDetails;
    }

}
