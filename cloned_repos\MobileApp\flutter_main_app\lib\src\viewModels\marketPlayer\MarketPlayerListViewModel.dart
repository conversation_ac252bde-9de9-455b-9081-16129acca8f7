///***
/// * 市场主体列表 视图模型
/// */

import 'package:flutter/material.dart';

import 'package:flutter_main_app/src/utils/ApiUtils.dart';

import 'package:flutter_main_app/src/models/common/HttpResponseDataListModel.dart';
import 'package:flutter_main_app/src/models/marketPlayer/SubjectBasicModel.dart';

class MarketPlayerListViewModel extends ChangeNotifier {

  MarketPlayerListViewModel();

  /// * 主体 ID，统一储存，下级页面都从这里获取 subjectID
  int subjectID;

  /// * 是否正在加载数据
  bool _isLoading = false;

  bool get isLoading => _isLoading;

  set isLoading(bool isLoading) {
    _isLoading = isLoading;

    notifyListeners();
  }

  ///***
  /// * 主体类型
  /// *
  /// * 1 - 招标人
  /// * 3 - 投标人
  /// * 5 - 招标代理
  /// */
  int _subjectTypeID = 3;

  int get subjectTypeID => _subjectTypeID;

  set subjectTypeID(int subjectTypeID) {
    _subjectTypeID = subjectTypeID;

    notifyListeners();
  }

  /// * 主体名称
  String _subjectName = '';

  String get subjectName => _subjectName;

  set subjectName(String subjectName) {
    _subjectName = subjectName;

    notifyListeners();
  }

  /// * 当前页号
  int _currentPage = 1;

  int get currentPage => _currentPage;

  set currentPage(int currentPage) {
    _currentPage = currentPage;

    notifyListeners();
  }

  /// * 每页显示条数
  int _pageSize = 20;

  int get pageSize => _pageSize;

  set pageSize(int pageSize) {
    _pageSize = pageSize;

    notifyListeners();
  }

  /// * 页脚信息
  int totalNumber;

  int totalPage;

  /// * 数据列表
  List<SubjectBasicModel> _dataList = [];

  List<SubjectBasicModel> get dataList => _dataList;

  ///***
  /// * 获取数据列表
  /// *
  /// * @return {Future<List<SubjectBasicModel>>}
  /// */
  Future<List<SubjectBasicModel>> fetchDataList() async {
    isLoading = true;

    final Map<String, dynamic> params = {
      'subjectTypeID': subjectTypeID,
      'currentPage': currentPage,
      'pageSize': pageSize,
    };

    if (subjectName != null && subjectName.length > 0) {
      params.addAll({
        'subjectName': subjectName,
      });
    }

    final HttpResponseDataListModel<SubjectBasicModel> httpResponse =
      await ApiUtils.getSubjectBasicList(params);

    List<SubjectBasicModel> newDataList;

    if (httpResponse != null) {
      newDataList = httpResponse.data ?? [];

      totalNumber = httpResponse.totalNumber ?? 0;

      totalPage = httpResponse.totalPage ?? 0;

      concatDataList(newDataList);
    }

    isLoading = false;

    return newDataList;
  }

  ///***
  /// * 清空数据列表
  /// * 同时通知所有订阅者
  /// */
  void clearDataList() {
    _dataList.clear();

    notifyListeners();
  }

  ///***
  /// * 给数据列表赋值
  /// * 同时通知所有订阅者
  /// *
  /// * @param {List<SubjectBasicModel>} newDataList
  /// */
  void setDataList(List<SubjectBasicModel> newDataList) {
    _dataList = newDataList;

    notifyListeners();
  }

  ///***
  /// * 向数据列表末尾添加新的数据列表
  /// * 同时通知所有订阅者
  /// *
  /// * @param {List<SubjectBasicModel>} newDataList
  /// */
  void concatDataList(List<SubjectBasicModel> newDataList) {
    _dataList?.addAll(newDataList);

    notifyListeners();
  }

}
