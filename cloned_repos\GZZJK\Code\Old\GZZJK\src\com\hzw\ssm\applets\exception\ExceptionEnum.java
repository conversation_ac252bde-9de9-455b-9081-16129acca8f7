package com.hzw.ssm.applets.exception;

public enum ExceptionEnum {


    INVALID_CODE(10001, "code无效,请检查"),
    VERIFIC_CODE_ATYPISM(10002, "验证码不一致"),
    NO_REGISTER(10003, "您填写的身份证号尚未注册,请前往完成注册,如有问题请联系人工客服4000580203"),
    CARD_PHONE_BOUND(10004, "您填写的身份证号已绑定手机尾号XXXX,请修改手机号完成绑定,如有问题请联系人工客服4000580203"),
    PHONE_BOUND(10005, "您填写的手机号已绑定,请修改手机号"),

    EDUCATION_EXIST(20001, "每个学历只允许添加一条记录"),
    TITLE_EXIST(20002, "同一个职称仅允许添加一条记录"),
    QUALIFICATION_EXIST(20003, "执业资格名称不允许重复"),
    EVALUATION_EXIST(20004, "同一评标专业仅允许一条记录"),
    EVALUATION_NUM_OVER(20005, "评标专业最少上传1条,最多上传5条"),
    TITLE_NUM_OVER(20006, "职称至少有一条中级满8年或者中级以上职称才能提交审核!"),

    PHONE_IS_NULL(20006, "手机号不能为空"),
    SEND_CODE_OVER(20007, "当天最多可发送五次"),
    SEND_CODE_MORE(20008, "您的操作太频繁,请稍后再发送验证码"),
    VERIFIC_CODE_WAIT(20009, "请发送验证码,并填写有效的验证码"),
    VERIFIC_CODE_INVALID(20010, "验证码已失效"),
    ATTACHMENT_DOWNLOADED(20011, "附件已下载"),

    //自定义的状态码
    DATABASE_EXCEPTION(2000, "数据库连接异常"),
    MONITOR_EXCEPTION(2001, "文件监控异常"),
    SERVER_IS_BUSY(2003, "服务器正忙..."),
    TOKEN_INVALID(2004, "无效的token"),
    DECRYPT_INVALID(2005, "token解密失败"),
    TIME_OUT(2006, "token已过期"),
    USER_DISABLED(2007, "十分抱歉,您的账号状态异常,暂不允许登录,如有疑问请联系人工客服4000580203"),
    FILE_IS_NULL(2008, "文件fileUrl为空");

    //错误码
    public Integer code;
    //提示信息
    public String message;

    //构造函数
    ExceptionEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    //获取状态码
    public Integer getCode() {
        return code;
    }

    //获取提示信息
    public String getMessage() {
        return message;
    }
}
