package com.hzw.common.basebean.utils;


import com.hzw.common.basebean.annotation.IgnoreCompare;
import com.hzw.common.basebean.domain.BaseCompareFlagBean;
import com.hzw.common.basebean.web.domain.BaseEntity;
import org.springframework.util.CollectionUtils;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.*;
import java.util.stream.Collectors;

public class HistoryCompareUtils {
    private static final String EMPTY_STRING = "";
    private static final String GET_METHOD_PREFIX = "get";

    /**
     * 比较类是否有所变化
     *
     * @param sourceObj  原数据
     * @param compareObj 比较的数据
     * @param <T>        目标类（要求继承BaseCompareFlagBean）
     * @return 是否有变化
     */
    public static <T> boolean compare(T sourceObj, Object compareObj) {
        //获得对象的类型
        Class sourceClassType = sourceObj.getClass();

        if ((compareObj != null) && (compareObj.getClass() != sourceClassType)) {
            // 比较的类型不同
            return true;
        }
        if (!(sourceObj instanceof BaseCompareFlagBean)) {
            // 类型不是BaseCompareFlagBean
            return false;
        }

        //获得对象的所有属性
        Field[] fields = sourceClassType.getDeclaredFields();
        boolean globalChanged = false;

        try {
            Map<String, Boolean> compareFlagMap = new HashMap<>(fields.length - 2);

            for (Field field : fields) {
                String fieldName = field.getName();

                // 跳过忽略对比的字段
                IgnoreCompare annotation = field.getAnnotation(IgnoreCompare.class);
                if (Objects.nonNull(annotation)) {
                    continue;
                }

                boolean isChanged = false;

                String firstLetter = fieldName.substring(0, 1).toUpperCase();
                // 获得和属性对应的get方法的名字
                String getMethodName = String.join(EMPTY_STRING, GET_METHOD_PREFIX, firstLetter, fieldName.substring(1));

                // 获得和属性对应的get方法
                Method getMethod = sourceClassType.getMethod(getMethodName, new Class[]{});

                // 调用原对象的get方法
                Object value = getMethod.invoke(sourceObj, new Object[]{});
                if (compareObj == null) {
                    // 比较对象为空
                    isChanged = true;
                } else {
                    Object compareValue = getMethod.invoke(compareObj, new Object[]{});

                    if ((value instanceof Collection) && (compareValue instanceof Collection)) {
                        // 如果目标属性是集合

                        // 原对象集合
                        Collection sourceCollection = (Collection) value;
                        Iterator sourceEach = sourceCollection.iterator();
                        // 比较对象集合
                        Collection compareCollection = (Collection) compareValue;
                        Iterator compareEach = compareCollection.iterator();

                        // 循环比较
                        while (sourceEach.hasNext() && compareEach.hasNext()) {
                            isChanged = compare(sourceEach.next(), compareEach.next());
                        }

                        // 原对象比比较对象多
                        while (sourceEach.hasNext()) {
                            compare(sourceEach.next(), null);
                            isChanged = true;
                        }

                        // 比较对象比原对象多
                        if (compareEach.hasNext()) {
                            isChanged = true;
                        }

                        List value1 = (List) value;
                        List compareValue1 = (List) compareValue;
                        if(value1.size() == compareValue1.size()){
                            for (int i = 0; i < value1.size(); i++) {
                                boolean compare = compareObjectList(value1.get(i), compareValue1.get(i));
                                isChanged = compare;
                                break;
                            }
                        }

                    }else if ((value instanceof BaseEntity)) {
                        // 类型是实体
                        boolean compare = compareObjectList(value, compareValue);
                        isChanged = compare;
                    } else {
                        // 比较结果
                        if (value != null) {
                            isChanged = !value.equals(compareValue);
                        } else {
                            if (compareValue != null) {
                                isChanged = true;
                            }
                        }
                    }
                }

                // 设置比较结果
                compareFlagMap.put(fieldName, isChanged);
                if (isChanged) {
                    globalChanged = true;
                }
            }

            // 设置比较结果
            BaseCompareFlagBean sourceBean = (BaseCompareFlagBean) sourceObj;
            sourceBean.setCompareFlag(compareFlagMap);
            sourceBean.setChanged(globalChanged);
        } catch (Exception e) {
            // do nothing
        }
        return globalChanged;
    }

    /**
     * 比较类是否有所变化
     *
     * @param sourceObj  原数据
     * @param compareObj 比较的数据
     * @param <T>        目标类（要求继承BaseCompareFlagBean）
     * @return 更新变化结果数据集
     */
    private static <T> T compareObj(T sourceObj, Object compareObj) {
        compare(sourceObj, compareObj);
        return sourceObj;
    }


    public static <T> boolean compareObjectList(T sourceObj, Object compareObj) {
        //获得对象的类型
        Class sourceClassType = sourceObj.getClass();
        //获得对象的所有属性
        Field[] fields = sourceClassType.getDeclaredFields();
        boolean globalChanged = false;
        try {
            for (Field field : fields) {
                String fieldName = field.getName();
                boolean isChanged = false;
                // 跳过忽略对比的字段
                IgnoreCompare annotation = field.getAnnotation(IgnoreCompare.class);
                if (Objects.nonNull(annotation)) {
                    continue;
                }
                String firstLetter = fieldName.substring(0, 1).toUpperCase();
                // 获得和属性对应的get方法的名字
                String getMethodName = String.join(EMPTY_STRING, GET_METHOD_PREFIX, firstLetter, fieldName.substring(1));
                // 获得和属性对应的get方法
                Method getMethod = sourceClassType.getMethod(getMethodName, new Class[]{});
                // 调用原对象的get方法
                Object value = getMethod.invoke(sourceObj, new Object[]{});
                if (compareObj == null) {
                    // 比较对象为空
                    isChanged = true;
                } else {
                    Object compareValue = getMethod.invoke(compareObj, new Object[]{});
                    if ((value instanceof Collection) && (compareValue instanceof Collection)) {
                        // 如果目标属性是集合
                        // 原对象集合
                        Collection sourceCollection = (Collection) value;
                        Iterator sourceEach = sourceCollection.iterator();
                        // 比较对象集合
                        Collection compareCollection = (Collection) compareValue;
                        Iterator compareEach = compareCollection.iterator();
                        // 循环比较
                        while (sourceEach.hasNext() && compareEach.hasNext()) {
                            isChanged = compare(sourceEach.next(), compareEach.next());
                        }
                        // 原对象比比较对象多
                        while (sourceEach.hasNext()) {
                            compare(sourceEach.next(), null);
                            isChanged = true;
                        }
                        // 比较对象比原对象多
                        if (compareEach.hasNext()) {
                            isChanged = true;
                        }

                        List value1 = (List) value;
                        List compareValue1 = (List) compareValue;
                        if(value1.size() == compareValue1.size()){
                            for (int i = 0; i < value1.size(); i++) {
                                boolean compare = compareObjectList(value1.get(i), compareValue1.get(i));
                                if(compare){
                                    return globalChanged = true;
                                }
                            }
                        }
                    } else {
                        // 比较结果
                        if (value != null) {
                            isChanged = !value.equals(compareValue);
                        } else {
                            if (compareValue != null) {
                                isChanged = true;
                            }
                        }
                    }
                }
                if (isChanged) {
                    globalChanged = true;
                }
            }
        } catch (Exception e) {
            // do nothing
        }
        return globalChanged;
    }

    /**
     * 集合比较变化
     *
     * @param sourceList 原数据集合
     * @param isAsc      排序：
     *                   true：升序：每一个元素和前一个元素相比，最新的在最后面
     *                   false：降序：每一个元素和后一个元素相比，最新的在最前面
     * @param <T>        目标类（要求继承BaseCompareFlagBean）
     * @return 附带变化比较的集合
     */

    public static <T> List<T> compareList(List<T> sourceList, boolean isAsc) {
        // 参数为空，直接返回
        if (CollectionUtils.isEmpty(sourceList)) {
            return Collections.emptyList();
        }

        int length = sourceList.size();

        if (isAsc) {
            // 升序排列（最新的在最后面）
            return sourceList.stream().map(sourceObj ->
                    compareObj(sourceObj,
                            sourceList.get((sourceList.indexOf(sourceObj) - 1 < 0) ? sourceList.indexOf(sourceObj) : sourceList.indexOf(sourceObj) - 1)
                    )).collect(Collectors.toList());
        } else {
            // 降序排列（最新的在最前面）
            return sourceList.stream().map(sourceObj ->
                    compareObj(sourceObj,
                            sourceList.get((sourceList.indexOf(sourceObj) + 1 == length) ? sourceList.indexOf(sourceObj) : sourceList.indexOf(sourceObj) + 1)
                    )).collect(Collectors.toList());
        }
    }

    /**
     * 集合比较变化
     * 默认排序：最新的在后面
     * 即，每一个元素和前一个元素相比
     *
     * @param sourceList 原数据集合
     * @param <T>        目标类（要求继承BaseCompareFlagBean）
     * @return 附带变化比较的集合
     */
    public static <T> List<T> compareList(List<T> sourceList) {
        return compareList(sourceList, true);
    }
}
