package com.hzw.sunflower.constant.constantenum;

/**
 * 逻辑删除状态枚举
 */
public enum IsDeleteEnum {

    /**
     * 项目状态
     */
    NORMAL(0, "正常"),
    DELETE(1, "删除");

    private Integer type;
    private String desc;

    IsDeleteEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
