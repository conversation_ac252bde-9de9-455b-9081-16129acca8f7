package com.hzw.ssm.sys.system.entity;

import java.util.Date;

import com.hzw.ssm.fw.base.BaseEntity;
import com.hzw.ssm.fw.util.Page;

public class SystemLogEntity extends BaseEntity {

	/**
	 * 系统日志实体类
	 */
	private static final long serialVersionUID = 8042703708357357837L;
	
	/**日志编号*/
	private String LOG_ID;
	
	/**操作人*/
	private String OPA_USER;
	
	/**操作时间*/
	private Date OPA_TIME;
	
	/**操作IP地址*/	
	private String OPA_IP;
	
	/**操作模块*/
	private String OPA_FUN;
	
	/**操作方法名*/
	private String OPA_METHOD;
	
	/**开始时间*/
	private String start_date;
	
	/**结束时间*/
	private String end_date;
	
	/** 分页 */
	private Page page;

	public String getLOG_ID() {
		return LOG_ID;
	}

	public void setLOG_ID(String log_id) {
		LOG_ID = log_id;
	}

	public String getOPA_USER() {
		return OPA_USER;
	}

	public void setOPA_USER(String opa_user) {
		OPA_USER = opa_user;
	}

	public String getOPA_IP() {
		return OPA_IP;
	}

	public void setOPA_IP(String opa_ip) {
		OPA_IP = opa_ip;
	}

	public String getOPA_FUN() {
		return OPA_FUN;
	}

	public void setOPA_FUN(String opa_fun) {
		OPA_FUN = opa_fun;
	}

	public String getOPA_METHOD() {
		return OPA_METHOD;
	}

	public void setOPA_METHOD(String opa_method) {
		OPA_METHOD = opa_method;
	}

	public Page getPage() {
		return page;
	}

	public void setPage(Page page) {
		this.page = page;
	}

	public Date getOPA_TIME() {
		return OPA_TIME;
	}

	public void setOPA_TIME(Date opa_time) {
		OPA_TIME = opa_time;
	}

	public String getStart_date() {
		return start_date;
	}

	public void setStart_date(String start_date) {
		this.start_date = start_date;
	}

	public String getEnd_date() {
		return end_date;
	}

	public void setEnd_date(String end_date) {
		this.end_date = end_date;
	}

	
}
