package com.hzw.sunflower.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.hzw.sunflower.common.BaseBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(description = "项目字段详细信息配置表-采购方式")
@TableName("s_column_purchase_config")
@Data
public class ColumnPurchaseConfig  extends BaseBean {

    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("一级采购方式 1.依法必招 2.非依法必招 3.政府采购 4.国际招标")
    private Integer purchaseType;

    @ApiModelProperty("二级采购方式 1.招标 2.竞争性磋商 3.竞争性谈判 4.询价 5.征询 6.单一来源 7.其他")
    private Integer purchaseMode;

    @ApiModelProperty("三级采购方式 1.预审公开 2.后审公开 3.邀请 4.二阶段 5.政府公开 4.政府邀请")
    private Integer purchaseStatus;

    @ApiModelProperty("是否开放 1.是 2.否")
    private Integer isOpen;

}
