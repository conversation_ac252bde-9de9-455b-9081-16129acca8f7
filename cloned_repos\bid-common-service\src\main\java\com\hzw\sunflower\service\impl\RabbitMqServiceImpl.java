package com.hzw.sunflower.service.impl;

import com.hzw.sunflower.service.RabbitMqService;
import org.springframework.amqp.core.AmqpTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


/**
 * 消息队列 Service接口实现
 *
 * <AUTHOR>
 * @version 1.0.0 2021-04-21
 */
@Service
public class RabbitMqServiceImpl implements RabbitMqService {

    @Autowired
    private AmqpTemplate rabbitTemplate;


    @Override
    public void sendMq(Long projectId) {
//        rabbitTemplate.convertAndSend("iris_project_id", projectId);
    }
}