package com.hzw.ssm.sys.project.action;

import java.io.PrintWriter;
import java.util.ArrayList;
import java.util.List;

import org.apache.struts2.convention.annotation.Action;
import org.apache.struts2.convention.annotation.Namespace;
import org.apache.struts2.convention.annotation.ParentPackage;
import org.apache.struts2.convention.annotation.Result;
import org.apache.struts2.convention.annotation.Results;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import com.hzw.ssm.fw.base.BaseAction;
import com.hzw.ssm.fw.util.Page;
import com.hzw.ssm.fw.util.SysConstants;
import com.hzw.ssm.sys.project.entity.ProjectEntity;
import com.hzw.ssm.sys.project.service.ProjectService;
import com.hzw.ssm.sys.project.service.VoiceProjectService;
import com.hzw.ssm.sys.system.entity.SmsRecordEntity;
import com.hzw.ssm.sys.system.service.SmsRecordService;
import com.hzw.ssm.sys.user.entity.UserEntity;

/**
 * 未收到短信的
 * <AUTHOR>
 *
 */
@Namespace("/noMessageReceived")
@ParentPackage(value = "default")
@Results( { 
	@Result(name = "initList", location = "/jsp/voice/noMessageReceivedList.jsp"),
			
})
public class NoMessageReceivedAction extends BaseAction {
	private static final long serialVersionUID = -8823858495799309882L;
	
	private SmsRecordEntity smsRecord;
	private List<SmsRecordEntity> smsList;//专家抽取结果列表

	@Autowired
	private SmsRecordService smsRecordService;
	/**
	 * 初始化页面
	 * 
	 */
	@Action("initList")
	public String initList() {
		this.context();
		if(null==smsRecord){
			smsRecord = new SmsRecordEntity();
			smsRecord.setIs_dispose("0");
		}
		smsRecord.setPage(this.getPage());
		//默认查询未处理的
		 //获得5分钟前发送短信但是失败，且没有通知综合处的短信列表
		smsList=smsRecordService.queryPageUnreceivedSmsRecordList(smsRecord);
		
		return "initList";
	}
	
	/**
	 * 修改短信是否处理状态
	 * @return
	 */
	@Action("updateSmsStatus")
	public String updateSmsStatus(){
		this.context();
		this.getResponse().setCharacterEncoding("utf-8");
		PrintWriter out = null;
		String msg = "";
		try {
			out = this.getResponse().getWriter();
			
			if(smsRecord.getSms_id()=="" || smsRecord.getSms_id()==null){
				msg = "短信主键不能为空！";
			}else{
				this.getResponse().getWriter();
				smsRecord.setIs_dispose("1");
				smsRecordService.updateSmsIsDispose(smsRecord);
				msg="success";
			}
			out.print(msg);
			
			out.close();
		} catch (Exception e) {
			e.printStackTrace();
			this.setAlertMessage("处理失败！");
		}
		return null;
	}
	public SmsRecordEntity getEntity() {
		return smsRecord;
	}
	public void setEntity(SmsRecordEntity entity) {
		this.smsRecord = entity;
	}
	public List<SmsRecordEntity> getSmsList() {
		return smsList;
	}
	public void setSmsList(List<SmsRecordEntity> smsList) {
		this.smsList = smsList;
	}
	public SmsRecordService getSmsRecordService() {
		return smsRecordService;
	}
	public void setSmsRecordService(SmsRecordService smsRecordService) {
		this.smsRecordService = smsRecordService;
	}
	public SmsRecordEntity getSmsRecord() {
		return smsRecord;
	}
	public void setSmsRecord(SmsRecordEntity smsRecord) {
		this.smsRecord = smsRecord;
	}

	

	
	
}
