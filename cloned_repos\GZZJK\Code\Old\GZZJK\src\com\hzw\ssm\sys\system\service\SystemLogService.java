package com.hzw.ssm.sys.system.service;

import java.util.List;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.hzw.ssm.fw.base.BaseService;
import com.hzw.ssm.sys.system.dao.SystemLogMapper;
import com.hzw.ssm.sys.system.entity.SystemLogEntity;

/**
 * 系统日志操作类
 * 
 * <AUTHOR>
 * 
 */
@Service
public class SystemLogService extends BaseService {

	private static Log log = LogFactory.getLog(SystemLogService.class);

	@Autowired
	private SystemLogMapper logMapper;

	/**
	 * 查询系统日志
	 * 
	 * @param logEntity
	 * @return
	 */
	public List<SystemLogEntity> queryPageLog(SystemLogEntity logEntity) {
		return logMapper.queryPageLog(logEntity);
	}

	/**
	 * 保存日志信息
	 */
	public void saveSystemLog(SystemLogEntity logEntity) {
		logMapper.saveSystemLog(logEntity);
	}

}
