package com.hzw.sunflower.constant.constantenum;

/**
 * <AUTHOR>
 * @date ：Created in 2021/6/29 10:02
 * @description： 招标文件类型
 * @version: 1.0
 */
public enum AnnexTypeEnum {
    BIDNOTICE(1, "招标公告文件"),

    BIDDOC(2, "招标文件附件"),

    BIDACCESSORY(3, "招标文件附件（图纸，工程量清单）");

    private Integer value;

    private String name;

    AnnexTypeEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    public Integer getValue() {
        return value;
    }

    public String getName() {
        return name;
    }

}
