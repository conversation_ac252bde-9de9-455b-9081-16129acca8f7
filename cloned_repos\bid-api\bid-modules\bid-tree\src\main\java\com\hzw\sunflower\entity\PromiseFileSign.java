package com.hzw.sunflower.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.hzw.sunflower.common.BaseBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @datetime 2022/11/04 8:35
 * @description: 项目承诺文件签署表
 * @version: 1.0
 */
@ApiModel("项目承诺文件签署表")
@TableName("t_promise_file_sign")
@Data
public class PromiseFileSign extends BaseBean implements Serializable {

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "专家用户ID")
    private Long userId;

    @ApiModelProperty(value = "承诺文件表ID")
    private Long promiseFileId;

    @ApiModelProperty(value = "oss文件ID")
    private Long fileOssId;

    @ApiModelProperty(value = "会议室id")
    private Long conferenceId;


}
