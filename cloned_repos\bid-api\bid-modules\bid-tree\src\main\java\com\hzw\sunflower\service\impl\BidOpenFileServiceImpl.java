package com.hzw.sunflower.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzw.sunflower.controller.request.StartDecSmsReq;
import com.hzw.sunflower.controller.response.BidFileInfo;
import com.hzw.sunflower.controller.response.BidOpenEndVo;
import com.hzw.sunflower.controller.response.BidOpenSupplierListVO;
import com.hzw.sunflower.dao.BidOpenFileMapper;
import com.hzw.sunflower.dto.BidOpenDTO;
import com.hzw.sunflower.dto.BidOpenFileDto;
import com.hzw.sunflower.entity.BidOpen;
import com.hzw.sunflower.entity.BidOpenFile;
import com.hzw.sunflower.service.BidOpenFileService;
import com.hzw.sunflower.service.BidOpenService;
import com.hzw.sunflower.service.BidOpenSupplierService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * <AUTHOR>
 * @date ：Created in 2021/8/27 13:51
 * @description：
 * @version: 1.0
 */
@Service
public class BidOpenFileServiceImpl extends ServiceImpl<BidOpenFileMapper, BidOpenFile> implements BidOpenFileService {

    @Autowired
    private BidOpenSupplierService bidOpenSupplierService;

    @Autowired
    private BidOpenService bidOpenService;

    /**
     * 存储开标记录表
     * @param bidOpenFile
     * @return
     */
    @Override
    public Boolean saveFile(BidOpenFile bidOpenFile) {
        BidOpenFile file = this.getOne(new LambdaQueryWrapper<BidOpenFile>().eq(BidOpenFile::getSectionId, bidOpenFile.getSectionId()));
        if (file != null) {
            file.setFileOssId(bidOpenFile.getFileOssId());
        } else {
            file = new BidOpenFile();
            BeanUtils.copyProperties(bidOpenFile, file);
        }
        return this.saveOrUpdate(file);
    }

    /**
     * 根据标段查询开标记录表文件
     * @param sectionId
     * @return
     */
    @Override
    public BidOpenFileDto getOpenFileBySectionId(Long sectionId) {
        return this.baseMapper.getOpenFileBySectionId(sectionId);
    }

    @Override
    public List<BidFileInfo> listBidOpenFile(StartDecSmsReq req) {
        return this.baseMapper.listBidOpenFile(req.getSectionId());
    }

    /**
     * 查询结束开标数据
     * @param dto
     * @return
     */
    @Override
    public BidOpenEndVo getBidOpenEndData(BidOpenDTO dto) {
        BidOpenEndVo vo = new BidOpenEndVo();
        // 获取开标结束时间
        LambdaQueryWrapper<BidOpen> qw = new LambdaQueryWrapper<>();
        qw.eq(BidOpen::getOnlineConferenceId,dto.getOnlineConferenceId());
        qw.eq(BidOpen::getSectionId,dto.getSectionId());
        qw.eq(BidOpen::getBidRound,dto.getBidRound());
        BidOpen bidOpen = bidOpenService.getBaseMapper().selectOne(qw);
        if (bidOpen != null) {
            vo.setEndTime(bidOpen.getEndTime());
        }
        // 获取开标记录表文件
        BidOpenFileDto bidOpenFile = this.getOpenFileBySectionId(dto.getSectionId());
        if (bidOpenFile != null) {
            vo.setFileOssId(bidOpenFile.getFileOssId());
            vo.setOssFileName(bidOpenFile.getOssFileName());
            vo.setOssFileKey(bidOpenFile.getOssFileKey());
        }
        // 获取无异议的供应商列表
        List<BidOpenSupplierListVO> supplierList = bidOpenSupplierService.findSupplierNoObjection(dto.getOnlineConferenceId(), dto.getSectionId(), dto.getBidRound());
        vo.setSupplierList(supplierList);
        return vo;
    }

}
