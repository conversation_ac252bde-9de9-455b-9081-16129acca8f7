package com.hzw.ssm.log.entity;

import java.util.Date;

/**
 * web service日志 <一句话功能简述> web service日志服务实体类 <功能详细描述>
 * 
 * <AUTHOR>
 * @version V1.0[产品/模块版本]
 */
public class WebServiceLogEntity {
	/**
	 * 服务唯一标识符
	 */
	private String serviceId;
	/**
	 * 服务类型
	 */
	private String serviceType;
	/**
	 * 条件id
	 */
	private String serviceCid;
	/**
	 * 服务入参
	 */
	private String serviceInXml;
	/**
	 * 服务出参
	 */
	private String serviceOutXml;
	/**
	 * 日志创建时间
	 */
	private Date createTime;
	
	/**
	 * 日志更新时间
	 */
	private Date updateTime;

	public String getServiceId() {
		return serviceId;
	}

	public void setServiceId(String serviceId) {
		this.serviceId = serviceId;
	}

	public String getServiceType() {
		return serviceType;
	}

	public void setServiceType(String serviceType) {
		this.serviceType = serviceType;
	}

	public String getServiceCid() {
		return serviceCid;
	}

	public void setServiceCid(String serviceCid) {
		this.serviceCid = serviceCid;
	}

	public String getServiceInXml() {
		return serviceInXml;
	}

	public void setServiceInXml(String serviceInXml) {
		this.serviceInXml = serviceInXml;
	}

	public String getServiceOutXml() {
		return serviceOutXml;
	}

	public void setServiceOutXml(String serviceOutXml) {
		this.serviceOutXml = serviceOutXml;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

}
