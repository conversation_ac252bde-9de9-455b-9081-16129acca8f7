<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!-- app已办记录表 (app_to_do_record) -->
<mapper namespace="com.hzw.sunflower.dao.AppToDoRecordMapper">

    <!-- 分页查询模板 -->
    <select id="getListByCondition" resultType="com.hzw.sunflower.dto.AppToDoRecordDto">
        SELECT
            r.*,
            p.purchase_name,
            p.purchase_number
        FROM
            app_to_do_record r
        LEFT JOIN t_project p ON p.id = r.project_id
        where r.is_delete = 0
        <if test="condition.keyWords != null and condition.keyWords != ''">
            and (r.title like concat('%',#{condition.keyWords},'%')
                or p.purchase_name like concat('%',#{condition.keyWords},'%')
                or p.purchase_number like concat('%',#{condition.keyWords},'%')
            )
        </if>
        <if test="condition.type != null and condition.type != '' and condition.type != 'total' and condition.type.size > 0">
            and r.type in
            <foreach collection="condition.type" item="item" open="(" separator="," close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="condition.createdUserId != null">
            and r.created_user_id = #{condition.createdUserId}
        </if>
        ORDER BY r.created_time desc
    </select>
    <select id="queryListByCondition" resultType="com.hzw.sunflower.dto.AppToDoRecordDto">
        SELECT
        r.*,
        p.purchase_name,
        p.purchase_number
        FROM
        app_to_do_record r
        LEFT JOIN t_project p ON p.id = r.project_id
        where r.is_delete = 0
        <if test="condition.type != null and condition.type != '' and condition.type != 'total' and condition.type.size > 0">
            and r.type in
            <foreach collection="condition.type" item="item" open="(" separator="," close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="condition.createdUserId != null">
            and r.created_user_id = #{condition.createdUserId}
        </if>
        <if test="condition.departId != null">
            and (r.created_user_depart_id is null or r.created_user_depart_id = #{condition.departId})
        </if>
        ORDER BY r.created_time desc
    </select>
</mapper>
