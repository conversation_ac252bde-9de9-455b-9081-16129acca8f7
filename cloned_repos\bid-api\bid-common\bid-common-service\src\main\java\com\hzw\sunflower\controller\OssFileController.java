package com.hzw.sunflower.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hzw.sunflower.common.BaseController;
import com.hzw.sunflower.common.Paging;
import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.common.annotation.Log;
import com.hzw.sunflower.constant.MessageConstants;
import com.hzw.sunflower.constant.constantenum.BusinessType;
import com.hzw.sunflower.entity.OssFile;
import com.hzw.sunflower.entity.condition.OssFileCondition;
import com.hzw.sunflower.service.OssFileService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;


/**
 * OSS附件 Controller
 *
 * <AUTHOR>
 * @version 1.0.0 2021-04-21
 */
@Api(tags = "OSS附件 服务")
@RestController
@RequestMapping("/ossFile")
public class OssFileController extends BaseController {

    @Autowired
    private OssFileService ossFileService;

    @Value("${oss.active}")
    private String ossType;

    @ApiOperation(value = "根据参数分页查询OSS附件 列表")
    @ApiImplicitParam(name = "condition", value = "OSS附件 查询条件", required = true, dataType = "OssFileCondition", paramType = "body")
    @PostMapping("/list")
    public Result<Paging<OssFile>> list(@RequestBody OssFileCondition condition) throws Exception {
        IPage<OssFile> page = ossFileService.findOssFileByCondition(condition);
        return Result.ok(Paging.buildPaging(page));
    }

    @ApiOperation(value = "根据主键ID查询OSS附件 信息")
    @ApiImplicitParam(name = "id", value = "主键ID", required = true)
    @GetMapping(value = "/get/{id}")
    public Result<OssFile> get(@PathVariable Long id) throws Exception {
        if (id == null) {
            return Result.failed(MessageConstants.GET_NULL);
        }
        OssFile ossFile = ossFileService.getOssFileById(id);
        return Result.ok(ossFile);
    }

    @ApiOperation(value = "新增OSS附件 信息")
    @Log(title = "OSS附件", description = "新增OSS附件", businessType = BusinessType.INSERT)
    @ApiImplicitParam(name = "ossFile", value = "OSS附件 ", required = true, dataType = "OssFile", paramType = "body")
    @PostMapping("/add")
    public Result add(@RequestBody OssFile ossFile) throws Exception {
        Boolean bool = ossFileService.addOssFile(ossFile);
        if (bool) {
            return Result.ok(ossFile);
        }
        return Result.okOrFailed(bool);
    }

    @ApiOperation(value = "修改OSS附件 信息")
    @Log(title = "OSS附件", description = "修改OSS附件", businessType = BusinessType.UPDATE)
    @ApiImplicitParam(name = "ossFile", value = "OSS附件 ", required = true, dataType = "OssFile", paramType = "body")
    @PutMapping(value = "/update")
    public Result<Boolean> update(@RequestBody OssFile ossFile) throws Exception {
        Long id = ossFile.getId();
        if (id == null) {
            return Result.failed(MessageConstants.UPDATE_NULL);
        }
        Boolean bool = ossFileService.updateOssFile(ossFile);
        return Result.okOrFailed(bool);
    }

    @ApiOperation(value = "根据主键ID删除OSS附件 ")
    @Log(title = "OSS附件", description = "删除OSS附件", businessType = BusinessType.DELETE)
    @ApiImplicitParam(name = "id", value = "主键ID", required = true)
    @DeleteMapping(value = "/delete/{id}")
    public Result<Boolean> delete(@PathVariable Long id) throws Exception {
        if (id == null) {
            return Result.failed(MessageConstants.DELETE_NULL);
        }
        Boolean bool = ossFileService.deleteOssFileById(id);
        return Result.okOrFailed(bool);
    }

    @ApiOperation(value = "根据主键ID列表批量删除OSS附件 ")
    @Log(title = "OSS附件", description = "批量删除OSS附件", businessType = BusinessType.DELETE)
    @ApiImplicitParam(name = "idList", value = "主键ID列表", required = true, allowMultiple = true, paramType = "body")
    @DeleteMapping("/deleteList")
    public Result<Boolean> deleteList(@RequestBody List<Long> idList) throws Exception {
        if (CollectionUtils.isEmpty(idList)) {
            return Result.failed(MessageConstants.DELETE_NULL);
        }
        Boolean bool = ossFileService.deleteOssFileByIds(idList);
        return Result.okOrFailed(bool);
    }

    @ApiOperation(value = "返回当前对象存储类型")
    @PostMapping("/getOssType")
    public Result<String> getOssType(){
        return Result.ok(ossType);
    }

    /**
     * ossFileKey
     * @param request
     * @param response
     * @param ossFile
     */
    @ApiOperation(value = "根据附件key，下载附件，以流的形式输出（pdf/word）")
    @PostMapping("/outFileByOssKey")
    public void outFileByOssKey(HttpServletRequest request, HttpServletResponse response, @RequestBody OssFile ossFile){
        ossFileService.outFileByOssKey(request,response,ossFile);
    }

}
