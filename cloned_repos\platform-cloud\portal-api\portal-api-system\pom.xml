<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.hzw</groupId>
        <artifactId>portal-api</artifactId>
        <version>1.0.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>portal-api-system</artifactId>
    <version>1.0.0</version>
    <description>
        portal-api-system系统接口模块
    </description>

    <dependencies>

        <!-- hzw Common Core-->
<!--        <dependency>
            <groupId>com.hzw</groupId>
            <artifactId>portal-common-core</artifactId>
        </dependency>-->
        <dependency>
            <groupId>com.hzw</groupId>
            <artifactId>portal-common-basebean</artifactId>
        </dependency>
        <dependency>
            <groupId>jakarta.validation</groupId>
            <artifactId>jakarta.validation-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.hzw</groupId>
            <artifactId>portal-common-excel</artifactId>
       </dependency>

    </dependencies>

</project>
