package com.hzw.sunflower.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.hzw.sunflower.common.BaseBean;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * Created by sz on 2021/06/30
 */
@ApiModel("招标文件关联信息表")
@TableName("t_project_bid_doc_relation")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProjectBidDocRelation extends BaseBean {

    private static final long serialVersionUID = -7224757432567936096L;
    /**
     * 主键ID
     *
     * @mbg.generated
     */
    private Long id;

    /**
     * 招标文件ID
     *
     * @mbg.generated
     */
    private Long docId;

    /**
     * 项目ID
     *
     * @mbg.generated
     */
    private Long projectId;

    /**
     * 标段ID
     *
     * @mbg.generated
     */
    private Long sectionId;

    /**
     * 招标文件发售结束时间类型（1：确定时间; 2：另行通知; 3：投标文件递交截止时间前一天）
     *
     * @mbg.generated
     */
    private Integer saleEndTimeType;

    /**
     * 招标文件发售结束时间
     *
     * @mbg.generated
     */
    private Date saleEndTime;

    /**
     * 投标文件递交截止时间类型（1：确定时间; 2：另行通知）
     *
     * @mbg.generated
     */
    private Integer submitEndTimeType;

    /**
     * 投标文件递交截止时间
     *
     * @mbg.generated
     */
    private Date submitEndTime;

    /**
     * 开标地点
     *
     * @mbg.generated
     */
    private String openBidAddress;
}
