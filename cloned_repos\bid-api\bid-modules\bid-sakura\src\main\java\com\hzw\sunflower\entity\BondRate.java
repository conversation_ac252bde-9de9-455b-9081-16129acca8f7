package com.hzw.sunflower.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hzw.sunflower.common.BaseBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 保证金利率表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-15
 */
@Getter
@Setter
@TableName("t_bond_rate")
@ApiModel(value = "BondSplit对象", description = "保证金利率表")
public class BondRate extends BaseBean implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    @TableId("id")
    private Long id;

    @ApiModelProperty("利率")
    @TableField("rate")
    private BigDecimal rate;

    @ApiModelProperty("调整之前的利率")
    @TableField("before_rate")
    private BigDecimal beforeRate;

    @ApiModelProperty("调整之前的时间")
    private Date beforeDate;

    @ApiModelProperty("生效日期")
    private Date effectDate;

    @ApiModelProperty("类型：1:保证金")
    @TableField("type")
    private String type;

    @ApiModelProperty("是否是默认的  1：是  2：否")
    private Integer isDefault;

    @ApiModelProperty("银行类型")
    private Integer bankType;
}
