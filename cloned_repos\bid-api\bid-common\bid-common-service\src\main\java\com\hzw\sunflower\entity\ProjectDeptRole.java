package com.hzw.sunflower.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.hzw.sunflower.common.BaseBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@TableName("t_project_dept_role")
@ApiModel(value = "t_project_dept_role", description = "项目所属部门及创建用户角色关系")
public class ProjectDeptRole extends BaseBean {

    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("项目id")
    private Long projectId;

    @ApiModelProperty("采购编号")
    private String purchaseNumber;

    @ApiModelProperty("处室id")
    private Long deptId;

    @ApiModelProperty("创建用户角色id")
    private Long roleId;
}
