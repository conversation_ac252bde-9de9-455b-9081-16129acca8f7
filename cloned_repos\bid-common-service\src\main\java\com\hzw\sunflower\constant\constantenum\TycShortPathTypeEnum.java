package com.hzw.sunflower.constant.constantenum;

/**
 * 天眼查最短路径查询类型枚举
 *
 * <AUTHOR>
 */
public enum TycShortPathTypeEnum {

    ALL("ALL", "所有类型"),

    OWN("OWN", "法定代表⼈"),
    SERVE_ALL("SERVE_ALL", "任职"),
    INVEST("INVEST", "投资"),
    BRANCH("BRANCH", "分⽀机构"),
    LAW("LAW", "法律诉讼⽀机构（包含：开庭公告+裁判⽂书+失信⼈+司法拍卖+法院公告）"),
    TM_M("TM_M", "同商标"),
    EQUITY_RELATION_M("EQUITY_RELATION_M", "股权出质"),
    CI("CI", "疑似同联系⽅式（包含：同电话+同地址+同邮箱）"),

    COURTNOTICES_M("COURTNOTICES_M", "开庭公告"),
    JUDICIALSALE_M("JUDICIALSALE_M", "司法拍卖"),
    LAWSUIT_All("LAWSUIT_All", "裁判⽂书"),
    DISHONEST_M("DISHONEST_M", "失信人"),
    ANNOUNCEMENT_RELATION_M("ANNOUNCEMENT_RELATION_M", "法院公告"),
    EQ("EQ", "质押抵押（包含：股权质押+ 动产抵押+ ⼟地抵押+ 股权出质）"),
    STOCK_PLEDGE_M("STOCK_PLEDGE_M", "股权质押"),
    MORTGAGE_M("MORTGAGE_M", "动产抵押"),
    LAND_MORTGAGE_M("LAND_MORTGAGE_M", "⼟地抵押"),

    CAC("CAC", "竞争合作（包含：供应商+客户+对外担保+招投标+同商标）"),
    SUPPLY_M("SUPPLY_M", "供应商"),
    CLIENT_M("CLIENT_M", "客户"),
    FOREIGN_GUARANTEE_M("FOREIGN_GUARANTEE_M", "对外担保"),
    BID_M("BID_M", "招投标"),


    TELEPHONE_M("TELEPHONE_M", "同电话"),
    ADDRESS_M("ADDRESS_M", "同地址"),
    EMAIL_M("EMAIL_M", "同邮箱"),
    HISTORICAL_SHAREHOLDER("HISTORICAL_SHAREHOLDER", "历史股东"),
    HISTORICAL_SERVE_ALL("HISTORICAL_SERVE_ALL", "历史董监高"),
    HISTORICAL_OWN("HISTORICAL_OWN", "历史法人");

    private String type;
    private String desc;

    TycShortPathTypeEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
