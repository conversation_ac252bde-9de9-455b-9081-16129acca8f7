<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzw.sunflower.dao.BondWaterlabelMapper">


    <select id="getWaterlabelCheck" resultType="java.lang.Integer">
        SELECT
            count(1)
        FROM
            ( SELECT * FROM t_bond_water_label WHERE  water_id = #{waterId} AND id IN ( SELECT MAX( id ) FROM t_bond_water_label GROUP BY water_id ) ) wl
        WHERE
            wl.label != 1
    </select>


    <select id="chekecWaterLabel" resultType="java.lang.Integer">
        SELECT
        count(1)
        FROM
        t_bond_water w
        LEFT JOIN ( SELECT * FROM t_bond_water_label WHERE id IN ( SELECT MAX( id ) FROM t_bond_water_label GROUP BY water_id ) ) wl ON wl.water_id = w.id
        LEFT JOIN t_bond_water_return wr on wr.water_id = w.id
        WHERE
        1 = 1
        AND w.is_delete = 0
        AND wl.is_delete = 0
        AND wl.label != 1
        <if test="null != waterIds and  waterIds.size > 0">
            and w.id in
            <foreach collection="waterIds" item="id"  open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
    </select>

</mapper>
