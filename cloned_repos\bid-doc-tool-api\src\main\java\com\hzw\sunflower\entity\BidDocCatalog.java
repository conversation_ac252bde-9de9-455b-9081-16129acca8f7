package com.hzw.sunflower.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;

import com.hzw.sunflower.common.BaseBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 招标文件编制目录表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-26
 */
@Getter
@Setter
@TableName("t_bid_doc_catalog")
@ApiModel(value = "BidDocCatalog对象", description = "招标文件编制目录表")
public class BidDocCatalog extends BaseBean implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("目录名称")
    @TableField("catalog_name")
    private String catalogName;

    @ApiModelProperty("是否启用 1.是 2.否")
    @TableField("status")
    private Integer status;

}
