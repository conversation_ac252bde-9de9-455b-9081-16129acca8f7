package com.hzw.sunflower.controller;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hzw.sunflower.common.BaseController;
import com.hzw.sunflower.common.Paging;
import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.common.annotation.Log;
import com.hzw.sunflower.common.annotation.RepeatSubmit;
import com.hzw.sunflower.constant.MessageConstants;
import com.hzw.sunflower.constant.constantenum.BusinessType;
import com.hzw.sunflower.controller.request.DictionaryReq;
import com.hzw.sunflower.controller.response.DictionaryVO;
import com.hzw.sunflower.controller.response.SubjectMatterVo;
import com.hzw.sunflower.dto.DictionaryDTO;
import com.hzw.sunflower.entity.Dictionary;
import com.hzw.sunflower.entity.condition.DictionaryCondition;
import com.hzw.sunflower.service.DictionaryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

/**
 * 字典表 Controller
 *
 * <AUTHOR>
 * @version 1.0.0 2021-04-07
 */
@Api(tags = "字典表 服务")
@RestController
@RequestMapping("/dictionary")
public class DictionaryController extends BaseController {

    @Autowired
    private DictionaryService dictionaryService;

    @ApiOperation(value = "根据条件分页查询字典表 列表")
    @ApiImplicitParam(name = "condition", value = "字典表 查询条件", required = true, dataType = "DictionaryCondition", paramType = "body")
    @PostMapping("/list")
    public Result<Paging<Dictionary>> list(@RequestBody DictionaryCondition condition) {
        IPage<Dictionary> page = dictionaryService.findDictionaryByCondition(condition);
        return Result.ok(Paging.buildPaging(page));
    }


    @ApiOperation(value = "根据条件查询字典表 列表")
    @ApiImplicitParam(name = "condition", value = "字典表 查询条件", required = true, dataType = "DictionaryCondition", paramType = "body")
    @PostMapping("/getByConditionDicList")
    public Result<List<DictionaryVO>> getByConditionDicList(@RequestBody DictionaryCondition condition) throws Exception {
        List<DictionaryDTO> byConditionDicList = dictionaryService.getByConditionDicList(condition);
        if (CollectionUtils.isEmpty(byConditionDicList)) {
            return Result.ok(new ArrayList<>());
        } else {
            return Result.ok(JSONUtil.toList(JSONUtil.toJsonStr(byConditionDicList), DictionaryVO.class));
        }

    }

    @ApiOperation(value = "该登录人上一次选择地点")
    //@ApiImplicitParam(name = "condition", value = "字典表 查询条件", required = true, dataType = "DictionaryCondition", paramType = "body")
    @GetMapping("/getByHistoryDicList")
    public Result<List<DictionaryVO>> getByHistoryDicList() {
        List<DictionaryVO> byConditionDicList = dictionaryService.getByHistoryDicList();
        if (CollectionUtils.isEmpty(byConditionDicList)) {
            return Result.ok(new ArrayList<>());
        } else {
            return Result.ok(JSONUtil.toList(JSONUtil.toJsonStr(byConditionDicList), DictionaryVO.class));
        }

    }




    @ApiOperation(value = "根据父id 查询字典表数据")
    @ApiImplicitParam(name = "dictionaryReq", value = "字典表 ", required = true, dataType = "DictionaryReq", paramType = "body")
    @PostMapping(value = "/selectByParentId")
    public Result<List<DictionaryVO>> selectDictionaryByParentId(@RequestBody DictionaryReq dictionaryReq) {
        List<DictionaryVO> dictionaries = dictionaryService.selectDictionaryByParentId(dictionaryReq);
        return Result.ok(dictionaries);
    }

    @ApiOperation(value = "根据父id 查询字典表树数据")
    @ApiImplicitParam(name = "dictionaryReq", value = "字典表 ", required = true, dataType = "DictionaryReq", paramType = "body")
    @PostMapping(value = "/selectTreeListByParentId")
    public Result<List<Dictionary>> selectTreeListByParentId(@RequestBody DictionaryReq dictionaryReq) {
        List<Dictionary> dictionaries = dictionaryService.selectTreeListByParentId(dictionaryReq);
        return Result.ok(dictionaries);
    }
    /**
     *
     * @param condition id,isDisable
     * @return
     */
    @ApiOperation(value = "启用禁用")
    @ApiImplicitParam(name = "condition", value = "字典表 查询条件", required = true, dataType = "DictionaryCondition", paramType = "body")
    @PostMapping("/updateDisable")
    public Result<Boolean> updateDisable(@RequestBody DictionaryCondition condition) {
        Boolean bool = dictionaryService.updateDisable(condition);
        return Result.okOrFailed(bool);
    }



    @ApiOperation(value = "根据主键ID查询字典表 信息")
    @ApiImplicitParam(name = "id", value = "主键ID", required = true)
    @GetMapping(value = "/get/{id}")
    public Result<Dictionary> get(@PathVariable Long id) {
        if (id == null) {
            return Result.failed(MessageConstants.GET_NULL);
        }
        Dictionary dictionary = dictionaryService.getDictionaryById(id);
        return Result.ok(dictionary);
    }

    @ApiOperation(value = "新增字典表 信息")
    @Log(title = "字典表", description = "新增字典表", businessType = BusinessType.INSERT)
    @ApiImplicitParam(name = "dictionary", value = "字典表 ", required = true, dataType = "Dictionary", paramType = "body")
    @PostMapping("/add")
    @RepeatSubmit
    public Result<Boolean> add(@RequestBody Dictionary dictionary) {
        Boolean bool = dictionaryService.addDictionary(dictionary);
        return Result.okOrFailed(bool);
    }

    @ApiOperation(value = "修改字典表 信息")
    @Log(title = "字典表", description = "修改字典表", businessType = BusinessType.UPDATE)
    @ApiImplicitParam(name = "dictionary", value = "字典表 ", required = true, dataType = "Dictionary", paramType = "body")
    @PutMapping(value = "/update")
    @RepeatSubmit
    public Result<Boolean> update(@RequestBody Dictionary dictionary) {
        Long id = dictionary.getId();
        if (id == null) {
            return Result.failed(MessageConstants.UPDATE_NULL);
        }
        Boolean bool = dictionaryService.updateDictionary(dictionary);
        return Result.okOrFailed(bool);
    }

    @ApiOperation(value = "根据主键ID删除字典表 ")
    @Log(title = "字典表", description = "删除字典表", businessType = BusinessType.DELETE)
    @ApiImplicitParam(name = "id", value = "主键ID", required = true)
    @DeleteMapping(value = "/delete/{id}")
    public Result<Boolean> delete(@PathVariable Long id) {
        if (id == null) {
            return Result.failed(MessageConstants.DELETE_NULL);
        }
        Boolean bool = dictionaryService.deleteDictionaryById(id);
        return Result.okOrFailed(bool);
    }


    @ApiOperation(value = "查询所有国家")
    @GetMapping("/selectAllCountry")
    public Result<List<Dictionary>> selectAllCountry(){
      List<Dictionary> dictionaryList =  dictionaryService.selectAllCountry();
      return Result.ok(dictionaryList);
    }



//    @ApiOperation(value = "根据主键ID列表批量删除字典表 ")
//    @Log(title = "字典表", description = "批量删除字典表", businessType = BusinessType.DELETE)
//    @ApiImplicitParam(name = "idList", value = "主键ID列表", required = true, allowMultiple = true, paramType = "body")
//    @DeleteMapping("/deleteList")
//    public Result<Boolean> deleteList(@RequestBody List<Long> idList) {
//        if (CollectionUtils.isEmpty(idList)) {
//            return Result.failed(MessageConstants.DELETE_NULL);
//        }
//        Boolean bool = dictionaryService.deleteDictionaryByIds(idList);
//        return Result.okOrFailed(bool);
//    }

    @ApiOperation(value = "删除缓存字典数据 ")
    @DeleteMapping("/deleteCache")
    public Result<Boolean> deleteCache() {
        return Result.okOrFailed(dictionaryService.deleteCache());
    }


    /**
     * parentId  父类id
     * @param dictionary
     * @return
     */
    @ApiOperation(value = "新增标地物信息")
    @Log(title = "字典表", description = "新增字典表", businessType = BusinessType.INSERT)
    @ApiImplicitParam(name = "dictionary", value = "字典表 ", required = true, dataType = "Dictionary", paramType = "body")
    @PostMapping("/saveSubjectMatter")
    @RepeatSubmit
    public Result<Boolean> saveSubjectMatter(@RequestBody Dictionary dictionary) {
        Boolean bool = dictionaryService.saveSubjectMatter(dictionary);
        return Result.okOrFailed(bool);
    }



    @ApiOperation(value = "标的物分类数量查询")
    @ApiImplicitParam(name = "dictionaryReq", value = "字典表 ", required = true, dataType = "DictionaryReq", paramType = "body")
    @PostMapping(value = "/getSubjectMatterCount")
    public Result<List<SubjectMatterVo>> getSubjectMatterCount(@RequestBody DictionaryReq dictionaryReq) {
        List<SubjectMatterVo> dictionaries = dictionaryService.getSubjectMatterCount(dictionaryReq);
        return Result.ok(dictionaries);
    }
}
