<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzw.sunflower.dao.BondWaterMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hzw.sunflower.entity.BondWater">
        <id column="id" property="id" />
        <result column="water_number" property="waterNumber" />
        <result column="date" property="date" />
        <result column="time" property="time" />
        <result column="company_name" property="companyName" />
        <result column="company_account" property="companyAccount" />
        <result column="company_bank_deposit" property="companyBankDeposit" />
        <result column="company_bank_code" property="companyBankCode" />
        <result column="amount" property="amount" />
        <result column="receive_acount" property="receiveAcount" />
        <result column="receive_bank" property="receiveBank" />
        <result column="post_script" property="postScript" />
        <result column="source" property="source" />
        <result column="abnormal_status" property="abnormalStatus" />
        <result column="created_user_id" property="createdUserId" />
        <result column="created_time" property="createdTime" />
        <result column="updated_user_id" property="updatedUserId" />
        <result column="updated_time" property="updatedTime" />
        <result column="is_delete" property="isDelete" />
        <result column="remark" property="remark" />
        <result column="version" property="version" />
    </resultMap>

    <!-- 根据标段和项目获取流水 -->
    <select id="getWaterListBySectionId" resultType="com.hzw.sunflower.entity.BondWater">
        SELECT w.* from t_bond_water w
        LEFT JOIN t_bond_relation r on r.water_id = w.id
        where r.is_delete = 0
        and r.section_id = #{sectionId}
        and r.`status` = #{relationStatus}
        and r.company_id = #{companyId}
        ORDER BY w.date asc, w.time asc
    </select>


    <select id="getWaterListBySectionIdCompleted" resultType="com.hzw.sunflower.entity.BondWater">
        SELECT w.* from t_bond_water w
        LEFT JOIN t_bond_relation r on r.water_id = w.id
        where
        r.section_id = #{sectionId}
        and r.company_id = #{companyId}
        and r.created_time = #{createdTime}
        ORDER BY w.date asc, w.time asc
    </select>
    <!-- 根据供应商名称精准搜索未关联流水 -->
    <select id="getWaterListByCompanyName" resultType="com.hzw.sunflower.entity.BondWater">
        SELECT
            w.*
        FROM
            t_bond_water w
            LEFT JOIN t_bond_relation br ON br.water_id = w.id and br.is_delete = 0
            LEFT JOIN t_bond_apply_refund bar on bar.water_id = w.id and bar.is_delete = 0
            LEFT JOIN t_bond_refund r on r.water_id = w.id and r.is_delete = 0
            LEFT JOIN(
                SELECT
                    *
                FROM
                    t_bond_water_label
                WHERE
                    id IN ( SELECT MAX( id ) FROM t_bond_water_label GROUP BY water_id )
            )wl on wl.water_id = w.id
        WHERE
            REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(w.company_name,char(32),''),char(40),''),char(41),''),char(44),''),char(46),''),'（',''),'）',''),'，','')
                =
            REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(#{companyName},char(32),''),char(40),''),char(41),''),char(44),''),char(46),''),'（',''),'）',''),'，','')
            AND br.id IS NULL
            AND bar.id is NULL
            AND r.id is NULL
            AND( wl.label = 1 or wl.label is null)
        ORDER BY w.date DESC, w.time desc
    </select>

    <!-- 分页查询流水 -->
    <select id="getListByCondition" resultType="com.hzw.sunflower.dto.BondWaterDto">
        SELECT
        distinct
        a.*,
        r.receipt_oss_file_id
        from (
            SELECT
                w.*,
                IF(br.`status` = 2,p.purchase_number, NULL) purchase_number,
                GROUP_CONCAT(distinct IF(br.`status` = 2,d.department_name, NULL)) department_name,
                IFNULL( br.`status`, 4 ) relation_status,
                MAX(br.created_time) relation_time,
                GROUP_CONCAT(distinct IF(br.`status` = 2,s.package_number, NULL)) packageNumberGroup
            FROM
                t_bond_water w
                LEFT JOIN t_bond_relation br ON br.water_id = w.id
                AND br.is_delete = 0
                LEFT JOIN t_project p ON p.id = br.project_id
                LEFT JOIN t_project_bid_section s ON s.id = br.section_id
                LEFT JOIN t_project_entrust_user eu ON eu.project_id = br.project_id
                AND eu.is_delete = 0
                AND eu.type = 1
                LEFT JOIN t_user u ON u.id = eu.user_id
                LEFT JOIN r_user_department ud ON ud.user_id = u.id  and eu.department_id = ud.department_id
                AND ud.is_delete = 0
                LEFT JOIN t_department d ON d.id = ud.department_id
                LEFT JOIN(
                SELECT
                *
                FROM
                t_bond_water_label
                WHERE
                id IN ( SELECT MAX( id ) FROM t_bond_water_label GROUP BY water_id )
                )wl on wl.water_id = w.id
            WHERE 1 = 1
                and w.is_delete = 0
                and( wl.label = 1 or wl.label is null)
                <if test="condition.startDate !=null and condition.startDate != ''">
                    and DATE_FORMAT(w.date, '%Y-%m-%d') &gt;= #{condition.startDate}
                </if>
                <if test="condition.endDate !=null and condition.endDate != ''">
                    and DATE_FORMAT(w.date, '%Y-%m-%d') &lt;= #{condition.endDate}
                </if>
                <if test="condition.relateStartDate !=null and condition.relateStartDate != ''">
                    and br.`status` = 2 and DATE_FORMAT(br.created_time, '%Y-%m-%d') &gt;= #{condition.relateStartDate}
                </if>
                <if test="condition.relateEndDate !=null and condition.relateEndDate != ''">
                    and br.`status` = 2 and DATE_FORMAT(br.created_time, '%Y-%m-%d') &lt;= #{condition.relateEndDate}
                </if>
                <if test="condition.relationStatus !=null">
                    AND IFNULL( br.`status`, 4 ) in
                    <foreach collection="condition.relationStatus" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="condition.bankName !=null and condition.bankName !=''">
                    AND w.receive_bank LIKE concat( '%', #{condition.bankName}, '%' )
                </if>
                <if test="condition.startAmount !=null and condition.startAmount != ''">
                    and w.amount &gt;= #{condition.startAmount}
                </if>
                <if test="condition.endAmount !=null and condition.endAmount != ''">
                    and w.amount &lt;= #{condition.endAmount}
                </if>
            GROUP BY w.id, br.`status`,p.purchase_number
        ) a
        LEFT JOIN t_bond_bank_receipt r on r.relation_num = a.water_number and DATE_FORMAT(r.date, '%Y%m%d') = DATE_FORMAT(a.date, '%Y%m%d') and r.receipt_type = 1 and r.is_delete = 0
        WHERE 1 = 1
        <if test="condition.purchaseNumber !=null and condition.purchaseNumber !=''">
            AND a.purchase_number LIKE concat( '%', #{condition.purchaseNumber}, '%' )
        </if>
        <if test="condition.payCompanyName !=null and condition.payCompanyName !=''">
            AND a.company_name LIKE concat( '%', #{condition.payCompanyName}, '%' )
        </if>
        <if test="condition.hasReceipt !=null and condition.hasReceipt == 1">
            and r.receipt_oss_file_id is not null
        </if>
        <if test="condition.hasReceipt !=null and condition.hasReceipt == 2">
            and r.receipt_oss_file_id is null
        </if>
        ORDER BY IF(a.relation_status = 2,1,2) ASC, IFNULL(a.relation_time,'2099-12-31') ASC, a.date ASC, a.time ASC
    </select>

    <!-- 导出流水 -->
    <select id="getListByConditionForExcel" resultType="com.hzw.sunflower.dto.BondWaterDto">
        SELECT a.*,
        r.receipt_oss_file_id
        from (
            SELECT
            w.*,
            IF(br.`status` = 2,p.purchase_number, NULL) purchase_number,
            GROUP_CONCAT(distinct IF(br.`status` = 2,d.department_name, NULL)) department_name,
            IFNULL( br.`status`, 4 ) relation_status,
            MAX(br.created_time) relation_time
            FROM
            t_bond_water w
            LEFT JOIN t_bond_relation br ON br.water_id = w.id
            AND br.is_delete = 0
            LEFT JOIN t_project p ON p.id = br.project_id
            LEFT JOIN t_project_bid_section s ON s.id = br.section_id
            LEFT JOIN t_project_entrust_user eu ON eu.project_id = br.project_id
            AND eu.is_delete = 0
            AND eu.type = 1
            LEFT JOIN t_user u ON u.id = eu.user_id
            LEFT JOIN r_user_department  ud ON ud.user_id = u.id and eu.department_id = ud.department_id
            AND ud.is_delete = 0
            LEFT JOIN t_department d ON d.id = ud.department_id
            LEFT JOIN(
            SELECT
            *
            FROM
            t_bond_water_label
            WHERE
            id IN ( SELECT MAX( id ) FROM t_bond_water_label GROUP BY water_id )
            )wl on wl.water_id = w.id
            WHERE
            1 = 1
            and w.is_delete = 0
            and( wl.label = 1 or wl.label is null)
            <if test="condition.startDate !=null and condition.startDate != ''">
                and DATE_FORMAT(w.date, '%Y-%m-%d') &gt;= #{condition.startDate}
            </if>
            <if test="condition.endDate !=null and condition.endDate != ''">
                and DATE_FORMAT(w.date, '%Y-%m-%d') &lt;= #{condition.endDate}
            </if>
            <if test="condition.relateStartDate !=null and condition.relateStartDate != ''">
                and br.`status` = 2 and DATE_FORMAT(br.created_time, '%Y-%m-%d') &gt;= #{condition.relateStartDate}
            </if>
            <if test="condition.relateEndDate !=null and condition.relateEndDate != ''">
                and br.`status` = 2 and DATE_FORMAT(br.created_time, '%Y-%m-%d') &lt;= #{condition.relateEndDate}
            </if>
            <if test="condition.relationStatus !=null">
                AND IFNULL( br.`status`, 4 ) in
                <foreach collection="condition.relationStatus" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="condition.bankName !=null and condition.bankName !=''">
                AND w.receive_bank LIKE concat( '%', #{condition.bankName}, '%' )
            </if>
            <if test="condition.startAmount !=null and condition.startAmount != ''">
                and w.amount &gt;= #{condition.startAmount}
            </if>
            <if test="condition.endAmount !=null and condition.endAmount != ''">
                and w.amount &lt;= #{condition.endAmount}
            </if>
            GROUP BY w.id, br.`status`,p.purchase_number
        ) a
        LEFT JOIN t_bond_bank_receipt r on r.relation_num = a.water_number and DATE_FORMAT(r.date, '%Y%m%d') = DATE_FORMAT(a.date, '%Y%m%d') and r.receipt_type = 1 and r.is_delete = 0
        WHERE 1 = 1
<!--        <if test="condition.keyWords !=null and condition.keyWords !=''">
            AND ( a.purchase_number LIKE concat( '%', #{condition.keyWords}, '%' ) OR a.company_name LIKE concat( '%', #{condition.keyWords}, '%' ) )
        </if>-->
        <if test="condition.purchaseNumber !=null and condition.purchaseNumber !=''">
            AND a.purchase_number LIKE concat( '%', #{condition.purchaseNumber}, '%' )
        </if>
        <if test="condition.payCompanyName !=null and condition.payCompanyName !=''">
            AND a.company_name LIKE concat( '%', #{condition.payCompanyName}, '%' )
        </if>
        <if test="condition.hasReceipt !=null and condition.hasReceipt == 1">
            and r.receipt_oss_file_id is not null
        </if>
        <if test="condition.hasReceipt !=null and condition.hasReceipt == 2">
            and r.receipt_oss_file_id is null
        </if>
        ORDER BY IF(a.relation_status = 2,1,2) ASC, IFNULL(a.relation_time,'2099-12-31') ASC, a.date ASC, a.time ASC
    </select>



    <!-- 分页查询流水 -->
    <select id="getListByConditionOther" resultType="com.hzw.sunflower.controller.response.BondWaterReturnVo">
        SELECT
            w.*,
            wl.label,
            wl.purchase_number,
            IFNULL(wr.status,0) as return_status,
            wr.return_time,
            wl.notes,
            wr.oss_file_id
        FROM
            t_bond_water w
                LEFT JOIN ( SELECT * FROM t_bond_water_label WHERE id IN ( SELECT MAX( id ) FROM t_bond_water_label GROUP BY water_id ) ) wl ON wl.water_id = w.id
                LEFT JOIN t_bond_water_return wr on wr.water_id = w.id
        WHERE
            1 = 1
          AND w.is_delete = 0
          AND wl.is_delete = 0
          AND wl.label != 1
        <if test="null != condition.bankName and condition.bankName !=''">
            AND w.receive_bank LIKE concat( '%', #{condition.bankName}, '%' )
        </if>
        <if test="null != condition.label">
            AND wl.label = #{condition.label}
        </if>
        <if test="null != condition.keyWords and condition.keyWords !=''">
            AND w.company_name LIKE concat( '%', #{condition.keyWords}, '%' )
        </if>
        ORDER BY w.date ASC, w.time ASC
    </select>






    <select id="getNotRelationWater" resultType="com.hzw.sunflower.entity.BondWater">
        select * from t_bond_water where is_delete = 0 and abnormal_status = 1 and
        id not in(
        select DISTINCT water_id from t_bond_relation where is_delete = 0 and `status` != 1
        )
    </select>

    <!-- 查询流水退还情况 -->
    <select id="getSectionExportList" resultType="com.hzw.sunflower.controller.response.BondSectionExportVo">
        SELECT
            w.id water_id,
            bs.section_id,
            s.package_number,
            bs.amount split_amount,
            br.`status` return_status
        FROM
            t_bond_water w
                LEFT JOIN t_bond_split bs ON bs.water_id = w.id
                AND bs.is_delete = 0
                LEFT JOIN t_bond_refund br ON br.is_delete = 0
                AND ( ( br.section_id = bs.section_id AND br.company_id = bs.section_id ) OR br.water_id = w.id )
                LEFT JOIN t_project_bid_section s ON s.id = bs.section_id
        WHERE
            w.is_delete = 0
          AND w.id = #{id}
    </select>


    <select id="abnormalWaterList" resultType="com.hzw.sunflower.controller.response.BondWaterRefundVO">
        select bw.*,
               br.id as refundId,
               IFNULL( br.status, 0 ) as refundStatus,
               br.file_ids
        from t_bond_water bw
        left join t_bond_refund br
        on bw.id = br.water_id  and br.is_delete = 0
        LEFT JOIN(
        SELECT
        *
        FROM
        t_bond_water_label
        WHERE
        id IN ( SELECT MAX( id ) FROM t_bond_water_label GROUP BY water_id )
        )wl on wl.water_id = bw.id
        where bw.is_delete = 0
        and bw.abnormal_status in (2,3)
        and bw.id not in (select water_id from t_bond_relation where is_delete = 0 and `status` != 1)
        and bw.id not in (select water_id from t_bond_apply_refund where is_delete = 0)
        and( wl.label = 1 or wl.label is null)
        <if test="condition.keywords != null and condition.keywords != ''">
            and bw.company_name LIKE concat( '%', #{condition.keywords}, '%' )
        </if>
        <if test="condition.status != null and condition.status.size > 0">
            AND (br.`status` in
            <foreach collection="condition.status" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            <if test="condition.status.contains(&quot;0&quot;)">
                or br.`status` is null
            </if>
                )
        </if>
        order by bw.date,bw.time
    </select>

    <select id="getWaterGroupByAccount" resultType="com.hzw.sunflower.entity.BondWater">
        SELECT
        bw.company_name,
        bw.company_account,
        bw.company_bank_deposit,
        bw.company_bank_code,
        bw.receive_bank,
        bw.receive_acount,
        sum( bs.amount ) AS amount,
        max(bw.date) as date
        FROM
        t_bond_split bs
        left join t_bond_water bw
        on bw.id = bs.water_id
        LEFT JOIN(
        SELECT
        *
        FROM
        t_bond_water_label
        WHERE
        id IN ( SELECT MAX( id ) FROM t_bond_water_label GROUP BY water_id )
        )wl on wl.water_id = bw.id
        WHERE
        bw.is_delete = 0
        and bs.is_delete = 0
        AND( wl.label = 1 or wl.label is null)
        and bs.id IN
        <foreach collection="splitIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        GROUP BY
        bw.company_name,
        bw.company_account,
        bw.company_bank_deposit,
        bw.company_bank_code,
        bw.receive_bank,
        bw.receive_acount
    </select>

    <!-- 校验当前流水是否关联过 -->
    <select id="checkIsRelation" resultType="java.lang.Integer">
        SELECT
            count(*)
        FROM
            t_bond_relation
        WHERE water_id = #{waterId} and status = 2
    </select>

    <!-- 获取带推送的隔月未关联收款流水 -->
    <select id="findNccReceiveWater" resultType="com.hzw.sunflower.dto.BondWaterNccDto">
        SELECT
            DISTINCT(w.id),
            w.*,
            CONCAT( w.date, ' ', w.time ) receiveTime,
            n.`code` customer,
            n.`id` customerId,
            CASE
                WHEN INSTR( w.receive_bank, '中国银行' ) THEN
                    ( SELECT account FROM t_bankdata_bond_to_ncc WHERE bank_type = 1 AND account_type = 2 )
                WHEN INSTR( w.receive_bank, '南京银行' ) THEN
                    ( SELECT account FROM t_bankdata_bond_to_ncc WHERE bank_type = 2 AND account_type = 2 )
                WHEN INSTR( w.receive_bank, '民生银行' ) THEN
                    ( SELECT account FROM t_bankdata_bond_to_ncc WHERE bank_type = 3 AND account_type = 2 )
                WHEN INSTR( w.receive_bank, '工商银行' ) THEN
                    ( SELECT account FROM t_bankdata_bond_to_ncc WHERE bank_type = 4 AND account_type = 2 )
                WHEN INSTR( w.receive_bank, '建设银行' ) THEN
                    ( SELECT account FROM t_bankdata_bond_to_ncc WHERE bank_type = 5 AND account_type = 2 )
                WHEN INSTR( w.receive_bank, '中信银行' ) THEN
                    ( SELECT account FROM t_bankdata_bond_to_ncc WHERE bank_type = 6 AND account_type = 2 )
                WHEN INSTR( w.receive_bank, '华夏银行' ) THEN
                    ( SELECT account FROM t_bankdata_bond_to_ncc WHERE bank_type = 7 AND account_type = 2 )
                WHEN INSTR( w.receive_bank, '农业银行' ) THEN
                    ( SELECT account FROM t_bankdata_bond_to_ncc WHERE bank_type = 8 AND account_type = 2 )
                WHEN INSTR( w.receive_bank, '浦发银行' ) THEN
                    ( SELECT account FROM t_bankdata_bond_to_ncc WHERE bank_type = 9 AND account_type = 2 )
                WHEN INSTR( w.receive_bank, '渤海银行' ) THEN
                    ( SELECT account FROM t_bankdata_bond_to_ncc WHERE bank_type = 10 AND account_type = 2 )
                WHEN INSTR( w.receive_bank, '江苏银行' ) THEN
                    ( SELECT account FROM t_bankdata_bond_to_ncc WHERE bank_type = 11 AND account_type = 2 )
                WHEN INSTR( w.receive_bank, '邮储银行' ) THEN
                    ( SELECT account FROM t_bankdata_bond_to_ncc WHERE bank_type = 12 AND account_type = 2 )
                END AS recAccount
        FROM
                t_bond_water w
                LEFT JOIN t_bond_relation br ON br.water_id = w.id
                AND br.is_delete = 0
                LEFT JOIN t_customer_to_ncc n ON n.`name` = w.company_name
                LEFT JOIN t_bond_refund r on r.water_id = w.id and r.is_delete = 0
                LEFT JOIN(
                SELECT
                    *
                FROM
                    t_bond_water_label
                WHERE
                    id IN ( SELECT MAX( id ) FROM t_bond_water_label GROUP BY water_id )
            )wl on wl.water_id = w.id
        WHERE
                DATE_FORMAT( w.date, '%Y-%m' ) = DATE_FORMAT( DATE_ADD( NOW(), INTERVAL - 1 MONTH ), '%Y-%m' )
        AND (r.id is NULL or r.`status` != 3)
        AND( wl.label = 1 or wl.label is null)
        ORDER BY
            w.date,
            w.time ASC
    </select>

    <select id="selectSupplierPage" resultType="com.hzw.sunflower.entity.BondWater">
        SELECT
            *
        FROM
            t_bond_water w
                LEFT JOIN(
                SELECT
                    *
                FROM
                    t_bond_water_label
                WHERE
                    id IN ( SELECT MAX( id ) FROM t_bond_water_label GROUP BY water_id )
            )wl on wl.water_id = w.id
        where
            w.is_delete = 0
          AND( wl.label = 1 or wl.label is null)
         AND REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(w.company_name,char(32),''),char(40),''),char(41),''),char(44),''),char(46),''),'（',''),'）',''),'，','')
                 =
             REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(#{req.companyName},char(32),''),char(40),''),char(41),''),char(44),''),char(46),''),'（',''),'）',''),'，','')
        <if test="req.date !=null and req.date != ''">
            and DATE_FORMAT(w.date, '%Y-%m-%d') = #{req.date}
        </if>
        <if test="req.amount !=null and req.amount !=''">
            and w.amount = #{req.amount}
        </if>
        <if test="req.relationWaterIds !=null and req.relationWaterIds.size > 0 ">
          and w.id not in
          <foreach collection="req.relationWaterIds" item="id" open="(" close=")" separator=",">
               #{id}
          </foreach>
        </if>
        ORDER BY w.date asc,time asc
    </select>
    <select id="abnormalWaterCount" resultType="java.lang.Integer">
        SELECT
            COUNT( 1 )
        FROM
            t_bond_water bw
            LEFT JOIN t_bond_refund br ON bw.id = br.water_id AND br.is_delete = 0
            LEFT JOIN ( SELECT * FROM t_bond_water_label WHERE id IN ( SELECT MAX( id ) FROM t_bond_water_label GROUP BY water_id ) ) wl ON wl.water_id = bw.id
        WHERE
            bw.is_delete = 0
            AND bw.abnormal_status IN ( 2, 3 )
            AND bw.id NOT IN ( SELECT water_id FROM t_bond_relation WHERE is_delete = 0 )
            AND bw.id NOT IN ( SELECT water_id FROM t_bond_apply_refund WHERE is_delete = 0 )
            AND ( wl.label = 1 OR wl.label IS NULL )
            AND ( br.`status` = 2 OR br.`status` IS NULL )
    </select>
    <select id="getWaterListByCompanyNameAll"
            resultType="com.hzw.sunflower.controller.response.BondWaterAllVo">
        SELECT DISTINCT
            w.*,
            ( CASE WHEN (br.`status` IS NOT NULL AND br.`status` = 1 AND br.is_auto = 1) THEN 1
                   WHEN (br.`status` IS NULL AND bar.`status` IS NULL AND r.`status` IS NULL) THEN 2
                   WHEN (br.`status` IS NOT NULL AND br.`status` = 3) THEN 4
                   WHEN (bar.`status` IS NOT NULL AND r.`status` IS NULL AND bar.`status` IN (1,3,4)) THEN 5
                   WHEN (bar.`status` IS NOT NULL AND r.`status` IS NULL AND bar.`status` = 2) THEN 6
                   WHEN (bar.`status` IS NOT NULL AND r.`status` IS NULL AND bar.`status` = 6) THEN 3
                   WHEN (br.`status` IS NOT NULL AND br.`status` = 2 AND (r.`status` IS NULL OR r.`status` IN (2,8))) THEN 7
                   WHEN (r.`status` IN (1,3,4,5)) THEN 8
                   ELSE 0 END ) AS waterStatus,
            r.id AS refundId
        FROM
            t_bond_water w
                LEFT JOIN t_bond_relation br ON br.water_id = w.id and br.is_delete = 0
                LEFT JOIN t_bond_apply_refund bar on bar.water_id = w.id and bar.is_delete = 0
                LEFT JOIN t_bond_refund r on ((r.company_id = br.company_id and r.section_id = br.section_id) OR  r.water_id = w.id) and r.is_delete = 0
                LEFT JOIN(
                SELECT
                    *
                FROM
                    t_bond_water_label
                WHERE
                    id IN ( SELECT MAX( id ) FROM t_bond_water_label GROUP BY water_id )
            )wl on wl.water_id = w.id
        WHERE
                REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(w.company_name,char(32),''),char(40),''),char(41),''),char(44),''),char(46),''),'（',''),'）',''),'，','')
                =
                REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(#{companyName},char(32),''),char(40),''),char(41),''),char(44),''),char(46),''),'（',''),'）',''),'，','')
          AND( wl.label = 1 or wl.label is null)
        ORDER BY waterStatus ASC, w.date DESC, w.time DESC
    </select>
</mapper>
