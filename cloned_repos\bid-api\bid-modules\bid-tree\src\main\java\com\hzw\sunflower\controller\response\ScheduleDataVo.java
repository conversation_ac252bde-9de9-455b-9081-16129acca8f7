package com.hzw.sunflower.controller.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date ：Created in 2021/7/8 15:56
 * @description：解析响应文件返回数据
 * @version: 1.0
 */
@Data
@NoArgsConstructor
public class ScheduleDataVo implements Serializable {

    @ApiModelProperty(value = "表格数据（解析）")
    private List<Map<Long, List<String>>> tableDatas;

    @ApiModelProperty(value = "表头（解析）")
    private List<String> tableTitles;

    @ApiModelProperty(value = "生成的文件id（拼接）")
    private Long fileOssId;

    @ApiModelProperty(value = "文件key")
    private String ossFileKey;

    @ApiModelProperty(value = "文件名称")
    private String ossFileName;

    @ApiModelProperty(value ="生成规则 1.直接拼接开标汇总表 2.从x行解析拼接")
    private Integer generateRule;

}
