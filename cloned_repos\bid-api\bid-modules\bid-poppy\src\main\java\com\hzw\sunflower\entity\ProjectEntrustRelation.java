package com.hzw.sunflower.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hzw.sunflower.common.BaseBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 项目委托关系表 (t_project_entrust_relation)
 *
 * <AUTHOR>
 * @version 1.0.0 2021-04-12
 */
@ApiModel(description = "项目委托关系表 ")
@TableName("t_project_entrust_relation")
@Data
public class ProjectEntrustRelation extends BaseBean {
    /**
     * 版本号
     */
    private static final long serialVersionUID = -4968824638407936858L;


    @ApiModelProperty(value = "主键", position = 1)
    private Long id;

    @ApiModelProperty(value = "项目ID", position = 2)
    private Long projectId;


}