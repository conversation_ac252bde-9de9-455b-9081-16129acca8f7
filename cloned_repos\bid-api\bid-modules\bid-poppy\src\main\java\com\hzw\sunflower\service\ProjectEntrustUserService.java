package com.hzw.sunflower.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hzw.sunflower.controller.project.request.ProjectEntrustUserREQ;
import com.hzw.sunflower.controller.project.response.ProjectEntrustUserVo;
import com.hzw.sunflower.dto.AgentInItDto;
import com.hzw.sunflower.entity.JwtUser;
import com.hzw.sunflower.entity.ProjectEntrustUser;

import java.util.List;
import java.util.Map;

/**
 * 项目委托关系人表 Service接口
 *
 * <AUTHOR>
 * @version 1.0.0 2021-04-07
 */
public interface ProjectEntrustUserService extends IService<ProjectEntrustUser> {


    /**
     * 新增项目委托关系人表 信息
     *
     * @param projectEntrustUser 项目委托关系人表 信息
     * @return 是否成功
     */
    Boolean addProjectEntrustUser(ProjectEntrustUserREQ projectEntrustUser, JwtUser user);


    /**
     * 查询委托人
     *
     * @param id
     * @param type
     * @return
     */
    List<Map<String, String>> queryPrincipalByIds(Long id, Integer type);


    /**
     * @param srcUserId
     * @param projectId
     * @return
     */
    ProjectEntrustUser getEntrustUser(Long srcUserId, Long projectId);


    /**
     * 查询项目是否是本级项目
     * @param deptId
     * @param projectId
     * @return
     */
    ProjectEntrustUser getDeptLevelProject(Long deptId, Long projectId);


    /**
     * 根据项目id查询委托人信息
     *
     * @param id
     * @return
     */
    AgentInItDto getEntrustUserByProjectIdAndType(Long id, int type);

    /**
     * 转委托修改
     *
     * @param projectEntrustUser 项目委托关系人表 信息
     * @return 是否成功
     */
    void updateProjectEntrustUserById(ProjectEntrustUser projectEntrustUser);

    /**
     * 获取项目被委托人信息
     * @param projectId
     * @return
     */
    ProjectEntrustUserVo selectEntrustUserAndDepartment(Long projectId);
}