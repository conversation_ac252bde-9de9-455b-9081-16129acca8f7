<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!-- app待阅 (app_to_read) -->
<mapper namespace="com.hzw.sunflower.dao.AppToReadMapper">

    <!-- 分页检索 -->
    <select id="listPage" resultType="com.hzw.sunflower.controller.response.AppToReadVo">
        SELECT
            r.*,
            u.user_name,
            p.purchase_number,
            p.purchase_name,
            p.package_segment_status,
            GROUP_CONCAT(DISTINCT s.package_number) AS package_numbers,
            1 as notice_type,
            dur.content AS data_update_content,
            dur.request_json as data_update_json,
            dur.type as data_update_type
        FROM
            app_to_read r
        LEFT JOIN t_user u ON u.id = r.created_user_id
        LEFT JOIN t_project p ON p.id = r.project_id AND p.is_delete = 0
        LEFT JOIN t_project_bid_section s on FIND_IN_SET(s.id,r.section_id)
        LEFT JOIN t_data_update_record dur on dur.id = r.business_id and r.business_code = 'dataModification'
        WHERE
            r.is_delete = 0
          AND u.is_delete = 0
          and r.apply_user_id = #{condition.createdUserId}
            <if test="condition.keyWords != null and condition.keyWords != ''">
                AND (
                p.purchase_number LIKE concat('%',#{condition.keyWords},'%')
                OR
                p.purchase_name LIKE concat('%',#{condition.keyWords},'%')
                OR
                r.title LIKE concat('%',#{condition.keyWords},'%')
                OR
                r.content LIKE concat('%',#{condition.keyWords},'%')
                <!--   不根据内容搜索
                OR
                dur.content LIKE concat('%',#{condition.keyWords},'%')
                -->
                )
            </if>
        GROUP BY
            r.id,
            r.project_id,
            p.purchase_number,
            p.purchase_name,
            p.package_segment_status
        ORDER BY
            r.created_time DESC
    </select>

    <!-- 消息中心全部列表 -->
    <select id="listAllPage" resultType="com.hzw.sunflower.controller.response.AppToReadVo">
        SELECT * from (
          SELECT
              r.*,
              u.user_name,
              p.purchase_number,
              p.purchase_name,
              p.package_segment_status,
              GROUP_CONCAT(DISTINCT s.package_number) AS package_numbers,
              1 as notice_type,
              null as notice_id,
              dur.content AS data_update_content,
              dur.request_json as data_update_json,
              dur.type as data_update_type
          FROM
              app_to_read r
          LEFT JOIN t_user u ON u.id = r.created_user_id
          LEFT JOIN t_project p ON p.id = r.project_id AND p.is_delete = 0
          LEFT JOIN t_project_bid_section s on FIND_IN_SET(s.id,r.section_id)
          LEFT JOIN t_data_update_record dur on dur.id = r.business_id and r.business_code = 'dataModification'
          WHERE
              r.is_delete = 0
            AND u.is_delete = 0
            and r.apply_user_id = #{condition.createdUserId}
            <if test="condition.keyWords != null and condition.keyWords != ''">
                AND (
                p.purchase_number LIKE concat('%',#{condition.keyWords},'%')
                OR
                p.purchase_name LIKE concat('%',#{condition.keyWords},'%')
                OR
                r.title LIKE concat('%',#{condition.keyWords},'%')
                OR
                r.content LIKE concat('%',#{condition.keyWords},'%')
                <!--   不根据内容搜索
                OR
                dur.content LIKE concat('%',#{condition.keyWords},'%')
                 -->
                )
            </if>
          GROUP BY
              r.id,
              r.project_id,
              p.purchase_number,
              p.purchase_name,
              p.package_segment_status
          UNION ALL
          SELECT
              ans.id,
              an.title,
              an.content,
              NULL as bussiness_code,
              NULL as business_id,
              NULL as project_id,
              NULL as section_id,
              NULL as bid_round,
              NULL as apply_user_id,
              NULL as apply_time,
              ans.is_read,
              ans.created_user_id,
              ans.created_time,
              ans.updated_user_id,
              ans.updated_time,
              ans.is_delete,
              ans.remark,
              ans.version,
              NULL as user_name,
              NULL as purchase_number,
              NULL as purchase_name,
              NULL as package_segment_status,
              NULL as package_numbers,
              2 AS notice_type,
              ans.notice_id,
              null as data_update_content,
              null as data_update_json,
              null as data_update_type
          FROM
              app_notice_scope ans
          LEFT JOIN app_notice an ON an.id = ans.notice_id
          WHERE
              an.is_delete = 0
            AND ans.is_delete = 0
            AND ans.user_id = #{condition.createdUserId}
            and ans.department_id = #{condition.departId}
            <if test="condition.keyWords != null and condition.keyWords != ''">
                AND (
                an.title LIKE concat('%',#{condition.keyWords},'%')
                OR
                an.content LIKE concat('%',#{condition.keyWords},'%')
                )
            </if>
        ) t
        ORDER BY t.created_time DESC
    </select>
    <select id="countToRead" resultType="java.lang.Long">
        SELECT
            COUNT(*)
        FROM
            app_to_read r
        LEFT JOIN t_user u ON u.id = r.created_user_id
        LEFT JOIN t_project p ON p.id = r.project_id AND p.is_delete = 0
        WHERE
            r.is_delete = 0
          AND u.is_delete = 0
          and r.apply_user_id = #{condition.createdUserId}
          and r.is_read = 2
    </select>
    <select id="countNotice" resultType="java.lang.Long">
        SELECT
            count(*)
        FROM
            app_notice_scope ans
        LEFT JOIN app_notice an ON an.id = ans.notice_id
        WHERE
            an.is_delete = 0
          AND ans.is_delete = 0
          AND ans.user_id = #{condition.createdUserId}
          AND ans.department_id = #{condition.departId}
          and ans.is_read = 2
    </select>

</mapper>
