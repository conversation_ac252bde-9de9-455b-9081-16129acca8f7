///***
/// * 信息订阅 - 订阅设置 - 筛选条件物件
/// *
/// * <AUTHOR>
/// * @date 20191016
/// */

import 'package:flutter/material.dart';

import 'package:flutter_main_app/src/constants/theme.dart';
import 'package:flutter_main_app/src/utils/ScreenUtils.dart';

import 'package:flutter_main_app/src/models/common/CheckBoxItemModel.dart';

import 'package:flutter_main_app/src/components/pickers/CustomMultipleSelectPicker.dart';

class FilterCriteria extends StatefulWidget {
  FilterCriteria({
    Key key,
    @required this.type,
    this.checkboxWidth,
    @required this.criteria,
    @required this.settings,
    this.onChange,
  })  : assert(type != null),
        assert(checkboxWidth != null && checkboxWidth > 0.0),
        assert(criteria != null),
        assert(settings != null),
        super(key: key);

  final String type;

  final double checkboxWidth;

  final List<String> criteria;

  final List<CheckBoxItemModel<String>> settings;

  final void Function(List<String>) onChange;

  @override
  _FilterCriteriaState createState() => _FilterCriteriaState();
}

class _FilterCriteriaState extends State<FilterCriteria> {

  List<String> _criteria;
  List<CheckBoxItemModel<String>> _settings;

  @override
  void initState() {
    super.initState();

    this._criteria = List.from(widget.criteria);

    this._settings = List.from(widget.settings);
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        this._handleFilterCriteriaClick(context);
      },
      child: Container(
        margin: const EdgeInsets.only(left: 15.0),
        padding: const EdgeInsets.fromLTRB(0.0, 17.0, 18.0, 17.0),
        width: ScreenUtils.screenWidth - 15.0,
        constraints: BoxConstraints(
          minHeight: 50.0,
        ),
        decoration: BoxDecoration(
          border: Border(
            bottom: themeBorderSide,
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: <Widget>[
            /// * 左侧 筛选条件类型 + 选定的筛选条件文本
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  Container(
                    constraints: BoxConstraints.tight(Size.fromHeight(17.0)),
                    child: Text(
                      widget.type,
                      style: themeTextStyle,
                      maxLines: 1,
                    ),
                  ),
                  if (this._criteria.length > 0) _buildCriterion(),
                ],
              ),
            ),

            /// * 图标
            Icon(
              Icons.arrow_forward_ios,
              size: 14.0,
              color: const Color(0xff999999),
            ),
          ],
        ),
      ),
    );
  }

  ///***
  /// * 选定的筛选条件文本
  /// */
  Widget _buildCriterion() {
    return Padding(
      padding: const EdgeInsets.only(top: 14.0),
      child: Text(
        this._criteria?.join('·'),
        style: themeTipsTextStyle,
      ),
    );
  }

  ///***
  /// * 筛选条件卡条点击事件
  /// *
  /// * @param {BuildContext} context
  /// */
  void _handleFilterCriteriaClick(BuildContext context) {
    /// * 多选框
    final Future<List<CheckBoxItemModel<String>>> modalBottomSheet =
      showModalBottomSheet<List<CheckBoxItemModel<String>>>(
        context: context,
        isScrollControlled: true,
        builder: (BuildContext context) {
          return CustomMultipleSelectPicker(
            settings: this._settings,
            checkboxWidth: widget.checkboxWidth,
          );
        },
      );

    modalBottomSheet.then((List<CheckBoxItemModel<String>> settings) {
      /// * 确认设置选中项
      if (settings != null) {
        final List<String> criteria = [];
        final List<String> values = [];

        settings.forEach((setting) {
          if (setting.isChecked) {
            criteria.add(setting.label);
            values.add(setting.value);
          }
        });

        setState(() {
          this._criteria = criteria;
          this._settings = List.from(settings);
        });

        widget.onChange(values);
      }
    });
  }

}
