///***
/// * 网络请求方法封装
/// */

import 'dart:convert' as convert;
import 'package:flutter_network_plugin/flutter_network_plugin.dart';

import 'package:flutter_main_app/src/models/common/HttpResponseModel.dart';

class HttpUtils {

  static Future<T> get<T>(
    String url,
    {
      Map<String, dynamic> params,
      bool isCachable,
      String domainName,
    }
  ) async {
    final String jsonStr = await FlutterNetworkPlugin.doGetRequest(
      url,
      params: params,
      isCachable: isCachable,
      domainName: domainName,
    );

    final T result = _convertHttpResponse<T>(jsonStr);

    return result;
  }

  static Future<T> postJson<T>(
    String url,
    {
      String json,
      bool isCachable,
      String domainName,
    }
  ) async {
    final String jsonStr = await FlutterNetworkPlugin.doPostJsonRequest(
      url,
      json: json,
      isCachable: isCachable,
      domainName: domainName,
    );

    final T result = _convertHttpResponse<T>(jsonStr);

    return result;
  }

  static Future<T> postForm<T>(
    String url,
    {
      Map<String, dynamic> params,
      bool isCachable,
      String domainName,
    }
  ) async {
    final String jsonStr = await FlutterNetworkPlugin.doPostFormRequest(
      url,
      params: params,
      isCachable: isCachable,
      domainName: domainName,
    );

    final T result = _convertHttpResponse<T>(jsonStr);

    return result;
  }

  static Future<T> putJson<T>(
    String url,
    {
      String json,
      String domainName,
    }
  ) async {
    final String jsonStr = await FlutterNetworkPlugin.doPutJsonRequest(
      url,
      json: json,
      domainName: domainName,
    );

    final T result = _convertHttpResponse<T>(jsonStr);

    return result;
  }

  static Future<T> putForm<T>(
    String url,
    {
      Map<String, dynamic> params,
      String domainName,
    }
  ) async {
    final String jsonStr = await FlutterNetworkPlugin.doPutFormRequest(
      url,
      params: params,
      domainName: domainName,
    );

    final T result = _convertHttpResponse<T>(jsonStr);

    return result;
  }

  static Future<T> delete<T>(
    String url,
    {
      Map<String, dynamic> params,
      String domainName,
    }
  ) async {
    final String jsonStr = await FlutterNetworkPlugin.doDeleteRequest(
      url,
      params: params,
      domainName: domainName,
    );

    final T result = _convertHttpResponse<T>(jsonStr);

    return result;
  }

}

///***
/// * 处理后端（原生）返回的 json 字符串
/// * 成功 - 返回 data
/// * 错误 - 处理
/// *
/// * @param {String} jsonStr
/// * @return {T} data || null
/// */
T _convertHttpResponse<T>(String jsonStr) {
  final Map<String, dynamic> json = convert.jsonDecode(jsonStr);

  final HttpResponseModel<T> httpResponse = HttpResponseModel.fromJson(json);

  T result;

  if (httpResponse?.success == true) {
    result = httpResponse.data;
  } else {
    /// * 错误处理
    /// TODO: 错误处理
    switch (httpResponse.code) {
      case 500:
      case 502:
      case 504:
      default:
        break;
    }
  }

  return result;
}
