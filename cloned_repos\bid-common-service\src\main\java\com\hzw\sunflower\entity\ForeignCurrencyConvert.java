package com.hzw.sunflower.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.hzw.sunflower.common.BaseBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
@TableName("t_foreign_currency_convert")
@ApiModel(value = "t_foreign_currency_convert", description = "外币转换表")
public class ForeignCurrencyConvert extends BaseBean {

    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("关联ID")
    private Long relationId;

    @ApiModelProperty("转换后金额")
    private BigDecimal convertMoney;

    @ApiModelProperty("现汇买入价")
    private BigDecimal buyingRate;

    @ApiModelProperty("现钞买入价")
    private BigDecimal cashPurchasePrice;

    @ApiModelProperty("现汇卖出价")
    private BigDecimal sellingRate;

    @ApiModelProperty("现钞卖出价")
    private BigDecimal cashSellingPrice;

    @ApiModelProperty("中行折算价")
    private BigDecimal conversionPrice;

    @ApiModelProperty("发布时间")
    private String publishTime;

    @ApiModelProperty("当前使用汇率")
    private String useRate;

    @ApiModelProperty("金额类型：1：项目委托金额；2：标段委托金额；3：中标价")
    private Integer type;
}
