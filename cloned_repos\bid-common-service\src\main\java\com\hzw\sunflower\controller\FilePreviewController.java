package com.hzw.sunflower.controller;

import com.hzw.sunflower.common.BaseController;
import com.hzw.sunflower.controller.request.FileMetaDataReq;
import com.hzw.sunflower.util.OssUtil;
import com.hzw.sunflower.util.oss.OssFileStorage;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

@Api(tags = "获取文件信息服务")
@Slf4j
@RestController
@RequestMapping("/filepreview")
public class FilePreviewController extends BaseController {

    @Value("${oss.active}")
    private String ossType;


    @RequestMapping(value = "/getFileMetaData", method = RequestMethod.POST)
    @ResponseBody
    public Object getFileMetaData(@RequestBody FileMetaDataReq fileMetaDataReq){
        Map<String,Object> retMap=new HashMap<>(16);

        if(!ObjectUtils.isEmpty(fileMetaDataReq.getUrlKey())){
            OssFileStorage storage = OssUtil.getOssFileStorage(ossType);
            retMap.put("data",storage.getSimplifiedObjectMeta(fileMetaDataReq.getUrlKey()));
            retMap.put("code",200);
            retMap.put("msg","ok");
        }

        return retMap;
    }

    @RequestMapping(value = "/getFileMetaData", method = RequestMethod.GET)
    @ResponseBody
    public Object getFileMetaData(String urlkey){
        Map<String,Object> retMap=new HashMap<>(16);
        if(!ObjectUtils.isEmpty(urlkey)){
            OssFileStorage storage = OssUtil.getOssFileStorage(ossType);
            retMap.put("data",storage.getSimplifiedObjectMeta(urlkey));
            retMap.put("code",200);
            retMap.put("msg","ok");
        }
        return retMap;
    }
    @RequestMapping(value = "/getPreviewSignUrl", method = RequestMethod.POST)
    @ResponseBody
    public String getPreviewSignUrl(@RequestBody FileMetaDataReq fileMetaDataReq){
        String url="";

        if(!ObjectUtils.isEmpty(fileMetaDataReq.getUrlKey())){
            OssFileStorage storage = OssUtil.getOssFileStorage(ossType);
            String signedUrl = storage.generatePresignedUrl(fileMetaDataReq.getUrlKey());
            log.info("==============getPreviewSignUrl=============");
            log.info("==============获取签名地址=============");
            log.info(signedUrl);
            url = signedUrl;
        }

        return url;
    }
}
