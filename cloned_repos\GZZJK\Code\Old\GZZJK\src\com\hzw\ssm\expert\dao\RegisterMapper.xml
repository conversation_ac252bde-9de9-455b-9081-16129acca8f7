<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper 
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hzw.ssm.expert.dao.RegisterMapper">

	<select id="checkLoginCodeExist" parameterType="String" resultType="int">
		select count(1) from ts_user tu where tu.login_code=#{login_code} and tu.DELETE_FLAG = 0
	</select>
	
	<select id="checkMobileExist" parameterType="String" resultType="int">
		select count(1) from ts_user tu where tu.mobile=#{mobile} and tu.DELETE_FLAG = 0
	</select>
	
	<insert id="doRegister" parameterType="UserEntity">
		insert into ts_user(
			user_id,
			login_code,
			password,
			user_name,
			recommend_code,
			mobile,
			email,
			create_id,
			create_time,
			role_id
		) 
		values(
			#{user_id},
			#{mobile,jdbcType=VARCHAR},
			#{password,jdbcType=VARCHAR},
			#{user_name,jdbcType=VARCHAR},
			#{recommendCode,jdbcType=VARCHAR},
			#{mobile,jdbcType=VARCHAR},
			#{email,jdbcType=VARCHAR},
			#{create_id,jdbcType=VARCHAR},
			#{createTime},
			#{role_id,jdbcType=VARCHAR}
		)
	</insert>
</mapper>
