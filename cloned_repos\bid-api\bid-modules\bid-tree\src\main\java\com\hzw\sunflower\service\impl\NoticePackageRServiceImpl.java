package com.hzw.sunflower.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzw.sunflower.dao.NoticePackageRMapper;
import com.hzw.sunflower.dto.PackageInfoDTO;
import com.hzw.sunflower.entity.NoticePackageR;
import com.hzw.sunflower.service.NoticePackageRService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 公告包段关联 服务实现
 */
@Service
public class NoticePackageRServiceImpl extends ServiceImpl<NoticePackageRMapper, NoticePackageR> implements NoticePackageRService {

    /**
     * 根据条件查询包段简要信息
     *
     * @param projectId 项目ID
     * @return 包段信息的列表
     */
    @Override
    public List<PackageInfoDTO> findPackageInfoByProjectIdAndRound(Long projectId,Integer bidRound) {

        List<PackageInfoDTO> records = this.baseMapper.queryPackageInfoByProjectIdAndRound(projectId,bidRound);

        if(CollUtil.isEmpty(records)){
            records=new ArrayList<>();
        }

        return records;
    }

    @Override
    public List<PackageInfoDTO> findPackageInfoByNoticeId(Long noticeId) {

        List<PackageInfoDTO> records = this.baseMapper.queryPackageInfoByNoticeId(noticeId);

        if(CollUtil.isEmpty(records)){
            records=new ArrayList<>();
        }

        return records;
    }

    /**
     * 根据招标公告ID查询此公告所关联的包段ID
     *
     * @param noticeId 招标公告ID
     * @return 此公告关联的包段ID的列表
     */
    @Override
    public List<Long> findNoticePackageIdsByNoticeId(Long noticeId) {
        return baseMapper.findNoticePackageIds(noticeId);
    }

    /**
     * 根据项目ID查询所有处于进行中和划分包段状态的包段的ID
     *
     * @param projectId 项目ID
     * @return 可用包段的列表
     */
    @Override
    public List<Long> findAllAvailablePackageIds(Long projectId) {
        return baseMapper.findAllAvailablePackageIds(projectId);
    }
}
