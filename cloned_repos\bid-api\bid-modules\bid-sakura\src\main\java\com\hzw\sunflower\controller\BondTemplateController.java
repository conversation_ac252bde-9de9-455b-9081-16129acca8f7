package com.hzw.sunflower.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hzw.sunflower.common.BaseController;
import com.hzw.sunflower.common.Paging;
import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.common.annotation.RepeatSubmit;
import com.hzw.sunflower.constant.MessageConstants;
import com.hzw.sunflower.dto.BondTemplateDto;
import com.hzw.sunflower.entity.BondTemplate;
import com.hzw.sunflower.entity.condition.BondTemplateCondition;
import com.hzw.sunflower.service.BondTemplateService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date ：Created in 2022/8/2 11:20
 * @description：银行流水模板
 * @modified By：`
 * @version: 1.0
 */
@Api(tags = "保证金流水模板")
@RestController
@RequestMapping("/bondTemplate")
public class BondTemplateController extends BaseController {

    @Autowired
    BondTemplateService templateService;

    @ApiOperation(value = "分页查询流水模板")
    @ApiImplicitParam(name = "condition", value = "分页查询条件", required = true, dataType = "BondTemplateCondition", paramType = "body")
    @PostMapping(value = "/list")
    public Result<Paging<BondTemplateDto>> list(@RequestBody BondTemplateCondition condition) {
        IPage<BondTemplateDto> page = templateService.listPage(condition);
        return Result.ok(Paging.buildPaging(page));
    }

    @ApiOperation(value = "新增、编辑模板")
    @ApiImplicitParam(name = "bondTemplate", value = "模板", required = true, dataType = "BondTemplate", paramType = "body")
    @PostMapping(value = "/addOrUpdateTemplate")
    @RepeatSubmit
    public Result<Boolean> addOrUpdateTemplate(@RequestBody BondTemplate bondTemplate) {
        return Result.ok(templateService.addOrUpdateTemplate(bondTemplate));
    }

    @ApiOperation(value = "修改模板配置")
    @ApiImplicitParam(name = "bondTemplate", value = "模板", required = true, dataType = "BondTemplate", paramType = "body")
    @PostMapping(value = "/updateTemplateDetail")
    @RepeatSubmit
    public Result<Boolean> updateTemplateDetail(@RequestBody BondTemplate bondTemplate) {
        if (bondTemplate.getId() == null) {
            return Result.failed(MessageConstants.PARAMS_NOT_NULL);
        }
        return Result.ok(templateService.updateTemplateDetail(bondTemplate));
    }

    @ApiOperation(value = "根据id查询模板详情")
    @ApiImplicitParam(name = "id", value = "主键ID", required = true)
    @GetMapping(value = "/get/{id}")
    public Result<BondTemplate> get(@PathVariable Long id) {
        if (id == null) {
            return Result.failed(MessageConstants.GET_NULL);
        }
        return Result.ok(templateService.getById(id));
    }
}
