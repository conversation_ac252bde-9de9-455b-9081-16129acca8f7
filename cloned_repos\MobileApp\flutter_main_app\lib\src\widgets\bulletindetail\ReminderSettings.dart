///***
/// * 提醒设置
/// */

import 'package:flutter/material.dart';

import 'package:flutter_main_app/src/constants/theme.dart';

import 'package:flutter_main_app/src/components/CustomAppBar.dart';
import 'package:flutter_main_app/src/components/CustomSafeArea.dart';
import 'package:flutter_main_app/src/components/CustomSubmitButton.dart';

import 'package:flutter_main_app/src/widgets/bulletindetail/components/ReminderSettingsRadio.dart';
import 'package:flutter_main_app/src/widgets/bulletindetail/components/RemainDaysPicker.dart';

class ReminderSettings extends StatefulWidget {

  const ReminderSettings({Key key}) : super(key: key);

  /// * 路由名称
  static const String _routeName = 'ReminderSettings';
  static String get routeName => _routeName;

  @override
  _ReminderSettingsState createState() => _ReminderSettingsState();

}

class _ReminderSettingsState extends State<ReminderSettings> {

  /// * 字体
  static const TextStyle _titleTextStyle = TextStyle(
    fontSize: 14.0,
    color: themeTitleColor,
  );
  static const TextStyle _tipsTextStyle = TextStyle(
    fontSize: 12.0,
    color: themeTipsTextColor,
  );
  static const TextStyle _activeTextStyle = TextStyle(
    fontSize: 12.0,
    color: themeActiveColor,
  );
  static const TextStyle _textFieldTextStyle = TextStyle(
    height: 1.5,
    fontSize: 14.0,
    color: themeTipsTextColor,
  );
  static const TextStyle _textFieldHintTextStyle = TextStyle(
    height: 1.05,
    fontSize: 14.0,
    color: themeHintTextColor,
  );

  /// * 招标公告/资格预审公告
  bool _isGetTenderDocSelected = false;
  bool _isDeliverBidDocSelected = false;
  bool _isBidOpenCountDownSelected = false;

  int _getTenderDocStartTimeRemainDays = 0;
  int _deliverBidDocEndTimeRemainDays = 0;
  int _bidOpenCountDownRemainDays = 0;

  /// * 中标候选人公示
  bool _isWinCandidateSelected = false;

  /// * 中标结果公示
  bool _isWinBidderSelected = false;

  /// * 更正公告公示
  bool _isChangeBulletinSelected = false;

  /// * 手机输入框
  final TextEditingController _textFieldController = TextEditingController();
  final FocusNode _textFieldFocusNode = FocusNode();

  String _phoneNumber = '';

  @override
  void initState() {
    super.initState();

    _textFieldController.text = _phoneNumber;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: '设置提醒',
      ),
      body: CustomSafeArea(
        backgroundColor: themeContentBackgroundColor,
        child: SingleChildScrollView(
          physics: BouncingScrollPhysics(),
          padding: const EdgeInsets.only(bottom: 59.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              /// * 发布招标公告/资格预审公告
              Padding(
                padding: const EdgeInsets.only(left: 20.0, top: 35.0, right: 20.0, bottom: 5.0),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Text(
                      '发布招标公告/资格预审公告',
                      style: _titleTextStyle,
                    ),
                    /// * 招标文件获取开始时间
                    Padding(
                      padding: const EdgeInsets.only(top: 21.0),
                      child: ReminderSettingsRadio(
                        richText: RichText(
                          text: TextSpan(
                            text: '招标文件获取开始时间',
                            style: _tipsTextStyle,
                            children: <InlineSpan>[
                              WidgetSpan(
                                child: Padding(
                                  padding: const EdgeInsets.symmetric(horizontal: 12.0),
                                  child: Text(
                                    '$_getTenderDocStartTimeRemainDays',
                                    style: _activeTextStyle,
                                  ),
                                ),
                              ),
                              TextSpan(
                                text: '天',
                                style: _tipsTextStyle,
                              ),
                            ],
                          ),
                        ),
                        onRadioTap: () {
                          _handleGetTenderDocRadioTap('radio');
                        },
                        onTextTap: () {
                          _handleGetTenderDocRadioTap('text');
                        },
                        isSelected: _isGetTenderDocSelected,
                      ),
                    ),
                    /// * 投标文件递交截止时间
                    Padding(
                      padding: const EdgeInsets.only(top: 21.0),
                      child: ReminderSettingsRadio(
                        richText: RichText(
                          text: TextSpan(
                            text: '投标文件递交截止时间',
                            style: _tipsTextStyle,
                            children: <InlineSpan>[
                              WidgetSpan(
                                child: Padding(
                                  padding: const EdgeInsets.symmetric(horizontal: 12.0),
                                  child: Text(
                                    '$_deliverBidDocEndTimeRemainDays',
                                    style: _activeTextStyle,
                                  ),
                                ),
                              ),
                              TextSpan(
                                text: '天',
                                style: _tipsTextStyle,
                              ),
                            ],
                          ),
                        ),
                        onRadioTap: () {
                          _handleDeliverBidDocRadioTap('radio');
                        },
                        onTextTap: () {
                          _handleDeliverBidDocRadioTap('text');
                        },
                        isSelected: _isDeliverBidDocSelected,
                      ),
                    ),
                    /// * 开标时间倒计时提醒
                    Padding(
                      padding: const EdgeInsets.only(top: 21.0),
                      child: ReminderSettingsRadio(
                        richText: RichText(
                          text: TextSpan(
                            text: '开标时间倒计时提醒',
                            style: _tipsTextStyle,
                            children: <InlineSpan>[
                              WidgetSpan(
                                child: Padding(
                                  padding: const EdgeInsets.symmetric(horizontal: 12.0),
                                  child: Text(
                                    '$_bidOpenCountDownRemainDays',
                                    style: _activeTextStyle,
                                  ),
                                ),
                              ),
                              TextSpan(
                                text: '天',
                                style: _tipsTextStyle,
                              ),
                            ],
                          ),
                        ),
                        onRadioTap: () {
                          _handleBidOpenCountDownRadioTap('radio');
                        },
                        onTextTap: () {
                          _handleBidOpenCountDownRadioTap('text');
                        },
                        isSelected: _isBidOpenCountDownSelected,
                      ),
                    ),
                  ],
                ),
              ),
              /// * 发布中标候选人公示
              Padding(
                padding: const EdgeInsets.only(left: 20.0, top: 35.0, right: 20.0, bottom: 5.0),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Text(
                      '发布中标候选人公示',
                      style: _titleTextStyle,
                    ),
                    Padding(
                      padding: const EdgeInsets.only(top: 21.0),
                      child: ReminderSettingsRadio(
                        text: Text(
                          '中标候选人公示发布时提醒',
                          style: _tipsTextStyle,
                        ),
                        onTap: _handleWinCandidateRadioTap,
                        isSelected: _isWinCandidateSelected,
                      ),
                    ),
                  ],
                ),
              ),
              /// * 发布中标结果公示
              Padding(
                padding: const EdgeInsets.only(left: 20.0, top: 35.0, right: 20.0, bottom: 5.0),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Text(
                      '发布中标结果公示',
                      style: _titleTextStyle,
                    ),
                    Padding(
                      padding: const EdgeInsets.only(top: 21.0),
                      child: ReminderSettingsRadio(
                        text: Text(
                          '中标结果公示发布时提醒',
                          style: _tipsTextStyle,
                        ),
                        onTap: _handleWinBidderRadioTap,
                        isSelected: _isWinBidderSelected,
                      ),
                    ),
                  ],
                ),
              ),
              /// * 发布更正公告公示
              Padding(
                padding: const EdgeInsets.only(left: 20.0, top: 35.0, right: 20.0, bottom: 5.0),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Text(
                      '发布更正公告公示',
                      style: _titleTextStyle,
                    ),
                    Padding(
                      padding: const EdgeInsets.only(top: 21.0),
                      child: ReminderSettingsRadio(
                        text: Text(
                          '更正公告公示发布时提醒',
                          style: _tipsTextStyle,
                        ),
                        onTap: _handleChangeBulletinRadioTap,
                        isSelected: _isChangeBulletinSelected,
                      ),
                    ),
                  ],
                ),
              ),
              /// * 手机号
              Padding(
                padding: const EdgeInsets.only(left: 20.0, top: 35.0, right: 20.0),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Text(
                      '手机号',
                      style: _titleTextStyle,
                    ),
                    Container(
                      margin: const EdgeInsets.only(top: 20.0),
                      width: 218.0,
                      height: 35.0,
                      decoration: BoxDecoration(
                        border: Border.all(
                          color: const Color(0xffc1c1c1),
                          width: 0.5,
                        ),
                        borderRadius: BorderRadius.circular(2.0),
                      ),
                      child: TextField(
                        controller: _textFieldController,
                        focusNode: _textFieldFocusNode,
                        onChanged: (String value) {
                          setState(() {
                            _phoneNumber = value;
                          });
                        },
                        style: _textFieldTextStyle,
                        decoration: InputDecoration(
                          hintText: '请输入您的手机号码',
                          hintStyle: _textFieldHintTextStyle,
                          contentPadding: const EdgeInsets.symmetric(horizontal: 10.0, vertical: 4.0),
                          border: InputBorder.none,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              /// * 保存设置
              Padding(
                padding: const EdgeInsets.only(top: 40.0),
                child: CustomSubmitButton(
                  text: '保存设置',
                  onTap: () {},
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  ///***
  /// * 招标文件获取开始时间 - 单选控件点击事件
  /// *
  /// * @param {'radio'|'text'} type: 点击区域区分 - 单选图标|文本
  /// */
  void _handleGetTenderDocRadioTap(String type) {
    if (type == 'radio' && _isGetTenderDocSelected) {
      /// * 取消选中并返回
      setState(() {
        _isGetTenderDocSelected = false;
      });

      return;
    }

    setState(() {
      _isGetTenderDocSelected = true;
    });

    final RemainDaysPicker remainDaysPicker = RemainDaysPicker(
      days: _getTenderDocStartTimeRemainDays,
    );

    final Future<int> modalBottomSheet = showModalBottomSheet<int>(
      context: context,
      builder: (BuildContext context) => remainDaysPicker,
    );

    modalBottomSheet?.then((int days) {
      if (days != null) {
        setState(() {
          _getTenderDocStartTimeRemainDays = days;
        });
      }
    });
  }

  ///***
  /// * 投标文件递交截止时间 - 单选控件点击事件
  /// *
  /// * @param {'radio'|'text'} type: 点击区域区分 - 单选图标|文本
  /// */
  void _handleDeliverBidDocRadioTap(String type) {
    if (type == 'radio' && _isDeliverBidDocSelected) {
      /// * 取消选中并返回
      setState(() {
        _isDeliverBidDocSelected = false;
      });

      return;
    }

    setState(() {
      _isDeliverBidDocSelected = true;
    });

    final RemainDaysPicker remainDaysPicker = RemainDaysPicker(
      days: _deliverBidDocEndTimeRemainDays,
    );

    final Future<int> modalBottomSheet = showModalBottomSheet<int>(
      context: context,
      builder: (BuildContext context) => remainDaysPicker,
    );

    modalBottomSheet?.then((int days) {
      if (days != null) {
        setState(() {
          _deliverBidDocEndTimeRemainDays = days;
        });
      }
    });
  }

  ///***
  /// * 开标时间倒计时提醒 - 单选控件点击事件
  /// *
  /// * @param {'radio'|'text'} type: 点击区域区分 - 单选图标|文本
  /// */
  void _handleBidOpenCountDownRadioTap(String type) {
    if (type == 'radio' && _isBidOpenCountDownSelected) {
      /// * 取消选中并返回
      setState(() {
        _isBidOpenCountDownSelected = false;
      });

      return;
    }

    setState(() {
      _isBidOpenCountDownSelected = true;
    });

    final RemainDaysPicker remainDaysPicker = RemainDaysPicker(
      days: _bidOpenCountDownRemainDays,
    );

    final Future<int> modalBottomSheet = showModalBottomSheet<int>(
      context: context,
      builder: (BuildContext context) => remainDaysPicker,
    );

    modalBottomSheet?.then((int days) {
      if (days != null) {
        setState(() {
          _bidOpenCountDownRemainDays = days;
        });
      }
    });
  }

  ///***
  /// * 中标候选人公示 - 单选控件点击事件
  /// */
  void _handleWinCandidateRadioTap() {
    setState(() {
      _isWinCandidateSelected = !_isWinCandidateSelected;
    });
  }

  ///***
  /// * 中标结果公示 - 单选控件点击事件
  /// */
  void _handleWinBidderRadioTap() {
    setState(() {
      _isWinBidderSelected = !_isWinBidderSelected;
    });
  }

  ///***
  /// * 更正公告公示 - 单选控件点击事件
  /// */
  void _handleChangeBulletinRadioTap() {
    setState(() {
      _isChangeBulletinSelected = !_isChangeBulletinSelected;
    });
  }

}
