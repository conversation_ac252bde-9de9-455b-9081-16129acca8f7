package com.hzw.ssm.sys.project.action;

import java.io.PrintWriter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import net.sf.json.JSONObject;

import org.apache.struts2.ServletActionContext;
import org.apache.struts2.convention.annotation.Action;
import org.apache.struts2.convention.annotation.Namespace;
import org.apache.struts2.convention.annotation.ParentPackage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;

import com.hzw.ssm.fw.base.BaseAction;
import com.hzw.ssm.sys.login.service.LoginService;
import com.hzw.ssm.sys.project.entity.ProjectEntity;
import com.hzw.ssm.sys.project.entity.ResultByProjectEntity;
import com.hzw.ssm.sys.project.service.ProjectService;
import com.hzw.ssm.sys.user.entity.UserEntity;

@Namespace("/projectAjax")
@ParentPackage(value = "default")
public class ProjectAjaxAction extends BaseAction {

	private ProjectAction action;

	@Autowired
	private LoginService loginService;

	@Autowired
	private ProjectService projectService;

	@Action("queryByProjectPoint")
	public void queryByProjectPoint() throws Exception {
		HttpServletResponse response = ServletActionContext.getResponse();
		response.setCharacterEncoding("utf-8");
		// 获取request对象
		HttpServletRequest request = ServletActionContext.getRequest();

		response.setHeader("Cache-Control", "no-cache");
		response.addHeader("Access-control-Allow-Origin", "*");
		response.setContentType("text/json;charset=UTF-8");

		JSONObject json = new JSONObject();
		String tipMsg = null;
		List<Map<String, String>> dataList = new ArrayList<Map<String, String>>();
		String tender = null;// 委托机构
		String bidTime = null;// 评审时间
		String projectNo = null;// 项目编号
		String openBidTime=request.getParameter("openBidTime");//项目开标时间
		try {
			// 业务逻辑部分
			// 1.验证用户
			// 接收ajax的参数值
			String login_code = request.getParameter("login_code");
			String password = request.getParameter("password");
			String projectNum = request.getParameter("projectNum");
			
			int state = 2;// 默认加密 1：加密 2：不加密
			// 用户信息验证（常规登录）
			UserEntity info = loginService.login(login_code, password, state);
			List<ResultByProjectEntity> byProjectEntities = null;
			if (info == null) {// 验证用户失败返回null
				tipMsg = "用户验证失败";
			} else {// 验证用户成功，根据项目编号查询项目专家参标记录
				ProjectEntity projectEntity=new ProjectEntity();
				projectEntity.setBidTime_(openBidTime);  //项目评审时间
				if(!(StringUtils.isEmpty(projectNum)&&projectNum.contains(","))){
					String [] strs=projectNum.split(",");
					List<String> list = new ArrayList<String>();
					for(String s:strs){
						list.add(s);
					}
					projectEntity.setProjectNoList(list);
				}else{
					projectEntity.setProjectNo(projectNum);  //项目编号
				}
				byProjectEntities = projectService.queryByProject(projectEntity);
			}
			
			if (byProjectEntities != null && byProjectEntities.size() > 0) {
				ResultByProjectEntity entryItem = byProjectEntities.get(0);
				tender = entryItem.getTender();// 委托机构
				bidTime = entryItem.getBidTime();// 评审时间
				projectNo = entryItem.getProjectNo();// 项目编号

				for (ResultByProjectEntity item : byProjectEntities) {
					if (item == null) {
						continue;
					}

					Map<String, String> map = new HashMap<String, String>();
					map.put("userName", item.getUserName());
					//验证用户证件类型，只传身份证号码
					if(item.getIdType().equals("1")){
						map.put("idNo", item.getIdNo());
					}else{
						map.put("idNo", "");
					}
					map.put("phone", item.getPhone());
					dataList.add(map);
				}
			}
		} catch (Exception e) {
			// 打印错误日志
			log.error("业务处理失败", e);
			tipMsg = "失败原因";
		}
		// 返回结果
		if(bidTime==null){
			json.put("code", "0001");
			json.put("info", "暂无确定参加的专家!");
		}else{
			if (tipMsg == null || "".equals(tipMsg)) {
				json.put("code", "0000");
				json.put("data", dataList);
				json.put("bidTime", bidTime);
				json.put("projectNo", projectNo);
				json.put("tender", tender);
			} else {
				json.put("code", "9999");
				json.put("errorMsg", tipMsg);
			}
		}
		
		PrintWriter out = response.getWriter();
		out.print("callback("+json.toString()+");");
		out.flush();
		out.close();
	}
}
