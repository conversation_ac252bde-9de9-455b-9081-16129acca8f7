<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hzw.ssm.applets.dao.PhoneCodeMapper" >

	<resultMap id="BaseResultMap" type="com.hzw.ssm.applets.entity.PhoneCode" >
		<id column="id" property="id"/>
		<result column="phone" property="phone"/>
		<result column="verification_code" property="verificationCode"/>
		<result column="code_status" property="codeStatus"/>
		<result column="create_time" property="createTime"/>
		<result column="update_time" property="updateTime"/>
		<result column="delete_flag" property="deleteFlag"/>
	</resultMap>

	<sql id="Base_Column_List" >
		 t.id, t.phone, t.verification_code, t.code_status, t.create_time, t.update_time, t.delete_flag
	</sql>

	<select id="checkPhone" resultType="PhoneCode">
		select
			t.create_time createTime
		from
			t_applets_code t
		where
			rownum = 1
			and delete_flag = 0
			and t.phone = #{phone}
		order by
			t.create_time desc
	</select>

	<select id="checkSms" resultType="PhoneCode" >
		select
			t.id,
			t.phone,
			t.verification_code verificationCode
		from
			t_applets_code t
		where
			t.verification_code = #{verificationCode}
			and t.phone = #{phone}
			and t.code_status = 0
	</select>

	<select id="selectByPrimaryKey" resultMap="BaseResultMap" >
		select
			<include refid="Base_Column_List" />
		from
			t_applets_code t
		where
			id = #{id}
	</select>

	<select id="queryByPhone" resultMap="BaseResultMap" >
		select
			*
		from (
			select
				<include refid="Base_Column_List" />
			from
				t_applets_code t
			where
				phone = #{phone}
				and delete_flag = 0
				and code_status = 0
			order by
				t.create_time desc
			)
		where
			rownum = 1
	</select>

	<insert id="insertSelective" parameterType="PhoneCode" useGeneratedKeys="true" keyProperty="id">
		insert into t_applets_code
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="id != null and !&quot;&quot;.equals(id)" >
				id,
			</if>
			<if test="phone != null and !&quot;&quot;.equals(phone)" >
				phone,
			</if>
			<if test="verificationCode != null and !&quot;&quot;.equals(verificationCode)" >
				verification_code,
			</if>
			<if test="codeStatus != null and !&quot;&quot;.equals(codeStatus)" >
				code_status,
			</if>
			<if test="createTime != null and !&quot;&quot;.equals(createTime)" >
				create_time,
			</if>
			<if test="updateTime != null and !&quot;&quot;.equals(updateTime)" >
				update_time,
			</if>
			<if test="deleteFlag != null and !&quot;&quot;.equals(deleteFlag)" >
				delete_flag,
			</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides="," >
			<if test="id != null and !&quot;&quot;.equals(id)" >
				#{id},
			</if>
			<if test="phone != null and !&quot;&quot;.equals(phone)" >
				#{phone},
			</if>
			<if test="verificationCode != null and !&quot;&quot;.equals(verificationCode)" >
				#{verificationCode},
			</if>
			<if test="codeStatus != null and !&quot;&quot;.equals(codeStatus)" >
				#{codeStatus},
			</if>
			<if test="createTime != null and !&quot;&quot;.equals(createTime)" >
				#{createTime},
			</if>
			<if test="updateTime != null and !&quot;&quot;.equals(updateTime)" >
				#{updateTime},
			</if>
			<if test="deleteFlag != null and !&quot;&quot;.equals(deleteFlag)" >
				#{deleteFlag},
			</if>
		</trim>
	</insert>

	<update id="updateByPrimaryKeySelective" parameterType="PhoneCode" >
		update t_applets_code
		<set >
			<if test="phone != null and !&quot;&quot;.equals(phone)" >
				phone = #{phone},
			</if>
			<if test="verificationCode != null and !&quot;&quot;.equals(verificationCode)" >
				verification_code = #{verificationCode},
			</if>
			<if test="codeStatus != null and !&quot;&quot;.equals(codeStatus)" >
				code_status = #{codeStatus},
			</if>
			<if test="createTime != null and !&quot;&quot;.equals(createTime)" >
				create_time = #{createTime},
			</if>
			<if test="updateTime != null and !&quot;&quot;.equals(updateTime)" >
				update_time = #{updateTime},
			</if>
			<if test="deleteFlag != null and !&quot;&quot;.equals(deleteFlag)" >
				delete_flag = #{deleteFlag},
			</if>
		</set>
		where id = #{id,jdbcType=INTEGER}
	</update>

	<update id="delete">
		UPDATE t_applets_code SET delete_flag = 1
		<where>
			AND id = #{id}
		</where>
	</update>

	<delete id="deleteByPhone">
		delete from t_applets_code where phone = #{phone}
	</delete>

	<select id="querySendCount" resultType="int">
		SELECT
			count( id ) id
		FROM
			t_applets_code pc
		WHERE
			DATE_FORMAT( pc.createtime, '%Y-%m-%d' ) = DATE_FORMAT( now( ), '%Y-%m-%d' )
			AND pc.phone = #{phone}
	</select>

</mapper>

