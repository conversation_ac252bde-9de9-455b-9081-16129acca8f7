package com.hzw.sunflower.constant.constantenum;

/**
 * 评价状态枚举
 *
 * <AUTHOR>
 */
public enum ExchangeRateEnum {
    //1美元 = 6.75 人民币
    //1 欧元 = 7 人民币
    //1 日元 = 0.05 人民币
    DOLLAR(6.75, "美元"),
    EURO(7.0, "欧元"),
    YEN(0.05, "日元");

    private Double type;
    private String desc;

    ExchangeRateEnum(Double type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Double getType() {
        return type;
    }

    public void setType(Double type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
