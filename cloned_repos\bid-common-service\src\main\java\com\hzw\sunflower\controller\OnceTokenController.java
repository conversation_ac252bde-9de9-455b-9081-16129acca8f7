package com.hzw.sunflower.controller;


import cn.hutool.crypto.SecureUtil;
import com.hzw.sunflower.common.BaseController;
import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.common.redis.RedisCache;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.UUID;

/**
 * 专家签章调用接口需要token
 * <AUTHOR>
 */
@Api(tags = "专家签章调用接口需要token")
@RestController
@RequestMapping("/signToken")
public class OnceTokenController extends BaseController {


    @Resource
    private RedisCache redisCache;

    @ApiOperation(value = "获专家签章调用接口token")
    @PostMapping("/getSign")
    public Result<Object> getToken(@RequestBody Map<String,Object> params) {
        Map<String, Object> paramMap = new LinkedHashMap<>();

        paramMap.putAll(params);
        paramMap.put("userId", getJwtUser().getUserId());
        String nonce= UUID.randomUUID().toString();
        paramMap.put("nonce", nonce);
        long timestamp = System.currentTimeMillis();
        paramMap.put("timestamp", timestamp);
        String secretKey="esignJstcc";
        String sign= SecureUtil.signParamsMd5(paramMap,secretKey);
        paramMap.put("sign",sign);
//        Map<String ,Object> resultData=new HashMap<>(16);
//        resultData.put("sign",sign);
//        resultData.put("nonce",nonce);
//        resultData.put("timestamp", timestamp);

//        redisCache.setCacheObject(sign,sign, , TimeUnit.MILLISECONDS);
        return Result.ok(paramMap);
    }
    @ApiOperation(value = "获专一次性token")
    @PostMapping("/getNonceToken")
    public Result<Object> getNonceToken() {
        Map<String, Object> paramMap = new LinkedHashMap<>();

//        paramMap.putAll(params);
//        paramMap.put("userId", getJwtUser().getUserId());
        String nonce= UUID.randomUUID().toString();
        paramMap.put("nonce", nonce+"-"+getJwtUser().getUserId());
        long timestamp = System.currentTimeMillis();
        paramMap.put("timestamp", timestamp);
//        String secretKey="esignJstcc";
//        String sign= SecureUtil.signParamsMd5(paramMap,secretKey);
//        paramMap.put("sign",sign);

        return Result.ok(paramMap);
    }
}
