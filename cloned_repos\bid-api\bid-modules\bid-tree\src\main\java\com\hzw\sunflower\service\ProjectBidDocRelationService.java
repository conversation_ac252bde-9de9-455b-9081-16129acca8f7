package com.hzw.sunflower.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hzw.sunflower.dto.ProjectBidDocRelationDTO;
import com.hzw.sunflower.entity.ProjectBidDocRelation;
import com.hzw.sunflower.entity.condition.ProjectBidDocRelationCondition;

import java.util.List;


/**
 * TProjectBidDocRelationService接口
 */
public interface ProjectBidDocRelationService extends IService<ProjectBidDocRelation> {
    /**
     * 根据条件分页查询列表
     *
     * @param condition 查询条件
     * @return 分页信息
     */
    IPage<ProjectBidDocRelation> findInfoByCondition(ProjectBidDocRelationCondition condition);

    /**
     * 根据主键ID查询信息
     *
     * @param id 主键ID
     * @return TProjectBidDocRelation信息
     */
    ProjectBidDocRelation getInfoById(Long id);

    /**
     * 新增
     *
     * @param tProjectBidDocRelation
     * @return 是否成功
     */
    Boolean addInfo(ProjectBidDocRelationDTO tProjectBidDocRelation);

    /**
     * 修改单位公司 信息
     *
     * @param tProjectBidDocRelation
     * @return 是否成功
     */
    Boolean updateInfo(ProjectBidDocRelationDTO tProjectBidDocRelation);

    /**
     * 根据主键ID删除
     *
     * @param id 主键ID
     * @return 是否成功
     */
    Boolean deleteById(Long id);

    /**
     * 根据主键ID列表批量删除
     *
     * @param idList 主键ID列表
     * @return 是否成功
     */
    Boolean deleteByIds(List<Long> idList);

}
