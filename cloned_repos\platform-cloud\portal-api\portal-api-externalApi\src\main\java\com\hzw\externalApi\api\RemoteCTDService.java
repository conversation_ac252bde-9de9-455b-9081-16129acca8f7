package com.hzw.externalApi.api;

import com.hzw.externalApi.api.domain.VoiceInfoVo;
import com.hzw.externalApi.api.domain.dto.VoiceEntityDTO;

import java.util.Date;
import java.util.List;

/**
 * ctd
 * <AUTHOR>
 */
public interface RemoteCTDService {
    /**
     * 语音通知
     * @param entity
     * @param reviewTime
     * @param reviewLocation
     * @return
     */
    void sendVoice(VoiceEntityDTO entity, Date reviewTime, String reviewLocation);

    /**
     * 修改语音通知短信发送状态
     * @param joinList
     * @param type
     * @return List
     */
    List<VoiceEntityDTO> updateVoiceList(List<VoiceInfoVo> joinList, String type);

    /**
     * 查询是否还有未通知的人员
     * @param id
     * @return Boolean
     */
    Boolean checkNoticeInfo(String id);

    /**
     * 修改未联系数据为已抽满
     * @param collect
     * @return  List
     */
    List<String> updateStatus(List<String> collect);

    /**
     * 未抽满且还未未联系的
     * @param reCallIds
     */
    void reTry(List<String> reCallIds);
}
