package com.hzw.sunflower.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hzw.sunflower.constant.constantenum.CaEnum;
import com.hzw.sunflower.controller.request.CaDataReq;
import com.hzw.sunflower.controller.response.CaBidOpeningVo;
import com.hzw.sunflower.controller.response.CaVo;
import com.hzw.sunflower.dao.CaDataMapper;
import com.hzw.sunflower.controller.response.CaDataVo;
import com.hzw.sunflower.entity.DepartProject;

import com.hzw.sunflower.controller.response.ProcessAddress;
import com.hzw.sunflower.entity.JwtUser;
import com.hzw.sunflower.service.CaDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/3/9 16:48 星期四
 */
@Service
public class CaDataServiceImpl  implements CaDataService {

    @Autowired
    CaDataMapper caDataMapper;




    @Override
    public IPage<CaDataVo> findInfoByCondition(CaDataReq req, JwtUser jwtUser) {

        IPage<CaDataVo> page = new Page<>(req.getPageIndex(), req.getPageSize());
        IPage<CaDataVo> listIPage = null;

        if (req.getHaveAllDept()!=null){
            if (req.getHaveAllDept()==0){
                Long deptId = jwtUser.getUser().getDepartId();
                req.setDeptId(deptId);
                listIPage = caDataMapper.findInfoByReq(page,req);
                return listIPage;
            }
        }

        if(req.getProcessAddress()!=null) {
            if (req.getProcessAddress().equals(CaEnum.YES.getCode())) {
                req.setIsProcess(1);
                req.setProcessAddress("");
            }
            if (req.getProcessAddress().equals(CaEnum.NO.getCode())) {
                req.setIsProcess(0);
                req.setProcessAddress("");
            }
            if (req.getProcessAddress().equals(CaEnum.ALL.getCode())) {
                req.setProcessAddress(null);
            }
        }

        listIPage = caDataMapper.findInfoByReq(page,req);
        return listIPage;

    }

    @Override
    public List<DepartProject> getDeptIds() {
        return caDataMapper.getDeptIds();
    }

    @Override
    public List<ProcessAddress> getRelatedProjectAddress() {
        return caDataMapper.getRelatedProjectAddress();
    }

    @Override
    public List<CaDataVo> exportCaList(CaDataReq req, JwtUser jwtUser) {

        if (req.getHaveAllDept()!=null){
            if (req.getHaveAllDept()==0){
                Long deptId = jwtUser.getUser().getDepartId();
                req.setDeptId(deptId);
                return caDataMapper.exportCaList(req);
            }
        }

        if(req.getProcessAddress()!=null) {
            if (req.getProcessAddress().equals(CaEnum.YES.getCode())) {
                req.setIsProcess(1);
                req.setProcessAddress("");
            }
            if (req.getProcessAddress().equals(CaEnum.NO.getCode())) {
                req.setIsProcess(0);
                req.setProcessAddress("");
            }
            if (req.getProcessAddress().equals(CaEnum.ALL.getCode())) {
                req.setProcessAddress(null);
            }
        }
        return caDataMapper.exportCaList(req);
    }

    @Override
    public Map<String, Object> getCaBidOpeningList() {
        HashMap<String, Object> ca = new HashMap<>();

        //插入CA信息
        List<CaVo> caList = caDataMapper.getCaList();
        if(!caList.isEmpty()){
            ca.put("caList",caList);
        }

        int todayNum = 0;
        int sumNum = 0;

        //插入今日开标项目数，并且判断
        List<CaBidOpeningVo> CaBidOpeningTodayList = caDataMapper.getCaBidOpeningList(1);
        if (CaBidOpeningTodayList.isEmpty()){
            CaBidOpeningTodayList.add(0, new CaBidOpeningVo(CaEnum.Today.getCode(),0));
        }else{
            for(CaBidOpeningVo today : CaBidOpeningTodayList){
                todayNum += today.getDepartmentNum();
            }
            CaBidOpeningTodayList.add(0, new CaBidOpeningVo(CaEnum.Today.getCode(),todayNum));
        }
        ca.put("todayOpen",CaBidOpeningTodayList);

        //插入累计开标项目数，并且判断
        List<CaBidOpeningVo> CaBidOpeningSumList = caDataMapper.getCaBidOpeningList(0);
        if (CaBidOpeningSumList.isEmpty()){
            CaBidOpeningSumList.add(0, new CaBidOpeningVo(CaEnum.Sum.getCode(),0));
        }else{
            for(CaBidOpeningVo sum : CaBidOpeningSumList){
                sumNum += sum.getDepartmentNum();
            }
            CaBidOpeningSumList.add(0, new CaBidOpeningVo(CaEnum.Sum.getCode(),sumNum));
        }
        ca.put("sumOpen",CaBidOpeningSumList);

        return ca;
    }



}
