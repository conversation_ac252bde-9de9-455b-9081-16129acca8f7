package com.hzw.auth.utils;


import com.alibaba.fastjson.JSONObject;
import com.hzw.auth.config.AuthConfig;
import com.hzw.common.core.utils.MyHttpUtils;
import com.hzw.common.core.utils.SpringUtils;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.*;
import java.util.HashMap;
import java.util.Map;

/**
 * 小程序入口
 *
 * @version V1.0
 * @author: tsy
 * @Email:
 * @date: 2019年11月11日 下午5:20:22
 */
public class SmallWxUtil {

    public static String appid;
    public static String appsecret;
    public static String prepic;
    public static String templatepath;

//    static {
//        String appID = "wxc6ec43de115c19b7";
//        String appSecret = "b8e48a4302599e6181f8dd05902ea91f";
//        String prePic = "http://20391e20d3.51mypc.cn/expertImages/card/";
////        String appID = PropertiesUtil.get("AppID");
////        String appSecret = PropertiesUtil.get("AppSecret");
////        String prePic = PropertiesUtil.get("PrePic");
//        String templatePath = "/temporary";
//        appid = appID;
//        appsecret = appSecret;
//        prepic = prePic;
//        templatepath = templatePath;
//    }

    static {
        AuthConfig bean = SpringUtils.getBean(AuthConfig.class) ;
        String appID = bean.getAppId();
        String appSecret = bean.getSecret();
        String prePic = bean.getPrePic();
        String templatePath = bean.getTemplatePath();
        appid = appID;
        appsecret = appSecret;
        prepic = prePic;
        templatepath = templatePath;
    }
    /**
     * 获取opneid
     *
     * @param code
     * @return
     */
    public static Map<String, Object> oauth2GetOpenid(String code) {


        String grant_type = "authorization_code";
        String requestUrl = "https://api.weixin.qq.com/sns/jscode2session";
        String params = "appid=" + appid + "&secret=" + appsecret + "&js_code=" + code + "&grant_type=" + grant_type;
        String data = MyHttpUtils.get(requestUrl, params);
        JSONObject json = JSONObject.parseObject(data);
        boolean containsKey = json.containsKey("openid");
        Map<String, Object> map = new HashMap<>();
        if (containsKey) {
            String openid = json.getString("openid");
            String sessionKey = json.getString("session_key");
            map.put("openid", openid);
            map.put("sessionKey", sessionKey);
            return map;
        }

        return map;
    }

    /**
     * 获取用户信息
     *
     * @param openId
     * @return
     */
   public static Map<String, Object> getWXUserInfo(String openId) throws Exception {

        String token = getToken();
        String url = "https://api.weixin.qq.com/sns/userinfo?access_token=" + token + "&openid=" + openId + "&lang=zh_CN";
        JSONObject jsonUser = JSONObject.parseObject(MyHttpUtils.doGet(url));
        Map<String, Object> map = new HashMap<>();
        String nickName = jsonUser.getString("nickName");
        String avatarUrl = jsonUser.getString("avatarUrl");
        map.put("nickName", nickName);
        map.put("avatarUrl", avatarUrl);
        return map;

    }


    /**
     * 获取token
     *
     * @return
     * @throws Exception
     */
    public static String getToken() throws Exception {

        String strTokenUrl = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential" + "&appid=" + appid + "&secret=" + appsecret;
        String resulstrToken = MyHttpUtils.doGet(strTokenUrl);
        JSONObject object = JSONObject.parseObject(resulstrToken);
        String accessToken = object.getString("access_token");
        return accessToken;
    }

    /**
     * 验证微信token
     *
     * @param token
     * @return
     * @throws Exception
     */
   public static Map<String, Object> weChatValid(String token) throws Exception {

        //1.判断token的有效性
        String url = "https://api.weixin.qq.com/cgi-bin/getcallbackip?access_token=" + token;
        String resulUrl = MyHttpUtils.doGet(url);
        JSONObject object = JSONObject.parseObject(resulUrl);

        //未失效 return
        String errCode = object.getString("errcode");
        if (errCode == null) {
            return null;
        }

        //失效重新获取
        String strTokenUrl = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential" + "&appid=" + appid + "&secret=" + appsecret;
        String resulstrToken = MyHttpUtils.doGet(strTokenUrl);
        object = JSONObject.parseObject(resulstrToken);
        String access_token = object.getString("access_token");

        String jsapi_ticket = jsapi_ticket(access_token);

        Map<String, Object> map = new HashMap<String, Object>();
        map.put("access_token", access_token);
        map.put("jsapi_token", jsapi_ticket);
        return map;
    }

    public static String jsapi_ticket(String access_token) {

        System.out.println("获取基本支持的jsapi_ticket开始");
        String token = null;
        String url = "https://api.weixin.qq.com/cgi-bin/ticket/getticket?access_token=" + access_token + "&type=jsapi";
        try {
            token = MyHttpUtils.HttpPostRequest(url);
            JSONObject jbMap = JSONObject.parseObject(token);
            if (jbMap.get("errmsg").equals("ok")) {
                String ticket = (String) jbMap.get("ticket");
                return ticket;
            } else {
                return null;
            }
        } catch (Exception e) {
            System.out.println("获取基本支持的jsapi_ticket程序异常");
            e.printStackTrace();
        }
        System.out.println("获取基本支持的jsapi_ticket成功");
        return null;
    }


    public static JSONObject uploadCard(String type, String imgUrl) throws Exception {

        String requestUrl = "https://api.weixin.qq.com/cv/ocr/idcard";
        String params = "type=" + type + "&img_url=" + prepic + imgUrl + "&access_token=" + getToken();
        String url = requestUrl + "?" + params;
        String data = MyHttpUtils.doPost(url, null);
        JSONObject json = JSONObject.parseObject(data);
        String errcode = json.getString("errcode");
        if (!"0".equals(errcode)) {
            throw new Exception("身份识别失败，请检查图片");
        }
        return json;
    }

    public static String uploadCard2(String type) throws Exception {

        String requestUrl = "https://api.weixin.qq.com/cv/ocr/idcard";
        String params = "type=" + type + "&access_token=" + getToken();
        String url = requestUrl + "?" + params;
        return url;
    }
    
    /**
     * 	微信小程序码生成
     * @return
     */
    public BufferedImage wechatAppletCode(String code) throws Exception {
        String access_token = getToken();
        RestTemplate rest = new RestTemplate();
        InputStream inputStream = null;
        OutputStream outputStream = null;
        try {
            String url = "https://api.weixin.qq.com/wxa/getwxacodeunlimit?access_token="+access_token;
            Map<String, Object> param = new HashMap<>();
            param.put("scene", "qr_code="+code);
            param.put("page", "pages/index/index");
            param.put("width", 280);
            param.put("auto_color", false);
            Map<String, Object> line_color = new HashMap<>();
            line_color.put("r", 0);
            line_color.put("g", 0);
            line_color.put("b", 0);
            param.put("line_color", line_color);
            System.out.println(("调用生成微信URL接口传参:" + param));
            MultiValueMap<String, String> headers = new LinkedMultiValueMap<>();
            HttpEntity requestEntity = new HttpEntity(param, headers);
            ResponseEntity<byte[]> entity = rest.exchange(url, HttpMethod.POST, requestEntity, byte[].class, new Object[0]);
            //  LOG.info("调用小程序生成微信永久小程序码URL接口返回结果:" + entity.getBody());
            byte[] result = entity.getBody();
            // LOG.info(Base64.encodeBase64String(result));
            inputStream = new ByteArrayInputStream(result);

            try {
                return ImageIO.read(inputStream);
            } catch (IOException e) {
                System.out.println(e.getMessage());
            }
            // ImageIO.read(inputStream);
            File file = new File(templatepath);
            if (!file.exists()){
                file.createNewFile();
            }
            outputStream = new FileOutputStream(file);
            int len = 0;
            byte[] buf = new byte[1024];
            while ((len = inputStream.read(buf, 0, 1024)) != -1) {
                outputStream.write(buf, 0, len);
            }
            outputStream.flush();
        } catch (Exception e) {
            // LOG.error("调用小程序生成微信永久小程序码URL接口异常",e);
        } finally {
            if(inputStream != null){
                try {
                    inputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if(outputStream != null){
                try {
                    outputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return null;
    } 

    public static int saveToImgByInputStream(InputStream instreams, String imgPath, String imgName){
        int stateInt = 1;
        if(instreams != null){
            try {
                File file=new File(imgPath,imgName);//可以是任何图片格式.jpg,.png等
                FileOutputStream fos=new FileOutputStream(file);
                byte[] b = new byte[1024];
                int nRead = 0;
                while ((nRead = instreams.read(b)) != -1) {
                    fos.write(b, 0, nRead);
                }
                fos.flush();
                fos.close();                
            } catch (Exception e) {
                stateInt = 0;
                e.printStackTrace();
            } finally {
            }
        }
        return stateInt;
    }
    public static void main(String[] args) {
    	try {
			SmallWxUtil small = new SmallWxUtil();
			small.wechatAppletCode("1234");
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}
}
