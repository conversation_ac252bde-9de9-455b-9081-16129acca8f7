package com.hzw.common.core.constant;

/**
 * 通用常量信息
 *
 * <AUTHOR>
 */
public interface Constants {
    /**
     * UTF-8 字符集
     */
    String UTF8 = "UTF-8";

    /**
     * GBK 字符集
     */
    String GBK = "GBK";

    /**
     * www主域
     */
    String WWW = "www.";

    /**
     * http请求
     */
    String HTTP = "http://";

    /**
     * https请求
     */
    String HTTPS = "https://";

    /**
     * 成功标记
     */
    Integer SUCCESS = 200;

    /**
     * 失败标记
     */
    Integer FAIL = 500;

    /**
     * 登录成功状态
     */
    String LOGIN_SUCCESS_STATUS = "0";

    /**
     * 登录失败状态
     */
    String LOGIN_FAIL_STATUS = "1";

    /**
     * 登录成功
     */
    String LOGIN_SUCCESS = "Success";

    /**
     * 注销
     */
    String LOGOUT = "Logout";

    /**
     * 注册
     */
    String REGISTER = "Register";

    /**
     * 登录失败
     */
    String LOGIN_FAIL = "Error";

    /**
     * 验证码有效期（分钟）
     */
    long CAPTCHA_EXPIRATION = 2;

    /**
     * 验证码有效期（分钟）
     */
    long CAPTCHA_EXPIRATION_1MIN = 1;

    /**
     * 验证码有效期（分钟）
     */
    long CAPTCHA_EXPIRATION_3MIN = 3;

    /**
     * 验证码有效期（分钟）
     */
    long TEMP_TOKEN_EXPIRATION = 30;

    /**
     * 防重提交 redis key
     */
    String REPEAT_SUBMIT_KEY = "repeat_submit:";

    String ACCESS_TOKEN = "access_token";

     String AUTHORIZATION = "JSTCC";

    /**
     * token前缀
     */
    String FRONT_TOKEN = "Bearer ";

    /**
     * 专家角色编号
     */
    String ROLE_EXPERT = "1683671230080667649";

    /**
     * 用户身份 1专家 2业务员
     */
    Integer IDENTITY_EXPERT = 1;
    Integer IDENTITY_BUSINESS = 2;

    /**
     * 帐号状态（1正常 2停用）
     */
    Integer STATUS_NORMAL = 1;
    Integer STATUS_STOPPED = 2;

    /**
     * 10：待提交（已注册，但未提交基本信息等信息到专家库管理员审核）
     *
     * 20：待审核（已提交基本信息等信息至专家库管理员审核）
     * 21: 暂停待审核
     * 30：
     * 正常（审核通过）
     * 40：
     * 退回（审核退回）
     *
     * 50：暂停（专家扣分后被系统自动暂停，或由专家库管理员手动暂停）
     * 60：
     * 出库（专家出库审核通过）
     * 70：待出库（待出库）
     */
    Integer TO_SUBMIT = 10;
    Integer TO_APPROVE = 20;
    Integer PAUSE_TO_APPROVE = 21;
    Integer REGULAR = 30;
    Integer RETURN = 40;
    Integer PAUSE = 50;
    Integer OUTBOUND = 60;
    Integer TO_OUTBOUND = 70;


    /**
     * 1：是 2：否
     */
    Integer YES = 1;
    Integer NO = 2;

}
