///***
/// * 主体基本信息 模型
/// */

import 'package:json_annotation/json_annotation.dart';

part 'SubjectBasicModel.g.dart';

@JsonSerializable(explicitToJson: true)
class SubjectBasicModel {

  SubjectBasicModel({
    this.address,
    this.branchCount,
    this.businessScope,
    this.capital,
    this.contactNumber,
    this.contactPerson,
    this.dataKey,
    this.enterpriseType,
    this.legalName,
    this.operationDeadline,
    this.registerTime,
    this.registrationAuthority,
    this.subjectCode,
    this.subjectID,
    this.subjectName,
    this.subjectTypeID,
    this.url,
    this.isexistSupplierCredit,
    this.isexistSupplierPerformance,
    this.isexistSupplierStaff,
    this.isexistSupplierSubjectQualification,
  });

  /// * 地址
  @JsonKey(defaultValue: '')
  final String address;

  /// * 分支机构数量
  @JsonKey(defaultValue: 0)
  final int branchCount;

  /// * 经营范围
  @Json<PERSON>ey(name: 'xxx', defaultValue: '')
  final String businessScope;

  /// * 注册资本
  @JsonKey(defaultValue: '')
  final String capital;

  /// * 联系电话
  @JsonKey(name: 'telcomName', defaultValue: '')
  final String contactNumber;

  /// * 联系人
  @JsonKey(name: 'contractName', defaultValue: '')
  final String contactPerson;

  /// * 
  @JsonKey(nullable: false)
  final String dataKey;

  /// * 企业类型
  @JsonKey(name: 'type', defaultValue: '')
  final String enterpriseType;

  /// * 法人代表
  @JsonKey(defaultValue: '')
  final String legalName;

  /// * 营业结束时间
  @JsonKey(name: 'endTime', defaultValue: '')
  String operationDeadline;

  /// * 成立日期
  @JsonKey(defaultValue: '')
  String registerTime;

  /// * 登记机关
  @JsonKey(defaultValue: '')
  final String registrationAuthority;

  /// * 统一社会信用代码
  @JsonKey(defaultValue: '')
  final String subjectCode;

  /// * 
  @JsonKey(nullable: false)
  final int subjectID;

  /// * 
  @JsonKey(defaultValue: '')
  final String subjectName;

  /// * 1 - 招标人，3 - 投标人，5 - 招标代理
  @JsonKey(nullable: false)
  final int subjectTypeID;

  /// * 网址
  @JsonKey(defaultValue: '')
  final String url;

  @JsonKey(defaultValue: false)
  final bool isexistSupplierStaff;

  @JsonKey(defaultValue: false)
  final bool isexistSupplierSubjectQualification;

  @JsonKey(defaultValue: false)
  final bool isexistSupplierPerformance;

  @JsonKey(defaultValue: false)
  final bool isexistSupplierCredit;

  factory SubjectBasicModel.fromJson(Map<String, dynamic> json) => _$SubjectBasicModelFromJson(json);

  Map<String, dynamic> toJson() => _$SubjectBasicModelToJson(this);

}
