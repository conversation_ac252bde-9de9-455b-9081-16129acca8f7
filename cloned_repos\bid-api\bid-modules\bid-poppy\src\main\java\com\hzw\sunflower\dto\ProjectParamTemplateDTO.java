package com.hzw.sunflower.dto;

import com.hzw.sunflower.entity.ProjectParamTemplate;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 项目参数表 数据库返回实体
 *
 * <AUTHOR>
 * @version 1.0.0 2021-04-14
 */
@ApiModel(description = "项目参数表 ")

@Data
public class ProjectParamTemplateDTO extends ProjectParamTemplate {

    @ApiModelProperty(value = "树节点", position = 1)
    private List<ProjectParamTemplateDTO> nodes;

}