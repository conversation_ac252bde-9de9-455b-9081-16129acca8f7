package com.hzw.sunflower.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzw.sunflower.controller.condition.TemCondition;
import com.hzw.sunflower.controller.response.TemplateVo;
import com.hzw.sunflower.dao.SenMessMapper;
import com.hzw.sunflower.entity.Template;
import com.hzw.sunflower.service.SenMessService;
import org.springframework.stereotype.Service;

import java.util.Date;


/**
 * <AUTHOR>
 * @since 2022/6/15
 */
@Service
public class SenMessServiceImpl extends ServiceImpl<SenMessMapper,Template> implements SenMessService {

    @Override
    public IPage<TemplateVo> getSmsList(TemCondition condition,Long userId) {
        IPage<Template> page = condition.buildPage();
        return this.baseMapper.findListByCondition(page, condition,userId);
    }

    @Override
    public Boolean addOrUpdateSms(Template req) {
        Boolean flag = false;
        LambdaQueryWrapper<Template> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(Template::getTemplateCode, req.getTemplateCode());
        if(req.getTemplateSecondCode() != null) {
            queryWrapper.eq(Template::getTemplateSecondCode, req.getTemplateSecondCode());
        }
        Template template = this.baseMapper.selectOne(queryWrapper);
        if(template != null){
            req.setId(template.getId());
            flag = this.updateById(req);
        }else {
            long time = new Date().getTime();
            //新增的时候没有id
            //Long id = req.getId();
            req.setTemplateCode(time+"");
            flag = this.save(req);
        }
        return flag;
    }

}
