package com.hzw.sunflower.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/**
 * 个人媒体信息
 *
 * <AUTHOR> 2021/07/12
 * @version 1.0.0
 */

@ApiModel(description = "个人媒体信息")
@Data
@Accessors(chain = true)
public class NoticeChangeRecordDTO {

    @ApiModelProperty(value = "提交次数", position = 1)
    private Long submitNum;

    @ApiModelProperty(value = "变更时间", position = 2)
    private Date changeTime;

    @ApiModelProperty(value = "变更类型：0 首次提交；1 撤回变更；2 退回变更", position = 3)
    private Integer changeType;

    @ApiModelProperty(value = "关联包段信息", position = 4)
    private List<PackageInfoDTO> packageInfo;

    @ApiModelProperty(value = "公告名称", position = 5)
    private String noticeName;

    @ApiModelProperty(value = "公告内容", position = 6)
    private String noticeContent;

    @ApiModelProperty(value = "公告拟发布时间", position = 7)
    private Date intendedReleaseTime;

    @ApiModelProperty(value = "公告截止时间", position = 8)
    private Date endTime;

    @ApiModelProperty(value = "公告关联附件的基本信息", position = 9)
    private List<AnnexDTO> annex;

    @ApiModelProperty(value = "公告关联的媒体的信息", position = 10)
    private List<NoticeMediaDTO> medias;
}
