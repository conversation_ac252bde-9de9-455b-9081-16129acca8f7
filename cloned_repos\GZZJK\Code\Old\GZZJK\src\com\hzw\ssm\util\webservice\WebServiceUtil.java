/**
 * 
 */
package com.hzw.ssm.util.webservice;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;

import com.hzw.ssm.expert.entity.ResultWebServiceEntity;
import com.hzw.ssm.sys.call.entity.VoiceDateEntity;
import com.hzw.ssm.sys.call.util.JsonTOBean;
import com.hzw.ssm.sys.project.entity.ResultEntity;

/**
 * <AUTHOR>
 *
 */
public class WebServiceUtil {

	public static String load(String url) throws IOException{
		URL restURL = new URL(url);
        /*
         * 此处的urlConnection对象实际上是根据URL的请求协议(此处是http)生成的URLConnection类 的子类HttpURLConnection
         */
        HttpURLConnection conn = (HttpURLConnection) restURL.openConnection();
        //请求方式
        conn.setRequestMethod("GET");
        //设置是否从httpUrlConnection读入，默认情况下是true; httpUrlConnection.setDoInput(true);
        conn.setDoOutput(true);
        //allowUserInteraction 如果为 true，则在允许用户交互（例如弹出一个验证对话框）的上下文中对此 URL 进行检查。
        conn.setAllowUserInteraction(false);
        // ps.close();
        BufferedReader bReader = new BufferedReader(new InputStreamReader(conn.getInputStream(),"UTF-8"));
        String line,resultStr="";

        while(null != (line=bReader.readLine()))
        {
        resultStr +=line;
        }
        bReader.close();
        
        return resultStr;
	 }
	
	public static void main(String[] args) {
		try {

			WebServiceUtil restUtil = new WebServiceUtil();

		    String resultString = restUtil.load("http://op.juhe.cn/idcard/query" +
		    		"?key=f4715774227799335a3575d3d7709316&idcard=320829197410905841&realname=蒋白纯");
		  System.out.println(resultString);
		  //String resultString = "{\"reason\":\"成功\",\"result\":{\"realname\":\"袁辉\",\"idcard\":\"320902199007297538\",\"res\":1},\"error_code\":0}";
		    		
		  JsonTOBean jsonToBean = new JsonTOBean();
		 // ResultWebServiceEntity entityt  = new ResultWebServiceEntity();
		  ResultWebServiceEntity dateEntity= jsonToBean.JsonToJavaBean(resultString, ResultWebServiceEntity.class);
		  System.out.println("3333");
		  //{"reason":"成功","result":{"realname":"袁辉","idcard":"320902199007297538","res":1},"error_code":0}
		    //{"reason":"参数错误：姓名或身份证格式错误","result":null,"error_code":210304}
		} catch (Exception e) {

		    // TODO: handle exception

		    System.out.print(e.getMessage());

		}

	}
}
