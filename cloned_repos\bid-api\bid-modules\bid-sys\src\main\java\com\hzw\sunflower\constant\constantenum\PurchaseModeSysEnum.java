package com.hzw.sunflower.constant.constantenum;

/**
 * 二级采购方式
 */
public enum PurchaseModeSysEnum {
    TENDER(1,"招标"),
    COMPETITIVE_CONSULTATIONS(2,"竞争性磋商"),
    GOVERNMENT_PROCUREMENT(3,"竞争性谈判"),
    GOVERNMENT_NEGOTIATION(4,"询价"),
    CONSULT(5,"征询"),
    SINGLE_SOURCE(6,"单一来源"),
    OTHER(7,"其他");

    private Integer type;
    private String desc;

    PurchaseModeSysEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }


    public static PurchaseModeSysEnum matchType(Integer type) {
        PurchaseModeSysEnum result = null;
        for (PurchaseModeSysEnum s : values()) {
            if (s.getType().equals(type)) {
                result = s;
                break;
            }
        }
        return result;
    }

    public static PurchaseModeSysEnum catchDesc(String desc) {
        PurchaseModeSysEnum result = null;
        for (PurchaseModeSysEnum s : values()) {
            if (s.getDesc().equals(desc)) {
                result = s;
                break;
            }
        }
        return result;
    }
}
