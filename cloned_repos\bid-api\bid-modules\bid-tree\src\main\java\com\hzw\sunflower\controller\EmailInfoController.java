package com.hzw.sunflower.controller;

import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.common.annotation.RepeatSubmit;
import com.hzw.sunflower.constant.MessageConstants;
import com.hzw.sunflower.controller.request.DocDataInfoREQ;
import com.hzw.sunflower.controller.request.EmailInfoAllReq;
import com.hzw.sunflower.controller.request.EmailInfoReq;
import com.hzw.sunflower.controller.response.EmailInfoVo;
import com.hzw.sunflower.entity.EmailEntity;
import com.hzw.sunflower.entity.EmailInfo;
import com.hzw.sunflower.entity.UserEmailAuthorization;
import com.hzw.sunflower.exception.ExceptionEnum;
import com.hzw.sunflower.security.utils.SecurityUtils;
import com.hzw.sunflower.service.EmailInfoService;
import com.hzw.sunflower.service.UserEmailConfigService;
import com.hzw.sunflower.util.ReceQQmail;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

/**
 * @ClassName:EmailInfoController
 * @Auther: lijinxin
 * @Description: 邮件信息
 * @Date: 2023/3/2 17:35
 * @Version: v1.0
 */
@Api(tags = "邮件信息接口")
@RestController
@RequestMapping("/emailInfo")
public class EmailInfoController {


    @Autowired
    private UserEmailConfigService userEmailConfigService;

    @Autowired
    private EmailInfoService emailInfoService;

    @Autowired
    private ReceQQmail receQQmail;

    @ApiOperation(value = "新增邮件接口")
    @RepeatSubmit
    @ApiImplicitParam(name = "addInfo", value = "新增邮件接口 ", required = true)
    @PostMapping("/saveEmailList")
    public Result<Boolean> addInfo(@RequestBody @Valid EmailInfoReq emailInfo) throws Exception {
        return emailInfoService.saveEmailList(emailInfo);
    }

    @ApiOperation(value = "查询邮件列表")
    @PostMapping("/findEmailList")
    public Result<List<EmailInfoVo>> findEmailList(@RequestBody @Valid EmailInfoReq emailInfo) throws Exception {
        return Result.ok(emailInfoService.findEmailList(emailInfo));
    }

    @ApiOperation(value = "获取所有邮箱列表")
    @PostMapping(value = "/getMailAllList")
    public Result<List<EmailEntity>> getMailAllList(@RequestBody EmailInfoAllReq req) throws Exception {
       return emailInfoService.getMailAllList(req);
    }

    @ApiOperation(value = "邮箱使用手册下载")
    @GetMapping(value = "/downloadEmailTool")
    public void downloadEncTool(HttpServletResponse response) {
        emailInfoService.downloadEmailTool(response);
    }


}
