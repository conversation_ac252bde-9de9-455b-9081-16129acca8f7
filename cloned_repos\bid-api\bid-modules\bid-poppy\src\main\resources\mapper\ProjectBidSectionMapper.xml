<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!-- 项目标段表 (t_project_bid_section) -->
<mapper namespace="com.hzw.sunflower.dao.ProjectBidSectionMapper">

    <select id="queryProjectStatusById" resultType="java.lang.String">
        SELECT
            GROUP_CONCAT( b.`status` )
        FROM
            t_project_bid_section b
        WHERE
            b.is_delete = 0
            AND b.project_id = #{id}
        GROUP BY
            b.project_id
    </select>

    <select id="queryAmountByProjectIdExceptBidId" resultType="java.lang.String"
            parameterType="com.hzw.sunflower.entity.ProjectBidSection">
        SELECT
            ROUND(SUM((b.entrust_money * d.value) / #{currency}), 2)
        FROM
            t_project_bid_section b
        LEFT JOIN t_dictionary d on d.id = b.entrust_currency
        WHERE
            b.is_delete = 0
            AND b.project_id = #{section.projectId}
            AND b.id != #{section.id}
    </select>

    <select id="queryMaxPackageNumber" resultType="java.lang.String">
        SELECT
            MAX(package_number)
        FROM
            t_project_bid_section
        WHERE
            project_id = #{projectId}
            AND is_delete = 0
    </select>

    <select id="queryProjectInfoById" resultType="com.hzw.sunflower.entity.Project">
        select p.* from t_project p
        left join t_project_bid_section pbs
        on p.id = pbs.project_id
        where p.is_delete = 0 and pbs.is_delete = 0
        and pbs.id = #{sectionId}
    </select>


    <select id="getGroupSection" resultType="java.lang.String">
        select REPLACE(section_ids,'、',',')  from t_section_group where id = #{groupId}
    </select>

    <select id="getMaxStatusSectionByProjectId" resultType="com.hzw.sunflower.entity.ProjectBidSection">
        SELECT
        t.id,
        t.STATUS,
        t.bid_round
        FROM
        (
        SELECT
        *
        FROM
        ( SELECT
                 id, `status`,bid_round,`status`*100 as sort
            FROM t_project_bid_section
            WHERE project_id = #{projectId}
            <if test="sectionId != null and sectionId != 0">
                and id = #{sectionId}
            </if>
        ) a
        ) t
        where 1 = 1
        ORDER BY t.sort DESC
        limit 1
    </select>
    <select id="listUpdate" resultType="com.hzw.sunflower.controller.project.response.ProjectBidSectionVO">
        SELECT DISTINCT
        s.* ,
	    w.winbid_people_num
        FROM
        t_project_bid_section s
        LEFT JOIN t_bid_win_notice n ON n.section_id = s.id AND n.is_delete = 0 AND n.bid_round = 2
        LEFT JOIN t_bid_win w ON w.section_id = s.id AND w.is_delete = 0 AND w.bid_round = 2
        WHERE
        s.is_delete = 0
        AND (n.id IS NULL OR n.`status` IN ( 3, 6, 11, 20 ))
        AND (
        s.project_id = #{projectId}
        AND s.abnormal_status = 0
        AND s.STATUS &gt;= #{value}
        AND s.STATUS &lt;= #{value1})
        ORDER BY
        s.package_number ASC
    </select>
    <select id="getMaxStatusSectionByProjectId2" resultType="com.hzw.sunflower.entity.ProjectBidSection">
        SELECT
        t.id,
        t.STATUS,
        t.bid_round
        FROM
        (
        SELECT
        *
        FROM
        ( SELECT
        id, `status`,bid_round,`status`*100 as sort
        FROM t_project_bid_section
        WHERE project_id = #{projectId}
        and `status` &lt; 70
        <if test="sectionId != null and sectionId != 0">
            and id = #{sectionId}
        </if>
        ) a
        ) t
        where 1 = 1
        ORDER BY t.sort DESC
        limit 1
    </select>

    <update id="updateDocSaleEndTime">
        update t_project_bid_doc_relation set sale_end_time = #{saleEndTime}, sale_end_time_type = 1 where section_id = #{sectionId} and is_delete = 0;
    </update>


    <update id="updateDocSubmitEndTime">
        update t_project_bid_doc_relation set submit_end_time = #{submitEndTime}, submit_end_time_type = 1 where section_id = #{sectionId} and is_delete = 0;
    </update>



    <select id="getProjectShareToNccInfoBySectionId" resultType="com.hzw.sunflower.dto.ProjectShareInfoDto">
        SELECT
            p.id AS projectId,
            pbs.id  AS sectionId,
            ( CASE WHEN pbs.package_number IS NOT NULL THEN CONCAT( p.purchase_number, '/', pbs.package_number ) ELSE p.purchase_number END ) AS purchase_number,
            s.share_department_code AS nccProjectDepartId
        FROM
            t_project p
            LEFT JOIN t_project_bid_section pbs ON p.id = pbs.project_id
            LEFT JOIN t_project_share_ncc s ON pbs.project_id = s.project_id
        WHERE
            p.is_delete = 0
            AND pbs.is_delete = 0
            AND s.is_delete = 0
            <if test="sectionId != null">
                and pbs.id  = #{sectionId}
            </if>
    </select>
    <select id="listInfo" resultType="com.hzw.sunflower.controller.project.response.ProjectBidSectionVO">
        SELECT
            p.id AS projectId,
            p.project_name AS purchaseName,
            p.purchase_number AS purchaseNumber,
            pbs.*
        FROM
        t_project p
        LEFT JOIN t_project_bid_section pbs ON p.id = pbs.project_id
        WHERE
        p.is_delete = 0
        AND pbs.is_delete = 0
        and p.id  = #{projectId}
        and pbs.abnormal_status = 0
        and pbs.STATUS &gt;= 5
        ORDER BY
        pbs.package_number ASC
    </select>

</mapper>
