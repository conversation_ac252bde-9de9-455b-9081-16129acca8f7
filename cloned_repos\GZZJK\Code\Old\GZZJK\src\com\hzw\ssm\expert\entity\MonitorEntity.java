package com.hzw.ssm.expert.entity;

import java.util.Date;

public class MonitorEntity {
	/**
	 * DECIMATIONBATCH	N	VARCHAR2(32)	Y			项目批次号
	 */
	private String decimationbatch;
	
	private String projectCode;
	
	private String projectName;
	
	/**
	 * 委托单位
	 */
	private String tender;
	
	private String userName;
	
	/**
	 * OPENINGTIME	N	DATE	Y			开标时间
	 */
	private String  openingTime;
	
	/**
	 * OPENINGADDRESS	N	VARCHAR2(255)	Y			开标地址
	 */
	private String openingAddress;
	
	
	


	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public String getProjectName() {
		return projectName;
	}

	public void setProjectName(String projectName) {
		this.projectName = projectName;
	}

	public String getProjectCode() {
		return projectCode;
	}

	public void setProjectCode(String projectCode) {
		this.projectCode = projectCode;
	}

	public String getTender() {
		return tender;
	}

	public void setTender(String tender) {
		this.tender = tender;
	}

	public String getDecimationbatch() {
		return decimationbatch;
	}

	public void setDecimationbatch(String decimationbatch) {
		this.decimationbatch = decimationbatch;
	}

	public String getOpeningAddress() {
		return openingAddress;
	}

	public void setOpeningAddress(String openingAddress) {
		this.openingAddress = openingAddress;
	}

	public String getOpeningTime() {
		return openingTime;
	}

	public void setOpeningTime(String openingTime) {
		this.openingTime = openingTime;
	}


	
	
	
}
