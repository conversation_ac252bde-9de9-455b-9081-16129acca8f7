package com.hzw.sunflower.dto;

import com.hzw.sunflower.entity.ProjectShare;
import com.hzw.sunflower.entity.ProjectShareNcc;
import com.hzw.sunflower.entity.ProjectTendererPower;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2021/4/14 10:37
 * @description：项目建档返回dto
 * @modified By：`
 * @version: 1.0
 */
@ApiModel(description = "项目信息dto ")
@Data
public class AgentProjectDTO extends ProjectDTO {

    @ApiModelProperty(value = "委托项目信息", position = 1)
    private List<ProjectEntrPersNumDTO> projectEntrPersNumDTO;

    @ApiModelProperty(value = "委托人信息", position = 2)
    private AgentInItDto agentUserDto;

    @ApiModelProperty(value = "被委托人信息", position = 3)
    private AgentInItDto pagentUserDto;


    @ApiModelProperty(value = "重新招标标段信息", position = 4)
    private List<ReSectionCanDTO> reSectionList;

    @ApiModelProperty(value = "招标人权利信息", position = 5)
    private List<ProjectTendererPower> tendererPowers;

    @ApiModelProperty(value = "情况", position = 6)
    private List<ProjectShare> shareList;

    @ApiModelProperty(value = "项目分摊情况(ncc)", position = 7)
    private List<ProjectShareNcc> projectShareNccList;

    @ApiModelProperty(value = "项目受理状态")
    private Integer agentStatus;

    @ApiModelProperty(value = "招标人数据权限")
    private Integer roleType;

    @ApiModelProperty(value = "项目来源 1代理机构 2招标人")
    private Integer projectSource;

}
