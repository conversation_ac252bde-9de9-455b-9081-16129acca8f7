package com.hzw.ssm.applets.action;

import com.hzw.ssm.applets.entity.Dictionary;
import com.hzw.ssm.applets.service.DictionaryService;
import com.hzw.ssm.applets.utils.GetJsonReqData;
import com.hzw.ssm.applets.utils.GetJsonRespData;
import com.hzw.ssm.applets.utils.JsonToObject;
import com.hzw.ssm.applets.utils.ResultJson;
import com.hzw.ssm.fw.base.BaseAction;
import net.sf.json.JSONObject;
import org.apache.struts2.convention.annotation.Action;
import org.apache.struts2.convention.annotation.Namespace;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @Date 2021-02-02 14:49
 * @Version 1.0
 */
@Namespace("/applets/dictionaryAction")
public class DictionaryAction extends BaseAction {

    @Autowired
    private DictionaryService dictionaryService;

    @Action("query")
    public String query() {
        JSONObject jsonReqData = GetJsonReqData.getJsonReqData();
        String toString = jsonReqData.getJSONObject("formData").toString();
        Dictionary dictionary = JsonToObject.jsonToObject(toString, Dictionary.class);
        ResultJson resultJson = dictionaryService.queryByType(dictionary);
        GetJsonRespData.getJsonRespData(resultJson);
        return null;
    }

    /**
     * 职称
     *
     * @return
     */
    @Action("title")
    public String title() {
        ResultJson resultJson = dictionaryService.queryTitle();
        GetJsonRespData.getJsonRespDataNoNull(resultJson);
        return null;
    }
}
