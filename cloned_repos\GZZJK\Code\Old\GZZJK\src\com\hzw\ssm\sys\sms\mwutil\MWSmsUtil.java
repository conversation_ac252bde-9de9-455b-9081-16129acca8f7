package com.hzw.ssm.sys.sms.mwutil;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;

import org.springframework.beans.factory.annotation.Value;

import com.hzw.ssm.sys.system.entity.SmsRecordEntity;

public class MWSmsUtil {
	//梦网短信账号
	@Value("${sms_userId}")
	private String userId;
	//梦网短信密码
	@Value("${sms_userPwd}")
	private String userPwd;
	//梦网主IP
	@Value("${sms_masterIpAddress}")
	private String masterIpAddress;
	
	//梦网备用IP1
	@Value("${sms_ipAddress1}")
	private String ipAddress1;
	//梦网备用IP2
	@Value("${sms_ipAddress2}")
	private String ipAddress2;
	//梦网发送短信接口
	/**
	 * 
	 * @description  单条发送  
	 * @param userid  用户账号
	 * @param pwd 用户密码
	 * @param isEncryptPwd 密码是否加密   true：密码加密;false：密码不加密
	 */
	public  int singleSend(String userId, String userPwd,String masterIpAddress,String ipAddress1,String ipAddress2,SmsRecordEntity smsRecordEntity)
	{
		// 返回值
		int result = -310099;
		try
		{   // 日期格式定义
			SimpleDateFormat	sdf	= new SimpleDateFormat("MMddHHmmss");
			// 参数类
			Message message = new Message();
			// 实例化短信处理对象
			CHttpPost cHttpPost = new CHttpPost();
			
			// 设置账号   将 userid转成大写,以防大小写不一致
			message.setUserid(userId.toUpperCase());
			boolean isEncryptPwd=publicModel(masterIpAddress,ipAddress1,ipAddress2);
			//判断密码是否加密。
			//密码加密，则对密码进行加密
			if(isEncryptPwd)
			{
				// 设置时间戳
				String timestamp = sdf.format(Calendar.getInstance().getTime());
				message.setTimestamp(timestamp);
				
				// 对密码进行加密
				String encryptPwd = cHttpPost.encryptPwd(message.getUserid(),userPwd, message.getTimestamp());
				// 设置加密后的密码
				message.setPwd(encryptPwd);
				
			}else
			{
				// 设置密码
				message.setPwd(userPwd);
			}
			
			// 设置手机号码 此处只能设置一个手机号码
			message.setMobile(smsRecordEntity.getSms_mobile());
			// 设置内容
			message.setContent(smsRecordEntity.getSms_content());
			// 设置扩展号
			message.setExno("");
			// 用户自定义流水编号
			message.setCustid(smsRecordEntity.getSms_id());
			// 自定义扩展数据
			message.setExdata("");
			//业务类型
			message.setSvrtype("");

			// 返回的平台流水编号等信息
			StringBuffer msgId = new StringBuffer();
			
			// 发送短信
			result = cHttpPost.singleSend(message, msgId);
			// result为0:成功;非0:失败
			smsRecordEntity.setMw_msgid(msgId.toString());
			smsRecordEntity.setMw_result(result);
			if(result == 0)
			{
				System.out.println("单条发送提交成功！");
				
				System.out.println(msgId.toString());

			}
			else
			{
				System.out.println("单条发送提交失败,错误码：" + result);
			}
		}
		catch (Exception e)
		{
			//异常处理
			e.printStackTrace();
		}
		return result;
	}
	
	/**
	 * 
	 * @description  相同内容群发
	 * @param userid  用户账号
	 * @param pwd 用户密码
	 * @param isEncryptPwd 密码是否加密   true：密码加密;false：密码不加密
	 */
	public  void batchSend(String userId, String userPwd,String masterIpAddress,String ipAddress1,String ipAddress2,SmsRecordEntity smsRecordEntity)
	{
		try
		{
			// 日期格式定义
			SimpleDateFormat	sdf	= new SimpleDateFormat("MMddHHmmss");
			// 参数类
			Message message = new Message();
			
			// 实例化短信处理对象
			CHttpPost cHttpPost = new CHttpPost();
			
			// 设置账号   将 userid转成大写,以防大小写不一致
			message.setUserid(userId.toUpperCase());
			
			//判断密码是否加密。
			boolean isEncryptPwd=publicModel(masterIpAddress,ipAddress1,ipAddress2);
			//密码加密，则对密码进行加密
			if(isEncryptPwd)
			{
				// 设置时间戳
				String timestamp = sdf.format(Calendar.getInstance().getTime());
				message.setTimestamp(timestamp);
				
				// 对密码进行加密
				String encryptPwd = cHttpPost.encryptPwd(message.getUserid(),userPwd, message.getTimestamp());
				// 设置加密后的密码
				message.setPwd(encryptPwd);
				
			}else
			{
				// 设置密码
				message.setPwd(userPwd);
			}
			
			// 设置手机号码
			message.setMobile(smsRecordEntity.getSms_mobile());
			// 设置内容
			message.setContent(smsRecordEntity.getSms_content());
			// 设置扩展号
			message.setExno("");
			// 用户自定义流水编号
			message.setCustid("");
			// 自定义扩展数据
			message.setExdata("");
			//业务类型
			message.setSvrtype("");

			// 返回的平台流水编号等信息
			StringBuffer msgId = new StringBuffer();
			// 返回值
			int result = -310099;
			// 发送短信
			result = cHttpPost.batchSend(message, msgId);
			// result为0:成功;非0:失败
			if(result == 0)
			{
				System.out.println("相同内容发送提交成功！");

				System.out.println(msgId.toString());
			}
			else
			{
				System.out.println("相同内容发送提交失败,错误码：" + result);
			}
		}
		catch (Exception e)
		{
			//异常处理
			e.printStackTrace();
		}
	}

	//接收短信报文
	public List<RPT> getRpt(String userId, String userPwd,String masterIpAddress,String ipAddress1,String ipAddress2,int retsize){
		//状态报告集合  本集合临时存储状态报告，需要将收到的状态报告保存在一个队列中，由另外一个线程去处理
		List<RPT> rpts=new ArrayList<RPT>();
		//处理后的密码
		String handlePwd=null;
		// 初始化返回值
		int result = -310099;
		//时间戳声明
		String timestamp=null;
		// 日期格式定义
		SimpleDateFormat	sdf	= new SimpleDateFormat("MMddHHmmss");
		
		// 发送管理类
		CHttpPost	cHttpPost	= new CHttpPost();
		boolean isEncryptPwd=publicModel(masterIpAddress,ipAddress1,ipAddress2);
		try
		{
			// 清空状态报告集合中的对象
			rpts.clear();
			// 将 userid转成大写,以防大小写不一致
			String userid = userId.toUpperCase();
			//判断密码是否加密。
			//密码加密，则对密码进行加密
			if(isEncryptPwd)
			{
				// 设置时间戳
			    timestamp = sdf.format(Calendar.getInstance().getTime());
				
				// 对密码进行加密
			    handlePwd = cHttpPost.encryptPwd(userId.toUpperCase(),userPwd, timestamp);
			}else
			{
				//不加密，不需要设置时间戳
				timestamp=null;
				//密码不加密，使用不加密的密码
				handlePwd=userPwd;
			}
			
			// 调用获取状态报告接口
			result = cHttpPost.getRpt(userid, handlePwd,timestamp,retsize,rpts);
			// 如果获取状态报告成功，并且有状态报告
			if(result != 0){
				System.out.println("获取状态报告失败，错误码为:" + result);
			}
			
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
		return rpts;
	}
	
	
	
	
	
	private boolean publicModel(String masterIpAddress,String ipAddress1,String ipAddress2){
		//主IP信息，请前往您的控制台获取请求域名(IP)或联系梦网客服进行获取
		String ipAddress=masterIpAddress;
		//备IP1  选填
		String ip1=ipAddress1;
		//备IP2  选填
		String ip2=ipAddress2;
		//备IP3  选填
		String ip3=null;
		//设置IP
		ConfigManager.setIpInfo(ipAddress, ip1, ip2, ip3);
		// 设置从连接池获取连接超时时间30秒
		ConfigManager.setConnectionRequestTimeout(30 * 1000);
		// 设置连接超时时间20秒
		ConfigManager.setConnectTimeout(20 * 1000);
		// 设置响应超时时间60秒
		ConfigManager.setSocketTimeout(60 * 1000);
		// 设置连接最大数20和每个ip连接最大数10
		ConfigManager.setMaxTotal(20, 10);
		//设置超时时间15秒
		ConfigManager.setClearTime(15);
		//密码是否加密   true：密码加密;false：密码不加密
		ConfigManager.IS_ENCRYPT_PWD=true;
		
		return ConfigManager.IS_ENCRYPT_PWD;
	}
}
