package com.hzw.sunflower.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hzw.sunflower.entity.BidFileSimilarity;
import com.hzw.sunflower.vo.BidDocCompareVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 投标文件相似度对比 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-04
 */
@Mapper
public interface BidDocCompareMapper extends BaseMapper<BidFileSimilarity> {

    /**
     * 查询报名供应商
     * @param sectionId
     * @return
     */
    List<BidFileSimilarity> queryApplyInfo(@Param("sectionId")Long sectionId,@Param("bidRound") Integer bidRound);

    /**
     * 查询标段报名供应商
     * @param sectionId
     * @return
     */
    List<BidDocCompareVo> queryApplyCompany(@Param("sectionId") Long sectionId,@Param("bidRound") Integer bidRound);

    /**
     * 查询开评标备案供应商
     * @param sectionId
     * @return
     */
    List<BidDocCompareVo> queryTenderRecord(@Param("sectionId") Long sectionId,@Param("bidRound") Integer bidRound);

    /**
     * 查询开评标备案供应商文件
     * @param ossIdList
     * @return
     */
    String queryTenderRecordFile(@Param("ossIdList")List<String> ossIdList);


    /**
     * 查询项目业务流程
     * @param projectId
     * @return
     */
    Integer queryProjectOperationFlow(@Param("projectId")Long projectId);
}
