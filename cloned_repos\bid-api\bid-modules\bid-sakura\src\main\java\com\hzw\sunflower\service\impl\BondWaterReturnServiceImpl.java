package com.hzw.sunflower.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.dao.BondWaterReturnMapper;
import com.hzw.sunflower.entity.BondWaterReturn;
import com.hzw.sunflower.exception.ExceptionEnum;
import com.hzw.sunflower.service.BondWaterReturnService;
import com.hzw.sunflower.service.BondWaterlabelService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @ClassName:BondWaterReturnServiceImpl
 * @Auther: lijinxin
 * @Description: 保证金其他流水退回
 * @Date: 2023/5/11 19:34
 * @Version: v1.0
 */
@Service
public class BondWaterReturnServiceImpl extends ServiceImpl<BondWaterReturnMapper, BondWaterReturn> implements BondWaterReturnService {

    @Autowired
    private BondWaterlabelService bondWaterlabelService;

    @Override
    public Result<Object> add(BondWaterReturn bondWaterReturn) {
        if(this.checkReturn(bondWaterReturn.getWaterId())){
            return Result.failed();
        }
        // 校验流水标签能否进行其他流水退还保证金
        if(  bondWaterlabelService.getWaterlabelCheck(bondWaterReturn.getWaterId())){
            return Result.failed(ExceptionEnum.BOND_WATER_OFFINE_RETURN.getCode(),ExceptionEnum.BOND_WATER_OFFINE_RETURN.getMessage());
        }
        // 新增其他标签保证金其他流水退还
        boolean save = this.save(bondWaterReturn);
        if(save){
            return Result.ok();
        }else{
            return Result.failed();
        }

    }

    @Override
    public Boolean checkReturn(Long waterId) {
        LambdaQueryWrapper<BondWaterReturn> lqw = new LambdaQueryWrapper<>();
        lqw.eq(BondWaterReturn::getWaterId,waterId);
        return  this.count(lqw) > 0;
    }
}
