package com.hzw.sunflower.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hzw.sunflower.dto.SealRoleDTO;
import com.hzw.sunflower.entity.SealRole;
import com.hzw.sunflower.entity.User;
import org.springframework.data.repository.query.Param;

import java.util.List;

/**
* SealRoleMapper接口
*
 * <AUTHOR>
 */
public interface SealRoleMapper extends BaseMapper<SealRole> {
    /**
     * 查询业务code对应的用户
     * @param businessCode
     * @return
     */
    List<User> queryNextRole(@Param("businessCode")String businessCode);

    /**
     * 根据处室 和业务code查询分管领导
     * @param departId
     * @param seal
     * @return
     */
    List<User> queryFGLD(@Param("departId")Long departId,@Param("seal")String seal);

    /**
     * 查询领导审批
     * @param companyId
     * @param sealLeader
     * @return
     */
    List<SealRoleDTO> querySealLeader(@Param("companyId")Long companyId, @Param("sealLeader")String sealLeader);
}