package com.hzw.sunflower.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.hzw.sunflower.common.BaseBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@ApiModel("流程记录(含跳过节点数据)")
@TableName("t_all_process_record")
@Data
public class AllProcessRecord extends BaseBean implements Serializable {
    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "业务code")
    private String businessCode;

    @ApiModelProperty(value = "业务id")
    private String businessId;

    @ApiModelProperty(value = "操作人id")
    private Long operatorId;
    @ApiModelProperty(value = "操作人姓名")
    private String operatorName;

    @ApiModelProperty(value = "操作")
    private String operation;

    @ApiModelProperty(value = "工作流审批节点")
    private String approvalNode;

    @ApiModelProperty(value = "是否为跳过节点  1 是  2 否")
    private Integer isJump;

    @ApiModelProperty(value = "节点备注")
    private String message;
}
