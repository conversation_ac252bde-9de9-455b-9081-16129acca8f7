package com.hzw.sunflower.constant.constantenum;

/**
 * 保证金申请退还处室处长处理状态
 */
public enum RegistrarConfirmStatusEnum {

    WAIT_DISPOSE(1, "待处理"),
    NO_DIVISION(2, "非本处项目"),
    DIVISION_AGREE(3, "本处项目同意退款"),
    DIVISION_NOT_AGREE(4,"本处项目不同意退款"),
    AGREE_REFUND(5,"同意退款"),
    NOT_AGREE_REFUND(6,"不同意退款");

    private Integer type;
    private String desc;

    RegistrarConfirmStatusEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

}
