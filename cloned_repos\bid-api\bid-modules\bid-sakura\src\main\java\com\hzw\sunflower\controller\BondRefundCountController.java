package com.hzw.sunflower.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hzw.sunflower.common.BaseController;
import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.controller.request.*;
import com.hzw.sunflower.dto.BondRefundManageDto;
import com.hzw.sunflower.dto.BondRelationNoRefundDto;
import com.hzw.sunflower.service.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;

/**
 * <p>
 * 保证金退还统计
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-15
 */
@Api(tags = "保证金退还统计")
@RestController
@RequestMapping("/bondRefundCount")
public class BondRefundCountController extends BaseController {

    @Autowired
    private BondRefundCountService bondRefundCountService;

    /**
     * 保证金退还管理
     * @return
     */
    @ApiOperation(value = "保证金退还管理")
    @ApiImplicitParam(name = "req", value = "查询条件", required = true, dataType = "RefundIdReq", paramType = "body")
    @PostMapping("/queryBondRefundManagePage")
    public Result<IPage<BondRefundManageDto>> queryBondRefundManagePage(@RequestBody RefundManageReq req) {
        IPage<BondRefundManageDto> page = bondRefundCountService.queryBondRefundManagePage(req);
        return Result.ok(page);
    }


    /**
     * 已关联保证金未退回
     * @return
     */
    @ApiOperation(value = "已关联保证金未退回")
    @ApiImplicitParam(name = "req", value = "查询条件", required = true, dataType = "RefundIdReq", paramType = "body")
    @PostMapping("/queryBondRelationNoRefundPage")
    public Result<IPage<BondRelationNoRefundDto>> queryBondRelationNoRefundPage(@RequestBody RelationNoRefundReq req) {
        IPage<BondRelationNoRefundDto> page = bondRefundCountService.queryBondRelationNoRefundPage(req);
        return Result.ok(page);
    }

    /**
     * 已关联未退回导出
     * @param condition
     * @param response
     */
    @ApiOperation(value = "导出")
    @PostMapping(value = "/exportBondRelationNoRefund")
    public void exportBondRelationNoRefund(@RequestBody RelationNoRefundReq condition, HttpServletResponse response){
        bondRefundCountService.exportBondRelationNoRefund(condition,response);
    }

}
