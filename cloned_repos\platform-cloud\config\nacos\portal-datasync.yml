spring:
  rabbitmq:
    host: 127.0.0.1
    port: 25672
    username: hzw
    password: TR0Y9s27Oda6oqKs
  cloud:
    stream:
      rabbit:
        bindings:
          expertinfo-in-0:
            consumer:
              delayedExchange: false
          expertinfo-out-0:
            producer:
              delayedExchange: false
      bindings:
        expertinfo-in-0:
          destination: expertinfo.exchange.cloud
          content-type: application/json
          group: expertinfo-group
          binder: rabbit
        expertinfo-out-0:
          destination: expertinfo.exchange.cloud
          content-type: application/json
          group: expertinfo-group
          binder: rabbit

