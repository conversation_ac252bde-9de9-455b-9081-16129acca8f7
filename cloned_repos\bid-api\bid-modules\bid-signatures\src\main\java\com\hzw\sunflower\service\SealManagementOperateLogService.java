package com.hzw.sunflower.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hzw.sunflower.dto.SealManagementOperateLogDTO;
import com.hzw.sunflower.entity.SealManagementOperateLog;
import com.hzw.sunflower.entity.condition.SealManagementOperateLogCondition;

import java.util.List;


/**
* SealManagementOperateLogService接口
*
*/
public interface SealManagementOperateLogService extends IService<SealManagementOperateLog> {
    /**
    * 根据条件分页查询列表
    *
    * @param condition 查询条件
    * @return 分页信息
    */
    IPage<SealManagementOperateLog> findInfoByCondition(SealManagementOperateLogCondition condition);

    /**
    * 根据主键ID查询信息
    *
    * @param id 主键ID
    * @return SealManagementOperateLog信息
    */
    SealManagementOperateLog getInfoById(Long id);

    /**
    * 新增
    *
    * @param sealManagementOperateLog
    * @return 是否成功
    */
    Boolean addInfo(SealManagementOperateLogDTO sealManagementOperateLog);

    /**
    * 修改单位公司 信息
    *
    * @param sealManagementOperateLog
    * @return 是否成功
    */
    Boolean updateInfo(SealManagementOperateLogDTO sealManagementOperateLog);

    /**
    * 根据主键ID删除
    *
    * @param id 主键ID
    * @return 是否成功
    */
    Boolean deleteById(Long id);

    /**
    * 根据主键ID列表批量删除
    *
    * @param idList 主键ID列表
    * @return 是否成功
    */
    Boolean deleteByIds(List<Long> idList);

}