package com.hzw.ssm.sys.system.service;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.hzw.ssm.fw.base.BaseService;
import com.hzw.ssm.fw.util.CommUtil;
import com.hzw.ssm.fw.util.HZWException;
import com.hzw.ssm.fw.util.MessageConstants;
import com.hzw.ssm.sys.system.dao.RoleMapper;
import com.hzw.ssm.sys.system.entity.MenuEntity;
import com.hzw.ssm.sys.system.entity.RoleEntity;

/**
 * 角色表操作类
 * 
 * <AUTHOR>
 * 
 */
@Service
public class RoleService extends BaseService {

	private static Log log = LogFactory.getLog(RoleService.class);

	@Autowired
	private RoleMapper roleMapper;

	/**
	 * 查询角色列表
	 * 
	 * @param userEntity
	 *            用户查询条件实体对象
	 * @return 实体对象集合
	 */
	public List<RoleEntity> queryRoleList(RoleEntity roleEntity) {
		return roleMapper.queryRoleList(roleEntity);
	}

	/**
	 * 根据角色id获取获取相应菜单id数组
	 * 
	 * @param roleEntity
	 * @return
	 */
	public String[] getMenuIdsByRoleId(String roleId) {
		List<RoleEntity> list = roleMapper.getMenuIdsByRoleId(roleId);
		String[] menuIds = new String[list.size()];
		for (int i = 0; list != null && i < list.size(); i++) {
			menuIds[i] = list.get(i).getMenuId();
		}
		return menuIds;
	}

	/**
	 * 更改角色权限
	 * 
	 * @param role
	 * @return
	 */
	@Transactional
	public void modifyRoleRelation(String roleId, String menuIds) {
		this.deleteRoleRelation(roleId);

		if (menuIds.length() > 0) {
			List<RoleEntity> rolList = new ArrayList<RoleEntity>();

			String[] mIds = menuIds.split(",");
			for (int i = 0; i < mIds.length; i++) {
				RoleEntity role = new RoleEntity();
				role.setAuthId(CommUtil.getKey());
				role.setRoleId(roleId);
				role.setMenuId(mIds[i]);
				rolList.add(role);
			}

			roleMapper.modifyRoleRelation(rolList);
		}

	}

	/**
	 * 根据角色id删除所有权限
	 * 
	 * @param roleId
	 */
	private void deleteRoleRelation(String roleId) {
		roleMapper.deleteRoleRelation(roleId);
	}

	/**
	 * 保存角色信息
	 * @param roleEntity
	 */
	public void saveRoleInfo(RoleEntity roleEntity) {
		RoleEntity role=this.getRoleInfoByName(roleEntity.getRoleName());
		if(role!=null&&!"".equals(role.getRoleId())){
			log.info(MessageConstants.SAMPLE_ROLE_NAME);
			throw new HZWException(MessageConstants.SAMPLE_ROLE_NAME);
		}
		roleEntity.setRoleId(CommUtil.getKey());
		roleMapper.saveRoleInfo(roleEntity);
		
	}
	/**
	 * 根据角色名称获得对象信息
	 * @param roleName
	 * @return
	 */
	public RoleEntity getRoleInfoByName(String roleName) {
		return roleMapper.getRoleInfoByName(roleName);
	}

	/**
	 * 修改角色信息
	 * @param roleEntity
	 */
	public void updateRoleInfo(RoleEntity roleEntity) {
		RoleEntity role=this.getRoleInfoByName(roleEntity.getRoleName());
		if(role!=null&&!"".equals(role.getRoleId())){
			log.info(MessageConstants.SAMPLE_ROLE_NAME);
			throw new HZWException(MessageConstants.SAMPLE_ROLE_NAME);
		}
		roleMapper.updateRoleInfo(roleEntity);
		
	}
	/**
	 * 根据id获得角色对象
	 * @param roleEntity
	 */
	public RoleEntity getRoleInfoById(String roleId) {
		return roleMapper.getRoleInfoById(roleId);
	}

	/**
	 * 根据id删除角色信息
	 * @param roleId
	 */
	@Transactional
	public void deleteRoleInfo(String roleId) {
		roleMapper.deleteRoleInfo(roleId);
		roleMapper.deleteRoleRelation(roleId);
	}

	/**
	 * 获得所有可用菜单集合
	 * @return
	 */
	public List<MenuEntity>  queryMenuList(){
		return roleMapper.queryMenuList();
	}

	/**
	 * 根据角色id获得所有菜单
	 * @param roleId
	 * @return
	 */
	public List<String> getAuthListByRoleId(String roleId) {
		return roleMapper.getAuthListByRoleId(roleId);
	}
}
