datasource:
  system-master:
    # jdbc 所有参数配置参考 https://lionli.blog.csdn.net/article/details/122018562
    # rewriteBatchedStatements=true 批处理优化 大幅提升批量插入更新删除性能
    url: *******************************************************************************************************************************************************************************************************************
    username: bid-cloud-dev
    password: bid-cloud-dev
  gen:
    url: ***************************************************************************************************************************************************************************************************************
    username: bid-cloud-dev
    password: bid-cloud-dev
  job:
    url: *****************************************************************************************************************************************************************************************************************
    username: bid-job-dev
    password: bid-job-dev
#  system-oracle:
#    url: *************************************
#    username: ROOT
#    password: password
#  system-postgres:
#    url: ******************************************************************************************************************************************
#    username: root
#    password: password

spring:
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    # 动态数据源文档 https://www.kancloud.cn/tracy5546/dynamic-datasource/content
    dynamic:
      # 性能分析插件(有性能损耗 不建议生产环境使用)
      p6spy: true
      # 开启seata代理，开启后默认每个数据源都代理，如果某个不需要代理可单独关闭
      seata: true
      # 严格模式 匹配不到数据源则报错
      strict: true
      hikari:
        # 最大连接池数量
        maxPoolSize: 20
        # 最小空闲线程数量
        minIdle: 10
        # 配置获取连接等待超时的时间
        connectionTimeout: 30000
        # 校验超时时间
        validationTimeout: 5000
        # 空闲连接存活最大时间，默认10分钟
        idleTimeout: 600000
        # 此属性控制池中连接的最长生命周期，值0表示无限生命周期，默认30分钟
        maxLifetime: 1800000
        # 连接测试query（配置检测连接是否有效）
        connectionTestQuery: SELECT 1
        # 多久检查一次连接的活性
        keepaliveTime: 30000

# seata配置
seata:
  # 关闭自动代理
  enable-auto-data-source-proxy: false
