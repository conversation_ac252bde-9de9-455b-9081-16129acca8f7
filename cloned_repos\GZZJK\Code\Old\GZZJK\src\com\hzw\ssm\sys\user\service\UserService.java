package com.hzw.ssm.sys.user.service;

import java.io.File;
import java.util.Date;
import java.util.List;
import com.hzw.ssm.applets.utils.SmallWxUtil;
import com.hzw.ssm.fw.base.BaseAction;
import com.hzw.ssm.fw.base.BaseService;
import com.hzw.ssm.fw.util.CommUtil;
import com.hzw.ssm.fw.util.DateUtil;
import com.hzw.ssm.fw.util.HZWException;
import com.hzw.ssm.fw.util.MD5Util;
import com.hzw.ssm.sys.system.entity.MenuEntity;
import com.hzw.ssm.sys.user.dao.UserMapper;
import com.hzw.ssm.sys.user.entity.HomeQrEntity;
import com.hzw.ssm.sys.user.entity.UserEntity;
import com.hzw.ssm.util.PictureUtil;
import com.hzw.ssm.util.file.FileUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.apache.struts2.ServletActionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.awt.image.BufferedImage;
import java.util.Map;
import java.util.Random;

/**
 * 用户表操作类
 * 
 * <AUTHOR>
 * 
 */
@Service
public class UserService extends BaseService {

	private static Log log = LogFactory.getLog(UserService.class);
	
	@Value("${QR_CODE_PATH}")
	private String qrCodePath;
	@Autowired
	private UserMapper userMapper;

	/**
	 * 查询用户列表
	 * 
	 * @param userEntity
	 *            用户查询条件实体对象
	 * @return 实体对象集合
	 */
	public List<UserEntity> queryPageUser(UserEntity userEntity) {
		return userMapper.queryPageUser(userEntity);
	}

	/**
	 * 查询用户列表记录条数
	 * 
	 * @param userEntity
	 *            用户查询条件实体对象
	 * @return 记录条数
	 */
	public int selectUserCount(UserEntity userEntity) {
		return userMapper.selectUserCount(userEntity);
	}

	/**
	 * 查询用户实体，给页面展示并编辑
	 * 
	 * @param key
	 *            根据前一画面传入的用户编号
	 * @return 根据用户编号查询出的用户实体对象
	 */
	public UserEntity selectUserById(String id) {
		return userMapper.selectUserById(id);
	}

	/**
	 * 保存用户信息，如果传入的用户编号为空则新增用户，否则更新用户 
	 * 注意1：用户更新时，可以更新用户编号，所以用到entity基类的key属性；
	 *     2：新增保存时，第一次保存为新增操作，再次点击保存时为更新操作，所以需将用户编号返回到页面；
	 * @param key
	 *            前一画面传入的用户代码
	 * @param entity
	 *            本画面映射的用户实体
	 * @return 用户代码
	 */
	@Transactional
	public String saveUser(String id, UserEntity userEntity, BaseAction action) {
		if(null == id || "".equals(id)){
			userEntity.setUser_id(CommUtil.getKey());
			userEntity.setPassword(MD5Util.reverse("123456"));
			userMapper.insertUser(userEntity);
		}else{
			userMapper.updateUser(userEntity);
		}
		return userEntity.getUser_id();
	}
	
	public void deleteUser(UserEntity userEntity){
		userMapper.deleteUser(userEntity);
	}

	/**
	 * 禁用用户
	 * 
	 * @param id
	 *            用户编号
	 */
	public void invalidUser(UserEntity userEntity) {
		userMapper.invalidUser(userEntity);
	}

	/**
	 * 重新启用用户
	 * 
	 * @param id
	 *            用户编号
	 */
	public void validUser(UserEntity userEntity) {
		userMapper.validUser(userEntity);
	}

	/**
	 * 重置用户密码
	 * 
	 * @param entity
	 *            用户对象
	 */
	public void resetPassword(UserEntity userEntity) {
		userEntity.setPassword(MD5Util.reverse("123456"));
		userMapper.resetPassword(userEntity);
	}
	
	/**
	 * 用户解锁
	 * @param id
	 */
	public void unlockUser(UserEntity userEntity){
		userMapper.unlockUser(userEntity);
	}
	
	/**
	 * 用户—修改密码
	 */
	public void changePassword(UserEntity userEntity,String newPassword){
		userEntity.setPassword(MD5Util.reverse(newPassword));
		userMapper.changePassword(userEntity);
	}
	
	/**
	 * 发送邮件时查询用户
	 * 
	 * @param userEntity
	 * @return
	 */
	public List<UserEntity> queryUserList(UserEntity userEntity) {
		return userMapper.queryUserList(userEntity);
	}
	

	/**
	 * 根据用户多个id查询用户信息
	 * 
	 * @param ids
	 * @return
	 */
	public List<UserEntity> queryUserListByIds(String ids) {
		return userMapper.queryUserListByIds(ids);
	}

	/**
	 * 用户修改自己信息
	 * @param objUser
	 */
	public void updatePersonnalUser(UserEntity objUser) {
		userMapper.updatePersonnalUser(objUser);
	}
	
	/**
	 * 验证登录名是否存在
	 * @return
	 */
	public int checkLoginCode(UserEntity objUser)
	{
		return userMapper.checkLoginCode(objUser);
	}
	
	/**
	 * 验证手机号码是否存在
	 * @param mobile
	 * @return
	 */
	public int checkMobile(UserEntity objUser)
	{
		return userMapper.checkMobile(objUser);
	}
	
	/**
	 * 根据用户编号查询用户信息
	 * @param objUser
	 * @return
	 */
	public List<UserEntity> getUserInfoByUserId(UserEntity objUser){
		return userMapper.getUserInfoByUserId(objUser);
	}
	
	/**
	 * 查询机电交易中心抽取人的手机号码
	 * @param role_name
	 * @return
	 */
	public List<UserEntity> queryExtractMobiles(String role_name)
	{
		return userMapper.queryExtractMobiles(role_name);
	}
	/**
	 * 查询该用户可访问url路径
	 */
	public int queryUrlByIdAndUrl(Map<String,Object> map){
		return userMapper.queryUrlByIdAndUrl(map);
	}
	
	/**
	 * 查询该用户 by login_code
	 */
	public UserEntity queruser(String login_code){
		return userMapper.queryUser(login_code);
	}

	public UserEntity checkLogin(UserEntity userInfo) {
		UserEntity userInfos= userMapper.checkLogin(userInfo);
		return userInfos;
	}
	
	/**
	 * 根据用户id 生成小程序推荐码图片 返回生成图片的路径
	 * @param userId
	 */
	public String generateRecommendationCode(String userId,UserEntity user) {
		//1.查询用户信息
		String path =qrCodePath;
		//2.判断是否已经生成推荐码
		try {
			if(user!=null) {
				if(null!=user.getQr_code()) {
					//2.1已经生成判断是否有生成图片	
					//2.1.1已经生成图片直接返回图片信息
					//2.1.2 没有生成图片去生成图片
					path=FileUtils.replaceSeparator(path+File.separator+user.getQr_code()+".png");
					if(!FileUtils.checkedFilePath(path)) {
						generateImages(user,path);
					}
				}else {
					//2.2 没有身处推荐码
					//2.2.1 生成4位不重复数字
					String code =createUserCode(user);
					//2.2.2 生成推荐码图片
					user.setQr_code(code);
					path=FileUtils.replaceSeparator(path+File.separator+user.getQr_code()+".png");
					generateImages(user,path);
				}
			}else {
				throw new HZWException("用户信息异常");
			}
		} catch (Exception e) {
			//异常
			e.printStackTrace();
		}	
		return path;
	}

	/**
	 * 生成唯一4位数码 并保存入库
	 * @param user
	 * @return
	 */
	private String createUserCode(UserEntity user) {
		String code =random();
		//判断是否是唯一值不是重新生成
		while (true) {
			int num=userMapper.selectUserRcode(code);
			if(num>0) {
				//重新抽取
				code =random();
			}else {
				//执行需要保存并退出操作
				user.setQr_code(code);
				userMapper.updateQCode(user);
				break;
			}
		}
		return code;
	}
	/**
	 * 生成4位随机数
	 * @return
	 */
	private String random() {
		return String.format("%04d",new Random().nextInt(9999));
	}
	//生成二维码图片
	private void generateImages(UserEntity user, String path) {
		try {
			//获取二维码
			PictureUtil tt = new PictureUtil();  
			SmallWxUtil small = new SmallWxUtil();
			BufferedImage c=small.wechatAppletCode(user.getQr_code());
			if(c!=null) {
			//合成图片
			BufferedImage d = tt.addPicture("",user.getQr_code()); 
			String ppath =FileUtils.replaceSeparator(qrCodePath+"/recom_code.png");
			ppath = FileUtils.replaceSeparator(ppath);
			BufferedImage b = tt.loadImageLocal(ppath);
			tt.writeImageLocal(FileUtils.replaceSeparator(path), tt.modifyImagetogeter(d, b,c));    
			}
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}
	public static void main(String[] args) {
		UserService u = new UserService();
		for (int i = 0; i < 100; i++) {
			System.out.println(u.random());
		}
		
	}

	/**
	 * 	根据用户id查询用户推荐
	 * @param user_id
	 */
	public HomeQrEntity selectUserRecomend(String user_id) {
		HomeQrEntity entity = new HomeQrEntity();
		UserEntity user = userMapper.selectUserById(user_id);
		//1.获取推荐码地址
		String recomPath = generateRecommendationCode(user_id,user);
		recomPath=recomPath.substring(recomPath.lastIndexOf("GZZJK")+5);
		entity.setReferralsPath(recomPath);
		//获取当前人当年的推荐数
		//
		String newdate=DateUtil.getFormatDateTime("yyyy", new Date());
		Integer num=userMapper.selectRecommend(user_id,newdate);
		entity.setReferralsCount(num);
		List<HomeQrEntity> list=userMapper.selectRecommendExamine(user.getQr_code());
		for (HomeQrEntity homeQrEntity : list) {
			entity.addCount(homeQrEntity);
		}
		return entity;
	}
	public int checkExtract(String userId){
		return userMapper.checkExtract(userId);
	}
	public List<String> selectUserJurisdiction(String userId) {
		return userMapper.selectUserJurisdiction(userId);
	}
}
