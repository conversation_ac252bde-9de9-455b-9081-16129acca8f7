package com.hzw.sunflower.service.impl;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzw.sunflower.constant.constantenum.LicenseFunctionEnum;
import com.hzw.sunflower.constant.constantenum.PurchaseModeSysEnum;
import com.hzw.sunflower.constant.constantenum.PurchaseStatusSysEnum;
import com.hzw.sunflower.constant.constantenum.PurchaseTypeSysEnum;
import com.hzw.sunflower.controller.request.NumberExampleReq;
import com.hzw.sunflower.controller.request.ProjectDetailsReq;
import com.hzw.sunflower.controller.request.PurchaseTypeReq;
import com.hzw.sunflower.controller.response.ProjectColumnRequiredVO;
import com.hzw.sunflower.controller.response.ProjectDetailsVo;
import com.hzw.sunflower.dao.*;
import com.hzw.sunflower.dto.LicenseDto;
import com.hzw.sunflower.entity.*;
import com.hzw.sunflower.service.ColumnConfigService;
import com.hzw.sunflower.service.LicenseService;
import com.hzw.sunflower.util.BeanListUtil;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.NumberFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class ColumnConfigServiceImpl extends ServiceImpl<ColumnConfigMapper, ColumnConfig> implements ColumnConfigService {

    @Autowired
    private ColumnPurchaseConfigMapper columnPurchaseConfigMapper;

    @Autowired
    private ColumnCheckConfigMapper columnCheckConfigMapper;

    @Autowired
    private SystemSettingMapper systemSettingMapper;

    @Autowired
    private ColumnRequiredMapper columnRequiredMapper;

    @Autowired
    private LicenseService licenseService;

    /**
     * 保存项目字段详细信息配置
     * @param req
     * @return
     */
    @Override
    public Boolean insertColumnConfig(ProjectDetailsReq req) {

        List<ColumnConfig> columnConfigList = new ArrayList<>();
        //项目字段设置
        columnConfigList = BeanListUtil.convertList(req.getProjectColumnRequired(),ColumnConfig.class);
        this.saveOrUpdateBatch(columnConfigList);
        //采购方式及其校验条件
        columnPurchaseConfigMapper.delete(new LambdaQueryWrapper<ColumnPurchaseConfig>());
        columnCheckConfigMapper.delete(new LambdaQueryWrapper<ColumnCheckConfig>());
        for (PurchaseTypeReq purchaseTypeReq : req.getPurchaseType()) {
            //采购方式
            ColumnPurchaseConfig columnPurchaseConfig = BeanListUtil.convert(purchaseTypeReq, ColumnPurchaseConfig.class);
            columnPurchaseConfigMapper.insert(columnPurchaseConfig);
            //校验条件
            ColumnCheckConfig columnCheckConfig = BeanListUtil.convert(purchaseTypeReq, ColumnCheckConfig.class);
            columnCheckConfig.setPurchaseConfigId(columnPurchaseConfig.getId());
            //拼接采购方式中文名称
            String purchaseType = PurchaseTypeSysEnum.matchType(columnPurchaseConfig.getPurchaseType()).getDesc();
            String purchaseMode = PurchaseModeSysEnum.matchType(columnPurchaseConfig.getPurchaseMode()).getDesc();
            String purchaseStatus = PurchaseStatusSysEnum.matchType(columnPurchaseConfig.getPurchaseStatus()).getDesc();
            String purchaseModeName = purchaseType+"-"+purchaseMode+"-"+purchaseStatus;
            columnCheckConfig.setPurchaseModeName(purchaseModeName);
            columnCheckConfigMapper.insert(columnCheckConfig);
        }
        return true;
    }

    /**
     * 查看项目详细信息设置详情
     * @return
     */
    @Override
    public ProjectDetailsVo selectColumnConfig() {
        return this.selectColumnConfig(null);
    }


    /**
     * 查看项目详细信息设置详情
     * @return
     */
    @Override
    public ProjectDetailsVo selectColumnConfig(String moduleCode) {
        ProjectDetailsVo projectDetailsVo = new ProjectDetailsVo();
        //采购方式及校验规则
        List<PurchaseTypeReq> purchaseTypeReqList = this.baseMapper.selectPurchaseTypeReqList();
        projectDetailsVo.setPurchaseTypeReqList(purchaseTypeReqList);

        LambdaQueryWrapper<ColumnRequired> lambdaQueryWrapper = new LambdaQueryWrapper();
        //  根据版本判断哪些字段关联模块
        Integer licenseFunction = getLicenseFunction();
        if(licenseFunction.equals(LicenseFunctionEnum.FUNCTION_BASIC.getType())){
            lambdaQueryWrapper.notIn(ColumnRequired::getRelationModule,1,2,3).or().isNull(ColumnRequired::getRelationModule);
        }else if(licenseFunction.equals(LicenseFunctionEnum.FUNCTION_PROFESSIONAL.getType())){
            lambdaQueryWrapper.notIn(ColumnRequired::getRelationModule,2,3).or().isNull(ColumnRequired::getRelationModule);
        }

        if(StringUtils.isNotBlank(moduleCode)){
            lambdaQueryWrapper.eq(ColumnRequired::getModuleCode,moduleCode);
        }
//        lambdaQueryWrapper.eq(ColumnRequired::getDisabled, CommonConstants.NO2);
        List<Object> objectList = columnRequiredMapper.selectObjs( lambdaQueryWrapper);
        //转类型
        List<String> stringList = objectList.stream().map(String::valueOf).collect(Collectors.toList());
        List<Long> longList = stringList.stream().map(Long::valueOf).collect(Collectors.toList());

        //字段
        List<ProjectColumnRequiredVO> projectColumnRequired = this.baseMapper.selectProjectColumn(longList);
//        //根据license版本查询是否展示全流程线上
//        //查询业务流程，id为20
//        List<ProjectColumnRequiredVO> collect = projectColumnRequired.stream().filter(a -> a.getId().equals(20L)).collect(Collectors.toList());
//        //如果是空值（初始化为空），根据当前系统版本赋值
//        if (collect.size() > 0 && collect.get(0) != null && StringUtils.isEmpty(collect.get(0).getShowProcess())) {
//            projectColumnRequired.stream().filter(x -> x.getId().equals(20L)).forEach(x -> x.setShowProcess(selectLicenseFunctionModule()));
//        }
        projectDetailsVo.setProjectColumnRequired(projectColumnRequired);
        return projectDetailsVo;
    }

    /**
     * 生成示例编号
     * @param req
     * @return
     */
    @Override
    public String generateNumberExample(NumberExampleReq req) {
        StringBuffer stringBuffer = new StringBuffer();
        String[] split = req.getGenRules().split(",");
        for (String genRule : split) {
            if ("1".equals(genRule)) {
                //查询租户简称
                LambdaQueryWrapper<SystemSetting> systemSettingLambdaQueryWrapper = new LambdaQueryWrapper<>();
                systemSettingLambdaQueryWrapper.orderByDesc(SystemSetting::getId);
                systemSettingLambdaQueryWrapper.last("limit 1");
                SystemSetting systemSetting = systemSettingMapper.selectOne(systemSettingLambdaQueryWrapper);
                if (systemSetting != null && systemSetting.getSystemAbbreviation() != null) {
                    stringBuffer.append(systemSetting.getSystemAbbreviation());
                } else {
                    stringBuffer.append("JSTCC");
                }
            } else if ("2".equals(genRule)) {
                if (req.getYearLen() != null) {
                    //年份
                    Date date = DateUtil.date();
                    String year = Convert.toStr(DateUtil.year(date));
                    //年份截取的起始位置
                    int subLen = 0;
                    if (req.getYearLen() == 2) {
                        subLen = 2;
                    }
                    //开始截取
                    String yearExample = StrUtil.sub(year, subLen, year.length());
                    stringBuffer.append(yearExample);
                }
            } else if ("3".equals(genRule)) {
                //部门编码
                String departmentCode = this.baseMapper.selectDepartmentCodeOne();
                if (StringUtils.isNotEmpty(departmentCode)) {
                    stringBuffer.append(departmentCode);
                } else {
                    stringBuffer.append("001");
                }
            }
//              else if ("4".equals(genRule)) {
//                //省内/省外
//                stringBuffer.append("1");
//            }
              else if ("5".equals(genRule)) {
                if (req.getIncreNumberLen() != null) {
                    //自增数字
                    Integer number = 1;
                    NumberFormat numberFormat = NumberFormat.getInstance();
                    // 禁用数字格式化分组。 如：  000,001
                    numberFormat.setGroupingUsed(false);
                    // 保留最小位数
                    numberFormat.setMinimumIntegerDigits(req.getIncreNumberLen());
                    // 保留最大位数
                    numberFormat.setMaximumIntegerDigits(req.getIncreNumberLen());
                    String format = numberFormat.format(number);
                    stringBuffer.append(format);
                }
            }
        }
        return stringBuffer.toString();
    }

    /**
     * 根据当前系统版本判断是否展示全流程线上
     * @return
     */
    private String selectLicenseFunctionModule() {
        String showProcess = "2,3,4";
            Integer licenseFunction = getLicenseFunction();
            List<Integer> moduleList = this.baseMapper.selectLicenseFunctionModule(licenseFunction);
            for (Integer module : moduleList) {
                if (module == 2) {
                    showProcess = "1,2,3,4";
                }
            }
        return showProcess;
    }

    /**
     * 获取license版本数字1.基础版 2.专业版 3.旗舰版
     * @return
     */
    @Override
    public Integer getLicenseFunction() {
        Integer licenseFunction = 0;
        LicenseDto license = licenseService.getLicense();
        if (license != null && license.getFunction() != null) {
            if (license.getFunction().equals(LicenseFunctionEnum.FUNCTION_BASIC.getDesc())) {
                licenseFunction = LicenseFunctionEnum.FUNCTION_BASIC.getType();
            } else if (license.getFunction().equals(LicenseFunctionEnum.FUNCTION_PROFESSIONAL.getDesc())) {
                licenseFunction = LicenseFunctionEnum.FUNCTION_PROFESSIONAL.getType();
            } else if (license.getFunction().equals(LicenseFunctionEnum.FUNCTION_ULTIMATE.getDesc())) {
                licenseFunction = LicenseFunctionEnum.FUNCTION_ULTIMATE.getType();
            }
        }
        return licenseFunction;
    }

}
