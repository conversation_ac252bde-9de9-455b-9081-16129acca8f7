package com.hzw.ssm.sys.template.dao;

import java.util.List;

import com.hzw.ssm.sys.template.entity.TemplateEntity;

public interface TemplateMapper {
	/**
	 * 根据ID查询模板信息
	 * @param entity
	 * @return
	 */
	public TemplateEntity queryTemplateById(TemplateEntity entity);
	/**
	 * 根据条件查询模板列表
	 * @param entity
	 * @return
	 */
	public List<TemplateEntity> queryTemplateList(TemplateEntity entity);
	
	
	/**
	 * 根据ID修改模板信息
	 * @param entity
	 * @return
	 */
	public int modifyTemplateById(TemplateEntity entity);
	
	/**
	 * 新增模板信息
	 * @param entity
	 * @return
	 */
	public void insertTemplate(TemplateEntity entity);
	/**
	 * 函数功能描述：根据id删除对应的模板
	 * @param entity
	 */
	public int deleteTemplateById(TemplateEntity entity);
	
	/**
	 * 函数功能描述：校验在同一个模板类型下是否有其他的生效模板
	 * @param entity
	 * @return
	 */
	public int ajaxCheckIsValid(TemplateEntity entity);
}
