// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'SubjectCreditModel.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SubjectCreditModel _$SubjectCreditModelFromJson(Map<String, dynamic> json) {
  return SubjectCreditModel(
    active: json['active'] as bool,
    approvalDate: json['approvalDate'] as String,
    approvalInstitution: json['approvalInstitution'] as String,
    caNoDataKey: json['caNoDataKey'] as String,
    checkContents: json['checkContents'] as String,
    contents: json['contents'] as String,
    createdDate: json['createdDate'] as String,
    creditBadType: json['creditBadType'] as String,
    creditId: json['creditId'] as int,
    creditNo: json['creditNo'] as String,
    creditRank: json['creditRank'] as String,
    creditType: json['creditType'] as int,
    dataKey: json['dataKey'] as String,
    expiryDate: json['expiryDate'] as String,
    modifiedDate: json['modifiedDate'] as String,
    otherExpiryDate: json['otherExpiryDate'] as String,
    punishInstitution: json['punishInstitution'] as String,
    serverity: json['serverity'] as String,
    statusValue: json['statusValue'] as int,
    subjectDataKey: json['subjectDataKey'] as String,
    subjectID: json['subjectID'] as int,
    subjectType: json['subjectType'] as String,
    validDate: json['validDate'] as String,
    year: json['year'] as String,
  );
}

Map<String, dynamic> _$SubjectCreditModelToJson(SubjectCreditModel instance) =>
    <String, dynamic>{
      'active': instance.active,
      'approvalDate': instance.approvalDate,
      'approvalInstitution': instance.approvalInstitution,
      'caNoDataKey': instance.caNoDataKey,
      'checkContents': instance.checkContents,
      'contents': instance.contents,
      'createdDate': instance.createdDate,
      'creditBadType': instance.creditBadType,
      'creditId': instance.creditId,
      'creditNo': instance.creditNo,
      'creditRank': instance.creditRank,
      'creditType': instance.creditType,
      'dataKey': instance.dataKey,
      'expiryDate': instance.expiryDate,
      'modifiedDate': instance.modifiedDate,
      'otherExpiryDate': instance.otherExpiryDate,
      'punishInstitution': instance.punishInstitution,
      'serverity': instance.serverity,
      'statusValue': instance.statusValue,
      'subjectDataKey': instance.subjectDataKey,
      'subjectID': instance.subjectID,
      'subjectType': instance.subjectType,
      'validDate': instance.validDate,
      'year': instance.year,
    };
