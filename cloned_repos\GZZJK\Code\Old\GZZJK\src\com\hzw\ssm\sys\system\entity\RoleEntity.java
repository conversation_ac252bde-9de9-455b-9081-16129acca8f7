package com.hzw.ssm.sys.system.entity;

import com.hzw.ssm.fw.base.BaseEntity;
import com.hzw.ssm.fw.util.Page;

public class RoleEntity extends BaseEntity {

	/**
	 * 角色实体类
	 */
	private static final long serialVersionUID = 8042703708357357837L;

	private String roleId;// 菜单id
	private String roleName; // 菜单名称 

	// 角色菜单关系
	private String authId;// 关系id
	private String menuId;// 菜单id

	/** 分页 */
	private Page page;


	public Page getPage() {
		return page;
	}

	public void setPage(Page page) {
		this.page = page;
	}


	public String getRoleId() {
		return roleId;
	}

	public void setRoleId(String roleId) {
		this.roleId = roleId;
	}

	public String getRoleName() {
		return roleName;
	}

	public void setRoleName(String roleName) {
		this.roleName = roleName.trim();
	}
 
	public String getAuthId() {
		return authId;
	}

	public void setAuthId(String authId) {
		this.authId = authId;
	}

	public String getMenuId() {
		return menuId;
	}

	public void setMenuId(String menuId) {
		this.menuId = menuId;
	}

}
