package com.hzw.sunflower.dto;

import lombok.Data;

/**
 * <AUTHOR>
 * @datetime 2024/06/26 16:40
 * @description: 投标邀请书填充DTO
 * @version: 1.0
 */
@Data
public class BidInviteFillDTO {

    /**
     * 采购项目名称
     */
    private String purchaseName;

    /**
     * 采购项目编号
     */
    private String purchaseNumber;

    /**
     * 被邀请的单位名称
     */
    private String invitedCompanyName;

    /**
     * 资金来源
     */
    private String fundingSource;

    /**
     * 出资比例
     */
    private String investRate;

    /**
     * 设备名称
     */
    private String deviceName;

    /**
     * 项目概况
     */
    private String projectProfile;

    /**
     * 招标范围
     */
    private String tenderScope;

    /**
     * 投标人资质要求
     */
    private String qaRequirements;

    /**
     * 投标人业绩要求
     */
    private String performanceRequirements;

    /**
     * 是否联合投标 接受或不接受
     */
    private String isReceptJointBid;

    /**
     * 接受联合投标
     */
    private String receptJointBid;

    /**
     * 不接受联合投标
     */
    private String notReceptJointBid;

    /**
     * 联合投标要求
     */
    private String jointBidRequirements;

    /**
     * 招标文件获取内容 纸质文件线下获取/电子文件线上发售
     */
    private String obtainType;
    private String obtainTime;
    private String buyTime;
    private String buyAddress;
    private String tenderFileObtainContent;

    /**
     * 邮购说明
     */
    private String postDescription;

    /**
     * 招标文件售价
     */
    private String sellingPrice;

    /**
     * 招标文件提交内容
     */
    private String submitType;
    private String submitEndTime;
    private String submitAddress;
    private String tenderFileSubmitContent1;
    private String tenderFileSubmitContent2;

    /**
     * 投标文件递交平台
     */
    private String submitPlatform;

    /**
     * 参标确认截至时间  	年 	月 	日 	时
     */
    private String confirmEndTime;

    /**
     * 招标人信息
     */
    private String tenderName;
    private String tenderAddress;
    private String tenderZipCode;
    private String tenderUserName;
    private String tenderUserPhone;
    private String tenderFax;
    private String tenderMail;
    private String tenderWebsite;
    private String tenderBank;
    private String tenderAccount;

    /**
     * 代理机构信息
     */
    private String agencyName;
    private String agencyAddress;
    private String agencyZipCode;
    private String agencyUserName;
    private String agencyUserPhone;
    private String agencyFax;
    private String agencyMail;
    private String agencyWebsite;
    private String agencyBank;
    private String agencyAccount;

    /**
     * 年
     */
    private String year;

    /**
     * 月
     */
    private String month;

    /**
     * 日
     */
    private String day;

    /**
     * 3.7投标文件的编制
     */
    private String bidCompileContent;

    /**
     * 4.1投标文件的密封和标记
     */
    private String bidMarkingContent;

    /**
     * 4.2投标文件的递交
     */
    private String bidSubmitContent1;
    private String bidSubmitContent2;
    private String bidSubmitContent3;

    /**
     * 4.3投标文件的修改与撤回
     */
    private String updateWithdrawContent;

    /**
     * 项目审批、备案机关
     */
    private String filingAuthority;

    /**
     * 批文名称及编号
     */
    private String approvalNumber;

    /**
     * 是否给予技术成果经济补偿
     */
    private String isTecCompensation;
    private String tecCompensation;
    private String noTecCompensation;

    /**
     * 技术成果经济补偿费用
     */
    private String compensationFee;

    /**
     * 技术资料押金
     */
    private String tecDeposit;

    /**
     * 图纸押金
     */
    private String drawingDeposit;
}
