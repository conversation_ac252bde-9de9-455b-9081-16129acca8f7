package com.hzw.sunflower.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzw.sunflower.constant.CommonConstants;
import com.hzw.sunflower.constant.constantenum.NoticeMediaTypeEnum;
import com.hzw.sunflower.controller.request.DeleteNoticeMediaREQ;
import com.hzw.sunflower.dao.NoticeMediaMapper;
import com.hzw.sunflower.dto.NoticeMediaDTO;
import com.hzw.sunflower.dto.PersonalMediaDTO;
import com.hzw.sunflower.entity.NoticeMedia;
import com.hzw.sunflower.entity.ProjectBidNotice;
import com.hzw.sunflower.exception.ExceptionEnum;
import com.hzw.sunflower.exception.SunFlowerException;
import com.hzw.sunflower.service.NoticeMediaRService;
import com.hzw.sunflower.service.NoticeMediaService;
import com.hzw.sunflower.service.ProjectBidNoticeService;
import com.hzw.sunflower.util.RequestUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class NoticeMediaServiceImpl extends ServiceImpl<NoticeMediaMapper, NoticeMedia> implements NoticeMediaService {

    @Autowired
    private NoticeMediaRService mediaRService;

    @Autowired
    private ProjectBidNoticeService noticeService;

    /**
     * 查询招标公告关联的媒体,包含发布截图等信息
     * @param noticeId 公告ID
     * @return 媒体信息列表
     * （弃用）
     */
    @Override
    public List<NoticeMediaDTO> findNoticeMediaWithAnnex(Long noticeId) {

        if(RequestUtil.isEmpty(noticeId)){
            throw new SunFlowerException(ExceptionEnum.ILLEGAL_PARAMETER_EXCEPTION,null);
        }

        return this.baseMapper.findNoticeMediaWithAnnex(noticeId);
    }

    /**
     *  根据公告ID查询与其关联的媒体ID列表
     * @param noticeId 公告ID
     * @return 关联媒体ID的列表
     */
    @Override
    public List<Long> findNoticeMediaIds(Long noticeId) {
        return baseMapper.findNoticeMediaIds(noticeId);
    }

    /**
     * 查询个人媒体
     * @param userId 用户ID
     * @return 媒体信息列表
     */
    @Override
    public List<PersonalMediaDTO> findPersonalMedias(Long userId) {

        List<PersonalMediaDTO> vo = this.baseMapper.findPersonalMedias(userId);
        Map<String, PersonalMediaDTO> map = new HashMap<>();
        vo.forEach((i) -> {
            map.put(i.getMediaName(),i);
        });

        QueryWrapper<NoticeMedia> wrapper = new QueryWrapper<>();
        wrapper.eq("state",1)
                .eq("is_delete", CommonConstants.NO);
        List<NoticeMedia> publicMedias = list(wrapper);

        for (NoticeMedia item : publicMedias) {
            if(!map.containsKey(item.getMediaName())){
                item
                        .setNumberUse(0)
                        .setId(null)
                        .setState(NoticeMediaTypeEnum.PRIVATE_MEDIA.getValue());
                insertOrUpdate(List.of(item),true);
                PersonalMediaDTO dto = new PersonalMediaDTO();
                dto
                        .setMediaId(item.getId())
                        .setMediaName(item.getMediaName())
                        .setMediaUrl(item.getMediaUrl())
                        .setUseTimes(item.getNumberUse())
                        .setState(item.getState())
                        .setIsSystem(true);
                vo.add(dto);
            }
        }

        return vo;
    }

    /**
     * 查询招标公告关联的媒体
     * @param noticeId 用户招标公告ID
     * @return 媒体信息列表
     */
    @Override
    public List<PersonalMediaDTO> findNoticeMedias(Long noticeId) {

        List<PersonalMediaDTO> noticeMedias = this.baseMapper.findNoticeMedias(noticeId);

        if(CollUtil.isEmpty(noticeMedias)){
            noticeMedias = new ArrayList<>();
        }
        return noticeMedias;
    }

    /**
     * 根据ID删除媒体
     * @param req 删除媒体请求实体
     * @return 媒体信息列表
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean deleteById(DeleteNoticeMediaREQ req) {
        NoticeMedia media = getById(req.getMediaId());
        if((media == null) || (!media.getState().equals(NoticeMediaTypeEnum.NOTICE_MEDIA.getValue()))){
            throw new SunFlowerException(ExceptionEnum.MEDIA_DELETE_ILLEGAL,null);
        }

        ProjectBidNotice notice = noticeService.getById(req.getNoticeId());
        if(notice == null){
            throw new SunFlowerException(ExceptionEnum.NOTICE_OPERATION_FAILED,null);
        }else{
            switch (notice.getNoticeProgress()){
                case 2:
                case 4:
                case 5:
                    throw new SunFlowerException(ExceptionEnum.NOTICE_OPERATION_FAILED,null);
            }
        }

        removeById(req.getMediaId());
        mediaRService.deleteById(req);

        return true;
    }

    /**
     * 批量操作 封装
     *
     * @param list 待批量操作的媒体信息列表
     * @param isInsert 批量新增为true，批量删除为false
     *
     * @return true 代表操作成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean insertOrUpdate(List<NoticeMedia> list, Boolean isInsert) {
        if(CollUtil.isNotEmpty(list)){
            if (isInsert){
                if(saveBatch(list)){
                    return true;
                }
                throw new RuntimeException();
            }else {
                if(updateBatchById(list)){
                    return true;
                }
                throw new RuntimeException();
            }
        }
        return true;
    }

    /**
     * 个人媒体使用次数+1
     *
     * @param noticeId 招标公告ID
     * @return 符合条件的记录行数
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean updateMediaUseTimes(Long noticeId) {
        Boolean flag = (baseMapper.updateMediaUseTimes(noticeId) > 0) ? true : false;
        if (!flag){
            throw new SunFlowerException(ExceptionEnum.UPDATE_PERSON_MEDIA_FAILED,ExceptionEnum.UPDATE_PERSON_MEDIA_FAILED.getMessage());
        }

        return flag;
    }
}
