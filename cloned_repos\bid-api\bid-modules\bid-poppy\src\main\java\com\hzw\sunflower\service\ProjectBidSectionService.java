package com.hzw.sunflower.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.controller.project.request.*;
import com.hzw.sunflower.controller.project.response.ProjectBidSectionVO;
import com.hzw.sunflower.controller.project.response.ProjectSectionPurchaseVo;
import com.hzw.sunflower.dto.ProjectBidSectionDTO;
import com.hzw.sunflower.dto.ProjectShareInfoDto;
import com.hzw.sunflower.dto.SupplierInfoDTO;
import com.hzw.sunflower.entity.ProjectBidSection;

import java.util.List;

/**
 * 项目标段表 Service接口
 *
 * <AUTHOR>
 * @version 1.0.0 2021-04-07
 */
public interface ProjectBidSectionService extends IService<ProjectBidSection> {


    /**
     * 根据项目ID查询项目标段表信息
     *
     * @param projectId 项目ID
     * @return 项目标段信息
     */
    List<ProjectBidSectionDTO> getProjectBidSectionByProjectId(Long projectId);


    /**
     * 修改项目标段表 信息
     *
     * @param projectBidSection 项目标段表 信息
     * @return 是否成功
     */
    Boolean updateProjectBidSection(ProjectBidSection projectBidSection);


    /**
     * 根据主键ID列表批量删除项目标段表
     *
     * @param idList 主键ID列表
     * @return 是否成功
     */
    Boolean deleteProjectBidSectionByIds(List<Long> idList);

    /**
     * 新增多个项目标段信息
     *
     * @param projectBidReq 项目标段表 信息
     * @return 是否成功
     */
    Boolean addProjectBidSections(ProjectBidReq projectBidReq);

    /**
     * 判断委托金额
     *
     * @param projectBidSectionList 标段信息集合
     * @return 超出 未超出 标段信息不存在
     */
    String judgeEntrustMoney(List<ProjectBidSection> projectBidSectionList);

    /**
     * 项目包件新建修改-项目冗余字段处理
     *
     * @param id 主键ID
     * @return 是否成功
     */
    String queryProjectStatusByProjectId(Long projectId);

    /**
     * 更新项目信息和标段信息
     *
     * @param projectBidReq
     * @return
     */
    Result<Object> updateProjectAndSectionInfo(ProjectBidReq projectBidReq);

    /**
     * 更新标段顺序
     *
     * @param list
     * @return
     */
    Boolean changePackageNumber(List<PackageNumberREQ> list);

    /**
     * 查询上次投标人信息
     *
     * @param supplierInfo
     * @return
     */
    List<SupplierInfoDTO> getSupplierInfo(SupplierInfoREQ supplierInfo);

    /**
     * 获取指定投标人信息
     *
     * @param sectionId
     * @return
     */
    List<SupplierInfoDTO> getAssignSupplierInfo(Long sectionId);

    /**
     * 修改项目信息
     *
     * @param projectBidReq
     * @return
     */
    Boolean updateProject(ProjectBidReq projectBidReq);

    /**
     * 资格预审按项目完善信息
     *
     * @param projectId
     * @param projectBidSection
     * @return
     */
    Boolean perfectPreQualificationInfo(Long projectId, ProjectBidSection projectBidSection);

    /**
     * 删除公告和文件
     *
     * @param projectId
     * @param sectionId
     * @return
     */
    Boolean deleteNoticeAndDoc(Long projectId, Long sectionId);

    /**
     * 新增重新招标标段信息
     *
     * @param reBidInfoREQ
     * @return
     */
    Boolean addReBidInfo(ReBidInfoREQ reBidInfoREQ);


    /**
     * 检索各标段状态
     *
     * @param id 项目ID
     * @return 各标段状态拼接
     */
    String queryProjectStatusById(Long id);


    /**
     * 根据标段id 获取标段顺序
     *
     * @param sections 标段id 集合
     * @return 标段号集合
     */
    String getSectionPackageNumberList(String sections);

    /**
     * 专家库 根据项目信息查看标段数据
     * @param projectId
     * @return
     */
    List<ProjectBidSection> getSectionListByExpert(Long projectId);

    /**
     * 获取标段信息
     * @param id
     * @return
     */
    ProjectBidSectionVO getSectionInfoById(Long id);

    /**
     * 查询套内的标段信息
     * @param sectionGroupId
     * @return
     */
    List<ProjectBidSection> getGroupSection(Long sectionGroupId);

    /**
     * 通过projectId获取最大标段的状态
     * @param projectId
     * @return
     */
    ProjectBidSection getMaxStatusSectionByProjectId(Long projectId,Long sectionId);

    /**
     * 修改标段采购文件的文件发售截至时间
     * @param sectionId
     * @param saleEndTime
     * @return
     */
    Boolean updateDocSaleEndTime(Long sectionId, String saleEndTime);

    /**
     * 修改标段响应文件递交截至时间
     * @param sectionId
     * @param submitEndTime
     * @return
     */
    Boolean updateDocSubmitEndTime(Long sectionId, String submitEndTime);

    List<ProjectBidSectionVO> listUpdate(Long projectId, Integer value, Integer value1);

    /**
     * 通过projectId获取最大标段的状态,除异常状态
     * @param projectId
     * @param sectionId
     * @return
     */
    ProjectBidSection getMaxStatusSectionByProjectId2(Long projectId, Long sectionId);

    /**
     * 根据标段id查询标段的采购方式
     * @param req
     * @return
     */
    ProjectSectionPurchaseVo selectSectionById(ProjectBidSectionReq req);

    /**
     * 查询ncc中的项目编号和项目的所在部门
     * @param sectionId
     * @return
     */
    ProjectShareInfoDto getProjectShareToNccInfoBySectionId(Long sectionId);

    /**
     * 查询标段列表信息 用印申请
     * @param projectId
     * @return
     */
    List<ProjectBidSectionVO> listInfo(Long projectId);
}
