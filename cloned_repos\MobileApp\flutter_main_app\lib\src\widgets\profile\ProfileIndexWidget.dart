///***
/// * 我的 - 首页
/// */

import 'package:flutter/material.dart';

import 'package:flutter_main_app/src/constants/theme.dart';
import 'package:flutter_main_app/src/utils/ScreenUtils.dart';
import 'package:flutter_main_app/src/fonts/fonts.dart';

import 'package:flutter_main_app/src/components/CustomSafeArea.dart';
import 'package:flutter_main_app/src/components/CustomListTile.dart';

class ProfileIndexWidget extends StatelessWidget {
  const ProfileIndexWidget({Key key}) : super(key: key);

  /// * 路由名称
  static const String _routeName = 'ProfileIndex';
  static String get routeName => _routeName;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: CustomSafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            /// * AppBar
            GestureDetector(
              onTap: () {
                Navigator.pushNamed(
                  context,
                  'ProfilePersonalInformationIndex',
                );
              },
              child: Container(
                width: ScreenUtils.screenWidth,
                height: 180.0,
                decoration: BoxDecoration(
                  image: DecorationImage(
                    image: AssetImage('assets/images/profile_appbar_background.png'),
                    fit: BoxFit.cover,
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: <Widget>[
                    /// * 头像、用户名
                    Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: <Widget>[
                        Container(
                          margin: const EdgeInsets.only(
                            left: 13.0,
                            right: 14.0,
                          ),
                          width: 58.0,
                          height: 58.0,
                          decoration: BoxDecoration(
                            color: themeBackgroundColor,
                            borderRadius: BorderRadius.circular(29.0),
                          ),
                          child: Image.asset(
                            'assets/images/qq.png',
                            fit: BoxFit.fill,
                          ),
                        ),
                        Column(
                          mainAxisSize: MainAxisSize.min,
                          mainAxisAlignment: MainAxisAlignment.start,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: <Widget>[
                            Text(
                              'Anthony',
                              style: TextStyle(
                                fontSize: 18.0,
                                color: Colors.white,
                              ),
                            ),
                            Container(
                              margin: const EdgeInsets.only(top: 7.0),
                              padding: const EdgeInsets.symmetric(horizontal: 6.0),
                              height: 20.0,
                              decoration: BoxDecoration(
                                border: Border.all(
                                  color: Colors.white,
                                ),
                              ),
                              child: Text(
                                '普通用户',
                                style: TextStyle(
                                  fontSize: 12.0,
                                  color: Colors.white,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                    /// * 箭头图标
                    Padding(
                      padding: const EdgeInsets.only(right: 18.0),
                      child: Icon(
                        Icons.arrow_forward_ios,
                        size: 14.0,
                        color: Colors.white,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            /// * 数据相关
            /// *   - 我的收藏
            /// *   - 我的跟踪
            /// * 设置相关
            /// *   - 关于我们
            /// *   - 设置
            Expanded(
              child: ListView(
                physics: BouncingScrollPhysics(),
                padding: const EdgeInsets.all(0.0),
                children: <Widget>[
                  ...this._buildDataListTiles(context),
                  Divider(
                    height: 10.0,
                    thickness: 10.0,
                    color: themeBackgroundColor,
                  ),
                  ...this._buildSettingsListTiles(context),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  ///***
  /// * 生成数据相关 ListTile 列表 UI
  /// *
  /// * @param {BuildContext} context
  /// *
  /// * @return {List<Widget>}
  /// */
  List<Widget> _buildDataListTiles(BuildContext context) {
    const List<String> titles = ['我的收藏', '我的跟踪'];
    const List<IconData> leadingIconDatas = [FontsHelper.starBorderIconData, FontsHelper.clockIconData];
    const List<Color> leadingIconColors = [Color(0xffd96666), Color(0xffe0ad69)];
    final List<String> navigateToWidgets = ['ProfileCollectionIndex', 'ProfileReminderIndex'];

    final List<Widget> listTiles = this._buildListTiles(
      context: context,
      titles: titles,
      leadingIconDatas: leadingIconDatas,
      leadingIconColors: leadingIconColors,
      navigateToWidgets: navigateToWidgets,
    );

    return listTiles;
  }

  ///***
  /// * 生成设置相关 ListTile 列表 UI
  /// *
  /// * @param {BuildContext} context
  /// *
  /// * @return {List<Widget>}
  /// */
  List<Widget> _buildSettingsListTiles(BuildContext context) {
    const List<String> titles = ['关于我们', '设置'];
    const List<IconData> leadingIconDatas = [FontsHelper.aboutUsIconData, FontsHelper.settingsIconData];
    const List<Color> leadingIconColors = [Color(0xff588ef5), Color(0xff789ee6)];
    final List<String> navigateToWidgets = ['ProfileAboutUsIndex', 'ProfileSettingsIndex'];

    List<Widget> listTiles = this._buildListTiles(
      context: context,
      titles: titles,
      leadingIconDatas: leadingIconDatas,
      leadingIconColors: leadingIconColors,
      navigateToWidgets: navigateToWidgets,
    );

    return listTiles;
  }

  ///***
  /// * 生成通用 ListTile 列表 UI
  /// *
  /// * @param {BuildContext} context
  /// * @param {List<String>} titles
  /// * @param {List<IconData>} leadingIconDatas
  /// * @param {List<Color>} leadingIconColors
  /// * @param {List<Widget>} navigateToWidgets
  /// *
  /// * @return {List<Widget>}
  /// */
  List<Widget> _buildListTiles({
    BuildContext context,
    List<String> titles,
    List<IconData> leadingIconDatas,
    List<Color> leadingIconColors,
    List<String> navigateToWidgets,
  }) {
    const double leftPadding = 18.0;

    const double leadingIconSize = 14.0;
    const double leadingIconRightPadding = 11.0;

    const IconData trailingIconData = Icons.arrow_forward_ios;
    const double trailingIconSize = 14.0;
    const Color trailingIconColor = Color(0xffc2c2c2);

    final List<Widget> listTiles = [];

    for (int i = 0, len = titles.length; i < len; i++) {
      final Widget listTile = CustomListTile(
        title: titles[i],
        onTap: () {
          Navigator.pushNamed(
            context,
            navigateToWidgets[i],
          );
        },
        padding: const EdgeInsets.only(left: leftPadding),
        leading: Padding(
          padding: const EdgeInsets.only(right: leadingIconRightPadding),
          child: Icon(
            leadingIconDatas[i],
            size: leadingIconSize,
            color: leadingIconColors[i],
          ),
        ),
        trailing: Icon(
          trailingIconData,
          size: trailingIconSize,
          color: trailingIconColor,
        ),
        isFirstChild: i == 0,
      );

      listTiles.add(listTile);
    }

    return listTiles;
  }
}
