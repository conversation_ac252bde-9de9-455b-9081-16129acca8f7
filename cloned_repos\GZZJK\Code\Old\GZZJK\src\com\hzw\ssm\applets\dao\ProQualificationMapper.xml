<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hzw.ssm.applets.dao.ProQualificationMapper">

    <resultMap id="BaseResultMap" type="ProQualification">
        <id column="id" property="id"/>
        <result column="user_id" property="user_id"/>
        <result column="certificate" property="certificate"/>
        <result column="certificate_no" property="certificate_no"/>
        <result column="certificate_fileid" property="certificate_fileid"/>
        <result column="delete_flag" property="delete_flag"/>
        <result column="old_certificate" property="old_certificate"/>
        <result column="get_time" property="getTime"/>
    </resultMap>

    <sql id="Base_Column_List">
		 t.id, t.user_id, t.certificate, t.certificate_no, t.certificate_fileid, t.delete_flag, t.old_certificate, t.get_time
	</sql>

    <insert id="insertProQualification" parameterType="ProQualification">
		insert into t_expert_practice (
			id,
			user_id,
			certificate,
			certificate_no,
			certificate_fileid,
			delete_flag,
			old_certificate,
			get_time
		)
		values(
			#{id,jdbcType=VARCHAR},
			#{user_id,jdbcType=VARCHAR},
			#{certificate,jdbcType=VARCHAR},
			#{certificate_no,jdbcType=VARCHAR},
			#{certificate_fileid,jdbcType=VARCHAR},
			#{delete_flag,jdbcType=NUMERIC},
			#{old_certificate,jdbcType=VARCHAR},
			#{getTime,jdbcType=VARCHAR}
		)
	</insert>

    <update id="updateProQualification" parameterType="ProQualification">
        update t_expert_practice
        <set>
            <if test="certificate != null and !&quot;&quot;.equals(certificate)">
                certificate = #{certificate},
            </if>
            <if test="certificate_no != null and !&quot;&quot;.equals(certificate_no)">
                certificate_no = #{certificate_no},
            </if>
            <if test="certificate_fileid != null and !&quot;&quot;.equals(certificate_fileid)">
                certificate_fileid = #{certificate_fileid},
            </if>
            <if test="old_certificate != null and !&quot;&quot;.equals(old_certificate)">
                old_certificate = #{old_certificate},
            </if>
            <if test="delete_flag != null and !&quot;&quot;.equals(delete_flag)">
                delete_flag = #{delete_flag},
            </if>
            <if test="getTime != null and !&quot;&quot;.equals(getTime)">
                get_time = #{getTime},
            </if>
        </set>
        where
            id = #{id}
    </update>

    <select id="getProQualification" parameterType="ProQualification" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List"/>
        from
            t_expert_practice t
        where
            id = #{id}
    </select>

    <select id="queryProQualification" parameterType="ProQualification" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List"/>
        from
            t_expert_practice t
        where
            delete_flag = 0
            <if test="user_id != null and !&quot;&quot;.equals(user_id)">
                and user_id = #{user_id}
            </if>
            <if test="certificate != null and !&quot;&quot;.equals(certificate)">
                and certificate = #{certificate}
            </if>
    </select>

    <select id="queryQualificationNumById" resultType="int">
        select
            count(t.id)
        from
            t_expert_practice t
        where
            delete_flag = 0
            and user_id = #{expertId}
            and certificate is not null
    </select>

    <select id="getProQualifications" parameterType="ProQualification" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List"/>
        from
            t_expert_practice t
        where
            t.user_id = #{user_id}
            and t.delete_flag = 0
        order by
            t.id asc
    </select>

    <update id="deleteProQualification" parameterType="ProQualification">
		UPDATE
			t_expert_practice
		SET
			delete_flag = 1
		where
			id = #{id}
	</update>

</mapper>
