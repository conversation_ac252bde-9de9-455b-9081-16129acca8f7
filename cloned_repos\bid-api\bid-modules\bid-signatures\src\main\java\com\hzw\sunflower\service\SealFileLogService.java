package com.hzw.sunflower.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hzw.sunflower.dto.SealFileLogDTO;
import com.hzw.sunflower.entity.SealFileLog;
import com.hzw.sunflower.entity.condition.SealFileLogCondition;

import java.util.List;


/**
* SealFileLogService接口
*
*/
public interface SealFileLogService extends IService<SealFileLog> {
    /**
    * 根据条件分页查询列表
    *
    * @param condition 查询条件
    * @return 分页信息
    */
    IPage<SealFileLog> findInfoByCondition(SealFileLogCondition condition);

    /**
    * 根据主键ID查询信息
    *
    * @param id 主键ID
    * @return SealFileLog信息
    */
    SealFileLog getInfoById(Long id);

    /**
    * 新增
    *
    * @param sealFileLog
    * @return 是否成功
    */
    Boolean addInfo(SealFileLogDTO sealFileLog);

    /**
    * 修改单位公司 信息
    *
    * @param sealFileLog
    * @return 是否成功
    */
    Boolean updateInfo(SealFileLogDTO sealFileLog);

    /**
    * 根据主键ID删除
    *
    * @param id 主键ID
    * @return 是否成功
    */
    Boolean deleteById(Long id);

    /**
    * 根据主键ID列表批量删除
    *
    * @param idList 主键ID列表
    * @return 是否成功
    */
    Boolean deleteByIds(List<Long> idList);

}