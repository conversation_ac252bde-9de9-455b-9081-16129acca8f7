package com.hzw.sunflower.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.hzw.sunflower.common.BaseBean;
import io.swagger.annotations.ApiModel;

import java.util.Date;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * Created by pj on 2021/07/14
 */
@ApiModel("t_approval_opinion表")
@TableName("t_approval_opinion")
@Data
@Accessors(chain = true)
public class ApprovalOpinion extends BaseBean {
    /**
     * 主键
     *
     * @mbg.generated
     */
    private Long id;

    /**
     * 审批内容
     *
     * @mbg.generated
     */
    private String approvalContent;

    /**
     * 审批状态； 0 提交审核；1 撤回；2 退回;3同意
     *
     * @mbg.generated
     */
    private Integer approvalType;

    /**
     * 审批人
     *
     * @mbg.generated
     */
    private Long approvalUserId;


    /**
     * 审批姓名
     *
     * @mbg.generated
     */
    private String approvalUserName;

    /**
     * 项目ID
     *
     * @mbg.generated
     */
    private Long projectId;

    /**
     * 标段ID
     *
     * @mbg.generated
     */
    private Long sectionId;

    /**
     * 审批时间
     *
     * @mbg.generated
     */
    private Date approvalTime;

    /**
     * 公告ID
     *
     * @mbg.generated
     */
    private Long noticeId;

    /**
     * 招标文件表ID
     *
     * @mbg.generated
     */
    private Long docId;

    /**
     * 下一步处理人
     */
    private String nextUserName;


}
