package ${package_name}.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import ${package_name}.dto.${modelname}DTO;
import ${package_name}.entity.${modelname};
import ${package_name}.entity.condition.${modelname}Condition;

import java.util.List;


/**
* ${modelname}Service接口
*
*/
public interface ${modelname}Service extends IService<${modelname}> {
    /**
    * 根据条件分页查询列表
    *
    * @param condition 查询条件
    * @return 分页信息
    */
    IPage<${modelname}> findInfoByCondition(${modelname}Condition condition);

    /**
    * 根据主键ID查询信息
    *
    * @param id 主键ID
    * @return ${modelname}信息
    */
    ${modelname} getInfoById(Long id);

    /**
    * 新增
    *
    * @param ${lowmodel}
    * @return 是否成功
    */
    Boolean addInfo(${modelname}DTO ${lowmodel});

    /**
    * 修改单位公司 信息
    *
    * @param ${lowmodel}
    * @return 是否成功
    */
    Boolean updateInfo(${modelname}DTO ${lowmodel});

    /**
    * 根据主键ID删除
    *
    * @param id 主键ID
    * @return 是否成功
    */
    Boolean deleteById(Long id);

    /**
    * 根据主键ID列表批量删除
    *
    * @param idList 主键ID列表
    * @return 是否成功
    */
    Boolean deleteByIds(List<Long> idList);

}