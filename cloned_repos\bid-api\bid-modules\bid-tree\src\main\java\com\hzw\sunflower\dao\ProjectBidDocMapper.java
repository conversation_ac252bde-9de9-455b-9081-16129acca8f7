package com.hzw.sunflower.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hzw.sunflower.controller.request.DocBidRoundREQ;
import com.hzw.sunflower.controller.response.ProjectBidDocVO;
import com.hzw.sunflower.dto.*;
import com.hzw.sunflower.entity.ApplyInfo;
import com.hzw.sunflower.entity.ProjectBidDoc;
import com.hzw.sunflower.entity.condition.ProjectBidDocCondition;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * TProjectBidDocMapper接口
 */
public interface ProjectBidDocMapper extends BaseMapper<ProjectBidDoc> {

    /**
     * 查询线上付款的供应商信息
     *
     * @param formerSectionId
     * @param bidRound
     * @param supplierIds
     * @return
     */
    List<ApplyInfo> getOnlinelist(@Param("formerSectionId")Long formerSectionId, @Param("bidRound")Integer bidRound, @Param("supplierIds")List<Long> supplierIds);

    /**
     * 招标文件分页查询
     *
     * @param page
     * @param condition
     * @return
     */
    IPage<ProjectBidDocDTO> queryBidDocInfo(IPage<ProjectBidDocVO> page, @Param("condition") ProjectBidDocCondition condition);

    /**
     * 查询项目所有标段文件进度
     *
     * @param docBidRoundREQ
     * @return
     */
    List<ProjectBidDocDTO> queryDocProgress(@Param("req") DocBidRoundREQ docBidRoundREQ);


    /**
     * 查询文件进度
     *
     * @param docId
     * @return
     */
    List<ProjectBidDocDTO> queryDocInfo(@Param("docId") Long docId);

    /**
     * 根据ID集合查询代办列表信息
     * @param docIds
     * @return
     */
    List<AppingTaskDocDTO> queryAppingTaskListByDocids(@Param("docIds") List<Long> docIds);

    /**
     * 查询招标文件审核内容
     *
     * @param docId
     * @return
     */
    List<ReviewBidDocDTO> queryReviewInfo(@Param("docId") Long docId);

    /**
     * 查询公告拟发布时间前24小时未审批结束和到公告拟发布时间尚未发布
     *
     * @return
     */
    List<ProjectBidDoc> queryDocInfoForTask();

    /**
     * 招标文件关联信息
     *
     * @param docId
     * @return
     */
    List<ProjectBidDocDTO> queryRelevantInfo(@Param("docId") Long docId);

    /**
     * 获取重新招标标段标书费 付款状态
     *
     * @param formerSectionId
     * @return
     */
    List<ReBidInfoDTO> queryRebidInfo(@Param("formerSectionId") Long formerSectionId);

    /**
     * 查询文件相关标段信息
     *
     * @param docId
     * @return
     */
    List<DocSectionDetailDTO> queryDocSectionInfo(@Param("docId") Long docId,@Param("bidRound") Integer bidRound);

    /**
     * 查询文件详情信息
     *
     * @param docId
     * @return
     */
    List<DocDetailDTO> queryDocDetailInfo(@Param("docId") Long docId);

    /**
     * 根据标段ID和招标阶段查询文件ID
     *
     * @param docBidRoundREQ
     * @return
     */
    Long queryDocId(@Param("req") DocBidRoundREQ docBidRoundREQ);


    ProjectBidDoc getDocInfoBySectionId(@Param("sectionId") Long formerSectionId,@Param("bidRound") Integer bidRound);


    /**
     * 根据标段集合获取挂网时间
     * @param sectionIds
     * @param bidRound
     * @return
     */
    List<ProjectBidDocDTO> findReleaseTimeBySections(@Param("sectionIds") List<Long> sectionIds,@Param("bidRound") Integer bidRound);


    List<Long> queryDocIdByBidIds(@Param("sectionIds") List<Long> sectionIds,@Param("bidRound") Integer bidRound);

    List<ProjectBidDoc> getProjectIdInfo(@Param("projectId")Long projectId, @Param("sectionId")Long sectionId, @Param("bidRound") Integer bidRound);

    List<ProjectBidDoc> getProjectIdInfoUpdate(@Param("id")Long id);
}
