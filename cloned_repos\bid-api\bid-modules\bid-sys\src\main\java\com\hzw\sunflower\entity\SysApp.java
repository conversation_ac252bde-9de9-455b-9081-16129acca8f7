package com.hzw.sunflower.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.hzw.sunflower.common.BaseBean;
import io.swagger.annotations.ApiModel;
import lombok.Data;



@ApiModel(description = "进场场地 ")
@TableName("t_sys_app")
@Data
public class SysApp extends BaseBean {

    private Long id;

    private String name;

    private Integer status;

    private String sn;

    private String secretKey;

    private String url;

    private String indexUrl;

    private String image;

    private Integer orderNo;

    private Integer currentSys;

}
