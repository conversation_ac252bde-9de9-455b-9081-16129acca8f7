package com.hzw.sunflower.util;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.controller.responese.BidDocDeviceVo;
import com.hzw.sunflower.dto.BidDocDeviceExcelDto;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date ：Created in 2024/1/11 9:36
 * @description：excel导入监听
 * @modified By：`
 * @version: 1.0
 */
@Slf4j
public class BidDocDeviceExcelListener extends AnalysisEventListener<BidDocDeviceExcelDto> {


    private final List<BidDocDeviceExcelDto> analysisRows = new ArrayList<>();

    private boolean isTempHead = true;

    @SneakyThrows
    @Override
    public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
        if (!headMap.containsKey(0) || !headMap.containsKey(1) || !headMap.containsKey(2) || !headMap.containsKey(3)
                || !headMap.containsKey(4)
                || !headMap.get(0).equals("设备名称") || !headMap.get(1).equals("规格")
                || !headMap.get(2).equals("数量及单位") || !headMap.get(3).equals("交货期")
                || !headMap.get(4).equals("交货地点")) {
            isTempHead = false;
        }
    }

    @Override
    public void invoke(BidDocDeviceExcelDto dto, AnalysisContext analysisContext) {
        analysisRows.add(dto);
    }

    @SneakyThrows
    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {

    }

    public Result<List<BidDocDeviceVo>> getResultRow() {
        if(!isTempHead){
            return Result.failed("模板错误，请下载模板重新导入");
        }
        if(analysisRows.size() > 100){
            return Result.failed("导入数据不能超过100行");
        }
        List<BidDocDeviceVo> resultList = BeanListUtil.convertList(analysisRows, BidDocDeviceVo.class);
        return Result.ok(resultList);
    }

}
