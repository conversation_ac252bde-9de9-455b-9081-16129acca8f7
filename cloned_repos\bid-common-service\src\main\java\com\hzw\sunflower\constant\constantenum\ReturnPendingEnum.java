package com.hzw.sunflower.constant.constantenum;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public enum ReturnPendingEnum {
    /**
     * 1、采购公告退回、撤回退回（资格预审公告、招标公告、xx邀请函等）
     * 2、采购文件退回、撤回退回（资格预审文件、招标文件等）
     * 3、更正公告退回、撤回退回
     * 4、澄清修改退回、撤回退回
     * 5、异议回复退回、撤回退回
     * 6、确认中标候选人退回、撤回退回
     * 7、中标候选人公示退回、撤回退回
     * 8、中标结果公示退回、撤回退回
     * 9、申请中标通知书退回、撤回退回
     * 10、重新申请中标通知书退回、撤回退回
     * 11、申请资格预审通知书退回、撤回退回
     * 12、重新申请资格预审通知书退回、撤回退回
     * 13、确认评标结果退回
     * 15、异常公告退回、撤回退回
     * 16、归档退回
     * 17、材料申请退回
     * 18、用锁申请退回
     * 19、换专票申请退回
     * 20、退标书费申请退回
     * 21、登记供应商退回
     * 22、补录付款凭证退回
     * 23、代理服务费修改退回
     * 24、数据修改退回
     * 25、保证金退还退回
     * 26、专家抽取退回
     * 27、专家抽取备案退回
     */
    ALL_INFO("qb","全部"),
    TENDER_BID("tenderBid","采购公告退回"),
    TENDER_DOC("tenderDoc","采购文件退回"),
    SUPPLEMENT_BID("supplementBid","更正公告退回"),
    CLARIFY_BID("clarifyBid","澄清修改退回"),
    OBJECTION("objection","异议回复退回"),
    WIN_CANDIDATE_BID("winningBidCandidate","中标候选人公示退回"),
    WIN_BID("winCandidateBid","中标结果公示退回"),
    WIN_NOTICE("winNotice","申请中标通知书退回"),
    RE_WIN_NOTICE("reWinNotice","重新申请中标通知书退回"),
    BID_WIN_NOTICE_PRE("bidWinNoticePre","申请资格预审通知书退回"),
    RE_BID_WIN_NOTICE_PRE("reBidWinNoticePre","重新申请资格预审通知书退回"),
    WINNING_BID_CANDIDATE("winBid","确认评标结果退回"),
    WINNING_BID_CANDIDATE_ZGYS("winBid_zgys","确认评审结果退回"),
    ABNORMAL_NOTICE("abnormalNotice","异常公告退回"),
    ARCHIVE("archive","归档退回"),
    MATERIAL_APPLICATION("materialApplication","材料申请退回"),
    WITH_LOCK("withLock","用锁申请退回"),
    SPECIAL_TICKET_CHANGE("specialTicketChange","换专票申请退回"),
    TENDER_FEES_APPLY("tenderFeesApply","退标书费申请退回"),
    REGISTERED_SUPPLIERS("registeredSuppliers","登记供应商退回"),
    RERECORD_PAYMENT("rerecordPayment","补录付款凭证退回"),
    AGENCY_SERVICE("agencyService","代理服务费修改退回"),
    DATA_MODIFICATION("dataModification","数据修改退回"),
    DEPOSIT_REFUND("depositRefund","保证金退还退回"),
    EXPERT_EXTRACTION("expertExtraction","专家抽取退回"),
    EXPERTS_SELECTED_RECORD("expertsSelectedRecord","专家抽取备案退回"),
    ONLINE_BOND_RETURN("onlineBondReturn","线上保证金退还退回"),
    EXPERTS_APPRAISE_RETURN("expertsAppraiseReturn","专家评价退回"),
    EXPERTS_OUT_RETURN("expertsOutReturn","专家出库退回"),
    EXPERTS_PAUSE_RETURN("expertsPauseReturn","专家暂停退回"),
    EXPERTS_UPDATE_RETURN("expertsUpdateReturn","专家信息变更退回"),
    SEAL_APPLICATION_RETURN("sealApplicationReturn","用印申请退回"),
    COMPLAIN_RECOVER_RETURN("complainRecoverReturn","恢复招标活动退回"),
    ;

    private String code;
    private String desc;

    ReturnPendingEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String type) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    //讲枚举转换成list格式，这样前台遍历的时候比较容易，列如 下拉框 后台调用toList方法，你就可以得到code 和name了
    public static List toList() {
        List list = new ArrayList();
        for (ReturnPendingEnum airlineTypeEnum : ReturnPendingEnum.values()) {
            Map<String, Object> map = new HashMap<String, Object>();
            map.put("code", airlineTypeEnum.getCode());
            map.put("desc", airlineTypeEnum.getDesc());
            list.add(map);
        }
        return list;
    }
}

