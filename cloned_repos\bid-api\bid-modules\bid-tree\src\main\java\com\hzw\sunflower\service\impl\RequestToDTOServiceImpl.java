package com.hzw.sunflower.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.hzw.sunflower.constant.CommonConstants;
import com.hzw.sunflower.constant.RegexConstants;
import com.hzw.sunflower.constant.constantenum.*;
import com.hzw.sunflower.controller.request.NoticeMediaREQ;
import com.hzw.sunflower.controller.request.UpdateNoticeREQ;
import com.hzw.sunflower.controller.response.ProjectBidNoticeVO;
import com.hzw.sunflower.dao.*;
import com.hzw.sunflower.dto.*;
import com.hzw.sunflower.entity.*;
import com.hzw.sunflower.exception.ExceptionEnum;
import com.hzw.sunflower.exception.SunFlowerException;
import com.hzw.sunflower.service.*;
import com.hzw.sunflower.util.RequestUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 请求到DTO的转换
 *
 * <AUTHOR>
 * @version 1.0.0 2021/07/07
 */
@Service
public class RequestToDTOServiceImpl implements RequestToDTOService {

    @Autowired
    private ProjectService projectService;

    @Autowired
    private ProjectBidNoticeService noticeService;

    @Autowired
    private NoticePackageRService packageRService;

    @Autowired
    private NoticeMediaService mediaService;

    @Autowired
    private NoticePackageRMapper noticePackageRMapper;

    @Autowired
    private RelevancyAnnexMapper relevancyAnnexMapper;

    @Autowired
    private NoticeChangeRecordMapper noticeChangeRecordMapper;


    @Autowired
    private ProjectBidSectionService projectBidSectionService;

    /**
     * 招标公告 初始化数据
     */
    public UpdateNoticeDTO checkAndInitData(UpdateNoticeREQ req){
        // 校验名称
        noticeNameIsIllegal(req.getNoticeName());
        // 校验内容
        noticeContentIsIllegal(req.getNoticeContent());
        // 媒体是否为空
        if (req.getMustHasMedias().intValue()==1&&req.getMedias()!=null&&req.getMedias().size()==0){
            throw new SunFlowerException(ExceptionEnum.MEDIA_DETAILS_ISEMPTY,null);
        }
//        projectIsExists(req.getProjectId());
        Project project = projectService.getProjectById(req.getProjectId());
        // 如果项目不存在，抛出异常
        if(project == null){
            throw new SunFlowerException(ExceptionEnum.PROJECT_NOT_FOUND, null);
        }
        // 页面提交过来的标段
        List<Long> sectionIds = req.getSectionIds();
        // 判断按项目 预审公开 关联所有预审公开包
        ProjectBidSection projectBidSection=projectBidSectionService.getById(sectionIds.get(0));
        if(PurchaseStatusEnum.PRE_TRIAL.getType().equals(projectBidSection.getPurchaseStatus())
                && PreQualificationEnum.PROJECT.getType().toString().equals(project.getPreQualificationMtd())
                &&projectBidSection.getBidRound().equals(BidRoundEnum.ZGYS.getType())){
            QueryWrapper queryWrapper=new QueryWrapper<ProjectBidSection>()
                    .eq("bid_round",BidRoundEnum.ZGYS.getType())
                    .gt("status",5)
                    .eq("project_id",project.getId())
                    .eq("purchase_mode",projectBidSection.getPurchaseMode())
                    .eq("purchase_status",PurchaseStatusEnum.PRE_TRIAL.getType());
            List<ProjectBidSection> list = projectBidSectionService.list(queryWrapper);
            List<Long> newSectionIds =list.stream().map(m->{
                 return m.getId();
             }).collect(Collectors.toList());
            sectionIds=newSectionIds;
            req.setSectionIds(newSectionIds);
        }
        //判断项目包段是否处于未被占用状态
        packageIsAvailable(req);
        UpdateNoticeDTO updateNoticeDTO=new UpdateNoticeDTO();
        ProjectBidNotice notice=null;

        // 查询所有可以关联的标段结合
        List<Long> packageIds = packageRService.findAllAvailablePackageIds(req.getProjectId());
        // 判断是否新增
        if(req.getNoticeId() != null) {
//            TProjectBidNotice notice = noticeIsExists(req.getNoticeId());
            notice = noticeService.getById(req.getNoticeId());
            // 如果招标公告不存在，抛出异常
            if(notice == null){
                throw new SunFlowerException(ExceptionEnum.NOTICE_NOT_FOUND, null);
            }
            if (notice.getNoticeProgress().equals(NoticeProgressEnum.DRAFTING.getValue()) ||
                    notice.getNoticeProgress().equals(NoticeProgressEnum.WITHDRAWAL.getValue()) ||
                    notice.getNoticeProgress().equals(NoticeProgressEnum.RETURN.getValue())){
                //

            }else{
                // 其它状态禁止修改公告数据
                throw new SunFlowerException(ExceptionEnum.NOTICE_OPERATION_FAILED, null);
            }
            // 处理标段数据
            // 查询当前公告已关联的标段
            List<Long> oldIds = packageRService.findNoticePackageIdsByNoticeId(notice.getId());
            // 将当前公告已关联的标段加入到所有可以关联的标段集合
            packageIds.addAll(oldIds);
            // 判断是否提交过来的标段中包含了不可以关联的标段
            if(!packageIds.containsAll(sectionIds)){
                throw new SunFlowerException(ExceptionEnum.NOTICE_PACKAGE_ILLEGAL,null);
            }
            // 为notice赋值
            BeanUtils.copyProperties(req,notice);
            // 页面提交过来的标段和当前公告已关联的标段集合 取交集
            Collection<Long> commonIds = CollUtil.intersection(sectionIds, oldIds);
            // 删除当前公告已关联的标段集合中交集部分得到需要删除关联的标段
            oldIds.removeAll(commonIds);
            // 删除页面提交过来的标段集合中交集部分得到需要更新关联的标段
            List<Long> updatePackageIds=new ArrayList<>();
            updatePackageIds.addAll(sectionIds);
            updatePackageIds.removeAll(commonIds);
            updateNoticeDTO.setDeletePackageIds(oldIds).setUpdatePackageIds(updatePackageIds);

            // 准备 标段关系表数据
            final Long  noticeId=notice.getId();
            List<NoticePackageR> insertPackages = new ArrayList<>();
            updatePackageIds.forEach((id) -> {
                NoticePackageR packageR = new NoticePackageR()
                        .setNoticeId(noticeId)
                        .setSectionId(id)
                        .setState(NoticeTypeEnum.PURCHASE_NOTICE.getValue());
                                packageR.setIsDelete(CommonConstants.NO);
                                packageR.setVersion(0);

                insertPackages.add(packageR);
            });
            updateNoticeDTO.setInsertPackages(insertPackages);
            /////////////招标公告附件///////////
            final Long  projectId=req.getProjectId();
            List<RelevancyAnnex> insertAnnexR = new ArrayList<>();
            if(!CollectionUtil.isEmpty(req.getAnnex())){
                // 如果有附件，则新增附件关联
                req.getAnnex().forEach((id) -> {
                    RelevancyAnnex annex = new RelevancyAnnex()
                            .setProjectId(projectId)
                            .setFileId(id)
                            .setFileType(AnnexTypeEnum.BIDNOTICE.getValue())
                            .setBusId(noticeId);

                                annex.setIsDelete(CommonConstants.NO);
                                annex.setVersion(0);

                    insertAnnexR.add(annex);
                });
            }
            updateNoticeDTO.setInsertAnnexes(insertAnnexR);
        }else{

            // 新增
            notice = new ProjectBidNotice();

            // 为notice赋值
            BeanUtils.copyProperties(req,notice);
            notice.setNoticeProgress(NoticeProgressEnum.DRAFTING.getValue());
            notice.setIsDelete(CommonConstants.NO);
            notice.setVersion(0);
            // 判断是否提交过来的标段中包含了不可以关联的标段
            if(!packageIds.containsAll(sectionIds)){
                throw new SunFlowerException(ExceptionEnum.NOTICE_PACKAGE_ILLEGAL,null);
            }
            //
            updateNoticeDTO
                    .setDeletePackageIds(null)
                    .setUpdatePackageIds(sectionIds);
            List<NoticePackageR> insertPackages = new ArrayList<>();
            sectionIds.forEach((id) -> {
                NoticePackageR packageR = new NoticePackageR()
//                        .setNoticeId(noticeId)
                        .setSectionId(id)
                        .setState(NoticeTypeEnum.PURCHASE_NOTICE.getValue());
                packageR.setIsDelete(CommonConstants.NO);
                packageR.setVersion(0);

                insertPackages.add(packageR);
            });
            updateNoticeDTO.setInsertPackages(insertPackages);
            /////////////招标公告附件///////////
            final Long  projectId=req.getProjectId();
            List<RelevancyAnnex> insertAnnexR = new ArrayList<>();
            if(!CollectionUtil.isEmpty(req.getAnnex())){
                // 如果有附件，则新增附件关联
                req.getAnnex().forEach((id) -> {
                    RelevancyAnnex annex = new RelevancyAnnex()
                            .setProjectId(projectId)
                            .setFileId(id)
                            .setFileType(AnnexTypeEnum.BIDNOTICE.getValue());
//                            .setBusId(noticeId);

                    annex.setIsDelete(CommonConstants.NO);
                    annex.setVersion(0);

                    insertAnnexR.add(annex);
                });
            }
            updateNoticeDTO.setInsertAnnexes(insertAnnexR);
//            updateNoticeDTO= initUpdateNoticeDTO(req,notice);
        }
        // 公告表状态是否暂存
        if(req.getSaveForShort()){
            notice.setState(2);
        }else{
            notice.setState(1);
        }
        notice.setNoticeOssKey(req.getNoticeFileId());
        updateNoticeDTO.setMedias(req.getMedias());
        updateNoticeDTO.setNotice(notice);
        return updateNoticeDTO;
    }


    /**
     * 根据请求中的关联媒体信息生成需要插入的关联媒体 （弃用）
    */
    private List<NoticeMediaR> updateMedia(UpdateNoticeREQ req){

        List<NoticeMediaREQ> mediaDTOS = req.getMedias();
        Long noticeId = req.getNoticeId();

        // 获取私有媒体
        List<PersonalMediaDTO> personalMedia = mediaService.findPersonalMedias(getJwtUser().getUserId());
        List<PersonalMediaDTO> noticeMedias = new ArrayList<>();
        if(!RequestUtil.isEmpty(noticeId)){
            noticeMedias = mediaService.findNoticeMedias(noticeId);
        }

        // 生成map
        Map<String,PersonalMediaDTO> personalMap = new HashMap<>();
        personalMedia.forEach((i) -> {
            personalMap.put(i.getMediaName(),i);
        });
        Map<String,PersonalMediaDTO> noticeMap = new HashMap<>();
        noticeMedias.forEach((i) -> {
            noticeMap.put(i.getMediaName(),i);
        });

        // 仅当请求中的媒体有值时
        List<Long> mediaIds = new ArrayList<>();
        Set<String> set = new HashSet<>();
        if(CollectionUtil.isNotEmpty(mediaDTOS)){
            for (NoticeMediaREQ i : mediaDTOS) {
                if(StrUtil.isEmpty(i.getMediaName()) || StrUtil.isEmpty(i.getMediaUrl())){
                    throw new SunFlowerException(ExceptionEnum.MEDIA_DETAILS_ISEMPTY,null);
                }
                if(!i.getMediaUrl().matches(RegexConstants.URL_REGEX)){
                    throw new SunFlowerException(ExceptionEnum.MEDIA_URL_ILLEGAL,null);
                }
                if((i.getMediaName().length() > 50) || (i.getMediaUrl().length() > 50)){
                    throw new SunFlowerException(ExceptionEnum.MEDIA_DETAILS_TOO_LONG,null);
                }
                set.add(i.getMediaName());
                set.add(i.getMediaUrl());

                // 看当前用户是否拥有此媒体
//                NoticeMedia media;
//                if(personalMap.containsKey(i.getMediaName())){
//                    if(RequestUtil.isEmpty(i.getMediaId())){
//                        throw new SunFlowerException(ExceptionEnum.MEDIA_DETAILS_ISEMPTY,null);
//                    }else{
//                        mediaIds.add(i.getMediaId());
//                    }
//                }else{
//                    if(noticeMap.containsKey(i.getMediaName())){
//                        if(RequestUtil.isEmpty(i.getMediaId())){
//                            throw new SunFlowerException(ExceptionEnum.MEDIA_DETAILS_ISEMPTY,null);
//                        }else{
//                            mediaIds.add(i.getMediaId());
//                        }
//                    }else{
//                        NoticeMedia item = new NoticeMedia()
//                                .setMediaName(i.getMediaName())
//                                .setMediaUrl(i.getMediaUrl())
//                                .setNumberUse(0)
//                                .setState(NoticeMediaTypeEnum.NOTICE_MEDIA.getValue());
//                        item.setIsDelete(0);
//                        item.setVersion(0);
//                        mediaService.insertOrUpdate(List.of(item),true);
//                        mediaIds.add(item.getId());
//
//                    }
//                }
            }
        }
        // 判断媒体信息是否重复
        if(set.size() != (2 * mediaDTOS.size())){
            throw new SunFlowerException(ExceptionEnum.NOTICE_MEDIA_REPEAT,null);
        }

        // 如果id的list不为空
        List<NoticeMediaR> mediaRS = new ArrayList<>();
        if(!mediaIds.isEmpty()){

            // 需要插入的关联媒体
            mediaIds.forEach((i) -> {
                NoticeMediaR noticeMediaR = new NoticeMediaR()
                        .setMediaId(i)
                        .setAnnexId(null)
//                .setState(MediaTypeEnum.NOTICE_MEDIA.getValue());
                        .setState(3);
                noticeMediaR.setIsDelete(CommonConstants.NO);
                noticeMediaR.setVersion(0);
                mediaRS.add(noticeMediaR);
//                mediaRS.add(initNoticeMediaRelation(i));
            });
        }
        return mediaRS;
    }

    /**
     * 生成NoticeChangeRecord的列表
     *
     * <AUTHOR>
     * @return vo 公告详情
     * @version 1.0.0 2021/07/06
     */
    public NoticeChangeRecord toNoticeChangeRecord(ProjectBidNoticeVO vo) {

        List<Long> packages = noticePackageRMapper.findNoticePackageIds(vo.getNoticeId());
        String strPackages = packages.stream().map(n -> String.valueOf(n)).collect(Collectors.joining(","));

        List<Long> annexs= relevancyAnnexMapper.findNoticeAnnexIds(vo.getNoticeId());
        String strAnnexs=annexs.stream().map(n -> String.valueOf(n)).collect(Collectors.joining(","));
//        List<Long> medias = mediaService.findNoticeMediaIds(vo.getNoticeId());

//        int max = packages.size();
//        if(max < annex.size()){
//            max = annex.size();
//        }
//        if(max < medias.size()){
//            max = medias.size();
//        }

//        List<NoticeChangeRecord> noticeChangeRecords = new ArrayList<>();
        // 循环填充
        Date changeTime = new Date();
        Integer noticeProgress = vo.getNoticeProgress();

        Integer changeType = ChangeTypeEnum.SUBMIT.getValue();
        if(noticeProgress.equals(NoticeProgressEnum.WITHDRAWAL.getValue())){
            changeType = ChangeTypeEnum.WITHDRAW.getValue();
        }else if(noticeProgress.equals(NoticeProgressEnum.RETURN.getValue())){
            changeType = ChangeTypeEnum.RETURN.getValue();
        }

        Long submitNum = noticeChangeRecordMapper.findMaxSubmit(vo.getNoticeId());
        if(submitNum == null){
            submitNum = 1L;
        }else{
            submitNum ++;
        }
        NoticeChangeRecord noticeChangeRecord = new NoticeChangeRecord()
                .setProjectId(vo.getProjectId())
//                .setMediaId(vo.getNoticeId())
                .setNoticeId(vo.getNoticeId())
                .setNoticeName(vo.getNoticeName())
                .setNoticeContent(vo.getNoticeContent())
                .setIntendedReleaseTime(vo.getIntendedReleaseTime())
                .setEndTime(vo.getEndTime());
                noticeChangeRecord.setIsDelete(CommonConstants.NO);
                noticeChangeRecord.setVersion(0);



                noticeChangeRecord.setSubmitNum(submitNum)
                .setChangeType(changeType)
                .setChangeTime(changeTime)
                .setSectionId(strPackages)
                .setAnnexId(strAnnexs);


        return noticeChangeRecord;
    }



    /**
     * 上传发布截图（弃用）
    */
    private UpdateNoticeDTO initUpdateMediaAnnex(UpdateNoticeREQ req,ProjectBidNotice notice){
        List<Long> mediaIds = mediaService.findNoticeMediaIds(req.getNoticeId());

        List<NoticeMediaREQ> medias = req.getMedias();
        if(medias.size() != mediaIds.size()){
            throw new SunFlowerException(ExceptionEnum.NOTICE_MEDIA_ILLEGAL,null);
        }

        List<NoticeMediaR> insertMedias = new ArrayList<>();
        medias.forEach((i) -> {
            // 新传入的媒体必须和数据库中此公告已经关联的媒体完全一致
//            if(!mediaIds.contains(i.getMediaId())){
//                throw new SunFlowerException(ExceptionEnum.NOTICE_MEDIA_ILLEGAL,null);
//            }

//            if(CollectionUtil.isNotEmpty(i.getPics())){
//                // 如果图片附件存在
//                if(i.getPics().size() > 5){
//                    throw new SunFlowerException(ExceptionEnum.NOTICE_MEDIA_ANNEX_ILLEGAL,null);
//                }
//                i.getPics().forEach((k) -> {
//                    NoticeMediaR insertMedia = new NoticeMediaR();
//                    insertMedia
//                            .setNoticeId(req.getNoticeId())
//                            .setMediaId(i.getMediaId())
//                            .setState(NoticeMediaTypeEnum.NOTICE_MEDIA.getValue())
//                            .setAnnexId(k);
//                    insertMedia.setIsDelete(0);
//                    insertMedia.setVersion(0);
//                    insertMedias.add(insertMedia);
//                });
//            }else{
//                // 如果图片附件不存在
//                NoticeMediaR insertMedia = new NoticeMediaR();
//                insertMedia
//                        .setNoticeId(req.getNoticeId())
//                        .setMediaId(i.getMediaId())
//                        .setState(NoticeMediaTypeEnum.NOTICE_MEDIA.getValue());
//                insertMedia.setIsDelete(0);
//                insertMedia.setVersion(0);
//                insertMedias.add(insertMedia);
//            }
        });

        return new UpdateNoticeDTO()
                .setNotice(notice)
//                .setInsertMedias(insertMedias)
                ;
    }

    /**
     * 判断项目包段是否处于未被占用状态
     *
     * @param req 招标公告变更请求实体
    */
    private void packageIsAvailable(UpdateNoticeREQ req){
        List<Long> ids = req.getSectionIds();
        Long noticeId = req.getNoticeId();
        Long projectId = req.getProjectId();

        if(CollUtil.isNotEmpty(ids)){
//            isRepeat(ids);
            // 判断是否有重复的标段
            if(CollUtil.isNotEmpty(ids)){
                Set<Long> set = Set.copyOf(ids);
                if(set.size() != ids.size()){
                    throw new SunFlowerException(ExceptionEnum.ID_REPEAT,null);
                }
            }
            List<Long> availaleIds = packageRService.findAllAvailablePackageIds(projectId);

            // 如果招标公告ID存在，则需要将此公告所关联的包段的ID存入可用包段ID列表
            if(req.getNoticeId() != null){
                List<Long> noticePackageIds = packageRService.findNoticePackageIdsByNoticeId(noticeId);
                availaleIds.addAll(noticePackageIds);
            }
            // 判断可用包段ID列表是否包含变更请求所包含的所有包段ID
            if(!availaleIds.containsAll(ids)){
                throw new SunFlowerException(ExceptionEnum.NOTICE_PACKAGE_ILLEGAL,null);
            }
        }
    }

    /**
     * 验证招标公告结束时间
    */
//    private void endTimeIsAcailable(ProjectBidNotice notice){
//        if(!noticeEndTimeIsIllegal(notice.getEndTime(),notice.getIntendedReleaseTime())){
//            throw new SunFlowerException(ExceptionEnum.NOTICE_END_TIME_ILLEGAL,ExceptionEnum.NOTICE_END_TIME_ILLEGAL.getMessage());
//        }
//    }

    /**
     * 对ID进行重复判断
     *
     * @param ids 待判断的ID列表
    */
//    private void isRepeat(List<Long> ids){
//        if(CollUtil.isNotEmpty(ids)){
//            Set<Long> set = Set.copyOf(ids);
//            if(set.size() != ids.size()){
//                throw new SunFlowerException(ExceptionEnum.ID_REPEAT,null);
//            }
//        }
//    }

    /**
     * 获取当前用户
     */
    private JwtUser getJwtUser(){
        // 获取当前用户
        return (JwtUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
    }

    /**
     * 通用初始化
     */
//    private NoticeChangeRecord initNoticeChangeRecord(ProjectBidNoticeVO vo){
//
//        NoticeChangeRecord noticeChangeRecord = new NoticeChangeRecord()
//                .setProjectId(vo.getProjectId())
//                .setMediaId(vo.getNoticeId())
//                .setNoticeId(vo.getNoticeId())
//                .setNoticeName(vo.getNoticeName())
//                .setNoticeContent(vo.getNoticeContent())
//                .setIntendedReleaseTime(vo.getIntendedReleaseTime())
//                .setEndTime(vo.getEndTime());
//        noticeChangeRecord.setIsDelete(0);
//        noticeChangeRecord.setVersion(0);
//
//        return noticeChangeRecord;
//    }

    /**
     * 生成noticeMediaR
     *
     * <AUTHOR>
     * @return NoticeMediaR
     * @version 1.0.0 2021/07/06
     */
//    private NoticeMediaR initNoticeMediaRelation(Long id){
//
//        NoticeMediaR noticeMediaR = new NoticeMediaR()
//                .setMediaId(id)
//                .setAnnexId(null)
////                .setState(MediaTypeEnum.NOTICE_MEDIA.getValue());
//                .setState(3);
//        noticeMediaR.setIsDelete(0);
//        noticeMediaR.setVersion(0);
//
//        return noticeMediaR;
//    }

    /**
     * 包段操作，包括新增招标公告包段关联，更新包段状态
     *
     * @param sectionIds 包段ID的列表
     * @param notice 招标公告
     * @param dto 公告变更请求DTO
    */
//    private void initNoticePackageRelations(List<Long> sectionIds,TProjectBidNotice notice,UpdateNoticeDTO dto){
//        List<Long> packageIds = packageRService.findAllAvailablePackageIds(notice.getProjectId());
//        if(notice.getId() == null){
//            if(!packageIds.containsAll(sectionIds)){
//                throw new SunFlowerException(ExceptionEnum.NOTICE_PACKAGE_ILLEGAL,null);
//            }
//
//            dto
//                    .setDeletePackageIds(null)
//                    .setUpdatePackageIds(sectionIds);
//        }else{
//            List<Long> oldIds = packageRService.findNoticePackageIds(notice.getId());
//            packageIds.addAll(oldIds);
//
//            if(!packageIds.containsAll(sectionIds)){
//                throw new SunFlowerException(ExceptionEnum.NOTICE_PACKAGE_ILLEGAL,null);
//            }
//
//            Collection<Long> commonIds = CollUtil.intersection(sectionIds, oldIds);
//            oldIds.removeAll(commonIds);
//            sectionIds.removeAll(commonIds);
//            dto
//                    .setDeletePackageIds(oldIds)
//                    .setUpdatePackageIds(sectionIds);
//        }
//
//        List<NoticePackageR> rs = new ArrayList<>();
//
//        sectionIds.forEach((id) -> {
//            NoticePackageR packageR = new NoticePackageR()
//                    .setNoticeId(notice.getId())
//                    .setSectionId(id)
//                    .setState(NoticeTypeEnum.PURCHASE_NOTICE.getValue());
//            packageR.setIsDelete(0);
//            packageR.setVersion(0);
//
//            rs.add(packageR);
//        });
//
//        dto.setInsertPackages(rs);
//    }

    /**
     * 生成RelevancyAnnex的list
     */
//    private List<RelevancyAnnex> initRelevancyAnnex(List<Long> ids,ProjectBidNotice notice){
//        List<RelevancyAnnex> annexes = new ArrayList<>();
//
//        ids.forEach((id) -> {
//            RelevancyAnnex annex = new RelevancyAnnex()
//                    .setProjectId(notice.getProjectId())
//                    .setFileId(id)
//                    .setFileType(AnnexTypeEnum.BIDNOTICE.getValue())
//                    .setBusId(notice.getId());
//            annex.setIsDelete(0);
//            annex.setVersion(0);
//
//            annexes.add(annex);
//        });
//
//        return annexes;
//    }

    /**
     * 根据ID判断招标公告是否存在
     *
     * @param  id 招标公告ID
     * @return TProjectBidNotice 招标公告实体
    */
//    private ProjectBidNotice noticeIsExists(Long id){
//
//        ProjectBidNotice notice = noticeService.getById(id);
//        // 如果招标公告不存在，抛出异常
//        if(notice == null){
//            throw new SunFlowerException(ExceptionEnum.NOTICE_NOT_FOUND, null);
//        }
//
//        return notice;
//    }

    /**
     * 根据ID判断项目是否存在
     *
     * @param  id 招标公告ID
     * @return Project 项目实体
     */
//    private Project projectIsExists(Long id){
//
//        Project project = projectService.getProjectById(id);
//        // 如果招标公告不存在，抛出异常
//        if(project == null){
//            throw new SunFlowerException(ExceptionEnum.PROJECT_NOT_FOUND, null);
//        }
//
//        return project;
//    }

    /**
     * 判断招标公告名长度是否符合要求
     *
     * @param  name 招标公告名称
     * @return true代表校验参数通过
     */
    private Boolean noticeNameIsIllegal(String name){
        System.out.println(name.length());
        if(StrUtil.isNotEmpty(name) && name.length() > 100){

            throw new SunFlowerException(ExceptionEnum.NOTICE_NAME_TOO_LONG,ExceptionEnum.NOTICE_NAME_TOO_LONG.getMessage());
        }
        return true;
    }

    /**
     * 判断招标公告内容长度是否符合要求
     *
     * @param  content 招标公告内容
     * @return true代表校验参数通过
     */
    private Boolean noticeContentIsIllegal(String content){
        if(StrUtil.isNotEmpty(content) && content.length() > 120000){
            throw new SunFlowerException(ExceptionEnum.NOTICE_CONTENT_TOO_LONG,ExceptionEnum.NOTICE_CONTENT_TOO_LONG.getMessage());
        }
        return true;
    }

    /**
     * 判断招标公告拟发布时间是否符合要求
     *
     * @param releaseTime 拟发布时间
     * @return true代表校验参数通过
     */
//    private Boolean noticeReleaseTimeIsIllegal(Date releaseTime){
//        if(releaseTime != null){
//            if(!RequestUtil.isAfterDays(releaseTime, new Date(),1)){
//                throw new SunFlowerException(ExceptionEnum.NOTICE_RELEASE_TIME_ILLEGAL,ExceptionEnum.NOTICE_RELEASE_TIME_ILLEGAL.getMessage());
//            }
//            return true;
//        }
//        return false;
//    }

    /**
     * 判断招标内容长度是否符合要求
     *
     * @param releaseTime 拟发布时间
     * @param endTime 公示结束时间
     * @return true代表校验参数通过
     */
    private Boolean noticeEndTimeIsIllegal(Date endTime,Date releaseTime){
        if(endTime != null){
            if(releaseTime != null){
                if(!(endTime.getTime() >= releaseTime.getTime())){
                    throw new SunFlowerException(ExceptionEnum.NOTICE_END_TIME_ILLEGAL,ExceptionEnum.NOTICE_END_TIME_ILLEGAL.getMessage());
                }
                return true;
            }else{
                if(!(endTime.getTime() >= new Date().getTime())){
                    throw new SunFlowerException(ExceptionEnum.NOTICE_END_TIME_ILLEGAL,ExceptionEnum.NOTICE_END_TIME_ILLEGAL.getMessage());
                }
                return true;
            }
        }
        return false;
    }
}
