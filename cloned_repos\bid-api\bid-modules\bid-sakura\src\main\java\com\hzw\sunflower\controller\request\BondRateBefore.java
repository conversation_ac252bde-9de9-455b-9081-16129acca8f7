package com.hzw.sunflower.controller.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date ：Created in 2023/7/29 11:10
 * @description：
 * @modified By：`
 * @version: 1.0
 */
@ApiModel(description = "今日利率")
@Data
public class BondRateBefore implements Serializable {

    @ApiModelProperty("调整之前的利率")
    private BigDecimal beforeRate;

    @ApiModelProperty("调整之前的生效日期")
    private String beforeDate;

    @ApiModelProperty("银行类型")
    private Integer bankType;

}
