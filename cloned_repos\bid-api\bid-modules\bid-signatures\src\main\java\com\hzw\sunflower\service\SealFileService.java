package com.hzw.sunflower.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.controller.request.SealFileSignReq;
import com.hzw.sunflower.dto.SealFileDTO;
import com.hzw.sunflower.entity.JwtUser;
import com.hzw.sunflower.entity.SealFile;
import com.hzw.sunflower.entity.condition.SealFileCondition;

import java.util.List;


/**
* SealFileService接口
*
*/
public interface SealFileService extends IService<SealFile> {


    /**
    * 修改单位公司 信息
    *
    * @param sealFile
    * @return 是否成功
    */
    Boolean updateInfo(SealFileDTO sealFile);


    /**
     * 签章完成接口
     * @param req
     * @param user
     * @return
     */
    Result<SealFile> completeSign(SealFileSignReq req, JwtUser user);


}