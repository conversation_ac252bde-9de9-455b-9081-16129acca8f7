package com.hzw.sunflower.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzw.sunflower.constant.SealEnum;
import com.hzw.sunflower.constant.constantenum.SealApplicationEnum;
import com.hzw.sunflower.constant.constantenum.SealApplicationInfoEnum;
import com.hzw.sunflower.dao.SealApplicationInfoMapper;
import com.hzw.sunflower.dto.ProcessRecordDTO;
import com.hzw.sunflower.dto.SealApplicationInfoDTO;
import com.hzw.sunflower.entity.SealApplication;
import com.hzw.sunflower.entity.SealApplicationInfo;
import com.hzw.sunflower.entity.condition.SealApplicationInfoCondition;
import com.hzw.sunflower.security.utils.SecurityUtils;
import com.hzw.sunflower.service.ProcessRecordService;
import com.hzw.sunflower.service.SealApplicationInfoService;
import com.hzw.sunflower.service.SealApplicationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;


/**
* SealApplicationInfoService接口实现
*
* <AUTHOR>
* @version 1.0.0 2021-04-07
*/
@Service
public class SealApplicationInfoServiceImpl extends ServiceImpl<SealApplicationInfoMapper, SealApplicationInfo> implements SealApplicationInfoService {


    @Autowired
    private ProcessRecordService processRecordService;

    @Autowired
    private SealApplicationService sealApplicationService;

    /**
    * 根据条件分页查询列表
    *
    * @param condition 查询条件
    * @return 分页数据
    */
    @Override
    public IPage<SealApplicationInfo> findInfoByCondition(SealApplicationInfoCondition condition) {
        IPage<SealApplicationInfo> page = condition.buildPage();
        QueryWrapper<SealApplicationInfo> queryWrapper = condition.buildQueryWrapper(SealApplicationInfo.class);
        return this.page(page, queryWrapper);
    }

    /**
    * 根据主键ID查询
    *
    * @param id 主键ID
    * @return
    */
    @Override
    public SealApplicationInfo getInfoById(Long id) {
        return this.getById(id);
    }

    /**
    * 新增
    *
    * @param sealApplicationInfo
    * @return 是否成功
    */
    @Override
    public Boolean addInfo(SealApplicationInfoDTO sealApplicationInfo) {
        return this.save(sealApplicationInfo);
    }

    /**
    * 修改成为处理
    *
    * @param sealApplicationInfo
    * @return 是否成功
    */
    @Override
    public Boolean updateInfo(SealApplicationInfoDTO sealApplicationInfo) {
        //SealApplicationInfo sealApplicationInfo1 = this.baseMapper.selectById(sealApplicationInfo.getId());
        SealApplicationInfo sai  = new SealApplicationInfo();
        sai.setId(sealApplicationInfo.getId());
        sai.setHandlingSituation(sealApplicationInfo.getHandlingSituation());
        // 改为已处理
        sai.setProcessingProgress(SealEnum.PROCESSING_PROGRESS_YES.getType());
        sai.setSealedTime(new Date());
        sai.setSealedUser(SecurityUtils.getJwtUser().getUserName());
        sai.setSealedUserId(SecurityUtils.getJwtUser().getUserId());
        SealApplicationInfo byId = getById(sealApplicationInfo.getId());
        SealApplication byId1 = sealApplicationService.getById(byId.getApplyId());
        if(BeanUtil.isNotEmpty(byId1)
                && SealApplicationEnum.TO_SHOW.getType().equals(byId1.getApproveStatus())
                && SealApplicationInfoEnum.BID_WIN_NOTICE.getType().equals(byId1.getApplyItem())
        ){
            byId1.setApproveStatus(SealApplicationEnum.AGREE.getType());
            byId1.setApplyTime(new Date());
            sealApplicationService.updateById(byId1);
            LambdaUpdateWrapper<SealApplicationInfo> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(SealApplicationInfo::getApplyId,byId1.getId())
                    .ne(SealApplicationInfo::getId,sai.getId())
                    .set(SealApplicationInfo::getProcessingProgress,SealEnum.PROCESSING_PROGRESS_YES.getType());
            updateWrapper.set(SealApplicationInfo::getSealedUser,SecurityUtils.getJwtUser().getUserName());
            updateWrapper.set(SealApplicationInfo::getSealedUserId,SecurityUtils.getJwtUser().getUserId());
            updateWrapper.set(SealApplicationInfo::getSealedTime,sai.getSealedTime());
            this.update(updateWrapper);
        }

        //插入流程记录
        ProcessRecordDTO recordDTO = new ProcessRecordDTO();
        recordDTO.setBusinessCode("REPORTING_MATTER_INFO");
        recordDTO.setBusinessId(sealApplicationInfo.getId());
        recordDTO.setOperation("盖章");
        recordDTO.setRemark(sealApplicationInfo.getRemark());
        recordDTO.setOperatorId(SecurityUtils.getJwtUser().getUserId());
        recordDTO.setOperatorName(SecurityUtils.getJwtUser().getUserName());
        processRecordService.addProcessRecord(recordDTO,SecurityUtils.getJwtUser().getUserOtherId(),"");

        return this.updateById(sai);
    }

    /**
    * 根据主键ID删除
    *
    * @param id 主键ID
    * @return 是否成功
    */
    @Override
    public Boolean deleteById(Long id) {
        return this.removeById(id);
    }

    /**
    * 根据主键ID列表批量删除
    *
    * @param idList 主键ID列表
    * @return 是否成功
    */
    @Override
    public Boolean deleteByIds(List<Long> idList) {
        return this.removeByIds(idList);
    }

}