package com.hzw.sunflower.constant.constantenum;

/**
 * <AUTHOR>
 * @date ：Created in 2021/6/29 10:02
 * @description： 承诺文件类型
 * @version: 1.0
 */
public enum PromiseFileTypeEnum {

    // 文件类型 1.评委宣誓词 2.协评人员宣誓词 3.健康情况说明 4.其他
    JUDGES_OATH(1, "评委宣誓词"),
    ASSIST_JUDGES_OATH(2, "协评人员宣誓词"),
    HEALTH_STATEMENT(3, "健康情况说明"),
    ELSE(4, "其他");

    private Integer type;

    private String name;

    PromiseFileTypeEnum(Integer type, String name) {
        this.type = type;
        this.name = name;
    }

    public Integer getType() {
        return type;
    }

    public String getName() {
        return name;
    }

}
