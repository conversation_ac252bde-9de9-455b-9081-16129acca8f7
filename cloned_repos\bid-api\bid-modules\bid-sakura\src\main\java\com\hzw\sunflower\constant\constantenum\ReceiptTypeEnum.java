package com.hzw.sunflower.constant.constantenum;

/**
 * <AUTHOR>
 * @date ：Created in 2022/07/27 9:23
 * @description：申请类型
 * @version: 1.0
 */
public enum ReceiptTypeEnum {
    //回单类型 1.收款流水回单（贷） 2.支付回单（借）
    COLLECTION_WATER(1, "收款流水回单"),
    PAY_WATER(2, "支付回单");

    private Integer type;
    private String desc;

    ReceiptTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
