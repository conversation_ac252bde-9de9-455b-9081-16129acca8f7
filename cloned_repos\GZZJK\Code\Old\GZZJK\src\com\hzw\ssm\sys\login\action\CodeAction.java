package com.hzw.ssm.sys.login.action;

import java.io.ByteArrayInputStream;

import org.apache.struts2.convention.annotation.Action;
import org.apache.struts2.convention.annotation.Namespace;
import org.apache.struts2.convention.annotation.Result;
import org.apache.struts2.convention.annotation.Results;

import com.hzw.ssm.fw.base.BaseAction;
import com.hzw.ssm.fw.util.SecurityCode;
import com.hzw.ssm.fw.util.SecurityImage;

@Namespace("/code")
@Results( { @Result(name = "success", type = "stream", params = { "contentType", "image/jpeg", "inputName",
		"imageStream", "bufferSize", "4096" }) })
public class CodeAction extends BaseAction {
	
	private static final long serialVersionUID = -8823858495799309882L;
	
	//图片流  
	private ByteArrayInputStream imageStream;
	

	public ByteArrayInputStream getImageStream() {
		return imageStream;
	}

	public void setImageStream(ByteArrayInputStream imageStream) {
		this.imageStream = imageStream;
	}
	

	@Action("imagecode")
	public String execute() throws Exception {
		//如果开启Hard模式，可以不区分大小写  
		//String securityCode = SecurityCode.getSecurityCode(4,SecurityCodeLevel.Hard, false).toLowerCase();  

		//获取默认难度和长度的验证码  
		String securityCode = SecurityCode.getSecurityCode();
		imageStream = SecurityImage.getImageAsInputStream(securityCode);
		//放入session中  
		this.context();
		getRequest().getSession().setAttribute("securityCode", securityCode);
		return SUCCESS;
	}

}
