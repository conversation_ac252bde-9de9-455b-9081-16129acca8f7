package com.hzw.externalApi.api.domain;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 单位公司 (t_company)
 *
 * <AUTHOR>
 * @version 1.0.0 2021-04-12
 */
//@TableName("t_company")
@Data
public class TycCompany implements Serializable {
    /**
     * 版本号
     */
    private static final long serialVersionUID = 6536848133524185899L;


    private Long id;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 注册资金
     */
    private BigDecimal registerMoney;

    /**
     * 单位类型 1招标人2代理机构3供应商
     */
    private Integer companyType;

    /**
     * 单位状态  0 正常 1 注销
     */
    private Integer companyStatus;

    /**
     * 三证合一 是否三证合一（0：否  1：是 ）
     */
    private Integer threeInOne;

    /**
     * 营业执照
     */
    private String businessLicense;

    /**
     * 组织机构代码
     */
    private String organizationNum;


    /**
     * 法定代表人
     */
    private String legalRepresentative;


    /**
     * 法定代表人身份证
     */
    private String legalRepresentativeIdentity;

    /**
     * 企业简称
     */
    private String abbreviation;

    /**
     * 是否被企查查验证过 0:否  1:是
     */
    private Integer isValidate;


    /**
     * 供应商是否被禁用 0：正常  1：禁用
     */
    private Integer supplierDisable;

    /**
     * 招标人是否被禁用 0：正常  1：禁用
     */
    private Integer bidderDisable;

    /**
     * 代理机构是否被禁用 0：正常  1：禁用
     */
    private Integer agencyDisable;
}