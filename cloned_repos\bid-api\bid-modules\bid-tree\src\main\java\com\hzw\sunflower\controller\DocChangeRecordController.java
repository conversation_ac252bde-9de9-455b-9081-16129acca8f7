package com.hzw.sunflower.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hzw.sunflower.common.BaseController;
import com.hzw.sunflower.util.BeanListUtil;
import com.hzw.sunflower.common.Paging;
import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.constant.MessageConstants;
import com.hzw.sunflower.controller.request.DocChangeRecordREQ;
import com.hzw.sunflower.controller.response.DocChangeRecordVO;
import com.hzw.sunflower.dto.DocChangeRecordDTO;
import com.hzw.sunflower.entity.DocChangeRecord;
import com.hzw.sunflower.entity.condition.DocChangeRecordCondition;
import com.hzw.sunflower.service.DocChangeRecordService;
import com.hzw.sunflower.service.OssFileService;
import com.hzw.sunflower.utils.CompareObjectUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;


/**
 * TDocChangeRecordController
 */
@Api(tags = "招标文件变更记录服务")
@RestController
@RequestMapping("/docChangeRecord")
public class DocChangeRecordController extends BaseController {
    @Autowired
    private DocChangeRecordService docChangeRecordService;

    @Autowired
    private OssFileService ossFileService;

    @ApiOperation(value = "根据条件分页查询TDocChangeRecord列表")
    @ApiImplicitParam(name = "tDocChangeRecordREQ", value = "用户表 查询条件", required = true, dataType = "TDocChangeRecordREQ", paramType = "body")
    @PostMapping("/list")
    public Paging<DocChangeRecord> list(@RequestBody DocChangeRecordREQ docChangeRecordREQ) {
        DocChangeRecordCondition condition = BeanListUtil.convert(docChangeRecordREQ, DocChangeRecordCondition.class);
        IPage<DocChangeRecord> page = docChangeRecordService.findInfoByCondition(condition);
        return Paging.buildPaging(page);
    }

    @ApiOperation(value = "根据主键ID查询TDocChangeRecord信息")
    @ApiImplicitParam(name = "id", value = "主键ID", required = true)
    @GetMapping(value = "/get/{id}")
    public Result<DocChangeRecord> get(@PathVariable Long id) {
        if (id == null) {
            return Result.failed(MessageConstants.GET_NULL);
        }
        DocChangeRecord docChangeRecord = docChangeRecordService.getInfoById(id);
        return Result.ok(docChangeRecord);
    }

    @ApiOperation(value = "根据招标文件ID查询变更记录所有次数")
    @ApiImplicitParam(name = "docId", value = "招标文件ID", required = true)
    @GetMapping(value = "/getTimesByDocId/{docId}")
    public Result<List<Long>> getTimesByDocId(@PathVariable Long docId) {
        if (docId == null) {
            return Result.failed(MessageConstants.GET_NULL);
        }
        List<Long> times = docChangeRecordService.getTimesByDocId(docId);
        return Result.ok(times);
    }

    @ApiOperation(value = "根据招标文件ID查询变更记录集合")
    @ApiImplicitParam(name = "docId", value = "招标文件ID", required = true)
    @GetMapping(value = "/getListByDocId/{docId}")
    public Result<List<DocChangeRecordVO>> getListByDocId(@PathVariable Long docId) {
        if (docId == null) {
            return Result.failed(MessageConstants.GET_NULL);
        }
        List<DocChangeRecordDTO> recordList = docChangeRecordService.getListByDocId(docId);
        List<DocChangeRecordVO> changeRecordVO = BeanListUtil.convertList(recordList, DocChangeRecordVO.class);
        return Result.ok(changeRecordVO);
    }

    @ApiOperation(value = "根据标段ID查询变更记录集合")
    @ApiImplicitParam(name = "bidId", value = "标段ID", required = true)
    @GetMapping(value = "/getListByBidId/{bidId}")
    public Result<List<DocChangeRecordVO>> getListByBidId(@PathVariable Long bidId) {
        if (bidId == null) {
            return Result.failed(MessageConstants.GET_NULL);
        }
        List<DocChangeRecordDTO> recordList = docChangeRecordService.getListByBidId(bidId);
        List<DocChangeRecordVO> changeRecordVO = BeanListUtil.convertList(recordList, DocChangeRecordVO.class);
        return Result.ok(changeRecordVO);
    }


    @ApiOperation(value = "查询文件变更记录详情")
    @ApiImplicitParam(name = "docChangeRecordREQ", value = "变更记录请求体", required = true)
    @PostMapping(value = "/getDocRecord")
    public Result<List<DocChangeRecordVO>> getDocRecord(@RequestBody DocChangeRecordREQ docChangeRecordREQ) {
        if (null == docChangeRecordREQ || null == docChangeRecordREQ.getDocId() || null == docChangeRecordREQ.getSubmitNumber()) {
            return Result.failed(MessageConstants.GET_NULL);
        }
        List<DocChangeRecordDTO> newRecord = docChangeRecordService.getDocRecord(docChangeRecordREQ);
        if (CollectionUtil.isEmpty(newRecord)) {
            return Result.failed(MessageConstants.GET_NULL);
        }
        List<DocChangeRecordVO> changeRecordVO = BeanListUtil.convertList(newRecord, DocChangeRecordVO.class);
        //两次及以上提交记录，比较变化
        if (docChangeRecordREQ.getSubmitNumber() >= 2) {
            docChangeRecordREQ.setSubmitNumber(docChangeRecordREQ.getSubmitNumber() - 1);
            List<DocChangeRecordDTO> oldRecord = docChangeRecordService.getDocRecord(docChangeRecordREQ);
            for (int i = 0; i < oldRecord.size(); i++) {
                for (int j = 0; j < newRecord.size(); j++) {
                    if (oldRecord.get(i).getSectionId().equals(newRecord.get(j).getSectionId())) {
                        Map<Object, Boolean> map = CompareObjectUtil.compareObject(oldRecord.get(i), newRecord.get(j));
                        changeRecordVO.get(j).setFlag(map);
                    }
                }
            }
        }
        changeRecordVO.stream().forEach(e -> e.setAnnexList(ossFileService.getOssFileByFileIds(e.getAnnexOssFileIds())));
        changeRecordVO.stream().forEach(e -> e.setScheduleList(ossFileService.getOssFileByFileIds(e.getScheduleOssFileIds())));
        return Result.ok(changeRecordVO);
    }

    @ApiOperation(value = "查询标段变更记录详情")
    @ApiImplicitParam(name = "docChangeRecordREQ", value = "变更记录请求体", required = true)
    @PostMapping(value = "/getBidRecord")
    public Result<List<DocChangeRecordVO>> getBidRecord(@RequestBody DocChangeRecordREQ docChangeRecordREQ) {
        if (null == docChangeRecordREQ || null == docChangeRecordREQ.getBidId() || null == docChangeRecordREQ.getBidSubmitNumber()) {
            return Result.failed(MessageConstants.GET_NULL);
        }
        List<DocChangeRecordDTO> newRecord = docChangeRecordService.getBidRecord(docChangeRecordREQ);
        if (CollectionUtil.isEmpty(newRecord)) {
            return Result.failed(MessageConstants.EMPTY_DATA);
        }
        List<DocChangeRecordVO> changeRecordVO = BeanListUtil.convertList(newRecord, DocChangeRecordVO.class);
        //两次及以上提交记录，比较变化
        if (docChangeRecordREQ.getBidSubmitNumber() >= 2) {
            docChangeRecordREQ.setBidSubmitNumber(docChangeRecordREQ.getBidSubmitNumber() - 1);
            List<DocChangeRecordDTO> oldRecord = docChangeRecordService.getBidRecord(docChangeRecordREQ);
            for (int i = 0; i < oldRecord.size(); i++) {
                for (int j = 0; j < newRecord.size(); j++) {
                    if (oldRecord.get(i).getSectionId().equals(newRecord.get(j).getSectionId())) {
                        Map<Object, Boolean> map = CompareObjectUtil.compareObject(oldRecord.get(i), newRecord.get(j));
                        changeRecordVO.get(j).setFlag(map);
                    }
                }
            }
        }
        changeRecordVO.stream().forEach(e -> e.setAnnexList(ossFileService.getOssFileByFileIds(e.getAnnexOssFileIds())));
        return Result.ok(changeRecordVO);
    }
}
