<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper 
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hzw.ssm.sys.login.dao.LoginMapper">
	<!-- 验证用户名和密码是否匹配 -->
	<select id="login" parameterType="Map" resultType="UserEntity">
		SELECT tsu.user_id,
		       tsu.role_id,
		       tsu.user_name,
		       tsu.login_code,
		       tsu.password,
		       tsu.sex,
		       tsu.mobile,
		       tsu.email,
		       tsu.tel,
		       tsu.enabled_flag,
		       tsu.login_fail_time,
		       tsu.login_fail_count,
		       b.role_name as role_name,
		       tsu.department
		  FROM ts_user tsu
		  left join ts_role b 
		  on tsu.role_id = b.role_id 
		Where tsu.delete_flag=0 and login_code = #{login_code}
		<if test="null != password and '' != password">
		  and password = '${password}'
		</if>
	</select>
	
	<!-- 登录错误设置 -->
	<update id="loginSet" parameterType="UserEntity">
		UPDATE TS_USER A
		<set>
		    <if test="login_fail_time != null">
		        A.LOGIN_FAIL_TIME=#{login_fail_time},
		    </if>
		    <if test="login_fail_count != null and login_fail_count > 0">
		        A.LOGIN_FAIL_COUNT=#{login_fail_count},
		    </if>
		</set>
 		WHERE A.USER_ID = #{user_id}
	</update>
	
	<!-- 登录成功设置 -->
	<update id="loginPass" parameterType="String">
		UPDATE TS_USER A
   			SET A.LOGIN_FAIL_TIME = NULL,
       		A.LOGIN_FAIL_COUNT = 0,
       		A.SMS_CODE = NULL
 		WHERE A.USER_ID = #{user_id}
	</update>
	
	<!-- 查询 角色的权限信息-->
	<select id="getMenuListOfAuth" parameterType="String"  resultType="MenuEntity">
		select m.menu_id,m.menu_name,m.menu_url,m.menu_image_url,m.parent_menu_id,m.sortNo 
			from ts_auth a inner join ts_menu m on a.menu_id=m.menu_id
		where a.role_id=#{roleId} and m.delete_flag='0' 
		order by m.sortno desc
	</select>
	<!-- 查询注册专家-->
	<select id="queryRegisterExpert" parameterType="String"  resultType="ExpertInfoEntity">
		select  id,
			user_id,
			user_name,
			sex,
			birthday,
			politics,
			id_type,
			id_no,
			id_fileid,
			school,
			major,
			certificate_fileid,
			educations,
			degree,
			grade,
			province,
			city,
			zone,
			district,
			company,
			company_addr,
			company_phone,
			company_zipcode,
			mobilephone,
			email,
			special_skill,
			bid_experience,
			training_experience,
			remark,
			status,
			create_time,
			delete_flag,
			expert_num,
			photo_fileid,
			technical_tital,
			technical_filed,
			position,
			qq_num
		from t_expert_info
		where status=#{status} and delete_flag=0
	</select>
	
	<!-- 查询项目数量 -->
	<select id="queryProjectCount" parameterType="ProjectEntity" resultType="java.lang.Long">
		SELECT COUNT(PROJECT_ID) COUNT FROM T_PROJECT WHERE STATUS = #{status} and delete_flag = 0 and (SYSDATE-3 &lt; bid_time or SYSDATE &lt; bid_time) 
		<if test="null != department">
			AND CREATE_USER IN (SELECT USER_ID FROM TS_USER WHERE DEPARTMENT = #{department} AND CREATE_USER != #{create_id})
		</if>
	</select>
	
	
	<!-- 查询用户id（用于转换机电过来的请求用户ID） -->
	<select id="queryUserId" parameterType="String" resultType="java.lang.String">
		SELECT A.USER_ID FROM TS_USER  A WHERE A.USER_NAME =#{userName}
	</select>
</mapper>
