<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper 
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hzw.ssm.date.dao.ZJKCalendarMapper">


	<!-- 根据项目id查询项目信息 -->
	<select id="queryAutoProjectList" parameterType="com.hzw.ssm.date.entity.AutoCheckEntity" resultType="com.hzw.ssm.sys.project.entity.ProjectEntity">
		select 
			p.project_id as projectId,p.decimationBatch as decimationBatch
		from t_project p
		<where>
				p.delete_flag = 0 
			AND (SYSDATE-3<![CDATA[ < ]]> p.bid_time or SYSDATE<![CDATA[ < ]]> p.bid_time) 
			AND p.status=90 
			AND p.modifytime!=null 
			AND TO_NUMBER(SYSDATE - p.modifytime)* 24 * 60 * 60 * 1000  >=  #{check_time};
		</where>
	</select>
	
	<select id="quertAutoCheck"  resultType="com.hzw.ssm.date.entity.AutoCheckEntity">
		SELECT
			check_time
		FROM 
			SYS_AUTO_CHECK
		<where>
			AND delete_flag=0
		</where>	
	</select>
	
	<update id="updateAutoCheck" parameterType="com.hzw.ssm.sys.project.entity.ProjectEntity">
		UPDATE 
			t_project 
		SET 
			status=#{status},
			check_type=#{check_type}, 
			modifytime=SYSDATE
		WHERE 
			decimationBatch=#{decimationBatch} and status=90
	</update>

	<select id="getCalendarByYear" parameterType="int" resultType="com.hzw.ssm.date.entity.CalendarEntity">
		SELECT
			*
		FROM 
			sys_calendar
		<where>
			<if test=" _parameter != null ">
				AND date_year=#{_parameter}
			</if>
			AND delete_flag=0
		</where>
		order by date_time		
	</select>
	
	<select id="getCalendarByYearMonth" parameterType="com.hzw.ssm.date.entity.CalendarEntity" resultType="com.hzw.ssm.date.entity.CalendarEntity">
		SELECT
			*
		FROM 
			sys_calendar
		<where>
			<if test=" date_year != null ">
				AND date_year=#{date_year}
			</if>
			<if test=" date_month != null ">
				AND date_month=#{date_month}
			</if>
			AND delete_flag=0
		</where>
		order by date_time		
	</select>
	
	<select id="getAllYears"  resultType="int">
		SELECT DISTINCT(a.date_year) FROM SYS_CALENDAR a where a.delete_flag=0		
	</select>
	
	
	<select id="queryCalendarExist" parameterType="int" resultType="int">
		SELECT
			count(1)
		FROM 
			sys_calendar
		<where>
			<if test=" _parameter != null ">
				AND date_year=#{_parameter}
			</if>
			AND delete_flag=0
		</where>		
	</select>
	
	
	<select id="getCalendarStatus" parameterType="java.lang.String" resultType="int">
		SELECT
			date_status
		FROM 
			sys_calendar
		<where>
			<if test=" _parameter != null ">
				AND date_time=#{_parameter}
			</if>
			AND delete_flag=0
		</where>		
	</select>
	
	<select id="getCalendarStatus2" parameterType="java.lang.String" resultType="java.lang.String">
		SELECT 
			date_time 
		FROM 
			sys_calendar b 
		WHERE 
			b.CALENDAR_ID=(
				SELECT 
					max(a.CALENDAR_ID) 
				FROM 
					sys_calendar a 
				<where>
					<if test=" _parameter != null ">
						AND a.DATE_TIME <![CDATA[ < ]]> #{bidDate2}
					</if>
				</where> 
					 AND (a.DATE_STATUS=10 OR a.DATE_STATUS=11) AND a.delete_flag=0)		
	</select>
	
	
	
	<insert id="insertCalendar" parameterType="com.hzw.ssm.date.entity.CalendarEntity">
		<selectKey keyProperty="calendar_id" resultType="int" order="BEFORE">
	    SELECT SEQ_DATEID.nextval FROM dual
	    </selectKey>
	    
		INSERT INTO sys_calendar(
			calendar_id,date_time,date_year,date_status,remark,
			create_user,create_date,modify_user,modify_date,delete_flag,date_month)
		VALUES(
			#{calendar_id},#{date_time},#{date_year},#{date_status},#{remark},
			#{create_user},#{create_date},
			#{modify_user},#{modify_date},#{delete_flag},#{date_month}
		)	
			
		
	</insert>
	
	<update id="deleteCalendarForYear" parameterType="int">
		UPDATE 
			sys_calendar 
		SET 
			delete_flag=1
		<where>
			AND date_year=#{date_year}
		</where>
	</update>	
	
	<update id="updateStatus" parameterType="com.hzw.ssm.date.entity.CalendarEntity">
		UPDATE 
			sys_calendar 
		SET 
			date_status=#{date_status},
			remark=#{remark}
		<where>
			AND date_time=#{date_time}
			AND delete_flag=0
		</where>
	</update>

</mapper>
