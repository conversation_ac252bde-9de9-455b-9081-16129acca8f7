package com.hzw.sunflower.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hzw.sunflower.controller.response.EmailFilesVo;
import com.hzw.sunflower.entity.EmailInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 *
 * @ClassName:EmailInfoMapper
 * @Auther: lijinxin
 * @Description: 邮件信息
 * @Date: 2023/3/2 17:11
 * @Version: v1.0
 */
public interface EmailInfoMapper  extends BaseMapper<EmailInfo> {

    /**
     * 获取邮件附件信息
     * @param type
     * @param id
     * @return
     */
    List<EmailFilesVo> getEmailFiles(@Param("type")Integer type, @Param("id")Long id);

    /**
     * 获取邮件正文附件信息
     * @param type
     * @param id
     * @return
     */
    List<EmailFilesVo> getEmailContentFiles(@Param("type")Integer type, @Param("id")Long id);
}
