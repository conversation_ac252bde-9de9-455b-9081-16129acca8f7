package com.hzw.sunflower.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;

import com.hzw.sunflower.common.BaseBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 保证金流水模板
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-28
 */
@Data
@TableName("t_bond_template")
@ApiModel(description = "保证金流水模板")
public class BondTemplate extends BaseBean implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("银行名称")
    @TableField("bank_name")
    private String bankName;

    @ApiModelProperty("模板名称")
    @TableField("template_name")
    private String templateName;

    @ApiModelProperty("表头行数（从第几行开始读取）")
    @TableField("head_row")
    private Integer headRow;

    @ApiModelProperty("交易流水表头")
    @TableField("water_number")
    private String waterNumber;

    @ApiModelProperty("交易日期表头")
    @TableField("date")
    private String date;

    @ApiModelProperty("交易时间表头")
    @TableField("time")
    private String time;

    @ApiModelProperty("付款户名表头")
    @TableField("company_name")
    private String companyName;

    @ApiModelProperty("付款账户表头")
    @TableField("company_account")
    private String companyAccount;

    @ApiModelProperty("开户行表头")
    @TableField("company_bank_deposit")
    private String companyBankDeposit;

    @ApiModelProperty("付款行号表头")
    @TableField("company_bank_code")
    private String companyBankCode;

    @ApiModelProperty("交易金额表头")
    @TableField("amount")
    private String amount;

    @ApiModelProperty("收款账户表头")
    @TableField("receive_acount")
    private String receiveAcount;

    @ApiModelProperty("收款银行表头")
    @TableField("receive_bank")
    private String receiveBank;

    @ApiModelProperty("附言表头")
    @TableField("post_script")
    private String postScript;

}
