package com.hzw.ssm.sys.project.service;

import com.hzw.ssm.expert.dao.ExpertInfoMapper;
import com.hzw.ssm.expert.entity.*;
import com.hzw.ssm.fw.base.BaseService;
import com.hzw.ssm.fw.util.*;
import com.hzw.ssm.sys.call.entity.VoiceEntity;
import com.hzw.ssm.sys.call.service.VoiceService;
import com.hzw.ssm.sys.call.util.CallClient;
import com.hzw.ssm.sys.project.dao.AppointsExpertsMapper;
import com.hzw.ssm.sys.project.dao.DebarbMapper;
import com.hzw.ssm.sys.project.dao.ProjectMapper;
import com.hzw.ssm.sys.project.entity.*;
import com.hzw.ssm.sys.sms.ReceiveMsgTimerRunnable;
import com.hzw.ssm.sys.sms.SMSNewUtil;
import com.hzw.ssm.sys.sms.SVSNewUtil;
import com.hzw.ssm.sys.sms.mwutil.MWSmsUtil;
import com.hzw.ssm.sys.system.entity.SmsRecordEntity;
import com.hzw.ssm.sys.system.service.SmsRecordService;
import com.hzw.ssm.sys.template.service.TemplateService;
import com.hzw.ssm.sys.user.dao.UserMapper;
import com.hzw.ssm.sys.user.entity.UserEntity;
import com.hzw.ssm.sys.user.service.UserService;
import com.hzw.ssm.util.empty.EmptyUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.util.*;

@Service
public class ProjectService extends BaseService {
	/** 日志对象 */
	protected static final Log log = LogFactory.getLog(ProjectService.class);
	//梦网短信账号
	@Value("${sms_userId}")
	private String userId;
	//梦网短信密码
	@Value("${sms_userPwd}")
	private String userPwd;
	//梦网主IP
	@Value("${sms_masterIpAddress}")
	private String masterIpAddress;

	//梦网备用IP1
	@Value("${sms_ipAddress1}")
	private String ipAddress1;
	//梦网备用IP2
	@Value("${sms_ipAddress2}")
	private String ipAddress2;

	@Autowired
	private ProjectMapper mapper;

	@Autowired
	private UserMapper userMapper;

	@Autowired
	private SmsRecordService smsRecordService;

	@Autowired
	private TemplateService templateService;

	@Autowired
	private UserService userService;

	@Autowired
	private ExpertInfoMapper expertInfoMapper;

	@Autowired
	private AppointsExpertsMapper appointsExpertsMapper;
	@Autowired
	private VoiceService voiceService;
	@Autowired
	private DebarbMapper debarbMapper;
	@Autowired
	private AppointsExpertsService appointsExpertsService;

	@Autowired
	private ExpertLimitService expertLimitService;


	/**
	 * 保存项目信息
	 *
	 * @param project
	 */
	@Transactional
	public void saveProject(ProjectEntity project, ConditionEntity conEntity) {
		try {
			project.setProjectId(CommUtil.getKey());
			project.setDecimationBatch(CommUtil.getKey());
			mapper.saveProject(project);
			if (conEntity != null) {
				conEntity.setId(CommUtil.getKey());
				conEntity.setProjectId(project.getProjectId());
				conEntity.setShowExpertType(conEntity.getExpertType());
				mapper.saveCondition(conEntity);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
	 * 保存项目信息
	 *
	 * @param project
	 */
	@Transactional
	public void saveProjects(List<ProjectEntity> projectList, ConditionEntity conEntity) {

		String decimationBatch = CommUtil.getKey();
		// 循环赋值
		for (ProjectEntity projectEntity : projectList) {
			projectEntity.setProjectId(CommUtil.getKey());
			projectEntity.setDecimationBatch(decimationBatch);
			mapper.saveProject(projectEntity);
			if (conEntity != null) {
				conEntity.setId(CommUtil.getKey());
				conEntity.setProjectId(projectEntity.getProjectId());
				if(conEntity.getMethod()==4L){
					mapper.saveAuditCondition(conEntity);
				}else{
					mapper.saveCondition(conEntity);
				}
			}
		}

	}

	/**
	 * 保存项目信息(语音)
	 *
	 * @param project
	 */
	@Transactional
	public void saveVoiceProjects(List<ProjectEntity> projectList, ConditionEntity conEntity,
			List<ExtractDebarbEntity> debarbEntityList) {
		try {

			String decimationBatch = CommUtil.getKey();
			// 循环赋值
			for (ProjectEntity projectEntity : projectList) {
				projectEntity.setProjectId(CommUtil.getKey());
				projectEntity.setDecimationBatch(decimationBatch);
				mapper.saveProject(projectEntity);
				if (conEntity != null) {
					conEntity.setId(CommUtil.getKey());
					conEntity.setProjectId(projectEntity.getProjectId());
					mapper.saveCondition(conEntity);
				}
			}

			if(debarbEntityList!=null && debarbEntityList.size()>0){
				//保存回避条件
				for(ExtractDebarbEntity debarbEntity:debarbEntityList){
					debarbEntity.setDecimationbatch(decimationBatch);
					debarbEntity.setId(CommUtil.getKey());
					debarbMapper.saveDebarbData(debarbEntity);
				}
			}
		}catch (Exception e) {
			e.printStackTrace();
		}

	}

	/**
	 * 更新项目信息
	 *
	 * @param project
	 */
	@Transactional
	public void updateProject(ProjectEntity project) {
		mapper.updateProject(project);
		mapper.updateProjectStatus(project);
	}

	/**
	 * 更新项目信息(多项目)
	 *
	 * @param project
	 */
	@Transactional
	public void updateProjects(List<ProjectEntity> projectList, ConditionEntity conEntity) {
		for (ProjectEntity project : projectList) {
			mapper.updateProject(project);
			mapper.updateProjectStatus(project);
		}

		if (conEntity != null) {
			mapper.updateCondition(conEntity);
		}
	}

	/**
	 * 更新抽取条件
	 *
	 * @param conEntity
	 */
	public void updateCondition(ConditionEntity conEntity) {
		mapper.updateCondition(conEntity);
	}

	/**
	 * 保存抽取条件
	 *
	 * @param conEntity
	 */
	public void saveCondition(ConditionEntity conEntity) {
		conEntity.setId(CommUtil.getKey());
		mapper.saveCondition(conEntity);
	}

	/**
	 * 根据项目id查询项目信息
	 *
	 * @param project
	 * @return
	 */
	public ProjectEntity queryProjectById(ProjectEntity project) {

		return mapper.queryProjectById(project);
	}

	/**
	 * 根据项目DecimationBatch查询项目信息
	 *
	 * @param project
	 * @return
	 */
	public List<ProjectEntity> queryProjectByDecimationBatch(ProjectEntity project) {

		return mapper.queryProjectByDecimationBatch(project);
	}

	/**
	 * 根据项目id或项目批次查询项目信息
	 *
	 * @param project
	 * @return
	 */
	public List<ProjectEntity> queryProjectListById(ProjectEntity project) {
		return mapper.queryProjectListById(project);
	}

	/**
	 * 根据条件id查询抽取条件
	 *
	 * @param conEntity
	 * @return
	 */
	public ConditionEntity queryConditionById(ConditionEntity conEntity) {
		return mapper.queryConditionById(conEntity);
	}

	/**
	 * 根据抽取条件查询可供抽取的专家人数(用于检验人数是否满足)
	 *
	 * @param conEntity
	 * @return
	 */
	public String queryExpertToCheck(ConditionEntity conEntity,List<ProjectEntity> pList) {
		/**
		 * 2.0 这是用来转换评标专业id 的方法 最后决定直接修改sql中的类型和对应的id
		 */
		if (null != conEntity.getSourceCode() && conEntity.getSourceCode() != ""
				&& conEntity.getSourceCode().equals("szyc")) {
			// 转换专业类别代码start
			// (此代码块用于苏招易采请求转换)

			// 1.获取专业类别代码
			List<SpecialtyInfoEntity> specialtyInfoList = expertInfoMapper.querySpecialtyInfoList();
			String[] specialCodeList = conEntity.getExpertTypeName().split(",");
			// 重新定义sep_type地段()
			StringBuffer expertType = new StringBuffer();
			// 循环匹配,取出对应的专业类别ID
			for (int i = 0; i < specialCodeList.length; i++) {
				for (int j = 0; j < specialtyInfoList.size(); j++) {
					if (specialCodeList[i].equals(specialtyInfoList.get(j).getSpe_name())) {
						expertType.append(specialtyInfoList.get(j).getSpe_id() + ",");
						break;
					}
				}
			}
			if (null != expertType.toString() && expertType.toString() != "") {
				conEntity.setExpertType(expertType.toString().substring(0, expertType.toString().length() - 1));
			}
			// 转换专业类别代码end
		}
		getExpertPhoneCondition(conEntity);

		// 获取专家的专业类别,可执行的语句sql
		getExpertTypeQueryCondition(conEntity);

		// 将抽取专家的区域处理成库里可执行的结果集
		getZoneQueryCondition(conEntity);

		Integer difference = null;
		Integer agreeCount = 0;        // 已参加人数
		Integer countJoin=0;
		// 查询所有满足条件的国家级专家
		conEntity.setGrade(2L);
		List<ExpertInfoEntity> allExperts = allSatisfyConditionExperts(conEntity,pList);

		if (null != conEntity.getId()) {
			// 查询已参加的专家数量
			agreeCount = mapper.queryAgreeExpertCount(conEntity);
			countJoin +=agreeCount;
		}

		difference = (conEntity.getSeniorNum().intValue() - agreeCount) * SysConstants.EXPERT_EXTRACTION_MULTIPLE;
		if (conEntity.getSeniorNum().intValue() != 0) {
			if(agreeCount>conEntity.getSeniorNum().intValue()){
				return MessageConstants.LT_ENOUGH_SENIOR_EXPERT;
			}

			// 判断国家级专家是否满足人数
			if (difference > allExperts.size()) {

				return MessageConstants.NOT_ENOUGH_SENIOR_EXPERT;

			}
		}

		// 查询所有满足条件的地方级专家
		conEntity.setGrade(1L);
		allExperts = allSatisfyConditionExperts(conEntity,pList);
		agreeCount = 0;
		if (null != conEntity.getId()) {
			agreeCount = mapper.queryAgreeExpertCount(conEntity);
			countJoin +=agreeCount;
		}
		difference = (conEntity.getLocalNum().intValue() - agreeCount) * SysConstants.EXPERT_EXTRACTION_MULTIPLE;
		// 测试代码
		/*
		 * if(difference>allExperts.size()){ mapper.deleteTmpMajor(); return
		 * MessageConstants.NOT_ENOUGH_PLACE_EXPERT; }
		 */

		// 判断地方级专家是否满足人数

		// 正式代码
		if(agreeCount>conEntity.getLocalNum().intValue()){
			return MessageConstants.LT_ENOUGH_SENIOR_EXPERT;
		}
		if (difference > allExperts.size()) {
			return MessageConstants.NOT_ENOUGH_PLACE_EXPERT;
		}
		//判断参加的人数是否与修改之后的人数相等
		if(countJoin == conEntity.getTotal().intValue()){
			return "faile";
		}
		return "success";
	}
	/**
	 * 处理回避的专家
	 * @param conEntity
	 */
	private void getExpertPhoneCondition(ConditionEntity conEntity) {
		String phones = "";
		//判断是否有设置规则的抽取条件
		if(!EmptyUtils.isEmpty(conEntity.getIsRule())&&SysConstants.IS_RULE.METHOD_ONE==conEntity.getIsRule() ){
			//查询满足设置规则条件的专家
			List<ResultEntity> resultEntityList = mapper.queryExtractedFrequency(conEntity);
			if(!EmptyUtils.isEmpty(resultEntityList)){

				for(ResultEntity entity :resultEntityList){
					if(EmptyUtils.isEmpty(entity.getPhone())){
						continue;
					}
					phones+=entity.getPhone()+",";
				}
			}
		}
		//判断回避专家是否为空
		if(!EmptyUtils.isEmpty(conEntity.getExtractDebarbList())){
			for(ExtractDebarbEntity entity : conEntity.getExtractDebarbList()){
				if(EmptyUtils.isEmpty(entity.getExpertPhone())){
					continue;
				}
				phones+=entity.getExpertPhone()+",";
			}


		}

		if(!EmptyUtils.isEmpty(phones)){
			phones = phones.substring(0,phones.lastIndexOf(","));
			String[] tmpArray = phones.split(",");
			List<String> tmpList = new ArrayList<String>();
			StringBuffer tmpType = null;
			for (int i = 0; i < tmpArray.length; i++) {
				if (i == 0) {
					tmpType = new StringBuffer();
					tmpType.append("(");
					tmpType.append("'" + tmpArray[0] + "'");
				} else {
					if (i % 800 == 0) {
						tmpType.append(")");
						tmpList.add(tmpType.toString());
						tmpType = new StringBuffer();
						tmpType.append("(");
						tmpType.append("'" + tmpArray[i] + "'");
					} else {
						tmpType.append(",'" + tmpArray[i] + "'");
					}
				}
				if (i == tmpArray.length - 1) {
					tmpType.append(")");
					tmpList.add(tmpType.toString());
				}
			}

			conEntity.setExtractsAvoidList(tmpList);
		}


	}
	/**
	 * 所有满足条件的专家
	 *
	 * @param conEntity
	 * @return
	 */
	private List<ExpertInfoEntity> allSatisfyConditionExperts(ConditionEntity conEntity,List<ProjectEntity> projectList) {

		// 查询符合条件的所有专家数量
		List<ExpertInfoEntity> allExperts = mapper.queryExpertsByRule(conEntity);
		// 获取当前开标时间被抽取的专家列表未通知\已确认 参标的专家
		List<ExpertInfoEntity> notContainExpertsList = mapper.queryExpertsByBidTime(conEntity);
		//查询当前处室下被屏蔽的专家
		ExpertLimit expertlimit = new ExpertLimit();
		expertlimit.setDepartment(projectList.get(0).getDepartment());
		expertlimit.setLimitTime(DateUtil.dateToString(projectList.get(0).getBidTime(),"yyyy-MM"));
		List<ExpertLimit> expertLimitList = expertLimitService.queryExpertByDepartment(expertlimit);
		for(ExpertLimit ex:expertLimitList) {
			ExpertInfoEntity entity = new ExpertInfoEntity();
			entity.setUser_id(ex.getExpertId());
			notContainExpertsList.add(entity);
		}

		// 判断开标当天是否存在已抽取专家未通知\已确认 的专家
		if (notContainExpertsList != null && !notContainExpertsList.isEmpty()) {
			// 存在在所有专家集合中去除掉
			for (ExpertInfoEntity entity : notContainExpertsList) {
				for (int i = 0; i < allExperts.size(); i++) {
					// 去除已抽取专家未通知\已确认 的专家
					if (entity.getUser_id().equals(allExperts.get(i).getUser_id())) {
						allExperts.remove(i);
						break;
					}
				}

			}
		}
		return allExperts;
	}

	/**
	 * 处理专家的地区,拼接成可执行sql语句
	 *
	 * @param conEntity
	 */
	private void getZoneQueryCondition(ConditionEntity conEntity) {
		String zoneTemp = conEntity.getZone();
		if (zoneTemp != null && !"".equals(zoneTemp.trim())) {// 区域字段条件拆分为省市县三个条件
			String[] zones = zoneTemp.split(",");
			StringBuffer province = new StringBuffer("'");
			StringBuffer city = new StringBuffer("'");
			StringBuffer zone = new StringBuffer("'");
			for (int i = 0; i < zones.length; i++) {
				String temp = zones[i].trim();
				if (temp.endsWith("0000")) {// 省份
					province.append(temp).append("','");
					continue;
				}
				if (temp.endsWith("00")) {// 城市
					city.append(temp).append("','");
					continue;
				}
				zone.append(temp).append("','");// 区县
			}
			conEntity.setProvince(
					province.lastIndexOf("'") > 2 ? province.substring(1, province.lastIndexOf("'") - 2) : null);
			conEntity.setCity(city.lastIndexOf("'") > 2 ? city.substring(1, city.lastIndexOf("'") - 2) : null);
			conEntity.setZone(zone.lastIndexOf("'") > 2 ? zone.substring(1, zone.lastIndexOf("'") - 2) : null);
		}
	}

	/**
	 * 处理专家的专业类别,拼接成可执行sql语句
	 *
	 * @param conEntity
	 * @return
	 */
	private ConditionEntity getExpertTypeQueryCondition(ConditionEntity conEntity) {
		String[] tmpArray = conEntity.getExpertType().split(",");
		List<String> tmpList = new ArrayList<String>();
		StringBuffer tmpType = null;
		for (int i = 0; i < tmpArray.length; i++) {
			if (i == 0) {
				tmpType = new StringBuffer();
				tmpType.append("(");
				tmpType.append("'" + tmpArray[0] + "'");
			} else {
				if (i % 800 == 0) {
					tmpType.append(")");
					tmpList.add(tmpType.toString());
					tmpType = new StringBuffer();
					tmpType.append("(");
					tmpType.append("'" + tmpArray[i] + "'");
				} else {
					tmpType.append(",'" + tmpArray[i] + "'");
				}
			}
			if (i == tmpArray.length - 1) {
				tmpType.append(")");
				tmpList.add(tmpType.toString());
			}
		}
		conEntity.setExpertTypeList(tmpList);
		return conEntity;
	}

	/**
	 * 根据抽取条件查询专家信息
	 *
	 * @param conEntity
	 * @return
	 * @throws InterruptedException
	 */
	/*
	 * @Transactional public List<ExpertInfoEntity>
	 * queryExpertsByRule(ConditionEntity conEntity, ProjectEntity project)
	 * throws InterruptedException { if (conEntity.getId() != null &&
	 * conEntity.getId() != "") {//conEntity.getNum().intValue() == 1 &&
	 * //mapper.deleteResultInfo(conEntity);// 删除 }
	 * //conEntity.setExpertType(StringUtil.strToString(conEntity.getExpertType(
	 * )));// 对专业类别的查询条件进行用于in条件的转换 getExpertTypeQueryCondition(conEntity);
	 * getZoneQueryCondition(conEntity); conEntity.setGrade(1L);//默认查询地方级专家
	 * List<ExpertInfoEntity> allExperts =
	 * mapper.queryExpertsByRule(conEntity);// if
	 * (conEntity.getSeniorNum().intValue() >= 0) { conEntity.setGrade(2L);// }
	 * List<ExpertInfoEntity> serniorExperts =
	 * allSatisfyConditionExperts(conEntity); // 从结果中随机抽取queryExpertsByRule
	 * List<ExpertInfoEntity> experts = this.mergeResultByGrade(allExperts,
	 * serniorExperts, conEntity, project, conEntity); ResultEntity result =
	 * null; List<ResultEntity> resultList = new ArrayList<ResultEntity>(); Long
	 * sort = mapper.queryResultSort(conEntity); if (sort == null) { sort = 0L;
	 * } conEntity.setGrade(null); Integer count =
	 * mapper.queryAgreeExpertCount(conEntity);
	 *
	 * //boolean status = false; if (experts == null || experts.size() <= 0 ||
	 * experts.size() < conEntity.getTotal() - count) { //status = true;
	 * //smsProjectCreateUser(project, MessageConstants.NOT_ENOUGH_EXPERT);
	 * throw new HZWException(MessageConstants.NOT_ENOUGH_EXPERT); }
	 * sort++;//抽取批次 // 是否参加 0:参加 1:不参加 2:未通知 Long joinStatus =
	 * "2".equals(conEntity.getJoin_status()) ? 4L : 2L;
	 *
	 * for (ExpertInfoEntity ex : experts) { result = new ResultEntity();
	 * result.setExtractTime(new Date());// 抽取时间
	 * result.setUserId(ex.getUser_id());// 专家id result.setSort(sort);// 抽取的批次
	 * result.setJoinStatus(joinStatus); resultList.add(result); }
	 * conEntity.setNum(sort);//抽取的轮次
	 *
	 * //根据抽取批次号保存多个抽取结果 ProjectEntity pEntity =
	 * mapper.queryConditionIdByProjectId(project); //根据项目id，查询同批次的项目信息
	 * List<ProjectEntity> projectEntityList =
	 * mapper.queryConditionIdByBatch(project); //用于入库的专家信息List
	 * List<ResultEntity> newResultList = new ArrayList<ResultEntity>();
	 * //循环项目Lsit封装专家信息List for(ProjectEntity proEntity:projectEntityList){
	 * for(ResultEntity entity:resultList){ ResultEntity resultEntity = new
	 * ResultEntity(); SpringApplicationContext.copyProperties(entity,
	 * resultEntity); // 新对象赋值
	 * resultEntity.setId(CommUtil.getKey());//抽取专家表的主键ID
	 * resultEntity.setProjectId(proEntity.getProjectId());//项目id
	 * resultEntity.setConditionId(proEntity.getConditionId());// 抽取条件id
	 * newResultList.add(resultEntity); } } for(ResultEntity entity:resultList){
	 * ResultEntity resultEntity = new ResultEntity();
	 * SpringApplicationContext.copyProperties(entity, resultEntity); // 新对象赋值
	 * resultEntity.setId(CommUtil.getKey());//抽取专家表的主键ID
	 * resultEntity.setProjectId(pEntity.getProjectId());//项目id
	 * resultEntity.setConditionId(pEntity.getConditionId());// 抽取条件id
	 * newResultList.add(resultEntity); }
	 * mapper.saveExtractedExpert(newResultList);// 将抽取的专家直接加入到抽取结果中，默认状态为未操作
	 *
	 *//**
		 * 判断抽取方式决定抽取通知专家方式 如果数据来自SZYC 则直接短信通知 1:人工抽取 2：短信抽取 3：短信语音抽取 4：指定专家
		 *//*
		 * List<ExpertInfoEntity> expertList =
		 * mapper.queryExpertInfoByBatch(conEntity); if(null !=
		 * conEntity.getSourceCode() &&
		 * conEntity.getSourceCode().equals("szyc")){
		 * sendMessageForExperts(conEntity,expertList); }else
		 * if(conEntity.getMethod().toString().equals("2")){
		 * sendMessageForExperts(conEntity,expertList); }else
		 * if(conEntity.getMethod().toString().equals("3")){
		 * sendVoiceMessageForExperts(project,conEntity,expertList); }
		 *
		 * if(status) { // smsProjectCreateUser(project,
		 * MessageConstants.NOT_ENOUGH_EXPERT); throw new
		 * HZWException(MessageConstants.NOT_ENOUGH_EXPERT); }
		 *
		 *
		 * return experts; }
		 */

	/**
	 * 根据抽取条件查询专家信息(刘红抽去专家按钮[手动])
	 *
	 * @param conEntity
	 * @return
	 * @throws InterruptedException
	 */
	public List<ExpertInfoEntity> queryExpertsToExtraction(ConditionEntity conEntity, List<ProjectEntity> projectList)
			throws InterruptedException {
		List<ExpertInfoEntity> experts;
		long cu=System.currentTimeMillis();
		if(conEntity.getExpertTypeNum()==null) {
			experts = extractingExpert(conEntity, projectList);
		}else {
			experts = extractingExperts(conEntity, projectList);
		}
		// 抽取专家
		
		/*try {
			Thread.currentThread().sleep(2*60*1000);
		} catch (InterruptedException e1) {
			// TODO Auto-generated catch block
			e1.printStackTrace();
		}*/
		//重新获取当前的抽取条件
		ConditionEntity cEntity= queryConditionById(conEntity);
		//判断当前项目是否是“语音抽取"
		if(cEntity.getMethod().longValue()==SysConstants.CONDITION_METHOD.METHOD_THREE) {
			//判断当前项目之前的抽取方式是否是语音,注：1.当前抽取方式 "语音抽取"+之前抽取方式"语音抽取"=应急抽取    2.当前抽取方式 "语音抽取"+之前抽取方式"null"=语音抽取
			if(cEntity.getCurrentStatus()==null || cEntity.getCurrentStatus().longValue()!=SysConstants.CONDITION_CURRENT_STATUS.METHOD_TWO){
				//重新获取当前已经抽取而没有通知的专家结果
				ConditionEntity entity =  new ConditionEntity();
				entity.setDecimationBatch(projectList.get(0).getDecimationBatch());
				entity.setJoin_status(SysConstants.JOIN_STATUS.NOINFORM);
				List<ExpertInfoEntity> voiceExpertList =  queryProjectExpertInfo(entity);
				//根据批次号查询处理过的项目信息
				List<ProjectEntity> projectLst = queryProjectTOVoice(entity);

				//保存需要发送语音的专家信息
				List<ExpertInfoEntity> saveExpertList =  new ArrayList<ExpertInfoEntity>();
				//获取需要发送语音的专家的记录条数
				for(int i=0;i<voiceExpertList.size();i++) {
					//由于专家是三倍抽取，所有优先通知1/3的专家
					if(i<voiceExpertList.size()/3) {
						saveExpertList.add(voiceExpertList.get(i));
					}

				}
				sendVoiceForExperts(projectLst.get(0), conEntity, saveExpertList);
			}
		}


		/**
		 * 判断抽取方式决定抽取通知专家方式
		 * 如果数据来自SZYC	则直接短信通知
		 * 1:人工抽取
		 * 2：短信抽取
		 * 3：短信语音抽取
		 * 4：指定专家
		 */
		List<ExpertInfoEntity> expertList = mapper.queryExpertInfoByBatch(conEntity);
		if(null != conEntity.getSourceCode() && conEntity.getSourceCode().equals("szyc")){
			sendMessageForExperts(conEntity,expertList);
		}/*else if((conEntity.getMethod().toString()).equals("2")){
			sendMessageForExperts(conEntity,expertList);
		}else if(conEntity.getMethod().toString().equals("3")){
			copySendVoiceMessageForExperts(projectList,conEntity,expertList);
		}	*/
		return experts;
	}

	/**
	 * 根据抽取条件查询专家信息(刘红抽去专家按钮[语音])
	 *
	 * @param conEntity
	 * @return
	 * @throws InterruptedException
	 */
	public List<ExpertInfoEntity> queryExpertsToExtractionByVoice(ConditionEntity conEntity, List<ProjectEntity> projectList) throws InterruptedException {

		//抽取专家
		List<ExpertInfoEntity> experts = extractingExpert(conEntity, projectList);
		/*try {
			Thread.currentThread().sleep(2*60*1000);
		} catch (InterruptedException e1) {
			// TODO Auto-generated catch block
			e1.printStackTrace();
		}*/
		//重新获取当前的抽取条件
		ConditionEntity cEntity= queryConditionById(conEntity);
		//判断当前项目是否是“语音抽取"
		if(cEntity.getMethod().longValue()==SysConstants.CONDITION_METHOD.METHOD_THREE) {
			//判断当前项目之前的抽取方式是否是语音,注：1.当前抽取方式 "语音抽取"+之前抽取方式"语音抽取"=应急抽取    2.当前抽取方式 "语音抽取"+之前抽取方式"null"=语音抽取
			if(cEntity.getCurrentStatus()==null || cEntity.getCurrentStatus().longValue()!=SysConstants.CONDITION_CURRENT_STATUS.METHOD_TWO){
				//重新获取当前已经抽取而没有通知的专家结果
				ConditionEntity entity =  new ConditionEntity();
				entity.setDecimationBatch(projectList.get(0).getDecimationBatch());
				entity.setJoin_status(SysConstants.JOIN_STATUS.NOINFORM);
				List<ExpertInfoEntity> voiceExpertList =  queryProjectExpertInfo(conEntity);
				//根据批次号查询处理过的项目信息
				List<ProjectEntity> projectLst = queryProjectTOVoice(entity);

				//保存需要发送语音的专家信息
				List<ExpertInfoEntity> saveExpertList =  new ArrayList<ExpertInfoEntity>();
				//获取需要发送语音的专家的记录条数
				for(int i=0;i<voiceExpertList.size();i++) {
					//由于专家是三倍抽取，所有优先通知1/3的专家
					if(i<voiceExpertList.size()/3) {
						saveExpertList.add(voiceExpertList.get(i));
					}

				}
				sendVoiceForExperts(projectLst.get(0), conEntity, saveExpertList);
			}
		}



		/**
		 * 判断抽取方式决定抽取通知专家方式 如果数据来自SZYC 则直接短信通知 1:人工抽取 2：短信抽取 3：短信语音抽取 4：指定专家
		 */
		List<ExpertInfoEntity> expertList = mapper.queryExpertInfoByBatch(conEntity);
		if (null != conEntity.getSourceCode() && conEntity.getSourceCode().equals("szyc")) {
			sendMessageForExperts(conEntity, expertList);
		} /*
			 * else if((conEntity.getMethod().toString()).equals("2")){
			 * sendMessageForExperts(conEntity,expertList); }else
			 * if(conEntity.getMethod().toString().equals("3")){
			 * copySendVoiceMessageForExperts(projectList,conEntity,expertList);
			 * }
			 */
		return experts;
	}

	/**
	 * 抽取专家（刘红）
	 *
	 * @param conEntity
	 * @param projectList
	 * @return
	 */
	@Transactional
	private synchronized List<ExpertInfoEntity>  extractingExpert(ConditionEntity conEntity, List<ProjectEntity> projectList) {
		if (conEntity.getId() != null && conEntity.getId() != "") {// conEntity.getNum().intValue()
																	// == 1 &&
			// mapper.deleteResultInfo(conEntity);// 删除
		}
		getExpertPhoneCondition(conEntity);

		// 处理专家的专业类别,拼接成可执行sql语句
		getExpertTypeQueryCondition(conEntity);
		// 处理专家的地区,拼接成可执行sql语句
		getZoneQueryCondition(conEntity);

		conEntity.setGrade(1L);// 默认查询地方级专家
		conEntity.setDecimationBatch(projectList.get(0).getDecimationBatch());

		// 查询所有满足条件的地方级专家
		List<ExpertInfoEntity> localExperts = allSatisfyConditionExperts(conEntity,projectList);//
		if (conEntity.getSeniorNum().intValue() >= 0) {
			conEntity.setGrade(2L);//
		}
		// 查询所有满足条件的国家级专家
		List<ExpertInfoEntity> serniorExperts = allSatisfyConditionExperts(conEntity,projectList);

		// 从结果中随机抽取queryExpertsByRule
		List<ExpertInfoEntity> experts = this.copyMergeResultByGrade(localExperts, serniorExperts, conEntity,
				conEntity);
		ResultEntity result = null;
		List<ResultEntity> resultList = new ArrayList<ResultEntity>();
		Long sort = getMaxSort(conEntity);
		conEntity.setGrade(null);
		Integer count = mapper.queryAgreeExpertCount(conEntity);

		// boolean status = false;
		if (experts == null || experts.size() <= 0 || experts.size() < conEntity.getTotal() - count) {
			// status = true;
			// smsProjectCreateUser(project,
			// MessageConstants.NOT_ENOUGH_EXPERT);
			throw new HZWException(MessageConstants.NOT_ENOUGH_EXPERT);
		}
		sort++;// 抽取批次
		// 是否参加 0:参加 1:不参加 2:未通知
		Long joinStatus = (SysConstants.CONDITION_METHOD.METHOD_TWO == conEntity.getMethod()) ? 4L : 2L;

		for (ExpertInfoEntity ex : experts) {
			result = new ResultEntity();
			result.setExtractTime(new Date());// 抽取时间
			result.setUserId(ex.getUser_id());// 专家id
			result.setSort(sort);// 抽取的批次
			result.setJoinStatus(joinStatus);
			resultList.add(result);
		}
		conEntity.setNum(sort);// 抽取的轮次

		// 用于入库的专家信息List
		List<ResultEntity> newResultList = new ArrayList<ResultEntity>();
		// 循环项目Lsit封装专家信息List
		for (ProjectEntity proEntity : projectList) {
			for (ResultEntity entity : resultList) {
				ResultEntity resultEntity = new ResultEntity();
				SpringApplicationContext.copyProperties(entity, resultEntity); // 新对象赋值
				resultEntity.setId(CommUtil.getKey());// 抽取专家表的主键ID
				resultEntity.setProjectId(proEntity.getProjectId());// 项目id
				resultEntity.setConditionId(proEntity.getConditionId());// 抽取条件id
				resultEntity.setDecimationBatch(proEntity.getDecimationBatch());
				// 判断当前项目是否是语音项目
				if (conEntity.getMethod().intValue() == SysConstants.CONDITION_METHOD.METHOD_THREE) {
					// 判断当前项目之前的抽取方式是否是语音
					if (conEntity.getCurrentStatus() != null
							&& conEntity.getCurrentStatus() == SysConstants.CONDITION_CURRENT_STATUS.METHOD_TWO) {
						// 将当前抽取专家状态标记为应急抽取
						resultEntity.setExpertWay(SysConstants.EXPERT_WAY.WAY_TWO);
					} else {
						// 将当前抽取专家状态标记为语音抽取
						resultEntity.setExpertWay(SysConstants.EXPERT_WAY.WAY_ONE);
					}
				}

				newResultList.add(resultEntity);
			}
		}
		mapper.saveExtractedExpert(newResultList);// 将抽取的专家直接加入到抽取结果中，默认状态为未操作
		//并且将项目状态修改为抽取中
		ProjectEntity projectEntity = new ProjectEntity();
		projectEntity.setDecimationBatch(conEntity.getDecimationBatch());
		projectEntity.setStatus(2L);
		updateProjectStatus(projectEntity);

		return experts;
	}



	/**
	 *
	 * 函数功能描述：获取最大的抽取结果
	 * @param conEntity
	 * @return
	 */
	public Long getMaxSort(ConditionEntity conEntity) {
		Long sort = mapper.queryResultSort(conEntity);
		if (sort == null) {
			sort = 0L;
		}
		return sort;
	}

	/**
	 * 根据抽取条件查询可供抽取的专家人数(复制的上面的方法做部分调整)
	 *
	 * @param conEntity
	 * @return
	 */
	/*
	 * public Integer copyQueryExpertCountByRule(ConditionEntity conEntity,int
	 * expertType) {
	 * conEntity.setExpertType(StringUtil.strToString(conEntity.getExpertType())
	 * );// 对专业类别的查询条件进行用于in条件的转换 getZoneQueryCondition(conEntity); Integer
	 * difference = null; Integer agreeCount = 0; // 已参加人数 Integer expertCount =
	 * 0; if(expertType == 2){ // 查询国家级专家 conEntity.setGrade(2L); expertCount =
	 * mapper.queryExpertCountByRule(conEntity); if (null != conEntity.getId())
	 * { agreeCount = mapper.queryAgreeExpertCount(conEntity); } Long seniorNum
	 * = conEntity.getSeniorNum(); if(seniorNum == null){ return expertCount; }
	 * difference = (seniorNum.intValue() - agreeCount) *
	 * SysConstants.EXPERT_EXTRACTION_MULTIPLE; // 判断国家级专家是否满足人数 if (difference
	 * > expertCount) { return expertCount; } }else if(expertType == 1){ //
	 * 查询地方级专家 conEntity.setGrade(1L); expertCount =
	 * mapper.copyQueryExpertCountByRule(conEntity); agreeCount = 0; if (null !=
	 * conEntity.getId()) { agreeCount =
	 * mapper.queryAgreeExpertCount(conEntity); } Long localNum =
	 * conEntity.getLocalNum(); if(localNum == null){ return expertCount; }
	 * difference = (localNum.intValue() - agreeCount) *
	 * SysConstants.EXPERT_EXTRACTION_MULTIPLE; // 判断地方级专家是否满足人数 if (difference
	 * > expertCount) { return difference-expertCount; } }else if(expertType ==
	 * 0){ // 查询国家级专家 conEntity.setGrade(2L); expertCount =
	 * mapper.queryExpertCountByRule(conEntity); // 查询国家级专家
	 * conEntity.setGrade(2L); expertCount =
	 * mapper.queryExpertCountByRule(conEntity) + expertCount; if (null !=
	 * conEntity.getId()) { agreeCount =
	 * mapper.queryAgreeExpertCount(conEntity); } Long seniorNum =
	 * conEntity.getSeniorNum(); Long localNum = conEntity.getLocalNum();
	 * if(seniorNum == null || localNum==null){ return expertCount; } difference
	 * = (conEntity.getLocalNum().intValue() - agreeCount) *
	 * SysConstants.EXPERT_EXTRACTION_MULTIPLE; //判断地方级专家是否满足人数 if (difference >
	 * expertCount) { return expertCount; } }
	 *
	 *
	 * return null; }
	 */

	/**
	 * 根据抽取条件查询可供抽取的专家人数
	 *
	 * @param conEntity
	 * @return
	 */
	public Integer queryExpertCountByRule(ConditionEntity conEntity, int expertType) {
		getExpertTypeQueryCondition(conEntity);
		getZoneQueryCondition(conEntity);
		Integer difference = null;
		Integer agreeCount = 0;        // 已参加人数
		Integer expertCount = 0;
		if (expertType == 2) {
			// 查询国家级专家
			conEntity.setGrade(2L);
			expertCount = mapper.queryExpertCountByRule(conEntity);
			if (null != conEntity.getId()) {
				agreeCount = mapper.queryAgreeExpertCount(conEntity);
			}
			Long seniorNum = conEntity.getSeniorNum();
			if (seniorNum == null) {
				return expertCount;
			}
			difference = (seniorNum.intValue() - agreeCount) * SysConstants.EXPERT_EXTRACTION_MULTIPLE;
			// 判断国家级专家是否满足人数
			if (difference > expertCount) {
				return expertCount;
			}
		} else if (expertType == 1) {
			// 查询地方级专家
			conEntity.setGrade(1L);
			expertCount = mapper.queryExpertCountByRule(conEntity);
			agreeCount = 0;
			if (null != conEntity.getId()) {
				agreeCount = mapper.queryAgreeExpertCount(conEntity);
			}
			Long localNum = conEntity.getLocalNum();
			if (localNum == null) {
				return expertCount;
			}
			difference = (localNum.intValue() - agreeCount) * SysConstants.EXPERT_EXTRACTION_MULTIPLE;
			// 判断地方级专家是否满足人数
			if (difference > expertCount) {
				return expertCount;
			}
		} else if (expertType == 0) {
			// 查询国家级专家
			conEntity.setGrade(2L);
			expertCount = mapper.queryExpertCountByRule(conEntity);
			// 查询国家级专家
			conEntity.setGrade(2L);
			expertCount = mapper.queryExpertCountByRule(conEntity) + expertCount;
			if (null != conEntity.getId()) {
				agreeCount = mapper.queryAgreeExpertCount(conEntity);
			}
			Long seniorNum = conEntity.getSeniorNum();
			Long localNum = conEntity.getLocalNum();
			if (seniorNum == null || localNum == null) {
				return expertCount;
			}
			difference = (conEntity.getLocalNum().intValue() - agreeCount) * SysConstants.EXPERT_EXTRACTION_MULTIPLE;
			// 判断地方级专家是否满足人数
			if (difference > expertCount) {
				return expertCount;
			}
		}

		return null;
	}

	/**
	 * 查询项目已抽取的专家
	 *
	 * @param conEntity
	 * @param num
	 * @return
	 */
	public List<ResultEntity> queryExtractedExperts(ConditionEntity conEntity) {
		return mapper.queryExtractedExperts(conEntity);
	}

	/**
	 * 更新抽取专家信息
	 *
	 * @param result
	 */
	public void updateExtractedExpert(ResultEntity result) {
		result.setCallTime(new Date());
		mapper.updateExtractedExpert(result);
	}

	/**
	 * 验证项目指定人数是否满足条件，是的话则自动更新该项目为已抽取
	 *
	 * @param condition
	 */
	public void autoUpdateProStatus(ConditionEntity condition) {
		condition = mapper.queryExtractConditionById(condition);
		// 查询已同意参标的专家人数
		Integer count = mapper.queryAgreeExpertCount(condition);

		if (condition.getLocalNum().intValue() + condition.getSeniorNum().intValue() == count) {
			ProjectEntity project = new ProjectEntity();
			project.setStatus(3L);
			project.setProjectId(condition.getProjectId());
			mapper.updateProjectStatus(project);
		}
	}

	/**
	 * 统计没有通知的专家数量（后期功能变化修改的功能）
	 *
	 * @param conEntity
	 */
	public Integer countNotExtractResult(ConditionEntity conEntity) {
		Integer count = mapper.countNotExtractedResult(conEntity);
		return count;
	}

	/**
	 * 整理结果集使得国家级的专家人数要求(复制上面的方法以满足一个流水号多个项目的抽取)
	 *
	 * @param allList
	 * @param serniorList
	 * @param con
	 *            抽取条件
	 * @return
	 */
	private List<ExpertInfoEntity> copyMergeResultByGrade(List<ExpertInfoEntity> localList,
			List<ExpertInfoEntity> serniorList, ConditionEntity con, ConditionEntity conEntity) {
		con.setGrade(2L);
		// 查询已同意参标的国际级专家人数
		Integer count = mapper.queryAgreeExpertCount(con);
		// 计算实际需要抽取国际级专家人数
		Integer difference = (con.getSeniorNum().intValue() - (count == null ? 0 : count))
				* SysConstants.EXPERT_EXTRACTION_MULTIPLE;
		List<ExpertInfoEntity> saveExpert = new ArrayList<ExpertInfoEntity>();
		// 判断国家级专家是否人数足够
		if (conEntity.getSeniorNum().intValue() != 0) {
			// 判断国家级专家是否满足人数
			if (difference > serniorList.size()) {
				// copySmsProjectCreateUser(projectList,
				// MessageConstants.NOT_ENOUGH_SENIOR_EXPERT);
				//将项目状态修改为人数不足
				ProjectEntity projectEntity = new ProjectEntity();
				projectEntity.setDecimationBatch(conEntity.getDecimationBatch());
				projectEntity.setStatus(20L);
				mapper.updateProjectStatus(projectEntity);
				throw new HZWException(
						MessageConstants.NOT_ENOUGH_SENIOR_EXPERT + "系统缺" + (difference - serniorList.size()) + "人");
			} else {
				saveExpert = this.getRandomExpertsFromResult(serniorList, difference);// 得到国家级专家集合
			}
		}

		con.setGrade(1L);
		// 查询已同意参标的地方级专家人数
		Integer count2 = mapper.queryAgreeExpertCount(con);
		// 计算实际需要抽取地方级专家人数
		difference = (conEntity.getLocalNum().intValue() - (count2 == null ? 0 : count2))
				* SysConstants.EXPERT_EXTRACTION_MULTIPLE;
		if (difference > localList.size()) {
			 /*copySmsProjectCreateUser(projectList,
			 MessageConstants.NOT_ENOUGH_PLACE_EXPERT);
			 Integer expertCount = this.copyQueryExpertCountByRule(conEntity,
			 1);*/
			ProjectEntity projectEntity = new ProjectEntity();
			projectEntity.setDecimationBatch(conEntity.getDecimationBatch());
			projectEntity.setStatus(20L);
			mapper.updateProjectStatus(projectEntity);
			throw new HZWException(
					MessageConstants.NOT_ENOUGH_PLACE_EXPERT + "系统缺：" + (difference - localList.size()) + "人");
		}

		List<ExpertInfoEntity> other = this.getRandomExpertsFromResult(localList, difference);// 得到其他专家集合
		saveExpert.addAll(other);// 两集合合并
		return saveExpert;
	}

	/*	*//**
			 * 整理结果集使得国家级的专家人数要求
			 *
			 * @param allList
			 * @param serniorList
			 * @param con
			 *            抽取条件
			 * @return
			 *//*
			 * private List<ExpertInfoEntity>
			 * mergeResultByGrade(List<ExpertInfoEntity> allList,
			 * List<ExpertInfoEntity> serniorList, ConditionEntity con,
			 * ProjectEntity project, ConditionEntity conEntity) {
			 * con.setGrade(2L); // 查询已同意参标的国际级专家人数 Integer count =
			 * mapper.queryAgreeExpertCount(con); // 计算实际需要抽取国际级专家人数 Integer
			 * difference = (con.getSeniorNum().intValue() - count) *
			 * SysConstants.EXPERT_EXTRACTION_MULTIPLE; List<ExpertInfoEntity>
			 * senior = this.getRandomExpertsFromResult(serniorList,
			 * difference);// 得到国家级专家集合 if (difference>0&&senior == null) {//
			 * 国家级专家人数不足 smsProjectCreateUser(project,
			 * MessageConstants.NOT_ENOUGH_SENIOR_EXPERT); throw new
			 * HZWException(MessageConstants.NOT_ENOUGH_SENIOR_EXPERT); }
			 * if(senior == null){//无国家级专家 senior=new
			 * ArrayList<ExpertInfoEntity>(); } con.setGrade(1L); //
			 * 查询已同意参标的地方级专家人数 Integer count2 =
			 * mapper.queryAgreeExpertCount(con); int otherNum = (int)
			 * (con.getTotal() - con.getSeniorNum());//
			 * 除已选择国家级专家外还需要的专家人数（可以为国家级专家,即需要在全集中选择） // 判断是否已抽取过 if (0 < count
			 * + count2) { otherNum = con.getTotal().intValue(); count +=
			 * count2; } // 计算实际需要抽取地方级专家人数 difference = (otherNum - count) * 3;
			 * List<ExpertInfoEntity> other =
			 * this.getRandomExpertsFromResult(allList, difference);// 得到其他专家集合
			 * String seniorExpertIds = ","; for (ExpertInfoEntity s : senior)
			 * {// 拼接已抽取的国家级专家id seniorExpertIds += s.getUser_id() + ","; }
			 * List<ExpertInfoEntity> lastOtherResult = new
			 * ArrayList<ExpertInfoEntity>();// 去除已选中的国家级专家 while (other !=
			 * null) { for (ExpertInfoEntity o : other) { if
			 * (seniorExpertIds.contains("," + o.getUser_id() + ",")) {//
			 * 已选择的国家级专家中包含该专家 continue; } lastOtherResult.add(o); }
			 * //库里的专家数量少用需要提供的数量，则显示全部 if(allList.size()<difference) { break; }
			 * if (lastOtherResult.size() == difference ) {// 剩下专家抽取完成 break; }
			 * // 继续抽取缺少的专家 other = this.getRandomExpertsFromResult(allList,
			 * difference - lastOtherResult.size());// 得到其他专家集合 } if (other ==
			 * null && lastOtherResult.size() != difference) {// 专家人数不足
			 * smsProjectCreateUser(project,
			 * MessageConstants.NOT_ENOUGH_PLACE_EXPERT); Integer expertCount =
			 * this.copyQueryExpertCountByRule(conEntity, 1); throw new
			 * HZWException(MessageConstants.NOT_ENOUGH_PLACE_EXPERT+"系统缺："+
			 * expertCount+"人"); } senior.addAll(lastOtherResult);// 两集合合并
			 * return senior; }
			 */

	/**
	 * 抽取专家人数不足时，向项目创建人发送短信
	 *
	 * @param project
	 * @param errorMsg
	 * 			@2.0 变更 增加原来查询用户信息拼接数据,改为从项目信息中获取 author:chaojian
	 *            date:2017-11-26
	 */
	public void smsProjectCreateUser(ProjectEntity project, String errorMsg) {
		if (project.getCreate_id() != null) {  // 新增加判断是否存在用户信息,因为接口入库的抽取项目信息是不存在项目创建人的
			UserEntity user = userMapper.selectUserById(project.getCreate_id());
			// 只向处室项目创建人发送提示短信
			if (!SysConstants.ROLE.EXTRACT.equals(user.getRole_name())) {
				List<String[]> msgList = new ArrayList<String[]>();
				String[] msg = new String[3];
				// 手机号
				msg[0] = user.getMobile();
				// 短信内容
				msg[1] = user.getUser_name() + "你好，" + "批次号:[" + project.getDecimationBatch() + "]" + errorMsg
						+ "。【省交易中心】";
				msgList.add(msg);
				// 记录短信
				SmsRecordEntity smsRecordEntity = new SmsRecordEntity();
				smsRecordEntity.setSms_id(CommUtil.getKey());
				smsRecordEntity.setProject_id(project.getProjectId());
				smsRecordEntity.setSms_user_id(user.getUser_id());
				smsRecordEntity.setSms_user_name(user.getUser_name());
				smsRecordEntity.setSms_content(msg[1]);
				smsRecordEntity.setSms_mobile(msg[0]);
				smsRecordEntity.setSms_type(SysConstants.SMS_TYPE.SMS_THREE);
				smsRecordEntity.setSms_time(new Date());
				smsRecordService.insertSmsRecord(smsRecordEntity);

				String result_mt = SMSNewUtil.sendMsgByDiffContent(msgList);
				if (result_mt.startsWith("-") || result_mt.equals("")) {// 发送短信，如果是以负号开头就是发送失败。
					System.out.print("发送失败！返回值为：" + result_mt + "请查看webservice返回值对照表");
				}
			}
		} else {
			List<String[]> msgList = new ArrayList<String[]>();
			String[] msg = new String[3];
			// 手机号
			msg[0] = project.getPhone();
			// 短信内容
			msg[1] = project.getManager() + "你好，" + project.getProjectName() + errorMsg + "请修改抽取条件重新提交。【省交易中心】";
			msgList.add(msg);
			// 记录短信
			SmsRecordEntity smsRecordEntity = new SmsRecordEntity();
			smsRecordEntity.setSms_id(CommUtil.getKey());
			smsRecordEntity.setProject_id(project.getProjectId());
			smsRecordEntity.setSms_user_id(project.getManager());
			smsRecordEntity.setSms_user_name(project.getManager());
			smsRecordEntity.setSms_content(msg[1]);
			smsRecordEntity.setSms_mobile(msg[0]);
			smsRecordEntity.setSms_type(SysConstants.SMS_TYPE.SMS_THREE);
			smsRecordEntity.setSms_time(new Date());
			smsRecordService.insertSmsRecord(smsRecordEntity);

			String result_mt = SMSNewUtil.sendMsgByDiffContent(msgList);
			if (result_mt.startsWith("-") || result_mt.equals("")) {// 发送短信，如果是以负号开头就是发送失败。
				System.out.print("发送失败！返回值为：" + result_mt + "请查看webservice返回值对照表");
			}
		}
	}



	/**
	 * 抽取专家人数不足时，向项目创建人发送短信(指定模板)
	 *
	 * @param project
	 * @param errorMsg
	 * 			@2.0 变更 增加原来查询用户信息拼接数据,改为从项目信息中获取 author:chaojian
	 *            date:2017-11-26
	 */
	public void smsProjectCreateUserTemplate(ProjectEntity project, String content) {
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			String smsStr =content;
			smsStr = smsStr.replaceAll("【批次号】", project.getDecimationBatch());
			smsStr = smsStr.replaceAll("【项目名称】", project.getProjectName());
			smsStr = smsStr.replaceAll("【项目编号】", project.getProjectNo());
			smsStr = smsStr.replaceAll("【开标时间】", sdf.format(project.getBidTime()));
			smsStr = smsStr.replaceAll("【开标地点】", project.getBidAddress());
			smsStr = smsStr.replaceAll("【项目负责人】", project.getManager());
			smsStr = smsStr.replaceAll("【电话】", project.getPhone());


		if (project.getCreate_id() != null) {  // 新增加判断是否存在用户信息,因为接口入库的抽取项目信息是不存在项目创建人的
			UserEntity user = userMapper.selectUserById(project.getCreate_id());
			// 只向处室项目创建人发送提示短信
			if (!SysConstants.ROLE.EXTRACT.equals(user.getRole_name())) {
				List<String[]> msgList = new ArrayList<String[]>();
				String[] msg = new String[3];
				// 手机号
				msg[0] = user.getMobile();
				// 短信内容
				msg[1] = user.getUser_name() + "，" + smsStr + "。【省交易中心】";
				msgList.add(msg);
				// 记录短信
				SmsRecordEntity smsRecordEntity = new SmsRecordEntity();
				smsRecordEntity.setSms_id(CommUtil.getKey());
				smsRecordEntity.setProject_id(project.getProjectId());
				smsRecordEntity.setSms_user_id(user.getUser_id());
				smsRecordEntity.setSms_user_name(user.getUser_name());
				smsRecordEntity.setSms_content(msg[1]);
				smsRecordEntity.setSms_mobile(msg[0]);
				smsRecordEntity.setSms_type(SysConstants.SMS_TYPE.SMS_THREE);
				smsRecordEntity.setSms_time(new Date());
				smsRecordService.insertSmsRecord(smsRecordEntity);

				String result_mt = SMSNewUtil.sendMsgByDiffContent(msgList);
				if (result_mt.startsWith("-") || result_mt.equals("")) {// 发送短信，如果是以负号开头就是发送失败。
					System.out.print("发送失败！返回值为：" + result_mt + "请查看webservice返回值对照表");
				}
			}
		} else {
			List<String[]> msgList = new ArrayList<String[]>();
			String[] msg = new String[3];
			// 手机号
			msg[0] = project.getPhone();
			// 短信内容
			msg[1] = project.getManager() + "，" +smsStr+ "【省交易中心】";
			msgList.add(msg);
			// 记录短信
			SmsRecordEntity smsRecordEntity = new SmsRecordEntity();
			smsRecordEntity.setSms_id(CommUtil.getKey());
			smsRecordEntity.setProject_id(project.getProjectId());
			smsRecordEntity.setSms_user_id(project.getManager());
			smsRecordEntity.setSms_user_name(project.getManager());
			smsRecordEntity.setSms_content(msg[1]);
			smsRecordEntity.setSms_mobile(msg[0]);
			smsRecordEntity.setSms_type(SysConstants.SMS_TYPE.SMS_THREE);
			smsRecordEntity.setSms_time(new Date());
			smsRecordService.insertSmsRecord(smsRecordEntity);

			String result_mt = SMSNewUtil.sendMsgByDiffContent(msgList);
			if (result_mt.startsWith("-") || result_mt.equals("")) {// 发送短信，如果是以负号开头就是发送失败。
				System.out.print("发送失败！返回值为：" + result_mt + "请查看webservice返回值对照表");
			}
		}
	}


	/**
	 * 抽取专家人数不足时，向项目创建人发送短信（复制上面的方法以满足一个流水号对应多个项目的短信发送）
	 *
	 * @param project
	 * @param errorMsg
	 * 			@2.0 变更 增加原来查询用户信息拼接数据,改为从项目信息中获取 author:chaojian
	 *            date:2017-11-26
	 */
	private void copySmsProjectCreateUser(List<ProjectEntity> projectList, String errorMsg) {
		for (ProjectEntity project : projectList) {
			if (project.getCreate_id() != null) {  // 新增加判断是否存在用户信息,因为接口入库的抽取项目信息是不存在项目创建人的
				UserEntity user = userMapper.selectUserById(project.getCreate_id());
				// 只向处室项目创建人发送提示短信
				if (!SysConstants.ROLE.EXTRACT.equals(user.getRole_name())) {
					List<String[]> msgList = new ArrayList<String[]>();
					String[] msg = new String[3];
					// 手机号
					msg[0] = user.getMobile();
					// 短信内容
					msg[1] = user.getUser_name() + "你好，" + project.getProjectName() + errorMsg + "请修改抽取条件重新提交。【省交易中心】";
					msgList.add(msg);
					// 记录短信
					SmsRecordEntity smsRecordEntity = new SmsRecordEntity();
					smsRecordEntity.setSms_id(CommUtil.getKey());
					smsRecordEntity.setProject_id(project.getProjectId());
					smsRecordEntity.setSms_user_id(user.getUser_id());
					smsRecordEntity.setSms_user_name(user.getUser_name());
					smsRecordEntity.setSms_content(msg[1]);
					smsRecordEntity.setSms_mobile(msg[0]);
					smsRecordEntity.setSms_type(SysConstants.SMS_TYPE.SMS_THREE);
					smsRecordEntity.setSms_time(new Date());
					smsRecordService.insertSmsRecord(smsRecordEntity);

					String result_mt = SMSNewUtil.sendMsgByDiffContent(msgList);
					if (result_mt.startsWith("-") || result_mt.equals("")) {// 发送短信，如果是以负号开头就是发送失败。
						System.out.print("发送失败！返回值为：" + result_mt + "请查看webservice返回值对照表");
					}
				}
			} else {
				List<String[]> msgList = new ArrayList<String[]>();
				String[] msg = new String[3];
				// 手机号
				msg[0] = project.getPhone();
				// 短信内容
				msg[1] = project.getManager() + "你好，" + project.getProjectName() + errorMsg + "请修改抽取条件重新提交。【省交易中心】";
				msgList.add(msg);
				// 记录短信
				SmsRecordEntity smsRecordEntity = new SmsRecordEntity();
				smsRecordEntity.setSms_id(CommUtil.getKey());
				smsRecordEntity.setProject_id(project.getProjectId());
				smsRecordEntity.setSms_user_id(project.getManager());
				smsRecordEntity.setSms_user_name(project.getManager());
				smsRecordEntity.setSms_content(msg[1]);
				smsRecordEntity.setSms_mobile(msg[0]);
				smsRecordEntity.setSms_type(SysConstants.SMS_TYPE.SMS_THREE);
				smsRecordEntity.setSms_time(new Date());
				smsRecordService.insertSmsRecord(smsRecordEntity);

				String result_mt = SMSNewUtil.sendMsgByDiffContent(msgList);
				if (result_mt.startsWith("-") || result_mt.equals("")) {// 发送短信，如果是以负号开头就是发送失败。
					System.out.print("发送失败！返回值为：" + result_mt + "请查看webservice返回值对照表");
				}
			}
		}
	}

	/**
	 * 语音通知完，专家回复参加之后，向专家发送短信通知专家开标时间、地点
	 *
	 * @param project
	 * @param errorMsg
	 * 			@2.0 变更 增加原来查询用户信息拼接数据,改为从项目信息中获取 author:chaojian
	 *            date:2017-11-26
	 */
	public SmsRecordEntity smsExperts(ResultEntity entity, ConditionEntity conEntity) {
		SmsRecordEntity smsRecordEntity = new SmsRecordEntity();
		try {
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			// 判断手机号码是否为空
			if (entity.getPhone() != "" && entity.getPhone() != null) {
				// 手机号码不为空，根据手机号码查询专家的名字
				ExpertInfoEntity expertInfo = expertInfoMapper.queryExpertByMobile(entity.getPhone());
				// 根据批次号查询项目信息
				ProjectEntity project = new ProjectEntity();
				project.setDecimationBatch(entity.getDecimationBatch());

				List<ProjectEntity> projectList = mapper.queryProjectByDecimationBatch(project);
				if (expertInfo != null) {
					// 编辑短信内容
					List<String[]> msgList = new ArrayList<String[]>();
					String[] msg = new String[3];
					// 手机号
					msg[0] = entity.getPhone();
					/*
					 * "您好，请于【开标时间】 前到江苏省国际招标公司（南京市西康路7号）【开标地点】会议室参加招标采购项目评审，联系人：【项目负责人】"
					 * +
					 * "联系电话：【电话】，时间预计半天结束，感谢您对招标采购工作的支持。请专家带好身份证去参加评审。项目识别码为：【批次号】，请牢记该码，"
					 * + "在请假等特殊情况时需要使用!"
					 */
					String smsStr = conEntity.getSmsContent();
					smsStr = smsStr.replaceAll("【批次号】", projectList.get(0).getDecimationBatch());
					smsStr = smsStr.replaceAll("【项目名称】", projectList.get(0).getProjectName());
					smsStr = smsStr.replaceAll("【项目编号】", projectList.get(0).getProjectNo());
					smsStr = smsStr.replaceAll("【开标时间】", sdf.format(projectList.get(0).getBidTime()));
					smsStr = smsStr.replaceAll("【开标地点】", projectList.get(0).getBidAddress());
					smsStr = smsStr.replaceAll("【项目负责人】", projectList.get(0).getManager());
					smsStr = smsStr.replaceAll("【电话】", projectList.get(0).getPhone());
					// 短信内容
					msg[1] = expertInfo.getUser_name() + "专家，" + smsStr;
					// expertInfo.getUser_name() + "您好，江苏省国际招标公司诚邀您于" +
					// sdf.format(projectList.get(0).getBidTime()) +
					// "在"+projectList.get(0).getBidAddress()+"参加评标。";
					msgList.add(msg);

					// XXX您好，江苏省国际招标公司诚邀您于【开标时间】在【开标地点】参加评标，回复：1-参加，2-不参加。以短信通知为准。
					// 记录短信
					// SmsRecordEntity smsRecordEntity = new SmsRecordEntity();
					smsRecordEntity.setSms_id(CommUtil.getKey());
					smsRecordEntity.setProject_id(project.getProjectId());
					smsRecordEntity.setSms_user_id(expertInfo.getUser_id());
					smsRecordEntity.setSms_user_name(expertInfo.getUser_name());
					smsRecordEntity.setSms_content(msg[1]);
					smsRecordEntity.setSms_mobile(msg[0]);
					smsRecordEntity.setDecimationBatch(project.getDecimationBatch());
					smsRecordEntity.setSms_type(SysConstants.SMS_TYPE.SMS_FOUR);
					smsRecordEntity.setSms_variety(SysConstants.CONDITION_CURRENT_STATUS.METHOD_THREE);
					smsRecordEntity.setSms_time(new Date());
					smsRecordEntity.setChange_result(SysConstants.CHANGE_RESULT.RESULT_1);
					// smsRecordService.insertSmsRecord(smsRecordEntity);
					// String
					// result_mt=SMSNewUtil.sendMsgByDiffContent(msgList);
					MWSmsUtil util = new MWSmsUtil();

					System.out.println("进入发送短信接口");
					int result = util.singleSend(userId, userPwd, masterIpAddress, ipAddress1, ipAddress2,
							smsRecordEntity);
					System.out.println("进入发送短信接口");
					smsRecordService.insertSmsRecord(smsRecordEntity);
					if (result != 0) {
						System.out.println("发送短信失败，请及时处理！");
					}
				}

			}
		} catch (Exception e) {
			// 异常处理
			e.printStackTrace();
		}
		return smsRecordEntity;
	}



	/**
	 * 项目变更，发送短信接口
	 * @param userId 			短信账号
	 * @param userPwd			短信密码
	 * @param masterIpAddress   发送短信的IP
	 * @param ipAddress1	          发送短信的IP
	 * @param ipAddress2		发送短信的IP
	 * @param projectEntity
	 * @param entityList
	 * @param conEntity
	 */
	public void changeProjectSmsExperts(ProjectEntity projectEntity,List<ExpertInfoEntity> entityList, ConditionEntity conEntity) {
		SmsRecordEntity smsRecordEntity = new SmsRecordEntity();
		try {
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			//判断满足条件的专家是否为空
			if(entityList!=null && entityList.size()>0){
				/*
				 * "您好，请于【开标时间】 前到江苏省国际招标公司（南京市西康路7号）【开标地点】会议室参加招标采购项目评审，联系人：【项目负责人】"
				 * +
				 * "联系电话：【电话】，时间预计半天结束，感谢您对招标采购工作的支持。请专家带好身份证去参加评审。项目识别码为：【批次号】，请牢记该码，"
				 * + "在请假等特殊情况时需要使用!"
				 */
				String smsStr = conEntity.getSmsContent();
				smsStr = smsStr.replaceAll("【批次号】", projectEntity.getDecimationBatch());
				smsStr = smsStr.replaceAll("【项目名称】", projectEntity.getProjectName());
				smsStr = smsStr.replaceAll("【项目编号】", projectEntity.getProjectNo());
				smsStr = smsStr.replaceAll("【开标时间】", sdf.format(projectEntity.getBidTime()));
				smsStr = smsStr.replaceAll("【开标地点】", projectEntity.getBidAddress());
				smsStr = smsStr.replaceAll("【项目负责人】",projectEntity.getManager());
				smsStr = smsStr.replaceAll("【电话】", projectEntity.getPhone());
				//循环发送短信
				for(ExpertInfoEntity entity : entityList){
					smsStr = entity.getUser_name() + "专家，您好！" + smsStr + "感谢您对招标采购工作的支持。";
					// 编辑短信内容
					List<String[]> msgList = new ArrayList<String[]>();
					String[] msg = new String[3];
					// 手机号
					msg[0] = entity.getMobilephone();
					// 短信内容
					msg[1] = smsStr;
					// expertInfo.getUser_name() + "您好，江苏省国际招标公司诚邀您于" +
					// sdf.format(projectList.get(0).getBidTime()) +
					// "在"+projectList.get(0).getBidAddress()+"参加评标。";
					msgList.add(msg);

					// XXX您好，江苏省国际招标公司诚邀您于【开标时间】在【开标地点】参加评标，回复：1-参加，2-不参加。以短信通知为准。
					// 记录短信
					// SmsRecordEntity smsRecordEntity = new SmsRecordEntity();
					smsRecordEntity.setSms_id(CommUtil.getKey());
					smsRecordEntity.setProject_id(projectEntity.getProjectId());
					smsRecordEntity.setDecimationBatch(projectEntity.getDecimationBatch());
					smsRecordEntity.setSms_user_id(entity.getUser_id());
					smsRecordEntity.setSms_user_name(entity.getUser_name());
					smsRecordEntity.setSms_content(msg[1]);
					smsRecordEntity.setSms_mobile(msg[0]);
					smsRecordEntity.setSms_type(SysConstants.SMS_TYPE.SMS_FOUR);
					smsRecordEntity.setSms_variety(SysConstants.CONDITION_CURRENT_STATUS.METHOD_THREE);
					smsRecordEntity.setSms_time(new Date());
					if(String.valueOf(SysConstants.TEMPLATE_TYPE.TYPE_FOUR).equals(conEntity.getTemplateType())){
						smsRecordEntity.setChange_result(SysConstants.CHANGE_RESULT.RESULT_2);
					}
					if(String.valueOf(SysConstants.TEMPLATE_TYPE.TYPE_FIVE).equals(conEntity.getTemplateType())){
						smsRecordEntity.setChange_result(SysConstants.CHANGE_RESULT.RESULT_3);
					}
					MWSmsUtil util = new MWSmsUtil();
					int result = util.singleSend(userId, userPwd, masterIpAddress, ipAddress1, ipAddress2,
							smsRecordEntity);

					smsRecordService.insertSmsRecord(smsRecordEntity);
					if (result != 0) {
						System.out.println("发送短信失败，请及时处理！");
					}
				}
			}
		} catch (Exception e) {
			// 异常处理
			e.printStackTrace();
		}
	}
	/**
	 * 从符合条件的专家结果中随机抽取需要的专家
	 *
	 * @param srclist
	 * @param total
	 * @return
	 */
	private List<ExpertInfoEntity> getRandomExpertsFromResult(List<ExpertInfoEntity> srclist, int total) {
		List<ExpertInfoEntity> desList = null;
		// 抽取时当库里专家人数少用需要提供的专家人数时，将显示全部专家
		if (srclist != null && srclist.size() > 0 && total != 0 && srclist.size() < total) {
			desList = srclist;
		}
		if (srclist != null && total != 0 && srclist.size() >= total) {
			desList = new ArrayList<ExpertInfoEntity>();// 随机获得后的结果
			int i = 0;// 被随机选中的专家数
			while (i < total) {
				int random = (int) ((Math.random() * 100) % srclist.size());
				ExpertInfoEntity ex = srclist.get(random);
				if (ex == null) {// 增加选中的概率
					while (ex == null) {// 没中
						if (++random == srclist.size()) {// 取后一个
							random = 0;// 记录索引，
						}
						ex = srclist.get(random);
					}
				}
				if (ex != null) {
					desList.add(ex);//
					srclist.set(random, null);// 置空已被选择的对象
					i++;
				}
			}
		}
		return desList;
	}

	/**
	 * 抽取条件是否已存在抽取结果记录
	 *
	 * @param conEntity
	 * @return
	 */
	public boolean isExistsExtractResult(ConditionEntity conEntity) {
		if (mapper.queryExtractedExperts(conEntity) != null) {
			return true;
		}
		return false;
	}

	/**
	 * 查询抽取结果的最大批次
	 *
	 * @param conEntity
	 * @return
	 */
	public Long queryResultSort(ConditionEntity conEntity) {
		return mapper.queryResultSort(conEntity);
	}

	/**
	 * 更新项目抽取状态
	 *
	 * @param conEntity
	 */
	public void saveExtractResult(ConditionEntity conEntity) {
		List<ResultEntity> list = mapper.queryAgreeFromResult(conEntity);
		int agree = list != null ? list.size() : 0;// 同意参加人数
		ConditionEntity con = mapper.queryConditionById(conEntity);
		int total = con.getTotal().intValue();// 总人数
		ProjectEntity project = new ProjectEntity();
		project.setProjectId(con.getProjectId());
		// 根据项目ID查询抽取批次号
		ProjectEntity newProject = new ProjectEntity();
		newProject = mapper.queryProjectById(project);
		// 根据抽取批次修改状态
		if (agree == total) {
			newProject.setStatus(3L);// 抽取完成
		} else {
			newProject.setStatus(2L);// 抽取中
		}
		mapper.updateProjectStatus(newProject);
	}

	/**
	 * 项目列表信息
	 *
	 * @param entity
	 * @return
	 */
	public List<ProjectEntity> queryPageProjectList(ProjectEntity entity, UserEntity currentUser) {


		List<ProjectEntity> list = mapper.queryPageProjectList(entity);

		// 判断是否是已抽取项目
		if (SysConstants.PROJECT_STATUS.ALREADY == entity.getStatus()
				|| SysConstants.PROJECT_STATUS.APPOINT_ALREADY == entity.getStatus()) {
			// 已抽取项目
			for (ProjectEntity project : list) {
				// 查询项目最后抽取时间
				ResultEntity resultEn = mapper.queryCallTime(project);
				Date lastExtractTime = resultEn.getCallTime();
				if (null == project.getMethod()) {
					project.setIsFillPump(1);
				} else {
					// 查询抽取专家总人数
					ConditionEntity condition = new ConditionEntity();
					condition.setId(project.getConditionId());
					Integer count = mapper.queryAgreeExpertCount(condition);
					// 判断不是当前抽取人创建的项目且已抽取人数满足条件则不可补抽
					if (!currentUser.getUser_id().equals(project.getCreate_id())
							&& count == project.getAppNum().intValue()) {
						continue;
					}
					// 短信通知时需在最后抽取时间6分钟之后才可补抽专家
					if (project.getMethod().equals(2L)) {
						// 无抽取记录则补抽专家
						if (null == lastExtractTime) {
							project.setIsFillPump(0);
							continue;
						}
						Calendar lastExtractCal = Calendar.getInstance();
						lastExtractCal.setTime(lastExtractTime);
						lastExtractCal.add(Calendar.MINUTE, 6);// 日期加5分钟
						lastExtractTime = lastExtractCal.getTime();
						Date currentTime = new Date();
						// 判断项目最后抽取时间6分钟之后是否大于等于当前时间
						if (0 >= lastExtractTime.compareTo(currentTime)) {
							project.setIsFillPump(0);
						}
					} else if (project.getMethod().equals(1L)) {
						project.setIsFillPump(0);
					}
				}
			}
		}
		//循环判断用于区分是否存在未通知的

		return list;
	}

	/**
	 * 函数功能描述：根据流水号等条件查询项目信息
	 *
	 * @param entity
	 * @param currentUser
	 * @return
	 */
	public List<ProjectEntity> queryProjectList(ProjectEntity entity, UserEntity currentUser) {
		List<ProjectEntity> list = mapper.queryProjectList(entity);

		// 判断是否是已抽取项目
		if (SysConstants.PROJECT_STATUS.ALREADY == entity.getStatus()
				|| SysConstants.PROJECT_STATUS.APPOINT_ALREADY == entity.getStatus()) {
			// 已抽取项目
			for (ProjectEntity project : list) {
				// 查询项目最后抽取时间
				ResultEntity resultEn = mapper.queryCallTime(project);
				Date lastExtractTime = resultEn.getCallTime();
				if (null == project.getMethod()) {
					project.setIsFillPump(1);
				} else {
					// 查询抽取专家总人数
					ConditionEntity condition = new ConditionEntity();
					condition.setId(project.getConditionId());
					Integer count = mapper.queryAgreeExpertCount(condition);
					// 判断不是当前抽取人创建的项目且已抽取人数满足条件则不可补抽
					if (!currentUser.getUser_id().equals(project.getCreate_id())
							&& count == project.getAppNum().intValue()) {
						continue;
					}
					// 短信通知时需在最后抽取时间6分钟之后才可补抽专家
					if (project.getMethod().equals(2L)) {
						// 无抽取记录则补抽专家
						if (null == lastExtractTime) {
							project.setIsFillPump(0);
							continue;
						}
						Calendar lastExtractCal = Calendar.getInstance();
						lastExtractCal.setTime(lastExtractTime);
						lastExtractCal.add(Calendar.MINUTE, 6);// 日期加5分钟
						lastExtractTime = lastExtractCal.getTime();
						Date currentTime = new Date();
						// 判断项目最后抽取时间6分钟之后是否大于等于当前时间
						if (0 >= lastExtractTime.compareTo(currentTime)) {
							project.setIsFillPump(0);
						}
					} else if (project.getMethod().equals(1L)) {
						project.setIsFillPump(0);
					}
				}
			}
		}
		return list;
	}

	/**
	 * 异常项目列表信息
	 *
	 * @param entity
	 * @return
	 */
	public List<ProjectEntity> queryPageByErrorProjectList(ProjectEntity entity, UserEntity currentUser) {
		List<ProjectEntity> list = mapper.queryPageByErrorProjectList(entity);
		return list;
	}

	/**
	 * 异常项目列表信息
	 *
	 * @param entity
	 * @return
	 */
	public List<ProjectEntity> queryErrorProjectList2(ProjectEntity entity, UserEntity currentUser) {
		List<ProjectEntity> list = mapper.queryErrorProjectList2(entity);
		return list;
	}

	/**
	 * 查询抽取条件信息
	 *
	 * @param entity
	 * @return
	 */
	public List<ConditionEntity> queryConditionList(ConditionEntity entity) {
		return mapper.queryConditionList(entity);
	}

	/**
	 * 查询抽取专家结果信息
	 *
	 * @param entity
	 * @return
	 */
	public List<ExpertInfoEntity> queryExtractedResult(ConditionEntity entity) {
		
	List<ExpertInfoEntity> listInfo = mapper.queryExtractedResult(entity);
		
		HashMap<String,ExpertInfoEntity> tempMap = new HashMap<String,ExpertInfoEntity>();
		//去掉重复的key
		for(ExpertInfoEntity expertInfo : listInfo){
			String temp = expertInfo.getUser_id();
			if(tempMap.containsKey(temp)){
				//合并相同key的value
				expertInfo.setTitalName((tempMap.get(temp).getTitalName() +"," + expertInfo.getTitalName()));
				//HashMap不允许key重复，当有key重复时，前面key对应的value值会被覆盖
				tempMap.put(temp,expertInfo);
				System.out.println(temp + "," + expertInfo.getJoin_status());
			}else{
				tempMap.put(temp,expertInfo );
				System.out.println(temp + "##" + expertInfo.getJoin_status());
			}
		}
		//去除重复key的list
		List<ExpertInfoEntity> newList = new ArrayList<ExpertInfoEntity>();
	        for(String temp:tempMap.keySet()){
	            newList.add(tempMap.get(temp));
	        }
		
		return newList;
	}

	/**
	 * 查询抽取记录信息
	 *
	 * @param projectId
	 * @return
	 */
	public List<ExtractRecordEntity> queryExtractRecordList(String projectId) {
		// 查询抽取记录信息
		List<ExtractRecordEntity> exRcordList = mapper.copyQueryExtractRecordList(projectId);

		for (int i = 0; i < exRcordList.size(); i++) {
			ExtractRecordEntity ex = exRcordList.get(i);

			ConditionEntity con = new ConditionEntity();
			con.setProjectId(projectId);
			con.setId(ex.getConId());
			// 修改2015-4-13
			if (0L == ex.getSort()) {
				con.setNum(ex.getSort());
			} else {
				con.setNum(Long.valueOf(i + 1));
			}

			// 查询参评专家信息
			List<ExpertInfoEntity> expertList = mapper.queryExtractedResult(con);
			ex.setExpertList(expertList);

			// 根据抽取条件查询抽取条件信息
			ConditionEntity conEntity = mapper.queryExtractConditionById(con);
			conEntity.setNum(con.getNum());
			ex.setConditionEntity(conEntity);
		}

		return exRcordList;
	}

	/**
	 * 查询抽取记录信息(拷贝上面的方法只为了解决业务员可以查看抽取的全部专家问题)
	 *
	 * @param projectId
	 * @return
	 */
	public List<ExtractRecordEntity> copyQueryExtractRecordList(String projectId) {
		// 查询抽取记录信息
		Long curr = System.currentTimeMillis();
		List<ExtractRecordEntity> exRcordList = mapper.queryExtractRecordList(projectId);
		for (int i = 0; i < exRcordList.size(); i++) {
			ExtractRecordEntity ex = exRcordList.get(i);
			ConditionEntity con = new ConditionEntity();
			con.setProjectId(projectId);
			con.setId(ex.getConId());
			//是否暂停评标 0:否；1：是
			con.setIsBid(0);
			// 查询参评专家信息
			List<ExpertInfoEntity> expertList = mapper.queryExtractedResult(con);
			ex.setExpertList(expertList);

			// 根据抽取条件查询抽取条件信息
			ConditionEntity conEntity = mapper.queryExtractConditionById(con);
			conEntity.setNum(con.getNum());
			ex.setConditionEntity(conEntity);
		}
		return exRcordList;
	}

	/**
	 * 保存申请信息
	 *
	 * @param apply
	 */
	@Transactional
	public void saveApplyInfo(ApplyRecordEntity apply, UserEntity user) {
		apply.setId(CommUtil.getKey());
		apply.setType(1L);// 指定抽取申请
		mapper.saveApplyInfo(apply);
		ProjectEntity project = new ProjectEntity();
		project.setProjectId(apply.getProjectId());
		if (SysConstants.ROLE.SALESMAN.equals(user.getRole_name())
				|| SysConstants.ROLE.EXTRACT.equals(user.getRole_name())) {
			project.setStatus(4L);
		} else {
			project.setStatus(5L);
		}
		mapper.updateProjectStatus(project);
	}

	/**
	 * 保存申请信息(拷贝上面的代码，用于多标段的指定专家的申请)
	 *
	 * @param apply
	 */
	@Transactional
	public void copySaveApplyInfo(ApplyRecordEntity apply, UserEntity user, List<AppointsExpertsEntity> appList) {
		if (apply.getDecimationBatch() == null || apply.getDecimationBatch() == "") {
			throw new HZWException("抽取时数据有误，请联系管理员！");
		}
		ProjectEntity projectEntity = new ProjectEntity();
		// 根据流水号查询项目
		projectEntity.setDecimationBatch(apply.getDecimationBatch());
		List<ProjectEntity> pList = mapper.queryProjectListById(projectEntity);

		for (ProjectEntity pe : pList) {
			apply.setId(CommUtil.getKey());
			apply.setType(1L);// 指定抽取申请
			apply.setProjectId(pe.getProjectId());
			mapper.saveApplyInfo(apply);
			// 通过项目编号查询项目抽取条件
			ProjectEntity entity = mapper.queryConditionIdByProjectId(pe);
			if (entity != null) {
				ConditionEntity cEntity = new ConditionEntity();
				cEntity.setMethod(4L);
				cEntity.setProjectId(pe.getProjectId());
				mapper.updateConditionMethod(cEntity);
			}

		}

		ProjectEntity project = new ProjectEntity();
		// project.setProjectId(apply.getProjectId());
		project.setDecimationBatch(apply.getDecimationBatch());
		if (SysConstants.ROLE.SALESMAN.equals(user.getRole_name())
				|| SysConstants.ROLE.EXTRACT.equals(user.getRole_name())) {
			project.setStatus(4L);
		} else {
			project.setStatus(5L);
		}
		if (appList != null && !appList.isEmpty()) {
			// 处理指定专家
			appointsExpertsMapper.saveInfoList(appList);

		}
		mapper.updateProjectStatus(project);
	}

	/**
	 * 专家抽取审核---主任审核
	 *
	 * @param user
	 * @param apply
	 */
	@Transactional
	public int saveAuditByDirector(UserEntity user, ProjectEntity project, String applyReason) {
		if (SysConstants.ROLE.DIRECTOR.equals(user.getRole_name())) {
			project.setStatus(91L);
		} else {
			project.setStatus(90L);
			project.setApplyReason(applyReason);
		}
		return mapper.updateProjectStatus(project);
	}

	/**
	 * 查询指定专家
	 *
	 * @param expert
	 */
	public List<ExpertInfoEntity> queryPagePointExperts(ConditionEntity conEntity) {
		Date bidTime = mapper.queryProjectInfoById(conEntity.getProjectId()).getBidTime();// 开标时间
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(bidTime);
		calendar.add(Calendar.DAY_OF_MONTH, -20);// 开标时间后退20天
		conEntity.setStartBidTime(calendar.getTime());
		calendar.setTime(bidTime);
		calendar.add(Calendar.DAY_OF_MONTH, 20);// 开标时间未来20天
		conEntity.setEndBidTime(calendar.getTime());

		return mapper.queryPagePointExperts(conEntity);
	}

	/**
	 * 根据专家身份证查询专家信息
	 *
	 * @param conEntity
	 * @return
	 */
	public List<ExpertInfoEntity> queryExperts(ConditionEntity conEntity) {
		return mapper.queryPagePointExperts(conEntity);
	}

	/**
	 * 保存指定专家信息
	 *
	 * @param result
	 */
	public void savePointExperts(ResultEntity result) {
		List<ResultEntity> list = new ArrayList<ResultEntity>();
		String[] expertIds = result.getUserId().split(",");
		if (expertIds.length > 0) {
			for (int i = 0; i < expertIds.length; i++) {
				ResultEntity e = new ResultEntity();
				e.setId(CommUtil.getKey());
				e.setUserId(expertIds[i]);
				e.setConditionId(result.getConditionId());
				e.setSort(0L);// 批次
				e.setJoinStatus(0L);// 默认参加
				e.setExtractTime(new Date());
				list.add(e);
			}
			mapper.saveExtractedExpert(list);
		}
	}

	/**
	 * 查询指定专家已抽取的专家
	 *
	 * @param conEntity
	 * @return
	 */
	public List<ResultEntity> queryPointedExperts(ConditionEntity conEntity) {
		return mapper.queryExtractedExperts(conEntity);
	}

	/**
	 * 校验当前的专家是否已经参标
	 * @param bidTime 开标时间
	 * @param idNo    专家身份证
	 * @return
	 */
	public ResultEntity checkExpertByBidTime(String bidTime,String idNo){
		return mapper.checkExpertByBidTime(bidTime,idNo);
	}
	/**
	 * 根据流水号查询抽取条件ID
	 *
	 * @param ProjectEntity
	 * @return
	 */
	public List<ProjectEntity> queryConditionIdByBatch(ProjectEntity ProjectEntity) {
		return mapper.queryConditionIdByBatch(ProjectEntity);
	}

	/**
	 * 删除指定专家抽取结果
	 *
	 * @param result
	 */
	public void deletePointResult(ResultEntity result) {
		mapper.deletePointResult(result);
	}

	/**
	 * 保存再次抽取申请信息
	 *
	 * @param apply
	 */
	@Transactional
	public void saveApplyAgainExtract(ApplyRecordEntity apply) {
		apply.setId(CommUtil.getKey());
		apply.setType(2L);// 3次后再次抽取
		mapper.saveApplyInfo(apply);
		ProjectEntity project = new ProjectEntity();
		project.setProjectId(apply.getProjectId());
		project.setStatus(7L);// 3次后待审核
		mapper.updateProjectStatus(project);
	}

	/**
	 * 提交指定专家结果
	 *
	 * @param result
	 */
	public void submitPointResult(ResultEntity result) {
		// 暂无操作
		List<ResultEntity> resultList = new ArrayList<ResultEntity>();
		resultList.add(result);
		mapper.saveExtractedExpert(resultList);
		 //mapper.submitPointResult(result);
		ProjectEntity project = new ProjectEntity();
		project.setStatus(10L);             // 10：指定专家已抽取
		project.setProjectId(result.getProjectId());
		mapper.updateProjectStatus(project);
	}

	/**
	 * 专家参与项目列表查询
	 *
	 * @param expert_id
	 * @return
	 */
	public List<ProjectEntity> queryPageExpertJoinProjectList(ProjectEntity colProject) {
		return mapper.queryPageExpertJoinProjectList(colProject);
	}

	/**
	 * 短信方式发送信息给抽取的专家
	 *
	 * @param conEntity
	 */
	@Transactional
	public void sendMessageForExperts(ConditionEntity conEntity, List<ExpertInfoEntity> experts) {
		// List<ExpertInfoEntity> experts=this.queryExpertsByRule(conEntity);
		ProjectEntity project = new ProjectEntity();
		project.setProjectId(conEntity.getProjectId());//
		project = mapper.queryProjectById(project);// 查询项目信息
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		SimpleDateFormat sdf1 = new SimpleDateFormat("yyyyMMddHHmmss");
		List<String[]> msgList = new ArrayList<String[]>();
		if (null == conEntity.getSmsStr()) {
			if ("2".equals(conEntity.getMethod().toString())) {// 短信抽取
				conEntity.setSmsStr("您好：XXX，江苏省招标中心有限公司诚邀您参加【" + project.getProjectName() + "】评标，开标时间："
						+ sdf.format(project.getBidTime()) + "。回复：1-参加，2-不参加。");
			} else {// 正常抽取
				conEntity.setSmsStr("您好：XXX，江苏省招标中心有限公司诚邀您参加【" + project.getProjectName() + "】评标，开标时间："
						+ sdf.format(project.getBidTime()) + ",开标地点：" + project.getBidAddress() + ",项目负责人："
						+ project.getManager() + ",电话：" + project.getPhone() + "");
			}
		}
		// 拼接短信内容
		List<ResultEntity> resultList = new ArrayList<ResultEntity>();// 通知抽取结果
		List<SmsRecordEntity> smsRecordList = new ArrayList<SmsRecordEntity>();
		Date callTime = new Date();// 通知时间

		for (ExpertInfoEntity e : experts) {
			// 兼容接口抽取专家
			if ((null != e && null != conEntity.getSourceCode() && conEntity.getSourceCode().equals("szyc"))
					|| (null != e && null != e.getJoin_status() && 0L == e.getJoin_status())
					|| (null != e && null != e.getJoin_status() && 3L == e.getJoin_status())) {
				String[] msg = new String[3];
				// 手机号
				msg[0] = e.getMobilephone();
				// 短信内容
				msg[1] = conEntity.getSmsStr().replaceAll("XXX", e.getUser_name()) + "【省交易中心】";
				msgList.add(msg);
				ResultEntity result = new ResultEntity();
				result.setConditionId(conEntity.getId());
				result.setUserId(e.getUser_id());
				result.setCallTime(callTime);
				result.setJoinStatus(3L);// 短信已通知
				result.setMessageNo(sdf1.format(callTime));// 短信码由通知时间+扩展码
				resultList.add(result);
				// 记录短信
				SmsRecordEntity smsRecordEntity = new SmsRecordEntity();
				smsRecordEntity.setSms_id(CommUtil.getKey());
				smsRecordEntity.setProject_id(project.getProjectId());
				smsRecordEntity.setSms_user_id(e.getUser_id());
				smsRecordEntity.setSms_user_name(e.getUser_name());
				smsRecordEntity.setSms_content(msg[1]);
				smsRecordEntity.setSms_mobile(msg[0]);
				smsRecordEntity.setSms_type("2".equals(conEntity.getMethod().toString()) ? SysConstants.SMS_TYPE.SMS_ONE
						: SysConstants.SMS_TYPE.SMS_FOUR);
				smsRecordEntity.setSms_time(callTime);
				smsRecordList.add(smsRecordEntity);
			}
		}
		// 批量添加短信记录
		if (!smsRecordList.isEmpty()) {
			smsRecordService.insertSmsRecordList(smsRecordList);
		}
		String result_mt = SMSNewUtil.sendMsgByDiffContent(msgList);
		// String result_mt= "123456789012345678";
		if (result_mt.startsWith("-") || result_mt.equals("")) {// 发送短信，如果是以负号开头就是发送失败。
			System.out.print("发送失败！返回值为：" + result_mt + "请查看webservice返回值对照表");
			return;
		}
		// 输出返回标识，为小于19位的正数，String类型的。记录您发送的批次。
		else {
			if ("2".equals(conEntity.getMethod().toString())) {// 短信抽取
				mapper.batchUpdateExtractedExpert(resultList);// 批量更新短信通知结果
				project.setStatus(3L);// 已抽取
				mapper.updateProjectStatus(project);
				new Thread(new ReceiveMsgTimerRunnable(1 * 60 * 1000L, mapper)).start();// 启动收信息线程
				System.out.print("发送成功，返回值为：" + result_mt);
			}
		}
	}

	/**
	 * 短信方式发送信息给抽取的专家[定时器专用]
	 *
	 * @param conEntity
	 */
	/*
	 * @Transactional public List<ResultEntity>
	 * sendMessageUseTimerForExperts(ProjectEntity projectEntity,ConditionEntity
	 * conEntity, List<ExpertInfoEntity> experts) {
	 *
	 * //获取随机的短信批次 String messageNo = checkMessageNo();
	 *
	 * SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
	 * List<MultiMt> multiMtList=new ArrayList<MultiMt>(); //根据版本ID获取版本信息
	 * TemplateEntity templateEntity = new TemplateEntity();
	 * templateEntity.setTemplateId(conEntity.getTemplateId()); templateEntity =
	 * templateService.queryTemplateById(templateEntity);
	 * conEntity.setSmsStr(templateEntity.getTemplateContent());
	 *//**
		 * 您好：XXX，江苏省招标中心有限公司诚邀您参加评标， 批次号：[批次号]([项目名称])，开标时间：[开标时间]，
		 * 开标地点：[开标地点]，项目负责人：[项目负责人]，电话：[电话] 回复：1-参加，2-不参加。
		 *//*
		 * //拼接短信内容 String smsStr = templateEntity.getTemplateContent();
		 * smsStr.replaceAll("[批次号]", projectEntity.getDecimationBatch());
		 * smsStr.replaceAll("[项目名称]", projectEntity.getProjectName());
		 * smsStr.replaceAll("[项目编号]", projectEntity.getProjectNo());
		 * smsStr.replaceAll("[开标时间]", sdf.format(projectEntity.getBidTime()));
		 * smsStr.replaceAll("[开标地点]", projectEntity.getBidAddress());
		 * smsStr.replaceAll("[项目负责人]", projectEntity.getManager());
		 * smsStr.replaceAll("[电话]", projectEntity.getPhone());
		 *
		 * List<ResultEntity> resultList = new
		 * ArrayList<ResultEntity>();//通知抽取结果 List<SmsRecordEntity>
		 * smsRecordList = new ArrayList<SmsRecordEntity>(); Date callTime=new
		 * Date();//通知时间 for(ExpertInfoEntity e:experts){ //兼容接口抽取专家 if ((null
		 * != e && null != conEntity.getSourceCode() &&
		 * conEntity.getSourceCode().equals("szyc")) || (null != e && null !=
		 * e.getJoin_status() && 4L == e.getJoin_status())) { //发送短信实体类 MultiMt
		 * multiMt=new MultiMt(); //手机号 multiMt.setMobile(e.getMobilephone());
		 * //短信内容 multiMt.setContent(conEntity.getSmsStr().replaceAll("XXX",
		 * e.getUser_name()) + "【省交易中心】"); //短信扩展码 multiMt.setExno(messageNo);
		 * multiMtList.add(multiMt);
		 *
		 * //专家抽取结果 ResultEntity result=new ResultEntity();
		 * result.setConditionId(conEntity.getId());
		 * result.setUserId(e.getUser_id()); result.setCallTime(callTime);
		 * result.setJoinStatus(3L);//短信已通知
		 * result.setMessageNo(messageNo);//短信批次 resultList.add(result);
		 *
		 * // 记录短信 SmsRecordEntity smsRecordEntity = new SmsRecordEntity();
		 * smsRecordEntity.setSms_id(CommUtil.getKey());
		 * smsRecordEntity.setDecimationBatch(projectEntity.getDecimationBatch()
		 * ); smsRecordEntity.setSms_user_id(e.getUser_id());
		 * smsRecordEntity.setSms_user_name(e.getUser_name());
		 * smsRecordEntity.setSms_content(multiMt.getContent());
		 * smsRecordEntity.setSms_mobile(multiMt.getMobile());
		 * smsRecordEntity.setSms_type("2".equals(conEntity.getMethod().toString
		 * ()) ? SysConstants.SMS_TYPE.SMS_ONE :
		 * SysConstants.SMS_TYPE.SMS_FOUR);
		 * smsRecordEntity.setSms_time(callTime);
		 * smsRecordList.add(smsRecordEntity); } } // 批量添加短信记录 if
		 * (!smsRecordList.isEmpty()) {
		 * smsRecordService.insertSmsRecordList(smsRecordList); } int result =
		 * MSMSClient.multiSend(multiMtList); //String result_mt=
		 * "123456789012345678"; if(result<0){//发送短信，如果是以负号开头就是发送失败。
		 * System.out.print("发送失败！返回值为："+result+"请查看webservice返回值对照表"); //return
		 * ; } return resultList; }
		 */

	/**
	 * 批量修改抽取专家结果
	 *
	 * @param resultList
	 * @param project
	 */
	@Transactional
	public String submitResult(List<ResultEntity> resultList, ConditionEntity condition) {
		String msg = "success";
		if (!resultList.isEmpty()) {
			Date callTime = new Date();
			// 根据流水号查询项目和抽取条件ID
			ProjectEntity entity = new ProjectEntity();
			entity.setDecimationBatch(condition.getDecimationBatch());
			List<ProjectEntity> list = mapper.queryConditionIdByBatch(entity);
			// 循环赋值
			for (ProjectEntity projectEntity : list) {
				for (ResultEntity result : resultList) {
					if (result.getMessageNo() == null) {
						result.setMessageNo("");
					}
					result.setCallTime(callTime);
					result.setConditionId(projectEntity.getConditionId());
					// 判断专家是否参加
					if (result.getJoinStatus().toString().equals("1")) {
						// 不参加判断是否填写理由
						if (result.getReason().isEmpty() || result.getReason() == null) {
							throw new HZWException(
									result.getExpert().getUser_name() + MessageConstants.NOT_ENOUGH_EXPERT);
						}
					}

				}
				mapper.batchUpdateExtractedExpert(resultList);// 批量更新短信通知结果
			}
			if (condition.getTotal().equals(condition.getJoinNum())) {
				ProjectEntity project = new ProjectEntity();
				project.setDecimationBatch(condition.getDecimationBatch());
				project.setStatus(3L);// 已抽取
				mapper.updateProjectStatus(project);
			} else {
				ProjectEntity project = new ProjectEntity();
				project.setDecimationBatch(condition.getDecimationBatch());
				// 判断再抽取的人数是否满足
				if (!getPeople(condition).equals("success")) {
					project.setStatus(20L);// 再次抽取（人数不足）
					msg = "符合条件的专家人数不足！请添加专家类别";
				} else {
					project.setStatus(2L);// 再次抽取
				}
				mapper.updateProjectStatus(project);
				/*
				 * if(condition.getMethod().longValue()==SysConstants.
				 * CONDITION_METHOD.METHOD_THREE){ //判断当前时间是否是开标2个小时之后
				 * //判断当前项目是否是半个小时以内 Calendar calendar = Calendar.getInstance();
				 * long currentTime = calendar.getTimeInMillis()+30*60*1000;
				 * Date date = new Date(currentTime); //获取当前时间 Calendar cal =
				 * Calendar.getInstance(); //在当前时间上减去2小时 cal.add(Calendar.HOUR,
				 * -2); if(list.get(0).getBidTime().before(cal.getTime())){ msg=
				 * "approve"; }else{ voiceService.disposeSendExpert(condition);
				 * }
				 *
				 * }
				 */
			}
		}
		return msg;
	}

	/**
	 * 批量修改抽取专家结果(语音)
	 *
	 * @param resultList
	 * @param project
	 */
	@Transactional
	public String submitResultToVoice(List<ResultEntity> resultList, ConditionEntity condition) {
		String msg = "success";
		if (!resultList.isEmpty()) {
			Date callTime = new Date();
			// 根据流水号查询项目和抽取条件ID
			ProjectEntity entity = new ProjectEntity();
			entity.setDecimationBatch(condition.getDecimationBatch());
			List<ProjectEntity> list = mapper.queryConditionIdByBatch(entity);
			// 循环赋值
			for (ProjectEntity projectEntity : list) {
				for (ResultEntity result : resultList) {
					if (result.getMessageNo() == null) {
						result.setMessageNo("");
					}
					result.setCallTime(callTime);
					result.setConditionId(projectEntity.getConditionId());
					// 判断专家是否参加
					if (result.getJoinStatus().toString().equals("1")) {
						// 不参加判断是否填写理由
						if (result.getReason().isEmpty() || result.getReason() == null) {
							throw new HZWException(
									result.getExpert().getUser_name() + MessageConstants.NOT_ENOUGH_EXPERT);
						}
					}

				}
				List<ResultEntity> lt = cleanExtractionRecords(resultList,projectEntity.getConditionId());
				if(lt!=null&&lt.size()>0) {
				mapper.batchUpdateExtractedExpert(lt);// 批量更新短信通知结果
				}
			}
			if (condition.getTotal().equals(condition.getJoinNum())) {
				ProjectEntity project = new ProjectEntity();
				project.setDecimationBatch(condition.getDecimationBatch());
				project.setStatus(3L);// 已抽取
				mapper.updateProjectStatus(project);
				msg = "success";
			} else {
				ProjectEntity project = new ProjectEntity();
				project.setDecimationBatch(condition.getDecimationBatch());
				// 判断再抽取的人数是否满足
				if (!getPeople(condition).equals("success")) {
					project.setStatus(20L);// 再次抽取（人数不足）
					// 继续抽取
					msg = "lackExperts";

				} else {
					project.setStatus(2L);// 再次抽取
					// 继续抽取
					msg = "continue";
				}
				mapper.updateProjectStatus(project);
			}
		}
		return msg;
	}
	private List<ResultEntity> cleanExtractionRecords(List<ResultEntity> resultList, String conditionId) {
			//根据抽取条件查询所有专家
			 List<ParamTypeEntity> list = mapper.queryConditionId(conditionId);
			 List<ResultEntity> lt = new ArrayList<ResultEntity>();
			 for (ParamTypeEntity param : list) {
				for (ResultEntity param2 : resultList) {
					if(param.getExpertId().equals(param2.getUserId())) {
						boolean flag =false;
						if(!param.getJoinStatus().equals(String.valueOf(param2.getJoinStatus())==null?"1":String.valueOf(param2.getJoinStatus()))) {
							flag= true;
						}
						if(!param.getReason().equals(param2.getReason()==null?"1":param2.getReason())) {
							flag= true;
						}
						
						if(!param.getQtReason().equals(null==param2.getQt_reason()?"1":"".equals(param2.getQt_reason())?"1":param2.getQt_reason())) {
							flag= true;
						}
						if(flag) {
							lt.add(param2);
						}
					}
					
				}
			}
		
		return lt;
	}

	/**
	 * 判断专家是否在开标当前参加过其他项目(用于业务员页面的提交按钮)
	 * 函数功能描述：TODO
	 * @param condition
	 * @return
	 */
	public List<ResultEntity> queryNotNoticeExpert(List<ResultEntity> resultList,ConditionEntity condition) {
		//获取所有的参加专家
		ProjectEntity entity = new ProjectEntity();
		entity.setDecimationBatch(condition.getDecimationBatch());
		List<ProjectEntity> projectList = mapper.queryProjectByDecimationBatch(entity);
		List<ResultEntity> attendList = new ArrayList<ResultEntity>();
		for(ResultEntity result : resultList) {
			if(result.getJoinStatus().equals(SysConstants.JOIN_STATUS.ATTEND)) {
				//attendList.add(result);
				result.setDecimationBatch(condition.getDecimationBatch());
				result.setBidTime(projectList.get(0).getBidTime());
				if(!judgeExpertIsAtendProject(result)) {
					attendList.add(result);
				}

			}
		}
		return attendList;
	}
	/**
	 * 判断当前专家在某个天是否被抽取过
	 * 函数功能描述：TODO
	 * @param result
	 * @return
	 */
	private boolean judgeExpertIsAtendProject(ResultEntity result) {
		int count = mapper.judgeExpertIsAtendProject(result);
		if(count>0) {
			return false;
		}else {
			return true;
		}
	}

	/**
	 * 查询项目申请记录表
	 *
	 * @param entity
	 * @return
	 */
	public ApplyRecordEntity queryApplyRecord(ApplyRecordEntity entity) {
		return mapper.queryApplyRecord(entity);
	}

	/**
	 * 申请审核
	 *
	 * @param entity
	 */
	@Transactional
	public void projectExtractAudit(ProjectAuditEntity projectAuditEntity, ProjectEntity projectEntity) {
		// 修改项目状态
		mapper.updateProjectStatus(projectEntity);

		// 向项目审核记录表插入数据
		mapper.saveProjectAudit(projectAuditEntity);
	}

	/**
	 * 指定专家申请审核
	 *
	 * @param entity
	 */
	@Transactional
	public void projectAppointAudit(ProjectAuditEntity projectAuditEntity, ProjectEntity projectEntity,
			UserEntity userEntity) {
		// 根据抽取批次号查询该批次所有的项目
		ProjectEntity entity = new ProjectEntity();
		if (EmptyUtils.isEmpty(projectEntity.getDecimationBatch())) {
			throw new HZWException("未获取批次号！请联系管理员");
		}
		entity.setDecimationBatch(projectEntity.getDecimationBatch());
		List<ProjectEntity> projectList = mapper.queryProjectList(entity);

		// 循环处理同一批次下的所有项目
		for (ProjectEntity tmpPro : projectList) {
			// 项目ID
			String projectId = tmpPro.getProjectId();
			projectEntity.setProjectId(projectId);
			projectEntity.setDecimationBatch(null);
			// 修改项目状态（根据项目ID修改单个项目）
			mapper.updateProjectStatus(projectEntity);

			// 向项目审核记录表插入数据
			projectAuditEntity.setProject_id(projectId);
			projectAuditEntity.setId(CommUtil.getKey());
			mapper.saveProjectAudit(projectAuditEntity);

			// 验证指定专家申请前有无保存抽取条件
			ConditionEntity con = new ConditionEntity();
			con.setProjectId(projectId);
			List<ConditionEntity> conditionList = mapper.queryConditionList(con);

			if (conditionList.isEmpty()) {
				// 指定专家申请审核通过向抽取条件表插入数据
				con.setId(CommUtil.getKey());
				con.setMethod(4L);
				mapper.saveAuditCondition(con);
			}else{
				con = conditionList.get(0);

			}

			if (SysConstants.ROLE.DIRECTOR.equals(userEntity.getRole_name()) && 1L == projectAuditEntity.getStatus()
					&& conditionList != null && conditionList.size() > 0) {
				for (int i = 0; i < conditionList.size(); i++) {
					if ("1".equals(conditionList.get(i).getMethod())) { // 指定专家
						con = new ConditionEntity();
						con.setId(conditionList.get(i).getId());
						con.setIsTwoCon(1L);
						mapper.updateTwoCon(con);
					}

					if("4".equals(conditionList.get(i).getMethod())){//如果是指定专家
						//保存抽取记录
						saveAppointAuditExtractedExpert(tmpPro,con);
						//将项目变成已抽取
						ProjectEntity project = new ProjectEntity();
						project.setStatus(10L);             // 10：指定专家已抽取
						project.setProjectId(tmpPro.getProjectId());
						mapper.updateProjectStatus(project);
					}

				}
			}
		}

	}
	/**
	 * 主任审核完之后，将指定专家入库
	 */
	private void saveAppointAuditExtractedExpert(ProjectEntity projectEntity,ConditionEntity con){
		AppointsExpertsEntity appointsExpertsEntity = new AppointsExpertsEntity();
		appointsExpertsEntity.setDecimationBatch(projectEntity.getDecimationBatch());
		List<AppointsExpertsEntity> appointsExpertsList =  appointsExpertsService.queryDateByDecimationBatch(appointsExpertsEntity);
		if(appointsExpertsList !=null && appointsExpertsList.size()>0){
			List<ResultEntity> resultList = new ArrayList<ResultEntity>();
			for(AppointsExpertsEntity entity :appointsExpertsList ){
				ResultEntity resultEntity = new ResultEntity();

				resultEntity.setId(CommUtil.getKey());// 抽取专家表的主键ID
				resultEntity.setProjectId(projectEntity.getProjectId());// 项目id
				resultEntity.setConditionId(con.getId());// 抽取条件id
				resultEntity.setDecimationBatch(projectEntity.getDecimationBatch());
				//根据专家ID查询库里是否存在专家
				ConditionEntity conEntity  = new ConditionEntity();
				conEntity.getExpert().setId_no(entity.getExpertsCodeId());
				List<ExpertInfoEntity> list=queryExperts(conEntity);
				//将指定专家存入到抽取结果表中
				if(list!=null && list.size()>0){
					resultEntity.setUserId(list.get(0).getUser_id());
					resultEntity.setUserName(list.get(0).getUser_name());
				}else{
					//当指定专家不存在时，用户ID默认赋予0
					resultEntity.setUserId("0");
					resultEntity.setUserName(entity.getExpertsName());

				}
				resultEntity.setExtractTime(new Date());// 抽取时间
				resultEntity.setSort(1L);
				resultEntity.setJoinStatus(SysConstants.JOIN_STATUS.ATTEND);
				resultList.add(resultEntity);

			}
			mapper.saveExtractedExpert(resultList);

		}


	}

	/**
	 * 查询所有项目负责人
	 *
	 * @return
	 */
	public List<UserEntity> queryProjectManager(UserEntity userEntity) {
		return mapper.queryProjectManager(userEntity);
	}

	/**
	 * 根据角色信息查询用户信息
	 *
	 * @return
	 */
	public List<UserEntity> queryUserByRole(UserEntity userEntity) {
		return mapper.queryUserByRole(userEntity);
	}

	/**
	 * 根据项目创建人获得项目
	 *
	 * @param project
	 * @return
	 */
	public List<ProjectEntity> queryProjectList(ProjectEntity project) {
		List<ProjectEntity> list = mapper.queryPageProjectInitList(project);
		for (ProjectEntity projectEntity : list) {
			projectEntity.setIsFillPump(1);
			// 判断当抽取方式为短信通知
			if (null != projectEntity.getMethod() && projectEntity.getMethod().equals(2L)
					&& (SysConstants.PROJECT_STATUS.DOING.equals(projectEntity.getStatus())
							|| SysConstants.PROJECT_STATUS.ALREADY.equals(projectEntity.getStatus()))) {
				ResultEntity resultEn = mapper.queryCallTime(projectEntity);
				Date lastExtractTime = resultEn.getCallTime();
				// 无抽取记录则补抽专家
				if (null == lastExtractTime) {
					if (0 == resultEn.getCallCount()) {
						projectEntity.setIsFillPump(2);
					}
				} else {
					Calendar lastExtractCal = Calendar.getInstance();
					lastExtractCal.setTime(lastExtractTime);
					lastExtractCal.add(Calendar.MINUTE, 6);// 日期加5分钟
					lastExtractTime = lastExtractCal.getTime();
					Date currentTime = new Date();
					// 判断项目最后抽取时间6分钟之后是否大于等于当前时间
					if (0 <= lastExtractTime.compareTo(currentTime)) {
						projectEntity.setIsFillPump(0);
					}
				}
			}
		}
		return list;
	}

	/**
	 * 更新项目状态
	 *
	 * @param project
	 */
	public void updateProjectStatus(ProjectEntity project) {
		mapper.updateProjectStatus(project);
	}

	/**
	 * 更新项目抽取状态（特定情况下使用）
	 *
	 * @param project
	 */
	public void updateChouProjectStatus(ProjectEntity project) {
		mapper.updateChouProjectStatus(project);
	}

	/**
	 * 查询已同意参标的国家级（或地方级）专家个数
	 *
	 * @param conEntity
	 * @return
	 */
	public Integer queryAgreeExpertCount(ConditionEntity conEntity) {
		return mapper.queryAgreeExpertCount(conEntity);
	}

	/**
	 * 根据项目ID，获取项目审批信息
	 *
	 * @param projectAuditEntity
	 * @return
	 */
	public ProjectAuditEntity getProAuditInfoByProId(ProjectAuditEntity projectAuditEntity) {
		return mapper.getProAuditInfoByProId(projectAuditEntity);
	}

	/**
	 * 根据项目ID，获取项目审批信息(语音：查找当前项目在指定的审核类型下，是否已审核过)
	 *
	 * @param projectAuditEntity
	 * @return
	 */
	public ProjectAuditEntity getProAuditInfoByProIdToVoice(ProjectAuditEntity projectAuditEntity) {
		return mapper.getProAuditInfoByProIdToVoice(projectAuditEntity);
	}

	/**
	 * 查询项目最新一条申请记录表
	 *
	 * @param entity
	 * @return
	 */
	public ApplyRecordEntity queryNewApplyRecord(ApplyRecordEntity entity) {
		return mapper.queryNewApplyRecord(entity);
	}

	/**
	 * 查询专家除参加当前项目外，在开标当天还参加过是否其他项目
	 *
	 * @param resultEntity
	 * @return
	 */
	public List<ProjectEntity> queryExtractExpertList(ProjectEntity project) {
		return mapper.queryExtractExpertList(project);
	}

	/**
	 * 更新违规记录
	 *
	 * @param result
	 */
	public void updateExtractExIllegal(ResultEntity result) {
		mapper.updateExtractExIllegal(result);
	}

	/**
	 * 查询专家参与项目
	 *
	 * @param project
	 * @return
	 */
	public List<ProjectEntity> queryPageExpertPro(ProjectEntity project) {
		return mapper.queryPageExpertPro(project);
	}

	/**
	 * 查询项目最小抽取时间
	 *
	 * @param conditionId
	 * @return
	 */
	public String queryMinExtractTime(String conditionId) {
		return mapper.queryMinExtractTime(conditionId);
	}

	/**
	 * 根据项目ID，获取项目审批列表信息
	 *
	 * @param project
	 * @return
	 */
	public List<ProjectAuditEntity> getAuditInfoById(ProjectAuditEntity project) {
		return mapper.getAuditInfoById(project);
	}

	/**
	 * 查询国招业务平台项目
	 *
	 * @param project
	 * @return
	 */
	public List<ProjectEntity> queryPageJSGZProjectList(ProjectEntity project) {
		return mapper.queryPageJSGZProjectList(project);
	}

	/**
	 * 根据项目编号和开标时间查询专家参标情况
	 *
	 * @param id
	 * @return
	 */
	public List<ResultByProjectEntity> queryByProject(ProjectEntity projectEntity) {
		return mapper.queryByProject(projectEntity);
	}

	/**
	 * 根据项目id删除记录
	 *
	 * @param id
	 * @return
	 */
	@Transactional
	public void deleteRecordByProjectId(ProjectEntity projectEntity, DeleteRecord deleteRecord) {
		mapper.deleteRecordByProjectId(projectEntity);

		if (!EmptyUtils.isEmpty(projectEntity.getDecimationBatch())) {
			// 如果抽取批次号不为空，则删除该批次的所有项目
			ProjectEntity entity = new ProjectEntity();
			entity.setDecimationBatch(projectEntity.getDecimationBatch());
			List<ProjectEntity> projectList = mapper.queryProjectList2(entity);

			for (ProjectEntity tmpPro : projectList) {
				deleteRecord.setId(CommUtil.getKey());
				deleteRecord.setProjectId(tmpPro.getProjectId());
				mapper.addDeleteRecord(deleteRecord);
			}
		} else {
			// 如果批次号为空，则删除某一个项目
			deleteRecord.setId(CommUtil.getKey());
			deleteRecord.setProjectId(projectEntity.getProjectId());
			mapper.addDeleteRecord(deleteRecord);
		}
	}

	/**
	 * 获取删除的项目
	 *
	 * @param project
	 * @return
	 */
	public List<ProjectEntity> queryPageDeleteProjectList(ProjectEntity project) {
		List<ProjectEntity> list = mapper.queryPageDeleteProjectInitList(project);
		for (ProjectEntity projectEntity : list) {
			projectEntity.setIsFillPump(1);
			// 判断当抽取方式为短信通知
			if (null != projectEntity.getMethod() && projectEntity.getMethod().equals(2L)
					&& (SysConstants.PROJECT_STATUS.DOING.equals(projectEntity.getStatus())
							|| SysConstants.PROJECT_STATUS.ALREADY.equals(projectEntity.getStatus()))) {
				ResultEntity resultEn = mapper.queryCallTime(projectEntity);
				Date lastExtractTime = resultEn.getCallTime();
				// 无抽取记录则补抽专家
				if (null == lastExtractTime) {
					if (0 == resultEn.getCallCount()) {
						projectEntity.setIsFillPump(2);
					}
				} else {
					Calendar lastExtractCal = Calendar.getInstance();
					lastExtractCal.setTime(lastExtractTime);
					lastExtractCal.add(Calendar.MINUTE, 6);// 日期加5分钟
					lastExtractTime = lastExtractCal.getTime();
					Date currentTime = new Date();
					// 判断项目最后抽取时间6分钟之后是否大于等于当前时间
					if (0 <= lastExtractTime.compareTo(currentTime)) {
						projectEntity.setIsFillPump(0);
					}
				}
			}
		}
		return list;
	}

	/**
	 * 根据项目id查询删除项目信息
	 *
	 * @param project
	 * @return
	 */
	public ProjectEntity queryDeleteProjectById(ProjectEntity project) {
		return mapper.queryDeleteProjectById(project);
	}

	/**
	 * 根据项目Decimationbatch查询删除项目信息
	 *
	 * @param project
	 * @return
	 */
	public List<ProjectEntity> queryDeleteProjectByDecimationbatch(ProjectEntity project) {
		return mapper.queryDeleteProjectByDecimationbatch(project);
	}

	/**
	 * 查询删除项目原因
	 *
	 * @param project
	 * @return
	 */
	public String getDeleteReason(DeleteRecord deleteRecord) {
		return mapper.getDeleteReason(deleteRecord);
	}

	/**
	 * 查询恢复删除项目原因
	 *
	 * @param project
	 * @return
	 */
	public String getRestoreDeleteReason(DeleteRecord deleteRecord) {
		return mapper.getRestoreDeleteReason(deleteRecord);
	}

	/**
	 * 更新删除记录表为申请恢复状态
	 *
	 * @param project
	 * @return
	 */
	public void modifyDeleteRecord(DeleteRecord deleteRecord) {
		ProjectEntity project = new ProjectEntity();
		project.setDecimationBatch(deleteRecord.getDecimationBatch());
		List<ProjectEntity> proList = mapper.queryProListByBatch(project);

		for (ProjectEntity entity : proList) {
			deleteRecord.setProjectId(entity.getProjectId());
			mapper.modifyDeleteRecord(deleteRecord);
		}
	}

	/**
	 * 查出删除记录list
	 *
	 * @param project
	 * @return
	 */
	public List<DeleteRecord> getDeleteRecordList() {
		return mapper.getDeleteRecordList();
	}

	/**
	 * 申请删除恢复项目列表信息
	 *
	 * @param entity
	 * @return
	 */
	public List<ProjectEntity> queryRestoreDeletePageProjectList(ProjectEntity entity, UserEntity currentUser) {
		List<ProjectEntity> list = mapper.queryRestoreDeletePageProjectList(entity);
		return list;
	}

	/**
	 * 删除恢复项目
	 *
	 * @param entity
	 * @return
	 */
	@Transactional
	public void restoreDeleteProject(ProjectEntity entity) {
		mapper.restoreDeleteProject(entity);

		List<ProjectEntity> proList = mapper.queryProListByBatch(entity);

		for (ProjectEntity pro : proList) {
			mapper.clearDeleteProject(entity);
		}

	}

	/**
	 * 保存成功之后向抽取人发送通知短信
	 */
	public void smsExtractMobile(List<ProjectEntity> projectList, String saveProjectNo) {
		List<UserEntity> extractList = userService.queryExtractMobiles(SysConstants.ROLE.EXTRACT);
		if (!extractList.isEmpty()) {
			List<SmsRecordEntity> smsRecordList = new ArrayList<SmsRecordEntity>();
			Date currentDate = new Date();

			List<String[]> msgList = new ArrayList<String[]>();

			// 拼接短信内容
			for (UserEntity extractUser : extractList) {
				String[] msg = new String[3];
				for (ProjectEntity project : projectList) {
					// 获取项目创建人信息
					UserEntity createUser = userService.selectUserById(project.getCreate_id());
					// 手机号
					msg[0] = extractUser.getMobile();
					// 短信内容
					msg[1] = extractUser.getUser_name() + "您好，" + createUser.getUser_name() + "申请的抽取批次["
							+ project.getDecimationBatch() + "]的项目[" + saveProjectNo + "];申请抽取专家。【省交易中心】";

					// 记录短信
					SmsRecordEntity smsRecordEntity = new SmsRecordEntity();
					smsRecordEntity.setSms_id(CommUtil.getKey());
					smsRecordEntity.setProject_id(project.getProjectId());
					smsRecordEntity.setSms_user_id(createUser.getUser_id());
					smsRecordEntity.setSms_user_name(extractUser.getUser_name());
					smsRecordEntity.setSms_content(msg[1]);
					smsRecordEntity.setSms_mobile(msg[0]);
					smsRecordEntity.setSms_type(SysConstants.SMS_TYPE.SMS_TWO);
					smsRecordEntity.setSms_time(currentDate);
					smsRecordList.add(smsRecordEntity);
				}
				msgList.add(msg);

			}
			try {
				// 批量添加短信记录
				smsRecordService.insertSmsRecordList(smsRecordList);
			} catch (Exception e) {
				e.printStackTrace();
			}
			String result_mt = SMSNewUtil.sendMsgByDiffContent(msgList);
			if (result_mt.startsWith("-") || result_mt.equals("")) {// 发送短信，如果是以负号开头就是发送失败。
				System.out.print("发送失败！返回值为：" + result_mt + "请查看webservice返回值对照表");
			}
		}
	}

	/**
	 * 保存成功之后向抽取人发送通知短信
	 */
	public void smsExtractMobileService(ProjectEntity project) {
		List<UserEntity> extractList = userService.queryExtractMobiles(SysConstants.ROLE.EXTRACT);
		if (!extractList.isEmpty()) {
			List<SmsRecordEntity> smsRecordList = new ArrayList<SmsRecordEntity>();
			Date currentDate = new Date();
			// 获取项目创建人信息
			UserEntity createUser = userService.selectUserById(project.getCreate_id());
			List<String[]> msgList = new ArrayList<String[]>();
			// 拼接短信内容
			for (UserEntity extractUser : extractList) {
				String[] msg = new String[3];
				// 手机号
				msg[0] = extractUser.getMobile();
				// 短信内容
				msg[1] = extractUser.getUser_name() + "你好，" + project.getOperator() + "申请" + project.getProjectName()
						+ "抽取专家。【省交易中心】";
				msgList.add(msg);
				// 记录短信
				SmsRecordEntity smsRecordEntity = new SmsRecordEntity();
				smsRecordEntity.setSms_id(CommUtil.getKey());
				smsRecordEntity.setProject_id(project.getProjectId());
				smsRecordEntity.setSms_user_id(project.getOperator());
				smsRecordEntity.setSms_user_name(extractUser.getUser_name());
				smsRecordEntity.setSms_content(msg[1]);
				smsRecordEntity.setSms_mobile(msg[0]);
				smsRecordEntity.setSms_type(SysConstants.SMS_TYPE.SMS_TWO);
				smsRecordEntity.setSms_time(currentDate);
				smsRecordList.add(smsRecordEntity);
			}
			// 批量添加短信记录
			smsRecordService.insertSmsRecordList(smsRecordList);
			String result_mt = SMSNewUtil.sendMsgByDiffContent(msgList);
			if (result_mt.startsWith("-") || result_mt.equals("")) {// 发送短信，如果是以负号开头就是发送失败。
				System.out.print("发送失败！返回值为：" + result_mt + "请查看webservice返回值对照表");
			}
		}
	}

	/**
	 * 根据项目批次查询项目
	 *
	 * @param project
	 * @return
	 */
	public List<ProjectEntity> queryProListByBatch(ProjectEntity project) {
		return mapper.queryProListByBatch(project);
	}

	/**
	 * 查询已同意参标专家个数
	 *
	 * @param conEntity
	 * @return
	 */
	public Integer queryConfirmExpertCount(ConditionEntity conEntity) {
		return mapper.queryConfirmExpertCount(conEntity);
	}

	public List<ConditionEntity> queryDecimationbatchByBidTime(ConditionEntity conEntity) {
		return mapper.queryDecimationbatchByBidTime(conEntity);
	}

	/**
	 * 抽取专家操作（适用于专家库系统自身操作）
	 *
	 * @param conEntity
	 * @return
	 */
	/*
	 * @Transactional public List<ExpertInfoEntity>
	 * queryExpertsByRuleForZJK(ConditionEntity conEntity, ProjectEntity
	 * project) {
	 *
	 * conEntity.setExpertType(StringUtil.strToString(conEntity.getExpertType())
	 * );// 对专业类别的查询条件进行用于in条件的转换
	 *
	 * getZoneQueryCondition(conEntity); conEntity.setGrade(1L);//默认查询地方级专家
	 * List<ExpertInfoEntity> allExperts =
	 * mapper.queryExpertsByRule(conEntity);// if
	 * (conEntity.getSeniorNum().intValue() >= 0) { conEntity.setGrade(2L);// }
	 * List<ExpertInfoEntity> serniorExperts =
	 * mapper.queryExpertsByRule(conEntity);// 高级专家 //
	 * 从结果中随机抽取queryExpertsByRule List<ExpertInfoEntity> experts =
	 * this.mergeResultByGrade(allExperts, serniorExperts, conEntity, project,
	 * conEntity); ResultEntity result = null; List<ResultEntity> resultList =
	 * new ArrayList<ResultEntity>(); Long sort =
	 * mapper.queryResultSort(conEntity); if (sort == null) { sort = 0L; }
	 * conEntity.setGrade(null); Integer count =
	 * mapper.queryAgreeExpertCount(conEntity); if (experts == null ||
	 * experts.size() <= 0 || experts.size() < conEntity.getTotal() - count) {
	 * smsProjectCreateUser(project, MessageConstants.NOT_ENOUGH_EXPERT); throw
	 * new HZWException(MessageConstants.NOT_ENOUGH_EXPERT); } sort++;//抽取批次 //
	 * 是否参加 0:参加 1:不参加 2:未通知 Long joinStatus =
	 * "2".equals(conEntity.getJoin_status()) ? 4L : 2L;
	 *
	 * for (ExpertInfoEntity ex : experts) { result = new ResultEntity();
	 * result.setExtractTime(new Date());// 抽取时间
	 * result.setUserId(ex.getUser_id());// 专家id result.setSort(sort);// 抽取的批次
	 * result.setJoinStatus(joinStatus); resultList.add(result); }
	 * conEntity.setNum(sort);//抽取的轮次
	 *
	 * //根据抽取批次号保存多个抽取结果 //根据项目id，查询同批次的项目信息 //ProjectEntity pEntity =
	 * mapper.queryConditionIdByProjectId(project); //用于入库的专家信息List
	 * List<ResultEntity> newResultList = new ArrayList<ResultEntity>();
	 * //循环项目Lsit封装专家信息List List<ProjectEntity> projectEntityList =
	 * mapper.queryConditionIdByBatch(project); for(ProjectEntity
	 * proEntity:projectEntityList){ for(ResultEntity entity:resultList){
	 * ResultEntity resultEntity = new ResultEntity();
	 * SpringApplicationContext.copyProperties(entity, resultEntity); // 新对象赋值
	 * resultEntity.setId(CommUtil.getKey());//抽取专家表的主键ID
	 * resultEntity.setProjectId(proEntity.getProjectId());//项目id
	 * resultEntity.setConditionId(proEntity.getConditionId());// 抽取条件id
	 * newResultList.add(resultEntity); } }
	 *
	 *
	 *
	 * mapper.saveExtractedExpert(newResultList);// 将抽取的专家直接加入到抽取结果中，默认状态为未操作
	 * //判断数据来源，如果是szyc，自动发送 if(null != conEntity.getSourceCode() &&
	 * conEntity.getSourceCode().equals("szyc")){ List<ExpertInfoEntity>
	 * expertList = mapper.queryExpertInfoByBatch(conEntity);
	 * sendMessageForExperts(conEntity,expertList); }
	 *
	 * return experts; }
	 */
	/**
	 * 向专家发送语音信息
	 *
	 * @param prEntity
	 * @param conEntity
	 * @param experts
	 * @throws InterruptedException
	 */
	public void sendVoiceMessageForExperts(ProjectEntity prEntity, ConditionEntity conEntity,
			List<ExpertInfoEntity> experts) throws InterruptedException {
		// 1.上传TSS内容
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		String content = "，江苏省招标中心有限公司诚邀您参加评标，开标时间：" + sdf.format(prEntity.getBidTime()) + "，开标地点："
				+ prEntity.getBidAddress() + "，项目负责人：" + prEntity.getManager() + "，电话：" + prEntity.getPhone()
				+ "。|参加请按1，不参加请按2。";

		publicSendVoiceMessageForExperts(content, conEntity, experts);
	}

	/**
	 * 向专家发送语音信息(多个项目公用一个流水号时使用)
	 *
	 * @param projectList
	 * @param conEntity
	 * @param experts
	 * @throws InterruptedException
	 */
	public void copySendVoiceMessageForExperts(List<ProjectEntity> projectList, ConditionEntity conEntity,
			List<ExpertInfoEntity> experts) throws InterruptedException {
		// 1.上传TSS内容
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

		String content = "，江苏省招标中心有限公司诚邀您参加评标，开标时间：" + sdf.format(projectList.get(0).getBidTime()) + "，开标地点："
				+ projectList.get(0).getBidAddress() + "，项目负责人：" + projectList.get(0).getManager() + "，电话："
				+ projectList.get(0).getPhone() + "。|参加请按1，不参加请按2。";

		publicSendVoiceMessageForExperts(content, conEntity, experts);

	}

	/**
	 * 向专家发送语音信息公共部分代码
	 *
	 * @param TTSContent
	 *            语音播报内容
	 * @param conEntity
	 * @param experts
	 */
	private void publicSendVoiceMessageForExperts(String content, ConditionEntity conEntity,
			List<ExpertInfoEntity> experts) {
		// 1.上传TSS内容
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		// 接受评审的专家计数器
		int counter = 0;

		for (ExpertInfoEntity entity : experts) {
			// 语音播报内容
			String TTSContent = "您好：" + entity.getUser_name() + content;
			// 达到满足数量的专家时退出循环
			if (counter >= conEntity.getTotal()) {
				break;
			}

			try {
				/**
				 * 先判断ID是否存在，如果不存在，则直接进行后续外呼操作； 如果存在，则调用外呼结果查询接口判断是否已经语音通知过该专家：
				 * 如果未通知过，则跳过发送文本直接调用外呼； 如果已经通知过，则修改表中扫描状态；
				 */

				counter = testTime(entity, counter, TTSContent);
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
	}

	private static synchronized int testTime(ExpertInfoEntity entity, int counter, String content)
			throws InterruptedException {

		// 1.发起TTS内容审核
		String synchroId = "123456799";
		// OutboundTTSOut ttsOut = SVSNewUtil.sendVoiceTTS(content,synchroId);

		// 1.1如果成功继续调用外呼接口
		// if(ttsOut.getTSR_RESULT().equals("0")){
		if (true) {
			// 等待审核（5分钟）
			// Thread.currentThread().sleep(3 * 1000);
			// 2.发起启呼接口（查询是否审核通过）
			OutboundVoiceOut voiceOut = null;
			while (true) {
				// 等待10秒
				Thread.currentThread().sleep(10 * 1000);
				voiceOut = SVSNewUtil.sendVoice(entity.getMobilephone(), synchroId);

				if (true) {
					// 只要不是未审核，则结束循环
					break;
				}
			}

			// 2.1外呼成功后
			if (voiceOut.getTSR_RESULT().equals("0")) {
				// 是否结束本次外呼的标识
				boolean flag = false;
				while (true) {
					if (flag) {
						break;
					}
					// 等待30秒
					Thread.currentThread().sleep(3 * 10 * 1000);

					// 3.调用外呼结果查询接口，直到查询到挂断数据为止(flag置为true)，结束循环
					QueryObVResultOut resultOut = SVSNewUtil.queryVoiceResult(voiceOut.getTSR_MSG().getLogId());
					// 3.1 外呼接口调用成功
					if ("0".equals(resultOut.getTSR_RESULT())) {
						// 判断返回结果是否参与评审，如果接受，则计数器自增
						if (null != resultOut.getTSR_MSG() && !resultOut.getTSR_MSG().isEmpty()) {
							if (resultOut.getTSR_MSG().get(0).getOper().equals("1")) {
								counter++;
							}
							flag = true;
						}

					} else {
						System.out.print("外呼结果接口调用失败！返回值为：" + resultOut.getTSR_MSG_ERR());
					}
				}
			} else {
				// 外呼接口请求失败
				System.out.print("外呼接口调用失败！返回值为：" + voiceOut.getTSR_MSG_ERR());
			}
		} else {
			// TTS同步接口请求失败
			// System.out.print("TTS内容同步调用失败！返回值为："+ttsOut.getTSR_MSG());
			System.out.print("TTS内容同步调用失败！返回值为：");
		}

		return counter;
	}

	// 检验抽取人数
	public String getPeople(ConditionEntity condition) {
		// 返回结果
		String msg = "";
		List<ProjectEntity> pList = new ArrayList<ProjectEntity>();
		if (null != condition.getDecimationBatch() && !condition.getDecimationBatch().isEmpty()) {
			ProjectEntity project = new ProjectEntity();
			project.setDecimationBatch(condition.getDecimationBatch());
			pList = queryProjectListById(project);
		}

		if (pList.size() > 0) {
			String tender = "";
			ConditionEntity conditionEntity = new ConditionEntity();
			for (ProjectEntity entity : pList) {
				conditionEntity.setId(entity.getConditionId());
				conditionEntity = queryConditionById(conditionEntity);

				if (null != entity.getTender() && !"".equals(entity.getTender())
						&& entity.getTender().indexOf(",") > -1) {
					String[] tenderSplit = entity.getTender().split(",");
					for (int i = 0; i < tenderSplit.length; i++) {
						tender += "'" + tenderSplit[i] + "',";
					}
					tender = tender.substring(0, tender.length() - 1);
				} else {
					tender = "'" + entity.getTender() + "'";
				}
				conditionEntity.setBidTimeDay(DateUtil.dateToString(entity.getBidTime()));
			}
			conditionEntity.setDecimationBatch(condition.getDecimationBatch());
			conditionEntity.setTender(tender);
			msg = queryExpertToChecks(conditionEntity,pList);
		}
		return msg;

	}

	public static String randomUUID() {
		return UUID.randomUUID().toString().replace("-", "");
	}

	/**
	 * 根据批次号查询抽取条件
	 *
	 * @param entity
	 * @return
	 */
	public List<ConditionEntity> queryConditionListByBatch(ProjectEntity entity) {
		return mapper.queryConditionListByBatch(entity);
	}

	/**
	 * 查询所有未通知的专家信息
	 *
	 * @param entity
	 * @return
	 */
	public List<ExpertInfoEntity> queryProjectExpertInfo(ConditionEntity entity) {
		return mapper.queryProjectExpertInfo(entity);
	}

	/**
	 * 校验短信批次号是否重复（系统自动语音定时器使用）
	 *
	 * @return
	 */
	private String checkMessageNo() {
		// 获取随机的短信批次
		String messageNo = UUID.randomUUID().toString().replaceAll("-", "");
		ResultEntity entity = new ResultEntity();
		entity.setMessageNo(messageNo);
		System.out.println("批次号");
		List<ResultEntity> resultList = mapper.queryExtracedResultToMessage(entity);
		// 判断当前短信批次在抽取当天是否重复
		if (resultList != null && resultList.size() > 0) {
			checkMessageNo();
		}
		return messageNo;

	}

	/*
	 * public List<ResultEntity> queryExtracedResultToMessage(ResultEntity
	 * entity) { return mapper.queryExtracedResultToMessage(entity); }
	 */

	public void batchUpdateExtractedExpert(List<ResultEntity> resultList) {
		mapper.batchUpdateExtractedExpert(resultList);
	}

	/***************************
	 * 语音接口调用方法 start
	 *****************************************************/
	/**
	 * 根据批次号+手机号码查询专家结果信息(当天)
	 *
	 * @param entity
	 * @return
	 */
	public List<ResultEntity> queryExtracedResultToVoice(ResultEntity entity) {
		return mapper.queryExtracedResultToVoice(entity);
	}

	/**
	 * 修改抽取结果中专家的状态
	 *
	 * @param resultEntity
	 */
	public int updateExtractedExpertTOVoice(ResultEntity resultEntity) {
		return mapper.updateExtractedExpertTOVoice(resultEntity);
	}

	/**
	 * 修改抽取结果中专家的状态
	 *
	 * @param resultEntity
	 */
	public int updateProjectEndExpertTOVoice(ResultEntity resultEntity) {
		return mapper.updateProjectEndExpertTOVoice(resultEntity);
	}

	/**
	 * 修改专家是否接收短信/通知综合处状态
	 *
	 * @param resultEntity
	 */
	public int updateExtractedSmsStartus(ResultEntity resultEntity) {
		return mapper.updateExtractedSmsStartus(resultEntity);
	}

	/**
	 * 修改抽取条件中的抽取方式
	 *
	 * @param conEntity
	 */
	public int updateConditionCurrentStatus(ConditionEntity conEntity) {
		return mapper.updateConditionCurrentStatus(conEntity);
	}

	/**
	 * 根据专家抽取方式查询所有正在抽取中的项目（同一批次的项目合并成一条语句）[语音、短信]
	 *
	 * @param entity
	 * @return
	 */
	public List<ProjectEntity> queryProjectTOVoice(ConditionEntity entity) {
		return mapper.queryProjectTOVoice(entity);
	}

	/**
	 * 发送语音给专家
	 *
	 * @param projectEntity
	 *            经过处理的项目信息
	 * @param conEntity
	 * @param experts
	 *            需要发送信息的专家集合
	 * @return
	 */
	public void sendVoiceForExperts(ProjectEntity projectEntity, ConditionEntity conEntity,
			List<ExpertInfoEntity> experts) {

		try {
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy年MM月dd日 HH时mm分");

			// 记录发送内容
			//List<SmsRecordEntity> smsRecordList = new ArrayList<SmsRecordEntity>();

			// 根据版本ID获取版本信息
			// TemplateEntity templateEntity = new TemplateEntity();
			// templateEntity.setTemplateId(conEntity.getTemplateId());
			// templateEntity =
			// templateService.queryTemplateById(templateEntity);
			// conEntity.setSmsStr(templateEntity.getTemplateContent());
			/**
			 * 您好：XXX，江苏省招标中心有限公司诚邀您参加评标， 批次号：【批次号】(【项目名称】)，开标时间：【开标时间】，
			 * 开标地点：【开标地点】，项目负责人：【项目负责人】，电话：【电话】 回复：1-参加，2-不参加。
			 */

			// 赋值 + 语音通知
			for (ExpertInfoEntity e : experts) {
				// 拼接语音内容
				String smsStr = conEntity.getVoiceSmsContent().toString();
				smsStr = smsStr.replaceAll("【批次号】", projectEntity.getDecimationBatch());
				smsStr = smsStr.replaceAll("【项目名称】", projectEntity.getProjectName());
				smsStr = smsStr.replaceAll("【项目编号】", projectEntity.getProjectNo());
				smsStr = smsStr.replaceAll("【开标时间】",
						sdf.format(projectEntity.getBidTime() == null ? new Date() : projectEntity.getBidTime()));
				smsStr = smsStr.replaceAll("【开标地点】", projectEntity.getBidAddress());
				smsStr = smsStr.replaceAll("【项目负责人】", projectEntity.getManager());
				smsStr = smsStr.replaceAll("【电话】", projectEntity.getPhone());
				Date callTime = new Date();// 通知时间
				try {
					List<ResultEntity> resultList = new ArrayList<ResultEntity>();// 通知抽取结果
					// 获取随机的短信批次
					String messageNo = checkMessageNo();
					// 专家抽取结果
					ResultEntity result = new ResultEntity();
					result.setUserId(e.getUser_id());// 专家id
					result.setCallTime(callTime);
					result.setJoinStatus(SysConstants.JOIN_STATUS.SMS_NOTIFICATION);
					result.setDecimationBatch(projectEntity.getDecimationBatch());
					result.setMessageNo(messageNo);
					resultList.add(result);
					// 将通知结果改成语音已通知
					batchUpdateExtractedExpert(resultList);
					// 您好：龚延风，江苏省招标中心有限公司诚邀您参加评标，
					// 批次号：[批次号]([项目名称])，开标时间：[开标时间]，
					// 开标地点：[开标地点]，项目负责人：[项目负责人]，电话：[电话]
					// 发送语音实体类
					VoiceEntity vEntity = new VoiceEntity();
					// 业务流水号(业务流水号为项目批次号)
					vEntity.setMessageId(messageNo);

					smsStr = e.getUser_name() + "专家！" + smsStr + "参加请按1，不参加请按2。";
					/*if("4".equals(templateType)||"5".equals(templateType)){
						smsStr1 = e.getUser_name() + "专家，您好！" + smsStr1 + "感谢您对招标采购工作的支持。";
					}else{

					}*/

					// 语音内容
					vEntity.setMediaContent(
							/*
							 * smsStr.replaceAll("XXX", e.getUser_name()).trim()
							 */smsStr.trim());
					// 手机号码
					vEntity.setCalledNum(e.getMobilephone());
					// 发送语音
					CallClient client = new CallClient();
					// 获取发送之后的返回值
					String voiceResult = client.sendVoice(vEntity);

					// 记录短信
					SmsRecordEntity smsRecordEntity = new SmsRecordEntity();
					smsRecordEntity.setSms_id(CommUtil.getKey());
					smsRecordEntity.setDecimationBatch(projectEntity.getDecimationBatch());
					smsRecordEntity.setSms_user_id(e.getUser_id());
					smsRecordEntity.setSms_user_name(e.getUser_name());
					smsRecordEntity.setSms_content(vEntity.getMediaContent());
					smsRecordEntity.setSms_mobile(vEntity.getCalledNum());
					smsRecordEntity.setSms_type(SysConstants.SMS_TYPE.SMS_FOUR);
					smsRecordEntity.setSms_time(callTime);
					smsRecordEntity.setSms_variety(SysConstants.CONDITION_CURRENT_STATUS.METHOD_TWO);
					smsRecordEntity.setSms_result(voiceResult);
					System.out.println(CommUtil.getKey() + "," + projectEntity.getDecimationBatch() + ","
							+ e.getUser_id() + "," + e.getUser_name() + "," + vEntity.getMediaContent() + ","
							+ vEntity.getCalledNum() + "," + SysConstants.SMS_TYPE.SMS_FOUR + "," + callTime + ","
							+ SysConstants.CONDITION_CURRENT_STATUS.METHOD_ONE + "," + voiceResult);
					// 将语音返回结果存入库中
					smsRecordService.insertSmsRecord(smsRecordEntity);
					// smsRecordList.add(smsRecordEntity);

				} catch (Exception q) {
					q.printStackTrace();
					// throw new HZWException("发送短信模块出错！");
				}

			}
			// 批量添加短信记录
			// smsRecordService.insertSmsRecordList(smsRecordList);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
	 * 语音功能，判断当前时间是否是开标时间半个小时内
	 */
	public String checkBidTime(List<ProjectEntity> projectList, ConditionEntity cEntity) {
		String msg = "success";
		// 判断当前抽取方式是否是语音抽取
		if (cEntity.getMethod().longValue() == SysConstants.CONDITION_METHOD.METHOD_THREE) {
			Calendar end = Calendar.getInstance();
			long currentTime = end.getTimeInMillis() + 30 * 60 * 1000;
			Date date = new Date(currentTime);

			Calendar startTime = Calendar.getInstance();
			startTime.add(Calendar.HOUR, -2);
			// 判断开标时间是否在当前时间+半小时之前
			if (projectList.get(0).getBidTime().after(startTime.getTime())
					&& projectList.get(0).getBidTime().before(date)) {
				// 将抽取记录表中留痕
				ConditionEntity conEntity = new ConditionEntity();
				conEntity.setDecimationBatch(projectList.get(0).getDecimationBatch());
				// 获取当前流水号下的抽取信息
				List<ExpertInfoEntity> expertList = queryProjectExpertInfo(conEntity);
				for (ProjectEntity project : projectList) {
					// 判断当前的批次号是否有抽取专家记录
					if (expertList != null && expertList.size() > 0) {
						// 存在抽取记录表示，当前的项目转为应急抽取
						// Method"语音抽取"+CurrentStatus"语音抽取"="应急抽取"
						cEntity.setCurrentStatus(SysConstants.CONDITION_CURRENT_STATUS.METHOD_TWO);
						// 将语音的状态留痕
						conEntity.setCurrentStatus(SysConstants.CONDITION_CURRENT_STATUS.METHOD_TWO);
					} else {
						cEntity.setCurrentStatus(SysConstants.CONDITION_CURRENT_STATUS.METHOD_TWO);
						cEntity.setMethod(SysConstants.CONDITION_METHOD.METHOD_ONE);
						// 将项目抽取方式改成人工抽取
						conEntity.setMethod(SysConstants.CONDITION_METHOD.METHOD_ONE);
						// 将语音的状态留痕
						conEntity.setCurrentStatus(SysConstants.CONDITION_CURRENT_STATUS.METHOD_TWO);
					}
					conEntity.setId(project.getConditionId());
					updateConditionCurrentStatus(conEntity);
				}
				// 通知业务员此抽取变成应急抽取，需要业务员手动操作
				smsProjectCreateUser(projectList.get(0), "当前项目已经转为应急抽取，需要手动确认专家");
				cEntity.setCurrentStatus(SysConstants.CONDITION_CURRENT_STATUS.METHOD_TWO);
			} else if (projectList.get(0).getBidTime().before(startTime.getTime())) {
				// 将抽取记录表中留痕
				ConditionEntity conEntity = new ConditionEntity();
				conEntity.setDecimationBatch(projectList.get(0).getDecimationBatch());
				// 获取当前流水号下的抽取信息
				List<ExpertInfoEntity> expertList = queryProjectExpertInfo(conEntity);
				for (ProjectEntity project : projectList) {
					// 判断当前的批次号是否有抽取专家记录
					if (expertList != null && expertList.size() > 0) {
						// 存在抽取记录表示，当前的项目转为应急抽取
						// Method"语音抽取"+CurrentStatus"语音抽取"="应急抽取"
						cEntity.setCurrentStatus(SysConstants.CONDITION_CURRENT_STATUS.METHOD_TWO);
						// 将语音的状态留痕
						conEntity.setCurrentStatus(SysConstants.CONDITION_CURRENT_STATUS.METHOD_TWO);
					} else {
						cEntity.setCurrentStatus(SysConstants.CONDITION_CURRENT_STATUS.METHOD_TWO);
						cEntity.setMethod(SysConstants.CONDITION_METHOD.METHOD_ONE);
						// 将项目抽取方式改成人工抽取
						conEntity.setMethod(SysConstants.CONDITION_METHOD.METHOD_ONE);
						// 将语音的状态留痕
						conEntity.setCurrentStatus(SysConstants.CONDITION_CURRENT_STATUS.METHOD_TWO);
					}
					conEntity.setId(project.getConditionId());
					updateConditionCurrentStatus(conEntity);
				}
				msg = "当前项目已经超过开标时间两小时，如需再次抽取需要走主任审核！";
			}
		}
		return msg;
	}

	/**
	 * 修改抽取条件中的抽取方式
	 *
	 * @param conEntity
	 */
	public String updateConditionStatusByDecimationBatch(ProjectEntity projectEntity) {
		// 修改抽取方式
		mapper.updateConditionStatusByDecimationBatch(projectEntity);
		// 删除还没有通知的专家
		ResultEntity resultEntity = new ResultEntity();
		resultEntity.setDecimationBatch(projectEntity.getDecimationBatch());
		resultEntity.setJoinStatus(SysConstants.JOIN_STATUS.NOINFORM);
		resultEntity.setDeleteFlag(0L);
		mapper.deleteExtractResultByDecimationBatch(resultEntity);
		return "success";
	}

	/**
	 * 根据流水号查询抽取结果
	 *
	 * @param conEntity
	 * @return
	 */
	public List<ResultEntity> queryAllExtractedExperts(ResultEntity entity) {
		return mapper.queryAllExtractedExperts(entity);

	}

	/**
	 * 根据流水号查询抽取结果
	 *
	 * @param conEntity
	 * @return
	 */
	public String updateExtractedExpertTOExpertWay(ResultEntity entity) {
		mapper.updateExtractedExpertTOExpertWay(entity);
		return "success";

	}

	/***************************
	 * 语音接口调用方法 end
	 *****************************************************/
	public List<ResultProjectExpertInfo> queryPageAllProjectExtractedExperts(ResultProjectExpertInfo eInfo) {
		return mapper.queryPageAllProjectExtractedExperts(eInfo);
	}

	/**
	 *
	 * 函数功能描述：取消项目,修改项目的MODIFY_TYPE状态
	 *
	 * @return
	 */
	public int cancelProject(ProjectEntity project) {
		return mapper.modifyCancelProject(project);
	}

	/**
	 *
	 * 函数功能描述：取消项目时，将专家参加评审修改为不参加
	 *
	 * @param project
	 * @return
	 */
	public int modifyJoinStaus(ProjectEntity project) {
		return mapper.modifyJoinStaus(project);
	}

	/**
	 * 变更抽取人数
	 * @param conditionRecordEntity
	 */
	public int updateConditionPersonNum(ConditionRecordEntity conditionRecordEntity) {
		conditionRecordEntity.setId(CommUtil.getKey());
		return mapper.updateConditionPersonNum(conditionRecordEntity);
	}

	public int updateConditionPerson(ConditionRecordEntity conditionRecordEntity) {
		return mapper.updateConditionPerson(conditionRecordEntity);
	}

	public List<ProjectEntity> queryPageProjectInfo(ProjectEntity project){
		return mapper.queryPageProjectInfo(project);
	}
	public List<ProjectEntity> queryPDFProjectInfo(ProjectEntity project)

	{
		return mapper.queryPDFProjectInfo(project);
	}
	public List<LoginRecord> queryPageLoginRecordList(LoginRecord loginRecord){
		return mapper.queryPageLoginRecordList(loginRecord);
	}
	public void saveLoginInfo(LoginRecord loginRecord){
		 mapper.saveLoginInfo(loginRecord);
	}



	/**
	 * 重写根据抽取条件查询可供抽取的专家人数(用于检验人数是否满足)
	 *
	 * @param conEntity
	 * @return
	 */
	public String queryExpertToChecks(ConditionEntity conEntity,List<ProjectEntity> pList) {
		if (conEntity.getId() != null && conEntity.getId() != "") {// conEntity.getNum().intValue()
			// == 1 &&
		// mapper.deleteResultInfo(conEntity);// 删除
		}
		//处理回避专家的信息
		getExpertPhoneCondition(conEntity);
		// 处理专家的地区,拼接成可执行sql语句
		getZoneQueryCondition(conEntity);
		conEntity.setDecimationBatch(pList.get(0).getDecimationBatch());
		//所有异常的专家集合
		List<String> anomalyExpert = new ArrayList<String>();
		//查询当天已经抽取参加及未处理的专家
		getOpenTimeToUntreatedExpert(conEntity,anomalyExpert);

		//查询所有暂停的专家
		getSuspendExpert(anomalyExpert);

		//查询当前处室当前月份不能参加的专家
		getNotCurrentMonthExpert(pList,anomalyExpert);
		//默认0无问题 其他 1 国家人数不足 2 地方人数不足 3已参加人数大于需要人数 4下一次抽取正好是结束
		Integer reuslt=getExtractionInfocheck(conEntity,pList,anomalyExpert);
		// 正式代码
//		if(agreeCount>conEntity.getLocalNum().intValue()){
//			return MessageConstants.LT_ENOUGH_SENIOR_EXPERT;
//		}
//		if (difference > allExperts.size()) {
//			return MessageConstants.NOT_ENOUGH_PLACE_EXPERT;
//		}
//		//判断参加的人数是否与修改之后的人数相等
//		if(countJoin == conEntity.getTotal().intValue()){
//			return "faile";
//		}

		if(reuslt==1) {
			return MessageConstants.NOT_ENOUGH_SENIOR_EXPERT;
		}else if(reuslt==2){
			return MessageConstants.NOT_ENOUGH_PLACE_EXPERT;
		}else if(reuslt==3) {
			return MessageConstants.LT_ENOUGH_SENIOR_EXPERT;
		}else if(reuslt==4) {
			return "faile";
		}
		return "success";
	}



	/**
	 * //默认0无问题 其他 1 国家人数不足 2 地方人数不足
	 * @param conEntity
	 * @param pList
	 * @param anomalyExpert
	 * @return
	 */
	private Integer getExtractionInfocheck(ConditionEntity conEntity, List<ProjectEntity> pList,
			List<String> anomalyExpert) {

		Integer msg = 0;//默认0无问题 其他 1 国家人数不足 2 地方人数不足
		//判断是否选中国家
		Long seciorNum = conEntity.getSeniorNum();//国家级人数
		if(seciorNum>0&&msg==0) {
			conEntity.setGrade(2L);
			msg = getExtractions(conEntity,1,anomalyExpert);
		}
		Long localNum = conEntity.getLocalNum();//地区级人数
		if(localNum>0&& msg==0) {
			conEntity.setGrade(1L);
			msg = getExtractions(conEntity,2,anomalyExpert);
		}
		return msg;
	}

	/**
	 * 重新写抽取专家规则
	 * @param conEntity
	 * @param projectList
	 * @return
	 */
	@Transactional
	private synchronized List<ExpertInfoEntity>  extractingExperts(ConditionEntity conEntity, List<ProjectEntity> projectList) {
		if (conEntity.getId() != null && conEntity.getId() != "") {// conEntity.getNum().intValue()
																	// == 1 &&
			// mapper.deleteResultInfo(conEntity);// 删除
		}
		//处理回避专家的信息
		getExpertPhoneCondition(conEntity);
		// 处理专家的地区,拼接成可执行sql语句
		getZoneQueryCondition(conEntity);
		conEntity.setDecimationBatch(projectList.get(0).getDecimationBatch());
		//所有异常的专家集合
		List<String> anomalyExpert = new ArrayList<String>();
		//查询当天已经抽取参加及未处理的专家
		getOpenTimeToUntreatedExpert(conEntity,anomalyExpert);

		//查询所有暂停的专家
		getSuspendExpert(anomalyExpert);

		//查询当前处室当前月份不能参加的专家
		getNotCurrentMonthExpert(projectList,anomalyExpert);
		// 从结果中随机抽取queryExpertsByRule专家
		List<ExpertInfoEntity> experts =getExtractionInfo(conEntity,projectList,anomalyExpert);
		ResultEntity result = null;
		List<ResultEntity> resultList = new ArrayList<ResultEntity>();
		Long sort = getMaxSort(conEntity);
		conEntity.setGrade(null);
		Integer count = mapper.queryAgreeExpertCount(conEntity);

		// boolean status = false;
		if (experts == null || experts.size() <= 0 || experts.size() < conEntity.getTotal() - count) {
			// status = true;
			// smsProjectCreateUser(project,
			// MessageConstants.NOT_ENOUGH_EXPERT);
			throw new HZWException(MessageConstants.NOT_ENOUGH_EXPERT);
		}
		sort++;// 抽取批次
		// 是否参加 0:参加 1:不参加 2:未通知
		Long joinStatus = (SysConstants.CONDITION_METHOD.METHOD_TWO == conEntity.getMethod()) ? 4L : 2L;

		for (ExpertInfoEntity ex : experts) {
			result = new ResultEntity();
			result.setExtractTime(new Date());// 抽取时间
			result.setUserId(ex.getUser_id());// 专家id
			result.setSort(sort);// 抽取的批次
			result.setJoinStatus(joinStatus);
			result.setMajorselection(ex.getMajorselection());
			resultList.add(result);
		}
		conEntity.setNum(sort);// 抽取的轮次

		// 用于入库的专家信息List
		List<ResultEntity> newResultList = new ArrayList<ResultEntity>();
		// 循环项目Lsit封装专家信息List
		for (ProjectEntity proEntity : projectList) {
			for (ResultEntity entity : resultList) {
				ResultEntity resultEntity = new ResultEntity();
				SpringApplicationContext.copyProperties(entity, resultEntity); // 新对象赋值
				resultEntity.setId(CommUtil.getKey());// 抽取专家表的主键ID
				resultEntity.setProjectId(proEntity.getProjectId());// 项目id
				resultEntity.setConditionId(proEntity.getConditionId());// 抽取条件id
				resultEntity.setDecimationBatch(proEntity.getDecimationBatch());
				resultEntity.setMajorselection(entity.getMajorselection());
				// 判断当前项目是否是语音项目
				if (conEntity.getMethod().intValue() == SysConstants.CONDITION_METHOD.METHOD_THREE) {
					// 判断当前项目之前的抽取方式是否是语音
					if (conEntity.getCurrentStatus() != null
							&& conEntity.getCurrentStatus() == SysConstants.CONDITION_CURRENT_STATUS.METHOD_TWO) {
						// 将当前抽取专家状态标记为应急抽取
						resultEntity.setExpertWay(SysConstants.EXPERT_WAY.WAY_TWO);
					} else {
						// 将当前抽取专家状态标记为语音抽取
						resultEntity.setExpertWay(SysConstants.EXPERT_WAY.WAY_ONE);
					}
				}

				newResultList.add(resultEntity);
			}
		}
		mapper.saveExtractedExpert(newResultList);// 将抽取的专家直接加入到抽取结果中，默认状态为未操作
		//并且将项目状态修改为抽取中
		ProjectEntity projectEntity = new ProjectEntity();
		projectEntity.setDecimationBatch(conEntity.getDecimationBatch());
		projectEntity.setStatus(2L);
		updateProjectStatus(projectEntity);

		return experts;
	}



	//返回抽取的专家结果
	private List<ExpertInfoEntity> getExtractionInfo(ConditionEntity conEntity, List<ProjectEntity> projectList, List<String> anomalyExpert) {
		List<ExpertInfoEntity> list = new ArrayList<ExpertInfoEntity>();
		//判断是否选中国家
		Long seciorNum = conEntity.getSeniorNum();//国家级人数
		if(seciorNum>0) {
			conEntity.setGrade(2L);
			List<ExpertInfoEntity> lists = getExtraction(conEntity,1,anomalyExpert);
			list.addAll(lists);
		}
		Long localNum = conEntity.getLocalNum();//地区级人数
		if(localNum>0) {
			conEntity.setGrade(1L);
			List<ExpertInfoEntity> listl = getExtraction(conEntity,2,anomalyExpert);
			list.addAll(listl);
		}


		return list;
	}

	//查询符合条件sql
	public List<ExpertInfoEntity> getExtraction(ConditionEntity conEntity,Integer secior, List<String> anomalyExpert){
		//判断是否选中职称全选置空查询，单选直接查询
		String titleCode =conEntity.getTitleCode();
		if(titleCode.length()>3) {
			conEntity.setTitleCode(null);
		}
		//合格专家
		//List<ExpertInfoEntity> listto = new ArrayList<ExpertInfoEntity>();
		List<ExpertInfoEntity> returnToexperts = null;
		if("20".equals(conEntity.geteMethod())) {
			returnToexperts = extractedExpertone(conEntity, secior, anomalyExpert);
		}else {
			returnToexperts = extractedExpertAll(conEntity, secior, anomalyExpert);
		}

		return returnToexperts;
	}
	
	/**
	 *  根据专家类型抽取所有专家
	 * @param conEntity 抽取条件
	 * @param secior 2国 家 1.地方
	 * @param anomalyExpert 异常专家集合
	 * @return
	 */
	public List<ExpertInfoEntity> extractedExpertAll(ConditionEntity conEntity, Integer secior,
			List<String> anomalyExpert) {
		//合格专家
		List<ExpertInfoEntity> listto = new ArrayList<ExpertInfoEntity>();
		List<ExpertInfoEntity> listto1 = new ArrayList<ExpertInfoEntity>();
		List<ExpertInfoEntity> list = mapper.queryExpertsByRules(conEntity);
		//能抽选专家类别人数集合
		List<ExpertNum> listNum = new ArrayList<ExpertNum>();
		String expertTypeNum=conEntity.getExpertTypeNum();
		//查询所有类型的专家参加人数
		List<ExpertInfoEntity> exlist  = new ArrayList<ExpertInfoEntity>();
		exlist= mapper.queryExpertCount(conEntity);
		listNum=splitexpertType(expertTypeNum,exlist,10L);
		for (ExpertInfoEntity expertInfoEntity : list) {
			boolean flag= true;
			for (String expertNum : anomalyExpert) {
				if(expertInfoEntity.getUser_id().equals(expertNum)) {
					flag= false;
					break;
				}
			}
			if(flag) {
			for (ExpertNum string : listNum) {
				flag= false;
				if(expertInfoEntity.getMajor().indexOf(string.getExpertName())!=-1) {
					expertInfoEntity.setMajorselection(string.getExpertName());
					flag= true;
					break;
				}
			}
			}
			if(flag) {
				if(!listto.contains(expertInfoEntity)) {
					listto.add(expertInfoEntity);
				}
				
			}
		
		}
		int cutt=0;
		if(exlist.size()>0) {
			cutt=exlist.get(0).getCount();
		}
		boolean flag=((conEntity.getTotal()-cutt)*SysConstants.EXPERT_EXTRACTION_MULTIPLE)<=listto.size();
		if(!flag) {
			ProjectEntity projectEntity = new ProjectEntity();
			projectEntity.setDecimationBatch(conEntity.getDecimationBatch());
			projectEntity.setStatus(20L);
			mapper.updateProjectStatus(projectEntity);
			updateProjectStatus(conEntity.getDecimationBatch());
			String msg ="";
			if(secior==1) {
				msg=MessageConstants.NOT_ENOUGH_SENIOR_EXPERT;
			}else if(secior==2) {
				msg=MessageConstants.NOT_ENOUGH_PLACE_EXPERT;
			}
			throw new HZWException(msg + "系统缺" + ((conEntity.getTotal()*SysConstants.EXPERT_EXTRACTION_MULTIPLE)-listto.size() )+ "人");
		}
		List<Integer> lr = new ArrayList<Integer>();
		if(listto!=null) {
			 lr = randomNum(conEntity.getTotal().intValue()-cutt, listto.size());
		}


		for (Integer expert : lr) {
			listto1.add(listto.get(expert));
		}
		return listto1;
	}
	
	/**
	 * 
	 * @param conEntity 根据专家类型类型单个抽取专家
	 * @param secior
	 * @param anomalyExpert
	 * @return
	 */
	public List<ExpertInfoEntity> extractedExpertone(ConditionEntity conEntity, Integer secior,
			List<String> anomalyExpert) {
		Map<String, List<ExpertInfoEntity>> map = new HashMap<String, List<ExpertInfoEntity>>();
		List<ExpertInfoEntity> list = mapper.queryExpertsByRules(conEntity);
		//实际抽取专家
		List<ExpertInfoEntity> returnToexperts = new ArrayList<ExpertInfoEntity>();
		//能抽选专家类别人数集合
		List<ExpertNum> listNum = new ArrayList<ExpertNum>();
		//取出专业类别
		String expertTypeNum=conEntity.getExpertTypeNum();
		//查询所有类型的专家参加人数
		List<ExpertInfoEntity> exlist = mapper.queryExpertCount(conEntity);
		listNum=splitexpertType(expertTypeNum,exlist,20L);
		for (ExpertInfoEntity expertInfoEntity : list) {
			boolean flag= true;
			for (String expertNum : anomalyExpert) {
				if(expertInfoEntity.getUser_id().equals(expertNum)) {
					flag= false;
					break;
				}
			}
			if(flag) {
			for (ExpertNum string : listNum) {
				if(expertInfoEntity.getMajor().indexOf(string.getExpertName())!=-1) {
					if(map.get(string.getExpert())==null) {
						map.put(string.getExpert(), new ArrayList<ExpertInfoEntity>());
					}
					expertInfoEntity.setMajorselection(string.getExpertName());
					map.get(string.getExpert()).add(expertInfoEntity);
					string.addNeedNum();
					break;
				}
			}
			}
		}
		//每个专业
		List<ExpertInfoEntity> lst = new ArrayList<ExpertInfoEntity>();
		for (int i=0;i<listNum.size();i++) {
			lst.clear();
			ExpertNum expertNum =listNum.get(i);
			//判断人数是否满足
			if(!expertNum.checkedNum()) {
				ProjectEntity projectEntity = new ProjectEntity();
				projectEntity.setDecimationBatch(conEntity.getDecimationBatch());
				projectEntity.setStatus(20L);
				mapper.updateProjectStatus(projectEntity);
				updateProjectStatus(conEntity.getDecimationBatch());
				String msg ="";
				if(secior==1) {
					msg=MessageConstants.NOT_ENOUGH_SENIOR_EXPERT;
				}else if(secior==2) {
					msg=MessageConstants.NOT_ENOUGH_PLACE_EXPERT;
				}
				throw new HZWException(msg + "系统缺" + expertNum.getLackNum() + "人");
			}
			List<ExpertInfoEntity> lt = map.get(expertNum.getExpert());
			List<Integer> lr = new ArrayList<Integer>();
			if(lt!=null) {
				 lr = randomNum(expertNum.getStillNeedNum(), lt.size());
			}


			for (Integer expert : lr) {
				lst.add(lt.get(expert));
			}
			boolean flag=true;
			//根据选取专业随机专家，有重复专家重新随机
			for (ExpertInfoEntity integer : returnToexperts) {
				for (ExpertInfoEntity integer2 : lst) {
					if(integer.getUser_id().equals(integer2.getUser_id())) {
						flag=false;
						break;
					}
				}
				if(!flag) {
					break;
				}
			}
			if(!flag) {
				i--;
			}else {
				returnToexperts.addAll(lst);
			}

		}
		return returnToexperts;
	}

	/**
	 * 修改项目状态为人数不足
	 * @param decimationBatch
	 */
	private void updateProjectStatus(String decimationBatch) {
		ProjectEntity projectEntity = new ProjectEntity();
		projectEntity.setDecimationBatch(decimationBatch);
		projectEntity.setStatus(20L);
		mapper.updateProjectStatus(projectEntity);
	}

		//查询符合条件sql 返回值 默认0无问题 其他 1 国家人数不足 2 地方人数不足
		public Integer getExtractions(ConditionEntity conEntity,Integer secior, List<String> anomalyExpert){
			//判断是否选中职称全选置空查询，单选直接查询
			String titleCode =conEntity.getTitleCode();
			if(titleCode!=null&&titleCode.length()>3) {
				conEntity.setTitleCode(null);
			}
			//合格专家
			//List<ExpertInfoEntity> listto = new ArrayList<ExpertInfoEntity>();
			if("20".equals(conEntity.geteMethod())) {
				return extractedone(conEntity, secior, anomalyExpert);
			}else {
				return extractedAll(conEntity, secior, anomalyExpert);
			}
			


			
		}
		
		/**
		 * 
		 * @param conEntity
		 * @param secior   返回值 默认0无问题 其他 1 国家人数不足 2 地方人数不足
		 * @param anomalyExpert
		 * @return
		 */
		private Integer extractedAll(ConditionEntity conEntity, Integer secior, List<String> anomalyExpert) {
			List<ExpertInfoEntity> listto = new ArrayList<ExpertInfoEntity>();
			List<ExpertInfoEntity> list = mapper.queryExpertsByRules(conEntity);
			//能抽选专家类别人数集合
			List<ExpertNum> listNum = new ArrayList<ExpertNum>();
			String expertTypeNum=conEntity.getExpertTypeNum();
			//查询所有类型的专家参加人数
			List<ExpertInfoEntity> exlist = new ArrayList<ExpertInfoEntity>();
			exlist = mapper.queryExpertCount(conEntity);
			listNum=splitexpertType(expertTypeNum,exlist,10L);
			for (ExpertInfoEntity expertInfoEntity : list) {
				boolean flag= true;
				for (String expertNum : anomalyExpert) {
					if(expertInfoEntity.getUser_id().equals(expertNum)) {
						flag= false;
						break;
					}
				}
				if(flag) {
				for (ExpertNum string : listNum) {
					flag= false;
					if(expertInfoEntity.getMajor().indexOf(string.getExpertName())!=-1) {
						flag=true;
						break;
					}
				}
				}
				if(flag) {
					
					if(!listto.contains(expertInfoEntity)) {
						listto.add(expertInfoEntity);
					}
					
				}
			
			}
			int cutt=0;
			if(exlist.size()>0) {
				cutt=exlist.get(0).getCount();
			}
			boolean flag=((conEntity.getTotal()-cutt)*SysConstants.EXPERT_EXTRACTION_MULTIPLE)<=listto.size();
			if(!flag) {
				if(secior==1) {
					return 1;
				}else if(secior==2) {
					return 2;
				}
			}
			return 0;
		}

		public Integer extractedone(ConditionEntity conEntity, Integer secior, List<String> anomalyExpert) {
			Map<String, List<ExpertInfoEntity>> map = new HashMap<String, List<ExpertInfoEntity>>();
			List<ExpertInfoEntity> list = mapper.queryExpertsByRules(conEntity);
			//实际抽取专家
			//能抽选专家类别人数集合
			List<ExpertNum> listNum = new ArrayList<ExpertNum>();
			//查询所有类型的专家参加人数
			List<ExpertInfoEntity> exlist = mapper.queryExpertCount(conEntity);
			//取出专业类别
			String expertTypeNum=conEntity.getExpertTypeNum();
			listNum=splitexpertType(expertTypeNum,exlist,20L);
			if(listNum==null) {
				updateProjectStatus(conEntity.getDecimationBatch());
				throw new HZWException(MessageConstants.NOT_ENOUGH_EXPERT);
			}

			for (ExpertInfoEntity expertInfoEntity : list) {
				boolean flag= true;
				for (String expertNum : anomalyExpert) {
					if(expertInfoEntity.getUser_id().equals(expertNum)) {
						flag= false;
						break;
					}
				}
				if(flag) {
				for (ExpertNum string : listNum) {
					if(string.getStillNeedNum()>0) {
					if(expertInfoEntity.getMajor().indexOf(string.getExpertName())!=-1) {
						if(map.get(string.getExpert())==null) {
							map.put(string.getExpert(), new ArrayList<ExpertInfoEntity>());
						}
						map.get(string.getExpert()).add(expertInfoEntity);
						string.addNeedNum();
						break;
					}
					}
				}
				}
			}
		
			
			
			
			
			for (ExpertNum expertNum : listNum) {
				//判断人数是否满足
				if(!expertNum.checkedNum()) {
					if(secior==1) {
						return 1;
					}else if(secior==2) {
						return 2;
					}
				}
				//判断人数
			}
			
			return 0;
		}
	/**
	 * 转换专家抽取的条件
	 * @param expertTypeNum
	 * @param exlist
	 * optator  10单个拆分 20 整体拆分
	 * @return
	 */
	private List<ExpertNum> splitexpertType(String expertTypeNum, List<ExpertInfoEntity> exlist,Long optator) {
		List<ExpertNum> list = new ArrayList<ExpertNum>();
		expertTypeNum=expertTypeNum.replace("；", ";");
		String[] numarr = expertTypeNum.split(";");
		for (String string : numarr) {
			if(string !=null && !"".equals(string.trim())) {
			String[] arr = string.split("&");
			ExpertNum enNum = new ExpertNum();
			enNum.setExpert(arr[1]);
			enNum.setHaveNum(0);
			enNum.setExpertName(arr[3]);
			Integer arr2=" ".equals(arr[2])?0:Integer.valueOf(arr[2]);
				enNum.setNeedNum(arr2);
				enNum.setStillNeedNum(arr2);	
			if(arr2==0&&optator==20L) {
				continue;
			}
			list.add(enNum);
			}
		}
		List<ExpertNum> lie = new ArrayList<ExpertNum>();
		if(optator==20L) {
		for (ExpertNum string : list) {
			boolean flag =true;
			for (ExpertInfoEntity string2 : exlist) {			
				if(string2.getMajorselection()!=null) {
				if(string.getExpertName().equals(string2.getMajorselection())) {
					if(string.getNeedNum()>=string2.getCount()) {
						string.setStillNeedNum(string.getNeedNum()-string2.getCount());
						if(string.getStillNeedNum()==0) {
							flag=false;
							break;
						}
					}
				}
				}
			}
			if(flag) {
				lie.add(string);
			}
		}
		}else {
			lie.addAll(list);
		}
		return lie;
	}

	/**
	 * 查询当前处室当前月份不能参加的专家
	 * @param projectList
	 * @param anomalyExpert
	 */
	private void getNotCurrentMonthExpert(List<ProjectEntity> projectList, List<String> anomalyExpert) {
		//查询当前处室下被屏蔽的专家
				ExpertLimit expertlimit = new ExpertLimit();
				expertlimit.setDepartment(projectList.get(0).getDepartment());
				expertlimit.setLimitTime(DateUtil.dateToString(projectList.get(0).getBidTime(),"yyyy-MM"));
				List<ExpertLimit> expertLimitList = expertLimitService.queryExpertByDepartment(expertlimit);

				for (ExpertLimit expertLimit2 : expertLimitList) {
					anomalyExpert.add(expertLimit2.getExpertId());
				}

	}

	/**
	 * 查询所有暂停的专家
	 * @param anomalyExpert
	 */
	private void getSuspendExpert(List<String> anomalyExpert) {
		// TODO Auto-generated method stub

	}

	/**
	 * 查询当天已经抽取参加及未处理的专家
	 * @param conEntity
	 * @param anomalyExpert
	 */
	private void getOpenTimeToUntreatedExpert(ConditionEntity conEntity, List<String> anomalyExpert) {
		//根据开标时间查询当天已被抽取的专家
		List<ExpertInfoEntity> notContainExpertsList = mapper.queryOpenTimeExper(conEntity.getBidTimeDay());
		 for (ExpertInfoEntity expertInfoEntity : notContainExpertsList) {
			 anomalyExpert.add(expertInfoEntity.getUser_id());
		}
	}

	/**
	 * 专家抽取核心
	 * @param curr
	 * @param count
	 * @return
	 */
	private List<Integer> randomNum(int curr,int count) {
		List<Integer> arr = new ArrayList<Integer>();
		curr=curr*SysConstants.EXPERT_EXTRACTION_MULTIPLE;
		for (int i = 0; i <curr; i++) {
			int random = (int) ((Math.random() * 100) % count);
			if(arr.contains(random)) {
				i--;
				continue;
			}
			arr.add(random);
		}
		return arr;
	}

	public static void main(String[] args) {
		Map<String, String> map = new HashMap<String, String>();
		System.out.println(map.get("1122"));
	}
}
