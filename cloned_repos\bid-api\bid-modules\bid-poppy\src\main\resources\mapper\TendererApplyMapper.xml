<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzw.sunflower.dao.TendererApplyMapper">

	<select id="queryListOnlyMyProjectByPage" resultType="com.hzw.sunflower.dto.MyProjectListDTO">
		SELECT DISTINCT
		p.id,
		p.project_name projectName,
		p.project_number projectNumber,
		p.purchase_name purchaseName,
		p.purchase_number purchaseNumber,
		p.status,
		p.re_tender reTender,
		p.base_project_id baseProjectId
		FROM
		t_project p
		LEFT JOIN t_project_entrust_user tpe  on p.id = tpe.project_id
        LEFT JOIN r_user_department rud on tpe.user_id = rud.user_id  and tpe.department_id = rud.department_id
		WHERE
		p.is_delete = 0
		and tpe.is_delete = 0
		and tpe.type= #{condition.type}
		and tpe.user_id=#{condition.userId}
		<if test="condition.keyWords != null and !&quot;&quot;.equals(condition.keyWords)">
			and CONCAT( IFNULL(`project_number`,''), IFNULL(`purchase_name`,''),
			IFNULL(`purchase_number`,'')) LIKE concat('%',#{condition.keyWords},'%')
		</if>
		ORDER BY p.id DESC
	</select>
	<select id="listMyProject" resultType="com.hzw.sunflower.dto.MyProjectListDTO">
		SELECT DISTINCT
        p.id,
        p.project_name projectName,
        p.project_number projectNumber,
        p.purchase_name purchaseName,
        p.purchase_number purchaseNumber,
        p.principal_company principalCompany,
        p.principal_charge_name principalChargeName,
        p.agent_company agentCompany,
        p.agent_charge_name agentChargeName,
        p.refuse_reason refuseReason,
        p.package_segment_status packageSegmentStatus,
        CASE
        p.package_segment_status
        WHEN 0 THEN 0 ELSE t.segmentNum
        END segmentNum,
        p.status status,
        p.project_status packagesStatus,
        p.project_status_code packagesStatusCode,
        p.created_time created_time,
        p.base_project_id,
        p.re_tender reTender,
        p.entrusted_amount,
        p.project_source,
        IFNULL(ptp.role_type,vp.right_code+1) role_type,
        IFNULL(ptp.is_admin,2) is_admin,
        ptp.user_id,
        paa.status as agent_status,
        t2.has_open_count
        FROM
        t_project p
        LEFT JOIN (
        SELECT
        count( pb.id ) segmentNum,
        pb.project_id
        FROM
        t_project_bid_section pb
        WHERE
        pb.is_delete = 0
        GROUP BY
        pb.project_id
        ) t ON t.project_id = p.id
        left join (SELECT * FROM t_project_tenderee_power ptp WHERE ptp.is_delete = 0 AND ptp.user_id = #{condition.userId}) ptp  on p.id = ptp.project_id
        left join t_project_agent_approval paa on p.id = paa.project_id and paa.is_delete = 0
        LEFT JOIN r_user_department  rud on ptp.user_id = rud.user_id  and rud.is_delete =0
        LEFT JOIN v_project_user_perm vp on p.id = vp.project_id and vp.user_id=#{condition.userId} and vp.depart_id=#{condition.departId}
        LEFT JOIN (
        SELECT p.id,(SELECT COUNT(*) FROM t_project_bid_section tps
        WHERE tps.project_id = p.id AND tps.is_delete = 0
        AND ((`status` &gt;= 30 AND `status` NOT IN ( 70, 71, 72 ))
        OR (former_abnormal_status &gt;= 30 AND `status` IN ( 70, 71, 72 )))) has_open_count
        FROM t_project p WHERE p.is_delete = 0
        ) t2 ON t2.id = p.id
        LEFT JOIN t_project_bid_section pb ON pb.project_id = p.id
        WHERE
        p.is_delete = 0 and is_admin = 1
        and (ptp.is_delete = 0
        or  (1=1  ${condition.dataScope}))
	</select>
</mapper>
