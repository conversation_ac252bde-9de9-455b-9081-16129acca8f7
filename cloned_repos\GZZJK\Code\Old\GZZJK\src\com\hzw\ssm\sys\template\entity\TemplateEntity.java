package com.hzw.ssm.sys.template.entity;

import com.hzw.ssm.fw.base.BaseEntity;
import com.hzw.ssm.fw.util.Page;

/**
 * 信息模板实体类
 * <AUTHOR>
 *
 */
public class TemplateEntity extends BaseEntity {

	private static final long serialVersionUID = 8042703708357357837L;
	/**
	 * 模板id
	 */
	private String templateId;
	/**
	 * 模板名称
	 */
	private String templateName;
	/**
	 * 模板内容
	 */
	private String templateContent;
	/**
	 * 模板类型 1.短信模板  2.语音模板
	 */
	private String  templateType;
	
	/** 是否有效 */
	private Integer isValid; // 逻辑删除标志
	
	/** 分页 */
	private Page page;
	public String getTemplateId() {
		return templateId;
	}
	public void setTemplateId(String templateId) {
		this.templateId = templateId;
	}
	public String getTemplateContent() {
		return templateContent;
	}
	public void setTemplateContent(String templateContent) {
		this.templateContent = templateContent;
	}
	public String getTemplateType() {
		return templateType;
	}
	public void setTemplateType(String templateType) {
		this.templateType = templateType;
	}
	public String getTemplateName() {
		return templateName;
	}
	public void setTemplateName(String templateName) {
		this.templateName = templateName;
	}
	public Page getPage() {
		return page;
	}
	public void setPage(Page page) {
		this.page = page;
	}
	public Integer getIsValid() {
		return isValid;
	}
	public void setIsValid(Integer isValid) {
		this.isValid = isValid;
	}
}
