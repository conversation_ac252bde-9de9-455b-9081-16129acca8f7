// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'BulletinListQueryModel.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

BulletinListQueryModel _$BulletinListQueryModelFromJson(
    Map<String, dynamic> json) {
  return BulletinListQueryModel(
    bidType: (json['bidType'] as List)?.map((e) => e as String)?.toList(),
    bulletinTypeName:
        (json['bulletinTypeName'] as List)?.map((e) => e as String)?.toList(),
    classifyName:
        (json['classifyName'] as List)?.map((e) => e as String)?.toList(),
    currentPage: json['currentPage'] as String,
    fundRange: (json['fundRange'] as List)?.map((e) => e as String)?.toList(),
    fundSource: (json['fundSource'] as List)?.map((e) => e as String)?.toList(),
    industryCode:
        (json['industryCode'] as List)?.map((e) => e as String)?.toList(),
    keyWord: (json['keyWord'] as List)?.map((e) => e as String)?.toList(),
    pageSize: json['pageSize'] as int,
    platformCode:
        (json['platformCode'] as List)?.map((e) => e as String)?.toList(),
    regionCode: (json['regionCode'] as List)?.map((e) => e as String)?.toList(),
    uid: json['uid'] as String,
  );
}

Map<String, dynamic> _$BulletinListQueryModelToJson(
        BulletinListQueryModel instance) =>
    <String, dynamic>{
      'bidType': instance.bidType,
      'bulletinTypeName': instance.bulletinTypeName,
      'classifyName': instance.classifyName,
      'currentPage': instance.currentPage,
      'fundRange': instance.fundRange,
      'fundSource': instance.fundSource,
      'industryCode': instance.industryCode,
      'keyWord': instance.keyWord,
      'pageSize': instance.pageSize,
      'platformCode': instance.platformCode,
      'regionCode': instance.regionCode,
      'uid': instance.uid,
    };
