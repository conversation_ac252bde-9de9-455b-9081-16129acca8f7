package com.hzw.sunflower.controller.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2021/7/22 9:04
 * @description：
 * @modified By：`
 * @version: 1.0
 */
@Data
public class ProcessRecordReq {


    @ApiModelProperty(value = "业务code （winBULLETIN公告流程code，bidwinpeople确认中标人流程code，winNotice通知书流程code）")
    private String businessCode;


    @ApiModelProperty(value = "业务主键")
    private String businessId;


    @ApiModelProperty(value = "是否显示审核流程")
    private Integer isShowExamine = 1;

    @ApiModelProperty(value = "业务集合")
    private List<ProcessRecordListReq> businessList;
}
