package com.hzw.sunflower.constant.constantenum;


public enum PendingItemEnum {

    PENDING(1, "待处理事项"),
    RETURN(2,"退回待处理");



    private Integer type;
    private String desc;

    PendingItemEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
