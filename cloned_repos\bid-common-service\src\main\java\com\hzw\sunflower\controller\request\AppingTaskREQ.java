package com.hzw.sunflower.controller.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class AppingTaskREQ {

    /**
     * 关键字
     */
    @ApiModelProperty(value = "搜索关键字")
    private String keywords;

    /**
     * 类别
     */
    @ApiModelProperty(value = "类别")
    private String processDefinitionKey;


    @ApiModelProperty(value = "当前页")
    private Integer page;

    @ApiModelProperty(value = "每页大小")
    private Integer pageSize;

    @ApiModelProperty(value = "项目ID")
    private Long projectId;

    @ApiModelProperty(value = "是否过滤")
    private Boolean isFilter = true;

    //流程实例ID集合
    @ApiModelProperty(
            value = "流程实例ID集合"
    )
    private List<String> processInstanceIds;

    @ApiModelProperty(value = "小铃铛刷新类型 1：自动刷新，2手动刷新")
    private Integer bellType;


}
