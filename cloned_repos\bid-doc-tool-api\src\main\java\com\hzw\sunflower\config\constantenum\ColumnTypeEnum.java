package com.hzw.sunflower.config.constantenum;

/**
 * <AUTHOR>
 * @date ：Created in 2022/07/27 9:23
 * @description：字段类型
 * @version: 1.0
 */
public enum ColumnTypeEnum {

    INPUT("input", "单行文本"),
    TEXTAREA("textarea", "多行文本"),
    RADIO("radio", "单选"),
    DATE("date", "时间选择-年月日"),
    DATETIME("datetime", "时间选择-年月日时分秒"),
    YEAR_RANGE("yearRange", "年份区间"),
    DATE_RANGE("dateRange", "日期区间-年月日"),
    TABLE("table", "表格"),
    OTHER("other", "其他");

    private String type;
    private String desc;

    ColumnTypeEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
