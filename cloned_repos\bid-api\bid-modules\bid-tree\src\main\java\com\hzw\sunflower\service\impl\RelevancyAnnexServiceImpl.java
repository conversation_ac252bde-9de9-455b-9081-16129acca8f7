package com.hzw.sunflower.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzw.sunflower.constant.constantenum.AnnexTypeEnum;
import com.hzw.sunflower.controller.request.AttachmentRelation;
import com.hzw.sunflower.controller.request.RelevancyAnnexREQ;
import com.hzw.sunflower.controller.response.EmailFilesVo;
import com.hzw.sunflower.dao.RelevancyAnnexMapper;
import com.hzw.sunflower.dto.AnnexDTO;
import com.hzw.sunflower.dto.RelevancyAnnexDTO;
import com.hzw.sunflower.entity.OssFile;
import com.hzw.sunflower.entity.ProjectBidNotice;
import com.hzw.sunflower.entity.RelevancyAnnex;
import com.hzw.sunflower.entity.condition.RelevancyAnnexCondition;
import com.hzw.sunflower.service.EmailInfoService;
import com.hzw.sunflower.service.OssFileService;
import com.hzw.sunflower.service.ProjectBidNoticeService;
import com.hzw.sunflower.service.RelevancyAnnexService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;


/**
 * TRelevancyAnnexService接口实现
 *
 * <AUTHOR>
 * @version 1.0.0 2021-04-07
 */
@Service
public class RelevancyAnnexServiceImpl extends ServiceImpl<RelevancyAnnexMapper, RelevancyAnnex> implements RelevancyAnnexService {

    @Autowired
    private ProjectBidNoticeService tProjectBidNoticeService;

    @Autowired
    private OssFileService ossFileService;

    @Autowired
    private EmailInfoService emailInfoService;


    @Override
    public List<AnnexDTO> findNoticeAnnexsByNoticeIdOne(Long noticeId) {
        List<AnnexDTO> list = baseMapper.findNoticeAnnexs(noticeId);
        return list;
    }


    @Override
    public List<AnnexDTO> findNoticeAnnexsByNoticeId(Long noticeId) {
        List<AnnexDTO> list = baseMapper.findNoticeAnnexs(noticeId);
        // 2023-03-14 补充邮件附件
        List<EmailFilesVo> emailFiles = emailInfoService.getEmailFiles(AnnexTypeEnum.BIDNOTICE.getValue(), noticeId);
        if(null != emailFiles && emailFiles.size() > 0){
            for (EmailFilesVo emailFile : emailFiles) {
                AnnexDTO annexDTO=new AnnexDTO();
                annexDTO.setId(emailFile.getOssId());
                annexDTO.setKey(emailFile.getOssFileKey());
                annexDTO.setName(emailFile.getOssFileName());
                list.add(annexDTO);
            }
        }
        // 补充邮件正文附件
        List<EmailFilesVo> contentFiles = emailInfoService.getEmailContentFiles(AnnexTypeEnum.BIDNOTICE.getValue(), noticeId);
        if(null != contentFiles && contentFiles.size() > 0){
            for (EmailFilesVo emailFile : contentFiles) {
                AnnexDTO annexDTO=new AnnexDTO();
                annexDTO.setId(emailFile.getOssId());
                annexDTO.setKey(emailFile.getOssFileKey());
                annexDTO.setName(emailFile.getOssFileName());
                list.add(annexDTO);
            }
        }
        return list;
    }

    /**
     *
     * @param condition 查询条件
     * @return 分页数据
     * 根据条件分页查询列表
     */
    @Override
    public IPage<RelevancyAnnex> findInfoByCondition(RelevancyAnnexCondition condition) {
        IPage<RelevancyAnnex> page = condition.buildPage();
        QueryWrapper<RelevancyAnnex> queryWrapper = condition.buildQueryWrapper(RelevancyAnnex.class);
        return this.page(page, queryWrapper);
    }

    /**
     * 根据主键ID查询
     *
     * @param id 主键ID
     * @return
     */
    @Override
    public RelevancyAnnex getInfoById(Long id) {
        return this.getById(id);
    }

    /**
     * 新增
     *
     * @param relevancyAnnex
     * @return 是否成功
     */
    @Override
    public Boolean addInfo(RelevancyAnnex relevancyAnnex) {
        return this.save(relevancyAnnex);
    }

    /**
     * 修改
     *
     * @param relevancyAnnex
     * @return 是否成功
     */
    @Override
    public Boolean updateInfo(RelevancyAnnex relevancyAnnex) {
        return this.updateById(relevancyAnnex);
    }

    /**
     * 根据主键ID删除
     *
     * @param id 主键ID
     * @return 是否成功
     */
    @Override
    public Boolean deleteById(Long id) {
        return this.removeById(id);
    }

    /**
     * 根据主键ID列表批量删除
     *
     * @param idList 主键ID列表
     * @return 是否成功
     */
    @Override
    public Boolean deleteByIds(List<Long> idList) {
        return this.removeByIds(idList);
    }

    /**
     * 保存/编辑 附件
     *
     * @param relevancyAnnexREQ
     * @return
     */
    @Override
    public boolean saveRelevancyAnnexInfo(RelevancyAnnexREQ relevancyAnnexREQ) {
        if (null == relevancyAnnexREQ) {
            return false;
        } else {
            //删除包关联附件
            if (!CollectionUtil.isEmpty(relevancyAnnexREQ.getDeleteSectionId())) {
                //有包件修改为无包件时, 根据项目ID,逻辑删除标段
                LambdaUpdateWrapper<RelevancyAnnex> deleteSection = new LambdaUpdateWrapper<>();
                deleteSection.in(RelevancyAnnex::getSectionId, relevancyAnnexREQ.getDeleteSectionId());
                this.baseMapper.delete(deleteSection);
            }
            //删除附件
            if (!CollectionUtil.isEmpty(relevancyAnnexREQ.getDeleteFileId())) {
                //有包件修改为无包件时, 根据项目ID,逻辑删除标段
                LambdaUpdateWrapper<RelevancyAnnex> deleteFile = new LambdaUpdateWrapper<>();
                deleteFile.in(RelevancyAnnex::getFileId, relevancyAnnexREQ.getDeleteFileId());
                int delete = this.baseMapper.delete(deleteFile);
            }
            //保存附件信息
            boolean flag = true;
            if (!CollectionUtil.isEmpty(relevancyAnnexREQ.getRelevancyAnnex())) {
                List<RelevancyAnnex> list = new ArrayList<>();
                for (AttachmentRelation relation : relevancyAnnexREQ.getRelevancyAnnex()) {
                    RelevancyAnnex relevancyAnnex = new RelevancyAnnex();
                    relevancyAnnex.setId(relation.getAttachmentId());
                    relevancyAnnex.setProjectId(relation.getProjectId());
                    relevancyAnnex.setSectionId(relation.getSectionId());
                    relevancyAnnex.setFileId(relation.getFileId());
                    relevancyAnnex.setFileType(AnnexTypeEnum.BIDACCESSORY.getValue());
                    list.add(relevancyAnnex);
                }
                flag = saveOrUpdateBatch(list);
            }
            return flag;
        }
    }

    @Override
    public List<RelevancyAnnexDTO> getInfoByDocId(Long docId) {
        return this.baseMapper.getInfoByDocId(docId);
    }
}
