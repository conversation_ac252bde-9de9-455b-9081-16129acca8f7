package com.hzw.bid.util;

import com.alibaba.fastjson.JSONObject;
import com.hzw.bid.project.controller.response.BaiduImageTextVO;
import com.hzw.bid.util.entity.BaiduApi;
import com.hzw.bid.util.entity.ImageTextProgress;
import lombok.Data;
import org.apache.http.NameValuePair;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpHead;
import org.apache.http.message.BasicNameValuePair;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;

/**
 * @ClassName:ImageTextUtil
 * @Auther: lijinxin
 * @Description: 百度图文转换工具类
 * @Date: 2023/9/25 09:43
 * @Version: v1.0
 */
@Data
@Component
public class ImageTextUtil {


    private static final BaiduApi bean = SpringUtils.getBean(BaiduApi.class);
    /**
     * 获取token
     */
    // 获取token
    public static String gettoken() throws Exception {

        String appkey =  bean.getClientId();
        String sket = bean.getClientSecret();
        String loginUrl = bean.getLoginUrl();
        HttpHead reqHeader = new HttpHead();
        reqHeader.setHeader("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8");
        String apiUri = loginUrl.replace("{client_id}",appkey).replace("{client_secret}",sket);
        List<NameValuePair> params = new ArrayList<>();
        UrlEncodedFormEntity entity = new UrlEncodedFormEntity(params, GeneralConstants.UTF8);
        String jsonResponse = HttpHelper.httpPost(apiUri, reqHeader.getAllHeaders(), entity);
        String accessToken = JSONObject.parseObject(jsonResponse).get("access_token").toString();
        System.out.println(accessToken);
        return  accessToken;
    }

    /**
     * 处理文件
     */
    // 处理pdf
    public static BaiduImageTextVO analysis(String filePath) throws Exception {
        String gettoken = gettoken();
        String apiUri =  bean.getAnalysisUrl()+gettoken;
        HttpHead reqHeader = new HttpHead();
        reqHeader.setHeader("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8");
        List<NameValuePair> params = new ArrayList<>();
        String encodedPdf = getFileContentAsBase64(filePath,true);
        System.out.println(encodedPdf);
        params.add(new BasicNameValuePair("pdf_file", encodedPdf));
        UrlEncodedFormEntity entity = new UrlEncodedFormEntity(params, GeneralConstants.UTF8);
        String jsonResponse = HttpHelper.httpPost(apiUri, reqHeader.getAllHeaders(), entity);
        JSONObject jsonObject = JSONObject.parseObject(jsonResponse);
        Boolean success = jsonObject.getBoolean("success");
        BaiduImageTextVO imageTextVO = new BaiduImageTextVO();
        if(success){ // 发送成功
            imageTextVO.setTaskId( jsonObject.getJSONObject("result").get("task_id").toString());
        }
        imageTextVO.setJsonResponse(jsonResponse);
        return imageTextVO;
    }

    /**
     * 文件处理
     * @param path
     * @param urlEncode
     * @return
     * @throws IOException
     */
    static String getFileContentAsBase64(String path, boolean urlEncode) throws IOException {
        byte[] b = Files.readAllBytes(Paths.get(path));
        String base64 = Base64.getEncoder().encodeToString(b);
        if (urlEncode) {
            base64 = URLEncoder.encode(base64, "utf-8");
        }
        return base64;
    }

    /**
     * 获取文件处理状态
     */
    public static ImageTextProgress analysisProgress(String taskId) throws Exception {
        ImageTextProgress imp = new ImageTextProgress();
        String gettoken = gettoken();
        String apiUri = bean.getAnalysisProgressUrl() + gettoken;

        HttpHead reqHeader = new HttpHead();
        reqHeader.setHeader("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8");
        List<NameValuePair> params = new ArrayList<>();
        params.add(new BasicNameValuePair("task_id", taskId));
        UrlEncodedFormEntity entity = new UrlEncodedFormEntity(params, GeneralConstants.UTF8);
        String jsonResponse = HttpHelper.httpPost(apiUri, reqHeader.getAllHeaders(), entity);
        imp.setJsonString(jsonResponse);
        imp.setTaskId(taskId);
        imp.setRetCode(JSONObject.parseObject(jsonResponse).getJSONObject("result").getInteger("ret_code"));
        imp.setPercent(JSONObject.parseObject(jsonResponse).getJSONObject("result").getInteger("percent"));
        imp.setWord(JSONObject.parseObject(jsonResponse).getJSONObject("result").getJSONObject("result_data").get("word").toString());
        imp.setExcel(JSONObject.parseObject(jsonResponse).getJSONObject("result").getJSONObject("result_data").get("excel").toString());
        return imp;
    }


}
