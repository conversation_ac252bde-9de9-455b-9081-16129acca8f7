package com.hzw.ssm.expert.entity;

import com.hzw.ssm.fw.base.BaseEntity;

/**
 * 专家评标专业表
 * <AUTHOR>
 * @date 2014-10-09
 */
public class ExpertMajorEntity extends BaseEntity {
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 7381537442710540283L;

	private String id;//主键
	
	private String user_id;//用户编号
	
	private String major;//评标专业id
	
	private String major_name;//专业名称

	private String getTime;//时间

	private Long year;//从事专业年限
	
	private String cou_major;//评标专业(国家)
	
	private String cou_major_name;//专业名称(国家)

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getUser_id() {
		return user_id;
	}

	public void setUser_id(String userId) {
		user_id = userId;
	}

	public String getMajor() {
		return major;
	}

	public void setMajor(String major) {
		this.major = major;
	}

	public Long getYear() {
		return year;
	}

	public void setYear(Long year) {
		this.year = year;
	}

	public String getMajor_name() {
		return major_name;
	}

	public void setMajor_name(String majorName) {
		major_name = majorName;
	}

	public String getCou_major() {
		return cou_major;
	}

	public void setCou_major(String couMajor) {
		cou_major = couMajor;
	}

	public String getCou_major_name() {
		return cou_major_name;
	}

	public void setCou_major_name(String couMajorName) {
		cou_major_name = couMajorName;
	}

	public String getGetTime() {
		return getTime;
	}

	public void setGetTime(String getTime) {
		this.getTime = getTime;
	}
}
