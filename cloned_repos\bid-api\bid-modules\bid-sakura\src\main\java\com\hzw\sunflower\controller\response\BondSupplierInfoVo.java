package com.hzw.sunflower.controller.response;

import com.hzw.sunflower.controller.user.response.SpecialTicketVo;
import com.hzw.sunflower.entity.BondOfflineCompany;
import com.hzw.sunflower.entity.SpecialTicket;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class BondSupplierInfoVo implements Serializable {

    @ApiModelProperty(value = "标段id")
    private Long sectionId;

    @ApiModelProperty(value = "企业id")
    private Long companyId;

    @ApiModelProperty(value = "退还id")
    private Long refundId;

    @ApiModelProperty(value = "供应商名称")
    private String companyName;

    @ApiModelProperty(value = "是否为境外企业 0.否 1.是")
    private Integer isCompanyAbroad;

    @ApiModelProperty(value = "保证金金额")
    private BigDecimal bondMoney;

    @ApiModelProperty(value = "代理服务费")
    private BigDecimal agencyFee;

    @ApiModelProperty(value = "原代理服务费")
    private BigDecimal formerAgencyFee;

    @ApiModelProperty(value = "退还方式 1：收取并退还剩余保证金  2：收取并保留剩余保证金  3：收取并支持再次收取   4：收入并结束收取")
    private Integer refundMode;

    @ApiModelProperty(value = "代理服务费收取对象 0投标人 1招标人")
    private Integer agencyFeeObj;

    @ApiModelProperty(value = "代理服务费类型 0不收取 1:收取")
    private Integer agencyCostFree;

    @ApiModelProperty(value = "代理服务费类型 0不收取 1定额 2比例 3 分批")
    private Integer feeType;

    @ApiModelProperty(value = "保证金类型 0不收取 1定额 2比例 ")
    private Integer bondType;

    @ApiModelProperty(value = "服务费收取方式：1：保证金转代理服务费  2：保证金全额转代理服务费并补差价  3：保证金全额退还，代理服务费另汇")
    private Integer agencyFeeType;

    @ApiModelProperty(value = "付款凭证/授权函")
    private String fileIds;

    @ApiModelProperty(value = "保证金退还金额")
    private BigDecimal refundMoney;

    @ApiModelProperty(value = "退还状态")
    private Integer refundStatus;

    @ApiModelProperty(value = "关联状态")
    private Integer relationStatus;

    @ApiModelProperty(value = "中标通知书状态")
    private Integer noticeStatus;

    @ApiModelProperty(value = "退回原因")
    private String remark;

    @ApiModelProperty(value = "利息")
    private BigDecimal rates;

    @ApiModelProperty(value = "中标信息")
    private BidWinSupplierVo bidWinInfo;

    @ApiModelProperty(value = "流水id 1,2,3")
    private String waterIds;

    @ApiModelProperty(value = "拆分表id 1,2,3")
    private String splitIds;

    @ApiModelProperty(value = "申请/异常关联状态 1.待处理 2.已退回 3.已处理")
    private Integer applyRelationStatus;

    @ApiModelProperty(value = "退还明细")
    private List<BondRefundDetailsVo> bondRefundDetails;

    @ApiModelProperty("是否开通下载权限，用于判断是否已购标 0.否 1.是")
    private Integer downloadFlag;

    @ApiModelProperty("退还时间")
    private Date returnTime;

    @ApiModelProperty("退还回单附件")
    private String receiptFiles;

    @ApiModelProperty("报名情况表id")
    private Long applyInfoId;

    @ApiModelProperty("保证金备注（退还）")
    private String bondRemark;

    @ApiModelProperty("是否已收代理服务费  1：是  2：否")
    private Integer isAgencyFee;

    @ApiModelProperty("专票信息")
    private SpecialTicketVo specialTicket;

    @ApiModelProperty("退还流程code")
    private String refundProcessCode;

    @ApiModelProperty("财务经办标识 1.可撤回 0.不可撤回")
    private Integer cwjbFlag;

    @ApiModelProperty("线下项目保证金退还供应商id")
    private Long bondOfflineCompanyId;

    @ApiModelProperty("线下项目供应商id")
    private Long offlineCompanyId;

    @ApiModelProperty("线下项目保证金退还供应商数据")
    private BondOfflineCompanyVo bondOfflineCompany;
}
