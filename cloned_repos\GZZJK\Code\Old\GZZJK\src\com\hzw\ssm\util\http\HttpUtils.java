/*
 * 公司名称：江苏华招网
 * 版权信息：江苏华招网版权
 * 描述：TODO
 * 文件名称：HttpUtils.java
 * 修改时间：2016年9月27日
 * 修改人：尤拍拍
 * 修改内容：
 * 跟踪单号：
 * 修改单号 ：
 */

package com.hzw.ssm.util.http;

import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.io.Writer;
import java.net.HttpURLConnection;
import java.net.URL;
import java.security.KeyManagementException;
import java.security.NoSuchAlgorithmException;

import javax.net.ssl.HostnameVerifier;
import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSession;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;

import com.hzw.ssm.util.empty.EmptyUtils;

/**
 * <一句话功能简述> http请求工具
 * <功能详细描述>
 * <AUTHOR>
 * @version V1.0[产品/模块版本]
 */
public class HttpUtils {

    public static String request(HttpConfig httpConfig, String urlStr, String paramStr)
            throws IOException, NoSuchAlgorithmException, KeyManagementException {

        StringBuffer sb = new StringBuffer();
        URL url = new URL(urlStr);
        HttpURLConnection conn = null;
        if (!urlStr.startsWith("https")) {
            conn = (HttpURLConnection) url.openConnection();
        } else {
            TrustManager[] trustAllCerts = new TrustManager[] { new X509TrustManager() {
                public java.security.cert.X509Certificate[] getAcceptedIssuers() {
                    return null;
                }

                public void checkClientTrusted(java.security.cert.X509Certificate[] certs,
                        String authType) {
                }

                public void checkServerTrusted(java.security.cert.X509Certificate[] certs,
                        String authType) {
                }
            } };
            SSLContext sc = SSLContext.getInstance("SSL");
            sc.init(null, trustAllCerts, new java.security.SecureRandom());
            HttpsURLConnection.setDefaultSSLSocketFactory(sc.getSocketFactory());
            HttpsURLConnection.setDefaultHostnameVerifier(new HostnameVerifier() {
                public boolean verify(String urlHostName, SSLSession session) {
                    return true;
                }
            });

            HttpsURLConnection conn1 = (HttpsURLConnection) url.openConnection();
            conn1.setHostnameVerifier(new HostnameVerifier() {
                public boolean verify(String arg0, SSLSession arg1) {
                    return true;
                }
            });
            conn = conn1;
        }

        //http正文内，因此需要设为true, 默认情况下是false;   
        conn.setDoOutput(true);
        //设置是否从httpUrlConnection读入，默认情况下是true; 
        conn.setDoInput(true);
        // Post 请求不能使用缓存   
        conn.setUseCaches(false);
        //设置超时
        if (httpConfig.getConntectionTimeOut() >= 0) {
            conn.setConnectTimeout(httpConfig.getConntectionTimeOut() * 1000);
        }
        if (httpConfig.getReadTimeOut() >= 0) {
            conn.setReadTimeout(httpConfig.getReadTimeOut() * 1000);
        }
        conn.setRequestMethod("GET");
        // 设定传送的内容类型是可序列化的java对象   
        // (如果不设此项,在传送序列化对象时,当WEB服务默认的不是这种类型时可能抛java.io.EOFException)   
        conn.setRequestProperty("Content-type", httpConfig.getContentType());
        if(!EmptyUtils.isEmpty(paramStr)){
            Writer writer = new BufferedWriter(
                    new OutputStreamWriter(conn.getOutputStream(), httpConfig.getDataEncoding()));
            writer.write(paramStr);
            writer.flush();
            writer.close();
        }

        BufferedReader reader = new BufferedReader(
                new InputStreamReader(conn.getInputStream(), httpConfig.getDataEncoding()));
        char[] buf = new char[512];
        int readCount = reader.read(buf, 0, 512);
        while (readCount >= 0) {
            sb.append(buf, 0, readCount);
            readCount = reader.read(buf, 0, 512);
        }
        String line = reader.readLine();
        while (line != null) {
            sb.append(line);
            line = reader.readLine();
        }
        reader.close();

        return sb.toString();
    }

}
