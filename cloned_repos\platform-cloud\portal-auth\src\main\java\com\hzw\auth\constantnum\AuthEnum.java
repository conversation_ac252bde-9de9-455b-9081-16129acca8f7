package com.hzw.auth.constantnum;

public enum AuthEnum {

    ISNOTUPDATE(1,"准入"),
    ISUPDATE(2,"变更"),
    isLogin(0,"未登录"),
    notLogin(1,"已登录")
    ;

    private Integer type;
    private String desc;

    AuthEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
