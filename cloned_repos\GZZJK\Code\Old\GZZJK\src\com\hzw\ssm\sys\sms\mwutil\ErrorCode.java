package com.hzw.ssm.sys.sms.mwutil;

public enum ErrorCode {

	ERRORCODE_300001("-300001", "账号格式不合法"),
	ERRORCODE_300002("-300002", "密码格式不合法"),
	ERRORCODE_300003("-300003", "IP和端口信息不合法"),
	ERRORCODE_300004("-300004", "IP不合法"),
	ERRORCODE_300005("-300005", "端口不合法"),
	ERRORCODE_300006("-300006", "手机号码格式不合法"),
	ERRORCODE_300007("-300007", "发送内容长度不合法"),
	ERRORCODE_300008("-300008", "扩展子号不合法"),
	ERRORCODE_300009("-300009", "流水号不合法"),
	ERRORCODE_300010("-300010", "密码加密失败"),
	ERRORCODE_300011("-300011", "业务类型不合法"),
	ERRORCODE_300012("-300012", "扩展数据不合法"),
	ERRORCODE_300013("-300013", "发送优先级不合法"),
	ERRORCODE_300014("-300014", "个性化群发发送条数不在0到100之间"),
	ERRORCODE_300015("-300015", "Message对象为空"),
	ERRORCODE_300016("-300016", "msgid参数为空或者msgid长度不为0"),
	ERRORCODE_300017("-300017", "账号或者密码错误"),
	ERRORCODE_300018("-300018", "发送类型错误"),
	ERRORCODE_300019("-300019", "上行mos为空或者mos集合大于0"),
	ERRORCODE_300020("-300020", "状态报告rpts为空或者rpts集合大于0"),
	ERRORCODE_300021("-300021", "请求路径为空"),
	ERRORCODE_300023("-300023", "是否需要日志设置不合法"),
	ERRORCODE_300024("-300024", "相同内容群发时，手机号码超过最大支持数量(1000)"),
	ERRORCODE_300025("-300025", "内容编码失败"),
	ERRORCODE_310001("-310001", "单条发送失败"),
	ERRORCODE_310002("-310002", "相同内容发送失败"),
	ERRORCODE_310003("-310003", "个性发送失败"),
	ERRORCODE_310004("-310004", "获取上行失败"),
	ERRORCODE_310005("-310005", "获取状态报告失败"),
	ERRORCODE_310006("-310006", "获取余额失败"),
	ERRORCODE_310007("-310007", "设置账号信息失败"),
	ERRORCODE_310008("-310008", "账号不存在或者账号不可用"),
	ERRORCODE_310009("-310009", "无可用的账号"),
	ERRORCODE_310010("-310010", "设置账号检查线程启动失败"),
	ERRORCODE_310011("-310011", "设置账号检查线程停止失败"),
	ERRORCODE_310012("-310012", "设置全局参数失败"),
	ERRORCODE_310013("-310013", "移除账号失败"),
	ERRORCODE_310014("-310014", "该账号在账号列表不存在，无需移除"),
	ERRORCODE_310099("-310099", "http请求失败"),
	ERRORCODE_310100("-310100","未知错误请检查代码"),
	SUSSCESSCODE_0("0","发送成功");

	private String errorCode;

	private String message;

	public int getErrorCode() {
		int code = Integer.valueOf(errorCode);
		return code;
	}

	public void setErrorCode(String errorCode) {
		this.errorCode = errorCode;
	}

	public String getMessage() {
		return message;
	}

	public void setMessage(String message) {
		this.message = message;
	}

	private ErrorCode(String value, String desc) {
		this.errorCode = value;
		this.message = desc;
	}

}
