package com.hzw.sunflower.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hzw.sunflower.common.BaseController;
import com.hzw.sunflower.util.BeanListUtil;
import com.hzw.sunflower.common.Paging;
import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.controller.request.LenovoUserQueryReq;
import com.hzw.sunflower.controller.response.LenovoUserQueryVO;
import com.hzw.sunflower.controller.response.CompanySuperviseVo;
import com.hzw.sunflower.controller.response.RoleVo;
import com.hzw.sunflower.controller.response.UserTableVo;
import com.hzw.sunflower.dto.AgentUserDTO;
import com.hzw.sunflower.entity.condition.CompanyOpenCondition;
import com.hzw.sunflower.entity.condition.TUserIdentityOpenCondition;
import com.hzw.sunflower.service.CommonOpenService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * 对外开发 Controller
 *
 * <AUTHOR>
 * @version 1.0.0 2021-04-07
 */
@Api(tags = "对外开发 服务")
@RestController
@RequestMapping("/open")
public class CommonOpenController extends BaseController {

    @Autowired
    private CommonOpenService openService;


    /**
     * 根据token获取用户权限
     *
     * @return
     */
    @ApiOperation(value = "根据token获取用户权限")
    @GetMapping("/getUserRoleByToken")
    public Result<List<RoleVo>> getUserRoleByToken() {
        List<RoleVo> roleVos = openService.getUserRoleByUserId(getJwtUser().getUserId());
        return Result.ok(roleVos);
    }



    /**
     * 招标人管理
     * param    keywords(企业名称，信用代码，法定代表人)；
     * @return
     */
    @ApiOperation(value = "招标人管理")
    @ApiImplicitParam(name = "condition", value = "keywords(企业名称，信用代码，法定代表人)；", required = true, dataType = "CompanyOpenCondition", paramType = "body")
    @PostMapping("/getBidderList")
    public Result<Paging<CompanySuperviseVo>> getBidderList(@RequestBody CompanyOpenCondition condition) {
        IPage<CompanySuperviseVo> companySupervise = openService.getBidderList(condition);
        return Result.ok(Paging.buildPaging(companySupervise));
    }


    /**
     * 招标人用户列表
     *
     * @param con companyId  keywords（姓名/联系方式/企业名称）
     * @return
     */
    @ApiOperation(value = "招标人用户列表")
    @ApiImplicitParam(name = "con", value = "companyId  keywords（姓名/联系方式/企业名称）", required = true, dataType = "TUserIdentityOpenCondition", paramType = "body")
    @PostMapping("/getBiddingUserList")
    public Result<Paging<UserTableVo>> getBiddingUserList(@RequestBody TUserIdentityOpenCondition con) {
        IPage<UserTableVo> page = openService.getBiddingUserList(con);
        return Result.ok(Paging.buildPaging(page));
    }



    /**
     * 供应商管理
     * param    keywords(企业名称，信用代码，法定代表人)；
     * @return
     */
    @ApiOperation(value = "供应商管理")
    @ApiImplicitParam(name = "condition", value = "keywords(企业名称，信用代码，法定代表人)；", required = true, dataType = "CompanyOpenCondition", paramType = "body")
    @PostMapping("/getSupplierList")
    public Result<Paging<CompanySuperviseVo>> getSupplierList(@RequestBody CompanyOpenCondition condition) {
        IPage<CompanySuperviseVo> page = openService.getSupplierList(condition);
        return Result.ok(Paging.buildPaging(page));
    }

    /**
     * 供应商人员列表
     *
     * @param con companyId  keywords（姓名/联系方式/企业名称）
     * @return
     */
    @ApiOperation(value = "供应商人员列表")
    @ApiImplicitParam(name = "con", value = "companyId  keywords（姓名/联系方式/企业名称）", required = true, dataType = "TUserIdentityOpenCondition", paramType = "body")
    @PostMapping("/getSupplierUserList")
    public Result<Paging<UserTableVo>> getSupplierUserList(@RequestBody TUserIdentityOpenCondition con) {
        IPage<UserTableVo> page = openService.getSupplierUserList(con);
        return Result.ok(Paging.buildPaging(page));
    }



    /**
     * 代理机构管理
     * param    keywords(企业名称，信用代码，法定代表人)；
     * @return
     */
    @ApiOperation(value = "代理机构管理")
    @ApiImplicitParam(name = "condition", value = "keywords(企业名称，信用代码，法定代表人)；", required = true, dataType = "CompanyOpenCondition", paramType = "body")
    @PostMapping("/getAgencyList")
    public Result<Paging<CompanySuperviseVo>> getAgencyList(@RequestBody CompanyOpenCondition condition) {
        IPage<CompanySuperviseVo> page = openService.getAgencyList(condition);
        return Result.ok(Paging.buildPaging(page));
    }


    /**
     * 代理机构人员列表
     *
     * @param con companyId  keywords（姓名/联系方式/企业名称）
     * @return
     */
    @ApiOperation(value = "代理机构人员列表")
    @ApiImplicitParam(name = "con", value = "companyId  keywords（姓名/联系方式/企业名称）", required = true, dataType = "TUserIdentityOpenCondition", paramType = "body")
    @PostMapping("/getAgencyUserList")
    public Result<Paging<UserTableVo>> getAgencyUserList(@RequestBody TUserIdentityOpenCondition con) {
        IPage<UserTableVo> page = openService.getAgencyUserList(con);
        return Result.ok(Paging.buildPaging(page));
    }


    /**
     * 委托人信息联想功能
     * @param lenovoUserQueryReq
     * @return
     */
    @ApiOperation(value = "委托人信息联想功能 ")
    @ApiImplicitParam(name = "lenovoUserQueryReq", value = "委托单位信息 ", required = true, dataType = "LenovoUserQueryReq", paramType = "body")
    @PostMapping(value = "/queryBidderInfo")
    public Result<List<LenovoUserQueryVO>> queryBidderInfo(@RequestBody LenovoUserQueryReq lenovoUserQueryReq) {
        List<AgentUserDTO> dto = openService.queryBidderInfo(lenovoUserQueryReq, getJwtUser());
        List<LenovoUserQueryVO> vo = BeanListUtil.convertList(dto, LenovoUserQueryVO.class);
        return Result.ok(vo);
    }



    /**
     *代理机构信息联想功能
     * @param lenovoUserQueryReq
     * @return
     */
    @ApiOperation(value = "代理机构信息联想功能 ")
    @ApiImplicitParam(name = "lenovoUserQueryReq", value = "代理机构信息 ", required = true, dataType = "LenovoUserQueryReq", paramType = "body")
    @PostMapping(value = "/queryAgencyInfo")
    public Result<List<LenovoUserQueryVO>> queryAgencyInfo(@RequestBody LenovoUserQueryReq lenovoUserQueryReq) {
        List<AgentUserDTO> dto = openService.queryAgencyInfo(lenovoUserQueryReq, getJwtUser());
        List<LenovoUserQueryVO> vo = BeanListUtil.convertList(dto, LenovoUserQueryVO.class);
        return Result.ok(vo);
    }


    /**
     *代理机构信息联想功能
     * @param lenovoUserQueryReq
     * @return
     */
    @ApiOperation(value = "代理机构信息联想功能 ")
    @ApiImplicitParam(name = "lenovoUserQueryReq", value = "代理机构信息 ", required = true, dataType = "LenovoUserQueryReq", paramType = "body")
    @PostMapping(value = "/queryAgencyInfoList")
    public Result<LenovoUserQueryVO> queryAgencyInfoList(@RequestBody LenovoUserQueryReq lenovoUserQueryReq) {
        AgentUserDTO dto = openService.queryAgencyInfoList(lenovoUserQueryReq, getJwtUser());
        LenovoUserQueryVO vo = BeanListUtil.convert(dto, LenovoUserQueryVO.class);
        return Result.ok(vo);
    }


    /**
     *供应商信息联想功能
     * @param lenovoUserQueryReq
     * @return
     */
    @ApiOperation(value = "供应商信息联想功能 ")
    @ApiImplicitParam(name = "lenovoUserQueryReq", value = "供应商信息 ", required = true, dataType = "LenovoUserQueryReq", paramType = "body")
    @PostMapping(value = "/querySupplierInfo")
    public Result<List<LenovoUserQueryVO>> querySupplierInfo(@RequestBody LenovoUserQueryReq lenovoUserQueryReq) {
        List<AgentUserDTO> dto = openService.querySupplierInfo(lenovoUserQueryReq, getJwtUser());
        List<LenovoUserQueryVO> vo = BeanListUtil.convertList(dto, LenovoUserQueryVO.class);
        return Result.ok(vo);
    }



    /**
     * 专家库抽取-获取企业清单
     * param    keywords(企业名称，信用代码，法定代表人)；
     * @return
     */
    @ApiOperation(value = "专家库抽取-获取企业清单")
    @ApiImplicitParam(name = "CompanyOpenCondition", value = "keywords(企业名称，信用代码，法定代表人)；principalUserId(项目委托人id)", required = true, dataType = "CompanyOpenCondition", paramType = "body")
    @PostMapping("/getCompanyInfoList")
    public Result<Paging<CompanySuperviseVo>> getCompanyInfoList(@RequestBody CompanyOpenCondition condition) {
        IPage<CompanySuperviseVo> page = openService.getCompanyInfoList(condition);
        return Result.ok(Paging.buildPaging(page));
    }


}