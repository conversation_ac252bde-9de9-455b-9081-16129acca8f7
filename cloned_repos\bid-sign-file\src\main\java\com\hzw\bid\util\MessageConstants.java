package com.hzw.bid.util;

/**
 * Message常量
 */
public class MessageConstants {

    /**
     * 系统错误
     */
    public static final String MESSAGE_SERVER_ERROR = "系统错误!";
    /**
     * 新增失败
     */
    public static final String ADD_FAILED = "新增失败!";
    /**
     * 修改失败
     */
    public static final String UPDATE_FAILED = "修改失败!";
    /**
     * 文件上传过大
     */
    public static final String UPLOAD_TOO_LARGE = "上传文件大小不得超过";

    public static final String SUCCESS = "成功!";
    public static final String FAIL = "失败!";

    public static final String PARAMS_NOT_NULL = "参数不能为空!";

    public static final String FILE_NOT_NULL = "文件不能为空!";

    public static final String NO_REPEATED_SUBMIT = "不允许重复提交，请稍后再试";

}
