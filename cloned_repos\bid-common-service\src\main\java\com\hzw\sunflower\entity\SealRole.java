package com.hzw.sunflower.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.hzw.sunflower.common.BaseBean;
import io.swagger.annotations.ApiModel;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * <AUTHOR>
 */
@ApiModel("t_seal_role表")
@TableName("t_seal_role")
@Data
public class SealRole extends BaseBean {
    private Long id;

    /**
     * 业务分组
     *
     * @mbg.generated
     */
    @ApiModelProperty("业务分组")
    private String businessCode;

    /**
     * 描述
     *
     * @mbg.generated
     */
    @ApiModelProperty("描述")
    private String description;

    /**
     * 角色id
     *
     * @mbg.generated
     */
    @ApiModelProperty("角色id")
    private Long roleId;

    /**
     * 角色代码
     *
     * @mbg.generated
     */
    @ApiModelProperty("角色代码")
    private String roleCode;

    /**
     * 排序
     *
     * @mbg.generated
     */
    @ApiModelProperty("排序")
    private Integer sort;

    @ApiModelProperty("用户id")
    private Long userId;

    @ApiModelProperty("企业id")
    private Long companyId;
    private static final long serialVersionUID = 1L;
}