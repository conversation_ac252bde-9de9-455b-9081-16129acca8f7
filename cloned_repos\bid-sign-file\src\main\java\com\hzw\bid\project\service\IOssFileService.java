package com.hzw.bid.project.service;

import com.hzw.bid.project.controller.response.OssFileVo;
import com.hzw.bid.project.model.OssFile;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * OSS附件  服务类
 * </p>
 *
 * <AUTHOR> Gen
 * @since 2022-11-25
 */
public interface IOssFileService extends IService<OssFile> {


    public Long saveOssFile(String fileName,String filePath) throws Exception;

    public OssFile getOssFileById(Long id);
}
