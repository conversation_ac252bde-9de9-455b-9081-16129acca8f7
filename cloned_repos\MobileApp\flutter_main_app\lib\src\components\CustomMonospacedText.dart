///***
/// * 构建等宽文字
/// */

import 'dart:math';
import 'package:flutter/material.dart';

import 'package:flutter_main_app/src/constants/theme.dart';

class CustomMonospacedText extends StatelessWidget {

  const CustomMonospacedText({
    Key key,
    @required this.width,
    @required this.text,
    this.textStyle = themeTextStyle,
    this.needColon = false,
    this.maxTextLengthLimit = 4,
  }): assert(width != null && width > 0.0),
    assert(text != null && text.length > 0),
    assert(textStyle != null),
    assert(needColon != null),
    assert(maxTextLengthLimit != null && maxTextLengthLimit >= 2),
    super(key: key);

  /// * 统一宽度值
  final double width;

  /// * 带切分的文字
  final String text;

  /// * 字体
  final TextStyle textStyle;

  /// * 是否需要在末尾添加冒号
  final bool needColon;

  /// * 最大允许文字数量
  final int maxTextLengthLimit;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width,
      child: Row(
        mainAxisSize: MainAxisSize.max,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: this._buildText(),
      ),
    );
  }

  ///***
  /// * 生成文字，一个字一个 Text
  /// * 字数不足时，由空白填充字间
  /// * 超过 4 个字符时，则丢弃之后的字符
  /// *
  /// * @return {List<Widget>}
  /// */
  List<Widget> _buildText() {
    final List<String> letters = text.split('');
    final List<Widget> widgets = [];

    final int len = min(letters.length, maxTextLengthLimit);

    for (int i = 0; i < len; i++) {
      final String letter = needColon && i == len - 1
        ? letters[i] + '：'
        : letters[i];
      final Widget widget = Text(
        letter,
        style: textStyle,
      );

      widgets.add(widget);
    }

    return widgets;
  }

}
