package com.hzw.sunflower.controller.project.request.bidder;

import com.hzw.sunflower.common.BaseCondition;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(description = "自采项目列表 查询条件")
public class MyselfProjectConditionReq extends BaseCondition {

    @ApiModelProperty(value = "关键字搜索（项目名称，项目编号）")
    private String keyWords;

    @ApiModelProperty(value = "项目编号")
    private String purchaseNum;

    @ApiModelProperty(value = "项目名称")
    private String purchaseName;

    @ApiModelProperty(value = "采购经理")
    private String purchaseManger;

    @ApiModelProperty(value = "部门id")
    private Long departmentId;

    @ApiModelProperty(value = "项目所在地-省")
    private String addressProvince;

    @ApiModelProperty(value = "项目所在地-市")
    private String addressCity;

    @ApiModelProperty(value = "项目状态")
    private String projectStatus;

    @ApiModelProperty(value = "创建时间起始时间")
    private String createBeginTime;

    @ApiModelProperty(value = "创建时间结束时间")
    private String createEndTime;

    @ApiModelProperty(value = "响应文件递交截至时间起始时间")
    private String submitBeginTime;

    @ApiModelProperty(value = "响应文件递交截至时间结束时间")
    private String submitEndTime;

    @ApiModelProperty(value = "用户id")
    private Long userId;

    @ApiModelProperty(value = "部门id")
    private Long userDepartId;

    @ApiModelProperty(value = "类型")
    private Integer type;

    @ApiModelProperty(value = "数据权限")
    private String dataScope;

}
