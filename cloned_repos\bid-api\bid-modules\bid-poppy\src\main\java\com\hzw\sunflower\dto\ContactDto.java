package com.hzw.sunflower.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date ：Created in 2021/4/16 15:13
 * @description：联系人Dto
 * @modified By：`
 * @version: 1.0
 */
@ApiModel(description = "联系人dto")
@Data
public class ContactDto {
    @ApiModelProperty(value = "委托用户名称", position = 3)
    private String userName;

    @ApiModelProperty(value = "委托用户id", position = 4)
    private Long userId;

    @ApiModelProperty(value = "部门id", position = 4)
    private Long departmentId;

    @ApiModelProperty(value = "委托用户联系方式", position = 5)
    private String userPhone;

    @ApiModelProperty(value = "来源", position = 6)
    private Integer source;

    @ApiModelProperty(value = "邮箱", position = 7)
    private String email;

    @ApiModelProperty(value = "是不是管理员", position = 9)
    private Integer isAdmin;

    @ApiModelProperty(value = "角色名称", position = 10)
    private String roleName;
}
