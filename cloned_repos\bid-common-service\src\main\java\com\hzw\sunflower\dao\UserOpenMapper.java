package com.hzw.sunflower.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hzw.sunflower.controller.response.*;
import com.hzw.sunflower.entity.User;
import com.hzw.sunflower.entity.condition.*;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户表 Mapper接口
 *
 * <AUTHOR>
 * @version 1.0.0 2021-04-07
 */
public interface UserOpenMapper extends BaseMapper<User> {


    /**
     * 对外开发招标人用户列表
     * @param page
     * @param condition
     * @return
     */
    IPage<UserTableVo> getUserTable(IPage<UserTableVo> page, @Param("condition") TUserIdentityCondition condition);

    /**
     * 对外开放 招标人管理列表
     * @param page
     * @param con
     * @return
     */
    IPage<CompanySuperviseVo> getCompanySupervise(IPage<CompanySuperviseVo> page, @Param("condition") CompanySuperviseOpenCondition con);


    /**
     *  获得单位信息列表
     * @param page
     * @return
     */
    IPage<CompanySuperviseVo> getCompanyInfoList(IPage<CompanySuperviseVo> page, @Param("keywords") String  keywords, @Param("companyId")Long companyId);


    /**
     * 获取招标人企业id
     * @param userId
     * @return
     */
    Long getCompanyIdByUserId(@Param("userId") Long userId);



    /**
     * 根据userId获取用户权限
     * @param userId
     * @return
     */
    List<RoleVo> getUserRoleByUserId(@Param("userId") Long userId);

    /**
     * 根据companyId获取企业信息
     * @param companyId
     * @return
     */
    OpenCompanyVo getCompanyInfoById(@Param("companyId") Long companyId);

    /**
     * 根据用户id查询用户部门企业信息
     * @param userId
     * @return
     */
    UserDeptCompanyVo getUserInfoById(@Param("userId")Long userId,@Param("identity") Integer identity);


    /**
     * 获取企业下的人员信息
     */
    IPage<UserInfoVo> getCompanyUser(IPage<UserInfoVo> page,@Param("condition") CompanyUserCondition condition);


    /**
     * 通过手机号获取用户信息
     */
    UserInfoVo  getCompanyUserInfoByPhone(@Param("condition") CompanyUserCondition condition);

    /**
     * 获取部门信息
     * @param userId
     * @param identity
     * @return
     */
    DepartmentInfoVo getDeptInfoByUserId(@Param("userId") Long userId,@Param("identity") Integer identity);


    /**
     * 获取用户数据
     * @param phone
     * @param identity
     * @return
     */
    User findUserWechatByPhone(@Param("identity")Integer identity,@Param("phone")String phone);

    /**
     *
     * @param userId
     * @param code
     * @return
     */
    UserSmsConfigVo getUserSmsConfig(@Param("userId") Long userId, @Param("code") String code);

    /**
     * 根据用户获取所属处室处长信息
     * @param deptCode
     * @return
     */
    List<UserInfoVo> getDirectorByUserId(@Param("deptCode") String deptCode,@Param("roleCode") String roleCode);

    /**
     * 获取标段最早的开标时间和发售结束时间
     * @param subIdList
     * @return
     */
    EarlySectionTimeVo getEarliestTimeBySectionIds(List<Long> subIdList);

    /**
     * 获取已递交投标文件供应商
     * @param sectionId
     * @param bidRound
     * @return
     */
    List<CompanyHasBidDocVo> getCompanyHasBidDoc(@Param("sectionId") Long sectionId, @Param("bidRound") Integer bidRound);

    /**
     * 根据用户id查询所有处长
     * @param czCodes
     * @param confirmUserId
     * @return
     */
    List<User> getChuZhangByUserId(@Param("czCodes")List<String> czCodes,@Param("userId") Long confirmUserId,@Param("organizationType") Integer organizationType);

    /**
     * 根据角色查询所有用户
     * @param czCodes
     * @return
     */
    List<User> getUserByRole(@Param("czCodes")List<String> czCodes);

    /**
     * 查询用户id 下 指定部门的处长
     * @param czCodes
     * @param confirmUserId
     * @param organizationType
     * @param departId
     * @return
     */
    List<User> getChuZhangByUserIdDepartId(@Param("czCodes")List<String> czCodes,@Param("userId") Long confirmUserId,@Param("organizationType") Integer organizationType,@Param("departId") Long departId);

    /**
     * 查询部门id所在公司的总经理
     * @param czCodes
     * @param departId
     * @return
     */
    List<User> getLeaderByDepartId(@Param("czCodes")List<String> czCodes,@Param("departId") Long departId);


    List<DepartmentInfoVo> getDepartInfoByOtherUserId(@Param("otherUserId") String otherUserId);

    /**
     * 查询用户登录ncc所需数据
     * @param userId
     * @param identity
     * @return
     */
    UserNccInfoVo queryUserNccInfo(@Param("userId") Long userId, @Param("identity") Integer identity);

    List<User> getUserByDepartId(@Param("code")String code, @Param("userId")Long confirmUserId, @Param("organizationType")Integer organizationType, @Param("deptId")Long deptId);

    List<UserInfoVo> getDirectorByUserIdF(@Param("deptCode") String deptCode,@Param("roleCode") String roleCode);

    List<User> getZJLDepartId(@Param("code")String code, @Param("deptId")Long deptId);

    /**
     * 根据userCode获取用户信息
     * @param userCode
     * @return
     */
    User getByOtherId(@Param("userCode")String userCode);
}
