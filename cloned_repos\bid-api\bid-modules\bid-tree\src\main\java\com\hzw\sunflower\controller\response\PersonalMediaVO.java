package com.hzw.sunflower.controller.response;

import com.hzw.sunflower.dto.PersonalMediaDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 个人媒体请求响应实体
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@ApiModel(description = "当前用户曾用的媒体信息")
@Data
@Accessors(chain = true)
public class PersonalMediaVO {

    @ApiModelProperty(value = "个人媒体信息列表", position = 1)
    private List<PersonalMediaDTO> medias;
}
