package com.hzw.sunflower.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.controller.project.request.AgentUserProPermREQ;
import com.hzw.sunflower.controller.project.request.UserProjectFromUserREQ;
import com.hzw.sunflower.dto.MyProjectListDTO;
import com.hzw.sunflower.dto.SingleProjectPermUserDTO;
import com.hzw.sunflower.dto.UserProjectFromUserDTO;
import com.hzw.sunflower.entity.UserProjectFromUser;


import java.util.List;
import java.util.Map;


public interface UserProjectFromUserService extends IService<UserProjectFromUser> {

    /**
     * 授权项目给多个用户
     * @param singleProjectPermUserDTO
     * @return
     */
    Result<Object> addUsers(SingleProjectPermUserDTO singleProjectPermUserDTO);

    /**
     * 删除授权的用户
     * @param singleProjectPermUserDTO
     * @return
     */
    Result<Object> delUsers(SingleProjectPermUserDTO singleProjectPermUserDTO);

    /**
     * 根据项目ID查询授权用户列表
     * @param record
     * @return
     */
    IPage<UserProjectFromUserDTO> selectUserListByProjectId(UserProjectFromUserREQ record);

    List<MyProjectListDTO> selectListWithPermByProjectIdAndUserId(AgentUserProPermREQ req);

    MyProjectListDTO selectOneWithPermByProjectIdAndUserId(AgentUserProPermREQ req);

    Map<Long,MyProjectListDTO> selectListMapWithPermByProjectIdsAndUserId(List<Long> projectIds,Long userId);


    /**
     * 查询项目是否有编辑权限
     * @param projectId
     * @return
     */
    Boolean getProjectPermissions(Long projectId);
}
