package com.hzw.sunflower.constant.constantenum;

/**
 * 外币转换类型
 *
 * <AUTHOR>
 */
public enum CurrencyConvertTypeEnum {
    PROJECT(1, "项目委托金额"),
    SECTION(2, "标段委托金额"),
    WIN(3, "中标价");

    private Integer type;
    private String desc;

    CurrencyConvertTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
