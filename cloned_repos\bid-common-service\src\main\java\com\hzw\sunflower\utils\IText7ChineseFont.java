package com.hzw.sunflower.utils;

import com.itextpdf.html2pdf.ConverterProperties;
import com.itextpdf.html2pdf.HtmlConverter;
import com.itextpdf.kernel.geom.PageSize;
import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.kernel.pdf.PdfWriter;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.element.AreaBreak;
import com.itextpdf.layout.font.FontProvider;
import com.itextpdf.styledxmlparser.css.media.MediaDeviceDescription;
import com.itextpdf.styledxmlparser.css.media.MediaType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.SystemUtils;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.io.*;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Objects;
import java.util.UUID;

@Slf4j
@Component
public class IText7ChineseFont {


    private static final String FONT_LIGHT_NAME = "AlibabaPuHuiTi-2-45-Light.ttf";
    private static final String FONT_BOLD_NAME = "AlibabaPuHuiTi-2-85-Bold.ttf";
    private static final String FONT_RESOURCE_PATH_PREFIX = "/fonts/";

    private static String cachedFontDirectoryPath;

    @PostConstruct
    public void init() {
        try {
            cachedFontDirectoryPath = createFontDirectory();
        } catch (IOException e) {
            throw new RuntimeException("字体目录初始化失败", e);
        }
    }

//    public static void test() throws Exception {
//        String inputPath = "/Users/<USER>/Desktop/test2.html";
//        String outputPath = "/Users/<USER>/Desktop/test2.pdf";
//        PageSize pageSize = new PageSize(PageSize.A4.getHeight(), PageSize.A4.getWidth());
//        convertToPdf( inputPath, outputPath,pageSize);
//    }
    /**
     * 文件流转换
     * 将输入流中的HTML内容转换为PDF格式，并保存到指定的输出路径
     * 使用指定的页面大小和字体目录进行转换
     *
     * @param inputStream 输入流，包含待转换的HTML内容
     * @param outputPath 输出路径，转换后的PDF文件将保存在此路径
     * @param pageSize 页面大小，用于设置PDF页面的尺寸
     * @throws Exception 如果转换过程中发生错误，将抛出异常
     */
    public static void convertToPdfStream(InputStream inputStream, String outputPath, PageSize pageSize) throws Exception {
        // 读取输入流并将其转换为UTF-8编码的字符串
        String html = IOUtils.toString(inputStream, StandardCharsets.UTF_8);
        // 调用html2pdf方法进行转换，传入HTML字符串、字体目录、页面大小和输出路径
        html2pdf(html, getAlibabaFontDirectory(), pageSize, outputPath);
    }

    /**
     * 文件路径转换
     * 将给定的文件路径转换为PDF格式并保存到指定的输出路径
     * 此方法主要用于将HTML文件转换为PDF，但也可以用于其他类型的文件转换，只要支持的转换逻辑适用
     *
     * @param inputPath 输入文件的路径，指定了要转换的文件位置
     * @param outputPath 输出文件的路径，指定了转换后的PDF文件保存位置
     * @param pageSize 页面大小，用于设置输出PDF的页面尺寸
     * @throws Exception 如果转换过程中发生错误，可能会抛出异常
     */
    public static void convertToPdf(String inputPath, String outputPath, PageSize pageSize) throws Exception {
        // 根据输入路径创建File对象，用于后续的文件读取操作
        File file = new File(inputPath);
        // 使用 try-with-resources 自动关闭资源
        try (InputStream inputStream = new FileInputStream(file)) {
            String html = IOUtils.toString(inputStream, StandardCharsets.UTF_8);
            html2pdf(html, getAlibabaFontDirectory(), pageSize, outputPath);
        } catch (Exception e) {
            log.error("文件转换失败: {}", e.getMessage(), e);
            throw new IOException("文件转换失败", e);
        }
    }


    /**
     * html 转pdf
     * @param html html 内容
     * @param fontDir 字体路径
     * @param pageSize 页面大小
     * @param outPath 输出路径
     * @throws Exception 可能抛出的异常
     */
    private static void html2pdf( String html, String fontDir, PageSize pageSize,String outPath) throws Exception {
        // 创建临时文件
       // File file = new File(outPath);
        // 创建转换属性对象
        final ConverterProperties properties = new ConverterProperties();
        // 创建字体提供者对象
        final FontProvider fontProvider = new FontProvider();
        // 添加字体路径到字体提供者
        fontProvider.addDirectory(fontDir);
        // 设置字体提供者到转换属性
        properties.setFontProvider(fontProvider);
        // 设置媒体设备描述为打印
        properties.setMediaDeviceDescription(new MediaDeviceDescription(MediaType.PRINT));

        // 尝试使用资源管理方式创建输出流、PDF写入器和PDF文档对象
        try (final OutputStream os = Files.newOutputStream(Path.of(outPath));
             final PdfWriter pdfWriter = new PdfWriter(os);
             final PdfDocument pdfDocument = new PdfDocument(pdfWriter)) {
            // 设置默认页面大小为传入的pageSize
            pdfDocument.setDefaultPageSize(pageSize);
            // 尝试使用资源管理方式将HTML内容转换为PDF文档
            try (final Document doc = HtmlConverter.convertToDocument(html, pdfDocument, properties)) {
                // 添加分页符到文档
                doc.add(new AreaBreak());
            } finally {
                // 确保PDF文档关闭
                pdfDocument.close();
                // 确保输出流关闭
                os.close();
            }
        }
    }


//    /**
//     * 获取阿里云字体文件目录
//     *
//     * 此方法将在系统临时目录下创建一个目录，并从类路径资源中复制两个字体文件到该目录
//     * 如果字体目录创建失败，将抛出IllegalStateException异常
//     * 在程序关闭时，该方法创建的目录将会被删除
//     *
//     * @return 字体目录的绝对路径
//     * @throws IOException 如果无法读取字体文件或创建目录失败
//     */
//    private static String getAlibabaFontDirectory() throws IOException {
//        // 创建一个唯一的字体目录
//        final File fontDir = new File(SystemUtils.getJavaIoTmpDir(), UUID.randomUUID().toString());
//        // 如果目录不存在且无法创建，抛出异常
//        if (!fontDir.exists() && !fontDir.mkdirs()) {
//            throw new IllegalStateException();
//        }
//
//        // 从类路径资源中获取字体文件输入流
//        final InputStream fontLight = Objects.requireNonNull(IText7ChineseFont.class.getResourceAsStream("/fonts/AlibabaPuHuiTi-2-45-Light.ttf"));
//        final InputStream fontBold = Objects.requireNonNull(IText7ChineseFont.class.getResourceAsStream("/fonts/AlibabaPuHuiTi-2-85-Bold.ttf"));
//
//        // 将字体文件复制到字体目录
//        IOUtils.copy(fontLight, Files.newOutputStream(new File(fontDir, "AlibabaPuHuiTi-2-45-Light.ttf").toPath()));
//        IOUtils.copy(fontBold, Files.newOutputStream(new File(fontDir, "AlibabaPuHuiTi-2-85-Bold.ttf").toPath()));
//
//        // 注册一个关闭钩子，在程序关闭时删除字体目录
//        Runtime.getRuntime().addShutdownHook(new Thread(() -> FileUtils.deleteQuietly(fontDir)));
//
//        // 返回字体目录的绝对路径
//        return fontDir.getAbsolutePath();
//    }

    /**
     * 获取阿里云字体文件目录
     *
     * 此方法将在系统临时目录下创建一个目录，并从类路径资源中复制两个字体文件到该目录
     * 如果字体目录创建失败，将抛出IllegalStateException异常
     * 在程序关闭时，该方法创建的目录将会被删除
     *
     * @return 字体目录的绝对路径
     * @throws IOException 如果无法读取字体文件或创建目录失败
     */
/*    private static String getAlibabaFontDirectory() throws IOException {
        // 缓存机制：避免重复创建和复制字体文件
        if (cachedFontDirectoryPath != null) {
            return cachedFontDirectoryPath;
        }

        synchronized (IText7ChineseFont.class) {
            if (cachedFontDirectoryPath != null) {
                return cachedFontDirectoryPath;
            }

            // 创建一个唯一的字体目录
            final File fontDir = new File(SystemUtils.getJavaIoTmpDir(), UUID.randomUUID().toString());
            if (!fontDir.exists() && !fontDir.mkdirs()) {
                throw new IllegalStateException("Failed to create font directory: " + fontDir.getAbsolutePath());
            }

            // 复制字体文件
            copyFontResourceToFile(fontDir, FONT_LIGHT_NAME);
            copyFontResourceToFile(fontDir, FONT_BOLD_NAME);

            // 注册关闭钩子
            Runtime.getRuntime().addShutdownHook(new Thread(() -> FileUtils.deleteQuietly(fontDir)));

            // 缓存路径
            cachedFontDirectoryPath = fontDir.getAbsolutePath();
            return cachedFontDirectoryPath;
        }
    }

    *//**
     * 将类路径中的字体文件复制到目标目录
     *//*
    private static void copyFontResourceToFile(File targetDir, String fontFileName) throws IOException {
        String resourcePath = FONT_RESOURCE_PATH_PREFIX + fontFileName;
        try (InputStream is = IText7ChineseFont.class.getResourceAsStream(resourcePath)) {
            if (is == null) {
                throw new IOException("Font resource not found: " + resourcePath);
            }
            File targetFile = new File(targetDir, fontFileName);
            try (OutputStream os = Files.newOutputStream(targetFile.toPath())) {
                byte[] buffer = new byte[8192];
                int bytesRead;
                while ((bytesRead = is.read(buffer)) != -1) {
                    os.write(buffer, 0, bytesRead);
                }
            }
        }
    }*/

    private static String createFontDirectory() throws IOException {
        File fontDir = new File(SystemUtils.getJavaIoTmpDir(), UUID.randomUUID().toString());
        if (!fontDir.exists() && !fontDir.mkdirs()) {
            throw new IllegalStateException("Failed to create font directory: " + fontDir.getAbsolutePath());
        }

        copyFontResourceToFile(fontDir, FONT_LIGHT_NAME);
        copyFontResourceToFile(fontDir, FONT_BOLD_NAME);

        Runtime.getRuntime().addShutdownHook(new Thread(() -> FileUtils.deleteQuietly(fontDir)));

        return fontDir.getAbsolutePath();
    }

    private static void copyFontResourceToFile(File targetDir, String fontFileName) throws IOException {
        String resourcePath = FONT_RESOURCE_PATH_PREFIX + fontFileName;
        try (InputStream is = IText7ChineseFont.class.getResourceAsStream(resourcePath)) {
            if (is == null) {
                throw new IOException("Font resource not found: " + resourcePath);
            }
            File targetFile = new File(targetDir, fontFileName);
            try (OutputStream os = Files.newOutputStream(targetFile.toPath())) {
                byte[] buffer = new byte[8192];
                int bytesRead;
                while ((bytesRead = is.read(buffer)) != -1) {
                    os.write(buffer, 0, bytesRead);
                }
            }
        }
    }

    public static String getAlibabaFontDirectory() {
        if (cachedFontDirectoryPath == null) {
            throw new IllegalStateException("Font directory not initialized. Ensure Spring context is loaded.");
        }
        return cachedFontDirectoryPath;
    }


}
