package com.hzw.sunflower.controller.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2022/8/1 8:35
 * @description：异常关联入参
 * @modified By：`
 * @version: 1.0
 */
@ApiModel(description = "异常关联入参")
@Data
public class ExceptionRelationReq implements Serializable {

    @ApiModelProperty(value = "流水id列表")
    List<Long> waterIdList;

    @ApiModelProperty(value = "标段id列表")
    List<BondSectionReq> sectionIdList;

    @ApiModelProperty(value = "修改前标段id列表，若第一次新增则无需传递")
    List<Long> formerSectionIdList;

    @ApiModelProperty(value = "修改前流水id列表，若第一次新增则无需传递")
    List<Long> formerWaterIdList;

    @ApiModelProperty(value = "供应商id")
    private Long companyId;

    @ApiModelProperty(value = "异常标段id")
    private Long exceptionSectionId;

    @ApiModelProperty(value = "异常项目id")
    private Long exceptionProjectId;

    @ApiModelProperty(value = "保证金线下项目供应商表id")
    private Long bondOfflineCompanyId;

    @ApiModelProperty(value = "线下项目供应商id")
    private Long offlineCompanyId;

}
