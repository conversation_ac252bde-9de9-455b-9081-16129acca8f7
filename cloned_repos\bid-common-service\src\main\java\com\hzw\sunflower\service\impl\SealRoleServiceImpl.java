package com.hzw.sunflower.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzw.sunflower.constant.SealRoleConstants;
import com.hzw.sunflower.dao.SealRoleMapper;
import com.hzw.sunflower.dto.SealRoleDTO;
import com.hzw.sunflower.entity.SealRole;
import com.hzw.sunflower.entity.User;
import com.hzw.sunflower.security.utils.SecurityUtils;
import com.hzw.sunflower.service.SealRoleService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;


/**
 * <AUTHOR>
 */
@Service
public class SealRoleServiceImpl extends ServiceImpl<SealRoleMapper, SealRole> implements SealRoleService {
    /**
     * 查询当前业务所需角色
     * @param businessCode
     * @return
     */
    @Override
    public List<SealRole> getRoleByBusinessCode(String businessCode) {
        LambdaQueryWrapper<SealRole> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SealRole::getBusinessCode,businessCode)
                .orderByAsc(SealRole::getSort);
        return this.list(queryWrapper);
    }

    /**
     * 查询业务code对应的用户
     * @param businessCode
     * @return
     */
    @Override
    public List<User> queryNextRole(String businessCode) {
        return this.baseMapper.queryNextRole(businessCode);
    }

    /**
     * 根据处室 和业务code查询分管领导
     * @param departId
     * @param seal
     * @return
     */
    @Override
    public List<User> queryFGLD(Long departId, String seal) {
        return this.baseMapper.queryFGLD(departId,seal);
    }

    /**
     * 查询领导审批数据
     * @param companyId
     * @return
     */
    @Override
    public List<SealRoleDTO> querySealLeader(Long companyId) {
        // 根据企业id 查询领导审批数据
        List<SealRoleDTO> users = this.baseMapper.querySealLeader(companyId, SealRoleConstants.SEAL_LEADER);
        if(CollectionUtils.isEmpty(users)){
            // 如果没有数据查询总公司的领导审批数据
            users = this.baseMapper.querySealLeader(SecurityUtils.getJwtUser().getCompanyId(), SealRoleConstants.SEAL_LEADER);
        }
        return users;
    }

    @Override
    public List<SealRole> querySealLeaderCode(Long companyId) {
        // 根据企业id 查询领导审批数据
        LambdaQueryWrapper<SealRole> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SealRole::getCompanyId,companyId)
                .eq(SealRole::getBusinessCode,SealRoleConstants.SEAL_LEADER);
        List<SealRole> list = this.list(queryWrapper);
        if(CollectionUtils.isEmpty(list)){
            // 如果没有数据查询总公司的领导审批数据
            LambdaQueryWrapper<SealRole> qw = new LambdaQueryWrapper<>();
            qw.eq(SealRole::getCompanyId,SecurityUtils.getJwtUser().getCompanyId())
                    .eq(SealRole::getBusinessCode,SealRoleConstants.SEAL_LEADER);
            list = this.list(qw);
        }
        return list;
    }
}