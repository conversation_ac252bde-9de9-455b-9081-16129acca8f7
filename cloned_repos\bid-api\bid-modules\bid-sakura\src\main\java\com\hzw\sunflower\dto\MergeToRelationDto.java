package com.hzw.sunflower.dto;

import com.hzw.sunflower.entity.BondChangeRecords;
import com.hzw.sunflower.entity.BondRelation;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2022/8/3 16:23
 * @description：关联列表和调整列表dto
 * @modified By：`
 * @version: 1.0
 */
@Data
public class MergeToRelationDto {

    /**
     * 关联列表
     */
    List<BondRelation> relationList;

    /**
     * 调整记录列表
     */
    List<BondChangeRecords> changeRecordsList;

}
