package com.hzw.sunflower.controller.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @ClassName:CommonUserRoleRelationVO
 * @Auther: lijinxin
 * @Description: 用户角色关系
 * @Date: 2024/7/23 15:37
 * @Version: v1.0
 */
@Data
public class CommonUserRoleRelationVO {

    @ApiModelProperty(value = "主键", position = 1)
    private Long id;

    /**
     * 用户id
     */
    @ApiModelProperty(value = "用户id", position = 2)
    private Long userId;

    /**
     * 角色id
     */
    @ApiModelProperty(value = "角色id", position = 3)
    private Long roleId;

    /*
     * 身份id
     */
    @ApiModelProperty(value = "身份id", position = 4)
    private Long userIdentityId;

    @ApiModelProperty(value = "角色代码", position = 1)
    private String roleCode;
}
