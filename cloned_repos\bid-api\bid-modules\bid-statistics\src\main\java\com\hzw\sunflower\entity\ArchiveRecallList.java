package com.hzw.sunflower.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */

@Data
public class ArchiveRecallList implements Serializable {


    @Excel(name = "项目编号",orderNum = "1",width=25)
    @ApiModelProperty("项目编号")
    private String purchaseNumber;

    @ApiModelProperty("项目标段号")
    private Long sectionId;

    @Excel(name = "项目名称",orderNum = "2",width=25)
    @ApiModelProperty("项目名称")
    private String projectName;

    @Excel(name = "标段包号",orderNum = "3",width=25)
    @ApiModelProperty("标段包号")
    private String packageNumber;

    @Excel(name = "标段包名称",orderNum = "4",width=25)
    @ApiModelProperty("标段包名称")
    private String packageName;

    @Excel(name = "撤回原因",orderNum = "5",width=25)
    @ApiModelProperty("撤回原因")
    private String remark;

    @Excel(name = "状态",orderNum = "6",width=25)
    @ApiModelProperty("操作状态")
    private String operation;

    @Excel(name = "申请人",orderNum = "7",width=25)
    @ApiModelProperty("操作人姓名")
    private String operatorName;

}
