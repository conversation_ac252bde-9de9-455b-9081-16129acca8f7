package com.hzw.sunflower.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hzw.sunflower.controller.request.AddRelationReq;
import com.hzw.sunflower.controller.request.BondRelationReq;
import com.hzw.sunflower.controller.response.BondRelationSectionVo;
import com.hzw.sunflower.controller.response.BondRelationVo;
import com.hzw.sunflower.controller.response.ProjectWaterVo;
import com.hzw.sunflower.entity.BondApplyRefundProject;
import com.hzw.sunflower.entity.BondRelation;
import com.hzw.sunflower.entity.BondWater;
import com.hzw.sunflower.entity.JwtUser;
import com.hzw.sunflower.entity.condition.BondRelationCondition;
import com.hzw.sunflower.entity.condition.ExceptionBondRelationCondition;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>
 * 保证金申请退还项目服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-27
 */
public interface BondApplyRefundProjectService extends IService<BondApplyRefundProject> {


}
