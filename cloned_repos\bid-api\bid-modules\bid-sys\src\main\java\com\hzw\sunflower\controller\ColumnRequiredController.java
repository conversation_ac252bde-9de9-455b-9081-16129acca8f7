package com.hzw.sunflower.controller;

import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.common.annotation.RepeatSubmit;
import com.hzw.sunflower.controller.request.ColumnRequiredReq;
import com.hzw.sunflower.controller.response.ColumnRequiredVO;
import com.hzw.sunflower.entity.ColumnRequired;
import com.hzw.sunflower.service.ColumnRequiredService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Api(tags = "项目字段必填配置")
@RestController
@RequestMapping("/columnRequired")
public class ColumnRequiredController {

    @Autowired
    ColumnRequiredService columnRequiredService;

    @ApiOperation(value = "回显")
    @ApiImplicitParam(name = "req", value = "参数", required = true, dataType = "ColumnRequired", paramType = "body")
    @PostMapping("/queryColumnRequired")
    public Object queryColumnRequired(@RequestBody ColumnRequiredReq req) {
        List<ColumnRequiredVO> columnRequiredVOList  = columnRequiredService.queryColumnRequired(req);
        return Result.ok(columnRequiredVOList);
    }

    @ApiOperation(value = "保存")
    @ApiImplicitParam(name = "columnRequiredVOList", value = "参数", required = true, dataType = "List<ColumnRequiredVO>", paramType = "body")
    @PostMapping("/insertColumnRequired")
    @RepeatSubmit
    public Object insertFunctionConfiguration(@RequestBody List<ColumnRequiredVO>  columnRequiredVOList) {
        Boolean bool  = columnRequiredService.insertColumnRequired(columnRequiredVOList);
        return Result.okOrFailed(bool);
    }

    @ApiOperation(value = "查询功能模块")
    @ApiImplicitParam(name = "columnRequiredVOList", value = "参数", required = true, dataType = "List<ColumnRequiredVO>", paramType = "body")
    @PostMapping("/queryModuleName")
    public Object queryModuleName(@RequestBody ColumnRequiredReq req) {
        List<ColumnRequired> columnRequireds  = columnRequiredService.queryModuleName(req);
        return Result.ok(columnRequireds);
    }


    @ApiOperation(value = "根据模块展示字段名")
    @ApiImplicitParam(name = "req", value = "参数", required = true, dataType = "ColumnRequiredReq", paramType = "body")
    @PostMapping("/queryColumnNameByModuleName")
    public Object queryColumnNameByModuleName(@RequestBody ColumnRequiredReq req) {
        List<ColumnRequired> columnNames  = columnRequiredService.queryColumnNameByModuleName(req);
        return Result.ok(columnNames);
    }


    @ApiOperation(value = "根据模块，字段名是否展示")
    @ApiImplicitParam(name = "req", value = "参数", required = true, dataType = "ColumnRequiredReq", paramType = "body")
    @PostMapping("/queryByColumnNameModuleName")
    public Object queryByColumnNameModuleName(@RequestBody ColumnRequiredReq req) {
        ColumnRequired columnName  = columnRequiredService.queryByColumnNameModuleName(req);
        return Result.ok(columnName);
    }
}
