package com.hzw.depositfee.api.exception;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * @ClassName:AmoebaException
 * @Auther: lijinxin
 * @Description: 异常
 * @Date: 2023/1/10 14:15
 * @Version: v1.0
 */
@Data
@Slf4j
public class CustomException extends RuntimeException {

    private ExceptionEnum exceptionEnum;

    /*
     * 自定义异常信息
     */
    private String errorDetail;

    /**
     * 带自定义异常信息的构造方法
     *
     * @param exceptionEnums
     * @param errorDetail
     */
    public CustomException(ExceptionEnum exceptionEnums, String errorDetail) {
        this.exceptionEnum = exceptionEnums;
        this.errorDetail = errorDetail;
    }
}
