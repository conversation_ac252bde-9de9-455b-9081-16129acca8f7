package com.hzw.ssm.sys.system.dao;

import java.util.List;

import com.hzw.ssm.sys.system.entity.MenuEntity;
import com.hzw.ssm.sys.system.entity.RoleEntity;

public interface RoleMapper {
	/**
	 * 查询角色列表
	 * 
	 * @param roleEntity
	 *            角色查询条件实体对象
	 * @return 实体对象集合
	 */
	public List<RoleEntity> queryRoleList(RoleEntity roleEntity);

	/**
	 * 根据角色id获取获取相应菜单id
	 * 
	 * @param roleEntity
	 * @return
	 */
	public List<RoleEntity> getMenuIdsByRoleId(String roleId);

	/**
	 * 根据角色id删除所有权限
	 * 
	 * @param roleId
	 */
	public void deleteRoleRelation(String roleId);

	/**
	 * 修改用户权限
	 * 
	 * @param roleList
	 */
	public void modifyRoleRelation(List<RoleEntity> roleList);

	/**
	 * 保存角色信息
	 * @param roleEntity
	 */
	public void saveRoleInfo(RoleEntity roleEntity);

	/**
	 * 根据角色名称获得对象信息
	 * @param roleName
	 * @return
	 */
	public RoleEntity getRoleInfoByName(String roleName);

	/**
	 * 修改角色信息
	 * @param roleEntity
	 */
	public void updateRoleInfo(RoleEntity roleEntity);
	/**
	 * 根据id获得角色对象
	 * @param roleEntity
	 */
	public RoleEntity getRoleInfoById(String roleId);

	/**
	 * 根据id获得删除角色
	 * @param roleId
	 */
	public void deleteRoleInfo(String roleId);
	
	/**
	 * 获得所有可用菜单集合
	 * @return
	 */
	public List<MenuEntity>  queryMenuList();

	/**
	 * 根据角色id获得所有菜单
	 * @param roleId
	 * @return
	 */
	public List<String> getAuthListByRoleId(String roleId);

}
