package com.hzw.ssm.sys.sms.action;

import java.io.PrintWriter;
import java.util.Arrays;
import java.util.List;

import org.apache.struts2.convention.annotation.Action;
import org.apache.struts2.convention.annotation.Namespace;
import org.apache.struts2.convention.annotation.ParentPackage;
import org.apache.struts2.convention.annotation.Result;
import org.apache.struts2.convention.annotation.Results;
import org.springframework.beans.factory.annotation.Autowired;

import com.hzw.ssm.fw.base.BaseAction;
import com.hzw.ssm.sys.call.service.VoiceService;
import com.hzw.ssm.util.empty.EmptyUtils;
/**
 * 使用梦网短信接口发送短信
 * <AUTHOR>
 *
 */
@Namespace("/smsAction")
@ParentPackage(value = "default")
@Results({ @Result( name="init",location="/jsp/sms/sms_add.jsp" )})
public class SMSAction extends BaseAction{
	private static final long serialVersionUID = -8823858495799309882L;
	
	/**
	 * 手机号码多个
	 */
	private String phones;
	/**
	 * 手机号码单个
	 */
	private String phone;
	/**
	 * 短信内容
	 */
	private String smsContent;
	
	@Autowired
	private VoiceService voiceService;
	@Action("init")
	public String init(){
		
		
		return "init";
	}
	
	@Action("sendSms")
	public String sendSms(){
		this.context();
		this.getResponse().setCharacterEncoding("utf-8");
		PrintWriter out = null;
		String msg ="";
		try {
			out = this.getResponse().getWriter();
			//将所有的中文逗号替换成因为
			phones= phones.replaceAll("，", ",");
			//英文逗号切割
			String[] contactInfoArr = phones.split(",");
			String content =smsContent;
			 if (!EmptyUtils.isEmpty(contactInfoArr)) {
				//循环遍历所有的联系人
	             for ( int i = 0 ; i < contactInfoArr.length ; i++ ) {
	            	 //判断是否包含中文逗号
	                 if (EmptyUtils.isEmpty(contactInfoArr[i])) {
	                     continue;
	                 }
	                 msg=contactInfoArr[i].trim();
	                 voiceService.smsTongzhi(contactInfoArr[i].trim(),content);
	             }
			 }
			 msg ="success";
		} catch (Exception e) {
			msg = msg+"发送失败";
			e.printStackTrace();
		}finally {
			out.print(msg);
			out.close();
		}
		return null;
	  }
	public String getPhones() {
		return phones;
	}
	public void setPhones(String phones) {
		this.phones = phones;
	}
	public String getPhone() {
		return phone;
	}
	public void setPhone(String phone) {
		this.phone = phone;
	}
	public String getSmsContent() {
		return smsContent;
	}
	public void setSmsContent(String smsContent) {
		this.smsContent = smsContent;
	}
	
}
