package com.hzw.sunflower.constant.constantenum;

/**
 * <AUTHOR>
 * @date ：Created in 2021/6/30 10:59
 * @description：招标文件进度
 * @version: 1.0
 */
public enum NoticeProgressEnum {
    DRAFTING(1, "起草中"),
    UNDERREVIEW(2, "待确认"),
    WITHDRAWAL(3, "已撤回"),
    REVIEW(4, "已确认"),
    RELEASE(5, "已发布"),
    RETURN(6, "已退回"),
    WITHDRAWUNDERREVIEW(7, "撤回待确认"),
    CONFIRMING(8, "确认中");

    private Integer value;

    private String name;

    NoticeProgressEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    public Integer getValue() {
        return value;
    }

    public String getName() {
        return name;
    }
}
