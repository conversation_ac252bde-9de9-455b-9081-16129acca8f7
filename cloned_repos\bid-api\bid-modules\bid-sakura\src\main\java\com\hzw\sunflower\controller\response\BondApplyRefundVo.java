package com.hzw.sunflower.controller.response;

import com.hzw.sunflower.entity.BondApplyRefund;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@ApiModel(description = "异常退款申请待处理列表")
@Data
public class BondApplyRefundVo extends BondApplyRefund implements Serializable {

    @ApiModelProperty("单位名称")
    private String companyName;

    @ApiModelProperty("联系人")
    private String userName;

    @ApiModelProperty("联系电话")
    private String userPhone;

    @ApiModelProperty("财务付款状态 null：未到财务付款 1：待处理  2：已退回    3：退还成功 4：退还失败  5.退还中")
    private Integer payStatus;

    @ApiModelProperty("原因")
    private String remark;

}
