package com.hzw.sunflower.controller;
import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.common.annotation.RepeatSubmit;
import com.hzw.sunflower.controller.request.BidDocDeviceReq;
import com.hzw.sunflower.controller.responese.BidDocDeviceVo;
import com.hzw.sunflower.service.BidDocDeviceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2024/6/26 10:54
 * @description：设备需求服务控制器
 * @modified By：`
 * @version: 1.0
 */
@Api(tags = "设备需求")
@RestController
@RequestMapping("/docDevice")
public class BidDocDeviceController {

    @Autowired
    private BidDocDeviceService bidDocDeviceService;

    @ApiOperation(value = "保存设备需求")
    @ApiImplicitParam(name = "reqList", value = "reqList", required = true, dataType = "BidDocDeviceReq", paramType = "body")
    @PostMapping(value = "/submitDevice")
    public Result<Boolean> submitDevice(@RequestBody List<BidDocDeviceReq> reqList) {
        return bidDocDeviceService.submitDevice(reqList);
    }

    @ApiOperation(value = "查询设备需求")
    @ApiImplicitParam(name = "req", value = "req", required = true, dataType = "BidDocDeviceReq", paramType = "body")
    @PostMapping(value = "/queryDevice")
    public Result<List<BidDocDeviceVo>> queryDevice(@RequestBody BidDocDeviceReq req) {
        if(null == req.getDocId()){
            return Result.error("招标文件编制主表id不能为空");
        }
        return bidDocDeviceService.queryDevice(req);
    }

    @ApiOperation(value = "导出设备需求模板")
    @PostMapping("/exportDeviceTemplate")
    public void exportDeviceTemplate(HttpServletResponse response, HttpServletRequest request) throws IOException {
        bidDocDeviceService.exportDeviceTemplate(response,request);
    }

    @ApiOperation(value = "设备需求数据导入")
    @ApiImplicitParam(name = "req", value = "请求参数", required = true, dataType = "BidDocDeviceReq", paramType = "body")
    @PostMapping("/importDevice")
    @RepeatSubmit
    public Result<List<BidDocDeviceVo>> importDevice(@RequestBody BidDocDeviceReq req) throws IOException {
        return bidDocDeviceService.importDevice(req.getOssId());
    }

}
