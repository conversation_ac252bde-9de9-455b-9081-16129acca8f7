package com.hzw.sunflower.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hzw.sunflower.controller.request.PromiseFileReq;
import com.hzw.sunflower.controller.response.ProjectPromiseFileVo;
import com.hzw.sunflower.entity.ProjectPromiseFile;
import com.hzw.sunflower.entity.condition.ProjectPromiseFileCondition;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2021/8/27 11:31
 * @description：
 * @version: 1.0
 */
public interface ProjectPromiseFileService extends IService<ProjectPromiseFile> {

    /**
     * 分页查询
     * @param condition
     * @return
     */
    IPage<ProjectPromiseFileVo> listPage(ProjectPromiseFileCondition condition);

    /**
     * 新增其他承诺文件
     * @param req
     * @return
     */
    Boolean addPromiseFile(PromiseFileReq req);

    /**
     * 删除承诺文件
     * @param id
     * @return
     */
    Boolean deletePromiseFile(Long id);

    /**
     * 编辑/重新上传承诺文件
     * @param req
     * @return
     */
    Boolean addOrUpdatePromiseFile(PromiseFileReq req);

    /**
     * 查询专家评审文件签署列表
     * @param projectIds
     * @param userId
     * @return
     */
    List<ProjectPromiseFileVo> listSign(List<Long> projectIds, Long userId, Long conferenceId);

    /**
     * 查询会议室关联专家参与的评审
     * @param conferenceId
     * @param userId
     * @return
     */
    List<Long> getProjectIdsByConferenceId(Long conferenceId, Long userId);

    /**
     * 保存评委宣誓词
     * @param projectId
     */
    void savePromiseFile(Long projectId,Long sectionId);
}
