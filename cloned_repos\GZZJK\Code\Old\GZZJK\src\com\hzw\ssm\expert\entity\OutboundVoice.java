package com.hzw.ssm.expert.entity;

/**
 * 外呼语音入参
 * 
 * <AUTHOR>
 * @date 2018-03-27
 */
public class OutboundVoice {

	/**
	 * 客户id
	 */
	private String client_id;
	
	/**
	 * 客户密码
	 */
	private String client_pass;
	
	/**
	 * 接口超时时间：默认30秒
	 */
	private String org_timeout;
	
	/**
	 * 主显号码，需智恒做白名单鉴权
	 */
	private String org_num;
	
	/**
	 * 主被叫号码
	 */
	private String call_num;
	
	/**
	 * tts记录的唯一标识
	 */
	private String synchro_id;
	
	/**
	 * N：0不循环、1循环1次、2循环2次....上限为4次；实际播报次数为N+1次；上限0<=N<5
	 */
	private String isFor;
	
	/**
	 * 通知类型(值：“A1”短信发送 “A2”按键反馈 ”B1”按键重听)默认“无”。
	 */
	private String noticeType;
	
	/**
	 * 每次循环播报的时间间隔：单位毫秒；上限15秒
	 */
	private String interval;
	
	/**
	 * 回调地址（调用方提供）
	 */
	private String report_url;
	
	/**
	 * 启调数据标识，保证唯一性（调用方提供）
	 */
	private String client_data;
	
	/**
	 * 外呼结果ID
	 */
	private String logId;
	

	public String getLogId() {
		return logId;
	}

	public void setLogId(String logId) {
		this.logId = logId;
	}

	public String getSynchro_id() {
		return synchro_id;
	}

	public void setSynchro_id(String synchro_id) {
		this.synchro_id = synchro_id;
	}

	public String getClient_id() {
		return client_id;
	}

	public void setClient_id(String client_id) {
		this.client_id = client_id;
	}

	public String getClient_pass() {
		return client_pass;
	}

	public void setClient_pass(String client_pass) {
		this.client_pass = client_pass;
	}

	public String getOrg_timeout() {
		return org_timeout;
	}

	public void setOrg_timeout(String org_timeout) {
		this.org_timeout = org_timeout;
	}

	public String getOrg_num() {
		return org_num;
	}

	public void setOrg_num(String org_num) {
		this.org_num = org_num;
	}

	public String getCall_num() {
		return call_num;
	}

	public void setCall_num(String call_num) {
		this.call_num = call_num;
	}

	public String getIsFor() {
		return isFor;
	}

	public void setIsFor(String isFor) {
		this.isFor = isFor;
	}

	public String getNoticeType() {
		return noticeType;
	}

	public void setNoticeType(String noticeType) {
		this.noticeType = noticeType;
	}


	public String getInterval() {
		return interval;
	}

	public void setInterval(String interval) {
		this.interval = interval;
	}

	public String getReport_url() {
		return report_url;
	}

	public void setReport_url(String report_url) {
		this.report_url = report_url;
	}

	public String getClient_data() {
		return client_data;
	}

	public void setClient_data(String client_data) {
		this.client_data = client_data;
	}
}
