package com.hzw.sunflower.service.impl;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.hzw.sunflower.constant.CheckTypeConstants;
import com.hzw.sunflower.constant.ConfigConstants;
import com.hzw.sunflower.constant.MessageConstants;
import com.hzw.sunflower.constant.constantenum.BidPurchaseModeConfigEnum;
import com.hzw.sunflower.controller.request.PurchaseTypeCheckReq;
import com.hzw.sunflower.controller.request.SaleAndSubmitCheckReq;
import com.hzw.sunflower.dao.ColumnCheckConfigMapper;
import com.hzw.sunflower.entity.ColumnCheckConfig;
import com.hzw.sunflower.service.ColumnPurchaseConfigService;
import com.hzw.sunflower.service.SaleAndSubmitCheckService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

@Service
public class SaleAndSubmitCheckServiceImpl implements SaleAndSubmitCheckService {

    @Autowired
    private ColumnPurchaseConfigService columnPurchaseConfigService;

    @Autowired
    private ColumnCheckConfigMapper columnCheckConfigMapper;

    /**
     * 校验采购文件发售截至时间和响应文件递交截至时间
     * @param req
     * @param checkType
     * @return
     */
    @Override
    public String checkTime(SaleAndSubmitCheckReq req, Integer checkType) {
        String result = null;
        //查询系统配置的校验条件
        PurchaseTypeCheckReq purchaseTypeCheckReq = new PurchaseTypeCheckReq();
        purchaseTypeCheckReq.setPurchaseType(req.getPurchaseType());
        purchaseTypeCheckReq.setPurchaseMode(req.getPurchaseMode());
        purchaseTypeCheckReq.setPurchaseStatus(req.getPurchaseStatus());
        ColumnCheckConfig purchaseTypeCheck = columnPurchaseConfigService.getPurchaseTypeCheck(purchaseTypeCheckReq);
        if (purchaseTypeCheck != null) {
            //校验开始时间
            Date startTime = new Date();
            if (req.getStartTime() != null) {
                startTime = req.getStartTime();
            }
            if (CheckTypeConstants.CHECK_SUBMIT_END_TIME.equals(checkType) || CheckTypeConstants.CHECK_ALL.equals(checkType)) {
                //响应文件递交截至时间是否强校验
                if (ConfigConstants.END_TIME_TYPE_CHECK.equals(purchaseTypeCheck.getSubmitEndTimeType())) {
                    Integer submitEndTimeDay = purchaseTypeCheck.getSubmitEndTimeDay();
                    Integer submitEndTimeUnit = purchaseTypeCheck.getSubmitEndTimeUnit();
                    //确定时间则校验截至时间(政府采购及国际招标项目，提交当天不算在内)
                    if (CheckTypeConstants.CONFIRM.equals(req.getSubmitEndTimeType())) {
                        Date submitEndTimeCheck = null;
                        Date submitEndTime = req.getSubmitEndTime();
                        String dateType = null;
                        //天/工作日
                        if (ConfigConstants.END_TIME_UNIT_DAY.equals(submitEndTimeUnit)) {
                            if (BidPurchaseModeConfigEnum.GOVERNMENT_PROCUREMENT.getType().equals(req.getPurchaseType()) || BidPurchaseModeConfigEnum.INTERNATIONAL_BIDDING.getType().equals(req.getPurchaseType())) {
                                submitEndTimeCheck = DateUtil.offsetDay(submitEndTime, -(submitEndTimeDay));
                            } else {
                                submitEndTimeCheck = DateUtil.offsetDay(submitEndTime, -(submitEndTimeDay - 1));
                            }
                            dateType = ConfigConstants.DAY;
                        } else if (ConfigConstants.END_TIME_UNIT_WORK_DAY.equals(submitEndTimeUnit)) {
                            if (BidPurchaseModeConfigEnum.GOVERNMENT_PROCUREMENT.getType().equals(req.getPurchaseType()) || BidPurchaseModeConfigEnum.INTERNATIONAL_BIDDING.getType().equals(req.getPurchaseType())) {
                                submitEndTimeCheck = columnCheckConfigMapper.getOldDayToTime(submitEndTimeDay, submitEndTime);
                            } else {
                                submitEndTimeCheck = columnCheckConfigMapper.getOldDayToTime(submitEndTimeDay - 1, submitEndTime);
                            }
                            dateType = ConfigConstants.WORK_DAY;
                        }
                        long between = DateUtil.between(DateUtil.beginOfDay(startTime), DateUtil.beginOfDay(submitEndTimeCheck), DateUnit.DAY, false);
                        if (between < 0L) {
                            result = MessageConstants.SUBMIT_END_TIME_CHECK + submitEndTimeDay + dateType;
                        }
                    }
                }
            }
            if (CheckTypeConstants.CHECK_SALE_END_TIME.equals(checkType) || CheckTypeConstants.CHECK_ALL.equals(checkType)) {
                //采购文件发售截至时间是否强校验
                if (ConfigConstants.END_TIME_TYPE_CHECK.equals(purchaseTypeCheck.getSaleEndTimeType())) {
                    Integer saleEndTimeDay = purchaseTypeCheck.getSaleEndTimeDay();
                    Integer saleEndTimeUnit = purchaseTypeCheck.getSaleEndTimeUnit();
                    //确定时间、投标文件递交截止时间前一天且投标文件递交截止时间为确定时间 校验时间(政府采购及国际招标项目，提交当天不算在内)
                    if (CheckTypeConstants.CONFIRM.equals(req.getSaleEndTimeType()) || (CheckTypeConstants.PREVIOUS_DAY.equals(req.getSaleEndTimeType()) && CheckTypeConstants.CONFIRM.equals(req.getSubmitEndTimeType()))) {
                        Date saleEndTimeCheck = new Date();
                        Date saleEndTime = new Date();
                        if (CheckTypeConstants.CONFIRM.equals(req.getSaleEndTimeType())) {
                            saleEndTime = req.getSaleEndTime();
                        } else if (CheckTypeConstants.PREVIOUS_DAY.equals(req.getSaleEndTimeType()) && CheckTypeConstants.CONFIRM.equals(req.getSubmitEndTimeType())) {
                            saleEndTime = DateUtil.offsetDay(req.getSubmitEndTime(), -1);
                        }
                        String dateType = null;
                        //天/工作日
                        if (ConfigConstants.END_TIME_UNIT_DAY.equals(saleEndTimeUnit)) {
                            if (BidPurchaseModeConfigEnum.GOVERNMENT_PROCUREMENT.getType().equals(req.getPurchaseType()) || BidPurchaseModeConfigEnum.INTERNATIONAL_BIDDING.getType().equals(req.getPurchaseType())) {
                                saleEndTimeCheck = DateUtil.offsetDay(saleEndTime, -(saleEndTimeDay));
                            } else {
                                saleEndTimeCheck = DateUtil.offsetDay(saleEndTime, -(saleEndTimeDay - 1));
                            }
                            dateType = ConfigConstants.DAY;
                        } else if (ConfigConstants.END_TIME_UNIT_WORK_DAY.equals(saleEndTimeUnit)) {
                            if (BidPurchaseModeConfigEnum.GOVERNMENT_PROCUREMENT.getType().equals(req.getPurchaseType()) || BidPurchaseModeConfigEnum.INTERNATIONAL_BIDDING.getType().equals(req.getPurchaseType())) {
                                saleEndTimeCheck = columnCheckConfigMapper.getOldDayToTime(saleEndTimeDay, saleEndTime);
                            } else {
                                saleEndTimeCheck = columnCheckConfigMapper.getOldDayToTime(saleEndTimeDay - 1, saleEndTime);
                            }
                            dateType = ConfigConstants.WORK_DAY;
                        }
                        long between = DateUtil.between(DateUtil.beginOfDay(startTime), DateUtil.beginOfDay(saleEndTimeCheck), DateUnit.DAY, false);
                        if (between < 0L) {
                            result = MessageConstants.SALE_END_TIME_CHECK + saleEndTimeDay + dateType;
                        }
                    }
                }
            }
        }
        return result;
    }
}
