package com.hzw.sunflower.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@ApiModel("t_project_share_ncc表")
@TableName("t_project_share_ncc")
@Data
public class ProjectShareNcc {

    private Long id;

    /**
     * 项目ID
     *
     * @mbg.generated
     */
    @ApiModelProperty("项目ID")
    private Long projectId;

    /**
     * 分摊人id
     *
     * @mbg.generated
     */
    @ApiModelProperty("分摊人id")
    private Long shareUser;


    /**
     *分摊人名称
     */
    @ApiModelProperty("分摊人名称")
    private String shareUserName;



    /**
     * 分摊部门id
     *
     * @mbg.generated
     */
    @ApiModelProperty("分摊部门id")
    private Long shareDepartment;



    /**
     * 分摊部门名称
     *
     * @mbg.generated
     */
    @ApiModelProperty("分摊部门名称")
    private String shareDepartmentName;

    /**
     * 分摊部门code
     *
     * @mbg.generated
     */
    @ApiModelProperty("分摊部门code")
    private String shareDepartmentCode;

    /**
     *分摊人员code
     *
     * @mbg.generated
     */
    @ApiModelProperty("分摊人员code")
    private String  shareUserCode;

    /**
     * 分摊比例
     *
     * @mbg.generated
     */
    @ApiModelProperty("分摊比例")
    private BigDecimal proportion;


    /**
     * 是否展示A用户选择框 1展示 ,2 不展示
     *
     * @mbg.generated
     */
    @ApiModelProperty("是否展示A用户选择框 1展示 ,2 不展示")
    private Integer isPeopleSelect;


    /**
     * 记账部门id
     *
     * @mbg.generated
     */
    @ApiModelProperty("记账部门id")
    private Long bookkeepId;


    /**
     * 删除标志(0:正常,1:删除 )
     *
     * @mbg.generated
     */
    @ApiModelProperty("删除标志(0:正常,1:删除 )")
    private Integer isDelete;

    /**
     * 修改人
     *
     * @mbg.generated
     */
    @ApiModelProperty("修改人")
    private Long updatedUserId;

    /**
     * 修改时间
     *
     * @mbg.generated
     */
    @ApiModelProperty("修改时间")
    private Date updatedTime;

    /**
     * 创建人
     *
     * @mbg.generated
     */
    @ApiModelProperty("创建人")
    private Long createdUserId;

    /**
     * 创建时间
     *
     * @mbg.generated
     */
    @ApiModelProperty("创建时间")
    private Date createdTime;

    /**
     * 备注
     *
     * @mbg.generated
     */
    @ApiModelProperty("备注")
    private String remark;

    /**
     * 版本号控制并发
     *
     * @mbg.generated
     */
    @ApiModelProperty("版本号控制并发")
    private Integer version;



    private static final long serialVersionUID = 1L;
}
