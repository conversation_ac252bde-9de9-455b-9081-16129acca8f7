package com.hzw.sunflower.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hzw.sunflower.constant.CommonConstants;
import com.hzw.sunflower.constant.constantenum.*;
import com.hzw.sunflower.controller.request.RefundManageReq;
import com.hzw.sunflower.controller.request.RelationNoRefundReq;
import com.hzw.sunflower.dao.BondRefundCountMapper;
import com.hzw.sunflower.datascope.utils.DataScopeUtil;
import com.hzw.sunflower.dto.BondRefundManageDto;
import com.hzw.sunflower.dto.BondRelationNoRefundDto;
import com.hzw.sunflower.entity.*;
import com.hzw.sunflower.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 保证金退还统计
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-15
 */
@Service
@Slf4j
public class BondRefundCountServiceImpl implements BondRefundCountService {

    @Autowired
    private BondRefundCountMapper bondRefundCountMapper;

    @Autowired
    private BondRefundService bondRefundService;

    @Autowired
    private BidWinPeopleService bidWinPeopleService;

    @Autowired
    private BidWinService bidWinService;

    @Override
    public IPage<BondRefundManageDto> queryBondRefundManagePage(RefundManageReq condition) {
        String dataScopeSql= DataScopeUtil.getProjectDataScopeFilterDepartSql("tpe","tpe","p");
        condition.setDataScope(dataScopeSql);
        IPage<BondRefundManageDto> page = condition.buildPage();
        IPage<BondRefundManageDto> resultPage = bondRefundCountMapper.queryBondRefundManagePage(page, condition);
        for(BondRefundManageDto dto:resultPage.getRecords()){
            //查询标包关联供应商总数
            List<BondRelation> list = bondRefundCountMapper.relationCompanyCount(dto.getSectionId());
            dto.setRefundTotal((long) list.size());
            //查询已退还供应商总数
            LambdaQueryWrapper<BondRefund> bondRefundQuery = new LambdaQueryWrapper<>();
            bondRefundQuery.eq(BondRefund::getStatus, BondRefundStatusEnum.SUCCESS.getType());
            bondRefundQuery.eq(BondRefund::getSectionId,dto.getSectionId());
            dto.setRefundAlready(bondRefundService.count(bondRefundQuery));
            //判断代理服务费状态
            if(AgencyFeeTypeEnum.NO_MONEY.getType().equals(dto.getAgencyCostFree())){
                dto.setAgencyFeeAlready(2);
            }else{
                if(dto.getProjectStatusCode()<PackageStatusEnum.SUBMITTING_WINNER_NOTICE.getValue()){
                    dto.setAgencyFeeAlready(2);
                }else{
                    //开标时间
                    String bidOpenTime = dto.getOpenBidTime();
                    if(StringUtils.isNotBlank(bidOpenTime)){
                        //当前时间
                        String timeStr= LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                        if(bidOpenTime.compareTo(timeStr)>0){
                            dto.setAgencyFeeAlready(2);
                        }else{
                            // 判断是否为中标人
                            LambdaQueryWrapper<BidWinPeople> queryWinWrapper = new LambdaQueryWrapper<>();
                            queryWinWrapper.eq(BidWinPeople::getSectionId,dto.getSectionId())
                                    .eq(BidWinPeople::getWinPeopleType, BidWinPeopleTypeEnum.NOTICE.getCode())
                                    .eq(BidWinPeople::getIsWin, CommonConstants.YES);
                            List<BidWinPeople> winPeopleList = bidWinPeopleService.list(queryWinWrapper);
                            dto.setAgencyFeeAlready(2);
                            for(BidWinPeople winPeople:winPeopleList){
                                LambdaQueryWrapper<BondRefund> refundQuery = new LambdaQueryWrapper<>();
                                refundQuery.eq(BondRefund::getSectionId,winPeople.getSectionId());
                                refundQuery.eq(BondRefund::getCompanyId,winPeople.getTendererId());
                                BondRefund fee = bondRefundService.getOne(refundQuery);
                                if(fee==null||CommonConstants.NO.equals(fee.getIsAgencyFee())){
                                    dto.setAgencyFeeAlready(1);
                                } else {
                                    dto.setAgencyFeeAlready(0);
                                }
                            }
                        }
                    }else{
                        dto.setOpenBidTime("-");
                    }
                }
            }
        }
        return page;
    }

    @Override
    public IPage<BondRelationNoRefundDto> queryBondRelationNoRefundPage(RelationNoRefundReq condition) {
        String dataScopeSql= DataScopeUtil.getProjectDataScopeFilterDepartSql("tpe","tpe","p");
        condition.setDataScope(dataScopeSql);
        IPage<BondRelationNoRefundDto> page = condition.buildPage();
        //查询中标候选人信息
        IPage<BondRelationNoRefundDto> resultPage = bondRefundCountMapper.queryBondRelationNoRefundPage(page, condition);
        for(BondRelationNoRefundDto dto:resultPage.getRecords()){
            //判断是否为中标候选人
            LambdaQueryWrapper<BidWinPeople> queryCandidateWrapper = new LambdaQueryWrapper<>();
            queryCandidateWrapper.eq(BidWinPeople::getSectionId,dto.getSectionId())
                    .eq(BidWinPeople::getTendererId,dto.getCompanyId())
                    .eq(BidWinPeople::getWinPeopleType, BidWinPeopleTypeEnum.BID_CANDIDATE_PEOPLE.getCode())
                    .orderByDesc(BidWinPeople::getCreatedTime).last("limit 1");
            BidWinPeople oneCandidate = bidWinPeopleService.getOne(queryCandidateWrapper);
            if(oneCandidate!=null&&CommonConstants.YES.equals(oneCandidate.getIsCandidate())){
                dto.setIsCandidate(CommonConstants.YES);
            }
            // 判断是否为中标人
            LambdaQueryWrapper<BidWinPeople> queryWinWrapper = new LambdaQueryWrapper<>();
            queryWinWrapper.eq(BidWinPeople::getSectionId,dto.getSectionId())
                    .eq(BidWinPeople::getTendererId,dto.getCompanyId())
                    .eq(BidWinPeople::getWinPeopleType, BidWinPeopleTypeEnum.BID_RESULT_PEOPLE.getCode())
                    .orderByDesc(BidWinPeople::getCreatedTime).last("limit 1");
            BidWinPeople one = bidWinPeopleService.getOne(queryWinWrapper);
            if(one!=null&&CommonConstants.YES.equals(one.getIsWin())){
                dto.setIsWin(CommonConstants.YES);
            }
            //若为中标人/中标候选人且中标通知书未盖章，且无备注，则默认备注为“未开中标通知书”
            if (StringUtils.isNotEmpty(dto.getSectionStatus())
                    && StringUtils.isEmpty(dto.getRemark())
                    && Integer.parseInt(dto.getSectionStatus()) < PackageStatusEnum.SUBMITTING_WINNER_NOTICE.getValue()
                    && (CommonConstants.YES.equals(dto.getIsWin()) || CommonConstants.YES.equals(dto.getIsCandidate()))) {
                dto.setRemark("未开中标通知书");
            }
        }
        return resultPage;
    }

    @Override
    public void exportBondRelationNoRefund(RelationNoRefundReq condition, HttpServletResponse response) {
        // 文件名    表头名
        String fileName = "保证金余额";
        if(StringUtils.isNotBlank(condition.getSendStartTime())&&StringUtils.isNotBlank(condition.getSendEndTime())){
            fileName = fileName+condition.getSendStartTime()+"-"+condition.getSendEndTime();
        }
        // xlsx类型
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        String sheetName = "";
        String dataScopeSql= DataScopeUtil.getProjectDataScopeFilterDepartSql("tpe","tpe","p");
        condition.setDataScope(dataScopeSql);
        List<BondRelationNoRefundDto> bondRelationNoRefundList = bondRefundCountMapper.queryBondRelationNoRefundExcel(condition);
        for(BondRelationNoRefundDto dto:bondRelationNoRefundList){
            //判断是否为中标候选人
            LambdaQueryWrapper<BidWinPeople> queryCandidateWrapper = new LambdaQueryWrapper<>();
            queryCandidateWrapper.eq(BidWinPeople::getSectionId,dto.getSectionId())
                    .eq(BidWinPeople::getTendererId,dto.getCompanyId())
                    .eq(BidWinPeople::getWinPeopleType, BidWinPeopleTypeEnum.BID_CANDIDATE_PEOPLE.getCode())
                    .orderByDesc(BidWinPeople::getCreatedTime).last("limit 1");
            BidWinPeople oneCandidate = bidWinPeopleService.getOne(queryCandidateWrapper);
            if(oneCandidate!=null&&CommonConstants.YES.equals(oneCandidate.getIsCandidate())){
                dto.setIsCandidate(CommonConstants.YES);
            }
            // 判断是否为中标人
            LambdaQueryWrapper<BidWinPeople> queryWinWrapper = new LambdaQueryWrapper<>();
            queryWinWrapper.eq(BidWinPeople::getSectionId,dto.getSectionId())
                    .eq(BidWinPeople::getTendererId,dto.getCompanyId())
                    .eq(BidWinPeople::getWinPeopleType, BidWinPeopleTypeEnum.BID_RESULT_PEOPLE.getCode())
                    .orderByDesc(BidWinPeople::getCreatedTime).last("limit 1");
            BidWinPeople one = bidWinPeopleService.getOne(queryWinWrapper);
            if(one!=null&&CommonConstants.YES.equals(one.getIsWin())){
                dto.setIsWin(CommonConstants.YES);
            }
            //若为中标人/中标候选人且中标通知书未盖章，且无备注，则默认备注为“未开中标通知书”
            if (StringUtils.isNotEmpty(dto.getSectionStatus())
                    && StringUtils.isEmpty(dto.getRemark())
                    && Integer.parseInt(dto.getSectionStatus()) < PackageStatusEnum.SUBMITTING_WINNER_NOTICE.getValue()
                    && (CommonConstants.YES.equals(dto.getIsWin()) || CommonConstants.YES.equals(dto.getIsCandidate()))) {
                dto.setRemark("未开中标通知书");
            }
        }
        ExcelWriter excelWrite = null;
        try{
            excelWrite = EasyExcel.write(response.getOutputStream()).build();
        }catch (IOException e){
            e.printStackTrace();
            log.error("导出文件IOException");
        }
        //全部
        sheetName = "全部";
        excelWrite = makeExcel(sheetName, fileName, bondRelationNoRefundList, excelWrite);
        excelWrite.finish();
    }

    /**
     * 导出
     * @param sheetName
     * @param fileName
     * @param bondRelationNoRefundList
     * @param excelWrite
     * @return
     */
    public ExcelWriter makeExcel(String sheetName, String fileName, List<BondRelationNoRefundDto> bondRelationNoRefundList,
                                 ExcelWriter excelWrite) {
        try {
            // 所有行的集合
            List<List<Object>> list = new ArrayList<List<Object>>();
            int n = 1;
            for (BondRelationNoRefundDto dto : bondRelationNoRefundList) {
                // 第 n 行的数据
                List<Object> row = new ArrayList<Object>();
                // 序号
                row.add(n);
                // 供应商名称
                row.add(dto.getCompanyName());
                // 项目编号
                row.add(dto.getPurchaseNumber());
                // 标段/包号
                row.add(dto.getPackageNumber());
                // 余额
                row.add(dto.getBalance());
                // 到账时间
                row.add(dto.getReceiveTime());
                // 中标人/中标候选人
                if(dto.getIsWin()!=null&&1 == dto.getIsWin()){
                    row.add("中标人");
                }else if(dto.getIsCandidate()!=null&&1 == dto.getIsCandidate()){
                    row.add("中标候选人");
                }else{
                    row.add("-");
                }
                // 中标通知书发放时间
                row.add(dto.getSendTime());
                // 备注
                row.add(dto.getRemark());
                list.add(row);
                n++;
            }
            // 动态添加 表头 headList --> 所有表头行集合
            List<List<String>> headList = new ArrayList<List<String>>();

            String[] title = {"序号", "供应商名称", "项目编号", "标段/包号", "余额", "到账时间", "中标人/中标候选人",
                    "中标通知书发放时间", "备注"};
            for (String s : title) {
                List<String> headTitle = new ArrayList<String>();
                headTitle.add(fileName);
                headTitle.add(s);
                // 组装标题头
                headList.add(headTitle);
            }
            // 头的策略
            WriteCellStyle headWriteCellStyle = new WriteCellStyle();
            // 背景色
            headWriteCellStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
            WriteFont headWriteFont = new WriteFont();
            headWriteFont.setFontHeightInPoints((short) 12);
            headWriteCellStyle.setWriteFont(headWriteFont);
            // 内容的策略
            WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
            WriteFont contentWriteFont = new WriteFont();
            // 字体大小
            contentWriteFont.setFontHeightInPoints((short) 12);
            contentWriteCellStyle.setWriteFont(contentWriteFont);
            //设置 自动换行
            contentWriteCellStyle.setWrapped(true);
            //设置 垂直居中
            contentWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            // 这个策略是 头是头的样式 内容是内容的样式 其他的策略可以自己实现
            HorizontalCellStyleStrategy horizontalCellStyleStrategy =
                    new HorizontalCellStyleStrategy(headWriteCellStyle, contentWriteCellStyle);
            WriteSheet writeSheet = EasyExcel.writerSheet(sheetName).head(headList)
                    .registerWriteHandler(horizontalCellStyleStrategy)
                    .build();
            excelWrite.write(list, writeSheet);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("导出异常");
        }
        return excelWrite;
    }
}
