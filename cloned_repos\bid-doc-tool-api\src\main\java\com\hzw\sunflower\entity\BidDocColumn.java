package com.hzw.sunflower.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;

import com.hzw.sunflower.common.BaseBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 招标文件编制字段配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-26
 */
@Getter
@Setter
@TableName("t_bid_doc_column")
@ApiModel(value = "BidDocColumn对象", description = "招标文件编制字段配置表")
public class BidDocColumn extends BaseBean implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("编制文件id")
    private Long docId;

    @ApiModelProperty("目录id")
    private Long catalogId;

    @ApiModelProperty("字段")
    private String columnCode;

    @ApiModelProperty("字段值")
    private String columnValue;

}
