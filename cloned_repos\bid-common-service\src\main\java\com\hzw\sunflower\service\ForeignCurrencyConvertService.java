package com.hzw.sunflower.service;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hzw.sunflower.dto.CurrencyConvertDto;
import com.hzw.sunflower.entity.ForeignCurrencyConvert;

/**
 * <p>
 * 外币转换表 服务类
 * </p>
 *
 * <AUTHOR> Gen
 * @since 2022-12-15
 */
public interface ForeignCurrencyConvertService extends IService<ForeignCurrencyConvert> {

    /**
     * 获取外币转换信息
     * @param relationId
     * @param type
     * @return
     */
    CurrencyConvertDto getCurrencyConvert(Long relationId, Integer type);

}
