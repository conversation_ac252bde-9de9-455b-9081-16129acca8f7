///***
/// * common Refresh Tip when release to refresh event fired for DataListView Widget
/// *
/// * <AUTHOR>
/// * @date 20101023
/// */

import "package:flutter/material.dart";

class DefaultRefreshReleaseTipWidget extends StatelessWidget {

  const DefaultRefreshReleaseTipWidget({Key key}) : super(key: key);

  static const String _releaseTip = '释放更新';

  @override
  Widget build(BuildContext context) {
    return Container(
      constraints: BoxConstraints(minHeight: 4, maxHeight: 20),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: <Widget>[
          Icon(
            Icons.arrow_upward,
            size: 15,
          ),
          Text(_releaseTip),
        ],
      ),
    );
  }

}
