package com.hzw.sunflower.controller.response;

import com.hzw.sunflower.dto.ApprovalOpinionDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 处理进度返回参数
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@ApiModel(description = "处理进度返回参数 ")
@Data
public class ApprovalOpinionVO extends ApprovalOpinionDTO {

    @ApiModelProperty(value = "审批时间字符串", position = 999)
    private String dateString;
}
