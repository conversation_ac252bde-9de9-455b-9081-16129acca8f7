package com.hzw.sunflower.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzw.sunflower.constant.constantenum.BidRoundEnum;
import com.hzw.sunflower.controller.request.BidDocumentPurchaseTypeReq;
import com.hzw.sunflower.dao.BidDocumentPurchaseTypeMapper;
import com.hzw.sunflower.entity.BidDocumentPurchaseType;
import com.hzw.sunflower.service.BidDocumentPurchaseTypeService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;


/**
 * <AUTHOR>
 * @date ：Created in 2021/7/8 13:55
 * @description：标段包与文件目录关系表service
 * @modified By：`
 * @version: 1.0
 */
@Service
public class BidDocumentPurchaseTypeServiceImpl extends ServiceImpl<BidDocumentPurchaseTypeMapper, BidDocumentPurchaseType> implements BidDocumentPurchaseTypeService {

    /**
     * 开评标备案采购方式
     * @param bidDocumentPurchaseType
     * @return
     */
    @Override
    public Boolean savePurchaseType(BidDocumentPurchaseTypeReq bidDocumentPurchaseType) {
        BidDocumentPurchaseType purchaseType = this.getOne(new LambdaQueryWrapper<BidDocumentPurchaseType>()
                .eq(BidDocumentPurchaseType::getProjectId, bidDocumentPurchaseType.getProjectId())
                .eq(BidDocumentPurchaseType::getSectionId, bidDocumentPurchaseType.getSectionId())
                .eq(BidDocumentPurchaseType::getBidRound, BidRoundEnum.HS.getType())
                .eq(BidDocumentPurchaseType::getEvaluationCount, bidDocumentPurchaseType.getEvaluationCount()));
        if (purchaseType == null) {
            purchaseType = new BidDocumentPurchaseType();
            BeanUtils.copyProperties(bidDocumentPurchaseType, purchaseType);
            purchaseType.setBidRound(BidRoundEnum.HS.getType());
        } else {
            purchaseType.setPurchaseType(bidDocumentPurchaseType.getPurchaseType());
            purchaseType.setPurchaseMode(bidDocumentPurchaseType.getPurchaseMode());
            purchaseType.setPurchaseStatus(bidDocumentPurchaseType.getPurchaseStatus());
            purchaseType.setPurchaseModeName(bidDocumentPurchaseType.getPurchaseModeName());
        }
        return this.saveOrUpdate(purchaseType);
    }
}
