package com.hzw.sunflower.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.hzw.sunflower.common.BaseBean;
import io.swagger.annotations.ApiModel;
import java.io.Serializable;
import java.util.Date;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
* Created by pj on 2023/11/03
*/
@ApiModel("t_seal_management表")
@TableName("t_seal_management")
@Data
public class SealManagement extends BaseBean implements Serializable {
    private Long id;

    /**
     * 印章名称
     *
     * @mbg.generated
     */
    @ApiModelProperty("印章名称")
    private String sealName;

    /**
     * 归属人
     *
     * @mbg.generated
     */
    @ApiModelProperty("归属人")
    private String belonger;

    @ApiModelProperty("印章类型 1 电子章 2实体章")
    private Integer sealType;

    /**
     * 归属人id
     *
     * @mbg.generated
     */
    @ApiModelProperty("归属人id")
    private Long belongerId;

    /**
     * 归属人回显
     *
     * @mbg.generated
     */
    @ApiModelProperty("归属人回显")
    private String belongerView;

    /**
     * 启用状态 1启用 2禁用
     *
     * @mbg.generated
     */
    @ApiModelProperty("启用状态 1启用 2禁用")
    private Integer status;


    private static final long serialVersionUID = 1L;
}