package com.hzw.ssm.sys.project.dao;

import java.util.Date;
import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

import com.hzw.ssm.expert.entity.ExpertInfoEntity;
import com.hzw.ssm.expert.entity.ResultProjectExpertInfo;
import com.hzw.ssm.sys.project.entity.ApplyRecordEntity;
import com.hzw.ssm.sys.project.entity.ConditionEntity;
import com.hzw.ssm.sys.project.entity.ConditionRecordEntity;
import com.hzw.ssm.sys.project.entity.DeleteRecord;
import com.hzw.ssm.sys.project.entity.ExtractRecordEntity;
import com.hzw.ssm.sys.project.entity.LoginRecord;
import com.hzw.ssm.sys.project.entity.ParamTypeEntity;
import com.hzw.ssm.sys.project.entity.ProjectAuditEntity;
import com.hzw.ssm.sys.project.entity.ProjectEntity;
import com.hzw.ssm.sys.project.entity.ResultByProjectEntity;
import com.hzw.ssm.sys.project.entity.ResultEntity;
import com.hzw.ssm.sys.project.entity.TmpMajor;
import com.hzw.ssm.sys.user.entity.UserEntity;

public interface ProjectMapper {

	/**
	 * 保存项目信息
	 * 
	 * @param project
	 */
	public void saveProject(ProjectEntity project);

	/**
	 * 保存项目信息 （多条）
	 * 
	 * @param project
	 */
	public void saveProjectList(List<ProjectEntity> projectList);

	/**
	 * 更新项目信息
	 * 
	 * @param project
	 */
	public void updateProject(ProjectEntity project);

	/**
	 * 更新抽取条件
	 * 
	 * @param conEntity
	 */
	public void updateCondition(ConditionEntity conEntity);

	/**
	 * 更新抽取条件 是否二次抽取[指定专家]
	 * 
	 * @param conEntity
	 */
	public void updateTwoCon(ConditionEntity conEntity);

	/**
	 * 保存抽取条件
	 * 
	 * @param conEntity
	 */
	public void saveCondition(ConditionEntity conEntity);

	/**
	 * 根据项目id查询项目信息
	 * 
	 * @param project
	 * @return
	 */
	public ProjectEntity queryProjectById(ProjectEntity project);

	/**
	 * 根据项目DecimationBatch查询项目信息
	 * 
	 * @param project
	 * @return
	 */
	public List<ProjectEntity> queryProjectByDecimationBatch(ProjectEntity project);

	/**
	 * 根据项目id查询项目信息(list)
	 * 
	 * @param project
	 * @return
	 */
	public List<ProjectEntity> queryProjectListById(ProjectEntity project);

	/**
	 * 根据条件id查询抽取条件
	 * 
	 * @param conEntity
	 * @return
	 */
	public ConditionEntity queryConditionById(ConditionEntity conEntity);

	/**
	 * 根据抽取条件抽取专家
	 * 
	 * @param conEntity
	 * @return
	 */
	public List<ExpertInfoEntity> queryExpertsByRule(ConditionEntity conEntity);

	/**
	 * 根据抽取条件抽取专家
	 * 
	 * @param conEntity
	 * @return
	 */
	public List<ExpertInfoEntity> copyQueryExpertsByRule(ConditionEntity conEntity);

	/**
	 * 根据抽取条件抽取专家
	 * 
	 * @param conEntity
	 * @return
	 */
	public List<ExpertInfoEntity> copyQueryExpertsByRule2(ConditionEntity conEntity);

	/**
	 * 根据抽取条件查询符合条件的专家人数
	 * 
	 * @param conEntity
	 * @return
	 */
	public Integer queryExpertCountByRule(ConditionEntity conEntity);

	/**
	 * 根据抽取条件查询符合条件的专家人数
	 * 
	 * @param conEntity
	 * @return
	 */
	public Integer copyQueryExpertCountByRule(ConditionEntity conEntity);

	/**
	 * 查询项目已抽取的专家
	 * 
	 * @param conEntity
	 * @return
	 */
	public List<ResultEntity> queryExtractedExperts(ConditionEntity conEntity);

	/**
	 * 保存抽取专家信息
	 * 
	 * @param result
	 */
	public void saveExtractedExpert(List<ResultEntity> resultList);

	/**
	 * 更新抽取专家信息
	 * 
	 * @param result
	 */
	public void updateExtractedExpert(ResultEntity result);

	/**
	 * 更新违规记录
	 * 
	 * @param result
	 */
	public void updateExtractExIllegal(ResultEntity result);

	/**
	 * 查询抽取结果的最大批次
	 * 
	 * @param conEntity
	 * @return
	 */
	public Long queryResultSort(ConditionEntity conEntity);

	/**
	 * 删除已存在的项目同条件id的记录
	 * 
	 * @param conEntity
	 */
	public void deleteResultInfo(ConditionEntity conEntity);

	/**
	 * 项目列表信息
	 * 
	 * @param entity
	 * @return
	 */
	public List<ProjectEntity> queryPageProjectList(ProjectEntity entity);

	/**
	 * 项目列表信息
	 * 
	 * @param entity
	 * @return
	 */
	public List<ProjectEntity> queryPageByErrorProjectList(ProjectEntity entity);

	/**
	 * 项目列表信息
	 * 
	 * @param entity
	 * @return
	 */
	public List<ProjectEntity> queryErrorProjectList2(ProjectEntity entity);

	/**
	 * 查询抽取条件信息
	 * 
	 * @param entity
	 * @return
	 */
	public List<ConditionEntity> queryConditionList(ConditionEntity entity);

	/**
	 * 获得项目信息
	 * 
	 * @param projectId
	 * @return
	 */
	public ProjectEntity queryProjectInfoById(String projectId);

	/**
	 * 获得某抽取条件下同意参加的记录
	 * 
	 * @param conEntity
	 * @return
	 */
	public List<ResultEntity> queryAgreeFromResult(ConditionEntity conEntity);

	/**
	 * 查询已同意参标的国家级（或地方级）专家个数
	 * 
	 * @param conEntity
	 * @return
	 */
	public Integer queryAgreeExpertCount(ConditionEntity conEntity);

	/**
	 * 统计参加供应商未通知的数量
	 * 
	 * @param conEntity
	 * @return
	 */
	public Integer countNotExtractedResult(ConditionEntity conEntity);

	/**
	 * 更新项目状态
	 * 
	 * @param projectEntity
	 */
	public int updateProjectStatus(ProjectEntity projectEntity);

	/**
	 * 查询抽取专家结果信息
	 * 
	 * @param entity
	 * @return
	 */
	public List<ExpertInfoEntity> queryExtractedResult(ConditionEntity entity);

	/**
	 * 查询抽取记录信息
	 * 
	 * @param projectId
	 * @return
	 */
	public List<ExtractRecordEntity> queryExtractRecordList(String projectId);

	/**
	 * 查询抽取记录信息
	 * 
	 * @param projectId
	 * @return
	 */
	public List<ExtractRecordEntity> copyQueryExtractRecordList(String projectId);

	/**
	 * 保存指定抽取信息申请说明
	 * 
	 * @param apply
	 */
	public void saveApplyInfo(ApplyRecordEntity apply);

	/**
	 * 查询指定专家
	 * 
	 * @param conEntity
	 * @return
	 */
	public List<ExpertInfoEntity> queryPagePointExperts(ConditionEntity conEntity);

	/**
	 * 根据专家身份证查询专家是否存在
	 * 
	 * @param entity
	 * @return
	 */
	public List<ExpertInfoEntity> queryExperts(ExpertInfoEntity entity);

	/**
	 * 删除指定专家抽取结果
	 * 
	 * @param result
	 * @return
	 */
	public void deletePointResult(ResultEntity result);

	/**
	 * 提交指定专家结果
	 * 
	 * @param result
	 */
	public void submitPointResult(ResultEntity result);

	/**
	 * 专家参与项目列表查询
	 * 
	 * @param expert_id
	 * @return
	 */
	public List<ProjectEntity> queryPageExpertJoinProjectList(ProjectEntity colProject);

	/**
	 * 查询抽取记录中的抽取条件
	 * 
	 * @param conEntity
	 * @return
	 */
	public ConditionEntity queryExtractConditionById(ConditionEntity conEntity);

	/**
	 * 查询项目申请记录表
	 * 
	 * @param entity
	 * @return
	 */
	public ApplyRecordEntity queryApplyRecord(ApplyRecordEntity entity);

	/**
	 * 查询项目最新一条申请记录表
	 * 
	 * @param entity
	 * @return
	 */
	public ApplyRecordEntity queryNewApplyRecord(ApplyRecordEntity entity);

	/**
	 * 批量更新专家抽取通知结果
	 * 
	 * @param resultList
	 */
	public void batchUpdateExtractedExpert(List<ResultEntity> resultList);

	/**
	 * 根据手机号查询所有抽取结果
	 * 
	 * @param mobile
	 */
	public List<ResultEntity> queryExtracedResultByPhone(ResultEntity resultEntity);

	/**
	 * 向项目审核记录表插入数据
	 * 
	 * @param entity
	 */
	public void saveProjectAudit(ProjectAuditEntity entity);

	/**
	 * 指定专家申请审核通过向抽取条件表插入数据
	 * 
	 * @param entity
	 */
	public void saveAuditCondition(ConditionEntity entity);

	/**
	 * 查询所有项目负责人
	 * 
	 * @return
	 */
	public List<UserEntity> queryProjectManager(UserEntity userEntity);

	/**
	 * 根据角色信息查询用户信息
	 * 
	 * @return
	 */
	public List<UserEntity> queryUserByRole(UserEntity userEntity);

	/**
	 * 根据项目创建人id获得项目信息
	 * 
	 * @param project
	 * @return
	 */
	public List<ProjectEntity> queryPageProjectInitList(ProjectEntity project);

	/**
	 * @param projectEntity
	 * @return
	 */
	public ResultEntity queryCallTime(ProjectEntity projectEntity);

	/**
	 * 查询项目最小抽取时间
	 * 
	 * @param conditionId
	 * @return
	 */
	public String queryMinExtractTime(String conditionId);

	/**
	 * 根据项目ID，获取项目审批信息
	 * 
	 * @param projectAuditEntity
	 * @return
	 */
	public ProjectAuditEntity getProAuditInfoByProId(ProjectAuditEntity projectAuditEntity);

	/**
	 * 根据项目ID，获取项目审批信息(语音：查询当前批次在指定的审核类型下，是否已经审核过)
	 * 
	 * @param projectAuditEntity
	 * @return
	 */
	public ProjectAuditEntity getProAuditInfoByProIdToVoice(ProjectAuditEntity projectAuditEntity);

	/**
	 * 查询专家除参加当前项目外，在评审当天还参加过是否其他项目
	 * 
	 * @param resultEntity
	 * @return
	 */
	public List<ProjectEntity> queryExtractExpertList(ProjectEntity project);

	/**
	 * 查询专家参与项目
	 * 
	 * @param project
	 * @return
	 */
	public List<ProjectEntity> queryPageExpertPro(ProjectEntity project);

	/**
	 * 根据项目ID，获取项目审批列表信息
	 * 
	 * @param project
	 * @return
	 */
	public List<ProjectAuditEntity> getAuditInfoById(ProjectAuditEntity project);

	/**
	 * 查询国招业务平台项目
	 * 
	 * @param projectNo
	 * @return
	 */
	public List<ProjectEntity> queryPageJSGZProjectList(ProjectEntity project);

	/**
	 * 根据项目编号和评审时间查询专家参标情况
	 * 
	 * @param id
	 * @return
	 */

	public List<ResultByProjectEntity> queryByProject(ProjectEntity projectEntity);

	/**
	 * 根据项目id删除记录
	 * 
	 * @param id
	 * @return
	 */
	public void deleteRecordByProjectId(ProjectEntity projectEntity);

	/**
	 * 插入删除记录
	 * 
	 * @param id
	 * @return
	 */
	public void addDeleteRecord(DeleteRecord deleteRecord);
	


	/**
	 * 根据项目创建人id获得已删除项目信息
	 * 
	 * @param project
	 * @return
	 */
	public List<ProjectEntity> queryPageDeleteProjectInitList(ProjectEntity project);

	/**
	 * 根据项目id查询删除项目信息
	 * 
	 * @param project
	 * @return
	 */
	public ProjectEntity queryDeleteProjectById(ProjectEntity project);

	/**
	 * 根据项目Decimationbatch查询删除项目信息
	 * 
	 * @param project
	 * @return
	 */
	public List<ProjectEntity> queryDeleteProjectByDecimationbatch(ProjectEntity project);

	/**
	 * 查询删除项目原因
	 * 
	 * @param project
	 * @return
	 */
	public String getDeleteReason(DeleteRecord deleteRecord);

	/**
	 * 查询恢复删除项目原因
	 * 
	 * @param project
	 * @return
	 */
	public String getRestoreDeleteReason(DeleteRecord deleteRecord);

	/**
	 * 更新删除记录表为申请恢复状态
	 * 
	 * @param project
	 * @return
	 */
	public void modifyDeleteRecord(DeleteRecord deleteRecord);

	/**
	 * 查出删除记录list
	 * 
	 * @param project
	 * @return
	 */
	public List<DeleteRecord> getDeleteRecordList();

	/**
	 * 申请删除恢复项目列表信息
	 * 
	 * @param entity
	 * @return
	 */
	public List<ProjectEntity> queryRestoreDeletePageProjectList(ProjectEntity entity);

	/**
	 * 删除恢复
	 * 
	 * @param entity
	 * @return
	 */
	public void restoreDeleteProject(ProjectEntity projectEntity);

	/**
	 * 清除删除表记录by项目id
	 * 
	 * @param entity
	 * @return
	 */
	public void clearDeleteProject(ProjectEntity projectEntity);

	/**
	 * 根据流水号查询抽取条件ID
	 * 
	 * @param entity
	 * @return
	 */
	public List<ProjectEntity> queryConditionIdByBatch(ProjectEntity entity);

	/**
	 * 
	 * @param 根据项目ID查询抽取条件ID
	 * @return
	 */
	public ProjectEntity queryConditionIdByProjectId(ProjectEntity entity);

	/**
	 * 更新项目抽取状态（特定情境下使用）
	 * 
	 * @param projectEntity
	 */
	public void updateChouProjectStatus(ProjectEntity projectEntity);

	/**
	 * 根据条件id查询抽取结果 （szyc）
	 * 
	 * @param conEntity
	 * @return
	 */
	public List<ExpertInfoEntity> queryExpertInfoByBatch(ConditionEntity conEntity);

	/**
	 * 修改抽取条件中的抽取方法
	 * 
	 * @param conEntity
	 * @return
	 */
	public void updateConditionMethod(ConditionEntity conEntity);

	/**
	 * 根据抽取条件编号
	 * 
	 * @param conEntity
	 * @return
	 */
	public List<ExpertInfoEntity> queryExpertInfoById(ConditionEntity conEntity);

	/**
	 * 根据流水号查询项目
	 * 
	 * @param entity
	 * @return
	 */
	public List<ProjectEntity> queryProListByBatch(ProjectEntity entity);

	/**
	 * 查询已同意参标专家个数
	 * 
	 * @param conEntity
	 * @return
	 */
	public Integer queryConfirmExpertCount(ConditionEntity conEntity);

	/**
	 * 临时表，处理sql条件文本过大
	 * 
	 * @param tmp
	 */
	public void insertIntoTmpMajor(TmpMajor tmp);

	/**
	 * 删除临时表，处理sql条件文本过大
	 * 
	 * @param tmp
	 */
	public void deleteTmpMajor();

	/**
	 * 根据流水号等条件查询项目
	 * 
	 * @param entity
	 * @return
	 */
	public List<ProjectEntity> queryProjectList(ProjectEntity entity);

	/**
	 * 根据流水号等条件查询项目
	 * 
	 * @param entity
	 * @return
	 */
	public List<ProjectEntity> queryProjectList2(ProjectEntity entity);

	/**
	 * 根据开标时间查询当天已被抽取的专家
	 * 
	 * @param conEntity
	 * @return
	 */
	public List<ExpertInfoEntity> queryExpertsByBidTime(ConditionEntity conEntity);

	/**
	 * 根据开标时间查询开标当天，已抽取未确认的专家所对应的批次号
	 * 
	 * @param conEntity
	 * @return
	 */
	public List<ConditionEntity> queryDecimationbatchByBidTime(ConditionEntity conEntity);

	/**
	 * 根据批次号查询抽取条件
	 * 
	 * @param entity
	 * @return
	 */
	public List<ConditionEntity> queryConditionListByBatch(ProjectEntity entity);

	/**
	 * 查询所有未通知的专家信息
	 * 
	 * @param entity
	 * @return
	 */
	public List<ExpertInfoEntity> queryProjectExpertInfo(ConditionEntity entity);

	public List<ExpertInfoEntity> queryExpertByBatch(ConditionEntity entity);
	/**
	 * 判断短信批次号在抽取当天是否重复[系统自动发送短信定时器使用]
	 * 
	 * @param entity
	 * @return
	 */
	public List<ResultEntity> queryExtracedResultToMessage(ResultEntity entity);

	/***************************
	 * 语音接口调用方法 start
	 *****************************************************/
	/**
	 * 修改专家状态
	 * 
	 * @param entity
	 */
	public int updateExtractedExpertTOVoice(ResultEntity entity);
	
	/**
	 * 修改专家状态
	 * 
	 * @param entity
	 */
	public int updateProjectEndExpertTOVoice(ResultEntity entity);


	/**
	 * 将未通知的专家修改为应急通知EXPERT_WAY
	 * 
	 * @param entity
	 */
	public int updateExtractedExpertTOExpertWay(ResultEntity entity);

	/**
	 * 自动发送语音功能查询[语音]
	 * 
	 * @param entity
	 * @return
	 */
	public List<ResultEntity> queryExtracedResultToVoice(ResultEntity entity);

	/**
	 * 根据专家抽取方式查询所有正在抽取中的项目（同一批次的项目合并成一条语句）[语音、短信]
	 * 
	 * @param conEntity
	 * @return
	 */
	public List<ProjectEntity> queryProjectTOVoice(ConditionEntity conEntity);

	/**
	 * 修改抽取条件中的抽取方法并且留痕[语音]
	 * 
	 * @param conEntity
	 * @return
	 */
	public int updateConditionCurrentStatus(ConditionEntity conEntity);

	public int updateConditionStatusByDecimationBatch(ProjectEntity projectEntity);

	/**
	 * 根据流水号查询抽取结果
	 * 
	 * @param conEntity
	 * @return
	 */
	public List<ResultEntity> queryAllExtractedExperts(ResultEntity entity);
	/**
	 * 查询专家大于指定时间段频次的集合
	 * @param conEntity
	 * @return
	 */
	public List<ResultEntity> queryExtractedFrequency(ConditionEntity conEntity);
	/**
	 * 
	 * @param 根据批次号删除已抽取专家
	 * @return
	 */
	public int deleteExtractResultByDecimationBatch(ResultEntity entity);

	/**
	 * 修改专家是否接收短信/通知综合处状态
	 * 
	 * @param resultEntity
	 */
	public int updateExtractedSmsStartus(ResultEntity resultEntity);

	/***************************
	 * 语音接口调用方法 end
	 *****************************************************/
	public List<ResultProjectExpertInfo> queryPageAllProjectExtractedExperts(ResultProjectExpertInfo eInfo);

	/**
	 * 
	 * 函数功能描述：修改项目中的变更类型状态
	 * 
	 * @return
	 */
	public int modifyCancelProject(ProjectEntity project);

	/**
	 * 
	 * 函数功能描述：将参加状态全部改为不参加
	 * 
	 * @return
	 */
	public int modifyJoinStaus(ProjectEntity project);

	/**
	 * 变更抽取人数
	 * @param conditionRecordEntity
	 * @return
	 */
	public int updateConditionPersonNum(ConditionRecordEntity conditionRecordEntity);

	public int updateConditionPerson(ConditionRecordEntity conditionRecordEntity);
	
	public ResultEntity checkExpertByBidTime(@Param("bidTime") String bidTime,@Param("idNo")String idNo);

	public List<ProjectEntity> queryPageProjectInfo(ProjectEntity  project);

	List<ProjectEntity> queryPDFProjectInfo(ProjectEntity project);
	public List<LoginRecord> queryPageLoginRecordList(LoginRecord loginRecord);
	public void saveLoginInfo(LoginRecord loginRecord);
	
	public Integer judgeExpertIsAtendProject(ResultEntity result);
	
	/**
	 * 根据抽取条件抽取专家
	 * 
	 * @param conEntity
	 * @return
	 */
	public List<ExpertInfoEntity> queryExpertsByRules(ConditionEntity conEntity);
	
	/**
	 * 根据开标时间查询开标当天参加/不参加的专家
	 * @param openTime 格式 yyyy-mm-dd
	 * @return
	 */
	public List<ExpertInfoEntity> queryOpenTimeExper(String openTime);
	
	/**
	 *  查询该专业类型的参加人数
	 * @param conEntity
	 * @return
	 */
	public List<ExpertInfoEntity>  queryExpertCount(ConditionEntity conEntity);
	
	/**
	 *  查询抽取条件id 查询所有专家
	 * @param conEntity
	 * @return
	 */
	public List<ParamTypeEntity>  queryConditionId(String conid);
	
}
