package com.hzw.sunflower.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hzw.sunflower.entity.OssFile;

/**
 * <p>
 * OSS附件  服务类
 * </p>
 *
 * <AUTHOR> Gen
 * @since 2022-11-25
 */
public interface OssFileService extends IService<OssFile> {


    /**保存文件
     * @throws Exception
     */
    public Long saveOssFile(String fileName,String filePath) throws Exception;

    /**
     * 根据主键ID查询OSS附件 信息
     *
     * @param id 主键ID
     * @return OSS附件 信息
     */
    public OssFile getOssFileById(Long id);
}
