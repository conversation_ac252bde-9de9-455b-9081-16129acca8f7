package com.hzw.sunflower.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.hzw.sunflower.common.BaseBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 邮件信息
 * @ClassName:EmailInfo
 * @Auther: lijinxin
 * @Description:
 * @Date: 2023/3/2 16:03
 * @Version: v1.0
 */
@ApiModel("邮件信息表")
@TableName("t_email_info")
@Data
public class EmailInfo extends BaseBean {

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("项目id")
    private Long projectId;


    @ApiModelProperty("轮次")
    private Integer bidRound;

    @ApiModelProperty("业务类型")
    private Integer bussinessType;

    @ApiModelProperty("业务id")
    private Long bussinessId;

    @ApiModelProperty("邮件信息id")
    private String messgaeId;

    @ApiModelProperty("邮件标题")
    private String subject;

    @ApiModelProperty("发件日期")
    private Date sentDate;

    @ApiModelProperty("收件日期")
    private Date receiveDate;

    @ApiModelProperty("收件人")
    private String toNames;

    @ApiModelProperty("收件人邮箱")
    private String toAddress;

    @ApiModelProperty("发件人邮箱")
    private String fromAddress;

    @ApiModelProperty("发件人")
    private String fromName;

    @ApiModelProperty("文件内容")
    private String content;

    @ApiModelProperty("文件内容id")
    private Long contentOssId;



}
