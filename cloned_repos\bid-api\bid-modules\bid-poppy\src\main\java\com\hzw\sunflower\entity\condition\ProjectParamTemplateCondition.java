package com.hzw.sunflower.entity.condition;

import com.hzw.sunflower.common.BaseCondition;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 项目参数表 查询条件
 *
 * <AUTHOR>
 * @version 1.0.0 2021-04-14
 */
@ApiModel(description = "项目参数表 查询条件")
@Data
public class ProjectParamTemplateCondition extends BaseCondition {
    /**
     * 版本号
     */
    private static final long serialVersionUID = -8870226821357572658L;

    @ApiModelProperty(value = "父ID", position = 7)
    private Long parentId;
}