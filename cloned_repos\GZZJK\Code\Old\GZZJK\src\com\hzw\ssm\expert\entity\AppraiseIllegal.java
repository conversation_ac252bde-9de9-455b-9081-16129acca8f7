package com.hzw.ssm.expert.entity;

import com.hzw.ssm.fw.base.BaseEntity;

/**
 * 专家评价表
 * 
 * <AUTHOR> @date 2014-10-17
 */
public class AppraiseIllegal extends BaseEntity {
	private static final long serialVersionUID = 1L;

	private String appraise_illegal_id;// 主键

	private String extract_result_id;//专家结果ID
	
	private String illegal_content;//违规或其他情况
	
	private String handling_opinions = "";//处理意见
	
	private String remark = "";//备注
	
	private Integer processing_status;//处理状态：1：项目负责人已处理2：专家管理处已处理3：公司领导已处理
	
	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public Integer getProcessing_status() {
		return processing_status;
	}

	public void setProcessing_status(Integer processing_status) {
		this.processing_status = processing_status;
	}

	public String getAppraise_illegal_id() {
		return appraise_illegal_id;
	}

	public void setAppraise_illegal_id(String appraise_illegal_id) {
		this.appraise_illegal_id = appraise_illegal_id;
	}

	public String getExtract_result_id() {
		return extract_result_id;
	}

	public void setExtract_result_id(String extract_result_id) {
		this.extract_result_id = extract_result_id;
	}

	public String getIllegal_content() {
		return illegal_content;
	}

	public void setIllegal_content(String illegal_content) {
		this.illegal_content = illegal_content;
	}

	public String getHandling_opinions() {
		return handling_opinions;
	}

	public void setHandling_opinions(String handling_opinions) {
		this.handling_opinions = handling_opinions;
	}
}
