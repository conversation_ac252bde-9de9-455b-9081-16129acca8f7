package com.hzw.sunflower.controller.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.*;

/**
 * 公告保存 请求实体
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@ApiModel(description = "公告保存请求实体")
@Data
public class UpdateNoticeREQ {

    @ApiModelProperty(value = "项目ID", position = 1)
    private Long projectId;

    @ApiModelProperty(value = "公告ID", position = 2)
    private Long noticeId;

    @ApiModelProperty(value = "公告名称", position = 3)
    private String noticeName;

    @ApiModelProperty(value = "公告内容", position = 4)
    private String noticeContent;

//    @ApiModelProperty(value = "公告拟发布时间", position = 5)
//    private Date intendedReleaseTime;
//
//    @ApiModelProperty(value = "公告截止时间", position = 6)
//    private Date endTime;

    @ApiModelProperty(value = "关联包段ID的数组", position = 5)
    private List<Long> sectionIds;

    @ApiModelProperty(value = "公告附件", position = 6)
    private List<Long> annex;

    @ApiModelProperty(value = "关联媒体信息的数组", position = 7)
    private List<NoticeMediaREQ> medias;

    @ApiModelProperty(value = "暂存标志，为true代表暂存，false为提交", position = 8)
    private Boolean saveForShort;

    /**
     * 招标公告阶段： 0：默认无阶段； 1：第一轮； 2：第二轮
     */
    @ApiModelProperty(value = "招标公告阶段", position = 9)
    private Integer bidRound;

    /**
     * 是否必须包含媒体： 0：否 1 是
     */
    @ApiModelProperty(value = "是否必须包含媒体", position = 9)
    private Integer mustHasMedias;

    @ApiModelProperty(value = "公告文件Id")
    private Long noticeFileId;
}
