package com.hzw.ssm.sys.system.service;

import java.util.List;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.hzw.ssm.fw.base.BaseService;
import com.hzw.ssm.fw.util.CommUtil;
import com.hzw.ssm.fw.util.StringUtil;
import com.hzw.ssm.sys.system.dao.MenuMapper;
import com.hzw.ssm.sys.system.entity.MenuEntity;

/**
 * 菜单表操作类
 * 
 * <AUTHOR>
 * 
 */
@Service
public class MenuService extends BaseService {

	private static Log log = LogFactory.getLog(MenuService.class);

	@Autowired
	private MenuMapper menuMapper;

	/**
	 * 加载树形菜单
	 */
	public String queryMenuList() {
		String str = null;
		List<MenuEntity> dataList = menuMapper.queryMenuList();
		if (dataList != null && dataList.size() > 0) {
			StringBuffer menustr = new StringBuffer();
			StringBuffer con = new StringBuffer();
			for (int i = 0; i < dataList.size(); i++) {
				MenuEntity menu = dataList.get(i);
				con.append("tree.add(" + menu.getMenu_id() + "," + menu.getParent_menu_id() + ",'"
						+ menu.getMenu_name() + "','findMenu?menuEntity.menu_id=" + menu.getMenu_id()
						+ "','','right','','','');\n");
			}
			menustr.append(con.toString());
			str = menustr.toString();
		}
		return str;
	}

	/**
	 * 显示某个菜单,这是右边的页面
	 */
	public MenuEntity findMenu(MenuEntity menu) {
		MenuEntity menuEntity = new MenuEntity();
		if (null != menu.getMenu_id()) {// 如果是点击了菜单则显示相关菜单的信息
			menuEntity = searchMuen(menu);
			return menuEntity;
		}
		List<MenuEntity> dataList = menuMapper.getMenuById(menu);
		if (null != dataList && dataList.size() > 0) {// 初始化默认显示一条菜单的信息
			MenuEntity m = (MenuEntity) dataList.get(0);
			m.setParentMenuName("根菜单");
			m.setHasMenu(checkChild(m));
			if (null != m.getParent_menu_id() && !m.getParent_menu_id().equals(-1L))
				m = searchMuen(m);
			menuEntity = m;
		}
		return menuEntity;
	}

	/**
	 * 查找菜单节点以及取得父节点的名称
	 */
	private MenuEntity searchMuen(MenuEntity menu) {
		MenuEntity m = new MenuEntity();
		List<MenuEntity> dataList = menuMapper.getMenuById(menu);
		if (null != dataList && dataList.size() > 0) {
			m = (MenuEntity) dataList.get(0);
			m.setParentMenuName("根菜单");
			m.setHasMenu(checkChild(m));
			if (!"-1".equals(m.getParent_menu_id())) {
				dataList = menuMapper.getMenuByParentId(m);
				m.setParentMenuName(dataList.get(0).getMenu_name());
			}
		}
		return m;
	}

	public String editMenu(MenuEntity menu) {
		String[] flags = menu.getFlag().split(",");
		String id = flags[0];
		MenuEntity menuEntity = new MenuEntity();
		menuEntity.setMenu_id(id);

		if (flags[1].equals("0")) {// 添加菜单默认添加节菜单的子菜单
			if ("-1".equals(id)) {// 添加根节点
				menu.setParent_menu_id("-1");
				menu.setParentMenuName("根节点");
			} else {
				MenuEntity m2 = searchMuens(menuEntity);
				menu.setParent_menu_id(m2.getMenu_id());
				menu.setParentMenuName(m2.getMenu_name());
			}

		} else if (flags[1].equals("1")) {// 修改菜单
			MenuEntity m2 = searchMuen(menuEntity);
			menu.setMenu_id(m2.getMenu_id());
			menu.setMenu_name(m2.getMenu_name());
			menu.setParent_menu_id(m2.getParent_menu_id());
			menu.setMenu_url(m2.getMenu_url());
			menu.setMenu_image_url(m2.getMenu_image_url());
			menu.setParentMenuName(m2.getParentMenuName());
		} else {
			/** 首先判断是否为叶子节点。叶子节点的删除直接将其删除标记置为1 */
			menu.setMenu_id(id);
			menu.setParent_menu_id(flags[2]);
			deleteByObject(menu);
			return "toMenuMain";
		}
		return "toMenuAdd";
	}

	/** 查找菜单节点以及取得父节点的名称,非根节点 */
	private MenuEntity searchMuens(MenuEntity menu) {
		MenuEntity m = new MenuEntity();
		List<MenuEntity> dataList = menuMapper.getMenuById(menu);
		if (null != dataList && dataList.size() > 0) {
			m = (MenuEntity) dataList.get(0);
			m.setParentMenuName(m.getMenu_name());
		}
		return m;
	}

	/**
	 * 判断其是否有子节点
	 */
	private boolean checkChild(MenuEntity menu) {
		List<MenuEntity> list = menuMapper.querySubMenuByMenuId(menu);
		if (null != list && list.size() > 0) {// 说明它有子节点，设置bean的标记
			return true;
		}
		return false;
	}

	/**
	 * 删除菜单信息
	 * 
	 * @param menu
	 */
	public void deleteByObject(MenuEntity menu) {
		menuMapper.deleteMenuById(menu);
	}

	/**
	 * 校验菜单名称是否存在
	 */
	public List<MenuEntity> checkMenu(MenuEntity menu) {
		menu.setMenu_name(StringUtil.trim(menu.getMenu_name()));
		List<MenuEntity> menuOne = menuMapper.getMenuByName(menu);
		return menuOne;
	}

	/**
	 * 保存菜单信息
	 * 
	 * @param menu
	 */
	public void saveMenu(MenuEntity menu) {
		menu.setMenu_id(CommUtil.getKey());
		menu.setMenu_name(StringUtil.trim(menu.getMenu_name()));
		menu.setMenu_url(StringUtil.trim(menu.getMenu_url()));
		menu.setMenu_image_url(StringUtil.trim(menu.getMenu_image_url()));
		menuMapper.saveMenu(menu);
	}

	/**
	 * 修改菜单信息
	 */
	public void modifyMenu(MenuEntity menu) {
		menu.setMenu_name(StringUtil.trim(menu.getMenu_name()));
		menu.setMenu_url(StringUtil.trim(menu.getMenu_url()));
		menu.setMenu_image_url(StringUtil.trim(menu.getMenu_image_url()));
		menuMapper.modifyMenu(menu);
	}
}
