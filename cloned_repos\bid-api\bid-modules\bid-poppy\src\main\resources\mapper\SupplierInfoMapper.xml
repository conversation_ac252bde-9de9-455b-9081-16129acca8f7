<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hzw.sunflower.dao.SupplierInfoMapper">

    <select id="getBuySupplier" resultType="com.hzw.sunflower.dto.SupplierInfoDTO">
        SELECT
            a.APPLY_ID applyId,
            a.company_name appplyCompany,
            a.APPLY_STATUS applyStatus
        FROM
            t_apply_info a
        WHERE
            a.DOWNLOAD_FLAG = 1
            AND a.SUB_ID = #{supplier.formerSectionId}
            AND a.IS_DELETE = 0
    </select>

    <select id="getAttentionSupplier" resultType="com.hzw.sunflower.dto.SupplierInfoDTO">
        SELECT
            a.APPLY_ID applyId,
            a.company_name appplyCompany,
            a.APPLY_STATUS applyStatus
        FROM
            t_apply_info a
        WHERE
            a.APPLY_STATUS IN ( 2, 3, 4, 5 )
            AND a.SUB_ID = #{supplier.formerSectionId}
            AND a.IS_DELETE = 0
    </select>

    <select id="getAllSupplier" resultType="com.hzw.sunflower.dto.SupplierInfoDTO">
        SELECT
            a.APPLY_ID applyId,
            a.company_name appplyCompany,
            a.APPLY_STATUS applyStatus,
            p.PAY_STATUS payStatus
        FROM
            t_apply_info a
            LEFT JOIN t_pay_all p ON ( p.id = a.PAY_ID AND p.IS_DELETE = 0 ) and a.download_flag = 1
        WHERE
            a.SUB_ID = #{supplier.formerSectionId}
            and a.apply_status != 6
            and a.apply_status != 7
            AND a.IS_DELETE = 0
    </select>

    <select id="getAssignSupplierInfo" resultType="com.hzw.sunflower.dto.SupplierInfoDTO">
        SELECT
            a.APPLY_ID applyId,
            a.company_name appplyCompany,
            a.APPLY_STATUS applyStatus,
            a.FORMER_APPLY_ID
        FROM
            t_apply_info a
        WHERE
            a.SUB_ID = #{sectionId}
            AND a.IS_DELETE = 0
    </select>

</mapper>
