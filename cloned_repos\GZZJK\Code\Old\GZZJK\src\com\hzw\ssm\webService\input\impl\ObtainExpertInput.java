package com.hzw.ssm.webService.input.impl;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;

import com.hzw.ssm.webService.input.BodyInput;

/**
 * 获取专家名单 参数
 * 
 * <AUTHOR>
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "ObtainExpert")
@XmlType(name = "ObtainExpert", propOrder = { "projectName", "projectNum", "extSeq", })
public class ObtainExpertInput extends BodyInput {
	// 抽取序号
	@XmlElement
	private String extSeq;
	// 项目名称 S
	@XmlElement
	private String projectName;
	// 项目编号 S
	@XmlElement
	private String projectNum;


	public String getExtSeq() {
		return extSeq;
	}

	public void setExtSeq(String extSeq) {
		this.extSeq = extSeq;
	}

	public String getProjectName() {
		return projectName;
	}

	public void setProjectName(String projectName) {
		this.projectName = projectName;
	}

	public String getProjectNum() {
		return projectNum;
	}

	public void setProjectNum(String projectNum) {
		this.projectNum = projectNum;
	}

}
