package com.hzw.sunflower.controller.project.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
@ApiModel(description = "重新上传委托合同")
public class EntrustContractREQ {

    @NotNull(message = "项目id不能为空")
    @ApiModelProperty(value = "项目id", position = 1)
    private Long projectId;

    @ApiModelProperty(value = "委托合同文件id，多个以逗号隔开", position = 2)
    private String uploadFileId;

}
