package com.hzw.sunflower.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hzw.sunflower.controller.response.ProjectCountVo;
import com.hzw.sunflower.entity.ApplyInfo;
import com.hzw.sunflower.entity.JwtUser;
import com.hzw.sunflower.entity.TendererReport;
import com.hzw.sunflower.entity.condition.TendererReportCondition;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface TendererReportMapper extends BaseMapper<TendererReport> {
    /**
     * 项目数量
     * @param condition
     * @param jwtUser
     * @param type
     * @return
     */
    ProjectCountVo findProjectCount(@Param("condition")TendererReportCondition condition,@Param("jwtUser") JwtUser jwtUser,@Param("type") Integer type);

    /**
     * 开标信息表
     * @param page
     * @param condition
     * @param jwtUser
     * @return
     */
    IPage<TendererReport> findOpenBidInfo(IPage<TendererReport> page, @Param("condition") TendererReportCondition condition, @Param("jwtUser")JwtUser jwtUser);

    /**
     * 查询会议室地点
     * @param type
     * @param projectId
     * @param packageNumber
     * @return
     */
    List<String> findConferenceAddress(@Param("type")Integer type, @Param("projectId")Long projectId, @Param("packageNumber")String packageNumber);

    /**
     * 导出开标信息表
     * @param condition
     * @param jwtUser
     * @return
     */
    List<TendererReport> findOpenBidInfoList(@Param("condition")TendererReportCondition condition,@Param("jwtUser") JwtUser jwtUser);

    /**
     * 报名情况信息
     * @param page
     * @param condition
     * @param jwtUser
     * @return
     */
    IPage<TendererReport> applyInfoList(IPage<TendererReport> page, @Param("condition")TendererReportCondition condition, @Param("jwtUser")JwtUser jwtUser);

    /**
     * 已购标供应商信息
     * @param projectId
     * @param sectionId
     * @return
     */
    List<ApplyInfo> findApplyInfos(@Param("projectId")Long projectId, @Param("sectionId")Long sectionId);

    /**
     * 导出报名情况信息
     * @param condition
     * @param jwtUser
     * @return
     */
    List<TendererReport> findApplyInfoList(TendererReportCondition condition, JwtUser jwtUser);

    /**
     * 查询评标时间
     * @param type
     * @param projectId
     * @param packageNumber
     * @return
     */
    List<String> findEvaluationTime(Integer type, Long projectId, String packageNumber);
}
