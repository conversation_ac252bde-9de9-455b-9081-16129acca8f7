package com.hzw.sunflower.controller.request;

import com.hzw.sunflower.entity.BidDocDevice;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @datetime 2023/03/28 17:06
 * @description: 设备需求入参
 * @version: 1.0
 */
@ApiModel(description = "设备需求入参")
@Data
public class BidDocDeviceReq extends BidDocDevice {

    @ApiModelProperty(value = "设备需求文件id")
    private Long ossId;

}
