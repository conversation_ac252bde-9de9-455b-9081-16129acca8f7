package com.hzw.sunflower.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hzw.sunflower.controller.request.ColumnRequiredReq;
import com.hzw.sunflower.controller.response.ColumnRequiredVO;
import com.hzw.sunflower.entity.ColumnRequired;

import java.util.List;

public interface ColumnRequiredService extends IService<ColumnRequired> {
    /**
     * 回显
     * @param req
     * @return
     */
    List<ColumnRequiredVO> queryColumnRequired(ColumnRequiredReq req);

    /**
     * 保存
     * @param columnRequiredVOList
     * @return
     */
    Boolean insertColumnRequired(List<ColumnRequiredVO> columnRequiredVOList);

    /**
     * 查询功能模块
     * @param req
     * @return
     */
    List<ColumnRequired> queryModuleName(ColumnRequiredReq req);

    /**
     * 根据模块展示字段名
     * @param req
     * @return
     */
    List<ColumnRequired> queryColumnNameByModuleName(ColumnRequiredReq req);


    /**
     * 根据模块，字段名是否展示
     * @param req
     * @return
     */
    ColumnRequired queryByColumnNameModuleName(ColumnRequiredReq req);
}
