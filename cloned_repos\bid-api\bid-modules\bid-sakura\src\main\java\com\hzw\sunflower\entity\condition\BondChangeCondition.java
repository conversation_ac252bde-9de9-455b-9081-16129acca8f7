package com.hzw.sunflower.entity.condition;

import com.hzw.sunflower.common.BaseCondition;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * <AUTHOR>
 * @date ：Created in 2022/7/27 11:18
 * @description：
 * @modified By：`
 * @version: 1.0
 */
@ApiModel(description = "调账列表页查询条件")
@Data
public class BondChangeCondition extends BaseCondition {

    @ApiModelProperty(value = "关键字")
    private String keyWords;

    @ApiModelProperty(value = "供应商名称")
    private String companyName;

    @ApiModelProperty(value = "开始时间, 2023-01-01")
    private String startTime;

    @ApiModelProperty(value = "结束时间, 2023-12-31")
    private String endTime;

}
