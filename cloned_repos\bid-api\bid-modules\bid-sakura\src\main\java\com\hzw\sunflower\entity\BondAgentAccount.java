package com.hzw.sunflower.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;

import com.hzw.sunflower.common.BaseBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 保证金代理服务费账号
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-12
 */
@Getter
@Setter
@TableName("t_bond_agent_account")
@ApiModel(value = "BondAgentAccount对象", description = "保证金代理服务费账号")
public class BondAgentAccount extends BaseBean implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty("收款账号")
    @TableField("agent_acount")
    private String agentAcount;

    @ApiModelProperty("收款公司名称")
    @TableField("agent_name")
    private String agentName;

    @ApiModelProperty("行号")
    @TableField("agent_bank_code")
    private String agentBankCode;

    @ApiModelProperty("开户行名称")
    @TableField("agent_bank_name")
    private String agentBankName;

    @ApiModelProperty("开户行地址")
    @TableField("agent_bank_addr")
    private String agentBankAddr;

    @ApiModelProperty("银行类型 1.中国银行 2.南京银行 3.民生银行")
    @TableField("bank_type")
    private Integer bankType;

}
