package com.hzw.sunflower.dto;

import com.alibaba.excel.annotation.ExcelProperty;

import java.io.Serializable;

/**
 * <p>
 * 流水导入对象
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-27
 */
public class BondWaterExcelDto implements Serializable {

    private static final long serialVersionUID = 1L;

    @ExcelProperty(value = "流水号")
    private String waterNumber;

    @ExcelProperty(value = "日期")
    private String date;

    @ExcelProperty(value = "时间")
    private String time;

    @ExcelProperty(value = "对方账户名称")
    private String companyName;

    @ExcelProperty(value = "对方账号")
    private String companyAccount;

    @ExcelProperty(value = "对方账号开户网点名称")
    private String companyBankDeposit;

    @ExcelProperty(value = "供应商付款银行行号")
    private String companyBankCode;

    @ExcelProperty(value = "贷方发生额")
    private String amount;

    @ExcelProperty(value = "交易账号")
    private String receiveAcount;

    @ExcelProperty(value = "交易账号开户网点名称")
    private String receiveBank;

    @ExcelProperty(value = "摘要")
    private String postScript;

    @ExcelProperty(value = "remark")
    private String remark;

    public String getWaterNumber() {
        return waterNumber;
    }

    public void setWaterNumber(String waterNumber) {
        this.waterNumber = waterNumber;
    }

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    public String getTime() {
        return time;
    }

    public void setTime(String time) {
        this.time = time;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getCompanyAccount() {
        return companyAccount;
    }

    public void setCompanyAccount(String companyAccount) {
        this.companyAccount = companyAccount;
    }

    public String getCompanyBankDeposit() {
        return companyBankDeposit;
    }

    public void setCompanyBankDeposit(String companyBankDeposit) {
        this.companyBankDeposit = companyBankDeposit;
    }

    public String getCompanyBankCode() {
        return companyBankCode;
    }

    public void setCompanyBankCode(String companyBankCode) {
        this.companyBankCode = companyBankCode;
    }

    public String getAmount() {
        return amount;
    }

    public void setAmount(String amount) {
        this.amount = amount;
    }

    public String getReceiveAcount() {
        return receiveAcount;
    }

    public void setReceiveAcount(String receiveAcount) {
        this.receiveAcount = receiveAcount;
    }

    public String getReceiveBank() {
        return receiveBank;
    }

    public void setReceiveBank(String receiveBank) {
        this.receiveBank = receiveBank;
    }

    public String getPostScript() {
        return postScript;
    }

    public void setPostScript(String postScript) {
        this.postScript = postScript;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
