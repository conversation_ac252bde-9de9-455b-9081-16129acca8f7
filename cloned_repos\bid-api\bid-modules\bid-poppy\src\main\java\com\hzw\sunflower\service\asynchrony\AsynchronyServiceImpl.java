package com.hzw.sunflower.service.asynchrony;

import com.hzw.sunflower.controller.project.request.ReturnListConditionReq;
import com.hzw.sunflower.entity.JwtUser;
import com.hzw.sunflower.service.WaitRefundCountService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class AsynchronyServiceImpl implements AsynchronyService{

    @Autowired
    private WaitRefundCountService waitRefundCountService;

    /**
     * 异步刷新缓存的数据-退回待处理事项的数量
     * @param condition
     * @param jwtUser
     * @param datascopesql
     */
    @Override
    public void refreshRedis(ReturnListConditionReq condition, JwtUser jwtUser, String datascopesql) {
        log.info("==========异步刷新缓存待处理事项数量开始！==========");
        waitRefundCountService.getReturnListCount(condition,jwtUser,datascopesql);
        log.info("==========异步刷新缓存待处理事项数量结束!==========");
    }

}
