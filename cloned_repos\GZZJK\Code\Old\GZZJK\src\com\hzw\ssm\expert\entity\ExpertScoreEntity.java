package com.hzw.ssm.expert.entity;

import java.util.Date;

import com.hzw.ssm.fw.base.BaseEntity;

public class ExpertScoreEntity extends BaseEntity {
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	private String id;//主键
	
	private String user_id;//用户编号
	
	private String user_name;//用户编号
	
	private Date pause_startTime;//暂停开始时间
	
	private Date pause_endTime;//暂停结束时间
	
	private Integer pause_number;//累计暂停月数
	
	private Double buckle_score;//扣分值
	
	private Date modify_time;//修改时间
	
	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getUser_id() {
		return user_id;
	}

	public void setUser_id(String user_id) {
		this.user_id = user_id;
	}

	public String getUser_name() {
		return user_name;
	}

	public void setUser_name(String user_name) {
		this.user_name = user_name;
	}

	public Date getPause_startTime() {
		return pause_startTime;
	}

	public void setPause_startTime(Date pause_startTime) {
		this.pause_startTime = pause_startTime;
	}

	public Date getPause_endTime() {
		return pause_endTime;
	}

	public void setPause_endTime(Date pause_endTime) {
		this.pause_endTime = pause_endTime;
	}

	public Integer getPause_number() {
		return pause_number;
	}

	public void setPause_number(Integer pause_number) {
		this.pause_number = pause_number;
	}

	public Double getBuckle_score() {
		return buckle_score;
	}

	public void setBuckle_score(Double buckle_score) {
		this.buckle_score = buckle_score;
	}

	public Date getModify_time() {
		return modify_time;
	}

	public void setModify_time(Date modify_time) {
		this.modify_time = modify_time;
	}

	
}
