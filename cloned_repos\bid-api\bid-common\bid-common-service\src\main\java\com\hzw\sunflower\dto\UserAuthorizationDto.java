package com.hzw.sunflower.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 用户表 请求实体
 *
 * <AUTHOR>
 * @version 1.0.0 2021-04-12
 */
@Data
public class UserAuthorizationDto {

    @ApiModelProperty(value = "用户id")
    private Long userId;

    @ApiModelProperty(value = "用户身份")
    private Integer identity;

    @ApiModelProperty(value = "项目编号")
    private String purchaseNumber;

    @ApiModelProperty(value = "授权方式：1 全项目授权  2 单项目授权")
    private Integer authorizationType;

    @ApiModelProperty("授权函")
    private Long legalPersonAuthorization;
}