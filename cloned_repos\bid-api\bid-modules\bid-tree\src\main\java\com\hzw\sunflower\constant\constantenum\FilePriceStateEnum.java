package com.hzw.sunflower.constant.constantenum;

/**
 * <AUTHOR>
 * @date ：Created in 2021/12/8 10:49
 * @description：文件售价说明
 * @version: 1.0
 */
public enum FilePriceStateEnum {

    NOTHING(0, "无"),
    TRAFFIC(1, "交通"),
    HYDRAULIC(2, "水利"),
    OTHER(3, "其他");

    private Integer value;

    private String name;

    FilePriceStateEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    public Integer getValue() {
        return value;
    }

    public String getName() {
        return name;
    }
}
