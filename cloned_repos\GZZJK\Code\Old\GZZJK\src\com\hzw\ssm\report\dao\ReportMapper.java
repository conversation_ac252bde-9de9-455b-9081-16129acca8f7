package com.hzw.ssm.report.dao;

import java.util.List;
import java.util.Map;

import com.hzw.ssm.report.entity.QueryBean;
import org.apache.ibatis.annotations.MapKey;

import com.hzw.ssm.report.entity.ReportEntity;

public interface ReportMapper {
	/**
	 * 查询明细
	 * @param rep
	 * @return
	 */
	public List<ReportEntity> queryReportDetail(ReportEntity rep);
	/**
	 * 查询汇总
	 * @param rep
	 * @return
	 */
	public List<ReportEntity> queryReportSummary(ReportEntity rep);
	public List<String> selectDetailExpertId(ReportEntity rep);
	/**
	 * 查询明细导出
	 * @param rep
	 * @return
	 */
	public List<ReportEntity> selectReportDetail(ReportEntity rep);
	public List<ReportEntity>  queryScoreInfo(ReportEntity rep);
	public List<QueryBean>  queryresultId(ReportEntity rep);
	public List<ReportEntity> selectReportDetails(ReportEntity rep);
	public List<QueryBean> selectDetailResultId(ReportEntity rep);
	/**
	 * 
	 * @param rep
	 * @return
	 */
	@MapKey("score")
	public Map<String ,List<ReportEntity>> queryReportDetailMap(ReportEntity rep);
	/**
	 * 查询汇总导出
	 * @param rep
	 * @return
	 */
	public List<ReportEntity> queryPageReportSummary(ReportEntity rep);
	/**
	 * 查询注销分页(已注销)
	 * @param rep
	 * @return
	 */
	public List<ReportEntity> queryPageReportCancellation(ReportEntity rep);
	/**
	 * 查询注销分页(待注销)
	 * @param rep
	 * @return
	 */
	public List<ReportEntity> queryPageReportCancellationT(ReportEntity rep);
	/**
	 * 注销导出(已注销)
	 * @param rep
	 * @return
	 */
	public List<ReportEntity> queryCancellationDate(ReportEntity rep);
	/**
	 * 注销导出(待注销)
	 * @param rep
	 * @return
	 */
	public List<ReportEntity> queryCancellationTDate(ReportEntity rep);
	public void updateScoreStatus(ReportEntity rep);
	public void updateAppraiseStatus(ReportEntity rep);
	public ReportEntity querySumScore(ReportEntity rep);
	public String queryExpertId(ReportEntity rep);
	public void updateNewScore(ReportEntity reportEntity);
	public void updateAppraiseInfo(ReportEntity reportEntity);
	public void updateRejectionReason(ReportEntity reportEntity);
	public String queryThisSumScore(ReportEntity reportEntity);
	public String queryExpertSumScore(ReportEntity reportEntity);
	public void updateExpertBidStatus(ReportEntity reportEntity);
	public void updateExpertStatus(String expertId);
}
