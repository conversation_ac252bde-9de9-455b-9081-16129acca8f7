package com.hzw.sunflower.constant.constantenum;

/**
 * <AUTHOR>
 * @date ：Created in 2022/07/27 9:23
 * @description：回单类型
 * @version: 1.0
 */
public enum TradeTypeEnum {

    TRADE_RECEIVE(1, "收款流水回单"),
    TRADE_PAY(2, "支付回单");

    private Integer type;
    private String desc;

    TradeTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
