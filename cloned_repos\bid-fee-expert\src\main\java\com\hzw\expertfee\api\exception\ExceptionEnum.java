package com.hzw.expertfee.api.exception;

/**
 * @ClassName:ExceptionEnum
 * @Auther: lijinxin
 * @Description:
 * @Date: 2023/1/10 14:17
 * @Version: v1.0
 */
public enum ExceptionEnum {

    ERROR_OPERATION(500, "异常操作"),
    PAY_FAIL(500,"项目经理银行卡账号信息不存在，请添加！"),
    TOKEN_CANNOT_EMPTY(11003, "您的登录信息已失效，请重新登录"),
    TOKEN_FORMAT_ERROR(11004, "您的登录信息有误，请重新登录"),
    TOKEN_IS_NOT_CONSTRUCTED_CORRECTLY(11005, "您的登录信息有误，请重新登录"),
    SIGNATURE_FAILED(11006, "您的登录信息认证失败，请重新登录"),
    ILLEGAL_PARAMETER_EXCEPTION(11007, "您输入的信息格式有误，请检查数据格式"),
    TOKEN_HAS_EXPIRED(11008, "您的登录信息已失效，请重新登录"),
    ;
    //错误码
    private Integer code;
    //提示信息
    private String message;

    //构造函数
    ExceptionEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    //获取状态码
    public Integer getCode() {
        return code;
    }

    //获取提示信息
    public String getMessage() {
        return message;
    }
}
