package com.hzw.sunflower.service.impl;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzw.sunflower.client.WorkFlowApiClient;
import com.hzw.sunflower.common.Paging;
import com.hzw.sunflower.constant.constantenum.*;
import com.hzw.sunflower.controller.project.request.DataUpdateExamineReq;
import com.hzw.sunflower.controller.project.request.DataUpdateReq;
import com.hzw.sunflower.controller.project.response.DataUpdateRecordDetailsVo;
import com.hzw.sunflower.controller.project.response.DataUpdateRecordVo;
import com.hzw.sunflower.controller.project.response.ProjectBidSectionVO;
import com.hzw.sunflower.controller.request.AppingTaskREQ;
import com.hzw.sunflower.controller.response.AppingTaskVO;
import com.hzw.sunflower.controller.user.request.PushProjectDataReq;
import com.hzw.sunflower.dao.DataUpdateRecordMapper;
import com.hzw.sunflower.dto.AppOaMsgDto;
import com.hzw.sunflower.dto.ProcessRecordDTO;
import com.hzw.sunflower.dto.ProjectInfoDTO;
import com.hzw.sunflower.dto.SectionInfoDTO;
import com.hzw.sunflower.entity.*;
import com.hzw.sunflower.entity.condition.DataUpdateRecordCondition;
import com.hzw.sunflower.exception.ExceptionEnum;
import com.hzw.sunflower.exception.SunFlowerException;
import com.hzw.sunflower.freesia.client.constant.FlowClientConstant;
import com.hzw.sunflower.freesia.client.vo.flowable.ReturnVo;
import com.hzw.sunflower.freesia.client.vo.flowable.task.TaskUserVo;
import com.hzw.sunflower.security.utils.SecurityUtils;
import com.hzw.sunflower.service.*;
import com.hzw.sunflower.util.BeanListUtil;
import com.hzw.sunflower.util.DateUtils;
import com.hzw.sunflower.util.LoginUtil;
import com.hzw.sunflower.util.WorkFlowExceptionUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * <AUTHOR>
 * @date ：Created in 2022/9/5 10:53
 * @description：
 * @modified By：`
 * @version: 1.0
 */
@Service
@Slf4j
public class DataUpdateRecordServiceImpl extends ServiceImpl<DataUpdateRecordMapper, DataUpdateRecord> implements DataUpdateRecordService {

    @Autowired
    CommonProjectRelevantService commonProjectRelevantService;

    @Autowired
    CommonSectionService commonSectionService;

    @Autowired
    TycRecordService tycRecordService;

    @Autowired
    OssFileService ossFileService;

    @Autowired
    private WorkFlowApiClient flowApiClient;

    @Autowired
    private ProcessRecordService processRecordService;

    @Autowired
    private WorkflowCacheService workflowCacheService;

    @Autowired
    private UserOpenService userOpenService;

    @Autowired
    private CompanyPushDataService companyPushDataService;

    @Autowired
    private DepartmentService departmentService;

    @Autowired
    private CommonMqService commonMqService;

    /**
     * 分页查询
     * @param condition
     * @return
     */
    @Override
    public IPage<DataUpdateRecordVo> listRecordPage(DataUpdateRecordCondition condition) {
        condition.setUserId(LoginUtil.getJwtUser().getUserId());
        condition.setDepartId(LoginUtil.getJwtUser().getUser().getDepartId());
        IPage<DataUpdateRecordVo> page = condition.buildPage();
        page = this.baseMapper.listRecordPage(page, condition);
/*        List<DataUpdateRecordVo> list = page.getRecords();
        // 文件集合赋值
        list.forEach(vo -> {
            String[] arr = vo.getFileIds().split(",");
            List<OssFile> fileList = ossFileService.list(new LambdaQueryWrapper<OssFile>().in(OssFile::getId, arr));
            vo.setFileList(fileList);
        });
        page.setRecords(list);*/

        page.getRecords().forEach(p->{
            if(StringUtils.isNotBlank(p.getRequestJson())) {
                JSONObject jsonObject = JSONUtil.parseObj(p.getRequestJson());
                Long projectId = jsonObject.getLong("projectId");
                Long docFile = jsonObject.getLong("docFile");
                Long annexFile = jsonObject.getLong("annexFile");
                p.setProjectId(projectId);
                p.setDocFile(docFile);
                p.setAnnexFile(annexFile);
            }

        });



        return page;
    }

    /**
     * 新增数据修改
     * @param req
     * @return
     */
    @Override
    public Boolean addData(DataUpdateReq req) {


        if(!DataUpdateEnum.TYC.getCode().equals(req.getType())){
            if(null == req.getProjectId()){
                throw new SunFlowerException(ExceptionEnum.PROJECT_INFOMATION_WRONG,ExceptionEnum.PROJECT_INFOMATION_WRONG.getMessage());
            }
            //todo
            //yg:不允许同一个项目标包存在两条待处理数据，允许同一个项目编号下同时存在多条申请数据
            List<DataUpdateRecord> dataUpdateRecords = this.baseMapper.getInfoByProjectId(req.getType(),req.getProjectId(),req.getSectionId(),req.getSectionIds());
            if(!dataUpdateRecords.isEmpty()){
                throw new SunFlowerException(ExceptionEnum.ID_REPEAT,ExceptionEnum.ID_REPEAT.getMessage());
            }
        }


        Boolean result = false;
        JwtUser jwtUser = LoginUtil.getJwtUser();
        DataUpdateRecord record = new DataUpdateRecord();
        record.setId(req.getId());
        record.setFileIds(req.getFileIds());
        record.setType(req.getType());
        record.setRemark(req.getRemark());
        record.setStatus(DataUpdateStatusEnum.WAIT_CONFIRM.getCode());
        JSONObject requestJson = JSONUtil.parseObj(req);
        record.setRequestJson(requestJson.toString());
        record.setApplyUserId(jwtUser.getUserId());
        record.setApplyDepartId(jwtUser.getUser().getDepartId());
        ProjectInfoDTO projectInfoDTO = new ProjectInfoDTO();
        if(req.getProjectId() != null){
            projectInfoDTO = commonProjectRelevantService.getProjectById(req.getProjectId());
        }
        // 获取包号
        String packageNumber = "/";
        // 获取原采购文件发售截至时间
        String saleEndTime = EndTimeTypeEnum.PREVIOUS_DAY.getDesc();
        // 获取原响应文件递交截止时间
        String submitEndTime = EndTimeTypeEnum.FURTHER_NOTICE.getDesc();
        //中标人数量
        String bidWinNumber = "0";
        //招标文件
        Long docFileId = null;
        String docFileName = "-";
        if(req.getSectionId() != null){
            SectionInfoDTO sectionInfoDTO = commonProjectRelevantService.getSectionById(req.getSectionId());
            if (sectionInfoDTO.getSaleEndTimeType() == null || sectionInfoDTO.getSubmitEndTimeType() == null) {
                throw new SunFlowerException(ExceptionEnum.BID_UPDATE_ERROR, ExceptionEnum.BID_UPDATE_ERROR.getMessage());
            }
            if (projectInfoDTO.getPackageSegmentStatus() == 1 && sectionInfoDTO.getPackageNumber() != null) {
                packageNumber = sectionInfoDTO.getPackageNumber();
            }
            if (sectionInfoDTO.getSaleEndTimeType().equals(EndTimeTypeEnum.CLEAR_TIME.getType())) {
                saleEndTime = DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, sectionInfoDTO.getSaleEndTime());
            }
            if (sectionInfoDTO.getSubmitEndTimeType().equals(EndTimeTypeEnum.CLEAR_TIME.getType())) {
                submitEndTime = DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, sectionInfoDTO.getSubmitEndTime());
            }
            if(null != sectionInfoDTO.getStatus() && sectionInfoDTO.getStatus() > PackageStatusEnum.SUBMITTING_WINNER_CANDIDATE.getValue()){
                //中标人数量
                bidWinNumber = this.baseMapper.queryBidWinNumber(sectionInfoDTO.getId(),sectionInfoDTO.getBidRound());
            }
        }
        if(!CollectionUtils.isEmpty(req.getSectionIds())){
            String sectionId = req.getSectionIds().get(0);//至少会有一个包
            ProjectBidSectionVO vo= this.baseMapper.findDoc(sectionId);
            if(null != vo && null != vo.getDocFile()){
                docFileId = vo.getDocFile();
            }
            String packageNumbers = vo.getPackageNumbers();
            if(null != vo && null != packageNumbers && !"null".equals(packageNumbers)){
                packageNumber = vo.getPackageNumbers();
            }
            docFileName = vo.getDocFileName();
            OssFile ossFileById = ossFileService.getOssFileById(req.getDocFile());
            req.setDocFileName(ossFileById.getOssFileName());
        }
        String content = "";
        String beforeUpdateContent = "";
        if (DataUpdateEnum.PROJECT_NAME.getCode().equals(req.getType())) {//委托项目名称
            content = "项目：" + projectInfoDTO.getPurchaseNumber() + "，委托项目名称：" + req.getProjectName() + "（修改前：" + projectInfoDTO.getProjectName() + "）";
            beforeUpdateContent = projectInfoDTO.getProjectName();
        } else if (DataUpdateEnum.PURCHASE_NAME.getCode().equals(req.getType())) {
            content = "项目：" + projectInfoDTO.getPurchaseNumber() + "，采购项目名称：" + req.getPurchaseName() + "（修改前：" + projectInfoDTO.getPurchaseName() + "）";
            beforeUpdateContent = projectInfoDTO.getPurchaseName();
        } else if (DataUpdateEnum.SALE_END_TIME.getCode().equals(req.getType())) {
            content= "项目："+ projectInfoDTO.getPurchaseNumber() +"，标段包："+ packageNumber +"，采购文件发售截止时间："+ req.getSaleEndTime() +"（修改前："+ saleEndTime +"）";
            beforeUpdateContent = saleEndTime;
        } else if (DataUpdateEnum.SUBMIT_END_TIME.getCode().equals(req.getType())) {
            content = "项目："+ projectInfoDTO.getPurchaseNumber() +"，标段包："+ packageNumber +"，响应文件递交截止时间："+ req.getSubmitEndTime() +"（修改前："+ submitEndTime +"）";
            beforeUpdateContent = submitEndTime;
        } else if (DataUpdateEnum.TYC.getCode().equals(req.getType())) {
            content = "修改天眼查企业信息："+ req.getCompanyName() + "，" + req.getOrganizationNum() + "，" + req.getLegalRepresentative();
        } else if (DataUpdateEnum.BID_WIN_NUMBER.getCode().equals(req.getType())) {
            content= "项目："+ projectInfoDTO.getPurchaseNumber() +"，标段包："+ packageNumber +"，中标人数量："+ req.getWinPeopleNumber() +"（修改前："+ bidWinNumber +"）";
            beforeUpdateContent = bidWinNumber;
        }else if (DataUpdateEnum.BID_DOC_UPDATE.getCode().equals(req.getType())) {
            content= "项目："+ projectInfoDTO.getPurchaseNumber() +"，标段包："+ packageNumber +"，招标文件："+ req.getDocFileName() + "，" + "（修改前："+ docFileName + "）";
            beforeUpdateContent = "招标文件：" + docFileId;
        } else {
            throw new SunFlowerException(ExceptionEnum.ILLEGAL_PARAMETER_EXCEPTION, ExceptionEnum.ILLEGAL_PARAMETER_EXCEPTION.getMessage());
        }
        record.setBeforeUpdateContent(beforeUpdateContent);
        record.setContent(content);
        result = this.saveOrUpdate(record);
        //开启流程引擎
        String str = flowApiClient.startProcessInstanceByKey(jwtUser.getUserOtherId(), FlowClientConstant.DATA_UPDATE_ONE, "数据修改审批", "t_data_update_record:" + record.getId(), null);
        //判断推送到流程是否成功
        WorkFlowExceptionUtil.checkFlowResult(str);
        ReturnVo returnVo = com.alibaba.fastjson.JSONObject.parseObject(str, ReturnVo.class);
        String data = (String) returnVo.getData();
        record.setProcessCode(data);
        //如果流程结束了，则状态为已确认
        if ("end".equals(returnVo.getMsg())) {
            record.setStatus(DataUpdateStatusEnum.COMPLETED.getCode());
            //修改数据
            if(StringUtils.isNotBlank(record.getRequestJson())){
                JSONObject jsonObject = JSONUtil.parseObj(record.getRequestJson());
                DataUpdateReq dataUpdateReq = JSONUtil.toBean(jsonObject,DataUpdateReq.class);
                SectionInfoDTO section = new SectionInfoDTO();
                if(dataUpdateReq.getSectionId() != null && !dataUpdateReq.getSectionId().equals(0)) {
                    section = commonProjectRelevantService.getSectionById(dataUpdateReq.getSectionId());
                }
                if (DataUpdateEnum.PROJECT_NAME.getCode().equals(record.getType())) {//委托项目名称
                    updateProjectName(dataUpdateReq);
                } else if (DataUpdateEnum.PURCHASE_NAME.getCode().equals(record.getType())) {//采购项目名称
                    updatePurchaseName(dataUpdateReq);
                } else if (DataUpdateEnum.SALE_END_TIME.getCode().equals(record.getType())) {//采购文件发售截止时间
                    updateSaleEndTime(dataUpdateReq,section);
                } else if (DataUpdateEnum.SUBMIT_END_TIME.getCode().equals(record.getType())) {//响应文件递交截止时间
                    updateSubmitEndTime(dataUpdateReq,section);
                } else if (DataUpdateEnum.TYC.getCode().equals(record.getType())) {//修改天眼查企业信息
                    updateTycRecord(dataUpdateReq);
                } else if (DataUpdateEnum.BID_WIN_NUMBER.getCode().equals(record.getType())) {//修改中标人数量
                    updateBidWinNumber(dataUpdateReq,section);
                }else if (DataUpdateEnum.BID_DOC_UPDATE.getCode().equals(record.getType())) {//修改招标文件
                    updateBidDoc(dataUpdateReq);
                }
            }
        }
        result = this.updateById(record);

        //插入流程记录
        ProcessRecordDTO recordDTO = new ProcessRecordDTO();
        recordDTO.setBusinessCode(FlowClientConstant.DATA_UPDATE_ONE);
        recordDTO.setBusinessId(record.getId());
        recordDTO.setOperation("新增");
        if(req.getId() != null && req.getId() != 0 ){
            recordDTO.setOperation("修改");
        }
        recordDTO.setOperatorId(jwtUser.getUserId());
        recordDTO.setOperatorName(jwtUser.getUserName());
        //recordDTO.setRemark("");
        processRecordService.addProcessRecord(recordDTO,jwtUser.getUserOtherId(),str);
        return result;
    }

    /**
     * 审核列表
     * @param condition
     * @return
     */
    @Override
    public IPage<DataUpdateRecordVo> queryList(DataUpdateRecordCondition condition) {
        IPage<DataUpdateRecordVo> page = condition.buildPage();
        JwtUser user = LoginUtil.getJwtUser();
        //封装流程引擎查询参数
        AppingTaskREQ appingTaskREQ = new AppingTaskREQ();
        appingTaskREQ.setPage(1);
        appingTaskREQ.setPageSize(1000);
        appingTaskREQ.setProcessDefinitionKey(FlowClientConstant.DATA_UPDATE_ONE);
        appingTaskREQ.setIsFilter(false);
        Map<String, AppingTaskVO> map = new HashMap<>();
        //查询待审批的流程数据
        Paging<AppingTaskVO> paddingList = workflowCacheService.getPaddingList(appingTaskREQ, user.getUserOtherId());
        List<AppingTaskVO> tasks = paddingList.getRecords();
        //封装流程code集合
        List<String> taskCode = new ArrayList<>();
        taskCode.add("1");
        tasks.forEach(p->{
            map.put(p.getProcessInstanceId(), p);
            taskCode.add(p.getProcessInstanceId());
        });
        condition.setTaskCodes(taskCode);
        page = this.baseMapper.queryList(page,condition);
        page.getRecords().forEach(p->{
            String processCode = p.getProcessCode();
            AppingTaskVO taskList = map.get(processCode);
            p.setTaskList(taskList);

            if(StringUtils.isNotBlank(p.getRequestJson())) {
                JSONObject jsonObject = JSONUtil.parseObj(p.getRequestJson());
                Long projectId = jsonObject.getLong("projectId");
                p.setProjectId(projectId);
            }

        });
        return page;
    }

    /**
     * app数据修改待办
     * @param condition
     * @return
     */
    @Override
    public List<DataUpdateRecordVo> queryListForApp(DataUpdateRecordCondition condition) {
        List<DataUpdateRecordVo> list = this.baseMapper.queryListForApp(condition);
        list.forEach(p -> {
            if(StringUtils.isNotBlank(p.getRequestJson())) {
                JSONObject jsonObject = JSONUtil.parseObj(p.getRequestJson());
                Long projectId = jsonObject.getLong("projectId");
                p.setProjectId(projectId);
            }
        });
        return list;
    }

    /**
     *
     * @param condition
     * @return
     */
    @Override
    public IPage<DataUpdateRecordVo> queryListApp(DataUpdateRecordCondition condition) {
        IPage<DataUpdateRecordVo> page = condition.buildPage();
        JwtUser user = LoginUtil.getJwtUser();
        //封装流程引擎查询参数
        AppingTaskREQ appingTaskREQ = new AppingTaskREQ();
        appingTaskREQ.setPage(1);
        appingTaskREQ.setPageSize(1000);
        appingTaskREQ.setProcessDefinitionKey(FlowClientConstant.DATA_UPDATE_ONE);
        appingTaskREQ.setIsFilter(false);
        Map<String, AppingTaskVO> map = new HashMap<>();
        //查询待审批的流程数据
        Paging<AppingTaskVO> paddingList = workflowCacheService.getPaddingList(appingTaskREQ, user.getUserOtherId());
        List<AppingTaskVO> tasks = paddingList.getRecords();
        //封装流程code集合
        List<String> taskCode = new ArrayList<>();
        taskCode.add("1");
        tasks.forEach(p->{
            map.put(p.getProcessInstanceId(), p);
            taskCode.add(p.getProcessInstanceId());
        });
        condition.setTaskCodes(taskCode);
        page = this.baseMapper.queryListApp(page,condition);
        page.getRecords().forEach(p->{
            String processCode = p.getProcessCode();
            AppingTaskVO taskList = map.get(processCode);
            p.setTaskList(taskList);

            if(StringUtils.isNotBlank(p.getRequestJson())) {
                JSONObject jsonObject = JSONUtil.parseObj(p.getRequestJson());
                Long projectId = jsonObject.getLong("projectId");
                p.setProjectId(projectId);
            }

        });
        return page;
    }

    @Override
    public DataUpdateRecordDetailsVo getDetailsById(Long id) {
        DataUpdateRecord dataUpdateRecord = this.baseMapper.selectById(id);
        DataUpdateRecordDetailsVo detailsVo = BeanListUtil.convert(dataUpdateRecord,DataUpdateRecordDetailsVo.class);
        List<SectionInfoDTO> infoDTOS = new ArrayList<>();
        if(StringUtils.isNotBlank(dataUpdateRecord.getRequestJson())){
            JSONObject jsonObject = JSONUtil.parseObj(dataUpdateRecord.getRequestJson());
            Long projectId = jsonObject.getLong("projectId");
            if(projectId != null){
                detailsVo.setProject(commonProjectRelevantService.getProjectById(projectId));
            }
            Long sectionId = jsonObject.getLong("sectionId");
            if(sectionId != null){
                SectionInfoDTO sectionById = commonProjectRelevantService.getSectionById(sectionId);
                if(null != sectionById && null != sectionById.getPackageNumber() ){
                    detailsVo.setSection(sectionById);
                }
            }
            JSONArray sectionIds = jsonObject.getJSONArray("sectionIds");
            if(null != sectionIds){
                List<String> strings = new ArrayList<>();
                for (int i = 0; i < sectionIds.size(); i++) {
                    String s = (String) sectionIds.get(i);
                    strings.add(s);
                    SectionInfoDTO sectionById = commonProjectRelevantService.getSectionById(Long.parseLong(s));
                    if(null != sectionById && null != sectionById.getPackageNumber() ){
                        infoDTOS.add(sectionById);
                    }
                }
                detailsVo.setSectionIds(strings);
                detailsVo.setSections(infoDTOS);
            }
            Long docFile = jsonObject.getLong("docFile");
            if(docFile != null){
                detailsVo.setDocFile(docFile);
            }
            Long annexFile = jsonObject.getLong("annexFile");
            if(annexFile != null){
                detailsVo.setAnnexFile(annexFile);
            }
        }
        return detailsVo;
    }

    /**
     * 审核
     * @param req
     * @param jwtUser
     * @return
     */
    @Override
    public Boolean updateExamine(DataUpdateExamineReq req, JwtUser jwtUser) {
        Boolean result = false;
        String operation = "";
        Boolean isAgree = false;
        //同意
        if (req.getOperation().equals("1")) {
            operation = "同意";
            isAgree = true;
        } else {
            operation = "退回";
        }
        DataUpdateRecord record = getById(req.getId());
        ProcessRecordDTO recordDTO = new ProcessRecordDTO();
        recordDTO.setBusinessId(record.getId());
        recordDTO.setOperation(operation);
        recordDTO.setOperatorId(jwtUser.getUserId());
        recordDTO.setOperatorName(jwtUser.getUserName());
        recordDTO.setRemark(req.getMessage());
        //拒绝后 状态为已付款
        if (req.getOperation().equals("2")) {
            record.setStatus(DataUpdateStatusEnum.RETURNED.getCode());
        }
        recordDTO.setBusinessCode(FlowClientConstant.DATA_UPDATE_ONE);
        //流程引擎审批
        String str = flowApiClient.review(jwtUser.getUserOtherId(), req.getProcessInstanceId() + "", req.getTaskId(), req.getMessage(), isAgree);
        log.info("数据修改流程引擎审批-------------->"+str);
        //判断推送到流程是否成功
        ReturnVo returnVo = com.alibaba.fastjson.JSONObject.parseObject(str, ReturnVo.class);
        result = WorkFlowExceptionUtil.checkFlowResult(str);
        //插入流程记录SS
        processRecordService.addProcessRecord(recordDTO,jwtUser.getUserOtherId(),str);
        if ("end".equals(returnVo.getMsg()) && "同意".equals(operation)) {
            record.setStatus(DataUpdateStatusEnum.COMPLETED.getCode());
            //修改数据
            if(StringUtils.isNotBlank(record.getRequestJson())){
                JSONObject jsonObject = JSONUtil.parseObj(record.getRequestJson());
                DataUpdateReq dataUpdateReq = JSONUtil.toBean(jsonObject,DataUpdateReq.class);
                SectionInfoDTO section = new SectionInfoDTO();
                if(dataUpdateReq.getSectionId() != null && !dataUpdateReq.getSectionId().equals(0)) {
                    section = commonProjectRelevantService.getSectionById(dataUpdateReq.getSectionId());
                }
                if (DataUpdateEnum.PROJECT_NAME.getCode().equals(record.getType())) {//委托项目名称
                     updateProjectName(dataUpdateReq);
                } else if (DataUpdateEnum.PURCHASE_NAME.getCode().equals(record.getType())) {//采购项目名称
                     updatePurchaseName(dataUpdateReq);
                } else if (DataUpdateEnum.SALE_END_TIME.getCode().equals(record.getType())) {//采购文件发售截止时间
                     updateSaleEndTime(dataUpdateReq,section);
                } else if (DataUpdateEnum.SUBMIT_END_TIME.getCode().equals(record.getType())) {//响应文件递交截止时间
                     updateSubmitEndTime(dataUpdateReq,section);
                } else if (DataUpdateEnum.TYC.getCode().equals(record.getType())) {//修改天眼查企业信息
                     updateTycRecord(dataUpdateReq);
                } else if (DataUpdateEnum.BID_WIN_NUMBER.getCode().equals(record.getType())) {//修改中标人数量
                     updateBidWinNumber(dataUpdateReq,section);
                }else if (DataUpdateEnum.BID_DOC_UPDATE.getCode().equals(record.getType())) {//修改招标文件
                    updateBidDoc(dataUpdateReq);
                }
                if(DataUpdateEnum.PROJECT_NAME.getCode().equals(record.getType())
                    || DataUpdateEnum.PURCHASE_NAME.getCode().equals(record.getType())
                        || DataUpdateEnum.SALE_END_TIME.getCode().equals(record.getType())
                        || DataUpdateEnum.SUBMIT_END_TIME.getCode().equals(record.getType())
                        || DataUpdateEnum.BID_WIN_NUMBER.getCode().equals(record.getType())
                        || DataUpdateEnum.BID_DOC_UPDATE.getCode().equals(record.getType())
                ){
                    // 数据修改完成,推送数据
                    PushProjectDataReq dataReq = new PushProjectDataReq();
                    dataReq.setProjectId(dataUpdateReq.getProjectId());
                    dataReq.setPushNode(PushNode.SJXG.getDesc());
                    companyPushDataService.projectPushOrNot(dataReq);
                }

            }
        }
        //修改
        result = updateById(record);
        return result;
    }


    /**
     *
     * @param id
     * @param jwtUser
     * @return
     */
    @Override
    public Boolean updateWithdraw(Long id, JwtUser jwtUser) {
        //插入流程记录
        ProcessRecordDTO recordDTO = new ProcessRecordDTO();
        recordDTO.setBusinessCode(FlowClientConstant.DATA_UPDATE_ONE);
        recordDTO.setBusinessId(id);
        recordDTO.setOperation("撤回");
        recordDTO.setOperatorId(jwtUser.getUserId());
        recordDTO.setOperatorName(jwtUser.getUserName());
        //recordDTO.setRemark("");
        processRecordService.addProcessRecord(recordDTO,jwtUser.getUserOtherId(),"");

        DataUpdateRecord record = new DataUpdateRecord();
        record.setId(id);
        record.setStatus(DataUpdateStatusEnum.WITHDRAWED.getCode());
        return updateById(record);
    }

    /**
     * 报分管领导审批
     * @param req
     * @param jwtUser
     * @return
     */
    @Override
    public Boolean updateToLeaderApprove(DataUpdateExamineReq req, JwtUser jwtUser) {
        Boolean result = false;
        DataUpdateRecord record = getById(req.getId());
        //结束当前流程
        String str = flowApiClient.review(jwtUser.getUserOtherId(), req.getProcessInstanceId() + "", req.getTaskId(), "报分管领导审批", false);
        //判断推送到流程是否成功
        ReturnVo returnVo = com.alibaba.fastjson.JSONObject.parseObject(str, ReturnVo.class);
        result = WorkFlowExceptionUtil.checkFlowResult(str);


        User user = userOpenService.getById(record.getCreatedUserId());
        Department depart = departmentService.getById(record.getApplyDepartId());
        //开启新流程
        str = flowApiClient.startProcessInstanceByKey(user.getOtherUserId(), FlowClientConstant.DATA_UPDATE_TWO, "数据修改审批", "t_data_update_record:" + req.getId(), null,depart.getOtherId());
        //判断推送到流程是否成功
        WorkFlowExceptionUtil.checkFlowResult(str);
        returnVo = com.alibaba.fastjson.JSONObject.parseObject(str, ReturnVo.class);
        String data = (String) returnVo.getData();
        record.setProcessCode(data);
        //如果流程结束了，则状态为已确认
        if ("end".equals(returnVo.getMsg())) {
            record.setStatus(DataUpdateStatusEnum.COMPLETED.getCode());
        }
        result = this.updateById(record);
        //插入流程记录
        ProcessRecordDTO recordDTO = new ProcessRecordDTO();
        recordDTO.setBusinessCode(FlowClientConstant.DATA_UPDATE_ONE);
        recordDTO.setBusinessId(record.getId());
        recordDTO.setOperation("报分管领导审批");
        recordDTO.setOperatorId(jwtUser.getUserId());
        recordDTO.setOperatorName(jwtUser.getUserName());
        recordDTO.setRemark(req.getNotes());
        processRecordService.addProcessRecord(recordDTO,user.getOtherUserId(),str);
        return result;
    }

    /**
     * 修改项目名称
     * @param req
     * @return
     */
    private Boolean updateProjectName(DataUpdateReq req) {
        return commonSectionService.updateProjectName(req.getProjectId(), req.getProjectName());
    }

    /**
     * 修改采购项目名称
     * @param req
     * @return
     */
    private Boolean updatePurchaseName(DataUpdateReq req) {
        return commonSectionService.updatePurchaseName(req.getProjectId(), req.getPurchaseName());
    }

    /**
     * 修改标段发售截至日期
     * @param req
     * @return
     */
    private Boolean updateSaleEndTime(DataUpdateReq req,SectionInfoDTO section) {
        return commonSectionService.updateSaleEndTime(req.getSectionId(), req.getSaleEndTime(), section.getBidRound());
    }

    /**
     * 修改响应文件递交截至日期
     * @param req
     * @return
     */
    private Boolean updateSubmitEndTime(DataUpdateReq req,SectionInfoDTO section) {
        return  commonSectionService.updateSubmitEndTime(req.getSectionId(), req.getSubmitEndTime(), section.getBidRound());
    }

    /**
     * 新增或修改天眼查校验记录
     * @param req
     * @return
     */
    private Boolean updateTycRecord(DataUpdateReq req) {
        // 软删除相同企业名称的数据
        tycRecordService.remove(new LambdaQueryWrapper<TycRecord>().eq(TycRecord::getCompanyName, req.getCompanyName()));
        // 新增天眼查记录
        TycRecord tycRecord = new TycRecord();
        tycRecord.setCompanyName(req.getCompanyName());
        tycRecord.setKeyword(req.getOrganizationNum());
        tycRecord.setSelect_time(new Date());
        tycRecord.setValidateResult(1);
        tycRecord.setErrorCode(0);
        String json = "{\"result\":{\"regStatus\":\"正常\",\"city\":\"\",\"staffNumRange\":null,\"bondNum\":null,\"historyNameList\":null,\"industry\":null,\"bondName\":null,\"revokeDate\":null,\"type\":1,\"updateTimes\":null,\"legalPersonName\":\""+ req.getLegalRepresentative() +"\",\"revokeReason\":null,\"regNumber\":\"\",\"creditCode\":\"\",\"property3\":\"\",\"usedBondName\":null,\"approvedTime\":null,\"fromTime\":null,\"socialStaffNum\":null,\"actualCapitalCurrency\":\"\",\"alias\":\"\",\"companyOrgType\":\"\",\"cancelReason\":null,\"orgNumber\":\"\",\"toTime\":null,\"actualCapital\":\"\",\"estiblishTime\":null,\"regInstitute\":\"\",\"businessScope\":\"\",\"taxNumber\":\""+req.getOrganizationNum()+"\",\"creditCode\":\""+req.getOrganizationNum()+"\",\"regLocation\":\"\",\"regCapitalCurrency\":\"人民币\",\"tags\":\"\",\"district\":\"\",\"bondType\":null,\"name\":\""+ req.getCompanyName() +"\",\"industryAll\":null,\"isMicroEnt\":0,\"base\":\"js\"},\"reason\":\"ok\",\"error_code\":0}";
        tycRecord.setReturnInfo(json);
        return tycRecordService.save(tycRecord);
    }

    private Boolean updateBidWinNumber(DataUpdateReq dataUpdateReq, SectionInfoDTO section) {
        Boolean bidWinFlag = false;
        Boolean bidWinPeopleFlag = true;
        Boolean bidWinPeopleRecordFlag = true;
        Long sectionId = dataUpdateReq.getSectionId();
        Integer bidRound = section.getBidRound();
        Integer winPeopleNumber = dataUpdateReq.getWinPeopleNumber();
        Integer updateBidWinRow = this.baseMapper.updateBidWin(sectionId,bidRound,winPeopleNumber);
        if(updateBidWinRow >0){
            bidWinPeopleFlag = true;
        }
        List<Long> ids = this.baseMapper.queryBidWinPeople(sectionId,bidRound,winPeopleNumber);
        if(ids.size()>0){
            Integer updateBidWinPeopleRow = this.baseMapper.updateBidWinPeople(sectionId,bidRound,winPeopleNumber);
            if(!(updateBidWinPeopleRow >=0)){
                bidWinPeopleFlag = false;
            }
        }
        List<Long> recordIds = this.baseMapper.queryBidWinPeopleRecord(sectionId,bidRound,winPeopleNumber);
        if(recordIds.size()>0){
            Integer updateBidWinPeopleRecordRow = this.baseMapper.updateBidWinPeopleRecord(sectionId,bidRound,winPeopleNumber);
            if(!(updateBidWinPeopleRecordRow >=0)){
                bidWinPeopleRecordFlag = false;
            }
        }
        if(bidWinFlag || bidWinPeopleFlag || bidWinPeopleRecordFlag){
            bidWinFlag = true;
        }
        return bidWinFlag;
    }

    private void updateBidDoc(DataUpdateReq dataUpdateReq) {
        Long docFileId = dataUpdateReq.getDocFile();//招标文件文件ID
        //Long annexFile = dataUpdateReq.getAnnexFile();//附件ID
        OssFile ossFileById = ossFileService.getOssFileById(docFileId);
        if(null != ossFileById){
            this.baseMapper.updateBidDoc(dataUpdateReq.getSectionIds().get(0),ossFileById);
            for (String s:dataUpdateReq.getSectionIds()) {
                this.baseMapper.updateBidDocRecord( s ,ossFileById);
            }
        }
        //String sectionIds = String.join(",", dataUpdateReq.getSectionIds());
        //Long annexFileId = this.baseMapper.selectAnnexFile(sectionIds);
        //if(null != annexFileId){
        //    if(null != annexFile){
        //        this.baseMapper.updateAnnexFile(sectionIds,annexFile,0);
        //    } else {
        //        this.baseMapper.updateAnnexFile(sectionIds,annexFileId,1);
        //    }
        //} else {
        //    for (String sectionId:dataUpdateReq.getSectionIds()) {
        //        this.baseMapper.insertAnnexFile(sectionId,dataUpdateReq.getProjectId(),annexFile);
        //    }
        //}

    }

}
