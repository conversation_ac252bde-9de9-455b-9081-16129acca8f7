package com.hzw.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class FileInfo {
    // 文件ID
    @JsonProperty("id")
    private String id;
    // 文档名称
    @JsonProperty("name")
    private String name;
    // 文档版本
    @JsonProperty("version")
    private Integer version = 1;
    // 文档大小
    @JsonProperty("size")
    private Integer size;
    // 文档下载地址
    @JsonProperty("download_url")
    private String downloadUrl;
    // file
    //@JsonProperty("file")
    //private String fileDownload;
    // 文档保存地址
    @JsonProperty("upload_url")
    private String uploadUrl;
    // 文档操作权限
    @JsonProperty("permission")
    private Permission permission = new Permission();
    // 添加水印信息
    @JsonProperty("watermark")
    private Watermark watermark = new Watermark();
    // 用户信息
    @JsonProperty("user")
    private User user = new User();
}
