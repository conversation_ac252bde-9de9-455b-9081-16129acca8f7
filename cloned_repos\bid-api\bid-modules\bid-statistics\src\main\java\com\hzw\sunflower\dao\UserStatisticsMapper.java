package com.hzw.sunflower.dao;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hzw.sunflower.controller.request.UserStatisticsReq;
import com.hzw.sunflower.controller.response.DepartmentStatisticsVO;
import com.hzw.sunflower.controller.response.UserStatisticsVO;
import com.hzw.sunflower.entity.UserStatistics;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface UserStatisticsMapper extends BaseMapper<UserStatistics> {


    List<UserStatistics> queryBidOpen();


    List<UserStatistics> queryBidNotOpen();


    List<UserStatistics> queryBidOpenMoney();


    List<UserStatistics> queryBidNotOpenMoney();

    UserStatistics selectId(@Param("userId") Long userId,@Param("month") String month);


    List<UserStatistics> selectUserStatisticsByMonthAndCondition(@Param("userStatisticsReq")UserStatisticsReq userStatisticsReq);


    List<UserStatistics> selectDepartmentStatisticsByMonthAndCondition(@Param("userStatisticsReq") UserStatisticsReq userStatisticsReq);


    List<UserStatistics> selectCompanyStatisticsByMonthAndCondition(@Param("userStatisticsReq") UserStatisticsReq userStatisticsReq);


    List<UserStatistics> selectUserStatisticsByMonthAndConditionAgo(@Param("userStatisticsReq") UserStatisticsReq userStatisticsReq);


    List<UserStatistics> selectDepartmentStatisticsByMonthAndConditionAgo(@Param("userStatisticsReq") UserStatisticsReq userStatisticsReq);


    List<UserStatistics> selectCompanyStatisticsByMonthAndConditionAgo(@Param("userStatisticsReq") UserStatisticsReq userStatisticsReq);


    List<UserStatisticsVO> selectUserStatistics(@Param("userStatisticsReq") UserStatisticsReq userStatisticsReq);


    List<UserStatisticsVO> selectDepartmentStatistics(@Param("userStatisticsReq") UserStatisticsReq userStatisticsReq);


    List<UserStatisticsVO> selectCompanyStatistics(@Param("userStatisticsReq") UserStatisticsReq userStatisticsReq);


    List<DepartmentStatisticsVO> selectDepartmentStatisticsBasis(@Param("userStatisticsReq") UserStatisticsReq userStatisticsReq);


    List<UserStatistics> queryBidOpenAgencyMoney();


    List<UserStatistics> queryBidNotOpenAgencyMoney();


    List<UserStatistics> queryBidOpenWinMoney();


    List<UserStatistics> queryBidNotOpenWinMoney();


    void deleteByYearMonth(String format);
}
