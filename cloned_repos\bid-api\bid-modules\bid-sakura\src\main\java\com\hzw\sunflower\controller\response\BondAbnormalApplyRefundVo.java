package com.hzw.sunflower.controller.response;

import com.hzw.sunflower.entity.BondApplyRefund;
import com.hzw.sunflower.entity.BondApplyRefundProject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@ApiModel(description = "保证金异常退款申请信息")
@Data
public class BondAbnormalApplyRefundVo {

    @ApiModelProperty("申请退还表")
    private BondApplyRefund bondApplyRefund;

    @ApiModelProperty("申请退还项目信息表")
    private List<BondApplyRefundProject> bondApplyRefundProjectList;

}
