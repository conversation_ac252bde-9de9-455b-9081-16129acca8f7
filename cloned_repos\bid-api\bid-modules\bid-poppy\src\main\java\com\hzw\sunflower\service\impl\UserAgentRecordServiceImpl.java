package com.hzw.sunflower.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzw.sunflower.dao.UserAgentRecordMapper;
import com.hzw.sunflower.dto.UserAgentRecordDTO;
import com.hzw.sunflower.entity.UserAgentRecord;
import com.hzw.sunflower.service.UserAgentRecordService;
import org.springframework.stereotype.Service;


/**
 * 用户创建代理的记录Service接口实现
 *
 * <AUTHOR>
 * @version 1.0.0 2021-04-14
 */
@Service
public class UserAgentRecordServiceImpl extends ServiceImpl<UserAgentRecordMapper, UserAgentRecord> implements UserAgentRecordService {

    /**
     * 根据用户id获取上一次填写的代理机构或招标人信息
     * @param userId
     * @return
     */
    @Override
    public UserAgentRecordDTO getByUserId(Integer userIdentity, Long userId) {
        return this.baseMapper.getByUserId(userIdentity,userId);
    }

}