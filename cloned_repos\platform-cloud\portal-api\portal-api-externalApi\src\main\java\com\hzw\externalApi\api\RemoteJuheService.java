package com.hzw.externalApi.api;


/**
 * @ClassName:JuheService
 * @Auther: lijinxin
 * @Description: 聚合接口
 * @Date: 2023/7/31 10:45
 * @Version: v1.0
 */
public interface RemoteJuheService {



     /**
      * 身份证三要素验证
      * @param name
      * @param idNumber
      * @param phone
      * @return
      */
     Boolean idValidate(String name, String idNumber, String phone);


     /**
      * 姓名手机号二要素验证
      * @param name
      * @param phone
      * @return
      */
     Boolean validateByNameAndPhone(String name, String phone);

     /**
      *姓名身份证二要素验证
      * @param name
      * @param idCard
      * @return
      */
     Boolean validateByNameAndIdCard(String name, String idCard);


}
