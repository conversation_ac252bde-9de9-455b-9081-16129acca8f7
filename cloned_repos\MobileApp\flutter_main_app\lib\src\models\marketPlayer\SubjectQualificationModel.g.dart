// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'SubjectQualificationModel.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SubjectQualificationModel _$SubjectQualificationModelFromJson(
    Map<String, dynamic> json) {
  return SubjectQualificationModel(
    active: json['active'] as bool,
    approvalInstitution: json['approvalInstitution'] as String,
    caNoDataKey: json['caNoDataKey'] as String,
    certificateNo: json['certificateNo'] as String,
    createdDate: json['createdDate'] as String,
    dataKey: json['dataKey'] as String,
    declarationPerson: json['declarationPerson'] as String,
    expiryDate: json['expiryDate'] as String,
    modifiedDate: json['modifiedDate'] as String,
    qualificationClass: json['qualificationClass'] as String,
    qualificationGrade: json['qualificationGrade'] as String,
    qualificationID: json['qualificationID'] as int,
    qualificationName: json['qualificationName'] as String,
    qualificationNo: json['qualificationNo'] as String,
    qualificationType: json['qualificationType'] as String,
    statusValue: json['statusValue'] as int,
    subjectCode: json['subjectCode'] as String,
    subjectDataKey: json['subjectDataKey'] as String,
    subjectID: json['subjectID'] as int,
    subjectType: json['subjectType'] as String,
    validDate: json['validDate'] as String,
  );
}

Map<String, dynamic> _$SubjectQualificationModelToJson(
        SubjectQualificationModel instance) =>
    <String, dynamic>{
      'active': instance.active,
      'approvalInstitution': instance.approvalInstitution,
      'caNoDataKey': instance.caNoDataKey,
      'certificateNo': instance.certificateNo,
      'createdDate': instance.createdDate,
      'dataKey': instance.dataKey,
      'declarationPerson': instance.declarationPerson,
      'expiryDate': instance.expiryDate,
      'modifiedDate': instance.modifiedDate,
      'qualificationClass': instance.qualificationClass,
      'qualificationGrade': instance.qualificationGrade,
      'qualificationID': instance.qualificationID,
      'qualificationName': instance.qualificationName,
      'qualificationNo': instance.qualificationNo,
      'qualificationType': instance.qualificationType,
      'statusValue': instance.statusValue,
      'subjectCode': instance.subjectCode,
      'subjectDataKey': instance.subjectDataKey,
      'subjectID': instance.subjectID,
      'subjectType': instance.subjectType,
      'validDate': instance.validDate,
    };
