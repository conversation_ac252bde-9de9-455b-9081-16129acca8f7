package com.hzw.sunflower.entity;


import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(description = "用户授权数据给用户表")
@TableName("t_user_dataperm_user")
@Data
public class UserDatapermUser {


    private Long id;

    @ApiModelProperty(value = "用户id")
    private Long userId;

    @ApiModelProperty(value = "部门id")
    private Long departId;

    @ApiModelProperty(value = "来源用户ID")
    private Long fromUserId;

    @ApiModelProperty(value = "数据范围：0 我的全部项目 1 选择项目")
    private Integer dataScope;

    @ApiModelProperty(value = "类型：project 项目")
    private String type ;

    @ApiModelProperty(value = "权限码0：查看，1：编辑查看")
    private Integer rightCode;
}
