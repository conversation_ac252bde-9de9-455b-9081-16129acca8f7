package com.hzw.ssm.fw.util;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.apache.commons.beanutils.PropertyUtils;

public class ExportEntity {

	private String column;
	private String titleTop;
	private String title;
	private String width;
	private String align;
	private String key;
	private String style;

	public static List<ExportEntity> parse(String exportFieldStyle) {
		List<ExportEntity> lst = new ArrayList<ExportEntity>();
		String[] fieldStyle = exportFieldStyle.split("[|]");
		for (int i = 0; i < fieldStyle.length; i++) {
			ExportEntity expEntity = new ExportEntity();
			String[] entity = fieldStyle[i].split("[/]");

			expEntity.setColumn(getString(entity, 0));
			if (getString(entity, 1).indexOf(",") > 0) {
				String[] title = getString(entity, 1).split("[,]");
				expEntity.setTitleTop(title[0]);
				expEntity.setTitle(title[1]);
			} else {
				expEntity.setTitle(getString(entity, 1));
			}
			if (getString(entity, 2).indexOf(",") > 0) {
				String[] widthAlign = getString(entity, 2).split("[,]");
				expEntity.setWidth(widthAlign[0]);
				expEntity.setAlign(widthAlign[1]);
			} else {
				expEntity.setWidth(getString(entity, 2));
			}
			expEntity.setKey(getString(entity, 3));
			expEntity.setStyle(getString(entity, 4));

			lst.add(expEntity);
		}
		return lst;
	}

	private static String getString(String[] s, int l) {
		String ret = "";
		if (l < s.length)
			ret = s[l];
		return ret;
	}

	@SuppressWarnings("rawtypes")
	public static byte[] parse(List lstData, List<ExportEntity> lstColumn,
			String exportFileName) throws Exception {
		byte[] buf = null;
		ExcelReport rep = new ExcelReport();
		rep.createSheet(exportFileName);
		int colNum = lstColumn.size();
		int rowNum = lstData.size();

		// 表格行标题、宽度、对齐方式等属性
		String[] column = new String[colNum];
		String[] titleTop = new String[colNum];
		String[] title = new String[colNum];
		int[] width = new int[colNum];
		int[] align = new int[colNum];
		String[] key = new String[colNum];
		String[] style = new String[colNum];

		for (int i = 0; i < colNum; i++) {
			column[i] = lstColumn.get(i).getColumn();
			titleTop[i] = lstColumn.get(i).getTitleTop();
			title[i] = lstColumn.get(i).getTitle();
			width[i] = CommUtil.changeStringToInt(lstColumn.get(i).getWidth(),
					1);
			align[i] = CommUtil.changeStringToInt(lstColumn.get(i).getAlign(),
					1);
			key[i] = lstColumn.get(i).getKey();
			style[i] = lstColumn.get(i).getStyle();
		}

		// 表格数据
		List<String[]> data = new java.util.ArrayList<String[]>();
		for (int i = 0; i < rowNum; i++) {
			Map map = PropertyUtils.describe(lstData.get(i));
			String[] rows = new String[colNum];
			for (int j = 0; j < colNum; j++) {
				Object obj = map.get(column[j]);
				String val = "";
				if (obj instanceof java.util.Date) {
					val = DateUtil.getFormatDateTime("yyyy-MM-dd",
							(java.util.Date) obj);
				} else if (obj instanceof String) {
					val = (String) obj;
				} else {
					val = String.valueOf(obj);
				}

				if (key[j] != null && !key[j].equals("")) {
					//rows[j] = Dicts.getNameByCode(key[j], val);
				} else {
					rows[j] = val;
				}
			}
			data.add(rows);
		}

		// 插入表格信息（含大标题）
		rep.insertTable(exportFileName, title, data, width, false);

		buf = rep.getBuf();
		return buf;
	}

	public String getColumn() {
		return column;
	}

	public void setColumn(String column) {
		this.column = column;
	}

	public String getTitleTop() {
		return titleTop;
	}

	public void setTitleTop(String titleTop) {
		this.titleTop = titleTop;
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public String getWidth() {
		return width;
	}

	public void setWidth(String width) {
		this.width = width;
	}

	public String getAlign() {
		return align;
	}

	public void setAlign(String align) {
		this.align = align;
	}

	public String getKey() {
		return key;
	}

	public void setKey(String key) {
		this.key = key;
	}

	public String getStyle() {
		return style;
	}

	public void setStyle(String style) {
		this.style = style;
	}

}
