package com.hzw.ssm.applets.action;

import com.hzw.ssm.applets.entity.TitleInfo;
import com.hzw.ssm.applets.service.TitleInfoService;
import com.hzw.ssm.applets.utils.GetJsonReqData;
import com.hzw.ssm.applets.utils.GetJsonRespData;
import com.hzw.ssm.applets.utils.JsonToObject;
import com.hzw.ssm.applets.utils.ResultJson;
import com.hzw.ssm.fw.base.BaseAction;
import net.sf.json.JSONObject;
import org.apache.struts2.convention.annotation.Action;
import org.apache.struts2.convention.annotation.Namespace;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @Date 2021-02-01 17:09
 * @Version 1.0
 */
@Namespace("/applets/titleInfoAction")
public class TitleInfoAction extends BaseAction {

    @Autowired
    private TitleInfoService titleInfoService;

    /**
     * 新增
     *
     * @return
     */
    @Action("save")
    public String save() {

        JSONObject jsonReqData = GetJsonReqData.getJsonReqData();
        String toString = jsonReqData.getJSONObject("formData").toString();
        TitleInfo titleInfo = JsonToObject.jsonToObject(toString, TitleInfo.class);
        ResultJson resultJson = titleInfoService.insertTitleInfo(titleInfo);
        GetJsonRespData.getJsonRespData(resultJson);
        return null;
    }

    /**
     * 编辑
     *
     * @return
     */
    @Action("update")
    public String update() {

        JSONObject jsonReqData = GetJsonReqData.getJsonReqData();
        String toString = jsonReqData.getJSONObject("formData").toString();
        TitleInfo titleInfo = JsonToObject.jsonToObject(toString, TitleInfo.class);
        ResultJson resultJson = titleInfoService.updateTitleInfo(titleInfo);
        GetJsonRespData.getJsonRespData(resultJson);
        return null;
    }

    /**
     * 删除
     *
     * @return
     */
    @Action("delete")
    public String delete() {

        JSONObject jsonReqData = GetJsonReqData.getJsonReqData();
        String toString = jsonReqData.getJSONObject("formData").toString();
        TitleInfo titleInfo = JsonToObject.jsonToObject(toString, TitleInfo.class);
        ResultJson resultJson = titleInfoService.deleteTitleInfo(titleInfo);
        GetJsonRespData.getJsonRespData(resultJson);
        return null;
    }

    /**
     * 查询单个
     *
     * @return
     */
    @Action("getOne")
    public String getOne() {

        JSONObject jsonReqData = GetJsonReqData.getJsonReqData();
        String toString = jsonReqData.getJSONObject("formData").toString();
        TitleInfo titleInfo = JsonToObject.jsonToObject(toString, TitleInfo.class);
        ResultJson resultJson = titleInfoService.getTitleInfo(titleInfo);
        GetJsonRespData.getJsonRespData(resultJson);
        return null;
    }

    /**
     * 查询所有
     *
     * @return
     */
    @Action("all")
    public String all() {

        JSONObject jsonReqData = GetJsonReqData.getJsonReqData();
        String toString = jsonReqData.getJSONObject("formData").toString();
        TitleInfo titleInfo = JsonToObject.jsonToObject(toString, TitleInfo.class);
        ResultJson resultJson = titleInfoService.getTitleInfos(titleInfo);
        GetJsonRespData.getJsonRespData(resultJson);
        return null;
    }

}
