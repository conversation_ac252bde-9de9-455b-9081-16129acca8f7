package com.hzw.sunflower.entity.condition;

import com.hzw.sunflower.common.BaseCondition;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 项目表 查询条件
 *
 * <AUTHOR>
 * @version 1.0.0 2021-04-07
 */
@Data
@ApiModel(description = "项目表 查询条件")
public class ProjectCondition extends BaseCondition {
    /**
     * 版本号
     */
    private static final long serialVersionUID = -4256128078404775662L;

    @ApiModelProperty(value = "关键字搜索")
    private String keyWords;
    @ApiModelProperty(value = "项目名称")
    private String projectName;
    @ApiModelProperty(value = "项目编号")
    private String projectNumber;
    @ApiModelProperty(value = "采购名称")
    private String purchaseName;
    @ApiModelProperty(value = "采购编号")
    private String purchaseNumber;
    @ApiModelProperty(value = "省")
    private String addressProvince;
    @ApiModelProperty(value = "市")
    private String addressCity;

    @ApiModelProperty(value = "代理单位")
    private String agentCompany;
    @ApiModelProperty(value = "代理机构负责人")
    private String agentChargeName;
    @ApiModelProperty(value = "代理机构负责人电话")
    private String agentChargePhone;

    @ApiModelProperty(value = "委托单位")
    private String principalCompany;
    @ApiModelProperty(value = "委托负责人")
    private String principalChargeName;
    @ApiModelProperty(value = "委托负责人电话")
    private String principalChargePhone;

    @ApiModelProperty(value = "项目状态")
    private String projectStatus;

    @ApiModelProperty(value = "开始时间")
    private String beginTime;
    @ApiModelProperty(value = "结束时间")
    private String endTime;

    @ApiModelProperty(value = "排序方式")
    private String orderType;

}