///***
/// * 数据报告 模型
/// */

import 'package:json_annotation/json_annotation.dart';

part 'DataReportModel.g.dart';

@JsonSerializable(explicitToJson: true)
class DataReportModel {

  DataReportModel({
    this.annualTransaction,
    this.annualTransactionComparisonLastYear,
    this.annualTransactionComparisonTheYearBeforeLastYear,
    this.annualTransactionComparisonThisYear,
    this.constructionAmountRanking,
    this.constructionCountRanking,
    this.projectTransaction,
    this.regionalTender,
  });

  ///***
  /// * 年度累计交易
  /// *
  /// * name: 名称
  /// * count: 宗数
  /// * amount: 总额
  /// */
  @JsonKey()
  final List<Map<String, dynamic>> annualTransaction;

  ///***
  /// * 近三年交易总数对比 - 今年
  /// */
  @JsonKey()
  final List<Map<String, dynamic>> annualTransactionComparisonThisYear;

  ///***
  /// * 近三年交易总数对比 - 去年
  /// */
  @JsonKey()
  final List<Map<String, dynamic>> annualTransactionComparisonLastYear;

  ///***
  /// * 近三年交易总数对比 - 前年
  /// */
  @JsonKey()
  final List<Map<String, dynamic>> annualTransactionComparisonTheYearBeforeLastYear;

  ///***
  /// * 施工类排名 - 中标金额
  /// */
  @JsonKey()
  final List<Map<String, dynamic>> constructionAmountRanking;

  ///***
  /// * 施工类排名 - 中标次数
  /// */
  @JsonKey()
  final List<Map<String, dynamic>> constructionCountRanking;

  ///***
  /// * 年度项目交易总量
  /// */
  @JsonKey()
  final List<Map<String, dynamic>> projectTransaction;

  ///***
  /// * 年度区域招标总体情况
  /// */
  @JsonKey()
  final List<Map<String, dynamic>> regionalTender;

  factory DataReportModel.fromJson(Map<String, dynamic> json) => _$DataReportModelFromJson(json);

  Map<String, dynamic> toJson() => _$DataReportModelToJson(this);

}
