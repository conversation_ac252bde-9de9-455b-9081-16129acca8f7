///***
/// * 我的 - 设置 - 入口页
/// */

import 'package:flutter/material.dart';

import 'package:flutter_main_app/src/constants/theme.dart';
import 'package:flutter_main_app/src/utils/ScreenUtils.dart';

import 'package:flutter_main_app/src/components/CustomAppBar.dart';
import 'package:flutter_main_app/src/components/CustomSafeArea.dart';
import 'package:flutter_main_app/src/components/CustomListTile.dart';
import 'package:flutter_main_app/src/components/CustomSubmitButton.dart';

class ProfileSettingsIndex extends StatelessWidget {

  const ProfileSettingsIndex({Key key}) : super(key: key);

  /// * 路由名称
  static const String _routeName = 'ProfileSettingsIndex';
  static String get routeName => _routeName;

  /// * 尾部图标
  static const Icon _trailingIcon = Icon(
    Icons.arrow_forward_ios,
    size: 14.0,
    color: Color(0xff999999),
  );

  static const TextStyle _infoTextStyle = TextStyle(
    fontSize: 14.0,
    color: themeHintTextColor,
  );

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      appBar: CustomAppBar(
        title: '设置',
      ),
      body: CustomSafeArea(
        child: Stack(
          children: <Widget>[
            /// * 设置选项
            ListView(
              physics: BouncingScrollPhysics(),
              children: <Widget>[
                GestureDetector(
                  onTap: () {
                    Navigator.pushNamed(
                      context,
                      'ProfileSettingsChangePassword',
                    );
                  },
                  child: CustomListTile(
                    title: '修改密码',
                    trailing: _trailingIcon,
                    isFirstChild: true,
                  ),
                ),
                GestureDetector(
                  onTap: () {
                    Navigator.pushNamed(
                      context,
                      'ProfileSettingsChangeMobileNumber',
                    );
                  },
                  child: CustomListTile(
                    title: '修改手机',
                    infos: <Widget>[
                      Text(
                        '186****4706',
                        style: _infoTextStyle,
                      ),
                    ],
                    trailing: _trailingIcon,
                  ),
                ),
                Divider(
                  height: 10.0,
                  thickness: 10.0,
                  color: themeBackgroundColor,
                ),
                GestureDetector(
                  onTap: () {
                    Navigator.pushNamed(
                      context,
                      'ProfileSettingsFeedBack',
                    );
                  },
                  child: CustomListTile(
                    title: '意见反馈',
                    trailing: _trailingIcon,
                    isFirstChild: true,
                  ),
                ),
                GestureDetector(
                  onTap: () {
                    print('cache cleared...');
                  },
                  child: CustomListTile(
                    title: '清除缓存',
                    infos: <Widget>[
                      Text(
                        '76.03K',
                        style: _infoTextStyle,
                      ),
                    ],
                  ),
                ),
              ],
            ),
            /// * 退出登录
            Positioned(
              left: 0,
              bottom: 50.0,
              child: CustomSubmitButton(
                text: '退出登录',
                onTap: () {
                  this._handleLogOut(context);
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  ///***
  /// * 退出登录事件
  /// *
  /// * @param {BuildContext context}
  /// */
  void _handleLogOut(BuildContext context) {
    const String confirm = 'confirm';

    Widget buildLogOutModalBottomSheet(BuildContext context) {
      final Widget logOutModalBottomSheet = Container(
        width: ScreenUtils.screenWidth,
        color: themeContentBackgroundColor,
        child: SafeArea(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: <Widget>[
              /// * 确认提示
              Container(
                width: ScreenUtils.screenWidth,
                height: 42.0,
                color: themeBackgroundColor,
                child: Center(
                  child: Text(
                    '确定退出当前账号吗？',
                    style: TextStyle(
                      fontSize: 14.0,
                      color: themeTipsTextColor,
                    ),
                  ),
                ),
              ),
              /// * 确定
              GestureDetector(
                onTap: () {
                  Navigator.of(context).pop(confirm);
                },
                child: Container(
                  width: ScreenUtils.screenWidth,
                  height: 40.0,
                  child: Center(
                    child: Text(
                      '确定',
                      style: TextStyle(
                        fontSize: 14.0,
                        color: themeActiveColor,
                      ),
                    ),
                  ),
                ),
              ),
              Divider(
                height: themeBorderWidth,
                thickness: themeBorderWidth,
                color: themeBackgroundColor,
              ),
              /// * 取消
              GestureDetector(
                onTap: () {
                  Navigator.of(context).pop();
                },
                child: Container(
                  width: ScreenUtils.screenWidth,
                  height: 40.0,
                  child: Center(
                    child: Text(
                      '取消',
                      style: TextStyle(
                        fontSize: 14.0,
                        color: themeHintTextColor,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      );

      return logOutModalBottomSheet;
    }

    final Future<String> modalBottomSheet = showModalBottomSheet<String>(
      context: context,
      builder: (BuildContext context) => buildLogOutModalBottomSheet(context),
    );

    modalBottomSheet.then((String value) {
      if (value != null && value == confirm) {
        print('log out confirmed...');
      } else {
        print('log out cancelled...');
      }
    });

  }
}
