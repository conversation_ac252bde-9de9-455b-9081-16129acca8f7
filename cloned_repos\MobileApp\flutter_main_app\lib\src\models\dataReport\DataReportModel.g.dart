// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'DataReportModel.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

DataReportModel _$DataReportModelFromJson(Map<String, dynamic> json) {
  return DataReportModel(
    annualTransaction: (json['annualTransaction'] as List)
        ?.map((e) => e as Map<String, dynamic>)
        ?.toList(),
    annualTransactionComparisonLastYear:
        (json['annualTransactionComparisonLastYear'] as List)
            ?.map((e) => e as Map<String, dynamic>)
            ?.toList(),
    annualTransactionComparisonTheYearBeforeLastYear:
        (json['annualTransactionComparisonTheYearBeforeLastYear'] as List)
            ?.map((e) => e as Map<String, dynamic>)
            ?.toList(),
    annualTransactionComparisonThisYear:
        (json['annualTransactionComparisonThisYear'] as List)
            ?.map((e) => e as Map<String, dynamic>)
            ?.toList(),
    constructionAmountRanking: (json['constructionAmountRanking'] as List)
        ?.map((e) => e as Map<String, dynamic>)
        ?.toList(),
    constructionCountRanking: (json['constructionCountRanking'] as List)
        ?.map((e) => e as Map<String, dynamic>)
        ?.toList(),
    projectTransaction: (json['projectTransaction'] as List)
        ?.map((e) => e as Map<String, dynamic>)
        ?.toList(),
    regionalTender: (json['regionalTender'] as List)
        ?.map((e) => e as Map<String, dynamic>)
        ?.toList(),
  );
}

Map<String, dynamic> _$DataReportModelToJson(DataReportModel instance) =>
    <String, dynamic>{
      'annualTransaction': instance.annualTransaction,
      'annualTransactionComparisonThisYear':
          instance.annualTransactionComparisonThisYear,
      'annualTransactionComparisonLastYear':
          instance.annualTransactionComparisonLastYear,
      'annualTransactionComparisonTheYearBeforeLastYear':
          instance.annualTransactionComparisonTheYearBeforeLastYear,
      'constructionAmountRanking': instance.constructionAmountRanking,
      'constructionCountRanking': instance.constructionCountRanking,
      'projectTransaction': instance.projectTransaction,
      'regionalTender': instance.regionalTender,
    };
