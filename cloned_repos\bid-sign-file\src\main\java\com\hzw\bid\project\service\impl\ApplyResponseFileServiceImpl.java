package com.hzw.bid.project.service.impl;


import cfca.sadk.util.EncryptUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzw.bid.project.constant.CommonConstants;
import com.hzw.bid.project.controller.response.DecryptFileVo;
import com.hzw.bid.project.mapper.ApplyResponseFileMapper;
import com.hzw.bid.project.model.ApplyResponseFile;
import com.hzw.bid.project.model.OssFile;
import com.hzw.bid.project.service.ApplyResponseFileService;
import com.hzw.bid.project.service.IOssFileService;
import com.hzw.bid.util.FileUtils;
import com.hzw.bid.util.PdfSignatureUtil;
import com.hzw.bid.util.oss.OssFileStorage;
import com.hzw.bid.util.oss.OssUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import java.io.*;
import java.util.*;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR> Gen
 * @since 2022-06-01
 */
@Slf4j
@Service
public class ApplyResponseFileServiceImpl extends ServiceImpl<ApplyResponseFileMapper, ApplyResponseFile> implements ApplyResponseFileService {

    @Value("${files.template.path}")
    private String templatePath;

    @Value("${files.temporary.path}")
    private String tempFilePath;

    @Value("${oss.active}")
    private String ossType;

    @Value("${files.temporary.path}")
    private String temporaryFilePath;



    @Autowired
    private IOssFileService ossFileService;



    /**
     * 校验pdf签名是否和对应组织机构代码的企业一样
     * @param temp
     * @param organizationNum
     * @return
     */
    @Override
    public Integer verifySignature(File temp, String organizationNum) {
        Integer result = CommonConstants.NO2;
//        OssFile ossFile = ossFileService.getBaseMapper().selectById(fileOssIdDec);
//        OssFileStorage storage = OssUtil.getOssFileStorage(ossType);
//        if(null != ossFile ){
        if(null != temp ){
//            byte[] bytes = storage.downloadFile(ossFile.getOssFileKey());
//            InputStream inputStream = FileUtils.byte2InputStream(bytes);
//            String fileName = ossFile.getOssFileName();
//            String filepath = temporaryFilePath + fileName;
//            FileUtils.inputStreamToFile(inputStream, filepath);
            try {
                result = PdfSignatureUtil.verifySignature(temp.getPath(), organizationNum) ? CommonConstants.YES : CommonConstants.NO2;
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
//                File file = new File(temp.getPath());
               // temp.delete();
            }
        }
        return result;
    }

    /**
     * 解密接口
     * @param ossId
     * @param pwd
     * @return
     * @throws
     */
    @Override
    public  DecryptFileVo  caDecrypt(Long ossId, String pwd) {
        DecryptFileVo decryptFileVo = new DecryptFileVo();
        Long ossid = null;
        OssFile ossFile = ossFileService.getBaseMapper().selectById(ossId);
        OssFileStorage storage = OssUtil.getOssFileStorage(ossType);
        if(null != ossFile ){
//            byte[] bytes = storage.downloadFile(ossFile.getOssFileKey());
//            InputStream inputStream = FileUtils.byte2InputStream(bytes);
            String fileName = ossFile.getOssFileName();
            // 非加密文件不处理
            if(!fileName.endsWith(".enc")){
                decryptFileVo.setOssid(null);
                return decryptFileVo;
            }
            String  dfileName = fileName.substring(0, fileName.lastIndexOf("."));
            String s = UUID.randomUUID().toString();
            String filepath = temporaryFilePath +  s+fileName;
            String dfilepath = temporaryFilePath +  s+dfileName;
            try {
                storage.downloadFilePath(ossFile.getOssFileKey(),filepath);
                //FileUtils.inputStreamToFile(inputStream,temporaryFilePath+ s+ossFile.getOssFileName());
                EncryptUtil.decryptFileByDES3(filepath, dfilepath, pwd);
                ossid = ossFileService.saveOssFile(dfileName, dfilepath);
                decryptFileVo.setOssid(ossid);
                decryptFileVo.setTemp(new File(dfilepath));
            }catch (Exception e){
                e.printStackTrace();
                log.error("filepath-----------"+filepath);
                log.error("dfilepath-----------"+dfilepath);
            }finally {
   /*             try {
                    inputStream.close();
                } catch (IOException e) {
                   e.printStackTrace();
                }*/
                //删除临时文件
                File file = new File(filepath);
                file.delete();
//                File temp = new File(dfilepath);
//                temp.delete();
            }
        }
        return decryptFileVo;
    }


}
