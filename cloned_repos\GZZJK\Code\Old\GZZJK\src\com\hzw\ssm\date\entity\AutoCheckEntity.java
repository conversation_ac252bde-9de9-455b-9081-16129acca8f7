package com.hzw.ssm.date.entity;

import java.io.Serializable;

import com.hzw.ssm.fw.base.BaseEntity;

public class AutoCheckEntity extends BaseEntity implements Serializable {
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	private Integer id;//表示主键ID
	
	private Long check_time;//自动审核判断时间
	
	private Integer delete_flag;//删除标记

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Long getCheck_time() {
		return check_time;
	}

	public void setCheck_time(Long check_time) {
		this.check_time = check_time;
	}

	@Override
	public Integer getDelete_flag() {
		return delete_flag;
	}

	@Override
	public void setDelete_flag(Integer delete_flag) {
		this.delete_flag = delete_flag;
	}

}
