package com.hzw.sunflower.constant.constantenum;


/**
 * 文件code枚举
 *
 * <AUTHOR>
 * @date ：Created in 2021/5/12 10:02
 * @description：文件code枚举
 * @modified By：`
 * @version: 1.0
 */
public enum FileDocumentCodeEnum {

    JKLX("jklx", "监控录像"),
    TBRQDWJ("tbrqdwj", "投标人签到文件"),
    PWQDWJ("pwqdwj", "评委签到文件"),
    // 一轮签到文件
    WTQDWJ("wtqdwj", "评委签到文件"),
    ZJCQWJ("zjcqwj", "专家抽取文件"),
    KBJLWJ("kbjlwj", "开标记录文件"),
    KBJLB("kbjlb", "开标记录表"),
    PBXCCQDYJLWJ("pbxccqdyjlwj", "评标现场澄清答疑记录文件"),
    DFJLWJ("dfjlwj", "打分记录文件"),
    YYCL("yycl", "异议处理"),
    KBGCWJ("kbgcwj", "开标过程文件"),
    PBBGWJ("pbbgwj", "评标报告文件"),
    ZBRTYJXKBHGBCGFSDQRH("zbrtyjxkbhgbcgfsdqrh", "招标人同意继续开标或改变采购方式的说明材料");
    /**
     * 类型
     */
    private String code;

    /**
     * 说明
     */
    private String msg;

    FileDocumentCodeEnum(String code, String desc) {
        this.code = code;
        this.msg = desc;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsgc(String msg) {
        this.msg = msg;
    }


    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    /**
     * 获取对应的枚举
     *
     * @param code 种类
     * @return 枚举值
     */
    public static FileDocumentCodeEnum getStatus(Integer code) {
        try {
            for (FileDocumentCodeEnum userStatusEnum : FileDocumentCodeEnum.values()) {
                if (userStatusEnum.code.equals(code)) {
                    return userStatusEnum;
                }
            }
            return null;
        } catch (Exception e) {
            return null;
        }
    }
}
