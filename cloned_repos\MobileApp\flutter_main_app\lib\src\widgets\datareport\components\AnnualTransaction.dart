///***
/// * 年度累计交易宗数、交易总额
/// */

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import 'package:flutter_main_app/src/utils/FunctionUtils.dart';
import 'package:flutter_main_app/src/constants/theme.dart';

import 'package:flutter_main_app/src/viewModels/dataReport/DataReportViewModel.dart';

import 'package:flutter_main_app/src/components/charts/CustomCircularRippleChart.dart';

class AnnualTransaction extends StatelessWidget {

  const AnnualTransaction({Key key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Consumer<DataReportViewModel>(
      builder: (context, model, child) {

        ///***
        /// * 年度累计交易 - 相关数据
        /// */

        final Map<String, dynamic> annualTransaction = model.reportData?.annualTransaction != null
          ? model.reportData?.annualTransaction[0]
          : null;

        final int count = annualTransaction != null
          ? annualTransaction['count'] ?? 0
          : 0;

        final double amount = annualTransaction != null
          ? FunctionUtils.convertYuanToWanYuan(annualTransaction['amount'] ?? 0.00)
          : 0.00;

        final int lastYearCount = annualTransaction != null
          ? annualTransaction['lastYearCount'] ?? 0
          : 0;

        final double lastYearAmount = annualTransaction != null
          ? FunctionUtils.convertYuanToWanYuan(annualTransaction['lastYearAmount'] ?? 0.00)
          : 0.00;

        final double countRatio = lastYearCount == 0
          ? 1.0
          : (count - lastYearCount) / lastYearCount;

        final double amountRatio = lastYearAmount == 0.00
          ? 1.0
          : (amount - lastYearAmount) / lastYearAmount;

        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 15.0),
          child: Row(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              Expanded(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: <Widget>[
                    CustomCircularRippleChart(
                      number: count,
                      unit: '宗',
                      yearOnYear: countRatio,
                    ),
                    Padding(
                      padding: const EdgeInsets.only(top: 15.0),
                      child: Text(
                        '年度累计交易宗数',
                        style: themeActiveTextStyle,
                      ),
                    ),
                  ],
                ),
              ),
              Expanded(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: <Widget>[
                    CustomCircularRippleChart(
                      number: amount,
                      unit: '万元',
                      yearOnYear: amountRatio,
                    ),
                    Padding(
                      padding: const EdgeInsets.only(top: 15.0),
                      child: Text(
                        '年度累计交易总额',
                        style: themeActiveTextStyle,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

}
