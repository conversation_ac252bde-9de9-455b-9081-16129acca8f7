package com.hzw.sunflower.controller.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 关联附件请求实体
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@ApiModel(description = "关联附件请求体")
@Data
public class RelevancyAnnexREQ implements Serializable {

    @ApiModelProperty(value = "删除包关联附件", position = 1)
    private List<Long> deleteFileId;

    @ApiModelProperty(value = "删除附件", position = 2)
    private List<Long> deleteSectionId;

    @ApiModelProperty(value = "新增附件信息", position = 3)
    private List<AttachmentRelation> relevancyAnnex;
}
