package com.hzw.sunflower.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hzw.sunflower.common.BaseController;
import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.service.CommonSmsService;
import com.hzw.sunflower.service.SmsRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.hzw.sunflower.entity.SmsRecord;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "外部系统连接管理")
@RestController
@RequestMapping("/smsApp")
public class SmsAppController extends BaseController {

    @Resource
    private CommonSmsService commonSmsService;

    @Autowired
    private SmsRecordService smsRecordService;

    @ApiOperation(value = "查询余额")
    @GetMapping("/getBalance")
    public Result<JSONObject> getBalance() {
        String balance = commonSmsService.getBalance();
        JSONObject jsonObject = JSONObject.parseObject(balance);
        return Result.ok(jsonObject);
    }

    @ApiOperation(value = "查询近二十条短信信息")
    @GetMapping("/getSmsRecord")
    public Result<List<SmsRecord>> getSmsRecord(){
        LambdaQueryWrapper<SmsRecord> smsQuery = new LambdaQueryWrapper<>();
        smsQuery.orderByDesc(SmsRecord::getCreatedTime)
                .last("limit 20");
        List<SmsRecord> list = smsRecordService.list(smsQuery);
        return Result.ok(list);
    }

}
