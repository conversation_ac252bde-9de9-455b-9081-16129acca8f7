package com.hzw.sunflower.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hzw.sunflower.common.BaseController;
import com.hzw.sunflower.util.BeanListUtil;
import com.hzw.sunflower.common.Paging;
import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.constant.MessageConstants;
import com.hzw.sunflower.dto.SealApplicationProjectDTO;
import com.hzw.sunflower.controller.request.SealApplicationProjectREQ;
import com.hzw.sunflower.entity.SealApplicationProject;
import com.hzw.sunflower.entity.condition.SealApplicationProjectCondition;
import com.hzw.sunflower.service.SealApplicationProjectService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
* SealApplicationProjectController
*/
@Api(tags = "SealApplicationProject服务")
@RestController
@RequestMapping("/sealApplicationProject")
public class SealApplicationProjectController extends BaseController {
    @Autowired
    private SealApplicationProjectService sealApplicationProjectService;

    @ApiOperation(value = "根据条件分页查询SealApplicationProject列表")
    @ApiImplicitParam(name = "sealApplicationProjectREQ", value = "用户表 查询条件", required = true, dataType = "SealApplicationProjectREQ", paramType = "body")
    @PostMapping("/list")
    public Paging<SealApplicationProject> list(@RequestBody SealApplicationProjectREQ sealApplicationProjectREQ) {
        SealApplicationProjectCondition condition = BeanListUtil.convert(sealApplicationProjectREQ,SealApplicationProjectCondition.class);
        IPage<SealApplicationProject> page = sealApplicationProjectService.findInfoByCondition(condition);
            return Paging.buildPaging(page);
    }


    @ApiOperation(value = "根据主键ID查询SealApplicationProject信息")
    @ApiImplicitParam(name = "id", value = "主键ID", required = true)
    @GetMapping(value = "/get/{id}")
    public Result<SealApplicationProject> get(@PathVariable Long id) {
        if (id == null) {
            return Result.failed(MessageConstants.GET_NULL);
        }
        SealApplicationProject sealApplicationProject = sealApplicationProjectService.getInfoById(id);
        return Result.ok(sealApplicationProject);
    }

    @ApiOperation(value = "新增SealApplicationProject信息")
    @ApiImplicitParam(name = "sealApplicationProjectDTO", value = "SealApplicationProject表 ", required = true, dataType = "SealApplicationProjectDTO", paramType = "body")
    @PostMapping("/add")
    public Result<Boolean> add(@RequestBody SealApplicationProjectDTO sealApplicationProjectDTO) {
        Boolean bool = sealApplicationProjectService.addInfo(sealApplicationProjectDTO);
        return Result.okOrFailed(bool);
    }

    @ApiOperation(value = "修改SealApplicationProject信息")
    @ApiImplicitParam(name = "sealApplicationProjectDTO", value = "SealApplicationProject表 ", required = true, dataType = "SealApplicationProjectDTO", paramType = "body")
    @PutMapping(value = "/update")
    public Result<Boolean> update(@RequestBody SealApplicationProjectDTO sealApplicationProjectDTO) {
        Long id = sealApplicationProjectDTO.getId();
        if (id == null) {
            return Result.failed(MessageConstants.UPDATE_NULL);
        }
        Boolean bool = sealApplicationProjectService.updateInfo(sealApplicationProjectDTO);
        return Result.okOrFailed(bool);
    }

    @ApiOperation(value = "根据主键ID删除SealApplicationProject表 ")
    @ApiImplicitParam(name = "id", value = "主键ID", required = true)
    @DeleteMapping(value = "/delete/{id}")
    public Result<Boolean> delete(@PathVariable Long id) {
        if (id == null) {
            return Result.failed(MessageConstants.DELETE_NULL);
        }
        Boolean bool = sealApplicationProjectService.deleteById(id);
        return Result.okOrFailed(bool);
    }

    @ApiOperation(value = "根据主键ID列表批量删除SealApplicationProject表 ")
    @ApiImplicitParam(name = "idList", value = "主键ID列表", required = true, allowMultiple = true, paramType = "body")
    @DeleteMapping("/deleteList")
    public Result<Boolean> deleteList(@RequestBody List<Long> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return Result.failed(MessageConstants.DELETE_NULL);
        }
        Boolean bool = sealApplicationProjectService.deleteByIds(idList);
        return Result.okOrFailed(bool);
    }
}