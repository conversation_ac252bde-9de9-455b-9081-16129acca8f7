package com.hzw.sunflower.service;

import com.hzw.sunflower.controller.response.UserTokenRolesVO;

/**
 * @ClassName:CommonUserService
 * @Auther: lijinxin
 * @Description: 用户共用
 * @Date: 2024/7/23 14:46
 * @Version: v1.0
 */
public interface CommonUserService {


    /**
     * 自动获取token role
     * @return
     */
     UserTokenRolesVO getUserRoles();


    /**
     * 手动获取token role
     * @param userId
     * @param userIdentity
     * @return
     */
     UserTokenRolesVO getUserRolesByparameter(Long userId,Long deptId, Integer userIdentity);
}
