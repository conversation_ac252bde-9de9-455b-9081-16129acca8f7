package com.hzw.sunflower.controller.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date ：Created in 2021/4/16 16:55
 * @description：单位及联系人vo
 * @modified By：`
 * @version: 1.0
 */
@ApiModel(description = "单位及联系人vo")
@Data
public class LenovoUserQueryVO {

    @ApiModelProperty(value = "委托单位id", position = 1)
    private Long companyId;

    @ApiModelProperty(value = "委托单位名称", position = 2)
    private String companyName;

    @ApiModelProperty(value = "委托用户名称", position = 3)
    private String userName;

    @ApiModelProperty(value = "委托用户id", position = 4)
    private Long userId;

    @ApiModelProperty(value = "委托用户联系方式", position = 5)
    private String userPhone;

    @ApiModelProperty(value = "委托合同id", position = 3)
    private String uploadFileId;
}
