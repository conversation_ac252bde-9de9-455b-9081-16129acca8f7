package com.hzw.sunflower.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzw.sunflower.dao.UploadFileMapper;
import com.hzw.sunflower.entity.UploadFile;
import com.hzw.sunflower.entity.condition.UploadFileCondition;
import com.hzw.sunflower.service.UploadFileService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 上传文件存储表 Service接口实现
 *
 * <AUTHOR>
 * @version 1.0.0 2021-04-07
 */
@Service
public class UploadFileServiceImpl extends ServiceImpl<UploadFileMapper, UploadFile> implements UploadFileService {

    /**
     * 根据条件分页查询上传文件存储表 列表
     *
     * @param condition 查询条件
     * @return 分页数据
     */
    @Override
    public IPage<UploadFile> findUploadFileByCondition(UploadFileCondition condition) {
        IPage<UploadFile> page = condition.buildPage();
        QueryWrapper<UploadFile> queryWrapper = condition.buildQueryWrapper(UploadFile.class);
        return this.page(page, queryWrapper);
    }

    /**
     * 根据主键ID查询上传文件存储表 信息
     *
     * @param id 主键ID
     * @return 上传文件存储表 信息
     */
    @Override
    public UploadFile getUploadFileById(Long id) {
        return this.getById(id);
    }

    /**
     * 新增上传文件存储表 信息
     *
     * @param uploadFile 上传文件存储表 信息
     * @return 是否成功
     */
    @Override
    public Boolean addUploadFile(UploadFile uploadFile) {
        return this.save(uploadFile);
    }

    /**
     * 修改上传文件存储表 信息
     *
     * @param uploadFile 上传文件存储表 信息
     * @return 是否成功
     */
    @Override
    public Boolean updateUploadFile(UploadFile uploadFile) {
        return this.updateById(uploadFile);
    }

    /**
     * 根据主键ID删除上传文件存储表
     *
     * @param id 主键ID
     * @return 是否成功
     */
    @Override
    public Boolean deleteUploadFileById(Long id) {
        return this.removeById(id);
    }

    /**
     * 根据主键ID列表批量删除上传文件存储表
     *
     * @param idList 主键ID列表
     * @return 是否成功
     */
    @Override
    public Boolean deleteUploadFileByIds(List<Long> idList) {
        return this.removeByIds(idList);
    }
}