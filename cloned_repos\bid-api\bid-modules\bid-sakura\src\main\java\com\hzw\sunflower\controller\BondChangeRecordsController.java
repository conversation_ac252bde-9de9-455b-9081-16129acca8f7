package com.hzw.sunflower.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hzw.sunflower.common.BaseController;
import com.hzw.sunflower.common.Paging;
import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.controller.request.BondChangeRecordReq;
import com.hzw.sunflower.controller.response.BondChangeRecordsVo;
import com.hzw.sunflower.controller.response.BondChangeRecordsListVo;
import com.hzw.sunflower.entity.condition.BondChangeCondition;
import com.hzw.sunflower.service.BondChangeRecordsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * <p>
 * 保证金调整记录表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-27
 */
@Api(tags = "保证金调整记录")
@RestController
@RequestMapping("/bondChange")
public class BondChangeRecordsController extends BaseController {

    @Autowired
    BondChangeRecordsService bondChangeRecordsService;

    @ApiOperation(value = "分页查询调账日志")
    @ApiImplicitParam(name = "condition", value = "调账列表页查询条件", required = true, dataType = "BondChangeCondition", paramType = "body")
    @PostMapping(value = "/getBondChangeRecordsPage")
    public Result<Paging<BondChangeRecordsListVo>> getBondChangeRecordsPage(@RequestBody BondChangeCondition condition) {
        IPage<BondChangeRecordsListVo> page = bondChangeRecordsService.getBondChangeRecordsPage(condition);
        return Result.ok(Paging.buildPaging(page));
    }

    @ApiOperation(value = "查看调整记录")
    @ApiImplicitParam(name = "req", value = "查看调整记录条件", required = true, dataType = "BondChangeRecordReq", paramType = "body")
    @PostMapping(value = "/getChangeRecords")
    public Result<List<BondChangeRecordsVo>> getChangeRecords(@RequestBody BondChangeRecordReq req) {
        return Result.ok(bondChangeRecordsService.getChangeRecords(req.getSectionId(), req.getCompanyId()));
    }



}
