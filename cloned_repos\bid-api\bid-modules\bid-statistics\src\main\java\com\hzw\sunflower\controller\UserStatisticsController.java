package com.hzw.sunflower.controller;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.controller.request.UserStatisticsReq;
import com.hzw.sunflower.controller.response.CompanyStatisticsVO;
import com.hzw.sunflower.controller.response.DepartmentStatisticsVO;
import com.hzw.sunflower.controller.response.DeptStatisticsVO;
import com.hzw.sunflower.controller.response.UserStatisticsVO;
import com.hzw.sunflower.service.UserStatisticsService;
import com.hzw.sunflower.util.BeanListUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.text.DateFormat;
import java.text.NumberFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;



@Api(tags = "统计")
@RestController
@RequestMapping("/statistics")
public class UserStatisticsController {

    @Autowired
    private UserStatisticsService userStatisticsService;
    @ApiOperation(value = "导出excel")
    @ApiImplicitParam(name = "req", value = "查询条件", required = true, dataType = "UserStatisticsReq", paramType = "body")
    @PostMapping("/exportExcel")
    public  void  exportExcel(HttpServletResponse response, @RequestBody UserStatisticsReq userStatisticsReq) {
        List<UserStatisticsVO> userStatisticsVOList=new ArrayList<>();
        List<CompanyStatisticsVO> companyStatisticsVOList=new ArrayList<>();
        List<DeptStatisticsVO> deptStatisticsVOList=new ArrayList<>();
        if(userStatisticsReq.getKeyWords().equals("1")){
            CompanyStatisticsVO userStatisticsVO=new CompanyStatisticsVO();
            userStatisticsVOList=userStatisticsService.selectCompanyStatistics(userStatisticsReq);
            Long projectCount=0L;
            Long noProjectCount=0L;
            BigDecimal entrustedAmount=new BigDecimal(0);
            BigDecimal noEntrustedAmount=new BigDecimal(0);
            BigDecimal winPrice=new BigDecimal(0);
            BigDecimal account=new BigDecimal(0);
            BigDecimal estimateAccount=new BigDecimal(0);
            BigDecimal noAccount=new BigDecimal(0);
            Long projectCountAgo=0L;
            Long noProjectCountAgo=0L;
            BigDecimal entrustedAmountAgo=new BigDecimal(0);
            BigDecimal noEntrustedAmountAgo=new BigDecimal(0);
            BigDecimal winPriceAgo=new BigDecimal(0);
            BigDecimal accountAgo=new BigDecimal(0);
            BigDecimal estimateAccountAgo=new BigDecimal(0);
            BigDecimal noAccountAgo=new BigDecimal(0);
            Long projectCountMinus=0L;
            Long noProjectCountMinus=0L;
            BigDecimal entrustedAmountMinus=new BigDecimal(0);
            BigDecimal noEntrustedAmountMinus=new BigDecimal(0);
            BigDecimal winPriceMinus=new BigDecimal(0);
            BigDecimal accountMinus=new BigDecimal(0);
            BigDecimal estimateAccountMinus=new BigDecimal(0);
            BigDecimal noAccountMinus=new BigDecimal(0);
            for (UserStatisticsVO statisticsVO : userStatisticsVOList) {
                projectCount +=statisticsVO.getProjectCount();
                noProjectCount +=statisticsVO.getNoProjectCount();
                entrustedAmount=entrustedAmount.add(statisticsVO.getEntrustedAmount());
                noEntrustedAmount =noEntrustedAmount.add(statisticsVO.getNoEntrustedAmount());
                winPrice= winPrice.add(statisticsVO.getWinPrice());
                account=account.add(statisticsVO.getAccount());
                estimateAccount=estimateAccount.add(statisticsVO.getEstimateAccount());
                noAccount=noAccount.add(statisticsVO.getNoAccount());

                projectCountAgo +=statisticsVO.getProjectCountAgo();
                noProjectCountAgo +=statisticsVO.getNoProjectCountAgo();
                entrustedAmountAgo=entrustedAmountAgo.add(statisticsVO.getEntrustedAmountAgo());
                noEntrustedAmountAgo=noEntrustedAmountAgo.add(statisticsVO.getNoEntrustedAmountAgo());
                winPriceAgo=winPriceAgo.add(statisticsVO.getWinPriceAgo());
                accountAgo=accountAgo.add(statisticsVO.getAccountAgo());
                estimateAccountAgo=estimateAccountAgo.add(statisticsVO.getEstimateAccountAgo());
                noAccountAgo=noAccountAgo.add(statisticsVO.getNoAccountAgo());

                projectCountMinus +=statisticsVO.getProjectCountMinus();
                noProjectCountMinus +=statisticsVO.getNoProjectCountMinus();
                entrustedAmountMinus=entrustedAmountMinus.add(statisticsVO.getEntrustedAmountMinus());
                noEntrustedAmountMinus=noEntrustedAmountMinus.add(statisticsVO.getNoEntrustedAmountMinus());
                winPriceMinus=winPriceMinus.add(statisticsVO.getWinPriceMinus());
                accountMinus=accountMinus.add(statisticsVO.getAccountMinus());
                estimateAccountMinus=estimateAccountMinus.add(statisticsVO.getEstimateAccountMinus());
                noAccountMinus=noAccountMinus.add(statisticsVO.getNoAccountMinus());
            }
            //公司今年的累计
            userStatisticsVO.setMon("累计");
            userStatisticsVO.setProjectCount(projectCount);
            userStatisticsVO.setNoProjectCount(noProjectCount);
            userStatisticsVO.setEntrustedAmount(entrustedAmount);
            userStatisticsVO.setNoEntrustedAmount(noEntrustedAmount);
            userStatisticsVO.setWinPrice(winPrice);
            userStatisticsVO.setAccount(account);
            userStatisticsVO.setEstimateAccount(estimateAccount);
            userStatisticsVO.setNoAccount(noAccount);
            //公司去年的累计
            userStatisticsVO.setProjectCountAgo(projectCountAgo);
            userStatisticsVO.setNoProjectCountAgo(noProjectCountAgo);
            userStatisticsVO.setEntrustedAmountAgo(entrustedAmountAgo);
            userStatisticsVO.setNoEntrustedAmountAgo(noEntrustedAmountAgo);
            userStatisticsVO.setWinPriceAgo(winPriceAgo);
            userStatisticsVO.setAccountAgo(accountAgo);
            userStatisticsVO.setEstimateAccountAgo(estimateAccountAgo);
            userStatisticsVO.setNoAccountAgo(noAccountAgo);
            //公司增长数累计
            userStatisticsVO.setProjectCountMinus(projectCountMinus);
            userStatisticsVO.setNoProjectCountMinus(noProjectCountMinus);
            userStatisticsVO.setEntrustedAmountMinus(entrustedAmountMinus);
            userStatisticsVO.setNoEntrustedAmountMinus(noEntrustedAmountMinus);
            userStatisticsVO.setWinPriceMinus(winPriceMinus);
            userStatisticsVO.setAccountMinus(accountMinus);
            userStatisticsVO.setEstimateAccountMinus(estimateAccountMinus);
            userStatisticsVO.setNoAccountMinus(noAccountMinus);
//            //公司今年的累计
//            userStatisticsVO.setMon("累计");
//            userStatisticsVO.setProjectCount(userStatisticsReq.getSummary().get("projectCount").intValue());
//            userStatisticsVO.setNoProjectCount(userStatisticsReq.getSummary().get("noProjectCount").intValue());
//            userStatisticsVO.setEntrustedAmount(BigDecimal.valueOf(userStatisticsReq.getSummary().get("entrustedAmount")) );
//            userStatisticsVO.setNoEntrustedAmount(BigDecimal.valueOf(userStatisticsReq.getSummary().get("noEntrustedAmount")) );
//            userStatisticsVO.setWinPrice(BigDecimal.valueOf(userStatisticsReq.getSummary().get("winPrice")));
//            userStatisticsVO.setAccount(BigDecimal.valueOf(userStatisticsReq.getSummary().get("account").intValue()));
//            userStatisticsVO.setEstimateAccount(BigDecimal.valueOf(userStatisticsReq.getSummary().get("estimateAccount")) );
//            userStatisticsVO.setNoAccount(BigDecimal.valueOf(userStatisticsReq.getSummary().get("noAccount")) );
//            //公司去年的累计
//            userStatisticsVO.setProjectCountAgo(userStatisticsReq.getSummary().get("projectCountAgo").intValue());
//            userStatisticsVO.setNoProjectCountAgo(userStatisticsReq.getSummary().get("noProjectCountAgo").intValue());
//            userStatisticsVO.setEntrustedAmountAgo(BigDecimal.valueOf(userStatisticsReq.getSummary().get("entrustedAmountAgo")) );
//            userStatisticsVO.setNoEntrustedAmountAgo(BigDecimal.valueOf(userStatisticsReq.getSummary().get("noEntrustedAmountAgo")) );
//            userStatisticsVO.setWinPriceAgo(BigDecimal.valueOf(userStatisticsReq.getSummary().get("winPriceAgo")));
//            userStatisticsVO.setAccountAgo(BigDecimal.valueOf(userStatisticsReq.getSummary().get("accountAgo").intValue()));
//            userStatisticsVO.setEstimateAccountAgo(BigDecimal.valueOf(userStatisticsReq.getSummary().get("estimateAccountAgo")) );
//            userStatisticsVO.setNoAccountAgo(BigDecimal.valueOf(userStatisticsReq.getSummary().get("noAccountAgo")) );
//            //公司增长数累计
//            userStatisticsVO.setProjectCountMinus(userStatisticsReq.getSummary().get("projectCountMinus").intValue());
//            userStatisticsVO.setNoProjectCountMinus(userStatisticsReq.getSummary().get("noProjectCountMinus").intValue());
//            userStatisticsVO.setEntrustedAmountMinus(BigDecimal.valueOf(userStatisticsReq.getSummary().get("entrustedAmountMinus")) );
//            userStatisticsVO.setNoEntrustedAmountMinus(BigDecimal.valueOf(userStatisticsReq.getSummary().get("noEntrustedAmountMinus")) );
//            userStatisticsVO.setWinPriceMinus(BigDecimal.valueOf(userStatisticsReq.getSummary().get("winPriceMinus")));
//            userStatisticsVO.setAccountMinus(BigDecimal.valueOf(userStatisticsReq.getSummary().get("accountMinus").intValue()));
//            userStatisticsVO.setEstimateAccountMinus(BigDecimal.valueOf(userStatisticsReq.getSummary().get("estimateAccountMinus")) );
//            userStatisticsVO.setNoAccountMinus(BigDecimal.valueOf(userStatisticsReq.getSummary().get("noAccountMinus")) );
         //   userStatisticsVOList=userStatisticsService.selectCompanyStatistics(userStatisticsReq);
            companyStatisticsVOList=BeanListUtil.convertList(userStatisticsVOList,CompanyStatisticsVO.class);
            companyStatisticsVOList.add(userStatisticsVO);
        }else if(userStatisticsReq.getKeyWords().equals("2")){
            DeptStatisticsVO userStatisticsVO=new DeptStatisticsVO();
//            userStatisticsVO.setDepartmentName("累计");
//            //部门今年的累计
//            userStatisticsVO.setProjectCount(userStatisticsReq.getSummary().get("projectCount").intValue());
//            userStatisticsVO.setNoProjectCount(userStatisticsReq.getSummary().get("noProjectCount").intValue());
//            userStatisticsVO.setEntrustedAmount(BigDecimal.valueOf(userStatisticsReq.getSummary().get("entrustedAmount")) );
//            userStatisticsVO.setNoEntrustedAmount(BigDecimal.valueOf(userStatisticsReq.getSummary().get("noEntrustedAmount")) );
//            userStatisticsVO.setWinPrice(BigDecimal.valueOf(userStatisticsReq.getSummary().get("winPrice")));
//            userStatisticsVO.setAccount(BigDecimal.valueOf(userStatisticsReq.getSummary().get("account").intValue()));
//            userStatisticsVO.setEstimateAccount(BigDecimal.valueOf(userStatisticsReq.getSummary().get("estimateAccount")) );
//            userStatisticsVO.setNoAccount(BigDecimal.valueOf(userStatisticsReq.getSummary().get("noAccount")) );
//            //部门去年的累计
//            userStatisticsVO.setProjectCountAgo(userStatisticsReq.getSummary().get("projectCountAgo").intValue());
//            userStatisticsVO.setNoProjectCountAgo(userStatisticsReq.getSummary().get("noProjectCountAgo").intValue());
//            userStatisticsVO.setEntrustedAmountAgo(BigDecimal.valueOf(userStatisticsReq.getSummary().get("entrustedAmountAgo")) );
//            userStatisticsVO.setNoEntrustedAmountAgo(BigDecimal.valueOf(userStatisticsReq.getSummary().get("noEntrustedAmountAgo")) );
//            userStatisticsVO.setWinPriceAgo(BigDecimal.valueOf(userStatisticsReq.getSummary().get("winPriceAgo")));
//            userStatisticsVO.setAccountAgo(BigDecimal.valueOf(userStatisticsReq.getSummary().get("accountAgo").intValue()));
//            userStatisticsVO.setEstimateAccountAgo(BigDecimal.valueOf(userStatisticsReq.getSummary().get("estimateAccountAgo")) );
//            userStatisticsVO.setNoAccountAgo(BigDecimal.valueOf(userStatisticsReq.getSummary().get("noAccountAgo")) );
//            //部门增长数的累计
//            userStatisticsVO.setProjectCountMinus(userStatisticsReq.getSummary().get("projectCountMinus").intValue());
//            userStatisticsVO.setNoProjectCountMinus(userStatisticsReq.getSummary().get("noProjectCountMinus").intValue());
//            userStatisticsVO.setEntrustedAmountMinus(BigDecimal.valueOf(userStatisticsReq.getSummary().get("entrustedAmountMinus")) );
//            userStatisticsVO.setNoEntrustedAmountMinus(BigDecimal.valueOf(userStatisticsReq.getSummary().get("noEntrustedAmountMinus")) );
//            userStatisticsVO.setWinPriceMinus(BigDecimal.valueOf(userStatisticsReq.getSummary().get("winPriceMinus")));
//            userStatisticsVO.setAccountMinus(BigDecimal.valueOf(userStatisticsReq.getSummary().get("accountMinus").intValue()));
//            userStatisticsVO.setEstimateAccountMinus(BigDecimal.valueOf(userStatisticsReq.getSummary().get("estimateAccountMinus")) );
//            userStatisticsVO.setNoAccountMinus(BigDecimal.valueOf(userStatisticsReq.getSummary().get("noAccountMinus")) );
            userStatisticsVOList=userStatisticsService.selectDepartmentStatistics(userStatisticsReq);

            Long projectCount=0L;
            Long noProjectCount=0L;
            BigDecimal entrustedAmount=new BigDecimal(0);
            BigDecimal noEntrustedAmount=new BigDecimal(0);
            BigDecimal winPrice=new BigDecimal(0);
            BigDecimal account=new BigDecimal(0);
            BigDecimal estimateAccount=new BigDecimal(0);
            BigDecimal noAccount=new BigDecimal(0);
            Long projectCountAgo=0L;
            Long noProjectCountAgo=0L;
            BigDecimal entrustedAmountAgo=new BigDecimal(0);
            BigDecimal noEntrustedAmountAgo=new BigDecimal(0);
            BigDecimal winPriceAgo=new BigDecimal(0);
            BigDecimal accountAgo=new BigDecimal(0);
            BigDecimal estimateAccountAgo=new BigDecimal(0);
            BigDecimal noAccountAgo=new BigDecimal(0);
            Long projectCountMinus=0L;
            Long noProjectCountMinus=0L;
            BigDecimal entrustedAmountMinus=new BigDecimal(0);
            BigDecimal noEntrustedAmountMinus=new BigDecimal(0);
            BigDecimal winPriceMinus=new BigDecimal(0);
            BigDecimal accountMinus=new BigDecimal(0);
            BigDecimal estimateAccountMinus=new BigDecimal(0);
            BigDecimal noAccountMinus=new BigDecimal(0);
            for (UserStatisticsVO statisticsVO : userStatisticsVOList) {
                projectCount +=statisticsVO.getProjectCount();
                noProjectCount +=statisticsVO.getNoProjectCount();
                entrustedAmount=entrustedAmount.add(statisticsVO.getEntrustedAmount());
                noEntrustedAmount =noEntrustedAmount.add(statisticsVO.getNoEntrustedAmount());
                winPrice= winPrice.add(statisticsVO.getWinPrice());
                account=account.add(statisticsVO.getAccount());
                estimateAccount=estimateAccount.add(statisticsVO.getEstimateAccount());
                noAccount=noAccount.add(statisticsVO.getNoAccount());

                projectCountAgo +=statisticsVO.getProjectCountAgo();
                noProjectCountAgo +=statisticsVO.getNoProjectCountAgo();
                entrustedAmountAgo=entrustedAmountAgo.add(statisticsVO.getEntrustedAmountAgo());
                noEntrustedAmountAgo=noEntrustedAmountAgo.add(statisticsVO.getNoEntrustedAmountAgo());
                winPriceAgo=winPriceAgo.add(statisticsVO.getWinPriceAgo());
                accountAgo=accountAgo.add(statisticsVO.getAccountAgo());
                estimateAccountAgo=estimateAccountAgo.add(statisticsVO.getEstimateAccountAgo());
                noAccountAgo=noAccountAgo.add(statisticsVO.getNoAccountAgo());

                projectCountMinus +=statisticsVO.getProjectCountMinus();
                noProjectCountMinus +=statisticsVO.getNoProjectCountMinus();
                entrustedAmountMinus=entrustedAmountMinus.add(statisticsVO.getEntrustedAmountMinus());
                noEntrustedAmountMinus=noEntrustedAmountMinus.add(statisticsVO.getNoEntrustedAmountMinus());
                winPriceMinus=winPriceMinus.add(statisticsVO.getWinPriceMinus());
                accountMinus=accountMinus.add(statisticsVO.getAccountMinus());
                estimateAccountMinus=estimateAccountMinus.add(statisticsVO.getEstimateAccountMinus());
                noAccountMinus=noAccountMinus.add(statisticsVO.getNoAccountMinus());
            }
            //公司今年的累计
            userStatisticsVO.setDepartmentName("累计");
            userStatisticsVO.setProjectCount(projectCount);
            userStatisticsVO.setNoProjectCount(noProjectCount);
            userStatisticsVO.setEntrustedAmount(entrustedAmount );
            userStatisticsVO.setNoEntrustedAmount(noEntrustedAmount );
            userStatisticsVO.setWinPrice(winPrice);
            userStatisticsVO.setAccount(account);
            userStatisticsVO.setEstimateAccount(estimateAccount);
            userStatisticsVO.setNoAccount(noAccount);
            //公司去年的累计
            userStatisticsVO.setProjectCountAgo(projectCountAgo);
            userStatisticsVO.setNoProjectCountAgo(noProjectCountAgo);
            userStatisticsVO.setEntrustedAmountAgo(entrustedAmountAgo);
            userStatisticsVO.setNoEntrustedAmountAgo(noEntrustedAmountAgo);
            userStatisticsVO.setWinPriceAgo(winPriceAgo);
            userStatisticsVO.setAccountAgo(accountAgo);
            userStatisticsVO.setEstimateAccountAgo(estimateAccountAgo);
            userStatisticsVO.setNoAccountAgo(noAccountAgo);
            //公司增长数累计
            userStatisticsVO.setProjectCountMinus(projectCountMinus);
            userStatisticsVO.setNoProjectCountMinus(noProjectCountMinus);
            userStatisticsVO.setEntrustedAmountMinus(entrustedAmountMinus);
            userStatisticsVO.setNoEntrustedAmountMinus(noEntrustedAmountMinus);
            userStatisticsVO.setWinPriceMinus(winPriceMinus);
            userStatisticsVO.setAccountMinus(accountMinus);
            userStatisticsVO.setEstimateAccountMinus(estimateAccountMinus);
            userStatisticsVO.setNoAccountMinus(noAccountMinus);

            deptStatisticsVOList=BeanListUtil.convertList(userStatisticsVOList,DeptStatisticsVO.class);
            deptStatisticsVOList.add(userStatisticsVO);
        }else {
            UserStatisticsVO userStatisticsVO=new UserStatisticsVO();
//            userStatisticsVO.setUserName("累计");
//            //项目经理今年累计
//            userStatisticsVO.setProjectCount(userStatisticsReq.getSummary().get("projectCount").intValue());
//            userStatisticsVO.setNoProjectCount(userStatisticsReq.getSummary().get("noProjectCount").intValue());
//            userStatisticsVO.setEntrustedAmount(BigDecimal.valueOf(userStatisticsReq.getSummary().get("entrustedAmount")) );
//            userStatisticsVO.setNoEntrustedAmount(BigDecimal.valueOf(userStatisticsReq.getSummary().get("noEntrustedAmount")) );
//            userStatisticsVO.setWinPrice(BigDecimal.valueOf(userStatisticsReq.getSummary().get("winPrice")));
//            userStatisticsVO.setAccount(BigDecimal.valueOf(userStatisticsReq.getSummary().get("account").intValue()));
//            userStatisticsVO.setEstimateAccount(BigDecimal.valueOf(userStatisticsReq.getSummary().get("estimateAccount")) );
//            userStatisticsVO.setNoAccount(BigDecimal.valueOf(userStatisticsReq.getSummary().get("noAccount")) );
//            //项目经理去年累计
//            userStatisticsVO.setProjectCountAgo(userStatisticsReq.getSummary().get("projectCountAgo").intValue());
//            userStatisticsVO.setNoProjectCountAgo(userStatisticsReq.getSummary().get("noProjectCountAgo").intValue());
//            userStatisticsVO.setEntrustedAmountAgo(BigDecimal.valueOf(userStatisticsReq.getSummary().get("entrustedAmountAgo")) );
//            userStatisticsVO.setNoEntrustedAmountAgo(BigDecimal.valueOf(userStatisticsReq.getSummary().get("noEntrustedAmountAgo")) );
//            userStatisticsVO.setWinPriceAgo(BigDecimal.valueOf(userStatisticsReq.getSummary().get("winPriceAgo")));
//            userStatisticsVO.setAccountAgo(BigDecimal.valueOf(userStatisticsReq.getSummary().get("accountAgo").intValue()));
//            userStatisticsVO.setEstimateAccountAgo(BigDecimal.valueOf(userStatisticsReq.getSummary().get("estimateAccountAgo")) );
//            userStatisticsVO.setNoAccountAgo(BigDecimal.valueOf(userStatisticsReq.getSummary().get("noAccountAgo")) );
//            //项目经理增长数累计
//            userStatisticsVO.setProjectCountMinus(userStatisticsReq.getSummary().get("projectCountMinus").intValue());
//            userStatisticsVO.setNoProjectCountMinus(userStatisticsReq.getSummary().get("noProjectCountMinus").intValue());
//            userStatisticsVO.setEntrustedAmountMinus(BigDecimal.valueOf(userStatisticsReq.getSummary().get("entrustedAmountMinus")) );
//            userStatisticsVO.setNoEntrustedAmountMinus(BigDecimal.valueOf(userStatisticsReq.getSummary().get("noEntrustedAmountMinus")) );
//            userStatisticsVO.setWinPriceMinus(BigDecimal.valueOf(userStatisticsReq.getSummary().get("winPriceMinus")));
//            userStatisticsVO.setAccountMinus(BigDecimal.valueOf(userStatisticsReq.getSummary().get("accountMinus").intValue()));
//            userStatisticsVO.setEstimateAccountMinus(BigDecimal.valueOf(userStatisticsReq.getSummary().get("estimateAccountMinus")) );
//            userStatisticsVO.setNoAccountMinus(BigDecimal.valueOf(userStatisticsReq.getSummary().get("noAccountMinus")) );
            userStatisticsVOList=userStatisticsService.selectUserStatistics(userStatisticsReq);

            Long projectCount=0L;
            Long noProjectCount=0L;
            BigDecimal entrustedAmount=new BigDecimal(0);
            BigDecimal noEntrustedAmount=new BigDecimal(0);
            BigDecimal winPrice=new BigDecimal(0);
            BigDecimal account=new BigDecimal(0);
            BigDecimal estimateAccount=new BigDecimal(0);
            BigDecimal noAccount=new BigDecimal(0);
            Long projectCountAgo=0L;
            Long noProjectCountAgo=0L;
            BigDecimal entrustedAmountAgo=new BigDecimal(0);
            BigDecimal noEntrustedAmountAgo=new BigDecimal(0);
            BigDecimal winPriceAgo=new BigDecimal(0);
            BigDecimal accountAgo=new BigDecimal(0);
            BigDecimal estimateAccountAgo=new BigDecimal(0);
            BigDecimal noAccountAgo=new BigDecimal(0);
            Long projectCountMinus=0L;
            Long noProjectCountMinus=0L;
            BigDecimal entrustedAmountMinus=new BigDecimal(0);
            BigDecimal noEntrustedAmountMinus=new BigDecimal(0);
            BigDecimal winPriceMinus=new BigDecimal(0);
            BigDecimal accountMinus=new BigDecimal(0);
            BigDecimal estimateAccountMinus=new BigDecimal(0);
            BigDecimal noAccountMinus=new BigDecimal(0);
            for (UserStatisticsVO statisticsVO : userStatisticsVOList) {
                projectCount +=statisticsVO.getProjectCount();
                noProjectCount +=statisticsVO.getNoProjectCount();
                entrustedAmount=entrustedAmount.add(statisticsVO.getEntrustedAmount());
                noEntrustedAmount =noEntrustedAmount.add(statisticsVO.getNoEntrustedAmount());
                winPrice= winPrice.add(statisticsVO.getWinPrice());
                account=account.add(statisticsVO.getAccount());
                estimateAccount=estimateAccount.add(statisticsVO.getEstimateAccount());
                noAccount=noAccount.add(statisticsVO.getNoAccount());

                projectCountAgo +=statisticsVO.getProjectCountAgo();
                noProjectCountAgo +=statisticsVO.getNoProjectCountAgo();
                entrustedAmountAgo=entrustedAmountAgo.add(statisticsVO.getEntrustedAmountAgo());
                noEntrustedAmountAgo=noEntrustedAmountAgo.add(statisticsVO.getNoEntrustedAmountAgo());
                winPriceAgo=winPriceAgo.add(statisticsVO.getWinPriceAgo());
                accountAgo=accountAgo.add(statisticsVO.getAccountAgo());
                estimateAccountAgo=estimateAccountAgo.add(statisticsVO.getEstimateAccountAgo());
                noAccountAgo=noAccountAgo.add(statisticsVO.getNoAccountAgo());

                projectCountMinus +=statisticsVO.getProjectCountMinus();
                noProjectCountMinus +=statisticsVO.getNoProjectCountMinus();
                entrustedAmountMinus=entrustedAmountMinus.add(statisticsVO.getEntrustedAmountMinus());
                noEntrustedAmountMinus=noEntrustedAmountMinus.add(statisticsVO.getNoEntrustedAmountMinus());
                winPriceMinus=winPriceMinus.add(statisticsVO.getWinPriceMinus());
                accountMinus=accountMinus.add(statisticsVO.getAccountMinus());
                estimateAccountMinus=estimateAccountMinus.add(statisticsVO.getEstimateAccountMinus());
                noAccountMinus=noAccountMinus.add(statisticsVO.getNoAccountMinus());
            }
            //公司今年的累计
            userStatisticsVO.setUserName("累计");
            userStatisticsVO.setProjectCount(projectCount);
            userStatisticsVO.setNoProjectCount(noProjectCount);
            userStatisticsVO.setEntrustedAmount(entrustedAmount);
            userStatisticsVO.setNoEntrustedAmount(noEntrustedAmount );
            userStatisticsVO.setWinPrice(winPrice);
            userStatisticsVO.setAccount(account);
            userStatisticsVO.setEstimateAccount(estimateAccount);
            userStatisticsVO.setNoAccount(noAccount);
            //公司去年的累计
            userStatisticsVO.setProjectCountAgo(projectCountAgo);
            userStatisticsVO.setNoProjectCountAgo(noProjectCountAgo);
            userStatisticsVO.setEntrustedAmountAgo(entrustedAmountAgo);
            userStatisticsVO.setNoEntrustedAmountAgo(noEntrustedAmountAgo);
            userStatisticsVO.setWinPriceAgo(winPriceAgo);
            userStatisticsVO.setAccountAgo(accountAgo);
            userStatisticsVO.setEstimateAccountAgo(estimateAccountAgo);
            userStatisticsVO.setNoAccountAgo(noAccountAgo);
            //公司增长数累计
            userStatisticsVO.setProjectCountMinus(projectCountMinus);
            userStatisticsVO.setNoProjectCountMinus(noProjectCountMinus);
            userStatisticsVO.setEntrustedAmountMinus(entrustedAmountMinus);
            userStatisticsVO.setNoEntrustedAmountMinus(noEntrustedAmountMinus);
            userStatisticsVO.setWinPriceMinus(winPriceMinus);
            userStatisticsVO.setAccountMinus(accountMinus);
            userStatisticsVO.setEstimateAccountMinus(estimateAccountMinus);
            userStatisticsVO.setNoAccountMinus(noAccountMinus);

            userStatisticsVOList.add(userStatisticsVO);
        }
        //设置标题信息
        Workbook workbook;
        String title;
        DateFormat sdf= new SimpleDateFormat("yyyy-MM");
        String startTime= null;
        String endTime= null;
        try {
            startTime = sdf.format(sdf.parse(userStatisticsReq.getStartTime()));
            endTime = sdf.format(sdf.parse(userStatisticsReq.getEndTime()));
        } catch (ParseException e) {
            e.printStackTrace();
        }

        title="经济指标对照总表"+ "("+startTime+"~"+endTime+")";
        if(userStatisticsReq.getKeyWords().equals("1")) {
             workbook = ExcelExportUtil.exportExcel(new ExportParams(title,"经济指标对照总表"), CompanyStatisticsVO.class, companyStatisticsVOList);
        }else if(userStatisticsReq.getKeyWords().equals("2")){
            workbook = ExcelExportUtil.exportExcel(new ExportParams(title,"经济指标对照总表"), DeptStatisticsVO.class, deptStatisticsVOList);
        }else {
            workbook = ExcelExportUtil.exportExcel(new ExportParams(title,"经济指标对照总表"), UserStatisticsVO.class, userStatisticsVOList);
        }
        try {
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("统计.xls", "UTF-8"));
            response.setContentType("application/vnd.ms-excel;charset=UTF-8");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        try {
            workbook.write(response.getOutputStream());
            response.getOutputStream().close();
            workbook.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    @ApiOperation(value = "导出excel")
    @ApiImplicitParam(name = "req", value = "查询条件", required = true, dataType = "UserStatisticsReq", paramType = "body")
    @PostMapping("/exportExcelDepartment")
    public  void  exportExcelDepartment(HttpServletResponse response, @RequestBody UserStatisticsReq userStatisticsReq) {
        List<DepartmentStatisticsVO> departmentStatisticsVOList=userStatisticsService.selectDepartmentStatisticsBasis(userStatisticsReq);
        for (int i = 0; i < departmentStatisticsVOList.size(); i++) {
            departmentStatisticsVOList.get(i).setOrdered(String.valueOf(i+1));
        }
        //加入同比数据
        if(userStatisticsReq.getSummary()!=null){
            DepartmentStatisticsVO departmentStatisticsVO=new DepartmentStatisticsVO();
//            //累计
//            departmentStatisticsVO.setOrdered("累计");
//            departmentStatisticsVO.setProjectCount(userStatisticsReq.getSummary().get("projectCount").intValue());
//            departmentStatisticsVO.setEntrustedAmount(BigDecimal.valueOf(userStatisticsReq.getSummary().get("entrustedAmount")) );
//
//            departmentStatisticsVO.setProjectCountAgo(userStatisticsReq.getSummary().get("projectCountAgo").intValue());
//            departmentStatisticsVO.setEntrustedAmountAgo(BigDecimal.valueOf(userStatisticsReq.getSummary().get("entrustedAmountAgo")) );
//            if(userStatisticsReq.getSummary().get("projectCountBasis")!=null) {
//                departmentStatisticsVO.setProjectCountBasis(userStatisticsReq.getSummary().get("projectCountBasis").doubleValue());
//            }
//            if(userStatisticsReq.getSummary().get("entrustedAmountBasis")!=null) {
//                departmentStatisticsVO.setEntrustedAmountBasis(BigDecimal.valueOf(userStatisticsReq.getSummary().get("entrustedAmountBasis")));
//            }
            Long projectCount=0L;
            Long projectCountAgo=0L;
            BigDecimal entrustedAmount=new BigDecimal(0);
            BigDecimal entrustedAmountAgo=new BigDecimal(0);
            for (DepartmentStatisticsVO statisticsVO : departmentStatisticsVOList) {
                projectCount += statisticsVO.getProjectCount();
                entrustedAmount=entrustedAmount.add(statisticsVO.getEntrustedAmount());
                projectCountAgo += statisticsVO.getProjectCountAgo();
                entrustedAmountAgo=entrustedAmountAgo.add(statisticsVO.getEntrustedAmountAgo());
            }

            departmentStatisticsVO.setProjectCount(projectCount);
            departmentStatisticsVO.setEntrustedAmount(entrustedAmount);

            departmentStatisticsVO.setProjectCountAgo(projectCountAgo);
            departmentStatisticsVO.setEntrustedAmountAgo(entrustedAmountAgo);
            departmentStatisticsVO.setOrdered("累计");
         //   if(userStatisticsReq.getSummary().get("projectCountBasis")!=null) {
        //        departmentStatisticsVO.setProjectCountBasis(Double.valueOf(userStatisticsReq.getSummary().get("projectCountBasis")));
            NumberFormat numberFormat = NumberFormat.getNumberInstance();
            numberFormat.setMaximumFractionDigits(2);
            //关闭分组，显示将不再以千位符分隔
            numberFormat.setGroupingUsed(false);
            if(projectCountAgo.equals(0L)){
                departmentStatisticsVO.setProjectCountBasis(null);
            }else {
                departmentStatisticsVO.setProjectCountBasis(Double.valueOf(numberFormat.format(Double.valueOf((projectCount - projectCountAgo) / projectCountAgo.doubleValue()) / 0.01)));
            }
                // }
           // if(userStatisticsReq.getSummary().get("entrustedAmountBasis")!=null) {
               // departmentStatisticsVO.setEntrustedAmountBasis(BigDecimal.valueOf(Double.valueOf(userStatisticsReq.getSummary().get("entrustedAmountBasis"))));
            BigDecimal bigDec = new BigDecimal(0);
            if(entrustedAmountAgo.compareTo(bigDec)!=0) {
                Double doub = ((entrustedAmount.subtract(entrustedAmountAgo)).divide(entrustedAmountAgo, 6, BigDecimal.ROUND_UP)).doubleValue() / 0.01;
                departmentStatisticsVO.setEntrustedAmountBasis(Double.valueOf(numberFormat.format(doub)));
            }else {
                departmentStatisticsVO.setEntrustedAmountBasis(null);
            }
           // }
            departmentStatisticsVOList.add(departmentStatisticsVO);
        }
        //设置标题信息
        Workbook workbook;
        String title;
        DateFormat sdf= new SimpleDateFormat("yyyy-MM");
        String startTime= null;
        String endTime= null;
        try {
            startTime = sdf.format(sdf.parse(userStatisticsReq.getStartTime()));
            endTime = sdf.format(sdf.parse(userStatisticsReq.getEndTime()));
        } catch (ParseException e) {
            e.printStackTrace();
        }

        title="增幅排名表"+ "("+startTime+"~"+endTime+")";
        workbook= ExcelExportUtil.exportExcel(new ExportParams(title,"增幅排名表"), DepartmentStatisticsVO.class,departmentStatisticsVOList);
        try {
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("统计.xls", "UTF-8"));
            response.setContentType("application/vnd.ms-excel;charset=UTF-8");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        try {
            workbook.write(response.getOutputStream());
            response.getOutputStream().close();
            workbook.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    @ApiOperation(value = "查询统计项目概况数据")
    @ApiImplicitParam(name = "req", value = "查询条件", required = true, dataType = "UserStatisticsReq", paramType = "body")
    @PostMapping("/queryProjectProfile")
    public Result<List<UserStatisticsVO>> queryProjectProfile(@RequestBody UserStatisticsReq userStatisticsReq) {
        List<UserStatisticsVO> userStatisticsVOList = new ArrayList<>();
        //公司
        if (userStatisticsReq.getKeyWords().equals("1")) {
            userStatisticsVOList = userStatisticsService.selectCompanyStatistics(userStatisticsReq);
        } else if (userStatisticsReq.getKeyWords().equals("2")) {
            //处室
            userStatisticsVOList = userStatisticsService.selectDepartmentStatistics(userStatisticsReq);
        } else {
            //人员
            userStatisticsVOList = userStatisticsService.selectUserStatistics(userStatisticsReq);
        }
        return Result.ok(userStatisticsVOList);
    }
    @ApiOperation(value = "查询统计处室排名数据")
    @ApiImplicitParam(name = "req", value = "查询条件", required = true, dataType = "UserStatisticsReq", paramType = "body")
    @PostMapping("/queryDepartmentRanking")
    public  Result<List<DepartmentStatisticsVO>>  queryDepartmentRanking(@RequestBody UserStatisticsReq userStatisticsReq) {
        List<DepartmentStatisticsVO> departmentStatisticsVOList=userStatisticsService.selectDepartmentStatisticsBasis(userStatisticsReq);
        return Result.ok(departmentStatisticsVOList);
    }
}
