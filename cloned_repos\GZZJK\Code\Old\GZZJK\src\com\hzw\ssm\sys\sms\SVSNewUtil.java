package com.hzw.ssm.sys.sms;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.HashMap;
import java.util.Map;

import net.sf.json.JSONObject;

import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.HttpClients;

import com.hzw.ssm.expert.entity.OutboundTTS;
import com.hzw.ssm.expert.entity.OutboundTTSOut;
import com.hzw.ssm.expert.entity.OutboundVoice;
import com.hzw.ssm.expert.entity.OutboundVoiceOut;
import com.hzw.ssm.expert.entity.QueryObVResultOut;

import flexjson.JSONDeserializer;
import flexjson.transformer.DateTransformer;



public class SVSNewUtil {
	//软件序列号
	private static String clientId = "20012XXX";
	// 密码
	private static String clientPass = "nj_sqj#es7XXX";
	
	private static SVSNewUtil svsNewUtil;

	public static SVSNewUtil getSVSNewUtil() {
		if (svsNewUtil == null) {
			svsNewUtil = new SVSNewUtil();
		}	
		return svsNewUtil;
	}
	

	/**
	 * 启用外呼接口
	 * @param mobiles
	 * @param content
	 * @param extNum
	 * @return
	 */
	public static OutboundVoiceOut sendVoice(String mobiles, String SynchroId)
	{
		OutboundVoiceOut out = new OutboundVoiceOut();
		try {
			
			//外呼接口
			OutboundVoice voice = new OutboundVoice();
			voice.setClient_id(clientId);
			voice.setClient_pass(clientPass);
			//接口超时时间
			voice.setOrg_timeout("30");
			//主显号码
			voice.setOrg_num("025-83323222");
			//TTS唯一标识
			voice.setSynchro_id(SynchroId);
			//是否重复
			voice.setIsFor("0");
			//拨通号码
			voice.setCall_num(mobiles);
			//每次循环播报间隔
			voice.setInterval("15");
			//通知类型A2按键反馈
			voice.setNoticeType("A2");
			
			// 创建HttpClient实例
			HttpClient client = HttpClients.createDefault();
			
			// 请求地址
			HttpPost httpPost = new HttpPost("http://202.102.40.184:21191/telecom-api/call/cServTts");
			

			// 封装请求参数 JSON格式
			JSONObject json = JSONObject.fromObject(voice);
			StringEntity entity = new StringEntity(json.toString(), "utf-8");// 解决中文乱码问题
			entity.setContentEncoding("UTF-8");
			entity.setContentType("application/json");
			httpPost.setEntity(entity);
			
			//发送请求
			HttpResponse response = client.execute(httpPost);
			HttpEntity httpentity = response.getEntity();
			String res = getResContent(httpentity.getContent());
			
			//获取请求结果
			@SuppressWarnings("unchecked")
			Map<String,Object> resultMap = new JSONDeserializer<Map>().use(null, HashMap.class)
		            .use(java.util.Date.class, new DateTransformer("yyyyMMddHHmmss")).deserialize(res);
			
			if(!"0".equals((String)resultMap.get("TSR_RESULT"))){
				out.setTSR_RESULT((String)resultMap.get("TSR_RESULT"));
				out.setTSR_MSG_ERR((String)resultMap.get("TSR_MSG"));
				
			}else{
				out = new JSONDeserializer<OutboundVoiceOut>().use(null, OutboundVoiceOut.class)
			            .use(java.util.Date.class, new DateTransformer("yyyyMMddHHmmss")).deserialize(res);
			}
			
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		
		return out;
	}
	
	
	/**
	 * tts内容同步接口
	 * @param content
	 * @return
	 */
	public static OutboundTTSOut sendVoiceTTS(String content,String synchroId)
	{
		OutboundTTSOut out = null;
		try {
			//1.tts同步接口
			OutboundTTS ttsVoice = new OutboundTTS();
			ttsVoice.setClient_id(clientId);
			ttsVoice.setClient_pass(clientPass);
			//语音播报内容
			ttsVoice.setTts_content(content);
			//唯一标识
			ttsVoice.setSynchro_id(synchroId);
			

			// 创建HttpClient实例
			HttpClient client = HttpClients.createDefault();

			// 请求地址
			HttpPost httpPost = new HttpPost("http://202.102.40.184:21191/telecom-api/call/cSyncroTts");

			// 封装请求参数 JSON格式
			JSONObject json = JSONObject.fromObject(ttsVoice);
			StringEntity entity = new StringEntity(json.toString(), "utf-8");// 解决中文乱码问题
			entity.setContentEncoding("UTF-8");
			entity.setContentType("application/json");
			httpPost.setEntity(entity);
			
			//发送请求
			HttpResponse response = client.execute(httpPost);
			HttpEntity httpentity = response.getEntity();
			String res = getResContent(httpentity.getContent());
			
			out = new JSONDeserializer<OutboundTTSOut>().use(null, OutboundTTSOut.class)
            .use(java.util.Date.class, new DateTransformer("yyyyMMddHHmmss")).deserialize(res);
			
		} catch (Exception e) {
			e.printStackTrace();
		}
		return out;
	}
	
	/**
	 * 外呼结果查询接口
	 * @param content
	 * @return
	 */
	public static QueryObVResultOut queryVoiceResult(String logId)
	{
		QueryObVResultOut out = null;
		try {
			//外呼接口
			OutboundVoice voice = new OutboundVoice();
			voice.setClient_id(clientId);
			voice.setClient_pass(clientPass);
			voice.setLogId(logId);
			
			
			// 创建HttpClient实例
			HttpClient client = HttpClients.createDefault();
			
			// 请求地址
			HttpPost httpPost = new HttpPost("http://202.102.40.184:21191/telecom-api/call/cQueryResult");
			

			// 封装请求参数 JSON格式
			JSONObject json = JSONObject.fromObject(voice);
			StringEntity entity = new StringEntity(json.toString(), "utf-8");// 解决中文乱码问题
			entity.setContentEncoding("UTF-8");
			entity.setContentType("application/json");
			httpPost.setEntity(entity);
			
			//发送请求
			HttpResponse response = client.execute(httpPost);
			HttpEntity httpentity = response.getEntity();
			String res = getResContent(httpentity.getContent());
			
			
			//获取请求结果
			@SuppressWarnings("unchecked")
			Map<String,Object> resultMap = new JSONDeserializer<Map>().use(null, HashMap.class)
		            .use(java.util.Date.class, new DateTransformer("yyyyMMddHHmmss")).deserialize(res);
			
			if(!"0".equals((String)resultMap.get("TSR_RESULT"))){
				out.setTSR_RESULT((String)resultMap.get("TSR_RESULT"));
				out.setTSR_MSG_ERR((String)resultMap.get("TSR_MSG"));
				
			}else{
				out = new JSONDeserializer<QueryObVResultOut>().use(null, QueryObVResultOut.class)
			            .use(java.util.Date.class, new DateTransformer("yyyyMMddHHmmss")).deserialize(res);
				
			}
			
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return out;
	}
	
	
	private static String getResContent(InputStream inputStream) throws Exception{
		BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, "UTF-8"));
        char[] buf = new char[512];
        int readCount = reader.read(buf, 0, 512);
        StringBuffer sb = new StringBuffer();
        while (readCount >= 0) {
            sb.append(buf, 0, readCount);
            readCount = reader.read(buf, 0, 512);
        }
        
        return sb.toString();
	}
}

