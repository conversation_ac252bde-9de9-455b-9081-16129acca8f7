package com.hzw.sunflower.service.impl;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzw.sunflower.client.WorkFlowApiClient;
import com.hzw.sunflower.common.Paging;
import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.common.redis.RedisCache;
import com.hzw.sunflower.constant.CommonConstants;
import com.hzw.sunflower.constant.GeneralConstants;
import com.hzw.sunflower.constant.SealEnum;
import com.hzw.sunflower.constant.constantenum.*;
import com.hzw.sunflower.controller.request.*;
import com.hzw.sunflower.controller.response.*;
import com.hzw.sunflower.dao.SealApplicationMapper;
import com.hzw.sunflower.datascope.utils.DataScopeUtil;
import com.hzw.sunflower.dto.ProcessRecordDTO;
import com.hzw.sunflower.dto.SealApplicationDTO;
import com.hzw.sunflower.dto.SealApplicationProjectDTO;
import com.hzw.sunflower.dto.SealFileDTO;
import com.hzw.sunflower.entity.*;
import com.hzw.sunflower.entity.condition.SealApplicationCondition;
import com.hzw.sunflower.freesia.client.constant.FlowClientConstant;
import com.hzw.sunflower.freesia.client.vo.flowable.ReturnVo;
import com.hzw.sunflower.security.utils.SecurityUtils;
import com.hzw.sunflower.service.*;
import com.hzw.sunflower.util.BeanListUtil;
import com.hzw.sunflower.util.WorkFlowExceptionUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


/**
* SealApplicationService接口实现
*
* <AUTHOR>
* @version 1.0.0 2021-04-07
*/
@Service
public class SealApplicationServiceImpl extends ServiceImpl<SealApplicationMapper, SealApplication> implements SealApplicationService {


    @Autowired
    private SealApplicationProjectService applicationProjectService;

    @Autowired
    private SealFileService sealFileService;

    @Autowired
    private SealApplicationInfoService sealApplicationInfoService;

    @Autowired
    private SealApproveService sealApproveService;

    @Autowired
    private WorkFlowApiClient flowApiClient;

    @Autowired
    private ProcessRecordService processRecordService;

    @Resource
    private WorkflowCacheService workflowCacheService;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private PendingItemService pendingItemService;

    @Autowired
    private UserService userService;

    @Autowired
    private SealManagementService sealManagementService;

    @Autowired
    private SealBidWinService sealBidWinService;

    /**
     * 根据条件分页查询列表
     *
     * @param condition 查询条件
     * @param jwtUser
     * @return 分页数据
     */
    @Override
    public IPage<SealApplicationDTO> findInfoByCondition(SealApplicationCondition condition, JwtUser jwtUser) {
        String datascopesql1= DataScopeUtil.getDataScopeFilterDepartConferenceSql("t1","t1");
        IPage<SealApplicationDTO> page = condition.buildPage();
        page = this.baseMapper.listInfo(page, condition,null,null,datascopesql1);
        return page;
    }

    @Override
    public IPage<SealApplicationDTO> listOnly(SealApplicationCondition condition, JwtUser jwtUser) {
        //数据权限
        String datascopesql1= DataScopeUtil.getDataScopeFilterDepartConferenceSql("t1","t1");
        IPage<SealApplicationDTO> page = condition.buildPage();
        page = this.baseMapper.listInfo(page, condition,null,null,datascopesql1);
        return page;
    }

    @Override
    public Boolean checkSymbolOnly(String symbol, Long id) {
        // 根据文号进行查询
        LambdaQueryWrapper<SealApplication> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SealApplication::getSymbol, symbol);
        if(null != id){
            queryWrapper.ne(SealApplication::getId, id);
        }
        long count = this.count(queryWrapper);
        if(count > 0){
            return true;
        } else {
            return false;
        }
    }

    @Override
    public SealApplication getByNoticeId(Long id) {
        return this.baseMapper.getByNoticeId(id);
    }

    @Override
    public Boolean updateSealInfo(SealApplication byId1, Date sealTime) {
        boolean b = this.updateById(byId1);
        // 改为已处理
        SealApplicationInfo sai  = new SealApplicationInfo();
        sai.setProcessingProgress(SealEnum.PROCESSING_PROGRESS_YES.getType());
        sai.setSealedTime(sealTime);
        sai.setSealedUser(SecurityUtils.getJwtUser().getUserName());
        sai.setSealedUserId(SecurityUtils.getJwtUser().getUserId());
        LambdaQueryWrapper<SealApplicationInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SealApplicationInfo::getApplyId, byId1.getId())
                .eq(SealApplicationInfo::getSealType,SealtypeEnum.PHYSICAL_SEAL.getType());
        sealApplicationInfoService.update(sai,queryWrapper);
        //插入流程记录
        ProcessRecordDTO recordDTO = new ProcessRecordDTO();
        recordDTO.setBusinessCode("REPORTING_MATTER_INFO");
        recordDTO.setBusinessId(byId1.getId());
        recordDTO.setOperation("盖章");
        recordDTO.setRemark(byId1.getRemark());
        recordDTO.setOperatorId(SecurityUtils.getJwtUser().getUserId());
        recordDTO.setOperatorName(SecurityUtils.getJwtUser().getUserName());
        processRecordService.addProcessRecord(recordDTO,SecurityUtils.getJwtUser().getUserOtherId(),"");
        return b;
    }

    @Override
    public IPage<SealApplicationDTO> listProjectInfo( SealApplicationCondition condition) {
        IPage<SealApplicationDTO> page = condition.buildPage();
        page = this.baseMapper.listProjectInfo(page, condition);
        return page;
    }

    /**
    * 根据主键ID查询
    *
    * @param id 主键ID
    * @return
    */
    @Override
    public SealApplicationVO getInfoById(Long id) {
        SealApplication application = getById(id);
        if(BeanUtil.isNotEmpty(application)){
            SealApplicationVO vo = new SealApplicationVO();
            BeanUtil.copyProperties(application,vo);
            vo.setProjectDTOS(this.baseMapper.queryProjectInfo(id));
            vo.setFileDTOS(this.baseMapper.queryFileInfo(id));
            if(StringUtils.isNotBlank(application.getMasterSend())){
                List<Long> ids = new ArrayList<>();
                for (String s : application.getMasterSend().split(",")) {
                    ids.add(Long.parseLong(s));
                }
                String s = this.baseMapper.queryCompanyInfo(ids);
                if(StringUtils.isNotBlank(application.getMasterName())){
                    vo.setMasterName(application.getMasterName()+ "," + s);
                } else {
                    vo.setMasterName(s);
                }
            }
            if(StringUtils.isNotBlank(application.getCopySend())){
                List<Long> ids = new ArrayList<>();
                for (String s : application.getCopySend().split(",")) {
                    ids.add(Long.parseLong(s));
                }
                String s = this.baseMapper.queryCompanyInfo(ids);
                if(StringUtils.isNotBlank(application.getCopySendName())){
                    vo.setCopySendName(application.getCopySendName()+ "," + s);
                } else {
                    vo.setCopySendName(s);
                }
            }
            LambdaQueryWrapper<SealApplicationInfo> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(SealApplicationInfo::getApplyId,id);
            List<SealApplicationInfo> list = sealApplicationInfoService.list(queryWrapper);
            List<Integer> collect = list.stream().map(SealApplicationInfo::getProcessingProgress).collect(Collectors.toList());
            if(collect.contains(SealEnum.PROCESSING_PROGRESS_YES.getType())
            && SealApplicationEnum.AGREE.getType().equals(vo.getApproveStatus())
            ){
                vo.setApproveStatus(SealApplicationEnum.SEAL.getType());
            }
            return vo;
        } else {
            return null;
        }
    }

    /**
    * 新增
    *
    * @param sealApplication
    * @return 是否成功
    */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> addInfo(SealApplicationREQ sealApplication) {
        if(sealApplication.getSealType().contains(SealtypeEnum.ELECTRONIC_SEAL.getType().toString()) && CollectionUtil.isEmpty(sealApplication.getFileDTOS())){
            return Result.failed("请上传用印材料！");
        }
        if(StringUtils.isBlank(sealApplication.getSealType())){
            return Result.failed("请选择用印方式！");
        }
        if(StringUtils.isBlank(sealApplication.getApproveDept()) && SealApplicationEnum.REPORTING_MATTER.getType().equals(sealApplication.getType())){
            return Result.failed("请选择归口审批部门！");
        }
        if(SealApplicationEnum.DRAFT_TEXT.getType().equals(sealApplication.getType()) &&
                null != sealApplication.getProofCheckUser()
                && sealApplication.getApplyUser().equals(sealApplication.getProofCheckUser() )){
            return Result.failed("清样校对不能选择自己！");
        }
        SealApplication application = BeanUtil.copyProperties(sealApplication, SealApplication.class);
        application.setApproveStatus(SealApplicationEnum.TO_APPROVE.getType());
        boolean save = save(application);
        if(save){
            // 保存用印方式数据
            if(StringUtils.isNotBlank(sealApplication.getSealType())){
                String[] types = sealApplication.getSealType().split(",");
                List<SealApplicationInfo> infoList = new ArrayList<>();
                for (String type:types) {
                    SealApplicationInfo info = new SealApplicationInfo();
                    info.setApplyId(application.getId());
                    info.setSealType(Integer.parseInt(type));
                    infoList.add(info);
                }
                sealApplicationInfoService.saveBatch(infoList);
            }
            // 保存项目信息
            if(CollectionUtil.isNotEmpty(sealApplication.getProjectDTOS())){
                List<SealApplicationProjectDTO> projectDTOS = sealApplication.getProjectDTOS();
                List<SealApplicationProject> sealApplicationProjects = BeanListUtil.convertList(projectDTOS, SealApplicationProject.class);
                sealApplicationProjects.forEach(l->l.setApplyId(application.getId()));
                applicationProjectService.saveBatch(sealApplicationProjects);
            }
            // 保存用印材料
            if(CollectionUtil.isNotEmpty(sealApplication.getFileDTOS())){
                List<SealFileDTO> fileDTOS = sealApplication.getFileDTOS();
                List<SealFile> files = BeanListUtil.convertList(fileDTOS, SealFile.class);
                files.forEach(l->l.setApplyId(application.getId()));
                sealFileService.saveBatch(files);
            }
            if(StringUtils.isNotBlank(sealApplication.getApproveDept()) && SealApplicationEnum.REPORTING_MATTER.getType().equals(sealApplication.getType())){
                List<SealApprove> approves = new ArrayList<>();
                List<SealApplicationApproveEnum> list = SealApplicationApproveEnum.getDeptList();
                for (SealApplicationApproveEnum approveEnum : list) {
                    SealApprove approve = new SealApprove();
                    approve.setApproveType(1);
                    approve.setApplyId(application.getId());
                    approve.setApproveDept(approveEnum.getType());
                    approve.setFormkey(approveEnum.getDesc());
                    if(sealApplication.getApproveDept().contains(approveEnum.getType().toString())){
                        approve.setIsChoose(CommonConstants.YES);
                    }
                    approves.add(approve);
                }
                sealApproveService.saveBatch(approves);
            }
            // 开启审批
            openFlow(application);
        }
        return Result.okOrFailed(save);
    }

    /**
     * 开启审批流
     * @param application
     */
    public Boolean openFlow(SealApplication application) {
        JwtUser jwtUser = SecurityUtils.getJwtUser();
        Map<String, Object> variables = new HashMap<>();
        String str = null;
        String processDefinitionKey = null;
        // 呈报事项
        if(SealApplicationEnum.REPORTING_MATTER.getType().equals(application.getType())){
            if(!application.getApproveDept().contains(SealApplicationApproveEnum.BGS.getType().toString())){
                variables.put("bgs", 1);
            }
            if(!application.getApproveDept().contains(SealApplicationApproveEnum.YGC.getType().toString())){
                variables.put("ygc", 1);
            }
            if(!application.getApproveDept().contains(SealApplicationApproveEnum.CSC.getType().toString())){
                variables.put("csc", 1);
            }
            //FlowClientConstant.REPORTING_MATTER
            processDefinitionKey = FlowClientConstant.REPORTING_MATTER;
        }
        // 合同事项
        if(SealApplicationEnum.CONTRACTUAL_MATTERS.getType().equals(application.getType())){
            processDefinitionKey = FlowClientConstant.CONTRACTUAL_MATTERS;
        }
        //拟文
        if(SealApplicationEnum.DRAFT_TEXT.getType().equals(application.getType())){
            // 判断是否选择清样校对人员
            if(null != application.getProofCheckUser()){
                User user = userService.getById(application.getProofCheckUser());
                variables.put("user",user.getOtherUserId());
            } else {
                variables.put("user","");
            }
            if(SealApplicationInfoEnum.CLARIFY.getType().equals(application.getApplyItem())){
                // 澄清/修改
                processDefinitionKey = FlowClientConstant.DRAFT_TEXT_CLARIFY;
            } else if(SealApplicationInfoEnum.REPLY.getType().equals(application.getApplyItem())){
                // 异议回复
                processDefinitionKey = FlowClientConstant.DRAFT_TEXT_REPLY;
            } else {
                // 其他
                processDefinitionKey = FlowClientConstant.DRAFT_TEXT_OTHER;
            }
        }
        // 用印审批
        if(SealApplicationEnum.SEAL_APPROVAL.getType().equals(application.getType())){
            if(SealApplicationInfoEnum.TENDER_DOCUMENT.getType().equals(application.getApplyItem())){
                processDefinitionKey = FlowClientConstant.SEAL_APPROVAL_TENDER;
            } else if(SealApplicationInfoEnum.BID_REPORT.getType().equals(application.getApplyItem())){
                processDefinitionKey = FlowClientConstant.SEAL_APPROVAL_REPORT;
            } else {
                processDefinitionKey = FlowClientConstant.SEAL_APPROVAL_PURCHASING;
            }
        }
        str = flowApiClient.startProcessInstanceByKey(jwtUser.getUserOtherId(), processDefinitionKey, "用印申请", "t_seal_application:" + application.getId(), variables,null,false);
        ReturnVo returnVo = JSONObject.parseObject(str, ReturnVo.class);
        WorkFlowExceptionUtil.checkFlowResult(str);
        if ("end".equals(returnVo.getMsg())) {
            application.setApproveStatus(SealApplicationEnum.AGREE.getType());
        } else {
            application.setProcessinstanceId(returnVo.getData().toString());
        }
        //插入流程记录
        ProcessRecordDTO recordDTO = new ProcessRecordDTO();
        recordDTO.setBusinessCode(FlowClientConstant.REPORTING_MATTER);
        recordDTO.setBusinessId(application.getId());
        recordDTO.setOperation("提交");
        recordDTO.setOperatorId(jwtUser.getUserId());
        recordDTO.setOperatorName(jwtUser.getUserName());
        processRecordService.addProcessRecord(recordDTO,jwtUser.getUserOtherId(),str);
        updateById(application);
        return true;
    }

    @Override
    public Result<Paging<SealAppingTaskVO>> queryApproveList(AppingTaskREQ appingTaskREQ) {
        Integer type = null;
        if(StringUtils.isNotBlank(appingTaskREQ.getProcessDefinitionKey())){
            type = Integer.valueOf(appingTaskREQ.getProcessDefinitionKey());
        }
        // 获取代办列表
        if(StringUtils.isBlank(appingTaskREQ.getProcessDefinitionKey())){
            appingTaskREQ.setProcessDefinitionKey(
                    FlowClientConstant.REPORTING_MATTER + ","
                            + FlowClientConstant.CONTRACTUAL_MATTERS + ","
                            + FlowClientConstant.DRAFT_TEXT_CLARIFY + ","
                            + FlowClientConstant.DRAFT_TEXT_REPLY + ","
                            + FlowClientConstant.DRAFT_TEXT_OTHER + ","
                            + FlowClientConstant.SEAL_APPROVAL_TENDER + ","
                            + FlowClientConstant.SEAL_APPROVAL_REPORT + ","
                            + FlowClientConstant.SEAL_APPROVAL_PURCHASING + ","
                            + FlowClientConstant.WITHDRAW_SEAL_APPLICATION + ","
                            + FlowClientConstant.REPORTING_MATTER_ONE
            );
        } else if (appingTaskREQ.getProcessDefinitionKey().contains(SealApplicationEnum.REPORTING_MATTER.getType().toString())){
            // 呈报事项
            appingTaskREQ.setProcessDefinitionKey(
                    FlowClientConstant.REPORTING_MATTER + ","
                            + FlowClientConstant.REPORTING_MATTER_ONE
            );
        } else if (appingTaskREQ.getProcessDefinitionKey().contains(SealApplicationEnum.CONTRACTUAL_MATTERS.getType().toString())){
            // 合同事项
            appingTaskREQ.setProcessDefinitionKey(
                    FlowClientConstant.CONTRACTUAL_MATTERS + ","
                            + FlowClientConstant.REPORTING_MATTER_ONE
            );
        } else if (appingTaskREQ.getProcessDefinitionKey().contains(SealApplicationEnum.DRAFT_TEXT.getType().toString())){
            // 拟文
            appingTaskREQ.setProcessDefinitionKey(
                    FlowClientConstant.DRAFT_TEXT_CLARIFY + ","
                            + FlowClientConstant.DRAFT_TEXT_REPLY + ","
                            + FlowClientConstant.DRAFT_TEXT_OTHER + ","
                            + FlowClientConstant.REPORTING_MATTER_ONE
            );
        }else if (appingTaskREQ.getProcessDefinitionKey().contains(SealApplicationEnum.SEAL_APPROVAL.getType().toString())) {
            // 用印审批
            appingTaskREQ.setProcessDefinitionKey(
                    FlowClientConstant.SEAL_APPROVAL_TENDER + ","
                            + FlowClientConstant.SEAL_APPROVAL_REPORT + ","
                            + FlowClientConstant.SEAL_APPROVAL_PURCHASING + ","
            );
        }
        String userOtherId= SecurityUtils.getJwtUser().getUserOtherId();
        Paging<AppingTaskVO> paging = workflowCacheService.getPaddingRemoteList(appingTaskREQ,userOtherId);
        List<AppingTaskVO> allData = paging.getRecords();
        // 数据转换
        List<SealAppingTaskVO> sealAppingTaskVOS = queryInfo(allData);
        // 过滤数据
        Paging<SealAppingTaskVO> pageList = filterPageList(appingTaskREQ,sealAppingTaskVOS,type);
        return Result.ok(pageList);
    }

    /**
     * 审批
     * @param req
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> reviewSeal(SealApprovalREQ req) {
        JwtUser jwtUser = SecurityUtils.getJwtUser();
        SealApplication application = getById(req.getBusId());
        if (BeanUtil.isEmpty(application)){
            return Result.failed("数据不存在！");
        }
        if(!SealApplicationEnum.TO_APPROVE.getType().equals(application.getApproveStatus()) && !SealApplicationEnum.WITHDRAW_PENDING.getType().equals(application.getApproveStatus())){
            return Result.failed("数据已审核！");
        }
        String operation = "";
        if(req.getIsAgree()){
            operation = "同意";
        } else {
            operation = "退回";
        }
        String str = flowApiClient.review(SecurityUtils.getJwtUser().getUserOtherId(), req.getProcessInstanceId(), req.getTaskId(), req.getMessage(), req.getIsAgree());
        WorkFlowExceptionUtil.checkFlowResult(str);
        ReturnVo returnVo = JSONObject.parseObject(str, ReturnVo.class);
        if(SealApplicationEnum.TO_APPROVE.getType().equals(application.getApproveStatus())){
            // 同意
            if ( req.getIsAgree()){
                // 审核结束
                if ("end".equals(returnVo.getMsg()) ) {
                    application.setApproveStatus(SealApplicationEnum.AGREE.getType());
                    // 判断是否开启子流程
                    if(StringUtils.isNotBlank(req.getLeader())){
                        application.setApproveLeader(req.getLeader());
                        String str1 = openSecondFlow(application,req.getLeader());
                        str = StringUtils.isNotBlank(str1)?str1:str;
                    }
                    if(SealApplicationEnum.REPORTING_MATTER.getType().equals(application.getType())){
                        LambdaUpdateWrapper<SealApprove> queryWrapper = new LambdaUpdateWrapper<>();
                        queryWrapper.eq(SealApprove::getApplyId,application.getId())
                                .eq(SealApprove::getIsChoose,CommonConstants.YES)
                                .eq(SealApprove::getIsApprove,CommonConstants.NO2);
                        queryWrapper.set(SealApprove::getIsApprove,CommonConstants.YES);
                        sealApproveService.update(queryWrapper);
                    }
                }
            }else {
                application.setApproveStatus(SealApplicationEnum.RETURN.getType());
                PendingItem pendingItem = new PendingItem();
                pendingItem.setBusinessCode(ReturnListEnum.SEAL_APPLICATION_RETURN.getCode());
                pendingItem.setBusinessId(application.getId());
                pendingItem.setOperationTime(new Date());
                pendingItem.setApplyTime(application.getApplyTime());
                pendingItem.setApplyUserId(application.getApplyUser());
                pendingItem.setBusinessType(2);
                pendingItemService.save(pendingItem);
            }

            if(SealApplicationEnum.REPORTING_MATTER.getType().equals(application.getType())){
                LambdaUpdateWrapper<SealApprove> queryWrapper = new LambdaUpdateWrapper<>();
                queryWrapper.eq(SealApprove::getApplyId,application.getId())
                        .eq(SealApprove::getIsChoose,CommonConstants.YES)
                        .eq(SealApprove::getIsApprove,CommonConstants.NO2)
                        .eq(SealApprove::getFormkey,req.getFormKey());
                queryWrapper.set(SealApprove::getIsApprove,CommonConstants.YES);
                sealApproveService.update(queryWrapper);
            }
        }
        if(SealApplicationEnum.WITHDRAW_PENDING.getType().equals(application.getApproveStatus())){
            // 同意
            if ( req.getIsAgree()){
                // 审核结束
                if ("end".equals(returnVo.getMsg()) ) {
                    application.setApproveStatus(SealApplicationEnum.WITHDRAW.getType());
                }
            }else {
                application.setApproveStatus(SealApplicationEnum.RETURN.getType());
                PendingItem pendingItem = new PendingItem();
                pendingItem.setBusinessCode(ReturnListEnum.SEAL_APPLICATION_RETURN.getCode());
                pendingItem.setBusinessId(application.getId());
                pendingItem.setOperationTime(new Date());
                pendingItem.setApplyTime(application.getApplyTime());
                pendingItem.setApplyUserId(application.getApplyUser());
                pendingItem.setBusinessType(2);
                pendingItemService.save(pendingItem);
            }
        }
        updateById(application);
        //插入流程记录
        ProcessRecordDTO recordDTO = new ProcessRecordDTO();
        recordDTO.setBusinessCode(FlowClientConstant.REPORTING_MATTER);
        recordDTO.setBusinessId(application.getId());
        recordDTO.setOperation(operation);
        recordDTO.setOperatorId(jwtUser.getUserId());
        recordDTO.setOperatorName(jwtUser.getUserName());
        recordDTO.setOperatorName(jwtUser.getUserName());
        recordDTO.setRemark(req.getMessage());
        recordDTO.setApprovalNode(req.getFormKey());
        processRecordService.addProcessRecord(recordDTO,jwtUser.getUserOtherId(),str);
        if(SealApplicationEnum.AGREE.getType().equals(application.getApproveStatus())){
            LambdaQueryWrapper<CalibrationProcessRecord> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(CalibrationProcessRecord::getBusinessId,application.getId())
                    .eq(CalibrationProcessRecord::getBusinessCode,FlowClientConstant.REPORTING_MATTER)
                    .orderByDesc(CalibrationProcessRecord::getCreatedTime)
                    .last("limit 1");
            CalibrationProcessRecord one = processRecordService.getOne(queryWrapper);
            one.setNextUserName(this.baseMapper.findName());
            processRecordService.updateById(one);
        }
        return Result.ok();
    }

    /**
     * 判断是否存在领导审批
     *
     * @param application
     * @param leader
     * @return
     */
    private String openSecondFlow(SealApplication application, String leader) {
        JwtUser jwtUser = SecurityUtils.getJwtUser();
        Map<String, Object> variables = new HashMap<>();
        //REPORTING_MATTER_ONE
        if(SealApplicationEnum.SEAL_APPROVAL.getType().equals(application.getType())){
            return null;
        } else {
            //if(StringUtils.isNotBlank(leader)){
            //    List<SealApprove> approves = new ArrayList<>();
            //    String[] approveDepts = leader.split(",");
            //    for (String dept:approveDepts) {
            //        SealApprove approve = new SealApprove();
            //        approve.setApplyId(application.getId());
            //        approve.setApproveDept(Integer.valueOf(dept));
            //        List<String> list = SealApplicationApproveEnum.getDeptList();
            //        if(list.contains(dept)){
            //            approve.setIsChoose(CommonConstants.YES);
            //        }
            //        approves.add(approve);
            //    }
            //    sealApproveService.saveBatch(approves);
            //}
            // 如果没有选择领导审批，则跳过
            if(!leader.contains(SealApplicationApproveEnum.WJZ.getType().toString())){
                variables.put("wjz", 1);
            }
            if(!leader.contains(SealApplicationApproveEnum.YFC.getType().toString())){
                variables.put("yfc", 1);
            }
            if(!leader.contains(SealApplicationApproveEnum.LW.getType().toString())){
                variables.put("lw", 1);
            }
            if(!leader.contains(SealApplicationApproveEnum.YRZ.getType().toString())){
                variables.put("yrz", 1);
            }
            if(!leader.contains(SealApplicationApproveEnum.SH.getType().toString())){
                variables.put("sh", 1);
            }
            String str = flowApiClient.startProcessInstanceByKey(jwtUser.getUserOtherId(), FlowClientConstant.REPORTING_MATTER_ONE, "用印申请", "t_seal_application:" + application.getId(), variables,null,false);
            ReturnVo returnVo = JSONObject.parseObject(str, ReturnVo.class);
            WorkFlowExceptionUtil.checkFlowResult(str);
            if ("end".equals(returnVo.getMsg())) {
                application.setApproveStatus(SealApplicationEnum.AGREE.getType());
            } else {
                application.setApproveStatus(SealApplicationEnum.TO_APPROVE.getType());
            }

            application.setProcessinstanceId(returnVo.getData().toString());
            return str;
        }

    }

    /**
     * 过滤数据
     *
     * @param req
     * @param sealAppingTaskVOS
     * @param type
     * @return
     */
    private Paging<SealAppingTaskVO> filterPageList(AppingTaskREQ req, List<SealAppingTaskVO> sealAppingTaskVOS, Integer type) {
        Paging<SealAppingTaskVO> paging = new Paging();
        paging.setPage(req.getPage().longValue());
        paging.setPageSize(req.getPageSize().longValue());
        List<SealAppingTaskVO> list = new ArrayList<>();
        int pageNum = req.getPage();
        int pageSize = req.getPageSize();
        int startRow = (pageNum - 1) * pageSize;
        long rowCount = 0L;
        Long deptId= SecurityUtils.getJwtUser().getUser().getDepartId();
        // 组装返回list数据
        for (SealAppingTaskVO taskVo : sealAppingTaskVOS) {
            // 数据是否符合过滤条件标志
            boolean f = false;
            // 根据查询条件过滤返回结果
            if(SealApplicationEnum.DRAFT_TEXT.getType().equals(taskVo.getType()) && null != deptId && !deptId.equals(taskVo.getProofCheckDept())){
                f = false;
            } else {
                if (StringUtils.isNotEmpty(req.getKeywords())) {
                    // 获取检索到的项目信息
                    if((null != taskVo.getProjectName() && taskVo.getProjectName().contains(req.getKeywords()))
                            || (null != taskVo.getProjectNum() && taskVo.getProjectNum().contains(req.getKeywords()))
                            || (null != taskVo.getItemDescribe() && taskVo.getItemDescribe().contains(req.getKeywords()))
                    ){
                        f = true;
                    }else{// 这条数据没有项目数据 那么不展示
                        f = false;
                    }
                    if (f && null != type) {
                        if(type.equals(taskVo.getType())){
                            f = true;
                        }else{
                            f = false;
                        }
                    }
                } else {
                    f = true;
                }
            }
            if (f) {
                rowCount++;
                if (rowCount >= startRow && list.size() < pageSize) {
                    list.add(taskVo);
                }
            }
        }
        // 设置总条数
        paging.setTotal(rowCount);
        // 设置返回记录
        paging.setRecords(list);
        return paging;
    }

    /**
     * 查询审批数据
     *
     * @param list
     * @param deptId
     * @return
     */
    private List<SealAppingTaskVO> queryInfo(List<AppingTaskVO> list) {
        List<SealAppingTaskVO> taskVOS = new ArrayList<>();
        for (AppingTaskVO appingTaskVO : list) {
            String businessKey = appingTaskVO.getBusinessKey();
            SealAppingTaskVO vo = redisCache.getCacheObject(businessKey);
            if (vo!=null){
            }else{
                vo = new SealAppingTaskVO();
                String strBusinessKey  = businessKey.substring(businessKey.indexOf(":") + 1);
                Long id= Long.parseLong(strBusinessKey);
                SealAppingTaskVO info = this.baseMapper.queryInfo(id);
                vo.setBusId(info.getBusId());
                vo.setProjectIds(info.getProjectIds());
                vo.setProjectNum(info.getProjectNum());
                vo.setProjectName(info.getProjectName());
                vo.setApplyUserName(info.getApplyUserName());
                vo.setApplyTime(info.getApplyTime());
                vo.setType(info.getType());
                vo.setApplyItem(info.getApplyItem());
                vo.setSealType(info.getSealType());
                vo.setDeptName(info.getDeptName());
                vo.setItemDescribe(info.getItemDescribe());
                vo.setProofCheckDept(info.getProofCheckDept());
                redisCache.setCacheObject(businessKey,vo, GeneralConstants.REDIS_CACHE_TIME_OUT, TimeUnit.MILLISECONDS);
                appingTaskVO.setProjectName(info.getProjectName());
            }
            vo.setBusinessKey(appingTaskVO.getBusinessKey());
            vo.setProcessInstanceId(appingTaskVO.getProcessInstanceId());
            vo.setBusinessKey(appingTaskVO.getBusinessKey());
            vo.setTaskId(appingTaskVO.getTaskId());
            //BeanUtils.copyProperties(appingTaskVO,vo);
            taskVOS.add(vo);
        }
        return taskVOS;
    }

    /**
    * 修改
    * @param sealApplication
    * @return 是否成功
    */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> updateInfo(SealApplicationREQ sealApplication) {
        SealApplication byId = getById(sealApplication.getId());
        if(BeanUtil.isNotEmpty(byId)
                && !SealApplicationEnum.RETURN.getType().equals(byId.getApproveStatus())
                && !SealApplicationEnum.WITHDRAW.getType().equals(byId.getApproveStatus())
        ){
            return Result.failed("数据状态错误！");
        }
        if(sealApplication.getSealType().contains(SealtypeEnum.ELECTRONIC_SEAL.getType().toString()) && CollectionUtil.isEmpty(sealApplication.getFileDTOS())){
            return Result.failed("请上传用印材料！");
        }
        if(StringUtils.isBlank(sealApplication.getSealType())){
            return Result.failed("请选择用印方式！");
        }
        if(StringUtils.isBlank(sealApplication.getApproveDept()) && SealApplicationEnum.REPORTING_MATTER.getType().equals(sealApplication.getType())){
            return Result.failed("请选择归口审批部门！");
        }
        if(SealApplicationEnum.DRAFT_TEXT.getType().equals(sealApplication.getType()) &&
                null != sealApplication.getProofCheckUser()
                && sealApplication.getApplyUser().equals(sealApplication.getProofCheckUser() )){
            return Result.failed("清样校对不能选择自己！");
        }
        SealApplication application = BeanUtil.copyProperties(sealApplication, SealApplication.class);
        application.setApproveStatus(SealApplicationEnum.TO_APPROVE.getType());
        boolean save = updateById(application);
        if(save){
            // 保存用印方式数据
            LambdaQueryWrapper<SealApplicationInfo> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(SealApplicationInfo::getApplyId,application.getId());
            sealApplicationInfoService.remove(queryWrapper);
            if(StringUtils.isNotBlank(sealApplication.getSealType())){
                String[] types = sealApplication.getSealType().split(",");
                List<SealApplicationInfo> infoList = new ArrayList<>();
                for (String type:types) {
                    SealApplicationInfo info = new SealApplicationInfo();
                    info.setApplyId(application.getId());
                    info.setSealType(Integer.parseInt(type));
                    infoList.add(info);
                }
                sealApplicationInfoService.saveBatch(infoList);
            }
            // 保存项目信息
            LambdaQueryWrapper<SealApplicationProject> projectQueryWrapper = new LambdaQueryWrapper<>();
            projectQueryWrapper.eq(SealApplicationProject::getApplyId,application.getId());
            applicationProjectService.remove(projectQueryWrapper);
            if(CollectionUtil.isNotEmpty(sealApplication.getProjectDTOS())){
                List<SealApplicationProjectDTO> projectDTOS = sealApplication.getProjectDTOS();
                List<SealApplicationProject> sealApplicationProjects = BeanListUtil.convertList(projectDTOS, SealApplicationProject.class);
                sealApplicationProjects.forEach(l->l.setApplyId(application.getId()));
                applicationProjectService.saveBatch(sealApplicationProjects);
            }
            // 保存用印材料
            LambdaQueryWrapper<SealFile> fileQueryWrapper = new LambdaQueryWrapper<>();
            fileQueryWrapper.eq(SealFile::getApplyId,application.getId());
            sealFileService.remove(fileQueryWrapper);
            if(CollectionUtil.isNotEmpty(sealApplication.getFileDTOS())){
                List<SealFileDTO> fileDTOS = sealApplication.getFileDTOS();
                List<SealFile> files = BeanListUtil.convertList(fileDTOS, SealFile.class);
                files.forEach(l->l.setApplyId(application.getId()));
                sealFileService.saveBatch(files);
            }
            if(StringUtils.isNotBlank(sealApplication.getApproveDept()) && SealApplicationEnum.REPORTING_MATTER.getType().equals(sealApplication.getType())){
                LambdaQueryWrapper<SealApprove> approveQueryWrapper = new LambdaQueryWrapper<>();
                approveQueryWrapper.eq(SealApprove::getApplyId,application.getId());
                sealApproveService.remove(approveQueryWrapper);
                List<SealApprove> approves = new ArrayList<>();
                List<SealApplicationApproveEnum> list = SealApplicationApproveEnum.getDeptList();
                for (SealApplicationApproveEnum approveEnum : list) {
                    SealApprove approve = new SealApprove();
                    approve.setApproveType(1);
                    approve.setApplyId(application.getId());
                    approve.setApproveDept(approveEnum.getType());
                    approve.setFormkey(approveEnum.getDesc());
                    if(sealApplication.getApproveDept().contains(approveEnum.getType().toString())){
                        approve.setIsChoose(CommonConstants.YES);
                    }
                    approves.add(approve);
                }
                sealApproveService.saveBatch(approves);
            }
            // 开启审批
            openFlow(application);
        }
        return Result.okOrFailed(save);
    }

    /**
     * 撤回
     * @param id
     * @return
     */
    @Override
    public Result<Boolean> withdrawApplication(Long id) {
        SealApplication byId = getById(id);
        JwtUser jwtUser = SecurityUtils.getJwtUser();
        // 待审批 -> 撤回
        if(BeanUtil.isNotEmpty(byId) && SealApplicationEnum.TO_APPROVE.getType().equals(byId.getApproveStatus())){
            byId.setApproveStatus(SealApplicationEnum.WITHDRAW.getType());
            // 撤回工作流
            String process = flowApiClient.revokeProcess(jwtUser.getUserOtherId(), "t_seal_application:" + id, "撤回");
            //判断推送到流程是否成功
            boolean b = WorkFlowExceptionUtil.checkFlowResult(process);
            if(b){
                //插入流程记录
                ProcessRecordDTO recordDTO = new ProcessRecordDTO();
                recordDTO.setBusinessId(id);
                recordDTO.setOperation("撤回");
                recordDTO.setOperatorId(jwtUser.getUserId());
                recordDTO.setOperatorName(jwtUser.getUserName());
                recordDTO.setBusinessCode(FlowClientConstant.REPORTING_MATTER);
                processRecordService.addProcessRecord(recordDTO,jwtUser.getUserOtherId(),process);
            }
        } else if(BeanUtil.isNotEmpty(byId) && SealApplicationEnum.AGREE.getType().equals(byId.getApproveStatus())){
            // 已确定 -> 撤回待审批
            // 判断是否已盖章
            LambdaQueryWrapper<SealApplicationInfo> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(SealApplicationInfo::getApplyId,id).eq(SealApplicationInfo::getProcessingProgress,CommonConstants.NO2);
            List<SealApplicationInfo> list = sealApplicationInfoService.list(queryWrapper);
            if(CollectionUtil.isEmpty(list) && !byId.getSealType().contains(SealtypeEnum.PHYSICAL_SEAL.getType().toString())){
                byId.setApproveStatus(SealApplicationEnum.WITHDRAW_PENDING.getType());
                String str = flowApiClient.startProcessInstanceByKey(jwtUser.getUserOtherId(), FlowClientConstant.WITHDRAW_SEAL_APPLICATION, "用印申请撤回", "t_seal_application:" + id,null);
                ReturnVo returnVo = JSONObject.parseObject(str, ReturnVo.class);
                WorkFlowExceptionUtil.checkFlowResult(str);
                if ("end".equals(returnVo.getMsg())) {
                    byId.setApproveStatus(SealApplicationEnum.WITHDRAW.getType());
                }
                //插入流程记录
                ProcessRecordDTO recordDTO = new ProcessRecordDTO();
                recordDTO.setBusinessCode(FlowClientConstant.REPORTING_MATTER);
                recordDTO.setBusinessId(id);
                recordDTO.setOperation("撤回");
                recordDTO.setOperatorId(jwtUser.getUserId());
                recordDTO.setOperatorName(jwtUser.getUserName());
                processRecordService.addProcessRecord(recordDTO,jwtUser.getUserOtherId(),str);
            } else {
                return Result.failed("已盖章不允许撤回！");
            }
        } else if(BeanUtil.isNotEmpty(byId) && SealApplicationEnum.WITHDRAW_PENDING.getType().equals(byId.getApproveStatus())){
            // 撤回待审批 -> 同意
            String process = flowApiClient.revokeProcess(jwtUser.getUserOtherId(), "t_seal_application:" + id, "撤回申请");
            byId.setApproveStatus(SealApplicationEnum.AGREE.getType());
            boolean b = WorkFlowExceptionUtil.checkFlowResult(process);
            if(b){
                //插入流程记录
                ProcessRecordDTO recordDTO = new ProcessRecordDTO();
                recordDTO.setBusinessId(id);
                recordDTO.setOperation("撤回申请");
                recordDTO.setOperatorId(jwtUser.getUserId());
                recordDTO.setOperatorName(jwtUser.getUserName());
                recordDTO.setBusinessCode(FlowClientConstant.REPORTING_MATTER);
                processRecordService.addProcessRecord(recordDTO,jwtUser.getUserOtherId(),process);
            }
        }
        return Result.okOrFailed(updateById(byId));
    }

    /**
     * 取消申请
     * @param id
     * @return
     */
    @Override
    public Result<Boolean> deleteById(Long id) {
        SealApplication byId = getById(id);
        if(BeanUtil.isNotEmpty(byId) && (SealApplicationEnum.RETURN.getType().equals(byId.getApproveStatus()) || SealApplicationEnum.WITHDRAW.getType().equals(byId.getApproveStatus()))){
            return Result.okOrFailed(removeById(id));
        } else {
            return Result.failed("数据状态异常，不允许取消！");
        }
    }

    /**
     * 用印审批列表
     * @param req
     * @return
     */
    @Override
    public IPage<SealApplicationHandleVO> selectSealHandleVO(SealApplicationHandleREQ req) {
        IPage<SealApplicationHandleVO> page = req.buildPage();
        IPage<SealApplicationHandleVO> pageinfo = this.getBaseMapper().selectSealHandleVO(page, req);
        if(null != pageinfo.getRecords() && pageinfo.getRecords().size() > 0){
            //转换数据
            for (SealApplicationHandleVO record : pageinfo.getRecords()) {
                changetype(record);
            }
        }
        return pageinfo;
    }


    /**
     * 转换承报类型
     * @param vo
     */
    void changetype(SealApplicationHandleVO vo){
        vo.setTypeInfo(SealApplicationEnum.getValueByKey(vo.getType()));
        vo.setSealTypeInfo(SealtypeEnum.getValueByKey(vo.getSealType()));
        // 处理文件
        vo.setSealFileList(this.getBaseMapper().getSealFileList(vo.getApplyId()));
        // 处理处理进度
        ProcessRecordReq req = new ProcessRecordReq();
        List<ProcessRecordListReq> reqlist = new ArrayList<>();
        ProcessRecordListReq l1 = new ProcessRecordListReq();
        ProcessRecordListReq l2 = new ProcessRecordListReq();
        l1.setBusinessCode("REPORTING_MATTER_INFO");
        l1.setBusinessId(vo.getId().toString());
        l2.setBusinessCode(FlowClientConstant.REPORTING_MATTER);
        l2.setBusinessId(vo.getApplyId().toString());
        reqlist.add(l1);
        reqlist.add(l2);
        req.setBusinessList(reqlist);
        List<ProcessRecordDTO> processRecordDTOS = processRecordService.queryProcessRecords(req);
        if(null != processRecordDTOS && processRecordDTOS.size() > 0){
            String process = "";
            int cunt = processRecordDTOS.size();
            for (ProcessRecordDTO processRecordDTO : processRecordDTOS) {
                SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                String createdTime = formatter.format(processRecordDTO.getCreatedTime());
                process = process+cunt+" : "+processRecordDTO.getOperatorName()+createdTime;
                if(null != processRecordDTO.getRemark()){
                    process = process+"备注："+processRecordDTO.getRemark();
                }
                if(null != processRecordDTO.getNextUserName()){
                    process = process+"待："+processRecordDTO.getNextUserName()+"处理";
                }
                process = process + "\n";
                cunt = cunt -1;
            }
            vo.setProcess(process);
        }




    }


    @Override
    public void exportSealHandleVO(HttpServletResponse response,SealApplicationHandleREQ req) {
        List<SealApplicationHandleVO> vos = this.getBaseMapper().selectSealHandleVO(req);
        if(null != vos && vos.size() > 0){
            //转换数据
            for (SealApplicationHandleVO record : vos) {
                changetype(record);
            }
        }

        //设置标题信息
        Workbook workbook;
        String title;
        title="用印管理";
        if(SealApplicationEnum.EXPORT_TYPE_TREAT.getType().equals(req.getExportType())){
            workbook= ExcelExportUtil.exportExcel(new ExportParams(title,"用印管理"), SealApplicationHandleVO.class,vos);
        }else{
            List<SealApplicationHandleTreatVO> vots = new ArrayList<>();
            vots =  BeanListUtil.convertList(vos, SealApplicationHandleTreatVO.class);
            workbook= ExcelExportUtil.exportExcel(new ExportParams(title,"用印管理"), SealApplicationHandleTreatVO.class,vots);
        }

        try {
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("用印管理.xls", "UTF-8"));
            response.setContentType("application/vnd.ms-excel;charset=UTF-8");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        try {
            workbook.write(response.getOutputStream());
            response.getOutputStream().close();
            workbook.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    @Override
    public Boolean saveInfo(SealApplicationREQ sealReq) {
        // 查询是否存在用章记录
        if(sealReq.getSealType().contains(SealtypeEnum.PHYSICAL_SEAL.getType().toString()) && CollectionUtil.isNotEmpty(sealReq.getFileDTOS())){
            LambdaQueryWrapper<SealBidWin> query =  new LambdaQueryWrapper<>();
            query.eq(SealBidWin::getNoticeId,sealReq.getFileDTOS().get(0).getNoticeId())
                    //.isNull(SealBidWin::getBidWinPeopleId)
                    .orderByDesc(SealBidWin::getCreatedTime)
                    .last("limit 1");
            SealBidWin one = sealBidWinService.getOne(query);
            if(BeanUtil.isNotEmpty(one)){
                sealReq.setId(one.getApplyId());
            }
        }
        // 查询用章  公章
        LambdaQueryWrapper<SealManagement> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(SealManagement::getSealName,"公章").orderByAsc(SealManagement::getCreatedTime).last("limit 1");
        SealManagement one = sealManagementService.getOne(queryWrapper);
        sealReq.setType(SealApplicationEnum.SEAL_APPROVAL.getType());
        sealReq.setApproveStatus(SealApplicationEnum.TO_SHOW.getType());
        sealReq.setApplyItem(SealApplicationInfoEnum.BID_WIN_NOTICE.getType());
        sealReq.setItemDescribe(SealApplicationInfoEnum.BID_WIN_NOTICE.getDesc());
        sealReq.setSealMethod(SealApplicationInfoEnum.LOCAL_PAGE.getType().toString());
        if(BeanUtil.isNotEmpty(one)){
            sealReq.setChooseSeal(one.getId().toString());
            sealReq.setChooseSealInfo(one.getSealName());
        }
        // 保存用印申请信息
        SealApplication application = BeanUtil.copyProperties(sealReq, SealApplication.class);
        boolean save = saveOrUpdate(application);
        // 删除用印申请详细信息
        LambdaQueryWrapper<SealApplicationInfo> queryWrapper1 = new LambdaQueryWrapper<>();
        queryWrapper1.eq(SealApplicationInfo::getApplyId,application.getId());
        sealApplicationInfoService.remove(queryWrapper1);
        // 保存用印方式数据
        if(StringUtils.isNotBlank(sealReq.getSealType())){
            String[] types = sealReq.getSealType().split(",");
            List<SealApplicationInfo> infoList = new ArrayList<>();
            for (String type:types) {
                SealApplicationInfo info = new SealApplicationInfo();
                info.setApplyId(application.getId());
                info.setSealType(Integer.parseInt(type));
                infoList.add(info);
            }
            sealApplicationInfoService.saveBatch(infoList);
        }
        // 删除旧的用印项目数据
        LambdaQueryWrapper<SealApplicationProject> projectQueryWrapper = new LambdaQueryWrapper<>();
        projectQueryWrapper.eq(SealApplicationProject::getApplyId,application.getId());
        applicationProjectService.remove(projectQueryWrapper);
        // 保存用印项目数据
        if(CollectionUtil.isNotEmpty(sealReq.getProjectDTOS())){
            List<SealApplicationProjectDTO> projectDTOS = sealReq.getProjectDTOS();
            List<SealApplicationProject> sealApplicationProjects = BeanListUtil.convertList(projectDTOS, SealApplicationProject.class);
            sealApplicationProjects.forEach(l->l.setApplyId(application.getId()));
            applicationProjectService.saveBatch(sealApplicationProjects);
        }
        // 删除旧的用印文件数据
        LambdaQueryWrapper<SealFile> fileQueryWrapper = new LambdaQueryWrapper<>();
        fileQueryWrapper.eq(SealFile::getApplyId,application.getId());
        sealFileService.remove(fileQueryWrapper);
        // 删除旧的用印申请关联的中标信息
        LambdaQueryWrapper<SealBidWin> winQueryWrapper = new LambdaQueryWrapper<>();
        winQueryWrapper.eq(SealBidWin::getApplyId,application.getId());
        sealBidWinService.remove(winQueryWrapper);
        // 保存用印材料  关联中标信息
        if(CollectionUtil.isNotEmpty(sealReq.getFileDTOS())){
            List<SealFileDTO> fileDTOS = sealReq.getFileDTOS();
            List<SealFile> files = BeanListUtil.convertList(fileDTOS, SealFile.class);
            files = files.stream().filter(l->l.getOriginalFile()!= null).collect(Collectors.toList());
            if(CollectionUtil.isNotEmpty(files)){
                files.forEach(l->l.setApplyId(application.getId()));
                sealFileService.saveBatch(files);
            }
            List<SealBidWin> sealBidWins = new ArrayList<>();
            // 判断是否为电子章
            if(sealReq.getSealType().contains(SealtypeEnum.ELECTRONIC_SEAL.getType().toString())){
                for (SealFileDTO sealFileDTO:fileDTOS) {
                    SealBidWin win = new SealBidWin();
                    win.setApplyId(application.getId());
                    win.setBidWinPeopleId(sealFileDTO.getBidWinPeopleId());
                    win.setIsWin(sealFileDTO.getIsWin());
                    win.setFile(sealFileDTO.getOriginalFile());
                    win.setNoticeId(sealFileDTO.getNoticeId());
                    sealBidWins.add(win);
                }
            } else {
                SealBidWin win = new SealBidWin();
                win.setApplyId(application.getId());
                win.setFile(fileDTOS.get(0).getOriginalFile());
                win.setNoticeId(fileDTOS.get(0).getNoticeId());
                sealBidWins.add(win);
            }
            sealBidWinService.saveBatch(sealBidWins);
        }
        return save;
    }

    @Override
    public List<SealFilesVO> getSealFileBySection(SealFileSectionREQ req) {
        return this.getBaseMapper().getSealFileBySection(req);
    }

    @Override
    public Result<Boolean> isEndReview(SealApprovalREQ req) {
        Boolean isEnd = false;
        SealApplication byId = getById(req.getBusId());
        if(SealApplicationEnum.REPORTING_MATTER.getType().equals(byId.getType())){
            LambdaQueryWrapper<SealApprove> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(SealApprove::getApplyId,req.getBusId()).eq(SealApprove::getIsApprove,CommonConstants.NO2).eq(SealApprove::getIsChoose,CommonConstants.YES);
            List<SealApprove> list = sealApproveService.list(queryWrapper);
            if(CollectionUtil.isNotEmpty(list) && list.size() == 1){
                AppingTaskREQ appingTaskREQ = new AppingTaskREQ();
                List<String> processInstanceIds = new ArrayList<>();
                processInstanceIds.add(req.getProcessInstanceId());
                appingTaskREQ.setProcessInstanceIds(processInstanceIds);
                appingTaskREQ.setProcessDefinitionKey(
                        FlowClientConstant.REPORTING_MATTER+ ","
                                + FlowClientConstant.CONTRACTUAL_MATTERS + ","
                                + FlowClientConstant.DRAFT_TEXT_CLARIFY + ","
                                + FlowClientConstant.DRAFT_TEXT_REPLY + ","
                                + FlowClientConstant.DRAFT_TEXT_OTHER + ","
                                + FlowClientConstant.SEAL_APPROVAL_TENDER + ","
                                + FlowClientConstant.SEAL_APPROVAL_REPORT + ","
                                + FlowClientConstant.SEAL_APPROVAL_PURCHASING + ","
                                + FlowClientConstant.WITHDRAW_SEAL_APPLICATION + ","
                                + FlowClientConstant.REPORTING_MATTER_ONE
                );
                String userOtherId= SecurityUtils.getJwtUser().getUserOtherId();
                Paging<AppingTaskVO> paging = workflowCacheService.getPaddingRemoteList(appingTaskREQ,userOtherId);
                List<AppingTaskVO> records = paging.getRecords();
                if(CollectionUtil.isNotEmpty(records)){
                    String formKey = records.get(0).getFormKey();
                    if(StringUtils.isNotBlank(formKey) && list.get(0).getFormkey().equals(formKey)){
                        isEnd = true;
                    }
                }
            }
        } else {
            AppingTaskREQ appingTaskREQ = new AppingTaskREQ();
            List<String> processInstanceIds = new ArrayList<>();
            processInstanceIds.add(req.getProcessInstanceId());
            appingTaskREQ.setProcessInstanceIds(processInstanceIds);
            appingTaskREQ.setProcessDefinitionKey(
                    FlowClientConstant.REPORTING_MATTER + ","
                            + FlowClientConstant.CONTRACTUAL_MATTERS + ","
                            + FlowClientConstant.DRAFT_TEXT_CLARIFY + ","
                            + FlowClientConstant.DRAFT_TEXT_REPLY + ","
                            + FlowClientConstant.DRAFT_TEXT_OTHER + ","
                            + FlowClientConstant.SEAL_APPROVAL_TENDER + ","
                            + FlowClientConstant.SEAL_APPROVAL_REPORT + ","
                            + FlowClientConstant.SEAL_APPROVAL_PURCHASING + ","
                            + FlowClientConstant.WITHDRAW_SEAL_APPLICATION + ","
                            + FlowClientConstant.REPORTING_MATTER_ONE
            );
            String userOtherId= SecurityUtils.getJwtUser().getUserOtherId();
            Paging<AppingTaskVO> paging = workflowCacheService.getPaddingRemoteList(appingTaskREQ,userOtherId);
            List<AppingTaskVO> records = paging.getRecords();
            if(CollectionUtil.isNotEmpty(records)){
                String formKey = records.get(0).getFormKey();
                if(StringUtils.isNotBlank(formKey)){
                    isEnd = true;
                }
            }
        }
        return Result.ok(isEnd);
    }

    @Override
    public List<SealFileVO> getSealFileList(Long applyId) {
        return this.getBaseMapper().getSealFileList(applyId);
    }
}