<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="com.hzw.sunflower.dao.BookkeepDepartmentConfirmMapper">

	<select id="queryShareList"
			resultType="com.hzw.sunflower.controller.project.response.BookkeepDepartmentConfirmVo">
		SELECT
		c.id,
		p.id projectId,
		p.purchase_number,
		p.project_name,
		p.entrusted_amount,
		u.user_name,
		c.apply_time
		FROM
		t_bookkeep_department_confirm c
		LEFT JOIN t_project p ON p.id = c.project_id
		LEFT JOIN t_user u ON u.id = p.created_user_id
		WHERE
		c.is_delete = 0
		and p.is_delete = 0
		and u.is_delete = 0
		and FIND_IN_SET(#{userId},c.confirm_user_id)
		and c.confirm_status = 1
		AND c.project_id NOT IN ( SELECT DISTINCT c.project_id FROM t_bookkeep_department_confirm c WHERE c.confirm_status = 3 AND c.is_delete = 0 )
		<if test="condition.keyWords !=null and condition.keyWords!=''">
			and (p.purchase_number  LIKE concat('%',#{condition.keyWords},'%') or p.purchase_name like concat('%',#{condition.keyWords},'%'))
		</if>
	</select>
    <select id="findUser" resultType="com.hzw.sunflower.entity.User">
		SELECT
			u.user_name,
			u.id
		FROM
			t_project p
			LEFT JOIN t_user u ON u.id = p.created_user_id
			where p.id = #{projectId}
	</select>
	<select id="findPhone" resultType="java.lang.String">
		SELECT user_phone FROM t_user WHERE id = #{id}
	</select>
</mapper>
