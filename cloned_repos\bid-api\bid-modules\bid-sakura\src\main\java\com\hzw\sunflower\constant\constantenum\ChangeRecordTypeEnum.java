package com.hzw.sunflower.constant.constantenum;

/**
 * <AUTHOR>
 * @date ：Created in 2022/07/27 9:23
 * @description：调账日志操作类型
 * @version: 1.0
 */
public enum ChangeRecordTypeEnum {

    SUPPLIER_CONFIRM_RELATION(1, "供应商确认关联（次月触发）"),
    SUPPLIER_RELATION(2, "供应商手动关联（次月或者再次关联）"),
    MANAGER_RELATION(3, "项目经理手动关联（次月或者再次关联）"),
    OPERMANAGE_RELATION(4, "运管处手动关联（次月触发）");

    private Integer type;
    private String desc;

    ChangeRecordTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public static ChangeRecordTypeEnum getType(Integer type) {
        try {
            for (ChangeRecordTypeEnum v : ChangeRecordTypeEnum.values()) {
                if (v.type.equals(type)) {
                    return v;
                }
            }
            return null;
        } catch (Exception e) {
            return null;
        }
    }
}
