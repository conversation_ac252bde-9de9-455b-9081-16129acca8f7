package com.hzw.sunflower.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzw.sunflower.constant.CommonConstants;
import com.hzw.sunflower.constant.constantenum.NoticeMediaTypeEnum;
import com.hzw.sunflower.controller.request.DeleteNoticeMediaREQ;
import com.hzw.sunflower.dao.NoticeMediaRMapper;
import com.hzw.sunflower.entity.NoticeMediaR;
import com.hzw.sunflower.service.NoticeMediaRService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class NoticeMediaRServiceImpl extends ServiceImpl<NoticeMediaRMapper, NoticeMediaR> implements NoticeMediaRService {
    /**
     * 根据公告ID和媒体ID删除记录
     * @param  req 删除媒体请求实体
     * @return true代表删除成功，false代表删除失败
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean deleteById(DeleteNoticeMediaREQ req) {
        QueryWrapper<NoticeMediaR> wrapper = new QueryWrapper<>();
        wrapper
                .eq("notice_id",req.getNoticeId())
                .eq("media_id",req.getMediaId())
                .eq("state", NoticeMediaTypeEnum.NOTICE_MEDIA.getValue())
                .eq("is_delete", CommonConstants.NO);
        this.baseMapper.delete(wrapper);

        return true;
    }
}
