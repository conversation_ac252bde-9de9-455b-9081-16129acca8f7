package com.hzw.ssm.webService.output.impl;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;

import com.hzw.ssm.webService.output.BodyOutput;

/**
 * 专家抽取申请 接口 输出参数
 * 
 * <AUTHOR>
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)  
@XmlRootElement  (name="ExtractExpert")
@XmlType(name = "ExtractExpert", propOrder = { "extSeq" })  
public class ExtractExpertOutput extends BodyOutput {
	// 抽取序号
	@XmlElement
	private String extSeq;
	
	public String getExtSeq() {
		return extSeq;
	}

	public void setExtSeq(String extSeq) {
		this.extSeq = extSeq;
	}

	
}
