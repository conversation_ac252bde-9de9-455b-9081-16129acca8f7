package com.hzw.ssm.fw.listener.service;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.hzw.ssm.fw.base.BaseService;
import com.hzw.ssm.fw.listener.dao.InterceptorMapper;
import com.hzw.ssm.sys.login.service.LoginService;

@Service
public class InterceptorService extends BaseService{
	
	private static Log log = LogFactory.getLog(LoginService.class);
	
	@Autowired
	private InterceptorMapper interceptorMapper;
	
	/**
	 * 用户权限验证
	 */
	
	public int hasQA (String login_id,String actionName){
		int result=0;
		Map<String, String> map = new HashMap<String, String>();
		map.put("login_id", login_id);
		map.put("actionName", actionName);
		result = interceptorMapper.hasQA(map);
		if(0==result){
			log.info("系统时间："+new Date()+"，用户"+login_id+",操作越权！action名称："+actionName);
		}
		return result;
	}
	
	/**
	 * 权限名称获取
	 */
	public String getMothod(String actionName){
		String result = null;
		Map<String, String> map = new HashMap<String, String>();
		map.put("actionName", actionName);
		result = interceptorMapper.getMothod(map);		
		return result;
	}
}
