package com.hzw.ssm.sys.system.entity;

import java.util.Date;
import java.util.List;

import com.hzw.ssm.fw.base.BaseEntity;
import com.hzw.ssm.fw.util.Page;

/**
 * 短信记录信息
 * <AUTHOR> 
 * @date 2015-08-05
 *
 */
public class SmsRecordEntity extends BaseEntity 
{
	/** 对象序列化时，该对象的唯一标识 */
	private static final long serialVersionUID = 8042703708357357826L;
	private String sms_id;
	private String project_id;         // 项目ID
	private String sms_user_id;        // 发送用户ID
	private String sms_user_name;      // 发送用户名称
	private String sms_mobile;         // 发送手机号码
	private String sms_content;        // 短信内容
	private Integer sms_type;          // 短信类型   1:邀请专家   2：通知专家
	private Date sms_time;             // 发送时间
	private String decimationBatch;	   //项目流水号
	private Long sms_variety;		   //记录类型属于语音还是短信(1.人工,2.语音,3.语音通知短信,4.综合处)
	private String sms_result;		   //记录发送之后返回值
	private Integer is_accept;         //是否成功 0,成功 1.不成功
	private String mw_msgid;           //梦网的msgid
	private int mw_result; 		       //梦网返回的短信发送结果
	private String change_result;		//项目变更修改状态（1.专家参标通知 2.项目变更  3.项目取消）
	private String informId;			//(语音抽取)未接收短信的是否通知综合处
	private List<String> sms_ids;      //短信主键集合
	
	private String  is_dispose;    //是否已处理
	/** 分页 */
	private Page page;
	public String getSms_id() {
		return sms_id;
	}

	public void setSms_id(String sms_id) {
		this.sms_id = sms_id;
	}

	public String getProject_id() {
		return project_id;
	}

	public void setProject_id(String project_id) {
		this.project_id = project_id;
	}

	public String getSms_user_id() {
		return sms_user_id;
	}

	public void setSms_user_id(String sms_user_id) {
		this.sms_user_id = sms_user_id;
	}

	public String getSms_user_name() {
		return sms_user_name;
	}

	public void setSms_user_name(String sms_user_name) {
		this.sms_user_name = sms_user_name;
	}

	public String getSms_mobile() {
		return sms_mobile;
	}

	public void setSms_mobile(String sms_mobile) {
		this.sms_mobile = sms_mobile;
	}

	public String getSms_content() {
		return sms_content;
	}

	public void setSms_content(String sms_content) {
		this.sms_content = sms_content;
	}

	public Integer getSms_type() {
		return sms_type;
	}

	public void setSms_type(Integer sms_type) {
		this.sms_type = sms_type;
	}

	public Date getSms_time() {
		return sms_time;
	}

	public void setSms_time(Date sms_time) {
		this.sms_time = sms_time;
	}

	public String getDecimationBatch() {
		return decimationBatch;
	}

	public void setDecimationBatch(String decimationBatch) {
		this.decimationBatch = decimationBatch;
	}

	public Long getSms_variety() {
		return sms_variety;
	}

	public void setSms_variety(Long sms_variety) {
		this.sms_variety = sms_variety;
	}

	public String getSms_result() {
		return sms_result;
	}

	public void setSms_result(String sms_result) {
		this.sms_result = sms_result;
	}

	public Integer getIs_accept() {
		return is_accept;
	}

	public void setIs_accept(Integer is_accept) {
		this.is_accept = is_accept;
	}

	public String getMw_msgid() {
		return mw_msgid;
	}

	public void setMw_msgid(String mw_msgid) {
		this.mw_msgid = mw_msgid;
	}

	public Page getPage() {
		return page;
	}

	public void setPage(Page page) {
		this.page = page;
	}

	public int getMw_result() {
		return mw_result;
	}

	public void setMw_result(int mw_result) {
		this.mw_result = mw_result;
	}

	public String getChange_result() {
		return change_result;
	}

	public void setChange_result(String change_result) {
		this.change_result = change_result;
	}

	public String getInformId() {
		return informId;
	}

	public void setInformId(String informId) {
		this.informId = informId;
	}

	public List<String> getSms_ids() {
		return sms_ids;
	}

	public void setSms_ids(List<String> sms_ids) {
		this.sms_ids = sms_ids;
	}

	public String getIs_dispose() {
		return is_dispose;
	}

	public void setIs_dispose(String is_dispose) {
		this.is_dispose = is_dispose;
	}
	
}
