/**
 *
 * (c) Copyright Ascensio System SIA 2024
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

package com.onlyoffice.integration.documentserver.models.filemodel;

import com.onlyoffice.integration.documentserver.models.AbstractModel;
import com.onlyoffice.integration.documentserver.storage.FileStoragePathBuilder;

import lombok.Getter;
import lombok.Setter;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

@Component
@Scope("prototype")
@Getter
@Setter
public class User extends AbstractModel {
    private FileStoragePathBuilder storagePathBuilder;
    private String id;
    private String name;
    private String group;
    private String image;

    // the user configuration parameters
    public void configure(final int idParam, final String nameParam, final String groupParam) {
        this.id = "uid-" + idParam;  // the user id
        this.name = nameParam;  // the user name
        this.group = groupParam;  // the group the user belongs to
        this.image = storagePathBuilder.getServerUrl(true) + "/css/img/uid-" + this.id + ".png";
    }
}
