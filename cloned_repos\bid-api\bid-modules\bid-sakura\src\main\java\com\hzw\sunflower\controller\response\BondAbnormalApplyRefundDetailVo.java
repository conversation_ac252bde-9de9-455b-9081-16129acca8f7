package com.hzw.sunflower.controller.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@ApiModel(description = "保证金异常退款申请详情")
@Data
public class BondAbnormalApplyRefundDetailVo {

    @ApiModelProperty("供应商信息")
    private VendorVo vendorVo;

    @ApiModelProperty("选择的项目信息")
    private ChooseProjectVo chooseProjectVo;

    @ApiModelProperty("参与的项目信息")
    private List<MyCompanyProjectVo> myCompanyProjectVos;

    @ApiModelProperty("部门处长处理详情")
    private List<DepartmentUserVo> departmentUserVos;

    @ApiModelProperty(value = "总条数")
    private Integer allTotals;

    @ApiModelProperty(value = "待处理条数")
    private Integer waitDisposeTotals;

    @ApiModelProperty(value = "非本处项目条数")
    private Integer noDivisionTotals;

    @ApiModelProperty("申请id")
    private Long applyId;

    @ApiModelProperty("该条申请的状态")
    private Integer status;

    @ApiModelProperty("该条申请的处理方式")
    private Integer handleType;

    @ApiModelProperty("保证金申请退还处室处长确认表id")
    private Long applyRefundIdConfirmId;

}
