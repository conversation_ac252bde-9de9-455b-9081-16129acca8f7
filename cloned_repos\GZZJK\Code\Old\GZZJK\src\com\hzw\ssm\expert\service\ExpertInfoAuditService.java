package com.hzw.ssm.expert.service;

import com.hzw.ssm.expert.dao.ExpertInfoAuditMapper;
import com.hzw.ssm.expert.dao.ExpertInfoMapper;
import com.hzw.ssm.expert.entity.*;
import com.hzw.ssm.fw.base.BaseService;
import com.hzw.ssm.fw.util.CommUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.DecimalFormat;
import java.util.Date;
import java.util.List;

/**
 * 专家信息服务类
 *
 * <AUTHOR> 2014-10-09
 */
@Service
public class ExpertInfoAuditService extends BaseService {

    @Autowired
    private ExpertInfoAuditMapper expertInfoAuditMapper;

    @Autowired
    private ExpertInfoMapper expertInfoMapper;

    /**
     * 获取专家信息审核列表
     *
     * @param colExpertInfo
     * @return
     */
    public List<ExpertInfoEntity> queryPageExpertInfoAuditList(ExpertInfoEntity colExpertInfo) {
        if (colExpertInfo.getProvince() != null && colExpertInfo.getProvince().equals("0")) {
            colExpertInfo.setProvince(null);
        }
        if (colExpertInfo.getCity() != null && colExpertInfo.getCity().equals("0")) {
            colExpertInfo.setCity(null);
        }
        return expertInfoAuditMapper.queryPageExpertInfoAuditList(colExpertInfo);
    }

    /**
     * 获取准入审批专家信息列表
     *
     * @param colExpertInfo
     * @return
     */
    public List<ExpertInfoEntity> queryPageExpInfoAuditList(ExpertInfoEntity colExpertInfo) {
        if (colExpertInfo.getProvince() != null && colExpertInfo.getProvince().equals("0")) {
            colExpertInfo.setProvince(null);
        }
        if (colExpertInfo.getCity() != null && colExpertInfo.getCity().equals("0")) {
            colExpertInfo.setCity(null);
        }
        return expertInfoAuditMapper.queryPageExpInfoAuditList(colExpertInfo);
    }

    /**
     * 专家信息审核
     * status=3  入库审核通过，或专家信息修改审核通过  最终审核通过审核记录 置为无效delete_flag=1
     * status=4 专家信息修改审核未通过，将本次专家修改记录置为无效 delete_flag=1
     *
     * @param expertEntity
     * @param auditEntity
     */
    @Transactional
    public void auditExpertInfo(ExpertInfoEntity expertEntity, ExpertAuditEntity auditEntity) {
        expertInfoAuditMapper.auditExpertInfo(expertEntity);
        expertInfoAuditMapper.addExpertAuditReason(auditEntity);

        if (expertEntity.getStatus() == 3 || expertEntity.getStatus() == 8) {
            expertInfoBak(expertEntity);
            expertInfoAuditMapper.updateAuditEntityByUserId(expertEntity.getUser_id());
            //专家入库审核通过为专家生成编号
            if (expertEntity.getExpert_num() == null || expertEntity.getExpert_num().equals("")) {
                expertEntity.setExpert_num(CreateExpertNum());
                expertInfoAuditMapper.createExpertForExpertNum(expertEntity);
            }
        } else if (expertEntity.getStatus() == 4) {
            List<ExpertUpdateRecordEntity> updateRecordList = expertInfoMapper.queryExpertUpdateRecordList(expertEntity.getUser_id());
            for (int i = 0; i < updateRecordList.size(); i++) {
                ExpertUpdateRecordEntity record = updateRecordList.get(i);
                expertInfoMapper.deleteExpertUpdateRecordById(record.getId());
            }
        }
    }

    /**
     * 生成专家编号
     *
     * @return
     */
    private String CreateExpertNum() {
        String expert_num = "";
        String maxExpertNum = expertInfoMapper.getMaxExpertNum();
        if (maxExpertNum != null && maxExpertNum != "") {
            int no = Integer.parseInt(maxExpertNum) + 1;
            expert_num = new DecimalFormat("00000").format(no);
        } else {
            expert_num = "00001";
        }
        return expert_num;
    }

    /**
     * 如果专家审核通过， 同意入库
     * 先将专家信息备份表中的数据删除
     * 然后将专家信息进行重新备份，用于专家信息修改对比
     *
     * @param expertEntity
     */
    private void expertInfoBak(ExpertInfoEntity expertEntity) {

        //删除备份表中的数据
        expertInfoMapper.deleteExpertInfoBakByUserId(expertEntity.getUser_id());
        expertInfoMapper.deletePracticeBakByUserId(expertEntity.getUser_id());
        expertInfoMapper.deleteMajorBakByUserId(expertEntity.getUser_id());
        expertInfoMapper.deleteExperienceBakByUserId(expertEntity.getUser_id());

        ExpertInfoEntity expertEntityBak = expertInfoMapper.getExpertInfoByUserIdOrId(expertEntity);
        expertInfoMapper.saveExpertInfoBak(expertEntityBak);

        List<ExpertPracticeEntity> practiceList = expertInfoMapper.queryPracticeByUserId(expertEntity.getUser_id());
        for (int i = 0; i < practiceList.size(); i++) {
            ExpertPracticeEntity practice = practiceList.get(i);
            expertInfoMapper.addExpertPracticeBak(practice);
        }

        List<ExpertMajorEntity> majorList = expertInfoMapper.queryMajorByUserId(expertEntity.getUser_id());
        for (int i = 0; i < majorList.size(); i++) {
            ExpertMajorEntity major = majorList.get(i);
            expertInfoMapper.addExpertMajorInfoBak(major);
        }

        List<ExpertExperienceEntity> experienceList = expertInfoMapper.queryExpertExperienceList(expertEntity.getUser_id());
        for (int i = 0; i < experienceList.size(); i++) {
            ExpertExperienceEntity experience = experienceList.get(i);
            expertInfoMapper.addExpertExperienceBak(experience);
        }
    }


    /**
     * 专家审核信息列表
     *
     * @param user_id
     * @return
     */
    public List<ExpertAuditEntity> queryExpertAuditEntityList(String user_id) {
        return expertInfoAuditMapper.queryExpertAuditEntityList(user_id);
    }

    public void addExpertUpdateRecord(String userId, String modify_user, String roleId) {
        //修改项叫：入库，修改前：待入库，修改后：已入库，修改时间：入库审批通过时间，修改角色：就获取对应审核人角色
        ExpertUpdateRecordEntity record = new ExpertUpdateRecordEntity();
        record.setId(CommUtil.getKey());
        record.setUser_id(userId);
        record.setItem("入库");
        record.setContent_before("待入库");
        record.setContent_after("已入库");
        record.setModify_role(roleId);
        record.setModify_user(modify_user);
        record.setModify_reason("入库");
        record.setBatch_number(0);
        record.setModify_time(new Date());
        record.setDelete_flag(0);
        expertInfoMapper.addExpertUpdateRecord(record);
    }
}
