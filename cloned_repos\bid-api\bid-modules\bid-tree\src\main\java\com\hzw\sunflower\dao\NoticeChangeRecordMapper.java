package com.hzw.sunflower.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.controller.request.NoticeAuditReq;
import com.hzw.sunflower.controller.request.NoticeChangeRecordReq;
import com.hzw.sunflower.controller.response.NoticeChangeRecordVO;
import com.hzw.sunflower.dto.AppingTaskNoticeDTO;
import com.hzw.sunflower.entity.NoticeChangeRecord;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface NoticeChangeRecordMapper extends BaseMapper<NoticeChangeRecord> {

    // 根据公告ID获取最大提交次数
    Long findMaxSubmit(@Param("noticeId") Long noticeId);

    // 根据公告ID查询公告的简单审批信息列表
    List<AppingTaskNoticeDTO> queryAppingTaskMapByIds(@Param("noticeIds") List<Long> noticeIds);

    // 根据请求查询变更记录
    List<NoticeChangeRecordVO> findChangeRecords(@Param("req") NoticeAuditReq req);

    //获取招标公告信息变更记录
    List<NoticeChangeRecordVO> noticeInfoByBd(@Param("req") NoticeChangeRecordReq req);
}
