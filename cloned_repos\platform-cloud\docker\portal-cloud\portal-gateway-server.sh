#!/bin/sh
#cp TEMPLATE FILE
docker stop portal-gateway-server || true
docker rm portal-gateway-server || true
docker rmi registry.cn-hangzhou.aliyuncs.com/hzw-jtcc/jtcc:portal-gateway-server-1.0.0 || true
docker pull registry.cn-hangzhou.aliyuncs.com/hzw-jtcc/jtcc:portal-gateway-server-1.0.0
docker run -itd --net=host  --log-opt max-size=100m --log-opt max-file=3  --name portal-gateway-server -v /home/<USER>/plan1/portal-cloud/config/portal-gateway-server:/hzw/gateway/config -v /data/log:/hzw/gateway/logs registry.cn-hangzhou.aliyuncs.com/hzw-jtcc/jtcc:portal-gateway-server-1.0.0
