package com.hzw.ssm.sys.project.entity;

import java.util.Date;

import com.hzw.ssm.fw.base.BaseEntity;

public class ApplyRecordEntity extends BaseEntity {
	private static final long serialVersionUID = -8151736570527763583L;

	private String id;
	private String projectId;//项目信息主键
	private String content;//申请说明
	private Long type;//申请类型
	private Date applyTime;//申请时间
	private String decimationBatch;//项目流水号（抽取批次号）
	public String getProjectId() {
		return projectId;
	}
	public void setProjectId(String projectId) {
		this.projectId = projectId;
	}
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public Long getType() {
		return type;
	}
	public void setType(Long type) {
		this.type = type;
	}
	public Date getApplyTime() {
		return applyTime;
	}
	public void setApplyTime(Date applyTime) {
		this.applyTime = applyTime;
	}
	public String getContent() {
		return content;
	}
	public void setContent(String content) {
		this.content = content;
	}
	public String getDecimationBatch() {
		return decimationBatch;
	}
	public void setDecimationBatch(String decimationBatch) {
		this.decimationBatch = decimationBatch;
	}
	
}
