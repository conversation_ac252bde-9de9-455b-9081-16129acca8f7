package com.hzw.sunflower.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 项目标段表 (t_project_bid_section)
 *
 * <AUTHOR>
 * @version 1.0.0 2021-04-12
 */
@ApiModel(description = "项目标段信息")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProjectBidSectionDTO {

    @ApiModelProperty(value = "主键", position = 1)
    private Long id;

    @ApiModelProperty(value = "项目ID", position = 2)
    private Long projectId;

    @ApiModelProperty(value = "包段编号", position = 3)
    private Integer packageNumber;

    @ApiModelProperty(value = "包名称", position = 4)
    private String packageName;

    @ApiModelProperty(value = "委托金额", position = 5)
    private BigDecimal entrustMoney;

    @ApiModelProperty(value = "代理服务费是否收费 0 免费 1 收费")
    private Integer agencyCostFree;

    @ApiModelProperty(value = "代理服务费类型其他描述文字")
    private String agencyFeeOtherDescribe;


//    @ApiModelProperty(value = "递交文件截止时间", position = 6)
//    @JsonFormat
//    private Date fileRequestEndTime;
//
//    @ApiModelProperty(value = "文件响应截止时间", position = 7)
//    @JsonFormat
//    private Date fileResponseEndTime;

    @ApiModelProperty(value = "标段状态", position = 8)
    private String status;

    @ApiModelProperty(value = "委托类型(0:入围, 1:带量)", position = 9)
    private String entrustType;

    @ApiModelProperty(value = "委托金额币种", position = 10)
    private Long entrustCurrency;

    @ApiModelProperty(value = "标段所在地(省级)", position = 11)
    private Long bidAddressProvince;

    @ApiModelProperty(value = "标段所在地(地市级)", position = 12)
    private Long bidAddressCity;

    @ApiModelProperty(value = "标的物分类级别一", position = 13)
    private Long bidFirstLevel;

    @ApiModelProperty(value = "标的物分类级别二", position = 14)
    private Long bidSecondLevel;

    @ApiModelProperty(value = "标的物分类级别三", position = 15)
    private Long bidThirdLevel;

    @ApiModelProperty(value = "采购方式", position = 16)
    private String purchaseMode;

    @ApiModelProperty(value = "保证金", position = 17)
    private BigDecimal bond;

    @ApiModelProperty(value = "保证金类型 0不收取 1定额 2比例 ", position = 18)
    private Integer bondType;

    @ApiModelProperty(value = "代理服务费", position = 19)
    private BigDecimal agencyFee;

    @ApiModelProperty(value = "代理服务费收取对象 0投标人 1招标人", position = 20)
    private Integer agencyFeeObj;

    @ApiModelProperty(value = "代理服务费类型 0不收取 1定额 2比例 3其它", position = 21)
    private Integer agencyFeeType;

    @ApiModelProperty(value = "标书费", position = 22)
    private BigDecimal tenderFee;

    @ApiModelProperty(value = "标书费 类型 0不收取 1按项目 2按标段/包", position = 23)
    private Integer tenderFeeType;

    @ApiModelProperty(value = "押金类型(0:图纸，1:其他，2:CA)", position = 24)
    private String depositType;

    @ApiModelProperty(value = "押金金额", position = 25)
    private BigDecimal depositAmount;

    @ApiModelProperty(value = "文件发售方式(0:电子文件线上下载, 1:纸质文件不支持邮寄)", position = 26)
    private String releaseFileType;

    @ApiModelProperty(value = "文件发售审核方式(0:无需审核, 1:审核无需材料, 2:审核上传材料)", position = 27)
    private String reviewFileType;

    @ApiModelProperty(value = "材料清单", position = 28)
    private String materialList;

    @ApiModelProperty(value = "标的物分类中文名称", position = 29)
    private String bidTypeName;

    @ApiModelProperty(value = "采购方式中文名称", position = 30)
    private String purchaseModeName;

    @ApiModelProperty(value = "1-预审公开 2-后审公开 3-邀请 4-二阶段 5-政府公开 6-政府邀请", position = 31)
    private Integer purchaseStatus;

    @ApiModelProperty(value = "1-预审公开 2-后审公开 3-邀请 4-二阶段 5-政府公开 6-政府邀请", position = 31)
    private Integer formerPurchaseStatus;

    @ApiModelProperty(value = "保证金比例值", position = 32)
    private BigDecimal bondPercent;

    @ApiModelProperty(value = "1：工程，2：服务，3：货物", position = 33)
    private Integer engineeringClass;

    @ApiModelProperty(value = "1 依法必招  2 非依法必招  3 政府采购  4 国际招标", position = 34)
    private Integer purchaseType;

    @ApiModelProperty(value = "标段所在地中文名称（用于前端列表显示）", position = 35)
    private String bidAddressName;

    @ApiModelProperty(value = "异常状态 0.正常 1.暂停 2.终止", position = 36)
    private Integer abnormalStatus;

    @ApiModelProperty(value = "标段code用于判断页面是新增编辑或详情", position = 37)
    private Integer bidSectionCode;

    @ApiModelProperty(value = "上次项目投标人(1.已购买招标文件的投标人 2.已通过关注审核的投标人 3.指定投标人 4.不带入)", position = 38)
    private Integer oldBidderType;

    @ApiModelProperty(value = "原标段编号", position = 39)
    private Integer formerPackageNumber;

    @ApiModelProperty(value = "原标段主键", position = 40)
    private Long formerSectionId;

    @ApiModelProperty(value = "押金类型为其他时描述信息", position = 41)
    private String depositDec;

    @ApiModelProperty(value = "版本", position = 42)
    private Integer version;

    @ApiModelProperty(value = "招标阶段：0：默认，1：第一轮，2：第二轮", position = 42)
    private Integer bidRound;

    @ApiModelProperty(value = "招标文件发售结束时间类型（1：确定时间; 2：另行通知; 3：投标文件递交截止时间前一天）", position = 43)
    private Integer saleEndTimeType;

    @ApiModelProperty(value = "招标文件发售结束时间", position = 44)
    @JsonFormat
    private Date saleEndTime;

    @ApiModelProperty(value = "投标文件递交截止时间类型（1：确定时间; 2：另行通知）", position = 45)
    private Integer submitEndTimeType;

    @ApiModelProperty(value = "投标文件递交截止时间", position = 46)
    @JsonFormat
    private Date submitEndTime;

    @ApiModelProperty(value = "资格预审方式 0 按包，1 按项目", position = 41)
    private String preQualificationMtd;

    @ApiModelProperty(value = "异常之前的标段状态，异常恢复使用使用", position = 38)
    private String formerAbnormalStatus;

    @ApiModelProperty(value = "固定金额收取类型 1 按供应商收取 2分摊收取")
    private Integer fixedAmountType;

    @ApiModelProperty(value = "1.国家纪委1980号文 2.苏招协2022002号文 3.固定比例 ")
    private Integer agencyFeeOption;

    @ApiModelProperty(value = "代理服务费批次 ")
    private Integer agencyFeeBatch;

    @ApiModelProperty(value = "是否可以关注：0：是 1：否")
    private String canSearch;

    @ApiModelProperty(value = "标的物多选")
    private List<Long> sectionExpertSpecialtyList;

}
