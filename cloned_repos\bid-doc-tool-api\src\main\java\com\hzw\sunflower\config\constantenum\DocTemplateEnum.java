package com.hzw.sunflower.config.constantenum;

/**
 * <AUTHOR>
 * @date ：Created in 2022/07/27 9:23
 * @description：招标文件模板类型
 * @version: 1.0
 */
public enum DocTemplateEnum {

    TEMPLATE_1(1, "公开招标-标准设备采购招标文件"),
    TEMPLATE_2(2, "邀请招标-标准设备采购招标文件"),
    TEMPLATE_3(3, "公开招标-标准设计招标文件"),
    TEMPLATE_4(4, "邀请招标-标准设计招标文件"),
    TEMPLATE_5(5, "公开招标-简要标准施工招标文件"),
    TEMPLATE_6(6, "邀请招标-简要标准施工招标文件");

    private Integer type;
    private String desc;

    DocTemplateEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
