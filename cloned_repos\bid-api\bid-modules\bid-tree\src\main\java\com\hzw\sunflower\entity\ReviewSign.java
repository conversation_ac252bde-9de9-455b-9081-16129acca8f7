package com.hzw.sunflower.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.hzw.sunflower.common.BaseBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @datetime 2023/09/11 10:35
 * @description: 专家签章文件表
 * @version: 1.0
 */
@ApiModel("专家签章文件表")
@TableName("t_review_sign")
@Data
public class ReviewSign extends BaseBean implements Serializable {

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "专家用户ID")
    private Long userId;

    @ApiModelProperty(value = "源文件ossid")
    private Long originFileOssId;

    @ApiModelProperty(value = "签章文件ossid")
    private Long fileOssId;

    @ApiModelProperty(value = "签署文件类型 1.评审文件 2.打分文件 3.初审文件  4.评审报告文件")
    private Integer fileType;

    @ApiModelProperty(value = "评审ID")
    private Long reviewId;

    @ApiModelProperty(value = "标段id")
    private Long sectionId;

}
