package com.hzw.sunflower.constant.constantenum;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 业务流程展示
 */
public enum ShowProcessEnum {

    SYSTEM_ABBREVIATION(0,"全流程线上"),
    YEAR(1,"线上+线下开评标"),
    DEPARTMENT_CODE(2,"线上+云开云评"),
    PROVINCE_IN_OUT(3,"全流程线下+线上归档");

    private Integer type;
    private String desc;

    ShowProcessEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    /**
     * 转list
     * @param flag 是否展示全流程线上（true:展示，false:不展示）
     * @return
     */
    public static List toList(boolean flag) {
        List list = new ArrayList();
        for (ShowProcessEnum showProcessEnum : ShowProcessEnum.values()) {
            if (flag) {
                Map<String, Object> map = new HashMap<String, Object>();
                map.put("type", showProcessEnum.getType());
                map.put("desc", showProcessEnum.getDesc());
                list.add(map);
            } else {
                if (showProcessEnum.getType() != 0) {
                    Map<String, Object> map = new HashMap<String, Object>();
                    map.put("type", showProcessEnum.getType());
                    map.put("desc", showProcessEnum.getDesc());
                    list.add(map);
                }
            }
        }
        return list;
    }

}
