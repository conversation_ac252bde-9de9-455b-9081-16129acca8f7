package com.hzw.sunflower.entity.condition;

import com.hzw.sunflower.common.BaseCondition;
import com.hzw.sunflower.controller.request.QueryPublicNoticeListREQ;
import com.hzw.sunflower.exception.ExceptionEnum;
import com.hzw.sunflower.exception.SunFlowerException;
import com.hzw.sunflower.util.RequestUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 招标公告列表页查询条件
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@ApiModel(description = "招标公告列表页查询条件")
@Data
public class QueryNoticeListCondition extends BaseCondition {

    @ApiModelProperty(value = "项目ID", position = 1)
    private Long projectId;

    @ApiModelProperty(value = "排序规则：1 更新时间；2 创建时间", position = 2)
    private String sort;

    @ApiModelProperty(value = "项目进度：0 全部；1 起草中；2 审核中；3 已撤回；4 已审核；5 已发布；6 已退回；", position = 3)
    private Integer status;

    @ApiModelProperty(value = "模糊搜索", position = 4)
    private String keyword;

    @ApiModelProperty(value = "招标公告阶段", position = 5)
    private Integer bidRound;

    public String setSortRule(Integer sort){
        switch (sort){
            case 1: return "pbn.`updated_time`";
            case 2: return "pbn.`created_time`";
        }
        return "pbn.updated_time";
    }

    /**
     * 有参构造器
    */
    public QueryNoticeListCondition(QueryPublicNoticeListREQ req){
        super();

        if(RequestUtil.isEmpty(req.getPage())){
            this.setPage(1L);
        }else{
            this.setPage(req.getPage());
        }

        if(RequestUtil.isEmpty(req.getPageSize())){
            this.setPageSize(40L);
        }else{
            this.setPageSize(req.getPageSize());
        }

        if(RequestUtil.isEmpty(req.getSort())){
            this.sort = setSortRule(1);
        }else{
            switch(req.getSort()){
                case 1:
                    this.sort = setSortRule(1);
                    break;
                case 2:
                    this.sort = setSortRule(2);
                    break;
                default:
                    throw new SunFlowerException(ExceptionEnum.NOTICE_PARAMS_INCURRECT,null);
            }
        }

        projectId = req.getProjectId();
        status = req.getStatus();
        keyword = req.getKeyForSearching();

        bidRound=req.getBidRound();
    }
}
