package com.hzw.sunflower.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class UnreturnedBondDto {

    @ApiModelProperty(value = "项目Id")
    private Long projectId;

    @ApiModelProperty(value = "项目编号")
    private String purchaseNumber;

    @ApiModelProperty(value = "项目名称")
    private String projectName;

    @ApiModelProperty(value = "标段Id")
    private Long sectionId;

    @ApiModelProperty(value = "标段状态")
    private Integer status;

    @ApiModelProperty(value = "采购方式")
    private String purchaseType;

    @ApiModelProperty(value = "项目经理id")
    private Long userId;

    @ApiModelProperty(value = "项目经理名称")
    private String userName;

    @ApiModelProperty(value = "项目经理手机号")
    private String userPhone;

    @ApiModelProperty(value = "供应商id")
    private Long companyId;

    @ApiModelProperty(value = "供应商名称")
    private String companyName;

    @ApiModelProperty(value = "未退保证金金额")
    private BigDecimal bondMoney;

    @ApiModelProperty(value = "中标通知书盖章时间")
    private Date sealTime;

    @ApiModelProperty(value = "部门名称")
    private String deptName;

    @ApiModelProperty(value = "部门code")
    private String deptCode;

    @ApiModelProperty(value = "是否为中标人")
    private Integer isWin;

    @ApiModelProperty(value = "是否为中标候选人")
    private Integer isCandidate;
}
