<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzw.sunflower.dao.SealManagementMapper">

    <select id="selectProjectName" resultType="java.lang.String">
        select distinct t2.project_name
        from
        t_seal_application t1
        left join t_seal_application_project t2 on t1.id = t2.apply_id
        where
        t1.is_delete = 0
        and t2.is_delete = 0
        and t1.approve_status = 1
        and find_in_set(#{id},t1.choose_seal)
    </select>
</mapper>