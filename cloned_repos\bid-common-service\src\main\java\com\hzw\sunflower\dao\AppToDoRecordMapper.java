package com.hzw.sunflower.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hzw.sunflower.controller.request.AppTaskCondition;
import com.hzw.sunflower.dto.AppToDoRecordDto;
import com.hzw.sunflower.entity.AppToDoRecord;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2021/8/27 11:28
 * @description： Mapper接口
 * @version: 1.0
 */
public interface AppToDoRecordMapper extends BaseMapper<AppToDoRecord> {

    /**
     * 分页检索
     * @param page
     * @param condition
     * @return
     */
    IPage<AppToDoRecordDto> getListByCondition(IPage<AppToDoRecordDto> page, @Param("condition") AppTaskCondition condition);

    List<AppToDoRecordDto> queryListByCondition(@Param("condition") AppTaskCondition condition);
}
