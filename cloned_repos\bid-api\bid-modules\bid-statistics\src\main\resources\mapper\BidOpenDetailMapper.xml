<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hzw.sunflower.dao.BidOpenDetailMapper" >

    <resultMap id="BaseResultMap" type="com.hzw.sunflower.entity.BidOpenDetail">
        <result column="project_id" property="projectId" />
        <result column="projectName" property="projectName" />
        <result column="purchaseName" property="purchaseName" />
        <result column="projectNumber" property="projectNumber" />
        <result column="bidPackage" property="bidPackage" />
        <result column="userName" property="userName" />
        <result column="departmentName" property="departmentName" />
        <result column="principalCompany" property="principalCompany" />
        <result column="entrustMoney" property="entrustMoney" />
        <result column="openTime" property="openTime" />
        <result column="sectionId" property="sectionId" />
    </resultMap>

<select id="selectBidOpenDetailList" resultMap="BaseResultMap">
    SELECT DISTINCT
    bs.id AS sectionId,
    bs.project_id,
    tp.purchase_name AS purchaseName,
    tp.project_name AS projectName,
    tp.purchase_number AS projectNumber,
    tp.created_time,
    bs.package_number AS bidPackage,
    tu.user_name AS userName,
    td.department_name AS departmentName,
    tp.principal_company AS principalCompany,
    bs.entrust_money AS entrustMoney,
    bs.submit_end_time AS openTime,
    bs.entrust_currency AS entrustCurrency,
    dic.`value` AS exchangeRate
    FROM
    t_project_bid_section bs
    LEFT JOIN t_project tp ON bs.project_id = tp.id
    LEFT JOIN t_project_entrust_user eu ON bs.project_id = eu.project_id
    LEFT JOIN r_user_department rud on eu.user_id = rud.user_id  and eu.department_id = rud.department_id
    LEFT JOIN t_department td ON td.id = rud.department_id
    LEFT JOIN t_user tu ON tu.id = rud.user_id
    LEFT JOIN t_dictionary dic ON bs.entrust_currency = dic.id
    WHERE
    1 = 1
    AND bs.is_delete = 0
    AND tp.is_delete = 0
    AND !(tp.re_tender = 1 AND bs.`status` &gt;= 70)
    AND eu.type = 1
    AND eu.is_delete = 0
--     AND bs.bid_round = 2
    AND bs.submit_end_time_type = 1
    AND bs.submit_end_time IS NOT NULL

    <if test='req.bidStatus == null or req.bidStatus ==""'>
--         AND ((bs.`status` &gt;= 30
--         AND bs.`status` &lt; 70) or (bs.status &gt;= 70 and bs.former_abnormal_status &gt;= 30 and bs.former_abnormal_status &lt; 70 )  OR ( DATE_FORMAT( bs.submit_end_time, '%Y-%m-%d' ) &gt; DATE_FORMAT( NOW(), '%Y-%m-%d' )
--         AND bs.`status` &lt; 70 ))
    </if>

    <if test='req.bidStatus != null and req.bidStatus =="1"'>
--         AND ((bs.`status` &gt;= 30
--         AND bs.`status` &lt; 70) or (bs.status &gt;= 70 and bs.former_abnormal_status &gt;= 30 and bs.former_abnormal_status &lt; 70 )  OR ( DATE_FORMAT( bs.submit_end_time, '%Y-%m-%d' ) &gt; DATE_FORMAT( NOW(), '%Y-%m-%d' )
--         AND bs.`status` &lt; 70 ))
    </if>

    <if test='req.bidStatus != null and req.bidStatus =="2"'>
        AND ((bs.`status` &gt;= 30
        AND bs.`status` &lt; 70) or (bs.status &gt;= 70 and bs.former_abnormal_status &gt;= 30 and bs.former_abnormal_status &lt; 70 )
        )
    </if>

    <if test='req.bidStatus != null and req.bidStatus =="3"'>
        AND bs.`status` &lt; 30
    </if>

    <if test='req.bidStatus != null and req.bidStatus =="4"'>
        AND bs.`status` &gt;= 70
    </if>


    <if test="startDate !=null and startDate !=''">
        AND DATE_FORMAT( bs.submit_end_time, '%Y-%m-%d' ) &gt;= #{startDate}
    </if>
    <if test="endDate !=null and endDate !=''">
        AND DATE_FORMAT( bs.submit_end_time, '%Y-%m-%d' ) &lt;= #{endDate}
    </if>
    <if test="keyWords != null and keyWords != ''">
        AND (
        tp.purchase_name LIKE '%${keyWords}%'
        or tu.user_name like '%${keyWords}%'
        )
    </if>
    <if test="req.purchaseVos != null and req.purchaseVos.size >0 ">

        and bs.purchase_type in
        <foreach collection="req.purchaseVos" index="index" item="purchaseVo" open="(" separator="," close=")">
            #{purchaseVo.purchaseType}
        </foreach>

        and bs.purchase_mode in
        <foreach collection="req.purchaseVos" index="index" item="purchaseVo" open="(" separator="," close=")">
            #{purchaseVo.code}
        </foreach>

        and bs.purchase_status in
        <foreach collection="req.purchaseVos" index="index" item="purchaseVo" open="(" separator="," close=")">
            #{purchaseVo.purchaseStatus}
        </foreach>

    </if>
    <if test="req.deptIds != null and req.deptIds.size >0 ">
    and td.id in
    <foreach collection="req.deptIds" index="index" item="item" open="(" separator="," close=")">
            #{item}
    </foreach>
    </if>
    <if test="req.userIds != null and req.userIds.size > 0">
        AND tu.id IN
        <foreach collection="req.userIds" item="item" index="id" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </if>
    <if test="req.minPrice != null and req.minPrice != ''">
        and bs.entrust_money &gt;= #{req.minPrice}*10000
    </if>
    <if test="req.maxPrice != null and req.maxPrice != ''">
        and bs.entrust_money &lt;= #{req.maxPrice}*10000
    </if>
    <if test="datascopesql != null and datascopesql != ''">
        ${datascopesql}
    </if>

    ORDER BY
    bs.submit_end_time,
    bs.project_id,
    bs.package_number

    <if test="startIndex != 0 and pageSize != 0">
        LIMIT #{startIndex} , #{pageSize}
    </if>


</select>

    <select id="selectCountOfBidOpenDetailList" resultType="int">
        SELECT
       count(*)
        FROM
        (SELECT DISTINCT
        bs.project_id,
        tp.purchase_name AS purchaseName,
        tp.project_name AS projectName,
        tp.purchase_number AS projectNumber,
        bs.package_number AS bidPackage,
        tu.user_name AS userName,
        td.department_name AS departmentName,
        tp.principal_company AS principalCompany,
        bs.entrust_money AS entrustMoney,
        bs.submit_end_time AS openTime,
        bs.entrust_currency AS entrustCurrency,
        dic.`value` AS exchangeRate
        FROM
        t_project_bid_section bs
        LEFT JOIN t_project tp ON bs.project_id = tp.id
        LEFT JOIN t_project_entrust_user peu ON bs.project_id = eu.project_id
        LEFT JOIN r_user_department rud on peu.user_id = rud.user_id and peu.department_id = rud.department_id
        LEFT JOIN t_department td ON td.id = rud.department_id
        LEFT JOIN t_user tu ON tu.id = rud.user_id
        LEFT JOIN t_dictionary dic ON bs.entrust_currency = dic.id
        WHERE
        1 = 1
        AND bs.is_delete = 0
        AND tp.is_delete = 0
        AND eu.type = 1
        AND eu.is_delete = 0
        AND bs.submit_end_time_type = 1
        AND bs.submit_end_time IS NOT NULL
        AND ((bs.`status` &gt;= 30 AND bs.`status` &lt;= 60) or (bs.status &gt;= 70 and bs.former_abnormal_status &gt;= 30 and bs.former_abnormal_status &lt; 70 ))
        <if test="startDate !=null and startDate !=''">
            AND DATE_FORMAT( bs.submit_end_time, '%Y-%m-%d' ) &gt;= '${startDate}'
        </if>
        <if test="endDate !=null and endDate !=''">
            AND DATE_FORMAT( bs.submit_end_time, '%Y-%m-%d' ) &lt;= '${endDate}'
        </if>
        <if test="keyWords != null and keyWords != ''">
            AND (
            tp.purchase_name LIKE '%${keyWords}%'
            or tu.user_name like '%${keyWords}%'
            )
        </if>
        <if test="req.purchaseVos != null and req.purchaseVos.size >0 ">
            and bs.purchase_type in
            <foreach collection="req.purchaseVos" index="index" item="purchaseVo" open="(" separator="," close=")">
                #{purchaseVo.purchaseType}
            </foreach>

            and bs.purchase_mode in
            <foreach collection="req.purchaseVos" index="index" item="purchaseVo" open="(" separator="," close=")">
                #{purchaseVo.code}
            </foreach>

            and bs.purchase_status in
            <foreach collection="req.purchaseVos" index="index" item="purchaseVo" open="(" separator="," close=")">
                #{purchaseVo.purchaseStatus}
            </foreach>
        </if>
        <if test="req.deptIds != null and req.deptIds.size >0 ">
            and td.id in
            <foreach collection="req.deptIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="req.userIds != null and req.userIds.size > 0">
            AND tu.id IN
            <foreach collection="req.userIds" item="item" index="id" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="req.minPrice != null and req.minPrice != ''">
            and bs.entrust_money >= #{req.minPrice}*10000
        </if>
        <if test="req.maxPrice != null and req.maxPrice != ''">
            and bs.entrust_money &lt;= #{req.maxPrice}*10000
        </if>
        <if test="datascopesql != null and datascopesql != ''">
            ${datascopesql}
        </if>) a
    </select>



    <select id="queryBidOpenDetailList" resultMap="BaseResultMap">
        SELECT DISTINCT
        bs.project_id,
        tp.purchase_name AS purchaseName,
        tp.project_name AS projectName,
        tp.purchase_number AS projectNumber,
        bs.package_number AS bidPackage,
        tu.user_name AS userName,
        td.department_name AS departmentName,
        tp.principal_company AS principalCompany,
        bs.entrust_money AS entrustMoney,
        bs.submit_end_time AS openTime,
        bs.entrust_currency AS entrustCurrency,
        dic.`value` AS exchangeRate,
        tp.created_time
        FROM
        t_project_bid_section bs
        LEFT JOIN t_project tp ON bs.project_id = tp.id
        LEFT JOIN t_project_entrust_user eu ON bs.project_id = eu.project_id
        LEFT JOIN r_user_department rud on eu.user_id = rud.user_id  and eu.department_id = rud.department_id
        LEFT JOIN t_department td ON td.id = rud.department_id
        LEFT JOIN t_user tu ON tu.id = rud.user_id
        LEFT JOIN t_dictionary dic ON bs.entrust_currency = dic.id
        WHERE
        1 = 1
        AND bs.is_delete = 0
        AND tp.is_delete = 0
        AND eu.type = 1
        AND eu.is_delete = 0
        AND bs.submit_end_time_type = 1
        AND bs.submit_end_time IS NOT NULL
        AND ((bs.`status` &gt;= 30 AND bs.`status` &lt;= 60) or (bs.status &gt;= 70 and bs.former_abnormal_status &gt;= 30 and bs.former_abnormal_status &lt; 70 ))
        <if test="startDate !=null and startDate !=''">
            AND DATE_FORMAT( bs.submit_end_time, '%Y-%m-%d' ) &gt;= '${startDate}'
        </if>
        <if test="endDate !=null and endDate !=''">
            AND DATE_FORMAT( bs.submit_end_time, '%Y-%m-%d' ) &lt;= '${endDate}'
        </if>
        <if test="keyWords != null and keyWords != ''">
            AND (
            tp.purchase_name LIKE '%${keyWords}%'
            or tu.user_name like '%${keyWords}%'
            )
        </if>
        <if test="req.purchaseVos != null and req.purchaseVos.size >0 ">
            and bs.purchase_type in
            <foreach collection="req.purchaseVos" index="index" item="purchaseVo" open="(" separator="," close=")">
                #{purchaseVo.purchaseType}
            </foreach>

            and bs.purchase_mode in
            <foreach collection="req.purchaseVos" index="index" item="purchaseVo" open="(" separator="," close=")">
                #{purchaseVo.code}
            </foreach>

            and bs.purchase_status in
            <foreach collection="req.purchaseVos" index="index" item="purchaseVo" open="(" separator="," close=")">
                #{purchaseVo.purchaseStatus}
            </foreach>
        </if>
        <if test="req.deptIds != null and req.deptIds.size >0 ">
            and td.id in
            <foreach collection="req.deptIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="req.userIds != null and req.userIds.size > 0">
            AND tu.id IN
            <foreach collection="req.userIds" item="item" index="id" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="req.minPrice != null and req.minPrice != ''">
            and bs.entrust_money &gt;= #{req.minPrice}*10000
        </if>
        <if test="req.maxPrice != null and req.maxPrice != ''">
            and bs.entrust_money &lt;= #{req.maxPrice}*10000
        </if>
        <if test="datascopesql != null and datascopesql != ''">
            ${datascopesql}
        </if>
        ORDER BY
        bs.submit_end_time,
        bs.project_id,
        bs.package_number

    </select>
    <select id="selectConferenceAddress" resultType="java.lang.String">
        SELECT
            cb.conference_address
        FROM
            t_project tp
            LEFT JOIN t_conference_booking_project bp ON tp.id = bp.project_id
            LEFT JOIN t_conference_booking cb ON cb.id = bp.booking_id
        WHERE
            cb.booking_purpose = 1
            AND tp.is_delete = 0
            AND bp.is_delete = 0
            AND cb.is_delete = 0
            AND tp.purchase_number =#{projectNumber}
            <if test="bidPackage != null &amp;&amp; bidPackage != ''">
            AND FIND_IN_SET(#{bidPackage},bp.package_nums)
            </if>
    </select>

    <select id="selectConferenceAddressNew" resultType="java.lang.String" >
        SELECT
        cb.conference_address
        FROM
        t_project tp
        LEFT JOIN t_conference_booking_project bp ON tp.id = bp.project_id
        LEFT JOIN t_conference_booking cb ON cb.id = bp.booking_id
        WHERE
        cb.booking_purpose = 1
        AND tp.is_delete = 0
        AND bp.is_delete = 0
        AND cb.is_delete = 0
        AND tp.id =#{projectId}
        <if test="bidPackage != null &amp;&amp; bidPackage != ''">
            AND FIND_IN_SET(#{bidPackage},bp.package_nums)
        </if>
    </select>

    <select id="selectConferenceAddressList" resultType="com.hzw.sunflower.dto.ConferenceAddress">
        select * from (
            SELECT
                tp.id  as projectId,
                substring_index(substring_index( bp.package_nums, ',', topic.help_topic_id + 1 ), ',',- 1 ) AS packageNum ,
                cb.conference_address as conferenceAddress
            FROM
            t_project tp
            LEFT JOIN t_conference_booking_project bp ON tp.id = bp.project_id
            LEFT JOIN t_conference_booking cb ON cb.id = bp.booking_id
            JOIN mysql.help_topic topic ON topic.help_topic_id &lt; ( length( bp.package_nums ) - length( REPLACE (
            bp.package_nums, ',', '' ) ) + 1 )
            WHERE
            cb.booking_purpose = 1
            AND tp.is_delete = 0
            AND bp.is_delete = 0
            AND cb.is_delete = 0
        ) a where 1=1
        <if test="projectId != null &amp;&amp; projectId != ''">
            AND projectId = #{projectId}
        </if>
        <if test="bidPackage != null &amp;&amp; bidPackage != ''">
            AND packageNum = #{bidPackage}
        </if>
    </select>

    <select id="queryInComeAmount" resultType="com.hzw.sunflower.controller.response.ScheduleAmountVO">

        select
            IFNULL(sum(m.yearAmount),0) yearToIncomeAmount,
            IFNULL(sum(m.monthAmount),0) monthToIncomeAmount,
            IFNULL(sum(m.dayAmount),0) dayToIncomeAmount
        from
        (
            select
                sum(IF(DATE_FORMAT(t.pay_time, '%Y') = #{req.year}, t.pay_money, 0)) AS yearAmount,
                sum(IF(DATE_FORMAT(t.pay_time, '%Y%m') = #{req.month}, t.pay_money, 0)) AS monthAmount,
                sum(IF(DATE_FORMAT(t.pay_time, '%Y-%m-%d') = #{req.day}, t.pay_money, 0)) AS dayAmount
            from
            (
                select
                    p.id as payId,
                    (case when p.pay_way = 0 then apply.tender_fee else p.pay_money end) as pay_money,
                    (case when p.pay_way = 0 then p.updated_time else p.pay_time end) as pay_time,
                    GROUP_CONCAT(rud.department_id)
                from
                    t_project tp
                LEFT JOIN t_project_entrust_user eu ON tp.id = eu.project_id
                LEFT JOIN r_user_department rud on eu.user_id = rud.user_id  and eu.department_id = rud.department_id
                left join t_pay_all p on tp.id = p.project_id
                left join (
                    SELECT
                    a.pay_id,
                    max(a.sub_id) as sub_id,
                    max(pbs.tender_fee) as tender_fee
                    FROM
                    t_apply_info a
                    LEFT JOIN t_project_bid_section pbs ON a.sub_id = pbs.id
                    WHERE
                    a.IS_DELETE = 0
                    AND pbs.IS_DELETE = 0
                    and pbs.tender_fee_type != 0
                    AND a.download_flag = 1
                    AND a.pay_id IS NOT NULL
                    GROUP BY a.pay_id
                ) apply on p.id = apply.pay_id
                where
                    1 = 1
                    and tp.is_delete = 0
                    and p.is_delete = 0
                    and p.pay_status in (2,3,5)
                    AND rud.is_delete = 0
                    AND eu.is_delete = 0
                    AND eu.type = 1
                    <if test="req.deptIds != null and req.deptIds.size >0 ">
                        and rud.department_id in
                        <foreach collection="req.deptIds" index="index" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </if>
                    <if test="req.userIds != null and req.userIds.size > 0">
                        AND rud.user_id IN
                        <foreach collection="req.userIds" item="item" index="id" open="(" close=")" separator=",">
                            #{item}
                        </foreach>
                    </if>
                    <if test="datascopesql != null and datascopesql != ''">
                        ${datascopesql}
                    </if>
                GROUP BY
                    p.id,
                    p.pay_way,
                    p.pay_money,
                    p.pay_time,
                    apply.tender_fee,
                    p.updated_time
            ) t
            where
                1 = 1
                AND DATE_FORMAT( t.pay_time, '%Y' ) = #{req.year}

            UNION ALL

            select
                sum(IF(DATE_FORMAT(t.seal_time, '%Y') = #{req.year}, t.ss_agency_fee, 0)) AS yearAmount,
                sum(IF(DATE_FORMAT(t.seal_time, '%Y%m') = #{req.month}, t.ss_agency_fee, 0)) AS monthAmount,
                sum(IF(DATE_FORMAT(t.seal_time, '%Y-%m-%d') = #{req.day}, t.ss_agency_fee, 0)) AS dayAmount
            from
            (
                SELECT
                    (
                        CASE
                        WHEN bwp.currency_type = 1 THEN
                        ( bwp.ss_agency_fee * #{req.dollar} )
                        WHEN bwp.currency_type = 2 THEN
                        ( bwp.ss_agency_fee * #{req.euro} )
                        WHEN bwp.currency_type = 3 THEN
                        ( bwp.ss_agency_fee * #{req.yen} ) ELSE bwp.ss_agency_fee
                        END
                    ) AS ss_agency_fee,
                    pbs.id as sectionId,
                    bwp.id as peopleId,
                    bwn.id as noticeId,
                    bwn.seal_time,
                    GROUP_CONCAT(DISTINCT rud.department_id)
                FROM
                    t_project_bid_section pbs
                left JOIN t_project tp on pbs.project_id = tp.id
                LEFT JOIN t_project_entrust_user eu ON tp.id = eu.project_id
                LEFT JOIN r_user_department rud on eu.user_id = rud.user_id  and eu.department_id = rud.department_id
                LEFT JOIN t_bid_win_notice bwn ON pbs.project_id = bwn.project_id AND bwn.bid_round = pbs.bid_round
                LEFT JOIN t_bid_win_people bwp ON bwp.winbid_id = bwn.id AND bwp.win_people_type = 1 AND bwp.is_win = 1
                WHERE
                    1 = 1
                    and tp.is_delete = 0
                    AND pbs.is_delete = 0
                    AND pbs.agency_fee_type != 0
                    AND pbs.bid_round = 2
                    AND bwn.`status` = 5
                    AND bwn.is_delete = 0
                    AND bwp.is_delete = 0
                    AND FIND_IN_SET( pbs.id, bwn.section_id )
                    AND bwp.ss_agency_fee != 0
                    AND eu.is_delete = 0
                    AND eu.type = 1
                    AND rud.is_delete = 0
                    AND DATE_FORMAT( bwn.seal_time, '%Y' ) = #{req.year}
                    <if test="req.deptIds != null and req.deptIds.size >0 ">
                        and rud.department_id in
                        <foreach collection="req.deptIds" index="index" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </if>
                    <if test="req.userIds != null and req.userIds.size > 0">
                        AND rud.user_id IN
                        <foreach collection="req.userIds" item="item" index="id" open="(" close=")" separator=",">
                            #{item}
                        </foreach>
                    </if>
                    <if test="datascopesql != null and datascopesql != ''">
                        ${datascopesql}
                    </if>
                GROUP BY
                    bwp.currency_type,
                    bwp.ss_agency_fee,
                    bwn.seal_time,
                    pbs.id,
                    bwp.id,
                    bwn.id
            ) t
        ) m
    </select>
    <select id="selectBidOpenDetailListAll" resultType="com.hzw.sunflower.entity.BidOpenDetail">
        SELECT DISTINCT
            bs.id AS sectionId,
            bs.project_id,
            tp.purchase_name AS purchaseName,
            tp.project_name AS projectName,
            tp.purchase_number AS projectNumber,
            tp.created_time,
            bs.package_number AS bidPackage,
            tu.user_name AS userName,
            td.department_name AS departmentName,
            tp.principal_company AS principalCompany,
            bs.entrust_money AS entrustMoney,
            bs.submit_end_time AS openTime,
            bs.entrust_currency AS entrustCurrency,
            dic.`value` AS exchangeRate
        FROM
            t_project_bid_section bs
            LEFT JOIN t_project tp ON bs.project_id = tp.id
            LEFT JOIN t_project_entrust_user eu ON bs.project_id = eu.project_id
            LEFT JOIN r_user_department rud on eu.user_id = rud.user_id  and eu.department_id = rud.department_id
            LEFT JOIN t_department td ON td.id = rud.department_id
            LEFT JOIN t_user tu ON tu.id = rud.user_id
            LEFT JOIN t_dictionary dic ON bs.entrust_currency = dic.id
        WHERE
            1 = 1
            AND bs.is_delete = 0
            AND tp.is_delete = 0
            AND !(tp.re_tender = 1 AND bs.`status` &gt;= 70)
            AND eu.type = 1
            AND eu.is_delete = 0
            AND bs.submit_end_time_type = 1
            AND bs.submit_end_time IS NOT NULL
        <if test='req.bidStatus != null and req.bidStatus =="1"'>
            AND ((bs.`status` &lt; 70) or (bs.status &gt;= 70 and bs.former_abnormal_status &gt;= 30 and bs.former_abnormal_status &lt; 70 )
            )
        </if>
        <if test="startDate !=null and startDate !=''">
            AND DATE_FORMAT( bs.submit_end_time, '%Y-%m-%d' ) &gt;= #{startDate}
        </if>
        <if test="endDate !=null and endDate !=''">
            AND DATE_FORMAT( bs.submit_end_time, '%Y-%m-%d' ) &lt;= #{endDate}
        </if>
        <if test="keyWords != null and keyWords != ''">
            AND (
            tp.purchase_name LIKE '%${keyWords}%'
            or tu.user_name like '%${keyWords}%'
            )
        </if>
        <if test="req.deptIds != null and req.deptIds.size >0 ">
            and td.id in
            <foreach collection="req.deptIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="req.userIds != null and req.userIds.size > 0">
            AND tu.id IN
            <foreach collection="req.userIds" item="item" index="id" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="datascopesql != null and datascopesql != ''">
            ${datascopesql}
        </if>
        ORDER BY
            bs.submit_end_time,
            bs.project_id,
            bs.package_number
    </select>
</mapper>
