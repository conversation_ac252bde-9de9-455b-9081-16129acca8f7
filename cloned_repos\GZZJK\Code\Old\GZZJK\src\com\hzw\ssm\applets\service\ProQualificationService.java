package com.hzw.ssm.applets.service;

import com.hzw.ssm.applets.dao.ProQualificationMapper;
import com.hzw.ssm.applets.entity.ProQualification;
import com.hzw.ssm.applets.exception.ExceptionEnum;
import com.hzw.ssm.applets.utils.RandomCode;
import com.hzw.ssm.applets.utils.ResultJson;
import com.hzw.ssm.expert.dao.ExpertInfoMapper;
import com.hzw.ssm.expert.entity.ExpertInfoEntity;
import com.hzw.ssm.expert.entity.ExpertUpdateRecordEntity;
import com.hzw.ssm.expert.service.ExpertInfoService;
import com.hzw.ssm.fw.base.BaseService;
import com.hzw.ssm.fw.util.CommUtil;
import com.hzw.ssm.sys.user.dao.UserMapper;
import com.hzw.ssm.sys.user.entity.UserEntity;
import com.hzw.ssm.util.string.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <AUTHOR>
 * @Date 2021-02-03 10:14
 * @Version 1.0
 */
@Service
public class ProQualificationService extends BaseService {

    private static Logger logger = Logger.getLogger(ProQualificationService.class);

    @Autowired
    private ProQualificationMapper proQualificationMapper;
    @Autowired
    private ExpertInfoMapper expertInfoMapper;

    @Autowired
    private ExpertInfoService expertInfoService;
    @Autowired
    private UserMapper userMapper;

    public ResultJson insertProQualification(ProQualification proQualification) {

        try {
            List<ProQualification> proQualifications = proQualificationMapper.getProQualifications(proQualification);
            // 条件满足，则pc有数据,此时新增
            if (null != proQualifications && proQualifications.size() > 0 && proQualifications.size() == 3) {
                for (ProQualification item : proQualifications) {

                    String certificate = item.getCertificate();
                    if (StringUtils.isEmpty(certificate)) {

                        //执业资格名称不允许重复
                        ProQualification qualification = new ProQualification();
                        qualification.setUser_id(proQualification.getUser_id());
                        qualification.setCertificate(proQualification.getCertificate());
                        qualification = proQualificationMapper.queryProQualification(qualification);
                        if (null != qualification) {
                            return new ResultJson(ExceptionEnum.QUALIFICATION_EXIST.getCode(), ExceptionEnum.QUALIFICATION_EXIST.getMessage());
                        }

                        proQualification.setId(item.getId());
                        proQualification.setDelete_flag(0);
                        String picture = proQualification.getPicture();
                        proQualification.setCertificate_fileid(picture);
                        proQualification.setOld_certificate(picture.substring(picture.lastIndexOf("/") + 1));
                        int updates = proQualificationMapper.updateProQualification(proQualification);
                        if (updates > 0) {

                            ExpertInfoEntity infoEntity = new ExpertInfoEntity();
                            infoEntity.setUser_id(proQualification.getUser_id());
                            ExpertInfoEntity expertInfoByUserIdOrId = expertInfoMapper.getExpertInfoByUserIdOrId(infoEntity);
                            long status = expertInfoByUserIdOrId.getStatus();
                            if (3 == status || 8 == status) {
                                expertInfoByUserIdOrId.setStatus(10L);
                                expertInfoMapper.updateExpertInfoById(expertInfoByUserIdOrId);
                            }
                            // 新增记录
                            if (3 == status || 8 == status || 4 == status || 10 == status) {
                                try {
                                    infoSaveRecord(proQualification);
                                } catch (Exception e) {
                                    e.printStackTrace();
                                }
                            }
                            List<Map<String, Object>> list = new ArrayList<>();
                            Map<String, Object> map = new HashMap<>();
                            map.put("id", item.getId());
                            list.add(map);
                            return new ResultJson(list);
                        }
                    }
                }
            } else {

                //执业资格名称不允许重复
                ProQualification qualification = new ProQualification();
                qualification.setUser_id(proQualification.getUser_id());
                qualification.setCertificate(proQualification.getCertificate());
                qualification = proQualificationMapper.queryProQualification(qualification);
                if (null != qualification) {
                    return new ResultJson(ExceptionEnum.QUALIFICATION_EXIST.getCode(), ExceptionEnum.QUALIFICATION_EXIST.getMessage());
                }

                List<Map<String, Object>> list = new ArrayList<>();
                Map<String, Object> map = new HashMap<>();

                String id = RandomCode.getId();
                proQualification.setId(id);
                proQualification.setDelete_flag(0);
                // 处理图片
                // List<String> pictures = proQualification.getPictures();
                // String json = JSON.toJSONString(pictures);
                String picture = proQualification.getPicture();
                proQualification.setCertificate_fileid(picture);
                proQualification.setOld_certificate(picture.substring(picture.lastIndexOf("/") + 1));
                int updates = proQualificationMapper.insertProQualification(proQualification);
                if (updates > 0) {
                    ExpertInfoEntity infoEntity = new ExpertInfoEntity();
                    infoEntity.setUser_id(proQualification.getUser_id());
                    ExpertInfoEntity expertInfoByUserIdOrId = expertInfoMapper.getExpertInfoByUserIdOrId(infoEntity);
                    long status = expertInfoByUserIdOrId.getStatus();
                    if (3 == status || 8 == status) {
                        expertInfoByUserIdOrId.setStatus(10L);
                        expertInfoMapper.updateExpertInfoById(expertInfoByUserIdOrId);
                    }
                    // 新增记录
                    if (3 == status || 8 == status || 4 == status || 10 == status) {
                        try {
                            infoSaveRecord(proQualification);
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                    map.put("id", id);
                    list.add(map);
                    return new ResultJson(list);
                }
            }
        } catch (Exception e) {
            logger.error(e.getMessage());
        }
        return new ResultJson(ExceptionEnum.SERVER_IS_BUSY.getCode(), ExceptionEnum.SERVER_IS_BUSY.getMessage());
    }

    public ResultJson updateProQualification(ProQualification proQualification) {

        try {
            //执业资格名称不允许重复
            ProQualification qualification = new ProQualification();
            String user_id = proQualification.getUser_id();
            qualification.setUser_id(user_id);
            qualification.setCertificate(proQualification.getCertificate());
            qualification = proQualificationMapper.queryProQualification(qualification);
            if (null != qualification && !qualification.getId().equals(proQualification.getId())) {
                return new ResultJson(ExceptionEnum.QUALIFICATION_EXIST.getCode(), ExceptionEnum.QUALIFICATION_EXIST.getMessage());
            }

            ExpertInfoEntity infoEntity = new ExpertInfoEntity();
            infoEntity.setUser_id(proQualification.getUser_id());
            ExpertInfoEntity expertInfoByUserIdOrId = expertInfoMapper.getExpertInfoByUserIdOrId(infoEntity);
            long status = expertInfoByUserIdOrId.getStatus();
            if (3 == status || 8 == status) {
                expertInfoByUserIdOrId.setStatus(10L);
                expertInfoMapper.updateExpertInfoById(expertInfoByUserIdOrId);
            }

            ProQualification info = new ProQualification();
            info.setId(proQualification.getId());
            info = proQualificationMapper.getProQualification(info);

            List<Map<String, Object>> list = new ArrayList<>();
            Map<String, Object> map = new HashMap<>();
            // 处理图片
//            List<String> pictures = proQualification.getPictures();
//            String json = JSON.toJSONString(pictures);
            proQualification.setCertificate_fileid(proQualification.getPicture());
            String picture = proQualification.getPicture();
            proQualification.setOld_certificate(picture.substring(picture.lastIndexOf("/") + 1));
            int updates = proQualificationMapper.updateProQualification(proQualification);
            if (updates > 0) {
                // 修改记录
                if (3 == status || 8 == status || 4 == status || 10 == status) {
                    try {
                        infoRecord(info, proQualification);
                    } catch (Exception e) {
                        System.out.println("记录出错!");
                    }
                }
                map.put("id", proQualification.getId());
                list.add(map);
                return new ResultJson(list);
            }
        } catch (Exception e) {
            logger.error(e.getMessage());
        }
        return new ResultJson(ExceptionEnum.SERVER_IS_BUSY.getCode(), ExceptionEnum.SERVER_IS_BUSY.getMessage());
    }

    private void infoSaveRecord(ProQualification after) {

        UserEntity userEntity = userMapper.selectUserById(after.getUser_id());
        String role_id;
        if (userEntity != null) {
            role_id = StringUtils.toStringNull(userEntity.getRole_id());
        } else {
            role_id = "无";
        }

        if (StringUtils.isNotEmpty(after.getOld_certificate())) {
            // 执业资格
            addExpertUpdateRecord(after.getUser_id(), "执业职称名称", null, StringUtils.toStringNull(after.getCertificate()), role_id, after.getUser_id(), "执业信息", 0);
        }
        if (StringUtils.isNotEmpty(after.getCertificate_no())) {
            // 资格证书号
            addExpertUpdateRecord(after.getUser_id(), "执业资格证书号", null, StringUtils.toStringNull(after.getCertificate_no()), role_id, after.getUser_id(), "执业信息", 0);

        }
        if (StringUtils.isNotEmpty(after.getGetTime())) {
            //获得时间
            addExpertUpdateRecord(after.getUser_id(), "执业资格证书获得时间", null, StringUtils.toStringNull(after.getGetTime()), role_id, after.getUser_id(), "执业信息", 0);
        }

        if (StringUtils.isNotEmpty(after.getCertificate_fileid())) {
            //资格证书附件名称 资格证书复印件
            String afterStyle = "<a style=\"color:blue\" onclick=\"javascript:picPreview(530,450,'/GZZJK/" + StringUtils.toStringNull(after.getCertificate_fileid()) + "')\">" + StringUtils.toStringNull(after.getOld_certificate()) + "</a>";
            addExpertUpdateRecord(after.getUser_id(), "执业资格证书", null, afterStyle, role_id, after.getUser_id(), "职称信息", 0);
        }
    }

    private void infoRecord(ProQualification before, ProQualification after) {

        if (before != null && after != null) {

            UserEntity userEntity = userMapper.selectUserById(before.getUser_id());
            String role_id;
            if (userEntity != null) {
                role_id = StringUtils.toStringNull(userEntity.getRole_id());
            } else {
                role_id = "无";
            }

            if (StringUtils.isNotEmpty(after.getOld_certificate()) && !StringUtils.toStringNull(after.getOld_certificate()).equalsIgnoreCase(StringUtils.toStringNull(before.getOld_certificate()))) {
                // 执业资格
                addExpertUpdateRecord(before.getUser_id(), "执业职称名称", StringUtils.toStringNull(before.getCertificate()), StringUtils.toStringNull(after.getCertificate()), role_id, before.getUser_id(), "执业信息", 0);
            }
            if (StringUtils.isNotEmpty(after.getCertificate_no()) && !StringUtils.toStringNull(after.getCertificate_no()).equalsIgnoreCase(StringUtils.toStringNull(before.getCertificate_no()))) {
                // 资格证书号
                addExpertUpdateRecord(before.getUser_id(), "执业资格证书号", StringUtils.toStringNull(before.getCertificate_no()), StringUtils.toStringNull(after.getCertificate_no()), role_id, before.getUser_id(), "执业信息", 0);
            }
            if (StringUtils.isNotEmpty(after.getGetTime()) && !StringUtils.toStringNull(StringUtils.toStringNull(after.getGetTime())).equalsIgnoreCase(StringUtils.toStringNull(before.getGetTime()))) {
                //获得时间
                addExpertUpdateRecord(before.getUser_id(), "执业资格证书获得时间", StringUtils.toStringNull(before.getGetTime()), StringUtils.toStringNull(after.getGetTime()), role_id, before.getUser_id(), "执业信息", 0);
            }

            if (StringUtils.isNotEmpty(after.getCertificate_fileid()) && !StringUtils.toStringNull(after.getCertificate_fileid()).equalsIgnoreCase(StringUtils.toStringNull(before.getCertificate_fileid()))) {
                //资格证书附件名称 资格证书复印件
                String befoStyle = "<a style=\"color:blue\" onclick=\"javascript:picPreview(530,450,'/GZZJK/" + StringUtils.toStringNull(before.getCertificate_fileid()) + "')\">" + StringUtils.toStringNull(before.getOld_certificate()) + "</a>";
                String afterStyle = "<a style=\"color:blue\" onclick=\"javascript:picPreview(530,450,'/GZZJK/" + StringUtils.toStringNull(after.getCertificate_fileid()) + "')\">" + StringUtils.toStringNull(after.getOld_certificate()) + "</a>";
                addExpertUpdateRecord(before.getUser_id(), "执业资格证书", befoStyle, afterStyle, role_id, before.getUser_id(), "职称信息", 0);
            }
        }
    }

    /**
     * 新增专家信息修改记录
     *
     * @param user_id        用户编号
     * @param item           修改项
     * @param content_before 修改前内容
     * @param content_after  修改后内容
     * @param modify_role    修改角色
     * @param modify_user    修改人
     * @param modify_reason  修改原因
     * @param batch_number   修改批次编号
     */
    public void addExpertUpdateRecord(String user_id, String item, String content_before, String content_after, String modify_role, String modify_user, String modify_reason, int batch_number) {
        ExpertUpdateRecordEntity record = new ExpertUpdateRecordEntity();
        record.setId(CommUtil.getKey());
        record.setUser_id(user_id);
        record.setItem(item);
        record.setContent_before(content_before);
        record.setContent_after(content_after);
        record.setModify_role(modify_role);
        record.setModify_user(modify_user);
        record.setModify_reason(modify_reason);
        record.setBatch_number(batch_number);
        record.setModify_time(new Date());
        record.setDelete_flag(0);
        expertInfoMapper.addExpertUpdateRecord(record);
    }

    public ResultJson deleteProQualification(ProQualification proQualification) {

        try {
            List<Map<String, Object>> list = new ArrayList<>();
            Map<String, Object> map = new HashMap<>();
            ProQualification info = proQualificationMapper.getProQualification(proQualification);

            int updates = proQualificationMapper.deleteProQualification(proQualification);
            if (updates > 0) {
                // 如果用户已审核通过，修改需要重新提交审核
                ExpertInfoEntity infoEntity = new ExpertInfoEntity();
                infoEntity.setUser_id(info.getUser_id());
                ExpertInfoEntity entity = expertInfoMapper.getExpertInfoByUserIdOrId(infoEntity);
                Long status = entity.getStatus();
                if (3 == status || 8 == status) {
                    entity.setStatus(10L);
                    expertInfoMapper.updateExpertInfoById(entity);
                }
                // 删除记录
                if (3 == status || 8 == status || 4 == status || 10 == status) {
                    try {
                        deleteProQualificationRecord(info);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }

                map.put("id", proQualification.getId());
                list.add(map);
                return new ResultJson(list);
            }
        } catch (Exception e) {
            logger.error(e.getMessage());
        }
        return new ResultJson(ExceptionEnum.SERVER_IS_BUSY.getCode(), ExceptionEnum.SERVER_IS_BUSY.getMessage());
    }

    private void deleteProQualificationRecord(ProQualification info) {

        UserEntity userEntity = userMapper.selectUserById(info.getUser_id());
        String content_after = "删除了【" + info.getCertificate() + "】执业资格信息";
        addExpertUpdateRecord(info.getUser_id(), "执业资格信息", null, content_after, userEntity.getRole_id(), info.getUser_id(), "执业资格信息", 0);

    }

    public ResultJson getProQualification(ProQualification proQualification) {

        List<ProQualification> list = new ArrayList<>();
        ProQualification qualification = proQualificationMapper.getProQualification(proQualification);
        // 处理图片
        String picture = qualification.getCertificate_fileid();
//        List<String> pictures = JSON.parseArray(picture, String.class);
        qualification.setPicture(picture);
        list.add(qualification);
        return new ResultJson(list);
    }

    public ResultJson getProQualifications(ProQualification proQualification) {

        List<ProQualification> qualifications = new ArrayList<>();

        List<ProQualification> proQualifications = proQualificationMapper.getProQualifications(proQualification);
        for (ProQualification qualification : proQualifications) {
            // 处理图片
            String picture = qualification.getCertificate_fileid();
            //List<String> pictures = JSON.parseArray(picture, String.class);
            qualification.setPicture(picture);

            // pc 执业资格新增默认就是会5条数据，所以需要过滤数据
            String certificate = qualification.getCertificate();
            if (!StringUtils.isEmpty(certificate)) {
                qualifications.add(qualification);
            }
        }
        return new ResultJson(qualifications);
    }

}
