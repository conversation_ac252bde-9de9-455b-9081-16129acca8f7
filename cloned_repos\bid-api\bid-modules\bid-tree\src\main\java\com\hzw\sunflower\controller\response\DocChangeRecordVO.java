package com.hzw.sunflower.controller.response;

import com.hzw.sunflower.dto.DocChangeRecordDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * TDocChangeRecord 返回实体
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@ApiModel(description = "TDocChangeRecord ")
@Data
public class DocChangeRecordVO extends DocChangeRecordDTO {

    @ApiModelProperty(value = "变更记录对比标识", position = 998)
    private Map flag;

    @ApiModelProperty(value = "附件信息集合", position = 999)
    private List<FileVo> annexList;

    @ApiModelProperty(value = "开标一览表信息集合", position = 1000)
    private List<FileVo> scheduleList;
}
