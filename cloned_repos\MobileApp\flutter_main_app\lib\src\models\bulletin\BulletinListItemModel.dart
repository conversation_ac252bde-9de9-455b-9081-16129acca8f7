///***
/// * 公告公示 - 数据模型
/// */

import 'package:json_annotation/json_annotation.dart';

part 'BulletinListItemModel.g.dart';

@JsonSerializable(explicitToJson: true)
class BulletinListItemModel {

  BulletinListItemModel({
    this.bidOpenTime,
    this.bidSectionID,
    this.bulletinID,
    this.bulletinName,
    this.bulletinTypeName,
    this.classifyName,
    this.currentPageCursor,
    this.docGetStartTime,
    this.docReferEndTime,
    this.isFav,
    this.noticeSendTime,
    this.platformCode,
    this.platformName,
    this.projectID,
    this.regionCode,
    this.regionName,
    this.ruleSpecial,
    this.uuid,
    this.viewCountOnPC,
    this.viewCountOnWeAPP,
    this.webviewURL,
    this.webviewURL_2,
  });

  @Json<PERSON>ey(name: 'BidOpenTime')
  final String bidOpenTime;

  @<PERSON>son<PERSON><PERSON>(name: 'bidSectionId')
  final String bidSectionID;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'bulletinID')
  final String bulletinID;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'BulletinName')
  final String bulletinName;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'BulletinTypeName')
  final String bulletinTypeName;

  @JsonKey(name: 'ClassifyName')
  final String classifyName;

  @JsonKey(name: 'CurrentPage')
  final String currentPageCursor;

  @JsonKey(name: 'docGetStartTime')
  final String docGetStartTime;

  @JsonKey(name: 'docReferEndTime')
  final String docReferEndTime;

  @JsonKey(name: 'IsFav')
  final bool isFav;

  @JsonKey(name: 'NoticeSendTime')
  final String noticeSendTime;

  @JsonKey(name: 'platformcode')
  final String platformCode;

  @JsonKey(name: 'platformName')
  final String platformName;

  @JsonKey(name: 'ProjectId')
  final String projectID;

  @JsonKey(name: 'RegionCode')
  final String regionCode;

  String regionName;

  @JsonKey(name: 'RuleSpecial')
  final String ruleSpecial;

  @JsonKey(name: 'UUID')
  final String uuid;

  @JsonKey(name: 'ViewPcCount')
  final int viewCountOnPC;

  @JsonKey(name: 'ViewMiniCount')
  final int viewCountOnWeAPP;

  @JsonKey(name: 'realUrl')
  final String webviewURL;

  @JsonKey(name: 'URL')
  final String webviewURL_2;

  factory BulletinListItemModel.fromJson(Map<String, dynamic> json) => _$BulletinListItemModelFromJson(json);

  Map<String, dynamic> toJson() => _$BulletinListItemModelToJson(this);
  
}
