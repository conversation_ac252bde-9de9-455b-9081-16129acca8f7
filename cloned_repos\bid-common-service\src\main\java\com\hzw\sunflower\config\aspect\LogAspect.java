package com.hzw.sunflower.config.aspect;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.hzw.sunflower.common.annotation.Log;
import com.hzw.sunflower.common.annotation.ParamsNotNull;
import com.hzw.sunflower.constant.GeneralConstants;
import com.hzw.sunflower.constant.MessageConstants;
import com.hzw.sunflower.constant.UserConstants;
import com.hzw.sunflower.constant.constantenum.BusinessStatus;
import com.hzw.sunflower.entity.JwtUser;
import com.hzw.sunflower.entity.OperationLog;
import com.hzw.sunflower.exception.ParamsNotNullException;
import com.hzw.sunflower.manager.AsyncManager;
import com.hzw.sunflower.manager.factory.AsyncFactory;
import com.hzw.sunflower.util.ServletUtils;
import com.hzw.sunflower.util.ip.IpUtils;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.*;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint;
import org.springframework.aop.framework.ReflectiveMethodInvocation;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpMethod;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.validation.BindingResult;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.HandlerMapping;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.lang.reflect.Parameter;
import java.util.*;

/**
 * 操作日志记录处理
 *
 * <AUTHOR>
 */
@Aspect
@Component
@Slf4j
public class LogAspect {

    @Value("${logging.enabled}")
    private Boolean enabled;

    private static final String ARG = "arg";

    // 配置织入点
    @Pointcut("execution(* com.hzw.sunflower.controller..*Controller.*(..))")
    public void declearJoinPointExpression() {
    }

    // 配置织入点
    @Pointcut("@annotation(com.hzw.sunflower.common.annotation.Log)")
    public void logPointCut() {
    }

    /**
     * 该标签声明次方法是一个前置通知：在目标方法开始之前执行
     *
     * @param joinPoint
     */
    @Before("declearJoinPointExpression()")
    public void beforeMethod(JoinPoint joinPoint) throws Exception {
        List<Class> classes = new ArrayList<>();
        List<Object> args = Arrays.asList(joinPoint.getArgs());
        MethodInvocationProceedingJoinPoint methPotin =
                (MethodInvocationProceedingJoinPoint) joinPoint;
        Field proxy = methPotin.getClass().getDeclaredField("methodInvocation");
        proxy.setAccessible(true);
        ReflectiveMethodInvocation j = (ReflectiveMethodInvocation) proxy.get(methPotin);
        Method method = j.getMethod();
        //不写注释不插入日志表
        if (method.getAnnotation(ApiOperation.class) != null) {
//            String logstr = method.getAnnotation(ApiOperation.class).value();
            methodEntityFieldEmpty(method, classes, args);
        }
    }

    /**
     * 处理完请求后执行
     *
     * @param joinPoint 切点
     */
    @AfterReturning(pointcut = "logPointCut()", returning = "jsonResult")
    public void doAfterReturning(JoinPoint joinPoint, Object jsonResult) {
        if (enabled) {
            handleLog(joinPoint, null, jsonResult);
        }
    }

    /**
     * 拦截异常操作
     *
     * @param joinPoint 切点
     * @param e         异常
     */
    @AfterThrowing(value = "logPointCut()", throwing = "e")
    public void doAfterThrowing(JoinPoint joinPoint, Exception e) {
        if (enabled) {
            handleLog(joinPoint, e, null);
        }
    }

    /**
     * @param joinPoint
     * @param e
     * @param jsonResult
     * <AUTHOR>
     * @Description
     * @Return void
     * @Date 2021-04-28 9:51
     */
    protected void handleLog(final JoinPoint joinPoint, final Exception e, Object jsonResult) {
        try {
            // 获得注解
            Log controllerLog = getAnnotationLog(joinPoint);
            if (controllerLog == null) {
                return;
            }

            // 获取当前的用户
            Object principal = SecurityContextHolder.getContext().getAuthentication().getPrincipal();
            JwtUser jwtUser = null;
            if (principal instanceof JwtUser) {
                jwtUser = (JwtUser) principal;
            }

            // *========数据库日志=========*//
            OperationLog operationLog = new OperationLog();
            operationLog.setStatus(BusinessStatus.SUCCESS.ordinal());
            // 请求的地址
            String ip = IpUtils.getIpAddr(ServletUtils.getRequest());
            // User-Agent:
            //Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
            String userAgent = ServletUtils.getRequest().getHeader("User-Agent");

            Enumeration<String> headerNames = ServletUtils.getRequest().getHeaderNames();
            StringBuilder header = new StringBuilder();
            headerNames.asIterator().forEachRemaining(headerName -> {
                header.append(headerName)
                                .append(":").append(ServletUtils.getRequest().getHeader(headerName)).append("\n");
            });
            operationLog.setHeadMessage(String.valueOf(header));
            // 确保 User-Agent 信息已成功获取
            if (StringUtils.isNotBlank(userAgent)) {
                if(StringUtils.isNotBlank(userAgent) ){
                    if(userAgent.length() > 2000){
                        userAgent = userAgent.substring(0, 2000);
                    }
                    operationLog.setUserAgent(userAgent);
                }
            }
            operationLog.setOperIp(ip);
            // 返回参数
            if (null != jsonResult) {
                operationLog.setJsonResult(JSON.toJSONString(jsonResult));
            }

            operationLog.setOperUrl(ServletUtils.getRequest().getRequestURI());
            if (jwtUser != null) {
                operationLog.setOperName(jwtUser.getUserName());
                operationLog.setOperId(jwtUser.getUserId());
                operationLog.setCompanyId(jwtUser.getCompanyId());
            }

            if (e != null) {
                operationLog.setStatus(BusinessStatus.FAIL.ordinal());
                operationLog.setErrorMsg(StringUtils.substring(e.getMessage(), 0, UserConstants.SUBSTRING_LENGTH));
            }
            // 设置方法名称
            String className = joinPoint.getTarget().getClass().getName();
            String methodName = joinPoint.getSignature().getName();
            operationLog.setMethod(className + "." + methodName + "()");
            // 设置请求方式
            operationLog.setRequestMethod(ServletUtils.getRequest().getMethod());
            // 处理设置注解上的参数
            getControllerMethodDescription(joinPoint, controllerLog, operationLog);
            // 保存数据库
            AsyncManager.me().execute(AsyncFactory.recordOper(operationLog));
        } catch (Exception exp) {
            // 记录本地异常日志
            log.error("==前置通知异常==");
            log.error("异常信息:{}", exp.getMessage());
            exp.printStackTrace();
        }
    }

    /**
     * 获取注解中对方法的描述信息 用于Controller层注解
     *
     * @param log          日志
     * @param operationLog 操作日志
     * @throws Exception
     */
    public void getControllerMethodDescription(JoinPoint joinPoint, Log log, OperationLog operationLog) throws Exception {
        // 设置action动作
        operationLog.setBusinessType(log.businessType().ordinal());
        // 设置标题
        operationLog.setTitle(log.title());
        // 设置描述
        operationLog.setDescription(log.description());
        // 设置操作人类别
        operationLog.setOperatorType(log.operatorType().ordinal());
        // 是否需要保存request，参数和值
        if (log.isSaveRequestData()) {
            // 获取参数的信息，传入到数据库中。
            setRequestValue(joinPoint, operationLog);
        }
    }

    /**
     * 获取请求的参数，放到log中
     *
     * @param operationLog 操作日志
     * @throws Exception 异常
     */
    private void setRequestValue(JoinPoint joinPoint, OperationLog operationLog) throws Exception {
        String requestMethod = operationLog.getRequestMethod();
        if (HttpMethod.PUT.name().equals(requestMethod) || HttpMethod.POST.name().equals(requestMethod)) {
            String params = argsArrayToString(joinPoint.getArgs());
            operationLog.setOperParam(StringUtils.substring(params, 0, UserConstants.SUBSTRING_LENGTH));
        } else {
            Map<?, ?> paramsMap = (Map<?, ?>) ServletUtils.getRequest().getAttribute(HandlerMapping.URI_TEMPLATE_VARIABLES_ATTRIBUTE);
            operationLog.setOperParam(StringUtils.substring(paramsMap.toString(), 0, UserConstants.SUBSTRING_LENGTH));
        }
    }

    /**
     * 是否存在注解，如果存在就获取
     */
    private Log getAnnotationLog(JoinPoint joinPoint) throws Exception {
        Signature signature = joinPoint.getSignature();
        MethodSignature methodSignature = (MethodSignature) signature;
        Method method = methodSignature.getMethod();

        if (method != null) {
            return method.getAnnotation(Log.class);
        }
        return null;
    }

    /**
     * 参数拼装
     */
    private String argsArrayToString(Object[] paramsArray) {
        String params = "";
        if (paramsArray != null && paramsArray.length > 0) {
            for (int i = 0; i < paramsArray.length; i++) {
                if (!isFilterObject(paramsArray[i])) {
                    Object jsonObj = JSON.toJSON(paramsArray[i]);
                    params += jsonObj.toString() + " ";
                }
            }
        }
        return params.trim();
    }

    /**
     * 判断是否需要过滤的对象。
     *
     * @param o 对象信息。
     * @return 如果是需要过滤的对象，则返回true；否则返回false。
     */
    @SuppressWarnings("rawtypes")
    public boolean isFilterObject(final Object o) {
        Class<?> clazz = o.getClass();
        if (clazz.isArray()) {
            return clazz.getComponentType().isAssignableFrom(MultipartFile.class);
        } else if (Collection.class.isAssignableFrom(clazz)) {
            Collection collection = (Collection) o;
            for (Iterator iter = collection.iterator(); iter.hasNext(); ) {
                return iter.next() instanceof MultipartFile;
            }
        } else if (Map.class.isAssignableFrom(clazz)) {
            Map map = (Map) o;
            for (Iterator iter = map.entrySet().iterator(); iter.hasNext(); ) {
                Map.Entry entry = (Map.Entry) iter.next();
                return entry.getValue() instanceof MultipartFile;
            }
        }
        return o instanceof MultipartFile || o instanceof HttpServletRequest || o instanceof HttpServletResponse
                || o instanceof BindingResult;
    }

    /**
     * @param method  方法
     * @param classes 类
     * @param args    参数
     * <AUTHOR>
     * @Description 获取请求的方法和参数
     * @Return void
     * @Date 2021/4/28 10:28
     */
    private void methodEntityFieldEmpty(Method method, List<Class> classes, List<Object> args) throws Exception {
        //获取请求参数对象
        Parameter[] parameters = method.getParameters();
        for (Parameter parameter : parameters) {
            //不是对象就忽略
            if (!parameter.getName().contains(ARG)) {
                Class<?>[] parameterTypes = parameter.getDeclaringExecutable().getParameterTypes();
                for (Class<?> parameterType : parameterTypes) {
                    //对象实例化
                    Class aClass = Class.forName(parameterType.getName());
                    classes.add(aClass);
                }
            }
        }
        //验证是否为空
        paramsNotNull(classes, args);
    }

    /**
     * @param classes 类
     * @param args    参数
     * <AUTHOR>
     * @Description 判断实体字段
     * @Return void
     * @Date 2021/4/28 10:30
     */
    private void paramsNotNull(List<Class> classes, List<Object> args) throws Exception {
        Map<String, String> map = new HashMap<>();
        //循环多个类
        for (Class aClass : classes) {
            Field[] f = aClass.getDeclaredFields();
            for (Field field : f) {
                //可以访问私有属性
                field.setAccessible(true);
                ParamsNotNull annotation = field.getAnnotation(ParamsNotNull.class);
                if (Objects.nonNull(annotation)) {
                    //获取字段说明
                    map.putAll(getDeclaredFieldsInfo(aClass.getDeclaredConstructor().newInstance(), map));
                    //字段名称
                    String name = field.getName();
                    //Object value = field.get(aClass.newInstance());
                    //循环判断值是否为空
                    for (Object arg : args) {
                        //获取控制层传入的参数
                        JSONObject jsonObject = JSONUtil.parseObj(arg);
                        Object o = jsonObject.get(name);
                        //为空抛出异常
                        if (Objects.isNull(o) || StrUtil.isEmpty(StrUtil.toString(o))) {
                            throw new ParamsNotNullException(map.get(name) + MessageConstants.PARAMS_NOT_NULL);
                        }

                        // 获取类型名称
                        Class<?> type = field.getType();
                        String canonicalName = type.getClass().getPackageName();
                        String stringName = String.class.getPackageName();

                        // 字符串类型处理其它类型后续根据业务集成即可
                        if (canonicalName.equalsIgnoreCase(stringName)) {
                            if (jsonObject.getStr(name).length() < annotation.length()) {
                                throw new ParamsNotNullException(map.get(name) + MessageConstants.PARAMS_NOT_LESS_THAN + annotation.length());
                            }
                        }


                    }
                }
            }
        }
    }

    /**
     * @param instance 类对象
     * @param fieldMap 说明MAP对象
     * <AUTHOR>
     * @Description 获取字段上的注解说明
     * @Return java.util.Map<java.lang.String, java.lang.String>
     * @Date 2021/4/28 10:30
     */
    public static Map<String, String> getDeclaredFieldsInfo(Object instance, Map<String, String> fieldMap) throws NoSuchFieldException {
        Map<String, String> map = new HashMap();
        Class<?> clazz = instance.getClass();
        Field[] fields = clazz.getDeclaredFields();
        boolean b = false;
        for (int i = 0; i < fields.length; i++) {
            // 除过fieldMap中的属性，其他属性都获取
            if (!fieldMap.containsValue(fields[i].getName())) {
                boolean annotationPresent = fields[i].isAnnotationPresent(ApiModelProperty.class);
                if (annotationPresent) {
                    // 获取注解值
                    String name = fields[i].getAnnotation(ApiModelProperty.class).value();
                    map.put(fields[i].getName(), name);
                }
            }
        }
        return map;
    }
}
