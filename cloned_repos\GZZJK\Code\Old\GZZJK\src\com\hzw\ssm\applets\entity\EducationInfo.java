package com.hzw.ssm.applets.entity;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2021-02-01 16:04
 * @Version 1.0
 */
public class EducationInfo {

    private String id;
    private String expertId;// 专家主键
    private String graduateSchool;// 毕业学校
    private String major;// 所学专业
    private String education;// 学历
    private String academicDegree;// 学位
    private String educationCertificate;// 学历证书
    private String educationCertificateName;// 学历证书
    private String academicCertificate;// 学位证书
    private String academicCertificateName;// 学位证书
    private int deleteFlag;// '删除状态 0-否，1-是'
    private Date createTime;
    private Date updateTime;

    private String educationValue;// 学历
    private String academicDegreeValue;// 学位
    private String dataDesc;//

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getExpertId() {
        return expertId;
    }

    public void setExpertId(String expertId) {
        this.expertId = expertId;
    }

    public String getGraduateSchool() {
        return graduateSchool;
    }

    public void setGraduateSchool(String graduateSchool) {
        this.graduateSchool = graduateSchool;
    }

    public String getMajor() {
        return major;
    }

    public void setMajor(String major) {
        this.major = major;
    }

    public String getEducation() {
        return education;
    }

    public void setEducation(String education) {
        this.education = education;
    }

    public String getAcademicDegree() {
        return academicDegree;
    }

    public void setAcademicDegree(String academicDegree) {
        this.academicDegree = academicDegree;
    }

    public String getEducationCertificate() {
        return educationCertificate;
    }

    public void setEducationCertificate(String educationCertificate) {
        this.educationCertificate = educationCertificate;
    }

    public String getAcademicCertificate() {
        return academicCertificate;
    }

    public void setAcademicCertificate(String academicCertificate) {
        this.academicCertificate = academicCertificate;
    }

    public int getDeleteFlag() {
        return deleteFlag;
    }

    public void setDeleteFlag(int deleteFlag) {
        this.deleteFlag = deleteFlag;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getEducationValue() {
        return educationValue;
    }

    public void setEducationValue(String educationValue) {
        this.educationValue = educationValue;
    }

    public String getAcademicDegreeValue() {
        return academicDegreeValue;
    }

    public void setAcademicDegreeValue(String academicDegreeValue) {
        this.academicDegreeValue = academicDegreeValue;
    }

    public String getEducationCertificateName() {
        return educationCertificateName;
    }

    public void setEducationCertificateName(String educationCertificateName) {
        this.educationCertificateName = educationCertificateName;
    }

    public String getAcademicCertificateName() {
        return academicCertificateName;
    }

    public void setAcademicCertificateName(String academicCertificateName) {
        this.academicCertificateName = academicCertificateName;
    }

    public String getDataDesc() {
        return dataDesc;
    }

    public void setDataDesc(String dataDesc) {
        this.dataDesc = dataDesc;
    }
}
