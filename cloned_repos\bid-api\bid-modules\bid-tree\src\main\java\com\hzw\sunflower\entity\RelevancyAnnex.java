package com.hzw.sunflower.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.hzw.sunflower.common.BaseBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;


/**
 * Created by zgq on 2021/07、02
 */
@ApiModel("通用附件表")
@TableName("t_relevancy_annex")
@Data
@Accessors(chain = true)
public class RelevancyAnnex extends BaseBean {

    /**
     * 主键
     * @mbg.generated
     */
    private Long id;

    /**
     * 项目ID
     */
    private Long projectId;

    /**
     * 包段ID
     */
    private Long sectionId;

    /**
     * OSS文件ID
     */
    private Long fileId;

    /**
     * 公告ID/招标文件ID
     */
    private Long busId;

    /**
     * 文件类型
     */
    @ApiModelProperty(value = "文件类型：1 招标公告附件；2 招标文件；3 招标文件附件", position = 6)
    private Integer fileType;

}
