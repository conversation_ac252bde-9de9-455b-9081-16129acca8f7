package com.hzw.sunflower.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @ClassName:CommonDeptRolesDTO
 * @Auther: lijinxin
 * @Description:
 * @Date: 2024/7/23 15:26
 * @Version: v1.0
 */
@Data
public class CommonDeptRolesDTO {

    @ApiModelProperty(value = "部门id", position = 2)
    private Long deptId;

    private String departOtherId;

    private String departName;

    private Long parentId;

    @ApiModelProperty(value = "标题", position = 3)
    private Long[] roleIds;

    private String[] roleNames;

    private String roleStrNames;

    private String roleStrIds;

    private String roleStrCodes;

    private String[] roleCodes;

    @ApiModelProperty("是否是主账户 1:是  2：否，新增或修改用户时使用")
    private Integer isMainAdd;

}
