package com.hzw.sunflower.constant.constantenum;

/**
 * <AUTHOR>
 * @date ：Created in 2022/07/27 9:23
 * @description：代理服务费类型
 * @version: 1.0
 */
public enum BondAgencyFeeTypeEnum {
    //服务费收取方式：1：保证金转代理服务费  2：保证金全额转代理服务费并补差价  3：保证金全额退还，代理服务费另汇
    BOND_TO_AGENCY(1, "保证金转代理服务费"),
    BOND_TO_AGENCY_SUPPLEMENT(2, "保证金全额转代理服务费并补差价"),
    BOND_REFUND(3, "保证金全额退还，代理服务费另汇");

    private Integer type;
    private String desc;

    BondAgencyFeeTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
