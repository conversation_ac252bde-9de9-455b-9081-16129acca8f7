package com.hzw.sunflower.controller.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "异常退款申请处长处理")
public class BondAbnormalApplyRefundRegistrarReq {

    @ApiModelProperty(value = "申请退还处理id")
    private Long applyRefundIdConfirmId;

    @ApiModelProperty(value = "处理方式，2.非本处项目 3.本处项目同意退款 4.本处项目不同意退款 5.同意退款 6.不同意退款")
    private Integer confirmType;

    @ApiModelProperty(value = "退回原因")
    private String remark;

}
