package com.hzw.ssm.expert.entity;


import java.util.Date;

/**
 * T_POLICIES_DETAILS_BAK
 *
 * <AUTHOR>
 * @version 1.0.0 2021-02-18
 */
public class PoliciesDetailsBak implements java.io.Serializable {
    /** 版本号 */
    private static final long serialVersionUID = -6779934510542667265L;

    /* This code was generated by TableGo tools, mark 1 begin. */

    /** 主键 */
    private String id;

    /** createUser */
    private String createUser;

    /** createTime */
    private Date createTime;

    /** updateUser */
    private String updateUser;

    /** updateTime */
    private Date updateTime;

    /** 前 */
    private String before;
    private PoliciesDetails  beforeDetails;
    private  PoliciesDetails         afterDetails;


    public PoliciesDetails getBeforeDetails() {
        return beforeDetails;
    }

    public void setBeforeDetails(PoliciesDetails beforeDetails) {
        this.beforeDetails = beforeDetails;
    }

    public PoliciesDetails getAfterDetails() {
        return afterDetails;
    }

    public void setAfterDetails(PoliciesDetails afterDetails) {
        this.afterDetails = afterDetails;
    }

    /** 后 */
    private String after;

    /** detailId */
    private String detailId;


    /** modifiedTtem */
    private String modifiedTtem;

    public String getModifiedTtem() {
        return modifiedTtem;
    }

    public void setModifiedTtem(String modifiedTtem) {
        this.modifiedTtem = modifiedTtem;
    }

    /* This code was generated by TableGo tools, mark 1 end. */

    /* This code was generated by TableGo tools, mark 2 begin. */

    /**
     * 获取主键
     *
     * @return 主键
     */
    public String getId() {
        return this.id;
    }

    /**
     * 设置主键
     *
     * @param id
     *          主键
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * 获取createUser
     *
     * @return createUser
     */
    public String getCreateUser() {
        return this.createUser;
    }

    /**
     * 设置createUser
     *
     * @param createUser
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    /**
     * 获取createTime
     *
     * @return createTime
     */
    public Date getCreateTime() {
        return this.createTime;
    }

    /**
     * 设置createTime
     *
     * @param createTime
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取updateUser
     *
     * @return updateUser
     */
    public String getUpdateUser() {
        return this.updateUser;
    }

    /**
     * 设置updateUser
     *
     * @param updateUser
     */
    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }

    /**
     * 获取updateTime
     *
     * @return updateTime
     */
    public Date getUpdateTime() {
        return this.updateTime;
    }

    /**
     * 设置updateTime
     *
     * @param updateTime
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 获取前
     *
     * @return 前
     */
    public String getBefore() {
        return this.before;
    }

    /**
     * 设置前
     *
     * @param before
     *          前
     */
    public void setBefore(String before) {
        this.before = before;
    }

    /**
     * 获取后
     *
     * @return 后
     */
    public String getAfter() {
        return this.after;
    }

    /**
     * 设置后
     *
     * @param after
     *          后
     */
    public void setAfter(String after) {
        this.after = after;
    }

    /**
     * 获取detailId
     *
     * @return detailId
     */
    public String getDetailId() {
        return this.detailId;
    }

    /**
     * 设置detailId
     *
     * @param detailId
     */
    public void setDetailId(String detailId) {
        this.detailId = detailId;
    }

    /* This code was generated by TableGo tools, mark 2 end. */
}