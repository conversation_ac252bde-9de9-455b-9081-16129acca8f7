package com.hzw.sunflower.service.impl;


import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzw.sunflower.constant.constantenum.BidPurchaseModeEnum;
import com.hzw.sunflower.controller.response.CaDataVo;
import com.hzw.sunflower.controller.response.ReportListVO;
import com.hzw.sunflower.dao.ReportListMapper;
import com.hzw.sunflower.dto.ProjectUserDTO;
import com.hzw.sunflower.entity.ArchiveRecallList;
import com.hzw.sunflower.entity.JwtUser;
import com.hzw.sunflower.entity.ReportList;
import com.hzw.sunflower.entity.TendererReport;
import com.hzw.sunflower.entity.condition.ArchiveRecallCondition;
import com.hzw.sunflower.entity.condition.ReportListCondition;
import com.hzw.sunflower.service.ReportListService;
import com.hzw.sunflower.util.StringUtil;
import lombok.Cleanup;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.BufferedOutputStream;
import java.io.ByteArrayOutputStream;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

@Service
public class ReportListServiceImpl extends ServiceImpl<ReportListMapper, ReportList> implements ReportListService {
    @Override
    public IPage<ReportList> findInfoByCondition(ReportListCondition condition) {
        IPage<ReportList> page = condition.buildPage();
        IPage<ReportList> listIPage = null;
        listIPage = this.baseMapper.findInfoByCondition(page,condition);
        List<ReportList> records = listIPage.getRecords();
        for(ReportList d:records) {
            if(null != d.getPackageNumber()){
                d.setProjectNo(d.getPurchaseNumber() + "/" + d.getPackageNumber());
            } else{
                d.setProjectNo(d.getPurchaseNumber());
            }
            if(null != d.getEntrustedAmount() && !d.getEntrustedAmount().equals(new BigDecimal(0))){
                d.setEntrustedAmount(d.getEntrustedAmount().divide(new BigDecimal("10000"),6,BigDecimal.ROUND_HALF_UP));
            }
            d.setConferenceAddress(this.baseMapper.findConferenceAddress(condition, d.getPurchaseNumber(),d.getPackageNumber()));
            if (null != d.getPurchaseType() && d.getPurchaseType().toString().equals(BidPurchaseModeEnum.GOVERNMENT_PROCUREMENT.getType())) {
                d.setIsGovernment("是");
            } else {
                d.setIsGovernment("否");
            }
        }
        return listIPage;
    }

    /**
     * 报表中心列表（20） 归档一览表
     * @param condition
     * @param jwtUser
     * @return
     */
    @Override
    public IPage<ReportList> findInfo(ReportListCondition condition, JwtUser jwtUser) {
        IPage<ReportList> page = condition.buildPage();
        IPage<ReportList> listIPage = null;
        Long userId = jwtUser.getUserId();
        List<Long> deptId = this.baseMapper.findDeptId(userId);
        String deptIds = StringUtils.join(deptId, ",");
        if(condition.getCode()){
            // 运管处
            listIPage = this.baseMapper.findAllInfo(page,condition);
        } else {
            // 普通处室
            listIPage = this.baseMapper.findInfoByDept(page,condition,deptIds);
        }
        List<ReportList> records = listIPage.getRecords();
        for(ReportList d:records) {
            if(null != d.getPackageNumber()){
                d.setProjectNo(d.getPurchaseNumber() + "/" + d.getPackageNumber());
            } else{
                d.setProjectNo(d.getPurchaseNumber());
            }

            Integer day = computingTimeOverdue(d.getSealTime(), d.getArchiveTime());
            if(day > 0){
                d.setOverdueTime(String.valueOf(day));
            } else {
                d.setOverdueTime("-");
            }

        }
        return listIPage;
    }

    /**
     * 报表中心列表 归档撤回统计
     * @param condition
     * @param jwtUser
     * @return
     */
    @Override
    public IPage<ArchiveRecallList> archiveRecallList(ArchiveRecallCondition condition, JwtUser jwtUser) {
        IPage<ArchiveRecallList> page = condition.buildPage();
        IPage<ArchiveRecallList> listIPage = null;

        List<ProjectUserDTO> projectUsers = condition.getProjectUser();
        StringBuilder sql = new StringBuilder();
        String str = null;
        //拼接自定义的sql查询语句
        //判断是否为空,如果为空代表无值，则不查询相关的SQL
        if (projectUsers!=null && projectUsers.size()>0 ){
            //调用方法拼接字符串
            makeSQL(projectUsers, sql, true);
            str = sql.toString();
        }

        listIPage = this.baseMapper.archiveRecallList(page,condition,str);
        return listIPage;
    }

    @Override
    public List<ArchiveRecallList> exportArchiveRecallList(ArchiveRecallCondition condition) {
        List<ProjectUserDTO> projectUsers = condition.getProjectUser();
        StringBuilder sql = new StringBuilder();
        String str = null;
        //拼接自定义的sql查询语句
        //判断是否为空,如果为空代表无值，则不查询相关的SQL
        if (projectUsers!=null && projectUsers.size()>0 ){
            //调用方法拼接字符串
            makeSQL(projectUsers, sql, true);
            str = sql.toString();
        }

        List<ArchiveRecallList> records = this.baseMapper.exportArchiveRecallList(condition,str);
        return records;
    }


    /**
     * 报表中心列表 归档撤回统计  当选择了对应的项目经理时候，调用此方法拼接SQL   后期如果多部门优化了，可以直接不使用此方法
     */
    private  void makeSQL(List<ProjectUserDTO> projectUsers, StringBuilder sql, boolean isFirst) {
        //isFirst 标记变量，表示是否是第一次循环，每次开始必为true
        //如果只选择了一个人，不走循环
        if (projectUsers.size()==1) {
            //主要sql语句  拼接后类似于  SUBSTRING(t.purchase_number, 8, 3) in ('001') and t.created_user_id in (1628752312787)
            String str = StringUtil.format("AND ( SUBSTRING(t.purchase_number, 8, 3) in ( '{}' ) and t.created_user_id in ( {} ))",projectUsers.get(0).getDepartCode(),projectUsers.get(0).getUserId());
            sql.append(str);
        }else{
            //此时为选择多个人得时候
            for (ProjectUserDTO projectUser : projectUsers){
                //判断选择了多少个项目经理，只选择一个不循环代码
                if (projectUser.getDepartCode()!=null && projectUser.getUserId()!=null){
                    //主要sql语句  拼接后类似于  SUBSTRING(t.purchase_number, 8, 3) in ('001') and t.created_user_id in (1628752312787)
                    String str = StringUtil.format("SUBSTRING(t.purchase_number, 8, 3) in ( '{}' ) and t.created_user_id in ( {} )",projectUser.getDepartCode(),projectUser.getUserId());
                    if (isFirst){
                        sql.append("and ((").append(str).append(")");
                        isFirst = false;
                    } else if (projectUsers.indexOf(projectUser) == projectUsers.size() - 1){
                        // 最后一次循环添加 ")"
                        sql.append(" or (").append(str).append("))");
                    } else {
                        sql.append(" or (").append(str).append(")");
                    }
                }
            }
        }
    }

    /**
     * 导出excel查询
     * @param condition
     * @param jwtUser
     * @return
     */
    @Override
    public List<ReportListVO> findInfoList(ReportListCondition condition, JwtUser jwtUser) {
        Long userId = jwtUser.getUserId();
        List<Long> deptId = this.baseMapper.findDeptId(userId);
        String deptIds = StringUtils.join(deptId, ",");
        List<ReportListVO> list = new ArrayList<>();
        if(condition.getCode()){
            list = this.baseMapper.findAllInfoList(condition);
            for (ReportListVO reportListVO : list) {
                if(reportListVO.getInternationalNumber()!=null) {
                    if (reportListVO.getInternationalNumber().equals("")) {
                        reportListVO.setInternationalNumber(null);
                    }
                }
            }
        } else {
            list = this.baseMapper.findInfoListByDept(condition,deptIds);
            for (ReportListVO reportListVO : list) {
                if(reportListVO.getInternationalNumber()!=null) {
                    if (reportListVO.getInternationalNumber().equals("")) {
                        reportListVO.setInternationalNumber(null);
                    }
                }
            }
        }

        for(ReportListVO d:list) {
            if(null != d.getPackageNumber()){
                d.setProjectNo(d.getPurchaseNumber() + "/" + d.getPackageNumber());
            } else{
                d.setProjectNo(d.getPurchaseNumber());
            }

            Integer day = computingTimeOverdue(d.getSealTime(), d.getArchiveTime());
            if(day > 0){
                d.setOverdueTime(String.valueOf(day));
            } else {
                d.setOverdueTime("-");
            }

        }
        return list;
    }

    public Integer computingTimeOverdue(String sealTime, String archiveTime){
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        Date beginTimeDate = new Date();
        Date endTimeDate = new Date();
        int day = 0;
        try {
            // 中标通知书盖章时间
            if(StringUtils.isNotBlank(sealTime)){
                beginTimeDate = simpleDateFormat.parse(sealTime);
            }
            //归档时间
            if(StringUtils.isNotBlank(archiveTime)){
                endTimeDate = simpleDateFormat.parse(archiveTime);
            }
            long begin = beginTimeDate.getTime();
            long end = endTimeDate.getTime();
            day = (int) (((end - begin) / (1000 * 60 * 60 * 24)) - 30);//两个时间天数差超过30天算逾期
        } catch (ParseException e) {
            e.printStackTrace();
        } finally {
            log.error("归档时间转换错误！");
        }
        return day;
    }


}
