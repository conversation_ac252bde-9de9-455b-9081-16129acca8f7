package com.hzw.sunflower.controller.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date ：Created in 2021/7/8 15:56
 * @description：开启唱标初始化请求实体类
 * @version: 1.0
 */
@Data
@NoArgsConstructor
public class StartSingInitReq implements Serializable {

    @ApiModelProperty(value = "供应商id集合")
    private List<Long> companyIdList;

    @ApiModelProperty(value = "供应商名称集合")
    private List<String> companyNameList;

    @ApiModelProperty(value = "标段id")
    private Long sectionId;

    @ApiModelProperty(value = "项目id")
    private Long projectId;

}
