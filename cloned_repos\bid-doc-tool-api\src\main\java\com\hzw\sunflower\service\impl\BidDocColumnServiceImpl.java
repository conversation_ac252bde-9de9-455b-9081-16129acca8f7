package com.hzw.sunflower.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hzw.sunflower.config.DocToolsConstants;
import com.hzw.sunflower.config.constantenum.DocCatalogEnum;
import com.hzw.sunflower.dto.*;
import com.hzw.sunflower.entity.BidDocColumn;
import com.hzw.sunflower.dao.BidDocColumnMapper;
import com.hzw.sunflower.service.BidDocColumnService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzw.sunflower.util.ConvertUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 招标文件编制字段配置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-26
 */
@Service
public class BidDocColumnServiceImpl extends ServiceImpl<BidDocColumnMapper, BidDocColumn> implements BidDocColumnService {

    /**
     * 保存字段
     * @param docId
     * @param columnList
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveColumns(Long docId, List<BidDocColumn> columnList) {
        this.remove(new LambdaQueryWrapper<BidDocColumn>().eq(BidDocColumn::getDocId, docId));
        for (BidDocColumn column : columnList) {
            column.setId(null);
            column.setDocId(docId);
        }
        return this.saveBatch(columnList);
    }

    /**
     * 生成填充DTO
     * @param docId
     * @return
     */
    @Override
    public TemplateFillDTO getFillDto(Long docId) {
        TemplateFillDTO dto = new TemplateFillDTO();
        // 填充字段
        List<BidDocColumn> columnList = this.list(new LambdaQueryWrapper<BidDocColumn>().eq(BidDocColumn::getDocId, docId));
        if (columnList.size() > 0) {
            // 招标公告
            dto.setTb(convertTb(columnList));
            // 投标邀请书
            dto.setBi(convertBi(columnList));
            // 投标人须知
            dto.setBn(convertBn(columnList));
            // 合同条款
            dto.setCt(convertCt(columnList));
            // 供货要求
            dto.setSr(convertSr(columnList));
            // 发包人要求
            dto.setEr(convertEr(columnList));
            // 技术标准和要求
            dto.setTr(convertTr(columnList));
        }
        // todo 填充其他格式内容

        System.out.println(dto);
        return dto;
    }

    /**
     * 招标公告：公开招标-标准设备采购招标文件、公开招标-标准设计招标文件、公开招标-简要标准施工招标文件
     * @param columnList
     * @return
     */
    private static TenderBulletinFillDTO convertTb(List<BidDocColumn> columnList) {
        List<BidDocColumn> tempList = columnList.stream().filter(s -> s.getCatalogId().equals(DocCatalogEnum.CATALOG_1.getType()))
                .collect(Collectors.toList());
        if (tempList.size() == 0) {
            return null;
        }
        Map<String, String> map = ConvertUtil.convertToMap(tempList, TenderBulletinFillDTO.class);
        TenderBulletinFillDTO dto = JSON.parseObject(JSON.toJSONString(map), TenderBulletinFillDTO.class);
        // 部分字段特殊处理
        if (dto.getIsReceptJointBid() != null) {
            if (dto.getIsReceptJointBid().equals(DocToolsConstants.YES)) {
                dto.setIsReceptJointBid("接受");
                dto.setReceptJointBid(DocToolsConstants.YES_CODE);
                dto.setNotReceptJointBid(DocToolsConstants.NO_CODE);
            } else {
                dto.setIsReceptJointBid("不接受");
                dto.setReceptJointBid(DocToolsConstants.NO_CODE);
                dto.setNotReceptJointBid(DocToolsConstants.YES_CODE);
            }
        }
        if (dto.getTecCompensation() != null) {
            if (dto.getIsTecCompensation().equals(DocToolsConstants.YES)) {
                dto.setIsTecCompensation("给予");
                dto.setTecCompensation(DocToolsConstants.YES_CODE);
                dto.setNoTecCompensation(DocToolsConstants.NO_CODE);
            } else {
                dto.setIsTecCompensation("不给予");
                dto.setTecCompensation(DocToolsConstants.NO_CODE);
                dto.setNoTecCompensation(DocToolsConstants.YES_CODE);
            }
        }
        // 根据文件获取方式组装替换段落
        if (dto.getObtainType() != null) {
            String tenderFileObtainContent = "";
            String bidCompileContent = "";
            if (dto.getObtainType().equals(DocToolsConstants.OBTAIN_TYPE_OFFLINE)) {
                tenderFileObtainContent = "凡有意参加投标者，请于 "+dto.getObtainTime()+"，" + dto.getBuyTime() +
                        "（北京时间，下同），在 " + dto.getBuyAddress() + "（详细地址）持单位介绍信购买招标文件。" + dto.getPostDescription();
                bidCompileContent = DocToolsConstants.OFFLINE_BID_COMPILE_CONTENT;
            } else {
                tenderFileObtainContent = "凡有意参加投标者，请于 "+dto.getObtainTime()+"(北京时间，下同)，登录 "+dto.getSubmitPlatform()+"（电子招标投标交易平台名称）下载电子招标文件。";
                bidCompileContent = DocToolsConstants.ONLINE_BID_COMPILE_CONTENT;
            }
            dto.setTenderFileObtainContent(tenderFileObtainContent);
            dto.setBidCompileContent(bidCompileContent);
        }

        // 根据文件递交方式组装替换段落
        LocalDateTime submitEndTime = LocalDateTime.parse(dto.getSubmitEndTime(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        String submitEndTimeStr = submitEndTime.getYear()+"年 "+submitEndTime.getMonthValue()+"月" + submitEndTime.getDayOfMonth() +
                " 日 "+submitEndTime.getHour()+"时 "+submitEndTime.getMinute()+"分";
        if (dto.getSubmitType() != null) {
            String tenderFileSubmitContent1 = "";
            String tenderFileSubmitContent2 = "";
            String bidMarkingContent = "";
            String updateWithdrawContent = "";
            String bidSubmitContent1 = "";
            String bidSubmitContent2 = "";
            String bidSubmitContent3 = "";
            if (dto.getSubmitType().equals(DocToolsConstants.SUBMIT_TYPE_OFFLINE)) {
                tenderFileSubmitContent1 = "投标文件递交的截止时间（投标截止时间，下同）为 "+submitEndTime.getYear()+"年 "+submitEndTime.getMonthValue()+"月" +
                        submitEndTime.getDayOfMonth() + " 日 "+submitEndTime.getHour()+"时 "+submitEndTime.getMinute()+"分，地点为 " + dto.getSubmitAddress() + "。";
                tenderFileSubmitContent2 = "逾期送达的、未送达指定地点的或者不按照招标文件要求密封的投标文件，招标人将予以拒收。";
                bidMarkingContent = DocToolsConstants.OFFLINE_BID_MARKING_CONTENT;
                updateWithdrawContent = DocToolsConstants.OFFLINE_UPDATE_WITHDRAW_CONTENT;
                bidSubmitContent1 = DocToolsConstants.OFFLINE_BID_SUBMIT_CONTENT_1;
                bidSubmitContent2 = DocToolsConstants.OFFLINE_BID_SUBMIT_CONTENT_2;
                bidSubmitContent3 = DocToolsConstants.OFFLINE_BID_SUBMIT_CONTENT_3;
            } else {
                tenderFileSubmitContent1 = "投标文件递交的截止时间（投标截止时间，下同）为 "+submitEndTime.getYear()+"年 "+submitEndTime.getMonthValue()+"月" +
                        submitEndTime.getDayOfMonth() + " 日 "+submitEndTime.getHour()+"时 "+submitEndTime.getMinute()+
                        "分，投标人应在截止时间前通过 "+dto.getSubmitPlatform()+"（电子招标投标交易平台）递交电子投标文件。";
                tenderFileSubmitContent2 = "逾期送达的投标文件，电子招标投标交易平台将予以拒收。";
                bidMarkingContent = DocToolsConstants.ONLINE_BID_MARKING_CONTENT;
                updateWithdrawContent = DocToolsConstants.ONLINE_UPDATE_WITHDRAW_CONTENT;
                bidSubmitContent1 = DocToolsConstants.ONLINE_BID_SUBMIT_CONTENT_1;
                bidSubmitContent2 = DocToolsConstants.ONLINE_BID_SUBMIT_CONTENT_2;
                bidSubmitContent3 = DocToolsConstants.ONLINE_BID_SUBMIT_CONTENT_3;
            }
            dto.setTenderFileSubmitContent1(tenderFileSubmitContent1);
            dto.setTenderFileSubmitContent2(tenderFileSubmitContent2);
            dto.setBidMarkingContent(bidMarkingContent);
            dto.setUpdateWithdrawContent(updateWithdrawContent);
            dto.setBidSubmitContent1(bidSubmitContent1);
            dto.setBidSubmitContent2(bidSubmitContent2);
            dto.setBidSubmitContent3(bidSubmitContent3);
        } else {
            // 简明标准施工模板单独处理
            String tenderFileSubmitContent1 = "投标文件递交的截止时间（投标截止时间，下同）为 "+submitEndTime.getYear()+"年 "+submitEndTime.getMonthValue()+"月" +
                    submitEndTime.getDayOfMonth() + " 日 "+submitEndTime.getHour()+"时 "+submitEndTime.getMinute()+"分，地点为 " + dto.getSubmitAddress() + "。";
            dto.setTenderFileSubmitContent1(tenderFileSubmitContent1);
        }
        dto.setSubmitEndTime(submitEndTimeStr);
        // 获取当前日期
        LocalDate currentDate = LocalDate.now();
        dto.setYear(currentDate.getYear() + "");
        dto.setMonth(currentDate.getMonthValue() + "");
        dto.setDay(currentDate.getDayOfMonth() + "");
        dto.setAgencyName("江苏省招标中心有限公司");
        return dto;
    }

    /**
     * 投标人邀请书：邀请招标-标准设备采购招标文件、邀请招标-标准设计招标文件、邀请招标-简要标准施工招标文件
     * @param columnList
     * @return
     */
    private BidInviteFillDTO convertBi(List<BidDocColumn> columnList) {
        List<BidDocColumn> tempList = columnList.stream().filter(s -> s.getCatalogId().equals(DocCatalogEnum.CATALOG_2.getType()))
                .collect(Collectors.toList());
        if (tempList.size() == 0) {
            return null;
        }
        Map<String, String> map = ConvertUtil.convertToMap(tempList, BidInviteFillDTO.class);
        BidInviteFillDTO dto = JSON.parseObject(JSON.toJSONString(map), BidInviteFillDTO.class);
        // 部分字段特殊处理
        if (dto.getIsReceptJointBid() != null) {
            if (dto.getIsReceptJointBid().equals(DocToolsConstants.YES)) {
                dto.setIsReceptJointBid("接受");
                dto.setReceptJointBid(DocToolsConstants.YES_CODE);
                dto.setNotReceptJointBid(DocToolsConstants.NO_CODE);
            } else {
                dto.setIsReceptJointBid("不接受");
                dto.setReceptJointBid(DocToolsConstants.NO_CODE);
                dto.setNotReceptJointBid(DocToolsConstants.YES_CODE);
            }
        }
        // 根据文件获取方式组装替换段落
        if (dto.getObtainType() != null) {
            String tenderFileObtainContent = "";
            String bidCompileContent = "";
            if (dto.getObtainType().equals(DocToolsConstants.OBTAIN_TYPE_OFFLINE)) {
                tenderFileObtainContent = "凡有意参加投标者，请于 "+dto.getObtainTime()+"，" + dto.getBuyTime() +
                        "（北京时间，下同），在 " + dto.getBuyAddress() + "（详细地址）持单位介绍信购买招标文件。" + dto.getPostDescription();
                bidCompileContent = DocToolsConstants.OFFLINE_BID_COMPILE_CONTENT;
            } else {
                tenderFileObtainContent = "凡有意参加投标者，请于 "+dto.getObtainTime()+"(北京时间，下同)，登录 "+dto.getSubmitPlatform()+"（电子招标投标交易平台名称）下载电子招标文件。";
                bidCompileContent = DocToolsConstants.ONLINE_BID_COMPILE_CONTENT;
            }
            dto.setTenderFileObtainContent(tenderFileObtainContent);
            dto.setBidCompileContent(bidCompileContent);
        }

        // 根据文件递交方式组装替换段落
        LocalDateTime submitEndTime = LocalDateTime.parse(dto.getSubmitEndTime(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        String submitEndTimeStr = submitEndTime.getYear()+"年 "+submitEndTime.getMonthValue()+"月" + submitEndTime.getDayOfMonth() +
                " 日 "+submitEndTime.getHour()+"时 "+submitEndTime.getMinute()+"分";
        if (dto.getSubmitType() != null) {
            String tenderFileSubmitContent1 = "";
            String tenderFileSubmitContent2 = "";
            String bidMarkingContent = "";
            String updateWithdrawContent = "";
            String bidSubmitContent1 = "";
            String bidSubmitContent2 = "";
            String bidSubmitContent3 = "";
            if (dto.getSubmitType().equals(DocToolsConstants.SUBMIT_TYPE_OFFLINE)) {
                tenderFileSubmitContent1 = "投标文件递交的截止时间（投标截止时间，下同）为 "+submitEndTime.getYear()+"年 "+submitEndTime.getMonthValue()+"月" +
                        submitEndTime.getDayOfMonth() + " 日 "+submitEndTime.getHour()+"时 "+submitEndTime.getMinute()+"分，地点为 " + dto.getSubmitAddress() + "。";
                tenderFileSubmitContent2 = "逾期送达的、未送达指定地点的或者不按照招标文件要求密封的投标文件，招标人将予以拒收。";
                bidMarkingContent = DocToolsConstants.OFFLINE_BID_MARKING_CONTENT;
                updateWithdrawContent = DocToolsConstants.OFFLINE_UPDATE_WITHDRAW_CONTENT;
                bidSubmitContent1 = DocToolsConstants.OFFLINE_BID_SUBMIT_CONTENT_1;
                bidSubmitContent2 = DocToolsConstants.OFFLINE_BID_SUBMIT_CONTENT_2;
                bidSubmitContent3 = DocToolsConstants.OFFLINE_BID_SUBMIT_CONTENT_3;
            } else {
                tenderFileSubmitContent1 = "投标文件递交的截止时间（投标截止时间，下同）为 "+submitEndTime.getYear()+"年 "+submitEndTime.getMonthValue()+"月" +
                        submitEndTime.getDayOfMonth() + " 日 "+submitEndTime.getHour()+"时 "+submitEndTime.getMinute()+
                        "分，投标人应在截止时间前通过 "+dto.getSubmitPlatform()+"（电子招标投标交易平台）递交电子投标文件。";
                tenderFileSubmitContent2 = "逾期送达的投标文件，电子招标投标交易平台将予以拒收。";
                bidMarkingContent = DocToolsConstants.ONLINE_BID_MARKING_CONTENT;
                updateWithdrawContent = DocToolsConstants.ONLINE_UPDATE_WITHDRAW_CONTENT;
                bidSubmitContent1 = DocToolsConstants.ONLINE_BID_SUBMIT_CONTENT_1;
                bidSubmitContent2 = DocToolsConstants.ONLINE_BID_SUBMIT_CONTENT_2;
                bidSubmitContent3 = DocToolsConstants.ONLINE_BID_SUBMIT_CONTENT_3;
            }
            dto.setTenderFileSubmitContent1(tenderFileSubmitContent1);
            dto.setTenderFileSubmitContent2(tenderFileSubmitContent2);
            dto.setBidMarkingContent(bidMarkingContent);
            dto.setUpdateWithdrawContent(updateWithdrawContent);
            dto.setBidSubmitContent1(bidSubmitContent1);
            dto.setBidSubmitContent2(bidSubmitContent2);
            dto.setBidSubmitContent3(bidSubmitContent3);
        } else {
            // 简明标准施工模板单独处理
            String tenderFileSubmitContent1 = "投标文件递交的截止时间（投标截止时间，下同）为 "+submitEndTime.getYear()+"年 "+submitEndTime.getMonthValue()+"月" +
                    submitEndTime.getDayOfMonth() + " 日 "+submitEndTime.getHour()+"时 "+submitEndTime.getMinute()+"分，地点为 " + dto.getSubmitAddress() + "。";
            dto.setTenderFileSubmitContent1(tenderFileSubmitContent1);
        }
        dto.setSubmitEndTime(submitEndTimeStr);

        // 获取当前日期
        LocalDate currentDate = LocalDate.now();
        dto.setYear(currentDate.getYear() + "");
        dto.setMonth(currentDate.getMonthValue() + "");
        dto.setDay(currentDate.getDayOfMonth() + "");
        dto.setAgencyName("江苏省招标中心有限公司");
        // 确认参标截至时间
        if (dto.getConfirmEndTime() != null) {
            LocalDateTime confirmEndTime = LocalDateTime.parse(dto.getConfirmEndTime(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            dto.setConfirmEndTime(confirmEndTime.getYear() + "年" + confirmEndTime.getMonthValue() + "月" + confirmEndTime.getDayOfMonth() + "日" + confirmEndTime.getHour() + "时" + confirmEndTime.getMinute() + "分");
        }
        return dto;
    }

    /**
     * 投标人须知：所有类型
     * @param columnList
     * @return
     */
    private static BidderNoticeFillDTO convertBn(List<BidDocColumn> columnList) {
        List<BidDocColumn> tempList = columnList.stream().filter(s -> s.getCatalogId().equals(DocCatalogEnum.CATALOG_3.getType()))
                .collect(Collectors.toList());
        if (tempList.size() == 0) {
            return null;
        }
        Map<String, String> map = ConvertUtil.convertToMap(tempList, BidderNoticeFillDTO.class);
        BidderNoticeFillDTO dto = JSON.parseObject(JSON.toJSONString(map), BidderNoticeFillDTO.class);
        // 特殊处理
        if (dto.getIsOpenMeeting() != null) {
            dto.setOpen(dto.getIsOpenMeeting().equals(DocToolsConstants.YES) ? DocToolsConstants.YES_CODE : DocToolsConstants.NO_CODE);
            dto.setNotOpen(dto.getIsOpenMeeting().equals(DocToolsConstants.YES) ? DocToolsConstants.NO_CODE : DocToolsConstants.YES_CODE);
        }
        if (dto.getIsAllowSubcontract() != null) {
            dto.setAllowSubcontract(dto.getIsAllowSubcontract().equals(DocToolsConstants.YES) ? DocToolsConstants.YES_CODE : DocToolsConstants.NO_CODE);
            dto.setNotAllowSubcontract(dto.getIsAllowSubcontract().equals(DocToolsConstants.YES) ? DocToolsConstants.NO_CODE : DocToolsConstants.YES_CODE);
        }
        if (dto.getIsAllowDeviation() != null) {
            dto.setAllowDeviation(dto.getIsAllowDeviation().equals(DocToolsConstants.YES) ? DocToolsConstants.YES_CODE : DocToolsConstants.NO_CODE);
            dto.setNotAllowDeviation(dto.getIsAllowDeviation().equals(DocToolsConstants.YES) ? DocToolsConstants.NO_CODE : DocToolsConstants.YES_CODE);
        }
        if (dto.getIsMaxBidPrice() != null) {
            dto.setHasMaxBidPrice(dto.getIsMaxBidPrice().equals(DocToolsConstants.YES) ? DocToolsConstants.YES_CODE : DocToolsConstants.NO_CODE);
            dto.setNotMaxBidPrice(dto.getIsMaxBidPrice().equals(DocToolsConstants.YES) ? DocToolsConstants.NO_CODE : DocToolsConstants.YES_CODE);
        }
        if (dto.getIsNeedBond() != null) {
            dto.setNeedBond(dto.getIsNeedBond().equals(DocToolsConstants.YES) ? DocToolsConstants.YES_CODE : DocToolsConstants.NO_CODE);
            dto.setNotNeedBond(dto.getIsNeedBond().equals(DocToolsConstants.YES) ? DocToolsConstants.NO_CODE : DocToolsConstants.YES_CODE);
        }
        if (dto.getIsSpecialRequirements() != null) {
            dto.setHasSpecialRequirements(dto.getIsSpecialRequirements().equals(DocToolsConstants.YES) ? DocToolsConstants.YES_CODE : DocToolsConstants.NO_CODE);
            dto.setNoSpecialRequirements(dto.getIsSpecialRequirements().equals(DocToolsConstants.YES) ? DocToolsConstants.NO_CODE : DocToolsConstants.YES_CODE);
        }
        if (dto.getIsAllowSubmit() != null) {
            dto.setAllowSubmit(dto.getIsAllowSubmit().equals(DocToolsConstants.YES) ? DocToolsConstants.YES_CODE : DocToolsConstants.NO_CODE);
            dto.setNoSubmit(dto.getIsAllowSubmit().equals(DocToolsConstants.YES) ? DocToolsConstants.NO_CODE : DocToolsConstants.YES_CODE);
        }
        if (dto.getIsNeedVolumeBind() != null) {
            dto.setNeedVolumnBind(dto.getIsNeedVolumeBind().equals(DocToolsConstants.YES) ? DocToolsConstants.YES_CODE : DocToolsConstants.NO_CODE);
            dto.setNoVolumeBind(dto.getIsNeedVolumeBind().equals(DocToolsConstants.YES) ? DocToolsConstants.NO_CODE : DocToolsConstants.YES_CODE);
        }
        if (dto.getIsReturn() != null) {
            dto.setHasReturn(dto.getIsReturn().equals(DocToolsConstants.YES) ? DocToolsConstants.YES_CODE : DocToolsConstants.NO_CODE);
            dto.setNoReturn(dto.getIsReturn().equals(DocToolsConstants.YES) ? DocToolsConstants.NO_CODE : DocToolsConstants.YES_CODE);
        }
        if (dto.getIsAuthEvaluation() != null) {
            dto.setHasAuthEvaluation(dto.getIsAuthEvaluation().equals(DocToolsConstants.YES) ? DocToolsConstants.YES_CODE : DocToolsConstants.NO_CODE);
            dto.setNoAuthEvaluation(dto.getIsAuthEvaluation().equals(DocToolsConstants.YES) ? DocToolsConstants.NO_CODE : DocToolsConstants.YES_CODE);
        }
        if (dto.getBondRequire() != null) {
            dto.setHasBondRequire(dto.getBondRequire().equals(DocToolsConstants.YES) ? DocToolsConstants.YES_CODE : DocToolsConstants.NO_CODE);
            dto.setNoBondRequire(dto.getBondRequire().equals(DocToolsConstants.YES) ? DocToolsConstants.NO_CODE : DocToolsConstants.YES_CODE);
        }
        // 根据是否电子开标替换段落
        if (dto.getIsElecBid() != null) {
            String openTimeAddressContent = "";
            String openProcessContent1 = "";
            String openProcessContent2 = "";
            if (dto.getIsElecBid().equals(DocToolsConstants.YES)) {
                dto.setHasElecBid(DocToolsConstants.YES_CODE);
                dto.setNoElecBid(DocToolsConstants.NO_CODE);
                openTimeAddressContent = DocToolsConstants.ONLINE_OPEN_TIME_ADDRESS_CONTENT;
                openProcessContent1 = DocToolsConstants.ONLINE_OPEN_PROCESS_CONTENT_1;
                openProcessContent2 = DocToolsConstants.ONLINE_OPEN_PROCESS_CONTENT_2;
            } else {
                dto.setHasElecBid(DocToolsConstants.NO_CODE);
                dto.setNoElecBid(DocToolsConstants.YES_CODE);
                openTimeAddressContent = DocToolsConstants.OFFLINE_OPEN_TIME_ADDRESS_CONTENT;
                openProcessContent1 = DocToolsConstants.OFFLINE_OPEN_PROCESS_CONTENT_1;
                openProcessContent2 = DocToolsConstants.OFFLINE_OPEN_PROCESS_CONTENT_2;
            }
            dto.setOpenTimeAddressContent(openTimeAddressContent);
            dto.setOpenProcessContent1(openProcessContent1);
            dto.setOpenProcessContent2(openProcessContent2);
        }
        return dto;
    }

    /**
     * 合同条款：所有类型
     * @param columnList
     * @return
     */
    private ContractTermsFillDTO convertCt(List<BidDocColumn> columnList) {
        List<BidDocColumn> tempList = columnList.stream().filter(s -> s.getCatalogId().equals(DocCatalogEnum.CATALOG_5.getType()))
                .collect(Collectors.toList());
        if (tempList.size() == 0) {
            return null;
        }
        Map<String, String> map = ConvertUtil.convertToMap(tempList, ContractTermsFillDTO.class);
        ContractTermsFillDTO dto = JSON.parseObject(JSON.toJSONString(map), ContractTermsFillDTO.class);
        return dto;
    }

    /**
     * 供货要求：公开招标-标准设备采购招标文件、邀请招标-标准设备采购招标文件
     * @param columnList
     * @return
     */
    private SupplyRequireFillDTO convertSr(List<BidDocColumn> columnList) {
        List<BidDocColumn> tempList = columnList.stream().filter(s -> s.getCatalogId().equals(DocCatalogEnum.CATALOG_6.getType()))
                .collect(Collectors.toList());
        if (tempList.size() == 0) {
            return null;
        }
        Map<String, String> map = ConvertUtil.convertToMap(tempList, SupplyRequireFillDTO.class);
        SupplyRequireFillDTO dto = JSON.parseObject(JSON.toJSONString(map), SupplyRequireFillDTO.class);
        return dto;
    }

    /**
     * 发包人要求：公开招标-简要标准施工招标文件、邀请招标-简要标准施工招标文件
     * @param columnList
     * @return
     */
    private EmployerRequireFillDTO convertEr(List<BidDocColumn> columnList) {
        List<BidDocColumn> tempList = columnList.stream().filter(s -> s.getCatalogId().equals(DocCatalogEnum.CATALOG_7.getType()))
                .collect(Collectors.toList());
        if (tempList.size() == 0) {
            return null;
        }
        Map<String, String> map = ConvertUtil.convertToMap(tempList, EmployerRequireFillDTO.class);
        EmployerRequireFillDTO dto = JSON.parseObject(JSON.toJSONString(map), EmployerRequireFillDTO.class);
        return dto;
    }

    /**
     * 技术标准和要求：公开招标-简要标准施工招标文件、邀请招标-简要标准施工招标文件
     * @param columnList
     * @return
     */
    private TecRequireFillDTO convertTr(List<BidDocColumn> columnList) {
        List<BidDocColumn> tempList = columnList.stream().filter(s -> s.getCatalogId().equals(DocCatalogEnum.CATALOG_10.getType()))
                .collect(Collectors.toList());
        if (tempList.size() == 0) {
            return null;
        }
        Map<String, String> map = ConvertUtil.convertToMap(tempList, TecRequireFillDTO.class);
        TecRequireFillDTO dto = JSON.parseObject(JSON.toJSONString(map), TecRequireFillDTO.class);
        return dto;
    }
}
