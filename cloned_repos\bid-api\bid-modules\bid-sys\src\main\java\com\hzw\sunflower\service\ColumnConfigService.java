package com.hzw.sunflower.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hzw.sunflower.controller.request.NumberExampleReq;
import com.hzw.sunflower.controller.request.ProjectDetailsReq;
import com.hzw.sunflower.controller.response.ProjectDetailsVo;
import com.hzw.sunflower.entity.ColumnConfig;

public interface ColumnConfigService extends IService<ColumnConfig> {

    /**
     * 保存项目字段详细信息配置
     * @param req
     * @return
     */
    Boolean insertColumnConfig(ProjectDetailsReq req);

    /**
     * 查看项目详细信息设置详情
     * @return
     */
    ProjectDetailsVo selectColumnConfig();

    /**
     * 查看项目详细信息设置详情
     * @return
     */
    ProjectDetailsVo selectColumnConfig(String moduleCode);

    /**
     * 生成示例编号
     * @param req
     * @return
     */
    String generateNumberExample(NumberExampleReq req);

    Integer getLicenseFunction();
}
