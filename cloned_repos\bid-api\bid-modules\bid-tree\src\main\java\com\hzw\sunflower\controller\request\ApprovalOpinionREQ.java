package com.hzw.sunflower.controller.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 审批请求实体
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@ApiModel(description = "审批接口实体")
@Data
public class ApprovalOpinionREQ {

    @ApiModelProperty(value = "公告ID", position = 1)
    private Long noticeId;

    @ApiModelProperty(value = "审核实例ID", position = 2)
    private String processInstanceId;

    @ApiModelProperty(value = "任务ID", position = 3)
    private String taskId;

    @ApiModelProperty(value = "是否同意，同意为true，不同意为false", position = 4)
    private Boolean isAgree;

    @ApiModelProperty(value = "审批意见，不同意时必填", position = 5)
    private String approvalOpinion;
}
