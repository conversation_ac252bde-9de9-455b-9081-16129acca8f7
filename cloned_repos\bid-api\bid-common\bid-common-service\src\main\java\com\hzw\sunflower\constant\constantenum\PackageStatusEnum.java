package com.hzw.sunflower.constant.constantenum;

/**
 * 包件状态
 *
 * <AUTHOR>
 * @date 2021-04-13
 */
public enum PackageStatusEnum {

    /**
     * 项目建档
     */
    INIT(0, "项目建档"),

    /**
     * 进行中
     */
    PROCESSING(1, "进行中"),

    /**
     * 划分标段/包
     */
    DIVIDE_SECTION(5, "划分标段/包"),

    /**
     * 投标邀请
     */
    TENDER_INVITATION(10, "投标邀请"),

    /**
     * 根据公告状态判断可编辑
     */
    EDIT_BY_NOTICE(11, "可编辑"),

    /**
     * 根据公告状态判断不可编辑
     */
    READONLY_BY_NOTICE(12, "不可编辑"),

    /**
     * 公告已发布
     */
    NOTICE_ISSUED(13, "公告已发布"),

    /**
     * 发标
     */
    ANNOUNCING(20, "发标"),

    /**
     * 开标
     */
    OPENING(30, "开标"),

    /**
     * 供应商已签到
     */
    OPENING_SIGNED_IN(31, "供应商已签到"),

    /**
     * 评标报告文件已上传
     */
    OPENING_REPORTED(32, "评标报告文件已上传"),

    /**
     * 定标
     */
    SUBMITTING(40, "定标"),

    /**
     * 中标候选人待确认
     */
    SUBMITTING_WINNER_CANDIDATE(41, "中标候选人待确认"),

    /**
     * 已确认中标人
     */
    SUBMITTING_WINNER_CONFIRMED(42, "已确认中标人"),

    /**
     * 中标结果公示
     */
    SUBMITTING_WINNER_BULLETIN(43, "中标结果公示"),

    /**
     * 存根
     */
    SUBMITTING_WINNER_NOTICE_STUB(44, "中标通知书存根已保存"),

    /**
     * 中标通知书已申请
     */
    SUBMITTING_WINNER_NOTICE_AUDIT(45, "中标通知书已申请"),

    /**
     * 中标通知书已盖章
     */
    SUBMITTING_WINNER_NOTICE(46, "中标通知书已盖章"),

    /**
     * 归档    55 已退回，54 已移交，53 待接收 , 52 未移交，51 暂存
     */
    ARCHIVING(50, "归档"),
    ARCHIVING_TEMP(51, "暂存"),
    ARCHIVING_NOT_SUBMIT(52, "未移交"),
    ARCHIVING_NOT_RECEIVED(53, "待接收"),
    ARCHIVING_HAS_RECEIVED(54, "已移交"),
    ARCHIVING_RETURN(55, "已退回"),
    ARCHIVING_OFFLINE_NOT_MUST_RECEIVED(56, "线下无需归档"),
    ARCHIVING_EDITABLE(57, "资格预审归档可编辑"),
    ARCHIVING_UN_EDITABLE(58, "资格预审归档不可编辑"),

    ARCHIVING_RECALL_NOT_RECEIVED(59, "撤回待确认"),
    ARCHIVING_RECALL_HAS_RECEIVED(65, "已撤回"),

    /**
     * 办结
     */
    OVER(60, "办结"),

    EXCEPT(70,"异常"),
    EXCEPT_SUSPEND (71,"暂停"),
    EXCEPT_STOP (72,"终止");
    private final Integer value;

    private final String name;

    PackageStatusEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    public Integer getValue() {
        return value;
    }

    public String getName() {
        return name;
    }

    /**
     * 匹配操作码
     */
    public static PackageStatusEnum matchType(String type) {
        for (PackageStatusEnum projectStatusEnum : PackageStatusEnum.values()) {
            if (projectStatusEnum.value.toString().equals(type)) {
                return projectStatusEnum;
            }
        }
        return null;
    }

    /**
     * 匹配操作码
     */
    public static PackageStatusEnum matchType(int type) {
        for (PackageStatusEnum projectStatusEnum : PackageStatusEnum.values()) {
            if (projectStatusEnum.value == type) {
                return projectStatusEnum;
            }
        }
        return null;
    }

    /**
     * 获得标段状态大阶段名称
     *
     * @return 标段状态阶段名称
     */
    public static String getPhaseName(PackageStatusEnum packageStatusEnum) {
        int code = packageStatusEnum.value;
        int grantCode = code / 10 * 10;
        PackageStatusEnum target = matchType(grantCode);
        if (target == null) {
            return "";
        } else {
            return target.name;
        }
    }
}
