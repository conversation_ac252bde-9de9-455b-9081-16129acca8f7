package com.hzw.sunflower.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.hzw.sunflower.common.BaseBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 用户创建代理的记录(t_user_agent_record)
 *
 * <AUTHOR>
 * @version 1.0.0 2021-04-14
 */
@ApiModel(description = "代理机构审批表")
@TableName("t_project_agent_approval")
@Data
public class ProjectAgentApproval extends BaseBean {
    /**
     * 版本号
     */
    private static final long serialVersionUID = -8268108670722582133L;

    /* This code was generated by TableGo tools, mark 1 begin. */

    @ApiModelProperty(value = "主键", position = 1)
    private Long id;

    @ApiModelProperty(value = "代理机构id", position = 3)
    private Long agentId;

    @ApiModelProperty(value = "代理机构名称", position = 4)
    private String agentCompany;

    @ApiModelProperty(value = "代理机构联系人名称", position = 5)
    private String agentChargeName;

    @ApiModelProperty(value = "代理机构联系人id", position = 6)
    private Long agentChargeId;

    @ApiModelProperty(value = "代理机构联系人手机号", position = 7)
    private String agentChargePhone;

    @ApiModelProperty(value = "项目id")
    private Long projectId;

    @ApiModelProperty(value = "代理机构部门id")
    private Long agentDepartmentId;

    @ApiModelProperty(value = "审核状态 12待运管处审核 13 运管处退回  14待处长审核  15 处长退回  16 待项目经理审审核  17 项目经理退回")
    private Integer status;

    @ApiModelProperty(value = "是否选择到人 1是 2否")
    private Integer isConfirm;

    @ApiModelProperty(value = "是否跳过处长审核1是 2否")
    private Integer isSkipDepartment;



}