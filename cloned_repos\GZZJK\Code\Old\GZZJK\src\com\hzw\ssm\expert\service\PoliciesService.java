package com.hzw.ssm.expert.service;

import com.hzw.ssm.applets.utils.GetJsonRespData;
import com.hzw.ssm.applets.utils.ResultJson;
import com.hzw.ssm.expert.dao.PoliciesDetailsMapper;
import com.hzw.ssm.expert.dao.PoliciesMapper;
import com.hzw.ssm.expert.entity.Policies;
import com.hzw.ssm.expert.entity.PoliciesDetails;
import com.hzw.ssm.fw.base.BaseService;
import com.hzw.ssm.sys.user.dao.UserMapper;
import com.hzw.ssm.sys.user.entity.UserEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
public class PoliciesService extends BaseService {

    @Autowired
    private PoliciesMapper policiesMapper;


    @Autowired
    private PoliciesDetailsMapper policiesDetailsMapper;


    @Autowired
    private UserMapper userMapper;


    public Integer getPoliciesCount(String status) {
        Policies policies = new Policies();
        policies.setStatus(status);
        List<Policies> policiesList = policiesMapper.queryPoliciesByCondition(policies);
        if (policiesList.isEmpty()) {
            return 0;
        } else {
            return policiesList.size();
        }

    }


    public List<Policies> getPagePolicies(Policies entity) {
        List<Policies> policies = policiesMapper.queryPagePoliciesByCondition(entity);
        for (int i = 0; i < policies.size(); i++) {


            PoliciesDetails policiesDetails = new PoliciesDetails();
            policiesDetails.setPoliciesId(policies.get(i).getId());
            List<PoliciesDetails> condition = policiesDetailsMapper.findPoliciesDetailsByCondition(policiesDetails);
            if (!condition.isEmpty()) {
                policies.get(i).setPoliciesDetailsCount(condition.size());
            }
            String createUser = policies.get(i).getCreateUser();
            String updateUser = policies.get(i).getUpdateUser();
            if (createUser != null && createUser.length() > 0) {
                UserEntity createUserObj = userMapper.selectUserById(createUser);
                if (createUserObj != null) {
                    policies.get(i).setCreateUser(createUserObj.getUser_name());
                }
            }
            if (updateUser != null && updateUser.length() > 0) {
                UserEntity updateUserObj = userMapper.selectUserById(updateUser);
                if (updateUserObj != null) {
                    policies.get(i).setUpdateUser(updateUserObj.getUser_name());
                }
            }


        }
        return policies;
    }


    public List<Policies> findPoliciesAll(Policies entity) {
        return policiesMapper.findPoliciesAll(entity);
    }

    public Policies findPoliciesById(String id) {
        return policiesMapper.findPoliciesById(id);
    }

    public String addPolicies(String name, String getUser_id) {
        Map result = new HashMap();
        if (name != null && name.trim().length() > 0) {
            Policies policies = new Policies();
            policies.setName(name);
            List<Policies> policiesAll = policiesMapper.queryPoliciesByCondition(policies);
            if (!policiesAll.isEmpty()) {
                result.put("code", 1);
                result.put("msg", "名称不能重复!");
            } else {
                policies.setCreateTime(new Date());
                //  policies.setUpdateTime(new Date());
                policies.setId(UUID.randomUUID().toString().replaceAll("-", ""));
                policies.setStatus("0");
                policies.setCreateUser(getUser_id);
                //    policies.setUpdateUser(getUser_id);
                Integer sort = policiesMapper.findSortMax();
                if (sort == null) {
                    sort = 0;
                }
                policies.setSort(sort + 1);
                Integer integer = policiesMapper.addPolicies(policies);
                if (integer != 0) {
                    result.put("code", 0);
                    result.put("msg", "新增成功!");
                } else {
                    result.put("code", 1);
                    result.put("msg", "新增失败!");
                }
            }

        } else {
            result.put("code", 1);
            result.put("msg", "名称不能为空!");
        }
        GetJsonRespData.getJsonRespData(result);
        return null;
    }


    public String updatePoliciesDb(String id, String name, String getUser_id) {

        Map result = new HashMap();

        if (name != null && name.trim().length() > 0) {
            Policies policies = new Policies();
            policies.setName(name);
            List<Policies> policiesAll = policiesMapper.queryPoliciesByCondition(policies);
            if (!policiesAll.isEmpty() && !policiesAll.get(0).getId().equalsIgnoreCase(id)) {
                result.put("code", 1);
                result.put("msg", "名称不能重复!");
            } else {
                policies = policiesMapper.findPoliciesById(id);
                policies.setName(name);
                policies.setUpdateTime(new Date());
                policies.setUpdateUser(getUser_id);
                Integer integer = policiesMapper.updatePolicies(policies);
                if (integer != 0) {
                    result.put("code", 0);
                    result.put("msg", "修改成功!");
                } else {
                    result.put("code", 1);
                    result.put("msg", "修改失败!");
                }
            }


        } else {
            result.put("code", 1);
            result.put("msg", "名称不能为空!");
        }
        GetJsonRespData.getJsonRespData(result);
        return null;

    }

    public Object updateStatus(String id, String status, String user_id) {
        Map result = new HashMap();

        Policies policies = policiesMapper.findPoliciesById(id);
        if (policies != null) {
            policies.setStatus(status);
            policies.setUpdateUser(user_id);
            policies.setUpdateTime(new Date());
            Integer integer = policiesMapper.updatePolicies(policies);

            if (status.equalsIgnoreCase("1")) {
                if (integer != 0) {
                    result.put("code", 0);
                    result.put("msg", "禁用成功!");
                } else {
                    result.put("code", 1);
                    result.put("msg", "禁用失败!");
                }
            } else {
                if (integer != 0) {
                    result.put("code", 0);
                    result.put("msg", "启用成功!");
                } else {
                    result.put("code", 1);
                    result.put("msg", "启用失败!");
                }
            }


        } else {
            result.put("code", 2);
            result.put("msg", "当前数据不存在!");
        }

        GetJsonRespData.getJsonRespData(result);

        return null;
    }

    public Object updateReviseSort(Integer sort, boolean reviseSortFlag, String status) {
        Map result = new HashMap();
        Policies policiesNew = new Policies();
        policiesNew.setSort(sort);
        policiesNew = policiesMapper.queryPoliciesByConditionOne(policiesNew);
        if (policiesNew != null) {
            if (!reviseSortFlag) {
                //上移
                Policies policiesOld;
                Integer sortOld=policiesNew.getSort();
                Integer sortMin = policiesMapper.findSortMin();
                while (true) {
                    sortOld = ( sortOld- 1);
                    policiesOld = new Policies();
                    policiesOld.setSort(sortOld);
                    policiesOld.setStatus(status);
                    policiesOld = policiesMapper.queryPoliciesByConditionOne(policiesOld);
                    if (policiesOld != null||sortOld<sortMin) {
                        break;
                    }
                }

                if (policiesOld != null) {

                    policiesNew.setSort(policiesOld.getSort());
                    Integer policiesNewInt = policiesMapper.updatePolicies(policiesNew);
                    policiesOld.setSort(sort);
                    Integer policiesOldInt = policiesMapper.updatePolicies(policiesOld);
                    if (policiesNewInt != 0 && policiesOldInt != 0) {
                        result.put("code", 0);
                        result.put("msg", "下移成功!");
                    } else {
                        result.put("code", 1);
                        result.put("msg", "下移成功!");
                    }

                } else {
                    result.put("code", 2);
                    result.put("msg", "对换下移对象不存在!");
                }

            } else {

                Integer sortOld=policiesNew.getSort();
                Policies policiesOld;
                Integer sortMax = policiesMapper.findSortMax();
                while (true) {
                    //上移
                    sortOld = ( sortOld+ 1);
                    policiesOld = new Policies();
                    policiesOld.setSort(sortOld);
                    policiesOld.setStatus(status);
                    policiesOld = policiesMapper.queryPoliciesByConditionOne(policiesOld);
                    if (policiesOld != null||sortOld>sortMax) {
                        break;
                    }
                }

                if (policiesOld != null) {

                    policiesNew.setSort(policiesOld.getSort());
                    Integer policiesNewInt = policiesMapper.updatePolicies(policiesNew);
                    policiesOld.setSort(sort);
                    Integer policiesOldInt = policiesMapper.updatePolicies(policiesOld);
                    if (policiesNewInt != 0 && policiesOldInt != 0) {
                        result.put("code", 0);
                        result.put("msg", "上移成功!");
                    } else {
                        result.put("code", 1);
                        result.put("msg", "上移失败!");
                    }

                } else {
                    result.put("code", 2);
                    result.put("msg", "对换上移对象不存在!");
                }
            }
        } else {
            result.put("code", 3);
            result.put("msg", "排序失败当前数据小于二条!");
        }


        GetJsonRespData.getJsonRespData(result);

        return null;
    }

    public void byIdDelete(String id) {

    }

    public ResultJson queryAllEffective(Policies policies) {
        List<Map<String, Object>> list = new ArrayList<>();
        List<Map<String, Object>> policiesList = policiesMapper.queryAllEffective(policies);
        for (Map<String, Object> map : policiesList) {
            Map<String, Object> info = new HashMap<>(16);
            info.put("id", map.get("ID"));
            info.put("name", map.get("NAME"));
            list.add(info);
        }
        return new ResultJson(list);
    }


}
