package com.hzw.ssm.expert.entity;

import com.hzw.ssm.fw.base.BaseEntity;

/**
 * 国家评标专业表
 * <AUTHOR>
 * @date 2014-10-09
 */
public class SpecialtyCouInfoEntity extends BaseEntity {
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 5929886403106519554L;
	
	private String spe_cou_id;//专业id
	
	private String spe_cou_name;//专业名称
	
	private String spe_cou_code;//专业代码
	
	private String spe_cou_parent;//父类的主键
	
	private Long spe_cou_level;//级别
	
	private String spe_cou_remark;//备注

	public String getSpe_cou_id() {
		return spe_cou_id;
	}

	public void setSpe_cou_id(String speCouId) {
		spe_cou_id = speCouId;
	}

	public String getSpe_cou_name() {
		return spe_cou_name;
	}

	public void setSpe_cou_name(String speCouName) {
		spe_cou_name = speCouName;
	}

	public String getSpe_cou_code() {
		return spe_cou_code;
	}

	public void setSpe_cou_code(String speCouCode) {
		spe_cou_code = speCouCode;
	}

	public String getSpe_cou_parent() {
		return spe_cou_parent;
	}

	public void setSpe_cou_parent(String speCouParent) {
		spe_cou_parent = speCouParent;
	}

	public Long getSpe_cou_level() {
		return spe_cou_level;
	}

	public void setSpe_cou_level(Long speCouLevel) {
		spe_cou_level = speCouLevel;
	}

	public String getSpe_cou_remark() {
		return spe_cou_remark;
	}

	public void setSpe_cou_remark(String speCouRemark) {
		spe_cou_remark = speCouRemark;
	}

}
