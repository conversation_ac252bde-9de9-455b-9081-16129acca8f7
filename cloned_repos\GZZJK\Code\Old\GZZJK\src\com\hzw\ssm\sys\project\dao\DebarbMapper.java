package com.hzw.ssm.sys.project.dao;

import java.util.List;

import com.hzw.ssm.sys.project.entity.ExtractDebarbEntity;



public interface DebarbMapper {

	/**
	 * 保存回避数据
	 * @param project
	 */
	public void saveDebarbData(ExtractDebarbEntity entity);
	/**
	 * 根据批次号查询回避数据
	 * @param entity
	 * @return
	 */
	public List<ExtractDebarbEntity> queryExtractDebarbList(ExtractDebarbEntity entity);
	
	/**
	 * 根据批次号删除数据
	 * @param entity
	 */
	public void deleteDebarbData(ExtractDebarbEntity entity);
}
