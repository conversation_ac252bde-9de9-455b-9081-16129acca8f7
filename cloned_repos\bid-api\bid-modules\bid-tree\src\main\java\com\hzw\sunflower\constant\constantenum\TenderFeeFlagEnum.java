package com.hzw.sunflower.constant.constantenum;

/**
 * <AUTHOR>
 * @date ：Created in 2021/11/19 10:27
 * @description：标书费是否收费类型
 * @version: 1.0
 */
public enum TenderFeeFlagEnum {
    CHARGE(1, "收费"),
    FREE(2, "免费");

    private Integer value;

    private String name;

    TenderFeeFlagEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    public Integer getValue() {
        return value;
    }

    public String getName() {
        return name;
    }
}
