package com.hzw.sunflower.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 设备需求导入对象
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-10
 */
@Data
public class BidDocDeviceExcelDto implements Serializable {

    private static final long serialVersionUID = 1L;

    @ExcelProperty(value = "设备名称",index = 0)
    private String deviceName;

    @ExcelProperty(value = "规格",index = 1)
    private String specifications;

    @ExcelProperty(value = "数量及单位",index = 2)
    private String quantityUnit;

    @ExcelProperty(value = "交货期",index = 3)
    private String deliveryDate;

    @ExcelProperty(value = "交货地点",index = 4)
    private String deliveryAddress;

}
