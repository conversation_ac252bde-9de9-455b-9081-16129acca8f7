package com.hzw.sunflower.constant.constantenum;

/**
 * <AUTHOR>
 * @date ：Created in 2021/7/15 14:29
 * @description：招标文件时间类型
 * @version: 1.0
 */
public enum TimeTypeEnum {
    CONFIRM(1, "确定时间"),

    UNCONFIRMED(2, "另行通知"),

    PREVIOUS_DAY(3, "投标文件递交截止时间前一天"),

    // 澄清异议时间类型
    C_CONFIRM(1, "确定时间"),
    C_PREVIOUS_DAY(2, "投标文件递交截止时间前一天"),

    // 是否支持澄清异议
    SUPPORT(1,"支持"),
    NOT_SUPPORT(2,"不支持")
    ;

    private Integer value;

    private String name;

    TimeTypeEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    public Integer getValue() {
        return value;
    }

    public String getName() {
        return name;
    }
}
