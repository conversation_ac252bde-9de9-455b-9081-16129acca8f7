server.version=1.8.0

server.address=
server.port=4000

filesize-max=5242880
filename-max=50

files.storage=
files.storage.folder=/data/documents

files.docservice.timeout=120000
files.docservice.history.postfix=-hist

files.docservice.url.site=http://*************:28940/
files.docservice.url.converter=ConvertService.ashx
files.docservice.url.command=coauthoring/CommandService.ashx
files.docservice.url.api=web-apps/apps/api/documents/api.js
files.docservice.url.preloader=web-apps/apps/api/documents/cache-scripts.html
files.docservice.url.example=

files.docservice.secret=xU7Qg6Ky8JvvNAc8s0m72YZn7TVE3NQc
files.docservice.header=Authorization
files.docservice.token-use-for-request=true

files.docservice.verify-peer-off=true

files.docservice.languages=en:English|ar:Arabic|hy:Armenian|az:Azerbaijani|eu:Basque|be:Belarusian|bg:Bulgarian|ca:Catalan|zh:Chinese (Simplified)|zh-TW:Chinese (Traditional)|cs:Czech|da:Danish|nl:Dutch|fi:Finnish|fr:French|gl:Galego|de:German|el:Greek|hu:Hungarian|id:Indonesian|it:Italian|ja:Japanese|ko:Korean|lo:Lao|lv:Latvian|ms:Malay (Malaysia)|no:Norwegian|pl:Polish|pt:Portuguese (Brazil)|pt-PT:Portuguese (Portugal)|ro:Romanian|ru:Russian|sr-Latn-CS:Serbian|si:Sinhala (Sri Lanka)|sk:Slovak|sl:Slovenian|es:Spanish|sv:Swedish|tr:Turkish|uk:Ukrainian|vi:Vietnamese|aa-AA:Test Language

spring.datasource.url=jdbc:h2:mem:usersdb
spring.datasource.driverClassName=org.h2.Driver
spring.datasource.username=sa
spring.datasource.password=password
spring.jpa.database-platform=org.hibernate.dialect.H2Dialect
hibernate.ddl-auto
spring.h2.console.enabled=true
spring.h2.console.path=/h2
spring.servlet.multipart.max-file-size=5MB
spring.servlet.multipart.max-request-size=5MB

url.index=/
url.converter=/converter
url.editor=/editor
url.track=/track
url.download=/download

log.level=trace
log.root.level=trace
