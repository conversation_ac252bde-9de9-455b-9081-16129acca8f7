package com.hzw.sunflower.entity.condition;

import com.hzw.sunflower.common.BaseCondition;
import io.swagger.annotations.ApiModel;

/**
 * 用户创建代理的记录查询条件
 *
 * <AUTHOR>
 * @version 1.0.0 2021-04-14
 */
@ApiModel(description = "用户创建代理的记录查询条件")
public class UserAgentRecordCondition extends BaseCondition {
    /**
     * 版本号
     */
    private static final long serialVersionUID = -8268108670722582133L;
}