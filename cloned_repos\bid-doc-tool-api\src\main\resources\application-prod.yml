server:
  port: 28922
  servlet:
    context-path: /ambV2
  max-http-header-size: 100000
#datasource
spring:
  main:
    allow-circular-references: true
  application:
    name: bid-api-ambV2
  datasource:
    url: *********************************************************************************************************************************************************************************************************
    username: bid-test53
    password: bid-test53
    driver-class-name: com.mysql.cj.jdbc.Driver
    #Spring Boot 默认是不注入这些属性值的，需要自己绑定
    #druid 数据源专有配置
    initialSize: 5
    minIdle: 5
    maxActive: 20
    maxWait: 60000
    timeBetweenEvictionRunsMillis: 60000
    minEvictableIdleTimeMillis: 300000
    validationQuery: SELECT 1 FROM DUAL
    testWhileIdle: true
    testOnBorrow: false
    testOnReturn: false
    poolPreparedStatements: true

    #配置监控统计拦截的filters，stat:监控统计、log4j：日志记录、wall：防御sql注入
    #如果允许时报错  java.lang.ClassNotFoundException: org.apache.log4j.Priority
    #则导入 log4j 依赖即可，Maven 地址：https://mvnrepository.com/artifact/log4j/log4j
    filters: stat,wall,log4j
    maxPoolPreparedStatementPerConnectionSize: 20
    useGlobalDataSourceStat: true
    connectionProperties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=500
  jackson:
    # 全局设置@JsonFormat的格式pattern
    date-format: yyyy-MM-dd HH:mm:ss
    # 当地时区
    locale: zh
    # 设置全局时区
    time-zone: GMT+8
    # 常用，全局设置pojo或被@JsonInclude注解的属性的序列化方式
    #    default-property-inclusion: non_null #不为空的属性才会序列化,具体属性可看JsonInclude.Include
    # 常规默认,枚举类SerializationFeature中的枚举属性为key，值为boolean设置jackson序列化特性,具体key请看SerializationFeature源码
    serialization:
      write-dates-as-timestamps: false # 返回的java.util.date转换成timestamp
      FAIL_ON_EMPTY_BEANS: true # 对象为空时是否报错，默认true
  servlet:
    multipart:
      # 开启 multipart 上传功能
      enabled: true
      # 文件写入磁盘的阈值
      file-size-threshold: 2KB
      # 最大文件大小
      max-file-size: 200MB
      # 最大请求大小
      max-request-size: 215MB

  resources:
    add-mappings: true
  mvc:
    pathmatch:
      matching-strategy: ANT_PATH_MATCHER
  throw-exception-if-no-handler-found: true
  redis:
    # 地址
    host: **************
    # 端口，默认为6379
    port: 26379
    # 数据库索引
    database: 0
    # 密码
    password: TR0Y9s27Oda6oqKs
    # 连接超时时间
    timeout: 10s
    lettuce:
      pool:
        # 连接池中的最小空闲连接
        min-idle: 0
        # 连接池中的最大空闲连接
        max-idle: 8
        # 连接池的最大数据库连接数
        max-active: 8
        # #连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1ms
  rabbitmq:
    # 地址
    host: **************
    # 端口
    port: 25672
    # 账号
    username: hzw
    # 密码
    password: TR0Y9s27Oda6oqKs
    publisher-confirm-type: correlated
    publisher-returns: true
    listener:
      simple:
        retry:
          enabled: true
          max-attempts: 3
          initial-interval: 2000
        default-requeue-rejected: false

#log
logging:
  # 过滤开关
  enabled: true
  level:
    root: INFO
    com:
      hzw:
        sunflower: trace
  file:
    path: /log
  logback:
    rollingpolicy:
      max-file-size: 10MB
      max-history: 60

swagger:
  urlPrefix: http://
  termsOfServiceUrl: /doc.html
knife4j:
  enable: true
  openapi:
    title: amobeV2接口
    description: "amobeV2接口"
    email: xxxx
    concat: amobeV2
    url: https://127.0.0.1
    version: v2.3
    license: Apache 2.0
    license-url: https://127.0.0.1
    terms-of-service-url: https://127.0.0.1
    group:
      test1:
        group-name: 分组
        api-rule: package
        api-rule-resources:
          - com.hzw.sunflower

mybatis-plus:
  global-config:
    #数据库相关配置
    db-config:
      #主键类型  AUTO:"数据库ID自增", INPUT:"用户输入ID", ID_WORKER:"全局唯一ID (数字类型唯一ID)", UUID:"全局唯一ID UUID";
      id-type: AUTO
  configuration:
    map-underscore-to-camel-case: true
    typeAliasesPackage: com.hzw.sunflower.entity
    mapper-locations: mapper/**/*Mapper.xml
    config-location: mybatis/mybatis-config.xml
    #logic-delete-field: isDelete  # 全局逻辑删除的实体字段名(since 3.3.0,配置后可以忽略不配置步骤2)
    logic-delete-value: 1 # 逻辑已删除值(默认为 1)
    logic-not-delete-value: 0 # 逻辑未删除值(默认为 0)
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    default-scripting-language: com.hzw.sunflower.mybatis.SunflowerMybatisLanguageDriver

# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /system/notice/*
  # 匹配链接（多个用逗号分隔）
  urlPatterns: /company/*,/monitor/*,/tool/*

#模板文件配置
files:
  template:
    path: /sunflower/template/
  temporary:
    path: /sunflower/temporary/
  sign:
    path: /data/esign/
email:
  qq:
    mailServerHost: imap.qq.com
    mailServerPort: 993
  temporary:
    path: /sunflower/temporary/


#天眼查配置
tianyan:
  token: e5ea09dd-5c42-4f14-8fc1-297de3ef77ce
  keywordsInfoUrl: http://open.api.tianyancha.com/services/open/search/2.0
  baseInfoUrl: http://open.api.tianyancha.com/services/open/ic/baseinfo/2.0
  baseInfoUrlSpecial: http://open.api.tianyancha.com/services/open/ic/baseinfo/special
  baseInfoUrlNormal: http://open.api.tianyancha.com/services/open/ic/baseinfo/normal
  validate: false

#实名认证 聚合
juhe:
  appcode: 聚合手机三要素SHA256
  apiUri: http://apis.juhe.cn/telecom_sha256/query
  validate: false
  key: 72f3a310efc4f0a79d16a3eebc869bee
  telecom2:
    appcode: 姓名手机号二要素验证SHA256版
    apiUri: http://apis.juhe.cn/telecom2_sha256/query
    key: d883398e6d8a77e9a803b5675704226c
  nameIdCard:
    appkey: f4715774227799335a3575d3d7709316
    queryUrl: http://op.juhe.cn/idcard/query?key=
    queryEncryUrl: http://op.juhe.cn/idcard/queryEncry?key=

#当前系统租户信息标志当前系统的管理方（组织机构代码）
organ:
  organization_num: 91320000134750085T
  organization_type: 2
  org_phone: ************
  org_website: www.jstcc.cn


#流程引擎接口地址
workflow:
  flow-api-url: http://**************:28988
  httppool:
    maxtotal: 64
    default-max-per-route: 64
    connect-timeout: 10000
    connection-request-timeout: 10000
    socket-timeout: 10000
    validate-after-inactivity: 10000
  api:
    app-sn: portal
    app-secret-key: K9909Jszc%YcleRr50c9aRO88q5QdxK#~0XeJz8FU89092MA5ytwyE%bFTL0MUwF
  jumpSameUser: false

oss:
  active: ctyun
  aliyun:
    endpoint: https://oss-cn-hangzhou.aliyuncs.com
    accessKeyId: LTAI5tAAyeuzcooZmRmsQ9eh
    accessKeySecret: ******************************
    bucketName: jtcc-dev
  ctyun:
    endpoint: oos-js.ctyunapi.cn
    accessKeyId: 37bee479813813bc76e3
    accessKeySecret: e2cbcf431ab590d2b1d05cf74d219df4fc44793b
    bucketName: jtcc-dev
#njbk配置
njbk:
  channelNO: 3200097
  bizType: 1
  paycertfile: /sunflower/njbk/test.pfx
  relAcctNbr: '0120250000000258'
  certPassword: '111111'
  url: https://openapi-dev.njcb.com.cn:23443/openapi/OPEN
  appID: 0f89cfb8_8bb9_4017_b54d_5e7c32f28a42
  channelID: COMMON
  publickey1024: MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC4ObfNuxe+******************************/LRhY+Tns/IVvgV3ZoF0A7qjNS4WvAtij8ypqcviuhsrpiQeqeMBbzY+hh7lrBf7dPM4XxSkx396I+a0FYYN5z1Qe40+IlRLp7+lNHil27h863WCK8wM0nzEEU3Lf0f0/CLQIDAQAB
#专家抽取
expert:
  api-url: http://**************:28081
  file-url: http://**************:28918
  httppool:
    maxtotal: 64
    default-max-per-route: 64
    connect-timeout: 10000
    connection-request-timeout: 10000
    socket-timeout: 10000
    validate-after-inactivity: 10000
  api:
    app-sn: bid_sunflower
    app-secret-key: 123456
#云屋
cloudroom:
  url: https://www.cloudroom.com/
  createRoomUrl: CLOUDROOM-API/room/create
  compId: 261155
  compSecret: 9e7d328aaa69d36ff8233e14f42c2ab7
  appId: knnh4gxwv4


invoice:
  retryCount: 100
