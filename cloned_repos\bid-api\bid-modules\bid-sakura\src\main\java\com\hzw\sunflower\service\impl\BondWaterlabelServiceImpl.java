package com.hzw.sunflower.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.dao.BondWaterlabelMapper;
import com.hzw.sunflower.entity.*;
import com.hzw.sunflower.exception.ExceptionEnum;
import com.hzw.sunflower.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * @ClassName:BondWaterlabelServiceImpl
 * @Auther: lijinxin
 * @Description: 保证金流水标签
 * @Date: 2023/5/10 16:48
 * @Version: v1.0
 */
@Service
public class BondWaterlabelServiceImpl extends ServiceImpl<BondWaterlabelMapper, BondWaterlabel> implements BondWaterlabelService {

    @Autowired
    private ProjectService projectService;

    @Autowired
    private BondWaterReturnService bondWaterReturnService;

    @Autowired
    private BondApplyRefundService bondApplyRefundService;

    @Autowired
    private BondRelationService bondRelationService;

    @Autowired
    private BondRefundService bondRefundService;

    @Autowired
    private BondRefundBalanceService bondRefundBalanceService;

    @Override
    public Result<Object> add(BondWaterlabel bondWaterlabel) {
        if(bondWaterReturnService.checkReturn(bondWaterlabel.getWaterId())){
            return Result.failed();
        }
        // 校验是否已经申请线上退回
        //查询是否有退回记录 存在线上退还数据 不能申请
        LambdaQueryWrapper<BondApplyRefund> lwq  =new LambdaQueryWrapper<>();
        lwq.eq(BondApplyRefund::getWaterId,bondWaterlabel.getWaterId());
        long count = bondApplyRefundService.count(lwq);
        if(count > 0 ){
            return Result.failed();
        }

        LambdaQueryWrapper<BondRefund> lwqr  =new LambdaQueryWrapper<>();
        lwqr.eq(BondRefund::getWaterId,bondWaterlabel.getWaterId());
        long countr = bondRefundService.count(lwqr);
        if(countr > 0 ){
            return Result.failed();
        }
        // 判断存不存在关联数据
        if(bondRelationService.checkBondRelationByWaterId(bondWaterlabel.getWaterId()) > 0){
            return Result.failed();
        }

         // 校验项目编号是否存在
         if( bondWaterlabel.getIsProject().equals(1) ){
             if(null == bondWaterlabel.getPurchaseNumber()){
                 return Result.failed(ExceptionEnum.PROJECT_NAME_ERROE.getCode(),ExceptionEnum.PROJECT_NAME_ERROE.getMessage());
             }
             LambdaQueryWrapper<Project> lqw = new LambdaQueryWrapper<>();
             lqw.eq(Project::getPurchaseNumber,bondWaterlabel.getPurchaseNumber());
             List<Project> projects = projectService.getBaseMapper().selectList(lqw);
             if(null == projects || projects.size() == 0 ){
                 return Result.failed(ExceptionEnum.PROJECT_NAME_ERROE.getCode(),ExceptionEnum.PROJECT_NAME_ERROE.getMessage());
             }
         }
         //如果是收款需要增加余额表总额，其他要减去余额表总额
        LambdaQueryWrapper<BondWaterlabel> query = new LambdaQueryWrapper<>();
        query.eq(BondWaterlabel::getWaterId,bondWaterlabel.getWaterId());
        query.orderByDesc(BondWaterlabel::getCreatedTime).last("limit 1");
        BondWaterlabel lastLabel = this.getOne(query);
        bondRefundBalanceService.changeTotalAmount(bondWaterlabel.getWaterId(),bondWaterlabel.getLabel(),lastLabel);
        // 新增数据
        boolean save = this.save(bondWaterlabel);
        return  Result.ok(save);
    }

    /**
     * 保证金流水标签校验
     * 判断最新的标签是不是收款标签
     * 最新的状态不是收款返回false
     * @param waterId
     * @return
     */
    @Override
    public Boolean getWaterlabelCheck(Long waterId) {
        Integer waterlabelCheck = this.getBaseMapper().getWaterlabelCheck(waterId);
        if(waterlabelCheck > 0){
            return false;
        }
        return true;
    }

    @Override
    public Integer chekecWaterLabel(List<Long> waterIds) {
        return this.getBaseMapper().chekecWaterLabel(waterIds);
    }
}
