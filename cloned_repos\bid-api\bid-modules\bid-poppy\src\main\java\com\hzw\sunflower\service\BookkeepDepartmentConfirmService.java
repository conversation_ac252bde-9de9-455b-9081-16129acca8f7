package com.hzw.sunflower.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hzw.sunflower.controller.project.request.ConfirmAgreeReq;
import com.hzw.sunflower.controller.project.request.ProjectIdsReq;
import com.hzw.sunflower.controller.project.response.BookkeepDepartmentConfirmVo;
import com.hzw.sunflower.dto.BookkeepDepartmentConfirmDTO;
import com.hzw.sunflower.entity.BookkeepDepartmentConfirm;
import com.hzw.sunflower.entity.JwtUser;
import com.hzw.sunflower.entity.condition.BookkeepDepartmentConfirmCondition;

import java.util.List;
import java.util.Map;

public interface BookkeepDepartmentConfirmService extends IService<BookkeepDepartmentConfirm> {
    /**
     * 角标是否展示
     * @param project
     * @return
     */
    Map<Long,Integer> isShow(ProjectIdsReq project);

    /**
     * 分摊人确认
     * @param confirmAgreeReq
     * @return
     */
    Boolean saveIsAgree(ConfirmAgreeReq confirmAgreeReq);

    /**
     * 分摊人确认列表
     * @param condition
     * @param jwtUser
     * @return
     */
    IPage<BookkeepDepartmentConfirmVo> queryShareList(BookkeepDepartmentConfirmCondition condition, JwtUser jwtUser);

    /**
     * 分摊人确认详情
     * @param projectId
     * @return
     */
    List<BookkeepDepartmentConfirmDTO> shareListDetail(Long projectId);

    /**
     * 发送短信
     * @param projectId
     * @return
     */
    Boolean sendConfirmSms(Long projectId, Long agentChargeId,Long userId, Integer type);



    /**
     * 保存分摊人信息
     * @param confirm
     * @return
     */
    Boolean saveBookKeepConfirmInfo(BookkeepDepartmentConfirm confirm);
}
