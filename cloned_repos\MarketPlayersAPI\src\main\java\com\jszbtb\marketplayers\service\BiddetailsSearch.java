package com.jszbtb.marketplayers.service;
import com.rabbitmq.client.Channel;
import org.springframework.amqp.core.Message;
import org.springframework.messaging.handler.annotation.Headers;

import java.util.List;
import java.util.Map;

public interface BiddetailsSearch {

     List<Object> select(String text, String startTime, String endTime, int pageSize, int pageIndex, String types);

     void insert(String str, Channel channel, Message message,@Headers Map<String,Object> map);

     void  scopeSend(Map<String,String> map);

     Object scroll(String text, String startTime, String endTime, int pageSize, int pageIndex, String types,String scrollId);

}
