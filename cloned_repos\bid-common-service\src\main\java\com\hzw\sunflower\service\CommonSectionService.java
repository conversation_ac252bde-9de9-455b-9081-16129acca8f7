package com.hzw.sunflower.service;


import com.hzw.sunflower.dto.ProjectShareInfoDto;
import com.hzw.sunflower.entity.ProjectBidSection;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

public interface CommonSectionService {

    /**
     * 统一修改标段状态（标段子表信息）
     * @param sectionIds 1,2,3,4,5
     * @param status
     * @return
     */
    Boolean updateSectionStatus(String sectionIds,String status);


    Boolean updateSectionCanSearchByIds(List<Long> ids,Integer status);


    /**
     * 统一修改标段状态（标段子表信息）
     * @param status
     * @return
     */
    Boolean updateSectionStatus(List<Long> ids,Integer status);


    /**
     * 统一修改标段状态（标段子表信息）
     * @return
     */
    Boolean updateSectionStatus(List<ProjectBidSection> sections);


    /**
     * 统一修改标段状态（标段子表信息）
     * @return
     */
    Boolean updateSectionStatus(ProjectBidSection section);


    /**
     * 统一修改标段状态（标段子表信息）
     * @param type = GD(归档)  addSection(添加标段信息)
     * @return
     */
    Boolean updateSectionStatus(List<ProjectBidSection> sections,String type);



    /**
     * 项目包件新建修改-项目冗余字段处理
     *
     * @param projectId 项目ID
     * @return 是否成功
     */
    Boolean saveOrUpdateProjectPackageFieldDealWith(Long projectId);

    /**
     * 修改委托项目名称
     * @param projectId
     * @param projectName
     * @return
     */
    Boolean updateProjectName(Long projectId, String projectName);

    /**
     * 修改采购项目名称
     * @param projectId
     * @param purchaseName
     * @return
     */
    Boolean updatePurchaseName(Long projectId, String purchaseName);

    /**
     * 修改标段文件发售截至时间
     * @param sectionId
     * @param saleEndTime
     * @param bidRound
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    Boolean updateSaleEndTime(Long sectionId, String saleEndTime, Integer bidRound);

    /**
     * 修改标段递交截至时间
     * @param sectionId
     * @param submitEndTime
     * @param bidRound
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    Boolean updateSubmitEndTime(Long sectionId, String submitEndTime, Integer bidRound);

    /**
     * 查询ncc中的项目编号和项目的所在部门
     * @param sectionId
     * @return
     */
    ProjectShareInfoDto getProjectShareToNccInfoBySectionId(Long sectionId);

    /**
     * 修改评标次数和状态
     * @param projectBidSection
     * @return
     */
    Boolean updateSectionEvaluation(ProjectBidSection projectBidSection);
}
