package com.jszbtb.mobile.plugins.network.flutter_network_plugin;

import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.Query;
import androidx.room.Update;

import java.util.List;

@Dao
public abstract class CacheDao {

    @Insert
    public abstract void insert(CacheEntity... data);

    @Update
    public abstract void update(CacheEntity... data);

    @Delete
    public abstract void delete(CacheEntity... data);

    @Query("select * from caches")
    abstract List<CacheEntity> getAll();

    @Query("select * from caches where CacheName = :cacheName")
    abstract List<CacheEntity> getCacheByName(String cacheName);
}
