<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="viewport" content="width=device-width" />
    <meta name="server-version" th:content="${serverVersion}"/>
    <!--
    *
    * (c) Copyright Ascensio System SIA 2024
    *
    * Licensed under the Apache License, Version 2.0 (the "License");
    * you may not use this file except in compliance with the License.
    * You may obtain a copy of the License at
    *
    *     http://www.apache.org/licenses/LICENSE-2.0
    *
    * Unless required by applicable law or agreed to in writing, software
    * distributed under the License is distributed on an "AS IS" BASIS,
    * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    * See the License for the specific language governing permissions and
    * limitations under the License.
    *
    -->
    <title>ONLYOFFICE</title>
    <link rel="icon" type="image/ico" href="/favicon.ico"/>
    <link rel="stylesheet" type="text/css" href="css/css.css" />
    <link rel="stylesheet" type="text/css" href="css/stylesheet.css" />
    <link rel="stylesheet" type="text/css" href="css/jquery-ui.css" />
    <link rel="stylesheet" type="text/css"  href="css/media.css">
</head>
<body>
    <header>
        <div class="center">
            <a href="">
                <img src ="css/img/logo.svg" alt="ONLYOFFICE" />
            </a>
        </div>
    </header>

    <div class="center main">
        <table class="table-main">
            <tbody>
            <tr>
                <td class="left-panel section">
                    <div class="help-block">
                        <span>Create new</span>
                        <div class="clearFix">
                            <div class="create-panel clearFix">
                                <ul class="try-editor-list clearFix">
                                    <li>
                                        <a class="try-editor word" data-type="docx">Document</a>
                                    </li>
                                    <li>
                                        <a class="try-editor cell" data-type="xlsx">Spreadsheet</a>
                                    </li>
                                    <li>
                                        <a class="try-editor slide" data-type="pptx">Presentation</a>
                                    </li>
                                    <li>
                                        <a class="try-editor form" data-type="docxf">PDF form</a>
                                    </li>
                                </ul>
                                <label class="side-option">
                                    <input id="createSample" class="checkbox" type="checkbox" />With sample content
                                </label>
                            </div>

                            <form method="POST" action="/upload" enctype="multipart/form-data" class="upload-panel clearFix">
                                <a class="file-upload">Upload file
                                    <input type="file" id="fileupload" name="file" />
                                </a>
                            </form>

                            <table class="user-block-table" cellspacing="0" cellpadding="0">
                                <tbody>
                                    <tr>
                                        <td valign="middle">
                                            <span class="select-user">Username</span>
                                            <img id="info" class="info" src="css/img/info.svg"/>
                                            <select class="select-user" id="user">
                                                <option th:each="user : ${users}"
                                                        th:value="${user.id}"
                                                        th:text="${user.name}"/>
                                            </select>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td valign="middle">
                                            <span class="select-user">Language</span>
                                            <img class="info info-tooltip" data-id="language"
                                                 data-tooltip="Choose the language for ONLYOFFICE editors interface"
                                                 src="css/img/info.svg" />
                                            <select class="select-user" id="language">
                                                <option th:each="language: ${languages}"
                                                        th:value="${language.key}"
                                                        th:text="${language.value}"/>
                                            </select>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td valign="middle">
                                            <label class="side-option">
                                                <input id="directUrl" type="checkbox" class="checkbox" />Try opening on client
                                                <img id="directUrlInfo" class="info info-tooltip" data-id="directUrlInfo" data-tooltip="Some files can be opened in the user's browser without connecting to the document server." src="css/img/info.svg" />
                                            </label>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </td>
                <td class="section">
                    <div class="main-panel">
                        <div id="portal-info" th:attr="tooltip=${tooltip}" th:style="${not #lists.isEmpty(files)} ? 'display: none' : 'display: table-cell' ">
                            <span class="portal-name">ONLYOFFICE Document Editors – Welcome!</span>
                            <span class="portal-descr">
                                    Get started with a demo-sample of ONLYOFFICE Document Editors, the first html5-based editors.
                                    <br /> You may upload your own documents for testing using the "<b>Upload file</b>" button and <b>selecting</b> the necessary files on your PC.
                                </span>
                                <span class="portal-descr">Please do NOT use this integration example on your own server without proper code modifications, it is intended for testing purposes only. In case you enabled this test example, disable it before going for production.</span>
                            <span class="portal-descr">You can open the same document using different users in different Web browser sessions, so you can check out multi-user editing functions.</span>
                        </div>
                        <th:block th:if="${not #lists.isEmpty(files)}">
                            <div class="stored-list">
                                <span class="header-list">Your documents</span>
                                <table class="tableHeader" cellspacing="0" cellpadding="0" width="100%">
                                    <thead>
                                    <tr>
                                        <td class="tableHeaderCell tableHeaderCellFileName">Filename</td>
                                        <td class="tableHeaderCell tableHeaderCellEditors contentCells-shift">Editors</td>
                                        <td class="tableHeaderCell tableHeaderCellViewers">Viewers</td>
                                        <td class="tableHeaderCell tableHeaderCellDownload">Download</td>
                                        <td class="tableHeaderCell tableHeaderCellRemove">Remove</td>
                                    </tr>
                                    </thead>
                                </table>
                                <div class="scroll-table-body">
                                    <table cellspacing="0" cellpadding="0" width="100%">
                                        <tbody>
                                        <tr th:each="file,iState : ${files}" class="tableRow" th:title="${files[iState.index].getName() + versions[iState.index]}">
                                            <td class="contentCells">
                                                <a class="stored-edit" th:classappend="${docTypes[iState.index]}"
                                                   th:with="directUrl=${directUrl}"
                                                   th:href="@{/editor(fileName=${files[iState.index].getName()}, __(${#strings.isEmpty(directUrl)} ? '' : ('directUrl=' + ${directUrl}))__)}" target="_blank">
                                                <span th:text="${files[iState.index].getName()}"></span>
                                                </a>
                                            </td>
                                            <th:block th:if="${filesEditable[iState.index]}">
                                                <td class="contentCells contentCells-icon">
                                                    <a th:with="directUrl=${directUrl}"
                                                       th:href="@{/editor(fileName=${files[iState.index].getName()},type='desktop',action='edit', __(${#strings.isEmpty(directUrl)} ? '' : ('directUrl=' + ${directUrl}))__)}" target="_blank">
                                                    <img src="css/img/desktop.svg" alt="Open in editor for full size screens" title="Open in editor for full size screens"/>
                                                    </a>
                                                </td>
                                                <td class="contentCells contentCells-icon">
                                                    <a th:with="directUrl=${directUrl}"
                                                       th:href="@{/editor(fileName=${files[iState.index].getName()},type='mobile',action='edit', __(${#strings.isEmpty(directUrl)} ? '' : ('directUrl=' + ${directUrl}))__)}" target="_blank">
                                                    <img src="css/img/mobile.svg" alt="Open in editor for mobile devices" title="Open in editor for mobile devices"/>
                                                    </a>
                                                </td>
                                                <td class="contentCells contentCells-icon">
                                                    <a th:with="directUrl=${directUrl}"
                                                       th:href="@{/editor(fileName=${files[iState.index].getName()},type='desktop',action='comment', __(${#strings.isEmpty(directUrl)} ? '' : ('directUrl=' + ${directUrl}))__)}" target="_blank">
                                                        <img src="css/img/comment.svg" alt="Open in editor for comment" title="Open in editor for comment"/>
                                                    </a>
                                                </td>
                                                    <div th:if="${docTypes[iState.index]} eq 'word'">
                                                        <td class="contentCells contentCells-icon">
                                                            <a th:with="directUrl=${directUrl}"
                                                               th:href="@{/editor(fileName=${files[iState.index].getName()},type='desktop',action='review', __(${#strings.isEmpty(directUrl)} ? '' : ('directUrl=' + ${directUrl}))__)}" target="_blank">
                                                            <img src="css/img/review.svg" alt="Open in editor for review" title="Open in editor for review"/>
                                                            </a>
                                                        </td>
                                                    </div>
                                                    <div th:if="${docTypes[iState.index]} eq 'cell'">
                                                        <td class="contentCells contentCells-icon">
                                                            <a th:with="directUrl=${directUrl}"
                                                               th:href="@{/editor(fileName=${files[iState.index].getName()},type='desktop',action='filter', __(${#strings.isEmpty(directUrl)} ? '' : ('directUrl=' + ${directUrl}))__)}" target="_blank">
                                                            <img src="css/img/filter.svg" alt="Open in editor without access to change the filter" title="Open in editor without access to change the filter" />
                                                            </a>
                                                        </td>
                                                    </div>
                                                    <div th:if="${docTypes[iState.index]} eq 'word'">
                                                        <td class="contentCells contentCells-icon">
                                                            <a th:with="directUrl=${directUrl}"
                                                               th:href="@{/editor(fileName=${files[iState.index].getName()},type='desktop',action='blockcontent', __(${#strings.isEmpty(directUrl)} ? '' : ('directUrl=' + ${directUrl}))__)}" target="_blank">
                                                            <img src="css/img/block-content.svg" alt="Open in editor without content control modification" title="Open in editor without content control modification"/>
                                                            </a>
                                                        </td>
                                                    </div>
                                                    <div th:if="not (${docTypes[iState.index]}  eq 'word')">
                                                        <td class="contentCells contentCells-icon"></td>
                                                    </div>
                                                    <div th:if="${docTypes[iState.index]} eq 'slide'">
                                                        <td class="contentCells contentCells-icon "></td>
                                                    </div>
                                                    <div th:if="${isFillFormDoc[iState.index]}">
                                                        <td class="contentCells contentCells-shift contentCells-icon firstContentCellShift">
                                                            <a th:with="directUrl=${directUrl}"
                                                               th:href="@{/editor(fileName=${files[iState.index].getName()},type='desktop',action='fillForms', __(${#strings.isEmpty(directUrl)} ? '' : ('directUrl=' + ${directUrl}))__)}" target="_blank">
                                                                <img src="css/img/fill-forms.svg" alt="Open in editor for filling in forms" title="Open in editor for filling in forms"/>
                                                            </a>
                                                        </td>
                                                    </div>
                                                    <div th:if="${not isFillFormDoc[iState.index]}">
                                                        <td class="contentCells contentCells-shift contentCells-icon firstContentCellShift"></td>
                                                    </div>
                                            </th:block>
                                            <th:block th:if="${isFillFormDoc[iState.index] and not filesEditable[iState.index]}">
                                                <td class="contentCells contentCells-icon "></td>
                                                <td class="contentCells contentCells-icon">
                                                    <a th:with="directUrl=${directUrl}"
                                                       th:href="@{/editor(fileName=${files[iState.index].getName()},type='mobile',action='fillForms', __(${#strings.isEmpty(directUrl)} ? '' : ('directUrl=' + ${directUrl}))__)}" target="_blank">
                                                        <img src="css/img/mobile-fill-forms.svg" alt="Open in editor for filling in forms for mobile devices" title="Open in editor for filling in forms for mobile devices" />
                                                    </a>
                                                </td>
                                                <td class="contentCells contentCells-icon "></td>
                                                <td class="contentCells contentCells-icon "></td>
                                                <td class="contentCells contentCells-icon "></td>
                                                <td class="contentCells contentCells-shift contentCells-icon firstContentCellShift">
                                                    <a th:with="directUrl=${directUrl}"
                                                       th:href="@{/editor(fileName=${files[iState.index].getName()},type='desktop',action='fillForms', __(${#strings.isEmpty(directUrl)} ? '' : ('directUrl=' + ${directUrl}))__)}" target="_blank">
                                                        <img src="css/img/fill-forms.svg" alt="Open in editor for filling in forms" title="Open in editor for filling in forms"/>
                                                    </a>
                                                </td>
                                            </th:block>
                                            <th:block th:if="${not filesEditable[iState.index]}">
                                                <td class="contentCells contentCells-shift contentCells-icon contentCellsEmpty" colspan="6"></td>
                                            </th:block>
                                            <td class="contentCells contentCells-icon firstContentCellViewers">
                                                <a th:with="directUrl=${directUrl}"
                                                   th:href="@{/editor(fileName=${files[iState.index].getName()},type='desktop',action='view', __(${#strings.isEmpty(directUrl)} ? '' : ('directUrl=' + ${directUrl}))__)}" target="_blank">
                                                <img src="css/img/desktop.svg" alt="Open in viewer for full size screens" title="Open in viewer for full size screens"/>
                                                </a>
                                            </td>
                                            <td class="contentCells contentCells-icon">
                                                <a th:with="directUrl=${directUrl}"
                                                   th:href="@{/editor(fileName=${files[iState.index].getName()},type='mobile',action='view', __(${#strings.isEmpty(directUrl)} ? '' : ('directUrl=' + ${directUrl}))__)}" target="_blank">
                                                <img src="css/img/mobile.svg" alt="Open in viewer for mobile devices" title="Open in viewer for mobile devices"/>
                                                </a>
                                            </td>
                                            <td class="contentCells contentCells-icon contentCells-shift">
                                                <a th:with="directUrl=${directUrl}"
                                                   th:href="@{/editor(fileName=${files[iState.index].getName()},type='embedded',action='embedded', __(${#strings.isEmpty(directUrl)} ? '' : ('directUrl=' + ${directUrl}))__)}" target="_blank">
                                                <img src="css/img/embeded.svg" alt="Open in embedded mode" title="Open in embedded mode"/>
                                                </a>
                                            </td>
                                            <td class="contentCells contentCells-icon contentCells-shift downloadContentCellShift">
                                                <a th:href="@{/download(fileName=${files[iState.index].getName()})}" target="_blank">
                                                <img class="icon-download" src="css/img/download.svg" alt="Download" title="Download" />
                                                </a>
                                            </td>
                                            <td class="contentCells contentCells-icon contentCells-shift">
                                                <a class="delete-file" th:attr="data-filename=${files[iState.index].getName()}">
                                                    <img class="icon-delete" src="css/img/delete.svg" alt="Delete" title="Delete" />
                                                </a>
                                            </td>
                                        </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </th:block>
                    </div>
                </td>
            </tr>
            </tbody>
        </table>
    </div>

    <div id="mainProgress">
        <div id="uploadSteps">
            <span id="uploadFileName" class="uploadFileName"></span>
            <div class="describeUpload">After these steps are completed, you can work with your document.</div>
            <span id="step1" class="step">1. Loading the file.</span>
            <span class="step-descr">The loading speed depends on file size and additional elements it contains.</span>
            <br />
            <span id="step2" class="step">2. Conversion.</span>
            <span class="step-descr">The file is converted to OOXML so that you can edit it.</span>
            <br />
            <div id="blockPassword">
                <span class="descrFilePass">The file is password protected.</span>
                <br />
                <div>
                    <input id="filePass" type="password"/>
                    <div id="enterPass" class="button orange">Enter</div>
                    <div id="skipPass" class="button gray">Skip</div>
                </div>
                <span class="errorPass"></span>
                <br />
            </div>
            <span id="step3" class="step">3. Loading editor scripts.</span>
            <span class="step-descr">They are loaded only once, they will be cached on your computer.</span>
            <input type="hidden" name="hiddenFileName" id="hiddenFileName" />
            <br />
            <span class="progress-descr">Note the speed of all operations depends on your connection quality and server location.</span>
            <br />
            <div class="error-message">
                <b>Upload error: </b><span></span>
                <br />
                Please select another file and try again.
            </div>
        </div>
        <iframe id="embeddedView" src="" height="345px" width="432px" frameborder="0" scrolling="no" allowtransparency></iframe>
        <br />
        <div class="buttonsMobile">
            <div id="beginEdit" class="button orange disable">Edit</div>
            <div id="beginView" class="button gray disable">View</div>
            <div id="beginEmbedded" class="button gray disable">Embedded view</div>
            <div id="cancelEdit" class="button gray">Cancel</div>
        </div>
    </div>

    <span id="loadScripts" th:attr="data-docs=${datadocs}"></span>

    <footer>
        <div class="center">
            <table>
                <tbody>
                <tr>
                    <td>
                        <a href="http://api.onlyoffice.com/editors/howitworks" target="_blank">API Documentation</a>
                    </td>
                    <td>
                        <a href="mailto:<EMAIL>">Submit your request</a>
                    </td>
                    <td class="copy">
                        &copy; Ascensio Systems SIA 2024. All rights reserved.
                    </td>
                </tr>
                </tbody>
            </table>
        </div>
    </footer>

    <script type="text/javascript" src="scripts/jquery-3.6.4.min.js"></script>
    <script type="text/javascript" src="scripts/jquery-migrate-3.4.1.min.js"></script>
    <script type="text/javascript" src="scripts/jquery-ui.js"></script>
    <script type="text/javascript" src="scripts/jquery.blockUI.js"></script>
    <script type="text/javascript" src="scripts/jquery.iframe-transport.js"></script>
    <script type="text/javascript" src="scripts/jquery.fileupload.js"></script>
    <script type="text/javascript" src="scripts/jquery.dropdownToggle.js"></script>
    <script type="text/javascript" src="scripts/jscript.js"></script>
    <script type="text/javascript" src="scripts/converter.js"></script>
    <script type="text/javascript">
        document.addEventListener('DOMContentLoaded', function() {
            var lang = document.cookie
                    .split('; ')
                    .find((row) => row.startsWith('ulang='))
                    ?.split('=')[1];

            var languages = Array.from(document.getElementById("language").options).map(e => e.value)

            if (!languages.includes(lang)) {
                lang = "en";
            }

            document.getElementById("language").value=lang;
        });
    </script>
</body>
</html>
