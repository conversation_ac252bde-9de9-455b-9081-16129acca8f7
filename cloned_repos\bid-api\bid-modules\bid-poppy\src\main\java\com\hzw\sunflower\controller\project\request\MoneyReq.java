package com.hzw.sunflower.controller.project.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date ：Created in 2021/7/29 15:54
 * @description：金额换算
 * @modified By：`
 * @version: 1.0
 */
@Data
public class MoneyReq {


    @ApiModelProperty(value = "1.国家纪委1980号文 2.苏招协2022002号文 3.固定比例")
    private Integer agencyFeeOption;

    @ApiModelProperty(value = "1.工程 2.服务 3.货物")
    private Integer type;

    @ApiModelProperty(value = "中标金额")
    private BigDecimal bidAmount;

    @ApiModelProperty(value = "百分比")
    private String chargeContent;
}
