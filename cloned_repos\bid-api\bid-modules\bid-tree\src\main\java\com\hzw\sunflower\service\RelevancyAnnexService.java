package com.hzw.sunflower.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hzw.sunflower.controller.request.RelevancyAnnexREQ;
import com.hzw.sunflower.dto.AnnexDTO;
import com.hzw.sunflower.dto.RelevancyAnnexDTO;
import com.hzw.sunflower.entity.RelevancyAnnex;
import com.hzw.sunflower.entity.condition.RelevancyAnnexCondition;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * TRelevancyAnnexService接口
 */
public interface RelevancyAnnexService extends IService<RelevancyAnnex> {

    /**
     * 根据招标公告ID查询相关联的附件信息
     * @param noticeId 公告ID
     * @return 招标公告关联附件DTO的集合
     */
    List<AnnexDTO> findNoticeAnnexsByNoticeIdOne(Long noticeId);

    /**
     * 根据招标公告ID查询相关联的附件信息+邮件附件
     * @param noticeId 公告ID
     * @return 招标公告关联附件DTO的集合
     */
    List<AnnexDTO> findNoticeAnnexsByNoticeId(Long noticeId);
    /**
     * 根据条件分页查询列表
     *
     * @param condition 查询条件
     * @return 分页信息
     */
    IPage<RelevancyAnnex> findInfoByCondition(RelevancyAnnexCondition condition);

    /**
     * 根据主键ID查询信息
     *
     * @param id 主键ID
     * @return TRelevancyAnnex信息
     */
    RelevancyAnnex getInfoById(Long id);

    /**
     * 新增
     *
     * @param relevancyAnnex
     * @return 是否成功
     */
    Boolean addInfo(RelevancyAnnex relevancyAnnex);

    /**
     * 修改单位公司 信息
     *
     * @param relevancyAnnex
     * @return 是否成功
     */
    Boolean updateInfo(RelevancyAnnex relevancyAnnex);

    /**
     * 根据主键ID删除
     *
     * @param id 主键ID
     * @return 是否成功
     */
    Boolean deleteById(Long id);

    /**
     * 根据主键ID列表批量删除
     *
     * @param idList 主键ID列表
     * @return 是否成功
     */
    Boolean deleteByIds(List<Long> idList);

    /**
     * 保存/编辑 附件
     *
     * @param relevancyAnnexREQ
     * @return
     */
    boolean saveRelevancyAnnexInfo(RelevancyAnnexREQ relevancyAnnexREQ);

    /**
     * 根据招标文件ID查询附件信息
     *
     * @param docId
     * @return
     */
    List<RelevancyAnnexDTO> getInfoByDocId(Long docId);
}
