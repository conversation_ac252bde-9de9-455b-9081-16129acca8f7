package com.hzw.sunflower.controller;

import com.hzw.sunflower.common.BaseController;
import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.controller.request.SealBidWinREQ;
import com.hzw.sunflower.controller.response.SealBidWinVO;
import com.hzw.sunflower.service.SealBidWinService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
* SealBidWinController
*/
@Api(tags = "SealBidWin服务")
@RestController
@RequestMapping("/sealBidWin")
public class SealBidWinController extends BaseController {
    @Autowired
    private SealBidWinService sealBidWinService;

    @ApiOperation(value = "查询中标通知书盖章文件")
    @ApiImplicitParam(name = "req", value = "用户表 查询条件", required = true, dataType = "SealBidWinREQ", paramType = "body")
    @PostMapping("/listSealFiles")
    public Result<List<SealBidWinVO>> listSealFiles(@RequestBody SealBidWinREQ req) {
            return Result.ok(sealBidWinService.listSealFiles(req));
    }

}