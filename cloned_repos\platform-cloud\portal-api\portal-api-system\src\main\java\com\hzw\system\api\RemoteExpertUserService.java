package com.hzw.system.api;

import com.hzw.system.api.model.BusinessUser;

/**
 * <AUTHOR>
 */
public interface RemoteExpertUserService {

    /**
     * 修改用户信息
     * @param user
     * @return
     */
    int updateById(BusinessUser user);

    /**
     * 查询用户信息
     * @param userId
     * @return
     */
    BusinessUser selectById(String userId);

    /**
     * 修改用户手机号
     * @param userId
     * @param phone
     * @return
     */
   int updatePhoneById(String userId,String phone);
}
