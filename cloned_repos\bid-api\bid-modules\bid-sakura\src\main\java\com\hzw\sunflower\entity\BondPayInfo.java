package com.hzw.sunflower.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;

import com.hzw.sunflower.common.BaseBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 保证金银行支付信息
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-26
 */
@Data
@TableName("t_bond_pay_info")
@ApiModel(value = "BondPayInfo对象", description = "保证金银行支付信息")
public class BondPayInfo extends BaseBean implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("退还详情表id")
    @TableField("refund_detail_id")
    private Long refundDetailId;

    @ApiModelProperty("支付结果 1.成功 2.失败")
    @TableField("status")
    private Integer status;

    @ApiModelProperty("银行类型 1.中国银行 2.南京银行 3.民生银行")
    @TableField("type")
    private Integer type;

    @ApiModelProperty("支付失败返回")
    @TableField("error_msg")
    private String errorMsg;

    @ApiModelProperty("成功返回json")
    @TableField("success_data")
    private String successData;

    @ApiModelProperty("支付唯一标识")
    @TableField("req_no")
    private String reqNo;

    @ApiModelProperty("用途类型：1：保证金   2：代理服务费")
    @TableField("req_type")
    private Integer reqType;

}
