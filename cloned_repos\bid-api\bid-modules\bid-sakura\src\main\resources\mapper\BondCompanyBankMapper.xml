<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzw.sunflower.dao.BondCompanyBankMapper">

    <select id="getPayFileCompanyInfo" resultType="com.hzw.sunflower.controller.response.BondCompanyBankVo">
        SELECT
            DISTINCT
            bcb.*,brd.status as refundStatus
        FROM
            t_bond_company_bank bcb
                LEFT JOIN t_bond_refund_details brd ON bcb.company_account = brd.refund_number
        WHERE
            bcb.IS_DELETE = 0
          AND brd.is_delete = 0
          AND bcb.company_id = #{companyId}
          and brd.refund_id = #{refundId}
          <!-- and brd.`status` = 4 -->
    </select>

</mapper>
