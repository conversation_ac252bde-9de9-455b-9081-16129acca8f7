package com.hzw.ssm.sys.sms.mwutil;

import java.util.concurrent.TimeUnit;

import org.apache.http.client.HttpClient;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.config.Registry;
import org.apache.http.config.RegistryBuilder;
import org.apache.http.conn.socket.ConnectionSocketFactory;
import org.apache.http.conn.socket.LayeredConnectionSocketFactory;
import org.apache.http.conn.socket.PlainConnectionSocketFactory;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.params.CoreConnectionPNames;

public class HttpPollManager {
	//连接配置对象
	private HttpClientBuilder builder = null;
	//连接池对象
	private static HttpPollManager instance;
	//配置网络环境对象
	private static RequestConfig config;
	
	public static HttpPollManager getInstance() {

		if (instance == null) {
			instance=new HttpPollManager();
		}
		return instance;
	}
	/**
	 * @describe 初始化配置连接池对象
	 * @param null
	 * @return void
	 * */
	private HttpPollManager() {
		ConnectionSocketFactory plainsf = PlainConnectionSocketFactory
				.getSocketFactory();
		LayeredConnectionSocketFactory sslsf = SSLConnectionSocketFactory
				.getSocketFactory();
		Registry<ConnectionSocketFactory> registry = RegistryBuilder
				.<ConnectionSocketFactory> create().register("http", plainsf)
				.register("https", sslsf).build();
		PoolingHttpClientConnectionManager cm = new PoolingHttpClientConnectionManager(
				registry);
		//设置最大连接数
		cm.setMaxTotal(ConfigManager.maxTotal);
		//设置每个ip最大连接数
		cm.setDefaultMaxPerRoute(ConfigManager.maxPerRoute);
		builder = HttpClients.custom();
		builder.setConnectionManager(cm);
		//25秒没用连接会自动清理
		builder.evictIdleConnections(ConfigManager.clearTime, TimeUnit.SECONDS);
		RequestConfig.Builder configBuilder = RequestConfig.custom();
		//从连接池获取连接超时时间
		configBuilder.setConnectionRequestTimeout(ConfigManager.connectionRequestTimeout);
		//连接超时时间
		configBuilder.setConnectTimeout(ConfigManager.connectTimeout);
		//响应超时时间
		configBuilder.setSocketTimeout(ConfigManager.socketTimeout);
		config = configBuilder.build();
		builder.setDefaultRequestConfig(config);
	}
	/**
	 * @describe 从连接池中获取一个连接对象
	 * @param null
	 * @return HttpClient
	 * */
    public HttpClient buildHttpClient() {
    	System.out.println(Thread.currentThread().getName());
    	
        return builder.build();
    }
}
