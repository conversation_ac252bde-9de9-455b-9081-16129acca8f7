package com.hzw.ssm.date.util;


public class ZJKCalendarMessage {
	
	//COUNT*********************************
	public static final Integer MAX_CREATE_FORMER_YEAR=5;//允许以当年为准,往前创建的最大日历年(包含当年)
	public static final Integer MAX_CREATE_FUTURE_YEAR=5;//允许以当年为准,往后创建的最大日历年(包含当年)
	
	public static final Integer MAX_CREATE_CALENDAR_HOUR=23;//允许当天创建日历的最迟时间(时)
	public static final Integer MAX_CREATE_CALENDAR_MINUTE=30;//允许当天创建日历的最迟时间(分)
	
	public static final Integer MAX_DELETE_FORMER_CALENDAR=3;//允许删除的过去日历范围(包含当年)
	public static final Integer MAX_DELETE_FUTURE_CALENDAR=3;//允许删除的未来日历范围(包含当年)
	
	public static final Long MILLISECOND_FOR_DAT=86400000L;//表示一天的毫秒数
	
	
	
	
	//INFO**********************************
	public static final String DAY_OF_WEEKEED="周末";//周末
	public static final String DAY_OF_WORKDAY="工作日";//工作日
	public static final String DAT_OF_DAYON="法定调休日";//法定调休日
	public static final String DAT_OF_DAYOFF="法定调班日";//法定调班日
	public static final String DAY_OF_NATIONAL="国庆节";//国庆节
	
	
	
	
	//ERROR**********************************
	public static final String MAX_CREATE_FORMER_ERROR="超过可创建的过去日历范围";//超出往前创建的最大日历年的错误信息
	public static final String MAX_CREATE_FUTURE_ERROR="超过可创建的未来日历范围";//超出往后创建的最大日历年的错误信息
	public static final String REPEAT_YEAR_ERROR="该年份日历已经存在!";//重复创建的年份日历的错误信息
	public static final String NULL_PARAMETER_ERROR="参数不可以为空!";//空参数的错误信息
	public static final String CREATE_CALENDAR_OVERTIME_ERROR="超过当天可创建日历的时间[00:00-"+MAX_CREATE_CALENDAR_HOUR+":"+MAX_CREATE_CALENDAR_MINUTE+"]!";//超出当天可创建日历的时间
	
}
