package com.hzw.ssm.expert.entity;

/**
 * 外呼结果信息实体类
 * 
 * <AUTHOR> @date 2014-10-17
 */
public class TSRMSG {
	private static final long serialVersionUID = 1L;
	
	/**
	 * 接听时间
	 */
	private String answer_time;
	
	/**
	 * 被叫
	 */
	private String call_num;
	
	
	/**
	 * 客户数据
	 */
	private String client_data;
	
	/**
	 * 创建时间
	 */
	private String create_time;
	
	/**
	 * 外呼结果
	 */
	private String call_result;
	
	/**
	 * 项目ID
	 */
	private String project_id;
	
	/**
	 * 挂机时间
	 */
	private String end_time;
	
	/**
	 * 主叫
	 */
	private String org_num;
	
	/**
	 * 奇呼时间
	 */
	private String call_time;
	
	/**
	 * 用户按键
	 */
	private String oper;
	
	/**
	 * 响应消息
	 */
	private String err_msg;
	
	/**
	 * 响应代码
	 */
	private Integer err_code;
	
	/**
	 * 日志ID
	 */
	private String logId;
	
	
	/**
	 * begin_time
	 */
	private String begin_time;
	
	/**
	 * 无意义参数，不应用于本地业务中
	 */
	private String tag;
	
	/**
	 * 无意义参数，不应用与业务中
	 */
	private String tag_id;

	
	public String getTag_id() {
		return tag_id;
	}

	public void setTag_id(String tag_id) {
		this.tag_id = tag_id;
	}

	public String getAnswer_time() {
		return answer_time;
	}

	public void setAnswer_time(String answer_time) {
		this.answer_time = answer_time;
	}

	public String getCall_num() {
		return call_num;
	}

	public void setCall_num(String call_num) {
		this.call_num = call_num;
	}

	public String getClient_data() {
		return client_data;
	}

	public void setClient_data(String client_data) {
		this.client_data = client_data;
	}

	public String getCreate_time() {
		return create_time;
	}

	public void setCreate_time(String create_time) {
		this.create_time = create_time;
	}

	public String getCall_result() {
		return call_result;
	}

	public void setCall_result(String call_result) {
		this.call_result = call_result;
	}

	public String getProject_id() {
		return project_id;
	}

	public void setProject_id(String project_id) {
		this.project_id = project_id;
	}

	public String getEnd_time() {
		return end_time;
	}

	public void setEnd_time(String end_time) {
		this.end_time = end_time;
	}

	public String getOrg_num() {
		return org_num;
	}

	public void setOrg_num(String org_num) {
		this.org_num = org_num;
	}

	public String getCall_time() {
		return call_time;
	}

	public void setCall_time(String call_time) {
		this.call_time = call_time;
	}

	public String getOper() {
		return oper;
	}

	public void setOper(String oper) {
		this.oper = oper;
	}

	public String getErr_msg() {
		return err_msg;
	}

	public void setErr_msg(String err_msg) {
		this.err_msg = err_msg;
	}

	public Integer getErr_code() {
		return err_code;
	}

	public void setErr_code(Integer err_code) {
		this.err_code = err_code;
	}

	public String getLogId() {
		return logId;
	}

	public void setLogId(String logId) {
		this.logId = logId;
	}

	public String getBegin_time() {
		return begin_time;
	}

	public void setBegin_time(String begin_time) {
		this.begin_time = begin_time;
	}

	public String getTag() {
		return tag;
	}

	public void setTag(String tag) {
		this.tag = tag;
	}

	public static long getSerialversionuid() {
		return serialVersionUID;
	}
	
}
