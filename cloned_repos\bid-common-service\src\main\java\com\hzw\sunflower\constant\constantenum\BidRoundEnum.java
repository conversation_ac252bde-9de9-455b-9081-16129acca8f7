package com.hzw.sunflower.constant.constantenum;

/**
 * 招标阶段
 *
 * <AUTHOR>
 */
public enum BidRoundEnum {
    ZGYS(1, "第一轮"),
    HS(2, "第二轮");
    private Integer type;
    private String desc;

    BidRoundEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
