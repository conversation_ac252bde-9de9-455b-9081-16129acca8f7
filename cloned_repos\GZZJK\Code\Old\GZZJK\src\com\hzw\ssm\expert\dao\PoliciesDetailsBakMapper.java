package com.hzw.ssm.expert.dao;

import com.hzw.ssm.expert.entity.PoliciesDetailsBak;

import java.util.List;

public interface PoliciesDetailsBakMapper {


   Integer addPoliciesDetailsBak(PoliciesDetailsBak policiesDetailsBak);
   List<PoliciesDetailsBak> queryPageFindPoliciesDetailsBakByCondition(PoliciesDetailsBak policiesDetailsBak);
   List<PoliciesDetailsBak> findPoliciesDetailsBakByCondition(PoliciesDetailsBak policiesDetailsBak);
  PoliciesDetailsBak findPoliciesDetailsBakById(String id);


}
