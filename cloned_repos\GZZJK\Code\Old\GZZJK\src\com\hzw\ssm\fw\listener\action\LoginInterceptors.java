package com.hzw.ssm.fw.listener.action;

import java.util.Date;
import java.util.Enumeration;
import java.util.Iterator;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;

import javax.servlet.ServletRequest;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.apache.struts2.ServletActionContext;
import org.springframework.beans.factory.annotation.Autowired;

import com.hzw.ssm.fw.listener.service.InterceptorService;
import com.hzw.ssm.fw.util.CommUtil;
import com.hzw.ssm.fw.util.StrUtil;
import com.hzw.ssm.sys.system.entity.SystemLogEntity;
import com.hzw.ssm.sys.system.service.SystemLogService;
import com.hzw.ssm.sys.user.entity.UserEntity;
import com.opensymphony.xwork2.ActionContext;
import com.opensymphony.xwork2.ActionInvocation;
import com.opensymphony.xwork2.interceptor.MethodFilterInterceptor;


public class LoginInterceptors extends MethodFilterInterceptor {

	@Autowired
	public InterceptorService ser;
	
	@Autowired
	private SystemLogService sLog;
	
	private SystemLogEntity logEntity = new SystemLogEntity();

	/**
	 * 日志类
	 */
	private static Log log = LogFactory.getLog(LoginInterceptors.class);

	/**
	 * 系统拦截
	 */
	protected String doIntercept(ActionInvocation actionInvocation) throws Exception {
		// 返回值
		String result = null;
		// 获取用户信息
		UserEntity user = (UserEntity) ActionContext.getContext().getSession().get("userInfo");
		// 获取请求的action名称
		String actionName = actionInvocation.getInvocationContext().getName();
		HttpServletRequest request = ServletActionContext.getRequest();
		// 获取访问路径
		String currentURL = request.getRequestURI();
		// 获取根路径
		String contextPath = request.getContextPath();
		// 获取访问地址
		String path = currentURL.replace(contextPath, "");
					
//		if (("access").equals(actionName) || ("cancellation").equals(actionName) || ("enter").equals(actionName) || ("index").equals(actionName)) {
//			// Action 执行返回值
//			result = actionInvocation.invoke();
//		} else {
			if (null == user) {
				result = "login";
			} else {
				//=============系统日志 start=================
				String mothod = ser.getMothod(path);  //菜单名称
				
				logEntity.setLOG_ID(CommUtil.getKey());
				logEntity.setOPA_USER(user.getUser_name());
				logEntity.setOPA_IP(request.getRemoteAddr());
				logEntity.setOPA_FUN(path);
				logEntity.setOPA_METHOD(mothod);
				Date opaDate = new Date();
				logEntity.setCreate_id(user.getUser_id());
				logEntity.setCreate_time(opaDate);
				logEntity.setModify_id(user.getUser_id());
				logEntity.setModify_time(opaDate);
				logEntity.setDelete_flag(0);
				sLog.saveSystemLog(logEntity);
				//=============系统日志 end=================
				
				/*if (!this.remote_login(request, user)) {// 异地登录拦截
					result = "has";
					return result;
				} else if (!this.competence(user, namespace)) {// 权限拦截
					result = "uv";
					return result;
				}
				else */ /*if (!this.injection(user)) {// SQL注入拦截
					result = "warning";
					return result;
				}*/ 
				
				String inj = injectInput(request);
				if(!inj.equals("")){
					result = "warning";
					return result;
				}
				else {
					result = actionInvocation.invoke();
				}
			}
//		}
		return result;
	}

	/**
	 * 异地登录拦截
	 */
	private boolean remote_login(HttpServletRequest request, UserEntity user) {
		boolean result = true;
		// 获取当前登录的sessionId
		String seesionId = request.getSession().getId();
		// 根据用户登录名获取服务端已存在的session
		HttpSession oldSession = (HttpSession) ActionContext.getContext().getApplication().get(user.getLogin_code());
		// 获取已存在的sessionId
		String oldSessionId = oldSession.getId();
		// 判断当前sessionId与服务器sessionId是否匹配
		if (!oldSessionId.equals(seesionId)) {
			result = false;
		}
		return result;
	}

	/**
	 * 权限拦截
	 */
	private boolean competence(UserEntity user, String actionName) {
		boolean result = true;
		if(!("").equals(actionName)){
			int hsaQA = ser.hasQA(user.getLogin_code(), actionName);
			if (0 == hsaQA) {
				result = false;
			}
		}		
		return result;
	}

	/**
	 * SQL注入拦截
	 */
/*	private boolean injection(UserEntity user) throws Exception {
		HttpServletRequest request = ServletActionContext.getRequest();
		boolean result = true;
		try {
			Map map = ActionContext.getContext().getParameters();
			Set entrySet = map.entrySet();
			for (Iterator it = entrySet.iterator(); it.hasNext();) {
				Entry entry = (Entry) it.next();

				Object[] value = (Object[]) entry.getValue();
				String paramName = entry.getKey().toString();

				if (value instanceof String[]) {
					if (0 < value.length) {
						String v = (String) value[0];

						// 包含敏感词
						if (StrUtil.isAttack(v)) {
							log.info("@@@@@@@@@@@@@@@@警告：发现疑似攻击现象！操作用户：" + user.getLogin_code());
							result = false;
							break;
						}
					}
				}
			}
		} catch (Exception e) {
			throw new Exception(e.getMessage());
		}
		return result;
	}*/
	
	
    /**
     * 判断request中是否含有注入攻击字符
     */
    public String injectInput(ServletRequest request) {
         
        Enumeration e = request.getParameterNames();
        String attributeName;
        String attributeValues[];
        String inj = "";
        String nameHasXss="";
         
        while (e.hasMoreElements()) {
            attributeName = (String)e.nextElement();
            
            //对邮箱密码之类的不过滤
            if(attributeName.equals("objUser.tel")||attributeName.equals("confirmPassword")||attributeName.equals("objUser.email")){
              continue;
          	}
            
            nameHasXss = StrUtil.cleanXSS(attributeName);
            if(!"".equals(nameHasXss)){
                return nameHasXss;
            }else{
                attributeValues = request.getParameterValues(attributeName);
                for (int i = 0; i < attributeValues.length; i++) {
                     
                    if(attributeValues[i]==null||attributeValues[i].equals(""))
                                    continue;
                     inj = StrUtil.cleanXSS(attributeValues[i]);
                     if (!inj.equals("")) {
                        return inj;
                     }
                }
            }
        }  
        return inj;
     }
	
	public SystemLogEntity getLogEntity() {
		return logEntity;
	}

	public void setLogEntity(SystemLogEntity logEntity) {
		this.logEntity = logEntity;
	}
}
