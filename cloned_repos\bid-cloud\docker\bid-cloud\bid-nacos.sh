#!/bin/sh
#cp TEMPLATE FILE
docker stop bid-nacos || true
docker rm bid-nacos || true
docker rmi registry.cn-hangzhou.aliyuncs.com/hzw-jtcc/jtcc:bid-nacos-1.0.0 || true
docker pull registry.cn-hangzhou.aliyuncs.com/hzw-jtcc/jtcc:bid-nacos-1.0.0
docker run -itd -p 8848:8848 --net=host  --log-opt max-size=100m --log-opt max-file=3  --name bid-nacos -v /home/<USER>/plan1/bid-cloud/config/nacos:/config -v /data/log/nacos:/root/nacos/logs  registry.cn-hangzhou.aliyuncs.com/hzw-jtcc/jtcc:bid-nacos-1.0.0
