package com.hzw.ssm.report.action;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.PrintWriter;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletResponse;

import com.hzw.ssm.fw.util.Page;
import com.hzw.ssm.fw.util.Tools;
import com.hzw.ssm.report.entity.QueryBean;
import com.hzw.ssm.sys.user.entity.UserEntity;
import net.sf.json.JSONArray;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFFont;
import org.apache.poi.hssf.usermodel.HSSFPrintSetup;
import org.apache.poi.hssf.usermodel.HSSFRichTextString;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.struts2.ServletActionContext;
import org.apache.struts2.convention.annotation.Action;
import org.apache.struts2.convention.annotation.Namespace;
import org.apache.struts2.convention.annotation.ParentPackage;
import org.apache.struts2.convention.annotation.Result;
import org.apache.struts2.convention.annotation.Results;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import com.google.gson.Gson;
import com.hzw.ssm.fw.base.BaseAction;
import com.hzw.ssm.fw.util.DateUtil;
import com.hzw.ssm.fw.util.ExcelUtil;
import com.hzw.ssm.report.entity.ReportEntity;
import com.hzw.ssm.report.service.ReportService;

@Namespace("/report")
@ParentPackage(value = "default")
@Results({ @Result(name = "queryReportDetail", location = "/jsp/report/detailList.jsp"),
		@Result(name = "queryReportSummary", location = "/jsp/report/summaryList.jsp"),
		@Result(name = "queryReportCancellation", location = "/jsp/report/cancellationList.jsp"),
		@Result(name = "queryDeducteScore", location = "/jsp/report/deducteScoreList.jsp"),
		@Result(name = "queryReportCancellationT", location = "/jsp/report/cancellationListT.jsp")

})

public class ReportAction extends BaseAction {

	private static final long serialVersionUID = 1L;
	
	private String tableTopStyle;
	
	//@Value("${FilePath}")
	//private String filePath;
	
	@Value("${ReprotFileName}")
	private String fileName;
	
	@Autowired
	private ReportService reportService;
	
	private ReportEntity reportEntity;

	private List<ReportEntity> reportEntityList;
	SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

	/**
	 * 扣分审核
	 * @return
	 */
	@Action("queryDeducteScore")
	public String queryDeducteScore() throws ParseException {
		this.context();
		if(null == reportEntity) {
			reportEntity= new ReportEntity();
			this.getPage().setCurrentPage(1);
		}
		reportEntityList=new ArrayList<ReportEntity>();
		if (StringUtils.isBlank(reportEntity.getIsApprove())){
			reportEntity.setIsApprove("0");
		}
		String sort="";
		Boolean asc=true;
		//排序
		if ("1".equals(reportEntity.getSortWay())){
			reportEntity.setSortWay("a.sumScore asc");
			sort="sumScore";
			asc=true;
		}else if ("2".equals(reportEntity.getSortWay())){
			reportEntity.setSortWay("a.sumScore desc");
			sort="sumScore";
			asc=false;
		}else if ("3".equals(reportEntity.getSortWay())){
			reportEntity.setSortWay("a.user_name asc");
			sort="expertName";
			asc=true;
		}else if ("4".equals(reportEntity.getSortWay())){
			reportEntity.setSortWay("a.user_name desc");
			sort="expertName";
			asc=false;
		}else if ("5".equals(reportEntity.getSortWay())){
			reportEntity.setSortWay("a.PROJECT_NAME asc");
			sort="projectName";
			asc=true;
		}else if ("6".equals(reportEntity.getSortWay())){
			reportEntity.setSortWay("a.PROJECT_NAME desc");
			sort="projectName";
			asc=false;
		}else if("7".equals(reportEntity.getSortWay())){
			reportEntity.setSortWay("a.appraise_time asc");
			sort="appraiseTime";
			asc=true;
		}else if ("8".equals(reportEntity.getSortWay())){
			reportEntity.setSortWay("a.appraise_time desc");
			sort="appraiseTime";
			asc=false;
		}else{
			reportEntity.setSortWay("a.appraise_time");
			sort="appraiseTime";
			asc=true;
		}
		Page page=this.getPage();
//		reportEntity.setPage(this.getPage());
		//查询条件下的抽取批次号
		List<QueryBean> resultIdList=reportService.queryresultId(reportEntity);
		if (CollectionUtils.isNotEmpty(resultIdList)) {
			for (QueryBean resultIdMap:resultIdList) {
				reportEntity.setExtractResultId(resultIdMap.getExtractResultId());
				if (StringUtils.isNotBlank(resultIdMap.getReviewTimeStr())){
					reportEntity.setReviewTimeStr(resultIdMap.getReviewTimeStr().substring(0,resultIdMap.getReviewTimeStr().length()-2));
				}
				List<ReportEntity> list = reportService.selectReportDetail(reportEntity);
				reportEntityList.addAll(list);
			}
		}
		Tools.sort(reportEntityList,sort,asc);
		page.setTotalResult(reportEntityList.size());
		int i=0;
		if (reportEntityList.size()%this.getPage().getShowCount()>0){
			i=reportEntityList.size()/this.getPage().getShowCount()+1;
		}else{
			i=reportEntityList.size()/this.getPage().getShowCount();
		}
		//下一页开始
		int n=(page.getCurrentPage()-1)*page.getShowCount();
		if (CollectionUtils.isNotEmpty(reportEntityList)) {
			if (page.getCurrentPage() * page.getShowCount() > reportEntityList.size()) {
				reportEntityList = reportEntityList.subList(n, reportEntityList.size());
			} else {
				reportEntityList = reportEntityList.subList(n, page.getCurrentPage() * page.getShowCount());
			}
		}
		this.getPage().setTotalPage(i);
		reportEntity.setPage(this.getPage());
		return "queryDeducteScore";
	}
	/**
	 * 查询扣分原因
	 */
	@Action("queryScoreInfo")
	public String queryScoreInfo(){
		this.context();
		if(null == reportEntity) {
			reportEntity= new ReportEntity();
		}
		if (null !=reportEntity.getAppraiseTime()){
			String formatDate = sdf.format(reportEntity.getAppraiseTime());
			reportEntity.setAppraiseTimeStr(formatDate);
		}
		reportEntityList = reportService.queryScoreInfo(reportEntity);
//		net.sf.json.JSONArray jsonArray = net.sf.json.JSONArray.fromObject(stringList);
		Gson gson1 = new Gson();
		String toString1 = gson1.toJson(reportEntityList);
		try {
			HttpServletResponse respose = this.getResponse();
			respose.setHeader("Content-type", "text/html;charset=utf-8");
			respose.setCharacterEncoding("utf-8");
			PrintWriter out = respose.getWriter();
			out.print(toString1);
			out.close();
		} catch (IOException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return null;
	}
	/**
	 * 更新扣分审核状态
	 */
	@Action("updateScoreStatus")
	public String updateScoreStatus(){
		this.context();
		if(null == reportEntity) {
			reportEntity= new ReportEntity();
		}
		// 当前登录的用户
		UserEntity user = (UserEntity) this.getRequest().getSession().getAttribute("userInfo");
		reportEntity.setReviewUser(user.getUser_name());
		reportEntity.setReviewTime(new Date());
		String extractResultId=reportEntity.getExtractResultId();
		//查询专家ID
		String expertId=reportService.queryExpertId(reportEntity);
		reportEntity.setExpertId(expertId);
		//更新评价结果表、更新评价表中的状态
		reportService.updateScoreStatus(reportEntity);
		//根据审核状态 更新专家分数 不通过
		if ("2".equals(reportEntity.getIsApprove())){
			reportEntity.setExtractResultId(extractResultId);
			reportEntity=reportService.querySumScore(reportEntity);
			double sumScore=Integer.parseInt(reportEntity.getEvalScore())+reportEntity.getScore();
			reportEntity.setScore(sumScore);
			//查询 专家现有分数
			reportEntity.setExtractResultId(extractResultId);
			reportEntity.setExpertId(expertId);
			reportService.updateNewScore(reportEntity);
			//让专家仍可以重新评价
			reportService.updateAppraiseInfo(reportEntity);
		}
		//审核通过
		if ("1".equals(reportEntity.getIsApprove())){

			//查询当前专家扣分情况
			reportEntity.setSortWay("A.sumScore asc");
			reportEntityList = reportService.queryPageReportSummary(reportEntity);
			if(CollectionUtils.isNotEmpty(reportEntityList)) {
				Date startBidDate=null;
				Date endBidDate=null;
				SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
				if (null!=reportEntity.getReviewTime()){
					String reviewTimeStr = format.format(reportEntity.getReviewTime());
					reportEntity.setReviewTimeStr(reviewTimeStr);
				}
				//查询本次评价分数
				String thisSumScore = reportService.queryThisSumScore(reportEntity);
				//累计扣分
				String sumScoreStr=reportService.queryExpertSumScore(reportEntity);
				double sumScore = Double.valueOf(sumScoreStr);
				//暂停评标开始时间
				startBidDate = reportEntityList.get(0).getStartBidDate();
				//根据扣分情况 暂停专家评标

				Calendar calendar = Calendar.getInstance();
				//已暂停评价
				if (null !=startBidDate){
					calendar.setTime(startBidDate);
					//单次扣分超过20分 暂停12个月
					if (StringUtils.isNotBlank(thisSumScore) && Double.valueOf(thisSumScore)>=20){
						calendar.add(Calendar.MONTH, 12);
						endBidDate = calendar.getTime();
					}else if (sumScore>=20){
						//总扣分超过20分 暂停6个月
						calendar.add(Calendar.MONTH, 6);
						endBidDate = calendar.getTime();
					}
				}else{
					startBidDate=new Date();
					calendar.setTime(startBidDate);
					//还未暂停评标 单次扣分超过20分
					if (StringUtils.isNotBlank(thisSumScore) && Double.valueOf(thisSumScore)>=20){
						calendar.add(Calendar.MONTH, 12);
						endBidDate = calendar.getTime();
					}else if (sumScore>=15 && sumScore<20){
						//总扣分超过15分,少于20分 暂停3个月
						calendar.add(Calendar.MONTH, 3);
						endBidDate = calendar.getTime();
					}else if (sumScore>=20){
						//总扣分超过20分 暂停6个月
						calendar.add(Calendar.MONTH, 6);
						endBidDate = calendar.getTime();
					}
				}
				//是否暂停评标 0:否；1：是
				if (null != endBidDate){
					reportEntity.setIsBid(1);
					reportEntity.setStartBidDate(startBidDate);
					reportEntity.setEndBidDate(endBidDate);
					//修改专家状态 STATUS 11 扣分暂停
					reportService.updateExpertStatus(expertId);
				}else{
					reportEntity.setIsBid(0);
				}
				//更新专家暂停评标信息
				reportService.updateExpertBidStatus(reportEntity);
			}
		}
		return "queryReportDetail";
	}
	/**
	 * 更新拒绝原因
	 */
	@Action("updateRejectionReason")
	public String updateRejectionReason(){
		this.context();
		if(null == reportEntity) {
			reportEntity= new ReportEntity();
		}
		reportService.updateRejectionReason(reportEntity);
		return "queryReportDetail";
	}
	/**
	 * 考核明细
	 */
	@Action("queryReportDetail")
	public String queryReportDetail() {
		this.context();
		if(null == reportEntity) {
			reportEntity= new ReportEntity();
			tableTopStyle="0";
			reportEntity.setStatus("1");
			this.getPage().setCurrentPage(1);
		}
		reportEntityList=new ArrayList<ReportEntity>();
//		setDate(reportEntity);
		if("1".equals(reportEntity.getStatus())) {
			reportEntity.setSortWay("t.score");
		}else if("2".equals(reportEntity.getStatus())){
			reportEntity.setSortWay("TXI.USER_ID");
		}else {
			reportEntity.setSortWay("tp.project_no");
		}
		Page page=this.getPage();
//		reportEntity.setPage(page);
		//查询抽取结果ID
		List<QueryBean> list=reportService.selectDetailResultId(reportEntity);
		if (CollectionUtils.isNotEmpty(list)){
			for (QueryBean bean:list){
				reportEntity.setExtractResultId(bean.getExtractResultId());
				reportEntity.setAppraiseTimeStr(bean.getReviewTimeStr().substring(0,bean.getReviewTimeStr().length()-2));
				List<ReportEntity> reportList = reportService.selectReportDetails(reportEntity);
				reportEntityList.addAll(reportList);
			}
		}
		page.setTotalResult(reportEntityList.size());
		int i=0;
		if (reportEntityList.size()%this.getPage().getShowCount()>0){
			i=reportEntityList.size()/this.getPage().getShowCount()+1;
		}else{
			i=reportEntityList.size()/this.getPage().getShowCount();
		}
		//下一页开始
		int n=(page.getCurrentPage()-1)*page.getShowCount();
		if (CollectionUtils.isNotEmpty(reportEntityList)) {
			if (page.getCurrentPage() * page.getShowCount() > reportEntityList.size()) {
				reportEntityList = reportEntityList.subList(n, reportEntityList.size());
			} else {
				reportEntityList = reportEntityList.subList(n, page.getCurrentPage() * page.getShowCount());
			}
		}
		page.setTotalPage(i);
		reportEntity.setPage(page);
		return "queryReportDetail";
	}
	
	/**
	 * 导出考核明细
	 * 
	 * @return
	 * @throws Exception
	 */
	@Action("exportDetail")
	public String exportDetail() throws Exception {
		this.context();
		this.getResponse().setCharacterEncoding("utf-8");
		PrintWriter out = null;
		try {
			out = this.getResponse().getWriter();
			int totalCols = 9;
			int currentRowNo = 0;
			HSSFWorkbook wb = ExcelUtil.createHSSFWorkbook();
			// 标题
			String titleName = "评标专家考核扣分情况统计(明细)";
			HSSFCellStyle titleCell = ExcelUtil.createHSSFCellStyle(wb, 30, HSSFFont.BOLDWEIGHT_BOLD,HSSFCellStyle.ALIGN_CENTER);
			HSSFSheet sheet = ExcelUtil.createHSSFSheet(wb, titleName, HSSFPrintSetup.A4_PAPERSIZE,tableTopStyle);
			currentRowNo = ExcelUtil.mergeRowsOne2(sheet, titleCell, currentRowNo, totalCols,new String[] { titleName }, new int[] { totalCols });
			// 小标题
			currentRowNo++;
			String amountUnit = " 年    月    日";
			HSSFCellStyle amountCell = ExcelUtil.createHSSFCellStyle(wb, 10, HSSFFont.BOLDWEIGHT_NORMAL,HSSFCellStyle.ALIGN_RIGHT);
			currentRowNo = ExcelUtil.mergeRowsOne2(sheet, amountCell, currentRowNo, totalCols,new String[] { amountUnit }, new int[] { totalCols });

			// 表头
			currentRowNo++;
			HSSFCellStyle tableTotalCell = ExcelUtil.createHSSFCellStyle(wb, 10, HSSFFont.BOLDWEIGHT_NORMAL,HSSFCellStyle.ALIGN_CENTER);
			String[] tableTotal = { "扣分值", "姓名", "手机号", "身份证号", "参评项目名称", "标段", "项目负责人", "日期", "扣分原因" };
			int[] tableCols = { 1, 1, 1, 1, 1, 1, 1, 1, 1 };
			currentRowNo = ExcelUtil.mergeRowsOne2(sheet, tableTotalCell, currentRowNo, totalCols, tableTotal,tableCols);
			//需要合并的列
			int index = 0 ;
			// 表单所用到的数据
			if(null == reportEntity) {
				reportEntity= new ReportEntity();
				reportEntity.setStatus("1");
			}
			//给开始时间和结束时间赋值
			setDate(reportEntity);
			if(reportEntity.getStatus().equals("1")) {
				reportEntity.setSortWay("t.score");
				index = 0 ;
			}else if(reportEntity.getStatus().equals("2")){
				reportEntity.setSortWay("TXI.USER_ID");
				index = 1 ;
			}else {
				reportEntity.setSortWay("tp.project_no");
				index = 4;
			}
			List<List<String>> entityLst = reportService.processData(reportEntity,index);
			if (entityLst.size() > 0) {
				// 保留数据
				int[] contentCols = { 1, 1, 1, 1, 1, 1, 1, 1, 1 };
				for (int i = 0; i < entityLst.size(); i++) {
					currentRowNo++;
					List<String> sls = entityLst.get(i);
					currentRowNo = mergeRowsOne1(sheet, tableTotalCell, currentRowNo, currentRowNo, totalCols, sls,contentCols);
				}
				ExcelUtil.autoSheetAutoSizeColumn(sheet, contentCols.length);
			} else {
				currentRowNo++;
				String error = "没有符合条件的数据！";
				currentRowNo = ExcelUtil.mergeRowsOne2(sheet, titleCell, currentRowNo, totalCols,
						new String[] { error }, new int[] { totalCols });
			}
			// 动态合并第二步：将生成的sheet的工作空间重新加载
			int sk = 3;
			if (sheet.getLastRowNum() > 3) {
				// 循环除了第一行以外的每一行
				for (int i = 4; i <= sheet.getLastRowNum(); i++) {
					HSSFRow rows = sheet.getRow(i);
					HSSFCell cell_0 = rows.getCell(index);
					// 判断是否是空值
					if (cell_0.getStringCellValue() == "") {
						if (i == sheet.getLastRowNum()) {
							sheet.addMergedRegion(new CellRangeAddress(sk, i, index, index));
						}
					} else {
						if (sk != i - 1) {
							sheet.addMergedRegion(new CellRangeAddress(sk, i - 1, index, index));
						}
						sk = i;
					}

				}
			}
			currentRowNo++;
			String[] tableFoolt = {"经办人：","处室负责人：","公司领导："};
			int[][] mergeCols = {{1,4,1},{1,3,5},{1,2,8}};
			currentRowNo = ExcelUtil.mergeRowsOne(sheet, tableTotalCell, currentRowNo, totalCols,
					tableFoolt, mergeCols);
			
			getFile(titleName, wb, out);
		} catch (Exception e) {
			e.printStackTrace();
			out.print("error");
		}
		return null;
	}
	/**
	 * 汇总查询页面
	 * @return
	 */
	@Action("queryReportSummary")
	public String queryReportSummary() {
		this.context();
		if(null == reportEntity) {
			reportEntity= new ReportEntity();
			tableTopStyle="0";
			this.getPage().setCurrentPage(1);
		}
		String sort="";
		//排序
		if (StringUtils.isBlank(reportEntity.getSortWay()) || "1".equals(reportEntity.getSortWay())) {
			reportEntity.setSortWay("A.sumScore asc");
		}else if ("2".equals(reportEntity.getSortWay())){
			reportEntity.setSortWay("A.sumScore desc");
		}else if ("3".equals(reportEntity.getSortWay())){
			reportEntity.setSortWay("A.bidDate asc");
		}else if ("4".equals(reportEntity.getSortWay())){
			reportEntity.setSortWay("A.bidDate desc");
		}
		reportEntity.setPage(this.getPage());
		reportEntityList=new ArrayList<ReportEntity>();
		reportEntityList = reportService.queryPageReportSummary(reportEntity);
		return "queryReportSummary";
	}
	/**
	 * 导出考核汇总
	 * 
	 * @return
	 * @throws Exception
	 */
	@Action("exportSummary")
	public String exportSummary() {
		this.context();
		this.getResponse().setCharacterEncoding("utf-8");
		PrintWriter out = null;
		tableTopStyle="0";
		try {
			out = this.getResponse().getWriter();
			int totalCols = 10;
			int currentRowNo = 0;
			HSSFWorkbook wb = ExcelUtil.createHSSFWorkbook();
			// 标题
			String titleName = "评标专家考核扣分情况统计(汇总)";
			HSSFCellStyle titleCell = ExcelUtil.createHSSFCellStyle(wb, 30, HSSFFont.BOLDWEIGHT_BOLD,HSSFCellStyle.ALIGN_CENTER);
			HSSFSheet sheet = ExcelUtil.createHSSFSheet(wb, titleName, HSSFPrintSetup.A4_PAPERSIZE,tableTopStyle);
			currentRowNo = ExcelUtil.mergeRowsOne2(sheet, titleCell, currentRowNo, totalCols,new String[] { titleName }, new int[] { totalCols });
			// 小标题
			currentRowNo++;
			String amountUnit = " 年    月    日";
			HSSFCellStyle amountCell = ExcelUtil.createHSSFCellStyle(wb, 10, HSSFFont.BOLDWEIGHT_NORMAL,HSSFCellStyle.ALIGN_RIGHT);
			currentRowNo = ExcelUtil.mergeRowsOne2(sheet, amountCell, currentRowNo, totalCols,new String[] { amountUnit }, new int[] { totalCols });

			// 表头
			currentRowNo++;
			HSSFCellStyle tableTotalCell = ExcelUtil.createHSSFCellStyle(wb, 10, HSSFFont.BOLDWEIGHT_NORMAL,HSSFCellStyle.ALIGN_CENTER);
			String[] tableTotal = { "统计年度","扣分累计", "姓名", "手机号", "身份证号","日期","扣分原因","扣分值","抽取批次号","处理意见"};
			int[] tableCols = { 1, 1, 1, 1, 1, 1, 1, 1, 1, 1};
			currentRowNo = ExcelUtil.mergeRowsOne2(sheet, tableTotalCell, currentRowNo, totalCols, tableTotal,tableCols);
			
			if(null==reportEntity) {
				reportEntity = new ReportEntity();
			}
			//给开始时间和结束时间赋值
			setDate(reportEntity);

			// 表单所用到的数据
			Map<String,List<List<String>>> entityMap = reportService.sumData(reportEntity);
			if (entityMap.get("gList").size() > 0) {
				List<List<String>> entityLst = entityMap.get("gList");
				// 保留数据
				int[] contentCols = { 1, 1, 1, 1, 1, 1, 1,1,1,1};
				
				for (int i = 0; i < entityLst.size(); i++) {
					currentRowNo++;
					List<String> sls = entityLst.get(i);
					// 动态合并第一步将相同的数据变成空值，保留第一次出现的数据()
					currentRowNo = mergeRowsOne1(sheet, tableTotalCell, currentRowNo, currentRowNo, totalCols, sls,contentCols);
					/*if (i == 0) {
						saveScore = sls.get(0);
						saveIdCode = sls.get(3);
						currentRowNo = mergeRowsOne1(sheet, tableTotalCell, currentRowNo, currentRowNo, totalCols, sls,contentCols);
					} else {
						if (sls.get(0).equals(saveScore)&&sls.get(3).equals(saveIdCode)) {
							sls.set(0, "");
							sls.set(1, "");
							sls.set(2, "");
							sls.set(3, "");
						} else {
							saveScore = entityLst.get(i).get(0);
							saveIdCode = entityLst.get(i).get(3);
						}
						currentRowNo = mergeRowsOne1(sheet, tableTotalCell, currentRowNo, currentRowNo, totalCols, sls,contentCols);
					}*/
				}
				ExcelUtil.autoSheetAutoSizeColumn(sheet, contentCols.length);
			} 
			
			/*if (entityMap.get("sList").size() > 0) {
				List<List<String>> entityLst = entityMap.get("sList");
				// 保留数据
				int[] contentCols = { 1, 1, 1, 1, 1, 1, 1,1,1,1};
				
				for (int i = 0; i < entityLst.size(); i++) {
					currentRowNo++;
					List<String> sls = entityLst.get(i);
					// 动态合并第一步将相同的数据变成空值，保留第一次出现的数据()
					currentRowNo = mergeRowsOne1(sheet, tableTotalCell, currentRowNo, currentRowNo, totalCols, sls,contentCols);
					
				}
				ExcelUtil.autoSheetAutoSizeColumn(sheet, contentCols.length);
			}*/
			//disposeDate(sheet);
			//如何没有输出输出
			if(/*entityMap.get("sList").size() == 0&&*/entityMap.get("gList").size() == 0) {
					currentRowNo++;
					String error = "没有符合条件的数据！";
					currentRowNo = ExcelUtil.mergeRowsOne2(sheet, titleCell, currentRowNo, totalCols,
							new String[] { error }, new int[] { totalCols });
			}
			// 动态合并第二步：将生成的sheet的工作空间重新加载
			int sk = 3;
			//保存处理意见行
			boolean flag = true;
			int rp=3;
			if (sheet.getLastRowNum() > 3) {
				// 循环除了第一行以外的每一行
				for (int i = 4; i <= sheet.getLastRowNum(); i++) {
					HSSFRow rows = sheet.getRow(i);
					HSSFCell cell_0 = rows.getCell(0);
					// 判断是否是空值
					
					if (cell_0.getStringCellValue() == "") {
						HSSFCell cell_7= rows.getCell(7);
						if(Double.parseDouble(cell_7.getStringCellValue().toString())>=20 ) {
							
							HSSFCell cell_9 = rows.getCell(9);
							HSSFRow rows20 = sheet.getRow(sk);
							HSSFCell row20_cell_9 = rows20.getCell(9);
							row20_cell_9.setCellValue(new HSSFRichTextString(cell_9.getStringCellValue()));
						}
						if (i == sheet.getLastRowNum()) {
							sheet.addMergedRegion(new CellRangeAddress(sk, i, 0, 0));
							sheet.addMergedRegion(new CellRangeAddress(sk, i, 1, 1));
							sheet.addMergedRegion(new CellRangeAddress(sk, i, 2, 2));
							sheet.addMergedRegion(new CellRangeAddress(sk, i, 3, 3));
							sheet.addMergedRegion(new CellRangeAddress(sk, i, 4, 4));
							sheet.addMergedRegion(new CellRangeAddress(sk, i, 9, 9));
						}
						
					} else {
						HSSFCell cell_7= rows.getCell(7);
						if(Double.parseDouble(cell_7.getStringCellValue().toString())>=20 ) {
							
							HSSFCell cell_9 = rows.getCell(9);
							HSSFRow rows20 = sheet.getRow(sk);
							HSSFCell row20_cell_9 = rows20.getCell(9);
							row20_cell_9.setCellValue(new HSSFRichTextString(cell_9.getStringCellValue()));
							/*if (rp != i - 1) {
							sheet.addMergedRegion(new CellRangeAddress(rp, i - 1, 9, 9));
							}
							rp=i;
							flag =false;*/
						}
						if (sk != i - 1) {                     
							sheet.addMergedRegion(new CellRangeAddress(sk, i - 1, 0, 0));
							sheet.addMergedRegion(new CellRangeAddress(sk, i - 1, 1, 1));
							sheet.addMergedRegion(new CellRangeAddress(sk, i - 1, 2, 2));
							sheet.addMergedRegion(new CellRangeAddress(sk, i - 1, 3, 3));
							sheet.addMergedRegion(new CellRangeAddress(sk, i - 1, 4, 4));
							sheet.addMergedRegion(new CellRangeAddress(sk, i - 1, 9, 9));
						}
						
						sk = i;
					}

				}
			}
			currentRowNo++;
			String[] tableFoolt = {"经办人：","处室负责人：","公司领导："};
			int[][] mergeCols = {{1,3,1},{1,2,4},{1,2,6}};
			currentRowNo = ExcelUtil.mergeRowsOne(sheet, tableTotalCell, currentRowNo, totalCols,
					tableFoolt, mergeCols);
			//保存文件
			getFile(titleName, wb, out);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}
	/**
	 * 注销专家页面
	 * @return
	 */
	@Action("queryReportCancellation")
	public String queryReportCancellation() {
		//默认加载待注销页面
		String returnPage = "queryReportCancellationT";
		this.context();
		//第一次加载赋值
		if(null == reportEntity) {
			reportEntity= new ReportEntity();
			reportEntity.setCancellationWay("2");
			reportEntity.setStatus("0");
			tableTopStyle="0";
		}
		if(reportEntity.getCancellationWay().equals("2")) {
			if(reportEntity.getStatus() ==null||reportEntity.getStatus().equals("0")) {
				reportEntity.getReasonLst().add("重大疾病");
				reportEntity.getReasonLst().add("已去世");
				reportEntity.getReasonLst().add("限制自由");
			}else {
				reportEntity.getReasonLst().add(reportEntity.getStatus());
			}
		}
		//给开始时间和结束时间赋值
		setDate(reportEntity);
		reportEntity.setPage(this.getPage());
		reportEntityList = reportService.queryPageReportCancellation(reportEntity);
		if(reportEntity.getCancellationWay().equals("2")) {
			if(reportEntity.getStatus()==null) {
				reportEntity.setStatus("0");
			}
			returnPage= "queryReportCancellationT";
		}else {
			returnPage = "queryReportCancellation";
		}
		return returnPage;
	}
	/**
	 * 导出注销专家
	 * 
	 * @return
	 * @throws Exception
	 */
	@Action("expertCancellation")
	public String expertCancellation() {
		this.context();
		this.getResponse().setCharacterEncoding("utf-8");
		PrintWriter out = null;
		try {
			out = this.getResponse().getWriter();
			int totalCols = 7;
			int currentRowNo = 0;
			HSSFWorkbook wb = ExcelUtil.createHSSFWorkbook();
			// 标题
			String titleName = "评标专家考核扣分情况统计(注销)";
			HSSFCellStyle titleCell = ExcelUtil.createHSSFCellStyle(wb, 30, HSSFFont.BOLDWEIGHT_BOLD,HSSFCellStyle.ALIGN_CENTER);
			HSSFSheet sheet = ExcelUtil.createHSSFSheet(wb, titleName, HSSFPrintSetup.A4_PAPERSIZE,tableTopStyle);
			currentRowNo = ExcelUtil.mergeRowsOne2(sheet, titleCell, currentRowNo, totalCols,new String[] { titleName }, new int[] { totalCols });
			// 小标题
			currentRowNo++;
			String amountUnit = " 年    月    日";
			HSSFCellStyle amountCell = ExcelUtil.createHSSFCellStyle(wb, 10, HSSFFont.BOLDWEIGHT_NORMAL,HSSFCellStyle.ALIGN_RIGHT);
			currentRowNo = ExcelUtil.mergeRowsOne2(sheet, amountCell, currentRowNo, totalCols,new String[] { amountUnit }, new int[] { totalCols });

			// 表头
			currentRowNo++;
			HSSFCellStyle tableTotalCell = ExcelUtil.createHSSFCellStyle(wb, 10, HSSFFont.BOLDWEIGHT_NORMAL,HSSFCellStyle.ALIGN_CENTER);
			String[] tableTotal = { "序号", "姓名", "手机号", "身份证号", "日期",  "原因" ,"备注"};
			int[] tableCols = { 1, 1, 1, 1, 1, 1, 1 };
			currentRowNo = ExcelUtil.mergeRowsOne2(sheet, tableTotalCell, currentRowNo, totalCols, tableTotal,tableCols);
			if(null==reportEntity) {
				reportEntity = new ReportEntity();
			}
			//给开始时间和结束时间赋值
			setDate(reportEntity);
			
			if(reportEntity.getCancellationWay().equals("2")) {
				if(reportEntity.getStatus().equals("0")) {
					reportEntity.getReasonLst().add("重大疾病");
					reportEntity.getReasonLst().add("已去世");
					reportEntity.getReasonLst().add("限制自由");
				}else {
					reportEntity.getReasonLst().add(reportEntity.getStatus());
				}
			}
			// 表单所用到的数据
			List<List<String>> entityLst = reportService.cancellationDate(reportEntity);
			if (entityLst.size() > 0) {
				int[] contentCols = { 1, 1, 1, 1, 1, 1, 1};
				for (int i = 0; i < entityLst.size(); i++) {
					currentRowNo++;
					List<String> sls = entityLst.get(i);
					currentRowNo = mergeRowsOne1(sheet, tableTotalCell, currentRowNo, currentRowNo, totalCols, sls,contentCols);
				}
				ExcelUtil.autoSheetAutoSizeColumn(sheet, contentCols.length);
				
				
			} else {
				currentRowNo++;
				String error = "没有符合条件的数据！";
				currentRowNo = ExcelUtil.mergeRowsOne2(sheet, titleCell, currentRowNo, totalCols,
						new String[] { error }, new int[] { totalCols });
			}

			// 动态合并第二步：将生成的sheet的工作空间重新加载
			int sk = 3;
			if (sheet.getLastRowNum() > 3) {
				// 循环除了第一行以外的每一行
				for (int i = 5; i <= sheet.getLastRowNum(); i++) {
					HSSFRow rows = sheet.getRow(i);
					HSSFCell cell_0 = rows.getCell(1);
					// 判断是否是空值
					if (cell_0.getStringCellValue() == "") {
						if (i == sheet.getLastRowNum()) {
							sheet.addMergedRegion(new CellRangeAddress(sk, i, 1, 1));
							sheet.addMergedRegion(new CellRangeAddress(sk, i, 2, 2));
							sheet.addMergedRegion(new CellRangeAddress(sk, i, 3, 3));
						}
					} else {
						if (sk != i - 1) {
							sheet.addMergedRegion(new CellRangeAddress(sk, i - 1, 1, 1));
							sheet.addMergedRegion(new CellRangeAddress(sk, i - 1, 2, 2));
							sheet.addMergedRegion(new CellRangeAddress(sk, i - 1, 3, 3));
						}
						sk = i;
					}

				}
			}
			currentRowNo++;
			String[] tableFoolt = {"经办人：","处室负责人：","公司领导："};
			int[][] mergeCols = {{1,2,1},{1,2,4},{1,2,6}};
			currentRowNo = ExcelUtil.mergeRowsOne(sheet, tableTotalCell, currentRowNo, totalCols,
					tableFoolt, mergeCols);
			//保存文件
			getFile(titleName, wb, out);
		} catch (Exception e) {
			out.print("error");
		}
		return null;
	}
	public ReportEntity getReportEntity() {
		return reportEntity;
	}

	public void setReportEntity(ReportEntity reportEntity) {
		this.reportEntity = reportEntity;
	}

	public List<ReportEntity> getReportEntityList() {
		return reportEntityList;
	}

	public void setReportEntityList(List<ReportEntity> reportEntityList) {
		this.reportEntityList = reportEntityList;
	}

	/**
	 * 
	 * 函数功能描述：生成结构化EXCEL (主要用在遍历没有跨行EXCEL的内容)
	 * 
	 * @param sheet
	 *            表单对象
	 * @param style
	 *            单元格风格
	 * @param startRowNo
	 *            开始行
	 * @param currentRowNo
	 *            当前行号
	 * @param colspanRowNo
	 *            合并的列
	 * @param totalCols
	 *            每行共有多少单元格
	 * @param contextList
	 *            需要合并的内容 单元格List
	 * @param mergeCols
	 *            内容对应占有多少单元格 new[] margeCols 只需要告知跨多少列
	 * @return
	 */
	public static int mergeRowsOne1(HSSFSheet sheet, HSSFCellStyle style, int startRowNo, int currentRowNo,
			int totalCols, List<String> contextList, int[] mergeCols) {
		if (mergeCols == null || mergeCols.length <= 0) {
			return currentRowNo;
		}
		if (contextList == null) {
			contextList = new ArrayList<String>();
		}
		// 1.创建改行
		HSSFRow row = ExcelUtil.createHSSFRow(style, sheet, currentRowNo, totalCols);
		int firstCols = 0;
		int lastCols = 0;
		for (int i = 0; i < mergeCols.length; i++) {
			lastCols = firstCols + mergeCols[i] - 1;
			HSSFCell cell = row.getCell(firstCols);
			cell.setCellStyle(style);
			String value = null;
			if (contextList.size() > i) {
				value = contextList.get(i);
			}

			if (value == null || value == "") {
				value = "";
			}
			cell.setCellType(HSSFCell.CELL_TYPE_STRING);
			cell.setCellValue(value);
			sheet.setColumnWidth(firstCols, value.getBytes().length * 2);
			/*
			 * sheet.addMergedRegion( new CellRangeAddress(startRowNo, currentRowNo,
			 * firstCols, lastCols));
			 */

			/* sheet.autoSizeColumn(firstCols, true); */

			firstCols = lastCols + 1;
		}
		return currentRowNo;
	}
	/**
	 * 获取当前年份一年前的时间
	 * @return
	 */
	private String getEndTime(String formatStr) {
		Calendar ca = Calendar.getInstance();// 得到一个Calendar的实例  
		ca.setTime(new Date()); // 设置时间为当前时间  
		ca.add(Calendar.MONTH, -1); // 月份减1  
		ca.set(Calendar.DAY_OF_MONTH, 1);
		Date resultDate = ca.getTime(); // 结果  
		SimpleDateFormat sdf = new SimpleDateFormat(formatStr);  
		return sdf.format(resultDate);  
	}
	/**
	 * 给页面查询条件的 开始和结束时间赋予默认值
	 * @param report
	 */
	private void setDate(ReportEntity report) {
		if(reportEntity.getStartTime()==null||reportEntity.getStartTime()=="") {
			reportEntity.setStartTime(getEndTime("yyyy-MM-dd"));
		}
		if(reportEntity.getEndTime()==null||reportEntity.getEndTime()=="") {
			reportEntity.setEndTime(DateUtil.dateToString(new Date()));
		}
		//审核时间
		if(StringUtils.isBlank(reportEntity.getStartReviewTime())) {
			reportEntity.setStartReviewTime(getEndTime("yyyy-MM-dd"));
		}
		if(StringUtils.isBlank(reportEntity.getEndReviewTime())) {
			reportEntity.setEndReviewTime(DateUtil.dateToString(new Date()));
		}
	}
	/**
	 * Excel中数据已经生成，将数据进行二次处理
	 */
	private void disposeDate(HSSFSheet sheet) {
		//表示每列的编号
		int rk_0 = 3;
		int rk_1 = 3;
		int rk_2 = 3;
		int rk_3 = 3;
		int rk_4 = 3;
		int rk_9 = 3;
		if (sheet.getLastRowNum() > 3) {
			// 循环除了第一行以外的每一行
			for (int i = 3; i <= sheet.getLastRowNum(); i++) {
				HSSFRow rows = sheet.getRow(i);
				HSSFCell cell_0 = rows.getCell(0);
				HSSFCell cell_1 = rows.getCell(1);
				HSSFCell cell_2 = rows.getCell(2);
				HSSFCell cell_3 = rows.getCell(3);
				HSSFCell cell_4 = rows.getCell(3);
				HSSFCell cell_9 = rows.getCell(9);
				
				// 判断是否是空值
				if (cell_0.getStringCellValue() == "") {
					sheet.addMergedRegion(new CellRangeAddress(rk_0, i, 0, 0));
					
				}else {
					rk_0 =i;
				}
				// 判断是否是空值
				if (cell_1.getStringCellValue() == "") {
					sheet.addMergedRegion(new CellRangeAddress(rk_1, i, 1, 1));
				}else {
					rk_1 =i;
				}
				// 判断是否是空值
				if (cell_2.getStringCellValue() == "") {
					sheet.addMergedRegion(new CellRangeAddress(rk_2, i, 2, 2));
				}else {
					rk_2 =i;
				}
				// 判断是否是空值
				if (cell_3.getStringCellValue() == "") {
					sheet.addMergedRegion(new CellRangeAddress(rk_3, i, 3, 3));
				}else {
					rk_3 =i;
				}
				
				// 判断是否是空值
				if (cell_4.getStringCellValue() == "") {
					sheet.addMergedRegion(new CellRangeAddress(rk_4, i, 4, 4));
				}else {
					rk_4 =i;
				}
				// 判断是否是空值
				if (cell_9.getStringCellValue() == "") {
					sheet.addMergedRegion(new CellRangeAddress(rk_9, i, 9, 9));
				}else {
					rk_9 =i;
				}

			}
		}
	}
	/**
	 * 获取文件保存路径
	 * @param titleName
	 * @param wb
	 * @param out
	 */
	private void getFile(String  titleName,HSSFWorkbook wb,PrintWriter out) {
		String savePath = ServletActionContext.getServletContext().getRealPath("") + File.separator+ fileName;
		File f1 = new File(savePath);
		if (!f1.exists()) {//
			f1.mkdirs();
		}
		String newName = titleName+"_" + System.currentTimeMillis() + ".xls";
		FileOutputStream fileOut = null;
		try {
			fileOut = new FileOutputStream(savePath +File.separator + newName);
			wb.write(fileOut);
		} catch (Exception e) {
			out.print("error");
			log.error(e);
		} finally {
			if (fileOut != null) {
				try {
					fileOut.close();
					// out.print("zjcqjl/"+projectEntity.getProjectId());
					// newName=new String(newName.getBytes("GB-2312"),"UTF-8");
					out.print("{\"filePath\":\""+ "/" + fileName + "\","+ "\"fileName\":\"" + newName + "\"}");
					out.close();
				} catch (IOException e) {
					out.print("error");
				}
			}
		}
	}


	public String getTableTopStyle() {
		return tableTopStyle;
	}


	public void setTableTopStyle(String tableTopStyle) {
		this.tableTopStyle = tableTopStyle;
	}
	
}
