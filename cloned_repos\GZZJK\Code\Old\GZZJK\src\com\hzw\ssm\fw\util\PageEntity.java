package com.hzw.ssm.fw.util;

import java.util.List;

@SuppressWarnings("rawtypes")
public class PageEntity {

	// 分页结果集
	private List dataList = null;
	// // 每页显示记录数
	// private int systemRecordPage = 20;
	// // 记录总数
	// private int systemRecordTotal = 0;
	// // 查询时记录偏移量（离开起始点的位置）
	 private int systemRecordOff = 0;
	// // 当前页记录开始条数
	// private int systemRecordFrom = 0;
	// // 当前页记录结束条数
	// private int systemRecordTo = 0;
	// // 当前页数
	// private int systemCurrentPageNum = 0;
	// // 总页数
	// private int systemPageTotal = 1;
	// // 分页页脚
	// private String systemPageInfo;
	// // 翻页类型 1:首页 2：上一页 3：下一页 4：末页
	// private int systemPageCommand = 0;

	// 当前页数
	private int currentPage = 1;
	// 每页显示数量
	private int everyPage = 10;
	// 总页数
	private int totalPage;
	// 总数量
	private int totalCount;

	// 初始化分页组件 （将被删除）
	public void init(int totalcount, int pageSize, int pageNum, List dataList) {
		this.currentPage = pageNum;
		this.everyPage = pageSize;
		setDataList(dataList);
	}

	// 设置总数量的同时，设置总页数
	public void setCount(int totalCount) {
		this.totalCount = totalCount;
		int temp = 0;
		if (totalCount % this.everyPage != 0) {
			temp++;
		}
		this.totalPage = totalCount / this.everyPage + temp;
	}

	public List getDataList() {
		return dataList;
	}

	public void setDataList(List dataList) {
		this.dataList = dataList;
	}

	public int getCurrentPage() {
		return currentPage;
	}

	public void setCurrentPage(int currentPage) {
		this.currentPage = currentPage;
	}

	public int getEveryPage() {
		return everyPage;
	}

	public void setEveryPage(int everyPage) {
		this.everyPage = everyPage;
	}

	public int getTotalPage() {
		return totalPage;
	}

	public void setTotalPage(int totalPage) {
		this.totalPage = totalPage;
	}

	public int getTotalCount() {
		return totalCount;
	}

	public void setTotalCount(int totalCount) {
		this.totalCount = totalCount;
	}

	public int getSystemRecordOff() {
		return systemRecordOff = (currentPage - 1) * everyPage;
	}

	public void setSystemRecordOff(int systemRecordOff) {
		this.systemRecordOff = CommUtil.changeStringToInt(systemRecordOff, 0);
	}
	
	

}
