package com.hzw.ssm.sys.system.dao;

import java.util.List;

import com.hzw.ssm.sys.system.entity.SmsRecordEntity;

/**
 * 短信记录信息操作类
 * <AUTHOR>
 * @date 2015-08-05
 *
 */
public interface SmsRecordMapper 
{
	/**
	 * 批量添加短信记录
	 * @param smsRecordList
	 */
	public void insertSmsRecordList(List<SmsRecordEntity> smsRecordList);
	
	/**
	 * 添加短信记录
	 * @param smsRecordList
	 */
	public void insertSmsRecord(SmsRecordEntity smsRecord);
	/**
	 * 语音短信修改成功与否的状态
	 * @param smsRecord
	 */
	public void updateSmsRecord(SmsRecordEntity smsRecord);
	/**
	 * 查询未收到短信的专家(自动短信)
	 * @param smsRecord
	 * @return
	 */
	public List<SmsRecordEntity> queryPageUnreceivedSmsRecord(SmsRecordEntity smsRecord);
	
	/**
	 * 查询未收到短信的专家(页面)
	 * @param smsRecord
	 * @return
	 */
	public List<SmsRecordEntity> queryPageUnreceivedSmsRecordList(SmsRecordEntity smsRecord);
	
	/**
	 * 修改通知综合处
	 * @param smsRecord
	 */
	public void updateInform(SmsRecordEntity smsRecord);
	
	public void updateSmsIsDispose(SmsRecordEntity smsRecord);
}
