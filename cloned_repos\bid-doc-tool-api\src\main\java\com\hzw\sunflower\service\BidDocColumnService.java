package com.hzw.sunflower.service;

import com.hzw.sunflower.dto.TemplateFillDTO;
import com.hzw.sunflower.entity.BidDocColumn;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>
 * 招标文件编制字段配置表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-26
 */
public interface BidDocColumnService extends IService<BidDocColumn> {

    /**
     * 保存编制字段
     * @param docId
     * @param columnList
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    boolean saveColumns(Long docId, List<BidDocColumn> columnList);

    /**
     * 生成填充DTO
     * @param docId
     * @return
     */
    TemplateFillDTO getFillDto(Long docId) throws Exception;
}
