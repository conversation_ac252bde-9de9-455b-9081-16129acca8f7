
/// 
/// * common Refresh gif animation view 
/// <AUTHOR>
/// @date 20101024
/// 
import "package:flutter/material.dart";

import 'package:flutter_main_app/src/utils/GifAnimation.dart';

class DefaultLoadingMoreDataWidget extends StatefulWidget {
  const DefaultLoadingMoreDataWidget({Key key}) : super(key: key);

  @override
  _DefaultLoadingMoreDataWidgetState createState() => _DefaultLoadingMoreDataWidgetState();
}

class _DefaultLoadingMoreDataWidgetState extends State<DefaultLoadingMoreDataWidget> with SingleTickerProviderStateMixin {

  static const String _loadingMoreDataTip = '正在加载更多数据';

  GifController _animationCtrl;

  Widget _animateWidget;

  bool _isShowPlaceHoldImage = true;

  @override
  void initState() {
    super.initState();

    _animationCtrl = GifController(
      vsync: this,
      duration: Duration(milliseconds: 1200),
      frameCount: 35,
      startAnimationCallback: () {
         setState(() {
          _isShowPlaceHoldImage = false;
        });
      },
    );
  }

  @override
  void dispose() {
    _animationCtrl.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {

    this._animateWidget = _buildGif();

    return Container(
      constraints: BoxConstraints(minHeight: 4, maxHeight: 20),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: <Widget>[
          Visibility(
            child: Image.asset('assets/images/waitting.png'),
            visible: _isShowPlaceHoldImage,
          ),
          this._animateWidget,
          Text(_loadingMoreDataTip),
        ],
      ),
    );
  }

  ///***
  /// * build loading widget rotating infinitely
  /// *
  /// * @return {Widget}
  /// */
  Widget _buildGif() {
    Widget ret = GifAnimation(
      image: AssetImage('assets/images/waitting.gif'),
      controller: _animationCtrl,
    );

    _animationCtrl.runAni();
    _animationCtrl.repeat();

    return ret;
  }

}