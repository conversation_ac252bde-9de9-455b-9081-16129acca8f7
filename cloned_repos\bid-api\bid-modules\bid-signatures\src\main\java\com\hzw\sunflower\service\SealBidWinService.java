package com.hzw.sunflower.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hzw.sunflower.controller.request.SealBidWinREQ;
import com.hzw.sunflower.controller.response.SealBidWinVO;
import com.hzw.sunflower.entity.SealBidWin;

import java.util.List;


/**
* SealBidWinService接口
*
*/
public interface SealBidWinService extends IService<SealBidWin> {
    /**
     * 发放中标通知书查询盖章文件
     * @param req
     * @return
     */
    List<SealBidWinVO> listSealFiles(SealBidWinREQ req);
}