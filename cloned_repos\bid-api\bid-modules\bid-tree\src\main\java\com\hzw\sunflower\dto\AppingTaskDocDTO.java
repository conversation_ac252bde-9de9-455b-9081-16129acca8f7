package com.hzw.sunflower.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

@Data
@EqualsAndHashCode
public class AppingTaskDocDTO  implements Serializable {

    /**
     * 关联标段
     */
    @ApiModelProperty(value = "项目ID")
    private Long projectId;
    /**
     * 项目编名称
     */
    @ApiModelProperty(value = "项目编名称")
    private String projectName;

    /**
     * 项目编号
     */
    @ApiModelProperty(value = "项目编号")
    private String projectNum;

    /**
     * 类别
     */
    @ApiModelProperty(value = "类别")
    private String processDefinitionKey;

    /**
     * 关联标段
     */
    @ApiModelProperty(value = "关联标段")
    private String projectBidSections;

    /**
     * 招标文件ID
     */
    @ApiModelProperty(value = "招标文件ID")
    private Long docId;


    /**
     * 阶段： 0：默认无阶段； 1：第一轮； 2：第二轮
     */
    @ApiModelProperty(value = "阶段", required = true)
    private Integer bidRound;
}
