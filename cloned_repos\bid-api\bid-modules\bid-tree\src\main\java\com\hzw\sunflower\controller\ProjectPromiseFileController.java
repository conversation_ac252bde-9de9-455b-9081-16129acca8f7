package com.hzw.sunflower.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hzw.sunflower.common.BaseController;
import com.hzw.sunflower.common.Paging;
import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.common.annotation.RepeatSubmit;
import com.hzw.sunflower.constant.MessageConstants;
import com.hzw.sunflower.controller.request.PromiseFileReq;
import com.hzw.sunflower.controller.request.PromiseFileSignReq;
import com.hzw.sunflower.controller.response.ProjectPromiseFileVo;
import com.hzw.sunflower.entity.condition.ProjectPromiseFileCondition;
import com.hzw.sunflower.service.ProjectPromiseFileService;
import com.hzw.sunflower.service.PromiseFileSignService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;


/**
 * <AUTHOR>
 * @date ：Created in 2021/8/30 10:05
 * @description：项目承诺文件服务
 * @version: 1.0
 */
@Api(tags = "项目承诺文件服务")
@RestController
@RequestMapping("/promiseFile")
public class ProjectPromiseFileController extends BaseController {

    @Autowired
    private ProjectPromiseFileService projectPromiseFileService;

    @Autowired
    private PromiseFileSignService promiseFileSignService;

    @ApiOperation(value = "根据条件分页查询承诺文件列表")
    @ApiImplicitParam(name = "condition", value = "承诺文件表查询条件", required = true, dataType = "ProjectPromiseFileCondition", paramType = "body")
    @PostMapping("/listPage")
    public Result<Paging<ProjectPromiseFileVo>> listPage(@RequestBody ProjectPromiseFileCondition condition) {
        if (condition.getProjectId() == null) {
            Result.failed(MessageConstants.PARAMS_NOT_NULL);
        }
        IPage<ProjectPromiseFileVo> page = projectPromiseFileService.listPage(condition);
        return Result.ok(Paging.buildPaging(page));
    }

    @ApiOperation(value = "新增其他承诺文件")
    @RepeatSubmit
    @ApiImplicitParam(name = "req", value = "承诺文件信息 ", required = true, dataType = "PromiseFileReq", paramType = "body")
    @PostMapping("/addPromiseFile")
    public Result<Boolean> addPromiseFile(@RequestBody PromiseFileReq req) {
        if (null == req.getProjectId()) {
            return Result.failed(MessageConstants.PARAMS_NOT_NULL);
        }
        return Result.okOrFailed(projectPromiseFileService.addPromiseFile(req));
    }

    @ApiOperation(value = "删除承诺文件")
    @ApiImplicitParam(name = "id", value = "主键ID", required = true)
    @PostMapping("/deletePromiseFile/{id}")
    @RepeatSubmit
    public Result<Boolean> deletePromiseFile(@PathVariable Long id) {
        if (null == id) {
            return Result.failed(MessageConstants.PARAMS_NOT_NULL);
        }
        return Result.okOrFailed(projectPromiseFileService.deletePromiseFile(id));
    }

    @ApiOperation(value = "编辑/重新上传承诺文件")
    @ApiImplicitParam(name = "req", value = "承诺文件信息 ", required = true, dataType = "PromiseFileReq", paramType = "body")
    @PostMapping("/addOrUpdatePromiseFile")
    @RepeatSubmit
    public Result<Boolean> addOrUpdatePromiseFile(@RequestBody PromiseFileReq req) {
        if (null == req) {
            return Result.failed(MessageConstants.PARAMS_NOT_NULL);
        }
        return Result.okOrFailed(projectPromiseFileService.addOrUpdatePromiseFile(req));
    }

    @ApiOperation(value = "专家签署承诺文件")
    //@ApiImplicitParam(name = "req", value = "签署承诺文件请求信息 ", required = true, dataType = "PromiseFileSignReq", paramType = "body")
    @PostMapping("/sign")
    @RepeatSubmit
    public Result<Boolean> sign(MultipartFile file,PromiseFileSignReq req) {
        if (null == req) {
            return Result.failed(MessageConstants.PARAMS_NOT_NULL);
        }
        return Result.okOrFailed(promiseFileSignService.sign(file,req, getJwtUser()));
    }


    @ApiOperation(value = "获取项目承诺文件")
    @ApiImplicitParam(name = "req", value = "签署承诺文件请求信息 ", required = true, dataType = "PromiseFileSignReq", paramType = "body")
    @PostMapping("/listSign")
    public Result<List<ProjectPromiseFileVo>> listSign(@RequestBody PromiseFileSignReq req) {
        if (null == req) {
            return Result.failed(MessageConstants.PARAMS_NOT_NULL);
        }
        // 查询会议室关联专家参与的评审
        List<Long> projectIds = projectPromiseFileService.getProjectIdsByConferenceId(req.getConferenceId(), getJwtUser().getUserId());
        List<ProjectPromiseFileVo> projectPromiseFileVos = projectPromiseFileService.listSign(projectIds, getJwtUser().getUserId(), req.getConferenceId());
        return Result.ok(projectPromiseFileVos);
    }

    @ApiOperation(value = "下载承诺文件模板")
    @ApiImplicitParam(name = "req", value = "项目信息", required = true, dataType = "PromiseFileReq", paramType = "body")
    @PostMapping(value = "/downloadPromiseFile")
    public void downloadPromiseFile(@RequestBody PromiseFileReq req, HttpServletResponse response, HttpServletRequest request) {
        //projectPromiseFileService.downloadPromiseFile(req,request,response);
    }

    @PostMapping("/savePromiseFile")
    public Result<Void> savePromiseFile(@RequestBody PromiseFileSignReq req) {
        projectPromiseFileService.savePromiseFile(req.getFileOssId(),req.getPromiseFileId());
        return Result.ok();
    }





}
