package com.hzw.sunflower.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.controller.request.*;
import com.hzw.sunflower.controller.response.*;
import com.hzw.sunflower.dto.BondProjectDTO;
import com.hzw.sunflower.dto.UnreturnedBondDto;
import com.hzw.sunflower.entity.BondRefund;
import com.hzw.sunflower.entity.BondRefundDetails;
import com.hzw.sunflower.entity.JwtUser;
import com.hzw.sunflower.entity.condition.BankRateCondition;
import com.hzw.sunflower.entity.condition.BondProjectCondition;
import com.hzw.sunflower.entity.condition.BondRefundCondition;

import java.util.List;

/**
 * <p>
 * 保证金退还服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-27
 */
public interface BondRefundService extends IService<BondRefund> {


    /**
     * 保证金退款项目列表
     * @param condition
     * @return
     */
    IPage<BondProjectVO> bondProjectList(BondProjectCondition condition);

    /**
     * 保证金退还项目列表(全流程线下+线上归档项目)
     * @param condition
     * @return
     */
    IPage<BondProjectVO> bondProjectListOffline(BondProjectCondition condition);

    /**
     * 保证金退还标供应商列表
     * @param req
     * @param page
     * @return
     */
    List<BondSupplierInfoVo> bondSupplierList(ProjectSectionReq req, IPage<BondSupplierInfoVo> page);

    /**
     * 鼠标悬停查看供应商中标情况
     * @param sectionId  companyId
     * @return
     */
    BidWinSupplierVo getBidWinInfo(Long sectionId, Long companyId);

    /**
     * 线下项目判断中标信息
     * @param sectionId
     * @param companyId
     * @return
     */
    BidWinSupplierVo getBidWinInfoOffline(Long sectionId, Long companyId);

    /**
     * 退还申请
     * @param req
     * @return
     */
    Boolean saveApplyRefund(BondRefundFeq req);

    /**
     * 退还失败异常列表
     * @param condition
     * @return
     */
    IPage<RefundListVo> payFailList(BondProjectCondition condition);

    /**
     * 编辑行号信息
     * @param req
     * @return
     */
    Boolean saveCompanyBankInfo(List<BondCompanyBankReq> req);

    /**
     * 补全行号信息
     * @param req
     * @return
     */
    Result<String> complementBankInfo(List<BondCompanyBankReq> req);

    /**
     * 财务退还列表
     *    keywords "关键字：项目编号/项目名称/供应商名称"
     *    applyTime "申请时间"
     *    bankName
     *    type "0：全部 1：待处理 2：已处理")
     * @return
     */
    IPage<RefundListVo> bondRefundList(BondRefundCondition condition);

    /**
     * 银行利率分页列表
     * @param condition
     * @return
     */
    IPage<BankRateListVo> bankRateList(BankRateCondition condition);

    /**
     * 银行利率历史记录列表
     * @param bankType
     * @return
     */
    Result<List<BankRateHistoryListVo>> bankRateHistoryList(Integer bankType);

    /**
     * 审核接口
     * @param req
     * @param jwtUser
     * @return
     */
    Boolean updateExamineForRefund(ExamineReq req, JwtUser jwtUser);

    /**
     * 批量审核接口
     * @param reqList
     * @param jwtUser
     * @return
     */
    Result<BatchRefundRecordVo> batchExamineForRefund(List<ExamineReq> reqList, JwtUser jwtUser);

    /**
     * 再次付款接口
     * @param req
     * @return
     */
    boolean saveAgainRefund(RefundIdReq req, JwtUser jwtUser);

    /**
     * 线下退还
     * @param req
     * @return
     */
    Boolean saveOfflineRefund(BondRefundFeq req);

    /**
     * 收款流水退还列表
     * @param condition
     * @return
     */
    IPage<RefundListVo> bondWaterRefundList(BondRefundCondition condition);

    /**
     * 打印保证金退还表
     * @param condition
     * @return
     */
    List<BondProjectDTO> printBondRefundApply(BondRefundCondition condition);

    /**
     * 打印中标通知书存根
     * @param condition
     * @return
     */
    List<String> printBidWinNoticeStub(BondRefundCondition condition);


    /**
     * 校验状态
     * @param list
     * @return
     */
    public Integer judgeStatus(List<BondRefundDetails> list);

    /**
     * 转异常
     * @param req
     * @return
     */
    boolean saveToAbnormal(RefundIdReq req);

    /**
     * 查看流水是否已处于退还状态
     * @param waterIdList
     * @return
     */
    Boolean judgeWaterInReturn(List<Long> waterIdList);

    /**
     * 刷新主表退还状态
     * @param refundId
     * @return
     */
    void refreshBondRefundStatus(Long refundId);

    /**
     * 打印保证金退还表
     * @param req
     * @return
     */
    List<BondProjectDTO> printBondRefundApply2(RefundSectionReq req);

    /**
     * 打印中标通知书存根
     * @param req
     * @return
     */
    List<String> printBidWinNoticeStub2(RefundSectionReq req);

    /**
     * 查询未退保证金数据
     * @return
     */
    List<UnreturnedBondDto> findUnreturnedBond(String startTime, String endTime);


    /**
     * 获取银行类型
     * @param refundOpenBank
     * @param bussineCode
     * @return
     */
    Integer getBankType(String refundOpenBank,Integer bussineCode);

    /**
     * 批量审核
     * @param req
     * @param jwtUser
     * @return
     */
    //Map<String,Long>  updateBatchExamine(List<ExamineReq> req, JwtUser jwtUser);

    /**
     * 批量退回
     * @param req
     * @return
     */
    Result<BatchRefundRecordVo> applyRefundBatch(List<BondRefundFeq> req);

    /**
     * 补缴代理服务费
     * @param req
     * @return
     */
    Boolean saveAgencyFeeFile(BondAgencyFeeFileReq req);


    /**
     * 撤回保证金申请
     * @param req
     * @return
     */
    Result<String> withdrawRefund(BondWithdrawFeq req);
}
