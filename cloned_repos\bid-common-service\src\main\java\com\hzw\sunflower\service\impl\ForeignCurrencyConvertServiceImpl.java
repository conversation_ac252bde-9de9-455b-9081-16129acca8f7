package com.hzw.sunflower.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzw.sunflower.constant.constantenum.CurrencyConvertTypeEnum;
import com.hzw.sunflower.dao.ForeignCurrencyConvertMapper;
import com.hzw.sunflower.dto.CurrencyConvertDto;
import com.hzw.sunflower.entity.ForeignCurrencyConvert;
import com.hzw.sunflower.service.ForeignCurrencyConvertService;
import com.hzw.sunflower.service.ForeignCurrencyRateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * <p>
 * 外币转换表 服务实现类
 * </p>
 *
 * <AUTHOR> Gen
 * @since 2024-05-20
 */
@Service
public class ForeignCurrencyConvertServiceImpl extends ServiceImpl<ForeignCurrencyConvertMapper, ForeignCurrencyConvert> implements ForeignCurrencyConvertService {

    @Autowired
    private ForeignCurrencyRateService foreignCurrencyRateService;

    @Override
    public CurrencyConvertDto getCurrencyConvert(Long relationId, Integer type) {
        CurrencyConvertDto currencyConvertDto = new CurrencyConvertDto();
        LambdaQueryWrapper<ForeignCurrencyConvert> fcc = new LambdaQueryWrapper<>();
        fcc.eq(ForeignCurrencyConvert::getRelationId, relationId);
        fcc.eq(ForeignCurrencyConvert::getType, type).last("LIMIT 1");
        ForeignCurrencyConvert foreignCurrencyConvert = this.getOne(fcc);
        if(null!=foreignCurrencyConvert){
            BeanUtil.copyProperties(foreignCurrencyConvert,currencyConvertDto);
            //查询汇率凭证
            List<Long> currencyRateList = foreignCurrencyRateService.queryForeignCurrencyRate(foreignCurrencyConvert.getId());
            currencyConvertDto.setCurrencyRateList(currencyRateList);
        }else{
            return null;
        }
        return currencyConvertDto;
    }
}
