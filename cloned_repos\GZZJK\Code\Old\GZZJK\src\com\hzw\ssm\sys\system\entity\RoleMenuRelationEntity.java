package com.hzw.ssm.sys.system.entity;

import java.util.Date;

import com.hzw.ssm.fw.base.BaseEntity;

public class RoleMenuRelationEntity extends BaseEntity {

	/**
	 * 角色实体类
	 */
	private static final long serialVersionUID = 8042703708357357837L;

	private String relation_id;// 关系id
	private String role_id; // 角色id
	private Date menu_id;// 菜单id

	public String getRelation_id() {
		return relation_id;
	}

	public void setRelation_id(String relationId) {
		relation_id = relationId;
	}

	public String getRole_id() {
		return role_id;
	}

	public void setRole_id(String roleId) {
		role_id = roleId;
	}

	public Date getMenu_id() {
		return menu_id;
	}

	public void setMenu_id(Date menuId) {
		menu_id = menuId;
	}

}
