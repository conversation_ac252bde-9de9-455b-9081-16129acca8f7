package com.hzw.ssm.expert.entity;

import java.util.List;

import com.hzw.ssm.fw.base.BaseEntity;

/**
 * 专家评价信息表
 * 
 * <AUTHOR> @date 2014-10-17
 */
public class AppraiseInfo2 extends BaseEntity {
	private static final long serialVersionUID = 1L;

	private String appraise_info_id;// 主键

	private String appraise_column;// 评价列

	private String parent_id;// 父ID

	private Long sort;// 排序号

	private Long delete_flag;// 删除标识

	private List<AppraiseInfo2> childList;

	private double score;// 评分

	public String getAppraise_info_id() {
		return appraise_info_id;
	}

	public void setAppraise_info_id(String appraiseInfoId) {
		appraise_info_id = appraiseInfoId;
	}

	public String getAppraise_column() {
		return appraise_column;
	}

	public void setAppraise_column(String appraiseColumn) {
		appraise_column = appraiseColumn;
	}


	public String getParent_id() {
		return parent_id;
	}

	public void setParent_id(String parentId) {
		parent_id = parentId;
	}

	public Long getSort() {
		return sort;
	}

	public void setSort(Long sort) {
		this.sort = sort;
	}

	public List<AppraiseInfo2> getChildList() {
		return childList;
	}

	public void setChildList(List<AppraiseInfo2> childList) {
		this.childList = childList;
	}

	public double getScore() {
		return score;
	}

	public void setScore(double score) {
		this.score = score;
	}



}
