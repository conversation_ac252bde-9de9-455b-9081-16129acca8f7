package com.hzw.sunflower.util.excel;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.fastjson.JSONObject;
import com.hzw.sunflower.dto.BondWaterExcelDto;
import com.hzw.sunflower.entity.BondTemplate;
import com.hzw.sunflower.exception.ExceptionEnum;
import com.hzw.sunflower.exception.SunFlowerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import java.io.*;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationHandler;
import java.lang.reflect.Proxy;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date ：Created in 2022/7/27 16:06
 * @description：
 * @modified By：`
 * @version: 1.0
 */
@Slf4j
public class ExcelUtil {

    public static <T> Map<String, List<T>> read(InputStream inputStream, final Class<?> clazz, BondTemplate template) {
        try {
            dealDownloadExcelProperty(clazz, beanToMap(template));
        } catch (Exception e) {
            e.printStackTrace();
            throw new SunFlowerException(ExceptionEnum.ERROR_OPERATION, ExceptionEnum.ERROR_OPERATION.getMessage());
        }
        if (inputStream == null) {
            throw new SunFlowerException(ExceptionEnum.ERROR_OPERATION, "文件不存在");
        }
        ExcelListener<T> listener = new ExcelListener<>();
        // 这里需要指定读用哪个class去读，然后读取第一个sheet 文件流会自动关闭
        EasyExcel.read(inputStream, clazz, listener).sheet().headRowNumber(template.getHeadRow()).doRead();
        return listener.getRowMap();
    }

    /**
     * 自定义表头
     * @param detailClass
     * @param map
     * @throws NoSuchFieldException
     * @throws IllegalAccessException
     */
    private static void dealDownloadExcelProperty(Class detailClass, Map<String, Object> map) throws NoSuchFieldException, IllegalAccessException {
        for(Map.Entry entry : map.entrySet()){
            Field field = detailClass.getDeclaredField(entry.getKey().toString());
            ExcelProperty annotation = field.getAnnotation(ExcelProperty.class);
            InvocationHandler invocationHandler = Proxy.getInvocationHandler(annotation);
            Field memberValues = invocationHandler.getClass().getDeclaredField("memberValues");
            memberValues.setAccessible(true);
            Map memberValuesMap = (Map) memberValues.get(invocationHandler);
            memberValuesMap.put("value", new String[]{entry.getValue().toString()});
        }
        // 打印当前注解值
        getFieldAnnotationValue(detailClass);
    }

    public static void getFieldAnnotationValue(Class clazz) throws SecurityException {
        // 获取所有的字段
        Field[] fields = clazz.getDeclaredFields();
        for (Field f : fields) {
            // 判断字段注解是否存在
            boolean annotationPresent2 = f.isAnnotationPresent(ExcelProperty.class);
            if (annotationPresent2) {
                ExcelProperty name = f.getAnnotation(ExcelProperty.class);
                // 获取注解值
                String[] nameStr = name.value();
                System.out.println(f.getName() + ":" + nameStr[0]);
            }
        }
    }

    /**
     * 转换表头
     * @param template
     * @return
     */
    public static Map<String, Object> beanToMap (BondTemplate template) {
        BondTemplate temp = new BondTemplate();
        // 必填项
        temp.setDate(template.getDate());
        temp.setCompanyName(template.getCompanyName());
        temp.setCompanyAccount(template.getCompanyAccount());
        temp.setCompanyBankDeposit(template.getCompanyBankDeposit());
        temp.setAmount(template.getAmount());
        temp.setPostScript(template.getPostScript());
        // 非必填项字段特殊处理， 随意设置一个值防止注解名重复
        if (!StringUtils.isBlank(template.getWaterNumber())) {
            temp.setWaterNumber(template.getWaterNumber());
        } else {
            temp.setWaterNumber("waterNumber");
        }
        if (!StringUtils.isBlank(template.getCompanyBankCode())) {
            temp.setCompanyBankCode(template.getCompanyBankCode());
        } else {
            temp.setCompanyBankCode("companyBankCode");
        }
        if (!StringUtils.isBlank(template.getReceiveBank())) {
            temp.setReceiveBank(template.getReceiveBank());
        } else {
            temp.setReceiveBank("receiveBank");
        }
        if (!StringUtils.isBlank(template.getReceiveAcount())) {
            temp.setReceiveAcount(template.getReceiveAcount());
        } else {
            temp.setReceiveAcount("receiveAccount");
        }
        if (!StringUtils.isBlank(template.getTime())) {
            temp.setTime(template.getTime());
        } else {
            temp.setTime("time");
        }
        Map<String, Object> map = BeanUtil.beanToMap(temp, false, true);
        log.info("导入模板表头为：{}", map.toString());
        return map;
    }

    public static void main(String[] args) throws IOException {
        // {waterNumber=主机交易流水号, date=交易日期, time=交易时间, companyName=对方账号名称, companyAccount=对方账号, companyBankDeposit=对方开户行, amount=贷方发生额, postScript=摘要}
        BondTemplate template = new BondTemplate();
        template.setHeadRow(14);
//        template.setWaterNumber("主机交易流水号");
//        template.setDate("交易时间");
        FileInputStream fis = new FileInputStream(new File("D:\\temp\\中信银行.xlsx"));
        Map<String, List<BondWaterExcelDto>> result = ExcelUtil.read(fis, BondWaterExcelDto.class, template);
        System.out.println("成功：" + JSONObject.toJSONString(result.get("rows")));
        System.out.println("失败：" + JSONObject.toJSONString(result.get("failRows")));
    }

}
