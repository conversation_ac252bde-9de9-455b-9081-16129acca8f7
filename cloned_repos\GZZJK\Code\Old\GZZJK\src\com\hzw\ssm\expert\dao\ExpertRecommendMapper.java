package com.hzw.ssm.expert.dao;

import java.util.List;

import com.hzw.ssm.expert.entity.*;
import com.hzw.ssm.sys.user.entity.UserEntity;


/**
 * 专家推荐信息
 *
 * <AUTHOR> 2014-10-09
 */
public interface ExpertRecommendMapper {

    /**
     * 
     * 函数功能描述：分页查询计划
     * @param info
     * @return
     */
    List<ExpertRecommendEntity> queryPagePlanList(ExpertRecommendEntity info);
    /**
     * 
     * 函数功能描述：查询推荐记录
     * @param info
     * @return
     */
    List<ExpertRecommendEntity> queryRecommenderList(ExpertRecommendEntity info);
    /**
     * 
     * 函数功能描述：分页查询推荐专家
     * @param info
     * @return
     */
    List<ExpertRecommendEntity> queryPageExpertList(ExpertRecommendEntity info);
    /**
     * 
     * 函数功能描述：查询推荐专家统计
     * @param info
     * @return
     */
    List<ExpertRecommendEntity> queryPageExpertStaticsList(ExpertRecommendEntity info);
    /**
     * 
     * 函数功能描述：修改年度计划
     * @param info
     */
    void updatePlanById(ExpertRecommendEntity info);
    /**
     * 
     * 函数功能描述：新增年度计划
     * @param info
     */
    void addPlanById(ExpertRecommendEntity info);
    /**
     * 
     * 函数功能描述：修改推荐人记录
     * @param info
     */
    void updateRecommendById(ExpertRecommendEntity info);
    /**
     * 
     * 函数功能描述：删除推荐人记录
     * @param info
     */
    void removeRecommend(ExpertRecommendEntity info);
    /**
     * 
     * 函数功能描述：修改推荐人记录
     * @param info
     */
    void addRecommendById(ExpertRecommendEntity info);
    
    
    List<UserEntity> querySalesMan(UserEntity entity);
    

}
