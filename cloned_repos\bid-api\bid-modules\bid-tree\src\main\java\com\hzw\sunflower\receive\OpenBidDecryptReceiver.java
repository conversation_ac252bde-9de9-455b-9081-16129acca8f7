package com.hzw.sunflower.receive;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hzw.sunflower.constant.OpenConstants;
import com.hzw.sunflower.constant.constantenum.GenerateRuleEnum;
import com.hzw.sunflower.constant.constantenum.ResponseFileTypeEnum;
import com.hzw.sunflower.constant.constantnum.OpenStatusEnum;
import com.hzw.sunflower.controller.response.DecryptFileVo;
import com.hzw.sunflower.dto.BidOpenSupplierDTO;
import com.hzw.sunflower.entity.*;
import com.hzw.sunflower.service.*;
import com.hzw.sunflower.util.ImageTextUtil;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.Date;

/**
 * @Auther: cx
 * @Date: 2023/09/06 10:55
 * @Description: 供应商解密
 */
@Component
@Slf4j
//@RabbitListener(queues = "open_bid_decrypt")
public class OpenBidDecryptReceiver {

    @Autowired
    BidOpenSupplierService bidOpenSupplierService;

    @Autowired
    ApplyResponseFileService applyResponseFileService;

    @Autowired
    ProjectBidDocScheduleValueService projectBidDocScheduleValueService;

    @Autowired
    ProjectSectionScheduleService projectSectionScheduleService;

    @Autowired
    BidOpenService bidOpenService;



    @RabbitHandler
    public void process(Message message, String dtoJson, Channel channel) throws IOException {
        BidOpenSupplierDTO bidOpenSupplierDTO = JSONObject.parseObject(dtoJson,BidOpenSupplierDTO.class);
        //手动确认
        channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        if(null != bidOpenSupplierDTO.getOpenId()){
            decryptOpenFile(bidOpenSupplierDTO,message,channel);
        }
        if(null != bidOpenSupplierDTO.getBidId()){
            decryptBidFile(bidOpenSupplierDTO,message,channel);
        }
     }

    /**
     * 解密开标一览表
     * @param bidOpenSupplierDTO
     */
    private void decryptOpenFile(BidOpenSupplierDTO bidOpenSupplierDTO,Message message,Channel channel) throws IOException {
        // 判断解密文件方式 是解析还是拼接，拼接不进行文件解析操作
        Boolean scheduleflag = false; // true 为解析
        LambdaQueryWrapper<ProjectSectionSchedule> plqw = new LambdaQueryWrapper<>();
        plqw.eq(ProjectSectionSchedule::getProjectId,bidOpenSupplierDTO.getProjectId());
        plqw.eq(ProjectSectionSchedule::getSectionId,bidOpenSupplierDTO.getSectionId());
        //plqw.eq(ProjectSectionSchedule::getBidRound,bidOpenSupplierDTO.getBidRound());
        ProjectSectionSchedule psc = projectSectionScheduleService.getOne(plqw);
        if(null != psc && GenerateRuleEnum.JOIN.getType().equals(psc.getGenerateRule())){
            scheduleflag = true;
        }
        ProjectBidDocScheduleValue pbds = new ProjectBidDocScheduleValue();
        BidOpenSupplier b = new BidOpenSupplier();
        pbds.setProjectId(bidOpenSupplierDTO.getProjectId());
        pbds.setSectionId(bidOpenSupplierDTO.getSectionId());
        pbds.setOnlineConferenceId(bidOpenSupplierDTO.getOnlineConferenceId());
        pbds.setRetCode(1);
        //  供应商名称
        pbds.setCompanyName(bidOpenSupplierDTO.getCompnayName());
        pbds.setSupplierId(bidOpenSupplierDTO.getSectionId());
        pbds.setApplyId(bidOpenSupplierDTO.getApplyId());
        DecryptFileVo decryptFileVo = null;
        try{
            b.setId(bidOpenSupplierDTO.getId());
            ApplyResponseFile arf = new ApplyResponseFile();
            arf.setId(bidOpenSupplierDTO.getOpenId());
             decryptFileVo = applyResponseFileService.caDecrypt(bidOpenSupplierDTO.getOpenFileEncId(), bidOpenSupplierDTO.getOpenOssPwd());
            if(null!=decryptFileVo.getOssid()){
                arf.setFileOssIdDec(decryptFileVo.getOssid());
                // 验章签名
                arf.setVerifyResult(applyResponseFileService.verifySignature(decryptFileVo.getTemp(), bidOpenSupplierDTO.getOrganizationNum()));
                arf.setDecTime(new Date());
                applyResponseFileService.updateById(arf);
                b.setOpenDecrypt(OpenConstants.YES_DECRYPT);
                //  解密成功文件解析
                if(scheduleflag){
                    pbds.setTaskId(ImageTextUtil.analysis(decryptFileVo.getTemp().getPath()));
                }

            }else{
                b.setOpenDecrypt(OpenConstants.FAIL_DECRYPT);
            }
        }catch(Exception e){
            log.error("供应商开标一览表解密异常:{}",e.getMessage());
            //更新状态为解密失败，供应商手动去重新解密
            b.setOpenDecrypt(OpenConstants.FAIL_DECRYPT);
            //消息丢弃
            channel.basicReject(message.getMessageProperties().getDeliveryTag(),false);
        }finally {
            decryptFileVo.getTemp().delete();
        }
        if(scheduleflag) {
            projectBidDocScheduleValueService.save(pbds);
        }
        boolean flag = bidOpenSupplierService.updateById(b);
        if(flag){
            updateBidOpenDecryptStatus(bidOpenSupplierDTO);
        }

    }

    /**
     * 解密招标文件
     * @param bidOpenSupplierDTO
     */
    private void decryptBidFile(BidOpenSupplierDTO bidOpenSupplierDTO,Message message,Channel channel) throws IOException {
        BidOpenSupplier b = new BidOpenSupplier();
        DecryptFileVo decryptFileVo = null;
        try{
            b.setId(bidOpenSupplierDTO.getId());
            ApplyResponseFile arf = new ApplyResponseFile();
              arf.setId(bidOpenSupplierDTO.getBidId());
               arf.setFileOssIdDec(bidOpenSupplierDTO.getBidFileEncId());
               decryptFileVo = applyResponseFileService.caDecrypt(bidOpenSupplierDTO.getBidFileEncId(), bidOpenSupplierDTO.getBidOssPwd());
            if(null!=decryptFileVo.getOssid()){
                arf.setFileOssIdDec(decryptFileVo.getOssid());
                // 验章签名
                arf.setVerifyResult(applyResponseFileService.verifySignature(decryptFileVo.getTemp(), bidOpenSupplierDTO.getOrganizationNum()));
                arf.setDecTime(new Date());
                applyResponseFileService.updateById(arf);
            }
            //如果文件解密ossId还有空的，则说明未解密完
            LambdaQueryWrapper<ApplyResponseFile> reFile = new LambdaQueryWrapper<>();
            reFile.eq(ApplyResponseFile::getApplyId,bidOpenSupplierDTO.getApplyId());
            reFile.eq(ApplyResponseFile::getFileType, ResponseFileTypeEnum.RESPONSE_FILE.getType());
            reFile.isNull(ApplyResponseFile::getFileOssIdDec);
            if(applyResponseFileService.count(reFile) == 0){
                b.setBidDecrypt(OpenConstants.YES_DECRYPT);
            }
        }catch(Exception e){
            log.error("供应商招标文件解密异常:{}",e.getMessage());
            //更新状态为解密失败，供应商手动去重新解密
            b.setBidDecrypt(OpenConstants.FAIL_DECRYPT);
            //消息丢弃
            channel.basicReject(message.getMessageProperties().getDeliveryTag(),false);
        }finally {
            decryptFileVo.getTemp().delete();
        }
        boolean flag = bidOpenSupplierService.updateById(b);
        if(flag){
            updateBidOpenDecryptStatus(bidOpenSupplierDTO);
        }
    }


    /**
     * 更新总的解密状态及状态是够自动唱标
     * @param bidOpenSupplierDTO
     */
    private void updateBidOpenDecryptStatus(BidOpenSupplierDTO bidOpenSupplierDTO){
        LambdaQueryWrapper<ApplyResponseFile>  lodlq = new LambdaQueryWrapper<>();
        lodlq.eq(ApplyResponseFile::getApplyId,bidOpenSupplierDTO.getApplyId());
       //开标一览表和响应文件解密状态 更新解密时间和解密开始时间
        lodlq.isNotNull(ApplyResponseFile::getFileOssIdDec);
        long count = applyResponseFileService.count(lodlq);
        // 供应商已经全部解密 那么开标流程需要继续向下走
        if(count > 0){
            LambdaQueryWrapper<BidOpen> bolq = new LambdaQueryWrapper<>();
            bolq.eq(BidOpen::getOnlineConferenceId,bidOpenSupplierDTO.getOnlineConferenceId());
            BidOpen bidOpen = bidOpenService.getBaseMapper().selectOne(bolq);
            BidOpen updateBidOpen = new BidOpen();
            updateBidOpen.setId(bidOpen.getId());
            updateBidOpen.setStatus(OpenStatusEnum.SING.getType());
            bidOpenService.getBaseMapper().updateById(updateBidOpen);
        }
    }

}
