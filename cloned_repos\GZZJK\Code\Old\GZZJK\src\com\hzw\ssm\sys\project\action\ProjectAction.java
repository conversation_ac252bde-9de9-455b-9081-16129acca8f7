package com.hzw.ssm.sys.project.action;

import java.io.IOException;
import java.io.PrintWriter;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.text.SimpleDateFormat;
import java.util.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;

import net.sf.json.JSONArray;
import net.sf.json.JsonConfig;
import net.sf.json.util.PropertyFilter;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.struts2.ServletActionContext;
import org.apache.struts2.convention.annotation.Action;
import org.apache.struts2.convention.annotation.Namespace;
import org.apache.struts2.convention.annotation.ParentPackage;
import org.apache.struts2.convention.annotation.Result;
import org.apache.struts2.convention.annotation.Results;
import org.springframework.beans.factory.annotation.Autowired;

import com.hzw.ssm.date.service.ZJKCalendarService;
import com.hzw.ssm.expert.entity.Appraise;
import com.hzw.ssm.expert.entity.AppraiseInfo;
import com.hzw.ssm.expert.entity.AppraiseRemark;
import com.hzw.ssm.expert.entity.ExpertInfoEntity;
import com.hzw.ssm.expert.entity.ResultProjectExpertInfo;
import com.hzw.ssm.expert.entity.SpecialtyInfoEntity;
import com.hzw.ssm.expert.service.AppraiseService;
import com.hzw.ssm.expert.service.ExpertInfoService;
import com.hzw.ssm.fw.base.BaseAction;
import com.hzw.ssm.fw.util.CommUtil;
import com.hzw.ssm.fw.util.DateUtil;
import com.hzw.ssm.fw.util.HZWException;
import com.hzw.ssm.fw.util.MessageConstants;
import com.hzw.ssm.fw.util.SpringApplicationContext;
import com.hzw.ssm.fw.util.SysConstants;
import com.hzw.ssm.fw.util.SysConstants.DATA_SOURCE;
import com.hzw.ssm.sys.project.entity.ApplyRecordEntity;
import com.hzw.ssm.sys.project.entity.AppointsExpertsEntity;
import com.hzw.ssm.sys.project.entity.ConditionEntity;
import com.hzw.ssm.sys.project.entity.ConditionRecordEntity;
import com.hzw.ssm.sys.project.entity.DeleteRecord;
import com.hzw.ssm.sys.project.entity.ExtractDebarbEntity;
import com.hzw.ssm.sys.project.entity.ExtractRecordEntity;
import com.hzw.ssm.sys.project.entity.ProjectAuditEntity;
import com.hzw.ssm.sys.project.entity.ProjectEntity;
import com.hzw.ssm.sys.project.entity.ResultEntity;
import com.hzw.ssm.sys.project.service.AppointsExpertsService;
import com.hzw.ssm.sys.project.service.DebarbService;
import com.hzw.ssm.sys.project.service.ProjectService;
import com.hzw.ssm.sys.system.service.SmsRecordService;
import com.hzw.ssm.sys.template.entity.TemplateEntity;
import com.hzw.ssm.sys.template.service.TemplateService;
import com.hzw.ssm.sys.user.entity.UserEntity;
import com.hzw.ssm.sys.user.service.UserService;

@Namespace("/project")
@ParentPackage(value = "default")
@Results( { @Result(name = "init", location = "/jsp/project/addProject.jsp"),
	@Result(name = "toProjectList2", location = "/jsp/project/projectAuditByDirector.jsp"),
	@Result(name = "apply", location = "/jsp/project/applyPointToExpertsProManager.jsp"),
	@Result(name = "removeRecords", location = "/jsp/project/removeRecords.jsp"),
	@Result(name = "applyAgain", location = "/jsp/project/applyAgainExtract.jsp"),
	@Result(name = "majorStr", location = "/jsp/project/majorList.jsp"),
	@Result(name = "selectManager", location = "/jsp/project/selectManager.jsp"),
	@Result(name = "initList", location = "/jsp/project/projectInitList.jsp"),
	@Result(name = "queryWarnList", location = "/jsp/project/queryWarnList.jsp"),
	@Result(name = "resultProjectInitList", location = "/jsp/project/resultProjectInitList.jsp"),
	@Result(name = "projectDetail", location = "/jsp/project/showProjectDetail.jsp"),
	@Result(name = "toProjectAppointDetail", location = "/jsp/project/projectAppointDetail.jsp"),
	@Result(name = "toProjectAlreadyDetail", location = "/jsp/project/projectDetail.jsp"),
	@Result(name = "selectedRecords", location = "/jsp/project/selectedRecords.jsp"),
	@Result(name = "toAppraiseDetail", location = "/jsp/expert/appraiseDetail.jsp"),
	@Result(name = "pointTo", location = "/jsp/project/pointToExperts.jsp"),
	@Result(name = "showProjects", location = "/jsp/project/showProjects.jsp"),
	@Result(name = "projectAuditReason", location = "/jsp/project/projectAuditReason.jsp"),
	@Result(name = "projectUpdateEx", location = "/jsp/project/updateMajor.jsp")
})
public class ProjectAction extends BaseAction {
	private static final long serialVersionUID = -8823858495799309882L;
	private String isSave;//是否为保存
	private List<ExpertInfoEntity> expertList;//专家信息列表
	private ProjectEntity project;
	private ProjectEntity projectEntity;
	private ConditionRecordEntity conditionRecordEntity;
	private ProjectAuditEntity projectAuditEntity;
	private ResultEntity result;
	private ApplyRecordEntity apply;
	private List<ResultEntity> resultList;//专家抽取结果列表
	private List<List<ResultEntity>> extractList;//不同轮次抽取结果集
	private ConditionEntity conEntity;
	private ExpertInfoEntity expert;
	private List<ExpertInfoEntity> extractExpertList;//已抽取用于指定专家页面展示
	private String majorStr;//专业选择
	private String expertType;         // 以选中的评标专业
	private String message;//
	List<UserEntity> userList;
	private List<UserEntity> operatorList;//经办人列表
	private List<ProjectEntity> projectList;//专家抽取结果列表
	private List<ConditionEntity> conditionList;//专家抽取结果列表
	private List<TemplateEntity> templateEntityList ;//短信通知模板
	private List<TemplateEntity> templateSmsList ;//短信通知模板
	private List<DeleteRecord> deleteRecordList;
	private List<ProjectEntity> queryWarnList;
	/**
	 * 指定专家
	 */
	private AppointsExpertsEntity appointsEntity;
	private List<AppointsExpertsEntity> appointsList;
	private List<AppointsExpertsEntity> appointsExpertsList;

	private String extractResultId;

	private ExpertInfoEntity expertInfoEntity;

	/** 评分项 */
	private List<AppraiseInfo> appInfoList;

	private List<Appraise> appraiseList;

	/** 专家评价备注 */
	private AppraiseRemark appraiseRemark;

	/** 指定专家列表 */
	private List<ExpertInfoEntity> expertInfoList;

	/** 抽取记录 */
	private List<ExtractRecordEntity> exRecordList;


	private List<ExtractDebarbEntity> companyDebarbList;// 回避机构
	private List<ExtractDebarbEntity> extractDebarbList;// 回避专家
	/** 本地专家人数by条件 */
	private Integer localExpertCount;
	/** 国家级专家人数by条件 */
	private Integer seniorExpertCount;
	/** 删除记录原因*/
	private String reason;

	private boolean applyFlag=false;

	private String applyDate;

	private boolean isHoliday=false;

	private boolean isLastWorkDay=false;

	private Date bidDate;

	private String currDate2;
	private String paixu;
	private String queryCount;
	private Date bidStartTime;
	private String projectNo;

	private Date bidEndTime;

	private ResultProjectExpertInfo resultProjectExpertInfo;

	private Long maxSort;
	private List<ResultProjectExpertInfo> resultProjectExpertInfoList;
	@Autowired
	private ExpertInfoService expertInfoService;

	@Autowired
	private ProjectService projectService;

	@Autowired
	private AppointsExpertsService appointsExpertsService;
	@Autowired
	private AppraiseService appraiseService;

	@Autowired
	private UserService userService;

	@Autowired
	private ZJKCalendarService zjkCalendarService;

	@Autowired
	private SmsRecordService smsRecordService;

	@Autowired
	private TemplateService templateService;

	@Autowired
	private DebarbService debarbService;
	/**
	 * 初始化查询项目负责人所有等待抽取的项目
	 * @return
	 */
	@Action("initList")
	public String initList() {

		if(project==null){
			project=new ProjectEntity();
		}

		this.context();
		project.setPage(this.getPage());
		UserEntity user=(UserEntity)this.getRequest().getSession().getAttribute("userInfo");
		project.setCreate_id(user.getUser_id());
		Long status = project.getStatus();
		if (null != status && 3L == status)
		{
			project.setStatus(null);
			project.setStatus_("3, 10");
		}
		projectList = projectService.queryProjectList(project);
		project.setStatus(status);
		if(this.getRequest().getParameter("applyFlag")!=null){
			applyFlag=true;
		}
		return "initList";
	}


	@Action("init")
	public String init() {
		this.context();
		UserEntity user = null;
		if (null == project)
		{
			user = (UserEntity)this.getRequest().getSession().getAttribute("userInfo");
			project = new ProjectEntity();
			// 处室
			project.setDepartment(user.getDepartment());
			// 项目负责人
			project.setManager(user.getUser_id());
			project.setManagerName(user.getUser_name());
			// 经办人
			project.setOperator(user.getUser_id());
			project.setOperatorName(user.getUser_name());
		}
		else
		{
			user = new UserEntity();
			user.setDepartment(project.getDepartment());
		}
		//查询项目负责人
		userList=projectService.queryProjectManager(user);
		
		if (maxSort == null) {
			maxSort = 0L;
		}
		// 查询项目经办人
		/*UserEntity user = new UserEntity();
		user.setRole_name(SysConstants.ROLE_NAME.OPERATOR);
		operatorList = projectService.queryUserByRole(user);*/

		return "init";
	}


	/**
	 * 查询专家抽取审核列表
	 *
	 * @return
	 */
	@Action("queryAuditProjectListDR")
	public String queryProjectList2() {
		this.context();
		// 当前session
		HttpSession session = getRequest().getSession();
		// 当前用户信息
		UserEntity user = (UserEntity) session.getAttribute("userInfo");

		try {

			if (null == projectEntity){
				projectEntity = new ProjectEntity();
				projectEntity.setPage(this.getPage());

			}

			projectEntity.setStatus_("90,91,92");

			// 查询部门下所有用户ID
			UserEntity ue = new UserEntity();
			ue.setUser_id(user.getUser_id());
			List<UserEntity> lstUser = userService.getUserInfoByUserId(ue);
			if(lstUser != null && lstUser.size()>0){
				String createUsers = "'";
				for (int i = 0; i < lstUser.size(); i++) {
					createUsers+=lstUser.get(i).getUser_id()+"','";
					//createUsers.concat(lstUser.get(i).getUser_id()).concat("','");
				}
				createUsers = createUsers.substring(0, createUsers.length()-2);
				projectEntity.setCreateUsers(createUsers);
			}
			this.getRequest().setAttribute("flag", "2");
			projectList = projectService.queryPageProjectList(projectEntity, user);

		} catch (Exception e) {
			e.printStackTrace();
		}

		return "toProjectList2";
	}
	/**
	 * 提交到主任审核
	 * @return
	 */
	@Action("auditByDirector")
	public String auditByDirector(){

		this.context();
		// 当前session
		HttpSession session = getRequest().getSession();
		// 当前用户信息
		UserEntity user = (UserEntity) session.getAttribute("userInfo");
		try {
			boolean applyFlag =false;
			//2018/6/15将功能修改为流水号执行
			List<ProjectEntity> pList =  projectService.queryProjectListById(project);
			String reason = java.net.URLDecoder.decode(project.getApplyReason(), "utf-8");
			for(ProjectEntity entity : pList) {
				entity.setAuditType(project.getAuditType());
				int count=projectService.saveAuditByDirector(user,entity,reason);
				applyFlag=count>0?true:false;
			}
			//跳转到专家抽取申请页面
			this.getResponse().sendRedirect(this.getRequest().getContextPath()+"/project/initList?applyFlag="+applyFlag);
		} catch (IOException e) {
			e.printStackTrace();
		}
		return null;
	}

	/**
	 * 提交到主任审核
	 * @return
	 */
	@Action("auditAndConvert")
	public String auditAndConvert(){

		this.context();
		// 当前session
		HttpSession session = getRequest().getSession();
		// 当前用户信息
		UserEntity user = (UserEntity) session.getAttribute("userInfo");
		try {
			boolean applyFlag =false;
			//2018/6/15将功能修改为流水号执行
			List<ProjectEntity> pList =  projectService.queryProjectListById(project);
			String reason = java.net.URLDecoder.decode(project.getApplyReason(), "utf-8");
			for(ProjectEntity entity : pList) {
				entity.setAuditType(project.getAuditType());
				int count=projectService.saveAuditByDirector(user,entity,reason);
				applyFlag=count>0?true:false;
			}
			//根据查询条件ID查询专家抽取记录
			ConditionEntity conditionEntity = new ConditionEntity();
			conditionEntity.setDecimationBatch(project.getDecimationBatch());
			//获取当前流水号下的抽取信息
			List<ExpertInfoEntity> expertList =projectService.queryProjectExpertInfo(conditionEntity);

			//判断当前的批次号是否有抽取专家记录
			if(expertList !=null && expertList.size()>0){
				//将项目状态修改为人工抽取
				for(ProjectEntity pEntity :pList) {
					//将抽取记录表中留痕
					ConditionEntity conEntity = new ConditionEntity();
					//存在抽取记录表示，当前的项目是人数不足
					//将语音的状态留痕
					conEntity.setCurrentStatus(SysConstants.CONDITION_CURRENT_STATUS.METHOD_TWO);
					conEntity.setId(pEntity.getConditionId());
					projectService.updateConditionCurrentStatus(conEntity);
				}
				//跳转到专家抽取申请页面
				this.getResponse().sendRedirect(this.getRequest().getContextPath()+"/voiceProject/initList");
			}else{
				//将项目状态修改为人工抽取
				for(ProjectEntity pEntity :pList) {
					ConditionEntity conEntity = new ConditionEntity();
					//将项目抽取方式改成人工抽取
					conEntity.setMethod(SysConstants.CONDITION_METHOD.METHOD_ONE);
					//将语音的状态留痕
					conEntity.setCurrentStatus(SysConstants.CONDITION_CURRENT_STATUS.METHOD_TWO);
					conEntity.setId(pEntity.getConditionId());
					projectService.updateConditionCurrentStatus(conEntity);
				}
				//跳转到专家抽取申请页面
				this.getResponse().sendRedirect(this.getRequest().getContextPath()+"/project/initList?applyFlag="+applyFlag);
			}



		} catch (IOException e) {
			e.printStackTrace();
		}
		return null;
	}

	/**
	 * 检查当前是否为指定开标日期的前一个工作日或者是开标当天
	 * @return
	 */
	@Action("valApplyDate2")
	public String valApplyDate2() {
		this.context();
		String resStatus = "success";
		PrintWriter out = null;
		Date bidTime = null;
		try {
			out = this.getResponse().getWriter();

			List<ProjectEntity> pList = projectService.queryProjectListById(project);
			if (pList != null && !pList.isEmpty() && pList.size() > 0) {
				bidTime = pList.get(0).getBidTime();
			} else {
				throw new Exception("查询开标时间失败");
			}


			boolean flag = true;

			Calendar calendar = Calendar.getInstance();
			// 已经超过开标时间
//			if (bidTime.before(calendar.getTime())) {
//				flag = false;
//				resStatus = "timeOut";
//			}

			if (flag) {
				// 不在工作时间段
				int hour = calendar.get(Calendar.HOUR_OF_DAY);
				if (hour < 7 || hour > 21) {
					flag = false;
					resStatus = "notWorkTime";
				}
			}

			if (flag) {
				SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
				String bidTimeStr = sdf.format(bidTime);
				String nowStr = sdf.format(calendar.getTime());
				// 判断当前时间是否为节假日
				isHoliday = zjkCalendarService.isLastWorkDay(nowStr, bidTimeStr);
				if (!(nowStr.equals(bidTimeStr) || isHoliday)) {
					flag = false;
					resStatus = "notWorkDay";
				}
			}
			//判断当前的项目是不是系统自动抽取
			if(pList.get(0).getMethod().equals(SysConstants.CONDITION_METHOD.METHOD_THREE)){
				if("timeOut".equals(resStatus)){
					//已经过了开标时间需要主任审批。需判断是否存在抽取记录：如果无抽取记录，则直接转为
					resStatus ="auditAndConvert";
				}else{
					//判断是否是正常工作日及工作时间
					if(flag){
						//是正常工作日及工作时间
						//判断当前时间是否在开标前半个小时之内（未过开标时间）
						Calendar now = Calendar.getInstance();
						long afterTimeValue = now.getTimeInMillis() + 30*60*1000;
						Date afterTime = new Date(afterTimeValue);
						if(bidTime.after(now.getTime()) && bidTime.before(afterTime) ) {
							//直接继续应急抽取
							resStatus ="exigence";
						}
					}else{
						//非正常工作日或者非工作时间
						Calendar now = Calendar.getInstance();
						long afterTimeValue = now.getTimeInMillis() + 30*60*1000;
						Date afterTime = new Date(afterTimeValue);
						if(bidTime.after(now.getTime()) && bidTime.before(afterTime) ) {
							//在开标前半小时之内，需主任审批并转为
							resStatus ="auditAndConvert";
						}

					}
				}
			}else{
				/**
				 * 20200519 新功能  由业务员自己抽取
				 */
				// 非正常工作日或者非工作时间
				String auditType = SysConstants.AUDIT_TYPE.TYPE_1;
				// 判断当前项目是否已经有审核通过的记录
				ProjectAuditEntity projectAuditEntity = queryProAuditInfo(auditType);
				if(projectAuditEntity !=null){
					resStatus = "success";
				}

			}
			if(resStatus.equalsIgnoreCase("notWorkDay")||resStatus.equalsIgnoreCase("timeOut")){
				//判断项目>>>>>>>>>>>>>>过时直接通过
				resStatus = "success";
			}
		} catch (Exception e) {
			resStatus = "failed";
			e.printStackTrace();
		} finally {
			out.print(resStatus);
			out.close();
		}

		return "initList";
	}


	private ProjectAuditEntity queryProAuditInfo(String auditType) {
		ProjectAuditEntity projectAuditEntity = new ProjectAuditEntity();
		projectAuditEntity.setDecimationBatch(project.getDecimationBatch());
		projectAuditEntity.setAuditType(project.getAuditType());
		projectAuditEntity.setStatus(SysConstants.AUDIT_STATUS.STATUS_1);
		projectAuditEntity.setAuditType(auditType);
		ProjectAuditEntity auditEntity = projectService.getProAuditInfoByProIdToVoice(projectAuditEntity);

		return auditEntity;
	}
	/**
	 * 检查当前是否为指定开标日期的前一个工作日或者是开标当天(只适合用于语音)
	 * @return
	 */
	@Action("valApplyDate3")
	public String valApplyDate3() {
		this.context();
		String resStatus = "success";
		PrintWriter out = null;
		Date bidTime = null;
		try {
			out = this.getResponse().getWriter();

			List<ProjectEntity> pList = projectService.queryProjectListById(project);
			if (pList != null && !pList.isEmpty() && pList.size() > 0) {
				bidTime = pList.get(0).getBidTime();
			} else {
				throw new Exception("查询开标时间失败");
			}


			boolean flag = true;

			//获取当前时间
			Calendar cal = Calendar.getInstance();
			//在当前时间上减去2小时
			cal.add(Calendar.HOUR, -2);

			// 判断是否已经超过2个小时了
			if (bidTime.before(cal.getTime())) {
				flag = false;
				resStatus = "timeOut";
			}
			Calendar calendar = Calendar.getInstance();
			if (flag) {
				// 不在工作时间段
				int hour = calendar.get(Calendar.HOUR_OF_DAY);
				if (hour < 8 || hour > 18) {
					flag = false;
					resStatus = "notWorkTime";
				}
			}

			if (flag) {
				SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
				String bidTimeStr = sdf.format(bidTime);
				String nowStr = sdf.format(calendar.getTime());
				// 判断当前时间是否为节假日
				isHoliday = zjkCalendarService.isLastWorkDay(nowStr, bidTimeStr);
				if (!(nowStr.equals(bidTimeStr) || isHoliday)) {
					flag = false;
					resStatus = "notWorkDay";
				}
			}
			//判断当前的项目是不是系统自动抽取
			if(pList.get(0).getMethod().equals(SysConstants.CONDITION_METHOD.METHOD_THREE)){
				//判断开标时间是否在半个小时之前
				if("timeOut".equals(resStatus)){
					//已经过了开标时间需要主任审批。需判断是否存在抽取记录：如果无抽取记录，则直接转为
					resStatus ="auditAndConvert";
				}else{
					//判断是否是正常工作日及工作时间
					if(flag){
						//是正常工作日及工作时间
						//判断当前时间是否在开标前半个小时之内（未过开标时间）
						Calendar now = Calendar.getInstance();
						long afterTimeValue = now.getTimeInMillis() + 30*60*1000;
						Date afterTime = new Date(afterTimeValue);
						if(bidTime.after(cal.getTime()) && bidTime.before(afterTime) ) {
							//直接继续应急抽取
							resStatus ="exigence";
						}
					}else{
						//非正常工作日或者非工作时间
						//判断当前项目是否有抽取记录
						Calendar now = Calendar.getInstance();
						long afterTimeValue = now.getTimeInMillis() + 30*60*1000;
						Date afterTime = new Date(afterTimeValue);

						if(bidTime.after(now.getTime()) && bidTime.before(afterTime) ) {
							//在开标前半小时之内，需主任审批并转为
							resStatus ="auditAndConvert";
						}

					}
				}
			}
		} catch (Exception e) {
			resStatus = "failed";
			e.printStackTrace();
		} finally {
			out.print(resStatus);
			out.close();
		}

		return "initList";
	}
	/**
	 * 查询项目负责人
	 * @return
	 */
	@Action("projectManagerList")
	public String projectManagerList()
	{
		this.context();
		this.getResponse().setContentType("text/html;charset=UTF-8");
		StringBuffer result = new StringBuffer("[");
		UserEntity user = new UserEntity();
		user.setDepartment(project.getDepartment());
		userList=projectService.queryProjectManager(user);
		for (int i = 0; i < userList.size(); i++)
		{
			user = userList.get(i);
			if (0 < i)
			{
				result.append(",");
			}
			result.append("{\"user_id\":\"").append(user.getUser_id()).append("\", \"user_name\":\"").append(user.getUser_name()).append("\"}");
		}
		result.append("]");
		try {
			this.getResponse().getWriter().write(result.toString());
		} catch (IOException e) {
			e.printStackTrace();
		}
		return null;
	}


	/**
	 * 查询项目明细
	 *
	 * @return
	 */
	@Action("projectDetail")
	public String toProjectDetail() {
		Long method = project.getMethod();
		String returnStr = "projectDetail";       // -2 标注为项目详细信息
		//project = projectService.queryProjectById(project);
		projectList = projectService.queryProjectListById(project);
		project = projectList.get(0);
		projectEntity = project;
		projectEntity.setManagerName(project.getManager());
		projectEntity.setOperatorName(project.getOperator());
		if (null != method && -1L == method)
		{
			// -1L 标注为跳转到申请指定专家页面
			//跳转之前先查询当前项目指定专家库里状态
			AppointsExpertsEntity entity =  new AppointsExpertsEntity();
			entity.setDecimationBatch(project.getDecimationBatch());
			appointsList = appointsExpertsService.queryDateByDecimationBatch(entity);
			return "apply";
		}

		if (null != method && -3L == method)
		{
			// -3L 标注为跳转到删除记录页面
			return "removeRecords";
		}

		if (null == conEntity){
			conEntity = new ConditionEntity();
		}
		conEntity.setProjectId(project.getProjectId());
		conEntity=projectService.queryConditionById(conEntity);

		// 空 标注为项目指定专家详细信息页面
		if (null == method || 4L == method)
		{
			ConditionEntity conditionEntity = new ConditionEntity();
			conditionEntity.setProjectId(project.getProjectId());
			conditionEntity.setJoin_status(0L);     // 参标5AM0128
			expertInfoList = projectService.queryExtractedResult(conditionEntity);
			return "toProjectAppointDetail";
		}
		// 查询参评专家信息
		if (null != method && 4L != method && -2L != method) {
			ConditionEntity conditionEntity = new ConditionEntity();
			conditionEntity.setProjectId(project.getProjectId());
			conditionEntity.setJoin_status(0L);     // 参标
			expertInfoList = projectService.queryExtractedResult(conditionEntity);
			exRecordList = projectService.queryExtractRecordList(project.getProjectId());
			returnStr = "toProjectAlreadyDetail";
		}


		companyDebarbList = new ArrayList<ExtractDebarbEntity>();
		extractDebarbList = new ArrayList<ExtractDebarbEntity>();
		// 根据批次号查询回避信息
		ExtractDebarbEntity debarbEntity = new ExtractDebarbEntity();
		debarbEntity.setDecimationbatch(project.getDecimationBatch());
		List<ExtractDebarbEntity> debarbList = debarbService.queryExtractDebarbList(debarbEntity);
		if (debarbList != null && debarbList.size() > 0) {
			for (ExtractDebarbEntity debarb : debarbList) {
				if (SysConstants.DEBARB_TYPE.TYPE_1.equals(debarb.getDebarbType())) {
					debarb.setCompanyDebarbReason(debarb.getDebarbReason());
					companyDebarbList.add(debarb);
				} else {
					debarb.setExpertDebarbReason(debarb.getDebarbReason());
					extractDebarbList.add(debarb);

				}
			}
		}

		return returnStr;

	}

	/**
	 * 查询项目明细，确认抽取结果
	 *
	 * @return
	 */
	@Action("selectedRecords")
	public String selectedRecords() {
		Long method = project.getMethod();
		projectList = projectService.queryProjectListById(project);
		project = projectList.get(0);
		if (null == conEntity){
			conEntity = new ConditionEntity();
		}
		conEntity.setProjectId(project.getProjectId());
		conEntity=projectService.queryConditionById(conEntity);
		projectEntity = project;
		ConditionEntity conditionEntity = new ConditionEntity();
		conditionEntity.setProjectId(project.getProjectId());
		conditionEntity.setJoinStatus("0");    // 参标
		expertInfoList = projectService.queryExtractedResult(conditionEntity);
		// 空 标注为项目指定专家详细信息页面
		if (null == method || 4L == method)
		{
			//20200515指定专家由自己处理
			AppointsExpertsEntity entity = new AppointsExpertsEntity();
			entity.setDecimationBatch(projectEntity.getDecimationBatch());
			appointsExpertsList = appointsExpertsService.queryDateByDecimationBatch(entity);

			return "toProjectAppointDetail";
		}
		exRecordList = projectService.copyQueryExtractRecordList(project.getProjectId());
		//获取最大的抽取次数
		maxSort =projectService.getMaxSort(conEntity);
		if(exRecordList == null || exRecordList.isEmpty()){
			localExpertCount = projectService.queryExpertCountByRule(conEntity, 1);
			seniorExpertCount = projectService.queryExpertCountByRule(conEntity, 2);
		}
		
		SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		if("2".equals(conEntity.getMethod().toString())){// 短信抽取
			//conEntity.setSmsStr("您好：XXX，江苏省招标中心有限公司诚邀您参加【"+project.getProjectName()+"】评标，开标时间："+sdf.format(project.getBidTime())+"。回复：1-参加，2-不参加。");
			conEntity.setSmsStr("您好：XXX，江苏省招标中心有限公司诚邀您参加评标，开标时间："+sdf.format(project.getBidTime())+"，开标地点："+project.getBidAddress()+"，项目负责人："+project.getManager()+"，电话："+project.getPhone()+"。回复：1-参加，2-不参加。");
		}else{// 正常抽取
			String projectName =  "";
			for(ProjectEntity p: projectList) {
				projectName+=p.getProjectName()+",";
			}
			projectName=projectName.substring(0,projectName.length()-2);
			//conEntity.setSmsStr("您好：XXX，江苏省招标中心有限公司诚邀您参加【"+project.getProjectName()+"】评标，开标时间："+sdf.format(project.getBidTime())+",开标地点："+project.getBidAddress()+",项目负责人："+project.getManager()+",电话："+project.getPhone()+"");
			conEntity.setSmsStr("您好：XXX，江苏省招标中心有限公司诚邀您参加评标，抽取批次:"+project.getDecimationBatch()+"("+projectName+")"+",开标时间："+sdf.format(project.getBidTime())+"，开标地点："+project.getBidAddress()+"，项目负责人："+project.getManager()+"，电话："+project.getPhone());
		}
		//回避信息

		// 新增查询回避条件
		ExtractDebarbEntity debarbEntity = new ExtractDebarbEntity();
		debarbEntity.setDecimationbatch(projectEntity.getDecimationBatch());
		companyDebarbList = new ArrayList<ExtractDebarbEntity>();
		extractDebarbList = new ArrayList<ExtractDebarbEntity>();
		List<ExtractDebarbEntity> debarbList = debarbService.queryExtractDebarbList(debarbEntity);
		if (debarbList != null && debarbList.size() > 0) {
			for (ExtractDebarbEntity debarb : debarbList) {
				if (SysConstants.DEBARB_TYPE.TYPE_1.equals(debarb.getDebarbType())) {
					companyDebarbList.add(debarb);
				} else {
					extractDebarbList.add(debarb);

				}
			}
		}
		conEntity.setCompanyDebarbList(companyDebarbList);
		conEntity.setExtractDebarbList(extractDebarbList);

		return "selectedRecords";

	}

	/**
	 * 根据抽取条件验证是否存在满足条件的专家
	 * @return
	 */
	@Action("valExtractionExport")
	public String valExtractionExport()
	{
		this.context();
		this.getResponse().setCharacterEncoding("utf-8");

		UserEntity user = (UserEntity)this.getRequest().getSession().getAttribute("userInfo");
		PrintWriter out = null;
		boolean flage =false;
		List<ProjectEntity> pList = new ArrayList<ProjectEntity>();
		String msg = null;
		try {
			if(null!=project.getDecimationBatch() && !project.getDecimationBatch().isEmpty()) {
				pList = projectService.queryProjectListById(project);
			}
			
			//保存项目中的委托单位
			String tender ="";
			//判断当前操作是否是修改
			if(conEntity!=null && "update".equals(conEntity.getOptType())) {
				out = this.getResponse().getWriter();
				if(null != project.getTender() && !"".equals(project.getTender()) && project.getTender().indexOf(",") > -1){
					String[] tenderSplit = project.getTender().split(",");
					for (int i = 0; i < tenderSplit.length; i++) {
						tender +="'"+tenderSplit[i]+"',";
					}
					tender = tender.substring(0,tender.length()-1);
				}else{
					tender = "'"+project.getTender()+"'";
				}
				conEntity.setTender(tender);
				conEntity.setBidTimeDay(DateUtil.dateToString(project.getBidTime()));

				// 获取处理过的回避信息
				if (companyDebarbList == null) {
					companyDebarbList = new ArrayList<ExtractDebarbEntity>();
				}
				conEntity.setCompanyDebarbList(companyDebarbList);

				if (extractDebarbList == null) {
					extractDebarbList = new ArrayList<ExtractDebarbEntity>();
				}
				conEntity.setExtractDebarbList(extractDebarbList);

				msg = projectService.queryExpertToChecks(conEntity,pList);
			}else {
				if(pList.size()>0) {
					conEntity = new ConditionEntity();
					for(ProjectEntity entity:pList) {
						conEntity.setId(entity.getConditionId());
						//查询抽取条件
						conEntity = projectService.queryConditionById(conEntity);
						out = this.getResponse().getWriter();
						if(null != entity.getTender() && !"".equals(entity.getTender()) && entity.getTender().indexOf(",") > -1){
							String[] tenderSplit = entity.getTender().split(",");
							for (int i = 0; i < tenderSplit.length; i++) {
								tender +="'"+tenderSplit[i]+"',";
							}
							tender = tender.substring(0,tender.length()-1);
						}else{
							tender = "'"+entity.getTender()+"'";
						}
						conEntity.setBidTimeDay(DateUtil.dateToString(entity.getBidTime()));
					}
					conEntity.setTender(tender);

					// 新增查询回避条件
					ExtractDebarbEntity debarbEntity = new ExtractDebarbEntity();
					debarbEntity.setDecimationbatch(pList.get(0).getDecimationBatch());
					companyDebarbList = new ArrayList<ExtractDebarbEntity>();
					extractDebarbList = new ArrayList<ExtractDebarbEntity>();
					List<ExtractDebarbEntity> debarbList = debarbService.queryExtractDebarbList(debarbEntity);
					if (debarbList != null && debarbList.size() > 0) {
						for (ExtractDebarbEntity debarb : debarbList) {
							if (SysConstants.DEBARB_TYPE.TYPE_1.equals(debarb.getDebarbType())) {
								companyDebarbList.add(debarb);
							} else {
								extractDebarbList.add(debarb);

							}
						}
					}
					conEntity.setCompanyDebarbList(companyDebarbList);
					conEntity.setExtractDebarbList(extractDebarbList);
					msg = projectService.queryExpertToChecks(conEntity,pList);
				}else {
					out = this.getResponse().getWriter();
					if(null != project.getTender() && !"".equals(project.getTender()) && project.getTender().indexOf(",") > -1){
						String[] tenderSplit = project.getTender().split(",");
						for (int i = 0; i < tenderSplit.length; i++) {
							tender +="'"+tenderSplit[i]+"',";
						}
						tender = tender.substring(0,tender.length()-1);
					}else{
						tender = "'"+project.getTender()+"'";
					}
					conEntity.setBidTimeDay(DateUtil.dateToString(project.getBidTime()));
					conEntity.setTender(tender);

					//获取处理过的回避信息
					if(companyDebarbList==null){
						companyDebarbList = new ArrayList<ExtractDebarbEntity>();
					}
					conEntity.setCompanyDebarbList(companyDebarbList);

					if(extractDebarbList==null){
						extractDebarbList = new ArrayList<ExtractDebarbEntity>();
					}
					conEntity.setExtractDebarbList(extractDebarbList);
					pList.add(project);

					msg = projectService.queryExpertToChecks(conEntity,pList);

				}
			}

			if(!"success".equals(msg)) {
				if("faile".equals(msg)){
					//修改项目状态
					ProjectEntity projectEntity = new ProjectEntity();
					projectEntity.setStatus(SysConstants.PROJECT_STATUS.ALREADY);
					projectEntity.setDecimationBatch(project.getDecimationBatch());
					projectService.updateProjectStatus(projectEntity);
				}else{
					//判断当前用户是否是中心抽取人(当存在某个项目抽取未确认，而库里专家人数不足时，中心抽取人抽取需要提示哪个项目没有确认)
					if(user.getRole_id().equals(SysConstants.ROLE_ID.EXTRACT)) {
						List<ConditionEntity> conList=projectService.queryDecimationbatchByBidTime(conEntity);
						String tig = "，还有部分项目业务员未确认，抽取批次为：";
						if(conList.size()>0) {
							for(ConditionEntity con :conList) {
								tig +=con.getDecimationBatch()+",";
							}
							tig= tig.substring(0,tig.length()-1);
							msg=msg+tig;
						}
					}
				}

			}

			out.print(msg);

			out.close();
		} catch (Exception e) {
			if(e instanceof HZWException){
				out.print(e.getMessage());
			}
			else e.printStackTrace();
		}
		return null;
	}

	/**
	 * 删除记录
	 * @return
	 */
	@SuppressWarnings("null")
	@Action("deleteRecord")
	public String deleteRecord(){
		this.context();
		this.getResponse().setCharacterEncoding("utf-8");
		PrintWriter out = null;
		Integer codeNum =  null;
		try {
			this.getResponse().getWriter();
			DeleteRecord deleteRecord = new DeleteRecord();
			deleteRecord.setReason(reason);
			projectService.deleteRecordByProjectId(project,deleteRecord);
			out = this.getResponse().getWriter();
			codeNum = 0 ;
			out.print(codeNum);
			out.close();
		} catch (Exception e) {
			e.printStackTrace();
		}

		return null;
	}

	/**
	 * 保存项目信息
	 * @return
	 */
	@Action("saveProject")
	public String saveProject() {
		try {
			//声明要新增的项目List
			List<ProjectEntity> proList = new ArrayList<ProjectEntity>();
			//1、设置条件开始查询
			HttpServletRequest request = ServletActionContext.getRequest();
			//招标项目编号
			String projectNum = request.getParameter("tenderProjectNum");
			//招标项目名称
			String projectName = request.getParameter("tenderProjectName");
			String tenders = request.getParameter("tenders");
			//
			String[] tenderProjectNum =  projectNum.split(",");
			String[] tenderProjectName = projectName.split(",");
			String[] tender = tenders.split(",");
			for(int i =0;i<tenderProjectNum.length;i++){
				ProjectEntity newEntity = new ProjectEntity();
				SpringApplicationContext.copyProperties(project, newEntity); // 新对象赋值
				newEntity.setProjectNo(tenderProjectNum[i]);
				//String str=new String(tenderProjectName[i].getBytes("ISO-8859-1"),"UTF-8");
				newEntity.setProjectName(tenderProjectName[i]);
				newEntity.setTender(tender[i]);
				this.setOpaUserAndDate(newEntity);
				proList.add(newEntity);
			}

			List<ExtractDebarbEntity> extractDebarbEntityList = disposeDebarbData();

			conEntity.setSourceCode(DATA_SOURCE.ZJK);
			projectService.saveVoiceProjects(proList,conEntity,extractDebarbEntityList);
			this.setAlertMessage(MessageConstants.SAVE_SUCCESS);

			//TODO 发送短信内容待修改部分
			// 申请抽取之后向抽取人发送通知短信
			String saveProjectNo="";
			for(ProjectEntity entity:proList) {
				saveProjectNo+=entity.getProjectNo()+",";
			}
			if(saveProjectNo=="") {
				throw new HZWException("数据有误！请联系管理员！");
			}else {
				saveProjectNo.substring(0,saveProjectNo.length()-1);

			}

			// 申请抽取之后向抽取人发送通知短信
			if (1 == project.getStatus().intValue())
			{
				//TODO 给抽取人发短信功能展示开放
				//projectService.smsExtractMobile(proList,saveProjectNo);
			}

			/*if (1 == project.getStatus().intValue())
			{
				this.setOpaUserAndDate(project);
				projectService.smsExtractMobile(project);
			}*/
		} catch (Exception e) {
			e.printStackTrace();
			this.setAlertMessage(MessageConstants.SAVE_FAILED);
			return init();
		}
		project = null;
		return initList();
	}

	/**
	 * 展示修改项目信息页面
	 *
	 * @return
	 */
	@Action("showModifyProject")
	public String showModifyProject() {
		projectList = projectService.queryProjectListById(project);
		if(projectList!=null&&projectList.size()>0){
			project = projectList.get(0);
			projectList.remove(0);
			if (null == conEntity){
				conEntity = new ConditionEntity();
			}
			conEntity.setProjectId(project.getProjectId());
			if (null != conEntity.getId() && !conEntity.getId().isEmpty())
			{
				conEntity=projectService.queryConditionById(conEntity);
			}

			//判断当前项目是否是非人工通知
			if(conEntity.getMethod()!='1') {
				if(conEntity.getMethod()==SysConstants.CONDITION_METHOD.METHOD_THREE){
					//默认查询“系统自动语音抽取”模板,且有效的模板
					TemplateEntity entity =  new TemplateEntity();
					entity.setTemplateType(String.valueOf(SysConstants.CONDITION_METHOD.METHOD_THREE));
					entity.setIsValid(SysConstants.IS_VALID.VALID_ZERO);
					//非人工通知根据抽取方式查询模板信息
					templateEntityList = templateService.queryTemplateList(entity);

					//默认查询“语音短信”模板，且有效的模板
					entity.setTemplateType(String.valueOf(SysConstants.CONDITION_METHOD.METHOD_TWO));
					//非人工通知根据抽取方式查询模板信息
					templateSmsList = templateService.queryTemplateList(entity);
				}else{
					TemplateEntity entity =  new TemplateEntity();
					entity.setTemplateType(conEntity.getMethod().toString());
					//非人工通知根据抽取方式查询模板信息
					templateEntityList = templateService.queryTemplateList(entity);
				}
			}
			companyDebarbList = new ArrayList<ExtractDebarbEntity>();
			extractDebarbList = new ArrayList<ExtractDebarbEntity>();
			// 根据批次号查询回避信息
			ExtractDebarbEntity debarbEntity = new ExtractDebarbEntity();
			debarbEntity.setDecimationbatch(project.getDecimationBatch());
			List<ExtractDebarbEntity> debarbList = debarbService.queryExtractDebarbList(debarbEntity);
			if (debarbList != null && debarbList.size() > 0) {
				for (ExtractDebarbEntity debarb : debarbList) {
					if (SysConstants.DEBARB_TYPE.TYPE_1.equals(debarb.getDebarbType())) {
						debarb.setCompanyDebarbReason(debarb.getDebarbReason());
						companyDebarbList.add(debarb);
					} else {
						debarb.setExpertDebarbReason(debarb.getDebarbReason());
						extractDebarbList.add(debarb);

					}
				}
			}
			maxSort =  projectService.getMaxSort(conEntity);
			isSave="1";//更新操作
			return this.init();
		}else{
			return this.initList();
		}
	}

	/**
	 * 更新项目信息
	 * @return
	 */
	@Action("updateProject")
	public String updateProject() {
		try {

			//1、设置条件开始查询
			HttpServletRequest request = ServletActionContext.getRequest();
			//招标项目编号
			String tenderProjectIds = request.getParameter("tenderProjectIds");
			//招标项目名称
			String projectName = request.getParameter("tenderProjectName");
			//获取抽取批次
			String decimationBatch = request.getParameter("decimationBatch");
			String tenders = request.getParameter("tenders");
			//循环修改项目信息
			String[] tenderProjectId =  tenderProjectIds.split(",");
			String[] tenderProjectName = projectName.split(",");
			String[] tender = tenders.split(",");
			List<ProjectEntity> proList =  new ArrayList<ProjectEntity>();
			for(int i =0;i<tenderProjectId.length;i++){
				//String str=new String(tenderProjectName[i].getBytes("ISO-8859-1"),"UTF-8");
				ProjectEntity newEntity = new ProjectEntity();
				SpringApplicationContext.copyProperties(project, newEntity); // 新对象赋值
				newEntity.setProjectId(tenderProjectId[i]);
				newEntity.setProjectName(tenderProjectName[i]);
				newEntity.setTender(tender[i]);
				this.setOpaUserAndDate(newEntity);
				projectService.updateProject(newEntity);
				proList.add(newEntity);
				this.setAlertMessage(1 == newEntity.getStatus() ? MessageConstants.APPLY_SUCCESS : MessageConstants.SAVE_SUCCESS);
				/*// 申请抽取之后向抽取人发送通知短信
				if (1 == newEntity.getStatus().intValue())
				{
					projectService.smsExtractMobile(newEntity);
				}*/
			}

			//TODO 发送短信内容待修改部分
			// 申请抽取之后向抽取人发送通知短信
			String saveProjectName="";
			for(ProjectEntity entity:proList) {
				saveProjectName+=entity.getProjectNo()+",";
			}
			if(saveProjectName=="") {
				throw new HZWException("数据有误！请联系管理员！");
			}else {
				saveProjectName.substring(0,saveProjectName.length()-1);

			}
			// 申请抽取之后向抽取人发送通知短信
			if (1 == project.getStatus().intValue())
			{
				//TODO 给抽取人发短信功能展示开放
				//projectService.smsExtractMobile(proList,saveProjectName);
			}


			List<ExtractDebarbEntity> extractDebarbEntityList = disposeDebarbData();
			// 将原有的回避信息删除
			ExtractDebarbEntity extractDebarbEntity = new ExtractDebarbEntity();
			extractDebarbEntity.setDecimationbatch(decimationBatch);
			debarbService.deleteDebarbData(extractDebarbEntity);

			// 添加新的回避信息
			if (extractDebarbEntityList != null && extractDebarbEntityList.size() > 0) {
				// 保存回避条件
				for (ExtractDebarbEntity debarbEntity : extractDebarbEntityList) {
					debarbEntity.setDecimationbatch(decimationBatch);
					debarbEntity.setId(CommUtil.getKey());
					debarbService.saveDebarbData(debarbEntity);
				}
			}

			//循环修改条件信息
			project.setDecimationBatch(decimationBatch);
			List<ProjectEntity> list = projectService.queryConditionIdByBatch(project);
			for(ProjectEntity entity : list){
				conEntity.setId(entity.getConditionId());
				this.setOpaUserAndDate(conEntity);
				projectService.updateCondition(conEntity);
			}
			/**
			 * 20200522修改
			 * 业务员自己抽取 ，只有满足所有条件才允许修改
			 */
			if(1 == project.getStatus().intValue()){
				extractExperts(project.getDecimationBatch());
				project.setMethod(1L);
				return this.selectedRecords();
			}
			isSave="1";
		} catch (Exception e) {
			e.printStackTrace();
			this.setAlertMessage(1 == project.getStatus() ? MessageConstants.APPLY_FAILED : MessageConstants.SAVE_FAILED);
		}
		project = null;
		return initList();
	}
	/**
	 * 更新项目状态
	 * @return
	 */
	@Action("updateProjectStatus")
	public String updateProjectStatus(){
		try {
			if(project.getDecimationBatch()==null||project.getDecimationBatch()=="") {
				throw new HZWException("数据有误！请联系管理员！");
			}
			//2018/6/15将功能修改为流水号执行
			//记录当前批次号下的所有项目名称，用逗号隔开
			String  saveProjectNo ="";
			List<ProjectEntity> pList =  projectService.queryProjectListById(project);
			for(ProjectEntity entity :pList) {
				//project.setStatus(2L);//抽取中,即为申请成功
				entity.setStatus(project.getStatus());
				projectService.updateProjectStatus(entity);
				this.setAlertMessage(MessageConstants.APPLY_SUCCESS);
				saveProjectNo+=entity.getProjectNo()+",";
				// 获取项目信息
				//entity = projectService.queryProjectById(entity);

			}
			if(saveProjectNo=="") {
				throw new HZWException("数据有误！请联系管理员！");
			}else {
				saveProjectNo.substring(0,saveProjectNo.length()-1);

			}
			/**
			 * 20200511修改的功能
			 * 抽取人数不在由抽取人抽取，直接由自己抽取
			 * 如果是申请抽取，将自行抽取
			 */
			if(1 == project.getStatus().intValue()){
				extractExperts(project.getDecimationBatch());
				project.setMethod(1L);
				return this.selectedRecords();
			}
			// 申请抽取之后向抽取人发送通知短信
			if (1 == project.getStatus().intValue())
			{
				//TODO 给抽取人发短信功能展示开放
				//projectService.smsExtractMobile(pList,saveProjectNo);
			}
		} catch (Exception e) {
			e.printStackTrace();
			this.setAlertMessage(MessageConstants.APPLY_FAILED);
		}
		project=null;
		return this.initList();
	}

	/**
	 * 直接抽取专家
	 *
	 * @return
	 */
	@Action("queryContinueExtract")
	public String queryContinueExtract() {
		Long curr = System.currentTimeMillis();
		try {
			long currTime = System.currentTimeMillis();
			// 查询项目信息
			if (project.getDecimationBatch() != null && project.getDecimationBatch() != "") {
				// 根据批次号查询项目信息
				projectList = projectService.queryProjectListById(project);
				if (projectList != null && projectList.size() > 0) {
					ConditionEntity entity = new ConditionEntity();
					entity.setProjectId(projectList.get(0).getProjectId());
					List<ConditionEntity> conditionList = projectService.queryConditionList(entity);
					// 直接抽取专家
					getExpert(projectList.get(0), conditionList.get(0));
					/* } */
				} else {
					throw new Exception("查询项目列表失败");
				}
			} else {
				throw new Exception("无批次号，请查证后提交！");
			}
			System.err.println(System.currentTimeMillis()-currTime);
		} catch (Exception e) {
			e.printStackTrace();
		}
		project.setMethod(1L);
		return this.selectedRecords();
	}
	/**
	 * 跳转到设置抽取条件页面
	 * @return
	 */
	@Action("addCondition")
	public String addCondition(){
		this.setOpaUserAndDate(project);
		if("1".equals(isSave)){//已保存过，进行更新操作
			//projectService.updateProject(project);
		}else{
			//projectService.saveProject(project);
		}

		if(conEntity!=null&&conEntity.getId()!=null&&!"".equals(conEntity.getId())){
			conEntity=projectService.queryConditionById(conEntity);
			isSave="1";//抽取条件已保存过
		}
		else {
			conEntity=new ConditionEntity();
			conEntity.setMethod(null);
			conEntity.setProjectId(project.getProjectId());
		}
		return "condition";
	}

	/**
	 * 保存抽取条件
	 * @return
	 */
	@Action("saveCondition")
	public String saveCondition(){
		try {
			this.setOpaUserAndDate(conEntity);
			projectService.saveCondition(conEntity);
			this.setAlertMessage(MessageConstants.SAVE_SUCCESS);
		} catch (Exception e) {
			e.printStackTrace();
			this.setAlertMessage(MessageConstants.SAVE_FAILED);
		}
		return "condition";
	}
	/**
	 * 更新抽取条件
	 * @return
	 */
	@Action("updateCondition")
	public String updateCondition(){
		try {
			this.setOpaUserAndDate(conEntity);
			projectService.updateCondition(conEntity);
			this.setAlertMessage(MessageConstants.SAVE_SUCCESS);
		} catch (Exception e) {
			e.printStackTrace();
			this.setAlertMessage(MessageConstants.SAVE_FAILED);
		}
		return "condition";
	}

	/**
	 * 回退到项目信息添加页面
	 * @return
	 */
	@Action("backProject")
	public String backProject(){
		if(this.isCreateCondition()){//是否要保存或更新信息
			if(conEntity!=null&&conEntity.getId()!=null&&!"".equals(conEntity.getId())){//抽取条件是否已保存
				projectService.updateCondition(conEntity);//更新
			}else {
				projectService.saveCondition(conEntity);//未保存过,对已填写的内容进行保存
			}
		}
		project=new ProjectEntity();
		isSave="1";//项目信息已保存,用作信息保存
		project.setProjectId(conEntity.getProjectId());
		project=projectService.queryProjectById(project);
		return init();
	}



	//TODO

	/**
	 * 申请指定专家
	 * @return
	 */
	@Action("auditPointToExperts")
	public String auditPointToExperts(){
		this.setOpaUserAndDate(project);
		if("1".equals(isSave)){//已保存过，进行更新操作
			projectService.updateProject(project);
		}else{
			//声明要新增的项目List
			List<ProjectEntity> proList = new ArrayList<ProjectEntity>();
			//1、设置条件开始查询
			HttpServletRequest request = ServletActionContext.getRequest();
			//招标项目编号
			String projectNum = request.getParameter("tenderProjectNum");
			//招标项目名称
			String projectName = request.getParameter("tenderProjectName");
			String tenders = request.getParameter("tenders");
			String[] tenderProjectNum =  projectNum.split(",");
			String[] tenderProjectName = projectName.split(",");
			String[] tender = tenders.split(",");
			for(int i =0;i<tenderProjectNum.length;i++){
				ProjectEntity newEntity = new ProjectEntity();
				SpringApplicationContext.copyProperties(project, newEntity); // 新对象赋值
				newEntity.setProjectNo(tenderProjectNum[i]);
				//String str=new String(tenderProjectName[i].getBytes("ISO-8859-1"),"UTF-8");
				newEntity.setProjectName(tenderProjectName[i]);
				newEntity.setTender(tender[i]);
				this.setOpaUserAndDate(newEntity);
				proList.add(newEntity);
			}

			ConditionEntity con = new ConditionEntity();
			con.setMethod(4L);
			projectService.saveProjects(proList,con);
			project.setDecimationBatch(proList.get(0).getDecimationBatch());
			projectList = projectService.queryProjectListById(project);
		}
		projectEntity=project;
		return "apply";
	}
	/**
	 * 保存指定专家申请说明
	 * @return
	 */
	@Action("saveApplyInfo")
	public String saveApplyInfo(){
		this.context();
		// 当前session
		HttpSession session = getRequest().getSession();
		// 当前用户信息
		UserEntity user = (UserEntity) session.getAttribute("userInfo");
		this.setOpaUserAndDate(apply);

		//处理用户指定专家列表
		//根据批次号查询指定专家的最大抽取次数
		Integer con =appointsExpertsService.queryMaxCON(apply.getDecimationBatch());
		if(con==null) {
			con=0;
		}
		con +=1;
		//声明要新增的项目List
		List<AppointsExpertsEntity> appList = new ArrayList<AppointsExpertsEntity>();
		//1、设置条件开始查询 HttpServletRequest
		HttpServletRequest request = ServletActionContext.getRequest();
		String[] appiontsExpertsName =request.getParameterValues("expertsName");
		String[] appiontsExpertsCodeId = request.getParameterValues("expertsCodeId");

		for(int i =0;i<appiontsExpertsName.length;i++){
			AppointsExpertsEntity newEntity = new AppointsExpertsEntity();
			newEntity.setOfficialId(CommUtil.getKey());
			newEntity.setExpertsName(appiontsExpertsName[i]);
			newEntity.setExpertsCodeId(appiontsExpertsCodeId[i]);
			newEntity.setDecimationBatch(apply.getDecimationBatch());
			newEntity.setExtractCon(con);
			newEntity.setStatus(SysConstants.APPOINTS_EXPERTS_STAUS.SAVE);
			this.setOpaUserAndDate(newEntity);
			appList.add(newEntity);
		}
		projectService.copySaveApplyInfo(apply,user,appList);

		this.context();
		try {
			//跳转到专家抽取申请页面
			this.getResponse().sendRedirect(this.getRequest().getContextPath()+"/project/initList");
		} catch (IOException e) {
			e.printStackTrace();
		}
		return null;
	}
	/**
	 * 提交指定专家
	 * @return
	 */
	@Action("submitPointResult")
	public String submitPointResult(){
		this.context();
		this.getResponse().setContentType("text/html;charset=UTF-8");
		try {
			projectService.submitPointResult(result);
			this.getResponse().getWriter().write("success");
		} catch (Exception e) {
			try {
				this.getResponse().getWriter().write("no");
			} catch (IOException e1) {
				e1.printStackTrace();
			}
			e.printStackTrace();
		}
		return null;
	}

	/**
	 *  查询指定专家
	 * @return
	 */
	@Action("queryPagePointExperts")
	public String queryPagePointExperts(){
		conEntity.setPage(this.getPage());
		conEntity.getPage().setShowCount(5);
		expertList=projectService.queryPagePointExperts(conEntity);
		resultList=projectService.queryPointedExperts(conEntity);
		return "pointTo";
	}
	/**
	 *  删除指定专家
	 * @return
	 */
	@Action("deletePointResult")
	public String deletePointResult(){
		projectService.deletePointResult(result);
		return null;
	}

	/**
	 *  保存指定专家
	 * @return
	 */
	@Action("savePointExperts")
	public String savePointExperts(){
		projectService.savePointExperts(result);
		return null;
	}
	/**
	 * 申请再次抽取（超3次后）
	 * @return
	 */
	@Action("applyAgainExtract")
	public String applyAgainExtract(){
		conEntity=projectService.queryConditionById(conEntity);
		project=new ProjectEntity();
		project.setProjectId(conEntity.getProjectId());
		project=projectService.queryProjectById(project);
		return "applyAgain";
	}

	/**
	 * 保存再次抽取申请说明
	 * @return
	 */
	@Action("saveApplyAgainExtract")
	public String saveApplyAgainExtract(){
		this.setOpaUserAndDate(apply);
		this.context();
		try {
			projectService.saveApplyAgainExtract(apply);
			this.setAlertMessage("信息提交成功！");
			this.getResponse().sendRedirect(this.getRequest().getContextPath()+"/waitProject/queryProjectList");
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}

	/**
	 * 查询所有项目负责人
	 * @return
	 */
	@Action("queryProjectManager")
	public String queryProjectManager(){
		UserEntity user = new UserEntity();
		userList=projectService.queryProjectManager(user);
		return "selectManager";
	}
	/**
	 * 查询评标专业列表
	 * @return
	 */
	@Action("queryMajorList")
	public String queryMajorList(){
		List<SpecialtyInfoEntity> specialtyInfoList=expertInfoService.querySpecialtyInfoList();
		if (null != expertType && !expertType.isEmpty())
		{
			List<String> expertTypes = Arrays.asList(expertType.split(","));
			//设置默认选择的菜单，即已选中的评标专业
			for(SpecialtyInfoEntity specialtyInfo : specialtyInfoList)
			{
				if(expertTypes.contains(specialtyInfo.getSpe_id()))
				{
					specialtyInfo.setChecked(true);
					setOpen(specialtyInfoList, specialtyInfo.getSpe_parent());
					if (specialtyInfo.getSpe_level()!=null && 3 == specialtyInfo.getSpe_level().intValue())
					{
						specialtyInfo.setOpen(true);
						setOpenNodes(specialtyInfoList, specialtyInfo.getSpe_id());
					}
				}
			}
		}
		JsonConfig config=new JsonConfig();
		//通过JsonConfig的属性过滤器去除不需要的属性
		config.setJsonPropertyFilter(new PropertyFilter() {
			@Override
			public boolean apply(Object arg0, String arg1, Object arg2) {
				if(arg1.equals("spe_id")||arg1.equals("spe_parent")||arg1.equals("spe_name")
						||arg1.equals("checked")||arg1.equals("open")||arg1.equals("spe_level")){
					return false;//不忽略
				}
				return true;
			}
		});
		JSONArray jsonArray=JSONArray.fromObject(specialtyInfoList,config);
		majorStr=jsonArray.toString();
		//将属性名称修改zTree对应的属性名称
		majorStr=majorStr.replaceAll("spe_parent","pId").replaceAll("spe_name", "name").replaceAll("spe_id", "id");
		return "majorStr";
	}

	/**
	 * 打开已选中的父级节点
	 * @param specialtyInfoList
	 * @param speId
	 */
	private void setOpen(List<SpecialtyInfoEntity> specialtyInfoList, String speId)
	{
		for (SpecialtyInfoEntity specialtyInfoEntity : specialtyInfoList)
		{
			if (specialtyInfoEntity.getSpe_id().equals(speId))
			{
				specialtyInfoEntity.setOpen(true);
				specialtyInfoEntity.setChecked(true);
				if (!"-1".equals(specialtyInfoEntity.getSpe_parent()))
				{
					setOpen(specialtyInfoList, specialtyInfoEntity.getSpe_parent());
				}
				break;
			}
		}
	}
	/**
	 * 抽取下一批专家
	 * @param projectEntity
	 * @param conditionEntity
	 * @return
	 * @throws InterruptedException
	 */
	private List<ExpertInfoEntity> getExpert(ProjectEntity projectEntity,ConditionEntity conditionEntity) throws InterruptedException{
		//抽取专家
		List<ProjectEntity> projectList = projectService.queryProjectListById(projectEntity);
		String tender = "";
		for(ProjectEntity entity:projectList) {
			if(null != entity.getTender() && !"".equals(entity.getTender()) && entity.getTender().indexOf("、") > -1){
				String[] tenderSplit = entity.getTender().split("、");
				for (int i = 0; i < tenderSplit.length; i++) {
					tender +="'"+tenderSplit[i]+"',";
				}
			}else{
				tender += "'" + entity.getTender() + "',";
			}
			conditionEntity.setBidTimeDay(DateUtil.dateToString(entity.getBidTime()));
		}
		tender = tender.substring(0, tender.length() - 1);
		conditionEntity.setTender(tender);

		List<ExpertInfoEntity> expertInfoList = null;

		//给回避专家赋值 获取回避单位、回避专家
		ExtractDebarbEntity debarbEntity = new ExtractDebarbEntity();
		debarbEntity.setDecimationbatch(projectEntity.getDecimationBatch());
		//查询回避单位
		debarbEntity.setDebarbType(SysConstants.DEBARB_TYPE.TYPE_1);
		List<ExtractDebarbEntity> debarbCompyList = debarbService.queryExtractDebarbList(debarbEntity);
		if(debarbCompyList !=null && debarbCompyList.size()>0){
			conditionEntity.setCompanyDebarbList(debarbCompyList);
		}
		debarbEntity.setDebarbType(SysConstants.DEBARB_TYPE.TYPE_2);
		List<ExtractDebarbEntity> debarbExtractCodeList = debarbService.queryExtractDebarbList(debarbEntity);
		if(debarbExtractCodeList !=null && debarbExtractCodeList.size()>0){
			conditionEntity.setExtractDebarbList(debarbExtractCodeList);
		}
		return projectService.queryExpertsToExtraction(conditionEntity, projectList);

	}
	/**
	 * 打开已选中的子级节点
	 * @param specialtyInfoList
	 * @param speId
	 */
	private void setOpenNodes(List<SpecialtyInfoEntity> specialtyInfoList, String speId)
	{
		for (SpecialtyInfoEntity specialtyInfoEntity : specialtyInfoList)
		{
			if (specialtyInfoEntity.getSpe_parent().equals(speId))
			{
				specialtyInfoEntity.setOpen(true);
				specialtyInfoEntity.setChecked(true);
				if(null == specialtyInfoEntity.getSpe_level()){
					continue;
				}else if (3 > specialtyInfoEntity.getSpe_level())
				{
					setOpenNodes(specialtyInfoList, specialtyInfoEntity.getSpe_id());
				}
			}
		}
	}

	/**
	 * 抽取专家条件是否填写
	 * @return
	 */
	@SuppressWarnings("unchecked")
	private boolean isCreateCondition(){
		Class cl=ConditionEntity.class;
		Method[] ms =cl.getDeclaredMethods();//得到所有方法不含继承的方法
		String exclude=",getProvince,getCity,getZone,getTotal,getExpertType,getScore,getSeniorNum";//用于判断是否填写的字段.
		try {
			for(Method method:ms){
				if(method.getName().startsWith("get")&&exclude.contains(","+method.getName()+",")){
					Object obj=method.invoke(conEntity);
					if(obj!=null&&!obj.equals("")){
						return true;
					}
				}
			}
		} catch (SecurityException e) {
			e.printStackTrace();
		} catch (IllegalArgumentException e) {
			e.printStackTrace();
		} catch (IllegalAccessException e) {
			e.printStackTrace();
		} catch (InvocationTargetException e) {
			e.printStackTrace();
		}
		return false;
	}

	/**
	 * 查询项目退回原因
	 * @return
	 */
	@Action("projectReturnReason")
	public String projectReturnReason(){
		if(projectAuditEntity == null){
			projectAuditEntity = new ProjectAuditEntity();
		}
		projectAuditEntity = projectService.getProAuditInfoByProId(projectAuditEntity);
		return "projectAuditReason";
	}

	/**
	 * 查看专家评价信息
	 *
	 * @return
	 * @throws Exception
	 */
	@Action("toAppraiseDetail")
	public String toAppraiseDetail() throws Exception {
		// 专家信息
		expertInfoEntity = appraiseService.queryBaseAppraise(extractResultId);
		// 查询评分项
		appInfoList = appraiseService.queryAppraiseInfoById("0");
		// 查询评分
		appraiseList = appraiseService.queryAppraiseDetail(extractResultId,"0");
		// 评价备注
		appraiseRemark = appraiseService.queryAppraiseRemark(extractResultId);
		return "toAppraiseDetail";
	}


	/**
	 * 批量修改抽取专家结果
	 * @return
	 */
	@Action("submitResult")
	public String submitResult()
	{
		this.context();
		String msg = "fail";
		PrintWriter out = null;
		try {
			out = this.getResponse().getWriter();
			//提交之前先判断参标的专家是否开标当天都没有参标项目
			
			long curr=System.currentTimeMillis();
			List<ResultEntity> resultFList = projectService.queryNotNoticeExpert(resultList, conEntity);
			if(resultFList != null && resultFList.size()>0) {
				msg ="";
				//获取到专家名称
				for(ResultEntity re : resultFList) {
					msg+=re.getUserName()+",";
				}
				msg = msg.substring(0, msg.length()-1);
				msg+="专家已在开标当天被其他项目选定评标，请重新确认参标专家名单！";
			}else {
				msg =projectService.submitResultToVoice(resultList, conEntity);
			}
			out.print(msg);
			out.close();

			/*if(msg.equals("success")) {
				this.setAlertMessage(MessageConstants.SAVE_SUCCESS);
			}else {
				this.setAlertMessage(msg);
				project = new ProjectEntity();
				project.setDecimationBatch(conEntity.getDecimationBatch());
				return this.updateMajor();
			}
			 */
		} catch (Exception e) {
			e.printStackTrace();
			this.setAlertMessage(MessageConstants.SAVE_FAILED);
		}
		return null;
	}
	/**
	 * 再次申请
	 * @return
	 */
	@Action("updateMajor")
	public String updateMajor() {
		projectList = projectService.queryProjectListById(project);
		if(projectList!=null&&projectList.size()>0){
			project = projectList.get(0);
			projectList.remove(0);
			if (null == conEntity){
				conEntity = new ConditionEntity();
			}
			conEntity.setProjectId(project.getProjectId());
			if (null != conEntity.getId() && !conEntity.getId().isEmpty())
			{
				conEntity=projectService.queryConditionById(conEntity);
			}
			companyDebarbList =  new ArrayList<ExtractDebarbEntity>();
			extractDebarbList = new ArrayList<ExtractDebarbEntity>();
			//根据批次号查询回避信息
			ExtractDebarbEntity debarbEntity = new ExtractDebarbEntity();
			debarbEntity.setDecimationbatch(project.getDecimationBatch());
			List<ExtractDebarbEntity> debarbList = debarbService.queryExtractDebarbList(debarbEntity);
			if(debarbList!=null&&debarbList.size()>0){
				for(ExtractDebarbEntity debarb:debarbList){
					if(SysConstants.DEBARB_TYPE.TYPE_1.equals(debarb.getDebarbType())){
						debarb.setCompanyDebarbReason(debarb.getDebarbReason());
						companyDebarbList.add(debarb);
					} else {
						debarb.setExpertDebarbReason(debarb.getDebarbReason());
						extractDebarbList.add(debarb);

					}
				}
			}
			maxSort =  projectService.getMaxSort(conEntity);
			isSave="1";//更新操作
		}
		return "projectUpdateEx";
	}

	/**
	 * 向专家发送邀请短信
	 * @return
	 */
	@Action("smsExperts")
	public String smsExperts()
	{
		this.context();
		this.getResponse().setContentType("text/html;charset=UTF-8");
		try {
			if (null == conEntity)
			{
				this.getResponse().getWriter().write("fail");
				return null;
			}
			projectService.sendMessageForExperts(conEntity, expertList);
			this.getResponse().getWriter().write("success");
		} catch (Exception e) {
			if(e instanceof HZWException){
				this.setAlertMessage(e.getMessage());
			}
			e.printStackTrace();
			try {
				this.getResponse().getWriter().write("fail");
			} catch (IOException e1) {
				e1.printStackTrace();
			}
		}
		return null;
	}

	/**
	 * 处理回避数据
	 *
	 * @return
	 */
	private List<ExtractDebarbEntity> disposeDebarbData() {
		// 获取回避信息
		List<ExtractDebarbEntity> extractDebarbEntityList = new ArrayList<ExtractDebarbEntity>();

		if (companyDebarbList != null) {
			for (int i = 0; i < companyDebarbList.size(); i++) {
				if(companyDebarbList.get(i)!=null){
					ExtractDebarbEntity newEntity = new ExtractDebarbEntity();
					newEntity.setCompany(companyDebarbList.get(i).getCompany());
					newEntity.setDebarbReason(companyDebarbList.get(i).getCompanyDebarbReason());
					newEntity.setDebarbType(SysConstants.DEBARB_TYPE.TYPE_1);
					this.setOpaUserAndDate(newEntity);
					extractDebarbEntityList.add(newEntity);
				}

			}
		}

		if (extractDebarbList != null) {
			for (int i = 0; i < extractDebarbList.size(); i++) {
				if(extractDebarbList.get(i)!=null){
					ExtractDebarbEntity newEntity = new ExtractDebarbEntity();
					newEntity.setExpertCode(extractDebarbList.get(i).getExpertCode());
					newEntity.setExpertPhone(extractDebarbList.get(i).getExpertPhone());
					newEntity.setDebarbReason(extractDebarbList.get(i).getExpertDebarbReason());
					newEntity.setDebarbType(SysConstants.DEBARB_TYPE.TYPE_2);
					this.setOpaUserAndDate(newEntity);
					extractDebarbEntityList.add(newEntity);
				}
			}
		}
		return extractDebarbEntityList;
	}
	/**
	 * 根据抽取批次查询项目
	 * @return
	 */
	@Action("showProjects")
	public String showProjects()
	{
		try {
			projectList = projectService.queryProListByBatch(project);
			deleteRecordList = projectService.getDeleteRecordList();
			this.setAlertMessage(MessageConstants.SAVE_SUCCESS);

		} catch (Exception e) {
			e.printStackTrace();
			this.setAlertMessage(MessageConstants.SAVE_FAILED);
		}
		return "showProjects";
	}


	/**
	 * 该项目是否能被删除
	 * @return
	 */
	@Action("queryConfirmExpert")
	public void queryConfirmExpert()
	{
		this.context();
		this.getResponse().setContentType("text/html;charset=UTF-8");
		try {
			Integer count = projectService.queryConfirmExpertCount(conEntity);
			this.getResponse().getWriter().write(count.toString());
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	//数据统计分析
	@Action("queryAllProjectExtractedExperts")
	public String queryAllProjectExtractedExperts(){
		try{
			if(resultProjectExpertInfo==null){
				resultProjectExpertInfo=new ResultProjectExpertInfo();
			}
			this.context();
			resultProjectExpertInfo.setPage(this.getPage());
			resultProjectExpertInfoList =  projectService.queryPageAllProjectExtractedExperts(resultProjectExpertInfo);
		}catch (Exception e) {
			e.printStackTrace();
		}
		return "resultProjectInitList";
	}



	/**
	 * 跳转抽取专家页面
	 *
	 * @return
	 * @throws Exception
	 */
	private String extractExperts(String decimationBatch) throws Exception {
		String returnStr = "SUCCESS";
		project.setDecimationBatch(decimationBatch);
		// 根据批次号查询项目信息
		projectList = projectService.queryProjectListById(project);


		if (projectList != null && projectList.size() > 0) {
			ConditionEntity entity = new ConditionEntity();
			entity.setProjectId(projectList.get(0).getProjectId());
			List<ConditionEntity> conditionList = projectService.queryConditionList(entity);

			// 直接抽取专家
			getExpert(projectList.get(0), conditionList.get(0));
			/* } */
		} else {
			throw new Exception("批次号异常，请联系管理员");
		}

		return returnStr;
	}


	/**
	 * 变更抽取人数
	 * @return
	 */
	@Action("updateConditionPersonNum")
	public String updateConditionPersonNum(){
		this.context();
		String resStatus = "success";
		PrintWriter out = null;
		try {
			out = this.getResponse().getWriter();
			Integer oldTotal = conditionRecordEntity.getOldTotal();
			Integer newTotal = conditionRecordEntity.getNewTotal();
			Integer person = conditionRecordEntity.getPerson();
			if(null == oldTotal){
				oldTotal=0;
			}
			if(null == person){
				person=0;
			}
			String projectId = conditionRecordEntity.getProjectId();
			ConditionRecordEntity conditionRecordEntity = new ConditionRecordEntity();
			conditionRecordEntity.setOldTotal(oldTotal);
			conditionRecordEntity.setNewTotal(newTotal);
			conditionRecordEntity.setPerson(person);
			conditionRecordEntity.setProjectId(projectId);
			UserEntity user=(UserEntity)this.getRequest().getSession().getAttribute("userInfo");
			conditionRecordEntity.setCreate_id(user.getUser_id());
			conditionRecordEntity.setCreate_time(new Date());
			conditionRecordEntity.setDelete_flag(0);
			projectService.updateConditionPersonNum(conditionRecordEntity);
			//修改专家抽取信息
			projectService.updateConditionPerson(conditionRecordEntity);
		} catch (Exception e) {
			resStatus = "failed";
			e.printStackTrace();
		}finally {
			out.print(resStatus);
			out.close();
		}
		return null;
	}

	/**
	 * 获取专家同意参加的个数
	 * @return
	 */
	@Action("queryAgreeExpert")
	public String queryAgreeExpert(){
		this.context();
		String resStatus ="";
		PrintWriter out = null;
		try {
			out = this.getResponse().getWriter();
			ConditionEntity conditionEntity = new ConditionEntity();
			conditionEntity.setProjectId(conEntity.getProjectId());
			conditionEntity.setJoinStatus(conEntity.getJoinStatus());
			conditionEntity.setOrderFlag("1");
			expertInfoList = projectService.queryExtractedResult(conditionEntity);
			resStatus = String.valueOf(expertInfoList.size());
		} catch (Exception e) {
			resStatus = "failed";
			e.printStackTrace();
		}finally {
			out.print(resStatus);
			out.close();
		}
		return null;
	}


	/**
	 * 申请再次抽取（超3次后）
	 * @return
	 */
	/*@Action("applyAgainExtract")
	public String applyAgainExtract(){
		conEntity=projectService.queryConditionById(conEntity);
		project=new ProjectEntity();
		project.setProjectId(conEntity.getProjectId());
		project=projectService.queryProjectById(project);
		return "applyAgain";
	}*/

	//数据导出
	/**
	 * 抽取次数预警
	 * @return
	 */
	@Action("queryWarnList")
	public String queryWarnList() {
		if(project==null){
			project=new ProjectEntity();
		}
		try {
			project.setPage(this.getPage());
			if (null==project.getQueryCount()){
				queryCount="3";
				project.setQueryCount(Integer.valueOf(queryCount));
			}
			if (null==project.getBidStartTime() && null==project.getBidEndTime()){
				Date date = new Date();
				SimpleDateFormat format=new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
				project.setBidEndTime(format.parse(format.format(date)));

				Calendar calendar = Calendar.getInstance();
				calendar.setTime(date);
				calendar.add(Calendar.DAY_OF_MONTH, -30);
				String newDateTime = format.format(calendar.getTime());
				Date date1 = format.parse(newDateTime);
				project.setBidStartTime(date1);
			}

			if ("1".equals(project.getPaixu())){
				project.setPaixu("m.DECIMATIONBATCH asc");
			}else if ("2".equals(project.getPaixu())){
				project.setPaixu("m.DECIMATIONBATCH desc");
			}else if ("3".equals(project.getPaixu())){
				project.setPaixu("m.projectNo asc");
			}else if ("4".equals(project.getPaixu())){
				project.setPaixu("m.projectNo desc");
			}else if ("5".equals(project.getPaixu())){
				project.setPaixu("m.bidTime asc");
			}else if ("6".equals(project.getPaixu())){
				project.setPaixu("m.bidTime desc");
			}
			//根据抽取批次查询对应的项目信息
			queryWarnList = projectService.queryPageProjectInfo(project);
		}catch (Exception e){
			e.printStackTrace();
		}
		return "queryWarnList";
	}

	public Date getBidDate() {
		return bidDate;
	}

	public void setBidDate(Date bidDate) {
		this.bidDate = bidDate;
	}

	public String getProjectNo() {
		return projectNo;
	}

	public void setProjectNo(String projectNo) {
		this.projectNo = projectNo;
	}

	public String getPaixu() {
		return paixu;
	}

	public void setPaixu(String paixu) {
		this.paixu = paixu;
	}

	public Date getBidStartTime() {
		return bidStartTime;
	}

	public void setBidStartTime(Date bidStartTime) {
		this.bidStartTime = bidStartTime;
	}

	public Date getBidEndTime() {
		return bidEndTime;
	}
	public String getQueryCount() {
		return queryCount;
	}

	public void setQueryCount(String queryCount) {
		this.queryCount = queryCount;
	}
	public void setBidEndTime(Date bidEndTime) {
		this.bidEndTime = bidEndTime;
	}

	public ProjectEntity getProject() {
		return project;
	}

	public void setProject(ProjectEntity project) {
		this.project = project;
	}

	public ProjectEntity getProjectEntity() {
		return projectEntity;
	}
	public void setProjectEntity(ProjectEntity projectEntity) {
		this.projectEntity = projectEntity;
	}
	public String getIsSave() {
		return isSave;
	}

	public void setIsSave(String isSave) {
		this.isSave = isSave;
	}

	public ConditionEntity getConEntity() {
		return conEntity;
	}

	public void setConEntity(ConditionEntity conEntity) {
		this.conEntity = conEntity;
	}

	public List<ExpertInfoEntity> getExpertList() {
		return expertList;
	}

	public void setExpertList(List<ExpertInfoEntity> expertList) {
		this.expertList = expertList;
	}

	public ResultEntity getResult() {
		return result;
	}

	public void setResult(ResultEntity result) {
		this.result = result;
	}

	public List<ResultEntity> getResultList() {
		return resultList;
	}

	public void setResultList(List<ResultEntity> resultList) {
		this.resultList = resultList;
	}

	public List<List<ResultEntity>> getExtractList() {
		return extractList;
	}

	public void setExtractList(List<List<ResultEntity>> extractList) {
		this.extractList = extractList;
	}

	public ApplyRecordEntity getApply() {
		return apply;
	}

	public void setApply(ApplyRecordEntity apply) {
		this.apply = apply;
	}

	public ProjectService getProjectService() {
		return projectService;
	}

	public void setProjectService(ProjectService projectService) {
		this.projectService = projectService;
	}

	public ExpertInfoEntity getExpert() {
		return expert;
	}

	public void setExpert(ExpertInfoEntity expert) {
		this.expert = expert;
	}

	public List<ExpertInfoEntity> getExtractExpertList() {
		return extractExpertList;
	}

	public void setExtractExpertList(List<ExpertInfoEntity> extractExpertList) {
		this.extractExpertList = extractExpertList;
	}

	public String getMajorStr() {
		return majorStr;
	}

	public void setMajorStr(String majorStr) {
		this.majorStr = majorStr;
	}

	public String getExpertType() {
		return expertType;
	}

	public void setExpertType(String expertType) {
		this.expertType = expertType;
	}

	public String getMessage() {
		return message;
	}

	public void setMessage(String message) {
		this.message = message;
	}

	public List<UserEntity> getUserList() {
		return userList;
	}

	public void setUserList(List<UserEntity> userList) {
		this.userList = userList;
	}

	public List<UserEntity> getOperatorList() {
		return operatorList;
	}

	public void setOperatorList(List<UserEntity> operatorList) {
		this.operatorList = operatorList;
	}
	public List<ProjectEntity> getProjectList() {
		return projectList;
	}
	public void setProjectList(List<ProjectEntity> projectList) {
		this.projectList = projectList;
	}
	public List<ConditionEntity> getConditionList() {
		return conditionList;
	}
	public void setConditionList(List<ConditionEntity> conditionList) {
		this.conditionList = conditionList;
	}
	public List<ExpertInfoEntity> getExpertInfoList() {
		return expertInfoList;
	}
	public void setExpertInfoList(List<ExpertInfoEntity> expertInfoList) {
		this.expertInfoList = expertInfoList;
	}
	public List<ExtractRecordEntity> getExRecordList() {
		return exRecordList;
	}
	public void setExRecordList(List<ExtractRecordEntity> exRecordList) {
		this.exRecordList = exRecordList;
	}
	public ProjectAuditEntity getProjectAuditEntity() {
		return projectAuditEntity;
	}
	public void setProjectAuditEntity(ProjectAuditEntity projectAuditEntity) {
		this.projectAuditEntity = projectAuditEntity;
	}
	public String getExtractResultId() {
		return extractResultId;
	}
	public void setExtractResultId(String extractResultId) {
		this.extractResultId = extractResultId;
	}
	public ExpertInfoEntity getExpertInfoEntity() {
		return expertInfoEntity;
	}
	public void setExpertInfoEntity(ExpertInfoEntity expertInfoEntity) {
		this.expertInfoEntity = expertInfoEntity;
	}
	public List<AppraiseInfo> getAppInfoList() {
		return appInfoList;
	}
	public void setAppInfoList(List<AppraiseInfo> appInfoList) {
		this.appInfoList = appInfoList;
	}
	public List<Appraise> getAppraiseList() {
		return appraiseList;
	}
	public void setAppraiseList(List<Appraise> appraiseList) {
		this.appraiseList = appraiseList;
	}
	public AppraiseRemark getAppraiseRemark() {
		return appraiseRemark;
	}
	public void setAppraiseRemark(AppraiseRemark appraiseRemark) {
		this.appraiseRemark = appraiseRemark;
	}

	public Integer getLocalExpertCount() {
		return localExpertCount;
	}

	public void setLocalExpertCount(Integer localExpertCount) {
		this.localExpertCount = localExpertCount;
	}

	public Integer getSeniorExpertCount() {
		return seniorExpertCount;
	}

	public void setSeniorExpertCount(Integer seniorExpertCount) {
		this.seniorExpertCount = seniorExpertCount;
	}

	public String getReason() {
		return reason;
	}

	public void setReason(String reason) {
		this.reason = reason;
	}


	public boolean isApplyFlag() {
		return applyFlag;
	}


	public void setApplyFlag(boolean applyFlag) {
		this.applyFlag = applyFlag;
	}


	public String getApplyDate() {
		return applyDate;
	}


	public void setApplyDate(String applyDate) {
		this.applyDate = applyDate;
	}


	public boolean isHoliday() {
		return isHoliday;
	}


	public void setHoliday(boolean isHoliday) {
		this.isHoliday = isHoliday;
	}


	public ZJKCalendarService getZjkCalendarService() {
		return zjkCalendarService;
	}


	public void setZjkCalendarService(ZJKCalendarService zjkCalendarService) {
		this.zjkCalendarService = zjkCalendarService;
	}


	public boolean isLastWorkDay() {
		return isLastWorkDay;
	}


	public void setLastWorkDay(boolean isLastWorkDay) {
		this.isLastWorkDay = isLastWorkDay;
	}




	public String getCurrDate2() {
		return currDate2;
	}


	public void setCurrDate2(String currDate2) {
		this.currDate2 = currDate2;
	}


	public AppointsExpertsEntity getAppointsEntity() {
		return appointsEntity;
	}


	public void setAppointsEntity(AppointsExpertsEntity appointsEntity) {
		this.appointsEntity = appointsEntity;
	}


	public List<AppointsExpertsEntity> getAppointsList() {
		return appointsList;
	}


	public void setAppointsList(List<AppointsExpertsEntity> appointsList) {
		this.appointsList = appointsList;
	}


	public List<TemplateEntity> getTemplateEntityList() {
		return templateEntityList;
	}


	public void setTemplateEntityList(List<TemplateEntity> templateEntityList) {
		this.templateEntityList = templateEntityList;
	}


	public ResultProjectExpertInfo getResultProjectExpertInfo() {
		return resultProjectExpertInfo;
	}


	public void setResultProjectExpertInfo(ResultProjectExpertInfo resultProjectExpertInfo) {
		this.resultProjectExpertInfo = resultProjectExpertInfo;
	}


	public List<ResultProjectExpertInfo> getResultProjectExpertInfoList() {
		return resultProjectExpertInfoList;
	}


	public void setResultProjectExpertInfoList(List<ResultProjectExpertInfo> resultProjectExpertInfoList) {
		this.resultProjectExpertInfoList = resultProjectExpertInfoList;
	}


	public List<ExtractDebarbEntity> getCompanyDebarbList() {
		return companyDebarbList;
	}


	public void setCompanyDebarbList(List<ExtractDebarbEntity> companyDebarbList) {
		this.companyDebarbList = companyDebarbList;
	}


	public List<ExtractDebarbEntity> getExtractDebarbList() {
		return extractDebarbList;
	}


	public void setExtractDebarbList(List<ExtractDebarbEntity> extractDebarbList) {
		this.extractDebarbList = extractDebarbList;
	}


	public List<AppointsExpertsEntity> getAppointsExpertsList() {
		return appointsExpertsList;
	}


	public void setAppointsExpertsList(List<AppointsExpertsEntity> appointsExpertsList) {
		this.appointsExpertsList = appointsExpertsList;
	}


	public ConditionRecordEntity getConditionRecordEntity() {
		return conditionRecordEntity;
	}


	public void setConditionRecordEntity(ConditionRecordEntity conditionRecordEntity) {
		this.conditionRecordEntity = conditionRecordEntity;
	}

	public List<ProjectEntity> getQueryWarnList() {
		return queryWarnList;
	}

	public void setQueryWarnList(List<ProjectEntity> queryWarnList) {
		this.queryWarnList = queryWarnList;
	}


	public Long getMaxSort() {
		return maxSort;
	}


	public void setMaxSort(Long maxSort) {
		this.maxSort = maxSort;
	}
	
}
