package com.hzw.sunflower.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzw.sunflower.constant.constantenum.AppReturnListEnum;
import com.hzw.sunflower.constant.constantenum.ProcessRecordEnum;
import com.hzw.sunflower.constant.constantenum.ReturnPendingEnum;
import com.hzw.sunflower.controller.request.PendingItemReq;
import com.hzw.sunflower.dao.PendingItemMapper;
import com.hzw.sunflower.dto.ProcessRecordDTO;
import com.hzw.sunflower.entity.PendingItem;
import com.hzw.sunflower.freesia.client.constant.FlowClientConstant;
import com.hzw.sunflower.service.CommonMqService;
import com.hzw.sunflower.service.PendingItemService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 待处理事项表 服务实现类
 * </p>
 *
 * <AUTHOR> Gen
 * @since 2022-12-15
 */
@Service
public class PendingItemServiceImpl extends ServiceImpl<PendingItemMapper, PendingItem> implements PendingItemService {

    @Autowired
    private PendingItemMapper pendingItemMapper;

    @Autowired
    private CommonMqService commonMqService;

    /**
     * 添加待处理表数据
     * @param dto
     */
    @Override
    public void addPendingItem(ProcessRecordDTO dto) {
        PendingItem pendingItem = new PendingItem();
        // 更正公告退回
        if (dto.getBusinessCode().equals(ProcessRecordEnum.SUPPLEMENT_BULLETIN.getType())) {
            pendingItem = pendingItemMapper.getSupplementBulletin(dto.getBusinessId());
        }
        // 澄清修改退回
        if (dto.getBusinessCode().equals(ProcessRecordEnum.CLARIFY_REPLY_NOTICE.getType())) {
            pendingItem = pendingItemMapper.getQuestionReply(dto.getBusinessId());
        }
        // 异议回复退回
        if (dto.getBusinessCode().equals(ProcessRecordEnum.OBJECTION_NOTICE.getType())) {
            pendingItem = pendingItemMapper.getProposeObjection(dto.getBusinessId());
        }
        // 中标候选人公示退回/中标结果公示退回
        if (dto.getBusinessCode().equals(ProcessRecordEnum.BID_WIN_BULLETIN.getType())) {
            pendingItem = pendingItemMapper.getWinBulletin(dto.getBusinessId());
            if (pendingItem == null) {
                pendingItem = pendingItemMapper.getWinBulletinResult(dto.getBusinessId());
            }
        }
        // 申请中标通知书退回/重新申请中标通知书退回/申请资格预审通知书退回
        if (dto.getBusinessCode().equals(ProcessRecordEnum.BID_WIN_NOTICE.getType())) {
            pendingItem = pendingItemMapper.getWinNotice(dto.getBusinessId());
            // 重新申请中标通知书退回
            if (pendingItem == null) {
                pendingItem = pendingItemMapper.getWinNoticeNew(dto.getBusinessId());
            }
            // 申请资格预审通知书退回
            if (pendingItem == null) {
                pendingItem = pendingItemMapper.getBidWinNoticePre(dto.getBusinessId());
            }
            // 重新申请资格预审通知书退回
            if (pendingItem == null) {
                pendingItem = pendingItemMapper.getBidWinNoticePreNew(dto.getBusinessId());
            }
        }
        // 确认评标结果退回资格预审/确认评审结果退回后审
        if (dto.getBusinessCode().equals(ProcessRecordEnum.BID_WIN_PEOPLE.getType())) {
            pendingItem = pendingItemMapper.getBidWinPeople(dto.getBusinessId());
            if (pendingItem == null) {
                pendingItem = pendingItemMapper.getBidWinPeopleHou(dto.getBusinessId());
            }
        }
        // 异常公告退回
        if (dto.getBusinessCode().equals(ProcessRecordEnum.EXCEPT_NOTICE.getType())) {
            pendingItem = pendingItemMapper.getExceptNotice(dto.getBusinessId());
        }
        // 归档退回

        // 材料申请退回

        // 用锁申请退回

        // 换专票申请退回
        if (dto.getBusinessCode().equals(FlowClientConstant.CHANGE_TICKET)) {
            pendingItem = pendingItemMapper.getPayAll(dto.getBusinessId());
        }
        // 退标书费申请退回
        if (dto.getBusinessCode().equals(FlowClientConstant.BSF_REFUND)) {
            pendingItem = pendingItemMapper.getPayAllTenderFees(dto.getBusinessId());
        }
        // 登记供应商退回
        if (dto.getBusinessCode().equals(FlowClientConstant.SUPPLIER_REGISTER_APPLY)) {
            pendingItem = pendingItemMapper.getSupplierRegister(dto.getBusinessId());
        }
        // 补录付款凭证退回
//        if (dto.getBusinessCode().equals("APPLY_PAY_FILE")) {
//            pendingItem = pendingItemMapper.getApplyPayFile(dto.getBusinessId());
//        }
        // 代理服务费修改退回
        if (dto.getBusinessCode().equals(FlowClientConstant.AGENCY_FEE_APPLY)) {
            pendingItem = pendingItemMapper.getAgencyFeeApply(dto.getBusinessId());
        }
        if (dto.getBusinessCode().equals(FlowClientConstant.DATA_UPDATE_ONE)) {
            pendingItem = pendingItemMapper.getDataUpdate(dto.getBusinessId());
        }
        // 保证金退还退回(线下)
        if (dto.getBusinessCode().equals(ProcessRecordEnum.RETURN_BOND.getType())) {
            pendingItem = pendingItemMapper.getBondApplyReturn(dto.getBusinessId());
        }
        // 专家抽取退回
        if (dto.getBusinessCode().equals(FlowClientConstant.EXPERT_EXTRACTION)) {
            pendingItem = pendingItemMapper.getExpertExtraction(dto.getBusinessId());
        }
//        // 专家抽取备案退回
//        if (dto.getBusinessCode().equals("EXPERT_EXTRACTION_BEIAN")) {
//            pendingItem = pendingItemMapper.getExpertExtractionBeian(dto.getBusinessId());
//        }
        if (pendingItem != null && pendingItem.getBusinessCode() != null) {
            pendingItem.setOperationTime(new Date());
            pendingItem.setBusinessType(2);
            this.save(pendingItem);

            // 发送app待阅
            AppReturnListEnum returnListEnum = AppReturnListEnum.getByCode(pendingItem.getBusinessCode());
            if (returnListEnum != null) {
                commonMqService.sendOaReadMsg(returnListEnum.getDesc(), pendingItem);
            }
        }
    }

    /**
     * 删除待处理表数据
     * @param dto
     */
    @Override
    public void deletePendingItem(ProcessRecordDTO dto) {
        LambdaQueryWrapper<PendingItem> pendingItemLambdaQueryWrapper = new LambdaQueryWrapper<>();
        List<String> businessCodes = new ArrayList<>();
        if (dto.getBusinessCode().equals("supplement")) {
            businessCodes.add(ReturnPendingEnum.SUPPLEMENT_BID.getCode());
        }
        // 澄清修改退回
        if (dto.getBusinessCode().equals("clarifyReply")) {
            businessCodes.add(ReturnPendingEnum.CLARIFY_BID.getCode());
        }
        // 异议回复退回
        if (dto.getBusinessCode().equals("objection")) {
            businessCodes.add(ReturnPendingEnum.OBJECTION.getCode());
        }
        // 中标候选人公示退回/中标结果公示退回
        if (dto.getBusinessCode().equals("winBULLETIN")) {
            businessCodes.add(ReturnPendingEnum.WIN_CANDIDATE_BID.getCode());
            businessCodes.add(ReturnPendingEnum.WIN_BID.getCode());
        }
        // 申请中标通知书退回/重新申请中标通知书退回/申请资格预审通知书退回
        if (dto.getBusinessCode().equals("winNotice")) {
            businessCodes.add(ReturnPendingEnum.WIN_NOTICE.getCode());
            businessCodes.add(ReturnPendingEnum.RE_WIN_NOTICE.getCode());
            businessCodes.add(ReturnPendingEnum.BID_WIN_NOTICE_PRE.getCode());
            businessCodes.add(ReturnPendingEnum.RE_BID_WIN_NOTICE_PRE.getCode());
        }

        // 确认评标结果退回资格预审/确认评审结果退回后审
        if (dto.getBusinessCode().equals("bidwinpeople")) {
            businessCodes.add(ReturnPendingEnum.WINNING_BID_CANDIDATE_ZGYS.getCode());
            businessCodes.add(ReturnPendingEnum.WINNING_BID_CANDIDATE.getCode());
        }
        // 异常公告退回
        if (dto.getBusinessCode().equals("exceptNoticeLc")) {
            businessCodes.add(ReturnPendingEnum.ABNORMAL_NOTICE.getCode());
        }
        // 归档退回

        // 材料申请退回

        // 用锁申请退回

        // 换专票申请退回
        if (dto.getBusinessCode().equals("CHANGE_TICKET")) {
            businessCodes.add(ReturnPendingEnum.SPECIAL_TICKET_CHANGE.getCode());
        }
        // 退标书费申请退回
        if (dto.getBusinessCode().equals("BSF_REFUND")) {
            businessCodes.add(ReturnPendingEnum.TENDER_FEES_APPLY.getCode());
        }
//        // 登记供应商退回
//        if (dto.getBusinessCode().equals("SUPPLIER_REGISTER_APPLY")) {
//            businessCodes.add(ReturnPendingEnum.REGISTERED_SUPPLIERS.getCode());
//        }
        // 补录付款凭证退回
//        if (dto.getBusinessCode().equals("APPLY_PAY_FILE")) {
//            businessCodes.add(ReturnPendingEnum.RERECORD_PAYMENT.getCode());
//        }
        // 代理服务费修改退回
        if (dto.getBusinessCode().equals("AGENCY_FEE_APPLY")) {
            businessCodes.add(ReturnPendingEnum.AGENCY_SERVICE.getCode());
        }
        // 数据修改退回
        if (dto.getBusinessCode().equals("DATA_UPDATE_ONE")) {
            businessCodes.add(ReturnPendingEnum.DATA_MODIFICATION.getCode());
        }

        // 保证金退还退回(线下)
        if (dto.getBusinessCode().equals("returnBond")) {
            businessCodes.add(ReturnPendingEnum.DEPOSIT_REFUND.getCode());
        }
        // 专家抽取退回
        if (dto.getBusinessCode().equals("EXPERT_EXTRACTION")) {
            businessCodes.add(ReturnPendingEnum.EXPERT_EXTRACTION.getCode());
        }
//        // 专家抽取备案退回
//        if (dto.getBusinessCode().equals("EXPERT_EXTRACTION_BEIAN")) {
//            businessCodes.add(ReturnPendingEnum.EXPERTS_SELECTED_RECORD.getCode());
//        }
        // 专家评价退回
        if (dto.getBusinessCode().equals("EXPERT_APPRAISE")) {
            businessCodes.add(ReturnPendingEnum.EXPERTS_APPRAISE_RETURN.getCode());
        }
        // 专家状态变更
        if (dto.getBusinessCode().equals("EXPERT_CHANGE")) {
            businessCodes.add(ReturnPendingEnum.EXPERTS_OUT_RETURN.getCode());
            businessCodes.add(ReturnPendingEnum.EXPERTS_PAUSE_RETURN.getCode());
        }
        // 专家信息变更
        if (dto.getBusinessCode().equals("EXPERT_UPDATE")) {
            businessCodes.add(ReturnPendingEnum.EXPERTS_UPDATE_RETURN.getCode());
        }
        // 用印申请
        if (FlowClientConstant.REPORTING_MATTER.equals(dto.getBusinessCode())
        || FlowClientConstant.CONTRACTUAL_MATTERS.equals(dto.getBusinessCode())
        || FlowClientConstant.DRAFT_TEXT_CLARIFY.equals(dto.getBusinessCode())
        || FlowClientConstant.DRAFT_TEXT_REPLY.equals(dto.getBusinessCode())
        || FlowClientConstant.DRAFT_TEXT_OTHER.equals(dto.getBusinessCode())
        || FlowClientConstant.SEAL_APPROVAL_TENDER.equals(dto.getBusinessCode())
        || FlowClientConstant.SEAL_APPROVAL_REPORT.equals(dto.getBusinessCode())
        || FlowClientConstant.SEAL_APPROVAL_PURCHASING.equals(dto.getBusinessCode())
        || FlowClientConstant.WITHDRAW_SEAL_APPLICATION.equals(dto.getBusinessCode())
        || FlowClientConstant.REPORTING_MATTER_ONE.equals(dto.getBusinessCode())
        ) {
            businessCodes.add(ReturnPendingEnum.SEAL_APPLICATION_RETURN.getCode());
        }
        // 招标恢复
        if (dto.getBusinessCode().equals("COMPLAINT_RECOVER")) {
            businessCodes.add(ReturnPendingEnum.COMPLAIN_RECOVER_RETURN.getCode());
        }


        if (businessCodes.size() > 0) {
            pendingItemLambdaQueryWrapper.in(PendingItem::getBusinessCode,businessCodes);
            pendingItemLambdaQueryWrapper.eq(PendingItem::getBusinessId,dto.getBusinessId());
            this.remove(pendingItemLambdaQueryWrapper);
        }
    }

    /**
     * 退回待处理 无需处理
     * @param req
     * @return
     */
    @Override
    public Boolean updateHandRequired(PendingItemReq req) {
        return this.removeById(req.getId());
    }
}
