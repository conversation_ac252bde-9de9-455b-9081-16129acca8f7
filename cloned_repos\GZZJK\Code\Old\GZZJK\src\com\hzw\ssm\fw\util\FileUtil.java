package com.hzw.ssm.fw.util;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileWriter;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.URL;
import java.util.Properties;

import javax.servlet.http.HttpServletResponse;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

public class FileUtil {

	private static Log log = LogFactory.getLog(FileUtil.class);

	/**
	 * 获取Properties文件信息
	 * 
	 * @param callingClass
	 *            类源
	 * @param resource
	 *            文件源
	 * @return Properties文件信息
	 * @throws Exception
	 */
	public static Properties getProperties(Class callingClass, String resource)
			throws Exception {
		InputStream objInputStream = FileUtil.getResourceAsStream(resource,
				callingClass);
		Properties prop = new Properties();
		prop.load(objInputStream);
		objInputStream.close();
		return prop;
	}

	/**
	 * 
	 * @param resourceName
	 * @param callingClass
	 * @return
	 */
	@SuppressWarnings("rawtypes")
	public static InputStream getResourceAsStream(String resourceName,
			Class callingClass) {
		URL url = getResource(resourceName, callingClass);
		try {
			return ((url != null) ? url.openStream() : null);
		} catch (IOException e) {
			log.debug("Resource " + resourceName + " has not found.", e);
		}
		return null;
	}

	/**
	 * 
	 * @param resourceName
	 * @param callingClass
	 * @return
	 */
	@SuppressWarnings("rawtypes")
	public static URL getResource(String resourceName, Class callingClass) {
		java.net.URL url = Thread.currentThread().getContextClassLoader()
				.getResource(resourceName);

		if (url == null) {
			url = FileUtil.class.getClassLoader().getResource(resourceName);
		}

		if (url == null) {
			String className = callingClass.getName();
			String packageName = className.substring(0, className
					.lastIndexOf(46) + 1);
			String path = packageName.replaceAll("\\.", String.valueOf('/'));
			url = callingClass.getClassLoader()
					.getResource(path + resourceName);
		}

		if (url == null) {
			ClassLoader cl = callingClass.getClassLoader();

			if (cl != null) {
				url = cl.getResource(resourceName);
			}
		}

		if ((url == null) && (resourceName != null)
				&& (resourceName.charAt(0) != '/')) {
			return getResource('/' + resourceName, callingClass);
		}

		return url;
	}

	/**
	 * 读取文件内容并用字符串返回
	 * 
	 * @param file
	 *            文件对象
	 * @return 返回文件内容
	 */
	public static String readFile(File file) {
		StringBuffer buf = new StringBuffer("");
		try {
			BufferedReader in = new BufferedReader(new InputStreamReader(
					new FileInputStream(file)));
			String inputLine;
			while ((inputLine = in.readLine()) != null) {
				buf.append(inputLine);
				buf.append('\n');
			}

			in.close();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return buf.toString();

	}

	/**
	 * 将指定的内容写到文件
	 * 
	 * @param file
	 *            文件对象
	 * @param content
	 *            文件内容
	 */
	public static void writeFile(File file, String content) {
		try {
			FileWriter out;
			out = new FileWriter(file);
			out.write(content);
			out.close();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
	 * 导出文件，注意：导出后缓存将被删除
	 * 
	 * @param fileName
	 *            文件名
	 * @param buffer
	 *            文件内容缓存
	 * @param response
	 *            HttpServletResponse对象
	 * @return 如果出错返回错误信息
	 */
	public static String download(String fileName, byte[] buffer, HttpServletResponse response) {
		String ret = "";
		javax.servlet.ServletOutputStream outputStream = null;
		try {

			if (buffer != null && buffer.length > 0) {
				String strFileName = new String(fileName.getBytes("gb2312"),
						"iso-8859-1");

				response.reset();
				response.setContentType("application/x-download");

				// if (request.getHeader("User-Agent").indexOf("MSIE 5.5") !=
				// -1) { ... IE5.5不能加上 attachment;
				response.setHeader("Content-Disposition",
						"attachment; filename=\"" + strFileName + "\"");

				int length = buffer.length;

				outputStream = response.getOutputStream();
				outputStream.write(buffer, 0, length);
				outputStream.flush();

				outputStream.close();
				response
						.setStatus(javax.servlet.http.HttpServletResponse.SC_OK);
				response.flushBuffer();
			}

		} catch (Exception e) {
			ret = e.toString();
			e.printStackTrace();
		} finally {
			buffer = null;

			// response.reset();
			// response.setContentType("text/html");
		}
		return ret;
	}
	/**
	 *	下载文件
	 * 
	 * @param fileName
	 *            文件名
	 * @param is
	 *            文件
	 * @param response
	 *            HttpServletResponse对象
	 * @return 如果出错返回错误信息
	 */
	public static String download(String fileName,InputStream is, HttpServletResponse response) {
		String ret = "";
		javax.servlet.ServletOutputStream outputStream = null;
		try {
			if (is!=null) {
				String strFileName = new String(fileName.getBytes("gb2312"),
						"iso-8859-1");
				response.reset();
				response.setContentType("application/x-download");
				response.setHeader("Content-Disposition",
						"attachment; filename=\"" + strFileName + "\"");

				outputStream = response.getOutputStream();
				byte[] buffer=new byte[512];
				int length=0;
				while((length=is.read(buffer))>0){
					outputStream.write(buffer, 0, length);
				}
				outputStream.flush();
				outputStream.close();
				response.setStatus(javax.servlet.http.HttpServletResponse.SC_OK);
				response.flushBuffer();
			}

		} catch (Exception e) {
			ret = e.toString();
			e.printStackTrace();
		} finally {
			try {
				if(is!=null){
					is.close();
				}
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
		return ret;
	}
}
