package com.hzw.sunflower.dto;

import com.hzw.sunflower.entity.BondSplit;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @datetime 2022/11/09 16:33
 * @description: ncc推送拆分实体类
 * @version: 1.0
 */
@ApiModel(description = "ncc推送拆分实体类")
@Data
public class BondSplitNccDto extends BondSplit {

    @ApiModelProperty(value = "记账部门编码")
    private String accountDepartmentCode;

    @ApiModelProperty("项目编号")
    private String purchaseNumber;

    @ApiModelProperty(value = "流水收款时间（创建时间）")
    private String creationTime;

    @ApiModelProperty(value = "客商ID")
    private String customer;

    @ApiModelProperty(value = "客商表主键ID，不推，用于查询")
    private Long customerId;

    @ApiModelProperty(value = "收款银行账户")
    private String recAccount;

    @ApiModelProperty(value = "保证金到账时间")
    private String receiveTime;

}
