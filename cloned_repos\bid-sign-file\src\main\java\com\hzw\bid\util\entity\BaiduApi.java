package com.hzw.bid.util.entity;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
public class BaiduApi {

    // appkey
    @Value("${baidu.imageText.client_id:}")
    private String clientId;

    // skey
    @Value("${baidu.imageText.client_secret:}")
    private String clientSecret;

    // gettoken
    @Value("${baidu.imageText.loginUrl:}")
    private String loginUrl;

    // analysis
    @Value("${baidu.imageText.analysisUrl:}")
    private String analysisUrl;


    // progress
    @Value("${baidu.imageText.analysisProgressUrl:}")
    private String analysisProgressUrl;
}
