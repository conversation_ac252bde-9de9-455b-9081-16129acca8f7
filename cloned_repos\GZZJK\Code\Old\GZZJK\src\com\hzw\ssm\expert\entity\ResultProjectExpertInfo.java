package com.hzw.ssm.expert.entity;

import java.util.Date;

import com.hzw.ssm.fw.base.BaseEntity;
import com.hzw.ssm.fw.util.Page;

//项目抽取结果信息实体类
public class ResultProjectExpertInfo extends BaseEntity{
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	private String decimationBatch;//抽取流水号
	private String projectId;//项目信息主键
	private String projectNo;//项目编号
	private String projectName;//项目名称
	private Date bidTime;
	private String manager;//项目负责人
	private String managerName;//项目负责人姓名
	private String conditionId;//条件id
	private String expertId;//专家用户id
	private String expertName;  // 专家名称
	private String reason = "";// 不参加原因
	private String qt_reason = "";//其他原因
	private String method;//抽取方式
	private Long joinStatus;// 是否参加
	private String department;//部门
	private Date bidStartTime;//评标开始时间
	private Date bidEndTime;//评标结束时间
	/** 分页 */
	private Page page;
	public String getProjectId() {
		return projectId;
	}
	public void setProjectId(String projectId) {
		this.projectId = projectId;
	}
	public String getProjectNo() {
		return projectNo;
	}
	public void setProjectNo(String projectNo) {
		this.projectNo = projectNo;
	}
	public String getProjectName() {
		return projectName;
	}
	public void setProjectName(String projectName) {
		this.projectName = projectName;
	}
	public String getManager() {
		return manager;
	}
	public void setManager(String manager) {
		this.manager = manager;
	}
	public String getManagerName() {
		return managerName;
	}
	public void setManagerName(String managerName) {
		this.managerName = managerName;
	}
	public String getConditionId() {
		return conditionId;
	}
	public void setConditionId(String conditionId) {
		this.conditionId = conditionId;
	}
	public String getExpertId() {
		return expertId;
	}
	public void setExpertId(String expertId) {
		this.expertId = expertId;
	}
	public String getExpertName() {
		return expertName;
	}
	public void setExpertName(String expertName) {
		this.expertName = expertName;
	}
	public String getReason() {
		return reason;
	}
	public void setReason(String reason) {
		this.reason = reason;
	}
	public String getQt_reason() {
		return qt_reason;
	}
	public void setQt_reason(String qt_reason) {
		this.qt_reason = qt_reason;
	}
	public Long getJoinStatus() {
		return joinStatus;
	}
	public void setJoinStatus(Long joinStatus) {
		this.joinStatus = joinStatus;
	}
	public String getDecimationBatch() {
		return decimationBatch;
	}
	public void setDecimationBatch(String decimationBatch) {
		this.decimationBatch = decimationBatch;
	}
	public Page getPage() {
		return page;
	}
	public void setPage(Page page) {
		this.page = page;
	}
	public String getMethod() {
		return method;
	}
	public void setMethod(String method) {
		this.method = method;
	}
	public Date getBidTime() {
		return bidTime;
	}
	public void setBidTime(Date bidTime) {
		this.bidTime = bidTime;
	}
	public String getDepartment() {
		return department;
	}
	public void setDepartment(String department) {
		this.department = department;
	}
	public Date getBidStartTime() {
		return bidStartTime;
	}
	public void setBidStartTime(Date bidStartTime) {
		this.bidStartTime = bidStartTime;
	}
	public Date getBidEndTime() {
		return bidEndTime;
	}
	public void setBidEndTime(Date bidEndTime) {
		this.bidEndTime = bidEndTime;
	}

	
}
