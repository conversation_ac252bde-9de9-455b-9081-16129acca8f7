# Tomcat
server:
  port: 9204

# Spring
spring:
  application:
    # 应用名称
    name: portal-resource
  profiles:
    # 环境配置
    active: dev
  cloud:
    stream:
      function:
        # 重点配置 与 binding 名与消费者对应
        definition: sms
--- # nacos 配置
spring:
  cloud:
    nacos:
      # nacos 服务地址
      server-addr: 127.0.0.1:8848
      discovery:
        # 注册组
        group: DEFAULT_GROUP
        namespace: ${spring.profiles.active}
      config:
        # 配置组
        group: DEFAULT_GROUP
        namespace: ${spring.profiles.active}
  config:
    import:
      - optional:nacos:application-common.yml
      - optional:nacos:datasource.yml
      - optional:nacos:${spring.application.name}.yml
