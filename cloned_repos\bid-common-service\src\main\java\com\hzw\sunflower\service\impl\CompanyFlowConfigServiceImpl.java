package com.hzw.sunflower.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzw.sunflower.common.redis.RedisCache;
import com.hzw.sunflower.constant.CommonConstants;
import com.hzw.sunflower.constant.CompanyFlowConfigConstants;
import com.hzw.sunflower.constant.GeneralConstants;
import com.hzw.sunflower.controller.request.CompanyFlowConfigReq;
import com.hzw.sunflower.dao.CompanyFlowConfigMapper;
import com.hzw.sunflower.entity.CompanyFlowConfig;
import com.hzw.sunflower.service.CompanyFlowConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


/**
* CompanyFlowConfigService接口实现
*
*/
@Service
public class CompanyFlowConfigServiceImpl extends ServiceImpl<CompanyFlowConfigMapper, CompanyFlowConfig> implements CompanyFlowConfigService {

    @Autowired
    private RedisCache redisCache;

    @Autowired
    protected CompanyFlowConfigMapper baseMapper;


    /**
     * 根据公司id获取业务流程配置
     * @param req
     * @return
     */
    @Override
    public CompanyFlowConfig getByCompanyId(CompanyFlowConfigReq req) {
        CompanyFlowConfig config = new CompanyFlowConfig();
        // 根据企业id + 业务编号查询
        LambdaQueryWrapper<CompanyFlowConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CompanyFlowConfig::getCompanyId, req.getCompanyId())
                .eq(CompanyFlowConfig::getBusinessCode, req.getBusinessCode())
                .last("limit 1");
        config = this.getOne(queryWrapper);
        // 判断数据是否存在
        if(BeanUtil.isEmpty(config)){
            // 不存在查询默认数据
            LambdaQueryWrapper<CompanyFlowConfig> configLambdaQueryWrapper = new LambdaQueryWrapper<>();
            configLambdaQueryWrapper.eq(CompanyFlowConfig::getCompanyId, CompanyFlowConfigConstants.DEFAULT_COMPANY_ID)
                    .eq(CompanyFlowConfig::getBusinessCode, req.getBusinessCode())
                    .eq(CompanyFlowConfig::getIsDefault, CommonConstants.YES)
                    .last("limit 1");
            config = this.getOne(configLambdaQueryWrapper);
        }
        return config;
    }

    /**
     * 根据公司id获取业务流程配置-用印申请
     * @param req
     * @return
     */
    @Override
    public CompanyFlowConfig getByCompanyIdForSeal(CompanyFlowConfigReq req) {
        CompanyFlowConfig config = new CompanyFlowConfig();
        // 根据企业id + 业务编号查询
        LambdaQueryWrapper<CompanyFlowConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CompanyFlowConfig::getCompanyId, req.getCompanyId())
                .eq(CompanyFlowConfig::getBusinessCode, req.getBusinessCode())
                .eq(CompanyFlowConfig::getBusinessType, req.getBusinessType())
                .last("limit 1");
        config = this.getOne(queryWrapper);
        // 判断数据是否存在
        if(BeanUtil.isEmpty(config)){
            // 不存在查询默认数据
            LambdaQueryWrapper<CompanyFlowConfig> configLambdaQueryWrapper = new LambdaQueryWrapper<>();
            configLambdaQueryWrapper.eq(CompanyFlowConfig::getCompanyId, CompanyFlowConfigConstants.DEFAULT_COMPANY_ID)
                    .eq(CompanyFlowConfig::getBusinessCode, req.getBusinessCode())
                    .eq(CompanyFlowConfig::getIsDefault, CommonConstants.YES)
                    .eq(CompanyFlowConfig::getBusinessType, req.getBusinessType())
                    .last("limit 1");
            config = this.getOne(configLambdaQueryWrapper);
        }
        return config;
    }

    /**
     * 根据业务code获取流程key
     * @param businessCode
     * @return
     */
    @Override
    public List<String> getKeyByBusinessCode(List<String> businessCode) {
        LambdaQueryWrapper<CompanyFlowConfig> configLambdaQueryWrapper = new LambdaQueryWrapper<>();
        configLambdaQueryWrapper.in(CompanyFlowConfig::getBusinessCode, businessCode);
        List<CompanyFlowConfig> list = this.list(configLambdaQueryWrapper);
        return list.stream().map(CompanyFlowConfig::getProcessDefinitionKey).distinct().collect(Collectors.toList());
    }


    /**
     * 根据流程key获取业务code
     * @param key
     * @return
     */
    @Override
    public String getBusinessCodeByKey(String key) {
        LambdaQueryWrapper<CompanyFlowConfig> configLambdaQueryWrapper = new LambdaQueryWrapper<>();
        configLambdaQueryWrapper.eq(CompanyFlowConfig::getProcessDefinitionKey, key);
        configLambdaQueryWrapper.last("limit 1");
        CompanyFlowConfig one = this.getOne(configLambdaQueryWrapper);
        if(one != null){
            return one.getBusinessCode();
        }else {
            return null;
        }
    }

    /**
     * 根据业务code获取流程键值对
     * @param values
     * @return
     */
    @Override
    public Map<String, String> getMapByBusinessCode(List<String> values) {
        Map<String, String> map = new HashMap<>();
        for (String value : values) {
            LambdaQueryWrapper<CompanyFlowConfig> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(CompanyFlowConfig::getBusinessCode, value);
            wrapper.eq(CompanyFlowConfig::getIsDelete,0);
            List<CompanyFlowConfig> list = this.list(wrapper);
            for (CompanyFlowConfig companyFlowConfig : list) {
                map.put(companyFlowConfig.getProcessDefinitionKey(), companyFlowConfig.getProcessDefinitionKey());
            }
        }
        return map;
    }

    /**
     * 查询审批人为空是否跳过
     * @param processDefinitionKey
     * @return
     */
    @Override
    public Boolean getFlowIsJump(String processDefinitionKey) {
        String key = processDefinitionKey;
        Boolean isJump = redisCache.getCacheObject(key);
        if(null == isJump){
            LambdaQueryWrapper<CompanyFlowConfig> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(CompanyFlowConfig::getProcessDefinitionKey, processDefinitionKey);
            wrapper.eq(CompanyFlowConfig::getIsDelete,0);
            wrapper.last("limit 1");
            CompanyFlowConfig config = this.getOne(wrapper);
            if(BeanUtil.isNotEmpty(config)){
                if(CommonConstants.YES.equals(config.getIsJump())){
                isJump = true;
            } else {
                isJump = false;
            }
            // 存入缓存
            redisCache.setCacheObject(key, isJump, 14400000L, TimeUnit.MILLISECONDS);
            } else {
                isJump = false;
            }
        }
        return isJump;
    }

    @Override
    public CompanyFlowConfig getByDeptId(CompanyFlowConfigReq req) {
        CompanyFlowConfig config = baseMapper.getByDeptId(req);
        // 判断数据是否存在
        if(BeanUtil.isEmpty(config)){
            // 不存在查询默认数据
            LambdaQueryWrapper<CompanyFlowConfig> configLambdaQueryWrapper = new LambdaQueryWrapper<>();
            configLambdaQueryWrapper.eq(CompanyFlowConfig::getCompanyId, CompanyFlowConfigConstants.DEFAULT_COMPANY_ID)
                    .eq(CompanyFlowConfig::getBusinessCode, req.getBusinessCode())
                    .eq(CompanyFlowConfig::getIsDefault, CommonConstants.YES)
                    .last("limit 1");
            config = this.getOne(configLambdaQueryWrapper);
        }
        return config;
    }

    /**
     * 根据流程key获取是否记录审批记录
     * @param processDefinitionKey
     * @return
     */
    @Override
    public Boolean getIsAddRecord(String processDefinitionKey) {
        String key = processDefinitionKey;
        // 判断是否需要添加操作记录
        String addRecordKey = key + "addRecord:";
        String addRecordCodeKey = key + "addRecordCode:";
        Boolean isAdd = redisCache.getCacheObject(addRecordKey);
        if(null == isAdd){
            LambdaQueryWrapper<CompanyFlowConfig> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(CompanyFlowConfig::getProcessDefinitionKey, processDefinitionKey);
            wrapper.eq(CompanyFlowConfig::getIsDelete,0);
            wrapper.last("limit 1");
            CompanyFlowConfig config = this.getOne(wrapper);
            if(BeanUtil.isNotEmpty(config)) {
                if (CommonConstants.YES.equals(config.getIsAddRecord())) {
                    isAdd =  true;
                    redisCache.deleteObject(addRecordCodeKey);
                    redisCache.setCacheObject(addRecordCodeKey,config.getRecordCode(),14400000L,TimeUnit.MILLISECONDS);
                } else {
                    isAdd = false;
                }
            } else {
                isAdd = false;
            }
            redisCache.setCacheObject(addRecordKey,isAdd,14400000L,TimeUnit.MILLISECONDS);
        }
        return isAdd;
    }

    /**
     * 获取审批code
     * @param processDefinitionKey
     * @return
     */
    @Override
    public String getRecordCode(String processDefinitionKey) {
        String code = null;
        LambdaQueryWrapper<CompanyFlowConfig> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CompanyFlowConfig::getProcessDefinitionKey, processDefinitionKey);
        wrapper.eq(CompanyFlowConfig::getIsDelete,0);
        wrapper.last("limit 1");
        CompanyFlowConfig config = this.getOne(wrapper);
        if(BeanUtil.isNotEmpty(config)) {
            code = config.getRecordCode();
        }
        return code;
    }

}