<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzw.sunflower.dao.SealBidWinMapper">

    <select id="listSealFiles" resultType="com.hzw.sunflower.controller.response.SealBidWinVO">
        select
            t3.tenderer_id ,
            t3.tenderer_name ,
            t3.bid_contacts_id ,
            t3.bid_contacts_name ,
            t1.original_file ,
            t1.seal_file ,
            t4.oss_file_key seal_file_key,
            t4.oss_file_name seal_file_name
        from
            t_seal_file t1
            left join t_seal_bid_win t2 on t1.apply_id = t2.apply_id and t1.original_file = t2.file
            left join t_bid_win_people t3 on t3.id = t2.bid_win_people_id
            left join t_oss_file t4 on t4.id = t1.seal_file
            left join (select max(id)id from t_bid_win_notice where project_id = #{req.projectId} and section_id = #{req.bidId} and is_delete = 0 )t5 on t5.id = t3.winbid_id
        where t1.is_delete = 0
            and t2.is_delete = 0
            and t3.is_delete = 0
            and t3.project_id = #{req.projectId}
            and t3.section_id = #{req.bidId}
            and t5.id = t3.winbid_id
    </select>
</mapper>