package com.hzw.sunflower.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hzw.sunflower.common.BaseController;
import com.hzw.sunflower.common.Paging;
import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.common.annotation.Log;
import com.hzw.sunflower.common.annotation.RepeatSubmit;
import com.hzw.sunflower.constant.MessageConstants;
import com.hzw.sunflower.constant.constantenum.BusinessType;
import com.hzw.sunflower.entity.Tenant;
import com.hzw.sunflower.entity.condition.TenantCondition;
import com.hzw.sunflower.service.TenantService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * 租户表 Controller
 *
 * <AUTHOR>
 * @version 1.0.0 2021-04-07
 */
@Api(tags = "租户表 服务")
@RestController
@RequestMapping("/tenant")
public class TenantController extends BaseController {
    @Autowired
    private TenantService tenantService;

    @ApiOperation(value = "根据条件分页查询租户表 列表")
    @ApiImplicitParam(name = "condition", value = "租户表 查询条件", required = true, dataType = "TenantCondition", paramType = "body")
    @PostMapping("/list")
    public Paging<Tenant> list(@RequestBody TenantCondition condition) {
        IPage<Tenant> page = tenantService.findTenantByCondition(condition);
        return Paging.buildPaging(page);
    }

    @ApiOperation(value = "根据主键ID查询租户表 信息")
    @ApiImplicitParam(name = "id", value = "主键ID", required = true)
    @GetMapping(value = "/get/{id}")
    public Result<Tenant> get(@PathVariable Long id) {
        if (id == null) {
            return Result.failed(MessageConstants.GET_NULL);
        }
        Tenant tenant = tenantService.getTenantById(id);
        return Result.ok(tenant);
    }

    @ApiOperation(value = "新增租户表 信息")
    @Log(title = "租户表", description = "新增租户表", businessType = BusinessType.INSERT)
    @ApiImplicitParam(name = "tenant", value = "租户表 ", required = true, dataType = "Tenant", paramType = "body")
    @PostMapping("/add")
    @RepeatSubmit
    public Result<Boolean> add(@RequestBody Tenant tenant) {
        Boolean bool = tenantService.addTenant(tenant);
        return Result.okOrFailed(bool);
    }

    @ApiOperation(value = "修改租户表 信息")
    @Log(title = "租户表", description = "修改租户表", businessType = BusinessType.UPDATE)
    @ApiImplicitParam(name = "tenant", value = "租户表 ", required = true, dataType = "Tenant", paramType = "body")
    @PutMapping(value = "/update")
    @RepeatSubmit
    public Result<Boolean> update(@RequestBody Tenant tenant) {
        Long id = tenant.getId();
        if (id == null) {
            return Result.failed(MessageConstants.UPDATE_NULL);
        }
        Boolean bool = tenantService.updateTenant(tenant);
        return Result.okOrFailed(bool);
    }

    @ApiOperation(value = "根据主键ID删除租户表 ")
    @Log(title = "租户表", description = "删除租户表", businessType = BusinessType.DELETE)
    @ApiImplicitParam(name = "id", value = "主键ID", required = true)
    @DeleteMapping(value = "/delete/{id}")
    public Result<Boolean> delete(@PathVariable Long id) {
        if (id == null) {
            return Result.failed(MessageConstants.DELETE_NULL);
        }
        Boolean bool = tenantService.deleteTenantById(id);
        return Result.okOrFailed(bool);
    }

    @ApiOperation(value = "根据主键ID列表批量删除租户表 ")
    @Log(title = "租户表", description = "批量删除租户表", businessType = BusinessType.DELETE)
    @ApiImplicitParam(name = "idList", value = "主键ID列表", required = true, allowMultiple = true, paramType = "body")
    @DeleteMapping("/deleteList")
    public Result<Boolean> deleteList(@RequestBody List<Long> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return Result.failed(MessageConstants.DELETE_NULL);
        }
        Boolean bool = tenantService.deleteTenantByIds(idList);
        return Result.okOrFailed(bool);
    }
}