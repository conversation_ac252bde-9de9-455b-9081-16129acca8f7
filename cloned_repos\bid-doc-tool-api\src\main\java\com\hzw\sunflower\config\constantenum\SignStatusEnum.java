package com.hzw.sunflower.config.constantenum;

/**
 * <AUTHOR>
 * @date ：Created in 2022/07/27 9:23
 * @description：招标文件模板类型
 * @version: 1.0
 */
public enum SignStatusEnum {

    SIGN_1(1, "已签章"),
    SIGN_2(2, "未签章"),
    SIGN_3(3, "无需签章");

    private Integer type;
    private String desc;

    SignStatusEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
