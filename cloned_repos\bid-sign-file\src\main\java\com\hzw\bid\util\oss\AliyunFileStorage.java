package com.hzw.bid.util.oss;

import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.model.OSSObject;
import com.hzw.bid.properties.OssAliProperties;
import com.hzw.bid.util.SpringUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Value;

import java.io.File;
import java.io.InputStream;
import java.util.UUID;

@Slf4j
@Data
public class AliyunFileStorage implements OssFileStorage {

    @Value("${oss.aliyun.endpoint}")
    private String endpoint;

    @Value("${oss.aliyun.accessKeyId}")
    private String accessKeyId;

    @Value("${oss.aliyun.accessKeySecret}")
    private String accessKeySecret;

    @Value("${oss.aliyun.bucketName}")
    private String bucketName;

    public OSS ossClient() {
        /**
         * 1.生成OSSClient，您可以指定一些参数，详见“SDK手册 > Java-SDK > 初始化”，
         * 链接地址是：https://help.aliyun.com/document_detail/oss/sdk/java-sdk/init.html?spm=5176.docoss/sdk/java-sdk/get-start
         */
        OssAliProperties ossAliProperties = SpringUtils.getBean(OssAliProperties.class);
        this.endpoint = ossAliProperties.getEndpoint();
        this.accessKeyId = ossAliProperties.getAccessKeyId();
        this.accessKeySecret = ossAliProperties.getAccessKeySecret();
        this.bucketName = ossAliProperties.getBucketName();

        OSS ossClient = new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);
        /**
         * 2.判断Bucket是否存在。详细请参看“SDK手册 > Java-SDK > 管理Bucket”。
         * 链接地址是：https://help.aliyun.com/document_detail/oss/sdk/java-sdk/manage_bucket.html?spm=5176.docoss/sdk/java-sdk/init
         * @return 存在返回true
         */
        if (!ossClient.doesBucketExist(bucketName)) {
            /**
             * 3.创建Bucket。详细请参看“SDK手册 > Java-SDK > 管理Bucket”。
             * 链接地址是：https://help.aliyun.com/document_detail/oss/sdk/java-sdk/manage_bucket.html?spm=5176.docoss/sdk/java-sdk/init
             */
            ossClient.createBucket(bucketName);
        }
        return ossClient;
    }

    @Override
    public byte[] downloadFile(String key) {
        OSS ossClient = ossClient();
        InputStream content = null;
        try {
            // 调用ossClient.getObject返回一个OSSObject实例，该实例包含文件内容及文件元信息。
            OSSObject ossObject = ossClient.getObject(bucketName, key);
            // 调用ossObject.getObjectContent获取文件输入流，可读取此输入流获取其内容。
            content = ossObject.getObjectContent();
            return IOUtils.toByteArray(content);
        } catch (Exception e) {
            return new byte[]{};
        } finally {
            try {
                if (null != content) {
                    content.close();
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    @Override
    public void downloadFilePath(String key, String filePath) {

    }

    @Override
    public byte[] downloadFile(String key,String bucketName) {
        OSS ossClient = ossClient();
        InputStream content = null;
        try {
            // 调用ossClient.getObject返回一个OSSObject实例，该实例包含文件内容及文件元信息。
            OSSObject ossObject = ossClient.getObject(bucketName, key);
            // 调用ossObject.getObjectContent获取文件输入流，可读取此输入流获取其内容。
            content = ossObject.getObjectContent();
            return IOUtils.toByteArray(content);
        } catch (Exception e) {
            e.printStackTrace();
            return new byte[]{};
        } finally {
            try {
                if (null != content) {
                    content.close();
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    @Override
    public String uploadFile(String key, InputStream is) {
        OSS ossClient = ossClient();
        ossClient.putObject(bucketName,key,is);
        return key;
    }
    @Override
    public String uploadFile(String key, InputStream is,String bucketName) {
        OSS ossClient = ossClient();
        ossClient.putObject(bucketName,key,is);
        return key;
    }
    @Override
    public String uploadFileByPath(String filePath) {
        String key = "";
        //上传到oss
        File ossFile = new File(filePath);
        OSS ossClient = ossClient();
        key = UUID.randomUUID() + ossFile.getName();
        ossClient.putObject(bucketName, key, ossFile);
        return key;
    }

    @Override
    public String generatePresignedUrl(String key) {
        return generatePresignedUrl(key,bucketName);
    }

    @Override
    public String generatePresignedUrl(String key,String bucketName) {
        return null;
    }

    @Override
    public Object getSimplifiedObjectMeta(String key) {
        return null;
    }
}
