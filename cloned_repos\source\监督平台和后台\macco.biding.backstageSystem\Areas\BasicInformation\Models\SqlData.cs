﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace macco.biding.backstageSystem.Areas.BasicInformation.Models
{
    public class SqlDataModel
    {

        public List<SqlData> Result
        {
            get;
            set;
        }
    }


    public class SqlData
    {
        /// <summary>
        /// 表格名称
        /// </summary>
        public string TableName
        {
            get;
            set;
        }
        /// <summary>
        /// 列名集合
        /// </summary>
        public List<string> ColumnNames
        {
            get;
            set;
        }
        /// <summary>
        /// 结果数据Json
        /// </summary>
        public string Data
        {
            get;
            set;
        }
    }
}