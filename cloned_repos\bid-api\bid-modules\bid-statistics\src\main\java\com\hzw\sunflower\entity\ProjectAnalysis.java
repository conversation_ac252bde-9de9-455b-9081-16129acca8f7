package com.hzw.sunflower.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class ProjectAnalysis implements Serializable {

    //所属(一级行业)
    @ExcelProperty(value = "所属", index = 0)
    private String engineeringClass;

    //二级行业
    @ExcelProperty(value = "行业", index = 1)
    private String projectIndustry;

    //项目个数
    @ExcelProperty(value = "项目数(个)", index = 2)
    private int projectCount;

    //委托金额
    @ExcelProperty(value = "委托金额(万元)", index = 3)
    private BigDecimal entrustMoney;

    //占比
    @ExcelProperty(value = "占比(%)", index = 4)
    private BigDecimal proportion;

}
