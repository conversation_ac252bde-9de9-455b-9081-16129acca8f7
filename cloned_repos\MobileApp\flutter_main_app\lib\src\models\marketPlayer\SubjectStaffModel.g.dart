// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'SubjectStaffModel.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SubjectStaffModel _$SubjectStaffModelFromJson(Map<String, dynamic> json) {
  return SubjectStaffModel(
    active: json['active'] as bool,
    age: json['age'] as String,
    approvalDate: json['approvalDate'] as String,
    areaCode: json['areaCode'] as String,
    birthDate: json['birthDate'] as String,
    caNoDataKey: json['caNoDataKey'] as String,
    certificateLevel: json['certificateLevel'] as String,
    certificateName: json['certificateName'] as String,
    certificateQualification: json['certificateQualification'] as String,
    companyCode: json['companyCode'] as String,
    companyName: json['companyName'] as String,
    contactAddress: json['contactAddress'] as String,
    contactNumber: json['contactNumber'] as String,
    createdDate: json['createdDate'] as String,
    dataKey: json['dataKey'] as String,
    education: json['education'] as String,
    expiryDate: json['expiryDate'] as String,
    gender: json['gender'] as String,
    iDCard: json['iDCard'] as String,
    isActiveStaff: json['isActiveStaff'] as String,
    major: json['major'] as String,
    modifiedDate: json['modifiedDate'] as String,
    otherExpiryDate: json['otherExpiryDate'] as String,
    position: json['position'] as String,
    postcode: json['postcode'] as String,
    professionalTitle: json['professionalTitle'] as String,
    sex: json['sex'] as String,
    staffCode: json['staffCode'] as String,
    staffID: json['staffID'] as int,
    staffName: json['staffName'] as String,
    staffNo: json['staffNo'] as String,
    staffType: json['staffType'] as int,
    startPositionDate: json['startPositionDate'] as String,
    statusValue: json['statusValue'] as int,
    subjectDataKey: json['subjectDataKey'] as String,
    subjectID: json['subjectID'] as int,
    subjectType: json['subjectType'] as String,
    validDate: json['validDate'] as String,
    workExperience: json['workExperience'] as String,
    workYears: json['workYears'] as String,
  );
}

Map<String, dynamic> _$SubjectStaffModelToJson(SubjectStaffModel instance) =>
    <String, dynamic>{
      'active': instance.active,
      'age': instance.age,
      'approvalDate': instance.approvalDate,
      'areaCode': instance.areaCode,
      'birthDate': instance.birthDate,
      'caNoDataKey': instance.caNoDataKey,
      'certificateLevel': instance.certificateLevel,
      'certificateName': instance.certificateName,
      'certificateQualification': instance.certificateQualification,
      'companyCode': instance.companyCode,
      'companyName': instance.companyName,
      'contactAddress': instance.contactAddress,
      'contactNumber': instance.contactNumber,
      'createdDate': instance.createdDate,
      'dataKey': instance.dataKey,
      'education': instance.education,
      'expiryDate': instance.expiryDate,
      'gender': instance.gender,
      'iDCard': instance.iDCard,
      'isActiveStaff': instance.isActiveStaff,
      'major': instance.major,
      'modifiedDate': instance.modifiedDate,
      'otherExpiryDate': instance.otherExpiryDate,
      'position': instance.position,
      'postcode': instance.postcode,
      'professionalTitle': instance.professionalTitle,
      'sex': instance.sex,
      'staffCode': instance.staffCode,
      'staffID': instance.staffID,
      'staffName': instance.staffName,
      'staffNo': instance.staffNo,
      'staffType': instance.staffType,
      'startPositionDate': instance.startPositionDate,
      'statusValue': instance.statusValue,
      'subjectDataKey': instance.subjectDataKey,
      'subjectID': instance.subjectID,
      'subjectType': instance.subjectType,
      'validDate': instance.validDate,
      'workExperience': instance.workExperience,
      'workYears': instance.workYears,
    };
