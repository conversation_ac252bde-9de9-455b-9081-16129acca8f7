package com.hzw.sunflower.receive;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.hzw.sunflower.common.redis.RedisCache;
import com.hzw.sunflower.constant.AnalysisStatusEnum;
import com.hzw.sunflower.constant.OperationFlowEnum;
import com.hzw.sunflower.dto.BidDocCompareDto;
import com.hzw.sunflower.entity.BidFileSimilarity;
import com.hzw.sunflower.service.BidDocCompareService;
import com.hzw.sunflower.util.AnalysisFileToString;
import com.hzw.sunflower.util.FileUtils;
import com.hzw.sunflower.util.OssUtil;
import com.hzw.sunflower.util.TextComparator;
import com.hzw.sunflower.util.oss.OssFileStorage;
import com.hzw.sunflower.vo.BidDocCompareVo;
import com.rabbitmq.client.Channel;
import io.jsonwebtoken.lang.Collections;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.*;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * @Auther: cx
 * @Date: 2024/04/09 10:55
 * @Description:
 */
@Component
@Slf4j
@RabbitListener(queues = "bid_doc_compare")
public class BidDocCompareReceiver {

    @Value("${oss.active}")
    private String ossType;

    @Autowired
    private BidDocCompareService bidDocCompareService;

    @RabbitHandler
    public void process(Message message, String bidDocCompareJson, Channel channel) throws IOException {
        try{
            //手动确认
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
            BidDocCompareDto bidDocCompareDto = JSONObject.parseObject(bidDocCompareJson, BidDocCompareDto.class);
            log.info("文件相似度对比开始:{}",bidDocCompareJson);
            //第一步：查询业务流程，若全流程线上查询t_apply_response_file，若是其他流程，则查询t_bid_tenderer_record
            Integer projectOperationFlow = bidDocCompareService.queryProjectOperationFlow(bidDocCompareDto.getProjectId());
            if(null!=projectOperationFlow){
                //第二步：循环供应商，找到每个供应商的投标文件
                List<BidDocCompareVo> applyCompanyList = getBidTendererRecord(bidDocCompareDto.getSectionId(),bidDocCompareDto.getBidRound());
//                if(OperationFlowEnum.Flow0.getValue().equals(projectOperationFlow)){
//                    applyCompanyList = getApplyResponseFile(bidDocCompareDto.getSectionId(),bidDocCompareDto.getBidRound());
//                    if(CollectionUtils.isEmpty(applyCompanyList)){
//                        applyCompanyList = getBidTendererRecord(bidDocCompareDto.getSectionId(),bidDocCompareDto.getBidRound());
//                    }
//                }else{
//                    applyCompanyList = getBidTendererRecord(bidDocCompareDto.getSectionId(),bidDocCompareDto.getBidRound());
//                }
                //第三步：解析投标文件，支持docx/pdf，没有的话不解析
                getBidText(applyCompanyList);
                //第四步：计算每个供应商的投标文件两两相似度，最终计算一个平均相似度
                calculateSimilarity(applyCompanyList);
                //第五步：将相似度写入数据库
                for(BidDocCompareVo vo: applyCompanyList){
                    LambdaUpdateWrapper<BidFileSimilarity> updateWrapper = new LambdaUpdateWrapper<>();
                    updateWrapper.eq(BidFileSimilarity::getBidRound,bidDocCompareDto.getBidRound());
                    updateWrapper.eq(BidFileSimilarity::getSectionId,bidDocCompareDto.getSectionId());
                    updateWrapper.eq(BidFileSimilarity::getCompanyId,vo.getCompanyId());
                    updateWrapper.set(BidFileSimilarity::getAnalysisStatus, AnalysisStatusEnum.COMPARE_ALREADY.getValue());
                    updateWrapper.set(BidFileSimilarity::getSimilarity, vo.getSimilarity());
                    bidDocCompareService.update(updateWrapper);
                }
                log.info("文件相似度对比结束:{}",applyCompanyList);
            }
        }catch(Exception e){
            log.error("文档相似度对比异常:{}",e.getMessage());
            //消息丢弃
            channel.basicReject(message.getMessageProperties().getDeliveryTag(),false);
        }
    }

    private List<BidDocCompareVo> getApplyResponseFile(Long sectionId, Integer bidRound){
        return bidDocCompareService.queryApplyCompany(sectionId,bidRound);
    }

    private List<BidDocCompareVo> getBidTendererRecord(Long sectionId, Integer bidRound){
        List<BidDocCompareVo> tendererRecordList = bidDocCompareService.queryTenderRecord(sectionId,bidRound);
        for(BidDocCompareVo vo: tendererRecordList){
            if(StringUtils.isNotBlank(vo.getOssIds())){
                List<String> ossIdList = Arrays.asList(vo.getOssIds().split(","));
                String ossFileKey = bidDocCompareService.queryTenderRecordFile(ossIdList);
                vo.setOssFileKey(ossFileKey);
            }
        }
        return tendererRecordList;
    }

  /*  private void getBidText(List<BidDocCompareVo> applyCompanyList) throws IOException {
        for(BidDocCompareVo vo: applyCompanyList){
            if(null!=vo.getOssFileKey()){
                OssFileStorage storage = OssUtil.getOssFileStorage(ossType);
                InputStream inputStream = null;
                if(null != vo.getOssFileKey() ){
                    byte[] bytes = storage.downloadFile(vo.getOssFileKey());
                    inputStream = FileUtils.byte2InputStream(bytes);
                }
                if(null!=inputStream){
                    //将投标文件解析成字符串
                    analysisFile(vo,vo.getOssFileKey(),inputStream);
                }
            }
        }
    }*/

    private void getBidText(List<BidDocCompareVo> applyCompanyList) throws IOException {
        OssFileStorage storage = OssUtil.getOssFileStorage(ossType);

        for (BidDocCompareVo vo : applyCompanyList) {
            String ossFileKey = vo.getOssFileKey();
            if (ossFileKey == null) {
                continue;
            }

            try (InputStream inputStream = downloadAndGetStream(storage, ossFileKey)) {
                if (inputStream != null) {
                    analysisFile(vo, ossFileKey, inputStream);
                }
            } catch (IOException e) {
                // 可选：记录日志或做其他处理
                log.error("getBidText 文件流获取报错",e);
                throw e; // 保持原意继续抛出
            }
        }
    }

    private InputStream downloadAndGetStream(OssFileStorage storage, String ossFileKey) throws IOException {
        byte[] bytes = storage.downloadFile(ossFileKey);
        if (bytes == null || bytes.length == 0) {
            return null;
        }
        return FileUtils.byte2InputStream(bytes);
    }


    private void analysisFile(BidDocCompareVo vo,String fileKey,InputStream inputStream){
        if(fileKey.endsWith(".docx")){
            String test = AnalysisFileToString.readDocx(inputStream);
            vo.setContent(test);
        }else if(fileKey.endsWith(".pdf")){
            String test = AnalysisFileToString.readPDF(inputStream);
            vo.setContent(test);
        }
    }

    private void calculateSimilarity(List<BidDocCompareVo> applyCompanyList){
        for(BidDocCompareVo voSelf: applyCompanyList){
            int count = 0;
            double similarityTotal = 0.0;
            for(BidDocCompareVo voOther: applyCompanyList){
                if(!voSelf.getCompanyId().equals(voOther.getCompanyId())){
                    if(StringUtils.isNotBlank(voSelf.getContent()) && StringUtils.isNotBlank(voOther.getContent())){
                        double similarity = TextComparator.calculateJaccardSimilarity(voSelf.getContent(),voOther.getContent());
                        similarityTotal+=similarity;
                        count++;
                    }
                }
            }
            if(0!=count){
                String formattedValue = String.format("%.2f", (similarityTotal / count) * 100);
                String companySimilarity = formattedValue +"%";
                voSelf.setSimilarity(companySimilarity);
            }
        }
    }

//    public static void main(String[] args) throws FileNotFoundException {
//        File file1 = new File("D:\\工具\\333.pdf");
//        File file2 = new File("D:\\工具\\333.pdf");
//        String test1 = AnalysisFileToString.readPDF(new FileInputStream(file1));
//        String test2 = AnalysisFileToString.readPDF(new FileInputStream(file2));
//        double ss = TextComparator.calculateJaccardSimilarity(test1,test2);
//        System.out.println(ss);
//    }
}
