package com.hzw.sunflower.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hzw.sunflower.common.BaseController;
import com.hzw.sunflower.common.Paging;
import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.common.annotation.RepeatSubmit;
import com.hzw.sunflower.constant.MessageConstants;
import com.hzw.sunflower.controller.request.ProjectBidDocRelationREQ;
import com.hzw.sunflower.dto.ProjectBidDocRelationDTO;
import com.hzw.sunflower.entity.ProjectBidDocRelation;
import com.hzw.sunflower.entity.condition.ProjectBidDocRelationCondition;
import com.hzw.sunflower.service.ProjectBidDocRelationService;
import com.hzw.sunflower.util.BeanListUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * TProjectBidDocRelationController
 */
@Api(tags = "TProjectBidDocRelation服务")
@RestController
@RequestMapping("/tProjectBidDocRelation")
public class ProjectBidDocRelationController extends BaseController {
    @Autowired
    private ProjectBidDocRelationService projectBidDocRelationService;

    @ApiOperation(value = "根据条件分页查询TProjectBidDocRelation列表")
    @ApiImplicitParam(name = "tProjectBidDocRelationREQ", value = "用户表 查询条件", required = true, dataType = "TProjectBidDocRelationREQ", paramType = "body")
    @PostMapping("/list")
    public Paging<ProjectBidDocRelation> list(@RequestBody ProjectBidDocRelationREQ projectBidDocRelationREQ) {
        ProjectBidDocRelationCondition condition = BeanListUtil.convert(projectBidDocRelationREQ, ProjectBidDocRelationCondition.class);
        IPage<ProjectBidDocRelation> page = projectBidDocRelationService.findInfoByCondition(condition);
        return Paging.buildPaging(page);
    }


    @ApiOperation(value = "根据主键ID查询TProjectBidDocRelation信息")
    @ApiImplicitParam(name = "id", value = "主键ID", required = true)
    @GetMapping(value = "/get/{id}")
    public Result<ProjectBidDocRelation> get(@PathVariable Long id) {
        if (id == null) {
            return Result.failed(MessageConstants.GET_NULL);
        }
        ProjectBidDocRelation projectBidDocRelation = projectBidDocRelationService.getInfoById(id);
        return Result.ok(projectBidDocRelation);
    }

    @ApiOperation(value = "新增TProjectBidDocRelation信息")
    @ApiImplicitParam(name = "tProjectBidDocRelationDTO", value = "TProjectBidDocRelation表 ", required = true, dataType = "TProjectBidDocRelationDTO", paramType = "body")
    @PostMapping("/add")
    @RepeatSubmit
    public Result<Boolean> add(@RequestBody ProjectBidDocRelationDTO tProjectBidDocRelationDTO) {
        Boolean bool = projectBidDocRelationService.addInfo(tProjectBidDocRelationDTO);
        return Result.okOrFailed(bool);
    }

    @ApiOperation(value = "修改TProjectBidDocRelation信息")
    @ApiImplicitParam(name = "tProjectBidDocRelationDTO", value = "TProjectBidDocRelation表 ", required = true, dataType = "TProjectBidDocRelationDTO", paramType = "body")
    @PutMapping(value = "/update")
    @RepeatSubmit
    public Result<Boolean> update(@RequestBody ProjectBidDocRelationDTO tProjectBidDocRelationDTO) {
        Long id = tProjectBidDocRelationDTO.getId();
        if (id == null) {
            return Result.failed(MessageConstants.UPDATE_NULL);
        }
        Boolean bool = projectBidDocRelationService.updateInfo(tProjectBidDocRelationDTO);
        return Result.okOrFailed(bool);
    }

    @ApiOperation(value = "根据主键ID删除TProjectBidDocRelation表 ")
    @ApiImplicitParam(name = "id", value = "主键ID", required = true)
    @DeleteMapping(value = "/delete/{id}")
    @RepeatSubmit
    public Result<Boolean> delete(@PathVariable Long id) {
        if (id == null) {
            return Result.failed(MessageConstants.DELETE_NULL);
        }
        Boolean bool = projectBidDocRelationService.deleteById(id);
        return Result.okOrFailed(bool);
    }

    @ApiOperation(value = "根据主键ID列表批量删除TProjectBidDocRelation表 ")
    @ApiImplicitParam(name = "idList", value = "主键ID列表", required = true, allowMultiple = true, paramType = "body")
    @DeleteMapping("/deleteList")
    @RepeatSubmit
    public Result<Boolean> deleteList(@RequestBody List<Long> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return Result.failed(MessageConstants.DELETE_NULL);
        }
        Boolean bool = projectBidDocRelationService.deleteByIds(idList);
        return Result.okOrFailed(bool);
    }
}
