package com.hzw.sunflower.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.controller.project.request.MoneyReq;
import com.hzw.sunflower.entity.ProjectBidSectionRecord;

import java.math.BigDecimal;


/**
 * 项目标段表 Service接口
 *
 */
public interface ProjectBidSectionRecordService extends IService<ProjectBidSectionRecord> {

    /**
     * 计算金额
     * 1.国家纪委1980号文
     * @param req
     * @return
     */
    BigDecimal calculate_1Moneys(MoneyReq req);

    /**
     * 2.苏招协2022002号文
     * @param req
     * @return
     */
    BigDecimal calculate_2Moneys(MoneyReq req);

    /**
     * 3.固定比例
     * @param req
     * @return
     */
    BigDecimal calculate_3Moneys(MoneyReq req);
}
