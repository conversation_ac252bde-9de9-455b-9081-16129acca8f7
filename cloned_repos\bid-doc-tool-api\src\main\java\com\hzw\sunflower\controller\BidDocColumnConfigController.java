package com.hzw.sunflower.controller;

import cn.hutool.core.io.FileUtil;
import com.deepoove.poi.XWPFTemplate;
import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.controller.request.BidDocCompileReq;
import com.hzw.sunflower.dto.TemplateFillDTO;
import com.hzw.sunflower.entity.BidDocColumnConfig;
import com.hzw.sunflower.service.BidDocColumnConfigService;
import com.hzw.sunflower.service.BidDocColumnService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.ClassUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @datetime 2024/06/26 15:42
 * @description: fanqh
 * @version: 1.0
 */
@Api(tags = "招标文件字段配置服务")
@RestController
@RequestMapping("/bidDocColumnConfig")
public class BidDocColumnConfigController {

    @Value("${files.template.path}")
    private String templatePath;

    @Value("${files.temporary.path}")
    private String temporaryPath;

    @Autowired
    private BidDocColumnConfigService bidDocColumnConfigService;

    @Autowired
    private BidDocColumnService bidDocColumnService;

    @ApiOperation(value = "根据模板id获取配置信息")
    @ApiImplicitParam(name = "id", value = "主键ID", required = true)
    @GetMapping("/getConfigByTemplateId/{templateId}")
    public Result<Map<Long, List<BidDocColumnConfig>>> getConfigByTemplateId(@PathVariable Long templateId) {
        return Result.ok(bidDocColumnConfigService.getConfigByTemplateId(templateId));
    }

    @ApiOperation(value = "测试生成招标文件")
    @PostMapping("/testCompile")
    public void testCompile(@RequestBody BidDocCompileReq req, HttpServletResponse response) throws Exception {
        //获取生成的文档路径
        String templateWordPath = templatePath + "公开招标-标准设备采购招标文件模板.docx";
        String outPath = temporaryPath + "test.docx";

        TemplateFillDTO dto = bidDocColumnService.getFillDto(req.getDocId());

        wordTemplateToWordFile(templateWordPath, dto, outPath);
        // 导出
    }

    /**
     * 根据word模板生成word
     */
    /**
     * @param temPath 模板路径
     * @param t       替换实体类
     * @param path    生成文件路径
     * @throws IOException
     */
    public void wordTemplateToWordFile(String temPath, TemplateFillDTO t, String path) throws IOException {
        //路径格式化下
        path = FileUtil.normalize(path);
        String tpath = FileUtil.normalize(temPath);
        //如果传入的是相对路径
        if (!FileUtil.isAbsolutePath(tpath)) {
            //当前项目路径
            String npath = ClassUtils.getDefaultClassLoader().getResource("").getPath();
            //模板的绝对路径 = 项目路径+相对路径
            tpath = FileUtil.normalize(npath + File.separator + temPath);
        }
        //生成
        XWPFTemplate.compile(tpath).render(t).writeToFile(path);
    }
}
