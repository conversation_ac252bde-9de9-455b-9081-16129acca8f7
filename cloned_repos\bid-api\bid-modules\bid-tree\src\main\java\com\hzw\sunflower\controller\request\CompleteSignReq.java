package com.hzw.sunflower.controller.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @datetime 2023/03/09 9:21
 * @description: 完成签章入参
 * @version: 1.0
 */
@ApiModel(description = "完成签章入参")
@Data
public class CompleteSignReq {

    @ApiModelProperty(value = "签章文件id")
    private Long fileId;

    @ApiModelProperty(value = "企业ID")
    private Long companyId;

    @ApiModelProperty(value = "标段ID")
    private Long sectionId;

}
