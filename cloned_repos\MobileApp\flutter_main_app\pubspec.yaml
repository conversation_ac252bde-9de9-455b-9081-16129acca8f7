name: flutter_main_app
description: A new Flutter project.

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
version: 1.0.0+1

environment:
  sdk: ">=2.1.0 <3.0.0"

dependencies:
  flutter:
    sdk: flutter
  # custom network service by Lokie
  flutter_network_plugin:
    path: ../flutter_network_plugin

  # A simple Event Bus using Dart Streams for decoupling applications.
  event_bus: ^1.1.0

  # Draw SVG (and some Android VectorDrawable (XML)) files on a Flutter Widget.
  flutter_svg: ^0.14.2

  # Defines the annotation used by json_seriallizable
  # to create code for JSON serialization and deserialization.
  json_annotation: ^3.0.0

  # A mixture between dependency injection and state management,
  # built with widgets for widgets.
  provider: ^3.1.0+1

  # A simple Service Locator for Dart and Flutter projects.
  get_it: ^3.0.3

  # Material Design data visualization library written natively in Dart.
  charts_flutter: ^0.8.1

  # A Flutter plugin for iOS and Android
  # allowing access to the device cameras.
  # camera: ^0.5.6+3

  # # A Flutter plugin for iOS and Android
  # # for picking images from the image library,
  # # and taking new pictures with the camera.
  # image_picker: ^0.6.2+1

  # # A Flutter plugin for finding commonly used locations on the filesystem.
  # # Supports iOS and Android.
  # path_provider: ^1.4.4

  # A comprehensive, cross-platform path manipulation library for Dart.
  path: ^1.6.4

  # 国际化
  flutter_localizations:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^0.1.2

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The `build_runner` package provides a concrete way
  # of generating files using Dart code, outside of tools like `pub`.
  # Unlike `pub serve/build`, files are always generated directly on disk,
  # and rebuilds are incremental - inspired by tools such as Bazel.
  build_runner: ^1.7.2

  # Provides Dart Build System builders for handling JSON.
  # The builders generate code when they find members annotated with
  # classes defined in package:json_annotation
  json_serializable: ^3.2.3

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add Flutter specific assets to your application, add an assets section, 
  # like this:
  assets:
    - assets/images/logo.png
    - assets/images/weibo.png
    - assets/images/wechat.png
    - assets/images/qq.png
    - assets/images/equipment.svg

    # 共用资源
    - assets/images/waitting.gif
    - assets/images/waitting.png
    - assets/images/no_data.png

    # 首页图片
    - assets/images/equipment.png
    - assets/images/material.png
    - assets/images/operation.png
    - assets/images/monitor.png
    - assets/images/survey.png
    - assets/images/planning.png
    - assets/images/design.png
    - assets/images/other.png

    # 公告详情页面
    - assets/images/tender.png
    - assets/images/tenderagency.png
    - assets/images/tenderdoc.png
    - assets/images/datareport.png

    # 信息订阅页面图片
    - assets/images/info_blank_page.png

    # 业主信息页面图片
    - assets/images/bg_supplier_subject_home.png

    # 我的页面图片
    - assets/images/profile_appbar_background.png

    # 地区的 json 数据
    - assets/regions/province.json
    - assets/regions/city.json

  #  - images/a_dot_burr.jpeg
  #  - images/a_dot_ham.jpeg 

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware.

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add Flutter specific custom fonts to your application, add a fonts
  # section here, in this "flutter" section. Each entry in this list should
  # have a "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  fonts:
    - family: MyFlutterApp
      fonts:
        - asset: assets/fonts/MyFlutterApp.ttf
    #  - family:  MyFlutterApp
    #    fonts:
    #      - asset: fonts/MyFlutterApp.ttf
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages


  # This section identifies your Flutter project as a module meant for
  # embedding in a native host app.  These identifiers should _not_ ordinarily
  # be changed after generation - they are used to ensure that the tooling can
  # maintain consistency when adding or modifying assets and plugins.
  # They also do not have any bearing on your native host application's
  # identifiers, which may be completely independent or the same as these.
  module:
    androidX: true
    androidPackage: com.jszbtb.mobile
    iosBundleIdentifier: com.jszbtb.mobile
