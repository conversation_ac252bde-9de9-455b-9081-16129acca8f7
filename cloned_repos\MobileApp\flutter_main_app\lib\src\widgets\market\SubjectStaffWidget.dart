///***
/// * 主体人|机构信息 - 人员
/// */

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import 'package:flutter_main_app/serviceLocator.dart';
import 'package:flutter_main_app/src/constants/theme.dart';

import 'package:flutter_main_app/src/viewModels/marketPlayer/SubjectStaffViewModel.dart';

import 'package:flutter_main_app/src/models/marketPlayer/SubjectStaffModel.dart';

import 'package:flutter_main_app/src/components/CustomAppBar.dart';
import 'package:flutter_main_app/src/components/CustomCircularProgressIndicator.dart';
import 'package:flutter_main_app/src/components/CustomSafeArea.dart';
import 'package:flutter_main_app/src/components/CustomInfoCard.dart';

class SubjectStaffWidget extends StatefulWidget {
  const SubjectStaffWidget({Key key}) : super(key: key);

  /// * 路由名称
  static const String _routeName = 'SubjectStaff';
  static String get routeName => _routeName;

  @override
  _SubjectStaffWidgetState createState() =>
      _SubjectStaffWidgetState();
}

class _SubjectStaffWidgetState
    extends State<SubjectStaffWidget> {

  /// * 主体人员 视图模型 单例
  final SubjectStaffViewModel _model = serviceLocator<SubjectStaffViewModel>();

  @override
  void initState() {
    super.initState();

    _model.fetchSubjectStaffList();
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider<SubjectStaffViewModel>.value(
      value: serviceLocator<SubjectStaffViewModel>(),
      child: Consumer<SubjectStaffViewModel>(
        builder: (context, model, child) {
          return Scaffold(
            appBar: CustomAppBar(
              title: '人员',
            ),
            body: model.isLoading
              ? CustomCircularProgressIndicator()
              : CustomSafeArea(
                  backgroundColor: themeContentBackgroundColor,
                  child: ListView.builder(
                    physics: BouncingScrollPhysics(),
                    itemCount: model.subjectStaffList.length,
                    itemBuilder: (context, index) {
                      final SubjectStaffModel staff = model.subjectStaffList[index];

                      return CustomInfoCard(
                        title: staff.staffName != null && staff.staffName.length > 0
                          ? staff.staffName
                          : '---',
                        dataFields: [
                          {
                            'fieldName': '注册类别',
                            'fieldValue': staff.certificateName,
                          },
                          {
                            'fieldName': '证书编号',
                            'fieldValue': staff.staffNo,
                          },
                          {
                            'fieldName': '专业',
                            'fieldValue': staff.major,
                          },
                        ],
                        isFirstChild: index == 0,
                      );
                    },
                  ),
                ),
          );
        },
      ),
    );
  }

}
