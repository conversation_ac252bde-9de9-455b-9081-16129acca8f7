package com.hzw.sunflower.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hzw.sunflower.controller.project.request.*;
import com.hzw.sunflower.controller.project.response.ProjectListVO;
import com.hzw.sunflower.controller.project.response.ProjectVo;
import com.hzw.sunflower.controller.project.response.WaitListVo;
import com.hzw.sunflower.dto.AgentProjectDTO;
import com.hzw.sunflower.dto.ApplyInfoListDTO;
import com.hzw.sunflower.dto.MyProjectListDTO;
import com.hzw.sunflower.dto.UserAgentRecordDTO;
import com.hzw.sunflower.entity.JwtUser;
import com.hzw.sunflower.entity.Project;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2021/4/12 16:16
 * @description：代理建项目service
 * @modified By：`
 * @version: 1.0
 */
public interface AgentProjectService extends IService<Project> {


    /**
     * 代理机构新增委托项目入口
     *
     * @param agentProject
     * @return
     */
    Boolean addAgentProject(AgentProjectReq agentProject, JwtUser user);

    /**
     * 根据项目id 查询项目信息
     *
     * @param id
     * @return
     */
    AgentProjectDTO getProjectById(Long id);

    /**
     * 修改项目信息
     *
     * @param agentProject
     * @param user
     * @return
     */
    Boolean updateAgentProject(AgentProjectReq agentProject, JwtUser user);


    /**
     * 修改项目信息
     *
     * @param agentProject
     * @return
     */
    Boolean updateProject(AgentProjectReq agentProject, JwtUser user);

    /**
     * 获取用户上一次填写的代理机构信息
     *
     * @return
     */
    UserAgentRecordDTO selectAgent(JwtUser user);

    /**
     * 根据条件分页查询项目表 列表
     *
     * @param condition 查询条件
     * @return 分页信息
     */
    IPage<ProjectListVO> queryProjectPageListByUserId(ProjectConditionRep condition, Long userId);

    /**
     * 根据条件查询我的项目（包含授权给我的）
     * @param condition
     * @param userId
     * @return
     */
    IPage<MyProjectListDTO> queryMyProjectPageListByUserId(AgentMyProjectREQ condition, Long userId);
    /**
     * 查询我的项目列表信息 (不包含授权给我的）
     * @param condition
     * @param userId
     * @return
     */
    IPage<MyProjectListDTO> queryOnlyMyProjectPageListByUserId(AgentMyProjectREQ condition, Long userId);
    /**
     * @param projectEntrust
     * @return
     */
    Boolean updateProjectEntrust(ProjectEntrustREQ projectEntrust);

    /**
     * 专家库 查询项目集合
     * @return
     */
    List<ProjectVo> getProjectListByExpert(String keyWords);

    IPage<ApplyInfoListDTO> queryApplyInfoByUserId(ApplyInfoConditionReq condition, Long userId);

    /**
     * 重新上传委托合同
     * @param req
     * @return
     */
    Boolean updateContract(EntrustContractREQ req);

    /**
     * 查询代办事项列表
     * @param condition
     * @return
     */
    IPage<WaitListVo> queryWaitList(ApplyInfoConditionReq condition, JwtUser jwtUser);


    /**
     * 查询代办事项数量统计
     * @param condition
     * @return
     */
    List<WaitListVo> queryWaitListCount(ApplyInfoConditionReq condition);
//
//    IPage<WaitListVo> queryReturnList(ApplyInfoConditionReq condition, JwtUser jwtUser);
//
//    List<WaitListVo> queryReturnListCount(ApplyInfoConditionReq condition, JwtUser jwtUser);

    /**
     * 查询全公司已归档项目列表
     * @param condition
     * @return
     */
    IPage<ProjectListVO> listAllFinishedBid(ProjectConditionRep condition, Long userId);

    /**
     * 查询项目编号是否存在
     * @param req
     * @return
     */
    Boolean queryProjectNumberExist(ProjectNumberExistReq req);

    /**
     * 用印申请 查询项目信息
     * @param condition
     * @param userId
     * @return
     */
    IPage<MyProjectListDTO> listMySeal(AgentMyProjectREQ condition, Long userId);

}
