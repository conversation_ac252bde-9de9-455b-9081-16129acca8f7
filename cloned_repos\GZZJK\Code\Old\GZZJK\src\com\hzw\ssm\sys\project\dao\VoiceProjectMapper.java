package com.hzw.ssm.sys.project.dao;

import java.util.List;

import com.hzw.ssm.expert.entity.ExpertInfoEntity;
import com.hzw.ssm.sys.project.entity.ConditionEntity;
import com.hzw.ssm.sys.project.entity.ProjectChangesEntity;
import com.hzw.ssm.sys.project.entity.ProjectEntity;
import com.hzw.ssm.sys.project.entity.ResultEntity;

public interface VoiceProjectMapper {
	/**
	 * 根据流水号等条件查询项目
	 * @param entity
	 * @return
	 */
	public List<ProjectEntity> queryPageVoiceProjectInitList(ProjectEntity entity);
	/**
	 * 语音紧急处理
	 * @param entity
	 * @return
	 */
	public List<ProjectEntity> queryPageEmergencyTreatmentList(ProjectEntity entity);
	/**
	 * 
	 * 函数功能描述：添加项目变更信息记录
	 * @param projectChange
	 * @return
	 */
	public Integer addProjectChangeRecords(ProjectChangesEntity projectChange);
	
	/**
	 * 
	 * 函数功能描述：修改项目信息
	 * @param entity
	 * @return
	 */
	public Integer updateChangeProject(ProjectEntity entity);

	
	/**
	 *
	 *函数功能描述：查询项目变更记录
	 * @param entity
	 * @return
	 */
	public List<ProjectEntity> queryPageProjectChangesList(ProjectEntity entity);

	
	/**
	 * 
	 * 函数功能描述：获得最大变更次数
	 * @return
	 */
	public Integer getMaxSort(ProjectChangesEntity projectChange);
	
	/**
	 * 
	 * 函数功能描述：获得专家集合
	 * @param result
	 * @return
	 */
	public List<ExpertInfoEntity> queryExpertList(ResultEntity result);
	
	/**
	 * 
	 * 函数功能描述：查询项目变更记录详情列表
	 * @param projectChange
	 * @return
	 */
	public List<ProjectChangesEntity> queryProChangeList(ProjectChangesEntity projectChange);
	
	/**
	 * 
	 * 函数功能描述：查询短信模板内容
	 * @param condition
	 * @return
	 */
	public List<ConditionEntity> querySysMessageTemplateList(ConditionEntity condition);

}
