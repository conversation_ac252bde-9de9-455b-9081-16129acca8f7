package com.hzw.sunflower.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hzw.sunflower.common.BaseBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @ClassName:BondWaterReturn
 * @Auther: lijinxin
 * @Description: 保证金其他流水退回表
 * @Date: 2023/5/11 14:34
 * @Version: v1.0
 */
@Data
@TableName("t_bond_water_return")
@ApiModel(value = "BondWaterReturn对象", description = "保证金流水标签退回表")
public class BondWaterReturn extends BaseBean implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("流水id")
    @TableField("water_id")
    private Long waterId;

    @ApiModelProperty("状态1 退回")
    @TableField("status")
    private Integer status;

    @ApiModelProperty("回单文件")
    @TableField("oss_file_id")
    private Long ossFileId;


    @ApiModelProperty("退回时间")
    @TableField("return_time")
    private Date returnTime;








}
