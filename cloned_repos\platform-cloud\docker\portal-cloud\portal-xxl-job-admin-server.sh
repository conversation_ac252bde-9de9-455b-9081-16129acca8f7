#!/bin/sh
#cp TEMPLATE FILE
docker stop portal-xxl-admin || true
docker rm portal-xxl-admin || true
docker rmi registry.cn-hangzhou.aliyuncs.com/hzw-jtcc/jtcc:portal-xxl-admin-1.0.0 || true
docker pull registry.cn-hangzhou.aliyuncs.com/hzw-jtcc/jtcc:portal-xxl-admin-1.0.0
docker run -itd --net=host  --log-opt max-size=100m --log-opt max-file=3  --name portal-xxl-admin -v /home/<USER>/plan1/portal-cloud/config/portal-xxl-admin:/hzw/xxl-job-admin/config -v /data/log:/hzw/xxl-job-admin/logs  registry.cn-hangzhou.aliyuncs.com/hzw-jtcc/jtcc:portal-xxl-admin-1.0.0
