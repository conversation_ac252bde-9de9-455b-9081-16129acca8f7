<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hzw.sunflower.dao.ProjectIndustryDistributionMapper" >

    <delete id="deleteCurrentMonth" parameterType="string">
        delete from t_project_industry_distribution where 1 = 1
        <if test="submitEndTime !=null and submitEndTime !=''">
            AND DATE_FORMAT(submit_end_time, '%Y-%m' ) = #{submitEndTime}
        </if>
    </delete>

    <insert id="insertBatch" parameterType="java.util.List">
        INSERT INTO t_project_industry_distribution
        (project_id,
        project_number,
        project_name,
        principal_company,
        user_name,
        package_number,
        first_level_code,
        first_level_name,
        second_level_code,
        second_level_name,
        expert_specialty_id,
        entrust_money,
        entrust_currency,
        exchange_rate,
        entrust_money_of_yuan,
        submit_end_time,
        city_code,
        city_name,
        department_id,
        department_name)
        VALUES
        <foreach collection ="industryList" item="industry" separator =",">
            (#{industry.projectId},
            #{industry.projectNumber},
            #{industry.projectName},
            #{industry.principalCompany},
            #{industry.userName},
            #{industry.packageNumber},
            #{industry.firstLevelCode},
            #{industry.firstLevelName},
            #{industry.secondLevelCode},
            #{industry.secondLevelName},
            #{industry.expertSpecialtyId},
            #{industry.entrustMoney},
            #{industry.entrustCurrency},
            #{industry.exchangeRate},
            #{industry.entrustMoneyOfYuan},
            #{industry.submitEndTime},
            #{industry.cityCode},
            #{industry.cityName},
            #{industry.departmentId},
            #{industry.departmentName})
        </foreach >
    </insert>

    <select id="selectProjectIndustryDistributionList"  resultType="com.hzw.sunflower.entity.ProjectIndustryDistribution">
        SELECT
        bs.project_id,
        bs.package_number,
        bs.entrust_money,
        bs.entrust_currency,
        bs.submit_end_time,
        es.expert_specialty_id,
        dic.`value` AS exchangeRate
        FROM
        t_project_bid_section bs
        LEFT JOIN t_section_expert_specialty es ON bs.id = es.section_id
        LEFT JOIN t_dictionary dic ON bs.entrust_currency = dic.id
        WHERE
        DATE_FORMAT( bs.submit_end_time, '%Y-%m' ) &gt;= #{startDate}
        AND DATE_FORMAT( bs.submit_end_time, '%Y-%m' ) &lt;= #{endDate}
        AND bs.is_delete = 0
    </select>


    <select id="selectProjectIndustryList"  resultType="com.hzw.sunflower.entity.ProjectIndustryDistribution">
        SELECT
        *
        FROM
        t_project_industry_distribution
        WHERE
        DATE_FORMAT( submit_end_time, '%Y-%m' ) &gt;= #{startDate}
        AND DATE_FORMAT( submit_end_time, '%Y-%m' ) &lt;= #{endDate}
    </select>

</mapper>
