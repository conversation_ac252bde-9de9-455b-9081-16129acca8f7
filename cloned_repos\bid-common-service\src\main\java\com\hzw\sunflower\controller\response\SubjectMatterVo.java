package com.hzw.sunflower.controller.response;

import com.hzw.sunflower.entity.Dictionary;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 字典表 返回实体
 *
 * <AUTHOR>
 * @version 1.0.0 2021-04-12
 */
@ApiModel(description = "字典表 ")
@Data
public class SubjectMatterVo extends Dictionary {

    @ApiModelProperty("一级分类数量")
    private Integer oneCount;

    @ApiModelProperty("二级分类数量")
    private Integer twoCount;


    @ApiModelProperty("三级分类数量")
    private Integer threeCount;


    @ApiModelProperty("四级分类数量")
    private Integer fourCount;
}