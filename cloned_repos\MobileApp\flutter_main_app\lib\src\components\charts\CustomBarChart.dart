///***
/// * 自定义柱状图
/// */

import 'package:flutter/material.dart';
import 'package:charts_flutter/flutter.dart' as charts;

import 'package:flutter_main_app/src/components/charts/CustomLegendSymbolRenderer.dart' show RectangleSymbolRenderer;

class CustomBar<PERSON>hart extends StatelessWidget {

  const CustomBarChart({
    this.seriesList,
    this.animate = true,
  });

  /// * 数据列表
  final List<charts.Series> seriesList;

  /// * 是否启用动画
  final bool animate;

  /// * 轴线文本样式
  static const charts.TextStyleSpec labelStyle = charts.TextStyleSpec(
    fontSize: 12,
    // color: charts.MaterialPalette.gray.shadeDefault,
    color: charts.Color(a: 255, r: 0x99, g: 0x99, b: 0x99),
  );

  /// * 纵轴分隔线数
  static const int tickCount = 6;

  /// * 分隔线样式
  static const charts.LineStyleSpec lineStyle = charts.LineStyleSpec(
    color: charts.Color(a: 255, r: 0xde, g: 0xde, b: 0xde),
    thickness: 1,
  );

  @override
  Widget build(BuildContext context) {
    return charts.BarChart(
      seriesList,
      animate: animate,
      defaultRenderer: charts.BarRendererConfig(
        /// * 图例形状
        // symbolRenderer: charts.RectSymbolRenderer(),
        symbolRenderer: RectangleSymbolRenderer(),
        /// * 分组
        groupingType: charts.BarGroupingType.grouped,
        strokeWidthPx: 2.0,
      ),
      /// * 2 条纵轴保持分隔线同步
      /// * 主纵轴样式
      primaryMeasureAxis: charts.NumericAxisSpec(
        renderSpec: charts.GridlineRendererSpec(
          labelStyle: labelStyle,
          lineStyle: lineStyle,
        ),
        tickProviderSpec: charts.BasicNumericTickProviderSpec(
          desiredTickCount: tickCount,
        ),
      ),
      /// * 次纵轴样式
      secondaryMeasureAxis: charts.NumericAxisSpec(
        renderSpec: charts.GridlineRendererSpec(
          labelStyle: labelStyle,
          labelRotation: -45,
          lineStyle: lineStyle,
        ),
        tickProviderSpec: charts.BasicNumericTickProviderSpec(
          desiredTickCount: tickCount,
        ),
      ),
      /// * 横轴样式
      /// TODO: 使横轴文本倾斜方向保持与纵轴一致
      /// * 思路：文本方向改为从右到左
      domainAxis: charts.OrdinalAxisSpec(
        renderSpec: charts.SmallTickRendererSpec(
          labelStyle: labelStyle,
          labelRotation: 45,
          lineStyle: lineStyle,
          // labelAnchor: charts.TickLabelAnchor.after,
          // labelOffsetFromAxisPx: 0,
          // labelOffsetFromTickPx: 0,
          // labelJustification: charts.TickLabelJustification.outside,
        ),
      ),
      behaviors: [
        /// * 图例
        charts.SeriesLegend(
          position: charts.BehaviorPosition.top,
          outsideJustification: charts.OutsideJustification.endDrawArea,
          showMeasures: true,
          measureFormatter: ((num value) {
            return value == null ? '0' : '${value.toInt()}';
          }),
          entryTextStyle: charts.TextStyleSpec(
            fontSize: 12,
            color: charts.Color(a: 255, r: 0x41, g: 0x41, b: 0x41),
          ),
        ),
      ],
    );
  }

}
