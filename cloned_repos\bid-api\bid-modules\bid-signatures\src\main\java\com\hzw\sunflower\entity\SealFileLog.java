package com.hzw.sunflower.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import java.io.Serializable;
import java.util.Date;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
* Created by pj on 2023/11/09
*/
@ApiModel("t_seal_file_log表")
@TableName("t_seal_file_log")
@Data
public class SealFileLog implements Serializable {
    private Long id;

    /**
     * 文件id
     *
     * @mbg.generated
     */
   @ApiModelProperty("文件id")
    private Long fileId;

    /**
     * 盖章文件
     *
     * @mbg.generated
     */
    @ApiModelProperty("盖章文件")
    private Long sealFile;



    private static final long serialVersionUID = 1L;
}