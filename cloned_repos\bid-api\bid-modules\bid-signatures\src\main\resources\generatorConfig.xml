<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE generatorConfiguration PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN" "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd" >
<generatorConfiguration>

    <!-- 配置文件 -->
    <properties resource="generator.properties"></properties>

    <context id="MyContext" targetRuntime="MyBatis3" defaultModelType="flat">

        <property name="javaFileEncoding" value="UTF-8"/>
        <!-- 由于beginningDelimiter和endingDelimiter的默认值为双引号(")，在Mysql中不能这么写，所以还要将这两个默认值改为`  -->
        <property name="beginningDelimiter" value="`"/>
        <property name="endingDelimiter" value="`"/>
        <!-- 为生成的Java模型创建一个toString方法 -->
       

        <!-- 为生成的Java模型类添加序列化接口，并生成serialVersionUID字段 -->
        <plugin type="com.hzw.sunflower.util.plugin.SerializablePlugin">
            <property name="suppressJavaInterface" value="false"/>
        </plugin>


        <!-- 去掉get set方法 -->
        <plugin type="com.hzw.sunflower.util.plugin.IngoreSetterAndGetterPlugin" />

        <!-- 生成一个新的selectByExample方法，这个方法可以接收offset和limit参数，主要用来实现分页，只支持mysql(已使用pagehelper代替) -->
        <!--<plugin type="com.zheng.common.plugin.PaginationPlugin"></plugin>-->

        <!-- 生成在XML中的<cache>元素 -->
        

        <!-- Java模型生成equals和hashcode方法 -->
       

        <!-- 生成的代码去掉注释 -->
        <commentGenerator type="com.hzw.sunflower.util.plugin.CommentGenerator">
            <property name="suppressAllComments" value="true" />
            <property name="suppressDate" value="true"/>
        </commentGenerator>

        <!-- 数据库连接 -->
        <jdbcConnection driverClass="${generator.jdbc.driver}"
                        connectionURL="${generator.jdbc.url}"
                        userId="${generator.jdbc.username}"
                        password="bid-test52" />
        <javaTypeResolver type="org.mybatis.generator.internal.types.JavaTypeResolverDefaultImpl">
            <!--
                true：使用BigDecimal对应DECIMAL和 NUMERIC数据类型
                false：默认,
                    scale>0;length>18：使用BigDecimal;
                    scale=0;length[10,18]：使用Long；
                    scale=0;length[5,9]：使用Integer；
                    scale=0;length<5：使用Short；
             -->
            <property name="forceBigDecimals" value="false"/>

        </javaTypeResolver>
        <!-- model生成 -->
        <javaModelGenerator targetPackage="com.hzw.sunflower.entity" targetProject="/Users/<USER>/Public/work/bid-api/bid-modules/bid-signatures/src/main/java" />

        <!-- MapperXML生成 -->

        <!-- 生成map.xml文件存放地址 -->


        <!-- 需要映射的表 -->
                        <table schema="BID-TEST52" tableName="t_seal_management_operate_log" domainObjectName="SealManagementOperateLog" enableCountByExample="false" enableUpdateByExample="false" enableSelectByExample="false" selectByExampleQueryId="false" enableDeleteByExample="false">

                        <generatedKey column="id" sqlStatement="MySql" identity="false" type="pre" />
                </table>
            </context>
</generatorConfiguration>
