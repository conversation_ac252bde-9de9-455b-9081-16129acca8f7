package com.hzw.sunflower.service;

import com.hzw.sunflower.entity.BidDocColumnConfig;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 招标文件编制字段配置展示表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-26
 */
public interface BidDocColumnConfigService extends IService<BidDocColumnConfig> {

    /**
     * 根据模板id获取配置信息
     * @param templateId
     * @return
     */
    Map<Long, List<BidDocColumnConfig>> getConfigByTemplateId(Long templateId);
}
