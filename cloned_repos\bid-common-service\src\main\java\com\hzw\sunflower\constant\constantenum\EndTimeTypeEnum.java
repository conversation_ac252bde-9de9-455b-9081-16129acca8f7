package com.hzw.sunflower.constant.constantenum;

/**
 * 文件响应时间类型
 *
 * <AUTHOR>
 */
public enum EndTimeTypeEnum {

    CLEAR_TIME(1, "明确时间"),
    FURTHER_NOTICE(2, "另行通知"),
    PREVIOUS_DAY(3, "投标文件递交截止时间前一天");

    private Integer type;
    private String desc;

    EndTimeTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
