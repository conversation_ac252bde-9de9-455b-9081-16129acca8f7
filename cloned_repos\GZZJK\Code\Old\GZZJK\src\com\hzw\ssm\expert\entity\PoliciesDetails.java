package com.hzw.ssm.expert.entity;

import com.hzw.ssm.fw.util.Page;


import java.util.Date;

/**
 * 政策法规目录详情(T_POLICIES_DETAILS)
 * 
 * <AUTHOR>
 * @version 1.0.0 2021-02-04
 */
public class PoliciesDetails implements java.io.Serializable {
    /** 版本号 */
    private static final long serialVersionUID = -6190769762298115492L;

    /* This code was generated by TableGo tools, mark 1 begin. */

    /** 主键 */
    private String id;

    /** 主题 */
    private String title;

    /** 所属目录 */
    private String policiesId;

    /** 状态 新增默认:0待发布  1编辑中、2已发布、3已下架 */
    private Integer status;

    /** 置顶 0不是置顶 1是置顶 */
    private Integer top;

    /** 文件路径 */
    private String fileId;

    /** 文件名称 */
    private String fileName;
    /** 文件大小 */
    private Long fileSize;






    /** 发布时间 */
    private Date onTime;

    /** 下架时间 */
    private Date offTime;

    /** createUser */
    private String createUser;

    /** createTime */
    private Date createTime;
    private String beginTime;

    /** updateUser */
    private String updateUser;

    /** updateTime */
    private Date updateTime;

    /** src */
    private String src;

    /** txt */
    private String txt;
    private Page page;


    private String topMsg;
    private String statusMsg;


    private String updSort;
    private String creSort;

    private String onSort;
    private String offSort;

    private String order;
    // 是否下载 0-否，1-是
    private int collectionStatus;
    private String expertId;

    private String pstatus;

    private String numberNo;


    public String getNumberNo() {
        return numberNo;
    }

    public void setNumberNo(String numberNo) {
        this.numberNo = numberNo;
    }

    public String getPstatus() {
        return pstatus;
    }

    public void setPstatus(String pstatus) {
        this.pstatus = pstatus;
    }

    public String getOrder() {
        return order;
    }

    public void setOrder(String order) {
        this.order = order;
    }

    public String getUpdSort() {
        return updSort;
    }

    public void setUpdSort(String updSort) {
        this.updSort = updSort;
    }

    public String getCreSort() {
        return creSort;
    }

    public void setCreSort(String creSort) {
        this.creSort = creSort;
    }

    public String getOnSort() {
        return onSort;
    }

    public void setOnSort(String onSort) {
        this.onSort = onSort;
    }

    public String getOffSort() {
        return offSort;
    }

    public void setOffSort(String offSort) {
        this.offSort = offSort;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public Long getFileSize() {
        return fileSize;
    }

    public void setFileSize(Long fileSize) {
        this.fileSize = fileSize;
    }

    public String getTopMsg() {
        return topMsg;
    }

    public void setTopMsg(String topMsg) {
        this.topMsg = topMsg;
    }

    public String getStatusMsg() {
        return statusMsg;
    }

    public void setStatusMsg(String statusMsg) {
        this.statusMsg = statusMsg;
    }

    /* This code was generated by TableGo tools, mark 1 end. */

    /* This code was generated by TableGo tools, mark 2 begin. */


    public Page getPage() {
        return page;
    }

    public void setPage(Page page) {
        this.page = page;
    }

    /**
     * 获取主键
     * 
     * @return 主键
     */
    public String getId() {
        return this.id;
    }

    /**
     * 设置主键
     * 
     * @param id
     *          主键
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * 获取主题
     * 
     * @return 主题
     */
    public String getTitle() {
        return this.title;
    }

    /**
     * 设置主题
     * 
     * @param title
     *          主题
     */
    public void setTitle(String title) {
        this.title = title;
    }

    /**
     * 获取所属目录
     * 
     * @return 所属目录
     */
    public String getPoliciesId() {
        return this.policiesId;
    }

    /**
     * 设置所属目录
     * 
     * @param policiesId
     *          所属目录
     */
    public void setPoliciesId(String policiesId) {
        this.policiesId = policiesId;
    }

    /**
     * 获取状态 新增默认:0待发布  1编辑中、2已发布、3已下架
     * 
     * @return 状态 新增默认
     */
    public Integer getStatus() {
        return this.status;
    }

    /**
     * 设置状态 新增默认:0待发布  1编辑中、2已发布、3已下架
     *
     * @param status
     *          状态 新增默认
     */
    public void setStatus(Integer status) {
        this.status = status;
    }

    /**
     * 获取置顶 0不是置顶 1是置顶
     * 
     * @return 置顶 0不是置顶 1是置顶
     */
    public Integer getTop() {
        return this.top;
    }

    /**
     * 设置置顶 0不是置顶 1是置顶
     * 
     * @param top
     *          置顶 0不是置顶 1是置顶
     */
    public void setTop(Integer top) {
        this.top = top;
    }

    /**
     * 获取文件路径
     * 
     * @return 文件路径
     */
    public String getFileId() {
        return this.fileId;
    }

    /**
     * 设置文件路径
     * 
     * @param fileId
     *          文件路径
     */
    public void setFileId(String fileId) {
        this.fileId = fileId;
    }

    /**
     * 获取发布时间
     * 
     * @return 发布时间
     */
    public Date getOnTime() {
        return this.onTime;
    }

    /**
     * 设置发布时间
     * 
     * @param onTime
     *          发布时间
     */
    public void setOnTime(Date onTime) {
        this.onTime = onTime;
    }

    /**
     * 获取下架时间
     * 
     * @return 下架时间
     */
    public Date getOffTime() {
        return this.offTime;
    }

    /**
     * 设置下架时间
     * 
     * @param offTime
     *          下架时间
     */
    public void setOffTime(Date offTime) {
        this.offTime = offTime;
    }

    /**
     * 获取createUser
     * 
     * @return createUser
     */
    public String getCreateUser() {
        return this.createUser;
    }

    /**
     * 设置createUser
     * 
     * @param createUser
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    /**
     * 获取createTime
     * 
     * @return createTime
     */
    public Date getCreateTime() {
        return this.createTime;
    }

    /**
     * 设置createTime
     * 
     * @param createTime
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取updateUser
     * 
     * @return updateUser
     */
    public String getUpdateUser() {
        return this.updateUser;
    }

    /**
     * 设置updateUser
     * 
     * @param updateUser
     */
    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }

    /**
     * 获取updateTime
     * 
     * @return updateTime
     */
    public Date getUpdateTime() {
        return this.updateTime;
    }

    /**
     * 设置updateTime
     * 
     * @param updateTime
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 获取src
     * 
     * @return src
     */
    public String getSrc() {
        return this.src;
    }

    /**
     * 设置src
     * 
     * @param src
     */
    public void setSrc(String src) {
        this.src = src;
    }

    /**
     * 获取txt
     * 
     * @return txt
     */
    public String getTxt() {
        return this.txt;
    }

    /**
     * 设置txt
     * 
     * @param txt
     */
    public void setTxt(String txt) {
        this.txt = txt;
    }

    /* This code was generated by TableGo tools, mark 2 end. */

    public String getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(String beginTime) {
        this.beginTime = beginTime;
    }

    public int getCollectionStatus() {
        return collectionStatus;
    }

    public void setCollectionStatus(int collectionStatus) {
        this.collectionStatus = collectionStatus;
    }

    public String getExpertId() {
        return expertId;
    }

    public void setExpertId(String expertId) {
        this.expertId = expertId;
    }
}