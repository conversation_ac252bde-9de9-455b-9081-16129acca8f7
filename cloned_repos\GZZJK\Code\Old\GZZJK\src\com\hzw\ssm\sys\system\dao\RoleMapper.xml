<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper 
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hzw.ssm.sys.system.dao.RoleMapper">

	<!-- 如果需要查询缓存，将cache的注释去掉即可
	<cache />
	-->

	<!-- 根据页面条件查询权限信息列表 -->
	<select id="queryRoleList" parameterType="RoleEntity"
		resultType="RoleEntity">
		SELECT
		role_id as roleId,role_name as roleName,create_time,delete_flag 
		from ts_role where delete_flag='0' order by create_time desc
	</select>
	<!-- 查询所有可用的菜单列表 -->
	<select id="queryMenuList" resultType="MenuEntity">
		select t.menu_id,t.parent_menu_id,t.menu_name  from ts_menu t   
			where delete_flag='0' order by sortno desc
	</select>
	<!-- 根据角色id查询菜单 -->
	<select id="getMenuIdsByRoleId" parameterType="Integer"
		resultType="RoleEntity">
		select relation_id,role_id,menu_id from
		t_sys_role_menu_relation where role_id = #{role_id}
	</select>

 
	<!-- 保存角色信息 -->
	<insert id="saveRoleInfo" parameterType="RoleEntity">
		insert into ts_role(role_id,role_name,delete_flag,create_time,create_id)
		values(#{roleId},#{roleName},0,#{create_time},'${create_id}')
	</insert>
	<!-- 更新角色信息 -->
	<insert id="updateRoleInfo" parameterType="RoleEntity">
		update ts_role set  role_name=#{roleName},modify_time=#{modify_time},modify_id='${modify_id}'
			where role_id=#{roleId}
	</insert>
	<!-- 根据角色名称查询对象 -->
	<select id="getRoleInfoByName" parameterType="String" resultType="RoleEntity">
		SELECT
		role_id as roleId,role_name as roleName,create_time  ,delete_flag 
		from ts_role where role_name=#{roleName} and delete_flag='0'
	</select>
	<!-- 根据角色id查询对象-->
	<select id="getRoleInfoById" parameterType="String" resultType="RoleEntity">
		SELECT
			role_id as roleId,role_name as roleName,create_time  ,delete_flag  
		from ts_role where role_id=#{roleId} 
	</select>
	
	<!-- 根据角色ID删除角色 -->
	<update id="deleteRoleInfo" parameterType="String">
		update  ts_role set delete_flag='1' where role_id = #{roleId}
	</update>
	<!--保存权限信息-->
	<insert id="modifyRoleRelation" parameterType="java.util.List" >
		insert into ts_auth(auth_id,role_id,menu_id)
		<foreach  collection="list" item="item" separator="union all">
			select #{item.authId},#{item.roleId},#{item.menuId} from dual
		</foreach>
	</insert>
	<!-- 根据角色id删除所有权限-->
	<delete id="deleteRoleRelation" parameterType="String">
		delete from ts_auth where role_id=#{roleId}
	</delete>
	<!-- 根据角色id查询所有有权限的菜单-->
	<select id="getAuthListByRoleId" parameterType="String" resultType="String">
		SELECT menu_id from ts_auth where role_id=#{roleId} 
	</select>
	
</mapper>