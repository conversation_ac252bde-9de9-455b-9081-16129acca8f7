package com.hzw.sunflower.controller.response;

import com.hzw.sunflower.entity.BondWater;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @ClassName:BondWaterReturnVo
 * @Auther: lijinxin
 * @Description: 保证金流水退还列表vo
 * @Date: 2023/5/11 17:02
 * @Version: v1.0
 */
@Data
@ApiModel(value = "BondWaterReturn对象", description = "保证金流水退还表")
public class BondWaterReturnVo extends BondWater {

     @ApiModelProperty("流水标签 1收款 2其他 3退汇 4错汇 5 标书费")
     private Integer label;

    @ApiModelProperty("委托项目编号")
    private String purchaseNumber;

    @ApiModelProperty("退回状态 1 退回 0 未退回")
    private Integer returnStatus;

    @ApiModelProperty("退回描述")
    private String notes;

    @ApiModelProperty("退回时间")
    private Date returnTime;

    @ApiModelProperty("退单回文件")
    private Long ossFileId;




}
