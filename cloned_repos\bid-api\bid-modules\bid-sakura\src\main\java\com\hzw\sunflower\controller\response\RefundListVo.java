package com.hzw.sunflower.controller.response;

import com.hzw.sunflower.entity.BondRefundDetails;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class RefundListVo implements Serializable {

    @ApiModelProperty(value = "项目id")
    private Long projectId;

    @ApiModelProperty(value = "项目编号")
    private String projectNum;

    @ApiModelProperty(value = "项目名称")
    private String projectName;

    @ApiModelProperty(value = "联系人")
    private String userName;

    @ApiModelProperty(value = "供应商名称")
    private String companyName;

    @ApiModelProperty(value = "收款银行")
    private String receiveBank;

    @ApiModelProperty(value = "评标情况")
    private String bidType;

    @ApiModelProperty(value = "中标情况")
    private String bidStatus;

    @ApiModelProperty(value = "代理服务费")
    private BigDecimal agencyFee;

    @ApiModelProperty(value = "原代理服务费")
    private BigDecimal formerAgencyFee;

    @ApiModelProperty(value = "退还金额")
    private BigDecimal refundMoney;

    @ApiModelProperty(value = "供应商id")
    private Long companyId;

    @ApiModelProperty(value = "标段id")
    private Long sectionId;

    @ApiModelProperty(value = "退还流程code")
    private String refundProcessCode;

    @ApiModelProperty(value = "状态：1：待处理  2：已退回    3：退还成功 4：退还失败")
    private Integer status;

    /**
     * 是否显示退款相关接口
     */
    @ApiModelProperty(value = "是否显示退款相关接口")
    private AppingTaskVO refundTask;

    @ApiModelProperty(value = "申请时间")
    private Date submitTime;

    @ApiModelProperty(value = "到账金额")
    private BigDecimal amount;

    @ApiModelProperty(value = "异常原因")
    private String abnormalReson;

    @ApiModelProperty(value = "到账日期")
    private String date;

    @ApiModelProperty(value = "到账时间")
    private String time;

    @ApiModelProperty(value = "所属处室")
    private String departmentName;

    @ApiModelProperty(value = "退还id")
    private Long refundId;

    @ApiModelProperty(value = "服务费收取方式：1：保证金转代理服务费  2：保证金全额转代理服务费并补差价  3：保证金全额退还，代理服务费另汇")
    private Integer agencyFeeType;

    @ApiModelProperty(value = "服务费收取对象：0：供应商 1：招标人")
    private Integer agencyFeeObj;

    @ApiModelProperty(value = "利息")
    private BigDecimal rates;

    @ApiModelProperty(value = "回单附件")
    private String receiptFiles;

    @ApiModelProperty(value = "退还时间")
    private Date refundTime;

    @ApiModelProperty(value = "付款凭证/授权函")
    private String  fileIds;

    @ApiModelProperty(value = "退还类型：1 ：项目经理申请退还  2：异常保证金退还  3：供应商申请退还")
    private String refundType;

    @ApiModelProperty(value = "供应商名称")
    private String supplierName;

    @ApiModelProperty(value = "退还凭证")
    private String refundFiles;

    @ApiModelProperty(value = "1:按流水收取  2：无流水收取")
    private Integer bondType;

    @ApiModelProperty(value = "退还方式 1：收取并退还剩余保证金  2：收取并保留剩余保证金")
    private Integer refundMode;

    @ApiModelProperty(value = "代理服务费类型 1定额 2比例 3 分批 4其他")
    private Integer feeType;

    @ApiModelProperty(value = "包段编号")
    private String packageNumber;

    @ApiModelProperty(value = "总金额")
    private BigDecimal totalAmount;

    @ApiModelProperty(value = "是否已收代理服务费  1：是  2：否")
    private Integer isAgencyFee;

    @ApiModelProperty(value = "备注")
    private String remark;

    private List<BondRefundDetails> bondRefundDetails;

}
