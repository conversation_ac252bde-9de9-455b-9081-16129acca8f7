/*
 * 公司名称：江苏华招网
 * 版权信息：江苏华招网版权
 * 描述：文件相关操作
 * 文件名称：FileUtils.java
 * 修改时间：2017年10月16日
 * 修改人：ypp
 * 修改内容：
 * 跟踪单号：
 * 修改单号 ：
 */

package com.hzw.ssm.util.file;

import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.FileWriter;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;

import com.hzw.ssm.util.empty.EmptyUtils;

/**
 * <一句话功能简述> 文件相关操作
 * <功能详细描述>
 * <AUTHOR>
 * @version V1.0[产品/模块版本]
 */
public class FileUtils {
    
    private final static String[] SUPPORT_FILE_SUFFIX_ARR = new String[] { "docx", "doc", "wps",
            "pdf", "zip", "rar", "7z", "bmp", "jpg", "jpeg", "png", "gif", "xls", "xlsx", "tiff",
            "pcx", "tga", "exif", "fpx", "svg", "psd", "cdr", "pcd", "dxf", "ufo", "eps", "ai",
            "raw" };
    
    /**
     * 
     * 函数功能描述：把路径中的分隔符替换成允许系统中的符号
     * @param path
     * @return
     */
    public static String replaceSeparator(String path) {
        if (File.separator.equals("/")) {
            return path.replaceAll("[\\\\/]+", "/");
        }
        return path.replaceAll("[\\\\/]+", "\\\\");
    }
    
    /**
     * 
     * 函数功能描述：根据字符串路径校验路径以及文件是否存在，如果不存在那么就创建
     * @param fileStr
     * @return
     * @throws IOException
     */
    public static File checkAndNewCreateFile(String fileStr) throws IOException {
        if (fileStr == null || fileStr.equals("")) {
            return null;
        }
        File file = new File(fileStr);
        if (!file.getParentFile().exists()) {
            file.getParentFile().mkdirs();
        }
        if (file.exists()) {
            file.delete();
        }
        if (!file.exists()) {
            file.createNewFile();
        }
        
        return file;
    }
    
    /**
     * 
     * 函数功能描述：根据字符串路径校验路径以及文件是否存在，如果不存在那么就创建
     * @param fileName 文件名称
     * @return boolean，如果支持该文件上传，那么返回true，否则false
     * @throws IOException
     */
    public static boolean isSupportUploadFileType(String fileName) {
        return isSupportUploadFileType(fileName, SUPPORT_FILE_SUFFIX_ARR);
    }
    
    /**
     * 
     * 函数功能描述：根据字符串路径校验路径以及文件是否存在，如果不存在那么就创建
     * @param fileName 文件名称
     * @param suffix 支持文件上传的后缀名数组，无须包含“.”
     * @return boolean，如果支持该文件上传，那么返回true，否则false
     * @throws IOException
     */
    public static boolean isSupportUploadFileType(String fileName, String... suffix) {
        if (EmptyUtils.isEmptyAfterTrim(fileName)) {
            return false;
        }
        int lastDotIndex = fileName.lastIndexOf(".");
        if (lastDotIndex == -1) {
            return false;
        }
        String fileSuffix = fileName.substring(lastDotIndex + 1);
        if (EmptyUtils.isEmptyAfterTrim(fileSuffix)) {
            return false;
        }
        fileSuffix = fileSuffix.trim();
        boolean isSupport = false;
        
        for ( String suffixItem : suffix ) {
            if (fileSuffix.equalsIgnoreCase(suffixItem)) {
                isSupport = true;
                break;
            }
        }
        
        return isSupport;
    }
    
    
    /**
     *	 根据路径判断文件是否存在
     * @param path
     * @return
     */
    public static boolean checkedFilePath(String path) {
    	File file = new File(path);
    	
    	if(file.exists()) {
    		return true;
    	}else {
    		return false;
    	}
    }
    /**
     * 函数功能描述：以指定的编码格式读取指定路径文件的内容
     * @param path String
     * @param encoding String
     * @return content String
     * @throws IOException
     */
    public static String readFileContent(String path, String encoding) throws IOException {
        String content = "";
        
        StringBuffer sb = new StringBuffer();
        BufferedReader in = null;
        try {
            InputStream _in = new FileInputStream(path);
            in = new BufferedReader(new InputStreamReader(_in, encoding));
            
            char[] buf = new char[512];
            int readCount = in.read(buf, 0, 512);
            while (readCount >= 0) {
                sb.append(buf, 0, readCount);
                readCount = in.read(buf, 0, 512);
            }
            content = sb.toString();
        } catch (Exception e) {
            e.printStackTrace();
        }
        finally {
            if (in != null) {
                in.close();
            }
        }
        
        return content;
    }
    
    /**
     * 函数功能描述：替换部分字符串
     * @param content String
     * @param target String
     * @param replacement String
     * @return
     */
    public static String replaceSubString(String content, String target, String replacement) {
        if (content == null) {
            content = "";
        }
        if (EmptyUtils.isEmpty(replacement)) {
            return content;
        }
        
        return content.replace(target, replacement);
    }
    
    /**
     * 函数功能描述：TODO
     * @param path
     * @param content
     */
    public static void writeFile(String path, String content) {
        FileWriter writer;
        try {
            if (path != null && !path.equals("")) {
                File file = new File(path);
                if (!file.getParentFile().exists()) {
                    file.getParentFile().mkdirs();
                }
                if (file.exists()) {
                    file.delete();
                }
                if (!file.exists()) {
                    file.createNewFile();
                }
                
                writer = new FileWriter(path);
                writer.write(content);
                writer.flush();
                writer.close();
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
    
    /**
     * 函数功能描述：按照指定的编码格式写入文件
     * @param path
     * @param content
     */
    public static void writeFileWithEncoding(String path, String content,String encoding) {
        try {
            if (path != null && !path.equals("")) {
                File file = new File(path);
                if (!file.getParentFile().exists()) {
                    file.getParentFile().mkdirs();
                }
                if (file.exists()) {
                    file.delete();
                }
                if (!file.exists()) {
                    file.createNewFile();
                }
                 
                BufferedWriter writer = new BufferedWriter(new OutputStreamWriter(  
                        new FileOutputStream(file), encoding));  
                writer.write(content); 
                writer.flush();
                writer.close();  
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}
