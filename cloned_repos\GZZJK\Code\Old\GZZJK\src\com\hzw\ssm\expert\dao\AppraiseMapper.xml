<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper 
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hzw.ssm.expert.dao.AppraiseMapper">
	<!-- 查询专家反馈项目列表 -->
	<select id="queryPageProjectInfo" resultType="ProjectEntity" parameterType="ProjectEntity">
		SELECT
			p.project_id as projectId,
			p.project_no as projectNo,
			p.project_name as projectName,
			p.bid_time as bidTime,
			p.tender,
			c.id as conditionId,
			c.method as method,
			(
				SELECT
					count(er.id) 
				FROM
					t_extract_result er 
				WHERE
					er.join_status = 0 AND er.delete_flag = 0 AND er.condition_id = c.id AND er.is_appraise=1	
			) as appNum,
		    (
		    	SELECT
					count(er.id) 
				FROM 
					t_extract_result er 
				WHERE
					er.join_status = 0 AND er.delete_flag = 0 AND er.condition_id = c.id
			) as totalNum
		FROM 
			t_condition c
		LEFT JOIN 
			t_project p 
		ON 
			p.project_id = c.project_id
		WHERE
			 p.delete_flag = 0 and p.status in (3 ,10) AND c.delete_flag = 0 
		<if test="null != bidStartTimeStr and '' != bidStartTimeStr">
			AND p.bid_time >= to_date(#{bidStartTimeStr},'yyyy-MM-dd hh24:mi:ss ')
		</if>
		<if test="null != bidEndTimeStr and '' != bidEndTimeStr">
			AND p.bid_time &lt;= to_date(#{bidEndTimeStr},'yyyy-MM-dd hh24:mi:ss ')
		</if>	 
		<if test="null != createUser and '' != createUser">
			AND p.create_user = #{createUser}
		</if>
		<if test="null != projectName and '' != projectName">
			AND p.project_name like '%${projectName}%'
		</if>
		<if test="null != tender and '' != tender">
			AND p.tender like '%${tender}%'
		</if>
		<if test="null != bidStartTime and '' != bidStartTime">
			AND p.bid_time >= #{bidStartTime}
		</if>
		<if test="null != bidEndTime and '' != bidEndTime">
			AND p.bid_time &lt;= #{bidEndTime}
		</if>
		ORDER BY
			p.bid_time DESC
	</select>

	<!-- 查询专家反馈专家列表 -->
	<select id="queryExpertAppriaiseList" resultType="ExpertInfoEntity" parameterType="ExpertInfoEntity">
		select distinct er.id as extractResultId,ei.user_name, ei.mobilephone,ei.company,
		er.is_approve as isApprove,er.rejection_reason as rejectionReason,er.appraise_time,er.is_appraise,er.is_reward
		from T_EXTRACT_RESULT er
		left join t_expert_info ei on er.expert_id = ei.user_id
		left join t_condition c on c.id = er.condition_id
		left join t_appraise a on a.extract_result_id = er.id
		where er.delete_flag = 0 and ei.delete_flag = 0 and er.join_status = 0 and c.delete_flag = 0
		<if test="null != conditionId and '' != conditionId">
			and c.id = #{conditionId}
		</if>
		<if test="null != user_name and '' != user_name">
			and ei.user_name like '%${user_name}%'
		</if>
		<if test="null != company and '' != company">
			and ei.company like '%${company}%'
		</if>
		<if test="null != is_appraise">
			and er.is_appraise = #{is_appraise}
		</if>
		order by er.appraise_time desc
	</select>

	<select id="queryIllegalExpertsList2" resultType="ExpertInfoEntity" parameterType="ExpertInfoEntity">
		select distinct er.id as extractResultId,ei.user_name, ei.mobilephone,ei.company,er.appraise_time,er.is_appraise,er.is_reward
		from T_EXTRACT_RESULT er
		left join t_expert_info ei on er.expert_id = ei.user_id
		left join t_condition c on c.id = er.condition_id
		left join t_appraise a on a.extract_result_id = er.id
		where er.delete_flag = 0 and ei.delete_flag = 0 and er.join_status = 0 and c.delete_flag = 0
		<if test="null != conditionId and '' != conditionId">
			and c.id = #{conditionId}
		</if>
		<if test="null != user_name and '' != user_name">
			and ei.user_name like '%${user_name}%'
		</if>
		<if test="null != company and '' != company">
			and ei.company like '%${company}%'
		</if>
		<if test="null != is_appraise and '' != is_appraise">
			and er.is_appraise = #{is_appraise}
		</if>
		order by er.appraise_time desc
	</select>
	
	<!-- 查询专家反馈专家列表 -->
	<select id="queryIllegalExpertsList" resultType="ExpertInfoEntity" parameterType="ExpertInfoEntity">
		select distinct er.id as extractResultId,ei.user_name, ei.mobilephone,ei.company,er.appraise_time,er.is_appraise,er.is_reward
		from t_appraise_illegal TAI
		LEFT JOIN T_EXTRACT_RESULT er ON TAI.EXTRACT_RESULT_ID = ER.ID
		left join t_expert_info ei on er.expert_id = ei.user_id
		left join t_condition c on c.id = er.condition_id
		left join t_appraise a on a.extract_result_id = er.id
		where er.delete_flag = 0 and ei.delete_flag = 0 and er.join_status = 0 and c.delete_flag = 0
		<if test="null != conditionId and '' != conditionId">
			and c.id = #{conditionId}
		</if>
		<if test="null != user_name and '' != user_name">
			and ei.user_name like '%${user_name}%'
		</if>
		<if test="null != company and '' != company">
			and ei.company like '%${company}%'
		</if>
		<if test="null != is_appraise and '' != is_appraise">
			and er.is_appraise = #{is_appraise}
		</if>
		order by er.appraise_time desc
	</select>
	<!-- 根据id查询评价信息表(基础信息) -->
	<select id="queryBaseAppraise" resultType="ExpertInfoEntity" parameterType="String">
		select distinct ei.id,
			ei.user_id,
			er.id as extractResultId,
			ei.user_name, 
			ei.company,
			ei.expert_num,
			er.is_appraise,
			er.appraise_time,
			p.project_no,
			p.project_name,
			decode((select u.user_name from ts_user u where u.user_id = p.manager and u.delete_flag = 0),'',p.manager,
			          (select u.user_name from ts_user u where u.user_id = p.manager and u.delete_flag = 0)) as manager,
			c.id as conditionId,
			er.isillegal,
            er.illegal_file_path,
            er.illegal_file_name,
            er.illegal_detail,p.tender 
		from T_EXTRACT_RESULT er
		left join t_expert_info ei on er.expert_id = ei.user_id
		left join t_condition c on c.id = er.condition_id
		left join t_appraise a on a.extract_result_id = er.id
		left join t_project p on p.project_id = c.project_id
		where er.delete_flag = 0 and ei.delete_flag = 0 and er.join_status = 0 and c.delete_flag = 0 and p.delete_flag = 0
		and er.id = #{extractResultId}
	</select>

	<!-- 根据id查询评价信息表(评分项) -->
	<select id="queryAppraiseInfoById" resultType="AppraiseInfo" parameterType="String">
		select
		ai.appraise_info_id,ai.appraise_column,ai.parent_id,ai.sort,ai.score
		from T_APPRAISE_INFO ai
		where ai.delete_flag = 0 and ai.parent_id =
		#{parentId} order by ai.sort
	</select>
	
	<!-- 根据id查询评价信息表(评分项) -->
	<select id="queryAppraiseInfoById2" resultType="AppraiseInfo2" parameterType="String">
		select
		ai.appraise_info_id,ai.appraise_column,ai.parent_id,ai.sort,ai.score
		from T_APPRAISE_INFO2 ai
		where ai.delete_flag = 0 and ai.parent_id =
		#{parentId} order by ai.sort
	</select>

	<!-- 专家评价 -->
	<insert id="saveAppraise" parameterType="Appraise">
		insert into t_appraise
		(appraise_id, appraise_info_id, extract_result_id, score , appraise_type,APPRAISE_USER,APPRAISE_TIME)
		<foreach collection="list" item="v" index="index" separator="union all">
			select #{v.appraise_id},#{v.appraise_info_id},#{v.extract_result_id},#{v.score},#{v.appraise_type},
			#{v.appraiseUser},#{v.appraiseTime} from dual
		</foreach>
	</insert>

	<!-- 专家评价备注 -->
	<insert id="saveAppraiseRemark" parameterType="AppraiseRemark">
		insert into t_appraise_remark
		(appraise_remark_id, extract_result_id, remark)
		values (#{appraise_remark_id},#{extract_result_id},#{remark})
	</insert>

	<!--业务员 修改专家抽取结果表是否评价状态 -->
	<update id="modifyIsAppraise" parameterType="ExpertInfoEntity">
		update t_extract_result set is_appraise = 1,IS_APPROVE=#{isApprove,jdbcType=VARCHAR},REJECTION_REASON=#{rejectionReason,jdbcType=VARCHAR},
		appraise_time = sysdate
		 where id = #{extractResultId}
	</update>
	<!--<select id="selectIsDuplicate" parameterType="String" resultType="String">
		select IS_APPROVE from t_extract_result where ID= #{extractResultId}
	</select>-->
	<select id="selectDeducteScore" parameterType="String" resultType="String">
		select sum(SCORE) from t_appraise where extract_result_id= #{extractResultId} and REVIEW_TIME is null
	</select>
	<!-- 修改专家抽取结果表是否奖励状态 -->
	<update id="modifyIsAppraiseReward" parameterType="String">
		update t_extract_result set IS_REWARD = 1,appraise_time = sysdate where id = #{extractResultId}
	</update>

	<!-- 查看专家已评价信息 -->
	<select id="queryAppraiseDetail" resultType="Appraise" parameterType="String">
		select * from (
		select a.appraise_id,a.appraise_info_id,a.extract_result_id,a.score from t_appraise a
		where a.extract_result_id = #{extractResultId} and a.appraise_type = 0
		 order by a.appraise_time desc
		) t where rownum <![CDATA[<]]> 26
	</select>
	
	<select id="queryAppraiseRewardDetail" resultType="Appraise" parameterType="String">
		select a.appraise_id,a.appraise_info_id,a.extract_result_id,a.score from t_appraise a  
		where a.extract_result_id = #{extractResultId} and a.appraise_type = 1
	</select>

	<!-- 查询评价备注信息 -->
	<select id="queryAppraiseRemark" resultType="AppraiseRemark" parameterType="String">
		select r.appraise_remark_id,r.extract_result_id,r.remark from t_appraise_remark r where r.extract_result_id = #{extractResultId}
	</select>
	<!-- 查看专家评价平均分 -->
	<select id="getExpertAvgScore" parameterType="Appraise" resultType="Appraise">
		select avg(c.score) score, c.expert_id as expert_id
		  from (select sum(a.score) score,
		               b.expert_id expert_id,
		               a.extract_result_id
		          from t_appraise a
		          left join t_extract_result b
		            on b.id = a.extract_result_id  
		         where b.delete_flag = 0 and b.expert_id = #{expert_id}
		         group by b.expert_id, a.extract_result_id) c
		 group by c.expert_id
	</select>
	<select id="getExpertScore" parameterType="String" resultType="Appraise">
		select a.appraise_id,a.extract_result_id ,a.score from t_appraise a where a.extract_result_id=#{expertId}
	</select>
	<insert id="addExpertScore" parameterType="Appraise">
		insert into t_appraise (appraise_id,extract_result_id,score) values(#{appraise_id},#{extract_result_id},#{score})
	</insert>
	<update id="modifyExpertScore" parameterType="Appraise">
		update t_appraise a set a.score = #{score} where a.extract_result_id = #{extract_result_id}
	</update>
	<select id="getExpertScoreBAK" parameterType="String" resultType="Appraise">
		select a.appraise_id,a.extract_result_id ,a.score from t_appraise2_bak a where a.extract_result_id=#{expertId}
	</select>
	<insert id="addExpertScoreBAK" parameterType="Appraise">
		insert into t_appraise2_bak (appraise_id,extract_result_id,score) values(#{appraise_id},#{extract_result_id},#{score})
	</insert>
	<update id="modifyExpertScoreBAK" parameterType="Appraise">
		update t_appraise2_bak set score = #{score} where appraise_id = #{appraise_id}
	</update>
	<update id="modifyExpertSuspension">
		update t_appraise set suspension = #{suspension}  where extract_result_id = #{id}
	</update>
	<update id="modifyExpertTotalSuspension">
		update t_appraise set total_suspension = #{totalSuspension}  where extract_result_id = #{id}
	</update>
	<update id="modifyExpertSuspensionBAK">
		update t_appraise2_bak set suspension = #{suspension}  where extract_result_id = #{id}
	</update>
	<update id="modifyExpertTotalSuspensionBAK">
		update t_appraise2_bak set total_suspension = #{totalSuspension}  where extract_result_id = #{id}
	</update>
	
	
	<!-- 查看专家违规记录 -->
	<select id="queryAppraiseIllegal" parameterType="String" resultType="AppraiseIllegal">
		select appraise_illegal_id,
		       extract_result_id,
		       illegal_content,
		       create_time,
		       create_id,
		       modify_time,
		       modify_id,
		       handling_opinions,
		       processing_status,
		       remark
		  from t_appraise_illegal a
		where a.extract_result_id =#{extract_result_id}
	</select>
	
	<!-- 新增专家违规记录 -->
	<insert id="saveAppraiseIllegal" parameterType="AppraiseIllegal">
		insert into t_appraise_illegal
			(appraise_illegal_id, extract_result_id, illegal_content, create_time , create_id ,modify_time,modify_id,handling_opinions,processing_status,remark)
		values 
			(#{appraise_illegal_id},#{extract_result_id},#{illegal_content},#{create_time},#{create_id},#{modify_time},#{modify_id},#{handling_opinions},#{processing_status},#{remark})
	</insert>
	
	<!-- 修改专家违规记录 -->
	<update id="updateAppraiseIllegal" parameterType="AppraiseIllegal">
		update t_appraise_illegal set  modify_time = #{modify_time},modify_id = #{modify_id}
		<if test="null != handling_opinions and '' != handling_opinions">
			,handling_opinions = #{handling_opinions}
		</if>
		<if test="null != processing_status and '' != processing_status">
			,processing_status = #{processing_status}
		</if>
		<if test="null != remark and '' != remark">
			,remark = #{remark}
		</if>
			 where appraise_illegal_id = #{appraise_illegal_id}
		
	</update>
	
	<!-- 查询专家反馈项目列表 -->
	<select id="queryPageAppraiseIllegalInfo" resultType="ProjectEntity" parameterType="ProjectEntity">
		SELECT a.project_id as projectId,A.PROJECT_NO projectNo,A.PROJECT_NAME projectName,
		A.bid_time bidTime,A.TENDER,C.PROCESSING_STATUS,r.id,b.id as conditionId FROM t_appraise_illegal C  
			left join t_extract_result r on r.id = c.extract_result_id
			LEFT JOIN T_CONDITION B ON b.id = r.condition_id
			LEFT JOIN T_PROJECT A ON a.project_id = b.project_id
		WHERE 1=1
		<if test="null != projectName and '' != projectName">
			AND a.project_name like '%${projectName}%'
		</if>
		<if test="null != projectNo and '' != projectNo">
			AND a.PROJECT_NO like '%${projectNo}%'
		</if>
		<if test="null != tender and '' != tender">
			AND a.tender like '%${tender}%'
		</if>
		<if test="null != bidStartTime and '' != bidStartTime">
			AND a.bid_time >= #{bidStartTime}
		</if>
		<if test="null != bidEndTime and '' != bidEndTime">
			AND a.bid_time &lt;= #{bidEndTime}
		</if>
		ORDER BY C.MODIFY_TIME DESC
	</select>
	
	<!-- 机电中心抽取人查询专家反馈专家列表for违规处理 -->
	<select id="queryExpertAppriaiseListByJD" resultType="ExpertInfoEntity" parameterType="ExpertInfoEntity">
		SELECT distinct er.id as extractResultId,ei.user_name, ei.mobilephone,ei.company,er.appraise_time,er.is_appraise,er.is_reward FROM T_APPRAISE_ILLEGAL p 
    	LEFT JOIN T_EXTRACT_RESULT er on p.extract_result_id = er.id
		left join t_expert_info ei on er.expert_id = ei.user_id
		left join t_condition c on c.id = er.condition_id
		left join t_appraise a on a.extract_result_id = er.id
		where er.delete_flag = 0 and ei.delete_flag = 0 and er.join_status = 0 and c.delete_flag = 0
		<if test="null != conditionId and '' != conditionId">
			and c.id = #{conditionId}
		</if>
		<if test="null != user_name and '' != user_name">
			and ei.user_name like '%${user_name}%'
		</if>
		<if test="null != company and '' != company">
			and ei.company like '%${company}%'
		</if>
		<if test="null != is_appraise">
			and er.is_appraise = #{is_appraise}
		</if>
		order by er.appraise_time desc
	</select>
	
	
	<!-- 机电中心抽取人查询专家反馈专家列表 -->
	<select id="queryPageProjectInfoByJD" resultType="ProjectEntity" parameterType="ProjectEntity">
		SELECT
			p.project_id as projectId,
			p.project_no as projectNo,
			p.project_name as projectName,
			p.bid_time as bidTime,
			p.tender,
			c.id as conditionId,
			c.method as method
		FROM 
			t_condition c
		LEFT JOIN 
			t_project p 
		ON 
			p.project_id = c.project_id
		WHERE
			 p.delete_flag = 0 and p.status in (3,10) AND c.delete_flag = 0 AND p.bid_time &lt; sysdate
		<if test="null != createUser and '' != createUser">
			AND p.create_user = #{createUser}
		</if>
		<if test="null != bidStartTimeStr and '' != bidStartTimeStr">
			AND p.bid_time >= to_date(#{bidStartTimeStr},'yyyy-MM-dd hh24:mi:ss ')
		</if>
		<if test="null != bidEndTimeStr and '' != bidEndTimeStr">
			AND p.bid_time &lt;= to_date(#{bidEndTimeStr},'yyyy-MM-dd hh24:mi:ss ')
		</if>
		<if test="null != projectName and '' != projectName">
			AND p.project_name like '%${projectName}%'
		</if>
		<if test="null != tender and '' != tender">
			AND p.tender like '%${tender}%'
		</if>
		<if test="null != bidStartTime and '' != bidStartTime">
			AND p.bid_time >= #{bidStartTime}
		</if>
		<if test="null != bidEndTime and '' != bidEndTime">
			AND p.bid_time &lt;= #{bidEndTime}
		</if>
		ORDER BY
			p.bid_time DESC
	</select>
</mapper>
