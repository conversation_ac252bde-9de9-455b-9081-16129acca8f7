<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzw.sunflower.dao.BondChangeRecordsMapper">


    <!-- 分页查看调整列表 -->
    <select id="getBondChangeRecordsPage" resultType="com.hzw.sunflower.controller.response.BondChangeRecordsListVo">
        SELECT
            IFNULL((p.purchase_number),"JSTCC9999") as purchaseNumber,
            IFNULL((p.purchase_name),"其他") as purchaseName,
            p.package_segment_status,
            s.package_number,
            c.name AS companyName,
            r.project_id,
            r.section_id,
            r.company_id,
            r.amount,
            r.former_project_id,
            r.former_section_id,
            r.change_user_id,
            r.change_time,
            IFNULL((p1.purchase_number),"JSTCC9999") AS formerPurchaseNumber,
            IFNULL((p1.purchase_name),"其他") AS formerPurchaseName,
            p1.package_segment_status AS formerPackageSegmentStatus,
            s1.package_number AS formerPackageNumber,
            u.user_name AS changeUserName
        FROM
            `t_bond_change_records` r
        LEFT JOIN t_project p ON p.id = r.project_id
        LEFT JOIN t_project_bid_section s ON s.id = r.section_id
        LEFT JOIN t_customer_to_ncc c ON c.id = r.company_id
        LEFT JOIN t_project p1 ON p1.id = r.former_project_id
        LEFT JOIN t_project_bid_section s1 ON s1.id = r.former_section_id
        LEFT JOIN t_user u ON r.change_user_id = u.id
        where
            r.is_delete = 0
            and c.is_delete = 0
            and u.is_delete = 0
            <if test="condition.keyWords !=null and condition.keyWords !=''">
                and (
                    p.purchase_number like concat('%',#{condition.keyWords},'%')
                    or
                    p.purchase_name like concat('%',#{condition.keyWords},'%')
                    or
                    p1.purchase_number like concat('%',#{condition.keyWords},'%')
                    or
                    p1.purchase_name like concat('%',#{condition.keyWords},'%')
                )
            </if>
            <if test="condition.companyName !=null and condition.companyName !=''">
                and c.name like concat('%',#{condition.companyName},'%')
            </if>
            <if test="condition.startTime !=null and condition.startTime !=''">
                and DATE_FORMAT(r.change_time,'%Y-%m-%d') &gt;= DATE_FORMAT(#{condition.startTime},'%Y-%m-%d')
            </if>
            <if test="condition.endTime !=null and condition.endTime !=''">
                and DATE_FORMAT(r.change_time,'%Y-%m-%d') &lt;= DATE_FORMAT(#{condition.endTime},'%Y-%m-%d')
            </if>
        ORDER BY change_time desc
    </select>

    <!-- 根据流水查询调整记录 -->
    <select id="getChangeRecords" resultType="com.hzw.sunflower.controller.response.BondChangeRecordsVo">
        SELECT
            r.*,
            IF( ui.identity = 2, u.user_name, c.company_name ) operator_name
        FROM
            t_bond_change_records r
        LEFT JOIN t_user u ON u.id = r.created_user_id
        LEFT JOIN t_user_identity ui ON ui.user_id = u.id
        LEFT JOIN t_company c ON c.id = r.company_id
        WHERE
            r.section_id = #{sectionId}
          AND r.company_id = #{companyId}
          and c.is_delete = 0
          and r.is_delete = 0
        ORDER BY r.created_time ASC
    </select>

</mapper>
