package com.hzw.ssm.expert.action;

import com.hzw.ssm.expert.service.RegisterService;
import com.hzw.ssm.fw.base.BaseAction;
import com.hzw.ssm.sys.login.service.LoginService;
import com.hzw.ssm.sys.user.entity.UserEntity;
import org.apache.commons.lang3.StringUtils;
import org.apache.struts2.ServletActionContext;
import org.apache.struts2.convention.annotation.Action;
import org.apache.struts2.convention.annotation.Namespace;
import org.apache.struts2.convention.annotation.Result;
import org.apache.struts2.convention.annotation.Results;
import org.springframework.beans.factory.annotation.Autowired;

import javax.servlet.http.HttpServletRequest;
import java.io.PrintWriter;
import java.util.Date;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.regex.PatternSyntaxException;

@Namespace("/register")
@Results({ @Result(	name = "toRegister",
					location = "/jsp/expert/expert_register.jsp") })
public class RegisterAction extends BaseAction {

	private static final long serialVersionUID = -8823858495799309882L;

	/** 随机码 */
	private String randomCode;

	/** 提示信息 */
	private String message;

	/** 登录名 */
	private String login_code;

	/** 手机号码 */
	private String mobile;

	/** 注册数据绑定 */
	private UserEntity registerEntity;

	/**短信验证码*/
	private String registerCode;

	@Autowired
	private RegisterService registerService;
	@Autowired
	private LoginService loginService;

	/**
	 * 加载注册页面
	 *
	 * @return
	 * @throws Exception
	 */
	@Action("toRegister")
	public String toRegister() throws Exception {
		return "toRegister";
	}

	/**
	 * 检查手机号码是否已经被注册过
	 *
	 * @return
	 * @throws Exception
	 */
	@Action("checkMobile")
	public String checkMobile() throws Exception {
		this.context();
		int result = registerService.checkMobileExist(mobile);
		PrintWriter pw = this.getResponse().getWriter();
		pw.print(result);
		return null;
	}

	/**
	 * 检查登录名是否已经被注册过
	 *
	 * @return
	 * @throws Exception
	 */
	@Action("checkLoginCode")
	public String checkLoginCode() throws Exception {
		this.context();
		int result = registerService.checkLoginCodeExist(login_code);
		PrintWriter pw = this.getResponse().getWriter();
		pw.print(result);
		return null;
	}

	/**
	 * 执行注册
	 *
	 * @return
	 * @throws Exception
	 */
	@Action("doRegister")
	public String doRegister() throws Exception {
		PrintWriter out = null;
		HttpServletRequest request = ServletActionContext.getRequest();
		try {
			this.context();
			this.getResponse().setCharacterEncoding("utf-8");
			out = this.getResponse().getWriter();
			String mobile = registerEntity.getMobile();
			String registerCode = request.getParameter("registerCode");
			// 有无验证码
			String code = (String) getRequest().getSession().getAttribute(mobile);
			if(StringUtils.isEmpty(code)){
				out.print("codeEmpty");
				out.close();
				return null;
			}
			//验证码是否过期
			Date registeDate=(Date)getRequest().getSession().getAttribute(mobile+"Time");
			//判断和当前时间相差多少秒
			Long nowDate=new Date().getTime();
			Long seconds = (nowDate-registeDate.getTime())/1000;
			if(seconds > 180){
				out.print("codeExpire");//验证码过期，3分钟
				out.close();
				return null;
			}
			if(!code.equals(registerCode)){
				out.print("codeError");//验证码输入错误
				out.close();
				return null;
			}else {
				if (registerEntity == null) {
					registerEntity = new UserEntity();
				}
				//验证码设置过期
				ServletActionContext.getRequest().getSession().setAttribute(mobile,"");
		        ServletActionContext.getRequest().getSession().setAttribute(mobile+"Time","");
				registerService.doRegister(registerEntity);
				out.print("success");
				out.close();
			}
		} catch (Exception e) {
			out.print("error");
		}
		return null;
	}

	/**
	 * 函数功能描述：注册页面发送验证码操作
	 *
	 * @return urlKey String
	 */
	@Action("/sendCode")
	public String sendSmsCode() {
		PrintWriter out = null;
		HttpServletRequest request = ServletActionContext.getRequest();
		String mobile = request.getParameter("mobile");
		try{
			this.context();
			this.getResponse().setCharacterEncoding("utf-8");
			out = this.getResponse().getWriter();
			if (!isChinaPhoneLegal(mobile)) {
				this.setMessage("手机号有误，若有疑问请联系人工客服4000580203");
				out.print(this.getMessage());
				out.close();
				return null;
			}
			//3分钟内是否已发送验证码
			String code = (String) getRequest().getSession().getAttribute(mobile);
			Date registeDate = (Date)getRequest().getSession().getAttribute(mobile+"Time");
			Long nowDate = new Date().getTime();
			if(!StringUtils.isEmpty(code) && registeDate != null && nowDate-registeDate.getTime() > 0 && nowDate-registeDate.getTime() < 180000){
				this.setMessage("您操作过于频繁，请稍后重试");
				out.print(this.getMessage());
				out.close();
				return null;
			}
			UserEntity loginUser = new UserEntity();
			loginUser.setMobile(mobile);
			String smscode = loginService.sendSMs(loginUser);
			if (!StringUtils.isEmpty(smscode)) {
				//将短信验证码保存到session中
		        ServletActionContext.getRequest().getSession().setAttribute(mobile,smscode);
		        ServletActionContext.getRequest().getSession().setAttribute(mobile+"Time",new Date());
		        System.out.println(smscode);
				this.setMessage("若您3分钟内未收到验证码，请联系人工客服4000580203");
			} else {
				this.setMessage("信息有误，若有疑问请联系人工客服4000580203");
			}
		}catch(Exception e){
			log.error("获取手机验证码失败", e);
			e.printStackTrace();
			out.print("error");
		}
		out.print(this.getMessage());
		out.close();
		return null;
	}

	public boolean isChinaPhoneLegal(String str) throws PatternSyntaxException {
		// ^ 匹配输入字符串开始的位置
		// \d 匹配一个或多个数字，其中 \ 要转义，所以是 \\d
		// $ 匹配输入字符串结尾的位置
		String regExp = "^((13[0-9])|(14[5,7,9])|(15[0-3,5-9])|(166)|(17[3,5,6,7,8])" + "|(18[0-9])|(19[8,9]))\\d{8}$";
		Pattern p = Pattern.compile(regExp);
		Matcher m = p.matcher(str);
		return m.matches();
	}

	public UserEntity getRegisterEntity() {
		return registerEntity;
	}

	public void setRegisterEntity(UserEntity registerEntity) {
		this.registerEntity = registerEntity;
	}

	public String getLogin_code() {
		return login_code;
	}

	public void setLogin_code(String loginCode) {
		login_code = loginCode;
	}

	public String getRandomCode() {
		return randomCode;
	}

	public void setRandomCode(String randomCode) {
		this.randomCode = randomCode;
	}

	public String getMessage() {
		return message;
	}

	public void setMessage(String message) {
		this.message = message;
	}

	public String getMobile() {
		return mobile;
	}

	public void setMobile(String mobile) {
		this.mobile = mobile;
	}
	public String getRegisterCode() {
		return registerCode;
	}

	public void setRegisterCode(String registerCode) {
		this.registerCode = registerCode;
	}

}
