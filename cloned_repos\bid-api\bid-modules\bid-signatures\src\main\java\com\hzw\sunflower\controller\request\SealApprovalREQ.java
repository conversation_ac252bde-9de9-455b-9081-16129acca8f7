package com.hzw.sunflower.controller.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@ApiModel(description = "审批接口实体")
@Data
public class SealApprovalREQ {
    @ApiModelProperty(value = "业务ID", position = 1)
    private Long busId;

    @ApiModelProperty(value = "审核实例ID", position = 2)
    private String processInstanceId;

    @ApiModelProperty(value = "任务ID", position = 3)
    private String taskId;

    @ApiModelProperty(value = "是否同意，同意为true，不同意为false", position = 4)
    private Boolean isAgree;

    @ApiModelProperty(value = "审批意见，不同意时必填", position = 5)
    private String message;

    @ApiModelProperty(value = "选择领导", position = 6)
    private String leader;

    @ApiModelProperty(value = "审批节点", position = 7)
    private String formKey;

}
