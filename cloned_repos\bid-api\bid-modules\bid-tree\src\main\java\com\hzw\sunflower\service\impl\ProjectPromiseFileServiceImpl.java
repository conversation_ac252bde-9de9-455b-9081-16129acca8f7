package com.hzw.sunflower.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzw.sunflower.constant.constantenum.BidPurchaseModeEnum;
import com.hzw.sunflower.constant.constantenum.ProjectOperationFlowEnum;
import com.hzw.sunflower.constant.constantenum.PromiseFileTypeEnum;
import com.hzw.sunflower.constant.constantnum.ReviewExpertTypeEnum;
import com.hzw.sunflower.controller.project.response.ProjectBidSectionVO;
import com.hzw.sunflower.controller.request.PromiseFileReq;
import com.hzw.sunflower.controller.response.ProjectPromiseFileVo;
import com.hzw.sunflower.dao.ProjectPromiseFileMapper;
import com.hzw.sunflower.entity.Project;
import com.hzw.sunflower.entity.ProjectPromiseFile;
import com.hzw.sunflower.entity.PromiseFileSign;
import com.hzw.sunflower.entity.condition.ProjectPromiseFileCondition;
import com.hzw.sunflower.service.*;
import com.hzw.sunflower.util.FileUtils;
import com.hzw.sunflower.util.WordToPdfAsposeUtil;
import com.hzw.sunflower.util.word.WordToPdf;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @date ：Created in 2021/8/27 13:51
 * @description：
 * @version: 1.0
 */
@Service
public class ProjectPromiseFileServiceImpl extends ServiceImpl<ProjectPromiseFileMapper, ProjectPromiseFile> implements ProjectPromiseFileService {

    @Value("${files.template.path}")
    private String templatePath;

    @Value("${files.temporary.path}")
    private String tempFilePath;

    @Autowired
    private PromiseFileSignService promiseFileSignService;

    @Autowired
    OssFileService ossFileService;

    @Autowired
    private ProjectService projectService;

    @Autowired
    private ProjectBidSectionService projectBidSectionService;

    /**
     * 分页查询
     * @param condition
     * @return
     */
    @Override
    public IPage<ProjectPromiseFileVo> listPage(ProjectPromiseFileCondition condition) {
        IPage<ProjectPromiseFileVo> page = condition.buildPage();
        return this.baseMapper.listPage(page, condition);
    }

    /**
     * 新增其他承诺文件
     * @param req
     * @return
     */
    @Override
    public Boolean addPromiseFile(PromiseFileReq req) {
        ProjectPromiseFile projectPromiseFile = new ProjectPromiseFile();
        BeanUtils.copyProperties(req, projectPromiseFile);
        projectPromiseFile.setFileType(PromiseFileTypeEnum.ELSE.getType());
        return this.save(projectPromiseFile);
    }

    /**
     * 删除承诺文件
     * @param id
     * @return
     */
    @Override
    public Boolean deletePromiseFile(Long id) {
        // 判断类型 非其他类型不允许删除
        ProjectPromiseFile projectPromiseFile = this.getById(id);
        if (!projectPromiseFile.getFileType().equals(PromiseFileTypeEnum.ELSE.getType())) {
            return false;
        }
        return this.removeById(id);
    }

    /**
     * 编辑/重新上传承诺文件
     * @param req
     * @return
     */
    @Override
    public Boolean addOrUpdatePromiseFile(PromiseFileReq req) {
        ProjectPromiseFile projectPromiseFile = new ProjectPromiseFile();
        BeanUtils.copyProperties(req, projectPromiseFile);
        Boolean saveOrUpdate = this.saveOrUpdate(projectPromiseFile);
        if(null != req.getId()){
            LambdaQueryWrapper<PromiseFileSign> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(PromiseFileSign::getPromiseFileId,req.getId());
            promiseFileSignService.remove(queryWrapper);
        }
        return saveOrUpdate;
    }

    /**
     * 查询专家评审文件签署列表
     * @param projectIds
     * @param userId
     * @return
     */
    @Override
    public List<ProjectPromiseFileVo> listSign(List<Long> projectIds, Long userId, Long conferenceId) {
        List<ProjectPromiseFileVo> list = this.baseMapper.listSign(projectIds, userId,conferenceId);
        // 查询专家评审身份
        String expertTypes = this.baseMapper.selectExpertTypes(conferenceId, userId);
        // 判断是否含有协评人员宣誓词
        List<ProjectPromiseFileVo> judgesList = list.stream().filter(s -> !s.getFileType().equals(PromiseFileTypeEnum.ASSIST_JUDGES_OATH.getType()))
                .collect(Collectors.toList());
        List<ProjectPromiseFileVo> assistList = list.stream().filter(s -> !s.getFileType().equals(PromiseFileTypeEnum.JUDGES_OATH.getType()))
                .collect(Collectors.toList());
        List<ProjectPromiseFileVo> assList = list.stream().filter(s -> s.getFileType().equals(PromiseFileTypeEnum.ASSIST_JUDGES_OATH.getType()))
                .collect(Collectors.toList());
        if (assList.size() > 0) {
            if (expertTypes.contains(ReviewExpertTypeEnum.XPRY.getType().toString()) && expertTypes.contains(ReviewExpertTypeEnum.EXPERT.getType().toString())) {
                return list;
            } else if (expertTypes.contains(ReviewExpertTypeEnum.XPRY.getType().toString())) {
                return assistList;
            } else {
                return judgesList;
            }
        } else {
            return judgesList;
        }
    }

    /**
     * 查询会议室关联专家参与的评审
     * @param conferenceId
     * @param userId
     * @return
     */
    @Override
    public List<Long> getProjectIdsByConferenceId(Long conferenceId, Long userId) {
        return this.baseMapper.getProjectIdsByConferenceId(conferenceId, userId);
    }

    @Override
    public void savePromiseFile(Long projectId,Long sectionId) {
        // 根据项目id 查询项目名称 项目编号 采购方式
        Project projectById = projectService.getProjectById(projectId);
        ProjectBidSectionVO sectionInfoById = projectBidSectionService.getSectionInfoById(sectionId);
        // 判断该项目是否存在文件
        LambdaQueryWrapper<ProjectPromiseFile> query = new LambdaQueryWrapper<>();
        query.eq(ProjectPromiseFile::getProjectId,projectId).eq(ProjectPromiseFile::getFileType,PromiseFileTypeEnum.JUDGES_OATH.getType());
        ProjectPromiseFile one = this.getOne(query);
        // 判断是否为全流程线上项目  并且不存在承诺文件
        if(ProjectOperationFlowEnum.Flow0.getValue().equals(projectById.getOperationFlow()) && BeanUtil.isEmpty(one)){
            // 判断是否为政府采购 确认使用模板
            String templateName = "promiseFile.docx";
            if(BidPurchaseModeEnum.GOVERNMENT_PROCUREMENT.getType().equals(sectionInfoById.getPurchaseType().toString())){
                templateName = "promiseFile_government.docx";
            } else { }
            // 封装所需模板数据
            List<Map<String, Object>> list = new ArrayList<>();
            Map<String,Object> param = new HashMap<>();
            param.put("projectName",projectById.getProjectName());
            param.put("projectNum",projectById.getPurchaseNumber());
            if(BidPurchaseModeEnum.GOVERNMENT_PROCUREMENT_924101.getType().equals(sectionInfoById.getPurchaseMode())){
                param.put("promiseType","评标委员会");
            } else {
                param.put("promiseType","评审委员会");
            }
            list.add(param);
            // 生成文件
            String outPath = tempFilePath + "promiseFile.docx";
            String outPdfPath = tempFilePath + "promiseFile.pdf";
            //读取的模板路径
            String readPath = templatePath+templateName;
            //填充word模板
            FileUtils.XWPFTemplateWord(readPath,outPath,list);
            // 上传文件
            try {
                // word转pdf 上传天翼云
                WordToPdfAsposeUtil.docToPdf(outPath, outPdfPath);
                Long ossFileId = ossFileService.saveOssFile(PromiseFileTypeEnum.JUDGES_OATH.getName() + ".pdf", outPdfPath);
                // 保存评委宣誓词文件数据
                ProjectPromiseFile file = new ProjectPromiseFile();
                file.setFileName(PromiseFileTypeEnum.JUDGES_OATH.getName());
                file.setFileType(PromiseFileTypeEnum.JUDGES_OATH.getType());
                file.setProjectId(projectId);
                file.setFileOssId(ossFileId);
                this.saveOrUpdate(file);
            } catch (Exception ex) {
                ex.printStackTrace();
            } finally {
                //删除临时文件
                File file = new File(outPath);
                file.delete();
                File file2 = new File(outPdfPath);
                file2.delete();
            }
        }
    }

}
