package com.hzw.sunflower.constant.constantenum;

/**
 * <AUTHOR>
 * @date ：Created in 2021/7/14 10:51
 * @description：审批状态
 * @version: 1.0
 */
public enum ApprovalTypeEnum {
    COMMIT(0, "提交审核"),

    REVIEW(1, "撤回"),

    DRAWBACK(2, "退回"),

    AGREE(3, "确认"),

    APPLY_REVIEW(4, "申请撤回"),

    DRAWBACK_WITHDRAW(5, "退回撤回"),

    AGREE_WITHDRAW(6, "同意撤回"),

    RELEASE(7, "发布"),

    NEXT_REVIEW(8, "下一步待审核人");


    private Integer value;

    private String name;

    ApprovalTypeEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    public Integer getValue() {
        return value;
    }

    public String getName() {
        return name;
    }
}
