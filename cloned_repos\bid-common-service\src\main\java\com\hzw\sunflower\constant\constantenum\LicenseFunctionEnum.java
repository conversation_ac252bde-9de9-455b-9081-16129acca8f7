package com.hzw.sunflower.constant.constantenum;

/**
 * <AUTHOR>
 * @datetime 2023/02/17 16:09
 * @description: license功能枚举
 * @version: 1.0
 */
public enum LicenseFunctionEnum {

    // license功能枚举（1.基础版 2.专业版 3.旗舰版）
    FUNCTION_BASIC(1,"基础版"),
    FUNCTION_PROFESSIONAL(2,"专业版"),
    FUNCTION_ULTIMATE(3,"旗舰版");

    private Integer type;
    private String desc;

    LicenseFunctionEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    /**
     * 根据描述获取对应枚举
     * @param desc
     * @return
     */
    public static LicenseFunctionEnum getFunction(String desc) {
        for (LicenseFunctionEnum e : LicenseFunctionEnum.values()) {
            if (e.getDesc().equals(desc)) {
                return e;
            }
        }
        return null;
    }
}
