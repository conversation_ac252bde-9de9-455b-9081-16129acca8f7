///***
/// * 公告数据列表的查询参数 - 数据模型
/// */

import 'package:json_annotation/json_annotation.dart';

part 'BulletinListQueryModel.g.dart';

@JsonSerializable(explicitToJson: true)
class BulletinListQueryModel {

  BulletinListQueryModel({
    this.bidType,
    this.bulletinTypeName,
    this.classifyName,
    this.currentPage,
    this.fundRange,
    this.fundSource,
    this.industryCode,
    this.keyWord,
    this.pageSize,
    this.platformCode,
    this.regionCode,
    this.uid,
  });

  /// * 招标方式
  @JsonKey(name: 'bidType')
  List<String> bidType;

  /// * 公告类型
  @JsonKey(name: 'bulletinTypeName')
  List<String> bulletinTypeName;

  /// * 招标专业
  @JsonKey(name: 'classifyName')
  List<String> classifyName;

  ///***
  /// * 当前页号
  /// * 第一页为 1
  /// * 之后为之前返回的 currentPageCursor - ElasticSearch 用游标
  /// */
  @JsonKey(name: 'currentPage')
  String currentPage;

  /// * 资金范围
  @Json<PERSON>ey(name: 'fundRange')
  List<String> fundRange;

  /// * 资金来源
  @JsonKey(name: 'fundSource')
  List<String> fundSource;

  /// * 行业
  @JsonKey(name: 'industryCode')
  List<String> industryCode;

  /// * 订阅|搜索关键词
  @JsonKey(name: 'keyWord')
  List<String> keyWord;

  /// * 每页条数
  @JsonKey(name: 'pageSize')
  int pageSize;

  /// * 推荐平台
  @JsonKey(name: 'platformCode')
  List<String> platformCode;

  /// * 地区
  @JsonKey(name: 'regionCode')
  List<String> regionCode;

  /// * 用户 ID
  @JsonKey(name: 'uid')
  String uid;

  factory BulletinListQueryModel.fromJson(Map<String, dynamic> json) => _$BulletinListQueryModelFromJson(json);

  Map<String, dynamic> toJson() => _$BulletinListQueryModelToJson(this);

}
