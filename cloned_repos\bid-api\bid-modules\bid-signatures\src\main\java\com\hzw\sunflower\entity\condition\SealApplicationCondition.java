package com.hzw.sunflower.entity.condition;

import com.hzw.sunflower.common.BaseCondition;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * <AUTHOR>
 */
@ApiModel(description = "SealApplication 查询条件")
@Data
public class SealApplicationCondition extends BaseCondition {

    @ApiModelProperty(value = "关键字")
    private String keyWords;

    @ApiModelProperty(value = "用印申请类型")
    private Integer type;

    @ApiModelProperty(value = "项目id")
    private Long projectId;

    @ApiModelProperty(value = "标段id")
    private Long sectionId;

}