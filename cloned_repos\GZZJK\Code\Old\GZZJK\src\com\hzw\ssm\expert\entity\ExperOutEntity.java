/*
 * 公司名称：江苏华招网信息技术有限公司
 * 版权信息：江苏华招网信息技术有限公司版权所有
 * 文件名称：ExperOutEntity.java
 * 修改时间：2021年2月23日
 * 修改人：宋伟
 * 修改内容：
 * 跟踪单号：
 * 修改单号 ：
 */

package com.hzw.ssm.expert.entity;

import java.util.Date;
import java.util.List;

import com.hzw.ssm.fw.base.BaseEntity;
import com.hzw.ssm.fw.util.Page;

/**
 * <一句话功能简述> 专家出库相关实体类
 * 
 * <AUTHOR>
 * @version V1.0[产品/模块版本]
 */
public class ExperOutEntity extends BaseEntity {

	private static final long serialVersionUID = 1L;
	/**
	 * 专家编号
	 */
	private String expertNum;
	/**
	 * 专家姓名
	 */
	private String userName;
	/**
	 * 手机号
	 */
	private String mobilePhone;
	/**
	 * 专家类型
	 */
	private String expertType;
	/**
	 * 评价得分
	 */
	private String evalScore;

	private String province;// 所在省份

	private String city;// 所在城市

	private String zone;// 所在区或县
	/**
	 * 行政区域
	 */
	private String district;
	/**
	 * 评标专业
	 */
	private String major;
	/**
	 * 出生日期
	 */
	private Date birthday;
	/**
	 * 入库时间
	 */
	private Date auditTime;
	/**
	 * 出库原因
	 */
	private String outReason;
	/**
	 * 是否查询专家的待评标项目开关
	 */
	private boolean queryProjectFlag = false;

	/**
	 * 是否存在待评标项目标识
	 */
	private String projectFlag;

	/**
	 * 状态
	 */
	private String status;
	/**
	 * 出库状态
	 */
	private String outStatus;
	/**
	 * 分页
	 */
	private Page page;
	/**
	 * 用户id
	 */
	private String userId;
	/**
	 * 修改角色id
	 */
	private String modifyRole;
	/**
	 * 存储过程扫描成待出库时间
	 */
	private Date dealOutTime;
	/**
	 * 审核时间
	 */
	private Date checkTime;
	/**
	 * 审核人id
	 */
	private String checkId;
	/**
	 * 审核人名称
	 */
	private String checkName;

	/**
	 * 专家id集合
	 */
	private List<String> userIds;
	/**
	 * 专家来源
	 */
	private String enterFlag;
	/**
	 * 开始时间
	 */
	private Date startTime;
	/**
	 * 结束时间
	 */
	private Date endTime;

	/**
	 * @return the expertNum
	 */
	public String getExpertNum() {
		return expertNum;
	}

	/**
	 * @param expertNum
	 *            the expertNum to set
	 */
	public void setExpertNum(String expertNum) {
		this.expertNum = expertNum;
	}

	/**
	 * @return the userName
	 */
	public String getUserName() {
		return userName;
	}

	/**
	 * @param userName
	 *            the userName to set
	 */
	public void setUserName(String userName) {
		this.userName = userName;
	}

	/**
	 * @return the mobilePhone
	 */
	public String getMobilePhone() {
		return mobilePhone;
	}

	/**
	 * @param mobilePhone
	 *            the mobilePhone to set
	 */
	public void setMobilePhone(String mobilePhone) {
		this.mobilePhone = mobilePhone;
	}

	/**
	 * @return the expertType
	 */
	public String getExpertType() {
		return expertType;
	}

	/**
	 * @param expertType
	 *            the expertType to set
	 */
	public void setExpertType(String expertType) {
		this.expertType = expertType;
	}

	/**
	 * @return the evalScore
	 */
	public String getEvalScore() {
		return evalScore;
	}

	/**
	 * @param evalScore
	 *            the evalScore to set
	 */
	public void setEvalScore(String evalScore) {
		this.evalScore = evalScore;
	}

	/**
	 * @return the district
	 */
	public String getDistrict() {
		return district;
	}

	/**
	 * @param district
	 *            the district to set
	 */
	public void setDistrict(String district) {
		this.district = district;
	}

	/**
	 * @return the major
	 */
	public String getMajor() {
		return major;
	}

	/**
	 * @param major
	 *            the major to set
	 */
	public void setMajor(String major) {
		this.major = major;
	}

	/**
	 * @return the birthday
	 */
	public Date getBirthday() {
		return birthday;
	}

	/**
	 * @param birthday
	 *            the birthday to set
	 */
	public void setBirthday(Date birthday) {
		this.birthday = birthday;
	}

	/**
	 * @return the outReason
	 */
	public String getOutReason() {
		return outReason;
	}

	/**
	 * @param outReason
	 *            the outReason to set
	 */
	public void setOutReason(String outReason) {
		this.outReason = outReason;
	}

	/**
	 * @return the status
	 */
	public String getStatus() {
		return status;
	}

	/**
	 * @param status
	 *            the status to set
	 */
	public void setStatus(String status) {
		this.status = status;
	}

	/**
	 * @return the outStatus
	 */
	public String getOutStatus() {
		return outStatus;
	}

	/**
	 * @param outStatus
	 *            the outStatus to set
	 */
	public void setOutStatus(String outStatus) {
		this.outStatus = outStatus;
	}

	/**
	 * @return the page
	 */
	public Page getPage() {
		return page;
	}

	/**
	 * @param page
	 *            the page to set
	 */
	public void setPage(Page page) {
		this.page = page;
	}

	/**
	 * @return the userId
	 */
	public String getUserId() {
		return userId;
	}

	/**
	 * @param userId
	 *            the userId to set
	 */
	public void setUserId(String userId) {
		this.userId = userId;
	}

	/**
	 * @return the checkId
	 */
	public String getCheckId() {
		return checkId;
	}

	/**
	 * @param checkId
	 *            the checkId to set
	 */
	public void setCheckId(String checkId) {
		this.checkId = checkId;
	}

	/**
	 * @return the userIds
	 */
	public List<String> getUserIds() {
		return userIds;
	}

	/**
	 * @param userIds
	 *            the userIds to set
	 */
	public void setUserIds(List<String> userIds) {
		this.userIds = userIds;
	}

	/**
	 * @return the dealOutTime
	 */
	public Date getDealOutTime() {
		return dealOutTime;
	}

	/**
	 * @param dealOutTime
	 *            the dealOutTime to set
	 */
	public void setDealOutTime(Date dealOutTime) {
		this.dealOutTime = dealOutTime;
	}

	/**
	 * @return the checkTime
	 */
	public Date getCheckTime() {
		return checkTime;
	}

	/**
	 * @param checkTime
	 *            the checkTime to set
	 */
	public void setCheckTime(Date checkTime) {
		this.checkTime = checkTime;
	}

	/**
	 * @return the checkName
	 */
	public String getCheckName() {
		return checkName;
	}

	/**
	 * @param checkName
	 *            the checkName to set
	 */
	public void setCheckName(String checkName) {
		this.checkName = checkName;
	}

	/**
	 * @return the projectFlag
	 */
	public String getProjectFlag() {
		return projectFlag;
	}

	/**
	 * @param projectFlag
	 *            the projectFlag to set
	 */
	public void setProjectFlag(String projectFlag) {
		this.projectFlag = projectFlag;
	}

	/**
	 * @return the province
	 */
	public String getProvince() {
		return province;
	}

	/**
	 * @param province
	 *            the province to set
	 */
	public void setProvince(String province) {
		this.province = province;
	}

	/**
	 * @return the city
	 */
	public String getCity() {
		return city;
	}

	/**
	 * @param city
	 *            the city to set
	 */
	public void setCity(String city) {
		this.city = city;
	}

	/**
	 * @return the zone
	 */
	public String getZone() {
		return zone;
	}

	/**
	 * @param zone
	 *            the zone to set
	 */
	public void setZone(String zone) {
		this.zone = zone;
	}

	/**
	 * @return the enterFlag
	 */
	public String getEnterFlag() {
		return enterFlag;
	}

	/**
	 * @param enterFlag
	 *            the enterFlag to set
	 */
	public void setEnterFlag(String enterFlag) {
		this.enterFlag = enterFlag;
	}

	/**
	 * @return the queryProjectFlag
	 */
	public boolean isQueryProjectFlag() {
		return queryProjectFlag;
	}

	/**
	 * @param queryProjectFlag
	 *            the queryProjectFlag to set
	 */
	public void setQueryProjectFlag(boolean queryProjectFlag) {
		this.queryProjectFlag = queryProjectFlag;
	}

	/**
	 * @return the startTime
	 */
	public Date getStartTime() {
		return startTime;
	}

	/**
	 * @param startTime
	 *            the startTime to set
	 */
	public void setStartTime(Date startTime) {
		this.startTime = startTime;
	}

	/**
	 * @return the endTime
	 */
	public Date getEndTime() {
		return endTime;
	}

	/**
	 * @param endTime
	 *            the endTime to set
	 */
	public void setEndTime(Date endTime) {
		this.endTime = endTime;
	}

	/**
	 * @return the auditTime
	 */
	public Date getAuditTime() {
		return auditTime;
	}

	/**
	 * @param auditTime
	 *            the auditTime to set
	 */
	public void setAuditTime(Date auditTime) {
		this.auditTime = auditTime;
	}

	/**
	 * @return the modifyRole
	 */
	public String getModifyRole() {
		return modifyRole;
	}

	/**
	 * @param modifyRole the modifyRole to set
	 */
	public void setModifyRole(String modifyRole) {
		this.modifyRole = modifyRole;
	}

	
}
