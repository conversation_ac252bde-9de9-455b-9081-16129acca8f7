package com.hzw.externalApi.api;

import com.hzw.common.core.domain.R;
import com.hzw.externalApi.api.domain.TycCompany;

/**
 * @Auther: ljx
 * @Date: 2022-5-5 10:24
 * @Description:
 */
public interface RemoteTycRecordService {

    /**
     * 天眼查查询联系方式
     * @param companyName
     * @return
     * @throws Exception
     */
    R<String> saveCompanyContactInfo(String companyName) throws Exception;

    /**
     * 天眼查校验
     * @param company
     * @param isInviteSupplier
     * @return
     */
    public Boolean selectKeywordInfo(TycCompany company, Boolean isInviteSupplier, Boolean tycKeyValueChanged);

    /**
     * 天眼查校验
     * @param company
     * @return
     */
    public Integer checkKeywordInfo(TycCompany company);
}
