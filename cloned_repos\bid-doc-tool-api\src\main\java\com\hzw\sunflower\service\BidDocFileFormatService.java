package com.hzw.sunflower.service;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.controller.request.BidDocFileFormatReq;
import com.hzw.sunflower.controller.responese.BidDocFileFormatVo;
import com.hzw.sunflower.entity.BidDocFileFormat;

import java.util.List;

/**
 * <p>
 * 招标文件编制投标文件格式 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-15
 */
public interface BidDocFileFormatService extends IService<BidDocFileFormat> {

    /**
     * 保存投标文件格式
     * @param req
     * @return
     */
    Result<Boolean> submitFileFormat(BidDocFileFormatReq req);

    /**
     * 查询投标文件格式
     * @param req
     * @return
     */
    Result<List<BidDocFileFormatVo>> queryFileFormat(BidDocFileFormatReq req);

    /**
     * 插入投标文件格式到word指定位置
     * @param docId
     * @param path
     */
    void insertFileFormatForWord(Long docId, String path);
}
