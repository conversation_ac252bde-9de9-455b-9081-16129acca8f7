package com.hzw.sunflower.controller;

import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.controller.request.NoticeAuditReq;
import com.hzw.sunflower.controller.request.NoticeChangeRecordReq;
import com.hzw.sunflower.controller.response.NoticeChangeRecordVO;
import com.hzw.sunflower.dao.NoticeChangeRecordMapper;
import com.hzw.sunflower.exception.ExceptionEnum;
import com.hzw.sunflower.exception.SunFlowerException;
import com.hzw.sunflower.service.NoticeChangeRecordService;
import com.hzw.sunflower.util.RequestUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * NoticeChangeRecordController
 */
@Api(tags = "招标公告变更记录服务")
@RestController
@RequestMapping("/noticeChangeRecord")
public class NoticeChangeRecordController {

    @Autowired
    private NoticeChangeRecordService noticeChangeRecordService;

    @Autowired
    private NoticeChangeRecordMapper noticeChangeRecordMapper;

    @ApiOperation(value = "根据公告和项目ID查询公告变更记录")
    @ApiImplicitParam(name = "noticeId", value = "公告ID", required = true)
    @PostMapping("/list")
    public Result<List<NoticeChangeRecordVO>> list(@RequestBody NoticeAuditReq req){
        // 判断公告ID是否正常输入
        if((req.getNoticeId() == null) || (req.getNoticeId().equals(0))){
            throw new SunFlowerException(ExceptionEnum.ILLEGAL_PARAMETER_EXCEPTION,"公告ID不合法");
        }
        if((req.getProjectId() == null)){
            throw new SunFlowerException(ExceptionEnum.ILLEGAL_PARAMETER_EXCEPTION,"项目ID不合法");
        }
        return noticeChangeRecordService.findChangeRecordListByNoticeId(req);
    }

    @ApiOperation(value = "根据公告ID查询总提交次数")
    @ApiImplicitParam(name = "noticeId", value = "公告ID", required = true)
    @GetMapping("/totalSubmit/{noticeId}")
    public Result<Long> maxSubmitNum(@PathVariable("noticeId") Long noticeId){
        // 判断公告ID是否正常输入
        if((noticeId == null) || (noticeId.equals(0))){
            throw new SunFlowerException(ExceptionEnum.ILLEGAL_PARAMETER_EXCEPTION,"公告参数不合法");
        }

        return Result.ok(noticeChangeRecordMapper.findMaxSubmit(noticeId));
    }

    @ApiOperation(value = "根据标段ID获取招标公告信息变更记录")
    @PostMapping(value = "/noticeInfoByBd")
    public Result<List<NoticeChangeRecordVO>> noticeInfoByBd(@RequestBody NoticeChangeRecordReq req) {
        if(RequestUtil.isEmpty(req.getSectionId())){
            throw new SunFlowerException(ExceptionEnum.PROJECT_BID_SECTIONS_NOT_FOUND,"标段ID不合法");
        }

        return noticeChangeRecordService.selectChangeRecordListListByBid(req);
    }

}
