package com.hzw.sunflower.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

@Data
@EqualsAndHashCode
public class RelevancyAnnexDTO implements Serializable {

    @ApiModelProperty(value = "附件ID ", position = 1)
    private Long id;

    @ApiModelProperty(value = "项目ID", position = 2)
    private Long projectId;

    @ApiModelProperty(value = "标段ID", position = 3)
    private Long sectionId;

    @ApiModelProperty(value = "文件ID", position = 4)
    private Long fileId;

    @ApiModelProperty(value = "包号", position = 5)
    private Long packageNumber;

    @ApiModelProperty(value = "ossFileKey", position = 6)
    private String ossFileKey;

    @ApiModelProperty(value = "ossFileName", position = 7)
    private String ossFileName;
}
