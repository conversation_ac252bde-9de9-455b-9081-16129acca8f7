package com.hzw.sunflower.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzw.sunflower.constant.CompanyFlowConfigConstants;
import com.hzw.sunflower.dao.CompanyFlowJumpMapper;
import com.hzw.sunflower.entity.CompanyFlowJump;
import com.hzw.sunflower.service.CompanyFlowJumpService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class CompanyFlowJumpServiceImpl extends ServiceImpl<CompanyFlowJumpMapper, CompanyFlowJump> implements CompanyFlowJumpService {
    @Override
    public List<String> getCompanyFlowJumpId(String processDefinitionKey) {
        List<String> processIds = new ArrayList<>();
        LambdaQueryWrapper<CompanyFlowJump> query = new LambdaQueryWrapper<>();
        query.eq(CompanyFlowJump::getProcessDefinitionKey, processDefinitionKey)
                .eq(CompanyFlowJump::getJumpType, CompanyFlowConfigConstants.JUMP_TYPE_1);
        List<CompanyFlowJump> list = this.list(query);
        if(list.size()>0){
            processIds = list.stream().map(CompanyFlowJump::getProcessId).collect(Collectors.toList());
        }
        return processIds;
    }

    @Override
    public List<String> IgnoreJump(String processDefinitionKey) {
        List<String> processIds = new ArrayList<>();
        LambdaQueryWrapper<CompanyFlowJump> query = new LambdaQueryWrapper<>();
        query.eq(CompanyFlowJump::getProcessDefinitionKey, processDefinitionKey)
                .eq(CompanyFlowJump::getJumpType, CompanyFlowConfigConstants.JUMP_TYPE_2);
        List<CompanyFlowJump> list = this.list(query);
        if(list.size()>0){
            processIds = list.stream().map(CompanyFlowJump::getProcessId).collect(Collectors.toList());
        }
        return processIds;
    }
}
