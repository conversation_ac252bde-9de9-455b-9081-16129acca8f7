<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hzw.sunflower.dao.ReProjectMapper">

    <select id="getNoticeByPurchaseNumber" resultType="com.hzw.sunflower.dto.ReSectionCanDTO">
        SELECT
            a.id projectId,
            a.package_segment_status packageSegmentStatus,
            pbs.id sectionId,
            pbs.package_name packageName,
            pbs.package_number,
            pbs.STATUS,
            pbs1.former_section_id,
            sn.notice_id noticeId,
            pbn.notice_progress noticeProgress
        FROM
            t_project a
            LEFT JOIN t_project_bid_section pbs ON a.id = pbs.project_id
            LEFT JOIN t_project_bid_section pbs1 on pbs.id =pbs1.former_section_id
            LEFT JOIN t_section_notice sn ON pbs.id = sn.section_id AND sn.is_delete = 0
            LEFT JOIN t_project_bid_notice pbn ON pbn.id = sn.notice_id AND pbn.notice_progress = 5 AND pbn.is_delete = 0
            AND pbn.bid_round = pbs.bid_round
        WHERE
            a.purchase_number = #{purchaseNumber}
            AND a.is_delete = 0
            AND pbs.is_delete = 0
            AND pbs1.base_section_id is null
--             AND ((pbs.status >12 and pbs.status &lt; 50) or pbs.status = 72 )
            and pbs.abnormal_status = 2
    </select>

    <select id="getRecord" resultType="com.hzw.sunflower.dto.ProjectRecordDTO">
        SELECT
            p.id project_id,
            p.purchase_name,
            p.purchase_number,
            pd.package_number,
            pd.base_package_number,
            p.created_time,
            pd.id as sectionId,
            pd.former_section_id,
            pd.former_package_number,
            pd.base_section_id
        FROM
            t_project p
            LEFT JOIN t_project_bid_section pd ON p.id = pd.project_id
        WHERE
            pd.IS_DELETE = 0
            AND p.is_delete = 0
            AND ( p.id = #{projectId} OR p.base_project_id = #{projectId} )
        ORDER BY
            pd.created_time ASC,
            pd.package_number ASC
    </select>
    <select id="getBaseProjectEntrust" resultType="com.hzw.sunflower.entity.ProjectEntrustUser">
        SELECT
            type,
            user_id,
            company_id,
            project_id,
            source,
            department_id
        FROM
            t_project_entrust_user
        WHERE
            is_delete = 0
            AND project_id = #{projectId}
            AND type = #{type}
              LIMIT 1
    </select>
    <select id="getNoticeByPurchaseNumberId" resultType="com.hzw.sunflower.dto.ReSectionCanDTO">
        SELECT
        a.id projectId,
        a.package_segment_status packageSegmentStatus,
        pbs.id sectionId,
        pbs.package_name packageName,
        pbs.package_number,
        pbs.STATUS,
        pbs1.former_section_id,
        sn.notice_id noticeId,
        pbn.notice_progress noticeProgress
        FROM
        t_project a
        LEFT JOIN t_project_bid_section pbs ON a.id = pbs.project_id
        LEFT JOIN t_project_bid_section pbs1 on pbs.id =pbs1.former_section_id
        LEFT JOIN t_section_notice sn ON pbs.id = sn.section_id AND sn.is_delete = 0
        LEFT JOIN t_project_bid_notice pbn ON pbn.id = sn.notice_id AND pbn.notice_progress = 5 AND pbn.is_delete = 0
        AND pbn.bid_round = pbs.bid_round
        WHERE
        a.purchase_number = #{req.purchaseNumber}
        AND a.is_delete = 0
        AND pbs.is_delete = 0
        AND pbs1.base_section_id is null
        and a.id != #{req.projectId}
        and pbs.abnormal_status = 2
    </select>
</mapper>
