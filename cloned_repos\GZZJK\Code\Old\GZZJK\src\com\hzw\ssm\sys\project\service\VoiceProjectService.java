package com.hzw.ssm.sys.project.service;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.hzw.ssm.expert.entity.ExpertInfoEntity;
import com.hzw.ssm.sys.project.dao.VoiceProjectMapper;
import com.hzw.ssm.sys.project.entity.ConditionEntity;
import com.hzw.ssm.sys.project.entity.ProjectChangesEntity;
import com.hzw.ssm.sys.project.entity.ProjectEntity;
import com.hzw.ssm.sys.project.entity.ResultEntity;

@Service
public class VoiceProjectService {

	@Autowired
	private VoiceProjectMapper voiceProjectMapper;

	/**
	 * 根据项目创建人获得项目
	 * 
	 * @param project
	 * @return
	 */
	public List<ProjectEntity> queryVoiceProjectList(ProjectEntity project) {

		List<ProjectEntity> list = voiceProjectMapper.queryPageVoiceProjectInitList(project);

		return list;
	}

	public List<ProjectEntity> queryPageEmergencyTreatmentList(ProjectEntity project) {
		List<ProjectEntity> list = voiceProjectMapper.queryPageEmergencyTreatmentList(project);

		return list;

	}

	/**
	 * 
	 * 函数功能描述：添加项目变更记录
	 * 
	 * @param projectChange
	 * @return
	 */
	public Integer addProjectChangeRecords(ProjectChangesEntity projectChange) {
		return voiceProjectMapper.addProjectChangeRecords(projectChange);
	}

	/**
	 * 
	 * 函数功能描述：修改原项目信息
	 * 
	 * @param project
	 * @return
	 */
	public Integer updateChangeProject(ProjectEntity project) {
		return voiceProjectMapper.updateChangeProject(project);
	}

	/**
	 * 获得变更记录表中最大变更次数 函数功能描述：TODO
	 * 
	 * @param projectChange
	 * @return
	 */
	public Integer getMaxSort(ProjectChangesEntity projectChange) {
		return voiceProjectMapper.getMaxSort(projectChange);
	}

	/**
	 * 
	 * 函数功能描述：获得专家集合
	 * 
	 * @param result
	 * @return
	 */
	public List<ExpertInfoEntity> queryExpertList(ResultEntity result) {

		return voiceProjectMapper.queryExpertList(result);

	}

	/**
	 * 
	 * 函数功能描述：查询项目变更记录
	 * 
	 * @return
	 */
	public List<ProjectEntity> queryPageProjectChangesList(ProjectEntity project) {
		List<ProjectEntity> list = voiceProjectMapper.queryPageProjectChangesList(project);
		return list;
	}

	/**
	 * 
	 * 函数功能描述：查询项目变更记录详情列表
	 * 
	 * @param projectChange
	 * @return
	 */
	public List<ProjectChangesEntity> queryProChangeList(ProjectChangesEntity projectChange) {
		List<ProjectChangesEntity> proChangeList = voiceProjectMapper.queryProChangeList(projectChange);
		return proChangeList;
	}

	/**
	 * 
	 * 函数功能描述：查询短信模板内容
	 * 
	 * @param condition
	 * @return
	 */
	public List<ConditionEntity> querySysMessageTemplateList(ConditionEntity condition) {
		return voiceProjectMapper.querySysMessageTemplateList(condition);
	}
}
