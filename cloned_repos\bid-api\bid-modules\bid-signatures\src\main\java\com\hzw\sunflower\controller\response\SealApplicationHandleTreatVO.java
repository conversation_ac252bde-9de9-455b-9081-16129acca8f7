package com.hzw.sunflower.controller.response;

import cn.afterturn.easypoi.excel.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @ClassName:SealApplicationHandleVO
 * @Auther: lijinxin
 * @Description: 用印管理处理列表vo
 * @Date: 2023/11/6 16:03
 * @Version: v1.0
 */
@Data
public class SealApplicationHandleTreatVO implements Serializable {


    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("applyId")
    private Long applyId;


    /**
     * 用印申请类型（1.呈报事项 2.合同事项 3.拟文 4.用印审批）
     */
    @ApiModelProperty("用印申请类型（1.呈报事项 2.合同事项 3.拟文 4.用印审批）")
    private Integer type;

    /**
     * 用印申请类型（1.呈报事项 2.合同事项 3.拟文 4.用印审批）
     */
    @Excel(name = "用印申请类型",orderNum = "1",width=25)
    @ApiModelProperty("用印申请类型（1.呈报事项 2.合同事项 3.拟文 4.用印审批）")
    private String typeInfo;

    @Excel(name = "用印事项",orderNum = "2",width=25)
    @ApiModelProperty("申请事项描述")
    private String itemDescribe;



    /**
     * 选择印章信息
     *
     * @mbg.generated
     */
    @Excel(name = "印章名称",orderNum = "3",width=25)
    @ApiModelProperty("选择印章信息")
    private String chooseSealInfo;


    /**
     * 项目信息
     */
    @Excel(name = "项目信息",orderNum = "4",width=25)
    @ApiModelProperty("项目信息")
    private String projectInfo ;

    /**
     * 申请人
     */
    @ApiModelProperty("申请人ID")
    private Long applyUser;


    @Excel(name = "申请人",orderNum = "5",width=25)
    @ApiModelProperty("申请人")
    private String applyUserName;


    /**
     * 用印申请时间
     */
    @ApiModelProperty("申请时间")
    @Excel(name = "申请时间",orderNum = "6",format = "yyyy-MM-dd",width=25)
    private Date applyTime;


    /**
     * 部门名称
     */
    @Excel(name = "所在处室",orderNum = "7",width=25)
    @ApiModelProperty("部门名称")
    private String deptName;

    /**
     * 签章类型 1电子签章 2纸质签章
     */
    @ApiModelProperty("签章类型 1电子签章 2纸质签章")
    private Integer sealType;

    /**
     * 签章类型 1电子签章 2纸质签章
     */
    @ApiModelProperty("签章类型 1电子签章 2纸质签章")
    @Excel(name = "用印方式",orderNum = "8",width=25)
    private String sealTypeInfo;

    /**
     * 文号
     */
    @ApiModelProperty("文号")
    @Excel(name = "文号",orderNum = "9",width=25)
    private String symbol;



    /**
     * 盖章人
     */
    @ApiModelProperty("盖章人")
    @Excel(name = "盖章人",orderNum = "10",width=25)
    private String sealedUser;


    /**
     * 盖章时间
     */
    @ApiModelProperty("盖章时间")
    @Excel(name = "盖章时间",orderNum = "11",format = "yyyy-MM-dd",width=25)
    private Date sealedTime;


    @ApiModelProperty("处理进度")
    @Excel(name = "处理进度",orderNum = "11",width=25)
    private String process;

    @ApiModelProperty("文件集合")
    private List<SealFileVO> sealFileList;



}
