package com.hzw.sunflower.service.impl;

import com.hzw.sunflower.dao.DataScopeMapper;
import com.hzw.sunflower.entity.JwtUser;
import com.hzw.sunflower.entity.Role;
import com.hzw.sunflower.service.CommonUserService;
import com.hzw.sunflower.service.DataScopeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DataScopeServiceImpl implements DataScopeService {

    @Autowired
    private DataScopeMapper dataScopeMapper;

    @Autowired
    private CommonUserService commonUserService;

    /**
     * 根据菜单id获取用户角色
     * @param jwtUser
     * @param menuId
     * @return
     */
    @Override
    public List<Role> getRolesByMenuId(JwtUser jwtUser,Long menuId) {
        if (null == menuId) {
            return commonUserService.getUserRoles().getRoles();
        }
        // 根据菜单id查询用户角色
        return dataScopeMapper.queryRoleByMenuId(jwtUser.getUser(), menuId);
    }
}
