package com.hzw.sunflower.constant.constantenum;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public enum BMFZRCodeEnum {

    BMFZR(RoleCodeEnum.BMFZR.getType(),RoleCodeEnum.BMFZR.getDesc()),
    BMFZRNJYB(RoleCodeEnum.BMFZRNJYB.getType(),RoleCodeEnum.BMFZRNJYB.getDesc()),
    BMFZR_NJEB(RoleCodeEnum.BMFZR_NJEB.getType(),RoleCodeEnum.BMFZR_NJEB.getDesc()),
    BMFZR_XZEB(RoleCodeEnum.BMFZR_XZEB.getType(),RoleCodeEnum.BMFZR_XZEB.getDesc()),
    BMFZR_XZYB(RoleCodeEnum.BMFZR_XZYB.getType(),RoleCodeEnum.BMFZR_XZYB.getDesc()),
    ;

    private String code;
    private String desc;

    BMFZRCodeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String type) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    //讲枚举转换成list格式，这样前台遍历的时候比较容易，列如 下拉框 后台调用toList方法，你就可以得到code 和name了
    public static Map<String, String> toMap() {
        Map<String, String> map = new HashMap<String, String>();
        for (BMFZRCodeEnum airlineTypeEnum : BMFZRCodeEnum.values()) {
            map.put(airlineTypeEnum.getCode(), airlineTypeEnum.getCode());
        }
        return map;
    }


    public static List<String> toList() {
        List<String> list = new ArrayList<>();
        for (BMFZRCodeEnum airlineTypeEnum : BMFZRCodeEnum.values()) {
            list.add(airlineTypeEnum.getCode());
        }
        return list;
    }
}
