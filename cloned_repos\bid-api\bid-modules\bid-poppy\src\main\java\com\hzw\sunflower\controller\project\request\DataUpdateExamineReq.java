package com.hzw.sunflower.controller.project.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date ：Created in 2021/7/20 15:01
 * @description：审核req
 * @modified By：`
 * @version: 1.0
 */
@Data
public class DataUpdateExamineReq {

    @ApiModelProperty(value = "id", required = true)
    private Long id;

    @ApiModelProperty(value = "审核实例ID", required = true)
    private String processInstanceId;

    @ApiModelProperty(value = "任务ID", required = true)
    private String taskId;

    @ApiModelProperty(value = "流程code", required = true)
    private String processCode;

    @ApiModelProperty(value = "操作  1：确定  2：退回 ", required = true)
    private String operation;

    @ApiModelProperty(value = "message意见")
    private String message;

    @ApiModelProperty(value = "报分管领导审批备注信息")
    private String notes;

}
