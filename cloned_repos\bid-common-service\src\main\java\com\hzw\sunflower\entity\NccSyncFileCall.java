package com.hzw.sunflower.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.hzw.sunflower.common.BaseBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * ncc同步文件调用表
 */
@ApiModel("t_ncc_sync_file_call")
@TableName("t_ncc_sync_file_call")
@Data
public class NccSyncFileCall extends BaseBean {

    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("推送数据类型 1.劳务费支付 2.劳务费报销")
    private Integer pushType;

    @ApiModelProperty("业务类型 1.支付 2.报销")
    private Integer businessType;

    @ApiModelProperty("单据id")
    private String pkBill;

    @ApiModelProperty("支付申请id")
    private Long applyId;

    @ApiModelProperty("报销id")
    private Long expenseApplyId;

    @ApiModelProperty("是否调用 1.是 2.否")
    private Integer isCall;

}
