package com.hzw.sunflower.entity;




import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class DepartProject implements Serializable {

    //处室编码
    @ExcelIgnore
    private Long departmentId;

    @ExcelProperty(value = "序号")
    private Integer seq;

    //处室名称
    @ExcelProperty(value = "处室")
    private String departmentName;

    //当期项目个数
    @ExcelProperty(value = {"项目数(个)","当月"})
    private Integer monthCount;

    //当年项目个数
    @ExcelProperty(value = {"项目数(个)","当年累计"})
    private Integer yearCount;

    //去年同期项目个数
    @ExcelProperty(value = {"项目数(个)","同期累计"})
    private Integer lastYearCount;

    //个数同比
    @ExcelProperty(value = {"项目数(个)","同比"})
    private BigDecimal countCompared;

    //当期项目金额
    @ExcelProperty(value = {"委托金额(万元)","当月"})
    private BigDecimal monthAmount;

    //当年项目金额
    @ExcelProperty(value = {"委托金额(万元)","当年累计"})
    private BigDecimal yearAmount;

    //去年同期项目金额
    @ExcelProperty(value = {"委托金额(万元)","同期累计"})
    private BigDecimal lastYearAmount;

    //金额同比
    @ExcelProperty(value = {"委托金额(万元)","同比"})
    private BigDecimal amountCompared;

}
