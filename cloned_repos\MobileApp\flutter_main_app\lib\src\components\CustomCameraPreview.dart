// ///***
// /// * 相机拍摄页面
// /// */

// import 'package:flutter/material.dart';
// import 'package:camera/camera.dart';
// import 'package:path/path.dart' show join;
// import 'package:path_provider/path_provider.dart';

// class CustomCameraPreview extends StatefulWidget {

//   CustomCameraPreview({
//     Key key,
//     @required this.camera,
//     @required this.onPictureTaken,
//   }) : super(key: key);

//   final CameraDescription camera;

//   final void Function(String path) onPictureTaken;

//   @override
//   _CustomCameraPreviewState createState() => _CustomCameraPreviewState();

// }

// class _CustomCameraPreviewState extends State<CustomCameraPreview> {

//   CameraController _controller;

//   Future<void> _initializeControllerFuture;

//   @override
//   void initState() {
//     super.initState();

//     _controller = CameraController(
//       widget.camera,
//       ResolutionPreset.medium,
//       enableAudio: false,
//     );

//     _initializeControllerFuture = _controller.initialize();
//   }

//   @override
//   void dispose() {
//     _controller.dispose();

//     super.dispose();
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       body: FutureBuilder(
//         future: _initializeControllerFuture,
//         builder: (context, snapshot) {
//           if (snapshot.connectionState == ConnectionState.done) {
//             return CameraPreview(_controller);
//           } else {
//             return Center(child: CircularProgressIndicator());
//           }
//         },
//       ),
//       floatingActionButton: FloatingActionButton(
//         child: Icon(Icons.camera_alt),
//         onPressed: () async {
//           /// * Take the Picture in a try / catch block. If anything goes wrong, catch the error.
//           try {
//             /// * Ensure that the camera is initialized.
//             await _initializeControllerFuture;

//             /// * Construct the path where the image should be saved using the pattern package.
//             final String path = join(
//               /// * Store the picture in the temp directory.
//               /// * Find the temp directory using the `path_provider` plugin.
//               (await getTemporaryDirectory()).path,
//               '${DateTime.now()}.png',
//             );

//             /// * Attempt to take a picture and log where it's been saved.
//             await _controller.takePicture(path);

//             /// * If the picture was taken, call the callback function with path as parameter.
//             widget.onPictureTaken(path);
//           } catch (e) {
//             /// * If an error occurs, log the error to the console.
//             print(e);
//           }
//         },
//       ),
//     );
//   }

// }
