package com.hzw.sunflower.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzw.sunflower.dao.ForeignCurrencyConvertMapper;
import com.hzw.sunflower.dao.ForeignCurrencyRateMapper;
import com.hzw.sunflower.entity.ForeignCurrencyConvert;
import com.hzw.sunflower.entity.ForeignCurrencyRate;
import com.hzw.sunflower.service.ForeignCurrencyConvertService;
import com.hzw.sunflower.service.ForeignCurrencyRateService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;


/**
 * <p>
 * 外币转换汇率凭证 服务实现类
 * </p>
 *
 * <AUTHOR> Gen
 * @since 2024-05-20
 */
@Service
public class ForeignCurrencyRateServiceImpl extends ServiceImpl<ForeignCurrencyRateMapper, ForeignCurrencyRate> implements ForeignCurrencyRateService {


    @Override
    public void submitForeignCurrencyRate(List<Long> fileIdList, Long covertId) {
        //全删全插
        LambdaQueryWrapper<ForeignCurrencyRate> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ForeignCurrencyRate::getConvertId,covertId);
        this.remove(queryWrapper);
        for(Long fileId:fileIdList){
            ForeignCurrencyRate foreignCurrencyRate = new ForeignCurrencyRate();
            foreignCurrencyRate.setRateFileId(fileId);
            foreignCurrencyRate.setConvertId(covertId);
            this.save(foreignCurrencyRate);
        }
    }

    @Override
    public List<Long> queryForeignCurrencyRate(Long covertId) {
        LambdaQueryWrapper<ForeignCurrencyRate> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ForeignCurrencyRate::getConvertId,covertId);
        List<ForeignCurrencyRate> list = this.list(queryWrapper);
        List<Long> fileList = list.stream().map(ForeignCurrencyRate::getRateFileId).collect(Collectors.toList());
        return fileList;
    }
}
