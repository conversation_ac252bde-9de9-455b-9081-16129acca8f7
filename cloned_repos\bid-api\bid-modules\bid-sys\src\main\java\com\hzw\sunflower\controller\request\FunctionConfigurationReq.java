package com.hzw.sunflower.controller.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class FunctionConfigurationReq {

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "招标人注册（1开放2不开放）")
    private Integer tendereeRegistration;

    @ApiModelProperty(value = "代理机构注册（1开放2不开放）")
    private Integer agencyRegistration;

    @ApiModelProperty(value = "供应商注册（1开放2不开放）")
    private Integer supplierRegistration;

    @ApiModelProperty(value = "身份证认证（1开放2不开放）")
    private Integer idcardAuthentication;

    @ApiModelProperty(value = "企业信息认证（1开放2不开放）")
    private Integer companyAuthentication;

    @ApiModelProperty(value = "短信")
    private Integer sms;
}
