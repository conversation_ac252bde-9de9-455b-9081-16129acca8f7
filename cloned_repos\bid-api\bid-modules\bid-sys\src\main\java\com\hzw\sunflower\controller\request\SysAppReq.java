package com.hzw.sunflower.controller.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


@ApiModel(description = "进场场地 ")
@Data
public class SysAppReq {

    private Long id;

    private String name;

    @ApiModelProperty(value = "0：启用 1：禁用")
    private Integer status;

    private String sn;

    private String secretKey;

    private String url;

    private String indexUrl;

    private String image;

    private Integer orderNo;

    @ApiModelProperty(value = "关键字：模糊搜索，系统名称、系统标识、系统URL前缀、系统URL首页")
    private String keywords;

}
