package com.hzw.sunflower.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hzw.sunflower.common.BaseBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <p>
 * app待阅
 * </p>
 *
 * <AUTHOR> Gen
 * @since 2022-12-15
 */
@Getter
@Setter
@TableName("app_to_read")
@ApiModel(value = "AppToRead对象", description = "app待阅表")
public class AppToRead extends BaseBean {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("标题")
    private String title;

    @ApiModelProperty("内容")
    private String content;

    @ApiModelProperty("业务类型code")
    private String businessCode;

    @ApiModelProperty("业务id")
    private Long businessId;

    @ApiModelProperty("项目id")
    private Long projectId;

    @ApiModelProperty("标段id")
    private String sectionId;

    @ApiModelProperty("轮次")
    private Integer bidRound;

    @ApiModelProperty("申请人id")
    private Long applyUserId;

    @ApiModelProperty("提出时间")
    private Date applyTime;

    @ApiModelProperty("是否已读 1.是 2.否")
    private Integer isRead;

}
