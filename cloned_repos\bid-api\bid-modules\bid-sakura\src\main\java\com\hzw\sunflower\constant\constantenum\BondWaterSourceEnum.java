package com.hzw.sunflower.constant.constantenum;

/**
 * <AUTHOR>
 * @date ：Created in 2022/07/27 9:23
 * @description：导入流水来源
 * @version: 1.0
 */
public enum BondWaterSourceEnum {

    EXCEL(2, "excel导入"),
    BANK_WATER(1, "银行流水");

    private Integer type;
    private String desc;

    BondWaterSourceEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
