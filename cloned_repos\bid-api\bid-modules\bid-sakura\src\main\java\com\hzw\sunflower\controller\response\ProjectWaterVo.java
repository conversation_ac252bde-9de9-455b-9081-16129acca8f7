package com.hzw.sunflower.controller.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 保证金流水表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-27
 */
@Data
@ApiModel(value = "BondWater对象", description = "保证金流水表")
public class ProjectWaterVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    private Long waterId;

    @ApiModelProperty("项目id")
    private Long projectId;

    @ApiModelProperty("标段id")
    private Long sectionId;

    @ApiModelProperty("供应商id")
    private Long companyId;

    @ApiModelProperty("项目名称")
    private String projectName;

    @ApiModelProperty("项目编号")
    private String projectNum;

    @ApiModelProperty("标段名称")
    private String packageName;

    @ApiModelProperty("标段号")
    private String packageNumber;

    @ApiModelProperty(value = "中标情况")
    private String bidStatus;
}
