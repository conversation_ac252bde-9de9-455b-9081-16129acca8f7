package com.hzw.ssm.fw.util;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class StringUtil {
	/**
	 * 将字符串时间转换成java.util.Date类型
	 * 
	 * @param str
	 *            要转换的字符
	 * @param format
	 *            时间格式
	 * @return 如果转换失败，返回null
	 */
	public static Date string2Date(String str, String format) {
		if (FwUtil.isEmpty(str) || FwUtil.isEmpty(format)) {
			return null;
		}

		// 定义日期/时间格式
		SimpleDateFormat sdf = new SimpleDateFormat(format);
		Date date;
		try {
			// 转换日期/时间格式
			date = sdf.parse(str);

			if (!str.equals(sdf.format(date))) {
				date = null;
			}
		} catch (ParseException e) {
			date = null;
		}

		return date;
	}

	/**
	 * 如果传入的字符串为null,则传出""
	 * 
	 * @param inStr
	 *            传入字符串
	 * @return outStr
	 */
	public static String changeNullToSpace(Object inStr) {

		return inStr == null ? "" : inStr.toString();
	}

	/**
	 * 去除右边全半角空格
	 * 
	 * @param strValue
	 *            String
	 * @return String String
	 */
	public static String rTrim(String strValue) {

		if ((strValue != null) && (!strValue.equals(""))) {
			char[] cValue = strValue.toCharArray();
			int nCur = 0;

			for (int i = cValue.length - 1; i > -1; i--) {
				if ((cValue[i] != '\u0020') && (cValue[i] != '\u3000')) {
					nCur = i;
					break;
				}
			}

			if ((nCur == 0)
					&& ((cValue[0] == '\u0020') || (cValue[0] == '\u3000'))) {
				return "";
			}

			return strValue.substring(0, nCur + 1);
		} else {
			return "";
		}
	}

	/**
	 * 去除半角空格
	 * 
	 * @param strValue
	 *            String
	 * @return String String
	 */
	public static String trim(String strValue) {

		String strReturn = strValue;
		if ((strValue != null)) {
			strReturn = strValue.trim();
		}
		return strReturn;
	}

	/**
	 * <PRE>
	 * 
	 * 转换null为"0".
	 * 
	 * </PRE>
	 * 
	 * .
	 * 
	 * @param str
	 *            String
	 * @return String 字符串为null返回"0"；否则返回原字符串.
	 */
	public static String zeroChangeNull(String str) {
		String result = str;

		if (null == str) {
			result = "0";
		}

		return result;
	}

	/**
	 * <PRE>
	 * 
	 * 转换字符串为Integer
	 * 
	 * </PRE>
	 * 
	 * @param value
	 *            String
	 * @return 转换后的Integer，失败是返回0
	 */
	public static Integer toInteger(String value) {

		try {
			return Integer.valueOf(value.trim());
		} catch (NumberFormatException e) {
			return new Integer(0);
		}
	}

	/**
	 * <PRE>
	 * 
	 * 转换字符串为Long
	 * 
	 * </PRE>
	 * 
	 * @param value
	 *            String
	 * @return 转换后的Long，失败是返回0
	 */
	public static Long toLong(String value) {

		try {
			return Long.valueOf(value);
		} catch (NumberFormatException e) {
			return new Long(0);
		}
	}

	/**
	 * <PRE>
	 * 
	 * 转换字符串为Short
	 * 
	 * </PRE>
	 * 
	 * @param value
	 *            String
	 * @return 转换后的Short，失败是返回0
	 */
	public static Short toShort(String value) {

		try {
			return Short.valueOf(value);
		} catch (NumberFormatException e) {
			return new Short("0");
		}
	}

	/**
	 * <PRE>
	 * 
	 * 转换字符串为Long
	 * 
	 * </PRE>
	 * 
	 * @param value
	 *            String
	 * @return 转换后的Long，失败是返回null
	 */
	public static Long toLongWithNull(String value) {

		try {
			return Long.valueOf(value);
		} catch (NumberFormatException e) {
			return null;
		}
	}

	/**
	 * <PRE>
	 * 
	 * 转换字符串为Double
	 * 
	 * </PRE>
	 * 
	 * @param value
	 *            String
	 * @return 转换后的Double，失败是返回0
	 */
	public static Double toDouble(String value) {

		try {
			return Double.valueOf(value);
		} catch (NumberFormatException e) {
			return new Double(0);
		}
	}

	/**
	 * <PRE>
	 * 
	 * 转换字符串为Boolean
	 * 
	 * </PRE>
	 * 
	 * @param value
	 *            0,1)
	 * @return Boolean 1-True/非1-False
	 */
	public static Boolean toBoolean(String value) {

		if ("1".equals(value)) {
			return Boolean.TRUE;
		} else {
			return Boolean.FALSE;
		}
	}

	/**
	 * 设置table的TR的class
	 * 
	 * @param rows
	 *            行数
	 * @return class style
	 */
	public static String[] getTrClass(int rows) {
		String[] arrCssValue = new String[rows];
		for (int i = 0; i < rows; i++) {
			if (i % 2 == 0) {
				arrCssValue[i] = "value0";

			} else {
				arrCssValue[i] = "value1";
			}

		}
		return arrCssValue;
	}

	/**
	 * <PRE>
	 * 
	 * check字节长度
	 * 
	 * </PRE>
	 * 
	 * @param param
	 *            String
	 * @return String GBK编码
	 * @throws Exception
	 *             Exception
	 */
	public static String getDecodePara(String param) throws Exception {

		if (param.equals("")) {
			return "";

		} else {

			return new String(param.getBytes("ISO-8859-1"), "GBK");

		}

	}

	/**
	 * 4舍5入,保留小数点后2位
	 * 
	 * @param value
	 *            value
	 * @param nScal
	 *            nScal
	 * @return String 4舍5入后的值
	 */
	public static String toDouble(double value, int nScal) {

		return String.valueOf(new java.math.BigDecimal(value).setScale(nScal,
				java.math.BigDecimal.ROUND_HALF_UP).doubleValue());
	}

	/**
	 * ArrayList数组转换成String数组.
	 * 
	 * @param lstArray
	 *            List
	 * @return String[] strTempArray
	 */
	public static String[] arrayListToStringArray(List lstArray) {
		String[] strTempArray = null;

		try {
			if (lstArray != null) {
				strTempArray = new String[lstArray.size()];

				for (int i = 0; i < lstArray.size(); i++) {
					strTempArray[i] = (String) lstArray.get(i);
				}
			}
		} catch (Exception e) {
			strTempArray = null;
		}

		return strTempArray;
	}

	/**
	 * 将文件大小字节转换为B;K;M;G
	 * 
	 * @param nSize
	 *            文件大小
	 * @return 转换后的文件大小
	 */
	public static String covSizeBKMG(int nSize) {
		String strRet = "";
		if (nSize / 1000 < 1) {
			strRet = nSize + "B";
		} else if (nSize / 1000 / 1000 < 1) {
			strRet = toDouble(((double) nSize) / 1000, 2) + "K";
		} else if (nSize / 1000 / 1000 / 1000 < 1) {
			strRet = toDouble((double) nSize / 1000 / 1000, 2) + "M";
		} else {
			strRet = toDouble((double) nSize / 1000 / 1000 / 1000, 2) + "G";
		}
		return strRet;
	}

	/**
	 * 转换日期格式的方法YYYY-MM-DD
	 * 
	 * @param time
	 *            传入要转换的时间
	 * @return 返回转换后的时间字符串
	 */
	public static String timeFormatChange(String time) {
		String first = time.substring(0, 1);
		first = numToStr(first);
		String second = time.substring(1, 2);
		second = numToStr(second);
		String third = time.substring(2, 3);
		third = numToStr(third);
		String forth = time.substring(3, 4);
		forth = numToStr(forth);
		// 月份
		String month = time.substring(5, 7);
		month = returnMonth(month);
		// 日
		String day = time.substring(8);
		day = returnDay(day);
		// 将年月日拼接
		StringBuffer buf = new StringBuffer();
		buf.append(first).append(second).append(third).append(forth)
				.append("年").append(month).append("月").append(day).append("日");
		// 返回时间
		time = buf.toString();
		return time;
	}

	/**
	 * 数字转换成中文的方法
	 * 
	 * @param num
	 *            传入要转换的数字
	 * @return 返回转换后的中文字符串
	 */
	public static String numToStr(String num) {
		if ("0".equals(num)) {
			num = "〇";
		} else if ("1".equals(num)) {
			num = "一";
		} else if ("2".equals(num)) {
			num = "二";
		} else if ("3".equals(num)) {
			num = "三";
		} else if ("4".equals(num)) {
			num = "四";
		} else if ("5".equals(num)) {
			num = "五";
		} else if ("6".equals(num)) {
			num = "六";
		} else if ("7".equals(num)) {
			num = "七";
		} else if ("8".equals(num)) {
			num = "八";
		} else if ("9".equals(num)) {
			num = "九";
		}
		return num;
	}

	/**
	 * 数字月份转换成中文的方法
	 * 
	 * @param month
	 *            传入要转换的月份
	 * @return 返回转换后的中文字符串
	 */
	private static String returnMonth(String month) {
		if (Integer.parseInt(month) < 10) {
			month = numToStr(month.substring(1));
		} else if (Integer.parseInt(month) == 10) {
			month = "十";
		} else {
			String monthSec = month.substring(1);
			monthSec = numToStr(monthSec);
			month = "十" + monthSec;
		}
		return month;
	}

	/**
	 * 数字日转换成中文的方法
	 * 
	 * @param day
	 *            传入要转换的日
	 * @return 返回转换后的中文字符串
	 */
	public static String returnDay(String day) {
		day = String.valueOf(Integer.parseInt(day));
		if (Integer.parseInt(day) < 10) {
			day = numToStr(day);
		} else if ((10 <= Integer.parseInt(day))
				&& (Integer.parseInt(day) < 20)) {
			if (10 == Integer.parseInt(day)) {
				day = "十";
			} else {
				String daySec = day.substring(1);
				daySec = numToStr(daySec);
				day = "十" + daySec;
			}
		} else if ((20 <= Integer.parseInt(day))
				&& (Integer.parseInt(day) < 30)) {
			if (20 == Integer.parseInt(day)) {
				day = "二十";
			} else {
				String daySec = day.substring(1);
				daySec = numToStr(daySec);
				day = "二十" + daySec;
			}
		} else {
			if (30 == Integer.parseInt(day)) {
				day = "三十";
			} else {
				String daySec = day.substring(1);
				daySec = numToStr(daySec);
				day = "三十" + daySec;
			}
		}
		return day;
	}

	/**
	 * <pre>
	 * 根据ID取得相应的名称
	 * </pre>
	 * 
	 * @param strIdNameArray
	 *            配列
	 * @param strId
	 *            编号
	 * @return 名称
	 */
	public static String getFormArrayByKey(String[][] strIdNameArray,
			String strId) {
		String strName = "";
		if (strIdNameArray != null && strIdNameArray.length > 0) {
			for (int i = 0; i < strIdNameArray.length; i++) {
				if (strIdNameArray[i][0] != null
						&& strIdNameArray[i][0].equals(strId)) {
					strName = strIdNameArray[i][1];
					break;
				}
			}
		}
		return strName;
	}

	/**
	 * 过滤html标签
	 * 
	 * @param inputString
	 * @return
	 */
	public static String html2Text(String inputString) {
		String htmlStr = inputString; // 含html标签的字符串
		String textStr = "";
		java.util.regex.Pattern p_script;
		java.util.regex.Matcher m_script;
		java.util.regex.Pattern p_style;
		java.util.regex.Matcher m_style;
		java.util.regex.Pattern p_html;
		java.util.regex.Matcher m_html;
		try {
			String regEx_script = "<[\\s]*?script[^>]*?>[\\s\\S]*?<[\\s]*?\\/[\\s]*?script[\\s]*?>"; // 定义script的正则表达式{或<script>]*?>[\s\S]*?<\/script>
			// }
			String regEx_style = "<[\\s]*?style[^>]*?>[\\s\\S]*?<[\\s]*?\\/[\\s]*?style[\\s]*?>"; // 定义style的正则表达式{或<style>]*?>[\s\S]*?<\/style>
			// }
			String regEx_html = "<[^>]+>"; // 定义HTML标签的正则表达式

			p_script = Pattern.compile(regEx_script, Pattern.CASE_INSENSITIVE);
			m_script = p_script.matcher(htmlStr);
			htmlStr = m_script.replaceAll(""); // 过滤script标签

			p_style = Pattern.compile(regEx_style, Pattern.CASE_INSENSITIVE);
			m_style = p_style.matcher(htmlStr);
			htmlStr = m_style.replaceAll(""); // 过滤style标签

			p_html = Pattern.compile(regEx_html, Pattern.CASE_INSENSITIVE);
			m_html = p_html.matcher(htmlStr);
			htmlStr = m_html.replaceAll(""); // 过滤html标签

			textStr = htmlStr;

		} catch (Exception e) {
			System.err.println("Html2Text: " + e.getMessage());
		}

		return textStr;
	}

	/**
	 * html符号转义
	 * 
	 * @param txt
	 * @return
	 */
	public static String toTextForHtml(String txt) {
		if (null == txt) {
			return "";
		}
		txt = txt.replaceAll("&", "&amp;");
		txt = txt.replaceAll("<", "&lt;");
		txt = txt.replaceAll(">", "&gt;");
		txt = txt.replaceAll("\"", "&quot;");
		txt = txt.replaceAll("'", "&#146;");
		return txt;
	}

	/**
	 * 把一个 0~9999 之间的整数转换为汉字的字符串，如果是 0 则返回 ""
	 * 
	 * @param amountPart
	 * @return
	 */
	public static String partTranslate(int amountPart) {

		if (amountPart < 0 || amountPart > 10000) {
			throw new IllegalArgumentException("参数必须是大于等于 0，小于 10000 的整数！");
		}
		final String[] chineseDigits = new String[] { "零", "一", "二", "三", "四",
				"五", "六", "七", "八", "九" };

		String[] units = new String[] { "", "十", "百", "千" };

		int temp = amountPart;

		String amountStr = new Integer(amountPart).toString();
		int amountStrLength = amountStr.length();
		boolean lastIsZero = true; // 在从低位往高位循环时，记录上一位数字是不是 0
		String chineseStr = "";

		for (int i = 0; i < amountStrLength; i++) {
			if (temp == 0) // 高位已无数据
				break;
			int digit = temp % 10;
			if (digit == 0) { // 取到的数字为 0
				if (!lastIsZero) // 前一个数字不是 0，则在当前汉字串前加“零”字;
					chineseStr = "零" + chineseStr;
				lastIsZero = true;
			} else { // 取到的数字不是 0
				chineseStr = chineseDigits[digit] + units[i] + chineseStr;
				lastIsZero = false;
			}
			temp = temp / 10;
		}
		return chineseStr;
	}

	/**
	 * 阿拉伯数字转换
	 * **/
	public static String getStringNember(int num) {
		String tempString = "";
		// int num = number.intValue();
		if ((num / 10) > 0) {
			tempString = getNum(num / 10) + "十";
			if ((num % 10) > 0) {
				tempString += getNum(num % 10);
			}
		} else if ((num % 10) > 0) {
			tempString += getNum(num % 10);
		}
		return tempString;
	}

	private static String getNum(int num) {
		switch (num) {
		case 1:
			return "一";
		case 2:
			return "二";
		case 3:
			return "三";
		case 4:
			return "四";
		case 5:
			return "五";
		case 6:
			return "六";
		case 7:
			return "七";
		case 8:
			return "八";
		case 9:
			return "九";
		default:
			return "";
		}

	}

	private static final String[] uppID = { "零", "壹", "贰", "叁", "肆", "伍", "陆",
			"柒", "捌", "玖" };

	public static long method(String num) {
		long high = 0;
		long mid = 0;
		long low = 0;
		String hi = "";
		String mi = "";
		String lo = "";
		hi = method1(num);
		mi = method2(num);
		lo = method3(num);
		System.out.println(hi + "  " + mi + " " + lo);
		if (hi != "") {
			high = suoShu(hi) * 100000000;
		}
		if (mi != "") {
			mid = suoShu(mi) * 10000;
		}
		if (lo != "") {
			low = suoShu(lo);
		}
		return high + mid + low;
	}

	private static String method1(String s) {
		if (s.contains("亿")) {
			String[] array = s.split("亿");
			return array[0];
		} else {
			return "";
		}
	}

	private static String method2(String s) {
		if (s.contains("亿")) {
			String[] array = s.split("亿");
			if (array[1].contains("万")) {
				String[] arr = array[1].split("万");
				return arr[0];
			} else {
				return "";
			}
		} else {
			if (s.contains("万")) {
				String[] arr = s.split("万");
				return arr[0];
			} else {
				return "";
			}
		}
	}

	private static String method3(String s) {
		if (!s.contains("万") && !s.contains("亿")) {
			return s;
		} else {
			String[] arr = s.split("万");
			return arr[arr.length - 1];
		}
	}

	private static long suoShu(String num) {
		char[] arr = num.toCharArray();
		long money = 0;
		long mult = 0;
		long no = 0;
		if (arr.length <= 1) {
			no = getNum(arr[0]);
			money = no * 1;
		} else {
			for (int i = 0; i < arr.length; i++) {
				long temp = 0;
				if (isNum(arr[i])) {
					no = getNum(arr[i]);
				} else if (isFlag(arr[i])) {
					mult = getFlag(arr[i]);
				}
				if (no > 0 && mult > 0) {
					temp = no * mult;
					no = 0;
					mult = 0;
				}
				money = money + temp;
			}
			money += no;
		}
		return money;
	}

	private static long getNum(char num) {
		switch (num) {
		case '零':
			return 0;
		case '壹':
			return 1;
		case '贰':
			return 2;
		case '叁':
			return 3;
		case '肆':
			return 4;
		case '伍':
			return 5;
		case '陆':
			return 6;
		case '柒':
			return 7;
		case '捌':
			return 8;
		case '玖':
			return 9;
		default:
			return 0;
		}
	}

	private static long getFlag(char flag) {
		switch (flag) {
		case '圆':
			return 1;
		case '拾':
			return 10;
		case '佰':
			return 100;
		case '仟':
			return 1000;
		case '万':
			return 10000;
		case '亿':
			return 100000000;
		default:
			return 0;
		}
	}

	private static boolean isNum(char num) {
		switch (num) {
		case '零':
			return true;
		case '壹':
			return true;
		case '贰':
			return true;
		case '叁':
			return true;
		case '肆':
			return true;
		case '伍':
			return true;
		case '陆':
			return true;
		case '柒':
			return true;
		case '捌':
			return true;
		case '玖':
			return true;
		default:
			return false;
		}
	}

	private static boolean isFlag(char flag) {
		switch (flag) {
		case '圆':
			return true;
		case '拾':
			return true;
		case '佰':
			return true;
		case '仟':
			return true;
		case '万':
			return true;
		case '亿':
			return true;
		case '兆':
			return true;
		default:
			return false;
		}
	}

	public static String translate(String money) {
		StringBuilder chinMoney = new StringBuilder();
		money = checkStart(money).trim();
		int zoreNum = 0;
		// 判断总位数
		// int total = money.trim().length();
		int index = money.indexOf(".");
		// 判断小数点前的位数
		int digit = 0;
		// 判断小数点后的位数
		int ldigit = 0;
		// 小数点前保存位数
		int temp = 0;
		// 小数点后保存位数
		int lasttemp = 0;
		String lmoney = "";
		String rmoney = "";
		if (index != -1) {
			lmoney = money.substring(0, index);
			digit = lmoney.length();
			rmoney = money.substring(index + 1, money.length());
			ldigit = rmoney.length();
		} else {
			lmoney = money;
			digit = money.length();
		}
		temp = digit;
		lasttemp = ldigit;
		if (temp > 0) { // 小数点前
			for (int i = 0; i < temp; i++) {
				// 判断当前位数是几
				int pos = Character.getNumericValue(lmoney.charAt(i));
				// 获取大写表示
				String chinDigit = uppID[pos];
				String unit = "";
				if (pos != 0) {
					// 得到当前最高位的中文表示
					unit = getUnitBefore(digit);
					zoreNum = 0;
				} else {
					// 如果亿、万为零，须特殊处理，零不说出，而保留亿或万，这时flag增加
					if (digit == 9 || digit == 5) {
						chinDigit = "";
						unit = getUnitBefore(digit);
					} else if (1 == digit) // 最后一位零不写
					{
						chinDigit = "";
					} else {
						++zoreNum;
					}
				}

				// 判断零的个数
				if (zoreNum > 1) {
					// 忽略该位，处理下一位
					if (pos == 0 && (i + 1) < temp) {
						int next = Character.getNumericValue(lmoney
								.charAt(i + 1));
						// begin lq 2013-5-31========>
                        int qianwei = 0;// 获得千位数
                        if (temp >= 8) {
                            qianwei = Integer.parseInt(lmoney.substring(
                                    temp - 8, temp - 8 + 1));
                        }
                        // 下一位不为零则加入
                        if (0 != next && zoreNum != 4 && zoreNum != 5&& zoreNum != 3) {// lq
                            if(!chinMoney.substring(chinMoney.length()-1,chinMoney.length()).equals("零")){//lq
                                chinMoney.append(unit).append(chinDigit);
                            }
                        } else if (9 == digit) {
                            chinMoney.append(unit).append(chinDigit);
                        } else if (5 == digit && qianwei != 0) {// lq
                            chinMoney.append(unit).append(chinDigit);
                        }
                        if (zoreNum == 3&&temp<=8&&!chinMoney.substring(chinMoney.length()-1,chinMoney.length()).equals("零")) {
                            chinMoney.append(unit).append(chinDigit);
                        }
                        if(zoreNum == 3&&temp>=9&&!chinMoney.substring(chinMoney.length()-1,chinMoney.length()).equals("零")){
                            chinMoney.append("").append(chinDigit);
                        }
                        // end lq 2013-5-31=========>
					}
					digit--;
				} else {
					// 如果是一十亿、一十万之类的，前面的“一”不要，这样更符合中文习惯
				    //注释掉  lq 2013-6-3
//					if (pos == 1) {
//						if ((digit == 6 || digit == 10) && 0 == i) {
//							chinDigit = "";
//						}
//					}
					if (pos == 0 && zoreNum > 0 && (i + 1) < temp
							&& (5 != digit || 9 != digit)) {
						int next = Character.getNumericValue(lmoney
								.charAt(i + 1));
						if (0 != next) {
							chinMoney.append(unit).append(chinDigit);
						}//==begin lq 2013-5-31
                        else if(temp>=7&&next==0&&(digit==5||digit==9)&&(unit.equals("万")||unit.equals("亿"))){
                            chinMoney.append(unit).append(chinDigit);
                        }
                        //==end ; lq 2013-5-31
					} else {
						chinMoney.append(chinDigit).append(unit);
					}
					digit--;
				}
			}
		}
		//begin lq  2013-5-31
		// modify by huyinmiao on 2013-07-03
       // if(chinMoney.substring(chinMoney.length()-1,chinMoney.length()).equals("零")){
		if(chinMoney.toString().length()>0&&chinMoney.substring(chinMoney.length()-1,chinMoney.length()).equals("零")){
       // modify end 
            chinMoney=new StringBuilder(chinMoney.substring(0,chinMoney.length()-1));
        }
        //end lq   2013-5-31
		if(!chinMoney.toString().endsWith("圆"))
		{
		    chinMoney.append("圆");
		}
		if (lasttemp > 0) {// 小数点后
			for (int i = 0; i < lasttemp; i++) {
				// 判断当前位数是几
				int pos = Character.getNumericValue(rmoney.charAt(i));
				// 获取大写表示
				String chinDigit = uppID[pos];
				String unit = "";
				// 得到当前最高位的中文表示
				//unit = getUnitAfter(ldigit);
				unit = getNewUnitAfter(i+1);
				chinMoney.append(chinDigit).append(unit);
				ldigit--;
			}
		}

		return chinMoney.toString();
	}

	/**
	 * 检查输入是否以零开头
	 * 
	 * @param money
	 * @return
	 */
	private static String checkStart(String money) {
		if (money.startsWith("0")) {
			money = money.substring(1);
		}
		return money.startsWith("0") ? checkStart(money) : money;
	}

	/**
	 * 位数表示，如亿、万等
	 * 
	 * @param digit
	 * @return
	 */
	private static String getUnitBefore(int digit) {
		String unit = null;
		switch (digit) {
		case 12:
		case 8:
		case 4:
			//unit = "千";
		    unit = "仟";
			break;
		case 11:
		case 7:
		case 3:
			//unit = "百";
		    unit = "佰";
			break;
		case 10:
		case 6:
		case 2:
			//unit = "十";
		    unit = "拾";
			break;
		case 9:
			unit = "亿";
			break;
		case 5:
			unit = "万";
			break;
		case 1:
			unit = "圆";
			break;
		default:
			unit = "";
			break;
		}
		return unit;
	}

	/**
	 * 检查小数位角分厘毫
	 * 
	 * @param money
	 * @return
	 */
	private static String getUnitAfter(int money) {
		String unit = null;
		switch (money) {
		case 1:
			unit = "毫";
			break;
		case 2:
			unit = "厘";
			break;
		case 3:
			unit = "分";
			break;
		case 4:
			unit = "角";
			break;
		default:
			unit = "";
			break;
		}
		return unit;
	}

	//新的得出角分
	private static String getNewUnitAfter(int money) {
        String unit = null;
        switch (money) {
        case 1:
            unit = "角";
            break;
        case 2:
            unit = "分";
            break;
        case 3:
            unit = "厘";
            break;
        case 4:
            unit = "毫";
            break;
        default:
            unit = "";
            break;
        }
        return unit;
    }
	public static String replace(String s, String org, String ob) {
		String newString = "";
		int first = 0;
		while (s.indexOf(org) != -1) {
			first = s.indexOf(org);
			if (first != s.length()) {
				newString = newString + s.substring(0, first) + ob;
				s = s.substring(first + org.length(), s.length());
			}

		}

		newString = newString + s;
		return newString;
	}

	/**
	 * 若传入的值小数点后为0，以整数形式显示 若传入的值小数点后不为0，保留两位小数
	 * */
	public static String keepDot(String num) {
		
		if (num.indexOf('.') == -1 || num.indexOf('E') != -1) {
			return num;
		} else {
			String str1 = num.substring(num.indexOf('.') + 1, num.length());
			// String st1 = num.substring(0, num.indexOf('.'));
			// if(Integer.parseInt(st1) == 0 && str1.length() >= 2){
			// String s1 = str1.substring(0, 2);
			// if(Integer.parseInt(s1) == 0){
			// return num;
			// }
			// }

			if (Integer.parseInt(str1) == 0) {
				return num.substring(0, num.indexOf('.'));
			} else {
				if (str1.length() == 1) {

					return num.substring(0, num.indexOf('.')) + "." + str1
							+ "0";
				} else if (str1.length() == 2) {
					return num.substring(0, num.indexOf('.')) + "."
							+ str1.substring(0, 2);

				} else {
					String sr1 = str1.substring(0, 2);
					int n1 = Integer.parseInt(str1.substring(2, 3));
					if (Integer.parseInt(sr1) != 0) {
						if (n1 >= 5) {
							Double d1 = Double.parseDouble(num.substring(0, num
									.indexOf('.'))
									+ "." + sr1);
							return (d1 + 0.01 + "").substring(0,
									(d1 + 0.01 + "").indexOf('.') + 3);
						} else {
							return num.substring(0, num.indexOf('.')) + "."
									+ sr1;
						}
					} else {
						int nn1 = 88;
						int nn2 = 88;
						int nn3 = 88;
						for (int i = 0; i < str1.length(); i++) {
							int nt = Integer.parseInt(str1.charAt(i) + "");
							if (nn1 == 88 && nt != 0) {
								nn1 = nt;
								nn3 = i;
							} else if (nn1 != 88) {
								nn2 = nt;
								nn3 = i;
							}
							if (nn1 != 88 && nn2 != 88) {
								nn3 = i;
								break;
							}
						}
						if (nn3 > 4) {
							return num.substring(0, num.indexOf('.')) + ".00";
						} else {
							String space = "";
							for (int i = 0; i <= nn3 - 2; i++) {
								space += "0";
							}
							if (nn2 >= 5 && nn2 != 88) {
								return num.substring(0, num.indexOf('.'))
										+ "."
										+ ((nn1 == 9) ? (space.substring(0,
												space.length() - 1) + "1")
												: (space + (nn1 + 1)));
							} else {
								return num.substring(0, num.indexOf('.')) + "."
										+ space + (nn2==88?"0":"") + nn1;
							}
						}
					}
				}
			}

		}

	}
	
	/**
	 * 将数字转为大写
	 * @param num
	 * @return
	 */
	public static String getBigNum(int num){
        String resultNum = null;
        String[] big={"","一","二","三","四","五","六","七","八","九"};  
        StringBuffer str=new StringBuffer();
        String strNum = String.valueOf(num);
        for(int i= strNum.length()-1 ; i >= 0 ; i--){
            String index= strNum.substring(i,i+1);
            str.insert(0,big[Integer.parseInt(index)]);
            if((strNum.length()-2>=0) && (i==strNum.length()-2)){
                if((!index.equals("十")) && (!index.equals("0"))){
                    str.insert(1, "十");
                }
                if(index.equals("0")){
                    str.insert(0, "零");
                }
            }
            if((strNum.length()-3>=0)&&(i==strNum.length()-3)){
                if((!index.equals("百")) && (!index.equals("0"))){
                    str.insert(1, "百");
                }
                if(index.equals("0")){
                    str.insert(0, "零");
                }
            }
        }
        resultNum = str.toString();
        if(resultNum.equals("一十"))
            return resultNum.substring(1);
        else if(resultNum.length() == 3){
            if(String.valueOf(resultNum.charAt(0)).equals("一"))
                return resultNum.substring(1);
        }
        return resultNum;
        }
	
	/**
     * 将数字转为大写
     * @param num
     * @return
     */
    public static String getBigNum(long num){
        String resultNum = null;
        String[] big={"","一","二","三","四","五","六","七","八","九"};  
        StringBuffer str=new StringBuffer();
        String strNum = String.valueOf(num);
        for(int i= strNum.length()-1 ; i >= 0 ; i--){
            String index= strNum.substring(i,i+1);
            str.insert(0,big[Integer.parseInt(index)]);
            if((strNum.length()-2>=0) && (i==strNum.length()-2)){
                if((!index.equals("十")) && (!index.equals("0"))){
                    str.insert(1, "十");
                }
                if(index.equals("0")){
                    str.insert(0, "零");
                }
            }
            if((strNum.length()-3>=0)&&(i==strNum.length()-3)){
                if((!index.equals("百")) && (!index.equals("0"))){
                    str.insert(1, "百");
                }
                if(index.equals("0")){
                    str.insert(0, "零");
                }
            }
        }
        resultNum = str.toString();
        if(resultNum.equals("一十"))
            return resultNum.substring(1);
        else if(resultNum.length() == 3){
            if(String.valueOf(resultNum.charAt(0)).equals("一"))
                return resultNum.substring(1);
        }
        return resultNum;
        }
    
    /**
     * 判断字符串中是否包含script,update set,insert into等敏感单词
     * @param str 待检字符串
     * @return true：有敏感词 false：无敏感词
     */
    public static boolean isAttack(String str) {
        boolean res = false;
        if (null == str || "".equals(str.trim())) {
            return res;
        }
        Pattern p = Pattern.compile("\\b\\w*[Ss]\\s*[Cc]\\s*[Rr]\\s*[Ii]\\s*[Pp]\\s*[Tt]\\b|\\b[Ii]\\s*[Nn]\\s*[Ss]\\s*[Ee]\\s*[Rr]\\s*[Tt]\\s*[Ii]\\s*[Nn]\\s*[Tt]\\s*[Oo]\\b|\\b[Uu]\\s*[Pp]\\s*[Dd]\\s*[Aa]\\s*[Tt]\\s*[Ee][\\s\\w]*[sS]\\s*[Ee]\\s*[Tt]\\b");
        Matcher m = p.matcher(str);
        res = m.matches();
        return res;
    }
    
    // 过滤特殊字符  
    public static boolean StringFilter(String str){                 
        // 清除掉所有特殊字符  
        boolean res = false;
        if (null == str || "".equals(str.trim())) {
            return res;
        }
        if(str.replaceAll("[\u4e00-\u9fa5]*[a-z]*[A-Z]*\\d*-*_*\\.*@*[u002E]*:*\\s*", "").length()!=0){
            res = true;
        }
        return res;     
    } 

	/**
	 * str转成含'的String（'aaa,bb'->aaa','bb）.
	 * 
	 * @param lstArray
	 * @return String 
	 */
	public static String strToString(String lstArray) {
		String strTempArray = new String();

		try {
			if (lstArray != null) {
				String[] temp = lstArray.split(",");
				for (String s:temp) {
					strTempArray += "'"+s+"',";
				}
				if(strTempArray.length()>0){
					strTempArray=strTempArray.substring(1,strTempArray.lastIndexOf("',"));
				}
			}
		} catch (Exception e) {
			strTempArray = null;
		}

		return strTempArray;
	}
	/**
	 * 将数组转换为list
	 * @param lstArray
	 * @return
	 */
	public static List<String> strToList(String lstArray){
		List<String> tempList = new ArrayList<String>();
		if (lstArray != null) {
			String[] temp =lstArray.split(",");
			tempList=Arrays.asList(temp);
		}
		return tempList;
	}
	
	/**
	 * str转成含'的String（aaa,bb->'aaa','bb'）.
	 * 
	 * @param lstArray
	 * @return String 
	 */
	public static List<String> strToString2(String lstArray) {
		List<String> expertTypeList = new ArrayList();
			if (lstArray != null) {
				String[] temp = lstArray.split(",");
				for (String s:temp) {
					expertTypeList.add(s);
				}
			}
		return expertTypeList;
	}
	/**
	 * @Description(去掉换行，空格，制表符) @param dest
	 * @return
	 */
	public static String deleteN(String dest) {
		Pattern p = Pattern.compile("\\s*|\t|\r|\n");
		Matcher m = p.matcher(dest);
		dest = m.replaceAll("");
		return dest;
	}
}
