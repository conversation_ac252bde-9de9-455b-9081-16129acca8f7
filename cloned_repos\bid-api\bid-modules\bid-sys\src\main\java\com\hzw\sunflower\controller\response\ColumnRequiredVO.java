package com.hzw.sunflower.controller.response;

import com.hzw.sunflower.entity.ColumnRequired;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class ColumnRequiredVO implements Serializable {

    @ApiModelProperty(value = "模块")
    private String moduleName;

    @ApiModelProperty(value = "模块code")
    private String moduleCode;

    @ApiModelProperty(value = "模块")
    private List<ColumnRequired> columnRequiredList;

}
