package com.hzw.sunflower.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hzw.sunflower.entity.ProjectBidDocRelation;
import org.apache.ibatis.annotations.Param;

/**
 * TProjectBidDocRelationMapper接口
 */
public interface ProjectBidDocRelationMapper extends BaseMapper<ProjectBidDocRelation> {


    ProjectBidDocRelation findDocRelation(@Param("projectId") Long projectId,@Param("sectionId") Long sectionId,@Param("bidRound")Integer bidRound);
}
