package com.hzw.sunflower.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.hzw.sunflower.common.BaseBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@ApiModel(description = "项目字段详细信息配置表-校验条件")
@TableName("s_column_check_config")
@Data
public class ColumnCheckConfig extends BaseBean {

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "采购方式id")
    private Long purchaseConfigId;

    @ApiModelProperty(value = "采购方式名称")
    private String purchaseModeName;

    @ApiModelProperty(value = "采购文件递交截至时间校验方式 1.不限制 2.仅作提示 3.强校验")
    private Integer submitEndTimeType;

    @ApiModelProperty(value = "时间")
    private Integer submitEndTimeDay;

    @ApiModelProperty(value = "单位 1.天 2.工作日")
    private Integer submitEndTimeUnit;

    @ApiModelProperty(value = "响应发售截至时间校验方式 1.不限制 2.仅作提示 3.强校验")
    private Integer saleEndTimeType;

    @ApiModelProperty(value = "时间")
    private Integer saleEndTimeDay;

    @ApiModelProperty(value = "单位 1.天 2.工作日")
    private Integer saleEndTimeUnit;

    @ApiModelProperty("文件售价说明限制金额")
    private BigDecimal amount;

}
