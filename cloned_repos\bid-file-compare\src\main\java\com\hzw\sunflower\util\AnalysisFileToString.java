package com.hzw.sunflower.util;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.text.PDFTextStripper;
import org.apache.poi.xwpf.extractor.XWPFWordExtractor;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import java.io.InputStream;
import java.util.List;

/**
 * <AUTHOR>
 * @datetime 2024/4/8 14:49
 * @description: 将文件解析成字符串
 * @version: 1.0
 */
@Slf4j
public class AnalysisFileToString {

    public static String readDocx(InputStream inputStream) {   //doc格式输出
        XWPFDocument document;
        //StringBuilder content = new StringBuilder();
        String text = "";
        try {
            document = new XWPFDocument(inputStream);
//            List<XWPFParagraph> paragraphs = document.getParagraphs();
//            for (XWPFParagraph para : paragraphs) {
//                content.append(para.getText());
//            }
            XWPFWordExtractor extractor = new XWPFWordExtractor(document);
            text = extractor.getText();
            text = text.replaceAll("\n", "").replaceAll("\r", "").replaceAll("\t", "");
        } catch (Exception e) {
            log.error("解析docx文件失败",e);
        }
        //这边要限制字符长度
        return text;
    }

    public static String readPDF(InputStream inputStream) {
        String resultPDF = "";
        PDDocument document = null;
        try{
            document = PDDocument.load(inputStream);
            PDFTextStripper stripper = new PDFTextStripper();
            resultPDF = stripper.getText(document);
            resultPDF = resultPDF.replaceAll("\n", "").replaceAll("\r", "").replaceAll("\t", "");
        }catch (Exception e ){
            log.error("解析docx文件失败",e);
        }
        return resultPDF;
    }
}
