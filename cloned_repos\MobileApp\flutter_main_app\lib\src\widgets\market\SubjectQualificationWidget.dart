///***
/// * 主体人|机构信息 - 资质
/// */

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import 'package:flutter_main_app/serviceLocator.dart';
import 'package:flutter_main_app/src/constants/theme.dart';

import 'package:flutter_main_app/src/viewModels/marketPlayer/SubjectQualificationViewModel.dart';

import 'package:flutter_main_app/src/models/marketPlayer/SubjectQualificationModel.dart';

import 'package:flutter_main_app/src/components/CustomAppBar.dart';
import 'package:flutter_main_app/src/components/CustomCircularProgressIndicator.dart';
import 'package:flutter_main_app/src/components/CustomSafeArea.dart';
import 'package:flutter_main_app/src/components/CustomInfoCard.dart';

class SubjectQualificationWidget extends StatefulWidget {
  const SubjectQualificationWidget({Key key}) : super(key: key);

  /// * 路由名称
  static const String _routeName = 'SubjectQualification';
  static String get routeName => _routeName;

  @override
  _SubjectQualificationWidgetState createState() =>
      _SubjectQualificationWidgetState();
}

class _SubjectQualificationWidgetState
    extends State<SubjectQualificationWidget> {

  /// * 主体资质 视图模型 单例
  final SubjectQualificationViewModel _model = serviceLocator<SubjectQualificationViewModel>();

  @override
  void initState() {
    super.initState();

    _model.fetchSubjectQualificationList();
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider<SubjectQualificationViewModel>.value(
      value: serviceLocator<SubjectQualificationViewModel>(),
      child: Consumer<SubjectQualificationViewModel>(
        builder: (context, model, child) {
          return Scaffold(
            appBar: CustomAppBar(
              title: '资质',
            ),
            body: model.isLoading
              ? CustomCircularProgressIndicator()
              : CustomSafeArea(
                  backgroundColor: themeContentBackgroundColor,
                  child: ListView.builder(
                    physics: BouncingScrollPhysics(),
                    itemCount: model.subjectQualificationList.length,
                    itemBuilder: (context, index) {
                      final SubjectQualificationModel qualification = model.subjectQualificationList[index];

                      return CustomInfoCard(
                        title: qualification.qualificationName != null && qualification.qualificationName.length > 0
                          ? qualification.qualificationName
                          : '---',
                        dataFields: [
                          {
                            'fieldName': '证书编号',
                            'fieldValue': qualification.qualificationNo,
                          },
                          {
                            'fieldName': '发证机关',
                            'fieldValue': qualification.approvalInstitution,
                          },
                          {
                            'fieldName': '有效日期',
                            'fieldValue': qualification.expiryDate,
                          },
                        ],
                        isFirstChild: index == 0,
                      );
                    },
                  ),
                ),
          );
        },
      ),
    );
  }

}
