package com.hzw.sunflower.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hzw.sunflower.entity.UploadFile;
import com.hzw.sunflower.entity.condition.UploadFileCondition;

import java.util.List;

/**
 * 上传文件存储表 Service接口
 *
 * <AUTHOR>
 * @version 1.0.0 2021-04-07
 */
public interface UploadFileService {
    /**
     * 根据条件分页查询上传文件存储表 列表
     *
     * @param condition 查询条件
     * @return 分页信息
     */
    IPage<UploadFile> findUploadFileByCondition(UploadFileCondition condition);

    /**
     * 根据主键ID查询上传文件存储表 信息
     *
     * @param id 主键ID
     * @return 上传文件存储表 信息
     */
    UploadFile getUploadFileById(Long id);

    /**
     * 新增上传文件存储表 信息
     *
     * @param uploadFile 上传文件存储表 信息
     * @return 是否成功
     */
    Boolean addUploadFile(UploadFile uploadFile);

    /**
     * 修改上传文件存储表 信息
     *
     * @param uploadFile 上传文件存储表 信息
     * @return 是否成功
     */
    Boolean updateUploadFile(UploadFile uploadFile);

    /**
     * 根据主键ID删除上传文件存储表
     *
     * @param id 主键ID
     * @return 是否成功
     */
    Boolean deleteUploadFileById(Long id);

    /**
     * 根据主键ID列表批量删除上传文件存储表
     *
     * @param idList 主键ID列表
     * @return 是否成功
     */
    Boolean deleteUploadFileByIds(List<Long> idList);
}