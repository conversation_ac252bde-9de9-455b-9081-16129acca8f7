package com.hzw.sunflower.constant;

/**
 * <AUTHOR>
 * @datetime 2024/05/21 17:27
 * @description: 缓存常量
 * @version: 1.0
 */
public class CacheConstant {

    /**
     * 未读数量
     */
    public static final String UN_READ_COUNT = "unreadCount-";

    /**
     * 转委托处理列表数量
     */
    public static final String TURN_ENTRUST_UNREAD_COUNT = "turnEntrustUnreadCount-";

    /**
     * 用户接口权限缓存
     */
    public static final String INTERFACE_AUTH_PERMISSION_AGENCY = "INTERFACE_AUTH_PERMISSION_AGENCY_";
    public static final String INTERFACE_AUTH_PERMISSION_OTHER = "INTERFACE_AUTH_PERMISSION_OTHER_";
}
