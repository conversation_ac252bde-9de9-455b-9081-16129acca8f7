package com.hzw.sunflower.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hzw.sunflower.common.BaseController;
import com.hzw.sunflower.common.Paging;
import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.common.annotation.RepeatSubmit;
import com.hzw.sunflower.constant.GeneralConstants;
import com.hzw.sunflower.controller.condition.TemCondition;
import com.hzw.sunflower.controller.request.TemplateReq;

import com.hzw.sunflower.controller.response.TemplateVo;
import com.hzw.sunflower.entity.Template;
import com.hzw.sunflower.service.SenMessService;
import com.hzw.sunflower.util.BeanListUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 *
 * 短信模板管理
 * <AUTHOR>
 * @since 2022/6/15
 */
@Api(tags = "短信模板管理")
@RestController
@RequestMapping("/smsTemplate")
public class SenMessController extends BaseController {

    @Resource
    private SenMessService senMessService;

    @ApiOperation(value = "查询短信模板列表 分页")
    @ApiImplicitParam(name = "req", value = "短信模板", required = true, dataType = "Template", paramType = "body")
    @PostMapping(value = "/querySMS")
    public Result<Paging<TemplateVo>> querySMS(@RequestBody TemCondition req) {
        IPage<TemplateVo> list = senMessService.getSmsList(req,getJwtUser().getUserId());
        return Result.ok(Paging.buildPaging(list));
    }

    @ApiOperation(value = "新增/修改短信模板")
    @ApiImplicitParam(name = "req", value = "短信", required = true, dataType = "TemplateReq", paramType = "body")
    @PostMapping(value = "/addOrUpdateSms")
    @RepeatSubmit
    public Result<Boolean> addOrUpdateSms(@RequestBody TemplateReq req) {
        Boolean flag = false;
        if(!(req.getTemplateContent().length() > GeneralConstants.TEMPLATE_CONTENT_LIMIT || req.getTemplateName().length() > GeneralConstants.TEMPLATE_NAME_LIMIT)){
            Template template = BeanListUtil.convert(req,Template.class);
            flag = senMessService.addOrUpdateSms(template);
        }
        return Result.okOrFailed(flag);
    }

    @ApiOperation(value = "删除短信模板")
    @ApiImplicitParam(name = "id", value = "主键ID", required = true)
    @GetMapping("/deleteSms/{id}")
    @RepeatSubmit
    public Result<Boolean> delete(@PathVariable Long id) {
        return Result.okOrFailed(senMessService.removeById(id));
    }

}
