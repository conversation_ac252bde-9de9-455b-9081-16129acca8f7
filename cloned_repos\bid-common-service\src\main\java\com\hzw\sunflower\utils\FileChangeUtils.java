package com.hzw.sunflower.utils;

import com.hzw.sunflower.config.aspect.FileChangeProperties;
import com.hzw.sunflower.entity.OssFile;
import com.hzw.sunflower.service.OssFileService;
import com.hzw.sunflower.util.FileUtils;
import com.hzw.sunflower.util.OssUtil;
import com.hzw.sunflower.util.WordToPdfAsposeUtil;
import com.hzw.sunflower.util.oss.OssFileStorage;
import com.hzw.sunflower.util.spring.SpringUtils;
import com.itextpdf.kernel.geom.PageSize;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;

/**
 * 文件更换类型
 */
@Slf4j
public class FileChangeUtils {

    /**
     * 根据文件id，获取文件并将文件抓为pdf 只支持pdf,doc,docx文件
     * @param ossFileId 文件id
     * @return
     */
    public static String changeFileToPdf(Long ossFileId) throws IOException {
        FileChangeProperties bean = SpringUtils.getBean(FileChangeProperties.class);
        String ossType = bean.getOssType();
        String tempFilePath = bean.getTempFilePath();
        OssFileService ossFileService = SpringUtils.getBean(OssFileService.class);
        String key = null;
        // 通过文件id 获取文件
        OssFile file = ossFileService.getOssFileById(ossFileId);
        // 判断文件是否为doc 或者docx 是的话转为pdf
        if(file.getOssFileName().endsWith(".doc") || file.getOssFileName().endsWith(".docx")){
            //下载文件
            OssFileStorage storage = OssUtil.getOssFileStorage(ossType);
            InputStream inputStream = null;
            // 直接下载文件流
            if(null != file && null != file.getOssFileKey() ){
                byte[] bytes = storage.downloadFile(file.getOssFileKey());
                inputStream = FileUtils.byte2InputStream(bytes);
            }
            // 去除文件后缀名
            String fileName = file.getOssFileName().substring(0,file.getOssFileName().lastIndexOf(".")) + ".pdf";
            // 转为pdf文件
            WordToPdfAsposeUtil.docToPdfByInputStream(inputStream,tempFilePath + fileName);
            try{
                // 上传文件
                key = storage.uploadFileByPath(tempFilePath + fileName);
            } catch (Exception e) {
                log.info("文件上传失败"+ e.getMessage());
            } finally {
                // 删除临时文件
                File file1 = new File(tempFilePath + fileName);
                if (file1.exists()) {
                    file1.delete();
                }
                inputStream.close();
            }
        } else if (file.getOssFileName().endsWith(".html")) {
            // 对HTML文件进行类似处理
            OssFileStorage storage = OssUtil.getOssFileStorage(ossType);
            InputStream inputStream = null;
            if (null != file.getOssFileKey()) {
                byte[] bytes = storage.downloadFile(file.getOssFileKey());
                inputStream = FileUtils.byte2InputStream(bytes);
            }
            String fileName = file.getOssFileName().substring(0, file.getOssFileName().lastIndexOf(".")) + ".pdf";
            PageSize pageSize = new PageSize(PageSize.A4.getHeight(), PageSize.A4.getWidth());
            try {
                IText7ChineseFont.convertToPdfStream(inputStream, tempFilePath + fileName, pageSize);
                key = storage.uploadFileByPath(tempFilePath + fileName);
            } catch (Exception e) {
                log.info("文件上传失败" + e.getMessage());
            } finally {
                File file1 = new File(tempFilePath + fileName);
                if (file1.exists()) {
                    file1.delete();
                }
                inputStream.close();
            }
        }
        else if(file.getOssFileName().endsWith(".pdf")){
            // 是pdf文件则直接返回文件key
            key = file.getOssFileKey();
        }
        return key;
    }


    public static void main(String[] args) {
        String inputPath = "D:/temporary/document.docx";
        String outputPath = "D:/temporary/document.pdf";
        WordToPdfAsposeUtil.docToPdf(inputPath, outputPath);
    }
}
