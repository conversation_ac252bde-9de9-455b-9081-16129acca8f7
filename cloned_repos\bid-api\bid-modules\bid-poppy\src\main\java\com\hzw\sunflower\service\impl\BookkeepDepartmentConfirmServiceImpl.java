package com.hzw.sunflower.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzw.sunflower.common.redis.RedisCache;
import com.hzw.sunflower.constant.constantenum.*;
import com.hzw.sunflower.controller.project.request.ConfirmAgreeReq;
import com.hzw.sunflower.controller.project.request.ConfirmSendSmsReq;
import com.hzw.sunflower.controller.project.request.ProjectIdsReq;
import com.hzw.sunflower.controller.project.response.BookkeepDepartmentConfirmVo;
import com.hzw.sunflower.dao.BookkeepDepartmentConfirmMapper;
import com.hzw.sunflower.dto.AppOaMsgDto;
import com.hzw.sunflower.dto.BookkeepDepartmentConfirmDTO;
import com.hzw.sunflower.dto.SendSmsDto;
import com.hzw.sunflower.entity.BookkeepDepartmentConfirm;
import com.hzw.sunflower.entity.JwtUser;
import com.hzw.sunflower.entity.Project;
import com.hzw.sunflower.entity.User;
import com.hzw.sunflower.entity.condition.BookkeepDepartmentConfirmCondition;
import com.hzw.sunflower.exception.ExceptionEnum;
import com.hzw.sunflower.exception.SunFlowerException;
import com.hzw.sunflower.service.*;
import com.hzw.sunflower.util.BeanListUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class BookkeepDepartmentConfirmServiceImpl extends ServiceImpl<BookkeepDepartmentConfirmMapper, BookkeepDepartmentConfirm> implements BookkeepDepartmentConfirmService {

    @Autowired
    private CommonSmsService smsPublicService;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private CommonOpenService commonOpenService;

    @Autowired
    private UserOpenService userOpenService;

    @Autowired
    private BookkeepDepartmentConfirmService bookkeepDepartmentConfirmService;

    @Autowired
    private ProjectService projectService;

    @Autowired
    private CommonMqService commonMqService;
    /**
     * 角标是否展示
     * @param project
     * @return
     */
    @Override
    public Map<Long,Integer> isShow(ProjectIdsReq project) {
        Map<Long,Integer> map = new HashMap<>();
        for(Long projectId:project.getProjectIds()){
            LambdaQueryWrapper<BookkeepDepartmentConfirm> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(BookkeepDepartmentConfirm::getProjectId,projectId);
            List<BookkeepDepartmentConfirm> list = bookkeepDepartmentConfirmService.list(queryWrapper);
            if(list.size()<1){
                map.put(projectId,0);//0：不展示
            }else{
                //是否存在退回的情况
                List<BookkeepDepartmentConfirm> refundList = list.stream().filter(x -> BookKeepConfirmStatusEnum.REFUNDED.getType().equals(x.getConfirmStatus())).collect(Collectors.toList());
                List<BookkeepDepartmentConfirm> conformList = list.stream().filter(x -> BookKeepConfirmStatusEnum.CONFIRMED.getType().equals(x.getConfirmStatus())).collect(Collectors.toList());
                if(refundList.size()>0){//存在审核退回
                    map.put(projectId,2);//2：退回
                }else if(conformList.size() == list.size()){//全部确认
                    map.put(projectId,0);//0：不展示
                } else{
                    map.put(projectId,1);//1：待确认
                }
            }
        }
        return map;
    }

    /**
     * 分摊人确认
     * @param confirmAgreeReq
     * @return
     */
    @Override
    public Boolean saveIsAgree(ConfirmAgreeReq confirmAgreeReq) {
        Boolean result = false;
        BookkeepDepartmentConfirm c = getById(confirmAgreeReq.getId());
        if(c == null){
            throw new SunFlowerException(ExceptionEnum.ILLEGAL_PARAMETER_EXCEPTION, ExceptionEnum.ILLEGAL_PARAMETER_EXCEPTION.getMessage());
        }
        if(!BookKeepConfirmStatusEnum.WAIT_CONFIRM.getType().equals(c.getConfirmStatus())){
            throw new SunFlowerException(ExceptionEnum.DATA_IS_PASS, ExceptionEnum.DATA_IS_PASS.getMessage());
        }

        //退回
        if(!confirmAgreeReq.getIsPass()){
            c.setConfirmStatus(BookKeepConfirmStatusEnum.REFUNDED.getType());
            c.setReturnReason(confirmAgreeReq.getReturnReason());
        }else{//同意
            c.setConfirmStatus(BookKeepConfirmStatusEnum.CONFIRMED.getType());
            //普通员工同意后生成一条待处长审批的数据
            if(BookKeepConfirmUserTypeEnum.ORDINARY.getType().equals(c.getUserType())){
                BookkeepDepartmentConfirm confirm = BeanListUtil.convert(c,BookkeepDepartmentConfirm.class);
                List<String> czCodes = CZCodeEnum.toList();
                //查询该用户下所有的处长
                List<User> users = commonOpenService.getChuZhangByUserId(czCodes,Long.valueOf(c.getConfirmUserId()));
                String userIds = "";
                String userNames = "";
                List<String> recipientList = new ArrayList<>();
                if(!CollectionUtils.isEmpty(users)){//判断是否有处长
                    for (User u:users){
                        userIds += u.getId()+",";
                        userNames += u.getUserName()+",";
                        recipientList.add(u.getOtherUserId());
                    }
                    confirm.setConfirmStatus(BookKeepConfirmStatusEnum.WAIT_CONFIRM.getType());
                    confirm.setConfirmUserId(userIds.substring(0,userIds.length()-1));
                    confirm.setUserType(BookKeepConfirmUserTypeEnum.CHUZHANG.getType());
                    confirm.setConfirmUserName(userNames.substring(0,userNames.length()-1));
                    this.save(confirm);

                    // 发送app待办
                    AppOaMsgDto dto = new AppOaMsgDto();
                    dto.setTaskType(AppTaskTypeEnum.PROJECT_SHARE.getCode());
                    dto.setRecipientList(recipientList);
                    commonMqService.sendOaMsg(dto);
                }
            }
        }
       this.updateById(c);

        Boolean isUpdate = true;
        //判断所有审批人是否都同意了，都同意的时候，修改项目状态
        LambdaQueryWrapper<BookkeepDepartmentConfirm> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BookkeepDepartmentConfirm::getProjectId,c.getProjectId());
        List<BookkeepDepartmentConfirm> all = this.list(queryWrapper);
        for(BookkeepDepartmentConfirm a:all){
            //有一个状态不是已确认的，就返回false
            if(!BookKeepConfirmStatusEnum.CONFIRMED.getType().equals(a.getConfirmStatus())){
                isUpdate = false;
            }
        }
        //同意之后，修改项目状态为3 可划分标段包
        if(isUpdate){
            Project project = new Project();
            project.setId(c.getProjectId());
            project.setStatus(ProjectStatusEnum.FINISH.getValue());
            result = projectService.updateProject(project);

            //推送项目到ncc
            projectService.pushProjectNcc(project.getId(),null);
        }
        return result;
    }


    /**
     * 分摊人确认列表
     * @param condition
     * @param jwtUser
     * @return
     */
    @Override
    public IPage<BookkeepDepartmentConfirmVo> queryShareList(BookkeepDepartmentConfirmCondition condition, JwtUser jwtUser) {
        jwtUser.getUserId();
        IPage<BookkeepDepartmentConfirmVo> page = condition.buildPage();
        page = this.baseMapper.queryShareList(page, condition,jwtUser.getUserId());
        return page;
    }

    /**
     * 分摊人确认详情
     * @param projectId
     * @return
     */
    @Override
    public List<BookkeepDepartmentConfirmDTO> shareListDetail(Long projectId) {
        //根据项目id查询分摊人情况
        List<BookkeepDepartmentConfirmDTO> dtos = new ArrayList<>();
        LambdaQueryWrapper<BookkeepDepartmentConfirm> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BookkeepDepartmentConfirm::getProjectId,projectId);
        List<BookkeepDepartmentConfirm> list = bookkeepDepartmentConfirmService.list(queryWrapper);
        for (BookkeepDepartmentConfirm confirm:list) {
            BookkeepDepartmentConfirmDTO dto = new BookkeepDepartmentConfirmDTO();
            BeanUtil.copyProperties(confirm, dto);
            if(confirm.getUserType().equals(BookKeepConfirmTypeEnum.EMPLOYEES.getType())){
                Boolean isCz = commonOpenService.checkIsCz(Long.valueOf(confirm.getConfirmUserId()));
                if(!isCz) {
                    // 项目经理待确认展示下一步审批人姓名
                    List<String> czCodes = CZCodeEnum.toList();
                    List<User> users = commonOpenService.getChuZhangByUserId(czCodes, Long.valueOf(confirm.getConfirmUserId()));
                    String userNames = "";
                    if(!CollectionUtils.isEmpty(users)){
                        for (User u : users) {
                            userNames += u.getUserName() + ",";
                        }
                        dto.setManagerName(userNames.substring(0, userNames.length() - 1));
                    }
                }
            } else {
                dto.setManagerConfirm(confirm.getConfirmStatus());
            }
            dtos.add(dto);
        }
        return dtos;
    }

    /**
     * 发送短信
     * @param projectId
     * @return
     */
    @Override
    public Boolean sendConfirmSms(Long projectId, Long agentChargeId,Long userId,Integer type) {
        String typeName = "";
        Boolean result = true;
        List<String> userMobileList = new ArrayList<>();
        if((BookKeepConfirmTypeEnum.COST.getType()).equals(type)){
            typeName = BookKeepConfirmTypeEnum.COST.getDesc();
        } else {
            typeName = BookKeepConfirmTypeEnum.INCOME.getDesc();
        }
        User user = this.baseMapper.findUser(projectId);
        userMobileList.add(this.baseMapper.findPhone(userId));
        if(!CollectionUtils.isEmpty(userMobileList)){
            String templateNameSuccess = user.getUserName() + "提交的立项项目申请，选择了您为" + typeName + "记账分摊人，请登录系统后在工作台-项目分摊确认菜单中进行确认。";

            ConfirmSendSmsReq sendSmsReq = new ConfirmSendSmsReq();
            sendSmsReq.setIsTemplate(false);
            sendSmsReq.setProjectId(projectId);
            sendSmsReq.setTemplateCode(SmsTypeEnum.FTQR.getType());
            sendSmsReq.setTemplateName(templateNameSuccess);
            sendSmsReq.setUserMobileList(userMobileList);
            result = this.sendSms(sendSmsReq);
        }
        return result;
    }


    public Boolean sendSms(ConfirmSendSmsReq req) {
        Boolean flag = false;
        List<String> mobileList = req.getUserMobileList();
        for (String mobile : mobileList) {
            //String code = RandoUtil.generatorCode();
            //发送短信
            SendSmsDto dto = new SendSmsDto();
            dto.setSendPhone(mobile);
            dto.setTemplateCode(req.getTemplateCode());
            dto.setIsTemplate(req.getIsTemplate());
            dto.setSmsContent(req.getTemplateName());
            // 新增项目id和标段id
            dto.setProjectId(req.getProjectId());
            dto.setSectionIds(req.getSectionId());
            if (smsPublicService.sendSms(dto)) {
                //String key = req.getTemplateCode() + mobile;
                //发送成功存放到redis缓存
                //redisCache.setCacheObject(key, code, SmsConstants.SMS_CODE_TIME, TimeUnit.MILLISECONDS);
                flag = true;
            }
            if (!flag) {
                return flag;
            }
        }
        return flag;
    }

    @Override
    public Boolean saveBookKeepConfirmInfo(BookkeepDepartmentConfirm confirm) {
        List<String> czCodes = CZCodeEnum.toList();
        //是否是处长
        Boolean isCz = commonOpenService.checkIsCz(Long.valueOf(confirm.getConfirmUserId()));
        if(isCz){
            //查询该用户下所有的处长
            List<User> users = commonOpenService.getChuZhangByUserId(czCodes,Long.valueOf(confirm.getConfirmUserId()));
            String userNames = "";
            for (User u:users){
                if(String.valueOf(u.getId()).equals(confirm.getConfirmUserId())){
                    userNames = u.getUserName();
                }
            }
            confirm.setConfirmStatus(BookKeepConfirmStatusEnum.WAIT_CONFIRM.getType());
            confirm.setConfirmUserId(confirm.getConfirmUserId());
            confirm.setUserType(BookKeepConfirmUserTypeEnum.CHUZHANG.getType());
            confirm.setConfirmUserName(userNames);
        }else{
            User user = userOpenService.getById(confirm.getConfirmUserId());
            //不是处长的时候，状态为待确认
            confirm.setConfirmStatus(BookKeepConfirmStatusEnum.WAIT_CONFIRM.getType());
            confirm.setUserType(BookKeepConfirmUserTypeEnum.ORDINARY.getType());
            confirm.setConfirmUserName(user.getUserName());
            confirm.setConfirmUserId(user.getId().toString());
        }
        confirm.setApplyTime(new Date());
        return saveOrUpdate(confirm);
    }

}
