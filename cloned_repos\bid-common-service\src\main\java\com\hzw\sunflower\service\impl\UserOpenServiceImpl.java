package com.hzw.sunflower.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzw.sunflower.controller.response.DepartmentInfoVo;
import com.hzw.sunflower.dao.UserOpenMapper;
import com.hzw.sunflower.entity.User;
import com.hzw.sunflower.service.UserOpenService;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 *UserService接口实现
 *
 * <AUTHOR>
 * @version 1.0.0 2021-04-21
 */
@Service
public class UserOpenServiceImpl extends ServiceImpl<UserOpenMapper, User> implements UserOpenService {

    @Override
    public DepartmentInfoVo getDeptInfoByUserId(Long userId, Integer identity) {
        return this.baseMapper.getDeptInfoByUserId(userId,identity);
    }

    /**
     * 获取用户数据
     * @param phone
     * @param identity
     * @return
     */
    @Override
    public User findUserWechatByPhone(Integer identity, String phone) {
        return this.baseMapper.findUserWechatByPhone(identity,phone);
    }

    @Override
    public List<DepartmentInfoVo> getDepartInfoByOtherUserId(String otherUserId) {
        return this.baseMapper.getDepartInfoByOtherUserId(otherUserId);
    }

    @Override
    public User getByOtherId(String userCode) {
        return this.baseMapper.getByOtherId(userCode);
    }
}