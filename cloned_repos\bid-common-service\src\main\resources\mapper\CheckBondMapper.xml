<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!-- 租户表 (t_tenant) -->
<mapper namespace="com.hzw.sunflower.dao.CheckBondMapper">

    <select id="getOfflineBondRefund" resultType="java.lang.Integer">
        select count(1) from t_bond where is_delete = 0
        and section_id in
        <foreach collection="sectionIds" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        and supplier_id = #{companyId}
        and status != 0
    </select>

    <select id="getOnlineBondRefund" resultType="java.lang.Integer">
        select sum(t.count)
        from
        (
            select count(1) as count from t_bond_relation where is_delete = 0
            and section_id in
            <foreach collection="sectionIds" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
            and company_id = #{companyId}
            union
            select count(1) as count from t_bond_refund where is_delete = 0
            and section_id in
            <foreach collection="sectionIds" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
            and company_id = #{companyId}
        ) t
    </select>
</mapper>