package com.hzw.ssm.expert.entity;

import com.hzw.ssm.fw.base.BaseEntity;

/**
 * 专家评价备注表
 * 
 * <AUTHOR> @date 2014-10-17
 */
public class AppraiseRemark extends BaseEntity {
	private static final long serialVersionUID = 1L;

	private String appraise_remark_id;// 主键

	private String extract_result_id;// 专家结果ID

	private String remark;// 备注

	public String getAppraise_remark_id() {
		return appraise_remark_id;
	}

	public void setAppraise_remark_id(String appraiseRemarkId) {
		appraise_remark_id = appraiseRemarkId;
	}

	public String getExtract_result_id() {
		return extract_result_id;
	}

	public void setExtract_result_id(String extractResultId) {
		extract_result_id = extractResultId;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

}
