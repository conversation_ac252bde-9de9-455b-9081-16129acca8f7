import "package:flutter/material.dart";

import 'package:flutter_main_app/src/constants/theme.dart';
import 'package:flutter_main_app/src/utils/ScreenUtils.dart';

import 'package:flutter_main_app/src/components/CustomAppBar.dart';

import 'package:flutter_main_app/src/widgets/bulletindetail/BulletinContact.dart';
import 'package:flutter_main_app/src/widgets/bulletindetail/BulletinDetailHeader.dart';
import 'package:flutter_main_app/src/widgets/bulletindetail/BulletinBottomBtn.dart';

class TenderBulletinDetail extends StatelessWidget {
  const TenderBulletinDetail({Key key}) : super(key: key);

  /// * 路由名称
  static const String _routeName = 'TenderBulletinDetail';
  static String get routeName => _routeName;

  static TextStyle titleTextStyle = TextStyle(
    color: themeTextColor,
    fontWeight: FontWeight.bold,
    fontSize: themeFontSize,
  );

  static TextStyle normalTextStyle = TextStyle(
    color: themeTextColor,
    fontSize: themeFontSize,
    height: 2,
  );

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: '招标公告详情',
        actions: <Widget>[
          IconButton(
            icon: Icon(Icons.share, color: Colors.white),
            onPressed: null,
            iconSize: 20,
          )
        ],
      ),
      body: Container(
        color: themeContentBackgroundColor,
        child: Stack(
          children: <Widget>[
            ListView(
              children: <Widget>[
                BulletinDetailHeader(),
                Padding(
                  padding: EdgeInsets.fromLTRB(15, 18, 0, 12),
                  child: Text(
                    '1. 招标条件',
                    style: titleTextStyle,
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.fromLTRB(15, 0, 15, 25),
                  child: Text(
                    '江苏顺悦建设监理有限公司受南京河西新城建设发展有限公司的委托，就其南京河西集中供热管网迁线工程所需直埋蒸汽保温管材料采购进行国内公开招标，现就有关事宜公告如下',
                    style: normalTextStyle,
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.fromLTRB(15, 0, 15, 12),
                  child: Text(
                    '2. 项目概述与招标范围',
                    style: titleTextStyle,
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.fromLTRB(15, 0, 15, 25),
                  child: Column(
                    children: <Widget>[
                      Padding(
                        padding: const EdgeInsets.only(bottom: 10),
                        child: Row(
                          children: <Widget>[
                            Text(
                              '项目批准单位：',
                              style: normalTextStyle,
                            ),
                            Padding(
                              padding: const EdgeInsets.only(left: 5),
                              child: Text(
                                '南京河西新城区开发建设管理委员会',
                                style: normalTextStyle,
                              ),
                            ),
                          ],
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.only(bottom: 10),
                        child: Row(
                          children: <Widget>[
                            Text(
                              '项目批准文件编号:',
                              style: normalTextStyle,
                            ),
                            Padding(
                              padding: const EdgeInsets.only(left: 5),
                              child: Text(
                                '宁新城委综字[2018]171号',
                                style: normalTextStyle,
                              ),
                            ),
                          ],
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.only(bottom: 10),
                        child: Row(
                          children: <Widget>[
                            Text(
                              '项目名称:',
                              style: normalTextStyle,
                            ),
                            Padding(
                              padding: const EdgeInsets.only(left: 5),
                              child: Text(
                                '南京河西集中供热管网迁线工程',
                                style: normalTextStyle,
                              ),
                            ),
                          ],
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.only(bottom: 10),
                        child: Row(
                          children: <Widget>[
                            Text(
                              '项目地点:',
                              style: normalTextStyle,
                            ),
                            Padding(
                              padding: const EdgeInsets.only(left: 5),
                              child: Text(
                                '建邺区',
                                style: normalTextStyle,
                              ),
                            ),
                          ],
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.only(bottom: 10),
                        child: Row(
                          children: <Widget>[
                            Text(
                              '标段名称:',
                              style: normalTextStyle,
                            ),
                            Padding(
                              padding: const EdgeInsets.only(left: 5),
                              child: Text(
                                '直埋蒸汽保温管材料采购 ',
                                style: normalTextStyle,
                              ),
                            ),
                          ],
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.only(bottom: 10),
                        child: Row(
                          children: <Widget>[
                            Text(
                              '招标数量:',
                              style: normalTextStyle,
                            ),
                            Padding(
                              padding: const EdgeInsets.only(left: 5),
                              child: Text(
                                '1批  ',
                                style: normalTextStyle,
                              ),
                            ),
                          ],
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.only(bottom: 10),
                        child: Row(
                          children: <Widget>[
                            Text(
                              '招标内容及规格:',
                              style: normalTextStyle,
                            ),
                            Padding(
                              padding: const EdgeInsets.only(left: 5),
                              child: Text(
                                '直埋蒸汽管',
                                style: normalTextStyle,
                              ),
                            ),
                          ],
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.only(bottom: 10),
                        child: Row(
                          children: <Widget>[
                            Text(
                              '交货时间:',
                              style: normalTextStyle,
                            ),
                            Padding(
                              padding: const EdgeInsets.only(left: 5),
                              child: Text(
                                '接甲方书面通知后30日历天交付至甲方指定',
                                style: normalTextStyle,
                              ),
                            ),
                          ],
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.only(bottom: 10),
                        child: Row(
                          children: <Widget>[
                            Text(
                              '资金来源:',
                              style: normalTextStyle,
                            ),
                            Padding(
                              padding: const EdgeInsets.only(left: 5),
                              child: Text(
                                '国有资金',
                                style: normalTextStyle,
                              ),
                            ),
                          ],
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.only(bottom: 10),
                        child: Row(
                          children: <Widget>[
                            Text(
                              '合同估算价:',
                              style: normalTextStyle,
                            ),
                            Padding(
                              padding: const EdgeInsets.only(left: 5),
                              child: Text(
                                '900（万元）',
                                style: normalTextStyle,
                              ),
                            ),
                          ],
                        ),
                      )
                    ],
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.fromLTRB(15, 0, 15, 12),
                  child: Text(
                    '3. 投标人资格要求',
                    style: titleTextStyle,
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.fromLTRB(15, 0, 15, 12),
                  child: Text(
                    '3.1 投标人要求：',
                    style: normalTextStyle,
                  ),
                ),
                Padding(
                    padding: const EdgeInsets.fromLTRB(15, 0, 15, 12),
                    child: Text.rich(TextSpan(
                      style: normalTextStyle,
                      children: <TextSpan>[
                        TextSpan(
                          text: '(1)资质要求：\r\n',
                        ),
                        TextSpan(
                            text:
                                "①投标人应具有独立的法人资格，营业执照在有效期内,招 标内容在营业执照的经营范围内。制造商注册资金不低于 10000万元人民币或等值外币（汇率以 开标 当天中国人民 银行公布的汇率为准）；如投标人为代理商的,代理商注册 资金不低于/ 万元人民币或等值外币（汇率以开标当天中国 人民银行公布的汇率为准）外，且其授权制造商注册资金应满足前款对制造商的要求。 \r\n"),
                        TextSpan(
                          text:
                              '②其他条件：a、本项目不接受代理商投标；b、投标人的营业执照经营范围包含蒸汽管或热力管道或地埋式蒸汽保温管或保温管制造（证明文件以供应商库内的信息为准，并挑选至电子投标文件中）。\r\n',
                        ),
                        TextSpan(
                          text: '(2)财务要求：\r\n',
                        ),
                        TextSpan(
                            text:
                                '①投标人须提供 2018 年度至 2018 年度经会计师事务所或审计机构审计的财务会计报表，包括资产负债表、现金流量表、利润表和财务情况说明书（财务报表附注)。 \r\n'),
                        TextSpan(text: '②其他条件：财务要求中①中内容投标时不作要求，为系统固定格式。'),
                        TextSpan(
                          text: '(3)业绩要求：\r\n',
                        ),
                        TextSpan(text: '①业绩证明材料,应符合下列条件： \r\n'),
                        TextSpan(text: '中标通知书及其项下材料交货验收证明材料或完工证明或使用合格证； \r\n'),
                        TextSpan(text: '合同协议书及其项下材料交货验收证明材料或完工证明或使用合格证；\r\n'),
                        TextSpan(
                          text:
                              '中标通知书和合同协议书及其项下材料交货验收证明材料或完工证明或使用合格证。 投标人须提供 2013-01-01 至 2018-09-21 完成的类似项目业绩，类似项目业绩应为 投标人承担过单项合同金额870万元及以上直埋（或地埋）蒸汽管业绩（需提供合同及完工证明材料（或交货验收证明材料），金额以合同为准，时间以合同签订时间为准，证明文件扫描上传至电子投标文件中)。\r\n',
                        ),
                        TextSpan(text: ' ②其他条件：不作要求 \r\n'),
                        TextSpan(
                          text: '(4)信誉要求：\r\n',
                        ),
                        TextSpan(text: '①投标人须提供以下承诺：\r\n'),
                        TextSpan(text: ' A、投标文件中的重要内容没有失实或者弄虚作假；\r\n'),
                        TextSpan(
                            text:
                                ' B、投标人未处于被责令停业、投标资格被取消或者财产被 接管、冻结和破产状态；\r\n'),
                        TextSpan(
                            text:
                                'C、投标人没有因骗取中标或者严重违约以及发生重大工程 质量、安全生产事故等问题，被有关部门暂停投标资格并在暂停期内的；\r\n'),
                        TextSpan(text: '②其他条件：不作要求\r\n'),
                        TextSpan(text: '(5)项目负责人资格要求：\r\n'),
                        TextSpan(text: '不需要注册建造师，但符合下列条件：\r\n'),
                        TextSpan(text: '职称： 及以上\r\n'),
                        TextSpan(
                            text:
                                '提供社保机构出具 至 投标人为项目负责人缴纳的养老保险 金缴费清单材料，加盖社保机构公章或社保中心参保缴费证明电子专用章（具有可验证的二维码或验证码)。 其他条件：不作要求\r\n'),
                        TextSpan(text: '(6)信用要求：\r\n'),
                        TextSpan(text: '有要求： \r\n'),
                        TextSpan(
                            text:
                                '①投标人应具备由第三方评估机构出具的南京招投标领域且在有效期内的“投标企业信用报告”，信用等级为级及以上。 \r\n'),
                        TextSpan(text: '②其他条件：不作要求\r\n'),
                        TextSpan(text: '(7)其他要求：\r\n'),
                        TextSpan(
                          text: '①一个制造商对同一品牌同一型号的货物，只能委托一个代理商参加投标。\r\n',
                        ),
                        TextSpan(
                          text: '②其他条件：其他要求①中内容投标时不作要求，为系统固定格式。\r\n',
                        )
                      ],
                    ))),
                Padding(
                  padding: const EdgeInsets.fromLTRB(15, 0, 15, 25),
                  child: Row(
                    children: <Widget>[
                      Text(
                        '3.2 是否接受联合体投标：',
                        style: normalTextStyle,
                      ),
                      Padding(
                        padding: const EdgeInsets.only(left: 10),
                        child: Text(
                          '不接受',
                          style: normalTextStyle,
                        ),
                      )
                    ],
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.fromLTRB(15, 0, 15, 12),
                  child: Text(
                    '4. 招标文件的获取',
                    style: titleTextStyle,
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.fromLTRB(15, 0, 15, 25),
                  child: Text.rich(TextSpan(
                    style: normalTextStyle,
                    children: <TextSpan>[
                      TextSpan(
                          text:
                              '4.1 凡有意参加投标者，自招标公告发布时间起至投标截止 时间止，登录南京市公共资源交易中心货物网上交易平台 （http://hwjy.njzwfw.gov.cn:8081）下载招标文件。\r\n'),
                      TextSpan(text: '4.2 招标文件售价 600 元，售后不退。\r\n')
                    ],
                  )),
                ),
                Padding(
                  padding: const EdgeInsets.fromLTRB(15, 0, 15, 12),
                  child: Text(
                    '5. 投标文件的递交',
                    style: titleTextStyle,
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.fromLTRB(15, 0, 15, 25),
                  child: Text.rich(TextSpan(
                    style: normalTextStyle,
                    children: <TextSpan>[
                      TextSpan(
                          text:
                              '5.1 投标文件递交的截止时间为 2018-10-15 10:30 ，地点为南京市江东中路265号南京市公共资源交易中心一楼1225室。\r\n'),
                      TextSpan(
                          text:
                              '5.2 投标文件递交的截止时间为 2018-10-15 10:30 ，投标人应在截止时间前通过南京市公共资源交易中心货物网上交易平台（http://hwjy.njzwfw.gov.cn:8081）递交电子投标文件。\r\n')
                    ],
                  )),
                ),
                Padding(
                  padding: const EdgeInsets.fromLTRB(15, 0, 15, 12),
                  child: Text(
                    '6. 开标时间及地点',
                    style: titleTextStyle,
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.fromLTRB(15, 0, 15, 25),
                  child: Text.rich(TextSpan(
                    style: normalTextStyle,
                    children: <TextSpan>[
                      TextSpan(text: '开标时间：', children: <TextSpan>[
                        TextSpan(
                          text: '2019年10月24日 14时30分\r\n',
                        )
                      ]),
                      TextSpan(text: '开标地点： ', children: <TextSpan>[
                        TextSpan(
                          text: '常州市关河中路8号常州邮政枢纽大楼905室\r\n',
                        )
                      ]),
                    ],
                  )),
                ),
                Padding(
                  padding: const EdgeInsets.fromLTRB(15, 0, 15, 12),
                  child: Text(
                    '7. 其他事项',
                    style: titleTextStyle,
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.fromLTRB(15, 0, 15, 25),
                  child: Text.rich(TextSpan(
                    style: normalTextStyle,
                    children: <TextSpan>[
                      TextSpan(text: '7.1 有下列行为之一的投标人，招标人不接受其参加投标：\r\n'),
                      TextSpan(text: '(1)有违反法律、法规行为，依法被取消投标资格且期限未 满的。\r\n'),
                      TextSpan(
                          text:
                              '(2)因招投标活动中有违法违规和不良行为，被有关招投标行政监督部门公示且期限未满的。\r\n'),
                      TextSpan(
                          text:
                              '(3)2013-01-01 以来存在出让或者出租资格、资质证书供 他人投标的, 使用通过受让或者租借等方式获取的资格、资 质证书投标的行为的。\r\n'),
                      TextSpan(text: '(4)其他：不作要求\r\n'),
                      TextSpan(
                          text:
                              '7.2 参加本项目投标的投标人，均须先办理JSCA锁，再登录“南京市公共资源交易平台“—”交易系统登录“—”工程货物“参与投标流程。JSCA锁办理请参阅南京市公共资源交易中心“新用户入网业务向导”。\r\n')
                    ],
                  )),
                ),
                Padding(
                  padding: const EdgeInsets.fromLTRB(15, 0, 15, 12),
                  child: Text(
                    '8. 监督部门',
                    style: titleTextStyle,
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.fromLTRB(15, 0, 15, 25),
                  child: Text.rich(TextSpan(
                    style: normalTextStyle,
                    children: <TextSpan>[
                      TextSpan(text: '本招标的监督部门为中国移动通信集团江苏有限公司泰州分公司。 \r\n'),
                    ],
                  )),
                ),
                Padding(
                  padding: const EdgeInsets.fromLTRB(15, 0, 15, 12),
                  child: Text(
                    '9. 发布媒介',
                    style: titleTextStyle,
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.fromLTRB(15, 0, 15, 25),
                  child: Text.rich(TextSpan(
                    style: normalTextStyle,
                    children: <TextSpan>[
                      TextSpan(text: '本次招标公告在下列媒介同时发布： \r\n'),
                      TextSpan(
                        text: '靖江公共资源交易网（http://www.jjggzyw.com）\r\n',
                      ),
                      TextSpan(text: '泰州市建设工程信息网（http://www.tzcetc.com）\r\n'),
                      TextSpan(text: '江苏建设工程招标网（http://www.jszb.com.cn\r\n'),
                    ],
                  )),
                ),
                Padding(
                  padding: const EdgeInsets.fromLTRB(15, 0, 15, 12),
                  child: Text(
                    '10. 联系方式',
                    style: titleTextStyle,
                  ),
                ),
                BullectinContact(),
                Padding(
                  padding: EdgeInsets.only(bottom: 20),
                  child: Container(
                    width: ScreenUtils.screenWidth,
                    height: 6,
                    color: themeBackgroundColor,
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.fromLTRB(15, 0, 15, 20),
                  child: Text(
                    '招标信息',
                    style: titleTextStyle,
                  ),
                ),
                Padding(
                  padding: EdgeInsets.only(bottom: 20),
                  child: Container(
                    width: ScreenUtils.screenWidth,
                    height: themeBorderWidth,
                    color: themeBorderColor,
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.only(bottom: 66),
                  child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: <Widget>[
                        Container(
                          alignment: Alignment.center,
                          // color: Colors.green,
                          width: 93,
                          height: 46,
                          child: Column(
                            children: <Widget>[
                              Image.asset('assets/images/tender.png'),
                              Padding(
                                padding: const EdgeInsets.only(top: 5),
                                child: Text(
                                  '招标人',
                                  style: TextStyle(
                                    color: themeTextColor,
                                    fontSize: 12,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                        Container(
                          width: themeBorderWidth,
                          height: 26,
                          color: themeBorderColor,
                        ),
                        Container(
                          alignment: Alignment.center,
                          // color: Colors.green,
                          width: 93,
                          height: 46,
                          child: Column(
                            children: <Widget>[
                              Image.asset('assets/images/tenderagency.png'),
                              Padding(
                                padding: const EdgeInsets.only(top: 5),
                                child: Text(
                                  '代理机构',
                                  style: TextStyle(
                                    color: themeTextColor,
                                    fontSize: 12,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                        Container(
                          width: themeBorderWidth,
                          height: 26,
                          color: themeBorderColor,
                        ),
                        Container(
                          alignment: Alignment.center,
                          // color: Colors.green,
                          width: 93,
                          height: 46,
                          child: Column(
                            children: <Widget>[
                              Image.asset('assets/images/tenderdoc.png'),
                              Padding(
                                padding: const EdgeInsets.only(top: 5),
                                child: Text(
                                  '招标文件预览',
                                  style: TextStyle(
                                    color: themeTextColor,
                                    fontSize: 12,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                        Container(
                          width: themeBorderWidth,
                          height: 26,
                          color: themeBorderColor,
                        ),
                        Container(
                          alignment: Alignment.center,
                          // color: Colors.green,
                          width: 93,
                          height: 46,
                          child: Column(
                            children: <Widget>[
                              Image.asset('assets/images/datareport.png'),
                              Padding(
                                padding: const EdgeInsets.only(top: 5),
                                child: Text(
                                  '数据报告',
                                  style: TextStyle(
                                    color: themeTextColor,
                                    fontSize: 12,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ]),
                ),
              ],
            ),
            Align(
              alignment: Alignment.bottomCenter,
              child: BulletinBottomBtn(),
            )
          ],
        ),
      ),
    );
  }
}
