package com.hzw.sunflower.controller.request;

import com.hzw.sunflower.entity.EmailInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @ClassName:EmailInforReq
 * @Auther: lijinxin
 * @Description:
 * @Date: 2023/3/2 18:41
 * @Version: v1.0
 */
@ApiModel(description = "邮件信息请求实体")
@Data
public class EmailInfoReq {


    @ApiModelProperty("项目id")
    private Long projectId;


    @ApiModelProperty("轮次")
    private Integer bidRound;

    @ApiModelProperty("业务类型")
    private Integer bussinessType;

    @ApiModelProperty("业务id")
    private Long bussinessId;

    @ApiModelProperty("邮件信息集合")
    private List<EmailInfo> infoList;
}
