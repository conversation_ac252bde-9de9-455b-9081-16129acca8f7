package com.hzw.depositfee.api.config;

import com.hzw.depositfee.api.Interceptor.TokenInterceptor;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import javax.annotation.Resource;

/**
 * 自定义拦截规则 表单不可以重复提交
 *
 * <AUTHOR>
 * @Date 2021年04月23日 14:20
 * @Version 1.0
 */
@Configuration
public class ResourcesConfig implements WebMvcConfigurer {

    @Resource
    private TokenInterceptor tokenInterceptor;


    /**
     * 自定义拦截规则
     */
    @Override
    public void addInterceptors(InterceptorRegistry registry) {

        registry.addInterceptor(tokenInterceptor).addPathPatterns("/**").excludePathPatterns(
                "/**/doc.html",
                "/**/webjars/bycdao-ui/images/api.ico",
                "/**/swagger-resources",
                "/**/swagger-resources/**",
                "/**/v2/**",
                "/*.html",
                "/**/*.html",
                "/**/*.css",
                "/**/*.js",
                "/**/*.woff",
                "/**/*.ttf",
                "/**/*.gif",
                "/**/error",
                "/**/error*",
                "/**/login"

        );
    }



}
