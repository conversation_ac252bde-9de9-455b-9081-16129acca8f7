<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper 
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hzw.ssm.sys.system.dao.MenuMapper">

	<!-- 如果需要查询缓存，将cache的注释去掉即可
	<cache />
	-->

	<!-- 查询子菜单 -->
	<select id="querySubMenuByMenuId" parameterType="MenuEntity"
		resultType="MenuEntity">
		SELECT
		MENU_ID,MENU_NAME,PARENT_MENU_ID,MENU_URL,menu_image_url from
		TS_MENU
		where rownum = 1 and DELETE_FLAG=0 and
		PARENT_MENU_ID=#{menu_id}
	</select>

	<!-- 加载树形菜单 -->
	<select id="queryMenuList" resultType="MenuEntity">
		select
		MENU_ID,MENU_NAME,PARENT_MENU_ID,MENU_URL from TS_MENU
		where
		DELETE_FLAG=0 order
		by SORTNO desc
	</select>

	<!-- 根据id获取一条菜单信息 -->
	<select id="getMenuById" parameterType="MenuEntity" resultType="MenuEntity">
		select
		MENU_ID,MENU_NAME,PARENT_MENU_ID,MENU_URL,menu_image_url from
		TS_MENU
		where
		DELETE_FLAG=0 and rownum=1
		<if test="menu_id != null">
			and menu_id = #{menu_id}
		</if>
	</select>

	<!-- 根据菜单名称获取菜单信息 -->
	<select id="getMenuByName" parameterType="MenuEntity"
		resultType="MenuEntity">
		select
		MENU_ID,MENU_NAME,PARENT_MENU_ID,MENU_URL,menu_image_url from
		TS_MENU
		where
		DELETE_FLAG=0 and MENU_NAME=#{menu_name}
	</select>

	<!-- 根据父菜单id获取信息 -->
	<select id="getMenuByParentId" parameterType="MenuEntity"
		resultType="MenuEntity">
		select
		MENU_ID,MENU_NAME,PARENT_MENU_ID,MENU_URL,menu_image_url from
		TS_MENU
		where
		DELETE_FLAG=0 and rownum=1
		and menu_id = #{parent_menu_id}
	</select>

	<insert id="saveMenu" parameterType="MenuEntity">
		INSERT INTO TS_MENU(
		MENU_ID,
		MENU_NAME,
		MENU_URL,
		PARENT_MENU_ID,
		menu_image_url,
		CREATE_TIME,
		DELETE_FLAG
		) VALUES
		(#{menu_id},#{menu_name},#{menu_url},#{parent_menu_id},#{menu_image_url},sysdate,0)
	</insert>

	<!-- 修改菜单信息 -->
	<update id="modifyMenu" parameterType="MenuEntity">
		update TS_MENU set
		MENU_NAME=#{menu_name},MENU_URL=#{menu_url},PARENT_MENU_ID=#{parent_menu_id},menu_image_url=#{menu_image_url}
		where
		MENU_ID=#{menu_id}
	</update>

	<!-- 根据id删除菜单信息 -->
	<update id="deleteMenuById" parameterType="MenuEntity">
		update TS_MENU set
		DELETE_FLAG = 1 where menu_id = #{menu_id}
	</update>
</mapper>