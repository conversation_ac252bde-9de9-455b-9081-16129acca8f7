package com.hzw.sunflower.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzw.sunflower.constant.constantenum.LicenseFunctionEnum;
import com.hzw.sunflower.controller.request.ColumnRequiredReq;
import com.hzw.sunflower.controller.response.ColumnRequiredVO;
import com.hzw.sunflower.dao.ColumnRequiredMapper;
import com.hzw.sunflower.entity.ColumnRequired;
import com.hzw.sunflower.service.ColumnRequiredService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class ColumnRequiredServiceImpl extends ServiceImpl<ColumnRequiredMapper, ColumnRequired> implements ColumnRequiredService {

    /**
     * 回显
     * @param req
     * @return
     */
    @Override
    public List<ColumnRequiredVO> queryColumnRequired(ColumnRequiredReq req) {
        LambdaQueryWrapper<ColumnRequired> lambdaQueryWrapper = new LambdaQueryWrapper();
        //  根据版本判断哪些字段关联模块
        if(req.getLicenseFunction() != null && !"".equals(req.getLicenseFunction())){
            if(req.getLicenseFunction().equals(LicenseFunctionEnum.FUNCTION_BASIC.getType())){
                lambdaQueryWrapper.notIn(ColumnRequired::getRelationModule,1,2,3).or().isNull(ColumnRequired::getRelationModule);
            }else if(req.getLicenseFunction().equals(LicenseFunctionEnum.FUNCTION_PROFESSIONAL.getType())){
                lambdaQueryWrapper.notIn(ColumnRequired::getRelationModule,2,3).or().isNull(ColumnRequired::getRelationModule);
            }
        }
        lambdaQueryWrapper.eq(ColumnRequired::getIsDelete,0);
        List<ColumnRequired> columnRequiredList = this.list(lambdaQueryWrapper);

        // 按模块名/code分类
        List<ColumnRequired> columnRequireds1 = this.baseMapper.queryModuleName();
        List<ColumnRequiredVO> columnRequiredVOList = new ArrayList<>();
        for (ColumnRequired columnRequired1 : columnRequireds1) {
            ColumnRequiredVO columnRequiredVO = new ColumnRequiredVO();
            List<ColumnRequired> columnRequireds = new ArrayList<>();
            for (ColumnRequired columnRequired : columnRequiredList) {
                if(columnRequired.getModuleCode().equals(columnRequired1.getModuleCode())){
                    columnRequireds.add(columnRequired);
                }
            }
            // 模块名/code
            columnRequiredVO.setModuleName(columnRequired1.getModuleName());
            columnRequiredVO.setModuleCode(columnRequired1.getModuleCode());
            columnRequiredVO.setColumnRequiredList(columnRequireds);
            columnRequiredVOList.add(columnRequiredVO);
        }
        return columnRequiredVOList;
    }

    /**
     * 保存
     * @param columnRequiredVOList
     * @return
     */
    @Override
    public Boolean insertColumnRequired(List<ColumnRequiredVO> columnRequiredVOList) {
//        LambdaQueryWrapper<ColumnRequired> lambdaQueryWrapper = new LambdaQueryWrapper<>();
//        this.baseMapper.delete(lambdaQueryWrapper);
        List<ColumnRequired> columnRequiredList = new ArrayList<>();
        for (ColumnRequiredVO columnRequiredVO : columnRequiredVOList) {
            for (ColumnRequired columnRequired : columnRequiredVO.getColumnRequiredList()) {
                columnRequiredList.add(columnRequired);
            }
        }
        Boolean flag = this.updateBatchById(columnRequiredList);
        return flag;
    }

    /**
     * 查询功能模块
     * @param req
     * @return
     */
    @Override
    public List<ColumnRequired> queryModuleName(ColumnRequiredReq req) {
        List<ColumnRequired> columnRequireds= this.baseMapper.queryModuleName();
        return columnRequireds;
    }

    /**
     * 根据模块展示字段名
     * @param req
     * @return
     */
    @Override
    public List<ColumnRequired> queryColumnNameByModuleName(ColumnRequiredReq req) {
        LambdaQueryWrapper<ColumnRequired> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if(req.getModuleName() != null && !"".equals(req.getModuleName())){
            lambdaQueryWrapper.eq(ColumnRequired::getModuleName,req.getModuleName());
        }
        lambdaQueryWrapper.eq(ColumnRequired::getIsDelete,0);
        //  根据版本判断哪些字段关联模块
        if(req.getLicenseFunction() != null && !"".equals(req.getLicenseFunction())){
            if(req.getLicenseFunction().equals(1)){
                lambdaQueryWrapper.notIn(ColumnRequired::getRelationModule,1,2,3).or().isNull(ColumnRequired::getRelationModule);
            }else if(req.getLicenseFunction().equals(2)){
                lambdaQueryWrapper.notIn(ColumnRequired::getRelationModule,2,3).or().isNull(ColumnRequired::getRelationModule);
            }
        }

        // 判断身份
//        if(req.getIdentity().equals(CompanyEnum.BIDDER.getType())){
//            lambdaQueryWrapper.eq(ColumnRequired::getIsRequiredTender, CommonConstants.YES);
//        }else if(req.getIdentity().equals(CompanyEnum.AGENCY.getType())){
//            lambdaQueryWrapper.eq(ColumnRequired::getIsRequiredAgent, CommonConstants.YES);
//        }else {
//            lambdaQueryWrapper.eq(ColumnRequired::getIsRequiredSupplier, CommonConstants.YES);
//        }
        List<ColumnRequired> columnRequiredList = this.list(lambdaQueryWrapper);
        return columnRequiredList;
    }

    /**
     * 根据模块，字段名是否展示
     * @param req
     * @return
     */
    @Override
    public ColumnRequired queryByColumnNameModuleName(ColumnRequiredReq req) {
        LambdaQueryWrapper<ColumnRequired> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ColumnRequired::getModuleName,req.getModuleName());
        lambdaQueryWrapper.eq(ColumnRequired::getColumnName,req.getColumnName());
        lambdaQueryWrapper.eq(ColumnRequired::getIsDelete,0);
        ColumnRequired columnRequired = this.baseMapper.selectOne(lambdaQueryWrapper);
        return columnRequired;
    }

}
