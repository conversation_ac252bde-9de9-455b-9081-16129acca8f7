package com.hzw.sunflower.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hzw.sunflower.dto.ProjectSectionScheduleDto;
import com.hzw.sunflower.entity.ProjectSectionSchedule;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface ProjectSectionScheduleMapper extends BaseMapper<ProjectSectionSchedule> {

    /**
     * 根据招标文件id获取开标一览表信息
     * @param docId
     * @return
     */
    List<ProjectSectionScheduleDto> getInfoByDocId(@Param("docId") Long docId, @Param("bidRound") Integer bidRound);

    /**
     * 根据标段获取开标一览表文件信息
     * @param sectionId
     * @return
     */
    ProjectSectionScheduleDto getInfoBySectionId(Long sectionId);
}
