package com.hzw.sunflower.service;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.controller.request.BidDocDeviceReq;
import com.hzw.sunflower.controller.responese.BidDocDeviceVo;
import com.hzw.sunflower.entity.BidDocDevice;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * <p>
 * 设备需求 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-15
 */
public interface BidDocDeviceService extends IService<BidDocDevice> {

    /**
     * 提交设备需求
     * @param reqList
     * @return
     */
    Result<Boolean> submitDevice(List<BidDocDeviceReq> reqList);

    /**
     * 查询设备需求
     * @param req
     * @return
     */
    Result<List<BidDocDeviceVo>> queryDevice(BidDocDeviceReq req);

    /**
     * 设备需求模板导出
     * @param response
     * @param request
     */
    void exportDeviceTemplate(HttpServletResponse response, HttpServletRequest request) throws IOException;

    /**
     * 设备需求导入
     * @param ossId
     * @return
     */
    Result<List<BidDocDeviceVo>> importDevice(Long ossId);

    /**
     * 设备需求插入
     * @param docId
     * @param path
     */
    void insertDeviceForWord(Long docId, String path);
}
