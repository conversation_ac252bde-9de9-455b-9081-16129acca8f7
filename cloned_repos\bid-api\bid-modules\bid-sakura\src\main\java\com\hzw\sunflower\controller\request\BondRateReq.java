package com.hzw.sunflower.controller.request;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2022/7/29 11:10
 * @description：
 * @modified By：`
 * @version: 1.0
 */
@ApiModel(description = "今日利率")
@Data
public class BondRateReq implements Serializable {

    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("利率")
    private BigDecimal rate;

    @ApiModelProperty("利率")
    private List<BondRateBefore> bondRateBefore;

    @ApiModelProperty("生效日期")
    private String effectDate;

    @ApiModelProperty("类型：bond")
    private String type;

}
