package com.hzw.sunflower.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzw.sunflower.dao.SealManagementOperateLogMapper;
import com.hzw.sunflower.dto.SealManagementOperateLogDTO;
import com.hzw.sunflower.entity.SealManagementOperateLog;
import com.hzw.sunflower.entity.condition.SealManagementOperateLogCondition;
import com.hzw.sunflower.exception.ExceptionEnum;
import com.hzw.sunflower.exception.SunFlowerException;
import com.hzw.sunflower.service.SealManagementOperateLogService;
import org.springframework.stereotype.Service;

import java.util.List;


/**
* SealManagementOperateLogService接口实现
*
* <AUTHOR>
* @version 1.0.0 2021-04-07
*/
@Service
public class SealManagementOperateLogServiceImpl extends ServiceImpl<SealManagementOperateLogMapper, SealManagementOperateLog> implements SealManagementOperateLogService {

    /**
    * 根据条件分页查询列表
    *
    * @param condition 查询条件
    * @return 分页数据
    */
    @Override
    public IPage<SealManagementOperateLog> findInfoByCondition(SealManagementOperateLogCondition condition) {
        IPage<SealManagementOperateLog> page = condition.buildPage();
        QueryWrapper<SealManagementOperateLog> queryWrapper = condition.buildQueryWrapper(SealManagementOperateLog.class);
        return this.page(page, queryWrapper);
    }

    /**
    * 根据主键ID查询
    *
    * @param id 主键ID
    * @return
    */
    @Override
    public SealManagementOperateLog getInfoById(Long id) {
        return this.getById(id);
    }

    /**
    * 新增
    *
    * @param sealManagementOperateLog
    * @return 是否成功
    */
    @Override
    public Boolean addInfo(SealManagementOperateLogDTO sealManagementOperateLog) {
        return this.save(sealManagementOperateLog);
    }

    /**
    * 修改
    *
    * @param sealManagementOperateLog
    * @return 是否成功
    */
    @Override
    public Boolean updateInfo(SealManagementOperateLogDTO sealManagementOperateLog) {
        return this.updateById(sealManagementOperateLog);
    }

    /**
    * 根据主键ID删除
    *
    * @param id 主键ID
    * @return 是否成功
    */
    @Override
    public Boolean deleteById(Long id) {
        return this.removeById(id);
    }

    /**
    * 根据主键ID列表批量删除
    *
    * @param idList 主键ID列表
    * @return 是否成功
    */
    @Override
    public Boolean deleteByIds(List<Long> idList) {
        return this.removeByIds(idList);
    }

}