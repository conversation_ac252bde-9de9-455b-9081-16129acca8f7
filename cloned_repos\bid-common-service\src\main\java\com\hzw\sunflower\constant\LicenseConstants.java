package com.hzw.sunflower.constant;

/**
 * <AUTHOR>
 * @datetime 2023/02/17 16:09
 * @description: license常量
 * @version: 1.0
 */
public class LicenseConstants {

    /*** license版本 ***/
    public static final String VERSION_OFFICIAL = "正式版";
    public static final String VERSION_TRIAL = "试用版";

    public static final String FUNCTION_BASE = "基础版";
    public static final String FUNCTION_PROFESSIONAL = "专业版";
    public static final String FUNCTION_Edition = "旗舰版";

    public static final String CONFIRM_INFO_1 = "激活码信息不匹配，请核实！";
    public static final String CONFIRM_INFO_2 = "当前输入的激活码无效或已过期，请输入正确的激活码！";
    public static final String CONFIRM_INFO_3 = "您已激活，无需重复激活！";
    public static final String CONFIRM_INFO_4 = "您已激活正式版系统使用权限，是否切换为体验版？";
    public static final String CONFIRM_INFO_5 = "您当前进行激活的系统功能版本低于已激活的系统功能版本，是否切换？";


    /**1.判断当前激活码中的机器指纹与服务器的机器指纹是否一致，若不一致则提示：“激活码信息不匹配，请核实”，不允许激活*/
    public static final Integer CONFIRM_CODE_1 = 1;
    /**2.验证License信息中有效期是否有效，即有效期结束时间应当大于当前时间，不允许激活*/
    public static final Integer CONFIRM_CODE_2 = 2;
    /**3.如果当前输入的License信息与已激活的License信息一致，则提示：“您已激活，无需重复激活”*/
    public static final Integer CONFIRM_CODE_3 = 3;
    /**4.如果当前输入的License信息系统版本低于已激活License信息中的系统版本，即已激活正式版，再次激活体验版的情况，进行系统提示，点击确认则进行激活，点击取消则不进行激活*/
    public static final Integer CONFIRM_CODE_4 = 4;
    /**5.如果当前输入的License信息中功能版本低于已激活License信息中的功能版本，即已激活旗舰版再次激活专业版，或已激活旗舰版再次激活基础版等情况，进行系统提示,点击确认则进行激活，点击取消则不进行激活*/
    public static final Integer CONFIRM_CODE_5 = 5;

    /**通过一切校验，无需弹出确认提示*/
    public static final Integer CONFIRM_CODE_6 = 6;


    /*** license过期提示天数 ***/
    public static final Integer TOAST_DAY_LIMIT = 15;

    /*** license过期三十天提示天数 ***/
    public static final Integer TOAST_DAY_SHOW = 30;

}
