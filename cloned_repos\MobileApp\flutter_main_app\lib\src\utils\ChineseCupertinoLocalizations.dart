///***
/// * 国际化
/// *
/// * 实际效果是使得 CupertinoDatePicker 显示中国地区的时间格式
/// */

import 'dart:async';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';

class _CupertinoLocalizationsDelegate extends LocalizationsDelegate<CupertinoLocalizations> {
  const _CupertinoLocalizationsDelegate();

  @override
  bool isSupported(Locale locale) => locale.languageCode == 'zh';

  @override
  Future<CupertinoLocalizations> load(Locale locale) => ChineseCupertinoLocalizations.load(locale);

  @override
  bool shouldReload(_CupertinoLocalizationsDelegate old) => false;

  @override
  String toString() => 'DefaultCupertinoLocalizations.delegate(zh_CN)';
}

/// * US English strings for the cupertino widgets.
class ChineseCupertinoLocalizations extends CupertinoLocalizations {
  ///***
  /// * Constructs an object that defines the cupertino widgets' localized strings for US English (only).
  /// *
  /// * [LocalizationsDelegate] implementations typically call the static [load] function,
  /// * rather than constructing this class directly.
  /// */
  ChineseCupertinoLocalizations();

  static const List<String> _shortWeekdays = <String> [
    '周一',
    '周二',
    '周三',
    '周四',
    '周五',
    '周六',
    '周日',
  ];

  static const List<String> _shortMonths = <String>[
    '1',
    '2',
    '3',
    '4',
    '5',
    '6',
    '7',
    '8',
    '9',
    '10',
    '11',
    '12',
  ];

  static const List<String> _months = <String>[
    '1',
    '2',
    '3',
    '4',
    '5',
    '6',
    '7',
    '8',
    '9',
    '10',
    '11',
    '12',
  ];

  @override
  String datePickerYear(int yearIndex) => yearIndex.toString() + '年';

  @override
  String datePickerMonth(int monthIndex) => _months[monthIndex - 1] + '月';

  @override
  String datePickerDayOfMonth(int dayIndex) => dayIndex.toString() + '日';

  @override
  String datePickerMediumDate(DateTime date) {
    return '${_shortWeekdays[date.weekday - DateTime.monday]} '
      '${_shortMonths[date.month - DateTime.january].padLeft(2, "0")}-'
      '${date.day.toString().padRight(2)}';
  }

  @override
  String datePickerHour(int hour) => hour.toString();

  @override
  String datePickerHourSemanticsLabel(int hour) => hour.toString() + '时';

  @override
  String datePickerMinute(int minute) => minute.toString().padLeft(2, '0');

  @override
  String datePickerMinuteSemanticsLabel(int minute) => minute.toString() + '分';

  @override
  DatePickerDateOrder get datePickerDateOrder => DatePickerDateOrder.ymd;

  @override
  DatePickerDateTimeOrder get datePickerDateTimeOrder => DatePickerDateTimeOrder.date_time_dayPeriod;

  @override
  String get anteMeridiemAbbreviation => '上午';

  @override
  String get postMeridiemAbbreviation => '下午';

  @override
  String get todayLabel => '今天';

  @override
  String get alertDialogLabel => 'Info';

  @override
  String timerPickerHour(int hour) => hour.toString();

  @override
  String timerPickerMinute(int minute) => minute.toString();

  @override
  String timerPickerSecond(int second) => second.toString();

  @override
  String timerPickerHourLabel(int hour) => '时';

  @override
  String timerPickerMinuteLabel(int minute) => '分';

  @override
  String timerPickerSecondLabel(int second) => '秒';

  @override
  String get cutButtonLabel => '剪切';

  @override
  String get copyButtonLabel => '复制';

  @override
  String get pasteButtonLabel => '粘贴';

  @override
  String get selectAllButtonLabel => '全选';

  ///***
  /// * Creates an object that provides US English resource values for the cupertino library widgets.
  /// *
  /// * The [locale] parameter is ignored.
  /// *
  /// * This method is typically used to create a [LocalizationsDelegate].
  /// */
  static Future<CupertinoLocalizations> load(Locale locale) {
    return SynchronousFuture<CupertinoLocalizations>(ChineseCupertinoLocalizations());
  }

  ///***
  /// * A [LocalizationsDelegate] that uses [DefaultCupertinoLocalizations.load]
  /// * to create an instance of this class.
  /// */
  static const LocalizationsDelegate<CupertinoLocalizations> delegate = _CupertinoLocalizationsDelegate();

}
