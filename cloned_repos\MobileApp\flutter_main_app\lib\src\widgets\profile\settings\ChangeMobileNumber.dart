///***
/// * 修改手机号码
/// */

import 'package:flutter/material.dart';

import 'package:flutter_main_app/src/constants/theme.dart';
import 'package:flutter_main_app/src/utils/ScreenUtils.dart';

import 'package:flutter_main_app/src/components/CustomAppBar.dart';
import 'package:flutter_main_app/src/components/CustomSafeArea.dart';
import 'package:flutter_main_app/src/components/CustomSubmitButton.dart';

class ChangeMobileNumber extends StatefulWidget {
  const ChangeMobileNumber({Key key}) : super(key: key);

  /// * 路由名称
  static const String _routeName = 'ChangeMobileNumber';
  static String get routeName => _routeName;

  @override
  _ChangeMobileNumberState createState() => _ChangeMobileNumberState();
}

class _ChangeMobileNumberState extends State<ChangeMobileNumber> {

  int step = 1;

  String oldMobile = '18651734706';
  String oldCode = '';

  String newMobile = '';
  String newCode = '';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: '修改手机',
      ),
      body: CustomSafeArea(
        child: SingleChildScrollView(
          physics: BouncingScrollPhysics(),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              /// * 步骤
              Container(
                width: ScreenUtils.screenWidth,
                height: 60.0,
                child: Row(
                  mainAxisSize: MainAxisSize.max,
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Expanded(
                      child: this._buildStep(
                        thisStep: 1,
                        label: '验证原号码',
                      ),
                    ),
                    Expanded(
                      child: this._buildStep(
                        thisStep: 2,
                        label: '绑定新手机',
                      ),
                    ),
                  ],
                ),
              ),
              /// * 手机号码验证
              this.step == 1
                ? this._buildMobileNumberValidator()
                : this._buildMobileNumberValidator(),
              /// * 按钮 - 下一步 | 提交
              Padding(
                padding: const EdgeInsets.only(top: 30.0),
                child: this.step == 1
                  ? CustomSubmitButton(
                      text: '下一步',
                      onTap: () {
                        setState(() {
                          this.step++;
                        });
                      },
                    )
                  : CustomSubmitButton(
                      text: '提交',
                      onTap: () {},
                    ),
              ),
              /// * 提示
              Container(
                padding: const EdgeInsets.only(
                  left: 22.0,
                  top: 34.0,
                  right: 20.0,
                ),
                width: ScreenUtils.screenWidth,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: <Widget>[
                        Padding(
                          padding: const EdgeInsets.only(
                            top: 2.0,
                            right: 2.0,
                          ),
                          child: Icon(
                            Icons.info,
                            size: themeFontSize,
                            color: themeHintTextColor,
                          ),
                        ),
                        Text(
                          '温馨提示：',
                          style: TextStyle(
                            height: 2.0,
                            fontSize: 10.0,
                            color: themeHintTextColor,
                          ),
                        ),
                      ],
                    ),
                    Text(
                      '手机号码修改成功后需要使用新的手机号码进行登录。',
                      style: TextStyle(
                        height: 2.0,
                        fontSize: 10.0,
                        color: themeHintTextColor,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  ///***
  /// * 生成步骤 UI
  /// *
  /// * @param {int} thisStep
  /// * @param {String} label
  /// *
  /// * @return {Widget}
  /// */
  Widget _buildStep({
    int thisStep,
    String label,
  }) {

    final bool isActive = thisStep == this.step;

    final Widget step = Center(
      child: Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: <Widget>[
          Container(
            margin: const EdgeInsets.only(
              right: 7.0,
            ),
            width: 16.0,
            height: 16.0,
            decoration: BoxDecoration(
              color: isActive ? themeActiveColor : themeHintTextColor,
              borderRadius: BorderRadius.circular(8.0),
            ),
            child: Center(
              child: Text(
                '$thisStep',
                style: TextStyle(
                  fontSize: 10.0,
                  color: Colors.white,
                ),
              ),
            ),
          ),
          Text(
            label,
            style: TextStyle(
              fontSize: 14.0,
              color: isActive ? themeActiveColor : themeHintTextColor,
            ),
          ),
        ],
      ),
    );

    return step;
  }

  ///***
  /// * 生成手机号码验证表单 UI
  /// *
  /// *
  /// *
  /// * @return {Widget}
  /// */
  Widget _buildMobileNumberValidator() {
    Widget validator = Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        /// * 手机号码 & 获取验证码
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 20.0),
          width: ScreenUtils.screenWidth,
          height: 51.0,
          decoration: BoxDecoration(
            color: themeContentBackgroundColor,
            border: Border(
              bottom: BorderSide(
                width: themeBorderWidth,
                color: themeBorderColor,
              ),
            ),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              /// * 手机号码
              Expanded(
                child: TextField(
                  style: themeTextStyle,
                  decoration: InputDecoration(
                    hintText: '请输入手机号码',
                    hintStyle: themeHintTextStyle,
                    border: InputBorder.none,
                  ),
                ),
              ),
              /// * 获取验证码
              SizedBox(
                width: 75.0,
                height: 50.0,
                child: Text(
                  '获取验证码',
                  textAlign: TextAlign.right,
                  style: TextStyle(
                    height: 2.9,
                    fontSize: 12.0,
                    color: themeActiveColor,
                  ),
                ),
              ),
            ],
          ),
        ),
        /// * 验证码
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 20.0),
          width: ScreenUtils.screenWidth,
          height: 50.0,
          decoration: BoxDecoration(
            color: themeContentBackgroundColor,
            border: Border(
              bottom: BorderSide(
                width: themeBorderWidth,
                color: themeBorderColor,
              ),
            ),
          ),
          child: TextField(
            style: themeTextStyle,
            decoration: InputDecoration(
              hintText: '请输入验证码',
              hintStyle: themeHintTextStyle,
              border: InputBorder.none,
            ),
          ),
        ),
      ],
    );

    return validator;
  }

}
