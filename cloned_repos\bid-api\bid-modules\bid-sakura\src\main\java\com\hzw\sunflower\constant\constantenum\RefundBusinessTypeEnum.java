package com.hzw.sunflower.constant.constantenum;

/**
 * <AUTHOR>
 * @date ：Created in 2022/07/27 9:23
 * @description：申请类型
 * @version: 1.0
 */
public enum RefundBusinessTypeEnum {
    //1:异常付款失败  2:财务审批  3:保证金流水退还列表
    ABNORMAL_LIST(1, "异常付款失败"),
    EXAMINE_LIST(2, "财务审批"),
    REFUND_LIST(3, "保证金流水退还列表");

    private Integer type;
    private String desc;

    RefundBusinessTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
