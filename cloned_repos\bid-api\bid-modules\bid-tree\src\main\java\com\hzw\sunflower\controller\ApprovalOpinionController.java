package com.hzw.sunflower.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hzw.sunflower.common.BaseController;
import com.hzw.sunflower.common.Paging;
import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.common.annotation.RepeatSubmit;
import com.hzw.sunflower.constant.FormatConstants;
import com.hzw.sunflower.constant.MessageConstants;
import com.hzw.sunflower.constant.NoticeProgressConstants;
import com.hzw.sunflower.constant.TableNameConstants;
import com.hzw.sunflower.controller.request.ApprovalOpinionREQ;
import com.hzw.sunflower.controller.request.CommonNoticeREQ;
import com.hzw.sunflower.controller.request.NoticeAuditReq;
import com.hzw.sunflower.controller.response.ApprovalOpinionVO;
import com.hzw.sunflower.controller.response.ProjectBidNoticeVO;
import com.hzw.sunflower.dto.ApprovalOpinionDTO;
import com.hzw.sunflower.entity.ApprovalOpinion;
import com.hzw.sunflower.entity.ProjectBidNotice;
import com.hzw.sunflower.entity.condition.ApprovalOpinionCondition;
import com.hzw.sunflower.exception.ExceptionEnum;
import com.hzw.sunflower.service.ApprovalOpinionService;
import com.hzw.sunflower.service.ProjectBidNoticeService;
import com.hzw.sunflower.service.RabbitMqService;
import com.hzw.sunflower.util.BeanListUtil;
import com.hzw.sunflower.util.RequestUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;


/**
 * TApprovalOpinionController
 */
@Api(tags = "处理进度服务/招标公告审批服务")
@RestController
@RequestMapping("/approvalOpinion")
public class ApprovalOpinionController extends BaseController {
    @Autowired
    private ApprovalOpinionService approvalOpinionService;
    @Autowired
    private ProjectBidNoticeService noticeService;
//    @Autowired
//    private NoticePackageRService packageRService;
//    @Autowired
//    private RequestToDTOService dtoService;
//    @Autowired
//    private ProjectService projectService;
    @Autowired
    private RabbitMqService rabbitMqService;

    @ApiOperation(value = "根据条件分页查询TApprovalOpinion列表")
    @ApiImplicitParam(name = "tApprovalOpinionREQ", value = "用户表 查询条件", required = true, dataType = "TApprovalOpinionREQ", paramType = "body")
    @PostMapping("/list")
    public Paging<ApprovalOpinion> list(@RequestBody ApprovalOpinionREQ approvalOpinionREQ) {
        ApprovalOpinionCondition condition = BeanListUtil.convert(approvalOpinionREQ, ApprovalOpinionCondition.class);
        IPage<ApprovalOpinion> page = approvalOpinionService.findInfoByCondition(condition);
        return Paging.buildPaging(page);
    }

    @ApiOperation(value = "根据主键ID查询TApprovalOpinion信息")
    @ApiImplicitParam(name = "id", value = "主键ID", required = true)
    @GetMapping(value = "/get/{id}")
    public Result<ApprovalOpinion> get(@PathVariable Long id) {
        if (id == null) {
            return Result.failed(MessageConstants.GET_NULL);
        }
        ApprovalOpinion approvalOpinion = approvalOpinionService.getInfoById(id);
        return Result.ok(approvalOpinion);
    }

    @ApiOperation(value = "根据businessKey查询处理进度信息")
    @ApiImplicitParam(name = "businessKey", value = "招标文件主键ID", required = true)
    @GetMapping(value = "/listByBusinessKey/{businessKey}")
    public Result<List<ApprovalOpinionVO>> getByDocId(@PathVariable String businessKey) {
        if (StringUtils.isEmpty(businessKey)) {
            return Result.failed(MessageConstants.GET_NULL);
        }else {

        }
        String[] arryBuskey=businessKey.split(":");
        String tableName=arryBuskey[0];
        Long busId=Long.parseLong(arryBuskey[1]);
        List<ApprovalOpinionDTO> list = new ArrayList<>();
        if (TableNameConstants.PROJECT_BID_DOC.equals(tableName+":")){
            list= approvalOpinionService.getInfoByDocId(busId);
        }else if(TableNameConstants.PROJECT_BID_NOTICE.equals(tableName)){
            list=approvalOpinionService.getInfoByNoticeId(busId);
        }

        List<ApprovalOpinionVO> info = BeanListUtil.convertList(list, ApprovalOpinionVO.class);
        if (!CollectionUtil.isEmpty(list)) {
            info.stream().forEach(e -> e.setDateString(DateUtil.format(e.getApprovalTime(), FormatConstants.FORMAT_DATE)));
        }
        return Result.ok(info);
    }

    @ApiOperation(value = "根据招标文件ID查询处理进度信息")
    @ApiImplicitParam(name = "docId", value = "招标文件主键ID", required = true)
    @GetMapping(value = "/getByDocId/{docId}")
    public Result<List<ApprovalOpinionVO>> getByDocId(@PathVariable Long docId) {
        if (docId == null) {
            return Result.failed(MessageConstants.GET_NULL);
        }
        List<ApprovalOpinionDTO> dtoList = approvalOpinionService.getInfoByDocId(docId);
        List<ApprovalOpinionVO> info = BeanListUtil.convertList(dtoList, ApprovalOpinionVO.class);
        if (!CollectionUtil.isEmpty(dtoList)) {
            info.stream().forEach(e -> e.setDateString(DateUtil.format(e.getApprovalTime(), FormatConstants.FORMAT_DATE)));
        }
        return Result.ok(info);
    }

    @ApiOperation(value = "招标公告提交审核")
    @ApiImplicitParam(name = "req", value = "公告通用请求实体", required = true, dataType = "CommonNoticeREQ", paramType = "body")
    @PostMapping("/submitNotice")
    @RepeatSubmit
    public Result<Boolean> submitNotice(@RequestBody CommonNoticeREQ req){
        // 公告ID不能为空/0
        if(RequestUtil.isEmpty(req.getNoticeId())){
            return Result.failed(ExceptionEnum.ILLEGAL_PARAMETER_EXCEPTION.getMessage());
        }
       Result<Boolean> result= approvalOpinionService.saveReview(req);
        // 此处代码存在事务回滚问题
        // 招标公告是否可提交
//        TProjectBidNotice notice = noticeService.noticeIsAvailable(req.getNoticeId(), NoticeProgressConstants.CAN_EDIT);
//        // 招标公告关联的包段ID集合
//        List<Long> packageIds = packageRService.findNoticePackageIds(notice.getId());
//        ProjectBidNoticeVO noticeVO = noticeService.findNoticeInfo(new CommonNoticeREQ().setProjectId(notice.getProjectId()).setNoticeId(notice.getId())).getData();
//        // 招标公告变更记录集合
//        List<NoticeChangeRecord> changeRecords = dtoService.toNoticeChangeRecord(noticeVO);
//        // 更新记录
//        approvalOpinionService.insertSubmitNotice(notice,packageIds,changeRecords);
//
//        projectService.saveOrUpdateProjectRedundantFieldDealWith(req.getProjectId());
//        projectService.saveOrUpdateProjectPackageFieldDealWith(req.getProjectId());

        return result;
    }

    @ApiOperation(value = "招标公告审批")
    @ApiImplicitParam(name = "req", value = "审批请求实体", required = true, dataType = "ApprovalOpinionREQ", paramType = "body")
    @PostMapping("/approvalNotice")
    @RepeatSubmit
    public Result<Boolean> approvalNotice(@RequestBody ApprovalOpinionREQ req){
        // 公告ID不能为空/0
        if(RequestUtil.isEmpty(req.getNoticeId())){
            return Result.failed(ExceptionEnum.ILLEGAL_PARAMETER_EXCEPTION.getMessage());
        }
        // 判断招标公告状态是否可用
        ProjectBidNotice notice = noticeService.noticeIsAvailable(req.getNoticeId(), NoticeProgressConstants.ONLY_READ);
        // 进行审批
        approvalOpinionService.insertApprovalNotice(req,notice);

        return Result.ok(true);
    }

    @ApiOperation(value = "招标公告撤回(废弃)")
    @ApiImplicitParam(name = "req", value = "公告通用请求实体", required = true, dataType = "CommonNoticeREQ", paramType = "body")
    @PostMapping("/withdrawNotice")
    @RepeatSubmit
    public Result<Boolean> withdrawNotice(@RequestBody CommonNoticeREQ req){
        // 公告ID不能为空/0
        if(RequestUtil.isEmpty(req.getNoticeId())){
            return Result.failed(ExceptionEnum.ILLEGAL_PARAMETER_EXCEPTION.getMessage());
        }
        // 判断招标公告状态是否可撤回
        ProjectBidNotice notice = noticeService.noticeIsAvailable(req.getNoticeId(), NoticeProgressConstants.CAN_WITHDRAW);
        // 招标公告撤回
        approvalOpinionService.insertWithdrawNotice(notice);
        //发送消息给消息队列 更新项目状态
        //rabbitMqService.sendMq(req.getProjectId());
        return Result.ok(true);
    }

    @ApiOperation(value = "同意招标公告撤回")
    @ApiImplicitParam(name = "req", value = "公告通用请求实体", required = true, dataType = "CommonNoticeREQ", paramType = "body")
    @PostMapping("/confirmWithdrawNotice")
    @RepeatSubmit
    public Result<Boolean> confirmWithdrawNotice(@RequestBody NoticeAuditReq req){
        // 公告ID不能为空/0
        if(RequestUtil.isEmpty(req.getNoticeId())){
            return Result.failed(ExceptionEnum.ILLEGAL_PARAMETER_EXCEPTION.getMessage());
        }
        // 招标公告撤回
        approvalOpinionService.confirmWithdrawNotice(req);

        return Result.ok(true);
    }

    @ApiOperation(value = "查询招标公告审批记录")
    @ApiImplicitParam(name = "req", value = "公告通用请求实体", required = true, dataType = "CommonNoticeREQ", paramType = "body")
    @PostMapping("/noticeApprovalRecords")
    public Result<List<ApprovalOpinionDTO>> noticeApprovalRecords(@RequestBody CommonNoticeREQ req){
        // 公告ID不能为空/0
        if(RequestUtil.isEmpty(req.getNoticeId())){
            return Result.failed(ExceptionEnum.ILLEGAL_PARAMETER_EXCEPTION.getMessage());
        }

        return approvalOpinionService.findNoticeApprovalRecords(req);
    }

    @ApiOperation(value = "查询待审批招标公告的内容")
    @ApiImplicitParam(name = "req", value = "公告通用请求实体", required = true, dataType = "CommonNoticeREQ", paramType = "body")
    @PostMapping("/noticeInfo")
    public Result<ProjectBidNoticeVO> findApprovalingNotice(@RequestBody CommonNoticeREQ req){
        // 公告ID不能为空/0
        if(RequestUtil.isEmpty(req.getNoticeId())){
            return Result.failed(ExceptionEnum.ILLEGAL_PARAMETER_EXCEPTION.getMessage());
        }

        return approvalOpinionService.findApprovalingNotice(req);
    }
}
