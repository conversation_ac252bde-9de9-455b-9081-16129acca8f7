package com.hzw.sunflower.dao;

import com.hzw.sunflower.dto.*;
import com.hzw.sunflower.entity.Project;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2021/7/14 19:05
 * @description：项目相关公共mapper
 * @modified By：`
 * @version: 1.0
 */
public interface ProjectRelevantMapper {
    /**
     * 根据项目id查询项目
     *
     * @param projectId
     */
    ProjectInfoDTO getProjectById(@Param("projectId") Long projectId);

    /**
     * 根据标段id查询标段信息
     *
     * @param sectionId
     * @return
     */
    SectionInfoDTO getSectionById(@Param("sectionId") Long sectionId);

    /**
     * 根据项目id查询项目委托单位信息
     *
     * @param projectId
     * @return
     */
    List<ProjectEntrustInfoDTO> getProjectEntrustById(@Param("projectId") Long projectId);

    /**
     * 查询招标文件信息
     *
     * @param projectId
     * @param sectionId
     * @return
     */
    ProjectBidDocRelationInfoDto getProjectBidDocRelationById(@Param("projectId") Long projectId,@Param("sectionId") Long sectionId);


    /**
     * 根据项目id查询标段
     *
     * @param projectId
     * @return
     */
    List<SectionInfoDTO> getProjectIdBySection(@Param(value = "projectId") Long projectId, @Param(value = "bidRound") Integer bidRound,@Param(value = "clarifyType")Integer clarifyType,@Param(value = "userIdentity")Integer userIdentity);

    /**
     * 修改标段时间
     *
     * @param list
     */
    void updateSectionTime(@Param(value = "list") List<SectionTimeDTO> list);

    /**
     * 修改标段子表时间
     *
     * @param list
     */
    void updateSectionRecordTime(@Param(value = "list") List<SectionTimeDTO> list,@Param("bidRound") Integer bidRound);

    /**
     * 查询标段信息，根据标段id
     *
     * @param sectionIds
     * @return
     */
    List<SectionInfoDTO> getSectionsInfo(@Param(value = "list") List<String> sectionIds);

    /**
     * 根据采购项目编号查询项目信息
     *
     * @param projectNumber
     * @return
     */
    List<Project> selectprojectNumber(@Param(value = "projectNumber") String projectNumber);

    /**
     * 根据项目编号查询标段
     *
     * @param purchaseNumber
     * @return
     */
    List<SectionInfoDTO> getNoticeByPurchaseNumber(@Param("purchaseNumber")String purchaseNumber,@Param("userId")Long userId);

    /**
     * 根据项目id查询资格预审方式 0：按包 1：按项目，和标段信息
     * @param projectId
     * @return
     */
    List<ProjectSectionDTO> getProjectSectionInfoById(@Param(value = "projectId") Long projectId,@Param(value = "purchaseMode") String purchaseMode,@Param(value = "purchaseStatus") String purchaseStatus,@Param(value = "bidRound") Integer bidRound);

    /**
     * 获取项目信息及委托人信息
     * @param projectId
     * @return
     */
    ProjectInfoDTO getProjectUserInfo(@Param(value = "projectId")Long projectId);

    /**
     * 获取资格预审第二轮的标段信息
     * @param subIdList
     * @return
     */
    List<SectionInfoDTO> getSectionsSecondRound(@Param(value = "list") List<Long> subIdList);

    /**
     * 根据项目id、当前租户类型查询项目数据
     * @param projectId
     * @param tenantOrganizationType
     * @return
     */
    ProjectInfoDTO getProjectUserInfoByType(@Param(value = "projectId") Long projectId, @Param(value = "tenantOrganizationType") Integer tenantOrganizationType,
                                            @Param(value = "entrustType") Integer entrustType );

    /**
     * 根据项目id查询项目当前负责人(被委托人)所在处室code
     * @param projectId
     * @return
     */
    String selectDepartCodeByProjectId(@Param("projectId") Long projectId);

}
