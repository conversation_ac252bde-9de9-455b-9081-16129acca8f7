package com.hzw.sunflower.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.constant.CommonConstants;
import com.hzw.sunflower.constant.OpenEnum;
import com.hzw.sunflower.constant.SealEnum;
import com.hzw.sunflower.controller.request.SealApplicationREQ;
import com.hzw.sunflower.dao.SealManagementMapper;
import com.hzw.sunflower.dto.SealManagementDTO;
import com.hzw.sunflower.entity.SealManagement;
import com.hzw.sunflower.entity.SealManagementOperateLog;
import com.hzw.sunflower.entity.condition.SealManagementCondition;
import com.hzw.sunflower.exception.ExceptionEnum;
import com.hzw.sunflower.exception.SunFlowerException;
import com.hzw.sunflower.security.utils.SecurityUtils;
import com.hzw.sunflower.service.SealManagementOperateLogService;
import com.hzw.sunflower.service.SealManagementService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.security.Security;
import java.util.Arrays;
import java.util.Date;
import java.util.List;


/**
* SealManagementService接口实现
*
* <AUTHOR>
* @version 1.0.0 2021-04-07
*/
@Service
public class SealManagementServiceImpl extends ServiceImpl<SealManagementMapper, SealManagement> implements SealManagementService {

    @Autowired
    private SealManagementOperateLogService sealManagementOperateLogService;


    /**
    * 根据条件分页查询列表
    *
    * @param condition 查询条件
    * @return 分页数据
    */
    @Override
    public List<SealManagement> findInfoByCondition(SealManagementCondition condition) {
        //IPage<SealManagement> page = condition.buildPage();
        LambdaQueryWrapper<SealManagement> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.orderByDesc(SealManagement::getUpdatedTime);
        if(null != condition.getSealName() && !"".equals(condition.getSealName())){
            queryWrapper.like(SealManagement::getSealName,condition.getSealName());
        }
        if(null != condition.getSealTypeList()){
            queryWrapper.in(SealManagement::getSealType,condition.getSealTypeList());
        }
        return this.list(queryWrapper);
    }

    /**
    * 根据主键ID查询
    *
    * @param id 主键ID
    * @return
    */
    @Override
    public SealManagement getInfoById(Long id) {
        return this.getById(id);
    }

    /**
    * 新增/修改
    *
    * @param sealManagement
    * @return 是否成功
    */
    @Override
    public Result<Boolean> addInfo(SealManagementDTO sealManagement) {
        // 判断是否重复
        LambdaQueryWrapper<SealManagement> qw = new LambdaQueryWrapper<>();
        qw.eq(SealManagement::getSealName,sealManagement.getSealName());
        if(null != sealManagement.getBelongerId()){
            qw.eq(SealManagement::getBelongerId,sealManagement.getBelongerId());
        }else{
            qw.isNull(SealManagement::getBelongerId);
        }
        if(null != sealManagement.getId()){
            qw.ne(SealManagement::getId,sealManagement.getId());
        }
        long count = this.count(qw);
        if(count > 0){
         return Result.failed(ExceptionEnum.ID_REPEAT.getMessage());
        }

        if(null != sealManagement.getId()){
            SealManagement byId = getById(sealManagement.getId());
            if(!sealManagement.getSealType().equals(byId.getSealType())){
                List<String> projectName = this.baseMapper.selectProjectName(sealManagement.getId());
                if(CollectionUtil.isNotEmpty(projectName)){
                    String join = String.join(",", projectName);
                    return Result.failed(join + "正在申请中，暂时不能修改。");
                }
            }
            LambdaUpdateWrapper<SealManagement> uqw = new LambdaUpdateWrapper<>();
            uqw.set(SealManagement::getBelongerId,sealManagement.getBelongerId());
            uqw.set(SealManagement::getSealName,sealManagement.getSealName());
            uqw.set(SealManagement::getBelonger,sealManagement.getBelonger());
            uqw.set(SealManagement::getSealType,sealManagement.getSealType());
            uqw.set(SealManagement::getBelongerView,sealManagement.getBelongerView());
            uqw.set(SealManagement::getUpdatedTime,new Date());
            uqw.eq(SealManagement::getId,sealManagement.getId());
            boolean b = this.update(uqw);
            // boolean b = this.updateById(sealManagement);
            if(b){
                saveLog(sealManagement.getId(), "修改");
            }
            return Result.okOrFailed(b);

        }else{
            boolean save = this.save(sealManagement);
            if(save){
                saveLog(sealManagement.getId(), "新增");
            }
            return Result.okOrFailed(save);
        }

    }


    /**
     * 启动禁用
     *
     * @param sealManagement
     * @return 是否成功
     */
    @Override
    public Result<Boolean> updateStatus(SealManagementDTO sealManagement) {
        if(CommonConstants.NO2.equals(sealManagement.getStatus())){
            List<String> projectName = this.baseMapper.selectProjectName(sealManagement.getId());
            if(CollectionUtil.isNotEmpty(projectName)){
                String join = String.join(",", projectName);
                return Result.failed(join + "正在申请中，暂时不能停用。");
            }
        }
        SealManagement sm = new SealManagement();
        sm.setId(sealManagement.getId());
        sm.setStatus(sealManagement.getStatus());
        saveLog(sealManagement.getId(), OpenEnum.getValueByKey(sealManagement.getStatus()));
        return Result.okOrFailed(this.updateById(sealManagement));
    }



    private void saveLog(Long sealId, String operate){
        SealManagementOperateLog log = new SealManagementOperateLog();
        log.setSealId(sealId);
        log.setOperate(operate);
        log.setOperateUser(SecurityUtils.getJwtUser().getUserName());
        sealManagementOperateLogService.save(log);
    }


    /**
     * 处理进度
     *
     * @param id
     * @return 是否成功
     */
    @Override
    public List<SealManagementOperateLog> getLog(Long id){
        LambdaQueryWrapper<SealManagementOperateLog> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SealManagementOperateLog::getSealId,id);
        lqw.orderByDesc(SealManagementOperateLog::getCreatedTime);
         return  sealManagementOperateLogService.list(lqw);
    }



    /**
    * 根据主键ID删除
    *
    * @param id 主键ID
    * @return 是否成功
    */
    @Override
    public Boolean deleteById(Long id) {
        return this.removeById(id);
    }

    /**
    * 根据主键ID列表批量删除
    *
    * @param idList 主键ID列表
    * @return 是否成功
    */
    @Override
    public Boolean deleteByIds(List<Long> idList) {
        return this.removeByIds(idList);
    }

    @Override
    public Boolean checkChooseSeal(SealApplicationREQ req) {
        Boolean result = true;
        if(StringUtils.isNotBlank(req.getSealType())){
            String[] split = req.getSealType().split(",");
            if(split.length == 1) {
                if (StringUtils.isNotBlank(req.getChooseSeal())) {
                    String[] seals = req.getChooseSeal().split(",");
                    LambdaQueryWrapper<SealManagement> qw = new LambdaQueryWrapper<>();
                    qw.eq(SealManagement::getSealType, req.getSealType());
                    qw.eq(SealManagement::getStatus, CommonConstants.YES);
                    qw.in(SealManagement::getId, Arrays.asList(seals));
                    long count = this.count(qw);
                    if (count == seals.length){
                        result = true;
                    } else {
                        result = false;
                    }
                }
            } else {
                if (StringUtils.isNotBlank(req.getChooseSeal())) {
                    String[] seals = req.getChooseSeal().split(",");
                    LambdaQueryWrapper<SealManagement> qw = new LambdaQueryWrapper<>();
                    qw.eq(SealManagement::getStatus, CommonConstants.YES);
                    qw.in(SealManagement::getId, Arrays.asList(seals));
                    long count = this.count(qw);
                    if (count == seals.length){
                        result = true;
                    } else {
                        result = false;
                    }
                }
            }
        }
        return result;
    }

}