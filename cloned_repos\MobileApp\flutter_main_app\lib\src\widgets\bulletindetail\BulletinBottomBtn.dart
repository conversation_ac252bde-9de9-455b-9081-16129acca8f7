import "package:flutter/material.dart";

import 'package:flutter_main_app/src/constants/theme.dart';
import "package:flutter_main_app/src/fonts/fonts.dart";

class BulletinBottomBtn extends StatefulWidget {
  @override
  _BulletinBottomBtnState createState() => _BulletinBottomBtnState();
}

class _BulletinBottomBtnState extends State<BulletinBottomBtn> {
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 25),
      child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            InkWell(
                child: Container(
                height: 40,
                width: 153,
                decoration: BoxDecoration(
                  color: themeActiveColor,
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(25),
                    bottomLeft:  Radius.circular(25),
                  )
                ),
                alignment: Alignment.center,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: <Widget>[
                    Padding(
                      padding: const EdgeInsets.only(right: 8),
                      child: Icon(
                        FontsHelper.starBorderIconData,
                        color: Colors.white,
                        size: 18,
                      ),
                    ),
                    Text(
                      '收藏',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                      ),
                    )
                  ],
                ),
              ),
              onTap: null,
            ),
            

            InkWell(
                child: Container(
                height: 40,
                width: 153,
                decoration: BoxDecoration(
                  color: Color(0xFFF59B1F),
                  borderRadius: BorderRadius.only(
                    topRight: Radius.circular(25),
                    bottomRight:  Radius.circular(25),
                  )
                ),
                alignment: Alignment.center,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: <Widget>[
                     Padding(
                      padding: const EdgeInsets.only(right: 8),
                      child: Icon(
                        FontsHelper.reminderIconData,
                        color: Colors.white,
                        size: 18,
                      ),
                    ),
                    Text(
                      '提醒',
                      style: TextStyle(
                        color: Color(0xFFFFFFFF),
                        fontSize: 14,
                      ),
                    )
                  ],
                )
              ),
              onTap: () {
                Navigator.pushNamed(context, 'ReminderSettings');
              },
            )
          ],
        ),
    );
  }
}