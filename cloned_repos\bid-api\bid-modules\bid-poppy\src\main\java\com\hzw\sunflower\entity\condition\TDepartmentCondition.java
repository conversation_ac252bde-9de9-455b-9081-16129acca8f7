package com.hzw.sunflower.entity.condition;

import com.hzw.sunflower.common.BaseCondition;
import io.swagger.annotations.ApiModel;
import lombok.Data;


@ApiModel(description = "TDepartment 查询条件")
@Data
public class TDepartmentCondition extends BaseCondition {

    /**
     * 父类id
     */
    private Long parentId;

    /**
     * 部门名称
     */
    private String departmentName;


    /**
     * 状态
     */
    private Integer status;

}