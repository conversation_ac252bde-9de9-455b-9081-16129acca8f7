<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper 
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hzw.ssm.sys.project.dao.AppointsExpertsMapper">
	<!-- 保存信息(多条) -->
	<insert id="saveInfoList" parameterType="java.util.List">
		insert into T_OFFICIAL_APPOINTS_EXPERTS(OFFICIAL_ID,experts_name,experts_code_id,extract_con,status,delete_flag,create_time,create_user,decimationbatch)
	    <foreach collection="list" item="item" index="index" separator="union all" >  
	       select #{item.officialId},#{item.expertsName},#{item.expertsCodeId},#{item.extractCon},
	         #{item.status},0,#{item.create_time},#{item.create_id},#{item.decimationBatch} from dual  
	    </foreach>  	
	</insert>
	<!-- 根据流水号查询数据信息 -->
	<select id="queryDateByDecimationBatch" parameterType="AppointsExpertsEntity" resultType="AppointsExpertsEntity">
		select P.OFFICIAL_ID AS officialId,
		 P.EXPERTS_NAME AS expertsName,P.EXPERTS_CODE_ID AS expertsCodeId,P.STATUS
		from T_OFFICIAL_APPOINTS_EXPERTS P
		where P.DELETE_FLAG =0 and P.DECIMATIONBATCH=#{decimationBatch}
	</select>
	<!-- 查询数据信息 -->
	<select id="queryList" parameterType="AppointsExpertsEntity" resultType="AppointsExpertsEntity">
		select P.OFFICIAL_ID AS officialId,
		 P.EXPERTS_NAME AS expertsName,P.EXPERTS_CODE_ID AS expertsCodeId,P.STATUS
		from T_OFFICIAL_APPOINTS_EXPERTS P
		where P.DELETE_FLAG =0 and P.OFFICIAL_ID=#{officialId}
	</select>
	
	<!-- 查询最大的修改次数 -->
	<select id="queryMaxCON" parameterType="AppointsExpertsEntity" resultType="AppointsExpertsEntity">
		select 
		 MAX(EXTRACT_CON)
		from T_OFFICIAL_APPOINTS_EXPERTS P
		where P.DELETE_FLAG =0 and P.DECIMATIONBATCH=#{decimationBatch}
	</select> 
	
	<!-- 修改数据状态 -->
	<update id="updateStatus" parameterType="AppointsExpertsEntity">
		UPDATE T_OFFICIAL_APPOINTS_EXPERTS P SET P.STATUS=#{status} WHERE P.OFFICIAL_ID=#{officialId}
	</update>
	
	<!-- 修改数据状态 -->
	<update id="deleteExpert" parameterType="AppointsExpertsEntity">
		UPDATE T_OFFICIAL_APPOINTS_EXPERTS P SET P.DELETE_FLAG=1 WHERE P.OFFICIAL_ID=#{officialId}
	</update>
</mapper>