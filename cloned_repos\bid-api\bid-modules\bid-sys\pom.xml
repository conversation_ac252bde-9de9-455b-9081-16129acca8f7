<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>bid-modules</artifactId>
        <groupId>com.hzw</groupId>
        <version>0.0.4</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>bid-sys</artifactId>
    <dependencies>
        <!--实体映射简单工具-->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.hzw</groupId>
            <artifactId>bid-common-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.hzw</groupId>
            <artifactId>bid-common-service</artifactId>
        </dependency>
        <dependency>
            <groupId>io.swagger</groupId>
            <artifactId>swagger-annotations</artifactId>
        </dependency>
        <dependency>
            <groupId>com.hzw</groupId>
            <artifactId>bid-common-sms</artifactId>
        </dependency>
    </dependencies>
</project>
