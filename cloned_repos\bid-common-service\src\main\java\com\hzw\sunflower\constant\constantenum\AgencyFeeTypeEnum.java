package com.hzw.sunflower.constant.constantenum;

/**
 * 业务操作类型
 *
 * <AUTHOR>
 */
public enum AgencyFeeTypeEnum {
    //代理服务费类型 0不收取 1定额 2比例 3 分批
    NO_MONEY(0, "不收取"),
    QUOTA(1, "定额"),
    PROPORTION(2, "比例"),
    BATCH(3, "分批"),
    OTHER(4, "其他");
    private Integer type;
    private String desc;

    AgencyFeeTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
