package com.hzw.sunflower.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hzw.sunflower.controller.response.ReportListVO;
import com.hzw.sunflower.entity.ArchiveRecallList;
import com.hzw.sunflower.entity.JwtUser;
import com.hzw.sunflower.entity.ReportList;
import com.hzw.sunflower.entity.condition.ArchiveRecallCondition;
import com.hzw.sunflower.entity.condition.ReportListCondition;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

public interface ReportListService extends IService<ReportList> {

    /**
     *
     * @param condition 查询条件
     * @return
     */
    IPage<ReportList> findInfoByCondition(ReportListCondition condition);

    /**
     * 报表中心列表（20） 归档一览表
     * @param condition
     * @param jwtUser
     * @return
     */
    IPage<ReportList> findInfo(ReportListCondition condition, JwtUser jwtUser);

    /**
     * 导出excel
     * @param condition
     * @param jwtUser
     * @return
     */
    List<ReportListVO> findInfoList(ReportListCondition condition, JwtUser jwtUser);


    /**
     * 归档撤回统计
     *  @param condition
     *     @param jwtUser
     *     @return
     */
    IPage<ArchiveRecallList> archiveRecallList(ArchiveRecallCondition condition, JwtUser jwtUser);

    /**
     * 归档撤回统计导出excel
     *  @param condition
     *     @return
     */
    List<ArchiveRecallList> exportArchiveRecallList(ArchiveRecallCondition condition);
}
