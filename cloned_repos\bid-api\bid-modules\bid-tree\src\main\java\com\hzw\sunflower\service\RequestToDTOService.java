package com.hzw.sunflower.service;

import com.hzw.sunflower.controller.request.UpdateNoticeREQ;
import com.hzw.sunflower.controller.response.ProjectBidNoticeVO;
import com.hzw.sunflower.dto.UpdateNoticeDTO;
import com.hzw.sunflower.entity.NoticeChangeRecord;

import java.util.List;

public interface RequestToDTOService {

    /**
     * 招标公告 初始化数据
     * @param req
     * @return
     */
    UpdateNoticeDTO checkAndInitData(UpdateNoticeREQ req);

    /**
     * 准备提交记录数据
     * @param vo
     * @return
     */
    NoticeChangeRecord toNoticeChangeRecord(ProjectBidNoticeVO vo);


}
