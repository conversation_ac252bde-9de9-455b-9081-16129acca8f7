package com.hzw.sunflower.constant.constantenum;

/**
 * 评价类型枚举
 *
 * <AUTHOR>
 */
public enum AppraiseTypeEnum {

    REDUCE(0, "减分"),
    ADD(1, "加分");

    private Integer type;
    private String desc;

    AppraiseTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
