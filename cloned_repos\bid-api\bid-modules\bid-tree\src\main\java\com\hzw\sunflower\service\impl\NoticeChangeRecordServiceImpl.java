package com.hzw.sunflower.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.constant.constantenum.MediaTypeEnum;
import com.hzw.sunflower.controller.request.CommonNoticeREQ;
import com.hzw.sunflower.controller.request.NoticeAuditReq;
import com.hzw.sunflower.controller.request.NoticeChangeRecordReq;
import com.hzw.sunflower.controller.response.NoticeChangeRecordVO;
import com.hzw.sunflower.controller.response.ProjectBidNoticeVO;
import com.hzw.sunflower.controller.response.ProjectTaskVO;
import com.hzw.sunflower.dao.NoticeChangeRecordMapper;
import com.hzw.sunflower.dao.OssFileMapper;
import com.hzw.sunflower.dao.ProjectBidNoticeMapper;
import com.hzw.sunflower.dto.*;
import com.hzw.sunflower.entity.*;
import com.hzw.sunflower.exception.ExceptionEnum;
import com.hzw.sunflower.exception.SunFlowerException;
import com.hzw.sunflower.service.*;
import com.hzw.sunflower.utils.CompareObjectUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class NoticeChangeRecordServiceImpl extends ServiceImpl<NoticeChangeRecordMapper, NoticeChangeRecord> implements NoticeChangeRecordService {

    @Autowired
    private NoticeChangeRecordMapper noticeChangeRecordMapper;

    @Autowired
    private ProjectBidNoticeService noticeService;

    @Autowired
    private ProjectService projectService;

    @Autowired
    private ProjectBidNoticeMapper tProjectBidNoticeMapper;

    @Autowired
    private ApprovalOpinionService approvalOpinionService;

    @Autowired
    private AllMediaService allMediaService;

    @Autowired
    private OssFileMapper ossFileMapper;

    @Autowired
    private ProjectBidSectionService projectBidSectionService;


    /**
     * 根据公告ID查询其基本信息
     * @param  noticeIds 公共ID的列表
     * @return 公告ID与公告简单信息的映射
    */
    @Override
    public Map<Long, ProjectTaskVO> queryAppingTaskMapByIds(List<Long> noticeIds) {
        // 参数判空
        if(CollUtil.isEmpty(noticeIds)){
            return null;
        }

        List<AppingTaskNoticeDTO> appingTaskNoticeDTOList = this.baseMapper.queryAppingTaskMapByIds(noticeIds);
        Map<Long, ProjectTaskVO> map = new HashMap<>();
        appingTaskNoticeDTOList.forEach(i->{
            ProjectTaskVO appingTaskVO=new ProjectTaskVO();
            BeanUtils.copyProperties(i,appingTaskVO);
            if (i.getPackageSegmentStatus()!=null&&i.getPackageSegmentStatus().intValue()==0){
                appingTaskVO.setProjectBidSections(null);
            }
            map.put(i.getNoticeId(),appingTaskVO);
        });

        return map;
    }

    /**
     * 根据公告ID查询变更记录
    */
    @Override
    public Result<List<NoticeChangeRecordVO>> findChangeRecordListByNoticeId(NoticeAuditReq req) {

        ProjectBidNotice notice = noticeService.getById(req.getNoticeId());
        if(notice == null){
            throw new SunFlowerException(ExceptionEnum.NOTICE_NOT_FOUND,null);
        }
        Project project = projectService.getProjectById(notice.getProjectId());
        if(project == null){
            throw new SunFlowerException(ExceptionEnum.NOTICE_NOT_FOUND,null);
        }

        // 获取历史提交记录
        List<NoticeChangeRecord> noticeChangeRecords=noticeChangeRecordMapper.selectList(
                new QueryWrapper<NoticeChangeRecord>()
                        .eq("notice_id",req.getNoticeId())
                        .eq("project_id",req.getProjectId())
        );
        List<NoticeChangeRecordVO> noticeChangeRecordVOList=new ArrayList<>();
        for (int i=0;i<noticeChangeRecords.size();i++){
            NoticeChangeRecord noticeChangeRecord=noticeChangeRecords.get(i);
            NoticeChangeRecordVO noticeChangeRecordVO=new NoticeChangeRecordVO();
            noticeChangeRecordVO.setId(noticeChangeRecord.getId());
            noticeChangeRecordVO.setNoticeId(noticeChangeRecord.getNoticeId());
            noticeChangeRecordVO.setSubmitNum(noticeChangeRecord.getSubmitNum());
            noticeChangeRecordVO.setNoticeName(noticeChangeRecord.getNoticeName());
            noticeChangeRecordVO.setChangeTime(noticeChangeRecord.getChangeTime());
            noticeChangeRecordVO.setChangeType(noticeChangeRecord.getChangeType());
            noticeChangeRecordVO.setNoticeContent(noticeChangeRecord.getNoticeContent());
            noticeChangeRecordVO.setHasPackage(project.getPackageSegmentStatus().equals(1));
            noticeChangeRecordVO.setNoticeProgress(notice.getNoticeProgress()+"");
            // 标段历史记录
            if (StringUtils.isNotBlank(noticeChangeRecord.getSectionId())){
                List<Long> packageIdsList = Arrays.asList(noticeChangeRecord.getSectionId()
                        .split(","))
                        .stream()
                        .map(s -> Long.parseLong(s.trim()))
                        .collect(Collectors.toList());
                noticeChangeRecordVO.setPackageInfo(selectPackageInfoListByPackageIds(packageIdsList));
            }else{
                noticeChangeRecordVO.setPackageInfo(new ArrayList<>());
            }
            // 附件历史记录
            if (StringUtils.isNotBlank(noticeChangeRecord.getAnnexId())){
                List<Long> annexIdsList = Arrays.asList(noticeChangeRecord.getAnnexId()
                        .split(","))
                        .stream()
                        .map(s -> Long.parseLong(s.trim()))
                        .collect(Collectors.toList());
                noticeChangeRecordVO.setAnnexes(selectAnnexListByOssfileIds(annexIdsList));
            }else{
                noticeChangeRecordVO.setAnnexes(new ArrayList<>());
            }
            // 媒体历史记录
            noticeChangeRecordVO.setMedias(allMediaService.queryMedia(noticeChangeRecord.getId(), MediaTypeEnum.TENDER_BULLETIN.getCode(),true));
            // 比对数据是否修改
            if (i==0){
                noticeChangeRecordVO.setFlags(CompareObjectUtil.compareObject(noticeChangeRecordVO,noticeChangeRecordVO));
            }else {
                noticeChangeRecordVO.setFlags(compareObject(noticeChangeRecordVO,noticeChangeRecordVOList.get(i - 1)));
            }
            noticeChangeRecordVOList.add(noticeChangeRecordVO);
        }
        //提交次数倒序
        noticeChangeRecordVOList.sort((x, y) -> Long.compare(y.getSubmitNum(), x.getSubmitNum()));

        return Result.ok(noticeChangeRecordVOList);
    }

    @Override
    public NoticeChangeRecordVO findLastedChangeRecordByNoticeId(Long noticeId) {
        ProjectBidNotice notice = noticeService.getById(noticeId);
        if (notice == null) {
            throw new SunFlowerException(ExceptionEnum.NOTICE_NOT_FOUND, null);
        }
        Project project = projectService.getProjectById(notice.getProjectId());
        if (project == null) {
            throw new SunFlowerException(ExceptionEnum.NOTICE_NOT_FOUND, null);
        }

        // 获取历史提交记录
        List<NoticeChangeRecord> noticeChangeRecords = noticeChangeRecordMapper.selectList(
                new QueryWrapper<NoticeChangeRecord>()
                        .eq("notice_id", noticeId)
                        .orderByAsc("id")
        );
        NoticeChangeRecordVO noticeChangeRecordVO = null;
        if (noticeChangeRecords != null && noticeChangeRecords.size() > 0) {
            noticeChangeRecordVO = new NoticeChangeRecordVO();
            NoticeChangeRecord noticeChangeRecord = noticeChangeRecords.get(noticeChangeRecords.size()-1);
            noticeChangeRecordVO.setId(noticeChangeRecord.getId());
            noticeChangeRecordVO.setNoticeId(noticeChangeRecord.getNoticeId());
            noticeChangeRecordVO.setSubmitNum(noticeChangeRecord.getSubmitNum());
            noticeChangeRecordVO.setNoticeName(noticeChangeRecord.getNoticeName());
            noticeChangeRecordVO.setChangeTime(noticeChangeRecord.getChangeTime());
            noticeChangeRecordVO.setChangeType(noticeChangeRecord.getChangeType());
            noticeChangeRecordVO.setNoticeContent(noticeChangeRecord.getNoticeContent());
            noticeChangeRecordVO.setHasPackage(project.getPackageSegmentStatus().equals(1));
            noticeChangeRecordVO.setNoticeProgress(notice.getNoticeProgress() + "");


            // 标段历史记录
            if (StringUtils.isNotBlank(noticeChangeRecord.getSectionId())) {
                List<Long> packageIdsList = Arrays.asList(noticeChangeRecord.getSectionId()
                        .split(","))
                        .stream()
                        .map(s -> Long.parseLong(s.trim()))
                        .collect(Collectors.toList());
                List<PackageInfoDTO> packageInfoDTOList=  selectPackageInfoListByPackageIds(packageIdsList);
                packageInfoDTOList.forEach((item)->{
                    item.setNoticeId(noticeId);
                });
                noticeChangeRecordVO.setPackageInfo(packageInfoDTOList);
            } else {
                noticeChangeRecordVO.setPackageInfo(new ArrayList<>());
            }

            // 附件历史记录
            if (StringUtils.isNotBlank(noticeChangeRecord.getAnnexId())) {
                List<Long> annexIdsList = Arrays.asList(noticeChangeRecord.getAnnexId()
                        .split(","))
                        .stream()
                        .map(s -> Long.parseLong(s.trim()))
                        .collect(Collectors.toList());
                noticeChangeRecordVO.setAnnexes(selectAnnexListByOssfileIds(annexIdsList));
            } else {
                noticeChangeRecordVO.setAnnexes(new ArrayList<>());

            }
            // 媒体历史记录
            noticeChangeRecordVO.setMedias(allMediaService.queryMedia(noticeChangeRecord.getId(), MediaTypeEnum.TENDER_BULLETIN.getCode(),true));
        }
        return noticeChangeRecordVO;
    }
    /**
     * 根据标段ID集合获取标段信息
     * @param ids
     * @return
     */
    @Override
    public List<PackageInfoDTO> selectPackageInfoListByPackageIds(List<Long> ids) {

        List<ProjectBidSection> projectBidSectionList=projectBidSectionService.list(
                new QueryWrapper<ProjectBidSection>()
                        .in("id",ids)
//                        .eq("is_delete",0)
        );
        List<PackageInfoDTO> packageInfoDTOList=new ArrayList<>();
        projectBidSectionList.stream().forEach((item)->{
            PackageInfoDTO packageInfoDTO=new PackageInfoDTO();
            packageInfoDTO.setPackageId(item.getId());
            packageInfoDTO.setPackageName(item.getPackageName());
            packageInfoDTO.setPurchaseMode(item.getPurchaseMode());
            packageInfoDTO.setPackageNumber(item.getPackageNumber()+"");
            packageInfoDTO.setPurchaseModeName(item.getPurchaseModeName());
            packageInfoDTO.setEntrustMoney(item.getEntrustMoney());
            packageInfoDTO.setPurchaseStatus(item.getPurchaseStatus()+"");
            packageInfoDTO.setPurchaseType(item.getPurchaseType()+"");
            packageInfoDTOList.add(packageInfoDTO);
        });
        return packageInfoDTOList;
    }

    /**
     * 更近附件ID集合获取附件信息
     * @param ids
     * @return
     */
    @Override
    public List<AnnexDTO> selectAnnexListByOssfileIds(List<Long> ids) {

        List<OssFile> ossFileList=ossFileMapper.selectList(new QueryWrapper<OssFile>().in("id",ids));
        List<AnnexDTO> annexDTOList=new ArrayList<>();
        ossFileList.stream().forEach((item)->{
            AnnexDTO annexDTO=new AnnexDTO();
            annexDTO.setId(item.getId());
            annexDTO.setKey(item.getOssFileKey());
            annexDTO.setName(item.getOssFileName());
            annexDTOList.add(annexDTO);
        });
        return annexDTOList;
    }


    /**
     * 比对两个条数据是否有变更
     * @param source
     * @param target
     * @return
     */
    private Map<Object,Boolean> compareObject(NoticeChangeRecordVO source,NoticeChangeRecordVO target){
        source.getMedias().forEach(item->item.setId(null));
        target.getMedias().forEach(item->item.setId(null));
       return CompareObjectUtil.compareObject(source,target);
    }
    /**
     *
     * 根据标段号获取招标公告信息变更记录
     * */
    @Override
    public Result<List<NoticeChangeRecordVO>> selectChangeRecordListListByBid(NoticeChangeRecordReq req) {

        Project project = projectService.getProjectById(req.getProjectId());

        SimpleBidNoticeDTO noticeInfo = tProjectBidNoticeMapper.queryNoticeBySectionId(req.getSectionId());

        // 获取历史提交记录
        QueryWrapper<NoticeChangeRecord> noticeChangeRecordQuery=new QueryWrapper<>();
        noticeChangeRecordQuery.orderByAsc("id");
        if(req.getBidRound()!=null&&req.getBidRound().intValue()==1){
            ProjectBidNotice projectBidNotice = tProjectBidNoticeMapper.selectBySectionIdAndRound(project.getId(), req.getSectionId(), req.getBidRound());
            if (projectBidNotice!=null){
                noticeChangeRecordQuery.eq("notice_id",projectBidNotice.getId());
            }else {
                noticeChangeRecordQuery.eq("notice_id",0);
            }
        }else{
            noticeChangeRecordQuery.like("section_id","%"+req.getSectionId()+"%");
        }
        List<NoticeChangeRecord> noticeChangeRecords=noticeChangeRecordMapper.selectList(noticeChangeRecordQuery);
        List<NoticeChangeRecordVO> noticeChangeRecordVOList=new ArrayList<>();
//        Map<Long,TProjectBidNotice> bidNoticeMap=new HashMap<>();
        for (int i=0;i<noticeChangeRecords.size();i++) {
            NoticeChangeRecord noticeChangeRecord = noticeChangeRecords.get(i);

            NoticeChangeRecordVO noticeChangeRecordVO = new NoticeChangeRecordVO();

            noticeChangeRecordVO.setId(noticeChangeRecord.getId());
            noticeChangeRecordVO.setNoticeId(noticeChangeRecord.getNoticeId());
            noticeChangeRecordVO.setSubmitNum(noticeChangeRecord.getSubmitNum());
            noticeChangeRecordVO.setNoticeName(noticeChangeRecord.getNoticeName());
            noticeChangeRecordVO.setChangeTime(noticeChangeRecord.getChangeTime());
            noticeChangeRecordVO.setChangeType(noticeChangeRecord.getChangeType());
            noticeChangeRecordVO.setNoticeContent(noticeChangeRecord.getNoticeContent());
            noticeChangeRecordVO.setHasPackage(project.getPackageSegmentStatus().equals(1));

            // 设置最后一次提交 审核状态
            if (i+1==noticeChangeRecords.size()){
               if (noticeInfo!=null){
                   noticeChangeRecordVO.setNoticeProgress(noticeInfo.getNoticeProgress() + "");
               }else{
                   noticeChangeRecordVO.setNoticeProgress("");
               }
            }
            // 标段历史记录
            if (StringUtils.isNotBlank(noticeChangeRecord.getSectionId())) {
                List<Long> packageIdsList = Arrays.asList(noticeChangeRecord.getSectionId()
                        .split(","))
                        .stream()
                        .map(s -> Long.parseLong(s.trim()))
                        .collect(Collectors.toList());
                noticeChangeRecordVO.setPackageInfo(selectPackageInfoListByPackageIds(packageIdsList));
            } else {
                noticeChangeRecordVO.setPackageInfo(new ArrayList<>());
            }


            // 附件历史记录
            if (StringUtils.isNotBlank(noticeChangeRecord.getAnnexId())) {
                List<Long> annexIdsList = Arrays.asList(noticeChangeRecord.getAnnexId()
                        .split(","))
                        .stream()
                        .map(s -> Long.parseLong(s.trim()))
                        .collect(Collectors.toList());
                noticeChangeRecordVO.setAnnexes(selectAnnexListByOssfileIds(annexIdsList));
            } else {
                noticeChangeRecordVO.setAnnexes(new ArrayList<>());
            }
            // 媒体历史记录
            noticeChangeRecordVO.setMedias(allMediaService.queryMedia(noticeChangeRecord.getId(), MediaTypeEnum.TENDER_BULLETIN.getCode(),true));
            // 比对数据是否修改
            if (i==0){
                noticeChangeRecordVO.setFlags(CompareObjectUtil.compareObject(noticeChangeRecordVO,noticeChangeRecordVO));
            }else {
//                noticeChangeRecordVO.setFlags(CompareObjectUtil.compareObject(noticeChangeRecordVO,noticeChangeRecordVOList.get(i - 1)));
                noticeChangeRecordVO.setFlags(compareObject(noticeChangeRecordVO,noticeChangeRecordVOList.get(i - 1)));
            }
            noticeChangeRecordVO.setSubmitNum(Long.parseLong((i+1)+""));
            noticeChangeRecordVOList.add(noticeChangeRecordVO);
        }

        // 历史记录为空时，读取暂存的数据
//        if(noticeChangeRecordVOList == null || noticeChangeRecordVOList.size() == 0) {
//
//            if (noticeInfo != null && noticeInfo.getState() == 2 && noticeInfo.getNoticeId() != null) {
//                //公告内容暂存 去t_project_bid_notice取内容
//                CommonNoticeREQ reqZc = new CommonNoticeREQ();
//                reqZc.setProjectId(req.getProjectId());
//                reqZc.setNoticeId(noticeInfo.getNoticeId());
//                Result<ProjectBidNoticeVO> result = approvalOpinionService.findApprovalingNotice(reqZc);
//                ProjectBidNoticeVO projectBidNoticeVO = result.getData();
//
//                NoticeChangeRecordVO recordVOLast = new NoticeChangeRecordVO();
//                recordVOLast.setNoticeName(projectBidNoticeVO.getNoticeName());
//                recordVOLast.setNoticeContent(projectBidNoticeVO.getNoticeContent());
//                recordVOLast.setAnnexes(projectBidNoticeVO.getAnnex());
//                recordVOLast.setMedias(projectBidNoticeVO.getMedias());
//                recordVOLast.setNoticeId(noticeInfo.getNoticeId());
//                recordVOLast.setIntendedReleaseTime(projectBidNoticeVO.getIntendedReleaseTime());
//                recordVOLast.setEndTime(projectBidNoticeVO.getEndTime());
//                noticeChangeRecordVOList.add(recordVOLast);
//
//
//            }else {
//
//            }
//        }else{
//
//        }

//
//        List<NoticeChangeRecordVO> records = noticeChangeRecordMapper.noticeInfoByBd(req);
//        NoticeChangeRecordVO recordVOLast = new NoticeChangeRecordVO();
//
//        if((records == null) || (records.size() == 0)){
//
//            if(noticeInfo!=null&&noticeInfo.getState()==2&&noticeInfo.getNoticeId()!=null){
//                //公告内容暂存 去t_project_bid_notice取内容
//                CommonNoticeREQ reqZc = new CommonNoticeREQ();
//                reqZc.setProjectId(req.getProjectId());
//                reqZc.setNoticeId(noticeInfo.getNoticeId());
//                Result<ProjectBidNoticeVO> result = approvalOpinionService.findApprovalingNotice(reqZc);
//                ProjectBidNoticeVO projectBidNoticeVO = result.getData();
//                recordVOLast.setNoticeName(projectBidNoticeVO.getNoticeName());
//                recordVOLast.setNoticeContent(projectBidNoticeVO.getNoticeContent());
//                recordVOLast.setAnnexes(projectBidNoticeVO.getAnnex());
//                recordVOLast.setMedias(projectBidNoticeVO.getMedias());
//                recordVOLast.setNoticeId(noticeInfo.getNoticeId());
//                recordVOLast.setIntendedReleaseTime(projectBidNoticeVO.getIntendedReleaseTime());
//                recordVOLast.setEndTime(projectBidNoticeVO.getEndTime());
//                records.add(recordVOLast);
//            }
//
//            //throw new SunFlowerException(ExceptionEnum.ILLEGAL_PARAMETER_EXCEPTION,"公告不存在或不可用");
//            return Result.ok(records);
//        }
//        records.get(0).setFlags(CompareObjectUtil.compareObject(records.get(0),records.get(0)));
//        records.get(0).setHasPackage(project.getPackageSegmentStatus().equals(1));
//
//        for(int i = 1;i < records.size();i ++){
//            records.get(i).setHasPackage(project.getPackageSegmentStatus().equals(1));
//            records.get(i).setFlags(CompareObjectUtil.compareObject(records.get(i),records.get(i - 1)));
//        }
//        //提交次数倒序
//        records.sort((x, y) -> Long.compare(y.getSubmitNum(), x.getSubmitNum()));
            //取最新的公告
//            if (req.getLatest() != null && req.getLatest() == 1) {
//
//                NoticeChangeRecordVO recordVOLast = recordVOLast = new NoticeChangeRecordVO();
//
//                if (noticeChangeRecordVOList.size()>0){
//                    recordVOLast = noticeChangeRecordVOList.get(0);
//                }
//                if (noticeInfo != null && noticeInfo.getState() == 2) {
//                    //公告内容暂存 去t_project_bid_notice取内容
//                    CommonNoticeREQ reqZc = new CommonNoticeREQ();
//                    reqZc.setProjectId(req.getProjectId());
//                    reqZc.setNoticeId(noticeInfo.getNoticeId());
//                    Result<ProjectBidNoticeVO> result = approvalOpinionService.findApprovalingNotice(reqZc);
//                    ProjectBidNoticeVO projectBidNoticeVO = result.getData();
//
//                    recordVOLast = new NoticeChangeRecordVO();
//                    recordVOLast.setNoticeName(projectBidNoticeVO.getNoticeName());
//                    recordVOLast.setNoticeContent(projectBidNoticeVO.getNoticeContent());
//                    recordVOLast.setAnnexes(projectBidNoticeVO.getAnnex());
//                    recordVOLast.setMedias(projectBidNoticeVO.getMedias());
//                    recordVOLast.setNoticeId(noticeInfo.getNoticeId());
//                }
//                noticeChangeRecordVOList.clear();
//                noticeChangeRecordVOList.add(recordVOLast);
//            }
        //提交次数倒序
        noticeChangeRecordVOList.sort((x, y) -> Long.compare(y.getSubmitNum(), x.getSubmitNum()));
        // 取最新的公告
        if (req.getLatest() != null && req.getLatest() == 1) {
            if (noticeInfo!=null){
                noticeChangeRecordVOList.clear();
                //公告内容暂存 去t_project_bid_notice取内容
                CommonNoticeREQ reqZc = new CommonNoticeREQ();
                reqZc.setProjectId(req.getProjectId());
                reqZc.setNoticeId(noticeInfo.getNoticeId());
                Result<ProjectBidNoticeVO> result = approvalOpinionService.findApprovalingNotice(reqZc);
                ProjectBidNoticeVO projectBidNoticeVO = result.getData();

                NoticeChangeRecordVO recordVOLast = new NoticeChangeRecordVO();
                recordVOLast.setNoticeName(projectBidNoticeVO.getNoticeName());
                recordVOLast.setNoticeContent(projectBidNoticeVO.getNoticeContent());
                recordVOLast.setPackageInfo(projectBidNoticeVO.getPackageInfo());
                recordVOLast.setAnnexes(projectBidNoticeVO.getAnnexes());
                recordVOLast.setMedias(projectBidNoticeVO.getMedias());
                recordVOLast.setNoticeId(noticeInfo.getNoticeId());
//                recordVOLast.setIntendedReleaseTime(projectBidNoticeVO.getIntendedReleaseTime());
//                recordVOLast.setEndTime(projectBidNoticeVO.getEndTime());
                noticeChangeRecordVOList.add(recordVOLast);
            }else{
                noticeChangeRecordVOList.clear();
            }

//            if (noticeChangeRecordVOList.size()>0){
//                if (noticeInfo!=null){
//                    NoticeChangeRecordVO recordVOLast = noticeChangeRecordVOList.get(0);
//                    noticeChangeRecordVOList.clear();
//                    noticeChangeRecordVOList.add(recordVOLast);
//                }else{
//                    noticeChangeRecordVOList.clear();
//                }
//
//            }

        }

        return Result.ok(noticeChangeRecordVOList);
    }


    /**
     *
     * 根据标段号获取招标公告信息变更记录App
     * */
    @Override
    public Result<List<NoticeChangeRecordVO>> selectChangeRecordListListByBidApp(NoticeChangeRecordReq req) {

        Project project = projectService.getProjectById(req.getProjectId());

        SimpleBidNoticeDTO noticeInfo = tProjectBidNoticeMapper.queryNoticeBySectionIdApp(req.getSectionId(),req.getBidRound());

        if(noticeInfo == null ){
            return Result.ok();
        }
        // 获取历史提交记录
        QueryWrapper<NoticeChangeRecord> noticeChangeRecordQuery=new QueryWrapper<>();
        noticeChangeRecordQuery.orderByAsc("id");
        if(req.getBidRound()!=null&&req.getBidRound().intValue()==1){
            ProjectBidNotice projectBidNotice = tProjectBidNoticeMapper.selectBySectionIdAndRound(project.getId(), req.getSectionId(), req.getBidRound());
            if (projectBidNotice!=null){
                noticeChangeRecordQuery.eq("notice_id",projectBidNotice.getId());
            }else {
                noticeChangeRecordQuery.eq("notice_id",0);
            }
        }else{
            noticeChangeRecordQuery.like("section_id","%"+req.getSectionId()+"%");
        }
        List<NoticeChangeRecord> noticeChangeRecords=noticeChangeRecordMapper.selectList(noticeChangeRecordQuery);
        List<NoticeChangeRecordVO> noticeChangeRecordVOList=new ArrayList<>();
//        Map<Long,TProjectBidNotice> bidNoticeMap=new HashMap<>();
        for (int i=0;i<noticeChangeRecords.size();i++) {
            NoticeChangeRecord noticeChangeRecord = noticeChangeRecords.get(i);

            NoticeChangeRecordVO noticeChangeRecordVO = new NoticeChangeRecordVO();

            noticeChangeRecordVO.setId(noticeChangeRecord.getId());
            noticeChangeRecordVO.setNoticeId(noticeChangeRecord.getNoticeId());
            noticeChangeRecordVO.setSubmitNum(noticeChangeRecord.getSubmitNum());
            noticeChangeRecordVO.setNoticeName(noticeChangeRecord.getNoticeName());
            noticeChangeRecordVO.setChangeTime(noticeChangeRecord.getChangeTime());
            noticeChangeRecordVO.setChangeType(noticeChangeRecord.getChangeType());
            noticeChangeRecordVO.setNoticeContent(noticeChangeRecord.getNoticeContent());
            noticeChangeRecordVO.setHasPackage(project.getPackageSegmentStatus().equals(1));

            // 设置最后一次提交 审核状态
            if (i+1==noticeChangeRecords.size()){
                if (noticeInfo!=null){
                    noticeChangeRecordVO.setNoticeProgress(noticeInfo.getNoticeProgress() + "");
                }else{
                    noticeChangeRecordVO.setNoticeProgress("");
                }
            }
            // 标段历史记录
            if (StringUtils.isNotBlank(noticeChangeRecord.getSectionId())) {
                List<Long> packageIdsList = Arrays.asList(noticeChangeRecord.getSectionId()
                        .split(","))
                        .stream()
                        .map(s -> Long.parseLong(s.trim()))
                        .collect(Collectors.toList());
                noticeChangeRecordVO.setPackageInfo(selectPackageInfoListByPackageIds(packageIdsList));
            } else {
                noticeChangeRecordVO.setPackageInfo(new ArrayList<>());
            }


            // 附件历史记录
            if (StringUtils.isNotBlank(noticeChangeRecord.getAnnexId())) {
                List<Long> annexIdsList = Arrays.asList(noticeChangeRecord.getAnnexId()
                        .split(","))
                        .stream()
                        .map(s -> Long.parseLong(s.trim()))
                        .collect(Collectors.toList());
                noticeChangeRecordVO.setAnnexes(selectAnnexListByOssfileIds(annexIdsList));
            } else {
                noticeChangeRecordVO.setAnnexes(new ArrayList<>());
            }
            // 媒体历史记录
            noticeChangeRecordVO.setMedias(allMediaService.queryMedia(noticeChangeRecord.getId(), MediaTypeEnum.TENDER_BULLETIN.getCode(),true));
            // 比对数据是否修改
            if (i==0){
                noticeChangeRecordVO.setFlags(CompareObjectUtil.compareObject(noticeChangeRecordVO,noticeChangeRecordVO));
            }else {
//                noticeChangeRecordVO.setFlags(CompareObjectUtil.compareObject(noticeChangeRecordVO,noticeChangeRecordVOList.get(i - 1)));
                noticeChangeRecordVO.setFlags(compareObject(noticeChangeRecordVO,noticeChangeRecordVOList.get(i - 1)));
            }
            noticeChangeRecordVO.setSubmitNum(Long.parseLong((i+1)+""));
            noticeChangeRecordVOList.add(noticeChangeRecordVO);
        }


        //提交次数倒序
        noticeChangeRecordVOList.sort((x, y) -> Long.compare(y.getSubmitNum(), x.getSubmitNum()));
        // 取最新的公告
        if (req.getLatest() != null && req.getLatest() == 1) {
            if (noticeInfo!=null){
                noticeChangeRecordVOList.clear();
                //公告内容暂存 去t_project_bid_notice取内容
                CommonNoticeREQ reqZc = new CommonNoticeREQ();
                reqZc.setProjectId(req.getProjectId());
                reqZc.setNoticeId(noticeInfo.getNoticeId());
                Result<ProjectBidNoticeVO> result = approvalOpinionService.findApprovalingNotice(reqZc);
                ProjectBidNoticeVO projectBidNoticeVO = result.getData();

                NoticeChangeRecordVO recordVOLast = new NoticeChangeRecordVO();
                recordVOLast.setNoticeName(projectBidNoticeVO.getNoticeName());
                recordVOLast.setNoticeContent(projectBidNoticeVO.getNoticeContent());
                recordVOLast.setPackageInfo(projectBidNoticeVO.getPackageInfo());
                recordVOLast.setAnnexes(projectBidNoticeVO.getAnnexes());
                recordVOLast.setMedias(projectBidNoticeVO.getMedias());
                recordVOLast.setNoticeId(noticeInfo.getNoticeId());
//                recordVOLast.setIntendedReleaseTime(projectBidNoticeVO.getIntendedReleaseTime());
//                recordVOLast.setEndTime(projectBidNoticeVO.getEndTime());
                noticeChangeRecordVOList.add(recordVOLast);
            }else{
                noticeChangeRecordVOList.clear();
            }

//            if (noticeChangeRecordVOList.size()>0){
//                if (noticeInfo!=null){
//                    NoticeChangeRecordVO recordVOLast = noticeChangeRecordVOList.get(0);
//                    noticeChangeRecordVOList.clear();
//                    noticeChangeRecordVOList.add(recordVOLast);
//                }else{
//                    noticeChangeRecordVOList.clear();
//                }
//
//            }

        }

        return Result.ok(noticeChangeRecordVOList);
    }
}
