package com.hzw.ssm.applets.action;

import com.hzw.ssm.applets.entity.ExpertDownFile;
import com.hzw.ssm.applets.service.ExpertDownFileService;
import com.hzw.ssm.applets.utils.GetJsonReqData;
import com.hzw.ssm.applets.utils.GetJsonRespData;
import com.hzw.ssm.applets.utils.JsonToObject;
import com.hzw.ssm.applets.utils.ResultJson;
import net.sf.json.JSONObject;
import org.apache.struts2.convention.annotation.Action;
import org.apache.struts2.convention.annotation.Namespace;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @Date 2021-02-07 15:26
 * @Version 1.0
 */
@Namespace("/applets/expertDownFileAction")
public class ExpertDownFileAction {

    @Autowired
    private ExpertDownFileService expertDownFileService;

    /**
     * 新增
     *
     * @return
     */
    @Action("save")
    public String save() {

        JSONObject jsonReqData = GetJsonReqData.getJsonReqData();
        String toString = jsonReqData.getJSONObject("formData").toString();
        ExpertDownFile expertDownFile = JsonToObject.jsonToObject(toString, ExpertDownFile.class);
        ResultJson resultJson = expertDownFileService.insertExpertDownFile(expertDownFile);
        GetJsonRespData.getJsonRespData(resultJson);
        return null;
    }

    /**
     * 新增
     *
     * @return
     */
    @Action("page")
    public String page() {

        JSONObject jsonReqData = GetJsonReqData.getJsonReqData();
        String toString = jsonReqData.getJSONObject("formData").toString();
        ExpertDownFile expertDownFile = JsonToObject.jsonToObject(toString, ExpertDownFile.class);
        ResultJson resultJson = expertDownFileService.queryPage(expertDownFile);
        GetJsonRespData.getJsonRespData(resultJson);
        return null;
    }
}
