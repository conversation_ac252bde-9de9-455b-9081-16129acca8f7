package com.hzw.ssm.applets.action;

import com.hzw.ssm.applets.utils.GetJsonReqData;
import com.hzw.ssm.applets.utils.GetJsonRespData;
import com.hzw.ssm.applets.utils.JsonToObject;
import com.hzw.ssm.applets.utils.ResultJson;
import com.hzw.ssm.expert.entity.Policies;
import com.hzw.ssm.expert.entity.PoliciesDetails;
import com.hzw.ssm.expert.service.PoliciesDetailsService;
import com.hzw.ssm.expert.service.PoliciesService;
import net.sf.json.JSONObject;
import org.apache.struts2.convention.annotation.Action;
import org.apache.struts2.convention.annotation.Namespace;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @Date 2021-02-05 17:26
 * @Version 1.0
 */
@Namespace("/applets/wxPoliciesAction")
public class WxPoliciesAction {

    @Autowired
    private PoliciesDetailsService policiesDetailsService;
    @Autowired
    private PoliciesService policiesService;

    /**
     * 政策法规
     *
     * @return
     */
    @Action("policiesdDtails")
    public String policies() {
        JSONObject jsonReqData = GetJsonReqData.getJsonReqData();
        String toString = jsonReqData.getJSONObject("formData").toString();
        PoliciesDetails policies = JsonToObject.jsonToObject(toString, PoliciesDetails.class);
        policies.setStatus(2);
        ResultJson resultJson = policiesDetailsService.policiesQueryPage(policies);
        GetJsonRespData.getJsonRespDataNoNull(resultJson);
        return null;
    }

    /**
     * 法规详情
     *
     * @return
     */
    @Action("getOne")
    public String getOne() {
        JSONObject jsonReqData = GetJsonReqData.getJsonReqData();
        String userId = jsonReqData.getString("userId");
        String toString = jsonReqData.getJSONObject("formData").toString();
        PoliciesDetails policies = JsonToObject.jsonToObject(toString, PoliciesDetails.class);
        policies.setExpertId(userId);
        ResultJson resultJson = policiesDetailsService.getOne(policies);
        GetJsonRespData.getTxtJsonRespDataNoNull(resultJson);
        return null;
    }

    /**
     * 法规类目
     *
     * @return
     */
    @Action("policies")
    public String policiesdDtails() {
        JSONObject jsonReqData = GetJsonReqData.getJsonReqData();
        String toString = jsonReqData.getJSONObject("formData").toString();
        Policies policies = JsonToObject.jsonToObject(toString, Policies.class);
        ResultJson resultJson = policiesService.queryAllEffective(policies);
        GetJsonRespData.getJsonRespData(resultJson);
        return null;
    }
}
