package com.hzw.sunflower.constant.constantenum;

public enum SealApplicationInfoEnum {
    // 申请事项
    CLARIFY(1,"澄清/修改"),
    REPLY(2,"异议/回复"),
    NOTICE(3,"通知"),
    CORRESPONDENCE(4,"函件"),
    ASK_FOR_INFORMATION(5,"请示"),
    REPORT(6,"报告"),
    TENDER_DOCUMENT(7,"投标文件"),
    BID_REPORT(8,"评标报告"),
    PURCHASING_DOCUMENT(9,"采购文件"),
    OTHER(10,"其他"),
    BID_WIN_NOTICE(11,"中标通知书"),

    LOCAL_PAGE(1,"当前页面签章"),
    ALL_PAGE(2,"全部页面批量签章"),
    RIDER_SEAL(3,"骑缝签章"),
    KEYWORDS(4,"关键字签章"),
    ;

    private Integer type;
    private String desc;

    SealApplicationInfoEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }


    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
