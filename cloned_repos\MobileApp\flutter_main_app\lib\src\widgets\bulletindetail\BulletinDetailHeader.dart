import "package:flutter/material.dart";

import 'package:flutter_main_app/src/constants/theme.dart';
import 'package:flutter_main_app/src/utils/ScreenUtils.dart';
import "package:flutter_main_app/src/fonts/fonts.dart";

class BulletinDetailHeader extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Container(
      color: themeContentBackgroundColor,
      child: Column(
        children: <Widget>[
          Padding(
            padding: const EdgeInsets.fromLTRB(15, 18, 20, 8),
            child: Text(
              '江苏方洋水务有限公司营收系统建设项目竞性 磋商公告',
              style: TextStyle(
                color: themeTitleColor,
                fontSize: 17,
              ),
            ),
          ),
          

          Padding(
            padding: const EdgeInsets.fromLTRB(15, 0, 0, 10),
            child: Row(
              children: <Widget>[
                Text(
                  '招标编号',
                  style: TextStyle(
                    color: themeTipsTextColor,
                    fontSize: 10,
                  ),
                ),
                
                Padding(
                  padding: const EdgeInsets.only(left: 5),
                  child: Text(
                    'Z310100J023J00002001',
                    style: TextStyle(
                      color: themeTipsTextColor,
                      fontSize: 10,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // 
          Padding(
            padding: const EdgeInsets.fromLTRB(15, 0, 0, 20),
            child: Row(
              children: <Widget>[
                 Container(
                    padding: EdgeInsets.symmetric(horizontal: 8),
                    height: 17,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.all(new Radius.circular(6)),
                      border: Border.all(
                        color: Color(0xFF3E78E6),
                        width: themeBorderWidth,
                      )
                    ),
                    child: Text(
                      '连云港市',
                      style: TextStyle(
                        color: Color(0xFF3E78E6),
                        fontSize: 10,
                      ),
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.only(left: 10),
                    child: Container(
                      padding: EdgeInsets.symmetric(horizontal: 8),
                      height: 17,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.all(new Radius.circular(6)),
                        border: Border.all(
                          color: Color(0xFFF9A545),
                          width: themeBorderWidth,
                        )
                      ),
                      child: Text(
                        '招标公告',
                        style: TextStyle(
                          color: Color(0xFFF9A545),
                          fontSize: 10,
                        ),
                      ),
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.only(left:22),
                    child: Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: <Widget>[
                          Icon(FontsHelper.clockIconData,size: 10,color: themeHintTextColor),
                          Padding(
                            padding: const EdgeInsets.only(left: 5),
                            child: Text(
                              '2019-05-20',
                              style: TextStyle(
                                color: themeHintTextColor,
                                fontSize: 10,
                              ),
                            ),
                          )
                        ],
                    ),
                  )
              ],
            ),
          ),

          Container(
            color: themeBorderColor,
            width: ScreenUtils.screenWidth,
            height: themeBorderWidth,
          )
        ],
      ),
    );
  }
}