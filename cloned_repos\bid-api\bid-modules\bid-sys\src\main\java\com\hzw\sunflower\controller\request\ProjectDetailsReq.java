package com.hzw.sunflower.controller.request;

import com.hzw.sunflower.controller.response.ProjectColumnRequiredVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@ApiModel(description = "项目详细信息设置")
@Data
public class ProjectDetailsReq {

    @ApiModelProperty(value = "字段必填及规则")
    private List<ProjectColumnRequiredVO> projectColumnRequired;

    @ApiModelProperty(value = "采购方式")
    private List<PurchaseTypeReq> purchaseType;

}
