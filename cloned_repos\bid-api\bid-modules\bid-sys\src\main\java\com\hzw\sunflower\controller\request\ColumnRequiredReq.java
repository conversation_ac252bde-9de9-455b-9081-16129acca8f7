package com.hzw.sunflower.controller.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ColumnRequiredReq {
    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "模块")
    private String moduleName;

    @ApiModelProperty(value = "字段名称")
    private String columnName;

    @ApiModelProperty(value = "字段编码")
    private String code;

    @ApiModelProperty(value = "关联功能 1.标书费 2.电子开评标 3.保证金")
    private Integer relationModule;

    @ApiModelProperty(value = "是否代理机构展示 1.是 2.否")
    private Integer isRequiredAgent;

    @ApiModelProperty(value = "是否招标人展示 1.是 2.否")
    private Integer isRequiredTender;

    @ApiModelProperty(value = "是否展示 1.是 2.否")
    private Integer isRequiredSupplier;

    @ApiModelProperty(value = "登陆人身份1招标人2代理机构3供应商")
    private Integer identity;

    @ApiModelProperty(value = "license功能 1.基础版 2.专业版 3.旗舰版")
    private Integer licenseFunction;
}
