package com.hzw.ssm.fw.uploadify.action;

import com.hzw.ssm.applets.dao.EducationInfoMapper;
import com.hzw.ssm.applets.dao.TitleInfoMapper;
import com.hzw.ssm.expert.dao.ExpertInfoMapper;
import com.hzw.ssm.expert.entity.ExpertInfoEntity;
import com.hzw.ssm.fw.base.BaseAction;
import com.hzw.ssm.fw.uploadify.bean.FileEntity;
import com.hzw.ssm.fw.uploadify.service.UploadService;
import com.hzw.ssm.fw.util.FileUtil;
import com.hzw.ssm.fw.util.SysConstants;
import com.hzw.ssm.util.FileTypeCheck;
import com.hzw.ssm.util.file.FileUtils;
import org.apache.struts2.ServletActionContext;
import org.apache.struts2.convention.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * 文件上传操作
 *
 * <AUTHOR>
 * @date 2014-8-7
 */
@Namespace("/jqueryUploadify")
@ParentPackage(value = "default")
@Results({@Result(name = "init", location = "/jsp/uploadify.jsp")
})

public class UploadAction extends BaseAction {

    /**
     *
     */
    private static final long serialVersionUID = -8823858495799309882L;

    //@Value("${FilePath}")
    //private String file_Path;


    /**
     * 文件类对应页面传递的文件域的名称相同
     */
    private File uploadify;

    /**
     * 页面上传的文件的文件名称
     */
    private String uploadifyFileName;

    /**
     * 要上传的文件类型
     */
    private String fileType;

    /**
     * 关联编号
     */
    private String relationId;

    /**
     * 文件实体类
     */
    private FileEntity objFile;

    /**
     * 文件路径
     */
    private String filePath;

    private String tableField;

    private String fileId;

    private String tableName;

    private String flag;

    private static final int BUFFER_SIZE = 16 * 1024;

    private InputStream inputStream;

    @Autowired
    private UploadService uploadService;

    @Autowired
    private ExpertInfoMapper expertInfoMapper;
    @Autowired
    private EducationInfoMapper educationInfoMapper;
    @Autowired
    private TitleInfoMapper titleInfoMapper;


    @Action("init")
    public String init() {

        return "init";
    }

    /**
     * 文件上传处理
     *
     * @return
     */
    @Action("doUploadFiles")
    public String uploadFiles() {
        if (objFile == null) {
            objFile = new FileEntity();
        }

        String savePath = ServletActionContext.getServletContext().getRealPath("") + "/" + SysConstants.FILE_PATH_ROOT;

        String nowTime = new SimpleDateFormat("yyyyMMdd").format(new Date());//当前时间

        File f1 = new File(savePath + nowTime);
        if (!f1.exists()) {//
            f1.mkdirs();
        }
        String msg = "";
//        String newName = System.currentTimeMillis() + uploadifyFileName.substring(uploadifyFileName.lastIndexOf("."));
        String newName = FileTypeCheck.fileTypeCheck(uploadifyFileName);
        try {
            String filePath = savePath + nowTime + "/" + newName;
            File dst = new File(filePath);
            boolean result = copyFile(uploadify, dst);
            msg = "文件上传成功！";
        } catch (Exception e) {
            e.printStackTrace();
            msg = "文件上传失败！";
        }

        HttpServletResponse response = ServletActionContext.getResponse();
        response.setCharacterEncoding("utf-8");
        PrintWriter writer = null;
        try {
            writer = response.getWriter();
            writer.print("/" + SysConstants.FILE_PATH_ROOT + nowTime + "/" + newName);
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            writer.flush();
            writer.close();
        }
        return null;
    }


    /**
     * 文件复制处理
     *
     * @param src
     * @param dst
     * @return
     */
    private static boolean copyFile(File src, File dst) {
        InputStream ins = null;
        OutputStream os = null;
        try {
            ins = new BufferedInputStream(new FileInputStream(src), BUFFER_SIZE);
            os = new BufferedOutputStream(new FileOutputStream(dst), BUFFER_SIZE);
            byte[] buffer = new byte[BUFFER_SIZE];
            int len = 0;
            while ((len = ins.read(buffer)) > 0) {
                os.write(buffer, 0, len);
            }
        } catch (FileNotFoundException e) {
            e.printStackTrace();
            return false;
        } catch (IOException e) {
            e.printStackTrace();
            return false;
        } finally {
            try {
                if (ins != null) {
                    ins.close();
                }
                if (os != null) {
                    os.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return true;
    }

    /**
     * 下载服务器上的文件
     *
     * @return
     */
    @Action("downLoadFile")
    public String downLoadFile() {
        String savePath = ServletActionContext.getServletContext().getRealPath("");
        this.filePath = savePath + filePath;
        this.context();
        String path = "";
        try {
            uploadifyFileName = new String(uploadifyFileName.getBytes("UTF-8"), "UTF-8");
            if ("1".equals(flag)) {
                this.filePath = filePath + File.separator + uploadifyFileName;
            }
            path = FileUtils.replaceSeparator(this.filePath);
            inputStream = new FileInputStream(path);
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        FileUtil.download(uploadifyFileName, inputStream, this.getResponse());

        return null;
    }

    /**
     * 删除服务器上的文件
     *
     * @return
     */
    @Action("deleteFiles")
    public String deleteFiles() {
        String savePath = ServletActionContext.getServletContext().getRealPath("");
        this.filePath = savePath + filePath;
        File file = new File(this.filePath);
        if (file.exists()) {
            //file.delete(); 文件不删除【如果删除，修改前文件无法查】
        }
        HttpServletResponse response = ServletActionContext.getResponse();
        response.setCharacterEncoding("utf-8");
        PrintWriter writer = null;
        try {
            writer = response.getWriter();
            writer.print("success");
            // 更新专家表文件
            ExpertInfoEntity expertInfoEntity = new ExpertInfoEntity();
            expertInfoEntity.setId(fileId);
            if("old_ICBackFile".equals(tableField)){
                expertInfoEntity.setTableField("ICBACK_NAME");
            }else{
                expertInfoEntity.setTableField(tableField);
            }
            expertInfoEntity.setTableName(tableName);
            if ("t_education_info".equals(tableName)) {
                // 删除学历
                educationInfoMapper.deletePictureById(fileId);
            }else if("t_education_info_acad".equals(tableName)){
                // 删除学位
                educationInfoMapper.deleteAcadPictureById(fileId);
            }else if("t_title_info".equals(tableName)){
                titleInfoMapper.deletePictureById(fileId);
            }else if("t_expert_info".equals(tableName)){
                expertInfoMapper.updateFile(expertInfoEntity);
            }else if("t_expert_practice".equals(tableName)){
                expertInfoMapper.updatePracticeFile(expertInfoEntity);
            }
        } catch (IOException e) {
            writer.print("fail");
        } finally {
            writer.flush();
            writer.close();
        }
        return null;
    }

    /**
     * 下载服务器上的PDF文件
     *
     * @return
     */
    @Action("downLoadPdfFile")
    public String downLoadPdfFile() {

        String savePath = ServletActionContext.getServletContext().getRealPath("");
        this.filePath = savePath + File.separator + filePath;
        this.context();
        String path = "";
        try {


            String fileName = new String(uploadifyFileName.getBytes("ISO8859-1"), "UTF-8");
            if (fileName.contains("?") || fileName.contains("\u0095")) {
                uploadifyFileName = new String(uploadifyFileName.getBytes("UTF-8"), "UTF-8");
            } else {
                uploadifyFileName = fileName;
            }
            if ("1".equals(flag)) {
                this.filePath = filePath + File.separator + uploadifyFileName;
            }
            path = FileUtils.replaceSeparator(this.filePath);
            inputStream = new FileInputStream(path);
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        FileUtil.download(uploadifyFileName, inputStream, this.getResponse());

        try {
            //删除服务器上生成的临时文件文件
            File file = new File(path);
            if (file.exists()) {
                file.delete();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return null;
    }

    public File getUploadify() {
        return uploadify;
    }

    public void setUploadify(File uploadify) {
        this.uploadify = uploadify;
    }

    public String getUploadifyFileName() {
        return uploadifyFileName;
    }

    public void setUploadifyFileName(String uploadifyFileName) {
        this.uploadifyFileName = uploadifyFileName;
    }

    public String getFileType() {
        return fileType;
    }

    public void setFileType(String fileType) {
        this.fileType = fileType;
    }

    public FileEntity getObjFile() {
        return objFile;
    }

    public void setObjFile(FileEntity objFile) {
        this.objFile = objFile;
    }

    public String getRelationId() {
        return relationId;
    }

    public void setRelationId(String relationId) {
        this.relationId = relationId;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public InputStream getInputStream() {
        return inputStream;
    }

    public void setInputStream(InputStream inputStream) {
        this.inputStream = inputStream;
    }

    public String getTableField() {
        return tableField;
    }

    public void setTableField(String tableField) {
        this.tableField = tableField;
    }

    public String getFileId() {
        return fileId;
    }

    public void setFileId(String fileId) {
        this.fileId = fileId;
    }

    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    public String getFlag() {
        return flag;
    }

    public void setFlag(String flag) {
        this.flag = flag;
    }

}
