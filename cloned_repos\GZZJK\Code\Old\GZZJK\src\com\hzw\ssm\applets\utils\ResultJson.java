package com.hzw.ssm.applets.utils;

public class ResultJson {

    private Integer ResultCode;
    private boolean Result;
    private Object DataSource;
    private String ResultText;
    private int PageSize;
    private int CurrentPage;
    private int TotalCount;

    public ResultJson(){}

    public ResultJson(Object object) {
        this.Result = true;
        this.ResultCode = 200;
        this.DataSource = object;
        this.ResultText = "请求或处理成功！";
    }

    public ResultJson(Integer resultCode, String resultText) {
        this.Result = false;
        this.ResultCode = resultCode;
        this.ResultText = resultText;
    }

    public Integer getResultCode() {
        return ResultCode;
    }

    public void setResultCode(Integer resultCode) {
        ResultCode = resultCode;
    }

    public boolean isResult() {
        return Result;
    }

    public void setResult(boolean result) {
        Result = result;
    }

    public Object getDataSource() {
        return DataSource;
    }

    public void setDataSource(Object dataSource) {
        DataSource = dataSource;
    }

    public String getResultText() {
        return ResultText;
    }

    public void setResultText(String resultText) {
        ResultText = resultText;
    }

    public int getPageSize() {
        return PageSize;
    }

    public void setPageSize(int pageSize) {
        PageSize = pageSize;
    }

    public int getCurrentPage() {
        return CurrentPage;
    }

    public void setCurrentPage(int currentPage) {
        CurrentPage = currentPage;
    }

    public int getTotalCount() {
        return TotalCount;
    }

    public void setTotalCount(int totalCount) {
        TotalCount = totalCount;
    }
}
