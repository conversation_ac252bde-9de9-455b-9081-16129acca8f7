///***
/// * 标签风格的文本
/// *
/// * 有 2 套订制宽高的途径
/// * 1.直接指定宽高
/// * 2.通过边距、字号、行高因子计算
/// *
/// * <AUTHOR>
/// * @date 20191025
/// */

import 'package:flutter/material.dart';

import 'package:flutter_main_app/src/constants/theme.dart';

class CustomTab extends StatelessWidget {

  /// * 边框默认宽度
  /// * 暂时不支持传入，会联动影响到 CustomInfoCard 组件宽高的计算
  static const double _borderWidth = themeBorderWidth;

  const CustomTab({
    Key key,
    @required this.text,
    final double width,
    final double height,
    this.horizontalPadding = 8.0,
    this.verticalPadding = 1.0,
    this.heightFactor = 1.4,
    this.fontSize = 10.0,
    this.color = const Color(0xfff9a545),
    this.backgroundColor = Colors.transparent,
    this.borderColor = const Color(0xfff9a545),
    this.borderRadius,
  }) : assert(text != null),
    assert(width == null || width > 0.0),
    assert(height == null || height >= 10.0),
    assert(horizontalPadding != null && horizontalPadding >= 2.0),
    assert(verticalPadding != null && verticalPadding >= 1.0),
    assert(heightFactor != null && heightFactor > 1.0),
    assert(fontSize != null && fontSize >= 10.0),
    assert(color != null),
    assert(backgroundColor != null),
    assert(borderColor != null),
    /// * 传入的宽度 ?? (文本长度 + 0.5 的富余) * 字号 + 水平内边距 + 边框
    width = width ?? (text.length + 0.5) * fontSize + 2 * horizontalPadding + 2.0,
    /// * 传入的高度 ?? 文本高度 + 竖直内边距 + 边框
    height = height ?? heightFactor * fontSize + 2 * verticalPadding + 2.0,
    super(key: key);

  /// * 文本
  final String text;
  /// * 宽度
  final double width;
  /// * 高度
  final double height;
  /// * 水平内边距
  final double horizontalPadding;
  /// * 竖直内边距
  final double verticalPadding;
  /// * 行高因子
  final double heightFactor;
  /// * 字号
  final double fontSize;
  /// * 字体颜色
  final Color color;
  /// * 背景颜色
  final Color backgroundColor;
  /// * 边框颜色
  final Color borderColor;
  /// * 圆角
  final BorderRadius borderRadius;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(
        left: this.horizontalPadding,
        top: this.verticalPadding,
        right: this.horizontalPadding,
        bottom: this.verticalPadding,
      ),
      constraints: BoxConstraints.tightFor(
        width: this.width,
        height: this.height,
      ),
      decoration: BoxDecoration(
        color: this.backgroundColor,
        border: Border.fromBorderSide(
          BorderSide(
            width: _borderWidth,
            color: this.borderColor,
          ),
        ),
        borderRadius: this.borderRadius ?? BorderRadius.circular(this.height / 2),
      ),
      child: Center(
        child: Text(
          this.text,
          style: TextStyle(
            fontSize: this.fontSize,
            color: this.color,
          ),
        ),
      ),
    );
  }
}
