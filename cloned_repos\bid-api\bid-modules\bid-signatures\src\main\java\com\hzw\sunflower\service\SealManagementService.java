package com.hzw.sunflower.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.controller.request.SealApplicationREQ;
import com.hzw.sunflower.dto.SealManagementDTO;
import com.hzw.sunflower.entity.SealManagement;
import com.hzw.sunflower.entity.SealManagementOperateLog;
import com.hzw.sunflower.entity.condition.SealManagementCondition;

import java.util.List;


/**
* SealManagementService接口
*
*/
public interface SealManagementService extends IService<SealManagement> {
    /**
    * 根据条件分页查询列表
    *
    * @param condition 查询条件
    * @return 分页信息
    */
    List<SealManagement> findInfoByCondition(SealManagementCondition condition);

    /**
    * 根据主键ID查询信息
    *
    * @param id 主键ID
    * @return SealManagement信息
    */
    SealManagement getInfoById(Long id);

    /**
    * 新增
    *
    * @param sealManagement
    * @return 是否成功
    */
    Result<Boolean> addInfo(SealManagementDTO sealManagement);



    /**
     * 停用启用
     *
     * @param sealManagement
     * @return 是否成功
     */
    Result<Boolean> updateStatus(SealManagementDTO sealManagement);


    /**
     *操作日志
     * @param id
     * @return
     */
     List<SealManagementOperateLog> getLog(Long id);


    /**
    * 根据主键ID删除
    *
    * @param id 主键ID
    * @return 是否成功
    */
    Boolean deleteById(Long id);

    /**
    * 根据主键ID列表批量删除
    *
    * @param idList 主键ID列表
    * @return 是否成功
    */
    Boolean deleteByIds(List<Long> idList);

    Boolean checkChooseSeal(SealApplicationREQ req);
}