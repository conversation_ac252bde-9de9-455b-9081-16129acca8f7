package com.hzw.sunflower.controller.request;

import com.hzw.sunflower.dto.SealApplicationProjectDTO;
import com.hzw.sunflower.dto.SealFileDTO;
import com.hzw.sunflower.entity.SealApplication;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
* SealApplication 请求实体
*
* <AUTHOR>
* @version 1.0.0
*/
@ApiModel(description = "SealApplication")
@Data
public class SealApplicationREQ extends SealApplication{

    @ApiModelProperty(value = "项目信息")
    private List<SealApplicationProjectDTO> projectDTOS;

    @ApiModelProperty(value = "文件信息")
    private List<SealFileDTO> fileDTOS;

}