package com.hzw.sunflower.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzw.sunflower.common.redis.RedisCache;
import com.hzw.sunflower.constant.CommonConstants;
import com.hzw.sunflower.constant.GeneralConstants;
import com.hzw.sunflower.constant.MessageConstants;
import com.hzw.sunflower.constant.constantenum.*;
import com.hzw.sunflower.controller.project.request.*;
import com.hzw.sunflower.controller.project.response.ProjectListVO;
import com.hzw.sunflower.controller.project.response.ProjectVo;
import com.hzw.sunflower.controller.project.response.WaitListVo;
import com.hzw.sunflower.dao.ProjectMapper;
import com.hzw.sunflower.datascope.utils.DataScopeUtil;
import com.hzw.sunflower.dto.*;
import com.hzw.sunflower.entity.*;
import com.hzw.sunflower.security.utils.SecurityUtils;
import com.hzw.sunflower.service.*;
import com.hzw.sunflower.service.bidder.ProjectAgentApprovalService;
import com.hzw.sunflower.util.BeanListUtil;
import com.hzw.sunflower.util.JsonUtils;
import com.hzw.sunflower.util.LoginUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @date ：Created in 2021/4/12 16:18
 * @description：代理建项目
 * @modified By：`
 * @version: 1.0
 */
@Service
@Slf4j
public class AgentProjectServiceImpl extends ServiceImpl<ProjectMapper, Project> implements AgentProjectService {

    @Autowired
    private ProjectEntrPersNumService projectEntrPersNumService;

    @Autowired
    private ProjectSerialNumService projectSerialNumService;

    @Autowired
    private UserAgentRecordService userAgentRecordService;

    @Autowired
    private ProjectService projectService;

    @Autowired
    private ProjectEntrustUserService projectEntrustUserService;

    @Autowired
    private ProjectBidSectionService bidSectionService;

    @Autowired
    private CommonSectionService commonSectionService;

    @Autowired
    private ProjectMapper projectMapper;

    @Autowired
    private ProjectShareService projectShareService;

    @Autowired
    private ProjectShareNccService projectShareNccService;

    @Autowired
    private BookkeepDepartmentConfirmService bookkeepDepartmentConfirmService;

    @Autowired
    private ProjectTendererPowerService projectTendererPowerService;

    @Autowired
    private ProjectAgentApprovalService projectAgentApprovalService;

    @Autowired
    private CommonApplyInfoService commonApplyInfoService;

    @Autowired
    private CommonMqService commonMqService;

    @Autowired
    private RedisCache redisCache;
    /**
     * 代理机构新增委托项目入口
     *
     * @param agentProject
     * @return
     */
    @Override
    public Boolean addAgentProject(AgentProjectReq agentProject, JwtUser user) {
        //执行项目入库
        Project project = new Project();
        // 添加项目
        boolean flag = addProject(agentProject, project);
        if (flag) {
            //执行委托项目编号入库
            flag = addAgentProjectEntrPersNum(agentProject, project);
        }
        if (flag) {
            flag = saveProjectEntrustUser(agentProject, project.getId());
        }
        if (flag) {
            //执行代理机构信息入库操作
            flag = lastAgentRecord(agentProject, user);
        }
        if (flag) {
            //执行更新项目表冗余字段
            projectService.saveOrUpdateProjectRedundantFieldDealWith(project.getId(),true);
            commonSectionService.saveOrUpdateProjectPackageFieldDealWith(project.getId());
        }
        return flag;
    }

    /**
     * 根据项目id 查询项目信息
     *
     * @param id
     */
    @Override
    public AgentProjectDTO getProjectById(Long id) {
        AgentProjectDTO dto = new AgentProjectDTO();
        //获取项目信息
        Project project = this.getById(id);
        BeanListUtil.copyProperties(project, dto);
        //查询所有关系数据 委托人
        AgentInItDto projectUser = projectEntrustUserService.getEntrustUserByProjectIdAndType(id, EntrustUserType.PRINCIPAL.getType());
        dto.setAgentUserDto(projectUser);
        //查询所有关系数据 被委托人
        AgentInItDto pUser = projectEntrustUserService.getEntrustUserByProjectIdAndType(id, EntrustUserType.ENTRUSTED_PERSON.getType());
        dto.setPagentUserDto(pUser);
        //获取委托项目信息
        LambdaQueryWrapper<ProjectEntrPersNum> projectNum = new LambdaQueryWrapper<>();
        projectNum.eq(ProjectEntrPersNum::getProjectId, id);
        List<ProjectEntrPersNum> list = projectEntrPersNumService.list(projectNum);
        dto.setProjectEntrPersNumDTO(BeanListUtil.convertList(list, ProjectEntrPersNumDTO.class));

        // 获取项目分摊情况
        //List<ProjectShare> shareList = new ArrayList<>();
        LambdaQueryWrapper<ProjectShare> qws = new LambdaQueryWrapper<>();
        qws.eq(ProjectShare::getProjectId,id);
        List<ProjectShare> psl = projectShareService.findInfo(id);

        // 获取项目分摊情况(ncc)
        LambdaQueryWrapper<ProjectShareNcc> qw = new LambdaQueryWrapper<>();
        qw.eq(ProjectShareNcc::getProjectId,id);
        List<ProjectShareNcc> ps2 = projectShareNccService.findInfo(id);
        // 如果存在直接返回
        //if(null != psl && psl.size() > 0){
        //    shareList = psl;
        //}else{
        // 不存在默认返回被委托人信息
        //    ProjectEntrustUserVo projectEntrustUserVos = projectEntrustUserService.selectEntrustUserAndDepartment(id);
        //    if(null != projectEntrustUserVos ){
        //        ProjectShare ps = new ProjectShare();
        //        ps.setShareDepartment(projectEntrustUserVos.getDepartmentId());
        //        ps.setProjectId(id);
        //        ps.setShareUser(projectEntrustUserVos.getUserId());
        //        ps.setShareDepartmentName(projectEntrustUserVos.getDepartmentName());
        //        ps.setShareUserName(projectEntrustUserVos.getUserName());
        //        shareList.add(ps);
        //    }
        //}
        dto.setShareList(psl);
        dto.setProjectShareNccList(ps2);



        // 获取项目委托人
        return dto;
    }

    /**
     * 修改项目信息
     *
     * @param agentProject
     * @return
     */
    @Override
    public Boolean updateAgentProject(AgentProjectReq agentProject, JwtUser user) {
        Project projectById = projectService.getProjectById(agentProject.getProjectId());
        List<ProjectShareDTO> allShareList = new ArrayList<>();
        //执行标识
        Boolean flag = true;
        //新增项目委托关系人表 信息
        // 暂存不需要判断是否新增了委托关系
        //if((ProjectSourceEnum.AGENCY.getType().equals(projectById.getProjectSource()))){
            if(OperationTypeEnum.SUBMIT.getType().equals(agentProject.getOperationType())){
                flag = projectEntrustUserService.addProjectEntrustUser(agentProject.getProjectEntrustUser(), user);
            }else{
                projectEntrustUserService.addProjectEntrustUser(agentProject.getProjectEntrustUser(), user);
            }
//        }else{
//
//        }


        if (flag) {

//            if(ProjectSourceEnum.AGENCY.getType().equals(projectById.getProjectSource())){
                // 更新委托人权限
                updateTendereePower(agentProject.getProjectEntrustUser(),projectById.getProjectSource());
//            }else{
                LambdaQueryWrapper<ProjectAgentApproval> lqwpaa = new LambdaQueryWrapper<>();
                lqwpaa.eq(ProjectAgentApproval::getProjectId,agentProject.getProjectId());
                ProjectAgentApproval agentApproval = projectAgentApprovalService.getBaseMapper().selectOne(lqwpaa);
                if(null != agentApproval &&   ProjectTendereeStatusEnum.XMJL_ACCEPTED.getCode().equals(agentApproval.getStatus())){
                    Project p = new Project();
                    p.setId(agentProject.getProjectId());
                    p.setStatus(ProjectStatusEnum.GOING.getValue());
                    p.setProjectStatus(ProjectStatusEnum.GOING.getName());
                    projectService.updateProject(p);
                    ProjectAgentApproval pa = new ProjectAgentApproval();
                    pa.setId(agentApproval.getId());
                    pa.setStatus(ProjectTendereeStatusEnum.XMJL_PERFECT.getCode());
                    projectAgentApprovalService.updateById(pa);
                }

//            }
            //// 更新委托人权限
            //updateTendereePower(agentProject.getProjectEntrustUser());
            //修改项目
            flag = updateProjectByAgent(agentProject);
        }
        if (flag) {
            //修改委托项目编号信息
            flag = updateProjectEntr(agentProject);
            // 更新项目分摊情况
            if(null != agentProject.getShareList() && agentProject.getShareList().size() > 0){
                // 判断用户部门是否重复
                long count = agentProject.getShareList().stream().collect(Collectors.collectingAndThen(
                        Collectors.toCollection(() -> new TreeSet<>(
                                Comparator.comparing(po ->po.getBookkeepId()+ ";" + po.getShareUser()))), ArrayList::new)).stream().count();

                // 判断用户是否重复
//                List<Long> shareUser = agentProject.getShareList().stream().map(ProjectShare::getBookkeepId).distinct().collect(Collectors.toList());
                if(agentProject.getShareList().size() == count){
                    projectShareService.updateShareList(agentProject,allShareList);
                }else{
                    flag = false;
                }
            }
        }
        if (flag) {
//            //修改委托项目编号信息
//            flag = updateProjectEntr(agentProject);
            // 更新项目分摊情况
            if(null != agentProject.getProjectShareNccList() && agentProject.getProjectShareNccList().size() > 0){

                // 塞入部门code
//                for (ProjectShareNcc projectShareNcc : agentProject.getProjectShareNccList()) {
//                  String  departmentCode = this.baseMapper.selectDepartmentCodeByBookkeepId(projectShareNcc.getBookkeepId()) ;
//                  projectShareNcc.setShareDepartmentCode(departmentCode);
//                }

                // 判断用户部门是否重复
                long count = agentProject.getProjectShareNccList().stream().collect(Collectors.collectingAndThen(
                        Collectors.toCollection(() -> new TreeSet<>(
                                Comparator.comparing(po ->po.getBookkeepId()+ ";" + po.getShareUser()))), ArrayList::new)).stream().count();

                // 判断用户是否重复
//                List<Long> shareUser = agentProject.getShareList().stream().map(ProjectShare::getBookkeepId).distinct().collect(Collectors.toList());
                if(agentProject.getProjectShareNccList().size() == count){
                    projectShareNccService.updateProjectShareNccList(agentProject,allShareList);
                }else{
                    flag = false;
                }
            }
        }
        if (flag) {

            Project project = projectService.getProjectById(agentProject.getProjectId());

            //提交的时候，插入分摊人审批表
            if (OperationTypeEnum.SUBMIT.getType().equals(agentProject.getOperationType()) && project.getStatus()<ProjectStatusEnum.FINISH.getValue()) {
                LambdaQueryWrapper<BookkeepDepartmentConfirm> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(BookkeepDepartmentConfirm::getProjectId, agentProject.getProjectId());
                bookkeepDepartmentConfirmService.remove(queryWrapper);

                List<String> recipientList = new ArrayList<>();
                if(allShareList.size()>0) {
                    for (ProjectShareDTO projectShare : allShareList) {
                        BookkeepDepartmentConfirm confirm = new BookkeepDepartmentConfirm();
                        confirm.setProjectId(agentProject.getProjectId());
                        confirm.setConfirmUserId(projectShare.getShareUser().toString());
                        confirm.setConfirmUserName(projectShare.getShareUserName());
                        confirm.setConfirmType(projectShare.getShareType());
                        confirm.setProjectShareId(projectShare.getId());
                        bookkeepDepartmentConfirmService.saveBookKeepConfirmInfo(confirm);
                        bookkeepDepartmentConfirmService.sendConfirmSms(agentProject.getProjectId(), agentProject.getAgentChargeId(), projectShare.getShareUser(), projectShare.getShareType());

                        recipientList.add(projectShare.getShareUser().toString());
                    }

                    // 发送app待办
                    AppOaMsgDto dto = new AppOaMsgDto();
                    dto.setTaskType(AppTaskTypeEnum.PROJECT_SHARE.getCode());
                    dto.setRecipientList(recipientList);
                    commonMqService.sendOaMsg(dto);
                }else{
                    //分摊人是自己的时候，直接可以提交
                    project.setStatus(ProjectStatusEnum.FINISH.getValue());
                    projectService.updateProject(project);

                    //推送项目到ncc
                    projectService.pushProjectNcc(project.getId(),null);
                }
            }

            //执行更新项目表冗余字段
            projectService.saveOrUpdateProjectRedundantFieldDealWith(agentProject.getProjectId(),false);
            commonSectionService.saveOrUpdateProjectPackageFieldDealWith(agentProject.getProjectId());
            // 无包项目同步修改标段委托金额 add by fanqh on 2022-05-31
            List<ProjectBidSectionDTO> list = bidSectionService.getProjectBidSectionByProjectId(agentProject.getProjectId());
            if (SegmentEnum.NO_BAG.getType().equals(project.getPackageSegmentStatus()) && project.getEntrustedAmount() != null
                    && list.size() == 1) {
                bidSectionService.update(new UpdateWrapper<ProjectBidSection>().set("entrust_money", project.getEntrustedAmount())
                        .set("entrust_currency", project.getEntrustedCurrency())
                        .eq("id", list.get(0).getId()));
            }

        }
        return flag;
    }




    // 更新招标人权限
    private void updateTendereePower(ProjectEntrustUserREQ projectEntrustUser,Integer projectSource){
        List<AgentUserReq> agentUser = projectEntrustUser.getAgentUser();
        if(null != agentUser && agentUser.size() > 0){
            // 删除关系重新维护
//            LambdaQueryWrapper<ProjectTendererPower> lqw = new LambdaQueryWrapper<>();
//            lqw.eq(ProjectTendererPower::getProjectId,projectEntrustUser.getProjectId());
//            projectTendererPowerService.remove(lqw);
            projectTendererPowerService.updateTetetendereeByAgency(projectEntrustUser.getProjectId());
            for (AgentUserReq agentUserReq : agentUser) {
                if(ProjectSourceEnum.AGENCY.getType().equals(agentUserReq.getSource())){
                    ProjectTendererPower ptp = new ProjectTendererPower();
                    ptp.setProjectId(projectEntrustUser.getProjectId());
                    ptp.setUserId(agentUserReq.getUserId());
                    ptp.setUserName(agentUserReq.getUserName());
                    ptp.setIsAdmin(agentUserReq.getIsAdmin());
                    ptp.setUserPhone(agentUserReq.getUserPhone());
                    ptp.setRoleName(agentUserReq.getRoleName());
                    projectTendererPowerService.save(ptp);
                }
            }
        }

    }



    /**
     * 修改项目信息
     *
     * @param agentProject
     * @return
     */
    @Override
    public Boolean updateProject(AgentProjectReq agentProject, JwtUser user) {
        Project oldProject = this.getBaseMapper().selectById(agentProject.getProjectId());
        //执行标识
        Boolean flag = false;
        //修改项目
        Project project = new Project();
        BeanUtil.copyProperties(agentProject, project);
        project.setId(agentProject.getProjectId());
        project.setStatus(ProjectStatusEnum.GOING.getValue());
        // 招标人新建项目没有委托编号，需要在修改时补充
        if(null == oldProject.getPurchaseNumber() || "".equals(oldProject.getPurchaseNumber())){
            //获取采购项目编号
            project.setPurchaseNumber(projectSerialNumService.generateProjectNum(agentProject.getProvinceInOut(), agentProject.getAgentDepartId()));
        }

        flag = this.updateById(project);
        if (agentProject.getAgentDepartId() == null) {
            agentProject.setAgentDepartId(user.getUser().getDepartId());
        }
        if (flag) {
            //修改委托项目编号信息
            flag = updateProjectEntr(agentProject);
        }
        if (flag) {
            //保存委托关系
            flag = saveProjectEntrustUser(agentProject, agentProject.getProjectId());
        }
        if (flag) {
            //执行代理机构信息入库操作
            flag = lastAgentRecord(agentProject, user);
        }
        if (flag) {
            //执行更新项目表冗余字段
            projectService.saveOrUpdateProjectRedundantFieldDealWith(agentProject.getProjectId(),false);
            commonSectionService.saveOrUpdateProjectPackageFieldDealWith(agentProject.getProjectId());
        }
        if(flag){
            LambdaQueryWrapper<ProjectAgentApproval> lqw = new LambdaQueryWrapper();
            lqw.eq(ProjectAgentApproval::getProjectId,agentProject.getProjectId());
            ProjectAgentApproval one = projectAgentApprovalService.getOne(lqw);
            if(BeanUtil.isNotEmpty(one) && ProjectTendereeStatusEnum.XMJL_ACCEPTED.getCode().equals(one.getStatus())){
                one.setStatus(ProjectTendereeStatusEnum.XMJL_PERFECT.getCode());
                projectAgentApprovalService.saveOrUpdate(one);
            }
        }
        return flag;
    }

    /**
     * 执行委托人项目编号入库
     *
     * @param agentProject
     * @param project
     */
    private Boolean addAgentProjectEntrPersNum(AgentProjectReq agentProject, Project project) {
        List<ProjectEntrPersNum> list = null;
        List<EntrustProjectReq> projectEntrPeersNums = agentProject.getEntrustProjectNumList();
        //删除上一次委托编号
        LambdaUpdateWrapper<ProjectEntrPersNum> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.eq(ProjectEntrPersNum::getProjectId, project.getId());
        projectEntrPersNumService.remove(lambdaUpdateWrapper);
        list = BeanListUtil.convertList(projectEntrPeersNums, ProjectEntrPersNum.class);
        //委托编号不存在时new个委托编号对象
        if (CollectionUtils.isEmpty(list)) {
            list = new ArrayList<>();
            list.add(new ProjectEntrPersNum());
        }

        list.forEach(p -> p.setProjectId(project.getId()));
        /*
         委托人项目编号没有的话，不要自动生成
        String numberCode = StrUtil.toString(System.currentTimeMillis()).substring(UserConstants.PHONE_AFTER);
        list.stream().forEach(p -> p.setProjectNumber(StringUtils.isBlank(p.getProjectNumber()) ? "WT" + numberCode : p.getProjectNumber()));
         */
        return projectEntrPersNumService.saveBatch(list);
    }

    /**
     * 存储用户新增时增加的代理机构信息
     *
     * @param agentProject
     */
    private Boolean lastAgentRecord(AgentProjectReq agentProject, JwtUser user) {
        UserAgentRecord record = BeanListUtil.convert(agentProject, UserAgentRecord.class);
        record.setUserIdentity(CompanyEnum.AGENCY.getType());
        record.setUserId(user.getUserId());
        return userAgentRecordService.save(record);
    }

    /**
     * 新增项目
     *
     * @param agentProject 参数
     * @param project      返回对象
     * @return
     */
    private Boolean addProject(AgentProjectReq agentProject, Project project) {
        BeanUtil.copyProperties(agentProject, project);
        //判断角色类型 代理机构新增项目直接生成采购项目 ,招标人在代理机构接收委托生成
        //获取采购项目编号
        project.setPurchaseNumber(projectSerialNumService.generateProjectNum(agentProject.getProvinceInOut(), agentProject.getAgentDepartId()));
        project.setStatus(ProjectStatusEnum.GOING.getValue());
        return this.save(project);
    }


    /**
     * 保存项目委托关系 （项目委托关系人表）
     *
     * @param agentProject
     */
    private Boolean saveProjectEntrustUser(AgentProjectReq agentProject, Long projectId) {
        List<ProjectEntrustUser> users = new ArrayList<ProjectEntrustUser>();
        ProjectEntrustUser euler = new ProjectEntrustUser();
        euler.setUserId(agentProject.getAgentChargeId());
        euler.setCompanyId(agentProject.getAgentId());
        euler.setType(EntrustUserType.ENTRUSTED_PERSON.getType());
        euler.setProjectId(projectId);
        euler.setDepartmentId(agentProject.getAgentDepartId());
        users.add(euler);
        LambdaUpdateWrapper<ProjectEntrustUser> up = new LambdaUpdateWrapper<ProjectEntrustUser>();
        up.eq(ProjectEntrustUser::getProjectId, agentProject.getProjectId());
        up.eq(ProjectEntrustUser::getUserId, agentProject.getAgentChargeId());
        projectEntrustUserService.remove(up);
        //新增被委托人信息
        return projectEntrustUserService.saveBatch(users);
    }


    /**
     * 代理机构修改项目信息
     *
     * @param agentProject
     */
    private Boolean updateProjectByAgent(AgentProjectReq agentProject) {
        Project projectById = projectService.getProjectById(agentProject.getProjectId());
        Project project = new Project();
        project.setIsInternational(agentProject.getIsInternational());
        //国际标自动生成
        if(agentProject.getIsInternational().equals(CommonConstants.YES)){
            // 存在国际标编号 不处理
            if(null == projectById.getInternationalNumber() || "".equals(projectById.getInternationalNumber())){
                //国际标生成规则
                //国际标项目编号生成规则：年份后两位（22）+部门编号（比如021）+四位自增数字。示例：220210001
                //查询最新的临时国际标编号
                Integer internationalCode = 1;
                Integer maxTempInternational = projectService.getMaxTempInternational();
                if(maxTempInternational != 0){
                    internationalCode = maxTempInternational + 1;
                }
                String internationalNumber = projectSerialNumService.getInternationalNumber(LoginUtil.getJwtUser().getUserId(), internationalCode);
                project.setInternationalNumber(internationalNumber);
                project.setTempInternational(internationalCode);
            }
        }else{
            project.setInternationalNumber("");
            project.setTempInternational(0);
        }

        project.setProjectName(agentProject.getProjectName());
        project.setPurchaseName(agentProject.getPurchaseName());
        project.setOperationFlow(agentProject.getOperationFlow());
        project.setIsProcess(agentProject.getIsProcess());
        project.setProcessAddress(agentProject.getProcessAddress());
        project.setProcessCode(agentProject.getProcessCode());
        project.setProjectApprovalFileName(agentProject.getProjectApprovalFileName());
        project.setProjectApprovalNumber(agentProject.getProjectApprovalNumber());
        project.setEntrustedAmount(agentProject.getEntrustedAmount());
        project.setEntrustedCurrency(agentProject.getEntrustedCurrency());
        project.setProjectApprovalUnit(agentProject.getProjectApprovalUnit());
        project.setId(agentProject.getProjectId());
        project.setIsShare(agentProject.getIsShare());
        //记账部门
        project.setAccountDepartmentCode(agentProject.getAccountDepartmentCode());
        project.setAccountDepartmentName(agentProject.getAccountDepartmentName());
        //暂存不修改数据状态
       // if (OperationTypeEnum.SUBMIT.getType().equals(agentProject.getOperationType())) {
            //修改项目状态  只有在空或者0时修改为2 其他事状态是不动
//            Project entity = this.getById(agentProject.getProjectId());
            //if (StringUtils.isBlank(entity.getProjectStatus()) || "0".equals(entity.getProjectStatusCode())) {
        //    project.setStatus(ProjectStatusEnum.FINISH.getValue());

            //  }
       // }
        return this.updateById(project);
    }

    /**
     * 修改时委托项目编号存取先删后存
     *
     * @param agentProject
     */
    private Boolean updateProjectEntr(AgentProjectReq agentProject) {
        //执行委托项目入库 先删除后新增
//        List<ProjectEntrPersNum> list = new ArrayList<>();
        LambdaUpdateWrapper<ProjectEntrPersNum> up = new LambdaUpdateWrapper<>();
        up.eq(ProjectEntrPersNum::getProjectId, agentProject.getProjectId());
        projectEntrPersNumService.remove(up);
        List<EntrustProjectReq> projectEntrPersNum = agentProject.getEntrustProjectNumList();
        List<ProjectEntrPersNum> list = BeanListUtil.convertList(projectEntrPersNum, ProjectEntrPersNum.class);
        return projectEntrPersNumService.saveBatch(list);
    }

    /**
     * 获取上一次用户填写的代理机构信息
     *
     * @return
     */
    @Override
    public UserAgentRecordDTO selectAgent(JwtUser user) {
//        UserAgentRecordDTO dto = null;
//        LambdaQueryWrapper<UserAgentRecord> userAgent = new LambdaQueryWrapper<UserAgentRecord>();
//        userAgent.eq(UserAgentRecord::getUserId, user.getUserId());
//        userAgent.orderByDesc(UserAgentRecord::getCreatedTime);
//        val list = userAgentRecordService.list(userAgent);
//        //非空
//        if (!CollectionUtils.isEmpty(list)) {
//            UserAgentRecord record = list.get(0);
//            dto = BeanListUtil.convert(record, UserAgentRecordDTO.class);
//        }
        return userAgentRecordService.getByUserId(CompanyEnum.AGENCY.getType(), user.getUserId());
    }


    /**
     *  根据条件分页查询 被委托人 项目表 列表
     *
     * @param condition 查询条件
     * @param userId
     * @return 分页数据
     */
    @Override
    public IPage<ProjectListVO> queryProjectPageListByUserId(ProjectConditionRep condition, Long userId) {
        condition.setUserId(userId);
        condition.setUserDepartId(SecurityUtils.getJwtUser().getUser().getDepartId());
        condition.setType(EntrustUserType.ENTRUSTED_PERSON.getType());
        // 处理数据权限
        String datascopesql=DataScopeUtil.getProjectDataScopeFilterDepartSql("tpe","tpe","p");
        condition.setDataScope(datascopesql);
        if(StringUtils.isNotBlank(condition.getAddressCity()) && condition.getAddressCity().equals("0")){
            condition.setAddressCity("");
        }
        if(StringUtils.isNotBlank(condition.getAddressProvince()) && condition.getAddressProvince().equals("0")){
            condition.setAddressProvince("");
        }
        IPage<ProjectListDTO> page=this.baseMapper.queryProjectByPage(condition.customizeBuildPage(), condition);
//        log.info("全部项目列表：page.getRecord:"+ JsonUtils.toJsonString(page.getRecords()));
        List<ProjectListDTO> records = new ArrayList<>();
        for (ProjectListDTO item : page.getRecords()) {
            List<String> submitEndTime = new ArrayList<>();
            QueryWrapper<ProjectBidSection> queryWrapper=new QueryWrapper<>();
            queryWrapper.lambda().eq(ProjectBidSection::getProjectId,item.getId())
                                 .eq(ProjectBidSection::getIsDelete, CommonConstants.NO)
                                 .orderByAsc(ProjectBidSection::getPackageNumber);
            List<ProjectBidSection> list = bidSectionService.list(queryWrapper);
            for (ProjectBidSection section : list) {
                if (null != section.getSubmitEndTime()) {
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    submitEndTime.add(sdf.format(section.getSubmitEndTime()));
                } else {
                    submitEndTime.add("-");
                }
            }
            item.setSubmitEndTime(submitEndTime);
            ApplyInfoConditionReq applyInfoConditionReq=new ApplyInfoConditionReq();
            applyInfoConditionReq.setProjectId(item.getId());
         //   List<WaitListVo> waitListVoIPage = projectMapper.selectWaitList(applyInfoConditionReq, datascopesql);
          //  Integer applyInfoNum = waitListVoIPage.size();
            //Integer applyInfoNum = projectMapper.countApplyInfoByProjectId(item.getId());//统计已报名待审核供应商数量
           // item.setApplyInfoNum(applyInfoNum);
            List<String> strBidRound= list.stream().map(p->p.getBidRound()+"").collect(Collectors.toList());
            item.setPackagesBidRound(strBidRound);
            //包件数量，0-无包，1-有
            if(item.getPackageSegmentStatus().equals(SegmentEnum.HAVE_BAG.getType().toString()) ){
                item.setSegmentNum(list.size());
            }else{
                item.setSegmentNum(0);
                // 根据项目项目id查询标段信息
                LambdaQueryWrapper<ProjectBidSection> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                lambdaQueryWrapper.eq(ProjectBidSection::getProjectId,item.getId());
                lambdaQueryWrapper.eq(ProjectBidSection::getIsDelete,0);
                Integer num = 0 ;
                List<ProjectBidSection> projectBidSections  = bidSectionService.list(lambdaQueryWrapper);
                if(projectBidSections != null && projectBidSections.size() > 0){
                    // 查询已购标数
                      num =  commonApplyInfoService.queryPurchasedBids(item.getId(),projectBidSections.get(0).getBidRound());
                }else {
                     num = commonApplyInfoService.queryPurchasedBids(item.getId(),2);
                }

               item.setPurchasedBids(num);
            }

            List<String> strBidrondOne = new ArrayList<>();
            List<String> strBidrondTwo = new ArrayList<>();
            list.forEach(p->{
                String bidroundOne = "";
                String bidroundTwo = "";
                //正常状态取status的值
                if(p.getAbnormalStatus() != null && p.getAbnormalStatus() == 0){
                    bidroundOne = p.getStatus();
                    bidroundTwo = p.getStatus();
                }else{
                    //异常状态取之前的值
                    bidroundOne = p.getFormerAbnormalStatus();
                    bidroundTwo = p.getFormerAbnormalStatus();
                }
                //第一轮标段状态拼接
                if(p.getBidRound().equals(BidRoundEnum.ZGYS.getType())){
                    strBidrondOne.add(bidroundOne);
                }

                //第二轮标段状态拼接
                if(p.getBidRound().equals(BidRoundEnum.HS.getType())){
                    strBidrondTwo.add(bidroundTwo);
                }
            });
            item.setPackagesBidRoundOne(strBidrondOne);
            item.setPackagesBidRoundTwo(strBidrondTwo);
            records.add(item);
        }
//        Map<Long,ProjectListDTO> map = new HashMap<>();
//        if(records.size() > 0){
//            for(ProjectListDTO p : records){
//                ProjectListDTO projectListDTOMap = map.get(p.getId());
//                if(projectListDTOMap == null){
//                    map.put(p.getId(),p);
//                }else{
//                    if(projectListDTOMap.getUserId().longValue() == userId.longValue()){
//                        map.put(p.getId(),p);
//                    }
//                }
//            }
//        }
//        records = new ArrayList<>(map.values());
//        page.setRecords(records);
//        page.setTotal(Long.valueOf(""+records.size()));


        IPage<ProjectListVO> voiPage = new Page<>();
        voiPage.setTotal(page.getTotal());
        voiPage.setSize(page.getSize());
        voiPage.setCurrent(page.getCurrent());
        voiPage.setPages(page.getPages());
        // DTO -- >VO
        List<ProjectListVO> vos = dTOChangeVo(page.getRecords());
        voiPage.setRecords(vos);
        return voiPage;
    }

    @Override
    public IPage<ApplyInfoListDTO> queryApplyInfoByUserId(ApplyInfoConditionReq condition, Long userId) {
        String datascopesql=DataScopeUtil.getProjectDataScopeFilterDepartSql("tpe","tpe","p");
        condition.setDataScope(datascopesql);
        IPage<ApplyInfoListDTO> page=this.baseMapper.queryApplyInfoByUserId(condition.customizeBuildPage(), condition,userId);
        List<ApplyInfoListDTO> records = new ArrayList<>();
        for (ApplyInfoListDTO item : page.getRecords()) {
            QueryWrapper<ProjectBidSection> queryWrapper=new QueryWrapper<>();
            queryWrapper.lambda().eq(ProjectBidSection::getProjectId,item.getProjectId())
                    .eq(ProjectBidSection::getIsDelete, CommonConstants.NO)
                    .orderByAsc(ProjectBidSection::getPackageNumber);
            List<ProjectBidSection> list = bidSectionService.list(queryWrapper);

//            List<String> strBidRound= list.stream().map(p->p.getBidRound()+"").collect(Collectors.toList());
            List<String> strPurchaseMode= list.stream().map(p->p.getPurchaseMode()+"").collect(Collectors.toList());
//            item.setBidRound(strBidRound);
            item.setPurchaseMode(strPurchaseMode);
            //包件数量，0-无包，1-有
//            if(item.getPackageSegmentStatus().equals(SegmentEnum.HAVE_BAG.getType().toString()) ){
//                item.setSegmentNum(list.size());
//            }else{
//                item.setSegmentNum(0);
//            }

            List<String> strBidrondOne = new ArrayList<>();
            List<String> strBidrondTwo = new ArrayList<>();
            list.forEach(p->{
                String bidroundOne = "";
                String bidroundTwo = "";
                //正常状态取status的值
                if(p.getAbnormalStatus() != null && p.getAbnormalStatus() == 0){
                    bidroundOne = p.getStatus();
                    bidroundTwo = p.getStatus();
                }else{
                    //异常状态取之前的值
                    bidroundOne = p.getFormerAbnormalStatus();
                    bidroundTwo = p.getFormerAbnormalStatus();
                }
                //第一轮标段状态拼接
                if(p.getBidRound().equals(BidRoundEnum.ZGYS.getType())){
                    strBidrondOne.add(bidroundOne);
                }

                //第二轮标段状态拼接
                if(p.getBidRound().equals(BidRoundEnum.HS.getType())){
                    strBidrondTwo.add(bidroundTwo);
                }
            });
            item.setPackagesBidRoundOne(strBidrondOne);
            item.setPackagesBidRoundTwo(strBidrondTwo);
            records.add(item);
        }
        page.setRecords(records);
        return page;
    }

    @Override
    public Boolean updateContract(EntrustContractREQ req) {
        Project project = new Project();
        project.setId(req.getProjectId());
        project.setUploadFileId(req.getUploadFileId());

        //根据项目id修改委托合同id
        int i = projectMapper.updateById(project);
        if (i > 0){
            return true;
        } else {
            return false;
        }
    }

    /**
     * 查询代办事项列表
     * @param condition
     * @return
     */
    @Override
    public IPage<WaitListVo> queryWaitList(ApplyInfoConditionReq condition, JwtUser jwtUser) {
        condition.setUserId(jwtUser.getUserId());
        //condition.setUserDepartId(jwtUser.getUser().getDepartId());
        // 处理数据权限
        String datascopesql= DataScopeUtil.getUserDataScopeFilterDepartSql("v");
        if (condition.getType() != null) {
            condition.setType((WaitListTypeEnum.all_info.getCode().equals(condition.getType()) || "0".equals(condition.getType()))?null:condition.getType());
        }
        Page<WaitListVo> page = condition.buildPage();
        IPage<WaitListVo> list = this.baseMapper.queryWaitList1(page,condition,datascopesql);
       list.getRecords().forEach(p->{
           //待归档的数据， 把状态返出去
            if(WaitListTypeEnum.archive_dispose.getCode().equals(p.getType())){
                p.setArchiveDisposeStatus(p.getOrderNum());
                p.setOrderNum(2);
            }
        });
        return list;
    }

    /**
     * 查询代办事项数量统计
      * @param condition
     * @return
     */
    @Override
    public List<WaitListVo> queryWaitListCount(ApplyInfoConditionReq condition) {
        String keyCache="queryWaitListCount_"+LoginUtil.getJwtUser().getUserId()+ JSON.toJSONString(condition);
        Object reJson= redisCache.getCacheObject(keyCache);
        List<WaitListVo> waitCounts=null;
        if (reJson!=null){
            waitCounts = JSONUtil.toList(JSONUtil.toJsonStr(reJson),WaitListVo.class);
        }else {
            condition.setUserId(LoginUtil.getJwtUser().getUserId());
            condition.setUserDepartId(LoginUtil.getJwtUser().getUser().getDepartId());
            // 处理数据权限
            String datascopesql= DataScopeUtil.getUserDataScopeFilterDepartSql("v");
            waitCounts = this.baseMapper.findCount1(condition, datascopesql);
            redisCache.setCacheObject(keyCache,JSONUtil.toJsonStr(waitCounts),30, TimeUnit.SECONDS);
        }

        return waitCounts;
    }


    /**
     * @Description: dto-vo
     * @Param: [records]
     * @return: java.util.List<com.hzw.sunflower.controller.response.ProjectListVO>
     * @Author: tsy
     * @Date: 2021-04-28
     */
    private List<ProjectListVO> dTOChangeVo(List<ProjectListDTO> records) {
        List<ProjectListVO> vos = new ArrayList<>();
        for (int i = 0; i < records.size(); i++) {
            ProjectListVO projectListVO = new ProjectListVO();
            ProjectListDTO projectListDTO = records.get(i);

            // 项目异常,先检查项目是否异常，项目异常则显示异常，否则展示包件状态（存在有包无包）
            int status = -1;
            if(null != projectListDTO.getStatus()){
                status = Integer.parseInt(projectListDTO.getStatus());
            }
            Map<String, String> map = new HashMap<>();
            if (status == ProjectStatusEnum.SUSPEND.getValue() || status == ProjectStatusEnum.REFUSED.getValue()
                    || status == ProjectStatusEnum.END.getValue() || status == ProjectStatusEnum.ALL_PACKAGES_ABNORMAL.getValue()) {
                map.put("status", MessageConstants.PROJRCT_ABNORMAL);
                map.put("errMsg", projectListDTO.getRefuseReason());
                projectListVO.setProjectStatus(map);
            } else {
                // 无包
                if (SegmentEnum.NO_BAG.getType() == Integer.parseInt(projectListDTO.getPackageSegmentStatus())) {
                    map.put("status", projectListDTO.getPackagesStatus());
                    projectListVO.setProjectStatus(map);
                    projectListVO.setPackagesStatus(Arrays.asList(projectListDTO.getPackagesStatus()));
                    projectListVO.setPackagesStatusCode(Arrays.asList(projectListDTO.getPackagesStatusCode()));
                }
                // 有包
                if (SegmentEnum.HAVE_BAG.getType() == Integer.parseInt(projectListDTO.getPackageSegmentStatus())) {
                    List<String> packagesStatus = Arrays.asList(projectListDTO.getPackagesStatus().split(GeneralConstants.SPLICING_PARAMETER_METHOD));
                    projectListVO.setPackagesStatus(packagesStatus);
                    if (projectListDTO.getPackagesStatusCode() != null) {
                        List<String> packagesStatusCode = Arrays.asList(projectListDTO.getPackagesStatusCode().split(GeneralConstants.SPLICING_PARAMETER_METHOD));
                        projectListVO.setPackagesStatusCode(packagesStatusCode);
                    }
                }
            }

            // 待接受委托
            if (status == ProjectStatusEnum.WAIT_ACCEPT.getValue()) {
                projectListVO.setAcceptStatus(false);
            } else {
                projectListVO.setAcceptStatus(true);
            }

            projectListVO.setAgentCompany(projectListDTO.getAgentCompany());
            // 项目编号非必填，可能为空
            String projectNumber = projectListDTO.getProjectNumber();
            if (StringUtils.isNotEmpty(projectNumber)) {
                List<String> projectNumbers = Arrays.asList(projectListDTO.getProjectNumber().split(GeneralConstants.SPLICING_PARAMETER_METHOD));
                projectListVO.setProjectNumber(projectNumbers);
            }
            List<String> agentChargeName = Arrays.asList(projectListDTO.getAgentChargeName().split(GeneralConstants.SPLICING_PARAMETER_METHOD));
            projectListVO.setAgentChargeName(agentChargeName);
            projectListVO.setSegmentNum(projectListDTO.getSegmentNum());
            projectListVO.setCreatedTime(projectListDTO.getCreatedTime());
            projectListVO.setApplyInfoNum(projectListDTO.getApplyInfoNum());
            projectListVO.setId(projectListDTO.getId());
            projectListVO.setProjectName(projectListDTO.getProjectName());
            projectListVO.setPurchaseName(projectListDTO.getPurchaseName());
            projectListVO.setUserId(projectListDTO.getUserId());
            projectListVO.setDepartmentId(projectListDTO.getDepartmentId());
            // 委托负责人可能为空
            if (StringUtils.isNotEmpty(projectListDTO.getPrincipalChargeName())) {
                List<String> principalChargeName = Arrays.asList(projectListDTO.getPrincipalChargeName().split(GeneralConstants.SPLICING_PARAMETER_METHOD));
                projectListVO.setPrincipalChargeName(principalChargeName);
                projectListVO.setPrincipalCompany(projectListDTO.getPrincipalCompany());
            }
            projectListVO.setPurchaseNumber(projectListDTO.getPurchaseNumber());
            projectListVO.setStatus(projectListDTO.getStatus());
            projectListVO.setBaseProjectId(projectListDTO.getBaseProjectId());
            projectListVO.setReTender(projectListDTO.getReTender());
            projectListVO.setPackagesBidRound(projectListDTO.getPackagesBidRound());
            projectListVO.setPackagesBidRoundOne(projectListDTO.getPackagesBidRoundOne());
            projectListVO.setPackagesBidRoundTwo(projectListDTO.getPackagesBidRoundTwo());
            projectListVO.setSubmitEndTime(projectListDTO.getSubmitEndTime());
            projectListVO.setOperationFlow(projectListDTO.getOperationFlow());
            projectListVO.setPurchasedBids(projectListDTO.getPurchasedBids());
            projectListVO.setDelegateStatus(null != projectListDTO.getDelegateStatus()?projectListDTO.getDelegateStatus():null);
            vos.add(projectListVO);
        }
        return vos;
    }

    @Override
    public IPage<MyProjectListDTO> queryMyProjectPageListByUserId(AgentMyProjectREQ condition, Long userId) {
        Page<MyProjectListDTO> page=new PageDTO();
        page.setCurrent(condition.getPage());
        page.setSize(condition.getPageSize());
        condition.setUserId(userId);
        condition.setSectionSatatus(PackageStatusEnum.ARCHIVING.getValue());
        condition.setType(EntrustUserType.ENTRUSTED_PERSON.getType());
        // 处理数据权限
        String datascopesql=DataScopeUtil.getProjectDataScopeFilterDepartSql("tpe","tpe","p");
        condition.setDatascope(datascopesql);
        IPage<MyProjectListDTO> myProjectListDTOIPage = projectMapper.queryListMyProjectByPage(page, condition);
        return myProjectListDTOIPage;
    }

    @Override
    public IPage<MyProjectListDTO> queryOnlyMyProjectPageListByUserId(AgentMyProjectREQ condition, Long userId) {
        Page<MyProjectListDTO> page=new PageDTO();
        page.setCurrent(1);
        page.setSize(2000);
        condition.setUserId(userId);
        condition.setUserDepartId(SecurityUtils.getJwtUser().getUser().getDepartId());
        condition.setType(EntrustUserType.ENTRUSTED_PERSON.getType());
        IPage<MyProjectListDTO> myProjectListDTOIPage = projectMapper.queryListOnlyMyProjectByPage(page, condition);
        return myProjectListDTOIPage;
    }

    @Override
    public Boolean updateProjectEntrust(ProjectEntrustREQ projectEntrust) {
        Long id = projectEntrust.getId();
        Project project = new Project();
        //bean赋值
        BeanUtils.copyProperties(projectEntrust, project);
        //接收委托项目编号赋值
        if (!ProjectStatusEnum.REFUSED.getValue().equals(projectEntrust.getStatus())) {
            project.setPurchaseNumber(projectSerialNumService.generateProjectNum(projectEntrust.getProvinceInOut(),null));
        }
        Boolean bool = this.updateById(project);
        if (bool) {
            //处理冗余字段
            commonSectionService.saveOrUpdateProjectPackageFieldDealWith(id);
            //更新委托人关系表
            JwtUser jwtUser = SecurityUtils.getJwtUser();
            LambdaQueryWrapper<ProjectEntrustUser> projectEntrustUserLambdaQueryWrapper = new LambdaQueryWrapper<>();
            projectEntrustUserLambdaQueryWrapper.eq(ProjectEntrustUser::getProjectId,project.getId());
            projectEntrustUserLambdaQueryWrapper.eq(ProjectEntrustUser::getUserId,jwtUser.getUserId());
            projectEntrustUserLambdaQueryWrapper.eq(ProjectEntrustUser::getType,EntrustUserType.ENTRUSTED_PERSON.getType());
            projectEntrustUserLambdaQueryWrapper.last("limit 1");
            ProjectEntrustUser projectEntrustUserServiceOne = projectEntrustUserService.getOne(projectEntrustUserLambdaQueryWrapper);
            projectEntrustUserServiceOne.setDepartmentId(jwtUser.getUser().getDepartId());
            projectEntrustUserService.updateById(projectEntrustUserServiceOne);
        }
        return bool;
    }

    @Override
    public List<ProjectVo> getProjectListByExpert(String keyWords) {
        // 处理数据权限
        String datascopesql=DataScopeUtil.getProjectDataScopeFilterDepartSql("tpe","tpe","p");
        return projectMapper.getProjectListByExpert(keyWords,datascopesql);
    }


//    @Override
//    public IPage<WaitListVo> queryReturnList(ApplyInfoConditionReq condition, JwtUser jwtUser) {
//        String datascopesql=DataScopeUtil.getProjectDataScopeFilterDepartSql("tpe","tpe","p");
//        condition.setType((ReturnListEnum.ALL_INFO.getCode().equals(condition.getType()) || "0".equals(condition.getType()))?null:condition.getType());
//        Page<WaitListVo> page = condition.buildPage();
//        IPage<WaitListVo> list = this.baseMapper.queryReturnList(page,condition,datascopesql,jwtUser.getUserId());
//        return list;
//    }

//    @Override
//    public List<WaitListVo> queryReturnListCount(ApplyInfoConditionReq condition, JwtUser jwtUser) {
//        String datascopesql=DataScopeUtil.getProjectDataScopeFilterDepartSql("tpe","tpe","p");
//        List<WaitListVo> count = this.baseMapper.findReturnCount(condition,datascopesql,jwtUser.getUserId());
//        WaitListVo waitListVo = new WaitListVo();
//        waitListVo.setType(ReturnListEnum.ALL_INFO.getCode());
//        if(!count.isEmpty()){
//            Integer integer = count.stream().map(e -> e.getCountNum()).reduce(Integer::sum).get();
//            waitListVo.setCountNum(integer);
//            count.add(waitListVo);
//        }
//        return count;
//    }

    @Override
    public IPage<ProjectListVO> listAllFinishedBid(ProjectConditionRep condition, Long userId) {
        condition.setUserId(userId);
        condition.setUserDepartId(SecurityUtils.getJwtUser().getUser().getDepartId());
        condition.setType(EntrustUserType.ENTRUSTED_PERSON.getType());
        //查询所有已经归档的项目数据列表
        condition.setSearchType(3);
        if(StringUtils.isNotBlank(condition.getAddressCity()) && condition.getAddressCity().equals("0")){
            condition.setAddressCity("");
        }
        if(StringUtils.isNotBlank(condition.getAddressProvince()) && condition.getAddressProvince().equals("0")){
            condition.setAddressProvince("");
        }
        IPage<ProjectListDTO> page=this.baseMapper.queryProjectByPage(condition.customizeBuildPage(), condition);
        log.info("全部项目列表：page.getRecord:"+ JsonUtils.toJsonString(page.getRecords()));
        List<ProjectListDTO> records = new ArrayList<>();
        for (ProjectListDTO item : page.getRecords()) {
            List<String> submitEndTime = new ArrayList<>();
            QueryWrapper<ProjectBidSection> queryWrapper=new QueryWrapper<>();
            queryWrapper.lambda().eq(ProjectBidSection::getProjectId,item.getId())
                    .eq(ProjectBidSection::getIsDelete, CommonConstants.NO)
                    .orderByAsc(ProjectBidSection::getPackageNumber);
            List<ProjectBidSection> list = bidSectionService.list(queryWrapper);
            for (ProjectBidSection section : list) {
                if (null != section.getSubmitEndTime()) {
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    submitEndTime.add(sdf.format(section.getSubmitEndTime()));
                } else {
                    submitEndTime.add("-");
                }
            }
            item.setSubmitEndTime(submitEndTime);
            ApplyInfoConditionReq applyInfoConditionReq=new ApplyInfoConditionReq();
            applyInfoConditionReq.setProjectId(item.getId());
            List<String> strBidRound= list.stream().map(p->p.getBidRound()+"").collect(Collectors.toList());
            item.setPackagesBidRound(strBidRound);
            //包件数量，0-无包，1-有
            if(item.getPackageSegmentStatus().equals(SegmentEnum.HAVE_BAG.getType().toString()) ){
                item.setSegmentNum(list.size());
            }else{
                item.setSegmentNum(0);
            }

            List<String> strBidrondOne = new ArrayList<>();
            List<String> strBidrondTwo = new ArrayList<>();
            list.forEach(p->{
                String bidroundOne = "";
                String bidroundTwo = "";
                //正常状态取status的值
                if(p.getAbnormalStatus() != null && p.getAbnormalStatus() == 0){
                    bidroundOne = p.getStatus();
                    bidroundTwo = p.getStatus();
                }else{
                    //异常状态取之前的值
                    bidroundOne = p.getFormerAbnormalStatus();
                    bidroundTwo = p.getFormerAbnormalStatus();
                }
                //第一轮标段状态拼接
                if(p.getBidRound().equals(BidRoundEnum.ZGYS.getType())){
                    strBidrondOne.add(bidroundOne);
                }

                //第二轮标段状态拼接
                if(p.getBidRound().equals(BidRoundEnum.HS.getType())){
                    strBidrondTwo.add(bidroundTwo);
                }
            });
            item.setPackagesBidRoundOne(strBidrondOne);
            item.setPackagesBidRoundTwo(strBidrondTwo);
            records.add(item);
        }
        IPage<ProjectListVO> voiPage = new Page<>();
        voiPage.setTotal(page.getTotal());
        voiPage.setSize(page.getSize());
        voiPage.setCurrent(page.getCurrent());
        voiPage.setPages(page.getPages());
        // DTO -- >VO
        List<ProjectListVO> vos = dTOChangeVo(page.getRecords());
        voiPage.setRecords(vos);
        return voiPage;
    }

    /**
     * 查询项目编号是否存在
     * @param req
     * @return
     */
    @Override
    public Boolean queryProjectNumberExist(ProjectNumberExistReq req) {
        LambdaQueryWrapper<Project> projectLambdaQueryWrapper = new LambdaQueryWrapper<>();
        projectLambdaQueryWrapper.eq(Project::getPurchaseNumber, req.getProjectNumber());
        long count = projectService.count(projectLambdaQueryWrapper);
        return count > 0;
    }

    @Override
    public IPage<MyProjectListDTO> listMySeal(AgentMyProjectREQ condition, Long userId) {
        Page<MyProjectListDTO> page=new PageDTO();
        page.setCurrent(condition.getPage());
        page.setSize(condition.getPageSize());
        condition.setUserId(userId);
        condition.setType(EntrustUserType.ENTRUSTED_PERSON.getType());
        String datascopesql=DataScopeUtil.getProjectDataScopeFilterDepartSql("tpe","tpe","p");
        condition.setDatascope(datascopesql);
        IPage<MyProjectListDTO> myProjectListDTOIPage = projectMapper.queryListSeal(page, condition);
        return myProjectListDTOIPage;
    }


}
