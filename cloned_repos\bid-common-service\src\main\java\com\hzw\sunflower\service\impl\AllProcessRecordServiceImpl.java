package com.hzw.sunflower.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzw.sunflower.common.redis.RedisCache;
import com.hzw.sunflower.dao.AllProcessRecordMapper;
import com.hzw.sunflower.dto.AllProcessRecordDTO;
import com.hzw.sunflower.entity.AllProcessRecord;
import com.hzw.sunflower.entity.User;
import com.hzw.sunflower.service.AllProcessRecordService;
import com.hzw.sunflower.service.CompanyFlowConfigService;
import com.hzw.sunflower.service.UserOpenService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

@Service
public class AllProcessRecordServiceImpl extends ServiceImpl<AllProcessRecordMapper, AllProcessRecord> implements AllProcessRecordService {
    @Autowired
    private RedisCache redisCache;

    @Autowired
    private CompanyFlowConfigService companyFlowConfigService;

    @Autowired
    private UserOpenService userOpenService;

    /**
     * 保存审批记录
     * 开启节点无需处理，审批节点需要单独添加
     * @param userCode
     * @param processDefinitionKey
     * @param businessKey
     * @param approvalNode
     */
    @Override
    public void addRecord(AllProcessRecordDTO dto) {
        String addRecordCodeKey = dto.getProcessDefinitionKey() + "addRecordCode:";
        String code = redisCache.getCacheObject(addRecordCodeKey);
        if(null == code){
            code = companyFlowConfigService.getRecordCode(dto.getProcessDefinitionKey());
            redisCache.setCacheObject(addRecordCodeKey,code,14400000L, TimeUnit.MILLISECONDS);
        }
        AllProcessRecord allProcessRecord = new AllProcessRecord();
        allProcessRecord.setBusinessCode(code);
        allProcessRecord.setBusinessId(dto.getBusinessKey().substring(dto.getBusinessKey().indexOf(":") + 1));
        allProcessRecord.setApprovalNode(dto.getApprovalNode());
        allProcessRecord.setOperation(dto.getOperation());
        allProcessRecord.setRemark(dto.getRemark());
        allProcessRecord.setMessage(dto.getMessage());
        allProcessRecord.setIsJump(dto.getIsJump());
        if(null != dto.getUser()){
            User operator = dto.getUser();
            allProcessRecord.setOperatorId(operator.getId());
            allProcessRecord.setOperatorName(operator.getUserName());
        } else {
            User user = userOpenService.getByOtherId(dto.getUserCode());
            if(null != user){
                allProcessRecord.setOperatorId(user.getId());
                allProcessRecord.setOperatorName(user.getUserName());
            }
        }

        save(allProcessRecord);
    }
}
