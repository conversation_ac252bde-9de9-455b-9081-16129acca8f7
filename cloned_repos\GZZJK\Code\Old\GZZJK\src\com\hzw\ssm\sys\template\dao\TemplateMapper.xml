<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzw.ssm.sys.template.dao.TemplateMapper">
	<resultMap type="TemplateEntity" id="TemplateEntity">
		<result column="TEMPLATE_ID" property="templateId"></result>
		<result column="TEMPLATE_TYPE" property="templateType"></result>
		<result column="TEMPLATE_NAME" property="templateName"></result>
		<result column="TEMPLATE_CONTENT" property="templateContent"></result>
		<result column="DELETE_FLAG" property="delete_flag"></result>
		<result column="CREATE_ID" property="create_id"></result>
		<result column="CREATE_TIME" property="create_time"></result>
		<result column="MODIFY_ID" property="modify_id"></result>
		<result column="MODIFY_TIME" property="modify_time"></result>
		<result column="IS_VALID" property="isValid"></result>
	</resultMap>
	
	<select id="queryTemplateById" resultMap="TemplateEntity" parameterType="TemplateEntity">
		SELECT T.TEMPLATE_ID,T.TEMPLATE_TYPE,T.TEMPLATE_CONTENT,T.TEMPLATE_NAME ,T.IS_VALID
		FROM SYS_MESSAGE_TEMPLATE T
		WHERE T.DELETE_FLAG = 0
		AND T.TEMPLATE_ID = #{templateId}
	</select>
	
	
	<select id="queryTemplateList" resultMap="TemplateEntity" parameterType="TemplateEntity">
		SELECT T.TEMPLATE_ID,T.TEMPLATE_TYPE,T.TEMPLATE_CONTENT,T.TEMPLATE_NAME,T.CREATE_TIME, T.IS_VALID
		FROM SYS_MESSAGE_TEMPLATE T
		WHERE T.DELETE_FLAG = 0
		<if test="isValid !=null">
			AND T.IS_VALID=#{isValid}
		</if>
		 <if test="templateType != null and templateType != ''" >
		 AND T.TEMPLATE_TYPE  = #{templateType}
		 </if>
		 <if test="templateName != null and templateName != ''" >
		 AND T.TEMPLATE_NAME  like '%${templateName}%'
		 </if>
		 order by T.CREATE_TIME DESC
	</select>
	
	<update id="modifyTemplateById" parameterType="TemplateEntity" >
		update SYS_MESSAGE_TEMPLATE 
		<set>
			<if test="templateType != null and templateType != ''" >
				TEMPLATE_TYPE=#{templateType},
			</if>
			<if test="templateContent != null and templateContent != ''" >
				TEMPLATE_CONTENT=#{templateContent},
			</if>
			<if test="templateName != null and templateName != ''" >
				TEMPLATE_NAME=#{templateName},
			</if>
			<if test="delete_flag != null and delete_flag != ''" >
				DELETE_FLAG=#{delete_flag},
			</if>
			<if test="modify_time != null and modify_time != ''" >
				MODIFY_TIME=#{modify_time},
			</if>
			<if test="modify_id != null and modify_id != ''" >
				MODIFY_ID=#{modify_id},
			</if>
			
		</set> 
		WHERE TEMPLATE_ID = #{templateId}
	</update>
	
	<insert id="insertTemplate" parameterType="TemplateEntity">
		<selectKey keyProperty="templateId" resultType="String" order="BEFORE">
	    	 select SEQ_SYS_MESSAGE_TEMPLATE.NEXTVAL as templateId from dual 
	    </selectKey>
		INSERT INTO SYS_MESSAGE_TEMPLATE(
			TEMPLATE_ID,TEMPLATE_TYPE,TEMPLATE_NAME,TEMPLATE_CONTENT,DELETE_FLAG,
			CREATE_ID,CREATE_TIME,MODIFY_ID,MODIFY_TIME,IS_VALID)
		VALUES(
			#{templateId},#{templateType},#{templateName},#{templateContent},0,
			#{create_id},sysdate,
			#{modify_id,jdbcType=VARCHAR},#{modify_time,jdbcType=TIMESTAMP},
			#{isValid,jdbcType=VARCHAR}
		)	
	</insert>
	
	<!--根据模板id删除对应数据  -->
	<delete id="deleteTemplateById" parameterType="String">
		delete from SYS_MESSAGE_TEMPLATE T where T.TEMPLATE_ID=#{templateId}
	</delete>
	
	<!-- 判断是否存在其他生效模板（同一个类型下） -->
	<select id="ajaxCheckIsValid" resultType="java.lang.Integer" parameterType="TemplateEntity">
		SELECT count(1)
		FROM SYS_MESSAGE_TEMPLATE T
		WHERE T.DELETE_FLAG = 0
		<if test="isValid !=null">
			AND T.IS_VALID=#{isValid}
		</if>
		 <if test="templateType != null and templateType != ''" >
		 AND T.TEMPLATE_TYPE  = #{templateType}
		 </if>
		 <if test="templateId != null and templateId != ''" >
		 AND T.TEMPLATE_ID  NOT IN (#{templateId})
		 </if>
		 order by T.CREATE_TIME DESC
	</select>
	
</mapper>