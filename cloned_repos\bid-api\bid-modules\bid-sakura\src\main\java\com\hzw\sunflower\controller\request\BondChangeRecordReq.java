package com.hzw.sunflower.controller.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date ：Created in 2022/7/29 11:10
 * @description：
 * @modified By：`
 * @version: 1.0
 */
@ApiModel(description = "关联页面详情查询入参")
@Data
public class BondChangeRecordReq implements Serializable {

    @ApiModelProperty(value = "标段id")
    private Long sectionId;

    @ApiModelProperty(value = "公司id")
    private Long companyId;

}
