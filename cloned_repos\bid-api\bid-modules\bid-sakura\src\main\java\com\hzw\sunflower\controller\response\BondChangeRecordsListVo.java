package com.hzw.sunflower.controller.response;

import com.hzw.sunflower.entity.BondChangeRecords;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date ：Created in 2022/7/27 11:31
 * @description：调账日志列表返回实体类
 * @modified By：`
 * @version: 1.0
 */
@ApiModel(description = "调账日志列表返回实体类")
@Data
public class BondChangeRecordsListVo extends BondChangeRecords implements Serializable {

    @ApiModelProperty(value = "项目编号")
    private String purchaseNumber;

    @ApiModelProperty(value = "项目名称")
    private String purchaseName;

    @ApiModelProperty(value = "包件数量，0-无包，1-有")
    private String packageSegmentStatus;

    @ApiModelProperty(value = "供应商名称")
    private String companyName;

//    @ApiModelProperty(value = "保证金金额")
//    private BigDecimal totalAmount;

//    @ApiModelProperty(value = "调整时间")
//    private String changeTime;

    @ApiModelProperty(value = "包号")
    private String packageNumber;

    @ApiModelProperty(value = "调整前项目编号")
    private String formerPurchaseNumber;

    @ApiModelProperty(value = "调整前项目名称")
    private String formerPurchaseName;

    @ApiModelProperty(value = "调整前包件数量，0-无包，1-有")
    private String formerPackageSegmentStatus;

    @ApiModelProperty(value = "调整前包号")
    private String formerPackageNumber;

    @ApiModelProperty("调整人")
    private String changeUserName;


}
