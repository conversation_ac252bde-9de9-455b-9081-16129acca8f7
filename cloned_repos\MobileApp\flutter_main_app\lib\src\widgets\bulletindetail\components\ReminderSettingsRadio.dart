///***
/// * 提醒设置的单选组件
/// */

import 'package:flutter/material.dart';

import 'package:flutter_main_app/src/constants/theme.dart';

class ReminderSettingsRadio extends StatelessWidget {

  ReminderSettingsRadio({
    this.text,
    this.richText,
    this.onTap,
    this.onRadioTap,
    this.onTextTap,
    @required this.isSelected,
  }) : assert(text != null || richText != null),
    assert(isSelected != null);

  /// * 文本
  final Text text;

  /// * 富文本
  final RichText richText;

  /// * 不区分点击区域的单选点击回调
  final void Function() onTap;

  /// * 单选图标点击回调
  final void Function() onRadioTap;

  /// * 文本点击回调
  final void Function() onTextTap;

  /// * 是否选中
  final bool isSelected;

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: <Widget>[
        /// * 单选图标
        InkWell(
          onTap: onTap ?? onRadioTap,
          child: Container(
            width: 14.0,
            height: 14.0,
            decoration: BoxDecoration(
              border: Border.all(
                color: themeTipsTextColor,
                width: 0.5,
              ),
              borderRadius: BorderRadius.circular(7.0),
            ),
            child: Container(
              margin: const EdgeInsets.all(4.0),
              width: 4.0,
              height: 4.0,
              decoration: BoxDecoration(
                color: isSelected ? themeActiveColor : Colors.transparent,
                border: Border.all(
                  color: isSelected ? themeActiveColor : themeTipsTextColor,
                  width: 0.5,
                ),
                borderRadius: BorderRadius.circular(4.0),
              ),
            ),
          ),
        ),
        /// * 文本
        InkWell(
          onTap: onTap ?? onTextTap,
          child: Padding(
            padding: const EdgeInsets.only(left: 7.0),
            child: text ?? richText,
          ),
        ),
      ],
    );
  }

}
