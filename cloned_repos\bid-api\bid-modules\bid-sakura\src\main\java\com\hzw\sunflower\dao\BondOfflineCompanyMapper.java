package com.hzw.sunflower.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hzw.sunflower.controller.response.OfflineCompanyVo;
import com.hzw.sunflower.entity.BondOfflineCompany;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;


@Mapper
public interface BondOfflineCompanyMapper  extends BaseMapper<BondOfflineCompany> {

    /**
     * 匹配保证金线下项目供应商表
     * @return
     */
    IPage<OfflineCompanyVo> queryFromBondOfflineCompany(@Param("page") IPage<OfflineCompanyVo> page, @Param("companyName") String companyName);

    /**
     * 匹配企业表
     * @return
     */
    IPage<OfflineCompanyVo> queryFromCompany(@Param("page") IPage<OfflineCompanyVo> page, @Param("companyName") String companyName);
}
