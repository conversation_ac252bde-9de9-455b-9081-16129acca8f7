<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hzw.ssm.applets.dao.TitleInfoMapper">

    <resultMap id="BaseResultMap" type="TitleInfo">
        <id column="id" property="id"/>
        <result column="expert_id" property="expertId"/>
        <result column="title_id" property="titleId"/>
        <result column="get_time" property="getTime"/>
        <result column="picture" property="picture"/>
        <result column="picture_name" property="pictureName"/>
        <result column="delete_flag" property="deleteFlag"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
		 t.id, t.expert_id, t.title_id, t.get_time, t.picture, t.picture_name, t.delete_flag, t.create_time, t.update_time
	</sql>

    <insert id="insertTitleInfo" parameterType="TitleInfo">
		insert into t_title_info (
			id,
			expert_id,
			title_id,
			get_time,
			picture,
			picture_name,
			delete_flag,
			create_time
		)
		values(
			#{id,jdbcType=VARCHAR},
			#{expertId,jdbcType=VARCHAR},
			#{titleId,jdbcType=VARCHAR},
			#{getTime,jdbcType=VARCHAR},
			#{picture,jdbcType=VARCHAR},
			#{pictureName,jdbcType=VARCHAR},
			#{deleteFlag,jdbcType=NUMERIC},
			to_date(to_char(sysdate,'yyyy-mm-dd hh24:mi:ss'),'yyyy-mm-dd hh24:mi:ss')
		)
	</insert>

    <update id="updateTitleInfo" parameterType="TitleInfo">
        update t_title_info
        <set>
            <if test="titleId != null">
                title_id = #{titleId},
            </if>
            <if test="getTime != null">
                get_time = #{getTime},
            </if>
            <if test="picture != null">
                picture = #{picture},
            </if>
            <if test="pictureName != null">
                picture_name = #{pictureName},
            </if>
        </set>
        where
            id = #{id}
    </update>

    <select id="getTitleInfo" parameterType="TitleInfo" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List"/>
        from
            t_title_info t
        where
            id = #{id}
    </select>

    <select id="queryTitleInfo" parameterType="TitleInfo" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List"/>
        from
            t_title_info t
        where
            delete_flag = 0
            <if test="expertId != null and !&quot;&quot;.equals(expertId)">
                and expert_id = #{expertId}
            </if>
            <if test="titleId != null and !&quot;&quot;.equals(titleId)">
                and title_id = #{titleId}
            </if>
    </select>

    <select id="queryTitleInfoNumById" resultType="int">
        select
            count(t.id)
        from
            t_title_info t
        where
            delete_flag = 0
            and expert_id = #{expertId}
    </select>

    <select id="getTitleInfos" parameterType="TitleInfo" resultMap="BaseResultMap">
        select
            (
            floor(
            ( months_between ( TO_DATE ( to_char ( sysdate, 'yyyy-MM-dd HH24:mi:ss' ), 'yyyy-mm-dd hh24:mi:ss' ), TO_DATE ( t.get_time, 'yyyy-mm-dd hh24:mi:ss' ) ) ) / 12
            )
            ) AS titleYears,
            <include refid="Base_Column_List"/>,
            a.EXPERT_TYPE expertType
        from
            t_title_info t
            LEFT JOIN T_TECHNICAL_TITAL a ON a.CODE = t.TITLE_ID
        where
            t.expert_id = #{expertId}
            and t.delete_flag = 0
        ORDER BY
            t.GET_TIME ASC
    </select>

    <select id="queryTitleInfoList" resultMap="BaseResultMap">
        select
            (
            floor(
            ( months_between ( TO_DATE ( to_char ( sysdate, 'yyyy-MM-dd HH24:mi:ss' ), 'yyyy-mm-dd hh24:mi:ss' ), TO_DATE ( t.get_time, 'yyyy-mm-dd hh24:mi:ss' ) ) ) / 12
            )
            ) AS titleYears,
            <include refid="Base_Column_List"/>
        from
            t_title_info t
        where
            t.expert_id = #{expertId}
            and t.delete_flag = 0
        ORDER BY
            t.GET_TIME ASC
    </select>

    <update id="deleteTitleInfo" parameterType="TitleInfo">
		UPDATE
			t_title_info
		SET
			delete_flag = 1
		where
			id = #{id}
	</update>

    <update id="deletePictureById">
      UPDATE
			t_title_info
		SET
			picture = '[]',
			picture_name = null
		where
			id = #{id}
    </update>
</mapper>
