package com.hzw.sunflower.controller.response;

import com.hzw.sunflower.common.OverrideBeanMethods;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.Date;

/**
 * 单位公司 返回实体
 *
 * <AUTHOR>
 * @version 1.0.0 2021-04-12
 */
@ApiModel(description = "人员列表 ")
@Data
public class UserInfoVo extends OverrideBeanMethods {


    /**
     * 企业名称
     */
    private String companyName;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 用户身份证
     */
    private String identityNumber;

    /**
     * 用户状态
     */
    private Integer userStatus;

    /**
     * 用户手机号
     */
    private String userPhone;

    private Date createdTime;

    /**
     * 法人授权函
     */
    private Long legalPersonAuthorization;

    /**
     * 身份审核状态
     */
    private Integer status;

    /**
     * 身份标识
     */
    private Integer identity;

    /**
     * 用户身份id
     */
    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 描述/拒绝原因
     */
    private String remark;
}