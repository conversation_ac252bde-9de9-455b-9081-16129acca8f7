package com.hzw.sunflower.constant.constantenum;


/**
 * 标段code枚举
 *
 * <AUTHOR>
 * @date ：Created in 2021/5/12 10:02
 * @description：标段code枚举
 * @modified By：`
 * @version: 1.0
 */
public enum BidSectionCodeEnum {

    //招标公告
    bidding_announcement_no(220, "招标公告-无"),
    bidding_announcement_save(221, "招标公告-起草中(暂存)"),
    bidding_announcement_submit(222, "招标公告-待确认(提交)"),
    bidding_announcement_withdraw(223, "招标公告-已撤回(撤回)"),
    bidding_announcement_confirm(224, "招标公告-已确认(确认)"),
    bidding_announcement_return(225, "招标公告-已退回(驳回)"),
    bidding_announcement_withdraw_waitfor_confirm(226, "招标公告-撤回待确认(已确认后撤回)"),
    bidding_announcement_release(230, "招标公告-已发布(发布)"),
    //招标文件
    bidding_file_submint(232, "招标文件-待确认(提交)"),
    bidding_file_withdraw(233, "招标文件-已撤回(撤回)"),
    bidding_file_confirm(234, "招标文件-已确认(确认)"),
    bidding_file_return(235, "招标文件-已退回(驳回)"),
    bidding_file_withdraw_waitfor_confirm(236, "招标文件-撤回待确认(已确认后撤回)"),
    bidding_file_release(240, "招标文件-已发布(发布)"),
    //报名情况
    sign_up_situation_before(240,"报名情况-阶段结束前"),
    sign_up_situation_end(250,"报名情况-阶段结束时"),
    //补充公告
    supplement_announcement_no(250,"补充公告-上一阶段结束"),
    supplement_announcement_save(251, "补充公告-起草中(暂存)"),
    supplement_announcement_submit(252, "补充公告-待确认(提交)"),
    supplement_announcement_withdraw(253, "补充公告-已撤回(撤回)"),
    supplement_announcement_confirm(254, "补充公告-已确认(确认)"),
    supplement_announcement_return(255, "补充公告-已退回(驳回)"),
    supplement_announcement_withdraw_waitfor_confirm(256, "补充公告-撤回待确认(已确认后撤回)"),
    supplement_announcement_release(260, "补充公告-已发布(发布)"),
    //澄清答疑
    clarification_before(260,"澄清答疑-阶段结束前"),
    clarification_end(280,"澄清答疑和异议回复阶段结束时"),
    //异议回复
    objection_reply_before(260,"异议回复-阶段结束前"),
    objection_reply_end(280,"澄清答疑和异议回复阶段结束时"),
    //延期公告
    delay_announcement_no(280, "延期公告-上一阶段结束"),
    delay_announcement_save(281, "延期公告-起草中(暂存)"),
    delay_announcement_submit(282, "延期公告-待确认(提交)"),
    delay_announcement_withdraw(283, "延期公告-已撤回(撤回)"),
    delay_announcement_confirm(284, "延期公告-已确认(确认)"),
    delay_announcement_return(285, "延期公告-已退回(驳回)"),
    delay_announcement_withdraw_waitfor_confirm(286, "延期公告-撤回待确认(已确认后撤回)"),
    delay_announcement_release(290, "延期公告-已发布(发布)"),
    //开评标备案
    bid_opening_and_evaluation_submit(291,"开评标备案-提交"),
    bid_opening_and_evaluation_detail(292,"开评标备案-详情（已申请确认中标候选人）"),
    bid_opening_and_evaluation_end(300,"开评标备案-阶段结束时"),
    //确认中标人
    bid_win_tenderer_submit(302,"确认中标人-待确认(提交)"),
    bid_win_tenderer_withdraw(303,"确认中标人-已撤回(撤回)"),
    bid_win_tenderer_confirm(304,"确认中标人-已确认(确认)"),
    bid_win_tenderer_return(305,"确认中标人-已退回(驳回)"),
    bid_win_tenderer_waitfor_confirm(306, "确认中标人-撤回待确认(已确认后撤回)"),
    bid_win_tenderer_release(310, "确认中标人-已发布(发布)"),
    //中标候选人公示
    bid_win_candidate_notice_save(311, "中标候选人公示-起草中(暂存)"),
    bid_win_candidate_notice_submit(312, "中标候选人公示-待确认(提交)"),
    bid_win_candidate_notice_withdraw(313, "中标候选人公示-已撤回(撤回)"),
    bid_win_candidate_notice_confirm(314, "中标候选人公示-已确认(确认)"),
    bid_win_candidate_notice_return(315, "中标候选人公示-已退回(驳回)"),
    bid_win_candidate_notice_withdraw_waitfor_confirm(316, "中标候选人公示-撤回待确认(已确认后撤回)"),
    bid_win_candidate_notice_release(320, "中标候选人公示-已发布(发布)"),
    //中标结果公示
    bid_win_result_notice_save(321, "中标结果公示-起草中(暂存)"),
    bid_win_result_notice_submit(322, "中标结果公示-待确认(提交)"),
    bid_win_result_notice_withdraw(323, "中标结果公示-已撤回(撤回)"),
    bid_win_result_notice_confirm(324, "中标结果公示-已确认(确认)"),
    bid_win_result_notice_return(325, "中标结果公示-已退回(驳回)"),
    bid_win_result_notice_withdraw_waitfor_confirm(326, "中标结果公示-撤回待确认(已确认后撤回)"),
    bid_win_result_notice_release(330, "中标结果公示-已发布(发布)"),
    //申请中标通知书  330-350
    apply_bid_win_notice(340,"申请中标通知书"),
    //归档
    keep_on_file_no(360, "归档-上阶段结束"),
    keep_on_file_save(361, "归档-起草中(暂存)"),
    keep_on_file_submit(362, "归档-待接收-移交"),
    keep_on_file_confirm(364, "归档-已确认(确认)"),
    keep_on_file_return(366, "归档-已退回(驳回)"),
    keep_on_file_withdraw_waitfor_confirm(367, "归档-撤回待确认(已确认后撤回)"),
    keep_on_file_end(368, "归档-结项");
    //异常处理  暂不处理

    /**
     * 类型
     */
    private Integer code;

    /**
     * 说明
     */
    private String msg;

    BidSectionCodeEnum(Integer code, String desc) {
        this.code = code;
        this.msg = desc;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsgc(String msg) {
        this.msg = msg;
    }


    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    /**
     * 获取对应的枚举
     *
     * @param code 种类
     * @return 枚举值
     */
    public static BidSectionCodeEnum getBidSectionCodeEnum(Integer code) {
        try {
            for (BidSectionCodeEnum userStatusEnum : BidSectionCodeEnum.values()) {
                if (userStatusEnum.code.equals(code)) {
                    return userStatusEnum;
                }
            }
            return null;
        } catch (Exception e) {
            return null;
        }
    }
}
