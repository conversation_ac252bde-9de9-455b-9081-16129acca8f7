package com.hzw.ssm.webService.input;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;

import com.hzw.ssm.webService.input.impl.ExtractExpertInput;

/**
 * 输入根节点
 * 
 * <AUTHOR>
 *
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "root")
@XmlType(name = "root", propOrder = { "head", "body"})
public class RootInputE {
	/**
	 * 公共参数部分
	 */
	@XmlElement
	private HeadInput head;
	/**
	 * 核心请求部分
	 */
	@XmlElement(name = "body")
	private ExtractExpertInput body;
	

	public HeadInput getHead() {
		return head;
	}

	public void setHead(HeadInput head) {
		this.head = head;
	}

	public ExtractExpertInput getBody() {
		return body;
	}

	public void setBody(ExtractExpertInput body) {
		this.body = body;
	}

}
