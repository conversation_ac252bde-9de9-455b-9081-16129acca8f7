package com.hzw.sunflower.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hzw.sunflower.constant.constantenum.*;
import com.hzw.sunflower.dao.*;
import com.hzw.sunflower.entity.*;
import com.hzw.sunflower.service.CommonSectionService;
import com.hzw.sunflower.service.PreQualificationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;


@Service(value = "DocPreQualificationServiceImpl")
public class DocPreQualificationServiceImpl implements PreQualificationService {

    @Autowired
    private ProjectBidDocMapper projectBidDocMapper;

    @Autowired
    private ProjectBidDocRelationMapper projectBidDocRelationMapper;

    @Autowired
    private ProjectBidSectionMapper projectBidSectionMapper;

    @Autowired
    private ProjectBidSectionRecordMapper projectBidSectionRecordMapper;

    @Autowired
    private CommonSectionService commonSectionService;

    @Autowired
    private ProjectMapper projectMapper;

    @Autowired
    private ProjectSectionBidOpenMapper projectSectionBidOpenMapper;

    @Autowired
    private ClarifyQuestionMapper clarifyQuestionMapper;


    /**
     * 资格预审按项目完善信息
     *
     * @param projectId
     * @param projectBidSection
     * @return
     */
    @Override
    public Boolean savePreQualificationInfo(Long projectId, ProjectBidSection projectBidSection) {
        Long sectionId = projectBidSection.getId();
        Boolean flag = true;
        //查询是否存在 资格预审 标段
        LambdaQueryWrapper<ProjectBidSectionRecord> bidQueryWrapper = new LambdaQueryWrapper<>();
        bidQueryWrapper.eq(ProjectBidSectionRecord::getProjectId, projectId);
        bidQueryWrapper.eq(ProjectBidSectionRecord::getPurchaseStatus, PurchaseStatusEnum.PRE_TRIAL.getType());
        bidQueryWrapper.eq(ProjectBidSectionRecord::getBidRound, BidRoundEnum.ZGYS.getType());
        bidQueryWrapper.ge(ProjectBidSectionRecord::getStatus, PackageStatusEnum.TENDER_INVITATION.getValue());
//        bidQueryWrapper.eq(ProjectBidSectionRecord::getAbnormalStatus, AbnormalStatusEnum.NORMAL.getType());
        bidQueryWrapper.eq(ProjectBidSectionRecord::getPurchaseMode, projectBidSection.getPurchaseMode());
        bidQueryWrapper.orderByAsc(ProjectBidSectionRecord::getPackageNumber);
        List<ProjectBidSectionRecord> bidSections = projectBidSectionRecordMapper.selectList(bidQueryWrapper);
        if (CollectionUtil.isEmpty(bidSections)) {
            return true;
        }
        //标段集合
        List<Long> sectionIds = new ArrayList<>();
        String packageNumberStr = "";
        for (int i = 0; i < bidSections.size(); i++) {
            sectionIds.add(bidSections.get(i).getSectionId());
            packageNumberStr += bidSections.get(i).getPackageNumber();
            if (i != bidSections.size() - 1) {
                packageNumberStr += "、";
            }
        }
        //查询文件标段关联信息
        LambdaQueryWrapper<ProjectBidDocRelation> queryRelationWrapper = new LambdaQueryWrapper<>();
        queryRelationWrapper.eq(ProjectBidDocRelation::getProjectId, projectId);
        queryRelationWrapper.in(ProjectBidDocRelation::getSectionId, sectionIds);
        List<ProjectBidDocRelation> relations = projectBidDocRelationMapper.selectList(queryRelationWrapper);
        if (CollectionUtil.isEmpty(relations)) {
            return true;
        }
        ProjectBidDocRelation projectBidDocRelation = relations.get(0);


        //查询该标段是否已经存在文件关联信息
//        LambdaQueryWrapper<ProjectBidDocRelation> ddd = new LambdaQueryWrapper<>();
//            ddd.eq(ProjectBidDocRelation::getProjectId, projectId);
//            ddd.eq(ProjectBidDocRelation::getSectionId, sectionId);
        ProjectBidDocRelation relation = projectBidDocRelationMapper.findDocRelation(projectId,sectionId,projectBidSection.getBidRound());
//        ProjectBidDocRelation relation = projectBidDocRelationMapper.selectOne(ddd);
        if (relation == null) {
            //新增该标段与文件关联信息
            projectBidDocRelation.setId(null);
            projectBidDocRelation.setSectionId(sectionId);
            int insert = projectBidDocRelationMapper.insert(projectBidDocRelation);
            flag = insert == 1;
            if (flag) {
                LambdaQueryWrapper<ProjectBidDoc> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(ProjectBidDoc::getId, projectBidDocRelation.getDocId());
                ProjectBidDoc preQualificationDoc = projectBidDocMapper.selectOne(queryWrapper);
                if (preQualificationDoc == null) {
                    //还未创建招标文件相关信息
                    return true;
                }
                //修改招标文件信息
                preQualificationDoc.setPackageNumber(packageNumberStr);
                int update = projectBidDocMapper.updateById(preQualificationDoc);
                flag = update == 1;
            }
        }
        //修改标段发售方式等信息
        if (flag) {
            ProjectBidSection bidSection = projectBidSectionMapper.selectById(sectionId);
            //标书费
            bidSection.setTenderFeeType(bidSections.get(0).getTenderFeeType());
            bidSection.setTenderFee(bidSections.get(0).getTenderFee());
            //保证金
            bidSection.setBondType(bidSections.get(0).getBondType());
            if (2 == bidSections.get(0).getBondType()) {
                bidSection.setBondPercent(bidSections.get(0).getBondPercent());
                bidSection.setBond(bidSection.getEntrustMoney().divide(new BigDecimal("100")).multiply(bidSections.get(0).getBondPercent()));
            } else {
                bidSection.setBond(bidSections.get(0).getBond());
            }
            //发售方式
            bidSection.setReleaseFileType(bidSections.get(0).getReleaseFileType());
            bidSection.setReviewFileType(bidSections.get(0).getReviewFileType());
            //文件截止时间等
            bidSection.setSaleEndTimeType(bidSections.get(0).getSaleEndTimeType());
            bidSection.setSaleEndTime(bidSections.get(0).getSaleEndTime());
            bidSection.setSubmitEndTimeType(bidSections.get(0).getSubmitEndTimeType());
            bidSection.setSubmitEndTime(bidSections.get(0).getSubmitEndTime());
            flag = commonSectionService.updateSectionStatus(bidSection);
            //保存招标文件是否支持线上开标数据
            LambdaQueryWrapper<ProjectSectionBidOpen> bidOpenLambdaQueryWrapper = new LambdaQueryWrapper<>();
            bidOpenLambdaQueryWrapper.eq(ProjectSectionBidOpen::getProjectId, projectId);
            bidOpenLambdaQueryWrapper.eq(ProjectSectionBidOpen::getBidRound, BidRoundEnum.ZGYS.getType());
            bidOpenLambdaQueryWrapper.orderByAsc(ProjectSectionBidOpen::getSectionId);
            List<ProjectSectionBidOpen> bidOpenList = projectSectionBidOpenMapper.selectList(bidOpenLambdaQueryWrapper);
            if (null != bidOpenList && bidOpenList.size() > 0) {
                ProjectSectionBidOpen projectSectionBidOpen = new ProjectSectionBidOpen();
                projectSectionBidOpen.setProjectId(projectId);
                projectSectionBidOpen.setSectionId(bidSection.getId());
                projectSectionBidOpen.setBidOpenOnline(bidOpenList.get(0).getBidOpenOnline());
                projectSectionBidOpen.setDecryptTime(bidOpenList.get(0).getDecryptTime());
                projectSectionBidOpen.setSupNumEndBid(bidOpenList.get(0).getSupNumEndBid());
                projectSectionBidOpen.setBidRound(bidOpenList.get(0).getBidRound());
                projectSectionBidOpen.setIsAutoChant(bidOpenList.get(0).getIsAutoChant());
                projectSectionBidOpenMapper.insert(projectSectionBidOpen);
            }
            //保存是否支持澄清异议数据
            LambdaQueryWrapper<ClarifyQuestion> clarifyQuestionLambdaQueryWrapper = new LambdaQueryWrapper<>();
            clarifyQuestionLambdaQueryWrapper.eq(ClarifyQuestion::getProjectId, projectId);
            clarifyQuestionLambdaQueryWrapper.orderByAsc(ClarifyQuestion::getSectionId);
            List<ClarifyQuestion> clarifyQuestionList = clarifyQuestionMapper.selectList(clarifyQuestionLambdaQueryWrapper);
            if (null != clarifyQuestionList && clarifyQuestionList.size() > 0) {
                ClarifyQuestion clarifyQuestion = new ClarifyQuestion();
                clarifyQuestion.setProjectId(projectId);
                clarifyQuestion.setSectionId(bidSection.getId());
                clarifyQuestion.setClarifyOnLine(clarifyQuestionList.get(0).getClarifyOnLine());
                clarifyQuestion.setClarifyEndTime(clarifyQuestionList.get(0).getClarifyEndTime());
                clarifyQuestion.setClarifyEndTimeType(clarifyQuestionList.get(0).getClarifyEndTimeType());
                clarifyQuestion.setQuestionOnLine(clarifyQuestionList.get(0).getQuestionOnLine());
                clarifyQuestion.setQuestionEndTime(clarifyQuestionList.get(0).getQuestionEndTime());
                clarifyQuestion.setQuestionEndTimeType(clarifyQuestionList.get(0).getQuestionEndTimeType());
                clarifyQuestionMapper.insert(clarifyQuestion);
            }
        }
        return flag;
    }

    @Override
    public Boolean deletePreQualificationInfo(Long projectId, Long sectionId) {
        Boolean flag = true;
        //该标段关联文件信息
        LambdaQueryWrapper<ProjectBidDocRelation> relationWrapper = new LambdaQueryWrapper<>();
        relationWrapper.eq(ProjectBidDocRelation::getSectionId, sectionId);
        ProjectBidDocRelation relation = projectBidDocRelationMapper.selectOne(relationWrapper);
        if (relation == null) {
            return flag;
        } else {
            Long docId = relation.getDocId();
            LambdaQueryWrapper<ProjectBidDocRelation> queryList = new LambdaQueryWrapper<>();
            queryList.eq(ProjectBidDocRelation::getProjectId, projectId);
            queryList.eq(ProjectBidDocRelation::getDocId, docId);
            List<ProjectBidDocRelation> relations = projectBidDocRelationMapper.selectList(queryList);
            if (CollectionUtil.isEmpty(relations)) {
                return flag;
            }
            //删除文件标段关联信息
            int delete = projectBidDocRelationMapper.deleteById(relation.getId());
            flag = delete == 1;
            if (flag) {
                //该招标文件只关联一个标段,删除文件信息 判断项目此时是否为无包状态，如果为无包，则直接删除
                Project project = projectMapper.selectById(projectId);
                if (relations.size() == 1 || project.getPackageSegmentStatus().equals(SegmentEnum.NO_BAG.getType())) {
                    delete = projectBidDocMapper.deleteById(docId);
                    flag = delete == 1;
                } else {
                    //该招标文件关联多个标段，修改文件的信息
                    ProjectBidSection projectBidSection = projectBidSectionMapper.selectById(sectionId);
                    ProjectBidDoc doc = projectBidDocMapper.selectById(docId);
                    String[] split = doc.getPackageNumber().split("、");
                    List<String> strings = CollectionUtil.toList(split);
                    strings.removeIf(e -> e.contains(projectBidSection.getPackageNumber().toString()));
                    String packge = "";
                    for (int i = 0; i < strings.size(); i++) {
                        packge += strings.get(i);
                        if (i != strings.size() - 1) {
                            packge += "、";
                        }
                    }
                    doc.setPackageNumber(packge);
                    int update = projectBidDocMapper.updateById(doc);
                    flag = update == 1;
                }
            }
            return flag;
        }
    }


}
