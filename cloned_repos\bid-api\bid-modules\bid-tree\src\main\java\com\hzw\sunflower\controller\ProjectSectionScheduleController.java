package com.hzw.sunflower.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.hzw.sunflower.common.BaseController;
import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.common.annotation.RepeatSubmit;
import com.hzw.sunflower.constant.MessageConstants;
import com.hzw.sunflower.controller.request.*;
import com.hzw.sunflower.controller.response.ApplyResponseFileVo;
import com.hzw.sunflower.controller.response.BidOpenEndVo;
import com.hzw.sunflower.controller.response.BidScheduleFileInfo;
import com.hzw.sunflower.controller.response.ScheduleDataVo;
import com.hzw.sunflower.dto.BidOpenDTO;
import com.hzw.sunflower.dto.BidOpenFileDto;
import com.hzw.sunflower.dto.ProjectSectionScheduleDto;
import com.hzw.sunflower.service.ApplyResponseFileService;
import com.hzw.sunflower.service.BidOpenFileService;
import com.hzw.sunflower.service.ProjectSectionScheduleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * 开标一览表文件服务
 */
@Api(tags = "开标一览表文件服务")
@RestController
@RequestMapping("/openBidSchedule")
public class ProjectSectionScheduleController extends BaseController {

    @Autowired
    private ProjectSectionScheduleService projectSectionScheduleService;

    @Autowired
    private ApplyResponseFileService applyResponseFileService;

    @Autowired
    private BidOpenFileService bidOpenFileService;
    
    @ApiOperation(value = "根据招标文件ID查询附件信息")
    @ApiImplicitParam(name = "req", value = "用户表 查询条件", required = true, dataType = "DocScheduleREQ", paramType = "body")
    @PostMapping(value = "/getByDocId")
    public Result<List<BidScheduleFileInfo>> getByDocId(@RequestBody DocScheduleREQ req) {
        if (req.getDocId() == null) {
            return Result.failed(MessageConstants.GET_NULL);
        }
        List<ProjectSectionScheduleDto> dtoList = projectSectionScheduleService.getInfoByDocId(req.getDocId(), req.getBidRound());
        if (CollectionUtil.isEmpty(dtoList)) {
            return Result.ok(new ArrayList<>());
        }
        //类型转换成前端定义数据格式
        Map<Long, BidScheduleFileInfo> map = new HashMap<>();
        Map<Long, String> numberMap = new HashMap<>();
        Map<Long, String> sectionIdMap = new HashMap<>();
        Map<Long, String> annexIdMap = new HashMap<>();
        dtoList.stream().forEach((e) -> {
            if (null == map.get(e.getFileOssId())) {
                numberMap.put(e.getFileOssId(), e.getPackageNumber() + "");
                sectionIdMap.put(e.getFileOssId(), e.getSectionId().toString());
                annexIdMap.put(e.getFileOssId(), e.getId().toString());
            } else {
                numberMap.put(e.getFileOssId(), numberMap.get(e.getFileOssId()) + "、" + e.getPackageNumber());
                sectionIdMap.put(e.getFileOssId(), sectionIdMap.get(e.getFileOssId()) + "、" + e.getSectionId());
                annexIdMap.put(e.getFileOssId(), annexIdMap.get(e.getFileOssId()) + "、" + e.getId());
            }
            BidScheduleFileInfo bidFileInfo = new BidScheduleFileInfo();
            bidFileInfo.setKey(e.getOssFileKey());
            bidFileInfo.setName(e.getOssFileName());
            bidFileInfo.setFileId(e.getFileOssId());
            bidFileInfo.setAmount(new String[]{numberMap.get(e.getFileOssId())});
            bidFileInfo.setSectionIdArray(sectionIdMap.get(e.getFileOssId()).split("、"));
            bidFileInfo.setAnnexIdArray(annexIdMap.get(e.getFileOssId()).split("、"));
            bidFileInfo.setGenerateRule(e.getGenerateRule());
            bidFileInfo.setRowNum(e.getRowNum());
            map.put(e.getFileOssId(), bidFileInfo);
        });
        List<BidScheduleFileInfo> collect = map.values().stream().collect(Collectors.toList());
        return Result.ok(collect);
    }

    @ApiOperation(value = "导出开标一览表模板")
    @GetMapping(value = "/exportScheduleTemplate/{projectId}")
    public void exportScheduleTemplate(@PathVariable Long projectId, HttpServletResponse response, HttpServletRequest request) {
        projectSectionScheduleService.exportScheduleTemplate(projectId, request,response);
    }

    @ApiOperation(value = "根据标段获取开标一览表模板文件信息")
    @PostMapping(value = "/getScheduleBySectionId/{sectionId}")
    public Result<ProjectSectionScheduleDto> getScheduleBySectionId(@PathVariable Long sectionId) {
        if (sectionId == null) {
            return Result.failed(MessageConstants.PARAMS_NOT_NULL);
        }
        return Result.ok(projectSectionScheduleService.getInfoBySectionId(sectionId));
    }

    @ApiOperation(value = "开启唱标-初始化开标记录表")
    @PostMapping(value = "/startSingInit")
    @ApiImplicitParam(name = "req", value = "请求条件", required = true, dataType = "StartSingInitReq", paramType = "body")
    public Result<ScheduleDataVo> startSingInit(@RequestBody StartSingInitReq req) {
        if (req.getSectionId() == null) {
            return Result.failed(MessageConstants.PARAMS_NOT_NULL);
        }
        return Result.ok(projectSectionScheduleService.startSingInit(req));
    }

    @ApiOperation(value = "查询唱标文件类型")
    @PostMapping(value = "/findSingType")
    @ApiImplicitParam(name = "req", value = "请求条件", required = true, dataType = "SingTypeREQ", paramType = "body")
    public Result<Integer> findSingType(@RequestBody SingTypeREQ req) {
        if (req.getSectionId() == null) {
            return Result.failed(MessageConstants.PARAMS_NOT_NULL);
        }
        return projectSectionScheduleService.findSingType(req);
    }

    @ApiOperation(value = "开启唱标-确认")
    @PostMapping(value = "/startSingConfirm")
    @ApiImplicitParam(name = "req", value = "请求条件", required = true, dataType = "StartSingConfirmReq", paramType = "body")
    @RepeatSubmit
    public Result<Boolean> startSingConfirm(@RequestBody StartSingConfirmReq req) throws Exception {
        if (req.getSectionId() == null) {
            return Result.failed(MessageConstants.PARAMS_NOT_NULL);
        }
        return Result.okOrFailed(projectSectionScheduleService.startSingConfirm(req));
    }

    @ApiOperation(value = "供应商唱标中-根据标段获取开标记录表")
    @GetMapping(value = "/getBidOpenFileBySectionId/{sectionId}")
    public Result<BidOpenFileDto> getBidOpenFileBySectionId(@PathVariable Long sectionId) {
        if (sectionId == null) {
            return Result.failed(MessageConstants.PARAMS_NOT_NULL);
        }
        return Result.ok(bidOpenFileService.getOpenFileBySectionId(sectionId));
    }

    @ApiOperation(value = "根据供应商id获取开标一览表响应文件")
    @GetMapping(value = "/getScheduleByCompanyId/{sectionId}/{companyId}")
    public Result<ApplyResponseFileVo> getScheduleByCompanyId(@PathVariable Long sectionId, @PathVariable Long companyId) {
        if (sectionId == null || companyId == null) {
            return Result.failed(MessageConstants.PARAMS_NOT_NULL);
        }
        return Result.ok(applyResponseFileService.getResponseFile(companyId, sectionId));
    }

    @ApiOperation(value = "结束唱标页面-查询结束开标数据")
    @ApiImplicitParam(name = "dto", value = "结束开标请求条件", required = true, dataType = "BidOpenDTO", paramType = "body")
    @PostMapping(value = "/getBidOpenEndData")
    public Result<BidOpenEndVo> getBidOpenEndData(@RequestBody BidOpenDTO dto) {
        return Result.ok(bidOpenFileService.getBidOpenEndData(dto));
    }

    @ApiOperation(value = "查询开标记录表")
    @ApiImplicitParam(name = "dto", value = "请求条件 ", required = true, dataType = "StartDecSmsReq", paramType = "body")
    @PostMapping(value = "/queryBidOpeningRecord")
    public Result<Long> queryBidOpeningRecord(@RequestBody StartDecSmsReq req) {
        return projectSectionScheduleService.queryBidOpeningRecord(req);
    }

}
