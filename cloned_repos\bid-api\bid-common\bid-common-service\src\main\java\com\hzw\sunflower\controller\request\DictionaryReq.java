package com.hzw.sunflower.controller.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date ：Created in 2021/7/29 15:54
 * @description：字典管理
 * @modified By：`
 * @version: 1.0
 */
@Data
public class DictionaryReq {
    @ApiModelProperty(value = "id")
    private Long id;
    @ApiModelProperty(value = "pid")
    private Long pid;

    @ApiModelProperty(value = "状态 -1：全部 0：启用 1：禁用")
    private Integer isDisable;

    @ApiModelProperty(value = "关键字：名称 code")
    private String keywords;
}
