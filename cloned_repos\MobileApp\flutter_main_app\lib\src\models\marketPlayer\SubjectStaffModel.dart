///***
/// * 主体人员 模型
/// */

import 'package:json_annotation/json_annotation.dart';

part 'SubjectStaffModel.g.dart';

@JsonSerializable(explicitToJson: true)
class SubjectStaffModel {

  SubjectStaffModel({
    this.active,
    this.age,
    this.approvalDate,
    this.areaCode,
    this.birthDate,
    this.caNoDataKey,
    this.certificateLevel,
    this.certificateName,
    this.certificateQualification,
    this.companyCode,
    this.companyName,
    this.contactAddress,
    this.contactNumber,
    this.createdDate,
    this.dataKey,
    this.education,
    this.expiryDate,
    this.gender,
    this.iDCard,
    this.isActiveStaff,
    this.major,
    this.modifiedDate,
    this.otherExpiryDate,
    this.position,
    this.postcode,
    this.professionalTitle,
    this.sex,
    this.staffCode,
    this.staffID,
    this.staffName,
    this.staffNo,
    this.staffType,
    this.startPositionDate,
    this.statusValue,
    this.subjectDataKey,
    this.subjectID,
    this.subjectType,
    this.validDate,
    this.workExperience,
    this.workYears,
  });

  final bool active;

  final String age;

  final String approvalDate;

  final String areaCode;

  final String birthDate;

  final String caNoDataKey;

  final String certificateLevel;

  final String certificateName;

  final String certificateQualification;

  final String companyCode;

  final String companyName;

  final String contactAddress;

  final String contactNumber;

  final String createdDate;

  final String dataKey;

  final String education;

  final String expiryDate;

  final String gender;

  final String iDCard;

  final String isActiveStaff;

  final String major;

  final String modifiedDate;

  final String otherExpiryDate;

  final String position;

  final String postcode;

  final String professionalTitle;

  final String sex;

  final String staffCode;

  final int staffID;

  final String staffName;

  final String staffNo;

  final int staffType;

  final String startPositionDate;

  final int statusValue;

  final String subjectDataKey;

  final int subjectID;

  final String subjectType;

  final String validDate;

  final String workExperience;

  final String workYears;

  factory SubjectStaffModel.fromJson(Map<String, dynamic> json) => _$SubjectStaffModelFromJson(json);

  Map<String, dynamic> toJson() => _$SubjectStaffModelToJson(this);

}
