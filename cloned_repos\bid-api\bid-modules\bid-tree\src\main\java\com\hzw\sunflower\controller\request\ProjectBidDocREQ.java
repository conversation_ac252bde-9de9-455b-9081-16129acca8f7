package com.hzw.sunflower.controller.request;

import com.hzw.sunflower.entity.SectionGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 招标文件信息请求实体
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@ApiModel(description = "招标文件请求参数")
@Data
public class ProjectBidDocREQ implements Serializable {

    private static final long serialVersionUID = -35408232177018412L;

    @ApiModelProperty(value = "主键ID", position = 1)
    private Long docId;

    @NotNull(message = "项目ID不能为空")
    @ApiModelProperty(value = "项目ID", position = 2)
    private Long projectId;

    @ApiModelProperty(value = "关联标段(冗余字段)", position = 3)
    private String packageNumber;

    @ApiModelProperty(value = "招标文件发售开始时间", position = 4)
    private Date saleStartTime;

    @ApiModelProperty(value = "招标文件发售结束时间类型（1：确定时间; 2：另行通知;3：投标文件递交截止时间前一天）", position = 5)
    private Integer saleEndTimeType;

    @ApiModelProperty(value = "招标文件发售结束时间", position = 6)
    private Date[] saleEndTimeArray;

    @ApiModelProperty(value = "招标文件进度 1：起草中 2：审核中 3：已撤回 4：已审核 5：已发布 6：已退回", position = 7)
    private Integer noticeProgress;

    @ApiModelProperty(value = "招标文件状态 1：正式 2：暂存", position = 8)
    private Integer state;

    @ApiModelProperty(value = "联系人", position = 9)
    private String contact;

    @ApiModelProperty(value = "手机号", position = 10)
    private String phone;

    @NotBlank(message = "招标文件名称不能为空")
    @ApiModelProperty(value = "招标文件名称", position = 11)
    private String annexName;

    @ApiModelProperty(value = "标书费支付方式", position = 12)
    private Integer paymentType;

    @ApiModelProperty(value = "费用类型 1.标书费 2.平台服务费")
    private Integer feeType;

    @NotNull(message = "文件发售方式不能为空")
    @ApiModelProperty(value = "文件发售方式(0:电子文件线上下载, 1:纸质文件不支持邮寄)", position = 13)
    private Integer releaseFileType;

    @NotNull(message = "文件发售审核方式不能为空")
    @ApiModelProperty(value = "文件发售审核方式(0:无需审核, 1:审核无需材料, 2:审核上传材料)", position = 14)
    private Integer reviewFileType;

    @ApiModelProperty(value = "材料清单", position = 15)
    private String materialList;

    @NotNull(message = "保证金类型不能为空")
    @ApiModelProperty(value = "保证金类型 0不收取 1定额 2比例 ", position = 16)
    private Integer bondType;

    @ApiModelProperty(value = "保证金（单个）", position = 17)
    private BigDecimal bond;

    @ApiModelProperty(value = "保证金金额(比例换算后金额)", position = 18)
    private BigDecimal[] bondArray;

    @NotNull(message = "标书费类型不能为空")
    @ApiModelProperty(value = "标书费类型 0不收取 1按套 2按标段/包 3收取", position = 19)
    private Integer tenderFeeType;

    @ApiModelProperty(value = "标书费（单个）", position = 20)
    private BigDecimal tenderFee;

    @ApiModelProperty(value = "标书费金额（按标段包）", position = 21)
    private BigDecimal[] tenderFeeArray;

    @NotEmpty(message = "标段/包主键不能为空")
    @ApiModelProperty(value = "标段/包主键", position = 22)
    private Long[] sectionIdArray;

    @NotNull(message = "oss文件主键不能为空")
    @ApiModelProperty(value = "oss文件主键", position = 23)
    private Long ossFileId;

    @ApiModelProperty(value = "投标文件递交截止时间", position = 24)
    private Date[] submitEndTimeArray;

    @NotBlank(message = "变更类型不能为空")
    @ApiModelProperty(value = "变更类型", position = 25)
    private String changeType;

    @ApiModelProperty(value = "保证金金额(比例值)", position = 26)
    private BigDecimal[] bondPercentArray;

    @ApiModelProperty(value = "开标地点", position = 27)
    private String[] address;

    @NotNull(message = "投标文件递交截止时间类型不能为空")
    @ApiModelProperty(value = "投标文件递交截止时间类型（1：确定时间; 2：另行通知）", position = 28)
    private Integer submitEndTimeType;

    @NotNull(message = "招标文件售价说明不能为空")
    @ApiModelProperty(value = "招标文件售价说明（ 0:无 1：交通；2：水利；3：其他）", position = 29)
    private Integer filePriceState;

    @ApiModelProperty(value = "按套销售信息", position = 30)
    private SectionGroup sectionGroup;

    @ApiModelProperty(value = "已购买招标文件的投标人本次是否收费", position = 31)
    private Integer free;

    @ApiModelProperty(value = "取消关联标段ID", position = 32)
    private List<Long> cancelAssociated;

    @ApiModelProperty(value = "版本号", position = 33)
    private Integer version;

    @ApiModelProperty(value = "招标文件阶段： 0：默认无阶段； 1：第一轮； 2：第二轮", position = 34)
    private Integer bidRound;

    @ApiModelProperty(value = "标书费是否收费1 收费 ，2免费", position = 35)
    private Integer tenderFeeFlag;

    @ApiModelProperty(value = "是否为资格预审按项目", position = 36)
    private Boolean preQualificationFlag;

    @ApiModelProperty(value = "文件售价说明其他时具体描述", position = 37)
    private String otherPriceDescribe;

    @ApiModelProperty(value = "线上澄清（1 支持 2 不支持）")
    private Integer clarifyOnLine;

    @ApiModelProperty(value ="提出澄清截止时间（1：确定时间; 2：投标文件递交截止时间前一天）")
    private Integer clarifyEndTimeType;

    @ApiModelProperty(value ="提出澄清截止时间")
    private Date[] clarifyEndTimeArray;

    @ApiModelProperty(value = "线上异议（1 支持 2 不支持）")
    private Integer questionOnLine;

    @ApiModelProperty(value ="提出异议截止时间（1：确定时间; 2：投标文件递交截止时间前一天）")
    private Integer questionEndTimeType;

    @ApiModelProperty(value ="提出异议截止时间")
    private Date[] questionEndTimeArray;

    @ApiModelProperty(value = "线上开标（1 是 2 否）")
    private Integer onlineBidOpen;

    @ApiModelProperty(value = "解密时间")
    private Integer[] decryptionTimeArray;

    @ApiModelProperty(value = "开标条件-终止开标供应商限制数量")
    private Integer[] conditionBidOpenArray;

    @ApiModelProperty(value = "开标一览表文件")
    private Long[] bidOpeningArray;

    @ApiModelProperty(value = "开标记录表生成方式")
    private Integer[] generationArray;

    @ApiModelProperty(value = "系统自动唱标数组")
    private Integer[] systemAutomaticLabelingArray;
}
