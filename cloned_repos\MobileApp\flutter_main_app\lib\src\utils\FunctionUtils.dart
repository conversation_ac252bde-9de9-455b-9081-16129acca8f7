///***
/// * 工具函数
/// */

import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';

import 'package:flutter_main_app/src/components/CustomPageRoute.dart';

class FunctionUtils {

  ///***
  /// * 路由跳转
  /// * 跳转目标为 MaterialPageRoute
  /// *
  /// * @test 试验用
  /// *
  /// * @param {BuildContext} context
  /// * @param {Widget} widget
  /// *
  /// * @return {Future<T>}
  /// */
  static Future<T> pushMaterialPageRoute<T extends Object>(
    BuildContext context,
    Widget widget,
  ) {
    const List<String> themes = <String>['size', 'fade', 'rotate', 'scale', 'slideUpDown', 'slide', 'others'];

    final Random random = Random();

    final int index = random.nextInt(themes.length - 1);

    final String theme = themes[index];

    // print('theme: $theme');

    return Navigator.push(
      context,
      // MaterialPageRoute<T>(
      //   builder: (BuildContext context) => widget,
      // ),
      // CupertinoPageRoute<T>(
      //   builder: (BuildContext context) => widget,
      // ),
      // CustomMaterialPageRoute<T>(
      //   builder: (BuildContext context) => widget,
      //   theme: theme,
      // ),
      // CustomCupertinoPageRoute<T>(
      //   builder: (BuildContext context) => widget,
      //   theme: theme,
      // ),
      CustomPageRouteBuilder<T>(
        widget: widget,
        theme: theme,
      ),
    );
  }

  ///***
  /// * 为进页面和出页面同时添加动画
  /// *
  /// * @animation SlideAnimation
  /// *
  /// * @test 试验用
  /// *
  /// * @param {BuildContext} context
  /// * @param {Widget} enterPage
  /// * @param {Widget} exitPage
  /// *
  /// * @return {Future<T>}
  /// */
  static Future<T> slideEnterExitPageRoute<T extends Object>(
    BuildContext context,
    Widget enterPage,
    Widget exitPage,
  ) {
    return Navigator.push(
      context,
      CustomEnterExitPageRoute(
        enterPage: enterPage,
        exitPage: exitPage,
      ),
    );
  }

  ///***
  /// * 将单位 '元' 转换为 单位 '万元'
  /// * 保留 2 位小数
  /// *
  /// * @param {num} yuan
  /// * @param {int} fixed
  /// * @return {double} fixed double wanYuan
  /// */
  static double convertYuanToWanYuan(
    num yuan,
    {
      int fixed = 2,
    }
  ) {
    int ratio = 10000;

    double wanYuan = yuan / ratio;

    return getFixedDouble(wanYuan, fixed: fixed);
  }

  ///***
  /// * 获取固定小数位的浮点数
  /// *
  /// * @param {num} value
  /// * @param {int} fixed
  /// * @return {double} fixed double value
  /// */
  static double getFixedDouble(
    num value,
    {
      int fixed = 2,
    }
  ) {
    return double.parse(value.toStringAsFixed(fixed));
  }

}
