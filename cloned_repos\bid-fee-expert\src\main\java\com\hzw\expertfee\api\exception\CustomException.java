package com.hzw.expertfee.api.exception;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;


/**
 * <AUTHOR>
 */
@Data
@Slf4j
public class CustomException extends RuntimeException {

    private ExceptionEnum exceptionEnum;

    /*
     * 自定义异常信息
     */
    private String errorDetail;

    /**
     * 带自定义异常信息的构造方法
     *
     * @param exceptionEnums
     * @param errorDetail
     */
    public CustomException(ExceptionEnum exceptionEnums, String errorDetail) {
        this.exceptionEnum = exceptionEnums;
        this.errorDetail = errorDetail;
    }
}
