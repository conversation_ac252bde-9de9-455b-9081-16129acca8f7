package com.hzw.expertfee.api.util;

import cn.hutool.core.text.StrFormatter;
import com.hzw.expertfee.api.constant.FormatConstants;
import org.apache.commons.lang3.StringUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.regex.PatternSyntaxException;

/**
 * String工具类
 */
public class StringUtil {

    /**
     * * 判断一个对象是否为空
     *
     * @param object Object
     * @return true：为空 false：非空
     */
    public static boolean isNull(Object object) {
        return object == null;
    }

    /**
     * 是否包含字符串
     *
     * @param str  验证字符串
     * @param strs 字符串组
     * @return 包含返回true
     */
    public static boolean inStringIgnoreCase(String str, String... strs) {
        if (str != null && strs != null) {
            for (String s : strs) {
                if (str.equalsIgnoreCase(trim(s))) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 去空格
     */
    public static String trim(String str) {
        return (str == null ? "" : str.trim());
    }

    /**
     * * 判断一个对象数组是否非空
     *
     * @param objects 要判断的对象数组
     * @return true：非空 false：空
     */
    public static boolean isNotEmpty(Object[] objects) {
        return !isEmpty(objects);
    }

    /**
     * * 判断一个对象数组是否为空
     *
     * @param objects 要判断的对象数组
     *                * @return true：为空 false：非空
     */
    public static boolean isEmpty(Object[] objects) {
        return isNull(objects) || (objects.length == 0);
    }

    /**
     * 检查时间类型字符串
     *
     * @param s       检查字符串
     * @param pattern 格式
     * @return 检查结果
     */
    public static boolean isValidDate(String s, String pattern) {
        DateTimeFormatter ofPattern = DateTimeFormatter.ofPattern(pattern);
        if (s == null || s.trim().equals("")) {
            return false;
        }
        try {
            return LocalDateTime.parse(s, ofPattern).format(ofPattern).equals(s);
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 检查短时间类型字符串
     *
     * @param s 检查字符串
     * @return 检查结果
     */
    public static boolean isValidDateShort(String s) {
        DateTimeFormatter ofPattern = DateTimeFormatter.ofPattern(FormatConstants.FORMAT_SHORT_DATE);

        if (s == null || s.trim().equals("")) {
            return false;
        }
        try {
            return LocalDateTime.parse(s, ofPattern).format(ofPattern).equals(s);
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 将字符串渲染到客户端
     *
     * @param response 渲染对象
     * @param string   待渲染的字符串
     * @return null
     */
    public static String renderString(HttpServletResponse response, String string) {
        try {
            response.setStatus(HttpServletResponse.SC_OK);
            response.setContentType("application/json");
            response.setCharacterEncoding("utf-8");
            response.getWriter().print(string);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    public static String lineToHump(String str) {
        if (null != str && !"".equals(str)) {
            str = str.toLowerCase();
            String[] strs = str.split("_");
            StringBuffer sb = new StringBuffer();
            String[] var3 = strs;
            int var4 = strs.length;

            for(int var5 = 0; var5 < var4; ++var5) {
                String _str = var3[var5];
                _str = _str.substring(0, 1).toUpperCase() + _str.substring(1);
                sb.append(_str);
            }

            str = sb.toString();
            return str;
        } else {
            return str;
        }
    }


    public static String toLowerCaseFirstOne(String s) {
        if (StringUtils.isBlank(s)) {
            return s;
        } else {
            return Character.isLowerCase(s.charAt(0)) ? s : Character.toLowerCase(s.charAt(0)) + s.substring(1);
        }
    }
    public static String format(String template, Object... params)
    {
        if (isEmpty(params) || StringUtils.isEmpty(template))
        {
            return template;
        }
        return StrFormatter.format(template, params);
    }

    /**
     * * 判断一个对象是否非空
     *
     * @param object Object
     * @return true：非空 false：空
     */
    public static boolean isNotNull(Object object)
    {
        return !isNull(object);
    }

    /**
     * 过滤所有特殊字符
     * @param str
     * @return
     * @throws PatternSyntaxException
     */
    public static String stringFilter(String str) throws PatternSyntaxException {
        // 清除掉所有特殊字符
//        String regEx = "[`~!@#$%^&*()+=|{}':;',//[//].<>/?~！@#￥%……&*（）——+|{}【】‘；：”“’。，、？]";
        String regEx = "[`~%^';',//[//]<>/~@#￥]";
        Pattern p = Pattern.compile(regEx);
        Matcher m = p.matcher(str);
        return m.replaceAll("").trim();
    }

}
