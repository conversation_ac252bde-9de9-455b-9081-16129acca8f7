package com.hzw.ssm.fw.util;

import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Random;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;

import com.hzw.ssm.expert.entity.ExpertInfoEntity;
import com.hzw.ssm.expert.entity.ExpertMajorEntity;
import com.hzw.ssm.sys.project.entity.ConditionEntity;
import com.hzw.ssm.sys.project.entity.ResultEntity;

public class CommUtil {

	/**
	 * 使用17位日期时间加4位流水号创建一个至少21位的主键值
	 * 
	 * @param len
	 *            主键位数，必须在21~32之间，否则返回的主键只有20位
	 * @return 返回主键
	 */
	public synchronized static String getKey(int len) {

		StringBuffer ret = new StringBuffer(32);
		dfDate.setLenient(false);
		ret.append(dfDate.format(new java.util.Date()));

		ret.append(dfSerialNumFour.format(serial++));
		if (serial >= 10000)
			serial = 0;

		if (len > 21 && len < 33) {
			int seed = Integer.valueOf(ret.substring(15, 18)).intValue();
			java.util.Random random = new java.util.Random(seed);
			String ran = dfRandomNum.format(Math.abs(random.nextLong()));
			ret.append(ran.substring(0, len - 21));
		}
		return ret.toString();
	}

	/** 最大三位的流水号 */
	private static int serial = 0;
	/** 日期生成字符串格式 */
	private static SimpleDateFormat dfDate = new SimpleDateFormat(
			"yyyyMMddHHmmssSSS"); // yyyy-MM-dd HH:mm:ss.SSS
	/** 三位流水号生成字符串格式 */
	private static DecimalFormat dfSerialNumThree = new DecimalFormat("000");
	/** 四位流水号生成字符串格式 */
	private static DecimalFormat dfSerialNumFour = new DecimalFormat("0000");
	/** 随机数生成字符串格式 */
	private static DecimalFormat dfRandomNum = new DecimalFormat("00000000000");

	/**
	 * 使用17位日期时间加3位流水号创建一个20位的主键值
	 * 
	 * @return 返回主键
	 */
	public synchronized static String getKey() {

		StringBuffer ret = new StringBuffer(20);
		dfDate.setLenient(false);
		ret.append(dfDate.format(new java.util.Date()));

		ret.append(dfSerialNumThree.format(serial++));
		if (serial >= 1000)
			serial = 0;

		return ret.toString();
	}

	/**
	 * 短信系统自动发送自定义短信批次
	 * @return 返回主键
	 */
	public synchronized static String getSmsKey() {
		Random random = new Random();
		String result="";
		for (int i=0;i<6;i++)
		{
			result+=random.nextInt(10);
		}
		return result;
	}
	
	
	/**
	 * 返回错误的堆栈信息
	 * 
	 * @param e
	 *            错误对象
	 * @param message
	 *            是否返回错误描述
	 * @return 返回堆栈信息
	 */
	public static String getStackTraceInfo(Exception e, boolean message) {
		StringBuffer buf = new StringBuffer("");
		if (message) {
			buf.append(e.getMessage());
		}
		StackTraceElement stack[] = e.getStackTrace();
		for (int i = 0; i < stack.length; i++) {
			buf.append("\n\t").append(stack[i].toString());
		}
		return buf.toString();
	}


	/**
	 * <pre>
	 *     卸载Session中的所有信息并使Session失效。
	 * </pre>
	 * 
	 * @param request
	 *            请求对象
	 */
	public static void destroyTempSession(HttpServletRequest request, String key) {
		HttpSession httpSession = request.getSession();
		if (httpSession != null) {
			String linked = (String) httpSession.getAttribute(key);
			if (linked != null && linked.equals("true")) {
				httpSession.setAttribute(key, null);
				httpSession.invalidate();
			}
		}
	}

	/**
	 * 将对象转换成数值
	 * 
	 * @param val
	 *            对象
	 * @param def
	 *            默认值
	 * @return 数值
	 */
	public static int changeStringToInt(Object val, int def) {
		int ret = def;
		try {
			if (val != null && !val.equals("")) {
				ret = Integer.valueOf(String.valueOf(val).trim()).intValue();
			}
		} catch (Exception e) {
			e.printStackTrace();
			ret = def;
		}
		return ret;
	}

	/**
	 * 过滤html脚本中的特殊字符
	 * 
	 * @param strInfo
	 *            html脚本
	 * @return 过滤后的脚本
	 */
	public static String filterHtml(String strInfo) {
		if (strInfo == null)
			return "";
		strInfo = strInfo.replaceAll("&", "&amp;");
		strInfo = strInfo.replaceAll(">", "&gt;");
		strInfo = strInfo.replaceAll("<", "&lt;");
		strInfo = strInfo.replaceAll("\"", "&quot;");
		return strInfo;
	}
	
	/**
	 * 拼接再次抽取生成的专家行
	 * @param list
	 * @return
	 */
	public static  String appendTable(List<ExpertInfoEntity> list,ConditionEntity conEntity, boolean flag){
		StringBuffer st=new StringBuffer();
		Integer startCol = conEntity.getOrder();//下一轮的开始列 
		for(ExpertInfoEntity ex: list){
			startCol++;
			st.append("<input type=\"hidden\" id=\"userId"+startCol+"\" value=\""+ex.getUser_id()+"\"/>");
			st.append("<tr>");
			if (flag && "2".equals(conEntity.getMethod()))
			{
				st.append("<td style=\"text-align:center\"><input type=\"checkbox\" id=\"chkSms\" name=\"chkSms\" onclick=\"chkChange('" + startCol + "')\" value=\"" + startCol + "\" /></td>");
			}
			st.append("<td style='text-align:center'>"+(startCol)+"</td>");
			st.append("<td style='text-align:center'>"+ex.getUser_name()+"</td>");
			st.append("<td style='text-align:center'>"+ex.getMobilephone()+"</td>");
			st.append("<td style='text-align:center'>"+(String.valueOf(ex.getGrade()).equals("2")==true?"国家":"地方")+"</td>");
			st.append("<td style='text-align:center' id=\"company" + startCol + "\">"+ex.getCompany()+"</td>");
			String temp="";
			for(ExpertMajorEntity major: ex.getMajorList()){//评标专业
				temp+=major.getMajor()+",";
			}
			st.append("<td style='text-align:center'>"+temp.substring(0,temp.length())+"</td>");
			st.append("<td>");
			// 只有抽取人自己申请的项目可以自己确认结果
			if (flag)
			{
				if ("2".equals(conEntity.getMethod()))
				{
					st.append("<span id=\"joinspan"+startCol+"\">短信未通知</span><span id=\"joinSpan" + startCol + "\" style=\"display:none\">");
				}
				else
				{
					st.append("<span id=\"joinSpan" + startCol + "\">");
				}
				st.append("<input type=\"checkbox\"  name=join"+startCol+" onClick=\"countJoin(this,'yes',"+startCol+")\"/>是 &nbsp;&nbsp;" +
						"<input type=\"checkbox\" name=join"+startCol+" onClick=\"countJoin(this,'no',"+startCol+")\"/>否</span></td>");
				st.append("<td><select id=\"select"+startCol+"\" onchange=\"updateReason('"+startCol+"')\" style=\"display:none\" class=\"span2\">");
				st.append("<option value=\"\">—请选择—</option>" );
				st.append("<option value=\"电话未接听\">电话未接听</option>");
				st.append("<option value=\"临时有事\">临时有事</option>");
				st.append("<option value=\"生病\">生病</option>" );
				st.append("<option value=\"出差\">出差</option>" );
				st.append("<option value=\"重大疾病\">重大疾病</option>" );
				st.append("<option value=\"已去世\">已去世</option>" );
				st.append("</select>");
			}
			else
			{
				if ("2".equals(conEntity.getMethod()))
				{
					st.append("短信");
				}
				st.append("未通知</td><td>&nbsp;");
			}
			st.append("</td></tr>");
		}
		return st.toString();
	}
	
	/**
	 * 拼接再次抽取生成的表格
	 * 抽取轮次不显示，抽取专家放在一个表格，故只添加行
	 * libb 2015-07-07
	 * @param list
	 * @return
	 */
	public static  String appendTable_(List<ExpertInfoEntity> list,ConditionEntity conEntity, boolean flag){
		StringBuffer st=new StringBuffer("<ul class=\"breadcrumb\"><li class=\"active\">第"+StringUtil.numToStr((conEntity.getNum().toString()))+"轮抽取</li></ul>");
		st.append("<table class=\"table table-striped table-bordered table-hover table-condensed\">");
		st.append("<thead><th width=\"4%\">序号</th>");
		st.append("<th width=\"10%\">专家姓名</th>");
		st.append("<th width=\"10%\">手机号码</th>");
		st.append("<th width=\"10%\">级别</th>");
		st.append("<th width=\"20%\">所在单位</th>");
		st.append("<th width=\"20%\">从事专业</th>");
		st.append("<th width=\"10%\">是否参评</th>");
		st.append("<th width=\"16%\">不参评原因</th></thead>");
		int num=0;//
		Long startCol=conEntity.getTotal()*(conEntity.getNum()-1);//下一轮的开始列 
		for(ExpertInfoEntity ex: list){
			startCol++;
			st.append("<input type=\"hidden\" id=\"userId"+startCol+"\" value=\""+ex.getUser_id()+"\"/>");
			st.append("<tr>");
			st.append("<td style='text-align:center'>"+(++num)+"</td>");
			st.append("<td style='text-align:center'>"+ex.getUser_name()+"</td>");
			st.append("<td style='text-align:center'>"+ex.getMobilephone()+"</td>");
			st.append("<td style='text-align:center'>"+(String.valueOf(ex.getGrade()).equals("2")==true?"国家":"地方")+"</td>");
			st.append("<td style='text-align:center'>"+ex.getCompany()+"</td>");
			String temp="";
			for(ExpertMajorEntity major: ex.getMajorList()){//评标专业
				temp+=major.getMajor()+",";
			}
			st.append("<td style='text-align:center'>"+temp.substring(0,temp.length())+"</td>");
			// 只有抽取人自己申请的项目可以自己确认结果
			if (flag)
			{
				st.append("<td><input type=\"checkbox\"  name=join"+startCol+" onClick=\"countJoin(this,'yes',"+startCol+")\"/>是 &nbsp;&nbsp;" +
						"<input type=\"checkbox\" name=join"+startCol+" onClick=\"countJoin(this,'no',"+startCol+")\"/>否</td>");
				st.append("<td><select id=\"select"+startCol+"\" onchange=\"updateReason('"+startCol+"')\" style=\"display:none\" class=\"span2\">");
				st.append("<option value=\"\">—请选择—</option>" );
				st.append("<option value=\"电话未接听\">电话未接听</option>");
				st.append("<option value=\"临时有事\">临时有事</option>");
				st.append("<option value=\"生病\">生病</option>" );
				st.append("<option value=\"出差\">出差</option>" );
				st.append("<option value=\"重大疾病\">重大疾病</option>" );
				st.append("<option value=\"已去世\">已去世</option>" );
				st.append("</select></td>");
			}
			else
			{
				st.append("<td>");
				if ("2".equals(conEntity.getMethod()))
				{
					st.append("短信");
				}
				st.append("未通知</td><td>&nbsp;</td>");
			}
			st.append("</tr>");
		}
		st.append("</table>");
		return st.toString();
	}
	
	/**
	 *  按抽取的轮次分割抽取结果
	 * @param list
	 * @return
	 */
	public static List<List<ResultEntity>> mergeList(List<ResultEntity> list){
		List<List<ResultEntity>> temp= new ArrayList<List<ResultEntity>>();
		HashMap<Long, List<ResultEntity>> map=new HashMap<Long, List<ResultEntity>>();
		if(list!=null){
			for(ResultEntity re:list){
				List<ResultEntity> reList=map.get(re.getSort());
				if(reList==null){//不存在创建一个空的
					reList=new ArrayList<ResultEntity>();
					map.put(re.getSort(),reList);//以轮次作为结果中的索引
				}
				reList.add(re);
			}
		}
		temp.addAll(map.values());//追加所有的结果集
		return temp;
	}
}
