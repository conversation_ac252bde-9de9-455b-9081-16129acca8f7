package com.hzw.sunflower.controller.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@ApiModel(description = "招标公告时间返回参数")
@Data
public class NoticeTimeVO {

    @ApiModelProperty(value = "公告id", position = 1)
    private Long sectionId;

    @ApiModelProperty(value = "公告时间", position = 2)
    private Date noticeTime;
}
