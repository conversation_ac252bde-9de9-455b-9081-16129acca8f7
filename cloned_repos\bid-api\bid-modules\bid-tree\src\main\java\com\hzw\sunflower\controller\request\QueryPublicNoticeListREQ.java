package com.hzw.sunflower.controller.request;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 招标公告列表页请求实体
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@ApiModel(description = "招标公告列表页请求参数")
@Data
public class QueryPublicNoticeListREQ {

    @ApiModelProperty(value = "项目ID", position = 1,required = true)
    private Long projectId;

    @ApiModelProperty(value = "页数", position = 2)
    private Long page;

    @ApiModelProperty(value = "每页大小", position = 3)
    private Long pageSize;

    @ApiModelProperty(value = "排序规则：1 更新时间；2 创建时间", position = 4)
    private Integer sort;

    @ApiModelProperty(value = "项目进度,用来条件查询：0 全部；1 起草中；2 审核中；3 已撤回；4 已审核；5 已发布；6 已退回；", position = 5,required = true)
    private Integer status;

    @ApiModelProperty(value = "模糊搜索关键词", position = 6)
    private String keyForSearching;
    /**
     * 招标公告阶段： 0：默认无阶段； 1：第一轮； 2：第二轮
     */
    @ApiModelProperty(value = "招标公告阶段", position = 6)
    private Integer bidRound;
}
