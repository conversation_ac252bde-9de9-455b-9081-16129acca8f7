package com.hzw.sunflower.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class TendererReport implements Serializable {

    @ApiModelProperty("项目id")
    private Long projectId;

    @ApiModelProperty("标段id")
    private Long sectionId;

    @ApiModelProperty("项目名称")
    private String projectName;

    @ApiModelProperty("项目编号")
    private String purchaseNumber;

    @ApiModelProperty("关联标段包")
    private String packageNumber;

    @ApiModelProperty("包名")
    private String packageName;

    @ApiModelProperty("委托金额")
    private BigDecimal entrustedAmount;

    @ApiModelProperty("委托金额币种")
    private Integer entrustCurrency;

    @ApiModelProperty("委托金额币种")
    private String entrustCurrencyName;

    @ApiModelProperty("采购方式")
    private String purchaseModeName;

    @ApiModelProperty("代理机构")
    private String agentCompany;

    @ApiModelProperty("代理机构项目经理")
    private String agentManager;

    @ApiModelProperty("代理机构项目经理联系方式")
    private String agentManagerContact;

    @ApiModelProperty("已购标供应商数量")
    private Integer applyInfoNum;

    @ApiModelProperty("购标截止时间")
    private String saleEndTime;

    @ApiModelProperty("采购情况是否查看")
    private Integer rightCode;

    @ApiModelProperty("代理机构授权")
    private Integer isEmpower;

    @ApiModelProperty("招标人确认协议")
    private Integer isDefine;

    @ApiModelProperty("开标时间")
    private String submitEndTime;

    @ApiModelProperty("已购标供应商名称")
    private List<ApplyInfo> applyInfos;

    @ApiModelProperty("开标地点")
    private List<String> conferenceAddress;

    @ApiModelProperty("评标时间")
    private List<String> evaluationTime;

    @ApiModelProperty("评标地点")
    private List<String> evaluationAddress;

}
