///***
/// * 首页 - 视图模型
/// */

import 'package:flutter/material.dart';

import 'package:flutter_main_app/src/utils/ApiUtils.dart';
import 'package:flutter_main_app/src/constants/region.dart';

import 'package:flutter_main_app/src/models/common/HttpResponseDataListModel.dart';
import 'package:flutter_main_app/src/models/bulletin/BulletinListQueryModel.dart';
import 'package:flutter_main_app/src/models/bulletin/BulletinListItemModel.dart';

class HomeIndexViewModel extends ChangeNotifier {

  HomeIndexViewModel();

  /// * 页脚信息
  int totalNumber;

  int totalPage;

  /// * 数据列表
  List<BulletinListItemModel> _dataList = [];

  List<BulletinListItemModel> get dataList => _dataList;

  ///***
  /// * 获取数据列表
  /// */
  Future<List<BulletinListItemModel>> fetchDataList() async {
    final HttpResponseDataListModel<BulletinListItemModel> httpResponse =
      await ApiUtils.postBulletin(params);

    List<BulletinListItemModel> newDataList;

    if (httpResponse != null) {
      newDataList = httpResponse.data ?? [];

      totalNumber = httpResponse.totalNumber ?? 0;

      totalPage = httpResponse.totalPage ?? 0;

      for (int i = 0; i < newDataList.length; i++) {
        final String code = newDataList[i].regionCode;

        if (code != null && code.isNotEmpty && code.length == 6) {
          for (int j = 0; j < jiangSuRegionList.length; j++) {
            if (code.substring(0, 4) == jiangSuRegionList[j].code.substring(0, 4)) {
              newDataList[i].regionName = jiangSuRegionList[j].name;

              break;
            }

            newDataList[i].regionName = '江苏省';
          }
        } else {
          newDataList[i].regionName = '江苏省';
        }
      }

      concatDataList(newDataList);
    }

    return newDataList;
  }

  ///***
  /// * 清空数据列表
  /// * 同时通知所有订阅者
  /// */
  void clearDataList() {
    _dataList.clear();

    /// ! handle error: setState() or markNeedsBuild() called during build.
    /// ! 下面的语句在下拉刷新中被调用，需要注释掉，不通知监听者
    // notifyListeners();
  }

  ///***
  /// * 给数据列表赋值
  /// * 同时通知所有订阅者
  /// *
  /// * @param {List<BulletinListItemModel>} newDataList
  /// */
  void setDataList(List<BulletinListItemModel> newDataList) {
    _dataList = newDataList;

    notifyListeners();
  }

  ///***
  /// * 向数据列表末尾添加新的数据列表
  /// * 同时通知所有订阅者
  /// *
  /// * @param {List<BulletinListItemModel>} newDataList
  /// */
  void concatDataList(List<BulletinListItemModel> newDataList) {
    _dataList?.addAll(newDataList);

    notifyListeners();
  }

  ///************************************* 筛选条件 *************************************///

  BulletinListQueryModel _params;

  BulletinListQueryModel get params => _params;

  set params(BulletinListQueryModel params) {
    _params = params;

    /// * 随便做点什么
    params = null;
    /// ! handle error: setState() or markNeedsBuild() called during build.
    /// ! 下面的语句在下拉刷新中被调用，需要注释掉，不通知监听者
    // notifyListeners();
  }

  ///***
  /// * 设置指定的参数对象值
  /// *
  /// * @param {String} paramName
  /// * @param {dynamic} paramValue
  /// */
  void setSpecifiedParam(String paramName, dynamic paramValue) {
    final Map<String, dynamic> tempParams = params.toJson();

    tempParams[paramName] = paramValue;

    params = BulletinListQueryModel.fromJson(tempParams);
  }

}
