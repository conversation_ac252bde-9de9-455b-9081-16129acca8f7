package com.hzw.sunflower.service.impl;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzw.ncc.client.dto.BondReceiveDto;
import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.constant.CommonConstants;
import com.hzw.sunflower.constant.constantenum.*;
import com.hzw.sunflower.constants.NccPushTypeEnum;
import com.hzw.sunflower.controller.request.*;
import com.hzw.sunflower.controller.response.BondRelationDetailVo;
import com.hzw.sunflower.controller.response.BondRelationSectionVo;
import com.hzw.sunflower.controller.response.BondRelationVo;
import com.hzw.sunflower.controller.response.BondWaterAllVo;
import com.hzw.sunflower.dao.BondApplyRelationMapper;
import com.hzw.sunflower.dao.BondApplyRefundMapper;
import com.hzw.sunflower.dao.BondOfflineCompanyMapper;
import com.hzw.sunflower.dao.BondRelationMapper;
import com.hzw.sunflower.dao.OfflineCompanyMapper;
import com.hzw.sunflower.dto.*;
import com.hzw.sunflower.entity.*;
import com.hzw.sunflower.entity.condition.BondRelationCondition;
import com.hzw.sunflower.exception.ExceptionEnum;
import com.hzw.sunflower.exception.SunFlowerException;
import com.hzw.sunflower.service.*;
import com.hzw.sunflower.util.BeanListUtil;
import com.hzw.sunflower.util.BondUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 保证金关联关系表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-27
 */
@Service
@Slf4j
public class BondRelationServiceImpl extends ServiceImpl<BondRelationMapper, BondRelation> implements BondRelationService {

    @Autowired
    BondWaterService bondWaterService;

    @Autowired
    ProjectBidSectionService projectBidSectionService;

    @Autowired
    BondChangeRecordsService bondChangeRecordsService;

    @Autowired
    CompanyService companyService;

    @Autowired
    BondSplitService bondSplitService;

    @Autowired
    BondApplyRelationService bondApplyRelationService;

    @Autowired
    BondRefundService bondRefundService;

    @Autowired
    CommonSmsService commonSmsService;

    @Autowired
    NccBondPushLogService nccBondPushLogService;

    @Autowired
    private CommonOpenService commonOpenService;

    @Autowired
    private BondWaterlabelService bondWaterlabelService;

    @Autowired
    private CommonMqService commonMqService;

    @Autowired
    private BondApplyRelationMapper bondApplyRelationMapper;
    @Autowired
    private BondApplyRefundMapper bondApplyRefundMapper;

    @Autowired
    private ProjectService projectService;

    @Autowired
    private BondOfflineCompanyMapper bondOfflineCompanyMapper;

    @Autowired
    private OfflineCompanyMapper offlineCompanyMapper;


    /**
     * 分页查询供应商关注单包关联情况
     * @param condition
     * @param jwtUser
     * @return
     */
    @Override
    public IPage<BondRelationSectionVo> getSectionRelationList(BondRelationCondition condition, JwtUser jwtUser) {
        IPage<BondRelationSectionVo> page = condition.buildPage();
        condition.setUserId(jwtUser.getUserId());
        page = this.baseMapper.getSectionRelationList(page, condition);
        // 查询标段保证金关联情况 并返回对应的状态
        List<BondRelationSectionVo> list = page.getRecords();
        list.forEach((item) -> {
            // 关联状态
            List<BondRelation> relationList = this.baseMapper.selectList(new LambdaQueryWrapper<BondRelation>()
                    .eq(BondRelation::getProjectId, item.getProjectId())
                    .eq(BondRelation::getCompanyId, condition.getCompanyId())
                    .eq(BondRelation::getSectionId, item.getId()));
            if (relationList.size() > 0) {
                // 多标段关联状态必定统一，故取第一条的状态即可
                item.setRelationStatus(relationList.get(0).getStatus());
            } else {
                item.setRelationStatus(RelationStatusEnum.NOT_RELATE.getType());
            }
            // 取消关联按钮展示 1、已关联状态且(当前项目标包未过开标时间或已异常) 2、当前包关联的所有流水均只关联了当前标包
            if (item.getRelationStatus().equals(RelationStatusEnum.HAS_RELATE.getType())
                    && (item.getSubmitEndTimeType().equals(EndTimeTypeEnum.FURTHER_NOTICE.getType()) || (DateUtil.compare(new Date(), item.getSubmitEndTime()) <= 0) || !AbnormalStatusEnum.NORMAL.getType().equals(item.getAbnormalStatus()))
                    && checkSectionRelationOnly(item.getId(), jwtUser.getCompanyId())) {
                item.setShowCancelBtn(CommonConstants.YES);
            } else {
                item.setShowCancelBtn(CommonConstants.NO2);
            }
        });
        page.setRecords(list);
        return page;
    }

    /**
     * 校验当前包关联的所有流水均只关联了当前标包
     * @param sectionId
     * @param companyId
     * @return
     */
    private Boolean checkSectionRelationOnly(Long sectionId, Long companyId) {
        Integer count = this.getBaseMapper().checkSectionRelationOnly(sectionId, companyId);
        return count == 0;
    }


    /**
     * 供应商关联情况和确认关联情况列表
     * @param condition
     * @return
     */
    @Override
    public BondRelationVo getSectionRelationDetail(BondRelationCondition condition) {
        BondRelationVo vo = new BondRelationVo();
        // 根据包查询关联/确认关联流水
        vo.setWaterList(bondWaterService.getWaterListBySectionId(Long.parseLong(condition.getSectionId()), condition.getRelationStatus(), condition.getCompanyId()));
        List<Long> waterIds = vo.getWaterList().stream()
                .map(m -> {
                    return m.getId();
                }).collect(Collectors.toList());
        if (waterIds.size() > 0) {
            vo.setSectionList(getSectionListByWaterIds(waterIds, condition.getRelationStatus()));
        } else {
            vo.setSectionList(new ArrayList<>());
        }
        return vo;
    }

    /**
     * 根据流水ids查询关联标段
     * @param waterIds
     * @param relationStatus
     * @return
     */
    @Override
    public List<BondRelationSectionVo> getSectionListByWaterIds(List<Long> waterIds, Integer relationStatus) {
        if (waterIds != null && waterIds.size() > 0) {
            return this.baseMapper.getSectionListByWaterIds(waterIds, relationStatus);
        } else {
            return new ArrayList<>();
        }
    }

    /**
     * 根据流水ids查询关联标段（已办）
     * @param waterIds
     * @param relationStatus
     * @return
     */
    @Override
    public List<BondRelationSectionVo> getSectionListByWaterIdsCompleted(List<Long> waterIds, Integer relationStatus,Date createdTime) {
        if (waterIds != null && waterIds.size() > 0) {
            return this.baseMapper.getSectionListByWaterIdsCompleted(waterIds, relationStatus,createdTime);
        } else {
            return new ArrayList<>();
        }
    }
    /**
     * 校验标段
     * @param sectionIds
     * @param companyId
     */
    @Override
    public void checkSection(String sectionIds, Long companyId) {
        String[] arr = sectionIds.split(",");
        for (String sectionId : arr) {
            BondRelationSectionVo section = this.getBaseMapper().getSectionById(Long.parseLong(sectionId), companyId);
            if (section == null) {
                throw new SunFlowerException(ExceptionEnum.PROJECT_BID_SECTIONS_NOT_FOUND, ExceptionEnum.PROJECT_BID_SECTIONS_NOT_FOUND.getMessage());
            } else if (section.getSubmitEndTimeType().equals(EndTimeTypeEnum.CLEAR_TIME.getType())
                    && (DateUtil.compare(new Date(), section.getSubmitEndTime()) > 0)) {
                throw new SunFlowerException(ExceptionEnum.OVER_BID_OPEN_TIME, ExceptionEnum.OVER_BID_OPEN_TIME.getMessage());
            } else if (section.getDownloadFlag().equals(CommonConstants.NO)) {
                throw new SunFlowerException(ExceptionEnum.WITHOUT_DOWNLOAD_FLAG, ExceptionEnum.WITHOUT_DOWNLOAD_FLAG.getMessage());
            }
        }
    }

    /**
     * 单包取消关联、确认关联弹框取消关联
     * @param condition
     * @return
     */
    @Override
    public Boolean cancelRelation(BondRelationCondition condition) {
//        // 校验标段开标时间和购标状态
//        checkSection(condition.getSectionId(), condition.getCompanyId());
        // 删除关联,如果waterId不为空则为确认关联取消，否则为单包已关联取消
        if (null != condition.getWaterId()) {
            return this.remove(new LambdaQueryWrapper<BondRelation>().eq(BondRelation::getWaterId, condition.getWaterId()));
        } else {
            // 单包取消 校验此包关联流水是否关联了其他包
            if (!checkSectionRelationOnly(Long.parseLong(condition.getSectionId()), condition.getCompanyId())) {
                throw new SunFlowerException(ExceptionEnum.ERROR_OPERATION, ExceptionEnum.ERROR_OPERATION.getMessage());
            }
            //判断是否存在退还数据
            if (!checkSectionCompanyReturn(Long.parseLong(condition.getSectionId()), condition.getCompanyId())) {
                throw new SunFlowerException(ExceptionEnum.WATER_IN_RETURN, ExceptionEnum.WATER_IN_RETURN.getMessage());
            }
            // 删除此包的拆分数据
            bondSplitService.remove(new LambdaQueryWrapper<BondSplit>().eq(BondSplit::getCompanyId, condition.getCompanyId()).eq(BondSplit::getSectionId, condition.getSectionId()));
            return this.remove(new LambdaQueryWrapper<BondRelation>().eq(BondRelation::getCompanyId, condition.getCompanyId()).eq(BondRelation::getSectionId, condition.getSectionId()));
        }
    }

    /**
     * 判断是否存在退还流程中的数据
     * @param sectionId
     * @param companyId
     * @return
     */
    private boolean checkSectionCompanyReturn(Long sectionId, Long companyId) {
        List<Integer> status = new ArrayList<>();
        status.add(BondRefundStatusEnum.RETURNED.getType());
        status.add(BondRefundStatusEnum.WITHDRAWED.getType());
        List<BondRefund> refundList = bondRefundService.list(new LambdaQueryWrapper<BondRefund>()
                .eq(BondRefund::getCompanyId, companyId)
                .eq(BondRefund::getSectionId, sectionId)
                .notIn(BondRefund::getStatus, status));
        return refundList.size() == 0;
    }

    /**
     * 确认关联
     * @param condition
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean confirmRelation(BondRelationCondition condition) {
        List<Long> sectionIdList = new ArrayList<>();
        StringBuffer sb = new StringBuffer();
        for (BondSectionReq req : condition.getSectionIdList()) {
            sb.append(req.getSectionId()).append(",");
            sectionIdList.add(req.getSectionId());
        }
        sb.deleteCharAt(sb.length() - 1);
        // 判断是否可以线上保证金退还
        if (commonOpenService.checkOnlineBondRefund(sectionIdList,condition.getCompanyId())) {
            throw new SunFlowerException(ExceptionEnum.HAVE_OFFLINE_BOND_REFUND, ExceptionEnum.HAVE_OFFLINE_BOND_REFUND.getMessage());
        }
        // 判断异常流水
        if (bondWaterlabelService.chekecWaterLabel(new ArrayList<>(){{add(condition.getWaterId());}}) > 0) {
            throw new SunFlowerException(ExceptionEnum.WATER_ERROR, ExceptionEnum.WATER_ERROR.getMessage());
        }

        // 校验标段开标时间和购标状态
        checkSection(sb.toString(), condition.getCompanyId());
        // 修改关联状态
        boolean result = this.update(new LambdaUpdateWrapper<BondRelation>().set(BondRelation::getStatus, RelationStatusEnum.HAS_RELATE.getType())
                .eq(BondRelation::getWaterId, condition.getWaterId()));
        // 拆分金额
        List<Long> waterIdList = new ArrayList<>();
        waterIdList.add(condition.getWaterId());
        bondSplitService.addSplit(condition.getCompanyId(), condition.getSectionIdList(), waterIdList);

        // 推送ncc 判断操作时间是否隔月
        BondWater bondWater = bondWaterService.getById(condition.getWaterId());
        if (BondUtils.isOverMonth(bondWater.getDate())) {
            // 记录调整日志、推送记录 单笔流水
//            List<BondAdjustDto> dtoList = bondSplitService.getBondAdjustDto(condition.getWaterId());
            BondWaterToNccDto item = BeanListUtil.convert(bondWater,BondWaterToNccDto.class);
            List<BondSplitDto> historySplitList =  bondSplitService.queryLatestSplit(bondWater.getId());
            bondSplitService.getBondAdjustDto(item,historySplitList);
//            for (BondAdjustDto dto : dtoList) {
//                NccBondPushLog pushLog = new NccBondPushLog();
//                pushLog.setBillId(dto.getBillId());
//                pushLog.setPushResult(CommonConstants.NO2);
//                pushLog.setRequestData(JSONObject.toJSONString(dto));
//                pushLog.setPushType(NccPushTypeEnum.ADJUST.getType());
//                nccBondPushLogService.save(pushLog);
//            }
        } else {
            // 获取待推送收款数据 记入推送记录表
            BondReceiveDto dto = bondSplitService.getBondReceiveDtoByWaterId(condition.getWaterId());
            NccBondPushLog pushLog = new NccBondPushLog();
            pushLog.setBillId(dto.getBillId());
            pushLog.setPushResult(CommonConstants.NO2);
            pushLog.setRequestData(JSONObject.toJSONString(dto));
            pushLog.setPushType(NccPushTypeEnum.RECEIVE.getType());
            nccBondPushLogService.save(pushLog);
        }
        return result;
    }

    /**
     * 供应商/项目经理 关联详情
     * @param request
     * @return
     */
    @Override
    public BondRelationDetailVo getRelationDetail (BondRelationReq request) {
        Integer relationStatus = request.getRelationStatus() == null ? RelationStatusEnum.HAS_RELATE.getType() : request.getRelationStatus();
        BondRelationDetailVo vo = new BondRelationDetailVo();
        //全流程线下项目取保证金线下项目供应商表，非全流程线下项目取企业表
        Project project = projectService.getById(request.getProjectId());
        if (ProjectOperationFlowEnum.Flow3.getValue().equals(project.getOperationFlow())) {
            // 根据供应商id获取企业名称
            BondOfflineCompany bondOfflineCompany = bondOfflineCompanyMapper.selectById(request.getBondOfflineCompanyId());
            OfflineCompany offlineCompany = offlineCompanyMapper.selectById(bondOfflineCompany.getOfflineCompanyId());
            String companyName = bondOfflineCompany.getApplyCompanyName();
            // 根据企业名称获取未关联流水
            List<BondWaterAllVo> waterList = bondWaterService.getWaterListByCompanyNameAll(companyName);
            // 根据关联状态查询标段流水
            List<BondWater> relatedWaterList = bondWaterService.getWaterListBySectionId(request.getSectionId(), relationStatus, bondOfflineCompany.getOfflineCompanyId());

            // 根据项目id 查询所有待关联且已购标的标段
            List<BondRelationSectionVo> sectionList = this.baseMapper.getNotRelateSectionOffline(request.getProjectId(), offlineCompany.getCompanyName());

            List<BondRelationSectionVo> relationSectionList = new ArrayList<>();
            // 查询已关联标段
            if (relatedWaterList.size() > 0) {
                List<Long> waterIds = relatedWaterList.stream()
                        .map(m -> {
                            return m.getId();
                        }).collect(Collectors.toList());
                relationSectionList = getSectionListByWaterIds(waterIds, relationStatus);
                // 查询已关联标段分配的金额
                for (BondRelationSectionVo sectionVo : relationSectionList) {
                    BondSplit split = bondSplitService.listBySectionId(sectionVo.getId(), bondOfflineCompany.getOfflineCompanyId());
                    sectionVo.setAmount(split.getAmount());
                }
            } else {
                // 未关联时，默认选中当前标段
                relationSectionList.add(this.getBaseMapper().getSectionByIdOffline(request.getSectionId(), bondOfflineCompany.getOfflineCompanyId()));
                sectionList = sectionList.stream().filter(s -> !s.getId().equals(request.getSectionId())).collect(Collectors.toList());
            }

            vo.setWaterList(waterList);
            vo.setRelatedWaterList(relatedWaterList);
            vo.setSectionList(sectionList);
            vo.setRelatedSectionList(relationSectionList);
        } else {
        // 根据供应商id获取企业名称
        Company company = companyService.getById(request.getCompanyId());
        String companyName = company.getCompanyName();
        // 当操作人为项目经理时 从申请关联数据里取企业名称
        if (request.getOperateRole().equals(OperateRoleEnum.MANAGE.getType())) {
            BondApplyRelation bondApplyRelation = bondApplyRelationService.getOne(new LambdaQueryWrapper<BondApplyRelation>().eq(BondApplyRelation::getCompanyId, request.getCompanyId())
                    .eq(BondApplyRelation::getSectionId, request.getSectionId()).eq(BondApplyRelation::getStatus, ApplyRelationStatusEnum.HANDLED.getType())
                    .eq(BondApplyRelation::getType, ApplyTypeEnum.RELATION_APPLY.getType()).orderByDesc(BondApplyRelation::getApplyTime).last("limit 1"));
            if (bondApplyRelation != null && bondApplyRelation.getContent() != null) {
                companyName = bondApplyRelation.getContent();
            }
        }
        // 根据企业名称获取未关联流水
        List<BondWaterAllVo> waterList = bondWaterService.getWaterListByCompanyNameAll(companyName);
        // 根据关联状态查询标段流水
        List<BondWater> relatedWaterList = bondWaterService.getWaterListBySectionId(request.getSectionId(), relationStatus, request.getCompanyId());

        // 根据项目id 查询所有待关联且已购标的标段 是否已开标根据操作角色决定
        List<BondRelationSectionVo> sectionList = this.baseMapper.getNotRelateSection(request.getProjectId(), request.getCompanyId(), request.getOperateRole());

        List<BondRelationSectionVo> relationSectionList = new ArrayList<>();
        // 查询已关联标段
        if (relatedWaterList.size() > 0) {
            List<Long> waterIds = relatedWaterList.stream()
                    .map(m -> {
                        return m.getId();
                    }).collect(Collectors.toList());
            relationSectionList = getSectionListByWaterIds(waterIds, relationStatus);
            // 查询已关联标段分配的金额
            for (BondRelationSectionVo sectionVo : relationSectionList) {
                BondSplit split = bondSplitService.listBySectionId(sectionVo.getId(), request.getCompanyId());
                sectionVo.setAmount(split.getAmount());
            }
        } else {
            // 未关联时，默认选中当前标段
            relationSectionList.add(this.getBaseMapper().getSectionById(request.getSectionId(), request.getCompanyId()));
            sectionList = sectionList.stream().filter(s -> !s.getId().equals(request.getSectionId())).collect(Collectors.toList());
        }

        vo.setWaterList(waterList);
        vo.setRelatedWaterList(relatedWaterList);
        vo.setSectionList(sectionList);
        vo.setRelatedSectionList(relationSectionList);
    }
        return vo;
    }

    /**
     * 运管处关联详情
     * @param request
     * @return
     */
    @Override
    public BondRelationDetailVo getRelationDetailByAgent(BondRelationAgentReq request) {
        BondRelationDetailVo vo = new BondRelationDetailVo();
        List<BondWater> relatedWaterList = new ArrayList<>();
        // 默认选中当前流水
        BondWater water = bondWaterService.getById(request.getWaterId());
        relatedWaterList.add(water);
        // 根据当前流水户名查询未关联流水 过滤当前流水
        List<BondWaterAllVo> waterList = bondWaterService.getWaterListByCompanyNameAll(water.getCompanyName());
        waterList = waterList.stream().filter(w -> !w.getId().equals(water.getId())).collect(Collectors.toList());
        vo.setWaterList(waterList);
        vo.setRelatedWaterList(relatedWaterList);
        return vo;
    }

    /**
     * 运管处搜索待关联标段
     * @param request
     * @return
     */
    @Override
    public List<BondRelationSectionVo> getSectionListByProjectId(BondRelationAgentReq request) {
        return this.baseMapper.getNotRelateSection(request.getProjectId(), request.getCompanyId(), OperateRoleEnum.OPERATOR.getType());
    }

    /**
     * 运管处关联详情-模糊搜索关注项目
     * @param request
     * @return
     */
    @Override
    public List<BondRelationSectionVo> getSectionListByPurchaseNum(BondRelationAgentReq request) {
        List<BondRelationSectionVo> list = new ArrayList<>();
        List<BondRelationSectionVo> voList = this.baseMapper.getSectionListByPurchaseNum(request.getPurchaseNumber(), request.getCompanyId());
        return voList;
    }

    /**
     * 关联详情-关联
     * @param req
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean addRelation(AddRelationReq req) {
        //区分项目流程
        Project project = projectService.getById(req.getSectionIdList().get(0).getProjectId());
        //全流程线下项目取保证金线下项目供应商表，非全流程线下项目取企业表
        if (ProjectOperationFlowEnum.Flow3.getValue().equals(project.getOperationFlow())) {
            BondOfflineCompany bondOfflineCompany = bondOfflineCompanyMapper.selectById(req.getBondOfflineCompanyId());
            // 判断是否可以线上保证金退还
            List<Long> sectionIdList = req.getSectionIdList().stream().map(BondSectionReq::getSectionId).collect(Collectors.toList());
            // 判断流水是否处于退还状态
            if (bondRefundService.judgeWaterInReturn(req.getWaterIdList())) {
                throw new SunFlowerException(ExceptionEnum.WATER_IN_RETURN, ExceptionEnum.WATER_IN_RETURN.getMessage());
            }
            // 校验标段和流水是否已关联
            if (checkHasRelation(req.getWaterIdList(), sectionIdList, bondOfflineCompany.getOfflineCompanyId(), req.getFormerSectionIdList(), req.getFormerWaterIdList())) {
                throw new SunFlowerException(ExceptionEnum.HAS_RELATION, ExceptionEnum.HAS_RELATION.getMessage());
            }
            // 判断异常流水
            if (bondWaterlabelService.chekecWaterLabel(req.getWaterIdList()) > 0) {
                throw new SunFlowerException(ExceptionEnum.WATER_ERROR, ExceptionEnum.WATER_ERROR.getMessage());
            }
            Boolean result = true;
            // 解析参数
            req.setOfflineCompanyId(bondOfflineCompany.getOfflineCompanyId());
            MergeToRelationDto dto = mergeToRelationListOffline(req);
            //判断是否为再次关联前先查询金额拆分信息历史
            List<BondSplitHistoryDto> historyDtoList = queryHistorySplit(req.getWaterIdList());
            // 判断是否为再次关联
            if (req.getFormerSectionIdList() != null && req.getFormerSectionIdList().size() > 0) {
                // 根据标包删除原有关联、金额拆分数据
                this.remove(new LambdaQueryWrapper<BondRelation>().eq(BondRelation::getCompanyId, bondOfflineCompany.getOfflineCompanyId()).in(BondRelation::getSectionId, req.getFormerSectionIdList()));
                bondSplitService.remove(new LambdaQueryWrapper<BondSplit>().eq(BondSplit::getCompanyId, bondOfflineCompany.getOfflineCompanyId()).in(BondSplit::getSectionId, req.getFormerSectionIdList()));
            }
            //删除关联待确认数据
            deleteRelation(req.getWaterIdList());
            //删除申请退还被退回数据
            deleteApplyRefundReturn(req.getWaterIdList());
            result = this.saveBatch(dto.getRelationList());
            // 拆分金额
            bondSplitService.addSplit(bondOfflineCompany.getOfflineCompanyId(), req.getSectionIdList(), req.getWaterIdList());
            // 推送ncc
            bondSplitService.pushNccData(historyDtoList, req.getType(), bondOfflineCompany.getOfflineCompanyId());
            return result;
        } else {
        // 判断是否可以线上保证金退还
        List<Long> sectionIdList = req.getSectionIdList().stream().map(BondSectionReq::getSectionId).collect(Collectors.toList());
        if (commonOpenService.checkOnlineBondRefund(sectionIdList,req.getCompanyId())) {
            throw new SunFlowerException(ExceptionEnum.HAVE_OFFLINE_BOND_REFUND, ExceptionEnum.HAVE_OFFLINE_BOND_REFUND.getMessage());
        }
        // 判断流水是否处于退还状态
        if (bondRefundService.judgeWaterInReturn(req.getWaterIdList())) {
            throw new SunFlowerException(ExceptionEnum.WATER_IN_RETURN, ExceptionEnum.WATER_IN_RETURN.getMessage());
        }
        // 校验标段和流水是否已关联
        if (checkHasRelation(req.getWaterIdList(), sectionIdList, req.getCompanyId(), req.getFormerSectionIdList(), req.getFormerWaterIdList())) {
            throw new SunFlowerException(ExceptionEnum.HAS_RELATION, ExceptionEnum.HAS_RELATION.getMessage());
        }
        // 判断异常流水
        if (bondWaterlabelService.chekecWaterLabel(req.getWaterIdList()) > 0) {
            throw new SunFlowerException(ExceptionEnum.WATER_ERROR, ExceptionEnum.WATER_ERROR.getMessage());
        }
        Boolean result = true;
        // 解析参数
        MergeToRelationDto dto = mergeToRelationList(req);
        //判断是否为再次关联前先查询金额拆分信息历史
        List<BondSplitHistoryDto> historyDtoList = queryHistorySplit(req.getWaterIdList());
        // 判断是否为再次关联
        if (req.getFormerSectionIdList() != null && req.getFormerSectionIdList().size() > 0) {
            // 根据标包删除原有关联、金额拆分数据
            this.remove(new LambdaQueryWrapper<BondRelation>().eq(BondRelation::getCompanyId, req.getCompanyId()).in(BondRelation::getSectionId, req.getFormerSectionIdList()));
            bondSplitService.remove(new LambdaQueryWrapper<BondSplit>().eq(BondSplit::getCompanyId, req.getCompanyId()).in(BondSplit::getSectionId, req.getFormerSectionIdList()));
        }
        //删除关联待确认数据
        deleteRelation(req.getWaterIdList());
        //删除申请退还被退回数据
        deleteApplyRefundReturn(req.getWaterIdList());
        result = this.saveBatch(dto.getRelationList());
        // 拆分金额
        bondSplitService.addSplit(req.getCompanyId(), req.getSectionIdList(), req.getWaterIdList());
        // 推送ncc
        bondSplitService.pushNccData(historyDtoList, req.getType(), req.getCompanyId());
        return result;
        }
    }

    /**
     * 删除关联待确认的数据
     * @param waterIdList
     */
    private void deleteRelation(List<Long> waterIdList) {
        LambdaQueryWrapper<BondRelation> bondRelationLambdaQueryWrapper = new LambdaQueryWrapper<>();
        bondRelationLambdaQueryWrapper.in(BondRelation::getWaterId, waterIdList);
        bondRelationLambdaQueryWrapper.eq(BondRelation::getStatus,RelationStatusEnum.CONFIRM_RELATE.getType());
        List<BondRelation> bondRelationList = this.list(bondRelationLambdaQueryWrapper);
        if (bondRelationList.size() > 0) {
            List<Long> relationIds = bondRelationList.stream().map(BondRelation::getId).collect(Collectors.toList());
            this.removeBatchByIds(relationIds);
        }
    }

    /**
     * 删除未关联流水申请退还被退回的数据
     * @param waterIdList
     */
    private void deleteApplyRefundReturn(List<Long> waterIdList) {
        LambdaQueryWrapper<BondApplyRefund> bondApplyRefundLambdaQueryWrapper = new LambdaQueryWrapper<>();
        bondApplyRefundLambdaQueryWrapper.in(BondApplyRefund::getWaterId, waterIdList);
        bondApplyRefundLambdaQueryWrapper.eq(BondApplyRefund::getStatus,BondApplyRefundStatus.RETURNED.getType());
        List<BondApplyRefund> bondApplyRefundList = bondApplyRefundMapper.selectList(bondApplyRefundLambdaQueryWrapper);
        if (bondApplyRefundList.size() > 0) {
            List<Long> applyRefundIds = bondApplyRefundList.stream().map(BondApplyRefund::getId).collect(Collectors.toList());
            bondApplyRefundMapper.deleteBatchIds(applyRefundIds);
        }
    }

    /**
     * 查询历史最新一组的流水拆分信息，调账用
     * @param waterIds
     * @return
     */
    private List<BondSplitHistoryDto> queryHistorySplit(List<Long> waterIds){
        List<BondSplitHistoryDto> historyDtoList = new ArrayList<>();
        for(Long waterId:waterIds){
            BondSplitHistoryDto historyDto = new BondSplitHistoryDto();
            List<BondSplitDto> splits = bondSplitService.queryLatestSplit(waterId);
            historyDto.setWaterId(waterId);
            historyDto.setBondSplitDtoList(splits);
            historyDtoList.add(historyDto);
        }
        return historyDtoList;
    }

    /**
     * 查询历史最新一组的异常流水拆分信息，调账用
     * @param waterIds
     * @return
     */
    private List<BondSplitHistoryDto> queryHistorySplitByRelationId(List<Long> waterIds,Long applyId){
        List<BondSplitHistoryDto> historyDtoList = new ArrayList<>();
        for(Long waterId:waterIds){
            BondSplitHistoryDto historyDto = new BondSplitHistoryDto();
            List<BondSplitDto> splits = bondSplitService.queryLatestSplitByRelationId(waterId,applyId);
            historyDto.setWaterId(waterId);
            historyDto.setBondSplitDtoList(splits);
            historyDtoList.add(historyDto);
        }
        return historyDtoList;
    }

    /**
     * 项目经理校验是否可以进入关联页面
     * @param request
     * @return
     */
    @Override
    public Boolean validRelationSectionInRefund(BondRelationReq request) {
        // 查询此标段关联情况
        List<BondWater> waterList = bondWaterService.getWaterListBySectionId(request.getSectionId(), RelationStatusEnum.HAS_RELATE.getType(), request.getCompanyId());
        // 未关联直接返回
        if (waterList.size() == 0) {
            return true;
        }
        List<Long> waterIds = waterList.stream()
                .map(m -> {
                    return m.getId();
                }).collect(Collectors.toList());
        List<BondRelationSectionVo> sectionList = getSectionListByWaterIds(waterIds, RelationStatusEnum.HAS_RELATE.getType());
        List<Long> sectionIds = sectionList.stream()
                .map(m -> {
                    return m.getId();
                }).collect(Collectors.toList());
        // 查询该企业、标段下是否已有退还流程
        List<BondRefund> list = bondRefundService.list(new LambdaQueryWrapper<BondRefund>().eq(BondRefund::getCompanyId, request.getCompanyId()).in(BondRefund::getSectionId, sectionIds));
        if (list.size() > 0) {
            return false;
        } else {
            return true;
        }
    }

    /**
     * 组装关联列表
     * @param req
     * @return
     */
    public MergeToRelationDto mergeToRelationList (AddRelationReq req) {
        MergeToRelationDto dto = new MergeToRelationDto();
        List<BondRelation> relationList = new ArrayList<>();
        for (BondSectionReq sectionReq : req.getSectionIdList()) {
            for (Long waterId : req.getWaterIdList()) {
                // 组装关联列表
                BondRelation relation = new BondRelation();
                relation.setCompanyId(req.getCompanyId());
                relation.setSectionId(sectionReq.getSectionId());
                relation.setProjectId(sectionReq.getProjectId());
                relation.setWaterId(waterId);
                relation.setStatus(RelationStatusEnum.HAS_RELATE.getType());
                relation.setIsAuto(CommonConstants.NO2);
                relationList.add(relation);
            }
        }
        dto.setRelationList(relationList);
        return dto;
    }

    /**
     * 组装关联列表
     * @param req
     * @return
     */
    public MergeToRelationDto mergeToRelationListOffline (AddRelationReq req) {
        MergeToRelationDto dto = new MergeToRelationDto();
        List<BondRelation> relationList = new ArrayList<>();
        for (BondSectionReq sectionReq : req.getSectionIdList()) {
            for (Long waterId : req.getWaterIdList()) {
                // 组装关联列表
                BondRelation relation = new BondRelation();
                relation.setCompanyId(req.getOfflineCompanyId());
                relation.setSectionId(sectionReq.getSectionId());
                relation.setProjectId(sectionReq.getProjectId());
                relation.setWaterId(waterId);
                relation.setStatus(RelationStatusEnum.HAS_RELATE.getType());
                relation.setIsAuto(CommonConstants.NO2);
                relationList.add(relation);
            }
        }
        dto.setRelationList(relationList);
        return dto;
    }

    /**
     * 新增异常关联
     * @param req
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean addExceptionRelation(ExceptionRelationReq req) {
        //区分项目流程
        Project project = projectService.getById(req.getSectionIdList().get(0).getProjectId());
        //全流程线下项目取保证金线下项目供应商表，非全流程线下项目取企业表
        if (ProjectOperationFlowEnum.Flow3.getValue().equals(project.getOperationFlow())) {
            BondOfflineCompany bondOfflineCompany = bondOfflineCompanyMapper.selectById(req.getBondOfflineCompanyId());
            // 判断是否可以线上保证金退还
            List<Long> sectionIdList = req.getSectionIdList().stream().map(BondSectionReq::getSectionId).collect(Collectors.toList());
            // 判断流水是否处于退还状态
            if (bondRefundService.judgeWaterInReturn(req.getWaterIdList())) {
                throw new SunFlowerException(ExceptionEnum.WATER_IN_RETURN, ExceptionEnum.WATER_IN_RETURN.getMessage());
            }
            // 校验标段和流水是否已关联
            if (checkHasRelation(req.getWaterIdList(), sectionIdList, bondOfflineCompany.getOfflineCompanyId(), req.getFormerSectionIdList(), req.getFormerWaterIdList())) {
                throw new SunFlowerException(ExceptionEnum.HAS_RELATION, ExceptionEnum.HAS_RELATION.getMessage());
            }
            // 解析参数
            req.setOfflineCompanyId(bondOfflineCompany.getOfflineCompanyId());
            MergeToRelationDto dto = mergeToRelationListExceptionOffline(req);
            // 判断是否为再次关联
            if (req.getFormerSectionIdList() != null && req.getFormerSectionIdList().size() > 0) {
                // 根据标包删除原有关联、金额拆分数据
                this.remove(new LambdaQueryWrapper<BondRelation>().eq(BondRelation::getCompanyId, bondOfflineCompany.getOfflineCompanyId()).in(BondRelation::getSectionId, req.getFormerSectionIdList()));
                bondSplitService.remove(new LambdaQueryWrapper<BondSplit>().eq(BondSplit::getCompanyId, bondOfflineCompany.getOfflineCompanyId()).in(BondSplit::getSectionId, req.getFormerSectionIdList()));
            }
            //删除关联待确认数据
            deleteRelation(req.getWaterIdList());
            //删除申请退还被退回数据
            deleteApplyRefundReturn(req.getWaterIdList());
            this.saveBatch(dto.getRelationList());
            // 添加异常申请记录
            LambdaQueryWrapper<BondApplyRelation> queryWrapper = new LambdaQueryWrapper<BondApplyRelation>()
                    .eq(BondApplyRelation::getCompanyId, bondOfflineCompany.getOfflineCompanyId())
                    .eq(BondApplyRelation::getType, ApplyRelationTypeEnum.ABNORMAL.getType())
                    .eq(BondApplyRelation::getProjectId, req.getExceptionProjectId())
                    .eq(BondApplyRelation::getSectionId, req.getExceptionSectionId());
            List<BondApplyRelation> bondApplyRelationList = bondApplyRelationService.list(queryWrapper);
            if (bondApplyRelationList.size() > 0) {
                bondApplyRelationService.remove(queryWrapper);
            }
            BondApplyRelation bondApplyRelation = new BondApplyRelation();
            bondApplyRelation.setCompanyId(bondOfflineCompany.getOfflineCompanyId());
            bondApplyRelation.setProjectId(req.getExceptionProjectId());
            bondApplyRelation.setSectionId(req.getExceptionSectionId());
            bondApplyRelation.setType(ApplyRelationTypeEnum.ABNORMAL.getType());
            boolean result = bondApplyRelationService.save(bondApplyRelation);
            //先删除所有拆分表关联id
            bondSplitService.updateRelationIdNull(req.getWaterIdList());
            for(Long waterId:req.getWaterIdList()){
                //关联拆分表最新的拆分数据
                bondSplitService.updateRelationId(waterId,bondApplyRelation.getId());
            }
            // 拆分金额
            bondSplitService.addSplit(bondOfflineCompany.getOfflineCompanyId(), req.getSectionIdList(), req.getWaterIdList());
            return result;
        } else {
        // 判断是否可以线上保证金退还
        List<Long> sectionIdList = req.getSectionIdList().stream().map(BondSectionReq::getSectionId).collect(Collectors.toList());
        if (commonOpenService.checkOnlineBondRefund(sectionIdList,req.getCompanyId())) {
            throw new SunFlowerException(ExceptionEnum.HAVE_OFFLINE_BOND_REFUND, ExceptionEnum.HAVE_OFFLINE_BOND_REFUND.getMessage());
        }
        // 判断流水是否处于退还状态
        if (bondRefundService.judgeWaterInReturn(req.getWaterIdList())) {
            throw new SunFlowerException(ExceptionEnum.WATER_IN_RETURN, ExceptionEnum.WATER_IN_RETURN.getMessage());
        }
        // 校验标段和流水是否已关联
        if (checkHasRelation(req.getWaterIdList(), sectionIdList, req.getCompanyId(), req.getFormerSectionIdList(), req.getFormerWaterIdList())) {
            throw new SunFlowerException(ExceptionEnum.HAS_RELATION, ExceptionEnum.HAS_RELATION.getMessage());
        }
        // 解析参数
        MergeToRelationDto dto = mergeToRelationListException(req);
        // 判断是否为再次关联
        if (req.getFormerSectionIdList() != null && req.getFormerSectionIdList().size() > 0) {
            // 根据标包删除原有关联、金额拆分数据
            this.remove(new LambdaQueryWrapper<BondRelation>().eq(BondRelation::getCompanyId, req.getCompanyId()).in(BondRelation::getSectionId, req.getFormerSectionIdList()));
            bondSplitService.remove(new LambdaQueryWrapper<BondSplit>().eq(BondSplit::getCompanyId, req.getCompanyId()).in(BondSplit::getSectionId, req.getFormerSectionIdList()));
        }
        //删除关联待确认数据
        deleteRelation(req.getWaterIdList());
        //删除申请退还被退回数据
        deleteApplyRefundReturn(req.getWaterIdList());
        this.saveBatch(dto.getRelationList());
        // 添加异常申请记录
        LambdaQueryWrapper<BondApplyRelation> queryWrapper = new LambdaQueryWrapper<BondApplyRelation>()
                .eq(BondApplyRelation::getCompanyId, req.getCompanyId())
                .eq(BondApplyRelation::getType, ApplyRelationTypeEnum.ABNORMAL.getType())
                .eq(BondApplyRelation::getProjectId, req.getExceptionProjectId())
                .eq(BondApplyRelation::getSectionId, req.getExceptionSectionId());
        List<BondApplyRelation> bondApplyRelationList = bondApplyRelationService.list(queryWrapper);
        if (bondApplyRelationList.size() > 0) {
            bondApplyRelationService.remove(queryWrapper);
        }
        BondApplyRelation bondApplyRelation = new BondApplyRelation();
        bondApplyRelation.setCompanyId(req.getCompanyId());
        bondApplyRelation.setProjectId(req.getExceptionProjectId());
        bondApplyRelation.setSectionId(req.getExceptionSectionId());
        bondApplyRelation.setType(ApplyRelationTypeEnum.ABNORMAL.getType());
        bondApplyRelation.setCreatedTime(dto.getRelationList().get(0).getCreatedTime());
        boolean result = bondApplyRelationService.save(bondApplyRelation);
        //先删除所有拆分表关联id
        bondSplitService.updateRelationIdNull(req.getWaterIdList());
        for(Long waterId:req.getWaterIdList()){
            //关联拆分表最新的拆分数据
            bondSplitService.updateRelationId(waterId,bondApplyRelation.getId());
        }
        // 拆分金额
        bondSplitService.addSplit(req.getCompanyId(), req.getSectionIdList(), req.getWaterIdList());

        String path = "/marginManagement/unusual/list";
        List<String> recipientList = this.baseMapper.selectUserIdByMenu(path);

        // 发送app待办
        AppOaMsgDto dto1 = new AppOaMsgDto();
        dto1.setTaskType(AppTaskTypeEnum.BOND_ABNORMAL_RELATION.getCode());
        dto1.setRecipientList(recipientList);
        commonMqService.sendOaMsg(dto1);
        return result;
        }
    }

    /**
     * 判断流水或标段是否已关联
     * @param waterIdList
     * @param sectionIdList
     * @param companyId
     * @return
     */
    private boolean checkHasRelation(List<Long> waterIdList, List<Long> sectionIdList, Long companyId, List<Long> formerSectionIdList, List<Long> formerWaterIdList) {
        // 去除过去的标段和流水
        if (formerSectionIdList != null && formerSectionIdList.size() > 0) {
            sectionIdList = sectionIdList.stream().filter(s -> !formerSectionIdList.contains(s)).collect(Collectors.toList());
        }
        if (formerWaterIdList != null && formerWaterIdList.size() > 0) {
            waterIdList = waterIdList.stream().filter(s -> !formerWaterIdList.contains(s)).collect(Collectors.toList());
        }
        // 查询流水和标段关联数量
        long waterRelCount = 0;
        long sectionRelCount = 0;
        if (waterIdList.size() > 0) {
            waterRelCount = this.count(new LambdaQueryWrapper<BondRelation>().in(BondRelation::getWaterId, waterIdList).ne(BondRelation::getStatus, RelationStatusEnum.CONFIRM_RELATE.getType()));
        }
        if (sectionIdList.size() > 0) {
            sectionRelCount = this.count(new LambdaQueryWrapper<BondRelation>().in(BondRelation::getSectionId, sectionIdList)
                    .eq(BondRelation::getCompanyId, companyId).ne(BondRelation::getStatus, RelationStatusEnum.CONFIRM_RELATE.getType()));
        }
        if (waterRelCount > 0 || sectionRelCount > 0) {
            return true;
        }
        return false;
    }

    /**
     * 异常关联组装数据
     * @param req
     * @return
     */
    public MergeToRelationDto mergeToRelationListException (ExceptionRelationReq req) {
        MergeToRelationDto dto = new MergeToRelationDto();
        List<BondRelation> relationList = new ArrayList<>();
        for (BondSectionReq sectionReq : req.getSectionIdList()) {
            // 组装关联列表
            for (Long waterId : req.getWaterIdList()) {
                BondRelation relation = new BondRelation();
                relation.setCompanyId(req.getCompanyId());
                relation.setSectionId(sectionReq.getSectionId());
                relation.setProjectId(sectionReq.getProjectId());
                relation.setWaterId(waterId);
                relation.setStatus(RelationStatusEnum.EXCEPTION_RELATE.getType());
                relation.setIsAuto(CommonConstants.NO2);
                relation.setCreatedTime(new Date());
                relationList.add(relation);
            }
        }
        dto.setRelationList(relationList);
        return dto;
    }

    /**
     * 线下项目异常关联组装数据
     * @param req
     * @return
     */
    public MergeToRelationDto mergeToRelationListExceptionOffline (ExceptionRelationReq req) {
        MergeToRelationDto dto = new MergeToRelationDto();
        List<BondRelation> relationList = new ArrayList<>();
        for (BondSectionReq sectionReq : req.getSectionIdList()) {
            // 组装关联列表
            for (Long waterId : req.getWaterIdList()) {
                BondRelation relation = new BondRelation();
                relation.setCompanyId(req.getOfflineCompanyId());
                relation.setSectionId(sectionReq.getSectionId());
                relation.setProjectId(sectionReq.getProjectId());
                relation.setWaterId(waterId);
                relation.setStatus(RelationStatusEnum.EXCEPTION_RELATE.getType());
                relation.setIsAuto(CommonConstants.NO2);
                relationList.add(relation);
            }
        }
        dto.setRelationList(relationList);
        return dto;
    }

    /**
     * 退回
     * @param req
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean applyRelationReturn(ExceptionRelationApplyReq req) {
        // 修改异常关联申请状态
        BondApplyRelation applyRelation = bondApplyRelationService.getById(req.getApplyId());
        if (applyRelation == null) {
            throw new SunFlowerException(ExceptionEnum.ILLEGAL_PARAMETER_EXCEPTION, ExceptionEnum.ILLEGAL_PARAMETER_EXCEPTION.getMessage());
        }
        // 查询异常关联情况
        List<BondWater> waterList = bondWaterService.getWaterListBySectionId(applyRelation.getSectionId(), RelationStatusEnum.EXCEPTION_RELATE.getType(), applyRelation.getCompanyId());
        List<Long> waterIds = waterList.stream().map(m -> {
            return m.getId();
        }).collect(Collectors.toList());
        // 批量修改关联标识为异常关联
        bondSplitService.updateSplitForExcept(waterIds);
        // 退回时删除拆分数据，防止多次退回出现重复数据
        bondSplitService.remove(new LambdaUpdateWrapper<BondSplit>().in(BondSplit::getWaterId, waterIds));

        //删除异常关联数据
        this.remove(new LambdaUpdateWrapper<BondRelation>().eq(BondRelation::getSectionId, applyRelation.getSectionId()).eq(BondRelation::getCompanyId, applyRelation.getCompanyId())
                .in(BondRelation::getWaterId, waterIds));
        applyRelation.setStatus(ApplyRelationStatusEnum.RETRUNED.getType());
        applyRelation.setReturnMsg(req.getReturnMsg());
        return bondApplyRelationService.updateById(applyRelation);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean applyRelationAgree(ExceptionRelationApplyReq req) {
        BondApplyRelation applyRelation = bondApplyRelationService.getById(req.getApplyId());
        if (applyRelation == null) {
            throw new SunFlowerException(ExceptionEnum.ILLEGAL_PARAMETER_EXCEPTION, ExceptionEnum.ILLEGAL_PARAMETER_EXCEPTION.getMessage());
        }
        // 查询异常关联情况
        List<BondWater> waterList = bondWaterService.getWaterListBySectionId(applyRelation.getSectionId(), RelationStatusEnum.EXCEPTION_RELATE.getType(), applyRelation.getCompanyId());
        List<Long> waterIds = waterList.stream().map(m -> {
            return m.getId();
        }).collect(Collectors.toList());

        // 修改关联状态为已关联
        this.update(new LambdaUpdateWrapper<BondRelation>().set(BondRelation::getStatus, RelationStatusEnum.HAS_RELATE.getType()).eq(BondRelation::getCompanyId, applyRelation.getCompanyId())
                .in(BondRelation::getWaterId, waterIds));
        // ncc推送调整数据
        //判断是否为再次关联前先查询金额拆分信息历史
        List<BondSplitHistoryDto> historyDtoList = queryHistorySplitByRelationId(waterIds,applyRelation.getId());
        bondSplitService.pushNccData(historyDtoList, ChangeRecordTypeEnum.MANAGER_RELATION.getType(), applyRelation.getCompanyId());
        // 修改异常关联申请状态为已处理
        applyRelation.setStatus(ApplyRelationStatusEnum.HANDLED.getType());
        applyRelation.setRemark(req.getRemark());
        return bondApplyRelationService.updateById(applyRelation);
    }

    /**
     * 异常关联处理详情
     * @param applyId
     * @return
     */
    @Override
    public BondRelationVo getExceptionRelationDetail(Long applyId) {
        BondRelationVo vo = new BondRelationVo();
        BondApplyRelation applyRelation = bondApplyRelationService.getById(applyId);
        // 查询异常关联情况
        List<BondWater> waterList = bondWaterService.getWaterListBySectionId(applyRelation.getSectionId(), RelationStatusEnum.EXCEPTION_RELATE.getType(), applyRelation.getCompanyId());
        List<Long> waterIds = waterList.stream().map(m -> {
            return m.getId();
        }).collect(Collectors.toList());
        vo.setWaterList(waterList);
        List<BondRelationSectionVo> result = getSectionListByWaterIds(waterIds, RelationStatusEnum.EXCEPTION_RELATE.getType());
        for(BondRelationSectionVo sectionVo:result){
            LambdaQueryWrapper<BondSplit> queryWrapper = new LambdaQueryWrapper<BondSplit>()
                    .eq(BondSplit::getCompanyId, applyRelation.getCompanyId())
                    .eq(BondSplit::getSectionId, sectionVo.getId());
            List<BondSplit> splitList = bondSplitService.list(queryWrapper);
            BigDecimal amount = new BigDecimal(0);
            for(BondSplit split:splitList){
                amount=amount.add(split.getAmount());
            }
            if(amount.compareTo(BigDecimal.ZERO)>0){
                sectionVo.setBond(amount);
            }
        }
        vo.setSectionList(result);
        return vo;
    }

    /**
     * 异常关联处理详情（已办）
     * @param applyId
     * @return
     */
    @Override
    public BondRelationVo getExceptionRelationDetailCompleted(Long applyId) {
        BondRelationVo vo = new BondRelationVo();
        BondApplyRelation applyRelation = bondApplyRelationMapper.queryByIdCompleted(applyId);
        // 查询异常关联情况
        List<BondWater> waterList = bondWaterService.getWaterListBySectionIdCompleted(applyRelation.getSectionId(), RelationStatusEnum.EXCEPTION_RELATE.getType(), applyRelation.getCompanyId(),applyRelation.getCreatedTime());
        List<Long> waterIds = waterList.stream().map(m -> {
            return m.getId();
        }).collect(Collectors.toList());
        vo.setWaterList(waterList);
        List<BondRelationSectionVo> result = getSectionListByWaterIdsCompleted(waterIds, RelationStatusEnum.EXCEPTION_RELATE.getType(),applyRelation.getCreatedTime());
        for(BondRelationSectionVo sectionVo:result){
            LambdaQueryWrapper<BondSplit> queryWrapper = new LambdaQueryWrapper<BondSplit>()
                    .eq(BondSplit::getCompanyId, applyRelation.getCompanyId())
                    .eq(BondSplit::getSectionId, sectionVo.getId());
            List<BondSplit> splitList = bondSplitService.list(queryWrapper);
            BigDecimal amount = new BigDecimal(0);
            for(BondSplit split:splitList){
                amount=amount.add(split.getAmount());
            }
            if(amount.compareTo(BigDecimal.ZERO)>0){
                sectionVo.setBond(amount);
            }
        }
        vo.setSectionList(result);
        return vo;
    }

    @Override
    public Integer checkBondRelationByWaterId(Long waterId) {
        return this.getBaseMapper().checkBondRelationByWaterId(waterId);
    }

    @Override
    public Result<List<BondWater>> checkOpenAccount(List<Long> waterIds) {
        // 查询流水信息
        List<BondWater> bondWaterList = bondWaterService.listByIds(waterIds);
        for(BondWater water:bondWaterList){
            if(!checkWater(water)){
                return Result.ok("开户行校验失败",bondWaterList);
            }
        }
        return Result.ok();
    }

    private boolean checkWater(BondWater water){
        //开户行号为空，开户行号不为12位
        if(StringUtils.isBlank(water.getCompanyBankCode())||12!=water.getCompanyBankCode().length()){
            return false;
        }
        //开户行名为空，开户行名不包含银行或者支行字样
        if(StringUtils.isBlank(water.getCompanyBankDeposit())){
            return false;
        }
        return water.getCompanyBankDeposit().contains("银行") || water.getCompanyBankDeposit().contains("支行");
    }

    /**
     * 查询关联表中是否有删除记录
     * @param waterId
     * @return
     */
    @Override
    public Integer getDeleteCountByWaterId(Long waterId) {
        return this.baseMapper.getDeleteCountByWaterId(waterId);
    }

    /**
     * 自动关联流水
     * @param water
     * @return
     */
    @Override
    public Boolean autoRelate(BondWater water) {
        List<BondRelation> relationList = new ArrayList<>();
        // 根据名称判断供应商是否存在
//        List<Company> companyList = companyService.list(new LambdaQueryWrapper<Company>().eq(Company::getCompanyName, water.getCompanyName()));
        List<Company> companyList = companyService.listIgnoreBracket(water.getCompanyName());
        if (companyList.size() != 1) {
            log.info("自动关联失败，企业名称匹配异常，流水:{}", water);
            return false;
        }
        Company company = companyList.get(0);
        // 判断附言格式，JSTCC2200802378/1/2/3
        String postScript = water.getPostScript();
        if (null == postScript || postScript.equals("")) {
            log.info("自动关联失败，附言为空，流水:{}", water);
            // 发送自动关联无附言短信
            sendNoPostScript(company.getId());
            return false;
        }
        String[] arr = postScript.split("/");
        if(arr.length == 0){
            log.info("自动关联失败，附言项目编号匹配失败，流水:{}", water);
            return false;
        }
        String purchaseNumber = arr[0];
        // 根据供应商名称和附言匹配 委托编号、项目编号、进场编号、国际标编号
        List<Project> projectList = this.baseMapper.selectProjectByPurchaseNumber(purchaseNumber, water.getCompanyName());
        if (projectList.size() == 0 || projectList.size() > 1) {
            log.info("自动关联失败，附言项目编号匹配失败，流水:{}", water);
            return false;
        }
        Project project = projectList.get(0);
        // 绑定标包
        if (arr.length == 1) {
            // 当只有项目编号时，判断该项目是否为不划分或者只有一个包，关联即可
            List<ProjectBidSectionDTO> sectionList = projectBidSectionService.getProjectBidSectionByProjectId(project.getId());
            if (sectionList.size() == 0 || sectionList.size() > 1) {
                log.info("自动关联失败，附言对应标段数量异常，流水:{}", water);
                // 发送仅匹配项目短信
                sendOnlyProject(project, company.getId());
                return false;
            }
            List<String> sectionIdList = new ArrayList<>();
            sectionIdList.add(sectionList.get(0).getId().toString());
            // 标段校验
            if(autoRelationCheck(sectionList.get(0).getId(), company.getId(), water.getDate(), water.getTime())) {
                BondRelation bondRelation = new BondRelation();
                bondRelation.setProjectId(project.getId());
                bondRelation.setWaterId(water.getId());
                bondRelation.setCompanyId(company.getId());
                bondRelation.setSectionId(sectionList.get(0).getId());
                bondRelation.setStatus(RelationStatusEnum.CONFIRM_RELATE.getType());
                bondRelation.setIsAuto(CommonConstants.YES);
                relationList.add(bondRelation);
                // 发送成功短信
                sendRelationResult(sectionIdList, project, company.getId(), "", TemplateCodeEnum.RELATION_SUCCESS.getSecondCode());
            } else {
                // 发送失败短信
                sendRelationResult(sectionIdList, project, company.getId(), "", TemplateCodeEnum.VERIFY_FAIL.getSecondCode());
            }
        } else {
            List<String> sectionIdList = new ArrayList<>();
            // 遍历绑定
            for (int i = 1; i < arr.length; i++) {
                ProjectBidSection section = projectBidSectionService.getOne(new LambdaQueryWrapper<ProjectBidSection>().eq(ProjectBidSection::getProjectId, project.getId())
                        .eq(ProjectBidSection::getPackageNumber, arr[i]));
                // 标段校验
                if (section != null) {
                    if(autoRelationCheck(section.getId(), company.getId(), water.getDate(), water.getTime())) {
                        BondRelation bondRelation = new BondRelation();
                        bondRelation.setProjectId(project.getId());
                        bondRelation.setWaterId(water.getId());
                        bondRelation.setCompanyId(company.getId());
                        bondRelation.setSectionId(section.getId());
                        bondRelation.setStatus(RelationStatusEnum.CONFIRM_RELATE.getType());
                        bondRelation.setIsAuto(CommonConstants.YES);
                        relationList.add(bondRelation);
                    }
                    sectionIdList.add(section.getId().toString());
                }
            }
            // 发送短信
            String packageNumber = postScript.replace(purchaseNumber, "");
            if ((arr.length - 1) == relationList.size()) {
                // 发送成功短信
                sendRelationResult(sectionIdList, project, company.getId(), packageNumber, TemplateCodeEnum.RELATION_SUCCESS.getSecondCode());
            } else if (sectionIdList.size() > 0) {
                // 清空list 并发送失败短信
                relationList.clear();
                sendRelationResult(sectionIdList, project, company.getId(), packageNumber, TemplateCodeEnum.VERIFY_FAIL.getSecondCode());
            }
        }
        // 关联流水和标段
        return this.saveBatch(relationList);
    }

    /**
     * 自动关联校验
     * 1.若匹配到的项目标包已过开标时间（按包判断时间），则无法关联
     * 2.若匹配到的项目标包开标时间小于保证金到账时间（按包判断时间），则无法关联
     * 3.若存在包已关联，则无法关联
     * 4.若项目在招标文件中标明该标包不收取投标保证金，则无法进行保证金的关联
     * 5.若供应商未购标，则无法关联
     * @param sectionId
     * @param companyId
     * @param date
     * @return
     */
    public boolean autoRelationCheck(Long sectionId, Long companyId, String date, String time) {
        String dateTime = date + " " + time;
        AutoRelationDto dto = this.getBaseMapper().checkAutoRelation(sectionId, companyId, dateTime);
        if (null != dto) {
            if (dto.getIsOverSubmitEndTime().equals(CommonConstants.YES)) {
                log.info("自动关联标段校验不通过，当前时间已过开标时间，标段id：{}", sectionId);
                return false;
            }
            if (dto.getIsOverBondTime().equals(CommonConstants.YES)) {
                log.info("自动关联标段校验不通过，保证金到账时间已过开标时间，标段id：{}", sectionId);
                return false;
            }
            if (dto.getRelateCount() > 0) {
                log.info("自动关联标段校验不通过，该标段已关联，标段id：{}", sectionId);
                return false;
            }
            if (dto.getBondType().equals(CommonConstants.NO)) {
                log.info("自动关联标段校验不通过，当前标段不收保证金，无需关联，标段id：{}", sectionId);
                return false;
            }
            if (dto.getDownloadFlag().equals(CommonConstants.NO)) {
                log.info("自动关联标段校验不通过，当前标段供应商还未购标，标段id：{}", sectionId);
                return false;
            }
            if (dto.getStatus() >= PackageStatusEnum.ARCHIVING.getValue()) {
                log.info("自动关联标段校验不通过，当前标段状态异常，标段id：{}", sectionId);
                return false;
            }
            return true;
        } else {
            log.info("自动关联标段校验不通过，标段不存在或当前供应商未报名，标段id：{}", sectionId);
            return false;
        }
    }

    /**
     * 自动关联无附言
     * @param companyId
     */
    private void sendNoPostScript(Long companyId) {
        // 查询此企业下所有存在已购标但未开标标段的联系人
        List<BondSmsDto> list = this.baseMapper.getNoPostScript(companyId);
        // 遍历发送短信
        for (BondSmsDto sms : list) {
            SendSmsDto dto = new SendSmsDto();
            dto.setIsTemplate(true);
            dto.setTemplateCode(TemplateCodeEnum.NO_POSTSCRIPT.getSecondCode());
            dto.setSendPhone(sms.getUserMobile());
            commonSmsService.sendSms(dto);
        }
    }

    /**
     * 仅匹配项目
     * @param project
     * @param companyId
     */
    private void sendOnlyProject(Project project, Long companyId) {
        // 查询项目下供应商关注/购标联系人
        List<BondSmsDto> list = this.baseMapper.getOnlyProject(project.getId(), companyId);
        // 遍历发送短信
        for (BondSmsDto sms : list) {
            SendSmsDto dto = new SendSmsDto();
            dto.setIsTemplate(true);
            dto.setTemplateCode(TemplateCodeEnum.ONLY_PROJECT.getSecondCode());
            dto.setSendPhone(sms.getUserMobile());
            Map<String, Object> map = new HashMap<String, Object>();
            map.put("projectNum", project.getPurchaseNumber());
            dto.setSmsPms(map);
            dto.setProjectId(project.getId());
            commonSmsService.sendSms(dto);
        }
    }

    /**
     * 发送成功/校验失败短信
     * @param sectionIdList
     * @param project
     * @param companyId
     * @param packageNumber
     * @param code
     */
    private void sendRelationResult(List<String> sectionIdList, Project project, Long companyId, String packageNumber, String code) {
        // 查询该供应商标段下所有购标联系人
        List<BondSmsDto> list = this.baseMapper.getRelationResult(sectionIdList, project.getId(), companyId);
        // 遍历发送短信
        for (BondSmsDto sms : list) {
            SendSmsDto dto = new SendSmsDto();
            dto.setIsTemplate(true);
            dto.setTemplateCode(code);
            dto.setSendPhone(sms.getUserMobile());
            Map<String, Object> map = new HashMap<String, Object>();
            map.put("projectNum", project.getPurchaseNumber());
            map.put("packageNumber", packageNumber);
            dto.setSmsPms(map);
            dto.setProjectId(project.getId());
            dto.setSectionIds(String.join(",", sectionIdList));
            commonSmsService.sendSms(dto);
        }
    }

}
