<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!-- 数据权限通用mapper -->
<mapper namespace="CommomMapper">

    <!-- 用户项目编辑权限sql -->
    <sql id="userEditProjectViewSql">
        SELECT DISTINCT
            `p`.`project_id` AS `project_id`,
            `p`.`user_id` AS `user_id`,
            `p`.`department_id` AS `department_id`,
            `p`.`role_id` AS `role_id`,
            `p`.`is_empower` AS `is_empower`
        FROM
            (
             `v_user_edit_permission` `e`
                JOIN (
                SELECT DISTINCT
                    `p`.`id` AS `project_id`,
                    `rud`.`user_id` AS `user_id`,
                    `rud`.`depart_id` AS `department_id`,
                    `rud`.`role_id` AS `role_id`,
                    0 AS `is_empower`
                FROM
                    `t_project` `p`
                        LEFT JOIN `t_project_entrust_user` `tpe` ON `p`.`id` = `tpe`.`project_id`
                        LEFT JOIN `r_depart_role` `rud` ON `tpe`.`user_id` = `rud`.`user_id`
                        AND `tpe`.`department_id` = `rud`.`depart_id`
                        LEFT JOIN `r_user_role` `ur` ON `rud`.`user_id` = `ur`.`user_id`
                WHERE
                    `p`.`is_delete` = 0
                  AND `tpe`.`is_delete` = 0
                  AND `rud`.`is_delete` = 0
                  AND `tpe`.`type` = 1
                  AND `ur`.`is_delete` = 0
                <if test="viewDto != null">
                    <if test="viewDto.userId != null">
                        AND tpe.user_id = #{viewDto.userId}
                    </if>
                    <if test="viewDto.projectId != null">
                        AND p.id = #{viewDto.projectId}
                    </if>
                    <if test="viewDto.departId != null">
                        AND rud.depart_id = #{viewDto.departId}
                    </if>
                </if>
                UNION
                SELECT DISTINCT
                    `t1`.`project_id` AS `project_id`,
                    `t1`.`user_id` AS `user_id`,
                    `t1`.`depart_id` AS `department_id`,
                    0 AS `role_id`,
                    1 AS `is_empower`
                FROM
                    (SELECT
                         `a`.`from_user_id` AS `from_user_id`,
                         `a`.`user_id` AS `user_id`,
                         `a`.`depart_id` AS `depart_id`,
                         `a`.`project_id` AS `project_id`,
                         `a`.`right_code` AS `right_code`,
                         `a`.`batch_single_cancel` AS `batch_single_cancel`
                     FROM
                         (
                             SELECT
                                 `upfu`.`from_user_id` AS `from_user_id`,
                                 `upfu`.`user_id` AS `user_id`,
                                 `upfu`.`depart_id` AS `depart_id`,
                                 `upfu`.`project_id` AS `project_id`,
                                 `upfu`.`right_code` AS `right_code`,
                                 `upfu`.`batch_single_cancel` AS `batch_single_cancel`
                             FROM
                                 `t_user_project_from_user` `upfu`
                             where 1 = 1
                            <if test="viewDto != null">
                                <if test="viewDto.userId != null">
                                    and upfu.user_id = #{viewDto.userId}
                                </if>
                                <if test="viewDto.projectId != null">
                                    AND upfu.project_id = #{viewDto.projectId}
                                </if>
                                <if test="viewDto.departId != null">
                                    AND upfu.depart_id = #{viewDto.departId}
                                </if>
                            </if>
                             UNION
                             SELECT
                                 `udu`.`from_user_id` AS `from_user_id`,
                                 `udu`.`user_id` AS `user_id`,
                                 `udu`.`depart_id` AS `depart_id`,
                                 `tpe`.`project_id` AS `project_id`,
                                 `udu`.`right_code` AS `right_code`,
                                 `upfu`.`batch_single_cancel` AS `batch_single_cancel`
                             FROM
                                 `t_user_dataperm_user` `udu`
                                     LEFT JOIN `t_project_entrust_user` `tpe` ON `tpe`.`user_id` = `udu`.`from_user_id`
                                     LEFT JOIN `t_user_project_from_user` `upfu` ON `upfu`.`from_user_id` = `udu`.`from_user_id` AND `tpe`.`project_id` = `upfu`.`project_id` AND `udu`.`user_id` = `upfu`.`user_id`
                             WHERE
                                 `tpe`.`is_delete` = 0
                               AND `upfu`.`id` IS NULL
                               AND `tpe`.`type` = 1
                               AND `udu`.`data_scope` = 0
                                <if test="viewDto != null">
                                    <if test="viewDto.userId != null">
                                        and udu.user_id = #{viewDto.userId}
                                    </if>
                                    <if test="viewDto.projectId != null">
                                        AND tpe.project_id = #{viewDto.projectId}
                                    </if>
                                    <if test="viewDto.departId != null">
                                        AND udu.depart_id = #{viewDto.departId}
                                    </if>
                                </if>
                         ) `a`
                     WHERE
                         `a`.`batch_single_cancel` IS NULL
                        OR `a`.`batch_single_cancel` = 0) `t1`
                WHERE
                    `t1`.`right_code` = 1
            ) `p` ON `p`.`user_id` = `e`.`user_id`
                AND `p`.`department_id` = `e`.`depart_id`
                AND `p`.`role_id` = `e`.`role_id`
                )
        WHERE 1 = 1
            AND ( `p`.`user_id` = `e`.`user_id` AND `e`.`data_permission` = 0 )
           OR ( `p`.`department_id` = `e`.`depart_id` AND `e`.`data_permission` = 1 )
           OR ( `p`.`role_id` = `e`.`role_id` AND `e`.`data_permission` = 2 )
           OR `e`.`data_permission` = 4
           OR (`p`.`is_empower` = 1 AND `e`.`data_permission` = 5)
    </sql>

    <!-- 用户项目权限视图sql -->
    <sql id="projectUserPermViewSql">
        SELECT
        `a`.`from_user_id` AS `from_user_id`,
        `a`.`user_id` AS `user_id`,
        `a`.`depart_id` AS `depart_id`,
        `a`.`project_id` AS `project_id`,
        `a`.`right_code` AS `right_code`,
        `a`.`batch_single_cancel` AS `batch_single_cancel`,
        `tu`.`user_name` AS `user_name`
        FROM
        (
        SELECT
        `upfu`.`from_user_id` AS `from_user_id`,
        `upfu`.`user_id` AS `user_id`,
        `upfu`.`depart_id` AS `depart_id`,
        `upfu`.`project_id` AS `project_id`,
        `upfu`.`right_code` AS `right_code`,
        `upfu`.`batch_single_cancel` AS `batch_single_cancel`
        FROM
        `t_user_project_from_user` `upfu`
        where 1 = 1
        <if test="viewDto != null">
            <if test="viewDto.userId != null">
                and upfu.user_id = #{viewDto.userId}
            </if>
            <if test="viewDto.projectId != null">
                and upfu.project_id = #{viewDto.projectId}
            </if>
            <if test="viewDto.departId != null">
                and upfu.depart_id = #{viewDto.departId}
            </if>
        </if>

        UNION
        SELECT
        `udu`.`from_user_id` AS `from_user_id`,
        `udu`.`user_id` AS `user_id`,
        `udu`.`depart_id` AS `depart_id`,
        `tpe`.`project_id` AS `project_id`,
        `udu`.`right_code` AS `right_code`,
        `upfu`.`batch_single_cancel` AS `batch_single_cancel`
        FROM
        `t_user_dataperm_user` `udu`
        LEFT JOIN `t_project_entrust_user` `tpe` ON `tpe`.`user_id` = `udu`.`from_user_id`
        LEFT JOIN `t_user_project_from_user` `upfu` ON `upfu`.`from_user_id` = `udu`.`from_user_id` AND `tpe`.`project_id` = `upfu`.`project_id` AND `udu`.`user_id` = `upfu`.`user_id`
        WHERE
        `tpe`.`is_delete` = 0
        AND `upfu`.`id` IS NULL
        AND `tpe`.`type` = 1
        AND `udu`.`data_scope` = 0
        <if test="viewDto != null">
            <if test="viewDto.userId != null">
                and udu.user_id = #{viewDto.userId}
            </if>
            <if test="viewDto.projectId != null">
                and tpe.project_id = #{viewDto.projectId}
            </if>
            <if test="viewDto.departId != null">
                and udu.depart_id = #{viewDto.departId}
            </if>
        </if>
        ) `a`  LEFT JOIN `t_user` `tu` ON `tu`.`id` = `a`.`user_id`
        WHERE
        `a`.`batch_single_cancel` IS NULL
        OR `a`.`batch_single_cancel` = 0
    </sql>

</mapper>
