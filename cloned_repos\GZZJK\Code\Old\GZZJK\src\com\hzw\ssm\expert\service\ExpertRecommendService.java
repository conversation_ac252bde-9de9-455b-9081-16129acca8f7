/*
 * 公司名称：江苏华招网信息技术有限公司
 * 版权信息：江苏华招网信息技术有限公司版权所有
 * 文件名称：ExpertOutService.java
 * 修改时间：2021年2月23日
 * 修改人：宋伟
 * 修改内容：
 * 跟踪单号：
 * 修改单号 ：
 */

package com.hzw.ssm.expert.service;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.UUID;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import com.hzw.ssm.expert.dao.ExpertRecommendMapper;
import com.hzw.ssm.expert.entity.ExpertRecommendEntity;
import com.hzw.ssm.fw.base.BaseService;
import com.hzw.ssm.sys.user.entity.UserEntity;

/**
 * <一句话功能简述> 专家推荐相关相关service
 * 
 * <AUTHOR>
 * @version V1.0[产品/模块版本]
 */
@Service
public class ExpertRecommendService extends BaseService {
	@Autowired
	private ExpertRecommendMapper recommendMapper;

	/**
	 * 
	 * 函数功能描述：新增/修改推荐计划
	 * 
	 * @param exertEntity
	 * @return
	 */
	@Transactional
	public void addPlan(ExpertRecommendEntity entity) {
		// 1.根据计划时间查询库中是否存在计划
		List<ExpertRecommendEntity>planList=recommendMapper.queryPagePlanList(entity);
		
		if (CollectionUtils.isEmpty(planList)) {
			// 2.不存在，新增
			entity.setCreate_time(new Date());
			entity.setModify_time(new Date());
			entity.setDelete_flag(0);
			entity.setId(UUID.randomUUID().toString().replaceAll("-", ""));
			recommendMapper.addPlanById(entity);
		} else {
			// 3.存在，修改
			entity.setModify_time(new Date());
			entity.setId(planList.get(0).getId());
			recommendMapper.updatePlanById(entity);
		}

	}

	/**
	 * 
	 * 函数功能描述：新增/修改单个推荐记录
	 * 
	 * @param exertEntity
	 * @return
	 */
	@Transactional
	public void addRecommender(ExpertRecommendEntity entity) {
		
		if (StringUtils.isEmpty(entity.getId())) {
			// 2.不存在，新增
			// 1.根据推荐记录id查询库中是否存在该记录
			ExpertRecommendEntity queryEntity=new ExpertRecommendEntity();
			queryEntity.setPlanId(entity.getPlanId());
			queryEntity.setRecommenderId(entity.getRecommenderId());
			List<ExpertRecommendEntity> queryResult=recommendMapper.queryRecommenderList(entity);
			if(CollectionUtils.isEmpty(queryResult)){
				entity.setCreate_time(new Date());
				entity.setModify_time(new Date());
				entity.setDelete_flag(0);
				entity.setId(UUID.randomUUID().toString().replaceAll("-", ""));
				recommendMapper.addRecommendById(entity);
			}else{
				entity.setId(queryResult.get(0).getId());
				// 3.存在，修改
				entity.setModify_time(new Date());
				recommendMapper.updateRecommendById(entity);
			}
			
		} else {
			// 3.存在，修改
			entity.setModify_time(new Date());
			
			recommendMapper.updateRecommendById(entity);
		}

	}
	/**
	 * 
	 * 函数功能描述：删除单个推荐记录
	 * 
	 * @param exertEntity
	 * @return
	 */
	@Transactional
	public void deleteRecommender(ExpertRecommendEntity entity) {
		ExpertRecommendEntity dao =new ExpertRecommendEntity();
		dao.setModify_id(entity.getModify_id());
		dao.setModify_time(new Date());
		dao.setDelete_flag(1);
		dao.setId(entity.getId());
		recommendMapper.removeRecommend(dao);
		
	}

	/**
	 * 
	 * 函数功能描述：查询推荐计划
	 * 
	 * @param exertEntity
	 * @return
	 */
	public List<ExpertRecommendEntity> queryPlans(ExpertRecommendEntity entity) {
		return recommendMapper.queryPagePlanList(entity);

	}
	/**
	 * 
	 * 函数功能描述：查询推荐专家
	 * 
	 * @param exertEntity
	 * @return
	 */
	public List<ExpertRecommendEntity> queryExpert(ExpertRecommendEntity entity) {
		if(!StringUtils.isEmpty(entity.getStatus())){
			entity.setStatusList(Arrays.asList(entity.getStatus().split(",")));
		}
		return recommendMapper.queryPageExpertList(entity);
		
	}
	/**
	 * 
	 * 函数功能描述：查询推荐统计
	 * 
	 * @param exertEntity
	 * @return
	 */
	public List<ExpertRecommendEntity> queryExpertStatics(ExpertRecommendEntity entity) {
		
		return recommendMapper.queryPageExpertStaticsList(entity);
		
	}

	/**
	 * 
	 * 函数功能描述：查询推荐人记录
	 * 
	 * @param exertEntity
	 * @return
	 */
	public List<ExpertRecommendEntity> queryRecommenders(ExpertRecommendEntity entity) {

		return recommendMapper.queryRecommenderList(entity);

	}
	/**
	 * 
	 * 函数功能描述：获取业务员信息
	 * @param entity
	 * @return
	 */
	public List<UserEntity> querySalesMan(UserEntity entity){
		entity.setRole_id("20141028140549978379");
		return recommendMapper.querySalesMan(entity);
	}

}
