package com.hzw.ssm.webService.output.impl;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

/**
 * 获取专家名单返回结果
 * 
 * <AUTHOR>
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(propOrder = { "expertName", "expertPhone","noticeType","noticeTime","idType","idNo","userId","userName","company" })
public class ExpertInfo {
	// 专家名称
	@XmlElement
	private String expertName;
	// 专家联系电话
	@XmlElement
	private String expertPhone;
	// 通知方式
	@XmlElement
	private Integer noticeType;
	// 通知时间
	@XmlElement
	private String noticeTime;
	//证件类型
	@XmlElement
	private String idType;
	//证件号码
	@XmlElement
	private String idNo;
	//专家用户id 
	@XmlElement
	private String userId;
	//专家用户名称
	@XmlElement
	private String userName;
	//专家单位
	@XmlElement
	private String company;

	
	public String getExpertName() {
		return expertName;
	}
	public void setExpertName(String expertName) {
		this.expertName = expertName;
	}
	public String getExpertPhone() {
		return expertPhone;
	}
	public void setExpertPhone(String expertPhone) {
		this.expertPhone = expertPhone;
	}
	public Integer getNoticeType() {
		return noticeType;
	}
	public void setNoticeType(Integer noticeType) {
		this.noticeType = noticeType;
	}
	public String getNoticeTime() {
		return noticeTime;
	}
	public void setNoticeTime(String noticeTime) {
		this.noticeTime = noticeTime;
	}
	public String getIdNo() {
		return idNo;
	}
	public void setIdNo(String idNo) {
		this.idNo = idNo;
	}
	public String getUserId() {
		return userId;
	}
	public void setUserId(String userId) {
		this.userId = userId;
	}
	public String getUserName() {
		return userName;
	}
	public void setUserName(String userName) {
		this.userName = userName;
	}
	public String getCompany() {
		return company;
	}
	public void setCompany(String company) {
		this.company = company;
	}
	public String getIdType() {
		return idType;
	}
	public void setIdType(String idType) {
		this.idType = idType;
	}
	
}