package com.hzw.sunflower.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(description = "记账部门分摊人")
@Data
public class ProjectShareVo {

    @ApiModelProperty("分摊人名称")
    private String shareUserName;

    @ApiModelProperty("分摊人确认状态")
    private String shareUserStatus;

    @ApiModelProperty("分摊人处长名称")
    private String shareManagerName;

    @ApiModelProperty("分摊人处长确认状态")
    private String shareManagerStatus;

    @ApiModelProperty("分摊类型")
    private Integer shareManagerType;

}
