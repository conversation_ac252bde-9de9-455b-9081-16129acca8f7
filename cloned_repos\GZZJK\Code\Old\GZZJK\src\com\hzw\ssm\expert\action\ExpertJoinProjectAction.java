package com.hzw.ssm.expert.action;



import java.util.List;

import javax.servlet.http.HttpSession;

import org.apache.struts2.convention.annotation.Action;
import org.apache.struts2.convention.annotation.Namespace;
import org.apache.struts2.convention.annotation.ParentPackage;
import org.apache.struts2.convention.annotation.Result;
import org.apache.struts2.convention.annotation.Results;
import org.springframework.beans.factory.annotation.Autowired;

import com.hzw.ssm.fw.base.BaseAction;
import com.hzw.ssm.sys.project.entity.ProjectEntity;
import com.hzw.ssm.sys.project.service.ProjectService;
import com.hzw.ssm.sys.user.entity.UserEntity;

@Namespace("/expertJoinProject")
@ParentPackage(value = "default")
@Results( { @Result(name = "joinProjectList", location = "/jsp/expert/expert_join_project_list.jsp"),
			@Result(name = "toProjectDetail", location = "/jsp/expert/projectDetail.jsp")
		})
/**
 * 专家参评项目Action
 * <AUTHOR> 2014-10-20
 */
public class ExpertJoinProjectAction extends BaseAction {
	
	private static final long serialVersionUID = -8823858495799309882L;
	
	/** 通用key*/
	private String key;
	
	/** 提示信息 */
	private String message;
	
	/** 项目*/
	private ProjectEntity projectEntity;
	
	/** 项目列表*/
	private List<ProjectEntity> projectList;
	
	@Autowired
	private ProjectService projectService;
	
	/**
	 * 专家参评项目列表
	 * @return
	 * @throws Exception
	 */
	@Action("expertJoinProjectList")
	public String expertJoinProjectList() throws Exception {
		this.context();
		//当前session
		HttpSession session = getRequest().getSession();
		//当前用户信息
		UserEntity info = (UserEntity)session.getAttribute("userInfo");
		
		if(projectEntity==null){
			projectEntity=new ProjectEntity();
		}
		projectEntity.setPage(this.getPage());
		projectEntity.setExpertId(info.getUser_id());
		if(projectEntity.getBidEndTime() != null && !"".equals(projectEntity.getBidEndTime())){
			projectEntity.getBidEndTime().setHours(23);
			projectEntity.getBidEndTime().setMinutes(59);
			projectEntity.getBidEndTime().setSeconds(59);
		}
		projectList=projectService.queryPageExpertJoinProjectList(projectEntity);
		
		return "joinProjectList";
	}

	/**
	 * 查询项目明细
	 * 
	 * @return
	 */
	@Action("toProjectDetail")
	public String toProjectDetail() {
		if(projectEntity==null){
			projectEntity=new ProjectEntity();
		}
		projectEntity.setProjectId(key);
		projectEntity = projectService.queryProjectById(projectEntity);
		return "toProjectDetail";
	}
	
	public String getKey() {
		return key;
	}

	public void setKey(String key) {
		this.key = key;
	}

	public String getMessage() {
		return message;
	}

	public void setMessage(String message) {
		this.message = message;
	}

	public ProjectEntity getProjectEntity() {
		return projectEntity;
	}

	public void setProjectEntity(ProjectEntity projectEntity) {
		this.projectEntity = projectEntity;
	}

	public List<ProjectEntity> getProjectList() {
		return projectList;
	}

	public void setProjectList(List<ProjectEntity> projectList) {
		this.projectList = projectList;
	}
	
}
