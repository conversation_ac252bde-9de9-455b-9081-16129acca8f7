package com.hzw.sunflower.service;

import com.hzw.sunflower.controller.request.ProjectAnalysisReq;
import com.hzw.sunflower.entity.*;

import java.util.List;

public interface ProjectAnalysisService {

    ProjectAnalysis queryAll(String startDate, String endDate);

    List<ProjectAnalysisArea> queryProjectListWithAreaNewForExport(String startDate, String endDate);

    /**
     * 查询大项目处室排名
     * @param startDate
     * @param endDate
     * @return
     */
    List<DepartProject> queryProjectByDepartment(String startDate,  String endDate);

    /**
     * 查询大项目明细
     * @param startDate
     * @param endDate
     * @return
     */
    List<LargeProject> queryLargeProjectList(String startDate, String endDate);

    /**
     * 项目分析同步
     * @param dateString
     */
    void updateProjectIndustryDistributionList(String dateString);

    /**
     * 查询标的物行业分布一览表
     * @param startDate
     * @param endDate
     * @return
     */
    List<ProjectAnalysisVo> queryProjectIndustryDistributionList (String startDate, String endDate);

    /**
     * 查询标的物行业分布一览表New
     * @param startDate
     * @param endDate
     * @return
     */
    List<ProjectAnalysisVo> queryProjectIndustryDistributionListNew (String startDate, String endDate);

    /**
     * 查询地区分布一览表
     * @param startDate
     * @param endDate
     * @return
     */
    List<ProjectAnalysisArea> queryProjectListWithArea(String startDate, String endDate);

    /**
     * 查询地区分布一览表
     * @param startDate
     * @param endDate
     * @return
     */
    List<ProjectAnalysisArea> queryProjectListWithAreaNew(String startDate, String endDate);

    /**
     * 查询地区分布一览表
     * @param startDate
     * @param endDate
     * @return
     */
    ProjectAnalysis queryAllForArea(String startDate, String endDate);

    /**
     * 导出行业项目分布（附表）
     * @param condition
     * @return
     */
    List<CollectInfo> collectExpertList(ProjectAnalysisReq condition);

    List<DepartProject> queryProjectByDepartmentNew(String startDate, String endDate);

    List<LargeProject> queryLargeProjectListNew(String startDate, String endDate);
}
