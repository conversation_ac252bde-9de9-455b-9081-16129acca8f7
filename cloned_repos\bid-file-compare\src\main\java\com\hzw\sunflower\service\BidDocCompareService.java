package com.hzw.sunflower.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.dto.BidDocCompareDto;
import com.hzw.sunflower.entity.BidFileSimilarity;
import com.hzw.sunflower.vo.BidDocCompareVo;

import java.util.List;


/**
 * <AUTHOR>
 * @Date 2023/7/11 10:30
 * @Version 1.0
 */
public interface BidDocCompareService extends IService<BidFileSimilarity> {

    /**
     * 投标文件相似度对比
     * @return
     */
    Result<String> bidDocCompare(BidDocCompareDto bidDocCompareDto);

    /**
     * 投标文件相似度比对mq推送
     * @return
     */
    void sendMq(BidDocCompareDto bidDocCompareDto);

    /**
     * 查询全流程线上供应商投标文件
     * @param sectionId
     * @param bidRound
     * @return
     */
    List<BidDocCompareVo> queryApplyCompany(Long sectionId, Integer bidRound);

    /**
     * 查询开评标备案供应商
     * @param sectionId
     * @return
     */
    List<BidDocCompareVo> queryTenderRecord(Long sectionId,Integer bidRound);

    /**
     * 查询开评标备案供应商文件
     * @param ossIdList
     * @return
     */
    String queryTenderRecordFile(List<String> ossIdList);

    /**
     * 查询项目业务流程
     * @param projectId
     * @return
     */
    Integer queryProjectOperationFlow(Long projectId);
}
