/**
 * 
 */
package com.hzw.ssm.expert.entity;

import java.util.Date;
import com.hzw.ssm.fw.base.BaseEntity;
import com.hzw.ssm.fw.util.Page;

/**
 * <AUTHOR>
 *
 */
public class IllegalExtractioninfoEntity extends BaseEntity{

	/**
	 * 
	 */
	private static final long serialVersionUID = 8200298586021497652L;

	/**
	 * DECIMATIONBATCH	N	VARCHAR2(32)	Y			项目批次号
	 */
	private String decimationbatch;
	
	/**
	 * USERID	N	VARCHAR2(32)	Y			经办人id
	 */
	private String userId;
	
	/**
	 * USERNAME	N	VARCHAR2(32)	Y			经办人名称
	 */
	private String userName;
	
	/**
	 * ILLEGALTIME	N	DATE	Y			违规时间
	 */
	private Date illegalTime;
	
	/**
	 * DEPARTMENTNAME	N	VARCHAR2(32)	Y			处室名
	 */
	private String departmentName;
	
	/**
	 * HANDLEFILE	N	VARCHAR2(255)	Y			处理文件
	 */
	private String handleFile;
	
	/**
	 * HANDLETIME	N	DATE	Y			处理时间
	 */
	private Date handleTime;
	
	/**
	 * ISHANDLE	N	VARCHAR2(2)	Y			是否处理（0,未处理 1.处理2.待处理3.不通过）
	 */
	private String isHandle;
	
	/**
	 * PROJECT_CODE	N	VARCHAR2(32)	Y			项目编号
	 */
	private String projectCode;
	
	/**
	 * OPENINGTIME	N	DATE	Y			开标时间
	 */
	private Date  openingTime;
	
	/**
	 * PROJECT_NAME	N	VARCHAR2(255)	Y			项目名称
	 */
	private String projectName;
	
	/**
	 * OPENINGADDRESS	N	VARCHAR2(255)	Y			开标地址
	 */
	private String openingAddress;
	
	/**
	 * EXTRACT_NUM	N	VARCHAR2(32)	Y			系统抽取人数
	 */
	private String extractNum;
	
	/**
	 * NEED_NUM	N	VARCHAR2(32)	Y			所需专家
	 */
	private String needNum;
	
	/**
	 * EXTRACT_TIMES	N	VARCHAR2(32)	Y			抽取次数
	 */
	private String extractTimes;
	
	/**
	 * EXAMINE_Name	N	VARCHAR2(32)	Y			审核人
	 */
	private String examineName;
	
	/**
	 * EXAMINE_TIME	N	DATE	Y			审核时间
	 */
	private Date examineTime;

	/**
	 * 排序
	 */
	private String reorder;
	
	/**
	 * 开始时间
	 */
	private Date beginTime;
	
	/**
	 * 结束时间
	 */
	private Date endTime;
	
	
	private String tender;
	/** 分页 */
	private Page page;
	
	/**
	 * 开标时间排序
	 */
	private String openingreOrder;
	/**
	 * 抽取次数排序
	 */
	private String extractNumOrder;
	
	/**
	 * 审核时间排序
	 */
	private String handletimeOrder;

	/**
	 * 抽取次数
	 */
	private Integer queryCount;
	
	/**
	 * 排序
	 */
	private String paixu;
	
	private String illRoleId;
	
	public String getDecimationbatch() {
		return decimationbatch;
	}

	public void setDecimationbatch(String decimationbatch) {
		this.decimationbatch = decimationbatch;
	}

	public String getUserId() {
		return userId;
	}

	public void setUserId(String userId) {
		this.userId = userId;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public Date getIllegalTime() {
		return illegalTime;
	}

	public void setIllegalTime(Date illegalTime) {
		this.illegalTime = illegalTime;
	}

	public String getDepartmentName() {
		return departmentName;
	}

	public void setDepartmentName(String departmentName) {
		this.departmentName = departmentName;
	}

	public String getHandleFile() {
		return handleFile;
	}

	public void setHandleFile(String handleFile) {
		this.handleFile = handleFile;
	}

	public Date getHandleTime() {
		return handleTime;
	}

	public void setHandleTime(Date handleTime) {
		this.handleTime = handleTime;
	}

	public String getIsHandle() {
		return isHandle;
	}

	public void setIsHandle(String isHandle) {
		this.isHandle = isHandle;
	}

	public String getProjectCode() {
		return projectCode;
	}

	public void setProjectCode(String projectCode) {
		this.projectCode = projectCode;
	}

	public Date getOpeningTime() {
		return openingTime;
	}

	public void setOpeningTime(Date openingTime) {
		this.openingTime = openingTime;
	}

	public String getProjectName() {
		return projectName;
	}

	public void setProjectName(String projectName) {
		this.projectName = projectName;
	}

	public String getOpeningAddress() {
		return openingAddress;
	}

	public void setOpeningAddress(String openingAddress) {
		this.openingAddress = openingAddress;
	}

	public String getExtractNum() {
		return extractNum;
	}

	public void setExtractNum(String extractNum) {
		this.extractNum = extractNum;
	}

	public String getNeedNum() {
		return needNum;
	}

	public void setNeedNum(String needNum) {
		this.needNum = needNum;
	}

	public String getExtractTimes() {
		return extractTimes;
	}

	public void setExtractTimes(String extractTimes) {
		this.extractTimes = extractTimes;
	}

	public String getReorder() {
		return reorder;
	}

	public void setReorder(String reorder) {
		this.reorder = reorder;
	}

	public Date getBeginTime() {
		return beginTime;
	}

	public void setBeginTime(Date beginTime) {
		this.beginTime = beginTime;
	}

	public Date getEndTime() {
		return endTime;
	}

	public void setEndTime(Date endTime) {
		this.endTime = endTime;
	}

	public Page getPage() {
		return page;
	}

	public void setPage(Page page) {
		this.page = page;
	}

	public String getOpeningreOrder() {
		return openingreOrder;
	}

	public void setOpeningreOrder(String openingreOrder) {
		this.openingreOrder = openingreOrder;
	}

	public String getExtractNumOrder() {
		return extractNumOrder;
	}

	public void setExtractNumOrder(String extractNumOrder) {
		this.extractNumOrder = extractNumOrder;
	}

	public String getHandletimeOrder() {
		return handletimeOrder;
	}

	public void setHandletimeOrder(String handletimeOrder) {
		this.handletimeOrder = handletimeOrder;
	}


	public String getExamineName() {
		return examineName;
	}

	public void setExamineName(String examineName) {
		this.examineName = examineName;
	}

	public Date getExamineTime() {
		return examineTime;
	}

	public void setExamineTime(Date examineTime) {
		this.examineTime = examineTime;
	}

	/**
	 * @return the queryCount
	 */
	public Integer getQueryCount() {
		return queryCount;
	}

	/**
	 * @param queryCount the queryCount to set
	 */
	public void setQueryCount(Integer queryCount) {
		this.queryCount = queryCount;
	}

	/**
	 * @return the paixu
	 */
	public String getPaixu() {
		return paixu;
	}

	/**
	 * @param paixu the paixu to set
	 */
	public void setPaixu(String paixu) {
		this.paixu = paixu;
	}

	public String getTender() {
		return tender;
	}

	public void setTender(String tender) {
		this.tender = tender;
	}

	public String getIllRoleId() {
		return illRoleId;
	}

	public void setIllRoleId(String illRoleId) {
		this.illRoleId = illRoleId;
	}
	
	
	
}
