package com.hzw.sunflower.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hzw.sunflower.constant.constantenum.ChangeRecordTypeEnum;
import com.hzw.sunflower.controller.response.BondChangeRecordsVo;
import com.hzw.sunflower.controller.response.BondChangeRecordsListVo;
import com.hzw.sunflower.dto.BondSplitNccDto;
import com.hzw.sunflower.entity.BondChangeRecords;
import com.hzw.sunflower.dao.BondChangeRecordsMapper;
import com.hzw.sunflower.entity.BondSplit;
import com.hzw.sunflower.entity.User;
import com.hzw.sunflower.entity.condition.BondChangeCondition;
import com.hzw.sunflower.service.BondChangeRecordsService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzw.sunflower.service.BondSplitService;
import com.hzw.sunflower.service.BondWaterService;
import com.hzw.sunflower.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 保证金调整记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-28
 */
@Service
public class BondChangeRecordsServiceImpl extends ServiceImpl<BondChangeRecordsMapper, BondChangeRecords> implements BondChangeRecordsService {

    @Autowired
    BondWaterService bondWaterService;

    @Autowired
    BondSplitService bondSplitService;

    @Autowired
    private UserService userService;

    /**
     * 分页查询调账日志
     * @param condition
     * @return
     */
    @Override
    public IPage<BondChangeRecordsListVo> getBondChangeRecordsPage(BondChangeCondition condition) {
        IPage<BondChangeRecordsListVo> page = condition.buildPage();
        page = this.baseMapper.getBondChangeRecordsPage(page, condition);
//        List<BondChangeRecordsListVo> list = page.getRecords();
//        // 遍历获取最新一次的金额
//        for (BondChangeRecordsListVo vo : list) {
//            // 分组查询记录
//            List<BondChangeRecordsVo> recordsVoList = this.getChangeRecords(vo.getSectionId(), vo.getCompanyId());
//            vo.setTotalAmount(recordsVoList.get(recordsVoList.size() - 1).getAmount());
//        }
//        page.setRecords(list);
        return page;
    }

    /**
     * 根据标段和供应商id查询调整记录
     * @param sectionId
     * @param companyId
     * @return
     */
    @Override
    public List<BondChangeRecordsVo> getChangeRecords(Long sectionId, Long companyId) {
        List<BondChangeRecordsVo> list = this.baseMapper.getChangeRecords(sectionId, companyId);
        // 当类型为项目经理手动关联时，读取拆分表操作人，防止异常关联导致操作人错误
        for (BondChangeRecordsVo vo : list) {
            if (vo.getOperation().equals(ChangeRecordTypeEnum.MANAGER_RELATION.getType())) {
                List<BondSplit> splitList = bondSplitService.list(new LambdaQueryWrapper<BondSplit>().eq(BondSplit::getSectionId, sectionId)
                        .eq(BondSplit::getCompanyId, companyId));
                if(splitList != null && splitList.size()>0) {
                    User user = userService.getById(splitList.get(0).getCreatedUserId());
                    vo.setOperatorName(user.getUserName());
                }
            }
        }
        return list;
    }

    /**
     * 根据拆分数据新增调整记录
     * @param list
     * @param operateType
     * @return
     */
    @Override
    public Boolean addRecordBySplit(List<BondSplitNccDto> list, Integer operateType) {
        List<BondChangeRecords> recordsList = new ArrayList<>();
        // 合并每个标段下的数据
        List<Long> sectionIds = list.stream().map(m -> {
            return m.getSectionId();
        }).distinct().collect(Collectors.toList());
        for (Long sectionId : sectionIds) {
            BondChangeRecords records = new BondChangeRecords();
            records.setSectionId(sectionId);
            records.setOperation(operateType);
            List<BondSplitNccDto> tempList = list.stream().filter(s -> s.getSectionId().equals(sectionId)).collect(Collectors.toList());
            BigDecimal total = new BigDecimal(0);
            for (BondSplitNccDto temp : tempList) {
                total = total.add(temp.getAmount());
            }
            records.setAmount(total);
            records.setCompanyId(tempList.get(0).getCompanyId());
            records.setProjectId(tempList.get(0).getProjectId());
            recordsList.add(records);
        }
        return this.saveBatch(recordsList);
    }

}
