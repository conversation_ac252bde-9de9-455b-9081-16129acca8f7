package com.hzw.sunflower.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hzw.sunflower.dto.AnnexDTO;
import com.hzw.sunflower.dto.RelevancyAnnexDTO;
import com.hzw.sunflower.entity.RelevancyAnnex;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface RelevancyAnnexMapper extends BaseMapper<RelevancyAnnex> {

    /**
     * 根据招标公告ID查询相关联的附件信息
     * @param noticeId 公告ID
     * @return 招标公告关联附件DTO的集合
     */
    List<AnnexDTO> findNoticeAnnexs(@Param("noticeId") Long noticeId);

    /**
     * 查询附件
     * @param docId
     * @return
     */
    List<RelevancyAnnexDTO> getInfoByDocId(@Param("docId") Long docId);

    /**
     * 查询附件
     * @param noticeId 公告ID
     * @return
     */
    List<Long> findNoticeAnnexIds(@Param("noticeId") Long noticeId);
}
