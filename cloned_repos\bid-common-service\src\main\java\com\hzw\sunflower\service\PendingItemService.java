package com.hzw.sunflower.service;

import com.hzw.sunflower.controller.request.PendingItemReq;
import com.hzw.sunflower.dto.ProcessRecordDTO;
import com.hzw.sunflower.entity.PendingItem;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 待处理事项表 服务类
 * </p>
 *
 * <AUTHOR> Gen
 * @since 2022-12-15
 */
public interface PendingItemService extends IService<PendingItem> {

    /**
     * 添加待处理表数据
     * @param dto
     */
    void addPendingItem(ProcessRecordDTO dto);

    /**
     * 删除待处理表数据
     * @param dto
     */
    void deletePendingItem(ProcessRecordDTO dto);

    /**
     * 退回待处理 无需处理
     * @param req
     * @return
     */
    Boolean updateHandRequired(PendingItemReq req);

}
