<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzw.sunflower.dao.BondSplitMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hzw.sunflower.entity.BondSplit">
        <id column="id" property="id" />
        <result column="project_id" property="projectId" />
        <result column="section_id" property="sectionId" />
        <result column="water_id" property="waterId" />
        <result column="company_id" property="companyId" />
        <result column="amount" property="amount" />
        <result column="except_flag" property="exceptFlag" />
        <result column="created_user_id" property="createdUserId" />
        <result column="created_time" property="createdTime" />
        <result column="updated_user_id" property="updatedUserId" />
        <result column="updated_time" property="updatedTime" />
        <result column="is_delete" property="isDelete" />
        <result column="remark" property="remark" />
        <result column="version" property="version" />
    </resultMap>

    <!-- 批量修改数据为非最新 -->
    <update id="updateSplitForIsLatest">
        UPDATE t_bond_split set is_latest = 1
        where water_id in
        <foreach item="item" collection="waterIds" separator="," open="(" close=")" index="">
            #{item}
        </foreach>
    </update>

    <!-- 异常关联时将拆分表和关联表关联上 -->
    <update id="updateRelationId">
        UPDATE t_bond_split set relation_id = #{relationId}
        where water_id = #{waterId} and is_latest = 0
    </update>

    <update id="updateRelationIdNull">
        UPDATE t_bond_split set relation_id = null
        where water_id in
        <foreach item="item" collection="waterIds" separator="," open="(" close=")" index="">
            #{item}
        </foreach>
    </update>

    <!-- 批量修改数据为异常标识 -->
    <update id="updateSplitForExcept">
        UPDATE t_bond_split set except_flag = 1
        where water_id in
        <foreach item="item" collection="waterIds" separator="," open="(" close=")" index="">
            #{item}
        </foreach>
        and is_delete = 0
    </update>

    <!-- 查询标包的拆分合计金额 -->
    <select id="listBySectionId" resultType="com.hzw.sunflower.entity.BondSplit">
        SELECT SUM(amount) amount from t_bond_split
        WHERE section_id = #{sectionId}
        and company_id = #{companyId}
        and is_delete = 0
    </select>

    <!-- 根据流水id获取拆分数据 -->
    <select id="getByWaterIds" resultType="com.hzw.sunflower.dto.BondSplitNccDto">
        SELECT
        s.*,
        ( SELECT share_department_code FROM t_project_share_ncc WHERE project_id = s.project_id LIMIT 1 ) account_department_code,
        (case when bs.package_number is not null then CONCAT(p.purchase_number,'/',bs.package_number) else p.purchase_number end) as purchase_number,
        w.created_time creationTime,
        CONCAT(w.date, ' ',w.time) receiveTime,
        n.`code` customer,
        n.`id` customerId,
        CASE WHEN INSTR(w.receive_bank, '中国银行') THEN (SELECT account from t_bankdata_bond_to_ncc where bank_type = 1 and account_type = 2)
        WHEN INSTR(w.receive_bank, '南京银行') THEN (SELECT account from t_bankdata_bond_to_ncc where bank_type = 2 and account_type = 2)
        WHEN INSTR(w.receive_bank, '民生银行') THEN (SELECT account from t_bankdata_bond_to_ncc where bank_type = 3 and account_type = 2)
        WHEN INSTR(w.receive_bank, '工商银行') THEN (SELECT account from t_bankdata_bond_to_ncc where bank_type = 4 and account_type = 2)
        WHEN INSTR(w.receive_bank, '建设银行') THEN (SELECT account from t_bankdata_bond_to_ncc where bank_type = 5 and account_type = 2)
        WHEN INSTR(w.receive_bank, '中信银行') THEN (SELECT account from t_bankdata_bond_to_ncc where bank_type = 6 and account_type = 2)
        WHEN INSTR(w.receive_bank, '华夏银行') THEN (SELECT account from t_bankdata_bond_to_ncc where bank_type = 7 and account_type = 2)
        WHEN INSTR(w.receive_bank, '农业银行') THEN (SELECT account from t_bankdata_bond_to_ncc where bank_type = 8 and account_type = 2)
        WHEN INSTR(w.receive_bank, '浦发银行') THEN (SELECT account from t_bankdata_bond_to_ncc where bank_type = 9 and account_type = 2)
        WHEN INSTR(w.receive_bank, '渤海银行') THEN (SELECT account from t_bankdata_bond_to_ncc where bank_type = 10 and account_type = 2)
        WHEN INSTR(w.receive_bank, '江苏银行') THEN (SELECT account from t_bankdata_bond_to_ncc where bank_type = 11 and account_type = 2)
        WHEN INSTR(w.receive_bank, '邮储银行') THEN (SELECT account from t_bankdata_bond_to_ncc where bank_type = 12 and account_type = 2)
        END as recAccount
        FROM
        t_bond_split s
        LEFT JOIN t_project_bid_section bs ON s.section_id = bs.id
        LEFT JOIN t_project p ON bs.project_id = p.id
        LEFT JOIN t_bond_water w on w.id = s.water_id
        LEFT JOIN t_customer_to_ncc n on n.name = w.company_name
        WHERE
        water_id in
        <foreach item="item" collection="waterIds" separator="," open="(" close=")" index="">
            #{item}
        </foreach>
        AND s.is_delete = 0
        AND bs.is_delete = 0
        and w.is_delete = 0
    </select>

    <!-- 根据流水查询最新一条非异常已删除拆分数据 -->
    <select id="queryLastDeleteByWaterId" resultType="com.hzw.sunflower.dto.BondSplitNccDto">
        SELECT
            s.*,
            (case when bs.package_number is not null then CONCAT(p.purchase_number,'/',bs.package_number) else p.purchase_number end) as purchase_number,
            ( SELECT share_department_code FROM t_project_share_ncc WHERE project_id = s.project_id LIMIT 1 ) account_department_code
        FROM
            t_bond_split s
        LEFT JOIN t_project_bid_section bs ON s.section_id = bs.id
        LEFT JOIN t_project p ON p.id = s.project_id
        WHERE
            s.water_id = #{waterId}
        AND s.is_delete = 1
        AND s.except_flag = 2
        ORDER BY s.created_time DESC
        limit 1
    </select>

    <!-- 根据流水查询最新的一组拆分数据 -->
    <select id="queryLatestSplit" resultType="com.hzw.sunflower.dto.BondSplitDto">
        SELECT
            s.*,
            (case when bs.package_number is not null then CONCAT(p.purchase_number,'/',bs.package_number) else p.purchase_number end) as purchaseNumber,
            ( SELECT share_department_code FROM t_project_share_ncc WHERE project_id = s.project_id LIMIT 1 ) as nccProjectDepartId
        FROM
            t_bond_split s
            LEFT JOIN t_project_bid_section bs ON s.section_id = bs.id
            LEFT JOIN t_project p ON p.id = s.project_id
        WHERE
            s.water_id = #{waterId}
          AND s.is_latest = 0
          AND s.except_flag = 2
        ORDER BY s.created_time DESC
    </select>

    <select id="queryLatestSplitByRelationId" resultType="com.hzw.sunflower.dto.BondSplitDto">
        SELECT
            s.*,
            (case when bs.package_number is not null then CONCAT(p.purchase_number,'/',bs.package_number) else p.purchase_number end) as purchaseNumber,
            ( SELECT share_department_code FROM t_project_share_ncc WHERE project_id = s.project_id LIMIT 1 ) as nccProjectDepartId
        FROM
            t_bond_split s
            LEFT JOIN t_project_bid_section bs ON s.section_id = bs.id
            LEFT JOIN t_project p ON p.id = s.project_id
        WHERE
            s.water_id = #{waterId}
          AND s.relation_id = #{relationId}
          AND s.except_flag = 2
        ORDER BY s.created_time DESC
    </select>

    <!-- 获取调整表头数据 -->
    <select id="getHeadByWaterIds" resultType="com.hzw.sunflower.dto.BondSplitNccDto">
        SELECT DISTINCT s.section_id,
            s.created_time,
            ( SELECT share_department_code FROM t_project_share_ncc WHERE project_id = s.project_id LIMIT 1 ) account_department_code,
            (case when bs.package_number is not null then CONCAT(p.purchase_number,'/',bs.package_number) else p.purchase_number end) as purchase_number
        from t_bond_split s
        LEFT JOIN t_project_bid_section bs ON s.section_id = bs.id
        LEFT JOIN t_project p on p.id = s.project_id
        where s.water_id in
        <foreach item="item" collection="waterIds" separator="," open="(" close=")" index="">
            #{item}
        </foreach>
        and s.is_delete = 0
    </select>

    <!-- 根据关联流水标段查询最新一条非异常已删除拆分数据 -->
    <select id="queryLastDeleteBySectionId" resultType="com.hzw.sunflower.dto.BondSplitNccDto">
        SELECT
            s.*,
            p.purchase_number,
            ( SELECT share_department_code FROM t_project_share_ncc WHERE project_id = s.project_id LIMIT 1 ) account_department_code
        FROM
            t_bond_split s
            LEFT JOIN t_project p ON p.id = s.project_id
        WHERE
            s.section_id in (SELECT section_id from t_bond_split where water_id = #{waterId} and is_delete = 0)
          AND s.company_id = #{companyId}
          AND s.is_delete = 1
          AND s.except_flag = 2
        ORDER BY s.created_time DESC
            limit 1
    </select>

</mapper>
