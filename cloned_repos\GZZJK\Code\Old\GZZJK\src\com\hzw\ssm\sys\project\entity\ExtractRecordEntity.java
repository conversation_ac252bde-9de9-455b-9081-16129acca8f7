package com.hzw.ssm.sys.project.entity;

import java.util.Date;
import java.util.List;

import com.hzw.ssm.expert.entity.ExpertInfoEntity;
import com.hzw.ssm.fw.base.BaseEntity;

public class ExtractRecordEntity extends BaseEntity {
	private static final long serialVersionUID = -8151736570527763583L;

	private String conId;// 抽取条件id
	private Long total;// 专家总人数
	private Long join;// 参评人数
	private Long noJoin;// 未出席人数

	private String expertTypeName;// 专家类别名称

	private Date extractTime;// 抽取时间

	private String zoneName;// 地区名称

	private List<ExpertInfoEntity> expertList;// 专家信息
	private ConditionEntity conditionEntity;// 项目抽取条件
	private Long sort;	// 排序
	private Long isTwoCon;

	public String getConId() {
		return conId;
	}

	public void setConId(String conId) {
		this.conId = conId;
	}

	public Long getTotal() {
		return total;
	}

	public void setTotal(Long total) {
		this.total = total;
	}

	public Long getJoin() {
		return join;
	}

	public void setJoin(Long join) {
		this.join = join;
	}

	public Long getNoJoin() {
		return noJoin;
	}

	public void setNoJoin(Long noJoin) {
		this.noJoin = noJoin;
	}

	public List<ExpertInfoEntity> getExpertList() {
		return expertList;
	}

	public void setExpertList(List<ExpertInfoEntity> expertList) {
		this.expertList = expertList;
	}

	public ConditionEntity getConditionEntity() {
		return conditionEntity;
	}

	public void setConditionEntity(ConditionEntity conditionEntity) {
		this.conditionEntity = conditionEntity;
	}

	public String getExpertTypeName() {
		return expertTypeName;
	}

	public void setExpertTypeName(String expertTypeName) {
		this.expertTypeName = expertTypeName;
	}

	public Date getExtractTime() {
		return extractTime;
	}

	public void setExtractTime(Date extractTime) {
		this.extractTime = extractTime;
	}

	public String getZoneName() {
		return zoneName;
	}

	public void setZoneName(String zoneName) {
		this.zoneName = zoneName;
	}

	public Long getSort() {
		return sort;
	}

	public void setSort(Long sort) {
		this.sort = sort;
	}

	public Long getIsTwoCon() {
		return isTwoCon;
	}

	public void setIsTwoCon(Long isTwoCon) {
		this.isTwoCon = isTwoCon;
	}
}
