<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hzw.sunflower.dao.UserStatisticsMapper" >
	<resultMap id="BaseResultMap" type="com.hzw.sunflower.entity.UserStatistics" >
		<id column="id" property="id" jdbcType="BIGINT" />
		<result column="year_month" property="yearmonth" jdbcType="VARCHAR" />
		<result column="department_id" property="departmentId" jdbcType="BIGINT" />
		<result column="department_name" property="departmentName" jdbcType="VARCHAR" />
		<result column="user_id" property="userId" jdbcType="BIGINT" />
		<result column="user_name" property="userName" jdbcType="VARCHAR" />
		<result column="project_count" property="projectCount" jdbcType="BIGINT" />
		<result column="no_project_count" property="noProjectCount" jdbcType="BIGINT" />
		<result column="entrusted_amount" property="entrustedAmount" jdbcType="DECIMAL" />
		<result column="no_entrusted_amount" property="noProjectCount" jdbcType="DECIMAL" />
		<result column="win_price" property="winPrice" jdbcType="DECIMAL" />
		<result column="account" property="account" jdbcType="DECIMAL" />
		<result column="estimate_account" property="estimateAccount" jdbcType="DECIMAL" />
		<result column="no_account" property="noAccount" jdbcType="DECIMAL" />
	</resultMap>
	<delete id="deleteByYearMonth">
		delete FROM `t_statistics_user` where yearmonth=#{format}
	</delete>
	<select id="queryBidOpen" resultType="com.hzw.sunflower.entity.UserStatistics">
		SELECT
		user_id,
		DATE_FORMAT( now(), '%Y-%m-01' ) AS yearmonth,
		count( user_id ) AS project_count,
		user_name,
		id AS department_id,
		department_name
		FROM
		(
		SELECT
		DISTINCT
		a.id as sectionId,
		b.user_id,
		c.user_name,
		d.id,
		d.department_name
		FROM
		`t_project_bid_section` a
		LEFT JOIN t_project_entrust_user b ON a.project_id = b.project_id
		and b.type = 1 and b.is_delete = 0
		LEFT JOIN t_user c ON c.id = b.user_id and c.is_delete = 0
		LEFT JOIN r_user_department  ud ON ud.user_id = c.id and b.department_id = ud.department_id and ud.is_delete = 0
		LEFT JOIN t_department d ON d.id = ud.department_id and d.is_delete = 0
		where a.IS_DELETE = 0
		and a.bid_round = 2
		AND a.submit_end_time_type = 1
		AND a.submit_end_time IS NOT NULL
		AND a.submit_end_time BETWEEN DATE_FORMAT( now(), '%Y-%m-01 00:00:00' )
		AND DATE_FORMAT( now(), '%Y-%m-%d %H:%i:%s' )
		and ((a.status >= 30 and a.status &lt; 70) or (a.status > 69 and a.former_abnormal_status > 29 and a.former_abnormal_status &lt; 70 ))
		) s
		GROUP BY
		user_id,
		user_name,
		department_id,
		department_name
		<!--
		SELECT
			user_id,
			DATE_FORMAT( now(), '%Y-%m-01' ) AS yearmonth,
			count( user_id ) AS project_count,
			user_name,
			id AS department_id,
			department_name
		FROM
			(
			SELECT
				a.project_id,
				b.user_id,
				c.user_name,
				d.id,
				d.department_name
			FROM
				`t_project_bid_section` a
				LEFT JOIN t_project_entrust_user b ON a.project_id = b.project_id
				LEFT JOIN t_user c ON c.id = b.user_id
				LEFT JOIN r_user_department ud ON ud.user_id = c.id
				LEFT JOIN t_department d ON d.id = ud.department_id
			WHERE
				a.submit_end_time_type = 1
				AND a.submit_end_time IS NOT NULL
				AND a.submit_end_time BETWEEN DATE_FORMAT( now(), '%Y-%m-01 00:00:00' )
				AND DATE_FORMAT( now(), '%Y-%m-%d %H:%i:%s' )
				AND b.type = 1
				and a.bid_round = 2
				AND b.is_delete = 0
			GROUP BY
				a.project_id,
				b.user_id,
				c.user_name,
				d.department_name,
				d.id
			) s
		GROUP BY
			user_id,
			user_name,
			department_id,
			department_name -->
  </select>
	<select id="queryBidNotOpen" resultType="com.hzw.sunflower.entity.UserStatistics">
		SELECT
		user_id,
		DATE_FORMAT( now(), '%Y-%m-01' ) AS yearmonth,
		count( sectionId ) AS no_project_count,
		user_name,
		id AS department_id,
		department_name
		FROM
		(
		SELECT
		DISTINCT
		a.id as sectionId,
		a.project_id,
		b.user_id,
		c.user_name,
		d.id,
		d.department_name
		FROM
		`t_project_bid_section` a
		LEFT JOIN t_project_entrust_user b ON a.project_id = b.project_id
		and b.type = 1 and b.is_delete = 0
		LEFT JOIN t_user c ON c.id = b.user_id and c.is_delete = 0
		LEFT JOIN r_user_department  ud ON ud.user_id = c.id  and b.department_id = ud.department_id and ud.is_delete = 0
		LEFT JOIN t_department d ON d.id = ud.department_id and d.is_delete = 0
		where a.IS_DELETE = 0
		<!-- and a.submit_end_time_type = 1
		and a.submit_end_time is not null
		AND a.submit_end_time > now()
		and a.bid_round = 2
		and (a.former_abnormal_status is null || a.former_abnormal_status > 29) -->
		and ((a.submit_end_time_type = 1
		and a.submit_end_time is not null
		AND a.submit_end_time > now()
		and a.bid_round = 2
		and ((a.status &lt; 30 ) or (a.status > 69 and a.former_abnormal_status &lt; 29))) or a.submit_end_time is null)
		) s
		GROUP BY
		user_id,
		user_name,
		department_id,
		department_name
		<!--
		SELECT
			user_id,
			DATE_FORMAT( now(), '%Y-%m-01' ) AS yearmonth,
			count( user_id ) AS no_project_count,
			user_name,
			id AS department_id,
			department_name
		FROM
			(
			SELECT
				a.project_id,
				b.user_id,
				c.user_name,
				d.id,
				d.department_name
			FROM
				`t_project_bid_section` a
				LEFT JOIN t_project_entrust_user b ON a.project_id = b.project_id
				LEFT JOIN t_user c ON c.id = b.user_id
				LEFT JOIN r_user_department ud ON ud.user_id = c.id
				LEFT JOIN t_department d ON d.id = ud.department_id
			WHERE
				 b.type = 1
				AND b.is_delete = 0
				AND a.project_id NOT IN (
				SELECT
					project_id
				FROM
					(
					SELECT
						a.project_id,
						b.user_id,
						c.user_name,
						d.id,
						d.department_name
					FROM
						`t_project_bid_section` a
						LEFT JOIN t_project_entrust_user b ON a.project_id = b.project_id
						LEFT JOIN t_user c ON c.id = b.user_id
						LEFT JOIN r_user_department ud ON ud.user_id = c.id
						LEFT JOIN t_department d ON d.id = ud.department_id
					WHERE
						a.submit_end_time_type = 1
						AND a.submit_end_time IS NOT NULL
						AND a.submit_end_time BETWEEN DATE_FORMAT( now(), '%Y-%m-01 00:00:00' )
						AND DATE_FORMAT( now(), '%Y-%m-%d %H:%i:%s' )
						AND b.type = 1
						and a.bid_round = 2
						AND b.is_delete = 0
					GROUP BY
						a.project_id,
						b.user_id,
						c.user_name,
						d.department_name,
						d.id
					) ss
				)
			GROUP BY
				a.project_id,
				b.user_id,
				c.user_name,
				d.department_name,
				d.id
			) s
		GROUP BY
			user_id,
			user_name,
			department_id,
			department_name -->
  </select>
	<select id="queryBidOpenMoney" resultType="com.hzw.sunflower.entity.UserStatistics">
		SELECT
		user_id,
		DATE_FORMAT( now(), '%Y-%m-01' ) AS yearmonth,
		user_name,
		id AS department_id,
		department_name,
		sum(
		entrust_money /(
		1 / s.
		VALUE
		)) AS entrusted_amount
		FROM
		(
		SELECT
		DISTINCT
		a.id as sectionId,
		a.project_id,
		b.user_id,
		c.user_name,
		d.id,
		d.department_name,
		m.
		VALUE
		,
		a.package_number,
		a.entrust_money/10000 AS entrust_money
		FROM
		`t_project_bid_section` a
		LEFT JOIN t_project_entrust_user b ON a.project_id = b.project_id
		AND b.type = 1  and b.is_delete = 0
		LEFT JOIN t_user c ON c.id = b.user_id and c.is_delete = 0
		LEFT JOIN r_user_department  ud ON ud.user_id = c.id  and b.department_id = ud.department_id and ud.is_delete = 0
		LEFT JOIN t_department d ON d.id = ud.department_id and d.is_delete = 0
		LEFT JOIN t_dictionary m ON m.id = a.entrust_currency and m.is_delete = 0
		WHERE
		a.submit_end_time_type = 1
		and a.bid_round = 2
		AND a.submit_end_time IS NOT NULL
		AND a.submit_end_time BETWEEN DATE_FORMAT( now(), '%Y-%m-01 00:00:00' )
		AND DATE_FORMAT( now(), '%Y-%m-%d %H:%i:%s' )
		and ((a.status >= 30 and a.status &lt; 70) or (a.status > 69 and a.former_abnormal_status > 29 and a.former_abnormal_status &lt; 70))
		) s
		GROUP BY
		user_id,
		user_name,
		department_id,
		department_name
		<!-- SELECT
			user_id,
			DATE_FORMAT( now(), '%Y-%m-01' ) AS yearmonth,
			user_name,
			id AS department_id,
			department_name,
			sum(
				entrust_money /(
					1 / s.
				VALUE

				)) AS entrusted_amount
		FROM
			(
			SELECT
				a.project_id,
				b.user_id,
				c.user_name,
				d.id,
				d.department_name,
				m.
			VALUE
				,
				a.package_number,
				a.entrust_money/10000 AS entrust_money
			FROM
				`t_project_bid_section` a
				LEFT JOIN t_project_entrust_user b ON a.project_id = b.project_id
				LEFT JOIN t_user c ON c.id = b.user_id
				LEFT JOIN r_user_department ud ON ud.user_id = c.id
				LEFT JOIN t_department d ON d.id = ud.department_id
				LEFT JOIN t_dictionary m ON m.id = a.entrust_currency
			WHERE
				a.submit_end_time_type = 1
				AND a.submit_end_time IS NOT NULL
				AND a.submit_end_time BETWEEN DATE_FORMAT( now(), '%Y-%m-01 00:00:00' )
				AND DATE_FORMAT( now(), '%Y-%m-%d %H:%i:%s' )
				AND b.type = 1
				and a.bid_round = 2
				AND b.is_delete = 0
			GROUP BY
				a.project_id,
				b.user_id,
				c.user_name,
				d.department_name,
				d.id,
				a.entrust_money,
				a.package_number,
				m.
			VALUE
			) s
		GROUP BY
			user_id,
			user_name,
			department_id,
			department_name -->
  </select>
	<select id="queryBidNotOpenMoney" resultType="com.hzw.sunflower.entity.UserStatistics">
		SELECT
		user_id,
		DATE_FORMAT( now(), '%Y-%m-01' ) AS yearmonth,
		user_name,
		id AS department_id,
		department_name,
		sum(
		entrust_money /(
		1 / s.
		VALUE

		)) AS no_entrusted_amount
		FROM
		(
		SELECT
		DISTINCT
		a.id as sectionId,
		a.project_id,
		b.user_id,
		c.user_name,
		d.id,
		d.department_name,
		m.
		VALUE
		,
		a.package_number,
		a.entrust_money/10000 AS entrust_money
		FROM
		`t_project_bid_section` a
		LEFT JOIN t_project_entrust_user b ON a.project_id = b.project_id
		and b.type = 1 and b.is_delete = 0
		LEFT JOIN t_user c ON c.id = b.user_id and c.is_delete = 0
		LEFT JOIN r_user_department  ud ON ud.user_id = c.id  and b.department_id = ud.department_id and ud.is_delete = 0
		LEFT JOIN t_department d ON d.id = ud.department_id and d.is_delete = 0
		LEFT JOIN t_dictionary m ON m.id = a.entrust_currency and m.is_delete = 0
		where a.IS_DELETE = 0
		<!-- and a.bid_round = 2
		and a.submit_end_time_type = 1
		and a.submit_end_time is not null
		AND a.submit_end_time > now()
		and (a.former_abnormal_status is null || a.former_abnormal_status > 29) -->
		and ((a.submit_end_time_type = 1
		and a.submit_end_time is not null
		AND a.submit_end_time > now()
		and a.bid_round = 2
		and ((a.status &lt; 30 ) or (a.status > 69 and a.former_abnormal_status &lt; 29))) or a.submit_end_time is null)
		) s
		GROUP BY
		user_id,
		user_name,
		department_id,
		department_name
		<!-- SELECT
			user_id,
			DATE_FORMAT( now(), '%Y-%m-01' ) AS yearmonth,
			user_name,
			id AS department_id,
			department_name,
			sum(
				entrust_money /(
					1 / s.
				VALUE

				)) AS no_entrusted_amount
		FROM
			(
			SELECT
				a.project_id,
				b.user_id,
				c.user_name,
				d.id,
				d.department_name,
				m.
			VALUE
				,
				a.package_number,
				a.entrust_money/10000 AS entrust_money
			FROM
				`t_project_bid_section` a
				LEFT JOIN t_project_entrust_user b ON a.project_id = b.project_id
				LEFT JOIN t_user c ON c.id = b.user_id
				LEFT JOIN r_user_department ud ON ud.user_id = c.id
				LEFT JOIN t_department d ON d.id = ud.department_id
				LEFT JOIN t_dictionary m ON m.id = a.entrust_currency
			WHERE

			   b.type = 1
				AND b.is_delete = 0
				AND a.project_id NOT IN (
				SELECT
					project_id
				FROM
					(
					SELECT
						a.project_id,
						b.user_id,
						c.user_name,
						d.id,
						d.department_name
					FROM
						`t_project_bid_section` a
						LEFT JOIN t_project_entrust_user b ON a.project_id = b.project_id
						LEFT JOIN t_user c ON c.id = b.user_id
						LEFT JOIN r_user_department ud ON ud.user_id = c.id
						LEFT JOIN t_department d ON d.id = ud.department_id
					WHERE
						a.submit_end_time_type = 1
						AND a.submit_end_time IS NOT NULL
						AND a.submit_end_time BETWEEN DATE_FORMAT( now(), '%Y-%m-01 00:00:00' )
						AND DATE_FORMAT( now(), '%Y-%m-%d %H:%i:%s' )
						AND b.type = 1
						and a.bid_round = 2
						AND b.is_delete = 0
					GROUP BY
						a.project_id,
						b.user_id,
						c.user_name,
						d.department_name,
						d.id
					) ss
				)

			GROUP BY
				a.project_id,
				b.user_id,
				c.user_name,
				d.department_name,
				d.id,
				a.package_number,
				a.entrust_money,
				m.
			VALUE
			) s
		GROUP BY
			user_id,
			user_name,
			department_id,
			department_name -->
    </select>
	<select id="selectId" resultType="com.hzw.sunflower.entity.UserStatistics">
        select * from t_statistics_user where user_id=#{userId} and yearmonth=#{month}
    </select>
	<select id="selectUserStatisticsByMonthAndCondition" resultType="com.hzw.sunflower.entity.UserStatistics">
        select * from t_statistics_user where yearmonth between #{userStatisticsReq.startTime} and #{userStatisticsReq.endTime}
    </select>
	<select id="selectDepartmentStatisticsByMonthAndCondition" resultType="com.hzw.sunflower.entity.UserStatistics">
		SELECT
			department_id,
			department_name,
			yearmonth,
			sum( project_count ),
			sum( no_project_count ),
			sum( entrusted_amount ),
			sum( no_entrusted_amount ),
			sum( win_price ),
			sum( account ),
			sum( estimate_account ),
			sum( no_account )
		FROM
			t_statistics_user
		WHERE
			yearmonth BETWEEN #{userStatisticsReq.startTime} and #{userStatisticsReq.endTime}

		GROUP BY
			department_id,
			department_name,
			yearmonth
    </select>
	<select id="selectCompanyStatisticsByMonthAndCondition" resultType="com.hzw.sunflower.entity.UserStatistics">
		SELECT
			yearmonth,
			sum( project_count ),
			sum( no_project_count ),
			sum( entrusted_amount ),
			sum( no_entrusted_amount ),
			sum( win_price ),
			sum( account ),
			sum( estimate_account ),
			sum( no_account )
		FROM
			t_statistics_user
		WHERE
			yearmonth BETWEEN #{userStatisticsReq.startTime} and #{userStatisticsReq.endTime}
		GROUP BY
			yearmonth
    </select>
	<select id="selectUserStatisticsByMonthAndConditionAgo" resultType="com.hzw.sunflower.entity.UserStatistics">
        select * from t_statistics_user where yearmonth between #{userStatisticsReq.startTimeAgo} and #{userStatisticsReq.endTimeAgo}
    </select>
	<select id="selectDepartmentStatisticsByMonthAndConditionAgo" resultType="com.hzw.sunflower.entity.UserStatistics">
		SELECT
			department_id,
			department_name,
			yearmonth,
			sum( project_count ),
			sum( no_project_count ),
			sum( entrusted_amount ),
			sum( no_entrusted_amount ),
			sum( win_price ),
			sum( account ),
			sum( estimate_account ),
			sum( no_account )
		FROM
			t_statistics_user
		WHERE
			yearmonth BETWEEN #{userStatisticsReq.startTimeAgo} and #{userStatisticsReq.endTimeAgo}
		GROUP BY
			department_id,
			department_name,
			yearmonth
    </select>
	<select id="selectCompanyStatisticsByMonthAndConditionAgo" resultType="com.hzw.sunflower.entity.UserStatistics">
		SELECT
			yearmonth,
			sum( project_count ),
			sum( no_project_count ),
			sum( entrusted_amount ),
			sum( no_entrusted_amount ),
			sum( win_price ),
			sum( account ),
			sum( estimate_account ),
			sum( no_account )
		FROM
			t_statistics_user
		WHERE
			yearmonth BETWEEN #{userStatisticsReq.startTimeAgo} and #{userStatisticsReq.endTimeAgo}
		GROUP BY
			yearmonth
    </select>
	<select id="selectCompanyStatistics" resultType="com.hzw.sunflower.controller.response.UserStatisticsVO">
		SELECT
			   a.yearmonth,
			DATE_FORMAT(a.yearmonth,'%m') as mon,
		IF
			( a.project_count, a.project_count, 0 ) as project_count,
		IF
			( a.no_project_count, a.no_project_count, 0 ) as no_project_count,
		IF
			( a.entrusted_amount, a.entrusted_amount, 0 ) as entrusted_amount,
		IF
			( a.no_entrusted_amount, a.no_entrusted_amount, 0 ) as no_entrusted_amount,
		IF
			( a.win_price, a.win_price, 0 ) as win_price,
		IF
			( a.account, a.account, 0 ) as account,
		IF
			( a.estimate_account, a.estimate_account, 0 ) as estimate_account,
		IF
			( a.no_account, a.no_account, 0 ) as no_account,
		IF
			( b.project_count_ago, b.project_count_ago, 0 ) as project_count_ago,
		IF
			( b.no_project_count_ago, b.no_project_count_ago, 0 ) as no_project_count_ago,
		IF
			( b.entrusted_amount_ago, b.entrusted_amount_ago, 0 ) as entrusted_amount_ago,
		IF
			( b.no_entrusted_amount_ago, b.no_entrusted_amount_ago, 0 ) as no_entrusted_amount_ago,
		IF
			( b.win_price_ago, b.win_price_ago, 0 ) as win_price_ago,
		IF
			( b.account_ago, b.account_ago, 0 ) as account_ago,
		IF
			( b.estimate_account_ago, b.estimate_account_ago, 0 ) as estimate_account_ago,
		IF
			( b.no_account_ago, b.no_account_ago, 0 ) as no_account_ago,
		IF
			( a.project_count - b.project_count_ago, a.project_count - b.project_count_ago, 0 ) AS project_count_minus,
		IF
			( a.no_project_count - b.no_project_count_ago, a.no_project_count - b.no_project_count_ago, 0 ) AS no_project_count_minus,
		IF
			( a.entrusted_amount - b.entrusted_amount_ago, a.entrusted_amount - b.entrusted_amount_ago, 0 ) AS entrusted_amount_minus,
		IF
			( a.no_entrusted_amount - b.no_entrusted_amount_ago, a.no_entrusted_amount - b.no_entrusted_amount_ago, 0 ) AS no_entrusted_amount_minus,
		IF
			( a.win_price - b.win_price_ago, a.win_price - b.win_price_ago, 0 ) AS win_price_minus,
		IF
			( a.account - b.account_ago, a.account - b.account_ago, 0 ) AS account_minus,
		IF
			( a.estimate_account - b.estimate_account_ago, a.estimate_account - b.estimate_account_ago, 0 ) AS estimate_account_minus,
		IF
			( a.no_account - b.no_account_ago, a.no_account - b.no_account_ago, 0 ) AS no_account_minus
		FROM
			(
			SELECT
				yearmonth,
				IF
				(sum( project_count ), sum( project_count ), 0 ) AS project_count,
				IF
				(sum( no_project_count ), sum( no_project_count ), 0 ) AS no_project_count,
				IF
				(sum( entrusted_amount ), sum( entrusted_amount ), 0 ) AS entrusted_amount,
				IF
				(sum( no_entrusted_amount ), sum( no_entrusted_amount ), 0 ) AS no_entrusted_amount,
				IF
				(sum( win_price ), sum( win_price ), 0 ) AS win_price,
				IF
				(sum( account ), sum( account ), 0 ) AS account,
				IF
				(sum( estimate_account ), sum( estimate_account ), 0 ) AS estimate_account,
				IF
				(sum( no_account ), sum( no_account ), 0 ) AS no_account
			FROM
				t_statistics_user
			WHERE
				yearmonth BETWEEN #{userStatisticsReq.startTime} and #{userStatisticsReq.endTime}

			GROUP BY
				yearmonth
			) a
			LEFT JOIN (
			SELECT
				yearmonth,
				IF
				(sum( project_count ), sum( project_count ), 0 ) AS project_count_ago,
				IF
				(sum( no_project_count ), sum( no_project_count ), 0 ) AS no_project_count_ago,
				IF
				(sum( entrusted_amount ), sum( entrusted_amount ), 0 ) AS entrusted_amount_ago,
				IF
				(sum( no_entrusted_amount ), sum( no_entrusted_amount ), 0 ) AS no_entrusted_amount_ago,
				IF
				(sum( win_price ), sum( win_price ), 0 ) AS win_price_ago,
				IF
				(sum( account ), sum( account ), 0 ) AS account_ago,
				IF
				(sum( estimate_account ), sum( estimate_account ), 0 ) AS estimate_account_ago,
				IF
				(sum( no_account ), sum( no_account ), 0 ) AS no_account_ago
			FROM
				t_statistics_user
			WHERE
				yearmonth BETWEEN #{userStatisticsReq.startTimeAgo} and #{userStatisticsReq.endTimeAgo}
			GROUP BY
			yearmonth
			) b ON date_format(a.yearmonth,'%m') = date_format(b.yearmonth,'%m')
    </select>
	<select id="selectDepartmentStatistics" resultType="com.hzw.sunflower.controller.response.UserStatisticsVO">
		SELECT
			a.department_id,
			a.department_name,
		IF
			( a.project_count, a.project_count, 0 ) as project_count,
		IF
			( a.no_project_count, a.no_project_count, 0 ) as no_project_count,
		IF
			( a.entrusted_amount, a.entrusted_amount, 0 ) as entrusted_amount,
		IF
			( a.no_entrusted_amount, a.no_entrusted_amount, 0 ) as no_entrusted_amount,
		IF
			( a.win_price, a.win_price, 0 ) as win_price,
		IF
			( a.account, a.account, 0 ) as account,
		IF
			( a.estimate_account, a.estimate_account, 0 ) as estimate_account,
		IF
			( a.no_account, a.no_account, 0 ) as no_account,
		IF
			( b.project_count_ago, b.project_count_ago, 0 ) as project_count_ago,
		IF
			( b.no_project_count_ago, b.no_project_count_ago, 0 ) as no_project_count_ago,
		IF
			( b.entrusted_amount_ago, b.entrusted_amount_ago, 0 ) as entrusted_amount_ago,
		IF
			( b.no_entrusted_amount_ago, b.no_entrusted_amount_ago, 0 ) as no_entrusted_amount_ago,
		IF
			( b.win_price_ago, b.win_price_ago, 0 ) as win_price_ago,
		IF
			( b.account_ago, b.account_ago, 0 ) as account_ago,
		IF
			( b.estimate_account_ago, b.estimate_account_ago, 0 ) as estimate_account_ago,
		IF
			( b.no_account_ago, b.no_account_ago, 0 ) as no_account_ago,
		IF
			( a.project_count - b.project_count_ago, a.project_count - b.project_count_ago, 0 ) AS project_count_minus,
		IF
			( a.no_project_count - b.no_project_count_ago, a.no_project_count - b.no_project_count_ago, 0 ) AS no_project_count_minus,
		IF
			( a.entrusted_amount - b.entrusted_amount_ago, a.entrusted_amount - b.entrusted_amount_ago, 0 ) AS entrusted_amount_minus,
		IF
			( a.no_entrusted_amount - b.no_entrusted_amount_ago, a.no_entrusted_amount - b.no_entrusted_amount_ago, 0 ) AS no_entrusted_amount_minus,
		IF
			( a.win_price - b.win_price_ago, a.win_price - b.win_price_ago, 0 ) AS win_price_minus,
		IF
			( a.account - b.account_ago, a.account - b.account_ago, 0 ) AS account_minus,
		IF
			( a.estimate_account - b.estimate_account_ago, a.estimate_account - b.estimate_account_ago, 0 ) AS estimate_account_minus,
		IF
			( a.no_account - b.no_account_ago, a.no_account - b.no_account_ago, 0 ) AS no_account_minus
		FROM
			(
			SELECT
				department_id,
				department_name,
				IF
				(sum( project_count ), sum( project_count ), 0 ) AS project_count,
				IF
				(sum( no_project_count ), sum( no_project_count ), 0 ) AS no_project_count,
				IF
				(sum( entrusted_amount ), sum( entrusted_amount ), 0 ) AS entrusted_amount,
				IF
				(sum( no_entrusted_amount ), sum( no_entrusted_amount ), 0 ) AS no_entrusted_amount,
				IF
				(sum( win_price ), sum( win_price ), 0 ) AS win_price,
				IF
				(sum( account ), sum( account ), 0 ) AS account,
				IF
				(sum( estimate_account ), sum( estimate_account ), 0 ) AS estimate_account,
				IF
				(sum( no_account ), sum( no_account ), 0 ) AS no_account
			FROM
				t_statistics_user
			WHERE
				yearmonth BETWEEN  #{userStatisticsReq.startTime} and #{userStatisticsReq.endTime}

			GROUP BY
				department_id,
				department_name
			) a
			LEFT JOIN (
			SELECT
				department_id,
				IF
				(sum( project_count ), sum( project_count ), 0 ) AS project_count_ago,
				IF
				(sum( no_project_count ), sum( no_project_count ), 0 ) AS no_project_count_ago,
				IF
				(sum( entrusted_amount ), sum( entrusted_amount ), 0 ) AS entrusted_amount_ago,
				IF
				(sum( no_entrusted_amount ), sum( no_entrusted_amount ), 0 ) AS no_entrusted_amount_ago,
				IF
				(sum( win_price ), sum( win_price ), 0 ) AS win_price_ago,
				IF
				(sum( account ), sum( account ), 0 ) AS account_ago,
				IF
				(sum( estimate_account ), sum( estimate_account ), 0 ) AS estimate_account_ago,
				IF
				(sum( no_account ), sum( no_account ), 0 ) AS no_account_ago
			FROM
				t_statistics_user
			WHERE
				yearmonth BETWEEN  #{userStatisticsReq.startTimeAgo} and #{userStatisticsReq.endTimeAgo}

			GROUP BY
			department_id
			) b ON a.department_id = b.department_id
			where a.department_id is not null and a.department_name is not null
    </select>
	<select id="selectUserStatistics" resultType="com.hzw.sunflower.controller.response.UserStatisticsVO">
		SELECT
			a.user_id,
			a.user_name,
		IF
			( a.project_count, a.project_count, 0 ) as project_count,
		IF
			( a.no_project_count, a.no_project_count, 0 ) as no_project_count,
		IF
			( a.entrusted_amount, a.entrusted_amount, 0 ) as entrusted_amount,
		IF
			( a.no_entrusted_amount, a.no_entrusted_amount, 0 ) as no_entrusted_amount,
		IF
			( a.win_price, a.win_price, 0 ) as win_price,
		IF
			( a.account, a.account, 0 ) as account,
		IF
			( a.estimate_account, a.estimate_account, 0 ) as estimate_account,
		IF
			( a.no_account, a.no_account, 0 ) as no_account,
		IF
			( b.project_count_ago, b.project_count_ago, 0 ) as project_count_ago,
		IF
			( b.no_project_count_ago, b.no_project_count_ago, 0 ) as no_project_count_ago,
		IF
			( b.entrusted_amount_ago, b.entrusted_amount_ago, 0 ) as entrusted_amount_ago,
		IF
			( b.no_entrusted_amount_ago, b.no_entrusted_amount_ago, 0 ) as no_entrusted_amount_ago,
		IF
			( b.win_price_ago, b.win_price_ago, 0 ) as win_price_ago,
		IF
			( b.account_ago, b.account_ago, 0 ) as account_ago,
		IF
			( b.estimate_account_ago, b.estimate_account_ago, 0 ) as estimate_account_ago,
		IF
			( b.no_account_ago, b.no_account_ago, 0 ) as no_account_ago,
		IF
			( a.project_count - b.project_count_ago, a.project_count - b.project_count_ago, 0 ) AS project_count_minus,
		IF
			( a.no_project_count - b.no_project_count_ago, a.no_project_count - b.no_project_count_ago, 0 ) AS no_project_count_minus,
		IF
			( a.entrusted_amount - b.entrusted_amount_ago, a.entrusted_amount - b.entrusted_amount_ago, 0 ) AS entrusted_amount_minus,
		IF
			( a.no_entrusted_amount - b.no_entrusted_amount_ago, a.no_entrusted_amount - b.no_entrusted_amount_ago, 0 ) AS no_entrusted_amount_minus,
		IF
			( a.win_price - b.win_price_ago, a.win_price - b.win_price_ago, 0 ) AS win_price_minus,
		IF
			( a.account - b.account_ago, a.account - b.account_ago, 0 ) AS account_minus,
		IF
			( a.estimate_account - b.estimate_account_ago, a.estimate_account - b.estimate_account_ago, 0 ) AS estimate_account_minus,
		IF
			( a.no_account - b.no_account_ago, a.no_account - b.no_account_ago, 0 ) AS no_account_minus
		FROM
			(
			SELECT
				user_id,
				user_name,
				IF
				(sum( project_count ), sum( project_count ), 0 ) AS project_count,
				IF
				(sum( no_project_count ), sum( no_project_count ), 0 ) AS no_project_count,
				IF
				(sum( entrusted_amount ), sum( entrusted_amount ), 0 ) AS entrusted_amount,
				IF
				(sum( no_entrusted_amount ), sum( no_entrusted_amount ), 0 ) AS no_entrusted_amount,
				IF
				(sum( win_price ), sum( win_price ), 0 ) AS win_price,
				IF
				(sum( account ), sum( account ), 0 ) AS account,
				IF
				(sum( estimate_account ), sum( estimate_account ), 0 ) AS estimate_account,
				IF
				(sum( no_account ), sum( no_account ), 0 ) AS no_account
			FROM
				t_statistics_user
			WHERE
				yearmonth BETWEEN #{userStatisticsReq.startTime} and #{userStatisticsReq.endTime}

			GROUP BY
				user_id,
				user_name
			) a
			LEFT JOIN (
			SELECT
				user_id,
				user_name,
				IF
				(sum( project_count ), sum( project_count ), 0 ) AS project_count_ago,
				IF
				(sum( no_project_count ), sum( no_project_count ), 0 ) AS no_project_count_ago,
				IF
				(sum( entrusted_amount ), sum( entrusted_amount ), 0 ) AS entrusted_amount_ago,
				IF
				(sum( no_entrusted_amount ), sum( no_entrusted_amount ), 0 ) AS no_entrusted_amount_ago,
				IF
				(sum( win_price ), sum( win_price ), 0 ) AS win_price_ago,
				IF
				(sum( account ), sum( account ), 0 ) AS account_ago,
				IF
				(sum( estimate_account ), sum( estimate_account ), 0 ) AS estimate_account_ago,
				IF
				(sum( no_account ), sum( no_account ), 0 ) AS no_account_ago
			FROM
				t_statistics_user
			WHERE
				yearmonth BETWEEN #{userStatisticsReq.startTimeAgo} and #{userStatisticsReq.endTimeAgo}

			GROUP BY
				user_id,
			user_name
			) b ON a.user_id = b.user_id
    </select>
	<select id="selectDepartmentStatisticsBasis" resultType="com.hzw.sunflower.controller.response.DepartmentStatisticsVO">
		SELECT
		a.department_id,
		a.department_name,
		IF
		( a.project_count, a.project_count, 0 ) as project_count,
		IF
		( a.entrusted_amount, a.entrusted_amount, 0 ) as entrusted_amount,
		IF
		( b.project_count_ago, b.project_count_ago, 0 ) as project_count_ago ,
		IF
		( b.entrusted_amount_ago, b.entrusted_amount_ago, 0 ) as entrusted_amount_ago ,
		IF(
		TRUNCATE (( a.project_count / b.project_count_ago - 1 )/ 0.01, 2 ),TRUNCATE (( a.project_count / b.project_count_ago - 1 )/ 0.01, 2 ),null) AS project_count_basis,
		TRUNCATE (( a.entrusted_amount / b.entrusted_amount_ago - 1 )/ 0.01, 2 ) AS entrusted_amount_basis

		FROM
		(
		SELECT
		department_id,
		department_name,
		IF
		(sum( project_count ), sum( project_count ), 0 ) AS project_count,
		IF
		(sum( entrusted_amount ), sum( entrusted_amount ), 0 ) AS entrusted_amount
		FROM
		t_statistics_user
		WHERE
		yearmonth BETWEEN #{userStatisticsReq.startTime} and #{userStatisticsReq.endTime}
		GROUP BY
		department_id,
		department_name
		) a
		LEFT JOIN (
		SELECT
		department_id,
		IF
		(sum( project_count ), sum( project_count ), 0 ) AS project_count_ago,
		IF
		(sum( entrusted_amount ), sum( entrusted_amount ), 0 ) AS entrusted_amount_ago
		FROM
		t_statistics_user
		WHERE
		yearmonth BETWEEN #{userStatisticsReq.startTimeAgo} and #{userStatisticsReq.endTimeAgo}
		GROUP BY
		department_id
		) b ON a.department_id = b.department_id
		<if test='userStatisticsReq.order == "1" and userStatisticsReq.prop == "1"' >
			order by ISNULL(project_count_basis),project_count_basis Asc
		</if>
		<if test='userStatisticsReq.order == "1" and userStatisticsReq.prop == "2"' >
			order by ISNULL(entrusted_amount_basis),entrusted_amount_basis Asc
		</if>
		<if test='userStatisticsReq.order == "2" and userStatisticsReq.prop == "2"' >
			order by ISNULL(entrusted_amount_basis),entrusted_amount_basis desc
		</if>
		<if test='userStatisticsReq.order == "2" and userStatisticsReq.prop == "1"' >
			order by ISNULL(project_count_basis),project_count_basis desc
		</if>
	</select>
	<select id="queryBidOpenAgencyMoney" resultType="com.hzw.sunflower.entity.UserStatistics">
		SELECT
			user_id,
			DATE_FORMAT( now(), '%Y-%m-01' ) AS yearmonth,
			user_name,
			id AS department_id,
			department_name,
			Sum( ss_agency_fee )* 0.0001 AS account
		FROM
			(
			SELECT
				DISTINCT
				a.id as sectionId,
				a.project_id,
				b.user_id,
				c.user_name,
				d.id,
				d.department_name,
				a.id AS section_id,
				m.id AS id1,
				m.ss_agency_fee,
				a.STATUS,
				m.quoted_price_type
			FROM
				`t_project_bid_section` a
				LEFT JOIN t_project_entrust_user b ON a.project_id = b.project_id
					AND b.type = 1 and b.is_delete = 0
				LEFT JOIN t_user c ON c.id = b.user_id and c.is_delete = 0
				LEFT JOIN r_user_department  ud ON ud.user_id = c.id and b.department_id = ud.department_id and ud.is_delete = 0
				LEFT JOIN t_department d ON d.id = ud.department_id and d.is_delete = 0
				LEFT JOIN t_bid_win_notice h ON h.section_id = a.id and h.is_delete = 0
				LEFT JOIN t_bid_win_people m ON h.id = m.winbid_id  AND m.win_people_type = 1 and m.is_delete = 0
			WHERE
				a.is_delete = 0
				and a.bid_round = 2
				and a.submit_end_time_type = 1
				AND a.submit_end_time IS NOT NULL
				AND a.submit_end_time BETWEEN DATE_FORMAT( now(), '%Y-%m-01 00:00:00' )
				AND DATE_FORMAT( now(), '%Y-%m-%d %H:%i:%s' )
				AND a.STATUS > 45
				AND a.STATUS != 70
				AND a.STATUS != 71
				AND a.STATUS != 72
			) s
		GROUP BY
			user_id,
			user_name,
			department_id,
			department_name
    </select>
	<select id="queryBidNotOpenAgencyMoney" resultType="com.hzw.sunflower.entity.UserStatistics">
		SELECT
			user_id,
			DATE_FORMAT( now(), '%Y-%m-01' ) AS yearmonth,
			user_name,
			id AS department_id,
			department_name,
			Sum( agency_fee_value )* 0.0001 AS no_account
		FROM
			(
			SELECT
				DISTINCT
				a.project_id,
				a.id AS section_id,
				b.user_id,
				c.user_name,
				d.id,
				d.department_name,
				a.agency_fee_value
			FROM
				`t_project_bid_section` a
				LEFT JOIN t_project_entrust_user b ON a.project_id = b.project_id
					AND b.type = 1 AND b.is_delete = 0
				LEFT JOIN t_user c ON c.id = b.user_id and c.is_delete = 0
				LEFT JOIN r_user_department  ud ON ud.user_id = c.id and b.department_id = ud.department_id and ud.is_delete = 0
				LEFT JOIN t_department d ON d.id = ud.department_id and d.is_delete = 0
			WHERE
			    a.is_delete = 0
			    and a.bid_round = 2
			    and a.submit_end_time_type = 1
				and a.submit_end_time IS NOT NULL
				AND a.submit_end_time BETWEEN DATE_FORMAT( now(), '%Y-%m-01 00:00:00' )
				AND DATE_FORMAT( now(), '%Y-%m-%d %H:%i:%s' )
				AND (
					a.STATUS &lt; 46 AND a.STATUS > 29
					OR ( a.former_abnormal_status > 29 AND a.former_abnormal_status &lt; 46 ))
			) s
		GROUP BY
			user_id,
			user_name,
			department_id,
			department_name
    </select>
	<select id="queryBidOpenWinMoney" resultType="com.hzw.sunflower.entity.UserStatistics">
		SELECT
			q.user_id,
			DATE_FORMAT( now(), '%Y-%m-01' ) AS yearmonth,
			q.user_name,
			q.id AS department_id,
			q.department_name,
			sum((
					price + price2
				))* 0.0001 AS win_price
		FROM
			(
			SELECT
				p.user_id,
				p.user_name,
				p.id,
				p.department_name,
				sum( IF ( p.quoted_price_type = '1', 0, p.entrust_money /( 1 / p.VALUE )/ 1.25 ) ) AS price,
				sum(
				IF
				( p.quoted_price_type = '1', p.write_content /( 1 / p.value1 ), 0 )) AS price2
			FROM
				(
					SELECT
						DISTINCT
						a.id as sectionId,
						a.project_id,
						b.user_id,
						c.user_name,
						d.id,
						d.department_name,
						m.id AS id1,
						m.write_content,
						a.entrust_money,
						a.STATUS,
						m.quoted_price_type,
						s.`value` AS
				VALUE
					,
					case CONCAT(m.currency_type,"") when 0 then 1 when 1 then 6.75 when 2 then 7 when 3 then 0.05 end as value1
					,
					a.entrust_currency,
					m.currency_type
					FROM
						`t_project_bid_section` a
						LEFT JOIN t_project_entrust_user b ON a.project_id = b.project_id
						AND b.type = 1 AND b.is_delete = 0
						LEFT JOIN t_user c ON c.id = b.user_id and c.is_delete = 0
						LEFT JOIN r_user_department  ud ON ud.user_id = c.id  and b.department_id = ud.department_id and ud.is_delete = 0
						LEFT JOIN t_department d ON d.id = ud.department_id  and d.is_delete = 0
						LEFT JOIN t_bid_win_notice h ON h.section_id = a.id and h.is_delete = 0
						LEFT JOIN t_bid_win_people m ON h.id = m.winbid_id AND m.is_delete = 0
						AND m.is_win = 1
						LEFT JOIN t_dictionary s ON s.id = a.entrust_currency  and s.is_delete = 0
					WHERE
					  a.submit_end_time_type = 1
					  AND a.submit_end_time IS NOT NULL
					  AND a.submit_end_time BETWEEN DATE_FORMAT( now(), '%Y-%m-01 00:00:00' )
					  AND DATE_FORMAT( now(), '%Y-%m-%d %H:%i:%s' )
					  AND a.STATUS > 45
					  AND a.STATUS != 70
					  AND a.STATUS != 71
					  AND a.STATUS != 72
					  and a.bid_round = 2
				) p
			GROUP BY
				p.quoted_price_type,
				p.user_id,
				p.user_name,
				p.id,
				p.department_name
			) q
		GROUP BY
			q.user_id,
			q.user_name,
			q.id,
			q.department_name
    </select>

	<select id="queryBidNotOpenWinMoney" resultType="com.hzw.sunflower.entity.UserStatistics">
		SELECT
			q.user_id,
			DATE_FORMAT( now(), '%Y-%m-01' ) AS yearmonth,
			q.user_name,
			q.id AS department_id,
			q.department_name,
			sum( price2 ) AS estimate_account
		FROM
			(
			SELECT
				p.user_id,
				p.user_name,
				p.id,
				p.department_name,
				sum(
				IF
					(
					p.quoted_price_type = '1' &amp;&amp;
					p.bid_amount != NULL,
					p.bid_amount,
					p.entrust_money/10000 /(
						1 / p.
						VALUE
						)/ 1.25
					)) AS price2
			FROM
				(
				SELECT
				    distinct
				    a.id as sectionId,
					a.project_id,
					b.user_id,
					c.user_name,
					d.id,
					d.department_name,
					a.id AS section_id,
					h.bid_amount,
					a.entrust_money,
					a.STATUS,
					m.quoted_price_type,
					s.
				VALUE
				FROM
					`t_project_bid_section` a
					LEFT JOIN t_project_entrust_user b ON a.project_id = b.project_id
					and b.type = 1 and b.is_delete = 0
					LEFT JOIN t_user c ON c.id = b.user_id and c.is_delete = 0
					LEFT JOIN r_user_department  ud ON ud.user_id = c.id  and b.department_id = ud.department_id and ud.is_delete = 0
					LEFT JOIN t_department d ON d.id = ud.department_id  and d.is_delete = 0
					LEFT JOIN t_bid_win_bulletin h ON h.related_bid_section = a.id  and h.is_delete = 0
					LEFT JOIN t_bid_win m ON m.section_id = a.id and m.is_delete = 0
					LEFT JOIN t_dictionary s ON s.id = a.entrust_currency and s.is_delete = 0
				WHERE
				    a.is_delete = 0
				    and a.bid_round = 2
					and a.submit_end_time_type = 1
					AND a.submit_end_time IS NOT NULL
					AND a.submit_end_time BETWEEN DATE_FORMAT( now(), '%Y-%m-01 00:00:00' )
					AND DATE_FORMAT( now(), '%Y-%m-%d %H:%i:%s' )
					AND (
						a.STATUS &lt; 46 AND a.STATUS > 29
						OR (a.former_abnormal_status > 29 AND a.former_abnormal_status &lt; 46 ))
				) p
			GROUP BY
				p.quoted_price_type,
				p.user_id,
				p.user_name,
				p.id,
				p.department_name
			) q
		GROUP BY
			q.user_id,
			q.user_name,
			q.id,
			q.department_name
    </select>
</mapper>
