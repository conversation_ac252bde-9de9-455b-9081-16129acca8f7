package com.hzw.sunflower.controller;

import com.hzw.sunflower.common.BaseController;
import com.hzw.sunflower.util.BeanListUtil;
import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.constant.MessageConstants;
import com.hzw.sunflower.controller.response.GroupSectionInfoVO;
import com.hzw.sunflower.controller.response.SectionGroupVO;
import com.hzw.sunflower.dto.GroupSectionInfoDTO;
import com.hzw.sunflower.dto.SectionGroupDTO;
import com.hzw.sunflower.service.SectionGroupService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2021/8/30 10:05
 * @description：按套销售服务
 * @version: 1.0
 */
@Api(tags = "按套销售服务")
@RestController
@RequestMapping("/sectionGroup")
public class SectionGroupController extends BaseController {

    @Autowired
    private SectionGroupService sectionGroupService;

    @ApiOperation(value = "查询项目所有按套销售信息")
    @ApiImplicitParam(name = "projectId", value = "项目ID", required = true)
    @GetMapping(value = "/queryInfo/{projectId}")
    public Result<List<SectionGroupVO>> queryDocInfo(@PathVariable("projectId") Long projectId) {
        if (projectId == null) {
            return Result.failed(MessageConstants.GET_NULL);
        }
        //按套销售信息
        List<SectionGroupDTO> sectionGroupDTO = sectionGroupService.getByProjectId(projectId);
        List<SectionGroupVO> sectionGroupVO = BeanListUtil.convertList(sectionGroupDTO, SectionGroupVO.class);
        return Result.ok(sectionGroupVO);
    }

    @ApiOperation(value = "查询按套信息")
    @ApiImplicitParam(name = "groupId", value = "按套ID", required = true)
    @GetMapping(value = "/querySection/{groupId}")
    public Result<GroupSectionInfoVO> querySection(@PathVariable("groupId") Long groupId) {
        if (groupId == null) {
            return Result.failed(MessageConstants.GET_NULL);
        }
        //按套销售信息
        GroupSectionInfoDTO dto = sectionGroupService.querySection(groupId);
        GroupSectionInfoVO vo = BeanListUtil.convert(dto, GroupSectionInfoVO.class);
        return Result.ok(vo);
    }
}
