package com.hzw.sunflower.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class ProjectShareInfoDto {

    @ApiModelProperty(value = "项目编号/标包号")
    private String purchaseNumber;

    @ApiModelProperty(value = "ncc项目部门id")
    private String nccProjectDepartId;

    private BigDecimal amount;

    @ApiModelProperty(value = "项目id")
    private Long projectId;

    @ApiModelProperty(value = "标段id")
    private Long sectionId;
}
