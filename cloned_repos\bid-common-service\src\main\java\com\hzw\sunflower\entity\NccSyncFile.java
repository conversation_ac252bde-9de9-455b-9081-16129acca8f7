package com.hzw.sunflower.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.hzw.sunflower.common.BaseBean;
import io.swagger.annotations.ApiModel;
import lombok.Data;

/**
 * ncc 推送文件表
 */
@ApiModel("t_ncc_sync_file")
@TableName("t_ncc_sync_file")
@Data
public class NccSyncFile extends BaseBean {
    private Long id;

    /**
     * 业务类型 1支付 2报销
     *
     * @mbg.generated
     */
    private Integer businessType;

    /**
     * 文件类型 1回单  0其他  11身份证 12 支付凭证
     *
     * @mbg.generated
     */
    private Integer fileType;

    /**
     * ncc 单据key
     *
     * @mbg.generated
     */
    private String pkBill;

    /**
     * 文件名称
     *
     * @mbg.generated
     */
    private String fileName;

    /**
     * 文件key
     *
     * @mbg.generated
     */
    private String fileKey;

    /**
     * 1 天翼云
     */
    private String ossType;

    /**
     * oss文件id
     */
    private Long ossId;


    /**
     * 推送状态 1代推送 2推送成功 3 推送失败
     */
    private Integer pushStatus;


    /**
     * 推送次数
     */
    private Integer pushCount;

    private static final long serialVersionUID = 1L;
}