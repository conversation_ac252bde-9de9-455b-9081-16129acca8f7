package com.jszbtb.mobile.plugins.network.flutter_network_plugin;

import android.content.Context;

import com.alibaba.fastjson.JSON;

import org.jetbrains.annotations.NotNull;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.security.NoSuchAlgorithmException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.FormBody;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import okhttp3.internal.http.HttpMethod;

import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.util.Log;

import com.facebook.network.connectionclass.ConnectionClassManager;
import com.facebook.network.connectionclass.ConnectionQuality;
import com.facebook.network.connectionclass.DeviceBandwidthSampler;

/**
 * <AUTHOR>
 * http client base on okhttp
 */
public class HttpClient implements ConnectionClassManager.ConnectionClassStateChangeListener {

    private static final int TIMEOUT = 15;

    /**
     * 连接超时
     */
    private static final int CONNECTION_TIMEOUT = 2;

    /**
     * 网络失败最大重试次数
     */
    private static final int MAX_RETRY_COUNT = 4;

    /**
     * 采样时间
     */
    private static final long SAMPLER_TIME = 5000;


    /**
     * 缓存管理器
     */
    private CacheManager cacheManager;

    /**
     * 缓存操作Doa层
     */
    private CacheDao cacheDao;

    /**
     * 网络连接状态
     */
    private ConnectivityManager connectivityManager;

    /**
     * HTTP单例
     */
    private static HttpClient mHttpClient;

    /**
     * 重试
     */
    private HashMap<String,Integer> retryDictionary;

    /**
     * Android Context
     */
    private Context context;

    /**
     * 网络质量
     */
    private ConnectionQuality connectionQuality;

    /**
     * 单例入口
     * @param context
     * @return
     */
    public static HttpClient getInstance(final Context context) {
        if(mHttpClient == null) {
            mHttpClient = new HttpClient(context);
        }
        return mHttpClient;
    }

    /**
     * 构造方法
     * @param context android上下文对象
     */
    private HttpClient(final Context context) {
        // 会话对象
        this.context = context;
        this.retryDictionary = new HashMap<>();
        this.connectivityManager = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
        // 异步线程初始化数据库
        Thread thread = new Thread(new Runnable() {
            @Override
            public void run() {
                cacheManager = CacheManager.getInstance(context);
                cacheDao = cacheManager.Dao();
            }
        });

        thread.start();

        // 后台线程采样网速
        this.startNetworkSpeedSampler();
        Thread networkSamplerThread = new Thread(new Runnable() {
            @Override
            public void run() {
                HttpClient.this.samplerNetwork();
            }
        });

        networkSamplerThread.start();
    }

    /**
     * 开始网路请求采样比
     */
    private void startNetworkSpeedSampler() {
        ConnectionClassManager.getInstance().register(this);
        DeviceBandwidthSampler.getInstance().startSampling();
    }

    /**
     * 网络速度采样
     */
    private void samplerNetwork() {
        if(this.isNetworkConnect()) {
            OkHttpClient okHttpClient = new OkHttpClient.Builder()
                    .retryOnConnectionFailure(true)
                    .connectTimeout(CONNECTION_TIMEOUT, TimeUnit.SECONDS) //连接超时
                    .readTimeout(TIMEOUT, TimeUnit.SECONDS) //读取超时
                    .writeTimeout(TIMEOUT, TimeUnit.SECONDS) //写超时
                    .build();
            Request request = new Request.Builder()
                    .url("http://www.baidu.com")
                    .build();
            Call call = okHttpClient.newCall(request);
            call.enqueue(new Callback() {
                @Override
                public void onFailure(@NotNull Call call, @NotNull IOException e) {
                    try {
                        Thread.sleep(SAMPLER_TIME);
                        HttpClient.this.samplerNetwork();
                    } catch (InterruptedException ex) {
                        HttpClient.this.samplerNetwork();
                    }
                }

                @Override
                public void onResponse(@NotNull Call call, @NotNull Response response) throws IOException {
                    try {
                        Thread.sleep(SAMPLER_TIME);
                        ConnectionQuality quality =
                                ConnectionClassManager.getInstance().getCurrentBandwidthQuality();
                        Log.d("Network quality", "onResponse: "+quality.toString());
                        HttpClient.this.samplerNetwork();
                    } catch (InterruptedException e) {
                        HttpClient.this.samplerNetwork();
                    }

                }
            });
        } else {
            try {
                Thread.sleep(SAMPLER_TIME);
                this.samplerNetwork();
            } catch (InterruptedException e) {
                this.samplerNetwork();
            }
        }
    }

    /**
     * 接口回调
     */
    public interface CallBack {
        void onResponseString(final String responseString);
        void onFailure(final String message);
    }

    public enum HttpMethod {
        GET,
        POST_JSON,
        POST_FORM,
        PUT_JSON,
        PUT_FORM,
        DELETE,
    }

    /**
     * 判断网络请求是否可达
     * @return 判断网路请求是否可达
     */
    private boolean isNetworkConnect() {
        NetworkInfo networkInfo = this.connectivityManager.getActiveNetworkInfo();
        boolean isConnected = false;
        if(networkInfo != null) {
            isConnected = networkInfo.isConnected();
        }
        return isConnected;
    }


    /**
     * 发出Get请求
     * @param context context
     * @param url http url
     * @param params 参数
     * @param cachable 是否使用缓存
     * @param isExternalDomainName 是否使用外部设置的域名
     * @param externalDomainName 外部域名
     * @param callBack 回调
     */
    public void Get(Context context,
                    String url,
                    HashMap<String, String> params,
                    boolean cachable,
                    boolean isExternalDomainName,
                    String externalDomainName,
                    final HttpClient.CallBack callBack){

        String fullApiUrl = "";

        // 外部域名存在使用外部域名否则走配置文件
        if (!isExternalDomainName) {
            fullApiUrl = ConfigUtil.getConfig(context,"jszbtb.apiHost").concat(url);
        } else {
            fullApiUrl = externalDomainName.concat(url);
        }
        String cacheName = UrlUtils.getUrlHashCode(fullApiUrl,params);

        if (params != null) {
            if (params.size() >0) {
                StringBuilder stringBuilder = new StringBuilder();
                for (Map.Entry<String, String> param: params.entrySet()) {
                    stringBuilder.append(param.getKey() + "=" + param.getValue() + "&");
                }

                fullApiUrl = fullApiUrl.concat("?").concat(stringBuilder.toString().substring(0,stringBuilder.length() -1));
            }
        }

        sendRequest(fullApiUrl,
                cachable,
                false,
                "",
                cacheName,
                "",
                null,
                HttpMethod.GET,
                callBack);

    }

    /**
     * 发送 请求
     * @param fullApiUrl url
     * @param cachable 是否使用缓存
     * @param isRetry 是否是重试的请求
     * @param preRequestHashCode 重试请求的唯一标示吗
     * @param cacheName 缓存名称
     * @param httpMethod http请求的方法
     * @param callBack 回调
     */
    private void sendRequest(final String fullApiUrl,
                             final boolean cachable,
                             boolean isRetry,
                             String preRequestHashCode,
                             final String cacheName,
                             final String json,
                             final HashMap<String, String> params,
                             final HttpMethod httpMethod,
                             final CallBack callBack) {
        // 不是重试请求即第一次请求
        String requestHashCode = "";
        if(!isRetry) {
            try {
                String ms = String.valueOf(System.currentTimeMillis());
                String hashCode = UrlUtils.computeSHA1(ms);
                requestHashCode = hashCode;
            } catch (NoSuchAlgorithmException e) {
                requestHashCode = "";
            } catch (UnsupportedEncodingException ex) {
                requestHashCode = "";
            }
        } else {
            requestHashCode = preRequestHashCode;
        }

        final String requestHashCodeFinal = requestHashCode;
        if (cachable) {
            // 异步线程获取是否有cache
            Thread thread = new Thread(new Runnable() {

                @Override
                public void run() {
                    List<CacheEntity> cacheEntities = HttpClient.this.cacheDao.getCacheByName(cacheName);
                    if(cacheEntities.size() > 0) {
                        final CacheEntity cacheEntity = cacheEntities.get(0);
                        callBack.onResponseString(cacheEntity.cacheValue);

                        if(httpMethod == HttpMethod.GET) {
                            HttpClient.this.sentGetNetworkRequest(fullApiUrl, new CallBack() {
                                @Override
                                public void onResponseString(String responseString) {
                                    cacheEntity.cacheName = cacheName;
                                    cacheEntity.cacheValue = responseString;
                                    HttpClient.this.cacheDao.update(cacheEntity);
                                }
                                @Override
                                public void onFailure(String message) {
                                    HttpClient.this.retryRequest(requestHashCodeFinal,
                                            fullApiUrl,
                                            cachable,
                                            cacheName,
                                            json,
                                            params,
                                            httpMethod,
                                            callBack);
                                }
                            });
                        } else if(httpMethod == HttpMethod.POST_JSON) {
                            HttpClient.this.sentPostJsonNetworkRequest(fullApiUrl, json, new CallBack() {
                                @Override
                                public void onResponseString(String responseString) {
                                    cacheEntity.cacheName = cacheName;
                                    cacheEntity.cacheValue = responseString;
                                    HttpClient.this.cacheDao.update(cacheEntity);
                                }

                                @Override
                                public void onFailure(String message) {
                                    HttpClient.this.retryRequest(requestHashCodeFinal,
                                            fullApiUrl,
                                            cachable,
                                            cacheName,
                                            json,
                                            params,
                                            httpMethod,
                                            callBack);
                                }
                            });
                        } else if(httpMethod == HttpMethod.POST_FORM) {
                            HttpClient.this.sentPostFormNetworkRequest(fullApiUrl, params, new CallBack() {
                                @Override
                                public void onResponseString(String responseString) {
                                    cacheEntity.cacheName = cacheName;
                                    cacheEntity.cacheValue = responseString;
                                    HttpClient.this.cacheDao.update(cacheEntity);
                                }

                                @Override
                                public void onFailure(String message) {
                                    HttpClient.this.retryRequest(requestHashCodeFinal,
                                            fullApiUrl,
                                            cachable,
                                            cacheName,
                                            json,
                                            params,
                                            httpMethod,
                                            callBack);
                                }
                            });
                        } else if(httpMethod == HttpMethod.PUT_FORM) {
                            HttpClient.this.sentPutFormNetworkRequest(fullApiUrl, params, new CallBack() {
                                @Override
                                public void onResponseString(String responseString) {
                                    cacheEntity.cacheName = cacheName;
                                    cacheEntity.cacheValue = responseString;
                                    HttpClient.this.cacheDao.update(cacheEntity);
                                }

                                @Override
                                public void onFailure(String message) {
                                    HttpClient.this.retryRequest(requestHashCodeFinal,
                                            fullApiUrl,
                                            cachable,
                                            cacheName,
                                            json,
                                            params,
                                            httpMethod,
                                            callBack);
                                }
                            });
                        } else if(httpMethod == HttpMethod.PUT_JSON) {
                            HttpClient.this.sentPutJsonNetworkRequest(fullApiUrl, json, new CallBack() {
                                @Override
                                public void onResponseString(String responseString) {
                                    cacheEntity.cacheName = cacheName;
                                    cacheEntity.cacheValue = responseString;
                                    HttpClient.this.cacheDao.update(cacheEntity);
                                }

                                @Override
                                public void onFailure(String message) {
                                    HttpClient.this.retryRequest(requestHashCodeFinal,
                                            fullApiUrl,
                                            cachable,
                                            cacheName,
                                            json,
                                            params,
                                            httpMethod,
                                            callBack);
                                }
                            });
                        } else if(httpMethod == HttpMethod.DELETE) {
                            HttpClient.this.sentDeleteNetworkRequest(fullApiUrl, new CallBack() {
                                @Override
                                public void onResponseString(String responseString) {
                                    cacheEntity.cacheName = cacheName;
                                    cacheEntity.cacheValue = responseString;
                                    HttpClient.this.cacheDao.update(cacheEntity);
                                }

                                @Override
                                public void onFailure(String message) {
                                    HttpClient.this.retryRequest(requestHashCodeFinal,
                                            fullApiUrl,
                                            cachable,
                                            cacheName,
                                            json,
                                            params,
                                            httpMethod,
                                            callBack);
                                }
                            });
                        }

                    } else {
                        if(httpMethod == HttpMethod.GET) {
                            HttpClient.this.sentGetNetworkRequest(fullApiUrl, new CallBack() {
                                @Override
                                public void onResponseString(String responseString) {
                                    callBack.onResponseString(responseString);
                                    CacheEntity cacheEntity = new CacheEntity();
                                    cacheEntity.cacheName = cacheName;
                                    cacheEntity.cacheValue = responseString;
                                    HttpClient.this.cacheDao.insert(cacheEntity);
                                    if (HttpClient.this.retryDictionary.containsKey(requestHashCodeFinal)) {
                                        HttpClient.this.retryDictionary.remove(requestHashCodeFinal);
                                    }
                                }

                                @Override
                                public void onFailure(String message) {
                                    HttpClient.this.retryRequest(requestHashCodeFinal,
                                            fullApiUrl,
                                            cachable,
                                            cacheName,
                                            json,
                                            params,
                                            httpMethod,
                                            callBack);
                                }
                            });
                        } else if(httpMethod == HttpMethod.POST_JSON) {
                            HttpClient.this.sentPostJsonNetworkRequest(fullApiUrl, json, new CallBack() {
                                @Override
                                public void onResponseString(String responseString) {
                                    callBack.onResponseString(responseString);
                                    CacheEntity cacheEntity = new CacheEntity();
                                    cacheEntity.cacheName = cacheName;
                                    cacheEntity.cacheValue = responseString;
                                    HttpClient.this.cacheDao.insert(cacheEntity);
                                    if (HttpClient.this.retryDictionary.containsKey(requestHashCodeFinal)) {
                                        HttpClient.this.retryDictionary.remove(requestHashCodeFinal);
                                    }
                                }

                                @Override
                                public void onFailure(String message) {
                                    HttpClient.this.retryRequest(requestHashCodeFinal,
                                            fullApiUrl,
                                            cachable,
                                            cacheName,
                                            json,
                                            params,
                                            httpMethod,
                                            callBack);
                                }
                            });
                        } else if(httpMethod == HttpMethod.POST_FORM) {
                            HttpClient.this.sentPostFormNetworkRequest(fullApiUrl, params, new CallBack() {
                                @Override
                                public void onResponseString(String responseString) {
                                    callBack.onResponseString(responseString);
                                    CacheEntity cacheEntity = new CacheEntity();
                                    cacheEntity.cacheName = cacheName;
                                    cacheEntity.cacheValue = responseString;
                                    HttpClient.this.cacheDao.insert(cacheEntity);
                                    if (HttpClient.this.retryDictionary.containsKey(requestHashCodeFinal)) {
                                        HttpClient.this.retryDictionary.remove(requestHashCodeFinal);
                                    }
                                }

                                @Override
                                public void onFailure(String message) {
                                    HttpClient.this.retryRequest(requestHashCodeFinal,
                                            fullApiUrl,
                                            cachable,
                                            cacheName,
                                            json,
                                            params,
                                            httpMethod,
                                            callBack);
                                }
                            });
                        } else if(httpMethod == HttpMethod.PUT_FORM) {
                            HttpClient.this.sentPutFormNetworkRequest(fullApiUrl, params, new CallBack() {
                                @Override
                                public void onResponseString(String responseString) {
                                    callBack.onResponseString(responseString);
                                    CacheEntity cacheEntity = new CacheEntity();
                                    cacheEntity.cacheName = cacheName;
                                    cacheEntity.cacheValue = responseString;
                                    HttpClient.this.cacheDao.insert(cacheEntity);
                                    if (HttpClient.this.retryDictionary.containsKey(requestHashCodeFinal)) {
                                        HttpClient.this.retryDictionary.remove(requestHashCodeFinal);
                                    }
                                }

                                @Override
                                public void onFailure(String message) {
                                    HttpClient.this.retryRequest(requestHashCodeFinal,
                                            fullApiUrl,
                                            cachable,
                                            cacheName,
                                            json,
                                            params,
                                            httpMethod,
                                            callBack);
                                }
                            });
                        } else if(httpMethod == HttpMethod.PUT_JSON) {
                            HttpClient.this.sentPutJsonNetworkRequest(fullApiUrl, json, new CallBack() {
                                @Override
                                public void onResponseString(String responseString) {
                                    callBack.onResponseString(responseString);
                                    CacheEntity cacheEntity = new CacheEntity();
                                    cacheEntity.cacheName = cacheName;
                                    cacheEntity.cacheValue = responseString;
                                    HttpClient.this.cacheDao.insert(cacheEntity);
                                    if (HttpClient.this.retryDictionary.containsKey(requestHashCodeFinal)) {
                                        HttpClient.this.retryDictionary.remove(requestHashCodeFinal);
                                    }
                                }

                                @Override
                                public void onFailure(String message) {
                                    HttpClient.this.retryRequest(requestHashCodeFinal,
                                            fullApiUrl,
                                            cachable,
                                            cacheName,
                                            json,
                                            params,
                                            httpMethod,
                                            callBack);
                                }
                            });
                        } else if(httpMethod == HttpMethod.DELETE) {
                            HttpClient.this.sentDeleteNetworkRequest(fullApiUrl, new CallBack() {
                                @Override
                                public void onResponseString(String responseString) {
                                    callBack.onResponseString(responseString);
                                    CacheEntity cacheEntity = new CacheEntity();
                                    cacheEntity.cacheName = cacheName;
                                    cacheEntity.cacheValue = responseString;
                                    HttpClient.this.cacheDao.insert(cacheEntity);
                                    if (HttpClient.this.retryDictionary.containsKey(requestHashCodeFinal)) {
                                        HttpClient.this.retryDictionary.remove(requestHashCodeFinal);
                                    }
                                }

                                @Override
                                public void onFailure(String message) {
                                    HttpClient.this.retryRequest(requestHashCodeFinal,
                                            fullApiUrl,
                                            cachable,
                                            cacheName,
                                            json,
                                            params,
                                            httpMethod,
                                            callBack);
                                }
                            });
                        }
                    }
                }
            });

            thread.start();

        } else {
            if(this.isNetworkConnect()) {
                if(this.connectionQuality != ConnectionQuality.POOR) {
                    final Thread thread = new Thread(new Runnable() {
                        @Override
                        public void run() {
                            if(httpMethod == HttpMethod.GET) {
                                HttpClient.this.sentGetNetworkRequest(fullApiUrl, new CallBack() {
                                    @Override
                                    public void onResponseString(String responseString) {
                                        callBack.onResponseString(responseString);
                                        CacheEntity cacheEntity = new CacheEntity();
                                        cacheEntity.cacheName = cacheName;
                                        cacheEntity.cacheValue = responseString;
                                        HttpClient.this.cacheDao.insert(cacheEntity);
                                        if (HttpClient.this.retryDictionary.containsKey(requestHashCodeFinal)) {
                                            HttpClient.this.retryDictionary.remove(requestHashCodeFinal);
                                        }
                                    }

                                    @Override
                                    public void onFailure(String message) {
                                        HttpClient.this.retryRequest(requestHashCodeFinal,
                                                fullApiUrl,
                                                cachable,
                                                cacheName,
                                                json,
                                                params,
                                                httpMethod,
                                                callBack);
                                    }
                                });
                            } else if(httpMethod == HttpMethod.POST_JSON) {
                                HttpClient.this.sentPostJsonNetworkRequest(fullApiUrl, json, new CallBack() {
                                    @Override
                                    public void onResponseString(String responseString) {
                                        callBack.onResponseString(responseString);
                                        CacheEntity cacheEntity = new CacheEntity();
                                        cacheEntity.cacheName = cacheName;
                                        cacheEntity.cacheValue = responseString;
                                        HttpClient.this.cacheDao.insert(cacheEntity);
                                        if (HttpClient.this.retryDictionary.containsKey(requestHashCodeFinal)) {
                                            HttpClient.this.retryDictionary.remove(requestHashCodeFinal);
                                        }
                                    }

                                    @Override
                                    public void onFailure(String message) {
                                        HttpClient.this.retryRequest(requestHashCodeFinal,
                                                fullApiUrl,
                                                cachable,
                                                cacheName,
                                                json,
                                                params,
                                                httpMethod,
                                                callBack);
                                    }
                                });
                            } else if (httpMethod == HttpMethod.POST_FORM) {
                                HttpClient.this.sentPostFormNetworkRequest(fullApiUrl, params, new CallBack() {
                                    @Override
                                    public void onResponseString(String responseString) {
                                        callBack.onResponseString(responseString);
                                        CacheEntity cacheEntity = new CacheEntity();
                                        cacheEntity.cacheName = cacheName;
                                        cacheEntity.cacheValue = responseString;
                                        HttpClient.this.cacheDao.insert(cacheEntity);
                                        if (HttpClient.this.retryDictionary.containsKey(requestHashCodeFinal)) {
                                            HttpClient.this.retryDictionary.remove(requestHashCodeFinal);
                                        }
                                    }

                                    @Override
                                    public void onFailure(String message) {
                                        HttpClient.this.retryRequest(requestHashCodeFinal,
                                                fullApiUrl,
                                                cachable,
                                                cacheName,
                                                json,
                                                params,
                                                httpMethod,
                                                callBack);
                                    }
                                });
                            }
                            else if(httpMethod == HttpMethod.PUT_FORM) {
                                HttpClient.this.sentPutFormNetworkRequest(fullApiUrl, params, new CallBack() {
                                    @Override
                                    public void onResponseString(String responseString) {
                                        callBack.onResponseString(responseString);
                                        CacheEntity cacheEntity = new CacheEntity();
                                        cacheEntity.cacheName = cacheName;
                                        cacheEntity.cacheValue = responseString;
                                        HttpClient.this.cacheDao.insert(cacheEntity);
                                        if (HttpClient.this.retryDictionary.containsKey(requestHashCodeFinal)) {
                                            HttpClient.this.retryDictionary.remove(requestHashCodeFinal);
                                        }
                                    }

                                    @Override
                                    public void onFailure(String message) {
                                        HttpClient.this.retryRequest(requestHashCodeFinal,
                                                fullApiUrl,
                                                cachable,
                                                cacheName,
                                                json,
                                                params,
                                                httpMethod,
                                                callBack);
                                    }
                                });
                            } else if(httpMethod == HttpMethod.PUT_JSON) {
                                HttpClient.this.sentPutJsonNetworkRequest(fullApiUrl, json, new CallBack() {
                                    @Override
                                    public void onResponseString(String responseString) {
                                        callBack.onResponseString(responseString);
                                        CacheEntity cacheEntity = new CacheEntity();
                                        cacheEntity.cacheName = cacheName;
                                        cacheEntity.cacheValue = responseString;
                                        HttpClient.this.cacheDao.insert(cacheEntity);
                                        if (HttpClient.this.retryDictionary.containsKey(requestHashCodeFinal)) {
                                            HttpClient.this.retryDictionary.remove(requestHashCodeFinal);
                                        }
                                    }

                                    @Override
                                    public void onFailure(String message) {
                                        HttpClient.this.retryRequest(requestHashCodeFinal,
                                                fullApiUrl,
                                                cachable,
                                                cacheName,
                                                json,
                                                params,
                                                httpMethod,
                                                callBack);
                                    }
                                });
                            } else if(httpMethod == HttpMethod.DELETE) {
                                HttpClient.this.sentDeleteNetworkRequest(fullApiUrl, new CallBack() {
                                    @Override
                                    public void onResponseString(String responseString) {
                                        callBack.onResponseString(responseString);
                                        CacheEntity cacheEntity = new CacheEntity();
                                        cacheEntity.cacheName = cacheName;
                                        cacheEntity.cacheValue = responseString;
                                        HttpClient.this.cacheDao.insert(cacheEntity);
                                        if (HttpClient.this.retryDictionary.containsKey(requestHashCodeFinal)) {
                                            HttpClient.this.retryDictionary.remove(requestHashCodeFinal);
                                        }
                                    }

                                    @Override
                                    public void onFailure(String message) {
                                        HttpClient.this.retryRequest(requestHashCodeFinal,
                                                fullApiUrl,
                                                cachable,
                                                cacheName,
                                                json,
                                                params,
                                                httpMethod,
                                                callBack);
                                    }
                                });
                            }
                        }
                    });
                    thread.start();
                } else {
                    // 网络太差，缓存优先
                    this.sendRequest(fullApiUrl,
                            true,
                            false,
                            "",
                            cacheName,
                            json,
                            params,
                            httpMethod,
                            callBack);
                }

            } else {
                // 网络不可达，重试
                int retryCount = 0;
                if (!HttpClient.this.retryDictionary.containsKey(requestHashCodeFinal)) {
                    retryCount = 0;
                } else {
                    retryCount = HttpClient.this.retryDictionary.get(requestHashCodeFinal);
                }
                retryCount = retryCount + 1;
                if (retryCount <= MAX_RETRY_COUNT) {
                    HttpClient.this.retryDictionary.put(requestHashCodeFinal,retryCount);
                    HttpClient.this.sendRequest(fullApiUrl,
                            cachable,
                            true,
                            requestHashCodeFinal,
                            cacheName,
                            json,
                            params,
                            httpMethod,
                            callBack);
                } else {
                    ResponseEntity responseEntity = new ResponseEntity();
                    responseEntity.setSuccess(false);
                    responseEntity.setCode(502);
                    responseEntity.setErrorMessage("Network and cache not available");
                    responseEntity.setData(null);
                    String responseEntityJson = JSON.toJSONString(responseEntity);
                    callBack.onFailure(responseEntityJson);
                }
            }
        }

    }

    /**
     * 重试请求
     * @param requestHashCodeFinal 上次请求的唯一标示
     * @param fullApiUrl 完整的URL
     * @param cachable 是否强制使用缓存
     * @param cacheName 缓存的名字
     * @param httpMethod http方法
     * @param callBack 回调
     */
    private void retryRequest(String requestHashCodeFinal,
                              final String fullApiUrl,
                              final boolean cachable,
                              final String cacheName,
                              final String json,
                              final HashMap<String, String> params,
                              final HttpMethod httpMethod,
                              final CallBack callBack) {
        int retryCount = 0;
        if(!HttpClient.this.retryDictionary.containsKey(requestHashCodeFinal)) {
            retryCount = 0;
        } else {
            retryCount = HttpClient.this.retryDictionary.get(requestHashCodeFinal);
        }

        retryCount = retryCount +1;
        if(retryCount <= MAX_RETRY_COUNT) {
            HttpClient.this.retryDictionary.put(requestHashCodeFinal,retryCount);
            try {
                Thread.sleep(1000);
                HttpClient.this.sendRequest(fullApiUrl,
                        cachable,
                        true,
                        requestHashCodeFinal,
                        cacheName,
                        json,
                        params,
                        httpMethod,
                        callBack);

            } catch (InterruptedException e) {
                ResponseEntity responseEntity = new ResponseEntity();
                responseEntity.setSuccess(false);
                responseEntity.setCode(502);
                responseEntity.setErrorMessage("Network and cache not available");
                responseEntity.setData(null);
                String responseEntityJson = JSON.toJSONString(responseEntity);
            }
        } else {
            ResponseEntity responseEntity = new ResponseEntity();
            responseEntity.setSuccess(false);
            responseEntity.setCode(502);
            responseEntity.setErrorMessage("Network and cache not available");
            responseEntity.setData(null);
            String responseEntityJson = JSON.toJSONString(responseEntity);
            callBack.onFailure(responseEntityJson);
        }
    }

    /**
     * 调用OKhttp 请求GET 方法的URL
     * @param fullApiUrl url
     * @param callBack 回调
     */
    private void sentGetNetworkRequest(String fullApiUrl,final CallBack callBack) {
        OkHttpClient okHttpClient = new OkHttpClient.Builder()
                .retryOnConnectionFailure(true)
                .connectTimeout(CONNECTION_TIMEOUT, TimeUnit.SECONDS) //连接超时
                .readTimeout(TIMEOUT, TimeUnit.SECONDS) //读取超时
                .writeTimeout(TIMEOUT, TimeUnit.SECONDS) //写超时
                .build();
        Request request = new Request.Builder()
                .url(fullApiUrl)
                .build();
        Call call = okHttpClient.newCall(request);
        call.enqueue(new Callback() {
            @Override
            public void onFailure(@NotNull Call call, @NotNull IOException e) {
                callBack.onFailure(e.toString());
            }

            @Override
            public void onResponse(@NotNull Call call, @NotNull Response response) throws IOException {
                callBack.onResponseString(response.body().string());
            }
        });
    }

    /**
     * 发送Post JSON的请求
     * @param fullApiUrl
     * @param json
     * @param callBack
     */
    private void sentPostJsonNetworkRequest(String fullApiUrl,String json,final CallBack callBack) {
        OkHttpClient okHttpClient = new OkHttpClient.Builder()
                .retryOnConnectionFailure(true)
                .connectTimeout(CONNECTION_TIMEOUT, TimeUnit.SECONDS) //连接超时
                .readTimeout(TIMEOUT, TimeUnit.SECONDS) //读取超时
                .writeTimeout(TIMEOUT, TimeUnit.SECONDS) //写超时
                .build();

        RequestBody body = RequestBody.create(json, MediaType.parse("application/json; charset=utf-8"));
        Request request = new Request.Builder()
                .url(fullApiUrl)
                .post(body)
                .build();

        Call call = okHttpClient.newCall(request);
        call.enqueue(new Callback() {
            @Override
            public void onFailure(@NotNull Call call, @NotNull IOException e) {
                callBack.onFailure(e.toString());
            }

            @Override
            public void onResponse(@NotNull Call call, @NotNull Response response) throws IOException {
                callBack.onResponseString(response.body().string());
            }
        });
    }

    /**
     * 发送Post Form的请求
     * @param fullApiUrl
     * @param params
     * @param callBack
     */
    private void sentPostFormNetworkRequest(String fullApiUrl,HashMap<String, String> params,final CallBack callBack) {
        OkHttpClient okHttpClient = new OkHttpClient.Builder()
                .retryOnConnectionFailure(true)
                .connectTimeout(CONNECTION_TIMEOUT, TimeUnit.SECONDS) //连接超时
                .readTimeout(TIMEOUT, TimeUnit.SECONDS) //读取超时
                .writeTimeout(TIMEOUT, TimeUnit.SECONDS) //写超时
                .build();
        FormBody.Builder formBodyBuilder = new FormBody.Builder();
        if (params != null) {
            for (Map.Entry<String, String> param: params.entrySet()) {
                formBodyBuilder.add(param.getKey(),param.getValue());
            }
        }

        FormBody formBody = formBodyBuilder.build();
        final Request request = new Request.Builder()
                .url(fullApiUrl)
                .post(formBody)
                .build();
        Call call = okHttpClient.newCall(request);
        call.enqueue(new Callback() {
            @Override
            public void onFailure(@NotNull Call call, @NotNull IOException e) {
                callBack.onFailure(e.toString());
            }

            @Override
            public void onResponse(@NotNull Call call, @NotNull Response response) throws IOException {
                callBack.onResponseString(response.body().string());
            }
        });
    }


    /**
     * 发送Put Form的请求
     * @param fullApiUrl 完整请求连接
     * @param params 参数
     * @param callBack 回到
     */
    private void sentPutFormNetworkRequest(String fullApiUrl,HashMap<String, String> params,final CallBack callBack) {
        OkHttpClient okHttpClient = new OkHttpClient.Builder()
                .retryOnConnectionFailure(true)
                .connectTimeout(CONNECTION_TIMEOUT, TimeUnit.SECONDS) //连接超时
                .readTimeout(TIMEOUT, TimeUnit.SECONDS) //读取超时
                .writeTimeout(TIMEOUT, TimeUnit.SECONDS) //写超时
                .build();
        FormBody.Builder formBodyBuilder = new FormBody.Builder();
        if (params != null) {
            for (Map.Entry<String, String> param: params.entrySet()) {
                formBodyBuilder.add(param.getKey(),param.getValue());
            }
        }

        FormBody formBody = formBodyBuilder.build();
        final Request request = new Request.Builder()
                .url(fullApiUrl)
                .put(formBody)
                .build();
        Call call = okHttpClient.newCall(request);
        call.enqueue(new Callback() {
            @Override
            public void onFailure(@NotNull Call call, @NotNull IOException e) {
                callBack.onFailure(e.toString());
            }

            @Override
            public void onResponse(@NotNull Call call, @NotNull Response response) throws IOException {
                callBack.onResponseString(response.body().string());
            }
        });
    }

    /**
     * 发送Put Json的请求
     * @param fullApiUrl 完整URL
     * @param json json地址
     * @param callBack 回调
     */
    private void sentPutJsonNetworkRequest(String fullApiUrl,String json,final CallBack callBack) {
        OkHttpClient okHttpClient = new OkHttpClient.Builder()
                .retryOnConnectionFailure(true)
                .connectTimeout(CONNECTION_TIMEOUT, TimeUnit.SECONDS) //连接超时
                .readTimeout(TIMEOUT, TimeUnit.SECONDS) //读取超时
                .writeTimeout(TIMEOUT, TimeUnit.SECONDS) //写超时
                .build();
        RequestBody body = RequestBody.create(json, MediaType.parse("application/json; charset=utf-8"));
        Request request = new Request.Builder()
                .url(fullApiUrl)
                .put(body)
                .build();

        Call call = okHttpClient.newCall(request);
        call.enqueue(new Callback() {
            @Override
            public void onFailure(@NotNull Call call, @NotNull IOException e) {
                callBack.onFailure(e.toString());
            }

            @Override
            public void onResponse(@NotNull Call call, @NotNull Response response) throws IOException {
                callBack.onResponseString(response.body().string());
            }
        });
    }

    /**
     * 发送Delete请求
     * @param fullApiUrl 完整URL
     * @param callBack 回调
     */
    private void sentDeleteNetworkRequest(String fullApiUrl,final CallBack callBack) {
        OkHttpClient okHttpClient = new OkHttpClient.Builder()
                .retryOnConnectionFailure(true)
                .connectTimeout(CONNECTION_TIMEOUT, TimeUnit.SECONDS) //连接超时
                .readTimeout(TIMEOUT, TimeUnit.SECONDS) //读取超时
                .writeTimeout(TIMEOUT, TimeUnit.SECONDS) //写超时
                .build();
        Request request = new Request.Builder()
                .url(fullApiUrl)
                .build();
        Call call = okHttpClient.newCall(request);
        call.enqueue(new Callback() {
            @Override
            public void onFailure(@NotNull Call call, @NotNull IOException e) {
                callBack.onFailure(e.toString());
            }

            @Override
            public void onResponse(@NotNull Call call, @NotNull Response response) throws IOException {
                callBack.onResponseString(response.body().string());
            }
        });
    }

    public void PostJSON(Context context,
                         String url,
                         String json,
                         boolean cachable,
                         boolean isExternalDomainName,
                         String externalDomainName,
                         final HttpClient.CallBack callBack) {
        String fullApiUrl = "";

        // 外部域名存在使用外部域名否则走配置文件
        if (!isExternalDomainName) {
            fullApiUrl = ConfigUtil.getConfig(context,"jszbtb.apiHost").concat(url);
        } else {
            fullApiUrl = externalDomainName.concat(url);
        }
        String cacheName = UrlUtils.getUrlHashCode(fullApiUrl,json);

        this.sendRequest(fullApiUrl,
                cachable,
                false,
                "",
                cacheName,
                json,
                null,
                HttpMethod.POST_JSON,
                callBack);
    }


    /**
     * 提交表单请求
     * @param context android context
     * @param url api路径
     * @param params 参数
     * @param cachable 是否强制使用缓存
     * @param isExternalDomainName 是否使用外部域名
     * @param externalDomainName 外部域名
     * @param callBack 回调
     */
    public void PostForm(Context context,
                         String url,
                         HashMap<String, String> params,
                         boolean cachable,
                         boolean isExternalDomainName,
                         String externalDomainName,
                         final HttpClient.CallBack callBack) {
        String fullApiUrl = "";

        // 外部域名存在使用外部域名否则走配置文件
        if (!isExternalDomainName) {
            fullApiUrl = ConfigUtil.getConfig(context,"jszbtb.apiHost").concat(url);
        } else {
            fullApiUrl = externalDomainName.concat(url);
        }
        String cacheName = UrlUtils.getUrlHashCode(fullApiUrl,params);

        this.sendRequest(fullApiUrl,
                cachable,
                false,
                "",
                cacheName,
                "",
                params,
                HttpMethod.POST_FORM,
                callBack);

    }


    /**
     * PUT 表单
     * @param context
     * @param url
     * @param params
     * @param isExternalDomainName
     * @param externalDomainName
     * @param callBack
     */
    public void PutForm(Context context,
                        String url,
                        HashMap<String, String> params,
                        boolean isExternalDomainName,
                        String externalDomainName,
                        final HttpClient.CallBack callBack) {

        String fullApiUrl = "";

        // 外部域名存在使用外部域名否则走配置文件
        if (!isExternalDomainName) {
            fullApiUrl = ConfigUtil.getConfig(context,"jszbtb.apiHost").concat(url);
        } else {
            fullApiUrl = externalDomainName.concat(url);
        }

        this.sendRequest(fullApiUrl,
                false,
                false,
                "",
                "",
                "",
                params,
                HttpMethod.PUT_FORM,
                callBack);
    }

    /**
     * PUT JSON
     * @param context
     * @param url
     * @param json
     * @param isExternalDomainName
     * @param externalDomainName
     * @param callBack
     */
    public void PutJSON(Context context,
                        String url,
                        String json,
                        boolean isExternalDomainName,
                        String externalDomainName,
                        final HttpClient.CallBack callBack) {
        String fullApiUrl = "";

        // 外部域名存在使用外部域名否则走配置文件
        if (!isExternalDomainName) {
            fullApiUrl = ConfigUtil.getConfig(context,"jszbtb.apiHost").concat(url);
        } else {
            fullApiUrl = externalDomainName.concat(url);
        }

        this.sendRequest(fullApiUrl,
                false,
                false,
                "",
                "",
                json,
                null,
                HttpMethod.PUT_JSON,
                callBack);
    }


    public void Delete(Context context,
                    String url,
                    HashMap<String, String> params,
                    boolean isExternalDomainName,
                    String externalDomainName,
                    final HttpClient.CallBack callBack) {
        String fullApiUrl = "";

        // 外部域名存在使用外部域名否则走配置文件
        if (!isExternalDomainName) {
            fullApiUrl = ConfigUtil.getConfig(context,"jszbtb.apiHost").concat(url);
        } else {
            fullApiUrl = externalDomainName.concat(url);
        }


        if (params != null) {
            StringBuilder stringBuilder = new StringBuilder();
            for (Map.Entry<String, String> param: params.entrySet()) {
                stringBuilder.append(param.getKey() + "=" + param.getValue() + "&");
            }

            fullApiUrl = fullApiUrl.concat("?").concat(stringBuilder.toString().substring(0,stringBuilder.length() -1));
        }

    }

    /**
     * 网络质量变更通知
     * @param bandwidthState 新的网络质量
     */
    @Override
    public void onBandwidthStateChange(ConnectionQuality bandwidthState) {
        this.connectionQuality = bandwidthState;
    }
}
