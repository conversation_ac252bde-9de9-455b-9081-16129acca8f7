/**
 * 
 */
package com.hzw.ssm.sys.user.entity;

/**
 * <AUTHOR>
 * 首页显示类
 */
public class HomeQrEntity {

	/**
	 * 推荐总人数
	 */
	private Integer referralsCount=0;
	
	/**
	 * 推荐成功人数
	 */
	private Integer referralsSucCount=0;
	
	/**
	 * 	未提交审核
	 */
	private Integer referralsNotSubCount=0;
	
	/**
	 * 	待入库人数
	 */
	private Integer referralsStorageCount=0;
	
	/**
	 * 	不通过人数
	 */
	private Integer referralsNoCount=0;
	
	private Integer qeStatus;
	
	private Integer qeCount;
	
	private String  avgrefer;
	/**
	 * 推荐码路径
	 */
	private String referralsPath;
	public Integer getReferralsCount() {
		return referralsCount;
	}
	public void setReferralsCount(Integer referralsCount) {
		if(referralsCount!=null) {
		this.referralsCount = referralsCount;
		}else {
			this.referralsCount=0;
		}
	}
	public Integer getReferralsSucCount() {
		return referralsSucCount;
	}
	public void setReferralsSucCount(Integer referralsSucCount) {
		this.referralsSucCount = referralsSucCount;
	}
	public Integer getReferralsNotSubCount() {
		return referralsNotSubCount;
	}
	public void setReferralsNotSubCount(Integer referralsNotSubCount) {
		this.referralsNotSubCount = referralsNotSubCount;
	}
	public Integer getReferralsStorageCount() {
		return referralsStorageCount;
	}
	public void setReferralsStorageCount(Integer referralsStorageCount) {
		this.referralsStorageCount = referralsStorageCount;
	}
	public Integer getReferralsNoCount() {
		return referralsNoCount;
	}
	public void setReferralsNoCount(Integer referralsNoCount) {
		this.referralsNoCount = referralsNoCount;
	}
	public Integer getQeStatus() {
		return qeStatus;
	}
	public void setQeStatus(Integer qeStatus) {
		this.qeStatus = qeStatus;
	}
	public Integer getQeCount() {
		return qeCount;
	}
	public void setQeCount(Integer qeCount) {
		this.qeCount = qeCount;
	}
	public String getReferralsPath() {
		return referralsPath;
	}
	public void setReferralsPath(String referralsPath) {
		this.referralsPath = referralsPath;
	}


	public String getAvgrefer() {
		return avgrefer;
	}
	public void setAvgrefer(String avgrefer) {
		this.avgrefer = avgrefer;
	}
	
	/**
	 * 1.不通过 2.审核中3.未提交审核 4.通过的
	 * @param entity
	 */
	public void addCount(HomeQrEntity entity) {
		
		if(entity!=null) {
			if(null!=entity.getQeStatus()) {
			if(1==entity.getQeStatus()) {
				this.setReferralsNoCount(entity.getQeCount());
			}
			if(2==entity.getQeStatus()) {
				this.setReferralsStorageCount(entity.getQeCount());
			}
			if(3==entity.getQeStatus()) {
				
				this.setReferralsNotSubCount(entity.getQeCount());
			}
			if(4==entity.getQeStatus()) {
				this.setReferralsSucCount(entity.getQeCount());
			}
			}
		}
	}
}
