package com.hzw.sunflower.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hzw.sunflower.common.BaseBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 收取并保留剩余保证金记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-27
 */
@Data
@TableName("t_bond_retain_record")
@ApiModel(value = "BondWater对象", description = "收取并保留剩余保证金记录表")
public class BondRetainRecord extends BaseBean implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("退还id")
    private Long refundId;

    @ApiModelProperty("退还详情id")
    private Long refundDetailId;

    @ApiModelProperty(value = "保证金金额")
    private BigDecimal bondMoney;

    @ApiModelProperty(value = "代理服务费")
    private BigDecimal agencyFee;

    @ApiModelProperty(value = "保证金利息")
    private BigDecimal rates;

}
