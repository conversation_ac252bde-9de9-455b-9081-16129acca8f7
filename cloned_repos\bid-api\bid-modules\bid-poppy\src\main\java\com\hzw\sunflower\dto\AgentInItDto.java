package com.hzw.sunflower.dto;

import com.hzw.sunflower.controller.response.FileVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2021/4/16 14:46
 * @description：委托人信息初始化dto
 * @modified By：`
 * @version: 1.0
 */
@ApiModel(description = "委托人初始化dto ")
@Data
public class AgentInItDto {

    @ApiModelProperty(value = "委托单位id", position = 1)
    private Long companyId;

    @ApiModelProperty(value = "委托单位名称", position = 2)
    private String companyName;

    @ApiModelProperty(value = "委托合同id", position = 3)
    private String uploadFileId;

    @ApiModelProperty(value = "委托联系人集合", position = 4)
    private List<ContactDto> contactDto;

    @ApiModelProperty(value = "委托合同文件信息", position = 5)
    private List<FileVo> ossFile;

    @ApiModelProperty(value = "委托用户id", position = 6)
    private Long userId;

    @ApiModelProperty(value = "委托用户id", position = 6)
    private Long departmentId;

    @ApiModelProperty(value = "来源", position = 7)
    private Integer source;
}
