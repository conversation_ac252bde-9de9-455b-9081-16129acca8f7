package com.hzw.ssm.fw.listener.action;

        import org.apache.struts2.dispatcher.Dispatcher;
        import org.apache.struts2.dispatcher.ng.filter.StrutsPrepareAndExecuteFilter;

        import javax.servlet.*;

        import java.io.IOException;

public class MyStrutsFilter  extends StrutsPrepareAndExecuteFilter {
    public MyStrutsFilter() {
        super();
    }

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        super.init(filterConfig);
    }

    @Override
    protected void postInit(Dispatcher dispatcher, FilterConfig filterConfig) {
        super.postInit(dispatcher, filterConfig);
    }

    @Override
    public void doFilter(ServletRequest req, ServletResponse res, FilterChain chain) throws IOException, ServletException {

        if(ComDischarged.ueditor(req,res,chain)){
            chain.doFilter(req, res);
        }else {
            super.doFilter(req, res, chain);
        }

    }

    @Override
    public void destroy() {
        super.destroy();
    }
}