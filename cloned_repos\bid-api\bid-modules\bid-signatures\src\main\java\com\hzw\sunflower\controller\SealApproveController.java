package com.hzw.sunflower.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hzw.sunflower.common.BaseController;
import com.hzw.sunflower.util.BeanListUtil;
import com.hzw.sunflower.common.Paging;
import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.constant.MessageConstants;
import com.hzw.sunflower.dto.SealApproveDTO;
import com.hzw.sunflower.controller.request.SealApproveREQ;
import com.hzw.sunflower.entity.SealApprove;
import com.hzw.sunflower.entity.condition.SealApproveCondition;
import com.hzw.sunflower.service.SealApproveService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
* SealApproveController
*/
@Api(tags = "SealApprove服务")
@RestController
@RequestMapping("/sealApprove")
public class SealApproveController extends BaseController {
    @Autowired
    private SealApproveService sealApproveService;

    @ApiOperation(value = "根据条件分页查询SealApprove列表")
    @ApiImplicitParam(name = "sealApproveREQ", value = "用户表 查询条件", required = true, dataType = "SealApproveREQ", paramType = "body")
    @PostMapping("/list")
    public Paging<SealApprove> list(@RequestBody SealApproveREQ sealApproveREQ) {
        SealApproveCondition condition = BeanListUtil.convert(sealApproveREQ,SealApproveCondition.class);
        IPage<SealApprove> page = sealApproveService.findInfoByCondition(condition);
            return Paging.buildPaging(page);
    }


    @ApiOperation(value = "根据主键ID查询SealApprove信息")
    @ApiImplicitParam(name = "id", value = "主键ID", required = true)
    @GetMapping(value = "/get/{id}")
    public Result<SealApprove> get(@PathVariable Long id) {
        if (id == null) {
            return Result.failed(MessageConstants.GET_NULL);
        }
        SealApprove sealApprove = sealApproveService.getInfoById(id);
        return Result.ok(sealApprove);
    }

    @ApiOperation(value = "新增SealApprove信息")
    @ApiImplicitParam(name = "sealApproveDTO", value = "SealApprove表 ", required = true, dataType = "SealApproveDTO", paramType = "body")
    @PostMapping("/add")
    public Result<Boolean> add(@RequestBody SealApproveDTO sealApproveDTO) {
        Boolean bool = sealApproveService.addInfo(sealApproveDTO);
        return Result.okOrFailed(bool);
    }

    @ApiOperation(value = "修改SealApprove信息")
    @ApiImplicitParam(name = "sealApproveDTO", value = "SealApprove表 ", required = true, dataType = "SealApproveDTO", paramType = "body")
    @PutMapping(value = "/update")
    public Result<Boolean> update(@RequestBody SealApproveDTO sealApproveDTO) {
        Long id = sealApproveDTO.getId();
        if (id == null) {
            return Result.failed(MessageConstants.UPDATE_NULL);
        }
        Boolean bool = sealApproveService.updateInfo(sealApproveDTO);
        return Result.okOrFailed(bool);
    }

    @ApiOperation(value = "根据主键ID删除SealApprove表 ")
    @ApiImplicitParam(name = "id", value = "主键ID", required = true)
    @DeleteMapping(value = "/delete/{id}")
    public Result<Boolean> delete(@PathVariable Long id) {
        if (id == null) {
            return Result.failed(MessageConstants.DELETE_NULL);
        }
        Boolean bool = sealApproveService.deleteById(id);
        return Result.okOrFailed(bool);
    }

    @ApiOperation(value = "根据主键ID列表批量删除SealApprove表 ")
    @ApiImplicitParam(name = "idList", value = "主键ID列表", required = true, allowMultiple = true, paramType = "body")
    @DeleteMapping("/deleteList")
    public Result<Boolean> deleteList(@RequestBody List<Long> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return Result.failed(MessageConstants.DELETE_NULL);
        }
        Boolean bool = sealApproveService.deleteByIds(idList);
        return Result.okOrFailed(bool);
    }
}