package com.hzw.sunflower.dto;

import lombok.Data;

/**
 * <AUTHOR>
 * @date ：Created in 2022/8/12 14:33
 * @description：南京银行回单明细接收实体类
 * @modified By：`
 * @version: 1.0
 */
@Data
public class NjBankTradeDetDto {

    /**
     * 交易日期 入参必选
     */
    String jiaoyirq;

    /**
     * 交易时间 入参必选
     */
    String jiaoyisj;

    /**
     * 营业机构
     */
    String yngyjigo;

    /**
     * 柜员流水号 入参必选
     */
    String guiylius;

    /**
     * 交易码
     */
    String jiaoyima;

    /**
     * 客户号
     */
    String kehuhaoo;

    /**
     * 账户开户机构
     */
    String kaihjigo;

    /**
     * 账号
     */
    String zhanghao;

    /**
     * 账户中文名
     */
    String zhuzwmin;

    /**
     * 客户账号
     */
    String kehuzhao;

    /**
     * 交易序号 入参必选
     */
    String jiaoyixh;

    /**
     * 借贷标志 0--借 1--贷
     */
    String jiedaibz;

    /**
     * 交易金额 入参必选
     */
    String jiaoyije;

    /**
     * 账户余额
     */
    String zhanghye;

    /**
     * 交易对手账号
     */
    String duifzhho;

    /**
     *  对方账号
     */
    String duiffzzh;

    /**
     *  对方户名
     */
    String duifmche;

    /**
     * 对方金融机构网点 类型
     */
    String duifjglx;

    /**
     * 对方金融机构网点 代码
     */
    String duifjgdm;

    /**
     * 对方金融机构网点 名称
     */
    String duifjgmc;

    /**
     *  摘要代码
     */
    String zhaiyodm;

    /**
     * 摘要描述
     */
    String zhaiyoms;

    /**
     *  备注信息
     */
    String beizhuxx;

    /**
     *  打印标志 0--未打印 1--已打印
     */
    String dayinbzz;

    /**
     * 打印次数
     */
    String dayincis;

}
