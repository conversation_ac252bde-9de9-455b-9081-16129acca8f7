<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hzw.ssm.expert.dao.ExpertInfoMapper">

	 <select id="checkLoginCodeExist" parameterType="String" resultType="int">
		select count(*) from ts_user tu where tu.login_code=#{login_code}
	</select>

	<!--查询专家基本信息  -->
	<select id="getExpertInfoByUserIdOrId" parameterType="ExpertInfoEntity" resultType="ExpertInfoEntity">
		select id,
			user_id,
			user_name,
			sex,
			birthday,
			politics,
			id_type,
			id_no,
			id_fileid,
			school,
			major,
			certificate_fileid,
			educations,
			degree,
			grade,
			province,
			city,
			zone,
			district,
			company,
			company_addr,
			company_phone,
			company_zipcode,
			mobilephone,
			email,
			special_skill,
			bid_experience,
			training_experience,
			remark,
			status,
			create_time,
			delete_flag,
			expert_num,
			photo_fileid,
			technical_tital,
			technical_filed,
			position,
			qq_num,
			document_no,
			OLD_CERTIFICATE,
			OLD_PHOTO,
			OLD_TECHNICAL,
			OLD_ID,
			enter_flag enterFlag,
			out_time outTime,
			out_reason outReason,
			eval_score,
            pre_idNo,
            suffix_idNo,
			expert_type expertType,
			TECHNICAL_TIME technicalTime,
			ICBACK_NAME ICBackName,
			ICBACK_FILEID ICBackFileId
      from t_expert_info where 1=1
      <if test="id !=null and id!=''">
      		and id=#{id}
      </if>
      <if test="user_id !=null and user_id!=''">
      		and user_id=#{user_id}
      </if>
	</select>

	<select id="getExpertInfoById" parameterType="ExpertInfoEntity" resultType="ExpertInfoEntity">
		select
			id,
			user_id,
			user_name,
			head_image headImage,
			sex,
			mobilephone,
			email,
			province,
			city,
			id_type,
			id_no,
			pre_idNo,
			suffix_idNo,
			status
      from
      	  t_expert_info
      where
      	  id=#{id}
	</select>

	<select id="getExpertInfoByUserId" parameterType="ExpertInfoEntity" resultType="ExpertInfoEntity">
		select
			id,
			user_id,
			user_name,
			head_image headImage,
			sex,
			mobilephone,
			email,
			grade,
			district,
			province,
			city,
			id_type,
			id_no,
			nvl(id_fileid,pre_idNo) pre_idNo,
			nvl(ICBACK_FILEID,suffix_idNo) suffix_idNo,
			company,
			company_addr,
			company_phone,
			company_zipcode,
			status
      from
      	  t_expert_info
      where
      	  user_id = #{user_id}
      	  and delete_flag = 0
	</select>

	<update id="submitExamine">
	  update
	  	  t_expert_info
      set
      	  status = #{status},
      	  modify_time = TO_DATE(#{modifyTime},'yyyy-mm-dd hh24:mi:ss')
      where
      	  user_id = #{expertId}
	</update>

	<select id="queryByParam" parameterType="ExpertInfoEntity" resultType="ExpertInfoEntity">
		select id,
			user_id,
			user_name,
			sex,
			birthday,
			politics,
			id_type,
			id_no,
			id_fileid,
			school,
			major,
			certificate_fileid,
			educations,
			degree,
			grade,
			province,
			city,
			zone,
			district,
			company,
			company_addr,
			company_phone,
			company_zipcode,
			mobilephone,
			email,
			special_skill,
			bid_experience,
			training_experience,
			remark,
			status,
			create_time,
			delete_flag,
			expert_num,
			photo_fileid,
			technical_tital,
			technical_filed,
			position,
			qq_num,
			document_no,
			OLD_CERTIFICATE,
			OLD_PHOTO,
			OLD_TECHNICAL,
			OLD_ID,
			enter_flag enterFlag,
			out_time outTime,
			out_reason outReason,
			eval_score,
			expert_type expertType
      from
      		t_expert_info
      where
		  1=1
		  <if test="id_no !=null and id_no !=''">
				and id_no=#{id_no}
		  </if>
		  <if test="mobilephone !=null and mobilephone!=''">
				and mobilephone=#{mobilephone}
		  </if>
	</select>

	<select id="queryExpertInfoByOpenId" resultType="ExpertInfoEntity">
	  select
	  		id,
	  		user_id,
	  		status,
	  		open_id
      from
      		t_expert_info
      where
		 	 open_id = #{openId}
	</select>

	<select id="queryCompanyByUserId" resultType="ExpertInfoEntity">
	  select
	  		id,
	  		user_id,
	  		company,
			company_addr,
			company_phone,
			company_zipcode
      from
      		t_expert_info
      where
		 	user_id = #{expertId}
	</select>

	<select id="queryInfoByMobilephone" resultType="ExpertInfoEntity">
	  select
	  		id,
	  		user_id,
	  		company,
			company_addr,
			company_phone,
			company_zipcode
      from
      		t_expert_info
      where
		 	mobilephone = #{mobilephone}
	</select>

	<update id="updateInfoByUserId" parameterType="ExpertInfoEntity">
		  update
				t_expert_info
		  set
				mobilephone = #{mobilephone}
		  where
				user_id = #{user_id}
	</update>

	<update id="updateCompanyByExpertId" parameterType="ExpertInfoEntity">
	  update
	  		t_expert_info
	  set
			company=#{company,jdbcType=NUMERIC},
			company_addr=#{company_addr,jdbcType=VARCHAR},
			company_phone=#{company_phone,jdbcType=VARCHAR},
			company_zipcode=#{company_zipcode,jdbcType=VARCHAR}
		where
			id = #{id}
	</update>

	<select id="queryExpertByToken" resultType="ExpertInfoEntity">
	  select
	  		id,
	  		open_id
      from
      		t_expert_info
      where
		 	 token = #{token}
	</select>

	<!--查询专家基本信息备份表  -->
	<select id="getExpertInfoBakByUserIdOrId" parameterType="ExpertInfoEntity" resultType="ExpertInfoEntity">
		select id,
			user_id,
			user_name,
			sex,
			birthday,
			politics,
			id_type,
			id_no,
			id_fileid,
			school,
			major,
			certificate_fileid,
			educations,
			degree,
			grade,
			province,
			city,
			zone,
			district,
			company,
			company_addr,
			company_phone,
			company_zipcode,
			mobilephone,
			email,
			special_skill,
			bid_experience,
			training_experience,
			remark,
			status,
			create_time,
			delete_flag,
			expert_num,
			photo_fileid,
			technical_tital,
			technical_filed,
			position,
			qq_num,
			document_no,
			OLD_CERTIFICATE,
			OLD_PHOTO,
			OLD_TECHNICAL,
			OLD_ID,
			enter_flag enterFlag
      from t_expert_info_bak where 1=1
      <if test="id !=null and id!=''">
      		and id=#{id}
      </if>
      <if test="user_id !=null and user_id!=''">
      		and user_id=#{user_id}
      </if>
	</select>

	<!--专家基本信息保存  -->
	<insert id="saveExpertInfo" parameterType="ExpertInfoEntity">
		insert into t_expert_info (
			id,
			user_id,
			user_name,
			sex,
			birthday,
			politics,
			id_type,
			id_no,
			id_fileid,
			school,
			major,
			certificate_fileid,
			educations,
			degree,
			grade,
			province,
			city,
			zone,
			district,
			company,
			company_addr,
			company_phone,
			company_zipcode,
			mobilephone,
			email,
			special_skill,
			bid_experience,
			training_experience,
			remark,
			status,
			create_time,
			modify_time,
			audit_time,
			delete_flag,
			expert_num,
			photo_fileid,
			technical_tital,
			position,
			qq_num,
			document_no,
			technical_filed,
			create_id,
			OLD_CERTIFICATE,
			OLD_PHOTO,
			OLD_TECHNICAL,
			OLD_ID,
			ENTER_FLAG,
			EXPERT_TYPE,
			TECHNICAL_TIME,
			REFERRER_QRCODE,
			ICBACK_NAME,
			ICBACK_FILEID
		)
		values(
			#{id},
			#{user_id},
			#{user_name,jdbcType=VARCHAR},
			#{sex,jdbcType=VARCHAR},
			#{birthday},
			#{politics,jdbcType=VARCHAR},
			#{id_type,jdbcType=VARCHAR},
			#{id_no,jdbcType=VARCHAR},
			#{id_fileid,jdbcType=VARCHAR},
			#{school,jdbcType=VARCHAR},
			#{major,jdbcType=VARCHAR},
			#{certificate_fileid,jdbcType=VARCHAR},
			#{educations,jdbcType=VARCHAR},
			#{degree,jdbcType=VARCHAR},
			#{grade,jdbcType=NUMERIC},
			#{province,jdbcType=NUMERIC},
			#{city,jdbcType=NUMERIC},
			#{zone,jdbcType=NUMERIC},
			#{district,jdbcType=NUMERIC},
			#{company,jdbcType=NUMERIC},
			#{company_addr,jdbcType=VARCHAR},
			#{company_phone,jdbcType=VARCHAR},
			#{company_zipcode,jdbcType=VARCHAR},
			#{mobilephone,jdbcType=VARCHAR},
			#{email,jdbcType=VARCHAR},
			#{special_skill,jdbcType=VARCHAR},
			#{bid_experience,jdbcType=VARCHAR},
			#{training_experience,jdbcType=VARCHAR},
			#{remark,jdbcType=VARCHAR},
			#{status,jdbcType=NUMERIC},
			#{create_time,jdbcType = TIMESTAMP},
			#{modify_time,jdbcType = TIMESTAMP},
			#{audit_time,jdbcType = TIMESTAMP},
			#{delete_flag},
			#{expert_num,jdbcType=VARCHAR},
			#{photo_fileid,jdbcType=VARCHAR},
			#{technical_tital,jdbcType=VARCHAR},
			#{position,jdbcType=VARCHAR},
			#{qq_num,jdbcType=VARCHAR},
			#{document_no,jdbcType=VARCHAR},
			#{technical_filed,jdbcType=VARCHAR},
			#{create_id,jdbcType=VARCHAR},
			#{old_certificate,jdbcType=VARCHAR},
			#{old_photo,jdbcType=VARCHAR},
			#{old_technical,jdbcType=VARCHAR},
			#{old_id,jdbcType=VARCHAR},
			#{enterFlag,jdbcType=NUMERIC},
			#{expertType,jdbcType=VARCHAR},
			#{technicalTime,jdbcType = TIMESTAMP},
			#{referrer_qrcode,jdbcType=VARCHAR},
			#{ICBackName,jdbcType=VARCHAR},
			#{ICBackFileId,jdbcType=VARCHAR}
		)
	</insert>

	<insert id="saveExpertInfoBySmall" parameterType="ExpertInfoEntity" useGeneratedKeys="true" keyProperty="id">
		insert into t_expert_info (
			id,
			user_id,
			user_name,
			mobilephone,
			sex,
			birthday,
			open_id,
			id_no,
			id_type,
			pre_idNo,
			id_fileid,
			old_id,
			suffix_idNo,
			ICBACK_FILEID,
			ICBACK_NAME,
			status,
			referrer_qrcode,
			create_time,
			enter_flag
		)
		values(
			#{id},
			#{user_id,jdbcType=VARCHAR},
			#{user_name,jdbcType=VARCHAR},
			#{mobilephone,jdbcType=VARCHAR},
			#{sex,jdbcType=VARCHAR},
			#{birthday,jdbcType = TIMESTAMP},
			#{open_id,jdbcType=VARCHAR},
			#{id_no,jdbcType=VARCHAR},
			#{id_type,jdbcType=VARCHAR},
			#{pre_idNo,jdbcType=VARCHAR},
			#{id_fileid,jdbcType=VARCHAR},
			#{old_id,jdbcType=VARCHAR},
			#{suffix_idNo,jdbcType=VARCHAR},
			#{ICBackFileId,jdbcType=VARCHAR},
			#{ICBackName,jdbcType=VARCHAR},
			#{status,jdbcType=NUMERIC},
			#{referrer_qrcode,jdbcType=VARCHAR},
			#{create_time,jdbcType = TIMESTAMP},
			#{enterFlag,jdbcType=NUMERIC}
		)
	</insert>

	<!--向专家备份表新增数据  -->
	<insert id="saveExpertInfoBak" parameterType="ExpertInfoEntity">
		insert into t_expert_info_bak (
			id,
			user_id,
			user_name,
			sex,
			birthday,
			politics,
			id_type,
			id_no,
			id_fileid,
			school,
			major,
			certificate_fileid,
			educations,
			degree,
			grade,
			province,
			city,
			zone,
			district,
			company,
			company_addr,
			company_phone,
			company_zipcode,
			mobilephone,
			email,
			special_skill,
			bid_experience,
			training_experience,
			remark,
			status,
			create_time,
			modify_time,
			audit_time,
			delete_flag,
			expert_num,
			photo_fileid,
			technical_tital,
			position,
			qq_num,
			document_no,
			technical_filed,
			create_id,
			OLD_CERTIFICATE,
			OLD_PHOTO,
			OLD_TECHNICAL,
			OLD_ID,
			ENTER_FLAG
		)
		values(
			#{id},
			#{user_id},
			#{user_name,jdbcType=VARCHAR},
			#{sex,jdbcType=VARCHAR},
			#{birthday,jdbcType = TIMESTAMP},
			#{politics,jdbcType=VARCHAR},
			#{id_type,jdbcType=VARCHAR},
			#{id_no,jdbcType=VARCHAR},
			#{id_fileid,jdbcType=VARCHAR},
			#{school,jdbcType=VARCHAR},
			#{major,jdbcType=VARCHAR},
			#{certificate_fileid,jdbcType=VARCHAR},
			#{educations,jdbcType=VARCHAR},
			#{degree,jdbcType=VARCHAR},
			#{grade,jdbcType=NUMERIC},
			#{province,jdbcType=NUMERIC},
			#{city,jdbcType=NUMERIC},
			#{zone,jdbcType=NUMERIC},
			#{district,jdbcType=NUMERIC},
			#{company,jdbcType=NUMERIC},
			#{company_addr,jdbcType=VARCHAR},
			#{company_phone,jdbcType=VARCHAR},
			#{company_zipcode,jdbcType=VARCHAR},
			#{mobilephone,jdbcType=VARCHAR},
			#{email,jdbcType=VARCHAR},
			#{special_skill,jdbcType=VARCHAR},
			#{bid_experience,jdbcType=VARCHAR},
			#{training_experience,jdbcType=VARCHAR},
			#{remark,jdbcType=VARCHAR},
			#{status,jdbcType=NUMERIC},
			#{create_time,jdbcType = TIMESTAMP},
			#{modify_time,jdbcType = TIMESTAMP},
			#{audit_time,jdbcType = TIMESTAMP},
			#{delete_flag,jdbcType=NUMERIC},
			#{expert_num,jdbcType=VARCHAR},
			#{photo_fileid,jdbcType=VARCHAR},
			#{technical_tital,jdbcType=VARCHAR},
			#{position,jdbcType=VARCHAR},
			#{qq_num,jdbcType=VARCHAR},
			#{document_no,jdbcType=VARCHAR},
			#{technical_filed,jdbcType=VARCHAR},
			#{create_id,jdbcType=VARCHAR},
			#{old_certificate,jdbcType=VARCHAR},
			#{old_photo,jdbcType=VARCHAR},
			#{old_technical,jdbcType=VARCHAR},
			#{old_id,jdbcType=VARCHAR},
			#{enterFlag,jdbcType=NUMERIC}
		)
	</insert>

	<!--修改专家基本信息表  -->
	<update id="updateExpertInfo" parameterType="ExpertInfoEntity">
		update t_expert_info set 
			user_name=#{user_name,jdbcType=VARCHAR},
			sex=#{sex,jdbcType=VARCHAR},
			birthday=#{birthday},
			politics=#{politics,jdbcType=VARCHAR},
			id_type=#{id_type,jdbcType=VARCHAR},
			id_no=#{id_no,jdbcType=VARCHAR},
			id_fileid=#{id_fileid,jdbcType=VARCHAR},
			school=#{school,jdbcType=VARCHAR},
			major=#{major,jdbcType=VARCHAR},
			certificate_fileid=#{certificate_fileid,jdbcType=VARCHAR},
			educations=#{educations,jdbcType=VARCHAR},
			degree=#{degree,jdbcType=VARCHAR},
			grade=#{grade,jdbcType=NUMERIC},
			province=#{province,jdbcType=VARCHAR},
			city=#{city,jdbcType=VARCHAR},
			zone=#{zone,jdbcType=VARCHAR},
			district=#{district,jdbcType=VARCHAR},
			company=#{company,jdbcType=NUMERIC},
			company_addr=#{company_addr,jdbcType=VARCHAR},
			company_phone=#{company_phone,jdbcType=VARCHAR},
			company_zipcode=#{company_zipcode,jdbcType=VARCHAR},
			mobilephone=#{mobilephone,jdbcType=VARCHAR},
			email=#{email,jdbcType=VARCHAR},
			special_skill=#{special_skill,jdbcType=VARCHAR},
			bid_experience=#{bid_experience,jdbcType=VARCHAR},
			training_experience=#{training_experience,jdbcType=VARCHAR},
			remark=#{remark,jdbcType=VARCHAR},
			status=#{status,jdbcType=NUMERIC},
			modify_time=#{modify_time,jdbcType = TIMESTAMP},
			photo_fileid=#{photo_fileid,jdbcType=VARCHAR},
			technical_tital=#{technical_tital,jdbcType=VARCHAR},
			position=#{position,jdbcType=VARCHAR},
			qq_num=#{qq_num,jdbcType=VARCHAR},
			document_no=#{document_no,jdbcType=VARCHAR},
			technical_filed=#{technical_filed,jdbcType=VARCHAR},
			old_certificate=#{old_certificate,jdbcType=VARCHAR},
			old_photo=#{old_photo,jdbcType=VARCHAR},
			old_technical=#{old_technical,jdbcType=VARCHAR},
			old_id=#{old_id,jdbcType=VARCHAR},
			EXPERT_TYPE =#{expertType,jdbcType=VARCHAR},
			TECHNICAL_TIME =#{technicalTime,jdbcType = TIMESTAMP},
			IS_BASIC_INFO =#{isBasicInfo,jdbcType = TIMESTAMP},
			IS_CERTIFICATE_INFO =#{isCertificateInfo,jdbcType = TIMESTAMP},
			ICBACK_NAME=#{ICBackName,jdbcType=VARCHAR},
			ICBACK_FILEID=#{ICBackFileId,jdbcType=VARCHAR}
		where id=#{id}
	</update>

	<!--修改专家基本信息表  -->
	<update id="updateExpertFileStatus" parameterType="ExpertInfoEntity">
		update t_expert_info set
			IS_CERTIFICATE_INFO=#{isCertificateInfo,jdbcType=VARCHAR}
		where id=#{id}
	</update>
	<!--修改专家是否完善-->
	<update id="updateExpertCompelete" parameterType="ExpertSupplementInfoEntity">
		update t_expert_info set
			IS_BASIC_INFO =#{isBasicInfo,jdbcType = VARCHAR},
			IS_CERTIFICATE_INFO =#{isCertificateInfo,jdbcType = VARCHAR}
		where id=#{id}
	</update>

	<!--修改专家备注-->
	<update id="updateExpertRemark" parameterType="ExpertSupplementInfoEntity">
		update t_expert_info set REMARK =#{remark,jdbcType = VARCHAR} where id=#{id}
	</update>

	<update id="updateTokenByOpenId" parameterType="ExpertInfoEntity">
		update
			t_expert_info
		set
			token = #{token},
			head_image = #{headImage},
			expiration_time = #{expirationTime}
		where
			open_id = #{open_id}
	</update>

	<update id="updateExpertInfoById" parameterType="ExpertInfoEntity">
		update
			t_expert_info
		<set>
			<if test="open_id != null and !&quot;&quot;.equals(open_id)">
				open_id = #{open_id},
			</if>
			<if test="user_name != null and !&quot;&quot;.equals(user_name)">
				user_name = #{user_name},
			</if>
			<if test="sex != null and !&quot;&quot;.equals(sex)">
				sex = #{sex},
			</if>
			<if test="district != null and !&quot;&quot;.equals(district)">
				district = #{district},
			</if>
			<if test="mobilephone != null and !&quot;&quot;.equals(mobilephone)">
				mobilephone = #{mobilephone},
			</if>
			<if test="email != null ">
				email = #{email},
			</if>
			<if test="province != null and !&quot;&quot;.equals(province)">
				province = #{province},
			</if>
			<if test="city != null and !&quot;&quot;.equals(city)">
				city = #{city},
			</if>
			<if test="id_type != null and !&quot;&quot;.equals(id_type)">
				id_type = #{id_type},
			</if>
			<if test="id_no != null and !&quot;&quot;.equals(id_no)">
				id_no = #{id_no},
			</if>
			<if test="pre_idNo != null and !&quot;&quot;.equals(pre_idNo)">
				pre_idNo = #{pre_idNo},
			</if>
			<if test="pre_idNo != null and !&quot;&quot;.equals(pre_idNo)">
				id_fileid = #{pre_idNo},
			</if>
			<if test="grade != null and !&quot;&quot;.equals(grade)">
				grade = #{grade,jdbcType=NUMERIC},
			</if>
			<if test="suffix_idNo != null and !&quot;&quot;.equals(suffix_idNo)">
				suffix_idNo = #{suffix_idNo},
			</if>
			<if test="suffix_idNo != null and !&quot;&quot;.equals(suffix_idNo)">
				ICBACK_FILEID = #{suffix_idNo},
			</if>
			<if test="status != null and !&quot;&quot;.equals(status)">
				status = #{status},
			</if>
			<if test="headImage != null and !&quot;&quot;.equals(headImage)">
				head_image = #{headImage},
			</if>
		</set>
		where
			id = #{id}
	</update>

	<!--修改专家平均分  -->
	<update id="updateExpertScore" parameterType="ExpertInfoEntity">
		update t_expert_info set 
			EVAL_SCORE=#{eval_score,jdbcType=NUMERIC}
		where id=#{id}
	</update>

	<!--添加专家执业资格  -->
	<insert id="addExpertPractice" parameterType="ExpertPracticeEntity">
		insert into t_expert_practice (
			id,
			user_id,
			certificate,
			certificate_no,
			certificate_fileid,
			old_certificate,
			delete_flag
		) values(
			#{id},
			#{user_id,jdbcType=VARCHAR},
			#{certificate,jdbcType=VARCHAR},
			#{certificate_no,jdbcType=VARCHAR},
			#{certificate_fileid,jdbcType=VARCHAR},
			#{old_certificate,jdbcType=VARCHAR},
			#{delete_flag}
		)
	</insert>

	<!--添加专家执业资格备份表  -->
	<insert id="addExpertPracticeBak" parameterType="ExpertPracticeEntity">
		insert into t_expert_practice_bak (
			id,
			user_id,
			certificate,
			certificate_no,
			certificate_fileid,
			old_certificate,
			delete_flag
		) values(
			#{id},
			#{user_id,jdbcType=VARCHAR},
			#{certificate,jdbcType=VARCHAR},
			#{certificate_no,jdbcType=VARCHAR},
			#{certificate_fileid,jdbcType=VARCHAR},
			#{old_certificate,jdbcType=VARCHAR},
			#{delete_flag}
		)
	</insert>

	<!--根据专家user_id 删除对应备份表中的数据  -->
	<delete id="deleteExpertInfoBakByUserId" parameterType="String">
		delete from t_expert_info_bak te where te.user_id=#{user_id}
	</delete>

	<!--根据专家user_id 删除专家职业资格记录备份表  -->
	<delete id="deletePracticeBakByUserId" parameterType="String">
		delete from t_expert_practice_bak where user_id=#{user_id}
	</delete>

	<!--根据专家user_id 删除专家评标专业记录备份表  -->
	<delete id="deleteMajorBakByUserId" parameterType="String">
		delete from t_expert_major_bak where user_id=#{user_id}
	</delete>

	<!--根据专家user_id 删除专家工作经历记录备份表  -->
	<delete id="deleteExperienceBakByUserId" parameterType="String">
		delete from t_expert_experience_bak where user_id=#{user_id}
	</delete>


	<!--更新职业资格  -->
	<update id="updateExpertPractice" parameterType="ExpertPracticeEntity">
		update t_expert_practice t set 
			t.certificate=#{certificate,jdbcType=VARCHAR},
			t.certificate_no=#{certificate_no,jdbcType=VARCHAR},
			t.certificate_fileid=#{certificate_fileid,jdbcType=VARCHAR},
			t.old_certificate=#{old_certificate,jdbcType=VARCHAR} 
		where t.id=#{id}
	</update>

	<!--查询专家执业资格列表  -->
	<select id="queryPracticeByUserId" parameterType="String" resultType="ExpertPracticeEntity">
		select t.id,t.user_id,t.certificate,t.certificate_no,t.certificate_fileid,t.delete_flag,t.old_certificate from t_expert_practice t where t.delete_flag=0 and t.user_id=#{user_id} order by t.id asc
	</select>

	<!--查询专家执业资格备份列表  -->
	<select id="queryPracticeBakByUserId" parameterType="String" resultType="ExpertPracticeEntity">
		select t.id,t.user_id,t.certificate,t.certificate_no,t.certificate_fileid,t.delete_flag,t.old_certificate from t_expert_practice_bak t where t.delete_flag=0 and t.user_id=#{user_id} order by t.id asc
	</select>


	<!--查询专家评标专业列表  -->
	<select id="queryMajorByUserId" parameterType="String" resultType="ExpertMajorEntity">
		select tm.id,tm.user_id,tm.major,
			(
				floor(
				( months_between ( TO_DATE ( to_char ( sysdate, 'yyyy-MM-dd HH24:mi:ss' ), 'yyyy-mm-dd hh24:mi:ss' ), TO_DATE ( tm.get_time, 'yyyy-mm-dd hh24:mi:ss' ) ) ) / 12
				)
			) AS YEAR,
		tm.get_time getTime, tm.delete_flag,ts.spe_name as major_name,tm.cou_major,tc.spe_cou_name as cou_major_name from t_expert_major tm
		left join t_specialty_info ts on tm.major=ts.spe_id 
    	left join t_specialty_cou_info tc on tm.cou_major=tc.spe_cou_id
    	where tm.delete_flag=0 and tm.user_id=#{user_id}
    	order by tm.id asc
	</select>

	<!--查询专家评标专业备份列表 -->
	<select id="queryMajorBakByUserId" parameterType="String" resultType="ExpertMajorEntity">
		select tm.id,tm.user_id,tm.major,tm.year,tm.delete_flag,ts.spe_name as major_name,tm.cou_major,
		tm.GET_TIME getTime,
		tc.spe_cou_name as cou_major_name from t_expert_major_bak tm
		left join t_specialty_info ts on tm.major=ts.spe_id 
	    left join t_specialty_cou_info tc on tm.cou_major=tc.spe_cou_id
	    where tm.delete_flag=0 and tm.user_id=#{user_id}
	    order by tm.id asc
	</select>


	<!--查询评标专业表  -->
	<select id="querySpecialtyInfoList" resultType="SpecialtyInfoEntity">
		select sp.spe_id,sp.spe_name,sp.spe_code,sp.spe_parent,sp.spe_level from t_specialty_info sp order by to_number(sp.spe_id)
	</select>

	<!--查询国家评标专业表  -->
	<select id="querySpecialtyCouInfoList" resultType="SpecialtyCouInfoEntity">
		select spc.spe_cou_id,spc.spe_cou_name,spc.spe_cou_code,spc.spe_cou_parent,spc.spe_cou_level from t_specialty_cou_info spc
	</select>

	<!-- 添加专家评标专业 -->
	<insert id="addExpertMajorInfo" parameterType="ExpertMajorEntity">
		insert into t_expert_major(
			id,
			user_id,
			major,
			cou_major,
			year,
			get_time,
			delete_flag
		)  values(
			#{id},
			#{user_id,jdbcType=VARCHAR},
			#{major,jdbcType=VARCHAR},
			#{cou_major,jdbcType=VARCHAR},
			#{year,jdbcType=NUMERIC},
			#{getTime,jdbcType=VARCHAR},
			#{delete_flag}
		)
	</insert>

	<!-- 专家评标专业备份-->
	<insert id="addExpertMajorInfoBak" parameterType="ExpertMajorEntity">
		insert into t_expert_major_bak(
			id,
			user_id,
			major,
			cou_major,
			year,
			get_time,
			delete_flag
		)  values(
			#{id},
			#{user_id,jdbcType=VARCHAR},
			#{major,jdbcType=VARCHAR},
			#{cou_major,jdbcType=VARCHAR},
			#{year,jdbcType=NUMERIC},
			#{getTime,jdbcType=VARCHAR},
			#{delete_flag}
		)
	</insert>


	<!--修改专家评标专业  -->
	<update id="updateExpertMajorInfo" parameterType="ExpertMajorEntity">
		update t_expert_major te set te.major=#{major,jdbcType=VARCHAR},cou_major=#{cou_major,jdbcType=VARCHAR},te.year=#{year,jdbcType=NUMERIC},
				te.get_time = #{getTime,jdbcType=VARCHAR}
		where te.id=#{id}
	</update>

	<!--添加专家工作经历 -->
	<insert id="addExpertExperience" parameterType="ExpertExperienceEntity">
		insert into t_expert_experience (
			id,
			user_id,
			between_time,
			company,
			witness,
			witness_phone,
			delete_flag
		) values(
			#{id},
			#{user_id,jdbcType=VARCHAR},
			#{between_time,jdbcType=VARCHAR},
			#{company,jdbcType=VARCHAR},
			#{witness,jdbcType=VARCHAR},
			#{witness_phone,jdbcType=VARCHAR},
			#{delete_flag}
		)
	</insert>

	<!--专家工作经历备份 -->
	<insert id="addExpertExperienceBak" parameterType="ExpertExperienceEntity">
		insert into t_expert_experience_bak (
			id,
			user_id,
			between_time,
			company,
			witness,
			witness_phone,
			delete_flag
		) values(
			#{id},
			#{user_id,jdbcType=VARCHAR},
			#{between_time,jdbcType=VARCHAR},
			#{company,jdbcType=VARCHAR},
			#{witness,jdbcType=VARCHAR},
			#{witness_phone,jdbcType=VARCHAR},
			#{delete_flag}
		)
	</insert>

	<!--修改专家工作经历  -->
	<update id="updateExpertExperience" parameterType="ExpertExperienceEntity">
		update t_expert_experience te set 
			te.between_time=#{between_time,jdbcType=VARCHAR},
			te.company=#{company,jdbcType=VARCHAR},
			te.witness=#{witness,jdbcType=VARCHAR},
			te.witness_phone=#{witness_phone,jdbcType=VARCHAR},
			te.delete_flag=#{delete_flag,jdbcType=NUMERIC}
		where te.id=#{id}
	</update>

	<!--删除专家工作经历  -->
	<update id="deleteExpertExperienceById" parameterType="String">
		update t_expert_experience te set te.delete_falg=1 where te.id=#{id}
	</update>

	<!-- 查询专家工作经历列表 -->
	<select id="queryExpertExperienceList" parameterType="String" resultType="ExpertExperienceEntity">
		select te.id,
			te.user_id,
			te.between_time,
			te.company,
			te.witness,
			te.witness_phone,
			te.delete_flag 
		from t_expert_experience te 
		where te.delete_flag=0 and te.user_id=#{user_id} order by te.id
	</select>

	<!-- 查询专家工作经历 备份列表-->
	<select id="queryExpertExperienceBakList" parameterType="String" resultType="ExpertExperienceEntity">
		select te.id,te.user_id,te.between_time,te.company,te.witness,te.witness_phone,te.delete_flag from t_expert_experience_bak te where te.delete_flag=0 and te.user_id=#{user_id}
	</select>


	<!--根据user_id 查询审批意见实体类 ,查询一条最新的退回原因 -->
	<select id="getExpertAuditEntityByUserId" parameterType="String" resultType="ExpertAuditEntity">
		select tea.id,tea.expert_id,tea.reason,tea.status,tea.audit_time from (
		select rownum,te.* from t_expert_audit te order by te.audit_time desc) tea 
		where tea.delete_flag=0 and tea.status=1 and rownum=1 and tea.expert_id=#{user_id}
	</select>

	<!--专家信息管理列表  -->
	<select id="queryPageExpertInfoManagerList" parameterType="ExpertInfoEntity" resultType="ExpertInfoEntity">
		select b.id, b.user_id,b.EXPERT_TYPE expertType,b.MOBILEPHONE, b.expert_num, b.user_name, b.province, b.city, b.zone, b.district,
			 b.modify_time, b.audit_time, b.expire_time, b.status, max(b.specialty_name) specialty_name,b.out_time,b.out_reason,b.enter_flag enterFlag,b.eval_score
        from (select expi.id,expi.EXPERT_TYPE,expi.MOBILEPHONE, expi.user_id, expi.expert_num,expi.user_name, expi.province, expi.city, expi.zone,
                       expi.district, expi.modify_time, expi.audit_time, expi.expire_time, expi.status,expi.eval_score,expi.enter_flag,
                       wm_concat(tsi.spe_name) over (partition by expi.user_id order by to_number(tsi.spe_id)) specialty_name,
                       expi.out_time,expi.out_reason
                  from t_expert_info expi
                  left join t_expert_major expm on expm.user_id = expi.user_id
                  left join t_specialty_info tsi on tsi.spe_id = expm.major
                 where expi.delete_flag = 0 and expi.status in (3, 4, 5, 6, 7, 8, 9, 10, 11, 12)
                 and expm.delete_flag = 0 and expm.major is not null
			<if test="user_name!=null and user_name!=''">
				and expi.user_name like '%${user_name}%'
			</if>
			<if test="status!=null and status!=''">
				<if test="status == 9 or status == 8 or status==7 or status==4 or status==10">
					and expi.status=#{status}
				</if>
				<if test="status==3">
					and expi.status in (3,8)
				</if>
				<if test="status==5">
					and expi.status in (5,6)
				</if>
				<if test="status==11">
					and expi.status in (11,12)
				</if>
			</if>
			<if test="expertType!=null and expertType!=''">
				and expi.EXPERT_TYPE like '%${expertType}%'
			</if>
			<if test="mobilephone!=null and mobilephone!=''">
				and expi.MOBILEPHONE like '%${mobilephone}%'
			</if>
			<if test="province!=null and province!=''">
				and expi.province like '%${province}%'
			</if>
			<if test="city!=null and city!=''">
				and expi.city like '%${city}%'
			</if>
			<if test="search_begin_time!=null and search_begin_time!=''">
				and to_date(to_char(expi.audit_time,'yyyy-mm-dd'),'yyyy-mm-dd')>=to_date(#{search_begin_time},'yyyy-mm-dd')
			</if>
			<if test="search_end_time!=null and search_end_time!=''">
				and to_date(to_char(expi.audit_time,'yyyy-mm-dd'),'yyyy-mm-dd')<![CDATA[<=]]>to_date(#{search_end_time},'yyyy-mm-dd')
			</if>
			<if test="spe_id!=null and spe_id!=''">
				and expm.major=#{spe_id}
			</if>
			) b
            group by b.id, b.user_id,b.EXPERT_TYPE,b.MOBILEPHONE,b.expert_num, b.user_name, b.province,b.district,
                  b.city, b.zone,b.modify_time,b.audit_time,b.expire_time, b.status,b.out_time,b.out_reason,b.eval_score,b.enter_flag

            order by b.status asc, b.audit_time desc
	</select>

	<!--更新专家等级，1：普通专家，2：高级专家  -->
	<update id="updateExpertGrade" parameterType="ExpertInfoEntity">
		update t_expert_info te set te.grade=#{grade},te.modify_time=#{modify_time} where te.user_id=#{user_id}
	</update>

	<!--更新备份表专家等级，1：普通专家，2：高级专家  -->
	<update id="updateExpertBakGrade" parameterType="ExpertInfoEntity">
		update t_expert_info_bak te set te.grade=#{grade},te.modify_time=#{modify_time} where te.user_id=#{user_id}
	</update>

	<!--更新专家状态，3：未暂停，4：暂停资格  -->
	<update id="updateExpertStatus" parameterType="ExpertInfoEntity">
		update t_expert_info te set te.status=#{status},te.modify_time=#{modify_time}
		<if test="outTime!=null and outTime!=''">
			,te.OUT_TIME=#{outTime}
		</if>
		<if test="outReason!=null and outReason!=''">
			,te.OUT_REASON=#{outReason}
		</if>
		 where te.user_id=#{user_id}
	</update>

	<!--更新备份表专家状态，3：未暂停，4：暂停资格  -->
	<update id="updateExpertBakStatus" parameterType="ExpertInfoEntity">
		update t_expert_info_bak te set te.status=#{status},te.modify_time=#{modify_time} where te.user_id=#{user_id}
	</update>

	<!--增加专家信息修改记录  -->
	<insert id="addExpertUpdateRecord" parameterType="ExpertUpdateRecordEntity">
		insert into t_expert_update_record(
			id,
			user_id,
			item,
			content_before,
			content_after,
			modify_time,
			modify_role,
			modify_user,
			modify_reason,
			delete_flag,
			batch_number
		)values(
			#{id},
			#{user_id,jdbcType=VARCHAR},
			#{item,jdbcType=VARCHAR},
			#{content_before,jdbcType=VARCHAR},
			#{content_after,jdbcType=VARCHAR},
			#{modify_time},
			#{modify_role,jdbcType=VARCHAR},
			#{modify_user,jdbcType=VARCHAR},
			#{modify_reason,jdbcType=VARCHAR},
			#{delete_flag,jdbcType=NUMERIC},
			#{batch_number,jdbcType=NUMERIC}
		)
	</insert>

	<!--查询专家信息修改记录列表,用于信息修改审核-->
	<!--<select id="queryExpertUpdateRecordList" parameterType="String" resultType="ExpertUpdateRecordEntity">-->
		<!--select tr.id,tr.item,tr.content_before,tr.content_after,tr.modify_time,tr.modify_reason from t_expert_update_record tr -->
		<!--where tr.delete_flag=0 and tr.batch_number=(select max(tu.batch_number) from t_expert_update_record tu -->
		<!--where tu.delete_flag=0 and tu.user_id=#{user_id} and tu.modify_user=#{user_id}) and tr.user_id=#{user_id} and tr.modify_user=#{user_id}-->
		<!--order by tr.modify_time desc-->
	<!--</select>-->
	<select id="queryExpertUpdateRecordList" parameterType="String" resultType="ExpertUpdateRecordEntity">
		SELECT
		*
		FROM
		(
		SELECT
		ROWNUM,
		t.*
		FROM
		(
			SELECT
			tr.id,
			tr.item,
			tr.content_before,
			tr.content_after,
			tr.modify_time,
			tr.modify_reason
			FROM
			t_expert_update_record tr
			WHERE
			tr.delete_flag = 0
			AND tr.USER_ID = #{user_id}
			ORDER BY
			tr.modify_time DESC
		) t
		) a
		WHERE
		ROWNUM &lt;= 20
	</select>

	<!--查询所有专家信息修改记录列表,用于专家信息管理页面查看-->
	<select id="queryAllExpertUpdateRecordList" parameterType="String" resultType="ExpertUpdateRecordEntity">
		select er.id,er.item,er.content_before,er.content_after,er.modify_time,tr.role_name as modify_role_name,tu.user_name as modify_user_name,er.modify_reason,er.batch_number from t_expert_update_record er 
		left join ts_user tu on er.modify_user=tu.user_id
		left join ts_role tr on er.MODIFY_ROLE=tr.role_id
		where er.delete_flag=0 and er.user_id=#{user_id}
		order by er.modify_time desc,er.batch_number desc
	</select>

	<select id="queryTitleStatus" parameterType="String" resultType="java.util.Map">
		SELECT
			titleYears,
			expertType
		FROM
			(
			SELECT
				(
				floor(
				( months_between ( TO_DATE ( to_char ( sysdate, 'yyyy-MM-dd HH24:mi:ss' ), 'yyyy-mm-dd hh24:mi:ss' ), TO_DATE ( t.get_time, 'yyyy-mm-dd hh24:mi:ss' ) ) ) / 12
				)
				) AS titleYears,
				a.EXPERT_TYPE expertType
			FROM
				t_title_info t
				LEFT JOIN T_TECHNICAL_TITAL a ON a.CODE = t.TITLE_ID
			WHERE
				t.expert_id = #{expertId}
				AND t.delete_flag = 0
			ORDER BY
				t.GET_TIME ASC
			) b
		WHERE
			b.expertType IN ( 1, 2 )
			OR ( b.expertType = 3 AND titleYears >= 8 )
	</select>

	<!--删除修改记录，修改审核未通过，修改记录无效  -->
	<update id="deleteExpertUpdateRecordById" parameterType="String">
		update t_expert_update_record tr set tr.delete_flag=1 where tr.id=#{id}
	</update>

	<!--查询专家修改记录最大批次编号  -->
	<select id="selectMaxBatchNumFromRecord" parameterType="String" resultType="int">
		select nvl(max(r.batch_number),0) from t_expert_update_record r where r.user_id=#{user_id}
	</select>

	<!--根据专业id查询评标专业  -->
	<select id="selectSpecialtyInfoBySpeId" parameterType="String" resultType="SpecialtyInfoEntity">
		select si.spe_id,si.spe_name,si.spe_code,si.spe_parent,si.spe_level,si.spe_remark from t_specialty_info si where si.spe_id=#{spe_id}
	</select>

	<!--根据专业id查询国家评标专业  -->
	<select id="selectSpecialtyCouInfoBySpeId" parameterType="String" resultType="SpecialtyCouInfoEntity">
		select si.spe_cou_id,si.spe_cou_name,si.spe_cou_code,si.spe_cou_parent,si.spe_cou_level,si.spe_cou_remark from t_specialty_cou_info si where si.spe_cou_id=#{spe_cou_id}
	</select>

	<!--验证身份证号码在数据库中是否已经存在  -->
	<select id="checkIdNo" parameterType="ExpertInfoEntity" resultType="int">
		select count(*) from t_expert_info ti where ti.id_no=#{id_no}
		<if test="user_id!=null and user_id!=''">
			and ti.user_id != #{user_id}
		</if>
		and ti.DELETE_FLAG = 0
	</select>
	<!--验证档案号在数据库中是否已经存在  -->
	<select id="checkDocumentNo" parameterType="ExpertInfoEntity" resultType="int">
		select count(*) from t_expert_info ti where ti.document_no=#{document_no}
		<if test="user_id!=null and user_id!=''">
			and ti.user_id != #{user_id}
		</if>
		and ti.DELETE_FLAG = 0
	</select>
	<!--验证手机号码在数据库中是否已经存在  -->
	<select id="checkMobile" parameterType="ExpertInfoEntity" resultType="int">
		select count(*) from t_expert_info ti where ti.MOBILEPHONE=#{mobilephone}
		<if test="user_id!=null and user_id!=''">
			and ti.user_id != #{user_id}
		</if>
		and ti.DELETE_FLAG = 0
	</select>

	<!--查询最大专家编号  -->
	<select id="getMaxExpertNum" resultType="String">
		select max(ei.expert_num) from t_expert_info ei
	</select>

	<!--更新文件名称  -->
	<update id="updateFile" parameterType="ExpertInfoEntity">
		update ${tableName} tr
		set
			tr.delete_flag=0
			<if test="tableField!=null and tableField !=''">
				,tr.${tableField}=''
			</if>
		where tr.id=#{id}
	</update>

	<update id="updatePracticeFile" parameterType="ExpertInfoEntity">
		update
			t_expert_practice
		set
			CERTIFICATE_FILEID = NULL,
			OLD_CERTIFICATE = NULL
		where
			id = #{id}
	</update>

	<!--获取评标的专家信息 -->
	<select id="queryPageBidExpertInfo" parameterType="ExpertInfoEntity" resultType="ExpertInfoEntity">
		select wm_concat(c.id) id,
		       c.user_id,
		       c.user_name,
		       c.expert_num,
		       c.district,
		       c.specialty_name
		  from (select b.id,
		               b.user_id,
		               b.user_name,
		               b.expert_num,
		               b.district,
		               max(b.specialty_name) specialty_name
		          from (select a.id,
		                       b.user_id,
		                       b.USER_NAME,
		                       b.expert_num,
		                       b.district,
		                      specialty_name
		                  from t_extract_result a
		                  left join t_expert_info b
		                    on a.expert_id = b.user_id
		                  left join (select user_id,
                                   wm_concat(i.spe_name) as specialty_name
                              from t_expert_major m
                              left join t_specialty_info i
                                on m.major = i.spe_id
                             where m.delete_flag = 0
                             <if test="spe_id!=null and spe_id!=''">
								and m.major=#{spe_id}
						   </if>
                             group by user_id) expm
		                    on expm.user_id = b.user_id
		                  
		                 where a.join_status = 0
		                   and a.delete_flag = 0
		                   and b.delete_flag = 0
		                   <if test="user_name!=null and user_name!=''">
								and b.user_name like '%${user_name}%'
						   </if>
						   <if test="province!=null and province!=''">
								and b.province like '%${province}%'
						   </if>
						   <if test="city!=null and city!=''">
								and b.city like '%${city}%'
						   </if>
						    and specialty_name is not null
						   and to_char(a.call_time ,'yyyy') = to_char(sysdate ,'yyyy')
		                   order by a.appraise_time desc) b
		         group by b.id, b.user_id, b.user_name, b.expert_num, b.district) c
		 group by c.user_id,
		          c.user_name,
		          c.expert_num,
		          c.district,
		          c.specialty_name
	</select>

	<!-- 查询专家库标记记录  分页 -->
	<select id="queryPageByExpertLibrary" parameterType="ExpertInfoEntity" resultType="ExpertInfoEntity">
		<![CDATA[
			SELECT E.USER_ID, E.USER_NAME, E.EXPERT_NUM,(TO_CHAR(SYSDATE, 'YYYY') - TO_CHAR(E.BIRTHDAY, 'YYYY')) AGE, C.CALL_TIME, C.ILLEGALCOUNT, C.REASON, U.USER_NAME MANAGER FROM 
				T_EXPERT_INFO E
			LEFT JOIN (SELECT B.EXPERT_ID, MAX(CASE WHEN B.CALL_TIME IS NULL THEN APPRAISE_TIME ELSE B.CALL_TIME END) CALL_TIME, SUM(B.ILLEGALCOUNT) ILLEGALCOUNT, WM_CONCAT(REASON) REASON, MAX(CONDITION_ID) CONDITION_ID FROM
			  (SELECT A.EXPERT_ID, A.CALL_TIME, A.APPRAISE_TIME, A.ILLEGALCOUNT, A.REASON, MAX(ER.CONDITION_ID) CONDITION_ID FROM (
		]]>
		<if test="null != healthy and '' != healthy">
		<![CDATA[
			  SELECT EXPERT_ID, MAX(CALL_TIME) CALL_TIME, NULL APPRAISE_TIME, 0 ILLEGALCOUNT, REASON FROM T_EXTRACT_RESULT
			    WHERE REASON = #{healthy} AND JOIN_STATUS = 1
			    GROUP BY EXPERT_ID, REASON
		]]>
		</if>
		<if test="null != healthy and '' != healthy and null != illegalCount and '' != illegalCount">
		<![CDATA[
			  UNION ALL
		]]>
		</if>
		<if test="null != illegalCount">
		<![CDATA[
			  SELECT EXPERT_ID, NULL CALL_TIME, MAX(APPRAISE_TIME) APPRAISE_TIME, COUNT(ID) ILLEGALCOUNT, NULL REASON FROM T_EXTRACT_RESULT
			    WHERE ISILLEGAL = 1
			    HAVING COUNT(ID) >= 
		]]>
				<if test="null == illegalCount">
					0
				</if>
				<if test="null != illegalCount">
					#{illegalCount}
				</if>
		<![CDATA[
			    GROUP BY EXPERT_ID
		]]>
		</if>
		<![CDATA[
			    ) A
			    LEFT JOIN T_EXTRACT_RESULT ER ON A.EXPERT_ID = ER.EXPERT_ID 
			    AND ((A.APPRAISE_TIME IS NULL AND A.CALL_TIME = ER.CALL_TIME) OR (A.CALL_TIME IS NULL AND A.APPRAISE_TIME = ER.APPRAISE_TIME))
			    GROUP BY A.EXPERT_ID, A.CALL_TIME, A.APPRAISE_TIME, A.ILLEGALCOUNT, A.REASON) B
			  GROUP BY B.EXPERT_ID) C ON C.EXPERT_ID = E.USER_ID 
			  LEFT JOIN T_CONDITION CO ON C.CONDITION_ID = CO.ID
			  LEFT JOIN T_PROJECT P ON P.PROJECT_ID = CO.PROJECT_ID
			  LEFT JOIN TS_USER U ON U.USER_ID = P.CREATE_USER
			  WHERE E.DELETE_FLAG = 0 AND E.STATUS = 3
			        AND (TO_CHAR(SYSDATE, 'YYYY') - TO_CHAR(BIRTHDAY, 'YYYY') >= #{age}
			        OR (TO_CHAR(SYSDATE, 'YYYY') - TO_CHAR(BIRTHDAY, 'YYYY') < #{age} AND C.ILLEGALCOUNT IS NOT NULL))
		]]>
		ORDER BY E.USER_ID, C.CALL_TIME DESC NULLS LAST
	</select>

	<!-- 查询专家库标记记录 无分页 -->
	<select id="queryExpertLibraryList" parameterType="ExpertInfoEntity" resultType="ExpertInfoEntity">
		<![CDATA[
			SELECT E.USER_ID, E.USER_NAME, E.EXPERT_NUM,(TO_CHAR(SYSDATE, 'YYYY') - TO_CHAR(E.BIRTHDAY, 'YYYY')) AGE, C.CALL_TIME, C.ILLEGALCOUNT, C.REASON, U.USER_NAME MANAGER FROM 
				T_EXPERT_INFO E
			LEFT JOIN (SELECT B.EXPERT_ID, MAX(CASE WHEN B.CALL_TIME IS NULL THEN APPRAISE_TIME ELSE B.CALL_TIME END) CALL_TIME, SUM(B.ILLEGALCOUNT) ILLEGALCOUNT, WM_CONCAT(REASON) REASON, MAX(CONDITION_ID) CONDITION_ID FROM
			  (SELECT A.EXPERT_ID, A.CALL_TIME, A.APPRAISE_TIME, A.ILLEGALCOUNT, A.REASON, MAX(ER.CONDITION_ID) CONDITION_ID FROM (
		]]>
		<if test="null != healthy and '' != healthy">
		<![CDATA[
			  SELECT EXPERT_ID, MAX(CALL_TIME) CALL_TIME, NULL APPRAISE_TIME, 0 ILLEGALCOUNT, REASON FROM T_EXTRACT_RESULT
			    WHERE REASON = #{healthy} AND JOIN_STATUS = 1
			    GROUP BY EXPERT_ID, REASON
		]]>
		</if>
		<if test="null != healthy and '' != healthy and null != illegalCount and '' != illegalCount">
		<![CDATA[
			  UNION ALL
		]]>
		</if>
		<if test="null != illegalCount">
		<![CDATA[
			  SELECT EXPERT_ID, NULL CALL_TIME, MAX(APPRAISE_TIME) APPRAISE_TIME, COUNT(ID) ILLEGALCOUNT, NULL REASON FROM T_EXTRACT_RESULT
			    WHERE ISILLEGAL = 1
			    HAVING COUNT(ID) >= 
		]]>
				<if test="null == illegalCount">
					0
				</if>
				<if test="null != illegalCount">
					#{illegalCount}
				</if>
		<![CDATA[
			    GROUP BY EXPERT_ID
		]]>
		</if>
		<![CDATA[
			    ) A
			    LEFT JOIN T_EXTRACT_RESULT ER ON A.EXPERT_ID = ER.EXPERT_ID 
			    AND ((A.APPRAISE_TIME IS NULL AND A.CALL_TIME = ER.CALL_TIME) OR (A.CALL_TIME IS NULL AND A.APPRAISE_TIME = ER.APPRAISE_TIME))
			    GROUP BY A.EXPERT_ID, A.CALL_TIME, A.APPRAISE_TIME, A.ILLEGALCOUNT, A.REASON) B
			  GROUP BY B.EXPERT_ID) C ON C.EXPERT_ID = E.USER_ID 
			  LEFT JOIN T_CONDITION CO ON C.CONDITION_ID = CO.ID
			  LEFT JOIN T_PROJECT P ON P.PROJECT_ID = CO.PROJECT_ID
			  LEFT JOIN TS_USER U ON U.USER_ID = P.CREATE_USER
			  WHERE E.DELETE_FLAG = 0 AND E.STATUS = 3
			        AND (TO_CHAR(SYSDATE, 'YYYY') - TO_CHAR(BIRTHDAY, 'YYYY') >= #{age}
			        OR (TO_CHAR(SYSDATE, 'YYYY') - TO_CHAR(BIRTHDAY, 'YYYY') < #{age} AND C.ILLEGALCOUNT IS NOT NULL))
		]]>
		ORDER BY E.USER_ID, C.CALL_TIME DESC NULLS LAST
	</select>

	<!-- 查询专家库标记记录只查询年龄  分页 -->
	<select id="queryPageByExpertByAge" parameterType="ExpertInfoEntity" resultType="ExpertInfoEntity">
		<![CDATA[
			SELECT E.USER_ID, E.USER_NAME, E.EXPERT_NUM, (TO_CHAR(SYSDATE, 'YYYY' )-TO_CHAR(BIRTHDAY, 'YYYY' )) AGE FROM T_EXPERT_INFO E
			WHERE E.DELETE_FLAG=0 AND E.STATUS=3
		]]>
		<if test="null != age and 0 != age">
			<![CDATA[
			AND TO_CHAR(SYSDATE, 'YYYY' )-TO_CHAR(BIRTHDAY, 'YYYY' ) >= #{age}
			]]>
		</if>
		<![CDATA[
		ORDER BY E.USER_ID
		]]>
	</select>

	<!-- 查询专家库标记记录只查询年龄 无分页 -->
	<select id="queryExpertListByAge" parameterType="ExpertInfoEntity" resultType="ExpertInfoEntity">
		<![CDATA[
			SELECT E.USER_ID, E.USER_NAME, E.EXPERT_NUM, (TO_CHAR(SYSDATE, 'YYYY' )-TO_CHAR(BIRTHDAY, 'YYYY' )) AGE FROM T_EXPERT_INFO E
			WHERE E.DELETE_FLAG=0 AND E.STATUS=3
		]]>
		<if test="null != age and 0 != age">
			<![CDATA[
			AND TO_CHAR(SYSDATE, 'YYYY' )-TO_CHAR(BIRTHDAY, 'YYYY' ) >= #{age}
			]]>
		</if>
		<![CDATA[
		ORDER BY E.USER_ID
		]]>
	</select>
	<!-- 更新专家状态（一年周期内扣分） -->
	<update id="modifyExpertStatusByScore">
		update t_expert_info set status = 13 where id in (select extract_result_id from t_appraise2  where score &lt;= #{score})
	</update>
	<!-- 更新专家状态byId -->
	<update id="modifyExpertStatusById">
		update t_expert_info set status = 13 where id = #{id}
	</update>

	<!--查询专家基本信息  -->
	<select id="queryExpertInfoList" resultType="ExpertInfoEntity">
		select a.id,a.user_id,a.user_name,a.eval_score,a.pause_number,a.pause_startTime,a.pause_endTime from t_expert_info a  
		where a.delete_flag = 0 and a.status = #{status} 
	</select>

	<!--查询所有的专家  -->
	<select id="queryAllExpertInfo" resultType="ExpertInfoEntity">
		select a.id,a.user_id,a.user_name,a.eval_score,a.pause_number,a.pause_startTime,a.pause_endTime from t_expert_info a  
		where a.delete_flag = 0 
	</select>
	<!-- 查询所有的专家身份证号 -->
	<select id="queryAllExpertIdNo" resultType="ExpertInfoEntity">
	select a.id,a.user_id,a.user_name,a.ID_TYPE,a.ID_NO,a.is_qual,a.check_msg from t_expert_info a  
		where a.delete_flag = 0 and a.is_qual is null and a.check_msg is null
	</select>
	<!-- 根据主键修改专家身份证校验结果 -->
	<update id="modifyExpertIdNo" parameterType="ExpertInfoEntity">
		update t_expert_info a SET a.is_qual = #{is_qual},a.check_msg = #{check_msg}
		where a.id = #{id}
	</update>

	<!--暂停专家  -->
	<update id="modifyExpertAssessment" parameterType="ExpertInfoEntity">
		update t_expert_info a SET a.modify_time=sysdate,eval_score=#{eval_score}
			<if test="null != status and  ''!= status">
				,a.status=#{status}
			</if>
			<if test="null != pause_startTime and  ''!= pause_startTime">
				,a.pause_startTime =#{pause_startTime}
			</if>
			<if test="null != pause_endTime and  ''!= pause_endTime">
				,a.pause_endTime=#{pause_endTime}
			</if>
			<if test="null != pause_number and  ''!= pause_number">
				,a.pause_number=#{pause_number}
			</if>

			where a.id  = #{id}
	</update>

	<!--跨年更新专家的分值  -->
	<update id="updateAllExpertScore" parameterType="ExpertInfoEntity">
		update t_expert_info a SET a.modify_time=sysdate,a.eval_score=#{eval_score},A.PAUSE_NUMBER =#{pause_number}
	</update>
	
	<!--奖励专家  -->
	<update id="modifyExpertScore" parameterType="ExpertInfoEntity">
		update t_expert_info a SET a.eval_score=#{eval_score},a.modify_time=sysdate  where a.id  = #{id}
	</update>
	
	
	<!--修改暂停专家的状态  -->
	<update id="updateExpertStatusByDate" parameterType="java.lang.String">
		update t_expert_info a SET a.status=8,a.IS_BID=0,START_BID_DATE='',END_BID_DATE=''
		WHERE 
		<![CDATA[TO_CHAR(A.END_BID_DATE,'yyyy-MM-dd')<=#{date}]]>
	</update>


	<!-- 查根据抽取记录ID查询专家信息 -->
	<select id="queryExpertByResultId" parameterType="String" resultType="ExpertInfoEntity">
		select a.id,a.eval_score,a.user_name,a.user_id,a.pause_number from t_expert_info a 
		left join t_extract_result b on b.expert_id = a.user_id
		where b.id=#{id}
	</select>

	<!-- 根据手机号码查询指定的专家 -->
	<select id="queryExpertByMobile" parameterType="java.lang.String" resultType="ExpertInfoEntity">
    	select * from t_expert_info t where t.mobilephone=#{mobile}
    </select>
    <!-- 根据批次号和专家id更新不参加理由和状态 -->
    <update id="updateExpertByIdAndBatch" parameterType="ResultEntity">
    	update T_EXTRACT_RESULT t set t.reason=#{reason},t.join_status=#{joinStatus}
    	where t.expert_id=#{userId} and t.decimationbatch=#{decimationBatch}
    </update>

   	<!-- 查询所有专家所属机构 -->
	<select id="queryExpertCompany" parameterType="ExpertInfoEntity" resultType="ExpertInfoEntity">
    	select distinct t.COMPANY from t_expert_info t where t.delete_flag=0 and t.COMPANY LIKE '%${company}%'
    </select>

       	<!-- 查询所有专家所属机构 -->
	<select id="queryExpertCode" parameterType="ExpertInfoEntity" resultType="ExpertInfoEntity">
    	select t.id_no,t.user_name,t.mobilephone from t_expert_info t where t.delete_flag=0

    	<if test="null != id_no and  ''!= id_no">
    	and t.id_no LIKE '%${id_no}%'
    	</if>
    	<if test="null != mobilephone and  ''!= mobilephone">
    	and t.mobilephone LIKE '%${mobilephone}%'
    	</if>

    </select>

	<select id="queryPageExpertInfoCompleteList" parameterType="ExpertSupplementInfoEntity" resultType="ExpertSupplementInfoEntity">
		select t.id,
		t.user_id userId,
		t.remark,
		t.user_name userName,
		t.mobilephone mobilePhone,
		(case when t.province='320000'then 0 else 1 end)isProv,
		t.is_basic_info isBasicInfo,
		t.is_certificate_info isCertificateInfo,
		tt.EXPERT_TYPE expertType
		from t_Expert_Info t
		left join T_TECHNICAL_TITAL tt on t.technical_tital = tt.code
		<where>
			<if test="userName != null and userName !=''">
				and 	instr(t.user_name,#{userName})>0
			</if>
			<if test="mobilePhone != null and mobilePhone != ''">
				and  instr(t.mobilephone,#{mobilePhone})>0
			</if>
			<if test="isProv != null and isProv!=''">
				<if test="isProv == '1' ">
					and 	t.province = '320000'
				</if>
				<if test="isProv == '0' ">
					and 	t.province != '320000'
				</if>
			</if>
			<if test="isBasicInfo != null and isBasicInfo != ''">
				and t.is_basic_info = #{isBasicInfo}
			</if>
			<if test="isCertificateInfo != null and isCertificateInfo !=''">
				and t.is_certificate_info = #{isCertificateInfo}
			</if>
			<if test="expertGrade !=null and expertGrade != '' ">
				<if test="expertGrade =='0000' ">
					and (tt.EXPERT_TYPE <![CDATA[<>]]> 1 or tt.EXPERT_TYPE is null)
				</if>
				<if test="expertGrade == '1000' ">
					and tt.EXPERT_TYPE = '1'
				</if>
			</if>
			<if test="isPerfect != null and isPerfect !='' ">
				<if test="isPerfect == '1000'">
					and (t.is_basic_info !='1' or t.is_certificate_info !='1')
				</if>
			</if>
			and t.STATUS in ('3','8')
		</where>
		order by t.MODIFY_TIME
	</select>

	<select id="queryExpertInfoCompleteList" parameterType="ExpertSupplementInfoEntity" resultType="ExpertSupplementInfoEntity">
		select t.id,
		t.user_id userId,
		t.user_name userName,
		t.mobilephone mobilePhone,
		t.remark,
		(case when t.province='320000'then 0 else 1 end)isProv,
		t.is_basic_info isBasicInfo,
		t.is_certificate_info isCertificateInfo,
		tt.EXPERT_TYPE expertType
		from t_Expert_Info t
		left join T_TECHNICAL_TITAL tt on t.technical_tital = tt.code
		<where>
			<if test="userName != null and userName !=''">
				and 	instr(t.user_name,#{userName})>0
			</if>
			<if test="mobilePhone != null and mobilePhone != ''">
				and  instr(t.mobilephone,#{mobilePhone})>0
			</if>
			<if test="isProv != null and isProv!=''">
				<if test="isProv == '1' ">
					and 	t.province = '320000'
				</if>
				<if test="isProv == '0' ">
					and 	t.province != '320000'
				</if>
			</if>
			<if test="isBasicInfo != null and isBasicInfo != ''">
				and t.is_basic_info = #{isBasicInfo}
			</if>
			<if test="isCertificateInfo != null and isCertificateInfo !=''">
				and t.is_certificate_info = #{isCertificateInfo}
			</if>
			<if test="expertGrade !=null and expertGrade != '' ">
				<if test="expertGrade =='0000' ">
					and (tt.EXPERT_TYPE <![CDATA[<>]]> 1 or tt.EXPERT_TYPE is null)
				</if>
				<if test="expertGrade == '1000' ">
					and tt.EXPERT_TYPE = '1'
				</if>
			</if>
			<if test="isPerfect != null and isPerfect !='' ">
				<if test="isPerfect == '1000'">
					and (t.is_basic_info !='1' or t.is_certificate_info !='1')
				</if>
			</if>
			and t.STATUS in ('3','8')
		</where>
		order by t.MODIFY_TIME
	</select>
	
	<!-- 专家出库审核页面查询列表 -->
	<select id="queryPageExpertOutList" resultType="ExperOutEntity">
		select 
			M.majorName major,t.AUDIT_TIME auditTime,t.CITY,t.PROVINCE,t.ENTER_FLAG enterFlag,u.user_name checkName, t.DEALOUT_TIME dealOutTime,t.CHECK_TIME checkTime,t.CHECK_ID checkId,t.USER_ID userId,t.EXPERT_NUM expertNum,t.USER_NAME userName,t.MOBILEPHONE mobilePhone,t.EXPERT_TYPE expertType,
			t.EVAL_SCORE evalScore,t.DISTRICT,t.birthday,t.OUT_REASON outReason,
			t.status,t.OUT_STATUS outStatus
		from T_EXPERT_INFO t left join ts_user u
		on t.check_Id=u.user_id
		LEFT JOIN (SELECT USER_ID,wmsys.wm_concat(f.spe_name) majorName FROM T_EXPERT_MAJOR M1 LEFT JOIN t_specialty_info f ON  m1.major=f.spe_id
                      WHERE M1.MAJOR IS NOT NULL
                      AND M1.delete_flag = 0
                      GROUP BY USER_ID) M
    	ON T.USER_ID = M.USER_ID
		where t.DELETE_FLAG=0
		
		<if test="outStatus!=null and outStatus!=''">
				
				and t.OUT_STATUS =#{outStatus}
		</if>
		<if test="userName!=null and userName!=''">
				AND INSTR(t.USER_NAME,#{userName})>0
		</if>
		<if test="mobilePhone!=null and mobilePhone!=''">
				and t.MOBILEPHONE =#{mobilePhone}
		</if>
		<if test="expertType!=null and expertType!=''">
				and t.EXPERT_TYPE =#{expertType}
		</if>
		<if test="outReason!=null and outReason!=''">
				AND INSTR(t.OUT_REASON,#{outReason})>0
		</if>
		<if test="province!=null and province!=''">
				AND INSTR(t.province,#{province})>0
		</if>
		<if test="city!=null and city!=''">
				AND INSTR(t.city,#{city})>0
		</if>
		<if test="startTime!=null">
			and t.AUDIT_TIME>=#{startTime}
		</if>
		<if test="endTime!=null">
			and t.AUDIT_TIME<![CDATA[<=]]>#{endTime}
		</if>
		order by t.create_time desc
		
	</select>
	<!-- 专家出库审核页面查询列表 -->
	<select id="queryPageOutList" resultType="ExperOutEntity">
		select 
			M.majorName major,t.AUDIT_TIME auditTime,t.CITY,t.PROVINCE,t.ENTER_FLAG enterFlag,t.USER_ID userId,t.EXPERT_NUM expertNum,t.USER_NAME userName,t.MOBILEPHONE mobilePhone,t.EXPERT_TYPE expertType,
			t.EVAL_SCORE evalScore,t.DISTRICT,t.birthday,t.OUT_REASON outReason,
			t.status,t.OUT_STATUS outStatus
		from T_EXPERT_INFO t 
		LEFT JOIN (SELECT USER_ID,wmsys.wm_concat(f.spe_name) majorName FROM T_EXPERT_MAJOR M1 LEFT JOIN t_specialty_info f ON  m1.major=f.spe_id
                      WHERE M1.MAJOR IS NOT NULL
                      AND M1.delete_flag = 0
                      GROUP BY USER_ID) M
    	ON T.USER_ID = M.USER_ID
		where t.DELETE_FLAG=0
		and t.status=9
		<if test="outReason!=null and outReason!=''">
			<if test="outReason == 4">
				and t.out_status is null
			</if>
			<if test="outReason != 4">
				and t.OUT_STATUS ='20'
				AND INSTR(t.OUT_REASON,#{outReason})>0
			</if>
				
		</if>
		<if test="userName!=null and userName!=''">
				AND INSTR(t.USER_NAME,#{userName})>0
		</if>
		<if test="mobilePhone!=null and mobilePhone!=''">
				and t.MOBILEPHONE =#{mobilePhone}
		</if>
		<if test="expertType!=null and expertType!=''">
				and t.EXPERT_TYPE =#{expertType}
		</if>

		<if test="province!=null and province!=''">
				AND INSTR(t.province,#{province})>0
		</if>
		<if test="city!=null and city!=''">
				AND INSTR(t.city,#{city})>0
		</if>
		<if test="startTime!=null">
			and t.AUDIT_TIME>=#{startTime}
		</if>
		<if test="endTime!=null">
			and t.AUDIT_TIME<![CDATA[<=]]>#{endTime}
		</if>
		order by t.create_time desc
		
	</select>
	<!-- 专家出库审核页面查询列表 -->
	<select id="queryExpertOutList" resultType="ExperOutEntity">
		select 
			M.majorName major,t.AUDIT_TIME auditTime,t.CITY,t.PROVINCE,t.ENTER_FLAG enterFlag,u.user_name checkName, t.DEALOUT_TIME dealOutTime,t.CHECK_TIME checkTime,t.CHECK_ID checkId,t.USER_ID userId,t.EXPERT_NUM expertNum,t.USER_NAME userName,t.MOBILEPHONE mobilePhone,t.EXPERT_TYPE expertType,
			t.EVAL_SCORE evalScore,t.DISTRICT,t.birthday,t.OUT_REASON outReason,
			t.status,t.OUT_STATUS outStatus
		from T_EXPERT_INFO t left join ts_user u
		on t.check_Id=u.user_id
		LEFT JOIN (SELECT USER_ID,wmsys.wm_concat(f.spe_name) majorName FROM T_EXPERT_MAJOR M1 LEFT JOIN t_specialty_info f ON  m1.major=f.spe_id
                      WHERE M1.MAJOR IS NOT NULL
		              AND M1.delete_flag = 0
                      GROUP BY USER_ID) M
    	ON T.USER_ID = M.USER_ID
		where t.DELETE_FLAG=0
		
		<if test="outStatus!=null and outStatus!=''">
				and t.OUT_STATUS =#{outStatus}
		</if>
		<if test="userName!=null and userName!=''">
				and t.USER_NAME =#{userName}
		</if>
		<if test="mobilePhone!=null and mobilePhone!=''">
				and t.MOBILEPHONE =#{mobilePhone}
		</if>
		<if test="outReason!=null and outReason!=''">
				AND INSTR(t.OUT_REASON,#{outReason})>0
		</if>
		<if test="province!=null and province!=''">
				AND INSTR(t.province,#{province})>0
		</if>
		<if test="city!=null and city!=''">
				AND INSTR(t.city,#{city})>0
		</if>
		<if test="startTime!=null">
			and t.AUDIT_TIME>=#{startTime}
		</if>
		<if test="endTime!=null">
			and t.AUDIT_TIME<![CDATA[<=]]>#{endTime}
		</if>
		order by t.create_time desc
		
	</select>
	<!-- 专家出库-根据专家id查询待开标项目 -->
	<select id="queryProjectByExpert"   resultType="ProjectEntity">
		SELECT PROJECT_NAME projectName,PROJECT_NO projectNo,MANAGER,AGENT,BID_TIME bidTime,BID_ADDRESS bidAddress,t.DECIMATIONBATCH
		 FROM t_project t LEFT JOIN t_extract_result e ON t.decimationbatch=e.decimationbatch
		WHERE t.bid_time>SYSDATE
		AND e.join_status=0
		AND e.expert_id=#{userId}
	</select>
	
	<!--更新专家出库状态  -->
	<update id="updateExpertOutStatus" parameterType="ExperOutEntity">
		update t_expert_info te 
		set te.CHECK_ID=#{checkId},
		<if test="outStatus!=null and outStatus=='20'">te.status=9,te.out_time=sysdate,</if>
		 
		te.CHECK_TIME=sysdate,te.OUT_STATUS=#{outStatus} where
		te.user_id in
		<foreach collection="userIds" item="item" index="index" open="(" close=")" separator=",">
		#{item}
        </foreach>
	</update>
	<!--专家出库专家重新入库-->
	<update id="updateExpertOutIn" parameterType="ExperOutEntity">
		update t_expert_info te 
		set 
		te.status=3,
		te.out_time=null,
		te.out_reason=null,
		te.OUT_STATUS='40',
		te.CHECK_TIME=null,
		te.CHECK_ID=null
		where
		te.user_id in
		<foreach collection="userIds" item="item" index="index" open="(" close=")" separator=",">
		#{item}
        </foreach>
	</update>
</mapper>
