package com.hzw.auth.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
public class AuthConfig {
    @Value("${weChat.auth.appId:}")
    private String appId;

    @Value("${weChat.auth.secret:}")
    private String secret;

    @Value("${weChat.auth.tokenUrl:}")
    private String tokenUrl;

    @Value("${weChat.auth.grantType:}")
    private String grantType;

    @Value("${weChat.auth.prePic:}")
    private String prePic;

    @Value("${weChat.auth.templatePath:}")
    private String templatePath;

    @Value("${weChat.auth.requestUrl:}")
    private String requestUrl;

}
