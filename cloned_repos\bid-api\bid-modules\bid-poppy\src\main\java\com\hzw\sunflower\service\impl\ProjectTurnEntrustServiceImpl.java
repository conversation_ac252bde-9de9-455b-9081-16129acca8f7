package com.hzw.sunflower.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzw.sunflower.client.WorkFlowApiClient;
import com.hzw.sunflower.common.Paging;
import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.constant.EntrustConstants;
import com.hzw.sunflower.constant.ReturnCodeConstants;
import com.hzw.sunflower.constant.constantenum.TurnEntrustEnum;
import com.hzw.sunflower.controller.project.request.ApprovalApplyReq;
import com.hzw.sunflower.controller.project.request.ProjectTurnEntrustREQ;
import com.hzw.sunflower.controller.project.response.ProjectTurnEntrustVo;
import com.hzw.sunflower.controller.request.AppingTaskREQ;
import com.hzw.sunflower.controller.response.AppingTaskVO;
import com.hzw.sunflower.dao.ProjectTurnEntrustMapper;
import com.hzw.sunflower.dto.ProcessRecordDTO;
import com.hzw.sunflower.entity.JwtUser;
import com.hzw.sunflower.entity.ProjectEntrustUser;
import com.hzw.sunflower.entity.ProjectTurnEntrust;
import com.hzw.sunflower.entity.User;
import com.hzw.sunflower.entity.condition.ProjectTurnEntrustCondition;
import com.hzw.sunflower.exception.ExceptionEnum;
import com.hzw.sunflower.exception.SunFlowerException;
import com.hzw.sunflower.exception.WorkFlowException;
import com.hzw.sunflower.freesia.client.constant.FlowClientConstant;
import com.hzw.sunflower.freesia.client.vo.flowable.ReturnVo;
import com.hzw.sunflower.freesia.client.vo.flowable.task.TaskRoleVo;
import com.hzw.sunflower.security.utils.SecurityUtils;
import com.hzw.sunflower.service.*;
import com.hzw.sunflower.util.BeanListUtil;
import com.hzw.sunflower.util.WorkFlowExceptionUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

import static com.hzw.sunflower.security.utils.SecurityUtils.getJwtUser;


/**
 * 项目转委托人表Service接口实现
 *
 * <AUTHOR>
 * @version 1.0.0 2021-04-14
 */
@Service
public class ProjectTurnEntrustServiceImpl extends ServiceImpl<ProjectTurnEntrustMapper, ProjectTurnEntrust> implements ProjectTurnEntrustService {

    @Autowired
    private ProjectEntrustUserService projectEntrustUserService;
    @Autowired
    private ProjectService projectService;

    @Autowired
    private WorkFlowApiClient flowApiClient;

    @Autowired
    private ProcessRecordService processRecordService;

    @Autowired
    private WorkflowCacheService workflowCacheService;


    /**
     * 新增转委托信息
     * @param projectTurnEntrustReq
     * @return
     * @throws Exception
     */
    @Override
    public Boolean saveTurnEntrust(ProjectTurnEntrustREQ projectTurnEntrustReq){
        Boolean flag = true;
        JwtUser jwtUser = SecurityUtils.getJwtUser();
        //转委托信息
        ProjectTurnEntrust turnEntrust = new ProjectTurnEntrust();
        turnEntrust.setProjectId(projectTurnEntrustReq.getProjectId());
        //现负责人
        turnEntrust.setDesUserId(projectTurnEntrustReq.getDesUserId());
        turnEntrust.setDesDepartId(projectTurnEntrustReq.getDesDepartId());
        //原负责人
        turnEntrust.setSrcUserId(projectTurnEntrustReq.getSrcUserId());
        turnEntrust.setCompanyId(projectTurnEntrustReq.getCompanyId());
        turnEntrust.setCreatedUserId(projectTurnEntrustReq.getDesUserId());
        turnEntrust.setStatus(TurnEntrustEnum.PENDING_REVIEW.getType());
        turnEntrust.setSubmitTime(new Date());
        turnEntrust.setSubmitter(jwtUser.getUserId());
        // 是否存在数据
        LambdaQueryWrapper<ProjectTurnEntrust> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjectTurnEntrust::getProjectId,projectTurnEntrustReq.getProjectId())
                .eq(ProjectTurnEntrust::getSrcUserId,projectTurnEntrustReq.getSrcUserId())
                .eq(ProjectTurnEntrust::getStatus,TurnEntrustEnum.PENDING_REVIEW.getType());
        ProjectTurnEntrust one2 = this.getOne(queryWrapper);
        if(BeanUtil.isNotEmpty(one2)){
            throw new SunFlowerException(ExceptionEnum.AGENT_PROJECT_TURN_ENTRUSTING,ExceptionEnum.AGENT_PROJECT_TURN_ENTRUSTING.getMessage());
        }
        List<Integer> list = new ArrayList<>();
        list.add(TurnEntrustEnum.RETURNED.getType());
        list.add(TurnEntrustEnum.WITHDRAWN.getType());
        LambdaQueryWrapper<ProjectTurnEntrust> queryWrapper1 = new LambdaQueryWrapper<>();
        queryWrapper1.eq(ProjectTurnEntrust::getProjectId,projectTurnEntrustReq.getProjectId())
                .eq(ProjectTurnEntrust::getSrcUserId,projectTurnEntrustReq.getSrcUserId())
                .in(ProjectTurnEntrust::getStatus,list);
        ProjectTurnEntrust one1 = this.getOne(queryWrapper1);
        if(BeanUtil.isNotEmpty(one1)){
            turnEntrust.setId(one1.getId());
        }

        if (null != projectTurnEntrustReq.getId()) {
            ProjectTurnEntrust byId = this.getById(projectTurnEntrustReq.getId());
            if(BeanUtil.isNotEmpty(byId)){
                turnEntrust.setId(byId.getId());
            }
        }

        this.saveOrUpdate(turnEntrust);
        // 两个人存在相同处室，跳过审批
        Map<String, Object> variables = new HashMap<>(16);
        List<Long> ids = this.baseMapper.findDeptId(projectTurnEntrustReq.getSrcUserId());
        List<Long> deptIds = this.baseMapper.findDeptId(projectTurnEntrustReq.getDesUserId());
        Integer size = ids.stream()
                .map(i -> deptIds.stream().filter(s -> Objects.nonNull(i) && Objects.nonNull(s) && Objects.equals(i, s)).findAny().orElse(null))
                .filter(Objects::nonNull)
                .collect(Collectors.toList())
                .size();
        if(size > 0){
          variables.put("type",1);
        }else{
          variables.put("type",0);
        }
        //开启流程引擎
        //获取存入信息（id）
        LambdaQueryWrapper<ProjectTurnEntrust> queryWrapper2 = new LambdaQueryWrapper<>();
        queryWrapper2.eq(ProjectTurnEntrust::getProjectId,projectTurnEntrustReq.getProjectId())
                .eq(ProjectTurnEntrust::getDesUserId,projectTurnEntrustReq.getDesUserId())
                .eq(ProjectTurnEntrust::getSrcUserId,projectTurnEntrustReq.getSrcUserId());
        ProjectTurnEntrust one = this.getOne(queryWrapper);
        String string = flowApiClient.startProcessInstanceByKey(jwtUser.getUserOtherId(), FlowClientConstant.AGENT_PROJECT_TURN_ENTRUST, "代理机构项目转委托", EntrustConstants.PROJECT_TURN_ENTRUST + one.getId(), variables);
        // 判断推送到流程是否成功
        WorkFlowExceptionUtil.checkFlowResult(string);
        ReturnVo vo = JSONObject.parseObject(string, ReturnVo.class);
        if (vo != null && ReturnCodeConstants.PROCESS_SUCCESS_CODE.equals(vo.getCode())) {
            String data = (String) vo.getData();
            one.setProcessInstanceId(data);
            // 如果流程结束了，则状态为已确认
            if ("end".equals(vo.getMsg())) {
                one.setStatus(TurnEntrustEnum.AGREED.getType());
                //更新用户委托关系表
                ProjectEntrustUser entrustUser = projectEntrustUserService.getEntrustUser(projectTurnEntrustReq.getSrcUserId(), projectTurnEntrustReq.getProjectId());
                entrustUser.setUserId(projectTurnEntrustReq.getDesUserId());
                entrustUser.setDepartmentId(projectTurnEntrustReq.getDesDepartId());
                entrustUser.setUpdatedUserId(projectTurnEntrustReq.getDesUserId());
                flag = projectEntrustUserService.updateById(entrustUser);
                //修改项目表的沉郁字段
                if (flag) {
                    projectService.updateAgentProjectRedundantFieldDealWith(projectTurnEntrustReq.getProjectId());
                }
            }
            this.updateById(one);
        } else {
            throw new WorkFlowException(ReturnCodeConstants.JSTCC_CODE_EXCEPTION, vo.getMsg());
        }
        //插入流程记录
        ProcessRecordDTO record = new ProcessRecordDTO();
        record.setBusinessCode(FlowClientConstant.AGENT_PROJECT_TURN_ENTRUST);
        record.setBusinessId(one.getId());
        record.setOperation(TurnEntrustEnum.SUBMIT.getDesc());
        record.setOperatorId(jwtUser.getUserId());
        record.setOperatorName(jwtUser.getUserName());
        flag = processRecordService.addProcessRecord(record, jwtUser.getUserOtherId(), string);
        return flag;

    }

    @Override
    public Boolean insertApprovalRecordAgent(ApprovalApplyReq req, ProjectTurnEntrust entrust) {
        ProcessRecordDTO record = new ProcessRecordDTO();
        // 保存审批记录
        record.setOperatorName(getJwtUser().getUserName());
        record.setOperatorId(getJwtUser().getUserId());
        record.setCreatedTime(new Date());
        // 填充审批意见，防止填充null
        if (StringUtils.isNoneEmpty(req.getApprovalOpinion())) {
            record.setRemark(req.getApprovalOpinion());
        } else {
            record.setRemark("");
        }
        // 提交流程引擎，判断是否成功，成功返回码为100
        req.setUserCode(getJwtUser().getUserOtherId());
        JSONObject jsonObject = applyApplicationApproval(req);
        if (!"100".equals(jsonObject.getString("code"))) {
            throw new WorkFlowException(ExceptionEnum.AGENT_PROJECT_TURN_ENTRUST.getCode(), jsonObject.getString("msg"));
        } else {
            //审批 根据角色判断状态
            if (req.getIsAgree()) {
                //查询下一步审批人
                String taskRole = flowApiClient.getTaskRoleByProcessInstanceId(null, req.getProcessInstanceId());
                JSONObject taskRoleJsonObject = JSON.parseObject(taskRole);
                List<TaskRoleVo> taskRoleVoList = JSONObject.parseArray(taskRoleJsonObject.getString("data"), TaskRoleVo.class);
                if(null != taskRoleVoList) {

                }else{
                    entrust.setStatus(TurnEntrustEnum.AGREED.getType());
                }
                record.setBusinessCode(FlowClientConstant.AGENT_PROJECT_TURN_ENTRUST);
                record.setBusinessId(req.getApplicationId());
                record.setOperation(TurnEntrustEnum.AGREE.getDesc());
                this.updateById(entrust);
            } else {
                entrust.setStatus(TurnEntrustEnum.RETURNED.getType());
                record.setBusinessCode(FlowClientConstant.AGENT_PROJECT_TURN_ENTRUST);
                record.setBusinessId(req.getApplicationId());
                record.setOperation(TurnEntrustEnum.RETURN.getDesc());
                this.updateById(entrust);
            }

            processRecordService.addProcessRecord(record, getJwtUser().getUserOtherId(), jsonObject.toString());
            if ("end".equals(jsonObject.getString("msg"))) {
                //更新用户委托关系表
                ProjectEntrustUser entrustUser = projectEntrustUserService.getEntrustUser(entrust.getSrcUserId(), entrust.getProjectId());
                entrustUser.setDepartmentId(entrust.getDesDepartId());
                entrustUser.setUserId(entrust.getDesUserId());
                entrustUser.setUpdatedUserId(entrust.getDesUserId());
                Boolean flag = projectEntrustUserService.updateById(entrustUser);
                //修改项目表的沉郁字段
                if (flag) {
                    projectService.updateAgentProjectRedundantFieldDealWith(entrust.getProjectId());
                }
            }
        }
        return true;
    }

    @Override
    public Boolean withdrawTurnEntrust(Long id) {
        ProjectTurnEntrust entrust = this.getById(id);
        insertWithdrawExpertFee(entrust);
        entrust.setStatus(TurnEntrustEnum.WITHDRAWN.getType());
        Boolean flag = this.updateById(entrust);
        return flag;
    }

    @Override
    public Result<Object> listWorkBench(ProjectTurnEntrustCondition condition) {
        String processDefinitionKey = FlowClientConstant.AGENT_PROJECT_TURN_ENTRUST;
        // 获取代办列表
        AppingTaskREQ req =new AppingTaskREQ();
        req.setProcessDefinitionKey(processDefinitionKey);
        req.setPage(condition.getPage().intValue());
        req.setPageSize(condition.getPageSize().intValue());
        String userOtherId= SecurityUtils.getJwtUser().getUserOtherId();
        Paging<AppingTaskVO> paging = workflowCacheService.getPaddingRemoteList(req,userOtherId);
        List<AppingTaskVO> allData=paging.getRecords();
        // 数据转换
        List<ProjectTurnEntrustVo> workBenchListVos = queryProjectTurnEntrustVoList(allData);
        // 分页
        Paging<ProjectTurnEntrustVo> pageList = filterPageList(condition,workBenchListVos);
        return Result.ok(pageList);
    }

    @Override
    public Result<IPage<ProjectTurnEntrustVo>> listTurnEntrust(ProjectTurnEntrustCondition condition) {
        IPage<ProjectTurnEntrustVo> page = condition.buildPage();
        JwtUser jwtUser = SecurityUtils.getJwtUser();
        IPage<ProjectTurnEntrustVo> list = this.baseMapper.listTurnEntrust(page,jwtUser);
        if(CollectionUtil.isNotEmpty(list.getRecords())){
            for (ProjectTurnEntrustVo vo : list.getRecords()) {
                vo.setProcessDefinitionKey(FlowClientConstant.AGENT_PROJECT_TURN_ENTRUST);
                List<User> user = this.baseMapper.findUser(vo.getProjectId());
                if(CollectionUtil.isNotEmpty(user)){
                    vo.setUser(user);
                }
                vo.setClientDept(this.baseMapper.findDeptName(vo.getId(), vo.getClientId()));
            }
        }
        return Result.ok(list);
    }

    @Override
    public Result<Integer> listWorkBenchCount(ProjectTurnEntrustCondition condition) {
        String processDefinitionKey = FlowClientConstant.AGENT_PROJECT_TURN_ENTRUST;
        // 获取代办列表
        AppingTaskREQ req =new AppingTaskREQ();
        req.setProcessDefinitionKey(processDefinitionKey);
        req.setPage(condition.getPage().intValue());
        req.setPageSize(condition.getPageSize().intValue());
        String userOtherId= SecurityUtils.getJwtUser().getUserOtherId();
        Paging<AppingTaskVO> paging = workflowCacheService.getPaddingRemoteList(req,userOtherId);
        Integer total = paging.getRecords().size();
        return Result.ok(null != total?total : 0);
    }

    /**
     * 向流程引擎提交审批
     */
    private JSONObject applyApplicationApproval(ApprovalApplyReq req){
        // 接入流程
        String result = flowApiClient.review(
                req.getUserCode(),
                req.getProcessInstanceId(),
                req.getTaskId(),
                req.getApprovalOpinion(),
                req.getIsAgree()
        );
        JSONObject jsonObject = JSON.parseObject(result);
        return jsonObject;
    }

    /**
     * 审批流撤回
     * @param entrust
     * @return true代表成功，false代表失败
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertWithdrawExpertFee(ProjectTurnEntrust entrust) {
        ProcessRecordDTO record =new ProcessRecordDTO();
        // 判断是否成功从流程引擎中撤回
        JSONObject jsonObject = noticeWithdraw(entrust, TurnEntrustEnum.WITHDRAW.getDesc());
        if(!jsonObject.getString("code").equals(ReturnCodeConstants.PROCESS_SUCCESS_CODE)){
            throw new WorkFlowException(ExceptionEnum.TURN_ENTRUST_WITHDRAW_FAILED.getCode(),jsonObject.getString("msg"));
        }else {
            entrust.setStatus(TurnEntrustEnum.WITHDRAWN.getType());
            record.setBusinessCode(FlowClientConstant.AGENT_PROJECT_TURN_ENTRUST);
            record.setBusinessId(entrust.getId());
            record.setOperation(TurnEntrustEnum.WITHDRAW.getDesc());
            record.setOperatorId(getJwtUser().getUserId());
            record.setOperatorName(getJwtUser().getUserName());
        }
        this.updateById(entrust);
        processRecordService.addProcessRecord(record,getJwtUser().getUserOtherId(),jsonObject.toString());
        return true;
    }

    /**
     * 调用流程引擎
     */
    private JSONObject noticeWithdraw(ProjectTurnEntrust entrust,String message){
        String userCode = getJwtUser().getUserOtherId();
        String businessKey = null;
        businessKey = EntrustConstants.PROJECT_TURN_ENTRUST + entrust.getId();
        // 调用流程引擎撤回
        String res = flowApiClient.revokeProcess(userCode, businessKey, message);
        JSONObject jsonObject = JSON.parseObject(res);
        return jsonObject;
    }

    /**
     * 工作台审批流数据处理
     * @param list
     * @return
     */
    private List<ProjectTurnEntrustVo> queryProjectTurnEntrustVoList( List<AppingTaskVO> list) {
        List<ProjectTurnEntrustVo> projectTurnEntrustVos = BeanListUtil.convertList(list, ProjectTurnEntrustVo.class);
        if(CollectionUtil.isNotEmpty(projectTurnEntrustVos)){
            for (ProjectTurnEntrustVo vo : projectTurnEntrustVos) {
                Integer index = vo.getBusinessKey().indexOf(":");
                String substring = vo.getBusinessKey().substring(index + 1);
                vo.setId(Long.parseLong(substring));
                ProjectTurnEntrustVo entrustVo = this.baseMapper.findInfo(vo.getId());
                if(BeanUtil.isNotEmpty(entrustVo)){
                    if(null != entrustVo.getCompanyName()){
                        vo.setCompanyName(entrustVo.getCompanyName());
                    }
                    vo.setProjectName(entrustVo.getProjectName());
                    vo.setSubmitter(entrustVo.getSubmitter());
                    vo.setClientName(entrustVo.getClientName());
                    vo.setStatus(entrustVo.getStatus());
                    vo.setSubmitTime(entrustVo.getSubmitTime());
                    List<User> user = this.baseMapper.findUser(entrustVo.getProjectId());
                    if(CollectionUtil.isNotEmpty(user)){
                        vo.setUser(user);
                    }
                    vo.setClientDept(this.baseMapper.findDeptName(vo.getId(),entrustVo.getClientId()));
                }
            }
        }
        return projectTurnEntrustVos;
    }

    /**
     * 审批列表接口分页检索实现
     * @param req
     * @param allData
     * @return
     */
    private Paging<ProjectTurnEntrustVo> filterPageList(ProjectTurnEntrustCondition req, List<ProjectTurnEntrustVo> allData) {
        Paging<ProjectTurnEntrustVo> paging = new Paging();
        paging.setPage(req.getPage().longValue());
        paging.setPageSize(req.getPageSize().longValue());
        List<ProjectTurnEntrustVo> list = new ArrayList<>();
        int pageNum = req.getPage().intValue();
        int pageSize = req.getPageSize().intValue();
        int startRow = (pageNum - 1) * pageSize;
        long rowCount = 0L;
        // 组装返回list数据
        for (ProjectTurnEntrustVo taskVo : allData) {
            rowCount++;
            if (rowCount > startRow && list.size() < pageSize) {
                list.add(taskVo);
            }
        }
        // 设置总条数
        paging.setTotal(rowCount);
        // 设置返回记录
        paging.setRecords(list);
        return paging;
    }
}