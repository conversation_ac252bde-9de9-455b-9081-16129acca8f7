///***
/// * 年度项目交易总量
/// */

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:charts_flutter/flutter.dart' as charts;

import 'package:flutter_main_app/src/utils/FunctionUtils.dart';
import 'package:flutter_main_app/src/constants/theme.dart';
import 'package:flutter_main_app/src/constants/common.dart';

import 'package:flutter_main_app/src/viewModels/dataReport/DataReportViewModel.dart';

import 'package:flutter_main_app/src/models/dataReport/AnnualBidsectionModel.dart';

import 'package:flutter_main_app/src/components/charts/CustomBarChart.dart';

class ProjectTransaction extends StatelessWidget {

  ProjectTransaction();

  static const charts.Color _primaryBarColor = charts.Color(a: 255, r: 0x44, g: 0xc7, b: 0xcc);
  static const charts.Color _secondaryBarColor = charts.Color(a: 255, r: 0x51, g: 0x8a, b: 0xf7);

  @override
  Widget build(BuildContext context) {
    return Consumer<DataReportViewModel>(
      builder: (context, model, child) {

        final List<AnnualBidsectionModel> projectTransactionBidsectionData = [];

        final List<Map<String, dynamic>> projectTransaction = model.reportData?.projectTransaction ?? [];

        for (int i = 0; i < projectTransaction.length; i++) {
          final Map<String, dynamic> item = projectTransaction[i] ?? <String, dynamic>{};

          projectTransactionBidsectionData.add(
            AnnualBidsectionModel(
              name: item['name'] ?? defaultStringPlaceholder,
              count: item['count'] ?? 0,
              amount: FunctionUtils.convertYuanToWanYuan(item['amount'] ?? 0.00), /// ? 单位万元 ？
            ),
          );
        }

        final List<charts.Series<AnnualBidsectionModel, String>> projectTransactionBidsectionSeriesList =
          <charts.Series<AnnualBidsectionModel, String>>[
            charts.Series<AnnualBidsectionModel, String>(
              id: '标段个数',
              domainFn: (AnnualBidsectionModel bidsection, _) => bidsection.name,
              measureFn: (AnnualBidsectionModel bidsection, _) => bidsection.count,
              colorFn: (_, __) => _primaryBarColor,
              fillColorFn: (_, __) => _primaryBarColor,
              data: projectTransactionBidsectionData,
            ),
            charts.Series<AnnualBidsectionModel, String>(
              id: '标段金额',
              domainFn: (AnnualBidsectionModel bidsection, _) => bidsection.name,
              measureFn: (AnnualBidsectionModel bidsection, _) => bidsection.amount,
              colorFn: (_, __) => _secondaryBarColor,
              fillColorFn: (_, __) => _secondaryBarColor,
              data: projectTransactionBidsectionData,
            )..setAttribute(charts.measureAxisIdKey, 'secondaryMeasureAxisId'),
          ];

        return Container(
          padding: const EdgeInsets.only(left: 5.0, top: 30.0, right: 5.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              Padding(
                padding: const EdgeInsets.only(left: 10.0),
                child: Text(
                  '${model.year}年度项目交易总量',
                  style: themeTitleTextStyle,
                ),
              ),
              Padding(
                padding: const EdgeInsets.only(top: 10.0),
                child: SizedBox(
                  height: 273.0,
                  child: CustomBarChart(
                    seriesList: projectTransactionBidsectionSeriesList,
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

}
