///***
/// * 年度区域招标总体情况
/// */

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:charts_flutter/flutter.dart' as charts;

import 'package:flutter_main_app/src/utils/FunctionUtils.dart';
import 'package:flutter_main_app/src/constants/theme.dart';
import 'package:flutter_main_app/src/constants/common.dart';
import 'package:flutter_main_app/src/constants/region.dart';

import 'package:flutter_main_app/src/viewModels/dataReport/DataReportViewModel.dart';

import 'package:flutter_main_app/src/models/common/RegionModel.dart';
import 'package:flutter_main_app/src/models/dataReport/AnnualBidsectionModel.dart';

import 'package:flutter_main_app/src/components/charts/CustomBarChart.dart';

class RegionalTender extends StatelessWidget {

  RegionalTender();

  static const charts.Color _primaryBarColor = charts.Color(a: 255, r: 0x44, g: 0xc7, b: 0xcc);
  static const charts.Color _secondaryBarColor = charts.Color(a: 255, r: 0x51, g: 0x8a, b: 0xf7);

  @override
  Widget build(BuildContext context) {
    return Consumer<DataReportViewModel>(
      builder: (context, model, child) {

        final List<AnnualBidsectionModel> regionalBidsectionData = <AnnualBidsectionModel>[];

        final List<Map<String, dynamic>> regionalTender = model.reportData?.regionalTender ?? [];

        final List<Map<String, dynamic>> regionalTenderOfJiangSu = [];

        ///***
        /// * 将地区代码转换为地区名称
        /// * 补齐缺失的地区数据
        /// */
        for (int i = 0; i < jiangSuRegionList.length; i++) {
          final RegionModel jiangSuRegion = jiangSuRegionList[i];

          Map<String, dynamic> newRegion = {};

          for (int j = 0; j < regionalTender.length; j++) {
            final Map<String, dynamic> region = regionalTender[j] ?? {};

            final String code = region['name'] ?? '';

            if (jiangSuRegion.code.substring(0, 4) == code.substring(0, 4)) {
              newRegion = Map<String, dynamic>.from(region);

              break;
            }
          }

          newRegion['name'] = jiangSuRegion.name;

          regionalTenderOfJiangSu.add(newRegion);
        }

        for (int i = 0; i < regionalTenderOfJiangSu.length; i++) {
          final Map<String, dynamic> region = regionalTenderOfJiangSu[i] ?? {};

          regionalBidsectionData.add(
            AnnualBidsectionModel(
              name: region['name'] ?? defaultStringPlaceholder,
              count: region['count'] ?? 0,
              amount: FunctionUtils.convertYuanToWanYuan(region['amount'] ?? 0.00), /// ? 单位万元 ？
            ),
          );
        }

        final List<charts.Series<AnnualBidsectionModel, String>> regionalBidsectionSeriesList =
          <charts.Series<AnnualBidsectionModel, String>>[
            charts.Series<AnnualBidsectionModel, String>(
              id: '标段个数',
              domainFn: (AnnualBidsectionModel bidsection, _) => bidsection.name,
              measureFn: (AnnualBidsectionModel bidsection, _) => bidsection.count,
              colorFn: (_, __) => _primaryBarColor,
              fillColorFn: (_, __) => _primaryBarColor,
              data: regionalBidsectionData,
            ),
            charts.Series<AnnualBidsectionModel, String>(
              id: '标段金额',
              domainFn: (AnnualBidsectionModel bidsection, _) => bidsection.name,
              measureFn: (AnnualBidsectionModel bidsection, _) => bidsection.amount,
              colorFn: (_, __) => _secondaryBarColor,
              fillColorFn: (_, __) => _secondaryBarColor,
              data: regionalBidsectionData,
            )..setAttribute(charts.measureAxisIdKey, 'secondaryMeasureAxisId'),
          ];

        return Container(
          padding: const EdgeInsets.only(left: 5.0, top: 30.0, right: 5.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              Padding(
                padding: const EdgeInsets.only(left: 10.0),
                child: Text(
                  '${model.year}年度区域招标总体情况',
                  style: themeTitleTextStyle,
                ),
              ),
              Padding(
                padding: const EdgeInsets.only(top: 10.0),
                child: SizedBox(
                  height: 250.0,
                  child: CustomBarChart(
                    seriesList: regionalBidsectionSeriesList,
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

}
