package com.hzw.system.api.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.hzw.common.core.constant.CacheConstants;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Set;

/**
 * 用户信息
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class LoginUser implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    private String userId;
    /**
     * 企业D
     */
    private String companyId;

    /**
     * 企业类型
     */

    private Integer companyType;
    /**
     * 企业名称
     */
    private String companyName;
    /**
     * 手机号
     */
    private String phone;

    /**
     * 部门ID
     */
    private String deptId;

    /**
     * 部门名
     */
    private String deptName;

    /**
     * 用户唯一标识
     */
    private String token;

    /**
     * 用户类型
     */
    private String userType;

    /**
     * 登录时间
     */
    private Long loginTime;

    /**
     * 过期时间
     */
    private Long expireTime;

    /**
     * 登录IP地址
     */
    private String ipaddr;

    /**
     * 登录地点
     */
    private String loginLocation;

    /**
     * 登录状态
     */
    private int status;

    /**
     * 浏览器类型
     */
    private String browser;

    /**
     * 操作系统
     */
    private String os;

    /**
     * 菜单权限
     */
    private Set<String> menuPermission;


    /**
     * 角色权限
     */
    private Set<String> rolePermission;

    /**
     * 用户名
     */
    private String username;

    /**
     * 密码
     */
    @JsonIgnore
    private String password;

    /**
     * 用户是否设置密码 0：设置；1：未设置
     */
    private Integer PasswordSymbol;
    /**
     * 专家id
     */
    private String expertId;

    /**
     * 角色对象
     */
    private List<RoleDTO> roles;

    /**
     *  当前角色ID
     */
    private String roleId;

    /**
     * 用户身份（1.专家 2.业务员）
     */
    private Integer identity;

    /**
     * 审批状态
     */
    private Integer approveStatus;

    /**
     * 获取登录id
     */
    public String getLoginId() {
        if (userType == null) {
            throw new IllegalArgumentException("用户类型不能为空");
        }
        if (userId == null) {
            throw new IllegalArgumentException("用户ID不能为空");
        }
        return userType + CacheConstants.LOGINID_JOIN_CODE + userId;
    }

}
