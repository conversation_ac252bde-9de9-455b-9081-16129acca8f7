package com.hzw.ssm.expert.service;

import com.hzw.ssm.expert.dao.ExpertInfoMapper;
import com.hzw.ssm.expert.dao.RegisterMapper;
import com.hzw.ssm.expert.entity.ExpertAuditEntity;
import com.hzw.ssm.expert.entity.ExpertExperienceEntity;
import com.hzw.ssm.expert.entity.ExpertInfoEntity;
import com.hzw.ssm.expert.entity.ExpertMajorEntity;
import com.hzw.ssm.expert.entity.ExpertPracticeEntity;
import com.hzw.ssm.expert.entity.ExpertUpdateRecordEntity;
import com.hzw.ssm.expert.entity.SpecialtyCouInfoEntity;
import com.hzw.ssm.expert.entity.SpecialtyInfoEntity;
import com.hzw.ssm.fw.base.BaseService;
import com.hzw.ssm.fw.util.CommUtil;
import com.hzw.ssm.fw.util.MD5Util;
import com.hzw.ssm.fw.util.StringUtil;
import com.hzw.ssm.fw.util.SysConstants;
import com.hzw.ssm.fw.util.XLSExport;
import com.hzw.ssm.sys.project.entity.ResultEntity;
import com.hzw.ssm.sys.user.dao.UserMapper;
import com.hzw.ssm.sys.user.entity.UserEntity;
import com.hzw.ssm.util.FileTypeCheck;
import com.hzw.ssm.util.string.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.util.Region;
import org.apache.struts2.ServletActionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 专家信息服务类
 *
 * <AUTHOR> 2014-10-09
 */
@Service
public class ExpertInfoService extends BaseService {

    @Autowired
    private ExpertInfoMapper expertInfoMapper;

    @Autowired
    private RegisterMapper registerMapper;

    @Autowired
    private UserMapper userMapper;


    //@Value("${FilePath}")
    //private String file_Path;

    /**
     * 获取专家基本信息
     *
     * @param user_id(用户表id) 专家用户id(专家基本信息表id)
     * @return
     */
    public ExpertInfoEntity getExpertInfoByUserIdOrId(ExpertInfoEntity colExpertInfo) {

        return expertInfoMapper.getExpertInfoByUserIdOrId(colExpertInfo);
    }

    /**
     * 保存专家基本信息
     *
     * @param expertInfoEntity
     */
    @Transactional
    public void saveExpertInfo(ExpertInfoEntity expertInfoEntity) {
        if (expertInfoEntity.getId() == null || expertInfoEntity.getId().equals("")) {
            UserEntity userEntity = userMapper.selectUserById(expertInfoEntity.getUser_id());
            expertInfoEntity.setId(CommUtil.getKey());
            //专家状态
            expertInfoEntity.setDelete_flag(0);
            //专家等级 1:普通专家  2:高级专家
            expertInfoEntity.setGrade(1L);
            expertInfoEntity.setModify_time(new Date());
            expertInfoEntity.setCreate_time(new Date());
            expertInfoEntity.setReferrer_qrcode(userEntity.getRecommendCode());
            expertInfoMapper.saveExpertInfo(expertInfoEntity);
        } else {
            expertInfoEntity.setModify_time(new Date());
            expertInfoMapper.updateExpertInfo(expertInfoEntity);
        }
        // 更新当前用户的名称
        UserEntity ue = new UserEntity();
        ue.setUser_id(expertInfoEntity.getUser_id());
        ue.setUser_name(expertInfoEntity.getUser_name());
        userMapper.updatePersonnalUser(ue);
    }

    /**
     * 专家信息录入
     *
     * @param expertInfoEntity
     * @param practiceOneList
     * @param majorOneList
     * @param experienceOneList
     */
    @Transactional
    public void addExpertInfo(ExpertInfoEntity expertInfoEntity, List<ExpertPracticeEntity> practiceList, List<ExpertMajorEntity> majorList, List<ExpertExperienceEntity> experienceList) {
        UserEntity user = new UserEntity();
        user.setUser_id(expertInfoEntity.getUser_id());
        user.setLogin_code(expertInfoEntity.getLogin_code());
        user.setUser_name(expertInfoEntity.getUser_name());
        user.setPassword(MD5Util.reverse(expertInfoEntity.getPassword()));
        user.setMobile(expertInfoEntity.getMobilephone());
        user.setEmail(expertInfoEntity.getEmail());
        user.setCreate_id(expertInfoEntity.getCreate_id());
        user.setCreateTime(new Date());
        user.setRole_id(SysConstants.ROLE_ID.EXPERT);//专家
        registerMapper.doRegister(user);

        expertInfoEntity.setId(CommUtil.getKey());
        expertInfoEntity.setModify_time(new Date());
        expertInfoEntity.setAudit_time(new Date());
        expertInfoEntity.setCreate_time(new Date());
        expertInfoEntity.setExpert_num(CreateExpertNum());
        expertInfoEntity.setDelete_flag(0);
        expertInfoMapper.saveExpertInfo(expertInfoEntity);

        this.updateExpertPractice(practiceList, user.getUser_id());

        this.updateExpertMajorInfo(majorList, user.getUser_id());

        this.updateExpertExperience(experienceList, user.getUser_id());

        //将数据同步到数据备份表
        this.expertInfoSyncIntoBakTable(expertInfoEntity);

    }

    /**
     * 生成专家编号
     *
     * @return
     */
    private String CreateExpertNum() {
        String expert_num = "";
        String maxExpertNum = expertInfoMapper.getMaxExpertNum();
        if (maxExpertNum != null && maxExpertNum != "") {
            int no = Integer.parseInt(maxExpertNum) + 1;
            expert_num = new DecimalFormat("00000").format(no);
        } else {
            expert_num = "00001";
        }
        return expert_num;
    }

    /**
     * 专家基本修改
     *
     * @param expertInfoEntity
     */
    public void updateExpertInfo(ExpertInfoEntity expertInfoEntity) {
        expertInfoMapper.updateExpertInfo(expertInfoEntity);
    }

    /**
     * 添加专家执业资格
     *
     * @param expertPracticeEntity
     */
    public void updateExpertPractice(List<ExpertPracticeEntity> practiceList, String user_id) {
        for (int i = 0; i < practiceList.size(); i++) {
            ExpertPracticeEntity expertPracticeEntity = practiceList.get(i);
            if (expertPracticeEntity.getId() == null || expertPracticeEntity.getId().equals("")) {
                expertPracticeEntity.setId(CommUtil.getKey());
                expertPracticeEntity.setUser_id(user_id);
                expertPracticeEntity.setDelete_flag(0);
                expertInfoMapper.addExpertPractice(expertPracticeEntity);
            } else {
                expertInfoMapper.updateExpertPractice(expertPracticeEntity);
            }
        }
    }

    /**
     * 查询执业资格
     *
     * @param user_id
     * @return
     */
    public List<ExpertPracticeEntity> queryPracticeByUserId(String user_id) {
        return expertInfoMapper.queryPracticeByUserId(user_id);
    }

    /**
     * 专家评标专业列表
     *
     * @param user_id
     * @return
     */
    public List<ExpertMajorEntity> queryMajorByUserId(String user_id) {
        return expertInfoMapper.queryMajorByUserId(user_id);
    }

    /**
     * 查询评标专业集合
     *
     * @return
     */
    public List<SpecialtyInfoEntity> querySpecialtyInfoList() {
        return expertInfoMapper.querySpecialtyInfoList();
    }

    /**
     * 查询国家评标专业集合
     *
     * @return
     */
    public List<SpecialtyCouInfoEntity> querySpecialtyCouInfoList() {
        return expertInfoMapper.querySpecialtyCouInfoList();
    }

    /**
     * 修改专家评标专业
     *
     * @param majorInfoList
     */
    public void updateExpertMajorInfo(List<ExpertMajorEntity> majorInfoList, String user_id) {
        for (int i = 0; i < majorInfoList.size(); i++) {
            ExpertMajorEntity major = majorInfoList.get(i);
            if (major.getId() == null || major.getId().equals("")) {
                major.setId(CommUtil.getKey());
                major.setUser_id(user_id);
                major.setDelete_flag(0);
                expertInfoMapper.addExpertMajorInfo(major);
            } else {
                if (StringUtils.isEmpty(major.getMajor())) {
                    major.setGetTime("");
                }
                expertInfoMapper.updateExpertMajorInfo(major);
            }
        }
    }

    /**
     * 修改专家工作经历
     *
     * @param experienceList
     * @param user_id
     */
    public void updateExpertExperience(List<ExpertExperienceEntity> experienceList, String user_id) {
        if (experienceList == null) {
            experienceList = new ArrayList<ExpertExperienceEntity>();
        }
        for (int i = 0; i < experienceList.size(); i++) {
            ExpertExperienceEntity experience = experienceList.get(i);
            if (experience == null) {
                continue;
            }
            if (experience.getId() == null || experience.getId().equals("")) {
                experience.setId(CommUtil.getKey());
                experience.setUser_id(user_id);
                experience.setDelete_flag(0);
                expertInfoMapper.addExpertExperience(experience);
            } else {
                expertInfoMapper.updateExpertExperience(experience);
            }
        }
    }

    /**
     * 查询专家工作经历列表
     *
     * @param user_id
     * @return
     */
    public List<ExpertExperienceEntity> queryExpertExperienceList(String user_id) {
        return expertInfoMapper.queryExpertExperienceList(user_id);
    }

    /**
     * 删除专家工作经历
     *
     * @param id
     */
    public void deleteExpertExperienceById(String id) {
        expertInfoMapper.deleteExpertExperienceById(id);
    }

    /**
     * 查询审批意见实体类，查询一条最新的退回原因
     *
     * @param user_id
     * @return
     */
    public ExpertAuditEntity getExpertAuditEntityByUserId(String user_id) {
        return expertInfoMapper.getExpertAuditEntityByUserId(user_id);
    }

    /**
     * 专家信息管理列表
     *
     * @param colExpertInfo
     * @return
     */
    public List<ExpertInfoEntity> queryPageExpertInfoManagerList(ExpertInfoEntity colExpertInfo) {
        if (colExpertInfo.getProvince() != null && colExpertInfo.getProvince().equals("0")) {
            colExpertInfo.setProvince(null);
        }
        if (colExpertInfo.getCity() != null && colExpertInfo.getCity().equals("0")) {
            colExpertInfo.setCity(null);
        }
        return expertInfoMapper.queryPageExpertInfoManagerList(colExpertInfo);
    }

    /**
     * 更新专家状态，3：未暂停，4：暂停资格 ，7：黑名单
     *
     * @param expertInfoEntity
     */
    @Transactional
    public void updateExpertStatus(ExpertInfoEntity expertInfoEntity) {
        expertInfoMapper.updateExpertStatus(expertInfoEntity);
        expertInfoMapper.updateExpertBakStatus(expertInfoEntity);

        //修改批次编号
        int maxBeachNum = expertInfoMapper.selectMaxBatchNumFromRecord(expertInfoEntity.getUser_id());
        if (maxBeachNum == 0) {
            maxBeachNum = 1;
        } else {
            maxBeachNum++;
        }
        String user_id = expertInfoEntity.getUser_id();
        String modify_role = expertInfoEntity.getModify_role();
        String modify_user = expertInfoEntity.getModify_user();
        String modify_reason = expertInfoEntity.getModify_reason();

        if (expertInfoEntity.getStatus() == 3L) {
            if (expertInfoEntity.getOldStatus() == 4L) {
                //addExpertUpdateRecord(user_id,"专家状态","资格暂停","未暂停",modify_role,modify_user,modify_reason,maxBeachNum);
                addExpertUpdateRecord(user_id, "专家状态", "资格暂停", "正常", modify_role, modify_user, modify_reason, maxBeachNum);
            } else if (expertInfoEntity.getOldStatus() == 7L) {
                //addExpertUpdateRecord(user_id,"专家状态","加入黑名单","未暂停",modify_role,modify_user,modify_reason,maxBeachNum);
                addExpertUpdateRecord(user_id, "专家状态", "加入黑名单", "正常", modify_role, modify_user, modify_reason, maxBeachNum);
            }
        } else if (expertInfoEntity.getStatus() == 4L) {
            //addExpertUpdateRecord(user_id,"专家状态","未暂停","资格暂停",modify_role,modify_user,modify_reason,maxBeachNum);
            addExpertUpdateRecord(user_id, "专家状态", "正常", "资格暂停", modify_role, modify_user, modify_reason, maxBeachNum);
        } else if (expertInfoEntity.getStatus() == 7L) {
            if (expertInfoEntity.getOldStatus() == 3L) {
                //addExpertUpdateRecord(user_id,"专家状态","未暂停","加入黑名单",modify_role,modify_user,modify_reason,maxBeachNum);
                addExpertUpdateRecord(user_id, "专家状态", "正常", "加入黑名单", modify_role, modify_user, modify_reason, maxBeachNum);
            } else if (expertInfoEntity.getOldStatus() == 4L) {
                addExpertUpdateRecord(user_id, "专家状态", "已暂停", "加入黑名单", modify_role, modify_user, modify_reason, maxBeachNum);
            }
        } else if (expertInfoEntity.getStatus() == 9L) {
            addExpertUpdateRecord(user_id, "专家状态", "正常", "出库", modify_role, modify_user, modify_reason, maxBeachNum);
        }
    }

    /**
     * 专家信息修改管理类
     *
     * @param expertInfoEntity
     */
    @Transactional
    public void compareExpertInfoManager(ExpertInfoEntity expertOne, List<ExpertPracticeEntity> practiceOneList, List<ExpertMajorEntity> majorNewList, List<ExpertExperienceEntity> experienceOneList) {
        //修改批次编号
        int maxBeachNum = expertInfoMapper.selectMaxBatchNumFromRecord(expertOne.getUser_id());
        if (maxBeachNum == 0) {
            maxBeachNum = 1;
        } else {
            maxBeachNum++;
        }

        String user_id = expertOne.getUser_id();
        String modify_role = expertOne.getModify_role();
        String modify_user = expertOne.getModify_user();
        String modify_reason = expertOne.getModify_reason();

        //基本信息比较
        ExpertInfoEntity expertTwo = expertInfoMapper.getExpertInfoBakByUserIdOrId(expertOne);
        if (expertOne != null && !"".equals(expertOne) && expertTwo != null && !"".equals(expertTwo)) {
            if (!expertOne.getUser_name().equals(expertTwo.getUser_name())) {
                addExpertUpdateRecord(user_id, "姓名", expertTwo.getUser_name(), expertOne.getUser_name(), modify_role, modify_user, modify_reason, maxBeachNum);
                // 更新当前用户的名称
                UserEntity ue = new UserEntity();
                ue.setUser_id(expertOne.getUser_id());
                ue.setUser_name(expertOne.getUser_name());
                userMapper.updatePersonnalUser(ue);
            }
            if (!expertOne.getSex().equals(expertTwo.getSex())) {
                addExpertUpdateRecord(user_id, "性别", expertTwo.getSex().equals("1") ? "男" : "女", expertOne.getSex().equals("1") ? "男" : "女", modify_role, modify_user, modify_reason, maxBeachNum);
            }

            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            if (expertOne.getBirthday() != null && !sdf.format(expertOne.getBirthday()).equals(sdf.format(expertTwo.getBirthday()))) {
                addExpertUpdateRecord(user_id, "出生年月", sdf.format(expertTwo.getBirthday()), sdf.format(expertOne.getBirthday()), modify_role, modify_user, modify_reason, maxBeachNum);
            }
            if (!StringUtil.changeNullToSpace(expertOne.getPolitics()).trim().equals(StringUtil.changeNullToSpace(expertTwo.getPolitics()).trim())) {
                addExpertUpdateRecord(user_id, "政治面貌", expertTwo.getPolitics(), expertOne.getPolitics(), modify_role, modify_user, modify_reason, maxBeachNum);
            }

            if (!expertOne.getId_type().equals(expertTwo.getId_type())) {
                Map<String, String> idMap = new HashMap<String, String>();
                idMap.put("1", "身份证");
                idMap.put("2", "警官证");
                idMap.put("3", "军人证");
                idMap.put("4", "其他");
                String one = idMap.get(expertOne.getId_type());
                String two = idMap.get(expertTwo.getId_type());
                addExpertUpdateRecord(user_id, "证件类型", two, one, modify_role, modify_user, modify_reason, maxBeachNum);
            }
            if (!expertOne.getId_no().equals(expertTwo.getId_no())) {
                addExpertUpdateRecord(user_id, "证件号码", expertTwo.getId_no(), expertOne.getId_no(), modify_role, modify_user, modify_reason, maxBeachNum);
            }

            if (!StringUtil.changeNullToSpace(expertOne.getId_fileid()).equals(StringUtil.changeNullToSpace(expertTwo.getId_fileid()))) {
                StringBuffer oneStr = new StringBuffer("");
                if (expertOne.getOld_id() != null && !"".equals(expertOne.getOld_id())) {
                    oneStr.append("<a style=\"color:blue\" onclick=\"javascript:picPreview(530,450,'/GZZJK/" + expertOne.getId_fileid() + "')\">" + expertOne.getOld_id() + "</a>");
                } else {
                    oneStr.append("无");
                }
                StringBuffer twoStr = new StringBuffer("");
                if (expertTwo.getOld_id() != null && !"".equals(expertTwo.getOld_id())) {
                    twoStr.append("<a style=\"color:blue\" onclick=\"javascript:picPreview(530,450,'/GZZJK/" + expertTwo.getId_fileid() + "')\">" + expertTwo.getOld_id() + "</a>");
                } else {
                    twoStr.append("无");
                }
                addExpertUpdateRecord(user_id, "身份证复印件", twoStr.toString(), oneStr.toString(), modify_role, modify_user, modify_reason, maxBeachNum);
            }

            if (!StringUtil.changeNullToSpace(expertOne.getPhoto_fileid()).equals(StringUtil.changeNullToSpace(expertTwo.getPhoto_fileid()))) {
                StringBuffer oneStr = new StringBuffer("");
                if (expertOne.getOld_photo() != null && !"".equals(expertOne.getOld_photo())) {
                    oneStr.append("<a style=\"color:blue\" onclick=\"javascript:picPreview(530,450,'/GZZJK/" + expertOne.getPhoto_fileid() + "')\">" + expertOne.getOld_photo() + "</a>");
                } else {
                    oneStr.append("无");
                }
                StringBuffer twoStr = new StringBuffer("");
                if (expertTwo.getOld_photo() != null && !"".equals(expertTwo.getOld_photo())) {
                    twoStr.append("<a style=\"color:blue\" onclick=\"javascript:picPreview(530,450,'/GZZJK/" + expertTwo.getPhoto_fileid() + "')\">" + expertTwo.getOld_photo() + "</a>");
                } else {
                    twoStr.append("无");
                }
                addExpertUpdateRecord(user_id, "证件照片上传", twoStr.toString(), oneStr.toString(), modify_role, modify_user, modify_reason, maxBeachNum);
            }

//            if (!StringUtil.changeNullToSpace(expertOne.getSchool()).trim().equals(StringUtil.changeNullToSpace(expertTwo.getSchool()).trim())) {
//                addExpertUpdateRecord(user_id, "毕业院校", expertTwo.getSchool(), expertOne.getSchool(), modify_role, modify_user, modify_reason, maxBeachNum);
//            }
//
//            if (!StringUtil.changeNullToSpace(expertOne.getMajor()).trim().equals(StringUtil.changeNullToSpace(expertTwo.getMajor()).trim())) {
//                addExpertUpdateRecord(user_id, "所学专业", expertTwo.getMajor(), expertOne.getMajor(), modify_role, modify_user, modify_reason, maxBeachNum);
//            }

//            if (!StringUtil.changeNullToSpace(expertOne.getCertificate_fileid()).equals(StringUtil.changeNullToSpace(expertTwo.getCertificate_fileid()))) {
//                StringBuffer oneStr = new StringBuffer("");
//                if (expertOne.getOld_certificate() != null && !"".equals(expertOne.getOld_certificate())) {
//                    oneStr.append("<a style=\"color:blue\" onclick=\"javascript:picPreview(530,450,'/GZZJK/" + expertOne.getCertificate_fileid() + "')\">" + expertOne.getOld_certificate() + "</a>");
//                } else {
//                    oneStr.append("无");
//                }
//                StringBuffer twoStr = new StringBuffer("");
//                if (expertTwo.getOld_technical() != null && !"".equals(expertTwo.getOld_certificate())) {
//                    twoStr.append("<a style=\"color:blue\" onclick=\"javascript:picPreview(530,450,'/GZZJK/" + expertTwo.getCertificate_fileid() + "')\">" + expertTwo.getOld_certificate() + "</a>");
//                } else {
//                    twoStr.append("无");
//                }
//                addExpertUpdateRecord(user_id, "毕业证复印件", twoStr.toString(), oneStr.toString(), modify_role, modify_user, modify_reason, maxBeachNum);
//            }

//            if (!StringUtil.changeNullToSpace(expertOne.getTechnical_filed()).equals(StringUtil.changeNullToSpace(expertTwo.getTechnical_filed()))) {
//                StringBuffer oneStr = new StringBuffer("");
//                if (expertOne.getOld_technical() != null && !"".equals(expertOne.getOld_technical())) {
//                    oneStr.append("<a style=\"color:blue\" onclick=\"javascript:picPreview(530,450,'/GZZJK/" + expertOne.getTechnical_filed() + "')\">" + expertOne.getOld_technical() + "</a>");
//                } else {
//
//                }
//                StringBuffer twoStr = new StringBuffer("");
//                if (expertTwo.getOld_technical() != null && !"".equals(expertTwo.getOld_technical())) {
//                    twoStr.append("<a style=\"color:blue\" onclick=\"javascript:picPreview(530,450,'/GZZJK/" + expertTwo.getTechnical_filed() + "')\">" + expertTwo.getOld_technical() + "</a>");
//                } else {
//                    twoStr.append("无");
//                }
//                addExpertUpdateRecord(user_id, "职称证书印件上传", twoStr.toString(), oneStr.toString(), modify_role, modify_user, modify_reason, maxBeachNum);
//            }

//            if (!expertOne.getEducations().equals(expertTwo.getEducations())) {
//                Map<String, String> eduMap = new HashMap<String, String>();
//                eduMap.put("-1", "待查");
//                eduMap.put("1", "初中");
//                eduMap.put("2", "高中");
//                eduMap.put("3", "大专");
//                eduMap.put("4", "本科");
//                eduMap.put("5", "硕士");
//                eduMap.put("6", "博士");
//                addExpertUpdateRecord(user_id, "最高学历", eduMap.get(expertTwo.getEducations()), eduMap.get(expertOne.getEducations()), modify_role, modify_user, modify_reason, maxBeachNum);
//            }

//            if (!expertOne.getDegree().equals(expertTwo.getDegree())) {
//                Map<String, String> degreeMap = new HashMap<String, String>();
//                degreeMap.put("-1", "待查");
//                degreeMap.put("1", "学士");
//                degreeMap.put("2", "硕士");
//                degreeMap.put("3", "博士");
//                addExpertUpdateRecord(user_id, "最高学位", degreeMap.get(expertTwo.getDegree()), degreeMap.get(expertOne.getDegree()), modify_role, modify_user, modify_reason, maxBeachNum);
//            }

//            if (!expertOne.getTechnical_tital().equals(expertTwo.getTechnical_tital())) {
//                Map<String, String> techMap = new HashMap<String, String>();
//                techMap.put("-1", "待查");
//                techMap.put("1", "研究员级高级工程师");
//                techMap.put("2", "高级工程师");
//                techMap.put("3", "工程师");
//                techMap.put("4", "高级建筑师");
//                techMap.put("5", "建筑师");
//                techMap.put("6", "高级经济师");
//                techMap.put("7", "经济师");
//                techMap.put("8", "高级会计师");
//                techMap.put("9", "中级会计师");
//                techMap.put("10", "会计师");
//                techMap.put("11", "高级审计师");
//                techMap.put("12", "审计师");
//                techMap.put("13", "高级技师");
//                techMap.put("14", "技师");
//                techMap.put("15", "教授");
//                techMap.put("16", "副教授");
//                techMap.put("17", "讲师");
//                techMap.put("18", "城市规划师");
//                techMap.put("19", "高级工艺美术师");
//                techMap.put("20", "工艺美术师");
//                techMap.put("21", "其他");
//                addExpertUpdateRecord(user_id, "职称", techMap.get(expertTwo.getTechnical_tital()), techMap.get(expertOne.getTechnical_tital()), modify_role, modify_user, modify_reason, maxBeachNum);
//            }

            if (!StringUtil.changeNullToSpace(expertOne.getPosition()).trim().equals(StringUtil.changeNullToSpace(expertTwo.getPosition()).trim())) {
                addExpertUpdateRecord(user_id, "职务", expertTwo.getPosition(), expertOne.getPosition(), modify_role, modify_user, modify_reason, maxBeachNum);
            }

            if (!expertOne.getGrade().equals(expertTwo.getGrade())) {
                Map<Long, String> gradeMap = new HashMap<Long, String>();
                gradeMap.put(1L, "地区");
                gradeMap.put(2L, "国家");
                addExpertUpdateRecord(user_id, "专家级别", gradeMap.get(expertTwo.getGrade()), gradeMap.get(expertOne.getGrade()), modify_role, modify_user, modify_reason, maxBeachNum);
            }

            if (!expertOne.getProvince().equals(expertTwo.getProvince()) || !expertOne.getCity().equals(expertTwo.getCity())) {
                addExpertUpdateRecord(user_id, "所在行政区域", expertTwo.getDistrict(), expertOne.getDistrict(), modify_role, modify_user, modify_reason, maxBeachNum);
            }

            if (!StringUtil.changeNullToSpace(expertOne.getCompany()).trim().equals(StringUtil.changeNullToSpace(expertTwo.getCompany()).trim())) {
                addExpertUpdateRecord(user_id, "工作单位", expertTwo.getCompany(), expertOne.getCompany(), modify_role, modify_user, modify_reason, maxBeachNum);
            }

            if (!StringUtil.changeNullToSpace(expertOne.getCompany_addr()).trim().equals(StringUtil.changeNullToSpace(expertTwo.getCompany_addr()).trim())) {
                addExpertUpdateRecord(user_id, "单位地址", expertTwo.getCompany_addr(), expertOne.getCompany_addr(), modify_role, modify_user, modify_reason, maxBeachNum);
            }

            if (!StringUtil.changeNullToSpace(expertOne.getCompany_phone()).equals(StringUtil.changeNullToSpace(expertTwo.getCompany_phone()))) {
                addExpertUpdateRecord(user_id, "单位电话", expertTwo.getCompany_phone(), expertOne.getCompany_phone(), modify_role, modify_user, modify_reason, maxBeachNum);
            }

            if (!StringUtil.changeNullToSpace(expertOne.getCompany_zipcode()).equals(StringUtil.changeNullToSpace(expertTwo.getCompany_zipcode()))) {
                addExpertUpdateRecord(user_id, "单位邮编", expertTwo.getCompany_zipcode(), expertOne.getCompany_zipcode(), modify_role, modify_user, modify_reason, maxBeachNum);
            }

            if (!StringUtil.changeNullToSpace(expertOne.getMobilephone()).equals(StringUtil.changeNullToSpace(expertTwo.getMobilephone()))) {
                addExpertUpdateRecord(user_id, "移动电话", expertTwo.getMobilephone(), expertOne.getMobilephone(), modify_role, modify_user, modify_reason, maxBeachNum);
            }

            if (!StringUtil.changeNullToSpace(expertOne.getEmail()).equals(StringUtil.changeNullToSpace(expertTwo.getEmail()))) {
                addExpertUpdateRecord(user_id, "电子邮箱", expertTwo.getEmail(), expertOne.getEmail(), modify_role, modify_user, modify_reason, maxBeachNum);
            }
            if (!StringUtil.changeNullToSpace(expertOne.getDocument_no()).equals(StringUtil.changeNullToSpace(expertTwo.getDocument_no()))) {
                addExpertUpdateRecord(user_id, "档案号", expertTwo.getDocument_no(), expertOne.getDocument_no(), modify_role, modify_user, modify_reason, maxBeachNum);
            }

            if (!StringUtil.changeNullToSpace(expertOne.getQq_num()).equals(StringUtil.changeNullToSpace(expertTwo.getQq_num()))) {
                addExpertUpdateRecord(user_id, "QQ号码", expertTwo.getQq_num(), expertOne.getQq_num(), modify_role, modify_user, modify_reason, maxBeachNum);
            }

            if (!StringUtil.changeNullToSpace(expertOne.getSpecial_skill()).trim().equals(StringUtil.changeNullToSpace(expertTwo.getSpecial_skill()).trim())) {
                addExpertUpdateRecord(user_id, "学术专长与研究方向", expertTwo.getSpecial_skill(), expertOne.getSpecial_skill(), modify_role, modify_user, modify_reason, maxBeachNum);
            }

            if (!StringUtil.changeNullToSpace(expertOne.getBid_experience()).trim().equals(StringUtil.changeNullToSpace(expertTwo.getBid_experience()).trim())) {
                addExpertUpdateRecord(user_id, "评标经验", expertTwo.getBid_experience(), expertOne.getBid_experience(), modify_role, modify_user, modify_reason, maxBeachNum);
            }

            if (!StringUtil.changeNullToSpace(expertOne.getTraining_experience()).trim().equals(StringUtil.changeNullToSpace(expertTwo.getTraining_experience()).trim())) {
                addExpertUpdateRecord(user_id, "培训经历", expertTwo.getTraining_experience(), expertOne.getTraining_experience(), modify_role, modify_user, modify_reason, maxBeachNum);
            }

            if (!StringUtil.changeNullToSpace(expertOne.getRemark()).trim().equals(StringUtil.changeNullToSpace(expertTwo.getRemark()).trim())) {
                addExpertUpdateRecord(user_id, "其他说明", expertTwo.getRemark(), expertOne.getRemark(), modify_role, modify_user, modify_reason, maxBeachNum);
            }

        }
        if (expertOne.getTechnical_filed() != null && expertOne.getId_fileid() != null && expertOne.getCertificate_fileid() != null && expertOne.getICBackFileId() != null && expertOne.getICBackName() != null) {
            expertOne.setIsCertificateInfo("1");
        } else {
            expertOne.setIsCertificateInfo("0");
        }
        expertInfoMapper.updateExpertInfo(expertOne);

        //执业资格比较
        List<ExpertPracticeEntity> practiceTwoList = expertInfoMapper.queryPracticeBakByUserId(expertOne.getUser_id());
        for (int i = 0; i < practiceOneList.size(); i++) {
            ExpertPracticeEntity one = practiceOneList.get(i);
            ExpertPracticeEntity two = null;
            if (i >= practiceTwoList.size()) {
                two = new ExpertPracticeEntity();
            } else {
                two = practiceTwoList.get(i);
            }

            if (one.getId().equals(two.getId())) {
                if (!StringUtil.changeNullToSpace(one.getCertificate()).trim().equals(StringUtil.changeNullToSpace(two.getCertificate()).trim()) || !StringUtil.changeNullToSpace(one.getCertificate_no()).trim().equals(StringUtil.changeNullToSpace(two.getCertificate_no()).trim()) || !StringUtil.changeNullToSpace(one.getCertificate_fileid()).equals(StringUtil.changeNullToSpace(two.getCertificate_fileid()))) {
                    StringBuffer oneStr = new StringBuffer();
                    oneStr.append("执业资格:" + StringUtil.changeNullToSpace(one.getCertificate()) != null && !"".equals(StringUtil.changeNullToSpace(one.getCertificate())) ? StringUtil.changeNullToSpace(one.getCertificate()) + "<br/>" : "无" + "<br/>");
                    oneStr.append("执业资格证书号:" + StringUtil.changeNullToSpace(one.getCertificate_no()) != null && !"".equals(StringUtil.changeNullToSpace(one.getCertificate_no())) ? StringUtil.changeNullToSpace(one.getCertificate_no()) + "<br/>" : "无" + "<br/>");
                    oneStr.append("执业资格证书复印件:");
                    if (one.getOld_certificate() != null && !"".equals(one.getOld_certificate())) {
                        oneStr.append("<a style=\"color:blue\" onclick=\"javascript:picPreview(530,450,'/GZZJK/" + one.getCertificate_fileid() + "')\">" + one.getOld_certificate() + "</a>");
                    } else {
                        oneStr.append("无");
                    }

                    StringBuffer twoStr = new StringBuffer();
                    twoStr.append("执业资格:" + StringUtil.changeNullToSpace(two.getCertificate()) != null && !"".equals(StringUtil.changeNullToSpace(two.getCertificate())) ? StringUtil.changeNullToSpace(two.getCertificate()) + "<br/>" : "无" + "<br/>");
                    twoStr.append("执业资格证书号:" + StringUtil.changeNullToSpace(two.getCertificate_no()) != null && !"".equals(StringUtil.changeNullToSpace(two.getCertificate_no())) ? StringUtil.changeNullToSpace(two.getCertificate_no()) + "<br/>" : "无" + "<br/>");
                    twoStr.append("执业资格证书复印件:");
                    if (two.getOld_certificate() != null && !"".equals(two.getOld_certificate())) {
                        twoStr.append("<a style=\"color:blue\" onclick=\"javascript:picPreview(530,450,'/GZZJK/" + two.getCertificate_fileid() + "')\">" + two.getOld_certificate() + "</a>");
                    } else {
                        twoStr.append("无");
                    }
                    addExpertUpdateRecord(user_id, "执业资格" + (1 + i), twoStr.toString(), oneStr.toString(), modify_role, modify_user, modify_reason, maxBeachNum);

                    expertInfoMapper.updateExpertPractice(one);
                }
            } else {
                String certificate = one.getCertificate();
                if (!StringUtils.isEmpty(certificate)) {
                    one.setId(CommUtil.getKey());
                    one.setUser_id(user_id);
                    one.setDelete_flag(0);
                    expertInfoMapper.addExpertPractice(one);
                    StringBuffer oneStr = new StringBuffer();
                    oneStr.append("执业资格:" + StringUtil.changeNullToSpace(one.getCertificate()) != null && !"".equals(StringUtil.changeNullToSpace(one.getCertificate())) ? StringUtil.changeNullToSpace(one.getCertificate()) + "<br/>" : "无" + "<br/>");
                    oneStr.append("执业资格证书号:" + StringUtil.changeNullToSpace(one.getCertificate_no()) != null && !"".equals(StringUtil.changeNullToSpace(one.getCertificate_no())) ? StringUtil.changeNullToSpace(one.getCertificate_no()) + "<br/>" : "无" + "<br/>");
                    oneStr.append("执业资格证书复印件:");
                    if (one.getOld_certificate() != null && !"".equals(one.getOld_certificate())) {
                        oneStr.append("<a style=\"color:blue\" onclick=\"javascript:picPreview(530,450,'/GZZJK/" + one.getCertificate_fileid() + "')\">" + one.getOld_certificate() + "</a>");
                    } else {
                        oneStr.append("无");
                    }
                    addExpertUpdateRecord(user_id, "执业资格" + (1 + i), null, oneStr.toString(), modify_role, modify_user, modify_reason, maxBeachNum);
                }
            }
        }

        //评标专业比较()										  queryMajorByUserId
        List<ExpertMajorEntity> majorOldList = expertInfoMapper.queryMajorBakByUserId(expertOne.getUser_id());

        boolean flag = false;
        //外层循环数量比内层循环数量多
        List<ExpertMajorEntity> outerList = majorNewList;
        List<ExpertMajorEntity> innerList = majorOldList;
        if (majorNewList.size() >= majorOldList.size()) {
            //修改或者扩展专业
            flag = true;
        }

        for (int i = 0; i < outerList.size(); i++) {
            ExpertMajorEntity outer = outerList.get(i);
            ExpertMajorEntity inner = null;
            if (i >= innerList.size()) {
                inner = new ExpertMajorEntity();
            } else {
                inner = innerList.get(i);
            }


            //判断是否新增
            if (outer.getId().equals(inner.getId())) {
                //修改
                if (outer.getYear() == null) {
                    outer.setYear(0L);
                }
                if (inner.getYear() == null) {
                    inner.setYear(0L);
                }

                //评标专业ID
                String outerMajor = StringUtil.changeNullToSpace(outer.getMajor()).trim();
                String innerMajor = StringUtil.changeNullToSpace(inner.getMajor()).trim();
                String outerCouMajor = StringUtil.changeNullToSpace(outer.getCou_major()).trim();
                String innerCouMajor = StringUtil.changeNullToSpace(inner.getCou_major()).trim();

                if (!StringUtils.isEmpty(outerMajor)) {
                    if (!outerMajor.equals(innerMajor) || !outer.getGetTime().equals(inner.getGetTime())) {
                        //查询新旧 地区评标专业名称信息
                        SpecialtyInfoEntity outerInfo = expertInfoMapper.selectSpecialtyInfoBySpeId(outerMajor);
                        SpecialtyInfoEntity innerInfo = expertInfoMapper.selectSpecialtyInfoBySpeId(innerMajor);

                        String outerMajorName = "";
                        String innerMajorName = "";
                        String getTime = "";

                        if (outerInfo != null) {
                            outerMajorName = outerInfo.getSpe_name();
                        }
                        if (innerInfo != null) {
                            innerMajorName = innerInfo.getSpe_name();
                        }
                        if (!StringUtils.isEmpty(inner.getGetTime())) {
                            getTime = inner.getGetTime();
                        }


                        //查询新旧 国家评标专业名称信息
						/*SpecialtyCouInfoEntity couOuterInfo=expertInfoMapper.selectSpecialtyCouInfoBySpeId(outerCouMajor);
						SpecialtyCouInfoEntity couInnerInfo=expertInfoMapper.selectSpecialtyCouInfoBySpeId(innerCouMajor);

						String outerCouMajorName="";
						String innerCouMajorName="";

						if(couOuterInfo!=null){
							outerCouMajorName=couOuterInfo.getSpe_cou_name();
						}
						if(couInnerInfo!=null){
							innerCouMajorName=couInnerInfo.getSpe_cou_name();
						}
						*/

                        StringBuffer outerStr = new StringBuffer();
                        StringBuffer innerStr = new StringBuffer();
                        //新的专业信息
//                    outerStr.append("评标专业:" + outerMajorName + "<br/>从事专业年限:" + outer.getYear());
//                    //旧的专业信息
//                    innerStr.append("评标专业:" + innerMajorName + "<br/>从事专业年限:" + inner.getYear());

                        //新的专业信息
                        outerStr.append("评标专业:" + outerMajorName + "<br/>从事专业时间:" + outer.getGetTime());
                        //旧的专业信息
                        innerStr.append("评标专业:" + innerMajorName + "<br/>从事专业时间:" + getTime);

                        addExpertUpdateRecord(user_id, "评标专业" + (1 + i), innerStr.toString(), outerStr.toString(), modify_role, modify_user, modify_reason, maxBeachNum);
                    }
                }
                if (StringUtils.isEmpty(outer.getMajor())) {
                    outer.setGetTime("");
                }
                expertInfoMapper.updateExpertMajorInfo(outer);
            } else {
                //缺新增add
                outer.setId(CommUtil.getKey());
                outer.setUser_id(user_id);
                outer.setDelete_flag(0);
                expertInfoMapper.addExpertMajorInfo(outer);
            }
        }

		/*for(int i=0;i<majorOldList.size();i++){
			for(int j=0;j<majorNewList.size();j++) {
				if(i != j) {
					continue;
				}
				ExpertMajorEntity one=majorNewList.get(i);
				ExpertMajorEntity two=majorOldList.get(j);

				if(one.getId().equals(two.getId())){
					if(one.getYear()==null){
						one.setYear(0L);
					}
					if(two.getYear()==null){
						two.setYear(0L);
					}
					if(!StringUtil.changeNullToSpace(one.getMajor()).trim().equals(StringUtil.changeNullToSpace(two.getMajor())) || one.getYear()!=two.getYear() || !StringUtil.changeNullToSpace(one.getCou_major()).trim().equals(StringUtil.changeNullToSpace(two.getCou_major())) ){
						SpecialtyInfoEntity sone=expertInfoMapper.selectSpecialtyInfoBySpeId(StringUtil.changeNullToSpace(one.getMajor()));
						SpecialtyInfoEntity stwo=expertInfoMapper.selectSpecialtyInfoBySpeId(StringUtil.changeNullToSpace(two.getMajor()));

						SpecialtyCouInfoEntity scone=expertInfoMapper.selectSpecialtyCouInfoBySpeId(StringUtil.changeNullToSpace(one.getCou_major()));
						SpecialtyCouInfoEntity sctwo=expertInfoMapper.selectSpecialtyCouInfoBySpeId(StringUtil.changeNullToSpace(two.getCou_major()));

						String spe_nameone="";
						String spe_nametwo="";

						String spe_cnameone="";
						String spe_cnametwo="";

						if(sone!=null){
							spe_nameone=sone.getSpe_name();
						}
						if(stwo!=null){
							spe_nametwo=stwo.getSpe_name();
						}
						if(scone!=null){
							spe_cnameone=scone.getSpe_cou_name();
						}
						if(sctwo!=null){
							spe_cnametwo=sctwo.getSpe_cou_name();
						}

						StringBuffer oneStr=new StringBuffer();
						oneStr.append("评标专业:"+spe_nameone+"<br/>从事专业年限:"+one.getYear());//国家评标专业:"+spe_cnameone+"<br/>

						StringBuffer twoStr=new StringBuffer();
						twoStr.append("评标专业:"+spe_nametwo+"<br/>从事专业年限:"+two.getYear());//国家评标专业:"+spe_cnametwo+"<br/>

						addExpertUpdateRecord(user_id,"评标专业"+(1+i),twoStr.toString(),oneStr.toString(),modify_role,modify_user,modify_reason,maxBeachNum);
					}
				}
				expertInfoMapper.updateExpertMajorInfo(one);
			}
		}*/

        //工作经历比较
        //情况一  ：工作经历id 为空，为新增
        //请况二  : 工作经历id 不为空，并在备份表中存在相同的id ，并且内容有修改
        //情况三  : 工作经历id 不为空，并提交审核，但未通过，并且在备份表中不存在相同的id 还算是新增
        //情况四  : 工作经历id 不为空，delete_flag=1，并且在备份表中存在相同的id 算是删除
        //情况五  ：之前删除，并提交审核，但审核未通过，但备份表中依然存在，算是删除
        List<ExpertExperienceEntity> experienceTowList = expertInfoMapper.queryExpertExperienceBakList(expertOne.getUser_id());
        if (experienceOneList == null) {
            experienceOneList = new ArrayList<ExpertExperienceEntity>();
        }
        for (int i = 0; i < experienceOneList.size(); i++) {
            ExpertExperienceEntity oneExp = experienceOneList.get(i);
            if (oneExp == null) {
                continue;
            }
            //情况一
            if (oneExp.getId() == null || oneExp.getId().equals("")) {
                oneExp.setId(CommUtil.getKey());
                oneExp.setUser_id(user_id);
                oneExp.setDelete_flag(0);
                StringBuffer sb = new StringBuffer();
                sb.append("起止年月:" + oneExp.getBetween_time() != null && !"".equals(oneExp.getBetween_time()) ? oneExp.getBetween_time() + "<br/>" : "无" + "<br/>");
                sb.append("工作单位及职务:" + oneExp.getCompany() != null && !"".equals(oneExp.getCompany()) ? oneExp.getCompany() + "<br/>" : "无" + "<br/>");
                sb.append("证明人:" + oneExp.getWitness() != null && !"".equals(oneExp.getWitness()) ? oneExp.getWitness() + "<br/>" : "无" + "<br/>");
                sb.append("证明人联系方式:" + oneExp.getWitness_phone() != null && !"".equals(oneExp.getWitness_phone()) ? oneExp.getWitness_phone() + "<br/>" : "无");
                addExpertUpdateRecord(user_id, "工作经历(<span style=\"color:red\">新增</span>)", "", sb.toString(), modify_role, modify_user, modify_reason, maxBeachNum);
                expertInfoMapper.addExpertExperience(oneExp);
            } else if (oneExp.getId() != null && oneExp.getDelete_flag() == 0) {
                boolean isequal = false;
                ExpertExperienceEntity twoExp = null;
                for (int j = 0; j < experienceTowList.size(); j++) {
                    twoExp = experienceTowList.get(j);
                    if (oneExp.getId().equals(twoExp.getId())) {
                        experienceTowList.remove(j);
                        isequal = true;
                        break;
                    }
                }
                if (isequal) {
                    //请况二
                    if (!StringUtil.changeNullToSpace(oneExp.getBetween_time()).trim().equals(StringUtil.changeNullToSpace(twoExp.getBetween_time()).trim()) || !StringUtil.changeNullToSpace(oneExp.getCompany()).trim().equals(StringUtil.changeNullToSpace(twoExp.getCompany()).trim()) || !StringUtil.changeNullToSpace(oneExp.getWitness()).trim().equals(StringUtil.changeNullToSpace(twoExp.getWitness()).trim()) || !StringUtil.changeNullToSpace(oneExp.getWitness_phone()).trim().equals(StringUtil.changeNullToSpace(twoExp.getWitness_phone()).trim())) {
                        StringBuffer oneSb = new StringBuffer();
                        oneSb.append("起止年月:" + oneExp.getBetween_time() != null && !"".equals(oneExp.getBetween_time()) ? oneExp.getBetween_time() + "<br/>" : "无" + "<br/>");
                        oneSb.append("工作单位及职务:" + oneExp.getCompany() != null && !"".equals(oneExp.getCompany()) ? oneExp.getCompany() + "<br/>" : "无" + "<br/>");
                        oneSb.append("证明人:" + oneExp.getWitness() != null && !"".equals(oneExp.getWitness()) ? oneExp.getWitness() + "<br/>" : "无");
                        oneSb.append("证明人联系方式:" + oneExp.getWitness_phone() != null && !"".equals(oneExp.getWitness_phone()) ? oneExp.getWitness_phone() + "<br/>" : "无");

                        StringBuffer twoSb = new StringBuffer();
                        twoSb.append("起止年月:" + twoExp.getBetween_time() != null && !"".equals(twoExp.getBetween_time()) ? twoExp.getBetween_time() + "<br/>" : "无" + "<br/>");
                        twoSb.append("工作单位及职务:" + twoExp.getCompany() != null && !"".equals(twoExp.getCompany()) ? twoExp.getCompany() + "<br/>" : "" + "<br/>");
                        twoSb.append("证明人:" + twoExp.getWitness() != null && !"".equals(twoExp.getWitness()) ? twoExp.getWitness() + "<br/>" : "无" + "<br/>");
                        twoSb.append("证明人联系方式:" + twoExp.getWitness_phone() != null && !"".equals(twoExp.getWitness_phone()) ? twoExp.getWitness_phone() + "<br/>" : "无");

                        addExpertUpdateRecord(user_id, "工作经历(<span style=\"color:red\">修改</span>)", twoSb.toString(), oneSb.toString(), modify_role, modify_user, modify_reason, maxBeachNum);
                        expertInfoMapper.updateExpertExperience(oneExp);
                    }
                } else {
                    //情况三
                    StringBuffer sb = new StringBuffer();
                    sb.append("起止年月:" + oneExp.getBetween_time() != null && !"".equals(oneExp.getBetween_time()) ? oneExp.getBetween_time() + "<br/>" : "无" + "<br/>");
                    sb.append("工作单位及职务:" + oneExp.getCompany() != null && !"".equals(oneExp.getCompany()) ? oneExp.getCompany() + "<br/>" : "无" + "<br/>");
                    sb.append("证明人:" + oneExp.getWitness() != null && !"".equals(oneExp.getWitness()) ? oneExp.getWitness() + "<br/>" : "无" + "<br/>");
                    sb.append("证明人联系方式:" + oneExp.getWitness_phone() != null && !"".equals(oneExp.getWitness_phone()) ? oneExp.getWitness_phone() + "<br/>" : "无");
                    addExpertUpdateRecord(user_id, "工作经历(<span style=\"color:red\">新增</span>)", "", sb.toString(), modify_role, modify_user, modify_reason, maxBeachNum);
                    expertInfoMapper.addExpertExperience(oneExp);
                }
                //情况四
            } else if (oneExp.getId() != null && oneExp.getDelete_flag() == 1) {
                boolean isequal = false;
                ExpertExperienceEntity twoExp = null;
                for (int j = 0; j < experienceTowList.size(); j++) {
                    twoExp = experienceTowList.get(j);
                    if (oneExp.getId().equals(twoExp.getId())) {
                        experienceTowList.remove(j);
                        isequal = true;
                        break;
                    }
                }
                if (isequal) {
                    StringBuffer twoSb = new StringBuffer();
                    twoSb.append("起止年月:" + twoExp.getBetween_time() != null && !"".equals(twoExp.getBetween_time()) ? twoExp.getBetween_time() + "<br/>" : "无" + "<br/>");
                    twoSb.append("工作单位及职务:" + twoExp.getCompany() != null && !"".equals(twoExp.getCompany()) ? twoExp.getCompany() + "<br/>" : "无" + "<br/>");
                    twoSb.append("证明人:" + twoExp.getWitness() != "null" && twoExp.getWitness() != null && !"".equals(twoExp.getWitness()) ? twoExp.getWitness() + "<br/>" : "无" + "<br/>");
                    twoSb.append("证明人联系方式:" + twoExp.getWitness_phone() != "null" && twoExp.getWitness_phone() != null && !"".equals(twoExp.getWitness_phone()) ? twoExp.getWitness_phone() + "<br/>" : "无");

                    addExpertUpdateRecord(user_id, "工作经历(<span style=\"color:red\">删除</span>)", twoSb.toString(), "", modify_role, modify_user, modify_reason, maxBeachNum);
                    expertInfoMapper.updateExpertExperience(oneExp);
                }
            }
        }

        //情况五
        for (int i = 0; i < experienceTowList.size(); i++) {
            ExpertExperienceEntity twoExp = experienceTowList.get(i);
            StringBuffer twoSb = new StringBuffer();
            twoSb.append("起止年月:" + twoExp.getBetween_time() != null && !"".equals(twoExp.getBetween_time()) ? twoExp.getBetween_time() + "<br/>" : "无" + "<br/>");
            twoSb.append("工作单位及职务:" + twoExp.getCompany() != null && !"".equals(twoExp.getCompany()) ? twoExp.getCompany() + "<br/>" : "无" + "<br/>");
            twoSb.append("证明人:" + twoExp.getWitness() != "null" && !"".equals(twoExp.getWitness()) ? twoExp.getWitness() + "<br/>" : "无" + "<br/>");
            twoSb.append("证明人联系方式:" + twoExp.getWitness_phone() != "null" && !"".equals(twoExp.getWitness_phone()) ? twoExp.getWitness_phone() + "<br/>" : "无");
            addExpertUpdateRecord(user_id, "工作经历(<span style=\"color:red\">删除</span>)", twoSb.toString(), "", modify_role, modify_user, modify_reason, maxBeachNum);
        }

    }

    /**
     * 管理员新增或修改专家信息
     * 将数据同步到数据备份表
     *
     * @param expertEntity
     */
    @Transactional
    public void expertInfoSyncIntoBakTable(ExpertInfoEntity expertEntity) {

        //删除备份表中的数据
        expertInfoMapper.deleteExpertInfoBakByUserId(expertEntity.getUser_id());
        expertInfoMapper.deletePracticeBakByUserId(expertEntity.getUser_id());
        expertInfoMapper.deleteMajorBakByUserId(expertEntity.getUser_id());
        expertInfoMapper.deleteExperienceBakByUserId(expertEntity.getUser_id());

        ExpertInfoEntity expertEntityBak = expertInfoMapper.getExpertInfoByUserIdOrId(expertEntity);
        expertInfoMapper.saveExpertInfoBak(expertEntityBak);

        List<ExpertPracticeEntity> practiceList = expertInfoMapper.queryPracticeByUserId(expertEntity.getUser_id());
        for (int i = 0; i < practiceList.size(); i++) {
            ExpertPracticeEntity practice = practiceList.get(i);
            expertInfoMapper.addExpertPracticeBak(practice);
        }

        List<ExpertMajorEntity> majorList = expertInfoMapper.queryMajorByUserId(expertEntity.getUser_id());
        for (int i = 0; i < majorList.size(); i++) {
            ExpertMajorEntity major = majorList.get(i);
            expertInfoMapper.addExpertMajorInfoBak(major);
        }

        List<ExpertExperienceEntity> experienceList = expertInfoMapper.queryExpertExperienceList(expertEntity.getUser_id());
        for (int i = 0; i < experienceList.size(); i++) {
            ExpertExperienceEntity experience = experienceList.get(i);
            expertInfoMapper.addExpertExperienceBak(experience);
        }
    }


    /**
     * 新增专家信息修改记录
     *
     * @param user_id        用户编号
     * @param item           修改项
     * @param content_before 修改前内容
     * @param content_after  修改后内容
     * @param modify_role    修改角色
     * @param modify_user    修改人
     * @param modify_reason  修改原因
     * @param batch_number   修改批次编号
     */
    public void addExpertUpdateRecord(String user_id, String item, String content_before, String content_after, String modify_role, String modify_user, String modify_reason, int batch_number) {
        ExpertUpdateRecordEntity record = new ExpertUpdateRecordEntity();
        record.setId(CommUtil.getKey());
        record.setUser_id(user_id);
        record.setItem(item);
        record.setContent_before(content_before);
        record.setContent_after(content_after);
        record.setModify_role(modify_role);
        record.setModify_user(modify_user);
        record.setModify_reason(modify_reason);
        record.setBatch_number(batch_number);
        record.setModify_time(new Date());
        record.setDelete_flag(0);
        expertInfoMapper.addExpertUpdateRecord(record);
    }

    /**
     * 查询专家信息修改记录列表,用于信息修改审核
     *
     * @param user_id
     * @return
     */
    public List<ExpertUpdateRecordEntity> queryExpertUpdateRecordList(String user_id) {
        return expertInfoMapper.queryExpertUpdateRecordList(user_id);
    }

    /**
     * 查询所有专家信息修改记录列表,用于专家信息管理页面查看
     *
     * @param user_id
     * @return
     */
    public List<ExpertUpdateRecordEntity> queryAllExpertUpdateRecordList(String user_id) {
        return expertInfoMapper.queryAllExpertUpdateRecordList(user_id);
    }

    /**
     * 文件上传
     *
     * @param expertInfoEntity
     * @param practiceList
     * @param idFile
     * @param idFileFileName
     * @param certificateFile
     * @param certificateFileFileName
     * @param practiceFile1
     * @param practiceFile1FileName
     * @param practiceFile2
     * @param practiceFile2FileName
     * @param practiceFile3
     * @param practiceFile3FileName
     * @param practiceFile4
     * @param practiceFile4FileName
     * @param practiceFile5
     * @param practiceFile5FileName
     */
    public void uploadFile(ExpertInfoEntity expertInfoEntity, List<ExpertPracticeEntity> practiceList, File idFile, String idFileFileName, File certificateFile, String certificateFileFileName, File photoFileid, String photoFileidFileName, File technicalFiled, String technicalFiledFileName, File practiceFile1, String practiceFile1FileName, File practiceFile2, String practiceFile2FileName, File practiceFile3, String practiceFile3FileName) {
        String rootPath = ServletActionContext.getServletContext().getRealPath("");

        String savePath = rootPath + "/" + SysConstants.EXPERT_FILE_PATH_ROOT + expertInfoEntity.getUser_id();

        File file1 = new File(savePath);

        if (!file1.exists()) {
            file1.mkdirs();
        }

        if (idFile != null) {
            String newName = FileTypeCheck.fileTypeCheck(idFileFileName);
            String filePath = savePath + "/" + newName;

            File file = new File(filePath);
            boolean result = this.copyFile(idFile, file);
            if (result) {
                expertInfoEntity.setId_fileid(SysConstants.EXPERT_FILE_PATH_ROOT + expertInfoEntity.getUser_id() + "/" + newName);
                expertInfoEntity.setOld_id(idFileFileName);
            }
        }

        if (certificateFile != null) {
            String newName = FileTypeCheck.fileTypeCheck(certificateFileFileName);
            String filePath = savePath + "/" + newName;
            File file = new File(filePath);
            boolean result = this.copyFile(certificateFile, file);
            if (result) {
                expertInfoEntity.setCertificate_fileid(SysConstants.EXPERT_FILE_PATH_ROOT + expertInfoEntity.getUser_id() + "/" + newName);
                expertInfoEntity.setOld_certificate(certificateFileFileName);
            }
        }

        if (photoFileid != null) {
            String newName = FileTypeCheck.fileTypeCheck(photoFileidFileName);
            String filePath = savePath + "/" + newName;
            File file = new File(filePath);
            boolean result = this.copyFile(photoFileid, file);
            if (result) {
                expertInfoEntity.setPhoto_fileid(SysConstants.EXPERT_FILE_PATH_ROOT + expertInfoEntity.getUser_id() + "/" + newName);
                expertInfoEntity.setOld_photo(photoFileidFileName);
            }
        }

        if (technicalFiled != null) {
            String newName = FileTypeCheck.fileTypeCheck(technicalFiledFileName);
            String filePath = savePath + "/" + newName;
            File file = new File(filePath);
            boolean result = this.copyFile(technicalFiled, file);
            if (result) {
                expertInfoEntity.setTechnical_filed(SysConstants.EXPERT_FILE_PATH_ROOT + expertInfoEntity.getUser_id() + "/" + newName);
                expertInfoEntity.setOld_technical(technicalFiledFileName);
            }
        }


        if (practiceFile1 != null) {
            String newName = FileTypeCheck.fileTypeCheck(practiceFile1FileName);
            String filePath = savePath + "/" + newName;
            File file = new File(filePath);
            boolean result = this.copyFile(practiceFile1, file);
            if (result) {
                practiceList.get(0).setCertificate_fileid(SysConstants.EXPERT_FILE_PATH_ROOT + expertInfoEntity.getUser_id() + "/" + newName);
                practiceList.get(0).setOld_certificate(practiceFile1FileName);
            }
        } else if (practiceList.get(0).getCertificate().equals("")) {
            File file = new File(rootPath + "/" + practiceList.get(0).getCertificate_fileid());
            if (file.exists()) {
                file.delete();
                practiceList.get(0).setCertificate_fileid("");
            }
        }

        if (practiceFile2 != null) {
            String newName = FileTypeCheck.fileTypeCheck(practiceFile2FileName);
            String filePath = savePath + "/" + newName;
            File file = new File(filePath);
            boolean result = this.copyFile(practiceFile2, file);
            if (result) {
                practiceList.get(1).setCertificate_fileid(SysConstants.EXPERT_FILE_PATH_ROOT + expertInfoEntity.getUser_id() + "/" + newName);
                practiceList.get(1).setOld_certificate(practiceFile2FileName);
            }
        } else if (practiceList.get(1).getCertificate().equals("")) {
            File file = new File(rootPath + "/" + practiceList.get(1).getCertificate_fileid());
            if (file.exists()) {
                file.delete();
                practiceList.get(1).setCertificate_fileid("");
            }
        }

        if (practiceFile3 != null) {
            String newName = FileTypeCheck.fileTypeCheck(practiceFile3FileName);
            String filePath = savePath + "/" + newName;
            File file = new File(filePath);
            boolean result = this.copyFile(practiceFile3, file);
            if (result) {
                practiceList.get(2).setCertificate_fileid(SysConstants.EXPERT_FILE_PATH_ROOT + expertInfoEntity.getUser_id() + "/" + newName);
                practiceList.get(2).setOld_certificate(practiceFile3FileName);
            }
        } else if (practiceList.get(2).getCertificate().equals("")) {
            File file = new File(rootPath + "/" + practiceList.get(2).getCertificate_fileid());
            if (file.exists()) {
                file.delete();
                practiceList.get(2).setCertificate_fileid("");
            }
        }

    }

    /**
     * 文件上传
     *
     * @param expertInfoEntity
     * @param practiceList
     * @param idFile
     * @param idFileFileName
     * @param certificateFile
     * @param certificateFileFileName
     * @param practiceFile1
     * @param practiceFile1FileName
     * @param practiceFile2
     * @param practiceFile2FileName
     * @param practiceFile3
     * @param practiceFile3FileName
     * @param practiceFile4
     * @param practiceFile4FileName
     * @param practiceFile5
     * @param practiceFile5FileName
     */

    public void newUploadFile(ExpertInfoEntity expertInfoEntity, List<ExpertPracticeEntity> practiceList, File idFile, String idFileFileName, File certificateFile, String certificateFileFileName, File photoFileid, String photoFileidFileName, File technicalFiled, String technicalFiledFileName, File practiceFile1, String practiceFile1FileName, File practiceFile2, String practiceFile2FileName, File practiceFile3, String practiceFile3FileName, File ICBackFile, String ICBackName) {
        String rootPath = ServletActionContext.getServletContext().getRealPath("");

        String savePath = rootPath + "/" + SysConstants.EXPERT_FILE_PATH_ROOT + expertInfoEntity.getUser_id();

        File file1 = new File(savePath);

        if (!file1.exists()) {
            file1.mkdirs();
        }

        if (idFile != null) {
            String newName = FileTypeCheck.fileTypeCheck(idFileFileName);
            String filePath = savePath + "/" + newName;

            File file = new File(filePath);
            boolean result = this.copyFile(idFile, file);
            if (result) {
                expertInfoEntity.setId_fileid(SysConstants.EXPERT_FILE_PATH_ROOT + expertInfoEntity.getUser_id() + "/" + newName);
                expertInfoEntity.setOld_id(idFileFileName);
            }
        }

        if (certificateFile != null) {
            String newName = FileTypeCheck.fileTypeCheck(certificateFileFileName);
            String filePath = savePath + "/" + newName;
            File file = new File(filePath);
            boolean result = this.copyFile(certificateFile, file);
            if (result) {
                expertInfoEntity.setCertificate_fileid(SysConstants.EXPERT_FILE_PATH_ROOT + expertInfoEntity.getUser_id() + "/" + newName);
                expertInfoEntity.setOld_certificate(certificateFileFileName);
            }
        }

        if (photoFileid != null) {
            String newName = FileTypeCheck.fileTypeCheck(photoFileidFileName);
            String filePath = savePath + "/" + newName;
            File file = new File(filePath);
            boolean result = this.copyFile(photoFileid, file);
            if (result) {
                expertInfoEntity.setPhoto_fileid(SysConstants.EXPERT_FILE_PATH_ROOT + expertInfoEntity.getUser_id() + "/" + newName);
                expertInfoEntity.setOld_photo(photoFileidFileName);
            }
        }

        if (technicalFiled != null) {
            String newName = FileTypeCheck.fileTypeCheck(technicalFiledFileName);
            String filePath = savePath + "/" + newName;
            File file = new File(filePath);
            boolean result = this.copyFile(technicalFiled, file);
            if (result) {
                expertInfoEntity.setTechnical_filed(SysConstants.EXPERT_FILE_PATH_ROOT + expertInfoEntity.getUser_id() + "/" + newName);
                expertInfoEntity.setOld_technical(technicalFiledFileName);
            }
        }

        if (ICBackFile != null) {
            String newName = FileTypeCheck.fileTypeCheck(ICBackName);
            String filePath = savePath + "/" + newName;
            File file = new File(filePath);
            boolean result = copyFile(ICBackFile, file);
            if (result) {
                expertInfoEntity.setICBackFileId(SysConstants.EXPERT_FILE_PATH_ROOT + expertInfoEntity.getUser_id() + "/" + newName);
                expertInfoEntity.setICBackName(ICBackName);
            }
        }

        if (practiceFile1 != null) {
            String newName = FileTypeCheck.fileTypeCheck(practiceFile1FileName);
            String filePath = savePath + "/" + newName;
            File file = new File(filePath);
            boolean result = this.copyFile(practiceFile1, file);
            if (result) {
                practiceList.get(0).setCertificate_fileid(SysConstants.EXPERT_FILE_PATH_ROOT + expertInfoEntity.getUser_id() + "/" + newName);
                practiceList.get(0).setOld_certificate(practiceFile1FileName);
            }
        } else if (practiceList.get(0).getCertificate().equals("")) {
            File file = new File(rootPath + "/" + practiceList.get(0).getCertificate_fileid());
            if (file.exists()) {
                file.delete();
                practiceList.get(0).setCertificate_fileid("");
            }
        }

        if (practiceFile2 != null) {
            String newName = FileTypeCheck.fileTypeCheck(practiceFile2FileName);
            String filePath = savePath + "/" + newName;
            File file = new File(filePath);
            boolean result = this.copyFile(practiceFile2, file);
            if (result) {
                practiceList.get(1).setCertificate_fileid(SysConstants.EXPERT_FILE_PATH_ROOT + expertInfoEntity.getUser_id() + "/" + newName);
                practiceList.get(1).setOld_certificate(practiceFile2FileName);
            }
        } else if (practiceList.get(1).getCertificate().equals("")) {
            File file = new File(rootPath + "/" + practiceList.get(1).getCertificate_fileid());
            if (file.exists()) {
                file.delete();
                practiceList.get(1).setCertificate_fileid("");
            }
        }

        if (practiceFile3 != null) {
            String newName = FileTypeCheck.fileTypeCheck(practiceFile3FileName);
            String filePath = savePath + "/" + newName;
            File file = new File(filePath);
            boolean result = this.copyFile(practiceFile3, file);
            if (result) {
                practiceList.get(2).setCertificate_fileid(SysConstants.EXPERT_FILE_PATH_ROOT + expertInfoEntity.getUser_id() + "/" + newName);
                practiceList.get(2).setOld_certificate(practiceFile3FileName);
            }
        } else if (practiceList.get(2).getCertificate().equals("")) {
            File file = new File(rootPath + "/" + practiceList.get(2).getCertificate_fileid());
            if (file.exists()) {
                file.delete();
                practiceList.get(2).setCertificate_fileid("");
            }
        }

    }

    /**
     * 文件复制处理
     *
     * @param src
     * @param dst
     * @return
     */
    public synchronized static boolean copyFile(File src, File dst) {
        if (src == null || dst == null) {
            return false;
        }
        int BUFFER_SIZE = 16 * 1024;
        InputStream ins = null;
        OutputStream os = null;
        try {
            ins = new BufferedInputStream(new FileInputStream(src), BUFFER_SIZE);
            os = new BufferedOutputStream(new FileOutputStream(dst), BUFFER_SIZE);
            byte[] buffer = new byte[BUFFER_SIZE];
            int len = 0;
            while ((len = ins.read(buffer)) > 0) {
                os.write(buffer, 0, len);
            }
            os.flush();
        } catch (FileNotFoundException e) {
            e.printStackTrace();
            return false;
        } catch (IOException e) {
            e.printStackTrace();
            return false;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        } finally {
            try {
                if (ins != null) {
                    ins.close();
                }
                if (os != null) {
                    os.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return true;
    }

    /**
     * 删除指定的上传文件
     */
    public void uploadFileDelete(ExpertInfoEntity expertInfoEntity, List<ExpertPracticeEntity> practiceList, File idFile, String idFileFileName, File certificateFile, String certificateFileFileName, File photoFileid, String photoFileidFileName, File technicalFiled, String technicalFiledFileName, File practiceFile1, String practiceFile1FileName, File practiceFile2, String practiceFile2FileName, File practiceFile3, String practiceFile3FileName) {
        String savePath = ServletActionContext.getServletContext().getRealPath("") + "/" + SysConstants.EXPERT_FILE_PATH_ROOT + expertInfoEntity.getUser_id();
        if (idFile != null) {
            String filePath = savePath + "/" + expertInfoEntity.getId_fileid();
            File file = new File(filePath);
            if (file.exists()) {
                file.delete();
            }
        }

        if (certificateFile != null) {
            String filePath = savePath + "/" + expertInfoEntity.getCertificate_fileid();
            File file = new File(filePath);
            if (file.exists()) {
                file.delete();
            }
        }

        if (photoFileid != null) {
            String filePath = savePath + "/" + expertInfoEntity.getPhoto_fileid();
            File file = new File(filePath);
            if (file.exists()) {
                file.delete();
            }
        }

        if (technicalFiled != null) {
            String filePath = savePath + "/" + expertInfoEntity.getTechnical_filed();
            File file = new File(filePath);
            if (file.exists()) {
                file.delete();
            }
        }

        if (practiceFile1 != null) {
            String filePath = savePath + "/" + practiceList.get(0).getCertificate_fileid();
            File file = new File(filePath);
            if (file.exists()) {
                file.delete();
            }
        }

        if (practiceFile2 != null) {
            String filePath = savePath + "/" + practiceList.get(1).getCertificate_fileid();
            File file = new File(filePath);
            if (file.exists()) {
                file.delete();
            }
        }

        if (practiceFile3 != null) {
            String filePath = savePath + "/" + practiceList.get(2).getCertificate_fileid();
            File file = new File(filePath);
            if (file.exists()) {
                file.delete();
            }
        }
    }

    /**
     * 验证身份证号码在数据库中是否已经存在
     *
     * @param id_no
     * @return
     */
    public int checkIdNo(ExpertInfoEntity expertInfoEntity) {
        return expertInfoMapper.checkIdNo(expertInfoEntity);
    }

    /**
     * 验证档案号在数据库中是否已经存在
     *
     * @param expertInfoEntity
     * @return
     */
    public int checkDocumentNo(ExpertInfoEntity expertInfoEntity) {
        return expertInfoMapper.checkDocumentNo(expertInfoEntity);
    }

    /**
     * 验证手机号码在数据库中是否已经存在
     *
     * @param expertInfoEntity
     * @return
     */
    public int checkMobile(ExpertInfoEntity expertInfoEntity) {
        return expertInfoMapper.checkMobile(expertInfoEntity);
    }

    /**
     * 修改专家平均分
     *
     * @param expertInfoEntity
     */
    public void updateExpertScore(ExpertInfoEntity expertInfoEntity) {
        expertInfoMapper.updateExpertScore(expertInfoEntity);
    }

    /**
     * 更新文件名称
     *
     * @param expertInfoEntity
     */
    public void updateFile(ExpertInfoEntity expertInfoEntity) {
        expertInfoMapper.updateFile(expertInfoEntity);
    }

    /**
     * 获取评标的专家信息
     *
     * @param expertInfoEntity
     * @return
     */
    public List<ExpertInfoEntity> queryPageBidExpertInfo(ExpertInfoEntity expertInfoEntity) {
        return expertInfoMapper.queryPageBidExpertInfo(expertInfoEntity);
    }

    /**
     * 查询专家库标记记录
     *
     * @param expertInfoEntity
     * @return
     */
    public List<ExpertInfoEntity> queryPageByExpertLibrary(ExpertInfoEntity expertInfoEntity) {
        if (null == expertInfoEntity.getIllegalCount()
                && (null == expertInfoEntity.getHealthy() || expertInfoEntity.getHealthy().isEmpty())) {
            return expertInfoMapper.queryPageByExpertByAge(expertInfoEntity);
        }
        return expertInfoMapper.queryPageByExpertLibrary(expertInfoEntity);
    }

    /**
     * 导出出库专家汇总及专家库标记记录表
     *
     * @param colExpertInfo
     * @param response
     */
    public void exportExpertInfo(ExpertInfoEntity colExpertInfo, HttpServletResponse response) {
        List<ExpertInfoEntity> expertInfoList = queryPageByExpertLibrary(colExpertInfo);
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        Integer lastIndex = 1;         // 最后行数
        String fileName = "出库专家汇总及专家库标记记录表.xls";
        // 创建 EXCEL
        XLSExport e = new XLSExport(response, fileName);
        HSSFSheet sheet = e.getSheet();
        e.createRow(0);
        e.setOne(0, 16, 50, "出库专家汇总及专家库标记记录表 ");
        e.setOne(1, 16, 50, "");
        e.setOne(2, 16, 50, "");
        e.setOne(3, 16, 50, "");
        e.setOne(4, 16, 50, "");
        e.setOne(5, 16, 50, "");
        e.setOne(6, 16, 50, "");
        sheet.addMergedRegion(new Region(0, (short) 0, 0, (short) (6)));
        // 列名
        e.createRow(1);
        e.setTwo(0, 10, 30f, 2000, "序号");
        e.setTwo(1, 10, 30f, 3700, "姓名");
        e.setTwo(2, 10, 30f, 4000, "专家编号");
        e.setTwo(3, 10, 30f, 7500, "出库原因\n（违规/年龄/健康等）");
        e.setTwo(4, 10, 30f, 5000, "标记日期");
        e.setTwo(5, 10, 30f, 3700, "标记输入人");
        e.setTwo(6, 10, 30f, 5300, "备注");
        // 存在数据
        if (null != expertInfoList && !expertInfoList.isEmpty()) {
            lastIndex += expertInfoList.size();
            for (int i = 0; i < expertInfoList.size(); i++) {
                ExpertInfoEntity expertInfo = expertInfoList.get(i);
                e.createRow(i + 2);
                e.setCel(0, 10, 30f, String.valueOf(i + 1));
                e.setCel(1, 10, 30f, expertInfo.getUser_name());
                e.setCel(2, 10, 30f, expertInfo.getExpert_num());
                StringBuffer libraryStr = new StringBuffer();
                // 判断是否超龄
                if (colExpertInfo.getAge() == expertInfo.getAge()) {
                    libraryStr.append("  等于").append(expertInfo.getAge()).append("岁");
                } else if (null != colExpertInfo.getAge() && expertInfo.getAge() > colExpertInfo.getAge()) {
                    libraryStr.append("  大于").append(colExpertInfo.getAge()).append("岁");
                }
                // 判断是否违规
                if (null != expertInfo.getIllegalCount() && 0 < expertInfo.getIllegalCount()) {
                    libraryStr.append("  违规").append(expertInfo.getIllegalCount()).append("次");
                }
                // 判断是否健康
                if (null != expertInfo.getReason() && !expertInfo.getReason().isEmpty()) {
                    libraryStr.append(" " + expertInfo.getReason());
                }
                e.setCel(3, 10, 30f, libraryStr.toString());
                e.setCel(4, 10, 30f, null == expertInfo.getCall_time() ? "" : format.format(expertInfo.getCall_time()));
                e.setCel(5, 10, 30f, expertInfo.getManager());
                e.setCel(6, 10, 30f, "");
            }
        } else {// 不存在数据
            lastIndex++;
            e.createRow(2);
            e.setCell(0, "没有查询到数据");
            e.setCell(1, "");
            e.setCell(2, "");
            e.setCell(3, "");
            e.setCell(4, "");
            e.setCell(5, "");
            e.setCell(6, "");
            sheet.addMergedRegion(new Region(2, (short) 0, 2, (short) (6)));
        }
        lastIndex = createNullRow(lastIndex, e, sheet, "（专家管理处）工作人员（签字）               年    月    日");
        lastIndex = createNullRow(lastIndex, e, sheet, "（公司领导签字）                            年    月    日");


        e.exportXLS();

    }

    /**
     * 创建空行
     *
     * @param lastIndex
     * @param e
     * @param sheet
     * @return
     */
    private Integer createNullRow(Integer lastIndex, XLSExport e, HSSFSheet sheet, String content) {
        lastIndex++;
        e.createRow(lastIndex);
        e.setCel(0, 10, 25f, content, HSSFCellStyle.ALIGN_RIGHT, false);
        for (int i = 1; i <= 6; i++) {
            e.setCel(i, 10, 30f, "", HSSFCellStyle.ALIGN_CENTER, false);
        }
        sheet.addMergedRegion(new Region(lastIndex, (short) 0, lastIndex, (short) (6)));
        return lastIndex;
    }

    /**
     * 更新专家状态（一年周期内扣分）
     *
     * @param
     * @return
     */
    public void modifyExpertStatusByScore() {
        expertInfoMapper.modifyExpertStatusByScore(80);
        expertInfoMapper.modifyExpertStatusByScore(75);
    }

    /**
     * 更新专家状态byId
     *
     * @param
     * @return
     */
    public void modifyExpertStatusById(String id) {
        expertInfoMapper.modifyExpertStatusById(id);
    }


    /**
     * 查询所有专家分数
     *
     * @return
     */
    public List<ExpertInfoEntity> queryExpertInfoList(Integer status) {
        return expertInfoMapper.queryExpertInfoList(status);
    }

    /**
     * 暂停专家
     */
    public void modifyExpertAssessment(ExpertInfoEntity entity) {
        expertInfoMapper.modifyExpertAssessment(entity);
    }

    /**
     * 跨年更新重置所有专家的分值
     */
    public void updateAllExpertScore(ExpertInfoEntity entity) {
        expertInfoMapper.updateAllExpertScore(entity);
    }

    /**
     * 函数功能描述：修改暂停专家的状态
     *
     * @param date
     */
    public void updateExpertStatusByDate(String date) {
        expertInfoMapper.updateExpertStatusByDate(date);
    }

    /**
     * 根据抽取记录查询专家信息
     *
     * @return
     */
    public ExpertInfoEntity queryExpertByResultId(String id) {
        ExpertInfoEntity entity = expertInfoMapper.queryExpertByResultId(id);
        return entity;
    }

    /**
     * 查询所有的专家信息
     * 函数功能描述：TODO
     *
     * @return
     */
    public List<ExpertInfoEntity> queryAllExpertInfo() {
        return expertInfoMapper.queryAllExpertInfo();
    }

    /**
     * 根据手机号查询对应专家信息
     */
    public ExpertInfoEntity queryExpertByMobile(String mobile) {
        return expertInfoMapper.queryExpertByMobile(mobile);
    }

    /**
     * 根据专家id和批次号更新专家的是否参加理由
     * 函数功能描述：TODO
     *
     * @param mobile
     * @return
     */
    public void updateExpertByIdAndBatch(ResultEntity result) {
        expertInfoMapper.updateExpertByIdAndBatch(result);
    }

    /**
     * 查询所有的专家信息
     * 函数功能描述：TODO
     *
     * @return
     */
    public List<ExpertInfoEntity> queryExpertCompany(ExpertInfoEntity info) {
        return expertInfoMapper.queryExpertCompany(info);
    }


    /**
     * 查询所有的专家信息
     * 函数功能描述：TODO
     *
     * @return
     */
    public List<ExpertInfoEntity> queryExpertCode(ExpertInfoEntity info) {
        return expertInfoMapper.queryExpertCode(info);
    }


    /**
     * 查询所有的专家身份证号
     * 函数功能描述：TODO
     *
     * @return
     */
    public List<ExpertInfoEntity> queryAllExpertIdNo(ExpertInfoEntity info) {
        return expertInfoMapper.queryAllExpertIdNo(info);
    }

    /**
     * 根据专家id和批次号更新不参加理由
     * 函数功能描述：TODO
     *
     * @param mobile
     * @return
     */
    public void modifyExpertIdNo(ExpertInfoEntity info) {
        expertInfoMapper.modifyExpertIdNo(info);
    }

    public int checkMajorNum(ExpertInfoEntity colExpertInfo) {
        List<ExpertMajorEntity> expertMajorEntities = expertInfoMapper.queryMajorByUserId(colExpertInfo.getUser_id());
        if (null != expertMajorEntities && expertMajorEntities.size() >= 5) {
            return 1;
        } else {
            return 0;
        }
    }
}
