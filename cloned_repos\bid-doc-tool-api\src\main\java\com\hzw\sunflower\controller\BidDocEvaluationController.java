package com.hzw.sunflower.controller;
import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.controller.request.BidDocDetailExcelReq;
import com.hzw.sunflower.controller.request.BidDocPreliminaryExcelReq;
import com.hzw.sunflower.service.BidDocEvaluationDetailsService;
import com.hzw.sunflower.service.BidDocEvaluationPreliminaryService;
import com.hzw.sunflower.service.BidDocEvaluationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

@Api(tags = "评标办法")
@RestController
@RequestMapping("/bidDocEvaluation")
public class BidDocEvaluationController{

    @Autowired
    private BidDocEvaluationService bidDocEvaluationService;

    @Autowired
    private BidDocEvaluationPreliminaryService bidDocEvaluationPreliminaryService;

    @Autowired
    private BidDocEvaluationDetailsService bidDocEvaluationDetailsService;

    @ApiOperation(value = "下载模板 初步评审评标办法导入模板")
    @PostMapping(value = "/downTemplatePreliminary")
    public void downTemplatePreliminary(HttpServletRequest request, HttpServletResponse response) {
        bidDocEvaluationPreliminaryService.downTemplatePreliminary(request, response);
    }

    @ApiOperation(value = "批量导入 初步评审评标办法文件解析")
    @ApiImplicitParam(name = "req", value = "文件id", required = true, dataType = "BidDocPreliminaryExcelReq", paramType = "body")
    @PostMapping(value = "/preliminaryExcelParse")
    public Result<List<List<String>>> preliminaryExcelParse(@RequestBody @Valid BidDocPreliminaryExcelReq req){
        List<List<String>> listList = bidDocEvaluationPreliminaryService.preliminaryExcelParse(req);
        return Result.ok(listList);
    }

    @ApiOperation(value = "下载模板 详细评审评标办法导入模板")
    @PostMapping(value = "/downTemplateDetails")
    public void downTemplateDetails(HttpServletRequest request, HttpServletResponse response) {
        bidDocEvaluationDetailsService.downTemplateDetails(request, response);
    }

    @ApiOperation(value = "批量导入 详细评审评标办法文件解析")
    @ApiImplicitParam(name = "req", value = "文件id", required = true, dataType = "BidDocDetailExcelReq", paramType = "body")
    @PostMapping(value = "/detailsExcelParse")
    public Result<List<List<String>>> detailsExcelParse(@RequestBody @Valid BidDocDetailExcelReq req){
        List<List<String>> listList = bidDocEvaluationDetailsService.detailsExcelParse(req);
        return Result.ok(listList);
    }

}
