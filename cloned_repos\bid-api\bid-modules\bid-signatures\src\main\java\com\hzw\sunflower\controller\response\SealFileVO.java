package com.hzw.sunflower.controller.response;

import  com.hzw.sunflower.dto.SealFileDTO;
import com.hzw.sunflower.entity.SealFile;
import io.swagger.annotations.ApiModel;
import lombok.Data;

/**
* SealFile 返回实体
*
* <AUTHOR>
* @version 1.0.0
*/
@ApiModel(description = "SealFilevo")
@Data
public class SealFileVO extends SealFile {

    /**
     * 原始文件key
     */
    private String originalKey;

    /**
     * 原始文件名
     */
     private String  originalName;

    /**
     * 签章以后文件key
     */
    private String sealKey;


    /**
     * 签章之后文件名
     */
    private String sealName;

}