package com.hzw.sunflower.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hzw.sunflower.controller.project.response.ProjectBidSectionVO;
import com.hzw.sunflower.dto.ProjectShareInfoDto;
import com.hzw.sunflower.entity.Project;
import com.hzw.sunflower.entity.ProjectBidSection;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 项目标段表 Mapper接口
 *
 * <AUTHOR>
 * @version 1.0.0 2021-04-07
 */
public interface ProjectBidSectionMapper extends BaseMapper<ProjectBidSection> {

    /**
     * 项目包件新建修改-项目冗余字段处理
     *
     * @param id 主键ID
     * @return 是否成功
     */
    String queryProjectStatusById(@Param("id") Long id);

    /**
     * 根据项目币种汇率查询除当前标段外的标段委托金额总和
     *
     * @param projectBidSection 标段信息
     * @param currency 项目币种汇率
     * @return 委托金额
     */
    String queryAmountByProjectIdExceptBidId(@Param("section") ProjectBidSection projectBidSection, @Param("currency") String currency);

    /**
     * 查询最大标段号
     *
     * @param projectId 项目ID
     * @return 标段号
     */
    String queryMaxPackageNumber(@Param("projectId") Long projectId);

    /**
     * 更加标段id查询项目信息
     * @param sectionId
     * @return
     */
    Project queryProjectInfoById(@Param("sectionId") Long sectionId);

    /**
     * 查询套内的标段信息
     * @param sectionGroupId
     * @return
     */
    String getGroupSection(@Param("groupId") Long sectionGroupId);

    /**
     * 通过projectId获取最大标段的状态
     * @param projectId
     * @return
     */
    ProjectBidSection getMaxStatusSectionByProjectId(@Param("projectId") Long projectId,@Param("sectionId") Long sectionId);

    /**
     * 修改标段采购文件的文件发售截至时间
     * @param sectionId
     * @param saleEndTime
     * @return
     */
    Integer updateDocSaleEndTime(@Param("sectionId") Long sectionId, @Param("saleEndTime") String saleEndTime);

    /**
     * 修改标段采购文件的响应文件递交截至时间
     * @param sectionId
     * @param submitEndTime
     * @return
     */
    Integer updateDocSubmitEndTime(@Param("sectionId") Long sectionId, @Param("submitEndTime") String submitEndTime);

    List<ProjectBidSectionVO> listUpdate(@Param("projectId")Long projectId, @Param("value")Integer value, @Param("value1")Integer value1);


    /**
     * 通过projectId获取最大标段的状态
     * @param projectId
     * @return
     */
    ProjectBidSection getMaxStatusSectionByProjectId2(@Param("projectId") Long projectId,@Param("sectionId") Long sectionId);

    /**
     * 查询ncc中的项目编号和项目的所在部门
     * @param sectionId
     * @return
     */
    ProjectShareInfoDto getProjectShareToNccInfoBySectionId(@Param("sectionId") Long sectionId);

    /**
     * 查询标段列表信息 用印申请
     * @param projectId
     * @return
     */
    List<ProjectBidSectionVO> listInfo(Long projectId);
}
