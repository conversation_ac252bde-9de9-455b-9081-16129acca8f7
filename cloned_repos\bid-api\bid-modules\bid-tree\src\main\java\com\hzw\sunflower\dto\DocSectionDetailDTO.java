package com.hzw.sunflower.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Map;

/**
 * <AUTHOR>
 * @date ：Created in 2021/12/15 10:44
 * @description：文件详情页面关联包信息
 * @version: 1.0
 */
@Data
@EqualsAndHashCode
public class DocSectionDetailDTO implements Serializable {

    @ApiModelProperty(value = "标段ID", position = 1)
    private Long sectionId;

    @ApiModelProperty(value = "包段编号", position = 3)
    private Integer packageNumber;

    @ApiModelProperty(value = "包名称", position = 4)
    private String packageName;

    @ApiModelProperty(value = "委托金额", position = 5)
    private BigDecimal entrustMoney;

    @ApiModelProperty(value = "委托类型(0:入围, 1:带量)", position = 6)
    private String entrustType;

    @ApiModelProperty(value = "保证金", position = 7)
    private BigDecimal bond;

    @ApiModelProperty(value = "保证金类型 0不收取 1定额 2比例 ", position = 8)
    private Integer bondType;

    @ApiModelProperty(value = "保证金比例值", position = 9)
    private BigDecimal bondPercent;

    @ApiModelProperty(value = "标书费", position = 10)
    private BigDecimal tenderFee;

    @ApiModelProperty(value = "标书费 类型 0不收取 1按项目 2按标段/包", position = 11)
    private Integer tenderFeeType;

    @ApiModelProperty(value = "招标文件发售结束时间类型（1：确定时间; 2：另行通知; 3：投标文件递交截止时间前一天）", position = 12)
    private Integer saleEndTimeType;

    @ApiModelProperty(value = "招标文件发售结束时间", position = 13)
    private Date saleEndTime;

    @ApiModelProperty(value = "投标文件递交截止时间类型（1：确定时间; 2：另行通知）", position = 14)
    private Integer submitEndTimeType;

    @ApiModelProperty(value = "投标文件递交截止时间", position = 15)
    private Date submitEndTime;

    @ApiModelProperty(value = "对比标识", position = 16)
    private Map flag;

    @ApiModelProperty(value = "采购方式", position = 17)
    private String purchaseMode;

    @ApiModelProperty(value = "1-预审公开 2-后审公开 3-邀请 4-二阶段 5-政府公开 6-政府邀请", position = 18)
    private Integer purchaseStatus;

    @ApiModelProperty(value = "1 依法必招  2 非依法必招  3 政府采购  4 国际招标", position = 19)
    private Integer purchaseType;

    private String purchaseModeName;

    @ApiModelProperty(value = "币种")
    private Long entrustCurrency;

    @ApiModelProperty(value = "线上澄清（1 支持 2 不支持）")
    private Integer clarifyOnLine;

    @ApiModelProperty(value ="提出澄清截止时间")
    private Date clarifyEndTime;

    @ApiModelProperty(value ="提出澄清截止时间（1：确定时间; 2：投标文件递交截止时间前一天）")
    private Integer clarifyEndTimeType;

    @ApiModelProperty(value = "线上异议（1 支持 2 不支持）")
    private Integer questionOnLine;

    @ApiModelProperty(value ="提出异议截止时间（1：确定时间; 2：投标文件递交截止时间前一天）")
    private Integer questionEndTimeType;

    @ApiModelProperty(value ="提出异议截止时间")
    private Date questionEndTime;

    @ApiModelProperty(value = "线上开标（1 支持 2 不支持）")
    private Integer bidOpenOnline;

    @ApiModelProperty(value ="解密时间")
    private Integer decryptTime;

    @ApiModelProperty(value ="开标条件")
    private Integer supNumEndBid;

    @ApiModelProperty(value ="开标一览表oss文件id")
    private Long bidOpeningFileId;

    @ApiModelProperty(value ="生成规则 1.直接拼接开标汇总表 2.从x行解析拼接")
    private Integer generationType;

    @ApiModelProperty(value = "是否自动唱标")
    private Integer isAutoChant;
}
