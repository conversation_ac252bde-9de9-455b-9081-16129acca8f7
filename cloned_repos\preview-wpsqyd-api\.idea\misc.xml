<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CheckstyleConfigurable">
    <option name="suppFilterFilename" value="" />
    <option name="suppCommentFilter" value="false" />
    <option name="offComment" value="CHECKSTYLE\:OFF" />
    <option name="onComment" value="CHECKSTYLE\:ON" />
    <option name="checkFormat" value=".*" />
    <option name="messageFormat" value="" />
    <option name="checkCPP" value="true" />
    <option name="checkC" value="true" />
    <option name="suppNearbyCommentFilter" value="false" />
    <option name="snCommentFormat" value="SUPPRESS CHECKSTYLE (\w+)" />
    <option name="snCheckFormat" value="$1" />
    <option name="snMessageFormat" value="" />
    <option name="snInfluenceFormat" value="0" />
    <option name="snCheckCPP" value="true" />
    <option name="snCheckC" value="true" />
    <option name="pathToUserRulesConfiguration" value="" />
    <option name="pathToJarWithRules" value="" />
  </component>
  <component name="ExternalStorageConfigurationManager" enabled="true" />
  <component name="FindBugsConfigurable">
    <option name="make" value="true" />
    <option name="effort" value="default" />
    <option name="priority" value="Medium" />
    <option name="excludeFilter" value="" />
  </component>
  <component name="MavenProjectsManager">
    <option name="originalFiles">
      <list>
        <option value="$PROJECT_DIR$/pom.xml" />
      </list>
    </option>
  </component>
  <component name="ProjectRootManager" version="2" languageLevel="JDK_11" default="true" project-jdk-name="11" project-jdk-type="JavaSDK">
    <output url="file://$PROJECT_DIR$/out" />
  </component>
  <component name="SuppressionsComponent">
    <option name="suppComments" value="[]" />
  </component>
</project>