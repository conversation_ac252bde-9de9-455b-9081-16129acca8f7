package com.hzw.sunflower.controller.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 保证金流水表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-27
 */
@Data
@ApiModel(value = "BondWater对象", description = "保证金流水表")
public class BondWaterVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("交易流水号")
    private String waterNumber;

    @ApiModelProperty("交易日期")
    private String date;

    @ApiModelProperty("交易时间")
    private String time;

    @ApiModelProperty("供应商名称")
    private String companyName;

    @ApiModelProperty("供应商付款账户")
    private String companyAccount;

    @ApiModelProperty("供应商付款开户行")
    private String companyBankDeposit;

    @ApiModelProperty("供应商付款银行行号")
    private String companyBankCode;

    @ApiModelProperty("交易金额（保留两位小数）")
    private BigDecimal amount;

    @ApiModelProperty("收款账户")
    private String receiveAcount;

    @ApiModelProperty("收款银行")
    private String receiveBank;

    @ApiModelProperty("附言")
    private String postScript;

    @ApiModelProperty("数据来源 1.银行对接 2.excel导入")
    private Integer source;

    @ApiModelProperty("数据来源 1.正常 2.45天未关联 3.60天未关联")
    private Integer abnormalStatus;

}
