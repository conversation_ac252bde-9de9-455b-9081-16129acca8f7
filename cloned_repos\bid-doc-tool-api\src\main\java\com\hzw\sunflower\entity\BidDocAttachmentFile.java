package com.hzw.sunflower.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.hzw.sunflower.common.BaseBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@TableName("t_bid_doc_attachment_file")
@ApiModel(value = "打包文件表", description = "")
public class BidDocAttachmentFile extends BaseBean implements Serializable {

    @ApiModelProperty(value = "主键id")
    private Long id;

    @ApiModelProperty(value = "招标文件附件目录id")
    private Long docId;

    @ApiModelProperty(value = "附件目录名称")
    private String docMenuName;

    @ApiModelProperty(value = "文件ossId")
    private Long fileId;
}
