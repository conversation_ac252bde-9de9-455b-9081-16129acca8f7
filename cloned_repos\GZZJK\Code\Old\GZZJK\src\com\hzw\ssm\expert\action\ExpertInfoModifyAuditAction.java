package com.hzw.ssm.expert.action;

import com.alibaba.fastjson.JSON;
import com.hzw.ssm.applets.dao.DictionaryMapper;
import com.hzw.ssm.applets.dao.EducationInfoMapper;
import com.hzw.ssm.applets.dao.TitleInfoMapper;
import com.hzw.ssm.applets.entity.Dictionary;
import com.hzw.ssm.applets.entity.EducationInfo;
import com.hzw.ssm.applets.entity.TitleInfo;
import com.hzw.ssm.expert.entity.*;
import com.hzw.ssm.expert.service.ExpertInfoAuditService;
import com.hzw.ssm.expert.service.ExpertInfoService;
import com.hzw.ssm.fw.base.BaseAction;
import com.hzw.ssm.fw.util.CommUtil;
import com.hzw.ssm.sys.sms.mwutil.MWSmsUtil;
import com.hzw.ssm.sys.system.entity.SmsRecordEntity;
import com.hzw.ssm.sys.user.entity.UserEntity;
import net.sf.json.JSONArray;
import net.sf.json.JsonConfig;
import net.sf.json.util.PropertyFilter;
import org.apache.struts2.convention.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import javax.servlet.http.HttpSession;
import java.util.Date;
import java.util.List;

@Namespace("/expertModifyAudit")
@ParentPackage(value = "default")
@Results( { @Result(name = "expertModifyAuditList", location = "/jsp/expert/expert_modify_audit_list.jsp"),
			@Result(name = "specialtyInfoList", location = "/jsp/expert/specialtyInfoList.jsp"),
			@Result(name = "initExpertModifyAuditInfo", location = "/jsp/expert/expert_modify_audit_view.jsp"),
			@Result(name = "initAuditReasonPage", location = "/jsp/expert/expert_audit_reasons.jsp"),
			@Result(name = "initReturnReasonPage", location = "/jsp/expert/expert_return_reasons.jsp")
		})
/**
 * 专家信息修改审核Action
 */
public class ExpertInfoModifyAuditAction extends BaseAction {
	
	private static final long serialVersionUID = -8823858495799309882L;

	//梦网短信账号
	@Value("${sms_userId}")
	private String userId;
	//梦网短信密码
	@Value("${sms_userPwd}")
	private String userPwd;
	//梦网主IP
	@Value("${sms_masterIpAddress}")
	private String masterIpAddress;

	//梦网备用IP1
	@Value("${sms_ipAddress1}")
	private String ipAddress1;
	//梦网备用IP2
	@Value("${sms_ipAddress2}")
	private String ipAddress2;
	
	/** 通用key*/
	private String key;
	
	/** 提示信息 */
	private String message;
	
	/** 专家基本信息*/
	private ExpertInfoEntity expertInfoEntity;
	
	/** 专家基本信息封装查询条件*/
	private ExpertInfoEntity colExpertInfo;
	
	/** 专家基本信息列表*/
	private List<ExpertInfoEntity> expertInfoList;
	/**
	 * 学历信息
	 */
	private List<EducationInfo> educationList;
	/**
	 * 职称信息
	 */
	private List<TitleInfo> titleInfoList;
	
	/**执业资格*/
	private List<ExpertPracticeEntity> practiceList;
	
	/**评标专业列表集合*/
	private List<ExpertMajorEntity> majorList;
	
	/**工作经历集合列表*/
	private List<ExpertExperienceEntity> experienceList;
	
	/** 评标专业 json格式字符串**/
	private String jsonSpecialtyInfo;
	
	/**审核信息列表*/
	private List<ExpertAuditEntity> auditList;    /**
	 * 职称字典
	 */
	private List<Dictionary> dictionaryList;

	
	/**专家信息修改记录列表*/
	private List<ExpertUpdateRecordEntity> updateRecordList;
	
	@Autowired
	private ExpertInfoAuditService expertInfoAuditService;
	
	@Autowired
	private ExpertInfoService expertInfoService;
	@Autowired
	private EducationInfoMapper educationInfoMapper;
	@Autowired
	private TitleInfoMapper titleInfoMapper; @Autowired
	private DictionaryMapper dictionaryMapper;

	/**
	 * 专家信息修改审核列表
	 * @return
	 * @throws Exception
	 */
	@Action("expertModifyAuditList")
	public String expertModifyAuditList() throws Exception {
		this.context();
		//当前session
		HttpSession session = getRequest().getSession();
		//当前用户信息
		UserEntity info = (UserEntity)session.getAttribute("userInfo");
		if(colExpertInfo==null){
			colExpertInfo=new ExpertInfoEntity();
		}
		if(info.getRole_id().equals("20161227193940035005")){//审核人员
			colExpertInfo.setStatus(5L);
			colExpertInfo.setStatus_("4,5,6,8");
			colExpertInfo.setAudit_user(info.getUser_id());
			colExpertInfo.setOrderStatus("when 5 then 1　when 4 then 2　when 5 then 3 when 6 then 4 when 8 then 5");
			this.getRequest().setAttribute("flag", "1");
		}else if(info.getRole_id().equals("20141017105452284107")){//主任审核
			colExpertInfo.setStatus(6L);
			colExpertInfo.setStatus_("8,4,6");
			colExpertInfo.setAudit_user(info.getUser_id());
			colExpertInfo.setOrderStatus("when 6 then 1　when 4 then 2　when 8 then 3 when 6 then 4");
			this.getRequest().setAttribute("flag", "2");
		}
		colExpertInfo.setPage(this.getPage());
		expertInfoList=expertInfoAuditService.queryPageExpertInfoAuditList(colExpertInfo);
		return "expertModifyAuditList";
	}
	
	/**
	 * 初始化专家信息修改审核页面
	 * @return
	 */
	@Action("initExpertModifyAuditInfo")
	public String initExpertModifyAuditInfo(){
		if(colExpertInfo==null){
			colExpertInfo=new ExpertInfoEntity();
			colExpertInfo.setUser_id(key);
		}
		expertInfoEntity=expertInfoService.getExpertInfoByUserIdOrId(colExpertInfo);
		practiceList=expertInfoService.queryPracticeByUserId(key);
		majorList=expertInfoService.queryMajorByUserId(key);
		experienceList=expertInfoService.queryExpertExperienceList(key);
		auditList=expertInfoAuditService.queryExpertAuditEntityList(key);
		updateRecordList=expertInfoService.queryExpertUpdateRecordList(key);

		dictionaryList = dictionaryMapper.queryTitle();
		// 学历和职称
		educationList = educationInfoMapper.queryEducationInfoList(key);
		titleInfoList = titleInfoMapper.queryTitleInfoList(key);
		for (TitleInfo titleInfo : titleInfoList) {
			List<String> pictures = JSON.parseArray(titleInfo.getPicture(), String.class);
			titleInfo.setPictures(pictures);
		}
		return "initExpertModifyAuditInfo";
	}
	
	/**
	 * 加载审批意见页面
	 * @return
	 */
	@Action("initAuditReasonPage")
	public String initAuditReasonPage(){
		return "initAuditReasonPage";
	}
	
	/**
	 * 加载退回原因页面
	 * @return
	 */
	@Action("initReturnReasonPage")
	public String initReturnReasonPage(){
		return "initReturnReasonPage";
	}
	
	/**
	 * 专家信息审核
	 * @return
	 */
	@Action("expertModifyDoAudit")
	public String expertModifyDoAudit(){
		try {
			this.context();
			//当前session
			HttpSession session = getRequest().getSession();
			//当前用户信息
			UserEntity info = (UserEntity)session.getAttribute("userInfo");
			
			//封装审核信息
			ExpertAuditEntity auditEntity=new ExpertAuditEntity();
			auditEntity.setId(CommUtil.getKey());
			auditEntity.setExpert_id(expertInfoEntity.getUser_id());
			auditEntity.setReason(expertInfoEntity.getAuditReason());
			auditEntity.setAudit_time(new Date());
			auditEntity.setAudit_user(info.getUser_id());
			auditEntity.setDelete_flag(0);
			if(expertInfoEntity.getStatus()==4){
				auditEntity.setStatus(1l);//不通过
			}else{
				auditEntity.setStatus(0L);//通过
			}

			ExpertInfoEntity entity = new ExpertInfoEntity();
			entity.setId(expertInfoEntity.getId());
			ExpertInfoEntity expertInfoByUserIdOrId = expertInfoService.getExpertInfoByUserIdOrId(entity);
			String mobilephone = expertInfoByUserIdOrId.getMobilephone();

			MWSmsUtil util = new MWSmsUtil();
			SmsRecordEntity smsRecordEntity = new SmsRecordEntity();
			smsRecordEntity.setSms_id(CommUtil.getKey());
			smsRecordEntity.setSms_mobile(mobilephone);

			Long status = expertInfoEntity.getStatus();
			// 修改审核 8- 审核通过 0-不通过
			if (8 == status) {
				smsRecordEntity.setSms_content("审核通过");
			} else if (0 == status) {
				smsRecordEntity.setSms_content("审核不通过");
			}
			int result = util.singleSend(userId, userPwd, masterIpAddress, ipAddress1, ipAddress2, smsRecordEntity);
			if (result != 0) {
				System.out.println("发送短信失败，请及时处理！");
			}
			
			if(info.getRole_id().equals("20161227193940035005")){//审核人员
				expertInfoAuditService.auditExpertInfo(expertInfoEntity,auditEntity);
			}else if(info.getRole_id().equals("20141017105452284107")){//主任审核
				expertInfoEntity.setAudit_time(new Date());
				expertInfoAuditService.auditExpertInfo(expertInfoEntity,auditEntity);
			}
			return this.expertModifyAuditList();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}
	
	/**
	 * 查询评标专业列表
	 * @return
	 */
	@Action("querySpecialtyInfoList")
	public String querySpecialtyInfoList(){
		List<SpecialtyInfoEntity> specialtyInfoList=expertInfoService.querySpecialtyInfoList();
		JsonConfig config=new JsonConfig();
		//通过JsonConfig的属性过滤器去除不需要的属性
		config.setJsonPropertyFilter(new PropertyFilter() {
			@Override
			public boolean apply(Object arg0, String arg1, Object arg2) {
				if(arg1.equals("spe_id")||arg1.equals("spe_parent")||arg1.equals("spe_name")){
					return false;//不忽略
				}
				return true;
			}
		});
		JSONArray jsonArray=JSONArray.fromObject(specialtyInfoList,config);
		jsonSpecialtyInfo=jsonArray.toString();
		//将属性名称修改zTree对应的属性名称
		jsonSpecialtyInfo=jsonSpecialtyInfo.replaceAll("spe_parent","pId").replaceAll("spe_name", "name").replaceAll("spe_id", "id");
		return "specialtyInfoList";
	}
	
	public String getMessage() {
		return message;
	}

	public void setMessage(String message) {
		this.message = message;
	}


	public ExpertInfoEntity getExpertInfoEntity() {
		return expertInfoEntity;
	}


	public void setExpertInfoEntity(ExpertInfoEntity expertInfoEntity) {
		this.expertInfoEntity = expertInfoEntity;
	}

	public List<ExpertMajorEntity> getMajorList() {
		return majorList;
	}

	public void setMajorList(List<ExpertMajorEntity> majorList) {
		this.majorList = majorList;
	}

	public String getJsonSpecialtyInfo() {
		return jsonSpecialtyInfo;
	}

	public void setJsonSpecialtyInfo(String jsonSpecialtyInfo) {
		this.jsonSpecialtyInfo = jsonSpecialtyInfo;
	}

	public List<ExpertExperienceEntity> getExperienceList() {
		return experienceList;
	}

	public void setExperienceList(List<ExpertExperienceEntity> experienceList) {
		this.experienceList = experienceList;
	}

	public String getKey() {
		return key;
	}

	public void setKey(String key) {
		this.key = key;
	}

	public ExpertInfoEntity getColExpertInfo() {
		return colExpertInfo;
	}

	public void setColExpertInfo(ExpertInfoEntity colExpertInfo) {
		this.colExpertInfo = colExpertInfo;
	}

	public List<ExpertInfoEntity> getExpertInfoList() {
		return expertInfoList;
	}

	public void setExpertInfoList(List<ExpertInfoEntity> expertInfoList) {
		this.expertInfoList = expertInfoList;
	}

	public List<ExpertPracticeEntity> getPracticeList() {
		return practiceList;
	}

	public void setPracticeList(List<ExpertPracticeEntity> practiceList) {
		this.practiceList = practiceList;
	}

	public List<ExpertAuditEntity> getAuditList() {
		return auditList;
	}

	public void setAuditList(List<ExpertAuditEntity> auditList) {
		this.auditList = auditList;
	}

	public List<ExpertUpdateRecordEntity> getUpdateRecordList() {
		return updateRecordList;
	}

	public void setUpdateRecordList(List<ExpertUpdateRecordEntity> updateRecordList) {
		this.updateRecordList = updateRecordList;
	}

	public List<TitleInfo> getTitleInfoList() {
		return titleInfoList;
	}

	public void setTitleInfoList(List<TitleInfo> titleInfoList) {
		this.titleInfoList = titleInfoList;
	}

	public List<EducationInfo> getEducationList() {
		return educationList;
	}

	public void setEducationList(List<EducationInfo> educationList) {
		this.educationList = educationList;
	}

	public List<Dictionary> getDictionaryList() {
		return dictionaryList;
	}

	public void setDictionaryList(List<Dictionary> dictionaryList) {
		this.dictionaryList = dictionaryList;
	}
}
