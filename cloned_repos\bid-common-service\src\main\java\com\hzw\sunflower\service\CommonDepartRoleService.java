package com.hzw.sunflower.service;

import com.hzw.sunflower.controller.response.CommonUserRoleRelationVO;
import com.hzw.sunflower.dto.CommonDeptRolesDTO;
import com.hzw.sunflower.entity.Role;

import java.util.List;

/**
 * @ClassName:CommonDepartRoleService
 * @Auther: lijinxin
 * @Description:
 * @Date: 2024/7/23 15:22
 * @Version: v1.0
 */
public interface CommonDepartRoleService {


    List<CommonDeptRolesDTO> getDepartRoles(Long userId);


    List<CommonUserRoleRelationVO> getUserRoleByIdentity(Long userId, Integer userIdentity);

    /**
     * 获取role集合
     * @param roleids
     * @return
     */
    List<Role>  getRoleList(List<Long> roleids);


}
