package com.hzw.ssm.fw.base;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.support.SqlSessionDaoSupport;
import org.springframework.beans.factory.annotation.Autowired;

import com.hzw.ssm.fw.util.PageEntity;

public class BaseDAO<T, PK extends Serializable> extends SqlSessionDaoSupport {

	private static final Log log = LogFactory.getLog(BaseDAO.class);

	@Autowired(required = true)
	@Resource(name = "sqlSessionFactory")
	public void setSuperSqlSessionFactory(SqlSessionFactory sqlSessionFactory) {
		super.setSqlSessionFactory(sqlSessionFactory);
	}

	// 保存
	public T save(T entity) {
		try {
			this.getSqlSession().insert(
					entity.getClass().getSimpleName() + ".save", entity);
			return entity;
		} catch (RuntimeException re) {
			log
					.error("save " + entity.getClass().getName()
							+ " failed :{}", re);
			throw re;
		}
	}

	// 更新
	public void update(T entity) {
		try {
			this.getSqlSession().update(
					entity.getClass().getSimpleName() + ".update", entity);
		} catch (RuntimeException re) {
			log.error("update " + entity.getClass().getName() + " failed :{}",
					re);
			throw re;
		}
	}

	// 删除
	public void delete(T entity) {
		try {
			this.getSqlSession().delete(
					entity.getClass().getSimpleName() + ".delete", entity);
		} catch (RuntimeException re) {
			log.error("delete " + entity.getClass().getName() + " failed :{}",
					re);
			throw re;
		}
	}
	
	// 根据id删除某个对象
    public void delete(Class<T> entityClass, PK id) {
        try {
            this.getSqlSession().delete(entityClass.getSimpleName() + ".deleteById", id);
        } catch (RuntimeException re) {
            log.error("delete " + entityClass.getName() + "failed :{}", re);
            throw re;
        }
    }
    
 // 根据id加载某个对象
    @SuppressWarnings("unchecked")
    public T findById(Class<T> entityClass, PK id) {
        try {
            return (T) this.getSqlSession().selectOne(entityClass.getSimpleName() + ".findById", id);
        } catch (RuntimeException re) {
            log.error("findById " + entityClass.getName() + "failed :{}", re);
            throw re;
        }
    }

    // 查找所有的对象
    @SuppressWarnings("unchecked")
    public List<T> findAll(Class<T> entityClass) {
        try {
            return this.getSqlSession().selectList(entityClass.getSimpleName() + ".findAll");
        } catch (RuntimeException re) {
            log.error("findAll " + entityClass.getName() + "failed :{}", re);
            throw re;
        }
    }

    // 根据查询参数，当前页数，每页显示的数目得到分页列表
    @SuppressWarnings("rawtypes")
    public PageEntity queryPage(Class<T> entityClass, Map param, int currentPage, int pageSize) {
        try {
            PageEntity page = new PageEntity();
            page.init((Integer) this.getSqlSession().selectOne(entityClass.getSimpleName() + ".getTotalCounts", param), pageSize, currentPage,
                    this.getSqlSession().selectList(entityClass.getSimpleName() + ".queryPage", param));
            return page ;
        } catch (RuntimeException re) {
            log.error("findList " + entityClass.getName() + "failed :{}", re);
            throw re;
        }
    }
}
