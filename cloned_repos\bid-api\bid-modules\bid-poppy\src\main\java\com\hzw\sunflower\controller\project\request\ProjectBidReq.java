package com.hzw.sunflower.controller.project.request;

import com.hzw.sunflower.entity.ProjectBidSection;
import com.hzw.sunflower.entity.SectionExpertSpecialty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2021/4/13 9:23
 * @description：项目标段请求参数
 * @version: 1.0
 */
@Data
public class ProjectBidReq {

    /**
     * 项目Id
     */
    @ApiModelProperty(value = "项目ID", position = 1, required = true)
    @NotNull(message = "项目ID不能为空")
    private Long projectId;

    /**
     * 包件状态（0-无包，1-有包)
     */
    @ApiModelProperty(value = "包件状态（0-无包，1-有包）", position = 2, required = true)
    @NotNull(message = "包件状态不能为空")
    private Integer packageSegmentStatus;

    /**
     * 0：标段，1：包
     */
    @ApiModelProperty(value = "0：标段，1：包", position = 3)
    private Integer bidTypeName;

    /**
     * 项目标段信息
     */
    @ApiModelProperty(value = "项目标段信息", position = 4)
    private List<ProjectBidSection> projectBidSectionList;



    /**
     * 操作类型(1:暂存，2，提交)
     */
    @ApiModelProperty(value = "操作类型(1:暂存，2，提交)", position = 5, required = true)
    @NotNull(message = "操作类型不能为空")
    private Integer operationType;

    @ApiModelProperty(value = "支付ID集合", position = 6, required = true)
    private List<Long> payIds;

    @ApiModelProperty(value = "关注ID集合", position = 7, required = true)
    private List<Long> applyIds;

    @ApiModelProperty(value = "是否为重新招标项目(true:是，false:否)", position = 8, required = true)
    private Boolean reTender;

    @ApiModelProperty(value = "资格预审方式 0 按包，1 按项目", position = 9)
    private String preQualificationMtd;

    @ApiModelProperty(value = "响应文件递交截至时间")
    private Date submitEndTime;

}
