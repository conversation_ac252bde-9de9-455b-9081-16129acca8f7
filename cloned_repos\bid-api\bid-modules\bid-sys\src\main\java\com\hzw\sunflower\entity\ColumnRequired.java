package com.hzw.sunflower.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.hzw.sunflower.common.BaseBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@ApiModel(description = "项目字段必填配置表")
@TableName("s_column_required")
@Data
public class ColumnRequired extends BaseBean implements Serializable {

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "模块")
    private String moduleName;

    @ApiModelProperty(value = "模块编码")
    private String moduleCode;

    @ApiModelProperty(value = "字段名称")
    private String columnName;

    @ApiModelProperty(value = "字段编码")
    private String code;

    @ApiModelProperty(value = "关联功能 1.标书费 2.电子开评标 3.保证金")
    private Integer relationModule;

    @ApiModelProperty(value = "是否代理机构必填 1.是 2.否")
    private Integer isRequiredAgent;

    @ApiModelProperty(value = "是否置灰")
    private Integer disabled;

    @ApiModelProperty(value = "排序")
    private Integer sort;
}
