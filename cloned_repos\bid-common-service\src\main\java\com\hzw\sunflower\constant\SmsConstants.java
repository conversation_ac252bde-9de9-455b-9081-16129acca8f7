package com.hzw.sunflower.constant;

/**
 * <AUTHOR>
 * @date ：Created in 2021/5/12 9:56
 * @description：短信常量
 * @modified By：`
 * @version: 1.0
 */
public class SmsConstants {

    /**
     * 短信验证码失效时间3分钟
     */
    public static final Integer SMS_CODE_TIME = 1000 * 60 * 3;

    /**
     * 图片验证码宽度
     */
    public static final Integer IMG_CODE_WIDTH = 200;

    /**
     * 图片验证码长度
     */
    public static final Integer IMG_CODE_LENGTH = 4;

    /**
     * 图片验证码模糊程度
     */
    public static final Integer IMG_CODE_VAGUE = 15;

    /**
     * 图片验证码高度
     */
    public static final Integer IMG_CODE_HEIGHT = 100;

    public static final String SMS_TYPE_NOTNULL = "短信类型不能为空";

    public static final String SMS_TYPE_ILLEGAL = "短信类型非法";

    public static final String SMS_PHONE_NOTNULL = "手机号不能为空";

    public static final String SMS_PHONE_FORMAT_ILLEGAL = "手机号非法";

    public static final String SMS_PHONE_CODE = "短信已发送，不能重复获取，请稍后再试！";

    public static final String SMS_SUCCESS = "短信发送成功！";

    public static final String SMS_ERROR = "短信发送失败！";

    public static final String IMG_SUCCESS = "图片验证码获取成功！";

    public static final String IMG_ERROR = "图片获取失败！";

    public static final String SMS_CODE_ERROR = "短信验证码不存在，请发送！";

    public static final String SMS_CODE_EQ_ERROR = "请输入正确的验证码";

    /**
     * 短信发送接口
     */
    public static final String SINGLE_SEND = "single_send";

    /**
     * 获取短信余额
     */
    public static final String GET_BALANCEALL = "get_balanceAll";

    /**
     * 获取回复记录
     */
    public static final String GET_MO = "get_mo";

    /**
     * 二次操作短信验证
     */
    public static final String OPERATION_IMAGE = "operation_image";

}
