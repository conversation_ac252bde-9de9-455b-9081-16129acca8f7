///***
/// * common Refresh Tip
/// * when just pull down the list view a little(bigger then 4lpx and less then 10 lpx) for DataList Widget
/// *
/// * <AUTHOR>
/// * @date 20101023
/// */

import "package:flutter/material.dart";

class DefaultRefreshPullDownTipWidget extends StatelessWidget {

  const DefaultRefreshPullDownTipWidget({Key key}) : super(key: key);

  static const String _pullDownTip = '下拉刷新';

  @override
  Widget build(BuildContext context) {
    return Container(
      constraints: BoxConstraints(minHeight: 4, maxHeight: 20),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: <Widget>[
          Icon(
            Icons.arrow_downward,
            size: 15,
          ),
          Text(_pullDownTip),
        ],
      ),
    );
  }

}
