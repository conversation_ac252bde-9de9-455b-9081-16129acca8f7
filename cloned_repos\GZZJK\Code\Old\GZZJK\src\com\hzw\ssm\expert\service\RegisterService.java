package com.hzw.ssm.expert.service;

import com.hzw.ssm.expert.dao.RegisterMapper;
import com.hzw.ssm.fw.base.BaseService;
import com.hzw.ssm.fw.util.CommUtil;
import com.hzw.ssm.fw.util.MD5Util;
import com.hzw.ssm.fw.util.SysConstants;
import com.hzw.ssm.sys.user.entity.UserEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

/**
 * 注册页面服务类
 * <AUTHOR> 2014-10-09
 *
 */
@Service
public class RegisterService extends BaseService {

	@Autowired
	private RegisterMapper registerMapper;

	/**
	 * 验证登录名是否存在
	 * @return
	 */
	public int checkLoginCodeExist(String login_code){
		return registerMapper.checkLoginCodeExist(login_code);
	}

	/**
	 * 验证手机号码是否存在
	 * @param mobile
	 * @return
	 */
	public int checkMobileExist(String mobile){
		return registerMapper.checkMobileExist(mobile);
	}

	/**
	 * 注册保存
	 * @param userEntity
	 */
	@Transactional
	public void doRegister(UserEntity userEntity){
		if(userEntity.getUser_id()==null || userEntity.getUser_id().equals("")){
			userEntity.setUser_id(CommUtil.getKey());
			userEntity.setCreate_id(userEntity.getUser_id());
			userEntity.setCreateTime(new Date());
			userEntity.setRole_id(SysConstants.ROLE_ID.EXPERT);//专家
			userEntity.setPassword(MD5Util.reverse(userEntity.getPassword()));
		}
		registerMapper.doRegister(userEntity);
	}
}
