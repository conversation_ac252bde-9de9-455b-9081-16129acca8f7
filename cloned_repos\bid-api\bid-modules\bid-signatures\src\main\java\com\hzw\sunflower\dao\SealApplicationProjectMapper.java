package com.hzw.sunflower.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hzw.sunflower.dto.SealApplicationProjectDTO;
import com.hzw.sunflower.entity.SealApplicationProject;
import com.hzw.sunflower.entity.condition.SealApplicationProjectCondition;

import java.util.List;

/**
* SealApplicationProjectMapper接口
*
*/
public interface SealApplicationProjectMapper extends BaseMapper<SealApplicationProject> {

}