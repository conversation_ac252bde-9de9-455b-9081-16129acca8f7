package com.hzw.ssm.applets.action;

import com.hzw.ssm.applets.entity.BidEvaluation;
import com.hzw.ssm.applets.service.BidEvaluationService;
import com.hzw.ssm.applets.utils.GetJsonReqData;
import com.hzw.ssm.applets.utils.GetJsonRespData;
import com.hzw.ssm.applets.utils.JsonToObject;
import com.hzw.ssm.applets.utils.ResultJson;
import com.hzw.ssm.fw.base.BaseAction;
import net.sf.json.JSONObject;
import org.apache.struts2.convention.annotation.Action;
import org.apache.struts2.convention.annotation.Namespace;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 评标专业
 *
 * <AUTHOR>
 * @Date 2021-02-03 10:17
 * @Version 1.0
 */
@Namespace("/applets/bidEvaluationAction")
public class BidEvaluationAction extends BaseAction {

    @Autowired
    private BidEvaluationService bidEvaluationService;

    /**
     * 新增
     *
     * @return
     */
    @Action("save")
    public String save() {

        JSONObject jsonReqData = GetJsonReqData.getJsonReqData();
        String toString = jsonReqData.getJSONObject("formData").toString();
        BidEvaluation bidEvaluation = JsonToObject.jsonToObject(toString, BidEvaluation.class);
        ResultJson resultJson = bidEvaluationService.insertBidEvaluation(bidEvaluation);
        GetJsonRespData.getJsonRespData(resultJson);
        return null;
    }

    /**
     * 编辑
     *
     * @return
     */
    @Action("update")
    public String update() {

        JSONObject jsonReqData = GetJsonReqData.getJsonReqData();
        String toString = jsonReqData.getJSONObject("formData").toString();
        BidEvaluation bidEvaluation = JsonToObject.jsonToObject(toString, BidEvaluation.class);
        ResultJson resultJson = bidEvaluationService.updateBidEvaluation(bidEvaluation);
        GetJsonRespData.getJsonRespData(resultJson);
        return null;
    }

    /**
     * 删除
     *
     * @return
     */
    @Action("delete")
    public String delete() {

        JSONObject jsonReqData = GetJsonReqData.getJsonReqData();
        String toString = jsonReqData.getJSONObject("formData").toString();
        BidEvaluation bidEvaluation = JsonToObject.jsonToObject(toString, BidEvaluation.class);
        ResultJson resultJson = bidEvaluationService.deleteBidEvaluation(bidEvaluation);
        GetJsonRespData.getJsonRespData(resultJson);
        return null;
    }

    /**
     * 查询单个
     *
     * @return
     */
    @Action("getOne")
    public String getOne() {

        JSONObject jsonReqData = GetJsonReqData.getJsonReqData();
        String toString = jsonReqData.getJSONObject("formData").toString();
        BidEvaluation bidEvaluation = JsonToObject.jsonToObject(toString, BidEvaluation.class);
        ResultJson resultJson = bidEvaluationService.getBidEvaluation(bidEvaluation);
        GetJsonRespData.getJsonRespData(resultJson);
        return null;
    }

    /**
     * 查询所有
     *
     * @return
     */
    @Action("all")
    public String all() {

        JSONObject jsonReqData = GetJsonReqData.getJsonReqData();
        String toString = jsonReqData.getJSONObject("formData").toString();
        BidEvaluation bidEvaluation = JsonToObject.jsonToObject(toString, BidEvaluation.class);
        ResultJson resultJson = bidEvaluationService.getBidEvaluations(bidEvaluation);
        GetJsonRespData.getJsonRespData(resultJson);
        return null;
    }

    /**
     * 查询所有
     *
     * @return
     */
    @Action("tree")
    public String tree() {

        JSONObject jsonReqData = GetJsonReqData.getJsonReqData();
        JSONObject formData = jsonReqData.getJSONObject("formData");
        String speParent = formData.getString("speParent");
        ResultJson resultJson = bidEvaluationService.tree(speParent);
        GetJsonRespData.getJsonRespData(resultJson);
        return null;
    }
}
