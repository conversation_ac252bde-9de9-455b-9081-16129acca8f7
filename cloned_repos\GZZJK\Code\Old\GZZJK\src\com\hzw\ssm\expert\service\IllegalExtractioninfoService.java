/**
 * 
 */
package com.hzw.ssm.expert.service;

import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import org.apache.struts2.ServletActionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.hzw.ssm.date.exception.ParameterException;
import com.hzw.ssm.date.service.ZJKCalendarService;
import com.hzw.ssm.expert.dao.IllegalExtractioninfoMapper;
import com.hzw.ssm.expert.entity.IllegalExtractioninfoEntity;
import com.hzw.ssm.expert.entity.MonitorEntity;
import com.hzw.ssm.fw.base.BaseService;
import com.hzw.ssm.fw.util.CommUtil;
import com.hzw.ssm.fw.util.DateUtil;
import com.hzw.ssm.fw.util.SysConstants;
import com.hzw.ssm.sys.user.entity.UserEntity;
import com.hzw.ssm.util.file.FileUtils;

/**
 * <AUTHOR>
 *
 */
@Service
public class IllegalExtractioninfoService extends BaseService{

	private String ASE_ORDER="1";
	private String DESC_ORDER="2";
	@Autowired
	private IllegalExtractioninfoMapper illegalExtractioninfoMapper;

	@Autowired
	private ZJKCalendarService zjkCalendarService;
	/**
	 * 根据userid查询违规处室业务
	 * @return
	 */
	public List<IllegalExtractioninfoEntity> queryMonitor(IllegalExtractioninfoEntity entity) {
		entity.setBeginTime(entity.getBeginTime());
		entity.setEndTime(entity.getEndTime());
		StringBuffer sb = new StringBuffer();
		//单一条件排序不并行查询
		if(entity.getOpeningreOrder()!=null&& entity.getOpeningreOrder().equalsIgnoreCase("1")) {
			sb.append("openingtime asc");
		}else if( entity.getOpeningreOrder()!=null&&entity.getOpeningreOrder().equalsIgnoreCase("2")){
			sb.append("openingtime desc");
		}else if( entity.getExtractNumOrder()!=null&& entity.getExtractNumOrder().equalsIgnoreCase("3")){
			sb.append("extract_num asc");

		} else if(  entity.getExtractNumOrder()!=null&& entity.getExtractNumOrder().equalsIgnoreCase("4")){
			sb.append("extract_num desc");

		}else if(  entity.getHandletimeOrder()!=null&&  entity.getHandletimeOrder().equalsIgnoreCase("5")){
			sb.append("handletime asc ");
		} else if(entity.getHandletimeOrder()!=null&&  entity.getHandletimeOrder().equalsIgnoreCase("6")){
			sb.append("handletime desc ");
		}else {//默认一个条件
			sb.append("openingtime desc");
		}
		entity.setReorder(sb.toString());
		List<IllegalExtractioninfoEntity> list = illegalExtractioninfoMapper.queryPageillegalExtraction(entity);
		return list;
	}







	/**
	 * 根据userid查询违规处室业务
	 * @return
	 */
	public List<IllegalExtractioninfoEntity> queryILLMonitor(IllegalExtractioninfoEntity entity) {

		entity.setBeginTime(entity.getBeginTime());
		entity.setEndTime(entity.getEndTime());
		StringBuffer sb = new StringBuffer();
		if(ASE_ORDER.equals(entity.getOpeningreOrder())) {
			sb.append("openingtime asc ,");
		}else {
			sb.insert(0,"openingtime desc ,");

		}
		if(ASE_ORDER.equals(entity.getExtractNumOrder())) {
			sb.append("EXTRACT_TIMES asc ,");		
		}else {
			sb.insert(0,"EXTRACT_TIMES desc ,");
		}

		if(ASE_ORDER.equals(entity.getHandletimeOrder())) {
			sb.append("handletime asc ,");
		}else {
			sb.insert(0,"handletime desc,");
		}
		entity.setReorder(sb.substring(0, sb.lastIndexOf(",")));
		List<IllegalExtractioninfoEntity> list = illegalExtractioninfoMapper.queryillegalExtraction(entity);
		return list;
	}


	public Boolean modifyMonitor(IllegalExtractioninfoEntity entity) {

		illegalExtractioninfoMapper.modifyMonitor(entity);
		return true;
	}


	public List<MonitorEntity> checkExtractFrequenc(IllegalExtractioninfoEntity entity) {
		List<MonitorEntity> detailList = new ArrayList<MonitorEntity>();

		try {
			int day=-5;
			Calendar cal = Calendar.getInstance();
			cal.setTimeInMillis(System.currentTimeMillis());
			cal.add(5, day);
			Date wa=cal.getTime();
			while(true) {
				if(zjkCalendarService.isHoliday(wa)) {
					break;
				}else {
					day=day-1;
					cal.add(5, day);
					wa=cal.getTime();
				}
			}

			entity.setEndTime(wa);

			List<IllegalExtractioninfoEntity> list = illegalExtractioninfoMapper.checkExtractFrequenc(entity);

			for (IllegalExtractioninfoEntity ille : list) {
				MonitorEntity m = new MonitorEntity();
				m.setUserName(ille.getUserName());
				m.setProjectName(ille.getProjectName());
				m.setProjectCode(ille.getProjectCode());
				m.setTender(ille.getTender());
				m.setDecimationbatch(ille.getDecimationbatch());
				m.setOpeningTime(DateUtil.dateToString(ille.getOpeningTime(), "yyyy-MM-dd HH:mm:ss"));
				m.setOpeningAddress(ille.getOpeningAddress());
				detailList.add(m);
			}
		} catch (ParameterException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return detailList;

	}

	public Boolean modifyMonitorill(IllegalExtractioninfoEntity entity) {
		entity.setHandleTime(new Date());
		illegalExtractioninfoMapper.modifyMonitorFile(entity);
		return null;
	}

	/**
	 * 文件复制处理
	 * @param src
	 * @param dst
	 * @return
	 */
	public synchronized static boolean copyFile(File src,File dst){
		if(src==null||dst==null){
			return false;
		}
		int BUFFER_SIZE = 16*1024; 
		InputStream ins=null;
		OutputStream os=null;
		try {
			ins=new BufferedInputStream(new FileInputStream(src),BUFFER_SIZE);
			os=new BufferedOutputStream(new FileOutputStream(dst),BUFFER_SIZE);
			byte[]buffer=new byte[BUFFER_SIZE];
			int len=0;
			while((len=ins.read(buffer))>0){
				os.write(buffer,0, len);
			}
			os.flush();
		} catch (FileNotFoundException e) {
			e.printStackTrace();
			return false;
		} catch (IOException e) {
			e.printStackTrace();
			return false;
		}catch(Exception e){
			e.printStackTrace();
			return false;
		}finally{
			try {
				if(ins!=null){
					ins.close();
				}
				if(os!=null){
					os.close();
				}
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
		return true;
	}
	public UserEntity getUserNameToId(String userName) {
		UserEntity user = new UserEntity();
		List<UserEntity> entity=illegalExtractioninfoMapper.getUserNameToId(userName);
		if(entity!=null&&entity.size()>0) {
			user=entity.get(0);
		}
		return user;
	}

	public List<IllegalExtractioninfoEntity> queryPageExpertsDrawnAfterBidOpening(IllegalExtractioninfoEntity entity) {
		entity.setBeginTime(entity.getBeginTime());
		entity.setEndTime(entity.getEndTime());
		StringBuffer sb = new StringBuffer();
		//单一条件排序不并行查询
		if(entity.getOpeningreOrder()!=null&& entity.getOpeningreOrder().equalsIgnoreCase("1")) {
			sb.append("openingtime asc");
		}else if( entity.getOpeningreOrder()!=null&&entity.getOpeningreOrder().equalsIgnoreCase("2")){
			sb.append("openingtime desc");
		}else if( entity.getExtractNumOrder()!=null&& entity.getExtractNumOrder().equalsIgnoreCase("3")){
			sb.append("extractTimes asc");

		} else if(  entity.getExtractNumOrder()!=null&& entity.getExtractNumOrder().equalsIgnoreCase("4")){
			sb.append("extractTimes desc");

		}else {//默认一个条件
			sb.append("openingtime desc");
		}
		entity.setReorder(sb.toString());
		List<IllegalExtractioninfoEntity> list = illegalExtractioninfoMapper.queryPageExpertsDrawnAfterBidOpening(entity);

		return list;
	}


	public List<IllegalExtractioninfoEntity> pdfMonitor(IllegalExtractioninfoEntity entity) {
		entity.setBeginTime(entity.getBeginTime());
		entity.setEndTime(entity.getEndTime());
		StringBuffer sb = new StringBuffer();
		//单一条件排序不并行查询
		if(entity.getOpeningreOrder()!=null&& entity.getOpeningreOrder().equalsIgnoreCase("1")) {
			sb.append("openingtime asc");
		}else if( entity.getOpeningreOrder()!=null&&entity.getOpeningreOrder().equalsIgnoreCase("2")){
			sb.append("openingtime desc");
		}else if( entity.getExtractNumOrder()!=null&& entity.getExtractNumOrder().equalsIgnoreCase("3")){
			sb.append("extractTimes asc");

		} else if(  entity.getExtractNumOrder()!=null&& entity.getExtractNumOrder().equalsIgnoreCase("4")){
			sb.append("extractTimes desc");

		}else {//默认一个条件
			sb.append("openingtime desc");
		}
		entity.setReorder(sb.toString());
		List<IllegalExtractioninfoEntity> list = illegalExtractioninfoMapper.pdfMonitorillegalExtraction(entity);

		return list;
	}
}
