package com.hzw.ssm.applets.action;

import com.alibaba.fastjson.JSONObject;
import com.google.gson.Gson;
import com.hzw.ssm.applets.exception.AppletsException;
import com.hzw.ssm.applets.exception.ExceptionEnum;
import com.hzw.ssm.applets.utils.*;
import com.hzw.ssm.fw.base.BaseAction;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.apache.struts2.ServletActionContext;
import org.apache.struts2.convention.annotation.Action;
import org.apache.struts2.convention.annotation.Namespace;
import org.apache.struts2.dispatcher.multipart.MultiPartRequestWrapper;
import org.springframework.web.context.ServletContextAware;

import javax.servlet.ServletContext;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.util.*;

import static org.apache.struts2.ServletActionContext.getServletContext;

/**
 * 文件上传
 */
@Namespace("/fileUploadAction")
public class FileUploadAction extends BaseAction implements ServletContextAware {

    private static final Logger logger = Logger.getLogger(FileUploadAction.class);

    private ServletContext servletContext;

    public static String uploadFolder = "expertFile/";
    private static String cardFolder = "applets/card/";
    private static String otherFolder = "applets/upload/";

    @Override
    public void setServletContext(ServletContext servletContext) {
        this.servletContext = servletContext;
    }

    /**
     * 普通文件上传
     *
     * @return
     */
    @Action("wxUpload")
    public String wxUpload() {
        ResultJson resultJson = null;
        try {
            List<Map<String, Object>> list = new ArrayList<>();
            Map<String, Object> map = new HashMap<>();

            MultiPartRequestWrapper wrapper = (MultiPartRequestWrapper) ServletActionContext.getRequest();
            Enumeration fileParameterNames = wrapper.getFileParameterNames();

            String mkdirDate = WxDateUtil.stampToDate(System.currentTimeMillis(), "yyyy/MM/dd");

//            String abspath = servletContext.getRealPath(uploadFolder + otherFolder + mkdirDate);
            String abspath = ServletActionContext.getServletContext().getRealPath("") + File.separator + uploadFolder + otherFolder + mkdirDate;
            File uploadDir = new File(abspath);
            if (!uploadDir.exists()) {
                uploadDir.mkdirs();
            }

            while (fileParameterNames != null && fileParameterNames.hasMoreElements()) {

                String inputName = (String) fileParameterNames.nextElement();
                String[] contentType = wrapper.getContentTypes(inputName);

                if (contentType != null && contentType.length != 0) {

                    String[] fileNames = wrapper.getFileNames(inputName);
                    File[] files = wrapper.getFiles(inputName);

                    for (int i = 0; i < files.length; i++) {

                        String fileName = fileNames[i];
                        File file = files[i];

                        String newFileName = randName() + fileName.substring(fileName.lastIndexOf("."));

                        File uploadedFile = new File(abspath, newFileName);
                        byte[] buffer = new byte[1024];
                        FileOutputStream fos = new FileOutputStream(uploadedFile);
                        InputStream in = new FileInputStream(file);

                        int num = 0;
                        while ((num = in.read(buffer)) > 0) {
                            fos.write(buffer, 0, num);
                        }
                        in.close();
                        fos.close();

                        map.put("fileName", uploadFolder + otherFolder + mkdirDate + "/" + newFileName);
                        list.add(map);
                    }
                }
            }
            resultJson = new ResultJson(list);
        } catch (Exception e) {
            logger.error(e.getMessage());
            resultJson = new ResultJson(-1, "请求或处理失败");
        }

        try {

            String toString1 = new Gson().toJson(resultJson);
            HttpServletResponse response = ServletActionContext.getResponse();
            response.setContentType("text/html;charset=utf-8");
            PrintWriter out = response.getWriter();
            out.print(toString1);
            out.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 身份证上传并识别
     *
     * @return
     */
    @Action("wxCardUpload")
    public String wxCardUpload() {

        ResultJson resultJson = null;
        String abspath = null;
        String newFileName = null;
        List<Map<String, Object>> list = new ArrayList<>();
        Map<String, Object> map = new HashMap<>();
        try {

            MultiPartRequestWrapper wrapper = (MultiPartRequestWrapper) ServletActionContext.getRequest();
            Enumeration fileParameterNames = wrapper.getFileParameterNames();

            String mkdirDate = WxDateUtil.stampToDate(System.currentTimeMillis(), "yyyy/MM/dd");
//            abspath = servletContext.getRealPath(uploadFolder + cardFolder + mkdirDate);
            abspath = ServletActionContext.getServletContext().getRealPath("") + File.separator + uploadFolder + cardFolder + mkdirDate;
            File uploadDir = new File(abspath);
            if (!uploadDir.exists()) {
                uploadDir.mkdirs();
            }

            while (fileParameterNames != null && fileParameterNames.hasMoreElements()) {

                String inputName = (String) fileParameterNames.nextElement();
                String[] contentType = wrapper.getContentTypes(inputName);

                if (contentType != null && contentType.length != 0) {

                    String[] fileNames = wrapper.getFileNames(inputName);
                    File[] files = wrapper.getFiles(inputName);

                    String fileName = fileNames[0];
                    File file = files[0];
                    long length = file.length();
                    if (length / 1024 > 2048) {

                    }

                    // 身份证识别
                    String url = SmallWxUtil.uploadCard2("photo");
                    String postForm = MyHttpUtils.postForm(url, file);
                    JSONObject object = JSONObject.parseObject(postForm);
                    String errcode = object.getString("errcode");
                    if (!"0".equals(errcode)) {
                        throw new AppletsException("身份识别失败，请上传有效证件");
                    }

                    // 上传图片
                    newFileName = randName() + fileName.substring(fileName.lastIndexOf("."));

                    File uploadedFile = new File(abspath, newFileName);
                    byte[] buffer = new byte[1024];
                    FileOutputStream fos = new FileOutputStream(uploadedFile);
                    InputStream in = new FileInputStream(file);

                    int num = 0;
                    while ((num = in.read(buffer)) > 0) {
                        fos.write(buffer, 0, num);
                    }
                    in.close();
                    fos.close();

                    String key = uploadFolder + cardFolder + mkdirDate + "/" + newFileName;

                    map.put("fileName", key);
                    map.put("cardInfo", object);
                    list.add(map);
                }
            }
            resultJson = new ResultJson(list);
        } catch (AppletsException e) {
            // 失败删除图片
//            File fileTemp = new File(abspath + "/" + newFileName);
//            boolean falg = fileTemp.exists();
//            if (falg) {
//                fileTemp.delete();
//            }
            resultJson = new ResultJson(-1, e.getMsgDes());
        } catch (Exception e) {
            logger.error(e.getMessage());
            resultJson = new ResultJson(-1, "请求或处理失败");
        }

        GetJsonRespData.getJsonRespData(resultJson);
        return null;
    }


    @Action("download")
    public String download() {

        HttpServletRequest request = ServletActionContext.getRequest();
        HttpServletResponse response = ServletActionContext.getResponse();
        InputStream in = null;
        OutputStream out = null;
        try {

            //获得请求文件名
            String filenameISO = request.getParameter("filename");
            if (StringUtils.isBlank(filenameISO)) {
                throw new Exception("文件fileUrl为空");
            }

            String filenameUTF = new String(filenameISO.getBytes("ISO-8859-1"), "UTF-8");
            response.setContentType(getServletContext().getMimeType(filenameUTF));
            //设置Content-Disposition
            //filename是ISO-8859-1的 如果改成utf-8 下载的文件名中就不能出现中文了
            long timeMillis = System.currentTimeMillis();
            String suffixName = filenameISO.substring(filenameISO.lastIndexOf("."));
            String new_filenameISO = timeMillis + suffixName;
            response.setHeader("Content-Disposition", "attachment;filename=" + new_filenameISO);
            response.setHeader("Content-type", "charset=UTF-8");
            response.setCharacterEncoding("UTF-8");
            //读取目标文件，通过response将目标文件写到客户端
//            String abspath = servletContext.getRealPath(uploadFolder);
            String abspath = ServletActionContext.getServletContext().getRealPath("") + File.separator + uploadFolder;
            in = new FileInputStream(abspath + "/" + filenameUTF);
            out = response.getOutputStream();
            //写文件
            int b;
            while ((b = in.read()) != -1) {
                out.write(b);
            }
            GetJsonRespData.getJsonRespData(new ResultJson(null));
        } catch (Exception e) {
            logger.error(e.getMessage());
            ResultJson resultJson = new ResultJson(ExceptionEnum.SERVER_IS_BUSY.getCode(), ExceptionEnum.SERVER_IS_BUSY.getMessage());
            GetJsonRespData.getJsonRespData(resultJson);
        } finally {
            try {
                if (null != in) {
                    in.close();
                }
                if (null != out) {
                    out.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }

        }

        return null;
    }

    /**
     * 随机
     *
     * @return
     */
    @SuppressWarnings("static-access")
    private static String randName() {
        Calendar calendar = Calendar.getInstance();
        StringBuffer sb = new StringBuffer();
        sb.append(calendar.get(calendar.YEAR));
        sb.append(calendar.get(calendar.MONTH) + 1);
        sb.append(calendar.get(calendar.DATE));
        sb.append(calendar.get(calendar.HOUR));
        sb.append(calendar.get(calendar.MINUTE));
        sb.append(calendar.get(calendar.SECOND));
        Random random = new Random();
        Integer n = random.nextInt(999999);
        sb.append(n.toString());
        return sb.toString();
    }

}
