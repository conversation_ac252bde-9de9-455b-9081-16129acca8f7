package com.hzw.sunflower.controller.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@ApiModel(description = "保证金关联申请列表")
@Data
public class BondApplyRelationVo implements Serializable {

    @ApiModelProperty("项目编号")
    private String projectNum;

    @ApiModelProperty("项目名称")
    private String projectName;

    @ApiModelProperty("包名称")
    private String packageName;

    @ApiModelProperty("响应文件递交截止时间")
    private Date submitEndTime;

    @ApiModelProperty("申请内容")
    private String content;

    @ApiModelProperty("申请类型，1：申请关联  2：异常关联")
    private Integer type;

    @ApiModelProperty("申请状态，1.待处理 2.已退回 3.已处理")
    private Integer status;

    @ApiModelProperty("退回原因")
    private String returnMsg;

    @ApiModelProperty("关联id")
    private Long id;

    @ApiModelProperty("包段编号")
    private String packageNum;

    @ApiModelProperty(value = "项目id")
    private Long projectId;

    @ApiModelProperty(value = "标段id")
    private Long sectionId;

    @ApiModelProperty(value = "供应商id")
    private Long companyId;

    @ApiModelProperty(value = "支付凭证文件id")
    private String paymentVoucherIds;

}
