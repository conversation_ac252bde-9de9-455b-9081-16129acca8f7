<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzw.sunflower.dao.BondOfflineCompanyMapper">

    <select id="queryFromBondOfflineCompany"
            resultType="com.hzw.sunflower.controller.response.OfflineCompanyVo">
        SELECT
            id AS company_id,
            company_name,
            organization_num,
            business_license,
            license_file_id,
            is_company_abroad
        FROM
            t_offline_company
        WHERE
            is_delete = 0
            AND company_name LIKE CONCAT('%',#{companyName},'%')
    </select>
    <select id="queryFromCompany" resultType="com.hzw.sunflower.controller.response.OfflineCompanyVo">
        SELECT
            id AS companyId,
            company_name,
            organization_num,
            business_license,
            license_file_id,
            is_company_abroad
        FROM
            t_company
        WHERE
            is_delete = 0
            AND company_name LIKE CONCAT('%',#{companyName},'%')
    </select>
</mapper>
