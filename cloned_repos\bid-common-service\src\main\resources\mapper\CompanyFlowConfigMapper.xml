<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzw.sunflower.dao.CompanyFlowConfigMapper">

    <select id="getByDeptId" resultType="com.hzw.sunflower.entity.CompanyFlowConfig">
        select distinct
            *
        from
            t_company_flow_config t1
        left join t_department t2 on t1.company_id = t2.bind_company
        where t1.is_delete = 0
        and t2.is_delete = 0
        and t1.business_code = #{req.businessCode}
        and t2.id = #{req.deptId}
    </select>
</mapper>