package com.hzw.sunflower.constant;

/**
 * 0全流程线上 1线上+线下开评标 2线上+云开云评 3 全流程线下+线上归档
 */
public enum OperationFlowEnum {

    Flow0(0, "全流程线上"),
    Flow1(1, "线上+线下开评标"),
    Flow2(2, "线上+云开云评"),
    Flow3(3, "全流程线下+线上归档");

    private Integer value;
    private String name;

    OperationFlowEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    public Integer getValue() {
        return value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
