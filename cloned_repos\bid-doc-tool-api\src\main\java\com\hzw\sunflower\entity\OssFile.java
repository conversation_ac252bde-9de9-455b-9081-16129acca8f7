package com.hzw.sunflower.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hzw.sunflower.common.BaseBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <p>
 * OSS附件
 * </p>
 *
 * <AUTHOR> Gen
 * @since 2023-03-24
 */
@Getter
@Setter
@TableName("t_oss_file")
@ApiModel(value = "OssFile对象", description = "OSS附件 ")
public class OssFile extends BaseBean implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("OSS文件key")
    private String ossFileKey;

    @ApiModelProperty("OSS文件名称")
    private String ossFileName;


}
