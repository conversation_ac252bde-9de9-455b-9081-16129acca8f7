package com.hzw.ssm.expert.entity;

import java.util.Date;

import com.hzw.ssm.fw.base.BaseEntity;

/**
 * 专家审核记录表
 * <AUTHOR>
 * @date 2014-10-17
 */
public class ExpertAuditEntity extends BaseEntity {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	private String id;//主键
	
	private String expert_id;//专家id
	
	private String reason;//理由
	
	private Long status;//状态 0：通过 1：不通过
	
	private Date audit_time;//审核时间
	
	private String audit_user;//审核人
	
	private String audit_user_name;//审核人姓名

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getExpert_id() {
		return expert_id;
	}

	public void setExpert_id(String expertId) {
		expert_id = expertId;
	}

	public String getReason() {
		return reason;
	}

	public void setReason(String reason) {
		this.reason = reason;
	}

	public Long getStatus() {
		return status;
	}

	public void setStatus(Long status) {
		this.status = status;
	}

	public Date getAudit_time() {
		return audit_time;
	}

	public void setAudit_time(Date auditTime) {
		audit_time = auditTime;
	}

	public String getAudit_user() {
		return audit_user;
	}

	public void setAudit_user(String auditUser) {
		audit_user = auditUser;
	}

	public String getAudit_user_name() {
		return audit_user_name;
	}

	public void setAudit_user_name(String auditUserName) {
		audit_user_name = auditUserName;
	}

}
