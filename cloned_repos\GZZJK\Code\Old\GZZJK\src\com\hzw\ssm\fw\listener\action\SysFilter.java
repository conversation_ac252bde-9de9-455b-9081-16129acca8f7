/*
 * 公司名称：江苏华招网信息技术有限公司
 * 版权信息：江苏华招网信息技术有限公司版权所有
 * 文件名称：SysFilter.java
 * 修改时间：2020年9月7日
 * 修改人：陈山杉
 * 修改内容：
 * 跟踪单号：
 * 修改单号 ：
 */

package com.hzw.ssm.fw.listener.action;

import java.io.IOException;

import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;

/**
 * <一句话功能简述> cookie中增加httpOnly属性
 * <功能详细描述>
 * <AUTHOR>
 * @version V0.0.1-SNAPSHOT
 */
public class SysFilter implements Filter{

	@Override
	public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
			throws IOException, ServletException {
		
		Cookie[] cookies = ((HttpServletRequest)request).getCookies();
		
		if(cookies!=null){
			
			for (Cookie cookie : cookies) {
				//tomcat7 支持该属性，tomcat6不支持
				cookie.setHttpOnly(true);
			}
			
		}
		chain.doFilter(request, response);
	}
	
	@Override
	public void destroy() {

	}

	@Override
	public void init(FilterConfig arg0) throws ServletException {

	}
}
