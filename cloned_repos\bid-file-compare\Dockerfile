FROM registry.cn-hangzhou.aliyuncs.com/hzw-jtcc/openjdk:11.0.4
ENV TZ=Asia/Shanghai
ENV LANG C.UTF-8
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone
VOLUME /log
VOLUME /sunflower/template
VOLUME /sunflower/temporary
#ARG JAR_FILE
COPY target/bid-filecompare-1.0.0.jar fileCompare.jar
COPY target/classes/application-prod.yml /config/application-prod.yml
#COPY FONT
COPY target/classes/font/simsun.ttc /usr/share/fonts/ttf-dejavu/simsun.ttc
COPY target/classes/font/simsun.ttc /usr/share/fonts/chinese/simsun.ttc
COPY target/classes/font/STFangsong.ttf /usr/share/fonts/ttf-dejavu/STFangsong.ttf
COPY target/classes/font/STFangsong.ttf /usr/share/fonts/chinese/STFangsong.ttf
#COPY TEMPLATE FILE
EXPOSE 28925
#docker run -p 主机端口:容器端口 -it  --name 指定启动容器名称 -v /export:/export -d 镜像名称
ENTRYPOINT ["sh","-c", "java $JAVA_OPTS -Dspring.profiles.active=prod -jar /fileCompare.jar "]