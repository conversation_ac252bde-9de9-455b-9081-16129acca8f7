package com.hzw.sunflower.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2023/3/10 14:56
 * @Version 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SectionSpecialty {

    @ApiModelProperty("标段id")
    private Long sectionId;

    @ApiModelProperty("标段行业分类id(3级或者4级)")
    private Long expertSpecialtyId;

    @ApiModelProperty("标段委托金额(元)")
    private BigDecimal entrustMoney;

    @ApiModelProperty("标段行业二级分类编码")
    private String secondLevelSpecialtyCode;

    @ApiModelProperty("标段行业二级分类名称")
    private String secondLevelSpecialtyName;
}
