package com.hzw.sunflower.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hzw.sunflower.dto.SealApproveDTO;
import com.hzw.sunflower.entity.SealApprove;
import com.hzw.sunflower.entity.condition.SealApproveCondition;

import java.util.List;


/**
* SealApproveService接口
*
*/
public interface SealApproveService extends IService<SealApprove> {
    /**
    * 根据条件分页查询列表
    *
    * @param condition 查询条件
    * @return 分页信息
    */
    IPage<SealApprove> findInfoByCondition(SealApproveCondition condition);

    /**
    * 根据主键ID查询信息
    *
    * @param id 主键ID
    * @return SealApprove信息
    */
    SealApprove getInfoById(Long id);

    /**
    * 新增
    *
    * @param sealApprove
    * @return 是否成功
    */
    Boolean addInfo(SealApproveDTO sealApprove);

    /**
    * 修改单位公司 信息
    *
    * @param sealApprove
    * @return 是否成功
    */
    Boolean updateInfo(SealApproveDTO sealApprove);

    /**
    * 根据主键ID删除
    *
    * @param id 主键ID
    * @return 是否成功
    */
    Boolean deleteById(Long id);

    /**
    * 根据主键ID列表批量删除
    *
    * @param idList 主键ID列表
    * @return 是否成功
    */
    Boolean deleteByIds(List<Long> idList);

}