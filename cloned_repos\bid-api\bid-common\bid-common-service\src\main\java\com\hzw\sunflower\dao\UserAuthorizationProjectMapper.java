package com.hzw.sunflower.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hzw.sunflower.entity.UserAuthorizationProject;
import org.apache.ibatis.annotations.Param;

/**
 * 用户表 Mapper接口
 *
 * <AUTHOR>
 * @version 1.0.0 2021-04-07
 */
public interface UserAuthorizationProjectMapper extends BaseMapper<UserAuthorizationProject> {


    /**
     * 修改用户授权函
     * @param userId
     * @param identity
     * @param legalPersonAuthorization
     * @return
     */
    Boolean updateUserPersonAuthorization(@Param("userId") Long userId, @Param("identity") Integer identity, @Param("legalPersonAuthorization") Long legalPersonAuthorization);
}
