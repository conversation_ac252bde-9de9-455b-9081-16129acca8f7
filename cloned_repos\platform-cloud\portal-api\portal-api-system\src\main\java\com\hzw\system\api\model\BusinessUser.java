package com.hzw.system.api.model;

import com.baomidou.mybatisplus.annotation.TableId;
import com.hzw.common.basebean.web.domain.BaseEntity;
import com.hzw.common.core.annotation.Sensitive;
import com.hzw.common.core.enums.SensitiveStrategy;
import com.hzw.common.core.xss.Xss;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class BusinessUser extends BaseEntity {

    /**
     * 用户ID
     */
    @TableId(value = "user_id")
    private String userId;

    /**
     * 部门ID
     */
    private String deptId;

    /**
     * 用户账号
     */
    @Xss(message = "用户账号不能包含脚本字符")
    @NotBlank(message = "用户账号不能为空")
    @Size(min = 0, max = 30, message = "用户账号长度不能超过30个字符")
    private String userName;

    /**
     * 用户昵称
     */
    @Xss(message = "用户昵称不能包含脚本字符")
    @NotBlank(message = "用户昵称不能为空")
    @Size(min = 0, max = 30, message = "用户昵称长度不能超过30个字符")
    private String nickName;

    /**
     * 用户类型（sys_user系统用户）
     */
    private String userType;

    /**
     * 用户邮箱
     */
    @Sensitive(strategy = SensitiveStrategy.EMAIL)
    @Email(message = "邮箱格式不正确")
    @Size(min = 0, max = 50, message = "邮箱长度不能超过50个字符")
    private String email;

    /**
     * 手机号码
     */
    @Sensitive(strategy = SensitiveStrategy.PHONE)
    private String phonenumber;

    /**
     * 用户性别
     */
    private String sex;

    /**
     * 用户头像
     */
    private String avatar;

    /**
     * 密码
     */
    private String password;

    /**
     * 帐号状态（0正常 1停用）
     */
    private Integer status;

    /**
     * 最后登录IP
     */
    private String loginIp;

    /**
     * 最后登录时间
     */
    private Date loginDate;
    /**
     * 身份证
     */
    private String idNum;

    private String otherUserId;
    /**
     * 推荐码
     */
    private String qrCode;
    /**
     * 推荐图片
     */
    private String qrCodeBase;
    /**
     * 注册推荐码
     */
    private String referralCode;
    /**
     * 小程序
     */
    private String openId;
    /**
     * 身份
     */
    private Integer identity;
}
