package com.hzw.sunflower.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.hzw.sunflower.common.BaseBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date ：Created in 2022/9/5 10:42
 * @description：
 * @modified By：`
 * @version: 1.0
 */
@ApiModel(description = "数据修改记录表")
@TableName("t_data_update_record")
@Data
public class DataUpdateRecord extends BaseBean {

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "修改类型 1.委托项目名称 2.采购项目名称 3.采购文件发售截止时间 4.响应文件递交截止时间 5.修改企业天眼查信息")
    private Integer type;

    @ApiModelProperty(value = "修改内容")
    private String content;

    @ApiModelProperty(value = "数据修改单文件id，多个以逗号隔开，最多5个")
    private String fileIds;

    @ApiModelProperty(value = "请求参数")
    private String requestJson;

    @ApiModelProperty(value = "审核状态：1:待确认 2:已撤回 3:已退回 4：已完成")
    private Integer status;

    @ApiModelProperty(value = "审核流程code")
    private String processCode;

    @ApiModelProperty(value = "修改前的内容")
    private String beforeUpdateContent;

    @ApiModelProperty(value = "申请人id")
    private Long applyUserId;

    @ApiModelProperty(value = "申请人部门id")
    private Long applyDepartId;
}

