package com.hzw.ssm.webService.input.impl;


/**
 * 专家考核评价输入参数
 * 
 * <AUTHOR>
 * 
 */
public class AppraiseExpertInput {

	// 抽取序号
	private String ordinalNumber;
	// 项目名称 S
	private String projectName;
	// 项目编号 S
	private String projectNum;
	// 是否迟到
	private Integer isLate;
	// 是否不能熟练操作计算机
	private Integer isOperateCpu;
	// 是否未即使补充完善个人信息
	private Integer isPersonInfo;
	// 是否认真阅读文件
	private Integer isRead;
	// 是否做与评审无关的事
	private Integer isDoOther;
	// 是否催促其它评委
	private Integer isUrge;
	// 是否一年内两次打分与其他专家平均分正负偏离超过20%
	private Integer isDeviate;
	// 是否一年内12次拒绝评标
	private Integer isRefuse;
	// 是否确认参加而未参加评标的
	private Integer isComeIn;
	// 是否擅自进入或者离开评标区域
	private Integer isLeave;
	// 是否擅自带出评标材料
	private Integer isTakeOut;
	// 是否不完整提交评标报告，或者决绝签字签章
	private Integer isSign;
	// 是否不满评标费
	private Integer  isDissatisfaction;
	// 是否在评标过程中有重大失误
	private Integer isError;
	// 是否无故拒绝参加复议
	private Integer isJoin;
	// 是否不服从现场管理
	private Integer isObey;
	// 是否应当回避而未回避
	private Integer isAvoid;
	// 是否按照文件规定的标准、方法评审的
	private Integer  isReview;
	// 是否私下接触投标人
	private Integer isTouch;
	// 是否具有倾向性
	private Integer isInclination;
	// 是否委托他人评标、签字
	private Integer isEntrust;
	// 是否诱导投标人
	private Integer isInclude;
	// 是否对依法应当否决的投标不提出否决意见；
	private Integer isPropose;
	// 是否有其它不客观、不公正履行职务的行为。
	private Integer isFair;
	// 是否积极配合调查
	private Integer isInvestigation;
	// 是否向监管部门提交合理化建议
	private Integer isProposal;
	// 是否主动向监管部门举报违法违规行为
	private Integer isReport;
	public String getOrdinalNumber() {
		return ordinalNumber;
	}
	public void setOrdinalNumber(String ordinalNumber) {
		this.ordinalNumber = ordinalNumber;
	}
	public String getProjectName() {
		return projectName;
	}
	public void setProjectName(String projectName) {
		this.projectName = projectName;
	}
	public String getProjectNum() {
		return projectNum;
	}
	public void setProjectNum(String projectNum) {
		this.projectNum = projectNum;
	}
	public Integer getIsLate() {
		return isLate;
	}
	public void setIsLate(Integer isLate) {
		this.isLate = isLate;
	}
	public Integer getIsOperateCpu() {
		return isOperateCpu;
	}
	public void setIsOperateCpu(Integer isOperateCpu) {
		this.isOperateCpu = isOperateCpu;
	}
	public Integer getIsPersonInfo() {
		return isPersonInfo;
	}
	public void setIsPersonInfo(Integer isPersonInfo) {
		this.isPersonInfo = isPersonInfo;
	}
	public Integer getIsRead() {
		return isRead;
	}
	public void setIsRead(Integer isRead) {
		this.isRead = isRead;
	}
	public Integer getIsDoOther() {
		return isDoOther;
	}
	public void setIsDoOther(Integer isDoOther) {
		this.isDoOther = isDoOther;
	}
	public Integer getIsUrge() {
		return isUrge;
	}
	public void setIsUrge(Integer isUrge) {
		this.isUrge = isUrge;
	}
	public Integer getIsDeviate() {
		return isDeviate;
	}
	public void setIsDeviate(Integer isDeviate) {
		this.isDeviate = isDeviate;
	}
	public Integer getIsRefuse() {
		return isRefuse;
	}
	public void setIsRefuse(Integer isRefuse) {
		this.isRefuse = isRefuse;
	}
	public Integer getIsComeIn() {
		return isComeIn;
	}
	public void setIsComeIn(Integer isComeIn) {
		this.isComeIn = isComeIn;
	}
	public Integer getIsLeave() {
		return isLeave;
	}
	public void setIsLeave(Integer isLeave) {
		this.isLeave = isLeave;
	}
	public Integer getIsTakeOut() {
		return isTakeOut;
	}
	public void setIsTakeOut(Integer isTakeOut) {
		this.isTakeOut = isTakeOut;
	}
	public Integer getIsSign() {
		return isSign;
	}
	public void setIsSign(Integer isSign) {
		this.isSign = isSign;
	}
	public Integer getIsDissatisfaction() {
		return isDissatisfaction;
	}
	public void setIsDissatisfaction(Integer isDissatisfaction) {
		this.isDissatisfaction = isDissatisfaction;
	}
	public Integer getIsError() {
		return isError;
	}
	public void setIsError(Integer isError) {
		this.isError = isError;
	}
	public Integer getIsJoin() {
		return isJoin;
	}
	public void setIsJoin(Integer isJoin) {
		this.isJoin = isJoin;
	}
	public Integer getIsObey() {
		return isObey;
	}
	public void setIsObey(Integer isObey) {
		this.isObey = isObey;
	}
	public Integer getIsAvoid() {
		return isAvoid;
	}
	public void setIsAvoid(Integer isAvoid) {
		this.isAvoid = isAvoid;
	}
	public Integer getIsReview() {
		return isReview;
	}
	public void setIsReview(Integer isReview) {
		this.isReview = isReview;
	}
	public Integer getIsTouch() {
		return isTouch;
	}
	public void setIsTouch(Integer isTouch) {
		this.isTouch = isTouch;
	}
	public Integer getIsInclination() {
		return isInclination;
	}
	public void setIsInclination(Integer isInclination) {
		this.isInclination = isInclination;
	}
	public Integer getIsEntrust() {
		return isEntrust;
	}
	public void setIsEntrust(Integer isEntrust) {
		this.isEntrust = isEntrust;
	}
	public Integer getIsInclude() {
		return isInclude;
	}
	public void setIsInclude(Integer isInclude) {
		this.isInclude = isInclude;
	}
	public Integer getIsPropose() {
		return isPropose;
	}
	public void setIsPropose(Integer isPropose) {
		this.isPropose = isPropose;
	}
	public Integer getIsFair() {
		return isFair;
	}
	public void setIsFair(Integer isFair) {
		this.isFair = isFair;
	}
	public Integer getIsInvestigation() {
		return isInvestigation;
	}
	public void setIsInvestigation(Integer isInvestigation) {
		this.isInvestigation = isInvestigation;
	}
	public Integer getIsProposal() {
		return isProposal;
	}
	public void setIsProposal(Integer isProposal) {
		this.isProposal = isProposal;
	}
	public Integer getIsReport() {
		return isReport;
	}
	public void setIsReport(Integer isReport) {
		this.isReport = isReport;
	}
	
	
	

}
