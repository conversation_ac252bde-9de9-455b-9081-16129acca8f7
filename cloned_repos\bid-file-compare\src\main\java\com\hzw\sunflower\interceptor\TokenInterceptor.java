package com.hzw.sunflower.interceptor;

import cn.hutool.json.JSONUtil;
import com.hzw.sunflower.common.redis.RedisCache;
import com.hzw.sunflower.constant.GeneralConstants;
import com.hzw.sunflower.constant.RedisCacheConstants;
import com.hzw.sunflower.entity.JwtUser;
import com.hzw.sunflower.exception.ExceptionEnum;
import com.hzw.sunflower.exception.SunFlowerException;
import com.hzw.sunflower.util.JwtTokenUtils;
import com.hzw.sunflower.util.spring.SpringUtils;
import io.jsonwebtoken.ExpiredJwtException;
import io.jsonwebtoken.MalformedJwtException;
import io.jsonwebtoken.SignatureException;
import io.jsonwebtoken.UnsupportedJwtException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class TokenInterceptor implements HandlerInterceptor {


    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        long start = System.currentTimeMillis();
        String token = request.getHeader(GeneralConstants.TOKEN);
        if (token == null || token.isEmpty()) {
            throw new SunFlowerException(ExceptionEnum.TOKEN_CANNOT_EMPTY, ExceptionEnum.TOKEN_CANNOT_EMPTY.getMessage());
        }

        try {
            // 校验token是否过期
            String jwtToken = token.replace(GeneralConstants.TOKEN_PREFIX, "");

            JwtUser jwtUser = new JwtUser(jwtToken);

            RedisCache redisCache = SpringUtils.getBean(RedisCache.class);
            // 检测redis中是否存在
            String redisUserKey = RedisCacheConstants.USER_INFO + jwtUser.getUserId();
            Object userJson = redisCache.getCacheObject(redisUserKey);
            if (Objects.isNull(userJson)) {
                log.error("Token已过期:  " + token);
                throw new SunFlowerException(ExceptionEnum.TOKEN_HAS_EXPIRED, ExceptionEnum.TOKEN_HAS_EXPIRED.getMessage());
            }

            String roleStr = JwtTokenUtils.getUserRole(jwtToken);
            List<String> roles = JSONUtil.toList(roleStr, String.class);
            List<SimpleGrantedAuthority> authorities = new ArrayList<>();
            for (String role : roles) {
                authorities.add(new SimpleGrantedAuthority(role));
            }

//            StpUtil.login(jwtUser.getUserId());
            long end = System.currentTimeMillis();
            long result = end - start;

            log.info("执行时间: " + result + "毫秒");
            return true;
        } catch (ExpiredJwtException e) {
            log.error("Token已过期: {} " + e);
            throw new SunFlowerException(ExceptionEnum.TOKEN_HAS_EXPIRED, e.getMessage());
        } catch (UnsupportedJwtException e) {
            log.error("Token格式错误: {} " + e);
            throw new SunFlowerException(ExceptionEnum.TOKEN_FORMAT_ERROR, e.getMessage());
        } catch (MalformedJwtException e) {
            log.error("Token没有被正确构造: {} " + e);
            throw new SunFlowerException(ExceptionEnum.TOKEN_IS_NOT_CONSTRUCTED_CORRECTLY, e.getMessage());
        } catch (SignatureException e) {
            log.error("签名失败: {} " + e);
            throw new SunFlowerException(ExceptionEnum.SIGNATURE_FAILED, e.getMessage());
        } catch (IllegalArgumentException e) {
            log.error("非法参数异常: {} " + e);
            throw new SunFlowerException(ExceptionEnum.ILLEGAL_PARAMETER_EXCEPTION, e.getMessage());
        }
    }
}
