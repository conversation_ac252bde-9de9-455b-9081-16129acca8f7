///***
/// * 搜索页面 - 头部
/// */

import 'package:flutter/material.dart';

import 'package:flutter_main_app/src/constants/theme.dart';
import 'package:flutter_main_app/src/utils/ScreenUtils.dart';

typedef OnSubmittedFunction = void Function(String);

class SearchHeader extends StatelessWidget {

  SearchHeader({
    Key key,
    @required this.keyword,
    @required this.onSubmittedCallback,
    this.focusNode,
  }) : assert(keyword != null),
    _textEditingController = TextEditingController(text: keyword),
    super(key: key);

  final String keyword;

  final OnSubmittedFunction onSubmittedCallback;

  final FocusNode focusNode;

  final TextEditingController _textEditingController;

  static final OutlineInputBorder _border = OutlineInputBorder(
    borderSide: BorderSide(
      color: themeContentBackgroundColor,
    ),
    borderRadius: BorderRadius.circular(6.0),
    gapPadding: 0.0,
  );

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.only(top: 44.0),
      width: ScreenUtils.screenWidth,
      height: 100.0,
      color: themeActiveColor,
      child: Row(
        mainAxisSize: MainAxisSize.max,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: <Widget>[
          /// * 返回
          IconButton(
            icon: Icon(
              Icons.arrow_back_ios,
              color: Colors.white,
            ),
            iconSize: 24.0,
            onPressed: () {
              _navigatorPop(context);
            },
          ),
          /// * 输入框
          Container(
            /// * 40.0(返回按钮) | 52.0(取消按钮) | 8.0(模拟器报错超出8.0) | 20.0(容错及间隔)
            width: ScreenUtils.screenWidth - 40.0 - 52.0 - 8.0 - 20.0,
            height: 40.0,
            child: TextField(
              controller: _textEditingController,
              focusNode: this.focusNode,
              decoration: InputDecoration(
                hintText: '请输入你想要查询的招标信息',
                hintStyle: TextStyle(
                  color: themeHintTextColor,
                  fontSize: 12.0,
                ),
                prefixIcon: Container(
                  transform: Matrix4.translationValues(0.0, 0.0, 0.0),
                  child: Icon(
                    Icons.search,
                    color: themeHintTextColor,
                    size: 24.0,
                  ),
                ),
                filled: true,
                fillColor: themeContentBackgroundColor,
                enabledBorder: _border,
                focusedBorder: _border,
              ),
              keyboardType: TextInputType.text,
              textInputAction: TextInputAction.search,
              style: themeTextStyle,
              strutStyle: StrutStyle(
                height: 1.65,
              ),
              textAlignVertical: TextAlignVertical.center,
              autofocus: true,
              onSubmitted: (String keyword) {
                if (this.onSubmittedCallback != null) {
                  this.onSubmittedCallback(keyword);
                }
              },
            ),
          ),
          /// * 取消
          InkWell(
            onTap: () {
              _navigatorPop(context);
            },
            child: Padding(
              padding: const EdgeInsets.only(left: 11.0, right: 15.0),
              child: Container(
                child: Text(
                  '取消',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: themeFontSize,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  ///***
  /// * 返回上一页
  /// *
  /// * @param {BuildContext} context
  /// */
  void _navigatorPop(BuildContext context) {
    Navigator.pop(context);
  }

}
