package com.hzw.sunflower.controller.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;


/**
 * 公告关联媒体请求体
 *
 * <AUTHOR> 2021/07/01
 * @version 1.0.0
 */
@ApiModel(description = "公告关联媒体请求体")
@Data
@Accessors(chain = true)
public class NoticeMediaREQ {

    @ApiModelProperty(value = "媒体ID", position = 1)
    private Long id;
    /**
     * 是否选中默认0否1是
     */
    @ApiModelProperty(value = "是否选中默认0否1是")
    private Integer isSelect;

    /**
     * 是否选中默认0否1是
     */
    @ApiModelProperty(value = "是否新增默认0否1是")
    private Integer newAdd;

    @ApiModelProperty(value = "媒体名称", position = 2)
    private String mediaName;

    @ApiModelProperty(value = "媒体链接", position = 3)
    private String mediaUrl;

    @ApiModelProperty(value = "0线上1线下", position = 4)
    private Integer isOnline;

//    @ApiModelProperty(value = "发布截图附件ID集合", position = 4)
//    private List<Long> pics;
//
//    @ApiModelProperty(value = "使用次数", position = 5)
//    private Long useTimes;

}
