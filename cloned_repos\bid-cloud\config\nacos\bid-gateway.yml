# 安全配置
security:
  # 验证码
  captcha:
    # 是否开启验证码
    enabled: false
    # 验证码类型 math 数组计算 char 字符验证
    type: MATH
    # line 线段干扰 circle 圆圈干扰 shear 扭曲干扰
    category: CIRCLE
    # 数字验证码位数
    numberLength: 1
    # 字符验证码长度
    charLength: 4
  # 防止XSS攻击
  xss:
    enabled: true
    excludeUrls:
      - /system/notice
  # 不校验白名单
  ignore:
    whites:
      - /code
      - /auth/logout
      - /auth/login
      - /auth/smsLogin
      - /auth/xcxLogin
      - /auth/register
      - /auth/registerByPhoneNumber
      - /auth/registerWechat
      - /auth/wxSmallLogin
      - /resource/sms/code
      - /resource/sms/v2/std/sendSmsCode
      - /*/v3/api-docs
      - /csrf
      - /expert/policies/allList
      - /system/setting/querySystemSetting
      - /resource/oss/upload
      - /resource/oss/uploadWx
      - /resource/oss/getPrivateUrl/{id}
      - /externalApi/ocr/uploadCardImgUrl
      - /externalApi/voice/callback
      - /expert/expertInfo/pic/{num}


spring:
  cloud:
    # 网关配置
    gateway:
      # 打印请求日志(自定义)
      requestLog: true
      discovery:
        locator:
          lowerCaseServiceId: true
          enabled: true
      routes:
        # 认证中心
        - id: bid-auth
          uri: lb://bid-auth
          predicates:
            - Path=/auth/**
          filters:
            # 验证码处理
            - ValidateCodeFilter
            - StripPrefix=1
        # 代码生成
        - id: bid-gen
          uri: lb://bid-gen
          predicates:
            - Path=/code/**
          filters:
            - StripPrefix=1
        # 系统模块
        - id: bid-system
          uri: lb://bid-system
          predicates:
            - Path=/system/**
          filters:
            - StripPrefix=1
        # 资源服务
        - id: bid-resource
          uri: lb://bid-resource
          predicates:
            - Path=/resource/**
          filters:
            - StripPrefix=1
        # 演示服务
        - id: bid-demo
          uri: lb://bid-demo
          predicates:
            - Path=/demo/**
          filters:
            - StripPrefix=1
        # MQ演示服务
        - id: bid-stream-mq
          uri: lb://bid-stream-mq
          predicates:
            - Path=/stream-mq/**
          filters:
            - StripPrefix=1
        # 专家库
        - id: bid-expert
          uri: lb://bid-expert
          predicates:
            - Path=/expert/**
          filters:
            - StripPrefix=1
        # 第三方对接
        - id: bid-external
          uri: lb://bid-external
          predicates:
            - Path=/externalApi/**
          filters:
            - StripPrefix=1
        # 微信小程序
        - id: bid-expertWechat
          uri: lb://bid-expert
          predicates:
            - Path=/expertWechat/**
          filters:
            - StripPrefix=1

    # sentinel 配置
    sentinel:
      filter:
        enabled: true
      # nacos配置持久化
      datasource:
        ds1:
          nacos:
            server-addr: ${spring.cloud.nacos.server-addr}
            dataId: sentinel-${spring.application.name}.json
            groupId: ${spring.cloud.nacos.config.group}
            namespace: ${spring.profiles.active}
            data-type: json
            rule-type: gw-flow
