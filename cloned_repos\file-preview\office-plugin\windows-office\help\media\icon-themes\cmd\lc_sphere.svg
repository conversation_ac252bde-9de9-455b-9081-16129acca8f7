<?xml-stylesheet type="text/css" href="icons.css" ?>
<svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
  <linearGradient
     id="linearGradient"
     x1="21"
     y1="3"
     x2="3"
     y2="21"
     gradientUnits="userSpaceOnUse">
    <stop
       style="stop-color:#ffffff;stop-opacity:0.6"
       />
    <stop
       style="stop-color:#ffffff;stop-opacity:0"
       offset="0.5"
       />
  </linearGradient>
  <path
     class="icn icn--highlight-color"
     d="M 21.5,12 A 9.5,9.5 0 0 1 12,21.5 9.5,9.5 0 0 1 2.5,12 9.5,9.5 0 0 1 12,2.5 9.5,9.5 0 0 1 21.5,12 Z"
     fill="#117acc"
     stroke="#0063b1"
     stroke-linecap="round"
     stroke-linejoin="round"
     />
  <path
     style="fill:url(#linearGradient)"
     d="M 21,12 A 9,9 0 0 1 12,21 9,9 0 0 1 3,12 a 9,9 0 0 1 9,-9 9,9 0 0 1 9,9 z"
     />
</svg>
