package com.hzw.ssm.date.entity;

import java.io.Serializable;
import java.util.Date;

import com.hzw.ssm.fw.base.BaseEntity;

public class CalendarEntity extends BaseEntity implements Serializable {
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	private Integer calendar_id;//表示主键ID
	
	private String date_time;//表示日期
	
	private Integer date_year;//表示日期所在年份
	
	private Integer date_month;//表示日期所在月份
	
	private Integer date_status;//该日期是否休假日(包括假日,法定假日,法定调休日)(1表示否,2表示是)
	
	private String remark;//日期说明(工作日/周末/XX节/法定调休/法定调班)
	
	private Integer create_user;//创建者
	
	private Date create_date;//创建日期
	
	private Integer modify_user;//修改者
	
	private Date modify_date;//修改日期
	
	private Integer delete_flag;//删除标记

	public Integer getCalendar_id() {
		return calendar_id;
	}

	public void setCalendar_id(Integer calendar_id) {
		this.calendar_id = calendar_id;
	}

	

	public String getDate_time() {
		return date_time;
	}

	public void setDate_time(String date_time) {
		this.date_time = date_time;
	}

	public Integer getDate_year() {
		return date_year;
	}

	public void setDate_year(Integer date_year) {
		this.date_year = date_year;
	}

	public Integer getDate_status() {
		return date_status;
	}

	public void setDate_status(Integer date_status) {
		this.date_status = date_status;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public Integer getCreate_user() {
		return create_user;
	}

	public void setCreate_user(Integer create_user) {
		this.create_user = create_user;
	}

	public Date getCreater_date() {
		return create_date;
	}

	public void setCreater_date(Date creater_date) {
		this.create_date = creater_date;
	}

	public Integer getModify_user() {
		return modify_user;
	}

	public void setModify_user(Integer modify_user) {
		this.modify_user = modify_user;
	}

	public Date getModify_date() {
		return modify_date;
	}

	public void setModify_date(Date modify_date) {
		this.modify_date = modify_date;
	}
	
	

	@Override
	public Integer getDelete_flag() {
		return delete_flag;
	}

	@Override
	public void setDelete_flag(Integer delete_flag) {
		this.delete_flag = delete_flag;
	}
	
	

	public Integer getDate_month() {
		return date_month;
	}

	public void setDate_month(Integer date_month) {
		this.date_month = date_month;
	}

	@Override
	public String toString() {
		return "CalendarEntity [calendar_id=" + calendar_id + ", date_time=" + date_time + ", date_year=" + date_year
				+ ", date_month=" + date_month + ", date_status=" + date_status + ", remark=" + remark
				+ ", create_user=" + create_user + ", create_date=" + create_date + ", modify_user=" + modify_user
				+ ", modify_date=" + modify_date + ", delete_flag=" + delete_flag + "]";
	}

}
