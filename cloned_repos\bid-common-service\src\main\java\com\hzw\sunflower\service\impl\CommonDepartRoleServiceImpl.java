package com.hzw.sunflower.service.impl;

import com.hzw.sunflower.controller.response.CommonUserRoleRelationVO;
import com.hzw.sunflower.dao.CommonDepartRoleMapper;
import com.hzw.sunflower.dto.CommonDeptRolesDTO;
import com.hzw.sunflower.service.CommonDepartRoleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;
import com.hzw.sunflower.entity.Role;

/**
 * @ClassName:CommonDepartRoleServiceImpl
 * @Auther: lijinxin
 * @Description:
 * @Date: 2024/7/23 15:22
 * @Version: v1.0
 */
@Service
public class CommonDepartRoleServiceImpl implements CommonDepartRoleService {

    @Autowired
    private CommonDepartRoleMapper commonDepartRoleMapper;


    /**
     * 查询用户部门角色关系
     */
    @Override
    public List<CommonDeptRolesDTO> getDepartRoles(Long userId) {
        //根据部门id查询角色信息
        List<CommonDeptRolesDTO> deptRoles = commonDepartRoleMapper.getDepartRoleInfoById(userId,null);
        for(CommonDeptRolesDTO drole:deptRoles) {
            if (drole != null && drole.getRoleStrIds() != null) {
                String[] strIds = drole.getRoleStrIds().split(",");
                Long[] roldIds = new Long[strIds.length];
                for (int i = 0; i < strIds.length; i++) {
                    roldIds[i] = Long.parseLong(strIds[i]);
                }
                drole.setRoleIds(roldIds);
            }

            if (drole != null && drole.getRoleStrCodes() != null) {
                String[] codeIds = drole.getRoleStrCodes().split(",");
                drole.setRoleCodes(codeIds);
            }

            if (drole != null && drole.getRoleStrNames() != null) {
                String[] names = drole.getRoleStrNames().split(",");
                drole.setRoleNames(names);
            }
        }
        return deptRoles;
    }

    @Override
    public List<CommonUserRoleRelationVO> getUserRoleByIdentity(Long userId, Integer userIdentity) {
        return commonDepartRoleMapper.getUserRoleByIdentity(userId,userIdentity);
    }

    @Override
    public List<Role> getRoleList(List<Long> roleids) {
        return commonDepartRoleMapper.getRoleList(roleids);
    }

}
