package com.hzw.sunflower.entity.condition;

import com.hzw.sunflower.common.BaseCondition;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2022/7/27 11:18
 * @description：
 * @modified By：`
 * @version: 1.0
 */
@ApiModel(description = "标段流水关联详情查询条件")
@Data
public class BondRefundCondition extends BaseCondition {

    @ApiModelProperty(value = "关键字：项目编号/项目名称/供应商名称")
    private String keywords;

    @ApiModelProperty(value = "申请时间")
    private Date applyTime;

    @ApiModelProperty(value = "开始申请时间")
    private String startApplyTime;

    @ApiModelProperty(value = "申请时间")
    private String endApplyTime;

    @ApiModelProperty(value = "收款银行")
    private String bankName;

    @ApiModelProperty(value = "0：全部 1：待处理 2：已处理")
    private Integer type;

    @ApiModelProperty(value = "状态：1：待处理  2：已退回    3：退还成功 4：退还失败")
    private Integer status;

    @ApiModelProperty(value = "1:异常付款失败  2:财务审批  3:保证金流水退还列表")
    private Integer businessType;

    @ApiModelProperty(value = "0.全部 1.已中标 2.未中标")
    private Integer bidStatus;

    @ApiModelProperty(value = "退还id")
    private Long refundId;

    @ApiModelProperty(value = "用户id")
    private Long userId;

    @ApiModelProperty(value = "角色id")
    private Long[] roleId;

    @ApiModelProperty(value = "用户Name")
    private String userName;

    @ApiModelProperty(value = "退还时间")
    private Date refundTime;

    @ApiModelProperty(value = "标段id")
    private Long sectionId;

    @ApiModelProperty(value = "是否有回单 1:是  2：否")
    private Integer hasReceipt;

    @ApiModelProperty(value = "是否为最后一级")
    private Boolean isLastleader = false;

    @ApiModelProperty(value = "流程code集合")
    private List<String> taskCodes;

    @ApiModelProperty(value = "项目编号")
    private String purchaseNumber;

    @ApiModelProperty(value = "供应商名称")
    private String companyName;

    @ApiModelProperty(value = "退还开始时间")
    private String refundStartTime;

    @ApiModelProperty(value = "退还结束时间")
    private String refundEndTime;

}
