package com.hzw.sunflower.service;

import com.hzw.sunflower.controller.request.UserFieldShowREQ;
import com.hzw.sunflower.dto.UserFieldShowDTO;
import com.hzw.sunflower.entity.JwtUser;
import com.hzw.sunflower.entity.UserFieldShow;

/**
 * 用户字段展示表 Service接口
 *
 * <AUTHOR>
 * @version 1.0.0 2021-04-07
 */
public interface UserFieldShowService {


    /**
     * 新增用户字段展示表 信息
     *
     * @param userFieldShow 用户字段展示表 信息
     * @return 是否成功
     */
    Boolean addUserFieldShow(UserFieldShow userFieldShow, JwtUser user);

    /**
     * 修改用户字段展示表 信息
     *
     * @param userFieldShow 用户字段展示表 信息
     * @return 是否成功
     */
    Boolean updateUserFieldShow(UserFieldShow userFieldShow, JwtUser user);


    /**
     * 根据用户字段展示表查询单条数据
     *
     * @param condition
     * @return
     */
    UserFieldShowDTO getOne(UserFieldShowREQ condition);
}