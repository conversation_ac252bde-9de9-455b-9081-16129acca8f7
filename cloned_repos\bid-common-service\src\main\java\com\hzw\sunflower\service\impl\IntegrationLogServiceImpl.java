package com.hzw.sunflower.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzw.sunflower.dao.IntegrationLogMapper;
import com.hzw.sunflower.dao.IpLibraryMapper;
import com.hzw.sunflower.entity.IntegrationLog;
import com.hzw.sunflower.entity.IpLibrary;
import com.hzw.sunflower.service.IntegrationLogService;
import com.hzw.sunflower.service.IpLibraryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

/**
 * @ClassName:IntegrationLogServiceImpl
 * @Auther: lijinxin
 * @Description: 系统集成日志
 * @Date: 2024/9/3 11:16
 * @Version: v1.0
 */
@Service
@Slf4j
public class IntegrationLogServiceImpl extends ServiceImpl<IntegrationLogMapper, IntegrationLog> implements IntegrationLogService {


    /**
     * 新增日志
     * @param log
     */
    @Async
    @Override
     public void addIntegrationLog(IntegrationLog log){
      this.baseMapper.insert(log);
     }
}
