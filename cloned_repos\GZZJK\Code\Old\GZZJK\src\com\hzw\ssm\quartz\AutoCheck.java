package com.hzw.ssm.quartz;

import java.util.Calendar;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;

import com.hzw.ssm.date.entity.AutoCheckEntity;
import com.hzw.ssm.date.service.ZJKCalendarService;
import com.hzw.ssm.sys.project.entity.ProjectEntity;

/**
 * 自动审核
 */
public class AutoCheck {
	
	
	@Autowired
	private ZJKCalendarService zjkCalendarService;

	/**
	 * 查询自动审核设置表中的自动审核时间字段值;
	 * 查询项目表中;状态=90项目待主任审核的数据,且当前时间和项目最后修改时间差值>自动审核时间字段的数据,更新为主任已审核通过状态
	 * 
	 */
	public void work() {
		try{
			//获取服务器当前时间
	        Calendar cd = Calendar.getInstance();
			
	        AutoCheckEntity auto = zjkCalendarService.quertAutoCheck();
	        List<ProjectEntity> autoList = null;
	        if(auto != null && auto.getCheck_time() != null){
	        	autoList = zjkCalendarService.queryAutoProjectList(auto);
	        }
	        if(autoList !=null && autoList.size() >0){
	        	for(ProjectEntity pro : autoList){
	        		pro.setStatus(91L);
	        		pro.setCheck_type(2L);
	        		zjkCalendarService.updateAutoCheck(pro);
	        	}
	        }
		}catch(Exception e){
			e.getMessage();
		}		
	}

}
