package com.hzw.sunflower.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hzw.sunflower.common.Paging;
import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.controller.request.BidDocTemplateListREQ;
import com.hzw.sunflower.controller.responese.BidDocTemplateVO;
import com.hzw.sunflower.dto.BidDocTemplateDTO;
import com.hzw.sunflower.service.BidDocTemplateService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @ClassName:BidDocTemplateController
 * @Auther: lijinxin
 * @Description: 招标文件编制模版表
 * @Date: 2024/6/27 14:10
 * @Version: v1.0
 */
@Api(tags = "招标文件编制模版")
@RestController
@RequestMapping("/docTemplate")
public class BidDocTemplateController {

    @Autowired
    private BidDocTemplateService docTemplateService;


    @ApiOperation(value = "招标文件编制模版列表")
    @ApiImplicitParam(name = "req", value = "招标文件编制模版列表", required = true, dataType = "BidDocTemplateListREQ", paramType = "body")
    @PostMapping("/selectTemplateList")
    public Result<Paging<BidDocTemplateVO>> selectTemplateList(@RequestBody BidDocTemplateListREQ req) {
        IPage<BidDocTemplateVO> docTemplateVOIPage = docTemplateService.selectTemplateList(req);
        return Result.ok(Paging.buildPaging(docTemplateVOIPage));
    }

    @ApiOperation(value = "启用/停用")
    @ApiImplicitParam(name = "req", value = "招标文件编制模版", required = true, dataType = "BidDocTemplateDTO", paramType = "body")
    @PostMapping("/onOrOff")
    public Result<Object> onOrOff(@RequestBody BidDocTemplateDTO dto) {
        return docTemplateService.onOrOff(dto);
    }



}
