package com.hzw.sunflower.controller.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * Created by zgq on 2021/07/05
 *
 * 通用请求实体
 */

@ApiModel(description = "通用请求实体 ")
@Data
@Accessors(chain = true)
public class CommonNoticeREQ {

    @ApiModelProperty(value = "项目ID", position = 1)
    private Long projectId;

    @ApiModelProperty(value = "公告ID", position = 2)
    private Long noticeId;

    @ApiModelProperty(value = "标段ID", position = 3)
    private Long sectionId;
    @ApiModelProperty(value = "轮次", position = 4)
    private Integer bidRound;
}
