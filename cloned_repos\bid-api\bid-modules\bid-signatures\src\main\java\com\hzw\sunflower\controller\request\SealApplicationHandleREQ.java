package com.hzw.sunflower.controller.request;

import com.hzw.sunflower.common.BaseCondition;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName:SealApplicationHandleREQ
 * @Auther: lijinxin
 * @Description: 用印列表检索REQ
 * @Date: 2023/11/6 16:23
 * @Version: v1.0
 */
@Data
public class SealApplicationHandleREQ  extends BaseCondition implements Serializable {

    @ApiModelProperty("关键字")
    private String keyWords;

    @ApiModelProperty("签章类型")
    private Integer type;

    @ApiModelProperty("开始时间")
    private String startTime;

    @ApiModelProperty("结束时间")
    private String endTime;

    @ApiModelProperty("文号")
    private Integer symbol;

    @ApiModelProperty("处理状态 1 未处理 2已处理")
    private Integer processingProgress;

    @ApiModelProperty("用户名")
    private String userName;

    @ApiModelProperty("部门id")
    private Long deptId;

    @ApiModelProperty(" 导出类型 1为处理 2 已处理")
    private Integer exportType;



}
