package com.hzw.bid.util.oss;


import com.hzw.bid.project.constant.CommonConstants;
import lombok.extern.slf4j.Slf4j;


/**
 * <AUTHOR>
 */
@Slf4j
public class OssUtil {

    /**
     * 获取服务
     * @param ossType
     * @return
     */
    public static OssFileStorage getOssFileStorage (String ossType) {
        if (ossType.equals(CommonConstants.ACTIVE_CTYUN)) {
            return new CtyunFileStorage();
        } else {
            return new AliyunFileStorage();
        }
    }

}
