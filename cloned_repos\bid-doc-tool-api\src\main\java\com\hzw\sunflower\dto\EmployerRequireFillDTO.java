package com.hzw.sunflower.dto;

import lombok.Data;

/**
 * <AUTHOR>
 * @datetime 2024/06/26 16:42
 * @description: 发包人要求填充DTO
 * @version: 1.0
 */
@Data
public class EmployerRequireFillDTO {

    /**
     * 设计要求
     */
    private String designRequirements;

    /**
     * 适用规范标准
     */
    private String applicableStandard;

    /**
     * 成果文件要求
     */
    private String achievementFileRequirements;

    /**
     * 发包人提供的设备、设施
     */
    private String equipmentFacilities;

    /**
     * 发包人提供的资料
     */
    private String employerMaterial;

    /**
     * 发包人财产使用要求及退还要求
     */
    private String useAndReturnRequirements;

    /**
     * 发包人提供的便利条件
     */
    private String convenienceConditions;

    /**
     * 设计人需要自备的工作条件
     */
    private String workConditions;

    /**
     * 发包人的其他要求
     */
    private String otherRequirements;

}
