package com.hzw.sunflower.controller;

import cn.hutool.json.JSONUtil;
import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.entity.DeptdataToNcc;
import com.hzw.sunflower.service.DeptDataToNccService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/10/10 13:47
 * @Version 1.0
 */
@Api(tags = "记账部门 服务")
@RestController
@RequestMapping("/deptdataToNcc")
public class DeptdataToNccController {

    @Autowired
    private DeptDataToNccService deptDataToNccService;

    @ApiOperation(value = "查询记账部门 列表")
    @PostMapping("/list")
    public Result<List<DeptdataToNcc>> getByConditionDicList() {
        List<DeptdataToNcc> deptTreeList = deptDataToNccService.queryDeptdataToNccTreeList();
        if (CollectionUtils.isEmpty(deptTreeList)) {
            return Result.ok(new ArrayList<>());
        } else {
            return Result.ok(JSONUtil.toList(JSONUtil.toJsonStr(deptTreeList), DeptdataToNcc.class));
        }

    }

}
