package com.hzw.sunflower.controller.request;

import com.hzw.sunflower.common.BaseCondition;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class ProjectSectionReq extends BaseCondition {

    @ApiModelProperty(value = "项目id")
    private Long projectId;

    @ApiModelProperty(value = "标段id")
    private Long sectionId;

    @ApiModelProperty(value = "标段id")
    private Long companyId;

    @ApiModelProperty(value = "流水id")
    private Long waterId;

    @ApiModelProperty(value = "退还id")
    private Long refundId;

    @ApiModelProperty(value = "关键字")
    private String keyWords;

    @ApiModelProperty(value = "关联状态, 1.确认关联 2.已关联 3.异常关联 4.未关联")
    private List<Integer> relationStatus;

    @ApiModelProperty(value = "关联状态, 1.确认关联 2.已关联 3.异常关联 4.未关联 5.申请关联待确认")
    private Integer relationStatusOne;

    @ApiModelProperty(value = "退还状态, 0：未退还  1：待处理  2：已退回  3：退还成功 4：退还失败  5.退还中  8:已撤回'")
    private List<Integer> refundStatus;

    @ApiModelProperty(value = "关联状态是否包含未关联")
    private Boolean relationNo = false;

    @ApiModelProperty(value = "关联状态是否包含申请关联")
        private Boolean relationApply = false;

    @ApiModelProperty(value = "退还状态是否包含未退还")
    private Boolean refundNo = false;

}
