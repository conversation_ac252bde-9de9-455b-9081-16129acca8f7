package com.hzw.sunflower.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.hzw.sunflower.common.BaseBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


@ApiModel("招标文件是否支持线上开标")
@TableName("t_project_section_bid_open")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProjectSectionBidOpen extends BaseBean {

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "项目ID")
    private Long projectId;

    @ApiModelProperty(value = "标段id")
    private Long sectionId;

    @ApiModelProperty(value = "线上开标（1.是 2.否）")
    private Integer bidOpenOnline;

    @ApiModelProperty(value ="（分钟）")
    private Integer decryptTime;

    @ApiModelProperty(value = "供应商少于x家终止开标")
    private Integer supNumEndBid;

    @ApiModelProperty(value ="招标文件阶段：1：第一轮；2：第二轮")
    private Integer bidRound;

//    @ApiModelProperty(value ="开标一览表ossId")
//    private Integer bidOpenOssId;
//
//    @ApiModelProperty(value ="开标记录表生成方式：1：拼接；2：解析")
//    private Integer bidOpenRecordMode;

    @ApiModelProperty(value ="是否自动唱标：0：否；1：是")
    private Integer isAutoChant;
}
