package com.hzw.ssm.date.service;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.hzw.ssm.date.dao.ZJKCalendarMapper;
import com.hzw.ssm.date.entity.AutoCheckEntity;
import com.hzw.ssm.date.entity.CalendarEntity;
import com.hzw.ssm.date.exception.ParameterException;
import com.hzw.ssm.date.util.ZJKCalendarMessage;
import com.hzw.ssm.sys.project.entity.ProjectEntity;

/**
 * 专家库专用日期服务类
 * <AUTHOR>
 * @version 1.0
 */
@Service
public class ZJKCalendarService {
	
	@Autowired
	private ZJKCalendarMapper zjkCalendarMapper;
	
	public AutoCheckEntity quertAutoCheck(){
		return zjkCalendarMapper.quertAutoCheck();
	}

	public List<ProjectEntity> queryAutoProjectList(AutoCheckEntity autoCheckEntity){
		return zjkCalendarMapper.queryAutoProjectList(autoCheckEntity);
	}
	
	public int updateAutoCheck(ProjectEntity projectEntity){
		return zjkCalendarMapper.updateAutoCheck(projectEntity);
	}
	
	/**
	 * 查询给定年份的数据
	 */
	public List<CalendarEntity> getCalendarByYear(Integer year){
		return zjkCalendarMapper.getCalendarByYear(year);
	}
	
	/**
	 * 查询给定年份和给定月份的数据
	 */
	public List<CalendarEntity> getCalendarByYearMonth(CalendarEntity calendarEntity){
		return zjkCalendarMapper.getCalendarByYearMonth(calendarEntity);
	}
	
	/**
	 * 查询库中所有年份
	 */
	public List<Integer> getAllYears(){
		return zjkCalendarMapper.getAllYears();
	}
	
	/**
	 * 给定年份,创建该年份日历,入库
	 * -1.要求不能创建超出范围的年份日历(防止数据库数据量过大)
	 * -2.不可创建已经存在的年份日历
	 * -3.创建的日历,只默认周末为节假日,其他均为工作日,需要创建后,手动根据国家颁发的假日通知来设定具体假日信息
	 * @param year-给定年份
	 * @return boolean-是否创建成功 
	 */
	public Map<String,Object> createCalendarForNewYear(Integer year){
		
		Map<String,Object> map=new HashMap<String, Object>();
		
		if(year==null){
			map.put("flag",false);
			map.put("description",ZJKCalendarMessage.NULL_PARAMETER_ERROR);
			return map;
		}
		
		Calendar calendar=Calendar.getInstance();
		calendar.setTime(new Date());
		
		//若申请该服务的时间已经大于系统设定的每天可创建日历的时间范围,则不可创建
		if(calendar.get(Calendar.HOUR_OF_DAY)>=ZJKCalendarMessage.MAX_CREATE_CALENDAR_HOUR&&calendar.get(Calendar.MINUTE)>30){
			map.put("flag",false);
			map.put("description",ZJKCalendarMessage.CREATE_CALENDAR_OVERTIME_ERROR);
			return map;
		}
		
		//给定年份大于当前年份,表示往后创建日历
		if(year>calendar.get(Calendar.YEAR)){
			//不可以超出可以创建的最大未来日历范围
			if(calendar.get(Calendar.YEAR)+ZJKCalendarMessage.MAX_CREATE_FUTURE_YEAR<year){
				map.put("flag",false);
				map.put("description",ZJKCalendarMessage.MAX_CREATE_FUTURE_ERROR);
				return map;
			}
			
		//给定年份小于当前年份,表示往前创建日历	
		}else if(year<calendar.get(Calendar.YEAR)){
			//不可以超出可以创建的最大过去日历范围
			if(calendar.get(Calendar.YEAR)-ZJKCalendarMessage.MAX_CREATE_FORMER_YEAR>year){
				map.put("flag",false);
				map.put("description",ZJKCalendarMessage.MAX_CREATE_FORMER_ERROR);
				return map;
			}
		}
		
		//处于可创建范围的年份,还需要判断是否已经创建过
		if(zjkCalendarMapper.queryCalendarExist(year)>0){
			map.put("flag",false);
			map.put("description",ZJKCalendarMessage.REPEAT_YEAR_ERROR);
			return map;
		}
		
		
		//检验都OK后,在数据库中创建该年份的日历,新建的日历只默认周末为节假日,具体法定假日,法定公休日,调休日等需要以后手动编辑
		calendar.set(Calendar.YEAR, year);
		int count=0;
		CalendarEntity entity=new CalendarEntity();
		entity.setDelete_flag(0);
		entity.setCreater_date(new Date());
		entity.setModify_date(new Date());
		
		//需修改  创建人和修改人
		entity.setCreate_user(0);
		entity.setModify_user(0);

		for(int i=0;i<12;i++){
			entity.setDate_month(i+1);
			calendar.set(Calendar.MONTH, i);
			calendar.set(Calendar.DAY_OF_MONTH, 1);//getActualMaximum()具有延迟,为了获取准时数据,这里把DAY_OF_MONTH设为1,无实际意义,主要为了避免延迟造成BUG
			int maxDaysOfMonth=calendar.getActualMaximum(Calendar.DAY_OF_MONTH);
			for(int j=1;j<=maxDaysOfMonth;j++){
				calendar.set(Calendar.DATE, j);
				entity.setDate_year(year);
				SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM-dd");
				entity.setDate_time(sdf.format(calendar.getTime()));
				
				if(calendar.get(Calendar.DAY_OF_WEEK)==1||calendar.get(Calendar.DAY_OF_WEEK)==7){
					entity.setRemark(ZJKCalendarMessage.DAY_OF_WEEKEED);
					entity.setDate_status(20);	
				}else{
					entity.setRemark(ZJKCalendarMessage.DAY_OF_WORKDAY);
					entity.setDate_status(10);
				}
				count+=zjkCalendarMapper.insertCalendar(entity);
			}
		}
		
		map.put("flag",true);
		map.put("count",count);
		map.put("description","创建["+year+"年]日历成功!");
		return map;
	}
	
	
	
	public int deleteCalendarForYear(Integer year) throws ParameterException{
		if(year==null){
			throw new ParameterException("年份参数不可以为NULL!");
		}
		if(!(zjkCalendarMapper.queryCalendarExist(year)>0)){
			throw new ParameterException("给定的日期所在的年份日历不存在于数据库中!");
		}
		Calendar calendar=Calendar.getInstance();
		calendar.setTime(new Date());
		int deleteYear=calendar.get(Calendar.YEAR);
		if(deleteYear==year||year-deleteYear<ZJKCalendarMessage.MAX_DELETE_FORMER_CALENDAR||deleteYear-year<ZJKCalendarMessage.MAX_DELETE_FUTURE_CALENDAR){
			throw new ParameterException("此年度数据将用于其他服务中,近年的日历数据均不允许删除!");
		}
		return zjkCalendarMapper.deleteCalendarForYear(year);
	}
	
	/**
	 * 给定日期(Date型),判断该日期是否为节假日
	 * -1.需要已经创建好改年度的日历信息
	 * -2.需要已经手动设定好此年度的节假日信息
	 * @param date
	 * @return boolean 
	 * @throws ParameterException
	 */
	public boolean isHoliday(Date date) throws ParameterException{
		if(date==null){
			throw new ParameterException("日期参数不可以为NULL!");
		}
		Calendar calendar=Calendar.getInstance();
		calendar.setTime(date);
		int year=calendar.get(Calendar.YEAR);
		if(!(zjkCalendarMapper.queryCalendarExist(year)>0)){
			throw new ParameterException("给定的日期所在的年份日历不存在于数据库中!");
		}
		SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM-dd");
		int status=zjkCalendarMapper.getCalendarStatus(sdf.format(date));
		if(status==1){
			return false;
		}else{
			return true;
		} 
	}
	
	/**
	 * 给定日期(String型),判断该日期是否为节假日
	 * -1.需要已经创建好改年度的日历信息
	 * -2.需要已经手动设定好此年度的节假日信息
	 * @param date
	 * @return boolean 
	 * @throws ParameterException
	 * @throws ParseException 
	 */
	public boolean isHoliday(String date) throws ParameterException, ParseException{
		if(date==null){
			throw new ParameterException("日期参数不可以为NULL!");
		}
		SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM-dd");
		Calendar calendar=Calendar.getInstance();
		calendar.setTime(sdf.parse(date));
		int year=calendar.get(Calendar.YEAR);
		if(!(zjkCalendarMapper.queryCalendarExist(year)>0)){
			throw new ParameterException("给定的日期所在的年份日历不存在于数据库中!");
		}
		int status=zjkCalendarMapper.getCalendarStatus(date);
		if(status==10||status==11){
			return false;
		}else{
			return true;
		}
	}

	
	/**
	 * 给定日期(String型),判断当前时间是否是给定开标日期的前一个工作日
	 * -1.需要已经创建好改年度的日历信息
	 * -2.需要已经手动设定好此年度的节假日信息
	 * @param date
	 * @return boolean 
	 * @throws ParameterException
	 * @throws ParseException 
	 */
	public boolean isLastWorkDay(String curDate,String bidDate) throws ParameterException, ParseException{
		if(curDate==null||bidDate==null){
			throw new ParameterException("日期参数不可以为NULL!");
		}
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		//中标前第一个工作日
        Calendar calendar = Calendar.getInstance();
        //中标时间
        Calendar bidCalendar = Calendar.getInstance();
        //当前时间
        Calendar curCalendar = Calendar.getInstance();
        //中标前第二个工作日
        Calendar twoCalendar = Calendar.getInstance();
		//获取开标时间前一个工作日日期
		/*
		 * 12.31修改开标日期两天内是否包含节假日，如果含有节假日则判断为两个工作日前否则为前两个工作日
		 */
		String onebidDate=zjkCalendarMapper.getCalendarStatus2(bidDate);
		curCalendar.setTime(sdf.parse(curDate));
		bidCalendar.setTime(sdf.parse(bidDate));
		calendar.setTime(sdf.parse(onebidDate));
        calendar.add(Calendar.DATE, 1);
        String oneBidDateTimple = sdf.format(calendar.getTime());
        //判断开标日期的上一天是否为工作日
        if(oneBidDateTimple.equals(bidDate)) {//工作日
        	//判断当前时间是否是开标前一天工作日
    		if(curDate.equals(onebidDate)){
    			return true;
    		}else{
    			//获取开标时间前两个工作日
    			String twobidDate=zjkCalendarMapper.getCalendarStatus2(onebidDate);
    			//中标日期前两天
    			calendar.add(Calendar.DATE, -2);
    			String twoBidDateTimple = sdf.format(calendar.getTime());
    			//判断前第二天是否为节假日
    			if(twobidDate.equals(twoBidDateTimple)) {
    				//判断当前时间是否是开标前两个工作日
        			if(curDate.equals(twobidDate)){
        				return true;
        			}
    			}else {
    				twoCalendar.setTime(sdf.parse(twobidDate));
    				//判断两个工作日内需要减一天
    				twoCalendar.add(Calendar.DATE, -1);
    				if(twoCalendar.before(curCalendar) && bidCalendar.after(curCalendar)) {
    					return true;
    				}
    			}
    		}
        }else {
        	//获取开标时间前第二个工作日
			String twobidDate=zjkCalendarMapper.getCalendarStatus2(onebidDate);
			twoCalendar.setTime(sdf.parse(twobidDate));
			//判断两个工作日内需要减一天
			twoCalendar.add(Calendar.DATE, -1);
			if(twoCalendar.before(curCalendar) && bidCalendar.after(curCalendar)) {
				return true;
			}
        }
        return false;
	}
	
	public int updateStatus(CalendarEntity calendarEntity){
		return zjkCalendarMapper.updateStatus(calendarEntity);
	}
}
