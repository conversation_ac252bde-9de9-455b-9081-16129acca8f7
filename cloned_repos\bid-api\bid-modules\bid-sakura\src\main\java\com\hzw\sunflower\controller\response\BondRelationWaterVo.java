package com.hzw.sunflower.controller.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2022/7/27 11:31
 * @description：标段流水关联详情返回实体类
 * @modified By：`
 * @version: 1.0
 */
@ApiModel(description = "标段流水关联详情返回实体类")
@Data
public class BondRelationWaterVo implements Serializable {

    private Long id;

    @ApiModelProperty("供应商名称")
    private String companyName;

    @ApiModelProperty("供应商付款账户")
    private String companyAccount;

    @ApiModelProperty("交易金额（保留两位小数）")
    private BigDecimal amount;

    @ApiModelProperty("交易日期")
    private String date;

    @ApiModelProperty("交易时间")
    private String time;

    @ApiModelProperty("标段信息")
    List<ProjectWaterVo> sectionList;

}
