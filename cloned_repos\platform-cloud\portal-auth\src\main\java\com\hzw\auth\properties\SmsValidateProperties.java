package com.hzw.auth.properties;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * @ClassName:SmsValidateProperties
 * @Auther: lijinxin
 * @Description: 短信验证配置
 * @Date: 2023/8/7 09:14
 * @Version: v1.0
 */

@Data
@Configuration
@RefreshScope
@ConfigurationProperties(prefix = "user.sms")
public class SmsValidateProperties {

    private Boolean validate;
}
