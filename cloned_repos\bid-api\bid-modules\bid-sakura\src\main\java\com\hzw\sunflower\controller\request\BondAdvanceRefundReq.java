package com.hzw.sunflower.controller.request;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 保证金提前退还申请表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-27
 */
@Data
@ApiModel(value = "BondWater对象", description = "保证金提前退还申请表")
public class BondAdvanceRefundReq implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "流水ids")
    private String waterIds;

    @ApiModelProperty(value = "保证金退还金额")
    private BigDecimal refundMoney;

    @ApiModelProperty(value = "状态：1：待处理  2：已退回    3：退还成功 4：退还失败")
    private Integer status;

    @ApiModelProperty(value = "退还时间")
    private Date refundTime;

    @ApiModelProperty(value = "退还流程code")
    private String refundProcessCode;

    @ApiModelProperty(value = "提前退还说明函")
    private Long advanceRefundFile;

}
