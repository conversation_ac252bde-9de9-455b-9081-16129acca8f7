package com.hzw.sunflower.constant.constantenum;

/**
 * <AUTHOR>
 * @date ：Created in 2021/4/15 10:36
 * @description：标的物分类
 * @modified By：`
 * @version: 1.0
 */
public enum SubjectMatterTypeLengthEnum {

    ONE(1, "一级分类"),
    TWO(3, "二级分类"),
    THREE(5, "三级分类"),
    FOUR(7, "四级分类");

    private Integer length;
    private String desc;

    SubjectMatterTypeLengthEnum(Integer length, String desc) {
        this.length = length;
        this.desc = desc;
    }

    public Integer getLength() {
        return length;
    }

    public void setType(Integer length) {
        this.length = length;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

}
