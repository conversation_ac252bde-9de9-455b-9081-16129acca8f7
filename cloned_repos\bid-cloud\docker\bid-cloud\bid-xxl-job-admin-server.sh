#!/bin/sh
#cp TEMPLATE FILE
docker stop bid-xxl-admin || true
docker rm bid-xxl-admin || true
docker rmi registry.cn-hangzhou.aliyuncs.com/hzw-jtcc/jtcc:bid-xxl-admin-1.0.0 || true
docker pull registry.cn-hangzhou.aliyuncs.com/hzw-jtcc/jtcc:bid-xxl-admin-1.0.0
docker run -itd --net=host  --log-opt max-size=100m --log-opt max-file=3  --name bid-xxl-admin -v /home/<USER>/plan1/bid-cloud/config/bid-xxl-admin:/hzw/xxl-job-admin/config -v /data/log:/hzw/xxl-job-admin/logs  registry.cn-hangzhou.aliyuncs.com/hzw-jtcc/jtcc:bid-xxl-admin-1.0.0
