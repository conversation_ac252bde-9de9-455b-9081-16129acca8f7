#!/bin/sh
docker stop portal-expertmge-ui || true
docker rm portal-expertmge-ui || true
docker rmi registry.cn-hangzhou.aliyuncs.com/hzw-jtcc/jtcc:portal-expertmge-ui-1.0.0 || true
docker pull registry.cn-hangzhou.aliyuncs.com/hzw-jtcc/jtcc:portal-expertmge-ui-1.0.0
docker run -itd -p 28999:80  --log-opt max-size=100m --log-opt max-file=3 --name portal-expertmge-ui -v /home/<USER>/plan1/portal-cloud/config/portal-expertmge-ui:/etc/nginx/conf.d registry.cn-hangzhou.aliyuncs.com/hzw-jtcc/jtcc:portal-expertmge-ui-1.0.0
