package com.hzw.ssm.util.http;

import javax.servlet.http.HttpServletRequest;

import com.hzw.ssm.util.string.StringUtils;

/**
 * 
 * <一句话功能简述> http信息基础工具
 * <功能详细描述>
 * <AUTHOR>
 * @version V1.0[产品/模块版本]
 */
public class HttpInfoUtils {

    /**
     * 
     * 函数功能描述：获取请求者的ip地址
     * @param request
     * @return
     */
    public static String getRemoteIp(HttpServletRequest request) {

        String ip = request.getHeader("x-forwarded-for");

        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }

        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }

        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }

        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }

        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        String[] ips = ip.split(",");
        for (int i = 0; i < ips.length; i++) {
            if (!StringUtils.isEmpty(ips[i]) && !"unknown".equals(ips[i])) {
                ip = ips[i];
                break;
            }
        }
        return ip;
    }
}
