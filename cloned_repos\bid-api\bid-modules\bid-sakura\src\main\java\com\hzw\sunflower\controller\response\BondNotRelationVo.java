package com.hzw.sunflower.controller.response;

import com.hzw.sunflower.entity.BondWater;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@ApiModel(description = "保证金未关联流水")
@Data
public class BondNotRelationVo extends BondWater implements Serializable {

    @ApiModelProperty("流水状态,1待申请退款,2待处理,3退款中,4已退款,5已退回")
    private Integer status;

    @ApiModelProperty("原因")
    private String applyRemark;

}
