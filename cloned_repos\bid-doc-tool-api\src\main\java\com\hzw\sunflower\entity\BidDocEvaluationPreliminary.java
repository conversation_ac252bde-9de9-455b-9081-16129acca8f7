package com.hzw.sunflower.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.hzw.sunflower.common.BaseBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(description = "初步评审评标办法表")
@TableName("t_bid_doc_evaluation_preliminary")
@Data
public class BidDocEvaluationPreliminary extends BaseBean {

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "评标办法表id")
    private Long docEvaluationId;

    @ApiModelProperty(value = "评标方法")
    private String evaluationApproach;

    @ApiModelProperty(value = "评审因素")
    private String reviewFactor;

    @ApiModelProperty(value = "评审标准")
    private String reviewCriteria;

}
