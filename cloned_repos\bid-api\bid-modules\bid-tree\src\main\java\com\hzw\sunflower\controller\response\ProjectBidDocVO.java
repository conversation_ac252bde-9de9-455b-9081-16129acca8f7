package com.hzw.sunflower.controller.response;

import com.hzw.sunflower.dto.ProjectBidDocDTO;
import com.hzw.sunflower.dto.ProjectBidSectionDTO;
import com.hzw.sunflower.entity.ProjectBidSection;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * TProjectBidDoc 返回实体
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@ApiModel(description = "招标文件返回参数")
@Data
public class ProjectBidDocVO extends ProjectBidDocDTO {

    @ApiModelProperty(value = "标段ID", position = 995)
    private List<Long> sectionIdArray;

    @ApiModelProperty(value = "招标文件递交截止时间集合", position = 996)
    private List<Date> submitEndTimeArray;

    @ApiModelProperty(value = "保证金金额(比例换算后金额)", position = 997)
    private List<BigDecimal> bondArray;

    @ApiModelProperty(value = "保证金比例换", position = 998)
    private List<BigDecimal> bondPercentArray;

    @ApiModelProperty(value = "标书费金额（按标段包）", position = 999)
    private List<BigDecimal> tenderFeeArray;

    @ApiModelProperty(value = "前端定义的所需数据结构", position = 1000)
    private BidFileInfo fileInfo;

    @ApiModelProperty(value = "招标文件发售结束时间集合", position = 1001)
    private List<Date> saleEndTimeArray;

    @ApiModelProperty(value = "关联标段", position = 1002)
    private List<ProjectBidDocDTO> relevantSection;

    @ApiModelProperty(value = "标段集合", position = 1003)
    private List<ProjectBidSection> sectionArray;

    @ApiModelProperty(value = "线上澄清（1 支持 2 不支持）")
    private Integer clarifyOnLine;

    @ApiModelProperty(value ="提出澄清截止时间")
    private List<Date> clarifyEndTimeArray;

    @ApiModelProperty(value ="提出澄清截止时间（1：确定时间; 2：投标文件递交截止时间前一天）")
    private Integer clarifyEndTimeType;

    @ApiModelProperty(value = "线上异议（1 支持 2 不支持）")
    private Integer questionOnLine;

    @ApiModelProperty(value ="提出异议截止时间（1：确定时间; 2：投标文件递交截止时间前一天）")
    private Integer questionEndTimeType;

    @ApiModelProperty(value ="提出异议截止时间")
    private List<Date> questionEndTimeArray;

    @ApiModelProperty(value = "线上开标（1 是 2 否）")
    private Integer onlineBidOpen;

    @ApiModelProperty(value = "解密时间")
    private List<Integer> decryptionTimeArray;

    @ApiModelProperty(value = "开标条件-终止开标供应商限制数量")
    private List<Integer> conditionBidOpenArray;

    @ApiModelProperty(value = "开标一览表文件")
    private List<Long> bidOpeningArray;

    @ApiModelProperty(value = "开标记录表生成方式")
    private List<Integer> generationArray;

    @ApiModelProperty(value = "系统自动唱标数组")
    private List<Integer> systemAutomaticLabelingArray;
}
