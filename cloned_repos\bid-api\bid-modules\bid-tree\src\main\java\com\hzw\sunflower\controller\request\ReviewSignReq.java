package com.hzw.sunflower.controller.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2023/9/11 15:57
 * @description：承诺文件签署请求体
 * @version: 1.0
 */
@Data
public class ReviewSignReq {

    @ApiModelProperty(value = "专家用户id")
    private Long userId;

    @ApiModelProperty(value = "源文件ossid")
    private Long originFileOssId;

    @ApiModelProperty(value = "评审ID")
    private Long reviewId;

    @ApiModelProperty(value = "标段ID")
    private Long sectionId;

    @ApiModelProperty(value = "签章备注")
    private String remark;

    @ApiModelProperty(value = "文件类型")
    private Integer fileType;

}
