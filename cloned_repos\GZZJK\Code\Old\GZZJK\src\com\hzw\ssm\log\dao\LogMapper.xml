<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hzw.ssm.log.dao.LogMapper">

	<!-- 如果需要查询缓存，将cache的注释去掉即可
	<cache />
	-->
	
	<!-- 插入日志 -->
	<insert id="insertLog" parameterType="LogEntity">
		INSERT INTO t_sys_log_list(
			id,
			user_id,
			opa_date,
			opa_ip,
			page_name,
			method_name,
			opa_type
		) VALUES (#{id},#{userId},#{opaDate},#{opaIp},#{pageName},#{methodName},#{opaType})
	</insert>

</mapper>