package com.hzw.sunflower.controller.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2023/3/13 19:26 星期一
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CaBidOpeningVo {

    @ApiModelProperty("累计电子开标项目部门")
    private String departmentName;

    @ApiModelProperty("累计电子开标项目数量")
    private Integer departmentNum;

}
