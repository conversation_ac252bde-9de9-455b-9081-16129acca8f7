<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!-- 用户表 (t_user) -->
<mapper namespace="com.hzw.sunflower.dao.UserOpenMapper">

    <select id="getUserTable" resultType="com.hzw.sunflower.controller.response.UserTableVo">
        SELECT
        c.company_name,
        u.user_name,
        u.identity_number,
        u.status as userStatus,
        ui.user_phone,
        ui.created_time,
        ui.legal_person_authorization,
        ui.STATUS,
        ui.identity,
        ui.id,
        ui.user_id,
        ui.remark,
        d.id as departmentId,
        d.department_name,
        r.role_name,
        r.id as roleId
        FROM
        t_user_identity ui
        LEFT JOIN t_user u ON ui.user_id = u.id and ui.is_delete = 0 AND u.is_delete = 0
        LEFT JOIN r_user_department ud ON ui.identity = ud.user_identity_id AND ui.user_id = ud.user_id
        LEFT JOIN t_department d ON ud.department_id = d.id
        LEFT JOIN r_user_role ur ON ui.user_id = ur.user_id and ui.identity = ur.user_identity_id
        LEFT JOIN t_role r ON ur.role_id = r.id
        left join t_company c on ui.company_id = c.id
        where
        ui.is_delete = 0
        and u.is_delete = 0
        and c.is_delete = 0
        and c.id is not null
        and ui.status = 1
        AND (ud.is_delete = 0 or ud.is_delete is null)
        AND (d.is_delete = 0 or d.is_delete is null)
        <if test="condition.identity != null and condition.identity != 0">
            and ui.identity = #{condition.identity}
        </if>
        <if test="condition.statusArr != null and condition.statusArr.size()>0">
            and ui.status in
            <foreach item="item" collection="condition.statusArr" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="condition.companyId != null and condition.companyId != 0">
            and ui.company_id = #{condition.companyId}
        </if>
        <if test="condition.keywords != null and condition.keywords != '' ">
            and (
            u.user_name like concat('%',#{condition.keywords},'%')
            or ui.user_phone like concat('%',#{condition.keywords},'%')
            or c.company_name like concat('%',#{condition.keywords},'%')
            )
        </if>
        order by ui.created_time desc
    </select>


    <select id="getCompanySupervise" resultType="com.hzw.sunflower.controller.response.CompanySuperviseVo">
        SELECT
        c.id,
        c.company_name,
        c.organization_num,
        c.legal_representative,
        c.created_time,
        (case when (ui.identity = 1 and c.bidder_disable = 1) then 1 when (ui.identity = 2 and c.agency_disable = 1)
        then 1 when (ui.identity = 3 and c.supplier_disable = 1) then 1 else 0 end ) as company_status, ui.identity,
        count( ui.id ) AS userCount
        FROM
        t_company c
        LEFT JOIN t_user_identity ui ON c.id = ui.company_id
        where ui.is_delete = 0 and c.is_delete = 0
        <if test="condition.identity != null and condition.identity != 0">
            and ui.identity = #{condition.identity}
            <if test="condition.identity == 1">
                and c.bidder_disable = 0
            </if>
            <if test="condition.identity == 2">
                and c.agency_disable = 0
            </if>
            <if test="condition.identity == 3">
                and c.supplier_disable = 0
            </if>
        </if>
        <if test="condition.keywords != null and condition.keywords != '' ">
            and (
            c.company_name like concat('%',#{condition.keywords},'%')
            or c.organization_num like concat('%',#{condition.keywords},'%')
            or c.legal_representative like concat('%',#{condition.keywords},'%')
            )
        </if>
        <if test="condition.companyId != null">
            and c.id = #{condition.companyId}
        </if>
        GROUP BY
        c.id,
        c.company_name,
        c.organization_num,
        c.legal_representative,
        c.created_time,
        ui.identity,
        c.company_status
        order by c.created_time desc
    </select>



    <select id="getCompanyInfoList" resultType="com.hzw.sunflower.controller.response.CompanySuperviseVo">
        SELECT
        c.id,
        c.company_name,
        c.organization_num,
        c.legal_representative,
        c.created_time
        FROM
        t_company c
        where  c.is_delete = 0
        <if test="keywords != null and keywords != ''">
            and (
            c.company_name like concat('%',#{keywords},'%')
            or c.organization_num like concat('%',#{keywords},'%')
            or c.legal_representative like concat('%',#{keywords},'%')
            )
        </if>
        <if test="companyId != null">
            and c.id = #{companyId}
        </if>
        order by c.created_time desc
    </select>


    <select id="getCompanyIdByUserId"  resultType="java.lang.Long">
        SELECT
            company_id
        FROM
            t_user_identity
        WHERE
            identity = 1
          AND is_delete = 0
          AND company_id IS NOT NULL
          AND user_id = #{userId}
    </select>


    <select id="getUserRoleByUserId" parameterType="java.lang.Long"
            resultType="com.hzw.sunflower.controller.response.RoleVo">
        SELECT
            r.id,
            r.role_name,
            r.role_code
        FROM
            t_user u
            LEFT JOIN t_user_identity ui ON u.id = ui.user_id and ui.`identity` = 2
            LEFT JOIN r_user_role ur ON ui.user_id = ur.user_id and ui.identity = ur.user_identity_id
            LEFT JOIN t_role r ON ur.role_id = r.id
        WHERE
            u.id = #{userId}
            and (ui.is_delete = 0 or ui.is_delete is null)
            and u.is_delete = 0
            and (r.is_delete = 0 or r.is_delete is null)
            and (ur.is_delete = 0 or ur.is_delete is null)
    </select>


    <select id="getCompanyInfoById" parameterType="java.lang.Long"
            resultType="com.hzw.sunflower.controller.response.OpenCompanyVo">
        select * from t_company  t where t.id = #{companyId}
    </select>

    <select id="getUserInfoById" resultType="com.hzw.sunflower.controller.response.UserDeptCompanyVo">
        SELECT
            u.id AS userId,
            u.user_name,
            u.identity_number,
            u.passport_number,
            u.nation_code,
            u.is_user_abroad,
            u.email,
            ui.identity,
            ui.id AS userIdentityId,
            ui.user_phone,
            ui.legal_person_authorization AS fileId,
            ui.is_admin,
            ui.STATUS,
            ui.company_id,
            d.id AS departmentId,
            d.department_name,
            d.`code` AS departmentCode,
            c.company_name,
            c.organization_num,
            c.business_license,
	        c.is_company_abroad,
            c.legal_representative,
            c.legal_representative_identity
        FROM
            t_user u
            LEFT JOIN t_user_identity ui ON u.id = ui.user_id
            LEFT JOIN r_user_department ud ON ui.user_id = ud.user_id AND ui.identity = ud.user_identity_id
            LEFT JOIN t_department d ON ud.department_id = d.id
            LEFT JOIN t_company c ON c.id = ui.company_id
        WHERE
            u.is_delete = 0
            AND ui.is_delete = 0
            AND (ud.is_delete = 0 or ud.is_delete is null)
            AND (d.is_delete = 0 or d.is_delete is null)
            AND c.is_delete = 0
            and ui.identity = #{identity}
            AND u.id = #{userId}
    </select>


    <select id="getCompanyUser" resultType="com.hzw.sunflower.controller.response.UserInfoVo">
        select u.user_name,u.identity_number,u.status as
        userStatus,ui.user_phone,ui.created_time,ui.legal_Person_Authorization,
        ui.status,ui.identity,ui.id,ui.user_id,ui.remark,c.company_name
        from t_user u
        left join t_user_identity ui
        on u.id = ui.user_id
        left join t_company c on ui.company_id = c.id
        where u.is_delete = 0
          and ui.is_delete = 0
          and c.is_delete = 0
          and ui.company_id = #{condition.companyId}
        <if test="condition.identity != null and condition.identity != '' ">
            and ui.identity = #{condition.identity}
        </if>
          and ui.status in (1,3,4,5,6,11,13,-97,40,41)
        <if test="condition.userName != null and condition.userName != '' ">
            and u.user_name like concat('%',#{condition.userName},'%')
        </if>

    </select>

    <select id="getCompanyUserInfoByPhone" resultType="com.hzw.sunflower.controller.response.UserInfoVo">
        select
        distinct
        u.user_name,
        u.identity_number,
        u.status as
        userStatus,
        ui.user_phone,
        ui.created_time,
        ui.legal_Person_Authorization,
        ui.status,
        ui.identity,
        ui.id,
        ui.user_id,
        ui.remark,
        c.company_name
        from
        t_user u
        left join t_user_identity ui on u.id = ui.user_id
        left join t_company c on ui.company_id = c.id
        left join r_user_department rud   on rud.user_id  = u.id
        left join t_department td     on rud.department_id  = td.id
        where
        u.is_delete = 0
        and ui.is_delete = 0
        and c.is_delete = 0
        and rud.is_delete = 0
        and td.is_delete = 0
        and td.bind_company  =  #{condition.companyId}
        <if test="condition.identity != null and condition.identity != '' ">
            and ui.identity = #{condition.identity}
        </if>
        and ui.status in (1,3,4,5,6,11,13,-97)
        and ui.user_phone = #{condition.userPhone}
        <if test="condition.userName != null and condition.userName != '' ">
            and u.user_name like concat('%',#{condition.userName},'%')
        </if>

    </select>


    <select id="getDeptInfoByUserId" resultType="com.hzw.sunflower.controller.response.DepartmentInfoVo">
        select t.* from t_department t
        LEFT JOIN  ud
        on t.id = ud.department_id
        left join t_user_identity ui
        on ud.user_id = ui.user_id and ud.user_identity_id = ui.identity
        where ui.user_id = #{userId} and ui.identity = #{identity}
        and t.is_delete = 0 and ud.is_delete = 0 and ui.is_delete = 0
    </select>

    <select id="getUserSmsConfig" resultType="com.hzw.sunflower.controller.response.UserSmsConfigVo">
        SELECT usc.* from t_user_sms_config usc
        LEFT JOIN t_template t on usc.template_id = t.id
        where usc.user_id = #{userId}
        and (t.template_code = #{code} or t.template_second_code = #{code})
    </select>

    <!-- 获取项目经理所属处室处长信息 -->
    <select id="getDirectorByUserId" resultType="com.hzw.sunflower.controller.response.UserInfoVo">
        SELECT ui.*, u.user_name from t_user_identity ui
        LEFT JOIN t_user u on ui.user_id = u.id
        LEFT JOIN r_user_role ur on ur.user_id = u.id
        LEFT JOIN t_role r on r.id = ur.role_id
        LEFT JOIN r_user_department  ud on ud.user_id = u.id
        LEFT JOIN t_department d on ud.department_id = d.id
        where r.role_code = #{roleCode}
        and ur.is_delete = 0
        and ud.is_delete = 0
        and u.is_delete = 0
        and ui.is_delete = 0
        and d.is_delete = 0
        and d.code = #{deptCode}
        <!-- and ud.department_id = (SELECT department_id from r_user_department where user_id = #{userId} and is_delete = 0)-->
    </select>

    <!-- 获取标段最早的开标时间和发售结束时间 -->
    <select id="getEarliestTimeBySectionIds" resultType="com.hzw.sunflower.controller.response.EarlySectionTimeVo">
        SELECT IFNULL(MIN(submit_end_time),'另行通知') submit_end_time, IFNULL(MIN(sale_end_time),'另行通知') sale_end_time from (
        SELECT
        CASE submit_end_time_type WHEN 1 THEN submit_end_time ELSE NULL END AS submit_end_time,
        CASE sale_end_time_type WHEN 1 THEN sale_end_time WHEN 3 THEN DATE_ADD(submit_end_time,INTERVAL -1 DAY) ELSE NULL END AS sale_end_time
        from t_project_bid_section
        where id in
        <foreach collection="subIdList" item="item" separator="," open="(" close=")" >
            #{item}
        </foreach>
        and is_delete = 0
        and submit_end_time is not null
        and sale_end_time is not null
        ) a
    </select>


    <select id="findUserWechatByPhone" resultType="com.hzw.sunflower.entity.User">
        SELECT
            t2.*
        FROM
            t_user_identity t1
                LEFT JOIN t_user t2 ON t1.user_id = t2.id
        WHERE   t1.is_delete = 0
                AND t2.is_delete = 0
                AND t1.identity = #{identity}
                AND t1.user_phone = #{phone}
    </select>

    <!-- 获取已递交投标文件供应商 -->
    <select id="getCompanyHasBidDoc" resultType="com.hzw.sunflower.controller.response.CompanyHasBidDocVo">
        SELECT
            a.apply_id,
            a.project_id,
            a.sub_id,
            a.company_id,
            a.company_name,
            a.contact_person,
            a.user_mobile,
            a.download_flag,
            p.purchase_name,
            p.purchase_number,
            p.package_segment_status,
            s.package_number,
            bo.decrypt_time,
            DATE_ADD(s.submit_end_time,INTERVAL bo.decrypt_time MINUTE) decrypt_end_time
        FROM
            t_apply_info a
        LEFT JOIN t_project p ON a.project_id = p.id
        LEFT JOIN t_project_bid_section s ON s.id = a.sub_id
        LEFT JOIN t_project_section_bid_open bo on bo.section_id = a.sub_id and bo.bid_round = a.bid_round
        WHERE
            a.sub_id = #{sectionId}
          AND a.bid_round = #{bidRound}
          and a.is_delete = 0
          and (SELECT COUNT(1) from t_apply_response_file where apply_id = a.apply_id and is_delete = 0) > 0
    </select>



    <select id="getChuZhangByUserId" resultType="com.hzw.sunflower.entity.User">
        SELECT DISTINCT
            u.*
        FROM
            t_user u
            LEFT JOIN r_depart_role dr ON u.id = dr.user_id
        WHERE
            u.is_delete = 0
            AND dr.is_delete = 0
            AND dr.role_id IN (
            select id from t_role where role_code in
            <foreach collection="czCodes" item="item" separator="," open="(" close=")" >
                #{item}
            </foreach>
            and is_delete = 0
            )
            AND dr.depart_id IN (
                SELECT
                    department_id
                FROM
                    r_user_department
                WHERE
                    user_id = #{userId}
                    AND is_delete = 0
        )
    </select>

    <select id="getUserByRole" resultType="com.hzw.sunflower.entity.User">
        SELECT DISTINCT
        u.*,
        dr.depart_id
        FROM
        t_user u
        LEFT JOIN r_depart_role dr ON u.id = dr.user_id
        WHERE
        u.is_delete = 0
        AND dr.is_delete = 0
        AND dr.role_id IN (
        select id from t_role where role_code in
        <foreach collection="czCodes" item="item" separator="," open="(" close=")" >
            #{item}
        </foreach>
        )
    </select>

    <select id="getChuZhangByUserIdDepartId" resultType="com.hzw.sunflower.entity.User">
        SELECT DISTINCT
        u.*
        FROM
        t_user u
        LEFT JOIN r_depart_role dr ON u.id = dr.user_id
        WHERE
        u.is_delete = 0
        AND dr.is_delete = 0
        AND dr.role_id IN (
        select id from t_role where role_code in
        <foreach collection="czCodes" item="item" separator="," open="(" close=")" >
            #{item}
        </foreach>
        and is_delete = 0
        )
        <if test="null != departId">
            AND dr.depart_id = #{departId}
        </if>
        AND dr.depart_id IN (
        SELECT
        department_id
        FROM
        r_user_department
        WHERE
        user_id = #{userId}
        AND is_delete = 0
        )
    </select>

    <select id="getLeaderByDepartId" resultType="com.hzw.sunflower.entity.User">
        SELECT DISTINCT
        u.*,
        dr.depart_id
        FROM
        t_user u
        LEFT JOIN r_depart_role dr ON u.id = dr.user_id
        WHERE
        u.is_delete = 0
        AND dr.is_delete = 0
        AND dr.role_id IN (
        select id from t_role where role_code in
        <foreach collection="czCodes" item="item" separator="," open="(" close=")" >
            #{item}
        </foreach>
        and is_delete = 0
        )
        AND dr.depart_id = (select id from t_department where bind_company  = (select bind_company from t_department where id = #{departId}) and is_company = '1' limit 1)
    </select>

    <select id="getDepartInfoByOtherUserId" resultType="com.hzw.sunflower.controller.response.DepartmentInfoVo">
            SELECT DISTINCT
                t.*
            FROM
                (
                SELECT
                    d.*
                FROM
                    t_department d
                    LEFT JOIN r_depart_role dr ON d.id = dr.depart_id
                    LEFT JOIN t_user u ON dr.user_id = u.id
                WHERE
                    d.is_delete = 0
                    AND dr.is_delete = 0
                    AND u.is_delete = 0
                    AND u.other_user_id = #{otherUserId}
                    AND d.other_id IS NOT NULL
                ORDER BY
                    dr.is_main,
                    d.id
                ) t
    </select>
    <select id="queryUserNccInfo" resultType="com.hzw.sunflower.controller.response.UserNccInfoVo">
        SELECT
            ui.user_phone
        FROM
            t_user_identity ui
        WHERE
            ui.is_delete = 0
            AND ui.user_id = #{userId}
            AND ui.identity = #{identity}
    </select>
    <select id="getUserByDepartId" resultType="com.hzw.sunflower.entity.User">
        SELECT DISTINCT
        u.*
        FROM
        t_user u
        LEFT JOIN r_depart_role dr ON u.id = dr.user_id
        WHERE
        u.is_delete = 0
        AND dr.is_delete = 0
        AND dr.role_id IN (
        select id from t_role where role_code like concat(#{code},'%')
        and is_delete = 0
        )
        <if test="null != deptId">
            AND dr.depart_id = #{deptId}
        </if>
        <if test="null != userId">
            AND dr.depart_id IN (
            SELECT
            department_id
            FROM
            r_user_department
            WHERE
            user_id = #{userId}
            AND is_delete = 0
            )
        </if>
    </select>
    <select id="getDirectorByUserIdF" resultType="com.hzw.sunflower.controller.response.UserInfoVo">
        SELECT ui.*, u.user_name from t_user_identity ui
        LEFT JOIN t_user u on ui.user_id = u.id
        LEFT JOIN r_user_role ur on ur.user_id = u.id
        LEFT JOIN t_role r on r.id = ur.role_id
        LEFT JOIN r_user_department  ud on ud.user_id = u.id
        LEFT JOIN t_department d on ud.department_id = d.id
        where r.role_code = #{roleCode}
        and ur.is_delete = 0
        and ud.is_delete = 0
        and u.is_delete = 0
        and ui.is_delete = 0
        and d.is_delete = 0
        and d.code like concat(#{deptCode},'%')
    </select>
    <select id="getZJLDepartId" resultType="com.hzw.sunflower.entity.User">
        SELECT DISTINCT
        u.*,
        dr.depart_id
        FROM
        t_user u
        LEFT JOIN r_depart_role dr ON u.id = dr.user_id
        WHERE
        u.is_delete = 0
        AND dr.is_delete = 0
        AND dr.role_id IN (
        select id from t_role where role_code like concat(#{code},'%')
        and is_delete = 0
        )
        AND dr.depart_id = (select id from t_department where bind_company  = (select bind_company from t_department where id = #{deptId}) and is_company = '1' limit 1)
    </select>
    <select id="getByOtherId" resultType="com.hzw.sunflower.entity.User">
        SELECT
            t1.id,
            t1.user_name
        FROM
            t_user t1
        left join t_user_identity t2 on t1.id = t2.user_id and t2.`identity` = 2
        where
            t1.other_user_id = #{userCode}
            and t1.is_delete = 0
            and t2.is_delete = 0
        limit 1
    </select>

</mapper>
