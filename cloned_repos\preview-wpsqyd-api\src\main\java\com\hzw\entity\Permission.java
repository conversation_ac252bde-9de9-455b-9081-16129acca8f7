package com.hzw.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class Permission {
    // 文档读取权限
    @JsonProperty("read")
    private Integer read = 1;
    // 文档拷贝权限
    @JsonProperty("copy")
    private Integer copy = 1;
    // 文档下载权限
    @JsonProperty("download")
    private Integer download = 1;
    // 文档打印权限
    @JsonProperty("print")
    private Integer print = 1;
    // 文档编辑权限
    @JsonProperty("write")
    private Integer write = 1;
    // 文档签章权限
    @JsonProperty("seal")
    private Integer seal = 1;
}
