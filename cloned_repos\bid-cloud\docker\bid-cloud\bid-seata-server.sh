#!/bin/sh
#cp TEMPLATE FILE
docker stop bid-seata-server || true
docker rm bid-seata-server || true
docker rmi registry.cn-hangzhou.aliyuncs.com/hzw-jtcc/jtcc:bid-seata-server-1.0.0 || true
docker pull registry.cn-hangzhou.aliyuncs.com/hzw-jtcc/jtcc:bid-seata-server-1.0.0
docker run -itd --net=host -e SEATA_IP=************** --log-opt max-size=100m --log-opt max-file=3  --name bid-seata-server -v /home/<USER>/plan1/bid-cloud/config/bid-seata-server:/hzw/seata-server/config -v /data/log:/hzw/seata-server/logs  registry.cn-hangzhou.aliyuncs.com/hzw-jtcc/jtcc:bid-seata-server-1.0.0
