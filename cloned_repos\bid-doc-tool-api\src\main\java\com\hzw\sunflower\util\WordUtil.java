package com.hzw.sunflower.util;

import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.data.CellRenderData;
import com.deepoove.poi.data.Includes;
import com.deepoove.poi.data.Rows;
import com.deepoove.poi.data.Tables;
import com.deepoove.poi.data.style.BorderStyle;
import com.deepoove.poi.xwpf.NiceXWPFDocument;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.xwpf.usermodel.*;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTP;

import java.io.*;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @datetime 2024/06/25 16:17
 * @description: word合并
 * @version: 1.0
 */
@Slf4j
public class WordUtil {

    private static final String[] CHINESE_NUMBERS = {"零", "一", "二", "三", "四", "五", "六", "七", "八", "九"};
    private static final String[] CHINESE_UNITS = {"", "十", "百", "千", "万", "亿"};

    //生成投标文件word目录
    //给投标文件模板word添加标题
    //合并上传的投标文件模板word

    /**
     * word文档添加标题
     * @param title
     * @param doc
     * @return
     */
    public static NiceXWPFDocument addTitleForWord(String title,NiceXWPFDocument doc){
        try {
            // 加载Word文档
            CTP ctpHeader = CTP.Factory.newInstance();
            XWPFParagraph paraTitle = new XWPFParagraph(ctpHeader, doc);
            XWPFRun runTitle = paraTitle.createRun();
            // 样式
            runTitle.setText(title);
            runTitle.setBold(true);
            runTitle.setFontSize(16);
            runTitle.setTextPosition(20);
            // 段落位置
            paraTitle.setAlignment(ParagraphAlignment.CENTER);
            // 插入第一个
            doc.setParagraph(paraTitle,0);
        } catch (Exception e) {
            log.error("投标文件word添加标题失败",e);
        }
        return doc;
    }

    /**
     * 生成目录
     * @param titleList
     * @return
     */
    public static NiceXWPFDocument createDirectory(List<String> titleList){
        NiceXWPFDocument directory = new NiceXWPFDocument();
        try {
            //创建目录
            XWPFParagraph paraTitle = directory.createParagraph();
            XWPFRun runTitle = paraTitle.createRun();
            // 样式
            runTitle.setText("目录");
            runTitle.setBold(true);
            runTitle.setFontSize(16);
            runTitle.setTextPosition(20);
            // 段落位置
            paraTitle.setAlignment(ParagraphAlignment.CENTER);
            // 生成目录
            for(int i = 0;i<titleList.size();i++){
                XWPFParagraph para = directory.createParagraph();
                XWPFRun run = para.createRun();
                run.setText(convertToChinese(i+1)+"、"+titleList.get(i));
                run.setFontSize(10);
                run.setTextPosition(16);
                para.setAlignment(ParagraphAlignment.LEFT);
            }
            //增加换页符
            XWPFParagraph pageBreak = directory.createParagraph();
            pageBreak.setPageBreak(true);
        } catch (Exception e) {
            log.error("投标文件目录生成失败",e);
        }
        return directory;
    }

//    public static void main(String[] args) throws Exception {
//        List<String> ss = new ArrayList<>();
//        ss.add("第一个标题");
//        ss.add("第二个标题");
//        ss.add("第三个标题");
//        ss.add("第四个标题");
//        NiceXWPFDocument directory = createDirectory(ss);
//        List<NiceXWPFDocument> list = new ArrayList<>();
//        list.add(new NiceXWPFDocument(new FileInputStream("E:\\111.docx")));
//        list.add(new NiceXWPFDocument(new FileInputStream("E:\\222.docx")));
//        list.add(new NiceXWPFDocument(new FileInputStream("E:\\333.docx")));
//        for (int i = 1; i < list.size(); i++) {
//            NiceXWPFDocument subDoc = list.get(i);
//            NiceXWPFDocument subTitle = addTitleForWord("第一个标题", subDoc);
//            directory = directory.merge(subTitle);
//        }
//        FileOutputStream out = new FileOutputStream("E:\\444.docx");
//        directory.write(out);
//        directory.close();
//        XWPFTemplate tmp = XWPFTemplate.compile("E:\\template.docx");
//        tmp.render(
//                new HashMap<String, Object>() {{
//                    put("subTmp", Includes.ofLocal("E:\\444.docx").create());
//                }}
//        );
//        tmp.writeAndClose(new FileOutputStream("E:\\template.docx"));
//    }

    public static void main(String[] args) throws IOException {
        Tables.TableBuilder tabData = Tables
                // 创建一个指定宽度的表格（docx 文档的 80% 宽度）
                .ofPercentWidth("80%")
                // 表格设为水平居中
                .center()
                // 设置表格边框
                .border(BorderStyle.builder()
                        // 边框样式
                        .withType(XWPFTable.XWPFBorderType.SINGLE)
                        .build()
                );
        List<String> ss = new ArrayList<>();
        ss.add("姓名");
        ss.add("国家");
        ss.add("国家2");
        String dd = String.join(",",ss);
        String[] arr = {"0","1","2"};
        tabData.addRow(Rows.of(arr)
                // 文字居中
                .center()
                .create()
        );
        tabData.addRow(Rows.of("派大星", "16", "男").create());
        XWPFTemplate tmp = XWPFTemplate.compile("E:\\template.docx");
        tmp.render(
                new HashMap<String, Object>() {{
                    put("tabsss", tabData.create());
                }}
        );
        tmp.writeAndClose(new FileOutputStream("E:\\template1.docx"));
    }
    /**
     * 数字转中文
     * @param number
     * @return
     */
    public static String convertToChinese(int number) {
        if (number == 0) {
            return CHINESE_NUMBERS[0];
        }
        StringBuilder result = new StringBuilder();
        int unitIndex = 0;
        boolean needZero = false;
        while (number > 0 && unitIndex < CHINESE_UNITS.length) {
            int currentNumber = number % 10;
            if (currentNumber == 0) {
                needZero = true;
            } else {
                if (needZero) {
                    result.insert(0, CHINESE_NUMBERS[0]);
                    needZero = false;
                }
                result.insert(0, CHINESE_NUMBERS[currentNumber] + CHINESE_UNITS[unitIndex]);
            }
            unitIndex++;
            number /= 10;
        }
        return result.toString();
    }
}
