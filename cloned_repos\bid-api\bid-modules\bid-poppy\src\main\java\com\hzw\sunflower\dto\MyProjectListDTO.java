package com.hzw.sunflower.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;


/**
 * <AUTHOR>
 *
 * @Version 1.0
 */
@Data
@ApiModel(description = "项目列表DTO")
public class MyProjectListDTO implements Serializable {

    @ApiModelProperty(value = "主键", position = 1)
    private Long id;

    @ApiModelProperty(value = "项目名称", position = 3)
    private String projectName;

    @ApiModelProperty(value = "项目编号", position = 4)
    private String projectNumber;

    @ApiModelProperty(value = "采购编号", position = 5)
    private String purchaseNumber;

    @ApiModelProperty(value = "采购项目名称", position = 6)
    private String purchaseName;

    @ApiModelProperty(value = "项目状态 项目状态（0：初始化    1：待接受委托 2：进行中 3：办结  4：中止  5：不接受委托）", position = 7)
    private Integer status;

    @ApiModelProperty(value = "权限码0：查看，1：编辑查看", position = 8)
    private Integer rightCode;

    @ApiModelProperty(value = "用户id", position = 8)
    private Long userId;

    @ApiModelProperty(value = "是否进场 （0否1是）", position = 9)
    private Integer isProcess;

    @ApiModelProperty(value = "进场地址", position = 10)
    private String processAddress;

    @ApiModelProperty(value = "进场编号", position = 11)
    private String processCode;

    @ApiModelProperty(value = "包件状态（0-无包，1-有包）", position = 12)
    private Integer packageSegmentStatus;

    @ApiModelProperty(value = "0：标段，1：包", position = 13)
    private Integer bidTypeName;

    @ApiModelProperty(value = "初始项目主键", position = 14)
    private Long baseProjectId;

    @ApiModelProperty(value = "是否重新招标 0.未重新招标 1.已重新招标", position = 15)
    private Integer reTender;
}
