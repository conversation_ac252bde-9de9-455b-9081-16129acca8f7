package com.hzw.sunflower.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.constant.BidOpenConstants;
import com.hzw.sunflower.constant.CommonConstants;
import com.hzw.sunflower.constant.MessageConstants;
import com.hzw.sunflower.constant.constantenum.GenerateRuleEnum;
import com.hzw.sunflower.constant.constantenum.TemplateCodeEnum;
import com.hzw.sunflower.constant.constantnum.OpenStatusEnum;
import com.hzw.sunflower.controller.request.*;
import com.hzw.sunflower.controller.response.ApplyResponseFileVo;
import com.hzw.sunflower.controller.response.CompanyHasBidDocVo;
import com.hzw.sunflower.controller.response.ScheduleDataVo;
import com.hzw.sunflower.dao.BidOpenTableMapper;
import com.hzw.sunflower.dao.ProjectSectionScheduleMapper;
import com.hzw.sunflower.dto.ProjectInfoDTO;
import com.hzw.sunflower.dto.ProjectSectionScheduleDto;
import com.hzw.sunflower.dto.SendSmsDto;
import com.hzw.sunflower.entity.*;
import com.hzw.sunflower.security.utils.SecurityUtils;
import com.hzw.sunflower.service.*;
import com.hzw.sunflower.util.FileUtils;
import com.hzw.sunflower.util.OssUtil;
import com.hzw.sunflower.util.oss.OssFileStorage;
import com.hzw.sunflower.utils.ScheduleFileUtil;
import com.itextpdf.text.Document;
import com.itextpdf.text.DocumentException;
import com.itextpdf.text.pdf.PdfWriter;
import com.itextpdf.tool.xml.XMLWorkerHelper;
import org.apache.commons.collections.MapUtils;
import org.apache.pdfbox.io.MemoryUsageSetting;
import org.apache.pdfbox.multipdf.PDFMergerUtility;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.nio.charset.Charset;
import java.util.*;

@Service
public class ProjectSectionScheduleServiceImpl extends ServiceImpl<ProjectSectionScheduleMapper, ProjectSectionSchedule> implements ProjectSectionScheduleService {

    @Value("${files.template.path}")
    private String templatePath;

    @Value("${files.temporary.path}")
    private String tempFilePath;

    @Value("${oss.active}")
    private String ossType;

    @Autowired
    private OssFileService ossFileService;

    @Autowired
    private ProjectService projectService;

    @Autowired
    private ProjectBidSectionService projectBidSectionService;

    @Autowired
    private ApplyResponseFileService applyResponseFileService;

    @Autowired
    private BidOpenFileService bidOpenFileService;

    @Autowired
    private BidOpenService bidOpenService;

    @Autowired
    private BidDocumentService documentService;

    @Autowired
    private CommonProjectRelevantService commonProjectRelevantService;

    @Autowired
    private CommonSmsService commonSmsService;

    @Autowired
    private CommonOpenService commonOpenService;

    @Autowired
    private BidOpenTableService bidOpenTableService;



    /**
     * 保存开标一览表信息
     * @param bidDocInfo
     * @return
     */
    @Override
    public boolean saveScheduleInfo(List<ProjectBidDocREQ> bidDocInfo) {
//        if (scheduleREQ == null) {
//            return false;
//        }
//        // 删除包关联开标一览表
//        if (!CollectionUtil.isEmpty(scheduleREQ.getDeleteSectionId())) {
//            this.baseMapper.delete(new LambdaQueryWrapper<ProjectSectionSchedule>().in(ProjectSectionSchedule::getSectionId, scheduleREQ.getDeleteSectionId())
//                .eq(ProjectSectionSchedule::getBidRound, scheduleREQ.getBidRound()));
//        }
//        // 删除开标一览表
//        if (!CollectionUtil.isEmpty(scheduleREQ.getDeleteFileId())) {
//            this.baseMapper.delete(new LambdaQueryWrapper<ProjectSectionSchedule>().in(ProjectSectionSchedule::getFileOssId, scheduleREQ.getDeleteFileId())
//                .eq(ProjectSectionSchedule::getBidRound, scheduleREQ.getBidRound()));
//        }
//        // 保存开标一览表信息
//        boolean flag = true;
//        if (!CollectionUtil.isEmpty(scheduleREQ.getScheduleList()) && CommonConstants.YES.equals(scheduleREQ.getOnlineBidOpen())) {
//            List<ProjectSectionSchedule> list = new ArrayList<>();
//            for (ProjectSectionScheduleReq sectionScheduleReq : scheduleREQ.getScheduleList()) {
//                ProjectSectionSchedule schedule = new ProjectSectionSchedule();
//                schedule.setProjectId(sectionScheduleReq.getProjectId());
//                schedule.setSectionId(sectionScheduleReq.getSectionId());
//                schedule.setGenerateRule(sectionScheduleReq.getGenerateRule());
//                schedule.setRowNum(sectionScheduleReq.getRowNum());
//                schedule.setFileOssId(sectionScheduleReq.getFileOssId());
//                schedule.setBidRound(scheduleREQ.getBidRound());
//                if (sectionScheduleReq.getScheduleId() != null) {
//                    schedule.setId(sectionScheduleReq.getScheduleId());
//                }
//                list.add(schedule);
//            }
//            flag = saveOrUpdateBatch(list);
//        }
//        return flag;
        for (ProjectBidDocREQ d :bidDocInfo) {
            List<Long> sectionList = Arrays.asList(d.getSectionIdArray());
            //先删除所有的开标一览表
            this.baseMapper.delete(new LambdaQueryWrapper<ProjectSectionSchedule>().in(ProjectSectionSchedule::getSectionId, sectionList)
                    .eq(ProjectSectionSchedule::getBidRound, d.getBidRound()));
            //判断是否是线上开标
            if(CommonConstants.YES.equals(d.getOnlineBidOpen())){
                List<ProjectSectionSchedule> list = new ArrayList<>();
                for (int i = 0; i < d.getSectionIdArray().length; i++) {
                    Long sectionId = d.getSectionIdArray()[i];
                    ProjectSectionSchedule schedule = new ProjectSectionSchedule();
                    schedule.setProjectId(d.getProjectId());
                    schedule.setSectionId(sectionId);
                    schedule.setGenerateRule(d.getGenerationArray()[i]);
                    schedule.setRowNum(0);
                    schedule.setFileOssId(d.getBidOpeningArray()[i]);
                    schedule.setBidRound(d.getBidRound());
                    list.add(schedule);
                }
                this.saveOrUpdateBatch(list);
            }
        }
        return true;
    }

    /**
     * 根据招标文件id获取开标一览表信息
     * @param docId
     * @return
     */
    @Override
    public List<ProjectSectionScheduleDto> getInfoByDocId(Long docId, Integer bidRound) {
        return this.baseMapper.getInfoByDocId(docId, bidRound);
    }

    /**
     * 生成开标一览表模板
     * @param projectId
     * @param request
     * @param response
     */
    @Override
    public void exportScheduleTemplate(Long projectId, HttpServletRequest request, HttpServletResponse response) {
        String fileName = "开标一览表模板.xlsx";
        String sourcePath = templatePath + "bidOpenSchedule.xlsx";
        String destPath = tempFilePath + System.currentTimeMillis() + ".xlsx";
        Project project = projectService.getProjectById(projectId);
        Map<String, Object> map = new HashMap<>();
        map.put("purchaseName", project.getPurchaseName());
        map.put("purchaseNumber", project.getPurchaseNumber());
        try {
            ScheduleFileUtil.replaceContent(sourcePath, destPath, map);
            FileUtils.outFile(destPath,"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",fileName,response);
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            // 删除临时文件
            File file = new File(destPath);
            if (file.exists()) {
                file.delete();
            }
        }
    }

    /**
     * 根据标段获取开标一览表文件信息
     * @param sectionId
     * @return
     */
    @Override
    public ProjectSectionScheduleDto getInfoBySectionId(Long sectionId) {
        return this.baseMapper.getInfoBySectionId(sectionId);
    }

    /**
     * 开启唱标初始化
     * @param req
     * @return
     */
    @Override
    public ScheduleDataVo startSingInit(StartSingInitReq req) {
        ScheduleDataVo vo = new ScheduleDataVo();
        List<String> fileVos = new ArrayList<>();
        ProjectSectionSchedule projectSectionSchedule = this.getOne(new LambdaQueryWrapper<ProjectSectionSchedule>().eq(ProjectSectionSchedule::getSectionId, req.getSectionId())
                .last("limit 1"));
        vo.setGenerateRule(projectSectionSchedule.getGenerateRule());
        OssFileStorage storage = OssUtil.getOssFileStorage(ossType);
        if (projectSectionSchedule.getGenerateRule().equals(GenerateRuleEnum.MERGE.getType())) {
            // 汇总 解析各供应商上传的响应文件，获得所有内容
            // 生成临时文件
            String fileName = "开标记录表";
            String outPath = tempFilePath + UUID.randomUUID().toString() + fileName + ".pdf";
            try {
                //// 创建导出excel
                //Workbook targetWorkBook = new XSSFWorkbook();
                //Sheet targetSheet = targetWorkBook.createSheet();
                //for (Long companyId : req.getCompanyIdList()) {
                //    ApplyResponseFileVo responseFileVo = applyResponseFileService.getResponseFile(companyId, req.getSectionId());
                //    if (responseFileVo != null && responseFileVo.getDecFileKey() != null) {
                //        byte[] decArr = storage.downloadFile(responseFileVo.getDecFileKey());
                //        Workbook sourceWorkBook1 = new XSSFWorkbook(FileUtils.byte2InputStream(decArr));
                //        Sheet sourceSheet1 = sourceWorkBook1.getSheetAt(0);
                //        MergeUtil.moveSourceSheetIntoTargetSheet(targetWorkBook, sourceSheet1, targetSheet, 0);
                //        targetSheet.createRow(targetSheet.getLastRowNum() + 1);
                //    }
                //}
                //FileOutputStream outputStream = new FileOutputStream(outPath);
                //targetWorkBook.write(outputStream);
                //outputStream.flush();
                //outputStream.close();
                PDFMergerUtility mergePdf = new PDFMergerUtility();
                for (Long companyId : req.getCompanyIdList()) {
                    ApplyResponseFileVo responseFileVo = applyResponseFileService.getResponseFile(companyId, req.getSectionId());
                    if (responseFileVo != null && responseFileVo.getDecFileKey() != null) {
                        int index = responseFileVo.getDecFileName().lastIndexOf(".pdf");
                        if(index!=-1 && (".pdf").equals(responseFileVo.getDecFileName().substring(index))){
                            // 循环添加要合并的pdf
                            byte[] decArr = storage.downloadFile(responseFileVo.getDecFileKey());
                            fileVos.add(tempFilePath + responseFileVo.getDecFileName());
                            BufferedOutputStream bos = null;
                            FileOutputStream fos = null;
                            File file = new File(tempFilePath + responseFileVo.getDecFileName());
                            fos = new FileOutputStream(file);
                            bos = new BufferedOutputStream(fos);
                            bos.write(decArr);
                            mergePdf.addSource(file);
                            fos.close();
                            bos.close();
                        }
                    }
                }
                // 设置合并生成pdf文件名称
                mergePdf.setDestinationFileName(outPath);
                // 合并pdf
                mergePdf.mergeDocuments(MemoryUsageSetting.setupMainMemoryOnly());
                // 上传文件至oss
                Long ossFileId = ossFileService.saveOssFile(fileName + ".pdf", outPath);
                // 入库
                BidOpenFile bidOpenFile = new BidOpenFile();
                bidOpenFile.setProjectId(req.getProjectId());
                bidOpenFile.setSectionId(req.getSectionId());
                bidOpenFile.setFileOssId(ossFileId);
                bidOpenFileService.saveFile(bidOpenFile);
                vo.setFileOssId(ossFileId);
                OssFile ossFile = ossFileService.getOssFileById(ossFileId);
                vo.setOssFileKey(ossFile.getOssFileKey());
                vo.setOssFileName(ossFile.getOssFileName());
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                // 删除文件
                File file = new File(outPath);
                if (file.exists()) {
                    file.delete();
                }
                if(CollectionUtil.isNotEmpty(fileVos)){
                    for (String s: fileVos) {
                        File file1 = new File(s);
                        if (file1.exists()) {
                            file1.delete();
                        }
                    }
                }
            }
        }
/*        else {
            // 拼接
            // 解析项目经理上传的临时文件，获取表头
            OssFile ossFile = ossFileService.getOssFileById(projectSectionSchedule.getFileOssId());
            byte[] arr = storage.downloadFile(ossFile.getOssFileKey());
            if (null != arr && arr.length > 0) {
                List<String> headList = ScheduleFileUtil.getHeadList(FileUtils.byte2InputStream(arr), projectSectionSchedule.getRowNum() - 1);
                headList.add(0, "供应商");
                vo.setTableTitles(headList);
            }
            // 获取各供应商上传的响应文件，遍历解析
            List<Map<Long, List<String>>> tableDataList = new ArrayList();
            for (int i = 0; i< req.getCompanyIdList().size(); i++) {
                Map<Long, List<String>> map = new HashMap<>();
                ApplyResponseFileVo responseFileVo = applyResponseFileService.getResponseFile(req.getCompanyIdList().get(i), req.getSectionId());
                if (responseFileVo != null && responseFileVo.getDecFileKey() != null) {
                    byte[] decArr = storage.downloadFile(responseFileVo.getDecFileKey());
                    List<String> rowData = ScheduleFileUtil.getRowData(FileUtils.byte2InputStream(decArr), vo.getTableTitles(), projectSectionSchedule.getRowNum() - 1);
                    rowData.set(0, req.getCompanyNameList().get(i));
                    map.put(req.getCompanyIdList().get(i), rowData);
                } else {
                    List<String> rowData = new ArrayList<>();
                    for (int j = 0; j < vo.getTableTitles().size(); j++) {
                        if (j == 0) {
                            rowData.add(req.getCompanyNameList().get(i));
                        } else {
                            rowData.add("");
                        }
                    }
                    map.put(req.getCompanyIdList().get(i), rowData);
                }
                tableDataList.add(map);
            }
            vo.setTableDatas(tableDataList);
        }*/
        return vo;
    }

    /**
     * 开启唱标确认
     * @param req
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean startSingConfirm(StartSingConfirmReq req) throws Exception {
        BidOpenFile bidOpenFile = new BidOpenFile();
        bidOpenFile.setSectionId(req.getSectionId());
        bidOpenFile.setProjectId(req.getProjectId());
        // 获取开标记录信息
        LambdaQueryWrapper<BidOpen> lqw = new LambdaQueryWrapper<>();
        lqw .eq(BidOpen::getBidRound, req.getBidRound())
                .eq(BidOpen::getSectionId, req.getSectionId())
                .eq(BidOpen::getOnlineConferenceId, req.getOnlineConferenceId());
        BidOpen openResult = bidOpenService.getOne(lqw);
        // 判断是否为手动上传的开标记录表
        if (req.getFileOssId() != null) {
            bidOpenFile.setFileOssId(req.getFileOssId());
        } else {
          /* // 生成临时文件
            String fileName = "开标记录表";
            String outPath = tempFilePath + UUID.randomUUID().toString() + fileName + ".xlsx";
            try {
                FileOutputStream fileOutputStream = new FileOutputStream(outPath);
                ExcelWriter excelWrite = EasyExcel.write(fileOutputStream).build();
                // 数据加上序号列
                for (int i = 0; i < req.getRowDataList().size(); i++) {
                    List<Object> row = req.getRowDataList().get(i);
                    if (i == 0) {
                        row.add(0, "序号");
                    } else {
                        row.add(0, i);
                    }
                }
                excelWrite = ScheduleFileUtil.makeExcel("开标汇总", fileName, req.getRowDataList(), excelWrite);
                excelWrite.finish();
                // 上传文件至oss
                Long ossFileId = ossFileService.saveOssFile(fileName + ".xlsx", outPath);
                bidOpenFile.setFileOssId(ossFileId);
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                // 删除文件
                File file = new File(outPath);
                if (file.exists()) {
                    file.delete();
                }
            }*/
            String fileName = "开标记录表";
            //转换成html
            String html = excelToHtml(changeTemplate(req.getRowDataList()));
            html = assembleHtml(html,openResult.getProjectId(),openResult.getSectionId());
            //转换成pdf（可以省略，具体看实现）
            Long fileId = ossFileService.editor2PDF(html,UUID.randomUUID()+"开标记录表.pdf","开标记录表");
            bidOpenFile.setFileOssId(fileId);

            // 记录table 方便运维
            BidOpenTable openTable = new BidOpenTable();
            openTable.setOpenId(openResult.getId());
            openTable.setAnalysisHtml(html);
            bidOpenTableService.save(openTable);

        }
        // 保存开标记录表
        bidOpenFileService.saveFile(bidOpenFile);
        // 保存开标记录表至开评标备案文件
        /*BidDocument bidDocument = new BidDocument();
        bidDocument.setBidRound(req.getBidRound());
        bidDocument.setSectionId(req.getSectionId().toString());
        bidDocument.setFileIds(bidOpenFile.getFileOssId().toString());
        bidDocument.setProjectId(req.getProjectId());
        bidDocument.setCatalogueCode(FileDocumentCodeEnum.KBJLB.getCode());
        documentService.save(bidDocument);*/
        // 发送开启唱标提醒短信
        ProjectInfoDTO project = commonProjectRelevantService.getProjectUserInfo(req.getProjectId());
        if (commonOpenService.validAutoByUserId(project.getUserId(), TemplateCodeEnum.KQCBTX.getSecondCode())) {
            SendSmsDto smsDto = new SendSmsDto();
            smsDto.setIsTemplate(true);
            smsDto.setProjectId(req.getProjectId());
            smsDto.setSectionIds(req.getSectionId().toString());
            smsDto.setTemplateCode(TemplateCodeEnum.KQCBTX.getSecondCode());
            // 获取已递交投标文件供应商
            List<CompanyHasBidDocVo> list = commonOpenService.getCompanyHasBidDoc(req.getSectionId(), req.getBidRound());
            for (CompanyHasBidDocVo vo : list) {
                smsDto.setSendPhone(vo.getUserMobile());
                Map<String, Object> map = new HashMap<>();
                map.put("projectName", vo.getPurchaseName());
                map.put("projectNum", vo.getPurchaseNumber());
                if (vo.getPackageSegmentStatus().equals(CommonConstants.YES)) {
                    map.put("packageNumber", "第" + vo.getPackageNumber() + "包");
                } else {
                    map.put("packageNumber", "");
                }
                smsDto.setSmsPms(map);
                commonSmsService.sendSms(smsDto);
            }
        }
        // 修改开标状态为唱标中

        BidOpen updateOpen = new BidOpen();
        updateOpen.setId(openResult.getId());
        updateOpen.setIsCreateFile(OpenStatusEnum.IS_CREATE_FILE.getType());
        // 已经开标状态不更新
        if(openResult.getStatus() <= OpenStatusEnum.SING.getType() ){
            updateOpen.setStatus(OpenStatusEnum.SING.getType());
/*            flag =  bidOpenService.update(new LambdaUpdateWrapper<BidOpen>()
                    .set(BidOpen::getStatus, OpenStatusEnum.SING.getType())
                    .eq(BidOpen::getBidRound, req.getBidRound())
                    .eq(BidOpen::getSectionId, req.getSectionId())
                    .eq(BidOpen::getOnlineConferenceId, req.getOnlineConferenceId()));*/
        }
        return bidOpenService.updateById(updateOpen);
    }

    private static List<List<Map<String, Object>>> changeTemplate(List<List<Map<String, Object>>> tableParse){
        List<List<Map<String, Object>>> template = new ArrayList<>();
        //int head = getHeadNum(tableParse);
        int head = tableParse.size();
        int count = 1;
        //表头
        for(int i=0;i<head;i++){
            List<Map<String,Object>> headList = new ArrayList<>(tableParse.get(i));
            //0-head-1为表头，表头加一列名称为"供应商"
            Map<String,Object> headMap = new HashMap<>();
            if(i==0){
                headMap.put("text","序号");
                headMap.put("type",1);
                //headMap.put("row",head);
                headMap.put("row",1);
                headMap.put("isBold",0);
            }else{
                headMap.put("text",headList.get(0).get("indexCompany"));
                headMap.put("type",1);
                headMap.put("row",headList.get(0).get("row"));
                headMap.put("isBold",0);
                count = count + 1;
            }
            headList.add(0,headMap);
            template.add(headList);
        }
        for(int i=0;i<template.size();i++) {
            List<Map<String, Object>> headList = new ArrayList<>(template.get(i));
            // todo 纵向合并单元格
            if(i!=0 && null !=headList.get(1).get("row") &&  Integer.parseInt(headList.get(1).get("row").toString()) != 1) {
                Integer row = Integer.parseInt(headList.get(1).get("row").toString());
                if (row != 1) {
                    for (int j = row + i; j > i+1; j--) {
                        template.get(j-1).subList(0,2).clear();
                      /*  Map<String, Object> col1 = template.get(j-1).get(0);
                        Map<String, Object> col2 = template.get(j-1).get(1);
                        col1.put("col", 0);
                        col1.put("type", 1);
                        col1.put("row", 0);
                        col1.put("display", 1);
                        col2.put("col", 0);
                        col2.put("row", 0);
                        col2.put("type", 1);
                        col2.put("display", 1);*/
                    }
                }
            }
        }
        return template;
    }


    private static String excelToHtml(List<List<Map<String, Object>>> tableParse){
        StringBuilder sb = new StringBuilder("<table border='1px' border-color='grey' style='width:100%;border-collapse:collapse'>");
        for(int r = 0;r<tableParse.size();r++){
            sb.append("<tr>");
            List<Map<String,Object>> cells = tableParse.get(r);
            for(int c = 0; c< cells.size();c++){
                tdMaker(sb,cells.get(c));
            }
            sb.append("</tr>");
        }
        sb.append("</table>");
        return sb.toString();
    }

    @Override
    public String  assembleHtml(String html, Long projectId, Long sectionId){
        Project project = projectService.getProjectById(projectId);
        ProjectBidSection section = projectBidSectionService.getById(sectionId);
        // 处理开标时间
        Date submitEndTime = section.getSubmitEndTime();
        String endTime = "";
        if(null != submitEndTime){
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(submitEndTime);
            String year = String.format("%tY", submitEndTime);
            Integer month = calendar.get(Calendar.MONTH)+1;
            String day = String.format("%te", submitEndTime);
            String hour = String.format("%tH", submitEndTime);
            String minute = String.format("%tM", submitEndTime);
            endTime = year+"年"+month+"月"+day+"日"+"  "+hour+"："+minute;
        }

        String before = "<div style=\"padding:10px 0;\">";
        String after = "<div style=\"margin-top:10px;\">\n" +
                "\t\t所有投标文件密封完好，开标程序符合规定\n" +
                "\t</div>";

        //  组装项目名称
        before = before + "<div style=\"margin-bottom: 10px;\">项目名称：" +project.getProjectName()+"</div>";
        //  组装项目编号
        before = before + "<div style=\"display: inline-block;width: 49%;margin-bottom: 10px;\">项目编号：" +project.getPurchaseNumber()+"</div>";
        //  组装开标时间

        before = before + "<div style=\"display: inline-block;width: 49%;margin-bottom: 10px;\">开标时间：" +endTime+"</div> </div>";
        //  组装唱标人
//        after = after +"<div style=\"padding:20px 100px;\">\n" +
//                "\t\t<div style=\"display: inline-block;width: 49%;\">唱标人：" + SecurityUtils.getJwtUser().getUserName() +"</div> " +
//                "\t\t<div style=\"display: inline-block;width: 49%;\">记录人："+ SecurityUtils.getJwtUser().getUserName() +"</div> </div>";

        html = before + html + after;
        return  html;
    }



    /**
     * 生成td字符串
     * @param sb
     * @param cell
     */
    private static void tdMaker(StringBuilder sb, Map<String, Object> cell){
        if((int)cell.get("type")==1){
            if(1 == MapUtils.getInteger(cell,"isBold")){
                if(null != cell.get("display") && 1 == MapUtils.getInteger(cell,"display")){
                    sb.append("<td style=' display: none; font-weight: bold'");
                }else{
                    sb.append("<td style='font-weight: bold'");
                }

            }else{
                if(null != cell.get("display") && 1 == MapUtils.getInteger(cell,"display")){
                    sb.append("<td style='display: none;'");
                }else{
                    sb.append("<td");
                }

            }
            if(cell.get("col")!=null){
                sb.append(" colspan='"+cell.get("col")+"'");
            }
            if(cell.get("row")!=null){
                sb.append(" rowspan='"+cell.get("row")+"'");
            }
            sb.append(">");
            if(null != cell.get("text") && !cell.get("text").toString().equals("null")){
                sb.append(cell.get("text"));
            }else{
                sb.append(" ");
            }

            sb.append("</td>");
        }
    }


    @Override
    public Result<Long> queryBidOpeningRecord(StartDecSmsReq req) {
        Long ossFileId = null;
        LambdaQueryWrapper<BidOpenFile> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BidOpenFile::getSectionId,req.getSectionId());
        BidOpenFile one = bidOpenFileService.getOne(queryWrapper);
        if(BeanUtil.isNotEmpty(one) && null != one.getFileOssId()){
            ossFileId = one.getFileOssId();
        } else {
            return Result.failed(BidOpenConstants.MANAGER_CONFIRMATION);
        }
        return Result.ok(ossFileId);
    }


    @Override
    public Result<Integer> findSingType(SingTypeREQ req) {
        ScheduleDataVo vo = new ScheduleDataVo();
        List<String> fileVos = new ArrayList<>();
        LambdaQueryWrapper <ProjectSectionSchedule> lqw =  new LambdaQueryWrapper<>();
        lqw.eq(ProjectSectionSchedule::getSectionId, req.getSectionId());
        lqw.eq(ProjectSectionSchedule::getBidRound,req.getBidRound());
        lqw.last("limit 1");
        ProjectSectionSchedule schedule = this.getOne(lqw);
        if(null != schedule){
        return Result.ok(schedule.getGenerateRule());
        }else {
            return Result.failed();
        }
    }


    // 验证表格生成样式
    public static void main(String[] args) throws IOException, DocumentException {
        Document document = new Document();
        String tempFile = "/Users/<USER>/Desktop/test/" + "111.pdf";
        PdfWriter writer = PdfWriter.getInstance(document, new FileOutputStream(tempFile));
        document.open();
        String content = "<div style=\"padding:10px 0;\">\n" +
                "\t<div style=\"margin-bottom: 10px;\">\n" +
                "\t\t项目名称：12.14线上开评标\n" +
                "\t</div>\n" +
                "\t<div style=\"display: inline-block;width: 49%;margin-bottom: 10px;\">\n" +
                "\t\t项目编号：JSTCC2301112895\n" +
                "\t</div>\n" +
                "\t<div style=\"display: inline-block;width: 49%;margin-bottom: 10px;\">\n" +
                "\t\t开标时间：2023年12月15日 14：05\n" +
                "\t</div>\n" +
                "</div>\n" +
                "<table border='1px' border-color='grey' style='width:100%;border-collapse:collapse'>\n" +
                "\t<tr>\n" +
                "\t\t<td rowspan='1' rowspan='1'>\n" +
                "\t\t\t序号\n" +
                "\t\t</td>\n" +
                "\t\t<td style='font-weight: bold' rowspan='1' rowspan='1'>\n" +
                "\t\t\t供应商\n" +
                "\t\t</td>\n" +
                "\t\t<td style='font-weight: bold'>\n" +
                "\t\t\t序号\n" +
                "\t\t</td>\n" +
                "\t\t<td style='font-weight: bold'>\n" +
                "\t\t\t报价组成\n" +
                "\t\t</td>\n" +
                "\t\t<td style='font-weight: bold'>\n" +
                "\t\t\t内容\n" +
                "\t\t</td>\n" +
                "\t\t<td style='font-weight: bold'>\n" +
                "\t\t\t价格（含税 ，元）\n" +
                "\t\t</td>\n" +
                "\t\t<td style='font-weight: bold'>\n" +
                "\t\t\t折扣（%）\n" +
                "\t\t</td>\n" +
                "\t\t<td style='font-weight: bold'>\n" +
                "\t\t\t报价（含税 ，元）\n" +
                "\t\t</td>\n" +
                "\t</tr>\n" +
                "\t<tr>\n" +
                "\t\t<td rowspan='4'>\n" +
                "\t\t\t1\n" +
                "\t\t</td>\n" +
                "\t\t<td rowspan='4'>\n" +
                "\t\t\t江苏天辰石油化工有限责任公司\n" +
                "\t\t</td>\n" +
                "\t\t<td>\n" +
                "\t\t\t1\n" +
                "\t\t</td>\n" +
                "\t\t<td>\n" +
                "\t\t\t产品费用\n" +
                "\t\t</td>\n" +
                "\t\t<td>\n" +
                "\t\t\t9\n" +
                "\t\t</td>\n" +
                "\t\t<td>\n" +
                "\t\t\t100000\n" +
                "\t\t</td>\n" +
                "\t\t<td>\n" +
                "\t\t\t30%\n" +
                "\t\t</td>\n" +
                "\t\t<td>\n" +
                "\t\t\t70000\n" +
                "\t\t</td>\n" +
                "\t</tr>\n" +
                "\t<tr>\n" +
                "\t\t<td colspan='0' rowspan='0'>\n" +
                "\t\t\t1\n" +
                "\t\t</td>\n" +
                "\t\t<td  colspan='0'  rowspan='0'>\n" +
                "\t\t\t江苏天辰石油化工有限责任公司\n" +
                "\t\t</td>\n" +
                "\t\t<td>\n" +
                "\t\t\t2\n" +
                "\t\t</td>\n" +
                "\t\t<td>\n" +
                "\t\t\t实施费用\n" +
                "\t\t</td>\n" +
                "\t\t<td>\n" +
                "\t\t\t8\n" +
                "\t\t</td>\n" +
                "\t\t<td>\n" +
                "\t\t\t200000\n" +
                "\t\t</td>\n" +
                "\t\t<td>\n" +
                "\t\t\t10%\n" +
                "\t\t</td>\n" +
                "\t\t<td>\n" +
                "\t\t\t180000\n" +
                "\t\t</td>\n" +
                "\t</tr>\n" +
                "\t<tr>\n" +
                "\t\t<td colspan='0'  rowspan='0'>\n" +
                "\t\t\t1\n" +
                "\t\t</td>\n" +
                "\t\t<td  colspan='0'  rowspan='0'>\n" +
                "\t\t\t江苏天辰石油化工有限责任公司\n" +
                "\t\t</td>\n" +
                "\t\t<td>\n" +
                "\t\t\t3\n" +
                "\t\t</td>\n" +
                "\t\t<td>\n" +
                "\t\t\t售后服务费用（3年跟踪维护期）\n" +
                "\t\t</td>\n" +
                "\t\t<td>\n" +
                "\t\t\t7\n" +
                "\t\t</td>\n" +
                "\t\t<td>\n" +
                "\t\t\t30000\n" +
                "\t\t</td>\n" +
                "\t\t<td>\n" +
                "\t\t\t10%\n" +
                "\t\t</td>\n" +
                "\t\t<td>\n" +
                "\t\t\t27000\n" +
                "\t\t</td>\n" +
                "\t</tr>\n" +
                "\t<tr>\n" +
                "\t\t<td  colspan='0'  rowspan='0'>\n" +
                "\t\t\t1\n" +
                "\t\t</td>\n" +
                "\t\t<td  colspan='0' rowspan='0'>\n" +
                "\t\t\t江苏天辰石油化工有限责任公司\n" +
                "\t\t</td>\n" +
                "\t\t<td>\n" +
                "\t\t\t项目总报价：实施费用＋产品费用＋售后服务费用（3年跟踪维护期）\n" +
                "\t\t</td>\n" +
                "\t\t<td>\n" +
                "\t\t\t项目总报价：实施费用＋产品费用＋售后服务费用（3年跟踪维护期）\n" +
                "\t\t</td>\n" +
                "\t\t<td>\n" +
                "\t\t\t项目总报价：实施费用＋产品费用＋售后服务费用（3年跟踪维护期）\n" +
                "\t\t</td>\n" +
                "\t\t<td>\n" +
                "\t\t\t277000\n" +
                "\t\t</td>\n" +
                "\t\t<td>\n" +
                "\t\t\t277000\n" +
                "\t\t</td>\n" +
                "\t\t<td>\n" +
                "\t\t\t277000\n" +
                "\t\t</td>\n" +
                "\t</tr>\n" +
                "</table>\n" +
                "<div style=\"margin-top:10px;\">\n" +
                "\t所有投标文件封完好，开标程序符合规定\n" +
                "</div>\n" +
                "<div style=\"padding:20px 100px;\">\n" +
                "\t<div style=\"display: inline-block;width: 49%;\">\n" +
                "\t\t唱标人：陈诚\n" +
                "\t</div>\n" +
                "\t<div style=\"display: inline-block;width: 49%;\">\n" +
                "\t\t记录人：陈诚\n" +
                "\t</div>\n" +
                "</div>\n";
         content = "<!DOCTYPE html PUBLIC \\\"-//W3C//DTD XHTML 1.0 Transitional//EN\\\" \\\"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd\\\">\\n"+
                 "<html><body style=\"font-family: 宋体, SimHei;\"><p style=\"text-align: center;font-family: 宋体, SimHei; font-weight:bold; font-size: 24px;\">" + 1111 + "</p>" + content + "</body></html>";
        byte[] b = content.getBytes("utf-8");
        ByteArrayInputStream bais = new ByteArrayInputStream(b);
        XMLWorkerHelper.getInstance().parseXHtml(writer, document, bais, Charset.forName("UTF-8"));
        document.close();
        File ossFile = new File(tempFile);

    }


}
