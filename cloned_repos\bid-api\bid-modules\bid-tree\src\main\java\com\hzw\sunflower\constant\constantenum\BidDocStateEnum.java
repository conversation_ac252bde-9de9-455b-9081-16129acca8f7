package com.hzw.sunflower.constant.constantenum;

/**
 * <AUTHOR>
 * @date ：Created in 2021/6/29 17:01
 * @description：招标文件状态
 * @version: 1.0
 */
public enum BidDocStateEnum {
    FORMAL(1, "正式"),

    INFORMAL(2, "暂存");

    private Integer value;

    private String name;

    BidDocStateEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    public Integer getValue() {
        return value;
    }

    public String getName() {
        return name;
    }
}
