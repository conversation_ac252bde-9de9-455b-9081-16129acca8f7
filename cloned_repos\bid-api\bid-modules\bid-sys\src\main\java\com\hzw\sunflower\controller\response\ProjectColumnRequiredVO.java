package com.hzw.sunflower.controller.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(description = "项目字段必填数据")
@Data
public class ProjectColumnRequiredVO {

    @ApiModelProperty(value = "模块")
    private String moduleName;

    @ApiModelProperty(value = "模块code")
    private String moduleCode;

    @ApiModelProperty(value = "字段名称")
    private String columnName;

    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("项目字段必填配置表id")
    private Integer columnRequiredId;

    @ApiModelProperty("是否必填 1.是 2.否")
    private Integer isRequired;

    @ApiModelProperty("生成方式 1.手动填写 2.自动生成")
    private Integer genType;

    @ApiModelProperty("生成规则多选以,隔开 1.系统简称 2.年份 3.部门编码 4.省外（若数据字典勾选该字段可显示）5.自增数字")
    private String genRules;

    @ApiModelProperty("年份位数 值固定为2或4")
    private Integer yearLen;

    @ApiModelProperty("自增数字位数")
    private Integer increNumberLen;

    @ApiModelProperty("业务流程展示 1.全流程线上 2.线上+线下 3.全流程线下+归档")
    private String showProcess;

    @ApiModelProperty("是否置灰，1是 2否")
    private Integer disabled;

    @ApiModelProperty("备注")
    private String code;

}
