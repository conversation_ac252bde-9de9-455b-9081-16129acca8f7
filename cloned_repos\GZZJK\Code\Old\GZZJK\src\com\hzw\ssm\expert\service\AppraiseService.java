package com.hzw.ssm.expert.service;

import java.io.File;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.apache.struts2.ServletActionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.hzw.ssm.expert.dao.AppraiseMapper;
import com.hzw.ssm.expert.dao.ExpertInfoMapper;
import com.hzw.ssm.expert.entity.Appraise;
import com.hzw.ssm.expert.entity.AppraiseIllegal;
import com.hzw.ssm.expert.entity.AppraiseInfo;
import com.hzw.ssm.expert.entity.AppraiseInfo2;
import com.hzw.ssm.expert.entity.AppraiseRemark;
import com.hzw.ssm.expert.entity.ExpertInfoEntity;
import com.hzw.ssm.fw.base.BaseService;
import com.hzw.ssm.fw.util.CommUtil;
import com.hzw.ssm.fw.util.SysConstants;
import com.hzw.ssm.sys.project.entity.ProjectEntity;
import com.hzw.ssm.sys.project.entity.ResultEntity;
import com.hzw.ssm.sys.project.service.ProjectService;

/**
 * 专家评价
 * 
 * <AUTHOR>
 * 
 */
@Service
public class AppraiseService extends BaseService {
	@Autowired
	private AppraiseMapper appraiseMapper;
	
	@Autowired
	private ExpertInfoMapper expertInfoMapper;
	
	@Autowired
	private ExpertInfoService expertInfoService;
	
	@Autowired
	private ProjectService projectService;
	//@Value("${FilePath}")
	//private String filePath;
	
	@Value("${PdfFileName}")
	private String pdfFileName;

	/**
	 * 查询专家反馈项目列表
	 * 
	 * @param entity
	 * @return
	 */
	public List<ProjectEntity> queryPageProjectInfo(ProjectEntity entity) {
		return appraiseMapper.queryPageProjectInfo(entity);
	}
	
	/**
	 * 查询专家反馈项目列表
	 * 
	 * @param entity
	 * @return
	 */
	public List<ProjectEntity> queryPageProjectInfoByJD(ProjectEntity entity) {
		return appraiseMapper.queryPageProjectInfoByJD(entity);
	}
	
	/**
	 * 查询专家反馈项目列表（违规情况录入）
	 * 
	 * @param entity
	 * @return
	 */
	public List<ProjectEntity> queryPageAppraiseIllegalInfo(ProjectEntity entity) {
		return appraiseMapper.queryPageAppraiseIllegalInfo(entity);
	}


	/**
	 * 查询专家反馈专家列表
	 * 
	 * @param entity
	 * @return
	 */
	public List<ExpertInfoEntity> queryExpertAppriaiseList(ExpertInfoEntity entity) {
		return appraiseMapper.queryExpertAppriaiseList(entity);
	}
	
	/**
	 * 查询违规专家列表
	 * 
	 * @param entity
	 * @return
	 */
	public List<ExpertInfoEntity> queryIllegalExpertsList(ExpertInfoEntity entity) {
		return appraiseMapper.queryIllegalExpertsList(entity);
	}
	
	/**
	 * 查询专家列表
	 * 
	 * @param entity
	 * @return
	 */
	public List<ExpertInfoEntity> queryIllegalExpertsList2(ExpertInfoEntity entity) {
		return appraiseMapper.queryIllegalExpertsList2(entity);
	}

	/**
	 * 根据id查询评价信息表(基础信息)
	 * 
	 * @return
	 */
	public ExpertInfoEntity queryBaseAppraise(String extractResultId) {
		return appraiseMapper.queryBaseAppraise(extractResultId);
	}

	/**
	 * 根据id查询专家评价信息表(评分项)
	 * 
	 * @param parentId
	 * @return
	 */
	public List<AppraiseInfo> queryAppraiseInfoById(String parentId) {

		List<AppraiseInfo> list = appraiseMapper.queryAppraiseInfoById(parentId);
		for (int i = 0; i < list.size(); i++) {
			AppraiseInfo aInfo = list.get(i);
			aInfo.setChildList(queryAppraiseInfoById(aInfo.getAppraise_info_id()));
		}

		return list;
	}
	
	
	/**
	 * 根据id查询专家评价信息表(评分项)
	 * 
	 * @param parentId
	 * @return
	 */
	public List<AppraiseInfo2> queryAppraiseInfoById2(String parentId) {

		List<AppraiseInfo2> list = appraiseMapper.queryAppraiseInfoById2(parentId);
		for (int i = 0; i < list.size(); i++) {
			AppraiseInfo2 aInfo = list.get(i);
			aInfo.setChildList(queryAppraiseInfoById2(aInfo.getAppraise_info_id()));
		}

		return list;
	}

	/**
	 * 专家评价
	 * 
	 * @param list
	 */
	@Transactional
	public void saveAppraise(List<Appraise> appraiseList, AppraiseRemark appraiseRemark, String exRId,ExpertInfoEntity expertInfoEntity,File illegal_file_path,String illegal_file_name,ResultEntity re) {
		String rootPath=ServletActionContext.getServletContext().getRealPath("");
		
		String savePath = rootPath+ "/"+SysConstants.EXPERT_FILE_PATH_ROOT+expertInfoEntity.getUser_id();
		File file1=new File(savePath);
		if(!file1.exists()){
			file1.mkdirs();
		}
		if(illegal_file_path!=null){
			String newName=CommUtil.getKey()+illegal_file_name.substring(illegal_file_name.lastIndexOf("."));
			String filePath=savePath+"/"+newName;
			
			File file=new File(filePath);
			boolean result=expertInfoService.copyFile(illegal_file_path, file);
			if(result){
				if(re == null){
					re = new ResultEntity();
				}
				re.setIllegal_file_path(SysConstants.EXPERT_FILE_PATH_ROOT+expertInfoEntity.getUser_id()+"/"+newName);
				re.setIllegal_file_name(illegal_file_name);
				re.setId(exRId);
				projectService.updateExtractExIllegal(re);
			}
		}
		
		// 向评价表插入数据
		appraiseMapper.saveAppraise(appraiseList);

		if (!("").equals(appraiseRemark.getRemark().trim())) {
			// 向评价备注表插入数据
			appraiseMapper.saveAppraiseRemark(appraiseRemark);
		}

		// 修改专家抽取结果表是否评价状态
		appraiseMapper.modifyIsAppraise(expertInfoEntity);
	}

	/**
	 * 查看专家已评价信息
	 * 
	 * @param extractResultId
	 * @return
	 */
	public List<Appraise> queryAppraiseDetail(String extractResultId,String appraiseType) {
		if(appraiseType.equals("0")){
			return appraiseMapper.queryAppraiseDetail(extractResultId);
		}else{
			return appraiseMapper.queryAppraiseRewardDetail(extractResultId);
		}
		
	}

	/**
	 * 查询评价备注信息
	 * 
	 * @param extractResultId
	 * @return
	 */
	public AppraiseRemark queryAppraiseRemark(String extractResultId) {
		return appraiseMapper.queryAppraiseRemark(extractResultId);
	}
	
	/**
	 * 查看专家评价平均分
	 * @param appraise
	 * @return
	 */
	public Appraise getExpertAvgScore(Appraise appraise){
		return appraiseMapper.getExpertAvgScore(appraise);
	}
	/**
	 * 查看专家评价分
	 * @param appraise
	 * @return
	 */
	public Appraise getExpertScore(String  expertId){
		return appraiseMapper.getExpertScore(expertId);
	}
	/**
	 * 修改专家评价分
	 * @param appraise
	 * @return
	 */
	@Transactional
	public void modifyExpertScore(Appraise appraise){
		appraiseMapper.modifyExpertScore(appraise);
	}
	
	/**
	 * 新增专家评价记录
	 * @param appraise
	 * @return
	 */
	@Transactional
	public void addExpertScore(Appraise appraise){
		appraiseMapper.addExpertScore(appraise);
	}
	
	/**
	 * 修改专家停职月数
	 * @param appraise
	 * @return
	 */
	public void modifyExpertSuspension(String id, Integer suspension){
		appraiseMapper.modifyExpertSuspension(id, suspension);
		appraiseMapper.modifyExpertSuspensionBAK(id, suspension);
	}
	/**
	 * 修改专家停职总月数
	 * @param appraise
	 * @return
	 */
	public void modifyExpertTotalSuspension(String id,Integer totalSuspension){
		appraiseMapper.modifyExpertTotalSuspension(id, totalSuspension);
		appraiseMapper.modifyExpertTotalSuspensionBAK(id, totalSuspension);
	}
	
	/**
	 * 专家评价动作
	 * 
	 * @param list
	 */
	@Transactional
	public void insertAppraise(List<Appraise> appraiseList,String extractResultId,String appraiseType,ExpertInfoEntity expertInfoEntity) {
		
		// 向评价表插入数据
		appraiseMapper.saveAppraise(appraiseList);
		//查询当前是否重复提交
//		String isApprove = appraiseMapper.selectIsDuplicate(extractResultId);
		/*if (StringUtils.isNotBlank(isApprove)){
			expertInfoEntity.setIsDuplicateSub("1");//重复提交
		}else{
			expertInfoEntity.setIsDuplicateSub("0");
		}*/
		//修改专家抽取结果表是否评价状态
		if(appraiseType.equals("0")){
			//查询该专家是否扣分
			String score=appraiseMapper.selectDeducteScore(extractResultId);
			if (StringUtils.isNotBlank(score) && Integer.parseInt(score)==0){
				expertInfoEntity.setIsApprove(3);//无需审核
			}else {
				expertInfoEntity.setIsApprove(0);//未审批
				expertInfoEntity.setRejectionReason("");//拒绝原因
			}
			expertInfoEntity.setExtractResultId(extractResultId);
			//更新专家抽去结果表信息
			appraiseMapper.modifyIsAppraise(expertInfoEntity);
			//更新专家分数
			expertInfoMapper.modifyExpertAssessment(expertInfoEntity);
		}else{
			appraiseMapper.modifyIsAppraiseReward(extractResultId);
			expertInfoMapper.modifyExpertScore(expertInfoEntity);
		}
	}
	
	/**
	 * 查询专家违规操作
	 * @param list
	 * @return 
	 */
	@Transactional
	public AppraiseIllegal queryAppraiseIllegal(String extractResultId) {
		return appraiseMapper.queryAppraiseIllegal(extractResultId);
	}
	
	/**
	 * 专家违规操作录入
	 * @param list
	 */
	@Transactional
	public void insertAppraiseILLEGAL(AppraiseIllegal illegal) {
		illegal.setAppraise_illegal_id(CommUtil.getKey());
		
		//录入操作
		appraiseMapper.saveAppraiseIllegal(illegal);
	}
	
	/**
	 * 专家违规操作录入(修改)
	 * @param list
	 */
	@Transactional
	public void updateAppraiseILLEGAL(AppraiseIllegal illegal) {
		//录入操作
		appraiseMapper.saveAppraiseIllegal(illegal);
	}
	
	/**
	 * 查询专家反馈专家列表by机电中心抽取人
	 * 
	 * @param entity
	 * @return
	 */
	public List<ExpertInfoEntity> queryExpertAppriaiseListByJD(ExpertInfoEntity entity) {
		return appraiseMapper.queryExpertAppriaiseListByJD(entity);
	}

	/**
	 * 修改专家违规记录
	 * @param illegal
	 */
	public void updateAppraiseIllegal(AppraiseIllegal illegal) {
		appraiseMapper.updateAppraiseIllegal(illegal);
	}
		
}
