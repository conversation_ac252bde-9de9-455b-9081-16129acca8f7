package com.hzw.ssm.expert.dao;

import com.hzw.ssm.expert.entity.Policies;

import java.util.List;
import java.util.Map;

public interface PoliciesMapper {


    //查询所有政策法规目录
    List<Policies> findPoliciesAll(Policies policies);

    //根据条件参数查询政策法规目录列表
    List<Policies> queryPagePoliciesByCondition(Policies policies);

    List<Policies> queryPoliciesByCondition(Policies policies);

    //根据主键查询政策法规目录信息
    Policies findPoliciesByIds(String id);

    //根据主键查询政策法规目录信息
    Policies findPoliciesById(String id);


    Integer addPolicies(Policies policies);

    Integer addPoliciess(List<Policies> lsit);


    Integer updatePolicies(Policies policies);

    Integer updatePoliciess(List<Policies> lsit);


    Integer deletePoliciesById(String id);

    Integer deletePoliciesByIds(List<String> list);


    Integer findSortMax();
    Integer findSortMin();

    Policies queryPoliciesByConditionOne(Policies policies);

    List<Map<String, Object>> queryAllEffective(Policies policies);
}
