package com.hzw.sunflower.controller;

import com.hzw.sunflower.common.BaseController;
import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.controller.request.BondPrintReq;
import com.hzw.sunflower.service.BondBankReceiptService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @datetime 2022/10/10 9:40
 * @description: 保证金回单
 * @version: 1.0
 */
@Api(tags = "保证金回单")
@RestController
@RequestMapping("/bondReceipt")
public class BondReceiptController extends BaseController {

    @Autowired
    BondBankReceiptService bondBankReceiptService;

    @ApiOperation(value = "打印回单")
    @ApiImplicitParam(name = "req", value = "回单请求条件", required = true, dataType = "BondPrintReq", paramType = "body")
    @PostMapping(value = "/printReceipt")
    public Result<List<String>> printReceipt(@RequestBody BondPrintReq req) {
        return Result.ok(bondBankReceiptService.printReceipt(req));
    }
}
