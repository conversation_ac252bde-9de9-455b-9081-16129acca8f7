package com.hzw.sunflower.controller;

import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.common.annotation.RepeatSubmit;
import com.hzw.sunflower.constant.constantenum.LicenseFunctionEnum;
import com.hzw.sunflower.constant.constantenum.PurchaseNumRuleEnum;
import com.hzw.sunflower.constant.constantenum.ShowProcessEnum;
import com.hzw.sunflower.controller.request.NumberExampleReq;
import com.hzw.sunflower.controller.request.ProjectDetailsReq;
import com.hzw.sunflower.controller.response.ProjectDetailsVo;
import com.hzw.sunflower.service.ColumnConfigService;
import com.hzw.sunflower.service.LicenseService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Api(tags = "项目字段详细信息配置服务")
@RestController
@RequestMapping("/columnConfig")
public class ColumnConfigController {

    @Autowired
    private ColumnConfigService columnConfigService;

    @ApiOperation(value = "保存项目字段详细信息配置")
    @ApiImplicitParam(name = "req", value = "参数", required = true, dataType = "ProjectDetailsReq", paramType = "body")
    @PostMapping("/insertColumnConfig")
    @RepeatSubmit
    public Object insertColumnConfig(@RequestBody ProjectDetailsReq req) {
        Boolean bool  = columnConfigService.insertColumnConfig(req);
        return Result.okOrFailed(bool);
    }

    @ApiOperation(value = "查看项目详细信息设置详情")
    @PostMapping("/selectColumnConfig")
    public Result<ProjectDetailsVo> selectColumnConfig() {
        ProjectDetailsVo projectDetailsVo  = columnConfigService.selectColumnConfig();
        return Result.ok(projectDetailsVo);
    }

    @ApiOperation(value = "获取编号生成规则枚举")
    @PostMapping("/getPurchaseNumRuleEnum")
    public Object getPurchaseNumRuleEnum() {
//        //省外字段是否展示
//        boolean flag = false;
//        ColumnRequiredReq req = new ColumnRequiredReq();
//        req.setModuleName("新建项目");
//        req.setColumnName("省外项目");
//        ColumnRequired columnName  = columnRequiredService.queryByColumnNameModuleName(req);
//        if (columnName != null && columnName.getIsRequiredAgent().equals(CommonConstants.YES)) {
//            flag = true;
//        }
        List list = PurchaseNumRuleEnum.toList();
        return Result.ok(list);
    }

    @ApiOperation(value = "生成示例编号")
    @PostMapping("/generateNumberExample")
    @ApiImplicitParam(name = "req", value = "参数", required = true, dataType = "NumberExampleReq", paramType = "body")
    public Object generateNumberExample(@RequestBody NumberExampleReq req) {
        String example = columnConfigService.generateNumberExample(req);
        return Result.ok(example);
    }


    @ApiOperation(value = "获取业务流程类型枚举")
    @PostMapping("/getShowProcessEnum")
    public Object getShowProcessEnum() {
        boolean flag = false;
        //是否展示全流程线上
        Integer licenseFunction = columnConfigService.getLicenseFunction();
        //只有旗舰版包含电子开评标功能，展示全流程线上
        if (licenseFunction != null && licenseFunction.equals(LicenseFunctionEnum.FUNCTION_ULTIMATE.getType())) {
            flag = true;
        }
        List list = ShowProcessEnum.toList(flag);
        return Result.ok(list);
    }

}
