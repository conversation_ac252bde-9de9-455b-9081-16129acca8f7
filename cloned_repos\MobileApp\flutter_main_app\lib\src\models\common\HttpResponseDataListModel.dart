///***
/// * HTTP 请求返回数据列表时的包裹类型
/// */

import 'package:flutter/material.dart';
import 'package:json_annotation/json_annotation.dart';

import 'package:flutter_main_app/src/utils/GenericConverter.dart';

part 'HttpResponseDataListModel.g.dart';

@JsonSerializable(explicitToJson: true)
class HttpResponseDataListModel<T> {

  const HttpResponseDataListModel({
    @required this.totalNumber,
    @required this.totalPage,
    @required this.pageSize,
    @required this.currentPage,
    @required this.data,
  });

  final int totalNumber;

  final int totalPage;

  final int pageSize;

  final int currentPage;

  @GenericConverter()
  final List<T> data;

  factory HttpResponseDataListModel.fromJson(Map<String, dynamic> json) => _$HttpResponseDataListModelFromJson<T>(json);

  Map<String, dynamic> toJson() => _$HttpResponseDataListModelToJson(this);

}
