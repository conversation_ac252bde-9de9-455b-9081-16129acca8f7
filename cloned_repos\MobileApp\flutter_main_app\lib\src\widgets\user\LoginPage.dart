import "package:flutter/material.dart";

import 'package:flutter_main_app/src/constants/theme.dart';

class LoginPageWidget extends StatefulWidget {
  const LoginPageWidget({Key key}) : super(key: key);

  /// * 路由名称
  static const String _routeName = 'LoginPage';
  static String get routeName => _routeName;

  @override
  _LoginPageWidgetState createState() => _LoginPageWidgetState();
}

class _LoginPageWidgetState extends State<LoginPageWidget> {
  // String _userName;
  // String _password;
  @override
  Widget build(BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;

    return Scaffold(
        appBar: AppBar(
          // Here we take the value from the MyHomePage object that was created by
          // the App.build method, and use it to set our appbar title.
          title: Text(
            '用户登录',
            style: TextStyle(color: themeTitleColor),
          ),
          backgroundColor: Color.fromARGB(255, 255, 255, 255),
          leading: Container(
            child: <PERSON><PERSON><PERSON><PERSON>on(
              icon: const Icon(Icons.arrow_back_ios),
              color: themeTitleColor,
              focusColor: themeTitleColor,
              onPressed: () {
                Navigator.pop(context);
              },
            ),
          ),
        ),
        body: Stack(
          children: <Widget>[
            Column(
              children: <Widget>[
                Container(
                    width: screenWidth,
                    height: 200,
                    // color: Colors.green,
                    child: Center(child: Image.asset('assets/images/logo.png'))),
                Column(
                  children: <Widget>[
                    // 用户名
                    Padding(
                        padding: EdgeInsets.fromLTRB(20, 30, 25, 0),
                        child: Container(
                            decoration: BoxDecoration(
                                border: Border(
                                  bottom: themeBorderSide,
                                )),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceAround,
                              children: <Widget>[
                                Padding(
                                  padding: EdgeInsets.fromLTRB(0, 0, 34, 0),
                                  child: Text(
                                    '+86',
                                    style: TextStyle(
                                        color: themeTitleColor, fontSize: 14),
                                  ),
                                ),
                                Flexible(
                                  child: TextField(
                                    decoration: InputDecoration.collapsed(
                                        hintText: '请输入您的手机号'),
                                  ),
                                ),
                              ],
                            ))),
                    // 密码
                    Padding(
                        padding: EdgeInsets.fromLTRB(20, 30, 25, 0),
                        child: Container(
                            decoration: BoxDecoration(
                                border: Border(
                                  bottom: themeBorderSide,
                                )),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceAround,
                              children: <Widget>[
                                Padding(
                                  padding: EdgeInsets.fromLTRB(0, 0, 19, 0),
                                  child: Text(
                                    '密    码',
                                    style: TextStyle(
                                        color: themeTitleColor, fontSize: 14),
                                  ),
                                ),
                                Flexible(
                                  child: TextField(
                                    decoration: InputDecoration.collapsed(
                                        hintText: '请输入密码'),
                                  ),
                                ),
                              ],
                            ))),
                    // 登录按钮
                    Padding(
                        padding: EdgeInsets.fromLTRB(25, 45, 25, 0),
                        child: SizedBox(
                            width: 325,
                            height: 40,
                            child: RaisedButton(
                              child: Text(
                                "登录",
                                style: TextStyle(color: Colors.white),
                              ),
                              onPressed: null,
                              disabledColor: Color(0xFFD5D5D5),
                              textColor: Colors.white,
                            ))),
                    // 注册或者忘记密码
                    Padding(
                      padding: EdgeInsets.fromLTRB(25, 0, 25, 0),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: <Widget>[
                          FlatButton(
                              child: Text("新人注册",
                                style: themeTipsTextStyle,
                              ),
                              onPressed: () =>
                                  Navigator.pushNamed(context, 'RegisterPage')),
                          FlatButton(
                            child: Text("忘记密码?",
                              style: themeTipsTextStyle,
                            ),
                            onPressed: () {
                              Navigator.pushNamed(context, 'ResetPassword');
                            },
                          )
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
            Align(
                alignment: Alignment.bottomCenter,
                child: Padding(
                  padding: EdgeInsets.fromLTRB(0, 0, 0, 30),
                  child: Container(
                    width: screenWidth,
                    height: 150,
                    child: Column(
                      children: <Widget>[
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: <Widget>[
                            Container(
                              width: 115,
                              height: 2,
                              color: themeBorderColor,
                            ),
                            Padding(
                              padding: EdgeInsets.fromLTRB(5, 0, 5, 0),
                              child: Text(
                                '第三方账号登录',
                                style: TextStyle(
                                    color: themeHintTextColor, fontSize: 12),
                              ),
                            ),
                            Container(
                              width: 115,
                              height: themeBorderWidth,
                              color: themeBorderColor,
                            ),
                          ],
                        ),
                        Padding(
                          padding: EdgeInsets.fromLTRB(0, 27, 0, 0),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: <Widget>[
                              Padding(
                                padding: EdgeInsets.fromLTRB(0, 0, 26, 0),
                                child: Column(
                                  children: <Widget>[
                                    GestureDetector(
                                        onTap: null,
                                        child: Container(
                                            width: 60,
                                            height: 60,
                                            child: Image.asset(
                                                'assets/images/wechat.png'))),
                                    Text(
                                      '微信',
                                      style: TextStyle(
                                          color: themeHintTextColor,
                                          fontSize: 12),
                                    )
                                  ],
                                ),
                              ),
                              Padding(
                                padding: EdgeInsets.fromLTRB(0, 0, 26, 0),
                                child: Column(
                                  children: <Widget>[
                                    GestureDetector(
                                        onTap: null,
                                        child: Container(
                                            width: 60,
                                            height: 60,
                                            child: Image.asset(
                                                'assets/images/weibo.png'))),
                                    Text(
                                      '微博',
                                      style: TextStyle(
                                          color: themeHintTextColor,
                                          fontSize: 12),
                                    )
                                  ],
                                ),
                              ),
                              Column(
                                children: <Widget>[
                                  GestureDetector(
                                      onTap: null,
                                      child: Container(
                                          width: 60,
                                          height: 60,
                                          child: Image.asset('assets/images/qq.png'))),
                                  Text(
                                    'QQ',
                                    style: TextStyle(
                                        color: themeHintTextColor, fontSize: 12),
                                  )
                                ],
                              )
                            ],
                          ),
                        )
                      ],
                    ),
                  ),
                ))
          ],
        ));
  }
}
