package com.hzw.sunflower.constant.constantenum;


/**
 * 状态结果枚举
 *
 * <AUTHOR>
 * @date ：Created in 2021/5/12 10:02
 * @modified By：`
 * @version: 1.0
 */
public enum StatusResultEnum {

    USER_NOT_EXIST ("CZ_001", "用户不存在"),
    USER_IS_DISABLE("JY_001","用户被禁用"),
    COMPANY_IS_DISABLE("JY_002","企业身份已禁用"),
    NOT_BINDING_COMPANY("WS_001","未绑定企业"),
    WAIT_PERFECT_INFO("WS_002","待完善信息"),
    EXAMINE_NOT_PASS("WS_003","审核不通过"),
    WAIT_EXAMINE("SH_001","待审核"),
    JITC_WAIT_EXAMINE("JITCSH_001","JITC待审核"),
    JITC_PEOPLE_PERFECT_INFO("JITCGRWS_002","JITC个人信息待完善"),
    JITC_WAIT_PERFECT_INFO("JITCWS_002","JITC企业信息待完善"),
    JITC_EXAMINE_NOT_PASS("JITCWS_003","JITC审核不通过"),
    USER_ADMIN_TRANSFER("ZY_001","企业管理员账号转移中"),
    A_COMPANY_USER("YB_001","一般企业人员"),
    A_PLATFORM_USER("YB_002","一般平台人员"),
    A_COMPANY_ADMIN("GL_001","企业管理员"),
    A_PLATFORM_ADMIN("GL_002","平台管理员");

    /**
     * 类型
     */
    private String code;

    /**
     * 说明
     */
    private String msg;

    StatusResultEnum(String code, String desc) {
        this.code = code;
        this.msg = desc;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsgc(String msg) {
        this.msg = msg;
    }


    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    /**
     * 获取对应的枚举
     *
     * @param code 种类
     * @return 枚举值
     */
    public static StatusResultEnum getStatusResult(String code) {
        try {
            for (StatusResultEnum userStatusEnum : StatusResultEnum.values()) {
                if (userStatusEnum.code.equals(code)) {
                    return userStatusEnum;
                }
            }
            return null;
        } catch (Exception e) {
            return null;
        }
    }
}
