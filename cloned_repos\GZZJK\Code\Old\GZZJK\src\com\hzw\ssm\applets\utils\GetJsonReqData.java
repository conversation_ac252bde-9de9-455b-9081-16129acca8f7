package com.hzw.ssm.applets.utils;

import net.sf.json.JSONObject;
import org.apache.struts2.ServletActionContext;

import javax.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.io.IOException;

public class GetJsonReqData {

    /**
     * 获取接口传递的JSON数据
     *
     * @param request HttpServletRequest对象
     * @return JSON格式数据
     * @explain
     */
    public static JSONObject getJsonReqData() {
        // 获取Request对象
        HttpServletRequest request = ServletActionContext.getRequest();
        StringBuffer sb = new StringBuffer();
        try {
            // json格式字符串
            String jsonStr = "";
            // 获取application/json格式数据，返回字符流
            BufferedReader reader = request.getReader();
            // 对字符流进行解析
            while ((jsonStr = reader.readLine()) != null) {
                sb.append(jsonStr);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }

        // 将json字符串（jsonStr）-->json对象（JSONObject）
        JSONObject jo = JSONObject.fromObject(sb.toString());
        return jo;
    }
}
