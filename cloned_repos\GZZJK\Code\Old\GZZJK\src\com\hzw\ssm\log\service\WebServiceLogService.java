package com.hzw.ssm.log.service;

import java.util.Date;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.hzw.ssm.log.dao.WebServiceLogMapper;
import com.hzw.ssm.log.entity.WebServiceLogEntity;

/**
 * web service 服务日志
 * 
 * <AUTHOR>
 * @version 1.0
 * 
 */
@Service
public class WebServiceLogService {

	@Autowired
	private WebServiceLogMapper webServiceLogMapper;

	public void insertLog(String serviceId,String serviceType,String serviceInXml) {
		WebServiceLogEntity entity=new WebServiceLogEntity();
		entity.setServiceId(serviceId);
		entity.setServiceType(serviceType);
		entity.setServiceInXml(serviceInXml);
		entity.setCreateTime(new Date());
		webServiceLogMapper.insertLog(entity);
	}
	
	public void updateLog(String serviceId,String serviceCid,String serviceOutXml){
		WebServiceLogEntity entity=new WebServiceLogEntity();
		entity.setServiceId(serviceId);
		entity.setServiceOutXml(serviceOutXml);
		entity.setServiceCid(serviceCid);
		entity.setUpdateTime(new Date());
		webServiceLogMapper.updateLog(entity);
	}
}
