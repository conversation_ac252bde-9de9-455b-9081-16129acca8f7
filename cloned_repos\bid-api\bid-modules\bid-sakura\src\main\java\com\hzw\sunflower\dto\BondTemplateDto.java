package com.hzw.sunflower.dto;

import com.hzw.sunflower.entity.BondTemplate;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date ：Created in 2022/8/11 9:51
 * @description：
 * @modified By：`
 * @version: 1.0
 */
@ApiModel(description = "保证金模板dto")
@Data
public class BondTemplateDto extends BondTemplate {

    @ApiModelProperty("更新人")
    private String updateUserName;

}
