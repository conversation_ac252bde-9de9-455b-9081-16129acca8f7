package com.hzw.sunflower.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.hzw.sunflower.common.BaseBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date ：Created in 2021/4/12 17:20
 * @description：委托项目编号
 * @modified By：`
 * @version: 1.0
 */
@ApiModel(description = "委托项目编号表 ")
@TableName("t_project_entr_pers_num")
@Data
public class ProjectEntrPersNum extends BaseBean {

    @ApiModelProperty(value = "主键", position = 1)
    private Long id;

    @ApiModelProperty(value = "项目id", position = 2)
    private Long projectId;

    @ApiModelProperty(value = "委托项目编号", position = 3)
    private String projectNumber;
}
