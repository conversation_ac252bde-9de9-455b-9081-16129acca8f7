package com.hzw.sunflower.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hzw.sunflower.dto.PackageInfoDTO;
import com.hzw.sunflower.entity.NoticePackageR;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface NoticePackageRService extends IService<NoticePackageR> {


    /**
     * 根据条件查询包段简要信息
     *
     * @param projectId 项目ID
     * @param bidRound 轮次
     * @return 包段信息的列表
     */
    List<PackageInfoDTO> findPackageInfoByProjectIdAndRound(@Param("projectId") Long projectId,@Param("bidRound") Integer bidRound);

    List<PackageInfoDTO> findPackageInfoByNoticeId(@Param("projectId") Long noticeId);

    /**
     * 根据招标公告ID查询此公告所关联的包段ID
     *
     * @param noticeId 招标公告ID
     * @return 此公告关联的包段ID的列表
     */
    List<Long> findNoticePackageIdsByNoticeId(Long noticeId);


    /**
     * 根据项目ID查询所有处于进行中和划分包段状态的包段的ID
     *
     * @param projectId 项目ID
     * @return 可用包段的列表
     */
    List<Long> findAllAvailablePackageIds(Long projectId);
}
