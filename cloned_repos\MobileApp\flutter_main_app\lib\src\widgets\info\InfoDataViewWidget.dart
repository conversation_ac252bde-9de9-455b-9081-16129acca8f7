///***
/// * 信息订阅 - 数据显示的页面
/// *
/// * <AUTHOR>
/// * @date 20191023
/// */

import 'package:flutter/material.dart';

import 'package:flutter_main_app/serviceLocator.dart';
import 'package:flutter_main_app/src/constants/theme.dart';
import 'package:flutter_main_app/src/utils/ScreenUtils.dart';

import 'package:flutter_main_app/src/viewModels/customInfo/CustomInfoViewModel.dart';

import 'package:flutter_main_app/src/models/bulletin/BulletinListItemModel.dart';

import 'package:flutter_main_app/src/components/CustomSafeArea.dart';
import 'package:flutter_main_app/src/components/DataListView.dart';
import 'package:flutter_main_app/src/components/CustomInfoCard.dart';
import 'package:flutter_main_app/src/components/CustomTab.dart';

class InfoDataViewWidget extends StatefulWidget {

  const InfoDataViewWidget({Key key}) : super(key: key);

  /// * 路由名称
  static const String _routeName = 'InfoDataView';
  static String get routeName => _routeName;

  @override
  _InfoDataViewWidgetState createState() => _InfoDataViewWidgetState();
}

class _InfoDataViewWidgetState extends State<InfoDataViewWidget> {

  final CustomInfoViewModel _model = serviceLocator<CustomInfoViewModel>();

  List<String> _keywords = [];

  Future<List<BulletinListItemModel>> _initDataList;

  @override
  void initState() {
    super.initState();

    _keywords.addAll(_model.keywords);

    /// ! 下面的函数不能触发组件的销毁重建，否则报错：
    /// ! setState() or markNeedsBuild() called during build.
    _initDataList = _model.fetchDataList();
  }

  @override
  void dispose() {
    _initDataList = null;

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return CustomSafeArea(
      child: Column(
        children: <Widget>[
          /// * 订阅关键词
          Container(
            margin: const EdgeInsets.only(bottom: 1.0),
            padding: const EdgeInsets.symmetric(horizontal: 15.0, vertical: 10.0),
            width: ScreenUtils.screenWidth,
            constraints: BoxConstraints(
              minHeight: 40.0,
            ),
            decoration: BoxDecoration(
              color: themeContentBackgroundColor,
              boxShadow: [
                BoxShadow(
                  color: const Color(0xff8292ba).withOpacity(0.21),
                  blurRadius: 6.0,
                ),
              ],
            ),
            child: Row(
              mainAxisSize: MainAxisSize.max,
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                Text(
                  '订阅关键词：',
                  style: TextStyle(
                    fontSize: 14.0,
                    color: themeTitleColor,
                  ),
                ),
                Expanded(
                  child: Text(
                    '${_keywords.join("·")}',
                    style: TextStyle(
                      fontSize: 14.0,
                      color: themeTitleColor,
                    ),
                  ),
                ),
              ],
            ),
          ),
          /// * 数据列表
          Expanded(
            child: FutureBuilder<List<BulletinListItemModel>>(
              future: _initDataList,
              builder: (BuildContext context, AsyncSnapshot<List<BulletinListItemModel>> snapshot) {
                switch (snapshot.connectionState) {
                  case ConnectionState.none:
                  case ConnectionState.active:
                  case ConnectionState.waiting:
                    return Center(
                      child: CircularProgressIndicator(),
                    );
                  case ConnectionState.done:
                    if (snapshot.hasError) {
                      return Text('Error: ${snapshot.error}');
                    }

                    return DataListView<BulletinListItemModel>(
                      initialDataItems: snapshot.data,
                      itemBuilder: (BuildContext context, int index, List<BulletinListItemModel> dataItems) {
                        final bulletin = dataItems[index];

                        return Container(
                          color: themeContentBackgroundColor,
                          child: CustomInfoCard(
                            title: bulletin.bulletinName != null && bulletin.bulletinName.isNotEmpty
                              ? bulletin.bulletinName
                              : '---',
                            tabs: [
                              CustomTab(
                                text: '${bulletin.regionName}',
                                color: const Color(0xff3e78e6),
                                borderColor: const Color(0xff3e78e6),
                              ),
                              CustomTab(
                                text: '${bulletin.classifyName ?? "其他"}',
                                color: const Color(0xfff9a545),
                                borderColor: const Color(0xfff9a545),
                              ),
                            ],
                            date: bulletin.noticeSendTime != null && bulletin.noticeSendTime.length >= 10
                              ? bulletin.noticeSendTime.substring(0, 10)
                              : null,
                            isFirstChild: index == 0,
                          ),
                        );
                      },
                      itemClickFunction: (_, __, ___) => _handleDataTileTaped,
                      refreshDataFunction: () => _handleDataListRefresh(),
                      loadingMoreDataFunction: () => _handleDataListLoadMore(),
                    );
                  default:
                    return Container();
                }
              },
            ),
          ),
        ],
      ),
    );
  }

  ///***
  /// * 下拉刷新数据列表
  /// *
  /// * @return {Future<List<BulletinListItemModel>>}
  /// */
  Future<List<BulletinListItemModel>> _handleDataListRefresh() async {
    _model.clearDataList();
    _model.setSpecifiedParam('currentPage', '1');

    return await _model.fetchDataList();
  }

  ///***
  /// * 滚动到数据列表底部加载更多
  /// *
  /// * @return {Future<List<BulletinListItemModel>>}
  /// */
  Future<List<BulletinListItemModel>> _handleDataListLoadMore() async {
    _model.setSpecifiedParam(
      'currentPage',
      _model.dataList[_model.dataList.length - 1].currentPageCursor,
    );

    return await _model.fetchDataList();
  }

  ///***
  /// * 点击查看市场主体的信息
  /// *
  /// * @param {BuildContext} context
  /// * @param {int} index
  /// * @param {List<BulletinListItemModel>} dataList
  /// */
  void _handleDataTileTaped(
    BuildContext context,
    int index,
    List<BulletinListItemModel> dataList,
  ) {
    /// TODO: 公告公示 Tile 点击事件
    if (index % 3 == 0) {
      Navigator.pushNamed(context, 'TenderBulletinDetail');
    } else if (index % 3 == 1) {
      Navigator.pushNamed(context, 'WinCandidateBulletinDetail');
    } else if (index % 3 == 2) {
      Navigator.pushNamed(context, 'WinBidBulletinDetail');
    }
  }

}
