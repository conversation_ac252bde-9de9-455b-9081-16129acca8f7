package com.hzw.sunflower.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Map;

@Data
public class WorkFlowDTO implements Serializable {
    private String userCode;
    private String departId;
    private String processDefinitionKey;
    private String formName;
    private String businessKey;
    private Map<String, Object> variables;
    private Boolean isJump = true;
    /**
     * 工作流返回结果字段
     */
    private String returnStr;

    private String processInstanceId;
    private String taskId;
    private String message;
    private Boolean isAgree;
    private String nextUserCode;
    private String nextTaskUserDepartCode;
    /**
     * 是否未处长
     */
    private Boolean isCZ = false;
    private String operation;

}
