package com.hzw.ssm.applets.utils;

import com.hzw.ssm.util.string.StringUtils;
import org.apache.commons.codec.binary.Base64;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.security.MessageDigest;

public class MyMD5Util {
	
	/***
     * MD5加码 生成32位md5码
     */
    public static String string2MD5(String inStr){
        MessageDigest md5 = null;
        try{
            md5 = MessageDigest.getInstance("MD5");
        }catch (Exception e){
            System.out.println(e.toString());
            e.printStackTrace();
            return "";
        }
        char[] charArray = inStr.toCharArray();
        byte[] byteArray = new byte[charArray.length];
 
        for (int i = 0; i < charArray.length; i++)
            byteArray[i] = (byte) charArray[i];
        byte[] md5Bytes = md5.digest(byteArray);
        StringBuffer hexValue = new StringBuffer();
        for (int i = 0; i < md5Bytes.length; i++){
            int val = ((int) md5Bytes[i]) & 0xff;
            if (val < 16)
                hexValue.append("0");
            hexValue.append(Integer.toHexString(val));
        }
        return hexValue.toString();
 
    }

    /**
     * 加密解密算法 执行一次加密，两次解密
     */
//    public static String convertMD5(String inStr){
//
//        char[] a = inStr.toCharArray();
//        for (int i = 0; i < a.length; i++){
//            a[i] = (char) (a[i] ^ 't');
//        }
//        String s = new String(a);
//        return s;
//
//    }



	 //--------------AES---------------
   private static final String KEY = "jiangsuhuabangyu";  // 密匙，必须16位
   private static final String OFFSET = "jiangsuhuabangyu"; // 偏移量
   private static final String ENCODING = "UTF-8"; // 编码
   private static final String ALGORITHM = "AES"; //算法
   private static final String CIPHER_ALGORITHM = "AES/CBC/PKCS5Padding"; // 默认的加密算法，CBC模式

	/**
    *  AES加密
    * @param data
    * @return String
    * <AUTHOR>
    * @date   2019-8-24 18:43:07
    */
   public static String AESencrypt(String data) throws Exception
   {
       Cipher cipher = Cipher.getInstance(CIPHER_ALGORITHM);
       SecretKeySpec skeySpec = new SecretKeySpec(KEY.getBytes("ASCII"), ALGORITHM);
       IvParameterSpec iv = new IvParameterSpec(OFFSET.getBytes());//CBC模式偏移量IV
       cipher.init(Cipher.ENCRYPT_MODE, skeySpec, iv);
       byte[] encrypted = cipher.doFinal(data.getBytes(ENCODING));
       return new Base64().encodeToString(encrypted);//加密后再使用BASE64做转码
   }

   /**
    * AES解密
    * @param data
    * @return String
    * <AUTHOR>
    * @date   2019-8-24 18:46:07
    */
   public static String AESdecrypt(String data) throws Exception
   {
       Cipher cipher = Cipher.getInstance(CIPHER_ALGORITHM);
       SecretKeySpec skeySpec = new SecretKeySpec(KEY.getBytes("ASCII"), ALGORITHM);
       IvParameterSpec iv = new IvParameterSpec(OFFSET.getBytes()); //CBC模式偏移量IV
       cipher.init(Cipher.DECRYPT_MODE, skeySpec, iv);
       byte[] buffer = new Base64().decode(data);//先用base64解码
       byte[] encrypted = cipher.doFinal(buffer);
       return new String(encrypted, ENCODING);
   }
	


    public static void main(String[] args) throws Exception {
    	String token = JudgeToken.generateToken();
		String s = new String(token+"@1@wechat");
        System.out.println("原始：" + s);
        String str =  string2MD5(s);
        System.out.println("加密的：" + str);
        String jm =  string2MD5(string2MD5(str));
        System.out.println("解密的：" +jm);
        System.out.println(StringUtils.isEmpty(" ".trim()));

		String ss = AESencrypt(s);
		System.out.println(ss);
		String aeSdecrypt = AESdecrypt(ss);
		System.out.println(aeSdecrypt);

	}




}
