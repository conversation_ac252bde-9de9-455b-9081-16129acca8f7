package com.hzw.sunflower.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzw.sunflower.constant.CommonConstants;
import com.hzw.sunflower.constant.constantenum.*;
import com.hzw.sunflower.controller.project.request.AgentReProjectReq;
import com.hzw.sunflower.controller.project.request.EntrustProjectReq;
import com.hzw.sunflower.controller.project.request.PurchaseNumberReq;
import com.hzw.sunflower.controller.project.request.ReProjectSection;
import com.hzw.sunflower.dao.ReProjectMapper;
import com.hzw.sunflower.dto.*;
import com.hzw.sunflower.entity.*;
import com.hzw.sunflower.exception.ExceptionEnum;
import com.hzw.sunflower.exception.SunFlowerException;
import com.hzw.sunflower.service.*;
import com.hzw.sunflower.util.BeanListUtil;
import com.hzw.sunflower.util.RequestUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class AgentReProjectServiceImpl extends ServiceImpl<ReProjectMapper, Project> implements AgentReProjectService {

    @Autowired
    private ProjectSerialNumService projectSerialNumService;

    @Autowired
    private ProjectService projectService;

    @Autowired
    private ProjectEntrustUserService projectEntrustUserService;

    @Autowired
    private ProjectEntrPersNumService projectEntrPersNumService;

    @Autowired
    private ProjectBidSectionService sectionService;

    @Autowired
    private UserAgentRecordService userAgentRecordService;

    @Autowired
    private ReProjectMapper reProjectMapper;

    @Autowired
    private RabbitMqService rabbitMqService;

    @Autowired
    private CommonSectionService commonSectionService;

    @Autowired
    private ISectionExpertSpecialtyService sectionExpertSpecialtyService;

    @Autowired
    private ProjectShareService projectShareService;

    @Autowired
    private ProjectShareNccService projectShareNccService;

    @Autowired
    private ProjectTendererPowerService projectTendererPowerService;

    @Autowired
    private ProjectDeptRoleService projectDeptRoleService;

    @Autowired
    private ProjectBidSectionService projectBidSectionService;


//    private long reProjectId;
//
//    private long baseProjectId;



    @Override
    public Boolean addAgentReProject(AgentReProjectReq agentReProjectReq, JwtUser user) {

        boolean flag = false;

        int index = 0;
        long reProjectId = 0;
        long baseProjectId = 0;

        for(ReProjectSection reProjectSection : agentReProjectReq.getProSectionList()){
            //092-BUG7719发现重复bid导致的重新招标流程出现重复标段数据
            reProjectSection.setBidList(reProjectSection.getBidList().stream().distinct().collect(Collectors.toList()));

            baseProjectId = reProjectSection.getProjectId();

            // 获取项目信息
            Project reProject = this.getById(reProjectSection.getProjectId());
            Long formProjectId = reProject.getId();
            // 执行重新招标项目入库
            flag = addReProject(agentReProjectReq,reProjectSection,reProject,index);
            reProjectId = reProject.getId();
            //委托信息直接带过来 不需要以前的保存委托关系操作
            if (flag&&index==0) {
                agentReProjectReq.setProjectId(reProjectId);
                //保存委托人关系数据
                //重新招标的项目使用原委托关系
                saveProjectEntrusetUser(agentReProjectReq, formProjectId);
                //执行委托项目入库
                bidderProject(agentReProjectReq, reProject);

                //保存分摊数据
                LambdaQueryWrapper<ProjectShare> qws = new LambdaQueryWrapper<>();
                qws.eq(ProjectShare::getProjectId,reProjectSection.getProjectId());
                List<ProjectShare> psl = projectShareService.getBaseMapper().selectList(qws);
                for (ProjectShare share:psl) {
                    share.setId(null);
                    share.setProjectId(reProject.getId());
                }
                projectShareService.saveOrUpdateBatch(psl);

                // 保存分摊数据(ncc)
                LambdaQueryWrapper<ProjectShareNcc> qw = new LambdaQueryWrapper<>();
                qw.eq(ProjectShareNcc::getProjectId,reProjectSection.getProjectId());
                List<ProjectShareNcc> ps2 = projectShareNccService.findInfo(reProjectSection.getProjectId());

                for (ProjectShareNcc share:ps2) {
                    share.setId(null);
                    share.setProjectId(reProject.getId());
                }
                projectShareNccService.saveOrUpdateBatch(ps2);

                // 招标人权限
                LambdaQueryWrapper<ProjectTendererPower> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(ProjectTendererPower::getProjectId,reProjectSection.getProjectId());
                List<ProjectTendererPower> powers = projectTendererPowerService.list(wrapper);
                for (ProjectTendererPower share:powers) {
                    share.setId(null);
                    share.setProjectId(reProject.getId());
                }
                projectTendererPowerService.saveOrUpdateBatch(powers);

                AgentInItDto agentInItDto = projectEntrustUserService.getEntrustUserByProjectIdAndType(baseProjectId, EntrustUserType.ENTRUSTED_PERSON.getType().intValue());
                agentReProjectReq.setAgentId(agentInItDto.getCompanyId());
                agentReProjectReq.setAgentChargeId(agentInItDto.getUserId());
                agentReProjectReq.setAgentDepartId(agentInItDto.getDepartmentId());
                //执行代理机构信息入库操作
                flag = lastAgentRecordRe(agentReProjectReq, reProject, user);

                //执行更新项目表冗余字段
                projectService.saveOrUpdateProjectRedundantFieldDealWith(baseProjectId,true);
                commonSectionService.saveOrUpdateProjectPackageFieldDealWith(baseProjectId);
            }
            index++;
        }
        // 查询重新招标项目id 发送消息到公函更新公函中项目状态
        LambdaQueryWrapper<Project> projectQuery = new LambdaQueryWrapper<>();
        projectQuery.eq(Project::getBaseProjectId, agentReProjectReq.getProjectId());
        List<Project> projects = reProjectMapper.selectList(projectQuery);
        for (Project project : projects) {
            rabbitMqService.sendMq(project.getId());
        }


        //推送项目到ncc
        projectService.pushProjectNcc(reProjectId,null);
        //推送标段
        LambdaQueryWrapper<ProjectBidSection> sectionQuery = new LambdaQueryWrapper<>();
        sectionQuery.eq(ProjectBidSection::getProjectId,reProjectId);
        List<ProjectBidSection> reSections = projectBidSectionService.list(sectionQuery);
        if(!reSections.isEmpty()){
            reSections.forEach(s->{
                if(s.getPackageNumber() != null){
                    projectService.pushProjectNcc(s.getProjectId(),s.getPackageNumber());
                }
            });
        }

        return flag;
    }

    /**
     * 根据采购项目编号 查询项目信息
     * @param purchaseNumber
     * @return
     */
    @Override
    public AgentProjectDTO getProjectByPurchaseNumber(String purchaseNumber) {
        AgentProjectDTO dto = new AgentProjectDTO();
        QueryWrapper<Project> queryWrapper = new QueryWrapper<>();
        queryWrapper.orderByDesc("created_time");
        queryWrapper.lambda().eq(Project::getPurchaseNumber,purchaseNumber);
        //获取项目信息
        List<Project> projectList = reProjectMapper.selectList(queryWrapper);
        // 如果项目不存在抛出异常
        if (RequestUtil.isEmpty(projectList) || projectList.size() == 0) {
            throw new SunFlowerException(ExceptionEnum.PROJECT_NOT_FOUND, ExceptionEnum.PROJECT_NOT_FOUND.getMessage());
        }

        BeanListUtil.copyProperties(projectList.get(0), dto);

        //新需求  修改getNoticeByPurchaseNumber  xml文件，存在标段终止的情况，才可以重新招标
        List<ReSectionCanDTO> reSectionCanDTOList = reProjectMapper.getNoticeByPurchaseNumber(purchaseNumber);

        if (reSectionCanDTOList.size() == 0) {
            //throw new SunFlowerException(ExceptionEnum.RE_TENDER_FAILED,ExceptionEnum.RE_TENDER_FAILED.getMessage());
            dto.setReSectionList(new ArrayList<ReSectionCanDTO>());
        } else {
            Map<Long, ReSectionCanDTO> filterNotice = reSectionCanDTOList.parallelStream().collect(Collectors.toMap
                    (ReSectionCanDTO::getSectionId, Function.identity(),
                            (c1, c2) -> c1.getNoticeId() > c2.getNoticeId() ? c1 : c2));
            reSectionCanDTOList = filterNotice.values().stream().collect(Collectors.toList()); // 092移除因重复公告导致的标段重复数据
            //可重新招标标段信息
            dto.setReSectionList(reSectionCanDTOList);
        }
       /* List<ReSectionCanDTO> reSectionList = new ArrayList<>();
        for(ReSectionCanDTO reSectionCanDTO : reSectionCanDTOList){
            //已发公告 未归档
            if(reSectionCanDTO.getNoticeProgress().equals(NoticeProgressEnum.RELEASE.getValue())&&reSectionCanDTO.getStatus()<= PackageStatusEnum.ARCHIVING.getValue()){
                reSectionList.add(reSectionCanDTO);
            }
        }*/

        //查询所有关系数据
        AgentInItDto projectUser = projectEntrustUserService.getEntrustUserByProjectIdAndType(projectList.get(0).getId(), EntrustUserType.PRINCIPAL.getType());
        dto.setAgentUserDto(projectUser);
        //查询所有关系数据
        AgentInItDto pUser = projectEntrustUserService.getEntrustUserByProjectIdAndType(projectList.get(0).getId(), EntrustUserType.ENTRUSTED_PERSON.getType());
        dto.setPagentUserDto(pUser);
        //获取委托项目信息
        LambdaQueryWrapper<ProjectEntrPersNum> projectNum = new LambdaQueryWrapper<ProjectEntrPersNum>();
        projectNum.eq(ProjectEntrPersNum::getProjectId, projectList.get(0).getId());
        List<ProjectEntrPersNum> list = projectEntrPersNumService.list(projectNum);
        dto.setProjectEntrPersNumDTO(BeanListUtil.convertList(list, ProjectEntrPersNumDTO.class));
        return dto;
    }

    /**
     * 重新招标历史记录
     *
     * @param projectId
     * @return
     */
    @Override
    public List<ProjectRecordDTO> getRecordByProjectId(Long projectId) {
        return reProjectMapper.getRecord(projectId);
    }

    @Override
    public AgentProjectDTO getByPurchaseNumberRe(PurchaseNumberReq req) {
        AgentProjectDTO dto = new AgentProjectDTO();
        QueryWrapper<Project> queryWrapper = new QueryWrapper<>();
        queryWrapper.orderByDesc("created_time");
        queryWrapper.lambda().eq(Project::getPurchaseNumber,req.getPurchaseNumber());
        //获取项目信息
        List<Project> projectList = reProjectMapper.selectList(queryWrapper);
        // 如果项目不存在抛出异常
        if (RequestUtil.isEmpty(projectList) || projectList.size() == 0) {
            throw new SunFlowerException(ExceptionEnum.PROJECT_NOT_FOUND, ExceptionEnum.PROJECT_NOT_FOUND.getMessage());
        }

        BeanListUtil.copyProperties(projectList.get(0), dto);

        //新需求  修改getNoticeByPurchaseNumber  xml文件，存在标段终止的情况，才可以重新招标
        List<ReSectionCanDTO> reSectionCanDTOList = reProjectMapper.getNoticeByPurchaseNumberId(req);

        if (reSectionCanDTOList.size() == 0) {
            //throw new SunFlowerException(ExceptionEnum.RE_TENDER_FAILED,ExceptionEnum.RE_TENDER_FAILED.getMessage());
            dto.setReSectionList(new ArrayList<ReSectionCanDTO>());
        } else {
            Map<Long, ReSectionCanDTO> filterNotice = reSectionCanDTOList.parallelStream().collect(Collectors.toMap
                    (ReSectionCanDTO::getSectionId, Function.identity(),
                            (c1, c2) -> c1.getNoticeId() > c2.getNoticeId() ? c1 : c2));
            reSectionCanDTOList = filterNotice.values().stream().collect(Collectors.toList()); // 092移除因重复公告导致的标段重复数据
            //可重新招标标段信息
            dto.setReSectionList(reSectionCanDTOList);
        }

        //查询所有关系数据
        AgentInItDto projectUser = projectEntrustUserService.getEntrustUserByProjectIdAndType(projectList.get(0).getId(), EntrustUserType.PRINCIPAL.getType());
        dto.setAgentUserDto(projectUser);
        //查询所有关系数据
        AgentInItDto pUser = projectEntrustUserService.getEntrustUserByProjectIdAndType(projectList.get(0).getId(), EntrustUserType.ENTRUSTED_PERSON.getType());
        dto.setPagentUserDto(pUser);
        //获取委托项目信息
        LambdaQueryWrapper<ProjectEntrPersNum> projectNum = new LambdaQueryWrapper<ProjectEntrPersNum>();
        projectNum.eq(ProjectEntrPersNum::getProjectId, projectList.get(0).getId());
        List<ProjectEntrPersNum> list = projectEntrPersNumService.list(projectNum);
        dto.setProjectEntrPersNumDTO(BeanListUtil.convertList(list, ProjectEntrPersNumDTO.class));
        return dto;
    }

    /**
     * 新增重新招标项目
     * @param agentReProjectReq
     * @param reProjectSection
     * @param reProject
     * @param index
     * @return
     */
    private Boolean addReProject(AgentReProjectReq agentReProjectReq, ReProjectSection reProjectSection, Project reProject,int index) {

        //BeanUtil.copyProperties(agentReProjectReq, reProject);
        //判断角色类型 代理机构新增项目直接生成采购项目 ,招标人在代理机构接收委托生成

        // 原项目 要变成已重新招标状态
        reProject.setReTender(CommonConstants.YES);
        reProject.setStatus(PackageStatusEnum.ARCHIVING.getValue());
        projectService.updateProject(reProject);

        //使用原采购编号
        if (agentReProjectReq.getIsFormerPurchaseNumber().equals(CommonConstants.NO)) {
            reProject.setPurchaseNumber(agentReProjectReq.getFormerPurchaseNumber());
            reProject.setIsFormerPurchaseNumber(CommonConstants.NO);
        } else {
            ProjectEntrustUser reuler = this.baseMapper.getBaseProjectEntrust(reProject.getId(), EntrustUserType.ENTRUSTED_PERSON.getType());
            reProject.setPurchaseNumber(projectSerialNumService.generateProjectNum(reProject.getProvinceInOut(), reuler.getDepartmentId()));
            reProject.setIsFormerPurchaseNumber(CommonConstants.YES);
        }
        //原项目采购编号
        reProject.setFormerPurchaseNumber(agentReProjectReq.getFormerPurchaseNumber());
        //reProject.setStatus(ProjectStatusEnum.GOING.getValue());
        reProject.setStatus(ProjectStatusEnum.FINISH.getValue());
        //第一次重新招标
        if (reProject.getBaseProjectId() == null) {
            reProject.setBaseProjectId(reProject.getId());
        }

        if (index == 0) {
            reProject.setReTender(CommonConstants.NO);

            //修复bug-1400
            String projectStatus = "";
            String projectStatusCode = "";
            for (int i = 0; i < reProjectSection.getBidList().size(); i++) {
                projectStatus += PackageStatusEnum.TENDER_INVITATION.getName();
                projectStatusCode += PackageStatusEnum.TENDER_INVITATION.getValue().toString();
                if (i != reProjectSection.getBidList().size() - 1) {
                    projectStatus += ",";
                    projectStatusCode += ",";
                }
            }
            reProject.setProjectStatus(projectStatus);
            reProject.setProjectStatusCode(projectStatusCode);
            //重新招标项目异常状态恢复
            reProject.setAbnormalStatus(AbnormalStatusEnum.NORMAL.getType());
            reProject.setId(null);
            this.save(reProject);
//            reProjectId = reProject.getId();
        }
        Long reProjectId = reProject.getId();

        List<ProjectBidSection> sections = new ArrayList<>();
        List<ProjectBidSection> reSections = new ArrayList<>();
        for (int i = 0; i < reProjectSection.getBidList().size(); i++) {
            Long sectionId = reProjectSection.getBidList().get(i);
            ProjectBidSection projectBidSectionSel = sectionService.getById(sectionId);
            //原标段终止
            projectBidSectionSel.setAbnormalStatus(AbnormalStatusEnum.END.getType());
            projectBidSectionSel.setStatus(PackageStatusEnum.EXCEPT_STOP.getValue().toString());
            //本次项目是否被人重新招标 0：否  1：是
            projectBidSectionSel.setIsRebidding(CommonConstants.YES);
            sections.add(projectBidSectionSel);
           // sectionService.updateById(projectBidSectionSel);

            //重新招标 只带包号 包名称
            ProjectBidSection projectBidSection = BeanListUtil.convert(projectBidSectionSel,ProjectBidSection.class);//new ProjectBidSection();
            projectBidSection.setStatus(PackageStatusEnum.TENDER_INVITATION.getValue().toString());
            projectBidSection.setBidSectionCode(BidSectionCodeEnum.bidding_announcement_no.getCode());
            projectBidSection.setPackageName(projectBidSectionSel.getPackageName());
            projectBidSection.setAbnormalStatus(AbnormalStatusEnum.NORMAL.getType());
            projectBidSection.setOldBidderType(1);//上次项目投标人(1.已购买招标文件的投标人 2.已通过关注审核的投标人 3.指定投标人 4.不带入
            projectBidSection.setFormerPurchaseStatus(projectBidSectionSel.getPurchaseStatus());
            projectBidSection.setSaleEndTimeType(null);
            projectBidSection.setSaleEndTime(null);
            projectBidSection.setSubmitEndTimeType(null);
            projectBidSection.setSubmitEndTime(null);
            projectBidSection.setId(null);
            projectBidSection.setFormerAbnormalStatus(null);
            //本次项目是否被人重新招标 0：否  1：是
            projectBidSection.setIsRebidding(CommonConstants.NO);
            //标段轮次初始化 资格预审项目走到第二轮 重新招标也需要从第一轮重新开始
            if(PurchaseStatusEnum.PRE_TRIAL.getType().equals(projectBidSectionSel.getPurchaseStatus())){
                projectBidSection.setBidRound(BidRoundEnum.ZGYS.getType());
            }
            projectBidSection.setSectionGroupId((long) 0);
            projectBidSection.setProjectId(reProjectId);

            if(projectBidSectionSel.getBaseSectionId()==null){
                projectBidSection.setBaseSectionId(sectionId);
                projectBidSection.setBasePackageNumber(projectBidSectionSel.getPackageNumber());
            }
            projectBidSection.setFormerSectionId(sectionId);
            projectBidSection.setFormerPackageNumber(projectBidSectionSel.getPackageNumber());

            //采用新的采购项目编号 新包对应原来包
            if (agentReProjectReq.getIsFormerPurchaseNumber().equals(CommonConstants.YES)) {
                if(reProject.getPackageSegmentStatus().equals(SegmentEnum.HAVE_BAG.getType())){
                    //有包状态
                    //解决1358【重新招标】划分标段包页面，包号显示错误
                    projectBidSection.setPackageNumber(i+1);
                }
            }else{
                projectBidSection.setPackageNumber(projectBidSectionSel.getPackageNumber());
            }
            sectionService.save(projectBidSection);
            //查询标的物分类信息 重现招标的时候备份过去
            LambdaQueryWrapper<SectionExpertSpecialty>  specialtyQuery = new LambdaQueryWrapper<>();
            specialtyQuery.eq(SectionExpertSpecialty::getSectionId,sectionId);
            List<SectionExpertSpecialty> list = sectionExpertSpecialtyService.list(specialtyQuery);
            list.forEach(l->{
                l.setSectionId(projectBidSection.getId());
                l.setId(null);
            });
            List<SectionExpertSpecialty> specialties = BeanListUtil.convertList(list,SectionExpertSpecialty.class);
            sectionExpertSpecialtyService.saveBatch(specialties);

            reSections.add(projectBidSection);
        }

        //刷新标段状态
        commonSectionService.updateSectionStatus(sections);
        //重新招标新增
        commonSectionService.updateSectionStatus(reSections);



        return true;
    }

    /**
     * 执行委托项目入库
     *
     * @param agentProject
     * @param project
     */
    private Boolean bidderProject(AgentReProjectReq agentProject, Project project) {
        List<ProjectEntrPersNum> list = null;
        List<EntrustProjectReq> projectEntrPeersNums = agentProject.getEntrustProjectNumList();
        list = BeanListUtil.convertList(projectEntrPeersNums, ProjectEntrPersNum.class);
        //委托编号不存在时new个委托编号对象
        if (CollectionUtils.isEmpty(list)) {
            list = new ArrayList<>();
            list.add(new ProjectEntrPersNum());
        }

        list.forEach(p -> p.setProjectId(project.getId()));
        return projectEntrPersNumService.saveBatch(list);
    }

    /**
     * 保存项目委托关系
     *
     * @param user
     * @param req
     */
    private Boolean saveProjectEntrusetUser(AgentReProjectReq req, Long baseProjectId) {
        List<ProjectEntrustUser> lt = new ArrayList<ProjectEntrustUser>();
        ProjectEntrustUser euler = new ProjectEntrustUser();
        euler = this.baseMapper.getBaseProjectEntrust(baseProjectId, EntrustUserType.PRINCIPAL.getType());
        euler.setProjectId(req.getProjectId());
        lt.add(euler);
        ProjectEntrustUser reuler = new ProjectEntrustUser();
        reuler = this.baseMapper.getBaseProjectEntrust(baseProjectId, EntrustUserType.ENTRUSTED_PERSON.getType());
        reuler.setProjectId(req.getProjectId());
        lt.add(reuler);
        //新增被委托人信息
        return projectEntrustUserService.saveOrUpdateBatch(lt);
    }

    /**
     * 存储用户新增时增加的代理机构信息
     *
     * @param reProject
     */
//    private Boolean lastAgentRecord(Project reProject, JwtUser user) {
//        UserAgentRecord record = BeanListUtil.convert(reProject, UserAgentRecord.class);
//        record.setUserId(user.getUserId());
//        record.setAgentId(user.getCompanyId());
//        record.setAgentChargeId(user.getUserId());
//        return userAgentRecordService.save(record);
//    }


    /**
     * 重新招标存储用户新增时增加的代理机构信息
     *
     * @param reProject
     */
    private Boolean lastAgentRecordRe(AgentReProjectReq agentReProjectReq,Project reProject, JwtUser user) {
        UserAgentRecord record = BeanListUtil.convert(reProject, UserAgentRecord.class);
        record.setId(null);
        record.setUserIdentity(CompanyEnum.AGENCY.getType());
        record.setUserId(user.getUserId());
        record.setAgentId(agentReProjectReq.getAgentId());
        record.setAgentDepartId(agentReProjectReq.getAgentDepartId());
        record.setAgentChargeId(agentReProjectReq.getAgentChargeId());
        return userAgentRecordService.save(record);
    }
}
