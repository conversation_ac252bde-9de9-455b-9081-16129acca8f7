///***
/// * 业主信息 - 首页
/// */
import "package:flutter/material.dart";

import 'package:flutter_main_app/serviceLocator.dart';
import 'package:flutter_main_app/src/constants/theme.dart';
import 'package:flutter_main_app/src/utils/ScreenUtils.dart';

import 'package:flutter_main_app/src/viewModels/marketPlayer/MarketPlayerListViewModel.dart';

import 'package:flutter_main_app/src/models/marketPlayer/SubjectBasicModel.dart';
import 'package:flutter_main_app/src/models/common/TabItemModel.dart';

import 'package:flutter_main_app/src/components/CustomAppBar.dart';
import 'package:flutter_main_app/src/components/CustomSafeArea.dart';
import 'package:flutter_main_app/src/components/DataListView.dart';
import 'package:flutter_main_app/src/components/CustomInfoCard.dart';

import 'package:flutter_main_app/src/widgets/market/SubjectHomeWidget.dart';

class MarketIndexWidget extends StatefulWidget {
  const MarketIndexWidget({Key key}) : super(key: key);

  /// * 路由名称
  static const String _routeName = 'MarketIndex';
  static String get routeName => _routeName;

  @override
  _MarketIndexWidgetState createState() => _MarketIndexWidgetState();
}

class _MarketIndexWidgetState extends State<MarketIndexWidget> {

  final TextEditingController _controller = TextEditingController();

  final FocusNode _focusNode = FocusNode();

  final MarketPlayerListViewModel _model = serviceLocator<MarketPlayerListViewModel>();

  Future<List<SubjectBasicModel>> _initMarketPlayerList;

  /// * tab 切换选项
  List<TabItemModel<int>> _marketPlayers = [
    new TabItemModel<int>(
      value: 3,
      label: '供应商',
      isActive: true,
    ),
    new TabItemModel<int>(
      value: 1,
      label: '招标人',
      isActive: false,
    ),
    new TabItemModel<int>(
      value: 5,
      label: '招标代理',
      isActive: false,
    ),
    // new TabItemModel<String>(
    //   value: '投标人',
    //   label: '投标人',
    //   isActive: false,
    // ),
  ];

  @override
  void initState() {
    super.initState();

    _controller.text = _model.subjectName;
    _controller.selection = TextSelection.collapsed(offset: _model.subjectName.length);

    _initMarketPlayerList = _model.fetchDataList();
  }

  @override
  void dispose() {
    /// * 每次切换 BottomNavigationBar 都还原配置
    _model.clearDataList();
    _model.subjectTypeID = 3;
    _model.subjectName = '';
    _model.currentPage = 1;
    _model.pageSize = 20;

    /// * handle error: setState() called after dispose()
    _initMarketPlayerList = null;

    _controller.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: '业主信息',
        leading: Container(),
      ),
      body: CustomSafeArea(
        child: Column(
          children: <Widget>[
            /// * 主体身份
            Container(
              width: ScreenUtils.screenWidth,
              height: 47.0,
              decoration: BoxDecoration(
                color: themeContentBackgroundColor,
                border: Border(
                  bottom: BorderSide(
                    width: themeBorderWidth,
                    color: themeBorderColor,
                  ),
                ),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.max,
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: this._buildMarketPlayerTabs(),
              ),
            ),
            /// * 搜索关键词
            Container(
              margin: const EdgeInsets.only(bottom: 12.0),
              padding: const EdgeInsets.fromLTRB(21.0, 10.0, 21.0, 10.0),
              color: themeContentBackgroundColor,
              child: Container(
                width: ScreenUtils.screenWidth,
                height: 40.0,
                padding: const EdgeInsets.symmetric(horizontal: 13.0),
                decoration: BoxDecoration(
                  color: themeBackgroundColor,
                  borderRadius: BorderRadius.circular(themeBorderRadius),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: <Widget>[
                    Icon(
                      Icons.search,
                      size: 18.0,
                      color: themeHintTextColor,
                    ),
                    Expanded(
                      child: TextField(
                        controller: _controller,
                        focusNode: _focusNode,
                        onChanged: _handleSubjectNameChange,
                        keyboardType: TextInputType.text,
                        style: TextStyle(
                          fontSize: 12.0,
                          color: themeTextColor,
                        ),
                        decoration: InputDecoration(
                          contentPadding: const EdgeInsets.only(left: 6.0),
                          hintText: '输入关键词单位、姓名、电话等',
                          hintStyle: TextStyle(
                            fontSize: 12.0,
                            color: themeHintTextColor,
                          ),
                          border: InputBorder.none,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            /// * 主体数据列表
            Expanded(
              child: FutureBuilder<List<SubjectBasicModel>>(
                future: _initMarketPlayerList,
                builder: (BuildContext context, AsyncSnapshot<List<SubjectBasicModel>> snapshot) {
                  switch (snapshot.connectionState) {
                    case ConnectionState.none:
                    case ConnectionState.active:
                    case ConnectionState.waiting:
                      return Center(
                        child: CircularProgressIndicator(),
                      );
                    case ConnectionState.done:
                      if (snapshot.hasError) {
                        return Text('Error: ${snapshot.error}');
                      }

                      return DataListView<SubjectBasicModel>(
                        initialDataItems: snapshot.data,
                        itemBuilder: (BuildContext context, int index, List<SubjectBasicModel> dataItems) {
                          final subject = dataItems[index];

                          return Container(
                            color: themeContentBackgroundColor,
                            child: CustomInfoCard(
                              title: subject.subjectName != null && subject.subjectName.isNotEmpty
                                ? subject.subjectName
                                : '---',
                              subTitle: subject.legalName != null && subject.legalName.isNotEmpty 
                                ? subject.legalName
                                : '---',
                              isFirstChild: index == 0,
                            ),
                          );
                        },
                        itemClickFunction: _handleDataTileTaped,
                        refreshDataFunction: _handleDataListRefresh,
                        loadingMoreDataFunction: _handleDataListLoadMore,
                      );
                    default:
                      return Container();
                  }
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  ///***
  /// * 生成市场主体的切换按钮列表 UI
  /// *
  /// * @return {List<Widget>}
  /// */
  List<Widget> _buildMarketPlayerTabs() {
    final List<Widget> marketPlayerWidgets = [];

    for (TabItemModel<int> marketPlayer in this._marketPlayers) {
      Widget marketPlayerWidget;

      if (marketPlayer.isActive) {
        marketPlayerWidget = Container(
          height: 46.0,
          decoration: BoxDecoration(
            border: Border(
              bottom: BorderSide(
                width: themeBorderWidth,
                color: themeActiveColor,
              ),
            ),
          ),
          child: Center(
            child: Text(
              marketPlayer.label,
              style: TextStyle(
                fontSize: 14.0,
                color: themeActiveColor,
              ),
            ),
          ),
        );
      } else {
        marketPlayerWidget = GestureDetector(
          onTap: () {
            this._handleMarketPlayerTabTaped(marketPlayer.value);
          },
          child: Container(
            height: 46.0,
            child: Center(
              child: Text(
                marketPlayer.label,
                style: TextStyle(
                  fontSize: 14.0,
                  color: themeTipsTextColor,
                ),
              ),
            ),
          ),
        );
      }

      marketPlayerWidgets.add(marketPlayerWidget);
    }

    return marketPlayerWidgets;
  }

  ///***
  /// * 市场主体切换按钮点击事件
  /// * 重建数据列表组件
  /// *
  /// * @param {int} value
  /// */
  void _handleMarketPlayerTabTaped(
    int value,
  ) {
    _model.clearDataList();
    _model.subjectTypeID = value;
    _model.subjectName = '';
    _model.currentPage = 1;
    _model.pageSize = 20;
    final Future<List<SubjectBasicModel>> _initMarketPlayerList = _model.fetchDataList();

    setState(() {
      this._initMarketPlayerList = _initMarketPlayerList;
    });

    final List<TabItemModel<int>> marketPlayers = List.from(this._marketPlayers);

    for (TabItemModel<int> marketPlayer in marketPlayers) {
      if (marketPlayer.value == value) {
        marketPlayer.isActive = true;
      } else {
        marketPlayer.isActive = false;
      }
    }

    setState(() {
      this._marketPlayers = marketPlayers;
    });
  }

  ///***
  /// * 模糊搜索关键词改变事件
  /// * 重建数据列表组件
  /// *
  /// * @param {String} subjectName
  /// */
  void _handleSubjectNameChange(String subjectName) {
    _model.clearDataList();
    _model.subjectName = subjectName;
    _model.currentPage = 1;
    _model.pageSize = 20;
    final Future<List<SubjectBasicModel>> _initMarketPlayerList = _model.fetchDataList();

    setState(() {
      this._initMarketPlayerList = _initMarketPlayerList;
    });
  }

  ///***
  /// * 下拉刷新数据列表
  /// *
  /// * @return {Future<List<SubjectBasicModel>>}
  /// */
  Future<List<SubjectBasicModel>> _handleDataListRefresh() async {
    _model.clearDataList();
    _model.currentPage = 1;
    _model.pageSize = 20;

    return await _model.fetchDataList();
  }

  ///***
  /// * 滚动到数据列表底部加载更多
  /// *
  /// * @return {Future<List<SubjectBasicModel>>}
  /// */
  Future<List<SubjectBasicModel>> _handleDataListLoadMore() async {
    _model.currentPage++;
    _model.pageSize = 20;

    return await _model.fetchDataList();
  }

  ///***
  /// * 点击查看市场主体的信息
  /// *
  /// * @param {BuildContext} context
  /// * @param {int} index
  /// * @param {List<SubjectBasicModel>} subjectBasicList
  /// */
  void _handleDataTileTaped(
    BuildContext context,
    int index,
    List<SubjectBasicModel> subjectBasicList,
  ) {
    final subjectID = subjectBasicList[index].subjectID;

    _model.subjectID = subjectID;

    Navigator.pushNamed(
      context,
      SubjectHomeWidget.routeName,
    );
  }

}
