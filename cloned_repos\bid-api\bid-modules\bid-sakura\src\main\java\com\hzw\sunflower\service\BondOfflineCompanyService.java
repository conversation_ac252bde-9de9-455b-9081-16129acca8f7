package com.hzw.sunflower.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hzw.sunflower.controller.request.BondOfflineCompanyReq;
import com.hzw.sunflower.controller.request.OfflineCompanyReq;
import com.hzw.sunflower.controller.response.OfflineCompanyVo;
import com.hzw.sunflower.entity.BondOfflineCompany;


public interface BondOfflineCompanyService  extends IService<BondOfflineCompany> {

    /**
     * 模糊搜索供应商信息
     * @param req
     * @return
     */
    IPage<OfflineCompanyVo> queryCompany(OfflineCompanyReq req);

    /**
     * 新增/修改供应商信息
     * @param req
     * @return
     */
    Boolean addCompany(BondOfflineCompanyReq req);

    /**
     * 编辑供应商信息
     * @param req
     * @return
     */
    Boolean updateCompany(BondOfflineCompanyReq req);

}
