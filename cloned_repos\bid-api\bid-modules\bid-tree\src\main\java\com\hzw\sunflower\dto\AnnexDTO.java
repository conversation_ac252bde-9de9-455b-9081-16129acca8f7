package com.hzw.sunflower.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * Created by zgq on 2021/07/05
 * 附件基本信息
 */

@Data
@Accessors(chain = true)
@ApiModel(description = "附件基本信息")
public class AnnexDTO {

    @ApiModelProperty(value = "附件ID", position = 1)
    private Long id;

    @ApiModelProperty(value = "OssKey", position = 2)
    private String key;

    @ApiModelProperty(value = "OssName", position = 3)
    private String name;
}
