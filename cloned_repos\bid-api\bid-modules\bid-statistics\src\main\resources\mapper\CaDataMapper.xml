<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hzw.sunflower.dao.CaDataMapper" >

    <select id="findInfoByReq" resultType="com.hzw.sunflower.controller.response.CaDataVo">
        SELECT DISTINCT tp.id projectId,tp.purchase_number projectNo,
        tp.project_name projectName,
        bs.package_number packageNumber,
        bs.package_name packageName,
        bs.submit_end_time  submitEndTime,
        (
        SELECT COUNT(*)
        FROM  t_apply_response_file arf
        LEFT JOIN t_apply_info ai on arf.apply_id = ai.apply_id
        WHERE bs.id = ai.sub_id
        and ai.is_delete= 0 and arf.is_delete = 0 and arf.file_type=2
        ) count,
        (case tp.is_international WHEN 1 THEN '是' ELSE '否' END) isInternational,
        (case bs.purchase_type WHEN 924100 THEN '是' ELSE '否' END) isGovernment
        FROM
        t_project tp
        LEFT JOIN t_project_bid_section bs on tp.id = bs.project_id
        LEFT JOIN t_project_section_bid_open bo on bs.id = bo.section_id
        left join t_approsh_address aa on  aa.code = tp.process_address
        LEFT JOIN t_project_entrust_user eu ON bs.project_id = eu.project_id
        LEFT JOIN r_user_department  rud ON eu.user_id = rud.user_id and rud.is_delete = 0 and rud.department_id = eu.department_id
        LEFT JOIN t_department td ON td.id = rud.department_id and td.is_delete = 0
        where  bs.is_delete = 0
        AND tp.is_delete = 0
        AND bs.submit_end_time_type = 1
        AND bs.submit_end_time IS NOT NULL
        AND eu.type = 1
        AND eu.is_delete = 0
        and bo.bid_open_online=1
        AND bs.submit_end_time_type = 1
        <if test="req.keyWords != null and req.keyWords != ''">
            AND (
            tp.purchase_number LIKE concat('%',#{req.keyWords},'%')
            or tp.project_name like concat('%',#{req.keyWords},'%')
            )
        </if>
        <if test="req.startDate !=null and req.startDate !=''">
            AND DATE_FORMAT( bs.submit_end_time, '%Y-%m-%d' ) &gt;= #{req.startDate}
        </if>
        <if test="req.endDate !=null and req.endDate !=''">
            AND DATE_FORMAT( bs.submit_end_time, '%Y-%m-%d' ) &lt;= #{req.endDate}
        </if>
        <if test='req.isInternational != null and req.isInternational == "否"'>
            and tp.is_international = 0
        </if>
        <if test='req.isInternational != null and req.isInternational == "是"'>
            and tp.is_international = 1
        </if>
        <if test="req.processAddress != null and req.processAddress !='' ">
            and aa.code = #{req.processAddress}
        </if>
        <if test='req.isGovernment != null and req.isGovernment == "否"'>
            and bs.purchase_type != 924100
        </if>
        <if test='req.isGovernment != null and req.isGovernment == "是"'>
            and bs.purchase_type = 924100
        </if>
        <if test="req.isProcess != null">
            and tp.is_process = #{req.isProcess}
        </if>
        <if test="req.deptId != null">
            and td.id = #{req.deptId}
        </if>
        ORDER BY bs.submit_end_time desc
    </select>

    <select id="getDeptIds" resultType="com.hzw.sunflower.entity.DepartProject">
    SELECT d.id departmentId,
        d.department_name departmentName
    FROM
        t_department d
    WHERE
        d.is_delete = 0
    </select>

    <select id="getRelatedProjectAddress" resultType="com.hzw.sunflower.controller.response.ProcessAddress">
        SELECT code,address
        FROM t_approsh_address
    </select>

    <select id="exportCaList" resultType="com.hzw.sunflower.controller.response.CaDataVo">
        SELECT DISTINCT tp.id projectId,tp.purchase_number projectNo,
        tp.project_name projectName,
        bs.package_number packageNumber,
        bs.package_name packageName,
        (
        SELECT COUNT(*)
        FROM  t_apply_response_file arf
        LEFT JOIN t_apply_info ai on arf.apply_id = ai.apply_id
        WHERE bs.id = ai.sub_id
        and ai.is_delete= 0 and arf.is_delete = 0 and arf.file_type=2
        ) count
        FROM
        t_project tp
        LEFT JOIN t_project_bid_section bs on tp.id = bs.project_id
        LEFT JOIN t_project_section_bid_open bo on bs.id = bo.section_id
        left join t_approsh_address aa on  aa.code = tp.process_address
        LEFT JOIN t_project_entrust_user eu ON bs.project_id = eu.project_id
        LEFT JOIN r_user_department  rud ON eu.user_id = rud.user_id and eu.department_id = rud.department_id
        LEFT JOIN t_department td ON td.id = rud.department_id
        where  bs.is_delete = 0
        AND tp.is_delete = 0
        AND bs.submit_end_time_type = 1
        AND bs.submit_end_time IS NOT NULL
        AND eu.type = 1
        AND eu.is_delete = 0
        and bo.bid_open_online=1
        AND bs.submit_end_time_type = 1
        <if test="req.keyWords != null and req.keyWords != ''">
            AND (
            tp.purchase_number LIKE concat('%',#{req.keyWords},'%')
            or tp.project_name like concat('%',#{req.keyWords},'%')
            )
        </if>
        <if test="req.startDate !=null and req.startDate !=''">
            AND DATE_FORMAT( bs.submit_end_time, '%Y-%m-%d' ) &gt;= #{req.startDate}
        </if>
        <if test="req.endDate !=null and req.endDate !=''">
            AND DATE_FORMAT( bs.submit_end_time, '%Y-%m-%d' ) &lt;= #{req.endDate}
        </if>
        <if test='req.isInternational != null and req.isInternational == "否"'>
            and tp.is_international = 0
        </if>
        <if test='req.isInternational != null and req.isInternational == "是"'>
            and tp.is_international = 1
        </if>
        <if test="req.processAddress != null and req.processAddress !='' ">
            and aa.address = #{req.processAddress}
        </if>
        <if test='req.isGovernment != null and req.isGovernment == "否"'>
            and bs.purchase_type != 924100
        </if>
        <if test='req.isGovernment != null and req.isGovernment == "是"'>
            and bs.purchase_type = 924100
        </if>
        <if test="req.isProcess != null">
            and tp.is_process = #{req.isProcess}
        </if>
        <if test="req.deptId != null">
            and td.id = #{req.deptId}
        </if>
        ORDER BY tp.id,
        bs.package_number
    </select>

    <select id="getCaList" resultType="com.hzw.sunflower.controller.response.CaVo">
    select
	sum(caUserTodaySum) caUserTodaySum,
	sum(caUserSum) caUserSum,
	sum(caLoginTodaySum) caLoginTodaySum,
	sum(encTodaySum) encTodaySum,
	sum(decTodaySum) decTodaySum,
	sum(caLoginSum) caLoginSum,
	sum(encSum) encSum,
	sum(decSum) decSum
    from (

	SELECT count(DISTINCT user_identity_id) caUserTodaySum,0 caUserSum,0 caLoginTodaySum,0 encTodaySum,0 decTodaySum,0 caLoginSum,0 encSum,0 decSum
	FROM t_user_ca uc
    WHERE uc.is_delete=0 and DATE_FORMAT( uc.created_time, "%Y-%m-%d" ) = CURDATE()

	UNION ALL

	SELECT 0 caUserTodaySum,count(DISTINCT user_identity_id) caUserSum,0 caLoginTodaySum,0 encTodaySum,0 decTodaySum,0 caLoginSum,0 encSum,0 decSum
	FROM t_user_ca uc
    WHERE uc.is_delete=0

	UNION ALL

	SELECT 0 caLoginTodaySum,0 caUserSum,count(1) caLoginTodaySum,0 encTodaySum,0 decTodaySum,0 caLoginSum,0 encSum,0 decSum
	FROM 	t_oper_log ol
    WHERE ol.title = '登录管理'
	AND ol.oper_url = '/sunflower/caLogin'
	AND DATE_FORMAT( ol.oper_time, "%Y-%m-%d" ) = CURDATE()

	UNION ALL

	SELECT 0 caUserTodaySum,0 caUserSum,0 caLoginTodaySum,count(1) encTodaySum,0 decTodaySum,0 caLoginSum,0 encSum,0 decSum
	FROM t_apply_response_file arf
    WHERE DATE_FORMAT( arf.created_time, "%Y-%m-%d" ) = CURDATE()	and arf.is_delete=0

	UNION ALL

	SELECT 0 caUserTodaySum,0 caUserSum,0 caLoginTodaySum,0 encTodaySum,count(file_oss_id_dec) decTodaySum,0 caLoginSum,0 encSum,0 decSum
	FROM t_apply_response_file arf
    WHERE DATE_FORMAT( arf.dec_time, "%Y-%m-%d" ) = CURDATE()	and arf.is_delete=0

	UNION ALL

	SELECT 0 caLoginTodaySum,0 caUserTodaySum,0 caLoginTodaySum,0 encTodaySum,0 decTodaySum,count(1) caLoginSum,0 encSum,0 decSum
	FROM 	t_oper_log ol
    WHERE ol.title = '登录管理'
	AND ol.oper_url = '/sunflower/caLogin'

	UNION ALL

	SELECT 0 caUserTodaySum,0 caUserSum,0 caLoginTodaySum,0 encTodaySum,0 decTodaySum,0 caLoginSum,count(arf.file_oss_id_enc) encSum,0 decSum
	FROM t_apply_response_file arf
    WHERE  arf.is_delete=0

	UNION ALL

	SELECT 0 caUserTodaySum,0 caUserSum,0 caLoginTodaySum,0 encTodaySum,0 decTodaySum,0 caLoginSum,0 encSum,count(arf.file_oss_id_dec) decSum
	FROM t_apply_response_file arf
    WHERE  arf.is_delete=0
    ) t
    </select>
    <select id="getCaBidOpeningList" resultType="com.hzw.sunflower.controller.response.CaBidOpeningVo" parameterType="integer">
        SELECT DISTINCT td.id,td.department_name departmentName,count(td.id) as departmentNum
        FROM
        t_project tp
        LEFT JOIN t_project_bid_section bs on tp.id = bs.project_id
        LEFT JOIN t_project_section_bid_open bo on bs.id = bo.section_id  and bo.is_delete = 0
        left join t_approsh_address aa on  aa.code = tp.process_address
        LEFT JOIN t_project_entrust_user eu ON bs.project_id = eu.project_id
        LEFT JOIN r_user_department  rud ON eu.user_id = rud.user_id  and rud.is_delete = 0 and eu.department_id = rud.department_id
        LEFT JOIN t_department td ON td.id = rud.department_id  and td.is_delete = 0
        where  bs.is_delete = 0
        AND tp.is_delete = 0
        AND bs.submit_end_time_type = 1
        AND bs.submit_end_time IS NOT NULL
        AND eu.type = 1
        AND eu.is_delete = 0
        and bo.bid_open_online=1
        AND bs.status >= 30
        AND bs.submit_end_time_type = 1
        <if test="req == 1">
            and  DATE_FORMAT( bs.submit_end_time, "%Y-%m-%d" ) = CURDATE()
        </if>
				GROUP BY td.id ORDER BY td.id
    </select>


</mapper>
