package com.hzw.sunflower.service;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @datetime 2022/11/17 14:24
 * @description: 线上开标service
 * @version: 1.0
 */
public interface CommonBidOpenService {

    /**
     * 获取标段线上开标配置
     * @param sectionId
     * @param bidRound
     * @return
     */
    Map<String, Object> getBidOpenBySectionId(String sectionId, Integer bidRound);

    /**
     * 根据报名id获取响应文件
     * @param applyId
     * @return
     */
    List<Map<String, Object>> getResponseFileByApplyId(Long applyId);

    /**
     * 根据评审id获取最新的评审报告签章文件
     * @param reviewId
     * @return
     */
    Map<String, Object> queryReviewReportLatest(Long reviewId);

    /**
     * 根据专家id和评审id查询评审报告签章情况
     * @param userId
     * @param reviewId
     * @return
     */
    Map<String, Object> queryReviewReportSign(Long userId, Long reviewId);
}
