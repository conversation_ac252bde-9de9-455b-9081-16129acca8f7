package com.hzw.ssm.expert.service;

import com.hzw.ssm.expert.dao.ExpertInfoMapper;
import com.hzw.ssm.expert.entity.ExpertInfoEntity;
import com.hzw.ssm.expert.entity.ExpertPracticeEntity;
import com.hzw.ssm.expert.entity.ExpertSupplementInfoEntity;
import com.hzw.ssm.fw.base.BaseService;
import com.hzw.ssm.fw.util.SysConstants;
import com.hzw.ssm.util.FileTypeCheck;
import org.apache.struts2.ServletActionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.List;

/**
 * 专家信息完善服务类
 * 
 * <AUTHOR> 2021-02-02
 *
 */
@Service
public class ExpertInfoCompleteService extends BaseService {
	@Autowired
	private ExpertInfoMapper expertInfoMapper;

	/**
	 * 分页
	 * 
	 * @param exertEntity
	 * @return
	 */
	public List<ExpertSupplementInfoEntity> queryListexpert(ExpertSupplementInfoEntity exertEntity) {
		List<ExpertSupplementInfoEntity> list = new ArrayList<ExpertSupplementInfoEntity>();

		if ("00".equals(exertEntity.getIsCertificateInfo()) && "00".equals(exertEntity.getIsBasicInfo())) {
			exertEntity.setIsPerfect("1000");
		}
		if ("00".equals(exertEntity.getIsBasicInfo())) {
			exertEntity.setIsBasicInfo(null);
		}
		if ("00".equals(exertEntity.getIsCertificateInfo())) {
			exertEntity.setIsCertificateInfo(null);
		}
		if ("2000".equals(exertEntity.getExpertGrade())) {
			exertEntity.setExpertGrade(null);
		}
		list = expertInfoMapper.queryPageExpertInfoCompleteList(exertEntity);
		if (null == exertEntity.getExpertGrade()) {
			exertEntity.setExpertGrade("2000");
		}
		return list;
	}

	/**
	 * 分页
	 * 
	 * @param exertEntity
	 * @return
	 */
	public List<ExpertSupplementInfoEntity> queryListexpertInfo(ExpertSupplementInfoEntity exertEntity) {
		List<ExpertSupplementInfoEntity> list = new ArrayList<ExpertSupplementInfoEntity>();

		if ("00".equals(exertEntity.getIsCertificateInfo()) && "00".equals(exertEntity.getIsBasicInfo())) {
			exertEntity.setIsPerfect("1000");
		}
		if ("00".equals(exertEntity.getIsBasicInfo())) {
			exertEntity.setIsBasicInfo(null);
		}
		if ("00".equals(exertEntity.getIsCertificateInfo())) {
			exertEntity.setIsCertificateInfo(null);
		}
		if ("2000".equals(exertEntity.getExpertGrade())) {
			exertEntity.setExpertGrade(null);
		}
		list = expertInfoMapper.queryExpertInfoCompleteList(exertEntity);
		if (null == exertEntity.getExpertGrade()) {
			exertEntity.setExpertGrade("2000");
		}
		return list;
	}

	/**
	 * 
	 * 函数功能描述：设置专家信息变为已完善
	 * 
	 * @param exertEntity
	 */
	@Transactional
	public void expertCompelete(ExpertSupplementInfoEntity exertEntity) {
		exertEntity.setIsBasicInfo("1");
		exertEntity.setIsCertificateInfo("1");
		expertInfoMapper.updateExpertCompelete(exertEntity);
	}

	/**
	 * 
	 * 函数功能描述：专家设置备注
	 * 
	 * @param exertEntity
	 */
	@Transactional
	public void expertRemark(ExpertSupplementInfoEntity exertEntity) {
		expertInfoMapper.updateExpertRemark(exertEntity);
	}

	/**
	 * 文件上传
	 * 
	 * @param expertInfoEntity
	 * @param practiceList
	 * @param idFile
	 * @param idFileFileName
	 * @param certificateFile
	 * @param certificateFileFileName
	 * @param practiceFile1
	 * @param practiceFile1FileName
	 * @param practiceFile2
	 * @param practiceFile2FileName
	 * @param practiceFile3
	 * @param practiceFile3FileName
	 * @param practiceFile4
	 * @param practiceFile4FileName
	 * @param practiceFile5
	 * @param practiceFile5FileName
	 */
	public void uploadFile(ExpertInfoEntity expertInfoEntity, List<ExpertPracticeEntity> practiceList, File idFile,
			String idFileFileName, File certificateFile, String certificateFileFileName, File photoFileid,
			String photoFileidFileName, File technicalFiled, String technicalFiledFileName, File practiceFile1,
			String practiceFile1FileName, File practiceFile2, String practiceFile2FileName, File practiceFile3,
			String practiceFile3FileName, File ICBackFile,String ICBackName) {
		String rootPath = ServletActionContext.getServletContext().getRealPath("");

		String savePath = rootPath + "/" + SysConstants.EXPERT_FILE_PATH_ROOT + expertInfoEntity.getUser_id();

		File file1 = new File(savePath);

		if (!file1.exists()) {
			file1.mkdirs();
		}

		if (idFile != null) {
			String newName = FileTypeCheck.fileTypeCheck(idFileFileName);
			String filePath = savePath + "/" + newName;

			File file = new File(filePath);
			boolean result = copyFile(idFile, file);
			if (result) {
				expertInfoEntity.setId_fileid(
						SysConstants.EXPERT_FILE_PATH_ROOT + expertInfoEntity.getUser_id() + "/" + newName);
				expertInfoEntity.setOld_id(idFileFileName);
			}
		}
		if (ICBackFile != null) {
			String newName = FileTypeCheck.fileTypeCheck(ICBackName);
			String filePath = savePath + "/" + newName;

			File file = new File(filePath);
			boolean result = copyFile(ICBackFile, file);
			if (result) {
				expertInfoEntity.setICBackFileId(
						SysConstants.EXPERT_FILE_PATH_ROOT + expertInfoEntity.getUser_id() + "/" + newName);
				expertInfoEntity.setICBackName(ICBackName);
			}
		}

		if (certificateFile != null) {
			String newName = FileTypeCheck.fileTypeCheck(certificateFileFileName);
			String filePath = savePath + "/" + newName;
			File file = new File(filePath);
			boolean result = copyFile(certificateFile, file);
			if (result) {
				expertInfoEntity.setCertificate_fileid(
						SysConstants.EXPERT_FILE_PATH_ROOT + expertInfoEntity.getUser_id() + "/" + newName);
				expertInfoEntity.setOld_certificate(certificateFileFileName);
			}
		}

		if (photoFileid != null) {
			String newName = FileTypeCheck.fileTypeCheck(photoFileidFileName);
			String filePath = savePath + "/" + newName;
			File file = new File(filePath);
			boolean result = copyFile(photoFileid, file);
			if (result) {
				expertInfoEntity.setPhoto_fileid(
						SysConstants.EXPERT_FILE_PATH_ROOT + expertInfoEntity.getUser_id() + "/" + newName);
				expertInfoEntity.setOld_photo(photoFileidFileName);
			}
		}

		if (technicalFiled != null) {
			String newName = FileTypeCheck.fileTypeCheck(technicalFiledFileName);
			String filePath = savePath + "/" + newName;
			File file = new File(filePath);
			boolean result = copyFile(technicalFiled, file);
			if (result) {
				expertInfoEntity.setTechnical_filed(
						SysConstants.EXPERT_FILE_PATH_ROOT + expertInfoEntity.getUser_id() + "/" + newName);
				expertInfoEntity.setOld_technical(technicalFiledFileName);
			}
		}

		if (practiceFile1 != null) {
			String newName = FileTypeCheck.fileTypeCheck(practiceFile1FileName);
			String filePath = savePath + "/" + newName;
			File file = new File(filePath);
			boolean result = copyFile(practiceFile1, file);
			if (result) {
				practiceList.get(0).setCertificate_fileid(
						SysConstants.EXPERT_FILE_PATH_ROOT + expertInfoEntity.getUser_id() + "/" + newName);
				practiceList.get(0).setOld_certificate(practiceFile1FileName);
			}
		} else if (practiceList.get(0).getCertificate().equals("")) {
			File file = new File(rootPath + "/" + practiceList.get(0).getCertificate_fileid());
			if (file.exists()) {
				file.delete();
				practiceList.get(0).setCertificate_fileid("");
			}
		}

		if (practiceFile2 != null) {
			String newName = FileTypeCheck.fileTypeCheck(practiceFile2FileName);
			String filePath = savePath + "/" + newName;
			File file = new File(filePath);
			boolean result = copyFile(practiceFile2, file);
			if (result) {
				practiceList.get(1).setCertificate_fileid(
						SysConstants.EXPERT_FILE_PATH_ROOT + expertInfoEntity.getUser_id() + "/" + newName);
				practiceList.get(1).setOld_certificate(practiceFile2FileName);
			}
		} else if (practiceList.get(1).getCertificate().equals("")) {
			File file = new File(rootPath + "/" + practiceList.get(1).getCertificate_fileid());
			if (file.exists()) {
				file.delete();
				practiceList.get(1).setCertificate_fileid("");
			}
		}

		if (practiceFile3 != null) {
			String newName = FileTypeCheck.fileTypeCheck(practiceFile3FileName);
			String filePath = savePath + "/" + newName;
			File file = new File(filePath);
			boolean result = copyFile(practiceFile3, file);
			if (result) {
				practiceList.get(2).setCertificate_fileid(
						SysConstants.EXPERT_FILE_PATH_ROOT + expertInfoEntity.getUser_id() + "/" + newName);
				practiceList.get(2).setOld_certificate(practiceFile3FileName);
			}
		} else if (practiceList.get(2).getCertificate().equals("")) {
			File file = new File(rootPath + "/" + practiceList.get(2).getCertificate_fileid());
			if (file.exists()) {
				file.delete();
				practiceList.get(2).setCertificate_fileid("");
			}
		}

	}

	/**
	 * 文件复制处理
	 * 
	 * @param src
	 * @param dst
	 * @return
	 */
	public synchronized static boolean copyFile(File src, File dst) {
		if (src == null || dst == null) {
			return false;
		}
		int BUFFER_SIZE = 16 * 1024;
		InputStream ins = null;
		OutputStream os = null;
		try {
			ins = new BufferedInputStream(new FileInputStream(src), BUFFER_SIZE);
			os = new BufferedOutputStream(new FileOutputStream(dst), BUFFER_SIZE);
			byte[] buffer = new byte[BUFFER_SIZE];
			int len = 0;
			while ((len = ins.read(buffer)) > 0) {
				os.write(buffer, 0, len);
			}
			os.flush();
		} catch (FileNotFoundException e) {
			e.printStackTrace();
			return false;
		} catch (IOException e) {
			e.printStackTrace();
			return false;
		} catch (Exception e) {
			e.printStackTrace();
			return false;
		} finally {
			try {
				if (ins != null) {
					ins.close();
				}
				if (os != null) {
					os.close();
				}
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
		return true;
	}

	/**
	 * 
	 * 函数功能描述：删除文件回调函数
	 * @param expertInfoEntity
	 */
	@Transactional
	public void deleteFiles(ExpertInfoEntity expertInfoEntity){
		//修改文件
		expertInfoMapper.updateFile(expertInfoEntity);
		//查询最新信息
		ExpertInfoEntity entity=expertInfoMapper.getExpertInfoByUserIdOrId(expertInfoEntity);
		//判断信息是否齐全
		
		if(entity.getOld_technical()!=null&&entity.getOld_id()!=null&&entity.getOld_certificate()!=null&&entity.getICBackName()!=null){
			entity.setIsCertificateInfo("1");
		}else{
			entity.setIsCertificateInfo("0");
		}
		expertInfoMapper.updateExpertFileStatus(entity);
	}
}
