<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzw.sunflower.dao.BidDocCompareMapper">

    <!-- 获取报名供应商列表 -->
    <select id="queryApplyInfo" resultType="com.hzw.sunflower.entity.BidFileSimilarity">
        select distinct
            tbfs.id as id,
            tai.sub_id as sectionId,
            tai.company_id as companyId,
            tai.bid_round as bidRound,
            if(tbtr.file_ids IS NOT NULL AND tbtr.file_ids  <![CDATA[ <> ]]> '',1,null) as analysisStatus
        from t_apply_info tai
        left join (select * from t_bid_tenderer_record where is_delete = 0) tbtr on tai.sub_id = tbtr.section_id and tai.company_id = tbtr.tenderer_id and tai.bid_round = tbtr.bid_round
        left join t_bid_file_similarity tbfs on tai.sub_id = tbfs.section_id and tai.company_id = tbfs.company_id and tai.bid_round = tbfs.bid_round
        where tai.apply_status = 4
        and tai.sub_id = #{sectionId}
        and tai.bid_round = #{bidRound}
        and tai.is_delete = 0
    </select>

    <!-- 获取全流程线上供应商投标文件 -->
    <select id="queryApplyCompany" resultType="com.hzw.sunflower.vo.BidDocCompareVo">
        select
            t.sectionId,
            t.companyId,
            t.bidRound,
            max(t.ossFileKey) as ossFileKey
        from(select
                 tai.sub_id as sectionId,
                 tai.bid_round as bidRound,
                 tai.company_id as companyId,
                 tof.id as ossId,
                 tof.oss_file_key as ossFileKey
             from
                 t_apply_info tai
                 left join (select apply_id,file_oss_id_dec,is_delete from t_apply_response_file where file_type = 2) tarf on
                 tai.apply_id = tarf.apply_id
                 left join (select id,oss_file_key from t_oss_file where (RIGHT(oss_file_key , 4) = '.pdf' or RIGHT(oss_file_key , 5) = '.docx') and is_delete = 0) tof on
                 tarf.file_oss_id_dec = tof.id
             where tai.apply_status = 4 and tai.is_delete = 0 and tarf.is_delete = 0) t
        where t.sectionId = #{sectionId} and t.bidRound = #{bidRound}
        group by t.sectionId,t.bidRound,t.companyId
    </select>

    <!-- 获取开评标备案供应商 -->
    <select id="queryTenderRecord" resultType="com.hzw.sunflower.vo.BidDocCompareVo">
        select
            tbtr.section_id as sectionId,
            tbtr.tenderer_id as companyId,
            tbtr.bid_round as bidRound,
            tbtr.file_ids as ossIds
        from t_bid_tenderer_record tbtr
        where tbtr.section_id = #{sectionId} and tbtr.bid_round = #{bidRound} and is_delete = 0
    </select>

    <!-- 获取开评标备案供应商投标文件 -->
    <select id="queryTenderRecordFile" resultType="java.lang.String">
        select
            tof.oss_file_key as ossFileKey
        from t_oss_file tof
        where is_delete = 0 and (RIGHT(oss_file_key , 5) = '.docx' or RIGHT(oss_file_key , 4) = '.pdf')
        AND tof.id IN
        <foreach collection="ossIdList" item="item" open="(" separator="," close=")" index="">
            #{item}
        </foreach>
        limit 1
    </select>

    <select id="queryProjectOperationFlow" resultType="java.lang.Integer">
        select operation_flow from t_project where id = #{projectId} and is_delete = 0
    </select>

</mapper>
