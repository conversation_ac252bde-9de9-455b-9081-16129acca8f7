package com.hzw.sunflower.dao;

import com.hzw.sunflower.dto.BondPayInfoDto;
import com.hzw.sunflower.entity.BondPayInfo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 保证金银行支付信息 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-26
 */
@Mapper
public interface BondPayInfoMapper extends BaseMapper<BondPayInfo> {

    /**
     * 获取待刷新状态的数据
     * @return
     */
    List<BondPayInfoDto> getRefreshData(@Param("refundId") Long refundId);

    /**
     * 获取待刷新状态的批量数据
     * @return
     */
    List<BondPayInfoDto> getBatchRefreshData(@Param("list") List<Long> list);
}
