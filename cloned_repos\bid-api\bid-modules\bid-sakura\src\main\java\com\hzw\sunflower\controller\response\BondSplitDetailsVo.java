package com.hzw.sunflower.controller.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2022/7/27 11:31
 * @description：标段流水关联详情返回实体类
 * @modified By：`
 * @version: 1.0
 */
@ApiModel(description = "标包关联情况返回实体类")
@Data
public class BondSplitDetailsVo implements Serializable {

    @ApiModelProperty("关联收款流水")
    List<ProjectWaterVo> projectWaters;

    @ApiModelProperty("同时关联标包")
    List<BondRelationWaterVo> sectionWaters;

}
