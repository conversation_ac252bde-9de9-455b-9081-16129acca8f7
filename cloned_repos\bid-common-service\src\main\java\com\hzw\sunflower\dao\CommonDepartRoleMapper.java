package com.hzw.sunflower.dao;

import com.hzw.sunflower.controller.response.CommonUserRoleRelationVO;
import com.hzw.sunflower.dto.CommonDeptRolesDTO;
import org.apache.ibatis.annotations.Param;
import com.hzw.sunflower.entity.Role;
import java.util.List;

/**
 * @ClassName:CommonDepartRoleMapper
 * @Auther: lijinxin
 * @Description:
 * @Date: 2024/7/23 15:23
 * @Version: v1.0
 */
public interface CommonDepartRoleMapper {


    /**
     * 根据部门id查询角色信息
     * @param deptId
     * @return
     */
    List<CommonDeptRolesDTO> getDepartRoleInfoById(@Param("userId")Long userId, @Param("deptId") Long deptId);

    List<CommonUserRoleRelationVO> getUserRoleByIdentity(@Param("userId") Long userId, @Param("userIdentity") Integer userIdentity);

    /**
     * 获取role集合
     * @param roleids
     * @return
     */
    List<Role>  getRoleList(@Param("roleids") List<Long> roleids);
}
