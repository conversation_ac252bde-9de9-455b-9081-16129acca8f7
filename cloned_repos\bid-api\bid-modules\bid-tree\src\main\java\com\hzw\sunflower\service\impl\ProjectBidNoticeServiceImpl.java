package com.hzw.sunflower.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.text.StrBuilder;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzw.sunflower.common.CommonConstants;
import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.constant.NoticeProgressConstants;
import com.hzw.sunflower.constant.constantenum.*;
import com.hzw.sunflower.controller.request.*;
import com.hzw.sunflower.controller.response.NoticeChangeRecordVO;
import com.hzw.sunflower.controller.response.NoticeTimeVO;
import com.hzw.sunflower.controller.response.ProjectBidNoticeListVO;
import com.hzw.sunflower.controller.response.ProjectBidNoticeVO;
import com.hzw.sunflower.dao.NoticePackageRMapper;
import com.hzw.sunflower.dao.ProjectBidNoticeMapper;
import com.hzw.sunflower.dto.*;
import com.hzw.sunflower.entity.*;
import com.hzw.sunflower.entity.condition.QueryNoticeListCondition;
import com.hzw.sunflower.exception.ExceptionEnum;
import com.hzw.sunflower.exception.SunFlowerException;
import com.hzw.sunflower.service.*;
import com.hzw.sunflower.util.BeanListUtil;
import com.hzw.sunflower.util.RequestUtil;
import com.hzw.sunflower.utils.CompareObjectUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
* TProjectBidNoticeService接口实现
*
* <AUTHOR>
* @version 1.0.0 2021/07/05
*/
@Service
public class ProjectBidNoticeServiceImpl extends ServiceImpl<ProjectBidNoticeMapper, ProjectBidNotice> implements ProjectBidNoticeService {

    @Autowired
    private ProjectBidSectionService projectBidSectionService;

    @Autowired
    private NoticePackageRService noticePackageRService;

    @Autowired
    private RelevancyAnnexService annexService;

    @Autowired
    private NoticeMediaService mediaService;

    @Autowired
    private NoticeMediaRService mediaRService;

    @Autowired
    private ProjectService projectService;
    @Autowired
    private ProjectBidNoticeService tProjectBidNoticeService;
    @Autowired
    private RequestToDTOService requestToDTOService;
    @Autowired
    private ApprovalOpinionService approvalOpinionService;
    @Autowired
    private AllMediaService allMediaService;
    @Autowired
    private OssFileService ossFileService;
    @Autowired
    private RabbitMqService rabbitMqService;
    @Autowired
    private NoticePackageRMapper noticePackageRMapper;
    @Autowired
    private NoticeChangeRecordService noticeChangeRecordService;
    @Autowired
    private CommonSectionService commonSectionService;

    @Autowired
    private ProjectBidNoticeMapper projectBidNoticeMapper;

    @Autowired
    private PendingItemService pendingItemService;

    /**
    * 根据条件分页查询列表
    *
    * @param req 查询请求
    * @return ProjectBidNoticeListVO
    */
    @Override
    public ProjectBidNoticeListVO findNoticeListPage(QueryPublicNoticeListREQ req) {

        QueryNoticeListCondition condition = new QueryNoticeListCondition(req);
        // 符合条件的分页查询结果
        IPage<SimpleBidNoticeDTO> noticeIPage = this.baseMapper.queryBidNoticeListPage(condition.customizeBuildPage(), condition);

        // 查询包段简要信息列表
        List<PackageInfoDTO> packageInfoDTOList = noticePackageRMapper.selectNoticePackageInfoListByProjectId(req.getProjectId());
        // 初始化返回值
        ProjectBidNoticeListVO vo = new ProjectBidNoticeListVO()
                .setCurrentPage(noticeIPage.getCurrent())
                .setPageSize(noticeIPage.getSize())
                .setBidNotices(noticeIPage.getRecords())
                .setTotalPackageNumber(packageInfoDTOList.size())
                .setTotalPage(noticeIPage.getPages())
                .setTotalNoticeNumber(noticeIPage.getTotal())
                .setCanCreate(false);

        noticeIPage.getRecords().forEach((item) -> {
            StrBuilder packageNumber = new StrBuilder("");
            List<PackageInfoDTO> packageInfoList = new ArrayList<>();
            for (PackageInfoDTO dto : packageInfoDTOList) {
                if(dto.getNoticeId() == null){
                    vo.setCanCreate(true);
                    continue;
                }else if(!dto.getNoticeId().equals(item.getNoticeId())){
                    continue;
                }
                else{
                    packageNumber.append(dto.getPackageNumber() + "、");
                    packageInfoList.add(dto);
                }
            }
            if(packageNumber.length() < 2){
                item.setPackageNumber(packageNumber.toString());
            }else{
                item.setPackageNumber(packageNumber.subString(0,packageNumber.length() - 1));
            }
            item.setPackageInfo(packageInfoList);
        });
        return vo;
    }

    /**
     * 根据ID查询公告信息
     *
     * @param req 通用请求实体
     * @return 公告完整信息
     */
    @Override
    public Result<ProjectBidNoticeVO> queryEditNoticeInfo(CommonNoticeREQ req) {
        // 获取基本公告信息，判断公告是否可用
        ProjectBidNotice notice = getById(req.getNoticeId());
        if(notice == null){
            throw new SunFlowerException(ExceptionEnum.NOTICE_NOT_FOUND,ExceptionEnum.NOTICE_NOT_FOUND.getMessage());
        }
        // 获取公告附件信息
        List<AnnexDTO> noticeAnnexs = annexService.findNoticeAnnexsByNoticeId(req.getNoticeId());
        // 获取公告关联的媒体信息
//        List<NoticeMediaDTO> noticeMediaDTOS = mediaService.findNoticeMediaWithAnnex(req.getNoticeId());
        List<MediaDTO> mediaDTOS = allMediaService.queryMedia(req.getNoticeId(), MediaTypeEnum.TENDER_BULLETIN.getCode());
        // 不为空，则进行排序
//        if(CollectionUtil.isNotEmpty(noticeMediaDTOS)){
//            noticeMediaDTOS.sort((a,b) -> {
//                return (a.getUseTimes() <= b.getUseTimes()) ? 1 : -1;
//            });
//        }
        // 获取项目关联包段信息
        List<PackageInfoDTO> packages = noticePackageRService.findPackageInfoByProjectIdAndRound(req.getProjectId(),notice.getBidRound());
        List<Long> noticePackageIds = noticePackageRService.findNoticePackageIdsByNoticeId(notice.getId());
        String purchaseModeName = "";
        if(CollUtil.isNotEmpty(noticePackageIds)){
            purchaseModeName = projectBidSectionService.getById(noticePackageIds.get(0)).getPurchaseModeName();
        }

        ProjectBidNoticeVO vo = new ProjectBidNoticeVO();
        // 同名属性赋值
        BeanUtils.copyProperties(notice,vo);
              vo.setNoticeId(notice.getId())
                .setAnnexes(noticeAnnexs)
                .setMedias(mediaDTOS)
                .setPackageInfo(packages)
                .setPurchaseModeName(purchaseModeName);

        QueryWrapper<NoticePackageR> wrapper = new QueryWrapper<>();
        wrapper
                .eq("notice_id",notice.getId())
                .eq("is_delete", CommonConstants.NO);
        List<NoticePackageR> list = noticePackageRService.list(wrapper);

        if(CollectionUtil.isNotEmpty(list)){
            vo.setNoticeType(list.get(0).getState());
        }

        // 确定项目是否划分包段
        Project project = projectService.getProjectById(req.getProjectId());
        vo.setHasPackage((project.getPackageSegmentStatus().equals(1) ? true : false));

        return Result.ok(vo);
    }

    @Override
    public Result<ProjectBidNoticeVO> queryNoticeInfo(CommonNoticeREQ req) {
        // 获取基本公告信息，判断公告是否可用
        ProjectBidNotice notice = getById(req.getNoticeId());
        if(notice == null){
            throw new SunFlowerException(ExceptionEnum.NOTICE_NOT_FOUND,ExceptionEnum.NOTICE_NOT_FOUND.getMessage());
        }
        // 确定项目是否划分包段
        Project project = projectService.getProjectById(notice.getProjectId());
        // 获取公告附件信息
        List<AnnexDTO> noticeAnnexs = annexService.findNoticeAnnexsByNoticeIdOne(req.getNoticeId());
        // 获取公告关联的媒体信息
        List<MediaDTO> mediaDTOS = allMediaService.queryMedia(req.getNoticeId(), MediaTypeEnum.TENDER_BULLETIN.getCode());
        // 获取项目关联包段信息
        List<PackageInfoDTO> packages = noticePackageRService.findPackageInfoByNoticeId(req.getNoticeId());
        // 采购方式中文名称
        String purchaseModeName = "";
        if(CollUtil.isNotEmpty(packages)){
            purchaseModeName = packages.get(0).getPurchaseModeName();
        }
        ProjectBidNoticeVO projectBidNoticeVO = new ProjectBidNoticeVO();
        // 同名属性赋值
        BeanUtils.copyProperties(notice,projectBidNoticeVO);
        projectBidNoticeVO
                .setNoticeId(notice.getId())
                .setAnnexes(noticeAnnexs)
                .setMedias(mediaDTOS)
                .setPackageInfo(packages)
                .setPurchaseModeName(purchaseModeName);

        projectBidNoticeVO.setHasPackage((project.getPackageSegmentStatus().equals(1) ? true : false));
        if(NoticeProgressConstants.CAN_EDIT.contains(notice.getNoticeProgress())){
            NoticeChangeRecordVO lastedChangeRecord = noticeChangeRecordService.findLastedChangeRecordByNoticeId(notice.getId());
            ProjectBidNoticeVO temp=new ProjectBidNoticeVO();
            if(lastedChangeRecord!=null){
                BeanUtils.copyProperties(lastedChangeRecord,temp);
                temp.setAnnexes(lastedChangeRecord.getAnnexes());
            }else{
                BeanUtils.copyProperties(projectBidNoticeVO,temp);
            }
            ProjectBidNoticeVO temp1=new ProjectBidNoticeVO();
            BeanUtils.copyProperties(projectBidNoticeVO,temp1);
            List<MediaDTO> temp1mlist=new ArrayList<>();
            for (int i = 0; i < temp1.getMedias().size(); i++) {
                MediaDTO item=new MediaDTO();
                item.setId(null);
                item.setIsSelect(null);
                item.setIsTick(null);
                item.setMediaFileId(null);
                item.setNewAdd(null);
                item.setMediaName(temp1.getMedias().get(i).getMediaName());
                item.setMediaUrl(temp1.getMedias().get(i).getMediaUrl());
                temp1mlist.add(item);
            }
            temp1.setMedias(temp1mlist);
            List<MediaDTO> tempmlist=new ArrayList<>();
            for (int i = 0; i < temp.getMedias().size(); i++) {
                MediaDTO item=new MediaDTO();
                item.setId(null);
                item.setIsSelect(null);
                item.setIsTick(null);
                item.setMediaFileId(null);
                item.setNewAdd(null);
                item.setMediaName(temp.getMedias().get(i).getMediaName());
                item.setMediaUrl(temp.getMedias().get(i).getMediaUrl());
                tempmlist.add(item);
            }
            temp.setMedias(tempmlist);

            projectBidNoticeVO.setFlags(CompareObjectUtil.compareObject(temp1,temp));
        }else{
            // 不可编辑状态数据对比
            NoticeAuditReq noticeAuditReq=new NoticeAuditReq();
            noticeAuditReq.setNoticeId(notice.getId());
            noticeAuditReq.setProjectId(notice.getProjectId());
            Result<List<NoticeChangeRecordVO>> changeRecordList = noticeChangeRecordService.findChangeRecordListByNoticeId(noticeAuditReq);
            if (changeRecordList!=null&&changeRecordList.getData()!=null&&changeRecordList.getData().size()>0){
                projectBidNoticeVO.setFlags(changeRecordList.getData().get(0).getFlags());
            }else {
                projectBidNoticeVO.setFlags(CompareObjectUtil.compareObject(projectBidNoticeVO,projectBidNoticeVO));
            }
        }
        return Result.ok(projectBidNoticeVO);
    }

    /**
     * 获取当前用户
     */
    private JwtUser getJwtUser(){
        // 获取当前用户
        return (JwtUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
    }
   /**
     * 新增公告
     *
     * @param noticeDTO 新增公告请求实体
     * @return 提示信息，true代表成功，其他失败；
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> insertNoticeInfo(UpdateNoticeDTO noticeDTO){

        ProjectBidNotice notice = noticeDTO.getNotice();

        List<Long> secionIds = noticeDTO.getInsertPackages().stream().map(NoticePackageR::getSectionId).collect(Collectors.toList());
        // 判断是否有标段已经上传公告
        Integer count = this.getBaseMapper().checkNoticeCount(secionIds, notice.getBidRound());
        if(count > 0 ){
            throw new SunFlowerException(ExceptionEnum.NOTICE_REPEAT,ExceptionEnum.NOTICE_REPEAT.getMessage());
        }

        // 上传公告
        try {
            if (StringUtils.isEmpty(notice.getNoticeContent())){
                notice.setNoticeContent(" ");
            }
            //long noticeOsskey = ossFileService.editor2PDF(notice.getNoticeContent(),notice.getNoticeName()+".pdf",notice.getNoticeName());
            //notice.setNoticeOssKey(noticeOsskey);
        } catch (Exception e) {
            e.printStackTrace();
            throw new SunFlowerException(ExceptionEnum.SAVE_NOTICE_FAILED,ExceptionEnum.SAVE_NOTICE_FAILED.getMessage());
        }
        // 保存公告
        save(notice);

        noticeDTO.getInsertPackages().forEach((i) -> {
            i.setNoticeId(notice.getId());
        });
        // 保存包段关联信息
        if(CollectionUtil.isNotEmpty(noticeDTO.getInsertPackages())){
            noticePackageRService.saveBatch(noticeDTO.getInsertPackages());
        }
        // 如果有附件关联
        List<RelevancyAnnex> insertAnnexes = noticeDTO.getInsertAnnexes();
        if(CollectionUtil.isNotEmpty(insertAnnexes)){
            insertAnnexes.forEach((i) -> {
                i.setBusId(notice.getId());
            });
            annexService.saveBatch(insertAnnexes);
        }
        if (noticeDTO.getMedias()!=null&&noticeDTO.getMedias().size()>0){
            addOrUpdateMedia(noticeDTO.getMedias(),notice.getId(),noticeDTO.getNotice().getProjectId(), getJwtUser().getUserId() );
            //087在建立标段的时候增加保密不保密项目，供应商能否搜索根据保密字段来，此处取消
           /* for (NoticeMediaREQ media : noticeDTO.getMedias()) {
                if (media.getMediaName().equals(MediaDefaultEnum.MEDIA_JSTCC.getCode())
                        &&media.getMediaUrl().contains(MediaDefaultEnum.MEDIA_JSTCC.getValue())){
                    if (media.getIsSelect().equals(CommonConstants.NO)){
                        //供应商 1 不可搜索 0 可以搜索
                        commonSectionService.updateSectionCanSearchByIds(noticeDTO.getUpdatePackageIds(),CanSearchEnum.NOT_CAN.getType());
                    }else{
                        commonSectionService.updateSectionCanSearchByIds(noticeDTO.getUpdatePackageIds(),CanSearchEnum.CAN.getType());
                    }
                }
            }*/
        }

        // 修改标段状态
        if(CollectionUtil.isNotEmpty(noticeDTO.getUpdatePackageIds())){
            commonSectionService.updateSectionStatus(noticeDTO.getUpdatePackageIds(),PackageStatusEnum.EDIT_BY_NOTICE.getValue());
        }

       // 同步项目状态
        tProjectBidNoticeService.syncProjectStatus(noticeDTO.getNotice().getProjectId());

        return Result.ok(true);
    }

    /**
     * 更新公告
     *
     * @param dto 更新公告DTO
     * @return 提示信息，true代表成功，其他失败；
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> updateNoticeInfo(UpdateNoticeDTO dto){

        ProjectBidNotice notice = dto.getNotice();
        Integer progress = notice.getNoticeProgress();
        if(NoticeProgressConstants.CAN_EDIT.contains(progress)){

            if (dto.getMedias()!=null&&dto.getMedias().size()>0){
                addOrUpdateMedia(dto.getMedias(),notice.getId(),dto.getNotice().getProjectId(), getJwtUser().getUserId() );
                //087在建立标段的时候增加保密不保密项目，供应商能否搜索根据保密字段来，此处取消
               /* for (NoticeMediaREQ media : dto.getMedias()) {
                    if (media.getMediaName().equals(MediaDefaultEnum.MEDIA_JSTCC.getCode())
                            &&media.getMediaUrl().contains(MediaDefaultEnum.MEDIA_JSTCC.getValue())){
                        if (media.getIsSelect().equals(CommonConstants.NO)){
                            //供应商 1 不可搜索 0 可以搜索
                            commonSectionService.updateSectionCanSearchByIds(dto.getUpdatePackageIds(),CanSearchEnum.NOT_CAN.getType());
                        }else{
                            commonSectionService.updateSectionCanSearchByIds(dto.getUpdatePackageIds(),CanSearchEnum.CAN.getType());
                        }
                    }
                }*/
            }

            if(CollUtil.isNotEmpty(dto.getDeletePackageIds())){
                //更新标段状态
                if(!commonSectionService.updateSectionStatus(dto.getDeletePackageIds(),PackageStatusEnum.TENDER_INVITATION.getValue())){
                    throw new SunFlowerException(ExceptionEnum.UPDATE_NOTICE_FAILED,ExceptionEnum.UPDATE_NOTICE_FAILED.getMessage());
                }
            }
            if(CollUtil.isNotEmpty(dto.getUpdatePackageIds())){
                //更新标段状态
                if(!commonSectionService.updateSectionStatus(dto.getUpdatePackageIds(),PackageStatusEnum.EDIT_BY_NOTICE.getValue())){
                    throw new SunFlowerException(ExceptionEnum.UPDATE_NOTICE_FAILED,ExceptionEnum.UPDATE_NOTICE_FAILED.getMessage());
                }
            }
            // 上传公告
            try {
                if (StringUtils.isEmpty(notice.getNoticeContent())){
                    notice.setNoticeContent(" ");
                }
                //long noticeOsskey = ossFileService.editor2PDF(notice.getNoticeContent(),notice.getNoticeName()+".pdf",notice.getNoticeName());
                //notice.setNoticeOssKey(noticeOsskey);
            } catch (Exception e) {
                e.printStackTrace();
                throw new SunFlowerException(ExceptionEnum.UPDATE_NOTICE_FAILED,ExceptionEnum.UPDATE_NOTICE_FAILED.getMessage());
            }
            if(!this.updateById(notice)){
                throw new SunFlowerException(ExceptionEnum.UPDATE_NOTICE_FAILED,ExceptionEnum.UPDATE_NOTICE_FAILED.getMessage());
            }

            if(CollUtil.isNotEmpty(dto.getDeletePackageIds())){
                QueryWrapper<NoticePackageR> packageRQueryWrapper = new QueryWrapper<>();
                packageRQueryWrapper
                        .eq("notice_id",dto.getNotice().getId())
                        .in("section_id",dto.getDeletePackageIds())
                        .eq("is_delete",CommonConstants.NO);
                noticePackageRService.remove(packageRQueryWrapper);
            }
            if(CollUtil.isNotEmpty(dto.getInsertPackages())){
                if(!noticePackageRService.saveBatch(dto.getInsertPackages())){
                    throw new SunFlowerException(ExceptionEnum.UPDATE_NOTICE_FAILED,ExceptionEnum.UPDATE_NOTICE_FAILED.getMessage());
                }
            }

            // 添加附件
            QueryWrapper<RelevancyAnnex> annexQueryWrapper = new QueryWrapper<>();
            annexQueryWrapper
                    .eq("bus_id",dto.getNotice().getId())
                    .eq("file_type",AnnexTypeEnum.BIDNOTICE.getValue())
                    .eq("is_delete",CommonConstants.NO);
            annexService.remove(annexQueryWrapper);

            if(CollUtil.isNotEmpty(dto.getInsertAnnexes())){
                if(!annexService.saveBatch(dto.getInsertAnnexes())){
                    throw new SunFlowerException(ExceptionEnum.UPDATE_NOTICE_FAILED,ExceptionEnum.UPDATE_NOTICE_FAILED.getMessage());
                }
            }
        }else if(NoticeProgressConstants.CAN_PUBLISH.contains(progress)){

            if(!this.updateById(notice)){
                throw new SunFlowerException(ExceptionEnum.UPDATE_NOTICE_FAILED,ExceptionEnum.UPDATE_NOTICE_FAILED.getMessage());
            }
        }
        else if(NoticeProgressConstants.CAN_UPDATE_PICS.contains(progress)){
//            QueryWrapper<NoticeMediaR> mediaRQueryWrapper = new QueryWrapper<>();
//            mediaRQueryWrapper
//                    .eq("notice_id",dto.getNotice().getId())
//                    .eq("is_delete",0);
//            if(!mediaRService.remove(mediaRQueryWrapper)){
//                throw new SunFlowerException(ExceptionEnum.UPDATE_NOTICE_FAILED,ExceptionEnum.UPDATE_NOTICE_FAILED.getMessage());
//            }
//
//            if(!mediaRService.saveBatch(dto.getInsertMedias())){
//                throw new SunFlowerException(ExceptionEnum.UPDATE_NOTICE_FAILED,ExceptionEnum.UPDATE_NOTICE_FAILED.getMessage());
//            }

        }
        // 同步项目状态
        tProjectBidNoticeService.syncProjectStatus(dto.getNotice().getProjectId());
        return Result.ok(true);
    }

    /**
     * 发布招标公告
     *
     * @param  notice 招标公告
     * @param ids 招标公告关联的包段ID集合
     * @return 提示信息，true代表成功，其他失败
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean updatePublishNotice(ProjectBidNotice notice,List<Long> ids) {
        // 上传公告
        try {
            if (StringUtils.isEmpty(notice.getNoticeContent())){
                notice.setNoticeContent("");
            }
            //long noticeOsskey = ossFileService.editor2PDF(notice.getNoticeContent(),notice.getNoticeName()+".pdf",notice.getNoticeName());
            //notice.setNoticeOssKey(noticeOsskey);
        } catch (Exception e) {
            e.printStackTrace();
            throw new SunFlowerException(ExceptionEnum.NOTICE_PUBLISH_FAILED,ExceptionEnum.NOTICE_PUBLISH_FAILED.getMessage());
        }

        // 更新招标公告状态
        notice.setNoticeProgress(NoticeProgressEnum.RELEASE.getValue());
        notice.setPublishTime(new Date());
        if(!updateById(notice)){
            throw new SunFlowerException(ExceptionEnum.NOTICE_PUBLISH_FAILED,ExceptionEnum.NOTICE_PUBLISH_FAILED.getMessage());
        }
        // 更新标段状态
        if(!commonSectionService.updateSectionStatus(ids,PackageStatusEnum.NOTICE_ISSUED.getValue())){
            throw new SunFlowerException(ExceptionEnum.NOTICE_PUBLISH_FAILED,ExceptionEnum.NOTICE_PUBLISH_FAILED.getMessage());
        }
        ///////// 发布媒体/////////
        allMediaService.addReleaseMedia(notice.getId(),MediaTypeEnum.TENDER_BULLETIN.getCode(),notice.getProjectId());
        ////////////////////////////////////////////////
        // 同步项目状态
        tProjectBidNoticeService.syncProjectStatus(notice.getProjectId());
        // 记录处理日志
        ApprovalOpinion opinion = new ApprovalOpinion();
            opinion
                    .setApprovalTime(new Date())
                    .setProjectId(notice.getProjectId())
                    .setNoticeId(notice.getId())
                    .setApprovalUserId(getJwtUser().getUserId())
                    .setApprovalUserName(getJwtUser().getUserName())
                    .setApprovalContent("")
                    .setApprovalType(ApprovalTypeEnum.RELEASE.getValue());
            opinion.setIsDelete(CommonConstants.NO);
            opinion.setVersion(0);
        approvalOpinionService.save(opinion);

        //发布，如果存在退回待处理，删除
        LambdaQueryWrapper<PendingItem> pendingItemLambdaQueryWrapper = new LambdaQueryWrapper<>();
        pendingItemLambdaQueryWrapper.eq(PendingItem::getBusinessCode, ReturnListEnum.TENDER_BID.getCode());
        pendingItemLambdaQueryWrapper.eq(PendingItem::getBusinessId,notice.getId());
        pendingItemLambdaQueryWrapper.eq(PendingItem::getProjectId,notice.getProjectId());
        pendingItemService.remove(pendingItemLambdaQueryWrapper);
        return true;
    }

    /**
     * 删除招标公告
     *
     * @param notice 招标公告实体
     * @return Result<Boolean>，成功返回true，失败返回false
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteById(ProjectBidNotice notice, List<Long> packageIds) {

        if(!removeById(notice.getId())){
            throw new SunFlowerException(ExceptionEnum.DELETE_FAILED,ExceptionEnum.DELETE_FAILED.getMessage());
        }

        // 更新项目包段状态
        if(CollUtil.isNotEmpty(packageIds)){
            if(!commonSectionService.updateSectionStatus(packageIds,PackageStatusEnum.TENDER_INVITATION.getValue())){
                throw new SunFlowerException(ExceptionEnum.DELETE_FAILED,ExceptionEnum.DELETE_FAILED.getMessage());
            }
        }
        // 删除包段关联
        QueryWrapper<NoticePackageR> noticePackageRQueryWrapper = new QueryWrapper<>();
        noticePackageRQueryWrapper
                .eq("notice_id",notice.getId())
                .eq("state",NoticeTypeEnum.PURCHASE_NOTICE.getValue())
                .eq("is_delete",CommonConstants.NO);
        noticePackageRService.remove(noticePackageRQueryWrapper);
        // 删除关联附件
        QueryWrapper<RelevancyAnnex> relevancyAnnexQueryWrapper = new QueryWrapper<>();
        relevancyAnnexQueryWrapper
                .eq("project_id",notice.getProjectId())
                .eq("bus_id",notice.getId())
                .eq("file_type",AnnexTypeEnum.BIDNOTICE.getValue())
                .eq("is_delete",CommonConstants.NO);
        annexService.remove(relevancyAnnexQueryWrapper);
        //删除媒体信息
        QueryWrapper<NoticeMediaR> noticeMediaRQueryWrapper = new QueryWrapper<>();
        noticeMediaRQueryWrapper
                .eq("notice_id",notice.getId())
                .eq("state", NoticeMediaTypeEnum.NOTICE_MEDIA.getValue())
                .eq("is_delete",CommonConstants.NO);
        mediaRService.remove(noticeMediaRQueryWrapper);
        // 同步项目状态
        tProjectBidNoticeService.syncProjectStatus(notice.getProjectId());

        return true;
    }

    /**
     * 返回当前用户的个人媒体信息列表
     *
     * @param userId 用户ID
     * @return List<PersonalMediaDTO> 个人用户的媒体信息列表
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<PersonalMediaDTO> initPersonalMedias(Long userId){

        QueryWrapper<NoticeMedia> wrapper = new QueryWrapper<>();
        wrapper
                .eq("created_user_id",userId)
                .eq("is_delete",CommonConstants.NO)
                .eq("state", NoticeMediaTypeEnum.PRIVATE_MEDIA.getValue());
        List<NoticeMedia> noticeMedia = mediaService.list(wrapper);
        HashMap<String, NoticeMedia> map = new HashMap<>();
        noticeMedia.forEach((item) -> {
            map.put(item.getMediaName(),item);
        });

        wrapper.clear();
        wrapper
                .eq("state", NoticeMediaTypeEnum.PUBLIC_MEDIA.getValue())
                .eq("is_delete",CommonConstants.NO);
        List<NoticeMedia> publicMedia = mediaService.list(wrapper);

        List<NoticeMedia> insert = new ArrayList<>();
        publicMedia.forEach((item) -> {
            if(!map.containsKey(item.getMediaName())){
                item
                        .setId(null)
                        .setState(NoticeMediaTypeEnum.PRIVATE_MEDIA.getValue())
                        .setNumberUse(0);
                insert.add(item);
            }
        });

        if(CollUtil.isNotEmpty(insert)){
            if(!mediaService.saveBatch(insert)){
                throw new RuntimeException();
            }
        }
        noticeMedia.addAll(insert);

        // 完成实体之间的转换
        List<PersonalMediaDTO> personalMediaDTOS = new ArrayList<>();
        noticeMedia.forEach((i) -> {
            PersonalMediaDTO personalMediaDTO = new PersonalMediaDTO()
                    .setMediaId(i.getId())
                    .setMediaName(i.getMediaName())
                    .setMediaUrl(i.getMediaUrl())
                    .setUseTimes(i.getNumberUse());

            personalMediaDTOS.add(personalMediaDTO);
        });

        return personalMediaDTOS;
    }

    /**
     * 检查项目是否可用，包括项目是否存在，包段信息是否存在
     *
     * @param id 项目ID
     * @return 项目可用则返回true；反之false
     */
//    private Boolean projectIsAvailable(Long id){
//
//        // 判断项目是否存在
//        Project project = projectService.getProjectById(id);
//        if(project == null){
//            return false;
//        }
//
//        // 根据项目ID去查包段信息
//        QueryWrapper<ProjectBidSection> queryWrapper = new QueryWrapper<>();
//        queryWrapper.eq("project_id",id).eq("is_delete",0);
//        Integer count = projectBidSectionService.count(queryWrapper);
//
//        if(count < 1){
//            return false;
//        }
//
//        return true;
//    }

    /**
     * 判断包段所对应的公告的发布状态
     *
     * @param packageIds 包段ID的集合
     * @return 全部已发布为true，有任何一个没发布则为false
     */
    @Override
    public Boolean isPublish(List<Long> packageIds, Integer bidRound) {
        Boolean f=false;
        for (Long packageId : packageIds) {
            f=false;
            // 查询包段所关联的公告
            QueryWrapper<NoticePackageR> packageRWrapper = new QueryWrapper<>();
            packageRWrapper
                    .eq("is_delete", CommonConstants.NO)
                    .eq("state", NoticeTypeEnum.PURCHASE_NOTICE.getValue())
                    .eq("section_id", packageId);
            List<NoticePackageR> packageRS = noticePackageRService.list(packageRWrapper);

            // 利用set去重
            Set<Long> noticeIds = new HashSet<>();
            packageRS.forEach((i) -> {
                if(i.getNoticeId() != null){
                    noticeIds.add(i.getNoticeId());
                }
            });
            // 如果公告ID的set为空
            if(CollectionUtil.isEmpty(noticeIds)){
                f =false;
            }else{
                // 获取所有对应的公告 发布状态
                List<ProjectBidNotice> notices = list(new QueryWrapper<ProjectBidNotice>()
                        .in("id",noticeIds)
                        .eq("is_delete", CommonConstants.NO)
                        .eq("notice_progress",NoticeProgressEnum.RELEASE.getValue())
                        .eq("bid_round",bidRound));
                if (null!=notices&&notices.size()>0){
                    f = true;
                }else {
                    f =false;
                }
            }
            if (f){
                continue;
            }else {
                break;
            }
        }

        return f;
    }

    /**
     * 招标公告定时服务，用于查询符合条件的招标公告
    */
    @Override
    public List<ProjectBidNotice> findNoticeForScheduleTask() {
        return this.baseMapper.findNoticeForScheduleTask();
    }

    /**
     * 根据项目包段状态自动同步项目状态
     *
     * @param projectId 项目ID
     * @return 受影响的记录行数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean syncProjectStatus(Long projectId) {
        Boolean flag = baseMapper.syncProjectStatus(projectId).equals(1) ? true : false;

        if(!flag) {
            throw new SunFlowerException(ExceptionEnum.SYNC_PROJECT_STATUS_FAILED,ExceptionEnum.SYNC_PROJECT_STATUS_FAILED.getMessage());
        }

        rabbitMqService.sendMq(projectId);
        return true;
    }

    /**
     * 判断招标公告是否属于对应状态
     *
     * @param noticeId 招标公告ID
     * @param progressList 在什么进度下的公告是可以操作的
     * @return 招标公告实体
     */
    @Override
    public ProjectBidNotice noticeIsAvailable(Long noticeId, List<Integer> progressList) {

        ProjectBidNotice notice = this.getById(noticeId);
        if(notice == null){
            throw new SunFlowerException(ExceptionEnum.NOTICE_NOT_FOUND,null);
        }

        if(!progressList.contains(notice.getNoticeProgress())){
            if (notice.getNoticeProgress().equals(NoticeProgressEnum.WITHDRAWAL.getValue())){
                throw new SunFlowerException(ExceptionEnum.NOTICE_CONTENT_WITHDRAW,null);
            }else{
                throw new SunFlowerException(ExceptionEnum.NOTICE_OPERATION_FAILED,null);
            }

        }

        return notice;
    }

    @Override
    public Boolean isInsertButton(Long projectId) {
        // 默认不能添加招标公告
        Boolean flag = false;
        QueryWrapper<ProjectBidSection> sectionWrapper = new QueryWrapper<>();
        // 查询包段所关联的公告
        sectionWrapper
                    .eq("is_delete",CommonConstants.NO)
                    .eq("project_id",projectId)
                    .ge("state", PackageStatusEnum.DIVIDE_SECTION.getValue());
        List<ProjectBidSection> sectionList = projectBidSectionService.list(sectionWrapper);
        if(null != sectionList && sectionList.size() > 0){
            for (ProjectBidSection section : sectionList) {
                QueryWrapper<NoticePackageR> packageWrapper = new QueryWrapper<>();
                packageWrapper.eq("",section.getId());
                Long count = noticePackageRService.count(packageWrapper);
                // 找到存在没有招标公告的包后就返回
                if(count == 0){
                    flag = true;
                    break;
                }
            }
        }
        return flag;
    }
    /**
     * 新增、修改媒体
     *
     * @param mediaList
     * @param id
     * @param projectId
     * @param userId
     * @return
     */
    private Result<Long> addOrUpdateMedia(List<NoticeMediaREQ> mediaList, Long id, Long projectId, Long userId) {
        MediaInfoDTO dto = new MediaInfoDTO();
        dto.setNoticeId(id);
        dto.setProjectId(projectId);
        dto.setNoticeType(MediaTypeEnum.TENDER_BULLETIN.getCode());
        dto.setUserId(userId);
        dto.setDtoList(BeanListUtil.convertList(mediaList, MediaDTO.class));
        allMediaService.addOrUpdateMedia(dto);
        return Result.ok(id);
    }
    /**
     * 新建或修改招标公告信息
     * @param req
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> saveOrUpdate(UpdateNoticeREQ req,JwtUser user) {
        Result<Boolean> result=null;
        // 准备数据
        UpdateNoticeDTO updateNoticeDTO = requestToDTOService.checkAndInitData(req);
        updateNoticeDTO.setUpdatePackageIds(req.getSectionIds());
        // 判断是否是新建公告/修改
        if(RequestUtil.isEmpty(req.getNoticeId())){

            result = tProjectBidNoticeService.insertNoticeInfo(updateNoticeDTO);
        }else{
            result = tProjectBidNoticeService.updateNoticeInfo(updateNoticeDTO);
        }

        // 判断是否是暂存/提交处理
        if(req.getSaveForShort()){
            //暂存
            //标段code用于判断页面是新增编辑或详情

        }else {
            // 提交审核
            ProjectBidNotice notice = updateNoticeDTO.getNotice();
            Integer progress = notice.getNoticeProgress();

            // 提交审核
            if(NoticeProgressConstants.CAN_EDIT.contains(progress)){
                ProjectBidNoticeVO noticeVO = tProjectBidNoticeService.queryEditNoticeInfo(new CommonNoticeREQ().setNoticeId(notice.getId()).setProjectId(notice.getProjectId())).getData();
                //保存提交历史记录
                NoticeChangeRecord changeRecords = requestToDTOService.toNoticeChangeRecord(noticeVO);
                List<Long> packageIds = noticePackageRService.findNoticePackageIdsByNoticeId(notice.getId());

                result =approvalOpinionService.insertSubmitNotice(notice,packageIds,changeRecords);
            }
            // 发布
            else if(NoticeProgressConstants.CAN_PUBLISH.contains(progress)){
                List<Long> packageIds = noticePackageRService.findNoticePackageIdsByNoticeId(notice.getId());
                result = Result.ok(tProjectBidNoticeService.updatePublishNotice(notice,packageIds));
            }
            else if(NoticeProgressConstants.CAN_UPDATE_PICS.contains(progress)){
                // 发布状态可以添加媒体截图
            }else{
                throw new SunFlowerException(ExceptionEnum.NOTICE_OPERATION_FAILED,null);
            }
            //提交、发布删除待处理表数据
            LambdaQueryWrapper<PendingItem> pendingItemLambdaQueryWrapper = new LambdaQueryWrapper<>();
            pendingItemLambdaQueryWrapper.eq(PendingItem::getBusinessCode, ReturnListEnum.TENDER_BID.getCode());
            pendingItemLambdaQueryWrapper.eq(PendingItem::getBusinessId,notice.getId());
            pendingItemService.remove(pendingItemLambdaQueryWrapper);
        }
        projectService.saveOrUpdateProjectRedundantFieldDealWith(req.getProjectId(),false);
        //commonSectionService.saveOrUpdateProjectPackageFieldDealWith(req.getProjectId());
        return result;
    }

    @Override
    public Boolean judgeSectionStatusById(Long sectionId) {
        Boolean f=false;
        QueryWrapper noticePackageRQueryWrapper=new QueryWrapper<NoticePackageR>()
                .eq("section_id",sectionId)
                .eq("is_delete", CommonConstants.NO);
        List<NoticePackageR> list= noticePackageRService.list(noticePackageRQueryWrapper);
        if (list!=null&&list.size()>0){
           ProjectBidSection projectBidSection= projectBidSectionService.getById(sectionId);
            List<Long> noticeIds=new ArrayList<>();
            for (int i = 0; i < list.size(); i++) {
                noticeIds.add(list.get(i).getNoticeId());
            }
            List<ProjectBidNotice> list1=tProjectBidNoticeService.list(new QueryWrapper<ProjectBidNotice>().in("id",noticeIds).eq("bid_round",projectBidSection.getBidRound()));
//            TProjectBidNotice projectBidNotice = list1.get(0);
            if (list1!=null&&list1.size()>0){
                for (int i = 0; i < list1.size(); i++) {
                    ProjectBidNotice projectBidNotice = list1.get(i);
                    if (NoticeProgressEnum.DRAFTING.getValue().equals(projectBidNotice.getNoticeProgress())||
                            NoticeProgressEnum.WITHDRAWAL.getValue().equals(projectBidNotice.getNoticeProgress())||
                            NoticeProgressEnum.RETURN.getValue().equals(projectBidNotice.getNoticeProgress())){
                        f=true;
                        break;
                    }else {
//                        f=false;
                    }
                }

            }else{
                f=true;
            }

        }else {
            f=true;
        }

        return f;
    }

    /**
     * 根据标段id查询公告信息
     * @param req
     * @return
     */
    @Override
    public List<NoticeTimeVO> getNoticeTime(NoticeSectionREQ req) {
        String[] sectionIdString = req.getSectionId().split(",");
        List<Long> sectionId = new ArrayList<>();
        for (String s : sectionIdString) {
            sectionId.add(Long.parseLong(s));
        }
        List<NoticeTimeVO> noticeTime = projectBidNoticeMapper.getNoticeTime(sectionId);
        return noticeTime;
    }


}
