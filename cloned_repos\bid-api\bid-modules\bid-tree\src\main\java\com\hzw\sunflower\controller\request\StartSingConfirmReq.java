package com.hzw.sunflower.controller.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date ：Created in 2021/7/8 15:56
 * @description：开启唱标确认请求实体类
 * @version: 1.0
 */
@Data
@NoArgsConstructor
public class StartSingConfirmReq implements Serializable {

    @ApiModelProperty(value = "线上会议室ID")
    private Long onlineConferenceId;

    @ApiModelProperty(value = "标段ID")
    private Long sectionId;

    @ApiModelProperty(value = "项目ID")
    private Long projectId;

    @ApiModelProperty(value = "手动上传的开标记录表文件id")
    private Long fileOssId;

    @ApiModelProperty(value = "轮次")
    private Integer bidRound;

//    @ApiModelProperty(value = "表格数据")
//    private List<List<Object>> rowDataList;

    @ApiModelProperty(value = "表格数据")
    List<List<Map<String, Object>>> rowDataList;

}
