<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper 
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hzw.ssm.expert.dao.ExpertNotParticipateMapper">
	<!-- 查询专家反馈项目列表 -->
	<select id="queryPageENPList" resultType="ExpertNotParticipateEntity" parameterType="ExpertNotParticipateEntity">
		SELECT TP.PROJECT_NAME AS projectName,
				TP.PROJECT_NO AS projectNo,
				TER.REASON AS reason,
				TEI.USER_NAME as expertName
			FROM T_EXTRACT_RESULT TER 
			LEFT JOIN T_EXPERT_INFO TEI ON TER.EXPERT_ID = TEI.USER_ID
			LEFT JOIN T_CONDITION TC ON TC.ID = TER.CONDITION_ID
			LEFT JOIN T_PROJECT TP ON TP.PROJECT_ID  =  TC.PROJECT_ID
			WHERE TER.JOIN_STATUS = 1
			<if test = "null != projectName and '' != projectName">
				AND TP.PROJECT_NAME LIKE '%${projectName}%'
			</if>
			<if test="null != projectNo and '' != projectNo">
				AND TP.PROJECT_NO like '%${projectNo}%'
			</if>
			<if test="null != reason and '' != reason">
				AND TER.REASON like '%${reason}%'
			</if>
			<if test="null != expertName and '' != expertName">
				AND TEI.USER_NAME like '%${expertName}%'
			</if>
			AND TER.DELETE_FLAG =0
			AND TEI.DELETE_FLAG =0
			AND TC.DELETE_FLAG = 0
			AND TP.DELETE_FLAG = 0
	</select>
</mapper>
