///***
/// * 搜索结果页
/// */

import 'dart:async';
import "package:flutter/material.dart";

import 'package:flutter_main_app/serviceLocator.dart';
import 'package:flutter_main_app/src/constants/theme.dart';

import 'package:flutter_main_app/src/viewModels/home/<USER>';

import 'package:flutter_main_app/src/models/bulletin/BulletinListQueryModel.dart';
import 'package:flutter_main_app/src/models/bulletin/BulletinListItemModel.dart';

import 'package:flutter_main_app/src/components/DataListView.dart';
import 'package:flutter_main_app/src/components/CustomInfoCard.dart';
import 'package:flutter_main_app/src/components/CustomTab.dart';

import 'package:flutter_main_app/src/widgets/home/<USER>/SearchHeader.dart';

class SearchResultWidget extends StatefulWidget {
  const SearchResultWidget({Key key}) : super(key: key);

  /// * 路由名称
  static const String _routeName = 'SearchResult';
  static String get routeName => _routeName;

  @override
  _SearchResultWidgetState createState() => _SearchResultWidgetState();
}

class _SearchResultWidgetState extends State<SearchResultWidget> {

  final FocusNode _focusNode = FocusNode();

  HomeSearchViewModel _model = serviceLocator<HomeSearchViewModel>();

  String _keyword;

  Future<List<BulletinListItemModel>> _initDataList;

  @override
  void initState() {
    super.initState();

    _keyword = _model.keyword;

    _model.params = BulletinListQueryModel(
      bidType: [],
      bulletinTypeName: [],
      classifyName: [],
      currentPage: '1',
      fundRange: [],
      fundSource: [],
      industryCode: [],
      keyWord: [],
      pageSize: 20,
      platformCode: [],
      regionCode: [],
      uid: '',
    );

    _initDataList = _model.fetchDataList();
  }

  @override
  void dispose() {
    _focusNode.dispose();

    _initDataList = null;

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        _unfocusTextField();
      },
      child: Scaffold(
        body: SafeArea(
          top: false,
          bottom: true,
          child: Column(
            mainAxisSize: MainAxisSize.max,
            children: <Widget>[
              /// * 头部
              SearchHeader(
                keyword: _keyword,
                onSubmittedCallback: (String keyword) {
                  setState(() {
                    _model.keyword = keyword;
                    _keyword = keyword;
                    _initDataList = _model.fetchDataList();
                  });
                },
                focusNode: _focusNode,
              ),
              /// * 数据列表
              Expanded(
                child: FutureBuilder(
                  future: _initDataList,
                  builder: (BuildContext context, AsyncSnapshot snapshot) {
                    switch (snapshot.connectionState) {
                      case ConnectionState.none:
                      case ConnectionState.active:
                      case ConnectionState.waiting:
                        return Center(
                          child: CircularProgressIndicator(),
                        );
                      case ConnectionState.done:
                        if (snapshot.hasError) {
                          return Text('Error: ${snapshot.error}');
                        }

                        return DataListView<BulletinListItemModel>(
                          initialDataItems: snapshot.data,
                          itemBuilder: (BuildContext context, int index, List<BulletinListItemModel> dataItems) {
                            final bulletin = dataItems[index];

                            return Container(
                              color: themeContentBackgroundColor,
                              child: CustomInfoCard(
                                title: bulletin.bulletinName != null && bulletin.bulletinName.isNotEmpty
                                  ? bulletin.bulletinName
                                  : '---',
                                tabs: [
                                  CustomTab(
                                    text: '${bulletin.regionName}',
                                    color: const Color(0xff3e78e6),
                                    borderColor: const Color(0xff3e78e6),
                                  ),
                                  CustomTab(
                                    text: '${bulletin.classifyName ?? "其他"}',
                                    color: const Color(0xfff9a545),
                                    borderColor: const Color(0xfff9a545),
                                  ),
                                ],
                                date: bulletin.noticeSendTime != null && bulletin.noticeSendTime.length >= 10
                                  ? bulletin.noticeSendTime.substring(0, 10)
                                  : null,
                                isFirstChild: index == 0,
                              ),
                            );
                          },
                          itemClickFunction: (_, __, ___) => _handleDataTileTaped,
                          refreshDataFunction: () => _handleDataListRefresh(),
                          loadingMoreDataFunction: () => _handleDataListLoadMore(),
                        );
                      default:
                        return Container();
                    }
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  ///***
  /// * 下拉刷新数据列表
  /// *
  /// * @return {Future<List<BulletinListItemModel>>}
  /// */
  Future<List<BulletinListItemModel>> _handleDataListRefresh() async {
    _model.clearDataList();
    _model.setSpecifiedParam('currentPage', '1');

    return await _model.fetchDataList();
  }

  ///***
  /// * 滚动到数据列表底部加载更多
  /// *
  /// * @return {Future<List<BulletinListItemModel>>}
  /// */
  Future<List<BulletinListItemModel>> _handleDataListLoadMore() async {
    _model.setSpecifiedParam(
      'currentPage',
      _model.dataList[_model.dataList.length - 1].currentPageCursor,
    );

    return await _model.fetchDataList();
  }

  ///***
  /// * 点击查看公告公示的信息
  /// *
  /// * @param {BuildContext} context
  /// * @param {int} index
  /// * @param {List<BulletinListItemModel>} dataList
  /// */
  void _handleDataTileTaped(
    BuildContext context,
    int index,
    List<BulletinListItemModel> dataList,
  ) {
    _unfocusTextField();

    /// TODO: 公告公示 Tile 点击事件
    if (index % 3 == 0) {
      Navigator.pushNamed(context, 'TenderBulletinDetail');
    } else if (index % 3 == 1) {
      Navigator.pushNamed(context, 'WinCandidateBulletinDetail');
    } else if (index % 3 == 2) {
      Navigator.pushNamed(context, 'WinBidBulletinDetail');
    }
  }

  ///***
  /// * 使文本框失焦
  /// */
  void _unfocusTextField() {
    _focusNode.unfocus();
  }

}
