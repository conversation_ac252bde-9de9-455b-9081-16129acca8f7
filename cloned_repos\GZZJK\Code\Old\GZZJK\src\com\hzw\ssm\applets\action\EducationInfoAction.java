package com.hzw.ssm.applets.action;

import com.hzw.ssm.applets.entity.EducationInfo;
import com.hzw.ssm.applets.service.EducationInfoService;
import com.hzw.ssm.applets.utils.GetJsonReqData;
import com.hzw.ssm.applets.utils.GetJsonRespData;
import com.hzw.ssm.applets.utils.JsonToObject;
import com.hzw.ssm.applets.utils.ResultJson;
import com.hzw.ssm.fw.base.BaseAction;
import net.sf.json.JSONObject;
import org.apache.struts2.convention.annotation.Action;
import org.apache.struts2.convention.annotation.Namespace;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @Date 2021-02-01 17:09
 * @Version 1.0
 */
@Namespace("/applets/educationInfoAction")
public class EducationInfoAction extends BaseAction {

    @Autowired
    private EducationInfoService educationInfoService;

    /**
     * 新增
     *
     * @return
     */
    @Action("save")
    public String save() {

        JSONObject jsonReqData = GetJsonReqData.getJsonReqData();
        String toString = jsonReqData.getJSONObject("formData").toString();
        EducationInfo educationInfo = JsonToObject.jsonToObject(toString, EducationInfo.class);
        ResultJson resultJson = educationInfoService.insertEducationInfo(educationInfo);
        GetJsonRespData.getJsonRespData(resultJson);
        return null;
    }

    /**
     * 编辑
     *
     * @return
     */
    @Action("update")
    public String update() {

        JSONObject jsonReqData = GetJsonReqData.getJsonReqData();
        String toString = jsonReqData.getJSONObject("formData").toString();
        EducationInfo educationInfo = JsonToObject.jsonToObject(toString, EducationInfo.class);
        ResultJson resultJson = educationInfoService.updateEducationInfo(educationInfo);
        GetJsonRespData.getJsonRespData(resultJson);
        return null;
    }

    /**
     * 删除
     *
     * @return
     */
    @Action("delete")
    public String delete() {

        JSONObject jsonReqData = GetJsonReqData.getJsonReqData();
        String toString = jsonReqData.getJSONObject("formData").toString();
        EducationInfo educationInfo = JsonToObject.jsonToObject(toString, EducationInfo.class);
        ResultJson resultJson = educationInfoService.deleteEducationInfo(educationInfo);
        GetJsonRespData.getJsonRespData(resultJson);
        return null;
    }

    /**
     * 查询单个
     *
     * @return
     */
    @Action("getOne")
    public String getOne() {

        JSONObject jsonReqData = GetJsonReqData.getJsonReqData();
        String toString = jsonReqData.getJSONObject("formData").toString();
        EducationInfo educationInfo = JsonToObject.jsonToObject(toString, EducationInfo.class);
        ResultJson resultJson = educationInfoService.getEducationInfo(educationInfo);
        GetJsonRespData.getJsonRespData(resultJson);
        return null;
    }

    /**
     * 查询所有
     *
     * @return
     */
    @Action("all")
    public String all() {

        JSONObject jsonReqData = GetJsonReqData.getJsonReqData();
        String toString = jsonReqData.getJSONObject("formData").toString();
        EducationInfo educationInfo = JsonToObject.jsonToObject(toString, EducationInfo.class);
        ResultJson resultJson = educationInfoService.getEducationInfos(educationInfo);
        GetJsonRespData.getJsonRespData(resultJson);
        return null;
    }

}
