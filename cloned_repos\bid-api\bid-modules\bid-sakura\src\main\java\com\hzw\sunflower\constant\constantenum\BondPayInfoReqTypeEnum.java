package com.hzw.sunflower.constant.constantenum;

/**
 * <AUTHOR>
 * @date ：Created in 2021/10/11 14:47
 * @description：银行类型
 * @version: 1.0
 */
public enum BondPayInfoReqTypeEnum {

    BOND(1, "保证金"),
    AGENT(2, "代理服务费");

    private Integer type;
    private String desc;

    BondPayInfoReqTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
