package com.hzw.sunflower.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.hzw.sunflower.common.BaseBean;
import io.swagger.annotations.ApiModel;
import lombok.Data;

/**
 * 公司流程配置表
 */
@ApiModel("t_company_flow_config表")
@TableName("t_company_flow_config")
@Data
public class CompanyFlowConfig extends BaseBean {
    private Long id;

    /**
     * 流程编码
     *
     * @mbg.generated
     */
    private String processDefinitionKey;

    /**
     * 公司ID
     *
     * @mbg.generated
     */
    private Long companyId;

    /**
     * 流程名称
     *
     * @mbg.generated
     */
    private String flowName;

    /**
     * 业务编号
     *
     * @mbg.generated
     */
    private String businessCode;

    /**
     * 是否默认流程：1 是，0否
     *
     * @mbg.generated
     */
    private Integer isDefault;

    /**
     * 流程归属类型  1 总公司  2 分公司
     */
    private Integer businessType;

    /**
     * 审批人为空是否跳过 1 是 2 否
     */
    private Integer isJump;

    /**
     * 是否添加记录 1 是 2 否
     */
    private Integer isAddRecord;

    /**
     * 流程code
     */
    private String recordCode;

    private static final long serialVersionUID = 1L;
}