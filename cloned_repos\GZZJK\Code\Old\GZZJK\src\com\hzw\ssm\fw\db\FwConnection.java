package com.hzw.ssm.fw.db;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.util.Map;
import java.util.Properties;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import com.hzw.ssm.fw.util.FileUtil;

public abstract class FwConnection {

	private static Log log = LogFactory.getLog(FwConnection.class);
	
	/** 操作结果的状态字 */
    public final static int LOGIC_SUCCESS = 0;
    public final static int LOGIC_ERROR = -1;
    public final static int LOGIC_WARN = -2;
    public final static int LOGIC_EXIST = -3;
    public final static int LOGIC_NOTEXIST = -4;
    public final static int LOGIC_ISNULL = -5;
    public final static int LOGIC_NOTNULL = -6;
    public final static int LOGIC_ERRINIT = -7;
    public final static int DBERR_SEARCH = -8;
    public final static int DBERR_INSERT = -9;
    public final static int DBERR_UPDATE = -10;
    public final static int DBERR_DELETE = -11;
    public final static int LOGIC_RETURN = 1;

    private static String driverClassName = "";
    private static String url = "";
    private static String username = "";
    private static String password = "";
    
    /** 数据库连接 */
    private Connection m_conn;

    /** 返回的结果 */
    protected Map<String,Object> m_objOut = null;

    /**
     * 构造函数，设置数据库连接
     */
    public FwConnection(){}
    
    /**
     * 取得数据库连接
     * @return m_conn
     */
    public Connection getConnection(){
    	return m_conn;
    }
    
    /**
     * 抽象的逻辑，需要自己实现特定的逻辑
     * @return int
     */
    protected abstract int doLogic(Map<String,Object> mapIn, Map<String,Object> mapOut);
    
    /**
     * 加载数据库连接信息
     * @throws Exception 抛出异常
     */
    private static void loadProperties() throws Exception{
        if(driverClassName==null || driverClassName.equals("")){
            String path = "jdbc.properties";
            Properties pro = FileUtil.getProperties(FwConnection.class,path);
            
            driverClassName=pro.getProperty("jdbc.driverClassName");
            url=pro.getProperty("jdbc.url");
            username=pro.getProperty("jdbc.username");
            password=pro.getProperty("jdbc.password");
        }
    }
    
    /**
     * 创建数据库链接
     * @param autoCommit 是否自动提交
     * @throws Exception 抛出异常
     */
    private void createConnection(boolean autoCommit) throws Exception {
    	
        loadProperties();
        
        Class.forName(driverClassName);
        m_conn = DriverManager.getConnection(url,username,password);
        if (m_conn == null) {
            throw new SQLException("---- Connection is null. ----");
        }
        
        m_conn.setAutoCommit(autoCommit);
        log.info("---- Open Database Connection ----");
    }
    
    /**
     * 自动获取数据库链接后,执行逻辑处理,最后关闭数据库链接
     * @return int 执行结果
     */
    public int execute(Map<String,Object> mapIn, Map<String,Object> mapOut) throws Exception {
        int nResult = LOGIC_SUCCESS;
        
        try {
            createConnection(true);
            nResult = doLogic(mapIn, mapOut);
            m_objOut = mapOut;
        } catch (Exception e) {
            nResult = LOGIC_ERROR;
            log.error(e);
            e.printStackTrace();
        } finally {
            try {
                if (m_conn!=null) {
                    m_conn.close();
                    m_conn = null;
                    log.info("---- Close Database Connection ----");
                }
            } catch (Exception e) {
                log.error(e);
            }
        }

        return nResult;
    }
    
    /**
     * 获取信息返回保存对象
     * @return 信息返回保存对象
     */
    public Map<String,Object> getOutData(){
        return m_objOut;
    }
    
    
}
