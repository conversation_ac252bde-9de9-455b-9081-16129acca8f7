package com.hzw.sunflower.entity.condition;

import com.hzw.sunflower.common.BaseCondition;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(description = "关联申请查询条件")
@Data
public class BondApplyRelationCondition extends BaseCondition {

    @ApiModelProperty(value = "关键字：项目编号/项目名称")
    private String keywords;

    @ApiModelProperty(value = "用户id，无需传递")
    private Long userId;

    @ApiModelProperty(value = "部门id，无需传递")
    private Long departId;

}
