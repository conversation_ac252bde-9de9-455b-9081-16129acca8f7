package com.hzw.sunflower.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hzw.sunflower.entity.SectionGroup;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @date ：Created in 2021/8/27 11:28
 * @description： 标段/包按套销售Mapper接口
 * @version: 1.0
 */
public interface SectionGroupMapper extends BaseMapper<SectionGroup> {
    /**
     * 根据标包id查询不等于当前套id的套
     * @param projectId
     * @param subId
     * @param id
     * @return
     */
    SectionGroup queryGroupInfoPassId(@Param("projectId") Long projectId,@Param("subId") Long subId,@Param("id") Long id);
}
