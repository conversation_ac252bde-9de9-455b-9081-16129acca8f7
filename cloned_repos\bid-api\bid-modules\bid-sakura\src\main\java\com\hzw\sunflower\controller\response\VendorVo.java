package com.hzw.sunflower.controller.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(description = "保证金异常退款申请详情-供应商信息")
@Data
public class VendorVo {

    @ApiModelProperty("公司名称")
    private String companyName;

    @ApiModelProperty("统一社会信用代码")
    private String organizationNum;

    @ApiModelProperty("法定代表人")
    private String legalRepresentative;

    @ApiModelProperty("法定代表人身份证号")
    private String legalRepresentativeIdentity;

    @ApiModelProperty("联系人姓名")
    private String userName;

    @ApiModelProperty("联系人手机号")
    private String userPhone;

    @ApiModelProperty("联系人身份证号")
    private String userIdentity;

    @ApiModelProperty("联系人邮箱")
    private String userEmail;

    @ApiModelProperty("法人代表授权函")
    private String legalPersonAuthorization;

}
