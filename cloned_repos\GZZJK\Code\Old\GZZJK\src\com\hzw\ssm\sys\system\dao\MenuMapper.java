package com.hzw.ssm.sys.system.dao;

import java.util.List;

import com.hzw.ssm.sys.system.entity.MenuEntity;

public interface MenuMapper {

	/**
	 * 加载树形菜单
	 * 
	 * @param menuEntity
	 *            菜单查询条件实体对象
	 * @return 实体对象集合
	 */
	public List<MenuEntity> queryMenuList();

	/**
	 * 根据id获取一条菜单信息
	 * 
	 * @return
	 */
	public List<MenuEntity> getMenuById(MenuEntity menu);


	/**
	 * 根据菜单名称获取菜单信息
	 * 
	 * @return
	 */
	public List<MenuEntity> getMenuByName(MenuEntity menu);

	/**
	 * 根据父菜单id获取信息
	 * 
	 * @param parentId
	 * @return
	 */
	public List<MenuEntity> getMenuByParentId(MenuEntity menu);

	/**
	 * 查询子菜单
	 * 
	 * @param parentId
	 * @return
	 */
	public List<MenuEntity> querySubMenuByMenuId(MenuEntity menu);

	/**
	 * 新增菜单对象
	 * 
	 * @param menuEntity
	 *            菜单实体对象
	 */
	public void saveMenu(MenuEntity menuEntity);

	/**
	 * 更新菜单对象
	 * 
	 * @param userEntity
	 *            菜单实体对象
	 */
	public void modifyMenu(MenuEntity menuEntity);

	/**
	 * 删除菜单信息
	 * 
	 * @param menuEntity
	 */
	public void deleteMenuById(MenuEntity menuEntity);

}
