package com.hzw.sunflower.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzw.sunflower.controller.response.BondCompanyBankVo;
import com.hzw.sunflower.dao.BondCompanyBankMapper;
import com.hzw.sunflower.entity.BondCompanyBank;
import com.hzw.sunflower.service.BondCompanyBankService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 供应商行号维护表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-28
 */
@Service
public class BondCompanyBankServiceImpl extends ServiceImpl<BondCompanyBankMapper, BondCompanyBank> implements BondCompanyBankService {

    /**
     * 根据companyId去供应商行号维护表中查询行号信息
     * @param companyId
     * @return
     */
    @Override
    public List<BondCompanyBankVo> getPayFileCompanyInfo(Long companyId,Long refundId) {
        return this.baseMapper.getPayFileCompanyInfo(companyId,refundId);
    }
}
