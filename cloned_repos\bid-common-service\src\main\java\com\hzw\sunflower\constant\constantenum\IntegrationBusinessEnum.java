package com.hzw.sunflower.constant.constantenum;

/**
 * <AUTHOR>
 * @date ：Created in 2021/4/15 10:36
 * @description：单位常量
 * @modified By：`
 * @version: 1.0
 */
public enum IntegrationBusinessEnum {

    /**
     * 实名认证
     */
    REAL_NAME_JUHE(11, "juhe"),

    /**
     * 企业认证
     */
    COMPANY_AUTHENTICATION_TYC(21, "tyc"),

    /**
     * 企业关系
     */
    COMPANY_RELATIONSHIP_TYC(31, "tyc"),

    /**
     * 表格解析
     */
    TABLE_OCR_BAIDU(41, "baidu"),
    TABLE_OCR_RESULT_BAIDU(42, "baidu");

    private Integer type;
    private String desc;

    IntegrationBusinessEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

}
