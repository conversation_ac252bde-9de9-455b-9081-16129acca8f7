package com.hzw.sunflower.controller.response;

import com.hzw.sunflower.common.Paging;
import com.hzw.sunflower.dto.BondWaterDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date ：Created in 2022/8/4 15:15
 * @description：收款流水分页返回
 * @modified By：`
 * @version: 1.0
 */
@Data
@ApiModel(description = "保证金收款流水分页返回实体类")
public class BondWaterPageVo implements Serializable {

    @ApiModelProperty("分页数据")
    Paging<BondWaterDto> pageData;

    @ApiModelProperty("总金额")
    BigDecimal totalAmount;
}
