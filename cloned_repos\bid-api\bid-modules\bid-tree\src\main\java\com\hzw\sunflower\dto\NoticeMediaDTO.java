package com.hzw.sunflower.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 公告关联媒体信息
 *
 * <AUTHOR> 2021/07/01
 * @version 1.0.0
 */
@ApiModel(description = "公告关联媒体信息")
@Data
@Accessors(chain = true)
public class NoticeMediaDTO {

    @ApiModelProperty(value = "媒体ID", position = 1)
    private Long mediaId;

    @ApiModelProperty(value = "媒体名称", position = 2)
    private String mediaName;

    @ApiModelProperty(value = "媒体链接", position = 3)
    private String mediaUrl;

    @ApiModelProperty(value = "发布截图附件", position = 4)
    private List<AnnexDTO> pics;

    @ApiModelProperty(value = "使用次数", position = 5)
    private Integer useTimes;

}
