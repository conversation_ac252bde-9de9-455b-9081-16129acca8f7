package com.hzw.sunflower.constant.constantenum;

/**
 * <AUTHOR>
 * @date ：Created in 2021/6/30 10:26
 * @description：保证金类型
 * @version: 1.0
 */
public enum BondTypeEnum {
    FREE(0, "不收取"),
    QUOTA(1, "定额"),
    PROPORTION(2, "比例");

    private Integer value;

    private String name;

    BondTypeEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    public Integer getValue() {
        return value;
    }

    public String getName() {
        return name;
    }
}
