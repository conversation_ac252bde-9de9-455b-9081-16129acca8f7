# Tomcat
server:
  port: 9900

# Spring
spring:
  application:
    # 应用名称
    name: portal-xxl-job-admin
  profiles:
    # 环境配置
    active: dev
  mvc:
    servlet:
      load-on-startup: 0
    static-path-pattern: /static/**
  web:
    resources:
      static-locations: classpath:/static/
  freemarker:
    charset: UTF-8
    request-context-attribute: request
    settings:
      number_format: 0.##########
    suffix: .ftl
    templateLoaderPath: classpath:/templates/

logging:
  config: classpath:logback-plus.xml

--- # nacos 配置
spring:
  cloud:
    nacos:
      # nacos 服务地址
      server-addr: 127.0.0.1:8848
      discovery:
        # 注册组
        group: DEFAULT_GROUP
        namespace: ${spring.profiles.active}
      config:
        # 配置组
        group: DEFAULT_GROUP
        namespace: ${spring.profiles.active}
  config:
    import:
      - optional:nacos:datasource.yml
      - optional:nacos:${spring.application.name}.yml
