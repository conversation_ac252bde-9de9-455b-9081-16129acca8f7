package com.hzw.sunflower.entity;


import com.baomidou.mybatisplus.annotation.TableName;
import com.hzw.sunflower.common.BaseBean;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("t_statistics_user")
public class UserStatistics extends BaseBean {
    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "月份")
    private String yearmonth;

    @ApiModelProperty(value = "项目经理id")
    private Long userId;

    @ApiModelProperty(value = "项目经理名称")
    private String userName;

    @ApiModelProperty(value = "部门id")
    private Long departmentId;

    @ApiModelProperty(value = "部门名称")
    private String departmentName;

    @ApiModelProperty(value = "已开标项目数")
    private Long projectCount;

    @ApiModelProperty(value = "未开标项目数")
    private Long noProjectCount;

    @ApiModelProperty(value = "已开标项目委托金额")
    private BigDecimal entrustedAmount;

    @ApiModelProperty(value = "未开标项目委托金额")
    private BigDecimal noEntrustedAmount;

    @ApiModelProperty(value = "中标金额")
    private BigDecimal winPrice;

    @ApiModelProperty(value = "已收费")
    private BigDecimal account;

    @ApiModelProperty(value = "已开标未收费中标金额预估")
    private BigDecimal estimateAccount;

    @ApiModelProperty(value = "已开标未收费金额")
    private BigDecimal noAccount;
}
