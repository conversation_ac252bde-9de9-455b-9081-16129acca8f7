package com.hzw.sunflower.constant.constantenum;

/**
 * 业务操作类型
 *
 * <AUTHOR>
 */
public enum OperateTypeEnum {
    ADD(1, "新增"),
    EDIT(2, "更新");

    private Integer type;
    private String desc;

    OperateTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
