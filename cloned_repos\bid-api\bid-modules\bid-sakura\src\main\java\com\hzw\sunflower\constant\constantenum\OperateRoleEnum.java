package com.hzw.sunflower.constant.constantenum;

/**
 * <AUTHOR>
 * @date ：Created in 2022/07/27 9:23
 * @description：操作角色标识枚举，用于关联详情逻辑判断
 * @version: 1.0
 */
public enum OperateRoleEnum {

    SUP(1, "供应商"),
    MANAGE(2, "项目经理"),
    OPERATOR(3, "运管处");

    private Integer type;
    private String desc;

    OperateRoleEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
