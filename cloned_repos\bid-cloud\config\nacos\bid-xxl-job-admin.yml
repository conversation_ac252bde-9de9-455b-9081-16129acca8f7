# server 配置
spring:
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ${datasource.job.url}
    username: ${datasource.job.username}
    password: ${datasource.job.password}
    hikari:
      auto-commit: true
      connection-test-query: SELECT 1
      connection-timeout: 10000
      idle-timeout: 30000
      max-lifetime: 900000
      maximum-pool-size: 30
      minimum-idle: 10
      pool-name: HikariCP
      validation-timeout: 1000
  mail:
    from: <EMAIL>
    host: smtp.qq.com
    username: <EMAIL>
    password: xxx
    port: 25
    properties:
      mail:
        smtp:
          auth: true
          socketFactory:
            class: javax.net.ssl.SSLSocketFactory
          starttls:
            enable: true
            required: true

# mybatis 配置
mybatis:
  mapper-locations: classpath:/mybatis-mapper/*Mapper.xml

# Actuator 监控端点的配置项
management:
  health:
    mail:
      enabled: false
  endpoints:
    web:
      exposure:
        include: '*'
  endpoint:
    health:
      show-details: ALWAYS
    logfile:
      external-file: ./logs/${spring.application.name}/console.log

# xxljob系统配置
xxl:
  job:
    # 鉴权token
    accessToken: xxl-job
    # 国际化
    i18n: zh_CN
    # 日志清理
    logretentiondays: 30
    triggerpool:
      fast:
        max: 200
      slow:
        max: 100
