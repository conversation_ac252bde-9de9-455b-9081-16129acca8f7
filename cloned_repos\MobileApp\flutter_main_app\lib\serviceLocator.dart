///***
/// * 返回视图模型的单例模式
/// */

import 'package:get_it/get_it.dart';

import 'package:flutter_main_app/src/viewModels/home/<USER>';
import 'package:flutter_main_app/src/viewModels/home/<USER>';

import 'package:flutter_main_app/src/viewModels/customInfo/CustomInfoViewModel.dart';

import 'package:flutter_main_app/src/viewModels/marketPlayer/MarketPlayerListViewModel.dart';
import 'package:flutter_main_app/src/viewModels/marketPlayer/SubjectBasicViewModel.dart';
import 'package:flutter_main_app/src/viewModels/marketPlayer/SubjectPerformanceViewModel.dart';
import 'package:flutter_main_app/src/viewModels/marketPlayer/SubjectQualificationViewModel.dart';
import 'package:flutter_main_app/src/viewModels/marketPlayer/SubjectStaffViewModel.dart';

import 'package:flutter_main_app/src/viewModels/dataReport/DataReportViewModel.dart';

GetIt serviceLocator = GetIt.instance;

void setupInfoServiceLocator() {

  /// * 首页
  serviceLocator.registerLazySingleton(() => HomeIndexViewModel());
  serviceLocator.registerLazySingleton(() => HomeSearchViewModel());

  /// * 信息订阅
  serviceLocator.registerLazySingleton(() => CustomInfoViewModel());

  /// * 市场主体
  serviceLocator.registerLazySingleton(() => MarketPlayerListViewModel());
  serviceLocator.registerLazySingleton(() => SubjectBasicViewModel());
  serviceLocator.registerLazySingleton(() => SubjectPerformanceViewModel());
  serviceLocator.registerLazySingleton(() => SubjectQualificationViewModel());
  serviceLocator.registerLazySingleton(() => SubjectStaffViewModel());

  /// * 数据报告
  serviceLocator.registerLazySingleton(() => DataReportViewModel());

}
