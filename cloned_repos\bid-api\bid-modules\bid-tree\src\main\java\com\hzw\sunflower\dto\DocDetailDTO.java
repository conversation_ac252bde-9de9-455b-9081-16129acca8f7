package com.hzw.sunflower.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;
import java.util.Map;

@Data
@EqualsAndHashCode
public class DocDetailDTO implements Serializable {

    @ApiModelProperty(value = "招标文件ID", position = 1)
    private Long docId;

    @ApiModelProperty(value = "文件名称", position = 2)
    private String annexName;

    @ApiModelProperty(value = "文件发售方式(0:电子文件线上下载, 1:纸质文件不支持邮寄)", position = 3)
    private Integer releaseFileType;

    @ApiModelProperty(value = "文件发售审核方式(0:无需审核, 1:审核无需材料, 2:审核上传材料)", position = 4)
    private Integer reviewFileType;

    @ApiModelProperty(value = "oss文件Key", position = 5)
    private String ossFileKey;

    @ApiModelProperty(value = "oss文件名称", position = 6)
    private String ossFileName;

    @ApiModelProperty(value = "oss文件ID", position = 7)
    private Long ossFileId;

    @ApiModelProperty(value = "招标文件售价说明（1：交通；2：水利；3：其他）", position = 8)
    private Integer filePriceState;

    @ApiModelProperty(value = "文件售价说明其他时具体描述", position = 9)
    private String otherPriceDescribe;

    @ApiModelProperty(value = "费用类型 1.标书费 2.平台服务费")
    private Integer feeType;

    @ApiModelProperty(value = "标书费支付方式", position = 10)
    private Integer paymentType;

    @ApiModelProperty(value = "标书费是否收费1 收费 ，2免费", position = 11)
    private Integer tenderFeeFlag;

    @ApiModelProperty(value = "关联招标附件oss表ID集合", position = 12)
    private String annexOssFileIds;

    @ApiModelProperty(value = "招标文件进度", position = 13)
    private Integer noticeProgress;

    @ApiModelProperty(value = "材料清单", position = 14)
    private String materialList;

    @ApiModelProperty(value = "已购买招标文件的投标人本次是否收费", position = 15)
    private Integer free;

    @ApiModelProperty(value = "变更记录对比标识", position = 99)
    private Map flag;

    @ApiModelProperty(value = "线上澄清（1 支持 2 不支持）")
    private Integer clarifyOnLine;

    @ApiModelProperty(value ="提出澄清截止时间")
    private Date clarifyEndTime;

    @ApiModelProperty(value ="提出澄清截止时间（1：确定时间; 2：投标文件递交截止时间前一天）")
    private Integer clarifyEndTimeType;

    @ApiModelProperty(value = "线上异议（1 支持 2 不支持）")
    private Integer questionOnLine;

    @ApiModelProperty(value ="提出异议截止时间（1：确定时间; 2：投标文件递交截止时间前一天）")
    private Integer questionEndTimeType;

    @ApiModelProperty(value ="提出异议截止时间")
    private Date questionEndTime;

    @ApiModelProperty(value = "线上开标（1 支持 2 不支持）")
    private Integer bidOpenOnline;

    @ApiModelProperty(value ="解密时间（分钟）")
    private Integer decryptTime;

    @ApiModelProperty(value ="供应商少于x家终止开标")
    private Integer supNumEndBid;

    @ApiModelProperty(value ="关联招标开标一览表oss表ID集合")
    private String scheduleOssFileIds;

    @ApiModelProperty(value ="开标一览表oss文件id")
    private Long bidOpeningFileId;

    @ApiModelProperty(value ="生成规则 1.直接拼接开标汇总表 2.从x行解析拼接")
    private Integer generationType;

}
