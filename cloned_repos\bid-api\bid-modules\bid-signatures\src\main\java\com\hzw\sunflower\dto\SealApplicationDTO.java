package com.hzw.sunflower.dto;

import com.hzw.sunflower.entity.SealApplication;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode
public class SealApplicationDTO extends SealApplication {

    @ApiModelProperty("项目id")
    private Long projectId;

    @ApiModelProperty("项目名称")
    private String projectName;

    @ApiModelProperty("项目编号")
    private String projectNumber;

    @ApiModelProperty("标段id")
    private String sectionId;

    @ApiModelProperty("标段编号")
    private String packageNumber;

    @ApiModelProperty("标段名称")
    private String packageName;

    @ApiModelProperty("电子签章")
    private Long electronId;

    @ApiModelProperty("实体签章")
    private Long entityId;

}