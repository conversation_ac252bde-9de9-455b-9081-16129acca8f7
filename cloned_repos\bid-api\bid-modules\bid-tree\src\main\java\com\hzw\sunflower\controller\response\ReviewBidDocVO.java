package com.hzw.sunflower.controller.response;

import com.hzw.sunflower.dto.ReviewBidDocDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2021/7/13 15:18
 * @description：审核内容返回格式
 * @version: 1.0
 */
@Data
public class ReviewBidDocVO extends ReviewBidDocDTO {

    @ApiModelProperty(value = "附件信息集合", position = 999)
    private List<FileVo> annexList;
}
