package com.hzw.sunflower.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzw.sunflower.dao.OssFileMapper;
import com.hzw.sunflower.entity.OssFile;
import com.hzw.sunflower.service.OssFileService;
import com.hzw.sunflower.util.oss.OssFileStorage;
import com.hzw.sunflower.util.oss.OssUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <p>
 * OSS附件  服务实现类
 * </p>
 *
 * <AUTHOR> Gen
 * @since 2022-11-25
 */
@Service
@Transactional
public class OssFileServiceImpl extends ServiceImpl<OssFileMapper, OssFile> implements OssFileService {

    @Value("${oss.active}")
    private String ossType;

    @Override
    public Long saveOssFile(String fileName,String filePath) throws Exception {
//        OssUtil ossUtil = new OssUtil();
        OssFileStorage storage = OssUtil.getOssFileStorage(ossType);
        String key = storage.uploadFileByPath(filePath);
        if(StringUtils.isNotBlank(key)) {
            OssFile ossFile = new OssFile();
            ossFile.setOssFileKey(key);
            ossFile.setOssFileName(fileName);
            this.save(ossFile);
            return ossFile.getId();
        }else {
            return null;
        }
    }

    @Override
    public OssFile getOssFileById(Long id) {
        return this.getById(id);
    }


}
