package com.hzw.ssm.expert.service;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.hzw.ssm.expert.dao.ExpertNotParticipateMapper;
import com.hzw.ssm.expert.entity.ExpertNotParticipateEntity;

@Service
public class ExpertNotParticipateService {

	@Autowired
	private ExpertNotParticipateMapper enpMapper;

	public List<ExpertNotParticipateEntity> queryPageENPList(ExpertNotParticipateEntity enp) {
		return enpMapper.queryPageENPList(enp);
	}
}
