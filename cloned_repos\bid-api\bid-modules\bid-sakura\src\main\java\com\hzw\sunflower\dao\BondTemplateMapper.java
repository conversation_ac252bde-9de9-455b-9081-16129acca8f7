package com.hzw.sunflower.dao;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hzw.sunflower.dto.BondTemplateDto;
import com.hzw.sunflower.entity.BondTemplate;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hzw.sunflower.entity.condition.BondTemplateCondition;
import org.apache.ibatis.annotations.Mapper;

/**
 * <p>
 * 保证金流水模板 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-28
 */
@Mapper
public interface BondTemplateMapper extends BaseMapper<BondTemplate> {

    /**
     * 分页查询模板
     * @param page
     * @param condition
     * @return
     */
    IPage<BondTemplateDto> listPage(IPage<BondTemplateDto> page, BondTemplateCondition condition);
}
