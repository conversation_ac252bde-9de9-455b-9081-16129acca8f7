package com.hzw.sunflower.controller.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date ：Created in 2021/7/8 15:56
 * @description：前端数据结构
 * @version: 1.0
 */
@Data
@NoArgsConstructor
public class BidFileInfo implements Serializable {

    @ApiModelProperty(value = "招标文件信息ID", position = 1)
    private Long docId;

    @ApiModelProperty(value = "文件key", position = 2)
    private String key;

    @ApiModelProperty(value = "文件name", position = 3)
    private String name;

    @ApiModelProperty(value = "关联标段包号", position = 4)
    private String[] amount;

    @ApiModelProperty(value = "文件类型", position = 5)
    private Integer fileType;

    @ApiModelProperty(value = "关联标段ID", position = 6)
    private String[] sectionIdArray;

    @ApiModelProperty(value = "项目ID", position = 8)
    private Long projectId;

    @ApiModelProperty(value = "oss文件ID", position = 9)
    private Long fileId;

    @ApiModelProperty(value = "附件表ID", position = 10)
    private String[] annexIdArray;

}
