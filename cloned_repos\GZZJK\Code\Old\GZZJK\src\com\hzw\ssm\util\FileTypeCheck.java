package com.hzw.ssm.util;

import com.hzw.ssm.fw.util.CommUtil;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021年04月25日 16:30
 * @Version 1.0
 */
public class FileTypeCheck {

    public static List<String> fileTypes = new ArrayList<>();

    static {
        String[] img = new String[]{".jpg", ".png", ".jpeg", ".gif", ".bmp"};
        String[] word = new String[]{".doc", ".docx"};
        String[] pdf = new String[]{".pdf"};
        String[] excel = new String[]{".xls", ".xlsx"};
        String[] ppt = new String[]{".ppt", ".pptx"};
        String[] txt = new String[]{".txt"};
        String[] archive = new String[]{".rar", ".zip"};
        fileTypes.addAll(Arrays.asList(img));
        fileTypes.addAll(Arrays.asList(word));
        fileTypes.addAll(Arrays.asList(pdf));
        fileTypes.addAll(Arrays.asList(excel));
        fileTypes.addAll(Arrays.asList(ppt));
        fileTypes.addAll(Arrays.asList(txt));
        fileTypes.addAll(Arrays.asList(archive));

    }

    /**
     * @param fileName
     * @return 文件名
     */
    public static String fileTypeCheck(String fileName) {
        String fileNameSuffix = fileName.substring(fileName.lastIndexOf("."));
        boolean contains = fileTypes.contains(fileNameSuffix);
        if (!contains) {
            throw new RuntimeException("上传文件格式错误，请上传正确的文件格式！");
        } else {
            return CommUtil.getKey() + fileNameSuffix;
        }
    }
}
