package com.hzw.sunflower.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @datetime 2024/07/22 15:32
 * @description: 代理服务费统计DTO
 * @version: 1.0
 */
@Data
public class AgencyFeeStatisticDTO {

    /**
     * 项目编号
     */
    private String projectCode;

    /**
     * 代理服务费实收金额
     */
    private BigDecimal amount;

    /**
     * 数据同步时间
     */
    private String lastSyncTime;

}
