/// 
/// * common Bulletin Data View Widget
/// <AUTHOR>
/// @date 20101020

import "package:flutter/material.dart";

import 'package:flutter_main_app/src/constants/theme.dart';
import "package:flutter_main_app/src/fonts/fonts.dart";

class BulletinWidget extends StatelessWidget {
  const BulletinWidget({Key key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    double _screenWidth = MediaQuery.of(context).size.width;
    return Container(
        color: themeContentBackgroundColor,
        constraints: BoxConstraints(
          maxWidth: _screenWidth,
          minWidth: _screenWidth,
          minHeight: 90,
          // maxHeight: 190,
        ),
        child: Column(
          children: <Widget>[
            // 内容区域
            Padding(
              padding: const EdgeInsets.fromLTRB(15, 12, 21, 12),
              child: Text(
                '连云港创联水务有限责任公司力洁污水厂二期污泥干化技改 项目连云港创联水务有限责任公司力洁污水厂二期污泥干化技改 项目',
                style: themeTextStyle,
              ),
            ),
            // 标签区域
            Padding(
             padding: const EdgeInsets.fromLTRB(15, 0, 15, 12),
             child: Row(
                children: <Widget>[
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 8),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.all(new Radius.circular(6)),
                      border: Border.all(
                        color: Color(0xFF3E78E6),
                        width: themeBorderWidth,
                      )
                    ),
                    child: Text(
                      '连云港市',
                      style: TextStyle(
                        color: Color(0xFF3E78E6),
                        fontSize: 10,
                      ),
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.only(left: 10),
                    child: Container(
                      padding: EdgeInsets.symmetric(horizontal: 8),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.all(new Radius.circular(6)),
                        border: Border.all(
                          color: Color(0xFFF9A545),
                          width: themeBorderWidth,
                        )
                      ),
                      child: Text(
                        '拟建项目',
                        style: TextStyle(
                          color: Color(0xFFF9A545),
                          fontSize: 10,
                        ),
                      ),
                    ),
                  ),
                  Expanded(
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: <Widget>[
                        Icon(FontsHelper.clockIconData,size: 10,color: themeHintTextColor),
                        Padding(
                          padding: const EdgeInsets.only(left: 5),
                          child: Text(
                            '2019-05-20',
                            style: TextStyle(
                              color: themeHintTextColor,
                              fontSize: 10,
                            ),
                          ),
                        )
                      ],
                    ),
                  ),
                  
                ],
              ),
            ),
            // 线
            Container(
              width: _screenWidth,
              height: themeBorderWidth,
              color: themeBorderColor,
            )
          ],
        ),
    );
  }
}