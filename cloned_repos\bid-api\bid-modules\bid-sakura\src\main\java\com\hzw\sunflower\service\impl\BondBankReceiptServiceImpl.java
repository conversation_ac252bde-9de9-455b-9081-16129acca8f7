package com.hzw.sunflower.service.impl;


import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzw.sunflower.config.BondBankProperties;
import com.hzw.sunflower.config.BondNjBankProperties;
import com.hzw.sunflower.constant.constantenum.BankLoanFlagEnum;
import com.hzw.sunflower.constant.constantenum.BankTypeEnum;
import com.hzw.sunflower.constant.constantenum.ReceiptTypeEnum;
import com.hzw.sunflower.constant.constantenum.TradeTypeEnum;
import com.hzw.sunflower.controller.request.BondPrintReq;
import com.hzw.sunflower.controller.request.CmbcDataReq;
import com.hzw.sunflower.controller.request.NjBankDataReq;
import com.hzw.sunflower.controller.request.PayReceiptReq;
import com.hzw.sunflower.dao.BondBankReceiptMapper;
import com.hzw.sunflower.dto.NjBankTradeDetDto;
import com.hzw.sunflower.entity.BondBankReceipt;
import com.hzw.sunflower.entity.BondRefundWater;
import com.hzw.sunflower.entity.BondWater;
import com.hzw.sunflower.entity.OssFile;
import com.hzw.sunflower.service.*;
import com.hzw.sunflower.util.*;
import com.hzw.sunflower.util.oss.OssFileStorage;
import com.hzw.sunflower.vo.UnzipFileVo;
import com.jcraft.jsch.ChannelSftp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.File;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <p>
 * 银行回单明细 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-12
 */
@Service
@Slf4j
public class BondBankReceiptServiceImpl extends ServiceImpl<BondBankReceiptMapper, BondBankReceipt> implements BondBankReceiptService {

    @Autowired
    BankService bankService;

    @Autowired
    OssFileService ossFileService;

    @Autowired
    private BondNjBankProperties njBankProperties;

    @Autowired
    private BondBankProperties properties;

    @Autowired
    private BondWaterService bondWaterService;

    @Autowired
    private BondRefundWaterService bondRefundWaterService;

    @Value("${files.temporary.path}")
    private String tempFilePath;

    @Value("${oss.active}")
    private String ossType;

    /**
     * 保存南京银行回单
     * @param list
     * @param bankType
     * @return
     */
    @Override
    public Integer saveNjBankTradeList(List<NjBankTradeDetDto> list, Integer bankType) {
        List<BondBankReceipt> receiptList = new ArrayList<>();
        // 遍历过滤已存在明细
        for (NjBankTradeDetDto dto : list) {
            // 获取回单下载参数
            String guiylius = dto.getGuiylius(); // 柜员流水号
            String jiaoyirq = dto.getJiaoyirq(); // 交易日期
            String jiaoyisj = dto.getJiaoyisj(); // 交易时间
            String jiaoyixh = dto.getJiaoyixh(); // 交易序号
            String jiaoyije = dto.getJiaoyije(); // 交易金额
            String jiedaibz = dto.getJiedaibz(); // 借贷标志 0-借 1-贷

            // 校验是否已存在
            LambdaQueryWrapper<BondBankReceipt> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(BondBankReceipt::getDate, jiaoyirq).eq(BondBankReceipt::getBankType, bankType);
            if (jiedaibz.equals(BankLoanFlagEnum.BORROW.getCode())) {
                queryWrapper.eq(BondBankReceipt::getRelationNum, dto.getBeizhuxx());
            } else {
                queryWrapper.eq(BondBankReceipt::getRelationNum, guiylius);
            }
            long count = this.count(queryWrapper);
            if (count < 1) {
                BondBankReceipt receipt = new BondBankReceipt();
                // 支付回单采用支付号作为关联标识
                if (jiedaibz.equals(BankLoanFlagEnum.BORROW.getCode())) {
                    receipt.setRelationNum(dto.getBeizhuxx()); // 支付号
                    receipt.setReceiptType(TradeTypeEnum.TRADE_PAY.getType());
                } else {
                    receipt.setRelationNum(dto.getGuiylius()); // 流水号
                    receipt.setReceiptType(TradeTypeEnum.TRADE_RECEIVE.getType());
                }
                receipt.setBankType(bankType);
                receipt.setDate(dto.getJiaoyirq());
                receipt.setTime(dto.getJiaoyisj());
                // 调用明细下载接口
                NjBankDataReq req = new NjBankDataReq();
                req.setTellerSerialNum(guiylius);
                req.setTradeDate(jiaoyirq);
                req.setTradeTime(jiaoyisj);
                req.setTradeNo(jiaoyixh);
                req.setTradeAmt(jiaoyije);
                req.setLoanFlag(jiedaibz);
                log.info("获取南京银行回单文件");
                String fileName = bankService.downTradeCircular(req);
                // 获取文件成功才入库 由于是准实时，定时任务轮询解析文件
                if (fileName != null) {
                    log.info("获取南京银行回单文件：获取成功" + fileName);
                    receipt.setFileName(fileName);
                    receiptList.add(receipt);
                }
            }
        }
        // 入库
        boolean result = this.saveBatch(receiptList);
        if (!result) {
            return 0;
        }
        return receiptList.size();
    }

    /**
     * 解析南京银行回单文件
     * @return
     */
    @Override
    public Integer dealNjBankTradeDetFile() {
        // 获取待解析的回单明细数据
        LambdaQueryWrapper<BondBankReceipt> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BondBankReceipt::getBankType, BankTypeEnum.NANJING.getType()).isNull(BondBankReceipt::getReceiptOssFileId);
        List<BondBankReceipt> receiptList = this.list(queryWrapper);
        List<BondBankReceipt> list = new ArrayList<>();
        try {
            // 根据文件名从ftp服务器获取文件
            for (BondBankReceipt receipt : receiptList) {
                String fileName = receipt.getFileName();
                //先判断ok文件是否存在，存在则下载回单文件
                log.info("获取南京银行回单文件：判断ok文件");
                Boolean exist = SftpUtils.fileExist(receipt.getFileName(), njBankProperties.getTradeRemotePath());
                if (exist) {
                    log.info("获取南京银行回单文件：" + fileName + "ok文件已存在");
                    String filePath = SftpUtils.getFile(fileName, njBankProperties.getTradeRemotePath(), njBankProperties.getTradeLocalPath());
                    if (filePath != null && !filePath.equals("")) {
                        // 上传至oss
                        log.info("获取南京银行回单文件：开始上传文件");
                        Long ossFileId = ossFileService.saveOssFile(fileName, filePath);
                        log.info("获取南京银行回单文件：上传成功" + ossFileId);
                        BondBankReceipt temp = new BondBankReceipt();
                        BeanUtils.copyProperties(receipt, temp);
                        temp.setReceiptOssFileId(ossFileId);
                        list.add(temp);
                        // 操作完成 删除临时文件
                        File file = new File(filePath);
                        if (file.exists()) {
                            file.delete();
                        }
                    }
                }
            }
            // 更新
            this.updateBatchById(list);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return list.size();
    }

    @Override
    public Integer dealNjBankTradeDetFileNew() {
        // 获取待解析的回单明细数据
        LambdaQueryWrapper<BondBankReceipt> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BondBankReceipt::getBankType, BankTypeEnum.NANJING.getType()).isNull(BondBankReceipt::getReceiptOssFileId);
        List<BondBankReceipt> receiptList = this.list(queryWrapper);
        List<BondBankReceipt> list = new ArrayList<>();
            // 根据文件名从ftp服务器获取文件
            for (BondBankReceipt receipt : receiptList) {
                String fileName = receipt.getFileName();
                //先判断ok文件是否存在，存在则下载回单文件
                log.info("获取南京银行回单文件：判断ok文件");
                File file = null;
                ChannelSftp sftp =null;
                try {
                    sftp = SftpUtils.sftpConnection();
                    Boolean exist = SftpUtils.fileExist(receipt.getFileName(), njBankProperties.getTradeRemotePath(),sftp);
                    long sftpFileSize = SftpUtils.getSFTPFileSize(receipt.getFileName(), njBankProperties.getTradeRemotePath(),sftp);
                    if (exist) {
                        log.info("获取南京银行回单文件：" + fileName + "ok文件已存在");
                        String filePath = SftpUtils.getFileNew(fileName, njBankProperties.getTradeRemotePath(), njBankProperties.getTradeLocalPath(),sftp);
                         file = new File(filePath);
                        if (filePath != null && !filePath.equals("") && sftpFileSize == file.length()) {
                            // 上传至oss
                            log.info("获取南京银行回单文件：开始上传文件");
                            Long ossFileId = ossFileService.saveOssFile(fileName, filePath);
                            log.info("获取南京银行回单文件：上传成功" + ossFileId);
                            BondBankReceipt temp = new BondBankReceipt();
                            BeanUtils.copyProperties(receipt, temp);
                            temp.setReceiptOssFileId(ossFileId);
                            list.add(temp);

                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }finally {
                    if(null != sftp){
                        SftpUtils.sftpClose(sftp);
                    }
                    // 操作完成 删除临时文件
                    if(null != file && file.exists()){
                       file.delete();
                    }
                }
            }
            // 更新
            this.updateBatchById(list);

        return list.size();
    }

    /**
     * 保存中国银行回单文件
     * @param tratype
     */
    @Override
    public void saveChinaBankReceipt(String tratype) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        String[] actacns = properties.getBondCollectNo().split(",");
        for (String bankNo : actacns) {
            PayReceiptReq receiptReq = new PayReceiptReq();
            receiptReq.setTratype(tratype);
            receiptReq.setDateFrom(sdf.format(DateUtil.yesterday()));
            receiptReq.setDateTo(sdf.format(DateUtil.yesterday()));
            receiptReq.setActacn(bankNo);
            JSONObject jsonObject = bankService.payReceipt(receiptReq);
            /*
             * {
             *     "msg": "ok",
             *     "filename": "396003910_********_7802_221008095939TE.zip",
             *     "code": "B001"
             * }
             */
            if (jsonObject.getString("code").equals("B001") || (jsonObject.getString("code").equals("B002"))) {
                BondBankReceipt receipt = new BondBankReceipt();
                String filename = jsonObject.getString("filename");
                //获取sftp文件 并下载到临时文件
                log.info("获取中国银行回单文件：开始获取文件" + filename);
                ChannelSftp channelSftp = SftpUtils.sftpConnection(properties.getSftpHost(), properties.getSftpPort(), properties.getSftpUserName(), properties.getSftpPwd());
                String filePath = SftpUtils.getFile(filename, properties.getSftpDownloadPath(), tempFilePath,channelSftp);
                if (filePath != null && !filePath.equals("")) {
                    //解析zip
                    File file = new File(filePath);
                    List<UnzipFileVo> unzips = UnZipUtils.unzip(file, tempFilePath);
                    for (UnzipFileVo fileVo : unzips) {
                        //文件名:********+************+*********.pdf
                        String pdf = fileVo.getFileName();
                        String[] split = pdf.split("\\+");
                        String waterNo = split[2].substring(0, split[2].lastIndexOf(".pdf"));
                        BondRefundWater water = new BondRefundWater();
                        LambdaQueryWrapper<BondBankReceipt> receiptWrapper = new LambdaQueryWrapper<>();
                        if (tratype.equals("C")) {//来帐信息查询流水表
                            LambdaQueryWrapper<BondWater> waterQuery = new LambdaQueryWrapper<>();
                            waterQuery.eq(BondWater::getWaterNumber, waterNo)
                                    .last("limit 1");
                            BondWater one = bondWaterService.getOne(waterQuery);
                            water = BeanListUtil.convert(one, BondRefundWater.class);

                            receiptWrapper.eq(BondBankReceipt::getRelationNum,waterNo);
                        } else if (tratype.equals("D")) {//往账信息查询退还流水表
                            LambdaQueryWrapper<BondRefundWater> waterQuery = new LambdaQueryWrapper<>();
                            waterQuery.eq(BondRefundWater::getWaterNumber, waterNo)
                                    .last("limit 1");
                            water = bondRefundWaterService.getOne(waterQuery);

                            receiptWrapper.eq(BondBankReceipt::getRelationNum,water.getInsid());
                        }
                        //查询库中是否存在
                        receiptWrapper.eq(BondBankReceipt::getBankType,BankTypeEnum.CHINA.getType());
                        Long count = this.count(receiptWrapper);
                        if (water != null && count<1) {
                            //上传到oss
                            Long ossFileId = null;
                            try {
                                log.info("获取中国银行回单文件：开始上传文件" + pdf);
                                ossFileId = ossFileService.saveOssFile(pdf, tempFilePath + pdf);
                                log.info("获取中国银行回单文件：上传成功" + ossFileId);
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                            receipt.setDate(water.getDate());
                            receipt.setTime(water.getTime());
                            receipt.setBankType(BankTypeEnum.CHINA.getType());
                            receipt.setReceiptOssFileId(ossFileId);

                            if (tratype.equals("C")) {//来帐信息查询流水表
                                receipt.setRelationNum(waterNo);
                                receipt.setReceiptType(ReceiptTypeEnum.COLLECTION_WATER.getType());
                            } else if (tratype.equals("D")) {//往账信息查询退还流水表
                                receipt.setRelationNum(water.getInsid());
                                receipt.setReceiptType(ReceiptTypeEnum.PAY_WATER.getType());
                            }
                            this.save(receipt);
                        }
                        // 操作完成 删除临时文件
                        File outfile = new File(tempFilePath + pdf);
                        if (outfile.exists()) {
                            outfile.delete();
                        }
                    }
                    //删除下载下来的临时zip文件
                    if (file.exists()) {
                        file.delete();
                    }
                }
            }
        }
    }

    /**
     * 保存民生银行回单
     * @param array
     * @param type
     * @return
     */
    @Override
    public Integer saveCmbcTradeList(JSONArray array, Integer type) {
        List<BondBankReceipt> receiptList = new ArrayList<>();
        // 遍历过滤已存在明细
        for (int i = 0; i < array.size(); i++) {
            JSONObject jsonObject = array.getJSONObject(i);
            // 获取回单下载所需参数
            String fileName = jsonObject.getString("FileName");
            String innerNo = jsonObject.getString("innerNo");
            String loanFlag = jsonObject.getString("LoanFlag");
            String svrId = jsonObject.getString("svrId");
            String trsDate = jsonObject.getString("TrsDate");
            // 校验是否已存在
            LambdaQueryWrapper<BondBankReceipt> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(BondBankReceipt::getDate, trsDate).eq(BondBankReceipt::getBankType, type);
            queryWrapper.eq(BondBankReceipt::getRelationNum, svrId);
            long count = this.count(queryWrapper);
            if (count < 1) {
                BondBankReceipt receipt = new BondBankReceipt();
                receipt.setRelationNum(svrId);
                // 支付回单采用支付号作为关联标识
                if (loanFlag.equals(BankLoanFlagEnum.BORROW.getCode())) {
                    receipt.setReceiptType(TradeTypeEnum.TRADE_PAY.getType());
                } else {
                    receipt.setReceiptType(TradeTypeEnum.TRADE_RECEIVE.getType());
                }
                receipt.setBankType(type);
                receipt.setDate(trsDate);

                // 调用明细下载接口
                CmbcDataReq req = new CmbcDataReq();
                req.setInnerNo(innerNo);
                req.setFileName(fileName);
                JSONObject json = bankService.b2eElectNoteDownLoadNew(req);
                log.info("民生银行回单下载返回结果：{}", json.toJSONString());
                String fileContent = json.getJSONArray("xDataBody").getJSONObject(0).getString("FileContent");
                String fileNameDownload = json.getJSONArray("xDataBody").getJSONObject(0).getString("FileName");
                // 上传oss
                Long ossFileId = ossFileService.saveOssBase64File(fileNameDownload, fileContent);
                log.info("民生银行回单文件上传成功：" + ossFileId);
                receipt.setReceiptOssFileId(ossFileId);

                receiptList.add(receipt);
            }
        }
        // 入库
        boolean result = this.saveBatch(receiptList);
        if (!result) {
            return 0;
        }
        return receiptList.size();
    }

    /**
     * 打印回单
     * @param req
     * @return
     */
    @Override
    public List<String> printReceipt(BondPrintReq req) {
        List<String> receiptList = new ArrayList<>();
        List<String> ossFileIds = Arrays.asList(req.getOssFileIds().split(","));
        if (ossFileIds.size() > 0) {
            List<OssFile> list = ossFileService.listByIdsOrder(ossFileIds);
            OssFileStorage storage = OssUtil.getOssFileStorage(ossType);
            for (OssFile ossFile : list) {
                byte[] bytes = storage.downloadFile(ossFile.getOssFileKey());
                List<String> jpgs = FileUtils.pdf2Base64(bytes);
                receiptList.addAll(jpgs);
                bytes=null;
            }
        }
        return receiptList;
    }
}
