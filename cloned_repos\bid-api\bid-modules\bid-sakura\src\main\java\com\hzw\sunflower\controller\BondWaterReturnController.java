package com.hzw.sunflower.controller;

import com.hzw.sunflower.common.BaseController;
import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.common.annotation.RepeatSubmit;
import com.hzw.sunflower.entity.BondWaterReturn;
import com.hzw.sunflower.entity.BondWaterlabel;
import com.hzw.sunflower.service.BondWaterReturnService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @ClassName:BondWaterReturnController
 * @Auther: lijinxin
 * @Description: 保证金其他流水退还
 * @Date: 2023/5/11 19:46
 * @Version: v1.0
 */
@Api(tags = "保证金其他流水退还")
@RestController
@RequestMapping("/bondWaterReturn")
public class BondWaterReturnController extends BaseController {

    @Autowired
    private BondWaterReturnService bondWaterReturnService;


    /**
     * 新增保证金其他流水退还
     * @param bondWaterReturn
     * @return
     */
    @ApiOperation(value = "新增保证金其他流水退还")
    @ApiImplicitParam(name = "bondWaterReturn", value = "保证金其他流水退还", required = true, dataType = "BondWaterReturn", paramType = "body")
    @PostMapping(value = "/add")
    @RepeatSubmit
    public Result<Object> add(@RequestBody BondWaterReturn bondWaterReturn) {
        return bondWaterReturnService.add(bondWaterReturn);
    }
}
