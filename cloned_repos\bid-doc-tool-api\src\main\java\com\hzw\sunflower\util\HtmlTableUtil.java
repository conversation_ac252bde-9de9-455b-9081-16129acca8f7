package com.hzw.sunflower.util;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * HTML表格工具类
 */
public class HtmlTableUtil {

    /**
     * 评标办法初步评审表格
     * @param list
     * @return
     */
    public static StringBuilder bidDocEvaluationPreliminaryTable(List<Map<String, String>> list) {
        StringBuilder rowsBuilder = new StringBuilder();

        // 按第一列（evaluationApproach）进行分组
        Map<String, List<Map<String, String>>> groupedData = new LinkedHashMap<>();
        for (Map<String, String> row : list) {
            String secondColumn = row.get("evaluationApproach");
            groupedData.computeIfAbsent(secondColumn, k -> new ArrayList<>()).add(row);
        }

        // 处理数据合并
        int sequence1 = 1;
        int sequence2 = 1;
        for (Map.Entry<String, List<Map<String, String>>> entry : groupedData.entrySet()) {
            String secondColumn = entry.getKey();
            List<Map<String, String>> rows = entry.getValue();
            boolean firstRow = true;
            for (Map<String, String> row : rows) {
                rowsBuilder.append("<tr>");
                if (firstRow) {
                    if (sequence1 == 1) {
                        rowsBuilder.append("<td rowspan='").append(rows.size()).append("'>").append(sequence1).append("</td>");
                    } else {
                        rowsBuilder.append("<td rowspan='").append(rows.size()).append("'>").append("2.1.").append(sequence2).append("</td>");
                        sequence2 = sequence2 + 1;
                    }
                    sequence1 = sequence1 + 1;
                    rowsBuilder.append("<td rowspan='").append(rows.size()).append("'>").append(secondColumn).append("</td>");
                    firstRow = false;
                }
                rowsBuilder.append("<td>").append(row.get("reviewFactor")).append("</td>");
                rowsBuilder.append("<td>").append(row.get("reviewCriteria")).append("</td>");
                rowsBuilder.append("</tr>");
            }
        }
        return rowsBuilder;
    }

    /**
     * 评标办法表格
     * 分值构成，评标基准价计算方法，投标报价的偏差率计算公式
     * @param list
     * @return
     */
    public static StringBuilder bidDocEvaluationTable(List<Map<String, String>> list) {
        StringBuilder rowsBuilder = new StringBuilder();
        // 合并
        Map<String, List<Map<String, String>>> groupedData = new LinkedHashMap<>();
        for (Map<String, String> row : list) {
            String secondColumn = row.get("row2");
            groupedData.computeIfAbsent(secondColumn, k -> new ArrayList<>()).add(row);
        }

        for (Map.Entry<String, List<Map<String, String>>> entry : groupedData.entrySet()) {
            List<Map<String, String>> rows = entry.getValue();
            for (Map<String, String> row : rows) {
                rowsBuilder.append("<tr>");
                rowsBuilder.append("<td colspan='2'").append(">").append(row.get("row1")).append("</td>");
                rowsBuilder.append("<td>").append(row.get("row3")).append("</td>");
                rowsBuilder.append("<td>").append(row.get("row4")).append("</td>");
                rowsBuilder.append("</tr>");
            }
        }
        return rowsBuilder;
    }

    /**
     * 详细评审评标办法表格
     * @param list
     * @return
     */
    public static StringBuilder bidDocEvaluationDetailsTable(List<Map<String, String>> list) {
        StringBuilder rowsBuilder = new StringBuilder();

        // 按第一列（scoreFactor）进行分组
        Map<String, List<Map<String, String>>> groupedData = new LinkedHashMap<>();
        for (Map<String, String> row : list) {
            String secondColumn = row.get("scoreFactor");
            groupedData.computeIfAbsent(secondColumn, k -> new ArrayList<>()).add(row);
        }

        // 处理数据合并
        int sequence = 1;
        for (Map.Entry<String, List<Map<String, String>>> entry : groupedData.entrySet()) {
            String secondColumn = entry.getKey();
            List<Map<String, String>> rows = entry.getValue();
            boolean firstRow = true;
            for (Map<String, String> row : rows) {
                rowsBuilder.append("<tr>");
                if (firstRow) {
                    rowsBuilder.append("<td rowspan='").append(rows.size()).append("'>").append("2.2.4.").append(sequence).append("</td>");
                    rowsBuilder.append("<td rowspan='").append(rows.size()).append("'>").append(secondColumn).append("</td>");
                    sequence = sequence + 1;
                    firstRow = false;
                }
                rowsBuilder.append("<td>").append(row.get("scoreCriteria")).append("</td>");
                rowsBuilder.append("<td>").append(row.get("scoreApproach")).append("</td>");
                rowsBuilder.append("</tr>");
            }
        }
        return rowsBuilder;
    }

}
