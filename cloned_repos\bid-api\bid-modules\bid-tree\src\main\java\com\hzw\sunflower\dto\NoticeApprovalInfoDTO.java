package com.hzw.sunflower.dto;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 公告审批基本信息
 *
 * <AUTHOR> 2021/07/12
 * @version 1.0.0
 */

@ApiModel(description = "公告审批的基本信息")
@Data
@Accessors(chain = true)
public class NoticeApprovalInfoDTO {

    /**
     * 公告ID
     */
    private Long noticeId;
    /**
     * 项目名称
    */
    private String projectName;
    /**
     * 项目编号
     */
    private String projectNumber;
    /**
     * 公告类型;1 采购公告；2 补充公告；3 采购结果公示；4 中标候选人公示
     */
    private Integer noticeType;
    /**
     * 包段编号
     */
    private List<String> packageNumbers;
    /**
     * 公告进度;2 审核中；3 已撤回；6 已退回
     */
    private Integer noticeProgress;
}
