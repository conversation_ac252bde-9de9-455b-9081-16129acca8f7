package com.hzw.sunflower.constant.constantenum;

/**
 * 用户授权方式
 *
 * <AUTHOR>
 */
public enum AuthorizationTypeEnum {
    ALL_PROJECT(1, "全项目授权"),
    ONE_PROJECT(2, "单项目授权");
    private Integer type;
    private String desc;

    AuthorizationTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
