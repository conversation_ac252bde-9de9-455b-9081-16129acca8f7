<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzw.sunflower.dao.UserAgentRecordMapper">

    <!-- 根据用户id获取上一次填写的代理机构或招标人信息 -->
    <select id="getByUserId" resultType="com.hzw.sunflower.dto.UserAgentRecordDTO">
        SELECT
            r.agent_id,
            c.company_name AS agent_company,
            r.agent_charge_id,
            r.agent_charge_name,
            r.agent_charge_phone,
            d.id AS departmentId,
            d.department_name
        FROM
            t_user_agent_record r
            LEFT JOIN t_department d ON d.id = r.agent_depart_id AND d.is_delete = 0
            LEFT JOIN t_company c ON r.agent_id = c.id
        WHERE
            r.user_id = #{userId}
            AND r.user_identity = #{userIdentity}
            AND r.is_delete = 0
		    and c.is_delete = 0
        ORDER BY
            r.created_time DESC
            LIMIT 1
    </select>
</mapper>