package com.hzw.bid.util;

import com.spire.pdf.PdfDocument;
import com.spire.pdf.security.PdfCertificate;
import com.spire.pdf.security.PdfSignature;
import com.spire.pdf.widget.PdfFormFieldWidgetCollection;
import com.spire.pdf.widget.PdfFormWidget;
import com.spire.pdf.widget.PdfSignatureFieldWidget;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;

/**
 * <AUTHOR>
 * @datetime 2023/03/07 15:59
 * @description: pdf签章工具类
 * @version: 1.0
 */
@Slf4j
public class PdfSignatureUtil {

    /**
     * 电子验签
     * @param filePath pdf签章文件路径
     * @param name 签名者
     * @return
     */
    public static Boolean verifySignature(String filePath, String name) {
        boolean flag = false;
        //创建PdfDocument实例
        PdfDocument pdf = new PdfDocument();
        //加载含有签名的PDF文件
        pdf.loadFromFile(filePath);
        //获取域集合
        PdfFormWidget pdfFormWidget = (PdfFormWidget) pdf.getForm();
        PdfFormFieldWidgetCollection pdfFormFieldWidgetCollection = pdfFormWidget.getFieldsWidget();
        //遍历域
        for (int i = 0; i < pdfFormFieldWidgetCollection.getCount(); i++) {
            //判定是否为签名域
            if (pdfFormFieldWidgetCollection.get(i) instanceof PdfSignatureFieldWidget) {
                //获取签名域
                PdfSignatureFieldWidget signatureFieldWidget = (PdfSignatureFieldWidget) pdfFormFieldWidgetCollection.get(i);
                //获取签名
                PdfSignature signature = signatureFieldWidget.getSignature();
                //获取签名证书信息
                PdfCertificate certificateInfo = signature.getCertificate();
                log.info("签名证书信息：{}", certificateInfo.getSubject());
                if (certificateInfo.getSubject().contains(name)) {
                    flag = true;
                    break;
                }
            }
        }
        return flag;
    }


    public static Boolean hasSignature(MultipartFile file) {
        boolean flag = false;
        try {
            //创建PdfDocument实例
            PdfDocument pdf = new PdfDocument();
            //加载含有签名的PDF文件
            pdf.loadFromBytes(file.getBytes());
            //获取域集合
            PdfFormWidget pdfFormWidget = (PdfFormWidget) pdf.getForm();
            PdfFormFieldWidgetCollection pdfFormFieldWidgetCollection = pdfFormWidget.getFieldsWidget();
            //遍历域
            for (int i = 0; i < pdfFormFieldWidgetCollection.getCount(); i++) {
                //判定是否为签名域
                if (pdfFormFieldWidgetCollection.get(i) instanceof PdfSignatureFieldWidget) {
                    //获取签名域
                    PdfSignatureFieldWidget signatureFieldWidget = (PdfSignatureFieldWidget) pdfFormFieldWidgetCollection.get(i);
                    //获取签名
                    PdfSignature signature = signatureFieldWidget.getSignature();
                    //获取签名证书信息
                    PdfCertificate certificateInfo = signature.getCertificate();
                    log.info("签名证书信息：{}", certificateInfo.getSubject());
                    if (certificateInfo != null) {
                        flag = true;
                        break;
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return flag;
    }

}
