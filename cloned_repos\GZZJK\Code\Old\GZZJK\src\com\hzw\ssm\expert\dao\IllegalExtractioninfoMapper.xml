<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper 
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hzw.ssm.expert.dao.IllegalExtractioninfoMapper">
<resultMap type="com.hzw.ssm.expert.entity.IllegalExtractioninfoEntity" id="IllegalExtractioninfoEntity">
		<result column="DECIMATIONBATCH" property="decimationbatch"></result>
		<result column="USERID" property="userId"></result>
		<result column="USERNAME" property="userName"></result>
		<result column="ILLEGALTIME" property="illegalTime"></result>
		<result column="DEPARTMENTNAME" property="departmentName"></result>
		<result column="HANDLEFILE" property="handleFile"></result>
		<result column="HANDLETIME" property="handleTime"></result>
		<result column="ISHANDLE" property="isHandle"></result>
		<result column="PROJECT_CODE" property="projectCode"></result>
		<result column="OPENINGTIME" property="openingTime"></result>
		<result column="PROJECT_NAME" property="projectName"></result>
		<result column="OPENINGADDRESS" property="openingAddress"></result>
		<result column="EXTRACT_NUM" property="extractNum"></result>
		<result column="NEED_NUM" property="needNum"></result>
		<result column="EXTRACT_TIMES" property="extractTimes"></result>
		<result column="EXAMINE_NAME" property="examineName"></result>
		<result column="EXAMINE_TIME" property="examineTime"></result>
	</resultMap>

<select id="queryPageillegalExtraction" parameterType="IllegalExtractioninfoEntity" resultType="IllegalExtractioninfoEntity">
	  select decimationbatch,
             userid userId,
             username userName,
             illegaltime illegalTime,
             departmentname departmentName,
             handlefile handleFile,
             handletime handleTime,
             ishandle isHandle,
             project_code projectCode,
             openingtime,
             project_name projectName,
             openingaddress openingAddress,
             extract_num extractNum,
             need_num needNum,
             extract_times extractTimes,
             EXAMINE_NAME examineName,
             EXAMINE_TIME examineTime
        from t_illegal_extractioninfo
        <where>
        	<if test="beginTime!=null and beginTime!=''">
					and OPENINGTIME>=#{beginTime}
			</if>
			<if test="endTime!=null and endTime!=''">
					and OPENINGTIME<![CDATA[<=]]>#{endTime}
			</if>

			<if test ="extractTimes != null and extractTimes != ''">
				and extract_times>#{extractTimes}
			</if>
			<if test="isHandle !=null">
				and ishandle =#{isHandle}
			</if>
			<if test="departmentName !=null and departmentName !=''">
				and departmentname =#{departmentName}
			</if>

            <if test="userName != null and userName != ''">
                AND username LIKE '%' || #{userName} || '%'
            </if>

            <if test="projectCode != null and projectCode != ''">
                AND project_code LIKE '%' || #{projectCode} || '%'
            </if>

        </where>
        <if test="reorder !=null and reorder !=''">
				order by ${reorder}
		</if>
</select>

    <select id="queryPageExpertsDrawnAfterBidOpening"
            parameterType="IllegalExtractioninfoEntity" resultType="IllegalExtractioninfoEntity">

select a.* from (
        select distinct
        P.DECIMATIONBATCH decimationbatch,
        P.PROJECT_NO projectCode,
        P.PROJECT_NAME projectName,
        (

        select u.USER_NAME from TS_USER u where p.MANAGER=u.USER_ID



        ) userName,
        P.BID_TIME openingtime,
        p.BID_ADDRESS openingAddress,
        (select max(m.SORT) from T_EXTRACT_RESULT m where p.DECIMATIONBATCH=m.DECIMATIONBATCH  ) as extractTimes
        from T_PROJECT P left join  T_EXTRACT_RESULT E on E.EXTRACT_TIME>=p.BID_TIME
        where  p.DECIMATIONBATCH=E.DECIMATIONBATCH
      

            <if test="beginTime!=null and beginTime!=''">
                and P.BID_TIME>=#{beginTime}
            </if>
            <if test="endTime!=null and endTime!=''">
                and P.BID_TIME<![CDATA[<=]]>#{endTime}
            </if>


            <if test="projectCode != null and projectCode != ''">
                AND P.PROJECT_NO LIKE '%' || #{projectCode} || '%'
            </if>


        <if test="reorder !=null and reorder !=''">
            order by ${reorder}
        </if>
        ) a
        <where>
        <if test="userName != null and userName != ''">
             a.userName LIKE '%' || #{userName} || '%'
        </if>
        </where>

    </select>



    <select id="pdfMonitorillegalExtraction" parameterType="IllegalExtractioninfoEntity" resultType="IllegalExtractioninfoEntity">

        select a.* from (
        select distinct
        P.DECIMATIONBATCH decimationbatch,
        P.PROJECT_NO projectCode,
        P.PROJECT_NAME projectName,
        (

        select u.USER_NAME from TS_USER u where p.MANAGER=u.USER_ID



        ) userName,
        P.BID_TIME openingtime,
        p.BID_ADDRESS openingAddress,
        (select max(m.SORT) from T_EXTRACT_RESULT m where p.DECIMATIONBATCH=m.DECIMATIONBATCH  ) as extractTimes
        from T_PROJECT P left join  T_EXTRACT_RESULT E on E.EXTRACT_TIME>=p.BID_TIME
        where  p.DECIMATIONBATCH=E.DECIMATIONBATCH


        <if test="beginTime!=null and beginTime!=''">
            and P.BID_TIME>=#{beginTime}
        </if>
        <if test="endTime!=null and endTime!=''">
            and P.BID_TIME<![CDATA[<=]]>#{endTime}
        </if>


        <if test="projectCode != null and projectCode != ''">
            AND P.PROJECT_NO LIKE '%' || #{projectCode} || '%'
        </if>


        <if test="reorder !=null and reorder !=''">
            order by ${reorder}
        </if>
        ) a
        <where>
            <if test="userName != null and userName != ''">
                a.userName LIKE '%' || #{userName} || '%'
            </if>
        </where>
    </select>



    <select id="queryillegalExtraction" parameterType="IllegalExtractioninfoEntity" resultType="IllegalExtractioninfoEntity">
	  select decimationbatch,
             userid userId,
             username userName,
             illegaltime illegalTime,
             departmentname departmentName,
             handlefile handleFile,
             handletime handleTime,
             ishandle isHandle,
             project_code projectCode,
             openingtime openingTime,
             project_name projectName,
             openingaddress openingAddress,
             extract_num extractNum,
             need_num needNum,
             extract_times extractTimes,
             EXAMINE_NAME examineName,
             EXAMINE_TIME examineTime
        from t_illegal_extractioninfo
        <where>
        	<if test="beginTime!=null and beginTime!=''">
					and OPENINGTIME>=#{beginTime}
			</if>
			<if test="endTime!=null and endTime!=''">
					and OPENINGTIME<![CDATA[<=]]>#{endTime}
			</if>

			<if test ="extractTimes != null and extractTimes != ''">
				and extract_times>#{extractTimes}
			</if>
			<if test="departmentName !=null and departmentName !=''">
				and departmentname =#{departmentName}
			</if>
        </where>
        <if test="reorder !=null and reorder !=''">
				order by ${reorder}
		</if>
</select>

<select id="checkExtractFrequenc" parameterType="IllegalExtractioninfoEntity" resultType="IllegalExtractioninfoEntity">
 select decimationbatch,
             userid userId,
             username userName,
             illegaltime illegalTime,
             departmentname departmentName,
             handlefile handleFile,
             handletime handleTime,
             ishandle isHandle,
             project_code projectCode,
             openingtime openingTime,
             project_name projectName,
             openingaddress openingAddress,
             extract_num extractNum,
             need_num needNum,
             extract_times extractTimes,
             EXAMINE_NAME examineName,
             EXAMINE_TIME examineTime,
             tender
        from t_illegal_extractioninfo
        where departmentName = #{departmentName}
        and to_char(illegaltime,'yyyy-mm-dd')  <![CDATA[  <  ]]> to_char(#{endTime},'yyyy-mm-dd')
        and ishandle !='40'
</select>
<select id="getUserNameToId" parameterType="String" resultType="UserEntity">
select t.user_id,t.user_name from ts_user  t where user_name= #{userName}
</select>


    <update id="modifyMonitor" parameterType="IllegalExtractioninfoEntity">
	update t_illegal_extractioninfo set ISHANDLE = #{isHandle} ,
	EXAMINE_NAME = #{examineName},
	EXAMINE_TIME = #{examineTime}
	where DECIMATIONBATCH = #{decimationbatch}
</update>

<update id="modifyMonitorFile" parameterType="IllegalExtractioninfoEntity">
update t_illegal_extractioninfo set ISHANDLE = #{isHandle} ,
	handlefile = #{handleFile},
	handletime = #{handleTime}
	where DECIMATIONBATCH = #{decimationbatch}
	</update>



</mapper>