package com.hzw.sunflower.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hzw.sunflower.dto.BondPayInfoDto;
import com.hzw.sunflower.dto.SyncNccRefundInfoDto;
import com.hzw.sunflower.entity.BondRefundDetails;

import java.util.List;

/**
 * <p>
 * 保证金退还服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-27
 */
public interface BondRefundDetailsService extends IService<BondRefundDetails> {


    /**
     * 查询退还详情（退还成功/处理中的不查）
     * @return
     */
    List<BondRefundDetails> getDetatisForRefund(Long refundId);

    /**
     * 查询同步到ncc的退还信息
     * @param id
     * @return
     */
    List<SyncNccRefundInfoDto> getSyncNccRefundInfo(Long id);


    /**
     * 刷新保证金退还状态
     * @param dto
     * @return
     */
    List<Long> refreshBondPayStatus(BondPayInfoDto dto);
}
