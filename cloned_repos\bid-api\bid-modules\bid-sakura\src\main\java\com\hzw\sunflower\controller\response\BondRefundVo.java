package com.hzw.sunflower.controller.response;

import com.hzw.sunflower.entity.BondRefundDetails;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 保证金退还表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-27
 */
@Data
public class BondRefundVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("标段id")
    private Long sectionId;

    @ApiModelProperty("流水id")
    private Long waterId;

    @ApiModelProperty("申请退还id")
    private Long applyRefundId;

    @ApiModelProperty("企业id")
    private Long companyId;

    @ApiModelProperty(value = "保证金金额")
    private BigDecimal bondMoney;

    @ApiModelProperty(value = "代理服务费")
    private BigDecimal agencyFee;

    @ApiModelProperty(value = "原代理服务费")
    private BigDecimal formerAgencyFee;

    @ApiModelProperty(value = "服务费收取方式：1：保证金转代理服务费  2：保证金全额转代理服务费并补差价  3：保证金全额退还，代理服务费另汇")
    private Integer agencyFeeType;

    @ApiModelProperty(value = "保证金利息")
    private BigDecimal rates;

    @ApiModelProperty(value = "保证金退还金额")
    private BigDecimal refundMoney;

    @ApiModelProperty(value = "状态：1：待处理  2：已退回    3：退还成功 4：退还失败")
    private Integer status;

    @ApiModelProperty(value = "退还流程code")
    private String refundProcessCode;

    @ApiModelProperty(value = "是否异常1：是  2:否")
    private Integer isAbnormal;

    @ApiModelProperty(value = "异常时间")
    private Date abnormalTime;

    @ApiModelProperty(value = "异常原因")
    private String abnormalReson;

    @ApiModelProperty(value = "付款凭证/授权函")
    private String fileIds;

    @ApiModelProperty(value = "退还类型：1 ：项目经理申请退还  2：异常保证金退还  3：供应商申请退还")
    private Integer refundType;

    @ApiModelProperty(value = "退还方式 1：收取并退还剩余保证金  2：收取并保留剩余保证金")
    private Integer refundMode;

    @ApiModelProperty(value = "提交时间")
    private Date submitTime;

    @ApiModelProperty(value = "退还时间")
    private Date refundTime;

    @ApiModelProperty(value = "退还附件")
    private String refundFiles;

    @ApiModelProperty(value = "1:线上 2:线下")
    private Integer refundWay;

    @ApiModelProperty(value = "1:按流水收取  2：无流水收取")
    private Integer bondType;

    private List<BondRefundDetails> bondRefundDetails;

}
