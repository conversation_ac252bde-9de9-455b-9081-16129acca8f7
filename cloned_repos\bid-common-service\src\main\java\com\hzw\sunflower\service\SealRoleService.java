package com.hzw.sunflower.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hzw.sunflower.dto.SealRoleDTO;
import com.hzw.sunflower.entity.SealRole;
import com.hzw.sunflower.entity.User;

import java.util.List;


/**
* SealRoleService接口
*
 * <AUTHOR>
 */
public interface SealRoleService extends IService<SealRole> {
    /**
     * 查询当前业务所需角色
     * @param businessCode
     * @return
     */
    List<SealRole> getRoleByBusinessCode(String businessCode);

    /**
     * 查询业务code对应的用户
     * @param businessCode
     * @return
     */
    List<User> queryNextRole(String businessCode);

    /**
     * 根据处室 和业务code查询分管领导
     * @param departId
     * @param seal
     * @return
     */
    List<User> queryFGLD(Long departId, String seal);

    /**
     * 查询领导审批
     */
    List<SealRoleDTO> querySealLeader(Long companyId);

    /**
     * 查询领导审批code
     * @param companyId
     * @return
     */
    List<SealRole> querySealLeaderCode(Long companyId);
}