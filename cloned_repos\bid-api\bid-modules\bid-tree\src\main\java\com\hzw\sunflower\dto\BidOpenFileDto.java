package com.hzw.sunflower.dto;

import com.hzw.sunflower.entity.BidOpenFile;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @datetime 2022/11/25 15:37
 * @description: 线上开标记录表dto
 * @version: 1.0
 */
@Data
public class BidOpenFileDto extends BidOpenFile {

    @ApiModelProperty(value = "OSS文件key")
    private String ossFileKey;

    @ApiModelProperty(value = "OSS文件名称")
    private String ossFileName;

    @ApiModelProperty(value ="生成规则 1.直接拼接开标汇总表 2.从x行解析拼接")
    private Integer generateRule;

}
