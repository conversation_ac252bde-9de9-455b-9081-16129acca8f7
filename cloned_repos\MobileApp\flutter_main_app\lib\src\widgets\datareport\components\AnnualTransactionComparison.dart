///***
/// * 近三年交易总数对比
/// */

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:charts_flutter/flutter.dart' as charts;

import 'package:flutter_main_app/src/utils/FunctionUtils.dart';
import 'package:flutter_main_app/src/constants/theme.dart';

import 'package:flutter_main_app/src/viewModels/dataReport/DataReportViewModel.dart';

import 'package:flutter_main_app/src/models/dataReport/AnnualBidsectionModel.dart';

import 'package:flutter_main_app/src/components/charts/CustomTimeSeriesChart.dart';

class AnnualTransactionComparison extends StatelessWidget {

  AnnualTransactionComparison();

  static const List<String> months = [
    '01', '02', '03', '04', '05', '06',
    '07', '08', '09', '10', '11', '12',
  ];

  static const charts.Color _thisYearLineColor = charts.Color(a: 255, r: 0x44, g: 0xc7, b: 0xcc);
  static const charts.Color _lastYearLineColor = charts.Color(a: 255, r: 0x51, g: 0x8a, b: 0xf7);
  static const charts.Color _theYearBeforeLastYearLineColor = charts.Color(a: 255, r: 0xf0, g: 0xb3, b: 0x50);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.only(left: 15.0, top: 30.0, right: 15.0),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Text(
            '近三年交易总数对比',
            style: themeTitleTextStyle,
          ),
          Padding(
            padding: const EdgeInsets.only(top: 10.0),
            child: SizedBox(
              height: 231.0,
              child: Consumer<DataReportViewModel>(
                builder: (context, model, child) {

                  /// * 图表用数据
                  final List<AnnualBidsectionModel> thisYearContrastBidsectionData = [];

                  final List<AnnualBidsectionModel> lastYearContrastBidsectionData = [];

                  final List<AnnualBidsectionModel> theYearBeforeLastYearContrastBidsectionData = [];

                  /// * 后端返回数据
                  final List<Map<String, dynamic>> annualTransactionComparisonThisYear =
                    model.reportData?.annualTransactionComparisonThisYear ?? [];

                  final List<Map<String, dynamic>> annualTransactionComparisonLastYear =
                    model.reportData?.annualTransactionComparisonLastYear ?? [];

                  final List<Map<String, dynamic>> annualTransactionComparisonTheYearBeforeLastYear =
                    model.reportData?.annualTransactionComparisonTheYearBeforeLastYear ?? [];

                  ///***
                  /// * 处理 3 年的数据
                  /// * 名称替换为月份，格式为 2 位长度，不够则左侧补 0
                  /// */
                  for (int i = 0; i < 12; i++) {
                    Map<String,dynamic> thisYear = {};
                    Map<String,dynamic> lastYear = {};
                    Map<String,dynamic> theYearBeforeLastYear = {};

                    for (int j = 0; j < annualTransactionComparisonThisYear.length; j++) {
                      final Map<String, dynamic> month = annualTransactionComparisonThisYear[j] ?? {};

                      final String name = '${month['name'] ?? ""}';

                      if (name.indexOf('-${months[i]}-') != -1) {
                        thisYear = Map<String, dynamic>.from(month);

                        thisYear['name'] = '${months[i]}';

                        break;
                      }
                    }

                    for (int j = 0; j < annualTransactionComparisonLastYear.length; j++) {
                      final Map<String, dynamic> month = annualTransactionComparisonLastYear[j] ?? {};

                      final String name = '${month['name'] ?? ""}';

                      if (name.indexOf('-${months[i]}-') != -1) {
                        lastYear = Map<String, dynamic>.from(month);

                        lastYear['name'] = '${months[i]}';

                        break;
                      }
                    }

                    for (int j = 0; j < annualTransactionComparisonTheYearBeforeLastYear.length; j++) {
                      final Map<String, dynamic> month = annualTransactionComparisonTheYearBeforeLastYear[j] ?? {};

                      final String name = '${month['name'] ?? ""}';

                      if (name.indexOf('-${months[i]}-') != -1) {
                        theYearBeforeLastYear = Map<String, dynamic>.from(month);

                        theYearBeforeLastYear['name'] = '${months[i]}';

                        break;
                      }
                    }

                    thisYearContrastBidsectionData.add(
                      AnnualBidsectionModel(
                        name: '${months[i]}',
                        count: thisYear['count'] ?? 0,
                        amount: FunctionUtils.convertYuanToWanYuan(thisYear['amount'] ?? 0.00), /// ? 单位万元 ？
                      ),
                    );

                    lastYearContrastBidsectionData.add(
                      AnnualBidsectionModel(
                        name: '${months[i]}',
                        count: lastYear['count'] ?? 0,
                        amount: FunctionUtils.convertYuanToWanYuan(lastYear['amount'] ?? 0.00), /// ? 单位万元 ？
                      ),
                    );

                    theYearBeforeLastYearContrastBidsectionData.add(
                      AnnualBidsectionModel(
                        name: '${months[i]}',
                        count: theYearBeforeLastYear['count'] ?? 0,
                        amount: FunctionUtils.convertYuanToWanYuan(theYearBeforeLastYear['amount'] ?? 0.00), /// ? 单位万元 ？
                      ),
                    );
                  }

                  final List<charts.Series<AnnualBidsectionModel, DateTime>> contrastBidsectionSeriesList =
                    <charts.Series<AnnualBidsectionModel, DateTime>>[
                      charts.Series<AnnualBidsectionModel, DateTime>(
                        id: '${model.year}',
                        domainFn: (AnnualBidsectionModel bidsection, _) => DateTime(model.year, int.parse(bidsection.name), 1),
                        measureFn: (AnnualBidsectionModel bidsection, _) => bidsection.count,
                        colorFn: (_, __) => _thisYearLineColor,
                        fillColorFn: (_, __) => _thisYearLineColor,
                        data: thisYearContrastBidsectionData,
                      ),
                      charts.Series<AnnualBidsectionModel, DateTime>(
                        id: '${model.year - 1}',
                        domainFn: (AnnualBidsectionModel bidsection, _) => DateTime(model.year, int.parse(bidsection.name), 1),
                        measureFn: (AnnualBidsectionModel bidsection, _) => bidsection.count,
                        colorFn: (_, __) => _lastYearLineColor,
                        fillColorFn: (_, __) => _lastYearLineColor,
                        data: lastYearContrastBidsectionData,
                      ),
                      charts.Series<AnnualBidsectionModel, DateTime>(
                        id: '${model.year - 2}',
                        domainFn: (AnnualBidsectionModel bidsection, _) => DateTime(model.year, int.parse(bidsection.name), 1),
                        measureFn: (AnnualBidsectionModel bidsection, _) => bidsection.count,
                        colorFn: (_, __) => _theYearBeforeLastYearLineColor,
                        fillColorFn: (_, __) => _theYearBeforeLastYearLineColor,
                        data: theYearBeforeLastYearContrastBidsectionData,
                      ),
                    ];

                  return CustomTimeSeriesChart(
                    seriesList: contrastBidsectionSeriesList,
                  );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }

}
