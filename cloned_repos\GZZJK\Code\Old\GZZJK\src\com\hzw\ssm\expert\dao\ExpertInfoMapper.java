package com.hzw.ssm.expert.dao;

import com.hzw.ssm.expert.entity.*;
import com.hzw.ssm.sys.project.entity.ProjectEntity;
import com.hzw.ssm.sys.project.entity.ResultEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 专家基本信息
 *
 * <AUTHOR> 2014-10-09
 */
public interface ExpertInfoMapper {

    /**
     * 获取专家基本信息
     *
     * @param user_id(用户表id) 专家用户id(专家基本信息表id)
     * @return
     */
    public ExpertInfoEntity getExpertInfoByUserIdOrId(ExpertInfoEntity colExpertInfo);

    /**
     * 获取专家基本信息备份表
     *
     * @param user_id(用户表id) 专家用户id(专家基本信息表id)
     * @return
     */
    public ExpertInfoEntity getExpertInfoBakByUserIdOrId(ExpertInfoEntity colExpertInfo);

    /**
     * 保存专家基本信息
     *
     * @param expertInfoEntity
     */
    public void saveExpertInfo(ExpertInfoEntity expertInfoEntity);

    /**
     * 设置专家信息变为已完善
     *
     * @param expertInfoEntity
     */
    public void updateExpertCompelete(ExpertSupplementInfoEntity exertEntity);

    /**
     * 添加专家备注
     *
     * @param expertInfoEntity
     */
    public void updateExpertRemark(ExpertSupplementInfoEntity exertEntity);

    /**
     * 向专家基本信息备份表保存数据
     *
     * @param expertInfoEntity
     */
    public void saveExpertInfoBak(ExpertInfoEntity expertInfoEntity);

    /**
     * 专家基本修改
     *
     * @param expertInfoEntity
     */
    public void updateExpertInfo(ExpertInfoEntity expertInfoEntity);

    /**
     * 专家证件状态修改
     *
     * @param expertInfoEntity
     */
    public void updateExpertFileStatus(ExpertInfoEntity expertInfoEntity);

    /**
     * 修改专家平均分
     *
     * @param expertInfoEntity
     */
    public void updateExpertScore(ExpertInfoEntity expertInfoEntity);

    /**
     * 添加专家执业资格
     *
     * @param expertPracticeEntity
     */
    public void addExpertPractice(ExpertPracticeEntity expertPracticeEntity);

    /**
     * 添加专家执业资格备份
     *
     * @param expertPracticeEntity
     */
    public void addExpertPracticeBak(ExpertPracticeEntity expertPracticeEntity);

    /**
     * 更新专家职业资格
     *
     * @param expertPracticeEntity
     */
    public void updateExpertPractice(ExpertPracticeEntity expertPracticeEntity);

    /**
     * 查询执业资格列表
     *
     * @param user_id
     * @return
     */
    public List<ExpertPracticeEntity> queryPracticeByUserId(String user_id);

    /**
     * 查询执业资格备份列表
     *
     * @param user_id
     * @return
     */
    public List<ExpertPracticeEntity> queryPracticeBakByUserId(String user_id);


    /**
     * 查询专家评标专业列表
     *
     * @param user_id
     * @return
     */
    public List<ExpertMajorEntity> queryMajorByUserId(String user_id);

    /**
     * 查询专家评标专业备份列表
     *
     * @param user_id
     * @return
     */
    public List<ExpertMajorEntity> queryMajorBakByUserId(String user_id);

    /**
     * 添加专家评标专业
     *
     * @param majorEntity
     */
    public void addExpertMajorInfo(ExpertMajorEntity majorEntity);

    /**
     * 添加专家评标专业备份
     *
     * @param majorEntity
     */
    public void addExpertMajorInfoBak(ExpertMajorEntity majorEntity);

    /**
     * 修改专家评标专业
     *
     * @param majorEntity
     */
    public void updateExpertMajorInfo(ExpertMajorEntity majorEntity);


    /**
     * 查询评标专业集合
     *
     * @return
     */
    public List<SpecialtyInfoEntity> querySpecialtyInfoList();

    /**
     * 查询国家评标专业集合
     *
     * @return
     */
    public List<SpecialtyCouInfoEntity> querySpecialtyCouInfoList();

    /**
     * 添加专家工作经历
     *
     * @param experienceEntity
     */
    public void addExpertExperience(ExpertExperienceEntity experienceEntity);

    /**
     * 工作经历备份
     *
     * @param experienceEntity
     */
    public void addExpertExperienceBak(ExpertExperienceEntity experienceEntity);

    /**
     * 修改专家工作经历
     *
     * @param experienceEntity
     */
    public void updateExpertExperience(ExpertExperienceEntity experienceEntity);

    /**
     * 删除专家工作经历
     *
     * @param id
     */
    public void deleteExpertExperienceById(String id);

    /**
     * 查询专家工作经历列表
     *
     * @param user_id
     * @return
     */
    public List<ExpertExperienceEntity> queryExpertExperienceList(String user_id);

    /**
     * 查询专家工作经历备份列表
     *
     * @param user_id
     * @return
     */
    public List<ExpertExperienceEntity> queryExpertExperienceBakList(String user_id);

    /**
     * 查询审批意见实体类，查询一条最新的退回原因
     *
     * @param user_id
     * @return
     */
    public ExpertAuditEntity getExpertAuditEntityByUserId(String user_id);

    /**
     * 专家信息管理列表
     *
     * @param colExpertInfo
     * @return
     */
    public List<ExpertInfoEntity> queryPageExpertInfoManagerList(ExpertInfoEntity colExpertInfo);

    /**
     * 更新专家等级，1：普通专家，2：高级专家
     *
     * @param expertInfoEntity
     */
    public void updateExpertGrade(ExpertInfoEntity expertInfoEntity);

    /**
     * 更新备份表专家等级，1：普通专家，2：高级专家
     *
     * @param expertInfoEntity
     */
    public void updateExpertBakGrade(ExpertInfoEntity expertInfoEntity);

    /**
     * 更新专家状态，3：未暂停，4：暂停资格
     *
     * @param expertInfoEntity
     */
    public void updateExpertStatus(ExpertInfoEntity expertInfoEntity);

    /**
     * 更新备份表专家状态，3：未暂停，4：暂停资格
     *
     * @param expertInfoEntity
     */
    public void updateExpertBakStatus(ExpertInfoEntity expertInfoEntity);

    /**
     * 增加专家信息修改记录
     *
     * @param recordEntity
     */
    public void addExpertUpdateRecord(ExpertUpdateRecordEntity recordEntity);

    /**
     * 查询专家修改记录最大批次编号
     *
     * @param user_id
     * @return
     */
    public int selectMaxBatchNumFromRecord(String user_id);

    /**
     * 根据专业id查询评标专业
     *
     * @param spe_id
     * @return
     */
    public SpecialtyInfoEntity selectSpecialtyInfoBySpeId(String spe_id);

    /**
     * 根据专业id查询国家评标专业
     *
     * @param spe_id
     * @return
     */
    public SpecialtyCouInfoEntity selectSpecialtyCouInfoBySpeId(String spe_cou_id);

    /**
     * 根据专家user_id 删除对应备份表中的数据
     *
     * @param user_id
     */
    public void deleteExpertInfoBakByUserId(String user_id);

    /**
     * 根据专家user_id 删除专家职业资格记录备份表
     *
     * @param user_id
     */
    public void deletePracticeBakByUserId(String user_id);

    /**
     * 根据专家user_id 删除专家评标专业记录备份表
     *
     * @param user_id
     */
    public void deleteMajorBakByUserId(String user_id);

    /**
     * 根据专家user_id 删除专家工作经历记录备份表
     *
     * @param user_id
     */
    public void deleteExperienceBakByUserId(String user_id);

    /**
     * 查询专家信息修改记录列表,用于信息修改审核
     *
     * @param user_id
     * @return
     */
    public List<ExpertUpdateRecordEntity> queryExpertUpdateRecordList(String user_id);

    /**
     * 查询所有专家信息修改记录列表,用于专家信息管理页面查看
     *
     * @param user_id
     * @return
     */
    public List<ExpertUpdateRecordEntity> queryAllExpertUpdateRecordList(String user_id);

    /**
     * 删除修改记录，修改审核未通过，修改记录无效
     *
     * @param id
     */
    public void deleteExpertUpdateRecordById(String id);

    /**
     * 验证身份证号码在数据库中是否已经存在
     *
     * @param idNo
     * @return
     */
    public int checkIdNo(ExpertInfoEntity expertInfoEntity);

    /**
     * 验证档案号在数据库中是否已经存在
     *
     * @param idNo
     * @return
     */
    public int checkDocumentNo(ExpertInfoEntity expertInfoEntity);

    /**
     * 验证手机号码在数据库中是否已经存在
     *
     * @param expertInfoEntity
     * @return
     */
    public int checkMobile(ExpertInfoEntity expertInfoEntity);

    /**
     * 查询最大专家编号
     *
     * @return
     */
    public String getMaxExpertNum();

    /**
     * 更新文件名称
     *
     * @param expertInfoEntity
     */
    public void updateFile(ExpertInfoEntity expertInfoEntity);


    /**
     * 获取评标的专家信息
     *
     * @param expertInfoEntity
     * @return
     */
    public List<ExpertInfoEntity> queryPageBidExpertInfo(ExpertInfoEntity expertInfoEntity);

    /**
     * 查询专家库标记记录 分页
     *
     * @param expertInfoEntity
     * @return
     */
    public List<ExpertInfoEntity> queryPageByExpertLibrary(ExpertInfoEntity expertInfoEntity);

    /**
     * 查询专家库标记记录 无分页
     *
     * @param expertInfoEntity
     * @return
     */
    public List<ExpertInfoEntity> queryExpertLibraryList(ExpertInfoEntity expertInfoEntity);

    /**
     * 查询专家库标记记录只查询年龄 分页
     *
     * @param expertInfoEntity
     * @return
     */
    public List<ExpertInfoEntity> queryPageByExpertByAge(ExpertInfoEntity expertInfoEntity);

    /**
     * 查询专家库标记记录只查询年龄 无分页
     *
     * @param expertInfoEntity
     * @return
     */
    public List<ExpertInfoEntity> queryExpertListByAge(ExpertInfoEntity expertInfoEntity);

    /**
     * 更新专家状态（一年周期内扣分）
     *
     * @param
     * @return
     */
    public void modifyExpertStatusByScore(@Param("score") Integer score);

    /**
     * 更新专家状态byId
     *
     * @param
     * @return
     */
    public void modifyExpertStatusById(@Param("id") String id);


    /**
     * 查询所有专家的分数
     *
     * @param user_id
     * @return
     */
    public List<ExpertInfoEntity> queryExpertInfoList(Integer status);


    /**
     * 批量暂停专家
     *
     * @param
     * @return
     */
    public void modifyExpertAssessment(ExpertInfoEntity entity);

    /**
     * 跨年批量更新专家分值
     */
    public void updateAllExpertScore(ExpertInfoEntity entity);

    /**
     * 修改暂停专家的状态
     * 函数功能描述：TODO
     *
     * @param date
     */
    public void updateExpertStatusByDate(String date);

    /**
     * 奖励专家
     *
     * @param
     * @return
     */
    public void modifyExpertScore(ExpertInfoEntity entity);


    /**
     * 根据抽取记录查询专家信息
     *
     * @return
     */
    public ExpertInfoEntity queryExpertByResultId(String id);

    /**
     * 查询所有的专家信息
     * 函数功能描述：TODO
     *
     * @param
     * @return
     */
    public List<ExpertInfoEntity> queryAllExpertInfo();

    /**
     * 根据手机号查询对应的专家信息
     */
    public ExpertInfoEntity queryExpertByMobile(String mobile);


    /**
     * 根据专家id和批次号更新不参加理由
     */
    public void updateExpertByIdAndBatch(ResultEntity result);

    /**
     * @param entity
     * @return
     */
    public List<ExpertInfoEntity> queryExpertCompany(ExpertInfoEntity entity);

    /**
     * @param entity
     * @return
     */
    public List<ExpertInfoEntity> queryExpertCode(ExpertInfoEntity entity);

    /**
     * 查询所有的专家身份证号
     *
     * @param entity
     * @return
     */
    public List<ExpertInfoEntity> queryAllExpertIdNo(ExpertInfoEntity entity);

    /**
     * 根据专家id和批次号更新不参加理由
     */
    public void modifyExpertIdNo(ExpertInfoEntity result);


    public List<ExpertSupplementInfoEntity> queryPageExpertInfoCompleteList(ExpertSupplementInfoEntity exertEntity);

    public List<ExpertSupplementInfoEntity> queryExpertInfoCompleteList(ExpertSupplementInfoEntity exertEntity);

    ExpertInfoEntity queryByParam(ExpertInfoEntity infoEntity);

    /**
     * 根据
     *
     * @param openId
     * @return
     */
    ExpertInfoEntity queryExpertInfoByOpenId(@Param("openId") String openId);

    void saveExpertInfoBySmall(ExpertInfoEntity infoEntity);

    int updateExpertInfoById(ExpertInfoEntity infoEntity);

    ExpertInfoEntity getExpertInfoById(ExpertInfoEntity infoEntity);

    void updateTokenByOpenId(ExpertInfoEntity expertInfoEntity);

    ExpertInfoEntity queryExpertByToken(@Param("token") String token);

    int submitExamine(@Param("expertId") String expertId, @Param("status") String status, @Param("modifyTime") String modifyTime);

    ExpertInfoEntity queryCompanyByUserId(@Param("expertId") String expertId);

    int updateCompanyByExpertId(ExpertInfoEntity expertInfoEntity);

    ExpertInfoEntity getExpertInfoByUserId(ExpertInfoEntity infoEntity);

    ExpertInfoEntity queryInfoByMobilephone(@Param("mobilephone") String mobilephone);

    int updateInfoByUserId(ExpertInfoEntity expertInfoEntity);

    /**
     * 函数功能描述：查询待出库专家
     *
     * @param exertEntity
     * @return
     */
    public List<ExperOutEntity> queryPageExpertOutList(ExperOutEntity exertEntity);
    /**
     * 函数功能描述：只查询出库专家
     *
     * @param exertEntity
     * @return
     */
    public List<ExperOutEntity> queryPageOutList(ExperOutEntity exertEntity);

    /**
     * 函数功能描述：查询待出库专家(不分页)
     *
     * @param exertEntity
     * @return
     */
    public List<ExperOutEntity> queryExpertOutList(ExperOutEntity exertEntity);

    /**
     * 函数功能描述：根据专家id查询该专家下的待开标项目
     *
     * @param exertEntity
     * @return
     */
    public List<ProjectEntity> queryProjectByExpert(ExperOutEntity exertEntity);

    /**
     * 函数功能描述：专家出库--修改专家出库状态
     *
     * @param exertEntity
     * @return
     */
    public void updateExpertOutStatus(ExperOutEntity exertEntity);

    /**
     * 函数功能描述：专家出库--重新入库
     *
     * @param exertEntity
     * @return
     */
    public void updateExpertOutIn(ExperOutEntity exertEntity);

    List<Map<String, Object>> queryTitleStatus(String expertId);

    void updatePracticeFile(ExpertInfoEntity expertInfoEntity);
}
