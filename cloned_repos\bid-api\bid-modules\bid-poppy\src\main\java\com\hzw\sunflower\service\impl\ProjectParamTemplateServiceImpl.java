package com.hzw.sunflower.service.impl;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzw.sunflower.constant.FormatConstants;
import com.hzw.sunflower.constant.GeneralConstants;
import com.hzw.sunflower.controller.project.request.ProjectParamTemplateREQ;
import com.hzw.sunflower.dao.ProjectParamTemplateMapper;
import com.hzw.sunflower.dto.ProjectParamTemplateDTO;
import com.hzw.sunflower.entity.ProjectParamTemplate;
import com.hzw.sunflower.entity.condition.ProjectParamTemplateCondition;
import com.hzw.sunflower.service.ProjectParamTemplateService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;


/**
 * 项目参数表 Service接口实现
 *
 * <AUTHOR>
 * @version 1.0.0 2021-04-14
 */
@Service
public class ProjectParamTemplateServiceImpl extends ServiceImpl<ProjectParamTemplateMapper, ProjectParamTemplate> implements ProjectParamTemplateService {

    @Override
    public IPage<ProjectParamTemplate> findProjectParamTemplateByCondition(ProjectParamTemplateCondition condition) throws Exception {
        IPage<ProjectParamTemplate> page = condition.buildPage();

        QueryWrapper<ProjectParamTemplate> queryWrapper = condition.buildQueryWrapper(ProjectParamTemplate.class);

        return this.page(page, queryWrapper);

    }

    @Override
    public ProjectParamTemplate getProjectParamTemplateById(Long id) throws Exception {
        return this.getById(id);
    }


    @Override
    public Boolean addProjectParamTemplate(ProjectParamTemplate projectParamTemplate) throws Exception {
        return this.save(projectParamTemplate);
    }


    @Override
    public Boolean updateProjectParamTemplate(ProjectParamTemplate projectParamTemplate) throws Exception {
        return this.updateById(projectParamTemplate);
    }


    @Override
    public Boolean deleteProjectParamTemplateById(Long id) throws Exception {
        return this.removeById(id);
    }


    @Override
    public Boolean deleteProjectParamTemplateByIds(List<Long> idList) throws Exception {
        return this.removeByIds(idList);
    }

    /**
     * @param condition 条件对象
     * <AUTHOR>
     * @Description 多条件查询模板参数
     * @Return java.util.List<com.hzw.sunflower.dto.ProjectParamTemplateDTO>
     * @Date 2021/4/28 10:00
     */
    @Override
    public List<ProjectParamTemplateDTO> listByConditionProjectParamTemplate(ProjectParamTemplateCondition condition) {
        //空对象就默认设置查询父节点
        if (Objects.isNull(condition) || Objects.isNull(condition.getParentId())) {
            condition.setParentId(-1L);
        }
        //构造父节点查询条件
        QueryWrapper<ProjectParamTemplate> projectParamTemplateQueryWrapper = condition.buildQueryWrapper(ProjectParamTemplate.class);
        //查询
        List<ProjectParamTemplate> top = this.list(projectParamTemplateQueryWrapper); //查询父节点
        //转换对象
        List<ProjectParamTemplateDTO> dtos = JSONUtil.toList(JSONUtil.toJsonStr(top), ProjectParamTemplateDTO.class);
        //创建新的条件查询构造对象
        projectParamTemplateQueryWrapper = new QueryWrapper<>();
        //设置排序
        projectParamTemplateQueryWrapper.orderByAsc(FormatConstants.CREATED_TIME);
        //查询所有
        List<ProjectParamTemplate> result = this.list(projectParamTemplateQueryWrapper); //全部查询出来
        //转换对象
        List<ProjectParamTemplateDTO> transferArr = JSONUtil.toList(JSONUtil.toJsonStr(result), ProjectParamTemplateDTO.class);
        //循环封装层级关系
        dtos.forEach(item -> {
            List<ProjectParamTemplateDTO> resultDto = new ArrayList<>();
            Long id = item.getId();
            transferArr.forEach(node -> {
                if (id.longValue() == node.getParentId().longValue()) {
                    resultDto.add(node);
                }
            });
            item.setNodes(resultDto);
        });
        return dtos;
    }

    @Override
    public Boolean updatesProjectParamTemplate(List<ProjectParamTemplateREQ> projectParamTemplates) {
        List<ProjectParamTemplate> req = JSONUtil.toList(JSONUtil.toJsonStr(projectParamTemplates), ProjectParamTemplate.class);
        return this.updateBatchById(req);
    }

    /**
     * 根据单位创建项目信息参数模板数据
     *
     * @param companyId
     */
    public void addInit(Long companyId) {
        //根据公司查询下面的模板是否存在
        ProjectParamTemplate paramTemplateReq = new ProjectParamTemplate();
        paramTemplateReq.setCompanyId(companyId);
        List<ProjectParamTemplate> list = this.list(new QueryWrapper<>(paramTemplateReq));
        //不存在就创建
        if (list.isEmpty()) {
            detailsInit(companyId);
            bidPackageInit(companyId);
            projectInfoInit(companyId);
            clientInfoInit(companyId);
        }
    }


    private void detailsInit(Long companyId) {
        ProjectParamTemplate paramTemplate;
        ProjectParamTemplate details = new ProjectParamTemplate();
        details.setName("详细信息");
        details.setCode("");
        details.setDisplayState(0); //显示状态 0必填   1选填  2不显示
        details.setNotModifyStatus(0); //0必选必填   1选填必选其中一个  2全部可以修改
        details.setParentId(-1L);
        details.setCompanyId(companyId);
        this.save(details);
        paramTemplate = new ProjectParamTemplate();
        paramTemplate.setName("(包/标段)委托金额");
        paramTemplate.setCode("entrustMoney");
        paramTemplate.setDisplayState(GeneralConstants.TWO); //显示状态 0必填   1选填  2不显示
        paramTemplate.setNotModifyStatus(GeneralConstants.TWO); //0必选必填   1选填必选其中一个  2全部可以修改
        paramTemplate.setParentId(details.getId());
        paramTemplate.setCompanyId(companyId);
        this.save(paramTemplate);
        paramTemplate = new ProjectParamTemplate();
        paramTemplate.setName("项目所在地");
        paramTemplate.setCode("bidAddressCity");
        paramTemplate.setDisplayState(GeneralConstants.TWO); //显示状态 0必填   1选填  2不显示
        paramTemplate.setNotModifyStatus(GeneralConstants.TWO); //0必选必填   1选填必选其中一个  2全部可以修改
        paramTemplate.setParentId(details.getId());
        paramTemplate.setCompanyId(companyId);
        this.save(paramTemplate);
        paramTemplate = new ProjectParamTemplate();
        paramTemplate.setName("标的物分类");
        paramTemplate.setCode("bidThirdLevel");
        paramTemplate.setDisplayState(GeneralConstants.TWO); //显示状态 0必填   1选填  2不显示
        paramTemplate.setNotModifyStatus(GeneralConstants.TWO); //0必选必填   1选填必选其中一个  2全部可以修改
        paramTemplate.setParentId(details.getId());
        paramTemplate.setCompanyId(companyId);
        this.save(paramTemplate);
        paramTemplate = new ProjectParamTemplate();
        paramTemplate.setName("采购方式");
        paramTemplate.setCode("purchaseMode");
        paramTemplate.setDisplayState(GeneralConstants.TWO); //显示状态 0必填   1选填  2不显示
        paramTemplate.setNotModifyStatus(GeneralConstants.TWO); //0必选必填   1选填必选其中一个  2全部可以修改
        paramTemplate.setParentId(details.getId());
        paramTemplate.setCompanyId(companyId);
        this.save(paramTemplate);
        paramTemplate = new ProjectParamTemplate();
        paramTemplate.setName("保证金");
        paramTemplate.setCode("bond");
        paramTemplate.setDisplayState(GeneralConstants.TWO); //显示状态 0必填   1选填  2不显示
        paramTemplate.setNotModifyStatus(GeneralConstants.TWO); //0必选必填   1选填必选其中一个  2全部可以修改
        paramTemplate.setParentId(details.getId());
        paramTemplate.setCompanyId(companyId);
        this.save(paramTemplate);
        paramTemplate = new ProjectParamTemplate();
        paramTemplate.setName("代理服务费");
        paramTemplate.setCode("agencyFee");
        paramTemplate.setDisplayState(GeneralConstants.TWO); //显示状态 0必填   1选填  2不显示
        paramTemplate.setNotModifyStatus(GeneralConstants.TWO); //0必选必填   1选填必选其中一个  2全部可以修改
        paramTemplate.setParentId(details.getId());
        paramTemplate.setCompanyId(companyId);
        this.save(paramTemplate);
        paramTemplate = new ProjectParamTemplate();
        paramTemplate.setName("标书费");
        paramTemplate.setCode("tenderFee");
        paramTemplate.setDisplayState(GeneralConstants.TWO); //显示状态 0必填   1选填  2不显示
        paramTemplate.setNotModifyStatus(GeneralConstants.TWO); //0必选必填   1选填必选其中一个  2全部可以修改
        paramTemplate.setParentId(details.getId());
        paramTemplate.setCompanyId(companyId);
        this.save(paramTemplate);
        paramTemplate = new ProjectParamTemplate();
        paramTemplate.setName("押金");
        paramTemplate.setCode("depositAmount");
        paramTemplate.setDisplayState(GeneralConstants.TWO); //显示状态 0必填   1选填  2不显示
        paramTemplate.setNotModifyStatus(GeneralConstants.TWO); //0必选必填   1选填必选其中一个  2全部可以修改
        paramTemplate.setParentId(details.getId());
        paramTemplate.setCompanyId(companyId);
        this.save(paramTemplate);
        paramTemplate = new ProjectParamTemplate();
        paramTemplate.setName("文件发售方式");
        paramTemplate.setCode("releaseFileType");
        paramTemplate.setDisplayState(GeneralConstants.TWO); //显示状态 0必填   1选填  2不显示
        paramTemplate.setNotModifyStatus(GeneralConstants.TWO); //0必选必填   1选填必选其中一个  2全部可以修改
        paramTemplate.setParentId(details.getId());
        paramTemplate.setCompanyId(companyId);
        this.save(paramTemplate);
        paramTemplate = new ProjectParamTemplate();
        paramTemplate.setName("材料清单");
        paramTemplate.setCode("materialList");
        paramTemplate.setDisplayState(GeneralConstants.TWO); //显示状态 0必填   1选填  2不显示
        paramTemplate.setNotModifyStatus(GeneralConstants.TWO); //0必选必填   1选填必选其中一个  2全部可以修改
        paramTemplate.setParentId(details.getId());
        paramTemplate.setCompanyId(companyId);
        this.save(paramTemplate);
    }

    private void bidPackageInit(Long companyId) {
        ProjectParamTemplate paramTemplate;
        ProjectParamTemplate bidPackage = new ProjectParamTemplate();
        bidPackage.setName("划分标段信息");
        bidPackage.setCode("");
        bidPackage.setDisplayState(0); //显示状态 0必填   1选填  2不显示
        bidPackage.setNotModifyStatus(0); //0必选必填   1选填必选其中一个  2全部可以修改
        bidPackage.setParentId(-1L);
        bidPackage.setCompanyId(companyId);
        this.save(bidPackage);

        paramTemplate = new ProjectParamTemplate();
        paramTemplate.setName("是否划分标段/包");
        paramTemplate.setCode("packageSegmentStatus");
        paramTemplate.setDisplayState(GeneralConstants.TWO); //显示状态 0必填   1选填  2不显示
        paramTemplate.setNotModifyStatus(1); //0必选必填   1选填必选其中一个  2全部可以修改
        paramTemplate.setParentId(bidPackage.getId());
        paramTemplate.setCompanyId(companyId);
        this.save(paramTemplate);
    }

    private void projectInfoInit(Long companyId) {
        ProjectParamTemplate paramTemplate;
        ProjectParamTemplate projectInfo = new ProjectParamTemplate();
        projectInfo.setName("项目信息");
        projectInfo.setCode("");
        projectInfo.setDisplayState(0); //显示状态 0必填   1选填  2不显示
        projectInfo.setNotModifyStatus(0); //0必选必填   1选填必选其中一个  2全部可以修改
        projectInfo.setParentId(-1L);
        projectInfo.setCompanyId(companyId);
        this.save(projectInfo);
        paramTemplate = new ProjectParamTemplate();
        paramTemplate.setName("委托人项目名称");
        paramTemplate.setCode("projectName");
        paramTemplate.setDisplayState(GeneralConstants.TWO); //显示状态 0必填   1选填  2不显示
        paramTemplate.setNotModifyStatus(0); //0必选必填   1选填必选其中一个  2全部可以修改
        paramTemplate.setParentId(projectInfo.getId());
        paramTemplate.setCompanyId(companyId);
        this.save(paramTemplate);
        paramTemplate = new ProjectParamTemplate();
        paramTemplate.setName("委托人项目编号");
        paramTemplate.setCode("projectNumber");
        paramTemplate.setDisplayState(GeneralConstants.TWO); //显示状态 0必填   1选填  2不显示
        paramTemplate.setNotModifyStatus(1); //0必选必填   1选填必选其中一个  2全部可以修改
        paramTemplate.setParentId(projectInfo.getId());
        paramTemplate.setCompanyId(companyId);
        this.save(paramTemplate);
        paramTemplate = new ProjectParamTemplate();
        paramTemplate.setName("采购项目名称");
        paramTemplate.setCode("purchaseName");
        paramTemplate.setDisplayState(GeneralConstants.TWO); //显示状态 0必填   1选填  2不显示
        paramTemplate.setNotModifyStatus(GeneralConstants.TWO); //0必选必填   1选填必选其中一个  2全部可以修改
        paramTemplate.setParentId(projectInfo.getId());
        paramTemplate.setCompanyId(companyId);
        this.save(paramTemplate);
        paramTemplate = new ProjectParamTemplate();
        paramTemplate.setName("采购项目编号");
        paramTemplate.setCode("purchaseNumber");
        paramTemplate.setDisplayState(1); //显示状态 0必填   1选填  2不显示
        paramTemplate.setNotModifyStatus(0); //0必选必填   1选填必选其中一个  2全部可以修改
        paramTemplate.setParentId(projectInfo.getId());
        paramTemplate.setCompanyId(companyId);
        this.save(paramTemplate);
        paramTemplate = new ProjectParamTemplate();
        paramTemplate.setName("业务流程");
        paramTemplate.setCode("operationFlow");
        paramTemplate.setDisplayState(GeneralConstants.TWO); //显示状态 0必填   1选填  2不显示
        paramTemplate.setNotModifyStatus(GeneralConstants.TWO); //0必选必填   1选填必选其中一个  2全部可以修改
        paramTemplate.setParentId(projectInfo.getId());
        paramTemplate.setCompanyId(companyId);
        this.save(paramTemplate);
        paramTemplate = new ProjectParamTemplate();
        paramTemplate.setName("是否进场");
        paramTemplate.setCode("isProcess");
        paramTemplate.setDisplayState(GeneralConstants.TWO); //显示状态 0必填   1选填  2不显示
        paramTemplate.setNotModifyStatus(GeneralConstants.TWO); //0必选必填   1选填必选其中一个  2全部可以修改
        paramTemplate.setParentId(projectInfo.getId());
        paramTemplate.setCompanyId(companyId);
        this.save(paramTemplate);
        paramTemplate = new ProjectParamTemplate();
        paramTemplate.setName("委托金额");
        paramTemplate.setCode("entrustedAmount");
        paramTemplate.setDisplayState(GeneralConstants.TWO); //显示状态 0必填   1选填  2不显示
        paramTemplate.setNotModifyStatus(GeneralConstants.TWO); //0必选必填   1选填必选其中一个  2全部可以修改
        paramTemplate.setParentId(projectInfo.getId());
        paramTemplate.setCompanyId(companyId);
        this.save(paramTemplate);
        paramTemplate = new ProjectParamTemplate();
        paramTemplate.setName("项目审批文件名称");
        paramTemplate.setCode("projectApprovalFileName");
        paramTemplate.setDisplayState(GeneralConstants.TWO); //显示状态 0必填   1选填  2不显示
        paramTemplate.setNotModifyStatus(GeneralConstants.TWO); //0必选必填   1选填必选其中一个  2全部可以修改
        paramTemplate.setParentId(projectInfo.getId());
        paramTemplate.setCompanyId(companyId);
        this.save(paramTemplate);
        paramTemplate = new ProjectParamTemplate();
        paramTemplate.setName("立项审批文号");
        paramTemplate.setCode("projectApprovalNumber");
        paramTemplate.setDisplayState(GeneralConstants.TWO); //显示状态 0必填   1选填  2不显示
        paramTemplate.setNotModifyStatus(GeneralConstants.TWO); //0必选必填   1选填必选其中一个  2全部可以修改
        paramTemplate.setParentId(projectInfo.getId());
        paramTemplate.setCompanyId(companyId);
        this.save(paramTemplate);
        paramTemplate = new ProjectParamTemplate();
        paramTemplate.setName("立项审批单位");
        paramTemplate.setCode("projectApprovalUnit");
        paramTemplate.setDisplayState(GeneralConstants.TWO); //显示状态 0必填   1选填  2不显示
        paramTemplate.setNotModifyStatus(GeneralConstants.TWO); //0必选必填   1选填必选其中一个  2全部可以修改
        paramTemplate.setParentId(projectInfo.getId());
        paramTemplate.setCompanyId(companyId);
        this.save(paramTemplate);
    }

    private void clientInfoInit(Long companyId) {
        ProjectParamTemplate clientInfo = new ProjectParamTemplate();
        clientInfo.setName("委托人信息");
        clientInfo.setCode("");
        clientInfo.setDisplayState(0); //显示状态 0必填   1选填  2不显示
        clientInfo.setNotModifyStatus(0); //0必选必填   1选填必选其中一个  2全部可以修改
        clientInfo.setParentId(-1L);
        clientInfo.setCompanyId(companyId);
        this.save(clientInfo);
        ProjectParamTemplate paramTemplate = new ProjectParamTemplate();
        paramTemplate.setName("委托人");
        paramTemplate.setCode("companyName");
        paramTemplate.setDisplayState(GeneralConstants.TWO); //显示状态 0必填   1选填  2不显示
        paramTemplate.setNotModifyStatus(GeneralConstants.TWO); //0必选必填   1选填必选其中一个  2全部可以修改
        paramTemplate.setParentId(clientInfo.getId());
        paramTemplate.setCompanyId(companyId);
        this.save(paramTemplate);
        paramTemplate = new ProjectParamTemplate();
        paramTemplate.setName("委托人联系人");
        paramTemplate.setCode("userName");
        paramTemplate.setDisplayState(GeneralConstants.TWO); //显示状态 0必填   1选填  2不显示
        paramTemplate.setNotModifyStatus(GeneralConstants.TWO); //0必选必填   1选填必选其中一个  2全部可以修改
        paramTemplate.setParentId(clientInfo.getId());
        paramTemplate.setCompanyId(companyId);
        this.save(paramTemplate);
        paramTemplate = new ProjectParamTemplate();
        paramTemplate.setName("手机");
        paramTemplate.setCode("userPhone");
        paramTemplate.setDisplayState(GeneralConstants.TWO); //显示状态 0必填   1选填  2不显示
        paramTemplate.setNotModifyStatus(GeneralConstants.TWO); //0必选必填   1选填必选其中一个  2全部可以修改
        paramTemplate.setParentId(clientInfo.getId());
        paramTemplate.setCompanyId(companyId);
        this.save(paramTemplate);
        paramTemplate = new ProjectParamTemplate();
        paramTemplate.setName("委托合同");
        paramTemplate.setCode("uploadFileId");
        paramTemplate.setDisplayState(GeneralConstants.TWO); //显示状态 0必填   1选填  2不显示
        paramTemplate.setNotModifyStatus(GeneralConstants.TWO); //0必选必填   1选填必选其中一个  2全部可以修改
        paramTemplate.setParentId(clientInfo.getId());
        paramTemplate.setCompanyId(companyId);
        this.save(paramTemplate);
    }

}