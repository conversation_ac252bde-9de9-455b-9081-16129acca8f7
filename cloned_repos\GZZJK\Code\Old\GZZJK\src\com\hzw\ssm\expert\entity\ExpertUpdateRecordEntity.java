package com.hzw.ssm.expert.entity;

import com.hzw.ssm.fw.base.BaseEntity;

import java.util.Date;

/**
 * 专家信息修改记录表（修改留痕）
 * <AUTHOR>
 * @date 2014-10-20
 */
public class ExpertUpdateRecordEntity extends BaseEntity {

	/**
	 * 
	 */
	private static final long serialVersionUID = 8200298586021497632L;

	private String id;//主键
	
	private String user_id;//用户编号
	
	private String item;//修改项
	
	private String content_before;//修改前内容
	
	private String content_after;//修改后内容
	
	private Date modify_time;//修改时间

	private String modify_role;//修改角色
	
	private String modify_role_name;//修改角色名称
	
	private String modify_user;//修改人
	
	private String modify_user_name;//修改人名称
	
	private String modify_reason;//修改原因
	
	private Integer batch_number;//修改批次编号

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getUser_id() {
		return user_id;
	}

	public void setUser_id(String userId) {
		user_id = userId;
	}

	public String getItem() {
		return item;
	}

	public void setItem(String item) {
		this.item = item;
	}

	public String getContent_before() {
		return content_before;
	}

	public void setContent_before(String contentBefore) {
		content_before = contentBefore;
	}

	public String getContent_after() {
		return content_after;
	}

	public void setContent_after(String contentAfter) {
		content_after = contentAfter;
	}

	public Date getModify_time() {
		return modify_time;
	}

	public void setModify_time(Date modifyTime) {
		modify_time = modifyTime;
	}

	public String getModify_role() {
		return modify_role;
	}

	public void setModify_role(String modifyRole) {
		modify_role = modifyRole;
	}

	public String getModify_user() {
		return modify_user;
	}

	public void setModify_user(String modifyUser) {
		modify_user = modifyUser;
	}

	public String getModify_reason() {
		return modify_reason;
	}

	public void setModify_reason(String modifyReason) {
		modify_reason = modifyReason;
	}

	public Integer getBatch_number() {
		return batch_number;
	}

	public void setBatch_number(Integer batchNumber) {
		batch_number = batchNumber;
	}

	public String getModify_role_name() {
		return modify_role_name;
	}

	public void setModify_role_name(String modifyRoleName) {
		modify_role_name = modifyRoleName;
	}

	public String getModify_user_name() {
		return modify_user_name;
	}

	public void setModify_user_name(String modifyUserName) {
		modify_user_name = modifyUserName;
	}

}
