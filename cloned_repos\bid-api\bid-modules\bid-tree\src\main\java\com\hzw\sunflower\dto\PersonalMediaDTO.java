package com.hzw.sunflower.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 个人媒体信息
 *
 * <AUTHOR> 2021/07/06
 * @version 1.0.0
 */

@ApiModel(description = "个人媒体信息")
@Data
@Accessors(chain = true)
public class PersonalMediaDTO {
    @ApiModelProperty(value = "媒体ID", position = 1)
    private Long mediaId;

    @ApiModelProperty(value = "媒体名称", position = 2)
    private String mediaName;

    @ApiModelProperty(value = "媒体链接", position = 3)
    private String mediaUrl;

    @ApiModelProperty(value = "使用次数", position = 4)
    private Integer useTimes;

    @ApiModelProperty(value = "媒体状态：2代表个人私有媒体；3代表招标公告关联媒体", position = 5)
    private Integer state;

    @ApiModelProperty(value = "是否可删除：true代表可删除；false代表不可删除", position = 6)
    private Boolean canDelete;

    @ApiModelProperty(value = "是否是系统媒体：true代表是系统媒体；false代表不是系统媒体", position = 7)
    private Boolean isSystem;
}
