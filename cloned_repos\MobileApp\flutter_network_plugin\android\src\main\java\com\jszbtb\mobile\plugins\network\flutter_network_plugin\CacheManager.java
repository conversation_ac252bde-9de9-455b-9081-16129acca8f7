package com.jszbtb.mobile.plugins.network.flutter_network_plugin;

import android.content.Context;

import androidx.room.Database;
import androidx.room.Room;
import androidx.room.RoomDatabase;
import androidx.room.TypeConverter;
import androidx.room.TypeConverters;

@Database(entities = {CacheEntity.class},version = 2,exportSchema = false)
@TypeConverters({DateConverter.class})
public abstract class CacheManager extends RoomDatabase {
    public abstract CacheDao Dao();

    private static CacheManager cacheManager;

    public static CacheManager getInstance(Context context) {

        if(cacheManager == null) {
            cacheManager = Room.databaseBuilder(context,CacheManager.class,"cache.sqlite3")//data.db 是你的数据库名称
                    .build();
        }

        return cacheManager;
    }

}
