package com.hzw.sunflower.controller.request;

import com.hzw.sunflower.common.BaseCondition;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2023/8/15 15:01
 * @description：已关联未退回
 * @modified By：`
 * @version: 1.0
 */
@Data
public class RelationNoRefundReq extends BaseCondition {

    @ApiModelProperty(value = "关键字")
    private String keyWords;

    @ApiModelProperty(value = "供应商")
    private String companyName;

    @ApiModelProperty(value = "到账时间开始")
    private String sendStartTime;

    @ApiModelProperty(value = "到账时间结束")
    private String sendEndTime;

    @ApiModelProperty(value = "部门")
    private List<String> deptId;

    @ApiModelProperty(value = "权限sql")
    private String dataScope;
}
