package com.hzw.sunflower.dto;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;


@Data
public class UserDataPermUserDTO {

    @ApiModelProperty(value = "用户部门id")
    private List<UserDepartDTO> userDeparts;
//
//    @ApiModelProperty(value = "来源用户ID")
//    private Long fromUserId;

    @ApiModelProperty(value = "数据范围：0 我的全部项目 1 选择项目")
    private Integer dataScope;

    @ApiModelProperty(value = "权限码0：查看，1：编辑查看")
    private Integer rightCode;

    @ApiModelProperty(value = "授权项目列表")
    private List<Long> projectList;

}
