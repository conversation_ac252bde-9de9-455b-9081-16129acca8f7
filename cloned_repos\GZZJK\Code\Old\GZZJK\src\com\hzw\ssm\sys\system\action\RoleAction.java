package com.hzw.ssm.sys.system.action;

import java.util.List;

import net.sf.json.JSONArray;
import net.sf.json.JsonConfig;
import net.sf.json.util.PropertyFilter;

import org.apache.struts2.convention.annotation.Action;
import org.apache.struts2.convention.annotation.Namespace;
import org.apache.struts2.convention.annotation.ParentPackage;
import org.apache.struts2.convention.annotation.Result;
import org.apache.struts2.convention.annotation.Results;
import org.springframework.beans.factory.annotation.Autowired;

import com.hzw.ssm.fw.base.BaseAction;
import com.hzw.ssm.fw.util.HZWException;
import com.hzw.ssm.fw.util.MessageConstants;
import com.hzw.ssm.sys.system.entity.MenuEntity;
import com.hzw.ssm.sys.system.entity.RoleEntity;
import com.hzw.ssm.sys.system.service.MenuService;
import com.hzw.ssm.sys.system.service.RoleService;

/**
 * 用户操作
 * 
 * @page user
 * <AUTHOR> 
 */
@Namespace("/roleinfo")
@ParentPackage(value = "default")
@Results( { @Result(name = "init", location = "/jsp/sys/roleList.jsp"),
		@Result(name = "add", location = "/jsp/sys/roleAdd.jsp"),
		@Result(name = "edit", location = "/jsp/sys/roleEdit.jsp"),
		@Result(name = "ajaxDone", location = "/ajaxDone.jsp"),
		@Result(name = "toTree", location = "/jsp/sys/authTreeList.jsp") })
public class RoleAction extends BaseAction {

	/**
	 * 
	 */
	private static final long serialVersionUID = -8823858495799309882L;

	/** 角色对象列表 */
	private List<RoleEntity> roleList;

	/** 角色对象列表的查询条件 */
	private RoleEntity roleEntity;

	/** 角色id */
	private String roleId;

	/**
	 * 菜单id集合
	 */
	private String menuIds;

	private String message;
	@Autowired
	private RoleService roleService;

	@Autowired
	private MenuService menuService;

	@Action("init")
	public String init() {
		if (roleEntity == null) {
			roleEntity = new RoleEntity();
		}
		roleList = roleService.queryRoleList(roleEntity);
		
		return INIT;
	}

	/**
	 * 初始化增加页面
	 * @return
	 */
	@Action("add")
	public String add() {
		
		return "add";
	}
	/**
	 * 初始化修改页面
	 * @return
	 */
	@Action("updateInit")
	public String updateInit() {
		if (roleEntity == null) {
			roleEntity = new RoleEntity();
		}
		roleEntity=roleService.getRoleInfoById(roleId);
		return "edit";
	}
	/**
	 * 保存角色信息
	 * @return
	 */
	@Action("save")
	public String save() {
		try {
			this.setOpaUserAndDate(roleEntity);
			roleService.saveRoleInfo(roleEntity);
			this.setAlertMessage(MessageConstants.SAVE_SUCCESS);
		} catch (Exception e) {
			if(e instanceof HZWException){
				this.addFieldError("roleName", MessageConstants.SAMPLE_ROLE_NAME);
			}
			e.printStackTrace();
			this.setAlertMessage(MessageConstants.SAVE_FAILED);
		}
		return "add";
	}
	/**
	 * 修改角色信息
	 * @return
	 */
	@Action("update")
	public String update() {
		try {
			this.setOpaUserAndDate(roleEntity);
			roleService.updateRoleInfo(roleEntity);
			this.setAlertMessage(MessageConstants.SAVE_SUCCESS);
		} catch (Exception e) {
			if(e instanceof HZWException){
				this.addFieldError("roleName", MessageConstants.SAMPLE_ROLE_NAME);
			}
			e.printStackTrace();
			this.setAlertMessage(MessageConstants.SAVE_FAILED);
		}
		return "edit";
	}
	/**
	 * 删除角色信息
	 * @return
	 */
	@Action("deleteRole")
	public String deleteRole() {
		try {
			roleService.deleteRoleInfo(roleId);
			this.setAlertMessage(MessageConstants.DELETE_SUCCESS);
		} catch (Exception e) {
			e.printStackTrace();
			this.setAlertMessage(MessageConstants.DELETE_FAILED);
		}
		return this.init();
	}
	/**
	 * 请求角色授权页面
	 * 
	 * @param roleId
	 * @param model
	 * @return
	 */
	@Action("toTree")
	public String toTree() {
		List<MenuEntity> menus=roleService.queryMenuList();//查询所有菜单
		List<String> menuIdsOfAuth=roleService.getAuthListByRoleId(roleId);//查询角色已赋予的菜单id
		for(MenuEntity en:menus){//设置默认选择的菜单，即已赋予的权限
			if(menuIdsOfAuth.contains(en.getMenu_id())){
				en.setChecked(true);
			}
		}
		JsonConfig config=new JsonConfig();
		//通过JsonConfig的属性过滤器去除不需要的属性
		config.setJsonPropertyFilter(new PropertyFilter() {
			@Override
			public boolean apply(Object arg0, String arg1, Object arg2) {
				if(arg1.equals("menu_id")||arg1.equals("parent_menu_id")||arg1.equals("menu_name")
						||arg1.equals("checked")||arg1.equals("open")){
					return false;//不忽略
				}
				return true;
			}
		});
		JSONArray jsonArray=JSONArray.fromObject(menus, config);
		menuIds=jsonArray.toString();
		//将属性名称修改zTree对应的属性名称
		menuIds=menuIds.replaceAll("parent_menu_id","pId").replaceAll("menu_name", "name").replaceAll("menu_id", "id");
		roleEntity=roleService.getRoleInfoById(roleId);
		return "toTree";
	}

	/**
	 * 保存角色权限信息
	 * 
	 * @param out
	 * @param role
	 */
	@Action("saveRoleRelation")
	public String saveRoleRelation() {
		try {
			roleService.modifyRoleRelation(roleId, menuIds);
			this.setAlertMessage(MessageConstants.SAVE_SUCCESS);
		} catch (Exception e) {
			e.printStackTrace();
			this.setAlertMessage(MessageConstants.SAVE_FAILED);
		}
		return this.toTree();
	}

	public List<RoleEntity> getRoleList() {
		return roleList;
	}

	public void setRoleList(List<RoleEntity> roleList) {
		this.roleList = roleList;
	}

	public RoleEntity getRoleEntity() {
		return roleEntity;
	}

	public void setRoleEntity(RoleEntity roleEntity) {
		this.roleEntity = roleEntity;
	}

	
	public String getMenuIds() {
		return menuIds;
	}

	public void setMenuIds(String menuIds) {
		this.menuIds = menuIds;
	}

	public String getRoleId() {
		return roleId;
	}

	public void setRoleId(String roleId) {
		this.roleId = roleId;
	}

	public String getMessage() {
		return message;
	}

	public void setMessage(String message) {
		this.message = message;
	}

}
