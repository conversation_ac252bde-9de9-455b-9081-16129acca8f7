package com.hzw.sunflower.controller.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(description = "部门处长")
@Data
public class DepartmentUserVo {

    @ApiModelProperty(value = "部门id")
    private Long departmentId;

    @ApiModelProperty(value = "部门名称")
    private String departmentName;

    @ApiModelProperty(value = "处长id")
    private Long userId;

    @ApiModelProperty(value = "处长姓名")
    private String userName;

    @ApiModelProperty(value = "处长处理状态")
    private Integer departmentDisposeStatus;

}
