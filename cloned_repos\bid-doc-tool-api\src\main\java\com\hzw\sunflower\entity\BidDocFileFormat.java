package com.hzw.sunflower.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.hzw.sunflower.common.BaseBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 招标文件编制投标文件格式表
 */
@ApiModel(description = "招标文件编制投标文件格式表")
@TableName("t_bid_doc_file_format")
@Data
public class BidDocFileFormat extends BaseBean {

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "招标文件编制表id")
    private Long docId;

    @ApiModelProperty(value = "内容模板id")
    private Long contentFileId;

    @ApiModelProperty(value = "内容模板名称")
    private String contentName;

}
