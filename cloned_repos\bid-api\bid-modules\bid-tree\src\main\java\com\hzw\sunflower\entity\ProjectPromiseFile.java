package com.hzw.sunflower.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.hzw.sunflower.common.BaseBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @datetime 2022/11/04 8:35
 * @description: 项目承诺文件表
 * @version: 1.0
 */
@ApiModel("项目承诺文件表")
@TableName("t_project_promise_file")
@Data
public class ProjectPromiseFile extends BaseBean implements Serializable {

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "项目ID")
    private Long projectId;

    @ApiModelProperty(value = "文件名")
    private String fileName;

    @ApiModelProperty(value = "oss文件ID")
    private Long fileOssId;

    @ApiModelProperty(value = "文件类型 1.评委宣誓词 2.协评人员宣誓词 3.健康情况说明 4.宣誓词 5.其他")
    private Integer fileType;

}
