package com.hzw.sunflower.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hzw.sunflower.controller.request.SealFileSectionREQ;
import com.hzw.sunflower.dto.SealApplicationInfoDTO;
import com.hzw.sunflower.entity.SealApplicationInfo;
import com.hzw.sunflower.entity.condition.SealApplicationInfoCondition;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
* SealApplicationInfoService接口
*
*/
public interface SealApplicationInfoService extends IService<SealApplicationInfo> {
    /**
    * 根据条件分页查询列表
    *
    * @param condition 查询条件
    * @return 分页信息
    */
    IPage<SealApplicationInfo> findInfoByCondition(SealApplicationInfoCondition condition);

    /**
    * 根据主键ID查询信息
    *
    * @param id 主键ID
    * @return SealApplicationInfo信息
    */
    SealApplicationInfo getInfoById(Long id);

    /**
    * 新增
    *
    * @param sealApplicationInfo
    * @return 是否成功
    */
    Boolean addInfo(SealApplicationInfoDTO sealApplicationInfo);

    /**
    * 修改单
    *
    * @param sealApplicationInfo
    * @return 是否成功
    */
    Boolean updateInfo(SealApplicationInfoDTO sealApplicationInfo);

    /**
    * 根据主键ID删除
    *
    * @param id 主键ID
    * @return 是否成功
    */
    Boolean deleteById(Long id);

    /**
    * 根据主键ID列表批量删除
    *
    * @param idList 主键ID列表
    * @return 是否成功
    */
    Boolean deleteByIds(List<Long> idList);



}