package com.hzw.sunflower.util;

import org.apache.poi.xwpf.usermodel.*;

import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.util.List;

/**
 * <AUTHOR>
 * @datetime 2024/06/25 16:17
 * @description: word合并
 * @version: 1.0
 */
public class WordMergeUtil {

    private static void copyParagraph(XWPFParagraph source, XWPFParagraph target) {
        target.getCTP().set(source.getCTP().copy());
    }

    private static void copyTable(XWPFTable source, XWPFTable target) {
        target.getCTTbl().set(source.getCTTbl().copy());
    }

    private static int findInsertPosition(XWPFDocument doc, String searchText) {
        List<XWPFParagraph> paragraphs = doc.getParagraphs();
        for (int i = 0; i < paragraphs.size(); i++) {
            XWPFParagraph paragraph = paragraphs.get(i);
            String paragraphText = paragraph.getText();
            if (paragraphText != null && paragraphText.equals(searchText)) {
                return i; // 返回查找到的段落位置
            }
        }
        return -1; // 未找到指定占位符
    }

    private static void insertDocument(XWPFDocument targetDoc, XWPFDocument sourceDoc, int insertPosition) {
        XWPFParagraph insertAfterParagraph = targetDoc.getParagraphArray(insertPosition);
        // 删除占位符段落
        XWPFParagraph paragraph = targetDoc.getParagraphArray(insertPosition);
        paragraph.removeRun(0);

        // 插入新的内容
        for (IBodyElement element : sourceDoc.getBodyElements()) {
            if (element instanceof XWPFParagraph) {
                XWPFParagraph newParagraph = targetDoc.insertNewParagraph(insertAfterParagraph.getCTP().newCursor());
                copyParagraph((XWPFParagraph) element, newParagraph);
            } else if (element instanceof XWPFTable) {
                XWPFTable newTable = targetDoc.insertNewTbl(insertAfterParagraph.getCTP().newCursor());
                copyTable((XWPFTable) element, newTable);
            }
        }
    }

    public static void mergeWord(String filePath, String wordPath, String outPath) throws Exception {
        // 加载第一个文档
        FileInputStream fis1 = new FileInputStream(filePath);
        XWPFDocument doc1 = new XWPFDocument(fis1);

        // 加载第二个文档
        FileInputStream fis2 = new FileInputStream(wordPath);
        XWPFDocument doc2 = new XWPFDocument(fis2);

        // 查找指定文字的位置
        String searchText = "{投标文件格式}"; // 你要查找的指定文字
        int insertPosition = findInsertPosition(doc1, searchText);

        if (insertPosition == -1) {
            System.out.println("未找到指定的文字位置");
            return;
        }

        // 在找到的段落位置后面插入 doc2 的内容
        insertDocument(doc1, doc2, insertPosition);

        // 保存合并后的文档
        FileOutputStream fos = new FileOutputStream(outPath);
        doc1.write(fos);

        // 关闭文件流
        fis1.close();
        fis2.close();
        fos.close();

        System.out.println("文档合并完成");
    }

}
