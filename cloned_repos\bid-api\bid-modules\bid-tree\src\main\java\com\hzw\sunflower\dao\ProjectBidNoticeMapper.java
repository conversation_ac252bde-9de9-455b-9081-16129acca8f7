package com.hzw.sunflower.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hzw.sunflower.controller.response.NoticeTimeVO;
import com.hzw.sunflower.dto.SimpleBidNoticeDTO;
import com.hzw.sunflower.entity.ProjectBidNotice;
import com.hzw.sunflower.entity.condition.QueryNoticeListCondition;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
* TProjectBidNoticeMapper接口
*/

@Repository
public interface ProjectBidNoticeMapper extends BaseMapper<ProjectBidNotice> {

    /**
     * 包含分页信息的简单公告信息的列表
    */
    IPage<SimpleBidNoticeDTO> queryBidNoticeListPage(IPage<SimpleBidNoticeDTO> page, @Param("condition") QueryNoticeListCondition condition);

    /**
     * 包含简单公告信息的根据sectionId
     */
    SimpleBidNoticeDTO queryNoticeBySectionId(@Param("sectionId") Long sectionId);
    /**
     * 定时任务，用来查找符合条件的公告
     */
    List<ProjectBidNotice> findNoticeForScheduleTask();

    /**
     * 根据项目包段状态自动同步项目状态
     */
    Integer syncProjectStatus(@Param("projectId") Long projectId);


    /**
     *
     * @param projectId
     * @param sectionId
     * @param bidRound
     * @return
     */
    ProjectBidNotice selectBySectionIdAndRound(@Param("projectId") Long projectId ,@Param("sectionId") Long sectionId,@Param("bidRound") Integer bidRound);


    /**
     *  根据标段查询公告
     * @param projectId
     * @param sectionId
     * @param bidRound
     * @return
     */
    List<ProjectBidNotice> selectBySectionIdsAndRound(@Param("projectId") Long projectId ,@Param("sectionIds") List<Long> sectionId,@Param("bidRound") Integer bidRound);

    /**
     * 根据标段id查询公告信息
     * @return
     */
    List<NoticeTimeVO> getNoticeTime(@Param("sectionIds") List<Long> sectionId);


    /**
     * 获取公告次数
     * @param sectionId
     * @param bidRound
     * @return
     */
    Integer checkNoticeCount(@Param("sectionIds") List<Long> sectionId,@Param("bidRound") Integer bidRound);

    SimpleBidNoticeDTO queryNoticeBySectionIdApp(@Param("sectionId")Long sectionId,@Param("bidRound") Integer bidRound);
}