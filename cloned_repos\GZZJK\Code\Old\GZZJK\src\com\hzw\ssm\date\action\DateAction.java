package com.hzw.ssm.date.action;

import java.io.PrintWriter;
import java.util.Calendar;
import java.util.List;
import java.util.Map;

import org.apache.struts2.convention.annotation.Action;
import org.apache.struts2.convention.annotation.Namespace;
import org.apache.struts2.convention.annotation.ParentPackage;
import org.apache.struts2.convention.annotation.Result;
import org.apache.struts2.convention.annotation.Results;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;

import com.hzw.ssm.date.entity.CalendarEntity;
import com.hzw.ssm.date.service.ZJKCalendarService;
import com.hzw.ssm.fw.base.BaseAction;

@Namespace("/dateManage")
@ParentPackage(value = "default")
@Results( { @Result(name = "dataManagePage", location = "/jsp/date/dateManage.jsp")
		})
/**
 * 日期管理Action
 */
public class DateAction extends BaseAction {
	
	private static final long serialVersionUID = -8823858495799309882L;
	
	private Integer addNewYear;
	private Integer queryYear;
	private Integer queryMonth;
	private String errorCode;
	private String alertInfo;
	private List<CalendarEntity> calendarList;
	private List<Integer> allYears;
	private String updateDate;
	private Integer updateStatus;
	@Autowired
	private ZJKCalendarService zjkCalendarService;
	
	/**
	 * 返回日历页面
	 * @return
	 * @throws Exception
	 */
	@Action("getCalendar")
	public String getCalendar() throws Exception {
		this.context();
		if(StringUtils.isEmpty(queryYear)){
			Calendar calendar=Calendar.getInstance();
			queryYear = calendar.get(Calendar.YEAR);
		}
		if(StringUtils.isEmpty(queryMonth)){
			Calendar calendar=Calendar.getInstance();
			queryMonth = calendar.get(Calendar.MONTH)+1;
		}
		CalendarEntity calendarEntity = new CalendarEntity();
		calendarEntity.setDate_year(queryYear);
		calendarEntity.setDate_month(queryMonth);
		calendarList = zjkCalendarService.getCalendarByYearMonth(calendarEntity);
		return "dataManagePage";
	}
	
	/**
	 * 返回日历数据,页面不变
	 * @return
	 * @throws Exception
	 */
	@Action("getCalendarData")
	public String getCalendarData() throws Exception {
		this.context();
		if(StringUtils.isEmpty(queryYear)){
			Calendar calendar=Calendar.getInstance();
			queryYear = calendar.get(Calendar.YEAR);
		}
		if(StringUtils.isEmpty(queryMonth)){
			Calendar calendar=Calendar.getInstance();
			queryMonth = calendar.get(Calendar.MONTH)+1;
		}
		CalendarEntity calendarEntity = new CalendarEntity();
		calendarEntity.setDate_year(queryYear);
		calendarEntity.setDate_month(queryMonth);
		calendarList = zjkCalendarService.getCalendarByYearMonth(calendarEntity);
		StringBuffer str = new StringBuffer("[");
		for(CalendarEntity en : calendarList){
			str.append("{\"date_status\":\"").append(en.getDate_status()).append("\"},");
		}
		str.substring(0,str.length()-1);
		str.append("]");
		this.getResponse().setCharacterEncoding("UTF-8");
		PrintWriter out=this.getResponse().getWriter();
		out.print(str);
		out.close();
		return null;
	}
	
	/**
	 * 更新日历状态
	 * @return
	 * @throws Exception
	 */
	@Action("updateStatus")
	public String updateStatus() throws Exception {
		this.context();
		//1.判断参数是否为空
		if(StringUtils.isEmpty(updateDate)||null==updateStatus){
			alertInfo = "参数异常!";
		}else{
			//2.查询要修改的日历年份是否存在
			queryYear = Integer.parseInt(updateDate.split("-")[0]);
			calendarList = zjkCalendarService.getCalendarByYear(queryYear);
			//3.不存在则新增该年份数据
			if(!(calendarList!=null&&calendarList.size()>0)){
				zjkCalendarService.createCalendarForNewYear(queryYear);
			}
			//4.修改指定日期的状态码
			CalendarEntity calendarEntity = new CalendarEntity();
			if(updateStatus==10){
				calendarEntity.setRemark("工作日");
			}else if(updateStatus==11){
				calendarEntity.setRemark("调班");
			}else if(updateStatus==20){
				calendarEntity.setRemark("周末");
			}else if(updateStatus==21){
				calendarEntity.setRemark("法定假日");
			}
			calendarEntity.setDate_status(updateStatus);
			calendarEntity.setDate_time(updateDate);
			int count = zjkCalendarService.updateStatus(calendarEntity);
			
			if(count>0){
				alertInfo = "更新日历状态成功!";
			}else{
				alertInfo = "更新日历状态失败!";
			}
		}
		this.getResponse().setCharacterEncoding("UTF-8");
		PrintWriter out=this.getResponse().getWriter();
		out.print(alertInfo);
		out.close();
		return this.getCalendar();
	}

	/**
	 * 新增给定年份的日历
	 * @return
	 * @throws Exception
	 */
	@Action("createCalendarForNewYear")
	public String createCalendarForNewYear() throws Exception {
		this.context();
		if(addNewYear==null){
			alertInfo = "参数异常!";
			return this.getCalendar();
		}
		Map<String,Object> map = zjkCalendarService.createCalendarForNewYear(addNewYear);
		alertInfo = (String)map.get("description");
		PrintWriter out=this.getResponse().getWriter();
		out.print(alertInfo);
		out.close();
		return this.getCalendar();
	}

	public Integer getAddNewYear() {
		return addNewYear;
	}

	public void setAddNewYear(Integer addNewYear) {
		this.addNewYear = addNewYear;
	}

	public String getErrorCode() {
		return errorCode;
	}

	public void setErrorCode(String errorCode) {
		this.errorCode = errorCode;
	}

	public String getErrorInfo() {
		return alertInfo;
	}

	public void setErrorInfo(String alertInfo) {
		this.alertInfo = alertInfo;
	}

	public Integer getQueryYear() {
		return queryYear;
	}

	public void setQueryYear(Integer queryYear) {
		this.queryYear = queryYear;
	}

	public List<CalendarEntity> getCalendarList() {
		return calendarList;
	}

	public void setCalendarList(List<CalendarEntity> calendarList) {
		this.calendarList = calendarList;
	}

	public List<Integer> getAllYears() {
		return allYears;
	}

	public void setAllYears(List<Integer> allYears) {
		this.allYears = allYears;
	}

	public String getUpdateDate() {
		return updateDate;
	}

	public void setUpdateDate(String updateDate) {
		this.updateDate = updateDate;
	}

	public Integer getUpdateStatus() {
		return updateStatus;
	}

	public void setUpdateStatus(Integer updateStatus) {
		this.updateStatus = updateStatus;
	}

	public Integer getQueryMonth() {
		return queryMonth;
	}

	public void setQueryMonth(Integer queryMonth) {
		this.queryMonth = queryMonth;
	}

	
}
