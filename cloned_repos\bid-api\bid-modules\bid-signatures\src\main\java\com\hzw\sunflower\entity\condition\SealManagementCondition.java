package com.hzw.sunflower.entity.condition;

import com.hzw.sunflower.common.BaseCondition;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;


@ApiModel(description = "SealManagement 查询条件")
@Data
public class SealManagementCondition extends BaseCondition {

    /**
     * 印章名称
     *
     * @mbg.generated
     */
    @ApiModelProperty("印章名称")
    private String sealName;

    /**
     * 印章名称
     *
     * @mbg.generated
     */
    @ApiModelProperty("印章类型集合")
    private List<Integer> sealTypeList;

}