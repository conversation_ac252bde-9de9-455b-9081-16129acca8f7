package com.hzw.ssm.webService.output.impl;

import java.util.List;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElementWrapper;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;

import com.hzw.ssm.webService.output.BodyOutput;

/**
 * 获取专家名单返回结果
 * 
 * <AUTHOR>
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "ObtainExpert")
@XmlType(name = "ObtainExpert", propOrder = {  "exprtList" })
public class ObtainExpertOutput extends BodyOutput {
	@XmlElementWrapper(name = "exprtList")  
    @XmlElement(name = "exprtInfo")  
	private List<ExpertInfo> exprtList;

	public List<ExpertInfo> getExprtList() {
		return exprtList;
	}

	public void setExprtList(List<ExpertInfo> exprtList) {
		this.exprtList = exprtList;
	}
}
