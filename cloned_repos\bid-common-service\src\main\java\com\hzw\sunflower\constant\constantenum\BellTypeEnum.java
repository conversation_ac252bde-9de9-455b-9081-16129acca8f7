package com.hzw.sunflower.constant.constantenum;

/**
 * 小铃铛刷新类型
 *
 * <AUTHOR>
 */
public enum BellTypeEnum {
    //小铃铛刷新类型 1.自动刷新 2.主动刷新
    AUTO(1, "自动"),
    ACTIVE(2, "主动");

    private Integer type;
    private String desc;

    BellTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
