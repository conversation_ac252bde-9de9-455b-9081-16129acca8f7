package com.hzw.sunflower.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.alibaba.excel.annotation.ExcelIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class BidOpenDetail  implements Serializable {

//    @Excel(name = "项目名称",orderNum = "1",width=25)
    @ApiModelProperty(value = "项目id")
    private Long projectId;

    @ExcelIgnore
    @ApiModelProperty(value = "标段id")
    private Long sectionId;

    @ApiModelProperty(value = "项目名称")
    private String projectName;

    @Excel(name = "项目名称",orderNum = "1",width=25)
    @ApiModelProperty(value = "项目名称")
    private String purchaseName;

    @Excel(name = "项目编号",orderNum = "2",width=25)
    @ApiModelProperty(value = "项目编号")
    private String projectNumber;

    @Excel(name = "关联标段包",orderNum = "3",width=25,replace = {"-_null"})
    @ApiModelProperty(value = "关联标段包")
    private String bidPackage;

    @Excel(name = "项目经理",orderNum = "4",width=25)
    @ApiModelProperty(value = "项目经理")
    private String userName;

    @Excel(name = "部门",orderNum = "5",width=25)
    @ApiModelProperty(value = "部门")
    private String departmentName;

    @Excel(name = "委托单位",orderNum = "6",width=25)
    @ApiModelProperty(value = "委托单位")
    private String principalCompany;

    @Excel(name = "委托金额(万元)",orderNum = "7",width=25)
    @ApiModelProperty(value = "委托金额(万元)")
    private BigDecimal entrustMoney;


    @ApiModelProperty(value = "币种")
    private Integer entrustCurrency;

    @ApiModelProperty(value = "汇率")
    private BigDecimal exchangeRate;

    @Excel(name = "开标时间",orderNum = "8",format = "yyyy-MM-dd HH:mm:ss",width=25)
    @ApiModelProperty(value = "开标时间")
    private Date openTime;
    @Excel(name = "开标地点",orderNum = "9",width=25,replace = {"-_[]"})
    @ApiModelProperty(value = "开标地点")
    private List<String> conferenceAddress;

    @ApiModelProperty(value = "总数")
    private Integer count;

}
