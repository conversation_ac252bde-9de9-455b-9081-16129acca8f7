<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzw.sunflower.dao.ColumnCheckConfigMapper">

    <select id="getOldDayToTime" resultType="java.util.Date">
        SELECT
            DATE_FORMAT(date,'%Y-%m-%d')
        FROM
            t_calendar
        WHERE
            date_type = 1
            AND date &lt;= DATE_FORMAT(#{time},'%Y-%m-%d')
        ORDER BY
            date DESC
        LIMIT #{day},1
    </select>
</mapper>