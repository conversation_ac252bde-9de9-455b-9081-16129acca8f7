package com.hzw.ssm.expert.entity;

import com.hzw.ssm.fw.base.BaseEntity;

/**
 * 评标专业表
 * <AUTHOR>
 * @date 2014-10-09
 */
public class SpecialtyInfoEntity extends BaseEntity {
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 5929886403106519554L;
	
	private String spe_id;//专业id
	
	private String spe_name;//专业名称
	
	private String spe_code;//专业代码
	
	private String spe_parent;//父类的主键
	
	private Long spe_level;//级别
	
	private String spe_remark;//备注
	
	private String flag;
	private boolean hasSpecialty = false;

	private String parentSpecialtyName;
	
	private boolean checked;// 是否默认选中
	private boolean open;// 是否默认展开

	public String getSpe_id() {
		return spe_id;
	}

	public void setSpe_id(String speId) {
		spe_id = speId;
	}

	public String getSpe_name() {
		return spe_name;
	}

	public void setSpe_name(String speName) {
		spe_name = speName;
	}

	public String getSpe_code() {
		return spe_code;
	}

	public void setSpe_code(String speCode) {
		spe_code = speCode;
	}

	public String getSpe_parent() {
		return spe_parent;
	}

	public void setSpe_parent(String speParent) {
		spe_parent = speParent;
	}

	public Long getSpe_level() {
		return spe_level;
	}

	public void setSpe_level(Long speLevel) {
		spe_level = speLevel;
	}

	public String getSpe_remark() {
		return spe_remark;
	}

	public void setSpe_remark(String speRemark) {
		spe_remark = speRemark;
	}

	public String getFlag() {
		return flag;
	}

	public void setFlag(String flag) {
		this.flag = flag;
	}

	public String getParentSpecialtyName() {
		return parentSpecialtyName;
	}

	public void setParentSpecialtyName(String parentSpecialtyName) {
		this.parentSpecialtyName = parentSpecialtyName;
	}

	public boolean isHasSpecialty() {
		return hasSpecialty;
	}

	public void setHasSpecialty(boolean hasSpecialty) {
		this.hasSpecialty = hasSpecialty;
	}

	public boolean isChecked() {
		return checked;
	}

	public void setChecked(boolean checked) {
		this.checked = checked;
	}

	public boolean isOpen() {
		return open;
	}

	public void setOpen(boolean open) {
		this.open = open;
	}

}
