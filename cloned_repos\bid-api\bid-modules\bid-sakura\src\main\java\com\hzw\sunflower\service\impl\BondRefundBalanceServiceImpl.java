package com.hzw.sunflower.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzw.sunflower.constant.constantenum.BondRefundStatusEnum;
import com.hzw.sunflower.dao.BondRefundBalanceMapper;
import com.hzw.sunflower.entity.BondRefundBalance;
import com.hzw.sunflower.entity.BondRefundDetails;
import com.hzw.sunflower.entity.BondWater;
import com.hzw.sunflower.entity.BondWaterlabel;
import com.hzw.sunflower.service.BondRefundBalanceService;
import com.hzw.sunflower.service.BondRefundDetailsService;
import com.hzw.sunflower.service.BondWaterService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 保证金退还余额 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-28
 */
@Service
public class BondRefundBalanceServiceImpl extends ServiceImpl<BondRefundBalanceMapper, BondRefundBalance> implements BondRefundBalanceService {

    @Autowired
    BondRefundBalanceMapper bondRefundBalanceMapper;

    @Autowired
    BondRefundDetailsService bondRefundDetailsService;

    @Autowired
    BondWaterService bondWaterService;

    @Override
    public boolean checkBondBalance(List<BondRefundDetails> list) {
        List<BondRefundDetails> mergeList = new ArrayList<>();
        //计算库中已经进行过退还的账户金额
        List<String> refundNumbers = list.stream().distinct().map(BondRefundDetails::getRefundNumber).collect(Collectors.toList());
        LambdaQueryWrapper<BondRefundDetails> detailsWrapper = new LambdaQueryWrapper<>();
        detailsWrapper.in(BondRefundDetails::getRefundNumber, refundNumbers);
        detailsWrapper.eq(BondRefundDetails::getIsDelete, 0);
        detailsWrapper.ne(BondRefundDetails::getStatus, 4);
        detailsWrapper.ne(BondRefundDetails::getStatus, 2);
        detailsWrapper.ne(BondRefundDetails::getStatus, 6);
        detailsWrapper.ne(BondRefundDetails::getStatus, 8);
        List<BondRefundDetails> bondRefundDetailsList = bondRefundDetailsService.list(detailsWrapper);
        //增加代理服务费
        for(BondRefundDetails details:bondRefundDetailsList){
            BigDecimal totalBond = details.getRefundMoney().add(details.getAgencyFee().subtract(details.getRates()));
            details.setBondMoney(totalBond);
        }
        mergeList.addAll(bondRefundDetailsList);
        mergeList.addAll(list);
        List<BondRefundDetails> resultList = mergeAmount(mergeList);
        for(BondRefundDetails details:resultList){
            LambdaQueryWrapper<BondRefundBalance> lambdaWrapper = new LambdaQueryWrapper<>();
            lambdaWrapper.eq(BondRefundBalance::getCompanyAccount, details.getRefundNumber());
            BondRefundBalance bondRefundBalance = this.getOne(lambdaWrapper);
            if(bondRefundBalance!=null){
                //账户余额不足返回false
                if(details.getBondMoney().compareTo(bondRefundBalance.getTotalAmount())>0){
                    return false;
                }
            }
        }
        return true;
    }

    @Override
    public void changeTotalAmount(Long waterId, Integer label, BondWaterlabel lastLabel) {
        //查询流水金额
        BondWater bondWater = bondWaterService.getById(waterId);
        LambdaUpdateWrapper<BondRefundBalance> lambdaWrapper = new LambdaUpdateWrapper<>();
        lambdaWrapper.eq(BondRefundBalance::getCompanyAccount, bondWater.getCompanyAccount());
        if(lastLabel == null){
            //第一次设置除了收款之外的情况都要减
            if(1!=label){
                lambdaWrapper.setSql("total_amount = total_amount -"+bondWater.getAmount()+"");
                this.update(lambdaWrapper);
            }
        }else{
            //之前若设置过先判断原来的是什么状态,1是收款2是其他，原来是1现在不是1需要减掉金额，原来是其他现在是1需要加上金额
            if(1 != lastLabel.getLabel()&&1 == label){
                lambdaWrapper.setSql("total_amount = total_amount +"+bondWater.getAmount()+"");
                this.update(lambdaWrapper);
            }else if(1 == lastLabel.getLabel()&&1 !=label){
                lambdaWrapper.setSql("total_amount = total_amount -"+bondWater.getAmount()+"");
                this.update(lambdaWrapper);
            }
        }

    }

    /**
     * 合并账户中退还保证金金额
     * @param list
     * @return
     */
    private List<BondRefundDetails> mergeAmount(List<BondRefundDetails> list) {
        //删除和退款失败的不计算
        return new ArrayList<>(list.stream()
                .collect(Collectors.toMap(BondRefundDetails::getRefundNumber, a -> a, (o1, o2) -> {
                    o1.setBondMoney(o1.getBondMoney().add(o2.getBondMoney()));
                    return o1;
                })).values());
    }

    @Override
    public void batchSaveOrUpdateChange(List<BondWater> reqList) {
        for(BondWater req:reqList){
            BondRefundBalance saveEntity = new BondRefundBalance();
            saveEntity.setCompanyAccount(req.getCompanyAccount());
            LambdaUpdateWrapper<BondRefundBalance> lambdaWrapper = new LambdaUpdateWrapper<>();
            lambdaWrapper.eq(BondRefundBalance::getCompanyAccount, req.getCompanyAccount());
            if(StringUtils.isBlank(req.getAmount())){
                saveEntity.setTotalAmount(new BigDecimal(0));
            }else{
                saveEntity.setTotalAmount(new BigDecimal(req.getAmount()));
                lambdaWrapper.setSql("total_amount = total_amount +"+saveEntity.getTotalAmount()+"");
            }
            boolean result = this.update(lambdaWrapper);
            if(!result){
                saveEntity.setRefundMoney(new BigDecimal(0));
                this.saveOrUpdate(saveEntity);
            }
        }
    }

    @Override
    public void batchUpdateRefund(List<BondRefundDetails> reqList) {
        for(BondRefundDetails req:reqList){
            if(!BondRefundStatusEnum.SUCCESS.getType().equals(req.getStatus())){
                continue;
            }
            LambdaUpdateWrapper<BondRefundBalance> lambdaWrapper = new LambdaUpdateWrapper<>();
            lambdaWrapper.eq(BondRefundBalance::getCompanyAccount, req.getRefundNumber());
            BigDecimal refundMoney = req.getBondMoney().add(req.getAgencyFee());
            lambdaWrapper.setSql("refund_money = refund_money +"+refundMoney+"");
            this.update(lambdaWrapper);
        }
    }
}
