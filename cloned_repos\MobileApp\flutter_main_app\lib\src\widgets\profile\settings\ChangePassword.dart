///***
/// * 修改密码
/// */

import 'package:flutter/material.dart';

import 'package:flutter_main_app/src/constants/theme.dart';
import 'package:flutter_main_app/src/utils/ScreenUtils.dart';

import 'package:flutter_main_app/src/components/CustomAppBar.dart';
import 'package:flutter_main_app/src/components/CustomSafeArea.dart';

class ChangePassword extends StatefulWidget {
  const ChangePassword({Key key}) : super(key: key);

  /// * 路由名称
  static const String _routeName = 'ChangePassword';
  static String get routeName => _routeName;

  @override
  _ChangePasswordState createState() => _ChangePasswordState();
}

class _ChangePasswordState extends State<ChangePassword> {

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: '修改密码',
      ),
      body: CustomSafeArea(
        child: SingleChildScrollView(
          physics: BouncingScrollPhysics(),
          child: Container(
            padding: const EdgeInsets.only(top: 10.0),
            width: ScreenUtils.screenWidth,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                this._buildInput(
                  label: '旧密码',
                  hintText: '请输入旧密码',
                ),
                Divider(
                  height: themeBorderWidth,
                  thickness: themeBorderWidth,
                  color: themeBorderColor,
                ),
                this._buildInput(
                  label: '新密码',
                  hintText: '请输入新密码(6至20位数字或字母))',
                ),
                Divider(
                  height: themeBorderWidth,
                  thickness: themeBorderWidth,
                  color: themeBorderColor,
                ),
                this._buildInput(
                  label: '确认密码',
                  hintText: '请再次输入密码',
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  ///***
  /// * 生成输入框
  /// *
  /// * @param {String} label
  /// * @param {String} hintText
  /// *
  /// * @return {Widget}
  /// */
  Widget _buildInput({
    String label,
    String hintText,
  }) {
    const double inputHeight = 50.0;
    const double labelWidth = 90.0;

    final Widget input = Container(
      width: ScreenUtils.screenWidth,
      height: inputHeight,
      color: themeContentBackgroundColor,
      child: Row(
        mainAxisSize: MainAxisSize.max,
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: <Widget>[
          /// * label
          Container(
            padding: const EdgeInsets.only(left: 15.0),
            width: labelWidth,
            child: Text(
              label,
              style: themeTextStyle,
            ),
          ),
          /// * input
          Expanded(
            child: TextField(
              style: themeTextStyle,
              decoration: InputDecoration(
                hintText: hintText,
                hintStyle: themeHintTextStyle,
                contentPadding: const EdgeInsets.all(7.0),
                border: InputBorder.none,
              ),
            ),
          ),
        ],
      ),
    );

    return input;
  }
}
