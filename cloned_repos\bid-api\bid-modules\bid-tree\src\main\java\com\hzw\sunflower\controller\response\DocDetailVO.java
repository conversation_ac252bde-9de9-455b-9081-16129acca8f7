package com.hzw.sunflower.controller.response;

import com.hzw.sunflower.dto.DocDetailDTO;
import com.hzw.sunflower.dto.DocSectionDetailDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * TProjectBidDoc 返回实体
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@ApiModel(description = "招标文件返回参数")
@Data
public class DocDetailVO extends DocDetailDTO {

    @ApiModelProperty(value = "关联包信息", position = 1000)
    private List<DocSectionDetailDTO> sectionDetailList;
}
