package com.hzw.sunflower.constant.constantenum;

/**
 * 招标阶段
 *
 * <AUTHOR>
 */
public enum AppMsgTypeEnum {
    // 消息通知类型 1.待办 2.待阅
    TO_DO("1", "待办"),
    TO_READ("2", "待阅"),
    TO_NOTICE("3","通知");
    private String type;
    private String desc;

    AppMsgTypeEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
