package com.hzw.sunflower.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hzw.sunflower.controller.request.DictionaryReq;
import com.hzw.sunflower.controller.response.DictionaryVO;
import com.hzw.sunflower.controller.response.SubjectMatterVo;
import com.hzw.sunflower.dto.DictionaryDTO;
import com.hzw.sunflower.entity.Dictionary;
import com.hzw.sunflower.entity.condition.DictionaryCondition;

import java.util.List;

/**
 * 字典表 Service接口
 *
 * <AUTHOR>
 * @version 1.0.0 2021-04-07
 */
public interface DictionaryService extends IService<Dictionary> {
    /**
     * 根据条件分页查询字典表 列表
     *
     * @param condition 查询条件
     * @return 分页信息
     */
    IPage<Dictionary> findDictionaryByCondition(DictionaryCondition condition);


    /**
     * 根据父id 查询数据字典数据
     *
     * @param dictionaryReq 查询条件
     * @return 分页信息
     */
    List<DictionaryVO> selectDictionaryByParentId(DictionaryReq dictionaryReq);

    List<Dictionary> selectTreeListByParentId(DictionaryReq dictionaryReq);

    /**
     * 根据主键ID查询字典表 信息
     *
     * @param id 主键ID
     * @return 字典表 信息
     */
    Dictionary getDictionaryById(Long id);

    /**
     * 新增字典表 信息
     *
     * @param dictionary 字典表 信息
     * @return 是否成功
     */
    Boolean addDictionary(Dictionary dictionary);

    /**
     * 修改字典表 信息
     *
     * @param dictionary 字典表 信息
     * @return 是否成功
     */
    Boolean updateDictionary(Dictionary dictionary);

    /**
     * 根据主键ID删除字典表
     *
     * @param id 主键ID
     * @return 是否成功
     */
    Boolean deleteDictionaryById(Long id);

    /**
     * 根据主键ID列表批量删除字典表
     *
     * @param idList 主键ID列表
     * @return 是否成功
     */
    Boolean deleteDictionaryByIds(List<Long> idList);

    /**
     * <AUTHOR>
     * @Description 多条件查询子单对象
     * @param condition 条件对象
     * @Return java.util.List<com.hzw.sunflower.dto.DictionaryDTO>
     * @Date 2021/4/28 9:56
     */
    List<DictionaryDTO> getByConditionDicList(DictionaryCondition condition) throws Exception;

    /**
     * <AUTHOR>
     * @Description 根据code查询子项code
     * @param code
     * @Date 2021/4/28 9:56
     */
    List<String> getDicByCode(Integer code);

    /**
     * <AUTHOR>
     * @Description 删除缓存
     * @Return java.lang.Boolean
     * @Date 2021/4/28 9:51
     */
    Boolean deleteCache();

    /**
     * 设置启用禁用
     * @param condition
     * @return
     */
    Boolean updateDisable(DictionaryCondition condition);

    /**
     * 查询所有币种
     * @return
     */
    List<Dictionary> selectDictionaryForCurrency();


    /**
     * 查询所有国家
     * @return
     */
    List<Dictionary> selectAllCountry();

    /**
     * 查询历史地点
     * @return
     */
    List<DictionaryVO> getByHistoryDicList();

    Boolean saveSubjectMatter(Dictionary dictionary);

    /**
     * 标的物分类数量
     * @param dictionaryReq
     * @return
     */
    List<SubjectMatterVo> getSubjectMatterCount(DictionaryReq dictionaryReq);

    /**
     * 查询所有缓存配置
     * @return
     */
    Dictionary selectDictionaryForCacheRefreshTime();

    /**
     * 根据code获取字典
     * @param code
     * @return
     */
    Dictionary getDictionaryByCode(String code);
}
