package com.hzw.sunflower.constant.constantenum;

/**
 * 业务分类
 *
 * <AUTHOR>
 */
public enum RoleCodeEnum {
    /**
     35	运维管理员	ROLE_ADMIN	4		0					2022-06-21 10:49:52	0		0	1000009400	b5d190491cde461f903dde8f211e773c
     3953	公司总经理	zjl	2		2				1628752318781	2023-06-01 21:49:18	0		3	1000009400	613efaa03bfb4ababc8d80ea1f2676b9
     3954	公司副总经理	fzjl	2		2				1628752312649	2023-05-06 18:36:26	0		6	1000009400	cb49c2ff08864707b2d5d452b2dd19dc
     3955	办公室主任	bgszr	2		2				1628752318781	2023-02-16 16:12:17	0		1	1000009400	5eefe89d33bf4265b09ab82d9db1a5ef
     3956	办公室主管	bgszg	2		0					2022-06-21 10:49:52	0		0	1000009400	36eda60635a246cba54ec361a60cccfe
     3957	办公室主办	bgszb	2		0					2022-06-21 10:49:52	0		0	1000009400	20c68783f040494da4144227b314f662
     3958	办公室专员	bgszy	2		0					2022-06-21 10:49:52	0		0	1000009400	6c4e149a89064358817fe3658e5acebb
     3959	运管处处长	ygcz	2		2				1628752313050	2022-07-18 15:43:25	0		3	1000009400	6721584a470e471fba19bedb8f77d80e
     3960	运业务管理员	ygcyw	2		2				1628752312649	2022-07-26 15:24:50	0		1	1000009400	99e2a31560424b2d889d71da8a5dda6c
     3961	运管处档案管理员	ygcda	2		0					2022-06-21 10:49:52	0		0	1000009400	f8e08a824ff445ff954f30348c06f61f
     3962	运管处信管员	ygcxx	2		2				1628752313050	2022-08-29 10:40:01	0		6	1000009400	e3b210b76f524f58acca92208d3f8c8a
     3963	运管处辅助岗	ygcfz	2		0				1628752312649	2022-07-26 15:23:27	0		2	1000009400	d7747d883930422593cd619bc121d418
     3964	财务与审计处处长	cwsjcz	2		4					2022-06-21 10:49:52	0		0	1000009400	700f522f9fc74dd6947b41759a690bb7
     3965	财务与审计处财务主管	cwsjzg	2		4					2022-06-21 10:49:52	0		0	1000009400	5ab234e340f7480e89e44936831d0901
     3966	财务与审计处财务经办	cwsjcw	2		4					2022-06-21 10:49:52	0		0	1000009400	f8174ce58d4c4a8086d75446a3f2d287
     3967	业务处处长	chuzhang	2		1				1628752312840	2022-06-24 14:17:06	0		1	1000009400	bd448fa77c3744fd85499c997d51cb5f
     3968	业务处项目经理	xmjl	2		0					2022-06-21 10:49:52	0		0	1000009400	3364f625d98340488c6dfdb943847f84
     3969	业务处业务助理	ywywzl	2		0					2022-06-21 10:49:52	0		0	1000009400	ec774a33968649b087f224660ba55837
     3970	招标人用户	ROLE_NORMAL	1		0					2022-06-21 10:49:52	0		0	1000009400	dd6e6aceaa8c4c44b3f5104566cb8655
     3971	招标人管理员	ROLE_ADMIN	1		0					2022-06-21 10:49:52	0		0	1000009400	0ccbce2e578c46a6a4ceadde79adc060
     3972	供应商用户	ROLE_NORMAL	3		0					2022-06-21 10:49:52	0		0	1000009400	317e03fa486a41d1b7b141589b191a55
     3973	供应商管理员	ROLE_ADMIN	3		0					2022-06-21 10:49:52	0		0	1000009400	70609dca1efa44aeb912516aa9eaa1e2
     3974	平台系统管理	ROLE_ADMIN	2		2				1628752312649	2022-06-21 10:49:52	0		1	1000009400	76964a2df10c11ecbf0898039b7766ea
     3975	财务与审计处财务审核	cwsjshh	2		4					2022-06-21 10:55:52	0		0	1000009400	859071b0f10c11ecbf0898039b7766ea
     3976	专家	ROLE_EXPERT	5		0						0		0	1000009400	919071b0f10c11ecbf0898039b7766eh
     3977	CA	0001	2		0		1628752313050	2022-12-12 17:37:06	1628752313050	2022-12-12 17:37:06	0		0		074d1926c2ca4f3ba14418c77e7674f3
     3978	董事长	dsz	2		2		1628752312649	2023-04-13 22:17:54	1628752312649	2023-04-13 23:55:26	0		1		d2e7c88aaf094b5db0cb16150295a10b
     3979	公司副总经理	linwen	2		2		1628752312649	2023-04-13 22:51:53	1628752313050	2023-04-17 11:26:09	0		3		4dd76efa3dd0453caa38f14f547983ac
     3980	公司副总经理	yefengchun	2		2		1628752312649	2023-04-13 22:53:02	1628752313050	2023-04-17 11:26:21	0		2		df2b7060708247cb8cc858212dc3c036
     3981	运管处测试	ygccs	2		2		1628752312649	2023-04-27 22:04:27	1628752312649	2023-04-27 22:05:41	0		1		816c45dd6e504a47adc6cc378c9738e5
     */
    ROLE_NORMAL("ROLE_NORMAL","普通用户"),
    ROLE_ADMIN("ROLE_ADMIN","管理员"),
    ZJL("zjl", "总经理"),
    FZJL("fzjl", "副总经理"),
    BGSZR("bgszr", "办公室主任"),
    BGSZG("bgszg", "办公室主管"),
    BGSZB("bgszb", "办公室主办"),
    BGSZY("bgszy", "办公室专员"),
    YGCZ("ygcz", "运管处处长"),
    YGCYW("ygcyw", "运管处业务管理员"),
    YGCDA("ygcda", "档案管理员"),
    YGCXX("ygcxx", "运管处信管员"),
    YGCFZ("ygcfz", "运管处业务辅助岗"),
    CWSJCZ("cwsjcz", "财务与审计处处长"),
    CWSJZG("cwsjzg", "财务与审计处财务主管"),
    CWSJCW("cwsjcw", "财务与审计处财务经办"),
    CHUZHANG("chuzhang", "业务处处长"),
    XMJL("xmjl", "业务处项目经理"),
    YWYWZL("ywywzl", "业务处业务助理"),
    CWSJSHH("cwsjshh", "财务与审计处财务审核"),
    NJBK("njbk", "南京银行"),
    ROLE_EXPERT("ROLE_EXPERT", "专家"),
    LINWEN("linwen", "副总经理"),
    YEFENGCHUN("yefengchun", "副总经理"),
    DSZ("dsz", "董事长"),
    CA("0001", "CA"),
    YGCCS("ygccs", "运管处测试"),
    ;

    private String type;
    private String desc;

    RoleCodeEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
