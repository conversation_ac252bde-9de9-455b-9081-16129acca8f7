package com.hzw.sunflower.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hzw.sunflower.controller.request.CompanyFlowConfigReq;
import com.hzw.sunflower.entity.CompanyFlowConfig;

import java.util.List;
import java.util.Map;


/**
* CompanyFlowConfigService接口
*
*/
public interface CompanyFlowConfigService extends IService<CompanyFlowConfig> {

    /**
     * 根据公司id获取业务流程配置
      * @param req
     * @return
     */
    CompanyFlowConfig getByCompanyId(CompanyFlowConfigReq req);

    /**
     * 根据公司id获取业务流程配置--用印申请
     * @param req
     * @return
     */
    CompanyFlowConfig getByCompanyIdForSeal(CompanyFlowConfigReq req);

    /**
     * 根据业务code获取流程key
     * @param businessCode
     * @return
     */
    List<String> getKeyByBusinessCode(List<String> businessCode);


    /**
     * 根据流程key获取业务code
     * @param businessCode
     * @return
     */
    String getBusinessCodeByKey(String key);

    /**
     * 根据业务code获取流程键值对
     * @param values
     * @return
     */
    Map<String, String> getMapByBusinessCode(List<String> values);

    /**
     * 查询审批人为空是否跳过
     * @param processDefinitionKey
     * @return
     */
    Boolean getFlowIsJump(String processDefinitionKey);

    /**
     * 根据部门id获取流程配置
     * @param req
     * @return
     */
    CompanyFlowConfig getByDeptId(CompanyFlowConfigReq req);

    /**
     * 根据流程key获取是否记录审批记录
     * @param key
     * @return
     */
    Boolean getIsAddRecord(String key);

    /**
     * 获取审批code
     * @param processDefinitionKey
     * @return
     */
    String getRecordCode(String processDefinitionKey);
}