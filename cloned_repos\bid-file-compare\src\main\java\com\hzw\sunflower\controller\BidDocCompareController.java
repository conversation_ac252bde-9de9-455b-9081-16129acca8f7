package com.hzw.sunflower.controller;
import com.hzw.sunflower.common.BaseController;
import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.dto.BidDocCompareDto;
import com.hzw.sunflower.service.BidDocCompareService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


@Api(tags = "投标文件")
@RequestMapping("/bidDoc")
@RestController
@Slf4j
public class BidDocCompareController extends BaseController {

    @Autowired
    private BidDocCompareService bidDocCompareService;

    @ApiOperation(value = "相似度对比")
    @PostMapping(value = "/compare")
    public Result<String> compare(@RequestBody BidDocCompareDto bidDocCompareDto) {
        return bidDocCompareService.bidDocCompare(bidDocCompareDto);
    }
}
