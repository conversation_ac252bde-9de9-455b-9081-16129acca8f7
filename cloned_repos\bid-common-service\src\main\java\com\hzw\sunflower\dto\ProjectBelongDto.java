package com.hzw.sunflower.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class ProjectBelongDto {

    @ApiModelProperty(value = "项目签订主体")
    private Long projectCompanyId;

    @ApiModelProperty(value = "项目开票主体")
    private Long ticketCompanyId;

    @ApiModelProperty(value = "项目签订主体 1：总公司 2：分公司")
    private Integer project;

    @ApiModelProperty(value = "项目开票主体 1：总公司 2：分公司")
    private Integer ticket;

    @ApiModelProperty(value = "保证金收款 1：总公司 2：分公司")
    private Integer bondReceive;

    @ApiModelProperty(value = "代理费收款 1：总公司 2：分公司")
    private Integer agencyFeeReceive;

    @ApiModelProperty(value = "代理费开票 1：总公司 2：分公司")
    private Integer agencyFeeInvoice;

    @ApiModelProperty(value = "待推送账套")
    private List<PkOrgDTO> pkOrgList;

}
