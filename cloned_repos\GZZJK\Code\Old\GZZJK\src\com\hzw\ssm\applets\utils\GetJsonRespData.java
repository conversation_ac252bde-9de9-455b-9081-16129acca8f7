package com.hzw.ssm.applets.utils;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import org.apache.struts2.ServletActionContext;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;

public class GetJsonRespData {

    /**
     * 获取接口传递的JSON数据
     *
     * @return JSON格式数据
     * @explain
     */
    public static void getJsonRespData(Object resultJson) {
        try {

            //在对result进行json转换时，new Gson().toJson(result)，遇到null值不进行转换问题，网上搜了下，使用GsonBuilder创建Gson即可解决
            Gson gson = new GsonBuilder().serializeNulls().create();
            String toString = gson.toJson(resultJson);
            HttpServletResponse response = ServletActionContext.getResponse();
            response.setContentType("text/html;charset=utf-8");
            PrintWriter out = response.getWriter();
            out.print(toString);
            out.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 获取接口传递的JSON数据
     *
     * @return JSON格式数据
     * @explain
     */
    public static void getJsonRespDataNoNull(Object resultJson) {
        try {
            //在对result进行json转换时，new Gson().toJson(result)，遇到null值不进行转换问题，网上搜了下，使用GsonBuilder创建Gson即可解决
            String toString = new Gson().toJson(resultJson);
            HttpServletResponse response = ServletActionContext.getResponse();
            response.setContentType("text/html;charset=utf-8");
            PrintWriter out = response.getWriter();
            out.print(toString);
            out.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 获取接口传递的JSON数据
     *
     * @return JSON格式数据
     * @explain
     */
    public static void getTxtJsonRespDataNoNull(Object resultJson) {
        try {
            //在对result进行json转换时，new Gson().toJson(result)，遇到null值不进行转换问题，网上搜了下，使用GsonBuilder创建Gson即可解决
            Gson gson = new GsonBuilder().disableHtmlEscaping().create();
            String toString = gson.toJson(resultJson);
            HttpServletResponse response = ServletActionContext.getResponse();
            response.setContentType("text/html;charset=utf-8");
            PrintWriter out = response.getWriter();
            out.print(toString);
            out.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

}
