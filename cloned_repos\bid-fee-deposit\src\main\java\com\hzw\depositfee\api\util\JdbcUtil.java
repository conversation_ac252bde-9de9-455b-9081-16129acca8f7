package com.hzw.depositfee.api.util;


import java.sql.*;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class JdbcUtil {
    private Connection conn;
    private PreparedStatement pstmt;
    private ResultSet rs;

    public JdbcUtil(String driver, String url, String username, String password) {
        try {
            Class.forName(driver);
            this.conn = DriverManager.getConnection(url, username, password);
            System.out.println("数据库连接成功");
        } catch (Exception var6) {
            var6.printStackTrace();
        }

    }

    public Connection getConn() {
        return this.conn;
    }

    public boolean updateByParams(String sql, List params) throws SQLException {
        boolean result = true;
        this.pstmt = this.conn.prepareStatement(sql);
        int index = 1;
        if (null != params && !params.isEmpty()) {
            for(int i = 0; i < params.size(); ++i) {
                this.pstmt.setObject(index++, params.get(i));
            }
        }

        int i = this.pstmt.executeUpdate();
        return i > 0;
    }

    public List<Map> selectByParams(String sql, List params) throws SQLException {
        List<Map> list = new ArrayList();
        int index = 1;
        this.pstmt = this.conn.prepareStatement(sql);
        if (null != params && !params.isEmpty()) {
            for(int i = 0; i < params.size(); ++i) {
                this.pstmt.setObject(index++, params.get(i));
            }
        }

        this.rs = this.pstmt.executeQuery();
        ResultSetMetaData metaData = this.rs.getMetaData();
        int colsLen = metaData.getColumnCount();

        while(this.rs.next()) {
            Map map = new HashMap(colsLen);

            for(int i = 0; i < colsLen; ++i) {
                String columnName = metaData.getColumnName(i + 1);
                Object columnValue = this.rs.getObject(columnName);
                if (null == columnValue) {
                    columnValue = "";
                }

                map.put(columnName, columnValue);
            }

            list.add(map);
        }

        return list;
    }

    public void release() {
        try {
            if (null != this.rs) {
                this.rs.close();
            }

            if (null != this.pstmt) {
                this.pstmt.close();
            }

            if (null != this.conn) {
                this.conn.close();
            }
        } catch (SQLException var2) {
            var2.printStackTrace();
        }

        System.out.println("释放数据库连接");
    }
}

