package com.hzw.sunflower.controller.response;

import com.hzw.sunflower.entity.BondRefundDetails;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 银行利率表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-27
 */
@Data
public class BankRateListVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("银行名称")
    private String bankName;

    @ApiModelProperty("银行类型")
    private Integer bankType;

    @ApiModelProperty("调整之前的时间")
    private String beforeDate;

    @ApiModelProperty("生效日期")
    private String effectDate;

    @ApiModelProperty("利率")
    private BigDecimal rate;
}
