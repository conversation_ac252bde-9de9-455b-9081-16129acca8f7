package com.jszbtb.marketplayers.controller;

import com.jszbtb.marketplayers.entity.JsonResult;
import com.jszbtb.marketplayers.service.BiddetailsSearch;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@CrossOrigin
@RestController
@Api(tags = "主体库查询",value="BiddetailsSearchController",description = "主体库查询controller")
public class BiddetailsSearchController extends AbstractController {

    @Autowired
    private BiddetailsSearch biddetailsSearch;

    @ApiImplicitParams({
            @ApiImplicitParam(name="text",value="text",dataType = "String"),
            @ApiImplicitParam(name="pageSize",value="显示页数",required = true,dataType = "int",defaultValue = "10"),
            @ApiImplicitParam(name="pageIndex",value = "当前页数",required = true,dataType = "int",defaultValue = "1"),
            @ApiImplicitParam(name="startTime",value="开始时间",dataType = "String"),
            @ApiImplicitParam(name="endTime",value = "结束时间",dataType = "String"),
            @ApiImplicitParam(name="types",value="公告类型",required = true,dataType = "String",defaultValue = "bidder")
    })
    @RequestMapping(value = "/marketplayers/biddetailssearch",method = RequestMethod.GET)
    public JsonResult<List<Object>> select(
                                                     @RequestParam(name = "text",required = false)String text,
                                                     @RequestParam(value = "pageSize",defaultValue = "10")int pageSize,
                                                     @RequestParam(value = "pageIndex",defaultValue = "1")int pageIndex,
                                                     @RequestParam(name = "startTime",required = false)String startTime,
                                                     @RequestParam(name = "endTime",required = false)String endTime,
                                                     @RequestParam(name = "types",defaultValue = "bidder")String types){
        JsonResult<List<Object>> jsonResult = new JsonResult<>();
        try {
            jsonResult.setData(biddetailsSearch.select(text,startTime,endTime,pageSize,pageIndex,types));
            jsonResult.setSuccess(true);
        }catch (Exception e){
            this.logError(e);
            jsonResult.setErrorMessage(e.getMessage());
            jsonResult.setSuccess(false);
        }
        return jsonResult;
    }


    @ApiImplicitParams({
            @ApiImplicitParam(name="text",value="text",dataType = "String"),
            @ApiImplicitParam(name="pageSize",value="显示页数",required = true,dataType = "int",defaultValue = "10"),
            @ApiImplicitParam(name="pageIndex",value = "当前页数",required = true,dataType = "int",defaultValue = "1"),
            @ApiImplicitParam(name="startTime",value="开始时间",dataType = "String"),
            @ApiImplicitParam(name="endTime",value = "结束时间",dataType = "String"),
            @ApiImplicitParam(name="types",value="公告类型",required = true,dataType = "String",defaultValue = "bidder"),
            @ApiImplicitParam(name="scrollId",value = "滚动ID",dataType = "String")
    })
    @RequestMapping(value = "/marketplayers/biddetailsscroll",method = RequestMethod.GET)
    public JsonResult<Object> selectScroll(@RequestParam(name = "text",required = false)String text,
                                     @RequestParam(value = "pageSize",defaultValue = "10")int pageSize,
                                     @RequestParam(value = "pageIndex",defaultValue = "1")int pageIndex,
                                     @RequestParam(name = "startTime",required = false)String startTime,
                                     @RequestParam(name = "endTime",required = false)String endTime,
                                     @RequestParam(name = "types",defaultValue = "bidder")String types,
                                     @RequestParam(name = "scrollId",required = false)String scrollId){
        JsonResult<Object> jsonResult = new JsonResult<>();
        try {
            jsonResult.setData(biddetailsSearch.scroll(text,startTime,endTime,pageSize,pageIndex,types,scrollId));
            jsonResult.setSuccess(true);
        }catch (Exception e){
            this.logError(e);
            jsonResult.setErrorMessage(e.getMessage());
            jsonResult.setSuccess(false);
        }
        return jsonResult;
    }

    /*@RequestMapping(value = "/send",method = RequestMethod.GET)
    public JsonResult< List<JSONObject>> send(Map<String,String> map){
        JsonResult<List<JSONObject>> jsonResult = new JsonResult<>();
        try {
            biddetailsSearch.scopeSend(map);
            jsonResult.setSuccess(true);
        }catch (Exception e){
            this.logError(e);
            jsonResult.setErrorMessage(e.getMessage());
            jsonResult.setSuccess(false);
        }
        return jsonResult;
    }

    @RequestMapping("/test")
    public void test(){
        biddetailsSearch.insert("",null ,null,null );
    }*/


}
