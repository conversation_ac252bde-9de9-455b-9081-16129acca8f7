package com.hzw.sunflower.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hzw.sunflower.controller.response.AbnormalApplyRelationVo;
import com.hzw.sunflower.controller.response.BondApplyRelationVo;
import com.hzw.sunflower.entity.BondApplyRelation;
import com.hzw.sunflower.entity.condition.BondApplyRelationCondition;
import com.hzw.sunflower.entity.condition.BondProjectCondition;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 保证金申请关联表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-27
 */
@Mapper
public interface BondApplyRelationMapper extends BaseMapper<BondApplyRelation> {

    /**
     * 异常关联申请列表
     * @param page
     * @param condition
     * @return
     */
    IPage<AbnormalApplyRelationVo> applyRelationList(Page<AbnormalApplyRelationVo> page,@Param("condition") BondProjectCondition condition);

    /**
     * 分页查询保证金关联申请列表(包含异常关联)
     * @param page
     * @param condition
     * @return
     */
    IPage<BondApplyRelationVo> getBondApplyRelationPage(Page<BondApplyRelation> page,@Param("condition") BondApplyRelationCondition condition);


    /**
     * 查询异常关联保证金（已办）
     * @param applyId
     * @return
     */
    BondApplyRelation queryByIdCompleted(Long applyId);
}
