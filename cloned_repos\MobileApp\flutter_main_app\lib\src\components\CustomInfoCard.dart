///***
/// * 标题 + 副标题 + 标签 + 日期时间 + 数据字段键值对 - 卡片 UI
/// *
/// * 按以下顺序在竖直方向上依次显示
/// * 标题 - 必传
/// * 副标题 - 选传
/// * 标签组件列表 && 日期时间 - 选传
/// * 数据字段键值对 - 选传
/// */

import 'package:flutter/material.dart';

import 'package:flutter_main_app/src/constants/theme.dart';
import 'package:flutter_main_app/src/utils/ScreenUtils.dart';
import 'package:flutter_main_app/src/fonts/fonts.dart';

import 'package:flutter_main_app/src/components/CustomFieldDisplay.dart';
import 'package:flutter_main_app/src/components/CustomTab.dart';

class CustomInfoCard extends StatelessWidget {

  /// * 日期时间高度
  static const double _dateHeight = 17.0;

  const CustomInfoCard({
    Key key,
    @required this.title,
    this.titleFontSize = themeFontSize,
    this.titleColor = themeTitleColor,
    this.subTitle,
    this.subTitleFontSize = themeFontSize,
    this.subTitleColor = themeTipsTextColor,
    this.tabs = const [],
    this.date,
    this.dateFontSize = 10.0,
    this.dateColor = themeHintTextColor,
    this.dataFields = const [],
    this.dataFieldHeight = 22.0,
    this.dataFieldFontSize = 12.0,
    this.backgroundColor = themeContentBackgroundColor,
    this.isFirstChild = false,
  }) : assert(title != null && title.length > 0),
    assert(titleFontSize != null && titleFontSize > 10.0),
    assert(titleColor != null),
    assert(subTitle == null || subTitle.length > 0),
    assert(subTitleFontSize != null && subTitleFontSize >= 10.0 && subTitleFontSize <= titleFontSize),
    assert(subTitleColor != null),
    assert(tabs != null),
    assert(date == null || date.length > 0),
    assert(dateFontSize != null && dateFontSize >= 10.0),
    assert(dateColor != null),
    assert(dataFields != null),
    assert(dataFieldHeight != null && dataFieldHeight - dataFieldFontSize >= 4.0),
    assert(dataFieldFontSize != null && dataFieldFontSize > 10.0),
    assert(backgroundColor != null),
    assert(isFirstChild != null),
    super(key: key);

  /// * 主标题
  final String title;
  /// * 主标题字号
  final double titleFontSize;
  /// * 主标题字体颜色
  final Color titleColor;

  /// * 副标题
  final String subTitle;
  /// * 副标题字号
  final double subTitleFontSize;
  /// * 副标题字体颜色
  final Color subTitleColor;

  /// * 标签组件列表
  final List<CustomTab> tabs;

  /// * 日期时间
  final String date;
  /// * 主标题字号
  final double dateFontSize;
  /// * 主标题字体颜色
  final Color dateColor;

  /// * 数据字段键值对列表
  final List<Map<String, String>> dataFields;
  /// * 键值对高度
  final double dataFieldHeight;
  /// * 键值对字号
  final double dataFieldFontSize;

  /// * 背景颜色
  final Color backgroundColor;
  /// * 是否是父元素的第一个子元素
  /// * 用于控制一些特殊样式
  final bool isFirstChild;

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 15.0),
      padding: EdgeInsets.only(
        top: 12.0,
        bottom: this.dataFields.length > 0
          ? 12.0 - (this.dataFieldHeight - this.dataFieldFontSize - 4.0) / 2
          : 12.0,
      ),
      width: ScreenUtils.screenWidth,
      decoration: BoxDecoration(
        color: this.backgroundColor,
        border: Border(
          top: this.isFirstChild
            ? BorderSide.none
            : themeBorderSide,
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          /// * 主标题
          Text(
            this.title,
            style: TextStyle(
              fontSize: this.titleFontSize,
              color: this.titleColor,
            ),
          ),
          /// * 副标题
          if (this.subTitle != null)
            this._buildSubTitle(),
          /// * 标签 && 日期时间
          if (this.tabs.length > 0 || this.date != null)
            this._buildTabsAndDate(),
          /// * 数据字段键值对
          if (this.dataFields.length > 0)
            this._buildDataFieldsWidget(),
        ],
      ),
    );
  }

  ///***
  /// * 生成副标题 UI
  /// *
  /// * @return {Widget}
  /// */
  Widget _buildSubTitle() {
    final Widget subTitle = Padding(
      padding: const EdgeInsets.only(top: 8.0),
      child: Text(
        this.subTitle,
        style: TextStyle(
          fontSize: this.subTitleFontSize,
          color: this.subTitleColor,
        ),
      ),
    );

    return subTitle;
  }

  ///***
  /// * 生成标签 && 日期时间 UI
  /// *
  /// * @return {Widget}
  /// */
  Widget _buildTabsAndDate() {
    final List<Widget> tabs = [];

    for (int i = 0, len = this.tabs.length; i < len; i++) {
      final Widget tab = Padding(
        padding: const EdgeInsets.only(
          top: 12.0,
          right: 10.0,
        ),
        child: this.tabs[i],
      );

      tabs.add(tab);
    }

    final Widget date = Container(
      margin: const EdgeInsets.only(top: 12.0),
      height: _dateHeight,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: <Widget>[
          Icon(
            FontsHelper.clockIconData,
            size: this.dateFontSize,
            color: this.dateColor,
          ),
          Padding(
            padding: const EdgeInsets.only(left: 2.0),
            child: Text(
              this.date ?? '',
              style: TextStyle(
                fontSize: this.dateFontSize,
                color: this.dateColor,
              ),
            ),
          ),
        ],
      ),
    );

    final Widget tabsAndDate = Row(
      mainAxisSize: MainAxisSize.max,
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        tabs.length > 0
          ? Expanded(
              child: Wrap(
                children: tabs,
              ),
            )
          : Container(),
        this.date == null
          ? Container()
          : date,
      ],
    );

    return tabsAndDate;
  }

  ///***
  /// * 生成数据字段键值对 UI
  /// * 包括外部边距等
  /// *
  /// * @return {Widget}
  /// */
  Widget _buildDataFieldsWidget() {
    final List<Widget> dataFieldWidgets = [];

    for (int i = 0, len = this.dataFields.length; i < len; i++) {
      final Widget dataFieldWidget = CustomFieldDisplay(
        fieldName: this.dataFields[i]['fieldName'] ?? '',
        fieldValue: this.dataFields[i]['fieldValue'] ?? '',
        height: this.dataFieldHeight,
        fontSize: this.dataFieldFontSize,
      );

      dataFieldWidgets.add(dataFieldWidget);
    }

    return Padding(
      padding: const EdgeInsets.only(top: 9.0),
      child: Column(
        children: dataFieldWidgets,
      ),
    );
  }
}
