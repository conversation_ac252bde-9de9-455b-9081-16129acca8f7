package com.hzw.sunflower.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date ：Created in 2021/10/12 13:54
 * @description：重新招标历史记录
 * @version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProjectRecordDTO implements Serializable {
    @ApiModelProperty(value = "项目ID", position = 1)
    private Long projectId;

    @ApiModelProperty(value = "采购项目名称", position = 2)
    private String purchaseName;

    @ApiModelProperty(value = "采购编号", position = 3)
    private String purchaseNumber;

    @ApiModelProperty(value = "包段编号", position = 4)
    private Integer packageNumber;

    @ApiModelProperty(value = "初始标段编号", position = 5)
    private Integer basePackageNumber;

    @ApiModelProperty(value = "创建时间", position = 6)
    private Date createdTime;

    @ApiModelProperty(value = "标段id", position = 6)
    private Long sectionId;

    @ApiModelProperty(value = "上一个标段", position = 6)
    private Long formerSectionId;

    @ApiModelProperty(value = "上一标段号", position = 4)
    private Integer formerPackageNumber;

    @ApiModelProperty(value = "原始标段id", position = 4)
    private Long baseSectionId;

}
