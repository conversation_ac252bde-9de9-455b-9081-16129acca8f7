package com.hzw.sunflower.controller.request;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * Created by zgq on 2021/07/05
 *
 * 通用请求实体
 */

@ApiModel(description = "删除媒体请求实体")
@Data
@Accessors(chain = true)
public class DeleteNoticeMediaREQ {

    @ApiModelProperty(value = "公告ID", position = 1)
    private Long noticeId;

    @ApiModelProperty(value = "媒体ID", position = 2)
    private Long mediaId;
}
