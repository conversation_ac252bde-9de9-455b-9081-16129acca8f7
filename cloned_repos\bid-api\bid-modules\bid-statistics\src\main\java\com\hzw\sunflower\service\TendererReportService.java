package com.hzw.sunflower.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hzw.sunflower.controller.response.ProjectCountVo;
import com.hzw.sunflower.entity.JwtUser;
import com.hzw.sunflower.entity.TendererReport;
import com.hzw.sunflower.entity.condition.TendererReportCondition;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface TendererReportService extends IService<TendererReport> {
    /**
     * 项目数量
     * @param condition
     * @param jwtUser
     * @return
     */
    List<ProjectCountVo> findProjectCount(TendererReportCondition condition, JwtUser jwtUser);

    /**
     * 项目开标信息
     * @param condition
     * @param jwtUser
     * @return
     */
    IPage<TendererReport> findOpenBidInfo(TendererReportCondition condition, JwtUser jwtUser);

    /**
     * 导出项目开标信息
     * @param condition
     * @param response
     * @param jwtUser
     */
    void exportExcel(TendererReportCondition condition, HttpServletResponse response, JwtUser jwtUser);

    /**
     * 项目报名信息
     * @param condition
     * @param jwtUser
     * @return
     */
    IPage<TendererReport> applyInfoList(TendererReportCondition condition, JwtUser jwtUser);

    /**
     * 导出项目报名信息
     * @param condition
     * @param response
     * @param jwtUser
     */
    void exportApplyInfoList(TendererReportCondition condition, HttpServletResponse response, JwtUser jwtUser);
}
