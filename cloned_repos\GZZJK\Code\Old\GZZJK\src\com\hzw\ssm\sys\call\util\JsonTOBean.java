package com.hzw.ssm.sys.call.util;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import com.fasterxml.jackson.core.JsonParseException;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.ObjectMapper;

public class JsonTOBean {
	/**
	 * 
	 * java实体类转json
	 * Object:实体类对象
	 * @throws JsonProcessingException 
	 */
	public  String JavaBeanToJson(Object t) throws JsonProcessingException {
		
		/** 
	     * ObjectMapper是JSON操作的核心，Jackson的所有JSON操作都是在ObjectMapper中实现。 
	     * ObjectMapper有多个JSON序列化的方法，可以把JSON字符串保存File、OutputStream等不同的介质中。 
	     * writeValue(File arg0, Object arg1)把arg1转成json序列，并保存到arg0文件中。 
	     * writeValue(OutputStream arg0, Object arg1)把arg1转成json序列，并保存到arg0输出流中。 
	     * writeValueAsBytes(Object arg0)把arg0转成json序列，并把结果输出成字节数组。 
	     * writeValueAsString(Object arg0)把arg0转成json序列，并把结果输出成字符串。 
	     */
		ObjectMapper mapper=new ObjectMapper();
		//类转json
		String json=mapper.writeValueAsString(t);
		//
		
		return json;

	}
	/**
	 * Java集合转Json
	 * @param <T>
	 * @param <T>
	 * @param interentity
	 * @throws JsonProcessingException 
	 */
	public   <T> String JavaListToJson(List<T> interentity) throws JsonProcessingException{
		
		ObjectMapper mapper=new ObjectMapper();
		
		//java集合转JSON
		String jsonlst=mapper.writeValueAsString(interentity);
		
		return jsonlst;
	}
	
	public   <T> String JavaSetToJson(Set<T> interentity) throws JsonProcessingException{
		
		ObjectMapper mapper=new ObjectMapper();
		
		//java集合转JSON
		String jsonlst=mapper.writeValueAsString(interentity);
		
		return jsonlst;
	}
	
	/**
	 * 
	 * @param json Json数据
	 * @param clazz 实体类的class
	 * @return
	 * @throws JsonParseException
	 * @throws JsonMappingException
	 * @throws IOException
	 */
	public  <T> T JsonToJavaBean(String json,Class<T> clazz) throws JsonParseException, JsonMappingException, IOException{
		/** 
	     * ObjectMapper支持从byte[]、File、InputStream、字符串等数据的JSON反序列化。 
	     */
		ObjectMapper mapper = new ObjectMapper(); 	
		
		//当json的数据比bean中的属性的要多，或者bean对象中的属性比传过来的json中的数据属性要多的时候需要添加这段代码
		mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
		T t = mapper.readValue(json, clazz);
		return t;
		
	}
	
	
	/*public static <T> List<T> JsonToListBean(String json) throws JsonParseException, JsonMappingException, IOException
	{
				ObjectMapper mapper = new ObjectMapper();
				//当json的数据比bean中的属性的要多，或者bean对象中的属性比传过来的json中的数据属性要多的时候需要添加这段代码
				mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
				
				List<T> slst=mapper.readValue(json,List.class);
				
				return slst;
		
	}*/
	
	public  <T> List<T> JsonToListBean(String json,Class<T> entity) throws JsonParseException, JsonMappingException, IOException
	{
				ObjectMapper mapper = new ObjectMapper();
				//当json的数据比bean中的属性的要多，或者bean对象中的属性比传过来的json中的数据属性要多的时候需要添加这段代码
				mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
				
				List<T> slst=mapper.readValue(json, mapper.getTypeFactory().constructParametricType(ArrayList.class, entity));
				
			//	List<SolrInfoEntity> slst=mapper.readValue(json,List.class);
				/*List<LinkedHashMap<String,SolrInfoEntity>> slst=mapper.readValue(json,List.class);
				for (int i = 0; i < slst.size(); i++) {
				    Map<String, SolrInfoEntity> map = slst.get(i);
				    Set<String> set = map.keySet();
				    for (Iterator<String> it = set.iterator(); it.hasNext();) {
				     String key = it.next();
				     System.out.println(key + ":" + map.get(key));
				    }
				   }*/
				return slst;
	}
	
	public <T> Set<T> JsonToSetBean(String json,Class<T> entity) throws JsonParseException, JsonMappingException, IOException
	{
		ObjectMapper mapper = new ObjectMapper();
		//当json的数据比bean中的属性的要多，或者bean对象中的属性比传过来的json中的数据属性要多的时候需要添加这段代码
		mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
		
		HashSet<T> hs=mapper.readValue(json, mapper.getTypeFactory().constructParametricType(Set.class, entity));
		
		return hs;
	}

}
