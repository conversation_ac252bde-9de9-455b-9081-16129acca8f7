package com.hzw.sunflower.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.hzw.sunflower.common.BaseBean;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date ：Created in 2021/8/27 11:24
 * @description：标段/包按套销售表
 * @version: 1.0
 */
@ApiModel("标段/包按套销售表")
@TableName("t_section_group")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SectionGroup extends BaseBean {
    /**
     * 主键ID
     *
     * @mbg.generated
     */
    private Long id;

    /**
     * 项目ID
     *
     * @mbg.generated
     */
    private Long projectId;

    /**
     * 按套关联标段信息（前端展示）
     *
     * @mbg.generated
     */
    private String groupInfo;

    /**
     * 按套关联标段id集合
     *
     * @mbg.generated
     */
    private String sectionIds;
}
