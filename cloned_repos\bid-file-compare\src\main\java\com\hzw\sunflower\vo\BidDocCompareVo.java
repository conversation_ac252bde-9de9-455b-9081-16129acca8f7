package com.hzw.sunflower.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 投标文件比对请求参数
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-09
 */
@Data
public class BidDocCompareVo {

    @ApiModelProperty("供应商ID")
    private Long companyId;

    @ApiModelProperty("标段ID")
    private Long sectionId;

    @ApiModelProperty("投标文件ID")
    private Long ossId;

    @ApiModelProperty("ossFileKey")
    private String ossFileKey;

    @ApiModelProperty("投标文件IDS")
    private String ossIds;

    @ApiModelProperty("招标文件内容")
    private String content;

    @ApiModelProperty("文件相似度")
    private String similarity;

}
