package com.hzw.ssm.expert.action;

import java.util.List;

import org.apache.struts2.convention.annotation.Action;
import org.apache.struts2.convention.annotation.Namespace;
import org.apache.struts2.convention.annotation.ParentPackage;
import org.apache.struts2.convention.annotation.Result;
import org.apache.struts2.convention.annotation.Results;
import org.springframework.beans.factory.annotation.Autowired;

import com.hzw.ssm.expert.entity.ExpertNotParticipateEntity;
import com.hzw.ssm.expert.service.ExpertNotParticipateService;
import com.hzw.ssm.fw.base.BaseAction;

/**
 * 专家不参加项目统计
 * 
 * <AUTHOR> 
 */
@Namespace("/expertNotParticipate")
@ParentPackage(value = "default")
@Results( { @Result(name = "expertNotPList", location = "/jsp/expert/expert_not_list.jsp")})
public class ExpertNotParticipateAction extends BaseAction {

	private static final long serialVersionUID = -8823858495799309882L;

	@Autowired
	private ExpertNotParticipateService enpService;
	
	private ExpertNotParticipateEntity expertNotParticipateEntity;

	private List<ExpertNotParticipateEntity> enpList;
	/**
	 * 查询参加评标的专家
	 * @return
	 */
	@Action("expertNotPList")
	public String appraiseExpertList(){
		this.context();
		if(expertNotParticipateEntity == null){
			expertNotParticipateEntity = new ExpertNotParticipateEntity();
		}
		expertNotParticipateEntity.setPage(this.getPage());
		enpList = enpService.queryPageENPList(expertNotParticipateEntity);
		return "expertNotPList";
	}

	public ExpertNotParticipateEntity getExpertNotParticipateEntity() {
		return expertNotParticipateEntity;
	}
	public void setExpertNotParticipateEntity(ExpertNotParticipateEntity expertNotParticipateEntity) {
		this.expertNotParticipateEntity = expertNotParticipateEntity;
	}
	public List<ExpertNotParticipateEntity> getEnpList() {
		return enpList;
	}

	public void setEnpList(List<ExpertNotParticipateEntity> enpList) {
		this.enpList = enpList;
	}

}
