#!/bin/sh
#cp TEMPLATE FILE
docker stop portal-resource-server || true
docker rm portal-resource-server || true
docker rmi registry.cn-hangzhou.aliyuncs.com/hzw-jtcc/jtcc:portal-resource-server-1.0.0 || true
docker pull registry.cn-hangzhou.aliyuncs.com/hzw-jtcc/jtcc:portal-resource-server-1.0.0
docker run -itd --net=host  --log-opt max-size=100m --log-opt max-file=3  --name portal-resource-server -v /home/<USER>/plan1/portal-cloud/config/portal-resource-server:/hzw/resource/config -v /data/log:/hzw/resource/logs -v /data/temporary:/data/temporary  registry.cn-hangzhou.aliyuncs.com/hzw-jtcc/jtcc:portal-resource-server-1.0.0
