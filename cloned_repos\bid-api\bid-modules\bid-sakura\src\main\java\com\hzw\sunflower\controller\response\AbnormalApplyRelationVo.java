package com.hzw.sunflower.controller.response;

import com.hzw.sunflower.entity.OssFile;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 异常关联申请
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-27
 */
@Data
@ApiModel(value = "BondWater对象", description = "异常关联申请")
public class AbnormalApplyRelationVo implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long applyRelationId;

    @ApiModelProperty("项目id")
    private Long projectId;

    @ApiModelProperty("项目编号")
    private String projectNum;

    @ApiModelProperty("项目名称")
    private String projectName;

    @ApiModelProperty("包名称")
    private String packageName;

    @ApiModelProperty("文件递交截止时间")
    private Date submitEndTime;

    @ApiModelProperty("申请内容")
    private String content;

    @ApiModelProperty("申请人")
    private String userName;

    @ApiModelProperty("部门")
    private String departmentName;

    @ApiModelProperty("申请时间")
    private Date applyTime;

    @ApiModelProperty("包段编号")
    private Integer packageNum;

    @ApiModelProperty("供应商名称")
    private String companyName;

    @ApiModelProperty("支付凭证文件id")
    private String paymentVoucherIds;

    @ApiModelProperty("支付凭证文件")
    private List<OssFile> ossFileList;

}
