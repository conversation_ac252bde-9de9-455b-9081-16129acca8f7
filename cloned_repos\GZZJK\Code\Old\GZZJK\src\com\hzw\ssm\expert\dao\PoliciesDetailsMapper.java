package com.hzw.ssm.expert.dao;


import com.hzw.ssm.applets.utils.ResultJson;
import com.hzw.ssm.expert.entity.Policies;
import com.hzw.ssm.expert.entity.PoliciesDetails;

import java.util.List;

public interface PoliciesDetailsMapper {
    // <!-- 查询所有政策法规目录详情 -->
  List<PoliciesDetails> findPoliciesDetailsAll();
  //<!-- 根据条件参数查询政策法规目录详情列表 -->
  List<PoliciesDetails>     findPoliciesDetailsByCondition(PoliciesDetails policiesDetails);
  List<PoliciesDetails>     findTitlePoliciesDetailsByCondition(PoliciesDetails policiesDetails);
    //<!-- 根据条件分页参数查询政策法规目录详情列表 -->
    List<PoliciesDetails>  queryPagePoliciesDetailsByCondition(PoliciesDetails policiesDetails);
   // <!-- 根据主键查询政策法规目录详情信息 -->
    List<PoliciesDetails>  findPoliciesDetailsByIds(List<String> list);
    //<!-- 新增政策法规目录详情信息 -->
    Integer addPoliciesDetails(PoliciesDetails policiesDetails);
    PoliciesDetails findPoliciesDetailsById(String id);
    //批量添加
    Integer addPoliciesDetailss(PoliciesDetails policiesDetails);
    //更新
    Integer updatePoliciesDetails(PoliciesDetails policiesDetails);

    //批量更新
    Integer updatePoliciesDetailss(PoliciesDetails policiesDetails);

    //删除
    Integer deletePoliciesDetailsById(String id);
    //批量删除
    Integer deletePoliciesDetailsByIds(List<String> list);

    ResultJson policiesQueryPage(Policies policies);
}
