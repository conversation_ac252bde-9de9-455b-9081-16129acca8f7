spring:
  datasource:
    dynamic:
      # 设置默认的数据源或者数据源组,默认值即为 master
      primary: master
      datasource:
        # 主库数据源
        master:
          type: ${spring.datasource.type}
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: ${datasource.system-master.url}
          username: ${datasource.system-master.username}
          password: ${datasource.system-master.password}
#模板文件配置
files:
  template:
    path: /data/template/
  temporary:
    path: /data/temporary/
# 自定义配置
bid:
  homePage: https://cg.jstcc.cn
media:
  domain: https://testapi.jszbtb.com/
  username: jstcc
  password: jshzw.cn
  platformCode: K3200001801
  pubservicePlatCode: X3200000028