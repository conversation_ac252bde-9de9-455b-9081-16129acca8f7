#!/bin/sh
#cp TEMPLATE FILE
docker stop bid-job-server || true
docker rm bid-job-server || true
docker rmi registry.cn-hangzhou.aliyuncs.com/hzw-jtcc/jtcc:bid-job-server-1.0.0 || true
docker pull registry.cn-hangzhou.aliyuncs.com/hzw-jtcc/jtcc:bid-job-server-1.0.0
docker run -itd --net=host  --log-opt max-size=100m --log-opt max-file=3  --name bid-job-server -v /home/<USER>/plan1/bid-cloud/config/bid-job-server:/hzw/job/config -v /data/log:/hzw/job/logs  registry.cn-hangzhou.aliyuncs.com/hzw-jtcc/jtcc:bid-job-server-1.0.0
