package com.hzw.sunflower.service.bidder;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.constant.CommonConstants;
import com.hzw.sunflower.constant.constantenum.*;
import com.hzw.sunflower.controller.project.request.ProjectAcceptanceRep;
import com.hzw.sunflower.controller.project.request.ReProjectSection;
import com.hzw.sunflower.controller.project.request.bidder.*;
import com.hzw.sunflower.controller.project.response.ProjectAcceptanceListVo;
import com.hzw.sunflower.controller.project.response.ProjectBidSectionVO;
import com.hzw.sunflower.controller.project.response.bidder.LenovoTenantUserVo;
import com.hzw.sunflower.controller.project.response.bidder.MyselfProjectVo;
import com.hzw.sunflower.controller.project.response.bidder.ReSectionVo;
import com.hzw.sunflower.datascope.utils.DataScopeUtil;
import com.hzw.sunflower.exception.ExceptionEnum;
import com.hzw.sunflower.exception.SunFlowerException;
import com.hzw.sunflower.security.utils.SecurityUtils;
import com.hzw.sunflower.util.BeanListUtil;
import com.hzw.sunflower.constant.UserConstants;
import com.hzw.sunflower.controller.project.request.EntrustProjectReq;
import com.hzw.sunflower.dao.ProjectMapper;
import com.hzw.sunflower.dto.*;
import com.hzw.sunflower.dto.bidder.BidderProjectCondition;
import com.hzw.sunflower.entity.*;
import com.hzw.sunflower.service.*;
import com.hzw.sunflower.util.RequestUtil;
import lombok.val;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date ：Created in 2021/6/10 9:53
 * @description：招标人项目impl
 * @modified By：`
 * @version: 1.0
 */
@Service
public class BidderProjectServiceImpl extends ServiceImpl<ProjectMapper, Project> implements BidderProjectService {


    @Autowired
    private UserAgentRecordService userAgentRecordService;

    @Autowired
    private ProjectEntrustUserService projectEntrustUserService;

    @Autowired
    private ProjectService projectService;

    @Autowired
    private ProjectEntrPersNumService projectEntrPersNumService;

    @Autowired
    private ProjectBidSectionService projectBidSectionService;

    @Autowired
    private CommonSectionService commonSectionService;

    @Autowired
    private ProjectAgentApprovalService projectAgentApprovalService;

    @Autowired
    private ProjectTendererPowerService projectTendererPowerService;

    @Autowired
    private ProcessRecordService processRecordService;

    @Autowired
    private CompanyService companyService;

    @Autowired
    private UserService userService;

    @Autowired
    private UserIdentityService userIdentityService;

    @Autowired
    private ProjectMapper projectMapper;

    @Autowired
    private ISectionExpertSpecialtyService sectionExpertSpecialtyService;

    @Autowired
    private RabbitMqService rabbitMqService;

    @Value("${organ.organization_num:}")
    private String organizationNum;

    @Value("${organ.organization_type:}")
    private Integer organizationType;

    @Autowired
    private ISectionExpertSpecialtyService iSectionExpertSpecialtyService;

    @Autowired
    private RUserDepartmentService rUserDepartmentService;

    @Autowired
    private SystemSettingService systemSettingService;

    @Autowired
    private ProjectSerialNumService projectSerialNumService;

    /**
     * 根据条件分页查询项目表 列表
     *
     * @param req 查询条件
     * @return 分页数据
     */
    @Override
    public IPage<ProjectListDTO> queryEntrustProjectByPage(ProjectConditionRep req, Long userId) {
        BidderProjectCondition condition = BeanListUtil.convert(req, BidderProjectCondition.class);
        if(null != condition.getProjectStatus()  && !"".equals(condition.getProjectStatus()) && ProjectStatusEnum.QQZB.getName().contains(condition.getProjectStatus())){
            condition.setProjectStatus(ProjectStatusEnum.WAIT_ACCEPT.getName());
        }
        condition.setUserId(userId);
        //如果租户是招标人，添加部门条件
        if (CompanyEnum.BIDDER.getType().equals(organizationType)) {
            condition.setDepartId(SecurityUtils.getJwtUser().getUser().getDepartId());
        }
        condition.setType(EntrustUserType.PRINCIPAL.getType());
        // 处理数据权限
        String datascopesql=DataScopeUtil.getProjectDataScopeFilterSql("rud","ptp","p");
        condition.setDataScope(datascopesql);
        IPage<ProjectListDTO> page=this.baseMapper.queryBidderEntrustProjectByPage(condition.customizeBuildPage(), condition);
        List<ProjectListDTO> records = new ArrayList<>();
        for (ProjectListDTO item : page.getRecords()) {
            QueryWrapper<ProjectBidSection> queryWrapper=new QueryWrapper<>();
            queryWrapper.lambda()
                    .eq(ProjectBidSection::getProjectId,item.getId())
                    .orderByAsc(ProjectBidSection::getPackageNumber)
                    .eq(ProjectBidSection::getIsDelete, CommonConstants.NO);

            List<ProjectBidSection> list = projectBidSectionService.list(queryWrapper);
            List<String> strBidRound= list.stream().map(p->p.getBidRound()+"").collect(Collectors.toList());
            item.setPackagesBidRound(strBidRound);
            List<String> strBidrondOne=list.stream().filter(p->p.getBidRound().equals(1)).map(p->p.getStatus()).collect(Collectors.toList());
            item.setPackagesBidRoundOne(strBidrondOne);
            List<String> strBidrondTwo=list.stream().filter(p->p.getBidRound().equals(2)).map(p->p.getStatus()).collect(Collectors.toList());
            item.setPackagesBidRoundTwo(strBidrondTwo);

            records.add(item);
        }
        page.setRecords(records);


        return page;
    }

    /**
     * 根据条件分页查询项目表 列表
     *
     * @param req 查询条件
     * @return 分页数据
     */
    @Override
    public IPage<ProjectListDTO> queryProjectByPage(ProjectConditionRep req, Long userId) {
        BidderProjectCondition condition = BeanListUtil.convert(req, BidderProjectCondition.class);
        condition.setUserId(userId);
        //如果租户是招标人，添加部门条件
        if (CompanyEnum.BIDDER.getType().equals(organizationType)) {
            condition.setDepartId(SecurityUtils.getJwtUser().getUser().getDepartId());
        }
        condition.setType(EntrustUserType.PRINCIPAL.getType());
        IPage<ProjectListDTO> page=this.baseMapper.queryBidderProjectByPage(condition.customizeBuildPage(), condition);
        List<ProjectListDTO> records = new ArrayList<>();
        for (ProjectListDTO item : page.getRecords()) {
            QueryWrapper<ProjectBidSection> queryWrapper=new QueryWrapper<>();
            queryWrapper.lambda()
                    .eq(ProjectBidSection::getProjectId,item.getId())
                    .orderByAsc(ProjectBidSection::getPackageNumber)
                    .eq(ProjectBidSection::getIsDelete, CommonConstants.NO);

            List<ProjectBidSection> list = projectBidSectionService.list(queryWrapper);
            List<String> strBidRound= list.stream().map(p->p.getBidRound()+"").collect(Collectors.toList());
            item.setPackagesBidRound(strBidRound);
            List<String> strBidrondOne=list.stream().filter(p->p.getBidRound().equals(1)).map(p->p.getStatus()).collect(Collectors.toList());
            item.setPackagesBidRoundOne(strBidrondOne);
            List<String> strBidrondTwo=list.stream().filter(p->p.getBidRound().equals(2)).map(p->p.getStatus()).collect(Collectors.toList());
            item.setPackagesBidRoundTwo(strBidrondTwo);

            records.add(item);
        }
        page.setRecords(records);


        return page;
    }

    /**
     * 代理机构新增委托项目入口
     *
     * @param agentProject
     * @return
     */
    @Override
    public Boolean addAgentProject(BidderProjectReq agentProject, JwtUser user) {
        Project project = new Project();
        //保存项目信息
        boolean flag = addProject(agentProject, project);
        if (flag) {
            //保存委托人关系数据
            agentProject.setProjectId(project.getId());
            saveProjectEntrusetUser(agentProject, user);
            //保存本次代理机构信息
//            lastAgentRecord(agentProject, user.getUserId());
            LambdaQueryWrapper<ProjectAgentApproval> lqw = new LambdaQueryWrapper();
            lqw.eq(ProjectAgentApproval::getProjectId,agentProject.getId());
            ProjectAgentApproval one = projectAgentApprovalService.getOne(lqw);
            List<Integer> status = new ArrayList<>();
            status.add(ProjectTendereeStatusEnum.XMJL_ACCEPTED.getCode());
            status.add(ProjectTendereeStatusEnum.XMJL_PERFECT.getCode());
            if((null !=one && !status.contains(one.getStatus())) || BeanUtil.isEmpty(one)){
                lastAgentRecord(agentProject, agentProject.getAgentChargeId());
                lastAgent(agentProject, agentProject.getAgentChargeId());
            }

            bidderProject(agentProject, project);

            if(null != agentProject.getId()){
                LambdaQueryWrapper<ProjectTendererPower> lwq = new LambdaQueryWrapper<>();
                lwq.eq(ProjectTendererPower::getProjectId,agentProject.getId());
                projectTendererPowerService.remove(lwq);
            }
            // 保存招标人权限数据
            agentProject.getPowerList().forEach(p->p.setProjectId(project.getId()));
            projectTendererPowerService.saveBatch(agentProject.getPowerList());
        }
        projectService.saveOrUpdateProjectRedundantFieldDealWith(project.getId(),true);
        commonSectionService.saveOrUpdateProjectPackageFieldDealWith(project.getId());
        return flag;
    }

    /**
     * 获取上一次用户填写的代理机构信息
     *
     * @return
     */
    @Override
    public UserAgentRecordDTO selectAgent(JwtUser user) {
        UserAgentRecordDTO dto = null;
        LambdaQueryWrapper<UserAgentRecord> userAgent = new LambdaQueryWrapper<UserAgentRecord>();
//        userAgent.eq(UserAgentRecord::getUserId, user.getUserId());
        userAgent.orderByDesc(UserAgentRecord::getCreatedTime);
        val list = userAgentRecordService.list(userAgent);
        //非空
        if (!CollectionUtils.isEmpty(list)) {
            UserAgentRecord record = list.get(0);
            dto = BeanListUtil.convert(record, UserAgentRecordDTO.class);
        }
        return dto;
    }

    @Override
    public Boolean updateProject(BidderProjectReq agentProject, JwtUser jwtUser) {
        Project project = new Project();
        //保存项目信息
        project.setId(agentProject.getProjectId());
        boolean flag = addProject(agentProject, project);
        if (flag) {
            //保存委托人关系数据
            agentProject.setProjectId(project.getId());
            flag = saveProjectEntrusetUser(agentProject, jwtUser);
        }
        if (flag) {
            bidderProject(agentProject, project);
        }
        if (flag) {
            //保存本次代理机构信息
            lastAgentRecord(agentProject, jwtUser.getUserId());
            LambdaQueryWrapper<ProjectAgentApproval> ppaq = new LambdaQueryWrapper<>();
            ppaq.eq(ProjectAgentApproval::getProjectId,project.getId());
            ProjectAgentApproval agentApproval = projectAgentApprovalService.getBaseMapper().selectOne(ppaq);
            if(null != agentApproval && ProjectTendereeStatusEnum.XMJL_ACCEPTED.getCode().equals(agentApproval.getStatus())){
                lastAgent(agentProject, agentProject.getAgentChargeId());
            }
        }
        projectService.saveOrUpdateProjectRedundantFieldDealWith(project.getId(),false);
        commonSectionService.saveOrUpdateProjectPackageFieldDealWith(project.getId());
        return flag;
    }

    /**
     * 根据项目id 查询项目信息
     *
     * @param id
     */
    @Override
    public AgentProjectDTO getProject(Long id) {
        AgentProjectDTO dto = new AgentProjectDTO();
        //获取项目信息
        Project project = this.getById(id);
        BeanListUtil.copyProperties(project, dto);
        //查询所有关系数据
        AgentInItDto projectUser = projectEntrustUserService.getEntrustUserByProjectIdAndType(id, EntrustUserType.ENTRUSTED_PERSON.getType());
        dto.setPagentUserDto(projectUser);
        //获取委托项目信息
        LambdaQueryWrapper<ProjectEntrPersNum> projectNum = new LambdaQueryWrapper<ProjectEntrPersNum>();
        projectNum.eq(ProjectEntrPersNum::getProjectId, id);
        List<ProjectEntrPersNum> list = projectEntrPersNumService.list(projectNum);
        dto.setProjectEntrPersNumDTO(BeanListUtil.convertList(list, ProjectEntrPersNumDTO.class));
        return dto;
    }

    @Override
    public Result<Boolean> deleteProject(Long id) {
        Project projectById = projectService.getProjectById(id);
        LambdaQueryWrapper<ProjectTendererPower> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjectTendererPower::getProjectId,id).eq(ProjectTendererPower::getIsAdmin,CommonConstants.YES);
        ProjectTendererPower one = projectTendererPowerService.getOne(queryWrapper);
        if(null != projectById
                && one.getUserId().equals(SecurityUtils.getJwtUser().getUserId())){
            Boolean flag = projectService.deleteProjectById(id);
            if(flag){
               return Result.ok();
            }
        }else{
            // todo 错误提示 项目不存在
        }

        return Result.failed();
    }

    @Override
    public Result<Boolean> updateRecall(Long id) {
        Project projectById = projectService.getProjectById(id);
        LambdaQueryWrapper<ProjectTendererPower> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjectTendererPower::getProjectId,id).eq(ProjectTendererPower::getIsAdmin,CommonConstants.YES);
        ProjectTendererPower one = projectTendererPowerService.getOne(queryWrapper);
        if(null != projectById
                && one.getUserId().equals(SecurityUtils.getJwtUser().getUserId())){
            LambdaQueryWrapper<ProjectAgentApproval> paa = new LambdaQueryWrapper<>();
            paa.eq(ProjectAgentApproval::getProjectId,id);
            ProjectAgentApproval approval = projectAgentApprovalService.getBaseMapper().selectOne(paa);
            if(null != approval){
                ProjectAgentApproval agentApproval = new ProjectAgentApproval();
                agentApproval.setId(approval.getId());
                agentApproval.setStatus(ProjectTendereeStatusEnum.CH.getCode());
                boolean b = projectAgentApprovalService.updateById(agentApproval);
                if(b){
                    return  Result.ok();
                }
            }
        }else{// todo 没有项目权限

        }
        return  Result.failed();
    }

    @Override
    public Result<Boolean> updateSubmandate(ProjectSubmandateReq req) {
        Long userId = SecurityUtils.getJwtUser().getUserId();
        // 被转移人
        User user = userService.getBaseMapper().selectById(req.getUserId());
        LambdaQueryWrapper<UserIdentity>  lqwui = new LambdaQueryWrapper<>();
        lqwui.eq(UserIdentity::getUserId,req.getUserId());
        lqwui.eq(UserIdentity::getIdentity,EnterSourceEnum.TENDEREE.getType());
        UserIdentity userIdentity = userIdentityService.getBaseMapper().selectOne(lqwui);
        boolean b = false;
        LambdaQueryWrapper<ProjectEntrustUser> peu = new LambdaQueryWrapper<>();
        peu.eq(ProjectEntrustUser::getType,EntrustUserType.PRINCIPAL.getType());
        peu.eq(ProjectEntrustUser::getProjectId,req.getProjectId());
        peu.eq(ProjectEntrustUser::getUserId,userId);
        ProjectEntrustUser projectEntrustUser = projectEntrustUserService.getBaseMapper().selectOne(peu);
        // 招标人在同一家单位内部转
        if(null != projectEntrustUser && SecurityUtils.getJwtUser().getCompanyId().equals(req.getCompanyId())){
            LambdaQueryWrapper<ProjectEntrustUser> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(ProjectEntrustUser::getType,EntrustUserType.PRINCIPAL.getType());
            queryWrapper.eq(ProjectEntrustUser::getProjectId,req.getProjectId());
            queryWrapper.eq(ProjectEntrustUser::getUserId,req.getUserId());
            ProjectEntrustUser entrustUser1 = projectEntrustUserService.getBaseMapper().selectOne(queryWrapper);
            if(BeanUtil.isEmpty(entrustUser1)){
                ProjectEntrustUser entrustUser = new ProjectEntrustUser();
                entrustUser.setId(projectEntrustUser.getId());
                entrustUser.setUserId(req.getUserId());
                b = projectEntrustUserService.updateById(entrustUser);
            } else {
                b = true;
            }
            // 修改招标人结束 修改人员项目权限
            if(b){
                LambdaQueryWrapper<ProjectTendererPower> ptp = new LambdaQueryWrapper<>();
                ptp.eq(ProjectTendererPower::getProjectId, req.getProjectId());
                ptp.eq(ProjectTendererPower::getUserId,userId);
                ProjectTendererPower projectTendererPower = projectTendererPowerService.getBaseMapper().selectOne(ptp);
                if(null != projectTendererPower){
                    projectTendererPowerService.remove(ptp);
                    LambdaQueryWrapper<ProjectTendererPower> queryWrapper1 = new LambdaQueryWrapper<>();
                    queryWrapper1.eq(ProjectTendererPower::getProjectId, req.getProjectId());
                    queryWrapper1.eq(ProjectTendererPower::getUserId,req.getUserId());
                    ProjectTendererPower projectTendererPower1 = projectTendererPowerService.getBaseMapper().selectOne(queryWrapper1);
                    ProjectTendererPower  nptp = new ProjectTendererPower();
                    BeanUtils.copyProperties(projectTendererPower,nptp);
                    if(BeanUtil.isNotEmpty(projectTendererPower1)){
                        nptp.setId(projectTendererPower1.getId());
                    }
                    nptp.setUserId(req.getUserId());
                    nptp.setUserPhone(userIdentity.getUserPhone());
                    nptp.setUserName(user.getUserName());
                    projectTendererPowerService.saveOrUpdate(nptp);
                }

            }
        }
        if(b){
            return Result.ok();
        }else{
            return Result.failed();
        }
    }


    /**
     * 根据项目id 查询项目信息
     *
     * @param id
     */
    @Override
    public Result<AgentProjectDTO> getProjectEntrust(Long id) {
        AgentProjectDTO dto = new AgentProjectDTO();
        // 查询招标人角色信息
        LambdaQueryWrapper<ProjectTendererPower> ptp = new LambdaQueryWrapper<>();
        ptp.eq(ProjectTendererPower::getProjectId, id);
        List<ProjectTendererPower> powers = projectTendererPowerService.list(ptp);
//        List<ProjectTendererPower> collect = powers.stream().filter(o -> o.getUserId().equals(SecurityUtils.getJwtUser().getUserId())).collect(Collectors.toList());
//        if(null != collect && collect.size() > 0){
//            dto.setRoleType(collect.get(0).getRoleType());
//        }else{
//            //招标人没有查看项目权限提示
//            return Result.failed(ExceptionEnum.ENTRUST_NO_PERMISSION.getCode(),ExceptionEnum.ENTRUST_NO_PERMISSION.getMessage());
//        }
        dto.setTendererPowers(powers);


        // 查询招标人角色信息
        //获取项目信息
        Project project = this.getById(id);
        BeanListUtil.copyProperties(project, dto);
        //查询所有关系数据
        AgentInItDto projectUser = projectEntrustUserService.getEntrustUserByProjectIdAndType(id, EntrustUserType.ENTRUSTED_PERSON.getType());
        dto.setPagentUserDto(projectUser);
        //获取委托项目信息
        LambdaQueryWrapper<ProjectEntrPersNum> projectNum = new LambdaQueryWrapper<ProjectEntrPersNum>();
        projectNum.eq(ProjectEntrPersNum::getProjectId, id);
        List<ProjectEntrPersNum> list = projectEntrPersNumService.list(projectNum);
        dto.setProjectEntrPersNumDTO(BeanListUtil.convertList(list, ProjectEntrPersNumDTO.class));
        // 委托受理状态
        LambdaQueryWrapper<ProjectAgentApproval> lqwpaa = new LambdaQueryWrapper<>();
        lqwpaa.eq(ProjectAgentApproval::getProjectId,id);
        ProjectAgentApproval approval = projectAgentApprovalService.getBaseMapper().selectOne(lqwpaa);
        if(null != approval){
            dto.setAgentStatus(approval.getStatus());
        }
        return Result.ok(dto);
    }

    /**
     * 新增项目
     *
     * @param agentProject 参数
     * @return
     */
    private Boolean addProject(BidderProjectReq agentProject, Project project) {
        //执行项目入库
        BeanUtil.copyProperties(agentProject, project);
        project.setId(agentProject.getId());
        LambdaQueryWrapper<ProjectAgentApproval> lqw = new LambdaQueryWrapper();
        lqw.eq(ProjectAgentApproval::getProjectId,agentProject.getId());
        ProjectAgentApproval one = projectAgentApprovalService.getOne(lqw);
        List<Integer> status = new ArrayList<>();
        status.add(ProjectTendereeStatusEnum.XMJL_ACCEPTED.getCode());
        status.add(ProjectTendereeStatusEnum.XMJL_PERFECT.getCode());
        if((null != one && !status.contains(one.getStatus())) || BeanUtil.isEmpty(one)){
            project.setStatus(ProjectStatusEnum.WAIT_ACCEPT.getValue());
        }
        project.setProjectSource(ProjectSourceEnum.TENDEREE.getType());
        project.setVersion(null);
        if(null == agentProject.getId()){
            Company company = companyService.getBaseMapper().selectById(SecurityUtils.getJwtUser().getCompanyId());
            User user = userService.getBaseMapper().selectById(SecurityUtils.getJwtUser().getUserId());
            project.setPrincipalChargeName(SecurityUtils.getJwtUser().getUserName());
            project.setPrincipalCompany(company.getCompanyName());
            project.setPrincipalChargePhone(user.getUserPhone());
        }
        return this.saveOrUpdate(project);
    }

    /**
     * 存储用户新增时增加的代理机构信息
     *
     * @param agentProject
     */
    private Boolean lastAgent(BidderProjectReq agentProject, Long userId) {
        if(null != agentProject.getId()){
         LambdaQueryWrapper<ProjectAgentApproval> lqw = new LambdaQueryWrapper();
            lqw.eq(ProjectAgentApproval::getProjectId,agentProject.getId());
            projectAgentApprovalService.remove(lqw);
        }
        ProjectAgentApproval record = BeanListUtil.convert(agentProject, ProjectAgentApproval.class);
        /**
         * 选择到人那么项目经理直接选择
         * 选择到部门 那么部门经理审核
         * 选择
         */
        if(null != agentProject.getAgentChargeId()){
            record.setStatus(ProjectTendereeStatusEnum.XMJL.getCode());
            record.setIsConfirm(YesOrNoEnum.YES.getType());
        }else{
            record.setStatus(ProjectTendereeStatusEnum.YGC.getCode());
        }
        boolean save = projectAgentApprovalService.save(record);


        if(save){
            //  写入审批日志
            CalibrationProcessRecord processRecord = new CalibrationProcessRecord();
            processRecord.setBusinessCode("PROJECT_AGENT");
            processRecord.setBusinessId(agentProject.getProjectId());
            processRecord.setOperation(ApprovalStatusEnum.COMMIT.getName());
            processRecord.setOperatorId(SecurityUtils.getJwtUser().getUserId());
            processRecord.setOperatorName(SecurityUtils.getJwtUser().getUserName());
            if(null != userId){
                processRecord.setNextUserName(agentProject.getAgentChargeName());
            }
            processRecordService.save(processRecord);
        }
        return save;
    }


    /**
     * 存储用户新增时增加的代理机构信息
     *
     * @param agentProject
     */
    private Boolean lastAgentRecord(BidderProjectReq agentProject, Long userId) {
        UserAgentRecord record = BeanListUtil.convert(agentProject, UserAgentRecord.class);
        record.setUserId(userId);
        return userAgentRecordService.save(record);
    }


    /**
     * D-006项目退回审核
     *
     * @param projectId
     * @param reason
     */
    @Override
    public Boolean updateProjectAgencyNoPass(Long projectId, String reason) {
        Boolean flag = false;
        LambdaQueryWrapper<ProjectAgentApproval> lqw = new LambdaQueryWrapper<>();
        lqw.eq(ProjectAgentApproval::getProjectId,projectId);
        ProjectAgentApproval approval = projectAgentApprovalService.getBaseMapper().selectOne(lqw);
        Integer status = null;
        // 是否退回项目到招标人标记
        Boolean returnFlag = false;
        // 判断项目是否需要修改
        if(null != approval){
            if(ProjectTendereeStatusEnum.XMJL.getCode().equals(approval.getStatus())) {
                if(YesOrNoEnum.YES.getType().equals(approval.getIsConfirm())){
                    returnFlag = true;
                }
                status = ProjectTendereeStatusEnum.XMJL_RETURN.getCode();
            }else if(ProjectTendereeStatusEnum.CZ.getCode().equals(approval.getStatus())){
                status = ProjectTendereeStatusEnum.CZ_RETURN.getCode();
            }else if(ProjectTendereeStatusEnum.YGC.getCode().equals(approval.getStatus()) || ProjectTendereeStatusEnum.CZ_RETURN.getCode().equals(approval.getStatus())){
                status = ProjectTendereeStatusEnum.YGC_RETURN.getCode();
                returnFlag = true;
            }else if(ProjectTendereeStatusEnum.XMJL_RETURN.getCode().equals(approval.getStatus())){
                if(YesOrNoEnum.YES.equals(approval.getIsSkipDepartment())){
                    status = ProjectTendereeStatusEnum.YGC_RETURN.getCode();
                }else{
                    status = ProjectTendereeStatusEnum.CZ_RETURN.getCode();
                }
            }
            ProjectAgentApproval yar = new ProjectAgentApproval();
            yar.setStatus(status);
            yar.setId(approval.getId());
            flag = projectAgentApprovalService.updateById(yar);
            //  如果直接退回给招标人 那么需要修改项目状态为退回
            if(flag){
                //  写入审批日志
                CalibrationProcessRecord processRecord = new CalibrationProcessRecord();
                processRecord.setBusinessCode("PROJECT_AGENT");
                processRecord.setBusinessId(projectId);
                processRecord.setOperation(ApprovalStatusEnum.DRAWBACK.getName());
                processRecord.setOperatorId(SecurityUtils.getJwtUser().getUserId());
                processRecord.setOperatorName(SecurityUtils.getJwtUser().getUserName());
                processRecord.setRemark(reason);
                processRecordService.save(processRecord);
            }

            if(flag && returnFlag){
              Project p = new Project();
              p.setId(projectId);
              p.setStatus(ProjectStatusEnum.REFUSED.getValue());
              p.setProjectStatus(ProjectStatusEnum.REFUSED.getName());
              p.setRefuseReason(reason);
              projectService.updateProject(p);
            }
        }
       return  flag;
    }

    @Override
    public Boolean updateProjectAgencyPass(ProjectAgentApproval record) {
        Boolean flag = false;
        ProjectAgentApproval yar = new ProjectAgentApproval();
        LambdaQueryWrapper<ProjectAgentApproval> lqw = new LambdaQueryWrapper<>();
        lqw.eq(ProjectAgentApproval::getProjectId,record.getProjectId());
        ProjectAgentApproval approval = projectAgentApprovalService.getBaseMapper().selectOne(lqw);
        Integer status = null;
        if(null != approval){
         if(ProjectTendereeStatusEnum.XMJL.getCode().equals(approval.getStatus())){
             status = ProjectTendereeStatusEnum.XMJL_ACCEPTED.getCode();
         }else if(ProjectTendereeStatusEnum.YGC.getCode().equals(approval.getStatus())
                 || ProjectTendereeStatusEnum.CZ_RETURN.getCode().equals(approval.getStatus())){ // 运管处确认
           // 判断运管处是否直接选中了项目经理
             if(null != record.getAgentChargeId()){
                 status = ProjectTendereeStatusEnum.XMJL.getCode();
                 yar.setIsSkipDepartment(YesOrNoEnum.YES.getType());
                 yar.setAgentDepartmentId(record.getAgentDepartmentId());
                 yar.setAgentChargeId(record.getAgentChargeId());
                 yar.setAgentChargePhone(record.getAgentChargePhone());
                 yar.setAgentChargeName(record.getAgentChargeName());
             }else{
                 status = ProjectTendereeStatusEnum.CZ.getCode();
                 yar.setAgentDepartmentId(record.getAgentDepartmentId());
             }
            }else if(ProjectTendereeStatusEnum.CZ.getCode().equals(approval.getStatus())
                 || (ProjectTendereeStatusEnum.XMJL_RETURN.getCode().equals(approval.getStatus()) &&  YesOrNoEnum.NO.getType().equals(approval.getIsConfirm())&&  YesOrNoEnum.NO.getType().equals(approval.getIsSkipDepartment()))) { // 处长确认
                 yar.setAgentChargeId(record.getAgentChargeId());
                 yar.setAgentChargePhone(record.getAgentChargePhone());
                 yar.setAgentChargeName(record.getAgentChargeName());
                 if(SecurityUtils.getJwtUser().getUserId().equals(record.getAgentChargeId())){
                     status = ProjectTendereeStatusEnum.XMJL_ACCEPTED.getCode();
                 }else{
                     status = ProjectTendereeStatusEnum.XMJL.getCode();
                 }
            }
         }
            yar.setStatus(status);
            yar.setId(approval.getId());
            flag = projectAgentApprovalService.updateById(yar);
            if(flag){
                //  写入审批日志
                CalibrationProcessRecord processRecord = new CalibrationProcessRecord();
                processRecord.setBusinessCode("PROJECT_AGENT");
                processRecord.setBusinessId(record.getProjectId());
                processRecord.setOperation(ApprovalStatusEnum.AGREE.getName());
                processRecord.setOperatorId(SecurityUtils.getJwtUser().getUserId());
                processRecord.setOperatorName(SecurityUtils.getJwtUser().getUserName());
                if(null != record.getAgentChargeId()){
                    processRecord.setNextUserName(record.getAgentChargeName());
                }
                processRecordService.save(processRecord);
            }
            // todo 项目写入代理机构
            if(flag && ProjectTendereeStatusEnum.XMJL_ACCEPTED.getCode().equals(status)){
//                Project p = new Project();
//                p.setId(record.getProjectId());
//                p.setStatus(ProjectStatusEnum.GOING.getValue());
//                p.setProjectStatus(ProjectStatusEnum.GOING.getName());
//                projectService.updateProject(p);
                // 新增代理人信息
                LambdaQueryWrapper<ProjectEntrustUser> lqweee = new LambdaQueryWrapper<>();
                lqweee.eq(ProjectEntrustUser::getProjectId,approval.getProjectId());
                lqweee.eq(ProjectEntrustUser::getType,EntrustUserType.ENTRUSTED_PERSON.getType());
                ProjectEntrustUser projectEntrustUser = projectEntrustUserService.getBaseMapper().selectOne(lqweee);
                ProjectEntrustUser reuler = new ProjectEntrustUser();
                reuler.setUserId(SecurityUtils.getJwtUser().getUserId());
                reuler.setId(projectEntrustUser.getId());
                reuler.setSource(ProjectSourceEnum.TENDEREE.getType());
                reuler.setDepartmentId(SecurityUtils.getJwtUser().getUser().getDepartId());
                projectEntrustUserService.updateById(reuler);
            }
            return flag;
        }
    @Override
    public IPage<ProjectAcceptanceListVo> selectYgcProjectList(ProjectAcceptanceRep projectAcceptanceRep) {
        return this.getBaseMapper().selectYgcProjectList( projectAcceptanceRep.customizeBuildPage(),projectAcceptanceRep);
    }

    @Override
    public IPage<ProjectAcceptanceListVo> selectAcceptanceProjectList(ProjectAcceptanceRep projectAcceptanceRep) {
        //如果租户是招标人，添加部门条件
        if (CompanyEnum.BIDDER.getType().equals(organizationType)) {
            projectAcceptanceRep.setDepartId(SecurityUtils.getJwtUser().getUser().getDepartId());
        }
        projectAcceptanceRep.setUserId(SecurityUtils.getJwtUser().getUserId());
        projectAcceptanceRep.setRoleCode(RoleCodeEnum.CHUZHANG.getType());
        return this.getBaseMapper().selectAcceptanceProjectList(projectAcceptanceRep.customizeBuildPage(),projectAcceptanceRep);
    }


    /**
     * 执行委托项目入库
     *
     * @param agentProject
     * @param project
     */
    private Boolean bidderProject(BidderProjectReq agentProject, Project project) {
        List<ProjectEntrPersNum> list = null;
        //去除上一次委托编号
        LambdaUpdateWrapper<ProjectEntrPersNum> lambdaUpdateWrapper = new LambdaUpdateWrapper<ProjectEntrPersNum>();
        lambdaUpdateWrapper.eq(ProjectEntrPersNum::getProjectId, project.getId());
        projectEntrPersNumService.remove(lambdaUpdateWrapper);
        List<EntrustProjectReq> projectEntrPeersNums = agentProject.getEntrustProjectNumList();
        list = BeanListUtil.convertList(projectEntrPeersNums, ProjectEntrPersNum.class);
        //委托编号不存在时new个委托编号对象
        if (CollectionUtils.isEmpty(list)) {
            list = new ArrayList<ProjectEntrPersNum>();
            list.add(new ProjectEntrPersNum());
        }

        list.forEach(p -> p.setProjectId(project.getId()));
        //后期修改，委托项目编号无，随机
//        String numberCode = StrUtil.toString(System.currentTimeMillis()).substring(UserConstants.PHONE_AFTER);
//        list.stream().forEach(p -> p.setProjectNumber(StringUtils.isBlank(p.getProjectNumber()) ? "JJ123" + numberCode : p.getProjectNumber()));
        return projectEntrPersNumService.saveBatch(list);
    }

    /**
     * 保存项目委托关系
     *
     * @param user
     * @param req
     */
    private Boolean saveProjectEntrusetUser(BidderProjectReq req, JwtUser user) {
        List<ProjectTendererPower> powerList = req.getPowerList();
        List<ProjectEntrustUser> lt = new ArrayList<ProjectEntrustUser>();
        for (ProjectTendererPower projectTendererPower : powerList) {
            ProjectEntrustUser euler = new ProjectEntrustUser();
            euler.setUserId(projectTendererPower.getUserId());
            euler.setCompanyId(user.getCompanyId());
            euler.setSource(ProjectSourceEnum.TENDEREE.getType());
            euler.setType(EntrustUserType.PRINCIPAL.getType());
            euler.setProjectId(req.getProjectId());
            lt.add(euler);
        }

        ProjectEntrustUser reuler = new ProjectEntrustUser();
        reuler.setUserId(req.getAgentChargeId());
        reuler.setCompanyId(req.getAgentId());
        reuler.setSource(ProjectSourceEnum.TENDEREE.getType());
        reuler.setType(EntrustUserType.ENTRUSTED_PERSON.getType());
        reuler.setProjectId(req.getProjectId());
        lt.add(reuler);

        LambdaUpdateWrapper<ProjectEntrustUser> up = new LambdaUpdateWrapper<ProjectEntrustUser>();
        up.eq(ProjectEntrustUser::getProjectId, req.getProjectId());
        projectEntrustUserService.remove(up);
        //新增被委托人信息
        return projectEntrustUserService.saveOrUpdateBatch(lt);
    }

    /**
     *
     * @param req
     * @param jwtUser
     * @return
     */
    @Override
    public IPage<ProjectListDTO> queryMyselfProjectByPage(MyselfProjectConditionReq req, JwtUser jwtUser) {
        req.setUserId(jwtUser.getUserId());
        req.setUserDepartId(jwtUser.getUser().getDepartId());
        //处理数据权限
        String dataScopeSql= DataScopeUtil.getProjectDataScopeFilterDepartSql("tpe","tpe","p");
        req.setDataScope(dataScopeSql);
        //自采项目展示给采购经理，即被委托人
        req.setType(EntrustUserType.ENTRUSTED_PERSON.getType());
        IPage<ProjectListDTO> page=this.baseMapper.queryMyselfProjectByPage(req.customizeBuildPage(), req);
        List<ProjectListDTO> records = new ArrayList<>();
        for (ProjectListDTO item : page.getRecords()) {
            QueryWrapper<ProjectBidSection> queryWrapper=new QueryWrapper<>();
            queryWrapper.lambda()
                    .eq(ProjectBidSection::getProjectId,item.getId())
                    .orderByAsc(ProjectBidSection::getPackageNumber)
                    .eq(ProjectBidSection::getIsDelete, CommonConstants.NO);
            //标段状态拼接
            List<ProjectBidSection> list = projectBidSectionService.list(queryWrapper);
            List<String> strBidRound= list.stream().map(p->p.getBidRound()+"").collect(Collectors.toList());
            item.setPackagesBidRound(strBidRound);
            List<String> strBidRondOne=list.stream().filter(p->p.getBidRound().equals(1)).map(p->p.getStatus()).collect(Collectors.toList());
            item.setPackagesBidRoundOne(strBidRondOne);
            List<String> strBidRondTwo=list.stream().filter(p->p.getBidRound().equals(2)).map(p->p.getStatus()).collect(Collectors.toList());
            item.setPackagesBidRoundTwo(strBidRondTwo);
            //递交截止时间拼接
            List<String> submitEndTime = new ArrayList<>();
            for (ProjectBidSection section : list) {
                if (null != section.getSubmitEndTime()) {
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    submitEndTime.add(sdf.format(section.getSubmitEndTime()));
                } else {
                    submitEndTime.add("-");
                }
            }
            item.setSubmitEndTime(submitEndTime);
            //统计已报名待审核供应商数量
            Integer applyInfoNum = projectMapper.countApplyInfoByProjectId(item.getId());
            item.setApplyInfoNum(applyInfoNum);
            records.add(item);
        }
        page.setRecords(records);
        return page;
    }

    /**
     * 新建自采项目
     * @param req
     * @param jwtUser
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean addMyselfProject(MyselfProjectReq req, JwtUser jwtUser) {
        Project project = new Project();
        //保存项目信息
        BeanUtil.copyProperties(req, project);
        if (StringUtils.isNotEmpty(project.getPurchaseName())) {
            project.setProjectName(project.getPurchaseName());
        }
        //自采项目
        project.setProcurementMethod(ProcurementMethodEnum.PROJECT.getType());
        //项目编号
        String systemAbbreviation = null;
        LambdaQueryWrapper<SystemSetting> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.orderByDesc(SystemSetting::getId).last("limit 1");
        SystemSetting systemSetting = systemSettingService.getOne(lambdaQueryWrapper);
        if (null != systemSetting && StringUtils.isNotEmpty(systemSetting.getSystemAbbreviation())) {
            systemAbbreviation = systemSetting.getSystemAbbreviation();
        }
        String purchaseNumber = projectSerialNumService.generateProjectNumMyPurchase(systemAbbreviation);
        project.setPurchaseNumber(purchaseNumber);
        project.setProjectNumber(purchaseNumber);
        //自采项目创建默认状态项目建档
        project.setStatus(ProjectStatusEnum.GOING.getValue());
        boolean flag = this.save(project);
        if (flag) {
            //保存委托人关系数据
            req.setProjectId(project.getId());
            flag = saveProjectEntrustUser(req, jwtUser);
        }
        if (flag) {
            //执行代理信息入库操作(被委托人，即采购经理)
            flag = saveAgentRecord(req, jwtUser);
        }
        commonSectionService.saveOrUpdateProjectPackageFieldDealWith(project.getId());
        return flag;
    }

    /**
     * 保存代理信息入库操作(被委托人，即采购经理)
     * @param req
     * @param jwtUser
     * @return
     */
    private boolean saveAgentRecord(MyselfProjectReq req, JwtUser jwtUser) {
        UserAgentRecord record = BeanListUtil.convert(req, UserAgentRecord.class);
        record.setAgentDepartId(req.getDepartmentId());
        record.setUserId(jwtUser.getUserId());
        record.setUserIdentity(CompanyEnum.BIDDER.getType());
        return userAgentRecordService.save(record);
    }

    /**
     * 招标人重新招标查询项目详情
     * @param req
     * @return
     */
    @Override
    public MyselfProjectVo queryRePurchaseProject(BidRePurchaseReq req) {
        MyselfProjectVo myselfProjectVo = new MyselfProjectVo();
        LambdaQueryWrapper<Project> projectLambdaQueryWrapper = new LambdaQueryWrapper<>();
        projectLambdaQueryWrapper.orderByDesc(Project::getCreatedTime);
        projectLambdaQueryWrapper.eq(Project::getPurchaseNumber,req.getPurchaseNumber());
        //获取项目信息
        List<Project> projectList = projectMapper.selectList(projectLambdaQueryWrapper);
        // 如果项目不存在抛出异常
        if (RequestUtil.isEmpty(projectList) || projectList.size() == 0) {
            throw new SunFlowerException(ExceptionEnum.PROJECT_NOT_FOUND, ExceptionEnum.PROJECT_NOT_FOUND.getMessage());
        }
        BeanListUtil.copyProperties(projectList.get(0), myselfProjectVo);
        //项目被委托人信息（采购经理）
        LambdaQueryWrapper<ProjectEntrustUser> projectEntrustUserLambdaQueryWrapper = new LambdaQueryWrapper<>();
        projectEntrustUserLambdaQueryWrapper.eq(ProjectEntrustUser::getProjectId, projectList.get(0).getId());
        projectEntrustUserLambdaQueryWrapper.eq(ProjectEntrustUser::getType, EntrustUserType.ENTRUSTED_PERSON.getType());
        projectEntrustUserLambdaQueryWrapper.last("limit 1");
        ProjectEntrustUser projectEntrustUserServiceOne = projectEntrustUserService.getOne(projectEntrustUserLambdaQueryWrapper);
        myselfProjectVo.setProjectEntrustUser(projectEntrustUserServiceOne);
        //查询可重新招标的标段数据集合
        List<ReSectionVo> reSectionVoList = this.baseMapper.queryReSectionList(req);
        if (reSectionVoList.size() == 0) {
            myselfProjectVo.setReSectionVoList(new ArrayList<ReSectionVo>());
        } else {
            Map<Long, ReSectionVo> filterNotice = reSectionVoList.parallelStream().collect(Collectors.toMap
                    (ReSectionVo::getSectionId, Function.identity(),
                            (c1, c2) -> c1.getNoticeId() > c2.getNoticeId() ? c1 : c2));
            reSectionVoList = filterNotice.values().stream().collect(Collectors.toList());
            //可重新招标标段信息
            myselfProjectVo.setReSectionVoList(reSectionVoList);
        }
        return myselfProjectVo;
    }

    /**
     * 自采项目重新组织采购
     * @param req
     * @param jwtUser
     * @return
     */
    @Override
    public Boolean addReProjectMyself(MyselfReProjectReq req, JwtUser jwtUser) {
        boolean flag = false;

        int index = 0;
        long reProjectId = 0;
        long baseProjectId = 0;

        for(ReProjectSection reProjectSection : req.getProSectionList()){

            reProjectSection.setBidList(reProjectSection.getBidList().stream().distinct().collect(Collectors.toList()));
            baseProjectId = reProjectSection.getProjectId();

            // 获取项目信息
            Project reProject = projectMapper.selectById(reProjectSection.getProjectId());
            // 执行重新招标项目入库
            flag = saveReProject(req,reProjectSection,reProject,index, jwtUser.getUser().getDepartId());
            reProjectId = reProject.getId();
            //委托信息直接带过来 不需要以前的保存委托关系操作
            if (flag&&index==0) {
                req.setProjectId(reProjectId);
                LambdaQueryWrapper<RUserDepartment> userDepartmentRelationLambdaQueryWrapper = new LambdaQueryWrapper<>();
                userDepartmentRelationLambdaQueryWrapper.eq(RUserDepartment::getUserId,req.getAgentChargeId());
                userDepartmentRelationLambdaQueryWrapper.last("limit 1");
                RUserDepartment one = rUserDepartmentService.getOne(userDepartmentRelationLambdaQueryWrapper);
                if (one != null) {
                    req.setDepartmentId(one.getDepartmentId());
                }
                //保存委托人关系数据
                saveProjectEntrustUser(req, jwtUser);

                //执行更新项目表冗余字段
                commonSectionService.saveOrUpdateProjectPackageFieldDealWith(baseProjectId);
            }
            index++;
        }
        // 查询重新招标项目id 发送消息到公函更新公函中项目状态
        LambdaQueryWrapper<Project> projectQuery = new LambdaQueryWrapper<>();
        projectQuery.eq(Project::getBaseProjectId, req.getProjectId());
        List<Project> projects = projectMapper.selectList(projectQuery);
        for (Project project : projects) {
            rabbitMqService.sendMq(project.getId());
        }

        //推送项目到ncc
        if(projects != null && projects.size()>0){
            //推送项目到ncc
            projectService.pushProjectNcc(projects.get(0).getId(),null);
        }
        return flag;
    }

    /**
     * 修改自采项目信息
     * @param req
     * @param jwtUser
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateMyselfProject(BidUpdateProjectReq req, JwtUser jwtUser) {
        //查询要修改的项目
        Project project = projectMapper.selectById(req.getProjectId());
        //项目字段修改
        project.setOperationFlow(req.getOperationFlow());
        project.setIsProcess(req.getIsProcess());
        if (req.getIsProcess().equals(CommonConstants.YES)) {
            project.setProcessAddress(req.getProcessAddress());
            project.setProcessCode(req.getProcessCode());
        }
        project.setEntrustedAmount(req.getEntrustedAmount());
        project.setEntrustedCurrency(req.getEntrustedCurrency());
        if (StringUtils.isNotEmpty(req.getProjectApprovalFileName())) {
            project.setProjectApprovalFileName(req.getProjectApprovalFileName());
        }
        if (StringUtils.isNotEmpty(req.getProjectApprovalNumber())) {
            project.setProjectApprovalNumber(req.getProjectApprovalNumber());
        }
        if (StringUtils.isNotEmpty(req.getProjectApprovalUnit())) {
            project.setProjectApprovalUnit(req.getProjectApprovalUnit());
        }
        project.setPurchaseName(req.getPurchaseName());
        project.setPurchaseNumber(req.getPurchaseNumber());
        project.setIsInternational(req.getIsInternational());
        //国际标编号
        if(req.getIsInternational().equals(CommonConstants.YES)){
            project.setInternationalNumber(req.getInternationalNumber());
        } else {
            project.setInternationalNumber("");
            project.setTempInternational(0);
        }
        if (OperationTypeEnum.SUBMIT.getType().equals(req.getOperationType())) {
            project.setStatus(ProjectStatusEnum.FINISH.getValue());
            commonSectionService.saveOrUpdateProjectPackageFieldDealWith(project.getId());
        }
        projectMapper.updateById(project);
        // 无包项目同步修改标段委托金额 add by fanqh on 2022-05-31
        List<ProjectBidSectionDTO> list = projectBidSectionService.getProjectBidSectionByProjectId(project.getId());
        if (SegmentEnum.NO_BAG.getType().equals(project.getPackageSegmentStatus()) && project.getEntrustedAmount() != null
                && list.size() == 1) {
            projectBidSectionService.update(new UpdateWrapper<ProjectBidSection>().set("entrust_money", project.getEntrustedAmount())
                    .set("entrust_currency", project.getEntrustedCurrency())
                    .eq("id", list.get(0).getId()));
        }
        return true;
    }

    /**
     * 当前租户信息联想功能
     * @param req
     * @return
     */
    @Override
    public List<LenovoTenantUserVo> queryTenantInfo(LenovoTenantUserReq req) {
        List<LenovoTenantUserVo> tenantUserVoList = new ArrayList<>();
        //获取当前租户公司统一社会信用代码
        req.setOrganizationNum(organizationNum);
        req.setOrganizationType(organizationType);
        //公司搜索
        if (req.getType() == 1) {
            tenantUserVoList = this.baseMapper.queryTenantInfoByCompany(req);
        }
        //部门搜索，公司id不为空
        if (req.getType() == 2 && req.getCompanyId() != null) {
            tenantUserVoList = this.baseMapper.queryTenantInfoByDepartment(req);
        }
        //人员搜索，公司id和部门id不为空
        if (req.getType() == 3 && req.getCompanyId() != null && req.getDepartmentId() != null) {
            tenantUserVoList = this.baseMapper.queryTenantInfoByUser(req);
        }
        return tenantUserVoList;
    }

    /**
     * 根据标段ID查询标段信息
     * @param req
     * @return
     */
    @Override
    public ProjectBidSectionVO querySection(ProjectSectionIdReq req) {
        ProjectBidSection projectBidSection = projectBidSectionService.getById(req.getSectionId());
        //查询项目里的所在地信息 默认带入
        Project project = projectService.getProjectById(projectBidSection.getProjectId());
        List<SectionExpertSpecialty> list = iSectionExpertSpecialtyService.list(new LambdaQueryWrapper<SectionExpertSpecialty>()
                .eq(SectionExpertSpecialty::getSectionId, req.getSectionId()));
        List<Long> spids = list.stream()
                .map(m -> {
                    return m.getExpertSpecialtyId();
                }).collect(Collectors.toList());
        // 判断标段是否为重新招标，若标的物备份失败则读取原标段数据
        if (null != projectBidSection.getBaseSectionId() && list.size() == 0) {
            List<SectionExpertSpecialty> baseList = iSectionExpertSpecialtyService.list(new LambdaQueryWrapper<SectionExpertSpecialty>()
                    .eq(SectionExpertSpecialty::getSectionId, projectBidSection.getBaseSectionId()));
            spids = baseList.stream()
                    .map(m -> {
                        return m.getExpertSpecialtyId();
                    }).collect(Collectors.toList());
        }
        projectBidSection.setSectionExpertSpecialtyList(spids);
        ProjectBidSectionVO projectBidSectionVO = BeanListUtil.convert(projectBidSection, ProjectBidSectionVO.class);
        projectBidSectionVO.setSectionExpertSpecialtyList(spids);
        projectBidSectionVO.setAddressProvince(project.getAddressProvince());
        projectBidSectionVO.setAddressCity(project.getAddressCity());
        return projectBidSectionVO;
    }

    /**
     * 根据项目ID查询信息
     * @param req
     * @return
     */
    @Override
    public Project queryProject(ProjectSectionIdReq req) {
        return projectMapper.selectById(req.getProjectId());
    }

    /**
     * 查询上一次填写代理信息（采购经理）
     * @param jwtUser
     * @return
     */
    @Override
    public UserAgentRecordDTO selectLastAgent(JwtUser jwtUser) {
        return userAgentRecordService.getByUserId(CompanyEnum.BIDDER.getType(),jwtUser.getUserId());
    }

    /**
     * 保存委托人与被委托人数据
     * @param req
     * @param jwtUser
     * @return
     */
    private Boolean saveProjectEntrustUser(MyselfProjectReq req, JwtUser jwtUser) {
        List<ProjectEntrustUser> projectEntrustUserList = new ArrayList<ProjectEntrustUser>();
        //当前登录用户是委托人
        ProjectEntrustUser euler = new ProjectEntrustUser();
        euler.setUserId(jwtUser.getUserId());
        euler.setCompanyId(jwtUser.getCompanyId());
        euler.setDepartmentId(jwtUser.getUser().getDepartId());
        euler.setType(EntrustUserType.PRINCIPAL.getType());
        euler.setProjectId(req.getProjectId());
        projectEntrustUserList.add(euler);
        //选择的采购经理是被委托人
        ProjectEntrustUser reuler = new ProjectEntrustUser();
        reuler.setUserId(req.getAgentChargeId());
        reuler.setCompanyId(req.getAgentId());
        reuler.setDepartmentId(req.getDepartmentId());
        reuler.setType(EntrustUserType.ENTRUSTED_PERSON.getType());
        reuler.setProjectId(req.getProjectId());
        projectEntrustUserList.add(reuler);
        //删除该项目上一次保存的委托人与被委托人数据
        LambdaUpdateWrapper<ProjectEntrustUser> up = new LambdaUpdateWrapper<ProjectEntrustUser>();
        up.eq(ProjectEntrustUser::getProjectId, req.getProjectId());
        projectEntrustUserService.remove(up);
        //新增被委托人信息
        return projectEntrustUserService.saveOrUpdateBatch(projectEntrustUserList);
    }

    /**
     * 重新招标项目保存
     * @param req
     * @param reProjectSection
     * @param reProject
     * @param index
     * @param deptId
     * @return
     */
    private boolean saveReProject(MyselfReProjectReq req, ReProjectSection reProjectSection, Project reProject, int index, Long deptId) {
        // 原项目 要变成已重新招标状态
        reProject.setReTender(CommonConstants.YES);
        reProject.setStatus(PackageStatusEnum.ARCHIVING.getValue());
        projectService.updateProject(reProject);

        //使用原采购编号zyytwyzmfzlqcdhzrmfdqx
        if (req.getIsFormerPurchaseNumber().equals(CommonConstants.NO)) {
            reProject.setPurchaseNumber(req.getFormerPurchaseNumber());
            reProject.setIsFormerPurchaseNumber(CommonConstants.NO);
        } else {
            //项目编号
            String systemAbbreviation = null;
            LambdaQueryWrapper<SystemSetting> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.orderByDesc(SystemSetting::getId).last("limit 1");
            SystemSetting systemSetting = systemSettingService.getOne(lambdaQueryWrapper);
            if (null != systemSetting && StringUtils.isNotEmpty(systemSetting.getSystemAbbreviation())) {
                systemAbbreviation = systemSetting.getSystemAbbreviation();
            }
            String purchaseNumber = projectSerialNumService.generateProjectNumMyPurchase(systemAbbreviation);
            reProject.setPurchaseNumber(purchaseNumber);
            reProject.setIsFormerPurchaseNumber(CommonConstants.YES);
        }
        //原项目采购编号
        reProject.setFormerPurchaseNumber(req.getFormerPurchaseNumber());
        reProject.setStatus(ProjectStatusEnum.FINISH.getValue());
        //自采项目
        reProject.setProcurementMethod(ProcurementMethodEnum.PROJECT.getType());
        //第一次重新招标
        if (reProject.getBaseProjectId() == null) {
            reProject.setBaseProjectId(reProject.getId());
        }
        if (index == 0) {
            reProject.setReTender(CommonConstants.NO);
            String projectStatus = "";
            String projectStatusCode = "";
            for (int i = 0; i < reProjectSection.getBidList().size(); i++) {
                projectStatus += PackageStatusEnum.TENDER_INVITATION.getName();
                projectStatusCode += PackageStatusEnum.TENDER_INVITATION.getValue().toString();
                if (i != reProjectSection.getBidList().size() - 1) {
                    projectStatus += ",";
                    projectStatusCode += ",";
                }
            }
            reProject.setProjectStatus(projectStatus);
            reProject.setProjectStatusCode(projectStatusCode);
            //重新招标项目异常状态恢复
            reProject.setAbnormalStatus(AbnormalStatusEnum.NORMAL.getType());
            reProject.setId(null);
            this.save(reProject);
        }
        Long reProjectId = reProject.getId();
        List<ProjectBidSection> sections = new ArrayList<>();
        List<ProjectBidSection> reSections = new ArrayList<>();
        for (int i = 0; i < reProjectSection.getBidList().size(); i++) {
            Long sectionId = reProjectSection.getBidList().get(i);
            ProjectBidSection projectBidSectionSel = projectBidSectionService.getById(sectionId);
            //原标段终止
            projectBidSectionSel.setAbnormalStatus(AbnormalStatusEnum.END.getType());
            projectBidSectionSel.setStatus(PackageStatusEnum.EXCEPT_STOP.getValue().toString());
            //本次项目是否被人重新招标 0：否  1：是
            projectBidSectionSel.setIsRebidding(CommonConstants.YES);
            sections.add(projectBidSectionSel);

            //重新招标 只带包号 包名称
            ProjectBidSection projectBidSection = BeanListUtil.convert(projectBidSectionSel,ProjectBidSection.class);
            projectBidSection.setStatus(PackageStatusEnum.TENDER_INVITATION.getValue().toString());
            projectBidSection.setBidSectionCode(BidSectionCodeEnum.bidding_announcement_no.getCode());
            projectBidSection.setPackageName(projectBidSectionSel.getPackageName());
            projectBidSection.setAbnormalStatus(AbnormalStatusEnum.NORMAL.getType());
            projectBidSection.setOldBidderType(1);//上次项目投标人(1.已购买招标文件的投标人 2.已通过关注审核的投标人 3.指定投标人 4.不带入
            projectBidSection.setFormerPurchaseStatus(projectBidSectionSel.getPurchaseStatus());
            projectBidSection.setSaleEndTimeType(null);
            projectBidSection.setSaleEndTime(null);
            projectBidSection.setSubmitEndTimeType(null);
            projectBidSection.setSubmitEndTime(null);
            projectBidSection.setId(null);
            projectBidSection.setFormerAbnormalStatus(null);
            //本次项目是否被人重新招标 0：否  1：是
            projectBidSection.setIsRebidding(CommonConstants.NO);
            //标段轮次初始化 资格预审项目走到第二轮 重新招标也需要从第一轮重新开始
            if(PurchaseStatusEnum.PRE_TRIAL.getType().equals(projectBidSectionSel.getPurchaseStatus())){
                projectBidSection.setBidRound(BidRoundEnum.ZGYS.getType());
            }
            projectBidSection.setSectionGroupId((long) 0);
            projectBidSection.setProjectId(reProjectId);

            if(projectBidSectionSel.getBaseSectionId()==null){
                projectBidSection.setBaseSectionId(sectionId);
                projectBidSection.setBasePackageNumber(projectBidSectionSel.getPackageNumber());
            }
            projectBidSection.setFormerSectionId(sectionId);
            projectBidSection.setFormerPackageNumber(projectBidSectionSel.getPackageNumber());

            //采用新的采购项目编号 新包对应原来包
            if (req.getIsFormerPurchaseNumber().equals(CommonConstants.YES)) {
                if(reProject.getPackageSegmentStatus().equals(SegmentEnum.HAVE_BAG.getType())){
                    //有包状态
                    projectBidSection.setPackageNumber(i+1);
                }
            }else{
                projectBidSection.setPackageNumber(projectBidSectionSel.getPackageNumber());
            }
            projectBidSectionService.save(projectBidSection);
            //查询标的物分类信息 重现招标的时候备份过去
            LambdaQueryWrapper<SectionExpertSpecialty>  specialtyQuery = new LambdaQueryWrapper<>();
            specialtyQuery.eq(SectionExpertSpecialty::getSectionId,sectionId);
            List<SectionExpertSpecialty> list = sectionExpertSpecialtyService.list(specialtyQuery);
            list.forEach(l->{
                l.setSectionId(projectBidSection.getId());
                l.setId(null);
            });
            List<SectionExpertSpecialty> specialties = BeanListUtil.convertList(list,SectionExpertSpecialty.class);
            sectionExpertSpecialtyService.saveBatch(specialties);

            reSections.add(projectBidSection);
        }
        //刷新标段状态
        commonSectionService.updateSectionStatus(sections);
        //重新招标新增
        commonSectionService.updateSectionStatus(reSections);
        return true;
    }

}
