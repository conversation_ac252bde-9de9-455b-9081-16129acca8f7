package com.hzw.sunflower.controller;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.constant.ProjectAnalysisConstants;
import com.hzw.sunflower.controller.request.ProjectAnalysisReq;
import com.hzw.sunflower.entity.*;
import com.hzw.sunflower.exception.ParamsNotNullException;
import com.hzw.sunflower.service.ProjectAnalysisService;
import com.hzw.sunflower.util.ExcelStyleUtil;
import com.hzw.sunflower.util.ExcelUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Api(tags = "项目分析")
@RestController
@RequestMapping("/projectAnalysis")
public class ProjectAnalysisController {

    @Autowired
    ProjectAnalysisService projectAnalysisService;

    /**
     * 查询标的物行业分布一览表
     * @param condition
     * @return
     */
    @ApiOperation(value = "查询标的物行业分布一览表")
    @ApiImplicitParam(name = "req", value = "查询条件", required = true, dataType = "ProjectAnalysisReq", paramType = "body")
    @PostMapping(value="/queryByIndustry")
    @ResponseBody
    public Result<List<ProjectAnalysisVo>> queryProjectAnalysis(@RequestBody ProjectAnalysisReq condition){

        List<ProjectAnalysisVo> projectAnalysisVoList = projectAnalysisService.queryProjectIndustryDistributionListNew(condition.getStartDate(), condition.getEndDate());
        return Result.ok(projectAnalysisVoList);
    }
    /**
     * 导出标的物行业分布一览表
     * @param condition
     * @return
     */
    @ApiOperation(value = "导出标的物行业分布一览表")
    @ApiImplicitParam(name = "req", value = "查询条件", required = true, dataType = "ProjectAnalysisReq", paramType = "body")
    @PostMapping(value="/exportByIndustry")
    public void exportProjectAnalysis(@RequestBody ProjectAnalysisReq condition, HttpServletResponse response){

        List<ProjectAnalysisVo> projectAnalysisVoList = projectAnalysisService.queryProjectIndustryDistributionListNew(condition.getStartDate(), condition.getEndDate());
        if(CollectionUtils.isNotEmpty(projectAnalysisVoList)){
            List<ProjectAnalysis> projectAnalysesSecond = new ArrayList<>();
            ProjectAnalysis lastRow = new ProjectAnalysis();
            lastRow.setEngineeringClass(ProjectAnalysisConstants.ALLCOMPANY);
            lastRow.setProjectIndustry("-");
            lastRow.setProjectCount(0);
            lastRow.setEntrustMoney(ProjectAnalysisConstants.ZERO4);
            lastRow.setProportion(new BigDecimal("100"));
            for (ProjectAnalysisVo vo : projectAnalysisVoList) {
                List<ProjectAnalysis> list = vo.getList();
                projectAnalysesSecond.addAll(list);
            }
            for (ProjectAnalysis projectAnalysis : projectAnalysesSecond) {
                lastRow.setProjectCount(lastRow.getProjectCount()+projectAnalysis.getProjectCount());
                lastRow.setEntrustMoney(lastRow.getEntrustMoney().add(projectAnalysis.getEntrustMoney()));
            }
            projectAnalysesSecond.add(lastRow);
            String fileName = "标的物行业分布一览";
            String sheetName = condition.getStartDate()+"至"+condition.getEndDate();
            try {
                int[] needMerge = {0};
                ExcelUtil.writeExcelWithMerge(response, projectAnalysesSecond, fileName,sheetName, ProjectAnalysis.class,needMerge,1);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }


    /**
     * 查询地区分布一览表
     * @param condition
     * @return
     */
    @ApiOperation(value = "查询地区分布一览表")
    @ApiImplicitParam(name = "req", value = "查询条件", required = true, dataType = "ProjectAnalysisReq", paramType = "body")
    @PostMapping(value="/queryByArea")
    @ResponseBody
    public Result<List<ProjectAnalysisArea>> queryProjectAnalysisArea(@RequestBody ProjectAnalysisReq condition){
       /* //按一级行业分组统计数据
        ProjectAnalysis projectAnalysTotal = projectAnalysisService.queryAllForArea(condition.getStartDate(), condition.getEndDate());

        //按二级行业分组统计数据
        List<ProjectAnalysisArea> projectAnalysesSecond = projectAnalysisService.queryProjectListWithArea(condition.getStartDate(), condition.getEndDate());

        //处理数据(计算比例，封装展示数据)
        projectAnalysesSecond = createAreaData(projectAnalysTotal,projectAnalysesSecond,false);
        return Result.ok(projectAnalysesSecond);*/

        List<ProjectAnalysisArea> projectAnalysisAreas = projectAnalysisService.queryProjectListWithAreaNew(condition.getStartDate(), condition.getEndDate());
        return Result.ok(projectAnalysisAreas);
    }

    /**
     * 导出地区分布一览表
     * @param condition
     * @return
     */
    @ApiOperation(value = "导出地区分布一览表")
    @ApiImplicitParam(name = "req", value = "查询条件", required = true, dataType = "ProjectAnalysisReq", paramType = "body")
    @PostMapping(value="/exportByArea")
    public void exportProjectAnalysisArea(@RequestBody ProjectAnalysisReq condition, HttpServletResponse response){
        List<ProjectAnalysisArea> projectAnalysisAreas = projectAnalysisService.queryProjectListWithAreaNewForExport(condition.getStartDate(), condition.getEndDate());
        if(CollectionUtils.isNotEmpty(projectAnalysisAreas)){
            String fileName = "地区分布一览";
            String sheetName = condition.getStartDate()+"至"+condition.getEndDate();
            try {
                ExcelUtil.writeExcel(response, projectAnalysisAreas, fileName,sheetName, ProjectAnalysisArea.class);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

    }


    /**
     * 导出地区分布一览表
     */
    /*@ApiOperation(value = "导出地区分布一览表")
    @ApiImplicitParam(name = "req", value = "查询条件", required = true, dataType = "ProjectAnalysisReq", paramType = "body")
    @PostMapping(value="/exportByArea")
    public void exportProjectAnalysisArea(@RequestBody ProjectAnalysisReq condition, HttpServletResponse response){
        //按一级行业分组统计数据
        ProjectAnalysis projectAnalysTotal = projectAnalysisService.queryAllForArea(condition.getStartDate(), condition.getEndDate());

        //按二级行业分组统计数据
        List<ProjectAnalysisArea> projectAnalysesSecond = projectAnalysisService.queryProjectListWithArea(condition.getStartDate(), condition.getEndDate());

        if(CollectionUtils.isNotEmpty(projectAnalysesSecond)){
            for (ProjectAnalysisArea p : projectAnalysesSecond) {//计算比例，保留2位小数
                if(projectAnalysTotal.getEntrustMoney().compareTo(BigDecimal.ZERO) > 0){
                    p.setProportion(p.getEntrustMoney().divide(projectAnalysTotal.getEntrustMoney(),4,RoundingMode.HALF_UP).multiply(new BigDecimal("100")).setScale(2, RoundingMode.HALF_UP));
                }
            }
            //处理数据(计算比例，封装展示数据)
            projectAnalysesSecond = createAreaData(projectAnalysTotal,projectAnalysesSecond,true);
            String fileName = "地区分布一览";
            String sheetName = condition.getStartDate()+"至"+condition.getEndDate();
            try {
                ExcelUtil.writeExcel(response, projectAnalysesSecond, fileName,sheetName, ProjectAnalysisArea.class);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

    }*/

    private List<ProjectAnalysisArea> createAreaData(ProjectAnalysis projectAnalysTotal,List<ProjectAnalysisArea> old,boolean isAllCompany){
        ProjectAnalysisArea[] arrayNew;
        List<ProjectAnalysisArea> newList = null;
        if(CollectionUtils.isNotEmpty(old)){
            arrayNew = new ProjectAnalysisArea[18];
            newList = new ArrayList<>();
            for (ProjectAnalysisArea p : old) {//计算比例，保留2位小数
                if(projectAnalysTotal.getEntrustMoney().compareTo(BigDecimal.ZERO) > 0){
                    p.setProportion(p.getEntrustMoney().divide(projectAnalysTotal.getEntrustMoney(),4,RoundingMode.HALF_UP).multiply(new BigDecimal("100")).setScale(2,RoundingMode.HALF_UP));
                }
                if(StringUtils.isNotEmpty(p.getCity()) && p.getCity().indexOf("省") > -1){
                    p.setCity(p.getCity().substring(p.getCity().indexOf("省")+1));
                }
            }


            ProjectAnalysisArea southSu = new ProjectAnalysisArea();
            southSu.setCity(ProjectAnalysisConstants.SOUTHSU);
            southSu.setEntrustMoney(ProjectAnalysisConstants.ZERO4);
            southSu.setProjectCount(0);
            southSu.setProportion(ProjectAnalysisConstants.ZERO2);
            ProjectAnalysisArea midSu = new ProjectAnalysisArea();
            midSu.setCity(ProjectAnalysisConstants.MIDSU);
            midSu.setEntrustMoney(ProjectAnalysisConstants.ZERO4);
            midSu.setProjectCount(0);
            midSu.setProportion(ProjectAnalysisConstants.ZERO2);
            ProjectAnalysisArea northSu = new ProjectAnalysisArea();
            northSu.setCity(ProjectAnalysisConstants.NORTHSU);
            northSu.setEntrustMoney(ProjectAnalysisConstants.ZERO4);
            northSu.setProjectCount(0);
            northSu.setProportion(ProjectAnalysisConstants.ZERO2);
            ProjectAnalysisArea outSu = new ProjectAnalysisArea();
            outSu.setCity(ProjectAnalysisConstants.OUTSU);
            outSu.setEntrustMoney(ProjectAnalysisConstants.ZERO4);
            outSu.setProjectCount(0);
            outSu.setProportion(ProjectAnalysisConstants.ZERO2);

            ProjectAnalysisArea allCompany = new ProjectAnalysisArea();
            allCompany.setCity(ProjectAnalysisConstants.ALLCOMPANY);
            if(projectAnalysTotal.getEntrustMoney() != null){
                allCompany.setEntrustMoney(projectAnalysTotal.getEntrustMoney().divide(ProjectAnalysisConstants.TEN_THOUSAND,6, RoundingMode.HALF_UP));
            }
            allCompany.setProjectCount(projectAnalysTotal.getProjectCount());
            allCompany.setProportion(new BigDecimal("100.00"));
            for (ProjectAnalysisArea projectAnalysisArea : old) {
                BigDecimal proportion = projectAnalysisArea.getProportion();
                if(ProjectAnalysisConstants.SOUTH_OF_JIANGSU.contains(projectAnalysisArea.getCityCode())){//苏南
                    southSu.setProjectCount(southSu.getProjectCount()+projectAnalysisArea.getProjectCount());
                    southSu.setEntrustMoney(southSu.getEntrustMoney().add(projectAnalysisArea.getEntrustMoney()));
                    if(proportion != null){
                        southSu.setProportion(southSu.getProportion().add(proportion));
                    }
                }else if(ProjectAnalysisConstants.MID_OF_JIANGSU.contains(projectAnalysisArea.getCityCode())){//苏中
                    midSu.setProjectCount(midSu.getProjectCount()+projectAnalysisArea.getProjectCount());
                    midSu.setEntrustMoney(midSu.getEntrustMoney().add(projectAnalysisArea.getEntrustMoney()));
                    if(proportion != null){
                        midSu.setProportion(midSu.getProportion().add(proportion));
                    }

                }else if(ProjectAnalysisConstants.NORTH_OF_JIANGSU.contains(projectAnalysisArea.getCityCode())){//苏北
                    northSu.setProjectCount(northSu.getProjectCount()+projectAnalysisArea.getProjectCount());
                    northSu.setEntrustMoney(northSu.getEntrustMoney().add(projectAnalysisArea.getEntrustMoney()));
                    if(proportion != null){
                        northSu.setProportion(northSu.getProportion().add(proportion));
                    }

                }else{//外省
                    outSu.setProjectCount(outSu.getProjectCount()+projectAnalysisArea.getProjectCount());
                    outSu.setEntrustMoney(outSu.getEntrustMoney().add(projectAnalysisArea.getEntrustMoney()));
                    if(proportion != null){
                        outSu.setProportion(outSu.getProportion().add(proportion));
                    }
                }
            }
            if(southSu.getEntrustMoney() != null){
                southSu.setEntrustMoney(southSu.getEntrustMoney().divide(ProjectAnalysisConstants.TEN_THOUSAND,6, RoundingMode.HALF_UP));
            }
            if(midSu.getEntrustMoney() != null){
                midSu.setEntrustMoney(midSu.getEntrustMoney().divide(ProjectAnalysisConstants.TEN_THOUSAND,6, RoundingMode.HALF_UP));
            }
            if(northSu.getEntrustMoney() != null){
                northSu.setEntrustMoney(northSu.getEntrustMoney().divide(ProjectAnalysisConstants.TEN_THOUSAND,6, RoundingMode.HALF_UP));
            }
            if(outSu.getEntrustMoney() != null){
                outSu.setEntrustMoney(outSu.getEntrustMoney().divide(ProjectAnalysisConstants.TEN_THOUSAND,6, RoundingMode.HALF_UP));

            }

            old.add(southSu);//苏南
            old.add(midSu);//苏中
            old.add(northSu);//苏北
            old.add(outSu);//外省

            for (ProjectAnalysisArea p : old) {//重排序
                if(null != p.getCityCode() ){
                    if(p.getCityCode().equals(ProjectAnalysisConstants.NANJING)){
                        if(p.getEntrustMoney() != null) {
                            p.setEntrustMoney(p.getEntrustMoney().divide(ProjectAnalysisConstants.TEN_THOUSAND,6, RoundingMode.HALF_UP));
                        }
                        arrayNew[0] = p;
                    }else if (p.getCityCode().equals(ProjectAnalysisConstants.WUXI) ){
                        if(p.getEntrustMoney() != null) {
                            p.setEntrustMoney(p.getEntrustMoney().divide(ProjectAnalysisConstants.TEN_THOUSAND,6, RoundingMode.HALF_UP));
                        }
                        arrayNew[1] = p;
                    }else if (p.getCityCode().equals(ProjectAnalysisConstants.CHANGZHOU) ){
                        if(p.getEntrustMoney() != null) {
                            p.setEntrustMoney(p.getEntrustMoney().divide(ProjectAnalysisConstants.TEN_THOUSAND,6, RoundingMode.HALF_UP));
                        }
                        arrayNew[2] = p;
                    }else if (p.getCityCode().equals(ProjectAnalysisConstants.SUZHOU) ){
                        if(p.getEntrustMoney() != null) {
                            p.setEntrustMoney(p.getEntrustMoney().divide(ProjectAnalysisConstants.TEN_THOUSAND,6, RoundingMode.HALF_UP));
                        }
                        arrayNew[3] = p;
                    }else if (p.getCityCode().equals(ProjectAnalysisConstants.ZHENJIANG) ){
                        if(p.getEntrustMoney() != null) {
                            p.setEntrustMoney(p.getEntrustMoney().divide(ProjectAnalysisConstants.TEN_THOUSAND,6, RoundingMode.HALF_UP));
                        }
                        arrayNew[4] = p;
                    }else if (p.getCityCode().equals(ProjectAnalysisConstants.YANGZHOU)){
                        if(p.getEntrustMoney() != null) {
                            p.setEntrustMoney(p.getEntrustMoney().divide(ProjectAnalysisConstants.TEN_THOUSAND,6, RoundingMode.HALF_UP));
                        }
                        arrayNew[6] = p;
                    }else if (p.getCityCode().equals(ProjectAnalysisConstants.TAIZHOU) ){
                        if(p.getEntrustMoney() != null) {
                            p.setEntrustMoney(p.getEntrustMoney().divide(ProjectAnalysisConstants.TEN_THOUSAND,6, RoundingMode.HALF_UP));
                        }
                        arrayNew[7] = p;
                    }else if (p.getCityCode().equals(ProjectAnalysisConstants.NANTONG) ){
                        if(p.getEntrustMoney() != null) {
                            p.setEntrustMoney(p.getEntrustMoney().divide(ProjectAnalysisConstants.TEN_THOUSAND,6, RoundingMode.HALF_UP));
                        }
                        arrayNew[8] = p;
                    }else if (p.getCityCode().equals(ProjectAnalysisConstants.SUQIAN) ){
                        if(p.getEntrustMoney() != null) {
                            p.setEntrustMoney(p.getEntrustMoney().divide(ProjectAnalysisConstants.TEN_THOUSAND,6, RoundingMode.HALF_UP));
                        }
                        arrayNew[10] = p;
                    }else if (p.getCityCode().equals(ProjectAnalysisConstants.HUAIAN) ){
                        if(p.getEntrustMoney() != null) {
                            p.setEntrustMoney(p.getEntrustMoney().divide(ProjectAnalysisConstants.TEN_THOUSAND,6, RoundingMode.HALF_UP));
                        }
                        arrayNew[11] = p;
                    }else if (p.getCityCode().equals(ProjectAnalysisConstants.YANCHENG) ){
                        if(p.getEntrustMoney() != null) {
                            p.setEntrustMoney(p.getEntrustMoney().divide(ProjectAnalysisConstants.TEN_THOUSAND,6, RoundingMode.HALF_UP));
                        }
                        arrayNew[12] = p;
                    }else if (p.getCityCode().equals(ProjectAnalysisConstants.XUZHOU) ){
                        if(p.getEntrustMoney() != null) {
                            p.setEntrustMoney(p.getEntrustMoney().divide(ProjectAnalysisConstants.TEN_THOUSAND,6, RoundingMode.HALF_UP));
                        }
                        arrayNew[13] = p;
                    }else if (p.getCityCode().equals(ProjectAnalysisConstants.LIANYUNGANG) ){
                        if(p.getEntrustMoney() != null) {
                            p.setEntrustMoney(p.getEntrustMoney().divide(ProjectAnalysisConstants.TEN_THOUSAND,6, RoundingMode.HALF_UP));
                        }
                        arrayNew[14] = p;
                    }
                }


                if(ProjectAnalysisConstants.SOUTHSU.equals(p.getCity())){
                    arrayNew[5] = p;
                }else if(ProjectAnalysisConstants.MIDSU.equals(p.getCity())){
                    arrayNew[9] = p;
                }else if(ProjectAnalysisConstants.NORTHSU.equals(p.getCity())){
                    arrayNew[15] = p;
                }else if(ProjectAnalysisConstants.OUTSU.equals(p.getCity())){
                    arrayNew[16] = p;
                }else if(ProjectAnalysisConstants.ALLCOMPANY.equals(p.getCity())){
                    arrayNew[17] = p;
                }
            }
            Collections.addAll(newList, arrayNew);
            newList.removeAll(Collections.singleton(null));
            if(isAllCompany){
                newList.add(allCompany);
            }
        }
        return  newList;
    }

    /**
     * 查询大项目处室排名
     * @param condition
     * @return
     */
    @ApiOperation(value = "查询大项目处室排名")
    @PostMapping(value="/queryByDepartment")
    @ApiImplicitParam(name = "req", value = "查询条件", required = true, dataType = "ProjectAnalysisReq", paramType = "body")
    @ResponseBody
    public Result<List<DepartProject>> queryByDepartment(@RequestBody ProjectAnalysisReq condition){
        if(StringUtils.isEmpty(condition.getStartDate()) || StringUtils.isEmpty(condition.getEndDate())){
            return Result.failed("暂无数据",null);
        }

        //按处室分组统计数据
//        List<DepartProject> departProjectList = projectAnalysisService.queryProjectByDepartment(condition.getStartDate(), condition.getEndDate());

        List<DepartProject> departProjectList = projectAnalysisService.queryProjectByDepartmentNew(condition.getStartDate(), condition.getEndDate());
        return Result.ok(departProjectList);
    }

    /**
     * 导出大项目处室排名
     * @param condition
     * @return
     */
    @ApiOperation(value = "导出大项目处室排名")
    @ApiImplicitParam(name = "req", value = "查询条件", required = true, dataType = "ProjectAnalysisReq", paramType = "body")
    @PostMapping(value="/exportByDepartment")
    public void exportByDepartment(HttpServletResponse response,@RequestBody ProjectAnalysisReq condition){


        //按处室分组统计数据
        List<DepartProject> departProjectList = projectAnalysisService.queryProjectByDepartmentNew(condition.getStartDate(), condition.getEndDate());
        if(CollectionUtils.isNotEmpty(departProjectList)){
            DepartProject allCompany = new DepartProject();
            allCompany.setMonthCount(0);
            allCompany.setMonthAmount(ProjectAnalysisConstants.ZERO4);
            allCompany.setYearCount(0);
            allCompany.setYearAmount(ProjectAnalysisConstants.ZERO4);
            allCompany.setLastYearCount(0);
            allCompany.setLastYearAmount(ProjectAnalysisConstants.ZERO4);
            allCompany.setCountCompared(ProjectAnalysisConstants.ZERO2);
            allCompany.setAmountCompared(ProjectAnalysisConstants.ZERO2);

            allCompany.setDepartmentName(ProjectAnalysisConstants.ALLCOMPANY);
            int seq = 1;
            for (DepartProject departProject : departProjectList) {
                departProject.setSeq(seq++);

                allCompany.setMonthCount(allCompany.getMonthCount()+departProject.getMonthCount());
                allCompany.setMonthAmount(allCompany.getMonthAmount().add(departProject.getMonthAmount()));
                allCompany.setYearCount(allCompany.getYearCount()+departProject.getYearCount());
                allCompany.setYearAmount(allCompany.getYearAmount().add(departProject.getYearAmount()));
                allCompany.setLastYearCount(allCompany.getLastYearCount()+departProject.getLastYearCount());
                allCompany.setLastYearAmount(allCompany.getLastYearAmount().add(departProject.getLastYearAmount()));
            }
            if(allCompany.getLastYearCount() != 0){
                BigDecimal countCompared = new BigDecimal(allCompany.getYearCount()).subtract(new BigDecimal(allCompany.getLastYearCount())).divide(new BigDecimal(allCompany.getLastYearCount()),2, RoundingMode.HALF_UP).multiply(new BigDecimal("100"));
                allCompany.setCountCompared(countCompared);
            }
            if(allCompany.getLastYearAmount().compareTo(BigDecimal.ZERO) > 0){
                allCompany.setAmountCompared((allCompany.getYearAmount().subtract(allCompany.getLastYearAmount())).divide(allCompany.getLastYearAmount(),4, RoundingMode.HALF_UP).multiply(new BigDecimal("100")).setScale(2,RoundingMode.HALF_UP));
            }
            departProjectList.add(allCompany);
        }
        String fileName = "大项目处室排名";
        String sheetName = condition.getStartDate()+"至"+condition.getEndDate();
        try {
            ExcelUtil.writeExcel(response, departProjectList, fileName,sheetName, DepartProject.class);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    /*@ApiOperation(value = "导出大项目处室排名")
    @ApiImplicitParam(name = "req", value = "查询条件", required = true, dataType = "ProjectAnalysisReq", paramType = "body")
    @PostMapping(value="/exportByDepartment")
    public void exportByDepartment(HttpServletResponse response,@RequestBody ProjectAnalysisReq condition){


        //按处室分组统计数据
        List<DepartProject> departProjectList = projectAnalysisService.queryProjectByDepartment(condition.getStartDate(), condition.getEndDate());
        if(CollectionUtils.isNotEmpty(departProjectList)){
            DepartProject allCompany = new DepartProject();
            allCompany.setMonthCount(0);
            allCompany.setMonthAmount(ProjectAnalysisConstants.ZERO4);
            allCompany.setYearCount(0);
            allCompany.setYearAmount(ProjectAnalysisConstants.ZERO4);
            allCompany.setLastYearCount(0);
            allCompany.setLastYearAmount(ProjectAnalysisConstants.ZERO4);
            allCompany.setCountCompared(ProjectAnalysisConstants.ZERO2);
            allCompany.setAmountCompared(ProjectAnalysisConstants.ZERO2);

            allCompany.setDepartmentName(ProjectAnalysisConstants.ALLCOMPANY);
            for (DepartProject departProject : departProjectList) {
                allCompany.setMonthCount(allCompany.getMonthCount()+departProject.getMonthCount());
                allCompany.setMonthAmount(allCompany.getMonthAmount().add(departProject.getMonthAmount()));
                allCompany.setYearCount(allCompany.getYearCount()+departProject.getYearCount());
                allCompany.setYearAmount(allCompany.getYearAmount().add(departProject.getYearAmount()));
                allCompany.setLastYearCount(allCompany.getLastYearCount()+departProject.getLastYearCount());
                allCompany.setLastYearAmount(allCompany.getLastYearAmount().add(departProject.getLastYearAmount()));
            }
            if(allCompany.getLastYearCount() != 0){
                BigDecimal countCompared = new BigDecimal(allCompany.getYearCount()).subtract(new BigDecimal(allCompany.getLastYearCount())).divide(new BigDecimal(allCompany.getLastYearCount()),2, RoundingMode.HALF_UP).multiply(new BigDecimal("100"));
                allCompany.setCountCompared(countCompared);
            }
            if(allCompany.getLastYearAmount().compareTo(BigDecimal.ZERO) > 0){
                allCompany.setAmountCompared((allCompany.getYearAmount().subtract(allCompany.getLastYearAmount())).divide(allCompany.getLastYearAmount(),4, RoundingMode.HALF_UP).multiply(new BigDecimal("100")).setScale(2,RoundingMode.HALF_UP));
            }
            departProjectList.add(allCompany);

        }
        String fileName = "大项目处室排名";
        String sheetName = condition.getStartDate()+"至"+condition.getEndDate();
        try {
            ExcelUtil.writeExcel(response, departProjectList, fileName,sheetName, DepartProject.class);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }*/

    /**
     * 查询大项目明细
     * @param condition
     * @return
     */
    @ApiOperation(value = "查询大项目明细")
    @ApiImplicitParam(name = "req", value = "查询条件", required = true, dataType = "ProjectAnalysisReq", paramType = "body")
    @PostMapping(value="/queryLargeProject")
    @ResponseBody
    public Result<List<LargeProject>> queryLargeProjectList(@RequestBody ProjectAnalysisReq condition){
        List<LargeProject> largeProjectList = projectAnalysisService.queryLargeProjectListNew(condition.getStartDate(), condition.getEndDate());
        return Result.ok(largeProjectList);
    }

    /**
     * 导出大项目明细
     * @param condition
     * @return
     */
    @ApiOperation(value = "导出大项目明细")
    @ApiImplicitParam(name = "req", value = "查询条件", required = true, dataType = "ProjectAnalysisReq", paramType = "body")
    @PostMapping(value="/exportLargeProject")
    public void exportLargeProjectList(HttpServletResponse response,@RequestBody ProjectAnalysisReq condition){
        List<LargeProject> largeProjectList = projectAnalysisService.queryLargeProjectListNew(condition.getStartDate(), condition.getEndDate());
        String fileName = "大项目明细";
        String sheetName = condition.getStartDate()+"至"+condition.getEndDate();
        try {
            ExcelUtil.writeExcel(response, largeProjectList, fileName,sheetName, LargeProject.class);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @ApiOperation(value = "导出行业项目分布（附表）")
    @ApiImplicitParam(name = "req", value = "查询条件", required = true, dataType = "ProjectAnalysisReq", paramType = "body")
    @PostMapping(value="/exportByIndustrySchedule")
    public void exportByIndustrySchedule(@RequestBody ProjectAnalysisReq condition, HttpServletResponse response){
        List<CollectInfo> collectExperts = projectAnalysisService.collectExpertList(condition);
        if(CollectionUtils.isEmpty(collectExperts)){
            throw new ParamsNotNullException("没有查询到要导出的记录!");
        }
        for (CollectInfo collectExpert:collectExperts) {
            if(null != collectExpert.getPackageNumber()){
                collectExpert.setPurchaseNumber(collectExpert.getPurchaseNumber() + "/" + collectExpert.getPackageNumber());
            }
        }
        //设置标题信息
        Workbook workbook;
        String title;
        DateFormat sdf= new SimpleDateFormat("yyyy-MM");
        String startTime= null;
        String endTime= null;
        try {
            startTime = sdf.format(sdf.parse(condition.getStartDate()));
            endTime = sdf.format(sdf.parse(condition.getEndDate()));
        } catch (ParseException e) {
            e.printStackTrace();
        }
        title="行业项目分布一览表（附表）";
        String secondTitle = "("+startTime+"至"+endTime+")";
        ExportParams exportParams = new ExportParams(title,secondTitle,"行业项目分布一览表（附表）");
        exportParams.setStyle(ExcelStyleUtil.class);
        workbook= ExcelExportUtil.exportExcel(exportParams, CollectInfo.class,collectExperts);
        try {
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("行业项目分布一览表（附表）.xls", "UTF-8"));
            response.setContentType("application/vnd.ms-excel;charset=UTF-8");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        try {
            workbook.write(response.getOutputStream());
            response.getOutputStream().close();
            workbook.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

}
