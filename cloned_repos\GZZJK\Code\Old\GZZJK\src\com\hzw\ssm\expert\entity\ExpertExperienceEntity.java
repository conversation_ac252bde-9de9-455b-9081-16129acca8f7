package com.hzw.ssm.expert.entity;

import com.hzw.ssm.fw.base.BaseEntity;

public class ExpertExperienceEntity extends BaseEntity {

	/**
	 * 
	 */
	private static final long serialVersionUID = -2846680897267014283L;

	private String id;//主键
	
	private String user_id;//用户编号
	
	private String between_time;//起止年月
	
	private String company;//工作单位
	
	private String witness;//证明人
	
	private String witness_phone;//证明人联系电话

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getUser_id() {
		return user_id;
	}

	public void setUser_id(String userId) {
		user_id = userId;
	}

	public String getBetween_time() {
		return between_time;
	}

	public void setBetween_time(String betweenTime) {
		between_time = betweenTime;
	}

	public String getCompany() {
		return company;
	}

	public void setCompany(String company) {
		this.company = company;
	}

	public String getWitness() {
		return witness;
	}

	public void setWitness(String witness) {
		this.witness = witness;
	}

	public String getWitness_phone() {
		return witness_phone;
	}

	public void setWitness_phone(String witnessPhone) {
		witness_phone = witnessPhone;
	}
	
}
