package com.hzw.sunflower.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.hzw.sunflower.common.BaseBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


@ApiModel(description = "评标办法表")
@TableName("t_bid_doc_evaluation")
@Data
public class BidDocEvaluation extends BaseBean {

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "招标文件编制表id")
    private Long docId;

    @ApiModelProperty(value = "评标方法：1综合评估法，2最低价法")
    private Integer evaluationMethod;

    @ApiModelProperty(value = "中标候选人排序方法")
    private String sortMethod;

    @ApiModelProperty(value = "分值构成")
    private String scoreComposition;

    @ApiModelProperty(value = "评标基准价计算方法")
    private String benchmarkComputeMethod;

    @ApiModelProperty(value = "投标报价的偏差率计算公式")
    private String deviationComputeMethod;

}
