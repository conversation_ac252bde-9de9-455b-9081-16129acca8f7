<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hzw.sunflower.dao.CompanyStatisticsMapper" >
    <resultMap id="BaseResultMap" type="com.hzw.sunflower.entity.CompanyStatistics" >
        <id column="id" property="id" jdbcType="BIGINT" />
        <result column="month" property="month" jdbcType="VARCHAR" />
        <result column="project_count" property="projectCount" jdbcType="BIGINT" />
        <result column="n_project_count" property="nProjectCount" jdbcType="BIGINT" />
        <result column="entrusted_amount" property="entrustedAmount" jdbcType="DECIMAL" />
        <result column="n_entrusted_amount" property="nEntrustedAmount" jdbcType="DECIMAL" />
        <result column="win_price" property="winPrice" jdbcType="DECIMAL" />
        <result column="account" property="account" jdbcType="DECIMAL" />
        <result column="estimate_account" property="estimateAccount" jdbcType="DECIMAL" />
        <result column="n_account" property="nAccount" jdbcType="DECIMAL" />
    </resultMap>
</mapper>