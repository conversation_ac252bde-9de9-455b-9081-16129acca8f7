package com.hzw.sunflower.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.hzw.sunflower.common.BaseBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 邮件附件
 * @ClassName:EmailFile
 * @Auther: lijinxin
 * @Description:
 * @Date: 2023/3/2 16:29
 * @Version: v1.0
 */
@ApiModel("文件附件表")
@TableName("t_email_file")
@Data
public class EmailFile extends BaseBean {

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("邮件id")
    private Long emailId;

    @ApiModelProperty("文件id")
    private Long ossId;
}
