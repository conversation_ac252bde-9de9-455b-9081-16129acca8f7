package com.hzw.sunflower.dto.bidder;

import com.hzw.sunflower.common.BaseCondition;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date ：Created in 2021/6/10 10:41
 * @description：招标人查询dto
 * @modified By：`
 * @version: 1.0
 */
@Data
public class BidderProjectCondition extends BaseCondition {


    /**
     * 版本号
     */
    private static final long serialVersionUID = -425612807840455662L;

    @ApiModelProperty(value = "关键字搜索")
    private String keyWords;
    @ApiModelProperty(value = "项目名称")
    private String projectName;
    @ApiModelProperty(value = "项目编号")
    private String projectNumber;
    @ApiModelProperty(value = "采购名称")
    private String purchaseName;
    @ApiModelProperty(value = "采购编号")
    private String purchaseNumber;
    @ApiModelProperty(value = "省")
    private String addressProvince;
    @ApiModelProperty(value = "市")
    private String addressCity;

    @ApiModelProperty(value = "代理单位")
    private String agentCompany;
    @ApiModelProperty(value = "代理机构负责人")
    private String agentChargeName;
    @ApiModelProperty(value = "代理机构负责人电话")
    private String agentChargePhone;

    @ApiModelProperty(value = "委托单位")
    private String principalCompany;
    @ApiModelProperty(value = "委托负责人")
    private String principalChargeName;
    @ApiModelProperty(value = "委托负责人电话")
    private String principalChargePhone;

    @ApiModelProperty(value = "项目状态")
    private String projectStatus;

    @ApiModelProperty(value = "开始时间")
    private String beginTime;
    @ApiModelProperty(value = "结束时间")
    private String endTime;

    @ApiModelProperty(value = "排序方式")
    private String orderType;

    @ApiModelProperty(value = "用户id")
    private Long userId;

    @ApiModelProperty(value = "部门id")
    private Long departId;

    @ApiModelProperty(value = "项目 0 全部   1 开标前  2 开标后 3 完结 ")
    private Integer searchType;

    @ApiModelProperty(value = "类型")
    private Integer type;

    @ApiModelProperty(value = "递交截止开始时间")
    private String submitBeginTime;
    @ApiModelProperty(value = "递交截止结束时间")
    private String submitEndTime;

    private String dataScope;
}
