package com.hzw.sunflower.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hzw.sunflower.controller.project.response.BookkeepDepartmentConfirmVo;
import com.hzw.sunflower.entity.BookkeepDepartmentConfirm;
import com.hzw.sunflower.entity.User;
import com.hzw.sunflower.entity.condition.BookkeepDepartmentConfirmCondition;
import org.apache.ibatis.annotations.Param;

public interface BookkeepDepartmentConfirmMapper extends BaseMapper<BookkeepDepartmentConfirm> {

    IPage<BookkeepDepartmentConfirmVo> queryShareList(IPage<BookkeepDepartmentConfirmVo> page, @Param("condition") BookkeepDepartmentConfirmCondition condition, @Param("userId")Long userId);

    User findUser(Long projectId);

    String findPhone(Long id);
}
