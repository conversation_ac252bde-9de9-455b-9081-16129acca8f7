package com.hzw.sunflower.controller;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.hzw.sunflower.common.BaseController;
import com.hzw.sunflower.common.Result;
import com.hzw.sunflower.common.redis.RedisCache;
import com.hzw.sunflower.controller.request.BidOpenDetailReq;
import com.hzw.sunflower.controller.request.ScheduleAmountREQ;
import com.hzw.sunflower.controller.response.ScheduleAmountVO;
import com.hzw.sunflower.entity.BidOpenDetail;
import com.hzw.sunflower.service.BidOpenDetailService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Api(tags = "开标一览")
@RestController
@RequestMapping("/bidOpenDetai")
public class BidOpenDetailController extends BaseController {

    @Autowired
    BidOpenDetailService bidOpenDetailService;
    @Autowired
    private RedisCache redisCache;

    /**
     * 查询开标一览表
     * @param req
     * @return
     */
    @ApiOperation(value = "查询开标一览表")
    @ApiImplicitParam(name = "req", value = "查询条件", required = true, dataType = "BidOpenDetailReq", paramType = "body")
    @PostMapping(value="/queryBidOpenDetailList")
    @ResponseBody
    public Map queryBidOpenDetailList(@RequestBody BidOpenDetailReq req){
        Map bidOpenDetails = bidOpenDetailService.selectBidOpenDetailList(req,req.getKeyWords(), req.getStartDate(), req.getEndDate(),req.getPageIndex(),req.getPageSize());
        return bidOpenDetails;
    }


    @ApiOperation(value = "根据筛选条件查询日程金额统计（年月）")
    @ApiImplicitParam(name = "req", value = "筛选条件", required = true, dataType = "ScheduleAmountREQ", paramType = "body")
    @PostMapping("/getScheduleAmount")
    public Result<ScheduleAmountVO> getScheduleAmount(@RequestBody ScheduleAmountREQ req) {
        String keyCache="getScheduleAmount_"+getJwtUser().getUserId()+"_"+ getJwtUser().getUser().getDepartId()+"_"+JSON.toJSONString(req);
        Object reJson= redisCache.getCacheObject(keyCache);
        ScheduleAmountVO scheduleAmountVO=null;
        if (reJson!=null){
             scheduleAmountVO = JSONUtil.toBean(JSONUtil.toJsonStr(reJson),ScheduleAmountVO.class);
        }else {
            scheduleAmountVO = bidOpenDetailService.getScheduleAmount(req);
            redisCache.setCacheObject(keyCache,JSONUtil.toJsonStr(scheduleAmountVO),600, TimeUnit.SECONDS);
        }


        return Result.ok(scheduleAmountVO);
    }


    @ApiOperation(value = "导出excel")
    @ApiImplicitParam(name = "req", value = "查询条件", required = true, dataType = "UserStatisticsReq", paramType = "body")
    @PostMapping("/exportBidOpenDetailList")
    public  void  exportBidOpenDetailList(HttpServletResponse response, @RequestBody BidOpenDetailReq req) {
        List<BidOpenDetail> bidOpenDetails = bidOpenDetailService.queryBidOpenDetailList(req,req.getKeyWords(), req.getStartDate(), req.getEndDate());
        //设置标题信息
        Workbook workbook;
        String title;
//        DateFormat sdf= new SimpleDateFormat("yyyy-MM");
//        String startTime= null;
//        String endTime= null;
//        try {
//            startTime = sdf.format(sdf.parse(req.getStartDate()));
//            endTime = sdf.format(sdf.parse(req.getEndDate()));
//        } catch (ParseException e) {
//            e.printStackTrace();
//        }

        title="开标一览表";
        workbook= ExcelExportUtil.exportExcel(new ExportParams(title,"开标一览表"), BidOpenDetail.class,bidOpenDetails);
        try {
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("统计.xls", "UTF-8"));
            response.setContentType("application/vnd.ms-excel;charset=UTF-8");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        try {
            workbook.write(response.getOutputStream());
            response.getOutputStream().close();
            workbook.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}
