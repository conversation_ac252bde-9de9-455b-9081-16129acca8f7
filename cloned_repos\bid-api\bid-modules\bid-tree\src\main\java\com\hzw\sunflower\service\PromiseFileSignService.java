package com.hzw.sunflower.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hzw.sunflower.controller.request.PromiseFileSignReq;
import com.hzw.sunflower.entity.JwtUser;
import com.hzw.sunflower.entity.PromiseFileSign;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 * @date ：Created in 2021/8/27 11:31
 * @description：
 * @version: 1.0
 */
public interface PromiseFileSignService extends IService<PromiseFileSign> {

    /**
     * 专家签署承诺文件
     * @param req
     * @param jwtUser
     * @return
     */
    Boolean sign(MultipartFile file,PromiseFileSignReq req, JwtUser jwtUser);

}
