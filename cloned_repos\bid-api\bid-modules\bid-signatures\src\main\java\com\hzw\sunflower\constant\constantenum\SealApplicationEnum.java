package com.hzw.sunflower.constant.constantenum;

/**
 * <AUTHOR>
 */

public enum SealApplicationEnum {
    // 申请类型
    REPORTING_MATTER(1,"呈报事项"),
    CONTRACTUAL_MATTERS(2,"合同事项"),
    DRAFT_TEXT(3,"拟文"),
    SEAL_APPROVAL(4,"用印审批"),

    // 申请状态
    TO_SHOW(-1,"不展示"),// 申请中标通知书插入数据，但是不展示
    TO_APPROVE(1,"待审批"),
    AGREE(2,"已确认"),
    RETURN(3,"已退回"),
    WITHDRAW_PENDING(4,"撤回待审批"),
    WITHDRAW(5,"已撤回"),
    SEAL(6,"已盖章"),
    EXPORT_TYPE_TREAT(1,"待处理"),
    EXPORT_TYPE_ALREADY(2,"已处理")
    ;

    private Integer type;
    private String desc;

    SealApplicationEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static String getValueByKey(Integer key) {
        for (SealApplicationEnum enumValue : SealApplicationEnum.values()) {
            if (enumValue.type.equals(key)) {
                return enumValue.desc;
            }
        }
        return null;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
