package com.hzw.sunflower.constant.constantenum;

/**
 * 保证金申请退还处理方式
 */
public enum BondApplyRefundHandleType {
//1：指定处室处长确认  2：所有处室处长确认
    DESIGNATE_REGISTRAR_CONFIRM(1, "指定处室处长确认"),
    ALL_REGISTRAR_CONFIRM(2, "所有处室处长确认"),
    RETURNED(3,"退回");

    private Integer type;
    private String desc;

    BondApplyRefundHandleType(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

}
