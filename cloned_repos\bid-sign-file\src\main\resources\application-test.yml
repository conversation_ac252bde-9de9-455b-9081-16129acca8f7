server:
  port: 28933
  max-http-header-size: 100000
  servlet:
    context-path: /sign

#datasource
spring:
  main:
    allow-circular-references: true
  application:
    name: bid-sign-expert-api
  datasource:
    url: ***************************************************************************************************************************************************************************************************************************************
    username: test-56
    password: test-56
    driver-class-name: com.mysql.cj.jdbc.Driver

  jackson:
    # 全局设置@JsonFormat的格式pattern
    date-format: yyyy-MM-dd HH:mm:ss
    # 当地时区
    locale: zh
    # 设置全局时区
    time-zone: GMT+8
    # 常用，全局设置pojo或被@JsonInclude注解的属性的序列化方式
    #    default-property-inclusion: non_null #不为空的属性才会序列化,具体属性可看JsonInclude.Include
    # 常规默认,枚举类SerializationFeature中的枚举属性为key，值为boolean设置jackson序列化特性,具体key请看SerializationFeature源码
    serialization:
      write-dates-as-timestamps: false # 返回的java.util.date转换成timestamp
      FAIL_ON_EMPTY_BEANS: true # 对象为空时是否报错，默认true

  redis:
    # 地址
    host: **************
    # 端口，默认为6379
    port: 26379
    # 数据库索引
    database: 10
    # 密码
    password: TR0Y9s27Oda6oqKs
    # 连接超时时间
    timeout: 10s
    lettuce:
      pool:
        # 连接池中的最小空闲连接
        min-idle: 0
        # 连接池中的最大空闲连接
        max-idle: 8
        # 连接池的最大数据库连接数
        max-active: 8
        # #连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1ms
  rabbitmq:
    # 地址
    host: **************
    # 端口
    port: 25672
    # 账号
    username: hzw
    # 密码
    password: TR0Y9s27Oda6oqKs
    publisher-confirm-type: correlated
    publisher-returns: true
    listener:
      simple:
        retry:
          enabled: true
          max-attempts: 3
          initial-interval: 2000
        default-requeue-rejected: false
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
    throw-exception-if-no-handler-found: true
  servlet:
    multipart:
      # 开启 multipart 上传功能
      enabled: true
      # 文件写入磁盘的阈值
      file-size-threshold: 2KB
      # 最大文件大小
      max-file-size: 200MB
      # 最大请求大小
      max-request-size: 215MB
  resources:
    add-mappings: true
mybatis-plus:
  global-config:
    #数据库相关配置
    db-config:
      #主键类型  AUTO:"数据库ID自增", INPUT:"用户输入ID", ID_WORKER:"全局唯一ID (数字类型唯一ID)", UUID:"全局唯一ID UUID";
      id-type: AUTO
  configuration:
    map-underscore-to-camel-case: true
    typeAliasesPackage: com.hzw.bid.**.model
    mapper-locations: mapper/**/*Mapper.xml
    config-location: mybatis/mybatis-config.xml
    #logic-delete-field: isDelete  # 全局逻辑删除的实体字段名(since 3.3.0,配置后可以忽略不配置步骤2)
    logic-delete-value: 1 # 逻辑已删除值(默认为 1)
    logic-not-delete-value: 0 # 逻辑未删除值(默认为 0)
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
sa-token:
  token-name: token
  sign:
    # API 接口签名秘钥 （随便乱摁几个字母即可）
    secret-key: kQwIOrYvnXmSDkwEiFngrKidMcdrgKoR

#log
logging:
  # 过滤开关
  enabled: true
  level:
    root: INFO
    com:
      hzw:
        bid: trace
  file:
    path: /log

swagger:
  enable: true
  title: JTCC-open-API
  description: JTCC-open-API
  urlPrefix: http://
  termsOfServiceUrl: /doc.html
  version: 1.0
  local: true
# oss
oss:
  active: ctyun
  aliyun:
    endpoint: https://oss-cn-hangzhou.aliyuncs.com
    accessKeyId: LTAI5tAAyeuzcooZmRmsQ9eh
    accessKeySecret: ******************************
    bucketName: jtcc-dev
  ctyun:
    endpoint: oos-js.ctyunapi.cn
    accessKeyId: 37bee479813813bc76e3
    accessKeySecret: e2cbcf431ab590d2b1d05cf74d219df4fc44793b
    bucketName: jtcc-dev

#模板文件配置
files:
  template:
    path: C:\Users\<USER>\Desktop\test\tmp\
  temporary:
    path: C:\Users\<USER>\Desktop\test\
  sign:
    path: C:\Users\<USER>\Desktop\test\

#百度图片识别
baidu:
  imageText:
    client_id: 5b4dtUzX4aqZDZSwFTveL7kW
    client_secret: r0BcPtTbnHtyVS16oZmPH6ZzbrEnIHG4
    loginUrl: https://aip.baidubce.com/oauth/2.0/token?grant_type=client_credentials&client_id={client_id}&client_secret={client_secret}
    analysisUrl: https://aip.baidubce.com/rest/2.0/ocr/v1/doc_convert/request?access_token=
    analysisProgressUrl: https://aip.baidubce.com/rest/2.0/ocr/v1/doc_convert/get_request_result?access_token=

