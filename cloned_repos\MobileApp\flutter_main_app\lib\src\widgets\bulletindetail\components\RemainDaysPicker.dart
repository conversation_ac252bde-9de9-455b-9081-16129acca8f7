///***
/// * 剩余天数选择器
/// */

import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';

import 'package:flutter_main_app/src/constants/theme.dart';
import 'package:flutter_main_app/src/utils/ScreenUtils.dart';

class RemainDaysPicker extends StatefulWidget {

  RemainDaysPicker({
    this.days = 0,
  }) : assert(days >= 0);

  final int days;

  @override
  _RemainDaysPickerState createState() => _RemainDaysPickerState();

}

class _RemainDaysPickerState extends State<RemainDaysPicker> {

  int _days = 0;

  FixedExtentScrollController _remainDaysController;

  @override
  void initState() {
    super.initState();

    _days = widget.days;

    _remainDaysController = FixedExtentScrollController(initialItem: _days - 1);
  }

  @override
  void dispose() {
    _remainDaysController.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      child: Container(
        width: ScreenUtils.screenWidth,
        color: themeContentBackgroundColor,
        child: SafeArea(
          top: false,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              /// * 功能按钮 - 取消|确定
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 30.0),
                width: ScreenUtils.screenWidth,
                height: 42.0,
                color: const Color(0xffe5e5e5),
                child: Row(
                  mainAxisSize: MainAxisSize.max,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: <Widget>[
                    GestureDetector(
                      child: Text(
                        '取消',
                        style: TextStyle(
                          fontSize: 14.0,
                          color: const Color(0xff999999),
                        ),
                      ),
                      onTap: () {
                        Navigator.of(context).pop();
                      },
                    ),
                    GestureDetector(
                      child: Text(
                        '确定',
                        style: TextStyle(
                          fontSize: 14.0,
                          color: themeActiveColor,
                        ),
                      ),
                      onTap: () {
                        Navigator.of(context).pop(_days);
                      },
                    ),
                  ],
                ),
              ),
              /// * 选项列表
              Container(
                width: ScreenUtils.screenWidth,
                constraints: BoxConstraints(
                  minHeight: 0.0,
                  maxHeight: 195.0,
                ),
                child: CupertinoPicker.builder(
                  backgroundColor: themeContentBackgroundColor,
                  scrollController: _remainDaysController,
                  itemExtent: 39.0,
                  onSelectedItemChanged: (int index) {
                    setState(() {
                      _days = index + 1;
                    });
                  },
                  itemBuilder: (BuildContext context, int index) {
                    return Center(
                      child: Text('${index + 1}'),
                    );
                  },
                  childCount: 30,
                  useMagnifier: true,
                  magnification: 1.05,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

}
