package com.hzw.sunflower.controller.response;


import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelCollection;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;


@Data
public class DepartmentStatisticsVO {

    @ApiModelProperty(value = "主键")
    private long id;

    @Excel(name = "排名")
    @ApiModelProperty(value = "主键")
    private String ordered;


    @ApiModelProperty(value = "月份")
    private String yearmonth;


    @ApiModelProperty(value = "部门id")
    private long departmentId;

    @Excel(isWrap = false,name = "部门名称",orderNum = "1")
    @ApiModelProperty(value = "部门名称")
    private String departmentName;

    @Excel(isWrap = false,name = "本期累计(个)",groupName = "项目数(个)",orderNum = "2")
    @ApiModelProperty(value = "项目数")
    private long projectCount;

    @Excel(isWrap = false,name = "同期累计(个)",groupName = "项目数(个)",orderNum = "3")
    @ApiModelProperty(value = "去年已开标项目数")
    private long projectCountAgo;

    @Excel(isWrap = false,name = "同比(%)",groupName = "项目数(个)",orderNum = "4",replace = {"-_null"})
    @ApiModelProperty(value = "项目数同比")
    private Double projectCountBasis;

    @Excel(isWrap = false,name = "本期(万元)",groupName = "委托金额(万元)",orderNum = "5")
    @ApiModelProperty(value = "委托金额")
    private BigDecimal entrustedAmount;

    @Excel(isWrap = false,name = "同期(万元)",groupName = "委托金额(万元)",orderNum = "6")
    @ApiModelProperty(value = "去年委托金额")
    private BigDecimal entrustedAmountAgo;

    @Excel(isWrap = false,name = "同比(万元)",groupName = "委托金额(万元)",orderNum = "7",replace = {"-_null"})
    @ApiModelProperty(value = "委托金额同比")
    private Double entrustedAmountBasis;

//    @ExcelCollection(name = "项目个数")
//    @ApiModelProperty(value = "项目个数")
//    private List<ProjectCountVO> projectCountVOs;
//
//    @ExcelCollection(name = "委托金额")
//    @ApiModelProperty(value = "委托金额")
//    private List<EntrustedAmountVO> entrustedAmountVOs;
}
