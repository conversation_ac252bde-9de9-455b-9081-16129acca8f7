package com.hzw.sunflower.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hzw.sunflower.controller.request.LicenseReq;
import com.hzw.sunflower.dto.LicenseDto;
import com.hzw.sunflower.entity.License;

import java.util.Map;

/**
 * <AUTHOR>
 * @datetime 2023/02/20 16:09
 * @description: license服务层
 * @version: 1.0
 */
public interface LicenseService extends IService<License> {


//    String getMacAddrCache();
    /**
     * 获取系统license
     * @return
     */
    LicenseDto getLicense();

    /**
     * 修改license信息
     * @param req
     * @return
     */
    Boolean updateLicense(LicenseReq req);




    Map<String, Object> beforeUpdateLicense(LicenseReq req);
}
