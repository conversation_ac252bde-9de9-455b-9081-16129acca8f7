package com.hzw.sunflower.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hzw.sunflower.common.BaseBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <p>
 * 待处理事项表
 * </p>
 *
 * <AUTHOR> Gen
 * @since 2022-12-15
 */
@Getter
@Setter
@TableName("t_pending_item")
@ApiModel(value = "PendingItem对象", description = "待处理事项表")
public class PendingItem extends BaseBean {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("提出时间")
    private Date applyTime;

    @ApiModelProperty("操作时间")
    private Date operationTime;

    @ApiModelProperty("业务类型code")
    private String businessCode;

    @ApiModelProperty("业务id")
    private Long businessId;

    @ApiModelProperty("业务类型：1:待处理事项  2：退回待处理")
    private Integer businessType;

    @ApiModelProperty("项目id")
    private Long projectId;

    @ApiModelProperty("标段id")
    private String sectionId;

    @ApiModelProperty("轮次")
    private Integer bidRound;

    @ApiModelProperty("申请人id")
    private Long applyUserId;

}
