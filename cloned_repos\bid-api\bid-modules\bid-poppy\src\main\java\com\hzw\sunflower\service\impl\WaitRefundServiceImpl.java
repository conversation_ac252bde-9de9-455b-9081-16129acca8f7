package com.hzw.sunflower.service.impl;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hzw.sunflower.common.redis.RedisCache;
import com.hzw.sunflower.constant.constantenum.ReturnListEnum;
import com.hzw.sunflower.controller.project.request.ReturnListConditionReq;
import com.hzw.sunflower.controller.project.response.WaitListVo;
import com.hzw.sunflower.dao.WaitRefundMapper;
import com.hzw.sunflower.datascope.utils.DataScopeUtil;
import com.hzw.sunflower.entity.JwtUser;
import com.hzw.sunflower.service.WaitRefundCountService;
import com.hzw.sunflower.service.WaitRefundService;
import com.hzw.sunflower.service.asynchrony.AsynchronyService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 项目表 Service接口实现
 *
 * <AUTHOR>
 * @version 1.0.0 2021-04-07
 */
@Service
@Slf4j
public class WaitRefundServiceImpl  implements WaitRefundService {

    @Autowired
    private WaitRefundMapper waitRefundMapper;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private AsynchronyService asynchronyService;

    @Autowired
    private WaitRefundCountService waitRefundCountService;

    @Override
    public  IPage<WaitListVo> queryReturnListCount(ReturnListConditionReq condition, JwtUser jwtUser) {
        condition.setUserId(jwtUser.getUserId());
        IPage<WaitListVo> page = condition.buildPage();
        List<WaitListVo> all = new ArrayList<>();
        String keyWords = "";
        String datascopesql= DataScopeUtil.getProjectDataScopeFilterDepartSql("tpe","tpe","p");
        if(condition.getType().equals(ReturnListEnum.ALL_INFO.getCode())){
            keyWords = condition.getKeyWords();
            condition.setKeyWords(null);
        }
        if(condition.getType().equals(ReturnListEnum.ALL_INFO.getCode()) || condition.getType().equals(ReturnListEnum.TENDER_BID.getCode())){
            //采购公告退回
            IPage<WaitListVo> tenderBid = waitRefundMapper.getTenderBid(datascopesql,condition,page);
            all.addAll(tenderBid.getRecords());
        }

        if(condition.getType().equals(ReturnListEnum.ALL_INFO.getCode()) || condition.getType().equals(ReturnListEnum.TENDER_DOC.getCode())){
            //采购文件退回
            IPage<WaitListVo> tenderDoc = waitRefundMapper.getTenderDoc(datascopesql,condition,page);
            all.addAll(tenderDoc.getRecords());
        }

        if(condition.getType().equals(ReturnListEnum.ALL_INFO.getCode()) || condition.getType().equals(ReturnListEnum.SUPPLEMENT_BID.getCode())){
            //更正公告
            IPage<WaitListVo> supplementBid = waitRefundMapper.getSupplementBid(datascopesql,condition,page);
            all.addAll(supplementBid.getRecords());
        }

        if(condition.getType().equals(ReturnListEnum.ALL_INFO.getCode()) || condition.getType().equals(ReturnListEnum.CLARIFY_BID.getCode())){
            //澄清修改
            IPage<WaitListVo> clarifyBid = waitRefundMapper.getClarifyBid(datascopesql,condition,page);
            all.addAll(clarifyBid.getRecords());
        }

        if(condition.getType().equals(ReturnListEnum.ALL_INFO.getCode()) || condition.getType().equals(ReturnListEnum.OBJECTION.getCode())){
            //异议回复
            IPage<WaitListVo> objection = waitRefundMapper.getObjection(datascopesql,condition,page);
            all.addAll(objection.getRecords());
        }

        if(condition.getType().equals(ReturnListEnum.ALL_INFO.getCode()) || condition.getType().equals(ReturnListEnum.WINNING_BID_CANDIDATE.getCode())){
            //确认中标候选人退回
            IPage<WaitListVo> winBid = waitRefundMapper.getWinBid(datascopesql,condition,page);
            all.addAll(winBid.getRecords());
        }

        if(condition.getType().equals(ReturnListEnum.ALL_INFO.getCode()) || condition.getType().equals(ReturnListEnum.WINNING_BID_CANDIDATE_ZGYS.getCode())){
            //资格预审退回
            IPage<WaitListVo> winBidZgys = waitRefundMapper.getwinBidZgys(datascopesql,condition,page);
            all.addAll(winBidZgys.getRecords());
        }

        if(condition.getType().equals(ReturnListEnum.ALL_INFO.getCode()) || condition.getType().equals(ReturnListEnum.WIN_CANDIDATE_BID.getCode())){
            //候选人公示
            IPage<WaitListVo> winningBidCandidate = waitRefundMapper.getWinningBidCandidate(datascopesql,condition,page);
            all.addAll(winningBidCandidate.getRecords());
        }

        if(condition.getType().equals(ReturnListEnum.ALL_INFO.getCode()) || condition.getType().equals(ReturnListEnum.WIN_BID.getCode())){
            //中标结果公示
            IPage<WaitListVo> winCandidateBid = waitRefundMapper.getWinCandidateBid(datascopesql,condition,page);
            all.addAll(winCandidateBid.getRecords());
        }

        if(condition.getType().equals(ReturnListEnum.ALL_INFO.getCode()) || condition.getType().equals(ReturnListEnum.WIN_NOTICE.getCode())){
            //中标通知书
            IPage<WaitListVo> winNotice = waitRefundMapper.getWinNotice(datascopesql,condition,page);
            all.addAll(winNotice.getRecords());
        }

        if(condition.getType().equals(ReturnListEnum.ALL_INFO.getCode()) || condition.getType().equals(ReturnListEnum.RE_WIN_NOTICE.getCode())){
            //重新申请中标通知书
            IPage<WaitListVo> reWinNotice = waitRefundMapper.getReWinNotice(datascopesql,condition,page);
            all.addAll(reWinNotice.getRecords());
        }

        if(condition.getType().equals(ReturnListEnum.ALL_INFO.getCode()) || condition.getType().equals(ReturnListEnum.BID_WIN_NOTICE_PRE.getCode())){
            //资格预审通知书
            IPage<WaitListVo> bidWinNoticePre = waitRefundMapper.getBidWinNoticePre(datascopesql,condition,page);
            all.addAll(bidWinNoticePre.getRecords());
        }
        if(condition.getType().equals(ReturnListEnum.ALL_INFO.getCode()) || condition.getType().equals(ReturnListEnum.RE_BID_WIN_NOTICE_PRE.getCode())){
            //重新申请资格预审通知书
            IPage<WaitListVo> reBidWinNoticePre = waitRefundMapper.getReBidWinNoticePre(datascopesql,condition,page);
            all.addAll(reBidWinNoticePre.getRecords());
        }

        if(condition.getType().equals(ReturnListEnum.ALL_INFO.getCode()) || condition.getType().equals(ReturnListEnum.ABNORMAL_NOTICE.getCode())){
            //异常公告退回、撤回退回
            IPage<WaitListVo> abnormalNotice = waitRefundMapper.getAbnormalNotice(datascopesql,condition,page);
            all.addAll(abnormalNotice.getRecords());
        }

        if(condition.getType().equals(ReturnListEnum.ALL_INFO.getCode()) || condition.getType().equals(ReturnListEnum.ARCHIVE.getCode())){
            //归档退回
            IPage<WaitListVo> archive = waitRefundMapper.getArchive(datascopesql,condition,page);
            all.addAll(archive.getRecords());
        }

        if((condition.getType().equals(ReturnListEnum.ALL_INFO.getCode()) || condition.getType().equals(ReturnListEnum.MATERIAL_APPLICATION.getCode()))){
            //材料申请退回
            IPage<WaitListVo> materialApplication = waitRefundMapper.getMaterialApplication(datascopesql,condition,page);
            all.addAll(materialApplication.getRecords());
        }

        if(condition.getType().equals(ReturnListEnum.ALL_INFO.getCode()) || condition.getType().equals(ReturnListEnum.WITH_LOCK.getCode())){
            //用锁申请退回
            IPage<WaitListVo> withLock = waitRefundMapper.getWithLock(datascopesql,condition,page);
            all.addAll(withLock.getRecords());
        }

        if(condition.getType().equals(ReturnListEnum.ALL_INFO.getCode()) || condition.getType().equals(ReturnListEnum.SPECIAL_TICKET_CHANGE.getCode())){
            //换专票申请退回
            IPage<WaitListVo> specialTicketChange = waitRefundMapper.getSpecialTicketChange(datascopesql,condition,page);
            all.addAll(specialTicketChange.getRecords());
        }

        if(condition.getType().equals(ReturnListEnum.ALL_INFO.getCode()) || condition.getType().equals(ReturnListEnum.TENDER_FEES_APPLY.getCode())){
            //推标书费申请退回
            IPage<WaitListVo> tenderFeesApply = waitRefundMapper.getTenderFeesApply(datascopesql,condition,page);
            all.addAll(tenderFeesApply.getRecords());
        }

        if(condition.getType().equals(ReturnListEnum.ALL_INFO.getCode()) || condition.getType().equals(ReturnListEnum.REGISTERED_SUPPLIERS.getCode())){
            //登记供应商退回
            IPage<WaitListVo> registeredSuppliers = waitRefundMapper.getRegisteredSuppliers(datascopesql,condition,page);
            all.addAll(registeredSuppliers.getRecords());
        }

        if(condition.getType().equals(ReturnListEnum.ALL_INFO.getCode()) || condition.getType().equals(ReturnListEnum.RERECORD_PAYMENT.getCode())){
            //补录付款凭证退回
            IPage<WaitListVo> rerecordPayment = waitRefundMapper.getRerecordPayment(datascopesql,condition,page);
            all.addAll(rerecordPayment.getRecords());
        }

        if(condition.getType().equals(ReturnListEnum.ALL_INFO.getCode()) || condition.getType().equals(ReturnListEnum.AGENCY_SERVICE.getCode())){
            //代理服务费修改退回
            IPage<WaitListVo> agencyService = waitRefundMapper.getAgencyService(datascopesql,condition,page);
            all.addAll(agencyService.getRecords());
        }

        if((condition.getType().equals(ReturnListEnum.ALL_INFO.getCode()) || condition.getType().equals(ReturnListEnum.DATA_MODIFICATION.getCode()))){
            //数据修改退回
            IPage<WaitListVo> dataModification = waitRefundMapper.getDataModification(datascopesql,condition,page);
            all.addAll(dataModification.getRecords());
        }

        if(condition.getType().equals(ReturnListEnum.ALL_INFO.getCode()) || condition.getType().equals(ReturnListEnum.DEPOSIT_REFUND.getCode())){
            //保证金退还退回
            IPage<WaitListVo> depositRefund = waitRefundMapper.getDepositRefund(datascopesql,condition,page);
            all.addAll(depositRefund.getRecords());
        }

        if(condition.getType().equals(ReturnListEnum.ALL_INFO.getCode()) || condition.getType().equals(ReturnListEnum.EXPERT_EXTRACTION.getCode())){
            //专家抽取退回
            IPage<WaitListVo> expertExtraction = waitRefundMapper.getExpertExtraction(datascopesql,condition,page);
            all.addAll(expertExtraction.getRecords());
        }

        if(condition.getType().equals(ReturnListEnum.ALL_INFO.getCode()) || condition.getType().equals(ReturnListEnum.EXPERTS_SELECTED_RECORD.getCode())){
            //专家抽取备案退回
            IPage<WaitListVo> expertsSelectedRecord = waitRefundMapper.getExpertsSelectedRecord(datascopesql,condition,page);
            all.addAll(expertsSelectedRecord.getRecords());
        }

        // 查全部数据存入缓存
        if(condition.getType().equals(ReturnListEnum.ALL_INFO.getCode())) {
            // 存入缓存
            redisCache.setCacheObject("ReturnList-" + jwtUser.getUserOtherId(),
                    JSONUtil.toJsonStr(all), 5, TimeUnit.SECONDS);
        }

        if(condition.getType().equals(ReturnListEnum.ALL_INFO.getCode()) && StringUtils.isNotEmpty(keyWords)){
            List<WaitListVo> date = new ArrayList<>();
            for (WaitListVo vo : all){
                // 数据是否符合过滤条件标志
                boolean f = false;
                // 根据查询条件过滤返回结果
                if (StringUtils.isNotEmpty(condition.getKeyWords()) || StringUtils.isNotEmpty(keyWords)) {
                    if ((vo.getProjectName() != null && vo.getProjectName().contains(StringUtils.isNotEmpty(condition.getKeyWords())?condition.getKeyWords():keyWords))
                            || (vo.getProjectNum() != null && vo.getProjectNum().contains(StringUtils.isNotEmpty(condition.getKeyWords())?condition.getKeyWords():keyWords))){
                        f = true;
                    }else{
//                        f=false;
//                    continue;
                    }
                } else {
                    f = true;
                }
                if (f) {
                    date.add(vo);
                }
            }
            all.clear();
            all.addAll(date);
        }

        IPage<WaitListVo> allPage = new Page<>();
        CountTypeNum(allPage,all);
        //List<WaitListVo> count = new ArrayList<>();
        //// 各类型对应的退回数量
        //Map<String, Integer> collect = all.stream().collect(Collectors.groupingBy(WaitListVo::getType, Collectors.summingInt(WaitListVo::getCountNum)));
        //for (Map.Entry<String, Integer> map : collect.entrySet()) {
        //    WaitListVo vo = new WaitListVo();
        //    vo.setType(map.getKey());
        //    vo.setCountNum(map.getValue());
        //    count.add(vo);
        //}
        //WaitListVo waitListVo = new WaitListVo();
        //waitListVo.setType(ReturnListEnum.ALL_INFO.getCode());
        //if(!count.isEmpty()){
        //    Integer integer = count.stream().map(e -> e.getCountNum()).reduce(Integer::sum).get();
        //    waitListVo.setCountNum(integer);
        //    count.add(waitListVo);
        //}
        //// 退回时间倒叙排序
        //allPage.setRecords(all);
        //allPage.getRecords().sort(Comparator.comparing(WaitListVo::getRefundTime).reversed());
        //if(!CollectionUtils.isEmpty(all) && null != all.get(0)){
        //    allPage.getRecords().get(0).setCount(count);
        //}
        allPage.setTotal(all.size());
        return allPage;
    }

    @Override
//    @Cached(name="selectReturnCount",timeUnit= TimeUnit.SECONDS, expire =10)
//    @CacheRefresh(timeUnit=TimeUnit.SECONDS,refresh = 10, stopRefreshAfterLastAccess = 31)
//    @CachePenetrationProtect
    public Integer selectReturnCount(ReturnListConditionReq condition, JwtUser jwtUser) {
        /*
        condition.setUserId(jwtUser.getUserId());
        String datascopesql= DataScopeUtil.getProjectDataScopeFilterDepartSql("tpe","tpe","p");
        condition.setType((ReturnListEnum.ALL_INFO.getCode().equals(condition.getType()) || "0".equals(condition.getType()))?null:condition.getType());
        Integer count = 0;
        //先去缓存查询
        Object value = redisCache.getCacheObject(EntrustConstants.RETURN_LIST_Count_CODE+jwtUser.getUserId());
        long expire = redisCache.getKeyExpire(EntrustConstants.RETURN_LIST_Count_CODE + jwtUser.getUserId(),TimeUnit.SECONDS);
        if (value != null) {
            count = Integer.valueOf(value.toString());
            //判断刷新时间，超过5s异步刷新
            if (EntrustConstants.RETURN_LIST_VALID_TIME-EntrustConstants.RETURN_LIST_REFRESH_TIME > expire) {
                //新线程异步查询最新数据并刷新缓存
                asynchronyService.refreshRedis(condition,jwtUser,datascopesql);
//                refreshRedis(condition,jwtUser,datascopesql);
            }
        } else {
            //缓存无数据则查询数据并存入缓存
            count = waitRefundCountService.getReturnListCount(condition,jwtUser,datascopesql);
        }
        return count;
         */
        Integer count = 0;
        condition.setUserId(jwtUser.getUserId());
        condition.setDepartId(jwtUser.getUser().getDepartId());
        //处理数据权限
        String datascopesql= DataScopeUtil.getUserDataScopeFilterDepartSql("v");
        //全部类别为null或空
        if(condition.getType() != null && condition.getType().equals(ReturnListEnum.ALL_INFO.getCode())) {
            condition.setType(null);
        }
        Integer returnListCount = waitRefundMapper.selectReturnListCount(datascopesql, condition);
        count += returnListCount;
        //自定义列名的数据权限
        String datascopematerialsql= DataScopeUtil.getUserDataScopeFilterCustomSql("t","t","apply_user_id","apply_user_depart_id");
        //如果是材料,不关联项目单独查询
        if (condition.getType() == null || condition.getType().equals(ReturnListEnum.MATERIAL_APPLICATION.getCode())) {
            Integer returnMaterialCount = waitRefundMapper.getReturnMaterialCount(datascopematerialsql, condition);
            count += returnMaterialCount;
        }
        //自定义列名的数据权限
        String datascopecustomsql= DataScopeUtil.getUserDataScopeFilterCustomSql("t","t","apply_user_id","apply_depart_id");
        //数据修改 不关联项目单独查询
        if (condition.getType() == null || condition.getType().equals(ReturnListEnum.DATA_MODIFICATION.getCode())) {
            Integer returnDataCount = waitRefundMapper.getReturnDataCount(datascopecustomsql, condition);
            count += returnDataCount;
        }
        // 用印申请 不关联项目
        String datascopesealsql= DataScopeUtil.getUserDataScopeFilterCustomSql("t","t","apply_user","dept_id");
        if (condition.getType() == null || condition.getType().equals(ReturnListEnum.SEAL_APPLICATION_RETURN.getCode())) {
            Integer sealReturnCount = waitRefundMapper.getSealReturnCount(datascopesealsql,condition);
            count += sealReturnCount;
        }
        return count;
    }


    @Override
    public IPage<WaitListVo> queryReturnList(ReturnListConditionReq condition, JwtUser jwtUser) {
        condition.setType((ReturnListEnum.ALL_INFO.getCode().equals(condition.getType())
                || "0".equals(condition.getType())
                || StringUtils.isEmpty(condition.getType()))?ReturnListEnum.ALL_INFO.getCode():condition.getType());
        Boolean isCache = false;
        if(condition.getType().equals(ReturnListEnum.ALL_INFO.getCode())){
            // 查全部从缓存中取
            isCache = true;
        }
        IPage<WaitListVo> allData= this.getCacheDataByUserOtherId(jwtUser.getUserOtherId(),isCache,condition,jwtUser);

        return allData;
    }

    /**
     * 查询退回待处理列表（待处理事项表）
     * @param condition
     * @param jwtUser
     * @return
     */
    @Override
    public IPage<WaitListVo> selectReturnList(ReturnListConditionReq condition, JwtUser jwtUser) {
        condition.setUserId(jwtUser.getUserId());
        condition.setDepartId(jwtUser.getUser().getDepartId());
        //处理数据权限
        String datascopesql= DataScopeUtil.getUserDataScopeFilterDepartSql("v");
        //全部类别为null或空
        if(condition.getType() != null && condition.getType().equals(ReturnListEnum.ALL_INFO.getCode())) {
            condition.setType(null);
        }
        List<WaitListVo> all = new ArrayList<>();
        IPage<WaitListVo> page = condition.buildPage();
        IPage<WaitListVo> waitListVoIPage = waitRefundMapper.selectReturnList(datascopesql, condition, page);
        if (waitListVoIPage.getRecords().size() > 0) {
            all.addAll(waitListVoIPage.getRecords());
        }

        //自定义列名的数据权限
        String datascopematerialsql= DataScopeUtil.getUserDataScopeFilterCustomSql("t","t","apply_user_id","apply_user_depart_id");
        //如果是材料,不关联项目单独查询
        if (condition.getType() == null || condition.getType().equals(ReturnListEnum.MATERIAL_APPLICATION.getCode())) {
            IPage<WaitListVo> waitRefundMapperReturnMaterial = waitRefundMapper.getReturnMaterial(datascopematerialsql, condition, page);
            if (waitRefundMapperReturnMaterial.getRecords().size() > 0) {
                all.addAll(waitListVoIPage.getRecords());
            }
        }

        //自定义列名的数据权限
        String datascopecustomsql= DataScopeUtil.getUserDataScopeFilterCustomSql("t","t","apply_user_id","apply_depart_id");
        //数据修改 不关联项目单独查询
        if (condition.getType() == null || condition.getType().equals(ReturnListEnum.DATA_MODIFICATION.getCode())) {
            IPage<WaitListVo> waitRefundMapperReturnData = waitRefundMapper.getReturnData(datascopecustomsql, condition, page);
            if (waitRefundMapperReturnData.getRecords().size() > 0) {
                all.addAll(waitListVoIPage.getRecords());
            }
        }
        // 用印申请
        String datascopesealsql= DataScopeUtil.getUserDataScopeFilterCustomSql("t","t","apply_user","dept_id");
        if (condition.getType() == null || condition.getType().equals(ReturnListEnum.SEAL_APPLICATION_RETURN.getCode())) {
            IPage<WaitListVo> waitRefundMapperReturnData = waitRefundMapper.getSealReturnData(datascopesealsql,condition, page);
            if (waitRefundMapperReturnData.getRecords().size() > 0) {
                all.addAll(waitListVoIPage.getRecords());
            }
        }
        if (all.size() > 0) {
            //统计各个类别的数量
            List<WaitListVo> count = new ArrayList<>();
            Map<String, Integer> collect = all.stream().collect(Collectors.groupingBy(WaitListVo::getType, Collectors.summingInt(WaitListVo::getCountNum)));
            for (Map.Entry<String, Integer> map : collect.entrySet()) {
                WaitListVo vo = new WaitListVo();
                vo.setType(map.getKey());
                vo.setCountNum(map.getValue());
                count.add(vo);
            }
            WaitListVo waitListVo = new WaitListVo();
            waitListVo.setType(ReturnListEnum.ALL_INFO.getCode());
            if(!count.isEmpty()){
                Integer integer = count.stream().map(e -> e.getCountNum()).reduce(Integer::sum).get();
                waitListVo.setCountNum(integer);
                count.add(waitListVo);
            }
            if (all.size() > 0) {
                all.get(0).setCount(count);
            }
        }
        IPage<WaitListVo> result = new Page<>();
        CountTypeNum(result,all);
        result.setTotal(all.size());
        return result;
    }


    private IPage<WaitListVo> getCacheDataByUserOtherId(String userOtherId, Boolean isCache, ReturnListConditionReq condition, JwtUser jwtUser){
        IPage<WaitListVo> allData= condition.buildPage();
        if (isCache){
            // 缓存中获取待办事项
            Object paddingJson = redisCache.getCacheObject("ReturnList-" + userOtherId);
            // 有结果组装数据
            if (paddingJson != null) {
                List<WaitListVo> waitListVos = JSONUtil.toList(JSONUtil.toJsonStr(paddingJson), WaitListVo.class);
                List<WaitListVo> date = new ArrayList<>();
                for (WaitListVo vo : waitListVos){
                    // 数据是否符合过滤条件标志
                    boolean f = false;
                    // 根据查询条件过滤返回结果
                    if (StringUtils.isNotEmpty(condition.getKeyWords())) {
                        if ((vo.getProjectName() != null && vo.getProjectName().contains(condition.getKeyWords()))
                                || (vo.getProjectNum() != null && vo.getProjectNum().contains(condition.getKeyWords()))){
                            f = true;
                        }else{
//                        f=false;
                            continue;
                        }
                    } else {
                        f = true;
                    }
                    if (f) {
                        date.add(vo);
                    }
                }
                if(CollectionUtils.isNotEmpty(date)){
                    CountTypeNum(allData,date);
                //    // 获得各种类似数据数量
                //    List<WaitListVo> count = new ArrayList<>();
                //    Map<String, Integer> collect = date.stream().collect(Collectors.groupingBy(WaitListVo::getType, Collectors.summingInt(WaitListVo::getCountNum)));
                //    for (Map.Entry<String, Integer> map : collect.entrySet()) {
                //        WaitListVo vo = new WaitListVo();
                //        vo.setType(map.getKey());
                //        vo.setCountNum(map.getValue());
                //        count.add(vo);
                //    }
                //    WaitListVo waitListVo = new WaitListVo();
                //    waitListVo.setType(ReturnListEnum.ALL_INFO.getCode());
                //    if(!count.isEmpty()){
                //        Integer integer = count.stream().map(e -> e.getCountNum()).reduce(Integer::sum).get();
                //        waitListVo.setCountNum(integer);
                //        count.add(waitListVo);
                //    }
                //    allData.setRecords(date);
                //    // 退回时间倒叙排序
                //    allData.getRecords().sort(Comparator.comparing(WaitListVo::getRefundTime).reversed());
                //    if(!CollectionUtils.isEmpty(date) && null != date.get(0)){
                //        allData.getRecords().get(0).setCount(count);
                //    }
                }
            }else {
                if(condition.getType().equals(ReturnListEnum.ALL_INFO.getCode())){
                    allData=this.queryReturnListCount(condition,jwtUser);
                }
            }
        }else {
            allData=this.queryReturnListCount(condition,jwtUser);
        }
        return allData;
    }

    private IPage<WaitListVo> CountTypeNum(IPage<WaitListVo> allData,List<WaitListVo> date){
        // 获得各种类似数据数量
        List<WaitListVo> count = new ArrayList<>();
        Map<String, Integer> collect = date.stream().collect(Collectors.groupingBy(WaitListVo::getType, Collectors.summingInt(WaitListVo::getCountNum)));
        for (Map.Entry<String, Integer> map : collect.entrySet()) {
            WaitListVo vo = new WaitListVo();
            vo.setType(map.getKey());
            vo.setCountNum(map.getValue());
            count.add(vo);
        }
        WaitListVo waitListVo = new WaitListVo();
        waitListVo.setType(ReturnListEnum.ALL_INFO.getCode());
        if(!count.isEmpty()){
            Integer integer = count.stream().map(e -> e.getCountNum()).reduce(Integer::sum).get();
            waitListVo.setCountNum(integer);
            count.add(waitListVo);
        }
        allData.setRecords(date);
        // 退回时间倒叙排序
        allData.getRecords().sort(Comparator.comparing(WaitListVo::getRefundTime).reversed());
        if(!CollectionUtils.isEmpty(date) && null != date.get(0)){
            allData.getRecords().get(0).setCount(count);
        }
        return allData;
    }



//    /**
//     * 异步刷新缓存的数据-退回待处理事项的数量
//     * @param condition
//     * @param jwtUser
//     */
//    public  void refreshRedis(ReturnListConditionReq condition, JwtUser jwtUser, String datascopesql){
//        new Thread() {
//            @Override
//            public void run() {
//                getReturnListCount(condition,jwtUser,datascopesql);
//            }
//        }.start();
//    }

}
