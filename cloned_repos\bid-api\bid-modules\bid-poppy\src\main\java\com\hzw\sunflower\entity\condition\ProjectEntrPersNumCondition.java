package com.hzw.sunflower.entity.condition;

import com.hzw.sunflower.common.BaseCondition;
import io.swagger.annotations.ApiModel;


/**
 * 委托人项目编号查询条件
 *
 * <AUTHOR>
 * @version 1.0.0 2021-04-12
 */
@ApiModel(description = "委托人项目编号查询条件")
public class ProjectEntrPersNumCondition extends BaseCondition {
    /**
     * 版本号
     */
    private static final long serialVersionUID = -7432358898141846815L;
}