package com.hzw.ssm.applets.service;

import com.hzw.ssm.applets.dao.CompanyMapper;
import com.hzw.ssm.applets.entity.Company;
import com.hzw.ssm.applets.exception.ExceptionEnum;
import com.hzw.ssm.applets.utils.RandomCode;
import com.hzw.ssm.applets.utils.ResultJson;
import com.hzw.ssm.expert.dao.ExpertInfoMapper;
import com.hzw.ssm.expert.entity.ExpertInfoEntity;
import com.hzw.ssm.expert.entity.ExpertUpdateRecordEntity;
import com.hzw.ssm.fw.base.BaseService;
import com.hzw.ssm.fw.util.CommUtil;
import com.hzw.ssm.sys.user.dao.UserMapper;
import com.hzw.ssm.sys.user.entity.UserEntity;
import com.hzw.ssm.util.string.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <AUTHOR>
 * @Date 2021-02-03 10:14
 * @Version 1.0
 */
@Service
public class CompanyService extends BaseService {

    private static Logger logger = Logger.getLogger(CompanyService.class);

    @Autowired
    private CompanyMapper companyMapper;
    @Autowired
    private ExpertInfoMapper expertInfoMapper;
    @Autowired
    private UserMapper userMapper;

    public ResultJson insertCompany(Company company) {
        try {
            List<Map<String, Object>> list = new ArrayList<>();
            Map<String, Object> map = new HashMap<>();

            String id = RandomCode.getId();
            company.setId(id);
            company.setCreateTime(new Date());
            company.setDeleteFlag("0");
            int updates = companyMapper.insertCompany(company);
            if (updates > 0) {
                map.put("id", id);
                list.add(map);
                return new ResultJson(list);
            }
        } catch (Exception e) {
            logger.error(e.getMessage());
        }
        return new ResultJson(ExceptionEnum.SERVER_IS_BUSY.getCode(), ExceptionEnum.SERVER_IS_BUSY.getMessage());
    }

    public ResultJson updateCompany(ExpertInfoEntity expertInfoEntity) {
        try {

            ExpertInfoEntity infoEntity = new ExpertInfoEntity();
            infoEntity.setUser_id(expertInfoEntity.getExpertId());
            ExpertInfoEntity expertInfoByUserIdOrId = expertInfoMapper.getExpertInfoByUserIdOrId(infoEntity);
            long status = expertInfoByUserIdOrId.getStatus();
            if (3 == status || 8 == status) {
                expertInfoByUserIdOrId.setStatus(10L);
                expertInfoMapper.updateExpertInfoById(expertInfoByUserIdOrId);
            }

            List<Map<String, Object>> list = new ArrayList<>();
            Map<String, Object> map = new HashMap<>();
            ExpertInfoEntity keyId = new ExpertInfoEntity();
            keyId.setId(expertInfoEntity.getId());
            ExpertInfoEntity ckey = expertInfoMapper.getExpertInfoByUserIdOrId(keyId);
            int updates = expertInfoMapper.updateCompanyByExpertId(expertInfoEntity);
            if (updates > 0) {
                // 修改单位
                if (3 == status || 8 == status || 4 == status || 10 == status) {
                    try {
                        infoRecord(ckey, expertInfoEntity);
                    } catch (Exception e) {
                        System.out.println("记录出错!");
                    }
                }
                map.put("id", expertInfoEntity.getId());
                list.add(map);
                return new ResultJson(list);
            }
        } catch (Exception e) {
            e.printStackTrace();
            logger.error(e.getMessage());
        }
        return new ResultJson(ExceptionEnum.SERVER_IS_BUSY.getCode(), ExceptionEnum.SERVER_IS_BUSY.getMessage());
    }

    private void infoRecord(ExpertInfoEntity before, ExpertInfoEntity after) {

        if (before != null && after != null) {
            UserEntity userEntity = userMapper.selectUserById(before.getUser_id());
            String role_id;
            if (userEntity != null) {
                role_id = StringUtils.toStringNull(userEntity.getRole_id());
            } else {
                role_id = "无";
            }

            if (!StringUtils.isEmpty(after.getCompany())) {
                if (!StringUtils.toStringNull(before.getCompany()).equalsIgnoreCase(StringUtils.toStringNull(after.getCompany()))) {
                    // 工作单位
                    addExpertUpdateRecord(before.getUser_id(), "单位名称", StringUtils.toStringNull(before.getCompany()), StringUtils.toStringNull(after.getCompany()), role_id, before.getUser_id(), "单位信息", 0);

                }
            }

            if (!StringUtils.isEmpty(after.getCompany_addr())) {
                if (!StringUtils.toStringNull(before.getCompany_addr()).equalsIgnoreCase(StringUtils.toStringNull(after.getCompany_addr()))) {
                    // 单位地址
                    addExpertUpdateRecord(before.getUser_id(), "单位地址", StringUtils.toStringNull(before.getCompany_addr()), StringUtils.toStringNull(after.getCompany_addr()), role_id, before.getUser_id(), "单位信息", 0);
                }
            }


            if (!StringUtils.isEmpty(after.getCompany_phone())) {
                if (!StringUtils.toStringNull(before.getCompany_phone()).equalsIgnoreCase(StringUtils.toStringNull(after.getCompany_phone()))) {
                    // 单位电话
                    addExpertUpdateRecord(before.getUser_id(), "单位电话", StringUtils.toStringNull(before.getCompany_phone()), StringUtils.toStringNull(after.getCompany_phone()), role_id, before.getUser_id(), "单位信息", 0);

                }
            }

            if (!StringUtils.isEmpty(after.getCompany_zipcode())) {
                if (!StringUtils.toStringNull(before.getCompany_zipcode()).equalsIgnoreCase(StringUtils.toStringNull(after.getCompany_zipcode()))) {
                    // 单位邮编
                    addExpertUpdateRecord(before.getUser_id(), "单位邮编", StringUtils.toStringNull(before.getCompany_zipcode()), StringUtils.toStringNull(after.getCompany_zipcode()), role_id, before.getUser_id(), "单位信息", 0);

                }
            }
        }
    }


    /**
     * 新增专家信息修改记录
     *
     * @param user_id        用户编号
     * @param item           修改项
     * @param content_before 修改前内容
     * @param content_after  修改后内容
     * @param modify_role    修改角色
     * @param modify_user    修改人
     * @param modify_reason  修改原因
     * @param batch_number   修改批次编号
     */
    public void addExpertUpdateRecord(String user_id, String item, String content_before, String content_after, String modify_role, String modify_user, String modify_reason, int batch_number) {
        ExpertUpdateRecordEntity record = new ExpertUpdateRecordEntity();
        record.setId(CommUtil.getKey());
        record.setUser_id(user_id);
        record.setItem(item);
        record.setContent_before(content_before);
        record.setContent_after(content_after);
        record.setModify_role(modify_role);
        record.setModify_user(modify_user);
        record.setModify_reason(modify_reason);
        record.setBatch_number(batch_number);
        record.setModify_time(new Date());
        record.setDelete_flag(0);
        expertInfoMapper.addExpertUpdateRecord(record);
    }


    public ResultJson deleteCompany(Company company) {
        try {
            List<Map<String, Object>> list = new ArrayList<>();
            Map<String, Object> map = new HashMap<>();

            int updates = companyMapper.deleteCompany(company);
            if (updates > 0) {
                map.put("id", company.getId());
                list.add(map);
                return new ResultJson(list);
            }
        } catch (Exception e) {
            logger.error(e.getMessage());
        }
        return new ResultJson(ExceptionEnum.SERVER_IS_BUSY.getCode(), ExceptionEnum.SERVER_IS_BUSY.getMessage());
    }

    public ResultJson getCompany(Company company) {
        List<Company> list = new ArrayList<>();
        Map<String, Object> map = new HashMap<>();
        Company com = companyMapper.getCompany(company);
        list.add(com);
        return new ResultJson(list);
    }

    public ResultJson getCompanys(Company entity) {
        List<Map<String, Object>> list = new ArrayList<>();
        Map<String, Object> map = new HashMap<>();
        String expertId = entity.getExpertId();
        ExpertInfoEntity expertInfoEntity = expertInfoMapper.queryCompanyByUserId(expertId);
        String company = expertInfoEntity.getCompany();
        String company_addr = expertInfoEntity.getCompany_addr();
        String company_phone = expertInfoEntity.getCompany_phone();
        String company_zipcode = expertInfoEntity.getCompany_zipcode();
        String user_id = expertInfoEntity.getUser_id();
        map.put("id", expertInfoEntity.getId());
        map.put("expertId", user_id);
        map.put("company", null != company ? company : "");
        map.put("company_addr", null != company_addr ? company_addr : "");
        map.put("company_phone", null != company_phone ? company_phone : "");
        map.put("company_zipcode", null != company_zipcode ? company_zipcode : "");
        list.add(map);
        return new ResultJson(list);
    }


}
