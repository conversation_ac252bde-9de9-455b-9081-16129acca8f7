package com.hzw.ssm.sys.project.entity;

import com.hzw.ssm.fw.base.BaseEntity;

public class DeleteRecord extends BaseEntity{
	
	private String id;
	private String projectId;
	private String reason;
	private String deleteFlag;
	private String status;
	private String restoreReason;
	private String decimationBatch;
	
	public String getDecimationBatch() {
		return decimationBatch;
	}

	public void setDecimationBatch(String decimationBatch) {
		this.decimationBatch = decimationBatch;
	}

	public void setId(String id) {
		this.id = id;
	}
	
	public String getProjectId() {
		return projectId;
	}

	public void setProjectId(String projectId) {
		this.projectId = projectId;
	}

	public String getReason() {
		return reason;
	}
	public void setReason(String reason) {
		this.reason = reason;
	}
	public String getDeleteFlag() {
		return deleteFlag;
	}
	public void setDeleteFlag(String deleteFlag) {
		this.deleteFlag = deleteFlag;
	}
	public String getStatus() {
		return status;
	}
	public void setStatus(String status) {
		this.status = status;
	}
	public String getId() {
		return id;
	}
	public String getRestoreReason() {
		return restoreReason;
	}
	public void setRestoreReason(String restoreReason) {
		this.restoreReason = restoreReason;
	}

}
