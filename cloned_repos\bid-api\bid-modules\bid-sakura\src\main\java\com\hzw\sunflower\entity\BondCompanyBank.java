package com.hzw.sunflower.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hzw.sunflower.common.BaseBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 供应商行名维护表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-27
 */
@Data
@TableName("t_bond_company_bank")
@ApiModel(value = "t_bond_company_bank", description = "供应商行名维护表")
public class BondCompanyBank extends BaseBean implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("供应商id")
    private Long companyId;

    @ApiModelProperty("付款户名")
    @TableField("company_name")
    private String companyName;

    @ApiModelProperty("付款账户")
    @TableField("company_account")
    private String companyAccount;

    @ApiModelProperty("付款开户行")
    @TableField("company_bank_deposit")
    private String companyBankDeposit;

    @ApiModelProperty("供应商付款银行行号")
    @TableField("company_bank_code")
    private String companyBankCode;
}
