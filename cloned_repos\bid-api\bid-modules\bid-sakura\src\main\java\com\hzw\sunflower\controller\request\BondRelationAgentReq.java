package com.hzw.sunflower.controller.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date ：Created in 2022/7/29 11:10
 * @description：
 * @modified By：`
 * @version: 1.0
 */
@ApiModel(description = "运管处关联页面详情查询入参")
@Data
public class BondRelationAgentReq implements Serializable {

    @ApiModelProperty(value = "流水id")
    private Long waterId;

    @ApiModelProperty(value = "项目id")
    private Long projectId;

    @ApiModelProperty(value = "采购项目编号")
    private String purchaseNumber;

    @ApiModelProperty(value = "企业id")
    private Long companyId;

}
