package com.hzw.sunflower.service.impl;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzw.sunflower.constant.constantenum.AppReturnListEnum;
import com.hzw.sunflower.controller.request.AppTaskCondition;
import com.hzw.sunflower.controller.response.AppToReadVo;
import com.hzw.sunflower.dao.AppToReadMapper;
import com.hzw.sunflower.entity.AppToRead;
import com.hzw.sunflower.entity.JwtUser;
import com.hzw.sunflower.service.AppToReadService;
import com.hzw.sunflower.util.LoginUtil;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * <p>
 * 待处理事项表 服务实现类
 * </p>
 *
 * <AUTHOR> Gen
 * @since 2022-12-15
 */
@Service
public class AppToReadServiceImpl extends ServiceImpl<AppToReadMapper, AppToRead> implements AppToReadService {

    /**
     * 分页查询待阅
     * @param condition
     * @return
     */
    @Override
    public IPage<AppToReadVo> listPage(AppTaskCondition condition) {
        IPage<AppToReadVo> page = condition.buildPage();
        page = this.baseMapper.listPage(page, condition);
        List<AppToReadVo> list = page.getRecords();
        for (AppToReadVo vo : list) {
            AppReturnListEnum returnListEnum = AppReturnListEnum.getByCode(vo.getBusinessCode());
            if (returnListEnum != null) {
                vo.setTitle(vo.getUserName() + "退回" + returnListEnum.getDesc().replace("退回", ""));
            }
        }
        page.setRecords(list);
        return page;
    }

    /**
     * 消息中心全部列表
     * @param condition
     * @return
     */
    @Override
    public IPage<AppToReadVo> listAllPage(AppTaskCondition condition) {
        IPage<AppToReadVo> page = condition.buildPage();
        condition.setDepartId(LoginUtil.getJwtUser().getUser().getDepartId());
        page = this.baseMapper.listAllPage(page, condition);
        List<AppToReadVo> list = page.getRecords();
        for (AppToReadVo vo : list) {
            if (vo.getNoticeType() == 1) {
                AppReturnListEnum returnListEnum = AppReturnListEnum.getByCode(vo.getBusinessCode());
                if (returnListEnum != null) {
                    vo.setTitle(vo.getUserName() + "退回" + returnListEnum.getDesc().replace("退回", ""));
                }
            }
        }
        page.setRecords(list);
        return page;
    }

    /**
     * 消息中心数量统计
     * @param condition
     * @return
     */
    @Override
    public Map<String, Long> countAll(AppTaskCondition condition) {
        Map<String, Long> map = new HashMap<>();
        condition.setDepartId(LoginUtil.getJwtUser().getUser().getDepartId());
        // 待阅未读数量
        Long toReadCount = this.baseMapper.countToRead(condition);
        // 通知未读数量
        Long noticeCount = this.baseMapper.countNotice(condition);
        map.put("toReadCount", toReadCount);
        map.put("noticeCount", noticeCount);
        map.put("total", toReadCount + noticeCount);
        return map;
    }
}
