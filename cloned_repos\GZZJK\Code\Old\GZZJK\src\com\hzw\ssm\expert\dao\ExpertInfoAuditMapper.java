package com.hzw.ssm.expert.dao;

import java.util.List;

import com.hzw.ssm.expert.entity.ExpertAuditEntity;
import com.hzw.ssm.expert.entity.ExpertInfoEntity;

/**
 * 专家基本信息审核
 * <AUTHOR> 2014-10-17
 *
 */
public interface ExpertInfoAuditMapper {

	/**
	 * 获取专家基本信息
	 * @param user_id(用户表id) 专家用户id(专家基本信息表id)
	 * @return
	 */
	public ExpertInfoEntity getExpertInfoByUserIdOrId(ExpertInfoEntity colExpertInfo);
	
	/**
	 * 获取准入审批专家信息列表
	 * @param colExpertInfo
	 * @return
	 */
	public List<ExpertInfoEntity> queryPageExpInfoAuditList(ExpertInfoEntity colExpertInfo);
	
	/**
	 * 获取专家信息审核列表
	 * @param colExpertInfo
	 * @return
	 */
	public List<ExpertInfoEntity> queryPageExpertInfoAuditList(ExpertInfoEntity colExpertInfo);
	
	/**
	 * 审核记录
	 * @param auditEntity
	 */
	public void addExpertAuditReason(ExpertAuditEntity auditEntity);
	
	/**
	 * 专家入库审批，修改专家相应信息
	 * @param expertInfo
	 */
	public void auditExpertInfo(ExpertInfoEntity expertInfo);
	
	/**
	 * 专家审核信息列表
	 * @param user_id
	 * @return
	 */
	public List<ExpertAuditEntity> queryExpertAuditEntityList(String user_id);
	
	/**
	 * 最终审核通过审核记录delete_flag=1
	 * @param user_id
	 */
	public void updateAuditEntityByUserId(String user_id);
	
	/**
	 * 专家审核通过则为专家生成专家编号 
	 */
	public void createExpertForExpertNum(ExpertInfoEntity expertInfo);
	
}
