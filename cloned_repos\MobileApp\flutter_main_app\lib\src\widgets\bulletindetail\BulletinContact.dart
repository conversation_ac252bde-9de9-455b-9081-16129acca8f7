import "package:flutter/material.dart";
// import 'package:flutterapp/com/CustomAppBar.dart';

class BullectinContact extends StatelessWidget {
  const BullectinContact({Key key}) : super(key: key);


  static TextStyle titleTextStyle = TextStyle(
    color: Color(0xFF414141),
    fontWeight: FontWeight.bold,
    fontSize: 13,
  );

  static TextStyle normalTextStyle = TextStyle(
    color: Color(0xFF414141),
    fontSize: 13,
    height: 2,
  );

  @override
  Widget build(BuildContext context) {
    return Container(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Padding(
                  padding: const EdgeInsets.fromLTRB(15, 0, 15, 12),
                  child: Text.rich(TextSpan(
                    style: normalTextStyle,
                    children: <TextSpan>[
                      TextSpan(text: '招标人：', children: <TextSpan>[
                        TextSpan(
                          text: '苏州新高融建建设发展有限公司\r\n',
                        )
                      ]),
                      TextSpan(text: '地址：', children: <TextSpan>[
                        TextSpan(
                          text: '苏州高新区\r\n',
                        )
                      ]),
                      TextSpan(text: '邮编：', children: <TextSpan>[
                        TextSpan(
                          text: '215011 \r\n',
                        )
                      ]),
                      TextSpan(text: '联系人：', children: <TextSpan>[
                        TextSpan(
                          text: '刘丽巍\r\n',
                        )
                      ]),
                      TextSpan(text: '电话：', children: <TextSpan>[
                        TextSpan(
                          text: '68759120\r\n',
                        )
                      ]),
                      TextSpan(text: '传真：', children: <TextSpan>[
                        TextSpan(
                          text: '0512-65160659\r\n',
                        )
                      ]),
                      TextSpan(text: '电子邮件：', children: <TextSpan>[
                        TextSpan(
                          text: '<EMAIL>\r\n',
                        )
                      ]),
                      TextSpan(text: '网址：', children: <TextSpan>[
                        TextSpan(
                          text: '\r\n',
                        )
                      ]),
                      TextSpan(text: '开户银行：', children: <TextSpan>[
                        TextSpan(
                          text: '\r\n',
                        )
                      ]),
                      TextSpan(text: '账号：', children: <TextSpan>[
                        TextSpan(
                          text: '\r\n',
                        )
                      ])
                    ],
                  )),
                ),
                
                Padding(
                  padding: const EdgeInsets.fromLTRB(15, 0, 15, 12),
                  child: Text.rich(TextSpan(
                    style: normalTextStyle,
                    children: <TextSpan>[
                      TextSpan(text: '代理机构：', children: <TextSpan>[
                        TextSpan(
                          text: '江苏顺悦建设监理有限公司\r\n',
                        )
                      ]),
                      TextSpan(text: '地址：', children: <TextSpan>[
                        TextSpan(
                          text: '鼓楼区牌楼巷星月大厦811室\r\n',
                        )
                      ]),
                      TextSpan(text: '邮编：', children: <TextSpan>[
                        TextSpan(
                          text: '215011 \r\n',
                        )
                      ]),
                      TextSpan(text: '联系人：', children: <TextSpan>[
                        TextSpan(
                          text: '陈工\r\n',
                        )
                      ]),
                      TextSpan(text: '电话：', children: <TextSpan>[
                        TextSpan(
                          text: '58760611\r\n',
                        )
                      ]),
                      TextSpan(text: '传真：', children: <TextSpan>[
                        TextSpan(
                          text: '0512-65160659\r\n',
                        )
                      ]),
                      TextSpan(text: '电子邮件：', children: <TextSpan>[
                        TextSpan(
                          text: '<EMAIL>\r\n',
                        )
                      ]),
                      TextSpan(text: '网址：', children: <TextSpan>[
                        TextSpan(
                          text: '\r\n',
                        )
                      ]),
                      TextSpan(text: '开户银行：', children: <TextSpan>[
                        TextSpan(
                          text: '\r\n',
                        )
                      ]),
                      TextSpan(text: '账号：', children: <TextSpan>[
                        TextSpan(
                          text: '\r\n',
                        )
                      ])
                    ],
                  )),
                ),
        ],
      ),
    );
  }
}